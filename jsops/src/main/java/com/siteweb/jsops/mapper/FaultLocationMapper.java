package com.siteweb.jsops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.jsops.dto.CauseAnalysisDTO;
import com.siteweb.jsops.dto.FaultEventDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface FaultLocationMapper extends BaseMapper<ActiveEvent> {
    List<FaultEventDTO> findFaultAlarmList(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("stationIdList") List<Integer> stationIdList);

    List<CauseAnalysisDTO> losePowerSupply(@Param("stationIdList") List<Integer> stationIdList);

    List<CauseAnalysisDTO> reducePowerSupply(@Param("stationIdList") List<Integer> stationIdList);
}
