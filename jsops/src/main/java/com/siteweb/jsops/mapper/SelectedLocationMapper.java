package com.siteweb.jsops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.jsops.dto.IdNameDTO;
import com.siteweb.monitoring.entity.StationStructure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SelectedLocationMapper extends BaseMapper<StationStructure> {

    /**
     * 查找城市
     *
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findCity();

    /**
     * 通过城市id 查找区县
     *
     * @param cityId 城市id
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findCountryByCityId(@Param("cityId") Integer cityId);

    /**
     * 获取所有区县
     *
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findAllCountry();
    /**
     * 查找局站，通过地市id与区县id
     *
     * @param cityId 城市id
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findStationByCity(@Param("cityId") Integer cityId);

    /**
     * 查找所有局站
     *
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findAllStation();

    /**
     * 通过区县id获取局站信息
     *
     * @param countryId 国主键id
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findStationByCityAndCountryId(@Param("countryId") Integer countryId);
}
