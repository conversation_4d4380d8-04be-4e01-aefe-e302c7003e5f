package com.siteweb.jsops.service.impl;

import com.siteweb.jsops.dto.IdNameDTO;
import com.siteweb.jsops.mapper.SelectedLocationMapper;
import com.siteweb.jsops.service.SelectedLocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SelectedLocationServiceImpl implements SelectedLocationService {
    /**
     * 全部
     */
    private static final int ALL = -1;
    @Autowired
    SelectedLocationMapper selectedLocationMapper;

    @Override
    public List<IdNameDTO> findCity() {
        return selectedLocationMapper.findCity();
    }

    @Override
    public List<IdNameDTO> findCountry(Integer cityId) {
        //获取所有的区县
        if (Objects.equals(cityId, ALL)) {
            return selectedLocationMapper.findAllCountry();
        }
        //通过地市id获取区县
        return selectedLocationMapper.findCountryByCityId(cityId);
    }

    @Override
    public List<IdNameDTO> findStation(Integer cityId, Integer countryId) {
        //获取所有局站
        if (Objects.equals(cityId, ALL) && Objects.equals(countryId, ALL)) {
            return selectedLocationMapper.findAllStation();
        }
        //通过地市id获取局站
        //if (Objects.equals(countryId, ALL)) {
        //    return selectedLocationMapper.findStationByCity(cityId);
        //}
        //通过区县id获取局站
        return selectedLocationMapper.findStationByCityAndCountryId(cityId);
    }
}
