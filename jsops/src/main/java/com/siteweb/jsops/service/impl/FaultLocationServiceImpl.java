package com.siteweb.jsops.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.jsops.dto.CauseAnalysisDTO;
import com.siteweb.jsops.dto.EventDTO;
import com.siteweb.jsops.dto.FaultEventDTO;
import com.siteweb.jsops.dto.StatisticsResult;
import com.siteweb.jsops.mapper.FaultLocationMapper;
import com.siteweb.jsops.schedule.FaultLocationSchedule;
import com.siteweb.jsops.service.FaultLocationService;
import com.siteweb.monitoring.dto.ActiveSignal;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ConfigEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.model.RealTimeSignalKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FaultLocationServiceImpl implements FaultLocationService {
    /**
     * 电压报警阈值
     */
    private static final int VOLTAGE_ALARM_THRESHOLD = 43;
    @Autowired
    FaultLocationMapper faultLocationMapper;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    FaultLocationSchedule faultLocationSchedule;
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    ConfigEventManager configEventManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Override
    public List<FaultEventDTO> findFaultAlarmList(Date startTime, Date endTime, List<Integer> stationIdList) {
        if (CollUtil.isEmpty(stationIdList)) {
            return Collections.emptyList();
        }
        List<FaultEventDTO> result = faultLocationMapper.findFaultAlarmList(startTime, endTime, stationIdList);
        result.sort(Comparator.comparing(FaultEventDTO::getStartTime));
        return result;
    }

    @Override
    public List<StatisticsResult<Long>> findStatisticsResult(List<Integer> stationIdList) {
        if (CollUtil.isEmpty(stationIdList)) {
            return Collections.emptyList();
        }
        HashSet<Integer> stationIdSet = new HashSet<>(stationIdList);
        List<StatisticsResult<Long>> equipmentCategoryStatistics = getEquipmentStatistics(stationIdSet);
        List<StatisticsResult<Long>> eventCategoryStatistics = getEventStatistics(stationIdSet);
        equipmentCategoryStatistics.addAll(eventCategoryStatistics);
        return equipmentCategoryStatistics;
    }

    /**
     * 电源失去部分供电能力
     *
     * @param stationIdList 局站列表
     * @return {@link List}<{@link CauseAnalysisDTO}>
     */
    @Override
    public List<CauseAnalysisDTO> findCauseAnalysisList(List<Integer> stationIdList) {
        if (CollUtil.isEmpty(stationIdList)) {
            return Collections.emptyList();
        }
        //电池故障
        List<CauseAnalysisDTO> batteryFaultList = getBatteryFault(stationIdList);
        //电源失去供电能力
        List<CauseAnalysisDTO> losePowerSupplyList = losePowerSupply(stationIdList);
        //电源失去部分供电能力
        List<CauseAnalysisDTO> reducePowerSupplyList = reducePowerSupply(stationIdList);
        //已经失去供电能力了，就不用放入到失去部分供电能力的结果中
         Set<Integer> losePowerSupplyEquipmentIdSet = losePowerSupplyList.stream()
                                                                         .map(CauseAnalysisDTO::getEquipmentId)
                                                                         .collect(Collectors.toSet());
        reducePowerSupplyList.removeIf(e -> losePowerSupplyEquipmentIdSet.contains(e.getEquipmentId()));
        batteryFaultList.addAll(losePowerSupplyList);
        batteryFaultList.addAll(reducePowerSupplyList);
        batteryFaultList.sort(Comparator.comparing(CauseAnalysisDTO::getOccurrenceTime).reversed());
        return batteryFaultList;
    }

    /**
     * 获取电池故障列表
     * 【电池故障】 电池组总电压低于43
     * @param stationIdList 局站id列表
     * @return {@link List}<{@link CauseAnalysisDTO}>
     */
    private List<CauseAnalysisDTO> getBatteryFault(List<Integer> stationIdList) {
        List<Equipment> equipmentList = equipmentManager.getEquipmentByStationId(stationIdList)
                                                        .stream()
                                                        .filter(e -> Objects.equals(FaultLocationSchedule.BATTERY_CATEGORY_ID, e.getEquipmentCategory()))
                                                        .toList();
        List<CauseAnalysisDTO> batteryFaultList = new ArrayList<>();
        for (Equipment equipment : equipmentList) {
            List<SimpleActiveSignal> realtimeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipment.getEquipmentId(), ListUtil.toList(1101170001L));
            realtimeSignal.stream().findFirst().ifPresent(signal -> {
                if (Objects.isNull(signal.getOriginalValue())) {
                    return;
                }
                double currentValue = Double.parseDouble(signal.getOriginalValue());
                if (currentValue <= VOLTAGE_ALARM_THRESHOLD) {
                    CauseAnalysisDTO causeAnalysisDTO = new CauseAnalysisDTO();
                    causeAnalysisDTO.setEquipmentId(equipment.getEquipmentId());
                    causeAnalysisDTO.setEquipmentName(equipment.getEquipmentName());
                    causeAnalysisDTO.setCurrentValue(signal.getOriginalValue());
                    causeAnalysisDTO.setOccurrenceTime(new Date());
                    batteryFaultList.add(causeAnalysisDTO);
                }
            });
        }

        //设置发生时间
        Map<Integer, Date> firstBatteryFaultTime = faultLocationSchedule.getFirstBatteryFaultTimeMap();
        for (CauseAnalysisDTO causeAnalysisDTO : batteryFaultList) {
            firstBatteryFaultTime.computeIfPresent(causeAnalysisDTO.getEquipmentId(), (key, value) -> {
                causeAnalysisDTO.setOccurrenceTime(value);
                return value;
            });
            causeAnalysisDTO.setEventName(localeMessageSourceUtil.getMessage("js.totalVoltage.tooLow"));
            causeAnalysisDTO.setSummary(localeMessageSourceUtil.getMessage("js.battery.lowVoltageAlarm"));
            causeAnalysisDTO.setCause(localeMessageSourceUtil.getMessage("js.battery.failure"));
            causeAnalysisDTO.getEventList().add(new EventDTO(causeAnalysisDTO.getOccurrenceTime(), localeMessageSourceUtil.getMessage("js.battery.packTooLowAlarm"),causeAnalysisDTO.getCurrentValue()));
        }
        return batteryFaultList;
    }

    /**
     * 电源失去供电能力
     * 1.输出中断告警 【告警基类:4013130001】
     * 2.输出电压过低告警 【告警基类:401111001】
     * 3.直流输出电压（低压43）【信号基类：401110001】
     * @param stationIdList 局站id
     * @return {@link List}<{@link CauseAnalysisDTO}>
     */
    private List<CauseAnalysisDTO> losePowerSupply(List<Integer> stationIdList){
        List<CauseAnalysisDTO> list = faultLocationMapper.losePowerSupply(stationIdList);

        setCurrentValue(list);
        // 直流输出电压（低压43）
        List<CauseAnalysisDTO>  dcOutputVoltageList = getDcOutputVoltageList(stationIdList);
        list.addAll(dcOutputVoltageList);
        list.sort(Comparator.comparing(CauseAnalysisDTO::getOccurrenceTime));
        Map<Integer, List<CauseAnalysisDTO>> collect = list.stream().collect(Collectors.groupingBy(CauseAnalysisDTO::getEquipmentId));
        List<CauseAnalysisDTO> result = new ArrayList<>();
        collect.forEach((key, value) -> {
            CauseAnalysisDTO causeAnalysisDTO = value.get(0);
            value.forEach(cause -> causeAnalysisDTO.getEventList()
                                                   .add(new EventDTO(cause.getOccurrenceTime(), cause.getEventName(), cause.getCurrentValue())));
            causeAnalysisDTO.setSummary(localeMessageSourceUtil.getMessage("js.batterySupply.packTooLowAlarm"));
            causeAnalysisDTO.setCause(localeMessageSourceUtil.getMessage("js.batterySupply.lostProvide"));
            result.add(causeAnalysisDTO);
        });
        return result;
    }

    private List<CauseAnalysisDTO> getDcOutputVoltageList(List<Integer> stationIdList) {
        List<Equipment> equipmentList = equipmentManager.getEquipmentByStationId(stationIdList)
                                                        .stream()
                                                        .filter(e -> Objects.equals(FaultLocationSchedule.POWER_SUPPLY_CATEGORY_ID, e.getEquipmentCategory()))
                                                        .toList();
        List<CauseAnalysisDTO> causeAnalysisDTOList  = new ArrayList<>();
        for (Equipment equipment : equipmentList) {
            List<SimpleActiveSignal> realTimeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipment.getEquipmentId(), ListUtil.toList(401110001L));
            realTimeSignal.stream().findFirst().ifPresent(signal->{
                if (CharSequenceUtil.isNotBlank(signal.getOriginalValue()) && Double.parseDouble(signal.getOriginalValue()) <= VOLTAGE_ALARM_THRESHOLD) {
                    causeAnalysisDTOList.add(new CauseAnalysisDTO(equipment.getEquipmentId(), equipment.getEquipmentName(), signal.getOriginalValue(), localeMessageSourceUtil.getMessage("js.batterySupply.lostProvide"), localeMessageSourceUtil.getMessage("js.batterySupply.packTooLowAlarm"), localeMessageSourceUtil.getMessage("js.powerSupply.lowOutPutVoltageAlarm")));
                }
            });
        }
        return causeAnalysisDTOList;
    }

    private void setCurrentValue(List<CauseAnalysisDTO> list) {
        Map<String, RealTimeSignalKey> eventSignalConfigMap = new LinkedHashMap<>();
        for (CauseAnalysisDTO causeAnalysisDTO : list) {
            ConfigEventDTO configEvent = configEventManager.findConfigEventByEquipmentIdAndEventId(causeAnalysisDTO.getEquipmentId(), causeAnalysisDTO.getEventId());
            if (configEvent == null) {
                continue;
            }
            Integer signalId =  getSignalIdByStartExpression(configEvent.getStartExpression());
            RealTimeSignalKey realTimeSignalKey = new RealTimeSignalKey(causeAnalysisDTO.getEquipmentId(), signalId);
            eventSignalConfigMap.put(causeAnalysisDTO.getEquipmentId() + "." + causeAnalysisDTO.getEventId(), realTimeSignalKey);
        }
        Map<String, ActiveSignal> activeSignalMap = activeSignalManager.getActiveSignalsByKeys(new ArrayList<>(eventSignalConfigMap.values()))
                                                                       .stream()
                                                                       .collect(Collectors.toMap(e -> e.getEquipmentId() + "." + e.getSignalId(), Function.identity()));
        for (CauseAnalysisDTO causeAnalysisDTO : list) {
            RealTimeSignalKey signalId = eventSignalConfigMap.get(causeAnalysisDTO.getEquipmentId() + "." + causeAnalysisDTO.getEventId());
            ActiveSignal activeSignal = activeSignalMap.get(signalId.getEquipmentId() + "." + signalId.getSignalId());
            causeAnalysisDTO.setCurrentValue(activeSignal.getOriginalValue());
        }
    }

    /**
     * 获取信号id根据告警表达式
     *
     * @param startExpression 开始表达式
     * @return {@link Integer}
     */
    private  Integer getSignalIdByStartExpression(String startExpression) {
        try {
            // 去除首尾的方括号
            String trimmedInput = startExpression.substring(1, startExpression.length() - 1);
            // 使用逗号分割字符串
            String[] numbers = trimmedInput.split(",");
            return Integer.valueOf(numbers[1]);
        } catch (NumberFormatException e) {
            log.error("表达式解析错误:{}", startExpression);
            return 0;
        }
    }

    /**
     * 电源失去部分供电能力
     * 1、第XX路直流输出分路断开告警 【告警基类id 101312001】
     * 2、负载熔丝故障告警 【告警基类id 401307001】
     * 3、负载总电流（少10%或者绝对值少50A,判断1个小时之前的数据或者超阀值的存储）
     * @param stationIdList 火车站主键id列表
     * @return {@link List}<{@link CauseAnalysisDTO}>
     */
    private List<CauseAnalysisDTO> reducePowerSupply(List<Integer> stationIdList) {
        List<CauseAnalysisDTO> list = faultLocationMapper.reducePowerSupply(stationIdList);
        List<Integer> equipmentIdList = equipmentManager.getEquipmentByStationId(stationIdList)
                                                        .stream()
                                                        .map(Equipment::getEquipmentId)
                                                        .toList();
        setCurrentValue(list);
        // 负载总电流（少10%或者绝对值少50A,判断1个小时之前的数据或者超阀值的存储）
        Map<Integer, CauseAnalysisDTO> totalLoadCurrentMap = faultLocationSchedule.getTotalLoadCurrentMap();
        for (Integer equipmentId : equipmentIdList) {
            totalLoadCurrentMap.computeIfPresent(equipmentId, (key, value) -> {
                list.add(BeanUtil.copyProperties(value, CauseAnalysisDTO.class));
                return value;
            });
        }
        list.sort(Comparator.comparing(CauseAnalysisDTO::getOccurrenceTime));
        Map<Integer, List<CauseAnalysisDTO>> collect = list.stream().collect(Collectors.groupingBy(CauseAnalysisDTO::getEquipmentId));
        List<CauseAnalysisDTO> result = new ArrayList<>();
        collect.forEach((key, value) -> {
            CauseAnalysisDTO causeAnalysisDTO = value.get(0);
            value.forEach(cause -> causeAnalysisDTO.getEventList()
                                                   .add(new EventDTO(cause.getOccurrenceTime(), cause.getEventName(), cause.getCurrentValue())));
            causeAnalysisDTO.setSummary(localeMessageSourceUtil.getMessage("js.powerSupply.normal"));
            causeAnalysisDTO.setCause(localeMessageSourceUtil.getMessage("js.batterySupply.lostPartProvide"));
            result.add(causeAnalysisDTO);
        });
        return result;
    }

    /**
     * 获取告警统计信息
     * 【停电告警、输出低压告警、总电压告警、高温告警】
     * @param stationIdSet 局站id列表
     * @return {@link Map}<{@link Integer}, {@link Long}>
     */
    private List<StatisticsResult<Long>> getEventStatistics(HashSet<Integer> stationIdSet) {

        // 初始化一个映射，用于存储所有组的计数，初始值为0
        Map<String, Long> eventCountInitMap = new LinkedHashMap<>();
        eventCountInitMap.put(localeMessageSourceUtil.getMessage("js.powerOutage.alarmCount"), 0L);
        eventCountInitMap.put(localeMessageSourceUtil.getMessage("js.lowOutputVoltage.alarmCount"), 0L);
        eventCountInitMap.put(localeMessageSourceUtil.getMessage("js.totalVoltage.alarmCount"), 0L);
        eventCountInitMap.put(localeMessageSourceUtil.getMessage("js.highTemperature.alarmCount"), 0L);
        //1501152001 停电告警
        //401111001 输出低压告警
        //503172001 1101170001 总电压告警
        //1004307001 1004001001 高温告警
        Set<Long> baseTypeIdSet = Set.of(1501152001L, 401111001L, 503172001L, 1101170001L, 1004307001L, 1004001001L);
        // 自定义一个分类函数，根据baseTypeId将事件分组
        Function<ActiveEvent, String> eventGroupFunc = event -> {
            Long baseTypeId = event.getBaseTypeId();
            if (Objects.equals(baseTypeId, 1501152001L)) {
                return localeMessageSourceUtil.getMessage("js.powerOutage.alarmCount");
            }
            if (Objects.equals(baseTypeId, 401111001L)) {
                return localeMessageSourceUtil.getMessage("js.lowOutputVoltage.alarmCount");
            }
            if (Objects.equals(baseTypeId, 503172001L) || Objects.equals(baseTypeId, 1101170001L)) {
                return localeMessageSourceUtil.getMessage("js.totalVoltage.alarmCount");
            }
            if (Objects.equals(baseTypeId, 1004307001L) || Objects.equals(baseTypeId, 1004001001L)) {
                return localeMessageSourceUtil.getMessage("js.highTemperature.alarmCount");
            }
            return "";
        };
        activeEventManager.queryAllActiveEvents()
                          .stream()
                          .filter(e -> Objects.isNull(e.getEndTime()))
                          .filter(e -> Objects.nonNull(e.getStationId()) && stationIdSet.contains(e.getStationId()))
                          .filter(e -> Objects.nonNull(e.getBaseTypeId()) && baseTypeIdSet.contains(e.getBaseTypeId()))
                          .collect(Collectors.groupingBy(eventGroupFunc, Collectors.counting()))
                          .forEach((group, count) -> eventCountInitMap.merge(group, count, Long::sum));
        List<StatisticsResult<Long>> result = new ArrayList<>();
        eventCountInitMap.forEach((key, value) -> result.add(new StatisticsResult<>(key, value)));
        return result;
    }

    /**
     * 获取设备统计信息
     * 【电表数量、电源数量、电池数量、环境数量】
     *
     * @return {@link List}<{@link StatisticsResult}<{@link Integer}>>
     */
    public List<StatisticsResult<Long>> getEquipmentStatistics(Set<Integer> stationIdSet) {
        //85 电表数量 22 电源数量 24 电池数量 51 环境数量
        Set<Integer> equipmentCategorySet = Set.of(85, 22, 24, 51);
        // 初始化一个映射，用于存储所有组的计数，初始值为0
        Map<String, Long> equipmentCategoryInitMap = new LinkedHashMap<>();
        equipmentCategoryInitMap.put(localeMessageSourceUtil.getMessage("js.floor.MeterCount"), 0L);
        equipmentCategoryInitMap.put(localeMessageSourceUtil.getMessage("js.power.DeviceCount"), 0L);
        equipmentCategoryInitMap.put(localeMessageSourceUtil.getMessage("js.battery.Count"), 0L);
        equipmentCategoryInitMap.put(localeMessageSourceUtil.getMessage("js.environmental.deviceCount"), 0L);
        // 自定义一个分类函数，根据equipmentCategory将事件分组
        Function<Equipment, String> equipmentCategoryGroupFunc = equipment -> {
            Integer equipmentCategory = equipment.getEquipmentCategory();
            if (Objects.equals(equipmentCategory, 85)) {
                return localeMessageSourceUtil.getMessage("js.floor.MeterCount");
            }
            if (Objects.equals(equipmentCategory, 22)) {
                return localeMessageSourceUtil.getMessage("js.power.DeviceCount");
            }
            if (Objects.equals(equipmentCategory, 24)) {
                return localeMessageSourceUtil.getMessage("js.battery.Count");
            }
            if (Objects.equals(equipmentCategory, 51)) {
                return localeMessageSourceUtil.getMessage("js.environmental.deviceCount");
            }
            return "";
        };
        equipmentManager.getAllEquipments()
                        .stream()
                        .filter(e -> Objects.nonNull(e.getStationId()) && stationIdSet.contains(e.getStationId()))
                        .filter(e -> Objects.nonNull(e.getEquipmentCategory()) && equipmentCategorySet.contains(e.getEquipmentCategory()))
                        .collect(Collectors.groupingBy(equipmentCategoryGroupFunc, Collectors.counting()))
                        .forEach((key, value) -> equipmentCategoryInitMap.merge(key, value, Long::sum));
        List<StatisticsResult<Long>> result = new ArrayList<>();
        equipmentCategoryInitMap.forEach((key, value) -> result.add(new StatisticsResult<>(key, value)));
        return result;
    }
}
