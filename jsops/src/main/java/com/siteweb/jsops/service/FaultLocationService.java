package com.siteweb.jsops.service;

import com.siteweb.jsops.dto.CauseAnalysisDTO;
import com.siteweb.jsops.dto.FaultEventDTO;
import com.siteweb.jsops.dto.StatisticsResult;

import java.util.Date;
import java.util.List;

public interface FaultLocationService {
    /**
     * 查找故障告警列表
     * eventSeverityId in (2, 3) 就是一级告警和二级告警
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param stationIdList 局站ids
     * @return {@link List}<{@link FaultEventDTO}>
     */
    List<FaultEventDTO> findFaultAlarmList(Date startTime, Date endTime, List<Integer> stationIdList);

    /**
     * 查找统计结果
     *
     * @param stationIdList 局站ids
     * @return {@link List}<{@link StatisticsResult}<{@link Long}>>
     */
    List<StatisticsResult<Long>> findStatisticsResult(List<Integer> stationIdList);

    /**
     * 查找原因分析列表
     *
     * @param stationIdList 局站ids
     * @return {@link List}<{@link CauseAnalysisDTO}>
     */
    List<CauseAnalysisDTO> findCauseAnalysisList(List<Integer> stationIdList);
}
