package com.siteweb.jsops.service;

import com.siteweb.jsops.dto.IdNameDTO;

import java.util.List;

public interface SelectedLocationService {
    /**
     * 获取地市数据
     *
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findCity();

    /**
     * 通过城市id查找区县
     *
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findCountry(Integer cityId);

    /**
     * 查找局站，通过地市id与区县id
     *
     * @param cityId    城市id
     * @param countryId 区县id
     * @return {@link List}<{@link IdNameDTO}>
     */
    List<IdNameDTO> findStation(Integer cityId, Integer countryId);
}
