package com.siteweb.jsops.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.jsops.service.FaultLocationService;
import com.siteweb.monitoring.mamager.StationManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 故障定位控制器
 *
 * <AUTHOR>
 * @date 2024/04/03
 */
@RestController
@RequestMapping("/api/jsops")
@Api(value = "FaultLocation接口管理",tags = "FaultLocation接口管理")
public class FaultLocationController {
    @Autowired
    FaultLocationService faultLocationService;
    @Autowired
    StationManager stationManager;


    @GetMapping("/faultlocation")
    @ApiOperation("故障定位列表")
    public ResponseEntity<ResponseResult> getAlarmInfo(Date startTime, Date endTime, String stationIds) {
        List<Integer> stationIdList = StringUtils.splitToIntegerList(stationIds);
        return ResponseHelper.successful(faultLocationService.findFaultAlarmList(startTime, endTime, stationIdList));
    }

    @GetMapping("/statistics")
    @ApiOperation("设备类型与告警的统计")
    public ResponseEntity<ResponseResult> getStatisticsResult(String stationIds) {
        List<Integer> stationIdList = StringUtils.splitToIntegerList(stationIds);
        return ResponseHelper.successful(faultLocationService.findStatisticsResult(stationIdList));
    }

    @GetMapping("/causeanalysis")
    @ApiOperation("原因分析")
    public ResponseEntity<ResponseResult> getCauseAnalysisList(String stationIds) {
        List<Integer> stationIdList = StringUtils.splitToIntegerList(stationIds);
        //TODO 记得删除
        //ArrayList<Integer> collect = stationManager.findAll()
        //                                           .stream()
        //                                           .map(Station::getStationId)
        //                                           .collect(Collectors.toCollection(ArrayList::new));
        return ResponseHelper.successful(faultLocationService.findCauseAnalysisList(stationIdList));
    }
}
