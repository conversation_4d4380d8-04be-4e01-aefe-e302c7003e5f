package com.siteweb.jsops.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.jsops.service.SelectedLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 获取BMS中的地市、区县、局站中的下拉框里面的数据
 *
 * <AUTHOR>
 * @date 2024/04/07
 */
@RestController
@RequestMapping("/api/jsops")
@Api(value = "FaultLocation接口管理", tags = "FaultLocation接口管理")
public class StationStructureController {
    @Autowired
    SelectedLocationService selectedLocationService;


    @ApiOperation("获取所有地市")
    @GetMapping("/city")
    public ResponseEntity<ResponseResult> getCity() {
        return ResponseHelper.successful(selectedLocationService.findCity());
    }

    @ApiOperation("通过地市id获取区县")
    @GetMapping("/country")
    public ResponseEntity<ResponseResult> getCountry(Integer cityId) {
        return ResponseHelper.successful(selectedLocationService.findCountry(cityId));
    }

    @ApiOperation("通过地市id与区县id获取局站")
    @GetMapping("/station")
    public ResponseEntity<ResponseResult> getStation(Integer cityId, Integer countryId) {
        return ResponseHelper.successful(selectedLocationService.findStation(cityId, countryId));
    }
}
