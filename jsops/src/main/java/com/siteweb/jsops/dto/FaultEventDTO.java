package com.siteweb.jsops.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.siteweb.common.serializer.DoubleNonNullSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class FaultEventDTO {
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 局站名
     */
    private String stationName;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 事件ID
     */
    private Integer eventId;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 条件ID
     */
    private Integer eventConditionId;
    /**
     * 告警等级ID
     */
    private Integer eventLevel;
    /**
     * 告警等级Id
     */
    private Integer eventSeverityId;
    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 触发值
     */
    @JsonSerialize(using = DoubleNonNullSerializer.class)
    private Double eventValue;
    /**
     * 告警含义
     */
    private String meanings;
}
