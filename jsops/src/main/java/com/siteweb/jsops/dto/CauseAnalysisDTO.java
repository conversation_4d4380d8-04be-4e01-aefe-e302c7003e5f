package com.siteweb.jsops.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CauseAnalysisDTO {
    public CauseAnalysisDTO(Integer equipmentId, String equipmentName, String currentValue, String cause, String summary, String eventName) {
        this.equipmentId = equipmentId;
        this.equipmentName = equipmentName;
        this.currentValue = currentValue;
        this.cause = cause;
        this.summary = summary;
        this.eventName = eventName;
        this.occurrenceTime = new Date();
        this.eventList = new ArrayList<>();
    }

    /**
     * 设备id
     */
    private Integer equipmentId;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 发生时间
     */
    private Date occurrenceTime;

    /**
     * 当前值
     */
    @JsonIgnore
    private String currentValue;

    /**
     * 原因
     */
    private String cause;

    /**
     * 总结
     */
    private String summary;
    /**
     * 事件列表
     */
    private List<EventDTO> eventList = new ArrayList<>();
    /**
     * 告警id
     */
    @JsonIgnore
    private Integer eventId;
    /**
     * 告警名称
     */
    @JsonIgnore
    private String eventName;
}
