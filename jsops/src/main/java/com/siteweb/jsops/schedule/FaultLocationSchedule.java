package com.siteweb.jsops.schedule;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.jsops.dto.CauseAnalysisDTO;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.HistorySignalManager;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class FaultLocationSchedule {

    /**
     * 直流输出电压基类id
     */
    //private static final Long DC_OUT_PUT_VOLTAGE_BASE_TYPE_ID = 401110001L;
    /**
     * 电压报警阈值
     */
    public static final int VOLTAGE_ALARM_THRESHOLD = 43;
    /**
     * 电池设备类型
     */
    public static final int BATTERY_CATEGORY_ID = 24;
    /**
     * 电源设备类型
     */
    public static final int POWER_SUPPLY_CATEGORY_ID = 22;
    /**
     * 记录第一次电池故障的时间map
     */
    @Getter
    private final Map<Integer, Date> firstBatteryFaultTimeMap = new ConcurrentHashMap<>();
    /**
     * 记录第一次直流输出电压（低压43）的设备原因
     */
    //@Getter
    //private final Map<Integer, CauseAnalysisDTO> dcOutputVoltageMap = new ConcurrentHashMap<>();

    /**
     * 负载总电流（少10%或者绝对值少50A,判断1个小时之前的数据或者超阀值的存储）
     */
    @Getter
    private final Map<Integer, CauseAnalysisDTO> totalLoadCurrentMap = new ConcurrentHashMap<>();

    /**
     * 负载总电流之前的值
     */
    private final Map<Integer, SimpleActiveSignal> preTotalLoadCurrent = new ConcurrentHashMap<>();

    @Value("${jsops.enable:false}")
    private boolean enable;
    @Autowired
    private ActiveSignalManager activeSignalManager;
    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @PostConstruct
    public void init() {
        if (!enable) {
            return;
        }
        reloadBatteryFaultTime();
        //reloadDcOutputVoltageMap();
    }


    /**
     * 用于缓存第一次电池组总电压低于43的时间
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    public void loadCacheSchedule(){
        if (!enable) {
            return;
        }
        reloadBatteryFaultTime();
        //reloadDcOutputVoltageMap();
    }

    /**
     * 负载总电流
     * 设备类型 22
     * 信号基类id 401113001
     * 负载总电流（少10%或者绝对值少50A,判断1个小时之前的数据或者超阀值的存储）由于存储周期就是一个小时一存，刚好可以使用
     */
    @Scheduled(fixedDelay = 60 * 60 * 1000) //60秒
    public void reloadTotalLoadCurrentMap() {
        TimeInterval timeInterval = new TimeInterval();
        timeInterval.start();
        List<ActiveSignalRequestByBaseTypeId> signalRequestByBaseTypeIds = equipmentManager.getEquipmentsByEqCategoryId(POWER_SUPPLY_CATEGORY_ID)
                                                                                           .stream()
                                                                                           .map(e -> {
                                                                                               ActiveSignalRequestByBaseTypeId activeSignalRequestByBaseTypeId = new ActiveSignalRequestByBaseTypeId();
                                                                                               activeSignalRequestByBaseTypeId.setEquipmentId(e.getEquipmentId());
                                                                                               activeSignalRequestByBaseTypeId.setBaseTypeId(ListUtil.toList(401113001L));
                                                                                               return activeSignalRequestByBaseTypeId;
                                                                                           }).toList();
        List<EquipmentActiveSignal> equipmentActiveSignalByBaseTypeId = activeSignalManager.getEquipmentActiveSignalByBaseTypeId(signalRequestByBaseTypeIds);
        for (EquipmentActiveSignal equipmentActiveSignal : equipmentActiveSignalByBaseTypeId) {
            equipmentActiveSignal.getActiveSignals().stream().findFirst().ifPresent(realtimeSignal -> {
                //存在才去对比历史信号值
                if (CharSequenceUtil.isBlank(realtimeSignal.getOriginalValue())) {
                    return;
                }
                double currentValue = Double.parseDouble(realtimeSignal.getOriginalValue());
                preTotalLoadCurrent.computeIfPresent(equipmentActiveSignal.getEquipmentId(), (key, oldSignalValue) -> {
                    double oldValue = Double.parseDouble(oldSignalValue.getOriginalValue());
                    if (oldValue - 50 >= currentValue || isCurrentValueAtLeastTenPercentLess(currentValue, oldValue)) {
                        totalLoadCurrentMap.put(equipmentActiveSignal.getEquipmentId(),
                                new CauseAnalysisDTO(equipmentActiveSignal.getEquipmentId(),
                                        equipmentActiveSignal.getEquipmentName(), realtimeSignal.getOriginalValue(),
                                        localeMessageSourceUtil.getMessage("js.batterySupply.lostPartProvide"),
                                        localeMessageSourceUtil.getMessage("js.powerSupply.normal"),
                                        localeMessageSourceUtil.getMessage("js.powerSupply.abnormalLoadCurrentAlarm")));
                    } else {
                        totalLoadCurrentMap.remove(equipmentActiveSignal.getEquipmentId());
                    }
                    return realtimeSignal;
                });
                //每次都要存起来，给下次计算使用
                preTotalLoadCurrent.put(equipmentActiveSignal.getEquipmentId(), realtimeSignal);
            });
        }
        log.info("Reloaded reloadTotalLoadCurrentMap in {} ms", timeInterval.interval());
    }

    /**
     * 直流输出电压（低压43）
     * 设备类型 22
     * 信号基类id 401110001
     */
    //private void reloadDcOutputVoltageMap() {
    //    TimeInterval timeInterval = new TimeInterval();
    //    timeInterval.start();
    //    List<Equipment> equipmentList = equipmentManager.getEquipmentsByEqCategoryId(22);
    //    for (Equipment equipment : equipmentList) {
    //        List<SimpleActiveSignal> realTimeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipment.getEquipmentId(), ListUtil.toList(DC_OUT_PUT_VOLTAGE_BASE_TYPE_ID));
    //        realTimeSignal.stream().findFirst().ifPresent(signal->{
    //            if (CharSequenceUtil.isNotBlank(signal.getOriginalValue()) && Double.parseDouble(signal.getOriginalValue()) <= VOLTAGE_ALARM_THRESHOLD) {
    //                dcOutputVoltageMap.computeIfAbsent(equipment.getEquipmentId(),
    //                        key -> new CauseAnalysisDTO(equipment.getEquipmentId(), equipment.getEquipmentName(), signal.getOriginalValue(), "电源失去供电能力", "该时间段产生过电源和电池低压告警，建议检查电源和电池性能是否正常。", "直流输出电压过低告警"));
    //                return;
    //            }
    //            dcOutputVoltageMap.remove(equipment.getEquipmentId());
    //
    //        });
    //    }
    //    log.info("Reloaded dcOutputVoltageMap in {} ms", timeInterval.interval());
    //}

    /**
     * 重新加载电池故障时间
     */
    private void reloadBatteryFaultTime() {
        TimeInterval timeInterval = new TimeInterval();
        timeInterval.start();
        List<Equipment> equipmentList = equipmentManager.getEquipmentsByEqCategoryId(BATTERY_CATEGORY_ID);
        for (Equipment equipment : equipmentList) {
            //电池故障：电池组总电压低于43
            List<SimpleActiveSignal> realtimeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipment.getEquipmentId(), ListUtil.toList(1101170001L));
            realtimeSignal.stream().findFirst().ifPresent(signal -> {
                if (Objects.isNull(signal.getOriginalValue())) {
                    return;
                }
                double currentValue = Double.parseDouble(signal.getOriginalValue());
                if (currentValue <= VOLTAGE_ALARM_THRESHOLD) {
                    firstBatteryFaultTimeMap.computeIfAbsent(equipment.getEquipmentId(), key -> DateUtil.parse(signal.getSampleTime()));
                    return;
                }
                firstBatteryFaultTimeMap.remove(equipment.getEquipmentId());
            });
        }
        log.info("reloadBatteryFaultTime in {} ms", timeInterval.interval());
    }

    public boolean isCurrentValueAtLeastTenPercentLess(double currentValue, double oldValue) {
        // 计算旧值的10%
        double tenPercentOfOldValue = oldValue * 0.1;

        // 计算减少10%后的阈值
        double threshold = oldValue - tenPercentOfOldValue;

        // 判断当前值是否小于或等于阈值
        return currentValue <= threshold;
    }
}
