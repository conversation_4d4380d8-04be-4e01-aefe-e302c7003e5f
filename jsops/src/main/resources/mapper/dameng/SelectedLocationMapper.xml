<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.jsops.mapper.SelectedLocationMapper">
    <select id="findCity" resultType="com.siteweb.jsops.dto.IdNameDTO">
        SELECT a.StructureId id, a.StructureName name
        FROM TBL_StationStructure a
                 INNER JOIN TBL_StationStructure b ON b.ParentStructureId = 0 AND a.ParentStructureId = b.StructureId
        WHERE a.IsUngroup = 0
    </select>
    <select id="findCountryByCityId" resultType="com.siteweb.jsops.dto.IdNameDTO">
        SELECT a.StructureId id, a.StructureName name
        FROM TBL_StationStructure a
                 INNER JOIN TBL_StationStructure b ON a.ParentStructureId = b.StructureId AND b.StructureId = #{cityId}
                 INNER JOIN TBL_StationStructure c ON c.ParentStructureId = 0 AND b.ParentStructureId = c.StructureId
        WHERE a.IsUngroup = 0
    </select>
    <select id="findStationByCity" resultType="com.siteweb.jsops.dto.IdNameDTO">
        SELECT station.StationId   id,
               station.StationName name
        FROM TBL_Station station
                 INNER JOIN TBL_StationStructureMap ssm ON ssm.StationId = station.StationId
                 INNER JOIN TBL_StationStructure ss1 ON ssm.StructureId = ss1.StructureId
                 INNER JOIN TBL_StationStructure ss2 ON ss1.ParentStructureId = ss2.StructureId
        WHERE ss2.StructureId = #{cityId}
          AND station.StationId > 0
        ORDER BY station.StationName
    </select>
    <select id="findAllCountry" resultType="com.siteweb.jsops.dto.IdNameDTO">
        SELECT a.StructureId id, a.StructureName name
        FROM TBL_StationStructure a
                 INNER JOIN TBL_StationStructure b ON a.ParentStructureId = b.StructureId
                 INNER JOIN TBL_StationStructure c ON c.ParentStructureId = 0 AND b.ParentStructureId = c.StructureId
        WHERE a.IsUngroup = 0
    </select>
    <select id="findAllStation" resultType="com.siteweb.jsops.dto.IdNameDTO">
        SELECT station.StationId   id,
               station.StationName name
        FROM TBL_Station station
        WHERE station.StationId > 0
        ORDER BY station.StationName
    </select>
    <select id="findStationByCityAndCountryId" resultType="com.siteweb.jsops.dto.IdNameDTO">
        SELECT station.StationId   id,
               station.StationName name
        FROM TBL_Station station
                 INNER JOIN TBL_StationStructureMap ssm ON ssm.StationId = station.StationId
        WHERE ssm.StructureId = #{countryId}
          AND station.StationId > 0
        ORDER BY station.StationName
    </select>
</mapper>