package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/** 能耗四级分类树 */
@Data
@NoArgsConstructor
public class MultiLevelObjectDTO {

    private Integer objectId;

    private String objectName;

    private Integer parentObjectId;

    private Integer levelNum;

    private List<MultiLevelObjectDTO> children = new ArrayList<>();
}
