package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class StructureOfComplexIndexValue {

    private Integer ParentResourceStructureId;
    private String ParentResourceStructureName;
    private Integer ParentObjectTypeId;
    private Integer ResourceStructureId;
    private String ResourceStructureName;
    private Integer ObjectTypeId;
    private Integer Level;
    private String levelOfPath;
    public Double sumValue;

    private Integer ComplexIndexId;
    private Integer businessTypeId;
    private String businessTypeName;
    private String unit;
    public StructureOfComplexIndexValue clone() {
        try {
            StructureOfComplexIndexValue temp = new StructureOfComplexIndexValue();
            temp.setComplexIndexId(this.ComplexIndexId);
            temp.setResourceStructureId(this.ResourceStructureId);
            temp.setResourceStructureName(this.ResourceStructureName);
            temp.setSumValue(this.sumValue);
            temp.setLevelOfPath(this.levelOfPath);
            temp.setLevel(this.Level);
            temp.setUnit(this.unit);
            temp.setParentResourceStructureId(this.ParentResourceStructureId);
            temp.setParentObjectTypeId(this.ParentObjectTypeId);
            temp.setParentResourceStructureName(this.ParentResourceStructureName);
            temp.setObjectTypeId(this.ObjectTypeId);
            temp.setBusinessTypeId(this.businessTypeId);
            temp.setBusinessTypeName(this.businessTypeName);
            return temp;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}