package com.siteweb.energy.dto;


import com.siteweb.energy.entity.EnergyManagementMap;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergyManagementMapInfo extends EnergyManagementMap {
    private String ResourceStructureName;
    private String LevelOfPath;
    private String ParentResourceStructureName;

    public EnergyManagementMapInfo(EnergyManagementMap map)
    {
        this.setMapId(map.getMapId());
        this.setEnergyId(map.getEnergyId());
        this.setResourceStructureId(map.getResourceStructureId());
        this.setIsMarker(map.getIsMarker());
        this.setProperty(map.getProperty());
    }
}
