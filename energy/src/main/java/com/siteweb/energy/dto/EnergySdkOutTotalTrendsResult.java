package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
/**
 * 用能效率
 */
public class EnergySdkOutTotalTrendsResult {

    private String nodeName;

    private Integer energyTypeId;

    private String energyTypeName;

    private List<EnergySdkOutTotalTrendsDefinition> definitions;

    public EnergySdkOutTotalTrendsResult(EnergySdkTotalTrendsResult energySdkTotalTrendsResult){
        this.nodeName = energySdkTotalTrendsResult.getResourceStructureName();
        this.energyTypeId = energySdkTotalTrendsResult.getBusinessTypeId();
        this.energyTypeName = energySdkTotalTrendsResult.getBusinessTypeName();
        List<EnergySdkOutTotalTrendsDefinition> temp = new ArrayList<>();
        for (EnergySdkTotalTrendsDefinition definition : energySdkTotalTrendsResult.getDefinitions()) {
            temp.add(new EnergySdkOutTotalTrendsDefinition(definition));
        }
        this.definitions = temp;
    }

}
