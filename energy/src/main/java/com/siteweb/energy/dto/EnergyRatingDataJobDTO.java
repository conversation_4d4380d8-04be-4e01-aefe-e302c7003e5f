package com.siteweb.energy.dto;

import com.siteweb.energy.entity.EnergyRatingConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.quartz.JobDataMap;

@Data
public class EnergyRatingDataJobDTO {

    private Integer ratingConfigId;

    private String workingCondition;

    private String outDryTempParam;

    private String outWetTempParam;

    private String inDryTempParam;

    private String inWetTempParam;

    private String runningLoadParam;

    private String iTPowerParam;

    private String totalPowerParam;

    private Integer intervalSecond;

    public EnergyRatingDataJobDTO(EnergyRatingConfigExDTO params, String workingCondition) {
        this.ratingConfigId = params.getRatingConfigId();
        this.outDryTempParam = params.getOutDryTempParam();
        this.outWetTempParam = params.getOutWetTempParam();
        this.inDryTempParam = params.getInDryTempParam();
        this.inWetTempParam = params.getInWetTempParam();
        this.runningLoadParam = params.getRunningLoadParam();
        this.iTPowerParam = params.getItPowerParam();
        this.totalPowerParam = params.getTotalPowerParam();
        this.workingCondition = workingCondition;
        this.intervalSecond = params.getIntervalSecond();
    }

    public EnergyRatingDataJobDTO(JobDataMap dataMap) {
        this.ratingConfigId = (Integer) dataMap.get("ratingConfigId");
        this.outDryTempParam = (String) dataMap.get("outDryTempParam");
        this.outWetTempParam = (String) dataMap.get("outWetTempParam");
        this.inDryTempParam = (String) dataMap.get("inDryTempParam");
        this.inWetTempParam = (String) dataMap.get("inWetTempParam");
        this.runningLoadParam = (String) dataMap.get("runningLoadParam");
        this.iTPowerParam = (String) dataMap.get("iTPowerParam");
        this.totalPowerParam = (String) dataMap.get("totalPowerParam");
        this.workingCondition = (String) dataMap.get("workingCondition");
        this.intervalSecond = (Integer) dataMap.get("intervalSecond");
    }

}
