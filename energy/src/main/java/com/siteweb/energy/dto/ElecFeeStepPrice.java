package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/** 电费方案相关阶梯定价类 */
@Data
@NoArgsConstructor
public class ElecFeeStepPrice {

    private Integer stepId;

    private Integer schemeId;
    private String stepName;
    private Integer upperLimit;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;
    /** 1-作为封顶阶梯； 0(或null或其他任何值)-表示非封顶阶梯 */
    private Integer asMaxStep;

    private String extendField1;
}
