package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@Measurement(name="EnergyHisFeeData")
public class HistoryComplexIndexFee {
    public static final long serialVersionUID = 1L;

    @Column(name = "time")
    //@JsonIgnore
    public String time;

    @Column(name = "ComplexIndexId", tag = true)
    public String complexIndexId;

    @Column(name = "CalcTime", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String calcTime;

    @Column(name = "IndexValue")
    public Double indexValue;

    @Column(name = "IndexFeeValue")
    public Double indexFeeValue;

    @Column(name = "UnitPrice")
    public Double UnitPrice;

    @Column(name = "SchemeId")
    public Integer schemeId;

    public String getFeeTime(){
        String tempTime = this.calcTime;
        if(tempTime!=null && !tempTime.equals("")){
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }

    public String getIndexFeeValue(){
        if (this.indexFeeValue == null || this.indexFeeValue.equals("")) {
            return "0";
        }
        BigDecimal value = new BigDecimal(this.indexFeeValue);
        return value.toString();
    }

    public Date getDateTime(){
        Date time = null ;
        try{
            time = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(getFeeTime());
        }catch (Exception e ){
        }
        return time;
    }
}