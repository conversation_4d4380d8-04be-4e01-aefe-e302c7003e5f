package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/** 电费方案相关峰平谷的值类 */
@Data
@NoArgsConstructor
public class ElecFeeFpgValue {

    private Integer fpgValueId;

    private Integer fpgId;
    private Integer stepId;
    private Integer schemeId;
    private String fpgValue;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;

    private String extendField1;

    private String stepName;
}
