package com.siteweb.energy.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HistoryComplexIndexResult {
    @Column(name = "time")
    public String time;

    @Column(name = "ComplexIndexId", tag = true)
    public String complexIndexId;

    @Column(name = "CalcTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String calcTime;

    @Column(name = "IndexValue")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String indexValue;

    @Column(name = "OriginalValue")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String originalValue;

    @Column(name = "Unit")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String unit;

    @Column(name = "Abnormal", tag = true)
    public String abnormal;

    @Column(name = "BusinessTypeId", tag = true)
    public String businessTypeId;

    public Date getDateTime() {
        Date time = null;
        try {
            time = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(getCalcTime());
        } catch (Exception e) {
        }
        return time;
    }

}
