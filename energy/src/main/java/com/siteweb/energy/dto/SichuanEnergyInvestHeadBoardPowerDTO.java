package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;

import java.util.Optional;

@Data
public class SichuanEnergyInvestHeadBoardPowerDTO {
    private Long baseTypeId;
    private String counterNo;
    private String road;
    private Integer equipmentId;
    private Integer signalId;
    private String signalKey;
    public String getSignalKey(){
//        return "755000031.120000021";
        return equipmentId+"."+signalId;
    }

    private Double dDiff1;
    private Double dDiff2;
    private Double dDiff3;
    private Double dDiff4;
    private Double dDiff5;
    private Double dDiff6;
    private Double dDiff7;
    private Double dDiff8;
    private Double dDiff9;
    private Double dDiff10;
    private Double dDiff11;
    private Double dDiff12;
    private Double dDiff13;
    private Double dDiff14;
    private Double dDiff15;
    private Double dDiff16;
    private Double dDiff17;
    private Double dDiff18;
    private Double dDiff19;
    private Double dDiff20;
    private Double dDiff21;
    private Double dDiff22;
    private Double dDiff23;
    private Double dDiff24;
    private Double dDiff25;
    private Double dDiff26;
    private Double dDiff27;
    private Double dDiff28;
    private Double dDiff29;
    private Double dDiff30;
    private Double dDiff31;

    private Double originValue1;
    private Double originValue2;
    private Double originValue3;
    private Double originValue4;
    private Double originValue5;
    private Double originValue6;
    private Double originValue7;
    private Double originValue8;
    private Double originValue9;
    private Double originValue10;
    private Double originValue11;
    private Double originValue12;
    private Double originValue13;
    private Double originValue14;
    private Double originValue15;
    private Double originValue16;
    private Double originValue17;
    private Double originValue18;
    private Double originValue19;
    private Double originValue20;
    private Double originValue21;
    private Double originValue22;
    private Double originValue23;
    private Double originValue24;
    private Double originValue25;
    private Double originValue26;
    private Double originValue27;
    private Double originValue28;
    private Double originValue29;
    private Double originValue30;
    private Double originValue31;
    private Double originValue32;

    public Double getDDiff1() {
        return calculateDDiff(originValue2, originValue1);
    }

    public Double getDDiff2() {
        return calculateDDiff(originValue3, originValue2);
    }

    public Double getDDiff3() {
        return calculateDDiff(originValue4, originValue3);
    }

    public Double getDDiff4() {
        return calculateDDiff(originValue5, originValue4);
    }

    public Double getDDiff5() {
        return calculateDDiff(originValue6, originValue5);
    }

    public Double getDDiff6() {
        return calculateDDiff(originValue7, originValue6);
    }

    public Double getDDiff7() {
        return calculateDDiff(originValue8, originValue7);
    }

    public Double getDDiff8() {
        return calculateDDiff(originValue9, originValue8);
    }

    public Double getDDiff9() {
        return calculateDDiff(originValue10, originValue9);
    }

    public Double getDDiff10() {
        return calculateDDiff(originValue11, originValue10);
    }

    public Double getDDiff11() {
        return calculateDDiff(originValue12, originValue11);
    }

    public Double getDDiff12() {
        return calculateDDiff(originValue13, originValue12);
    }
    public Double getDDiff13() {
        return calculateDDiff(originValue14, originValue13);
    }
    public Double getDDiff14() {
        return calculateDDiff(originValue15, originValue14);
    }
    public Double getDDiff15() {
        return calculateDDiff(originValue16, originValue15);
    }
    public Double getDDiff16() {
        return calculateDDiff(originValue17, originValue16);
    }
    public Double getDDiff17() {
        return calculateDDiff(originValue18, originValue17);
    }
    public Double getDDiff18() {
        return calculateDDiff(originValue19, originValue18);
    }
    public Double getDDiff19() {
        return calculateDDiff(originValue20, originValue19);
    }
    public Double getDDiff20() {
        return calculateDDiff(originValue21, originValue20);
    }
    public Double getDDiff21() {
        return calculateDDiff(originValue22, originValue21);
    }
    public Double getDDiff22() {
        return calculateDDiff(originValue23, originValue22);
    }
    public Double getDDiff23() {
        return calculateDDiff(originValue24, originValue23);
    }
    public Double getDDiff24() {
        return calculateDDiff(originValue25, originValue24);
    }
    public Double getDDiff25() {
        return calculateDDiff(originValue26, originValue25);
    }
    public Double getDDiff26() {
        return calculateDDiff(originValue27, originValue26);
    }
    public Double getDDiff27() {
        return calculateDDiff(originValue28, originValue27);
    }
    public Double getDDiff28() {
        return calculateDDiff(originValue29, originValue28);
    }
    public Double getDDiff29() {
        return calculateDDiff(originValue30, originValue29);
    }
    public Double getDDiff30() {
        return calculateDDiff(originValue31, originValue30);
    }
    public Double getDDiff31() {
        return calculateDDiff(originValue32, originValue31);
    }


    private Double calculateDDiff(Double originValueCurrent, Double originValuePrevious) {
        return Optional.ofNullable(originValueCurrent)
                .flatMap(current -> Optional.ofNullable(originValuePrevious)
                        .map(previous -> NumberUtil.doubleAccuracy(current - previous, 2)))
                .orElse(null);
    }
}
