package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/** 电费方案相关层级类 */
@Data
@NoArgsConstructor
public class ElecFeeStructure {

    private Integer mapId;

    private Integer schemeId;
    private Integer resourceStructureId;
    private String resourceStructureName;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;

    private String extendField1;
}
