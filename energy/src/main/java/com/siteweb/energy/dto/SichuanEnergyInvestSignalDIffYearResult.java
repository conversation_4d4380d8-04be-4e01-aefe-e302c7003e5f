package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;

import java.util.Optional;

@Data
public class SichuanEnergyInvestSignalDIffYearResult extends SichuanEnergyInvestSignalDiffDTO {
    //每个月份的差值
    private Double mDiff1;
    private Double mDiff2;
    private Double mDiff3;
    private Double mDiff4;
    private Double mDiff5;
    private Double mDiff6;
    private Double mDiff7;
    private Double mDiff8;
    private Double mDiff9;
    private Double mDiff10;
    private Double mDiff11;
    private Double mDiff12;

    //原始月份数据
    private Double originValue1;
    private Double originValue2;
    private Double originValue3;
    private Double originValue4;
    private Double originValue5;
    private Double originValue6;
    private Double originValue7;
    private Double originValue8;
    private Double originValue9;
    private Double originValue10;
    private Double originValue11;
    private Double originValue12;
    private Double originValue13;

    public Double getMDiff1() {
        return calculateMDiff(originValue2, originValue1);
    }

    public Double getMDiff2() {
        return calculateMDiff(originValue3, originValue2);
    }

    public Double getMDiff3() {
        return calculateMDiff(originValue4, originValue3);
    }

    public Double getMDiff4() {
        return calculateMDiff(originValue5, originValue4);
    }

    public Double getMDiff5() {
        return calculateMDiff(originValue6, originValue5);
    }

    public Double getMDiff6() {
        return calculateMDiff(originValue7, originValue6);
    }

    public Double getMDiff7() {
        return calculateMDiff(originValue8, originValue7);
    }

    public Double getMDiff8() {
        return calculateMDiff(originValue9, originValue8);
    }

    public Double getMDiff9() {
        return calculateMDiff(originValue10, originValue9);
    }

    public Double getMDiff10() {
        return calculateMDiff(originValue11, originValue10);
    }

    public Double getMDiff11() {
        return calculateMDiff(originValue12, originValue11);
    }

    public Double getMDiff12() {
        return calculateMDiff(originValue13, originValue12);
    }


    private Double calculateMDiff(Double originValueCurrent, Double originValuePrevious) {
        return Optional.ofNullable(originValueCurrent)
                .flatMap(current -> Optional.ofNullable(originValuePrevious)
                        .map(previous -> NumberUtil.doubleAccuracy(current - previous, 2)))
                .orElse(null);
    }
}
