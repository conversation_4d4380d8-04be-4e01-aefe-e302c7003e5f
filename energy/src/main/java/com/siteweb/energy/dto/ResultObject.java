package com.siteweb.energy.dto;

import lombok.Data;

/**
 * http处理返回统一结果结构
 */
@Data
public class ResultObject<T> {
    public static Integer SUCCESS = 1;
    public static Integer ERROR = 0;

    /**
     * 返回是否成功的状态, 1表示成功, 0表示失败
     */
    public Integer state;

    /**
     * 成功时候,返回的数据对象
     */
    public T data;

    /**
     * 是错误时候的错误消息
     */
    public String errmsg;


    /**
     * 错误时候的错误码
     */
    public Integer errcode;

    public ResultObject()
    {
        state = SUCCESS;
    }

    public ResultObject(Integer state, T data, String errmsg, Integer errcode)
    {
        this.state = state;
        this.data = data;
        this.errmsg = errmsg;
        this.errcode = errcode;
    }

    public ResultObject(String errMsg, Integer errcode)
    {
        state = ERROR;
        data = null;
        this.errcode = errcode;
        this.errmsg = errMsg;
    }

    public ResultObject(T data)
    {
        state = SUCCESS;
        this.data = data;
    }

}

