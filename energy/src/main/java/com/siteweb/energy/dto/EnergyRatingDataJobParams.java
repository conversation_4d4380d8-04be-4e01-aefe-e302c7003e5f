package com.siteweb.energy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.bind.DefaultValue;

import java.util.Date;

@Data
public class EnergyRatingDataJobParams {

    @ApiModelProperty("配置id")
    private Integer ratingConfigId;

    @ApiModelProperty("工况")
    private String workingCondition;

    @ApiModelProperty("运行时间(分钟)")
    private Integer runTime;

}
