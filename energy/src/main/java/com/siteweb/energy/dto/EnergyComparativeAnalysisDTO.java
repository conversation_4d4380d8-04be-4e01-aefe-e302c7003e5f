package com.siteweb.energy.dto;

import lombok.Data;

@Data
public class EnergyComparativeAnalysisDTO {

    //能耗类型id
    private Integer businessTypeId;
    //能耗类型名
    private String businessTypeName;
    //总用量
    private Double value;
    //总用碳
    private Double valueCo2;
    //总标煤
    private Double valueCoal;
    //同比
    private Double YoY;
    //同比值
    private Double valueYoY;
    //环比
    private Double CoC;
    //环比值
    private Double valueCoC;
    //单位
    private String unit;
    //计划量
    private Double planValue;
    //计划标煤
    private Double planValueCoal;
    //计划碳
    private Double planValueCo2;
}
