package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergySavingListDTO extends EnergySavingDataDTO {
    private Integer objectId;

    private String ObjectName;

    private String levelOfPath;

    private Integer people;

    private Double area;

    private Double energySavingRate;

    private String businessTypeName;

    private Double addEnergySaving;

    public EnergySavingListDTO(EnergySavingDataDTO energySavingDataDTO) {
        this.setEnergySavingValue(energySavingDataDTO.getEnergySavingValue());

        this.setPlanValue(energySavingDataDTO.getPlanValue());

        this.setOverstepValue(energySavingDataDTO.getOverstepValue());

        this.setValue(energySavingDataDTO.getValue());

        this.setEnergySavingValueCoC(energySavingDataDTO.getEnergySavingValueCoC());

        this.setPlanValueCoC(energySavingDataDTO.getPlanValueCoC());

        this.setOverstepValueCoC(energySavingDataDTO.getOverstepValueCoC());

        this.setValueCoC(energySavingDataDTO.getValueCoC());

        this.setEnergySavingValueYoY(energySavingDataDTO.getEnergySavingValueYoY());

        this.setPlanValueYoY(energySavingDataDTO.getPlanValueYoY());

        this.setOverstepValueYoY(energySavingDataDTO.getOverstepValueYoY());

        this.setValueYoY(energySavingDataDTO.getValueYoY());

        this.setUnit(energySavingDataDTO.getUnit());
    }

    public void setEnergySavingRate() {
        Double value = this.getValue();
        Double planValue = this.getPlanValue();
        if (planValue == 0){
            this.setEnergySavingRate(0d);
        }else{
            this.setEnergySavingRate(NumberUtil.doubleAccuracy((planValue-value)/planValue*100,2));
        }
    }
}
