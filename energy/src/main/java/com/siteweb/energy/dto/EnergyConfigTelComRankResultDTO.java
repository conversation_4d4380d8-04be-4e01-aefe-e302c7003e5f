package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;

@Data
public class EnergyConfigTelComRankResultDTO {
    private String resourceStructureName;
    private Integer resourceStructureId;
    private Double totalEnergy;
    private Double cityElectValue;
    private Double oilElectValue;
    private Double greenElectValue;
    private Double totalCarbonValue;
    private Double cityElectCarbonValue;
    private Double oilElectCarbonValue;
    private Double greenElectCarbonValue;

    public Double getTotalEnergy(){
        double city = this.cityElectValue == null ? 0 : this.cityElectValue;
        double oil = this.oilElectValue == null ? 0 : this.oilElectValue;
        double green = this.greenElectValue == null ? 0 : this.greenElectValue;
        return NumberUtil.doubleAccuracy((city + oil + green),2);
    }

    public Double getTotalCarbonValue(){
        double cityCarbon = this.cityElectCarbonValue == null ? 0 : this.cityElectCarbonValue;
        double oilCarbon = this.oilElectCarbonValue == null ? 0 : this.oilElectCarbonValue;
        return NumberUtil.doubleAccuracy((cityCarbon + oilCarbon ),2);
    }
}
