package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class EnergySdkResourceComplexIndexDTO {
    /**
     * 层级ID
     */
    private Integer nodeId;
    /**
     * 层级类型
     */
    private Integer nodeTypeId;
    /**
     * 层级类型名称
     */
    private String nodeTypeName;
    /**
     * 父节点ID
     */
    private Integer parentNodeId;
    /**
     * 层级名称
     */
    private String nodeName;
    /**
     * 层级下属指标信息
     */
    private List<ComplexIndexInfoDTO> dataList;
}
