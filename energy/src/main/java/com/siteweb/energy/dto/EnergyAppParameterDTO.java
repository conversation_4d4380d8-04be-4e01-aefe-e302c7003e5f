package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class EnergyAppParameterDTO {
    private Integer itemId;      //总量 分项 效率 其他
    private String complexIndexIds;    //查询的指标
    private String timeType;    //查询类型 'y' 'm' 'd' 'h'
    private Date startTime;     //查询开始时间
    private Date endTime;       //查询结束时间
}
