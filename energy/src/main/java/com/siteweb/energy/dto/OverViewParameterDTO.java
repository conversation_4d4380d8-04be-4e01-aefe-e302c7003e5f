package com.siteweb.energy.dto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class OverViewParameterDTO {
    private Integer businessTypeId; //电2 水3 气4
    private Integer dimensionTypeId;    //层级对象
    private String resourceStructureIds;//前台过滤筛选的节点
    private Long userId;                //登录用户
    private Date startTime;             //查询开始时间
    private Date endTime;               //查询结束时间
    private String timeType;    //查询类型 'y' 'm'
    private String sequence;    //排序方式 'asc' 'desc' 'default'
}
