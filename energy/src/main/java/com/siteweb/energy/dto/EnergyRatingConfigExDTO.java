package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import com.siteweb.energy.entity.EnergyRatingConfig;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@NoArgsConstructor
public class EnergyRatingConfigExDTO extends EnergyRatingConfig {
    private String outDryTempName;
    private String outWetTempName;
    private String inDryTempName;
    private String inWetTempName;
    private String runningLoadName;
    private String itPowerName;
    private String totalPowerName;


    public EnergyRatingConfigExDTO (EnergyRatingConfig config){
        this.setRatingConfigId(config.getRatingConfigId());
        this.setOutDryTempParam(config.getOutDryTempParam());
        this.setOutWetTempParam(config.getOutWetTempParam());
        this.setInDryTempParam(config.getInDryTempParam());

        this.setInWetTempParam(config.getInWetTempParam());
        this.setRunningLoadParam(config.getRunningLoadParam());
        this.setItPowerParam(config.getItPowerParam());
        this.setTotalPowerParam(config.getTotalPowerParam());

        this.setObjectId(config.getObjectId());
        this.setName(config.getName());
        this.setIntervalSecond(config.getIntervalSecond());
        this.setStatus(config.getStatus());

        this.setStartTime(config.getStartTime());
        this.setEndTime(config.getEndTime());
        this.setWorkingCondition(config.getWorkingCondition());
        this.setUserId(config.getUserId());
        this.setCityId(config.getCityId());

    }
}



