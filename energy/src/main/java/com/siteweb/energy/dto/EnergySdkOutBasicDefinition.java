package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergySdkOutBasicDefinition {

    private Integer labelId;

    private String labelName;

    private Integer indexId;

    private String indexName;

    private Double value;

    public EnergySdkOutBasicDefinition(EnergySdkBasicDefinition energySdkBasicDefinition){
        this.indexId = energySdkBasicDefinition.getIndexId();
        this.indexName = energySdkBasicDefinition.getIndexName();
        this.labelId = energySdkBasicDefinition.getComplexIndexDefinitionId();
        this.labelName = energySdkBasicDefinition.getComplexIndexDefinitionName();
        this.value = energySdkBasicDefinition.getValue();
    }

}
