package com.siteweb.energy.dto;
import com.siteweb.common.util.NumberUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class EnergyReportMonthTrendResultDTO extends EnergyReportTrendResultDTO {

    private Double D1;
    private Double D2;
    private Double D3;
    private Double D4;
    private Double D5;
    private Double D6;
    private Double D7;
    private Double D8;
    private Double D9;
    private Double D10;
    private Double D11;
    private Double D12;
    private Double D13;
    private Double D14;
    private Double D15;
    private Double D16;
    private Double D17;
    private Double D18;
    private Double D19;
    private Double D20;
    private Double D21;
    private Double D22;
    private Double D23;
    private Double D24;
    private Double D25;
    private Double D26;
    private Double D27;
    private Double D28;
    private Double D29;
    private Double D30;
    private Double D31;
    private Double Total;

    public EnergyReportMonthTrendResultDTO(EnergyReportTrendResultDTO dto){
        this.setComplexIndexId(dto.getComplexIndexId());
        this.setObjectName(dto.getObjectName());
        this.setSearchTypeName(dto.getSearchTypeName());
        this.setComplexIndexDefinitionId(dto.getComplexIndexDefinitionId());
        this.setObjectTypeId(dto.getObjectTypeId());
        this.setObjectId(dto.getObjectId());
        this.setUnit(dto.getUnit());
        this.setClassificationName(dto.getClassificationName());
        this.setClassificationId(dto.getClassificationId());
        this.setComplexIndexName(dto.getComplexIndexName());
    }

    //赋值总量
    public void SetTotalConsume(){
        Double tmpTotal =
                (D1 == null ? 0d : D1) +
                (D2 == null ? 0d : D2) +
                (D3 == null ? 0d : D3) +
                (D4 == null ? 0d : D4) +
                (D5 == null ? 0d : D5) +
                (D6 == null ? 0d : D6) +
                (D7 == null ? 0d : D7) +
                (D8 == null ? 0d : D8) +
                (D9 == null ? 0d : D9) +
                (D10 == null ? 0d : D10) +
                (D11 == null ? 0d : D11) +
                (D12 == null ? 0d : D12) +
                (D13 == null ? 0d : D13) +
                (D14 == null ? 0d : D14) +
                (D15 == null ? 0d : D15) +
                (D16 == null ? 0d : D16) +
                (D17 == null ? 0d : D17) +
                (D18 == null ? 0d : D18) +
                (D19 == null ? 0d : D19) +
                (D20 == null ? 0d : D20) +
                (D21 == null ? 0d : D21) +
                (D22 == null ? 0d : D22) +
                (D23 == null ? 0d : D23) +
                (D24 == null ? 0d : D24) +
                (D25 == null ? 0d : D25) +
                (D26 == null ? 0d : D26) +
                (D27 == null ? 0d : D27) +
                (D28 == null ? 0d : D28) +
                (D29 == null ? 0d : D29) +
                (D30 == null ? 0d : D30) +
                (D31 == null ? 0d : D31);
        this.setTotal(NumberUtil.doubleAccuracy(tmpTotal,2));
    }
    //赋值平均值
    public void SetAvgConsume(){
        SetTotalConsume();
        Double tempTotal = this.getTotal();
        if (tempTotal == null ||  tempTotal == 0) {
            this.setTotal(0d);
            return;
        }

        Integer tempValidCount = 0;
        if (D1 != null) tempValidCount++;
        if (D2 != null) tempValidCount++;
        if (D3 != null) tempValidCount++;
        if (D4 != null) tempValidCount++;
        if (D5 != null) tempValidCount++;
        if (D6 != null) tempValidCount++;
        if (D7 != null) tempValidCount++;
        if (D8 != null) tempValidCount++;
        if (D9 != null) tempValidCount++;
        if (D10 != null) tempValidCount++;
        if (D11 != null) tempValidCount++;
        if (D12 != null) tempValidCount++;
        if (D13 != null) tempValidCount++;
        if (D14 != null) tempValidCount++;
        if (D15 != null) tempValidCount++;
        if (D16 != null) tempValidCount++;
        if (D17 != null) tempValidCount++;
        if (D18 != null) tempValidCount++;
        if (D19 != null) tempValidCount++;
        if (D20 != null) tempValidCount++;
        if (D21 != null) tempValidCount++;
        if (D22 != null) tempValidCount++;
        if (D23 != null) tempValidCount++;
        if (D24 != null) tempValidCount++;
        if (D25 != null) tempValidCount++;
        if (D26 != null) tempValidCount++;
        if (D27 != null) tempValidCount++;
        if (D28 != null) tempValidCount++;
        if (D29 != null) tempValidCount++;
        if (D30 != null) tempValidCount++;
        if (D31 != null) tempValidCount++;

        this.setTotal(NumberUtil.doubleAccuracy(tempTotal/tempValidCount,2));
    }

}

