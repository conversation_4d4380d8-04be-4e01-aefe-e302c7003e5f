package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name = "historydatas")
public class EnergyHisDataSichuanResult {
    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "SignalId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String signalId;

    @Column(name = "PointValue")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String pointValue;

    public String getTime() {
        String tempTime = this.time.toString();
        if (tempTime != null && !tempTime.equals("")) {
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }
}
