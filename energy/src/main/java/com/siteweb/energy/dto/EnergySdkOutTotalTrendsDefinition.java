package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class EnergySdkOutTotalTrendsDefinition {

    private Integer labelId;

    private String labelName;

    private Integer indexId;

    private String indexName;

    private List<EnergySdkTotalTrendData> data;

    public EnergySdkOutTotalTrendsDefinition(EnergySdkTotalTrendsDefinition energySdkTotalTrendsDefinition){
        this.indexId = energySdkTotalTrendsDefinition.getIndexId();
        this.indexName = energySdkTotalTrendsDefinition.getIndexName();
        this.labelId = energySdkTotalTrendsDefinition.getComplexIndexDefinitionId();
        this.labelName = energySdkTotalTrendsDefinition.getComplexIndexDefinitionName();
        this.data = energySdkTotalTrendsDefinition.getData();
    }

}
