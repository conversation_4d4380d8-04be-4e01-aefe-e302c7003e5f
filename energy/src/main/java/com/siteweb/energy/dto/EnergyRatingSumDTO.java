package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.DateTime;

import java.util.Date;

@Data
@NoArgsConstructor
public class EnergyRatingSumDTO {
    private String workingCondition;

    private Date minSampleTime;
    private Date maxSampleTime;

    private Double avgTotalPower;
    private Double avgITPower;
    private Double avgRunningLoad;
    private Double sumIntervalSecond;

    private Double avgOutDryTemp;
    private Double avgOutWetTemp;
    private Double avgInDryTemp;
    private Double avgInWetTemp;
}