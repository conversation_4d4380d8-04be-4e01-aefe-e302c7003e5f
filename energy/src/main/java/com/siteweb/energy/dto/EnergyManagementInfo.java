package com.siteweb.energy.dto;


import com.siteweb.energy.entity.EnergyManagement;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergyManagementInfo extends EnergyManagement {
    private Integer StructureCount;
    private Integer StructureIsMarkCount;

    public void getFromEnergyManagement(EnergyManagement em){
        this.setEnergyId(em.getEnergyId());
        this.setEnergyName(em.getEnergyName());
        this.setEndTime(em.getEndTime());
        this.setNotes(em.getNotes());
        this.setContact(em.getContact());
        this.setOperator(em.getOperator());
        this.setStartTime(em.getStartTime());
        this.setUnit(em.getUnit());
    }
}
