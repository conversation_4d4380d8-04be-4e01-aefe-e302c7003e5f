package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergyTaiBoResultDTO extends EnergyReportTrendResultDTO {
    /*
    供电量
     */
    private Double sumValue;

    public EnergyTaiBoResultDTO(EnergyReportTrendResultDTO dto){
        this.setComplexIndexId(dto.getComplexIndexId());
        this.setObjectName(dto.getObjectName());
        this.setSearchTypeName(dto.getSearchTypeName());
        this.setComplexIndexDefinitionId(dto.getComplexIndexDefinitionId());
        this.setObjectTypeId(dto.getObjectTypeId());
        this.setObjectId(dto.getObjectId());
        this.setUnit(dto.getUnit());
    }

}
