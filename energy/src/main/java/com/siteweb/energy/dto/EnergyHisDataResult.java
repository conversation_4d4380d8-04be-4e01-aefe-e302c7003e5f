package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnergyHisDataResult {

    public static final long serialVersionUID = 1L;

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public String time;

    @Column(name = "result")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String result;

    @Column(name = "ComplexIndexId",tag = true)
    public String complexIndexId;

    @Column(name = "Unit",tag = true)
    public String unit;

    @Column(name = "IndexValue",tag = true)
    public String indexValue;
}
