package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class EnergySdkOutBasicTrendsDefinition {

    private Integer labelId;

    private String labelName;

    private Integer indexId;

    private String indexName;

    private List<EnergySdkBasicTrendData> data;

    public EnergySdkOutBasicTrendsDefinition(EnergySdkBasicTrendsDefinition energySdkBasicTrendsDefinition) {
        this.indexId = energySdkBasicTrendsDefinition.getIndexId();
        this.indexName = energySdkBasicTrendsDefinition.getIndexName();
        this.labelId = energySdkBasicTrendsDefinition.getComplexIndexDefinitionId();
        this.labelName = energySdkBasicTrendsDefinition.getComplexIndexDefinitionName();
        this.data = energySdkBasicTrendsDefinition.getData();
    }

}
