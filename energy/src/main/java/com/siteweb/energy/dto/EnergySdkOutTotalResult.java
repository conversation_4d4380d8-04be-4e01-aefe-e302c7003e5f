package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
/**
 * 用能效率
 */
public class EnergySdkOutTotalResult {

    private String nodeName;

    private Integer energyTypeId;

    private String energyTypeName;

    private List<EnergySdkOutTotalDefinition> definitions;


    public EnergySdkOutTotalResult(EnergySdkTotalResult energySdkTotalResult){
        this.nodeName = energySdkTotalResult.getResourceStructureName();
        this.energyTypeId = energySdkTotalResult.getBusinessTypeId();
        this.energyTypeName = energySdkTotalResult.getBusinessTypeName();
        List<EnergySdkOutTotalDefinition> temp = new ArrayList<>();
        for (EnergySdkTotalDefinition definition : energySdkTotalResult.getDefinitions()) {
            temp.add(new EnergySdkOutTotalDefinition(definition));
        }
        this.definitions = temp;

    }

}
