package com.siteweb.energy.dto;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/** 电费方案数据类 */
@Data
@NoArgsConstructor
public class ElecFeeScheme {

    private Integer schemeId;

    private String schemeName;

    private String appliedRange;

    private Date enableDate;

    private Date deactivateDate;

    private Integer startMonth;
    private Integer endMonth;
    /** 由字段startMonth与endMonth组合而成, eg: 1月-3月 */
    private String appliedMonth;

    /** 是否启用 */
    private Boolean enableStatusBool;
    /** 是否启用(启用 or 禁用) */
    private String enableStatus;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;
    private Integer businessTypeId;
    private String businessTypeName;

    private String extendField1;
    private String extendField2;

    private String typeName;

    private List<ElecFeeStructure> structures;
    private List<ElecFeeStepPrice> stepPrices;
    private List<ElecFeeFpg> fpgs;

    private List<Integer> structureIds;

    public String joinAppliedMonth(LocaleMessageSourceUtil messageSourceUtil) {
        if(startMonth == null || endMonth == null) {
            return "";
        } else {
            String mKey = "energy.enum.month.m";
            String startMonthTxt = messageSourceUtil.getMessage(mKey + startMonth);
            String endMonthTxt = messageSourceUtil.getMessage(mKey + endMonth);
            return startMonthTxt + "-" + endMonthTxt;
        }
    }
}
