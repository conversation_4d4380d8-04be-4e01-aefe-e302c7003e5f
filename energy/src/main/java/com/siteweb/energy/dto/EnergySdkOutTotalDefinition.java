package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergySdkOutTotalDefinition {

    private Integer labelId;

    private String labelName;

    private Integer indexId;

    private String indexName;
    private Double value;

    private Double coalValue;

    private Double carbonValue;

    EnergySdkOutTotalDefinition(EnergySdkTotalDefinition energySdkTotalDefinition){
        this.indexId = energySdkTotalDefinition.getIndexId();
        this.indexName = energySdkTotalDefinition.getIndexName();
        this.labelId = energySdkTotalDefinition.getComplexIndexDefinitionId();
        this.labelName = energySdkTotalDefinition.getComplexIndexDefinitionName();
        this.value = energySdkTotalDefinition.getValue();
        this.carbonValue = energySdkTotalDefinition.getCarbonValue();
        this.coalValue = energySdkTotalDefinition.getCoalValue();
    }

}
