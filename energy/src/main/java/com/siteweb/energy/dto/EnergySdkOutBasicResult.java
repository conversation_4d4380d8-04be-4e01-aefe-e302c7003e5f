package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
/**
 * 用能效率
 */
public class EnergySdkOutBasicResult {


    private String nodeName;

    private Integer energyTypeId;

    private String energyTypeName;

    private List<EnergySdkOutBasicDefinition> definitions;

    public EnergySdkOutBasicResult(EnergySdkBasicResult energySdkBasicResult){
        this.nodeName = energySdkBasicResult.getResourceStructureName();
        this.energyTypeId = energySdkBasicResult.getBusinessTypeId();
        this.energyTypeName = energySdkBasicResult.getBusinessTypeName();
        List<EnergySdkOutBasicDefinition> temp = new ArrayList<>();
        for (EnergySdkBasicDefinition definition : energySdkBasicResult.getDefinitions()) {
            temp.add(new EnergySdkOutBasicDefinition(definition));
        }
        this.definitions = temp;
    }

}
