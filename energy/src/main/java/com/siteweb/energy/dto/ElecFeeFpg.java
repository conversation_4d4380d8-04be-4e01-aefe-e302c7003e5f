package com.siteweb.energy.dto;

import com.siteweb.common.util.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/** 电费方案相关峰平谷的时间区间类 */
@Data
@NoArgsConstructor
public class ElecFeeFpg {

    private Integer fpgId;

    private Integer schemeId;
    private String effectiveStartStr;
    private String effectiveEndStr;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;

    private String extendField1;

    private Integer fpgDescKey;
    private String fpgDesc;

    private List<ElecFeeFpgValue> fpgValues;

    private Date effectiveStart;
    private Date effectiveEnd;
    private long effectiveStartMill;
    private long effectiveEndMill;

    public void initEffectiveTimeData() {
        if(this.getEffectiveStart() == null) {
            this.setEffectiveStart(convertHHmmssToDate(this.getEffectiveStartStr()));
            this.setEffectiveStartMill(this.getEffectiveStart().getTime());
        }
        if(this.getEffectiveEnd() == null) {
            this.setEffectiveEnd(convertHHmmssToDate(this.getEffectiveEndStr()));
            this.setEffectiveEndMill(this.getEffectiveEnd().getTime());
        }
    }

    /** 将 HH:mm:ss 格式的时间，统一转为 2000-01-01 HH:mm:ss 格式的Date对象。 */
    public static Date convertHHmmssToDate(String hhmmssTimeStr) {
        String dateStr = "2000-01-01 " + hhmmssTimeStr;
        return DateUtil.stringToDate(dateStr);
    }

}
