package com.siteweb.energy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("评级采集值查询条件")
public class EnergyRatingDataParams {
    /**
     * 需要排序的字段关键字
     */
    @ApiModelProperty("需要排序的字段关键字")
    private String orderByKey;
    /**
     * 排序规则 asc升序  desc降序
     */
    @ApiModelProperty("排序规则 asc升序  desc降序")
    private String orderByDirection;
    /**
     * 每页显示多少条
     */
    @ApiModelProperty("每页显示多少条 不传默认为：10")
    private Integer pageSize = 10;
    /**
     * 第几页
     */
    @ApiModelProperty("第几页 不传默认为：1")
    private Integer PageNumber = 1;

    @ApiModelProperty("配置id")
    private Integer ratingConfigId;

    @ApiModelProperty("工况")
    private String workingCondition;
}
