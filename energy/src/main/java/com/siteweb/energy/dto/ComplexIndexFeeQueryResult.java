package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name="EnergyHisFeeData")
public class ComplexIndexFeeQueryResult {

    public static final long serialVersionUID = 1L;

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "result")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String result;

    public Integer complexIndexId;
}

