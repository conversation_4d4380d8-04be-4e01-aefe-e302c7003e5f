package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Measurement(name="EnergyHisMonthUEData")
public class EnergyHisMonthUEDataResult {

    public static final long serialVersionUID = 1L;

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "result",tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String result;

    @Column(name = "ComplexIndexId",tag = true)
    public String complexIndexId;

}

