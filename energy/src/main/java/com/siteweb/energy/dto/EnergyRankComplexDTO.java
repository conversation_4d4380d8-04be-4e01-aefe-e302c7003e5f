package com.siteweb.energy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnergyRankComplexDTO {
    /*总能耗*/
    private Double totalValue;
    /*总能耗环比*/
    private Double totalValueCoC;
    /*总能耗同比*/
    private Double totalValueYoY;
    /*人均能耗*/
    private Double manAvgEnergy;
    /*单位面积能耗*/
    private Double areaAvgEnergy;
    /*节能率*/
    private Double energySavingRate;
    /*节能率环比*/
    private Double energySavingRateCoC;
    /*节能率同比*/
    private Double energySavingRateYoY;
    /*单位*/
    private String unit;
}
