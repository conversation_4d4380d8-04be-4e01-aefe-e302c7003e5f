package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnergyUEMaxMinAvgDTO {

    public static final long serialVersionUID = 1L;

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "maxValue",tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String maxValue;

    @Column(name = "minValue",tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String minValue;

    @Column(name = "avgValue",tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String avgValue;

    @Column(name = "countNum",tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String countNum;




}

