package com.siteweb.energy.dto;

import lombok.Data;

@Data
public class EnergyConfigTelComResultDTO {

    //总用电量、同环比
    private Double totalElectricityValue;
    private Double yoyTotalElectricityValue;
    private Double qoqTotalElectricityValue;

    //市电、同环比
    private Double cityElectValue ;
    private Double yoyCityElectValue;
    private Double qoqCityElectValue;
    //油机、同环比
    private Double oilElectValue ;
    private Double yoyOilElectValue;
    private Double qoqOilElectValue;
    //绿电、同环比
    private Double greenElectValue ;
    private Double yoyGreenElectValue;
    private Double qoqGreenElectValue;

    //总碳排放、同环比
    private Double totalCarbonValue ;
    private Double yoyTotalCarbonValue;
    private Double qoqTotalCarbonValue;
    //市电碳排放
    private Double cityElectCarbonValue ;
    //油机碳排放
    private Double oilElectCarbonValue ;
    //绿电碳排放
    private Double greenElectCarbonValue;
    private Double yoyGreenElectCarbonValue;
    private Double qoqGreenElectCarbonValue;
}
