package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
@NoArgsConstructor
public class EnergyComplexTree {
    private Integer parentId;
    private Integer id;
    private String name;
    private Integer type;  //0:指标 1:设备 2:信号
    private List<EnergyComplexTree> children;

    public EnergyComplexTree(Integer parentId, Integer id, String name, Integer type) {
        this.parentId = parentId;
        this.id = id;
        this.name = name;
        this.type = type;
        this.children = new ArrayList<>();
    }

    public void addChild(EnergyComplexTree node) {
        this.children.add(node);
    }
}
