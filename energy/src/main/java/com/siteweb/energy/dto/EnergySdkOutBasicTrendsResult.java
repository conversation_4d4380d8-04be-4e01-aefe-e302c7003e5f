package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
/**
 * 用能效率
 */
public class EnergySdkOutBasicTrendsResult {


    private String nodeName;

    private Integer energyTypeId;

    private String energyTypeName;

    private List<EnergySdkOutBasicTrendsDefinition> definitions;

    public EnergySdkOutBasicTrendsResult(EnergySdkBasicTrendsResult energySdkBasicTrendsResult) {
        this.nodeName = energySdkBasicTrendsResult.getResourceStructureName();
        this.energyTypeId = energySdkBasicTrendsResult.getBusinessTypeId();
        this.energyTypeName = energySdkBasicTrendsResult.getBusinessTypeName();
        List<EnergySdkOutBasicTrendsDefinition> temp = new ArrayList<>();
        for (EnergySdkBasicTrendsDefinition definition : energySdkBasicTrendsResult.getDefinitions()) {
            temp.add(new EnergySdkOutBasicTrendsDefinition((definition)));
        }
        this.definitions = temp;
    }

}
