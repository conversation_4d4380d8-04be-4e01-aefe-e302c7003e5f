package com.siteweb.energy.dto;

import com.siteweb.energy.entity.EnergyCarbonEmissionPara;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class EnergyCarbonEmissionDTO {
    /**
     * 节点ID
     */
    private String objectId;
    /**
     * 节点类型
     */
    private String objectTypeId;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 年总配额
     */
    private Float yearPlanTotalValue;
    /**
     * 面积
     */
    private Float area;
    /**
     * 面积还是产出。面积1；产出0
     */
    private Integer isArea;
    /**
     * 单位i
     */
    private String units;
    /**
     * 月配额
     */
    private List<EnergyCarbonManageDTO> list;

    public List<EnergyCarbonEmissionPara> getEnergyCarbonEmissionDataList(){
        List<EnergyCarbonEmissionPara> energyCarbonEmissionData = new ArrayList<>();
        for (EnergyCarbonManageDTO e:this.list){
            EnergyCarbonEmissionPara data = new EnergyCarbonEmissionPara();
            data.setMonth(e.getMonth());
            data.setId(e.getId());
            data.setObjectId(this.objectId);
            data.setObjectTypeId(this.objectTypeId);
            data.setYear(this.year);
            data.setPlanValue(e.getPlanValue());
            data.setYearPlanTotalValue(this.yearPlanTotalValue);
            data.setArea(this.area);
            data.setIsArea(this.isArea);
            data.setUnits(this.units);
            energyCarbonEmissionData.add(data);
        }
        return energyCarbonEmissionData;
    }
}
