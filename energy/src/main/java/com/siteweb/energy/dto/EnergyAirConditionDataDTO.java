package com.siteweb.energy.dto;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@NoArgsConstructor
public class EnergyAirConditionDataDTO {
    private String operateMode; //运行模式
    private String dryTemperatureRange; //干球温度区间
    private String totalITPower; //总IT功率
    private String airConditioningPower; //空调功率
    private String totalDistributionLosses; //总配电损耗
    private String totalOtherPower; //其他总功率
    private String totalOperateTime; //运行总时间
    private String clfPlfPue;  //Clf/Plf/Pue
    private String operateDateTime; //运行日期及时间点
}

