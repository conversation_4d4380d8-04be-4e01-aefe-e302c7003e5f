package com.siteweb.energy.dto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class EnergyReportParameterDTO {
    private Integer businessTypeId;     //电水气
    private Integer dimensionTypeId;    //层级对象
    private String resourceStructureIds;    //前台过滤筛选的节点
    private Integer complexIndexDefinitionTypeId;   //总量、效率、分项、其他等
    private String complexIndexDefinitionIds; //指标定义
    private String timeType;    //查询类型 'y' 'm'
    private Integer userId;     //登录用户
    private Date startTime;     //查询开始时间
    private Date endTime;       //查询结束时间


    //四级分类Ids
    private String classificationIds;

}
