package com.siteweb.energy.dto;

import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ComplexIndexBusinessTypeCoefficient extends ComplexIndexBusinessType {
    private Double coal;
    private Double co2;

    public ComplexIndexBusinessTypeCoefficient(ComplexIndexBusinessType complexIndexBusinessType){
        this.setBusinessTypeName(complexIndexBusinessType.getBusinessTypeName());
        this.setBusinessTypeId(complexIndexBusinessType.getBusinessTypeId());
        this.setDescription(complexIndexBusinessType.getDescription());
        this.setParentId(complexIndexBusinessType.getParentId());
    }
}
