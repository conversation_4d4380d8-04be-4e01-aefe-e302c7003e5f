package com.siteweb.energy.dto;

import com.siteweb.energy.entity.EnergyManagement;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.siteweb.energy.dto.EnergyConsumeDataDTO;
import com.siteweb.energy.entity.EnergyConsumeData;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class EnergyConsumeDataParamsDTO {
    /**
     * 当前资源id
     */
    private String objectId;
    /**
     * 当前资源类型
     */
    private String objectTypeId;

    private Integer energyTypeId;

    private Integer year;

    private List<EnergyConsumeDataDTO> list;

    public List<EnergyConsumeData> getEnergyConsumeDataList(){
        List<EnergyConsumeData> energyConsumeData = new ArrayList<>();
        for (EnergyConsumeDataDTO e:this.list){
            EnergyConsumeData data = new EnergyConsumeData();
            data.setPlanValuePreAlarm(e.getPlanValuePreAlarm());
            data.setOverstepValuePreAlarm(e.getOverstepValuePreAlarm());
            data.setId(e.getId());
            data.setObjectId(this.objectId);
            data.setObjectTypeId(this.objectTypeId);
            data.setEnergyTypeId(this.energyTypeId);
            data.setYear(this.year);
            data.setMonth(e.getMonth());
            data.setEnergySavingValue(e.getEnergySavingValue());
            data.setPlanValue(e.getPlanValue());
            data.setOverstepValue(e.getOverstepValue());
            energyConsumeData.add(data);
        }
        return energyConsumeData;
    }
}
