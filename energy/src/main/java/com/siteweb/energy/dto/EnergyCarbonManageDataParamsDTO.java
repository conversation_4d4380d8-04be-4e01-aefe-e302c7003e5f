package com.siteweb.energy.dto;

import com.siteweb.energy.entity.EnergyCarbonManegePara;import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnergyCarbonManageDataParamsDTO {


    private String objectId;

    private String objectTypeId;

    private Integer year;

    private Float yearPlanTotalValue;

    private List<EnergyCarbonManageDTO> list;

    public List<EnergyCarbonManegePara> getEnergyCarbonDataList(){
        List<EnergyCarbonManegePara> energyCarbonData = new ArrayList<>();
        for (EnergyCarbonManageDTO e:this.list){
            EnergyCarbonManegePara data = new EnergyCarbonManegePara();
            data.setMonth(e.getMonth());
            data.setId(e.getId());
            data.setObjectId(this.objectId);
            data.setObjectTypeId(this.objectTypeId);
            data.setYear(this.year);
            data.setPlanValue(e.getPlanValue());
            data.setYearPlanTotalValue(this.yearPlanTotalValue);
            energyCarbonData.add(data);
        }
        return energyCarbonData;
    }
}
