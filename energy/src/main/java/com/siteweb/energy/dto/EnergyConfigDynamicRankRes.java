package com.siteweb.energy.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;

@Data
public class EnergyConfigDynamicRankRes {
    private String resourceStructureName;
    private Integer resourceStructureId;
    private Double totalValue;
    private Double value1;
    private Double value2;
    private Double value3;
    private Double value4;
    private Double value5;
    private Double value6;
    private Double value7;
    private Double value8;
    private Double value9;
    private Double value10;

    // 构造方法，设置默认值
    public EnergyConfigDynamicRankRes() {
        this.value1 = 0.0;
        this.value2 = 0.0;
        this.value3 = 0.0;
        this.value4 = 0.0;
        this.value5 = 0.0;
        this.value6 = 0.0;
        this.value7 = 0.0;
        this.value8 = 0.0;
        this.value9 = 0.0;
        this.value10 = 0.0;
    }


    public Double getTotalValue(){
        return NumberUtil.doubleAccuracy((this.value1 + this.value2 + this.value3 + this.value4 + this.value5
                + this.value6 + this.value7 + this.value8 + this.value9 + this.value10),2);
    }

}
