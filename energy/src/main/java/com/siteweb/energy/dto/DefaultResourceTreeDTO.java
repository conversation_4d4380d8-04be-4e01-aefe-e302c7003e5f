package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class DefaultResourceTreeDTO {
    /**
     * 当前资源id
     */
    private Integer objectId;
    /**
     * 当前资源类型
     */
    private Integer objectTypeId;
    /**
     * 当前资源名称
     */
    private String resourceName;

    /**
     * 父层级Id
     */
    private Integer parentResourceStructureId;
    /**
     * 父层级类型
     */
    private Integer parentResourceStructureTypeId;

    /**
     * 子集合
     */
    List<DefaultResourceTreeDTO> children;
}
