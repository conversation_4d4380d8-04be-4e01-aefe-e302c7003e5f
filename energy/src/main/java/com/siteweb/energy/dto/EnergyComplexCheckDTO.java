package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnergyComplexCheckDTO {
    private Integer complexIndexId;
    private Integer objectId;
    private Integer objectTypeId;
    private String complexIndexLevelName;
    private String complexIndexName;
    private Integer businessTypeId;
    private String businessTypeName;
    private String errorReason;
    private Integer calcType;//是否差值
    private String calcTypeDesc;//是否差值描述
    private String calcCron;//计算周期
    private String calcCronDesc;//计算周期描述
    private String saveCron;//存储周期
    private String saveCronDesc;//存储周期描述
    private String expression;//指标表达式
    private String expressionDesc;//指标表达式描述
    private String unit; //单位
}


