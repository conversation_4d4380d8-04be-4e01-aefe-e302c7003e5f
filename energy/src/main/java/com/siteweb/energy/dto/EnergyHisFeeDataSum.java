package com.siteweb.energy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name="EnergyHisFeeData")
public class EnergyHisFeeDataSum {
    public static final long serialVersionUID=1L;

    @Column(name="time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String feeTime;

    @Column(name="result",tag=true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String indexFeeValue;


    public String getFeeTime(){
        String tempTime = this.feeTime;
        if(tempTime!=null && !tempTime.equals("")){
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }

}
