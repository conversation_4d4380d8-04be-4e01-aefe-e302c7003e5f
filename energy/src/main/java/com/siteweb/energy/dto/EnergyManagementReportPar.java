package com.siteweb.energy.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class EnergyManagementReportPar {
    private String energyIds;
    private String resourceStructureIds;  //前台过滤筛选的节点
    private String resourceStructure2Ids; //前台过滤筛选的节点2
    private Long userId;                  //登录用户
    private Date startTime;               //查询开始时间
    private Date endTime;                 //查询结束时间
    private Date startTime2;              //查询开始时间
    private Date endTime2;                //查询结束时间
    private Integer days;                 //查询天数
}
