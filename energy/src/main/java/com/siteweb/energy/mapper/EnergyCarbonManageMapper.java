package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.dto.EnergyCarbonManageDTO;
import com.siteweb.energy.entity.EnergyCarbonManegePara;
import com.siteweb.monitoring.entity.ResourceStructure;

import java.util.List;


public interface EnergyCarbonManageMapper extends BaseMapper<EnergyCarbonManegePara> {
    List<EnergyCarbonManegePara> getConfiguredYearByObjectId(String objectId, String objectTypeId);

    List<EnergyCarbonManegePara> getObjectConfigInfo(String objectId, String objectTypeId, Integer year);
    Integer deleteDataById(Integer id,float planValue);

    Integer updateDataById(Integer id, Float planValue, float yearPlanTotalValue);
    ResourceStructure getFatherObjectInfo(String objectId);

    String getAllChildsTotalValue(String fatherObjectId, String objectTypeId,Integer year);

    String getObjectFatherId(String objectId, String objectTypeId);

    EnergyCarbonManegePara getObjectIdById(Integer id);

    List<ResourceStructure> getObjectChildren(String objectId);
}
