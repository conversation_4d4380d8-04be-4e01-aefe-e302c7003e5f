package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyElecFeeFpg;

import java.util.List;

public interface EnergyElecFeeFpgMapper extends BaseMapper<EnergyElecFeeFpg> {
    void deleteBySchemeId(Integer schemeId);

    List<EnergyElecFeeFpg> getBySchemeId(Integer schemeId);

    EnergyElecFeeFpg getByFpgIdAndSchemeId(Integer fpgId, Integer schemeId);

    void delByCondition(Integer fpgId, Integer schemeId);

    EnergyElecFeeFpg findByFpgId(Integer fpgId);

    List<EnergyElecFeeFpg> getByCondition(Integer schemeId, Integer fpgId);
}
