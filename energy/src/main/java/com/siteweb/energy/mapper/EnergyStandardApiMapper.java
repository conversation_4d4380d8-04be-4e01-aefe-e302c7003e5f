package com.siteweb.energy.mapper;


import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.energy.dto.EnergySdkResourceComplexIndexDTO;
import com.siteweb.energy.entity.EnergyCarbonManegePara;

import java.util.List;

public interface EnergyStandardApiMapper {
    String getResourceStructureNameById(Integer objectId);
    List<EnergySdkResourceComplexIndexDTO> getAllObjectInfoAndComplexIndex();

    List<EnergyCarbonManegePara> getObjectCarbonQuotaByIdAndYear(Integer objectId, Integer objectTypeId, String year);

    List<ComplexIndexBusinessType> getAllNoGreenBusiness();

    Double getObjectCarbonEmissionByIdAndYearAndMonth(Integer objectId, Integer objectTypeId, String year, String month);

    Double getObjectCarbonEmissionByIdAndYear(Integer objectId, Integer objectTypeId, String year);
}
