package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.dto.ComplexIndexClassificationListDTO;
import com.siteweb.energy.entity.EnergyComplexIndexClassificationMap;

import java.util.List;

public interface EnergyComplexIndexClassificationMapMapper extends BaseMapper<EnergyComplexIndexClassificationMap> {

    List<ComplexIndexClassificationListDTO> getComplexIndexClassificationInfoById(String objectId, String objectTypeId, Integer businessTypeId);
    List<EnergyComplexIndexClassificationMap> getComplexIndexByResIdAndClassificationId(String resourceStructureIds,String classificationIds);
    void updateTable(String objectId, String objectTypeId,Integer businessTypeId);
}
