package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.dto.EnergyTaiBoObjectData;
import com.siteweb.energy.entity.EnergyObjectMap;
import com.siteweb.energy.model.EnergyResourceStructure;

import java.util.List;

public interface EnergyObjectMapMapper extends BaseMapper<EnergyObjectMap> {
    /** 得到所有的节点(含设备) */
    List<EnergyResourceStructure> findAllNodesByDimensionTypeId(Integer dimensionTypeId, int energyResourceType, int resourceStructureType, int dimensionStructureObjecttypeid,
                                                                int equipmentStructureType, int computerRackStructureType, int itDeviceStructureType);

    List<Integer> getAllPrivateStructureIds(Integer id, int energyStructureType);

    void deleteByDimensionTypeId(Integer dimensionTypeId);

    List<EnergyObjectMap> findByDimensionTypeIdAndObjectIdAndObjectIdType(Integer dimensionTypeId, Integer objectId, Integer objectIdType);

    List<EnergyObjectMap> findAllLevel1ChildNodes(Integer objectId, Integer objectIdType, Integer dimensionTypeId);

    int getCountByOtherTreeUsed(Integer objectId, Integer objectIdType, Integer id);

    List<EnergyResourceStructure> findSameNameNodeUnderSameParentObjectId(Integer dimensionTypeId, Integer parentObjectId, Integer parentObjectIdType, String nodeName,
            int energyResourceType, int resourceStructureType,int equipmentStructureType, int computerRackStructureType, int itDeviceStructureType, int dimensionStructureObjecttypeid);

    /** 得到某棵自定义树的根节点 */
    List<EnergyObjectMap> findRootNodeByDimensionTypeId(Integer dimensionTypeId);

    /** 得到所有的层级节点(不包含设备) */
    List<EnergyResourceStructure> findAllStrutureNodesByDimensionTypeId(Integer dimensionTypeId, int energyResourceType, int resourceStructureType, int dimensionStructureObjecttypeid);

    EnergyResourceStructure findObjectMapWithSourceCategoryById(Integer primaryId,
            int energyResourceType, int resourceStructureType,int equipmentStructureType, int computerRackStructureType, int itDeviceStructureType, int dimensionStructureObjecttypeid);

    List<EnergyObjectMap> getAllPrivateRefStructureIds(Integer id, int equipmentStructureType, int computerRackStructureType, int itDeviceStructureType);
    /** 得到某棵自定义树的根节点以及所有根节点流入节点 */
    List<EnergyResourceStructure> findAllRootInflowNodeByDimensionTypeId(Integer dimensionTypeId, int energyResourceType, int resourceStructureType, int dimensionStructureObjecttypeid,
                                                                int equipmentStructureType, int computerRackStructureType, int itDeviceStructureType);

    List<EnergyResourceStructure> findAllRootFatherInflowNodeByDimensionTypeId(Integer dimensionTypeId, int energyResourceType, int resourceStructureType, int dimensionStructureObjecttypeid,
                                                                               int equipmentStructureType, int computerRackStructureType, int itDeviceStructureType);

    List<EnergyTaiBoObjectData> findChildObjectById(Integer dimensionTypeId, Integer objectId);

    List<EnergyTaiBoObjectData> findChildObjectInResourcestructureById(Integer objectId);
    EnergyTaiBoObjectData findObjectInfoByObjectId(Integer objectId);

    EnergyTaiBoObjectData findObjectInfoInDimensionMap(Integer dimensionTypeId,Integer objectId);

    EnergyTaiBoObjectData findRootNodeInfoByDimensionTypeId(Integer dimensionTypeId);
    List<EnergyObjectMap> selectList(EnergyObjectMap condition);

//    查找某个树的进线返回Energy_ObjectMap
    List<EnergyObjectMap> findRootInflowNodeByDimensionTypeId(Integer dimensionTypeId);
}
