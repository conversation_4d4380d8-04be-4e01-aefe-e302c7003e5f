package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyCustomerTBReportMap;
import com.siteweb.energy.entity.EnergyCustomerTBReportRecord;
import com.siteweb.energy.entity.EnergyCustomerTbReport;
import com.siteweb.energy.entity.EnergyStructure;

import java.util.Date;
import java.util.List;

public interface EnergyTaiBoReportMapper extends BaseMapper<EnergyCustomerTbReport>  {
    List<EnergyCustomerTbReport> getAllReportInfo();
    List<EnergyCustomerTBReportMap> getAllEquipmentInfoByReportId(Integer reportId);

    List<EnergyCustomerTBReportRecord> GetTaiBoReportList(Date startTime, Date endTime);

    EnergyCustomerTBReportRecord getTaiBoReportById(Integer pId);
}
