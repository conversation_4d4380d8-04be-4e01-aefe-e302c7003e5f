package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyElecFeeScheme;

import java.util.List;

public interface EnergyElecFeeSchemeMapper extends BaseMapper<EnergyElecFeeScheme> {
    List<EnergyElecFeeScheme> getAll();

    List<EnergyElecFeeScheme> findByResourceStructureId(Integer resourceStructureId);

    EnergyElecFeeScheme findBySchemeId(Integer schemeId);

    void deleteBySchemeId(Integer schemeId);

    List<EnergyElecFeeScheme> getAllEnabledSchemes();

    /** 根据业务类型查询某层级下的所有方案 */
    List<EnergyElecFeeScheme> findByResourceStructureIdAndTypeId(Integer resourceStructureId, Integer businessTypeId);
}
