package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyElecFeeFpgValue;

import java.util.List;

public interface EnergyElecFeeFpgValueMapper extends BaseMapper<EnergyElecFeeFpgValue> {

    void deleteBySchemeId(Integer schemeId);

    List<EnergyElecFeeFpgValue> findListBy(Integer fpgId, Integer schemeId);

    List<EnergyElecFeeFpgValue> getByStepPriceId(Integer stepPriceId, Integer schemeId);

    void delByStepPriceId(Integer stepPriceId, Integer schemeId);

    List<EnergyElecFeeFpgValue> getByFpgId(Integer fpgId, Integer schemeId);

    void delByFpgId(Integer fpgId, Integer schemeId);
}
