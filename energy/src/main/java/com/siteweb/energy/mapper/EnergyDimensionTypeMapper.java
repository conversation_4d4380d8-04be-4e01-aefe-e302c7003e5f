package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyDimensionType;

import java.util.List;

public interface EnergyDimensionTypeMapper extends BaseMapper<EnergyDimensionType> {

    EnergyDimensionType findByDimensionTypeId(Integer dimensionTypeId);

    List<EnergyDimensionType> findByIsUsedIs(int usedState);
}
