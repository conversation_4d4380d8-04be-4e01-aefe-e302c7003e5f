package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.energy.entity.EnergyDataItem;

import java.util.List;

public interface EnergyDimensionComplexIndexMapper extends BaseMapper<ComplexIndex> {
    List<ComplexIndex> findByResourceStructureIdAndType(Integer resourceStructureId, Integer structureTypeId);

    List<ComplexIndex> findByBusinessTypeIdAndComplexIndexDefinitionIdAndObjectTypeIdAndObjectId(Integer businessTypeId, Integer complexIndexDefinitionId, Integer objectTypeId, Integer objectId);

    List<ComplexIndex> findAllComplexindexByObjectTypeId(Integer objectTypeId, int dimensionStructureObjecttypeid);
}
