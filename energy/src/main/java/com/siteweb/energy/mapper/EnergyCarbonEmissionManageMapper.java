package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyCarbonEmissionPara;

import java.util.List;

public interface EnergyCarbonEmissionManageMapper extends BaseMapper<EnergyCarbonEmissionPara> {
    List<String> getUnit();

    int addAreaCarbonEmission(String objectId, String objectType, Float area,Integer isArea,String units);

    int deleteNoAreaCarbonEmissionById(Integer id);

    List<EnergyCarbonEmissionPara> getConfiguration(String objectId, String objectTypeId);

    List<EnergyCarbonEmissionPara> getHistoryData(String objectId, String objectTypeId, Integer year);

    EnergyCarbonEmissionPara getAreaIsConfiguration(String objectId, String objectType);

    int updateAreaInfo(String objectId, String objectType, Float area, String units);

    List<EnergyCarbonEmissionPara> getAreaConfigurationInfo(String objectId, String objectTypeId, Integer isArea);

    List<EnergyCarbonEmissionPara> getNoAreaConfigurationInfo(String objectId, String objectTypeId, Integer year);

    int updateDataById(Integer id, Float planValue, Float yearPlanTotalValue,String units);
}
