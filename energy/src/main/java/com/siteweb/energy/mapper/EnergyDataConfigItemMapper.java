package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyObjectMap;

import java.io.Serializable;
import java.util.List;

public interface EnergyDataConfigItemMapper extends BaseMapper<EnergyDataItem> {

    List<EnergyDataItem> getAllByEntryId(Integer entryId);

    EnergyDataItem getDataItemById(Integer entryId, Integer itemId);
}
