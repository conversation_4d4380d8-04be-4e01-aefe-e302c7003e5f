package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyElecFeeStepPrice;

import java.util.List;

public interface EnergyElecFeeStepPriceMapper extends BaseMapper<EnergyElecFeeStepPrice> {
    void deleteBySchemeId(Integer schemeId);

    List<EnergyElecFeeStepPrice> getBySchemeId(Integer schemeId);

    int countBy(Integer schemeId);

    EnergyElecFeeStepPrice getByCondition(Integer stepPriceId, Integer schemeId);

    void delByCondition(Integer stepPriceId, Integer schemeId);

    EnergyElecFeeStepPrice findByStepPriceId(Integer stepPriceId);
}
