package com.siteweb.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.energy.entity.EnergyRatingDataHistory;

import java.util.Date;

public interface EnergyRatingDataHistoryMapper extends BaseMapper<EnergyRatingDataHistory> {

    /*获取最新的一条数据*/
    Integer getLatestPieceOfTimes();

    void insertToHistory(Integer times, Date deleteDate,Integer ratingConfigId);

    void clearRatingData(Integer times, Date deleteDate,Integer ratingConfigId);

}
