package com.siteweb.energy.scheduler;


import com.siteweb.energy.service.EnergyConsumeAlarmService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@EnableScheduling
@Component
@Slf4j
public class EnergyPreAlarmSchedule {
    //@Autowired
    private Scheduler scheduler;

    @Autowired
    private EnergyConsumeAlarmService energyConsumeAlarmService;
    @Autowired
    private HAStatusService haStatusService;

    @PostConstruct
    private void init() {
    }

    //@Scheduled(fixedDelay = 10 * 60 * 1000)   //每10分钟执行
    @Scheduled(cron = "0 10 * * * ?") //every 10 minute of hour
    protected void schedule() {
        try {
            if(!haStatusService.isMasterHost()) {
                log.info("EnergyPreAlarmSchedule : HAStatus is BACKUP.");
                return;
            }
            energyConsumeAlarmService.GenerateConsumeAlarm();
        } catch (Exception ex) {
            log.error("EnergyPreAlarmSchedule->schedule", ex);
        }
    }
}
