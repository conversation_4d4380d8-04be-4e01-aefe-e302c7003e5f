package com.siteweb.energy.scheduler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.common.util.GetBeanUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.energy.dto.EnergyRatingConfigExDTO;
import com.siteweb.energy.dto.EnergyRatingDataJobDTO;
import com.siteweb.energy.entity.EnergyRatingData;
import com.siteweb.energy.service.EnergyRatingDataService;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.utility.service.HAStatusService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class EnergyRatingJob implements Job {

    @Autowired
    LiveComplexIndexManager liveComplexIndexManager;

    @Autowired
    EnergyRatingDataService energyRatingDataService;
    private HAStatusService haStatusService = GetBeanUtil.getBean(HAStatusService.class);

    @Autowired
    private RealTimeSignalManager autoRealTimeSignalManager;

    private static RealTimeSignalManager realRealTimeSignalManager;

    private static EnergyRatingDataService staticEnergyRatingDataService;

    private static LiveComplexIndexManager staticLiveComplexIndexManager;

    @PostConstruct
    public void init() {
        this.staticEnergyRatingDataService = this.energyRatingDataService;
        this.realRealTimeSignalManager = this.autoRealTimeSignalManager;
        this.staticLiveComplexIndexManager = this.liveComplexIndexManager;
    }

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        if (ObjectUtil.isNull(this.staticLiveComplexIndexManager)) {
            return;
        }

        if(!haStatusService.isMasterHost()) {
            log.info("EnergyRatingJob : HAStatus is BACKUP.");
            return;
        }
        JobDataMap dataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        EnergyRatingDataJobDTO energyRatingDataJobDTO = new EnergyRatingDataJobDTO(dataMap);
        runDataCollection(energyRatingDataJobDTO);

    }

    @SneakyThrows
    private void runDataCollection(EnergyRatingDataJobDTO params) {
        Double inDryTemp = getExpressionResult(params.getInDryTempParam());
        Double outDryTemp = getExpressionResult(params.getOutDryTempParam());
        String workCond = getWorkingConditionByOutDeyTemp(outDryTemp);

        EnergyRatingData ratingData = new EnergyRatingData();
        ratingData.setRatingConfigId(params.getRatingConfigId());
        if (isInDryTemp(inDryTemp) && (params.getWorkingCondition().contains(workCond) || params.getWorkingCondition().contains("all"))){
            ratingData.setWorkingCondition(workCond);
        }else{
            ratingData.setWorkingCondition(null);
        }
        ratingData.setSampleTime(new Date());
        ratingData.setInDryTemp(inDryTemp);
        ratingData.setOutDryTemp(outDryTemp);
        ratingData.setOutWetTemp(getExpressionResult(params.getOutWetTempParam()));
        ratingData.setInWetTemp(getExpressionResult(params.getInWetTempParam()));
        ratingData.setRunningLoad(getExpressionResult(params.getRunningLoadParam()));
        ratingData.setItPower(getExpressionResult(params.getITPowerParam()));
        ratingData.setTotalPower(getExpressionResult(params.getTotalPowerParam()));
        ratingData.setIntervalSecond(params.getIntervalSecond());
        staticEnergyRatingDataService.saveEnergyRatingData(ratingData);
    }

    public EnergyRatingData getRatingDataRealTimeByConfig(EnergyRatingConfigExDTO params) {
        EnergyRatingData res = new EnergyRatingData();
        res.setOutWetTemp(getExpressionResult(params.getOutWetTempParam()));
        res.setOutDryTemp(getExpressionResult(params.getOutDryTempParam()));
        res.setInDryTemp(getExpressionResult(params.getInDryTempParam()));
        res.setInWetTemp(getExpressionResult(params.getInWetTempParam()));
        res.setRunningLoad(getExpressionResult(params.getRunningLoadParam()));
        res.setItPower(getExpressionResult(params.getItPowerParam()));
        res.setTotalPower(getExpressionResult(params.getTotalPowerParam()));
        return res;
    }


    /*获取根据内干球温度判断是否满足工况*/
    private static Boolean isInDryTemp(Double inDryTemp) {
        if (inDryTemp >= 18 && inDryTemp <= 27) {
            return true;
        }
        return false;
    }

    /*获取根据外干球温度获取工况*/
    private static String getWorkingConditionByOutDeyTemp(Double outDryTemp) {
        if (outDryTemp >= 30) {
            return "a";
        }
        if (outDryTemp < 30 && outDryTemp >= 20) {
            return "b";
        }
        if (outDryTemp < 20 && outDryTemp >= 10) {
            return "c";
        }
        if (outDryTemp < 10 && outDryTemp >= 0) {
            return "d";
        }
        if (outDryTemp < 0) {
            return "e";
        }
        return "null";
    }

    @SneakyThrows
    public Double getExpressionResult(String expression) {
        if (StrUtil.isEmpty(expression)) {
            return null;
        }
        expression = parseExpression(expression);
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext();
        standardEvaluationContext.registerFunction("cp", EnergyRatingJob.class.getDeclaredMethod("cp", new Class[]{String.class}));
        standardEvaluationContext.registerFunction("ci", EnergyRatingJob.class.getDeclaredMethod("ci", new Class[]{Integer.class}));
        Object res = parser.parseExpression(expression).getValue(standardEvaluationContext);
        return (Double) res;
    }


    /*格式化表达式*/
    public static String parseExpression(String cronExpression) {
        if (StringUtils.isEmpty(cronExpression)) {
            return cronExpression;
        }
        cronExpression = cronExpression.toLowerCase();
        cronExpression = cronExpression.replace(" ", "");
        cronExpression = cronExpression.replace("cp", "#cp");
        cronExpression = cronExpression.replace("ci", "#ci");

        if (cronExpression.contains("cp")) {
            Pattern p = Pattern.compile("(?<=cp\\()[^\\)]+");
            Matcher m = p.matcher(cronExpression);
            StringBuffer sb = new StringBuffer();
            while (m.find()) {
                m.appendReplacement(sb, "\"" + m.group() + "\"");
            }
            m.appendTail(sb);
            cronExpression = sb.toString();
        }

        return cronExpression;
    }

    public static Double cp(String key) {
//        Random rand=new Random();
//        int n3=rand.nextInt(10)+18;//[18,27]内的随机整数
//        return Double.valueOf(n3);

        if (StringUtils.isEmpty(key)) {
            return 0d;
        }
        String[] keys = key.split("\\.");
        RealTimeSignalItem realTimeSignalItem = realRealTimeSignalManager.getRealTimeSignalBySignalId(Integer.valueOf(keys[0]), Integer.valueOf(keys[1]));
        if (realTimeSignalItem == null) {
            return 0d;
        }
        String result = StrUtil.isEmpty(realTimeSignalItem.getCurrentValue()) ? "0" : realTimeSignalItem.getCurrentValue();
        return NumberUtil.doubleAccuracy(Double.valueOf(result),2);

    }

    public static Double ci(Integer complexIndexId) {
//        Random rand=new Random();
//        int n3=rand.nextInt(10)+18;//[18,27]内的随机整数
//        return Double.valueOf(n3);
        LiveComplexIndex liveComplexIndex = staticLiveComplexIndexManager.findLiveComplexIndexById(complexIndexId);
        if (ObjectUtil.isNull(liveComplexIndex)) {
            return 0d;
        }
        return NumberUtil.doubleAccuracy(Double.valueOf(liveComplexIndex.getCurrentValue()),2);
    }

}
