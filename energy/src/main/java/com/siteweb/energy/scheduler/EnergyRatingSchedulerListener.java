package com.siteweb.energy.scheduler;

import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.energy.service.EnergyRatingConfigService;
import com.siteweb.energy.service.EnergyRatingDataService;
import org.quartz.impl.matchers.GroupMatcher;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
@Slf4j
public class EnergyRatingSchedulerListener implements SchedulerListener, ApplicationListener<BaseSpringEvent<HAStatusChanged>> {

    @Autowired
    EnergyRatingConfigService energyRatingConfigService;
    @Autowired
    private HAStatusService haStatusService;
    @Autowired
    private EnergyRatingDataService energyRatingDataService;

    private static EnergyRatingConfigService staticEnergyRatingConfigService;
    private static EnergyRatingDataService staticEnergyRatingDataService;
    
    // 与EnergyRatingSchedule中定义的常量保持一致
    private static final String ENERGY_RATING_GROUP = "EnergyRatingGroup";

    @PostConstruct
    public void init(){
        this.staticEnergyRatingConfigService = this.energyRatingConfigService;
        this.staticEnergyRatingDataService = this.energyRatingDataService;
    }

    private Scheduler scheduler;

    public EnergyRatingSchedulerListener(Scheduler scheduler){
        this.scheduler = scheduler;
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<HAStatusChanged> event) {
        //如果未启用双机高可用部署，则直接退出
        if (!haStatusService.isEnabled()) {
            return;
        }
        
        HAStatusChanged haStatusChanged = event.getData();
        log.info("EnergyRatingSchedulerListener receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", 
                haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        
        try {
            // 清理所有任务，然后重新初始化
            clearAllJobs();
            
            // 重新初始化任务
            if (haStatusService.isMasterHost()) {
                // 只有在主机状态下才重新加载任务
                staticEnergyRatingDataService.checkRatingConfigIsRun();
            }
        } catch (Exception e) {
            log.error("Failed to handle HA status change for energy rating jobs", e);
        }
    }
    
    /**
     * 只清理能耗评定相关的定时任务
     */
    private void clearAllJobs() {
        try {
            // 直接获取能源评级组的所有作业
            for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(ENERGY_RATING_GROUP))) {
                // 找到关联的触发器
                List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
                for (Trigger trigger : triggers) {
                    scheduler.pauseTrigger(trigger.getKey());
                    scheduler.unscheduleJob(trigger.getKey());
                    log.info("Paused and unscheduled energy rating trigger: {}", trigger.getKey().getName());
                }
                
                // 删除作业
                scheduler.deleteJob(jobKey);
                log.info("Deleted energy rating job: {}", jobKey.getName());
            }
        } catch (SchedulerException e) {
            log.error("Failed to clear energy rating jobs", e);
        }
    }

    @Override
    public void jobScheduled(Trigger trigger) {

    }

    @Override
    public void jobUnscheduled(TriggerKey triggerKey) {

    }

    @Override
    public void triggerFinalized(Trigger trigger) {
        try {
            // 确保只处理能源评级组的触发器
            if (ENERGY_RATING_GROUP.equals(trigger.getKey().getGroup())) {
                scheduler.deleteJob(trigger.getJobKey());
                scheduler.unscheduleJob(trigger.getKey());
                staticEnergyRatingConfigService.closeJob(Integer.valueOf(trigger.getKey().getName()));
            }
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void triggerPaused(TriggerKey triggerKey) {

    }

    @Override
    public void triggersPaused(String s) {

    }

    @Override
    public void triggerResumed(TriggerKey triggerKey) {

    }

    @Override
    public void triggersResumed(String s) {

    }

    @Override
    public void jobAdded(JobDetail jobDetail) {

    }

    @Override
    public void jobDeleted(JobKey jobKey) {

    }

    @Override
    public void jobPaused(JobKey jobKey) {

    }

    @Override
    public void jobsPaused(String s) {

    }

    @Override
    public void jobResumed(JobKey jobKey) {

    }

    @Override
    public void jobsResumed(String s) {

    }

    @Override
    public void schedulerError(String s, SchedulerException e) {

    }

    @Override
    public void schedulerInStandbyMode() {

    }

    @Override
    public void schedulerStarted() {

    }

    @Override
    public void schedulerStarting() {

    }

    @Override
    public void schedulerShutdown() {

    }

    @Override
    public void schedulerShuttingdown() {

    }

    @Override
    public void schedulingDataCleared() {

    }
}
