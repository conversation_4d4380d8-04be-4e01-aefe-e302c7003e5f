package com.siteweb.energy.scheduler;

import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyDataAuditService;
import com.siteweb.energy.service.EnergyHistoryDataService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@EnableScheduling
@Component
@Slf4j
public class EnergyComplexCheckSchedule {
    //@Autowired
    private Scheduler scheduler;
    @Autowired
    private EnergyHistoryDataService energyHistoryDataService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private EnergyDataAuditService energyDataAuditService;

    @PostConstruct
    private void init() {
    }

    @Scheduled(cron = "0 0 2 * * ?") //every 2 hour of day
    protected void schedule() {
        try {
            if(!haStatusService.isMasterHost()) {
                log.info("AlarmChangeListener : HAStatus is BACKUP.");
                return;
            }
            log.info("---------指标稽查预警检测---------");
            energyDataAuditService.EnergyComplexCheck();
        } catch (Exception ex) {
            log.error("EnergyComplexCheckSchedule->schedule", ex);
        }
    }
}

