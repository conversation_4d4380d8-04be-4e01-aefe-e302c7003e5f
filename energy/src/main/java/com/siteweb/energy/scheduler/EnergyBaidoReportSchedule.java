package com.siteweb.energy.scheduler;

import com.siteweb.energy.manager.CronExpressionProvider;
import com.siteweb.energy.service.EnergyBaidoReportService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@EnableScheduling
@Component
@Slf4j
public class EnergyBaidoReportSchedule {

    private final EnergyBaidoReportService energyBaidoReportService;
    private final HAStatusService haStatusService;
    private final EnergyOverViewService energyOverViewService;
    private final CronExpressionProvider cronExpressionProvider;

    @Autowired
    public EnergyBaidoReportSchedule(EnergyBaidoReportService energyBaidoReportService,
                                     HAStatusService haStatusService,
                                     EnergyOverViewService energyOverViewService,
                                     CronExpressionProvider cronExpressionProvider) {
        this.energyBaidoReportService = energyBaidoReportService;
        this.haStatusService = haStatusService;
        this.energyOverViewService = energyOverViewService;
        this.cronExpressionProvider = cronExpressionProvider;
    }

    @Scheduled(cron = "#{@cronExpressionProvider.getBaidoCronExpression()}")  //百度修改为动态定时任务
    protected void schedule() {
        try {
            if (!energyOverViewService.getCustomerConfigResult(5)) {
                log.info("--------- yangquan baidu month report Skip execution ---------");
                return;
            }
            log.info("---------阳泉百度月报表定时任务开始执行---------");
            if (!haStatusService.isMasterHost()) {
                log.info("EnergyBaidoReportSchedule : HAStatus is BACKUP.");
                return;
            }
            energyBaidoReportService.doSchedule();
        } catch (Exception ex) {
            log.error("EnergyBaidoReportSchedule schedule() throw Exception: ", ex);
        }
    }
}
