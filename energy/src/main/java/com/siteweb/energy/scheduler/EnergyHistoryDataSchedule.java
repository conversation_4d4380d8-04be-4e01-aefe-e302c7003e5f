package com.siteweb.energy.scheduler;

import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyHistoryDataHBDXService;
import com.siteweb.energy.service.EnergyHistoryDataService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@EnableScheduling
@Component
@Slf4j
public class EnergyHistoryDataSchedule {
    //@Autowired
    private Scheduler scheduler;
    @Autowired
    private EnergyHistoryDataService energyHistoryDataService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private HAStatusService haStatusService;

    @PostConstruct
    private void init() {
        //用于调试立即执行
        //energyComplexIndexManager.Init();
        //energyHistoryDataService.startStatisticEnergyHistoryData();
    }

    //@Scheduled(fixedDelay = 10 * 60 * 1000)   //每10分钟执行
    @Scheduled(cron = "0 17 * * * ?") //every 17 minute of hour
    protected void schedule() {
        try {
            //判断是否移动设计院现场
            if (energyOverViewService.getCustomerConfigResult(3)) {
                return;
            }

            if (!haStatusService.isMasterHost()) {
                log.info("EnergyHistoryDataSchedule : HAStatus is BACKUP.");
                return;
            }
            //刷新指标缓存
            energyComplexIndexManager.Init();
            log.info("---------能耗指标数据转移任务启动---------");
            energyHistoryDataService.startStatisticEnergyHistoryData();
        } catch (Exception ex) {
            log.error("能耗指标数据转移job错误 EnergyHistoryDataService.startStatisticEnergyHistoryData()" + ex.getMessage());
        }
    }
}

