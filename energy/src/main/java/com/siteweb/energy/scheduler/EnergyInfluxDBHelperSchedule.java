package com.siteweb.energy.scheduler;

import cn.hutool.core.date.DateTime;
import com.siteweb.common.util.DateUtil;
import com.siteweb.energy.controller.EnergyConfigTelComApiController;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyConfigTelComService;
import com.siteweb.energy.service.EnergyHistoryDataService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;

@EnableScheduling
@Component
@Slf4j
public class EnergyInfluxDBHelperSchedule {
    //@Autowired
    private Scheduler scheduler;
    @Autowired
    private EnergyHistoryDataService energyHistoryDataService;
    @Autowired
    private EnergyConfigTelComService energyConfigTelComService;
    @Autowired
    private HAStatusService haStatusService;
    @PostConstruct
    private void init() {

    }

    //@Scheduled(fixedDelay = 10 * 60 * 1000)   //每10分钟执行
    @Scheduled(cron = "0 2,12,22,32,42,52 * * * ?") //every 10 minute
    protected void schedule() {
        try {
            if(!haStatusService.isMasterHost()) {
                log.info("EnergyInfluxDBHelperSchedule : HAStatus is BACKUP.");
                return;
            }
            //energyHistoryDataService.energyInfluxDBHistoryData();

            //Date startTime = setStartTimeByTimeType("y");
            //Date endTime = setEndTimeByTimeType(startTime,"y");

            //energyConfigTelComService.getTotalElectricity("y", startTime, endTime, -1,"electTotal");
            //energyConfigTelComService.getNextLevelRank("y", startTime, endTime, -1,"carbon");
            //energyConfigTelComService.findMaxMInAvgUETrend(startTime, endTime,"y",-1);
            //energyConfigTelComService.getEnergyAndCarbonTrend("y", startTime, endTime, 1,"energyCategory");
            //energyConfigTelComService.getTotalElectricity("y", startTime, endTime, -1,"carbonCategory");
        } catch (Exception ex) {
            log.error("能耗指标缓存job错误 EnergyInfluxDBHelperSchedule.energyInfluxDBHistoryData()"+ex.getMessage());
        }
    }

    public Date setStartTimeByTimeType(String timeType){
        Date startTime = new Date();
        switch (timeType){
            case "d" :
                startTime = DateUtil.getTodayStartTime().getTime();
                break;
            case "m":
                startTime = DateUtil.getFirstDayOfMonth(DateTime.now());
                break;
            case "y":
                startTime = DateUtil.dateAddMonth(DateUtil.getNextYearFirstDay(DateTime.now()),-12);
                break;
        }
        //startTime.setYear(122); //1900+122==2022
        return startTime;
    }
    public Date setEndTimeByTimeType(Date startTime,String timeType){
        Date endTime = new Date();
        switch (timeType){
            case "d" :
                endTime = DateUtil.getLastSecondsOfToday(startTime);
                break;
            case "m":
                endTime = DateUtil.getLastDayOfMonth(DateTime.now());
                break;
            case "y":
                endTime = DateUtil.dateAddSeconds(DateUtil.getNextYearFirstDay(DateTime.now()),-1);
                break;
        }

        return endTime;
    }
}
