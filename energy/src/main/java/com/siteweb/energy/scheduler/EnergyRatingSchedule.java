package com.siteweb.energy.scheduler;


import cn.hutool.core.bean.BeanUtil;
import com.siteweb.energy.dto.EnergyRatingConfigExDTO;
import com.siteweb.energy.dto.EnergyRatingDataJobDTO;
import com.siteweb.energy.dto.EnergyRatingDataJobParams;
import com.siteweb.energy.service.EnergyRatingConfigService;
import com.siteweb.utility.service.HAStatusService;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Calendar;

@Component
@EnableScheduling
public class EnergyRatingSchedule {

    private final Logger log = LoggerFactory.getLogger(EnergyRatingSchedule.class);
    private Scheduler scheduler;
    
    // 添加常量定义
    private static final String ENERGY_RATING_GROUP = "EnergyRatingGroup";

    @Autowired
    private EnergyRatingConfigService energyRatingConfigService;
    @Autowired
    private HAStatusService haStatusService;


    @PostConstruct
    void init() throws SchedulerException {
        SchedulerFactory schedulerFactory = new StdSchedulerFactory();
        scheduler = schedulerFactory.getScheduler();
        scheduler.getListenerManager().addSchedulerListener(new EnergyRatingSchedulerListener(scheduler));
    }

    /*创建job*/
    private JobDetail createJobDetail(String configId, JobDataMap energyRatingConfig) {
        JobDetail jobDetail = JobBuilder
                .newJob(EnergyRatingJob.class)
                .withIdentity(JobKey.jobKey(configId, ENERGY_RATING_GROUP))
                .usingJobData(energyRatingConfig)
                .build();
        return jobDetail;
    }

    private Trigger createTrigger(String triggerKey,Date endTime, Integer interval) {
        Trigger trigger = TriggerBuilder.newTrigger()
                .startNow()
                .endAt(endTime)
                .withIdentity(TriggerKey.triggerKey(triggerKey, ENERGY_RATING_GROUP))
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInSeconds(interval)//每隔seconds执行一次
                        .repeatForever()
                ).build();
        return trigger;
    }

    public Integer createJob(EnergyRatingDataJobParams params) throws SchedulerException {
        if(!haStatusService.isMasterHost()) {
            log.info("EnergyRatingSchedule : HAStatus is BACKUP.");
            return 0 ;
        }
        String jobKey = String.valueOf(params.getRatingConfigId());
        if (scheduler.checkExists(new JobKey(jobKey, ENERGY_RATING_GROUP))) {
            log.error("EnergyRatingSchedule-createJob error {}","job is running");
            return -1;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.HOUR_OF_DAY, params.getRunTime());

        EnergyRatingConfigExDTO ratingConfig = energyRatingConfigService.getEnergyRatingExByConfigId(params.getRatingConfigId());

        EnergyRatingDataJobDTO energyRatingDataJobDTO = new EnergyRatingDataJobDTO(ratingConfig, params.getWorkingCondition());
        JobDataMap jobDataMap = new JobDataMap(BeanUtil.beanToMap(energyRatingDataJobDTO));
        JobDetail jobDetail = createJobDetail(jobKey, jobDataMap);
        Trigger trigger = createTrigger(jobKey,cal.getTime(), ratingConfig.getIntervalSecond());
        scheduler.scheduleJob(jobDetail, trigger);
        scheduler.start();
        /*任务开始 更新数据库*/
        energyRatingConfigService.startJob(ratingConfig.getRatingConfigId(), new Date(), cal.getTime(), params.getWorkingCondition());
        return 0;
    }


    /*重启一个job*/
    public Integer restartJob(Integer ratingConfigId,String workingCondition,Date endTime) throws SchedulerException {
        String jobKey = String.valueOf(ratingConfigId);
        if (scheduler.checkExists(new JobKey(jobKey, ENERGY_RATING_GROUP))) {
            log.error("EnergyRatingSchedule-createJob error {}","job is running");
            return -1;
        }


        EnergyRatingConfigExDTO ratingConfig = energyRatingConfigService.getEnergyRatingExByConfigId(ratingConfigId);

        EnergyRatingDataJobDTO energyRatingDataJobDTO = new EnergyRatingDataJobDTO(ratingConfig, workingCondition);
        JobDataMap jobDataMap = new JobDataMap(BeanUtil.beanToMap(energyRatingDataJobDTO));
        JobDetail jobDetail = createJobDetail(jobKey, jobDataMap);
        Trigger trigger = createTrigger(jobKey,endTime, ratingConfig.getIntervalSecond());
        scheduler.scheduleJob(jobDetail, trigger);
        scheduler.start();
        return 0;
    }

    public void deleteJob(Integer id) throws SchedulerException {
        String jobKey = String.valueOf(id);
        energyRatingConfigService.closeJob(id);
        if (!scheduler.checkExists(new JobKey(jobKey, ENERGY_RATING_GROUP))){
            log.error("EnergyRatingSchedule-deleteJob error {}","job is not found");
            return;
        };
        scheduler.pauseJob(new JobKey(jobKey, ENERGY_RATING_GROUP));
        scheduler.deleteJob(new JobKey(jobKey, ENERGY_RATING_GROUP));
        scheduler.unscheduleJob(new TriggerKey(jobKey, ENERGY_RATING_GROUP));
    }
}







