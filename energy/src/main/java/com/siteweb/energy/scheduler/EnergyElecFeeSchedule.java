package com.siteweb.energy.scheduler;

import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyElecFeeService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@EnableScheduling
@Component
@Slf4j
public class EnergyElecFeeSchedule {
    //@Autowired
    private Scheduler scheduler;

    @Autowired
    private EnergyElecFeeService energyElecFeeService;
    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private HAStatusService haStatusService;

    @PostConstruct
    private void init() {
        //初始化指标缓存
        energyComplexIndexManager.Init();
    }

    //@Scheduled(fixedDelay = 10 * 60 * 1000)   //每10分钟执行
    @Scheduled(cron = "0 30 * * * ?") //every 30 minute of hour  注意计算电费要在能耗小时转存之后进行
    protected void schedule() {
        try {
            //判断是否移动设计院现场
            if(energyOverViewService.getCustomerConfigResult(3)){
                return;
            }
            if(!haStatusService.isMasterHost()) {
                log.info("EnergyElecFeeSchedule : HAStatus is BACKUP.");
                return;
            }
            //刷新指标缓存
            energyComplexIndexManager.Init();
            log.info("---------能耗费用定时计算任务启动---------");
            energyElecFeeService.scheduleCalcEnergyFee();
        } catch (Exception ex) {
            log.error("EnergyElecFeeSchedule", ex);
        }
    }
}
