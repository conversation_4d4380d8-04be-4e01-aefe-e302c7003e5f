package com.siteweb.energy.scheduler;

import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.energy.service.EnergyTaiBoReportService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@EnableScheduling
@Component
@Slf4j
public class EnergyTaiBoHistoryDataSchedule {
    private Scheduler scheduler;
    @Autowired
    private EnergyTaiBoReportService energyTaiBoReportService;
    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private EnergyOverViewService energyOverViewService;
    @PostConstruct
    private void init() {
        //测试
//        energyTaiBoReportService.doSchedule();
    }


    @Scheduled(cron = "0 10 0 * * ?") //每天0点十分
    protected void schedule() {
        try {

            if(energyOverViewService.getCustomerConfigResult(1)){
                log.info("---------台玻日报表定时任务---------");
                energyTaiBoReportService.doSchedule();
            }

        } catch (Exception ex) {
            log.error("schedule() throw Exception: ", ex);
        }
    }

    @Scheduled(cron = "0 10 8 * * ?") //每天八点十分执行一次
    protected void scheduleExec() {
        try {
            if(!energyOverViewService.getCustomerConfigResult(1)) {
                log.info("--------- taibo month report Skip execution ---------");
                return;
            }
            log.info("---------台玻月报表定时任务开始执行---------");
            if(!haStatusService.isMasterHost()) {
                log.info("EnergyTaiBoHistoryDataSchedule : HAStatus is BACKUP.");
                return ;
            }
            boolean isSuccess = energyTaiBoReportService.createMonthlyNaturalGasReport();
            log.info("---------scheduleExec result=" + isSuccess + "---------");
        } catch (Exception ex) {
            log.error("scheduleExec() throw Exception: ", ex);
        }
    }
}
