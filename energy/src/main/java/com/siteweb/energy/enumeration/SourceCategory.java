package com.siteweb.energy.enumeration;

import com.siteweb.monitoring.enumeration.SourceType;

/** 多维度中使用的资源类别 */
public enum SourceCategory {

    /**
     * 说明：RESOURCE_STRUCTURE、EQUIPMENT、COMPUTERRACK、ITDEVICE 类型在 energy_objectmap 表的 ObjectTypeId 字段时，分别表示对应平台的层级表、设备表、机架表、IT设备表。
     *      但在energy_structure表中的SourceCategory字段时，分别表示此节点是能耗自定义节点的层级节点、设备节点、机架节点、IT设备节点。
     */

    /**
     * 层级类
     */
    RESOURCE_STRUCTURE(1),

    /**
     * 设备类
     */
    EQUIPMENT(3),

    /**
     * 机架类
     */
    COMPUTERRACK(4),

    /**
     * IT设备类
     */
    ITDEVICE(5),

    /**
     * 能耗自建资源(存储于Energy_Structure表)
     */
    ENERGY_RESOURCE(2);

    private Integer value = 0;

    SourceCategory(Integer value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

    /** 从int到enum的转换函数 */
    public static SourceCategory valueOf(Integer value) {
        return switch (value) {
            case 1 -> RESOURCE_STRUCTURE;
            case 2 -> ENERGY_RESOURCE;
            case 3 -> EQUIPMENT;
            case 4 -> COMPUTERRACK;
            case 5 -> ITDEVICE;
            default -> null;
        };
    }

    /** 转换EQUIPMENT、COMPUTERRACK、ITDEVICE 至对应的SourceType类型，其他返回null */
    public SourceType toSourceType() {
        return switch (this) {
            case EQUIPMENT -> SourceType.EQUIPMENT;
            case COMPUTERRACK -> SourceType.COMPUTERRACK;
            case ITDEVICE -> SourceType.ITDEVICE;
            default -> null;
        };
    }
}
