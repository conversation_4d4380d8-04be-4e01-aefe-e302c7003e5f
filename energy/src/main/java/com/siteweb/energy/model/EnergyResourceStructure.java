package com.siteweb.energy.model;

import com.siteweb.energy.entity.EnergyObjectMap;
import com.siteweb.energy.enumeration.SourceCategory;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/** 自定义维度树节点对象 */
@Data
@NoArgsConstructor
public class EnergyResourceStructure extends EnergyObjectMap {

    private Long resourceStructureId;

    private String resourceStructureName;

    //private Long globalResourceId;

    private String structureNotes;

    private Integer objectTypeId;

    /** 能耗自定义节点的类别划分 */
    private Integer energyStructureSourceCategory;
    /**
     * 是否是根节点的流入节点，1-是，非1或null-不是。
     * 为1时表示此节点是根节点的流入节点，此节点属于特殊节点，其父节点固定为节点
     */
    private Integer asRootInflowNode;

    private List<EnergyResourceStructure> children;

    public List<EnergyResourceStructure> getChildren() {
        if(children == null){
            children = new ArrayList<>();
        }
        return children;
    }

    /** 为前端树节点展示而添加的冗余字段，完全等于resourceStructureName的值 */
    public String getValue() {
        return resourceStructureName;
    }

    /**
     * @return getObjectId().toString() + "|" + getObjectIdType().toString()
     */
    public String getStructureKey() {
        return getObjectId().toString() + "|" + getObjectIdType().toString();
    }

    /**
     * @return getParentObjectId().toString() + "|" + getParentObjectIdType().toString()
     */
    public String getStructureParentKey() {
        return getParentObjectId().toString() + "|" + getParentObjectIdType().toString();
    }

    public static String getStructureKeyByObjectIdAndObjectIdType(Integer objectId, Integer objectIdType) {
        return objectId + "|" + objectIdType;
    }

    /** 作为树节点的唯一标识 */
    public String getReourceResId() {
        return getObjectId() + "_" + objectTypeId;
    }

    /** 得到该节点的类别 */
    public SourceCategory getSourceCategory() {
        if(getObjectIdType() == null) {
            return null;
        } else {
            if(getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value())) {
                return energyStructureSourceCategory == null ? null : SourceCategory.valueOf(energyStructureSourceCategory);
            } else {
                return SourceCategory.valueOf(getObjectIdType());
            }

        }
    }
}
