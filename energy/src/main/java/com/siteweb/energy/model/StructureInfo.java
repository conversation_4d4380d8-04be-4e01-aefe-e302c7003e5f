package com.siteweb.energy.model;

import com.siteweb.energy.dto.DefaultResourceTreeDTO;
import com.siteweb.energy.entity.EnergyStructure;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import lombok.Data;

import java.util.List;

/** 用于添加节点时接收数据 */
@Data
public class StructureInfo {
    private EnergyResourceStructure parentNode;

    private EnergyStructure addOneEnergyNode;
    private List<EnergyResourceStructure> addStructuresEnergy;
    private List<DefaultResourceTreeDTO> addStructuresDefault;

    //新增时的选项参数
    /** true-表示仅复制节点名称； false-表示直接引用该节点 */
    private Boolean onlyCopyName;
    /** true-表示保留原树结构； false-表示不保留原树结构，将所有选择的节点全部添加为父节点的直接子节点 */
    private Boolean preserveTreeStructure;
}
