package com.siteweb.energy.model;


import com.siteweb.common.util.StringUtils;
import lombok.Data;

import java.util.List;

@Data
public class OperateLogInfo {

    /** 操作类型：1-新增； 2-修改； 3-删除 */
    private int operateType;

    private String operateObj;

    private String schemeName;
    private String schemeId;

    private String fpgDescKey;
    private String fpgDesc;
    private String fpgStartTime;
    private String fpgEndTime;
    private String fpgId;

    private String stepPriceName;
    private String stepPriceId;

    private String fpgValue;
    private String fpgValueId;

    private List<ChangeField> changeFieldList;

    private String oldStructures;
    private String newStructures;

    private String operateTitle() {
        String tmp = StringUtils.isBlank(operateObj) ? "" : (" " + operateObj);
        if(operateType == 1) {
            return "[New" + tmp + "]: ";
        } else if(operateType == 2) {
            return "[UPDATE" + tmp + "]: ";
        } else if(operateType == 3) {
            return "[DELETE" + tmp + "]: ";
        } else {
            return "[UNKNOWN] ";
        }
    }

    public String toString(){
        StringBuffer sb = new StringBuffer();
        sb.append(operateTitle());
        if(StringUtils.isNoneBlank(schemeName)) {
            sb.append(schemeName);
            sb.append("[" + schemeId + "] ");
        }

        if(StringUtils.isNotBlank(fpgDesc)) {
            sb.append(" | " + fpgDesc);
            sb.append("[" + fpgDescKey + "]");
        }
        if(StringUtils.isNotBlank(fpgStartTime)) {
            sb.append(" From " + fpgStartTime);
        }
        if(StringUtils.isNotBlank(fpgEndTime)) {
            sb.append(" To " + fpgEndTime);
        }
        if(StringUtils.isNotBlank(fpgId)) {
            sb.append(" fpgId(" + fpgId + ") ");
        }
        if(StringUtils.isNotBlank(stepPriceName)) {
            sb.append(" | StepPrice:" + stepPriceName);
            sb.append("[" + stepPriceId + "] ");
        }
        if(StringUtils.isNotBlank(fpgValue)) {

            sb.append(" | fpgValue:" + fpgValue);
            sb.append("[" + fpgValueId + "] ");
        }

        boolean listNotEmpty = changeFieldList != null && changeFieldList.size() > 0;
        boolean structuresNotEmpty = StringUtils.isNotBlank(oldStructures);

        if(listNotEmpty || structuresNotEmpty) {
            sb.append("  \r\nChange Detail：  \r\n");

            if(listNotEmpty) {
                for (ChangeField changeField : changeFieldList) {
                    sb.append("  " + changeField.getObjectName());
                    sb.append("->");
                    sb.append(changeField.getFieldName());
                    sb.append(": change from ");
                    sb.append(changeField.getOldValue());
                    sb.append(" to ");
                    sb.append(changeField.getNewValue());
                    sb.append("  \r\n");
                }
            }
            if(structuresNotEmpty) {
                sb.append("  elecfeeschemestructuremap->appliedRange: has been changed ");
                sb.append("  \r\n");
            }
        }
        return sb.toString();
    }
}