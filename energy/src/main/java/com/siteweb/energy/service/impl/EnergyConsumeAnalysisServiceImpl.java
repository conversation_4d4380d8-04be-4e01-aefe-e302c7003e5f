package com.siteweb.energy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ctc.wstx.util.DataUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.dto.ComplexIndexQueryResult;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.enumeration.SourceCategory;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.*;
import com.siteweb.energy.service.*;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.mapper.PreAlarmHistoryMapper;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.annotation.AccessType;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
public class EnergyConsumeAnalysisServiceImpl implements EnergyConsumeAnalysisService {
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    PreAlarmHistoryMapper preAlarmHistoryMapper;
    @Autowired
    EnergyOverViewService energyOverViewService;
    @Autowired
    PreAlarmService preAlarmService;
    @Autowired
    PreAlarmSeverityService preAlarmSeverityService;
    @Autowired
    EnergyTotalAnalysisService energyTotalAnalysisService;

    @Autowired
    private EnergyElecFeeConfigService energyElecFeeConfigService;
    @Autowired
    private EnergyElecFeeSchemeMapper schemeMapper;
    @Autowired
    private EnergyElecFeeFpgMapper energyElecFeeFpgMapper;
    @Autowired
    private EnergyDataConfigItemService energyDataConfigItemService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;
    /** 需量分析
     * @Description:预警排名(汇总、一级告警、二级告警...)
     **/
    @Override
    public List<EnergyPreAlarmDTO> findPreAlarmRank(Date startTime, Date endTime,Integer objectId,Integer objectTypeId,Integer businessTypeId, Integer isConsumeAlarm) {
        List<EnergyPreAlarmDTO> result = new ArrayList<>();
        EnergyPreAlarmDTO oneResult = new EnergyPreAlarmDTO();

        // 为空获取根节点
        if (ObjectUtil.isNull(objectId) || ObjectUtil.isNull(objectTypeId)) {
            ResourceStructure resourceStructure = resourceStructureManager.getRoot();
            objectId = resourceStructure.getResourceStructureId();
            objectTypeId = resourceStructure.getStructureTypeId();
        }

        // 查询预警列表
        List<PreAlarmHistory> allPreAlarm =  findPreAlarmList(startTime, endTime,objectId,objectTypeId,businessTypeId,isConsumeAlarm);
        List<PreAlarmSeverity> lstPreAlarmSeverity = preAlarmSeverityService.findAllPreAlarmSeverity();

        //存在的告警级别
        List<Integer> lstExistsSeverity = allPreAlarm.stream().map(PreAlarmHistory::getPreAlarmSeverity).distinct().collect(Collectors.toList());

        // 汇总预警
        oneResult = new EnergyPreAlarmDTO();
        PreAlarmSeverity totalSeverity = new PreAlarmSeverity();
        totalSeverity.setPreAlarmSeverityId(-1);
        oneResult.setPreAlarmSeverity(totalSeverity);

        List<EnergyNameValueDTO> lstNameValueTotal = new ArrayList<>();
        Map<String, Long> severityTotalGroupBy = allPreAlarm.stream().collect(Collectors.groupingBy(PreAlarmHistory::getObjectName,Collectors.counting()));
        Iterator<String> iteratorTotal = severityTotalGroupBy.keySet().iterator();
        while (iteratorTotal.hasNext()) {
            EnergyNameValueDTO nameValue = new EnergyNameValueDTO();
            String key = iteratorTotal.next();
            nameValue.setName(key);
            nameValue.setValue(severityTotalGroupBy.get(key));

            lstNameValueTotal.add(nameValue);
        }
        oneResult.setLstPreAlarmHistory(lstNameValueTotal.stream().sorted(Comparator.comparing(EnergyNameValueDTO::getValue).reversed()).collect(Collectors.toList()));
        result.add(oneResult);

        // 各等级预警
        for (Integer severityId : lstExistsSeverity){
            oneResult = new EnergyPreAlarmDTO();
            PreAlarmSeverity thisSeverity = lstPreAlarmSeverity.stream().filter(i->i.getPreAlarmSeverityId().equals(severityId)).findFirst().orElse(null);
            oneResult.setPreAlarmSeverity(thisSeverity);
            List<EnergyNameValueDTO> lstNameValue = new ArrayList<>();

            List<PreAlarmHistory> thisSeverityAlarm = allPreAlarm.stream().filter(i->i.getPreAlarmSeverity().equals(severityId)).collect(Collectors.toList());
            Map<String, Long> severityGroupBy = thisSeverityAlarm.stream().collect(Collectors.groupingBy(PreAlarmHistory::getObjectName,Collectors.counting()));
            Iterator<String> iterator = severityGroupBy.keySet().iterator();
            while (iterator.hasNext()) {
                EnergyNameValueDTO nameValue = new EnergyNameValueDTO();
                String key = iterator.next();
                nameValue.setName(key);
                nameValue.setValue(severityGroupBy.get(key));

                lstNameValue.add(nameValue);
            }
            oneResult.setLstPreAlarmHistory(lstNameValue.stream().sorted(Comparator.comparing(EnergyNameValueDTO::getValue).reversed()).collect(Collectors.toList()));
            result.add(oneResult);
        }
        return result;
    }

    /** 需量分析
     * @Description:预警列表
     **/
    @Override
    public List<PreAlarmHistory> findPreAlarmList(Date startTime, Date endTime,Integer objectId,Integer objectTypeId,Integer businessTypeId, Integer isConsumeAlarm){
        List<PreAlarmHistory> result = new ArrayList<>();

        //获取预警和历史预警表中在这个时间范围的预警
        List<PreAlarm> preAlarmList = preAlarmService.findAllPreAlarm().stream().filter(
                i->i.getStartTime().after(startTime) && i.getStartTime().before(endTime)
                        && i.getPreAlarmCategory().equals(2) && i.getBusinessTypeId() != null
        ).collect(Collectors.toList());

        List<PreAlarmHistory> preAlarmHistoryList = preAlarmHistoryMapper.selectList(
                new QueryWrapper<PreAlarmHistory>().ge("startTime", startTime).le("startTime", endTime).eq("preAlarmCategory", 2)
        );

        if (!ObjectUtil.isNull(businessTypeId)) {
            preAlarmList = preAlarmList.stream().filter(i->i.getBusinessTypeId().equals(businessTypeId)).collect(Collectors.toList());
            preAlarmHistoryList = preAlarmHistoryList.stream().filter(i->i.getBusinessTypeId().equals(businessTypeId)).collect(Collectors.toList());
        }

        //PreAlarmPointId<0为能耗管控计划预警
        if (isConsumeAlarm == 1){  //能源用量预警
            preAlarmList = preAlarmList.stream().filter(i->
                    i.getPreAlarmPointId()>0 && (
                            (i.getObjectId().equals(objectId) && i.getObjectTypeId().equals(objectTypeId))
                                    || (i.getLevelOfPath().contains(objectId +".")) )
            ).collect(Collectors.toList());
            preAlarmHistoryList = preAlarmHistoryList.stream().filter(i->
                    i.getPreAlarmPointId()>0 && (
                            (i.getObjectId().equals(objectId) && i.getObjectTypeId().equals(objectTypeId))
                                    || (i.getLevelOfPath().contains(objectId +".")) )
            ).collect(Collectors.toList());
        }else{   //计划量预警
            preAlarmList = preAlarmList.stream().filter(i->
                    i.getPreAlarmPointId()<0 && (
                            (i.getObjectId().equals(objectId) && i.getObjectTypeId().equals(objectTypeId))
                                    || (i.getLevelOfPath().contains(objectId +".")) )
            ).collect(Collectors.toList());
            preAlarmHistoryList = preAlarmHistoryList.stream().filter(i->
                    i.getPreAlarmPointId()<0 && (
                            (i.getObjectId().equals(objectId) && i.getObjectTypeId().equals(objectTypeId))
                                    || (i.getLevelOfPath().contains(objectId +".")) )
            ).collect(Collectors.toList());
        }

        for(PreAlarm preAlarm : preAlarmList){
            PreAlarmHistory preAlarmHistory = new PreAlarmHistory();
            preAlarmHistory.convert(preAlarm);
            result.add(preAlarmHistory);
        }
        for(PreAlarmHistory preAlarmHistory : preAlarmHistoryList){
            result.add(preAlarmHistory);
        }

        return result.stream().sorted(Comparator.comparing(PreAlarmHistory::getStartTime).reversed()).collect(Collectors.toList());
    }

    /** 需量分析
     * @Description:子节点需量计划量列表
     **/
    @Override
    public List<EnergyConsumeChildDTO> findChildConsumeTrend(Date startTime, Date endTime,Integer objectId,Integer objectTypeId,Integer businessTypeId) {
        List<EnergyConsumeChildDTO> result = new ArrayList<EnergyConsumeChildDTO>();
        List<StructureOfComplexIndexValue> complexIndexConsumeresult = new ArrayList<StructureOfComplexIndexValue>();
        List<ResourceStructure> childResourceStructure = new ArrayList<>();

        try {
            childResourceStructure = resourceStructureManager.getResourceStructureLstByParentId(objectId);
            complexIndexConsumeresult = energyOverViewService.GetConsumeOfResourceStructure(businessTypeId,childResourceStructure,startTime,endTime,"d",false);

            //赋值计划量
            for(StructureOfComplexIndexValue temp : complexIndexConsumeresult){
                EnergyConsumeChildDTO child = new EnergyConsumeChildDTO();
                child.setSumValue(temp.getSumValue());
                child.setPlanValue(energyTotalAnalysisService.findPlanValueByEnergyTypeIdAndTime(startTime, endTime, businessTypeId , objectId));
                child.setResourceStructureName(temp.getResourceStructureName());
                child.setDeviationValue(NumberUtil.doubleAccuracy(child.getPlanValue()-child.getSumValue(),2));
                result.add(child);
            }
            return result.stream().sorted(Comparator.comparing(EnergyConsumeChildDTO::getSumValue)).collect(Collectors.toList());
        }catch (Exception e){
        }
        return result;
    }



    /** 峰平谷分析
     * @Description:费控时段及分项
     **/
    @Override
    public List<EnergyFpgValueDTO> findFpgValue(Date startTime, Date endTime,Integer objectId,Integer objectTypeId){

        List<EnergyFpgValueDTO> result = new ArrayList<>();
        EnergyFpgValueDTO dtoTotal = new EnergyFpgValueDTO();
        dtoTotal.setItemValue(messageSourceUtil.getMessage("energy.totalanalysis.total"));
        dtoTotal.setItemId(-1);
        dtoTotal.setSumValue(0d);
        dtoTotal.setSumFeeValue(0d);

        EnergyFpgValueDTO dto = new EnergyFpgValueDTO();

        List<Integer> structures = energyElecFeeConfigService.getAllStructureIdsHasScheme();
        // 所有费控时段名称
        List<EnergyDataItem> allDataItem = energyDataConfigItemService.getAllByEntryId(1);
        // 总用电量指标；
        ComplexIndex cIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex().stream().filter(
                i->i.getObjectId().equals(objectId) && i.getBusinessTypeId().equals(2)).findFirst().orElse(null);

        List<Integer> lstExistFpg = new ArrayList<>();
        // 判断有总用电量指标   // 和节点电费方案
        if (cIndex != null){ // && structures.contains(objectId)) {
            List<EnergyElecFeeScheme> allScheme = new ArrayList<>();
            if(structures.contains(objectId)){
                allScheme = schemeMapper.findByResourceStructureId(objectId);
            }
            else {
                //找有方案的父节点的用电分项配置
                ResourceStructure parentRS = resourceStructureManager.getResourceStructureById(resourceStructureManager.getResourceStructureById(objectId).getParentResourceStructureId());
                while (parentRS != null) {
                    if (structures.contains(parentRS.getResourceStructureId())) {
                        allScheme = schemeMapper.findByResourceStructureId(parentRS.getResourceStructureId());
                        break;
                    }
                    parentRS = resourceStructureManager.getResourceStructureById(parentRS.getParentResourceStructureId());
                }
            }

            List<EnergyElecFeeFpg> lstFpg = new ArrayList<>();
            for(EnergyElecFeeScheme scheme : allScheme){
                lstFpg.addAll(energyElecFeeFpgMapper.getBySchemeId(scheme.getSchemeId()));
            }

            if(lstFpg.size() == 0){
                result.add(dtoTotal);
                return result;
            }
            lstFpg = lstFpg.stream().sorted(Comparator.comparing(EnergyElecFeeFpg::getFpgDescKey)).collect(Collectors.toList());
            //因业务修改为所有峰平谷类型全部输出
            for(EnergyDataItem item : allDataItem){
                if (lstExistFpg.contains(item.getItemId()))
                    continue;
                lstExistFpg.add(item.getItemId());
                dto = new EnergyFpgValueDTO();
                List<HistoryComplexIndexFeeSum> resultInfluxDB = new ArrayList<>();
                try {
                    QueryResult query = null;
                    InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("select SUM(IndexValue) as indexSum,SUM(IndexFeeValue) as sum from EnergyHisFeeData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId and fpgDescKey = $fpgDKey group by ComplexIndexId,fpgDescKey ")
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .bind("complexIndexId", cIndex.getComplexIndexId().toString())
                            .bind("fpgDKey",item.getItemId().toString())
                            .create();
                    query = influxDB.query(queryBuilder);

                    if (query != null) {
                        resultInfluxDB = resultMapper.toPOJO(query,HistoryComplexIndexFeeSum.class);
                    }
                    if (resultInfluxDB.isEmpty()) {
                        dto.setSumValue(0d);
                        dto.setSumFeeValue(0d);
                        dto.setItemId(item.getItemId());
                        dto.setItemValue(item.getItemValue());
                    }
                    else{
                        String sumValue = resultInfluxDB.get(0).getIndexSum()==null?"":resultInfluxDB.get(0).getIndexSum();
                        Double sumValueDouble  = NumberUtil.doubleAccuracy(Double.parseDouble(sumValue),2);
                        dto.setSumValue(sumValueDouble);

                        Double sumFeeValue = resultInfluxDB.get(0).getSum() == null ? 0d : NumberUtil.doubleAccuracy(Double.parseDouble(resultInfluxDB.get(0).getSum()),2);
                        dto.setSumFeeValue(sumFeeValue);

                        dto.setItemId(item.getItemId());
                        EnergyDataItem energyDataItem = allDataItem.stream().filter(i->i.getItemId().equals(item.getItemId())).findFirst().orElse(null);
                        if (energyDataItem == null) continue;
                        dto.setItemValue(energyDataItem.getItemValue());

                        dtoTotal.setSumValue(NumberUtil.doubleAccuracy(dtoTotal.getSumValue()+ dto.getSumValue(),2));
                        dtoTotal.setSumFeeValue(NumberUtil.doubleAccuracy(dtoTotal.getSumFeeValue()+ dto.getSumFeeValue(),2));
                    }

                    result.add(dto);
                }catch (Exception e){
                }
            }
            result.add(0,dtoTotal);
        }
        else{
            result.add(dtoTotal);
        }
        return result;
    }

    /** 峰平谷分析
     * @Description:所有子节点的峰平谷分析
     **/
    @Override
    public List<EnergyChildFpgValueDTO> findChildFpgValue(Date startTime, Date endTime,Integer objectId,Integer objectTypeId){
        List<EnergyChildFpgValueDTO> result = new ArrayList<>();
        EnergyChildFpgValueDTO oneResult;
        List<ResourceStructure> lstChildResourceStructure = resourceStructureManager.getResourceStructureLstByParentId(objectId);
        for(ResourceStructure rs : lstChildResourceStructure){
            oneResult = new EnergyChildFpgValueDTO();
            oneResult.setResourceStructureId(rs.getResourceStructureId());
            oneResult.setResourceStructureName(rs.getResourceStructureName());
            oneResult.setLstEnergyFpgValueDTO(findFpgValue(startTime,endTime,rs.getResourceStructureId(),rs.getStructureTypeId()));
            result.add(oneResult);
        }
        return result;
    }

}
