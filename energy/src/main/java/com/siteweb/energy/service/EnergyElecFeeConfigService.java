package com.siteweb.energy.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.energy.dto.ElecFeeFpg;
import com.siteweb.energy.dto.ElecFeeScheme;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyElecFeeStepPrice;
import com.siteweb.energy.model.FpgInfo;
import com.siteweb.energy.model.SchemeUnitPrice;
import com.siteweb.monitoring.entity.ResourceStructure;

import java.util.Date;
import java.util.List;

public interface EnergyElecFeeConfigService {
    /** 拿到完整的默认层级树 */
    ResourceStructure findResourceStructureTree();

    /**
     * 插入一条新的电费方案
     * @return 插入的结果信息
     */
    ResultObject<String> addElecFeeScheme(ElecFeeScheme newScheme, Integer userId, String userName) throws JsonProcessingException;

    ResultObject<List<ElecFeeScheme>> getElecFeeSchemesByResourceStructureId(Integer resourceStructureId);

    ResultObject<String> delElecFeeSchemes(List<Integer> ids, Integer userId, String userName) throws JsonProcessingException;

    ResultObject<ElecFeeScheme> getElecFeeSchemeBySchemeId(Integer schemeId);

    ResultObject<List<EnergyElecFeeStepPrice>> getStepPricesBySchemeId(Integer schemeId);

    ResultObject<FpgInfo> getFpgsBySchemeId(Integer schemeId);

    ResultObject<String> delElecFeeStepPrice(Integer stepPriceId, Integer schemeId, Integer userId, String userName) throws JsonProcessingException;

    ResultObject<String> delElecFeeFpg(Integer fpgId, Integer schemeId, Integer userId, String userName) throws JsonProcessingException;

    ResultObject<EnergyElecFeeStepPrice> getStepPriceByStepPriceId(Integer stepPriceId);

    ResultObject<ElecFeeFpg> getFpgByFpgId(Integer fpgId, Integer schemeId);

    ResultObject<String> updateElecFeeScheme(ElecFeeScheme newScheme, Integer userId, String userName);

    ResultObject<String> updateElecFeeStepPrice(EnergyElecFeeStepPrice newStepPrice, Integer userId, String userName);

    ResultObject<String> updateElecFeeFpg(ElecFeeFpg newFpg, Integer userId, String userName);

    ResultObject<String> addElecFeeStepPrice(EnergyElecFeeStepPrice newStepPrice, Integer userId, String userName) throws JsonProcessingException;

    ResultObject<String> addElecFeeFpg(ElecFeeFpg newFpg, Integer userId, String userName) throws JsonProcessingException;

    /** 根据节点某时刻用电量获取电单价
    SchemeUnitPrice GetSchemeUnitPrice(Integer resourceStructureId, Date calcTime, double thisMonthSum);*/
    /** 根据节点某时刻用电量获取【某一业务类型】的单价 */
    SchemeUnitPrice GetSchemeUnitPrice(Integer resourceStructureId, Date calcTime, double thisMonthSum, Integer businessTypeId);

    /** 获取电费配置需要的层级树，deep为从根节点开始要显示的深度 */
    ResourceStructure getResourceStructuresByPersonId(Integer personId, Integer deep);

    /** true-至少已配置了一个启用的电费方案； false-不存在已启用的电费方案*/
    boolean hasEnabledScheme();

    /** 重新加载电费方案缓存 */
    void reloadCache();

    /** 得到传入时间范围内已配置有效电费方案的所有节点id的集合(约定开始时间和结束时间不会跨天)
    List<Integer> getAllStructureIdsHasScheme(Date startTime, Date endTime);*/
    /** 得到传入时间范围内【某一业务类型】已配置有效方案的所有节点id的集合(约定开始时间和结束时间不会跨天) */
    List<Integer> getAllStructureIdsHasScheme(Date startTime, Date endTime, Integer businessTypeId);

    /** 得到已配置有效电费方案的所有节点id的集合 */
    List<Integer> getAllStructureIdsHasScheme();
    /** 得到【某一业务类型】已配置有效方案的所有节点id的集合  */
    List<Integer> getAllStructureIdsHasScheme(Integer businessTypeId);

    /**  获取能耗相关的所有子类业务类型(ParentId==1的类型) */
    List<Object> getComplexindexbusinesstypeForEnergy();
}
