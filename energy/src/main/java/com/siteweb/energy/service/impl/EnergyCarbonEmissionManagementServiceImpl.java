package com.siteweb.energy.service.impl;

import com.siteweb.energy.dto.EnergyCarbonEmissionDTO;
import com.siteweb.energy.dto.EnergyCarbonManageDTO;
import com.siteweb.energy.entity.EnergyCarbonEmissionPara;
import com.siteweb.energy.mapper.EnergyCarbonEmissionManageMapper;
import com.siteweb.energy.service.EnergyCarbonEmissionManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
@Service
@Slf4j
public class EnergyCarbonEmissionManagementServiceImpl implements EnergyCarbonEmissionManagementService {
    @Autowired
    private EnergyCarbonEmissionManageMapper energyCarbonEmissionManageMapper;

    @Override
    public List<String> getUnit() {
        return energyCarbonEmissionManageMapper.getUnit();
    }
    @Override
    public Integer addNewConfiguration(EnergyCarbonEmissionDTO energyCarbonEmissionDTO) {
        String objectId = energyCarbonEmissionDTO.getObjectId();
        String objectType = energyCarbonEmissionDTO.getObjectTypeId();
        int res = -1;
        if(energyCarbonEmissionDTO.getIsArea() == 1){
            EnergyCarbonEmissionPara check = energyCarbonEmissionManageMapper.getAreaIsConfiguration(objectId,objectType);
            if(check == null)
                res = energyCarbonEmissionManageMapper.addAreaCarbonEmission(objectId,objectType,energyCarbonEmissionDTO.getArea(),energyCarbonEmissionDTO.getIsArea(),energyCarbonEmissionDTO.getUnits());
            else
                res = energyCarbonEmissionManageMapper.updateAreaInfo(objectId,objectType,energyCarbonEmissionDTO.getArea(),energyCarbonEmissionDTO.getUnits());
        }
        else {
            for (EnergyCarbonEmissionPara e : energyCarbonEmissionDTO.getEnergyCarbonEmissionDataList()) {
                res = energyCarbonEmissionManageMapper.insert(e);
                if (res != 1){
                    log.error("EnergyCarbonEmissionManagementServiceImpl-addNewConfiguration 插入数据失败");
                    return res;
                }
            }
        }
        if (res != 1)
            log.error("EnergyCarbonEmissionManagementServiceImpl-addNewConfiguration 插入数据失败");
        return res;
    }

    @Override
    public Integer deleteConfiguration(Integer id) {
        int res = -1;
        res = energyCarbonEmissionManageMapper.deleteNoAreaCarbonEmissionById(id);
        return res;
    }

    @Override
    public List<Integer> findCarbonEmissionYears(String objectId, String objectTypeId) {
        List<EnergyCarbonEmissionPara> years = energyCarbonEmissionManageMapper.getConfiguration(objectId,objectTypeId);
        return years.stream()
                .sorted(Comparator.comparingDouble(EnergyCarbonEmissionPara::getYear).reversed())
                .map(EnergyCarbonEmissionPara::getYear)
                .distinct().toList();
    }

    @Override
    public List<EnergyCarbonEmissionPara> findCarbonEmissionHistoryData(String objectId, String objectTypeId, Integer year) {

        List<EnergyCarbonEmissionPara> res = energyCarbonEmissionManageMapper.getHistoryData(objectId,objectTypeId,year - 1);
        if(res.isEmpty()){
            for (int i = 1; i < 13; i++) {
                EnergyCarbonEmissionPara temp = new EnergyCarbonEmissionPara();
                temp.setIsArea(0);
                temp.setObjectId(objectId);
                temp.setObjectTypeId(objectTypeId);
                temp.setYear(year);
                temp.setYearPlanTotalValue(0.0F);
                temp.setMonth(i);
                temp.setPlanValue(0.0F);
                res.add(temp);
            }
        }
        return res;
    }

    @Override
    public Integer updateConfiguration(EnergyCarbonEmissionDTO energyCarbonEmissionDTO) {
        int res = -1;
        for (EnergyCarbonManageDTO e : energyCarbonEmissionDTO.getList()) {
            res = res & energyCarbonEmissionManageMapper.updateDataById(e.getId(), e.getPlanValue(),energyCarbonEmissionDTO.getYearPlanTotalValue(),energyCarbonEmissionDTO.getUnits());
            if (res != 1){
                log.error("EnergyCarbonQuotaManagementServiceImpl-updateCarbonQuotaData 更新数据失败");
                return -1;
            }
        }
        return res;
    }

    @Override
    public List<EnergyCarbonEmissionPara> findCarbonEmission(String objectId, String objectTypeId, Integer isArea, String year) {
        List<EnergyCarbonEmissionPara> result = new ArrayList<>();
        if(year .equals("null"))
            year = "1970";
        if(isArea == 1){
            result = energyCarbonEmissionManageMapper.getAreaConfigurationInfo(objectId,objectTypeId,isArea);
        }
        else {
            result = energyCarbonEmissionManageMapper.getNoAreaConfigurationInfo(objectId,objectTypeId,Integer.parseInt(year));
        }
        return result;
    }
}
