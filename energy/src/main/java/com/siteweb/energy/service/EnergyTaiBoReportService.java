package com.siteweb.energy.service;

import com.siteweb.energy.entity.EnergyCustomerTBReportRecord;
import com.siteweb.energy.entity.EnergyCustomerTbReport;

import java.util.Date;
import java.util.List;

public interface EnergyTaiBoReportService {
    void doSchedule();

    List<EnergyCustomerTBReportRecord> GetTaiBoReportList(Date startTime, Date endTime,String reportIds);

    Object[] GetTaiBoReportListDownLoad(Integer recordId);

    /** 创建天然气月报表 */
    boolean createMonthlyNaturalGasReport();

    List<EnergyCustomerTbReport> GetTaiBoReportTemplate();
}
