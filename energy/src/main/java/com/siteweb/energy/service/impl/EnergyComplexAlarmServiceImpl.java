package com.siteweb.energy.service.impl;

import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyComplexPreAlarmService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmCategory;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import com.siteweb.prealarm.service.PreAlarmCategoryService;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;

@Service
@Slf4j
public class EnergyComplexAlarmServiceImpl implements EnergyComplexPreAlarmService {
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private PreAlarmService preAlarmService;
    @Autowired
    private PreAlarmMapper preAlarmMapper;
    @Autowired
    private PreAlarmCategoryService preAlarmCategoryService;
    @Autowired
    private PreAlarmSeverityService preAlarmSeverityService;
    @Autowired
    ResourceStructureManager resourceStructureManager;


    @Override
    public void EnergyComplexValueCheck(String time, String complexIndexId, String indexValue,String originalIndexValue) {
        try {
            ComplexIndex complex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(Integer.parseInt(complexIndexId))).findFirst().orElse(null);
            if (complex == null) return;

            PreAlarmSeverity preAlarmSeverity = preAlarmSeverityService.findByPreAlarmSeverityId(2);
            PreAlarmCategory preAlarmCategory = preAlarmCategoryService.findByCategoryId(9);

            PreAlarm preAlarm = new PreAlarm();
            preAlarm.setPreAlarmPointId(-1);
            preAlarm.setMeanings(complex.getComplexIndexName() + "," + "指标值:"+originalIndexValue+"异常，修正值为："+indexValue);
            preAlarm.setPreAlarmSeverity(2);
            preAlarm.setPreAlarmSeverityName("二级预警");
            preAlarm.setPreAlarmCategory(preAlarmCategory.getCategoryId());
            preAlarm.setPreAlarmCategoryName(preAlarmCategory.getCategoryName());
            preAlarm.setColor(preAlarmSeverity.getColor());

            preAlarm.setUniqueId(String.valueOf(complexIndexId));
            preAlarm.setUniqueName(complex.getComplexIndexName());
            preAlarm.setObjectId(complex.getComplexIndexId());
            preAlarm.setObjectTypeId(complex.getObjectTypeId());

            ResourceStructure resourceStructure =  resourceStructureManager.getResourceStructureById(complex.getObjectId());
            preAlarm.setObjectName(resourceStructure.getResourceStructureName());
            preAlarm.setResourceStructureId(complex.getObjectId());

            preAlarm.setLevelOfPath(resourceStructure.getLevelOfPath());
            String levelName = resourceStructureManager.getFullPath(complex.getObjectId());
            preAlarm.setLevelOfPathName(levelName);
            preAlarm.setTriggerValue(originalIndexValue);
            preAlarm.setUnit(complex.getUnit());
            preAlarm.setSampleTime(new Date());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date errorTime = dateFormat.parse(time);
            preAlarm.setStartTime(errorTime);
            preAlarm.setBusinessTypeId(complex.getBusinessTypeId());
            preAlarmMapper.insert(preAlarm);
            preAlarmService.addToCache(preAlarm);
        } catch (Exception e) {
            log.error("EnergyComplexValueCheck createNewPreAlarm error", e);
        }
    }
}
