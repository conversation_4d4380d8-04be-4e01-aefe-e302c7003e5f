package com.siteweb.energy.service.impl;


import com.siteweb.common.util.NumberUtil;

import com.siteweb.complexindex.entity.ComplexIndex;

import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.complexindex.service.ComplexIndexDefinitionService;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyConfigTelComService;

import com.siteweb.energy.service.EnergyDataConfigItemService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.service.ResourceStructureService;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;


import static com.siteweb.common.util.DateUtil.dateToString;

@Service
public class EnergyConfigTelComServiceImpl implements EnergyConfigTelComService {
    private final Logger log = LoggerFactory.getLogger(EnergyConfigTelComService.class);

    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;
    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    EnergyDataConfigItemService energyDataConfigItemService;

    @Autowired
    ComplexIndexDefinitionService complexIndexDefinitionService;
    @Autowired
    ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;


    @Override
    public EnergyConfigTelComResultDTO getTotalElectricity( String timeType, Date startTime, Date endTime, Integer userId, String queryType) {
        EnergyConfigTelComResultDTO res = new EnergyConfigTelComResultDTO();
        double cityCarbonCoefficient = findCarbonCoefficient(2);
        double oilCarbonCoefficient = findCarbonCoefficient(3);
        double greenCarbonCoefficient = findCarbonCoefficient(4);
        //总用电量、同环比
        double totalElectricityValue = 0D;
        double yoyTotalElectricityValue = 0D;
        double qoqTotalElectricityValue = 0D;

        //市电、同环比
        double cityElectValue = 0D;
        double yoyCityElectValue = 0D;
        double qoqCityElectValue = 0D;
        //油机、同环比
        double oilElectValue = 0D;
        double yoyOilElectValue = 0D;
        double qoqOilElectValue = 0D;
        //绿电、同环比
        double greenElectValue = 0D;
        double yoyGreenElectValue = 0D;
        double qoqGreenElectValue = 0D;

        //总碳排放、同环比
        double totalCarbonValue = 0D;
        double yoyTotalCarbonValue = 0D;
        double qoqTotalCarbonValue = 0D;
        //市电碳排放
        double cityElectCarbonValue = 0D;
        //油机碳排放
        double oilElectCarbonValue = 0D;
        //绿电碳排放
        double greenElectCarbonValue = 0D;
        double yoyGreenElectCarbonValue = 0D;
        double qoqGreenElectCarbonValue = 0D;
        List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
        //筛选出基站的节点
        List<ResourceStructure> filterResourceStructures = roleResourceStructures.stream().filter(i->i.getStructureTypeId().equals(104)).toList();
        List<StructureOfComplexIndexValue> structureComplexList  = new ArrayList<>();
        for (int i = 2; i < 5; i++) {
            if(i == 4)
                structureComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures, 4,"total"));
            else
                structureComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures, i,"total"));
        }
        List<StructureOfComplexIndexValue> yoyStructureComplexList = structureComplexList.stream()
                .map(StructureOfComplexIndexValue::clone)
                .collect(Collectors.toList());
        List<StructureOfComplexIndexValue> qoqStructureComplexList = structureComplexList.stream()
                .map(StructureOfComplexIndexValue::clone)
                .collect(Collectors.toList());
        //当前总用电量
        GetTotalValueOfComplexIndex(structureComplexList,startTime,endTime,timeType,false);
        for (StructureOfComplexIndexValue oneObject : structureComplexList) {
            double tempValue = oneObject.getSumValue() == null ? 0D : oneObject.getSumValue();
            if (oneObject.getBusinessTypeId() == 2)
                cityElectValue += tempValue;
            else if (oneObject.getBusinessTypeId() == 3)
                oilElectValue += tempValue;
            else
                greenElectValue += tempValue;

            totalElectricityValue += tempValue;
        }

        cityElectCarbonValue = cityElectValue * cityCarbonCoefficient;
        oilElectCarbonValue = oilElectValue * oilCarbonCoefficient;
        greenElectCarbonValue = greenElectValue * greenCarbonCoefficient;

        totalCarbonValue = cityElectCarbonValue + oilElectCarbonValue ;

        //同比
        List<Date> YoYDate = getQoQDateOrYoYDate(startTime,endTime,"YoY",timeType);
        GetTotalValueOfComplexIndex(yoyStructureComplexList,YoYDate.get(0),YoYDate.get(1),timeType,false);
        for (StructureOfComplexIndexValue oneObject: yoyStructureComplexList ) {
            double tempValue = oneObject.getSumValue() == null ? 0D : oneObject.getSumValue();
            if (oneObject.getBusinessTypeId() == 2)
                yoyCityElectValue += tempValue;
            else if (oneObject.getBusinessTypeId() == 3)
                yoyOilElectValue += tempValue;
            else
                yoyGreenElectValue += tempValue;

            yoyTotalElectricityValue += tempValue;
        }

        yoyTotalCarbonValue = yoyCityElectValue * cityCarbonCoefficient + yoyOilElectValue * oilCarbonCoefficient ;
        yoyGreenElectCarbonValue = yoyGreenElectValue * greenCarbonCoefficient;
        //环比
        List<Date> QoQDate = getQoQDateOrYoYDate(startTime,endTime,"QoQ",timeType);
        GetTotalValueOfComplexIndex(qoqStructureComplexList,QoQDate.get(0),QoQDate.get(1),timeType,false);
        for (StructureOfComplexIndexValue oneObject: qoqStructureComplexList ) {
            double tempValue = oneObject.getSumValue() == null ? 0D : oneObject.getSumValue();
            if (oneObject.getBusinessTypeId() == 2)
                qoqCityElectValue += tempValue;
            else if (oneObject.getBusinessTypeId() == 3)
                qoqOilElectValue += tempValue;
            else
                qoqGreenElectValue += tempValue;
            qoqTotalElectricityValue += tempValue;
        }
        qoqTotalCarbonValue = qoqCityElectValue * cityCarbonCoefficient + qoqOilElectValue * oilCarbonCoefficient;
        qoqGreenElectCarbonValue = qoqGreenElectValue * greenCarbonCoefficient;
        switch (queryType) {
            case "electTotal" -> {
                res.setTotalElectricityValue(NumberUtil.doubleAccuracy(totalElectricityValue,2));
                res.setYoyTotalElectricityValue(NumberUtil.doubleAccuracy(yoyTotalElectricityValue,2));
                res.setQoqTotalElectricityValue(NumberUtil.doubleAccuracy(qoqTotalElectricityValue,2));
            }
            case "carbonTotal" -> {
                res.setTotalCarbonValue(NumberUtil.doubleAccuracy(totalCarbonValue,2));
                res.setYoyTotalCarbonValue(NumberUtil.doubleAccuracy(yoyTotalCarbonValue,2));
                res.setQoqTotalCarbonValue(NumberUtil.doubleAccuracy(qoqTotalCarbonValue,2));
            }
            case "electCategory" -> {
                res.setCityElectValue(NumberUtil.doubleAccuracy(cityElectValue,2));
                res.setOilElectValue(NumberUtil.doubleAccuracy(oilElectValue,2));
                res.setGreenElectValue(NumberUtil.doubleAccuracy(greenElectValue,2));
            }
            case "carbonCategory" -> {
                res.setCityElectCarbonValue(NumberUtil.doubleAccuracy(cityElectCarbonValue,2));
                res.setOilElectCarbonValue(NumberUtil.doubleAccuracy(oilElectCarbonValue,2));
                res.setGreenElectCarbonValue(NumberUtil.doubleAccuracy(greenElectCarbonValue,2));
            }
            case "greenCarbon" -> {
                res.setGreenElectCarbonValue(NumberUtil.doubleAccuracy(greenElectCarbonValue,2));
                res.setYoyGreenElectCarbonValue(NumberUtil.doubleAccuracy(yoyGreenElectCarbonValue,2));
                res.setQoqGreenElectCarbonValue(NumberUtil.doubleAccuracy(qoqGreenElectCarbonValue,2));
            }
        }

        return res;
    }


    public double findCarbonCoefficient(Integer businessTypeId){
        double result = 0d;
        List<EnergyDataItem> energyDataItems = energyComplexIndexManager.getEnergyDataItemsByEntryId(7);
        ComplexIndexBusinessType type = energyComplexIndexManager.complexIndexBusinessTypeById(businessTypeId);
        if(type != null){
            for (EnergyDataItem item : energyDataItems) {
                if (item.getItemId().toString().equals(type.getDescription())) {
                    result  = item.getExtendField3() == null ? 0 : Double.parseDouble(item.getExtendField3());
                }
            }
        }
        return result;
    }

    public List<Date> getQoQDateOrYoYDate(Date startTime, Date endTime, String type,String timeType) {
        List<Date> res = new ArrayList<>();
        Date sTime = new Date();
        Date eTime = new Date();
        Calendar calendar = Calendar.getInstance();
        if (type.equals("YoY")) {
            calendar.setTime(startTime);
            calendar.add(Calendar.YEAR, -1);
            sTime = calendar.getTime();
            calendar.setTime(endTime);
            calendar.add(Calendar.YEAR, -1);
            eTime = calendar.getTime();
        }
        if (type.equals("QoQ")) {
            switch (timeType) {
                case "m":
                    // 月环比时间
                    calendar.setTime(startTime);
                    calendar.add(Calendar.MONTH, -1);
                    sTime = calendar.getTime();


                    calendar.add(Calendar.MONTH, 1);
                    calendar.add(Calendar.SECOND, -1);
                    eTime = calendar.getTime();
                    break;
                case "y":
                    calendar.setTime(startTime);
                    calendar.add(Calendar.YEAR, -1);
                    sTime = calendar.getTime();

                    calendar.setTime(endTime);
                    calendar.add(Calendar.YEAR, -1);
                    eTime = calendar.getTime();


                    break;
                case "d":
                    calendar.setTime(startTime);
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    sTime = calendar.getTime();

                    calendar.setTime(endTime);
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    eTime = calendar.getTime();

                    break;
            }

        }
        res.add(sTime);
        res.add(eTime);
        return res;
    }
    // 获取用能趋势以及碳排放趋势
    @Override
    public Object getEnergyAndCarbonTrend(String timeType, Date startTime, Date endTime, Integer userId,String resultType) {
        List<EnergyConfigTelComTrendResultDTO> res = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            res.add(getRoomEnergyAndCarbon(timeType,startTime,endTime,null,userId,"trend",resultType));
            startTime = AddOneStepByTimeType(startTime,timeType);
            endTime = GetEndTimeByStartTimeAndTimeType(startTime,timeType);
        }
        return res;
    }


    @Override
    public EnergyConfigTelComTrendResultDTO getRoomEnergyAndCarbon(String timeType, Date startTime, Date endTime, Integer resourceStructureId, Integer userId,String queryType,String resultType) {
        EnergyConfigTelComTrendResultDTO res = new EnergyConfigTelComTrendResultDTO();
        double cityElectValue = 0D;
        double oilElectValue = 0D;
        double greenElectValue = 0D;
        double totalElectricityValue = 0D;

        double cityCarbonValue = 0D;
        double oilCarbonValue = 0D;
        double greenCarbonValue = 0D;
        double totalCarbonValue = 0D;

        double cityCarbonCoefficient = findCarbonCoefficient(2);
        double oilCarbonCoefficient = findCarbonCoefficient(3);
        double greenCarbonCoefficient = findCarbonCoefficient(4);

        ResourceStructure oneRoom;
        List<ResourceStructure> roomList = new ArrayList<>();
        List<StructureOfComplexIndexValue> roomComplexList = new ArrayList<>();
        if(userId != null){
            List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
            //筛选出基站的节点
            List<ResourceStructure> filterResourceStructures = roleResourceStructures.stream().filter(i->i.getStructureTypeId().equals(104)).toList();
            roomList.addAll(filterResourceStructures);
        }
        else{
            oneRoom = resourceStructureService.findById(resourceStructureId);
            roomList.add(oneRoom);
        }

        for (int i = 2; i < 5; i++) {
            if(i == 4){
                roomComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(roomList,4,"total"));
            }
            else
                roomComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(roomList, i,"total"));
        }
        //当前总用电量
        GetTotalValueOfComplexIndex(roomComplexList,startTime,endTime,timeType,false);
        for (StructureOfComplexIndexValue oneObject : roomComplexList) {
            double tempValue = oneObject.getSumValue() == null ? 0D : oneObject.getSumValue();

            if (oneObject.getBusinessTypeId() == 2)
                cityElectValue += tempValue;
            else if (oneObject.getBusinessTypeId() == 3)
                oilElectValue += tempValue;
            else
                greenElectValue += tempValue;

            totalElectricityValue += tempValue;
        }
        cityCarbonValue = cityElectValue * cityCarbonCoefficient;
        oilCarbonValue = oilElectValue * oilCarbonCoefficient;
        greenCarbonValue = greenElectValue * greenCarbonCoefficient;

        totalCarbonValue = cityCarbonValue + oilCarbonValue + greenCarbonValue;

        switch (resultType) {
            case "carbon&energy" -> {
                res.setEnergyTotal(NumberUtil.doubleAccuracy(totalElectricityValue, 2));
                res.setCarbonTotal(NumberUtil.doubleAccuracy(totalCarbonValue, 2));
            }
            case "carbonCategory" -> {
                res.setCityElectCarbonValue(NumberUtil.doubleAccuracy(cityCarbonValue, 2));
                res.setOilElectCarbonValue(NumberUtil.doubleAccuracy(oilCarbonValue, 2));
                res.setGreenElectCarbonValue(NumberUtil.doubleAccuracy(greenCarbonValue, 2));
            }
            case "energyCategory" -> {
                res.setCityElectValue(NumberUtil.doubleAccuracy(cityElectValue, 2));
                res.setOilElectValue(NumberUtil.doubleAccuracy(oilElectValue, 2));
                res.setGreenElectValue(NumberUtil.doubleAccuracy(greenElectValue, 2));
            }
        }

        if(queryType.equals("trend"))
            res.setTime(startTime);
        return res;
    }






    // 获取层级的某种能源类型总用量指标
    public List<StructureOfComplexIndexValue> GetTotalComplexIndexByResourceStructureAndBusinessType(List<ResourceStructure> lstStructure, Integer businessTypeId,String complexType){
        List<StructureOfComplexIndexValue> lstStructureComplexIndex = new ArrayList<>();
        for(ResourceStructure rs : lstStructure){
            ComplexIndex  cIndex;
            if(complexType.equals("total")){
                cIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex().stream().filter(i-> i.getBusinessTypeId().equals(businessTypeId) && i.getObjectId().equals(rs.getResourceStructureId())).findFirst().orElse(null);
                if (cIndex == null) continue;
                StructureOfComplexIndexValue structureComplexIndex = new StructureOfComplexIndexValue();
                structureComplexIndex.setLevelOfPath(rs.getLevelOfPath());
                structureComplexIndex.setResourceStructureId(rs.getResourceStructureId());
                structureComplexIndex.setResourceStructureName(rs.getResourceStructureName());
                structureComplexIndex.setParentResourceStructureId(cIndex.getComplexIndexDefinitionId());
                structureComplexIndex.setComplexIndexId(cIndex.getComplexIndexId());
                structureComplexIndex.setBusinessTypeId(businessTypeId);
                lstStructureComplexIndex.add(structureComplexIndex);
            }
            else{
                List<ComplexIndex> cIndexes = energyComplexIndexManager.GetAllOptionConsumeComplexIndex().stream().filter(i-> i.getBusinessTypeId().equals(businessTypeId) && i.getObjectId().equals(rs.getResourceStructureId())).toList();
                if(cIndexes.isEmpty())
                    continue;
                for (ComplexIndex oneIndex : cIndexes) {
                    StructureOfComplexIndexValue tempStructure = new StructureOfComplexIndexValue();
                    tempStructure.setResourceStructureId(rs.getResourceStructureId());
                    tempStructure.setResourceStructureName(rs.getResourceStructureName());
                    tempStructure.setParentResourceStructureId(oneIndex.getComplexIndexDefinitionId());
                    tempStructure.setComplexIndexId(oneIndex.getComplexIndexId());
                    tempStructure.setBusinessTypeId(businessTypeId);
                    lstStructureComplexIndex.add(tempStructure);
                }
            }

        }
        return lstStructureComplexIndex;
    }



    //下级层级节点
    public List<ResourceStructure> getNextLevel(Integer userId){
        List<ResourceStructure> nextLevel = new ArrayList<>();
        List<ResourceStructure> result = new ArrayList<>();
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        Integer minType = allRoleResourceStructure.stream().map(ResourceStructure::getStructureTypeId).min(Integer::compare).orElse(null);

        if (minType == 101)
            nextLevel = allRoleResourceStructure.stream().filter(i->i.getStructureTypeId().equals(102)).collect(Collectors.toList());
        else if (minType == 102 || minType == 104)
            nextLevel = allRoleResourceStructure.stream().filter(i->i.getStructureTypeId().equals(104)).collect(Collectors.toList());

        return nextLevel;
    }

    // 获取指标列表的influxdb和
    public void GetTotalValueOfComplexIndex(List<StructureOfComplexIndexValue> paraResult, Date startTime, Date endTime, String timeType, boolean isFee){
        String dbname = databaseEnergy;
        String meanTalbeName = "EnergyHisDayData";
        try {
            String selectDuration = "select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

            if (timeType.equals("y") || timeType.equals("m")) {
                selectDuration = "select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";
                meanTalbeName = "EnergyHisMonthData";
            }

            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for(StructureOfComplexIndexValue ciId:paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0){
                    List<String> temComplexIndexIds = new ArrayList<>();
                    List<ComplexIndexFeeQueryResult> resultComplexIndexFeeQuery = new ArrayList<>();
                    String strSelectDuration = selectDuration.replace("$someComplexIndex",bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                            .forDatabase(dbname)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query =  influxDB.query(queryBuilder);
                    if (query != null) {
                        List<EnergyHisDataResult> resultComplexIndexQuery = new ArrayList<>();
                        resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyHisDataResult.class,meanTalbeName);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for(EnergyHisDataResult temp :resultComplexIndexQuery){
                                StructureOfComplexIndexValue st = paraResult.stream().filter(i->i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null){
                                    st.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()),2));
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
            paraResult.removeIf(item->item.getSumValue() == null || item.getSumValue().equals(0d));
        }catch (Exception e){
            log.error("EnergyConfigTelComServiceImpl-GetTotalValueOfComplexIndex error {}", e.getMessage());
        }
    }
    @Override
    public Object getNextLevelRank(String timeType, Date startTime, Date endTime, Integer userId, String queryType) {
        double cityCarbonCoefficient = findCarbonCoefficient(2);
        double oilCarbonCoefficient = findCarbonCoefficient(3);
        double greenCarbonCoefficient = findCarbonCoefficient(4);
        //获取最高层级的下层级点
        List<ResourceStructure> parentLevel = getNextLevel(userId);
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        List<ResourceStructure> filterResourceStructures = new ArrayList<>();
        Integer minType = allRoleResourceStructure.stream().map(ResourceStructure::getStructureTypeId).min(Integer::compare).orElse(null);
        //给下层级点配父子关系
        if(minType == 101){
            for (ResourceStructure oneRes : parentLevel ) {
                List<ResourceStructure> temp = allRoleResourceStructure.stream().filter(i -> i.getStructureTypeId().equals(104) && i.getLevelOfPath().contains("." + oneRes.getResourceStructureId().toString())).toList();
                for (ResourceStructure oneChild :temp ) {
                    oneChild.setLevelOfPath(oneRes.getResourceStructureId().toString());
                }
                filterResourceStructures.addAll(temp);
            }
        }else{
            filterResourceStructures.addAll(parentLevel);
        }
        List<StructureOfComplexIndexValue> structureComplexList  = new ArrayList<>();
        for (int i = 2; i < 5; i++) {
            if(i == 4)
                structureComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures, 4,"total"));
            else
                structureComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures, i,"total"));
        }
        //当前总用电量
        GetTotalValueOfComplexIndex(structureComplexList,startTime,endTime,timeType,false);
        Map<Integer, EnergyConfigTelComRankResultDTO> idToObject = parentLevel.stream()
                .collect(Collectors.toMap(
                        ResourceStructure::getResourceStructureId,
                        EnergyConfigTelComServiceImpl::convertToEnergyConfigTelComRankResultDTO,
                        (existingValue, newValue) -> existingValue
                ));
        for (StructureOfComplexIndexValue oneObject : structureComplexList) {
            double tempValue = oneObject.getSumValue() == null ? 0D : oneObject.getSumValue();
            EnergyConfigTelComRankResultDTO ect = new EnergyConfigTelComRankResultDTO();
            if(minType == 101)
                ect = idToObject.get(Integer.valueOf(oneObject.getLevelOfPath()));
            else
                ect = idToObject.get(oneObject.getResourceStructureId());
            if (oneObject.getBusinessTypeId() == 2){
                ect.setCityElectValue(NumberUtil.doubleAccuracy(tempValue,2));
                ect.setCityElectCarbonValue(NumberUtil.doubleAccuracy((tempValue * cityCarbonCoefficient),2));
            }
            else if (oneObject.getBusinessTypeId() == 3){
                ect.setOilElectValue(NumberUtil.doubleAccuracy(tempValue,2));
                ect.setOilElectCarbonValue(NumberUtil.doubleAccuracy((tempValue * oilCarbonCoefficient),2));
            }
            else{
                ect.setGreenElectValue(NumberUtil.doubleAccuracy(tempValue,2));
                ect.setGreenElectCarbonValue(NumberUtil.doubleAccuracy((tempValue * greenCarbonCoefficient),2));
            }
        }
        List<EnergyConfigTelComRankResultDTO> res = new ArrayList<>(idToObject.values());
        if(queryType.equals("carbon"))
            return res.stream().sorted(Comparator.comparing(EnergyConfigTelComRankResultDTO::getTotalCarbonValue).reversed()).collect(Collectors.toList());
        else
            return res.stream().sorted(Comparator.comparing(EnergyConfigTelComRankResultDTO::getTotalEnergy).reversed()).collect(Collectors.toList());
    }

    @Override
    public Object electCategoryProportion(String timeType, Date startTime, Date endTime, Integer userId, Integer businessTypeId) {
        List<EnergyConfigTelComOptionResultDTO> res = new ArrayList<>();
        List<ComplexIndexDefinition> comDefinitions = complexIndexDefinitionService.getOptionsComplexIndexDefinitionByBusinessTypeId(businessTypeId);
        for (ComplexIndexDefinition oneDefinition :comDefinitions ) {
            EnergyConfigTelComOptionResultDTO temp = new EnergyConfigTelComOptionResultDTO();
            temp.setBusinessId(businessTypeId);
            temp.setDefinitionId(oneDefinition.getComplexIndexDefinitionId());
            temp.setDefinitionName(oneDefinition.getComplexIndexDefinitionName());
            temp.setSumValue(0D);
            res.add(temp);
        }
        List<StructureOfComplexIndexValue> roomComplexList = new ArrayList<>();
        //获取改人员下属的节点
        List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
        List<ResourceStructure> filterResourceStructures = roleResourceStructures.stream().filter(i->i.getStructureTypeId().equals(104)).toList();
        //获取层级结构的市电分项指标

        roomComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures,2,"option"));

        //各分项用电量
        GetTotalValueOfComplexIndex(roomComplexList,startTime,endTime,timeType,false);

        for (StructureOfComplexIndexValue oneObject : roomComplexList) {
            Double tempValue = oneObject.getSumValue() == null ? 0 : oneObject.getSumValue();
            EnergyConfigTelComOptionResultDTO et = res.stream().filter(i->i.getDefinitionId().equals(oneObject.getParentResourceStructureId())).findFirst().orElse(null);
            if(et != null){
                Double tempSumValue = et.getSumValue() == null ? 0 : et.getSumValue();
                et.setSumValue(NumberUtil.doubleAccuracy((tempSumValue + tempValue),2));
            }
        }

        return res;
    }

    private static EnergyConfigTelComRankResultDTO convertToEnergyConfigTelComRankResultDTO(ResourceStructure value) {
        EnergyConfigTelComRankResultDTO dto = new EnergyConfigTelComRankResultDTO();
        dto.setResourceStructureId(value.getResourceStructureId());
        dto.setResourceStructureName(value.getResourceStructureName());
        dto.setTotalEnergy(0D);
        dto.setTotalCarbonValue(0D);
        dto.setOilElectValue(0D);
        dto.setOilElectCarbonValue(0D);
        dto.setCityElectValue(0D);
        dto.setCityElectCarbonValue(0D);
        dto.setGreenElectValue(0D);
        dto.setGreenElectCarbonValue(0D);
        // 其他属性设置省略...
        return dto;
    }
    private static EnergyConfigDynamicRankRes convertToEnergyConfigDynamicRankRes(ResourceStructure value) {
        EnergyConfigDynamicRankRes dto = new EnergyConfigDynamicRankRes();
        dto.setResourceStructureId(value.getResourceStructureId());
        dto.setResourceStructureName(value.getResourceStructureName());
        dto.setTotalValue(0D);
        return dto;
    }

    //***************************************************************  PUE  ************************************************************************************************

    // 某基站PUE
    @Override
    public Double GetPUEOFResourceStructure(Integer resourceStructureId, Date startTime, Date endTime, String timeType) {
        Double result = 0d;

        ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i->i.getObjectId().equals(resourceStructureId) && i.getComplexIndexDefinitionId().equals(5)).findFirst().orElse(null);
        if (complexIndex == null) return result;

        String dbname = databaseEnergy;
        String meanTalbeName = "EnergyHisDayData";
        try {
            String selectDuration = "select mean(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

            if (timeType.equals("y") || timeType.equals("m")) {
                selectDuration = "select mean(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";
                meanTalbeName = "EnergyHisMonthData";
            }
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            List<ComplexIndexFeeQueryResult> resultComplexIndexFeeQuery = new ArrayList<>();
            String strSelectDuration = selectDuration.replace("$someComplexIndex", " ComplexIndexId='" + complexIndex.getComplexIndexId() + "' ");
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                    .forDatabase(dbname)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .create();

            query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisDataResult> resultComplexIndexQuery = new ArrayList<>();
                resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataResult.class,meanTalbeName);
                if (!resultComplexIndexQuery.isEmpty()) {
                    result = NumberUtil.doubleAccuracy( Double.parseDouble(resultComplexIndexQuery.get(0).result),2);
                }
            }

        } catch (Exception e) {
            log.error("EnergyConfigTelComServiceImpl-GetPUEOFResourceStructure error {}", e.getMessage());
        }
        return result;
    }

    // 权限基站汇总PUE
    @Override
    public EnergyUEMaxMinAvgDTO findMaxMInAvgUE(Date startTime, Date endTime, String timeType, Integer userId){
        EnergyUEMaxMinAvgDTO result = new EnergyUEMaxMinAvgDTO();
        List<ResourceStructure> allResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allResourceStructure == null){
            return result;
        }
        allResourceStructure = allResourceStructure.stream().filter(i->i.getStructureTypeId().equals(104)).collect(Collectors.toList());
        List<Integer> lstComplexIndexId = new ArrayList<>();
        for(ResourceStructure rs : allResourceStructure){
            ComplexIndex complexIndex =  energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getObjectId().equals(rs.getResourceStructureId()) && i.getObjectTypeId().equals(rs.getStructureTypeId()) && i.getComplexIndexDefinitionId().equals(5)).findFirst().orElse(null);
            if (complexIndex != null)
                lstComplexIndexId.add(complexIndex.getComplexIndexId());
        }

        return GetMaxMinAvgValueOfComplexIndex(lstComplexIndexId, startTime, endTime, timeType);
    }

    // PUE 获取UE最大 最小 平均值 查询数据库返回
    public EnergyUEMaxMinAvgDTO GetMaxMinAvgValueOfComplexIndex(List<Integer> paraResult, Date startTime, Date endTime, String timeType){
        EnergyUEMaxMinAvgDTO result = new EnergyUEMaxMinAvgDTO();
        List<EnergyUEMaxMinAvgDTO> lstResult = new ArrayList<>();
        String dbname = databaseEnergy;
        String meanTalbeName = "EnergyHisDayData";
        try {
            String selectDuration = "select max(IndexValue) as maxValue, min(IndexValue) as minValue, mean(IndexValue) as avgValue, count(IndexValue) as countNum from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) ";

            if (timeType.equals("y") || timeType.equals("m")) {
                selectDuration = "select max(IndexValue) as maxValue, min(IndexValue) as minValue, mean(IndexValue) as avgValue, count(IndexValue) as countNum  from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) ";
                meanTalbeName = "EnergyHisMonthData";
            } else if (timeType.equals("h")){
                selectDuration = "select max(IndexValue) as maxValue, min(IndexValue) as minValue, mean(IndexValue) as avgValue, count(IndexValue) as countNum  from EnergyHisHourData where time >=$startTime and time <=$endTime and ($someComplexIndex) ";
                meanTalbeName = "EnergyHisHourData";
            }
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for(Integer ciId:paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId+"' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId+"' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0){
                    List<String> temComplexIndexIds = new ArrayList<>();
                    List<ComplexIndexFeeQueryResult> resultComplexIndexFeeQuery = new ArrayList<>();
                    String strSelectDuration = selectDuration.replace("$someComplexIndex",bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                            .forDatabase(dbname)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query =  influxDB.query(queryBuilder);
                    if (query != null) {
                        List<EnergyUEMaxMinAvgDTO> resultComplexIndexQuery = new ArrayList<>();
                        resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyUEMaxMinAvgDTO.class,meanTalbeName);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            lstResult.add(resultComplexIndexQuery.get(0));
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }

            double avgValueSum = 0d;
            double minValueResult = 0d;
            double maxValueResult = 0d;
            double countNum = 0d;

            for (EnergyUEMaxMinAvgDTO dto: lstResult){
                if (dto.getMinValue() != null && dto.getMinValue() != ""){
                    if (minValueResult == 0d) {
                        minValueResult =  Double.parseDouble(dto.getMinValue());
                    } else if (minValueResult > Double.parseDouble(dto.getMinValue())) {
                        minValueResult = Double.parseDouble(dto.getMinValue());
                    }
                }
                if (dto.getMaxValue() != null && dto.getMaxValue() != ""){
                    if (maxValueResult == 0d) {
                        maxValueResult =  Double.parseDouble(dto.getMaxValue());
                    } else if (maxValueResult < Double.parseDouble(dto.getMaxValue())) {
                        maxValueResult = Double.parseDouble(dto.getMaxValue());
                    }
                }
                avgValueSum += (dto.getAvgValue()==null || dto.getAvgValue() == "" ||
                                dto.getCountNum()==null || dto.getCountNum() == "" ?
                               0: Double.valueOf(dto.getAvgValue())*Double.valueOf(dto.getCountNum()));
                countNum += (dto.getCountNum()==null || dto.getCountNum() == "" ? 0: Double.valueOf(dto.getCountNum()));
            }

            if (countNum == 0d){
                result.setAvgValue("0");
                result.setMinValue("0");
                result.setMaxValue("0");
                result.setCountNum("0");
            } else {
                result.setMinValue(NumberUtil.doubleAccuracy(minValueResult,2).toString());
                result.setMaxValue(NumberUtil.doubleAccuracy(maxValueResult,2).toString());
                result.setCountNum(String.valueOf(countNum));
                result.setAvgValue(NumberUtil.doubleAccuracy(avgValueSum/countNum,2).toString());
            }
            result.setTime((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(startTime));
            return result;
        } catch (Exception e) {
            log.error("EnergyConfigTelComServiceImpl-GetMaxMinAvgValueOfComplexIndex error {}", e.getMessage());
        }
        return result;
    }

    // 权限基站汇总PUE趨勢
    @Override
    public List<EnergyUEMaxMinAvgDTO> findMaxMInAvgUETrend(Date startTime, Date endTime, String timeType, Integer userId){
        List<EnergyUEMaxMinAvgDTO> result = new ArrayList<>();
        List<ResourceStructure> allResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);

        if (allResourceStructure == null){
            return result;
        }
        allResourceStructure = allResourceStructure.stream().filter(i->i.getStructureTypeId().equals(104)).collect(Collectors.toList());
        List<Integer> lstComplexIndexId = new ArrayList<>();
        for(ResourceStructure rs : allResourceStructure){
            ComplexIndex complexIndex =  energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getObjectId().equals(rs.getResourceStructureId()) && i.getObjectTypeId().equals(rs.getStructureTypeId()) && i.getComplexIndexDefinitionId().equals(5)).findFirst().orElse(null);
            if (complexIndex != null)
                lstComplexIndexId.add(complexIndex.getComplexIndexId());
        }

        int count = 23;
        String trendTimeType = "d";
        switch (timeType){
            case "d" :
                count = 23;
                trendTimeType = "h";
                break;
            case "m":
                count = YearMonth.from(LocalDate.now()).lengthOfMonth()-1;
                trendTimeType = "d";
                break;
            case "y":
                count = 11;
                trendTimeType = "m";
                break;
        }

        for(int i=0;i<=count;i++){
            endTime = GetEndTimeByStartTimeAndTimeType(startTime,timeType);
            result.add(GetMaxMinAvgValueOfComplexIndex(lstComplexIndexId, startTime, endTime, trendTimeType));
            startTime = AddOneStepByTimeType(startTime,timeType);
        }

        return result;
    }

    private Date AddOneStepByTimeType(Date time, String timeType){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        switch (timeType){
            case "d" :
                calendar.add(Calendar.HOUR_OF_DAY, 1);
                break;
            case "m":
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                break;
            case "y":
                calendar.add(Calendar.MONTH, 1);
                break;
        }
        return  calendar.getTime();
    }
    private Date GetEndTimeByStartTimeAndTimeType(Date time, String timeType){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        switch (timeType){
            case "d" :
                calendar.add(Calendar.HOUR_OF_DAY, 1);
                break;
            case "m":
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                break;
            case "y":
                calendar.add(Calendar.MONTH, 1);
                break;
        }
        calendar.add(Calendar.SECOND,-1);
        return calendar.getTime();
    }



    //----------------------------------------电信场景动态获取能源类型计算用能及碳排放数据----------------------------------------

    /**
     * 各用能类型总量
     */
    @Override
    public List<EnergyConfigDynamicEnergyAndRatioRes> everyEnergyAndRatioUseOfRole(String timeType, Date startTime, Date endTime, Integer userId, String queryType) {
        List<EnergyConfigDynamicEnergyAndRatioRes> res = new ArrayList<>();
        try {
            List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
            //筛选出基站的节点
            List<ResourceStructure> filterResourceStructures = roleResourceStructures.stream().filter(i -> i.getStructureTypeId().equals(104)).toList();
            List<StructureOfComplexIndexValue> structureComplexList = new ArrayList<>();

            //获取所有能源类型
            List<ComplexIndexBusinessType> complexIndexBusinessTypes = energyComplexIndexManager.getAllComplexIndexBusinessType();
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                structureComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures, oneType.getBusinessTypeId(), "total"));
            }
            List<StructureOfComplexIndexValue> yoyStructureComplexList = structureComplexList.stream()
                    .map(StructureOfComplexIndexValue::clone)
                    .collect(Collectors.toList());
            List<StructureOfComplexIndexValue> qoqStructureComplexList = structureComplexList.stream()
                    .map(StructureOfComplexIndexValue::clone)
                    .collect(Collectors.toList());

            //当前总用电量
            GetTotalValueOfComplexIndex(structureComplexList, startTime, endTime, timeType, false);
            //将获取的指标按能源类型进行分类
            Map<Integer, List<StructureOfComplexIndexValue>> groupedByType = structureComplexList.stream()
                    .collect(Collectors.groupingBy(StructureOfComplexIndexValue::getBusinessTypeId));

            //同比总用电量
            List<Date> YoYDate = getQoQDateOrYoYDate(startTime, endTime, "YoY", timeType);
            GetTotalValueOfComplexIndex(yoyStructureComplexList, YoYDate.get(0), YoYDate.get(1), timeType, false);
            Map<Integer, List<StructureOfComplexIndexValue>> yoyGroupedByType = yoyStructureComplexList.stream()
                    .collect(Collectors.groupingBy(StructureOfComplexIndexValue::getBusinessTypeId));
            //环比总用电量
            List<Date> QoQDate = getQoQDateOrYoYDate(startTime, endTime, "QoQ", timeType);
            GetTotalValueOfComplexIndex(qoqStructureComplexList, QoQDate.get(0), QoQDate.get(1), timeType, false);
            Map<Integer, List<StructureOfComplexIndexValue>> qoqGroupedByType = qoqStructureComplexList.stream()
                    .collect(Collectors.groupingBy(StructureOfComplexIndexValue::getBusinessTypeId));

            //获取各用能类型的转碳系数
            Map<Integer, Double> typeCarbonCoefficientMap = new HashMap<>();
            if (queryType.equals("carbon")) {
                typeCarbonCoefficientMap = getBusinessTypeCoefficient("all");
            }
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                Integer businessTypeId = oneType.getBusinessTypeId();
                double sum = 0D, yoySum = 0D, qoqSum = 0D;
                if (groupedByType.get(businessTypeId) != null) {
                    sum = groupedByType.get(businessTypeId).stream()
                            .mapToDouble(StructureOfComplexIndexValue::getSumValue)
                            .sum();
                }
                if (yoyGroupedByType.get(businessTypeId) != null) {
                    yoySum = yoyGroupedByType.get(businessTypeId).stream()
                            .mapToDouble(StructureOfComplexIndexValue::getSumValue)
                            .sum();
                }
                if (qoqGroupedByType.get(businessTypeId) != null) {
                    qoqSum = qoqGroupedByType.get(businessTypeId).stream()
                            .mapToDouble(StructureOfComplexIndexValue::getSumValue)
                            .sum();
                }

                EnergyConfigDynamicEnergyAndRatioRes temp = new EnergyConfigDynamicEnergyAndRatioRes();
                temp.setTotalValue(NumberUtil.doubleAccuracy(sum, 2));
                temp.setBusinessTypeName(oneType.getBusinessTypeName());
                temp.setBusinessTypeId(oneType.getBusinessTypeId());
                temp.setYoyValue(NumberUtil.doubleAccuracy(yoySum, 2));
                temp.setQoqValue(NumberUtil.doubleAccuracy(qoqSum, 2));
                if (queryType.equals("carbon")) {
                    double carbonCoefficient = typeCarbonCoefficientMap.get(oneType.getBusinessTypeId()) == null ? -0D : typeCarbonCoefficientMap.get(oneType.getBusinessTypeId());
                    temp.setCarbonValue(NumberUtil.doubleAccuracy((sum * carbonCoefficient), 2));
                }
                res.add(temp);
            }
        } catch (Exception ex) {
            log.error("EnergyConfigTelComServiceImpl-everyEnergyAndRatioUseOfRole error ", ex);
            return new ArrayList<>();
        }
        return res;
    }

    /**
     * 总用电量及同环比&&碳排放及同环比
     */
    @Override
    public EnergyConfigDynamicEnergyTotalAndRatioRes totalEnergyAndRatioUseOfRole(String timeType, Date startTime, Date endTime, Integer userId, String queryType) {
        EnergyConfigDynamicEnergyTotalAndRatioRes res = new EnergyConfigDynamicEnergyTotalAndRatioRes();
        try {
            //获取各用能类型总量
            List<EnergyConfigDynamicEnergyAndRatioRes> everyEnergyValue = everyEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId, "energy");
            double totalValue = 0D, yoyValue = 0D, qoqValue = 0D;
            double carbonTotalValue = 0D, yoyCarbonTotalValue = 0D, qoqCarbonTotalValue = 0D;
            //获取各用能类型的转碳系数
            Map<Integer, Double> typeCarbonCoefficientMap = new HashMap<>();
            if (queryType.equals("carbon")) {
                typeCarbonCoefficientMap = getBusinessTypeCoefficient("notGreen");
            }
            for (EnergyConfigDynamicEnergyAndRatioRes oneEnergy : everyEnergyValue) {
                totalValue += oneEnergy.getTotalValue();
                yoyValue += oneEnergy.getYoyValue();
                qoqValue += oneEnergy.getQoqValue();
                if (queryType.equals("carbon")) {
                    if (typeCarbonCoefficientMap.get(oneEnergy.getBusinessTypeId()) == null)
                        continue;
                    double carbonCoefficient = typeCarbonCoefficientMap.get(oneEnergy.getBusinessTypeId());
                    carbonTotalValue += (oneEnergy.getTotalValue() * carbonCoefficient);
                    yoyCarbonTotalValue += (oneEnergy.getYoyValue() * carbonCoefficient);
                    qoqCarbonTotalValue += (oneEnergy.getQoqValue() * carbonCoefficient);
                }
            }

            res.setTotalValue(NumberUtil.doubleAccuracy(totalValue, 2));
            res.setYoyTotalValue(NumberUtil.doubleAccuracy(yoyValue, 2));
            res.setQoqTotalValue(NumberUtil.doubleAccuracy(qoqValue, 2));
            if (queryType.equals("carbon")) {
                res.setCarbonValue(NumberUtil.doubleAccuracy(carbonTotalValue, 2));
                res.setYoyCarbonValue(NumberUtil.doubleAccuracy(yoyCarbonTotalValue, 2));
                res.setQoqCarbonValue(NumberUtil.doubleAccuracy(qoqCarbonTotalValue, 2));
            }

        } catch (Exception ex) {
            log.error("EnergyConfigTelComServiceImpl-totalEnergyAndRatioUseOfRole error ", ex);
            return null;
        }
        return res;
    }

    /**
     * 碳抵消及同环比
     */
    @Override
    public EnergyConfigDynamicEnergyTotalAndRatioRes greenCarbonOfRole(String timeType, Date startTime, Date endTime, Integer userId) {
        EnergyConfigDynamicEnergyTotalAndRatioRes res = new EnergyConfigDynamicEnergyTotalAndRatioRes();
        try {
            //获取各用能类型总量
            List<EnergyConfigDynamicEnergyAndRatioRes> everyEnergyValue = everyEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId, "energy");
            double carbonTotalValue = 0D, yoyCarbonTotalValue = 0D, qoqCarbonTotalValue = 0D;
            double totalValue = 0D, yoyValue = 0D, qoqValue = 0D;

            //获取各用能类型的转碳系数
            Map<Integer, Double> typeCarbonCoefficientMap = getBusinessTypeCoefficient("green");

            for (EnergyConfigDynamicEnergyAndRatioRes oneEnergy : everyEnergyValue) {
                if (typeCarbonCoefficientMap.get(oneEnergy.getBusinessTypeId()) == null)
                    continue;
                totalValue += oneEnergy.getTotalValue();
                yoyValue += oneEnergy.getYoyValue();
                qoqValue += oneEnergy.getQoqValue();

                double carbonCoefficient = typeCarbonCoefficientMap.get(oneEnergy.getBusinessTypeId());
                carbonTotalValue += (totalValue * carbonCoefficient);
                yoyCarbonTotalValue += (yoyValue * carbonCoefficient);
                qoqCarbonTotalValue += (qoqValue * carbonCoefficient);
            }
            res.setCarbonValue(carbonTotalValue);
            res.setYoyCarbonValue(yoyCarbonTotalValue);
            res.setQoqCarbonValue(qoqCarbonTotalValue);

            return res;
        }catch (Exception ex){
            log.error("EnergyConfigTelComServiceImpl-greenCarbonOfRole error ", ex);
            return res;
        }
    }

    /**
     * 某基站用碳及用能总量
     */
    @Override
    public Object dynamicGetRoomEnergyAndCarbon(String timeType, Date startTime, Date endTime, Integer resourceStructureId, Integer userId) {

        try {
            ResourceStructure oneRoom;
            List<ResourceStructure> roomList = new ArrayList<>();
            List<StructureOfComplexIndexValue> roomComplexList = new ArrayList<>();
            if (userId != null) {
                List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
                //筛选出基站的节点
                List<ResourceStructure> filterResourceStructures = roleResourceStructures.stream().filter(i -> i.getStructureTypeId().equals(104)).toList();
                roomList.addAll(filterResourceStructures);
            } else {
                oneRoom = resourceStructureService.findById(resourceStructureId);
                roomList.add(oneRoom);
            }

            //获取所有能源类型
            List<ComplexIndexBusinessType> complexIndexBusinessTypes = energyComplexIndexManager.getAllComplexIndexBusinessType();
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                roomComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(roomList, oneType.getBusinessTypeId(), "total"));
            }

            //当前总用电量
            GetTotalValueOfComplexIndex(roomComplexList, startTime, endTime, timeType, false);
            Map<Integer, List<StructureOfComplexIndexValue>> groupedByType = roomComplexList.stream()
                    .collect(Collectors.groupingBy(StructureOfComplexIndexValue::getBusinessTypeId));

            //获取各用能类型的转碳系数
            Map<Integer, Double> typeCarbonCoefficientMap = getBusinessTypeCoefficient("all");
            double totalValue = 0D, carbonValue = 0D;

            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                Integer businessTypeId = oneType.getBusinessTypeId();
                if (groupedByType.get(businessTypeId) == null)
                    continue;
                double sum = groupedByType.get(businessTypeId).stream()
                        .mapToDouble(StructureOfComplexIndexValue::getSumValue)
                        .sum();
                double carbonCoefficient = typeCarbonCoefficientMap.get(oneType.getBusinessTypeId()) == null ? 0d : typeCarbonCoefficientMap.get(oneType.getBusinessTypeId());
                totalValue += sum;
                carbonValue += (sum * carbonCoefficient);
            }
            Map<String, Double> res = new HashMap<>();
            res.put("totalValue", NumberUtil.doubleAccuracy(totalValue, 2));
            res.put("carbonValue", NumberUtil.doubleAccuracy(carbonValue, 2));
            return res;
        }catch (Exception ex){
            log.error("EnergyConfigTelComServiceImpl-dynamicGetRoomEnergyAndCarbon error ", ex);
            return  new ArrayList<>();
        }
    }

    /**
     * 各用能类型趋势
     */
    public EnergyConfigDynamicTrendRes dynamicEnergyAndCarbonTrend(String timeType, Date startTime, Date endTime, Integer resourceStructureId, Integer userId, String queryType) {
        EnergyConfigDynamicTrendRes res = new EnergyConfigDynamicTrendRes();
        try {
            ResourceStructure oneRoom;
            List<ResourceStructure> roomList = new ArrayList<>();
            List<StructureOfComplexIndexValue> roomComplexList = new ArrayList<>();
            if (userId != null) {
                List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
                //筛选出基站的节点
                List<ResourceStructure> filterResourceStructures = roleResourceStructures.stream().filter(i -> i.getStructureTypeId().equals(104)).toList();
                roomList.addAll(filterResourceStructures);
            } else {
                oneRoom = resourceStructureService.findById(resourceStructureId);
                roomList.add(oneRoom);
            }

            //获取所有能源类型
            List<ComplexIndexBusinessType> complexIndexBusinessTypes = energyComplexIndexManager.getAllComplexIndexBusinessType();
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                roomComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(roomList, oneType.getBusinessTypeId(), "total"));
            }

            //当前总用电量
            GetTotalValueOfComplexIndex(roomComplexList, startTime, endTime, timeType, false);
            Map<Integer, List<StructureOfComplexIndexValue>> groupedByType = roomComplexList.stream()
                    .collect(Collectors.groupingBy(StructureOfComplexIndexValue::getBusinessTypeId));

            //获取各用能类型的转碳系数
            Map<Integer, Double> typeCarbonCoefficientMap = getBusinessTypeCoefficient("all");

            int i = 1;
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                Integer businessTypeId = oneType.getBusinessTypeId();
                double sum = 0D, carbonValue = 0D, valueSet = 0D;
                if (groupedByType.get(businessTypeId) != null) {
                    sum = groupedByType.get(businessTypeId).stream()
                            .mapToDouble(StructureOfComplexIndexValue::getSumValue)
                            .sum();
                    valueSet = sum;
                    if (queryType.equals("carbon")) {
                        double carbonCoefficient = typeCarbonCoefficientMap.get(oneType.getBusinessTypeId()) == null ? 0d : typeCarbonCoefficientMap.get(oneType.getBusinessTypeId());
                        carbonValue = (sum * carbonCoefficient);
                        valueSet = carbonValue;
                    }
                    switch (i) {
                        case 1 -> res.setTotalValue1(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 2 -> res.setTotalValue2(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 3 -> res.setTotalValue3(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 4 -> res.setTotalValue4(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 5 -> res.setTotalValue5(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 6 -> res.setTotalValue6(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 7 -> res.setTotalValue7(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 8 -> res.setTotalValue8(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 9 -> res.setTotalValue9(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 10 -> res.setTotalValue10(NumberUtil.doubleAccuracy(valueSet, 2));
                    }
                }
                i++;
            }
            res.setTime(startTime);
            return res;
        }catch (Exception ex){
            log.error("EnergyConfigTelComServiceImpl-dynamicEnergyAndCarbonTrend error ", ex);
            return res ;
        }
    }

    @Override
    public Object dynamicGetEnergyAndCarbonTrend(String timeType, Date startTime, Date endTime, Integer userId, String queryType) {
        List<EnergyConfigDynamicTrendRes> res = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            res.add(dynamicEnergyAndCarbonTrend(timeType, startTime, endTime, null, userId, queryType));
            startTime = AddOneStepByTimeType(startTime, timeType);
            endTime = GetEndTimeByStartTimeAndTimeType(startTime, timeType);
        }
        return res;
    }

    @Override
    public Object dynamicGetNextLevelRank(String timeType, Date startTime, Date endTime, Integer userId, String queryType) {
        try {
            //获取最高层级的下层级点
            List<ResourceStructure> parentLevel = getNextLevel(userId);
            List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
            List<ResourceStructure> filterResourceStructures = new ArrayList<>();
            Integer minType = allRoleResourceStructure.stream().map(ResourceStructure::getStructureTypeId).min(Integer::compare).orElse(null);
            //给下层级点配父子关系
            if (minType != null && (minType == 101 || minType == 1)) {
                for (ResourceStructure oneRes : parentLevel) {
                    List<ResourceStructure> temp = allRoleResourceStructure.stream().filter(i -> i.getStructureTypeId().equals(104) && i.getLevelOfPath().contains("." + oneRes.getResourceStructureId().toString())).toList();
                    for (ResourceStructure oneChild : temp) {
                        oneChild.setLevelOfPath(oneRes.getResourceStructureId().toString());
                    }
                    filterResourceStructures.addAll(temp);
                }
            } else {
                filterResourceStructures.addAll(parentLevel);
            }
            List<StructureOfComplexIndexValue> structureComplexList = new ArrayList<>();

            //获取所有能源类型
            List<ComplexIndexBusinessType> complexIndexBusinessTypes = energyComplexIndexManager.getAllComplexIndexBusinessType();
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                structureComplexList.addAll(GetTotalComplexIndexByResourceStructureAndBusinessType(filterResourceStructures, oneType.getBusinessTypeId(), "total"));
            }
            //获取各用能类型的转碳系数
            Map<Integer, Double> typeCarbonCoefficientMap = getBusinessTypeCoefficient("all");

            //当前总用电量
            GetTotalValueOfComplexIndex(structureComplexList, startTime, endTime, timeType, false);
            Map<Integer, EnergyConfigDynamicRankRes> idToObject = parentLevel.stream()
                    .collect(Collectors.toMap(
                            ResourceStructure::getResourceStructureId,
                            EnergyConfigTelComServiceImpl::convertToEnergyConfigDynamicRankRes,
                            (existingValue, newValue) -> existingValue
                    ));

            Map<Integer, List<StructureOfComplexIndexValue>> groupedByType = structureComplexList.stream()
                    .collect(Collectors.groupingBy(StructureOfComplexIndexValue::getBusinessTypeId));

            int i = 1;
            for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {
                List<StructureOfComplexIndexValue> tempStructureComplexList = groupedByType.get(oneType.getBusinessTypeId());
                if (tempStructureComplexList == null) {
                    i++;
                    continue;
                }
                double carbonCoefficient = typeCarbonCoefficientMap.get(oneType.getBusinessTypeId()) == null ? 0d : typeCarbonCoefficientMap.get(oneType.getBusinessTypeId());
                for (StructureOfComplexIndexValue oneObject : tempStructureComplexList) {
                    double tempValue = oneObject.getSumValue() == null ? 0D : oneObject.getSumValue();
                    EnergyConfigDynamicRankRes ect = new EnergyConfigDynamicRankRes();
                    if (minType == 101)
                        ect = idToObject.get(Integer.valueOf(oneObject.getLevelOfPath()));
                    else
                        ect = idToObject.get(oneObject.getResourceStructureId());
                    double valueSet = tempValue;
                    if (queryType.equals("carbon")) {
                        valueSet = tempValue * carbonCoefficient;
                    }
                    switch (i) {
                        case 1 -> ect.setValue1(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 2 -> ect.setValue2(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 3 -> ect.setValue3(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 4 -> ect.setValue4(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 5 -> ect.setValue5(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 6 -> ect.setValue6(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 7 -> ect.setValue7(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 8 -> ect.setValue8(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 9 -> ect.setValue9(NumberUtil.doubleAccuracy(valueSet, 2));
                        case 10 -> ect.setValue10(NumberUtil.doubleAccuracy(valueSet, 2));
                    }
                }
                i++;
            }
            List<EnergyConfigDynamicRankRes> res = new ArrayList<>(idToObject.values());

            return res.stream().sorted(Comparator.comparing(EnergyConfigDynamicRankRes::getTotalValue).reversed()).collect(Collectors.toList());
        } catch (Exception ex) {
            log.error("EnergyConfigTelComServiceImpl-dynamicGetNextLevelRank error ", ex);
            return new ArrayList<>();
        }
    }
    //碳排放及碳抵消占比
    @Override
    public EnergyConfigDynamicGreenOtherRatio greenOrOtherRatio(String timeType, Date startTime, Date endTime, Integer userId) {
        EnergyConfigDynamicEnergyTotalAndRatioRes greenValue = greenCarbonOfRole(timeType, startTime, endTime, userId);
        EnergyConfigDynamicEnergyTotalAndRatioRes otherValue = totalEnergyAndRatioUseOfRole(timeType, startTime, endTime, userId,"carbon");
        EnergyConfigDynamicGreenOtherRatio res = new EnergyConfigDynamicGreenOtherRatio();
        Double greenCarbon = greenValue.getCarbonValue() == null ? 0D : greenValue.getCarbonValue();
        res.setGreenCarbonValue(NumberUtil.doubleAccuracy(greenCarbon,2));
        Double otherCarbon = otherValue.getCarbonValue() == null ? 0D : otherValue.getCarbonValue();
        res.setOtherCarbonValue(NumberUtil.doubleAccuracy(otherCarbon,2));
        double ratio = 0D;
        if((greenCarbon + otherCarbon) != 0)
            ratio = greenCarbon/(greenCarbon + otherCarbon);
        res.setRatioValue(NumberUtil.doubleAccuracy(ratio,2));
        return res;
    }


    /**
     * 获取能源转碳系数
     */
    public Map<Integer, Double> getBusinessTypeCoefficient(String energyType) {
        Map<Integer, Double> typeCarbonCoefficientMap = new HashMap<>();
        List<ComplexIndexBusinessType> complexIndexBusinessTypes = energyComplexIndexManager.getAllComplexIndexBusinessType();
        //获取各非清洁能源的转碳系数
        for (ComplexIndexBusinessType oneType : complexIndexBusinessTypes) {

            if (energyType.equals("green")) {
                if (Integer.parseInt(oneType.getDescription()) < 100)
                    continue;
            }
            if (energyType.equals("notGreen")) {
                if (Integer.parseInt(oneType.getDescription()) > 100)
                    continue;
            }

            typeCarbonCoefficientMap.put(oneType.getBusinessTypeId(), findCarbonCoefficient(oneType.getBusinessTypeId()));
        }
        return typeCarbonCoefficientMap;
    }
}