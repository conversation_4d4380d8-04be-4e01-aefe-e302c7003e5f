package com.siteweb.energy.service.impl;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.service.EnergySichuanEnergyInvestReportService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.service.SignalService;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
@Slf4j
public class EnergySichuanEnergyInvestReportServiceImpl implements EnergySichuanEnergyInvestReportService {
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private SignalService signalService;
    @Value("${spring.influx.database}")
    private String database;
    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private ConfigSignalManager configSignalManager;

    @Override
    public List<ResourceStructure> getBuildingList() {
        return resourceStructureService.findResourceStructureByUserId(TokenUserUtil.getLoginUserId()).stream().filter(o -> o.getStructureTypeId().equals(104) || o.getStructureTypeId().equals(4)).toList();
    }

    @Override
    public List<ResourceStructure> getRoomList(Integer buildingId) {
        List<ResourceStructure> roleRoomList = resourceStructureService.findResourceStructureByUserId(TokenUserUtil.getLoginUserId()).stream().filter(o -> o.getStructureTypeId().equals(105) || o.getStructureTypeId().equals(5)).toList();
        return roleRoomList.stream().filter(o -> o.getParentResourceStructureId().equals(buildingId)).toList();
    }

    @Override
    public List<Equipment> getEquipmentList(Integer resourceStructureId) {
        List<Equipment> res = new ArrayList<>(equipmentManager.findEquipmentsByResourceStructureId(resourceStructureId).stream().filter(o -> o.getEquipmentCategory().equals(12) && o.getEquipmentBaseType().equals(1703)).toList());
        res.sort(Comparator.comparing(Equipment::getEquipmentName));
        return res;
    }

    @Override
    public List<ConfigSignalItem> getEquipmentSignalList(Integer equipmentId) {
        return signalService.findEquipmentId(equipmentId).stream().filter(o -> o.getSignalCategory().equals(1)).toList();
    }

    /**
     * 差值：下个周期减去上个周期的值为这个周期的差值
     * 如果是年查询，查询12个月的月差值
     * 如果是月查询，查询月的天数的天差值
     * 查询时间约定:
     * 年查询:传入当年的第一天(2024-01-01 00:00:00)
     * 月查询:传入当月的第一天(2024-06-01 00:00:00)
     */
    /**
     * 获取信号差值列表(年查询)
     * @param signalList 设备ID.信号ID拼的数组
     * @param queryTime 查询时间
     */
    @Override
    public List<SichuanEnergyInvestSignalDIffYearResult> getSignalDifferenceYearReport(List<String> signalList, Date queryTime) {
        if(signalList.isEmpty() || queryTime == null)
            return null;
        List<SichuanEnergyInvestSignalDIffYearResult> res = new ArrayList<>();

        Map<String, SichuanEnergyInvestSignalDIffYearResult> signalMap = new HashMap<>();
        //填充返回结果
        for (String oneSignal : signalList) {
            int dotIndex = oneSignal.indexOf('.');
            String deviceId = oneSignal.substring(0, dotIndex);
            String signalId = oneSignal.substring(dotIndex + 1);

            Equipment equipment = equipmentManager.getEquipmentById(Integer.valueOf(deviceId));
            if (equipment == null)
                continue;
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipment.getResourceStructureId());
            if (resourceStructure == null)
                continue;
            ResourceStructure parentResource = resourceStructureManager.getResourceStructureById(resourceStructure.getParentResourceStructureId());

            ConfigSignalItem configSignal = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(Integer.parseInt(deviceId), Integer.parseInt(signalId));

            SichuanEnergyInvestSignalDIffYearResult temp = new SichuanEnergyInvestSignalDIffYearResult();
            temp.setEquipmentId(equipment.getEquipmentId());
            temp.setEquipmentName(equipment.getEquipmentName());
            temp.setResourceStructureId(resourceStructure.getResourceStructureId());
            temp.setResourceStructureName(resourceStructure.getResourceStructureName());
            temp.setParentResourceStructureId(parentResource.getResourceStructureId());
            temp.setParentResourceStructureName(parentResource.getResourceStructureName());
            temp.setSignalId(configSignal.getSignalId());
            temp.setSignalName(configSignal.getSignalName());
            temp.setSignalKey(oneSignal);
            res.add(temp);
            signalMap.put(oneSignal,temp);
        }

        Date startTime = DateUtil.getFirstDayOfYear(queryTime);
        Date endTime = DateUtil.getMonthEndTime(startTime);

        for (int i = 1; i <= 12; i++) {
            if (startTime.before(new Date())) {
                //查询InfluxDB
                getYearDataFromInflux(false, startTime, endTime, res, signalMap);
            }
            startTime = DateUtil.getNextMonthFirstDay(startTime);
            endTime = DateUtil.getMonthEndTime(startTime);
        }

        if (startTime.before(new Date())) {
            //获取查询时间之后一个周期的数据
            getYearDataFromInflux(true, startTime, endTime, res, signalMap);
        }

        return res;
    }

    /**
     * 从库中获取年区间历史数据
     */
    private void getYearDataFromInflux(Boolean isAfter, Date startTime, Date endTime, List<SichuanEnergyInvestSignalDIffYearResult> resList, Map<String, SichuanEnergyInvestSignalDIffYearResult> signalMap) {
        try {
            String selectDuration = "SELECT * FROM historydatas WHERE time >=$startTime and time <= $endTime and ( $someSignalId )  group by SignalId  order by time asc limit 1";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereSignalId = new StringBuilder();
            int count = 0;
            for (SichuanEnergyInvestSignalDIffYearResult oneRes : resList) {
                if (bsWhereSignalId.length() == 0) {
                    bsWhereSignalId.append(" SignalId='").append(oneRes.getSignalKey()).append("' ");
                } else {
                    bsWhereSignalId.append(" or SignalId='").append(oneRes.getSignalKey()).append("' ");
                }
                count++;
                if (count == resList.size() || count % 500 == 0) {
                    List<EnergyHisDataSichuanResult> resultComplexIndexQuery = new ArrayList<>();
                    selectDuration = selectDuration.replace("$someSignalId", bsWhereSignalId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                            .forDatabase(database)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataSichuanResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for (EnergyHisDataSichuanResult temp : resultComplexIndexQuery) {
                                SichuanEnergyInvestSignalDIffYearResult st = signalMap.get(temp.getSignalId());
                                if (st != null) {
                                    if (temp.getPointValue() == null) continue;
                                    Double valueTemp = NumberUtil.doubleAccuracy(Double.parseDouble(temp.getPointValue()), 2);
                                    if (isAfter) {
                                        st.setOriginValue13(valueTemp);
                                        continue;
                                    }
                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime(endTime);
                                    switch (ca.get(Calendar.MONTH) + 1) {
                                        case 1 -> st.setOriginValue1(valueTemp);
                                        case 2 -> st.setOriginValue2(valueTemp);
                                        case 3 -> st.setOriginValue3(valueTemp);
                                        case 4 -> st.setOriginValue4(valueTemp);
                                        case 5 -> st.setOriginValue5(valueTemp);
                                        case 6 -> st.setOriginValue6(valueTemp);
                                        case 7 -> st.setOriginValue7(valueTemp);
                                        case 8 -> st.setOriginValue8(valueTemp);
                                        case 9 -> st.setOriginValue9(valueTemp);
                                        case 10 -> st.setOriginValue10(valueTemp);
                                        case 11 -> st.setOriginValue11(valueTemp);
                                        case 12 -> st.setOriginValue12(valueTemp);
                                    }
                                }
                            }
                        }
                    }
                    bsWhereSignalId = new StringBuilder();
                }
            }
        } catch (Exception ex) {
            log.error("EnergySichuanEnergyInvestReportServiceImpl-getYearDataFromInflux error ", ex);
        }
    }

    /**
     * 获取信号差值列表(月查询)
     * @param signalList 设备ID.信号ID拼的数组
     * @param queryTime 查询时间
     */
    @Override
    public List<SichuanEnergyInvestSignalDIffMonthResult> getSignalDifferenceMonthReport(List<String> signalList, Date queryTime) {
        if(signalList.isEmpty() || queryTime == null)
            return null;
        List<SichuanEnergyInvestSignalDIffMonthResult> res = new ArrayList<>();
        Map<String, SichuanEnergyInvestSignalDIffMonthResult> signalMap = new HashMap<>();
        //填充返回结果
        for (String oneSignal : signalList) {
            int dotIndex = oneSignal.indexOf('.');
            String deviceId = oneSignal.substring(0, dotIndex);
            String signalId = oneSignal.substring(dotIndex + 1);

            Equipment equipment = equipmentManager.getEquipmentById(Integer.valueOf(deviceId));
            if (equipment == null)
                continue;
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipment.getResourceStructureId());
            if (resourceStructure == null)
                continue;
            ResourceStructure parentResource = resourceStructureManager.getResourceStructureById(resourceStructure.getParentResourceStructureId());

            ConfigSignalItem configSignal = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(Integer.parseInt(deviceId), Integer.parseInt(signalId));

            SichuanEnergyInvestSignalDIffMonthResult temp = new SichuanEnergyInvestSignalDIffMonthResult();
            temp.setEquipmentId(equipment.getEquipmentId());
            temp.setEquipmentName(equipment.getEquipmentName());
            temp.setResourceStructureId(resourceStructure.getResourceStructureId());
            temp.setResourceStructureName(resourceStructure.getResourceStructureName());
            temp.setParentResourceStructureId(parentResource.getResourceStructureId());
            temp.setParentResourceStructureName(parentResource.getResourceStructureName());
            temp.setSignalId(configSignal.getSignalId());
            temp.setSignalName(configSignal.getSignalName());
            temp.setSignalKey(oneSignal);
            res.add(temp);
            signalMap.put(oneSignal, temp);
        }

        Date startTime = DateUtil.getMonthStartTime(queryTime);
        Date endTime = DateUtil.getMonthEndTime(startTime);
        List<EnergyHisDataSichuanResult> dataList = new ArrayList<>();
        getMonthDataFromInflux(startTime, endTime, res, dataList);

        // 日期格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dayFormat = new SimpleDateFormat("d");
        for (EnergyHisDataSichuanResult data : dataList) {
            String signalId = data.getSignalId();
            String time = data.getTime();
            if (data.getPointValue() == null)
                continue;
            Double pointValue = Double.valueOf(data.pointValue);

            SichuanEnergyInvestSignalDIffMonthResult matchingDTO = signalMap.get(signalId);
            if (matchingDTO != null) {
                try {
                    int day = Integer.parseInt(dayFormat.format(dateFormat.parse(time)));
                    // 根据天数设置对应的originValue
                    switch (day) {
                        case 1 -> matchingDTO.setOriginValue1(pointValue);
                        case 2 -> matchingDTO.setOriginValue2(pointValue);
                        case 3 -> matchingDTO.setOriginValue3(pointValue);
                        case 4 -> matchingDTO.setOriginValue4(pointValue);
                        case 5 -> matchingDTO.setOriginValue5(pointValue);
                        case 6 -> matchingDTO.setOriginValue6(pointValue);
                        case 7 -> matchingDTO.setOriginValue7(pointValue);
                        case 8 -> matchingDTO.setOriginValue8(pointValue);
                        case 9 -> matchingDTO.setOriginValue9(pointValue);
                        case 10 -> matchingDTO.setOriginValue10(pointValue);
                        case 11 -> matchingDTO.setOriginValue11(pointValue);
                        case 12 -> matchingDTO.setOriginValue12(pointValue);
                        case 13 -> matchingDTO.setOriginValue13(pointValue);
                        case 14 -> matchingDTO.setOriginValue14(pointValue);
                        case 15 -> matchingDTO.setOriginValue15(pointValue);
                        case 16 -> matchingDTO.setOriginValue16(pointValue);
                        case 17 -> matchingDTO.setOriginValue17(pointValue);
                        case 18 -> matchingDTO.setOriginValue18(pointValue);
                        case 19 -> matchingDTO.setOriginValue19(pointValue);
                        case 20 -> matchingDTO.setOriginValue20(pointValue);
                        case 21 -> matchingDTO.setOriginValue21(pointValue);
                        case 22 -> matchingDTO.setOriginValue22(pointValue);
                        case 23 -> matchingDTO.setOriginValue23(pointValue);
                        case 24 -> matchingDTO.setOriginValue24(pointValue);
                        case 25 -> matchingDTO.setOriginValue25(pointValue);
                        case 26 -> matchingDTO.setOriginValue26(pointValue);
                        case 27 -> matchingDTO.setOriginValue27(pointValue);
                        case 28 -> matchingDTO.setOriginValue28(pointValue);
                        case 29 -> matchingDTO.setOriginValue29(pointValue);
                        case 30 -> matchingDTO.setOriginValue30(pointValue);
                        case 31 -> matchingDTO.setOriginValue31(pointValue);
                    }
                } catch (Exception e) {
                    log.error("EnergySichuanEnergyInvestReportServiceImpl-getSignalDifferenceMonthReport error ", e);
                }
            }
        }

        //获取下个周期的数据
        dataList.clear();
        Date nextMonthStartTime = DateUtil.getNextMonthFirstDay(queryTime);
        Date nextMonthEndTime = DateUtil.getLastSecondsOfToday(nextMonthStartTime);
        if (nextMonthStartTime.before(new Date())) {
            getMonthDataFromInflux(nextMonthStartTime, nextMonthEndTime, res, dataList);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(queryTime);
            int days = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            for (EnergyHisDataSichuanResult data : dataList) {
                String signalId = data.getSignalId();
                if (data.getPointValue() == null)
                    continue;
                Double pointValue = Double.valueOf(data.pointValue);

                SichuanEnergyInvestSignalDIffMonthResult matchingDTO = signalMap.get(signalId);
                //将获取的下一个周期的数据按月份天数的不同,填充到不同的属性上
                if (matchingDTO != null) {
                    switch (days) {
                        case 28 -> matchingDTO.setOriginValue29(pointValue);
                        case 29 -> matchingDTO.setOriginValue30(pointValue);
                        case 30 -> matchingDTO.setOriginValue31(pointValue);
                        case 31 -> matchingDTO.setOriginValue32(pointValue);
                    }
                }
            }
        }
        return res;
    }

    /**
     * 从库中获取月区间历史数据
     */
    private void getMonthDataFromInflux(Date startTime, Date endTime, List<SichuanEnergyInvestSignalDIffMonthResult> resList, List<EnergyHisDataSichuanResult> dataList) {
        try {
            String selectDuration = "SELECT FIRST(PointValue) as PointValue  FROM historydatas WHERE time >=$startTime and time <= $endTime and ( $someSignalId )  group by SignalId, time(1d)";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereSignalId = new StringBuilder();
            int count = 0;
            for (SichuanEnergyInvestSignalDIffMonthResult oneRes : resList) {
                if (bsWhereSignalId.length() == 0) {
                    bsWhereSignalId.append(" SignalId='").append(oneRes.getSignalKey()).append("' ");
                } else {
                    bsWhereSignalId.append(" or SignalId='").append(oneRes.getSignalKey()).append("' ");
                }
                count++;
                if (count == resList.size() || count % 500 == 0) {
                    List<EnergyHisDataSichuanResult> resultComplexIndexQuery = new ArrayList<>();
                    selectDuration = selectDuration.replace("$someSignalId", bsWhereSignalId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                            .forDatabase(database)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataSichuanResult.class);
                        dataList.addAll(resultComplexIndexQuery);
                    }
                    bsWhereSignalId = new StringBuilder();
                }
            }
        } catch (Exception ex) {
            log.error("EnergySichuanEnergyInvestReportServiceImpl-getMonthDataFromInflux error ", ex);
        }
    }

    @Override
    public Object getHeadBoardPowerReport(Integer aEquipmentId, Integer bEquipmentId, Date queryTime) {

        List<SichuanEnergyInvestHeadBoardPowerDTO> res = new ArrayList<>();
        Map<Long, ConfigSignalItem> aConfigSignalMap = configSignalManager.getConfigSignalByEquipmentId(aEquipmentId).stream()
                .filter(i -> i.getBaseTypeId() != null && i.getBaseTypeId().toString().contains("1703325"))
                .sorted(Comparator.comparing(ConfigSignalItem::getBaseTypeId))
                .collect(Collectors.toMap(
                        ConfigSignalItem::getBaseTypeId,
                        item -> item,
                        (existing, replacement) -> replacement ));
        Map<Long, ConfigSignalItem>  bConfigSignalMap = configSignalManager.getConfigSignalByEquipmentId(bEquipmentId).stream()
                .filter(i -> i.getBaseTypeId() != null && i.getBaseTypeId().toString().contains("1703325"))
                .sorted(Comparator.comparing(ConfigSignalItem::getBaseTypeId))
                .collect(Collectors.toMap(
                        ConfigSignalItem::getBaseTypeId,
                        item -> item,
                        (existing, replacement) -> replacement ));

        List<ConfigSignalItem> totalSignal = new ArrayList<>();
        totalSignal.addAll(aConfigSignalMap.values());
        totalSignal.addAll(bConfigSignalMap.values());
        //获取所有支路信号集合
        Set<Long> sortedBaseTypeIdSet = totalSignal.stream()
                .map(ConfigSignalItem::getBaseTypeId)
                .collect(Collectors.toCollection(TreeSet::new));

        int i = 1;
        Map<Long,SichuanEnergyInvestHeadBoardPowerDTO> totalRecordMap = new HashMap<>();
        Map<Long,SichuanEnergyInvestHeadBoardPowerDTO> aRecordMap = new HashMap<>();
        Map<Long,SichuanEnergyInvestHeadBoardPowerDTO> bRecordMap = new HashMap<>();
        Map<String,SichuanEnergyInvestHeadBoardPowerDTO> allResMap = new HashMap<>();

        //前端强调要这样的数据格式展示数据,不让合并!
        for (Long oneBaseTypeId : sortedBaseTypeIdSet ) {
            ConfigSignalItem signalItemA = aConfigSignalMap.get(oneBaseTypeId);
            if(signalItemA != null){
                SichuanEnergyInvestHeadBoardPowerDTO tempA = new SichuanEnergyInvestHeadBoardPowerDTO();
                tempA.setCounterNo("机柜" + i);
                tempA.setRoad(signalItemA.getSignalName());
                tempA.setBaseTypeId(oneBaseTypeId);
                tempA.setEquipmentId(aEquipmentId);
                tempA.setSignalId(signalItemA.getSignalId());
                res.add(tempA);
                allResMap.put(tempA.getSignalKey(),tempA);
                aRecordMap.put(oneBaseTypeId,tempA);
            }

            ConfigSignalItem signalItemB = bConfigSignalMap.get(oneBaseTypeId);
            if(signalItemB != null){
                SichuanEnergyInvestHeadBoardPowerDTO tempB = new SichuanEnergyInvestHeadBoardPowerDTO();
                tempB.setCounterNo("机柜" + i);
                tempB.setRoad(signalItemB.getSignalName());
                tempB.setBaseTypeId(oneBaseTypeId);
                tempB.setEquipmentId(bEquipmentId);
                tempB.setSignalId(signalItemB.getSignalId());
                res.add(tempB);
                allResMap.put(tempB.getSignalKey(),tempB);
                bRecordMap.put(oneBaseTypeId,tempB);
            }

            SichuanEnergyInvestHeadBoardPowerDTO tempTotal = new SichuanEnergyInvestHeadBoardPowerDTO();
            tempTotal.setCounterNo("机柜" + i);
            tempTotal.setRoad("总电量");
            tempTotal.setBaseTypeId(oneBaseTypeId);
            totalRecordMap.put(oneBaseTypeId,tempTotal);

            i++;
        }

        //从influxDB获取数据
        Date startTime = DateUtil.getMonthStartTime(queryTime);
        Date endTime = DateUtil.getMonthEndTime(startTime);
        List<EnergyHisDataSichuanResult> dataList = new ArrayList<>();
        getHeadBoardPowerData(startTime,endTime,res,dataList);
        // 日期格式化
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dayFormat = new SimpleDateFormat("d");
        for (EnergyHisDataSichuanResult oneData : dataList ) {
            String time = oneData.getTime();
            if (oneData.getPointValue() == null)
                continue;
            Double pointValue = Double.valueOf(oneData.pointValue);
            SichuanEnergyInvestHeadBoardPowerDTO matchingDTO = allResMap.get(oneData.getSignalId());
            if (matchingDTO != null) {
                try {
                    int day = Integer.parseInt(dayFormat.format(dateFormat.parse(time)));
                    // 根据天数设置对应的originValue
                    switch (day) {
                        case 1 -> matchingDTO.setOriginValue1(pointValue);
                        case 2 -> matchingDTO.setOriginValue2(pointValue);
                        case 3 -> matchingDTO.setOriginValue3(pointValue);
                        case 4 -> matchingDTO.setOriginValue4(pointValue);
                        case 5 -> matchingDTO.setOriginValue5(pointValue);
                        case 6 -> matchingDTO.setOriginValue6(pointValue);
                        case 7 -> matchingDTO.setOriginValue7(pointValue);
                        case 8 -> matchingDTO.setOriginValue8(pointValue);
                        case 9 -> matchingDTO.setOriginValue9(pointValue);
                        case 10 -> matchingDTO.setOriginValue10(pointValue);
                        case 11 -> matchingDTO.setOriginValue11(pointValue);
                        case 12 -> matchingDTO.setOriginValue12(pointValue);
                        case 13 -> matchingDTO.setOriginValue13(pointValue);
                        case 14 -> matchingDTO.setOriginValue14(pointValue);
                        case 15 -> matchingDTO.setOriginValue15(pointValue);
                        case 16 -> matchingDTO.setOriginValue16(pointValue);
                        case 17 -> matchingDTO.setOriginValue17(pointValue);
                        case 18 -> matchingDTO.setOriginValue18(pointValue);
                        case 19 -> matchingDTO.setOriginValue19(pointValue);
                        case 20 -> matchingDTO.setOriginValue20(pointValue);
                        case 21 -> matchingDTO.setOriginValue21(pointValue);
                        case 22 -> matchingDTO.setOriginValue22(pointValue);
                        case 23 -> matchingDTO.setOriginValue23(pointValue);
                        case 24 -> matchingDTO.setOriginValue24(pointValue);
                        case 25 -> matchingDTO.setOriginValue25(pointValue);
                        case 26 -> matchingDTO.setOriginValue26(pointValue);
                        case 27 -> matchingDTO.setOriginValue27(pointValue);
                        case 28 -> matchingDTO.setOriginValue28(pointValue);
                        case 29 -> matchingDTO.setOriginValue29(pointValue);
                        case 30 -> matchingDTO.setOriginValue30(pointValue);
                        case 31 -> matchingDTO.setOriginValue31(pointValue);
                    }
                } catch (Exception e) {
                    log.error("EnergySichuanEnergyInvestReportServiceImpl-getHeadBoardPowerReport error ", e);
                }
            }
        }

        //获取下个周期的数据(要做差值计算)
        dataList.clear();
        Date nextMonthStartTime = DateUtil.getNextMonthFirstDay(queryTime);
        Date nextMonthEndTime = DateUtil.getLastSecondsOfToday(nextMonthStartTime);
        if (nextMonthStartTime.before(new Date())) {
            getHeadBoardPowerData(nextMonthStartTime, nextMonthEndTime, res, dataList);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(queryTime);
            int days = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            for (EnergyHisDataSichuanResult data : dataList) {
                String signalId = data.getSignalId();
                if (data.getPointValue() == null)
                    continue;
                Double pointValue = Double.valueOf(data.pointValue);

                SichuanEnergyInvestHeadBoardPowerDTO matchingDTO = allResMap.get(signalId);
                //将获取的下一个周期的数据按月份天数的不同,填充到不同的属性上
                if (matchingDTO != null) {
                    switch (days) {
                        case 28 -> matchingDTO.setOriginValue29(pointValue);
                        case 29 -> matchingDTO.setOriginValue30(pointValue);
                        case 30 -> matchingDTO.setOriginValue31(pointValue);
                        case 31 -> matchingDTO.setOriginValue32(pointValue);
                    }
                }
            }
        }

        for (Long oneBasTypeId : sortedBaseTypeIdSet ) {
            SichuanEnergyInvestHeadBoardPowerDTO totalRecord = totalRecordMap.get(oneBasTypeId);
            if (totalRecord == null)
                continue;
            SichuanEnergyInvestHeadBoardPowerDTO aRoad = aRecordMap.get(oneBasTypeId);
            SichuanEnergyInvestHeadBoardPowerDTO bRoad = bRecordMap.get(oneBasTypeId);
            if (aRoad == null) {
                if (bRoad != null) {
                    copyValues(totalRecord, bRoad);
                }
            } else {
                if (bRoad == null) {
                    copyValues(totalRecord, aRoad);
                } else {
                    mergeValues(totalRecord, aRoad, bRoad);
                }
            }
            res.add(totalRecord);
        }
        return res.stream().sorted(Comparator.comparing(SichuanEnergyInvestHeadBoardPowerDTO::getBaseTypeId));
    }

    private void copyValues(SichuanEnergyInvestHeadBoardPowerDTO target, SichuanEnergyInvestHeadBoardPowerDTO source) {
        target.setOriginValue1(source.getOriginValue1());
        target.setOriginValue2(source.getOriginValue2());
        target.setOriginValue3(source.getOriginValue3());
        target.setOriginValue4(source.getOriginValue4());
        target.setOriginValue5(source.getOriginValue5());
        target.setOriginValue6(source.getOriginValue6());
        target.setOriginValue7(source.getOriginValue7());
        target.setOriginValue8(source.getOriginValue8());
        target.setOriginValue9(source.getOriginValue9());
        target.setOriginValue10(source.getOriginValue10());
        target.setOriginValue11(source.getOriginValue11());
        target.setOriginValue12(source.getOriginValue12());
        target.setOriginValue13(source.getOriginValue13());
        target.setOriginValue14(source.getOriginValue14());
        target.setOriginValue15(source.getOriginValue15());
        target.setOriginValue16(source.getOriginValue16());
        target.setOriginValue17(source.getOriginValue17());
        target.setOriginValue18(source.getOriginValue18());
        target.setOriginValue19(source.getOriginValue19());
        target.setOriginValue20(source.getOriginValue20());
        target.setOriginValue21(source.getOriginValue21());
        target.setOriginValue22(source.getOriginValue22());
        target.setOriginValue23(source.getOriginValue23());
        target.setOriginValue24(source.getOriginValue24());
        target.setOriginValue25(source.getOriginValue25());
        target.setOriginValue26(source.getOriginValue26());
        target.setOriginValue27(source.getOriginValue27());
        target.setOriginValue28(source.getOriginValue28());
        target.setOriginValue29(source.getOriginValue29());
        target.setOriginValue30(source.getOriginValue30());
        target.setOriginValue31(source.getOriginValue31());
        target.setOriginValue32(source.getOriginValue32());
    }

    private void mergeValues(SichuanEnergyInvestHeadBoardPowerDTO totalRecord, SichuanEnergyInvestHeadBoardPowerDTO aRoad, SichuanEnergyInvestHeadBoardPowerDTO bRoad) {
        totalRecord.setOriginValue1(getMergedValue(aRoad.getOriginValue1(), bRoad.getOriginValue1()));
        totalRecord.setOriginValue2(getMergedValue(aRoad.getOriginValue2(), bRoad.getOriginValue2()));
        totalRecord.setOriginValue3(getMergedValue(aRoad.getOriginValue3(), bRoad.getOriginValue3()));
        totalRecord.setOriginValue4(getMergedValue(aRoad.getOriginValue4(), bRoad.getOriginValue4()));
        totalRecord.setOriginValue5(getMergedValue(aRoad.getOriginValue5(), bRoad.getOriginValue5()));
        totalRecord.setOriginValue6(getMergedValue(aRoad.getOriginValue6(), bRoad.getOriginValue6()));
        totalRecord.setOriginValue7(getMergedValue(aRoad.getOriginValue7(), bRoad.getOriginValue7()));
        totalRecord.setOriginValue8(getMergedValue(aRoad.getOriginValue8(), bRoad.getOriginValue8()));
        totalRecord.setOriginValue9(getMergedValue(aRoad.getOriginValue9(), bRoad.getOriginValue9()));
        totalRecord.setOriginValue10(getMergedValue(aRoad.getOriginValue10(), bRoad.getOriginValue10()));
        totalRecord.setOriginValue11(getMergedValue(aRoad.getOriginValue11(), bRoad.getOriginValue11()));
        totalRecord.setOriginValue12(getMergedValue(aRoad.getOriginValue12(), bRoad.getOriginValue12()));
        totalRecord.setOriginValue13(getMergedValue(aRoad.getOriginValue13(), bRoad.getOriginValue13()));
        totalRecord.setOriginValue14(getMergedValue(aRoad.getOriginValue14(), bRoad.getOriginValue14()));
        totalRecord.setOriginValue15(getMergedValue(aRoad.getOriginValue15(), bRoad.getOriginValue15()));
        totalRecord.setOriginValue16(getMergedValue(aRoad.getOriginValue16(), bRoad.getOriginValue16()));
        totalRecord.setOriginValue17(getMergedValue(aRoad.getOriginValue17(), bRoad.getOriginValue17()));
        totalRecord.setOriginValue18(getMergedValue(aRoad.getOriginValue18(), bRoad.getOriginValue18()));
        totalRecord.setOriginValue19(getMergedValue(aRoad.getOriginValue19(), bRoad.getOriginValue19()));
        totalRecord.setOriginValue20(getMergedValue(aRoad.getOriginValue20(), bRoad.getOriginValue20()));
        totalRecord.setOriginValue21(getMergedValue(aRoad.getOriginValue21(), bRoad.getOriginValue21()));
        totalRecord.setOriginValue22(getMergedValue(aRoad.getOriginValue22(), bRoad.getOriginValue22()));
        totalRecord.setOriginValue23(getMergedValue(aRoad.getOriginValue23(), bRoad.getOriginValue23()));
        totalRecord.setOriginValue24(getMergedValue(aRoad.getOriginValue24(), bRoad.getOriginValue24()));
        totalRecord.setOriginValue25(getMergedValue(aRoad.getOriginValue25(), bRoad.getOriginValue25()));
        totalRecord.setOriginValue26(getMergedValue(aRoad.getOriginValue26(), bRoad.getOriginValue26()));
        totalRecord.setOriginValue27(getMergedValue(aRoad.getOriginValue27(), bRoad.getOriginValue27()));
        totalRecord.setOriginValue28(getMergedValue(aRoad.getOriginValue28(), bRoad.getOriginValue28()));
        totalRecord.setOriginValue29(getMergedValue(aRoad.getOriginValue29(), bRoad.getOriginValue29()));
        totalRecord.setOriginValue30(getMergedValue(aRoad.getOriginValue30(), bRoad.getOriginValue30()));
        totalRecord.setOriginValue31(getMergedValue(aRoad.getOriginValue31(), bRoad.getOriginValue31()));
        totalRecord.setOriginValue32(getMergedValue(aRoad.getOriginValue32(), bRoad.getOriginValue32()));
    }

    private Double getMergedValue(Double valueA, Double valueB) {
        if (valueA == null && valueB == null) {
            return null;
        }
        if (valueA == null) {
            return valueB;
        }
        if (valueB == null) {
            return valueA;
        }
        return valueA + valueB;
    }
    private void getHeadBoardPowerData(Date startTime, Date endTime, List<SichuanEnergyInvestHeadBoardPowerDTO> resList, List<EnergyHisDataSichuanResult> dataList) {
        try {
            String selectDuration = "SELECT FIRST(PointValue) as PointValue  FROM historydatas WHERE time >=$startTime and time <= $endTime and ( $someSignalId )  group by SignalId, time(1d)";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereSignalId = new StringBuilder();
            int count = 0;
            for (SichuanEnergyInvestHeadBoardPowerDTO oneRes : resList) {
                if (bsWhereSignalId.length() == 0) {
                    bsWhereSignalId.append(" SignalId='").append(oneRes.getSignalKey()).append("' ");
                } else {
                    bsWhereSignalId.append(" or SignalId='").append(oneRes.getSignalKey()).append("' ");
                }
                count++;
                if (count == resList.size() || count % 500 == 0) {
                    List<EnergyHisDataSichuanResult> resultComplexIndexQuery = new ArrayList<>();
                    selectDuration = selectDuration.replace("$someSignalId", bsWhereSignalId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                            .forDatabase(database)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataSichuanResult.class);
                        dataList.addAll(resultComplexIndexQuery);
                    }
                    bsWhereSignalId = new StringBuilder();
                }
            }
        } catch (Exception ex) {
            log.error("EnergySichuanEnergyInvestReportServiceImpl-getHeadBoardPowerData error ", ex);
        }
    }
}
