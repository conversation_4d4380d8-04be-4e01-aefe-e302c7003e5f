package com.siteweb.energy.service;

import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyDataItemTimeliness;

import java.util.List;

public interface EnergyDataConfigItemService {
    List<EnergyDataItem> getAllByEntryId(Integer entryId);

    EnergyDataItem getDataItemById(Integer entryId, Integer itemId);

    EnergyDataItem createEnergyDataItem(EnergyDataItem energyDataItem);

    EnergyDataItem updateEnergyDataItem(EnergyDataItem energyDataItem);

    void deleteEntryItemId(Integer entryItemId);

    Integer createEnergyDataItemTimeliness(EnergyDataItemTimeliness energyDataItemTimeliness);

    List<EnergyDataItem> getAllByEntryIdTimeliness(Integer entryId);

    List<EnergyDataItemTimeliness> getDataItemTimelinessByEntryItemId(Integer entryItemId);

    void deleteEnergyDataTimeliness(Integer id);

    EnergyDataItemTimeliness updateEnergyDataTimeliness(EnergyDataItemTimeliness energyDataItemTimeliness);

    EnergyDataItem getEnergyDataItemTimeliness(Integer entryId,Integer dataItemId);
}
