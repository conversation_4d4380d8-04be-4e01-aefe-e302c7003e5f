package com.siteweb.energy.service.impl;

import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.dto.ComplexIndexQueryResult;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.energy.dto.EnergyHisHourDataResult;
import com.siteweb.energy.dto.EnergyHisMonthDataResult;
import com.siteweb.energy.entity.EnergyHisHourData;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.model.SchemeUnitPrice;
import com.siteweb.energy.service.EnergyElecFeeConfigService;
import com.siteweb.energy.service.EnergyElecFeeService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.*;

@Service
/*
    能耗费用计算类， 将robin入库Influxdb的用电量指标取出换算成电费数据并入库保存
 */
public class EnergyElecFeeServiceImpl implements EnergyElecFeeService {
    private final Logger log = LoggerFactory.getLogger(EnergyElecFeeServiceImpl.class);

    @Autowired
    private InfluxDBManager influxDBManager;
    @Autowired
    private EnergyElecFeeConfigService energyElecFeeConfigService;
    @Autowired
    private ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    private Date startTime,endTime;

    @Override
    public void scheduleCalcEnergyFee() {
        //获取上一小时的区间
        startTime = DateUtil.getLastHourStartOrEndTime(true);
        endTime = DateUtil.getLastHourStartOrEndTime(false);
        //所有能源类型
        List<Integer> lstEnergyComplexIndexBusinessType = complexIndexBusinessTypeService.GetComplexIndexBusinessTypeByParentid(1).stream().map(ComplexIndexBusinessType::getBusinessTypeId).collect(Collectors.toList());
        for (Integer complexIndexBusinessType : lstEnergyComplexIndexBusinessType) {
            //获取配置电费的层级节点
            List<Integer> lstResourceStructureHasScheme = energyElecFeeConfigService.getAllStructureIdsHasScheme(startTime, endTime,complexIndexBusinessType);
            if (lstResourceStructureHasScheme == null || lstResourceStructureHasScheme.size() == 0) {
                log.info("查询开始" + dateToString(startTime) + "结束时间" + dateToString(endTime) + "查询计费方案节点数量为0");
                continue;
            }

            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            QueryResult query;
            String selectDuration = "select * from EnergyHisHourData where time >=$startTime and time <=$endTime and ($someComplexIndex) order by time asc ";
            List<EnergyHisHourData> influxdbComplexIndex;


            for (Integer rs : lstResourceStructureHasScheme) {
                ResourceStructure rst = resourceStructureManager.getResourceStructureById(rs);
                // 存放所有子节点的总用电指标；
                List<ComplexIndex> lstComplexIndexAllChildPower = new ArrayList<>();

                List<ComplexIndex> lstComplexIndexThisStructure = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                        i -> i.getObjectId().equals(rst.getResourceStructureId()) && i.getObjectTypeId().equals(rst.getStructureTypeId())
                ).collect(Collectors.toList());
                if (lstComplexIndexThisStructure == null) continue;
                //complexIndexService.findByObjectIdAndObjectTypeId(rst.getResourceStructureId(),rst.getStructureTypeId());
                List<Integer> lstPowerComplexIndexDefinitionId = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                        i -> i.getBusinessTypeId().equals(complexIndexBusinessType) && (i.getComplexIndexDefinitionTypeId().equals(1) || i.getComplexIndexDefinitionTypeId().equals(3))
                ).map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());

                lstComplexIndexThisStructure.removeIf(i -> !lstPowerComplexIndexDefinitionId.contains(i.getComplexIndexDefinitionId()));

                if (lstComplexIndexThisStructure.size() == 0) {
                    continue;
                }

                //判断有无总用电量指标
                BusinessDefinitionMap bdMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                        i -> i.getBusinessTypeId().equals(complexIndexBusinessType) && i.getComplexIndexDefinitionTypeId().equals(1)
                ).findFirst().orElse(null);
                if (bdMap == null) return;
                ComplexIndex totalPowerComplexIndex = lstComplexIndexThisStructure.stream().filter(item -> item.getComplexIndexDefinitionId().equals(bdMap.getComplexIndexDefinitionId())).findFirst().orElse(null);
                if (totalPowerComplexIndex == null) {
                    continue;
                }
                double thisMonthTotal = GetThisMonthSume(totalPowerComplexIndex.getComplexIndexId(), startTime);

                //添加1级子节点
                List<ResourceStructure> childResourceStructurelst = resourceStructureManager.getAll().stream().filter(i -> i.getLevelOfPath() != null &&
                        i.getLevelOfPath().startsWith(rst.getLevelOfPath() + ".")
                                &&  countLevel(i.getLevelOfPath(), ".") - countLevel(rst.getLevelOfPath(), ".")  ==  1
                ).collect(Collectors.toList());
                for (ResourceStructure oneChildRS : childResourceStructurelst) {
                    //子节点本身有方案的，不包含在内
                    if (lstResourceStructureHasScheme.contains(oneChildRS.getResourceStructureId())) continue;


                    List<ComplexIndex> lstComplexIndexChild = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                            i -> i.getObjectId().equals(oneChildRS.getResourceStructureId()) && i.getObjectTypeId().equals(oneChildRS.getStructureTypeId())
                    ).collect(Collectors.toList());
                    if (lstComplexIndexChild == null) continue;
                    //只取总用量
                    List<Integer> lstPowerComplexIndexDefinitionIdChild = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                            i -> i.getBusinessTypeId() == complexIndexBusinessType && i.getComplexIndexDefinitionTypeId().equals(1)
                    ).map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());

                    lstComplexIndexChild.removeIf(i -> !lstPowerComplexIndexDefinitionIdChild.contains(i.getComplexIndexDefinitionId()));
//                    if (lstComplexIndexChild.size()>0){
//                        for(ComplexIndex thisIndex : lstComplexIndexChild){
//                            if (!lstPowerComplexIndexDefinitionIdChild.contains(thisIndex.getComplexIndexId()))
//                                lstComplexIndexChild.remove(thisIndex);
//                        }
//                    }

                    if (lstComplexIndexChild.size() > 0) lstComplexIndexAllChildPower.addAll(lstComplexIndexChild);
                }

                if (lstComplexIndexAllChildPower.size() > 0)
                    lstComplexIndexThisStructure.addAll(lstComplexIndexAllChildPower);

                StringBuilder bsWhereComplexIndexId = new StringBuilder();
                for (ComplexIndex complexIndex : lstComplexIndexThisStructure) {
                    if (bsWhereComplexIndexId.length() == 0) {
                        bsWhereComplexIndexId.append(" ComplexIndexId='" + complexIndex.getComplexIndexId() + "'");
                    } else {
                        bsWhereComplexIndexId.append(" or ComplexIndexId='" + complexIndex.getComplexIndexId() + "'");
                    }
                }
                //String sql = String.format(selectDuration, dateToString(startTime), dateToString(endTime), bsWhereComplexIndexId.toString());
                String strSelectDuration = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                        .forDatabase(databaseEnergy)
                        .bind("startTime", dateToString(startTime))
                        .bind("endTime", dateToString(endTime))
                        .create();

                query = influxDB.query(queryBuilder);
                if (query != null) {
                    influxdbComplexIndex = resultMapper.toPOJO(query, EnergyHisHourData.class);
                    if (!influxdbComplexIndex.isEmpty()) {
                        //判断
                        for (EnergyHisHourData historyComplexIndex : influxdbComplexIndex) {
                            SaveHistoryComplexIndexFee(historyComplexIndex, thisMonthTotal, rs
                                    , lstComplexIndexAllChildPower.stream().filter(i -> i.getComplexIndexId().toString().equals(historyComplexIndex.getComplexIndexId())).collect(Collectors.toList()).size() > 0
                            , complexIndexBusinessType);
                        }
                    }
                }
            }
        }
    }

    // 获取电费并保存influxdb
    private void SaveHistoryComplexIndexFee(EnergyHisHourData historyComplexIndex,Double thisMonthTotal,Integer hasSchemeId,  boolean isChild,Integer complexIndexBusinessType){
        try {
            ComplexIndex thisIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getComplexIndexId().toString().equals(historyComplexIndex.getComplexIndexId())
            ).findFirst().orElse(null);
            //complexIndexService.findByComplexIndexId(Integer.parseInt(historyComplexIndex.getComplexIndexId()));

            // 获取当前时刻对应的电费单价和电费方案
            SchemeUnitPrice schemeUnitPrice = new SchemeUnitPrice();
            if (thisIndex == null) return;

            if(isChild){
                if (hasSchemeId != null) {
                    schemeUnitPrice = energyElecFeeConfigService.GetSchemeUnitPrice(hasSchemeId,
                            stringToDate(historyComplexIndex.getTime()), thisMonthTotal, complexIndexBusinessType);
                }
            }else{
                // 获取当前时刻对应的电费单价和电费方案
                schemeUnitPrice = energyElecFeeConfigService.GetSchemeUnitPrice(thisIndex.getObjectId(),
                        stringToDate(historyComplexIndex.getTime()),thisMonthTotal, complexIndexBusinessType);

            }
            if (schemeUnitPrice.getUnitPrice() < 0 )
                return;

            Map<String, String> tags = new HashMap<>();
            Map<String, Object> fields = new HashMap<>();
            tags.put("ComplexIndexId", historyComplexIndex.getComplexIndexId());
            tags.put("CalcTime", historyComplexIndex.getTime());
            tags.put("fpgDescKey", String.valueOf(schemeUnitPrice.getFpgDescKey()));

            String indexValue = historyComplexIndex.getIndexValue();
            if (indexValue.equals("-")) {
                fields.put("IndexValue", 0);
                fields.put("IndexFeeValue", 0);
            }
            else {
                fields.put("IndexValue", NumberUtil.doubleAccuracy(NumberUtil.formatNumeric(indexValue), 2));
                fields.put("IndexFeeValue", NumberUtil.doubleAccuracy(NumberUtil.formatNumeric(indexValue) * schemeUnitPrice.getUnitPrice(), 2));
            }
            fields.put("UnitPrice", NumberUtil.doubleAccuracy(schemeUnitPrice.getUnitPrice(),2));
            fields.put("SchemeId", schemeUnitPrice.getSchemeId());

            //influxDBManager.insert("HistoryComplexIndexFee",tags,fields);
            influxDBManager.insertWithTime("EnergyHisFeeData",tags,fields,
                    DateUtil.localToUTC((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(historyComplexIndex.getTime())).getTime(),databaseEnergy);
        }
        catch (Exception ex){
            log.error("SaveEnergyHisFeeData error:historyComplexIndexId="+ historyComplexIndex.getComplexIndexId() + ";time=" + historyComplexIndex.getTime());
        }
    }

    @Override
    // 获取此指标本月累加值
    public Double GetThisMonthSume(int complexIndexId,Date calcTime){
        Date startTime = DateUtil.getMonthStartTime(calcTime);
        return GetIndexValueByComplexIndexId(complexIndexId,startTime,calcTime);
    }

    // 根据指标ID获取时间段用电量
    private Double GetIndexValueByComplexIndexId(int complexIndexId,Date startTime, Date endTime){
        List<EnergyHisMonthDataResult> resultComplexIndexQuery;

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("select IndexValue as result  from EnergyHisMonthData where time = $startTime and ComplexIndexId = $complexIndexId ")
                .forDatabase(databaseEnergy)
                .bind("startTime", dateToString(startTime))
                //.bind("endTime", dateToString(endTime))
                .bind("complexIndexId", Integer.toString(complexIndexId))
                .create();
        query =  influxDB.query(queryBuilder);
        if (query != null) {
            resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
            if (!resultComplexIndexQuery.isEmpty()) {
                return NumberUtil.doubleAccuracy (Double.parseDouble(resultComplexIndexQuery.get(0).result),2);
            }
        }
        return 0d;
    }

    // 拆分LevelOfPath层级。471000001.471000002.471000013.471000065返回3
    public Integer countLevel(String str,String s){
        int c = 0; s = s.toLowerCase(); //大写转小写
        char ch = s.charAt(0); str = str.toLowerCase(); //大写转小写
        for(int i=0;i<str.length();i++){
            if(ch == str.charAt(i)) c++;
        }
        return c;
    }

}
