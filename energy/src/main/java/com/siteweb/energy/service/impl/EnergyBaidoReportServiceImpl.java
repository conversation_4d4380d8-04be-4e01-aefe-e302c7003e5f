package com.siteweb.energy.service.impl;

import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.energy.dto.HistoryDatasResult;
import com.siteweb.energy.entity.EnergyCustomerTBReportMap;
import com.siteweb.energy.entity.EnergyCustomerTBReportRecord;
import com.siteweb.energy.entity.EnergyCustomerTbReport;
import com.siteweb.energy.mapper.EnergyTaiBoReportMapper;
import com.siteweb.energy.mapper.EnergyTaiBoReportRecordMapper;
import com.siteweb.energy.service.EnergyBaidoReportService;
import com.siteweb.monitoring.entity.HistorySignal;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

import static com.siteweb.common.util.DateUtil.*;

@Service
@Slf4j
public class EnergyBaidoReportServiceImpl implements EnergyBaidoReportService {
    @Value("${spring.influx.database}")
    private String database;
    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private EnergyTaiBoReportMapper energyTaiBoReportMapper;
    @Autowired
    private EnergyTaiBoReportRecordMapper energyTaiBoReportRecordMapper;
    public final static String ENERGY_ROOT = "upload-dir";

    @Override
    public void doSchedule() {
        String templatePath = "";
        String recordPath = "" ;
        String recordName = "";
        Integer reportId = null;
        Date currentDate = new Date();
//        LocalDateTime specifiedDateTime = LocalDateTime.of(2023, 7, 31, 3, 30, 0);
//        LocalDate currentDate1 = specifiedDateTime.toLocalDate();
//        Date currentDate = java.sql.Date.valueOf(currentDate1);

        List<EnergyCustomerTbReport> allReportInfo = energyTaiBoReportMapper.getAllReportInfo();
        if(allReportInfo == null || allReportInfo.isEmpty()) {
            log.warn(" --------- EnergyBaido allReportInfo == null || allReportInfo.size() < 1 ");
            return;
        }
        for (EnergyCustomerTbReport report: allReportInfo) {
            templatePath = report.getPath();
            recordPath = report.getRecordpath();
            recordName = report.getReportname();
            reportId = report.getReportid();
            try {
                //得到设备的id以及信号id信息
                List<EnergyCustomerTBReportMap> equipmentInfo = energyTaiBoReportMapper.getAllEquipmentInfoByReportId(reportId);
                FileInputStream file = new FileInputStream(new File(ENERGY_ROOT + templatePath));
                Workbook workbook = new HSSFWorkbook(file);
                int sheetCount = reportId == 1 ? 7 : 8;
                // 设置单元格计算模式为自动计算
                workbook.setForceFormulaRecalculation(true);
                for (int i = 0; i < sheetCount; i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    int cellId = 0;
                    if (i == 0 || i == sheetCount-1) {
                        cellId = 2;
                    } else {
                        cellId = 3;
                    }
                    int sheetId = i;
                    Date startDate = SetReportDate(sheet, cellId, i, sheetCount-1);
                    if (i != sheetCount-1) {
                        List<EnergyCustomerTBReportMap> resultTemp = equipmentInfo.stream().filter(map -> map.getSheetId() == sheetId).toList();
                        if (resultTemp.isEmpty())
                            continue;
                        for (EnergyCustomerTBReportMap etm: resultTemp) {
                            Row row = sheet.getRow(etm.getRowId());
                            Cell cell = row.getCell(etm.getCellId());
                            List<HistoryDatasResult> results = getHistoryDataMonth(etm, startDate, currentDate);
                            int count = 1;
                            for (HistoryDatasResult temp: results) {
                                cell.setCellValue(temp.getResult() == null ? 0d :Double.parseDouble(temp.getResult()));
                                cell = row.getCell(etm.getCellId() + count);
                                count++;
                            }
                        }
                    }
                }
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
                String formattedDate = dateFormat.format(currentDate);
                FileOutputStream out = new FileOutputStream(new File(ENERGY_ROOT + recordPath + "/" + recordName + formattedDate + ".xls"));
                workbook.write(out);
                out.close();

                //插入文件生成记录
                EnergyCustomerTBReportRecord reportRecord = new EnergyCustomerTBReportRecord();
                reportRecord.setReportId(reportId);
                reportRecord.setReportName(recordName + formattedDate + ".xls");
                reportRecord.setFilePath(recordPath);
                reportRecord.setCreateTime(currentDate);
                energyTaiBoReportRecordMapper.insert(reportRecord);
            } catch (Exception e) {
                log.error("EnergyBaidoReportServiceImpl-doSchedule error " , e);
            }
        }
//        EnergyCustomerTbReport report = allReportInfo.stream().filter(item -> item.getReportid() != null && item.getReportid().equals(1))
//                .findFirst().orElse(null);
//        if (report == null) {
//            log.warn("--------- EnergyBaido report == null ");
//            return;
//        }
    }

    private Date SetReportDate(Sheet sheet, int cellId, int sheetId, int maxSheetId) {
        Date startDate = null;
       try {
           Row rowDate = sheet.getRow(0);
           Cell cellDate = rowDate.getCell(cellId);

           LocalDate currentDate = LocalDate.now();
//           LocalDateTime specifiedDateTime = LocalDateTime.of(2023, 7, 31, 3, 30, 0);
//           LocalDate currentDate = specifiedDateTime.toLocalDate();

           int currentDays = currentDate.getDayOfMonth();
           int currentMonth = currentDate.getMonthValue();
           // 如果今天是本月的第一天，或者是本年的第一天
           if (currentDays == 1 || (currentMonth == 1 && currentDate.getDayOfYear() == 1)) {
               LocalDate lastMonthDate = currentDate.minusMonths(1);
               int lastMonthDays = lastMonthDate.lengthOfMonth();
               int startColIndex = lastMonthDays + cellId;
               DeleteReportColumn(sheet, startColIndex, sheetId, maxSheetId);
               startDate = java.sql.Date.valueOf(lastMonthDate.withDayOfMonth(1));
               for (int day = 1; day <= lastMonthDays; day++) {
                   LocalDate date = lastMonthDate.withDayOfMonth(day);
                   cellDate.setCellValue(date);
                   cellDate = rowDate.getCell(cellId + day);
               }
               if (sheetId != maxSheetId) {
                   cellDate = rowDate.getCell(cellId + lastMonthDays);
                   cellDate.setCellValue(currentDate);
               }
           } else {
               int startColIndex = currentDays + cellId - 1;
               DeleteReportColumn(sheet, startColIndex, sheetId, maxSheetId);
               startDate = java.sql.Date.valueOf(currentDate.withDayOfMonth(1));
               currentDays = sheetId == maxSheetId ? currentDays -1 : currentDays;
               for (int day = 1; day <= currentDays; day++) {
                   LocalDate date = currentDate.withDayOfMonth(day);
                   cellDate.setCellValue(date);
                   cellDate = rowDate.getCell(cellId + day);
               }
           }
       } catch (Exception e) {
           log.error("EnergyBaidoReportServiceImpl-SetReportDate " , e);
       }
        return startDate;
    }

    //删除多余的列
    private void DeleteReportColumn(Sheet sheet, int startColIndex, int sheetId, int maxSheetId) {
        try {
            int endColIndex = 0;
            if (sheetId == maxSheetId) {
                endColIndex = sheet.getRow(0).getLastCellNum()-1;
            } else {
                endColIndex = sheet.getRow(0).getLastCellNum()-2;
            }
            if (startColIndex > endColIndex)
                return;
            int[] columnsToDelete;
            if (startColIndex == endColIndex) {
                columnsToDelete = new int[]{startColIndex};
            } else {
                columnsToDelete = IntStream.rangeClosed(startColIndex, endColIndex).toArray();
            }
            // 逆序删除列，以防止索引错乱
            for (int i = columnsToDelete.length - 1; i >= 0; i--) {
                int columnIndex = columnsToDelete[i];
                for (int j = 0; j <= sheet.getLastRowNum(); j++) {
                    Row row = sheet.getRow(j);
                    if (row != null) {
                        Cell cell = row.getCell(columnIndex);
                        if (cell != null) {
                            row.removeCell(cell); // 删除指定列的单元格
                        }
                        shiftCellsLeft(row, columnIndex); // 将后面的单元格左移
                    }
                }
            }
        } catch (Exception e) {
            log.error("EnergyTaiBoReportServiceImpl-DeleteReportColumn throw exception:", e);
        }
    }

    // 将指定行中的单元格左移
    private void shiftCellsLeft(Row row, int columnIndex) {
        try {
            for (int i = columnIndex; i < row.getLastCellNum(); i++) {
                Cell cell = row.getCell(i + 1);
                if (cell != null) {
                    Cell newCell = row.createCell(i, cell.getCellType());
                    cloneCell(cell, newCell);
                    if (cell.getCellStyle() != null) {
                        newCell.setCellStyle(cell.getCellStyle());
                    }
                    row.removeCell(cell);
                }
            }
        } catch (Exception e) {
            log.error("EnergyTaiBoReportServiceImpl-shiftCellsLeft throw exception:", e);
        }
    }

    // 复制单元格的内容到新的单元格
    private void cloneCell(Cell oldCell, Cell newCell) {
        newCell.setCellComment(oldCell.getCellComment());
        newCell.setCellStyle(oldCell.getCellStyle());

        switch (oldCell.getCellType()) {
            case BOOLEAN:
                newCell.setCellValue(oldCell.getBooleanCellValue());
                break;
            case NUMERIC:
                newCell.setCellValue(oldCell.getNumericCellValue());
                break;
            case STRING:
                newCell.setCellValue(oldCell.getStringCellValue());
                break;
            case FORMULA:
                newCell.setCellFormula(oldCell.getCellFormula());
                break;
            case BLANK:
                // Leave cell blank
                break;
            default:
                break;
        }
    }

    public List<HistoryDatasResult> getHistoryDataMonth(EnergyCustomerTBReportMap equipmentInfo, Date startTime, Date endTime) {
        List<HistoryDatasResult> result = new ArrayList<>();
        try {
            //处理设备id不存在的情况
            if(equipmentInfo == null || equipmentInfo.getEquipmentId() == null || equipmentInfo.getSignalId() == null) {
                return result;
            }
            String selectDuration = "select MIN(\"PointValue\") AS \"result\"  from historydatas where time >=$startTime and time <= $endTime and SignalId= '"
                    + equipmentInfo.getEquipmentId()+"."+equipmentInfo.getSignalId()+"' group by time(1d) ";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(database)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .create();

            query = influxDB.query(queryBuilder);
            if (query != null)
                result = resultMapper.toPOJO(query, HistoryDatasResult.class);
        }catch (Exception e){
            log.error("EnergyTaiBoReportServiceImpl-getHistoryDataMonth throw exception:", e);
        }
        return result;
    }
}
