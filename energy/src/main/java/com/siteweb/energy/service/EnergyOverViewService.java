package com.siteweb.energy.service;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyCustomerConfig;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import org.influxdb.dto.QueryResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface EnergyOverViewService {
    List<StructureOfComplexIndexValue> GetConsumeOfResourceStructure(Integer businessTypeId,List<ResourceStructure> allResourceStructure, Date startTime, Date endTime,String timeType, boolean isFee);
    List<ResourceStructure> GetEnergyOverViewResourceStructure(List<Integer> roleStructureIds);
    List<ResourceStructureMin> GetEnergyOverViewResourceStructureAll(int dimensionTypeId, int personId);
    List<StructureOfComplexIndexValue> GetEnergyOverViewResourceStructureElecConsume(Integer dimensionTypeId , Integer businessTypeId,
                                                                                     Date startTime, Date endTime, String resourceStructureIds, int personId,String timeType,String sequence);
    ComplexIndexValue GetUEByRsourceStructureId(int businessTypeId,String resourceStructureId, Date startTime, Date endTime, String timeType);
    List<StructureOfComplexIndexValue> GetEnergyOverViewResourceStructureChild(int dimensionTypeId,int businessTypeId,String resourceStructureId,Date startTime, Date endTime,String timeType,boolean isFee,String sequence);
    List<ComplexIndexValue> findliveComplexIndexByResourceStructure(int businessTypeId, String resourceStructureId);
    List<PreAlarm> liveEmsPreAlarmByResourceStructure(String resourceStructureId);
    List<PreAlarmHistoryCategoryOrder> hisEmsPreAlarmOrder(String resourceStructureId, Date startTime, Date endTime);
    List<PreAlarmSeverity> getPreAlarmSeverity();
    List<HistoryComplexIndex> hisUEOfResourceId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime);
    Map<String,List<HistoryComplexIndex>> hisEventTypeOfResourceId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime);
    Map<String,List<EnergyHisFeeDataSum>> hisEventTypeFeeOfResourceId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime,String timeType);
    List<StructureOfComplexIndexValue> childEnergyValueOfResourceStructureId(int dimensionTypeId,int businessTypeId, String resourceStructureId, Date startTime, Date endTime, int level,int personId,int topn,boolean isFee);
    int childLevelsOfResourceId(int dimensionTypeId,String resourceStructureId,int personId);
    List<ResourceStructure> GetResourceStructureOfFee(int businessTypeId,int personId);
    String historyComplexIndexSimulator(Integer times, Integer seconds, Integer number, Date date, String complexIndexIds, Double value);
    String energyHisDataSimulator(Integer times, Integer seconds, Integer number, Date date, String complexIndexIds, Double value);
    String energyHisDataSimulatorMonthDayHour(Date startTime, Date endTime, Integer complexIndexId, Double hourValue,boolean isAvg);

    String WriteEnergyHisMonthUEData(Date startTime, Integer complexIndexId, Double value);
    String WriteEnergyHisData(Date startTime, Integer complexIndexId, Double value,String tableName);

    String DeleteEnergyHisData(Date startTime, Integer complexIndexId,String tableName);

    void UpdateStructureOfComplexIndexSumValue(QueryResult query, List<StructureOfComplexIndexValue> paraResult, Boolean isHisDayData);

    void GetConsumeOfStructureList(List<StructureOfComplexIndexValue> paraResult, int businessTypeId, Date startTime, Date endTime, String timeType, boolean isFee);
    /** 查询自定义维度树中所有的根节点流入节点 */
    List<Object> getAllRootInflowNodes(Integer dimensionTypeId);

    Object getPowerRatio(Integer dimensionTypeId, Integer businessTypeId, Date startTime, Date endTime, String timeType,Integer complexIndexDefinitionIds);

    Object getWaterType(String resourceStructureId, Integer dimensionTypeId, Integer businessTypeId, Date startTime, Date endTime, String timeType);

    Object getAirHistogram(String resourceStructureId, Integer dimensionTypeId, Integer businessTypeId, Date startTime, Date endTime, String timeType, int complexIndexDefinitionId );
    boolean getCustomerConfigResult(Integer configId);
    EnergyCustomerConfig getCustomerConfig(Integer configId);
    ComplexIndexValue GetUEByRsourceStructureIdByTimeType(int businessTypeId, String resourceStructureId, String timeType);
    String getUnitByBusinessTypeId(Integer businessTypeId);

    String simulateSheJiYuan();

    Object modifymonthpuehebei(Integer complexIndexId, Double value);
    Object modifyhourdatahebei(Date modifyTime, Date usedTime,Integer hours, Integer tableType);

    Object simulatorenergydata(Date startTime, Date endTime);

    /** 能耗全景图导出 */
    void downLoadExcel(OverViewParameterDTO overViewParameterDTO, HttpServletResponse response);

}
