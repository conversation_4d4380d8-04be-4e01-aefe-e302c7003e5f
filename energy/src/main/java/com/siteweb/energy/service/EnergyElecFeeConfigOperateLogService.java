package com.siteweb.energy.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.energy.dto.ElecFeeScheme;
import com.siteweb.energy.dto.ElecFeeStructure;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.model.ChangeField;

import java.util.Date;
import java.util.List;

public interface EnergyElecFeeConfigOperateLogService {
    /**
     * 查询所有的电费配置操作日志记录
     * @return 查询所得结果集
     */
    List<EnergyElecFeeConfigOperateLog> getAllOperateLog();
    /**
     * 查询所有的电费配置操作日志记录（ChangeContent为空）
     * @return 查询所得结果集
     */
    List<EnergyElecFeeConfigOperateLog> getAllExcludeChangeContentOrderBy();

    /** 比对方案主体信息，返回有变动的字段 */
    List<ChangeField> diffSchemeTrunk(EnergyElecFeeScheme schemeDb, boolean appliedRangeHasChange);
    /** 比对阶梯定价主体信息，返回有变动的字段 */
    List<ChangeField> diffStepPrice(EnergyElecFeeStepPrice newStepPrice);
    /** 比对峰平谷主体信息，返回有变动的字段 */
    List<ChangeField> diffFeeFpg(EnergyElecFeeFpg fpgDb);
    /** 比对峰平谷Value信息，返回有变动的字段 */
    List<ChangeField> diffFeeFpgValue(EnergyElecFeeFpgValue fpgValueDb);

    /** 保存方案主体被修改的相关操作日志 */
    void saveSchemeTrunkUpdateLog(EnergyElecFeeScheme schemeDb, List<ChangeField> changeFieldList, Date curDate, Integer userId, String userName);
    /** 保存应用范围被修改的相关操作日志 */
    void saveStructureMapUpdateLog(EnergyElecFeeScheme schemeDb, List<EnergyElecFeeSchemeStructureMap> oldMaps, List<ElecFeeStructure> newMaps, Date curDate, Integer userId, String userName);
    /** 保存阶梯定价被修改的相关操作日志 */
    void saveStepPriceUpdateLog(EnergyElecFeeStepPrice newStepPrice, List<ChangeField> changeFieldList, Date curDate, Integer userId, String userName);
    /** 保存峰平谷被修改的相关操作日志 */
    void saveFpgUpdateLog(EnergyElecFeeFpg fpgDb, List<ChangeField> changeFieldFpgList, Date updateDate, Integer userId, String userName);
    /** 保存峰平谷value被修改的相关操作日志 */
    void saveFpgValueUpdateLog(EnergyElecFeeFpgValue fpgDb, List<ChangeField> changeFieldFpgList, Date updateDate, Integer userId, String userName);

    /** 保存方案新增的相关操作日志 */
    void saveSchemeInsertLog(ElecFeeScheme schemeObj, Date curDate, Integer userId, String userName) throws JsonProcessingException;
    /** 保存阶梯定价新增的相关操作日志 */
    void saveStepPriceInsertLog(EnergyElecFeeStepPrice spDb, List<EnergyElecFeeFpgValue> fpgValueSaveDbs, Date curDate, Integer userId, String userName) throws JsonProcessingException;
    /** 保存fpg新增的相关操作日志 */
    void saveFpgInsertLog(EnergyElecFeeFpg fpgDb, List<EnergyElecFeeFpgValue> vleSaveList, Date curDate, Integer userId, String userName) throws JsonProcessingException;

    /** 保存方案删除的相关操作日志 */
    void saveSchemeDeleteLog(ElecFeeScheme schemeWithStructureMaps, Date curDate, Integer userId, String userName) throws JsonProcessingException;
    /** 保存阶梯定价删除的相关操作日志 */
    void saveStepPriceDeleteLog(EnergyElecFeeStepPrice delStepPrice, Date curDate, Integer userId, String userName) throws JsonProcessingException;
    /** 保存峰平谷值 删除的相关操作日志 */
    void saveFpgValueDeleteLog(EnergyElecFeeStepPrice delStepPrice, EnergyElecFeeFpgValue delFpgValue, Date curDate, Integer userId, String userName) throws JsonProcessingException;
    /** 保存峰平谷 删除的相关操作日志 */
    void saveFpgDeleteLog(EnergyElecFeeFpg delFpg, Date curDate, Integer userId, String userName) throws JsonProcessingException;

    /** 获取操作日志详情 */
    ResultObject<String> getLogDetail(Integer logId);
}
