package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.complexindex.mapper.ComplexIndexMapper;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyErrorDataModifyRecord;
import com.siteweb.energy.entity.EnergyHisErrorData;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyDataAuditMapper;
import com.siteweb.energy.service.EnergyAppService;
import com.siteweb.energy.service.EnergyDataAuditService;
import com.siteweb.energy.service.EnergyHistoryDataService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmCategory;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.prealarm.service.PreAlarmCategoryService;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import com.siteweb.utility.entity.GoCronExpression;
import com.siteweb.utility.service.GoCronExpressionService;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
@Slf4j
public class EnergyDataAuditServiceImpl implements EnergyDataAuditService {
    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    @Autowired
    private InfluxDB influxDB;

    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    private ComplexIndexBusinessTypeService complexIndexBusinessTypeService;

    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;

    @Autowired
    private GoCronExpressionService goCronExpressionService;

    @Autowired
    private EnergyDataAuditMapper energyDataAuditMapper;
    @Autowired
    private ConfigSignalManager configSignalManager;

    @Autowired
    private ComplexIndexMapper complexIndexMapper;

    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    private PreAlarmService preAlarmService;
    @Autowired
    private PreAlarmMapper preAlarmMapper;
    @Autowired
    private PreAlarmCategoryService preAlarmCategoryService;
    @Autowired
    private PreAlarmSeverityService preAlarmSeverityService;

    @Autowired
    private EnergyHistoryDataService energyHistoryDataService;

    @Autowired
    private LiveComplexIndexManager liveComplexIndexManager;

    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    HistorySignalManager historySignalManager;

    @Autowired
    EnergyAppService energyAppService;

    @Override
    public Map<String, List<?>> getEnergyHisErrorData(Date startTime, Date endTime, String businessTypeId) {
        List<EnergyHisErrorDataDTO> tableList = new ArrayList<>();
        Map<String, List<?>> resultData = new HashMap<>();
        try {
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder typeBuilder = new StringBuilder();
            if(businessTypeId.contains(",")) {
                String[] typeStrings = businessTypeId.split(",");
                for (String typeStr : typeStrings) {
                    if (typeBuilder.isEmpty()) {
                        typeBuilder.append(" BusinessTypeId='" + typeStr + "' ");
                    } else {
                        typeBuilder.append(" or BusinessTypeId='" + typeStr + "' ");
                    }
                }
            } else {
                typeBuilder.append(" BusinessTypeId='" + businessTypeId + "' ");
            }
            String selectStr = "select * from EnergyHisErrorData where time >= $startTime and time <= $endTime and ( $businessTypeIds )";
            selectStr = selectStr.replace("$businessTypeIds", typeBuilder.toString());
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectStr)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .create();
            QueryResult query = influxDB.query(queryBuilder);
            if(query != null) {
                List<EnergyHisErrorData> dataList = resultMapper.toPOJO(query, EnergyHisErrorData.class);
                if (dataList != null && !dataList.isEmpty()) {
                    Map<String, Integer> countMap = new HashMap<>();
                    for (EnergyHisErrorData oneData: dataList) {
                        String complexIndexId = oneData.getComplexIndexId();
                        countMap.put(complexIndexId, countMap.getOrDefault(complexIndexId, 0) + 1);

                        EnergyHisErrorDataDTO data = new EnergyHisErrorDataDTO();
                        data.setComplexIndexId(Integer.valueOf(complexIndexId));
                        Optional<ComplexIndex> findComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(complexIndexId))).findFirst();
                        if (findComplexIndex.isPresent()) {
                            ComplexIndex item = findComplexIndex.get();
                            Integer objectId = item.getObjectId();
                            String level = resourceStructureManager.getFullPath(objectId);
                            data.setComplexIndexLevel(level);
                            data.setComplexIndexName(item.getComplexIndexName());
                            data.setErrorTime(oneData.getTime());
                            data.setOriginalValue(oneData.getOriginalValue());
                            data.setIndexValue(oneData.getIndexValue());
                            data.setUnit(oneData.getUnit());
                            data.setExpression(item.getExpression());
                        }
                        tableList.add(data);
                    }
                    List<EnergyHisErrorBarDataDTO> barList = getBarList(countMap);
                    resultData.put("tableList", tableList);
                    resultData.put("barList", barList);
                }
            }
        }catch (Exception ex) {
            log.error("能管数据稽查总览异常数据查询错误getEnergyHisErrorData；", ex);
            return null;
        }
        return resultData;
    }

    @NotNull
    private List<EnergyHisErrorBarDataDTO> getBarList(Map<String, Integer> countMap) {
        List<EnergyHisErrorBarDataDTO> list = new ArrayList<>();
        try {
            if (countMap != null && !countMap.isEmpty()) {
                for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
                    EnergyHisErrorBarDataDTO barData = new EnergyHisErrorBarDataDTO();
                    barData.setComplexIndexId(entry.getKey());
                    barData.setErrorCount(entry.getValue());
                    Optional<ComplexIndex> findComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(entry.getKey()))).findFirst();
                    if (findComplexIndex.isPresent()) {
                        ComplexIndex item = findComplexIndex.get();
                        Integer objectId = item.getObjectId();
                        String level = resourceStructureManager.getFullPath(objectId);
                        String name = level + "_" + item.getComplexIndexName();
                        barData.setName(name);
                    }
                    list.add(barData);
                }
            }
        } catch (Exception ex) {
            log.error("能管数据稽查总览柱状图异常数据查询错误getBarList；", ex);
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public Integer insertErrorDataModifyRecord(EnergyErrorDataModifyRecord record) {
        energyHistoryDataService.modifyEnergyHisHourDataInfluxdb(record.getComplexIndexId(),record.getErrorTime(),record.getIndexValue());
        return energyDataAuditMapper.insert(record);
    }

    @Override
    public List<EnergyErrorDataModifyRecord> getErrorDataModifyRecord(Integer complexIndexId, Date errorTime) {
        List<EnergyErrorDataModifyRecord> recordList = energyDataAuditMapper.selectList(new QueryWrapper<EnergyErrorDataModifyRecord>().eq("ComplexIndexId", complexIndexId)
                .and(wrapper -> wrapper.eq("ErrorTime", errorTime)));
        if (recordList != null && !recordList.isEmpty()) {
            recordList = recordList.stream()
                    .sorted(Comparator.comparing(EnergyErrorDataModifyRecord::getModifyTime).reversed())
                    .collect(Collectors.toList());
            return recordList;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<EnergyComplexCheckDTO> getComplexNotExist(String objectIds) {
        if (objectIds == null || objectIds.isEmpty()) {
            return new ArrayList<>();
        }
        Collection<Integer> ids = stringToCollection(objectIds);
        return getComplexError(ids);
    }

    @Override
    public void EnergyComplexCheck() {
        try {
            List<Integer> ids =  resourceStructureManager.getAllObjectIds();
            if (ids == null || ids.isEmpty()) {
                log.error("EnergyComplexCheck: 层级Id为空");
                return;
            }
            List<EnergyComplexCheckDTO> complexList = getComplexError(ids);
            PreAlarmSeverity preAlarmSeverity = preAlarmSeverityService.findByPreAlarmSeverityId(2);
            PreAlarmCategory preAlarmCategory = preAlarmCategoryService.findByCategoryId(8);
            for (EnergyComplexCheckDTO complex: complexList) {
                List<PreAlarm> existPreAlarm = preAlarmService.findAlarms(complex.getComplexIndexId(), complex.getObjectTypeId()).stream()
                        .filter(p -> p.getUniqueId()!= null && p.getUniqueId().equals(String.valueOf(complex.getComplexIndexId())) && p.getEndTime() == null && p.getConfirmTime() == null)
                        .toList();
                //存在该指标的告警
                if (!existPreAlarm.isEmpty()) {
                    PreAlarm preAlarm = existPreAlarm.get(0);
                    if (preAlarm == null) {
                        log.error("CreateNewPreAlarm error preAlarm is null");
                        continue;
                    }
                    preAlarm.setSampleTime(new Date());
                    preAlarm.setTriggerValue(complex.getErrorReason());
                    preAlarmMapper.updateById(preAlarm);
                    preAlarmService.addToCache(preAlarm);
                    continue;
                }
                PreAlarm preAlarm = new PreAlarm();
                preAlarm.setPreAlarmPointId(-1);
                preAlarm.setMeanings(complex.getComplexIndexName() + "," + complex.getErrorReason());
                preAlarm.setPreAlarmSeverity(2);
                preAlarm.setPreAlarmSeverityName("二级预警");
                preAlarm.setPreAlarmCategory(preAlarmCategory.getCategoryId());
                preAlarm.setPreAlarmCategoryName(preAlarmCategory.getCategoryName());
                preAlarm.setColor(preAlarmSeverity.getColor());
                preAlarm.setUniqueId(String.valueOf(complex.getComplexIndexId()));
                preAlarm.setUniqueName(complex.getComplexIndexName());
                preAlarm.setObjectId(complex.getComplexIndexId());
                preAlarm.setObjectTypeId(complex.getObjectTypeId());
                preAlarm.setResourceStructureId(complex.getObjectId());
                ResourceStructure resourceStructure =  resourceStructureManager.getResourceStructureById(complex.getObjectId());
                preAlarm.setObjectName(resourceStructure.getResourceStructureName());
                preAlarm.setLevelOfPath(resourceStructure.getLevelOfPath());
                preAlarm.setLevelOfPathName(complex.getComplexIndexLevelName());
                preAlarm.setTriggerValue(complex.getErrorReason());
                preAlarm.setSampleTime(new Date());
                preAlarm.setStartTime(new Date());
                preAlarm.setBusinessTypeId(complex.getBusinessTypeId());
                preAlarmMapper.insert(preAlarm);
                preAlarmService.addToCache(preAlarm);
            }
        } catch (Exception e) {
            log.error("EnergyComplexCheck createNewPreAlarm error", e);
        }
    }

    public List<EnergyComplexCheckDTO> getComplexError(Collection<Integer> objectIds) {
        List<EnergyComplexCheckDTO> dataList = new ArrayList<>();
        try {
            List<ResourceStructure> resourceStructureList = resourceStructureManager.getResourceStructureByIds(objectIds);
            for (ResourceStructure item: resourceStructureList) {
                List<ComplexIndex> complexList = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i-> i.getObjectId().equals(item.getResourceStructureId()) && i.getObjectTypeId().equals(item.getStructureTypeId())
                        && i.getExpression()!= null && !i.getExpression().isEmpty()).toList();
                for (ComplexIndex complexItem: complexList) {
                    EnergyComplexCheckDTO data = new EnergyComplexCheckDTO();
                    Pattern p = Pattern.compile("(?<=cp\\()[^)]+", Pattern.CASE_INSENSITIVE);
                    Matcher m = p.matcher(complexItem.getExpression().toLowerCase());
                    StringBuilder sb = new StringBuilder();
                    while (m.find()) {
                        if (m.group().split("\\.").length == 2) {
                            Integer equipmentId = Integer.parseInt(m.group().split("\\.")[0]);
                            Integer signalId = Integer.parseInt(m.group().split("\\.")[1]);
                            if (configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId,signalId) == null){
                                sb.append(equipmentId);
                                sb.append(",");
                            }
                        }
                    }
                    if (!sb.isEmpty()) {
                        int lastIndex = sb.length() - 1;
                        sb.deleteCharAt(lastIndex);
                        data.setErrorReason("设备不存在:" + sb.toString());
                        data.setComplexIndexId(complexItem.getComplexIndexId());
                        data.setComplexIndexName(complexItem.getComplexIndexName());
                        Integer objectId = complexItem.getObjectId();
                        String levelName = resourceStructureManager.getFullPath(objectId);
                        data.setObjectId(objectId);
                        data.setObjectTypeId(complexItem.getObjectTypeId());
                        data.setComplexIndexLevelName(levelName);
                        ComplexIndexBusinessType businessType = complexIndexBusinessTypeService.findById(complexItem.getBusinessTypeId());
                        data.setBusinessTypeId(complexItem.getBusinessTypeId());
                        data.setBusinessTypeName(businessType.getBusinessTypeName());
                        data.setExpression(complexItem.getExpression());

                        dataList.add(data);
                    }
                }
            }

        } catch (Exception ex) {
            log.error("指标稽查异常数据查询错误getComplexError；", ex);
            return new ArrayList<>();
        }
        return dataList;
    }

    private Collection<Integer> stringToCollection(String idsString) {
        Collection<Integer> idsCollection = new ArrayList<>();
        try {
            if ((idsString.contains(","))) {
                String[] idStrings = idsString.split(",");
                for (String idStr : idStrings) {
                    int number = Integer.parseInt(idStr.trim());
                    idsCollection.add(number);
                }
            } else {
                int number = Integer.parseInt(idsString.trim());
                idsCollection.add(number);
            }
        } catch (Exception ex) {
            log.error("指标稽查无效的数字字符串stringToCollection：", ex);
        }
        return idsCollection;
    }

    @Override
    public List<ComplexIndex> getComplexList(Integer objectId, Integer objectTypeId, String businessTypeId) {
//        List<EnergyComplexCheckDTO> resultList = new ArrayList<>();
        List<ComplexIndex> complexList = new ArrayList<>();
        try {
            String levelName = resourceStructureManager.getFullPath(objectId);
            List<ComplexIndex> complexListAll = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getObjectId().equals(objectId)
              && i.getObjectTypeId().equals(objectTypeId)).toList();
            List<String> typeIds;
            if (businessTypeId.contains(",")) {
                typeIds = Arrays.asList(businessTypeId.split(","));
            } else {
                typeIds = new ArrayList<>();
                typeIds.add(businessTypeId);
            }
            complexList = GetComplexListByBusinessTypeId(complexListAll, typeIds);
//            for (ComplexIndex item: complexList) {
//                EnergyComplexCheckDTO data = getEnergyComplexCheckDTO(item, levelName);
//                resultList.add(data);
//            }
        } catch (Exception ex) {
            log.error("指标拓扑根据层级节点获取指标信息查询错误getComplexList；", ex);
            return new ArrayList<>();
        }
        return complexList;
    }

    private List<ComplexIndex> GetComplexListByBusinessTypeId(List<ComplexIndex> complexListAll, List<String> typeIds) {
        List<ComplexIndex> findComplex = new ArrayList<>();
        for (ComplexIndex index: complexListAll) {
            if (index.getBusinessTypeId() != null) {
                if (typeIds.contains(index.getBusinessTypeId().toString())) {
                    findComplex.add(index);
                }
            }
        }
        return findComplex;
    }

    public EnergyComplexCheckDTO getComplexInfo(String complexIndexId) {
        EnergyComplexCheckDTO result = new EnergyComplexCheckDTO();
        try {
            Integer complexId = Integer.valueOf(complexIndexId);
            Optional<ComplexIndex> findComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(complexId)).findFirst();
            if (findComplexIndex.isPresent()) {
                ComplexIndex item = findComplexIndex.get();
                result = getEnergyComplexCheckDTO(item);
            }
        } catch (Exception ex) {
            log.error("指标拓扑计算周期和存储周期翻译错误GetCronDesc；", ex);
            return null;
        }
        return result;
    }

    private EnergyComplexCheckDTO getEnergyComplexCheckDTO(ComplexIndex item) {
        EnergyComplexCheckDTO data = new EnergyComplexCheckDTO();
        try {
            data.setComplexIndexId(item.getComplexIndexId());
            data.setComplexIndexName(item.getComplexIndexName());
            data.setObjectId(item.getObjectId());
            data.setBusinessTypeId(item.getBusinessTypeId());
            data.setBusinessTypeName(item.getBusinessTypeName());
            data.setExpression(item.getExpression());
            data.setCalcType(item.getCalcType());
            data.setCalcCron(item.getCalcCron());
            data.setSaveCron(item.getSaveCron());
            if (item.getCalcType() != null) {
                String calcTypeDesc = item.getCalcType() == 0 ? "否" : "是";
                data.setCalcTypeDesc(calcTypeDesc);
            }
            String calcCronDesc = GetCronDesc(item.getCalcCron());
            String saveCronDesc = GetCronDesc(item.getSaveCron());
            data.setCalcCronDesc(calcCronDesc);
            data.setSaveCronDesc(saveCronDesc);
            String expressionDesc = GetExpressionDesc(item.getExpression());
            data.setExpressionDesc(expressionDesc);
            data.setUnit(item.getUnit());
        } catch (Exception ex) {
            log.error("指标拓扑计算周期和存储周期翻译错误GetCronDesc；", ex);
            return null;
        }
        return data;
    }

    @NotNull
    private String GetCronDesc(String cron) {
        String cronDesc = "";
        try {
            Optional<GoCronExpression> findExpression = goCronExpressionService.findAll().stream().filter(p -> p.getExpression()!= null && p.getExpression().equals(cron)).findFirst();
            if (findExpression.isPresent()) {
                GoCronExpression goCronExpression = findExpression.get();
                cronDesc = goCronExpression.getExpressionDescription();
            }
        } catch (Exception ex) {
            log.error("指标拓扑计算周期和存储周期翻译错误GetCronDesc；", ex);
            return "";
        }
        return cronDesc;
    }

    @NotNull
    private String GetExpressionDesc(@NotNull String expression) {
        if (expression.isEmpty()) return "";
        try {
//            Pattern pattern = Pattern.compile("(CP|CI)\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
//            Matcher matcher = pattern.matcher(expression);
//            StringBuilder result = new StringBuilder();
//            while (matcher.find()) {
//                String replaced = replaceExpressDesc(matcher.group(), matcher.group(2));
//                matcher.appendReplacement(result, matcher.group(1) + "(" + replaced + ")");
//            }
//            matcher.appendTail(result);
//            return result.toString();

            Pattern p = expression.toLowerCase().contains("ci") ?
                    Pattern.compile("(?<=ci\\()[^)]+", Pattern.CASE_INSENSITIVE) :
                    Pattern.compile("(?<=cp\\()[^)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(expression);
            if (m.find()) {
                return m.replaceAll(match -> replaceExpressDesc(expression, match.group()));
            } else {
                return "";
            }
        } catch (Exception ex) {
            log.error("指标拓扑表达式翻译错误GetExpressionDesc；", ex);
            return "";
        }
    }

    private String replaceExpressDesc(String expression,String groupStr) {
        try {
            if(expression.toLowerCase().contains("ci")) {
                Integer complexId = Integer.valueOf(groupStr);
                Optional<ComplexIndex> findComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(complexId)).findFirst();
                if (findComplexIndex.isPresent()) {
                    ComplexIndex item = findComplexIndex.get();
                    return item.getComplexIndexName() != null ? item.getComplexIndexName() : complexId.toString();
                }
            } else {
                StringBuilder builder = new StringBuilder();
                if (groupStr.split("\\.").length == 2) {
                    Integer equipmentId = Integer.parseInt(groupStr.split("\\.")[0]);
                    Integer signalId = Integer.parseInt(groupStr.split("\\.")[1]);
                    Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
                    String equipmentStr = equipment != null ? equipment.getEquipmentName() : equipmentId.toString();
                    builder.append(equipmentStr);
                    ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId,signalId);
                    String signalStr = configSignalItem != null ? configSignalItem.getSignalName() : signalId.toString();
                    builder.append(".");
                    builder.append(signalStr);
                    return builder.toString();
                }
            }
        } catch (Exception ex) {
            log.error("指标拓扑表达式翻译错误replaceExpressDesc；", ex);
            return "";
        }
        return "";
    }

    @Override
    public EnergyComplexTree getComplexTree(Integer complexIndexId, String complexIndexName, String expression) {
        if(!expression.isEmpty()) {
            String expressStr = expression;
            if (expression.toLowerCase().contains("ci")) {
                expressStr = getCiExpress(expression);
            }
            Map<Integer, List<Integer>> complexMap = getCpExpressMap(expressStr);
            return getComplexTree(complexMap, complexIndexId, complexIndexName);
        } else {
            return null;
        }
    }

    @NotNull
    private Map<Integer, List<Integer>> getCpExpressMap(String expression) {
        Map<Integer, List<Integer>> expressMap = new HashMap<>();
        try {
            Pattern p = Pattern.compile("(?<=cp\\()[^)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(expression);
            while (m.find()) {
                if (m.group().split("\\.").length == 2) {
                    Integer equipmentId = Integer.parseInt(m.group().split("\\.")[0]);
                    Integer signalId = Integer.parseInt(m.group().split("\\.")[1]);
                    if (!expressMap.containsKey(equipmentId)) {
                        expressMap.put(equipmentId, new ArrayList<>());
                    }
                    expressMap.get(equipmentId).add(signalId);
                }
            }
        } catch (Exception ex) {
            log.error("构造指标拓扑发生错误getCpExpressMap；", ex);
            return new HashMap<>();
        }
        return expressMap;
    }

    private String getCiExpress(String expression) {
        String result = "";
        StringBuilder builder = new StringBuilder();
        try {
            if(expression.contains("+")) {
                String[] array = expression.split("\\+");
                for (String str: array) {
                    if (str.toLowerCase().contains("cp")) {
                        builder.append(str);
                        builder.append("+");
                    }
                }
            }
            Pattern p = Pattern.compile("(?<=ci\\()[^)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(expression);
            while (m.find()) {
                String ee = m.group();
                Integer complexId = Integer.valueOf(ee);
                Optional<ComplexIndex> findComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(complexId)).findFirst();
                if (findComplexIndex.isPresent()) {
                    ComplexIndex item = findComplexIndex.get();
                    if (!item.getExpression().isEmpty()) {
                        builder.append(item.getExpression());
                        builder.append("+");
                    }
                }
            }
            if (builder.toString().toLowerCase().contains("ci")) {
                result = getCiExpress(builder.substring(0, builder.length()-1));
            } else {
                result = builder.substring(0, builder.length()-1);
            }
        } catch (Exception ex) {
            log.error("构造指标拓扑发生错误getCiExpress；", ex);
            return "";
        }
        return result;
    }

    private EnergyComplexTree getComplexTree(Map<Integer, List<Integer>> expressMap, Integer complexIndexId, String complexIndexName) {
        EnergyComplexTree root = new EnergyComplexTree(0, complexIndexId, complexIndexName, 0);
        try {
            for (Map.Entry<Integer, List<Integer>> entry: expressMap.entrySet()) {
                Integer equipmentId = entry.getKey();
                Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
                String equipmentStr = equipment != null ? equipment.getEquipmentName() : equipmentId.toString();
                buildTree(root, complexIndexId, equipmentId, equipmentStr, 1);
                List<Integer> signalIds = entry.getValue();
                Optional<EnergyComplexTree> findTreeNode = root.getChildren().stream().filter(i -> i.getId().equals(equipmentId)).findFirst();
                if (findTreeNode.isPresent()) {
                    EnergyComplexTree treeNode = findTreeNode.get();
                    for (Integer signalId : signalIds) {
                        ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalId);
                        String signalStr = configSignalItem != null ? configSignalItem.getSignalName() : signalId.toString();
                        buildTree(treeNode, equipmentId, signalId, signalStr, 2);
                    }
                }
            }
        } catch (Exception ex) {
            log.error("构造指标拓扑发生错误getComplexTree；", ex);
            return null;
        }
        return root;
    }

    private void buildTree(@NotNull EnergyComplexTree root, Integer parentId, Integer id, String name, Integer type) {
        EnergyComplexTree node = new EnergyComplexTree(parentId,id, name, type);
        root.addChild(node);
    }


    @Override
    public List<EnergyAppResultDTO> getcomplexListAndCurve(Date startTime, Date endTime, Integer complexIndexId) {
        List<EnergyAppResultDTO> tableList = new ArrayList<>();
        try {
            ComplexIndex findComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(complexIndexId)).findFirst().orElse(null);
            if (findComplexIndex == null) return null;
            BusinessDefinitionMap bdMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                    i -> i.getComplexIndexDefinitionId().equals(findComplexIndex.getComplexIndexDefinitionId())).findFirst().orElse(null);
            if (bdMap != null) {
                EnergyAppParameterDTO param = new EnergyAppParameterDTO();
                Integer ComplexIndexDefinitionTypeId = bdMap.getComplexIndexDefinitionTypeId();
                param.setItemId(ComplexIndexDefinitionTypeId);
                param.setComplexIndexIds(String.valueOf(complexIndexId));
                param.setStartTime(startTime);
                param.setEndTime(endTime);
                param.setTimeType("h");

                tableList = energyAppService.getEnergyTrend(param);
            }
        }catch (Exception ex) {
            log.error("指标拓扑获取指标列表和曲线数据发生错误getcomplexListAndCurve；", ex);
            return null;
        }
        return tableList;
    }


    @Override
    public Map<String, Object> getSignalListAndCurve(Date startTime, Date endTime, Integer equipmentId, Integer signalId, String signalName) {
        Map<String, Object> resultData = new HashMap<>();
        try {
            List<Integer> signalIds = Collections.singletonList(signalId);
            List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
            if (signalList != null && !signalList.isEmpty()) {
                String currentValue = signalList.get(0).getCurrentValue();
                resultData.put("signalValue",currentValue);
            }
            List<HistorySignal> historySignals = historySignalManager.findHistorySignalByDuration(startTime, endTime, equipmentId, signalId, null, false);
            List<EnergyHistorySignalDTO> tableList = new ArrayList<>();
            for (HistorySignal item: historySignals) {
                EnergyHistorySignalDTO data = new EnergyHistorySignalDTO();
                data.setSignalId(item.getSignalId());
                data.setSignalName(signalName);
                data.setTime(item.getTime());
                data.setValue(item.getPointValue());
                tableList.add(data);
            }
            resultData.put("tableList",tableList);
        }catch (Exception ex) {
            log.error("指标拓扑获取测点的列表和曲线数据发生错误getSignalListAndCurve；", ex);
            return null;
        }
        return resultData;
    }
}
