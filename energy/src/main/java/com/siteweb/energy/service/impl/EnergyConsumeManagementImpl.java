package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.complexindex.dto.ComplexIndexQueryResult;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.energy.dto.EnergyConsumeDataParamsDTO;
import com.siteweb.energy.dto.EnergyConsumeHistoryDTO;
import com.siteweb.energy.dto.EnergyHisMonthDataResult;
import com.siteweb.energy.entity.EnergyConsumeConst;
import com.siteweb.energy.entity.EnergyConsumeData;
import com.siteweb.energy.mapper.EnergyConsumeDataMapper;
import com.siteweb.energy.service.EnergyConsumeManagementService;
import com.siteweb.energy.mapper.EnergyConsumeConstMapper;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.influxdb.InfluxDB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.siteweb.energy.manager.EnergyComplexIndexManager;

import java.util.*;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
public class EnergyConsumeManagementImpl implements EnergyConsumeManagementService {


    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String energyDatabase;

    @Autowired
    private EnergyConsumeConstMapper energyConsumeConstMapper;

    @Autowired
    private EnergyConsumeDataMapper energyConsumeDataMapper;

    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;

    @Override
    public EnergyConsumeConst findEnergyConsumeConst(String objectId, String objectTypeId) {
        QueryWrapper<EnergyConsumeConst> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", objectId);
        wrapper.eq("objectTypeId", objectTypeId);
        return energyConsumeConstMapper.selectOne(wrapper);
    }

    @Override
    public Integer saveEnergyConsumeConst(EnergyConsumeConst energyConsumeConst) {
        QueryWrapper<EnergyConsumeConst> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", energyConsumeConst.getObjectId());
        wrapper.eq("objectTypeId", energyConsumeConst.getObjectTypeId());
        EnergyConsumeConst findEnergyConsumeConst = energyConsumeConstMapper.selectOne(wrapper);
        Integer res = null;
        if (findEnergyConsumeConst == null) {
            res = energyConsumeConstMapper.insert(energyConsumeConst);
        } else {
            res = energyConsumeConstMapper.update(energyConsumeConst, wrapper);
        }
        return res;
    }

    @Override
    public List<EnergyConsumeData> findEnergyConsumeData(String objectId, String objectTypeId, Integer energyTypeId, Integer year) {
        QueryWrapper<EnergyConsumeData> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", objectId);
        wrapper.eq("objectTypeId", objectTypeId);
        wrapper.eq("energyTypeId", energyTypeId);
        wrapper.eq("year", year);
        return energyConsumeDataMapper.selectList(wrapper);
    }

    @Override
    public EnergyConsumeData findEnergyConsumeDataYoY(String objectId, String objectTypeId, Integer energyTypeId, Integer year ,Integer month) {
        QueryWrapper<EnergyConsumeData> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", objectId);
        wrapper.eq("objectTypeId", objectTypeId);
        wrapper.eq("energyTypeId", energyTypeId);
        wrapper.eq("year", year-1);
        wrapper.eq("month", month);
        List<EnergyConsumeData> energyConsumeData = energyConsumeDataMapper.selectList(wrapper);
        if (energyConsumeData.isEmpty()){
            return new EnergyConsumeData(-1,objectId,objectTypeId,energyTypeId,year-1,month,0d,0d,0d,0d,0d);
        }else {
            return energyConsumeData.get(0);
        }
    }

    @Override
    public Integer[] findEnergyConsumeYears(String objectId, String objectTypeId, Integer energyTypeId) {
        Set<Integer> set = new HashSet<>();
        QueryWrapper<EnergyConsumeData> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", objectId);
        wrapper.eq("objectTypeId", objectTypeId);
        wrapper.eq("energyTypeId", energyTypeId);
        List<EnergyConsumeData> energyConsumeData = energyConsumeDataMapper.selectList(wrapper);
        for (EnergyConsumeData item:energyConsumeData){
            set.add(item.getYear());
        }
        Integer[] res = (Integer[]) set.toArray(new Integer[set.size()]);
        Arrays.sort(res);
        return res;
    }

    private Integer findComplexIndexIdByParams(String objectId, String objectTypeId, Integer energyTypeId) {
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        for (ComplexIndex item : allTotalConsumeComplexIndex) {
            if (item.getObjectId().equals(Integer.valueOf(objectId)) && item.getObjectTypeId().equals(Integer.valueOf(objectTypeId)) && item.getBusinessTypeId().equals(energyTypeId)) {
                return item.getComplexIndexId();
            }

        }
        return null;
    }


    private Double findSumValueOfHistoryComplexIndexByIdAndDuration(Date startTime, Date endTime, Integer complexIndexId) {

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("select sum(IndexValue) as result  from EnergyHisMonthData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId and Abnormal = '0'")
                .forDatabase(energyDatabase)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .bind("complexIndexId", complexIndexId.toString())
                .create();

        query =  influxDB.query(queryBuilder);
        if (query != null) {
            List<EnergyHisMonthDataResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
            if (!resultComplexIndexQuery.isEmpty()) {
                return Double.parseDouble(resultComplexIndexQuery.get(0).result);
            }
        }
        return null;
    }

    private List<Date> getDateRangeByYearAndMonth(Integer year, Integer month) {
        List<Date> res = new ArrayList<>();
        Calendar start = Calendar.getInstance();
        start.set(Calendar.YEAR, year);
        start.set(Calendar.MONTH, month - 1);
        int firstDay = start.getActualMinimum(Calendar.DAY_OF_MONTH);
        start.set(Calendar.DAY_OF_MONTH, firstDay);
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        Date firstDayOfMonth = start.getTime();

        Calendar end = Calendar.getInstance();
        end.set(Calendar.YEAR, year);
        end.set(Calendar.MONTH, month - 1);
        int lastDay = end.getActualMaximum(Calendar.DAY_OF_MONTH);
        end.set(Calendar.DAY_OF_MONTH, lastDay);
        end.set(Calendar.HOUR_OF_DAY, 23);
        end.set(Calendar.MINUTE, 59);
        end.set(Calendar.SECOND, 59);
        Date lastDayOfMonth = end.getTime();
        res.add(firstDayOfMonth);
        res.add(lastDayOfMonth);
        return res;
    }


    @Override
    public List<EnergyConsumeHistoryDTO> findEnergyConsumeHistoryData(String objectId, String objectTypeId, Integer energyTypeId, Integer year) {
        List<EnergyConsumeHistoryDTO> res = new ArrayList<>();
        Integer complexIndexId = findComplexIndexIdByParams(objectId, objectTypeId, energyTypeId);
        if (complexIndexId == null) {
            return null;
        }
        for (int n = 1; n <= 12; n = n + 1) {
            List<Date> date = getDateRangeByYearAndMonth(year, n);
            Double value = findSumValueOfHistoryComplexIndexByIdAndDuration(date.get(0), date.get(1), complexIndexId);
            if (value == null) {
                value = 0.00;
            } else {
                value = Double.valueOf(String.format("%.2f", value));
            }
            EnergyConsumeHistoryDTO historyDTO = new EnergyConsumeHistoryDTO();
            historyDTO.setObjectId(objectId);
            historyDTO.setObjectTypeId(objectTypeId);
            historyDTO.setEnergyTypeId(energyTypeId);
            historyDTO.setMonth(String.valueOf(n));
            historyDTO.setYear(year);
            historyDTO.setTotalValue(value);
            res.add(historyDTO);
        }
        return res;
    }

    @Override
    public Integer saveEnergyConsumeData(EnergyConsumeDataParamsDTO energyConsumeDataParamsDTO) {
        List<EnergyConsumeData> energyConsumeData = energyConsumeDataParamsDTO.getEnergyConsumeDataList();
        Integer res = 1;
        for (EnergyConsumeData e : energyConsumeData) {
            res = res & energyConsumeDataMapper.insert(e);
        }
        return res;
    }

    @Override
    public Integer upDateEnergyConsumeData(EnergyConsumeData energyConsumeData) {
        return energyConsumeDataMapper.updateById(energyConsumeData);
    }

    @Override
    public Integer deleteEnergyConsumeData(Integer id) {
        EnergyConsumeData energyConsumeData = energyConsumeDataMapper.selectById(id);
        energyConsumeData.setPlanValue(0d);
        energyConsumeData.setOverstepValue(0d);
        energyConsumeData.setEnergySavingValue(0d);
        energyConsumeData.setOverstepValuePreAlarm(0d);
        energyConsumeData.setPlanValuePreAlarm(0d);
        return energyConsumeDataMapper.updateById(energyConsumeData);
    }

}
