package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyCarbonManageDTO;
import com.siteweb.energy.dto.EnergyCarbonManageDataParamsDTO;
import com.siteweb.energy.entity.EnergyCarbonManegePara;

import java.util.List;

public interface EnergyCarbonQuotaManagementService {

    List<Integer> findCarbonQuotaYears(String objectId, String objectTypeId);

    List<EnergyCarbonManegePara> findCarbonQuotaData(String objectId, String objectTypeId, Integer year);

    String saveCarbonQuotaData(EnergyCarbonManageDataParamsDTO energyConsumeDataParamsDTO);

    String updateCarbonQuotaData(List<EnergyCarbonManageDTO> list,float yearPlanTotalValue);

    Integer deleteCarbonQuotaData(Integer id);

    List<EnergyCarbonManegePara> findCarbonQuotaHistoryData(String objectId, String objectTypeId, Integer year);

}
