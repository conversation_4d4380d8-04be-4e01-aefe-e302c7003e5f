package com.siteweb.energy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.energy.dto.EnergyRatingConfigExDTO;
import com.siteweb.energy.dto.EnergyRatingDataJobParams;
import com.siteweb.energy.dto.EnergyRatingDataParams;
import com.siteweb.energy.entity.EnergyRatingConfig;
import com.siteweb.energy.entity.EnergyRatingData;
import com.siteweb.energy.mapper.EnergyRatingConfigMapper;
import com.siteweb.energy.mapper.EnergyRatingDataHistoryMapper;
import com.siteweb.energy.mapper.EnergyRatingDataMapper;
import com.siteweb.energy.scheduler.EnergyRatingJob;
import com.siteweb.energy.scheduler.EnergyRatingSchedule;
import com.siteweb.energy.service.EnergyRatingConfigService;
import com.siteweb.energy.service.EnergyRatingDataService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class EnergyRatingDataServiceImpl implements EnergyRatingDataService {

    @Autowired
    private EnergyRatingConfigService energyRatingConfigService;

    @Autowired
    private EnergyRatingDataMapper energyRatingDataMapper;


    @Autowired
    private EnergyRatingDataHistoryMapper energyRatingDataHistoryMapper;


    @Autowired
    private LiveComplexIndexManager liveComplexIndexManager;

    @Autowired
    private EnergyRatingSchedule energyRatingSchedule;

    @Autowired
    private EnergyRatingConfigMapper energyRatingConfigMapper;


    @PostConstruct
    public void init() {
        checkRatingConfigIsRun();
    }


    /*每次加载运行获取数据库和内存的任务同步*/
    @Override
    public void checkRatingConfigIsRun() {
        /*判断如果有过期的任务就关闭*/
        List<EnergyRatingConfig> endList = energyRatingConfigMapper.selectList(new QueryWrapper<EnergyRatingConfig>().lt("endTime", new Date()).eq("status", 1));
        if (!endList.isEmpty()) {
            for (EnergyRatingConfig item : endList) {
                energyRatingConfigService.closeJob(item.getRatingConfigId());
            }
        }
        List<EnergyRatingConfig> runList = energyRatingConfigMapper.selectList(new QueryWrapper<EnergyRatingConfig>().gt("endTime", new Date()).eq("status", 1));
        if (!runList.isEmpty()) {
            for (EnergyRatingConfig item : runList) {
                try {
                    energyRatingSchedule.restartJob(item.getRatingConfigId(),item.getWorkingCondition(),item.getEndTime());
                } catch (SchedulerException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    /*获取采集值列表不分页*/
    @Override
    public List<EnergyRatingData> getEnergyRatingDataList(EnergyRatingDataParams params) {
        QueryWrapper<EnergyRatingData> eq = new QueryWrapper<EnergyRatingData>().eq("ratingConfigId", params.getRatingConfigId()).orderByDesc("sampleTime");
        if (!params.getWorkingCondition().equals("all")) {
            eq.eq("workingCondition", params.getWorkingCondition());
        }
        eq.isNotNull("workingCondition");
        return energyRatingDataMapper.selectList(eq);
    }


    /*清空所有采集值，迁移到历史表*/
    @Override
    public Boolean clearAllEnergyRatingData(Integer ratingConfigId) {
        Integer times = energyRatingDataHistoryMapper.getLatestPieceOfTimes();
        if (ObjectUtil.isNotNull(times)) {
            times = times + 1;
        }else{
            times = 1;
        }
        Date date = new Date();
        energyRatingDataHistoryMapper.insertToHistory(times, date,ratingConfigId);
        energyRatingDataHistoryMapper.clearRatingData(times, date,ratingConfigId);
        return true;
    }


    /* 获取采集值列表 分页*/
    @Override
    public Page getEnergyRatingDataPage(EnergyRatingDataParams params) {
        QueryWrapper<EnergyRatingData> eq = new QueryWrapper<EnergyRatingData>().eq("ratingConfigId", params.getRatingConfigId());
        if (!params.getWorkingCondition().equals("all")) {
            eq.eq("workingCondition", params.getWorkingCondition());
        }
        Page page = Page.of(params.getPageNumber(), params.getPageSize());
        return energyRatingDataMapper.selectPage(page, eq);
    }


    /*创建采集任务*/
    @Override
    public Integer scheduleGenerateRatingData(EnergyRatingDataJobParams params) {
        try {
        return energyRatingSchedule.createJob(params);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }


    /*保存采集值*/
    @Override
    public void saveEnergyRatingData(EnergyRatingData energyRatingData) {
        energyRatingDataMapper.insert(energyRatingData);
    }

    /*批量删除采集值*/
    @Override
    public Boolean deleteEnergyRatingData(List<Integer> ratingDataIds) {
        for (Integer ratingDataId : ratingDataIds) {
            EnergyRatingData energyRatingData = new EnergyRatingData();
            energyRatingData.setRatingDataId(ratingDataId);
            energyRatingData.setWorkingCondition(null);
            energyRatingDataMapper.updateById(energyRatingData);
        }
        return true;
    }

    /*获取对应配置的实时值*/
    @Override
    public EnergyRatingData getRatingDataRealTime(Integer configId) {
        EnergyRatingConfigExDTO configDTO = energyRatingConfigService.getEnergyRatingExByConfigId(configId);
        EnergyRatingJob energyRatingJob = new EnergyRatingJob();
        EnergyRatingData res = energyRatingJob.getRatingDataRealTimeByConfig(configDTO);
        return res;
    }

    /*结束一个采集任务*/
    @Override
    public void unScheduleGenerateRatingData(Integer id) {
        try {
            energyRatingSchedule.deleteJob(id);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

}
