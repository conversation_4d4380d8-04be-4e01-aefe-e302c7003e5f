package com.siteweb.energy.service;


import com.siteweb.energy.dto.EnergyManagementReportPar;
import com.siteweb.energy.dto.EnergyManagementReportResult;
import com.siteweb.energy.entity.EnergyManagement;

import java.util.List;

public interface EnergyManagementService {
    List<EnergyManagement> findAll();
    EnergyManagement findById(Integer energyId);
    void deleteById(Integer energyId);
    EnergyManagement saveInfo(EnergyManagement energyInfo);
    EnergyManagement updateInfo(EnergyManagement energyInfo);
    List<EnergyManagementReportResult> getEnergyReportBeforeAfter(EnergyManagementReportPar reportPar);
    EnergyManagementReportResult getEnergyReportOnOrNot(EnergyManagementReportPar reportPar);
    List<EnergyManagementReportResult> getEnergyReportOn(EnergyManagementReportPar reportPar);
    List<EnergyManagementReportResult> getEnergyReportNot(EnergyManagementReportPar reportPar);
    List<EnergyManagementReportResult> getEnergyReportEfficiency(EnergyManagementReportPar reportPar);
}
