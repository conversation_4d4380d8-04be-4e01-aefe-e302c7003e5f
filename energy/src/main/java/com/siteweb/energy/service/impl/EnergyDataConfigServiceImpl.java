package com.siteweb.energy.service.impl;


import com.siteweb.energy.entity.EnergyDataEntry;
import com.siteweb.energy.mapper.EnergyDataConfigMapper;
import com.siteweb.energy.service.EnergyDataConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class EnergyDataConfigServiceImpl implements EnergyDataConfigService {
    @Autowired
    private EnergyDataConfigMapper energyDataConfigMapper;

    public List<EnergyDataEntry> getAllEnergyDataEntrys(){
        return energyDataConfigMapper.selectList(null);
    }

    public EnergyDataEntry createEnergyDataEntry(EnergyDataEntry energyDataEntry){
        EnergyDataEntry energyDataEntryData= energyDataConfigMapper.selectById(energyDataEntry.getEntryId());
        if(energyDataEntryData!=null){
            return null;
        }
        energyDataConfigMapper.insert(energyDataEntry);
        return energyDataEntry;
    }

    public EnergyDataEntry updateEnergyDataEntry(EnergyDataEntry energyDataEntry){
        energyDataConfigMapper.updateById(energyDataEntry);
        return energyDataEntry;
    }

    @Transactional
    public void deleteEnergyDataEntry(Integer id){
        energyDataConfigMapper.deleteById(id);
    }
}

