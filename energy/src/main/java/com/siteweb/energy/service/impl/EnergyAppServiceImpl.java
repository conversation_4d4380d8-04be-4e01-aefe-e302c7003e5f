package com.siteweb.energy.service.impl;

import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.energy.dto.EnergyAppParameterDTO;
import com.siteweb.energy.dto.EnergyAppResultDTO;
import com.siteweb.energy.dto.EnergyHisDataResult;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyAppService;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
@Slf4j
public class EnergyAppServiceImpl implements EnergyAppService {


    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    ///趋势查询
    @Override
    public List<EnergyAppResultDTO> getEnergyTrend(EnergyAppParameterDTO para){
        List<EnergyAppResultDTO> result = new ArrayList<>();

        //拆选每个指标
        for(String complexIndexId:para.getComplexIndexIds().split(",")){
            ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->complexIndexId.equals(i.getComplexIndexId().toString())
            ).findFirst().orElse(null);
            if(thisComplexIndex == null) continue;

            EnergyAppResultDTO dto = new EnergyAppResultDTO();
            dto.setComplexIndexId(thisComplexIndex.getComplexIndexId());
            dto.setComplexIndexName(thisComplexIndex.getComplexIndexName());
            dto.setUnit(thisComplexIndex.getUnit());
            dto.setLstHisData(getHisDataResult(
                    thisComplexIndex.getComplexIndexId(), para.getStartTime(),para.getEndTime(),para.getTimeType(),para.getItemId()));

            result.add(dto);
        }
        return result;
    }


    List<EnergyHisDataResult> getHisDataResult(Integer complexIndexId, Date startTime, Date endTime,String timeType,Integer itemId){

        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        List<EnergyHisDataResult> lstResultComplexIndexQuery = new ArrayList<>();

        String tableName = "EnergyHisDayData";
        //默认查询天表
        String selectDuration = "select IndexValue as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ComplexIndexId='"+complexIndexId+"' group by ComplexIndexId";
        if (timeType.equals("y")) {  //月表
            if(itemId == 2 || itemId == 100 ) {    //效率和其他是取平均值
                selectDuration = "select mean(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ComplexIndexId='" + complexIndexId + "' group by ComplexIndexId";
            } else{
                selectDuration = "select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ComplexIndexId='" + complexIndexId + "' group by ComplexIndexId";
            }
            tableName = "EnergyHisMonthData";
        } else if (timeType.equals("m")) {  // 月表
            selectDuration = "select IndexValue as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ComplexIndexId='"+complexIndexId+"' group by ComplexIndexId";
            tableName = "EnergyHisMonthData";
        }if (timeType.equals("h")) {  // 小时表
            selectDuration = "select IndexValue as result, Unit as Unit from EnergyHisHourData where time >=$startTime and time <=$endTime and ComplexIndexId='"+complexIndexId+"' group by ComplexIndexId";
            tableName = "EnergyHisHourData";
        }
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .create();
            QueryResult query  =  influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisDataResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataResult.class,tableName);
                if (!resultComplexIndexQuery.isEmpty()) {
                    //resultComplexIndexQuery.removeIf(item -> item.getResult() == null || item.getResult().equals("") || item.getResult().equals("0"));
                    for(EnergyHisDataResult oneResult : resultComplexIndexQuery){
                        oneResult.setResult(NumberUtil.doubleAccuracy(Double.parseDouble(oneResult.getResult()),2).toString());
                        oneResult.setTime(oneResult.getTime().replace("T"," ").replace("Z",""));
                        oneResult.setUnit(oneResult.getUnit());
                    }
                    lstResultComplexIndexQuery.addAll(resultComplexIndexQuery);
                }
            }
        } catch (Exception e) {
            log.error("EnergyAppServiceImpl-getEnergyTrend error {}", e.getMessage());
            return lstResultComplexIndexQuery;
        }

        return lstResultComplexIndexQuery;
    }

}
