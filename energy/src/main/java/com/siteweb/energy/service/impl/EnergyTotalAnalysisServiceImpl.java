package com.siteweb.energy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.*;
import com.siteweb.complexindex.dto.EnergyComplexIndexQueryResult;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyConsumeConst;
import com.siteweb.energy.entity.EnergyConsumeData;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyConsumeConstMapper;
import com.siteweb.energy.mapper.EnergyConsumeDataMapper;
import com.siteweb.energy.mapper.EnergyDataConfigItemMapper;
import com.siteweb.energy.service.EnergyDataConfigItemService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.energy.service.EnergyTotalAnalysisService;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.prealarm.mapper.PreAlarmHistoryMapper;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.influxdb.querybuilder.WhereNested;
import org.influxdb.querybuilder.WhereQueryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.InfluxDB;

import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static org.influxdb.querybuilder.BuiltQuery.QueryBuilder.*;
import static org.influxdb.querybuilder.time.DurationLiteral.*;

@Service
public class EnergyTotalAnalysisServiceImpl implements EnergyTotalAnalysisService {

    @Autowired
    private InfluxDB influxDB;


    private String database = "sitewebenergy_v2";

    @Autowired
    ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    EnergyComplexIndexManager energyComplexIndexManager;

    @Autowired
    ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;

    @Autowired
    EnergyDataConfigItemMapper energyDataConfigItemMapper;


    @Autowired
    EnergyDataConfigItemService energyDataConfigItemService;

    @Autowired
    EnergyConsumeDataMapper energyConsumeDataMapper;

    @Autowired
    EnergyConsumeConstMapper energyConsumeConstMapper;

    @Autowired
    private ResourceObjectManager resourceObjectManager;

    @Autowired
    PreAlarmMapper preAlarmMapper;

    @Autowired
    PreAlarmHistoryMapper preAlarmHistoryMapper;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    EnergyOverViewService energyOverViewService;

    @Autowired
    private ResourceStructureService resourceStructureService;


    /**
     * @param startTime: 开始时间
     * @param endTime:   结束时间
     * @param objectId:  objectId
     * @Description: 获取综合累计指标标煤 co2 人均能耗 平均面积能耗
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:31
     * @return: com.siteweb.energy.dto.TotalComplexIndexDTO
     **/
    @Override
    public TotalComplexIndexDTO findTotalComplexIndex(Date startTime, Date endTime, Integer objectId) {
        EnergyConsumeConst energyConsumeConst = energyConsumeConstMapper.selectOne(new QueryWrapper<EnergyConsumeConst>().eq("objectId", objectId));
        TotalComplexIndexDTO res = new TotalComplexIndexDTO();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, true);
        Double coal = 0.0;
        Double co2 = 0.0;
        Double planCoal = 0d;
        Double planCo2 = 0d;
        for (EnergyTypeResourceComplexIndexMapDTO item : complexIndexMaps) {
            Double value = findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, item.getComplexIndex(), null);
            coal += value * item.getCoalCoefficient();
            co2 += value * item.getCo2Coefficient();
        }
        res.setStandardCoal(Double.valueOf(String.format("%.2f", coal)));
        res.setCo2(Double.valueOf(String.format("%.2f", co2)));
        if (ObjectUtil.isNull(energyConsumeConst) || energyConsumeConst.getPeoples() == 0 || ObjectUtil.isNull(energyConsumeConst.getPeoples())) {
            res.setManAvgEnergy(0d);
        } else {
            res.setManAvgEnergy(Double.valueOf(String.format("%.2f", coal / energyConsumeConst.getPeoples())));
        }
        if (ObjectUtil.isNull(energyConsumeConst) || energyConsumeConst.getPeoples() == 0 || ObjectUtil.isNull(energyConsumeConst.getPeoples())) {
            res.setAreaAvgEnergy(0d);
        } else {
            res.setAreaAvgEnergy(Double.valueOf(String.format("%.2f", coal / energyConsumeConst.getArea())));
        }
        List<EnergyComparativeAnalysisDTO> findComparativeAnalysis = findComparativeAnalysis(startTime, endTime, objectId, null, null);
        for (EnergyComparativeAnalysisDTO item : findComparativeAnalysis) {
            planCoal += item.getPlanValueCoal();
            planCo2 += item.getPlanValueCo2();
        }
        res.setPlanCoal(planCoal);
        res.setPlanCo2(planCo2);
        return res;
    }

    /**
     * @param startTime: 开始时间
     * @param endTime:   结束时间
     * @param objectId:  objectId
     * @Description: 某个时间段某个层级下不同能耗类型的同比和环比 对比分析
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:31
     * @return: com.siteweb.energy.dto.TotalComplexIndexDTO
     **/
    @Override
    public List<EnergyComparativeAnalysisDTO> findComparativeAnalysis(Date startTime, Date endTime, Integer objectId, Integer userId, String timeType) {
        List<EnergyComparativeAnalysisDTO> res = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps;
        if (userId != null) {
            complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTOByUserId(objectId, true, userId);
        } else {
            complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, true);
        }
        for (EnergyTypeResourceComplexIndexMapDTO item : complexIndexMaps) {
            Double value = 0.0;
            Double valueCoC = 0.0;
            Double valueYoY = 0.0;
            String unit = "";
            value += findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, item.getComplexIndex(), timeType);
            valueCoC += getValueYoYOrValueCoC(startTime, endTime, item.getComplexIndex(), "CoC", timeType);
            valueYoY += getValueYoYOrValueCoC(startTime, endTime, item.getComplexIndex(), "YoY", timeType);
            if (ObjectUtil.isNotNull(item.getComplexIndex())) {
                unit = item.getComplexIndex().getUnit();
            } else {
                unit = "";
            }
            Double planValue = findPlanValueByEnergyTypeIdAndTime(startTime, endTime, item.getBusinessType().getBusinessTypeId(), objectId);
            EnergyComparativeAnalysisDTO temp = new EnergyComparativeAnalysisDTO();
            temp.setBusinessTypeId(item.getBusinessType().getBusinessTypeId());
            temp.setBusinessTypeName(item.getBusinessType().getBusinessTypeName());
            temp.setValue(Double.valueOf(String.format("%.2f", value)));
            temp.setValueYoY(Double.valueOf(String.format("%.2f", valueYoY)));
            temp.setValueCoC(Double.valueOf(String.format("%.2f", valueCoC)));
            temp.setUnit(unit);
            temp.setPlanValue(planValue);
            temp.setPlanValueCoal(Double.valueOf(String.format("%.2f", planValue * item.getCoalCoefficient())));
            temp.setPlanValueCo2(Double.valueOf(String.format("%.2f", planValue * item.getCo2Coefficient())));
            temp.setValueCo2(Double.valueOf(String.format("%.2f", value * item.getCo2Coefficient())));
            temp.setValueCoal(Double.valueOf(String.format("%.2f", value * item.getCoalCoefficient())));
            if (valueYoY == 0.0) {

                if (value == 0.0) {
                    temp.setYoY(0d);
                } else {
                    temp.setYoY(1d * 100);
                }
            } else {
                temp.setYoY(Double.valueOf(String.format("%.2f", ((value - valueYoY) / valueYoY) * 100)));
            }
            if (valueCoC == 0.0) {
                if (value == 0.0) {
                    temp.setCoC(0d);
                } else {
                    temp.setCoC(1d * 100);
                }
            } else {
                temp.setCoC(Double.valueOf(String.format("%.2f", ((value - valueCoC) / valueCoC) * 100)));
            }
            res.add(temp);
        }

        return res;
    }


    /**
     * @param startTime:
     * @param endTime:
     * @param objectId:
     * @Description: 某个时间段某个层级下不同能耗类型用能值和占比
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:33
     * @return: java.util.List<com.siteweb.energy.dto.EnergyPercentageAnalysisDTO>
     **/
    @Override
    public List<EnergyPercentageAnalysisDTO> findPercentageAnalysis(Date startTime, Date endTime, Integer objectId) {
        List<EnergyPercentageAnalysisDTO> res = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, true);
        for (EnergyTypeResourceComplexIndexMapDTO item : complexIndexMaps) {
            Double value = findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, item.getComplexIndex(), null) * item.getCoalCoefficient();
            EnergyPercentageAnalysisDTO temp = new EnergyPercentageAnalysisDTO();
            temp.setName(item.getBusinessType().getBusinessTypeName());
            temp.setValue(NumberUtil.doubleAccuracy(value, 2));
            temp.setUnit(ObjectUtil.isNotNull(item.getComplexIndex())?item.getComplexIndex().getUnit():"");
            res.add(temp);
        }
        return res;
    }

    @Override
    /**
     * @Description: 某个时间段某个层级下不同能耗类型的用能趋势
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:34
     * @param startTime:
     * @param endTime:
     * @param objectId:
     * @param objectTypeId:
     * @return: com.siteweb.energy.dto.EnergyTrendAnalysisDTO
     **/
    public synchronized EnergyTrendAnalysisDTO findTrendAnalysis(Date startTime, Date endTime, Integer objectId, Boolean isStandardCoal) {
        EnergyTrendAnalysisDTO res = new EnergyTrendAnalysisDTO();
        long nd = 1000 * 24 * 60 * 60;
        long diff = startTime.getTime() - endTime.getTime();
        long day = Math.abs(diff / nd);
        if (day > 31) {
            /*展示月*/
            res = getEnergyTrendSeriesMonth(startTime, endTime, objectId, isStandardCoal, null);
        }
        if (day <= 31 && day > 0) {
            /*展示天*/
            res = getEnergyTrendSeriesDay(startTime, endTime, objectId, isStandardCoal, "d", null);
        }
        if (day == 0) {
            /*展示时*/
            res = getEnergyTrendSeriesDay(startTime, endTime, objectId, isStandardCoal, "h", null);
        }
        /*删除总标煤*/
//        EnergyTrendSeries temp = new EnergyTrendSeries();
//        Double[] value = new Double[res.getXAxisData().size()];
//        for (int i = 0; i < res.getXAxisData().size(); i++) {
//            value[i] = 0d;
//        }
//        List<EnergyTrendSeries> series = res.getSeries();
//        for (EnergyTrendSeries item : series) {
//            for (int i = 0; i < item.getData().size(); i++) {
//                value[i] = Double.valueOf(String.format("%.2f", value[i] + item.getData().get(i)));
//            }
//        }
//        temp.setData(Arrays.stream(value).toList());
//        temp.setName(messageSourceUtil.getMessage("energy.totalanalysis.TotalCoal"));
//        res.getSeries().add(0, temp);
        return res;
    }


    /**
     * @param startTime:
     * @param endTime:
     * @param objectId:
     * @Description: 下级用能占比
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/19 10:47
     * @return: java.util.List<com.siteweb.energy.dto.EnergyPercentageAnalysisDTO>
     **/
    @Override
    public List<EnergyPercentageAnalysisDTO> findChildNodePercentage(Date startTime, Date endTime, Integer objectId) {
        List<EnergyPercentageAnalysisDTO> res = new ArrayList<>();
        Integer userId = TokenUserUtil.getLoginUserId();
        List<ResourceObjectEntity> resourceObjectEntityList = resourceObjectManager.findAllResourceByUserId(userId);
        List<ResourceObjectEntity> findChildNode = resourceObjectEntityList.stream().filter(item -> item.getParentResourceStructureId().equals(objectId)).toList();
        for (ResourceObjectEntity resourceObjectEntity : findChildNode) {
            EnergyPercentageAnalysisDTO temp = new EnergyPercentageAnalysisDTO();
            TotalComplexIndexDTO totalComplexIndexDTO = this.findTotalComplexIndex(startTime, endTime, resourceObjectEntity.getObjectId());
            temp.setValue(totalComplexIndexDTO.getStandardCoal());
            temp.setName(resourceObjectEntity.getResourceName());
            res.add(temp);
        }
        return res;
    }

    @Override
    /**
     * @Description:预警排名
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/19 14:43
     * @param startTime: 开始时间
     * @param endTime: 结束时间
     * @return: com.siteweb.energy.dto.EnergyPreAlarmRankDTO
     **/
    public EnergyPreAlarmRankDTO findPrealarmRank(Date startTime, Date endTime) {
        EnergyPreAlarmRankDTO res = new EnergyPreAlarmRankDTO();
        List<EnergyTrendSeries> resSeries = new ArrayList<>();
        List<String> xAxisData = new ArrayList<>();
//        获取预警和历史预警表中在这个时间范围的预警
        List<PreAlarm> preAlarmList = preAlarmMapper.selectList(new QueryWrapper<PreAlarm>().ge("startTime", startTime).le("startTime", endTime).eq("preAlarmCategory", 2));
        List<PreAlarmHistory> preAlarmHistoryList = preAlarmHistoryMapper.selectList(new QueryWrapper<PreAlarmHistory>().ge("startTime", startTime).le("startTime", endTime).eq("preAlarmCategory", 2));
//        获取所有能耗涉及到的预警等级
        HashSet<String> tempSeverityList = new HashSet<>();
        tempSeverityList.add("0-" + messageSourceUtil.getMessage("energy.totalanalysis.total") + "-#007fff");
//        获取所有能耗预警所在的层级设为Y轴
        HashSet<String> objectList = new HashSet<>();
        for (PreAlarm item : preAlarmList) {
            tempSeverityList.add(item.getPreAlarmSeverity() + "-" + item.getPreAlarmSeverityName() + "-" + item.getColor());
            objectList.add(item.getObjectId() + "-" + item.getObjectTypeId() + "-" + item.getObjectName());
        }
        for (PreAlarmHistory item : preAlarmHistoryList) {
            tempSeverityList.add(item.getPreAlarmSeverity() + "-" + item.getPreAlarmSeverityName() + "-" + item.getColor());
            objectList.add(item.getObjectId() + "-" + item.getObjectTypeId() + "-" + item.getObjectName());
        }
        List<String> severityList = new ArrayList<>(tempSeverityList);
        severityList.sort(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return Integer.valueOf(o1.split("-")[0]) - Integer.valueOf(o2.split("-")[0]);
            }
        });


        for (String s : objectList) {
            xAxisData.add(s.split("-")[2]);
        }
        for (String item : severityList) {
            EnergyTrendSeries temp = new EnergyTrendSeries();
            List<Double> data = new ArrayList<>();
            String[] severity = item.split("-");
            HashMap<String, Integer> hashMap = new HashMap<>();
//            初始化map
            for (String s : objectList) {
                hashMap.put(s, 0);
            }

            for (String s : objectList) {
                for (PreAlarm preAlarmItem : preAlarmList) {
                    String key = preAlarmItem.getObjectId() + "-" + preAlarmItem.getObjectTypeId() + "-" + preAlarmItem.getObjectName();
                    if (severity[0].equals("0")) {
                        if (s.equals(key)) {
                            hashMap.put(key, hashMap.get(key) + 1);
                        }
                    } else {
                        if (s.equals(key) && preAlarmItem.getPreAlarmSeverity().equals(Integer.valueOf(severity[0]))) {
                            hashMap.put(key, hashMap.get(key) + 1);
                        }
                    }
                }
                for (PreAlarmHistory preAlarmItem : preAlarmHistoryList) {
                    String key = preAlarmItem.getObjectId() + "-" + preAlarmItem.getObjectTypeId() + "-" + preAlarmItem.getObjectName();
                    if (severity[0].equals("0")) {
                        if (s.equals(key)) {
                            hashMap.put(key, hashMap.get(key) + 1);
                        }
                    } else {
                        if (s.equals(key) && preAlarmItem.getPreAlarmSeverity().equals(Integer.valueOf(severity[0]))) {
                            hashMap.put(key, hashMap.get(key) + 1);
                        }
                    }

                }
            }
            for (String s : objectList) {
                data.add(Double.valueOf(hashMap.get(s)));
            }
            temp.setName(severity[1]);
            temp.setColor(severity[2]);
            temp.setData(data);
            resSeries.add(temp);
        }
        res.setSeries(resSeries);
        res.setXAxisData(xAxisData);
        return res;
    }

    @Override
    /**
     * @Description:能耗排名中的综合对比分析
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/8 10:20
     * @param startTime: 开始时间
     * @param endTime: 结束时间
     * @param objectId: objectId
     * @param objectTypeId: objectTypeId
     * @param businessTypeId:能耗类型id
     * @return: com.siteweb.energy.dto.EnergyRankComplexDTO
     **/
    public EnergyRankComplexDTO findEnergyRankComplex(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        Double findPlaneValue = findPlanValueByEnergyTypeIdAndTime(startTime, endTime, businessTypeId, objectId);
        EnergyConsumeConst energyConsumeConst = energyConsumeConstMapper.selectOne(new QueryWrapper<EnergyConsumeConst>().eq("objectId", objectId));
        EnergyRankComplexDTO res = new EnergyRankComplexDTO(0d, 0d, 0d, 0d, 0d, 0d, 0d, 0d, "");
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, false);
        EnergyTypeResourceComplexIndexMapDTO findItem = new EnergyTypeResourceComplexIndexMapDTO();
        for (EnergyTypeResourceComplexIndexMapDTO complexIndexMap : complexIndexMaps) {
            if (complexIndexMap.getBusinessType().getBusinessTypeId().equals(businessTypeId)) {
                findItem = complexIndexMap;
            }
        }
        if (ObjectUtil.isNull(findItem.getComplexIndex())) {
            return res;
        }
        res.setUnit(findItem.getComplexIndex().getUnit());
        Double totalValue = NumberUtil.doubleAccuracy(findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, findItem.getComplexIndex(), null), 2);
        Double valueCoC = getValueYoYOrValueCoC(startTime, endTime, findItem.getComplexIndex(), "CoC", null);
        Double valueYoY = getValueYoYOrValueCoC(startTime, endTime, findItem.getComplexIndex(), "YoY", null);
        Double manAvgEnergy = 0d;
        Double areaAvgEnergy = 0d;
        if (ObjectUtil.isNotNull(energyConsumeConst) && ObjectUtil.isNotNull(energyConsumeConst.getPeoples()) && energyConsumeConst.getPeoples() != 0) {
            manAvgEnergy = totalValue / energyConsumeConst.getPeoples();
        }
        if (ObjectUtil.isNotNull(energyConsumeConst) && ObjectUtil.isNotNull(energyConsumeConst.getArea()) && energyConsumeConst.getArea() != 0) {
            areaAvgEnergy = totalValue / energyConsumeConst.getArea();
        }
        res.setTotalValue(totalValue);
        if (valueCoC == 0) {
            if (totalValue != 0) {
                res.setTotalValueCoC(100d);
            }
        } else {
            res.setTotalValueCoC(NumberUtil.doubleAccuracy((totalValue - valueCoC) / valueCoC * 100, 2));
        }
        if (valueYoY == 0) {
            if (totalValue != 0) {
                res.setTotalValueYoY(100d);
            }
        } else {
            res.setTotalValueYoY(NumberUtil.doubleAccuracy((totalValue - valueYoY) / valueYoY * 100, 2));
        }
        res.setManAvgEnergy(NumberUtil.doubleAccuracy(manAvgEnergy, 2));
        res.setAreaAvgEnergy(NumberUtil.doubleAccuracy(areaAvgEnergy, 2));
        Double energySavingRate = 0d;
        if (findPlaneValue != 0) {
            energySavingRate = NumberUtil.doubleAccuracy((findPlaneValue - totalValue) / findPlaneValue * 100, 2);
        }
        res.setEnergySavingRate(energySavingRate);
        Double planeValueCoC = getPlaneValueYoYOrValueCoC(startTime, endTime, businessTypeId, objectId, "CoC");
        Double planeValueYoY = getPlaneValueYoYOrValueCoC(startTime, endTime, businessTypeId, objectId, "YoY");
        Double energySavingRateCoCValue = 0d;
        Double energySavingRateYoYValue = 0d;
        if (planeValueCoC != 0) {
            energySavingRateCoCValue = (planeValueCoC - valueCoC) / planeValueCoC;
        }
        if (planeValueYoY != 0) {
            energySavingRateYoYValue = (planeValueYoY - valueYoY) / planeValueYoY;
        }
        if (energySavingRateCoCValue == 0) {
            if (energySavingRate != 0) {
                res.setEnergySavingRateCoC(100d);
            }
        } else {
            res.setEnergySavingRateCoC(NumberUtil.doubleAccuracy((energySavingRate - energySavingRateCoCValue) / energySavingRateCoCValue * 100, 2));
        }
        if (energySavingRateYoYValue == 0) {
            if (energySavingRate != 0) {
                res.setEnergySavingRateYoY(100d);
            }
        } else {
            res.setEnergySavingRateYoY(NumberUtil.doubleAccuracy((energySavingRate - energySavingRateYoYValue) / energySavingRateYoYValue * 100, 2));
        }
        return res;
    }

    @Override
    /**
     * @Description: 能耗排名四个柱状图
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/8 15:10
     * @param startTime: 开始时间
     * @param endTime: 结束时间
     * @param objectId: objectId
     * @param objectTypeId: objectTypeId
     * @param businessTypeId:能耗类型id
     * @return: java.util.List<com.siteweb.energy.dto.EnergyTrendAnalysisDTO>
     **/
    public List<EnergyRankListDTO> findEnergyRankList(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        List<EnergyRankListDTO> res = new ArrayList<>();
        Integer userId = TokenUserUtil.getLoginUserId();
        List<ResourceObjectEntity> resourceObjectEntityList = resourceObjectManager.findAllResourceByUserId(userId);
        List<ResourceObjectEntity> findChildNode = resourceObjectEntityList.stream().filter(item -> item.getParentResourceStructureId().equals(objectId)).toList();
        List<String> xAxisData = new ArrayList<>();
        List<Double> dataTotal = new ArrayList<>();
        List<Double> dataMan = new ArrayList<>();
        List<Double> dataArea = new ArrayList<>();
        List<Double> dataSaving = new ArrayList<>();
        String unit = "";
        for (ResourceObjectEntity resourceObjectEntity : findChildNode) {
            xAxisData.add(resourceObjectEntity.getResourceName());
            TotalManAreaSaving temp = getTotalManAreaSave(startTime, endTime, resourceObjectEntity.getObjectId(), resourceObjectEntity.getObjectTypeId(), businessTypeId);
            dataTotal.add(temp.getTotalValue());
            dataMan.add(temp.getManAvgEnergy());
            dataArea.add(temp.getAreaAvgEnergy());
            dataSaving.add(temp.getEnergySavingRate());
            unit = temp.getUnit();
        }
        EnergyRankListDTO resTotal = new EnergyRankListDTO();
        resTotal.setXAxisData(xAxisData);
        resTotal.setData(dataTotal);
        resTotal.setUnit(unit);
        res.add(resTotal);
        EnergyRankListDTO resMan = new EnergyRankListDTO();
        resMan.setXAxisData(xAxisData);
        resMan.setData(dataMan);
        resMan.setUnit(unit);
        res.add(resMan);
        EnergyRankListDTO resArea = new EnergyRankListDTO();
        resArea.setXAxisData(xAxisData);
        resArea.setData(dataArea);
        resArea.setUnit(unit);
        res.add(resArea);
        EnergyRankListDTO resSaving = new EnergyRankListDTO();
        resSaving.setXAxisData(xAxisData);
        resSaving.setData(dataSaving);
        resSaving.setUnit("%");
        res.add(resSaving);
        return res;
    }

    private TotalManAreaSaving getTotalManAreaSave(Date startTime, Date endTime, Integer objectId, Integer objectTypeId, Integer businessTypeId) {
        TotalManAreaSaving res = new TotalManAreaSaving(0d, 0d, 0d, 0d, "");
        Double findPlaneValue = findPlanValueByEnergyTypeIdAndTime(startTime, endTime, businessTypeId, objectId);
        EnergyConsumeConst energyConsumeConst = energyConsumeConstMapper.selectOne(new QueryWrapper<EnergyConsumeConst>().eq("objectId", objectId).eq("objectTypeId", objectTypeId));
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, false);
        EnergyTypeResourceComplexIndexMapDTO findItem = new EnergyTypeResourceComplexIndexMapDTO();
        for (EnergyTypeResourceComplexIndexMapDTO complexIndexMap : complexIndexMaps) {
            if (complexIndexMap.getBusinessType().getBusinessTypeId().equals(businessTypeId)) {
                findItem = complexIndexMap;
            }
        }
        if (ObjectUtil.isNull(findItem.getComplexIndex())) {
            return res;
        }
        Double totalValue = findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, findItem.getComplexIndex(), null);
        res.setTotalValue(NumberUtil.doubleAccuracy(totalValue, 2));
        if (ObjectUtil.isNotNull(energyConsumeConst) && energyConsumeConst.getPeoples() != 0) {
            res.setManAvgEnergy(NumberUtil.doubleAccuracy(totalValue / energyConsumeConst.getPeoples(), 2));
        }
        if (ObjectUtil.isNotNull(energyConsumeConst) && energyConsumeConst.getArea() != 0) {
            res.setAreaAvgEnergy(NumberUtil.doubleAccuracy(totalValue / energyConsumeConst.getArea(), 2));
        }
        Double energySavingRate = 0d;
        if (findPlaneValue != 0) {
            energySavingRate = NumberUtil.doubleAccuracy((findPlaneValue - totalValue) / findPlaneValue * 100, 2);
        }
        res.setEnergySavingRate(energySavingRate);
        res.setUnit(findItem.getComplexIndex().getUnit());
        return res;
    }

    /**
     * @param startTime:
     * @param endTime:
     * @param objectId:
     * @Description: 按照月获取能耗趋势
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:45
     * @return: com.siteweb.energy.dto.EnergyTrendAnalysisDTO
     **/
    private EnergyTrendAnalysisDTO getEnergyTrendSeriesMonth(Date startTime, Date endTime, Integer objectId, Boolean isStandardCoal, Integer userId) {

        EnergyTrendAnalysisDTO res = new EnergyTrendAnalysisDTO();
        List<EnergyTrendSeries> energyTrendSeriesList = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps;
        if (userId != null)
            complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTOByUserId(objectId, isStandardCoal, userId);
        else
            complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, isStandardCoal);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endTime);
        List<String> xAxisData = new ArrayList<>();
        Boolean flag = true;
        for (EnergyTypeResourceComplexIndexMapDTO complexIndexMap : complexIndexMaps) {
            List<Double> data = new ArrayList<>();
            Date nowStartTime = startTime;
            Date nowEndTime = DateUtil.getLastDayOfMonth(startTime);
            while (true) {
                Calendar tempCalendar = Calendar.getInstance();
                tempCalendar.setTime(nowStartTime);
                if (tempCalendar.get(Calendar.MONTH) == endCalendar.get(Calendar.MONTH) && tempCalendar.get(Calendar.YEAR) == endCalendar.get(Calendar.YEAR)) {
                    Double tempValue = Double.valueOf(String.format("%.2f", findSumValueOfHistoryComplexIndexByIdAndDuration(nowStartTime, endTime, complexIndexMap.getComplexIndex(), null) * complexIndexMap.getCoalCoefficient()));
                    data.add(tempValue);
                    if (flag) {
//                        xAxisData.add(DateUtil.dateToString(nowStartTime)+DateUtil.dateToString(endTime));
                        xAxisData.add(DateUtil.dateToString(nowStartTime));
                    }
                    break;
                }
                Double tempValue = Double.valueOf(String.format("%.2f", findSumValueOfHistoryComplexIndexByIdAndDuration(nowStartTime, nowEndTime, complexIndexMap.getComplexIndex(), null) * complexIndexMap.getCoalCoefficient()));
                data.add(tempValue);
                if (flag) {
//                    xAxisData.add(DateUtil.dateToString(nowStartTime)+DateUtil.dateToString(nowEndTime));
                    xAxisData.add(DateUtil.dateToString(nowStartTime));
                }
                tempCalendar.add(Calendar.MONTH, 1);
                nowStartTime = DateUtil.getFirstDayOfMonth(tempCalendar.getTime());
                nowEndTime = DateUtil.getLastDayOfMonth(tempCalendar.getTime());
            }
            flag = false;
            EnergyTrendSeries energyTrendSeries = new EnergyTrendSeries();
            energyTrendSeries.setData(data);
            energyTrendSeries.setName(complexIndexMap.getBusinessType().getBusinessTypeName());
            energyTrendSeries.setBusinessTypeId(complexIndexMap.getBusinessType().getBusinessTypeId());
            if (ObjectUtil.isNotNull(complexIndexMap.getComplexIndex())) {
                energyTrendSeries.setUnit(complexIndexMap.getComplexIndex().getUnit());
            } else {
                energyTrendSeries.setUnit("");
            }
            energyTrendSeriesList.add(energyTrendSeries);
        }
        res.setSeries(energyTrendSeriesList);
        res.setXAxisData(formatTimexAxisData(xAxisData));
        return res;
    }

    private List<String> formatTimexAxisData(List<String> xAxisData) {
        List<String> res = new ArrayList<>();
        if (xAxisData.size() == 0) {
            return res;
        }
        String formatString_1 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_1");
        String formatString_2 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_2");
        String formatString_3 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_3");
        String formatString_4 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_4");

        Date time1 = DateUtil.stringToDate(xAxisData.get(0));
        Date time2 = DateUtil.stringToDate(xAxisData.get(xAxisData.size() - 1));
        long nd = 1000 * 24 * 60 * 60;
        long diff = time1.getTime() - time2.getTime();
        long day = Math.abs(diff / nd);
        Calendar startTime = Calendar.getInstance();
        Calendar endTime = Calendar.getInstance();
        startTime.setTime(time1);
        endTime.setTime(time2);
        if (day > 31) {
            /*展示月*/
            SimpleDateFormat formatter;
            if (startTime.get(Calendar.YEAR) != endTime.get(Calendar.YEAR)) {
                formatter = new SimpleDateFormat(formatString_1);
            } else {
                formatter = new SimpleDateFormat(formatString_2);
            }
            for (String item : xAxisData) {
                Date temp = DateUtil.stringToDate(item);
                res.add(formatter.format(temp));
            }
        } else if (day <= 31 && day > 0) {
            /*展示天*/
            SimpleDateFormat formatter;
            formatter = new SimpleDateFormat(formatString_3);
            for (String item : xAxisData) {
                Date temp = DateUtil.stringToDate(item);
                res.add(formatter.format(temp));
            }
        } else if (day == 0) {
            /*展示时*/
            SimpleDateFormat formatter;
            formatter = new SimpleDateFormat(formatString_4);
            for (String item : xAxisData) {
                Date temp = DateUtil.stringToDate(item);
                res.add(formatter.format(temp));
            }
        }

        return res;
    }


    /**
     * @param startTime:
     * @param endTime:
     * @param objectId:
     * @param timeType:
     * @Description: 获取天Or时的能耗趋势
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:46
     * @return: com.siteweb.energy.dto.EnergyTrendAnalysisDTO
     **/
    private EnergyTrendAnalysisDTO getEnergyTrendSeriesDay(Date startTime, Date endTime, Integer objectId, Boolean isStandardCoal, String timeType, Integer userId) {

        EnergyTrendAnalysisDTO res = new EnergyTrendAnalysisDTO();
        List<EnergyTrendSeries> energyTrendSeriesList = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps;
        if (userId != null)
            complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTOByUserId(objectId, isStandardCoal, userId);
        else
            complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, isStandardCoal);
        List<String> xAxisData = new ArrayList<>();
        Boolean flag = true;
        for (EnergyTypeResourceComplexIndexMapDTO complexIndexMap : complexIndexMaps) {
            List<Double> data = new ArrayList<>();
            List<EnergyComplexIndexQueryResult> complexIndexQueryResults = findSumValueOfHistoryComplexIndexByIdAndDurationGroupBy(startTime, endTime, complexIndexMap.getComplexIndex(), timeType);
            for (EnergyComplexIndexQueryResult complexIndexQueryResult : complexIndexQueryResults) {

                data.add(Double.valueOf(String.format("%.2f", Double.parseDouble(ObjectUtil.isNull(complexIndexQueryResult.getResult()) ? "0.0" : complexIndexQueryResult.getResult()) * complexIndexMap.getCoalCoefficient())));
                if (flag) {
                    String t = complexIndexQueryResult.getTime();
                    for (String s : Arrays.asList("T", "Z")) {
                        t = t.replace(s, " ");
                    }
                    xAxisData.add(t);

                }
            }
            flag = false;
            EnergyTrendSeries energyTrendSeries = new EnergyTrendSeries();
            energyTrendSeries.setData(data);
            energyTrendSeries.setName(complexIndexMap.getBusinessType().getBusinessTypeName());
            energyTrendSeries.setBusinessTypeId(complexIndexMap.getBusinessType().getBusinessTypeId());
            if (ObjectUtil.isNotNull(complexIndexMap.getComplexIndex())) {
                energyTrendSeries.setUnit(complexIndexMap.getComplexIndex().getUnit());
            } else {
                energyTrendSeries.setUnit("");
            }
            energyTrendSeriesList.add(energyTrendSeries);
        }

        res.setSeries(energyTrendSeriesList);
        res.setXAxisData(formatTimexAxisData(xAxisData));
        return res;
    }

    /**
     * @param startTime:
     * @param endTime:
     * @param id:能耗类型
     * @Description:获取某时间某层级节点下某能耗类型能耗计划量
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:47
     * @return: java.lang.Double
     **/
    @Override
    public Double findPlanValueByEnergyTypeIdAndTime(Date startTime, Date endTime, Integer id, Integer object) {
        List<String> yearMonth = getYearMonthByDateRange(startTime, endTime);
        Double res = 0d;
        HashMap<String, Double> redis = new HashMap<String, Double>();
        for (String item : yearMonth) {
            Double avg = redis.get(item);
            if (avg == null) {
                String[] temp = item.split("-");
                Double avgTemp = findPlanValue(Integer.valueOf(temp[0]), Integer.valueOf(temp[1]), id, object);
                redis.put(item, avgTemp);
                avg = avgTemp;
            }
            res += avg;
        }
        return Double.valueOf(String.format("%.2f", res));
    }

    @Override
    /**
     * @Description: 用能对比-水电煤气油使用量计划量和同比环比
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/19 16:12
     * @param params: 
     * @return: java.util.List<com.siteweb.energy.dto.EnergyComparativeAnalysisDTO>
     **/
    public List<EnergyComparativeAnalysisDTO> findUseEnergyComparedOption(UseEnergyComparedParamsDTO params) {
        /*如果是默认层级*/
        if (ObjectUtil.isNull(params.getResourceStructures())) {
            ResourceStructureTreeDTO resourceAll = resourceStructureManager.getTreeDtoRoot();
            List<Integer> resourceList = new ArrayList<>();
            String resourceString;
            if (resourceAll.getChildren().size() != 0) {
                for (ResourceStructureTreeDTO child : resourceAll.getChildren()) {
                    resourceList.add(child.getId());
                }
                resourceString = StringUtils.join(resourceList, ",");
            } else {
                resourceString = String.valueOf(resourceAll.getId());
            }
            params.setResourceStructures(resourceString);
        }
        List<EnergyComparativeAnalysisDTO> res = new ArrayList<>();
        List<String> objects = List.of(params.getResourceStructures().split(","));
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        /*过滤给出层级下的总量指标*/
        allTotalConsumeComplexIndex = allTotalConsumeComplexIndex.stream().filter(item -> objects.contains(String.valueOf(item.getObjectId()))).toList();
        List<StructureOfComplexIndexValue> valueList = new ArrayList<>();
        List<StructureOfComplexIndexValue> valueCoCList = new ArrayList<>();
        List<StructureOfComplexIndexValue> valueYoYList = new ArrayList<>();
        for (ComplexIndex totalConsumeComplexIndex : allTotalConsumeComplexIndex) {
            StructureOfComplexIndexValue temp1 = new StructureOfComplexIndexValue();
            StructureOfComplexIndexValue temp2 = new StructureOfComplexIndexValue();
            StructureOfComplexIndexValue temp3 = new StructureOfComplexIndexValue();
            temp1.setComplexIndexId(totalConsumeComplexIndex.getComplexIndexId());
            temp2.setComplexIndexId(totalConsumeComplexIndex.getComplexIndexId());
            temp3.setComplexIndexId(totalConsumeComplexIndex.getComplexIndexId());
            valueList.add(temp1);
            valueCoCList.add(temp2);
            valueYoYList.add(temp3);
        }
        /*获取环比和同比时间段*/
        List<Date> timeCoC = getCoCDateOrYoYDate(params.getStartTime(), params.getEndTime(), "CoC");
        List<Date> timeYoY = getCoCDateOrYoYDate(params.getStartTime(), params.getEndTime(), "YoY");
        /*分别计算当前时间和同比和环比的值*/
        energyOverViewService.GetConsumeOfStructureList(valueList, 0, params.getStartTime(), params.getEndTime(), "d", false);
        energyOverViewService.GetConsumeOfStructureList(valueCoCList, 0, timeCoC.get(0), timeCoC.get(1), "d", false);
        energyOverViewService.GetConsumeOfStructureList(valueYoYList, 0, timeYoY.get(0), timeYoY.get(1), "d", false);
        List<ComplexIndexBusinessType> businessTypeList = complexIndexBusinessTypeMapper.selectList(new QueryWrapper<ComplexIndexBusinessType>().eq("parentId", 1));
        for (ComplexIndexBusinessType businessTypeItem : businessTypeList) {
            EnergyComparativeAnalysisDTO temp = new EnergyComparativeAnalysisDTO();
            Double value = 0d;
            Double planeValue = 0d;
            Double valueCoC = 0d;
            Double valueYoY = 0d;
            String unit = "";
            List<ComplexIndex> findComplexIndexByBusinessType = allTotalConsumeComplexIndex.stream().filter(item -> item.getBusinessTypeId().equals(businessTypeItem.getBusinessTypeId())).toList();
            for (String object : objects) {
                String[] objectString = object.split("\\.");
                Integer objectId = Integer.valueOf(objectString[0]);
                planeValue += findPlanValueByEnergyTypeIdAndTime(params.getStartTime(), params.getEndTime(), businessTypeItem.getBusinessTypeId(), objectId);
            }
            if (findComplexIndexByBusinessType.size() != 0) {
                unit = findComplexIndexByBusinessType.get(0).getUnit();
            }
            for (ComplexIndex complexIndex : findComplexIndexByBusinessType) {
                for (StructureOfComplexIndexValue tempValue : valueList) {
                    if (tempValue.getComplexIndexId().equals(complexIndex.getComplexIndexId())) {
                        if (ObjectUtil.isNotNull(tempValue.getSumValue())) {
                            value += tempValue.getSumValue();
                        }
                    }
                }
                for (StructureOfComplexIndexValue tempValue : valueCoCList) {
                    if (tempValue.getComplexIndexId().equals(complexIndex.getComplexIndexId())) {
                        if (ObjectUtil.isNotNull(tempValue.getSumValue())) {
                            valueCoC += tempValue.getSumValue();
                        }
                    }
                }
                for (StructureOfComplexIndexValue tempValue : valueYoYList) {
                    if (tempValue.getComplexIndexId().equals(complexIndex.getComplexIndexId())) {
                        if (ObjectUtil.isNotNull(tempValue.getSumValue())) {
                            valueYoY += tempValue.getSumValue();
                        }
                    }
                }
                break;
            }
            temp.setValue(NumberUtil.doubleAccuracy(value, 2));
            temp.setPlanValue(NumberUtil.doubleAccuracy(planeValue, 2));
            temp.setUnit(unit);
            temp.setBusinessTypeId(businessTypeItem.getBusinessTypeId());
            temp.setBusinessTypeName(businessTypeItem.getBusinessTypeName());
            temp.setValueCoC(NumberUtil.doubleAccuracy(valueCoC, 2));
            temp.setValueYoY(NumberUtil.doubleAccuracy(valueYoY, 2));
            temp.setCoC(getCoCOrYoYByValue(value, valueCoC));
            temp.setYoY(getCoCOrYoYByValue(value, valueYoY));
            res.add(temp);
        }
        return res;
    }

    @Override
    /**
     * @Description: 用能对比-分类能耗占比分析
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/19 16:12
     * @param params:
     * @return: java.util.List<com.siteweb.energy.dto.EnergyPercentageAnalysisDTO>
     **/
    public List<EnergyPercentageAnalysisDTO> findUseEnergyPercentage(UseEnergyComparedParamsDTO params) {
        List<EnergyPercentageAnalysisDTO> res = new ArrayList<>();
        List<EnergyComparativeAnalysisDTO> energyComparativeAnalysis = findUseEnergyComparedOption(params);
        Map<Integer, ComplexIndexBusinessTypeCoefficient> energyCoalMap = getBusinessTypeCoalMap();
        energyCoalMap.forEach((key, value) -> {
            for (EnergyComparativeAnalysisDTO comparativeAnalysis : energyComparativeAnalysis) {

                if (comparativeAnalysis.getBusinessTypeId() == key) {
                    EnergyPercentageAnalysisDTO temp = new EnergyPercentageAnalysisDTO();
                    temp.setName(comparativeAnalysis.getBusinessTypeName());
                    temp.setValue(NumberUtil.doubleAccuracy(comparativeAnalysis.getValue() * value.getCoal(), 2));
                    res.add(temp);
                }
            }
        });
        return res;
    }

    @Override
    /**
     * @Description: 用能对比-占比分析模板对象
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/19 16:13
     * @param useEnergyComparedParamsDTO:
     * @return: java.util.List<com.siteweb.energy.dto.EnergyPercentageAnalysisDTO>
     **/
    public List<EnergyPercentageAnalysisDTO> findUseEnergyPercentageTemp(UseEnergyComparedParamsDTO params) {
        /*如果是默认层级*/
        if (ObjectUtil.isNull(params.getResourceStructures())) {
            ResourceStructureTreeDTO resourceAll = resourceStructureManager.getTreeDtoRoot();
            List<Integer> resourceList = new ArrayList<>();
            String resourceString;
            if (resourceAll.getChildren().size() != 0) {
                for (ResourceStructureTreeDTO child : resourceAll.getChildren()) {
                    resourceList.add(child.getId());
                }
                resourceString = StringUtils.join(resourceList, ",");
            } else {
                resourceString = String.valueOf(resourceAll.getId());
            }
            params.setResourceStructures(resourceString);
        }
        List<EnergyPercentageAnalysisDTO> res = new ArrayList<>();
        Map<Integer, ComplexIndexBusinessTypeCoefficient> energyCoalMap = getBusinessTypeCoalMap();
        List<String> objects = List.of(params.getResourceStructures().split(","));
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        /*过滤给出层级下的总量指标*/
        allTotalConsumeComplexIndex = allTotalConsumeComplexIndex.stream().filter(item -> objects.contains(String.valueOf(item.getObjectId()))).toList();
        List<StructureOfComplexIndexValue> valueList = new ArrayList<>();
        for (ComplexIndex totalConsumeComplexIndex : allTotalConsumeComplexIndex) {
            StructureOfComplexIndexValue temp = new StructureOfComplexIndexValue();
            temp.setComplexIndexId(totalConsumeComplexIndex.getComplexIndexId());
            temp.setResourceStructureId(totalConsumeComplexIndex.getObjectId());
            temp.setResourceStructureName(totalConsumeComplexIndex.getObjectName());
            temp.setBusinessTypeId(totalConsumeComplexIndex.getBusinessTypeId());
            valueList.add(temp);
        }
        /*分别计算当前时间和同比和环比的值*/
        energyOverViewService.GetConsumeOfStructureList(valueList, 0, params.getStartTime(), params.getEndTime(), "d", false);
        for (String object : objects) {
            EnergyPercentageAnalysisDTO temp = new EnergyPercentageAnalysisDTO();
            Double value = 0d;
            for (StructureOfComplexIndexValue structureOfComplexIndexValue : valueList) {
                if (ObjectUtil.isNotNull(energyCoalMap.get(structureOfComplexIndexValue.getBusinessTypeId())) && structureOfComplexIndexValue.getResourceStructureId().equals(Integer.valueOf(object))) {
                    temp.setName(structureOfComplexIndexValue.getResourceStructureName());
                    value += ObjectUtil.isNull(structureOfComplexIndexValue.getSumValue()) ? 0d : structureOfComplexIndexValue.getSumValue() * energyCoalMap.get(structureOfComplexIndexValue.getBusinessTypeId()).getCoal();
                }
            }
            temp.setValue(NumberUtil.doubleAccuracy(value, 2));
            res.add(temp);
        }
        return res;
    }

    @Override
    public List<EnergyUseBarDTO> findUseEnergyRank(UseEnergyComparedParamsDTO params) {
        /*如果是默认层级*/
        if (ObjectUtil.isNull(params.getResourceStructures())) {
            ResourceStructureTreeDTO resourceAll = resourceStructureManager.getTreeDtoRoot();
            List<Integer> resourceList = new ArrayList<>();
            String resourceString;
            if (resourceAll.getChildren().size() != 0) {
                for (ResourceStructureTreeDTO child : resourceAll.getChildren()) {
                    resourceList.add(child.getId());
                }
                resourceString = StringUtils.join(resourceList, ",");
            } else {
                resourceString = String.valueOf(resourceAll.getId());
            }
            params.setResourceStructures(resourceString);
        }
        List<EnergyConsumeConst> energyConsumeConst = energyConsumeConstMapper.selectList(null);
        List<EnergyUseBarDTO> res = new ArrayList<>();
        Map<Integer, ComplexIndexBusinessTypeCoefficient> energyCoalMap = getBusinessTypeCoalMap();
        List<String> objects = List.of(params.getResourceStructures().split(","));
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        /*过滤给出层级下的总量指标*/
        allTotalConsumeComplexIndex = allTotalConsumeComplexIndex.stream().filter(item -> objects.contains(String.valueOf(item.getObjectId()))).toList();
        List<StructureOfComplexIndexValue> valueList = new ArrayList<>();
        for (ComplexIndex totalConsumeComplexIndex : allTotalConsumeComplexIndex) {
            StructureOfComplexIndexValue temp = new StructureOfComplexIndexValue();
            temp.setComplexIndexId(totalConsumeComplexIndex.getComplexIndexId());
            temp.setResourceStructureId(totalConsumeComplexIndex.getObjectId());
            temp.setResourceStructureName(totalConsumeComplexIndex.getObjectName());
            temp.setBusinessTypeId(totalConsumeComplexIndex.getBusinessTypeId());
            temp.setBusinessTypeName(totalConsumeComplexIndex.getBusinessTypeName());
            temp.setUnit(totalConsumeComplexIndex.getUnit());
            valueList.add(temp);
        }
        /*计算当前时间的值*/
        energyOverViewService.GetConsumeOfStructureList(valueList, 0, params.getStartTime(), params.getEndTime(), "d", false);
        for (String object : objects) {
            EnergyConsumeConst findEnergyConsumeConst = new EnergyConsumeConst();
            for (EnergyConsumeConst consumeConst : energyConsumeConst) {
                if (consumeConst.getObjectId().equals(object)) {
                    findEnergyConsumeConst = consumeConst;
                }
            }
            EnergyUseBarDTO energyUseBarDTO = new EnergyUseBarDTO();
            energyUseBarDTO.setData(new ArrayList<>());
            for (ComplexIndexBusinessTypeCoefficient value : energyCoalMap.values()) {
                EnergyUseBarDataDTO energyUseBarDataDTO = new EnergyUseBarDataDTO();
                List<StructureOfComplexIndexValue> findValueList = valueList.stream().filter(item -> item.getResourceStructureId().equals(Integer.valueOf(object)) && item.getBusinessTypeId().equals(value.getBusinessTypeId())).toList();
                energyUseBarDataDTO.setBusinessTypeId(value.getBusinessTypeId());
                energyUseBarDataDTO.setBusinessTypeName(value.getBusinessTypeName());
                energyUseBarDTO.setObjectId(Integer.valueOf(object));
                energyUseBarDTO.setObjectName(resourceStructureManager.getResourceStructureById(Integer.valueOf(object)).getResourceStructureName());
                if (findValueList.isEmpty()) {
                    energyUseBarDataDTO.setValueEnergy(0d);
                    energyUseBarDataDTO.setManAvgEnergy(0d);
                    energyUseBarDataDTO.setAreaAvgEnergy(0d);

                } else {
                    Double v = ObjectUtil.isNull(findValueList.get(0).getSumValue()) ? 0d : findValueList.get(0).getSumValue();
                    energyUseBarDataDTO.setValueEnergy(v);
                    energyUseBarDataDTO.setUnit(findValueList.get(0).getUnit());
                    if (ObjectUtil.isNull(findEnergyConsumeConst.getArea()) || findEnergyConsumeConst.getArea() == 0) {
                        energyUseBarDataDTO.setAreaAvgEnergy(0d);
                    } else {
                        energyUseBarDataDTO.setAreaAvgEnergy(NumberUtil.doubleAccuracy(v / findEnergyConsumeConst.getArea(), 2));
                    }
                    if (ObjectUtil.isNull(findEnergyConsumeConst.getPeoples()) || findEnergyConsumeConst.getPeoples() == 0) {
                        energyUseBarDataDTO.setManAvgEnergy(0d);
                    } else {
                        energyUseBarDataDTO.setManAvgEnergy(NumberUtil.doubleAccuracy(v / findEnergyConsumeConst.getPeoples(), 2));
                    }
                }
                energyUseBarDTO.getData().add(energyUseBarDataDTO);
            }
            res.add(energyUseBarDTO);
        }
        return res;
    }

    @Override
    public List<EnergyUseTrendResult> findUseEnergyTrend(UseEnergyComparedParamsDTO params) {
        List<EnergyUseTrendResult> res = new ArrayList<>();
        /*如果是默认层级*/
        if (ObjectUtil.isNull(params.getResourceStructures())) {
            ResourceStructureTreeDTO resourceAll = resourceStructureManager.getTreeDtoRoot();
            List<Integer> resourceList = new ArrayList<>();
            String resourceString;
            if (resourceAll.getChildren().size() != 0) {
                for (ResourceStructureTreeDTO child : resourceAll.getChildren()) {
                    resourceList.add(child.getId());
                }
                resourceString = StringUtils.join(resourceList, ",");
            } else {
                resourceString = String.valueOf(resourceAll.getId());
            }
            params.setResourceStructures(resourceString);
        }
        long nd = 1000 * 24 * 60 * 60;
        long diff = params.getStartTime().getTime() - params.getEndTime().getTime();
        long day = Math.abs(diff / nd);
        String formatString_1 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_1");
        String formatString_2 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_2");
        String formatString_3 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_3");
        String formatString_4 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_4");
        List<String> objects = List.of(params.getResourceStructures().split(","));
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        List<EnergyConsumeConst> energyConsumeConst = energyConsumeConstMapper.selectList(null);
        /*过滤给出层级下的总量指标*/
        allTotalConsumeComplexIndex = allTotalConsumeComplexIndex.stream().filter(item -> objects.contains(String.valueOf(item.getObjectId()))).toList();
        List<Integer> ids = new ArrayList<>();
        for (ComplexIndex totalConsumeComplexIndex : allTotalConsumeComplexIndex) {
            ids.add(totalConsumeComplexIndex.getComplexIndexId());
        }
        Map<Integer, ComplexIndexBusinessTypeCoefficient> energyCoalMap = getBusinessTypeCoalMap();
        SimpleDateFormat formatter = formatter = new SimpleDateFormat(formatString_1);
        List<EnergyComplexIndexQueryResult> complexIndexQueryResults = new ArrayList<>();

        if (day > 31) {
            /*展示月*/
            Calendar startTime = Calendar.getInstance();
            Calendar endTime = Calendar.getInstance();
            startTime.setTime(params.getStartTime());
            endTime.setTime(params.getEndTime());
            if (startTime.get(Calendar.YEAR) != endTime.get(Calendar.YEAR)) {
                formatter = new SimpleDateFormat(formatString_1);
            } else {
                formatter = new SimpleDateFormat(formatString_2);
            }
            complexIndexQueryResults = getUseEnergyTrendMonth(params.getStartTime(), params.getEndTime(), ids);
        }
        if (day <= 31 && day > 0) {
            /*展示天*/
            complexIndexQueryResults = getComplexIndexResultBatchGroupByIdAndTime(params.getStartTime(), params.getEndTime(), ids, "d");
            formatter = new SimpleDateFormat(formatString_3);
        }
        if (day == 0) {
            /*展示时*/
            complexIndexQueryResults = getComplexIndexResultBatchGroupByIdAndTime(params.getStartTime(), params.getEndTime(), ids, "h");
            formatter = new SimpleDateFormat(formatString_4);
        }

        for (String object : objects) {
            Integer people = 0;
            Double area = 0d;
            for (EnergyConsumeConst consumeConst : energyConsumeConst) {
                if (consumeConst.getObjectId().equals(object)) {
                    people = consumeConst.getPeoples();
                    area = consumeConst.getArea();
                }
            }
            EnergyUseTrendResult temp = new EnergyUseTrendResult();
            temp.setObjectId(Integer.valueOf(object));
            temp.setObjectName(resourceStructureManager.getResourceStructureById(Integer.valueOf(object)).getResourceStructureName());
            List<String> timeList = new ArrayList<>();
            for (EnergyComplexIndexQueryResult complexIndexQueryResult : complexIndexQueryResults) {
                timeList.add(complexIndexQueryResult.getTime());
            }
            List<ComplexIndex> filterComp = allTotalConsumeComplexIndex.stream().filter(i -> i.getObjectId().equals(Integer.valueOf(object))).toList();
            timeList = timeList.stream().distinct().collect(Collectors.toList());
            List<EnergyUseTrendData> energyUseTrendDataList = new ArrayList<>();
            for (String times : timeList) {
                EnergyUseTrendData energyUseTrendData = new EnergyUseTrendData();
                List<EnergyComplexIndexQueryResult> filterRes = complexIndexQueryResults.stream().filter(i -> filterComp.stream().filter(item -> item.getComplexIndexId().equals(Integer.valueOf(i.getComplexIndexId()))).toList().size() != 0 && i.getTime().equals(times)).toList();
                Double energyValue = 0d;
                for (EnergyComplexIndexQueryResult resItem : filterRes) {
                    ComplexIndex findComp = filterComp.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(resItem.getComplexIndexId()))).toList().get(0);
                    energyValue += Double.valueOf(resItem.getResult()) * energyCoalMap.get(findComp.getBusinessTypeId()).getCoal();
                    energyUseTrendData.setEnergyValue(NumberUtil.doubleAccuracy(energyValue, 2));
                    energyUseTrendData.setManAvgEnergy(NumberUtil.doubleAccuracy(people == 0 ? 0d : energyValue / people, 2));
                    energyUseTrendData.setAreaAvgEnergy(NumberUtil.doubleAccuracy(area == 0 ? 0d : energyValue / area, 2));
                    String t = times;
                    for (String s : Arrays.asList("T", "Z")) {
                        t = t.replace(s, " ");
                    }
                    energyUseTrendData.setName(formatter.format(DateUtil.stringToDate(t)));
                }
                energyUseTrendDataList.add(energyUseTrendData);
            }
            temp.setData(energyUseTrendDataList);
            res.add(temp);
        }

        return res;
    }


    @Override
    public EnergySavingDataDTO findSavingEnergyData(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        EnergySavingDataDTO res = new EnergySavingDataDTO();
        List<Date> yoY = getCoCDateOrYoYDate(startTime, endTime, "YoY");
        List<Date> coC = getCoCDateOrYoYDate(startTime, endTime, "CoC");
        EnergyConsumeData value = findSavingEnergyDataByTimeObjectIdBusinessTypeId(startTime, endTime, objectId, businessTypeId);
        EnergyConsumeData valueYoY = findSavingEnergyDataByTimeObjectIdBusinessTypeId(yoY.get(0), yoY.get(1), objectId, businessTypeId);
        EnergyConsumeData valueCoC = findSavingEnergyDataByTimeObjectIdBusinessTypeId(coC.get(0), coC.get(1), objectId, businessTypeId);
        res.setPlanValue(NumberUtil.doubleAccuracy(value.getPlanValue(), 2));
        res.setEnergySavingValue(NumberUtil.doubleAccuracy(value.getEnergySavingValue(), 2));
        res.setOverstepValue(NumberUtil.doubleAccuracy(value.getOverstepValue(), 2));
        res.setPlanValueCoC(getCoCOrYoYByValue(value.getPlanValue(), valueCoC.getPlanValue()));
        res.setPlanValueYoY(getCoCOrYoYByValue(value.getPlanValue(), valueYoY.getPlanValue()));
        res.setEnergySavingValueCoC(getCoCOrYoYByValue(value.getEnergySavingValue(), valueCoC.getEnergySavingValue()));
        res.setEnergySavingValueYoY(getCoCOrYoYByValue(value.getEnergySavingValue(), valueYoY.getEnergySavingValue()));
        res.setOverstepValueCoC(getCoCOrYoYByValue(value.getOverstepValue(), valueCoC.getOverstepValue()));
        res.setOverstepValueYoY(getCoCOrYoYByValue(value.getOverstepValue(), valueYoY.getOverstepValue()));
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, true);
        EnergyTypeResourceComplexIndexMapDTO findMap = complexIndexMaps.stream().filter(i -> i.getBusinessType().getBusinessTypeId().equals(businessTypeId)).findFirst().orElse(null);
        if (ObjectUtil.isNull(findMap)) {
            res.setValue(0d);
            res.setValueCoC(0d);
            res.setValueYoY(0d);
        } else {
            Double v = findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, findMap.getComplexIndex(), null);
            Double vCoC = getValueYoYOrValueCoC(startTime, endTime, findMap.getComplexIndex(), "CoC", null);
            Double vYoY = getValueYoYOrValueCoC(startTime, endTime, findMap.getComplexIndex(), "YoY", null);
            res.setValue(NumberUtil.doubleAccuracy(v, 2));
            res.setValueCoC(getCoCOrYoYByValue(v, vCoC));
            res.setValueYoY(getCoCOrYoYByValue(v, vYoY));
            res.setUnit(ObjectUtil.isNull(findMap.getComplexIndex()) ? "" : findMap.getComplexIndex().getUnit());
        }
        return res;
    }

    @Override
    public List<EnergySavingListDTO> findSavingEnergyList(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        List<EnergySavingListDTO> res = new ArrayList<>();
        List<ResourceStructure> parentList = resourceStructureManager.getResourceStructureLstByParentId(objectId);
        List<EnergyConsumeConst> energyConsumeConst = energyConsumeConstMapper.selectList(null);
        ComplexIndexBusinessType findBusinessType = complexIndexBusinessTypeService.findAll().stream().filter(i -> i.getBusinessTypeId().equals(businessTypeId)).findFirst().orElse(null);
        for (ResourceStructure resourceStructure : parentList) {
            EnergyConsumeConst findConst = energyConsumeConst.stream().filter(i -> i.getObjectId().equals(String.valueOf(resourceStructure.getResourceStructureId()))).findFirst().orElse(null);
            EnergySavingDataDTO data = findSavingEnergyData(startTime, endTime, resourceStructure.getResourceStructureId(), businessTypeId);
            EnergySavingListDTO temp = new EnergySavingListDTO(data);
            temp.setObjectId(resourceStructure.getResourceStructureId());
            temp.setObjectName(resourceStructure.getResourceStructureName());
            temp.setLevelOfPath(resourceStructureManager.getFullPath(resourceStructure.getResourceStructureId()));
            temp.setAddEnergySaving(temp.getEnergySavingValue() - temp.getValue());
            temp.setEnergySavingRate();
            temp.setBusinessTypeName(findBusinessType.getBusinessTypeName());
            if (ObjectUtil.isNull(findConst)) {
                temp.setArea(0d);
                temp.setPeople(0);
            } else {
                temp.setArea(findConst.getArea());
                temp.setPeople(findConst.getPeoples());
            }
            res.add(temp);
        }
        return res;
    }

    @Override
    public List<EnergyDemandTrendDTO> findDemandTrend(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        List<EnergyDemandTrendDTO> res = new ArrayList<>();
        long nd = 1000 * 24 * 60 * 60;
        long diff = startTime.getTime() - endTime.getTime();
        long day = Math.abs(diff / nd);
        List<String> timeList = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(objectId, true);
        EnergyTypeResourceComplexIndexMapDTO findMap = complexIndexMaps.stream().filter(i -> i.getBusinessType().getBusinessTypeId().equals(businessTypeId)).findFirst().orElse(null);
        List<Integer> ids = new ArrayList<>();
        if (ObjectUtil.isNotNull(findMap) && ObjectUtil.isNotNull(findMap.getComplexIndex())) {
            ids.add(findMap.getComplexIndex().getComplexIndexId());
        }
        List<EnergyComplexIndexQueryResult> complexIndexQueryResults = new ArrayList<>();
        List<EnergyConsumeData> energyConsumeDataList = new ArrayList<>();
        if (day > 31) {
            /*展示月*/
            timeList = getTimeRangeList(startTime, endTime, "m");
            if (!ids.isEmpty()) {
                complexIndexQueryResults = getUseEnergyTrendMonth(startTime, endTime, ids);
            }
            energyConsumeDataList = getConsumeData(startTime, endTime, objectId, businessTypeId);
            for (int i = 0; i < timeList.size(); i++) {
                EnergyDemandTrendDTO temp = new EnergyDemandTrendDTO();
                if (ids.isEmpty()) {
                    temp.setValue(0d);
                } else {
                    temp.setValue(Double.valueOf(complexIndexQueryResults.get(i).getResult()));
                }
                temp.setPlanValue(energyConsumeDataList.get(i).getPlanValue());
                temp.setOverstepValue(energyConsumeDataList.get(i).getOverstepValue());
                temp.setName(formatDateToName(DateUtil.stringToDate(timeList.get(i)), 2));
                res.add(temp);
            }
        }
        if (day <= 31 && day > 0) {
            /*展示天*/
            timeList = getTimeRangeList(startTime, endTime, "d");
            if (!ids.isEmpty()) {
                complexIndexQueryResults = getComplexIndexResultBatchGroupByIdAndTime(startTime, endTime, ids, "d");
            }
            for (int i = 0; i < timeList.size(); i++) {
                String dateString = timeList.get(i).substring(0, 7);
                EnergyDemandTrendDTO temp = new EnergyDemandTrendDTO();
                if (ids.isEmpty()) {
                    temp.setValue(0d);
                } else {
                    temp.setValue(Double.valueOf(complexIndexQueryResults.get(i).getResult()));
                }
                temp.setPlanValue(findSavingEnergyDataAvgDayByTimeObjectIdBusinessTypeId(dateString, objectId, businessTypeId).getPlanValue());
                temp.setOverstepValue(findSavingEnergyDataAvgDayByTimeObjectIdBusinessTypeId(dateString, objectId, businessTypeId).getOverstepValue());
                temp.setName(formatDateToName(DateUtil.stringToDate(timeList.get(i)), 3));
                res.add(temp);
            }
        }
        if (day == 0) {
            /*展示时*/
            timeList = getTimeRangeList(startTime, endTime, "h");
            if (!ids.isEmpty()) {
                complexIndexQueryResults = getComplexIndexResultBatchGroupByIdAndTime(startTime, endTime, ids, "h");
            }
            for (int i = 0; i < timeList.size(); i++) {
                String dateString = timeList.get(i).substring(0, 7);
                EnergyDemandTrendDTO temp = new EnergyDemandTrendDTO();
                if (ids.isEmpty()) {
                    temp.setValue(0d);
                } else {
                    temp.setValue(Double.valueOf(complexIndexQueryResults.get(i).getResult()));
                }
                temp.setPlanValue(findSavingEnergyDataAvgDayByTimeObjectIdBusinessTypeId(dateString, objectId, businessTypeId).getPlanValue() / 24);
                temp.setOverstepValue(findSavingEnergyDataAvgDayByTimeObjectIdBusinessTypeId(dateString, objectId, businessTypeId).getOverstepValue() / 24);
                temp.setName(formatDateToName(DateUtil.stringToDate(timeList.get(i)), 4));
                res.add(temp);
            }
        }
        for (EnergyDemandTrendDTO re : res) {
            re.setValue(NumberUtil.doubleAccuracy(re.getValue(), 2));
            re.setPlanValue(NumberUtil.doubleAccuracy(re.getPlanValue(), 2));
            re.setOverstepValue(NumberUtil.doubleAccuracy(re.getOverstepValue(), 2));
        }
        return res;
    }

    @Override
    public UseEnergyAndCarbonTotal findUseEnergyAndCarbonTotal(Date startTime, Date endTime, Integer objectId, Integer businessTypeId, Integer userId) {
        UseEnergyAndCarbonTotal res = new UseEnergyAndCarbonTotal();
        List<EnergyComparativeAnalysisDTO> comparativeAnalysis = this.findComparativeAnalysis(startTime, endTime, objectId, userId, null);
        List<EnergyComparativeAnalysisDTO> energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId().equals(businessTypeId)).toList();
        if (energyComparativeAnalysisDTOS.isEmpty()) {
            return res;
        }
        EnergyComparativeAnalysisDTO findNowAnalysisDTO = energyComparativeAnalysisDTOS.get(0);
        res.setUseEnergyTotal(findNowAnalysisDTO.getValue());
        res.setUseEnergyYoY(findNowAnalysisDTO.getYoY());
        res.setCo2Total(findNowAnalysisDTO.getValueCo2());
        res.setCo2YoY(findNowAnalysisDTO.getYoY());
        res.setUnit(findNowAnalysisDTO.getUnit());
        return res;
    }

    @Override
    public EnergyTrendAnalysisDTO findUseElectricityTrendAnalysis(Date startTime, Date endTime, Integer objectId, Integer businessTypeId, String timeType, Integer userId) {
        EnergyTrendAnalysisDTO res = new EnergyTrendAnalysisDTO();
        if (timeType.equals("m")) {
            /*展示月*/
            res = getEnergyTrendSeriesMonth(startTime, endTime, objectId, true, userId);
        }
        if (timeType.equals("d")) {
            /*展示天*/
            res = getEnergyTrendSeriesDay(startTime, endTime, objectId, true, "d", userId);
        }
        if (timeType.equals("h")) {
            /*展示时*/
            res = getEnergyTrendSeriesDay(startTime, endTime, objectId, true, "h", userId);
        }
        res.setSeries(res.getSeries().stream().filter(item -> item.getBusinessTypeId().equals(businessTypeId)).toList());
        ;
        return res;
    }


    public String formatDateToName(Date date, Integer type) {
        String formatString_1 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_1");
        String formatString_2 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_2");
        String formatString_3 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_3");
        String formatString_4 = messageSourceUtil.getMessage("energy.totalanalysis.formatString_4");
        SimpleDateFormat formatter;
        switch (type) {
            case 2:
                formatter = new SimpleDateFormat(formatString_2);
                break;
            case 3:
                formatter = new SimpleDateFormat(formatString_3);
                break;
            case 4:
                formatter = new SimpleDateFormat(formatString_4);
                break;
            case 1:
            default:
                formatter = new SimpleDateFormat(formatString_1);
        }
        return formatter.format(date);
    }

    public List<String> getTimeRangeList(Date startTime, Date endTime, String type) {
        List<String> res = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        cal.setTime(startTime);
        while (cal != null && !cal.getTime().after(endTime)) {//一个月一个月查询,结束条件月开始时间大于结束时间
            res.add(DateUtil.dateToString(cal.getTime()));
            if (type.equals("h")) {
                cal.add(Calendar.HOUR, 1);
            }
            if (type.equals("d")) {
                cal.add(Calendar.DAY_OF_MONTH, 1);
            }
            if (type.equals("m")) {
                cal.add(Calendar.MONTH, 1);
            }
        }
        return res;
    }


    public EnergyConsumeData findSavingEnergyDataAvgDayByTimeObjectIdBusinessTypeId(String yearMonth, Integer objectId, Integer businessTypeId) {
        String[] yearMonthString = yearMonth.split("-");
        List<EnergyConsumeData> energyConsumeData = energyConsumeDataMapper.selectList(new QueryWrapper<EnergyConsumeData>().eq("objectId", objectId).eq("energyTypeId", businessTypeId));
        EnergyConsumeData findEnergyConsume = energyConsumeData.stream()
                .filter(i -> i.getYear().equals(Integer.valueOf(yearMonthString[0])) && i.getMonth().equals(Integer.valueOf(yearMonthString[1])))
                .findFirst().orElseGet(() -> {
                    EnergyConsumeData temp1 = new EnergyConsumeData();
                    temp1.setEnergySavingValue(0d);
                    temp1.setPlanValue(0d);
                    temp1.setOverstepValue(0d);
                    return temp1;
                });
        Integer days = getDayOfMonth(Integer.valueOf(yearMonthString[0]), Integer.valueOf(yearMonthString[1]));
        findEnergyConsume.setEnergySavingValue(findEnergyConsume.getEnergySavingValue() / days);
        findEnergyConsume.setPlanValue(findEnergyConsume.getPlanValue() / days);
        findEnergyConsume.setOverstepValue(findEnergyConsume.getOverstepValue() / days);
        return findEnergyConsume;
    }


    public EnergyConsumeData findSavingEnergyDataByTimeObjectIdBusinessTypeId(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        List<String> yearMonth = getYearMonthByDateRange(startTime, endTime);
        EnergyConsumeData res = new EnergyConsumeData();
        res.setEnergySavingValue(0d);
        res.setPlanValue(0d);
        res.setOverstepValue(0d);
        HashMap<String, EnergyConsumeData> redis = new HashMap<String, EnergyConsumeData>();
        for (String item : yearMonth) {
            EnergyConsumeData avg = redis.get(item);
            if (avg == null) {
                avg = findSavingEnergyDataAvgDayByTimeObjectIdBusinessTypeId(item, objectId, businessTypeId);
            }
            res.setEnergySavingValue(res.getEnergySavingValue() + avg.getEnergySavingValue());
            res.setPlanValue(res.getPlanValue() + avg.getPlanValue());
            res.setOverstepValue(res.getOverstepValue() + avg.getOverstepValue());
        }
        return res;
    }


    private List<EnergyConsumeData> getConsumeData(Date startTime, Date endTime, Integer objectId, Integer businessTypeId) {
        List<EnergyConsumeData> res = new ArrayList<>();
        Date startTimeMonthOfFirstDay = DateUtil.getFirstDayOfMonth(startTime);//开始时间当月第一天
        Date startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTime);//开始时间当月最后一天
        EnergyConsumeData temp = findSavingEnergyDataByTimeObjectIdBusinessTypeId(startTime, startTimeMonthOfLastDay, objectId, businessTypeId);
        res.add(temp);
        startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTime);
        while (startTimeMonthOfFirstDay != null && !startTimeMonthOfFirstDay.after(endTime)) {//一个月一个月查询,结束条件月开始时间大于结束时间
            startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTimeMonthOfFirstDay);
            temp = findSavingEnergyDataByTimeObjectIdBusinessTypeId(startTimeMonthOfFirstDay, startTimeMonthOfLastDay, objectId, businessTypeId);
            res.add(temp);
            startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTimeMonthOfFirstDay);
        }
        return res;
    }


    private List<EnergyComplexIndexQueryResult> getUseEnergyTrendMonth(Date startTime, Date endTime, List<Integer> ids) {

        List<EnergyComplexIndexQueryResult> result = new ArrayList<>();
        Date startTimeMonthOfFirstDay = DateUtil.getFirstDayOfMonth(startTime);//开始时间当月第一天
        Date startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTime);//开始时间当月最后一天
        List<EnergyComplexIndexQueryResult> temp = getComplexIndexResultBatchGroupById(startTime, startTimeMonthOfLastDay, ids);
        if (CollectionUtil.isNotEmpty(temp)) {
            result.addAll(temp);
        }
        startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTime);
        while (startTimeMonthOfFirstDay != null && !startTimeMonthOfFirstDay.after(endTime)) {//一个月一个月查询,结束条件月开始时间大于结束时间
            startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTimeMonthOfFirstDay);
            temp = getComplexIndexResultBatchGroupById(startTimeMonthOfFirstDay, startTimeMonthOfLastDay, ids);
            if (CollectionUtil.isNotEmpty(temp)) {
                result.addAll(temp);
            }
            startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTimeMonthOfFirstDay);
        }

        return result;
    }

    private List<EnergyComplexIndexQueryResult> getComplexIndexResultBatchGroupById(Date startTime, Date endTime, List<Integer> ids) {
        WhereNested queryBuilderWhere = select()
                .sum("IndexValue")
                .as("result")
                .from(database, "EnergyHisDayData")
                .where(gte("time", DateUtil.dateToString(startTime)))
                .and(lte("time", DateUtil.dateToString(endTime)))
                .and(eq("Abnormal", "0"))
                .andNested();

        for (Integer id : ids) {
            queryBuilderWhere.or(eq("ComplexIndexId", id.toString()));
        }
        Query queryBuilder = queryBuilderWhere.close().groupBy("ComplexIndexId").fill(0);
        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        query = influxDB.query(queryBuilder);
        List<EnergyComplexIndexQueryResult> resComplexIndexQueryResult = new ArrayList<>();
        if (query != null) {
            resComplexIndexQueryResult = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, "EnergyHisDayData");
        }
        if (resComplexIndexQueryResult.isEmpty()) {
            for (Integer id : ids) {
                EnergyComplexIndexQueryResult temp = new EnergyComplexIndexQueryResult(DateUtil.dateToString(startTime), "0.0", id.toString());
                resComplexIndexQueryResult.add(temp);
            }
        }
        return resComplexIndexQueryResult;
    }

    private List<EnergyComplexIndexQueryResult> getComplexIndexResultBatchGroupByIdAndTime(Date startTime, Date endTime, List<Integer> ids, String time) {
        String databaseInfluxdb = "EnergyHisDayData";
        if (time.equals("h")) {
            databaseInfluxdb = "EnergyHisHourData";
        }
        WhereNested queryBuilderWhere = select()
                .sum("IndexValue")
                .as("result")
                .from(database, databaseInfluxdb)
                .where(gte("time", DateUtil.dateToString(startTime)))
                .and(lte("time", DateUtil.dateToString(endTime)))
                .and(eq("Abnormal", "0"))
                .andNested();

        for (Integer id : ids) {
            queryBuilderWhere.or(eq("ComplexIndexId", id.toString()));
        }
        Query queryBuilder = queryBuilderWhere.close().groupBy(time(1L, time), "ComplexIndexId").fill(0);
        String sql = "";
        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        query = influxDB.query(queryBuilder);
        List<EnergyComplexIndexQueryResult> resComplexIndexQueryResult = new ArrayList<>();
        if (query != null) {
            resComplexIndexQueryResult = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, databaseInfluxdb);
        }
        if (resComplexIndexQueryResult.isEmpty()) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(startTime);
            while (cal != null && !cal.getTime().after(endTime)) {//一个月一个月查询,结束条件月开始时间大于结束时间
                for (Integer id : ids) {
                    EnergyComplexIndexQueryResult temp = new EnergyComplexIndexQueryResult(DateUtil.dateToString(cal.getTime()), "0.0", id.toString());
                    resComplexIndexQueryResult.add(temp);
                }
                if (time.equals("h")) {
                    cal.add(Calendar.HOUR, 1);// 24小时制
                }
                if (time.equals("d")) {
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }
            }
        }
        return resComplexIndexQueryResult;
    }

    /**
     * @Description: 获取能耗类型和转标煤map映射
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/19 16:37
     **/
    private Map<Integer, ComplexIndexBusinessTypeCoefficient> getBusinessTypeCoalMap() {
        List<ComplexIndexBusinessType> complexIndexBusinessTypeList = complexIndexBusinessTypeMapper.selectList(new QueryWrapper<ComplexIndexBusinessType>().eq("parentId", 1));
        List<EnergyDataItem> energyDataItems = energyDataConfigItemService.getAllByEntryIdTimeliness(7);
        Map<Integer, ComplexIndexBusinessTypeCoefficient> energyCoalMap = new HashMap<>();
        for (ComplexIndexBusinessType complexIndexBusinessType : complexIndexBusinessTypeList) {
            if (complexIndexBusinessType.getDescription() != "-1") {
                for (EnergyDataItem item : energyDataItems) {
                    if (item.getItemId().equals(Integer.valueOf(complexIndexBusinessType.getDescription()))) {
                        ComplexIndexBusinessTypeCoefficient c = new ComplexIndexBusinessTypeCoefficient(complexIndexBusinessType);
                        c.setCoal(Double.parseDouble(ObjectUtil.isNull(item.getExtendField1()) ? "0" : item.getExtendField1()));
                        c.setCo2(Double.parseDouble(ObjectUtil.isNull(item.getExtendField3()) ? "0" : item.getExtendField3()));
                        energyCoalMap.put(complexIndexBusinessType.getBusinessTypeId(), c);
                    }
                }
            }
        }
        return energyCoalMap;
    }


    private Double getCoCOrYoYByValue(Double value, Double beforeValue) {
        Double res = 0d;
        if (beforeValue == 0.0) {
            if (value == 0.0) {
                res = 0d;
            } else {
                res = 100d;
            }
        } else {
            res = NumberUtil.doubleAccuracy(((value - beforeValue) / beforeValue) * 100, 2);
        }
        return res;
    }

    private List<String> getYearMonthByDateRange(Date startTime, Date endTime) {
        List<String> res = new ArrayList<>();
        Calendar dd = Calendar.getInstance();
        dd.setTime(startTime);
        while (dd.getTime().before(endTime)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String str = sdf.format(dd.getTime());
            res.add(str);
            dd.add(Calendar.DAY_OF_MONTH, 1);
        }
        return res;
    }

    /**
     * @param year:
     * @param month:
     * @Description: 获取某年某月有多少天
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:55
     * @return: java.lang.Integer
     **/
    public Integer getDayOfMonth(int year, int month) {
        Calendar c = Calendar.getInstance();
        c.set(year, month, 0); //输入类型为int类型
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * @param year:
     * @param month:
     * @param id:
     * @param objectId:
     * @Description:获取某层级下根据年月能耗类型求平均计划量
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:56
     * @return: java.lang.Double
     **/
    private Double findPlanValue(Integer year, Integer month, Integer id, Integer objectId) {
        Integer rootObjectId;
        if (ObjectUtil.isNull(objectId)) {
            ResourceStructure resourceStructure = resourceStructureManager.getRoot();
            // 获取根节点
            rootObjectId = resourceStructure.getResourceStructureId();
        } else {
            rootObjectId = objectId;
        }
        List<EnergyConsumeData> energyConsumeData = energyConsumeDataMapper.selectList(new QueryWrapper<EnergyConsumeData>().eq("objectId", rootObjectId).eq("energyTypeId", id).eq("year", year));
        List<Double> planValueAvg = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            EnergyConsumeData findDateByMonth = null;
            for (EnergyConsumeData item : energyConsumeData) {
                if (item.getMonth().equals(i)) {
                    findDateByMonth = item;
                }
            }
            if (findDateByMonth == null) {
                planValueAvg.add(0d);
            } else {
                planValueAvg.add(findDateByMonth.getPlanValue() / getDayOfMonth(year, i));
            }
        }
        return planValueAvg.get(month - 1);
    }

    /**
     * @param energyDataItems:字典表    为了减少查询
     * @param itemId:能耗类型Description
     * @Description: 根据能耗类型Description获取转换系数，数组[标煤系数][co2系数]
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:57
     * @return: java.util.List<java.lang.Double>
     **/
    private List<Double> findParamsByBusinessType(List<EnergyDataItem> energyDataItems, Integer itemId) {
        List<Double> res = new ArrayList<>();
        for (EnergyDataItem item : energyDataItems) {
            if (item.getItemId().equals(itemId)) {
                res.add(item.getExtendField1() == null ? 0 : Double.parseDouble(item.getExtendField1()));
                res.add(item.getExtendField3() == null ? 0 : Double.parseDouble(item.getExtendField3()));
            }
        }
        return res;
    }


    /**
     * @param startTime:    开始时间
     * @param endTime:      结束时间
     * @param complexIndex: 指标
     * @Description: 查指标值 跑sql
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:59
     * @return: java.lang.Double
     **/
    private Double findSumValueOfHistoryComplexIndexByIdAndDuration(Date startTime, Date endTime, ComplexIndex complexIndex, String timeType) {
        if (ObjectUtil.isNull(complexIndex)) {
            return 0.0;
        }
        String tableName = "EnergyHisDayData";
        if (timeType != null) {
            switch (timeType) {
                case "m", "y" -> {
                    tableName = "EnergyHisMonthData";
                }
                default -> tableName = "EnergyHisDayData";
            }
        }

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        Query queryBuilder = select()
                .sum("IndexValue")
                .as("result")
                .from(database, tableName)
                .where(gte("time", DateUtil.dateToString(startTime)))
                .and(lte("time", DateUtil.dateToString(endTime)))
                .and(eq("ComplexIndexId", complexIndex.getComplexIndexId().toString()))
                .and(eq("Abnormal", "0"))
                .fill(0);

        query = influxDB.query(queryBuilder);
        if (query != null) {
            List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, tableName);
            if (!resultComplexIndexQuery.isEmpty()) {
                return Double.parseDouble(resultComplexIndexQuery.get(0).result);
            }
        }
        return 0.0;
    }

    /**
     * @param startTime:    开始时间
     * @param endTime:      结束时间
     * @param complexIndex: 指标
     * @param time:         类似 h d m
     * @Description: 查指标值 groupBy time()
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:59
     * @return: java.lang.Double
     **/
    private List<EnergyComplexIndexQueryResult> findSumValueOfHistoryComplexIndexByIdAndDurationGroupBy(Date startTime, Date endTime, ComplexIndex complexIndex, String time) {

        String databaseInfluxdb = "EnergyHisDayData";
        if (time.equals("h")) {
            databaseInfluxdb = "EnergyHisHourData";
        }
        List<EnergyComplexIndexQueryResult> res = new ArrayList<>();
        if (ObjectUtil.isNull(complexIndex)) {
            List<String> dateList = new ArrayList<>();
            if (time.equals("h")) {
                dateList = DateUtil.getDateRange("H", startTime, endTime, 1);
            }
            if (time.equals("d")) {
                dateList = DateUtil.getDateRange("D", startTime, endTime, 1);
            }
            for (String s : dateList) {
                EnergyComplexIndexQueryResult temp = new EnergyComplexIndexQueryResult();
                temp.setResult("0.0");
                temp.setTime(s);
                res.add(temp);
            }
            return res;
        } else {
            String sql = null;
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            ;
            Query queryBuilder = select()
                    .sum("IndexValue")
                    .as("result")
                    .from(database, databaseInfluxdb)
                    .where(gte("time", DateUtil.dateToString(startTime)))
                    .and(lte("time", DateUtil.dateToString(endTime)))
                    .and(eq("ComplexIndexId", complexIndex.getComplexIndexId().toString()))
                    .and(eq("Abnormal", "0"))
                    .groupBy(time(1L, time))
                    .fill(0);
            query = influxDB.query(queryBuilder);

            List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, databaseInfluxdb);
            /*如果没有找到对应的总量指标，sql里面去掉where语句，拿到时间数组以后循环赋值为0*/
            if (resultComplexIndexQuery.size() == 0) {
                List<String> dateList = new ArrayList<>();
                if (time.equals("h")) {
                    dateList = DateUtil.getDateRange("H", startTime, endTime, 1);
                }
                if (time.equals("d")) {
                    dateList = DateUtil.getDateRange("D", startTime, endTime, 1);
                }
                for (String s : dateList) {
                    EnergyComplexIndexQueryResult temp = new EnergyComplexIndexQueryResult();
                    temp.setResult("0.0");
                    temp.setTime(s);
                    res.add(temp);
                }
                return res;
            } else {
                return resultComplexIndexQuery;
            }
        }
    }


    /**
     * @Description:获取某一个时间段内某指标的同比或环比值
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 18:01
     * @return: java.lang.Double
     **/
    private Double getValueYoYOrValueCoC(Date startTime, Date endTime, ComplexIndex complexIndex, String type, String timeType) {
        Date sTime = new Date();
        Date eTime = new Date();
        Calendar calendar = Calendar.getInstance();
        if (type.equals("YoY")) {
            calendar.setTime(startTime);
            calendar.add(Calendar.YEAR, -1);
            sTime = calendar.getTime();
            calendar.setTime(endTime);
            calendar.add(Calendar.YEAR, -1);
            eTime = calendar.getTime();
        }
        if (type.equals("CoC")) {
            long startLong = startTime.getTime() / 1000;
            long endLong = endTime.getTime() / 1000;
            long range = startLong - endLong - 1;
            long newStartLong = startLong + range;
            sTime = new Date(newStartLong * 1000);
            eTime = new Date((startLong - 1) * 1000);
        }
        return findSumValueOfHistoryComplexIndexByIdAndDuration(sTime, eTime, complexIndex, timeType);
    }


    /**
     * @Description:获取某一个时间段内计划量的同比和环比
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 18:01
     * @return: java.lang.Double
     **/
    private Double getPlaneValueYoYOrValueCoC(Date startTime, Date endTime, Integer businessTypeId, Integer objectId, String type) {
        Date sTime = new Date();
        Date eTime = new Date();
        Calendar calendar = Calendar.getInstance();
        if (type.equals("YoY")) {
            calendar.setTime(startTime);
            calendar.add(Calendar.YEAR, -1);
            sTime = calendar.getTime();
            calendar.setTime(endTime);
            calendar.add(Calendar.YEAR, -1);
            eTime = calendar.getTime();
        }
        if (type.equals("CoC")) {
            long startLong = startTime.getTime() / 1000;
            long endLong = endTime.getTime() / 1000;
            long range = startLong - endLong - 1;
            long newStartLong = startLong + range;
            sTime = new Date(newStartLong * 1000);
            eTime = new Date((startLong - 1) * 1000);
        }
        return findPlanValueByEnergyTypeIdAndTime(sTime, eTime, businessTypeId, objectId);
    }

    /**
     * @Description: 获取同比或环比的开始时间和结束时间
     * @Author: Li.Qupan.Pan
     * @Date: 2022/9/16 16:23
     * @return: null
     **/
    private List<Date> getCoCDateOrYoYDate(Date startTime, Date endTime, String type) {
        List<Date> res = new ArrayList<>();
        Date sTime = new Date();
        Date eTime = new Date();
        Calendar calendar = Calendar.getInstance();
        if (type.equals("YoY")) {
            calendar.setTime(startTime);
            calendar.add(Calendar.YEAR, -1);
            sTime = calendar.getTime();
            calendar.setTime(endTime);
            calendar.add(Calendar.YEAR, -1);
            eTime = calendar.getTime();
        }
        if (type.equals("CoC")) {
            // 计算环比时间
            calendar.setTime(startTime);
            calendar.add(Calendar.MONTH, -1);
            sTime = adjustDayOfMonth(calendar, startTime);

            calendar.setTime(endTime);
            calendar.add(Calendar.MONTH, -1);
            eTime = adjustDayOfMonth(calendar, endTime);
        }
        res.add(sTime);
        res.add(eTime);
        return res;
    }
    private static Date adjustDayOfMonth(Calendar calendar, Date originalDate) {
        Calendar originalCal = Calendar.getInstance();
        originalCal.setTime(originalDate);
        int originalDayOfMonth = originalCal.get(Calendar.DAY_OF_MONTH);
        int adjustedDayOfMonth = Math.min(originalDayOfMonth, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.DAY_OF_MONTH, adjustedDayOfMonth);
        return calendar.getTime();
    }

    /**
     * @param objectId:
     * @Description: 获取对应层级节点下能耗业务类型和指标映射，如果层级为空则从根节点拿
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 18:03
     * @return: java.util.List<com.siteweb.energy.dto.EnergyTypeResourceComplexIndexMapDTO>
     **/
    private List<EnergyTypeResourceComplexIndexMapDTO> getEnergyTypeResourceComplexIndexMapDTO(Integer objectId, Boolean isStandardCoal) {
        List<EnergyTypeResourceComplexIndexMapDTO> res = new ArrayList<>();
        Integer rootObjectId;
        Integer rootObjectTypeId;
        if (ObjectUtil.isNull(objectId)) {
            ResourceStructure resourceStructure = resourceStructureManager.getRoot();
            // 获取根节点
            rootObjectId = resourceStructure.getResourceStructureId();
            rootObjectTypeId = resourceStructure.getStructureTypeId();
        } else {
            rootObjectId = objectId;
        }
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        //获取对应层级节点下总量指标
        List<ComplexIndex> rootTotalComplexIndex = allTotalConsumeComplexIndex.stream().filter(item -> item.getObjectId().equals(rootObjectId)).toList();
        List<ComplexIndexBusinessType> complexIndexBusinessTypeList = complexIndexBusinessTypeMapper.selectList(new QueryWrapper<ComplexIndexBusinessType>().eq("parentId", 1));
        //获取字典
        List<EnergyDataItem> energyDataItems = energyDataConfigItemService.getAllByEntryIdTimeliness(7);
        for (ComplexIndexBusinessType item : complexIndexBusinessTypeList) {
            if (isStandardCoal && item.getDescription().equals("-1")) {
                continue;
            }
            EnergyTypeResourceComplexIndexMapDTO temp = new EnergyTypeResourceComplexIndexMapDTO();
            temp.setBusinessType(item);
            //判断是否可以转标煤，如果不能转标煤则系数设为1，相当于没有转
            if (item.getDescription().equals("-1") || !isStandardCoal) {
                temp.setCoalCoefficient(1d);
                temp.setCo2Coefficient(1d);
            } else {
                List<Double> coefficient = findParamsByBusinessType(energyDataItems, Integer.valueOf(item.getDescription()));
                temp.setCoalCoefficient(coefficient.get(0));
                temp.setCo2Coefficient(coefficient.get(1));
            }
            for (ComplexIndex c : rootTotalComplexIndex) {
                if (c.getBusinessTypeId().equals(item.getBusinessTypeId())) {
                    temp.setComplexIndex(c);
                    break;
                }
            }
            res.add(temp);
        }
        return res;
    }

    private List<EnergyTypeResourceComplexIndexMapDTO> getEnergyTypeResourceComplexIndexMapDTOByUserId(Integer objectId, Boolean isStandardCoal, Integer userId) {
        List<EnergyTypeResourceComplexIndexMapDTO> res = new ArrayList<>();
        List<ResourceStructure> roleResourceStructures = new ArrayList<>();
        List<ResourceStructure> filterRoleResourceStructures = new ArrayList<>();
        if (ObjectUtil.isNull(objectId)) {
            // 获取该用户权限下的节点
            roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
            //将节点按类型层级排序
            roleResourceStructures = roleResourceStructures.stream().sorted(Comparator.comparing(ResourceStructure::getStructureTypeId)).collect(Collectors.toList());
        } else {
            ResourceStructure temp = new ResourceStructure();
            temp.setResourceStructureId(objectId);
            roleResourceStructures.add(temp);
        }
        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();

        //这个地方的思路，先按层级的大小遍历看当前层级有没有配置指标，如果有则将当前层级的子层级全部去除。
        for (ResourceStructure objects : roleResourceStructures) {
            //如果当前节点配置了父节点配置了则不考虑当前节点
            if (filterRoleResourceStructures.stream()
                    .map(ResourceStructure::getResourceStructureId)
                    .anyMatch(id -> objects.getLevelOfPath().contains(String.valueOf(id))))
                continue;
            //如果当前层级有配置指标，则加入过滤过的集合中
            if (allTotalConsumeComplexIndex.stream().anyMatch(index -> index.getObjectId().toString().contains(objects.getResourceStructureId().toString()))) {
                filterRoleResourceStructures.add(objects);
            }
        }

        //获取对应层级节点下总量指标
        List<ComplexIndex> roleObjectsTotalComplexIndex = filterRoleResourceStructures.stream()
                .flatMap(item -> allTotalConsumeComplexIndex.stream()
                        .filter(items -> items.getObjectId().equals(item.getResourceStructureId()))).toList();
        List<ComplexIndexBusinessType> complexIndexBusinessTypeList = complexIndexBusinessTypeMapper.selectList(new QueryWrapper<ComplexIndexBusinessType>().eq("parentId", 1));
        //获取字典
        List<EnergyDataItem> energyDataItems = energyDataConfigItemService.getAllByEntryIdTimeliness(7);
        for (ComplexIndexBusinessType item : complexIndexBusinessTypeList) {
            if (isStandardCoal && item.getDescription().equals("-1")) {
                continue;
            }
            EnergyTypeResourceComplexIndexMapDTO temp = new EnergyTypeResourceComplexIndexMapDTO();
            temp.setBusinessType(item);

            List<Double> coefficient = findParamsByBusinessType(energyDataItems, Integer.valueOf(item.getDescription()));
            temp.setCoalCoefficient(coefficient.get(0));
            temp.setCo2Coefficient(coefficient.get(1));

            for (ComplexIndex c : roleObjectsTotalComplexIndex) {
                if (c.getBusinessTypeId().equals(item.getBusinessTypeId())) {
                    temp.setComplexIndex(c);
                    break;
                }
            }
            res.add(temp);
        }
        return res;
    }


    /**
     * @param startTime: 开始时间
     * @param endTime:   结束时间
     * @param objectId:  objectId
     * @Description:
     * @Author: Li.Qupan.Pan
     * @Date: 2022/8/18 17:31
     * @return: com.siteweb.energy.dto.TotalComplexIndexDTO
     **/
    @Override
    public List<EnergyComparativeAnalysisDTO> findObjectEnergyTotal(Date startTime, Date endTime, Integer objectId, Integer userId, String timeType) {
        List<EnergyComparativeAnalysisDTO> res = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTOByUserId(objectId, true, userId);

        for (EnergyTypeResourceComplexIndexMapDTO item : complexIndexMaps) {
            Double value = 0.0;

            String unit = "";
            value += findSumValueOfHistoryComplexIndexByIdAndDuration(startTime, endTime, item.getComplexIndex(), timeType);

            if (ObjectUtil.isNotNull(item.getComplexIndex())) {
                unit = item.getComplexIndex().getUnit();
            } else {
                unit = "";
            }
            Double planValue = findPlanValueByEnergyTypeIdAndTime(startTime, endTime, item.getBusinessType().getBusinessTypeId(), objectId);
            EnergyComparativeAnalysisDTO temp = new EnergyComparativeAnalysisDTO();
            temp.setBusinessTypeId(item.getBusinessType().getBusinessTypeId());
            temp.setBusinessTypeName(item.getBusinessType().getBusinessTypeName());
            temp.setValue(Double.valueOf(String.format("%.2f", value)));

            temp.setUnit(unit);
            temp.setPlanValue(planValue);
            temp.setPlanValueCoal(Double.valueOf(String.format("%.2f", planValue * item.getCoalCoefficient())));
            temp.setPlanValueCo2(Double.valueOf(String.format("%.2f", planValue * item.getCo2Coefficient())));
            temp.setValueCo2(Double.valueOf(String.format("%.2f", value * item.getCo2Coefficient())));
            temp.setValueCoal(Double.valueOf(String.format("%.2f", value * item.getCoalCoefficient())));

            res.add(temp);
        }

        return res;
    }


}
