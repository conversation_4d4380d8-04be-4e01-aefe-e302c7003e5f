package com.siteweb.energy.service.impl;

import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyGrowthService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import static com.siteweb.common.util.DateUtil.dateToString;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnergyGrowthServiceImpl implements EnergyGrowthService {
    private final Logger log = LoggerFactory.getLogger(EnergyGrowthServiceImpl.class);

    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;

    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    @Override
    public String getHistoryComplexIndexSum(Date startTime,Date endTime, long complexIndexId,String timeType, boolean isFee) {
        if (isFee){
            return getHistoryComplexIndexFeeSum(startTime, endTime, complexIndexId);
        }

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        String sql = "select sum(IndexValue) as result  from EnergyHisMonthData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId ";

        if (timeType.equals("m") || timeType.equals("o")) {
            sql = "select sum(IndexValue) as result  from EnergyHisDayData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId ";
        }
        if (timeType.equals("d")) {
            sql = "select sum(IndexValue) as result  from EnergyHisHourData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId ";
        }
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                .forDatabase(databaseEnergy)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .bind("complexIndexId", Long.toString(complexIndexId))
                .create();
        query = influxDB.query(queryBuilder);
        if (timeType.equals("m") || timeType.equals("o")){
            List<EnergyHisDayDataResult> result = new ArrayList<>();
            try {
                if (query != null) {
                    result = resultMapper.toPOJO(query,EnergyHisDayDataResult.class);
                }
                return result.isEmpty() ? null : result.get(0).getResult();
            }catch (Exception e){
                log.error("EnergyGrowthServiceImpl-getHistoryComplexIndexSum timeType={}, error {}",timeType, e.getMessage());
                return null;
            }
        } else if (timeType.equals("d")) {
            List<EnergyHisHourDataResult> result = new ArrayList<>();
            try {
                if (query != null) {
                    result = resultMapper.toPOJO(query,EnergyHisHourDataResult.class);
                }
                return result.isEmpty() ? null : result.get(0).getResult();
            }catch (Exception e){
                log.error("EnergyGrowthServiceImpl-getHistoryComplexIndexSum timeType={}, error {}",timeType, e.getMessage());
                return null;
            }
        }else {
            List<EnergyHisMonthDataResult> result = new ArrayList<>();
            try {
                if (query != null) {
                    result = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                }
                return result.isEmpty() ? null : result.get(0).getResult();
            } catch (Exception e) {
                log.error("EnergyGrowthServiceImpl-getHistoryComplexIndexSum timeType={}, error {}", timeType, e.getMessage());
                return null;
            }
        }
    }

    public String getHistoryComplexIndexFeeSum(Date startTime,Date endTime, long complexIndexId) {
        List<HistoryComplexIndexFeeSum> result = new ArrayList<>();

        try {
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("select sum(IndexFeeValue) as sum  from EnergyHisFeeData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId ")
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .bind("complexIndexId", Long.toString(complexIndexId))
                    .create();
            query = influxDB.query(queryBuilder);
            if (query != null) {
                result = resultMapper.toPOJO(query,HistoryComplexIndexFeeSum.class);
            }
            return result.isEmpty() ? null : result.get(0).getSum();
        }catch (Exception e){
            log.error("EnergyGrowthServiceImpl-getEnergyHisFeeDataSum error {}", e.getMessage());
            return null;
        }
    }

    // 历史数据用电量、同比、环比
    @Override
    public EnergyGrowth CalculationYOY(int businessTypeId,Date startTime,Date endTime,String resourceStructureId,String timeType){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.YEAR,-1);
        Date historystarttime = calendar.getTime();
        calendar.setTime(endTime);
        calendar.add(Calendar.YEAR,-1);
        Date historyendtime = calendar.getTime();

        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);

        try {
            BusinessDefinitionMap thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getBusinessTypeId().equals(businessTypeId)
                    && i.getComplexIndexDefinitionTypeId().equals(1)).findFirst().orElse(null);
            if (thisMap == null) return null;
            int energyIndexDefinition = thisMap.getComplexIndexDefinitionId();
            EnergyGrowth energyGrowth = new EnergyGrowth();
            List<ComplexIndex> complexIndices = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getObjectId().equals(rsId) && i.getComplexIndexDefinitionId().equals(energyIndexDefinition)
            ).collect(Collectors.toList());
            //complexIndexService.findByDefinitionIdAndObjectId(energyIndexDefinition,Long.valueOf(rsId));
            ComplexIndex comp = complexIndices.stream().filter(i->i.getObjectTypeId().equals(rsTypeId)).findFirst().orElse(null);
            if(comp == null) return energyGrowth;

            if (complexIndices.size() > 0){
                long complexIndexid = comp.getComplexIndexId();
                String historyEnergysum = getHistoryComplexIndexSum(historystarttime,historyendtime,complexIndexid,timeType,false);
                String energusum = getHistoryComplexIndexSum(startTime,endTime,complexIndexid,timeType,false);
                if (energusum == null)
                    energusum = "0";

                energyGrowth.ComplexIndexId = complexIndexid;
                energyGrowth.Electricity = energusum;
                energyGrowth.ComplexIndexName = comp.getComplexIndexName();
                energyGrowth.Unit = comp.getUnit();

                if (historyEnergysum == null) {
                    energyGrowth.ElectricityYoY = "0";
                    if (Double.parseDouble(energusum) == 0){
                        energyGrowth.YoY = 0d;
                    } else {
                        energyGrowth.YoY = 100d;
                    }
                } else {
                    energyGrowth.ElectricityYoY = historyEnergysum;
                    energyGrowth.YoY = Math.abs ((Double.parseDouble(energusum)- Double.parseDouble(historyEnergysum))/Double.parseDouble(historyEnergysum))*100;
                }
                //计算环比时间
               List<Date> dates = getQoQDateOrYoYDate(startTime, endTime,"QoQ",timeType);
                Date historymomstarttime =dates.get(0);
                Date historymomendtime = dates.get(1);
                String historyMoMEnergysum = getHistoryComplexIndexSum(historymomstarttime, historymomendtime,complexIndexid,timeType,false);
                if (historyMoMEnergysum == null) {
                    energyGrowth.ElectricityMoM = "0";
                    if (Double.parseDouble(energusum) == 0){
                        energyGrowth.MoM = 0d;
                    } else {
                        energyGrowth.MoM = 100d;
                    }
                } else {
                    energyGrowth.ElectricityMoM = historyMoMEnergysum;
                    energyGrowth.MoM = Math.abs(
                            (Double.parseDouble(energusum)-Double.parseDouble(historyMoMEnergysum)) /Double.parseDouble(historyMoMEnergysum)*100
                    );
                }
            }
            return energyGrowth;
        }catch (Exception e){
            log.error("EnergyGrowthServiceImpl-CalculationYOY error {}", e.getMessage());
            return null;
        }
    }
    private  List<Date> getQoQDateOrYoYDate(Date startTime, Date endTime, String type,String timeType) {
        List<Date> res = new ArrayList<>();
        Date sTime = new Date();
        Date eTime = new Date();
        Calendar calendar = Calendar.getInstance();
        if (type.equals("YoY")) {
            calendar.setTime(startTime);
            calendar.add(Calendar.YEAR, -1);
            sTime = calendar.getTime();
            calendar.setTime(endTime);
            calendar.add(Calendar.YEAR, -1);
            eTime = calendar.getTime();
        }
        if (type.equals("QoQ")) {
            switch (timeType) {
                case "m":
                    // 月环比时间
                    calendar.setTime(startTime);
                    calendar.add(Calendar.MONTH, -1);
                    sTime = calendar.getTime();


                    calendar.add(Calendar.MONTH, 1);
                    calendar.add(Calendar.SECOND, -1);
                    eTime = calendar.getTime();
                    break;
                case "y":
                    calendar.setTime(startTime);
                    calendar.add(Calendar.YEAR, -1);
                    sTime = calendar.getTime();

                    calendar.setTime(endTime);
                    calendar.add(Calendar.YEAR, -1);
                    eTime = calendar.getTime();
                    break;
                case "d":
                    calendar.setTime(startTime);
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    sTime = calendar.getTime();

                    calendar.setTime(endTime);
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    eTime = calendar.getTime();

                    break;
                case "o":
                    // 转换为LocalDate
                    LocalDate startLocalDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate endLocalDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                    // 计算两个日期之间的天数
                    Long betweenDays = ChronoUnit.DAYS.between(startLocalDate, endLocalDate)+1;

                    calendar.setTime(startTime);
                    calendar.add(Calendar.DAY_OF_MONTH, 0-betweenDays.intValue());
                    sTime = calendar.getTime();

                    calendar.setTime(endTime);
                    calendar.add(Calendar.DAY_OF_MONTH, 0-betweenDays.intValue());
                    eTime = calendar.getTime();
                    break;
            }
        }
        res.add(sTime);
        res.add(eTime);
        return res;
    }

    // 所选节点用电类型饼图数据
    @Override
    public List<EnergyPowerType> findEnergyTypeByStructure(int businessTypeId,Date startTime,Date endTime,String resourceStructureId,String timeType,boolean isFee ){
        List<EnergyPowerType> energyPowerTypes = new ArrayList<>();
        EnergyPowerType energypowertype = new EnergyPowerType();

        try {
            Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
            Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);
            ResourceStructure thisStructure = resourceStructureManager.getResourceStructureById(rsId);

            // 获取此节点的所有指标
            List<ComplexIndex> lstComplexIndexOfStructure = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getObjectId().equals(rsId) && i.getObjectTypeId().equals(rsTypeId)
            ).collect(Collectors.toList());

            List<ComplexIndex> filterComplexIndex = new ArrayList<>();
            List<BusinessDefinitionMap> thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                    i->i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(3)).collect(Collectors.toList());
            if (thisMap == null) return null;
            List<Integer> lstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());

            List<Integer> finalLstDefinition = lstDefinition;
            filterComplexIndex = lstComplexIndexOfStructure.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());

            String complexIndexSum = null;
            for(ComplexIndex com:filterComplexIndex){
                energypowertype = new EnergyPowerType();
                energypowertype.ComplexIndexName = com.getComplexIndexName();
                complexIndexSum = getHistoryComplexIndexSum(startTime,endTime,com.getComplexIndexId(),timeType,isFee);
                if(complexIndexSum == null){
                    energypowertype.ComplexIndexValue = 0.0;
                } else {
                    energypowertype.ComplexIndexValue = Double.parseDouble(complexIndexSum);
                }
                energyPowerTypes.add(energypowertype);
            }
            return energyPowerTypes;
        }catch (Exception e){
            log.error("EnergyGrowthServiceImpl-findComplexIndexByGlobalSource error {}", e.getMessage());
            return null;
        }
    }

}
