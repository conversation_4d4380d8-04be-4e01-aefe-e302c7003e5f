package com.siteweb.energy.service.impl;

import com.siteweb.energy.dto.EnergyHisFeeDataSum;
import com.siteweb.energy.service.HistoryComplexIndexFeeService;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
public class HistoryComplexIndexFeeServiceImpl implements HistoryComplexIndexFeeService {
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;
    /***
     *  根据时间段获取指标的历史数据
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param complexIndexId 指标Id
     * @param pageable
     * @return 历史指标列表
     */
    public List<EnergyHisFeeDataSum> findHistoryComplexIndeFeeByIdAndDuration(Date startTime, Date endTime, Integer complexIndexId, Pageable pageable, String timeType) {
        List<EnergyHisFeeDataSum> result = new ArrayList<>();
        Query queryBuilder = null;
        if (pageable != null) {
            queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("select *  from EnergyHisFeeData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId order by time asc limit $lit offset $offs ")
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .bind("complexIndexId", Long.toString(complexIndexId))
                    .bind("lit",pageable.getPageSize())
                    .bind("offs",pageable.getPageNumber() * pageable.getPageSize())
                    .create();
        }
        else{
            String sql = "select sum(IndexFeeValue) as result  from EnergyHisFeeData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId ";
            if(timeType.equals("d"))
                sql = "select sum(IndexFeeValue) as result  from EnergyHisFeeData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId group by time(1h) ";
            else if(timeType.equals("m")||timeType.equals("o"))
                sql = "select sum(IndexFeeValue) as result  from EnergyHisFeeData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId group by time(1d) ";

            queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .bind("complexIndexId", Long.toString(complexIndexId))
                    .create();
        }

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        query =  influxDB.query(queryBuilder);

        if (query != null) {
            result = resultMapper.toPOJO(query, EnergyHisFeeDataSum.class);
        }
        return result;
    }


}
