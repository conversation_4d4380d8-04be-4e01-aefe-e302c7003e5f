package com.siteweb.energy.service;

import com.siteweb.energy.dto.*;

import java.util.Date;
import java.util.List;

public interface EnergyStandardApiService {

    List<EnergySdkResourceComplexIndexDTO> getResourceStructureComplexIndexIds();

    EnergySdkTotalResult getTotalEnergyUse(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkTotalTrendsResult getTotalEnergyUseTrends(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkBasicResult getEfficiencyEnergyUse(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkBasicTrendsResult getEfficiencyEnergyUseTrends(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkBasicResult getOptionsEnergyUse(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkBasicTrendsResult getOptionsEnergyUseTrends(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkBasicResult getEnergyUseByComplexIndexDefinitionTypeId(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer complexIndexDefinitionTypeId);

    EnergySdkBasicTrendsResult getEnergyUseTrendByComplexIndexDefinitionTypeId(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer complexIndexDefinitionTypeId);

    List<EnergySdkBasicResult> getOptionsEnergyUseChild(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    List<EnergySdkBasicTrendsResult> getOptionsEnergyUseTrendsChild(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime);

    List<EnergySdkComplexIndexResult> getComplexIndexsValue(Integer[] complexIndexIds, String timeType, Date startTime, Date endTime);

    List<EnergySdkComplexIndexTrendsResult> getComplexIndexsValueTrends(Integer[] complexIndexIds, String timeType, Date startTime, Date endTime);

    EnergySdkTotalDefinitionYOYResult calculationEnergyYOY(Integer objectId, Integer objectTypeId, int businessTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkGreenTotalResultDTO getGreenEnergyTotal(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkCarbonTotalResultDTO getTotalCarbonEmissions(int businessTypeId, Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkCarbonEmissionProgressResultDTO getCarbonEmissionsProgress(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkCarbonEmissionTrendResultDTO getCarbonTrend(int businessTypeId, Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkGreenProportionResultDTO getGreenEnergyProportion(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime);

    EnergySdkGreenPowerTrendResultDTO getGreenEnergyTrend(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime);

    List<EnergySdkEnergyTypeDTO> getEnergyTypeList();
    EnergyCoefficientDTO getCoefficient(Integer businessTypeId);

    Object getCarbonIntensityRatio(Integer objectId, Integer objectTypeId, String timeType, Date startTime);

    Object getEveryCarbonIntensityRatio(Integer objectId, Integer objectTypeId, String timeType, Date startTime);

    //*******************************************权限××××××××××××××××××××××××××××××××××××××××××××××××××××//

    UseEnergyAndCarbonTotal getTotalEnergyUseOfRole(Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer userId);
    List<UseEnergyAndCarbon> getChildTotalEnergyUseOfRole(Integer childObjectTypeId,Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer userId);

    Object findHistoryComplexIndexsByIdAndDuration(List<Integer> complexIndexIds, String tableType,Date startTime, Date endTime);
}
