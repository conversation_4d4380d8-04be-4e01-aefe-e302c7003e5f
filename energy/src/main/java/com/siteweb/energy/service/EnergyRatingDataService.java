package com.siteweb.energy.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.energy.dto.EnergyRatingDataJobParams;
import com.siteweb.energy.dto.EnergyRatingDataParams;
import com.siteweb.energy.entity.EnergyRatingData;

import java.util.List;


public interface EnergyRatingDataService {
    Page getEnergyRatingDataPage(EnergyRatingDataParams energyRatingDataParams);

    Integer scheduleGenerateRatingData(EnergyRatingDataJobParams params);

    Boolean deleteEnergyRatingData(List<Integer> ratingDataIds);

    void saveEnergyRatingData(EnergyRatingData energyRatingData);

    List<EnergyRatingData> getEnergyRatingDataList(EnergyRatingDataParams energyRatingDataParams);

    Boolean clearAllEnergyRatingData(Integer ratingConfigId);

    EnergyRatingData getRatingDataRealTime(Integer ratingConfigId);

    void unScheduleGenerateRatingData(Integer id);

    /**
     * 检查评级配置是否运行，并同步数据库和内存中的任务
     */
    void checkRatingConfigIsRun();
}
