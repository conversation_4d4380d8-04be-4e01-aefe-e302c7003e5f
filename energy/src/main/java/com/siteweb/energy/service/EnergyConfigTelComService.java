package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyConfigDynamicEnergyTotalAndRatioRes;
import com.siteweb.energy.dto.EnergyConfigTelComResultDTO;
import com.siteweb.energy.dto.EnergyConfigDynamicEnergyAndRatioRes;
import com.siteweb.energy.dto.EnergyUEMaxMinAvgDTO;

import java.util.Date;
import java.util.List;

public interface EnergyConfigTelComService {
    EnergyConfigTelComResultDTO getTotalElectricity(String timeType, Date startTime, Date endTime, Integer userId, String queryType);

    Object getEnergyAndCarbonTrend(String timeType, Date startTime, Date endTime, Integer userId,String resultType);

    Object getRoomEnergyAndCarbon(String timeType, Date startTime, Date endTime, Integer resourceStructureId, Integer userId,String queryType,String resultType);


    // PUE  获取UE最大 最小 平均值
    EnergyUEMaxMinAvgDTO findMaxMInAvgUE(Date startTime, Date endTime, String timeType, Integer userId);

    // PUE 求某个具体层级节点的PUE
    Double GetPUEOFResourceStructure(Integer resourceStructureId, Date startTime, Date endTime, String timeType);

    //PUE趋势
    List<EnergyUEMaxMinAvgDTO> findMaxMInAvgUETrend(Date startTime, Date endTime, String timeType, Integer userId);

    Object getNextLevelRank(String timeType, Date startTime, Date endTime, Integer userId, String queryType);

    Object electCategoryProportion(String timeType, Date startTime, Date endTime, Integer userId,Integer businessId);
    List<Date> getQoQDateOrYoYDate(Date startTime, Date endTime, String type,String timeType);

    EnergyConfigDynamicEnergyTotalAndRatioRes totalEnergyAndRatioUseOfRole(String timeType, Date startTime, Date endTime, Integer userId, String queryType);
    List<EnergyConfigDynamicEnergyAndRatioRes> everyEnergyAndRatioUseOfRole(String timeType, Date startTime, Date endTime, Integer userId, String queryType);

    EnergyConfigDynamicEnergyTotalAndRatioRes greenCarbonOfRole(String timeType, Date startTime, Date endTime, Integer userId);

    Object dynamicGetRoomEnergyAndCarbon(String timeType, Date startTime, Date endTime, Integer resourceStructureId,  Integer userId);

    Object dynamicGetEnergyAndCarbonTrend(String timeType, Date startTime, Date endTime, Integer userId, String queryType);

    Object dynamicGetNextLevelRank(String timeType, Date startTime, Date endTime, Integer userId, String queryType);

    Object greenOrOtherRatio(String timeType, Date startTime, Date endTime, Integer userId);
}
