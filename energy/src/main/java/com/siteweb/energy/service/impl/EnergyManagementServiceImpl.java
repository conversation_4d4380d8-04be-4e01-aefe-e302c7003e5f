package com.siteweb.energy.service.impl;

import com.siteweb.energy.dto.EnergyManagementReportPar;
import com.siteweb.energy.dto.EnergyManagementReportResult;
import com.siteweb.energy.dto.StructureOfComplexIndexValue;
import com.siteweb.energy.entity.EnergyManagement;
import com.siteweb.energy.entity.EnergyManagementMap;
import com.siteweb.energy.mapper.EnergyManagementMapMapper;
import com.siteweb.energy.mapper.EnergyManagementMapper;
import com.siteweb.energy.service.EnergyManagementMapService;
import com.siteweb.energy.service.EnergyManagementService;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.StringUtils.getIntegerListByString;

@Service
@Slf4j
public class EnergyManagementServiceImpl implements EnergyManagementService {

    @Autowired
    EnergyManagementMapper mapper;
    @Autowired
    EnergyManagementMapMapper mapMapper;
    @Autowired
    EnergyManagementMapService mapService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyOverViewService eovService;

    @Override
    public List<EnergyManagement> findAll() {
        return mapper.selectList(null);
    }

    @Override
    public EnergyManagement findById(Integer energyId) {
        return mapper.selectById(energyId);
    }

    @Override
    public void deleteById(Integer energyId) {
        mapper.deleteById(energyId);
        mapMapper.deleteByEnergyId(energyId);
    }

    @Override
    public EnergyManagement saveInfo(EnergyManagement energyInfo) {
        boolean isAdd = false;
        if (energyInfo.getEnergyId() == null) isAdd = true;

        EnergyManagement newEnergyInfo = new EnergyManagement();
        newEnergyInfo.setEnergyName(energyInfo.getEnergyName());
        newEnergyInfo.setContact(energyInfo.getContact());
        newEnergyInfo.setEndTime(energyInfo.getEndTime());
        newEnergyInfo.setNotes(energyInfo.getNotes());
        newEnergyInfo.setStartTime(energyInfo.getStartTime());
        newEnergyInfo.setOperator(energyInfo.getOperator());
        newEnergyInfo.setUnit(energyInfo.getUnit());

        if (isAdd)
            mapper.insert(newEnergyInfo);
        else {
            newEnergyInfo.setEnergyId(energyInfo.getEnergyId());
            mapper.updateById(newEnergyInfo);
        }
        return mapper.selectById(newEnergyInfo.getEnergyId());
    }

    @Override
    public EnergyManagement updateInfo(EnergyManagement energyInfo) {
        return saveInfo(energyInfo);
    }

    /**
     * 1. 节能措施前后站点用电量报表
     * */
    @Override
    public List<EnergyManagementReportResult> getEnergyReportBeforeAfter(EnergyManagementReportPar reportPar) {

        List<Integer> postIds = getIntegerListByString(reportPar.getResourceStructureIds());
        if (postIds.size() == 0) return null;

        List<EnergyManagementReportResult> result = new ArrayList<>();
        EnergyManagementReportResult oneResult = new EnergyManagementReportResult();
        List<ResourceStructure> maps = resourceStructureManager.getAll().stream().filter(i->postIds.contains(i.getResourceStructureId())).collect(Collectors.toList());

        //删除已不再配置中的ID
        List<EnergyManagementMap> allEnergyManagementMap = mapService.findByEnergyId(Integer.parseInt(reportPar.getEnergyIds()));
        maps.removeIf(m->allEnergyManagementMap.stream().filter(item->item.getResourceStructureId().equals(m.getResourceStructureId())).findFirst().orElse(null) == null);

        List<StructureOfComplexIndexValue> lstBefore = eovService.GetConsumeOfResourceStructure(2,maps,reportPar.getStartTime(),reportPar.getEndTime(),"d",false);
        List<StructureOfComplexIndexValue> lstAfter = eovService.GetConsumeOfResourceStructure(2,maps,reportPar.getStartTime2(),reportPar.getEndTime2(),"d",false);

        Integer energyId = getIntegerListByString(reportPar.getEnergyIds()).get(0);

        EnergyManagement thisEnergy = mapper.selectById(energyId);
        if (thisEnergy == null) return result;
        for(StructureOfComplexIndexValue one : lstBefore)
        {
            oneResult = new EnergyManagementReportResult();
            oneResult.setEnergyIds(energyId);
            oneResult.setEnergyName(thisEnergy.getEnergyName());
            oneResult.setResourceStructureId(one.getResourceStructureId());
            oneResult.setResourceStructureName(one.getResourceStructureName());
            oneResult.setTimeSpan1((new SimpleDateFormat("yyyy-MM-dd")).format(reportPar.getStartTime())
                    +" -- "+(new SimpleDateFormat("yyyy-MM-dd")).format(reportPar.getEndTime()));
            oneResult.setSumValue(one.getSumValue());
            oneResult.setTimeSpan2((new SimpleDateFormat("yyyy-MM-dd")).format(reportPar.getStartTime2())
                    +" -- "+(new SimpleDateFormat("yyyy-MM-dd")).format(reportPar.getEndTime2()));
            Double sumValue = 0d;
            List<StructureOfComplexIndexValue> structureOfComplexIndexValue = lstAfter.stream().
                    filter(e->e.getResourceStructureId().equals(one.getResourceStructureId())).collect(Collectors.toList());
            if (structureOfComplexIndexValue.size() > 0)  sumValue = structureOfComplexIndexValue.get(0).getSumValue();
            oneResult.setSumValue2(sumValue );

            oneResult.setSumValueDiff(one.getSumValue() - sumValue);
            oneResult.setLevelOfPath(one.getLevelOfPath());

            result.add(oneResult);
        }
        return result.stream().sorted(Comparator.comparing(EnergyManagementReportResult::getLevelOfPath)).collect(Collectors.toList());
    }

    /**
     * 2. 节能措施站点与否用电量报表
     * */
    @Override
    public EnergyManagementReportResult getEnergyReportOnOrNot(EnergyManagementReportPar reportPar) {
        List<EnergyManagementReportResult>  lstOn = getEnergyReportOfStructures(reportPar.getEnergyIds(), reportPar.getResourceStructureIds(),
                reportPar.getStartTime(),reportPar.getEndTime());
        List<EnergyManagementReportResult>  lstOff = getEnergyReportOfStructures(reportPar.getEnergyIds(), reportPar.getResourceStructure2Ids(),
                reportPar.getStartTime(),reportPar.getEndTime());
        EnergyManagementReportResult result = new EnergyManagementReportResult();

        EnergyManagement thisEnergy = mapper.selectById(getIntegerListByString(reportPar.getEnergyIds()).get(0));
        if (thisEnergy == null) return result;

        result.setEnergyIds(thisEnergy.getEnergyId());
        result.setEnergyName(thisEnergy.getEnergyName());

        result.setTimeSpan1((new SimpleDateFormat("yyyy-MM-dd")).format(reportPar.getStartTime())
                +" -- "+(new SimpleDateFormat("yyyy-MM-dd")).format(reportPar.getEndTime()));

        double avg1 = lstOn.stream().mapToDouble(EnergyManagementReportResult::getSumValue).average().orElse(0f);
        result.setAvgValue((double) Math.round(avg1 * 100) / 100);
        double avg2 = lstOff.stream().mapToDouble(EnergyManagementReportResult::getSumValue).average().orElse(0f);
        result.setAvgValue2((double) Math.round(avg2 * 100) / 100);

        if (avg2 == 0){
            result.setAvgRate("0%");
        }
        else{
            //(@avgElec2-@avgElec1)*110)/(@avgElec2*1.1)
            double avgRate = ((avg2-avg1)*110) /(avg2*1.1);
            result.setAvgRate(new DecimalFormat("0.00").format(avgRate)+"%");
        }
        return result;
    }

    @Override
    public List<EnergyManagementReportResult> getEnergyReportOn(EnergyManagementReportPar reportPar) {
        //根据前端传过来的站点ID获取ResourceStructure
        List<Integer> postIds = getIntegerListByString(reportPar.getResourceStructureIds());
        List<ResourceStructure> maps = resourceStructureManager.getAll().stream().filter(i->postIds.contains(i.getResourceStructureId())).collect(Collectors.toList());

        //删除已不再配置中的ID
        List<EnergyManagementMap> allEnergyManagementMap = mapService.findByEnergyId(Integer.parseInt(reportPar.getEnergyIds()));
        maps.removeIf(m->allEnergyManagementMap.stream().filter(item->item.getResourceStructureId().equals(m.getResourceStructureId())).findFirst().orElse(null) == null);
        String onIds = maps.stream().map(ResourceStructure::getResourceStructureId).map(String::valueOf).collect(Collectors.joining(","));

        List<EnergyManagementReportResult>  lstOn = getEnergyReportOfStructures(reportPar.getEnergyIds(), onIds,
                reportPar.getStartTime(),reportPar.getEndTime());
        return lstOn;
    }

    @Override
    public List<EnergyManagementReportResult> getEnergyReportNot(EnergyManagementReportPar reportPar) {
        //根据前端传过来的站点ID获取ResourceStructure
        List<Integer> postIds = getIntegerListByString(reportPar.getResourceStructure2Ids());
        List<ResourceStructure> maps = resourceStructureManager.getAll().stream().filter(i->postIds.contains(i.getResourceStructureId())).collect(Collectors.toList());

        //删除已再配置中的ID
        List<EnergyManagementMap> allEnergyManagementMap = mapService.findByEnergyId(Integer.parseInt(reportPar.getEnergyIds()));
        maps.removeIf(m->allEnergyManagementMap.stream().filter(item->item.getResourceStructureId().equals(m.getResourceStructureId())).findFirst().orElse(null) != null);
        String offIds = maps.stream().map(ResourceStructure::getResourceStructureId).map(String::valueOf).collect(Collectors.joining(","));

        List<EnergyManagementReportResult>  lstOff = getEnergyReportOfStructures(reportPar.getEnergyIds(), offIds,
                reportPar.getStartTime(),reportPar.getEndTime());
        return lstOff;
    }

    public List<EnergyManagementReportResult> getEnergyReportOfStructures(String energyIds,String structureIds,Date startTime, Date endTime) {
        List<EnergyManagementReportResult> result = new ArrayList<>();
        EnergyManagementReportResult oneResult;

        if (structureIds.equals("-1"))
        {
            List<EnergyManagementMap> energyMaps = mapService.findByEnergyId(getIntegerListByString(energyIds).get(0)).stream().
                    filter(item->item.getIsMarker().equals(1)).collect(Collectors.toList());
            if (energyMaps.size() == 0) return result;
            structureIds = energyMaps.stream().map(EnergyManagementMap::getResourceStructureId)
                    .map(String::valueOf).collect(Collectors.joining(","));
        }
        List<Integer> postIds = getIntegerListByString(structureIds);
        List<ResourceStructure> maps = resourceStructureManager.getAll().stream().filter(i->postIds.contains(i.getResourceStructureId())).collect(Collectors.toList());

        List<StructureOfComplexIndexValue> lastValue = eovService.GetConsumeOfResourceStructure(2,maps,startTime,endTime,"d",false);

        EnergyManagement thisEnergy = mapper.selectById(getIntegerListByString(energyIds).get(0));
        if (thisEnergy == null) return result;

        for(StructureOfComplexIndexValue one : lastValue)
        {
            oneResult = new EnergyManagementReportResult();
            oneResult.setEnergyIds(thisEnergy.getEnergyId());
            oneResult.setEnergyName(thisEnergy.getEnergyName());
            oneResult.setResourceStructureId(one.getResourceStructureId());
            oneResult.setResourceStructureName(one.getResourceStructureName());
            oneResult.setLevelOfPath(one.getLevelOfPath());
            oneResult.setSumValue(one.getSumValue());
            oneResult.setTimeSpan1((new SimpleDateFormat("yyyy-MM-dd")).format(startTime)
                    +" -- "+(new SimpleDateFormat("yyyy-MM-dd")).format(endTime));
            //oneResult.setSumValue2(one.getSumValue());
            result.add(oneResult);
        }
        return result.stream().sorted(Comparator.comparing(EnergyManagementReportResult::getLevelOfPath)).collect(Collectors.toList());
    }

    @Override
    public List<EnergyManagementReportResult> getEnergyReportEfficiency(EnergyManagementReportPar reportPar) {
        List<EnergyManagementReportResult>  result = new ArrayList<>();
        Calendar calendar = new GregorianCalendar();
        for (Integer energyId : getIntegerListByString(reportPar.getEnergyIds())){
            EnergyManagement thisEnergy = mapper.selectById(energyId);
            if (thisEnergy == null) continue;
            List<EnergyManagementReportResult> thisEnergyResultBefore;
            List<EnergyManagementReportResult> thisEnergyResultEnd;
            calendar.setTime(thisEnergy.getStartTime());
            calendar.add(calendar.DATE,-reportPar.getDays()-1);
            Date startTime1 =calendar.getTime();

            calendar.setTime(thisEnergy.getStartTime());
            calendar.add(calendar.DATE,-1);
            Date endTime1 =calendar.getTime();

            thisEnergyResultBefore = getEnergyReportOfStructures(energyId.toString(),"-1",startTime1,endTime1);

            calendar.setTime(thisEnergy.getEndTime());
            calendar.add(calendar.DATE,1);
            Date startTime2 =calendar.getTime();

            calendar.setTime(thisEnergy.getEndTime());
            calendar.add(calendar.DATE,reportPar.getDays()+1);
            Date endTime2 =calendar.getTime();
            thisEnergyResultEnd= getEnergyReportOfStructures(energyId.toString(),"-1",startTime2,endTime2);

            EnergyManagementReportResult thisResult = new EnergyManagementReportResult();
            thisResult.setEnergyIds(energyId);
            thisResult.setEnergyName(thisEnergy.getEnergyName());
            thisResult.setTimeSpan1((new SimpleDateFormat("yyyy-MM-dd")).format(startTime1)
                    +" -- "+(new SimpleDateFormat("yyyy-MM-dd")).format(endTime1));
            thisResult.setTimeSpan2((new SimpleDateFormat("yyyy-MM-dd")).format(startTime2)
                    +" -- "+(new SimpleDateFormat("yyyy-MM-dd")).format(endTime2));

            double avgBefore = thisEnergyResultBefore.stream().mapToDouble(EnergyManagementReportResult::getSumValue).average().orElse(0f);
            double avgEnd = thisEnergyResultEnd.stream().mapToDouble(EnergyManagementReportResult::getSumValue).average().orElse(0f);
            thisResult.setAvgValue(avgBefore);
            thisResult.setAvgValue2(avgEnd);
            thisResult.setAvgValueDiff(avgBefore-avgEnd);

            if (avgEnd == 0){
                thisResult.setAvgRateValue(0d);
                thisResult.setAvgRate("0%");
            }
            else{
                double avgRate = ((avgBefore-avgEnd)*110) /(avgEnd*1.1);
                thisResult.setAvgRate(new DecimalFormat("0.00").format(avgRate)+"%");
                thisResult.setAvgRateValue(avgRate);
            }
            result.add(thisResult);
        }

        result = result.stream().sorted(Comparator.comparing(EnergyManagementReportResult::getAvgRateValue,Comparator.reverseOrder())).collect(Collectors.toList());
        return result;
    }

}
