package com.siteweb.energy.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyDataItemTimeliness;
import com.siteweb.energy.mapper.EnergyDataConfigItemMapper;
import com.siteweb.energy.mapper.EnergyDataItemTimelinessMapper;
import com.siteweb.energy.service.EnergyDataConfigItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class EnergyDataConfigItemServiceImpl implements EnergyDataConfigItemService {

    @Autowired
    private EnergyDataConfigItemMapper energyDataConfigItemMapper;

    @Autowired
    private EnergyDataItemTimelinessMapper energyDataItemTimelinessMapper;

    @Override
    public List<EnergyDataItem> getAllByEntryId(Integer entryId) {
        return energyDataConfigItemMapper.getAllByEntryId(entryId);
    }

    @Override
    public EnergyDataItem getDataItemById(Integer entryId, Integer itemId) {
        return energyDataConfigItemMapper.getDataItemById(entryId, itemId);
    }

    public EnergyDataItem createEnergyDataItem(EnergyDataItem energyDataItem) {
        EnergyDataItem energyDataItemData = energyDataConfigItemMapper.selectById(energyDataItem.getEntryItemId());
        if (energyDataItemData != null) {
            return null;
        }
        energyDataConfigItemMapper.insert(energyDataItem);
        return energyDataItem;
    }

    public EnergyDataItem updateEnergyDataItem(EnergyDataItem energyDataItem) {
        energyDataConfigItemMapper.updateById(energyDataItem);
        return energyDataItem;
    }

    @Transactional
    public void deleteEntryItemId(Integer id) {
        energyDataConfigItemMapper.deleteById(id);
    }

    @Override
    public Integer createEnergyDataItemTimeliness(EnergyDataItemTimeliness energyDataItemTimeliness) {

        try {
            energyDataItemTimelinessMapper.insert(energyDataItemTimeliness);
        } catch (Exception e) {
            if (e instanceof DuplicateKeyException) {
                return -2;
            }
        }
        return 0;
    }

    public List<EnergyDataItem> getAllByEntryIdTimeliness(Integer entryId) {
        Date now = new Date();
        List<EnergyDataItem> energyDataItems = energyDataConfigItemMapper.selectList(new QueryWrapper<EnergyDataItem>().eq("EntryId", entryId));
        List<EnergyDataItemTimeliness> energyDataItemTimeliness = energyDataItemTimelinessMapper.selectList(null);
        for (EnergyDataItem energyDataItem : energyDataItems) {
            List<EnergyDataItemTimeliness> isHaveTimeliness = energyDataItemTimeliness.stream().filter(i -> i.getEntryItemId().equals(energyDataItem.getEntryItemId()) && i.getStartTime().before(now)).toList();
            if (isHaveTimeliness.isEmpty()) {
                continue;
            } else {
                List<EnergyDataItemTimeliness> endTimeTempList = new ArrayList<>(isHaveTimeliness);
                endTimeTempList.sort((t1, t2) -> (int) ((t2.getStartTime().getTime() - t1.getStartTime().getTime()) / 1000));
                EnergyDataItemTimeliness temp = endTimeTempList.get(0);
                energyDataItem.setItemValue(temp.getItemValue());
                energyDataItem.setExtendField1(temp.getExtendField1());
                energyDataItem.setExtendField2(temp.getExtendField2());
                energyDataItem.setExtendField3(temp.getExtendField3());
                energyDataItem.setExtendField4(temp.getExtendField4());
                energyDataItem.setExtendField5(temp.getExtendField5());
            }

        }
        return energyDataItems;
    }

    @Override
    public List<EnergyDataItemTimeliness> getDataItemTimelinessByEntryItemId(Integer entryItemId) {
        Date now = new Date();
        List<EnergyDataItemTimeliness> res = energyDataItemTimelinessMapper.selectList(new QueryWrapper<EnergyDataItemTimeliness>().eq("EntryItemId", entryItemId));
        if (res.isEmpty()) {
            return res;
        }
        res.sort((t1, t2) -> (int) ((t1.getStartTime().getTime() - t2.getStartTime().getTime()) / 1000));
        Integer key = -1;
        for (int i = 0; i < res.size(); i++) {
            if (res.get(i).getStartTime().after(now)) {
                if (key == -1) {
                    key = i;
                }
                res.get(i).setStatus(3);
            } else {
                res.get(i).setStatus(1);
            }
        }
        if (key == -1) {
            res.get(res.size() - 1).setStatus(2);
        }
        if (key > 0) {
            res.get(key - 1).setStatus(2);
        }

        return res;
    }

    @Override
    public void deleteEnergyDataTimeliness(Integer id) {
        energyDataItemTimelinessMapper.deleteById(id);
    }

    @Override
    public EnergyDataItemTimeliness updateEnergyDataTimeliness(EnergyDataItemTimeliness energyDataItemTimeliness) {
        energyDataItemTimelinessMapper.updateById(energyDataItemTimeliness);
        return energyDataItemTimeliness;
    }

    @Override
    public EnergyDataItem getEnergyDataItemTimeliness(Integer entryId,Integer dataItemId){
        List<EnergyDataItem> getAllByEntryIdTimeliness = getAllByEntryIdTimeliness(entryId);
        return getAllByEntryIdTimeliness.stream().filter(i -> i.getItemId().equals(dataItemId)).findFirst().get();
    }


}
