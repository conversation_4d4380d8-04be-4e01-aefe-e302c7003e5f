package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyAirConditionDataDTO;
import com.siteweb.energy.dto.EnergyLoadRateTempDTO;
import com.siteweb.monitoring.entity.Equipment;

import java.util.Date;
import java.util.List;

public interface EnergyAirConditionInfoService {
    List<Equipment> getAirConditionlist();

    List<EnergyAirConditionDataDTO> getAirConditionData(Date startTime, Date endTime, String equipmentId);

    List<EnergyLoadRateTempDTO> getLoadRateTempData(Date startTime, Date endTime, String loadranges, String tdranges);
}
