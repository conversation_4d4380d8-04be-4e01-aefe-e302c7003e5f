package com.siteweb.energy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.*;
import com.siteweb.energy.model.ChangeField;
import com.siteweb.energy.model.FpgInfo;
import com.siteweb.energy.model.SchemeUnitPrice;
import com.siteweb.energy.service.EnergyElecFeeConfigOperateLogService;
import com.siteweb.energy.service.EnergyElecFeeConfigService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyElecFeeConfigServiceImpl implements EnergyElecFeeConfigService, ApplicationListener<BaseSpringEvent<HAStatusChanged>> {

    private static String NOT_COVER_24HOURS = "";
    private static String FPG_HAS_OVERLAP = "";
    private static String STRUCTURE_HAS_OVERLAP = "";
    private static String APPLIED_RANGE_NOT_NULL = "";
    private static String AT_LEAST_ONE_STEP = "";

    @Autowired
    private EnergyElecFeeSchemeMapper schemeMapper;
    @Autowired
    private EnergyElecFeeStructureMapMapper structureMapMapper;
    @Autowired
    private EnergyElecFeeStepPriceMapper stepMapper;
    @Autowired
    private EnergyElecFeeFpgMapper fpgMapper;
    @Autowired
    private EnergyElecFeeFpgValueMapper fpgValueMapper;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyObjectMapMapper objectMapMapper;
    @Autowired
    private ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;

    @Autowired
    private EnergyElecFeeConfigOperateLogService operateLogService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    private HAStatusService haStatusService;

    @Override
    public ResourceStructure findResourceStructureTree() {
        return resourceStructureManager.getRoot();
    }

    /** 缓存已启用的方案 */
    /** 电费方案缓存对象1：电费方案对象 */
    private ConcurrentHashMap<Integer, ElecFeeScheme> elecFeeSchemeHashMap = new ConcurrentHashMap<>();
    /** 电费方案缓存对象2：层级id与电费方案关联关系集 */
    private List<EnergyElecFeeSchemeStructureMap>  structureMaps = new ArrayList<>();


    /** 获取当前日期对象，毫秒置为0 */
    public Date getCurDate() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    private String getMsgNotCover24Hours() {
        String defaultMsg = "_时间段未覆盖全天24小时";
        if(StringUtils.isBlank(NOT_COVER_24HOURS)) {
            String i18nMsg = localeMessageSourceUtil.getMessage("energy.msg.elecfeeconfig.notcover24hours");
            NOT_COVER_24HOURS = StringUtils.isBlank(i18nMsg) ? defaultMsg : i18nMsg;
        }
        return NOT_COVER_24HOURS;
    }
    private String getMsgFpgHasOverlap() {
        String defaultMsg = "_峰平谷各时间段有重叠";
        if(StringUtils.isBlank(FPG_HAS_OVERLAP)) {
            String i18nMsg = localeMessageSourceUtil.getMessage("energy.msg.elecfeeconfig.fpghasoverlap");
            FPG_HAS_OVERLAP = StringUtils.isBlank(i18nMsg) ? defaultMsg : i18nMsg;
        }
        return FPG_HAS_OVERLAP;
    }
    private String getMsgStructureHasOverlap() {
        String defaultMsg = "_以下已选层级与其已有方案存在[适用月份]重叠：";
        if(StringUtils.isBlank(STRUCTURE_HAS_OVERLAP)) {
            String i18nMsg = localeMessageSourceUtil.getMessage("energy.msg.elecfeeconfig.structurehasoverlap");
            STRUCTURE_HAS_OVERLAP = StringUtils.isBlank(i18nMsg) ? defaultMsg : i18nMsg;
        }
        return STRUCTURE_HAS_OVERLAP;
    }
    private String getMsgAppliedRangeNotNull() {
        String defaultMsg = "_应用范围不能为空";
        if(StringUtils.isBlank(APPLIED_RANGE_NOT_NULL)) {
            String i18nMsg = localeMessageSourceUtil.getMessage("energy.msg.elecfeeconfig.appliedrangenotnull");
            APPLIED_RANGE_NOT_NULL = StringUtils.isBlank(i18nMsg) ? defaultMsg : i18nMsg;
        }
        return APPLIED_RANGE_NOT_NULL;
    }
    private String getMsgAtLeastOneStep() {
        String defaultMsg = "_需至少保留一条阶梯定价";
        if(StringUtils.isBlank(AT_LEAST_ONE_STEP)) {
            String i18nMsg = localeMessageSourceUtil.getMessage("energy.msg.elecfeeconfig.atleastonestep");
            AT_LEAST_ONE_STEP = StringUtils.isBlank(i18nMsg) ? defaultMsg : i18nMsg;
        }
        return AT_LEAST_ONE_STEP;
    }

    @Override
    public ResultObject<List<ElecFeeScheme>> getElecFeeSchemesByResourceStructureId(Integer resourceStructureId) {
        List<EnergyElecFeeScheme> listDb = new ArrayList<>();
        List<ElecFeeScheme> list = new ArrayList<>();
        if(resourceStructureId == -1) {
            listDb = schemeMapper.getAll();
        } else {
            listDb = schemeMapper.findByResourceStructureId(resourceStructureId);
        }
        if(listDb == null) {
            listDb = new ArrayList<>();
        }
        listDb.forEach(one -> {
            ElecFeeScheme scheme = convertDBObjToElecFeeScheme(one);
            list.add(scheme);
        });
        return new ResultObject<>(list);
    }

    @Override
    public ResultObject<String> delElecFeeSchemes(List<Integer> ids, Integer userId, String userName) throws JsonProcessingException {
        Date curDate = getCurDate();

        if(ids != null && ids.size() > 0) {
            for (Integer schemeId : ids) {
                EnergyElecFeeScheme scheme = schemeMapper.findBySchemeId(schemeId);
                if(scheme == null) continue;
                ElecFeeScheme schemeWithStructureMaps = getSchemeWithStructureMaps(scheme);
                operateLogService.saveSchemeDeleteLog(schemeWithStructureMaps, curDate, userId, userName);
                //删除方案
                fpgValueMapper.deleteBySchemeId(schemeId);
                fpgMapper.deleteBySchemeId(schemeId);
                stepMapper.deleteBySchemeId(schemeId);
                structureMapMapper.deleteBySchemeId(schemeId);
                schemeMapper.deleteBySchemeId(schemeId);
                cacheDelSchemeInfoById(schemeId);
            }
        }

        cacheReloadStructureMaps();
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<ElecFeeScheme> getElecFeeSchemeBySchemeId(Integer schemeId) {
        EnergyElecFeeScheme schemeDb = schemeMapper.findBySchemeId(schemeId);
        if(schemeDb == null) {
            return new ResultObject<>(null);
        } else {
            List<EnergyElecFeeSchemeStructureMap> structureMaps = structureMapMapper.getBySchemeId(schemeId);
            ElecFeeScheme scheme = convertDBObjToElecFeeScheme(schemeDb);
            List<ElecFeeStructure> structures = new ArrayList<>();
            List<Integer> structureIds = new ArrayList<>();
            if(structureMaps != null) {
                structureMaps.forEach(item -> {
                    ElecFeeStructure structure = convertDBObjToElecFeeStructure(item);
                    structures.add(structure);
                    structureIds.add(structure.getResourceStructureId());
                });
            }
            scheme.setStructures(structures);
            scheme.setStructureIds(structureIds);
            return new ResultObject<>(scheme);
        }
    }

    @Override
    public ResultObject<List<EnergyElecFeeStepPrice>> getStepPricesBySchemeId(Integer schemeId) {
        List<EnergyElecFeeStepPrice> list = stepMapper.getBySchemeId(schemeId);
        return new ResultObject<>(list);
    }

    @Override
    public ResultObject<FpgInfo> getFpgsBySchemeId(Integer schemeId) {
        FpgInfo fpgInfo = new FpgInfo();
        //阶梯定价信息
        List<EnergyElecFeeStepPrice> steps = stepMapper.getBySchemeId(schemeId);
        fpgInfo.setStepList(steps == null ? new ArrayList<>() : steps);
        //峰平谷信息
        List<EnergyElecFeeFpg> fpgDbs = fpgMapper.getBySchemeId(schemeId);
        List<ElecFeeFpg> fpgs = new ArrayList<>();
        if(fpgDbs != null) {
            fpgDbs.forEach(fpgDb -> {
                ElecFeeFpg fpg = convertDBObjToElecFeeFpg(fpgDb);
                fpg.initEffectiveTimeData();
                fpg.setFpgValues(new ArrayList<ElecFeeFpgValue>());
                List<EnergyElecFeeFpgValue> valuesDb = fpgValueMapper.findListBy(fpg.getFpgId(), schemeId);
                valuesDb.forEach(vleDb -> {
                    fpg.getFpgValues().add(convertDBObjToElecFeeFpgValue(vleDb));
                });
                fpgs.add(fpg);
            });
        }
        fpgInfo.setFpgs(fpgs);
        //获取此类型方案的单位
        fpgInfo.setBusinessTypUnit(getUnitBySchemeId(schemeId));
        return new ResultObject<>(fpgInfo);
    }

    @Override
    public ResultObject<String> delElecFeeStepPrice(Integer stepPriceId, Integer schemeId, Integer userId, String userName) throws JsonProcessingException {

        int count = stepMapper.countBy(schemeId);
        if(count == 1) {
            return new ResultObject<>(getMsgAtLeastOneStep(), -110);
        }
        Date curDate = getCurDate();
        //删除并记录删除日志
        EnergyElecFeeStepPrice delStepPrice = stepMapper.getByCondition(stepPriceId, schemeId);
        if(delStepPrice != null) {
            operateLogService.saveStepPriceDeleteLog(delStepPrice, curDate, userId, userName);
            stepMapper.delByCondition(stepPriceId, schemeId); //删除阶梯定价记录
        }

        List<EnergyElecFeeFpgValue> delFpgValueList = fpgValueMapper.getByStepPriceId(stepPriceId, schemeId);
        if(delFpgValueList != null && delFpgValueList.size() > 0) {
            for (EnergyElecFeeFpgValue delFpgValue : delFpgValueList) {
                operateLogService.saveFpgValueDeleteLog(delStepPrice, delFpgValue, curDate, userId, userName);
            }
            fpgValueMapper.delByStepPriceId(stepPriceId, schemeId); //删除相应的value值
        }

        //更新缓存
        cacheUpdateSchemeInfoById(schemeId);
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> delElecFeeFpg(Integer fpgId, Integer schemeId, Integer userId, String userName) throws JsonProcessingException {

        Date curDate = getCurDate();
        List<EnergyElecFeeFpgValue> delFpgValueList = fpgValueMapper.getByFpgId(fpgId, schemeId);
        if(delFpgValueList != null && delFpgValueList.size() > 0) {
            for (EnergyElecFeeFpgValue delFpgValue : delFpgValueList) {
                operateLogService.saveFpgValueDeleteLog(null, delFpgValue, curDate, userId, userName);
            }
            fpgValueMapper.delByFpgId(fpgId, schemeId);
        }

        EnergyElecFeeFpg delFpg = fpgMapper.getByFpgIdAndSchemeId(fpgId, schemeId);
        if(delFpg != null) {
            operateLogService.saveFpgDeleteLog(delFpg, curDate, userId, userName);
            fpgMapper.delByCondition(fpgId, schemeId);
        }
        cacheUpdateSchemeInfoById(schemeId);
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<EnergyElecFeeStepPrice> getStepPriceByStepPriceId(Integer stepPriceId) {
        EnergyElecFeeStepPrice sp = stepMapper.findByStepPriceId(stepPriceId);
        return new ResultObject<>(sp);
    }

    @Override
    public ResultObject<ElecFeeFpg> getFpgByFpgId(Integer fpgId, Integer schemeId) {
        List<EnergyElecFeeStepPrice> stepPrices = stepMapper.getBySchemeId(schemeId);
        EnergyElecFeeFpg fpgDb = fpgMapper.findByFpgId(fpgId);
        ElecFeeFpg fpg = convertDBObjToElecFeeFpg(fpgDb);
        fpg.setFpgValues(new ArrayList<>());
        List<EnergyElecFeeFpgValue> listVleDb = fpgValueMapper.findListBy(fpgId, schemeId);
        if(listVleDb != null) {
            listVleDb.forEach(vleDb -> {
                ElecFeeFpgValue value = convertDBObjToElecFeeFpgValue(vleDb);
                if(stepPrices != null && stepPrices.size() > 0) {
                    for (EnergyElecFeeStepPrice item : stepPrices) {
                        if(item.getStepPriceId().equals(value.getStepId())) {
                            value.setStepName(item.getStepName());
                            break;
                        }
                    }
                }
                fpg.getFpgValues().add(value);
            });
        }
        return new ResultObject<>(fpg);
    }

    @Override
    public ResultObject<String> updateElecFeeScheme(ElecFeeScheme newScheme, Integer userId, String userName) {
        ResultObject<String> result = checkDataValidity(newScheme, 2);
        if(ResultObject.SUCCESS.equals(result.state)) {
            Date curDate = getCurDate();
            Integer schemeId = newScheme.getSchemeId();
            //0. 校验应用范围是否改变
            boolean hasChange = false;
            List<EnergyElecFeeSchemeStructureMap> oldMaps = structureMapMapper.getBySchemeId(schemeId);
            List<ElecFeeStructure> newMaps = newScheme.getStructures();
            String oldStructureIds = "";
            String newStructureIds = "";
            if(oldMaps != null && oldMaps.size() > 0) {
                oldStructureIds = oldMaps.stream().sorted(Comparator.comparing(EnergyElecFeeSchemeStructureMap::getResourceStructureId, Comparator.nullsFirst(Integer::compareTo)))
                        .map(item -> item.getResourceStructureId()+"").collect(Collectors.joining(","));
            }
            if(newMaps != null && newMaps.size() > 0) {
                newStructureIds = newMaps.stream().sorted(Comparator.comparing(ElecFeeStructure::getResourceStructureId, Comparator.nullsFirst(Integer::compareTo)))
                        .map(item -> item.getResourceStructureId()+"").collect(Collectors.joining(","));
            }
            if(!oldStructureIds.equals(newStructureIds)) {
                hasChange = true;
            }

            //1. 保存方案
            EnergyElecFeeScheme schemeDb = convertToScheme(newScheme, curDate, userId, userName);
            List<ChangeField> changeFieldList = operateLogService.diffSchemeTrunk(schemeDb, hasChange);
            if(changeFieldList != null && changeFieldList.size() > 0) {//如果有变更
                schemeMapper.updateById(schemeDb); //有schemeId时，save执行更新操作
                if(newScheme.getEnableStatusBool()) {
                    cacheUpdateSchemeInfoById(schemeId);
                } else {
                    cacheDelSchemeInfoById(schemeId);
                }
                operateLogService.saveSchemeTrunkUpdateLog(schemeDb, changeFieldList, curDate, userId, userName);
            }
            //2. 保存使用范围
            if(hasChange) {//如果应用范围发生了变化，则需要更新，否则保持不变

                //2.1 删除旧的所有关联数据
                structureMapMapper.deleteBySchemeId(schemeId);
                //2.2 插入新的修改过的关联集合数据
                List<EnergyElecFeeSchemeStructureMap> structureMapList = convertToStructureMap(newScheme, schemeId, curDate, userId, userName);
                structureMapList.forEach(one -> { structureMapMapper.insert(one); });
                //2.3 保存操作日志
                operateLogService.saveStructureMapUpdateLog(schemeDb, oldMaps, newMaps, curDate, userId, userName);
            }

            cacheReloadStructureMaps();//防止单独修改状态为启用，而应用范围又没有变化 且 之间映射数据为空时，映射数据载入
            //3. 封装执行成功数据
            result.state = ResultObject.SUCCESS;
            result.errcode = 1;
            result.data = "OK";
        }
        return result;
    }

    @Override
    public ResultObject<String> updateElecFeeStepPrice(EnergyElecFeeStepPrice newStepPrice, Integer userId, String userName) {

        List<ChangeField> changeFieldList = operateLogService.diffStepPrice(newStepPrice);
        if(changeFieldList != null && changeFieldList.size() > 0) {
            Date curDate = getCurDate();
            newStepPrice.setUpdateDate(curDate);
            newStepPrice.setUpdaterId(userId);
            newStepPrice.setUpdaterName(userName);
            stepMapper.updateById(newStepPrice);
            cacheUpdateSchemeInfoById(newStepPrice.getSchemeId());
            operateLogService.saveStepPriceUpdateLog(newStepPrice, changeFieldList , curDate, userId, userName);
        }
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> updateElecFeeFpg(ElecFeeFpg newFpg, Integer userId, String userName) {

        //校验时间段重复情况
        List<EnergyElecFeeFpg> otherFpgDbs = fpgMapper.getByCondition(newFpg.getSchemeId(), newFpg.getFpgId());
        if(otherFpgDbs != null || otherFpgDbs.size() > 0) {
            List<ElecFeeFpg> fpgs = new ArrayList<>();
            for (EnergyElecFeeFpg otherFpgDb : otherFpgDbs) {
                fpgs.add(convertDBObjToElecFeeFpg(otherFpgDb));
            }
            fpgs.add(newFpg);
            boolean hasOverlap = checkTimeHasOverlap(fpgs);
            if(hasOverlap) {
                return new ResultObject<>(getMsgFpgHasOverlap(), -101);
            }
        }
        //更新fpg
        boolean hasChange = false;
        Date updateDate = getCurDate();
        EnergyElecFeeFpg fpgDb = convertToFpg(newFpg, newFpg.getSchemeId(), updateDate, true, userId, userName);
        List<ChangeField> changeFieldFpgList = operateLogService.diffFeeFpg(fpgDb);
        if(changeFieldFpgList != null && changeFieldFpgList.size() > 0) {
            hasChange = true;
            fpgMapper.updateById(fpgDb);
            operateLogService.saveFpgUpdateLog(fpgDb, changeFieldFpgList, updateDate, userId, userName);
        }
        //更新fpgValues
        List<ElecFeeFpgValue> fpgValues = newFpg.getFpgValues();
        List<EnergyElecFeeFpgValue> fpgValuesDb = convertToFpgValue(fpgValues, updateDate, userId, userName);
        for (EnergyElecFeeFpgValue fpgValueDb : fpgValuesDb) {
            Double fValue = Double.valueOf(fpgValueDb.getFpgValue());
            List<ChangeField> changeFieldFpgVleList = operateLogService.diffFeeFpgValue(fpgValueDb);
            if(changeFieldFpgVleList != null && changeFieldFpgVleList.size() > 0) {
                hasChange = true;
                fpgValueMapper.updateById(fpgValueDb);
                operateLogService.saveFpgValueUpdateLog(fpgValueDb, changeFieldFpgVleList, updateDate, userId, userName);
            }
        }
        if(hasChange) {
            cacheUpdateSchemeInfoById(fpgDb.getSchemeId());
        }
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> addElecFeeStepPrice(EnergyElecFeeStepPrice newStepPrice, Integer userId, String userName) throws JsonProcessingException {
        Date curDate = getCurDate();
        newStepPrice.setCreateDate(curDate);
        newStepPrice.setUpdateDate(curDate);
        newStepPrice.setCreaterId(userId);
        newStepPrice.setCreaterName(userName);
        newStepPrice.setUpdaterId(userId);
        newStepPrice.setUpdaterName(userName);

        stepMapper.insert(newStepPrice);
        //新增阶梯定价后，需要为此方案下的所有峰平谷默认插入一条值为0的记录
        List<EnergyElecFeeFpg> fpgDbs = fpgMapper.getBySchemeId(newStepPrice.getSchemeId());
        List<EnergyElecFeeFpgValue> fpgValueSaveDbs = new ArrayList<>();
        if(fpgDbs != null) {
            fpgDbs.forEach(fpg -> {
                EnergyElecFeeFpgValue oneValue = new EnergyElecFeeFpgValue();
                oneValue.setFpgId(fpg.getFpgId());
                oneValue.setSchemeId(newStepPrice.getSchemeId());
                oneValue.setStepPriceId(newStepPrice.getStepPriceId());
                oneValue.setFpgValue("-1"); //默认值为0
                oneValue.setCreateDate(curDate);
                oneValue.setUpdateDate(curDate);
                oneValue.setCreaterId(userId);
                oneValue.setCreaterName(userName);
                oneValue.setUpdaterId(userId);
                oneValue.setUpdaterName(userName);
                fpgValueMapper.insert(oneValue);
                fpgValueSaveDbs.add(oneValue);
            });
        }

        cacheUpdateSchemeInfoById(newStepPrice.getSchemeId());

        //添加日志
        operateLogService.saveStepPriceInsertLog(newStepPrice, fpgValueSaveDbs, curDate, userId, userName);

        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> addElecFeeFpg(ElecFeeFpg newFpg, Integer userId, String userName) throws JsonProcessingException {
        Date curDate = getCurDate();
        //0. 校验时间段重复
        List<EnergyElecFeeFpg> otherFpgDbs = fpgMapper.getBySchemeId(newFpg.getSchemeId());
        if(otherFpgDbs != null || otherFpgDbs.size() > 0) {
            List<ElecFeeFpg> fpgs = new ArrayList<>();
            for (EnergyElecFeeFpg otherFpgDb : otherFpgDbs) {
                fpgs.add(convertDBObjToElecFeeFpg(otherFpgDb));
            }
            fpgs.add(newFpg);
            boolean hasOverlap = checkTimeHasOverlap(fpgs);
            if(hasOverlap) {
                return new ResultObject<>(getMsgFpgHasOverlap(), -101);
            }
        }
        //1. 保存一个fpg记录
        EnergyElecFeeFpg fpg = convertToFpg(newFpg, newFpg.getSchemeId(), curDate, false, userId, userName);
        fpgMapper.insert(fpg);
        Integer curFpgId = fpg.getFpgId();
        //2. 保存这个fpg记录的所用子记录的值记录
        List<EnergyElecFeeFpgValue> vleDbList = convertToFpgValue(newFpg.getFpgValues(), curDate, userId, userName);
        List<EnergyElecFeeFpgValue> vleSaveList = new ArrayList<>();
        for (EnergyElecFeeFpgValue vleDb : vleDbList) {
            Double fValue = Double.valueOf(vleDb.getFpgValue());
            vleDb.setFpgId(curFpgId);//与新创建的fpgId作关联
            fpgValueMapper.insert(vleDb);
            vleSaveList.add(vleDb);
        }

        cacheUpdateSchemeInfoById(newFpg.getSchemeId());

        //添加日志
        operateLogService.saveFpgInsertLog(fpg, vleSaveList, curDate, userId, userName);

        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> addElecFeeScheme(ElecFeeScheme newScheme, Integer userId, String userName) throws JsonProcessingException {
        ResultObject<String> result = checkDataValidity(newScheme, 1);
        if(ResultObject.SUCCESS.equals(result.state)) {//校验通过
            Date curDate = getCurDate(); //本次操作统一使用此时间
            //1. 保存方案
            EnergyElecFeeScheme scheme = convertToScheme(newScheme, curDate, userId, userName);
            schemeMapper.insert(scheme);
            Integer schemeId = scheme.getSchemeId();
            //2. 保存使用范围
            List<EnergyElecFeeSchemeStructureMap> structureMapList = convertToStructureMap(newScheme, schemeId, curDate, userId, userName);
            structureMapList.forEach(one -> { structureMapMapper.insert(one); });
            //3. 保存阶梯定价
            Map<Integer, Integer> newOldStepId = new HashMap<>(); //用于保存页面传回的临时阶梯id与实际存入数据库的阶梯id的对应关系
            List<EnergyElecFeeStepPrice> spList = convertToStepPrice(newScheme, schemeId, curDate, userId, userName);
            for (EnergyElecFeeStepPrice oneSp : spList) {
                Integer pageStepId = oneSp.getStepPriceId();
                oneSp.setStepPriceId(null);
                stepMapper.insert(oneSp);
                newOldStepId.put(pageStepId, oneSp.getStepPriceId());
            }
            //4. 保存峰平谷记录
            for (ElecFeeFpg one : newScheme.getFpgs()) {
                //4.1. 保存一个fpg记录
                EnergyElecFeeFpg fpg = convertToFpg(one, schemeId, curDate, false, userId, userName);
                fpgMapper.insert(fpg);
                Integer curFpgId = fpg.getFpgId();
                //4.2. 保存这个fpg记录的所用子记录的值记录
                List<EnergyElecFeeFpgValue> vleList = convertToFpgValue(one.getFpgValues(), newOldStepId, schemeId, curFpgId, curDate, userId, userName);
                for (EnergyElecFeeFpgValue vle : vleList) {
                    Double fValue = Double.valueOf(vle.getFpgValue());
                    fpgValueMapper.insert(vle);
                }
            }

            if(newScheme.getEnableStatusBool()) {//新增的启用的方案才需要加载到缓存中
                cacheUpdateSchemeInfoById(schemeId);
            }
            cacheReloadStructureMaps();

            //插入新增日志
            ElecFeeScheme schemeObj = getSchemeWithStructureMaps(scheme);
            operateLogService.saveSchemeInsertLog(schemeObj, curDate, userId, userName);

            result.state = ResultObject.SUCCESS;
            result.errcode = 1;
            result.data = "OK";
        }
        return result;
    }

    private ElecFeeFpgValue convertDBObjToElecFeeFpgValue(EnergyElecFeeFpgValue valueDb) {
        ElecFeeFpgValue value = new ElecFeeFpgValue();
        value.setFpgValueId(valueDb.getFpgValueId());
        value.setFpgId(valueDb.getFpgId());
        value.setFpgValue(valueDb.getFpgValue());
        value.setSchemeId(valueDb.getSchemeId());
        value.setStepId(valueDb.getStepPriceId());
        value.setCreateDate(valueDb.getCreateDate());
        value.setUpdateDate(valueDb.getUpdateDate());

        value.setCreaterId(valueDb.getCreaterId());
        value.setCreaterName(valueDb.getCreaterName());
        value.setUpdaterId(valueDb.getUpdaterId());
        value.setUpdaterName(valueDb.getUpdaterName());

        value.setExtendField1(valueDb.getExtendField1());
        return value;
    }

    private ElecFeeFpg convertDBObjToElecFeeFpg(EnergyElecFeeFpg fpgDb) {
        ElecFeeFpg fpg = new ElecFeeFpg();
        fpg.setFpgId(fpgDb.getFpgId());
        fpg.setEffectiveStartStr(toHHmmssStr(fpgDb.getEffectiveStart()));
        fpg.setEffectiveEndStr(toHHmmssStr(fpgDb.getEffectiveEnd()));
        fpg.setCreateDate(fpgDb.getCreateDate());
        fpg.setUpdateDate(fpgDb.getUpdateDate());
        fpg.setSchemeId(fpgDb.getSchemeId());

        fpg.setCreaterId(fpgDb.getCreaterId());
        fpg.setCreaterName(fpgDb.getCreaterName());
        fpg.setUpdaterId(fpgDb.getUpdaterId());
        fpg.setUpdaterName(fpgDb.getUpdaterName());

        fpg.setFpgDescKey(fpgDb.getFpgDescKey());
        fpg.setFpgDesc(fpgDb.getFpgDesc());

        fpg.setExtendField1(fpgDb.getExtendField1());
        return fpg;
    }

    private ElecFeeStructure convertDBObjToElecFeeStructure(EnergyElecFeeSchemeStructureMap structureMapDb) {
        ElecFeeStructure structure = new ElecFeeStructure();
        structure.setMapId(structureMapDb.getMapId());
        structure.setSchemeId(structureMapDb.getSchemeId());
        structure.setResourceStructureId(structureMapDb.getResourceStructureId());
        structure.setResourceStructureName(structureMapDb.getResourceStructureName());
        structure.setCreateDate(structureMapDb.getCreateDate());
        structure.setUpdateDate(structureMapDb.getUpdateDate());

        structure.setCreaterId(structureMapDb.getCreaterId());
        structure.setCreaterName(structureMapDb.getCreaterName());
        structure.setUpdaterId(structureMapDb.getUpdaterId());
        structure.setUpdaterName(structureMapDb.getUpdaterName());

        structure.setExtendField1(structureMapDb.getExtendField1());
        return structure;
    }

    private ElecFeeScheme convertDBObjToElecFeeScheme(EnergyElecFeeScheme schemeDb) {
        ElecFeeScheme scheme = new ElecFeeScheme();
        scheme.setSchemeId(schemeDb.getSchemeId());
        scheme.setSchemeName(schemeDb.getSchemeName());
        scheme.setAppliedRange(schemeDb.getAppliedRange());
        scheme.setEnableDate(schemeDb.getEnableDate());
        scheme.setDeactivateDate(schemeDb.getDeactivateDate());
        scheme.setCreateDate(schemeDb.getCreateDate());
        scheme.setUpdateDate(schemeDb.getUpdateDate());
        String enable = localeMessageSourceUtil.getMessage("energy.common.enable");
        String disable = localeMessageSourceUtil.getMessage("energy.common.disable");
        enable = StringUtils.isBlank(enable) ? "_enable" : enable;
        disable = StringUtils.isBlank(disable) ? "_disable" : disable;
        scheme.setEnableStatus(schemeDb.getEnableStatus() == 1 ? enable : disable);
        scheme.setEnableStatusBool(schemeDb.getEnableStatus() == 1);
        scheme.setStartMonth(schemeDb.getStartMonth());
        scheme.setEndMonth(schemeDb.getEndMonth());
        scheme.setAppliedMonth(scheme.joinAppliedMonth(localeMessageSourceUtil));

        scheme.setCreaterId(schemeDb.getCreaterId());
        scheme.setCreaterName(schemeDb.getCreaterName());
        scheme.setUpdaterId(schemeDb.getUpdaterId());
        scheme.setUpdaterName(schemeDb.getUpdaterName());
        scheme.setBusinessTypeId(schemeDb.getBusinessTypeId());
        scheme.setBusinessTypeName(schemeDb.getBusinessTypeName());
        scheme.setTypeName(schemeDb.getTypeName());

        scheme.setExtendField1(schemeDb.getExtendField1());
        scheme.setExtendField2(schemeDb.getExtendField2());
        return scheme;
    }

    /** 仅用于新增方案时批量保存阶梯定价前的转换 */
    private List<EnergyElecFeeFpgValue> convertToFpgValue(List<ElecFeeFpgValue> fpgValues, Map<Integer, Integer> newOldStepId,Integer schemeId, Integer fpgId, Date curDate, Integer userId, String userName) {
        List<EnergyElecFeeFpgValue> vleList = new ArrayList<>();
        fpgValues.forEach(oneValue -> {
            EnergyElecFeeFpgValue vleDb = new EnergyElecFeeFpgValue();
            vleDb.setFpgId(fpgId);
            vleDb.setSchemeId(schemeId);
            vleDb.setStepPriceId(newOldStepId.get(oneValue.getStepId()));
            vleDb.setFpgValue(oneValue.getFpgValue());
            vleDb.setCreateDate(curDate);
            vleDb.setUpdateDate(curDate);

            vleDb.setCreaterId(userId);
            vleDb.setCreaterName(userName);
            vleDb.setUpdaterId(userId);
            vleDb.setUpdaterName(userName);

            vleList.add(vleDb);
        });
        return vleList;
    }
    /** 用于单条修改或单条新增时 */
    private List<EnergyElecFeeFpgValue> convertToFpgValue(List<ElecFeeFpgValue> fpgValues, Date curDate, Integer userId, String userName) {
        List<EnergyElecFeeFpgValue> vleList = new ArrayList<>();
        fpgValues.forEach(oneValue -> {
            EnergyElecFeeFpgValue vleDb = new EnergyElecFeeFpgValue();
            vleDb.setFpgId(oneValue.getFpgId());
            vleDb.setSchemeId(oneValue.getSchemeId());
            vleDb.setStepPriceId(oneValue.getStepId());
            vleDb.setFpgValue(oneValue.getFpgValue());
            vleDb.setCreateDate(curDate);
            vleDb.setUpdateDate(curDate);
            vleDb.setCreaterId(userId);
            vleDb.setCreaterName(userName);
            vleDb.setUpdaterId(userId);
            vleDb.setUpdaterName(userName);
            if(oneValue.getFpgValueId() != null) {//修改
                vleDb.setFpgValueId(oneValue.getFpgValueId());
                //还原创建相关信息
                vleDb.setCreateDate(oneValue.getCreateDate());
                vleDb.setCreaterId(oneValue.getCreaterId());
                vleDb.setCreaterName(oneValue.getCreaterName());
            }
            vleList.add(vleDb);
        });
        return vleList;
    }

    private EnergyElecFeeFpg convertToFpg(ElecFeeFpg fpg, Integer schemeId, Date curDate, boolean isUpdate, Integer userId, String userName) {
        EnergyElecFeeFpg fpgDb = new EnergyElecFeeFpg();
        fpgDb.setSchemeId(schemeId);
        fpgDb.setEffectiveStart(ElecFeeFpg.convertHHmmssToDate(fpg.getEffectiveStartStr()));
        fpgDb.setEffectiveEnd(ElecFeeFpg.convertHHmmssToDate(fpg.getEffectiveEndStr()));
        fpgDb.setCreateDate(curDate);
        fpgDb.setUpdateDate(curDate);
        fpgDb.setCreaterId(userId);
        fpgDb.setCreaterName(userName);
        fpgDb.setUpdaterId(userId);
        fpgDb.setUpdaterName(userName);
        fpgDb.setFpgDescKey(fpg.getFpgDescKey());
        fpgDb.setFpgDesc(fpg.getFpgDesc());
        if(isUpdate) {
            fpgDb.setFpgId(fpg.getFpgId());
            fpgDb.setCreateDate(fpg.getCreateDate());
            fpgDb.setCreaterId(fpg.getCreaterId());
            fpgDb.setCreaterName(fpg.getCreaterName());
        }
        return fpgDb;
    }



    /** 只取日期的 HH:mm:ss部分 */
    private String toHHmmssStr(Date time) {
        SimpleDateFormat formatter;
        formatter = new SimpleDateFormat("HH:mm:ss");
        return formatter.format(time);
    }

    private List<EnergyElecFeeStepPrice> convertToStepPrice(ElecFeeScheme newScheme, Integer schemeId, Date curDate, Integer userId, String userName) {
        List<EnergyElecFeeStepPrice> list = new ArrayList<>();
        for (ElecFeeStepPrice stepPrice : newScheme.getStepPrices()) {
            EnergyElecFeeStepPrice sp = new EnergyElecFeeStepPrice();
            sp.setStepPriceId(stepPrice.getStepId()); //此id只是页面保存的临时id
            sp.setSchemeId(schemeId);
            sp.setStepName(stepPrice.getStepName());
            sp.setUpperLimit(stepPrice.getUpperLimit());
            sp.setCreateDate(curDate);
            sp.setUpdateDate(curDate);
            sp.setCreaterId(userId);
            sp.setCreaterName(userName);
            sp.setUpdaterId(userId);
            sp.setUpdaterName(userName);
            sp.setAsMaxStep(stepPrice.getAsMaxStep());
            list.add(sp);
        }
        return  list;
    }

    private List<EnergyElecFeeSchemeStructureMap> convertToStructureMap(ElecFeeScheme newScheme, Integer schemeId, Date curDate, Integer userId, String userName) {
        List<EnergyElecFeeSchemeStructureMap> list = new ArrayList<>();
        for (ElecFeeStructure structure : newScheme.getStructures()) {
            EnergyElecFeeSchemeStructureMap map = new EnergyElecFeeSchemeStructureMap();
            map.setSchemeId(schemeId);
            map.setResourceStructureId(structure.getResourceStructureId());
            map.setResourceStructureName((structure.getResourceStructureName()));
            map.setCreateDate(curDate);
            map.setUpdateDate(curDate);
            map.setCreaterId(userId);
            map.setCreaterName(userName);
            map.setUpdaterId(userId);
            map.setUpdaterName(userName);
            map.setExtendField1(structure.getExtendField1());
            list.add(map);
        }
        return list;
    }

    private EnergyElecFeeScheme convertToScheme(ElecFeeScheme newScheme, Date curDate, Integer userId, String userName) {
        EnergyElecFeeScheme scheme = new EnergyElecFeeScheme();
        scheme.setSchemeName(newScheme.getSchemeName());
        scheme.setAppliedRange(joinStructureName(newScheme.getStructures()));
        scheme.setCreateDate(curDate);
        scheme.setUpdateDate(curDate);
        scheme.setEnableDate(newScheme.getEnableDate());
        scheme.setDeactivateDate(newScheme.getDeactivateDate());
        scheme.setEnableStatus(newScheme.getEnableStatusBool() ? 1 : 0);
        scheme.setStartMonth(newScheme.getStartMonth());
        scheme.setEndMonth(newScheme.getEndMonth());
        scheme.setCreaterId(userId);
        scheme.setCreaterName(userName);
        scheme.setUpdaterId(userId);
        scheme.setUpdaterName(userName);
        scheme.setBusinessTypeId(newScheme.getBusinessTypeId());
        scheme.setBusinessTypeName(newScheme.getBusinessTypeName());
        if(newScheme.getSchemeId() != null) {//如果是更新，保持创建者的信息不变
            scheme.setSchemeId(newScheme.getSchemeId());
            scheme.setCreateDate(newScheme.getCreateDate());
            scheme.setCreaterId(newScheme.getCreaterId());
            scheme.setCreaterName(newScheme.getCreaterName());
        }
        return scheme;
    }

    private String joinStructureName(List<ElecFeeStructure> list) {
        String result = "";
        for (ElecFeeStructure one : list) {
            result += ("," + one.getResourceStructureName());
        }
        if(result.length() > 0) {
            result = result.substring(1);
            if(result.length() > 1000) {//数据库此字段最多存储1024个字符
                result = result.substring(0, 1000) + "...";
            }
        }
        return result;
    }

    /** 校验欲新增的方案的数据有效性。 (type: 1-新增, 2-修改)*/
    private ResultObject<String> checkDataValidity(ElecFeeScheme scheme, int type) {
        ResultObject<String> result = new ResultObject<String>();
        //1.校验应用范围 （同一个层级下，同一个类型可以有多个方案，但是同一个类型的多个方案的适用月份范围不可重叠）
        List<ElecFeeStructure> structures = scheme.getStructures();
        if(structures == null || structures.size() < 1) {
            return new ResultObject<String>(getMsgAppliedRangeNotNull(), -100);
        } else {
            Integer curSchemeId = type == 2 ? scheme.getSchemeId() : null;
            List<ElecFeeStructure> hasOverlapStcts = new ArrayList<>();
            structures.forEach(stct -> {
                List<EnergyElecFeeScheme> existSchemes = schemeMapper.findByResourceStructureIdAndTypeId(stct.getResourceStructureId(), scheme.getBusinessTypeId());
                if(existSchemes != null) {
                    for (EnergyElecFeeScheme one : existSchemes) {
                        if(curSchemeId == null || !curSchemeId.equals(one.getSchemeId())) {//新增方案时需要对所有已有方案进行校验；方案修改时，需要对除正在修改的方案之外的所有已有方案进行校验
                            if((scheme.getStartMonth() >= one.getStartMonth() && scheme.getStartMonth() <= one.getEndMonth())
                                    || (scheme.getEndMonth() >= one.getStartMonth() && scheme.getEndMonth() <= one.getEndMonth())
                                    ||(one.getStartMonth() >= scheme.getStartMonth() && one.getStartMonth() <= scheme.getEndMonth())
                                    || (one.getEndMonth() >= scheme.getStartMonth() && one.getEndMonth() <= scheme.getEndMonth())) {//适用月份有重叠
                                hasOverlapStcts.add(stct);
                                break;
                            }
                        }
                    }
                }
            });
            if(hasOverlapStcts.size() > 0) {
                String resultStr = getMsgStructureHasOverlap() + "<br/>";
                for (ElecFeeStructure hasOverlapStct : hasOverlapStcts) {
                    resultStr += (hasOverlapStct.getResourceStructureName() + "(" + hasOverlapStct.getResourceStructureId() + ")；");
                }
                resultStr = resultStr.substring(0, resultStr.length() - 1);
                return new ResultObject<String>(resultStr, -99);
            }
        }
        //2. 校验峰平谷时间段
        if(type == 1) {//新增时
            List<ElecFeeFpg> fpgs = scheme.getFpgs();
            boolean timeHasOverlap = checkTimeHasOverlap(fpgs);
            if(timeHasOverlap) {//时段有重叠
                return new ResultObject<String>(getMsgFpgHasOverlap(), -101);
            } else {//若没有重叠，校验是否覆盖了一整天(24小时)
                boolean hasCoverAll = checkTimeAllCover(fpgs);
                if(!hasCoverAll) {
                    return new ResultObject<String>(getMsgNotCover24Hours(), -102);
                }
            }
        } else if(type == 2) {//修改时
            //修改提交时，峰平谷的时段重叠校验已经做过，此处直接进行全覆盖验证
            if(scheme.getSchemeId() == null) {
                return new ResultObject<String>("schemeId is Null", -103);
            } else {
                List<EnergyElecFeeFpg> fpgDbs = fpgMapper.getBySchemeId(scheme.getSchemeId());
                List<ElecFeeFpg> fpgs = new ArrayList<>();
                if(fpgDbs == null || fpgDbs.size() < 1) {
                    return new ResultObject<String>("fpgs is Empty", -104);
                } else {
                    for (EnergyElecFeeFpg fpgDb : fpgDbs) {
                        ElecFeeFpg elecFeeFpg = convertDBObjToElecFeeFpg(fpgDb);
                        fpgs.add(elecFeeFpg);
                    }
                    boolean hasCoverAll = checkTimeAllCover(fpgs);
                    if(!hasCoverAll) {
                        return new ResultObject<String>(getMsgNotCover24Hours(), -102);
                    }
                }
            }
        }
        return result;
    }

    /** 判断传入的平谷时间段集合中任意两个时间段是否有重叠： true-有重叠；false-无重叠 */
    private boolean checkTimeHasOverlap(List<ElecFeeFpg> fpgs) {
        boolean timeHasOverlap = false;
        if(fpgs == null || fpgs.size() <= 1) {//起码有两个才比较
            return false;
        }
        for (int i = 0; i < fpgs.size(); i++) {
            boolean hasFindOverLap = false;
            for (int j = fpgs.size() - 1; j > i; j--) {
                ElecFeeFpg f1 = fpgs.get(i);
                ElecFeeFpg f2 = fpgs.get(j);
                f1.initEffectiveTimeData();
                f2.initEffectiveTimeData();
                long f1MillStart = f1.getEffectiveStartMill();
                long f1MillEnd = f1.getEffectiveEndMill();
                long f2MillStart = f2.getEffectiveStartMill();
                long f2MillEnd = f2.getEffectiveEndMill();
                if((f2MillStart >= f1MillStart && f2MillStart <= f1MillEnd)
                        || (f2MillEnd >= f1MillStart && f2MillEnd <= f1MillEnd)
                        || (f1MillStart >= f2MillStart && f1MillStart <= f2MillEnd)
                        || (f1MillEnd >= f2MillStart && f1MillEnd <= f2MillEnd)
                ) {
                    hasFindOverLap = true;
                    break;
                }
            }
            if(hasFindOverLap) {
                timeHasOverlap = true;
                break;
            }
        }
        return timeHasOverlap;
    }

    /** 校验传入的所有峰平谷各时间段是否完全覆盖了全天24小时 */
    private boolean checkTimeAllCover(List<ElecFeeFpg> elecFeeFpgs) {
        if(elecFeeFpgs == null || elecFeeFpgs.size() < 1) {
            return false;
        } else {
            long day24HourMill = 24 * 60 * 60 * 1000;
            long totalMill = 0;
            for (ElecFeeFpg fpg : elecFeeFpgs) {
                fpg.initEffectiveTimeData();
                long thisMill = fpg.getEffectiveEndMill() - fpg.getEffectiveStartMill() + 1000;//1000ms
                totalMill += thisMill;
            }
            if(totalMill != day24HourMill) {
                return false;
            }
        }
        return true;
    }

    //------  缓存相关方法 begin
    @PostConstruct
    public void init() {
        log.info("开始加载电费方案至缓存...");
        cacheLoadAllEnabledSchemeInfo();
        log.info("加载电费方案至缓存完毕：maps count=" + (structureMaps == null ? -1 : structureMaps.size())
                + "; scheme count=" + (elecFeeSchemeHashMap == null ? -1 : elecFeeSchemeHashMap.size()));
    }

    /**
     * 发生双机切换，重新初始化缓存
     * @param haStatusChangedEvent 双机切换事件
     */
    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<HAStatusChanged> haStatusChangedEvent) {
        if (!haStatusService.isEnabled()) {
            return;
        }
        log.info("检测到双机切换事件，开始加载电费方案至缓存...");
        cacheLoadAllEnabledSchemeInfo();
        log.info("检测到双机切换事件，加载电费方案至缓存完毕：maps count=" + (structureMaps == null ? -1 : structureMaps.size())
                + "; scheme count=" + (elecFeeSchemeHashMap == null ? -1 : elecFeeSchemeHashMap.size()));
    }


    @Override
    public void reloadCache() {
        cacheLoadAllEnabledSchemeInfo();
    }

    private void cacheLoadAllEnabledSchemeInfo() {

        try {
            cacheClear();
            List<EnergyElecFeeScheme> schemeDbs = schemeMapper.getAllEnabledSchemes();
            List<ElecFeeScheme> allEnabledSchemes = getSchemeObj(schemeDbs);
            if(allEnabledSchemes != null && allEnabledSchemes.size() > 0) {
                for (ElecFeeScheme oneEnabledScheme : allEnabledSchemes) {
                    elecFeeSchemeHashMap.put(oneEnabledScheme.getSchemeId(), oneEnabledScheme);
                }
                List<EnergyElecFeeSchemeStructureMap> allStructureMap = structureMapMapper.selectList(null);
                if(allStructureMap != null && allStructureMap.size() > 0) {
                    structureMaps = allStructureMap;
                }
            }
        } catch (Exception ex) {
            cacheClear();
            log.error("启动加载电费方案时出现未知异常（清空现有缓冲）：", ex);
        }
    }

    /**  重新加载电费方案应用范围关系表信息 */
    public boolean cacheReloadStructureMaps() {
        try {
            List<EnergyElecFeeSchemeStructureMap> allStructureMap = structureMapMapper.selectList(null);
            if(allStructureMap == null || allStructureMap.size() < 1) {
                structureMaps = new ArrayList<>();
            } else {
                structureMaps = allStructureMap;
            }
            return true;
        } catch (Exception ex) {
            log.error("reloadStructureMaps时出现未知异常：", ex);
            throw ex;
        }
    }

    /** 清除某个方案 */
    public boolean cacheDelSchemeInfoById(Integer schemeId) {
        try {
            elecFeeSchemeHashMap.remove(schemeId);
            return true;
        } catch (Exception ex) {
            log.error("delSchemeInfoById(" + schemeId + ")时出现未知异常：", ex);
            throw ex;
        }
    }

    /** 更新或新增某个方案 */
    public boolean cacheUpdateSchemeInfoById(Integer schemeId) {
        try {
            EnergyElecFeeScheme schemeDb = schemeMapper.findBySchemeId(schemeId);
            if(schemeDb == null) {
                log.warn("updateSchemeInfoById：方案(schemeId=" + schemeId + ")不存在");
            } else if(schemeDb.getEnableStatus() == null) {
                log.error("updateSchemeInfoById：方案(schemeId=" + schemeId + ") schemeDb.getEnableStatus() == null");
            } else if(schemeDb.getEnableStatus().equals(1)) {//方案启用才进缓存
                List<EnergyElecFeeScheme> param = new ArrayList<>();
                param.add(schemeDb);
                List<ElecFeeScheme> schemeObj = getSchemeObj(param);
                if(schemeObj != null && schemeObj.size() > 0) {
                    elecFeeSchemeHashMap.put(schemeObj.get(0).getSchemeId(), schemeObj.get(0));
                }
            } else {
                log.warn("updateSchemeInfoById：方案(schemeId=" + schemeId + ") enableStatus(" + schemeDb.getEnableStatus() + ") != 1，未启用的方案不进缓存");
            }
            return true;
        } catch (Exception ex) {
            log.error("updateSchemeInfoById(" + schemeId + ")时出现未知异常：", ex);
            throw ex;
        }
    }

    /** 清空所有缓冲信息 */
    private void cacheClear() {
        elecFeeSchemeHashMap = new ConcurrentHashMap<>();
        structureMaps = new ArrayList<>();
    }

    /** 获取完整的电费方案对象 */
    private List<ElecFeeScheme> getSchemeObj(List<EnergyElecFeeScheme> schemeDbs) {

        try {
            List<ElecFeeScheme> rtn = new ArrayList<>();
            if(schemeDbs != null && schemeDbs.size() >0) {
                for (EnergyElecFeeScheme schemeDb : schemeDbs) {
                    Integer schemeId = schemeDb.getSchemeId();
                    ElecFeeScheme scheme = convertDBObjToElecFeeScheme(schemeDb);
                    //封装阶梯定价数据 和 峰平谷数据
                    ResultObject<FpgInfo> result = getFpgsBySchemeId(schemeId);
                    if(result == null) {
                        log.error("getSchemeObj: schemeId(" + schemeId + "), getFpgsBySchemeId: result==null");
                    } else if(result.state != 1) {
                        log.error("getSchemeObj: schemeId(" + schemeId + "), getFpgsBySchemeId: result.state != 1");
                    } else if(result.data == null) {
                        log.error("getSchemeObj: schemeId(" + schemeId + "), getFpgsBySchemeId: result.data == null");
                    } else {
                        FpgInfo info = result.data;//info中同时包含和阶梯定价信息和峰平谷信息
                        //封装阶梯定价数据
                        List<ElecFeeStepPrice> sps = new ArrayList<>();
                        for (EnergyElecFeeStepPrice spDb : info.getStepList()) {
                            ElecFeeStepPrice sp = convertDBObjToElecFeeStepPrice(spDb);
                            sps.add(sp);
                        }
                        scheme.setStepPrices(sps);
                        //封装峰平谷数据
                        scheme.setFpgs(info.getFpgs());
                    }
                    rtn.add(scheme);
                }
            }
            return rtn;
        } catch (Exception ex) {
            log.error("getSchemeObj(schemeDbs)执行时出现未知异常：", ex);
            throw ex;
        }
    }

    private ElecFeeStepPrice convertDBObjToElecFeeStepPrice(EnergyElecFeeStepPrice spDb) {
        ElecFeeStepPrice sp = new ElecFeeStepPrice();
        sp.setStepId(spDb.getStepPriceId());
        sp.setSchemeId(spDb.getSchemeId());
        sp.setStepName(spDb.getStepName());
        sp.setUpperLimit(spDb.getUpperLimit());
        sp.setCreaterId(spDb.getCreaterId());
        sp.setCreaterName(spDb.getCreaterName());
        sp.setCreateDate(spDb.getCreateDate());
        sp.setUpdaterId(spDb.getUpdaterId());
        sp.setUpdaterName(spDb.getUpdaterName());
        sp.setUpdateDate(spDb.getUpdateDate());
        sp.setExtendField1(spDb.getExtendField1());
        return sp;
    }
    //------  缓存相关方法 end

    //------  单价请求接口相关方法 begin
    /** 查找此层级对应的单价方案，未找到时返回null */
    private ElecFeeScheme tryToGetScheme(int resourceId, Date calcTime, Integer businessTypeId) {

        if(businessTypeId == null) return null;
        List<ElecFeeScheme> findSchemes = new ArrayList<>();
        ElecFeeScheme result = null;
        for (EnergyElecFeeSchemeStructureMap structure : structureMaps) {
            if(structure.getResourceStructureId().equals(resourceId)) {
                ElecFeeScheme s = elecFeeSchemeHashMap.get(structure.getSchemeId());
                if(s != null && businessTypeId.equals(s.getBusinessTypeId())) {
                    findSchemes.add(s);
                }
            }
        }
        if(findSchemes.size() > 0) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(calcTime);
            int month = cal.get(Calendar.MONTH) + 1;
            long caclMilli = calcTime.getTime();
            for (ElecFeeScheme oneScheme : findSchemes) {
                if(oneScheme.getEnableStatusBool()) {
                    if(oneScheme.getEnableStatusBool() &&
                            month >= oneScheme.getStartMonth() && month <= oneScheme.getEndMonth()
                            && caclMilli >= oneScheme.getEnableDate().getTime() && caclMilli <= oneScheme.getDeactivateDate().getTime()) {
                        result = oneScheme;
                        break;
                    }
                }
            }
        }
        return result;
    }

    @Override
    /** 根据节点某时刻用量获取单价, 现情况下传入的resourceStructureId即为ResourceStructure表中主键(默认层级树主键) */
    public SchemeUnitPrice GetSchemeUnitPrice(Integer resourceStructureId, Date calcTime, double thisMonthSum, Integer businessTypeId) {
        SchemeUnitPrice result = new SchemeUnitPrice();
        result.setUnitPrice(-1);
        result.setSchemeId(-1);
        try {
            if(resourceStructureId == null || calcTime == null) {
                log.warn("GetSchemeUnitPrice: 入参为空");
                return result;
            }
            if(businessTypeId == null) {
                log.warn("GetSchemeUnitPrice: businessTypeId为空");
                return result;
            }
            String leveOfPath = null;
            ResourceStructure curResourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
            leveOfPath = curResourceStructure.getLevelOfPath();

            if(StringUtils.isBlank(leveOfPath)) {
                log.warn("resourceStructureId=" + resourceStructureId + "的层级节点LevelOfPath值为空");
            } else {
                List<String> parentLst = StringUtils.splitToList(".", leveOfPath);
                ElecFeeScheme findScheme = null;
                //20220519: 修定为不上溯父节点的方案
                int ii=parentLst.size() - 1;
                findScheme = tryToGetScheme(Integer.parseInt(parentLst.get(ii)), calcTime, businessTypeId);

                if(findScheme == null) {
                    log.warn("resourceStructureId=" + resourceStructureId + "未查找到有效方案，businessTypeId=" + businessTypeId);
                } else {
                    Integer spId = null; //命中的阶梯id
                    List<ElecFeeStepPrice> stepPrices = findScheme.getStepPrices();
                    if(stepPrices == null || stepPrices.size() < 1) {
                        log.warn("resourceStructureId=" + resourceStructureId + ", schemeId=" + findScheme.getSchemeId() + ", 阶梯定价集合为空, " +
                                "calcTime=" + DateUtil.dateToString(calcTime) + ", thisMonthSum=" + thisMonthSum + ", businessTypeId=" + businessTypeId);
                    } else {
                        List<ElecFeeStepPrice> stepPricesSorted = stepPrices.stream().sorted(Comparator.comparing(ElecFeeStepPrice::getUpperLimit, Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.toList());
                        for (int i = 0; i < stepPricesSorted.size(); i++) {
                            ElecFeeStepPrice sp = stepPricesSorted.get(i);
                            if(thisMonthSum <= sp.getUpperLimit()) {
                                spId = sp.getStepId();
                                break;
                            }
                        }
                        if(spId == null) {//没有匹配的阶梯用量
                            log.warn("resourceStructureId=" + resourceStructureId + ", schemeId=" + findScheme.getSchemeId() + ", 方案中未命中任何有效阶梯单价, " +
                                    "calcTime=" + DateUtil.dateToString(calcTime) + ", thisMonthSum=" + thisMonthSum + ", businessTypeId=" + businessTypeId);
                        } else {
                            String dateStr = DateUtil.dateToString(calcTime);
                            Date timeInfo = DateUtil.stringToDate("2000-01-01 " + dateStr.substring(11));
                            long timeinfoMilli = timeInfo.getTime();
                            List<ElecFeeFpg> fpgs = findScheme.getFpgs();
                            ElecFeeFpg findFpg = null;
                            if(fpgs == null || fpgs.size() < 1) {
                                log.warn("resourceStructureId=" + resourceStructureId + ", schemeId=" + findScheme.getSchemeId() + ", 峰平谷集合为空, " +
                                        "calcTime=" + DateUtil.dateToString(calcTime) + ", thisMonthSum=" + thisMonthSum + ", businessTypeId=" + businessTypeId);
                            } else {
                                for (ElecFeeFpg fpg : fpgs) {
                                    fpg.initEffectiveTimeData();
                                    if(timeinfoMilli >= fpg.getEffectiveStartMill()
                                            && timeinfoMilli <= fpg.getEffectiveEndMill()) {
                                        findFpg = fpg;
                                        break;
                                    }
                                }
                            }
                            if(findFpg == null) {
                                log.warn("resourceStructureId=" + resourceStructureId + ", schemeId=" + findScheme.getSchemeId()
                                        + ", 方案中未命中任何有效峰平谷时间段, " + ", stepPriceId=" + spId +
                                        ", calcTime=" + DateUtil.dateToString(calcTime) + ", thisMonthSum=" + thisMonthSum + ", businessTypeId=" + businessTypeId);
                            } else {
                                List<ElecFeeFpgValue> fpgValues = findFpg.getFpgValues();
                                if(fpgValues == null || fpgValues.size() < 1) {
                                    log.warn("resourceStructureId=" + resourceStructureId + ", schemeId=" + findScheme.getSchemeId() + ", 峰平谷value集合为空, " +
                                            "calcTime=" + DateUtil.dateToString(calcTime) + ", thisMonthSum=" + thisMonthSum +
                                            ", fpgId=" + findFpg.getFpgId() + ", stepPriceId=" + spId +
                                            ", time range: " + findFpg.getEffectiveStartStr() + " - " + findFpg.getEffectiveEndStr() + ", businessTypeId=" + businessTypeId);
                                } else {
                                    String fpgVleStr = null;
                                    for (ElecFeeFpgValue fpgValue : fpgValues) {
                                        if(fpgValue.getStepId().equals(spId)) {
                                            fpgVleStr = fpgValue.getFpgValue();
                                            break;
                                        }
                                    }
                                    if(StringUtils.isBlank(fpgVleStr)) {
                                        log.warn("resourceStructureId=" + resourceStructureId + ", schemeId=" + findScheme.getSchemeId()
                                                + ", fpgId=" + findFpg.getFpgId() + ", stepPriceId=" + spId + ", fpgValue值为空, " +
                                                "calcTime=" + DateUtil.dateToString(calcTime) + ", thisMonthSum=" + thisMonthSum + ", businessTypeId=" + businessTypeId);
                                    } else {
                                        result.setSchemeId(findScheme.getSchemeId());
                                        result.setUnitPrice(Double.parseDouble(fpgVleStr));
                                        result.setFpgDescKey(findFpg.getFpgDescKey());
                                        result.setFpgDesc(findFpg.getFpgDesc());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return result;
        } catch (Exception ex) {
            log.error("GetSchemeUnitPrice出现未知异常,resourceStructureId=" + resourceStructureId + ", businessTypeId=" + businessTypeId + ": ", ex);
            result = new SchemeUnitPrice();
            result.setUnitPrice(-2);
            result.setSchemeId(-2);
            return result;
        }
    }

    @Override
    public boolean hasEnabledScheme() {
        return elecFeeSchemeHashMap != null && !elecFeeSchemeHashMap.isEmpty();
    }
    //------  单价请求接口相关方法 end

    @Override
    public ResourceStructure getResourceStructuresByPersonId(Integer personId, Integer deep) {

        ResourceStructure completeTreeSource = resourceStructureManager.getRoot();//包含所有节点的完整树
        if(deep < 1) deep = 1;
        ResourceStructure targTree = copyTree(completeTreeSource, deep);
        return targTree;
    }

    private ResourceStructure copyTree(ResourceStructure sourceNode, int deep) {

        ResourceStructure targNode = new ResourceStructure();
        BeanUtil.copyProperties(sourceNode, targNode, "extendedField","children");

        deep -= 1;

        List<ResourceStructure> childen = new ArrayList<>();
        List<ResourceStructure> childenSource = sourceNode.getChildren();
        if(deep > 0 && childenSource.size() > 0) {
            for (ResourceStructure resourceStructure : childenSource) {
                ResourceStructure targNodeTmp = copyTree(resourceStructure, deep);
                childen.add(targNodeTmp);
            }
        }
        targNode.setChildren(childen);
        return targNode;
    }

    private ElecFeeScheme getSchemeWithStructureMaps(EnergyElecFeeScheme schemeDb) {
        List<ElecFeeScheme> schemeObjList = getSchemeObj(new ArrayList<EnergyElecFeeScheme>() {{
            add(schemeDb);
        }});
        ElecFeeScheme schemeObj = schemeObjList.get(0);
        List<EnergyElecFeeSchemeStructureMap> structureMaps = structureMapMapper.getBySchemeId(schemeObj.getSchemeId());
        List<ElecFeeStructure> sttms = new ArrayList<>();
        for (EnergyElecFeeSchemeStructureMap structureMap : structureMaps) {
            ElecFeeStructure structure = convertDBObjToElecFeeStructure(structureMap);
            sttms.add(structure);
        }
        schemeObj.setStructures(sttms);
        return schemeObj;
    }

    @Override
    public List<Integer> getAllStructureIdsHasScheme(Date startTime, Date endTime, Integer businessTypeId) {
        List<Integer> structureIdsHasScheme = new ArrayList<>();
        try {
            if(startTime != null && endTime != null && businessTypeId != null) {
                long sTime = startTime.getTime();
                long eTime = endTime.getTime();
                if(sTime > eTime) {//如果开始时间大于结束时间为异常
                    log.error("异常，开始时间(" + sTime + ")不能大于结束时间(" + eTime + ").");
                } else {
                    Map<Integer, Integer> map = new HashMap<>(); //使用map的key不可重复的特性去重

                    //开始月份
                    Calendar calStart = Calendar.getInstance();
                    calStart.setTime(startTime);
                    int monthStart = calStart.get(Calendar.MONTH) + 1;
                    //结束月份
                    Calendar calEnd = Calendar.getInstance();
                    calEnd.setTime(endTime);
                    int monthEnd = calEnd.get(Calendar.MONTH) + 1;

                    if(structureMaps != null && structureMaps.size() > 0) {
                        for (EnergyElecFeeSchemeStructureMap structureMap : structureMaps) {
                            ElecFeeScheme efs = elecFeeSchemeHashMap.get(structureMap.getSchemeId());
                            if(efs != null && businessTypeId.equals(efs.getBusinessTypeId())) {//需要符合业务类型
                                long millEnableDate = efs.getEnableDate().getTime();
                                long millDeactivateDate = efs.getDeactivateDate().getTime();
                                if((sTime >= millEnableDate && sTime <= millDeactivateDate)
                                        || (eTime >= millEnableDate && eTime <= millDeactivateDate)
                                        || (millEnableDate >= sTime && millEnableDate <= eTime)
                                        || (millDeactivateDate >= sTime && millDeactivateDate <= eTime)
                                ) {//必须在有效时间范围内
                                    if((monthStart >= efs.getStartMonth() && monthStart <= efs.getEndMonth())
                                            || (monthEnd >= efs.getStartMonth() && monthEnd <= efs.getEndMonth())
                                            || (efs.getStartMonth() >= monthStart && efs.getStartMonth() <= monthEnd)
                                            || (efs.getEndMonth() >= monthStart && efs.getEndMonth() <= monthEnd)
                                    ) {//且必须在有效月份区间内
                                        if(efs.getEnableStatusBool()) {
                                            map.put(structureMap.getResourceStructureId(), 1);
                                        }
                                    }
                                }

                            }
                        }
                    }
                    //存入list中
                    for (int key : map.keySet()){
                        structureIdsHasScheme.add(key);
                    }
                }
            }
        } catch (Exception ex) {
            log.error("getAllStructureIdsHasScheme(StartTime, EndTime) 出现未知异常：", ex);
        }
        return structureIdsHasScheme;
    }

    public List<Integer> getAllStructureIdsHasScheme() {
        return getAllStructureIdsHasScheme(getElecfeeBusinessTypeId());
    }

    @Override
    public List<Integer> getAllStructureIdsHasScheme(Integer businessTypeId) {
        List<Integer> structureIdsHasScheme = new ArrayList<>();
        if(businessTypeId == null) return structureIdsHasScheme;
        try {
            Map<Integer, Integer> map = new HashMap<>(); //使用map的key不可重复的特性去重
            if(structureMaps != null && structureMaps.size() > 0) {
                for (EnergyElecFeeSchemeStructureMap structureMap : structureMaps) {
                    ElecFeeScheme efs = elecFeeSchemeHashMap.get(structureMap.getSchemeId());
                    if(efs != null && businessTypeId.equals(efs.getBusinessTypeId())) {
                        if(efs.getEnableStatusBool()) {
                            map.put(structureMap.getResourceStructureId(), 1);
                        }
                    }
                }
            }
            //存入list中
            for (int key : map.keySet()){
                structureIdsHasScheme.add(key);
            }
        } catch (Exception ex) {
            log.error("getAllStructureIdsHasScheme() 出现未知异常：", ex);
        }
        return structureIdsHasScheme;
    }

    @Override
    public List<Object> getComplexindexbusinesstypeForEnergy() {
        QueryWrapper<ComplexIndexBusinessType> wrapper = new QueryWrapper<>();
        wrapper.eq("ParentId",1);
        List<ComplexIndexBusinessType> lstType = complexIndexBusinessTypeMapper.selectList(wrapper);
        List<Object> result = new ArrayList<>();
        //默认将碳添加到下拉框第一个
        String carbonNameTxt = localeMessageSourceUtil.getMessage("energy.displaytext.carbon");
        String finalCarbonNameTxt = StringUtils.isBlank(carbonNameTxt) ? "CO2" : carbonNameTxt;;
        result.add(new Object() { public Integer getTypeId() { return 0; } public String getTypeName() { return finalCarbonNameTxt; } public String getTypeUnit() { return "T"; } });
        if(CollectionUtils.isNotEmpty(lstType)) {
            for (ComplexIndexBusinessType oneType : lstType) {
                String unit = energyComplexIndexManager.getUnitByBusinessTypeId(oneType.getBusinessTypeId());
                String finalUnit = StringUtils.isBlank(unit) ? "" : unit.trim();
                result.add(new Object() { public Integer getTypeId() { return oneType.getBusinessTypeId(); } public String getTypeName() { return oneType.getBusinessTypeName(); } public String getTypeUnit() { return finalUnit; } });
            }
        }
        return result;
    }
    /** 得到此方案所对应业务类型的 单位 */
    private String getUnitBySchemeId(Integer schemeId) {
        try {
            ElecFeeScheme feeScheme = elecFeeSchemeHashMap.get(schemeId);
            Integer businessTypeId = -1;
            if(feeScheme == null) {
                EnergyElecFeeScheme bySchemeId = schemeMapper.findBySchemeId(schemeId);
                businessTypeId = bySchemeId == null || bySchemeId.getBusinessTypeId() == null ? -1 : bySchemeId.getBusinessTypeId();
            } else {
                businessTypeId = feeScheme.getBusinessTypeId();
                businessTypeId = businessTypeId == null ? -1 : businessTypeId;
            }
            String unit = businessTypeId == -1 ? "" : energyComplexIndexManager.getUnitByBusinessTypeId(businessTypeId);
            return StringUtils.isBlank(unit) ? "" : unit.trim();
        } catch (Exception e) {
            log.error("get scheme(" + schemeId +") unit exception:", e);
            return "";
        }
    }
    /** 得到电费的业务类型id */
    private Integer getElecfeeBusinessTypeId() {
        QueryWrapper<ComplexIndexBusinessType> wrapper = new QueryWrapper<>();
        wrapper.eq("Description",1); //Description字段等于1，表示此记录就是电能耗类型
        List<ComplexIndexBusinessType> lstType = complexIndexBusinessTypeMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(lstType)) {
            if(lstType.size() == 1) {
                return lstType.get(0).getBusinessTypeId();
            } else {
                log.warn("Description == 1 的ComplexIndexBusinessType记录有多条：" + lstType.size());
            }
        }
        return null;
    }
}
