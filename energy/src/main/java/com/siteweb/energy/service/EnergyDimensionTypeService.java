package com.siteweb.energy.service;

import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyDimensionType;

import java.util.List;

public interface EnergyDimensionTypeService {

    /** 新增一个维度类型 */
    ResultObject<String> addDimensionType(EnergyDimensionType newDimension);

    /** 批量删除维度类型 */
    ResultObject<String> delDimensionTypeByIds(List<Integer> ids);

    /** 更新一个维度类型 */
    ResultObject<String> updateDimensionType(EnergyDimensionType newDimension);

    /** 根据主键获取维度类型对象，传入-1时返回所有 */
    ResultObject<List<EnergyDimensionType>> getDimensionTypes(Integer dimensionTypeId);

    /** 得到所有的在用维度类型 */
    ResultObject<List<EnergyDimensionType>> getUsedDimensionTypes();
}
