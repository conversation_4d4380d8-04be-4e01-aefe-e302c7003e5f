package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.energy.dto.ComplexIndexClassificationListDTO;
import com.siteweb.energy.dto.MultiLevelObjectDTO;
import com.siteweb.energy.entity.EnergyComplexIndexClassificationMap;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyComplexIndexClassificationMapMapper;
import com.siteweb.energy.mapper.EnergyMultiLevelObjectMapper;
import com.siteweb.energy.service.EnergyComplexIndexClassificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyComplexIndexClassificationServiceImpl implements EnergyComplexIndexClassificationService {

    @Autowired
    private EnergyComplexIndexClassificationMapMapper energyComplexIndexClassificationMapMapper;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private EnergyMultiLevelObjectMapper energyMultiLevelObjectMapper;

    @Override
    public List<ComplexIndexClassificationListDTO> findComplexIndexByObjectId(String objectId, String objectTypeId) {
        try{
            if (objectId == null || objectTypeId == null)
                return new ArrayList<>();
            //更新下这张表（删除已经在层级上删除了的指标映射关系）
            energyComplexIndexClassificationMapMapper.updateTable(objectId,objectTypeId,2);
            return energyComplexIndexClassificationMapMapper.getComplexIndexClassificationInfoById(objectId, objectTypeId, 2);
        }catch (Exception e){
            log.error("EnergyComplexIndexClassificationServiceImpl - findComplexIndexByObjectId",e);
            return new ArrayList<>();
        }
    }

    @Override
    public Integer deleteComplexIndexClassificationMapByComplexIndexId(Integer complexIndexId) {
        return energyComplexIndexClassificationMapMapper.delete(new QueryWrapper<EnergyComplexIndexClassificationMap>().eq("complexIndexId", complexIndexId));
    }

    @Override
    public Integer addComplexIndexClassification(EnergyComplexIndexClassificationMap energyComplexIndexClassificationMap) {
        try{
            EnergyComplexIndexClassificationMap isExist = energyComplexIndexClassificationMapMapper.selectOne(new QueryWrapper<EnergyComplexIndexClassificationMap>().eq("complexIndexId", energyComplexIndexClassificationMap.getComplexIndexId()));
            if (isExist == null) {
                return energyComplexIndexClassificationMapMapper.insert(energyComplexIndexClassificationMap);
            } else {
                return energyComplexIndexClassificationMapMapper.update(energyComplexIndexClassificationMap, new QueryWrapper<EnergyComplexIndexClassificationMap>().eq("complexIndexId", energyComplexIndexClassificationMap.getComplexIndexId()));
            }
        }catch (Exception e){
            log.error("EnergyComplexIndexClassificationServiceImpl - addComplexIndexClassification",e);
            return 0;
        }
    }

    @Override
    public List<MultiLevelObjectDTO> getEnergyClassificationTree() {

        List<MultiLevelObjectDTO> tree = new ArrayList<>();
        List<MultiLevelObjectDTO> allList = energyMultiLevelObjectMapper.findAllLevelObjects();
        if(allList == null) return null;
        if(allList.size() < 1) return tree;
        // 集合转map
        final Map<Integer, MultiLevelObjectDTO> pkMap = allList.stream().collect(
                Collectors.toMap(MultiLevelObjectDTO::getObjectId, Function.identity()));
        // 创建树结构
        for (MultiLevelObjectDTO node : allList) {
            Integer parentObjectId = node.getParentObjectId();
            MultiLevelObjectDTO parent = parentObjectId == null ? null : pkMap.get(parentObjectId);
            if(parent == null) {// 如果 parent 找不到，认为是根节点
                tree.add(node);
            } else {
                parent.getChildren().add(node);
            }
        }
        return tree;
    }
}
