package com.siteweb.energy.service;

import com.siteweb.energy.dto.SichuanEnergyInvestSignalDIffMonthResult;
import com.siteweb.energy.dto.SichuanEnergyInvestSignalDIffYearResult;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;

import java.util.Date;
import java.util.List;

public interface EnergySichuanEnergyInvestReportService {
    List<ResourceStructure> getBuildingList();

    List<ResourceStructure> getRoomList(Integer buildingId);

    List<Equipment> getEquipmentList(Integer resourceStructureId);

    List<ConfigSignalItem> getEquipmentSignalList(Integer equipmentId);

    List<SichuanEnergyInvestSignalDIffYearResult> getSignalDifferenceYearReport(List<String> signalList, Date queryTime);

    List<SichuanEnergyInvestSignalDIffMonthResult> getSignalDifferenceMonthReport(List<String> signalList, Date queryTime);

    Object getHeadBoardPowerReport(Integer aEquipmentId, Integer bEquipmentId, Date queryTime);

}
