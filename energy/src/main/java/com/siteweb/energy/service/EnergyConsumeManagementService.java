package com.siteweb.energy.service;

import com.siteweb.energy.entity.EnergyConsumeConst;
import com.siteweb.energy.entity.EnergyConsumeData;
import com.siteweb.energy.dto.EnergyConsumeDataParamsDTO;
import com.siteweb.energy.dto.EnergyConsumeHistoryDTO;

import java.util.List;

public interface EnergyConsumeManagementService {

    EnergyConsumeConst findEnergyConsumeConst(String objectId, String objectTypeId);

    Integer saveEnergyConsumeConst(EnergyConsumeConst energyConsumeConst);

    List<EnergyConsumeData> findEnergyConsumeData(String objectId, String objectTypeId, Integer energyTypeId, Integer year);
    EnergyConsumeData findEnergyConsumeDataYoY(String objectId, String objectTypeId, Integer energyTypeId, Integer year ,Integer month);

    Integer[] findEnergyConsumeYears(String objectId, String objectTypeId, Integer energyTypeId);

    List<EnergyConsumeHistoryDTO> findEnergyConsumeHistoryData(String objectId, String objectTypeId, Integer energyTypeId, Integer year);

    Integer saveEnergyConsumeData(EnergyConsumeDataParamsDTO energyConsumeDataParamsDTO);

    Integer upDateEnergyConsumeData(EnergyConsumeData energyConsumeData);

    Integer deleteEnergyConsumeData(Integer id);


}
