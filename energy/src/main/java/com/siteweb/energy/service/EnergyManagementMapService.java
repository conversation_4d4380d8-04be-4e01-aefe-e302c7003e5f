package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyManagementMapInfo;
import com.siteweb.energy.dto.EnergyManagementStructure;
import com.siteweb.energy.entity.EnergyManagementMap;

import java.util.List;

public interface EnergyManagementMapService {
    List<EnergyManagementMap> findByEnergyId(Integer energyId);
    Boolean saveEnergyStructure(EnergyManagementStructure energyStructure);
    Boolean saveEnergyStructureMarker(EnergyManagementStructure energyStructure,Integer isMarker);
    List<EnergyManagementMapInfo> GetEnergyManagementMapList(Integer energyId);
}
