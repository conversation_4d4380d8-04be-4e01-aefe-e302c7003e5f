package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyGrowth;
import com.siteweb.energy.dto.EnergyPowerType;

import java.util.Date;
import java.util.List;

public interface EnergyGrowthService {
    String getHistoryComplexIndexSum(Date startTime, Date endTime, long complexIndexId,String timeType, boolean isFee);
    EnergyGrowth CalculationYOY(int businessTypeId, Date startTime, Date endTime, String resourceStructureId,String timeType);
    List<EnergyPowerType> findEnergyTypeByStructure(int businessTypeId, Date startTime, Date endTime, String resourceStructureId,String timeType, boolean isFee);
}
