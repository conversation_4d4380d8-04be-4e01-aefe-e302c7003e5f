package com.siteweb.energy.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.energy.entity.EnergyObjectMap;
import com.siteweb.energy.entity.EnergyStructure;
import com.siteweb.energy.mapper.EnergyObjectMapMapper;
import com.siteweb.energy.service.EnergyObjectMapService;
import com.siteweb.energy.service.EnergyStructureService;
import liquibase.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.siteweb.common.util.StringUtils.getIntegerListByString;

@Service
public class EnergyObjectMapServiceImpl implements EnergyObjectMapService {
    @Autowired
    EnergyObjectMapMapper mapper;
    @Autowired
    EnergyStructureService energyStructureService;
    @Override
    public List<EnergyObjectMap> findByDimensionTypeId(Integer dimensionTypeId) {
        return mapper.selectList(new QueryWrapper<EnergyObjectMap>().eq("DimensionTypeId", dimensionTypeId));
    }

    @Override
    public List<EnergyObjectMap> findByDimensionTypeIdAndParentObjectIdAndParentObjectIdType(
            Integer dimensionTypeId,int parentObjectId,int parentObjectIdType) {
        List<EnergyObjectMap> allMap = new ArrayList<>();

        //需要先判断下是否是进线
        List<EnergyObjectMap> allRoot = mapper.findRootInflowNodeByDimensionTypeId(dimensionTypeId);
        EnergyObjectMap oneRoot = allRoot.stream().filter(i->i.getObjectId().equals(parentObjectId) && i.getObjectIdType().equals(parentObjectIdType)).findFirst().orElse(null);
        //不是进线
        if (oneRoot == null){
            allMap = mapper.findAllLevel1ChildNodes(parentObjectId,parentObjectIdType,dimensionTypeId);
            List<Integer> integerList = new ArrayList<>();
            for(EnergyObjectMap oneMap : allMap){
                if (oneMap.getObjectIdType() == 2){
                    integerList.add(oneMap.getObjectId());
                }
            }
            if (integerList.size()>0){
                List<EnergyStructure> thisStructures = energyStructureService.findByStructureIdIn(integerList);
                Iterator<EnergyObjectMap> iterator = allMap.iterator();
                while (iterator.hasNext()) {
                    EnergyObjectMap map = iterator.next();
                    if (map.getObjectIdType() == 2) {
                        EnergyStructure str = thisStructures.stream().filter(i->i.getStructureId().equals(map.getObjectId())).findFirst().orElse(null);
                        if (str != null){
                            if (str.getAsRootInflowNode() != null &&  str.getAsRootInflowNode() == 1)
                                iterator.remove();  //
                        }
                    }
                }
            }
        } else {  //是进线
            allMap =  mapper.findRootNodeByDimensionTypeId(dimensionTypeId);
        }
        return allMap;
    }

}

