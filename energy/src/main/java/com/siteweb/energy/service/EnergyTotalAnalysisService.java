package com.siteweb.energy.service;

import com.siteweb.energy.dto.*;
import org.springframework.http.HttpStatus;

import java.util.Date;
import java.util.List;

public interface EnergyTotalAnalysisService {
    TotalComplexIndexDTO findTotalComplexIndex(Date startTime, Date endTime, Integer objectId);

    List<EnergyComparativeAnalysisDTO> findComparativeAnalysis(Date startTime, Date endTime, Integer objectId,Integer userId, String timeType);

    List<EnergyPercentageAnalysisDTO> findPercentageAnalysis(Date startTime, Date endTime, Integer objectId);

    EnergyTrendAnalysisDTO findTrendAnalysis(Date startTime, Date endTime, Integer objectId, Boolean isStandardCoal);

    List<EnergyPercentageAnalysisDTO> findChildNodePercentage(Date startTime, Date endTime, Integer objectId);

    EnergyPreAlarmRankDTO findPrealarmRank(Date startTime, Date endTime);

    EnergyRankComplexDTO findEnergyRankComplex(Date startTime, Date endTime, Integer objectId, Integer businessTypeId);

    List<EnergyRankListDTO> findEnergyRankList(Date startTime, Date endTime, Integer objectId, Integer businessTypeId);

    Double findPlanValueByEnergyTypeIdAndTime(Date startTime, Date endTime, Integer id, Integer object);

    List<EnergyComparativeAnalysisDTO> findUseEnergyComparedOption(UseEnergyComparedParamsDTO useEnergyComparedParamsDTO);

    List<EnergyPercentageAnalysisDTO> findUseEnergyPercentage(UseEnergyComparedParamsDTO useEnergyComparedParamsDTO);

    List<EnergyPercentageAnalysisDTO> findUseEnergyPercentageTemp(UseEnergyComparedParamsDTO useEnergyComparedParamsDTO);

    List<EnergyUseBarDTO> findUseEnergyRank(UseEnergyComparedParamsDTO useEnergyComparedParamsDTO);

    List<EnergyUseTrendResult> findUseEnergyTrend(UseEnergyComparedParamsDTO useEnergyComparedParamsDTO);

    EnergySavingDataDTO findSavingEnergyData(Date startTime, Date endTime, Integer objectId, Integer businessTypeId);

    List<EnergySavingListDTO> findSavingEnergyList(Date startTime, Date endTime, Integer objectId, Integer businessTypeId);

    List<EnergyDemandTrendDTO> findDemandTrend(Date startTime, Date endTime, Integer objectId, Integer businessTypeId);

    UseEnergyAndCarbonTotal findUseEnergyAndCarbonTotal(Date startTime, Date endTime, Integer objectId, Integer businessTypeId,Integer userId);

    Object findUseElectricityTrendAnalysis(Date startTime, Date endTime, Integer objectId, Integer businessTypeId, String timeType,Integer userId);


    List<EnergyComparativeAnalysisDTO> findObjectEnergyTotal(Date startTime, Date endTime, Integer objectId,Integer userId,String timeType);
}
