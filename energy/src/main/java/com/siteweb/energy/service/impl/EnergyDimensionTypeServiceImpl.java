package com.siteweb.energy.service.impl;

import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.service.ComplexIndexBusinessObjectMapService;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyDimensionType;
import com.siteweb.energy.entity.EnergyObjectMap;
import com.siteweb.energy.enumeration.SourceCategory;
import com.siteweb.energy.mapper.EnergyDimensionTypeMapper;
import com.siteweb.energy.mapper.EnergyObjectMapMapper;
import com.siteweb.energy.mapper.EnergyStructureMapper;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.service.EnergyDimensionTypeService;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyDimensionTypeServiceImpl implements EnergyDimensionTypeService {

    public static String CREATE_DEFAULT = "created by system default";//系统默认创建
    public static String CREATE_CUSTOM_NODE = "create custom node";//创建自定义节点时创建
    public static String USE_EXISTS_NODE = "use exists node";//引用已有节点时创建
    public static String USE_EXISTS_NODE_NAME = "just use exists node name";//仅复制已存在节点名称时创建

    @Autowired
    private EnergyDimensionTypeMapper dimensionTypeMapper;
    @Autowired
    private EnergyObjectMapMapper objectMapMapper;
    @Autowired
    private EnergyStructureMapper structureMapper;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyStructureService energyStructureService;
    @Autowired
    private ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;

    @Override
    public ResultObject<String> addDimensionType(EnergyDimensionType newDimension) {

        //1. 保存此维度类型
        if(newDimension.getDimensionTypeId() != null) {
            newDimension.setDimensionTypeId(null);
        }
        newDimension.setUpdateTime(new Date()); //更新时间采用服务器时间
        dimensionTypeMapper.insert(newDimension);
        if(newDimension.getDimensionTypeId() == null) {
            throw new RuntimeException("unknown exception: newDimensionTypeId is null.");
        }

        //2. 给这个新增的维度类型插入一棵只包含默认层级1根节点的树
        ResourceStructure rootNode = resourceStructureManager.getRoot();
        List<ResourceStructure> rootChildren = new ArrayList<>();
        if(rootNode == null || rootNode.getResourceStructureId() == null) {
            log.warn("can not find rootNode; do not create the default tree.");
            return new ResultObject<>("can not find rootNode");
        } else {
            //创建根节点
            EnergyObjectMap rootObj = new EnergyObjectMap();
            rootObj.setDimensionTypeId(newDimension.getDimensionTypeId());
            rootObj.setLevelId(1); //根节点，1级
            rootObj.setObjectId(rootNode.getResourceStructureId());
            rootObj.setObjectIdType(SourceCategory.RESOURCE_STRUCTURE.value()); //默认层级中的节点为1，自定义节点为2
            rootObj.setParentObjectId(0); //根节点的父节点为0
            rootObj.setParentObjectIdType(1); //与ObjectIdType字段意义相同
            rootObj.setLevelOfPath(EnergyResourceStructure.getStructureKeyByObjectIdAndObjectIdType(rootNode.getResourceStructureId(), SourceCategory.RESOURCE_STRUCTURE.value()));
            rootObj.setNotes(CREATE_DEFAULT);
            objectMapMapper.insert(rootObj);
        }
        //3. 返回添加成功结果
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> delDimensionTypeByIds(List<Integer> ids) {

        for (Integer id : ids) {
            EnergyDimensionType dimensionType = dimensionTypeMapper.selectById(id);
            if(dimensionType == null) {
                log.warn("DimensionTypeId=" + id + " 的多维度方案不存在");
                continue;
            }

            //1.删除维度类型记录
            dimensionTypeMapper.deleteById(id);

            //2.将自建的且没有被引用的节点删除，并删除其所对应的所有指标记录
            List<Integer> privateIds = objectMapMapper.getAllPrivateStructureIds(id, SourceCategory.ENERGY_RESOURCE.value()); //拿到只有本维度类型所引用的自定义节点id集合
            if(privateIds != null && privateIds.size() > 0) {
                for (Integer prvtId : privateIds) {
                    if(structureMapper.selectById(prvtId) != null) {
                        structureMapper.deleteById(prvtId); //删除此私有自定义节点
                    } else {
                        log.warn("自定义节点不存在，structureId=" + prvtId);
                    }
                    energyStructureService.batchDeleteComplexIndex(prvtId, EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID, null);
                }
            }

            //3.删除引用节点(类型为 设备(3)、机架(4)、IT设备(5))下的能耗相关指标
            List<EnergyObjectMap> mapList = objectMapMapper.getAllPrivateRefStructureIds(id, SourceCategory.EQUIPMENT.value()
                    , SourceCategory.COMPUTERRACK.value(), SourceCategory.ITDEVICE.value());
            if(mapList != null && mapList.size() > 0) {
                for (EnergyObjectMap oneMap : mapList) {
                    SourceType sourceType = SourceCategory.valueOf(oneMap.getObjectIdType()).toSourceType();//得到对应的SourceType，指标中存储的是SourceType对应的值
                    if(sourceType == null) {
                        throw new RuntimeException("SourceType(" + oneMap.getObjectIdType() + ") is NULL.");
                    }

                    List<Integer> energyBusinessTypeIds =  complexIndexBusinessTypeMapper.selectList(null).stream().filter(i->i.getParentId().equals(1) || i.getBusinessTypeId().equals(1))
                            .map(ComplexIndexBusinessType::getBusinessTypeId).collect(Collectors.toList());
                    energyStructureService.batchDeleteComplexIndex(oneMap.getObjectId(), sourceType.value(), energyBusinessTypeIds);
                }
            }

            //4.删除该维度树所有节点映射关系
            objectMapMapper.deleteByDimensionTypeId(id);
        }
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> updateDimensionType(EnergyDimensionType newDimension) {
        newDimension.setUpdateTime(new Date()); //更新时间采用服务器时间
        dimensionTypeMapper.updateById(newDimension);
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<List<EnergyDimensionType>> getDimensionTypes(Integer dimensionTypeId) {

        List<EnergyDimensionType> list = new ArrayList<>();
        if(dimensionTypeId == null || dimensionTypeId.equals(-1)) {
            list = dimensionTypeMapper.selectList(null);
        } else {
            EnergyDimensionType dimensionType = dimensionTypeMapper.findByDimensionTypeId(dimensionTypeId);
            list.add(dimensionType);
        }
        return new ResultObject<>(list);
    }

    @Override
    public ResultObject<List<EnergyDimensionType>> getUsedDimensionTypes() {
        List<EnergyDimensionType> list = dimensionTypeMapper.findByIsUsedIs(1);
        return new ResultObject<>(list);
    }

}

