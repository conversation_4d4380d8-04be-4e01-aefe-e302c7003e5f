package com.siteweb.energy.service;

import com.siteweb.energy.dto.*;
import com.siteweb.prealarm.entity.PreAlarmHistory;

import java.util.Date;
import java.util.List;

public interface EnergyConsumeAnalysisService {

    //需量分析
    List<EnergyPreAlarmDTO> findPreAlarmRank(Date startTime, Date endTime,Integer objectId,Integer objectTypeId,Integer businessTypeId, Integer isConsumeAlarm);
    List<PreAlarmHistory> findPreAlarmList(Date startTime, Date endTime,Integer objectId,Integer objectTypeId,Integer businessTypeId,Integer isConsumeAlarm);
    List<EnergyConsumeChildDTO> findChildConsumeTrend(Date startTime, Date endTime,Integer objectId,Integer objectTypeId,Integer businessTypeId);

    //峰平谷分析
    List<EnergyFpgValueDTO> findFpgValue(Date startTime, Date endTime,Integer objectId,Integer objectTypeId);
    List<EnergyChildFpgValueDTO> findChildFpgValue(Date startTime, Date endTime,Integer objectId,Integer objectTypeId);

//    TotalComplexIndexDTO findTotalComplexIndex(Date startTime, Date endTime, Integer objectId, Integer objectTypeId);
//
//    List<EnergyComparativeAnalysisDTO> findComparativeAnalysis(Date startTime, Date endTime, Integer objectId, Integer objectTypeId);
//
//    List<EnergyPercentageAnalysisDTO> findPercentageAnalysis(Date startTime, Date endTime, Integer objectId, Integer objectTypeId);
//
//    EnergyTrendAnalysisDTO findTrendAnalysis(Date startTime, Date endTime, Integer objectId, Integer objectTypeId, Boolean isStandardCoal);
//
//    List<EnergyPercentageAnalysisDTO> findChildNodePercentage(Date startTime, Date endTime, Integer objectId, Integer objectTypeId);


}
