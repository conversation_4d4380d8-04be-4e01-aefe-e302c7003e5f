package com.siteweb.energy.service;

import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.energy.dto.EnergyAppResultDTO;
import com.siteweb.energy.dto.EnergyComplexCheckDTO;
import com.siteweb.energy.dto.EnergyComplexTree;
import com.siteweb.energy.entity.EnergyErrorDataModifyRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface EnergyDataAuditService {

    Map<String, List<?>> getEnergyHisErrorData(Date startTime, Date endTime, String businessTypeId);

    Integer insertErrorDataModifyRecord(EnergyErrorDataModifyRecord energyErrorDataModifyRecord);

    List<EnergyErrorDataModifyRecord> getErrorDataModifyRecord(Integer complexIndexId, Date errorTime);

    List<EnergyComplexCheckDTO> getComplexNotExist(String objectIds);

    //指标表达式预警
    void EnergyComplexCheck();

    List<ComplexIndex> getComplexList(Integer objectId, Integer objectTypeId, String businessTypeId);

    EnergyComplexCheckDTO getComplexInfo(String complexIndexId);

    EnergyComplexTree getComplexTree(Integer complexIndexId, String complexIndexName, String expression);

    List<EnergyAppResultDTO> getcomplexListAndCurve(Date startTime, Date endTime, Integer complexIndexId);

    Map<String, Object> getSignalListAndCurve(Date startTime, Date endTime, Integer equipmentId, Integer signalId, String signalName);
}

