package com.siteweb.energy.service;

import com.siteweb.energy.entity.EnergyDataEntry;

import java.util.List;

public interface EnergyDataConfigService {
    List<EnergyDataEntry> getAllEnergyDataEntrys();

    EnergyDataEntry createEnergyDataEntry(EnergyDataEntry energyDataEntry);

    EnergyDataEntry updateEnergyDataEntry(EnergyDataEntry energyDataEntry);

    void deleteEnergyDataEntry(Integer id);
}
