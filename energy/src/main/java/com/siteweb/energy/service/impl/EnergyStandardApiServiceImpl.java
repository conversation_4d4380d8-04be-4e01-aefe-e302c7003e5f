package com.siteweb.energy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.dto.EnergyComplexIndexQueryResult;
import com.siteweb.complexindex.entity.*;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.service.BusinessDefinitionMapService;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.complexindex.service.ComplexIndexDefinitionService;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyCarbonManegePara;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyStandardApiMapper;
import com.siteweb.energy.service.*;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;
import static com.siteweb.common.util.DateUtil.getTodayStartTime;
import static org.influxdb.querybuilder.BuiltQuery.QueryBuilder.*;
import static org.influxdb.querybuilder.BuiltQuery.QueryBuilder.eq;

@Service
@Slf4j
public class EnergyStandardApiServiceImpl implements EnergyStandardApiService {

    @Autowired
    EnergyOverViewService energyOverViewService;

    @Autowired
    EnergyStandardApiMapper energyStandardApiMapper;

    @Autowired
    EnergyComplexIndexManager energyComplexIndexManager;

    @Autowired
    EnergyDataConfigItemService energyDataConfigItemService;

    @Autowired
    BusinessDefinitionMapService businessDefinitionMapService;

    @Autowired
    ResourceObjectManager resourceObjectManager;

    @Autowired
    ComplexIndexBusinessTypeService complexIndexBusinessTypeService;

    @Autowired
    ComplexIndexDefinitionService complexIndexDefinitionService;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    ComplexIndexService complexIndexService;


    @Autowired
    ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;
    @Autowired
    EnergyTotalAnalysisService energyTotalAnalysisService;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    EnergyConfigTelComService energyConfigTelComService;

    @Autowired
    public InfluxDB influxDB;

    @Value("${spring.influx.database3}")
    public String databaseEnergy;


    /**
     * 获取层级结构及其指标配置信息
     * @return
     */
    @Override
    public List<EnergySdkResourceComplexIndexDTO> getResourceStructureComplexIndexIds() {
        return energyStandardApiMapper.getAllObjectInfoAndComplexIndex();
    }
    /**
     * 用能总量同环比接口
     * @param businessTypeId 用能类型
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkTotalDefinitionYOYResult calculationEnergyYOY(Integer objectId, Integer objectTypeId, int businessTypeId, String timeType, Date startTime, Date endTime) {
        //同比时间计算
        List<Date> YoYDate = energyConfigTelComService.getQoQDateOrYoYDate(startTime,endTime,"YoY",timeType);

        //计算环比时间
        List<Date> QoqDate = energyConfigTelComService.getQoQDateOrYoYDate(startTime,endTime,"QoQ",timeType);

        EnergySdkTotalDefinitionYOYResult result = new EnergySdkTotalDefinitionYOYResult();
        try {
            //获取节点的现在用能总量
            EnergySdkTotalResult nowTotalResult = getTotalEnergyUse(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime);
            //获取节点的同比总量
            EnergySdkTotalResult historyYOYTotalResult = getTotalEnergyUse(objectId, objectTypeId, businessTypeId, timeType, YoYDate.get(0), YoYDate.get(1));
            //获取节点的环比总量
            EnergySdkTotalResult historyMOMTotalResult = getTotalEnergyUse(objectId, objectTypeId, businessTypeId, timeType, QoqDate.get(0), QoqDate.get(1));

            result.setResourceStructureName(nowTotalResult.getResourceStructureName() == null ? null : nowTotalResult.getResourceStructureName());
            result.setBusinessName(nowTotalResult.getBusinessTypeName() == null ? null : nowTotalResult.getBusinessTypeName());
            result.setBusinessYOYInfo(new ArrayList<>());
            //计算同比百分比（当年与去年）;计算环比百分比（当月与上月）
            for (EnergySdkTotalDefinition estd : nowTotalResult.getDefinitions()) {
                Integer complexIndexDefinitionId = estd.getComplexIndexDefinitionId();
                if (complexIndexDefinitionId == null)
                    continue;
                EnergySdkTotalDefinitionYOYDTO resultComplex = new EnergySdkTotalDefinitionYOYDTO();
                Double nowValue = estd.getValue();
                Double nowCoalValue = estd.getCoalValue();
                Double nowCarbonValue = estd.getCarbonValue();
                resultComplex.setComplexIndexDefinitionId(estd.getComplexIndexDefinitionId());
                resultComplex.setComplexIndexDefinitionName(estd.getComplexIndexDefinitionName());
                resultComplex.setValue(nowValue);
                resultComplex.setCoalValue(nowCoalValue);
                resultComplex.setCarbonValue(nowCarbonValue);
                //同比数据
                EnergySdkTotalDefinition historyYOYTemp = historyYOYTotalResult.getDefinitions().stream().filter(j -> j.getComplexIndexDefinitionId() != null && j.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)).findFirst().orElse(null);
                if (historyYOYTemp != null) {
                    Double historyYOYValue = historyYOYTemp.getValue();
                    Double historyYOYCarbonValue = historyYOYTemp.getCarbonValue() == null ? 0 : historyYOYTemp.getCarbonValue();
                    Double historyYOYCoalValue = historyYOYTemp.getCoalValue() == null ? 0 : historyYOYTemp.getCoalValue();
                    resultComplex.setYOYCarbonValue(historyYOYCarbonValue);
                    resultComplex.setYOYCoalValue(historyYOYCoalValue);
                    if (historyYOYValue == 0d) {
                        resultComplex.setYOYValue( 0d );
                        if (nowValue == 0d)
                            resultComplex.setYOYValuePercent(0d);
                        else
                            resultComplex.setYOYValuePercent(100d);
                    }else {
                        resultComplex.setYOYValue(historyYOYValue);
                        resultComplex.setYOYValuePercent(NumberUtil.doubleAccuracy(Math.abs(((nowValue - historyYOYValue) / nowValue)) * 100,2));
                    }
                }else{
                    resultComplex.setYOYValue( 0d );
                    resultComplex.setYOYCarbonValue(null);
                    resultComplex.setYOYCoalValue(null);
                    if (nowValue == 0)
                        resultComplex.setYOYValuePercent(0d);
                    else
                        resultComplex.setYOYValuePercent(100d);
                }
                //环比数据
                EnergySdkTotalDefinition historyMOMTemp = historyMOMTotalResult.getDefinitions().stream().filter(i -> i.getComplexIndexDefinitionId() != null && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)).findFirst().orElse(null);
                if (historyMOMTemp != null) {
                    Double historyMOMValue = historyMOMTemp.getValue();
                    Double historyMOMCoalValue = historyMOMTemp.getCoalValue();
                    Double historyMOMCarbonValue = historyMOMTemp.getCarbonValue();
                    resultComplex.setMOMCarbonValue(historyMOMCarbonValue);
                    resultComplex.setMOMCoalValue(historyMOMCoalValue);
                    if (historyMOMValue == 0d) {
                        resultComplex.setMOMValue( 0d );
                        if (nowValue == 0d)
                            resultComplex.setMOMValuePercent(0d);
                        else
                            resultComplex.setMOMValuePercent(100d);
                    }else {
                        resultComplex.setMOMValue(historyMOMValue);
                        resultComplex.setMOMValuePercent(NumberUtil.doubleAccuracy(Math.abs(((nowValue - historyMOMValue ) / nowValue)* 100),2));
                    }
                }else{
                    resultComplex.setMOMValue( 0d );
                    resultComplex.setMOMCarbonValue(null);
                    resultComplex.setMOMCoalValue(null);
                    if (nowValue == 0d)
                        resultComplex.setMOMValuePercent(0d);
                    else
                        resultComplex.setMOMValuePercent(100d);
                }
                result.getBusinessYOYInfo().add(resultComplex);
            }
        } catch (Exception e) {
            log.error("EnergyStandardApiServiceImpl-calculationEnergyYOY error ", e);
            return result;
        }
        return result;
    }
    /**
     * 绿能总量
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkGreenTotalResultDTO getGreenEnergyTotal(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime) {
        //获取该节点的所有绿能类型，然后从屈盘的用能总量获取该绿能类型的用能总量即可
        EnergySdkGreenTotalResultDTO result = new EnergySdkGreenTotalResultDTO();
        String resourceStructureName = energyStandardApiMapper.getResourceStructureNameById(objectId);
        if(resourceStructureName == null || resourceStructureName.equals(""))
            return result;
        result.setResourceStructureName(resourceStructureName);
        // 获取此节点的所有指标
        List<ComplexIndex> objectComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(objectId, objectTypeId);
        if (objectComplexIndex.isEmpty())
            return result;
        result.setTotalValue(0d);
        result.setCoalValue(0d);
        result.setCarbonValue(0d);
        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap = new ArrayList<>();
        //获取所有绿能类型
        List<BusinessDefinitionMap> temp = new ArrayList<>();
        List<ComplexIndexBusinessType> allBusinessType = energyStandardApiMapper.getAllNoGreenBusiness();
        for (ComplexIndexBusinessType cbt : allBusinessType) {
            if (Integer.parseInt(cbt.getDescription()) >= 100) {
                temp = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(cbt.getBusinessTypeId()) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
            }
            thisMap.addAll(temp);
        }
        if (thisMap.isEmpty()) return result;
        List<Integer> finalLstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).toList();
        filterComplexIndex = objectComplexIndex.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());
        if (filterComplexIndex.isEmpty()) return result;

        List<Integer> objectAllGreenBusinessTypeId = filterComplexIndex.stream().map(ComplexIndex::getBusinessTypeId).toList();
        if (objectAllGreenBusinessTypeId.isEmpty()) return result;
        //获取所有绿能类型的用能量
        EnergySdkTotalResult energyTotalResult;
        for (Integer businessTypeId : objectAllGreenBusinessTypeId) {
            energyTotalResult = getTotalEnergyUse(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime);
            List<EnergySdkTotalDefinition> allComplexIndexValueList = energyTotalResult.getDefinitions();
            if(!allComplexIndexValueList.isEmpty()){
                for (EnergySdkTotalDefinition estd : allComplexIndexValueList) {
                    result.setTotalValue(NumberUtil.doubleAccuracy(result.getTotalValue() + (estd.getValue() == null ? 0d : estd.getValue()),2));
                    result.setCoalValue(NumberUtil.doubleAccuracy(result.getCoalValue() + (estd.getCoalValue() == null ? 0d : estd.getCoalValue()),2));
                    result.setCarbonValue(NumberUtil.doubleAccuracy(result.getCarbonValue() + (estd.getCarbonValue() == null ? 0d : estd.getCarbonValue()),2));
                }
            }
        }
        return result;
    }
    /**
     * 绿能趋势
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkGreenPowerTrendResultDTO getGreenEnergyTrend(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime) {

        EnergySdkGreenPowerTrendResultDTO result = new EnergySdkGreenPowerTrendResultDTO();
        String resourceStructureName = energyStandardApiMapper.getResourceStructureNameById(objectId);
        if (resourceStructureName == null || resourceStructureName.equals(""))
            return result;
        result.setResourceStructureName(resourceStructureName);
        result.setValueTrend(new ArrayList<EnergySdkTotalTrendData>());

        // 获取此节点的所有指标
        List<ComplexIndex> objectComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(objectId, objectTypeId);
        if (objectComplexIndex.isEmpty())
            return result;
        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap = new ArrayList<>();
        //获取所有绿能类型
        List<BusinessDefinitionMap> temp = new ArrayList<>();
        List<ComplexIndexBusinessType> allBusinessType = energyStandardApiMapper.getAllNoGreenBusiness();
        for (ComplexIndexBusinessType cbt : allBusinessType) {
            if (Integer.parseInt(cbt.getDescription()) >= 100) {
                temp = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(cbt.getBusinessTypeId()) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
            }
            thisMap.addAll(temp);
        }
        if (thisMap.isEmpty()) return result;

        List<Integer> finalLstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).toList();
        filterComplexIndex = objectComplexIndex.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());
        if (filterComplexIndex.isEmpty()) return result;

        List<Integer> objectAllGreenBusinessTypeId = filterComplexIndex.stream().map(ComplexIndex::getBusinessTypeId).toList();
        if (objectAllGreenBusinessTypeId.isEmpty()) return result;

        //获取所有绿能的用能趋势
        EnergySdkTotalTrendsResult energyTotalTrendResult;
        int count = 0;
        for (Integer businessTypeId : objectAllGreenBusinessTypeId) {
            energyTotalTrendResult = getTotalEnergyUseTrends(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime);
            List<EnergySdkTotalTrendsDefinition> allComplexIndexValueList = energyTotalTrendResult.getDefinitions();
            if(!allComplexIndexValueList.isEmpty()){
                //绿能只有一个总量
                List<EnergySdkTotalTrendData> energyData = allComplexIndexValueList.get(0).getData();
                if (!energyData.isEmpty()) {
                    if(count == 0){
                        result.getValueTrend().addAll(energyData);
                        count++;
                        continue;
                    }
                    for (EnergySdkTotalTrendData oneData : energyData) {
                        EnergySdkTotalTrendData tempData = result.getValueTrend().stream().filter(i->i.getTime().equals(oneData.getTime())).findFirst().orElse(null);
                        if(tempData == null) continue;
                        tempData.setValue(NumberUtil.doubleAccuracy(tempData.getValue() + (oneData.getValue() == null ? 0d : oneData.getValue()),2));
                        tempData.setCoalValue(NumberUtil.doubleAccuracy(tempData.getCoalValue() + (oneData.getCoalValue()  == null ? 0d : oneData.getCoalValue()),2));
                        tempData.setCarbonValue(NumberUtil.doubleAccuracy(tempData.getCarbonValue() + (oneData.getCarbonValue()  == null ? 0d : oneData.getCarbonValue()),2));
                    }
                }
            }
        }

        return result;
    }

    @Override
    public List<EnergySdkEnergyTypeDTO> getEnergyTypeList() {
        QueryWrapper<ComplexIndexBusinessType> wrapper = new QueryWrapper<>();
        wrapper.eq("ParentId",1);
        List<ComplexIndexBusinessType> lstType = complexIndexBusinessTypeMapper.selectList(wrapper);
        List<EnergySdkEnergyTypeDTO> res = new ArrayList<>();
        for (ComplexIndexBusinessType complexIndexBusinessType : lstType) {
            res.add(new EnergySdkEnergyTypeDTO(complexIndexBusinessType.getBusinessTypeId(),complexIndexBusinessType.getBusinessTypeName()));
        }
        return res;
    }

    /**
     * 绿能占比
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkGreenProportionResultDTO getGreenEnergyProportion(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkGreenProportionResultDTO result = new EnergySdkGreenProportionResultDTO();
        String resourceStructureName = energyStandardApiMapper.getResourceStructureNameById(objectId);
        if (resourceStructureName == null || resourceStructureName.equals(""))
            return result;


        result.setResourceStructureName(resourceStructureName);
        result.setGreenPowerCoalValue(0d);
        result.setOtherPowerCoalValue(0d);
        result.setGreenValuePercent(0d);

        // 获取此节点的所有指标
        List<ComplexIndex> objectComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(objectId, objectTypeId);
        if (objectComplexIndex.isEmpty())
            return result;


        //绿能总量
        EnergySdkGreenTotalResultDTO greenEnergyTotal = getGreenEnergyTotal(objectId, objectTypeId, timeType, startTime, endTime);
        EnergySdkGreenTotalResultDTO noGreenEnergyTotal = new EnergySdkGreenTotalResultDTO();
        noGreenEnergyTotal.setTotalValue(0d);
        noGreenEnergyTotal.setCoalValue(0d);
        noGreenEnergyTotal.setCarbonValue(0d);


        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap = new ArrayList<>();
        //获取所有非绿能类型
        List<BusinessDefinitionMap> temp = new ArrayList<>();
        List<ComplexIndexBusinessType> allBusinessType = energyStandardApiMapper.getAllNoGreenBusiness();
        for (ComplexIndexBusinessType cbt : allBusinessType) {
            if (Integer.parseInt(cbt.getDescription()) < 100) {
                temp = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(cbt.getBusinessTypeId()) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
            }
            thisMap.addAll(temp);
        }
        if (thisMap.isEmpty()) return result;
        List<Integer> finalLstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).toList();
        filterComplexIndex = objectComplexIndex.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());
        if (filterComplexIndex.isEmpty()) return result;


        List<Integer> objectAllGreenBusinessTypeId = filterComplexIndex.stream().map(ComplexIndex::getBusinessTypeId).toList();
        if (objectAllGreenBusinessTypeId.isEmpty()) return result;
        //获取所有非绿能类型的用能量
        EnergySdkTotalResult energyTotalResult;
        for (Integer businessTypeId : objectAllGreenBusinessTypeId) {
            energyTotalResult = getTotalEnergyUse(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime);
            List<EnergySdkTotalDefinition> allComplexIndexValueList = energyTotalResult.getDefinitions();
            if (!allComplexIndexValueList.isEmpty()) {
                for (EnergySdkTotalDefinition estd : allComplexIndexValueList) {
                    noGreenEnergyTotal.setTotalValue(noGreenEnergyTotal.getTotalValue() + (estd.getValue() == null ? 0d : estd.getValue()));
                    noGreenEnergyTotal.setCoalValue(noGreenEnergyTotal.getCoalValue() + (estd.getCoalValue() == null ? 0d : estd.getCoalValue()));
                    noGreenEnergyTotal.setCarbonValue(noGreenEnergyTotal.getCarbonValue() + (estd.getCarbonValue() == null ? 0d : estd.getCarbonValue()));
                }
            }
        }
        result.setGreenPowerCoalValue(NumberUtil.doubleAccuracy(greenEnergyTotal.getCoalValue(), 2));
        result.setOtherPowerCoalValue(NumberUtil.doubleAccuracy(noGreenEnergyTotal.getCoalValue(), 2));
        if (result.getGreenPowerCoalValue() == 0) {
            result.setGreenValuePercent(0d);
        } else {
            if (result.getOtherPowerCoalValue() == 0)
                result.setGreenValuePercent(100d);
            else
                result.setGreenValuePercent(NumberUtil.doubleAccuracy((result.getGreenPowerCoalValue() / (result.getGreenPowerCoalValue() + result.getOtherPowerCoalValue())) * 100, 2));
        }

        return result;
    }


    /**
     * 获取碳排放总量
     * @param businessTypeId 用能类型，值为-1时返回所有非绿能的碳排放总量
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkCarbonTotalResultDTO getTotalCarbonEmissions(int businessTypeId, Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkCarbonTotalResultDTO result = new EnergySdkCarbonTotalResultDTO();
        String resourceStructureName = energyStandardApiMapper.getResourceStructureNameById(objectId);
        if(resourceStructureName == null || resourceStructureName.equals(""))
            return result;
        result.setResourceStructureName(resourceStructureName);
        result.setCarbonTotalValue(0d);
        result.setBusinessInfo(new ArrayList<EnergySdkBusinessInfoDTO>());
        // 获取此节点的所有指标
        List<ComplexIndex> objectComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(objectId,objectTypeId);
        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap = new ArrayList<>();
        //所有业务信息
        List<ComplexIndexBusinessType> allBusinessType = energyStandardApiMapper.getAllNoGreenBusiness();
        Map<Integer, String> businessTypeMap = allBusinessType.stream()
                .collect(Collectors.toMap(ComplexIndexBusinessType::getBusinessTypeId,
                        ComplexIndexBusinessType::getBusinessTypeName));
        //获取所有非绿能类型的总量指标
        if(businessTypeId == -1){
            List<BusinessDefinitionMap> temp = new ArrayList<>();
            for (ComplexIndexBusinessType cbt: allBusinessType) {
                if(Integer.parseInt(cbt.getDescription()) < 100){
                    temp = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getBusinessTypeId().equals(cbt.getBusinessTypeId()) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
                }
                thisMap.addAll(temp);
            }
        }
        else {
            thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
        }
        if (thisMap.isEmpty()) return result;
        List<Integer> finalLstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).toList();
        filterComplexIndex = objectComplexIndex.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());
        //得到所有要查询的用能类型
        List<Integer> objectAllGreenBusinessTypeId = filterComplexIndex.stream().map(ComplexIndex::getBusinessTypeId).toList();
        if(objectAllGreenBusinessTypeId.isEmpty())
            return result;
        for (Integer oneBusinessTypeId : objectAllGreenBusinessTypeId) {
            EnergySdkTotalResult energyTotalResult = getTotalEnergyUse(objectId, objectTypeId, oneBusinessTypeId, timeType, startTime, endTime);
            List<EnergySdkTotalDefinition> allComplexIndexValueList = energyTotalResult.getDefinitions();
            if (!allComplexIndexValueList.isEmpty()) {
                for (EnergySdkTotalDefinition oneDefinition : allComplexIndexValueList) {
                    result.setCarbonTotalValue(result.getCarbonTotalValue() + (oneDefinition.getCarbonValue() == null ? 0 : oneDefinition.getCarbonValue()));
                    EnergySdkBusinessInfoDTO temp = new EnergySdkBusinessInfoDTO();
                    temp.setTotalValue(oneDefinition.getValue() == null ? 0 : oneDefinition.getValue());
                    temp.setCarBonValue(oneDefinition.getCarbonValue() == null ? 0 : oneDefinition.getCarbonValue());
                    temp.setBusinessTypeName(businessTypeMap.get(oneBusinessTypeId));
                    result.getBusinessInfo().add(temp);
                }
            }
        }
        return result;
    }

    /**
     * 碳排放趋势
     * @param businessTypeId 用能类型，值为-1时返回所有非绿能的碳排放总量
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkCarbonEmissionTrendResultDTO getCarbonTrend(int businessTypeId, Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkCarbonEmissionTrendResultDTO result = new EnergySdkCarbonEmissionTrendResultDTO();
        String resourceStructureName = energyStandardApiMapper.getResourceStructureNameById(objectId);
        if(resourceStructureName == null || resourceStructureName.equals(""))
            return result;
        result.setResourceStructureName(resourceStructureName);
        result.setValueTrend(new ArrayList<>());

        // 获取此节点的所有指标
        List<ComplexIndex> objectComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(objectId,objectTypeId);
        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap = new ArrayList<>();
        //所有业务信息
        List<ComplexIndexBusinessType> allBusinessType = energyStandardApiMapper.getAllNoGreenBusiness();
        Map<Integer, String> businessTypeMap = allBusinessType.stream()
                .collect(Collectors.toMap(ComplexIndexBusinessType::getBusinessTypeId,
                        ComplexIndexBusinessType::getBusinessTypeName));
        //获取所有非绿能类型的总量指标
        if(businessTypeId == -1){
            List<BusinessDefinitionMap> temp = new ArrayList<>();
            for (ComplexIndexBusinessType cbt: allBusinessType) {
                if(Integer.parseInt(cbt.getDescription()) < 100){
                    temp = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getBusinessTypeId().equals(cbt.getBusinessTypeId()) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
                }
                thisMap.addAll(temp);
            }
        }
        else {
            thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
        }
        if (thisMap.isEmpty()) return result;
        List<Integer> finalLstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).toList();
        filterComplexIndex = objectComplexIndex.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());
        //得到所有要查询的用能类型
        List<Integer> objectAllGreenBusinessTypeId = filterComplexIndex.stream().map(ComplexIndex::getBusinessTypeId).toList();
        if(objectAllGreenBusinessTypeId.isEmpty())
            return result;

        LocalDateTime startDateTime = LocalDateTime.ofInstant( Instant.ofEpochMilli(startTime.getTime()), ZoneId.systemDefault());
        LocalDateTime endDateTime = LocalDateTime.ofInstant( Instant.ofEpochMilli(endTime.getTime()), ZoneId.systemDefault());
        Calendar calendar = Calendar.getInstance();


        if(timeType.equals("y")){
            Date myStartTime =  startTime;
            Date myEndTime = DateUtil.getMonthEndTime(myStartTime);
            long monthsBetween = Math.abs(ChronoUnit.MONTHS.between(startDateTime, endDateTime)) + 1;
            for (int i = 0; i < monthsBetween; i++) {
                EnergySdkCarbonTrendData temp = new EnergySdkCarbonTrendData();
                temp.setCarbonTotalValue(0d);
                temp.setBusinessInfo(new ArrayList<>());
                for ( Integer oneBusinessTypeId: objectAllGreenBusinessTypeId) {
                    EnergySdkCarbonTotalResultDTO tempResult = getTotalCarbonEmissions(oneBusinessTypeId,objectId,objectTypeId,timeType,myStartTime,myEndTime);
                    if(tempResult == null)
                        continue;
                    temp.getBusinessInfo().addAll(tempResult.getBusinessInfo());
                    temp.setTime(DateUtil.dateToString(myStartTime));
                    temp.setCarbonTotalValue(NumberUtil.doubleAccuracy(temp.getCarbonTotalValue() + (tempResult.getCarbonTotalValue() == null ? 0d : tempResult.getCarbonTotalValue()),2));
                }
                result.getValueTrend().add(temp);
                myStartTime = DateUtil.getNextMonthFirstDay(myStartTime);
                myEndTime = DateUtil.getMonthEndTime(myStartTime);
            }
        }else if(timeType.equals("m") || timeType.equals("o")){
            Date myStartTime =  startTime;
            calendar.setTime(startTime);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date myEndTime = calendar.getTime();
            calendar.setTime(myEndTime);
            calendar.add(Calendar.MINUTE, -1);
            myEndTime = calendar.getTime();
            long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDateTime, endDateTime)) + 1;
            for (int i = 0; i < daysBetween ; i++) {
                EnergySdkCarbonTrendData temp = new EnergySdkCarbonTrendData();
                temp.setCarbonTotalValue(0d);
                temp.setBusinessInfo(new ArrayList<>());
                for ( Integer oneBusinessTypeId: objectAllGreenBusinessTypeId) {
                    EnergySdkCarbonTotalResultDTO tempResult = getTotalCarbonEmissions(oneBusinessTypeId,objectId,objectTypeId,timeType,myStartTime,myEndTime);
                    if(tempResult == null)
                        continue;
                    temp.getBusinessInfo().addAll(tempResult.getBusinessInfo());
                    temp.setTime(DateUtil.dateToString(myStartTime));
                    temp.setCarbonTotalValue(NumberUtil.doubleAccuracy(temp.getCarbonTotalValue() + (tempResult.getCarbonTotalValue() == null ? 0d : tempResult.getCarbonTotalValue()),2));
                }
                result.getValueTrend().add(temp);
                calendar.setTime(myStartTime);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                myStartTime = calendar.getTime();
                calendar.setTime(myStartTime);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                myEndTime = calendar.getTime();
                calendar.setTime(myEndTime);
                calendar.add(Calendar.MINUTE, -1);
                myEndTime = calendar.getTime();
            }
        }else if(timeType.equals("d")){
            Date myStartTime =  startTime;
            calendar.setTime(startTime);
            calendar.add(Calendar.HOUR_OF_DAY, 1);
            Date myEndTime = calendar.getTime();
            calendar.setTime(myEndTime);
            calendar.add(Calendar.MINUTE, -1);
            myEndTime = calendar.getTime();
            long hoursBetween = Math.abs(ChronoUnit.HOURS.between(startDateTime, endDateTime)) + 1;
            for (int i = 0; i < hoursBetween; i++) {
                EnergySdkCarbonTrendData temp = new EnergySdkCarbonTrendData();
                temp.setCarbonTotalValue(0d);
                temp.setBusinessInfo(new ArrayList<>());
                for ( Integer oneBusinessTypeId: objectAllGreenBusinessTypeId) {
                    EnergySdkCarbonTotalResultDTO tempResult = getTotalCarbonEmissions(oneBusinessTypeId,objectId,objectTypeId,timeType,myStartTime,myEndTime);
                    if(tempResult == null)
                        continue;
                    temp.getBusinessInfo().addAll(tempResult.getBusinessInfo());
                    temp.setTime(DateUtil.dateToString(myStartTime));
                    temp.setCarbonTotalValue(NumberUtil.doubleAccuracy(temp.getCarbonTotalValue() + (tempResult.getCarbonTotalValue() == null ? 0d : tempResult.getCarbonTotalValue()),2));
                }
                result.getValueTrend().add(temp);
                calendar.setTime(myStartTime);
                calendar.add(Calendar.HOUR_OF_DAY, 1);
                myStartTime = calendar.getTime();
                calendar.setTime(myStartTime);
                calendar.add(Calendar.HOUR_OF_DAY, 1);
                myEndTime = calendar.getTime();
                calendar.setTime(myEndTime);
                calendar.add(Calendar.MINUTE, -1);
                myEndTime = calendar.getTime();

            }
        }

        return result;
    }


    /**
     * 获取当前节点的碳排放进度
     * @param objectId 层级id
     * @param objectTypeId  层级类型
     * @param timeType  时间类型 y,m,d,o 年月日其他
     * @param startTime 开始时间
     * @param endTime  结束时间
     */
    @Override
    public EnergySdkCarbonEmissionProgressResultDTO getCarbonEmissionsProgress(Integer objectId, Integer objectTypeId, String timeType, Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        String year = sdf.format(startTime);
        EnergySdkCarbonEmissionProgressResultDTO result = new EnergySdkCarbonEmissionProgressResultDTO();
        String resourceStructureName = energyStandardApiMapper.getResourceStructureNameById(objectId);
        if(resourceStructureName == null || resourceStructureName.equals(""))
            return result;
        result.setResourceStructureName(resourceStructureName);
        result.setPlanCarbonValue(0d);
        result.setUsedCarbonValue(0d);
        result.setPercentValue(0d);
        //获取当前节点的碳配额值
        List<EnergyCarbonManegePara> carbonQuota = energyStandardApiMapper.getObjectCarbonQuotaByIdAndYear(objectId,objectTypeId,year);
        float carbonYearTotalPlan = 0;
        if(!carbonQuota.isEmpty()){
            carbonYearTotalPlan = carbonQuota.get(0).getYearPlanTotalValue() == null ? 0 : carbonQuota.get(0).getYearPlanTotalValue();
        }
        //获取碳排放总量
        EnergySdkCarbonTotalResultDTO energyCarbonTotal = getTotalCarbonEmissions(-1,objectId,objectTypeId,timeType,startTime,endTime);
        double carbonEmissionsTotal = energyCarbonTotal.getCarbonTotalValue();
        if(carbonYearTotalPlan == 0){
            result.setPercentValue(100d);
        }else {
            result.setPercentValue(NumberUtil.doubleAccuracy((carbonEmissionsTotal / carbonYearTotalPlan ) * 100, 2));
        }
        result.setUsedCarbonValue(NumberUtil.doubleAccuracy(carbonEmissionsTotal,2));
        result.setPlanCarbonValue((double) carbonYearTotalPlan);
        return result;
    }
    /**
     * 用能总量接口
     */
    @Override
    public EnergySdkTotalResult getTotalEnergyUse(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkTotalResult res = new EnergySdkTotalResult();
        List<EnergySdkTotalDefinition> definitions = new ArrayList<>();
        ResourceObjectEntity resourceStructures = getResourceStructures(objectId, objectTypeId);
        ComplexIndexBusinessType complexIndexBusinessType = getComplexIndexBusinessType(businessTypeId);
        EnergyDataItem complexIndexDefinitionType = getComplexIndexDefinitionType(10, 1);
        List<ComplexIndex> complexIndexs = getComplexIndex(objectId, objectTypeId, businessTypeId, 1);
        EnergyCoefficientDTO coefficient = getCoefficient(businessTypeId);
        for (ComplexIndex complexIndex : complexIndexs) {
            ComplexIndexDefinition complexIndexDefinition = complexIndexDefinitionService.findById(complexIndex.getComplexIndexDefinitionId());
            EnergySdkTotalDefinition temp = new EnergySdkTotalDefinition();
            Double influxdbResult = Double.valueOf(findInfluxdbResult(complexIndex.getComplexIndexId(), timeType, startTime, endTime).getResult());
            Double coalValue = influxdbResult * coefficient.getCoal();
            Double carbonValue = influxdbResult * coefficient.getCarbon();
            temp.setIndexId(complexIndex.getComplexIndexId());
            temp.setIndexName(complexIndex.getComplexIndexName());
            temp.setComplexIndexDefinitionId(complexIndex.getComplexIndexDefinitionId());
            temp.setComplexIndexDefinitionName(complexIndexDefinition.getComplexIndexDefinitionName());
            temp.setValue(NumberUtil.doubleAccuracy(influxdbResult, 2));
            temp.setCarbonValue(NumberUtil.doubleAccuracy(carbonValue, 2));
            temp.setCoalValue(NumberUtil.doubleAccuracy(coalValue, 2));
            definitions.add(temp);
        }
        res.setBusinessTypeId(businessTypeId);
        res.setBusinessTypeName(complexIndexBusinessType.getBusinessTypeName());
        res.setResourceStructureName(resourceStructures.getResourceName());
        res.setComplexIndexDefinitionTypeId(complexIndexDefinitionType.getEntryId());
        res.setComplexIndexDefinitionTypeName(complexIndexDefinitionType.getItemValue());
        res.setDefinitions(definitions);
        return res;

    }

    /**
     * 用能总量趋势接口
     */
    @Override
    public EnergySdkTotalTrendsResult getTotalEnergyUseTrends(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkTotalTrendsResult res = new EnergySdkTotalTrendsResult();
        List<EnergySdkTotalTrendsDefinition> definitions = new ArrayList<>();
        ResourceObjectEntity resourceStructures = getResourceStructures(objectId, objectTypeId);
        ComplexIndexBusinessType complexIndexBusinessType = getComplexIndexBusinessType(businessTypeId);
        EnergyDataItem complexIndexDefinitionType = getComplexIndexDefinitionType(10, 1);
        List<ComplexIndex> complexIndexs = getComplexIndex(objectId, objectTypeId, businessTypeId, 1);
        for (ComplexIndex complexIndex : complexIndexs) {
            ComplexIndexDefinition complexIndexDefinition = complexIndexDefinitionService.findById(complexIndex.getComplexIndexDefinitionId());
            EnergySdkTotalTrendsDefinition temp = new EnergySdkTotalTrendsDefinition();
            temp.setIndexId(complexIndex.getComplexIndexId());
            temp.setIndexName(complexIndex.getComplexIndexName());
            temp.setComplexIndexDefinitionId(complexIndex.getComplexIndexDefinitionId());
            temp.setComplexIndexDefinitionName(complexIndexDefinition.getComplexIndexDefinitionName());
            temp.setData(getEnergySdkTotalTrendData(complexIndex.getComplexIndexId(), businessTypeId, timeType, startTime, endTime));
            definitions.add(temp);
        }
        res.setBusinessTypeId(businessTypeId);
        res.setBusinessTypeName(complexIndexBusinessType.getBusinessTypeName());
        res.setResourceStructureName(resourceStructures.getResourceName());
        res.setComplexIndexDefinitionTypeId(complexIndexDefinitionType.getItemId());
        res.setComplexIndexDefinitionTypeName(complexIndexDefinitionType.getItemValue());
        res.setDefinitions(definitions);
        return res;
    }

    /**
     * 用能效率接口
     */
    @Override
    public EnergySdkBasicResult getEfficiencyEnergyUse(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkBasicResult basicResult = getBasicResult(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime, 2);
        return basicResult;
    }

    /**
     * 用能效率趋势接口
     */
    @Override
    public EnergySdkBasicTrendsResult getEfficiencyEnergyUseTrends(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkBasicTrendsResult basicTrendsResult = getBasicTrendsResult(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime, 2);
        return basicTrendsResult;
    }

    /**
     * 用能分项接口
     */
    @Override
    public EnergySdkBasicResult getOptionsEnergyUse(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkBasicResult basicResult = getBasicResult(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime, 3);
        return basicResult;
    }

    /**
     * 用能分项趋势接口
     */
    @Override
    public EnergySdkBasicTrendsResult getOptionsEnergyUseTrends(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        EnergySdkBasicTrendsResult basicTrendsResult = getBasicTrendsResult(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime, 3);
        return basicTrendsResult;
    }

    /**
     * 根据complexIndexDefinitionTypeId获取用能数据
     */
    @Override
    public EnergySdkBasicResult getEnergyUseByComplexIndexDefinitionTypeId(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer complexIndexDefinitionTypeId) {
        EnergySdkBasicResult basicResult = getBasicResult(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime, complexIndexDefinitionTypeId);
        return basicResult;
    }

    /**
     * 根据complexIndexDefinitionTypeId获取用能趋势数据
     */
    @Override
    public EnergySdkBasicTrendsResult getEnergyUseTrendByComplexIndexDefinitionTypeId(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer complexIndexDefinitionTypeId) {
        EnergySdkBasicTrendsResult basicTrendsResult = getBasicTrendsResult(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime, complexIndexDefinitionTypeId);
        return basicTrendsResult;
    }

    /**
     * 子节点用能分项接口
     */
    @Override
    public List<EnergySdkBasicResult> getOptionsEnergyUseChild(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        List<EnergySdkBasicResult> res = new ArrayList<>();
        ResourceObjectEntity resourceStructures = getResourceStructures(objectId, objectTypeId);
        List<ResourceStructure> resourceStructureParentList = getResourceStructureLstByParentId(resourceStructures.getResourceStructureId());
        for (ResourceStructure item : resourceStructureParentList) {
            res.add(getBasicResult(item.getResourceStructureId(),item.getStructureTypeId(),businessTypeId,timeType,startTime,endTime,3));
        }
        return res;
    }

    /**
     * 子节点用能分项趋势接口
     */
    @Override
    public List<EnergySdkBasicTrendsResult> getOptionsEnergyUseTrendsChild(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        List<EnergySdkBasicTrendsResult> res = new ArrayList<>();
        ResourceObjectEntity resourceStructures = getResourceStructures(objectId, objectTypeId);
        List<ResourceStructure> resourceStructureParentList = getResourceStructureLstByParentId(resourceStructures.getResourceStructureId());
        for (ResourceStructure item : resourceStructureParentList) {
            res.add(getBasicTrendsResult(item.getResourceStructureId(), item.getStructureTypeId(), businessTypeId, timeType, startTime, endTime, 3));
        }
        return res;
    }

    /**
     * 指标时间段总量接口
     */
    @Override
    public List<EnergySdkComplexIndexResult> getComplexIndexsValue(Integer[] complexIndexIds, String timeType, Date startTime, Date endTime) {
        List<EnergySdkComplexIndexResult> res = new ArrayList<>();
        List<ComplexIndex> complexIndexs = complexIndexService.findByComplexIndexIds(complexIndexIds);
        for (ComplexIndex item : complexIndexs) {
            EnergyComplexIndexQueryResult influxdbResult = findInfluxdbResult(item.getComplexIndexId(), timeType, startTime, endTime);
            EnergySdkComplexIndexResult temp = new EnergySdkComplexIndexResult();
            temp.setComplexIndexId(item.getComplexIndexId());
            temp.setValue(NumberUtil.doubleAccuracy(Double.valueOf(influxdbResult.getResult()),2));
            temp.setComplexIndexName(item.getComplexIndexName());
            res.add(temp);
        }
        return res;
    }

    /**
     * 指标时间段趋势接口
     */
    @Override
    public List<EnergySdkComplexIndexTrendsResult> getComplexIndexsValueTrends(Integer[] complexIndexIds, String timeType, Date startTime, Date endTime) {
        List<EnergySdkComplexIndexTrendsResult> res = new ArrayList<>();
        List<ComplexIndex> complexIndexs = complexIndexService.findByComplexIndexIds(complexIndexIds);
        for (ComplexIndex item : complexIndexs) {
            EnergySdkComplexIndexTrendsResult temp = new EnergySdkComplexIndexTrendsResult();
            temp.setComplexIndexId(item.getComplexIndexId());
            temp.setComplexIndexName(item.getComplexIndexName());
            temp.setValue(getEnergySdkBasicTrendData(item.getComplexIndexId(),timeType,startTime,endTime));
            res.add(temp);
        }
        return res;
    }


    /**
     * 根据objectId，objectTypeId，businessTypeId，complexIndexDefinitionTypeId 获取指标
     *
     * @param objectId                     层级id
     * @param objectTypeId                 层级类型
     * @param businessTypeId               能源类型
     * @param complexIndexDefinitionTypeId 1 总量 2 效率 3分项 4 空调节能 100 其他
     * @return ComplexIndex 指标
     */
    public List<ComplexIndex> getComplexIndex(Integer objectId, Integer objectTypeId, Integer businessTypeId, Integer complexIndexDefinitionTypeId) {
        List<Integer> complexIndexDefinitionIds = businessDefinitionMapService.getComplexIndexDefinitionIds(businessTypeId, complexIndexDefinitionTypeId);
        List<ComplexIndex> allComplexIndex = energyComplexIndexManager.GetAllComplexIndex();
        List<ComplexIndex> complexIndexOptional = allComplexIndex.stream().filter(item -> item.getObjectId().equals(objectId) && item.getObjectTypeId().equals(objectTypeId) && item.getBusinessTypeId().equals(businessTypeId) && complexIndexDefinitionIds.contains(item.getComplexIndexDefinitionId())).toList();
        return complexIndexOptional;
    }

    /**
     * 根据businessTypeId获取当前生效的标煤二氧化碳转换系数
     *
     * @param businessTypeId 能源类型
     * @return EnergyCoefficientDTO 标煤系数、二氧化碳系数
     */
    @Override
    public EnergyCoefficientDTO getCoefficient(Integer businessTypeId) {
        EnergyCoefficientDTO res = new EnergyCoefficientDTO();
        List<EnergyDataItem> energyDataItems = energyDataConfigItemService.getAllByEntryIdTimeliness(7);
        ComplexIndexBusinessType type = complexIndexBusinessTypeService.findById(businessTypeId);
        if (type.getDescription() == null || type.getDescription().trim().equals(""))
            return res;

        for (EnergyDataItem item : energyDataItems) {
            if (item.getItemId().toString().equals(type.getDescription().trim())) {
                res.setCarbon(ObjectUtil.isNull(item.getExtendField1()) ? 0d : Double.parseDouble(item.getExtendField1()));
                res.setCoal(ObjectUtil.isNull(item.getExtendField3()) ? 0d : Double.parseDouble(item.getExtendField3()));
            }
        }
        return res;
    }

    /**
     * 根据单个complexIndexId查询influxdb
     *
     * @param complexIndexId 指标id
     * @param timeType       时间类型
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @return 查询influxdb结果
     */
    public EnergyComplexIndexQueryResult findInfluxdbResult(Integer complexIndexId, String timeType, Date startTime, Date endTime) {
        EnergyComplexIndexQueryResult res = new EnergyComplexIndexQueryResult();
        res.setResult("0");
        res.setTime(startTime.toString());
        res.setComplexIndexId(String.valueOf(complexIndexId));
        String influxTable = "";
        if (timeType.equals("y")) {
            influxTable = "EnergyHisMonthData";
        } else if (timeType.equals("m") || timeType.equals("o")) {
            influxTable = "EnergyHisDayData";
        } else if (timeType.equals("d")) {
            influxTable = "EnergyHisHourData";
        }

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        Query queryBuilder = select().sum("IndexValue").as("result").from(databaseEnergy, influxTable).where(gte("time", DateUtil.dateToString(startTime))).and(lte("time", DateUtil.dateToString(endTime))).and(eq("ComplexIndexId", complexIndexId.toString())).and(eq("Abnormal", "0")).fill(0);

        query = influxDB.query(queryBuilder);
        if (query != null) {
            List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, influxTable);
            if (!resultComplexIndexQuery.isEmpty()) {
                return resultComplexIndexQuery.get(0);
            }
        }
        return res;
    }


    /**
     * 查询层级属性
     * @param objectId
     * @param objectTypeId
     * @return
     */
    @SneakyThrows
    public ResourceObjectEntity getResourceStructures(Integer objectId, Integer objectTypeId) {
        ResourceObject resourceObject = new ResourceObject(objectId, objectTypeId);
        ResourceObjectEntity res = resourceObjectManager.findEntityByObject(resourceObject);
        if (ObjectUtil.isNull(res)){
            throw new BusinessException("No ResourceStructures found");
        }
        return res;
    }


    /**
     * 查询子节点
     * @param objectId
     * @return
     */
    @SneakyThrows
    public List<ResourceStructure> getResourceStructureLstByParentId(Integer objectId) {

        return resourceStructureManager.getResourceStructureLstByParentId(objectId);
    }

    /**
     * 查询指标类型
     *
     * @param entryId
     * @param itemId
     * @return
     */
    public EnergyDataItem getComplexIndexDefinitionType(Integer entryId, Integer itemId) {
        EnergyDataItem energyDataItemTimeliness = energyDataConfigItemService.getEnergyDataItemTimeliness(entryId, itemId);
        return energyDataItemTimeliness;
    }

    public String timeStringFormat(String time) {
        for (String s : Arrays.asList("T", "Z")) {
            time = time.replace(s, " ");
        }
        return time;
    }

    public List<EnergySdkTotalTrendData> getEnergySdkTotalTrendData(Integer complexIndexId, Integer businessTypeId, String timeType, Date startTime, Date endTime) {
        String influxTable = "";
        String time = "";
        List<EnergySdkTotalTrendData> res = new ArrayList<>();
        EnergyCoefficientDTO coefficient = getCoefficient(businessTypeId);
        if (timeType.equals("m") || timeType.equals("d") || timeType.equals("o")) {
            if (timeType.equals("m") || timeType.equals("o")) {
                time = "d";
                influxTable = "EnergyHisDayData";
            } else if (timeType.equals("d")) {
                time = "h";
                influxTable = "EnergyHisHourData";
            }
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            Query queryBuilder = select().sum("IndexValue")
                    .as("result").from(databaseEnergy, influxTable)
                    .where(gte("time", DateUtil.dateToString(startTime)))
                    .and(lte("time", DateUtil.dateToString(endTime)))
                    .and(eq("ComplexIndexId", complexIndexId.toString()))
                    .and(eq("Abnormal", "0"))
                    .groupBy(time(1L, time))
                    .fill(0);

            query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, influxTable);
                for (EnergyComplexIndexQueryResult item : resultComplexIndexQuery) {
                    Double influxdbResult = Double.valueOf(item.result);
                    Double coalValue = influxdbResult * coefficient.getCoal();
                    Double carbonValue = influxdbResult * coefficient.getCarbon();
                    EnergySdkTotalTrendData temp = new EnergySdkTotalTrendData();
                    temp.setValue(NumberUtil.doubleAccuracy(influxdbResult, 2));
                    temp.setCoalValue(NumberUtil.doubleAccuracy(coalValue, 2));
                    temp.setCarbonValue(NumberUtil.doubleAccuracy(carbonValue, 2));
                    temp.setTime(timeStringFormat(item.getTime()));
                    res.add(temp);
                }

            }

        } else if (timeType.equals("y")) {
            List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = getUseEnergyTrendMonth(complexIndexId, startTime, endTime);
            for (EnergyComplexIndexQueryResult item : resultComplexIndexQuery) {
                Double influxdbResult = Double.valueOf(item.result);
                Double coalValue = influxdbResult * coefficient.getCoal();
                Double carbonValue = influxdbResult * coefficient.getCarbon();
                EnergySdkTotalTrendData temp = new EnergySdkTotalTrendData();
                temp.setValue(NumberUtil.doubleAccuracy(influxdbResult, 2));
                temp.setCoalValue(NumberUtil.doubleAccuracy(coalValue, 2));
                temp.setCarbonValue(NumberUtil.doubleAccuracy(carbonValue, 2));
                temp.setTime(timeStringFormat(item.getTime()));
                res.add(temp);
            }
        }
        return res;
    }

    public List<EnergySdkBasicTrendData> getEnergySdkBasicTrendData(Integer complexIndexId, String timeType, Date startTime, Date endTime) {
        String influxTable = "";
        String time = "";
        List<EnergySdkBasicTrendData> res = new ArrayList<>();
        if (timeType.equals("m") || timeType.equals("d")|| timeType.equals("o")) {
            if (timeType.equals("m")|| timeType.equals("o")) {
                time = "d";
                influxTable = "EnergyHisDayData";
            } else if (timeType.equals("d")) {
                time = "h";
                influxTable = "EnergyHisHourData";
            }
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            Query queryBuilder = select().sum("IndexValue")
                    .as("result").from(databaseEnergy, influxTable)
                    .where(gte("time", DateUtil.dateToString(startTime)))
                    .and(lte("time", DateUtil.dateToString(endTime)))
                    .and(eq("ComplexIndexId", complexIndexId.toString()))
                    .and(eq("Abnormal", "0"))
                    .groupBy(time(1L, time))
                    .fill(0);

            query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyComplexIndexQueryResult.class, influxTable);
                for (EnergyComplexIndexQueryResult item : resultComplexIndexQuery) {
                    Double influxdbResult = Double.valueOf(item.result);
                    EnergySdkBasicTrendData temp = new EnergySdkBasicTrendData();
                    temp.setValue(NumberUtil.doubleAccuracy(influxdbResult, 2));
                    temp.setTime(timeStringFormat(item.getTime()));
                    res.add(temp);
                }

            }

        } else if (timeType.equals("y")) {
            List<EnergyComplexIndexQueryResult> resultComplexIndexQuery = getUseEnergyTrendMonth(complexIndexId, startTime, endTime);
            for (EnergyComplexIndexQueryResult item : resultComplexIndexQuery) {
                Double influxdbResult = Double.valueOf(item.result);
                EnergySdkBasicTrendData temp = new EnergySdkBasicTrendData();
                temp.setValue(NumberUtil.doubleAccuracy(influxdbResult, 2));
                temp.setTime(timeStringFormat(item.getTime()));
                res.add(temp);
            }
        }
        return res;
    }

    private List<EnergyComplexIndexQueryResult> getUseEnergyTrendMonth(Integer complexIndexId, Date startTime, Date endTime) {
        List<EnergyComplexIndexQueryResult> result = new ArrayList<>();
        Date startTimeMonthOfFirstDay = DateUtil.getFirstDayOfMonth(startTime);//开始时间当月第一天
        Date startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTime);//开始时间当月最后一天
        EnergyComplexIndexQueryResult temp = findInfluxdbResult(complexIndexId, "y", startTime, startTimeMonthOfLastDay);
        if (ObjectUtil.isNotNull(temp)) {
            result.add(temp);
        }
        startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTime);
        while (startTimeMonthOfFirstDay != null && !startTimeMonthOfFirstDay.after(endTime)) {//一个月一个月查询,结束条件月开始时间大于结束时间
            startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTimeMonthOfFirstDay);
            temp = findInfluxdbResult(complexIndexId, "y", startTimeMonthOfFirstDay, startTimeMonthOfLastDay);
            if (ObjectUtil.isNotNull(temp)) {
                result.add(temp);
            }
            startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTimeMonthOfFirstDay);
        }

        return result;
    }


    public EnergySdkBasicResult getBasicResult(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer definitionType) {
        EnergySdkBasicResult res = new EnergySdkBasicResult();
        List<EnergySdkBasicDefinition> definitions = new ArrayList<>();
        ResourceObjectEntity resourceStructures = getResourceStructures(objectId, objectTypeId);
        ComplexIndexBusinessType complexIndexBusinessType = getComplexIndexBusinessType(businessTypeId);
        EnergyDataItem complexIndexDefinitionType = getComplexIndexDefinitionType(10, definitionType);
        List<ComplexIndex> complexIndexs = getComplexIndex(objectId, objectTypeId, businessTypeId, definitionType);
        for (ComplexIndex complexIndex : complexIndexs) {
            ComplexIndexDefinition complexIndexDefinition = complexIndexDefinitionService.findById(complexIndex.getComplexIndexDefinitionId());
            EnergySdkBasicDefinition temp = new EnergySdkBasicDefinition();
            Double influxdbResult = Double.valueOf(findInfluxdbResult(complexIndex.getComplexIndexId(), timeType, startTime, endTime).getResult());
            temp.setIndexId(complexIndex.getComplexIndexId());
            temp.setIndexName(complexIndex.getComplexIndexName());
            temp.setComplexIndexDefinitionId(complexIndex.getComplexIndexDefinitionId());
            temp.setComplexIndexDefinitionName(complexIndexDefinition.getComplexIndexDefinitionName());
            temp.setValue(NumberUtil.doubleAccuracy(influxdbResult, 2));
            definitions.add(temp);
        }
        res.setBusinessTypeId(businessTypeId);
        res.setBusinessTypeName(complexIndexBusinessType.getBusinessTypeName());
        res.setResourceStructureName(resourceStructures.getResourceName());
        res.setComplexIndexDefinitionTypeId(complexIndexDefinitionType.getItemId());
        res.setComplexIndexDefinitionTypeName(complexIndexDefinitionType.getItemValue());
        res.setDefinitions(definitions);
        return res;

    }

    private EnergySdkBasicTrendsResult getBasicTrendsResult(Integer objectId, Integer objectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer definitionType) {
        EnergySdkBasicTrendsResult res = new EnergySdkBasicTrendsResult();
        List<EnergySdkBasicTrendsDefinition> definitions = new ArrayList<>();
        ResourceObjectEntity resourceStructures = getResourceStructures(objectId, objectTypeId);
        ComplexIndexBusinessType complexIndexBusinessType = getComplexIndexBusinessType(businessTypeId);
        EnergyDataItem complexIndexDefinitionType = getComplexIndexDefinitionType(10, definitionType);
        List<ComplexIndex> complexIndexs = getComplexIndex(objectId, objectTypeId, businessTypeId, definitionType);
        for (ComplexIndex complexIndex : complexIndexs) {
            ComplexIndexDefinition complexIndexDefinition = complexIndexDefinitionService.findById(complexIndex.getComplexIndexDefinitionId());
            EnergySdkBasicTrendsDefinition temp = new EnergySdkBasicTrendsDefinition();
            temp.setIndexId(complexIndex.getComplexIndexId());
            temp.setIndexName(complexIndex.getComplexIndexName());
            temp.setComplexIndexDefinitionId(complexIndex.getComplexIndexDefinitionId());
            temp.setComplexIndexDefinitionName(complexIndexDefinition.getComplexIndexDefinitionName());
            temp.setData(getEnergySdkBasicTrendData(complexIndex.getComplexIndexId(), timeType, startTime, endTime));
            definitions.add(temp);
        }
        res.setBusinessTypeId(businessTypeId);
        res.setBusinessTypeName(complexIndexBusinessType.getBusinessTypeName());
        res.setResourceStructureName(resourceStructures.getResourceName());
        res.setComplexIndexDefinitionTypeId(complexIndexDefinitionType.getItemId());
        res.setComplexIndexDefinitionTypeName(complexIndexDefinitionType.getItemValue());
        res.setDefinitions(definitions);
        return res;
    }

    private ComplexIndexBusinessType getComplexIndexBusinessType(Integer id){
        ComplexIndexBusinessType complexIndexBusinessType = complexIndexBusinessTypeService.findById(id);
        if (ObjectUtil.isNull(complexIndexBusinessType)){
            throw new BusinessException("No businessType found");
        }
        return complexIndexBusinessType;
    }


    @Override
    public Object getCarbonIntensityRatio(Integer objectId, Integer objectTypeId, String timeType, Date startTime) {

        CarbonIntensityRatioResult result = new CarbonIntensityRatioResult();
        Date endTime;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        //获取查询日期的年和月份
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        if(timeType.equals("m")){
            //获取月末的时间日期
            int maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            calendar.set(Calendar.DAY_OF_MONTH, maxDay);
        }else {
            //获取年末的时间日期
            int maxDayOfYear = calendar.getActualMaximum(Calendar.DAY_OF_YEAR);
            calendar.set(Calendar.DAY_OF_YEAR, maxDayOfYear);
        }
        endTime = calendar.getTime();

        //获取该节点下所有非绿能的总用量转碳
        EnergySdkCarbonTotalResultDTO tempResult = getTotalCarbonEmissions(-1,objectId,objectTypeId,timeType,startTime,endTime);
        Double configurationResult ;
        //获取该节点配置的碳强度数据
        if(timeType.equals("m")){
            configurationResult  = energyStandardApiMapper.getObjectCarbonEmissionByIdAndYearAndMonth(objectId,objectTypeId, String.valueOf(year),String.valueOf(month));
        }else {
            configurationResult = energyStandardApiMapper.getObjectCarbonEmissionByIdAndYear(objectId,objectTypeId, String.valueOf(year));
        }

        if(configurationResult == null){
            configurationResult = 0D;
        }

        if(configurationResult == 0 ){
            result.setRatio(100D);
        }
        else {
            result.setRatio(NumberUtil.doubleAccuracy(((tempResult.getCarbonTotalValue() == null ? 0 : ((tempResult.getCarbonTotalValue())/configurationResult) * 100)),2));
        }
        result.setCarbonTotalAmount(tempResult.getCarbonTotalValue() == null ? 0 : tempResult.getCarbonTotalValue());
        result.setCarbonConfigAmount(configurationResult);
        return result;
    }

    @Override
    public Object getEveryCarbonIntensityRatio(Integer objectId, Integer objectTypeId, String timeType, Date startTime) {
        HashMap<String,Object> result = new HashMap<>();
        Date endTime;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        //获取查询日期的年和月份
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        Double configurationResult ;
        //获取该节点配置的碳强度数据
        if(timeType.equals("m")){
            configurationResult  = energyStandardApiMapper.getObjectCarbonEmissionByIdAndYearAndMonth(objectId,objectTypeId, String.valueOf(year),String.valueOf(month));
        }else {
            configurationResult = energyStandardApiMapper.getObjectCarbonEmissionByIdAndYear(objectId,objectTypeId, String.valueOf(year));
        }

        if(timeType.equals("m")){
            //获取月末的时间日期
            int maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            calendar.set(Calendar.DAY_OF_MONTH, maxDay);
        }else {
            //获取年末的时间日期
            int maxDayOfYear = calendar.getActualMaximum(Calendar.DAY_OF_YEAR);
            calendar.set(Calendar.DAY_OF_YEAR, maxDayOfYear);
        }
        endTime = calendar.getTime();

        if(configurationResult == null){
            configurationResult = 0D;
        }
        result.put("carbonConfigAmount",configurationResult);
        result.put("eachEnergyCarbonRatio",new ArrayList<>());
        // 获取此节点的所有指标
        List<ComplexIndex> objectComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(objectId,objectTypeId);
        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap = new ArrayList<>();
        //所有业务信息
        List<ComplexIndexBusinessType> allBusinessType = energyStandardApiMapper.getAllNoGreenBusiness();
        //获取所有非绿能类型的总量指标
        List<BusinessDefinitionMap> temp = new ArrayList<>();
        for (ComplexIndexBusinessType cbt: allBusinessType) {
            if(Integer.parseInt(cbt.getDescription()) < 100){
                temp = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getBusinessTypeId().equals(cbt.getBusinessTypeId()) && i.getComplexIndexDefinitionTypeId().equals(1)).collect(Collectors.toList());
            }
            thisMap.addAll(temp);
        }
        if (thisMap.isEmpty()) return result;
        List<Integer> finalLstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).toList();
        filterComplexIndex = objectComplexIndex.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());
        //得到所有要查询的用能类型
        List<Integer> objectAllGreenBusinessTypeId = filterComplexIndex.stream().map(ComplexIndex::getBusinessTypeId).toList();
        if(objectAllGreenBusinessTypeId.isEmpty())
            return result;

        List<EachEnergyCarbonIntensityRatioResult> eachEnergyCarbonRatioList = new ArrayList<>();
        for ( Integer oneId : objectAllGreenBusinessTypeId) {
                EnergySdkTotalResult tempResult = getTotalEnergyUse(objectId, objectTypeId, oneId, timeType, startTime, endTime);
                if(tempResult != null){
                    EnergySdkTotalDefinition oneEnergyInfo = tempResult.getDefinitions().get(0);
                    if(oneEnergyInfo != null){
                        double ratio;
                        if(configurationResult == 0)
                            ratio = 100D;
                        else
                            ratio = NumberUtil.doubleAccuracy((oneEnergyInfo.getValue() == null ? 0 : ((oneEnergyInfo.getValue()/configurationResult) * 100 )),2);
                        eachEnergyCarbonRatioList.add(new EachEnergyCarbonIntensityRatioResult(oneEnergyInfo.getIndexName(),oneEnergyInfo.getValue(),ratio));
                    }
                }
         }
        result.put("eachEnergyCarbonRatio",eachEnergyCarbonRatioList);
        return result;
    }


    //**********************************************************权限接口××××××××××××××××××××××××××××××××××××××××××××××××××××××//
    @Override
    public UseEnergyAndCarbonTotal getTotalEnergyUseOfRole(Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer userId) {
        UseEnergyAndCarbonTotal res = new UseEnergyAndCarbonTotal();
        List<EnergyComparativeAnalysisDTO> comparativeAnalysis = energyTotalAnalysisService.findComparativeAnalysis(startTime, endTime, null,userId,timeType);
        List<EnergyComparativeAnalysisDTO> energyComparativeAnalysisDTOS = new ArrayList<>();
        //-1是所有非清洁能源  0是绿色能源
        if (businessTypeId == 0) {
            energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId() >= 100).toList();
        } else if (businessTypeId == -1){
            energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId() < 100).toList();
        } else  {
            energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId().equals(businessTypeId)).toList();
        }

        if (energyComparativeAnalysisDTOS.isEmpty()) {
            return res;
        }

        if (businessTypeId>0 ) { //只是某一种能
            EnergyComparativeAnalysisDTO dto = energyComparativeAnalysisDTOS.get(0);
            res.setUseEnergyTotal(dto.getValue());
            res.setUseEnergyYoY(dto.getYoY());
            res.setCo2Total(dto.getValueCo2());
            res.setCo2YoY(dto.getYoY());
            res.setCo2CoC(dto.getCoC());
            res.setUnit(dto.getUnit());
        } else{
            Double co2Total = 0d;
            Double co2YoY = 0d;
            Double co2CoC = 0d;
            for (var dto : energyComparativeAnalysisDTOS){
                co2Total += dto.getValueCo2();
                co2YoY += dto.getYoY();
                co2CoC += dto.getCoC();
            }
            res.setCo2Total(co2Total);
            res.setCo2YoY(co2YoY);
            res.setCo2CoC(co2CoC);
            res.setUnit(energyComparativeAnalysisDTOS.get(0).getUnit());
        }
        return res;
    }

    @Override
    public List<UseEnergyAndCarbon> getChildTotalEnergyUseOfRole(Integer childObjectTypeId, Integer businessTypeId, String timeType, Date startTime, Date endTime, Integer userId) {
        List<UseEnergyAndCarbon> result = new ArrayList<>();

        //获取此用户拥有这个层级类型的的节点
        List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(userId).stream().filter(i -> i.getStructureTypeId().equals(childObjectTypeId)).toList();

        for (ResourceStructure structure : roleResourceStructures) {
            UseEnergyAndCarbon res = new UseEnergyAndCarbon();
            res.setResourceStructureName(structure.getResourceStructureName());
            res.setCo2Total(0d);
            res.setUseEnergyTotal(0d);

            List<EnergyComparativeAnalysisDTO> comparativeAnalysis = energyTotalAnalysisService.findObjectEnergyTotal(startTime, endTime, structure.getResourceStructureId(),userId,timeType);
            List<EnergyComparativeAnalysisDTO> energyComparativeAnalysisDTOS = new ArrayList<>();
            //-1是所有非清洁能源  0是绿色能源
            if (businessTypeId == 0) {
                energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId() >= 100).toList();
            } else if (businessTypeId == -1){
                energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId() < 100).toList();
            } else  {
                energyComparativeAnalysisDTOS = comparativeAnalysis.stream().filter(item -> item.getBusinessTypeId().equals(businessTypeId)).toList();
            }

            if (!energyComparativeAnalysisDTOS.isEmpty()) {
                if (businessTypeId>0 ) { //只是某一种能
                    EnergyComparativeAnalysisDTO dto = energyComparativeAnalysisDTOS.get(0);
                    res.setUseEnergyTotal(dto.getValue());
                    res.setCo2Total(dto.getValueCo2());

                    res.setUnit(dto.getUnit());
                } else{
                    Double co2Total = 0d;
                    for (var dto : energyComparativeAnalysisDTOS){
                        co2Total += dto.getValueCo2();
                    }
                    res.setCo2Total(co2Total);

                    res.setUnit(energyComparativeAnalysisDTOS.get(0).getUnit());
                }

            }
            result.add(res);
        }

        return result.stream().sorted(Comparator.comparing(UseEnergyAndCarbon::getCo2Total).reversed()).collect(Collectors.toList());
    }



    //查询能耗指标历史数据
    @Override
    public Object findHistoryComplexIndexsByIdAndDuration(List<Integer> complexIndexIds, String tableType,Date startTime, Date endTime) {
        Map<Integer, List<EnergyHisDataResult>> historyComplexIndexMap = new HashMap<>();

        for (Integer cId : complexIndexIds){
            historyComplexIndexMap.put(cId,getHistoryComplexIndexValueByTimeType(cId, tableType.toLowerCase(), startTime, endTime));
        }
        return historyComplexIndexMap;
    }


    public List<EnergyHisDataResult> getHistoryComplexIndexValueByTimeType(Integer complexIndexId, String tableType, Date startTime, Date endTime) {
        List<EnergyHisDataResult> results = new ArrayList<>();
        String sql = "";
        String measurementName = "" ;

        if (tableType.equals("y")) {
            measurementName = "EnergyHisMonthData";
            sql = "SELECT * FROM EnergyHisMonthData  WHERE time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId";
        } else if (tableType.equals("m")) {
            measurementName = "EnergyHisDayData";
            sql = "SELECT * FROM EnergyHisDayData  WHERE time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId";
        } else if (tableType.equals("d")) {
            measurementName = "EnergyHisHourData";
            sql = "SELECT * FROM EnergyHisHourData  WHERE time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId";
        }

        if (measurementName == null) return results;

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        List<EnergyHisDataResult> energyQueryResult = new ArrayList<>();
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .bind("complexIndexId", String.valueOf(complexIndexId))
                    .create();
            query = influxDB.query(queryBuilder);

            if (query != null) {
                energyQueryResult = resultMapper.toPOJO(query, EnergyHisDataResult.class, measurementName);
            }
            if (energyQueryResult == null || energyQueryResult.size() == 0)
                return new ArrayList<>();

            for (EnergyHisDataResult result : energyQueryResult) {
                result.time = result.time.replace("T"," ").replace("Z","");
            }
            return energyQueryResult;
        } catch (Exception e) {
            log.error("energyStandardApiService-getHistoryComplexIndexValueByTimeType 组态指标接口 error {}", e.getMessage());
            return new ArrayList<>();
        }
    }

}
