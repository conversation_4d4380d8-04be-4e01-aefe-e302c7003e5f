package com.siteweb.energy.service;

import java.util.Date;

public interface EnergyHistoryDataService {
    void startStatisticEnergyHistoryData();
    void energyInfluxDBHistoryData();

    boolean manualEnergyHisDataInfluxdb(Integer complexIndexId, Date startTime, Date endTime, Double totalValue);

    boolean modifyEnergyHisHourDataInfluxdb(Integer complexIndexId, Date startTime, Double value);

}
