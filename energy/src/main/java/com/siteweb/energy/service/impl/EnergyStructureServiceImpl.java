package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.mapper.ComplexIndexMapper;
import com.siteweb.energy.dto.DefaultResourceTreeDTO;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyObjectMap;
import com.siteweb.energy.entity.EnergyStructure;
import com.siteweb.energy.enumeration.SourceCategory;
import com.siteweb.energy.mapper.EnergyDimensionComplexIndexMapper;
import com.siteweb.energy.mapper.EnergyObjectMapMapper;
import com.siteweb.energy.mapper.EnergyStructureMapper;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.model.StructureInfo;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyStructureServiceImpl implements EnergyStructureService {

    //public final static int DEFAULT_STRUCTURE_TYPE = 1;//SourceCategory.RESOURCE_STRUCTURE.value()
    //public final static int ENERGY_STRUCTURE_TYPE = 2;//SourceCategory.ENERGY_RESOURCE.value()
    /** 此节点是一个[根节点流入节点] */
    public final static Integer IS_ROOT_INFLOW_NODE = 1;

    @Autowired
    private EnergyStructureMapper structureMapper;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyObjectMapMapper objectMapMapper;
    @Autowired
    private ComplexIndexMapper complexIndexMapper;
    @Autowired
    private EnergyDimensionComplexIndexMapper energyDimensionComplexIndexMapper;
    @Autowired
    private ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;
    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Override
    public List<EnergyStructure> findAll() {
        return structureMapper.selectList(null);
    }

    @Override
    public List<EnergyStructure> findByStructureIdIn(List<Integer> listStructureIdId) {
        return structureMapper.selectBatchIds(listStructureIdId);
    }


    @Override
    public ResourceStructureTreeDTO getDefaultTree() {

        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            log.warn("getDefaultTree: loginUserId is null");
            return null;
        }
        return resourceStructureService.findResourceStructureTreeByUserId(loginUserId);
    }

    @Override
    public EnergyResourceStructure getEnergyStructureTree(Integer dimensionTypeId) {
        Map<String, EnergyResourceStructure> allNodeMaps = getEnergyStructureTreeMap(dimensionTypeId);
        return allNodeMaps.get("-1");
    }

    @Override
    public ResultObject<String> addOneEnergyStructure(StructureInfo newStructure) {

        //校验父节点
        EnergyResourceStructure paramParentNode = newStructure.getParentNode();

        boolean canAdd = validCanAddChildren(paramParentNode);
        if(!canAdd) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.cannotaddchild"),-1);//"此类型节点不可添加子节点"
        }

        if(SourceCategory.COMPUTERRACK == paramParentNode.getSourceCategory()) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.computerrackcannotaddstructurenode"),-1);//"机架节点下不可再添加层级节点"
        }
        EnergyObjectMap pObjectMap; //父节点

        if(paramParentNode.getObjectId().equals(0)) {//父节点为0，表示要新增的是根节点
            List<EnergyObjectMap> rootNodes = objectMapMapper.findRootNodeByDimensionTypeId(paramParentNode.getDimensionTypeId());
            if(rootNodes != null && rootNodes.size() > 0) {
                String msg = getI18nMsg("energy.msg.dimension.rootnodeexists");//根节点已存在
                return new ResultObject<>(msg + "(" + paramParentNode.getDimensionTypeId() + ")", -10);
            } else {//模拟一个objectId为0的节点
                pObjectMap = new EnergyObjectMap();
                pObjectMap.setObjectId(0);
                pObjectMap.setObjectIdType(SourceCategory.ENERGY_RESOURCE.value());
                pObjectMap.setLevelId(0);
                pObjectMap.setLevelOfPath("");
                log.info("此多维度类型(" + paramParentNode.getDimensionTypeId() + ")将新增一个根节点。");
            }
        } else {
            List<EnergyObjectMap> pNodes = objectMapMapper.findByDimensionTypeIdAndObjectIdAndObjectIdType(paramParentNode.getDimensionTypeId(),
                    paramParentNode.getObjectId(), paramParentNode.getObjectIdType());
            if(pNodes == null || pNodes.size() < 1) {
                String msg = getI18nMsg("energy.msg.dimension.parentnodenotexist");//父节点不存在
                return new ResultObject<>(msg + "(" + paramParentNode.getObjectId() + ")", -10);
            }
            if(pNodes.size() > 1) {
                String msg = getI18nMsg("energy.msg.dimension.parentnodegreaterthanone");//父节点大于1个
                return new ResultObject<>(msg + "(" + paramParentNode.getObjectId() + ")", -10);
            }
            pObjectMap = pNodes.get(0);
        }

        //节点名重名校验
        EnergyStructure energyStructure = newStructure.getAddOneEnergyNode();
        energyStructure.setStructureId(null);
        boolean existSameNodeName = vaildSameNodeName(null,energyStructure.getStructureName(), paramParentNode.getDimensionTypeId(),
                pObjectMap.getObjectId(), pObjectMap.getObjectIdType());

        if(existSameNodeName) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.nodenameexists"), -10);//"节点名已存在"
        }

        //添加特殊节点[根节点流入节点]的校验逻辑 begin ====
        if(IS_ROOT_INFLOW_NODE.equals(energyStructure.getAsRootInflowNode())) {//新增的是[根节点流入节点]
            if(paramParentNode.getObjectId().equals(0)) {//要创建根节点
                return new ResultObject<>("inflow node can not be root node", -20);//[根节点流入节点]不能同时也是根节点
            }
            if(!Integer.valueOf(0).equals(pObjectMap.getParentObjectId())) {//不等于0，说明父节点不是根节点
                return new ResultObject<>("inflow node must be added to root node", -20);//[根节点流入节点]必须是根节点的直接子节点
            }
        }
        //添加特殊节点[根节点流入节点]的校验逻辑 end ====

        //保存节点对象
        structureMapper.insert(energyStructure);

        //保存映射表数据
        EnergyObjectMap objectMap = new EnergyObjectMap();
        objectMap.setParentObjectId(pObjectMap.getObjectId());
        objectMap.setParentObjectIdType(pObjectMap.getObjectIdType());
        objectMap.setObjectId(energyStructure.getStructureId());
        objectMap.setObjectIdType(SourceCategory.ENERGY_RESOURCE.value());
        objectMap.setLevelId(pObjectMap.getLevelId() + 1);
        objectMap.setDimensionTypeId(paramParentNode.getDimensionTypeId());
        String tmp = StringUtils.isBlank(pObjectMap.getLevelOfPath()) ? "" : (pObjectMap.getLevelOfPath() + ".");
        objectMap.setLevelOfPath(tmp + EnergyResourceStructure.getStructureKeyByObjectIdAndObjectIdType(energyStructure.getStructureId(), SourceCategory.ENERGY_RESOURCE.value()));
        objectMap.setNotes(EnergyDimensionTypeServiceImpl.CREATE_CUSTOM_NODE);
        objectMapMapper.insert(objectMap);

        if(pObjectMap.getObjectId().equals(0)) {
            log.info("此多维度类型(" + paramParentNode.getDimensionTypeId() + ")已新增一个根节点。structureId=" + energyStructure.getStructureId()
                    + ";structureName=" + energyStructure.getStructureName()
                    + ";asRootInflowNode=" + energyStructure.getAsRootInflowNode()
                    + ";mapId=" + objectMap.getId()
                    + "");
        }

        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> delOneEnergyStructure(EnergyResourceStructure node) {
        EnergyObjectMap objectMap = new EnergyObjectMap();
        objectMap.setId(node.getId());
        objectMap.setDimensionTypeId(node.getDimensionTypeId());
        objectMap.setObjectId(node.getObjectId());
        objectMap.setObjectIdType(node.getObjectIdType());
        objectMap.setParentObjectId(node.getParentObjectId());
        objectMap.setParentObjectIdType(node.getParentObjectIdType());
        objectMap.setLevelId(node.getLevelId());
        objectMap.setLevelOfPath(node.getLevelOfPath());
        objectMap.setNotes(node.getNotes());
        delAllNodes(node);
        return new ResultObject<>("OK");
    }

    @Override
    public ResultObject<String> updateOneEnergyStructure(EnergyResourceStructure node) {

        if(node.getObjectIdType() == null) {
            return new ResultObject<>("ObjectIdType is null", -1);
        } else if(node.getObjectIdType().equals(SourceCategory.RESOURCE_STRUCTURE.value())) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.defaultnodecannotedit"), -1);//"默认层级节点此处不可编辑"
        } else if(node.getObjectIdType().equals(SourceCategory.EQUIPMENT.value())) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.refequipmentnodecannotedit"), -1);//"引用设备不可编辑"
        } else if(node.getObjectIdType().equals(SourceCategory.COMPUTERRACK.value())) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.refcomputerracknodecannotedit"), -1);//"引用机架不可编辑"
        } else if(node.getObjectIdType().equals(SourceCategory.ITDEVICE.value())) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.refitdevicenodecannotedit"), -1);//"引用IT设备不可编辑"
        } else if(node.getObjectId() == null) {
            return new ResultObject<>("StructureId is Null", -1);
        } else if(node.getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value())) {
            boolean existSameNodeName = vaildSameNodeName(node.getResourceStructureId(), node.getResourceStructureName(), node.getDimensionTypeId(),
                    node.getParentObjectId(), node.getParentObjectIdType());
            if(existSameNodeName) {
                return new ResultObject<>(getI18nMsg("energy.msg.dimension.nodenameexists"), -10);//"节点名已存在"
            }

            EnergyStructure updateObj = new EnergyStructure();
            updateObj.setStructureId(node.getObjectId());
            updateObj.setStructureName(node.getResourceStructureName());
            updateObj.setNotes(node.getStructureNotes());
            updateObj.setSourceCategory(node.getEnergyStructureSourceCategory());
            updateObj.setAsRootInflowNode(node.getAsRootInflowNode());
            structureMapper.updateById(updateObj);

            return new ResultObject<>("OK");
        } else {
            log.error("Unknown ObjectIdType=" + node.getObjectIdType());
            return new ResultObject<>("Unknown ObjectIdType", -1);
        }
    }

    @Override
    public EnergyResourceStructure getEnergyStructureSubTree(Integer dimensionTypeId, Integer subTreeRootId, Integer objectIdType) {
        Map<String, EnergyResourceStructure> energyStructureTreeMap = getEnergyStructureTreeMap(dimensionTypeId);
        return energyStructureTreeMap.get(EnergyResourceStructure.getStructureKeyByObjectIdAndObjectIdType(subTreeRootId, objectIdType));
    }

    /** 递归删除当前节点及其所有子节点 */
    private void delAllNodes(EnergyObjectMap node) {

        Integer objectMapPkId = node.getId();
        EnergyObjectMap parentNode = objectMapMapper.selectById(objectMapPkId);
        if(parentNode == null ) {
            log.error("待删除的节点不存在(objectMapPkId=" + objectMapPkId + ")");
            throw new RuntimeException(getI18nMsg("energy.msg.dimension.deletenodenotexists"));//"待删除的节点不存在"
        }

        List<EnergyObjectMap> childen = objectMapMapper.findAllLevel1ChildNodes(parentNode.getObjectId(), parentNode.getObjectIdType(), parentNode.getDimensionTypeId());
        if(childen != null && childen.size() > 0) {
            for (EnergyObjectMap child : childen) {
                delAllNodes(child);
            }
        }
        delOneNode(parentNode);
    }

    private boolean delOneNode(EnergyObjectMap objectMap) {

        if(objectMap.getObjectIdType() == null) {
            log.error("objectMap.getObjectIdType() == null");
            throw new RuntimeException("when del one node，objectMap.getObjectIdType() == null");
        }
        if(objectMap.getObjectIdType().equals(SourceCategory.RESOURCE_STRUCTURE.value())) {//如果是引用层级节点，则只删除关联关系
            objectMapMapper.deleteById(objectMap.getId());
            return true;
        }
        if(objectMap.getObjectIdType().equals(SourceCategory.EQUIPMENT.value())
                || objectMap.getObjectIdType().equals(SourceCategory.COMPUTERRACK.value())
                || objectMap.getObjectIdType().equals(SourceCategory.ITDEVICE.value())) {//如果是引用这三类设备节点，删除关联关系后，考虑是否要删除对应的指标
            objectMapMapper.deleteById(objectMap.getId());
            SourceType sourceType = SourceCategory.valueOf(objectMap.getObjectIdType()).toSourceType(); //得到对应的SourceType，指标中存储的是SourceType对应的值
            if(sourceType == null) {
                throw new RuntimeException("sourceType(" + objectMap.getObjectIdType() + ") is NULL");
            }
            //校验此节点是否被其他树引用
            int countUsed = objectMapMapper.getCountByOtherTreeUsed(objectMap.getObjectId(), objectMap.getObjectIdType(), objectMap.getId());
            if(countUsed < 1) {
                List<Integer> energyBusinessTypeIds =  complexIndexBusinessTypeMapper.selectList(null).stream().filter(i->i.getParentId().equals(1) || i.getBusinessTypeId().equals(1))
                        .map(ComplexIndexBusinessType::getBusinessTypeId).collect(Collectors.toList());
                batchDeleteComplexIndex(objectMap.getObjectId(), sourceType.value(), energyBusinessTypeIds);
            }
            return true;
        }
        if(objectMap.getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value())) {//如果是自定义层级节点

            //不管有无引用，都需删除关联关系
            objectMapMapper.deleteById(objectMap.getId());
            //校验此节点是否被其他树引用
            int countUsed = objectMapMapper.getCountByOtherTreeUsed(objectMap.getObjectId(), objectMap.getObjectIdType(), objectMap.getId());
            if(countUsed < 1) {//无引用, 则需删除节点记录，以及相应的所有指标记录
                if(structureMapper.selectById(objectMap.getObjectId()) != null) {
                    structureMapper.deleteById(objectMap.getObjectId()); //删除此私有自定义节点
                } else {
                    log.warn("自定义节点不存在，structureId=" + objectMap.getObjectId());
                }
                batchDeleteComplexIndex(objectMap.getObjectId(), EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID, null);
            }
            //int delCount = objectMapRepo.deleteByDimensionTypeIdAndObjectIdAndObjectIdType(objectMap.getDimensionTypeId(), objectMap.getObjectId(), objectMap.getObjectIdType());
            //if(delCount > 1) {
            //    log.error("删除count=" + delCount + "; 删除多于一条，请联系管理员处理2");
            //    throw new RuntimeException("when del one node，count>1.");
            //}
            return true;
        }
        return false;
    }

    /** 校验是否存在同名节点。nodeId为null是新增的节点，nodeId非null是编辑节点
     * true - 存在同名节点 */
    private boolean vaildSameNodeName(Long nodeId, String nodeName, Integer dimensionTypeId,
                                      Integer parentObjectId, Integer parentObjectIdType) {
        boolean rtn = false;
        List<EnergyResourceStructure> sameNameNodeList = objectMapMapper.findSameNameNodeUnderSameParentObjectId(dimensionTypeId, parentObjectId, parentObjectIdType, nodeName,
                SourceCategory.ENERGY_RESOURCE.value(), SourceCategory.RESOURCE_STRUCTURE.value(),
                SourceCategory.EQUIPMENT.value(),SourceCategory.COMPUTERRACK.value(),SourceCategory.ITDEVICE.value(),
                EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID);
        if(sameNameNodeList != null && sameNameNodeList.size() > 0) {
            if(nodeId == null) {
                rtn = true;
            } else {
                for (EnergyResourceStructure oneSame : sameNameNodeList) {
                    if(!nodeId.equals(oneSame.getResourceStructureId())) {
                        rtn = true;
                        break;
                    }
                }
            }
        }
        if(rtn) {
            log.warn("Same Node Name. nodeId=" + nodeId + "; NodeName=" + nodeName + "; dimensionTypeId=" + dimensionTypeId
                    + "; parentObjectId=" + parentObjectId + "; parentObjectIdType=" + parentObjectIdType);
        }
        return rtn;
    }

    public static List<EnergyResourceStructure> convertMapToObj(List<Map<String, Object>> sourceMap) {
        List<EnergyResourceStructure> list = new ArrayList<>();
        if(sourceMap != null && sourceMap.size() > 0) {

            for (Map<String, Object> oneMap : sourceMap) {
                EnergyResourceStructure one = new EnergyResourceStructure();
                one.setId(oneMap.get("Id") == null ? null : Integer.parseInt(oneMap.get("Id").toString()));
                one.setDimensionTypeId(oneMap.get("DimensionTypeId") == null ? null : Integer.parseInt(oneMap.get("DimensionTypeId").toString()));
                one.setObjectId(oneMap.get("ObjectId") == null ? null : Integer.parseInt(oneMap.get("ObjectId").toString()));
                one.setObjectIdType(oneMap.get("ObjectIdType") == null ? null : Integer.parseInt(oneMap.get("ObjectIdType").toString()));
                one.setParentObjectId(oneMap.get("ParentObjectId") == null ? null : Integer.parseInt(oneMap.get("ParentObjectId").toString()));
                one.setParentObjectIdType(oneMap.get("ParentObjectIdType") == null ? null : Integer.parseInt(oneMap.get("ParentObjectIdType").toString()));
                one.setLevelId(oneMap.get("LevelId") == null ? null : Integer.parseInt(oneMap.get("LevelId").toString()));
                one.setLevelOfPath(oneMap.get("LevelOfPath") == null ? null : oneMap.get("LevelOfPath").toString());
                one.setNotes(oneMap.get("Notes") == null ? null : oneMap.get("Notes").toString());
                one.setStructureNotes(oneMap.get("StructureNotes") == null ? null : oneMap.get("StructureNotes").toString());
                one.setResourceStructureId(oneMap.get("ResourceStructureId") == null ? null : Long.parseLong(oneMap.get("ResourceStructureId").toString()));
                one.setResourceStructureName(oneMap.get("ResourceStructureName") == null ? null : oneMap.get("ResourceStructureName").toString());
                //one.setGlobalResourceId(oneMap.get("GlobalResourceId") == null ? null : Long.parseLong(oneMap.get("GlobalResourceId").toString()));
                one.setObjectTypeId(oneMap.get("ObjectTypeId") == null ? null : Integer.parseInt(oneMap.get("ObjectTypeId").toString()));
                one.setChildren(new ArrayList<>());
                list.add(one);
            }
        }
        return list;
    }

    /** 获得此指定多维度方案自定义树的任意节点子树map; 为方便取得根节点，map.get(-1)中保存了根节点对象 */
    private Map<String, EnergyResourceStructure> getEnergyStructureTreeMap(Integer dimensionTypeId) {

        Map<String, EnergyResourceStructure> allNodeMaps = new HashMap<>();
        List<EnergyResourceStructure> allNodes = objectMapMapper.findAllNodesByDimensionTypeId(dimensionTypeId, SourceCategory.ENERGY_RESOURCE.value(), SourceCategory.RESOURCE_STRUCTURE.value(), EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID,
                SourceCategory.EQUIPMENT.value(), SourceCategory.COMPUTERRACK.value(), SourceCategory.ITDEVICE.value());

        if(allNodes == null || allNodes.size() < 1) {
            return allNodeMaps;
        }
        String rootKey = null;

        for (EnergyResourceStructure node : allNodes) {
            allNodeMaps.put(node.getStructureKey(), node);//同一棵树中，不会有两个节点的"ObjectId+ObjectIdType"是一样的，即同一个节点只能归属一棵树一次，不能重复挂
            if (node.getParentObjectId() == 0) {
                rootKey = node.getStructureKey();
            }
        }
        for (EnergyResourceStructure oneNode : allNodes) {
            if (!Integer.valueOf(0).equals(oneNode.getParentObjectId())) {
                if (allNodeMaps.containsKey(oneNode.getStructureParentKey())) {
                    EnergyResourceStructure param = allNodeMaps.get(oneNode.getStructureParentKey());
                    param.getChildren().add(oneNode);
                }
            }
        }
        allNodeMaps.put("-1", allNodeMaps.get(rootKey)); //为方便取得根节点，约定用-1为key再重复保存一次根节点对象
        return allNodeMaps;
    }


    //--------------- 添加已存在结节 begin

    /** 添加已存在层级的节点，节点可能是自定义节点，也可能是默认层级节点，或者两者混合 */
    private ResultObject<String> addExistStructuresNodes(StructureInfo newStructure) {
        EnergyObjectMap parentObjectMap1 = objectMapMapper.selectById(newStructure.getParentNode().getId());
        EnergyResourceStructure parentObjectMapWithSourceCategory = objectMapMapper.findObjectMapWithSourceCategoryById(newStructure.getParentNode().getId(),
                SourceCategory.ENERGY_RESOURCE.value(), SourceCategory.RESOURCE_STRUCTURE.value(),
                SourceCategory.EQUIPMENT.value(),SourceCategory.COMPUTERRACK.value(),SourceCategory.ITDEVICE.value(),
                EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID);

        if(parentObjectMapWithSourceCategory == null) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.parentnodenotexist"),-1);//"父节点不存在"
        }
        //校验根节点的类型，只有层级节点和机架节点才能添加子孙节点
        boolean canAddChild = validCanAddChildren(parentObjectMapWithSourceCategory);
        if(!canAddChild) {
            return new ResultObject<>(getI18nMsg("energy.msg.dimension.cannotaddchild"),-1);//"此类型节点不可添加子节点"
        }

        if(newStructure.getPreserveTreeStructure()) {//保留树结构

            List<EnergyResourceStructure> subTreeList = newStructure.getAddStructuresEnergy();
            for (EnergyResourceStructure oneSubTree : subTreeList) {
                addNodesByTreeStructure(parentObjectMapWithSourceCategory, oneSubTree, newStructure.getOnlyCopyName(), parentObjectMapWithSourceCategory);
            }
        } else {//不保留树结构
            List<EnergyResourceStructure> allNode = convertEnergyTreeToList(newStructure.getAddStructuresEnergy());
            for (EnergyResourceStructure oneNode : allNode) {

                checkStructureNode(parentObjectMapWithSourceCategory, oneNode);

                EnergyObjectMap objectMap = new EnergyObjectMap();
                if(newStructure.getOnlyCopyName()) {//只复制名字

                    //boolean existSameNodeName = vaildSameNodeName( null, oneNode.getResourceStructureName(), parentObjectMap.getDimensionTypeId(),
                    //        parentObjectMap.getObjectId(), parentObjectMap.getObjectIdType());
                    //if(existSameNodeName) {
                    //    throw new RuntimeException("Node with the same name already exists[" + oneNode.getResourceStructureName() + "]");
                    //}
                    //保存节点对象
                    EnergyStructure structureObj = new EnergyStructure();
                    structureObj.setStructureName(oneNode.getResourceStructureName());
                    structureObj.setNotes(oneNode.getStructureKey() + ";[COPY by PreserveTreeStructure(false)]");
                    SourceCategory sourceCategory = oneNode.getSourceCategory();
                    if(sourceCategory == null) {
                        log.error("1. sourceCategory is null");
                        throw new RuntimeException("sourceCategory is null");
                    }
                    structureObj.setSourceCategory(sourceCategory.value());
                    structureMapper.insert(structureObj);

                    //构造映射表数据
                    objectMap.setObjectId(structureObj.getStructureId());
                    objectMap.setObjectIdType(SourceCategory.ENERGY_RESOURCE.value());
                    String tmp = StringUtils.isBlank(parentObjectMapWithSourceCategory.getLevelOfPath()) ? "" : (parentObjectMapWithSourceCategory.getLevelOfPath() + ".");
                    objectMap.setLevelOfPath(tmp + EnergyResourceStructure.getStructureKeyByObjectIdAndObjectIdType(structureObj.getStructureId(), SourceCategory.ENERGY_RESOURCE.value()));
                    objectMap.setNotes(EnergyDimensionTypeServiceImpl.USE_EXISTS_NODE_NAME + ";" + oneNode.getStructureKey() + ";[by PreserveTreeStructure(false)]");

                } else {//直接引用节点

                    if(nodeIsExist(oneNode.getResourceStructureId(), oneNode.getObjectIdType())) {

                        checkInflowNodeAddCondition(parentObjectMapWithSourceCategory, oneNode);

                        //校验此节点是否已经被本棵树引用过了
                        checkNodeHasUsed(parentObjectMapWithSourceCategory.getDimensionTypeId(), oneNode.getResourceStructureName(),
                                oneNode.getObjectId(), oneNode.getObjectIdType());
                        //校验是否有同名节点
                        //boolean existSameNodeName = vaildSameNodeName( null, oneNode.getResourceStructureName(), parentObjectMap.getDimensionTypeId(),
                        //        parentObjectMap.getObjectId(), parentObjectMap.getObjectIdType());
                        //if(existSameNodeName) {
                        //    throw new RuntimeException("Node with the same name already exists[" + oneNode.getResourceStructureName() + "]");
                        //}

                        //构造映射表数据
                        objectMap.setObjectId(oneNode.getResourceStructureId().intValue());
                        objectMap.setObjectIdType(oneNode.getObjectIdType());
                        String tmp = StringUtils.isBlank(parentObjectMapWithSourceCategory.getLevelOfPath()) ? "" : (parentObjectMapWithSourceCategory.getLevelOfPath() + ".");
                        objectMap.setLevelOfPath(tmp + oneNode.getStructureKey());
                        objectMap.setNotes(EnergyDimensionTypeServiceImpl.USE_EXISTS_NODE + ";" + oneNode.getStructureKey() + ";[by PreserveTreeStructure(false)]");

                    } else {
                        log.warn("Node[ResourceStructureId= " + oneNode.getResourceStructureId() + "; ObjectIdType= " + oneNode.getObjectIdType() + ";" +
                                " ResourceStructureName= " + oneNode.getResourceStructureName() + "; GlobalResourceId= " + "]  Not Exists");
                        throw new RuntimeException("Node Not Exists");
                    }
                }
                objectMap.setParentObjectId(parentObjectMapWithSourceCategory.getObjectId());
                objectMap.setParentObjectIdType(parentObjectMapWithSourceCategory.getObjectIdType());
                objectMap.setLevelId(parentObjectMapWithSourceCategory.getLevelId() + 1);
                objectMap.setDimensionTypeId(parentObjectMapWithSourceCategory.getDimensionTypeId());
                //最终保存映射关系
                objectMapMapper.insert(objectMap);
            }
        }
        return new ResultObject<>("OK");
    }

    /** 校验传入的节点下是否可以添加子孙节点 */
    private boolean validCanAddChildren(EnergyResourceStructure parentNode) {

        if(parentNode != null) {
            SourceCategory curCategory = parentNode.getSourceCategory();
            return SourceCategory.RESOURCE_STRUCTURE == curCategory ||
                    SourceCategory.COMPUTERRACK == curCategory;
        }
        return false;
    }

    /** 校验传入节点是否已被当前树所引用过，引用过则抛出异常 */
    private void checkNodeHasUsed(Integer dimensionTypeId, String resourceStructureName, Integer objectId, Integer objectIdType) {
        List<EnergyObjectMap> hasUsedList = objectMapMapper.findByDimensionTypeIdAndObjectIdAndObjectIdType(dimensionTypeId,
                objectId, objectIdType);
        if(hasUsedList != null && hasUsedList.size() > 0) {
            log.warn("Node[objectId= " + objectId + "; ObjectIdType= " + objectIdType + "; DimensionTypeId= " + dimensionTypeId + ";" +
                    " ResourceStructureName= " + resourceStructureName + "]  节点重复引用");
            throw new RuntimeException(getI18nMsg("energy.msg.dimension.nodedupref"));//"节点重复引用"
        }
    }

    /** 校验如果引用的节点是[根节点流入节点]，则需要其父节点必须是根节点 */
    private void checkInflowNodeAddCondition(EnergyResourceStructure parentObjectMap, EnergyResourceStructure oneSubTree) {
        if(IS_ROOT_INFLOW_NODE.equals(oneSubTree.getAsRootInflowNode())) {//若要引入的子节点是[根节点流入节点]
            if(!Integer.valueOf(0).equals(parentObjectMap.getParentObjectId())) {
                log.warn("Node[objectId= " + oneSubTree.getObjectId() + "; ObjectIdType= " + oneSubTree.getObjectIdType() + "; DimensionTypeId= " + parentObjectMap.getDimensionTypeId() + ";" +
                        " ResourceStructureName= " + oneSubTree.getResourceStructureName() + "]  AsRootInflowNode==1,but parentNode is not rootNode");
                throw new RuntimeException("Node(" + oneSubTree.getResourceStructureName() + ") can only be referenced to the root node");//"根节点流入节点只能被根节点引用"
            }
        }
    }


    /** 以保留树结构的方式 添加已存在的节点(包括自定义节点和默认层级节点) */
    private void addNodesByTreeStructure(EnergyResourceStructure parentObjectMap, EnergyResourceStructure oneSubTree, Boolean onlyCopyName, EnergyResourceStructure curRootParentNode) {

        checkStructureNode(curRootParentNode, oneSubTree);

        EnergyResourceStructure objectMap = new EnergyResourceStructure();
        if(onlyCopyName) {//只复制节点名字

            //boolean existSameNodeName = vaildSameNodeName( null, oneSubTree.getResourceStructureName(), parentObjectMap.getDimensionTypeId(),
            //        parentObjectMap.getObjectId(), parentObjectMap.getObjectIdType());
            //if(existSameNodeName) {
            //    throw new RuntimeException("Node with the same name already exists[" + oneSubTree.getResourceStructureName() + "]");
            //}

            EnergyStructure structureObj = new EnergyStructure();
            structureObj.setStructureName(oneSubTree.getResourceStructureName());
            structureObj.setNotes(oneSubTree.getStructureKey() + ";[COPY by PreserveTreeStructure(true)]");
            SourceCategory sourceCategory = oneSubTree.getSourceCategory();
            if(sourceCategory == null) {
                log.error("2. sourceCategory is null");
                throw new RuntimeException("sourceCategory is NULL.");
            }
            structureObj.setSourceCategory(sourceCategory.value());
            structureMapper.insert(structureObj);

            objectMap.setObjectId(structureObj.getStructureId());
            objectMap.setObjectIdType(SourceCategory.ENERGY_RESOURCE.value());
            String tmp = StringUtils.isBlank(parentObjectMap.getLevelOfPath()) ? "" : (parentObjectMap.getLevelOfPath() + ".");
            objectMap.setLevelOfPath(tmp + EnergyResourceStructure.getStructureKeyByObjectIdAndObjectIdType(structureObj.getStructureId(), SourceCategory.ENERGY_RESOURCE.value()));
            objectMap.setNotes(EnergyDimensionTypeServiceImpl.USE_EXISTS_NODE_NAME + ";" + oneSubTree.getStructureKey() + ";[by PreserveTreeStructure(true)]");
            objectMap.setEnergyStructureSourceCategory(sourceCategory.value());
        } else {//直接引用节点
            if(nodeIsExist(oneSubTree.getResourceStructureId(), oneSubTree.getObjectIdType())) {

                checkInflowNodeAddCondition(parentObjectMap, oneSubTree);

                //校验此节点是否已经被本棵树引用过了
                checkNodeHasUsed(parentObjectMap.getDimensionTypeId(), oneSubTree.getResourceStructureName(),
                        oneSubTree.getObjectId(), oneSubTree.getObjectIdType());
                //校验是否有同名节点
                //boolean existSameNodeName = vaildSameNodeName( null, oneSubTree.getResourceStructureName(), parentObjectMap.getDimensionTypeId(),
                //        parentObjectMap.getObjectId(), parentObjectMap.getObjectIdType());
                //if(existSameNodeName) {//先判断是否有引用，之后再判断是否有同名
                //    throw new RuntimeException("Node with the same name already exists[" + oneSubTree.getResourceStructureName() + "]");
                //}

                objectMap.setObjectId(oneSubTree.getResourceStructureId().intValue());
                objectMap.setObjectIdType(oneSubTree.getObjectIdType());
                String tmp = StringUtils.isBlank(parentObjectMap.getLevelOfPath()) ? "" : (parentObjectMap.getLevelOfPath() + ".");
                objectMap.setLevelOfPath(tmp + oneSubTree.getStructureKey());
                objectMap.setNotes(EnergyDimensionTypeServiceImpl.USE_EXISTS_NODE + ";" + oneSubTree.getStructureKey() + ";[by PreserveTreeStructure(true)]");
                objectMap.setEnergyStructureSourceCategory(oneSubTree.getEnergyStructureSourceCategory());
            } else {
                log.error("Node1[ResourceStructureId= " + oneSubTree.getResourceStructureId() + "; ObjectIdType= " + oneSubTree.getObjectIdType() + ";" +
                        " ResourceStructureName= " + oneSubTree.getResourceStructureName() + "]  Not Exists");
                throw new RuntimeException("Node Not Exists");
            }
        }

        //保存映射表数据
        objectMap.setParentObjectId(parentObjectMap.getObjectId());
        objectMap.setParentObjectIdType(parentObjectMap.getObjectIdType());
        objectMap.setLevelId(parentObjectMap.getLevelId() + 1);
        objectMap.setDimensionTypeId(parentObjectMap.getDimensionTypeId());
        objectMapMapper.insert(objectMap);

        List<EnergyResourceStructure> children = oneSubTree.getChildren();
        if(children != null && children.size() > 0) {
            for (EnergyResourceStructure child : children) {
                addNodesByTreeStructure(objectMap, child, onlyCopyName, curRootParentNode);
            }
        }

    }

    /** 将树型结构的多棵子树的所有节点，全部放入list集合中返回 */
    private List<EnergyResourceStructure> convertEnergyTreeToList(List<EnergyResourceStructure> addStructuresEnergyTree) {
        List<EnergyResourceStructure> list = new ArrayList<>();
        for (EnergyResourceStructure oneTree : addStructuresEnergyTree) {
            List<EnergyResourceStructure> thisTreeList = recursionGetEnergyList(oneTree);
            list.addAll(thisTreeList);
        }
        return list;
    }
    /**  递归将树的所有节点转成list  */
    private List<EnergyResourceStructure> recursionGetEnergyList(EnergyResourceStructure oneTree) {
        List<EnergyResourceStructure> list = new ArrayList<>();
        list.add(oneTree);
        List<EnergyResourceStructure> children = oneTree.getChildren();
        if(children != null && children.size() > 0) {
            for (EnergyResourceStructure child : children) {
                List<EnergyResourceStructure> childList = recursionGetEnergyList(child);
                list.addAll(childList);
            }
        }
        return list;
    }

    /** 校验节点的一些前置条件限定，节点可能是自定义节点，也可能是默认层级节点 */
    private void checkStructureNode(EnergyResourceStructure curRootParentNode, EnergyResourceStructure oneNode) {

        if(oneNode.getObjectIdType() == null) {
            log.error("ObjectIdType is null. node[id= " + oneNode.getId() + "; resourceStructureId= " + oneNode.getResourceStructureId() +
                    //"; ObjectIdType= " + oneNode.getObjectIdType() +
                    "; resourceStructureName= " + oneNode.getResourceStructureName() + ";]");
            throw new RuntimeException("ObjectIdType is null");
        }
        if(oneNode.getObjectId() == null || oneNode.getResourceStructureId() == null) {
            log.error("ObjectId or ResourceStructureId is null. node[id= " + oneNode.getId() + "; resourceStructureId= " + oneNode.getResourceStructureId() +
                    "; ObjectIdType= " + oneNode.getObjectIdType() + "; ObjectId= " + oneNode.getObjectId() +
                    "; resourceStructureName= " + oneNode.getResourceStructureName() + ";]");
            throw new RuntimeException("ObjectId is null");
        }
        if(StringUtils.isBlank(oneNode.getResourceStructureName())) {
            log.error("ResourceStructureName is null. node[id= " + oneNode.getId() + "; resourceStructureId= " + oneNode.getResourceStructureId() +
                    "; ObjectIdType= " + oneNode.getObjectIdType() + "; ObjectId= " + oneNode.getObjectId() +
                    "; resourceStructureName= " + oneNode.getResourceStructureName() + ";]");
            throw new RuntimeException("resourceStructureName is Blank");
        }
        if(curRootParentNode == null) {
            log.error("curRootParentNode is Null");
            throw new RuntimeException("curRootParentNode is Null");
        } else {
            if(curRootParentNode.getSourceCategory() == SourceCategory.COMPUTERRACK) {//如果是在机架类型节点下添加，有一些限制
                if(oneNode.getSourceCategory() == SourceCategory.COMPUTERRACK) {
                    log.error("机架节点下不允许再添加机架节点. node[id= " + oneNode.getId() + "; resourceStructureId= " + oneNode.getResourceStructureId() +
                            "; ObjectIdType= " + oneNode.getObjectIdType() + "; ObjectId= " + oneNode.getObjectId() +
                            "; resourceStructureName= " + oneNode.getResourceStructureName() + ";]" +
                            "，curRootParentNode[id= " + curRootParentNode.getId() + "; resourceStructureId= " + curRootParentNode.getResourceStructureId() +
                            "; ObjectIdType= " + curRootParentNode.getObjectIdType() + "; ObjectId= " + curRootParentNode.getObjectId() +
                            "; resourceStructureName= " + curRootParentNode.getResourceStructureName() + ";]");
                    throw new RuntimeException(getI18nMsg("energy.msg.dimension.computerrackcannotaddracknode")); //机架节点下不允许再添加机架节点
                }
                if(oneNode.getSourceCategory() == SourceCategory.RESOURCE_STRUCTURE) {
                    log.error("机架节点下不允许再添加层级节点. node[id= " + oneNode.getId() + "; resourceStructureId= " + oneNode.getResourceStructureId() +
                            "; ObjectIdType= " + oneNode.getObjectIdType() + "; ObjectId= " + oneNode.getObjectId() +
                            "; resourceStructureName= " + oneNode.getResourceStructureName() + ";]" +
                            "，curRootParentNode[id= " + curRootParentNode.getId() + "; resourceStructureId= " + curRootParentNode.getResourceStructureId() +
                            "; ObjectIdType= " + curRootParentNode.getObjectIdType() + "; ObjectId= " + curRootParentNode.getObjectId() +
                            "; resourceStructureName= " + curRootParentNode.getResourceStructureName() + ";]");
                    throw new RuntimeException(getI18nMsg("energy.msg.dimension.computerrackcannotaddstructurenode")); //机架节点下不允许再添加层级节点
                }
            }
        }
    }

    @Override
    public ResultObject<String> addExistEnergyStructures(StructureInfo newStructure) {
        return addExistStructuresNodes(newStructure);
    }

    @Override
    public ResultObject<String> addExistDefaultStructures(StructureInfo newStructure) {

        StructureInfo transferStructureInfo = new StructureInfo();
        transferStructureInfo.setParentNode(newStructure.getParentNode());
        transferStructureInfo.setOnlyCopyName(newStructure.getOnlyCopyName());
        transferStructureInfo.setPreserveTreeStructure(newStructure.getPreserveTreeStructure());

        List<DefaultResourceTreeDTO> addDefaultTree = newStructure.getAddStructuresDefault();
        transferStructureInfo.setAddStructuresEnergy(convertToEnergyResoureStructure(addDefaultTree));

        return addExistStructuresNodes(transferStructureInfo);
    }

    @Override
    public int batchDeleteComplexIndex(int objectId, int objectTypeId, List<Integer> businessTypeIds) {

        QueryWrapper<ComplexIndex> complexIndexQueryWrapper = new QueryWrapper<>();
        complexIndexQueryWrapper.eq("ObjectId", objectId);
        complexIndexQueryWrapper.eq("ObjectTypeId", objectTypeId);
        if(businessTypeIds != null && businessTypeIds.size() > 0) {
            complexIndexQueryWrapper.in("BusinessTypeId", businessTypeIds);
        }
        List<ComplexIndex> complexIndexs = energyDimensionComplexIndexMapper.selectList(complexIndexQueryWrapper);
        if(complexIndexs != null && complexIndexs.size() > 0) {
            return complexIndexMapper.deleteBatchIds(complexIndexs);
        }
        return 0;
    }

    /** 将 ResourceStructure集合 转换为 EnergyResourceStructure集合 */
    private List<EnergyResourceStructure> convertToEnergyResoureStructure(List<DefaultResourceTreeDTO> defaultTree) {
        List<EnergyResourceStructure> rtn = new ArrayList<>();
        if(defaultTree != null && defaultTree.size() > 0) {
            for (DefaultResourceTreeDTO resourceStructure : defaultTree) {
                EnergyResourceStructure ers = convertToEnergyResoureStructureObj(resourceStructure);
                if(resourceStructure != null && resourceStructure.getChildren() != null && resourceStructure.getChildren().size() >0) {
                    List<EnergyResourceStructure> childStructures = convertToEnergyResoureStructure(resourceStructure.getChildren());
                    ers.getChildren().addAll(childStructures);
                }
                rtn.add(ers);
            }
        }
        return rtn;
    }
    /** 将 ResourceStructure对象 转换为 EnergyResourceStructure对象 */
    private EnergyResourceStructure convertToEnergyResoureStructureObj(DefaultResourceTreeDTO rs) {
        EnergyResourceStructure ers = new EnergyResourceStructure();
        ers.setId(null);
        ers.setObjectId(rs.getObjectId());
        ers.setObjectIdType(convertObjectTypeIdToObjectIdType(rs.getObjectTypeId()).value());
        ers.setParentObjectId(rs.getParentResourceStructureId());
        ers.setParentObjectIdType(convertObjectTypeIdToObjectIdType(rs.getParentResourceStructureTypeId()).value());
        ers.setResourceStructureId(rs.getObjectId().longValue());
        ers.setResourceStructureName(rs.getResourceName());
        ers.setObjectTypeId(rs.getObjectTypeId());
        ers.setChildren(new ArrayList<>());
        //------------
        ers.setStructureNotes(null);
        ers.setNotes(null);
        ers.setDimensionTypeId(null);
        ers.setLevelId(null);
        ers.setLevelOfPath(null);
        return ers;
    }

    private SourceCategory convertObjectTypeIdToObjectIdType(int objectTypeId) {
        if(objectTypeId == SourceType.EQUIPMENT.value()) {//S6 设备类型
            return SourceCategory.EQUIPMENT;
        } else if(objectTypeId == SourceType.COMPUTERRACK.value()) {//S6 机架类型
            return SourceCategory.COMPUTERRACK;
        } else if(objectTypeId == SourceType.ITDEVICE.value()) {//S6 IT设备类型
            return SourceCategory.ITDEVICE;
        } else {
            return SourceCategory.RESOURCE_STRUCTURE;
        }
    }

    /** 直接返回true，认为前端传送的数据没问题，若要强校验可以实现此方法。
     *
     * objectIdType==1时，resourceStructureId存储的是 ResourceStructure 表中的 ResourceStructureId
     * objectIdType==2时，resourceStructureId存储的是 Energy_Structure 表中的 StructureId
     */
    private boolean nodeIsExist(Long resourceStructureId, Integer objectIdType) {
        return true;
    }

    //--------------- 添加已存在结节 end

    private String getI18nMsg(String key) {
        String i18nMsg = localeMessageSourceUtil.getMessage(key);
        return StringUtils.isBlank(i18nMsg) ? key : i18nMsg;
    }
}

