package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyRatingConfigExDTO;
import com.siteweb.energy.dto.EnergyRatingSumDTO;
import com.siteweb.energy.entity.EnergyRatingConfig;

import java.util.Date;
import java.util.List;

public interface EnergyRatingConfigService {
    /*获取等级评定配置单个或列表*/
    List<EnergyRatingConfig> getEnergyRatingConfigList(Integer objectId);

    /*获取某个等级评定配详细信息*/
    EnergyRatingConfigExDTO getEnergyRatingExByConfigId(Integer configId);

    /*新增一个等级评定配置*/
    Integer addEnergyRatingConfig(EnergyRatingConfig config);

    /*修改一个等级评定配置*/
    Integer modifyEnergyRatingConfig(EnergyRatingConfig config);

    /*删除一个等级评定配置*/
    Boolean deleteEnergyRatingConfig(Integer configId);

    /*计算能耗等级评定*/
    Double getEnergyRatingResult(Integer configId);

    /*计算能耗等级评定下载*/
    List<EnergyRatingSumDTO> getEnergyRatingResultTable(Integer configId);

    /*开始一个任务，更新配置信息*/
    Boolean startJob(Integer ratingConfigId, Date startTime, Date endTime, String workingCondition);

    /*结束一个任务，更新配置信息*/
    Boolean closeJob(Integer ratingConfigId);
}
