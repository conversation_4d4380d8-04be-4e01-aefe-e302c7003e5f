package com.siteweb.energy.service.impl;

import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.energy.dto.HistoryDatasResult;
import com.siteweb.energy.entity.EnergyCustomerTBReportMap;
import com.siteweb.energy.entity.EnergyCustomerTBReportRecord;
import com.siteweb.energy.entity.EnergyCustomerTbReport;
import com.siteweb.energy.mapper.EnergyTaiBoReportMapper;
import com.siteweb.energy.mapper.EnergyTaiBoReportRecordMapper;
import com.siteweb.energy.service.EnergyTaiBoReportService;
import com.siteweb.monitoring.entity.HistorySignal;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.*;

@Service
@Slf4j
public class EnergyTaiBoReportServiceImpl implements EnergyTaiBoReportService {

    @Value("${spring.influx.database}")
    private String database;

    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private EnergyTaiBoReportMapper energyTaiBoReportMapper;
    @Autowired
    private EnergyTaiBoReportRecordMapper energyTaiBoReportRecordMapper;
    public final static String ENERGY_ROOT = "upload-dir";
    /** CDG能源平台天然气月报表 */
    public final static Integer REPORTID_6 = 6;
    @Override
    public void doSchedule() {
        //获取报表信息
        List<EnergyCustomerTbReport> allReportInfo = energyTaiBoReportMapper.getAllReportInfo();
        Integer reportId = 0;
        String templatePath = "";
        String recordPath = "" ;
        String recordName = "";
        Integer dateRowId = 0;
        Integer dateCellId = 0;
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormatName = new SimpleDateFormat("yyyyMMdd");
        Date yesterdayStartTimeDate = getYesterdayStartTime();
        String yesterdayStartTime = dateFormat.format(yesterdayStartTimeDate);
        //获取当前时间，文档命名使用
        String yesterdayDate = dateFormatName.format(yesterdayStartTimeDate);
        String todayStartTime = dateFormat.format(getTodayStartTime());
        for (EnergyCustomerTbReport ect : allReportInfo) {
            reportId = ect.getReportid();
            switch (reportId){
                case 1:{dateRowId = 3;dateCellId = 1;break;}
                case 3:{dateRowId = 1;dateCellId = 26;break;}
                case 4:{dateRowId = 1;dateCellId = 10;break;}
                case 5, 6:{dateRowId = 1;dateCellId = 8;break;}
                default: log.error("EnergyTaiBoReportServiceImpl-doSchedule error Not Exist Report In DataBase!");
            }
            if(reportId == 6)//月报表
                continue;
            templatePath = ect.getPath();
            recordPath = ect.getRecordpath();
            recordName = ect.getReportname();
            //得到设备的id以及信号id信息
            List<EnergyCustomerTBReportMap> equipmentInfo = energyTaiBoReportMapper.getAllEquipmentInfoByReportId(reportId);
            //获取历史数据
            List<HistorySignal> result = getHistoryData(equipmentInfo, getYesterdayStartTime(), getTodayStartTime());
            try {
                FileInputStream file = new FileInputStream(new File(ENERGY_ROOT + templatePath));
                Workbook workbook = new XSSFWorkbook(file);
                // 设置单元格计算模式为自动计算
                workbook.setForceFormulaRecalculation(true);
                for (int i = 0; i < 4; i++) {
                    int sheetId = i;
                    if(reportId != 1 && i > 0)
                        continue;
                    Sheet sheet = workbook.getSheetAt(sheetId);
                    Row rowDate = sheet.getRow(dateRowId);
                    Cell cellDate = rowDate.getCell(dateCellId);
                    cellDate.setCellValue(yesterdayStartTime);
                    List<EnergyCustomerTBReportMap> resultTemp = equipmentInfo.stream().filter(map -> map.getSheetId() == sheetId).toList();
                    if(resultTemp.isEmpty())
                        continue;
                    for (EnergyCustomerTBReportMap etm :resultTemp) {
                        Row row = sheet.getRow(etm.getRowId() );
                        Cell cell = row.getCell(etm.getCellId() );
                        String signalId = etm.getEquipmentId() + "." + etm.getSignalId();
                        List<HistorySignal> tempResultList = result.stream()
                                .filter(j -> j.getSignalId().equals(signalId)).toList();
                        if(tempResultList.isEmpty())
                            continue;
                        int count = 1;
                        for (int k = 0; k <= 24; k= k + 2) {
                            String hour = String.valueOf(k);
                            if(k < 10)
                                hour = "0" + hour;
                            String hours = hour;
                            HistorySignal temp = tempResultList.stream().filter(j->j.getTime().substring(11, 13).equals(hours)).findFirst().orElse(null);
                            //第二天零点
                            if(k == 24){
                                hour = "00";
                                String hours2 = hour;
                                temp = tempResultList.stream().filter(j->j.getTime().substring(0,10).equals(todayStartTime) && j.getTime().substring(11, 13).equals(hours2)).findFirst().orElse(null);
                            }
                            if(temp == null)
                                continue;
                            cell.setCellValue(temp.getPointValue() == null ? 0d :Double.parseDouble(temp.getPointValue()));
                            row = sheet.getRow(etm.getRowId() + count);
                            cell = row.getCell(etm.getCellId() );
                            count++;
                        }
                    }
                }
                FileOutputStream out = new FileOutputStream(new File(ENERGY_ROOT + recordPath + "/" + recordName + yesterdayDate + ".xlsx"));
                workbook.write(out);
                out.close();

                //插入文件生成记录
                EnergyCustomerTBReportRecord reportRecord = new EnergyCustomerTBReportRecord();
                reportRecord.setReportId(reportId);
                reportRecord.setReportName(recordName + yesterdayDate + ".xlsx");
                reportRecord.setFilePath(recordPath);
                reportRecord.setCreateTime(date);
                energyTaiBoReportRecordMapper.insert(reportRecord);

            } catch (Exception e) {
                log.error("EnergyTaiBoReportServiceImpl-doSchedule error " , e);
            }
        }
    }

    @Override
    public List<EnergyCustomerTBReportRecord> GetTaiBoReportList(Date startTime, Date endTime,String reportIds) {
        List<EnergyCustomerTBReportRecord> allRecords = energyTaiBoReportMapper.GetTaiBoReportList(startTime,endTime);
        if (allRecords.size() == 0 ) return null;
        List<String> lstRes =  StringUtils.getStringToList(reportIds);
        return allRecords.stream().filter(i->lstRes.contains(i.getReportId().toString())).collect(Collectors.toList());
    }

    @Override
    public Object[] GetTaiBoReportListDownLoad(Integer pId) {
        EnergyCustomerTBReportRecord reportRecord = energyTaiBoReportMapper.getTaiBoReportById(pId);
        // 从目录中获取文件
        File file = new File(ENERGY_ROOT + reportRecord.getFilePath() + "/" + reportRecord.getReportName());
        InputStreamResource inputStreamResource = null;
        try {
            inputStreamResource = new InputStreamResource(new FileInputStream(file));
        } catch (FileNotFoundException e) {
            log.error("File not exists, pId=" + pId);
            throw new RuntimeException(e);
        }
        return new Object[] {inputStreamResource , reportRecord.getReportName(), String.valueOf(file.length())};
    }


    //获取历史数据
    public List<HistorySignal> getHistoryData(List<EnergyCustomerTBReportMap> equipmentInfo, Date startTime, Date endTime) {
        List<HistorySignal> result = new ArrayList<>();
        try {
            String selectDuration = "select * from historydatas where time >=$startTime and time <= $endTime and ( $someSignals ) group by SignalId ";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereSignalId = new StringBuilder();
            int count = 0;
            for (EnergyCustomerTBReportMap equipment : equipmentInfo) {
                if (bsWhereSignalId.length() == 0) {
                    bsWhereSignalId.append(" SignalId='").append((equipment.getEquipmentId() == null ? 0 : equipment.getEquipmentId()) + "." + (equipment.getSignalId() == null ? 0 : equipment.getSignalId())).append("' ");
                } else {
                    bsWhereSignalId.append(" or SignalId='").append((equipment.getEquipmentId() == null ? 0 : equipment.getEquipmentId()) + "." + (equipment.getSignalId() == null ? 0 : equipment.getSignalId())).append("' ");
                }
                count++;
                if (count == equipmentInfo.size() || count % 500 == 0) {

                    selectDuration = selectDuration.replace("$someSignals", bsWhereSignalId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                            .forDatabase(database)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        result = resultMapper.toPOJO(query, HistorySignal.class);
                    }
                    bsWhereSignalId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyTaiBoReportServiceImpl-getHistoryData error {}", e.getMessage());
        }
        return result;
    }

    // 获取当天的0点时间
    public static Date getTodayStartTime() {
        LocalDateTime todayStart = LocalDate.now().atTime(LocalTime.MIN);
        return Date.from(todayStart.atZone(ZoneId.systemDefault()).toInstant());
    }

    // 获取昨天的0点时间
    public static Date getYesterdayStartTime() {
        LocalDateTime yesterdayStart = LocalDate.now().minusDays(1).atTime(LocalTime.MIN);
        return Date.from(yesterdayStart.atZone(ZoneId.systemDefault()).toInstant());
    }

    @Override
    public boolean createMonthlyNaturalGasReport() {

        Date yesterdayStartTimeDate = getYesterdayStartTime();
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
        String yesterdayStartTime = dateFormat1.format(yesterdayStartTimeDate);

        List<EnergyCustomerTbReport> allReportInfo = energyTaiBoReportMapper.getAllReportInfo();
        if(allReportInfo == null || allReportInfo.size() < 1) {
            log.warn(" ---------  allReportInfo == null || allReportInfo.size() < 1 ");
            return false;
        }
        EnergyCustomerTbReport monthlyNaturalGasReport = allReportInfo.stream().filter(item -> item.getReportid() != null && item.getReportid().equals(REPORTID_6))
                .findFirst().orElse(null);//天然气月报表，reportid==6
        if(monthlyNaturalGasReport == null) {
            log.warn(" ---------  monthlyNaturalGasReport == null ");
            return false;
        }
        FileInputStream in = null;
        FileOutputStream out = null;
        Workbook workbook = null;
        //当前日期
        Date date = new Date();
        //Date date = DateUtil.stringToDate("2023-04-01 08:10:00");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String currentDate = dateFormat.format(yesterdayStartTimeDate);
        try {
            in = new FileInputStream(new File(ENERGY_ROOT + monthlyNaturalGasReport.getPath()));
            workbook = new XSSFWorkbook(in);
            int sheetId = 0;
            Sheet sheet = workbook.getSheetAt(sheetId);

            if(sheet == null){
                log.warn(" ---------  sheet == null ");
                return false;
            }
            //表头添加日期
            Row rowDate1 = sheet.getRow(1);
            Cell cellDate1 = rowDate1.getCell(8);
            cellDate1.setCellValue(yesterdayStartTime);
            //计算开始时间
            Date firstDayOfMonth = getFirstDayOfMonth(dateAddDays(date, -1));
            Date startTime = dateAddHours(firstDayOfMonth, 8);

            List<EnergyCustomerTBReportMap> equipmentInfo = energyTaiBoReportMapper.getAllEquipmentInfoByReportId(REPORTID_6);
            for (int i = 0; i < equipmentInfo.size(); i++) {
                EnergyCustomerTBReportMap map = equipmentInfo.get(i);
                List<HistoryDatasResult> result = getHistoryDataMonth(map, startTime, date);

                Integer rowId = map.getRowId();
                Integer cellId = map.getCellId();

                int size = result.size();
                for (int i1 = 0; i1 < size-1; i1++) {
                    HistoryDatasResult historyDatasResult = result.get(i1);
                    HistoryDatasResult historyDatasResultNext = result.get(i1 + 1);
                    Row row = sheet.getRow(rowId);
                    int cellDateId = 0; //日期在第一列
                    Cell cellDate = row.getCell(cellDateId);
                    String time = historyDatasResult.getTime();
                    if (StringUtils.isBlank(time)) {
                        log.info("time is null, map.SignalId=" + map.getEquipmentId() + "." + map.getSignalId());
                        rowId++;
                        continue;
                    } else {
                        String[] s = time.split(" ");
                        time = s.length == 2 ? s[0] : time;
                    }
                    cellDate.setCellValue(time);
                    Cell cell = row.getCell(cellId);
                    if(historyDatasResult.getResult() == null) {
                        log.info("result is null, map.SignalId=" + map.getEquipmentId() + "." + map.getSignalId());
                        rowId++;
                        continue;
                    }
                    Double thisMin = historyDatasResult.getResult() == null ? 0d : Double.parseDouble(historyDatasResult.getResult());
                    Double nextMin = historyDatasResultNext.getResult() == null ? 0d : Double.parseDouble(historyDatasResultNext.getResult());
                    Double value = NumberUtil.doubleAccuracy(nextMin - thisMin,2);
                    cell.setCellValue(value);
                    rowId++;
                }
            }
            String saveExcelFileName = monthlyNaturalGasReport.getReportname() + currentDate + ".xlsx";
            out = new FileOutputStream(new File(ENERGY_ROOT + monthlyNaturalGasReport.getRecordpath()  + "/" + saveExcelFileName));
            workbook.write(out);
            //插入文件生成记录
            EnergyCustomerTBReportRecord reportRecord = new EnergyCustomerTBReportRecord();
            reportRecord.setReportId(REPORTID_6);
            reportRecord.setReportName(saveExcelFileName);
            reportRecord.setFilePath(monthlyNaturalGasReport.getRecordpath());
            reportRecord.setCreateTime(date);
            energyTaiBoReportRecordMapper.insert(reportRecord);
        } catch (FileNotFoundException e) {
            log.error(" ---------  createMonthlyNaturalGasReport() throw FileNotFoundException:", e);
            return false;
        } catch (IOException e2) {
            log.error(" ---------  createMonthlyNaturalGasReport() throw IOException:", e2);
            return false;
        }  catch (Exception e3) {
            log.error(" ---------  createMonthlyNaturalGasReport() throw Exception:", e3);
            return false;
        } finally {
            // 关闭 XSSFWorkbook 对象
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException ee1) {
                    log.warn(" ---------  workbook.close() throw IOException:", ee1);
                }
            }
            // 关闭输出流
            if (out != null) {
                try {
                    out.close();
                } catch (IOException ee2) {
                    log.warn(" ---------  out.close() throw IOException:", ee2);
                }
            }
            // 关闭输入流
            if (in != null) {
                try {
                    in.close();
                } catch (IOException ee3) {
                    log.warn(" ---------  in.close() throw IOException:", ee3);
                }
            }
        }
        return true;
    }

    @Override
    public List<EnergyCustomerTbReport> GetTaiBoReportTemplate() {
        return energyTaiBoReportMapper.getAllReportInfo();
    }

    public List<HistoryDatasResult> getHistoryDataMonth(EnergyCustomerTBReportMap equipmentInfo, Date startTime, Date endTime) {
        List<HistoryDatasResult> result = new ArrayList<>();
        try {
            //处理设备id不存在的情况
            if(equipmentInfo == null || equipmentInfo.getEquipmentId() == null || equipmentInfo.getSignalId() == null) {
                return result;
            }
            String selectDuration = "select MIN(\"PointValue\") AS \"result\"  from historydatas where time >=$startTime and time <= $endTime and SignalId= '"
                    + equipmentInfo.getEquipmentId()+"."+equipmentInfo.getSignalId()+"' group by time(1d,8h) ";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(database)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .create();

            query = influxDB.query(queryBuilder);
            if (query != null)
                result = resultMapper.toPOJO(query, HistoryDatasResult.class);
        }catch (Exception e){
            log.error("EnergyTaiBoReportServiceImpl-getHistoryDataMonth throw exception:", e);
        }
        return result;
    }
}
