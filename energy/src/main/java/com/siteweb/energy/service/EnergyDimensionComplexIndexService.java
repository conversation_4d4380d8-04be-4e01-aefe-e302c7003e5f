package com.siteweb.energy.service;

import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.dto.ComplexIndexDTO;

import java.util.List;
import java.util.Map;

public interface EnergyDimensionComplexIndexService {

    ResultObject<ComplexIndexBusinessType> getEnergybusinesstree();
    ResultObject<ComplexIndexBusinessType> getEnergybusinesstreenotc();

    ResultObject<Map<Integer, List<ComplexIndexDefinition>>> getComplexindexdefinition();

    List<ComplexIndex> findEnergyComplexindexByResourceStructureIdAndType(Integer resourceStructureId, Integer structureTypeId);

    ResultObject<String> batchComplexIndexs(ComplexIndexDTO complexIndexDTO);

    List<ComplexIndex> findAllComplexindexByObjectTypeId(Integer objectTypeId);

    List<ComplexIndexDefinition> getComplexIndexDefinition(Integer businessTypeId,String complexIndexDefinitionTypeId);

    }
