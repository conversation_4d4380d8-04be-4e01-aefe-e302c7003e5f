package com.siteweb.energy.service.impl;

import cn.hutool.core.math.Calculator;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.energy.dto.EnergyHisDataResult;
import com.siteweb.energy.dto.EnergyHisHourDataResult;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyCustomerConfigMapper;
import com.siteweb.energy.service.*;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.*;

@Service
@Slf4j
public class EnergyHistoryDataServiceImpl implements EnergyHistoryDataService {

    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private InfluxDBManager influxDBManager;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private EnergyCustomerConfigMapper energyCustomerConfigMapper;
    @Autowired
    private EnergyDataConfigItemService energyDataConfigItemService;
    @Autowired
    @Lazy
    private EnergyHistoryDataHBDXService energyHistoryDataHBDXService;
    @Autowired
    @Lazy
    private EnergyComplexPreAlarmService energyComplexPreAlarmService;

    //所有能耗指标
    private Map<String, ComplexIndex> lstEnergyComplexIndex;
    private List<BusinessDefinitionMap> allBusinessDefinitionMap;

    private Map<String, EnergyHisHourData> lstEnergyHisHourData = new HashMap<>();
    private Map<String, EnergyHisHourData> lstEnergyHisHourData_Sum = new HashMap<>();
    private Map<String, EnergyHisHourData> lstEnergyHisHourData_Efficiency = new HashMap<>();
    private Map<String, EnergyHisHourData> lstEnergyHisHourData_Other = new HashMap<>();

    // 日数据队列
    private Map<String, EnergyHisDayData> lstEnergyHisDayData = new HashMap<>();
    // 月数据队列
    private Map<String, EnergyHisMonthData> lstEnergyHisMonthData = new HashMap<>();
    //能耗数据库小时表
    private static final String EnergyHisHourDataTable = "EnergyHisHourData";
    //能耗数据库天表
    private static final String EnergyHisDayDataTable = "EnergyHisDayData";
    //能耗数据库月表
    private static final String EnergyHisMonthDataTable = "EnergyHisMonthData";
    private static final String EnergyHisErrorDataTable = "EnergyHisErrorData";
    private double maxElecHour = 8000d;    //指标每小时值为8000 现场认为是异常数据。
    private int influxdbBatchSize = 8000;    //小时表，日表，月表存储时每次写多少个指标数据
    private int influxdbDataSaveBeginTable = 1;  //能耗存储的最小颗粒度表1小时表，2日表，3月表
    private Date dayStartTime, monthStartTime;

    List<Point> lstDayPoint = new ArrayList<>();
    List<Point> lstMonthPoint = new ArrayList<>();
    List<Point> lstErrorPoint = new ArrayList<>();
    private boolean reloadEnergyHistoryData = false;

    private boolean isAbnormalToPreAlarm = false;
    private Date lastEnergyHistoryDataTime = null;


    @Override
    public void startStatisticEnergyHistoryData() {
        //河北电信定制 执行自己的方法
        if (energyOverViewService.getCustomerConfigResult(2)) {
            energyHistoryDataHBDXService.startStatisticEnergyHistoryData();
            return;
        }

        //取每小时的异常值
        try {
            EnergyCustomerConfig result = energyCustomerConfigMapper.selectById(2);
            if (result == null || result.getNotes() == null || result.getNotes().equals("")) {
                maxElecHour = 8000d;
            } else {
                String[] paras = result.getNotes().split(",");          //现场notes字段8000,8000,1
                maxElecHour = Double.parseDouble(paras[0]);
                if (paras.length > 1) {
                    influxdbBatchSize = Integer.parseInt(paras[1]);
                }
                if (paras.length > 2) {
                    influxdbDataSaveBeginTable = Integer.parseInt(paras[2]);
                    if (influxdbDataSaveBeginTable < 0) {
                        reloadEnergyHistoryData = true;
                        return;       //第三个参数为<0 ，直接退出本次循环。
                    }
                }
            }
        } catch (Exception ex) {
            log.error("小时值配置异常：energy_customer_config-id2:", ex);
            return;
        }

        //所有能源类型
        List<ComplexIndexBusinessType> lstEnergyComplexIndexBusinessType = complexIndexBusinessTypeService.GetComplexIndexBusinessTypeByParentid(1);
        //所有能耗指标
        lstEnergyComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                .stream()
                .filter(i -> lstEnergyComplexIndexBusinessType
                        .stream()
                        .anyMatch(item -> item.getBusinessTypeId().equals(i.getBusinessTypeId())))
                .collect(Collectors.toMap(
                        i -> String.valueOf(i.getComplexIndexId()),// 使用 ComplexIndex 的 ID 作为键
                        i -> i // 使用 ComplexIndex 本身作为值
                ));

        //没有能耗指标则退出
        if (lstEnergyComplexIndex.size() == 0) {
            log.info("能耗数据转存，能耗指标数量为空");
            return;
        }
        //能源类型与指标类型的关系
        allBusinessDefinitionMap = energyComplexIndexManager.GetAllBusinessDefinitionMap();

        //获取本此清洗的开始,结束时间
        Date startTime = GetStartTimeFormEnergyDB();

        while (startTime.before(DateUtil.getLastHourStartOrEndTime(false))) {
            lstEnergyHisHourData_Sum = new HashMap<>();
            lstEnergyHisHourData_Other = new HashMap<>();
            lstEnergyHisHourData_Efficiency = new HashMap<>();

            // 判断是否新的一天，需要读取日月数据
            LocalDateTime localDateTime = startTime.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            int hour = localDateTime.getHour(); // 获取小时数（24小时制）
            int day = localDateTime.getDayOfMonth();

            if (reloadEnergyHistoryData) {
                setLstEnergyHisDayData(startTime);
                setLstEnergyHisMonthData(startTime);
                reloadEnergyHistoryData = false;
            } else {
                //刚启动或上次计算时间早于当前历史数据小时的前一小时
                if (lastEnergyHistoryDataTime == null || lastEnergyHistoryDataTime.before(DateUtil.dateAddHours(startTime, -1))){
                    setLstEnergyHisDayData(startTime);
                    setLstEnergyHisMonthData(startTime);
                } else {
                    // 正常情况下的处理
                    boolean needResetDayData = hour == 0; // 凌晨时刻
                    boolean needResetMonthData = needResetDayData && day == 1; // 月初凌晨

                    // 凌晨时清空日数据
                    if (needResetDayData) {
                        lstEnergyHisDayData = new HashMap<>();
                    } else if (lstEnergyHisDayData.size() == 0) {
                        // 其他时间但数据为空时重新加载
                        setLstEnergyHisDayData(startTime);
                    }

                    // 月初凌晨时清空月数据
                    if (needResetMonthData) {
                        lstEnergyHisMonthData = new HashMap<>();
                    } else if (lstEnergyHisMonthData.size() == 0) {
                        // 非月初凌晨但数据为空时重新加载
                        setLstEnergyHisMonthData(startTime);
                    }
                }
            }

            log.info("开始1小时能耗数据转存time=" + dateToString(startTime));
            //某1小时的数据转存
            processStatisticEnergyHistoryData(startTime);
            log.error("这不是报错！完成1小时能耗数据转存time=" + dateToString(startTime));

            //记录最后一批数据的时间
            lastEnergyHistoryDataTime = DateUtil.dateAddHours(startTime, 0);
            //开始时间增加1小时
            startTime = DateUtil.dateAddHours(startTime, 1);

            log.info("日志：本小时转移指标数量lstThisHourHistoryComplexIndex长度=" + lstEnergyHisHourData.size());
            lstEnergyHisHourData.clear();
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    //获取本次清洗数据的开始时间
    private Date GetStartTimeFormEnergyDB() {
        //return DateUtil.getLastHourStartOrEndTime(true);
        //return DateUtil.stringToDate("2023-02-05 4:00:00");
        //return DateUtil.getMonthStartTime(new Date());

        //上一小时的开始时间
        Date lastTime = DateUtil.getLastHourStartOrEndTime(true);

        QueryResult query = null;
        try {
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            ComplexIndex oneComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex().stream().filter(i -> i.getCalcCron() != null && i.getSaveCron() != null).findFirst().orElse(null);
            if (oneComplexIndex == null) return lastTime;
            Date lastHourTime = dateAddHours(new Date(), -24);   //约定查询最近24小时的
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("SELECT * FROM EnergyHisHourData where time>='"
                            + dateToString(lastHourTime) + "' and time < '"
                            + dateToString(new Date()) + "' and ComplexIndexId='" + oneComplexIndex.getComplexIndexId()
                            + "'  ORDER BY time DESC LIMIT 1")
                    .forDatabase(databaseEnergy)
                    .create();
            query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisHourData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisHourData.class);
                if (resultComplexIndexQuery.size() > 0) {
                    lastTime = stringToDate(resultComplexIndexQuery.get(0).getTime());
                    lastTime = dateAddHours(lastTime, 1);
                }
            }
        } catch (Exception ex) {
            log.error("能耗转存JOB 获取最后数据库时间错误；" + ex.getMessage());
            return lastTime;
        }
        return lastTime;
    }

    private void setLstEnergyHisDayData(Date startTime) {

        log.info("** 开始读取能耗日数据" + dateToString(startTime));
        long dTime1 = System.nanoTime();

        lstEnergyHisDayData.clear();
        Date dayTime = DateUtil.dateFirstTimeFormat(startTime);
        //Date monthTime = DateUtil.getMonthStartTime(startTime);
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        //取日数据
        String selectDuration = "select * from EnergyHisDayData where time =$startTime ";
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(dayTime))
                    .create();
            QueryResult query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisDayData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayData.class);
                if (!resultComplexIndexQuery.isEmpty()) {
                    lstEnergyHisDayData = resultComplexIndexQuery.stream().collect(Collectors.toMap(
                            EnergyHisDayData::getComplexIndexId,
                            i -> i // 使用 ComplexIndex 本身作为值
                    ));
                    log.info("读取日表指标数据数量：" + lstEnergyHisDayData.size());
                } else {
                    lstEnergyHisDayData = new HashMap<>();
                    log.info("读取日表指标数据，列表为空");
                }
            } else {
                log.error("读取日表指标数据，query=null");
            }
        } catch (Exception e) {
            log.error("EnergyHistoryDataHBDXServiceImpl-setLstEnergyHisDayMonthData error {}", e);
        }

        long dTime2 = System.nanoTime();
        log.info("** 读取能耗日数据完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
    }

    private void setLstEnergyHisMonthData(Date startTime) {
        log.info("** 开始读取能耗月数据" + dateToString(startTime));
        long dTime1 = System.nanoTime();

        lstEnergyHisMonthData.clear();
        Date monthTime = DateUtil.getMonthStartTime(startTime);
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        // 取月数据
        String selectDuration = "select * from EnergyHisMonthData where time =$startTime ";
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(monthTime))
                    .create();
            QueryResult query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisMonthData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthData.class);
                if (!resultComplexIndexQuery.isEmpty()) {
                    lstEnergyHisMonthData = resultComplexIndexQuery.stream().collect(Collectors.toMap(
                            EnergyHisMonthData::getComplexIndexId,
                            i -> i // 使用 ComplexIndex 本身作为值
                    ));
                    log.info("读取月表指标数据数量：" + lstEnergyHisMonthData.size());
                } else {
                    lstEnergyHisMonthData = new HashMap<>();
                    log.info("读取当月指标数据，列表为空");
                }
            } else {
                log.error("读取当月指标数据，query=null");
            }
            long dTime2 = System.nanoTime();
            log.info("** 读取能耗月数据完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
        } catch (Exception e) {
            log.error("EnergyHistoryDataHBDXServiceImpl-setLstEnergyHisDayMonthData error {}", e);
        }
    }

    //一次转存过程
    private void processStatisticEnergyHistoryData(Date startTime) {
        dayStartTime = DateUtil.dateFirstTimeFormat(startTime);
        monthStartTime = DateUtil.getMonthStartTime(startTime);
        lstDayPoint.clear();
        lstMonthPoint.clear();
        lstErrorPoint.clear();

        //处理能耗小时数据
        log.info("**开始处理能耗小时数据 time=" + dateToString(startTime));
        processEnergyHisHourData(startTime);
        log.info("**结束处理能耗小时数据 time=" + dateToString(startTime));

        //处理总量类指标日月
        log.info("**开始处理总量日月 time=" + dateToString(startTime));
        processSumEnergyHistoryData();
        log.info("**结束处理总量日月 time=" + dateToString(startTime));

        //处理其他类指标日月
        log.info("**开始处理其他类指标日月 time=" + dateToString(startTime));
        processOtherEnergyHistoryData(startTime);
        log.info("**结束处理其他类指标日月 time=" + dateToString(startTime));

        //处理其他类指标日月
        log.info("**开始处理效率类指标日月 time=" + dateToString(startTime));
        processEfficiencyEnergyHistoryData(startTime);
        log.info("**结束处理效率类指标日月 time=" + dateToString(startTime));

        // 日指标写库
        if (lstDayPoint.size() > 0 && influxdbDataSaveBeginTable <= 2) {
            try {
                log.info("** 指标日表数据队列开始入库，数量：" + lstDayPoint.size());
                long dTime1 = System.nanoTime();
                influxDBManager.batchInsertPoint(lstDayPoint, databaseEnergy, influxdbBatchSize);
                long dTime2 = System.nanoTime();
                log.error("** 这不是报错！指标日表数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒.数量" + lstDayPoint.size());
            } catch (Exception ex) {
                log.error("** 指标日表写入报错。指标数量=" + lstDayPoint.size(), ex);
            }
        }
        // 月指标写库
        if (lstMonthPoint.size() > 0 && influxdbDataSaveBeginTable <= 3) {
            try {
                log.info("** 指标月表数据队列开始入库，数量：" + lstMonthPoint.size());
                long dTime1 = System.nanoTime();
                influxDBManager.batchInsertPoint(lstMonthPoint, databaseEnergy, influxdbBatchSize);
                long dTime2 = System.nanoTime();
                log.error("** 这不是报错！指标月表数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒.数量" + lstMonthPoint.size());
            } catch (Exception ex) {
                log.error("** 指标月表写入报错。指标数量=" + lstMonthPoint.size(), ex);
            }
        }

        // 月效率指标写库UE
        if (lstErrorPoint.size() > 0) {
            try {
                log.info("** 指标异常值表数据队列开始入库，数量：" + lstErrorPoint.size());
                long dTime1 = System.nanoTime();
                influxDBManager.batchInsertPoint(lstErrorPoint, databaseEnergy, influxdbBatchSize);
                long dTime2 = System.nanoTime();
                log.error("** 这不是报错！指标异常值表数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒.数量" + lstErrorPoint.size());
            } catch (Exception ex) {
                log.error("** 指标异常值表写入报错。指标数量=" + lstErrorPoint.size(), ex);
            }
        }
        log.info("指标日值，月值计算完成写库完成！！");
    }

    private void processEnergyHisHourData(Date startTime) {
        Date endTime = DateUtil.dateAddSeconds(startTime, 60 * 60 - 1);

        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        String selectDuration = "select * from HistoryComplexIndex where time >=$startTime and time <= $endTime ";
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(database)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .create();
        QueryResult query = influxDB.query(queryBuilder);

        if (query != null) {
            List<HistoryComplexIndex> resultComplexIndexQuery = resultMapper.toPOJO(query, HistoryComplexIndex.class);
            if (!resultComplexIndexQuery.isEmpty()) {
                //能耗库 数据点仓库
                List<Point> lstPoint = new ArrayList<>();
                //List<Point> lstErrorPoint = new ArrayList<>();

                for (HistoryComplexIndex temp : resultComplexIndexQuery) {
                    //ComplexIndex thisIndex = lstEnergyComplexIndex.stream().filter(i -> i.getComplexIndexId().toString().equals(temp.getComplexIndexId())).findFirst().orElse(null);
                    ComplexIndex thisIndex = lstEnergyComplexIndex.getOrDefault(temp.getComplexIndexId(), null);

                    if (thisIndex == null) continue;   //此指标不是能耗指标
                    try {
                        //指标原始值
                        Double originalIndexValue = NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2);

                        //如果小时值<0 设置为前面几个小时平均值
                        if (originalIndexValue < 0 || originalIndexValue > maxElecHour || temp.getAbnormal().equals("1")) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(temp.getDateTime());
                            // 将 Calendar 的日期减去几小时
                            calendar.add(Calendar.HOUR_OF_DAY, -5);
                            Double cleanValue = GetCleanIndexValue(temp.getComplexIndexId(), calendar.getTime());
                            temp.setIndexValue(cleanValue.toString());

                            Point pointError = Point.measurement(EnergyHisErrorDataTable)
                                    .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                                    //.tag("CalcTime", dateToString(startTime))
                                    .tag("ComplexIndexId", String.valueOf(temp.getComplexIndexId()))
                                    .tag("Abnormal", temp.getAbnormal())
                                    .tag("BusinessTypeId", temp.getBusinessTypeId())
                                    .addField("IndexValue", NumberUtil.doubleAccuracy(cleanValue, 2))
                                    .addField("OriginalValue", originalIndexValue)
                                    .addField("Unit", temp.getUnit() == null ? "" : temp.getUnit())
                                    .build();

                            //加入异常数据点仓库
                            lstErrorPoint.add(pointError);
                        }

                        // 区分汇总、其他、效率类指标
                        BusinessDefinitionMap map = allBusinessDefinitionMap.stream().filter(
                                i -> i.getComplexIndexDefinitionId().equals(thisIndex.getComplexIndexDefinitionId())).findFirst().orElse(null);
                        if (map == null)
                            continue;

                        if (lstEnergyHisHourData.containsKey(temp.getComplexIndexId()))
                            continue;

                        EnergyHisHourData hourData = new EnergyHisHourData();
                        hourData.setAbnormal(temp.getAbnormal());
                        hourData.setTime(temp.getTime());
                        //hourData.setCalcTime(temp.getCalcTime());
                        hourData.setIndexValue(temp.getIndexValue());
                        hourData.setOriginalValue(originalIndexValue.toString());
                        hourData.setComplexIndexId(temp.getComplexIndexId());
                        hourData.setUnit(temp.getUnit());
                        hourData.setBusinessTypeId(temp.getBusinessTypeId());
                        lstEnergyHisHourData.put(temp.getComplexIndexId(), hourData);

                        Point point = Point.measurement(EnergyHisHourDataTable)
                                .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                                .tag("ComplexIndexId", String.valueOf(temp.getComplexIndexId()))
                                .tag("Abnormal", "0")
                                .tag("BusinessTypeId", temp.getBusinessTypeId())
                                .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2))
                                .addField("OriginalValue", originalIndexValue)
                                .addField("Unit", temp.getUnit() == null ? "" : temp.getUnit())
                                .build();
                        //加入数据点仓库
                        lstPoint.add(point);


                        //如果是总量或者分项，或者空调用电量 (字典1,3,4) 5重要设备 则直接求和
                        if (map.getComplexIndexDefinitionTypeId().equals(1) || map.getComplexIndexDefinitionTypeId().equals(3) || map.getComplexIndexDefinitionTypeId().equals(5)) {
                            //lstEnergyHisHourData_Sum.add(hourData);
                            lstEnergyHisHourData_Sum.put(hourData.getComplexIndexId(), hourData);
                        }
                        //效率和其他： 差值计算的需要拆分表达式重新计算， 非差值计算的取平均值
                        else if (map.getComplexIndexDefinitionTypeId().equals(100) || map.getComplexIndexDefinitionTypeId().equals(4)) {
                            //lstEnergyHisHourData_Other.add(hourData);
                            lstEnergyHisHourData_Other.put(hourData.getComplexIndexId(), hourData);
                        } else if (map.getComplexIndexDefinitionTypeId().equals(2)) {
                            //lstEnergyHisHourData_Efficiency.add(hourData);
                            lstEnergyHisHourData_Efficiency.put(hourData.getComplexIndexId(), hourData);
                        }
                    } catch (Exception ex) {
                        log.error("获取小时数据后加入lstPoint出错，", ex);
                    }
                }

                // 能耗小时数据存储
                if (lstPoint.size() > 0 && influxdbDataSaveBeginTable == 1) {   //influxdbDataSaveBeginTable 1存小时表，2存日表，3存月表
                    try {
                        log.info("** 能耗小时数据队列开始入库，数量：" + lstPoint.size());
                        long dTime1 = System.nanoTime();
                        influxDBManager.batchInsertPoint(lstPoint, databaseEnergy, influxdbBatchSize);
                        long dTime2 = System.nanoTime();
                        log.info("** 能耗小时数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
                    } catch (Exception ex) {
                        log.error("** 能耗小时表写入报错。小时指标数量=" + lstPoint.size() + "小时为：" + dateToString(startTime), ex);
                    }
                }
            } else {
                log.info("startStatisticEnergyHistoryData resultComplexIndexQuery empty，StartTime=" + dateToString(startTime));
            }
        } else {
            log.info("HistoryComplexIndex query查询为空=null，StartTime=" + dateToString(startTime));
        }

    }

    //获取清洗计算值 最近3次的平均值
    private Double GetCleanIndexValue(String complexIndexId, Date startTime) {
        Double cleanIndexValue = 0d;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        List<EnergyHisHourDataResult> resultComplexIndexQuery = new ArrayList<>();

        String selectDuration = "select mean(IndexValue) as result from EnergyHisHourData where time >= $startTime and ComplexIndexId= $complexIndexId group by ComplexIndexId ORDER BY time DESC LIMIT 3";
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(databaseEnergy)
                .bind("complexIndexId", complexIndexId)
                .bind("startTime", dateToString(startTime))
                .create();
        try {
            QueryResult query = influxDB.query(queryBuilder);
            if (query != null) {
                resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisHourDataResult.class);
                if (resultComplexIndexQuery.size() > 0) {
                    cleanIndexValue = NumberUtil.doubleAccuracy(Double.parseDouble(resultComplexIndexQuery.get(0).getResult()), 2);
                }
            }
            return cleanIndexValue;
        } catch (Exception ex) {
            log.error("GetCleanIndexValue ComplexIndexId=" + complexIndexId + " startTime=" + dateToString(startTime), ex);
            return cleanIndexValue;
        }
    }

    //处理总量日月
    private void processSumEnergyHistoryData() {
        //存在汇总的小时值
        if (lstEnergyHisHourData_Sum.size() > 0) {
            for (EnergyHisHourData thisHourData : lstEnergyHisHourData_Sum.values()) {
                //日查找
                //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
                EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(thisHourData.getComplexIndexId(), null);
                if (thisDayValue != null) {
                    double value = NumberUtil.formatNumeric(thisDayValue.getIndexValue()) + NumberUtil.formatNumeric(thisHourData.getIndexValue());
                    thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
                } else {
                    thisDayValue = new EnergyHisDayData();
                    thisDayValue.setComplexIndexId(thisHourData.getComplexIndexId());
                    thisDayValue.setIndexValue(thisHourData.getIndexValue());
                    thisDayValue.setUnit(thisHourData.getUnit());
                    thisDayValue.setTime(dateToString(dayStartTime));

                    lstEnergyHisDayData.put(thisDayValue.getComplexIndexId(), thisDayValue);
                }

                Point point = Point.measurement(EnergyHisDayDataTable)
                        .time(DateUtil.localToUTC(dayStartTime).getTime(), TimeUnit.MILLISECONDS)
                        .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                        .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                        .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                        .build();
                //加入日数据点仓库
                lstDayPoint.add(point);

                // 月查找
                //EnergyHisDataResult thisMonthValue = lstEnergyHisMonthData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
                EnergyHisMonthData thisMonthValue = lstEnergyHisMonthData.getOrDefault(thisHourData.getComplexIndexId(), null);

                if (thisMonthValue != null) {
                    double value = NumberUtil.formatNumeric(thisMonthValue.getIndexValue()) + NumberUtil.formatNumeric(thisHourData.getIndexValue());
                    thisMonthValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
                } else {
                    thisMonthValue = new EnergyHisMonthData();
                    thisMonthValue.setComplexIndexId(thisHourData.getComplexIndexId());
                    thisMonthValue.setIndexValue(thisHourData.getIndexValue());
                    thisMonthValue.setUnit(thisHourData.getUnit());
                    thisMonthValue.setTime(dateToString(monthStartTime));

                    lstEnergyHisMonthData.put(thisMonthValue.getComplexIndexId(), thisMonthValue);
                }

                Point point2 = Point.measurement(EnergyHisMonthDataTable)
                        .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                        .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                        .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                        .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                        .build();
                //加入日数据点仓库
                lstMonthPoint.add(point2);
            }
        } else {
            log.info("当前小时数据属于汇总类指标的个数为0.");
        }
    }

    //处理其他类指标日月
    private void processOtherEnergyHistoryData(Date calTime) {
        if (lstEnergyHisHourData_Other.size() == 0) {
            log.info("当前小时数据属于取平均值类指标的个数为0.");
            return;
        }
        // 判断是否新的一天，需要读取日月数据
        LocalDateTime localDateTime = calTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        int hour = localDateTime.getHour(); // 获取小时数（24小时制）
        int day = localDateTime.getDayOfMonth();

        // lstEfficiencyAndOther中的只需要求平均值。
        for (EnergyHisHourData thisHourData : lstEnergyHisHourData_Other.values()) {
            //日查找
            //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisDayValue != null) {
                double value = (NumberUtil.formatNumeric(thisDayValue.getIndexValue()) * hour + NumberUtil.formatNumeric(thisHourData.getIndexValue())) / (hour + 1);
                thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
            } else {
                thisDayValue = new EnergyHisDayData();
                thisDayValue.setComplexIndexId(thisHourData.getComplexIndexId());
                thisDayValue.setIndexValue(thisHourData.getIndexValue());
                thisDayValue.setUnit(thisHourData.getUnit());
                thisDayValue.setTime(dateToString(dayStartTime));

                lstEnergyHisDayData.put(thisDayValue.getComplexIndexId(), thisDayValue);
            }

            Point point = Point.measurement(EnergyHisDayDataTable)
                    .time(DateUtil.localToUTC(dayStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                    .build();
            //加入日数据点仓库
            lstDayPoint.add(point);

            // 月查找
            //EnergyHisDataResult thisMonthValue = lstEnergyHisMonthData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            EnergyHisMonthData thisMonthValue = lstEnergyHisMonthData.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisMonthValue != null) {
                double value = (NumberUtil.formatNumeric(thisMonthValue.getIndexValue()) * ((day - 1) * 24 + hour) + NumberUtil.formatNumeric(thisHourData.getIndexValue())) / ((day - 1) * 24 + hour + 1);
                thisMonthValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
            } else {
                thisMonthValue = new EnergyHisMonthData();
                thisMonthValue.setComplexIndexId(thisHourData.getComplexIndexId());
                thisMonthValue.setIndexValue(thisHourData.getIndexValue());
                thisMonthValue.setUnit(thisHourData.getUnit());
                thisMonthValue.setTime(dateToString(monthStartTime));

                lstEnergyHisMonthData.put(thisMonthValue.getComplexIndexId(), thisMonthValue);
            }

            Point point2 = Point.measurement(EnergyHisMonthDataTable)
                    .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                    .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                    .build();
            //加入日数据点仓库
            lstMonthPoint.add(point2);
        }
    }

    //处理效率类指标日月
    private void processEfficiencyEnergyHistoryData(Date calTime) {
        if (lstEnergyHisHourData_Efficiency.size() == 0) {
            log.info("当前小时数据属于效率类指标的个数为0.");
            return;
        }

        for (EnergyHisHourData thisHourData : lstEnergyHisHourData_Efficiency.values()) {
            ComplexIndex thisIndex = lstEnergyComplexIndex.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisIndex == null || thisIndex.getExpression() == null || thisIndex.getExpression().length() == 0)
                continue;
            String Expression = thisIndex.getExpression().toLowerCase();
            Pattern p = Pattern.compile("(?<=ci\\(|last\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(thisIndex.getExpression().toLowerCase());

            //日查找
            while (m.find()) {
                int complexId = Integer.parseInt(m.group());
                //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(Integer.toString(complexId))).findFirst().orElse(null);
                EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(Integer.toString(complexId), null);
                String replaceContent = thisDayValue == null ? "0" : thisDayValue.getIndexValue();
                Expression = Expression.replace("ci(" + complexId + ")", replaceContent).replace("last(" + complexId + ")", replaceContent);
            }

            double indexValue = CalculatorConvert(thisIndex.getComplexIndexId().toString(), Expression);
            //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisDayValue != null) {
                thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(indexValue, 2).toString());
            } else {
                thisDayValue = new EnergyHisDayData();
                thisDayValue.setComplexIndexId(thisHourData.getComplexIndexId());
                thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(indexValue, 2).toString());
                thisDayValue.setUnit(thisHourData.getUnit());
                thisDayValue.setTime(dateToString(dayStartTime));
                lstEnergyHisDayData.put(thisDayValue.getComplexIndexId(), thisDayValue);
            }

            Point point = Point.measurement(EnergyHisDayDataTable)
                    .time(DateUtil.localToUTC(dayStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                    .build();
            //加入日数据点仓库
            lstDayPoint.add(point);

            m = p.matcher(thisIndex.getExpression().toLowerCase());
            while (m.find()) {
                int complexId = Integer.parseInt(m.group());
                EnergyHisMonthData thisMonthValue = lstEnergyHisMonthData.getOrDefault(Integer.toString(complexId), null);
                String replaceContent = thisMonthValue == null ? "0" : thisMonthValue.getIndexValue();
                Expression = Expression.replace("ci(" + complexId + ")", replaceContent).replace("last(" + complexId + ")", replaceContent);
            }

            indexValue = CalculatorConvert(thisIndex.getComplexIndexId().toString(), Expression);
            Point point2 = Point.measurement(EnergyHisMonthDataTable)
                    .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisIndex.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisIndex.getBusinessTypeId().toString())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(indexValue, 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(indexValue, 2))
                    .addField("Unit", "")
                    .build();
            //加入数据点仓库
            lstMonthPoint.add(point2);
        }
    }

    private Double CalculatorConvert(String complexIndexId, String expression) {
        Double result = 0d;
        try {
            result = NumberUtil.doubleAccuracy(Calculator.conversion(expression), 2);
        } catch (Exception ex) {
            log.info("CalculatorConvert complexIndexId=" + complexIndexId + " Expression=" + expression);
        }
        return result;
    }

    /**
     * 用能数据手动补录 输入某时间段用能总量
     */
    @Override
    public boolean manualEnergyHisDataInfluxdb(Integer complexIndexId,Date startTime, Date endTime, Double totalValue) {
        try{
            log.info("start manualEnergyHisDataInfluxdb complexIndexId="+complexIndexId+" startTime="+dateToString(startTime));
            return modifyHourDayMonthData(complexIndexId,startTime, endTime, totalValue,null);
        }catch(Exception ex){
            log.error("start manualEnergyHisDataInfluxdb complexIndexId="+complexIndexId+" startTime="+dateToString(startTime));
        }
        return false;
    }

    /**
     * 小时异常数据修改
     */
    @Override
    public boolean modifyEnergyHisHourDataInfluxdb(Integer complexIndexId, Date startTime, Double value){
        //插入模拟数据
        /*
        List<Point> lstPoint1 = new ArrayList<>();
        Point point1 = Point.measurement(EnergyHisErrorDataTable)
                .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                //.tag("CalcTime", dateToString(startTime))
                .tag("ComplexIndexId", complexIndexId.toString())
                .tag("Abnormal", "1")
                .tag("BusinessTypeId", "2")
                .addField("IndexValue", NumberUtil.doubleAccuracy(value, 2))
                .addField("OriginalValue", NumberUtil.doubleAccuracy(77d, 2))
                .addField("Unit", "kwh")
                .build();
        lstPoint1.add(point1);
        influxDBManager.batchInsertPoint(lstPoint1,databaseEnergy);
        */

        Double result = 0d;
        String selectDuration = "select *  from EnergyHisErrorData where time =$startTime and ComplexIndexId='"+ complexIndexId+"'";
        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(databaseEnergy)
                .bind("startTime", dateToString(startTime))
                .create();

        query  =  influxDB.query(queryBuilder);
        if (query != null) {
            List<EnergyHisErrorData> resultComplexIndexQuery = new ArrayList<>();
            resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisErrorData.class,EnergyHisErrorDataTable);
            if (!resultComplexIndexQuery.isEmpty()) {
                EnergyHisErrorData originRecord =  resultComplexIndexQuery.get(0);
                Date endTime = DateUtil.dateAddSeconds(DateUtil.dateAddHours(startTime,1),-1);
                Double originValue = NumberUtil.doubleAccuracy(Double.parseDouble(originRecord.getOriginalValue()),2);

                //修正异常表数据
                List<Point> lstPoint = new ArrayList<>();
                Point point = Point.measurement(EnergyHisErrorDataTable)
                        .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                        //.tag("CalcTime", dateToString(startTime))
                        .tag("ComplexIndexId", complexIndexId.toString())
                        .tag("Abnormal", originRecord.getAbnormal())
                        .tag("BusinessTypeId", originRecord.getBusinessTypeId().toString())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(value, 2))
                        .addField("OriginalValue", originValue)
                        .addField("Unit", originRecord.getUnit() == null ? "" : originRecord.getUnit())
                        .build();
                lstPoint.add(point);
                influxDBManager.batchInsertPoint(lstPoint,databaseEnergy);

                //修正小时，日，月表数据
                return modifyHourDayMonthData(complexIndexId,startTime, endTime, value,originValue);
            }
        } else{
            log.info("error modifyEnergyHisHourDataInfluxdb complexIndexId="+complexIndexId+" startTime="+dateToString(startTime)+" is null in influxdb EnergyHisErrorData");
            return false;
        }
        return true;
    }

    boolean modifyHourDayMonthData(Integer complexIndexId,Date startTime, Date endTime, Double totalValue,Double originValue){
        boolean result = true;
        boolean isModifyErrorData = originValue==null ? false:true;

        ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i->i.getComplexIndexId().equals(complexIndexId)).findFirst().orElse(null);
        if (thisComplexIndex == null) return false;
        BusinessDefinitionMap map =  energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i->i.getComplexIndexDefinitionId().equals(thisComplexIndex.getComplexIndexDefinitionId())).findFirst().orElse(null);
        if (map == null) return false;
        String funName = "";
        if (map.getComplexIndexDefinitionTypeId().equals(1) || map.getComplexIndexDefinitionTypeId().equals(3))
            funName = "sum";
        else
            funName = "mean";

        // 将Date转换为LocalDateTime
        LocalDateTime startDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 计算startTime和endTime之间的小时数
        long hoursBetween = Duration.between(startDateTime, endDateTime).toHours();
        // 计算每小时的值
        double valuePerHour = 0d;

        if (isModifyErrorData) {
            valuePerHour = totalValue;
        } else {    //时间段手动录入能耗
            if (hoursBetween > 0) {
                valuePerHour = NumberUtil.doubleAccuracy(totalValue / hoursBetween, 2);
                originValue = valuePerHour;
            }
        }

        //批量写入小时表
        List<Point> lstPoint = new ArrayList<>();
        Date tempTime =startTime;
        while(tempTime.before(endTime)) {
            Point point = Point.measurement(EnergyHisHourDataTable)
                    .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", dateToString(tempTime))
                    .tag("ComplexIndexId", complexIndexId.toString())
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(valuePerHour, 2))
                    .addField("OriginalValue", originValue)
                    .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                    .build();
            lstPoint.add(point);

            tempTime = DateUtil.dateAddHours(tempTime,1);
        }

        if (lstPoint.size()>0)
            influxDBManager.batchInsertPoint(lstPoint,databaseEnergy);

        //批量写入日数据
        tempTime = DateUtil.dateFirstTimeFormat(startTime);
        lstPoint.clear();
        Double valuePerDay = 0d;

        //获取第一天
        Double firstDayValue = GetSumValueOfComplexIndex(EnergyHisHourDataTable,tempTime,DateUtil.dateAddDays(tempTime,1),complexIndexId,funName);
        Point point = Point.measurement(EnergyHisDayDataTable)
                .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                //.tag("CalcTime", dateToString(tempTime))
                .tag("ComplexIndexId", complexIndexId.toString())
                .tag("Abnormal", "0")
                .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                .addField("IndexValue", NumberUtil.doubleAccuracy(firstDayValue, 2))
                .addField("OriginalValue", firstDayValue)
                .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                .build();
        lstPoint.add(point);

        //时间段手动录入能耗
        if (!isModifyErrorData) {
            //获取最后一天
            Date endDayTime = DateUtil.dateFirstTimeFormat(endTime);
            Double endDayValue = GetSumValueOfComplexIndex(EnergyHisHourDataTable, endDayTime, DateUtil.dateAddDays(endDayTime, 1), complexIndexId, funName);
            point = Point.measurement(EnergyHisDayDataTable)
                    .time(DateUtil.localToUTC(endDayTime).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", dateToString(endDayTime))
                    .tag("ComplexIndexId", complexIndexId.toString())
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(endDayValue, 2))
                    .addField("OriginalValue", endDayValue)
                    .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                    .build();
            lstPoint.add(point);
            tempTime = DateUtil.dateAddDays(tempTime, 1);
            while (tempTime.before(endDayTime)) {
                point = Point.measurement(EnergyHisDayDataTable)
                        .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                        //.tag("CalcTime", dateToString(tempTime))
                        .tag("ComplexIndexId", complexIndexId.toString())
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(valuePerHour * 24, 2))
                        .addField("OriginalValue", valuePerHour * 24)
                        .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                        .build();
                lstPoint.add(point);

                tempTime = DateUtil.dateAddDays(tempTime, 1);
            }
        }
        if (lstPoint.size()>0)
            influxDBManager.batchInsertPoint(lstPoint,databaseEnergy);

        //批量写入月数据
        tempTime = DateUtil.getMonthStartTime(startTime);
        lstPoint.clear();
        Double valuePerMonth = 0d;

        //获取第一月
        Double firstMonthValue = GetSumValueOfComplexIndex(EnergyHisDayDataTable,tempTime,DateUtil.dateAddMonth(tempTime,1),complexIndexId,funName);
        point = Point.measurement(EnergyHisMonthDataTable)
                .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                //.tag("CalcTime", dateToString(tempTime))
                .tag("ComplexIndexId", complexIndexId.toString())
                .tag("Abnormal", "0")
                .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                .addField("IndexValue", NumberUtil.doubleAccuracy(firstMonthValue, 2))
                .addField("OriginalValue", firstMonthValue)
                .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                .build();
        lstPoint.add(point);

        //时间段手动录入能耗
        if (!isModifyErrorData) {
            //获取最后一月
            Date endMonthTime = DateUtil.getMonthStartTime(endTime);
            Double endMonthValue = GetSumValueOfComplexIndex(EnergyHisDayDataTable, endMonthTime, DateUtil.dateAddMonth(endMonthTime, 1), complexIndexId, funName);
            point = Point.measurement(EnergyHisMonthDataTable)
                    .time(DateUtil.localToUTC(endMonthTime).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", dateToString(endMonthTime))
                    .tag("ComplexIndexId", complexIndexId.toString())
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(endMonthValue, 2))
                    .addField("OriginalValue", endMonthValue)
                    .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                    .build();
            lstPoint.add(point);
            tempTime = DateUtil.dateAddMonth(tempTime, 1);

            while (tempTime.before(endMonthTime)) {
                // 计算startTime和endTime之间的小时数
                // 将Date转换为LocalDateTime
                startDateTime = tempTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                endDateTime = DateUtil.dateAddMonth(tempTime, 1).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                // 计算startTime和endTime之间的小时数
                hoursBetween = Duration.between(startDateTime, endDateTime).toHours();

                point = Point.measurement(EnergyHisMonthDataTable)
                        .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                        //.tag("CalcTime", dateToString(tempTime))
                        .tag("ComplexIndexId", complexIndexId.toString())
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisComplexIndex.getBusinessTypeId().toString())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(valuePerHour * hoursBetween, 2))
                        .addField("OriginalValue", valuePerHour * hoursBetween)
                        .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                        .build();
                lstPoint.add(point);

                tempTime = DateUtil.dateAddMonth(tempTime, 1);
            }
        }
        if (lstPoint.size()>0)
            influxDBManager.batchInsertPoint(lstPoint,databaseEnergy);
        return result;
    }

    //获取某时间段指标的值 和或者平均值
    private Double GetSumValueOfComplexIndex(String tableName, Date startTime, Date endTime,Integer complexIndexId,String funName){
        Double result = 0d;
        String selectDuration = "select "+funName+"(IndexValue) as result from "+ tableName+" where time >=$startTime and time <$endTime and ComplexIndexId='"+ complexIndexId+"'";
        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(databaseEnergy)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .create();

        query  =  influxDB.query(queryBuilder);
        if (query != null) {
            List<EnergyHisDataResult> resultComplexIndexQuery = new ArrayList<>();
            resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataResult.class,tableName);
            if (!resultComplexIndexQuery.isEmpty()) {
                for(EnergyHisDataResult temp :resultComplexIndexQuery){
                    try{
                        return  NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()),2);
                    } catch (Exception e) {
                        return 0d;
                    }
                }
            }
        }
        return result;
    }

    @Override
    public void energyInfluxDBHistoryData() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, Calendar.JANUARY);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startOfYear = calendar.getTime();

            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery("SELECT * FROM EnergyHisMonthData where time>='"
                            + dateToString(startOfYear) + "'")
                    .forDatabase(databaseEnergy)
                    .create();
            influxDB.query(queryBuilder);
        } catch (Exception ex) {
            log.error("energyInfluxDBHistoryData " + ex.getMessage());
        }
    }
}
