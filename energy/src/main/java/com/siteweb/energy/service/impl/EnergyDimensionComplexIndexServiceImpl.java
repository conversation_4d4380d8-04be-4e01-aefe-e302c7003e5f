package com.siteweb.energy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import com.siteweb.complexindex.mapper.BusinessDefinitionMapMapper;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.mapper.ComplexIndexDefinitionMapper;
import com.siteweb.complexindex.mapper.ComplexIndexMapper;
import com.siteweb.complexindex.service.ComplexIndexBusinessObjectMapService;
import com.siteweb.complexindex.service.ComplexIndexDefinitionService;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.complexindex.service.impl.ComplexIndexServiceImpl;
import com.siteweb.energy.dto.ComplexIndexDTO;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.enumeration.SourceCategory;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyDimensionComplexIndexMapper;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.service.EnergyDimensionComplexIndexService;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyDimensionComplexIndexServiceImpl implements EnergyDimensionComplexIndexService {

    /** 自定义节点的objectTypeId约定为200 */
    public static final int DIMENSION_STRUCTURE_OBJECTTYPEID = 200;
//    public static final Integer[] energyBusinessTypeIds = new Integer[]{1,2,3,4}; //注意：如果此处有修改，需要将sql中有写死的地方也一致进行修改
//    public static final Integer[] energyElecDefinitionIds = new Integer[]{5,6,7,8,9,22,23,24,25,26,27}; //能耗电相关指标类型
//    public static final Integer[] energyWaterDefinitionIds = new Integer[]{28,29,30,31,32,33,34,35,36,37,38,39,40,41,42};//能耗水相关指标类型
//    public static final Integer[] energyGasDefinitionIds = new Integer[]{43};//能耗气相关指标类型

    @Autowired
    private ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;
    @Autowired
    private ComplexIndexDefinitionService complexIndexDefinitionService;
    @Autowired
    ComplexIndexMapper complexIndexMapper;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private ComplexIndexBusinessObjectMapService complexIndexBusinessObjectMapService;
    @Autowired
    private EnergyStructureService structureService;
    @Autowired
    private EnergyDimensionComplexIndexMapper energyDimensionComplexIndexMapper;
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    private ResourceObjectManager resourceObjectManager;

    @Autowired
    private ComplexIndexDefinitionMapper complexIndexDefinitionMapper;
    @Autowired
    private BusinessDefinitionMapMapper businessDefinitionMapMapper;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;

    @Autowired
    private ComplexIndexService complexIndexService;

    @Override
    public ResultObject<ComplexIndexBusinessType> getEnergybusinesstree() {
        List<ComplexIndexBusinessType> all = complexIndexBusinessTypeMapper.selectList(null);
        List<ComplexIndexBusinessType> energyBusinessTypes = new ArrayList<>();
        LinkedHashMap<Integer, ComplexIndexBusinessType> map = new LinkedHashMap<>();
        Integer rootId = 0;
        for (ComplexIndexBusinessType oneType : all) {
            if(oneType.getParentId().equals(1) || oneType.getBusinessTypeId().equals(1)) {
                energyBusinessTypes.add(oneType);
                map.put(oneType.getBusinessTypeId(), oneType);
                if(oneType.getParentId().equals(0)) {
                    rootId = oneType.getBusinessTypeId();
                }
            }
        }

        for (ComplexIndexBusinessType complexIndexBusinessType : energyBusinessTypes) {
            if (complexIndexBusinessType.getParentId().intValue() != 0) {
                if (map.containsKey(complexIndexBusinessType.getParentId())) {
                    ComplexIndexBusinessType param = map.get(complexIndexBusinessType.getParentId());
                    param.getChildren().add(complexIndexBusinessType);
                }
            }
        }

        return new ResultObject<>(map.get(rootId));
    }

    @Override
    public ResultObject<ComplexIndexBusinessType> getEnergybusinesstreenotc() {
        List<ComplexIndexBusinessType> all = complexIndexBusinessTypeMapper.selectList(null);

        List<ComplexIndexBusinessType> children = new ArrayList<>();
        ComplexIndexBusinessType rootType = all.stream().filter(i->i.getBusinessTypeId()==1).findFirst().orElse(null);
        if (rootType == null) return new ResultObject<ComplexIndexBusinessType>();

        for (ComplexIndexBusinessType oneType : all) {
            if(oneType.getParentId().equals(1)
                    && oneType.getDescription() != null  && !oneType.getDescription().equals("")
                    && !oneType.getDescription().equals("3")
                    && !oneType.getDescription().equals("31")) {
                children.add(oneType);
            }
        }
        rootType.setChildren(children);
        return new ResultObject<ComplexIndexBusinessType>(rootType);
    }

    @Override
    public ResultObject<Map<Integer, List<ComplexIndexDefinition>>> getComplexindexdefinition() {
        Map<Integer, List<ComplexIndexDefinition>> map = new LinkedHashMap<>();

        List<ComplexIndexBusinessType> all = complexIndexBusinessTypeMapper.selectList(null);
        List<ComplexIndexBusinessType> energyBusinessTypes = all.stream().filter(i->i.getParentId().equals(1)).collect(Collectors.toList());
        List<ComplexIndexDefinition> allComplexIndexDefinition = complexIndexDefinitionMapper.selectList(null);
        List<BusinessDefinitionMap> allBusinessDefinitionMap = businessDefinitionMapMapper.selectList(null);
        for(ComplexIndexBusinessType type:energyBusinessTypes){
            List<BusinessDefinitionMap> maps =  allBusinessDefinitionMap.stream().filter(i->i.getBusinessTypeId().equals(type.getBusinessTypeId())).collect(Collectors.toList());
            List<ComplexIndexDefinition> thisTypeDefinition = new ArrayList<>();
            for (BusinessDefinitionMap thisMap:maps){
                ComplexIndexDefinition oneDefinition = allComplexIndexDefinition.stream().filter(i->i.getComplexIndexDefinitionId().equals(thisMap.getComplexIndexDefinitionId())).findFirst().orElse(null);
                if (oneDefinition != null)
                    thisTypeDefinition.add(oneDefinition);
            }

            map.put(type.getBusinessTypeId(),thisTypeDefinition);
        }

        return new ResultObject<>(map);
    }

    @Override
    public List<ComplexIndex> findEnergyComplexindexByResourceStructureIdAndType(Integer resourceStructureId, Integer structureTypeId) {
        return energyDimensionComplexIndexMapper.findByResourceStructureIdAndType(resourceStructureId, structureTypeId);
    }

    @Override
    public ResultObject<String> batchComplexIndexs(ComplexIndexDTO complexIndexDTO) {
        ComplexIndex complexIndex = new ComplexIndex();
        if (ObjectUtil.isNull(complexIndexDTO.getBatchType())) {
            log.error("batchType is null");
            return new ResultObject<String>("batchType is null", -1);
        }
        if (ObjectUtil.isNotNull(complexIndexDTO.getComplexIndexId())) {// 指标实例id不为空，则表示是编辑
            complexIndexDTO.setBatchType(0); //编辑时，现只允许编辑当前节点
        }
        // 判断批量类型 0-当前 1-同类型、2-子级(包含当前)
        if (complexIndexDTO.getBatchType().equals(0)) {
            if(complexIndexDTO.getObjectIdType() != null &&
                    (   complexIndexDTO.getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value()) //自定义的节点可操作
                            ||  complexIndexDTO.getObjectIdType().equals(SourceCategory.EQUIPMENT.value())       //引用的设备节点可操作
                            ||  complexIndexDTO.getObjectIdType().equals(SourceCategory.COMPUTERRACK.value())    //引用的机架节点可操作
                            ||  complexIndexDTO.getObjectIdType().equals(SourceCategory.ITDEVICE.value())        //引用的IT设备节点可操作
                    )
            ) {
                Integer objectTypeId = DIMENSION_STRUCTURE_OBJECTTYPEID;
                if(!complexIndexDTO.getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value())) {
                    SourceType sourceType = SourceCategory.valueOf(complexIndexDTO.getObjectIdType()).toSourceType();
                    if(sourceType == null) {
                        log.error("Unknown ObjectIdType=" + complexIndexDTO.getObjectIdType());
                        return new ResultObject<String>("Unknown ObjectIdType("+complexIndexDTO.getObjectIdType()+")", -1);
                    } else {
                        objectTypeId = sourceType.value();
                    }
                }
                complexIndexDTO.setObjectTypeId(objectTypeId);//自定义节点的objectTypeId约定为200
                BeanUtils.copyProperties(complexIndexDTO, complexIndex);

                if(complexIndexDTO.getComplexIndexId() == null) {//新增
                    List<ComplexIndex> existsComplexIndexList = energyDimensionComplexIndexMapper.findByBusinessTypeIdAndComplexIndexDefinitionIdAndObjectTypeIdAndObjectId(complexIndexDTO.getBusinessTypeId(), complexIndexDTO.getComplexIndexDefinitionId(), complexIndexDTO.getObjectTypeId(), complexIndexDTO.getObjectId());
                    if(existsComplexIndexList == null || existsComplexIndexList.size() < 1) {//现有不存在
                        complexIndexMapper.insert(complexIndex);
                        complexIndex.setSynTypeId(0);
                        ((ComplexIndexServiceImpl)complexIndexService).publishComplexIndex(complexIndex);
                        return new ResultObject<String>("OK");
                    } else {
                        return new ResultObject<String>(getI18nMsg("energy.msg.dimension.complexindexexist"), -1);//"该类指标已存在"
                    }
                } else {//修改
                    complexIndexMapper.updateById(complexIndex);
                    complexIndex.setSynTypeId(0);
                    ((ComplexIndexServiceImpl)complexIndexService).publishComplexIndex(complexIndex);
                    return new ResultObject<String>("OK");
                }
            } else {
                return new ResultObject<String>(getI18nMsg("energy.msg.dimension.defaultnodecannotoperate"), -1);//"默认节点不可在此操作"
            }
        } else if (complexIndexDTO.getBatchType().equals(2)) {//按现有逻辑，type为2时，其实只剩下新增指标的逻辑了。编辑的batchType在前面限定为0了
            // 当前以及子级
            return subclass(complexIndexDTO);
        } else {
            log.error("Unknown BatchType=" + complexIndexDTO.getBatchType());
            return new ResultObject<String>("Unknown BatchType", -1);
        }
    }

    private ResultObject<String> subclass(ComplexIndexDTO complexIndexDTO) {
        List<ComplexIndex> complexIndexList = new ArrayList<>();
        // 资源id查询出树结构
        EnergyResourceStructure energyStructureSubTree = structureService.getEnergyStructureSubTree(complexIndexDTO.getDimensionTypeId(), complexIndexDTO.getObjectId(), complexIndexDTO.getObjectIdType());
        if(energyStructureSubTree == null) {
            log.error("子树不存在：DimensionTypeId=" + complexIndexDTO.getDimensionTypeId() + "; ResourceStructureId=" + complexIndexDTO.getResourceStructureId() + "; ObjectId=" + complexIndexDTO.getObjectId() + "; ObjectIdType=" + complexIndexDTO.getObjectIdType());
            return new ResultObject<String>(getI18nMsg("energy.msg.dimension.subtreenotexist"), -2);//"子树不存在"
        }

        // 获取直接子集
        List<EnergyResourceStructure> children = energyStructureSubTree.getChildren();
        if (CollectionUtil.isNotEmpty(children)) {
            // 递归
            recursion(children, complexIndexList, complexIndexDTO);
        }
        // 处理当前
        if(energyStructureSubTree.getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value()) //自定义的节点可操作
                ||  energyStructureSubTree.getObjectIdType().equals(SourceCategory.EQUIPMENT.value())       //引用的设备节点可操作
                ||  energyStructureSubTree.getObjectIdType().equals(SourceCategory.COMPUTERRACK.value())    //引用的机架节点可操作
                ||  energyStructureSubTree.getObjectIdType().equals(SourceCategory.ITDEVICE.value())        //引用的IT设备节点可操作
        ) {

            List<ComplexIndex> existsComplexIndexList = energyDimensionComplexIndexMapper.findByBusinessTypeIdAndComplexIndexDefinitionIdAndObjectTypeIdAndObjectId(complexIndexDTO.getBusinessTypeId(), complexIndexDTO.getComplexIndexDefinitionId(), energyStructureSubTree.getObjectTypeId(), energyStructureSubTree.getObjectId());
            if(existsComplexIndexList == null || existsComplexIndexList.size() < 1) {//当前节点下不存在此类指标，才需要新增，已存在了就跳过

                ComplexIndex complexIndex = new ComplexIndex();
                complexIndexDTO.setObjectTypeId(energyStructureSubTree.getObjectTypeId());
                BeanUtil.copyProperties(complexIndexDTO, complexIndex); //complexIndexDTO中已有objectId
                complexIndex.setComplexIndexDefinitionId(complexIndexDTO.getComplexIndexDefinitionId());
                complexIndexList.add(complexIndex);
            }
        }

        if(complexIndexList.size() > 0) {
            List<ComplexIndex> insertList = new ArrayList<>();
            List<ComplexIndex> updateList = new ArrayList<>();
            for (ComplexIndex complexIndex : complexIndexList) {
                if(ObjectUtil.isNull(complexIndex.getComplexIndexId())) {
                    insertList.add(complexIndex);
                } else {
                    updateList.add(complexIndex);
                }
            }
            saveAll(insertList);
            updateAll(updateList);
            complexIndexList.stream().forEach(a -> {
                a.setSynTypeId(0);
                ((ComplexIndexServiceImpl)complexIndexService).publishComplexIndex(a);
            });
            return new ResultObject<String>("OK.");
        } else {
            log.info("实际新增个数为0：DimensionTypeId=" + complexIndexDTO.getDimensionTypeId() + "; ResourceStructureId=" + complexIndexDTO.getResourceStructureId() + "; ObjectIdType=" + complexIndexDTO.getObjectIdType());
            return new ResultObject<String>(getI18nMsg("energy.msg.dimension.newzero"), -2);//"该类指标已存在或不需新增，实际新增0个"
        }
    }

    /**
     * 用递归遍历处理每个节点。引用的默认层级节点不处理(不新增，不更新)
     *
     * @param list
     * @param complexIndexList
     * @param complexIndexDTO
     */
    private void recursion(List<EnergyResourceStructure> list, List<ComplexIndex> complexIndexList, ComplexIndexDTO complexIndexDTO) {
        // 遍历
        for (EnergyResourceStructure r : list) {

            if(r.getObjectIdType().equals(SourceCategory.ENERGY_RESOURCE.value()) //自定义的节点可操作
                    ||  r.getObjectIdType().equals(SourceCategory.EQUIPMENT.value())       //引用的设备节点可操作
                    ||  r.getObjectIdType().equals(SourceCategory.COMPUTERRACK.value())    //引用的机架节点可操作
                    ||  r.getObjectIdType().equals(SourceCategory.ITDEVICE.value())        //引用的IT设备节点可操作
            ) {
                complexIndexDTO.setObjectTypeId(r.getObjectTypeId());
                if (ObjectUtil.isNull(complexIndexDTO.getComplexIndexId())) {
                    // 指标实例id不存在就是批量新增
                    List<ComplexIndex> existsComplexIndexList = energyDimensionComplexIndexMapper.findByBusinessTypeIdAndComplexIndexDefinitionIdAndObjectTypeIdAndObjectId(complexIndexDTO.getBusinessTypeId(), complexIndexDTO.getComplexIndexDefinitionId(), complexIndexDTO.getObjectTypeId(), r.getObjectId());
                    if(existsComplexIndexList == null || existsComplexIndexList.size() < 1) {//当前节点下不存在此类指标，才需要新增，已存在了就跳过
                        ComplexIndex complexIndex1 = ComplexIndex.cloneComplexIndexDTO(complexIndexDTO);
                        complexIndex1.setObjectId(r.getObjectId());
                        complexIndex1.setObjectTypeId(r.getObjectTypeId());
                        complexIndexList.add(complexIndex1);
                    }
                } else {//20220606：当前业务逻辑下，全部为新增不会有编辑，此分支永不执行
                    // 指标id不为空就是修改
                    // 修改之前判断出传入的业务分类id和指标定义id是否为空（因为旧版本的指标实例这两个字段为空，如果放开相当于都被批量修改了，不符合业务规范。
                    if (ObjectUtil.isNotNull(complexIndexDTO.getBusinessTypeId()) && ObjectUtil.isNotNull(complexIndexDTO.getComplexIndexDefinitionId())) {
                        // 根据业务分类、指标定义、对象类型、资源唯一id 查询出所有指标实例
                        List<ComplexIndex> updateComplexIndexList = energyDimensionComplexIndexMapper.findByBusinessTypeIdAndComplexIndexDefinitionIdAndObjectTypeIdAndObjectId(complexIndexDTO.getBusinessTypeId(), complexIndexDTO.getComplexIndexDefinitionId(), complexIndexDTO.getObjectTypeId(), r.getObjectId());
                        if (CollectionUtil.isNotEmpty(updateComplexIndexList)) {
                            for (ComplexIndex c : updateComplexIndexList) {
                                // 修改
                                c.updateComplexIndexByDTO(complexIndexDTO);
                                complexIndexList.add(c);
                            }
                        } else {
                            // 新增
                            ComplexIndex complexIndex1 = ComplexIndex.cloneComplexIndexDTO(complexIndexDTO);
                            complexIndex1.setObjectId(r.getObjectId());
                            complexIndex1.setObjectTypeId(r.getObjectTypeId());
                            complexIndexList.add(complexIndex1);
                        }
                    }
                }
            }
            // 判断是否还有子节点
            if (CollectionUtil.isEmpty(r.getChildren())) {
                continue;
            }
            // 继续递归
            recursion(r.getChildren(), complexIndexList, complexIndexDTO);
        }
    }

    @Override
    public List<ComplexIndex> findAllComplexindexByObjectTypeId(Integer objectTypeId) {
        List<ComplexIndex> allComplexindexByObjectTypeId = energyDimensionComplexIndexMapper.findAllComplexindexByObjectTypeId(objectTypeId,
                DIMENSION_STRUCTURE_OBJECTTYPEID);
        return getObjectName(allComplexindexByObjectTypeId);
    }

    private List<ComplexIndex> getObjectName(List<ComplexIndex> complexIndexList) {
        if (CollUtil.isEmpty(complexIndexList)) {
            return complexIndexList;
        }
        complexIndexList.forEach(e -> {
            if(e.getObjectTypeId() != DIMENSION_STRUCTURE_OBJECTTYPEID) {//填充除能耗多维度自定义节点之外的其他资源的名称
                ResourceObjectEntity resourceObject = resourceObjectManager.findEntityByObject(new ResourceObject(e.getObjectId(), e.getObjectTypeId()));
                e.setObjectName(resourceObject == null ? null : resourceObject.getResourceName());
            }
        });
        return complexIndexList;
    }

    @Override
    public List<ComplexIndexDefinition> getComplexIndexDefinition(Integer businessTypeId,String complexIndexDefinitionTypeId){
        List<ComplexIndexDefinition> result = new ArrayList<>();

        List<ComplexIndexDefinition> allComplexIndexDefinition = complexIndexDefinitionMapper.selectList(null);

//        List<BusinessDefinitionMap> maps =  energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
//                i->i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(complexIndexDefinitionTypeId)).collect(Collectors.toList());

        String[] ids = complexIndexDefinitionTypeId.split(",");
        List<BusinessDefinitionMap> maps = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream()
                .filter(i -> i.getBusinessTypeId().equals(businessTypeId) && Arrays.asList(ids).contains(String.valueOf(i.getComplexIndexDefinitionTypeId())))
                .collect(Collectors.toList());

        for (BusinessDefinitionMap thisMap : maps){
            ComplexIndexDefinition oneDefinition = allComplexIndexDefinition.stream().filter(i->i.getComplexIndexDefinitionId().equals(thisMap.getComplexIndexDefinitionId())).findFirst().orElse(null);
            if (oneDefinition != null)
                result.add(oneDefinition);
        }

        return result;
    }

    private ComplexIndex cloneComplexIndexDTOPrivate(com.siteweb.complexindex.dto.ComplexIndexDTO complexIndexDTO) {
        ComplexIndex ci = new ComplexIndex();
        // 前端传入的需要应用的指标属性
        String str = complexIndexDTO.getFields();
        String[] fields = str.split(" ");
        if (ArrayUtil.isNotEmpty(fields)) {
            ci.setCalcCron(ArrayUtil.contains(fields, "calcCron") ? complexIndexDTO.getCalcCron() : null);
            ci.setCalcType(ArrayUtil.contains(fields, "calcType") ? complexIndexDTO.getCalcType() : null);
            ci.setSaveCron(ArrayUtil.contains(fields, "saveCron") ? complexIndexDTO.getSaveCron() : null);
            ci.setUnit(ArrayUtil.contains(fields, "unit") ? complexIndexDTO.getUnit() : null);
            ci.setAccuracy(ArrayUtil.contains(fields, "accuracy") ? complexIndexDTO.getAccuracy() : null);
            ci.setCheckExpression(ArrayUtil.contains(fields, "checkExpression") ? complexIndexDTO.getCheckExpression() : null);
        }
        ci.setComplexIndexName(complexIndexDTO.getComplexIndexName());
        ci.setComplexIndexDefinitionId(complexIndexDTO.getComplexIndexDefinitionId());
        //ci.setObjectTypeId(complexIndexDTO.getObjectTypeId());
        ci.setRemark(complexIndexDTO.getRemark());
        ci.setBusinessTypeId(complexIndexDTO.getBusinessTypeId());

        return ci;
    }

    private List<ComplexIndex> saveAll(List<ComplexIndex> insertList) {
        int count = complexIndexMapper.insertBatch(insertList);
        if (count > 0) {
            return insertList;
        }
        return new ArrayList<>();
    }

    private List<ComplexIndex> updateAll(List<ComplexIndex> updateList) {
        int totalCount = 0;
        if(updateList != null && updateList.size() > 0) {
            for (ComplexIndex complexIndex : updateList) {
                if(ObjectUtil.isNotNull(complexIndex.getComplexIndexId())) {
                    int count = complexIndexMapper.updateById(complexIndex);
                    totalCount += count;
                }
            }
        }
        if (totalCount > 0) {
            return updateList;
        }
        return new ArrayList<>();
    }

    private String getI18nMsg(String key) {
        String i18nMsg = localeMessageSourceUtil.getMessage(key);
        return StringUtils.isBlank(i18nMsg) ? key : i18nMsg;
    }
}

