package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyCarbonManegePara;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyCarbonManageMapper;
import com.siteweb.energy.service.EnergyCarbonQuotaManagementService;
import com.siteweb.energy.service.EnergyDataConfigItemService;
import com.siteweb.energy.service.EnergyTotalAnalysisService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
@Slf4j
public class EnergyCarbonQuotaManagementServiceImpl implements EnergyCarbonQuotaManagementService {

    @Autowired
    private EnergyCarbonManageMapper energyCarbonManageMapper;

    @Autowired
    private EnergyTotalAnalysisService energyTotalAnalysisService;
    @Autowired
    EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;
    @Autowired
    EnergyDataConfigItemService energyDataConfigItemService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database3}")
    private String databaseEnergy;
    @Override
    public List<Integer> findCarbonQuotaYears(String objectId, String objectTypeId) {

        List<EnergyCarbonManegePara> result = energyCarbonManageMapper.getConfiguredYearByObjectId(objectId, objectTypeId);
        return result.stream()
                .sorted(Comparator.comparingDouble(EnergyCarbonManegePara::getYear).reversed())
                .map(EnergyCarbonManegePara::getYear)
                .distinct().toList();
    }

    @Override
    public List<EnergyCarbonManegePara> findCarbonQuotaData(String objectId, String objectTypeId, Integer year) {
        List<EnergyCarbonManegePara> res = new ArrayList<>();
        res = energyCarbonManageMapper.getObjectConfigInfo(objectId, objectTypeId, year);
        return res;
    }

    @Override
    public List<EnergyCarbonManegePara> findCarbonQuotaHistoryData(String objectId, String objectTypeId, Integer year) {
        List<EnergyCarbonManegePara> res = new ArrayList<>();
        List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps;
        complexIndexMaps = getEnergyTypeResourceComplexIndexMapDTO(Integer.parseInt(objectId));
        List<EnergyReportYearTrendResultDTO> result = new ArrayList<>();
        for (EnergyTypeResourceComplexIndexMapDTO ecd: complexIndexMaps) {
            EnergyReportTrendResultDTO tempResult = new EnergyReportTrendResultDTO();
            tempResult.setComplexIndexId(ecd.getComplexIndex().getComplexIndexId());
            result.add(new EnergyReportYearTrendResultDTO(tempResult));
        }
        for (int month = 1; month <= 12; month++) {
            List<Date> dates = getYearStartAndEnd(year - 1, month);
            double co2Value = 0.0;
            //今天之后的数据不用查询
            Date tempStartTime = dates.get(0);
            Date tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
            if (tempStartTime.before(new Date())) {
                //获取当年的碳使用量
                findSumValueOfHistoryComplexIndexByIdAndDuration(result,tempStartTime,tempEndTime,complexIndexMaps);
                co2Value = getTotal(result,"M"+month);
                co2Value = Double.parseDouble(String.format("%.2f", co2Value));
                EnergyCarbonManegePara energyCarbonManegePara = new EnergyCarbonManegePara();
                energyCarbonManegePara.setObjectId(objectId);
                energyCarbonManegePara.setObjectTypeId(objectTypeId);
                energyCarbonManegePara.setMonth(month);
                energyCarbonManegePara.setYear(year - 1);
                energyCarbonManegePara.setPlanValue((float) co2Value);
                res.add(energyCarbonManegePara);
            }
            else{
                co2Value = Double.parseDouble(String.format("%.2f", co2Value));
                EnergyCarbonManegePara energyCarbonManegePara = new EnergyCarbonManegePara();
                energyCarbonManegePara.setObjectId(objectId);
                energyCarbonManegePara.setObjectTypeId(objectTypeId);
                energyCarbonManegePara.setMonth(month);
                energyCarbonManegePara.setYear(year - 1);
                energyCarbonManegePara.setPlanValue((float) co2Value);
                res.add(energyCarbonManegePara);
            }
        }
        return res;
    }

    //获取当前月份的几种用能的碳总额
    public static double getTotal(List<EnergyReportYearTrendResultDTO> result, String propertyName) {
        double total = 0;
        try {
            Method getter = EnergyReportYearTrendResultDTO.class.getMethod("get" + propertyName);
            for (EnergyReportYearTrendResultDTO dto : result) {
                if (getter.invoke(dto) != null)
                    total += (double) getter.invoke(dto);
            }
        } catch (Exception e) {
            log.error("AirGroupReportServiceImpl-getTotal error {}", e.getMessage());
            return 0;
        }
        return total;
    }

    public void findSumValueOfHistoryComplexIndexByIdAndDuration(List<EnergyReportYearTrendResultDTO> result, Date startTime, Date endTime, List<EnergyTypeResourceComplexIndexMapDTO> complexIndexMaps) {
        try {
            String selectDuration = "select IndexValue as result from EnergyHisMonthData where time >=$startTime and time <= $endTime and ( $someComplexIndex )  group by ComplexIndexId";
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for (EnergyTypeResourceComplexIndexMapDTO ciId : complexIndexMaps) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='").append(ciId.getComplexIndex().getComplexIndexId()).append("' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='").append(ciId.getComplexIndex().getComplexIndexId()).append("' ");
                }
                count++;
                if (count == complexIndexMaps.size() || count % 500 == 0) {
                    List<EnergyHisMonthDataResult> resultComplexIndexQuery = new ArrayList<>();
                    String strSelectDuration = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for (EnergyHisMonthDataResult temp : resultComplexIndexQuery) {
                                EnergyTypeResourceComplexIndexMapDTO ec = complexIndexMaps.stream().filter(i -> i.getComplexIndex().getComplexIndexId() != null && i.getComplexIndex().getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                EnergyReportYearTrendResultDTO st = result.stream().filter(i -> i.getComplexIndexId() != null && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (ec != null && st != null) {
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2);
                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime(endTime);
                                    switch (ca.get(Calendar.MONTH) + 1) {
                                        case 1:st.setM1(valueTemp * ec.getCo2Coefficient());break;
                                        case 2:st.setM2(valueTemp * ec.getCo2Coefficient());break;
                                        case 3:st.setM3(valueTemp * ec.getCo2Coefficient());break;
                                        case 4:st.setM4(valueTemp * ec.getCo2Coefficient());break;
                                        case 5:st.setM5(valueTemp * ec.getCo2Coefficient());break;
                                        case 6:st.setM6(valueTemp * ec.getCo2Coefficient());break;
                                        case 7:st.setM7(valueTemp * ec.getCo2Coefficient());break;
                                        case 8:st.setM8(valueTemp * ec.getCo2Coefficient());break;
                                        case 9:st.setM9(valueTemp * ec.getCo2Coefficient());break;
                                        case 10:st.setM10(valueTemp * ec.getCo2Coefficient());break;
                                        case 11:st.setM11(valueTemp * ec.getCo2Coefficient());break;
                                        case 12:st.setM12(valueTemp * ec.getCo2Coefficient());break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        } catch (Exception e) {
            log.error("AirGroupReportServiceImpl-EnergyStatisticsYearReport error {}", e.getMessage());
        }
    }


    private List<EnergyTypeResourceComplexIndexMapDTO> getEnergyTypeResourceComplexIndexMapDTO(Integer objectId) {
        List<EnergyTypeResourceComplexIndexMapDTO> res = new ArrayList<>();

        List<ComplexIndex> allTotalConsumeComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex();
        //获取对应层级节点下总量指标
        List<ComplexIndex> rootTotalComplexIndex = allTotalConsumeComplexIndex.stream().filter(item -> item.getObjectId().equals(objectId)).toList();
        List<ComplexIndexBusinessType> complexIndexBusinessTypeList = complexIndexBusinessTypeMapper.selectList(new QueryWrapper<ComplexIndexBusinessType>().eq("parentId", 1));
        //获取字典
        List<EnergyDataItem> energyDataItems = energyDataConfigItemService.getAllByEntryIdTimeliness(7);

        for (ComplexIndex complexIndex : rootTotalComplexIndex) {
            for (ComplexIndexBusinessType item : complexIndexBusinessTypeList) {
                EnergyTypeResourceComplexIndexMapDTO temp = new EnergyTypeResourceComplexIndexMapDTO();
                if (!complexIndex.getBusinessTypeId().equals(item.getBusinessTypeId())) {
                    continue;
                }
                temp.setBusinessType(item);
                List<Double> coefficient = findParamsByBusinessType(energyDataItems, Integer.valueOf(item.getDescription()));
                temp.setCo2Coefficient(coefficient.get(1));
                temp.setComplexIndex(complexIndex);
                res.add(temp);
                break;
            }
        }
        return res;
    }

    private List<Date> getYearStartAndEnd (Integer year, Integer month) {
        List<Date> res = new ArrayList<>();
        Calendar start = Calendar.getInstance();
        start.set(Calendar.YEAR, year);
        start.set(Calendar.MONTH, month - 1);
        int firstDay = start.getActualMinimum(Calendar.DAY_OF_MONTH);
        start.set(Calendar.DAY_OF_MONTH, firstDay);
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        Date firstDayOfMonth = start.getTime();

        Calendar end = Calendar.getInstance();
        end.set(Calendar.YEAR, year);
        end.set(Calendar.MONTH, month - 1);
        int lastDay = end.getActualMaximum(Calendar.DAY_OF_MONTH);
        end.set(Calendar.DAY_OF_MONTH, lastDay);
        end.set(Calendar.HOUR_OF_DAY, 23);
        end.set(Calendar.MINUTE, 59);
        end.set(Calendar.SECOND, 59);
        Date lastDayOfMonth = end.getTime();
        res.add(firstDayOfMonth);
        res.add(lastDayOfMonth);
        return res;
    }
    private List<Double> findParamsByBusinessType(List<EnergyDataItem> energyDataItems, Integer itemId) {
        List<Double> res = new ArrayList<>();
        for (EnergyDataItem item : energyDataItems) {
            if (item.getItemId().equals(itemId)) {
                res.add(Double.parseDouble(item.getExtendField1() == null ? "0.0" :item.getExtendField1()));
                res.add(Double.parseDouble(item.getExtendField3() == null ? "0.0" :item.getExtendField3()));
                break;
            }
        }
        return res;
    }


    @Override
    public String saveCarbonQuotaData(EnergyCarbonManageDataParamsDTO energyCarbonManageDataParamsDTO) {
        Integer res = 1;
        //得到此节点的配置信息，配额量以及节点ID 年份
        float planTotalValue = energyCarbonManageDataParamsDTO.getYearPlanTotalValue();
        String objectId = energyCarbonManageDataParamsDTO.getObjectId();
        String objectTypeId = energyCarbonManageDataParamsDTO.getObjectTypeId();
        Integer year = energyCarbonManageDataParamsDTO.getYear();

        String checkFlag = CheckFatherAndChildCarbonValueInfo(objectId,objectTypeId,year,planTotalValue,true);
        if(!checkFlag.equals("OK"))
            return checkFlag;

        for (EnergyCarbonManegePara e : energyCarbonManageDataParamsDTO.getEnergyCarbonDataList()) {
            res = res & energyCarbonManageMapper.insert(e);
            if (res != 1)
                log.error("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 插入数据失败");
        }
        return res == 1 ? "OK" : messageSourceUtil.getMessage("energy.msg.dimension.InsertFailed");
    }

    @Override
    public String updateCarbonQuotaData(List<EnergyCarbonManageDTO> list,float yearPlanTotalValue) {
        EnergyCarbonManegePara objectInfo = energyCarbonManageMapper.getObjectIdById(list.get(0).getId());
        String objectId = objectInfo.getObjectId();
        String objectType = objectInfo.getObjectTypeId();
        Integer year = objectInfo.getYear();

        String checkFlag = CheckFatherAndChildCarbonValueInfo(objectId,objectType,year,yearPlanTotalValue,false);
        if(!checkFlag.equals("OK"))
            return checkFlag;

        Integer res = 1;
        for (EnergyCarbonManageDTO e : list) {
            res = res & energyCarbonManageMapper.updateDataById(e.getId(), e.getPlanValue(),yearPlanTotalValue);
            if (res != 1)
                log.error("EnergyCarbonQuotaManagementServiceImpl-updateCarbonQuotaData 更新数据失败");
        }
        return res == 1 ? "OK" : messageSourceUtil.getMessage("energy.msg.dimension.updateFailed");
    }

    @Override
    public Integer deleteCarbonQuotaData(Integer id) {
        return energyCarbonManageMapper.deleteDataById(id,0);
    }

    public String CheckFatherAndChildCarbonValueInfo(String objectId,String objectTypeId,int year,float planTotalValue,boolean isAdd){
        //获取该节点的父节点ID
        //String objectFather = energyCarbonManageMapper.getObjectFatherId(objectId,objectTypeId);
        ResourceStructure thisResourceStructure = resourceStructureManager.getResourceStructureById(Integer.parseInt(objectId));
        if (thisResourceStructure == null) return messageSourceUtil.getMessage("no resourcestructure of resourcestructureId = " + objectId);
        String objectFather = thisResourceStructure.getParentResourceStructureId().toString();

        if(objectFather == null){
            log.error("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 此节点没有找到对应的父节点");
            return  messageSourceUtil.getMessage("energy.msg.dimension.objectNoFather");
        }
        int childObjectType = Integer.parseInt(objectTypeId);
        childObjectType = childObjectType + 1;
        //获取该节点的子节点碳配额
        String objectChildTotalValue = energyCarbonManageMapper.getAllChildsTotalValue(objectId, String.valueOf(childObjectType),year);
        if(objectChildTotalValue != null){
            if(planTotalValue < Float.parseFloat(objectChildTotalValue)){
                log.error("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 当前额度小于子节点总额;子节点总额为"+objectChildTotalValue);
                return messageSourceUtil.getMessage("energy.msg.dimension.fatherValueLowChild") + objectChildTotalValue;
            }
        }
        //跳过根节点
        if(!objectFather.equals("0")){
            //得到此节点，再寻找此节点的父节点
            ResourceStructure fatherObject = energyCarbonManageMapper.getFatherObjectInfo(objectFather);
            if(fatherObject == null){
                log.error("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 此节点没有找到对应的父节点");
                return  messageSourceUtil.getMessage("energy.msg.dimension.objectNoFather");
            }
            //根据父节点的Id获取父节点下的所有子节点以及父节点的碳配额信息
            List<EnergyCarbonManegePara> fatherObjectCarbonInfo = energyCarbonManageMapper.getObjectConfigInfo(fatherObject.getResourceStructureId().toString(),fatherObject.getStructureTypeId().toString(),year);
            if(fatherObjectCarbonInfo.isEmpty()){
                log.info("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 此节点的父节点没有配置该年份的碳配额信息");
                return "OK";
            }
            float fatherCarbonTotalValue = fatherObjectCarbonInfo.get(0).getYearPlanTotalValue();
            //获取所有子节点的碳配额信息
            String childsTotalValue = energyCarbonManageMapper.getAllChildsTotalValue(fatherObject.getResourceStructureId().toString(),objectTypeId,year);
            if(childsTotalValue == null){
                childsTotalValue = "0";
                log.info("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 子节点总配额为0");
            }
            if(isAdd){
                if((Float.parseFloat(childsTotalValue) + planTotalValue) > fatherCarbonTotalValue){
                    log.error("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 当前节点配额已超总额,剩余量为"+(fatherCarbonTotalValue-Float.parseFloat(childsTotalValue)));
                    return messageSourceUtil.getMessage("energy.msg.dimension.objectOverTotal") + (fatherCarbonTotalValue-Float.parseFloat(childsTotalValue));
                }
            }else {
                List<EnergyCarbonManegePara> objectConfig =  findCarbonQuotaData(objectId,objectTypeId,year);
                float objectConfigValue = objectConfig.get(0).getYearPlanTotalValue();
                float diffRes = planTotalValue - objectConfigValue;
                if((Float.parseFloat(childsTotalValue) + diffRes) > fatherCarbonTotalValue){
                    float lastRes = fatherCarbonTotalValue - (Float.parseFloat(childsTotalValue) - objectConfigValue);
                    log.error("EnergyCarbonQuotaManagementServiceImpl-saveCarbonQuotaData 当前节点配额已超总额,剩余量为"+lastRes);
                    return messageSourceUtil.getMessage("energy.msg.dimension.objectOverTotal") + lastRes;
                }
            }
        }
        return "OK";
    }
}
