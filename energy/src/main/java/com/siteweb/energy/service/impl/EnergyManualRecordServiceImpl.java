package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.Region;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.mapper.ComplexIndexMapper;
import com.siteweb.energy.entity.EnergyManualRecord;
import com.siteweb.energy.service.EnergyHistoryDataService;
import com.siteweb.energy.service.EnergyManualRecordService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import com.siteweb.energy.mapper.EnergyManualRecordMapper;

@Service
@Slf4j
public class EnergyManualRecordServiceImpl implements EnergyManualRecordService {

    @Autowired
    private ComplexIndexMapper complexIndexMapper;
    @Autowired
    private  EnergyManualRecordMapper energyManualRecordMapper;

    @Autowired
    private EnergyHistoryDataService energyHistoryDataService;


    @Override
    public List<ComplexIndex> getComplexIndex(Integer objectId) {
        return complexIndexMapper.findComplexIndexOfObjectToManualRecord(objectId);
    }

    @Override
    public List<EnergyManualRecord> getManualRecord(Integer complexIndexId) {
        return energyManualRecordMapper.findManualRecordByComplexIndexId(complexIndexId);
    }

    @Override
    public Integer insertManualRecord(@NotNull EnergyManualRecord record) {
        List<EnergyManualRecord> findRecordList = energyManualRecordMapper.selectList(new QueryWrapper<EnergyManualRecord>().
                eq("InsertTime", record.getInsertTime()).and(wrapper -> wrapper.eq("ComplexIndexId", record.getComplexIndexId())));
        if (findRecordList != null && !findRecordList.isEmpty()) {
            return 0;
        }
        insertManualEnergyHisData(record);
        return energyManualRecordMapper.insert(record);
    }

    private void insertManualEnergyHisData(@NotNull EnergyManualRecord record) {
        try {
            List<EnergyManualRecord> recordList = energyManualRecordMapper.findManualRecordByComplexIndexId(record.getComplexIndexId());
            if (!recordList.isEmpty()) {
                EnergyManualRecord lastRecord = recordList.get(0);
                if (record.getCalcType() == 0) {
                    energyHistoryDataService.manualEnergyHisDataInfluxdb(record.getComplexIndexId(), lastRecord.getInsertTime(), record.getInsertTime(), record.getRecordValue());
                } else {
                    double calcValue = record.getRecordValue() - lastRecord.getRecordValue();
                    energyHistoryDataService.manualEnergyHisDataInfluxdb(record.getComplexIndexId(), lastRecord.getInsertTime(), record.getInsertTime(), calcValue);
                }
            }
        } catch (Exception ex) {
            log.error("手动抄表历史数据插入异常insertManualEnergyHisData：", ex);
        }
    }
}
