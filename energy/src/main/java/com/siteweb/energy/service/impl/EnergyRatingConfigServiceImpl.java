package com.siteweb.energy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.mapper.ComplexIndexMapper;
import com.siteweb.energy.dto.EnergyRatingConfigExDTO;
import com.siteweb.energy.dto.EnergyRatingSumDTO;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyRatingConfig;
import com.siteweb.energy.mapper.EnergyDataConfigItemMapper;
import com.siteweb.energy.mapper.EnergyRatingConfigMapper;
import com.siteweb.energy.service.EnergyRatingConfigService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyRatingConfigServiceImpl implements EnergyRatingConfigService {
    //全年小时数常量
    private static Integer Total_Year_Hours = 8760;
    //各工况历史数据计算等级时有效最小值12小时
    private static Integer Min_Effective_Seconds = 12 * 60 * 60;

    @Autowired
    NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    private EnergyRatingConfigMapper energyRatingConfigMapper;
    @Autowired
    private ComplexIndexMapper complexIndexMapper;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ConfigSignalManager configSignalManager;
    @Autowired
    private EnergyDataConfigItemMapper energyDataConfigItemMapper;

    //获取某个层级的等级评定配置列表
    @Override
    public List<EnergyRatingConfig> getEnergyRatingConfigList(Integer objectId) {
        List<EnergyRatingConfig> result = new ArrayList<>();
        List<EnergyRatingConfig> allConfigs = energyRatingConfigMapper.selectList(new QueryWrapper<>());

        result = ObjectUtil.isNotNull(objectId) ?
                allConfigs.stream().filter(i -> i.getObjectId() != null && i.getObjectId().equals(objectId)).collect(Collectors.toList()) :
                allConfigs;
        return result;
    }

    //获取某个等级评定配置详情
    @Override
    public EnergyRatingConfigExDTO getEnergyRatingExByConfigId(Integer configId) {
        EnergyRatingConfigExDTO result = new EnergyRatingConfigExDTO();
        EnergyRatingConfig config = energyRatingConfigMapper.selectById(configId);

        if (ObjectUtil.isNotNull(config)) {
            result = getEnergyRatingConfigExDTOFromEnergyRatingConfig(config);
        }
        return result;
    }

    //赋值信号表达式的中文名称
    private EnergyRatingConfigExDTO getEnergyRatingConfigExDTOFromEnergyRatingConfig(EnergyRatingConfig config) {
        EnergyRatingConfigExDTO dto = new EnergyRatingConfigExDTO(config);
        if (ObjectUtil.isNotNull(config.getInDryTempParam())) {
            dto.setInDryTempName(getNameByParam(config.getInDryTempParam()));
        }
        if (ObjectUtil.isNotNull(config.getInWetTempParam())) {
            dto.setInWetTempName(getNameByParam(config.getInWetTempParam()));
        }
        if (ObjectUtil.isNotNull(config.getOutDryTempParam())) {
            dto.setOutDryTempName(getNameByParam(config.getOutDryTempParam()));
        }
        if (ObjectUtil.isNotNull(config.getOutWetTempParam())) {
            dto.setOutWetTempName(getNameByParam(config.getOutWetTempParam()));
        }
        if (ObjectUtil.isNotNull(config.getItPowerParam())) {
            dto.setItPowerName(getNameByParam(config.getItPowerParam()));
        }
        if (ObjectUtil.isNotNull(config.getTotalPowerParam())) {
            dto.setTotalPowerName(getNameByParam(config.getTotalPowerParam()));
        }
        if (ObjectUtil.isNotNull(config.getRunningLoadParam())) {
            dto.setRunningLoadName(getNameByParam(config.getRunningLoadParam()));
        }
        return dto;
    }

    //查找中文名称
    private String getNameByParam(String param) {
        try {
            //区分从指标和从测点
            Pattern p = param.toLowerCase().contains("ci") ?
                    Pattern.compile("(?<=ci\\()[^\\)]+", Pattern.CASE_INSENSITIVE) :
                    Pattern.compile("(?<=cp\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(param);

            if (m.find()){
                if (param.toLowerCase().contains("ci")) {
                    Integer complexId = Integer.valueOf(m.group());
                    ComplexIndex thisComplexIndex = complexIndexMapper.selectById(complexId);

                    if (ObjectUtil.isNotNull(thisComplexIndex)) {
                        return thisComplexIndex.getComplexIndexName();
                    }
                } else {
                    String equipAndSignalId = m.group();
                    if (ObjectUtil.isNotNull(equipAndSignalId)) {
                        String[] equipmentAndSignalArray = StringUtils.split(equipAndSignalId, '.');
                        String equipId = equipmentAndSignalArray[0];
                        String signalId = equipmentAndSignalArray[1];
                        Equipment equipment = equipmentManager.getEquipmentById(Integer.valueOf(equipId));
                        ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(Integer.valueOf(equipId), Integer.valueOf(signalId));

                        if (ObjectUtil.isNotNull(equipment) && ObjectUtil.isNotNull(configSignalItem)) {
                            return equipment.getEquipmentName() + "--" + configSignalItem.getSignalName();
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("getNameByParam error:"+ex.getMessage());
            return "";
        }
        return "";
    }

    //新增等级评定配置
    @Override
    public Integer addEnergyRatingConfig(EnergyRatingConfig config) {
        if (ObjectUtil.isNull(config.getObjectId())) return 0;
        List<EnergyRatingConfig> sameNameConfig = energyRatingConfigMapper.selectList(new QueryWrapper<EnergyRatingConfig>().eq("Name", config.getName()));
        if (sameNameConfig.size() > 0) {
            log.error("addEnergyRatingConfig already same name;"+config.getName());
            return -1;
        }
        return energyRatingConfigMapper.insert(config);
    }

    //修改等级评定配置
    @Override
    public Integer modifyEnergyRatingConfig(EnergyRatingConfig config){
        if (ObjectUtil.isNotNull(config.getRatingConfigId())){
            EnergyRatingConfig dbConfig = energyRatingConfigMapper.selectById(config.getRatingConfigId());
            if (ObjectUtil.isNotNull(dbConfig) && dbConfig.getStatus() == 1)    return -2;
            //重名判断
            List<EnergyRatingConfig> sameNameConfig = energyRatingConfigMapper.selectList(new QueryWrapper<EnergyRatingConfig>().eq("Name", config.getName()));
            sameNameConfig.removeIf(i-> i.getRatingConfigId().equals(config.getRatingConfigId()));
            if (sameNameConfig.size() > 0) {
                log.error("modifyEnergyRatingConfig already same name;"+config.getName());
                return -1;
            }

            //部分字段修改
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.eq("RatingConfigId", config.getRatingConfigId());
            updateWrapper.set("OutDryTempParam",  config.getOutDryTempParam());
            updateWrapper.set("OutWetTempParam",  config.getOutWetTempParam());
            updateWrapper.set("InDryTempParam",  config.getInDryTempParam());
            updateWrapper.set("InWetTempParam",  config.getInWetTempParam());
            updateWrapper.set("RunningLoadParam",  config.getRunningLoadParam());
            updateWrapper.set("ITPowerParam",  config.getItPowerParam());
            updateWrapper.set("TotalPowerParam",  config.getTotalPowerParam());
            updateWrapper.set("ObjectId",  config.getObjectId());
            updateWrapper.set("Name",  config.getName());
            updateWrapper.set("IntervalSecond",  config.getIntervalSecond());
            updateWrapper.set("UserId",  config.getUserId());
            updateWrapper.set("CityId",  config.getCityId());

            return energyRatingConfigMapper.update(null, updateWrapper);
        }
        return null;
    }

    //删除等级评定配置
    @Override
    public Boolean deleteEnergyRatingConfig(Integer configId){
        if (ObjectUtil.isNotNull(configId)){
            return energyRatingConfigMapper.deleteById(configId) == 1;
        }
        return false;
    }

    //计算能耗等级评定
    @Override
    public Double getEnergyRatingResult(Integer configId) {
        Double result = null;

        //总用电量
        Double ETotal = 0d;
        //主设备用电量
        Double EIT = 0d;
        //更加配置所属地市，获取工况系数
        EnergyRatingConfig config = energyRatingConfigMapper.selectById(configId);
        if (ObjectUtil.isNull(config) || ObjectUtil.isNull(config.getCityId()))
            return result;

        EnergyDataItem dataItem = energyDataConfigItemMapper.selectOne(new QueryWrapper<EnergyDataItem>().eq("EntryId", 6).eq("ItemId",config.getCityId()));
        if (ObjectUtil.isNull(dataItem)) return result;
        Double Ta = dataItem.getExtendField1() == null ? 0 : Double.valueOf(dataItem.getExtendField1());
        Double Tb = dataItem.getExtendField2() == null ? 0 : Double.valueOf(dataItem.getExtendField2());
        Double Tc = dataItem.getExtendField3() == null ? 0 : Double.valueOf(dataItem.getExtendField3());
        Double Td = dataItem.getExtendField4() == null ? 0 : Double.valueOf(dataItem.getExtendField4());
        Double Te = dataItem.getExtendField5() == null ? 0 : Double.valueOf(dataItem.getExtendField5());

        //获取各工况平均后的采集值、采集时间
        String sql = "select WorkingCondition,avg(TotalPower) as avgTotalPower,avg(ITPower) as avgITPower,avg(RunningLoad) as avgRunningLoad,sum(IntervalSecond) as sumIntervalSecond from energy_ratingdata where RatingConfigId = :configId and WorkingCondition is not null group by WorkingCondition";
        Map<String, Integer> paramMap = Map.of("configId", configId);
        List<EnergyRatingSumDTO> liveEnergyRatingSumDTOs = jdbcTemplate.query(sql, paramMap,(rs, rowNum) -> {
            EnergyRatingSumDTO dto = new EnergyRatingSumDTO();
            dto.setWorkingCondition(rs.getString("WorkingCondition"));
            dto.setAvgTotalPower(rs.getDouble("avgTotalPower"));
            dto.setAvgITPower(rs.getDouble("avgITPower"));
            dto.setAvgRunningLoad(rs.getDouble("avgRunningLoad"));
            dto.setSumIntervalSecond(rs.getDouble("sumIntervalSecond"));
            return dto;
        });

        Double ETotalTemp = 0d;
        Double EITTemp = 0d;
        Double TTemp = 0d;
        for (EnergyRatingSumDTO sumDTO : liveEnergyRatingSumDTOs){
            if (sumDTO.getSumIntervalSecond()< Min_Effective_Seconds) continue;

            switch (sumDTO.getWorkingCondition()){
                case "a": TTemp = Ta; break;
                case "b": TTemp = Tb; break;
                case "c": TTemp = Tc; break;
                case "d": TTemp = Td; break;
                case "e": TTemp = Te; break;
                default: TTemp = 0d;
            }
            ETotalTemp = Total_Year_Hours * sumDTO.getAvgTotalPower() * TTemp;
            EITTemp = Total_Year_Hours * sumDTO.getAvgITPower() * TTemp;
            ETotal += ETotalTemp;
            EIT += EITTemp;
        }

        if (EIT == 0)
            result = null;
        else{
            result = NumberUtil.doubleAccuracy(ETotal/EIT,3);
        }
        return result;
    }

    //计算能耗等级评定导出
    @Override
    public List<EnergyRatingSumDTO> getEnergyRatingResultTable(Integer configId){
        //获取各工况平均后的采集值、采集时间
        String sql = """
                 select WorkingCondition,avg(TotalPower) as avgTotalPower,avg(ITPower) as avgITPower,avg(RunningLoad) as avgRunningLoad,sum(intervalSecond) as sumIntervalSecond,
                 avg(OutDryTemp) as avgOutDryTemp, avg(OutWetTemp) as avgOutWetTemp,avg(InDryTemp) as avgInDryTemp, avg(InWetTemp) as avgInWetTemp,
                 min(SampleTime) as minSampleTime,max(SampleTime) as maxSampleTime
                 from energy_ratingdata where RatingConfigId = :configId and WorkingCondition is not null group by WorkingCondition order by WorkingCondition
                 """;
        Map<String, Integer> paramMap = Map.of("configId", configId);
        List<EnergyRatingSumDTO> liveEnergyRatingSumDTOs = jdbcTemplate.query(sql,paramMap,(rs, rowNum) -> {
            EnergyRatingSumDTO dto = new EnergyRatingSumDTO();
            dto.setWorkingCondition(rs.getString("WorkingCondition"));
            dto.setAvgTotalPower(NumberUtil.doubleAccuracy(rs.getDouble("avgTotalPower"),2));
            dto.setAvgITPower(NumberUtil.doubleAccuracy(rs.getDouble("avgITPower"),2));
            dto.setAvgRunningLoad(NumberUtil.doubleAccuracy(rs.getDouble("avgRunningLoad"),2));
            dto.setSumIntervalSecond(NumberUtil.doubleAccuracy(rs.getDouble("sumIntervalSecond"),2));

            dto.setAvgOutDryTemp(NumberUtil.doubleAccuracy(rs.getDouble("avgOutDryTemp"),2));
            dto.setAvgOutWetTemp(NumberUtil.doubleAccuracy(rs.getDouble("avgOutWetTemp"),2));
            dto.setAvgInDryTemp(NumberUtil.doubleAccuracy(rs.getDouble("avgInDryTemp"),2));
            dto.setAvgInWetTemp(NumberUtil.doubleAccuracy(rs.getDouble("avgInWetTemp"),2));

            dto.setMinSampleTime(rs.getTimestamp("minSampleTime"));
            dto.setMaxSampleTime(rs.getTimestamp("maxSampleTime"));
            return dto;
        });

        List<String> hasWorkingCondition =  liveEnergyRatingSumDTOs.stream().map(EnergyRatingSumDTO::getWorkingCondition).collect(Collectors.toList());
        for(String workingCondition : StringUtils.splitToList(",","a,b,c,d,e")){
            if (!hasWorkingCondition.stream().anyMatch(i->i.equals(workingCondition))){
                EnergyRatingSumDTO newSumDTO = new EnergyRatingSumDTO();
                newSumDTO.setWorkingCondition(workingCondition);
                liveEnergyRatingSumDTOs.add(newSumDTO);
            }
            else{
                EnergyRatingSumDTO dto = liveEnergyRatingSumDTOs.stream().filter(i->i.getWorkingCondition().equals(workingCondition)).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(dto) && dto.getSumIntervalSecond() < Min_Effective_Seconds){
                    dto.setMinSampleTime(null);
                    dto.setMaxSampleTime(null);
                    dto.setAvgOutDryTemp(null);
                    dto.setAvgOutWetTemp(null);
                    dto.setAvgInDryTemp(null);
                    dto.setAvgInWetTemp(null);
                    dto.setAvgITPower(null);
                    dto.setAvgTotalPower(null);
                    dto.setAvgRunningLoad(null);
                }
            }
        }
        return liveEnergyRatingSumDTOs.stream().sorted(Comparator.comparing(EnergyRatingSumDTO::getWorkingCondition)).collect(Collectors.toList());
    }

    //开始采集时修改配置状态
    @Override
    public Boolean startJob(Integer ratingConfigId, Date startTime, Date endTime, String workingCondition) {
        EnergyRatingConfig config = new EnergyRatingConfig();
        config.setStatus(1);
        config.setRatingConfigId(ratingConfigId);
        config.setStartTime(startTime);
        config.setEndTime(endTime);
        config.setWorkingCondition(workingCondition);
        return energyRatingConfigMapper.updateById(config) == 1;
    }

    //停止采集时修改配置状态
    @Override
    public Boolean closeJob(Integer ratingConfigId) {
        EnergyRatingConfig config = new EnergyRatingConfig();
        config.setStatus(0);
        config.setRatingConfigId(ratingConfigId);
        config.setStartTime(null);
        config.setEndTime(null);
        config.setWorkingCondition(null);
        return energyRatingConfigMapper.updateById(config) == 1;
    }
}
