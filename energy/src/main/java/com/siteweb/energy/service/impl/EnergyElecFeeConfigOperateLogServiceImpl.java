package com.siteweb.energy.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.common.util.DateUtil;
import com.siteweb.energy.dto.ElecFeeScheme;
import com.siteweb.energy.dto.ElecFeeStructure;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.mapper.*;
import com.siteweb.energy.model.ChangeField;
import com.siteweb.energy.model.OperateLogInfo;
import com.siteweb.energy.service.EnergyElecFeeConfigOperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnergyElecFeeConfigOperateLogServiceImpl implements EnergyElecFeeConfigOperateLogService {

    // json转换
    private static ObjectMapper objectMapper = new ObjectMapper();

    static {
        // 所有日期格式都统一为：yyyy-MM-dd HH:mm:ss
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }

    @Autowired
    private EnergyElecFeeConfigOperateLogMapper logMapper;
    @Autowired
    private EnergyElecFeeSchemeMapper schemeMapper;
    @Autowired
    private EnergyElecFeeFpgMapper fpgMapper;
    @Autowired
    private EnergyElecFeeStepPriceMapper stepPriceMapper;
    @Autowired
    private EnergyElecFeeFpgValueMapper fpgValueMapper;

    @Override
    public List<EnergyElecFeeConfigOperateLog> getAllOperateLog() {
        return logMapper.getAllOrderBy();
    }

    @Override
    public List<EnergyElecFeeConfigOperateLog> getAllExcludeChangeContentOrderBy() {
        return logMapper.getAllExcludeChangeContentOrderBy();
    }

    @Override
    public ResultObject<String> getLogDetail(Integer logId) {
        ResultObject<String> result = new ResultObject<>("");
        EnergyElecFeeConfigOperateLog operateLog = logMapper.selectById(logId);
        if(operateLog != null) {
            String operationContent = operateLog.getOperationContent();
            String changeContent = operateLog.getChangeContent();
            changeContent = StringUtils.isBlank(changeContent) ? "" : changeContent;
            result = new ResultObject<>(operationContent + "\r\n" + changeContent);
        }
        return result;
    }


    @Override
    public List<ChangeField> diffSchemeTrunk(EnergyElecFeeScheme schemeDb, boolean appliedRangeHasChange) {

        String objectName = "elecfeescheme";
        List<ChangeField> changeFields = new ArrayList<>();
        EnergyElecFeeScheme schemeOld = schemeMapper.findBySchemeId(schemeDb.getSchemeId());
        if(!compareString(schemeOld.getSchemeName(), schemeDb.getSchemeName())) {
            ChangeField cf = new ChangeField(objectName, "SchameName", schemeOld.getSchemeName(), schemeDb.getSchemeName());
            changeFields.add(cf);
        }
        if(!compareDate(schemeOld.getEnableDate(), schemeDb.getEnableDate())) {
            ChangeField cf = new ChangeField(objectName, "EnableDate",
                    schemeOld.getEnableDate()==null ? "null" : DateUtil.dateToString(schemeOld.getEnableDate()),
                    schemeDb.getEnableDate()==null ? "null" : DateUtil.dateToString(schemeDb.getEnableDate()));
            changeFields.add(cf);
        }
        if(!compareDate(schemeOld.getDeactivateDate(), schemeDb.getDeactivateDate())) {
            ChangeField cf = new ChangeField(objectName, "DeactivateDate",
                    schemeOld.getDeactivateDate()==null ? "null" : DateUtil.dateToString(schemeOld.getDeactivateDate()),
                    schemeDb.getDeactivateDate()==null ? "null" : DateUtil.dateToString(schemeDb.getDeactivateDate()));
            changeFields.add(cf);
        }
        if(!compareInteger(schemeOld.getStartMonth(), schemeDb.getStartMonth())) {
            ChangeField cf = new ChangeField(objectName, "StartMonth",
                    schemeOld.getStartMonth().toString(), schemeDb.getStartMonth().toString());
            changeFields.add(cf);
        }
        if(!compareInteger(schemeOld.getEndMonth(), schemeDb.getEndMonth())) {
            ChangeField cf = new ChangeField(objectName, "EndMonth",
                    schemeOld.getEndMonth().toString(), schemeDb.getEndMonth().toString());
            changeFields.add(cf);
        }
        if(!compareInteger(schemeOld.getEnableStatus(), schemeDb.getEnableStatus())) {
            ChangeField cf = new ChangeField(objectName, "EnableStatus",
                    schemeOld.getEnableStatus().toString(), schemeDb.getEnableStatus().toString());
            changeFields.add(cf);
        }
        if(!compareInteger(schemeOld.getBusinessTypeId(), schemeDb.getBusinessTypeId())) {
            ChangeField cf = new ChangeField(objectName, "BusinessTypeId",
                    schemeOld.getBusinessTypeId().toString(), schemeDb.getBusinessTypeId().toString());
            changeFields.add(cf);
        }
        if(!compareString(schemeOld.getBusinessTypeName(), schemeDb.getBusinessTypeName())) {
            ChangeField cf = new ChangeField(objectName, "BusinessTypeName", schemeOld.getBusinessTypeName(), schemeDb.getBusinessTypeName());
            changeFields.add(cf);
        }
        if(appliedRangeHasChange) {
            ChangeField cf = new ChangeField(objectName, "AppliedRange", schemeOld.getAppliedRange(), schemeDb.getAppliedRange());
            changeFields.add(cf);
        }
        return changeFields;
    }

    @Override
    public List<ChangeField> diffStepPrice(EnergyElecFeeStepPrice newStepPrice) {
        String objectName = "elecfeestepprice";
        List<ChangeField> changeFields = new ArrayList<>();
        EnergyElecFeeStepPrice oldStepPrice = stepPriceMapper.findByStepPriceId(newStepPrice.getStepPriceId());
        if(!compareString(oldStepPrice.getStepName(), newStepPrice.getStepName())) {
            ChangeField cf = new ChangeField(objectName, "StepName",
                    oldStepPrice.getStepName(), newStepPrice.getStepName());
            changeFields.add(cf);
        }
        if(!compareInteger(oldStepPrice.getUpperLimit(), newStepPrice.getUpperLimit())) {
            ChangeField cf = new ChangeField(objectName, "UpperLimit",
                    oldStepPrice.getUpperLimit().toString(), newStepPrice.getUpperLimit().toString());
            changeFields.add(cf);
        }
        return changeFields;
    }

    @Override
    public List<ChangeField> diffFeeFpg(EnergyElecFeeFpg fpgDb) {
        String objectName = "elecfeefpg";
        List<ChangeField> changeFields = new ArrayList<>();
        EnergyElecFeeFpg oldFpg = fpgMapper.findByFpgId(fpgDb.getFpgId());

        if(!compareDate(oldFpg.getEffectiveStart(), fpgDb.getEffectiveStart())) {
            ChangeField cf = new ChangeField(objectName, "EffectiveStart",
                    oldFpg.getEffectiveStart() == null ? "null" : DateUtil.dateToString(oldFpg.getEffectiveStart()),
                    fpgDb.getEffectiveStart() == null ? "null" : DateUtil.dateToString(fpgDb.getEffectiveStart()));
            changeFields.add(cf);
        }
        if(!compareDate(oldFpg.getEffectiveEnd(), fpgDb.getEffectiveEnd())) {
            ChangeField cf = new ChangeField(objectName, "EffectiveEnd",
                    oldFpg.getEffectiveEnd() == null ? "null" : DateUtil.dateToString(oldFpg.getEffectiveEnd()),
                    fpgDb.getEffectiveEnd() == null ? "null" : DateUtil.dateToString(fpgDb.getEffectiveEnd()));
            changeFields.add(cf);
        }
        if(!compareInteger(oldFpg.getFpgDescKey(), fpgDb.getFpgDescKey())) {
            ChangeField cf = new ChangeField(objectName, "FpgDescKey",
                    oldFpg.getFpgDescKey() == null ? "Null" : oldFpg.getFpgDescKey().toString(),
                    fpgDb.getFpgDescKey() == null ? "Null" : fpgDb.getFpgDescKey().toString());
            changeFields.add(cf);
        }
        if(!compareString(oldFpg.getFpgDesc(), fpgDb.getFpgDesc())) {
            ChangeField cf = new ChangeField(objectName, "FpgDesc",
                    oldFpg.getFpgDesc(), fpgDb.getFpgDesc());
            changeFields.add(cf);
        }
        return changeFields;
    }

    @Override
    public List<ChangeField> diffFeeFpgValue(EnergyElecFeeFpgValue fpgValueDb) {
        String objectName = "elecfeefpgvalue";
        List<ChangeField> changeFields = new ArrayList<>();
        EnergyElecFeeFpgValue oldFpgValue = fpgValueMapper.selectById(fpgValueDb.getFpgValueId());
        if(!compareString(oldFpgValue.getFpgValue(), fpgValueDb.getFpgValue())) {
            ChangeField cf = new ChangeField(objectName, "FpgValue",
                    oldFpgValue.getFpgValue(), fpgValueDb.getFpgValue());
            changeFields.add(cf);
        }
        return changeFields;
    }

    @Override
    public void saveSchemeTrunkUpdateLog(EnergyElecFeeScheme schemeDb, List<ChangeField> changeFieldList, Date curDate, Integer userId, String userName) {

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(2);
        logInfo.setSchemeId(schemeDb.getSchemeId().toString());
        logInfo.setSchemeName(schemeDb.getSchemeName());
        logInfo.setChangeFieldList(changeFieldList);

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());
        operateLog.setChangeContent(null);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveStructureMapUpdateLog(EnergyElecFeeScheme schemeDb, List<EnergyElecFeeSchemeStructureMap> oldMaps, List<ElecFeeStructure> newMaps,
                                          Date curDate, Integer userId, String userName) {

        String oldStructures = oldMaps.stream().sorted(Comparator.comparing(EnergyElecFeeSchemeStructureMap::getResourceStructureId, Comparator.nullsFirst(Integer::compareTo)))
                .map(item -> item.getResourceStructureName() + "[" + item.getResourceStructureId() + "]").collect(Collectors.joining(","));

        String newStructures = newMaps.stream().sorted(Comparator.comparing(ElecFeeStructure::getResourceStructureId, Comparator.nullsFirst(Integer::compareTo)))
                .map(item -> item.getResourceStructureName() + "[" + item.getResourceStructureId() + "]").collect(Collectors.joining(","));
        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(2);
        logInfo.setSchemeId(schemeDb.getSchemeId().toString());
        logInfo.setSchemeName(schemeDb.getSchemeName());
        logInfo.setChangeFieldList(null);
        logInfo.setOldStructures(oldStructures);
        logInfo.setNewStructures(newStructures);

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());
        operateLog.setChangeContent(" Change From \r\n  " + oldStructures + "\r\nTo \r\n  " + newStructures);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveStepPriceUpdateLog(EnergyElecFeeStepPrice newStepPrice, List<ChangeField> changeFieldList, Date curDate, Integer userId, String userName) {

        EnergyElecFeeScheme scheme = schemeMapper.findBySchemeId(newStepPrice.getSchemeId());

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(2);
        logInfo.setSchemeId(newStepPrice.getSchemeId().toString());
        logInfo.setSchemeName(scheme.getSchemeName());
        logInfo.setStepPriceId(newStepPrice.getStepPriceId().toString());
        logInfo.setStepPriceName(newStepPrice.getStepName());
        logInfo.setChangeFieldList(changeFieldList);

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());
        operateLog.setChangeContent(null);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveFpgUpdateLog(EnergyElecFeeFpg fpgDb, List<ChangeField> changeFieldFpgList, Date updateDate, Integer userId, String userName) {

        EnergyElecFeeScheme scheme = schemeMapper.findBySchemeId(fpgDb.getSchemeId());

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(2);
        logInfo.setSchemeId(fpgDb.getSchemeId().toString());
        logInfo.setSchemeName(scheme.getSchemeName());
        logInfo.setFpgId(fpgDb.getFpgId().toString());
        logInfo.setFpgStartTime(toHHmmssStr(fpgDb.getEffectiveStart()));
        logInfo.setFpgEndTime(toHHmmssStr(fpgDb.getEffectiveEnd()));
        logInfo.setFpgDescKey(fpgDb.getFpgDescKey() == null ? "Null" : fpgDb.getFpgDescKey().toString());
        logInfo.setFpgDesc(fpgDb.getFpgDesc());
        logInfo.setChangeFieldList(changeFieldFpgList);

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(updateDate);
        operateLog.setOperationContent(logInfo.toString());
        operateLog.setChangeContent(null);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveFpgValueUpdateLog(EnergyElecFeeFpgValue fpgValueDb, List<ChangeField> changeFieldFpgList, Date updateDate, Integer userId, String userName) {

        EnergyElecFeeScheme scheme = schemeMapper.findBySchemeId(fpgValueDb.getSchemeId());
        EnergyElecFeeFpg fptObj = fpgMapper.findByFpgId(fpgValueDb.getFpgId());
        EnergyElecFeeStepPrice stepPrice = stepPriceMapper.findByStepPriceId(fpgValueDb.getStepPriceId());

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(2);
        logInfo.setSchemeId(fpgValueDb.getSchemeId().toString());
        logInfo.setSchemeName(scheme.getSchemeName());
        logInfo.setStepPriceId(stepPrice.getStepPriceId().toString());
        logInfo.setStepPriceName(stepPrice.getStepName());
        logInfo.setFpgId(fptObj.getFpgId().toString());
        logInfo.setFpgStartTime(toHHmmssStr(fptObj.getEffectiveStart()));
        logInfo.setFpgEndTime(toHHmmssStr(fptObj.getEffectiveEnd()));
        logInfo.setFpgDescKey(fptObj.getFpgDescKey() == null ? "Null" : fptObj.getFpgDescKey().toString());
        logInfo.setFpgDesc(fptObj.getFpgDesc());
        logInfo.setFpgValueId(fpgValueDb.getFpgValueId().toString());
        logInfo.setFpgValue(fpgValueDb.getFpgValue());
        logInfo.setChangeFieldList(changeFieldFpgList);

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(updateDate);
        operateLog.setOperationContent(logInfo.toString());
        operateLog.setChangeContent(null);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveSchemeInsertLog(ElecFeeScheme schemeObj, Date curDate, Integer userId, String userName) throws JsonProcessingException {
        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(1);
        logInfo.setOperateObj("Scheme");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "new add scheme json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(schemeObj);
        } catch (JsonProcessingException e) {
            log.error("new scheme [name=" + schemeObj.getSchemeName() + ", schemeId=" + schemeObj.getSchemeId() + "]", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveStepPriceInsertLog(EnergyElecFeeStepPrice spDb, List<EnergyElecFeeFpgValue> fpgValueSaveDbs, Date curDate, Integer userId, String userName)
            throws JsonProcessingException {

        EnergyElecFeeScheme schemeObj = schemeMapper.findBySchemeId(spDb.getSchemeId());

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(1);
        logInfo.setOperateObj("StepPrice");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());
        logInfo.setStepPriceId(spDb.getStepPriceId().toString());
        logInfo.setStepPriceName(spDb.getStepName());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "new add StepPrice json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(spDb);
        } catch (JsonProcessingException e) {
            log.error("new add StepPrice toJSONString throw Exception: ", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);

        for (EnergyElecFeeFpgValue fpgValueSaveDb : fpgValueSaveDbs) {
            saveFpgValueInsertLog(schemeObj,null, spDb, fpgValueSaveDb, curDate, userId, userName);
        }
    }

    @Override
    public void saveFpgInsertLog(EnergyElecFeeFpg fpgDb, List<EnergyElecFeeFpgValue> vleSaveList, Date curDate, Integer userId, String userName) throws JsonProcessingException {

        EnergyElecFeeScheme schemeObj = schemeMapper.findBySchemeId(fpgDb.getSchemeId());
        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(1);
        logInfo.setOperateObj("Fpg");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());
        logInfo.setFpgId(fpgDb.getFpgId().toString());
        logInfo.setFpgStartTime(toHHmmssStr(fpgDb.getEffectiveStart()));
        logInfo.setFpgEndTime(toHHmmssStr(fpgDb.getEffectiveEnd()));
        logInfo.setFpgDesc(fpgDb.getFpgDesc());
        logInfo.setFpgDescKey(fpgDb.getFpgDescKey() == null ? "Null" : fpgDb.getFpgDescKey().toString());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "new add Fpg json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(fpgDb);
        } catch (JsonProcessingException e) {
            log.error("new add Fpg toJSONString throw Exception: ", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);

        for (EnergyElecFeeFpgValue fpgValueSaveDb : vleSaveList) {
            saveFpgValueInsertLog(schemeObj,fpgDb, null, fpgValueSaveDb, curDate, userId, userName);
        }
    }

    @Override
    public void saveSchemeDeleteLog(ElecFeeScheme schemeWithStructureMaps, Date curDate, Integer userId, String userName) throws JsonProcessingException {
        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(3);
        logInfo.setOperateObj("Scheme");
        logInfo.setSchemeId(schemeWithStructureMaps.getSchemeId().toString());
        logInfo.setSchemeName(schemeWithStructureMaps.getSchemeName());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "delete scheme json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(schemeWithStructureMaps);
        } catch (JsonProcessingException e) {
            log.error("delete scheme [name=" + schemeWithStructureMaps.getSchemeName() + ", schemeId=" + schemeWithStructureMaps.getSchemeId() + "]", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveStepPriceDeleteLog(EnergyElecFeeStepPrice delStepPrice, Date curDate, Integer userId, String userName) throws JsonProcessingException {
        EnergyElecFeeScheme schemeObj = schemeMapper.findBySchemeId(delStepPrice.getSchemeId());

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(3);
        logInfo.setOperateObj("StepPrice");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());
        logInfo.setStepPriceId(delStepPrice.getStepPriceId().toString());
        logInfo.setStepPriceName(delStepPrice.getStepName());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "delete StepPrice json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(delStepPrice);
        } catch (JsonProcessingException e) {
            log.error("delete StepPrice toJSONString throw Exception: ", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveFpgValueDeleteLog(EnergyElecFeeStepPrice delStepPrice, EnergyElecFeeFpgValue delFpgValue, Date curDate, Integer userId, String userName) throws JsonProcessingException {
        EnergyElecFeeScheme schemeObj = schemeMapper.findBySchemeId(delFpgValue.getSchemeId());
        EnergyElecFeeFpg fpgObj = fpgMapper.findByFpgId(delFpgValue.getFpgId());
        EnergyElecFeeStepPrice stepPriceObj = delStepPrice==null ? stepPriceMapper.findByStepPriceId(delFpgValue.getStepPriceId()) : delStepPrice;

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(3);
        logInfo.setOperateObj("FpgValue");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());
        logInfo.setStepPriceId(stepPriceObj.getStepPriceId().toString());
        logInfo.setStepPriceName(stepPriceObj.getStepName());
        logInfo.setFpgId(fpgObj.getFpgId().toString());
        logInfo.setFpgDesc(fpgObj.getFpgDesc());
        logInfo.setFpgDescKey(fpgObj.getFpgDescKey() == null ? "Null" : fpgObj.getFpgDescKey().toString());
        logInfo.setFpgStartTime(toHHmmssStr(fpgObj.getEffectiveStart()));
        logInfo.setFpgEndTime(toHHmmssStr(fpgObj.getEffectiveEnd()));
        logInfo.setFpgValueId(delFpgValue.getFpgValueId().toString());
        logInfo.setFpgValue(delFpgValue.getFpgValue());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "delete FpgValue json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(delFpgValue);
        } catch (JsonProcessingException e) {
            log.error("delete FpgValue toJSONString throw Exception: ", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);
    }

    @Override
    public void saveFpgDeleteLog(EnergyElecFeeFpg delFpg, Date curDate, Integer userId, String userName) throws JsonProcessingException {
        EnergyElecFeeScheme schemeObj = schemeMapper.findBySchemeId(delFpg.getSchemeId());
        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(3);
        logInfo.setOperateObj("Fpg");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());
        logInfo.setFpgId(delFpg.getFpgId().toString());
        logInfo.setFpgStartTime(toHHmmssStr(delFpg.getEffectiveStart()));
        logInfo.setFpgEndTime(toHHmmssStr(delFpg.getEffectiveEnd()));
        logInfo.setFpgDesc(delFpg.getFpgDesc());
        logInfo.setFpgDescKey(delFpg.getFpgDescKey() == null ? "Null" : delFpg.getFpgDescKey().toString());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "delete Fpg json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(delFpg);
        } catch (JsonProcessingException e) {
            log.error("delete Fpg toJSONString throw Exception: ", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);
    }

    /** 峰平谷值的保存日志 */
    private void saveFpgValueInsertLog(EnergyElecFeeScheme scheme, EnergyElecFeeFpg fpg, EnergyElecFeeStepPrice stepPrice,
                                       EnergyElecFeeFpgValue vleSave, Date curDate, Integer userId, String userName) throws JsonProcessingException {
        EnergyElecFeeScheme schemeObj = scheme==null ? schemeMapper.findBySchemeId(vleSave.getSchemeId()) : scheme;
        EnergyElecFeeFpg fpgObj = fpg==null ? fpgMapper.findByFpgId(vleSave.getFpgId()) : fpg;
        EnergyElecFeeStepPrice stepPriceObj = stepPrice==null ? stepPriceMapper.findByStepPriceId(vleSave.getStepPriceId()) : stepPrice;

        OperateLogInfo logInfo = new OperateLogInfo();
        logInfo.setOperateType(1);
        logInfo.setOperateObj("FpgValue");
        logInfo.setSchemeId(schemeObj.getSchemeId().toString());
        logInfo.setSchemeName(schemeObj.getSchemeName());
        logInfo.setStepPriceId(stepPriceObj.getStepPriceId().toString());
        logInfo.setStepPriceName(stepPriceObj.getStepName());
        logInfo.setFpgId(fpgObj.getFpgId().toString());
        logInfo.setFpgDesc(fpgObj.getFpgDesc());
        logInfo.setFpgDescKey(fpgObj.getFpgDescKey() == null ? "Null" : fpgObj.getFpgDescKey().toString());
        logInfo.setFpgStartTime(toHHmmssStr(fpgObj.getEffectiveStart()));
        logInfo.setFpgEndTime(toHHmmssStr(fpgObj.getEffectiveEnd()));
        logInfo.setFpgValueId(vleSave.getFpgValueId().toString());
        logInfo.setFpgValue(vleSave.getFpgValue());

        EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
        operateLog.setOperator(userName);
        operateLog.setOperatorId(userId);
        operateLog.setUpdateDate(curDate);
        operateLog.setOperationContent(logInfo.toString());

        String changeContent = "new add FpgValue json:   \r\n";
        try {
            changeContent += objectMapper.writeValueAsString(vleSave);
        } catch (JsonProcessingException e) {
            log.error("new add FpgValue toJSONString throw Exception: ", e);
            throw e;
        }
        operateLog.setChangeContent(changeContent);

        logMapper.insert(operateLog);
    }


    /** 比较两个字符串: true-相等;  false-不相等 */
    private boolean compareString(String str1, String str2) {
        if(str1 == null && str2 == null) {
            return true;
        }
        if(str1 != null && str2 != null) {
            return str1.equals(str2);
        }
        return false;
    }

    /** 比较两个Integer: true-相等;  false-不相等 */
    private boolean compareInteger(Integer int1, Integer int2) {
        if(int1 == null && int2 == null) {
            return true;
        }
        if(int1 != null && int2 != null) {
            return int1.equals(int2);
        }
        return false;
    }

    /** 比较两个Date: true-相等;  false-不相等 */
    private boolean compareDate(Date date1, Date date2) {
        if(date1 == null && date2 == null) {
            return true;
        }
        if(date1 != null && date2 != null) {
            return date1.getTime() - date2.getTime() == 0;
        }
        return false;
    }

    /** 只取日期的 HH:mm:ss部分 */
    private String toHHmmssStr(Date time) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        return formatter.format(time);
    }
}

