package com.siteweb.energy.service;

import com.siteweb.energy.dto.EnergyCarbonEmissionDTO;
import com.siteweb.energy.entity.EnergyCarbonEmissionPara;
import com.siteweb.energy.entity.EnergyCarbonManegePara;

import java.util.List;

public interface EnergyCarbonEmissionManagementService {
    List<String> getUnit();

    Integer addNewConfiguration(EnergyCarbonEmissionDTO energyCarbonEmissionDTO);

    Integer deleteConfiguration(Integer id);

    List<Integer> findCarbonEmissionYears(String objectId, String objectTypeId);

    List<EnergyCarbonEmissionPara> findCarbonEmissionHistoryData(String objectId, String objectTypeId, Integer year);

    Integer updateConfiguration(EnergyCarbonEmissionDTO energyCarbonEmissionDTO);

    List<EnergyCarbonEmissionPara> findCarbonEmission(String objectId, String objectTypeId, Integer isArea, String year);
}
