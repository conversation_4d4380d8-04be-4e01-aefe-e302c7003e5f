package com.siteweb.energy.service;

import com.siteweb.energy.dto.ComplexIndexClassificationListDTO;
import com.siteweb.energy.dto.MultiLevelObjectDTO;
import com.siteweb.energy.entity.EnergyComplexIndexClassificationMap;

import java.util.List;

public interface EnergyComplexIndexClassificationService {
    List<ComplexIndexClassificationListDTO> findComplexIndexByObjectId(String objectId, String objectTypeId);

    Integer deleteComplexIndexClassificationMapByComplexIndexId(Integer complexIndexId);

    Integer addComplexIndexClassification(EnergyComplexIndexClassificationMap energyComplexIndexClassificationMap);

    List<MultiLevelObjectDTO> getEnergyClassificationTree();
}
