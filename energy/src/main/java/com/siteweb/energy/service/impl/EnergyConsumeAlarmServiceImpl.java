package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.util.DateUtil;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.energy.entity.EnergyConsumeData;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyConsumeDataMapper;
import com.siteweb.energy.service.EnergyConsumeAlarmService;
import com.siteweb.energy.service.EnergyConsumeManagementService;
import com.siteweb.energy.service.EnergyElecFeeService;
import com.siteweb.prealarm.dto.PreAlarmDTO;
import com.siteweb.prealarm.service.PreAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Component
public class EnergyConsumeAlarmServiceImpl implements EnergyConsumeAlarmService {

    @Value("${spring.web.locale}")
    private String locale;

    @Autowired
    private EnergyConsumeDataMapper energyConsumeDataMapper;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private PreAlarmService preAlarmService;
    @Autowired
    private ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    private EnergyElecFeeService energyElecFeeService;

    @Override
    public void GenerateConsumeAlarm() {
        boolean isChina = locale.toLowerCase().equals("zh_cn");

        Date startTime = DateUtil.getYesterdayStartOrEndTime(true);
        Date endTime = DateUtil.getYesterdayStartOrEndTime(false);

        Calendar ca = Calendar.getInstance();
        ca.setTime(startTime);
        int consumeYear = ca.get(Calendar.YEAR);
        int consumeMonth = ca.get(Calendar.MONTH)+1;

        // 获取所有能源管控计划
        List<EnergyConsumeData> lstAllEnergyConsumeData = energyConsumeDataMapper.selectList(new QueryWrapper<>());
        if (lstAllEnergyConsumeData.size() == 0) return;

        List<EnergyConsumeData> lstEnergyConsumeData = lstAllEnergyConsumeData
                .stream().filter(i->i.getMonth().equals(String.valueOf(consumeMonth)) && i.getYear().equals(consumeYear))
                .collect(Collectors.toList());
        if (lstEnergyConsumeData.size() == 0) return;

        for(EnergyConsumeData consumeData: lstEnergyConsumeData){
            ComplexIndex thisIndex =  energyComplexIndexManager.GetAllTotalConsumeComplexIndex().stream()
                    .filter(i->i.getObjectId().toString().equals(consumeData.getObjectId())
                            && i.getObjectTypeId().toString().equals(consumeData.getObjectTypeId())
                            && i.getBusinessTypeId().equals(consumeData.getEnergyTypeId())
                    ).findFirst().orElse(null);
            if (thisIndex == null) continue;

            ComplexIndexBusinessType thisType =  complexIndexBusinessTypeService.findAll().stream().filter(
                    i->i.getBusinessTypeId().equals(consumeData.getEnergyTypeId())
            ).findFirst().orElse(null);
            if (thisType == null) continue;

            // 获取指标截至本月用能里
            Double thisMonthConsume = energyElecFeeService.GetThisMonthSume(thisIndex.getComplexIndexId(), startTime);
            if (thisMonthConsume == 0) continue;

            PreAlarmDTO preAlarmDTO = new PreAlarmDTO();
            preAlarmDTO.setBusinessTypeId(consumeData.getEnergyTypeId());
            preAlarmDTO.setPreAlarmPointId(0-consumeData.getEnergyTypeId());
            preAlarmDTO.setObjectTypeId(Integer.parseInt(consumeData.getObjectTypeId()));
            preAlarmDTO.setObjectId(Integer.parseInt(consumeData.getObjectId()));
            preAlarmDTO.setStartTime(startTime);
            preAlarmDTO.setTriggerValue(thisMonthConsume.toString());

            if (thisMonthConsume >= consumeData.getOverstepValuePreAlarm() ){    // 大于超限量
                preAlarmDTO.setPreAlarmSeverity(1);
                preAlarmDTO.setFlag(0);
                preAlarmDTO.setUniqueName(consumeData.getOverstepValue().toString());
                if (isChina){
                    preAlarmDTO.setMeanings(thisType.getBusinessTypeName()+"本月用能总量超过‘超限量预警值’");
                }else{
                    preAlarmDTO.setMeanings(thisType.getBusinessTypeName()+"The total amount of energy used this month exceeds the 'exceed limit alarm value'");
                }
            } else if (thisMonthConsume >= consumeData.getPlanValuePreAlarm()){   // 大于计划量
                preAlarmDTO.setPreAlarmSeverity(2);
                preAlarmDTO.setFlag(0);
                preAlarmDTO.setUniqueName(consumeData.getPlanValue().toString());
                if (isChina){
                    preAlarmDTO.setMeanings(thisType.getBusinessTypeName()+"本月用能总量超过‘计划量预警值’");
                }else{
                    preAlarmDTO.setMeanings(thisType.getBusinessTypeName()+"The total amount of energy used this month exceeds the 'planned consumption alarm value'");
                }
            } else {     //当前层级不需要产生此用能管控的预警
                preAlarmDTO.setFlag(1);
                preAlarmDTO.setMeanings("");
            }

            preAlarmService.createPreAlarmByEnergy(preAlarmDTO);
        }
    }
}
