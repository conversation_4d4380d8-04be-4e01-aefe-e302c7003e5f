package com.siteweb.energy.service;

import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyStructure;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.model.StructureInfo;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.entity.ResourceStructure;

import java.util.ArrayList;
import java.util.List;

public interface EnergyStructureService {
    List<EnergyStructure> findAll();
    List<EnergyStructure> findByStructureIdIn(List<Integer> listStructureIdId);

    /** 得到默认层级树 */
    ResourceStructureTreeDTO getDefaultTree();

    /** 根据dimensionTypeId得到此棵能耗多维度的自定义树 */
    EnergyResourceStructure getEnergyStructureTree(Integer dimensionTypeId);

    /** 新增一个自定义节点 */
    ResultObject<String> addOneEnergyStructure(StructureInfo newStructure);

    /** 批量一个自定义节点：1.如果是引用的默认节点只删除关联关系；
     *                  2.如果是引用的其他自定义节点则只有在没有任何引用后才删除(同时删除关联和指标)；如果还有其他树引用此节点，则只删除关联关系
     *                  3.如果此节点有子、孙...节点，则会级联删除，对待每一个节点都遵循以上1、2点原则*/
    ResultObject<String> delOneEnergyStructure(EnergyResourceStructure node);

    /** 更新一个节点, 只能更新自定义节点，而且更新的也只是名称字段 */
    ResultObject<String> updateOneEnergyStructure(EnergyResourceStructure node);

    /** 根据dimensionTypeId得到此棵能耗多维度的自定义树, 再根据subTreeRootId得到其对应的子树(subTreeRootId即 ObjectId 的值) */
    EnergyResourceStructure getEnergyStructureSubTree(Integer dimensionTypeId, Integer subTreeRootId, Integer objectIdType);

    //-------------------
    /** 添加已存在的自定义节点 */
    ResultObject<String> addExistEnergyStructures(StructureInfo newStructure);
    /** 添加已存在的默认层级节点 */
    ResultObject<String> addExistDefaultStructures(StructureInfo newStructure);

    /** 批量删除指标 */
    int batchDeleteComplexIndex(int objectId, int objectTypeId, List<Integer> businessTypeIds);
}
