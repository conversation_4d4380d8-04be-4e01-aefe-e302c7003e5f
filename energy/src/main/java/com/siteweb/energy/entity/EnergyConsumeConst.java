package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("Energy_ConsumeConst")
public class EnergyConsumeConst {
    @TableId(value="id", type = IdType.AUTO)
    private Integer id;


    private String objectId;

    private String objectTypeId;

    private Integer peoples;

    private Double area;

}
