package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_errordatamodifyrecord")
public class EnergyErrorDataModifyRecord {
    @TableId(value="Id", type = IdType.AUTO)
    private Integer id;

    private Integer complexIndexId;

    private Date errorTime;

    private Date modifyTime;

    private Integer userId;

    private String userName;

    private double originalValue;

    private double indexValue;

    private String note;

    private String extendField1;
}
