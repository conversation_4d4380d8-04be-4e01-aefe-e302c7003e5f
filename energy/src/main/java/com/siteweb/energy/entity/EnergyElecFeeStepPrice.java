package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 能耗-电费方案对应的阶梯定价类
 */
@Data
@NoArgsConstructor
@TableName("Energy_ElecFeeStepPrice")
public class EnergyElecFeeStepPrice {

    /**
     * 自增主键
     */
    @TableId(value="StepPriceId", type = IdType.AUTO)
    private Integer stepPriceId;

    private Integer schemeId;
    private String stepName;
    private Integer upperLimit;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;
    /** 1-作为封顶阶梯； 0(或null或其他任何值)-表示非封顶阶梯 */
    private Integer asMaxStep;

    private String extendField1;

}
