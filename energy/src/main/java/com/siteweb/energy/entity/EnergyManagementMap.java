package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/*
    节能措施与层级节点映射关系表
 */
@Data
@NoArgsConstructor
@TableName("Energy_ManagementMap")
public class EnergyManagementMap {
    /**
     * 自增主键
     */
    @TableId(value="MapId", type = IdType.AUTO)
    private Integer mapId;
    private Integer energyId;
    private Integer resourceStructureId;
    private Integer isMarker;
    private String property;
}
