package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Constructor;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_ratingdatahistory")
public class EnergyRatingDataHistory extends EnergyRatingData{

    private Integer times;

    private Date deleteDate;

    public EnergyRatingDataHistory(EnergyRatingData energyRatingData){
        this.setRatingDataId(energyRatingData.getRatingDataId());
        this.setRatingConfigId(energyRatingData.getRatingConfigId());
        this.setSampleTime(energyRatingData.getSampleTime());
        this.setIntervalSecond(energyRatingData.getIntervalSecond());
        this.setWorkingCondition(energyRatingData.getWorkingCondition());
        this.setOutDryTemp(energyRatingData.getOutDryTemp());
        this.setOutWetTemp(energyRatingData.getOutWetTemp());
        this.setInDryTemp(energyRatingData.getInDryTemp());
        this.setInWetTemp(energyRatingData.getInWetTemp());
        this.setRunningLoad(energyRatingData.getRunningLoad());
        this.setItPower(energyRatingData.getItPower());
        this.setTotalPower(energyRatingData.getTotalPower());
    }

}
