package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("Energy_Structure")
public class EnergyStructure {

    @TableId(value="StructureId", type = IdType.AUTO)
    private Integer structureId;

    private String structureName;

    private Integer sourceCategory;

    private String notes;
    /**
     * 是否是根节点的流入节点，1-是，非1或null-不是。
     * 为1时表示此节点是根节点的流入节点，此节点属于特殊节点，其父节点固定为节点
     */
    private Integer asRootInflowNode;
}
