package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 能耗-电费方案与资源层级map类
 */
@Data
@NoArgsConstructor
@TableName("Energy_ElecFeeSchemeStructureMap")
public class EnergyElecFeeSchemeStructureMap {

    /**
     * 自增主键
     */
    @TableId(value="MapId", type = IdType.AUTO)
    private Integer mapId;

    private Integer schemeId;
    private Integer resourceStructureId;
    private String resourceStructureName;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;

    private String extendField1;

}
