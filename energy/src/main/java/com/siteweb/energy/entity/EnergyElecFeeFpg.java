package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 能耗-电费峰平谷记录类
 */
@Data
@NoArgsConstructor
@TableName("Energy_ElecFeeFpg")
public class EnergyElecFeeFpg {

    /**
     * 自增主键
     */
    @TableId(value="FpgId", type = IdType.AUTO)
    private Integer fpgId;

    private Integer schemeId;
    private Date effectiveStart;
    private Date effectiveEnd;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;

    /** 描述文字的key，"峰" 、 "平" 、 "谷" 分别对应 1、2、3 */
    private Integer fpgDescKey;
    /** 描述文字，"峰" 、 "平" 、 "谷" */
    private String fpgDesc;

    private String extendField1;

}
