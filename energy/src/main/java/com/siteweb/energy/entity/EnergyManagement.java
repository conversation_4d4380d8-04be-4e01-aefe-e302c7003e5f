package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/*
    节能措施信息表
 */
@Data
@NoArgsConstructor
@TableName("Energy_Management")
public class EnergyManagement {
     /**
     * 自增主键
     */
    @TableId(value="EnergyId", type = IdType.AUTO)
    private Integer energyId;
    private String energyName;
    private String unit;
    private String operator;
    private String contact;
    private Date startTime;
    private Date endTime;
    private String notes;
}
