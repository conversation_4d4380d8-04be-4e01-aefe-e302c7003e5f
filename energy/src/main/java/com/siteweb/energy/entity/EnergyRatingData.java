package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_ratingdata")
public class EnergyRatingData {

    @TableId(value="RatingDataId", type = IdType.AUTO)
    private Integer ratingDataId;

    private Integer ratingConfigId;

    private Date sampleTime;

    private Integer intervalSecond;


    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String workingCondition;

    private Double outDryTemp;

    private Double outWetTemp;

    private Double inDryTemp;

    private Double inWetTemp;

    private Double runningLoad;

    private Double itPower;

    private Double totalPower;

}
