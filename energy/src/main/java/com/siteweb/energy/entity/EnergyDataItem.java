package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("Energy_DataItem")
public class EnergyDataItem {

    @TableId(value="EntryItemId", type = IdType.AUTO)
    private Integer entryItemId;

    private Integer parentEntryId;

    private Integer entryId;

    private Integer itemId;

    private String itemValue;

    private String itemAlias;

    private Integer isSystem;

    private Integer isDefault;

    private String description;

    private String extendField1;

    private String extendField2;

    private String extendField3;

    private String extendField4;

    private String extendField5;

    private Integer canEdit;

    private Integer canDelete;

    private Integer canTimeliness;
}
