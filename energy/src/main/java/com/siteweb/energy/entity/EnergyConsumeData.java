package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("Energy_ConsumeData")
public class EnergyConsumeData {

    @TableId(value="id", type = IdType.AUTO)
    private Integer id;

    private String objectId;

    private String objectTypeId;

    private Integer energyTypeId;

    private Integer year;

    private Integer month;

    private Double energySavingValue;

    private Double planValue;

    private Double overstepValue;

    private Double planValuePreAlarm;

    private Double overstepValuePreAlarm;

}
