package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Energy_DimensionType")
public class EnergyDimensionType {

    @TableId(value="DimensionTypeId", type = IdType.AUTO)
    private Integer dimensionTypeId;

    private String dimensionTypeName;

    private Integer isUsed;

    private Integer operatorUserId;

    private Date updateTime;

    private String Notes;
}
