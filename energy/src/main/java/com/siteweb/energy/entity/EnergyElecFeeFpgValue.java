package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 能耗-保存峰平谷的具体值
 */
@Data
@NoArgsConstructor
@TableName("Energy_ElecFeeFpgValue")
public class EnergyElecFeeFpgValue {

    /**
     * 自增主键
     */
    @TableId(value="FpgValueId", type = IdType.AUTO)
    private Integer fpgValueId;

    private Integer fpgId;
    private Integer stepPriceId;
    private Integer schemeId;
    private String fpgValue;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;

    private String extendField1;

}
