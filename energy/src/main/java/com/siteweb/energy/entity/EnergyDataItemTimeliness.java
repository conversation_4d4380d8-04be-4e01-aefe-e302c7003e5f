package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.sql.Date;

@Data
@NoArgsConstructor
@TableName("Energy_DataItemTimeliness")
public class EnergyDataItemTimeliness {

    @TableId(value = "ItemTimelinessId", type = IdType.AUTO)
    private Integer itemTimelinessId;

    private Integer entryItemId;

    private String ItemValue;

    private String extendField1;

    private String extendField2;

    private String extendField3;

    private String extendField4;

    private String extendField5;

    private Date startTime;

    //    1 过期 2 当前生效 3 未生效
    @TableField(exist = false)
    private Integer status;

}
