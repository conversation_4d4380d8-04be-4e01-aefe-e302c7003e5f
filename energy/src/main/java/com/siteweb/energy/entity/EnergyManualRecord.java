package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_manualrecord")
public class EnergyManualRecord {
    @TableId(value="Id", type = IdType.AUTO)
    private Integer id;

    private Integer complexIndexId;

    private Date recordTime;

    private Double recordValue;

    private Integer calcType;

    private Integer userId;

    private String userName;

    private Date insertTime; //抄表时间

    private String extendField1;

    private String unit;
}
