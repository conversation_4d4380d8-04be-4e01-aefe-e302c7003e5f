package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("energy_carbonemissionmanage")
public class EnergyCarbonEmissionPara {

    @TableId(value="id", type = IdType.AUTO)

    private Integer id;
    /**
     * 节点ID
     */
    private String objectId;
    /**
     * 节点类型
     */
    private String objectTypeId;
    /**
     * 面积
     */
    private Float area;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 月份
     */
    private Integer month;
    /**
     * 月计划量
     */
    private Float planValue;
    /**
     * 年总配额
     */
    private Float yearPlanTotalValue;
    /**
     * 是否面积。面积1；产出0。
     */
    private Integer isArea;
    /**
     * 单位
     */
    private String units;

}
