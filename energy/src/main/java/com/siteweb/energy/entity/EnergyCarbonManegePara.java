package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("energy_carbonmanage")
public class EnergyCarbonManegePara {

    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    /**
     * 当前资源id
     */
    private String objectId;
    /**
     * 当前资源类型
     */
    private String objectTypeId;
    /**
     * 配置年份
     */
    private Integer Year;
    /**
     * 配置月份
     */
    private Integer month;
    /**
     * 月计划量
     */
    private Float planValue;
    /**
     * 年计划总量
     */
    private Float yearPlanTotalValue;

}
