package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 能耗-电费方案类
 */
@Data
@NoArgsConstructor
@TableName("Energy_ElecFeeScheme")
public class EnergyElecFeeScheme {

    /**
     * 自增主键
     */
    @TableId(value="SchemeId", type = IdType.AUTO)
    private Integer schemeId;

    /**
     * 方案名称
     */
    private String schemeName;

    private String appliedRange;

    private Date enableDate;

    private Date deactivateDate;

    private Integer startMonth;
    private Integer endMonth;

    /** 是否启用： 1-启用； 其他-都表示不启用 */
    private Integer enableStatus;

    private Date createDate;
    private Date updateDate;

    private Integer createrId;
    private String createrName;
    private Integer updaterId;
    private String updaterName;
    private Integer businessTypeId;
    private String businessTypeName;

    private String extendField1;
    private String extendField2;

    @TableField(exist = false)
    private String typeName;

}
