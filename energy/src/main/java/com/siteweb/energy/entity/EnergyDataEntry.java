package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("Energy_DataEntry")
public class EnergyDataEntry {

    @TableId(value="EntryId", type = IdType.AUTO)
    private Integer entryId;

    private String entryName;

    private String entryAlias;

    private String description;

    private String extendField1;

    private Integer canEdit;

    private Integer canDelete;

}
