package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_customer_tb_reportrecord")
public class EnergyCustomerTBReportRecord {
    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    private Integer reportId;
    private Date createTime;
    private String reportName;
    private String filePath;
    private String notes;
}
