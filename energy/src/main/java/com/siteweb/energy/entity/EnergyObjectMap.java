package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("Energy_ObjectMap")
public class EnergyObjectMap {

    @TableId(value="Id", type = IdType.AUTO)
    private Integer id;

    private Integer dimensionTypeId;

    private Integer levelId;

    private Integer objectId;

    private Integer objectIdType;

    private Integer parentObjectId;

    private Integer parentObjectIdType;

    private String levelOfPath;

    private String notes;
}
