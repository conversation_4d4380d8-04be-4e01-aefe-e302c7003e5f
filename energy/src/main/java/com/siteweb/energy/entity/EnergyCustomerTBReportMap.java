package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("energy_customer_tb_reportmap")
public class EnergyCustomerTBReportMap {

    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    private Integer reportId;
    private Integer sheetId;
    private Integer rowId;
    private Integer cellId;
    private Integer hour;
    private String equipmentId;
    private String signalId;

}
