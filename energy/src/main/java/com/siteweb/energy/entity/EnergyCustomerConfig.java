package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_customer_config")
public class EnergyCustomerConfig {

    @TableId(value="id")
    private Integer id;

    private String customername;

    private Boolean enable;

    private Date createtime;

    private String notes;

}
