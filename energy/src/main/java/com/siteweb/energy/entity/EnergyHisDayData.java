package com.siteweb.energy.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @author: deovo
 * @time: 2022/12/22 16:53
 * @description:
 **/
@Data
@NoArgsConstructor
@Measurement(name = "EnergyHisDayData")
public class EnergyHisDayData {

    @Column(name = "time")
    public String time;

    @Column(name = "ComplexIndexId", tag = true)
    public String complexIndexId;

    @Column(name = "CalcTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String calcTime;

    @Column(name = "BusinessTypeId")
    public String businessTypeId;

    @Column(name = "IndexValue")
    public String indexValue;

    @Column(name = "OriginalValue")
    public String originalValue;

    @Column(name = "Unit")
    public String unit;

    @Column(name = "Abnormal", tag = true)
    public String abnormal;

    public String getSampleTime() {
        return getTime();
    }

    public String getTime() {
        String tempTime = this.time;
        if (tempTime != null && !tempTime.equals("")) {
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }

    public String getIndexValue() {
        if (this.indexValue == null || this.indexValue.equals("")) {
            return "-";
        }
        BigDecimal value = BigDecimal.valueOf(Double.parseDouble(this.indexValue));
        return value.toString();
    }

    public Date getDateTime() {
        Date time = null;
        try {
            time = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(getTime());
        } catch (Exception e) {
        }
        return time;
    }

}

