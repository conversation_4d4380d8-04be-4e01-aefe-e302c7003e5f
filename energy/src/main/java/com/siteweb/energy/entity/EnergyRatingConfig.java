package com.siteweb.energy.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("energy_ratingconfig")
public class EnergyRatingConfig {

    @TableId(value="ratingConfigId", type = IdType.AUTO)
    private Integer ratingConfigId;

    private String outDryTempParam;

    private String outWetTempParam;

    private String inDryTempParam;

    private String inWetTempParam;

    private String runningLoadParam;

    private String itPowerParam;

    private String totalPowerParam;

    private Integer objectId;

    private String name;

    private Integer intervalSecond;

    /*结束一个配置任务的时候 这些字段需要赋null*/
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer status;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date startTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String workingCondition;

    private Integer userId;

    private Integer cityId;

}
