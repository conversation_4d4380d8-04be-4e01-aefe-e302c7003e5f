package com.siteweb.energy.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper;
import com.siteweb.complexindex.service.BusinessDefinitionMapService;
import com.siteweb.complexindex.service.ComplexIndexDefinitionService;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.mapper.EnergyDataConfigItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class EnergyComplexIndexManager {

    @Autowired
    private BusinessDefinitionMapService businessDefinitionMapService;
    @Autowired
    private ComplexIndexService complexIndexService;
    @Autowired
    private ComplexIndexDefinitionService complexIndexDefinitionService;
    @Autowired
    ComplexIndexBusinessTypeMapper complexIndexBusinessTypeMapper;
    @Autowired
    EnergyDataConfigItemMapper energyDataConfigItemMapper;
    //缓存指标
    private List<ComplexIndex> allComplexIndex = new ArrayList<>();
    //缓存所有总用能指标
    private List<ComplexIndex> allTotalConsumeComplexIndex = new ArrayList<>();
    //缓存所有分项能指标
    private List<ComplexIndex> allOptionConsumeComplexIndex = new ArrayList<>();
    //缓存能源类型与指标类型的关系
    private List<BusinessDefinitionMap> allBusinessDefinitionMap = new ArrayList<>();
    //缓存指标定义类型
    private List<ComplexIndexDefinition> allComplexIndexDefinition = new ArrayList<>();
    //缓存能源类型
    private List<ComplexIndexBusinessType> allComplexIndexBusinessType = new ArrayList<>();
    private List<EnergyDataItem> allEnergyDataItems = new ArrayList<>();

    public void Init(){
        allComplexIndex = complexIndexService.findAll().stream().filter(i->i.getBusinessTypeId() != null && i.getComplexIndexDefinitionId() != null).collect(Collectors.toList());
        allBusinessDefinitionMap = businessDefinitionMapService.findAll();
        allComplexIndexDefinition = complexIndexDefinitionService.findAll();
        List<Integer> lstTotalConsumeDefinitionIds = allBusinessDefinitionMap.stream().filter(i->i.getComplexIndexDefinitionTypeId().equals(1)).map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());
        List<Integer> lstOptionConsumeDefinitionIds = allBusinessDefinitionMap.stream().filter(i->i.getComplexIndexDefinitionTypeId().equals(3)).map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());

        if (lstTotalConsumeDefinitionIds.size() == 0)
            return;
        allTotalConsumeComplexIndex = allComplexIndex.stream().filter(i->lstTotalConsumeDefinitionIds.contains(i.getComplexIndexDefinitionId())).collect(Collectors.toList());

        allOptionConsumeComplexIndex = allComplexIndex.stream().filter(i->lstOptionConsumeDefinitionIds.contains(i.getComplexIndexDefinitionId())).collect(Collectors.toList());

        QueryWrapper<ComplexIndexBusinessType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parentId",1);
        allComplexIndexBusinessType = complexIndexBusinessTypeMapper.selectList(queryWrapper);
        allEnergyDataItems = energyDataConfigItemMapper.selectList(null);
    }

    public List<ComplexIndex> GetAllComplexIndex(){
        return allComplexIndex;
    }

    public List<ComplexIndex> GetAllTotalConsumeComplexIndex(){
        return allTotalConsumeComplexIndex;
    }
    public List<ComplexIndex> GetAllOptionConsumeComplexIndex(){
        return allOptionConsumeComplexIndex;
    }

    public List<BusinessDefinitionMap> GetAllBusinessDefinitionMap(){
        return allBusinessDefinitionMap;
    }

    public List<ComplexIndexDefinition> GetAllComplexIndexDefinition(){
        return allComplexIndexDefinition;
    }
    public List<ComplexIndexBusinessType> getAllComplexIndexBusinessType(){
        return allComplexIndexBusinessType.stream().filter(o->o.getDescription()!=null).collect(Collectors.toList());
    }
    public ComplexIndexBusinessType complexIndexBusinessTypeById(Integer businessTypeId){
        return allComplexIndexBusinessType.stream().filter(o->o.getBusinessTypeId().equals(businessTypeId)).findFirst().orElse(null);
    }
    public List<EnergyDataItem> getEnergyDataItemsByEntryId(Integer entryId){
        return allEnergyDataItems.stream().filter(o->o.getEntryId().equals(entryId)).collect(Collectors.toList());
    }
    /** 根据业务类型id，得到它的单位 */
    public String getUnitByBusinessTypeId(Integer businessTypeId) {
        if(businessTypeId == null) return "";
        ComplexIndex thisIndex = allTotalConsumeComplexIndex.stream().filter(i->i.getUnit() != null && i.getBusinessTypeId() != null
                && i.getBusinessTypeId() == businessTypeId ).findFirst().orElse(null);
        return thisIndex == null ? "" : thisIndex.getUnit();
    }
}
