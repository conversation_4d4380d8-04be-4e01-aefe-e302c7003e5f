package com.siteweb.energy.manager;


import com.siteweb.energy.entity.EnergyCustomerConfig;
import com.siteweb.energy.service.EnergyOverViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CronExpressionProvider {

    private String baidoCronExpression = "0 0 5 * * ?"; // Default 5 hour cron expression

    @Autowired
    private EnergyOverViewService energyOverViewService;

    public String getBaidoCronExpression() {
        try {
            EnergyCustomerConfig config = energyOverViewService.getCustomerConfig(5);
            if (config != null && config.getNotes() != null && !config.getNotes().isEmpty()) {
                baidoCronExpression = config.getNotes();
            }
            log.info("CronExpressionProvider Cron expression initialized to: {}", baidoCronExpression);
        } catch (Exception e) {
            log.error("CronExpressionProvider Failed to initialize cron expression, using default: {}", baidoCronExpression, e);
        }
        return baidoCronExpression;
    }
}
