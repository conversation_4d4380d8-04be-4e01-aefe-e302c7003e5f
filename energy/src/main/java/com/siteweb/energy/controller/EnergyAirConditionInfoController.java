package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.service.EnergyAirConditionInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api/energy/energyapi/airconditioninfo")
@Api(value = "EnergyAirConditionInfoController",tags = "快手报表能管空调信息统计相关接口")
public class EnergyAirConditionInfoController {
    @Autowired
    private EnergyAirConditionInfoService energyAirConditionInfoService;

    @ApiOperation(value = "获取空调设备列表")
    @GetMapping(value = "/getairconditionlist",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirConditionlist(){
        return ResponseHelper.successful(energyAirConditionInfoService.getAirConditionlist());
    }

    @ApiOperation(value = "获取某个空调的数据统计信息")
    @GetMapping(value = "/getairconditiondata",produces = MediaType.APPLICATION_JSON_VALUE, params = {"startTime","endTime","equipmentId"})
    public ResponseEntity<ResponseResult> getAirConditionData(Date startTime, Date endTime, String equipmentId){
        return ResponseHelper.successful(energyAirConditionInfoService.getAirConditionData(startTime, endTime, equipmentId));
    }

    @ApiOperation(value = "获取不同负载率不同温度下的数据统计信息")
    @GetMapping(value = "/getloadratetempdata",produces = MediaType.APPLICATION_JSON_VALUE, params = {"startTime","endTime","loadranges", "tdranges" })
    public ResponseEntity<ResponseResult> getLoadRateTempData(Date startTime, Date endTime, String loadranges, String tdranges){
        return ResponseHelper.successful(energyAirConditionInfoService.getLoadRateTempData(startTime, endTime, loadranges, tdranges));
    }

}
