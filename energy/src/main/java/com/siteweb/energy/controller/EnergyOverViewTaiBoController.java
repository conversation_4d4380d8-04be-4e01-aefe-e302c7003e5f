package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.scheduler.EnergyTaiBoHistoryDataSchedule;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.energy.service.EnergyTaiBoReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Date;


@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyOverViewTaiBoController",tags = "台玻能耗全景图相关接口")
public class EnergyOverViewTaiBoController {

    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private EnergyTaiBoReportService energyTaiBoReportService;
    @ApiOperation("电能耗三路进线")
    @GetMapping(value = "/electricityThreeRoute",params = {"dimensionTypeId","businessTypeId","timeType","startTime","endTime"})
    public ResponseEntity<ResponseResult> getPowerRatio(
            @RequestParam Integer dimensionTypeId,
            @RequestParam Integer businessTypeId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String timeType ){
        if(businessTypeId.equals(4))//气两路进线
            return ResponseHelper.successful(energyOverViewService.getPowerRatio(dimensionTypeId,businessTypeId,startTime,endTime,timeType,42));
        return ResponseHelper.successful(energyOverViewService.getPowerRatio(dimensionTypeId,businessTypeId,startTime,endTime,timeType,6));

    }

    @ApiOperation("用水类型占比饼图")
    @GetMapping(value = "/waterType",params = {"resourceStructureId","dimensionTypeId","businessTypeId","timeType","startTime","endTime"})
    public ResponseEntity<ResponseResult> getWaterType(
            @RequestParam String resourceStructureId,
            @RequestParam Integer dimensionTypeId,
            @RequestParam Integer businessTypeId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String timeType
    ){
        return ResponseHelper.successful(energyOverViewService.getWaterType(resourceStructureId,dimensionTypeId,businessTypeId,startTime,endTime,timeType));
    }

    @ApiOperation("压缩空气比功率柱状图")
    @GetMapping(value = "/airHistogram",params = {"resourceStructureId","dimensionTypeId","businessTypeId","timeType","startTime","endTime"})
    public ResponseEntity<ResponseResult> getAirHistogram(
            @RequestParam String resourceStructureId,
            @RequestParam Integer dimensionTypeId,
            @RequestParam Integer businessTypeId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String timeType
    ){
        return ResponseHelper.successful(energyOverViewService.getAirHistogram(resourceStructureId,dimensionTypeId,businessTypeId,startTime,endTime,timeType,44));
    }


    @ApiOperation(value = "查询台玻自动报表")
    @GetMapping(value = "/taiBoReportList",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetTaiBoReportList(
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String reportIds) throws ParseException {

        return ResponseHelper.successful(energyTaiBoReportService.GetTaiBoReportList(startTime,endTime,reportIds));
    }

    @ApiOperation(value = "获取台波自动报表模板")
    @GetMapping(value = "/taiBoReportTemplate",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetTaiBoReportTemplate() throws ParseException {
        return ResponseHelper.successful(energyTaiBoReportService.GetTaiBoReportTemplate());
    }


    @ApiOperation(value = "下载台玻自动报表")
    @GetMapping(value = "/taiBoReportListDownLoad",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Resource> GetTaiBoReportListDownLoad(
            @RequestParam Integer pId) throws ParseException {
        Object[] rtn = energyTaiBoReportService.GetTaiBoReportListDownLoad(pId);
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(rtn[1].toString(), StandardCharsets.UTF_8));
        headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
        headers.add(HttpHeaders.CONTENT_LENGTH, rtn[2].toString());

        // 创建 ResponseEntity 对象，并设置响应体、响应头和响应状态码
        return ResponseEntity.ok()
                .headers(headers)
                .body((InputStreamResource)rtn[0]);
    }


//    @ApiOperation(value = "Job测试")
//    @PostMapping(value = "/taiBoReportTest",produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> TaiBoReportTest(){
//        energyTaiBoReportService.createMonthlyNaturalGasReport();
//        return ResponseHelper.successful();
//    }
}
