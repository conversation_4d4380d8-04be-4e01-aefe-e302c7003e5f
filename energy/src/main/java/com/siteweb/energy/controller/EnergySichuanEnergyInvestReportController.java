package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.SichuanEnergyInvestSignalDiffRequestBody;
import com.siteweb.energy.service.EnergySichuanEnergyInvestReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/api/energy/sichuan")
@Api(value = "EnergyReportController", tags = "四川能投定制报表查询相关接口")
public class EnergySichuanEnergyInvestReportController {
    @Autowired
    private EnergySichuanEnergyInvestReportService energySichuanEnergyInvestReportService;

    @ApiOperation(value = "获取楼层/基站列表")
    @GetMapping(value = "/getbulidlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getBuildList() {
        return ResponseHelper.successful(energySichuanEnergyInvestReportService.getBuildingList());
    }

    @ApiOperation(value = "获取房间/局房列表")
    @GetMapping(value = "/getroomlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRoomList(@RequestParam Integer buildingId) {
        return ResponseHelper.successful(energySichuanEnergyInvestReportService.getRoomList(buildingId));
    }

    @ApiOperation(value = "获取设备列表")
    @GetMapping(value = "/getequipmentlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentList(@RequestParam Integer resourceStructureId) {
        return ResponseHelper.successful(energySichuanEnergyInvestReportService.getEquipmentList(resourceStructureId));
    }


    @ApiOperation(value = "根据设备ID获取信号列表")
    @GetMapping(value = "/getequipmentsignallist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSignalList(@RequestParam Integer equipmentId) {
        return ResponseHelper.successful(energySichuanEnergyInvestReportService.getEquipmentSignalList(equipmentId));
    }

    @ApiOperation(value = "信号差值统计表")
    @PostMapping(value = "/signaldifferencereport", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getSignalDifferenceReport(@RequestBody SichuanEnergyInvestSignalDiffRequestBody requestBody) {
        if (requestBody.getQueryType().equals("y"))
            return ResponseHelper.successful(energySichuanEnergyInvestReportService.getSignalDifferenceYearReport(requestBody.getSignalList(), requestBody.getQueryTime()));
        return ResponseHelper.successful(energySichuanEnergyInvestReportService.getSignalDifferenceMonthReport(requestBody.getSignalList(), requestBody.getQueryTime()));
    }

    @ApiOperation(value = "微模块机柜电量表")
    @GetMapping(value = "/headboardpowerreport", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getHeadBoardPowerReport(@RequestParam Integer AEquipmentId,
                                                                  @RequestParam Integer BEquipmentId,
                                                                  @RequestParam Date queryTime) {
        return ResponseHelper.successful(energySichuanEnergyInvestReportService.getHeadBoardPowerReport(AEquipmentId, BEquipmentId, queryTime));
    }

}
