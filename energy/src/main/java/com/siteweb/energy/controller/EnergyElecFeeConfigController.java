package com.siteweb.energy.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.ElecFeeFpg;
import com.siteweb.energy.dto.ElecFeeScheme;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyElecFeeConfigOperateLog;
import com.siteweb.energy.entity.EnergyElecFeeStepPrice;
import com.siteweb.energy.model.FpgInfo;
import com.siteweb.energy.service.EnergyElecFeeConfigOperateLogService;
import com.siteweb.energy.service.EnergyElecFeeConfigService;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 *  价格配置后台相关接口
 *
 *  * 20230310: 原电价配置后台相关controller、service、mapper及entity、数据库表等的命名，都包含有"ElecFee"关键字。
 *              现此功能由电价配置扩充为单价配置，包含有"ElecFee"关键字的相关命名不作修改，只扩充功能逻辑。
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyElecFeeConfigController",tags = "价格配置相关接口")
public class EnergyElecFeeConfigController {

    private final Logger log = LoggerFactory.getLogger(EnergyElecFeeConfigController.class);

    @Autowired
    private EnergyElecFeeConfigOperateLogService operateLogService;
    @Autowired
    private EnergyElecFeeConfigService configService;

    @Autowired
    private EnergyStructureService energyStructureService;

    @ApiOperation(value = "获取完整默认层级树")
    @GetMapping(value = "/resourcestructuretree")
    public ResponseEntity<ResponseResult> findResourcestructures() {
        //ResourceStructure resourceStructureRoot = configService.findResourceStructureTree();
        ResourceStructureTreeDTO resourceStructureRoot = energyStructureService.getDefaultTree();
        return Optional.ofNullable(resourceStructureRoot)
                .map(result -> ResponseHelper.successful(resourceStructureRoot, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "查询全部价格配置操作日志(不加载详情字段)")
    @GetMapping(value = "/operateLogs",produces = MediaType.APPLICATION_JSON_VALUE)
    public List<EnergyElecFeeConfigOperateLog> getAllExcludeChangeContentOrderBy(){
        return operateLogService.getAllExcludeChangeContentOrderBy();
    }

    @ApiOperation(value = "查询全部价格配置操作日志")
    @GetMapping(value = "/alloperateLogs",produces = MediaType.APPLICATION_JSON_VALUE)
    public List<EnergyElecFeeConfigOperateLog> getAllOperateLog(){
        return operateLogService.getAllOperateLog();
    }

    /** 通过主键id获取其 logDetail */
    @ApiOperation(value = "查询某条操作日志的详情")
    @GetMapping(value = "/logDetail/{logId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getLogDetail(@PathVariable Integer logId) {
        ResultObject<String> result = operateLogService.getLogDetail(logId);
        return responseOK(result);
    }

    /** 新增一个方案 */
    @ApiOperation(value = "新增一个方案")
    @PostMapping(value = "/elecFeeScheme",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addElecFeeScheme(@Valid @RequestBody ElecFeeScheme newScheme
            ,@RequestParam Integer userId,@RequestParam String userName){
        if(newScheme == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newScheme.getBusinessTypeId() == null) {
            return responseBadRequest(new ResultObject<String>("BusinessTypeId is Null",0));
        } else {
            ResultObject<String> resultObject = null;
            try {
                resultObject = configService.addElecFeeScheme(newScheme, userId, userName);
            } catch (Exception e) {
                configService.reloadCache();
                log.error("add ElecFeeScheme throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",0));
            }
            return responseOK(resultObject);
        }
    }

    /** 修改一个方案的主体信息 */
    @ApiOperation(value = "修改一个方案的主体信息")
    @PutMapping(value = "/elecFeeScheme",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateElecFeeScheme(@Valid @RequestBody ElecFeeScheme newScheme
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(newScheme == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newScheme.getSchemeId() == null) {
            return responseOK(new ResultObject<String>("id is Null",0));
        } else if(newScheme.getBusinessTypeId() == null) {
            return responseBadRequest(new ResultObject<String>("BusinessTypeId is Null",0));
        }  else {
            try {
                ResultObject<String> resultObject = configService.updateElecFeeScheme(newScheme, userId, userName);
                return responseOK(resultObject);
            }catch (Exception e) {
                configService.reloadCache();
                log.error("updateElecFeeScheme throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",-1));
            }
        }
    }

    /** 通过层级id获取其所有方案集合 */
    @ApiOperation(value = "通过层级id获取其所有方案集合")
    @GetMapping(value = "/elecFeeSchemes/{resourceStructureId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getElecFeeSchemes(@PathVariable Integer resourceStructureId) {
        if(resourceStructureId == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<List<ElecFeeScheme>> resultObject = configService.getElecFeeSchemesByResourceStructureId(resourceStructureId);
            return responseOK(resultObject);
        }
    }

    /** 批量删除方案 */
    @ApiOperation(value = "批量删除方案")
    @DeleteMapping(value = "/elecFeeSchemes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delElecFeeSchemes(@RequestBody List<Integer> ids
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(ids == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<String> resultObject = null;
            try {
                resultObject = configService.delElecFeeSchemes(ids, userId, userName);
            } catch (Exception e) {
                configService.reloadCache();
                log.error("del ElecFeeSchemes throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",0));
            }
            return responseOK(resultObject);
        }
    }

    /** 通过schemeId获取方案主体信息(不包含阶梯定价与峰平谷相关信息) */
    @ApiOperation(value = "通过schemeId获取方案主体信息(不包含阶梯定价与峰平谷相关信息)")
    @GetMapping(value = "/elecFeeScheme/{schemeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getElecFeeScheme(@PathVariable Integer schemeId) {
        if(schemeId == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<ElecFeeScheme> resultObject = configService.getElecFeeSchemeBySchemeId(schemeId);
            return responseOK(resultObject);
        }
    }

    /** 通过schemeId获取此方案的阶梯定价集合 */
    @ApiOperation(value = "通过schemeId获取此方案的阶梯定价集合")
    @GetMapping(value = "/elecFeeStepPrices/{schemeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStepPrices(@PathVariable Integer schemeId) {
        if(schemeId == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<List<EnergyElecFeeStepPrice>> resultObject = configService.getStepPricesBySchemeId(schemeId);
            return responseOK(resultObject);
        }
    }

    /** 通过schemeId获取此方案的峰平谷集合 */
    @ApiOperation(value = "通过schemeId获取此方案的峰平谷集合")
    @GetMapping(value = "/elecFeeFpgs/{schemeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getFpgs(@PathVariable Integer schemeId) {
        if(schemeId == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<FpgInfo> resultObject = configService.getFpgsBySchemeId(schemeId);
            return responseOK(resultObject);
        }
    }

    /** 通过stepPriceId获取一个梯定价对象 */
    @ApiOperation(value = "通过stepPriceId获取一个梯定价对象")
    @GetMapping(value = "/elecFeeStepPrice/{stepPriceId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStepPrice(@PathVariable Integer stepPriceId) {
        if(stepPriceId == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<EnergyElecFeeStepPrice> resultObject = configService.getStepPriceByStepPriceId(stepPriceId);
            return responseOK(resultObject);
        }
    }

    /** 通过fpgId获取一条峰平谷记录 */
    @ApiOperation(value = "通过fpgId获取一条峰平谷记录")
    @GetMapping(value = "/elecFeeFpg", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getFpg(@RequestParam Integer fpgId, @RequestParam Integer schemeId) {
        if(fpgId == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<ElecFeeFpg> resultObject = configService.getFpgByFpgId(fpgId, schemeId);
            return responseOK(resultObject);
        }
    }

    /** 删除一条阶梯定价 */
    @ApiOperation(value = "删除一条阶梯定价")
    @DeleteMapping(value = "/elecFeeStepPrice", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delElecFeeStepPrice(@RequestParam Integer stepPriceId, @RequestParam Integer schemeId
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(stepPriceId == null || schemeId == null) {
            return responseBadRequest(new ResultObject<>("Some param is Null",0));
        } else {
            ResultObject<String> resultObject = null;
            try {
                resultObject = configService.delElecFeeStepPrice(stepPriceId, schemeId, userId, userName);
            } catch (Exception e) {
                configService.reloadCache();
                log.error("del ElecFeeStepPrice throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",0));
            }
            return responseOK(resultObject);
        }
    }

    /** 删除一条峰平谷记录 */
    @ApiOperation(value = "删除一条峰平谷记录")
    @DeleteMapping(value = "/elecFeeFpg", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delElecFeeFpg(@RequestParam Integer fpgId, @RequestParam Integer schemeId
            ,@RequestParam Integer userId,@RequestParam String userName) {

        if(fpgId == null || schemeId == null) {
            return responseBadRequest(new ResultObject<>("Some param is Null",0));
        } else {
            ResultObject<String> resultObject = null;
            try {
                resultObject = configService.delElecFeeFpg(fpgId, schemeId, userId, userName);
            } catch (Exception e) {
                configService.reloadCache();
                log.error("del ElecFeeFpg throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",0));
            }
            return responseOK(resultObject);
        }
    }

    /** 修改一个阶梯定价信息 */
    @ApiOperation(value = "修改一个阶梯定价信息")
    @PutMapping(value = "/elecFeeStepPrice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateElecFeeStepPrice(@Valid @RequestBody EnergyElecFeeStepPrice newStepPrice
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(newStepPrice == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newStepPrice.getStepPriceId() == null) {
            return responseOK(new ResultObject<String>("id is Null",0));
        } else {
            try {
                ResultObject<String> resultObject = configService.updateElecFeeStepPrice(newStepPrice, userId, userName);
                return responseOK(resultObject);
            }catch (Exception e) {
                configService.reloadCache();
                log.error("throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",-1));
            }
        }
    }

    /** 修改一个峰平谷信息 */
    @ApiOperation(value = "修改一个峰平谷信息")
    @PutMapping(value = "/elecFeeFpg",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateElecFeeFpg(@Valid @RequestBody ElecFeeFpg newFpg
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(newFpg == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newFpg.getFpgId() == null) {
            return responseOK(new ResultObject<String>("id is Null",0));
        } else if(newFpg.getSchemeId() == null) {
            return responseOK(new ResultObject<String>("schemeId is Null",0));
        } else if(newFpg.getFpgValues() == null || newFpg.getFpgValues().size() < 1) {
            return responseOK(new ResultObject<String>("fpgValueList is Null or Empty.",0));
        } else {
            try {
                ResultObject<String> resultObject = configService.updateElecFeeFpg(newFpg, userId, userName);
                return responseOK(resultObject);
            }catch (Exception e) {
                configService.reloadCache();
                log.error("throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",-1));
            }
        }
    }

    /** 新增一个阶梯定价信息 */
    @ApiOperation(value = "新增一个阶梯定价信息")
    @PostMapping(value = "/elecFeeStepPrice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addElecFeeStepPrice(@Valid @RequestBody EnergyElecFeeStepPrice newStepPrice
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(newStepPrice == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newStepPrice.getStepPriceId() != null) {
            return responseOK(new ResultObject<String>("id is Null",0));
        } else {
            ResultObject<String> resultObject = null;
            try {
                resultObject = configService.addElecFeeStepPrice(newStepPrice, userId, userName);
            } catch (Exception e) {
                configService.reloadCache();
                log.error("add ElecFeeStepPrice throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",0));
            }
            return responseOK(resultObject);
        }
    }

    /** 新增一个峰平谷信息 */
    @ApiOperation(value = "新增一个峰平谷信息")
    @PostMapping(value = "/elecFeeFpg",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addElecFeeFpg(@Valid @RequestBody ElecFeeFpg newFpg
            ,@RequestParam Integer userId,@RequestParam String userName) {
        if(newFpg == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newFpg.getFpgId() != null) {
            return responseOK(new ResultObject<String>("FpgId is not null",0));
        } else if(newFpg.getSchemeId() == null) {
            return responseOK(new ResultObject<String>("SchemeId is Null",0));
        } else if(newFpg.getFpgValues() == null || newFpg.getFpgValues().size() < 1) {
            return responseOK(new ResultObject<String>("fpgValueList is Null or Empty.",0));
        } else {
            ResultObject<String> resultObject = null;
            try {
                resultObject = configService.addElecFeeFpg(newFpg, userId, userName);
            } catch (Exception e) {
                configService.reloadCache();
                log.error("add ElecFeeFpg throw Exception: ", e);
                return responseOK(new ResultObject<String>("Server Exception",0));
            }
            return responseOK(resultObject);
        }
    }

    @ApiOperation(value = "通过给定deep，拿到从根节点开始的deep层的树")
    @GetMapping(value = "/resourcestructuretree",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResourceStructure getResourceStructures(@RequestParam Integer deep) {
        return configService.getResourceStructuresByPersonId(-99999, deep);
    }

    @ApiOperation("获取能耗相关的所有子类业务类型")
    @GetMapping(value = "/complexindexbusinesstypeforenergy",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexindexbusinesstypeForEnergy() {
        return ResponseHelper.successful(configService.getComplexindexbusinesstypeForEnergy());
    }

    private ResponseEntity<ResponseResult> responseOK(ResultObject resultObject) {
        ResponseResult result = new ResponseResult();
        result.setState(true);
        result.setData(resultObject);
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(String.valueOf(ErrorCode.NORMAL.value()));
        return new ResponseEntity(result, HttpStatus.OK);
    }
    private ResponseEntity<ResponseResult> responseBadRequest(ResultObject resultObject) {

        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setData(resultObject.getData());
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(resultObject.getErrcode().toString());
        result.setErrMsg(resultObject.getErrmsg());
        return new ResponseEntity(result, HttpStatus.BAD_REQUEST);
    }
}
