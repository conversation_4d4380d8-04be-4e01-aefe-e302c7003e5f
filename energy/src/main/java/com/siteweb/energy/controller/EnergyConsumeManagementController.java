package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyConsumeHistoryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import com.siteweb.energy.entity.EnergyConsumeConst;
import com.siteweb.energy.entity.EnergyConsumeData;
import com.siteweb.energy.service.EnergyConsumeManagementService;
import com.siteweb.energy.dto.EnergyConsumeDataParamsDTO;

import java.util.List;
import java.util.Locale;


@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyConsumeManagementController",tags = "用能管控相关接口")
@Component
public class EnergyConsumeManagementController {


    @Autowired
    private EnergyConsumeManagementService energyConsumeManagementService;

    @Value("${spring.web.locale}")
    private String locale;


    @ApiOperation(value = "获取对应层级人数和面积")
    @GetMapping(value = "/energyConsumeConst",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId"})
    public ResponseEntity<ResponseResult> findEnergyConsumeConst(String objectId,String objectTypeId){
        return ResponseHelper.successful(energyConsumeManagementService.findEnergyConsumeConst(objectId,objectTypeId));
    }


    @ApiOperation(value = "新增或编辑对应层级人数和面积")
    @PostMapping(value = "/energyConsumeConst",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveEnergyConsumeConst(@RequestBody EnergyConsumeConst energyConsumeConst){
        return ResponseHelper.successful(energyConsumeManagementService.saveEnergyConsumeConst(energyConsumeConst));
    }

    @ApiOperation(value = "获取对应层级已配置参数年份")
    @GetMapping(value = "/energyConsumeYears",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","energyTypeId"})
    public ResponseEntity<ResponseResult> findEnergyConsumeYears(String objectId,String objectTypeId,Integer energyTypeId){
        return ResponseHelper.successful(energyConsumeManagementService.findEnergyConsumeYears(objectId,objectTypeId,energyTypeId));
    }

    @ApiOperation(value = "获取对应层级用能配置")
    @GetMapping(value = "/energyConsumeData",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","energyTypeId","year"})
    public ResponseEntity<ResponseResult> findEnergyConsumeData(String objectId,String objectTypeId,Integer energyTypeId,Integer year){
        return ResponseHelper.successful(energyConsumeManagementService.findEnergyConsumeData(objectId,objectTypeId,energyTypeId,year));
    }

    @ApiOperation(value = "获取对应层级用能配置")
    @GetMapping(value = "/energyConsumeDataYoY",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","energyTypeId","year"})
    public ResponseEntity<ResponseResult> findEnergyConsumeDataYoY(String objectId,String objectTypeId,Integer energyTypeId,Integer year,Integer month){
        return ResponseHelper.successful(energyConsumeManagementService.findEnergyConsumeDataYoY(objectId,objectTypeId,energyTypeId,year,month));
    }

    @ApiOperation(value = "获取对应层级用能历史配置")
    @GetMapping(value = "/energyConsumeHistoryData",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","energyTypeId","year"})
    public ResponseEntity<ResponseResult> findEnergyConsumeHistoryData(String objectId,String objectTypeId,Integer energyTypeId,Integer year){

        List<EnergyConsumeHistoryDTO> res =  energyConsumeManagementService.findEnergyConsumeHistoryData(objectId,objectTypeId,energyTypeId,year);
        boolean isChina = locale.toLowerCase().equals("zh_cn");
        if (res == null){
            if (isChina){
                return ResponseHelper.failed("当前层级下没有配置对应能源总量指标");
            }else{
                return ResponseHelper.failed("The corresponding total energy indicator is not configured under the current level");
            }

        }else {
            return ResponseHelper.successful(res);
        }

    }


    @ApiOperation(value = "新增对应层级用能配置")
    @PostMapping(value = "/energyConsumeData",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveEnergyConsumeData(@RequestBody EnergyConsumeDataParamsDTO energyConsumeDataParamsDTO){
        return ResponseHelper.successful(energyConsumeManagementService.saveEnergyConsumeData(energyConsumeDataParamsDTO));
    }

    @ApiOperation(value = "更新对应层级用能配置")
    @PutMapping(value = "/energyConsumeData",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEnergyConsumeData(@RequestBody EnergyConsumeData energyConsumeData){
        return ResponseHelper.successful(energyConsumeManagementService.upDateEnergyConsumeData(energyConsumeData));
    }

    @ApiOperation(value = "删除对应层级用能配置")
    @DeleteMapping(value = "/energyConsumeData/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyConsumeData(@PathVariable Integer id){
        return ResponseHelper.successful(energyConsumeManagementService.deleteEnergyConsumeData(id));
    }



}
