package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.service.BusinessDefinitionMapService;
import com.siteweb.energy.dto.EnergyAppParameterDTO;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.service.EnergyAppService;
import com.siteweb.energy.service.EnergyDataConfigItemService;
import com.siteweb.energy.service.EnergyReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/energy/app")
@Api(value = "EnergyAPPController",tags = "辽宁联通定制相关接口")
public class EnergyAPPController {

    @Autowired
    private EnergyDataConfigItemService energyDataConfigItemService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private EnergyReportService energyReportService;
    @Autowired
    private EnergyAppService energyAppService;
    @Autowired
    private BusinessDefinitionMapService businessDefinitionMapService;

    @ApiOperation(value = "获取指标类型")
    @GetMapping(value = "/eleccomplexindexdefinition",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllEnergyDataItem(){
        return ResponseHelper.successful(energyDataConfigItemService.getAllByEntryId(10));
    }

    @ApiOperation(value = "通过层级节点和指标定义Id获取指标")
    @GetMapping(value = "/complexindexofobject",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "itemId"})
    public ResponseEntity<ResponseResult> getComplexindexOfObject(@RequestParam Integer objectId,@RequestParam Integer itemId){
        List<ComplexIndex> result = new ArrayList<>();

        List<Integer> lstDefinitionId = businessDefinitionMapService.getComplexIndexDefinitionIds(2,itemId);
        for(Integer complexIndexDefinitionId : lstDefinitionId){
            List<ComplexIndex> thisDefinitionComplexIndexs = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getObjectId().equals(objectId) && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)).toList();

            result.addAll(thisDefinitionComplexIndexs);
        }

        return ResponseHelper.successful(result);
    }

    //所选指标的小时、日、月、年，趋势查询
    @ApiOperation(value = "趋势查询")
    @PostMapping(value = "/energytrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyTrend(@RequestBody EnergyAppParameterDTO para){
        return ResponseHelper.successful(energyAppService.getEnergyTrend(para));
    }


}
