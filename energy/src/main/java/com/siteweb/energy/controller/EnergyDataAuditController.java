package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.ComplexCheckParameterDTO;
import com.siteweb.energy.dto.ComplexTreeParameter;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyErrorDataModifyRecord;
import com.siteweb.energy.service.EnergyDataAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/api/energy/energyapi/dataaudit")
@Api(value = "EnergyDataAuditController",tags = "能管数据稽查相关接口")
public class EnergyDataAuditController {
    @Autowired
    private EnergyDataAuditService energyDataAuditService;

    @ApiOperation(value = "获取稽查总览的异常数据列表和柱状图数据")
    @GetMapping(value = "/getenergyhiserrordata",produces = MediaType.APPLICATION_JSON_VALUE, params = {"startTime","endTime","businessTypeId"})
    public ResponseEntity<ResponseResult> getEnergyHisErrorData(Date startTime, Date endTime, String businessTypeId){
       return ResponseHelper.successful(energyDataAuditService.getEnergyHisErrorData(startTime, endTime, businessTypeId));
    }

    @ApiOperation(value = "稽查总览新增一条异常数据修改记录")
    @PostMapping(value = "/inserterrordatamodifyrecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> insertErrorDataModifyRecord(@RequestBody EnergyErrorDataModifyRecord energyErrorDataModifyRecord){
        return ResponseHelper.successful(energyDataAuditService.insertErrorDataModifyRecord(energyErrorDataModifyRecord));
    }

    @ApiOperation(value = "稽查总览根据指标Id和异常时间获取异常数据修改记录")
    @GetMapping(value = "/geterrordatamodifyrecord",produces = MediaType.APPLICATION_JSON_VALUE, params = {"complexIndexId", "errorTime"})
    public ResponseEntity<ResponseResult> getErrorDataModifyRecord(Integer complexIndexId, Date errorTime){
        return ResponseHelper.successful(energyDataAuditService.getErrorDataModifyRecord(complexIndexId, errorTime));
    }

    @ApiOperation(value = "指标稽查获取指标列表(手动检查指标的设备和测点是否存在)")
    @PostMapping(value = "/getcomplexnotexist",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexNotExist(@RequestBody ComplexCheckParameterDTO complexCheckParameterDTO){
        return ResponseHelper.successful(energyDataAuditService.getComplexNotExist(complexCheckParameterDTO.getObjectIds()));
    }

    @ApiOperation(value = "指标拓扑根据层级节点获取指标列表")
    @GetMapping(value = "/getcomplexlist",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId", "businessTypeId"})
    public ResponseEntity<ResponseResult> getComplexList(Integer objectId, Integer objectTypeId, String businessTypeId){
        return ResponseHelper.successful(energyDataAuditService.getComplexList(objectId, objectTypeId, businessTypeId));
    }

    @ApiOperation(value = "指标拓扑根据指标ID获取指标相关信息")
    @GetMapping(value = "/getcomplexinfo",produces = MediaType.APPLICATION_JSON_VALUE, params = {"complexIndexId"})
    public ResponseEntity<ResponseResult> getComplexInfo(String complexIndexId){
        return ResponseHelper.successful(energyDataAuditService.getComplexInfo(complexIndexId));
    }

    @ApiOperation(value = "指标拓扑根据指标获取树状结构")
    @PostMapping(value = "/getcomplextree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexTree(@RequestBody ComplexTreeParameter complexTreeParameter){
        return ResponseHelper.successful(energyDataAuditService.getComplexTree(complexTreeParameter.getComplexIndexId(), complexTreeParameter.getComplexIndexName(), complexTreeParameter.getExpression()));
    }

    @ApiOperation(value = "指标拓扑 获取指标列表与曲线")
    @GetMapping(value = "/getcomplexListAndCurve",produces = MediaType.APPLICATION_JSON_VALUE, params = {"startTime","endTime","complexIndexId"})
    public ResponseEntity<ResponseResult> getComplexListAndCurve(Date startTime, Date endTime, Integer complexIndexId){
        return ResponseHelper.successful(energyDataAuditService.getcomplexListAndCurve(startTime, endTime, complexIndexId));
    }

    @ApiOperation(value = "指标拓扑 获取测点的信息")
    @GetMapping(value = "/getsignalListAndCurve",produces = MediaType.APPLICATION_JSON_VALUE, params = {"startTime","endTime","equipmentId","signalId", "signalName"})
    public ResponseEntity<ResponseResult> getSignalListAndCurve(Date startTime, Date endTime, Integer equipmentId, Integer signalId, String signalName){
        return ResponseHelper.successful(energyDataAuditService.getSignalListAndCurve(startTime, endTime, equipmentId, signalId,signalName));
    }
}
