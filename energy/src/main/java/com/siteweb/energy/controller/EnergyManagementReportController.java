package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyManagementReportPar;
import com.siteweb.energy.service.EnergyManagementMapService;
import com.siteweb.energy.service.EnergyManagementService;
import com.siteweb.monitoring.service.ResourceStructureService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URISyntaxException;

/**
 * 节能措施功能 相关接口
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyManagementReportController",tags = "节能措施报表相关接口")
public class EnergyManagementReportController {
    private final Logger log = LoggerFactory.getLogger(EnergyManagementReportController.class);

    private static final String ENTITY_NAME = "EnergyManagementReport";

    @Autowired
    private EnergyManagementService energyManagementService;
    @Autowired
    private EnergyManagementMapService energyManagementMapService;
    @Autowired
    private ResourceStructureService resourceStructureService;

    /**
     * 1. 节能措施前后站点用电量报表
     * */
    @PostMapping(value="/energymanagementreportbeforeafter",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyReportBeforeAfter(
            @RequestBody EnergyManagementReportPar reportPar) throws URISyntaxException{
        log.debug("REST request energymanagementreport1 : {}", reportPar);
        if(reportPar.getEnergyIds() != null){
            return ResponseHelper.successful(energyManagementService.getEnergyReportBeforeAfter(reportPar));
        }
        return ResponseHelper.successful(null);
    }

    /**
     * 2. 节能措施站点与否用电量报表
     * */
    @PostMapping(value="/energymanagementreportonornot",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyReportOnOrNot(
            @RequestBody EnergyManagementReportPar reportPar) throws URISyntaxException{
        log.debug("REST request energymanagementreport1 : {}", reportPar);
        if(reportPar.getEnergyIds() != null){
            return ResponseHelper.successful(energyManagementService.getEnergyReportOnOrNot(reportPar));
        }
        return ResponseHelper.successful(null);
    }
    /**
     * 2.1 节能措施站点电量报表
     * */
    @PostMapping(value="/energymanagementreporton",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> getEnergyReportOn(
            @RequestBody EnergyManagementReportPar reportPar) throws URISyntaxException{
        log.debug("REST request energymanagementreport1 : {}", reportPar);
        if(reportPar.getEnergyIds() != null){
            return ResponseHelper.successful(energyManagementService.getEnergyReportOn(reportPar));
        }
        return ResponseHelper.successful(null);
    }
    /**
     * 2.2 节非能措施站点电量报表
     * */
    @PostMapping(value="/energymanagementreportnot",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> getEnergyReportNot(
            @RequestBody EnergyManagementReportPar reportPar) throws URISyntaxException{
        log.debug("REST request energymanagementreport1 : {}", reportPar);
        if(reportPar.getEnergyIds() != null){
            return ResponseHelper.successful(energyManagementService.getEnergyReportNot(reportPar));
        }
        return ResponseHelper.successful(null);
    }

    /**
     * 3 节非能措施效果排名
     * */
    @PostMapping(value="/energymanagementreportefficiency",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> getEnergyReportEfficiency(
            @RequestBody EnergyManagementReportPar reportPar) throws URISyntaxException{
        log.debug("REST request energymanagementreport1 : {}", reportPar);
        if(reportPar.getEnergyIds() != null){
            return ResponseHelper.successful(energyManagementService.getEnergyReportEfficiency(reportPar));
        }
        return ResponseHelper.successful(null);
    }
}
