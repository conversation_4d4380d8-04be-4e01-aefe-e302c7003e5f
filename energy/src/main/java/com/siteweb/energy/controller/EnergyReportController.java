package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyReportParameterDTO;
import com.siteweb.energy.dto.OverViewParameterDTO;
import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.energy.service.EnergyReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyReportController", tags = "能耗报表查询相关接口")
public class EnergyReportController {

    @Autowired
    private EnergyReportService energyReportService;

    @ApiOperation(value = "用能趋势查询")
    @PostMapping(value = "/EnergyConsumeTrendReport",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> EnergyConsumeTrendReport(@RequestBody EnergyReportParameterDTO para){
        if (para.getTimeType().toLowerCase().equals("y"))
            return ResponseHelper.successful(energyReportService.EnergyConsumeYearTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("m"))
            return ResponseHelper.successful(energyReportService.EnergyConsumeMonthTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("d"))
            return ResponseHelper.successful(energyReportService.EnergyConsumeDayTrendReport(para));
        else
            return ResponseHelper.failed("error timeType:"+para.getTimeType());
    }

    @ApiOperation(value = "用能损耗趋势查询")
    @PostMapping(value = "/EnergyLossTrendReport",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> EnergyLossTrendReport(@RequestBody EnergyReportParameterDTO para){
        if (para.getTimeType().toLowerCase().equals("y"))
            return ResponseHelper.successful(energyReportService.EnergyLossYearTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("m"))
            return ResponseHelper.successful(energyReportService.EnergyLossMonthTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("d"))
            return ResponseHelper.successful(energyReportService.EnergyLossDayTrendReport(para));
        else
            return ResponseHelper.failed("error timeType:"+para.getTimeType());
    }

    @ApiOperation(value = "用碳趋势查询")
    @PostMapping(value = "/EnergyCarbonTrendReport",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> EnergyCarbonTrendReport(@RequestBody EnergyReportParameterDTO para){
        if (para.getTimeType().toLowerCase().equals("y"))
            return ResponseHelper.successful(energyReportService.EnergyCarbonYearTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("m"))
            return ResponseHelper.successful(energyReportService.EnergyCarbonMonthTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("d"))
            return ResponseHelper.successful(energyReportService.EnergyCarbonDayTrendReport(para));
        else
            return ResponseHelper.failed("error timeType:"+para.getTimeType());
    }

    @ApiOperation(value = "能源费用趋势")
    @PostMapping(value = "/EnergyCostTrendReport",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> EnergyCostTrendReport(@RequestBody EnergyReportParameterDTO para){
        if (para.getTimeType().toLowerCase().equals("y"))
            return ResponseHelper.successful(energyReportService.EnergyCostYearTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("m"))
            return ResponseHelper.successful(energyReportService.EnergyCostMonthTrendReport(para));
        else if (para.getTimeType().toLowerCase().equals("d"))
            return ResponseHelper.successful(energyReportService.EnergyCostDayTrendReport(para));
        else
            return ResponseHelper.failed("error timeType:"+para.getTimeType());
    }
}
