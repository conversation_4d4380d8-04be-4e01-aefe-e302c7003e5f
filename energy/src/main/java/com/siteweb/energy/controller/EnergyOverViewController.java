package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.service.*;
import com.siteweb.complexindex.service.EnergyPreAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 能耗全景图页面 相关接口
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyOverViewController",tags = "能耗全景图相关接口")
public class EnergyOverViewController {
    private final Logger log = LoggerFactory.getLogger(EnergyOverViewController.class);

    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private EnergyGrowthService energyGrowthService;
    @Autowired
    private EnergyPreAlarmService energyPreAlarmService;
    @Autowired
    private EnergyHistoryDataService energyHistoryDataService;
    @Autowired
    @Lazy
    private EnergyHistoryDataHBDXReCalcService energyHistoryDataHBDXReCalcService;
    @Autowired
    @Lazy
    private EnergyHistoryDataHBDXService energyHistoryDataHBDXService;

    /**
     * 1. 获取所有层级节点
     * GET  /attachments : get all the ResourceStructure.     *
     * @return the ResponseEntity with status 200 (OK) and the list of documents in body
     */
    @ApiOperation(value = "获取是所选层级的所有节点")
    @GetMapping(value = "/energyoverviewresourcestructure",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyOverViewResourceStructureAll(
            @RequestParam int dimensionTypeId,
            @RequestParam int personId) {
        return ResponseHelper.successful(energyOverViewService.GetEnergyOverViewResourceStructureAll(dimensionTypeId,personId));
    }

    /**
     * 2. 上下级节点能耗
     * Post  /attachments : get all the ElecConsume of ResourceStructure.
     */
    @ApiOperation(value = "获取是所选层级节点的能耗值")
    @PostMapping(value = "/energyoverviewresourcestructureelecconsume",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyOverViewResourceStructureElecConsume(
            @RequestBody OverViewParameterDTO overViewParameterDTO){
        //能耗全景图组态sequence 和 timeType 未传
        if (overViewParameterDTO.getSequence() == null || overViewParameterDTO.getSequence().equals("")){
            overViewParameterDTO.setSequence("default");

            if (DateUtil.isTotalYear(overViewParameterDTO.getStartTime(),overViewParameterDTO.getEndTime())){
                overViewParameterDTO.setTimeType("y");
            } else if(DateUtil.isTotalMonth(overViewParameterDTO.getStartTime(),overViewParameterDTO.getEndTime())){
                overViewParameterDTO.setTimeType("m");
            } else  {
                overViewParameterDTO.setTimeType("d");
            }
        }

        return ResponseHelper.successful(energyOverViewService.GetEnergyOverViewResourceStructureElecConsume(overViewParameterDTO.getDimensionTypeId(),
                overViewParameterDTO.getBusinessTypeId(), overViewParameterDTO.getStartTime(),
                overViewParameterDTO.getEndTime(),overViewParameterDTO.getResourceStructureIds(),overViewParameterDTO.getUserId().intValue(),overViewParameterDTO.getTimeType(),overViewParameterDTO.getSequence()));
    }




    /**
     * 3.所选层级节点PUE.WUE     *
     * GET  /attachments : get pue of the ResourceStructure.
     */
    @ApiOperation(value = "获取所选节点的效率值(pue/wue等)")
    @GetMapping(value = "/energyoverviewresourcestructurepue",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyOverViewResourceStructurePUE(
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String timeType) throws ParseException{
        return ResponseHelper.successful(energyOverViewService.GetUEByRsourceStructureId(businessTypeId,resourceStructureId,startTime,endTime,timeType));
    }

    /**
     * 3.所选层级节点PUE.WUE     *
     * GET  /attachments : get pue of the ResourceStructure.
     */
    @ApiOperation(value = "获取所选节点的效率值(pue/wue等)")
    @GetMapping(value = "/energyoverviewresourcestructurepuetimetype",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyOverViewResourceStructurePUEByTimeType(
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam String timeType) throws ParseException{
        return ResponseHelper.successful(energyOverViewService.GetUEByRsourceStructureIdByTimeType(businessTypeId,resourceStructureId,timeType));
    }

    /**
     * 4. 所选层级节点子能耗
     * GET  /attachments : get all the child object elecconsume of ResourceStructure.
     */
    @ApiOperation(value = "获取所选层级节点子能耗")
    @GetMapping(value = "/energyoverviewresourcestructurechild",produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> GetEnergyOverViewResourceStructureChild(
            @RequestParam int dimensionTypeId,
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String timeType,
            @RequestParam String sequence) throws ParseException{
        // Date startTime = (new SimpleDateFormat("yyyy-MM-dd")).parse("2021-8-21");
        // String startTime = (new SimpleDateFormat("yyyy-MM-dd")).format(startTime);
        return ResponseHelper.successful(energyOverViewService.GetEnergyOverViewResourceStructureChild(dimensionTypeId,businessTypeId,resourceStructureId,startTime,endTime,timeType,false,sequence));
    }

    /**
     * 5. 历史数据用电量、同比、环比
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选层级节点历史数据用电量、同比、环比")
    @GetMapping(value = "/energygrowths",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> CalculationYOY(
            @RequestParam int businessTypeId,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String resourceStructureId,
            @RequestParam String timeType ){
        return ResponseHelper.successful(energyGrowthService.CalculationYOY(businessTypeId,startTime,endTime,resourceStructureId,timeType));
    }

    /**
     * 6. 所选节点用电类型饼图数据
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选层级节点用电类型饼图数据")
    @GetMapping(value = "/energypowertype",params = {"businessTypeId","startTime","endTime","resourceStructureId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findEnergyTypeByStructure(
            @RequestParam int businessTypeId,
            @RequestParam("startTime") @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam("resourceStructureId") String resourceStructureId,
            @RequestParam String timeType ){
        return ResponseHelper.successful(energyGrowthService.findEnergyTypeByStructure(businessTypeId,startTime,endTime,resourceStructureId,timeType,false));
    }

    /**
     * 6.1 所选节点用电类型费用饼图数据
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选层级节点用电类型费用饼图数据")
    @GetMapping(value = "/energypowertypefee",params = {"businessTypeId","startTime","endTime","resourceStructureId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findEnergyTypeByStructureFee(
            @RequestParam int businessTypeId,
            @RequestParam("startTime") @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam("endTime") @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam("resourceStructureId") String resourceStructureId ){
        return ResponseHelper.successful(energyGrowthService.findEnergyTypeByStructure(businessTypeId,startTime,endTime,resourceStructureId,null,true));
    }

    /**
     * 7. 所选节点实时能耗值
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选层级节点实时能耗值")
    @GetMapping(value = "/livecomplexindex",params = {"businessTypeId","resourceStructureId"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findliveComplexIndexByResourceStructure(
            @RequestParam("businessTypeId") int businessTypeId,
            @RequestParam("resourceStructureId") String resourceStructureId  ){
        return ResponseHelper.successful(energyOverViewService.findliveComplexIndexByResourceStructure(businessTypeId,resourceStructureId));
    }

    /**
     * 8. 所选节点实时能耗预警
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选层级节点实时能耗预警")
    @GetMapping(value = "/liveemsprealarm",params = {"resourceStructureId"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> liveEmsPreAlarmByGlobalSource(
            @RequestParam("resourceStructureId") String resourceStructureId ){
        return ResponseHelper.successful(energyOverViewService.liveEmsPreAlarmByResourceStructure(resourceStructureId));
    }

    /**
     * 8.1 所选节点历史能耗预警排名
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选层级节点历史能耗预警排名")
    @GetMapping(value = "/hisemsprealarmorder",params = {"resourceStructureId","startTime","endTime"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> hisEmsPreAlarmOrder(
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime){
        return ResponseHelper.successful(energyOverViewService.hisEmsPreAlarmOrder(resourceStructureId,startTime,endTime));
    }

    /**
     * 9. 获取所有预警的颜色值
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所有预警等级的信息、颜色值")
    @GetMapping(value = "/prealarmseverity",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPreAlarmSeverity(){
        return ResponseHelper.successful(energyOverViewService.getPreAlarmSeverity());
    }

    /**
     * 10 所选节点PUE/WUE等时间段历史值
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选节点PUE/WUE等时间段历史值")
    @GetMapping(value = "/historyueofresourceid",params = {"businessTypeId","resourceStructureId","startTime","endTime"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> hisUEOfResourceId(
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime){
        return ResponseHelper.successful(energyOverViewService.hisUEOfResourceId(businessTypeId,resourceStructureId,startTime,endTime));
    }

    /**
     * 11 所选节点各用能类型的历史值
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选节点各用能类型的历史值")
    @GetMapping(value = "/historyenergytypevalueofresourceid",params = {"businessTypeId","resourceStructureId","startTime","endTime"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> hisEventTypeOfResourceId(
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime){
        return ResponseHelper.successful(energyOverViewService.hisEventTypeOfResourceId(businessTypeId,resourceStructureId,startTime,endTime));
    }

    /**
     * 11.1 所选节点各用能类型费用的历史值
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选节点各用能类型费用的历史值")
    @GetMapping(value = "/historyenergytypefeevalueofresourceid",params = {"businessTypeId","resourceStructureId","startTime","endTime"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> hisEventTypeFeeOfResourceId(
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyOverViewService.hisEventTypeFeeOfResourceId(businessTypeId,resourceStructureId,startTime,endTime,timeType));
    }

    /**
     * 12 所选节点子节点的能耗用量
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选节点子节点的能耗用量")
    @GetMapping(value = "/childenergyvalueofresourceid",params = {"businessTypeId","resourceStructureId","startTime","endTime","level","personId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> childEnergyValueOfResourceId(
            @RequestParam int dimensionTypeId,
            @RequestParam int businessTypeId,
            @RequestParam String resourceStructureId,
            @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam int level,
            @RequestParam int personId){
        return ResponseHelper.successful(energyOverViewService.childEnergyValueOfResourceStructureId(dimensionTypeId,businessTypeId,resourceStructureId,startTime,endTime,level,personId,Integer.MAX_VALUE,false));
    }

    /**
     * 13 所选节点有几层子节点
     * GET  /attachments :
     */
    @ApiOperation(value = "获取所选节点有几层子节点")
    @GetMapping(value = "/childlevelsofresourceid",params = {"resourceStructureId","personId"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> childlevelsOfResourceId( @RequestParam  int dimensionTypeId,@RequestParam String resourceStructureId,@RequestParam int personId){
        return ResponseHelper.successful(energyOverViewService.childLevelsOfResourceId(dimensionTypeId,resourceStructureId,personId));
    }

    /**
     * 14. 获取有电费方案的节点
     */
    @ApiOperation(value = "获取有费方案的节点")
    @GetMapping(value = "/resourcestructureoffee",params = {"businessTypeId","personId"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetResourceStructureOfFee(@RequestParam int businessTypeId,@RequestParam int personId){
        return ResponseHelper.successful(energyOverViewService.GetResourceStructureOfFee(businessTypeId,personId));
    }

    // 15. 模拟指标历史数据
    @GetMapping(value = "/historycomplexindexs/simulator")
    public ResponseEntity<ResponseResult> historyComplexIndexSimulator(Integer times, Integer seconds, Integer number, Date date, String complexIndexIds, Double value) {
        return ResponseHelper.successful(energyOverViewService.historyComplexIndexSimulator(times, seconds, number, date, complexIndexIds, value));
    }

    // 15.1 模拟能耗历史数据
    @GetMapping(value = "/EnergyHisData/simulator")
    public ResponseEntity<ResponseResult> energyHisDataSimulator(Integer tableType,Integer times, Integer number, Date date, String complexIndexIds, Double value) {
        return ResponseHelper.successful(energyOverViewService.energyHisDataSimulator(tableType,times,  number, date, complexIndexIds, value));
    }

    @ApiOperation(value = "能耗小时日月表数据模拟")
    @GetMapping(value = "/EnergyHisData/hourdaymonthsimulator")
    public ResponseEntity<ResponseResult> energyHisDataSimulatorMonthDayHour(Date startTime, Date endTime, Integer complexIndexId, Double hourValue,boolean isAvg) {
        return ResponseHelper.successful(energyOverViewService.energyHisDataSimulatorMonthDayHour(startTime, endTime, complexIndexId, hourValue,isAvg));
    }

    @ApiOperation(value = "influxdb重新写EnergyHisMonthUEData")
    @GetMapping(value = "/EnergyHisData/writeenergyhismonthuedata")
    public ResponseEntity<ResponseResult> writeenergyhismonthuedata(Date startTime, Integer complexIndexId, Double hourValue) {
        return ResponseHelper.successful(energyOverViewService.WriteEnergyHisMonthUEData(startTime, complexIndexId, hourValue));
    }

    @ApiOperation(value = "influxdb重新写EnergyHisData表")
    @GetMapping(value = "/EnergyHisData/writeenergyhisdata")
    public ResponseEntity<ResponseResult> writeenergyhisdata(Date startTime, Integer complexIndexId, Double hourValue,String tableName) {
        return ResponseHelper.successful(energyOverViewService.WriteEnergyHisData(startTime, complexIndexId, hourValue,tableName));
    }
    @ApiOperation(value = "influxdb删除EnergyHisData表数据")
    @GetMapping(value = "/EnergyHisData/deleteenergyhisdata")
    public ResponseEntity<ResponseResult> deleteenergyhisdata(Date startTime, Integer complexIndexId,String tableName) {
        return ResponseHelper.successful(energyOverViewService.DeleteEnergyHisData(startTime, complexIndexId, tableName));
    }

    // 16
    @ApiOperation(value = "获取自定义维度树的所有根节点流入节点")
    @GetMapping(value = "/rootInflowNodes/{dimensionTypeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStepPrice(@PathVariable Integer dimensionTypeId) {
        return ResponseHelper.successful(energyOverViewService.getAllRootInflowNodes(dimensionTypeId));
    }

    // 17 获取能源类型单位名称
    @ApiOperation(value = "获取能源类型的单位名称")
    @GetMapping(value = "/getUnitByBusinessTypeId/{businessTypeId}")
    public ResponseEntity<ResponseResult> getUnitByBusinessTypeId(@PathVariable Integer businessTypeId) {
        return ResponseHelper.successful(energyOverViewService.getUnitByBusinessTypeId(businessTypeId));
    }

    // 能耗服务测试接口
    @GetMapping(value = "/energytest/1")
    public ResponseEntity<ResponseResult> test1(Date startTime,Date endTime, Integer complexIndexId,Double value) {

        if (value>100)
            energyHistoryDataService.manualEnergyHisDataInfluxdb(complexIndexId,startTime,endTime,value);
        else
            energyHistoryDataService.modifyEnergyHisHourDataInfluxdb(complexIndexId,startTime,value);
        return ResponseHelper.successful(energyPreAlarmService.findLastDayValueOfComplexIndex(1));
    }

    @GetMapping(value = "/shejiyuan")
    public ResponseEntity<ResponseResult> shejiyuan() {
        return ResponseHelper.successful(energyOverViewService.simulateSheJiYuan());
    }


    // 指标小时、日、月数据模拟
    @GetMapping(value = "/simulatorenergydata")
    public ResponseEntity<ResponseResult> simulatorenergydata(Date startTime,Date endTime) {
        return ResponseHelper.successful(energyOverViewService.simulatorenergydata(startTime,endTime));
    }

    // 河北电信手动修改当月PUE
    @GetMapping(value = "/modifymonthpuehebei")
    public ResponseEntity<ResponseResult> modifymonthpuehebei(Integer complexIndexId,Double value) {
        return ResponseHelper.successful(energyOverViewService.modifymonthpuehebei(complexIndexId,value));
    }

    // 河北电信当前小时修改为指定小时数据
    @GetMapping(value = "/modifyhourdatahebei")
    public ResponseEntity<ResponseResult> modifyhourdatahebei(Date modifyTime,Date usedTime,Integer hours,Integer tableType) {
        return ResponseHelper.successful(energyOverViewService.modifyhourdatahebei(modifyTime,usedTime,hours,tableType));
    }

    // 河北电信从某小时重新计算能耗数据
    @GetMapping(value = "/recalcenergydatahebei")
    public ResponseEntity<ResponseResult> recalcenergydatahebei(Date startTime,Date endTime) {
        return ResponseHelper.successful(energyHistoryDataHBDXReCalcService.energyHistoryDataHBDXService(startTime,endTime));
    }

    // 河北电信触发一次定时任务
    @GetMapping(value = "/startschedulerhebei")
    public ResponseEntity<ResponseResult> startschedulerhebei() {
        energyHistoryDataHBDXService.startschedulerhebei();
        return ResponseHelper.successful("success!");
    }

    // 18 能耗全景图导出
    @ApiOperation(value = "能耗全景图导出")
    @PostMapping(value = "/energyoverviewexcel")
    public void downLoadExcel(@RequestBody OverViewParameterDTO overViewParameterDTO, HttpServletResponse response) {
        energyOverViewService.downLoadExcel(overViewParameterDTO, response);
    }

}
