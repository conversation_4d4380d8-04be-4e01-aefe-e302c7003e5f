package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyCarbonEmissionDTO;
import com.siteweb.energy.service.EnergyCarbonEmissionManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyCarbonEmissionManagementController",tags = "碳排放强度管理相关接口")
public class EnergyCarbonEmissionManagementController {
    @Autowired
    private EnergyCarbonEmissionManagementService energyCarbonEmissionManagementService;

    @Value("${spring.web.locale}")
    private String locale;



    @ApiOperation(value = "获取单位")
    @GetMapping(value = "/getUnit",produces = MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<ResponseResult> getUnits(){
        return ResponseHelper.successful(energyCarbonEmissionManagementService.getUnit());
    }

    @ApiOperation(value = "获取对应层级已配置参数年份")
    @GetMapping(value = "/carbonEmissionConfiguredYears",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId"})
    public ResponseEntity<ResponseResult> findEnergyEmissionYears(String objectId,String objectTypeId){
        return ResponseHelper.successful(energyCarbonEmissionManagementService.findCarbonEmissionYears(objectId,objectTypeId));
    }
    @ApiOperation(value = "获取层级配置")
    @GetMapping(value = "/getCarbonEmissionConfigured",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","isArea","year"})
    public ResponseEntity<ResponseResult> findEnergyEmission(String objectId,String objectTypeId,Integer isArea,String year){
        return ResponseHelper.successful(energyCarbonEmissionManagementService.findCarbonEmission(objectId,objectTypeId,isArea,year));
    }

    @ApiOperation(value = "获取对应层级历史配置")
    @GetMapping(value = "/carbonEmissionHistoryData",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","year"})
    public ResponseEntity<ResponseResult> findCarbonEmissionHistoryData(String objectId,String objectTypeId,Integer year){
        return ResponseHelper.successful( energyCarbonEmissionManagementService.findCarbonEmissionHistoryData(objectId,objectTypeId,year));
    }

    @ApiOperation(value = "新增层级配置")
    @PostMapping(value = "/addNewConfiguration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addNewConfigurationData(@RequestBody EnergyCarbonEmissionDTO energyCarbonEmissionDTO){
        boolean isChina = locale.toLowerCase().equals("zh_cn");
        int result = energyCarbonEmissionManagementService.addNewConfiguration(energyCarbonEmissionDTO);
        if(result == 1)
            return ResponseHelper.successful("OK");
        else{
            if(isChina){
                return ResponseHelper.failed("插入失败");
            }else
                return ResponseHelper.failed("Insert failed");
        }

    }

    @ApiOperation(value = "删除层级配置")
    @DeleteMapping(value = "/deleteConfiguration/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteConfigurationData(@PathVariable Integer id){
        boolean isChina = locale.toLowerCase().equals("zh_cn");
        int result = energyCarbonEmissionManagementService.deleteConfiguration(id);
        if(result == 1){
            return ResponseHelper.successful("OK");
        }
        else{
            if(isChina){
                return ResponseHelper.failed("删除失败");
            }else
                return ResponseHelper.failed("Delete failed");
        }

    }

    @ApiOperation(value = "更新层级配置")
    @PostMapping(value = "/updateConfiguration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateConfigurationData(@RequestBody EnergyCarbonEmissionDTO energyCarbonEmissionDTO){
        boolean isChina = locale.toLowerCase().equals("zh_cn");
        Integer result = energyCarbonEmissionManagementService.updateConfiguration(energyCarbonEmissionDTO);
        if(result == 1)
            return ResponseHelper.successful("ok");
        else{
            if(isChina){
                return ResponseHelper.failed("更新失败");
            }else
                return ResponseHelper.failed("Update failed");
        }
    }
}
