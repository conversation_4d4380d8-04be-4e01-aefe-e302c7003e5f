package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyRatingDataJobParams;
import com.siteweb.energy.dto.EnergyRatingDataParams;
import com.siteweb.energy.entity.EnergyRatingData;
import com.siteweb.energy.service.EnergyRatingDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyRatingDataController", tags = "等级评定数据相关接口")
public class EnergyRatingDataController {

    @Autowired
    private EnergyRatingDataService energyRatingDataService;

    @ApiOperation(value = "获取等级评定列表")
    @GetMapping(value = "/getRatingDataList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyRatingDataList(EnergyRatingDataParams energyRatingDataParams) {
        return ResponseHelper.successful(energyRatingDataService.getEnergyRatingDataList(energyRatingDataParams));
    }

    @ApiOperation(value = "获取等级评定分页")
    @GetMapping(value = "/getRatingDataPage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyRatingDataPage(EnergyRatingDataParams energyRatingDataParams) {
        return ResponseHelper.successful(energyRatingDataService.getEnergyRatingDataPage(energyRatingDataParams));
    }

    @ApiOperation(value = "插入等级评定数据")
    @PostMapping(value = "/insertRatingData", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveEnergyRatingData(@RequestBody EnergyRatingData energyRatingData) {
        energyRatingDataService.saveEnergyRatingData(energyRatingData);
        return ResponseHelper.successful(true);
    }

    @ApiOperation(value = "批量删除等级评定数据")
    @DeleteMapping(value = "/deleteRatingData", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyRatingData(@RequestParam List<Integer> ratingDataId) {
        return ResponseHelper.successful(energyRatingDataService.deleteEnergyRatingData(ratingDataId));
    }

    @ApiOperation(value = "清空所有采集值")
    @GetMapping(value = "/clearRatingData", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> clearAllEnergyRatingData(@RequestParam Integer ratingConfigId) {
        return ResponseHelper.successful(energyRatingDataService.clearAllEnergyRatingData(ratingConfigId));
    }

    @ApiOperation(value = "创建采集任务")
    @PostMapping(value = "/createRatingDataJob", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> scheduleGenerateRatingData(@RequestBody EnergyRatingDataJobParams params) {
       if ( energyRatingDataService.scheduleGenerateRatingData(params)==0){
           return ResponseHelper.successful(true);
       }else{
           return ResponseHelper.failed("-1", "job is running", HttpStatus.BAD_REQUEST);
       }
    }

    @ApiOperation(value = "结束采集任务")
    @GetMapping(value = "/deleteRatingDataJob",params = "id", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> unScheduleGenerateRatingData(@RequestParam Integer id) {
        energyRatingDataService.unScheduleGenerateRatingData(id);
        return ResponseHelper.successful(true);
    }

    @ApiOperation(value = "获取对应配置的实时值")
    @GetMapping(value = "/getRatingDataRealTime", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRatingDataRealTime(Integer ratingConfigId) {
        return ResponseHelper.successful(energyRatingDataService.getRatingDataRealTime(ratingConfigId));
    }
}
