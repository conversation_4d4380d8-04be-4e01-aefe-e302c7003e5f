package com.siteweb.energy.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.entity.EnergyDimensionType;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.service.EnergyDimensionTypeService;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 能耗多维度 相关接口
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyDimensionController",tags = "多维护类型管理相关接口")
public class EnergyDimensionController {
    private final Logger log = LoggerFactory.getLogger(EnergyDimensionController.class);

    @Autowired
    private EnergyDimensionTypeService dimensionTypeService;
    @Autowired
    private EnergyStructureService energyStructureService;

    /** 新增一个维度类型 */
    @ApiOperation(value = "新增一个维度类型")
    @PostMapping(value = "/dimensionType",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addDimensionType(@Valid @RequestBody EnergyDimensionType newDimension) {
        if(newDimension == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else if(newDimension.getIsUsed() == null) {
            return responseOK(new ResultObject<>("isUsed is Null",0));
        } else {
            ResultObject<String> resultObject = dimensionTypeService.addDimensionType(newDimension);
            return responseOK(resultObject);
        }
    }

    /** 批量删除维度类型 */
    @ApiOperation(value = "批量删除维度类型")
    @DeleteMapping(value = "/dimensionType", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delDimensionType(@RequestBody List<Integer> ids) {
        if(ids == null) {
            return responseBadRequest(new ResultObject<>("Param is Null",0));
        } else {
            ResultObject<String> resultObject = dimensionTypeService.delDimensionTypeByIds(ids);
            return responseOK(resultObject);
        }
    }

    /** 更新一个维度类型 */
    @ApiOperation(value = "更新一个维度类型")
    @PutMapping(value = "/dimensionType",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateDimensionType(@Valid @RequestBody EnergyDimensionType newDimension) {
        if(newDimension == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newDimension.getDimensionTypeId()== null) {
            return responseOK(new ResultObject<String>("id is Null",0));
        } else if(newDimension.getIsUsed() == null) {
            return responseOK(new ResultObject<String>("isUsed is Null",0));
        } else {
            ResultObject<String> resultObject = dimensionTypeService.updateDimensionType(newDimension);
            return responseOK(resultObject);
        }
    }

    /** 查询所有维度类型 */
    @ApiOperation(value = "查询所有维度类型")
    @GetMapping(value = "/dimensionTypes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDimensionTypes() {
        ResultObject<List<EnergyDimensionType>> resultObject = dimensionTypeService.getDimensionTypes(-1);
        return responseOK(resultObject);
    }

    /** 查询所有在用的维度类型 */
    @ApiOperation(value = "查询所有在用的维度类型")
    @GetMapping(value = "/dimensionTypes/used", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getUsedDimensionTypes() {
        ResultObject<List<EnergyDimensionType>> resultObject = dimensionTypeService.getUsedDimensionTypes();
        return responseOK(resultObject);
    }

    /** 根据dimensionTypeId获取一棵能耗多维度自定义树; 传入-1则返回默认层级树 */
    @ApiOperation(value = "根据dimensionTypeId获取一棵能耗多维度自定义树; 传入-1则返回默认层级树(包含设备)")
    @GetMapping(value = "/energystructuretree",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceStructures(@RequestParam Integer dimensionTypeId) {
        if(dimensionTypeId == null) {
            return responseBadRequest(new ResultObject<Object>("dimensionTypeId is Null",0));
        } else if(dimensionTypeId.equals(-1)) {
            ResourceStructureTreeDTO defaultTree = energyStructureService.getDefaultTree();
            return responseOK(new ResultObject<Object>(defaultTree));
        } else {
            EnergyResourceStructure energyStructureTree = energyStructureService.getEnergyStructureTree(dimensionTypeId);
            return responseOK(new ResultObject<Object>(energyStructureTree));
        }
    }

    private ResponseEntity<ResponseResult> responseOK(ResultObject resultObject) {
        ResponseResult result = new ResponseResult();
        result.setState(true);
        result.setData(resultObject);
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(String.valueOf(ErrorCode.NORMAL.value()));
        return new ResponseEntity(result, HttpStatus.OK);
    }
    private ResponseEntity<ResponseResult> responseBadRequest(ResultObject resultObject) {

        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setData(resultObject.getData());
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(resultObject.getErrcode().toString());
        result.setErrMsg(resultObject.getErrmsg());
        return new ResponseEntity(result, HttpStatus.BAD_REQUEST);
    }

}
