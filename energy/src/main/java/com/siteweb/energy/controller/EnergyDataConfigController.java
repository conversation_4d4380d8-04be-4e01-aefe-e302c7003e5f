package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.entity.EnergyDataEntry;
import com.siteweb.energy.entity.EnergyDataItem;
import com.siteweb.energy.entity.EnergyDataItemTimeliness;
import com.siteweb.energy.service.EnergyDataConfigItemService;
import com.siteweb.energy.service.EnergyDataConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;

@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyDataConfigController",tags = "能耗字典配置后台相关接口")
public class EnergyDataConfigController {
    private final Logger log = LoggerFactory.getLogger(EnergyDataConfigController.class);

    @Autowired
    private EnergyDataConfigService energyDataConfigService;

    @Autowired
    private EnergyDataConfigItemService energyDataConfigItemService;

    @ApiOperation(value = "查询所有DataEntry集合")
    @GetMapping(value = "/energydataconfig",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllEnergyDataEntry(){
        return ResponseHelper.successful(energyDataConfigService.getAllEnergyDataEntrys());
    }

    @ApiOperation(value = "通过entryId得到其对应的DataItem集合")
    @GetMapping(value = "/energydataconfigitem",params = "entryId",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllEnergyDataItem(@RequestParam Integer entryId){
        return ResponseHelper.successful(energyDataConfigItemService.getAllByEntryId(entryId));
    }

    @ApiOperation(value = "通过entryId和ItemId得到其对应的DataItem")
    @GetMapping(value = "/energydataitem",params = {"entryId", "itemId"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyDataItem(@RequestParam Integer entryId, @RequestParam Integer itemId){
        return ResponseHelper.successful(energyDataConfigItemService.getDataItemById(entryId, itemId));
    }

    @ApiOperation(value = "新增一个DataEntry对象")
    @PostMapping(value = "/energydataconfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEnergyDataEntry(@Valid @RequestBody EnergyDataEntry energyDataEntry) throws URISyntaxException {
        if(energyDataEntry.getEntryId()!=null){
            return ResponseHelper.successful(null);
        }
        EnergyDataEntry result = energyDataConfigService.createEnergyDataEntry(energyDataEntry);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "根据主键删除一个DataEntry对象")
    @DeleteMapping(value = "/energydataconfig/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyDataEntry (@PathVariable Integer id){
        energyDataConfigService.deleteEnergyDataEntry(id);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "编辑DataEntry对象")
    @PutMapping(value = "/energydataconfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEnergyDataEntry(@Valid @RequestBody EnergyDataEntry energyDataEntry) throws URISyntaxException {
        if(energyDataEntry.getEntryId() == null){
            return createEnergyDataEntry(energyDataEntry);
        }
        EnergyDataEntry result = energyDataConfigService.updateEnergyDataEntry(energyDataEntry);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "新增一个DataItem对象")
    @PostMapping(value = "/energydataconfigitem", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEnergyDataItem(@Valid @RequestBody EnergyDataItem energyDataItem) throws URISyntaxException{
        if(energyDataItem.getEntryItemId()!=null){
            return ResponseHelper.successful(null);
        }
        EnergyDataItem result = energyDataConfigItemService.createEnergyDataItem(energyDataItem);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "根据主键删除DataItem对象")
    @DeleteMapping(value = "/energydataconfigitem/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyDataItem (@PathVariable Integer id){
        energyDataConfigItemService.deleteEntryItemId(id);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "根据主键编辑DataItem对象")
    @PutMapping(value = "/energydataconfigitem", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEnergyDataItem(@Valid @RequestBody EnergyDataItem energyDataItem) throws URISyntaxException {
        if(energyDataItem.getEntryItemId() == null){
            return ResponseHelper.successful(createEnergyDataItem(energyDataItem));
        }
        EnergyDataItem result = energyDataConfigItemService.updateEnergyDataItem(energyDataItem);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "新增一个DataItemTimeliness对象")
    @PostMapping(value = "/energydatatimeliness", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEnergyDataItemTimeliness(@Valid @RequestBody EnergyDataItemTimeliness energyDataItemTimeliness) throws URISyntaxException{
        Integer result = energyDataConfigItemService.createEnergyDataItemTimeliness(energyDataItemTimeliness);
        if (result == -2){
            return ResponseHelper.failed("-2", "time is not unique", HttpStatus.OK);
        }
        return ResponseHelper.successful(result);
    }


    @ApiOperation(value = "通过entryId得到其对应的DataItem集合(包含时效性)")
    @GetMapping(value = "/energydatatimeliness",params = "entryId",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllEnergyDataItemByTimeliness(@RequestParam Integer entryId){
        return ResponseHelper.successful(energyDataConfigItemService.getAllByEntryIdTimeliness(entryId));
    }

    @ApiOperation(value = "通过EntryItemId获取时效性列表")
    @GetMapping(value = "/energydatatimeliness",params = "entryItemId",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDataItemTimelinessByEntryItemId(@RequestParam Integer entryItemId){
        return ResponseHelper.successful(energyDataConfigItemService.getDataItemTimelinessByEntryItemId(entryItemId));
    }

    @ApiOperation(value = "根据主键删除dataitemtimeliness对象")
    @DeleteMapping(value = "/energydatatimeliness/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyDataTimeliness (@PathVariable Integer id){
        energyDataConfigItemService.deleteEnergyDataTimeliness(id);
        return ResponseHelper.successful();
    }


    @ApiOperation(value = "根据主键编辑dataitemtimeliness对象")
    @PutMapping(value = "/energydatatimeliness", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEnergyDataTimeliness(@Valid @RequestBody EnergyDataItemTimeliness energyDataItemTimeliness) throws URISyntaxException {
        EnergyDataItemTimeliness result = energyDataConfigItemService.updateEnergyDataTimeliness(energyDataItemTimeliness);
        return ResponseHelper.successful(result);
    }

}
