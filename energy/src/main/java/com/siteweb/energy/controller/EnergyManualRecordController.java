package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.entity.EnergyManualRecord;
import com.siteweb.energy.service.EnergyBaidoReportService;
import com.siteweb.energy.service.EnergyManualRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/energy/energyapi/manualrecord")
@Api(value = "EnergyManualRecordController",tags = "能管手动抄表相关接口")
public class EnergyManualRecordController {

    @Autowired
    private EnergyManualRecordService energyManualRecordService;
    @Autowired
    private EnergyBaidoReportService energyBaidoReportService;

    @ApiOperation(value = "根据层级节点获取总量和分项指标")
    @GetMapping(value = "/getcomplexindex",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId"})
    public ResponseEntity<ResponseResult> getComplexIndex(@RequestParam Integer objectId){
        return ResponseHelper.successful(energyManualRecordService.getComplexIndex(objectId));
    }

    @ApiOperation(value = "根据指标Id获取抄表记录列表")
    @GetMapping(value = "/getmanualrecord",produces = MediaType.APPLICATION_JSON_VALUE, params = {"complexIndexId"})
    public ResponseEntity<ResponseResult> getManualRecord(@RequestParam Integer complexIndexId){
        return ResponseHelper.successful(energyManualRecordService.getManualRecord(complexIndexId));
    }

    @ApiOperation(value = "新增一条抄表记录")
    @PostMapping(value = "/insertmanualrecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> insertManualRecord(@RequestBody EnergyManualRecord energyManualRecord){
        int res = energyManualRecordService.insertManualRecord(energyManualRecord);
        if (res == 0)
            return ResponseHelper.failed("此抄表时间的记录已存在,请勿反复抄表");
        return ResponseHelper.successful(res);
    }

    @ApiOperation(value = "BaidoBaidoBaidoBaido")
    @GetMapping(value = "/doSchedule",produces = MediaType.APPLICATION_JSON_VALUE, params = {"complexIndexId"})
    public ResponseEntity<ResponseResult> doSchedule(@RequestParam Integer complexIndexId){
        energyBaidoReportService.doSchedule();
        return ResponseHelper.successful();
    }
}
