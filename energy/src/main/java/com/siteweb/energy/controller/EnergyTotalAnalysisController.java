package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;

import com.siteweb.energy.dto.TotalComplexIndexDTO;
import com.siteweb.energy.dto.UseEnergyComparedParamsDTO;
import com.siteweb.energy.service.EnergyTotalAnalysisService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/api/energy/total")
@Api(value = "EnergyTotalAnalysisController", tags = "总量分析和分项分析相关接口")
@Component
public class EnergyTotalAnalysisController {

    @Autowired
    private EnergyTotalAnalysisService energyTotalAnalysisService;

    @ApiOperation(value = "综合累计指标(不传objectId,objectTypeId默认根节点总量分析)")
    @ApiImplicitParams({@ApiImplicitParam(name = "startTime", value = "开始时间", type = "String"), @ApiImplicitParam(name = "endTime", value = "结束时间", type = "String")})
    @GetMapping(value = "/complexIndex", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findTotalComplexIndex(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                @ApiParam(name = "objectId", value = "objectId") Integer objectId,
                                                                @ApiParam(name = "objectTypeId", value = "objectTypeId") Integer objectTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findTotalComplexIndex(startTime, endTime, objectId));
    }

    @ApiOperation(value = "能源对比分析(不传objectId,objectTypeId默认根节点总量分析)")
    @GetMapping(value = "/comparative", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findComparativeAnalysis(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                  @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                  @ApiParam(name = "objectId", value = "objectId") Integer objectId,
                                                                  @ApiParam(name = "objectTypeId", value = "objectTypeId") Integer objectTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findComparativeAnalysis(startTime, endTime, objectId, null,null));
    }

    @ApiOperation(value = "能源占比分析(不传objectId,objectTypeId默认根节点总量分析)")
    @GetMapping(value = "/percentage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findPercentageAnalysis(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                 @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                 @ApiParam(name = "objectId", value = "objectId") Integer objectId,
                                                                 @ApiParam(name = "objectTypeId", value = "objectTypeId") Integer objectTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findPercentageAnalysis(startTime, endTime, objectId));
    }

    @ApiOperation(value = "能源分类趋势分析(不传objectId,objectTypeId默认根节点总量分析)")
    @GetMapping(value = "/trend", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findTrendAnalysis(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                            @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                            @ApiParam(name = "objectId", value = "objectId") Integer objectId,
                                                            @ApiParam(name = "objectTypeId", value = "objectTypeId") Integer objectTypeId,
                                                            @ApiParam(name = "isStandardCoal", value = "是否转标煤") Boolean isStandardCoal) {
        return ResponseHelper.successful(energyTotalAnalysisService.findTrendAnalysis(startTime, endTime, objectId, isStandardCoal));
    }

    @ApiOperation(value = "下级用能占比")
    @GetMapping(value = "/childNodePercentage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findChildNodePercentage(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                  @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                  @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                                  @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findChildNodePercentage(startTime, endTime, objectId));
    }

    @ApiOperation(value = "预警排名")
    @GetMapping(value = "/prealarmRank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findPrealarmRank(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                           @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                           @ApiParam(name = "preAlarmSeverity", value = "preAlarmSeverity") Integer preAlarmSeverity) {
        return ResponseHelper.successful(energyTotalAnalysisService.findPrealarmRank(startTime, endTime));
    }

    @ApiOperation(value = "能耗排名-综合对比分析")
    @GetMapping(value = "/energyRankComplex", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findEnergyRankComplex(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                                @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId,
                                                                @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findEnergyRankComplex(startTime, endTime, objectId, businessTypeId));

    }

    @ApiOperation(value = "能耗排名-四个排名,返回数组按照总能、人均、面积均、节能率顺序")
    @GetMapping(value = "/energyRankList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findEnergyRankList(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                             @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                             @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                             @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId,
                                                             @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findEnergyRankList(startTime, endTime, objectId, businessTypeId));

    }

    @ApiOperation(value = "用能对比-水电煤气油使用量计划量和同比环比")
    @PostMapping(value = "/useEnergyComparedOption", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyComparedOption(@RequestBody UseEnergyComparedParamsDTO useEnergyComparedParamsDTO) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyComparedOption(useEnergyComparedParamsDTO));

    }

    @ApiOperation(value = "用能对比-分类能耗占比分析")
    @PostMapping(value = "/useEnergyPercentage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyPercentage(@RequestBody UseEnergyComparedParamsDTO useEnergyComparedParamsDTO) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyPercentage(useEnergyComparedParamsDTO));

    }

    @ApiOperation(value = "用能对比-占比分析模板对象")
    @PostMapping(value = "/useEnergyPercentageTemp", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyPercentageTemp(@RequestBody UseEnergyComparedParamsDTO useEnergyComparedParamsDTO) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyPercentageTemp(useEnergyComparedParamsDTO));

    }

    @ApiOperation(value = "用能对比-能耗排名三个柱状图数据")
    @PostMapping(value = "/useEnergyRank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyRank(@RequestBody UseEnergyComparedParamsDTO useEnergyComparedParamsDTO) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyRank(useEnergyComparedParamsDTO));

    }

    @ApiOperation(value = "用能对比-三个趋势图")
    @PostMapping(value = "/useEnergyTrend", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyTrend(@RequestBody UseEnergyComparedParamsDTO useEnergyComparedParamsDTO) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyTrend(useEnergyComparedParamsDTO));

    }

    @ApiOperation(value = "节能分析-计划、实际、节能")
    @GetMapping(value = "/savingEnergyData", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSavingEnergyData(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                               @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                               @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                               @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findSavingEnergyData(startTime, endTime, objectId, businessTypeId));

    }

    @ApiOperation(value = "节能分析-节能达成情况分析")
    @GetMapping(value = "/savingEnergyList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSavingEnergyList(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                               @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                               @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                               @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findSavingEnergyList(startTime, endTime, objectId, businessTypeId));

    }


    @ApiOperation(value = "需量分析-需量趋势")
    @GetMapping(value = "/demandTrend", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findDemandTrend(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                          @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                          @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                          @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findDemandTrend(startTime, endTime, objectId, businessTypeId));

    }

    @ApiOperation(value = "用能&碳排放统计（组态）")
    @GetMapping(value = "/useEnergyAndCarbonTotal", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyAndCarbonTotal(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                      @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                      @ApiParam(name = "objectId", value = "objectId") Integer objectId,
                                                                      @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId,
                                                                      @ApiParam(name = "userId", value = "用户id", required = true) Integer userId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyAndCarbonTotal(startTime, endTime, objectId, businessTypeId, userId));

    }


    @ApiOperation(value = "用能趋势分析（组态）")
    @GetMapping(value = "/useElectricityTrendAnalysis", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseElectricityTrendAnalysis(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                          @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                          @ApiParam(name = "objectId", value = "objectId") Integer objectId,
                                                                          @ApiParam(name = "businessTypeId", value = "能源类型id", required = true) Integer businessTypeId,
                                                                          @ApiParam(name = "timeType", value = "能源类型id", required = true) String timeType,
                                                                          @ApiParam(name = "userId", value = "用户id", required = true) Integer userId) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseElectricityTrendAnalysis(startTime, endTime, objectId, businessTypeId,timeType,userId));

    }

}
