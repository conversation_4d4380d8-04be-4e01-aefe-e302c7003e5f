package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;

import com.siteweb.energy.dto.TotalComplexIndexDTO;
import com.siteweb.energy.service.EnergyConsumeAnalysisService;
import com.siteweb.energy.service.EnergyTotalAnalysisService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api/energy/consume")
@Api(value = "EnergyConsumeAnalysisController", tags = "需量分析相关接口")
@Component
public class EnergyConsumeAnalysisController {

    @Autowired
    private EnergyConsumeAnalysisService energyConsumeAnalysisService;

    @ApiOperation(value = "需量分析-预警排名")
    @GetMapping(value = "/prealarmRank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findPrealarmRank(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                           @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                           @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                           @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId,
                                                           @ApiParam(name = "businessTypeId", value = "能源类型", required = true) Integer businessTypeId,
                                                           @ApiParam(name = "isConsumeAlarm", value = "是否能源消耗预警1是0不是", required = true) Integer isConsumeAlarm) {
        return ResponseHelper.successful(energyConsumeAnalysisService.findPreAlarmRank(startTime, endTime,objectId,objectTypeId,businessTypeId,isConsumeAlarm));
    }

    @ApiOperation(value = "需量分析-预警列表")
    @GetMapping(value = "/prealarmList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findPrealarmList(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                           @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                           @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                           @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId,
                                                           @ApiParam(name = "businessTypeId", value = "能源类型", required = true) Integer businessTypeId,
                                                           @ApiParam(name = "isConsumeAlarm", value = "是否能源消耗预警1是0不是", required = true) Integer isConsumeAlarm){
        return ResponseHelper.successful(energyConsumeAnalysisService.findPreAlarmList(startTime, endTime,objectId,objectTypeId,businessTypeId,isConsumeAlarm));
    }

    @ApiOperation(value = "需量分析-子节点需量计划量列表")
    @GetMapping(value = "/childconsume", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findChildConsumeTrend(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                            @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                            @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                            @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId,
                                                            @ApiParam(name = "businessTypeId", value = "能源类型", required = true) Integer businessTypeId) {
        return ResponseHelper.successful(energyConsumeAnalysisService.findChildConsumeTrend(startTime, endTime, objectId, objectTypeId,businessTypeId));
    }

    @ApiOperation(value = "峰平谷分析-费控时段及分项")
    @GetMapping(value = "/fpgdetail", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findFpgValue(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                                @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId) {
        return ResponseHelper.successful(energyConsumeAnalysisService.findFpgValue(startTime, endTime, objectId, objectTypeId));
    }

    @ApiOperation(value = "峰平谷分析-所有子节点的峰平谷分析")
    @GetMapping(value = "/childfpgdetail", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findChildFpgValue(@ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                       @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                       @ApiParam(name = "objectId", value = "objectId", required = true) Integer objectId,
                                                       @ApiParam(name = "objectTypeId", value = "objectTypeId", required = true) Integer objectTypeId) {
        return ResponseHelper.successful(energyConsumeAnalysisService.findChildFpgValue(startTime, endTime, objectId, objectTypeId));
    }
}
