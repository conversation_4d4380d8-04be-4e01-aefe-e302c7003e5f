package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.energy.dto.EnergyManagementInfo;
import com.siteweb.energy.dto.EnergyManagementMapInfo;
import com.siteweb.energy.dto.EnergyManagementStructure;
import com.siteweb.energy.entity.EnergyManagement;
import com.siteweb.energy.entity.EnergyManagementMap;
import com.siteweb.energy.service.EnergyManagementMapService;
import com.siteweb.energy.service.EnergyManagementService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.service.ResourceStructureTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.siteweb.common.util.StringUtils.getIntegerListByString;

/**
 * 节能措施功能 相关接口
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyManagementController",tags = "节能措施功能相关接口")
public class EnergyManagementController {
    private final Logger log = LoggerFactory.getLogger(EnergyManagementController.class);
    private static final String ENTITY_NAME = "EnergyManagement";

    @Autowired
    private EnergyManagementService energyManagementService;

    @Autowired
    private EnergyManagementMapService energyManagementMapService;

    @Autowired
    private ResourceStructureTypeService objectTypeService;

    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    private ResourceStructureService resourceStructureService;

    /**
     * 1. 获取所有節能措施 及 采用站點數量和標杆站點數量
     */
    @ApiOperation(value = "获取所有节能措施列表")
    @GetMapping(value = "/energymanagementallinfo",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyManagement() throws URISyntaxException {
        List<EnergyManagementInfo> result  = new ArrayList<>();
        EnergyManagementInfo info = null;
        List<EnergyManagement> allEnergyManagement = energyManagementService.findAll();
        List<EnergyManagementMap> allEnergyManagementMap = new ArrayList<>();
        for ( EnergyManagement oneNode :allEnergyManagement ) {
            allEnergyManagementMap = energyManagementMapService.findByEnergyId(oneNode.getEnergyId());

            info = new EnergyManagementInfo();
            info.getFromEnergyManagement(oneNode);
            info.setStructureCount(allEnergyManagementMap.size());
            info.setStructureIsMarkCount(allEnergyManagementMap.stream().filter(
                    item->item.getIsMarker().equals(1)).collect(Collectors.toList()).size()
            );
            result.add(info);
        }
        return ResponseHelper.successful(result);
    }

    /**
     * 2. 获取某个節能措施
     */
    @ApiOperation(value = "获取某个节能措施信息")
    @GetMapping(value = "/energymanagementallinfo/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyManagementById(
            @PathVariable Integer id) throws URISyntaxException {
        return ResponseHelper.successful(energyManagementService.findById(id));
    }

    /**
     * 3. 获取某个節能措施的实施站点
     */
    @ApiOperation(value = "获取某个節能措施的实施站点")
    @GetMapping(value = "/energymanagementstructure/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyManagementMap(
            @PathVariable Integer id) throws URISyntaxException {
        String result = "";
        List<EnergyManagementMap> lstm = energyManagementMapService.findByEnergyId(id);
        if (lstm == null) return ResponseHelper.successful(result);
        return ResponseHelper.successful(StringUtils.join(lstm.stream().map(EnergyManagementMap::getResourceStructureId).collect(Collectors.toList()),','));
    }

    /**
     * 4. 获取制定措施的站点（標杆站）
     */
    @ApiOperation(value = "获取某个節能措施的站点（標杆站）")
    @GetMapping(value = "/energymanagementstructuremarker/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyManagementMapMarker(
            @PathVariable Integer id) throws URISyntaxException {
        String result = "";
        List<EnergyManagementMap> lstm = energyManagementMapService.findByEnergyId(id).stream().filter(
                item->item.getIsMarker().equals(1)).collect(Collectors.toList());
        if (lstm == null) return ResponseHelper.successful(result);
        return ResponseHelper.successful(
                StringUtils.join(lstm.stream().map(EnergyManagementMap::getResourceStructureId).collect(Collectors.toList()),','));
    }

    /**
     * 4.1 获取某个節能措施的实施站点，标杆站
     */
    @ApiOperation(value = "获取某个節能措施的实施站点")
    @GetMapping(value = "/energymanagementstructurelist/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> GetEnergyManagementMapList(
            @PathVariable Integer id) throws URISyntaxException {
        List<EnergyManagementMapInfo> lstm = energyManagementMapService.GetEnergyManagementMapList(id);
        return ResponseHelper.successful(lstm);
    }

    /**
     * 5. 新增节能措施信息
     * */
    @ApiOperation(value = "新增节能措施信息")
    @PostMapping(value="/energymanagement",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEnergyManagement(
            @RequestBody EnergyManagement energyInfo) throws URISyntaxException{
        log.debug("REST request to save EnergyManagement : {}", energyInfo);
        if(energyInfo.getEnergyId() != null){
            return ResponseHelper.successful(null);
        }
        EnergyManagement result = energyManagementService.saveInfo(energyInfo);
        return ResponseHelper.successful(result);
    }

    /**
     * 6. 修改节能措施信息
     * */
    @ApiOperation(value = "修改节能措施信息")
    @PutMapping(value="/energymanagement",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> UpdateEnergyManagement(
            @RequestBody EnergyManagement energyInfo) throws URISyntaxException{

        log.debug("REST request to save EnergyManagement : {}", energyInfo);
        EnergyManagement result;
        if(energyInfo.getEnergyId() == null){
            return ResponseHelper.successful(null);
        }
        result = energyManagementService.updateInfo(energyInfo);
        return ResponseHelper.successful(result);
    }

    /**
     * 7. 修改节能措施站点
     */
    @ApiOperation(value = "修改节能措施站点")
    @PutMapping(value = "/energymanagementstructure")
    public ResponseEntity<ResponseResult> updateEnergyManagement(
            @RequestBody EnergyManagementStructure energyStructure) throws URISyntaxException {
        return ResponseHelper.successful(energyManagementMapService.saveEnergyStructure(energyStructure));
    }

    /**
     * 8. 设置节能措施措施站点（標杆站）
     */
    @ApiOperation(value = "设置节能措施措施站点（標杆站）")
    @PutMapping(value = "/energymanagementstructuremarker")
    public ResponseEntity<ResponseResult> updateEnergyManagementmarker(
            @RequestBody EnergyManagementStructure energyStructure) throws URISyntaxException {
        return ResponseHelper.successful(energyManagementMapService.saveEnergyStructureMarker(energyStructure,1));
    }

    /**
     * 8.1 取消节能措施措施站点（標杆站）
     */
    @ApiOperation(value = "取消节能措施措施站点（標杆站）")
    @PutMapping(value = "/energymanagementstructureremovemarker")
    public ResponseEntity<ResponseResult> updateEnergyManagementremovemarker(
            @RequestBody EnergyManagementStructure energyStructure) throws URISyntaxException {
        return ResponseHelper.successful(energyManagementMapService.saveEnergyStructureMarker(energyStructure,0));
    }

    /**
     * 9. 删除节能措施
     */
    @ApiOperation(value = "删除节能措施")
    @DeleteMapping(value = "/energymanagement/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyManagement(@PathVariable Integer id) {
        EnergyManagement energyManagement = energyManagementService.findById(id);
        if (energyManagement == null) {
            return ResponseHelper.successful(false);
        }
        energyManagementService.deleteById(id);
        return ResponseHelper.successful(true);
    }

    /**
     * 10. 更加层级类型筛选层级
     */
    @GetMapping(value = "/resourcestructures", params = {"objectTypeIds"})
    public ResponseEntity<ResponseResult> getResourceStructureByObjectTypeId(
            @RequestParam("objectTypeIds" ) String objectTypeIds) throws URISyntaxException {
        List<Integer> lstRsId  = getIntegerListByString(objectTypeIds);
        return ResponseHelper.successful(resourceStructureManager.getAll().stream().filter(i->lstRsId.contains(i.getStructureTypeId())).collect(Collectors.toList()));
    }

    /**
     * 11. 查询层级树 节点类型
     */
    @GetMapping(value = "/resourcestructureobjecttypes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceStructureObjectTypes() throws URISyntaxException {
        return ResponseHelper.successful(objectTypeService.findUsedStructureObjectType());
    }

    /**
     * 11. 根据sceneId查询层级树节点类型
     */

    @GetMapping(value = "/resourcestructureobjecttypes", params = {"sceneId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceStructureObjectTypesByScene(
            @RequestParam("sceneId" ) Integer sceneId
    ) throws URISyntaxException {
        return ResponseHelper.successful(objectTypeService.findResourceStructureObjectTypes(sceneId));
    }

    /**
     * 12. 查询未实施此措施的节点
     */
    @GetMapping(value = "/energymanagementstructurenot",params = {"energyId","personId"})
    public ResponseEntity<ResponseResult> GetResourceStructureNotIn(
            @RequestParam Integer energyId,@RequestParam Integer personId) throws URISyntaxException {
        List<ResourceStructure> result = new ArrayList<>();
        List<EnergyManagementMapInfo> lstm = energyManagementMapService.GetEnergyManagementMapList(energyId);

        List<Integer> lstRsId = new ArrayList<>();
        lstm.forEach(item->lstRsId.add(item.getResourceStructureId()));

        // 获取未实施的所有站点
        result = resourceStructureManager.getAll().stream().filter(i->!lstRsId.contains(i.getResourceStructureId())).collect(Collectors.toList());
        // 有权限的站点
        List<ResourceStructure> roleResourceStructures =  resourceStructureService.findResourceStructureByUserId(personId);
        // 剔除无权限的站点
        result.removeIf(rs ->
                roleResourceStructures.stream().filter(i->i.getResourceStructureId().equals(rs.getResourceStructureId())).collect(Collectors.toList()).size() == 0);

        return ResponseHelper.successful(result.stream().sorted(Comparator.comparing(ResourceStructure::getLevelOfPath)).collect(Collectors.toList()));
    }
}
