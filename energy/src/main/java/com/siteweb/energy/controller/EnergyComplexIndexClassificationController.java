package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.entity.EnergyComplexIndexClassificationMap;
import com.siteweb.energy.service.EnergyComplexIndexClassificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/api/energy/complexindexclassification")
@Api(value = "EnergyComplexIndexClassificationController",tags = "能耗指标分类相关接口")
public class EnergyComplexIndexClassificationController {

    @Autowired
    private EnergyComplexIndexClassificationService energyComplexIndexClassificationService;
    @ApiOperation(value = "获取对应层级已配置指标列表")
    @GetMapping(value = "/complexindexs",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId"})
    public ResponseEntity<ResponseResult> findComplexIndexByObjectId(String objectId, String objectTypeId){
        return ResponseHelper.successful(energyComplexIndexClassificationService.findComplexIndexByObjectId(objectId,objectTypeId));
    }
    @ApiOperation(value = "删除对应层级指标分类映射关系")
    @DeleteMapping(value = "/complexindexs/{complexIndexId}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteComplexIndexClassificationMapByComplexIndexId(@PathVariable Integer complexIndexId){
        return ResponseHelper.successful(energyComplexIndexClassificationService.deleteComplexIndexClassificationMapByComplexIndexId(complexIndexId));
    }

    @ApiOperation(value = "新增、修改对应层级指标分类映射关系")
    @PostMapping(value = "/complexindexs",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addComplexIndexClassification(@RequestBody EnergyComplexIndexClassificationMap energyComplexIndexClassificationMap){
        return ResponseHelper.successful(energyComplexIndexClassificationService.addComplexIndexClassification(energyComplexIndexClassificationMap));
    }

    @ApiOperation(value = "查询能耗四级分类树")
    @GetMapping(value = "/tree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyClassificationTree(){
        return ResponseHelper.successful(energyComplexIndexClassificationService.getEnergyClassificationTree());
    }
}
