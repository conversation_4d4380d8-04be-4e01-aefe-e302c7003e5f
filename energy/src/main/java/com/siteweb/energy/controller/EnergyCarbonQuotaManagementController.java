package com.siteweb.energy.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyCarbonManageDataParamsDTO;
import com.siteweb.energy.entity.EnergyCarbonManegePara;
import com.siteweb.energy.service.EnergyCarbonQuotaManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyCarbonQuotaManagementController",tags = "碳配额管理相关接口")
public class EnergyCarbonQuotaManagementController {

    @Autowired
    private EnergyCarbonQuotaManagementService energyCarbonQuotaManagementService;

    @Value("${spring.web.locale}")
    private String locale;


    @ApiOperation(value = "获取对应层级已配置参数年份")
    @GetMapping(value = "/carbonQuotaConfiguredYears",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId"})
    public ResponseEntity<ResponseResult> findEnergyConsumeYears(String objectId,String objectTypeId){
        return ResponseHelper.successful(energyCarbonQuotaManagementService.findCarbonQuotaYears(objectId,objectTypeId));
    }


    @ApiOperation(value = "获取对应层级用能配置")
    @GetMapping(value = "/carbonQuotaData",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","year"})
    public ResponseEntity<ResponseResult> findEnergyConsumeData(String objectId,String objectTypeId,Integer year){
        return ResponseHelper.successful(energyCarbonQuotaManagementService.findCarbonQuotaData(objectId,objectTypeId,year));
    }

    @ApiOperation(value = "获取对应层级用能历史配置")
    @GetMapping(value = "/carbonQuotaHistoryData",produces = MediaType.APPLICATION_JSON_VALUE, params = {"objectId", "objectTypeId","year"})
    public ResponseEntity<ResponseResult> findEnergyConsumeHistoryData(String objectId,String objectTypeId,Integer year){
        boolean isChina = locale.toLowerCase().equals("zh_cn");
        List<EnergyCarbonManegePara> res =  energyCarbonQuotaManagementService.findCarbonQuotaHistoryData(objectId,objectTypeId,year);
        if (res.isEmpty()){
            if (isChina){
                return ResponseHelper.failed("当前层级下没有配置对应能源总量指标");
            }else{
                return ResponseHelper.failed("The corresponding total energy indicator is not configured under the current level");
            }
        }else {
            return ResponseHelper.successful(res);
        }
    }

    @ApiOperation(value = "新增对应层级用能配置")
    @PostMapping(value = "/addCarbonQuotaData",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveEnergyConsumeData(@RequestBody EnergyCarbonManageDataParamsDTO energyConsumeDataParamsDTO){
        String result = energyCarbonQuotaManagementService.saveCarbonQuotaData(energyConsumeDataParamsDTO);
        if(result.equals("OK"))
            return  ResponseHelper.successful(result);
        else
            return ResponseHelper.failed(result);
    }

    @ApiOperation(value = "更新对应层级用能配置")
    @PutMapping(value = "/updateCarbonQuotaData",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEnergyConsumeData(@RequestBody EnergyCarbonManageDataParamsDTO energyConsumeDataParamsDTO){
        boolean isChina = locale.toLowerCase().equals("zh_cn");
        String result = energyCarbonQuotaManagementService.updateCarbonQuotaData(energyConsumeDataParamsDTO.getList(),energyConsumeDataParamsDTO.getYearPlanTotalValue());
        if(result.equals("OK"))
            return  ResponseHelper.successful(result);
        else
            return ResponseHelper.failed(result);
    }

    @ApiOperation(value = "删除对应层级用能配置")
    @DeleteMapping(value = "/deleteCarbonQuotaData/{id}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyConsumeData(@PathVariable Integer id){
        return ResponseHelper.successful(energyCarbonQuotaManagementService.deleteCarbonQuotaData(id));
    }

}
