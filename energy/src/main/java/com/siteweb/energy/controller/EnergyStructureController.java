package com.siteweb.energy.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.energy.dto.ResultObject;
import com.siteweb.energy.enumeration.SourceCategory;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.model.StructureInfo;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 能耗多维度-自定义层级 相关接口
 */
@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyStructureController",tags = "多维护树管理相关接口")
public class EnergyStructureController {
    private final Logger log = LoggerFactory.getLogger(EnergyStructureController.class);

    @Autowired
    private EnergyStructureService structureService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;

    /** 新增一个自定义层级节点 */
    @ApiOperation(value = "新增一个自定义层级节点")
    @PostMapping(value = "/energyStructure",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addOneEnergyStructure(@RequestBody StructureInfo newStructure) {
        if(newStructure == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else if(newStructure.getParentNode() == null) {
            return responseOK(new ResultObject<String>("parentNode is Null",0));
        } else if(newStructure.getAddOneEnergyNode() == null) {
            return responseOK(new ResultObject<String>("EnergyNode is Null",0));
        } else if(StringUtils.isBlank(newStructure.getAddOneEnergyNode().getStructureName())) {
            return responseOK(new ResultObject<String>("structureName is blank",0));
        } else {
            newStructure.getAddOneEnergyNode().setSourceCategory(SourceCategory.RESOURCE_STRUCTURE.value());//自定义层级节点归属于类型：SourceCategory.RESOURCE_STRUCTURE
            ResultObject<String> resultObject = structureService.addOneEnergyStructure(newStructure);
            return responseOK(resultObject);
        }
    }

    /** 删除一个节点 */
    @ApiOperation(value = "删除一个节点，级联删除所有子孙节点")
    @DeleteMapping(value = "/energyStructure", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delOneEnergyStructure(@RequestBody EnergyResourceStructure node) {
        if(node == null) {
            return responseOK(new ResultObject<>("Param is Null",0));
        } else if(node.getId() == null) {
            return responseOK(new ResultObject<>("Id is Null",0));
        } else {
            ResultObject<String> resultObject;
            try {
                resultObject = structureService.delOneEnergyStructure(node);
            } catch (Exception ex) {
                resultObject = new ResultObject<>(ex.getMessage(), -1);
                log.error("Delete Fail：", ex);
            }
            return responseOK(resultObject);
        }
    }

    /** 更新一个节点 */
    @ApiOperation(value = "更新一个节点")
    @PutMapping(value = "/energyStructure",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEnergyStructure(@RequestBody EnergyResourceStructure node) {
        if(node == null) {
            return responseBadRequest(new ResultObject<String>("Param is Null",0));
        } else {
            ResultObject<String> resultObject = structureService.updateOneEnergyStructure(node);
            return responseOK(resultObject);
        }
    }

    @ApiOperation(value = "添加已存在的自定义层级节点")
    @PostMapping(value = "/existEnergyStructures",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addExistEnergyStructures(@RequestBody StructureInfo newStructure) {
        log.info("addExistEnergyStructures_1 ");
        if(newStructure == null) {
            return responseOK(new ResultObject<>("Param is Null",0));
        } else if(newStructure.getParentNode() == null) {
            return responseOK(new ResultObject<>("parentNode is Null",0));
        } else if(newStructure.getParentNode().getId() == null) {
            return responseOK(new ResultObject<>("parentNode.Id is Null",0));
        } else if(newStructure.getParentNode().getDimensionTypeId() == null) {
            return responseOK(new ResultObject<>("DimensionTypeId is Null",0));
        } else if(newStructure.getAddStructuresEnergy() == null || newStructure.getAddStructuresEnergy().size() < 1) {
            return responseOK(new ResultObject<>("StructuresEnergy Array is Empty",0));
        } else if(newStructure.getOnlyCopyName() == null) {
            return responseOK(new ResultObject<>("onlyCopyName is Null",0));
        } else if(newStructure.getPreserveTreeStructure() == null) {
            return responseOK(new ResultObject<>("preserveTreeStructure is Null",0));
        }

        try {
            log.info("addExistEnergyStructures_2 ");
            ResultObject<String> resultObject = structureService.addExistEnergyStructures(newStructure);
            log.info("addExistEnergyStructures_3 ");
            return responseOK(resultObject);
        } catch (Exception ex) {
            log.error("Add Fail 1：", ex);
            return responseOK(new ResultObject<>("Add Fail:" + ex.getMessage(),-1));
        }

    }

    @ApiOperation(value = "添加已存在的默认层级节点")
    @PostMapping(value = "/existDefaultStructures",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addExistDefaultStructures(@RequestBody StructureInfo newStructure) {
        log.info("addExistDefaultStructures_1 ");
        if(newStructure == null) {
            return responseOK(new ResultObject<>("Param is Null",0));
        } else if(newStructure.getParentNode() == null) {
            return responseOK(new ResultObject<>("parentNode is Null",0));
        } else if(newStructure.getParentNode().getId() == null) {
            return responseOK(new ResultObject<>("parentNode.Id is Null",0));
        } else if(newStructure.getParentNode().getDimensionTypeId() == null) {
            return responseOK(new ResultObject<>("DimensionTypeId is Null",0));
        } else if(newStructure.getAddStructuresDefault() == null || newStructure.getAddStructuresDefault().size() < 1) {
            return responseOK(new ResultObject<>("StructuresDefault Array is Empty",0));
        } else if(newStructure.getOnlyCopyName() == null) {
            return responseOK(new ResultObject<>("onlyCopyName is Null",0));
        } else if(newStructure.getPreserveTreeStructure() == null) {
            return responseOK(new ResultObject<>("preserveTreeStructure is Null",0));
        }

        try {
            log.info("addExistDefaultStructures_2 ");
            ResultObject<String> resultObject = structureService.addExistDefaultStructures(newStructure);
            log.info("addExistDefaultStructures_3 ");
            return responseOK(resultObject);
        } catch (Exception ex) {
            log.error("Add Fail 2：", ex);
            return responseOK(new ResultObject<>("Operate Fail:" + ex.getMessage(),-1));
        }
    }

    private ResponseEntity<ResponseResult> responseOK(ResultObject resultObject) {
        ResponseResult result = new ResponseResult();
        result.setState(true);
        result.setData(resultObject);
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(String.valueOf(ErrorCode.NORMAL.value()));
        return new ResponseEntity(result, HttpStatus.OK);
    }
    private ResponseEntity<ResponseResult> responseBadRequest(ResultObject resultObject) {

        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setData(resultObject.getData());
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(resultObject.getErrcode().toString());
        result.setErrMsg(resultObject.getErrmsg());
        return new ResponseEntity(result, HttpStatus.BAD_REQUEST);
    }

}
