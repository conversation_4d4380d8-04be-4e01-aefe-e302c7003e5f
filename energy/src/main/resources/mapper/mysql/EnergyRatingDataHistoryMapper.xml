<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyRatingDataHistoryMapper">


    <select id="getLatestPieceOfTimes" resultType="java.lang.Integer">
        select times from energy_ratingdatahistory order by RatingDataId desc LIMIT 1
    </select>

    <insert id="insertToHistory">
        insert into energy_ratingdatahistory(ratingDataId,
        ratingConfigId,sampleTime,intervalSecond,workingCondition,outDryTemp,outWetTemp,inDryTemp,inWetTemp,runningLoad,itPower,totalPower,times, deleteDate)
        SELECT ratingDataId,ratingConfigId,sampleTime,intervalSecond,workingCondition,outDryTemp,outWetTemp,inDryTemp,inWetTemp,runningLoad,itPower,totalPower,#{times} as times,#{deleteDate} as deleteDate
        FROM energy_ratingdata where RatingConfigId = #{ratingConfigId};
    </insert>

    <delete id="clearRatingData">
        delete from energy_ratingdata where RatingConfigId = #{ratingConfigId};
    </delete>
</mapper>