<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyDataConfigItemMapper">


    <select id="getAllByEntryId" resultType="com.siteweb.energy.entity.EnergyDataItem">
        SELECT * FROM Energy_DataItem WHERE EntryId = #{entryId}
    </select>
    <select id="getDataItemById" resultType="com.siteweb.energy.entity.EnergyDataItem">
        SELECT * FROM Energy_DataItem WHERE EntryId = #{entryId} and ItemId = #{itemId}
    </select>
</mapper>