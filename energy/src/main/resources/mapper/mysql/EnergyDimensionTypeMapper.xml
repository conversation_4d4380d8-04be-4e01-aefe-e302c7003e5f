<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyDimensionTypeMapper">

    <select id="findByDimensionTypeId" resultType="com.siteweb.energy.entity.EnergyDimensionType">
        SELECT * FROM Energy_DimensionType WHERE DimensionTypeId = #{dimensionTypeId}
    </select>
    <select id="findByIsUsedIs" resultType="com.siteweb.energy.entity.EnergyDimensionType">
        SELECT * FROM Energy_DimensionType WHERE IsUsed = #{usedState}
    </select>
</mapper>