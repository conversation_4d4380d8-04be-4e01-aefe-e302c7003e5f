<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyTaiBoReportMapper">

    <select id="getAllReportInfo" resultType="com.siteweb.energy.entity.EnergyCustomerTbReport">
        select * from energy_customer_tb_report;
    </select>
    <select id="getAllEquipmentInfoByReportId"
            resultType="com.siteweb.energy.entity.EnergyCustomerTBReportMap">
        select * from energy_customer_tb_reportmap where reportid = #{reportId};
    </select>
    <select id="GetTaiBoReportList" resultType="com.siteweb.energy.entity.EnergyCustomerTBReportRecord">
        select * from energy_customer_tb_reportrecord where createtime &gt;#{startTime} and createtime &lt;#{endTime} ORDER BY id DESC;
    </select>
    <select id="getTaiBoReportById" resultType="com.siteweb.energy.entity.EnergyCustomerTBReportRecord">
        select * from energy_customer_tb_reportrecord where id=#{pId}
    </select>
</mapper>