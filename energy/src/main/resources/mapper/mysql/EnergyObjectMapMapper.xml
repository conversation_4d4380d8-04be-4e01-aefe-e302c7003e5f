<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyObjectMapMapper">

    <sql id="Source_Table_Columns">
        o.*, o.ObjectId as ResourceStructureId, es.AsRootInflowNode,
            case when o.ObjectIdType = ${energyResourceType} then ${dimensionStructureObjecttypeid}
                when o.ObjectIdType = ${resourceStructureType} then rs.StructureTypeId
                when o.ObjectIdType = ${equipmentStructureType} then 7
                when o.ObjectIdType = ${computerRackStructureType} then 9
                when o.ObjectIdType = ${itDeviceStructureType} then 10
              end as ObjectTypeId,
            case when o.ObjectIdType = ${energyResourceType} then es.StructureName
                when o.ObjectIdType = ${resourceStructureType} then rs.ResourceStructureName
                when o.ObjectIdType = ${equipmentStructureType} then eq.EquipmentName
                when o.ObjectIdType = ${computerRackStructureType} then cr.ComputerRackName
                when o.ObjectIdType = ${itDeviceStructureType} then it.ITDeviceName
              end as ResourceStructureName,
        case when o.ObjectIdType = ${energyResourceType} then es.Notes else '' end as StructureNotes,
        case when o.ObjectIdType = ${energyResourceType} then es.SourceCategory else null end as EnergyStructureSourceCategory
    </sql>
    <sql id="Source_Table_From_LeftJoin">
        LEFT JOIN ResourceStructure rs ON o.ObjectIdType=#{resourceStructureType} AND o.ObjectId=rs.ResourceStructureId   <!-- 系统默认层级节点 -->
        LEFT JOIN tbl_equipment eq ON o.ObjectIdType=#{equipmentStructureType} AND o.ObjectId=eq.EquipmentId <!-- 普通设备表 -->
        LEFT JOIN ComputerRack cr ON o.ObjectIdType=#{computerRackStructureType} AND o.ObjectId=cr.ComputerRackId <!-- 机架表 -->
        LEFT JOIN ITDevice it ON o.ObjectIdType=#{itDeviceStructureType} AND o.ObjectId=ITDeviceId <!-- IT设备表 -->
        LEFT JOIN Energy_Structure es ON o.ObjectIdType=#{energyResourceType} AND o.ObjectId=es.StructureId  <!-- 能耗多维度自定义资源节点 -->
    </sql>

    <delete id="deleteByDimensionTypeId">
        DELETE FROM Energy_ObjectMap WHERE DimensionTypeId = #{dimensionTypeId}
    </delete>

    <select id="findAllNodesByDimensionTypeId" resultType="com.siteweb.energy.model.EnergyResourceStructure">
        SELECT
            <include refid="Source_Table_Columns"/>
        FROM
            (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{dimensionTypeId}) o
            <include refid="Source_Table_From_LeftJoin"/>
        ORDER BY es.AsRootInflowNode DESC, o.Id ASC
    </select>
    <select id="getAllPrivateStructureIds" resultType="java.lang.Integer">
        SELECT o1.ObjectId FROM
                    (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{id} AND ObjectIdType =${energyStructureType} ) o1
          LEFT JOIN (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId!=#{id} AND ObjectIdType =${energyStructureType} ) o2
          ON o1.ObjectId=o2.ObjectId
          WHERE o2.Id IS NULL
    </select>
    <select id="findByDimensionTypeIdAndObjectIdAndObjectIdType" resultType="com.siteweb.energy.entity.EnergyObjectMap">
        SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{dimensionTypeId} AND ObjectId=#{objectId} AND ObjectIdType=#{objectIdType}
    </select>
    <select id="findAllLevel1ChildNodes" resultType="com.siteweb.energy.entity.EnergyObjectMap">
        SELECT * FROM Energy_ObjectMap WHERE ParentObjectId=#{objectId} AND ParentObjectIdType=#{objectIdType} AND DimensionTypeId=#{dimensionTypeId}
    </select>
    <select id="getCountByOtherTreeUsed" resultType="java.lang.Integer">
        SELECT count(Id) FROM Energy_ObjectMap WHERE ObjectId=#{objectId} AND ObjectIdType=#{objectIdType} AND Id!=#{id}
    </select>

    <select id="findSameNameNodeUnderSameParentObjectId" resultType="com.siteweb.energy.model.EnergyResourceStructure">
        SELECT * FROM (
            SELECT
                <include refid="Source_Table_Columns"/>
            FROM
                (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{dimensionTypeId}) o
                <include refid="Source_Table_From_LeftJoin"/>
        ) tbl
        WHERE tbl.ParentObjectId=#{parentObjectId} AND tbl.ParentObjectIdType=#{parentObjectIdType} AND tbl.ResourceStructureName=#{nodeName}
    </select>

    <select id="findAllStrutureNodesByDimensionTypeId"
            resultType="com.siteweb.energy.model.EnergyResourceStructure"> <!-- 该方法暂时没用到 -->
        SELECT o.*, o.ObjectId as ResourceStructureId,
            case when o.ObjectIdType = ${energyResourceType} then ${dimensionStructureObjecttypeid} else rs.StructureTypeId end as ObjectTypeId,
            case when o.ObjectIdType = ${energyResourceType} then o.stNotes else '' end as StructureNotes,
            case when o.ObjectIdType = ${energyResourceType} then o.StructureName else rs.ResourceStructureName end as ResourceStructureName,
            case when o.ObjectIdType = ${energyResourceType} then o.SourceCategory else null end as EnergyStructureSourceCategory
        FROM
            (SELECT inner_om.*,inner_st.StructureId,inner_st.StructureName,inner_st.SourceCategory,inner_st.Notes as stNotes, inner_st.AsRootInflowNode
               FROM Energy_ObjectMap inner_om LEFT JOIN Energy_Structure inner_st
                ON inner_om.ObjectIdType=${energyResourceType} AND inner_om.ObjectId=inner_st.StructureId
                WHERE DimensionTypeId=#{dimensionTypeId} AND (inner_om.ObjectIdType=#{resourceStructureType}
                     OR (inner_om.ObjectIdType=#{energyResourceType} AND inner_st.SourceCategory=#{resourceStructureType}) )
            ) o <!-- 仅拿到该树的层级类节点(默认层级和自定义层级)，不包含设备 energyStructureSourceCategory-->
            LEFT JOIN ResourceStructure rs ON o.ObjectIdType=#{resourceStructureType} AND o.ObjectId=rs.ResourceStructureId
    </select>

    <select id="findObjectMapWithSourceCategoryById_Old"
            resultType="com.siteweb.energy.model.EnergyResourceStructure">
        SELECT o.*,o.ObjectId as resourceStructureId, es.StructureId, es.StructureName as resourceStructureName, es.SourceCategory as energyStructureSourceCategory, es.Notes as StructureNotes, es.AsRootInflowNode
        FROM
        (SELECT * FROM Energy_ObjectMap WHERE Id=#{primaryId}) o
        LEFT JOIN Energy_Structure es ON o.ObjectIdType=#{energyResourceType} AND o.ObjectId=es.StructureId  <!-- 能耗多维度自定义资源节点 -->
    </select>

    <select id="findObjectMapWithSourceCategoryById"
                         resultType="com.siteweb.energy.model.EnergyResourceStructure">
        SELECT
            <include refid="Source_Table_Columns"/>
        FROM
            (SELECT * FROM Energy_ObjectMap WHERE Id=#{primaryId}) o
            <include refid="Source_Table_From_LeftJoin"/>
    </select>
    <select id="findRootNodeByDimensionTypeId" resultType="com.siteweb.energy.entity.EnergyObjectMap">
        SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{dimensionTypeId} AND ParentObjectId=0
    </select>
    <select id="getAllPrivateRefStructureIds" resultType="com.siteweb.energy.entity.EnergyObjectMap">
        SELECT o1.* FROM
            (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{id} AND ObjectIdType IN (${equipmentStructureType}, ${computerRackStructureType}, ${itDeviceStructureType}) ) o1
            LEFT JOIN (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId!=#{id} AND ObjectIdType IN (${equipmentStructureType}, ${computerRackStructureType}, ${itDeviceStructureType}) ) o2
            ON o1.ObjectId=o2.ObjectId AND o1.ObjectIdType=o2.ObjectIdType
        WHERE o2.Id IS NULL
    </select>
    <select id="findAllRootInflowNodeByDimensionTypeId" resultType="com.siteweb.energy.model.EnergyResourceStructure">
        SELECT
        <include refid="Source_Table_Columns"/>
        FROM
        (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{dimensionTypeId}) o
        <include refid="Source_Table_From_LeftJoin"/>
        WHERE es.AsRootInflowNode = 1 OR o.ParentObjectId = 0
        ORDER BY o.Id
    </select>

    <select id="findAllRootFatherInflowNodeByDimensionTypeId" resultType="com.siteweb.energy.model.EnergyResourceStructure">
        SELECT
        <include refid="Source_Table_Columns"/>
        FROM
        (SELECT * FROM Energy_ObjectMap WHERE DimensionTypeId=#{dimensionTypeId}) o
        <include refid="Source_Table_From_LeftJoin"/>
        WHERE es.AsRootInflowNode = 1
        ORDER BY o.Id
    </select>
    <select id="findChildObjectById" resultType="com.siteweb.energy.dto.EnergyTaiBoObjectData">
        SELECT ObjectId,rs.ResourceStructureId,rs.ResourceStructureName
        FROM energy_objectmap eo
        left JOIN energy_structure es ON eo.ObjectId = es.StructureId
        left JOIN resourcestructure rs on eo.ObjectId = rs.ResourceStructureId
        WHERE eo.DimensionTypeId =  #{dimensionTypeId} AND eo.ParentObjectId = #{objectId} AND (es.StructureId IS NULL OR es.AsRootInflowNode != 1);
    </select>
    <select id="findChildObjectInResourcestructureById" resultType="com.siteweb.energy.dto.EnergyTaiBoObjectData">
        select ResourceStructureId ObjectId,ResourceStructureName from resourcestructure where ParentResourceStructureId = #{objectId};
    </select>
    <select id="findObjectInfoByObjectId" resultType="com.siteweb.energy.dto.EnergyTaiBoObjectData">
        select ResourceStructureId ObjectId,ResourceStructureName from resourcestructure where ResourceStructureId = #{objectId};
    </select>
    <select id="findObjectInfoInDimensionMap" resultType="com.siteweb.energy.dto.EnergyTaiBoObjectData">
        SELECT ObjectId,rs.ResourceStructureId,rs.ResourceStructureName,es.StructureName,es.AsRootInflowNode
        FROM energy_objectmap eo
        left JOIN energy_structure es ON eo.ObjectId = es.StructureId
        left JOIN resourcestructure rs on eo.ObjectId = rs.ResourceStructureId
        WHERE eo.DimensionTypeId =   #{dimensionTypeId} AND eo.ObjectId =  #{objectId} ;
    </select>
    <select id="findRootNodeInfoByDimensionTypeId" resultType="com.siteweb.energy.dto.EnergyTaiBoObjectData">
        SELECT es.structureId objectId,es.StructureName
        FROM Energy_Structure es
        JOIN Energy_ObjectMap eom ON es.StructureId = eom.ObjectId
        WHERE eom.DimensionTypeId =  #{dimensionTypeId} AND eom.ParentObjectId = 0

        UNION

        SELECT rs.ResourceStructureId objectId,rs.ResourceStructureName
        FROM ResourceStructure rs
        JOIN Energy_ObjectMap eom ON rs.ResourceStructureId = eom.ObjectId
        WHERE eom.DimensionTypeId = #{dimensionTypeId} AND eom.ParentObjectId = 0;
    </select>
    <select id="findRootInflowNodeByDimensionTypeId" resultType="com.siteweb.energy.entity.EnergyObjectMap">
        SELECT aa.* FROM Energy_ObjectMap aa
        inner join energy_structure b on aa.ObjectId=b.StructureId and aa.ObjectIdType=2  and b.ASRootInflowNode = 1
        WHERE aa.DimensionTypeId= #{dimensionTypeId} AND aa.ParentObjectIdType=1;
    </select>

</mapper>