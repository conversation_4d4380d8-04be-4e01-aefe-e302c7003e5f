<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyElecFeeStructureMapMapper">

    <delete id="deleteBySchemeId">
        DELETE FROM Energy_ElecFeeSchemeStructureMap WHERE SchemeId = #{schemeId}
    </delete>
    <select id="getBySchemeId" resultType="com.siteweb.energy.entity.EnergyElecFeeSchemeStructureMap">
        SELECT * FROM Energy_ElecFeeSchemeStructureMap WHERE SchemeId=#{schemeId} ORDER BY MapId
    </select>
</mapper>