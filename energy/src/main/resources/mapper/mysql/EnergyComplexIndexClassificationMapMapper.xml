<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyComplexIndexClassificationMapMapper">
    <update id="updateTable">
        DELETE FROM energy_complexindexclassificationmap
        WHERE complexIndexId > 0
        AND complexIndexId NOT IN(SELECT complexIndexId FROM complexIndex );
    </update>

    <select id="getComplexIndexClassificationInfoById"
            resultType="com.siteweb.energy.dto.ComplexIndexClassificationListDTO">
        SELECT ci.complexIndexId , ci.complexIndexName ,ci.objectId resourceStructureId,emo.objectName classificationName,ecm.classificationId, ct.businessTypeName
        FROM complexIndex ci
        LEFT JOIN energy_complexindexclassificationmap ecm ON ecm.complexIndexid = ci.complexIndexId
        LEFT JOIN energy_multilevelobject emo ON ecm.classificationid = emo.ObjectId
        INNER JOIN complexindexbusinesstype ct ON ct.BusinessTypeId = ci.BusinessTypeId
        WHERE ci.ObjectId = #{objectId} AND ci.ObjectTypeId = #{objectTypeId} AND ci.BusinessTypeId = #{businessTypeId};
    </select>
    <select id="getComplexIndexByResIdAndClassificationId"
            resultType="com.siteweb.energy.entity.EnergyComplexIndexClassificationMap">
        SELECT * FROM energy_complexindexclassificationmap WHERE resourcestructureid IN (${resourceStructureIds}) AND classificationId IN (${classificationIds});
    </select>
</mapper>