<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyElecFeeStepPriceMapper">

    <delete id="deleteBySchemeId">
        DELETE FROM Energy_ElecFeeStepPrice WHERE SchemeId = #{schemeId}
    </delete>
    <delete id="delByCondition">
        delete FROM Energy_ElecFeeStepPrice where StepPriceId = #{stepPriceId} and SchemeId = #{schemeId}
    </delete>
    <select id="getBySchemeId" resultType="com.siteweb.energy.entity.EnergyElecFeeStepPrice">
        SELECT * FROM Energy_ElecFeeStepPrice WHERE SchemeId=#{schemeId} ORDER BY AsMaxStep,StepPriceId
    </select>
    <select id="countBy" resultType="java.lang.Integer">
        SELECT COUNT(StepPriceId) FROM Energy_ElecFeeStepPrice WHERE SchemeId=#{schemeId}
    </select>
    <select id="getByCondition" resultType="com.siteweb.energy.entity.EnergyElecFeeStepPrice">
        SELECT * FROM Energy_ElecFeeStepPrice where StepPriceId = #{stepPriceId} and SchemeId = #{schemeId}
    </select>
    <select id="findByStepPriceId" resultType="com.siteweb.energy.entity.EnergyElecFeeStepPrice">
        SELECT * FROM Energy_ElecFeeStepPrice WHERE StepPriceId = #{stepPriceId}
    </select>
</mapper>