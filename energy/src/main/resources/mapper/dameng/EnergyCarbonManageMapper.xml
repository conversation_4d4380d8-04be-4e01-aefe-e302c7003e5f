<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyCarbonManageMapper">

    <update id="deleteDataById">
        UPDATE energy_carbonmanage SET PlanValue = #{planValue} WHERE Id = #{id};
    </update>
    <update id="updateDataById">
        UPDATE energy_carbonmanage SET PlanValue = #{planValue}, YearPlanTotalValue = #{yearPlanTotalValue} WHERE Id = #{id};
    </update>

    <select id="getConfiguredYearByObjectId" resultType="com.siteweb.energy.entity.EnergyCarbonManegePara">
        SELECT * FROM energy_carbonmanage WHERE ObjectId = #{objectId} and ObjectTypeId = #{objectTypeId};
    </select>
    <select id="getObjectConfigInfo" resultType="com.siteweb.energy.entity.EnergyCarbonManegePara">
        SELECT * FROM energy_carbonmanage WHERE ObjectId = #{objectId} and ObjectTypeId = #{objectTypeId} and "Year" = #{year};
    </select>

    <select id="getFatherObjectInfo" resultType="com.siteweb.monitoring.entity.ResourceStructure">
        select * from resourcestructure
        where ResourceStructureId  = #{objectId}
    </select>
    <select id="getAllChildsTotalValue" resultType="java.lang.String">
        select sum(YearPlanTotalValue)
        from energy_carbonmanage
        where
        ObjectTypeId = #{objectTypeId} and ObjectId in ( select ResourceStructureId
        from resourcestructure
        where ParentResourceStructureId = #{fatherObjectId} and StructureTypeId = #{objectTypeId} )
        and "year" = #{year} and "month" = 1;
    </select>
    <select id="getObjectFatherId" resultType="java.lang.String">
        select ParentResourceStructureId from resourcestructure
        where ResourceStructureId = #{objectId}  and StructureTypeId = #{objectTypeId}
    </select>
    <select id="getObjectIdById" resultType="com.siteweb.energy.entity.EnergyCarbonManegePara">
        select * from energy_carbonmanage where id = #{id};
    </select>
    <select id="getObjectChildren" resultType="com.siteweb.monitoring.entity.ResourceStructure">
        select * from resourcestructure where ParentResourceStructureId = #{objectId}
    </select>
</mapper>