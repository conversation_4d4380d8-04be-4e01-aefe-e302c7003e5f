<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyElecFeeConfigOperateLogMapper">

    <select id="getAllOrderBy" resultType="com.siteweb.energy.entity.EnergyElecFeeConfigOperateLog">
        SELECT LogId, Operator, OperatorId, UpdateDate, OperationContent, ChangeContent, ExtendField1 FROM Energy_ElecFeeConfigOperateLog Where ExtendField1 is null or ExtendField1 != 'preAlarmPoint' ORDER BY UpdateDate DESC, LogId DESC
    </select>
    <select id="getAllExcludeChangeContentOrderBy" resultType="com.siteweb.energy.entity.EnergyElecFeeConfigOperateLog">
        SELECT LogId, Operator, OperatorId, UpdateDate, OperationContent, '' AS ChangeContent, ExtendField1 FROM Energy_ElecFeeConfigOperateLog Where ExtendField1 is null or ExtendField1 != 'preAlarmPoint' ORDER BY UpdateDate DESC, LogId DESC
    </select>

</mapper>