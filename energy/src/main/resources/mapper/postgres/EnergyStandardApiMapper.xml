<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyStandardApiMapper">

    <resultMap id="ResourceStructureComplexIndex" type="com.siteweb.energy.dto.EnergySdkResourceComplexIndexDTO">
        <result column="ResourceStructureId" property="nodeId"/>
        <result column="ParentResourceStructureId" property="parentNodeId"/>
        <result column="ResourceStructureName" property="nodeName"/>
        <result column="StructureTypeId" property="nodeTypeId"/>
        <result column="ResourceStructureTypeName" property="nodeTypeName"/>
        <collection property="dataList" ofType="com.siteweb.energy.dto.ComplexIndexInfoDTO"
                    column="ObjectId">
            <result column="Unit" property="unit"/>
            <result column="ComplexIndexDefinitionId" property="labelId"/>
            <result column="ComplexIndexName" property="indexName"/>
            <result column="ComplexIndexId" property="indexId"/>
            <result column="ComplexIndexDefinitionName" property="labelName"/>
            <result column="BusinessTypeId" property="energyTypeId"/>
            <result column="BusinessTypeName" property="energyTypeName"/>
        </collection>
    </resultMap>

    <select id="getAllObjectInfoAndComplexIndex"
            resultMap="ResourceStructureComplexIndex">
        select * from resourcestructure rs
        inner join resourcestructuretype rt on rt.ResourceStructureTypeId = rs.StructureTypeId
        inner join complexindex ci on ci.ObjectId = rs.ResourceStructureId
        inner join complexindexdefinition cf on  cf.ComplexIndexDefinitionId = ci.ComplexIndexDefinitionId
        inner join complexindexbusinesstype cb on cb.BusinessTypeId = ci.BusinessTypeId order by rs.ResourceStructureId;
    </select>
    <select id="getObjectCarbonQuotaByIdAndYear" resultType="com.siteweb.energy.entity.EnergyCarbonManegePara">
        select * from energy_carbonmanage where ObjectId = #{objectId} and ObjectTypeId = #{objectTypeId} and Year = #{year};
    </select>
    <select id="getAllNoGreenBusiness" resultType="com.siteweb.complexindex.entity.ComplexIndexBusinessType">
        select * from complexindexbusinesstype where "Description" is not  null;
    </select>
    <select id="getResourceStructureNameById" resultType="java.lang.String">
        select resourceStructureName from resourcestructure where ResourceStructureId = #{objectId};
    </select>
    <select id="getObjectCarbonEmissionByIdAndYearAndMonth" resultType="java.lang.Double">
        select planValue from energy_carbonemissionmanage where ObjectId = #{objectId} and ObjectTypeId = #{objectTypeId} and Year = #{year} and Month = #{month} and isArea = 0;
    </select>
    <select id="getObjectCarbonEmissionByIdAndYear" resultType="java.lang.Double">
        select distinct yearPlanTotalValue from energy_carbonemissionmanage where ObjectId = #{objectId} and ObjectTypeId = #{objectTypeId} and Year = #{year} and isArea = 0;
    </select>
</mapper>