<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyElecFeeSchemeMapper">
    <delete id="deleteBySchemeId">
        DELETE FROM Energy_ElecFeeScheme WHERE SchemeId = #{schemeId}
    </delete>

    <select id="getAll" resultType="com.siteweb.energy.entity.EnergyElecFeeScheme">
        SELECT s.*, CASE WHEN s.BusinessTypeId=0 THEN s.BusinessTypeName ELSE CASE WHEN t.BusinessTypeName IS NULL THEN CONCAT('.',s.BusinessTypeName) ELSE t.BusinessTypeName END END AS TypeName
        FROM Energy_ElecFeeScheme s LEFT JOIN complexindexbusinesstype t ON s.BusinessTypeId = t.BusinessTypeId
        ORDER BY s.CreateDate DESC
    </select>

    <select id="findByResourceStructureId" resultType="com.siteweb.energy.entity.EnergyElecFeeScheme">
        SELECT s.*, CASE WHEN s.BusinessTypeId=0 THEN s.BusinessTypeName ELSE CASE WHEN t.BusinessTypeName IS NULL THEN CONCAT('.',s.BusinessTypeName) ELSE t.BusinessTypeName END END AS TypeName
        FROM Energy_ElecFeeScheme s LEFT JOIN complexindexbusinesstype t ON s.BusinessTypeId = t.BusinessTypeId
        WHERE s.SchemeId In
           (SELECT SchemeId from Energy_ElecFeeSchemeStructureMap WHERE ResourceStructureId=#{resourceStructureId})
        ORDER BY s.CreateDate DESC
    </select>
    <select id="findBySchemeId" resultType="com.siteweb.energy.entity.EnergyElecFeeScheme">
        SELECT s.*, CASE WHEN t.BusinessTypeName IS NULL THEN CONCAT('.',s.BusinessTypeName) ELSE t.BusinessTypeName END AS TypeName
        FROM Energy_ElecFeeScheme s LEFT JOIN complexindexbusinesstype t ON s.BusinessTypeId = t.BusinessTypeId
        WHERE s.SchemeId = #{schemeId}
    </select>
    <select id="getAllEnabledSchemes" resultType="com.siteweb.energy.entity.EnergyElecFeeScheme">
        SELECT s.*, CASE WHEN t.BusinessTypeName IS NULL THEN CONCAT('.',s.BusinessTypeName) ELSE t.BusinessTypeName END AS TypeName
        FROM Energy_ElecFeeScheme s LEFT JOIN complexindexbusinesstype t ON s.BusinessTypeId = t.BusinessTypeId
        WHERE s.EnableStatus=1 ORDER BY s.CreateDate DESC
    </select>
    <select id="findByResourceStructureIdAndTypeId" resultType="com.siteweb.energy.entity.EnergyElecFeeScheme">
        SELECT s.*, CASE WHEN t.BusinessTypeName IS NULL THEN CONCAT('.',s.BusinessTypeName) ELSE t.BusinessTypeName END AS TypeName
          FROM Energy_ElecFeeScheme s LEFT JOIN complexindexbusinesstype t ON s.BusinessTypeId = t.BusinessTypeId
        WHERE s.SchemeId In
                (SELECT SchemeId from Energy_ElecFeeSchemeStructureMap WHERE ResourceStructureId=#{resourceStructureId})
              AND s.BusinessTypeId = #{businessTypeId}
        ORDER BY s.CreateDate DESC
    </select>
</mapper>