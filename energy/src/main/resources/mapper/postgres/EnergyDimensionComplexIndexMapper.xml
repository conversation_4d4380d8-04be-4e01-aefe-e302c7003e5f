<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyDimensionComplexIndexMapper">

    <resultMap type="com.siteweb.complexindex.entity.ComplexIndex" id="ComplexIndexMap">
        <result property="complexIndexId" column="ComplexIndexId" jdbcType="INTEGER"/>
        <result property="complexIndexName" column="ComplexIndexName" jdbcType="VARCHAR"/>
        <result property="complexIndexDefinitionId" column="ComplexIndexDefinitionId" jdbcType="INTEGER"/>
        <result property="objectId" column="ObjectId" jdbcType="INTEGER"/>
        <result property="objectName" column="ObjectName" jdbcType="VARCHAR"/>
        <result property="calcCron" column="CalcCron" jdbcType="VARCHAR"/>
        <result property="calcType" column="CalcType" jdbcType="INTEGER"/>
        <result property="saveCron" column="SaveCron" jdbcType="VARCHAR"/>
        <result property="expression" column="Expression" jdbcType="VARCHAR"/>
        <result property="unit" column="Unit" jdbcType="VARCHAR"/>
        <result property="accuracy" column="Accuracy" jdbcType="VARCHAR"/>
        <result property="objectTypeId" column="ObjectTypeId" jdbcType="INTEGER"/>
        <result property="remark" column="Remark" jdbcType="VARCHAR"/>
        <result property="label" column="Label" jdbcType="VARCHAR"/>
        <result property="businessTypeId" column="BusinessTypeId" jdbcType="INTEGER"/>
        <result property="checkExpression" column="CheckExpression" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="findByResourceStructureIdAndType" resultMap="ComplexIndexMap">
        SELECT a.ComplexIndexId, a.ComplexIndexName, a.ObjectId, a.CalcCron, a.CalcType, -- 5
        a.SaveCron, a.Expression, a.Unit, a.Accuracy, a.ObjectTypeId, -- 10
        a.Remark, a.Label, a.BusinessTypeId, a.ComplexIndexDefinitionId, b.BusinessTypeName, -- 15
        a.CheckExpression
        FROM complexindex a
        LEFT JOIN complexindexbusinesstype b ON a.BusinessTypeId = b.BusinessTypeId
        LEFT JOIN complexindexdefinition c ON a.ComplexIndexDefinitionId = c.ComplexIndexDefinitionId
        WHERE a.ObjectId = #{resourceStructureId,jdbcType=INTEGER}
        AND a.objectTypeId = #{structureTypeId,jdbcType=INTEGER}
    </select>

    <select id="findByBusinessTypeIdAndComplexIndexDefinitionIdAndObjectTypeIdAndObjectId" resultMap="ComplexIndexMap">
        SELECT a.*, b.BusinessTypeName
        FROM complexindex a
        LEFT JOIN complexindexbusinesstype b ON a.BusinessTypeId = b.BusinessTypeId
        LEFT JOIN complexindexdefinition c ON a.ComplexIndexDefinitionId = c.ComplexIndexDefinitionId
        WHERE a.ObjectId = #{objectId,jdbcType=INTEGER} AND a.objectTypeId = #{objectTypeId,jdbcType=INTEGER}
        AND b.BusinessTypeId = #{businessTypeId} AND c.ComplexIndexDefinitionId = #{complexIndexDefinitionId}
    </select>
    <select id="findAllComplexindexByObjectTypeId" resultType="com.siteweb.complexindex.entity.ComplexIndex">
        SELECT a.*, b.BusinessTypeName, c.StructureName as ObjectName
        FROM complexindex a
        LEFT JOIN complexindexbusinesstype b ON a.BusinessTypeId = b.BusinessTypeId
        LEFT JOIN Energy_Structure c ON a.ObjectTypeId=${dimensionStructureObjecttypeid} AND a.ObjectId=c.StructureId
        WHERE 1=1
        <if test="objectTypeId != null">
            AND a.ObjectTypeId=#{objectTypeId}
        </if>
    </select>
</mapper>