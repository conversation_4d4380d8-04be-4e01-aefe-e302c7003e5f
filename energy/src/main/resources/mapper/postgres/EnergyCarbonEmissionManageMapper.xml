<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyCarbonEmissionManageMapper">
    <insert id="addAreaCarbonEmission">
        insert Into energy_carbonemissionmanage (objectId, objectTypeId, area, isArea, units) VALUES (#{objectId} ,#{objectType} , #{area} ,#{isArea} ,#{units})
    </insert>
    <select id="getNoAreaConfigurationInfo" resultType="com.siteweb.energy.entity.EnergyCarbonEmissionPara">
        select * from energy_carbonemissionmanage where objectId = #{objectId} and objectTypeId = #{objectTypeId} and year = #{year} and isArea = 0;
    </select>
    <update id="deleteNoAreaCarbonEmissionById">
        UPDATE energy_carbonemissionmanage SET planValue = 0 WHERE Id = #{id};
    </update>
    <select id="getAreaIsConfiguration" resultType="com.siteweb.energy.entity.EnergyCarbonEmissionPara">
        select * from energy_carbonemissionmanage where objectId = #{objectId} and objectTypeId = #{objectType} and isArea = 1;
    </select>
    <update id="updateAreaInfo">
        update energy_carbonemissionmanage set area = #{area} , units = #{units} where objectId = #{objectId} and objectTypeId= #{objectType} and isArea = 1;
    </update>
    <update id="updateDataById">
        UPDATE energy_carbonemissionmanage SET PlanValue = #{planValue}, YearPlanTotalValue = #{yearPlanTotalValue} , units = #{units} WHERE Id = #{id};
    </update>
    <select id="getUnit" resultType="java.lang.String">
        select ItemValue from energy_dataItem where EntryId = 11;
    </select>
    <select id="getConfiguration" resultType="com.siteweb.energy.entity.EnergyCarbonEmissionPara">
        select * from energy_carbonemissionmanage where objectId = #{objectId} and objectTypeId = #{objectTypeId} and isArea = 0;
    </select>
    <select id="getHistoryData" resultType="com.siteweb.energy.entity.EnergyCarbonEmissionPara">
        select * from energy_carbonemissionmanage where objectId = #{objectId} and objectTypeId = #{objectTypeId} and year = #{year} and isArea = 0;
    </select>
    <select id="getAreaConfigurationInfo" resultType="com.siteweb.energy.entity.EnergyCarbonEmissionPara">
        select * from energy_carbonemissionmanage where objectId = #{objectId} and objectTypeId = #{objectTypeId} and isArea = 1;
    </select>
</mapper>