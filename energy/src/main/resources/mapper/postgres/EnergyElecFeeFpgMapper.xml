<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyElecFeeFpgMapper">

    <delete id="deleteBySchemeId">
        DELETE FROM Energy_ElecFeeFpg WHERE SchemeId = #{schemeId}
    </delete>
    <delete id="delByCondition">
        delete FROM Energy_ElecFeeFpg where FpgId = #{fpgId} and SchemeId = #{schemeId}
    </delete>
    <select id="getBySchemeId" resultType="com.siteweb.energy.entity.EnergyElecFeeFpg">
        SELECT * FROM Energy_ElecFeeFpg WHERE SchemeId=#{schemeId} ORDER BY FpgId
    </select>
    <select id="getByFpgIdAndSchemeId" resultType="com.siteweb.energy.entity.EnergyElecFeeFpg">
        SELECT * FROM Energy_ElecFeeFpg WHERE FpgId = #{fpgId} and SchemeId = #{schemeId}
    </select>
    <select id="findByFpgId" resultType="com.siteweb.energy.entity.EnergyElecFeeFpg">
        SELECT * FROM Energy_ElecFeeFpg WHERE FpgId = #{fpgId}
    </select>
    <select id="getByCondition" resultType="com.siteweb.energy.entity.EnergyElecFeeFpg">
        SELECT * FROM Energy_ElecFeeFpg WHERE SchemeId=#{schemeId} AND FpgId!=#{fpgId} ORDER BY FpgId
    </select>
</mapper>