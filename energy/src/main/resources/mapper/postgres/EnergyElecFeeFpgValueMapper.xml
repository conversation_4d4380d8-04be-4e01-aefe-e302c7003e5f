<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.energy.mapper.EnergyElecFeeFpgValueMapper">

    <delete id="deleteBySchemeId">
        DELETE FROM Energy_ElecFeeFpgValue WHERE SchemeId = #{schemeId}
    </delete>
    <delete id="delByStepPriceId">
        delete FROM Energy_ElecFeeFpgValue where StepPriceId = #{stepPriceId} and SchemeId = #{schemeId}
    </delete>
    <delete id="delByFpgId">
        delete FROM Energy_ElecFeeFpgValue where FpgId = #{fpgId} and SchemeId = #{schemeId}
    </delete>
    <select id="findListBy" resultType="com.siteweb.energy.entity.EnergyElecFeeFpgValue">
        SELECT * FROM Energy_ElecFeeFpgValue WHERE FpgId=#{fpgId} and SchemeId=#{schemeId} ORDER BY FpgValueId
    </select>
    <select id="getByStepPriceId" resultType="com.siteweb.energy.entity.EnergyElecFeeFpgValue">
        SELECT * FROM Energy_ElecFeeFpgValue where StepPriceId = #{stepPriceId} and SchemeId = #{schemeId}
    </select>
    <select id="getByFpgId" resultType="com.siteweb.energy.entity.EnergyElecFeeFpgValue">
        SELECT * FROM Energy_ElecFeeFpgValue where FpgId = #{fpgId} and SchemeId = #{schemeId}
    </select>
</mapper>