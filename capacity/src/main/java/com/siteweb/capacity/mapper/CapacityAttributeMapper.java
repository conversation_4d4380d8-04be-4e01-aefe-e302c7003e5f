package com.siteweb.capacity.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.capacity.entity.CapacityAttribute;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/06
 */
public interface CapacityAttributeMapper extends BaseMapper<CapacityAttribute> {
    /**
     * 获取所有属性容量数据
     * @return {@link List}<{@link CapacityAttribute}>
     */
    List<CapacityAttribute> findAllAttribute();
    /**
     * @param globalResourceIds
     * @param baseAttributeId
     * @return {@link List}<{@link CapacityAttribute}>
     */
    List<CapacityAttribute> findByComputerRackIdsAndBaseAttributeId(@Param("objectIds") List<Integer> objectIds, @Param("baseAttributeId") Integer baseAttributeId);

    /**
     * 批量添加容量信息
     * @param attributeList
     * @return {@link List}<{@link CapacityAttribute}>
     */
    void batchInsert(@Param("attributeList") List<CapacityAttribute> attributeList);

    /**
     * 批量更新容量信息
     * @param attributeList
     */
    void batchUpdate(@Param("attributeList") List<CapacityAttribute> attributeList);
}
