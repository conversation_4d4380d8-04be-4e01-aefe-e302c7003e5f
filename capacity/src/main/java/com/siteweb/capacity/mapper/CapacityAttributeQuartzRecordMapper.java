package com.siteweb.capacity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.capacity.entity.CapacityAttributeQuartzRecord;
import com.siteweb.utility.vo.NameValueVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CapacityAttributeQuartzRecordMapper extends BaseMapper<CapacityAttributeQuartzRecord> {
    void batchInsert(List<CapacityAttributeQuartzRecord> recordList);

    List<NameValueVO> findSumUIndexRate(@Param("resourceStructureIds") List<Integer> resourceStructureIds, @Param("dateList") List<Date> dateList);
}
