package com.siteweb.capacity.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.capacity.entity.CapacityBaseAttribute;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/06
 */
public interface CapacityBaseAttributeMapper extends BaseMapper<CapacityBaseAttribute> {

    /**
     * 查找所有容量基类属性
     * @return {@link List}<{@link CapacityBaseAttribute}>
     */
    List<CapacityBaseAttribute> findAll();
}
