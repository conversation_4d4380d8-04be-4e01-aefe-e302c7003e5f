package com.siteweb.capacity.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 容量记录表（定时任务每天0点一记录）
 *
 * <AUTHOR>
 * @date 2022/06/16
 */
@Data
@NoArgsConstructor
public class CapacityAttributeQuartzRecord {
    /**
     * 容量记录主键id
     */
    private Integer recordId;
    /**
     * 容量基类id
     */
    private Integer baseAttributeId;
    /**
     * 容量名称
     */
    private String attributeName;
    /**
     * 资源id
     */
    private Integer objectId;
    /**
     * 资源类型id
     */
    private Integer objectTypeId;
    /**
     * 额定容量
     */
    private Double ratedCapacity;
    /**
     * 已使用容量
     */
    private Double usedCapacity;
    /**
     * 剩余容量
     */
    private Double freeCapacity;
    /**
     * 容量百分比(已使用/空闲剩余容量)
     */
    private Double percent;
    /**
     * 创建时间
     */
    private Date createTime;

    public CapacityAttributeQuartzRecord(CapacityAttribute capacityAttribute) {
        this.baseAttributeId = capacityAttribute.getBaseAttributeId();
        this.attributeName = capacityAttribute.getAttributeName();
        this.objectId = capacityAttribute.getObjectId();
        this.objectTypeId = capacityAttribute.getObjectTypeId();
        this.ratedCapacity = capacityAttribute.getRatedCapacity() != null ? capacityAttribute.getRatedCapacity() : 0d;
        this.usedCapacity = capacityAttribute.getUsedCapacity() != null ? capacityAttribute.getUsedCapacity() : 0d;
        this.freeCapacity = capacityAttribute.getFreeCapacity() != null ? capacityAttribute.getFreeCapacity() : 0d;
        this.percent = capacityAttribute.getPercent() != null ? capacityAttribute.getPercent() : 0d;
    }
}
