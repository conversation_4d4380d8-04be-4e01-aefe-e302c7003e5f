package com.siteweb.capacity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email
 * @date 2019-11-28 16:57:58
 */

@Data
@NoArgsConstructor
@TableName("CapacityBaseAttribute")
public class CapacityBaseAttribute {

    /**
     * 容量基类属性ID
     */
    @TableId(type = IdType.AUTO)
    private Integer baseAttributeId;

    /**
     * 容量基类属性名称
     */
    private String baseAttributeName;


    /**
     * 所属容量基类ID
     */
    private Integer baseTypeId;


    /**
     * 容量分类属性名
     */
    private String attributeName;


    /**
     * 逻辑处理方式
     *  0,被动更新
     *  1,指标计算
     */
    private LogicType logicType;

    /**
     * 容量单位
     */
    private String unit;


    /**
     * 备注
     */
    private String description;


    public void copy(CapacityBaseAttribute baseAttribute){
        this.setBaseAttributeName(baseAttribute.getBaseAttributeName());
        this.setBaseTypeId(baseAttribute.getBaseTypeId());
        this.setAttributeName(baseAttribute.getAttributeName());
        this.setLogicType(baseAttribute.getLogicType());
        this.setDescription(baseAttribute.getDescription());
    }
}
