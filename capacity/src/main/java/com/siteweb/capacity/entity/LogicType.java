package com.siteweb.capacity.entity;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 计算逻辑类型
 */
public enum LogicType {

    /**
     * 被动更新
     * 等待推送
     */
    PASSIVE_UPDATE(0),

    /**
     * 主动更新
     * 指标计算
     */
    COMPLEX_INDEX(1);


    LogicType(Integer value) {
        this.value = value;
    }


    public Integer getValue() {
        return value;
    }

    @EnumValue
    private final Integer value;
}
