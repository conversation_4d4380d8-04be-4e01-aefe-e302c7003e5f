package com.siteweb.capacity.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @email
 * @date 2019-11-28 16:57:58
 */
@Data
@NoArgsConstructor
@TableName("CapacityBaseType")
public class CapacityBaseType {

    /**
     * 容量基类ID
     */
    @TableId(type = IdType.AUTO)
    private Integer baseTypeId;
    /**
     * 容量基类名称
     */
    private String baseTypeName;

    /**
     * 备注
     */
    private String description;


    public void copy(CapacityBaseType baseType) {
        this.setBaseTypeId(baseType.getBaseTypeId());
        this.setBaseTypeName(baseType.getBaseTypeName());
        this.setDescription(baseType.getDescription());
    }
}
