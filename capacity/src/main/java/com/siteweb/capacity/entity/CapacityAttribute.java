package com.siteweb.capacity.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("CapacityAttribute")
public class CapacityAttribute {

    /**
     * 容量属性唯一ID
     */
    @TableId(type = IdType.AUTO)
    private Integer attributeId;


    private Integer baseTypeId;

    /**
     * 容量基类名称（只读，不能写库 没字段）
     */
    @TableField(exist = false)
    private String baseTypeName;


    private Integer baseAttributeId;

    /**
     * 基类属性名称（只读，不能写库 没字段）
     */
    @TableField(exist = false)
    private String baseAttributeName;
    @TableField(exist = false)
    private String levelOfPathName;
    @TableField(exist = false)
    private String levelOfPath;

    private String attributeName;


    private String description;

    /**
     * 容量属性所属对象ID
     */
    private Integer objectId;
    /**
     * 容量属性挂在类型
     */
    private Integer objectTypeId;

    /**
     * 资源名称
     */
    @TableField(exist = false)
    private String resourceName;
    /**
     * 层级id
     */
    @TableField(exist = false)
    private Integer resourceStructureId;
    /**
     * 指标ID（不可修改）
     * 创建/修改 容量属性时当LogicTType 为指标计算时 由容量创建
     */
    private Integer complexIndex;


    /**
     * 输入 原始数据（originCapacity）有效值限定
     * 默认为0
     */

    private Double minValue;


    /**
     * 输入 原始数据（originCapacity）有效值限定
     * 默认为 999999999
     */
    @TableField(value = "`maxValue`")
    private Double maxValue;


    /**
     * 默认的 percent
     * 当计算异常或输入数据异常时 默认的原始数据（originCapacity）值
     */
    private Double defaultValue;

    /**
     * 补偿因子
     * 对原始数据进行补偿计算
     */
    private Double compensateFactor;

    /**
     * 额定容量
     */
    private Double ratedCapacity;

    /**
     * 原始输入数据。
     */
    private Double originCapacity;

    /**
     * 已使用的容量
     * originCapacity * compensateFactor
     */
    private Double usedCapacity;

    /**
     * 空闲的的容量
     */
    private Double freeCapacity;


    /**
     * 容量百分比
     */
    private Double percent;

    /**
     * 时间
     */
    private String sampleTime;

    /**
     * 容量单位
     */
    private String unit;
    /**
     * 精度
     */
    @TableField(value = "`precision`")
    private Integer precision;

    /**
     * 逻辑处理方式
     * 0,被动更新
     * 1,指标计算
     */
    private LogicType logicType;
    public void copy(CapacityAttribute attr) {
        this.setAttributeId((attr.attributeId));
        this.setBaseTypeId((attr.baseTypeId));
        this.setBaseAttributeId((attr.baseAttributeId));
        this.setAttributeName((attr.attributeName));
        this.setDescription((attr.description));
        this.setObjectId((attr.getObjectId()));
        this.setObjectTypeId((attr.getObjectTypeId()));
        this.setComplexIndex((attr.complexIndex));
        this.setMinValue((attr.minValue));
        this.setMaxValue((attr.maxValue));
        this.setDefaultValue((attr.defaultValue));
        this.setCompensateFactor((attr.compensateFactor));
        this.setBaseTypeName(attr.getBaseTypeName());
        this.setBaseAttributeName(attr.getBaseAttributeName());
        // ===============================================
        this.setRatedCapacity((attr.ratedCapacity));
        this.setOriginCapacity(attr.originCapacity);
        this.setUsedCapacity(attr.usedCapacity);
        this.setPercent((attr.percent));
        this.setSampleTime((attr.sampleTime));
        // ===============================================
        this.setLogicType(attr.logicType);
        this.setUnit((attr.unit));
        this.setPrecision((attr.precision));
    }



    public CapacityAttribute clone( ) {
        CapacityAttribute attr = new CapacityAttribute();
        attr.setAttributeId((this.attributeId));
        attr.setBaseTypeId((this.baseTypeId));
        attr.setBaseAttributeId((this.baseAttributeId));
        attr.setAttributeName((this.attributeName));
        attr.setDescription((this.description));
        attr.setObjectId((this.getObjectId()));
        attr.setObjectTypeId((this.getObjectTypeId()));
        attr.setComplexIndex((this.complexIndex));
        attr.setMinValue((this.minValue));
        attr.setMaxValue((this.maxValue));
        attr.setDefaultValue((this.defaultValue));
        attr.setCompensateFactor((this.compensateFactor));
        attr.setBaseTypeName(this.getBaseTypeName());
        attr.setBaseAttributeName(this.getBaseAttributeName());
        // ===============================================
        attr.setRatedCapacity((this.ratedCapacity));
        attr.setOriginCapacity(this.originCapacity);
        attr.setUsedCapacity(this.usedCapacity);
        attr.setFreeCapacity(this.freeCapacity);
        attr.setPercent((this.percent));
        attr.setSampleTime((this.sampleTime));
        // ===============================================
        attr.setLogicType(this.logicType);
        attr.setUnit((this.unit));
        attr.setPrecision((this.precision));
        return attr;
    }






    @JsonIgnore
    public String getRedisKey() {
        return "Capacity:" + this.attributeId;
    }

    @TableField(exist = false)
    @JsonIgnore
    private boolean isChanged;



    @JsonIgnore
    public String getRedisValue() {
        return this.attributeId + ","           // 0
                + this.sampleTime + ","         // 1
                + this.originCapacity + ","     // 2
                + this.usedCapacity + ","       // 3
                + this.freeCapacity + ","       // 4
                + this.percent + ","            // 5
                + this.unit + ","               // 6
                + this.ratedCapacity + ","      // 7
                + this.objectId + ","   // 8
                + this.objectTypeId + ","   // 9
                + this.baseAttributeId;         // 10
    }

    public void parseRedisValue(String[] valueArray) {
        Integer attributeId = Integer.parseInt(valueArray[0]);
        if (attributeId.equals(this.attributeId)) {
            this.setSampleTime(valueArray[1]);
            this.setOriginCapacity(this.convertDouble(valueArray[2]));
            this.setUsedCapacity(this.convertDouble(valueArray[3]));
            this.setFreeCapacity(this.convertDouble(valueArray[4]));
            this.setPercent(this.convertDouble(valueArray[5]));
        }
    }

    private Double convertDouble(String value) {
        if ("null".equals(value)) {
            return null;
        }
        if ("NaN".equals(value)) {
            return Double.NaN;
        }
        return Double.parseDouble(value);
    }


    public void parseRedisValue(String value) {
        this.parseRedisValue(value.split(","));
    }

}
