package com.siteweb.capacity.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.capacity.entity.CapacityAttributeQuartzRecord;
import com.siteweb.capacity.mapper.CapacityAttributeQuartzRecordMapper;
import com.siteweb.capacity.service.CapacityAttributeQuartzRecordService;
import com.siteweb.utility.vo.NameValueVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CapacityAttributeQuartzRecordServiceImpl implements CapacityAttributeQuartzRecordService {

    @Autowired
    CapacityAttributeQuartzRecordMapper capacityAttributeQuartzRecordMapper;

    @Override
    public void batchInsert(List<CapacityAttributeQuartzRecord> recordList) {
        if (CollUtil.isEmpty(recordList)) {
            return;
        }
        capacityAttributeQuartzRecordMapper.batchInsert(recordList);
    }

    @Override
    public List<NameValueVO> findSumUIndexRate(List<Integer> resourceStructureIds, List<Date> dateList) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return new ArrayList<>();
        }
        return capacityAttributeQuartzRecordMapper.findSumUIndexRate(resourceStructureIds, dateList);
    }
}
