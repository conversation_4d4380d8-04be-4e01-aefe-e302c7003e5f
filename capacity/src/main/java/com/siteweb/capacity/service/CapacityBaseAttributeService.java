package com.siteweb.capacity.service;


import com.siteweb.capacity.entity.CapacityBaseAttribute;

import java.util.Collection;
import java.util.List;

public interface CapacityBaseAttributeService {

    List<CapacityBaseAttribute> findAllTypes();
    List<CapacityBaseAttribute> findByBaseTypeId(Integer baseTypeId);
    CapacityBaseAttribute findByBaseAttributeId(Integer baseTypeId);
    List<CapacityBaseAttribute> findByBaseAttributeName(String baseAttributeName);
    List<CapacityBaseAttribute> findByBaseAttributeIds(Collection<Integer> baseAttributeIds);
    CapacityBaseAttribute create(CapacityBaseAttribute baseAttribute);
    CapacityBaseAttribute update(CapacityBaseAttribute baseAttribute);
    CapacityBaseAttribute deleteById(Integer capacityTypeId);
    CapacityBaseAttribute findByAttributeName(String attributeName);



}