package com.siteweb.capacity.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.capacity.entity.CapacityBaseType;
import com.siteweb.capacity.mapper.CapacityBaseTypeMapper;
import com.siteweb.capacity.service.CapacityBaseTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("capacityBaseTypeService")
public class CapacityBaseTypeServiceimpl implements CapacityBaseTypeService {
    @Autowired
    private CapacityBaseTypeMapper capacityBaseTypeRepository;

    private List<CapacityBaseType> baseTypes;
    private Map<Integer, CapacityBaseType> mapCache;


    @PostConstruct
    public void init() {
        this.mapCache = new HashMap<>();
        this.baseTypes = capacityBaseTypeRepository.selectList(Wrappers.emptyWrapper());
        for (CapacityBaseType baseType : this.baseTypes) {
            this.mapCache.put(baseType.getBaseTypeId(), baseType);
        }
    }

    @Override
    public List<CapacityBaseType> findAllTypes() {
        return new ArrayList<>(this.baseTypes);
    }

    @Override
    public CapacityBaseType findByBaseTypeId(Integer baseTypeId) {
        return this.mapCache.get(baseTypeId);
    }

    @Override
    public CapacityBaseType update(CapacityBaseType baseType) {
        CapacityBaseType cache = this.findByBaseTypeId(baseType.getBaseTypeId());
        if (cache != null) {
            capacityBaseTypeRepository.insert(baseType);
            cache.copy(baseType);
            return cache;
        }
        return null;
    }

    @Override
    public void deleteById(Integer baseTypeId) {
        CapacityBaseType baseType = this.findByBaseTypeId(baseTypeId);
        if (baseType != null) {
            capacityBaseTypeRepository.deleteById(baseTypeId);
            this.baseTypes.remove(baseType);
            this.mapCache.remove(baseTypeId);
        }
    }
}
