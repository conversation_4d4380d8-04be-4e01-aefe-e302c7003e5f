package com.siteweb.capacity.service;

import com.siteweb.capacity.entity.CapacityAttributeQuartzRecord;
import com.siteweb.utility.vo.NameValueVO;

import java.util.Date;
import java.util.List;

public interface CapacityAttributeQuartzRecordService {
    void batchInsert(List<CapacityAttributeQuartzRecord> recordList);

    List<NameValueVO> findSumUIndexRate(List<Integer> resourceStructureIds, List<Date> dateList);
}
