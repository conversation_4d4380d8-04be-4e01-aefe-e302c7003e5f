
package com.siteweb.capacity.service;

import com.siteweb.capacity.dto.*;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.monitoring.dto.ResourceObject;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CapacityAttributeService {
    /**
     * 获取所有属性
     * @return
     */
    List<CapacityAttribute> findAttributes();

    Page<CapacityAttribute> findAttributes(CapacityFilterRequest capacityFilterRequest);

    /**
     * 获取某个全局对象的所有属性
     * @param resourceObject 资源对象
     * @return
     */
    List<CapacityAttribute> findAttributes(ResourceObject resourceObject);

    /**
     * 获取CapacityAttribute集合
     */
    List<CapacityAttribute> findAttributes(List<ResourceObject> resourceObjects);

    /**
     * 获取某个设备对象的所有属性
     * @param deviceId 设备ID
     * @return
     */
    List<CapacityAttribute> findAttributesByEquipmentId(Integer deviceId);

    /**
     * 获取 attributeId 的属性记录
     * @param attributeId
     * @return 单条属性，未找到则返回null
     */
    CapacityAttribute findAttribute(Integer attributeId);

    /**
     * 获取 attributeId 的属性记录
     * @param objectTypeId 资源类型id
     * @return 单条属性，未找到则返回null
     */
    List<CapacityAttribute> findAttribute(Integer objectTypeId,Integer baseAttributeId);

    /**
     * 获取容量属性根据ids
     * @param attributeIds 容量属性ids
     * @return {@link List}<{@link CapacityAttribute}>
     */
    List<CapacityAttribute> findAttributes(String attributeIds);

    /**
     * 获取指定 globalResourceId 的指定 baseAttributeId 属性
     *
     * @param resourceObject 资源对象
     * @param baseAttributeId  基类属性ID
     * @return 单条属性，未找到则返回null
     */
    CapacityAttribute findAttribute(ResourceObject resourceObject, Integer baseAttributeId);

    /**
     * 获取指定 attributeName 属性名获取 globalResourceId 的 属性
     *
     * @param resourceObject 资源对象
     * @param attributeName    属性名
     * @return 单条属性，未找到则返回null
     */
    CapacityAttribute findAttribute(ResourceObject resourceObject, String attributeName);

    /**
     * 给全局对象创建一条指定基类的属性，如果已存在则返回已存在的记录
     * 参数 globalResourceId
     * 参数 baseAttributeId
     * @param params
     * @return
     */
    List<CapacityAttribute> createAttribute(CapacityAttributeCreatedParam params);

    /**
     * 创建多条属性，如果已存在则返回已存在的记录
     * 参数 globalResourceId
     * 参数 baseAttributeId
     * @param params
     * @return
     */
    List<CapacityAttribute> createAttributes(List<CapacityAttributeCreatedParam> params);

    /**
     * 创建多条属性，如果已存在则返回已存在的记录
     * 参数 globalResourceId
     * 参数 baseAttributeId
     * @param params
     * @return
     */
    List<CapacityAttribute> createAttributes(CapacityAttributeCreatedParam params);

    /**
     * 更新属性信息
     * @param attribute
     * @return
     */
    CapacityAttribute updateAttribute(CapacityAttribute attribute);

    /**
     * 更新多条属性信息
     * @param attributes
     * @return
     */
    List<CapacityAttribute> updateAttributes(List<CapacityAttribute> attributes);


    boolean updateTempAttributeValue(@NotNull CapacityAttribute attribute, Double originValue,boolean inputCheck);

    /**
     * 更新 结果值
     * @param values
     * @return
     */
    List<CapacityAttribute> updateValues(List<CapacityUpdateAttributeValue> values);


    /**
     * 根据一个属性ID删除一条属性。
     * @param attributeId
     * @return
     */
    CapacityAttribute deleteAttribute(Integer attributeId);


    void  deleteByObject(ResourceObject resourceObject);

    /**
     * 监听配置更新事件
     * @param baseAttributeId
     */
    void addConfigureListener(Integer baseAttributeId,CapacityAttributeConfigureListener listener);

    /**
     * 监听配置更新事件
     * @param baseAttributeId
     */
    void removeConfigureListener(Integer baseAttributeId,CapacityAttributeConfigureListener listener);

    /**
     * 获取各个百分比区间的统计
     * @param caPercentParam
     * @return
     */
    List<CapacityAttributePercentCount> getCAPercentCountList(CapacityAttributePercentParam caPercentParam);

    void batchUpdateAttribute(List<CapacityAttribute> complexIndexResult);
}