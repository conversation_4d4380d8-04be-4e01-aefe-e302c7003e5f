package com.siteweb.capacity.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.capacity.dto.*;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.entity.CapacityBaseAttribute;
import com.siteweb.capacity.entity.CapacityBaseType;
import com.siteweb.capacity.entity.LogicType;
import com.siteweb.capacity.mapper.CapacityAttributeMapper;
import com.siteweb.capacity.service.CapacityAttributeConfigureListener;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.capacity.service.CapacityBaseAttributeService;
import com.siteweb.capacity.service.CapacityBaseTypeService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceObjectService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service("capacityAttributeService")
@Slf4j
public class CapacityAttributeServiceImpl implements CapacityAttributeService, ApplicationListener<BaseSpringEvent<Equipment>> {
    @Autowired
    private CapacityAttributeMapper capacityAttributeMapper;

    @Autowired
    private CapacityBaseAttributeService capacityBaseAttributeService;

    @Autowired
    private CapacityBaseTypeService capacityBaseTypeService;

    @Autowired
    private ResourceObjectManager resourceObjectManager;

    @Autowired
    private EquipmentManager equipmentManager;

    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    private ComplexIndexService complexIndexService;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    HAStatusService haStatusService;
    private final Object syncLock;
    /**
     * 时间格式化器
     */
    private SimpleDateFormat dateFormat;
    /**
     * 需要写入到 mysql 的对象列表
     */
    private List<CapacityAttribute> needsUpdate;
    /**
     * 需要写入到 redis 的数据列表
     */
    private Map<String, Object> redisWriterMap;


    /**
     * 属性列表数组
     */
    private List<CapacityAttribute> attributes;
    /**
     * AttributeId 缓存
     */
    private Map<Integer, CapacityAttribute> mapAttributeIdCache;
    /**
     * globalResourceId 缓存
     */
    private Map<ResourceObject, List<CapacityAttribute>> mapResourceCache;

    private Map<Integer, List<CapacityAttributeConfigureListener>> configureListeners;

    public CapacityAttributeServiceImpl() {
        this.syncLock = new Object();
        this.attributes = new ArrayList<>();
        this.needsUpdate = new ArrayList<>();
        this.redisWriterMap = new ConcurrentHashMap<>();
        this.mapAttributeIdCache = new ConcurrentHashMap<>();
        this.mapResourceCache = new ConcurrentHashMap<>();
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.configureListeners = new ConcurrentHashMap<>();
    }

    @PostConstruct
    public void init() {
        this.mapAttributeIdCache.clear();
        this.mapResourceCache.clear();
        this.attributes.clear();
        List<CapacityAttribute> attributes = capacityAttributeMapper.findAllAttribute();
        for (CapacityAttribute attribute : attributes) {
            this.updateStaticData(attribute);
            this.addToCache(attribute);
        }
        //拉取redis数据
        this.pullRedis();
    }


    /**
     * 发生双机切换，重新初始化缓存,但是得在设备缓存之后
     * @param equipmentCacheFinishEvent 设备缓存完毕事件
     */
    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<Equipment> equipmentCacheFinishEvent) {
        init();
        log.info("CapacityAttribute Cache Init!");
    }

    /**
     * 每10秒刷新mysql数据
     */
    @Scheduled(fixedDelay = 10 * 1000)
    private void fixedUpdate() {
        List<CapacityAttribute> list;
        synchronized (this.syncLock) {
            if (CollUtil.isEmpty(this.needsUpdate)) {
                return;
            }
            list = new ArrayList<>(this.needsUpdate);
            this.needsUpdate.clear();
        }
        try {
            capacityAttributeMapper.batchUpdate(list);
            ArrayList<CapacityAttribute> objects = new ArrayList<>(list);
            for (CapacityAttribute attribute : objects) {
                this.updateStaticData(attribute);
                CapacityAttribute cache = this.findCache(attribute.getAttributeId());
                if (cache != null) {
                    cache.copy(attribute);
                }
            }
        } catch (Exception ex) {
            log.error("fixedUpdate", ex);
        }
    }

    private void pullRedis() {
        List<Integer> ids = new ArrayList<>();
        List<String> keys = new ArrayList<>();
        for (CapacityAttribute attribute : this.attributes) {
            ids.add(attribute.getAttributeId());
            keys.add(attribute.getRedisKey());
        }
        List<Object> values = redisUtil.mget(keys);
        if (values != null && values.size() == ids.size()) {
            //
            for (int i = 0; i < values.size(); i++) {
                Integer attributeId = ids.get(i);
                Object data = values.get(i);
                if (data != null) {
                    CapacityAttribute attribute = this.findCache(attributeId);
                    if (attribute != null) {
                        attribute.parseRedisValue(data.toString());
                    }
                }
            }
        }
    }

    /**
     * @param attributeId 查找容量属性缓存
     * @return {@link CapacityAttribute}
     */
    private CapacityAttribute findCache(Integer attributeId) {
        return this.mapAttributeIdCache.getOrDefault(attributeId, null);
    }

    private CapacityAttribute findCache(ResourceObject resourceObject, Integer baseAttributeId) {
        List<CapacityAttribute> globalResourceGroup = this.mapResourceCache.getOrDefault(resourceObject, null);
        if (ObjectUtil.isNull(globalResourceGroup)) {
            return null;
        }
        for (CapacityAttribute attribute : globalResourceGroup) {
            if (attribute.getBaseAttributeId().equals(baseAttributeId)) {
                return attribute;
            }
        }
        return null;
    }

    private CapacityAttribute findCache(ResourceObject resourceObject, String attributeName) {
        List<CapacityAttribute> resourceGroup = this.mapResourceCache.getOrDefault(resourceObject, null);
        if (resourceGroup == null) {
            return null;
        }
        for (CapacityAttribute attribute : resourceGroup) {
            if (attribute.getAttributeName().equals(attributeName)) {
                return attribute;
            }
        }
        return null;
    }

    /**
     * 为attribute添加部分需要连表得数据
     *
     * @param attribute 容量属性
     */
    private void updateStaticData(CapacityAttribute attribute) {
        CapacityBaseAttribute baseAttribute = this.capacityBaseAttributeService.findByBaseAttributeId(attribute.getBaseAttributeId());
        if (baseAttribute != null) {
            attribute.setBaseAttributeName(baseAttribute.getBaseAttributeName());
            CapacityBaseType baseType = this.capacityBaseTypeService.findByBaseTypeId(baseAttribute.getBaseTypeId());
            if (baseType != null) {
                attribute.setBaseTypeName(baseType.getBaseTypeName());
            }
        }
    }

    @NotNull
    private String getNow() {
        return this.dateFormat.format(new Date(System.currentTimeMillis()));
    }

    private void addToCache(CapacityAttribute attribute) {
        //更新资源名称，基类名称，路径信息
        ResourceObjectEntity entityByObject = resourceObjectManager.findEntityByObject(new ResourceObject(attribute.getObjectId(), attribute.getObjectTypeId()));
        if (ObjectUtil.isNull(entityByObject)) {
            return;
        }
        attribute.setResourceName(entityByObject.getResourceName());
        String levelOfPath = resourceStructureManager.getResourceStructureById(entityByObject.getResourceStructureId())
                                                     .getLevelOfPath();
        String levelOfPathName = resourceStructureManager.getLevelOfPathName(levelOfPath);
        attribute.setLevelOfPathName(levelOfPathName);
        attribute.setLevelOfPath(levelOfPath);
        attribute.setResourceStructureId(entityByObject.getResourceStructureId());
        CapacityBaseAttribute baseAttribute = capacityBaseAttributeService.findByBaseAttributeId(attribute.getBaseAttributeId());
        attribute.setBaseAttributeName(baseAttribute.getBaseAttributeName());
        this.mapAttributeIdCache.put(attribute.getAttributeId(), attribute);
        ResourceObject resourceKey = new ResourceObject(attribute.getObjectId(), attribute.getObjectTypeId());
        this.mapResourceCache.computeIfAbsent(resourceKey, k -> new ArrayList<>())
                             .add(attribute);
        this.attributes.add(attribute);
    }

    @Override
    public List<CapacityAttribute> findAttributes() {
        List<CapacityAttribute> result = new ArrayList<>();
        for (CapacityAttribute attribute : this.attributes) {
            result.add(attribute.clone());
        }
        return result;
    }

    @Override
    public Page<CapacityAttribute> findAttributes(CapacityFilterRequest capacityFilterRequest) {
        if (StrUtil.isNotBlank(capacityFilterRequest.getOrderByKey())) {
            //排序
            this.sortAttributes(capacityFilterRequest.getOrderByKey(), capacityFilterRequest.getOrderByDirection());
        }
        //根据用户ID过滤权限下的节点
        if(capacityFilterRequest.getUserId() != null && capacityFilterRequest.getResourceStructureIds() == null){
            List<ResourceObjectEntity> permissionRes = ((ResourceObjectService)resourceStructureService).findAllResourceObjectByUserId(capacityFilterRequest.getUserId());
            String resIdStr = permissionRes.stream()
                    .map(ResourceObjectEntity::getResourceStructureId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            capacityFilterRequest.setResourceStructureIds(resIdStr);
        }
        //根据字段 数据 条件 过滤容量列表
        List<CapacityAttribute> resourcedStructureFilterResult = this.getFilterResult("resourceStructureId", attributes, capacityFilterRequest.getResourceStructureIds());
        List<CapacityAttribute> baseTypeFilterResult = this.getFilterResult("baseTypeId", resourcedStructureFilterResult, capacityFilterRequest.getBaseTypeIds());
        List<CapacityAttribute> keyWordFilterResult = this.getFilterResult("keyWord", baseTypeFilterResult, capacityFilterRequest.getKeywords());
        //构建分页数据
        return buildAttributePageList(keyWordFilterResult, capacityFilterRequest.getNumber(), capacityFilterRequest.getSize());
    }

    /**
     * 构建分页好的容量属性列表
     *
     * @param capacityAttributeList 需要分页显示的容量属性列表
     * @param number                当前页
     * @param size                  每页显示条数
     * @return {@link Page}<{@link CapacityAttribute}>
     */
    private Page<CapacityAttribute> buildAttributePageList(List<CapacityAttribute> capacityAttributeList, Integer number, Integer size) {
        Pageable pageable = PageRequest.of(number - 1, size);
        List<CapacityAttribute> records = capacityAttributeList.stream()
                .skip((long) (number - 1) * size)
                .limit(size)
                .toList();
        return new PageImpl<>(records, pageable, capacityAttributeList.size());
    }

    /**
     * 过滤容量熟悉
     *
     * @param columnName   过滤字段
     * @param attributes   容量列表
     * @param filterString 过滤条件
     * @return {@link List}<{@link CapacityAttribute}>
     */
    private List<CapacityAttribute> getFilterResult(String columnName, List<CapacityAttribute> attributes, String filterString) {
        //如果时空字符串，就把源头返回回去
        if (StrUtil.isBlank(filterString)) return attributes;
        //如果过滤字符串不为空，查找
        List<CapacityAttribute> result = new ArrayList<>();
        //过滤条件根据逗号分割
        Set<String> uniqueFilterList = new HashSet<>(StrUtil.split(filterString, ','));
        for (String filter : uniqueFilterList) {
            if (StrUtil.isBlank(filter)) continue;
            switch (columnName) {
                case "baseTypeId" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getBaseTypeId() == Integer.parseInt(filter)))
                        .toList());
                case "resourceStructureId" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getResourceStructureId() == Integer.parseInt(filter)))
                        .toList());
                case "keyWord" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getBaseAttributeName().contains(filter) || item.getResourceName().contains(filter) || item.getLevelOfPathName().contains(filter)))
                        .toList());
            }
        }
        return result;
    }

    /**
     * 容量属性排序
     *
     * @param orderByKey       排序字段
     * @param orderByDirection 升序asc 降序desc
     */
    private void sortAttributes(String orderByKey, String orderByDirection) {
        CapacityComparator comparatorTask = new CapacityComparator(orderByKey, orderByDirection);
        attributes.sort(comparatorTask);
    }

    @Override
    public List<CapacityAttribute> findAttributes(ResourceObject resourceObject) {
        List<CapacityAttribute> result = new ArrayList<>();
        List<CapacityAttribute> resourceGroup = this.mapResourceCache.getOrDefault(resourceObject, null);
        if (resourceGroup != null) {
            for (CapacityAttribute capacityAttribute : resourceGroup) {
                result.add(capacityAttribute.clone());
            }
        }
        return result;
    }

    @Override
    public List<CapacityAttribute> findAttributes(List<ResourceObject> resourceObjects) {
        List<CapacityAttribute> result = new ArrayList<>();
        for (ResourceObject resourceObject : resourceObjects) {
            List<CapacityAttribute> resourceGroup = this.mapResourceCache.getOrDefault(resourceObject, null);
            if (resourceGroup != null) {
                for (CapacityAttribute capacityAttribute : resourceGroup) {
                    result.add(capacityAttribute.clone());
                }
            }
        }
        return result;
    }

    @Override
    public List<CapacityAttribute> findAttributesByEquipmentId(Integer equipmentId) {
        Equipment equipment = this.equipmentManager.getEquipmentById(equipmentId);
        if (ObjectUtil.isNull(equipment)) {
            return Collections.emptyList();
        }
        ResourceObject resourceObject = new ResourceObject(equipment.getEquipmentId(), SourceType.EQUIPMENT.value());
        return this.findAttributes(resourceObject);
    }

    @Override
    public CapacityAttribute findAttribute(Integer attributeId) {
        CapacityAttribute attribute = this.mapAttributeIdCache.getOrDefault(attributeId, null);
        if (ObjectUtil.isNull(attribute)) {
            return null;
        }
        return attribute.clone();
    }

    @Override
    public List<CapacityAttribute> findAttribute(Integer objectTypeId, Integer baseAttributeId) {
        return this.attributes.stream()
                .filter(attribute -> Objects.equals(objectTypeId, attribute.getObjectTypeId()) &&
                        Objects.equals(baseAttributeId, attribute.getBaseAttributeId()))
                .map(CapacityAttribute::clone)
                .collect(Collectors.toList());
    }

    @Override
    public List<CapacityAttribute> findAttributes(String attributeIds) {
        List<CapacityAttribute> result = new ArrayList<>();
        List<String> attributeIdSplit = StrUtil.split(attributeIds, ',');
        for (String attributeId : attributeIdSplit) {
            CapacityAttribute attribute = this.findAttribute(Integer.parseInt(attributeId));
            if (ObjectUtil.isNotNull(attribute)) {
                result.add(attribute);
            }
        }
        return result;
    }

    @Override
    public CapacityAttribute findAttribute(ResourceObject resourceObject, Integer baseAttributeId) {
        List<CapacityAttribute> resourceGroup = this.mapResourceCache.getOrDefault(resourceObject, null);
        if (ObjectUtil.isNull(resourceGroup)) {
            return null;
        }
        for (CapacityAttribute attribute : resourceGroup) {
            if (attribute.getBaseAttributeId().equals(baseAttributeId)) {
                return attribute.clone();
            }
        }
        return null;
    }

    @Override
    public CapacityAttribute findAttribute(ResourceObject resourceObject, String attributeName) {
        List<CapacityAttribute> resourceGroup = this.mapResourceCache.getOrDefault(resourceObject, null);
        if (ObjectUtil.isNull(resourceGroup)) {
            return null;
        }
        for (CapacityAttribute attribute : resourceGroup) {
            if (attribute.getAttributeName().equals(attributeName)) {
                return attribute;
            }
        }
        return null;
    }

    @Override
    public List<CapacityAttribute> createAttribute(CapacityAttributeCreatedParam createdParam) {
        List<CapacityAttribute> result = new ArrayList<>();
        List<CapacityAttribute> createList = new ArrayList<>();
        Map<Integer, Boolean> createdMap = new HashMap<>();
        // 校验 GlobalResourceId 是否有效
        ResourceObject resourceObjectKey = new ResourceObject(createdParam.getObjectId(), createdParam.getObjectTypeId());
        if (!this.isResourceObject(resourceObjectKey)) {
            return null;
        }
        List<CapacityAttributeCreateItem> createItems = createdParam.getAttributes();
        for (CapacityAttributeCreateItem createItem : createItems) {
            int baseAttributeId = createItem.getBaseAttributeId();
            // 重复过滤
            if (createdMap.get(baseAttributeId) != null) {
                continue;
            }
            createdMap.put(baseAttributeId, true);
            // GlobalResourceId+BaseAttributeId匹配 如果已有记录，返回已有的。
            ResourceObject resourceObject = new ResourceObject(createdParam.getObjectId(), createdParam.getObjectTypeId());
            CapacityAttribute cache = this.findCache(resourceObject, baseAttributeId);
            if (cache != null) {
                result.add(cache);
                continue;
            }
            // 获取 验证基类id
            CapacityBaseAttribute capacityBaseAttribute = this.capacityBaseAttributeService.findByBaseAttributeId(baseAttributeId);
            if (capacityBaseAttribute != null) {
                CapacityAttribute attribute = new CapacityAttribute();
                CapacityBaseType baseType = this.capacityBaseTypeService.findByBaseTypeId(capacityBaseAttribute.getBaseTypeId());
                if (baseType != null) {
                    attribute.setBaseTypeName(baseType.getBaseTypeName());
                }
                attribute.setComplexIndex(null);
                attribute.setSampleTime(this.getNow());
                attribute.setBaseAttributeName(capacityBaseAttribute.getBaseAttributeName());
                attribute.setObjectId(createdParam.getObjectId());
                attribute.setObjectTypeId(createdParam.getObjectTypeId());
                attribute.setBaseAttributeId(baseAttributeId);
                attribute.setBaseTypeId(capacityBaseAttribute.getBaseTypeId());
                attribute.setBaseAttributeId(capacityBaseAttribute.getBaseAttributeId());
                attribute.setAttributeName(capacityBaseAttribute.getAttributeName());
                attribute.setDescription(createItem.getDescription() != null ? createItem.getDescription() : null);
                attribute.setDefaultValue(createItem.getDefaultValue() != null ? createItem.getDefaultValue() : null);
                attribute.setMinValue(createItem.getMinValue() != null ? createItem.getMinValue() : 0d);
                attribute.setMaxValue(createItem.getMaxValue() != null ? createItem.getMaxValue() : 999999d);
                attribute.setRatedCapacity(createItem.getRatedCapacity() != null ? createItem.getRatedCapacity() : 2000d);
                attribute.setCompensateFactor(createItem.getCompensateFactor() != null ? createItem.getCompensateFactor() : 1.0d);
                attribute.setUnit(createItem.getUnit() != null ? createItem.getUnit() : capacityBaseAttribute.getUnit());
                attribute.setPrecision(createItem.getPrecision() != null ? createItem.getPrecision() : 2);
                attribute.setLogicType(createItem.getLogicType() != null ? createItem.getLogicType() : capacityBaseAttribute.getLogicType());
                if (LogicType.COMPLEX_INDEX.equals(attribute.getLogicType())) {
                    ComplexIndex complexIndex = this.createOrGetComplexIndex(createdParam.getObjectId(), createdParam.getObjectTypeId(), attribute.getAttributeName());
                    attribute.setComplexIndex(complexIndex != null ? complexIndex.getComplexIndexId() : null);
                }
                attribute.setOriginCapacity(null);
                attribute.setUsedCapacity(null);
                attribute.setFreeCapacity(null);
                attribute.setPercent(null);
                createList.add(attribute);
            }
        }
        if (CollUtil.isNotEmpty(createList)) {
            try {
                capacityAttributeMapper.batchInsert(createList);
                List<CapacityAttribute> success = new ArrayList<>(createList);
                createList.clear();
                for (CapacityAttribute attr : success) {
                    this.updateStaticData(attr);
                    this.dispatchConfigureUpdate(attr);
                    this.addToCache(attr);
                    attr.setChanged(true);
                    result.add(attr.clone());
                }
                success.clear();
            } catch (Exception ex) {
                log.error("createAttribute", ex);
                for (CapacityAttribute attribute : createList) {
                    if (attribute.getLogicType().equals(LogicType.COMPLEX_INDEX) && attribute.getComplexIndex() != null) {
                        this.complexIndexService.deleteById(attribute.getComplexIndex());
                    }
                }
            }
        }
        createdMap.clear();
        return result;
    }

    private void dispatchConfigureUpdate(CapacityAttribute attr) {
        List<CapacityAttributeConfigureListener> list = this.configureListeners.get(attr.getBaseAttributeId());
        if (CollUtil.isNotEmpty(list)) {
            ArrayList<CapacityAttributeConfigureListener> items = new ArrayList<>(list);
            for (CapacityAttributeConfigureListener listener : items) {
                listener.onUpdateCapacityAttributeConfigyre(attr);
            }
        }
    }

    private ComplexIndex createOrGetComplexIndex(Integer objectId, Integer objectTypeId, String complexIndexName) {
        ComplexIndex index = null;
        try {
            index = new ComplexIndex();
            // 所属对象
            index.setObjectId(objectId);
            index.setObjectTypeId(objectTypeId);
            index.setComplexIndexName(complexIndexName);
            // 5秒计算一次
            index.setCalcCron("0/5 * * * * ?");
            // 暂时不存储
            index.setSaveCron("0 0 0 1 1/1 ?");
            // 表达式默认 0
            index.setExpression("0");
            index.setAccuracy("0.000");
            //index.setObjectTypeId(1);
            index = this.complexIndexService.save(index);
        } catch (Exception ex) {
            index = this.complexIndexService.findByComplexIndexNameAndObjectId(complexIndexName, objectId, objectTypeId);
        }
        return index;
    }

    private boolean isResourceObject(ResourceObject resourceObject) {
        return resourceObjectManager.findEntityByObject(resourceObject) != null;
    }

    @Override
    public List<CapacityAttribute> createAttributes(List<CapacityAttributeCreatedParam> attributes) {
        List<CapacityAttribute> result = new ArrayList<>();
        for (CapacityAttributeCreatedParam attribute : attributes) {
            List<CapacityAttribute> attrs = this.createAttribute(attribute);
            if (CollUtil.isNotEmpty(attrs)) {
                result.addAll(attrs);
            }
        }
        return result;
    }

    @Override
    public List<CapacityAttribute> createAttributes(CapacityAttributeCreatedParam params) {
        return this.createAttribute(params);
    }

    @Override
    public CapacityAttribute updateAttribute(CapacityAttribute attribute) {
        CapacityAttribute cache = this.findCache(attribute.getAttributeId());
        if (cache != null && Objects.equals(cache.getObjectId(), attribute.getObjectId()) && Objects.equals(cache.getObjectTypeId(), attribute.getObjectTypeId())) {
            // 属性变更 值不会动
            attribute.setOriginCapacity(cache.getOriginCapacity());
            attribute.setUsedCapacity(cache.getUsedCapacity());
            attribute.setFreeCapacity(cache.getFreeCapacity());
            attribute.setPercent(cache.getPercent());

            boolean isChangeRated = !attribute.getRatedCapacity().equals(cache.getRatedCapacity());

            // 变更了基类属性
            if (!cache.getBaseAttributeId().equals(attribute.getBaseAttributeId())) {
                CapacityBaseAttribute capacityBaseAttribute = this.capacityBaseAttributeService.findByBaseAttributeId(attribute.getBaseAttributeId());
                if (capacityBaseAttribute == null) return null;
                attribute.setBaseTypeId(capacityBaseAttribute.getBaseTypeId());
                attribute.setAttributeName(capacityBaseAttribute.getAttributeName());
            }
            // 更换计算逻辑时 更新指标配置
            if (attribute.getLogicType().equals(LogicType.COMPLEX_INDEX) && attribute.getComplexIndex() == null) {
                // 更改逻辑类型，添加指标
                ComplexIndex complexIndex = this.createOrGetComplexIndex(attribute.getObjectId(), attribute.getObjectTypeId(), attribute.getAttributeName());
                attribute.setComplexIndex(complexIndex != null ? complexIndex.getComplexIndexId() : null);
            } else if (!attribute.getLogicType().equals(LogicType.COMPLEX_INDEX) && attribute.getComplexIndex() != null) {
                // 更改计算逻辑类型，删除指标
                this.complexIndexService.deleteById(attribute.getComplexIndex());
                attribute.setComplexIndex(null);
            }
            capacityAttributeMapper.batchUpdate(List.of(attribute));
            cache.copy(attribute);
            if (isChangeRated) {
                cache.setChanged(true);
                this.updateAttributeValue(cache, cache.getOriginCapacity(), this.getNow());
                this.flushRedis();
            }

            this.updateStaticData(cache);
            this.dispatchConfigureUpdate(cache);
            return cache.clone();
        }
        return null;
    }

    /**
     * 将容量属性的数据 写入到 redis 中
     */
    private void flushRedis() {
        Map<String, Object> writerMap = null;
        synchronized (this.syncLock) {
            if (this.redisWriterMap.size() > 0) {
                writerMap = new HashMap<>(this.redisWriterMap);
                this.redisWriterMap.clear();
            }
        }
        if (writerMap != null) {
            redisUtil.mSet(writerMap);
            writerMap.clear();
        }
    }

    /**
     * 更新一个属性的 容量数据值
     *
     * @param attribute   容量对象
     * @param originValue 最原始的数据值，如指标中的计算结果 或机架中的U位占用数 等。
     * @param now         当前时间
     */
    private boolean updateAttributeValue(CapacityAttribute attribute, Double originValue, String now) {
        Double percent = null;
        Double used = null;
        Double free = null;
        Double origin = originValue;
        if (origin != null && (origin < attribute.getMinValue() || origin > attribute.getMaxValue())) {
            // 输入的原始数据 不合法。
            origin = attribute.getDefaultValue();
        }
        Double attributeOriginCapacity = attribute.getOriginCapacity();
        if (doubleEquals(origin, attributeOriginCapacity) && !attribute.isChanged()) {
            return false;
        }
        Double ratedCapacity = attribute.getRatedCapacity();
        try {
            if (origin != null) {
                // rated percent
                used = this.doubleBits(origin * attribute.getCompensateFactor(), attribute.getPrecision());
                free = this.doubleBits(ratedCapacity - used, attribute.getPrecision());
                // precision
                percent = this.doubleBits(used / ratedCapacity * 100d, attribute.getPrecision());
                // min - max
            }
        } catch (Exception ex) {
            log.error("updateAttributeValue", ex);
        } finally {
            // store
            attribute.setOriginCapacity(originValue);
            attribute.setPercent(percent);
            attribute.setUsedCapacity(used);
            attribute.setFreeCapacity(free);
            attribute.setSampleTime(now);
            attribute.setChanged(false);
            // 需要更新到mysql表数据
            synchronized (this.syncLock) {
                if (!this.needsUpdate.contains(attribute)) {
                    this.needsUpdate.add(attribute);
                }
                // GlobalResourceId BaseAttributeId 更新时间 原始值，额定容量，使用容量，剩余容量，百分比 单位
                this.redisWriterMap.put(attribute.getRedisKey(), attribute.getRedisValue());
            }
        }
        return true;
    }

    private Double doubleBits(double value, Integer precision) {
        BigDecimal b = BigDecimal.valueOf(value);
        return b.setScale(precision, RoundingMode.DOWN).doubleValue();
    }

    /**
     * 判断两个 Double 是否一样大。
     * null inFinite NaN 被认为时相同的数值（无效的）
     *
     * @param a 比较数据a
     * @param b 比较数据b
     * @return
     */
    private boolean doubleEquals(Double a, Double b) {
        int v = 0;
        if (a == null || a.isNaN() || a.isInfinite()) {
            v++;
        }
        if (b == null || b.isInfinite() || b.isNaN()) {
            v++;
        }
        if (v == 1) {
            return false;
        }
        if (v == 2) {
            return true;
        }
        double precision = 0.000001d;
        return Math.abs(a - b) < precision;
    }

    @Override
    public List<CapacityAttribute> updateAttributes(List<CapacityAttribute> attributes) {
        List<CapacityAttribute> result = new ArrayList<>();
        for (CapacityAttribute attribute : attributes) {
            result.add(this.updateAttribute(attribute));
        }
        return result;
    }

    @Override
    public boolean updateTempAttributeValue(@NotNull CapacityAttribute attribute, Double originValue, boolean inputCheck) {
        Double percent = null;
        Double used = null;
        Double free = null;
        Double origin = originValue;
        if (inputCheck) {
            if (origin != null && (origin < attribute.getMinValue() || origin > attribute.getMaxValue())) {
                // 输入的原始数据 不合法。
                origin = attribute.getDefaultValue();
            }
        }
        Double attributeOriginCapacity = attribute.getOriginCapacity();
        if (doubleEquals(origin, attributeOriginCapacity) && !attribute.isChanged()) {
            return false;
        }
        Double ratedCapacity = attribute.getRatedCapacity();
        try {
            if (origin != null) {
                // rated percent
                used = this.doubleBits(origin * attribute.getCompensateFactor(), attribute.getPrecision());
                free = this.doubleBits(ratedCapacity - used, attribute.getPrecision());
                // precision
                percent = this.doubleBits(used / ratedCapacity * 100d, attribute.getPrecision());
                // min - max
            }
        } catch (Exception ex) {
            log.error("updateTempAttributeValue", ex);
        } finally {
            // store
            attribute.setOriginCapacity(originValue);
            attribute.setPercent(percent);
            attribute.setUsedCapacity(used);
            attribute.setFreeCapacity(free);
            attribute.setSampleTime(this.getNow());
        }
        return true;
    }

    @Override
    public List<CapacityAttribute> updateValues(List<CapacityUpdateAttributeValue> values) {
        List<CapacityAttribute> hitObjects = new ArrayList<>();
        String now = this.getNow();
        for (CapacityUpdateAttributeValue row : values) {
            CapacityAttribute attribute = null;
            ResourceObject resourceKey = new ResourceObject(row.getObjectId(), row.getObjectTypeId());
            if (row.getAttributeId() != null) {
                attribute = this.findCache(row.getAttributeId());
            } else if (row.getObjectId() != null && row.getObjectTypeId() != null && row.getAttributeName() != null) {
                attribute = this.findCache(resourceKey, row.getAttributeName());
            } else if (row.getObjectId() != null && row.getObjectTypeId() != null && row.getBaseAttributeId() != null) {
                attribute = this.findCache(resourceKey, row.getBaseAttributeId());
            }
            if (attribute != null) {
                if (this.updateAttributeValue(attribute, row.getOriginValue(), now)) {
                    hitObjects.add(attribute.clone());
                }
            }
        }
        this.flushRedis();
        return hitObjects;
    }

    @Override
    public CapacityAttribute deleteAttribute(Integer attributeId) {
        CapacityAttribute cache = this.findCache(attributeId);
        if (cache != null) {
            this.removeCache(cache);
            capacityAttributeMapper.deleteById(attributeId);
            if (cache.getLogicType().equals(LogicType.COMPLEX_INDEX)) {
                if (cache.getComplexIndex() != null) {
                    this.complexIndexService.deleteById(cache.getComplexIndex());
                }
            }
            return cache;
        }
        return null;
    }


    @Override
    public void deleteByObject(ResourceObject resourceObject) {
        List<CapacityAttribute> result = this.findAttributes(resourceObject);
        if (result != null && result.size() > 0) {
            for (CapacityAttribute attribute : result) {
                this.deleteAttribute(attribute.getAttributeId());
            }
        }
    }


    private void removeCache(CapacityAttribute attribute) {
        List<CapacityAttribute> globalResourceGroup = this.mapResourceCache.get(new ResourceObject(attribute.getObjectId(), attribute.getObjectTypeId()));
        if (globalResourceGroup != null) {
            globalResourceGroup.remove(attribute);
        }
        this.attributes.remove(attribute);
        this.mapAttributeIdCache.remove(attribute.getBaseAttributeId());
    }

    @Override
    public void addConfigureListener(Integer baseAttributeId, CapacityAttributeConfigureListener listener) {
        List<CapacityAttributeConfigureListener> list = this.configureListeners.computeIfAbsent(baseAttributeId, k -> new ArrayList<>());
        if (!list.contains(listener)) {
            list.add(listener);
        }
    }

    @Override
    public void removeConfigureListener(Integer baseAttributeId, CapacityAttributeConfigureListener listener) {
        List<CapacityAttributeConfigureListener> list = this.configureListeners.get(baseAttributeId);
        if (list != null) {
            list.remove(listener);
            if (list.isEmpty()) {
                this.configureListeners.remove(baseAttributeId);
            }
        }
    }

    @Override
    public List<CapacityAttributePercentCount> getCAPercentCountList(CapacityAttributePercentParam caPercentParam) {
        List<Integer> computerRackIds = caPercentParam.getGlobalResourceIds();
        Integer baseAttributeId = caPercentParam.getBaseAttributeId();
        List<CapacityAttributePercentCount> caPercentCountList = caPercentParam.getCaPercentCountList();
        if (ObjectUtil.isNull(baseAttributeId) || CollUtil.isEmpty(caPercentCountList)) {
            return new ArrayList<>();
        }
        if (CollUtil.isEmpty(computerRackIds)) {
            //查询所有的机架
            computerRackIds = resourceObjectManager.findEntityByObjectType(SourceType.COMPUTERRACK.value())
                    .stream()
                    .map(ResourceObjectEntity::getObjectId)
                    .toList();
        }
        List<CapacityAttribute> caList = capacityAttributeMapper.findByComputerRackIdsAndBaseAttributeId(computerRackIds, baseAttributeId);
        // 判断百分比属于哪个区间
        for (CapacityAttributePercentCount capc : caPercentCountList) {
            int cnt = 0;
            for (CapacityAttribute ca : caList) {
                Double percent = ca.getPercent();
                if (ObjectUtil.isNull(percent)) {
                    continue;
                }
                if (percent >= capc.getMinimum() && percent < capc.getMaximum()) {
                    cnt++;
                }
            }
            capc.setCount(cnt);
        }
        return caPercentCountList;
    }


    @Override
    public void batchUpdateAttribute(List<CapacityAttribute> complexIndexResult) {
        String now = this.getNow();
        for (CapacityAttribute oneAttribute : complexIndexResult) {
            CapacityAttribute attribute = null;
            if (oneAttribute.getAttributeId() != null) {
                attribute = this.findCache(oneAttribute.getAttributeId());
            }
            if (attribute != null) {
                updateAttributeValue(attribute, oneAttribute.getOriginCapacity(), now);
            }
        }
        this.flushRedis();
    }
}
