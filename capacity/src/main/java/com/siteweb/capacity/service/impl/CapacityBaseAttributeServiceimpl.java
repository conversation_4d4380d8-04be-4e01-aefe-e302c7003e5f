package com.siteweb.capacity.service.impl;

import com.siteweb.capacity.entity.CapacityBaseAttribute;
import com.siteweb.capacity.mapper.CapacityBaseAttributeMapper;
import com.siteweb.capacity.service.CapacityBaseAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;


@Service("capacityBaseAttributeService")
public class CapacityBaseAttributeServiceimpl implements CapacityBaseAttributeService {

    @Autowired
    private CapacityBaseAttributeMapper capacityAttributeBaseTypeRepository;
    private List<CapacityBaseAttribute> baseAttributes;
    private Map<Integer, CapacityBaseAttribute> mapCache;


    @PostConstruct
    public void init() {
        this.mapCache = new HashMap<>();
        this.baseAttributes = capacityAttributeBaseTypeRepository.findAll();
        for (CapacityBaseAttribute baseAttribute : this.baseAttributes) {
            this.mapCache.put(baseAttribute.getBaseAttributeId(), baseAttribute);
        }
    }


    @Override
    public List<CapacityBaseAttribute> findAllTypes() {
        return this.baseAttributes;
    }

    @Override
    public CapacityBaseAttribute findByBaseAttributeId(Integer baseAttributeId) {
        return this.mapCache.getOrDefault(baseAttributeId, null);
    }


    @Override
    public List<CapacityBaseAttribute> findByBaseAttributeIds(Collection<Integer> baseAttributeIds) {
        List<CapacityBaseAttribute> result = new ArrayList<>();
        if(baseAttributeIds == null) {
            return result;
        }
        for (Integer baseAttributeId : baseAttributeIds) {
            CapacityBaseAttribute baseAttr = this.mapCache.getOrDefault(baseAttributeId, null);
            if (baseAttr != null) {
                result.add(baseAttr);
            }
        }
        return result;
    }


    @Override
    public List<CapacityBaseAttribute> findByBaseAttributeName(String baseAttributeName) {

        List<CapacityBaseAttribute> capacityBaseAttributes = new ArrayList<>();
        for(CapacityBaseAttribute item : this.baseAttributes){
                if (item.getBaseAttributeName().contains(baseAttributeName))
                    capacityBaseAttributes.add(item);
        }
        return capacityBaseAttributes;
    }

    @Override
    public List<CapacityBaseAttribute> findByBaseTypeId(Integer baseTypeId) {
        List<CapacityBaseAttribute> result = new ArrayList<>();
        for (CapacityBaseAttribute baseAttribute : this.baseAttributes) {
            if (baseAttribute.getBaseTypeId().equals(baseTypeId)) {
                result.add(baseAttribute);
            }
        }
        return result;
    }

    @Override
    public CapacityBaseAttribute create(CapacityBaseAttribute baseAttribute) {
        if (baseAttribute.getBaseAttributeId() == null) {
            return null;
        }
        capacityAttributeBaseTypeRepository.insert(baseAttribute);
        this.baseAttributes.add(baseAttribute);
        this.mapCache.put(baseAttribute.getBaseAttributeId(), baseAttribute);
        return baseAttribute;
    }


    @Override
    public CapacityBaseAttribute update(CapacityBaseAttribute baseAttribute) {
        if (baseAttribute.getBaseAttributeId() == null) {
            return null;
        }
        CapacityBaseAttribute cache = this.findByBaseAttributeId(baseAttribute.getBaseAttributeId());
        if (cache != null) {
            capacityAttributeBaseTypeRepository.updateById(baseAttribute);
            cache.copy(baseAttribute);
            return cache;
        }
        return null;
    }

    @Override
    public CapacityBaseAttribute deleteById(Integer baseAttributeId) {
        for (CapacityBaseAttribute baseAttribute : this.baseAttributes) {
            if (baseAttribute.getBaseAttributeId().equals(baseAttributeId)) {
                capacityAttributeBaseTypeRepository.deleteById(baseAttributeId);
                this.baseAttributes.remove(baseAttribute);
                this.mapCache.remove(baseAttributeId);
                return baseAttribute;
            }
        }
        return null;
    }

    @Override
    public CapacityBaseAttribute findByAttributeName(String attributeName) {

        for(CapacityBaseAttribute item : this.baseAttributes){
            if (item.getAttributeName().equals(attributeName))
                return item;
        }
        return null;
    }
}
