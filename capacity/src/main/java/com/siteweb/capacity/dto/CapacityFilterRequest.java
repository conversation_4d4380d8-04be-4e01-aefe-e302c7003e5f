package com.siteweb.capacity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/05/11
 */
@Data
@ApiModel("容量列表过滤条件对象")
public class CapacityFilterRequest {
    /**
     * 基类类型
     */
    @ApiModelProperty("基类类型")
    private String baseTypeIds;
    /**
     * 过滤关键字
     */
    @ApiModelProperty("层级id多个用逗号隔开")
    private String keywords;
    /**
     * 层级id
     */
    @ApiModelProperty("层级id多个用逗号隔开")
    private String resourceStructureIds;
    /**
     * 需要排序的字段关键字
     */
    @ApiModelProperty("需要排序的字段关键字")
    private String orderByKey;
    /**
     * 排序规则 asc升序  desc降序
     */
    @ApiModelProperty("排序规则 asc升序  desc降序")
    private String orderByDirection;
    /**
     * 每页显示多少条
     */
    @ApiModelProperty("每页显示多少条 不传默认为：10")
    private Integer size = 10;
    /**
     * 第几页
     */
    @ApiModelProperty("第几页 不传默认为：1")
    private Integer number = 1;

    /**
     * 用户userId
     */
    @ApiModelProperty("用户userId")
    private Integer userId;
}
