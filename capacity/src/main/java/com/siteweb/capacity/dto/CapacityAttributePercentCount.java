package com.siteweb.capacity.dto;

import lombok.Data;

/**
 * @author: Habits
 * @time: 2021/11/26 10:02
 * @description:
 **/
@Data
public class CapacityAttributePercentCount {

    /**
     * 最小限度
     */
    private Double minimum;

    /**
     * 最大限度
     */
    private Double maximum;


    /**
     * 计数
     */
    private Integer count;

    public Integer getCount(){
        Integer count = this.count;
        if(count == null){
            return 0;
        }
        return count;
    }

}
