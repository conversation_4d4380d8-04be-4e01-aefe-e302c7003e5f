package com.siteweb.capacity.dto;

import com.siteweb.capacity.entity.LogicType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("创建容量属性参数实体")
public class CapacityAttributeCreateItem {
    /**
     * 容量基类属性ID
     */
    @ApiModelProperty("容量基类属性ID")
    private int baseAttributeId;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("精度")
    private Integer precision;

    @ApiModelProperty("计算方式 被动更新:COMPLEX_INDEX,主动更新:PASSIVE_UPDATE")
    private LogicType logicType;

    @ApiModelProperty("最小值")
    private Double minValue;

    @ApiModelProperty("最大值")
    private Double maxValue;

    @ApiModelProperty("默认值")
    private Double defaultValue;

    @ApiModelProperty("补偿因子")
    private Double compensateFactor;

    @ApiModelProperty("额定容量")
    private Double ratedCapacity;

    @ApiModelProperty("单位")
    private String unit;
}
