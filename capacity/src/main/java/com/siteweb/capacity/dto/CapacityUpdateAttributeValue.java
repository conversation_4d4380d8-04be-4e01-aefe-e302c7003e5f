package com.siteweb.capacity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class CapacityUpdateAttributeValue {

    /**
     * 容量属性的ID（该ID 不需要其他ID组合，单独使用）
     * 优先级：1
     */
    private Integer attributeId;


    /**
     * 资源对象id
     *
     */
    private Integer objectId;

    /**
     * 资源对象类型id
     *
     */
    private Integer objectTypeId;

    /**
     * 属性名（组合属性，需组合 globalResourceId）
     */
    private String attributeName;

    /**
     * 属性基类ID（组合属性，需组合 globalResourceId）
     */
    private Integer baseAttributeId;


    /**
     * ===============================================================
     * 更新的容量值，该值为原始的使用容量，如果为NaN则把该值设为null。
     * ===============================================================
     * 容量的 originValue 更新计算方式如下
     *
     * OriginCapacity = this.originValue;
     * UsedCapacity = OriginCapacity * CompensateFactor;
     * FreeCapacity = RatedCapacity - UsedCapacity;
     * Percent = UsedCapacity / RatedCapacity * 100;
     *
     */
    private Double originValue;

}
