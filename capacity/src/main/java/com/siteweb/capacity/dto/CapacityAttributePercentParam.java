package com.siteweb.capacity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Habits
 * @time: 2021/11/26 9:43
 * @description:
 **/
@Data
public class CapacityAttributePercentParam {

    /**
     * 机架id集合
     */
    @ApiModelProperty("机架id集合")
    private List<Integer> globalResourceIds;

    /**
     * 基类属性
     */
    @ApiModelProperty("基类属性")
    private Integer baseAttributeId;

    /**
     * 百分比区间
     */
    @ApiModelProperty("百分比区间")
    private List<CapacityAttributePercentCount> caPercentCountList;
}
