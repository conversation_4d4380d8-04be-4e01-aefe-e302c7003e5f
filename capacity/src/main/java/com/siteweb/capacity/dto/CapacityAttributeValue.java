package com.siteweb.capacity.dto;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.capacity.entity.CapacityAttribute;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class CapacityAttributeValue {

    private Integer attributeId;

    private Integer objectId;

    private Integer objectTypeId;

    private Integer baseAttributeId;

    private String attributeName;

    private String baseAttributeName;

    private String description;

    private Double compensateFactor;

    private Double ratedCapacity;

    private Double originCapacity;

    private Double usedCapacity;

    private Double freeCapacity;

    private Double percent;

    private String sampleTime;


    private String unit;

    public static CapacityAttributeValue fromAttribute(CapacityAttribute attribute) {
        if (ObjectUtil.isNull(attribute)) {
            return null;
        }
        CapacityAttributeValue result = new CapacityAttributeValue();
        result.setObjectId(attribute.getObjectId());
        result.setObjectTypeId(attribute.getObjectTypeId());
        result.setAttributeId(attribute.getAttributeId());
        result.setBaseAttributeId(attribute.getBaseAttributeId());
        result.setAttributeName(attribute.getAttributeName());
        result.setBaseAttributeName(attribute.getBaseAttributeName());
        result.setCompensateFactor(attribute.getCompensateFactor());
        result.setRatedCapacity(attribute.getRatedCapacity());
        result.setOriginCapacity(attribute.getOriginCapacity());
        result.setUsedCapacity(attribute.getUsedCapacity());
        result.setFreeCapacity(attribute.getFreeCapacity());
        result.setPercent(attribute.getPercent());
        result.setSampleTime(attribute.getSampleTime());
        result.setDescription(attribute.getDescription());
        result.setUnit(attribute.getUnit());
        return result;
    }


}
