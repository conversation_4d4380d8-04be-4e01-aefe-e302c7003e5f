package com.siteweb.capacity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/05/11
 */
@ApiModel("创建容量参数实体")
@Data
public class CapacityAttributeCreatedParam {
    /**
     * 对象ID
     */
    @ApiModelProperty("对象id 通常为主键唯一id")
    private Integer objectId;
    /**
     * 对象类型
     */
    @ApiModelProperty("对象类型id 设备 7 通用设备 8 机架 9 IT设备 10 层级资源 11")
    private Integer objectTypeId;

    /**
     * 创建的容量属性项
     */
    @ApiModelProperty("创建的容量属性项")
    private List<CapacityAttributeCreateItem> attributes;
}
