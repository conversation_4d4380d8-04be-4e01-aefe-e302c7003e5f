package com.siteweb.capacity.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.capacity.dto.CapacityAttributePercentCount;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.dto.CapacityAttributePercentParam;
import com.siteweb.capacity.dto.CapacityAttributeValue;
import com.siteweb.capacity.dto.CapacityUpdateAttributeValue;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ResourceObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api")
@Api(value = "容量信息管理(简化版)",tags = "容量信息管理(简化版)")
public class CapacityValueController {

    @Autowired
    private CapacityAttributeService capacityAttributeService;


    @ApiOperation("查询所有容量信息")
    @GetMapping(value = "/capacity/values")
    public ResponseEntity<ResponseResult> GetAllCapacityAttribute() {
        List<CapacityAttribute> attributes = capacityAttributeService.findAttributes();
        List<CapacityAttributeValue> result = new ArrayList<>();
        for (CapacityAttribute attribute : attributes) {
            CapacityAttributeValue value = CapacityAttributeValue.fromAttribute(attribute);
            result.add(value);
        }
       return ResponseHelper.successful(result);
    }


    @ApiOperation("根据设备id查询容量信息")
    @GetMapping(value = "capacity/values",
            params = {"equipmentId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByDeviceId(@RequestParam(value = "equipmentId") Integer equipmentId) {
        List<CapacityAttribute> attributes = capacityAttributeService.findAttributesByEquipmentId(equipmentId);
        if (attributes == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        List<CapacityAttributeValue> result = new ArrayList<>();
        for (CapacityAttribute attribute : attributes) {
            CapacityAttributeValue value = CapacityAttributeValue.fromAttribute(attribute);
            result.add(value);
        }
        return ResponseHelper.successful(result);
    }


    @ApiOperation("根据对象id，对象类型id和基类id查询容量信息")
    @GetMapping(value = "capacity/values",
            params = {"objectId","objectTypeId", "baseAttributeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAttribute(@RequestParam(value = "objectId") Integer objectId,
                                                       @RequestParam(value = "objectTypeId") Integer objectTypeId,
                                               @RequestParam(value = "baseAttributeId") Integer baseAttributeId) {
        CapacityAttribute attribute = capacityAttributeService.findAttribute(new ResourceObject(objectId,objectTypeId), baseAttributeId);
        return ResponseHelper.successful(CapacityAttributeValue.fromAttribute(attribute));
    }


    @ApiOperation("根据对象id，对象类型id和容量名称查询容量信息")
    @GetMapping(value = "capacity/values",
            params = {"objectId","objectTypeId", "attributeName"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByGlobalResourceId(@RequestParam(value = "objectId") Integer objectId,
                                                                                 @RequestParam(value = "objectTypeId") Integer objectTypeId,
                                                                         @RequestParam(value = "attributeName") String attributeName) {
        CapacityAttribute attribute = capacityAttributeService.findAttribute(new ResourceObject(objectId,objectTypeId), attributeName);
        return ResponseHelper.successful(CapacityAttributeValue.fromAttribute(attribute));
    }


    @ApiOperation("根据对象id，对象类型id查询容量信息")
    @GetMapping(value = "capacity/values",
            params = {"objectId","objectTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByObjectId(@RequestParam(value = "objectId") Integer objectId,@RequestParam(value = "objectTypeId") Integer objectTypeId) {
        Map<String, CapacityAttributeValue> result = new HashMap<>();
        ResourceObject resourceObjectKey = new ResourceObject(objectId, objectTypeId);
        List<CapacityAttribute> attributes = capacityAttributeService.findAttributes(resourceObjectKey);
        if (CollUtil.isNotEmpty(attributes)) {
            for (CapacityAttribute attribute : attributes) {
                CapacityAttributeValue value = CapacityAttributeValue.fromAttribute(attribute);
                if (value != null) {
                    result.put(value.getAttributeName(), value);
                }
            }
        }
        return ResponseHelper.successful(result);
    }


    @ApiOperation("根据容量id查询容量信息")
    @GetMapping(value = "capacity/values",
            params = {"attributeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByAttributeId(@RequestParam(value = "attributeId") Integer attributeId) {
        CapacityAttribute attribute = capacityAttributeService.findAttribute(attributeId);
        return ResponseHelper.successful(CapacityAttributeValue.fromAttribute(attribute));
    }


    @ApiOperation("批量更新容量信息")
    @PostMapping(value = "/capacity/values")
    public ResponseEntity<ResponseResult> updateValues(@RequestBody List<CapacityUpdateAttributeValue> values) {
        List<CapacityAttributeValue> list = new ArrayList<>();
        if (values != null && values.size() > 0) {
            List<CapacityAttribute> result = capacityAttributeService.updateValues(values);
            list = result.stream().map(CapacityAttributeValue::fromAttribute).collect(Collectors.toList());
        }
        return ResponseHelper.successful(list);
    }


    @ApiOperation("获取机架容量信息")
    @PostMapping(value = "/capacity/percentcount")
    public ResponseEntity<ResponseResult> getCAPercentCountList(@RequestBody CapacityAttributePercentParam caPercentParam){
        if(ObjectUtil.isNull(caPercentParam)){
            return ResponseHelper.failed("参数错误");
        }
        List<CapacityAttributePercentCount> result = capacityAttributeService.getCAPercentCountList(caPercentParam);
        return ResponseHelper.successful(result);
    }
}
