package com.siteweb.capacity.controller;

import com.siteweb.capacity.service.CapacityBaseTypeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "容量基类类型管理",tags = "容量基类类型管理")
public class CapacityBaseTypeController {

    @Autowired
    private CapacityBaseTypeService capacityBaseTypeService;


    @ApiOperation("获取所有基类类型")
    @GetMapping(value = "/capacity/basetype")
    public ResponseEntity<ResponseResult> getAllTypes() {
        return ResponseHelper.successful(capacityBaseTypeService.findAllTypes());
    }


    @ApiOperation("通过id获取所有基类类型")
    @GetMapping(value = "capacity/basetype",
            params = {"baseTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getBaseAttributeByBaseTypeId(@RequestParam(value = "baseTypeId") Integer baseTypeId) {
        return ResponseHelper.successful(capacityBaseTypeService.findByBaseTypeId(baseTypeId));
    }
}
