
package com.siteweb.capacity.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.capacity.dto.CapacityAttributeCreatedParam;
import com.siteweb.capacity.dto.CapacityFilterRequest;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ResourceObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "容量接口管理",tags = "容量接口管理")
public class CapacityAttributeController {

    @Autowired
    private CapacityAttributeService capacityAttributeService;


    @ApiOperation("获取所有容量属性")
    @GetMapping(value = "/capacity/attributes")
    public ResponseEntity<ResponseResult> getAllCapacityAttribute() {
        return ResponseHelper.successful(capacityAttributeService.findAttributes());
    }

    @ApiOperation("分页获取容量属性")
    @PostMapping(value = "/capacity/pageattributes")
    public ResponseEntity<ResponseResult> getAllCapacityAttribute(@RequestBody CapacityFilterRequest capacityFilterRequest) {
        return ResponseHelper.successful(capacityAttributeService.findAttributes(capacityFilterRequest));
    }
    @ApiOperation("根据容量id获取容量信息")
    @GetMapping(value = "capacity/attributes", params = {"attributeId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParam(name = "attributeId",value = "容量id")
    public ResponseEntity<ResponseResult> getCapacityAttributeByAttributeId(@RequestParam(value = "attributeId") Integer attributeId) {
        return ResponseHelper.successful(capacityAttributeService.findAttribute(attributeId));
    }

    @ApiOperation("根据容量ids获取容量信息(多个用逗号隔开)")
    @GetMapping(value = "capacity/attributes", params = {"attributeIds"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByAttributeIds(@RequestParam(value = "attributeIds") String attributeIds) {
        return ResponseHelper.successful(capacityAttributeService.findAttributes(attributeIds));
    }

    @ApiOperation("根据容量ids获取容量信息")
    @GetMapping(value = "capacity/attributes", params = {"attributeIdList"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByAttributeIdList(@RequestParam(value = "attributeIdList") List<Integer> attributeIdList) {
        return ResponseHelper.successful(capacityAttributeService.findAttributes(CharSequenceUtil.join(",", attributeIdList)));
    }

    @ApiOperation("根据设备id获取容量信息")
    @GetMapping(value = "capacity/attributes", params = {"equipmentId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByDeviceId(@RequestParam(value = "equipmentId") Integer equipmentId) {
        return ResponseHelper.successful(capacityAttributeService.findAttributesByEquipmentId(equipmentId));
    }


    @ApiOperation("根据对象id和对象类型获取容量信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "objectId",value = "对象id 通常为主键唯一id"),
            @ApiImplicitParam(name = "objectTypeId",value = "对象类型id 设备 7 通用设备 8 机架 9 IT设备 10 层级资源 11")
    })
    @GetMapping(value = "capacity/attributes", params = {"objectId","objectTypeId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityAttributeByGlobalResourceId(@RequestParam(value = "objectId") Integer objectId,@RequestParam(value = "objectTypeId") Integer objectTypeId) {
        ResourceObject resourceObjectKey = new ResourceObject(objectId, objectTypeId);
        return ResponseHelper.successful(capacityAttributeService.findAttributes(resourceObjectKey));
    }

    @PostMapping(value = "/capacity/attribute")
    @ApiOperation("添加容量属性")
    public ResponseEntity<ResponseResult> createCapacityAttribute(@RequestBody CapacityAttributeCreatedParam params) {
        List<CapacityAttribute> result = capacityAttributeService.createAttribute(params);
        return ResponseHelper.successful(result);
    }


    @PostMapping(value = "/capacity/attributes")
    @ApiOperation("批量添加容量属性")
    public ResponseEntity<ResponseResult> createCapacityAttributes(@RequestBody List<CapacityAttributeCreatedParam> params) {
        List<CapacityAttribute> result = capacityAttributeService.createAttributes(params);
        return ResponseHelper.successful(result);
    }


    @PutMapping(value = "/capacity/attributes")
    @ApiOperation("更新容量属性")
    public ResponseEntity<ResponseResult> updateCapacityAttribute(@Valid @RequestBody CapacityAttribute attribute) {
        return ResponseHelper.successful(capacityAttributeService.updateAttribute(attribute));
    }


    @DeleteMapping(value = "/capacity/attributes/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("删除容量属性")
    public ResponseEntity<ResponseResult> deleteCapacityAttribute(@PathVariable Integer id) {
        CapacityAttribute result = capacityAttributeService.deleteAttribute(id);
        return ResponseHelper.successful(result);
    }
}
