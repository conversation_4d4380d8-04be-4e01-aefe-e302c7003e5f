package com.siteweb.capacity.controller;

import com.siteweb.capacity.service.CapacityBaseAttributeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "容量基类接口管理",tags = "容量基类接口管理")
public class CapacityBaseAttributeController {

    @Autowired
    private CapacityBaseAttributeService capacityAttributeTypeService;


    @ApiOperation("查询所有容量基类")
    @GetMapping(value = "/capacity/baseattribute")
    public ResponseEntity<ResponseResult> getAllTypes() {
        return ResponseHelper.successful(capacityAttributeTypeService.findAllTypes());
    }


    @ApiOperation("根据容量基类类型id查询容量基类信息")
    @GetMapping(value = "capacity/baseattribute",
            params = {"baseTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getBaseAttributeByBaseTypeId(@RequestParam(value = "baseTypeId") Integer baseTypeId) {
        return ResponseHelper.successful(capacityAttributeTypeService.findByBaseTypeId(baseTypeId));
    }

    @ApiOperation("根据容量基类id查询容量基类信息")
    @GetMapping(value = "capacity/baseattribute",
            params = {"baseAttributeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getBaseAttributeByBaseAttributeId(@RequestParam(value = "baseAttributeId") Integer baseAttributeId) {
        return ResponseHelper.successful(capacityAttributeTypeService.findByBaseAttributeId(baseAttributeId));
    }


}
