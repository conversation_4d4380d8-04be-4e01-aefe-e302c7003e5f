package com.siteweb.capacity.quartz;

import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 容量定时任务
 *
 * <AUTHOR>
 * @date 2022/06/16
 */
@Component
@Slf4j
public class CapacityAttributeQuartz {
    @Autowired
    CapacityAttributeService capacityAttributeService;
    @Autowired
    LiveComplexIndexManager liveComplexIndexManager;
    @Autowired
    HAStatusService haStatusService;

    /**
     * 更新通用容量系统所有容量属性中基于指标计算的值
     */
    @Scheduled(cron = "0/5 * * * * ?")
    protected void updateUsedCapacity(){
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP:更新通用容量系统所有容量属性中基于指标计算的值退出");
            return;
        }
        //获取所有基于指标计算的容量的信息
        List<CapacityAttribute> allAttributeCaChe = capacityAttributeService.findAttributes();
        List<CapacityAttribute>  allComplexIndexCapacityList = allAttributeCaChe.stream().filter(attribute -> attribute.getLogicType().getValue() == 1 && attribute.getComplexIndex() != null).toList();
        if(!allComplexIndexCapacityList.isEmpty()){
            Map<Integer, CapacityAttribute> maps = new HashMap<>();
            for (CapacityAttribute oneAttribute: allComplexIndexCapacityList ) {
                CapacityAttribute oneUpdateAttribute = new CapacityAttribute();
                oneUpdateAttribute.setAttributeId(oneAttribute.getAttributeId());
                maps.put(oneAttribute.getComplexIndex(),oneUpdateAttribute);
            }
            List<LiveComplexIndex> complexIndexResult = liveComplexIndexManager.findLiveComplexIndexByIds(maps.keySet().stream().toList());
            if(complexIndexResult != null && !complexIndexResult.isEmpty()){
                for (LiveComplexIndex oneComplexIndex : complexIndexResult){
                    CapacityAttribute item = maps.get(oneComplexIndex.getComplexIndexId());
                    if(item != null){
                        try{
                            item.setOriginCapacity(Double.parseDouble(oneComplexIndex.getCurrentValue()));
                        }catch (Exception ex) {
                            log.error("CapacityAttributeQuartz->updateUsedCapacity", ex);
                        }
                    }
                }
                capacityAttributeService.batchUpdateAttribute(maps.values().stream().toList());
            }
        }
    }
}
