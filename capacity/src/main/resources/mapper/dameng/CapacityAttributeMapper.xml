<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.capacity.mapper.CapacityAttributeMapper">
    <insert id="batchInsert" keyProperty="attributeId" useGeneratedKeys="true">
        INSERT INTO CapacityAttribute (basetypeid, baseattributeid, attributename, logictype, "description", ObjectId,
                                       ObjectTypeId, complexindex, "minvalue", "maxvalue", ratedcapacity,
                                       defaultvalue, compensatefactor, origincapacity, usedcapacity, freecapacity,
                                       "percent", sampletime, unit, "precision")
        VALUES
            <foreach collection="attributeList" item="item" separator="," >
                (#{item.baseTypeId},#{item.baseAttributeId},#{item.attributeName},#{item.logicType.value},#{item.description},#{item.objectId},#{item.objectTypeId},
                 #{item.complexIndex},#{item.minValue},#{item.maxValue},#{item.ratedCapacity},#{item.defaultValue},#{item.compensateFactor},#{item.originCapacity},
                 #{item.usedCapacity},#{item.freeCapacity},#{item.percent},#{item.sampleTime},#{item.unit},#{item.precision})
            </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="attributeList" item="item" separator=";">
            UPDATE capacityattribute SET BaseTypeId = #{item.baseTypeId}, baseattributeid = #{item.baseAttributeId},
            attributename = #{item.attributeName},logictype = #{item.logicType.value},"description"= #{item.description},
            ObjectId = #{item.objectId},ObjectTypeId =#{item.objectTypeId}, complexindex = #{item.complexIndex},
            "minValue" = #{item.minValue}, "maxvalue" = #{item.maxValue},ratedcapacity = #{item.ratedCapacity},
            defaultvalue = #{item.defaultValue}, compensateFactor = #{item.compensateFactor}, origincapacity = #{item.originCapacity},
            usedcapacity = #{item.usedCapacity},freecapacity = #{item.freeCapacity},"percent" = #{item.percent},
            sampletime = #{item.sampleTime},unit = #{item.unit},"precision" = #{item.precision}
            WHERE AttributeId = #{item.attributeId}
        </foreach>
    </update>
    <select id="findByComputerRackIdsAndBaseAttributeId" resultMap="attributeResultMap">
        SELECT attributeid, basetypeid, baseattributeid, attributename, logictype, "description", ObjectId,ObjectTypeId,
        complexindex, "minvalue", "maxvalue", ratedcapacity, defaultvalue, compensatefactor, origincapacity,
        usedcapacity,
        freecapacity, "percent", sampletime, unit, "precision" FROM CapacityAttribute WHERE (ObjectId,ObjectTypeId) IN
        <foreach collection="objectIds" item="item" open="(" close=")" separator=",">
            (#{item},9)
        </foreach>
        AND BaseAttributeId = #{baseAttributeId}
    </select>
    <resultMap id="attributeResultMap" type="com.siteweb.capacity.entity.CapacityAttribute">
        <id column="AttributeId" property="attributeId"/>
        <result column="BaseTypeId" property="baseTypeId"/>
        <result column="BaseAttributeId" property="baseAttributeId"/>
        <result column="AttributeName" property="attributeName"/>
        <result column="LogicType" property="logicType" typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <result column="Description" property="description"/>
        <result column="ObjectId" property="objectId"/>
        <result column="ObjectTypeId" property="objectTypeId"/>
        <result column="ComplexIndex" property="complexIndex"/>
        <result column="MinValue" property="minValue"/>
        <result column="MaxValue" property="maxValue"/>
        <result column="RatedCapacity" property="ratedCapacity"/>
        <result column="DefaultValue" property="defaultValue"/>
        <result column="CompensateFactor" property="compensateFactor"/>
        <result column="OriginCapacity" property="originCapacity"/>
        <result column="UsedCapacity" property="usedCapacity"/>
        <result column="FreeCapacity" property="freeCapacity"/>
        <result column="Percent" property="percent"/>
        <result column="SampleTime" property="sampleTime"/>
        <result column="Unit" property="unit"/>
        <result column="Precision" property="precision"/>
    </resultMap>
    <select id="findAllAttribute" resultMap="attributeResultMap">
        SELECT capacity.AttributeId,
               capacity.BaseTypeId,
               capacity.BaseAttributeId,
               capacity.AttributeName,
               capacity.LogicType,
               capacity.Description,
               capacity.ObjectId,
               capacity.ObjectTypeId,
               capacity.ComplexIndex,
               capacity.MinValue,
               capacity.MaxValue,
               capacity.RatedCapacity,
               capacity.DefaultValue,
               capacity.CompensateFactor,
               capacity.OriginCapacity,
               capacity.UsedCapacity,
               capacity.FreeCapacity,
               capacity."Percent",
               capacity.SampleTime,
               capacity.Unit,
               capacity."Precision"
        FROM capacityattribute capacity
    </select>
</mapper>