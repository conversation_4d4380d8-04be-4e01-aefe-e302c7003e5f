<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.capacity.mapper.CapacityBaseAttributeMapper">
    <resultMap id="capacityBaseAttributeResultMap" type="com.siteweb.capacity.entity.CapacityBaseAttribute">
        <id column="BaseAttributeId" property="baseAttributeId"/>
        <result column="BaseAttributeName" property="baseAttributeName"/>
        <result column="BaseTypeId" property="baseTypeId"/>
        <result column="AttributeName" property="attributeName"/>
        <result column="LogicType" property="logicType" typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <result column="unit" property="unit"/>
        <result column="description" property="description"/>
    </resultMap>
    <select id="findAll" resultType="com.siteweb.capacity.entity.CapacityBaseAttribute" resultMap="capacityBaseAttributeResultMap">
        SELECT baseAttribute.BaseAttributeId,
               baseAttribute.BaseAttributeName,
               baseAttribute.BaseTypeId,
               baseAttribute.AttributeName,
               baseAttribute.LogicType,
               baseAttribute.unit,
               baseAttribute.description
        FROM CapacityBaseAttribute baseAttribute
    </select>
</mapper>