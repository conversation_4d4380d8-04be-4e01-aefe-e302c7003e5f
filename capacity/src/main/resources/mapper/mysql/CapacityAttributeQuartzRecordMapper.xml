<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.capacity.mapper.CapacityAttributeQuartzRecordMapper">
    <insert id="batchInsert">
        INSERT INTO capacityattributequartzrecord (baseattributeid, attributename, ObjectId,
        ObjectTypeId, RatedCapacity,usedcapacity,freecapacity,percent,CreateTime)
        VALUES
        <foreach collection="recordList" item="item" separator=",">
            (#{item.baseAttributeId},#{item.attributeName},#{item.objectId},
            #{item.objectTypeId},#{item.ratedCapacity},#{item.usedCapacity},#{item.freeCapacity},#{item.percent},now())
        </foreach>
    </insert>
    <select id="findSumUIndexRate" resultType="com.siteweb.utility.vo.NameValueVO">
        SELECT DATE_FORMAT(record.createtime, '%Y-%m-%d') as `name`, SUM(record.RatedCapacity) as `value`
        FROM capacityattributequartzrecord record
                 INNER JOIN computerrack rack ON record.ObjectId = rack.ComputerRackId AND record.ObjectTypeId = 9 AND
                                                 record.BaseAttributeId = 2000000
                 INNER JOIN resourcestructure resource ON rack.ResourceStructureId = resource.ResourceStructureId
        WHERE resource.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND
        <foreach collection="dateList" item="item" open="(" close=")" separator=" or ">
            record.CreateTime &gt;= #{item} and record.CreateTime &lt; adddate(#{item},interval 1 day)
        </foreach>
        GROUP BY DATE_FORMAT(record.createtime, '%Y-%m-%d')
    </select>
</mapper>