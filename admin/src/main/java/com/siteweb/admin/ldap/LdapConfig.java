package com.siteweb.admin.ldap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ldap.LdapProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import java.util.Collections;


@Configuration
@EnableConfigurationProperties(LdapProperties.class)
public class LdapConfig {

    @Autowired
    Environment environment;

    @Autowired
    LdapProperties ldapProperties;

    @Bean
    public LdapContextSource ldapContextSource() {
        LdapContextSource source = new LdapContextSource();
        source.setUserDn(ldapProperties.getUsername());
        source.setPassword(ldapProperties.getPassword());
        source.setBase(ldapProperties.getBase());
        source.setUrls(ldapProperties.determineUrls(environment));
        source.setPooled(true);
        source.setReferral("follow");
        source.setBaseEnvironmentProperties(Collections.unmodifiableMap(ldapProperties.getBaseEnvironment()));
        return source;
    }

    @Bean
    public LdapTemplate ldapTemplate() {
        return new LdapTemplate(ldapContextSource());
    }
}
