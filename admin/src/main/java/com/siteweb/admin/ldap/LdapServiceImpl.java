package com.siteweb.admin.ldap;

import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.admin.vo.AccountVO;
import com.siteweb.admin.vo.EmployeeVO;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.ldap.filter.OrFilter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class LdapServiceImpl implements LdapService {

    private final Logger log = LoggerFactory.getLogger(LdapServiceImpl.class);

    @Autowired
    LdapTemplate ldapTemplate;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    AccountService accountService;

    @Autowired
    UserRoleMapper userRoleMapper;

    @Autowired
    Environment environment;

    @Override
    public boolean adAuthenticate(String userName, String password) {
        OrFilter filter = new OrFilter();
        filter.or(new EqualsFilter("sAMAccountName", userName));
        filter.or(new EqualsFilter("uid", userName));
        filter.or(new EqualsFilter("cn", userName));
        String userBase = environment.getProperty("spring.ldap.userbase", "");
        return ldapTemplate.authenticate(userBase, filter.toString(), password);
    }

    @Transactional
    @Override
    public void createEmployeeAndAccountByAdCommonName(String adUserCommonName) {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("ldap.user.defaultDepartmentIdAndRoleId");
        if (systemConfig == null || systemConfig.getSystemConfigValue() == null || systemConfig.getSystemConfigValue().isEmpty()) {
            log.error("ldap.user.defaultDepartmentIdAndRoleId 未配置");
            return;
        }
        String[] tmpArray = systemConfig.getSystemConfigValue().split(",");
        if (tmpArray.length != 2) {
            log.error("ldap.user.defaultDepartmentIdAndRoleId 配置错误");
            return;
        }
        //新增人员
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setEmployeeName(adUserCommonName);
        employeeVO.setDepartmentId(Integer.parseInt(tmpArray[0]));
        employeeVO.setJobNumber(adUserCommonName);
        employeeVO.setEnable(true);
        int employeeId = employeeService.createEmployee(-1,employeeVO);
        if (employeeId < 0) {
            log.error("获取主键EmployeeId失败");
            return;
        }
        //新增账号
        AccountVO accountVO = new AccountVO();
        accountVO.setUserId(employeeId);
        accountVO.setUserName(adUserCommonName);
        accountVO.setLogonId(adUserCommonName);
        accountVO.setPassword("");
        accountVO.setMaxError(null);
        accountVO.setValidTime(null);
        accountVO.setPasswordValidTime(null);
        accountVO.setEnable(true);
        accountVO.setLocked(false);
        accountVO.setRemote(false);
        accountVO.setDescription(null);
        accountVO.setNeedResetPwd(false);
        accountService.createAccount(-1,accountVO);
        //设置人员角色
        userRoleMapper.createUserRoleMap(employeeId, Integer.parseInt(tmpArray[1]));
    }

}
