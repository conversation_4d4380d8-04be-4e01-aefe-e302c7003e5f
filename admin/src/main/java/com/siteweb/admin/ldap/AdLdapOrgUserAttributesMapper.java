package com.siteweb.admin.ldap;

import org.springframework.ldap.core.AttributesMapper;

import javax.naming.NamingException;
import javax.naming.directory.Attributes;

public class AdLdapOrgUserAttributesMapper implements AttributesMapper<AdLdapOrgUser> {
    @Override
    public AdLdapOrgUser mapFromAttributes(Attributes attributes) throws NamingException {
        AdLdapOrgUser adLdapOrgUser = new AdLdapOrgUser();
        adLdapOrgUser.setCommonName((String) attributes.get("sAMAccountName").get());
        adLdapOrgUser.setSuperName((String) attributes.get("distinguishedName").get());
        return adLdapOrgUser;
    }
}
