package com.siteweb.admin.parse;

import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;

import java.util.List;

public interface PermissionParse {
    /**
     * 能够处理的权限类型
     * @return {@link List}<{@link PermissionCategoryEnum}>
     */
    PermissionCategoryEnum permissionCategory();

    /**
     * 通过权限类型查找所有权限点
     *
     * @param categoryId      权限点类型
     * @param menuProfileId 菜单方案id
     * @return {@link Object}
     */
    List<Permission> findPermission(Integer menuProfileId, Integer categoryId);

    /**
     * 查找角色拥有的权限点
     *
     * @param roleId        角色Id
     * @param categoryId    类型id
     * @param menuProfileId
     * @return {@link List}<{@link Permission}>
     */
    List<RolePermissionMap> findRolePermission(Integer roleId, Integer categoryId, Integer menuProfileId);

    /**
     * 更新角色权限
     * @param updateRolePermissionDTO 更新角色权限dto
     * @return int 更新超过数量
     */
    int updateRolePermission(UpdateRolePermissionDTO updateRolePermissionDTO);
}
