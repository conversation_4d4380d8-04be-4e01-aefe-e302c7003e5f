package com.siteweb.admin.parse.impl;

import com.siteweb.admin.entity.MenuPermissionGroup;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.RolePermissionMapMapper;
import com.siteweb.admin.parse.PermissionParse;
import com.siteweb.admin.parse.PermissionParseAdapter;
import com.siteweb.admin.service.MenuPermissionGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 菜单权限
 *
 * <AUTHOR>
 * @date 2023/03/08
 */
@Component
public class MenuParse extends PermissionParseAdapter implements PermissionParse {
    @Autowired
    RolePermissionMapMapper rolePermissionMapMapper;
    @Autowired
    MenuPermissionGroupService menuPermissionGroupService;

    @Override
    public PermissionCategoryEnum permissionCategory() {
        return PermissionCategoryEnum.MENU;
    }

    @Override
    public List<Permission> findPermission(Integer menuProfileId, Integer categoryId) {
        List<MenuPermissionGroup> menuGroupList = menuPermissionGroupService.findAll();
        return menuGroupList.stream()
                            .map(group -> {
                                Permission permission = new Permission();
                                permission.setName(group.getMenuPermissionGroupName());
                                permission.setPermissionId(group.getMenuPermissionGroupId());
                                permission.setCategory(PermissionCategoryEnum.MENU.getPermissionCategoryId());
                                permission.setDescription(group.getDescription());
                                return permission;
                            })
                            .toList();
    }
}
