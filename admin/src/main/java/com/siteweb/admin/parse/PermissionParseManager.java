package com.siteweb.admin.parse;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@Component
public class PermissionParseManager {
    private final HashMap<Integer, PermissionParse> resourceObjectServiceHashMap = new HashMap<>();
    @Autowired
    private List<PermissionParse> permissionParseList;

    @PostConstruct
    public void init(){
        for (PermissionParse permissionParse : permissionParseList) {
            PermissionCategoryEnum permissionCategoryEnum = permissionParse.permissionCategory();
            PermissionParse parse = resourceObjectServiceHashMap.get(permissionCategoryEnum.getPermissionCategoryId());
            if (ObjectUtil.isNotNull(parse)) {
                String error = String.format("检测到对象类型[%s]重复映射：%s", permissionCategoryEnum.getPermissionCategoryName(), parse.getClass().getName());
                throw new IllegalArgumentException(error);
            }
            resourceObjectServiceHashMap.put(permissionCategoryEnum.getPermissionCategoryId(), permissionParse);
        }
    }

    /**
     * 通过权限类型id获取权限点
     * @param menuProfileId 菜单方案id(仅只有菜单权限需要使用到)
     * @param categoryId 权限类型id
     * @return {@link List}<{@link Permission}>
     */
    public List<Permission> findByPermissionCategory(Integer menuProfileId, Integer categoryId){
        PermissionParse permissionParse = resourceObjectServiceHashMap.get(categoryId);
        if (ObjectUtil.isNull(permissionParse)) {
            return Collections.emptyList();
        }
        return permissionParse.findPermission(menuProfileId, categoryId);
    }

    /**
     * 获取角色权限id
     *
     * @param roleId        角色id
     * @param categoryId    权限类型id
     * @param menuProfileId
     * @return {@link List}<{@link RolePermissionMap}>
     */
    public List<RolePermissionMap> findRolePermission(Integer roleId, Integer categoryId, Integer menuProfileId){
        PermissionParse permissionParse = resourceObjectServiceHashMap.get(categoryId);
        if (ObjectUtil.isNull(permissionParse)) {
            return Collections.emptyList();
        }
        return permissionParse.findRolePermission(roleId, categoryId,menuProfileId);
    }

    /**
     * 更新角色权限
     * @param updateRolePermissionDTO
     * @return int 受影响的行数
     */
    public int updateRolePermission(UpdateRolePermissionDTO updateRolePermissionDTO){
        PermissionParse permissionParse = resourceObjectServiceHashMap.get(updateRolePermissionDTO.getCategoryId());
        if (ObjectUtil.isNull(permissionParse)) {
            return 0;
        }
        return permissionParse.updateRolePermission(updateRolePermissionDTO);
    }
}
