package com.siteweb.admin.parse.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.PermissionMapper;
import com.siteweb.admin.parse.PermissionParse;
import com.siteweb.admin.parse.PermissionParseAdapter;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.vo.UserRoleRightVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 专业权限
 * <AUTHOR>
 * @date 2023/03/08
 */
@Component
public class MajorParse extends PermissionParseAdapter implements PermissionParse {
    @Autowired
    PermissionMapper permissionMapper;
    @Autowired
    RolePermissionMapService rolePermissionMapService;
    @Override
    public PermissionCategoryEnum permissionCategory() {
        return PermissionCategoryEnum.MAJOR;
    }

    @Override
    public List<Permission> findPermission(Integer menuProfileId, Integer categoryId) {
        return permissionMapper.getAllSpecialtyGroupPermission();
    }

    @Override
    public List<RolePermissionMap> findRolePermission(Integer roleId, Integer categoryId, Integer menuProfileId) {
        return rolePermissionMapService.getSpecialtyPermissionMaps(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRolePermission(UpdateRolePermissionDTO updateRolePermissionDTO) {
        rolePermissionMapService.delUserRoleRightByRoleIdAndOperationType(updateRolePermissionDTO.getRoleId(), 3);
        if (CollUtil.isEmpty(updateRolePermissionDTO.getRolePermissionMapList())) {
            return 0;
        }
        List<UserRoleRightVO> userRoleRightVOList = new ArrayList<>();
        for (RolePermissionMap rpm : updateRolePermissionDTO.getRolePermissionMapList()) {
            UserRoleRightVO userRoleRightVO = new UserRoleRightVO();
            userRoleRightVO.setRoleId(updateRolePermissionDTO.getRoleId());
            userRoleRightVO.setOperationId(rpm.getPermissionId());
            userRoleRightVO.setOperationType(3);
            userRoleRightVOList.add(userRoleRightVO);
        }
        rolePermissionMapService.batchCreateUserRoleRight(userRoleRightVOList);
        return updateRolePermissionDTO.getRolePermissionMapList().size();
    }
}
