package com.siteweb.admin.parse;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.service.PermissionService;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.service.SceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public abstract class PermissionParseAdapter implements PermissionParse {
    @Autowired
    PermissionService permissionService;
    @Autowired
    RolePermissionMapService rolePermissionMapService;
    @Autowired
    SceneService sceneService;

    @Override
    public List<Permission> findPermission(Integer menuProfileId, Integer categoryId) {
        return permissionService.findCurrentScenePermissionByCategory(categoryId);
    }

    @Override
    public List<RolePermissionMap> findRolePermission(Integer roleId, Integer categoryId, Integer menuProfileId) {
        return rolePermissionMapService.findByRoleIdsAndCategoryId(List.of(roleId), categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRolePermission(UpdateRolePermissionDTO updateRolePermissionDTO) {
        //删除旧权限
        rolePermissionMapService.deleteByRoleIdAndCategoryId(updateRolePermissionDTO.getRoleId(), updateRolePermissionDTO.getCategoryId());
        if (CollUtil.isEmpty(updateRolePermissionDTO.getRolePermissionMapList())) {
            return 0;
        }
        //添加新权限
        return rolePermissionMapService.batchCreateRolePermissionMap(updateRolePermissionDTO.getRolePermissionMapList());
    }
}
