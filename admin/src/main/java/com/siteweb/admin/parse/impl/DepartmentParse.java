package com.siteweb.admin.parse.impl;

import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.parse.PermissionParse;
import com.siteweb.admin.parse.PermissionParseAdapter;
import com.siteweb.admin.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 部门权限
 * <AUTHOR>
 * @date 2023/03/08
 */
@Component
public class DepartmentParse extends PermissionParseAdapter implements PermissionParse {
    @Autowired
    private DepartmentService departmentService;
    @Override
    public PermissionCategoryEnum permissionCategory() {
        return PermissionCategoryEnum.DEPARTMENT;
    }

    @Override
    public List<Permission> findPermission(Integer menuProfileId, Integer categoryId) {
        return departmentService.findAll().stream().map(Permission::new).toList();
    }
}
