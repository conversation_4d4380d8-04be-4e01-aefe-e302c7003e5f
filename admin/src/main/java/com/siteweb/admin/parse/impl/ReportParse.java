package com.siteweb.admin.parse.impl;

import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.parse.PermissionParse;
import com.siteweb.admin.parse.PermissionParseAdapter;
import org.springframework.stereotype.Component;

/**
 * 报表权限
 * <AUTHOR>
 * @date 2023/03/08
 */
@Component
public class ReportParse extends PermissionParseAdapter implements PermissionParse {
    @Override
    public PermissionCategoryEnum permissionCategory() {
        return PermissionCategoryEnum.REPORT;
    }
}
