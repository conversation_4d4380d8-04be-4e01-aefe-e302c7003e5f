package com.siteweb.admin.parse.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.entity.OperationGroup;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.parse.PermissionParse;
import com.siteweb.admin.parse.PermissionParseAdapter;
import com.siteweb.admin.service.OperationGroupService;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.vo.UserRoleRightVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户操作权限
 *
 * <AUTHOR>
 * @date 2023/03/08
 */
@Component
public class OperationParse extends PermissionParseAdapter implements PermissionParse {
    @Autowired
    RolePermissionMapService rolePermissionMapService;
    @Autowired
    OperationGroupService operationGroupService;
    @Override
    public PermissionCategoryEnum permissionCategory() {
        return PermissionCategoryEnum.OPERATION;
    }

    @Override
    public List<Permission> findPermission(Integer menuProfileId, Integer categoryId) {
        List<OperationGroup> operationGroupList = operationGroupService.findAll();
        return operationGroupList.stream()
                                 .map(group -> {
                                     Permission permission = new Permission();
                                     permission.setName(group.getGroupName());
                                     permission.setPermissionId(group.getGroupId());
                                     permission.setCategory(PermissionCategoryEnum.OPERATION.getPermissionCategoryId());
                                     permission.setDescription(group.getDescription());
                                     return permission;
                                 })
                                 .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRolePermission(UpdateRolePermissionDTO updateRolePermissionDTO) {
        Integer roleId = updateRolePermissionDTO.getRoleId();
        //根据角色id和操作类型id删除UserRoleRight
        rolePermissionMapService.delUserRoleRightByRoleIdAndOperationType(roleId, 1);
        //根据角色id删除OperationGroupMap
        rolePermissionMapService.deleteByRoleIdAndCategoryId(updateRolePermissionDTO.getRoleId(), updateRolePermissionDTO.getCategoryId());
        if (CollUtil.isEmpty(updateRolePermissionDTO.getRolePermissionMapList())) {
            return 0;
        }
        //同步UserRoleRight
        List<UserRoleRightVO> userRoleRightVOList = new ArrayList<>(updateRolePermissionDTO.getRolePermissionMapList().size());
        for (RolePermissionMap rolePermissionMap : updateRolePermissionDTO.getRolePermissionMapList()) {
            UserRoleRightVO userRoleRightVO = new UserRoleRightVO();
            userRoleRightVO.setRoleId(roleId);
            userRoleRightVO.setOperationId(rolePermissionMap.getPermissionId());
            userRoleRightVO.setOperationType(1);
            userRoleRightVOList.add(userRoleRightVO);
        }
        rolePermissionMapService.batchCreateUserRoleRight(userRoleRightVOList);
        return rolePermissionMapService.batchCreateRolePermissionMap(updateRolePermissionDTO.getRolePermissionMapList());
    }
}
