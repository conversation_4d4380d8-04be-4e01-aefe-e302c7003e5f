package com.siteweb.admin.parse.impl;

import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.parse.PermissionParse;
import com.siteweb.admin.parse.PermissionParseAdapter;
import com.siteweb.admin.service.RegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 区域权限
 * <AUTHOR>
 * @date 2023/03/08
 */
@Component
public class RegionParse extends PermissionParseAdapter implements PermissionParse {
    @Autowired
    RegionService regionService;
    @Override
    public PermissionCategoryEnum permissionCategory() {
        return PermissionCategoryEnum.REGION;
    }

    @Override
    public List<Permission> findPermission(Integer menuProfileId, Integer categoryId) {
        return regionService.findAllRegions().stream().map(Permission::new).toList();
    }
}
