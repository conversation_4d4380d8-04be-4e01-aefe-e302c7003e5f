package com.siteweb.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 路由跳转DTO
 * <AUTHOR>
 * @date 2024/10/25
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RouterRedirectDTO {
    public RouterRedirectDTO(Boolean isEmbed, Boolean isExternalWeb) {
        this.isEmbed = isEmbed;
        this.isExternalWeb = isExternalWeb;
    }

    /**
     * 跳转路径
     */
    private String path;
    /**
     * 是否内嵌
     */
    private Boolean isEmbed;
    /**
     * 是否外部链接
     */
    private Boolean isExternalWeb;
}
