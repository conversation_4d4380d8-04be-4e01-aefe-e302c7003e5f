package com.siteweb.admin.dto;


import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.siteweb.admin.entity.Employee;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
public class ExportEmployeeDTO {

    private String employeeName;

    private String gender;

    private String mobile;

    private String employeeTitle;

    private String jobNumber;

    private String phone;

    private String email;

    private String address;

    private String postAddress;

    private String alias;


    public ExportEmployeeDTO(Employee employee, boolean isHidden, LocaleMessageSourceUtil messageSourceUtil) {
        if (isHidden) {
            this.employeeName = DesensitizedUtil.chineseName(employee.getEmployeeName());
            this.mobile = DesensitizedUtil.mobilePhone(employee.getMobile());
            this.phone = DesensitizedUtil.mobilePhone(employee.getPhone());
            this.email = DesensitizedUtil.email(employee.getEmail());
        } else {
            this.employeeName = employee.getEmployeeName();
            this.mobile = employee.getMobile();
            this.phone = employee.getPhone();
            this.email = employee.getEmail();
        }
        this.gender = getGender(employee.getGender(),messageSourceUtil);
        this.alias = employee.getAlias();
        this.address = employee.getAddress();
        this.postAddress = employee.getPostAddress();
        this.employeeTitle = getEmployeeTitle(employee.getEmployeeTitle(),messageSourceUtil);
        this.jobNumber = employee.getJobNumber();
    }
    private String getGender(Integer gender,LocaleMessageSourceUtil messageSourceUtil){
        if (Objects.isNull(gender)) {
            return messageSourceUtil.getMessage("common.man");
        }
        return Objects.equals(gender,1) ? messageSourceUtil.getMessage("common.man") : messageSourceUtil.getMessage("common.woman");
    }
    private String getEmployeeTitle(Integer employeeTitle,LocaleMessageSourceUtil messageSourceUtil){
        if (Objects.isNull(employeeTitle)) {
            return CharSequenceUtil.EMPTY;
        }
        return Objects.equals(employeeTitle,0) ? messageSourceUtil.getMessage("common.generalStaff") : messageSourceUtil.getMessage("common.lead");
    }
}
