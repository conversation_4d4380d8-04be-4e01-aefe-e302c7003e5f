package com.siteweb.admin.dto;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@Data
public class CQCTCCAccountSyncResponse {

    /**
     * 响应流水，与请求流水保持一致
     */
    @JsonProperty("transaction_id")
    public String transactionId;

    /**
     * 响应时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("response_time")
    public String responseTime;

    /**
     * 响应编码 0000表示成功，1001 表示 人主账号不存在，其他响应编码均表示失败
     */
    @JsonProperty("rsp_code")
    public String rspCode;

    /**
     * 响应描述 成功 失败
     */
    @JsonProperty("rsp_desc")
    public String rspDesc;

    public static ResponseEntity<CQCTCCAccountSyncResponse> successful(String transactionId) {
        CQCTCCAccountSyncResponse result = new CQCTCCAccountSyncResponse();
        result.setTransactionId(transactionId);
        result.setResponseTime(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        result.setRspCode("0000");
        result.setRspDesc("成功");
        return new ResponseEntity(result, HttpStatus.OK);
    }

    public static ResponseEntity<CQCTCCAccountSyncResponse> successful(String transactionId, String rspCode, String rspDesc) {
        CQCTCCAccountSyncResponse result = new CQCTCCAccountSyncResponse();
        result.setTransactionId(transactionId);
        result.setResponseTime(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        result.setRspCode(rspCode);
        result.setRspDesc(rspDesc);
        return new ResponseEntity(result, HttpStatus.OK);
    }

    public static ResponseEntity<CQCTCCAccountSyncResponse> failedRequest(String transactionId, String rspCode, String rspDesc) {
        CQCTCCAccountSyncResponse result = new CQCTCCAccountSyncResponse();
        result.setTransactionId(transactionId);
        result.setResponseTime(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        result.setRspCode(rspCode);
        result.setRspDesc(rspDesc);
        return new ResponseEntity(result, HttpStatus.BAD_REQUEST);
    }

    public static ResponseEntity<CQCTCCAccountSyncResponse> failedResponse(String transactionId) {
        CQCTCCAccountSyncResponse result = new CQCTCCAccountSyncResponse();
        result.setTransactionId(transactionId);
        result.setResponseTime(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        result.setRspCode("2001");
        result.setRspDesc("账号处理失败");
        return new ResponseEntity(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }


}
