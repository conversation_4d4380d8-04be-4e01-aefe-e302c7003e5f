package com.siteweb.admin.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SecurityReportParamDto {

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 类别
     */
    private List<String> typeList;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 描述
     */
    private String details;
    /**
     * 查询最大数量
     */
    private Integer maxCount;
}
