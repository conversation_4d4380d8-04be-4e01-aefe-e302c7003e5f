package com.siteweb.admin.dto;


import com.siteweb.admin.entity.Employee;
import com.siteweb.common.enums.SensitiveType;
import com.siteweb.common.sensitive.jackson.SensitiveInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description EmployeeDTO
 * @createTime 2022-07-15 08:34:23
 */
@Data
@NoArgsConstructor
public class EmployeeDTO {

    private Integer employeeId;

    private Integer departmentId;
    @SensitiveInfo(value = SensitiveType.CHINESE_NAME)
    private String employeeName;

    private Integer employeeType;

    private Integer employeeTitle;

    private String jobNumber;

    private Integer gender;

    @SensitiveInfo(value = SensitiveType.MOBILE_PHONE)
    private String mobile;

    @SensitiveInfo(value = SensitiveType.FIXED_PHONE)
    private String phone;

    @SensitiveInfo(value = SensitiveType.EMAIL)
    private String email;

    @SensitiveInfo(value = SensitiveType.ADDRESS)
    private String address;

    @SensitiveInfo(value = SensitiveType.ADDRESS)
    private String postAddress;

    private Boolean enable;

    private String description;

    private Boolean addTempUser;

    private Integer userValidTime;

    private String alias;

    public EmployeeDTO(Employee employee) {
        BeanUtils.copyProperties(employee, this);
    }

}
