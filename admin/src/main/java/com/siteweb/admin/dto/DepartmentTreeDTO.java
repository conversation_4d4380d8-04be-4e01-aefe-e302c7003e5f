package com.siteweb.admin.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/03/01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentTreeDTO {


    private Integer departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门等级
     */
    private String departmentLevel;

    /**
     * 部门职能
     */
    private String departmentFunction;

    /**
     * 父部门Id
     */
    private Integer parentDeprtId;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 修改时间
     */
    private Date lastUpdateDate;

    /**
     * 是否拥有权限
     */

    private boolean permissionFlag;


    /**
     * 子节点
     */
    private List<DepartmentTreeDTO> children;

    /**
     * 权限id = 部门id
     * @return {@link Integer}
     */
    public Integer getPermissionId(){
        return this.departmentId;
    }
}
