package com.siteweb.admin.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class MenuStructureDTO extends MenuLanguageBase<MenuStructureDTO>{

	private Integer menuStructureId;

	private Integer menuProfileId;

	private Integer parentId;

	private String path;

	private String title;

	private String alias;

	private String icon;

	private Boolean selected;

	private Boolean expanded;

	private String pathMatch;

	private Integer layoutPosition;

	private Boolean isSystemConfig;

	private Boolean isExternalWeb;

	private Boolean isEmbed;

	private Boolean menuHasNavigation;

	private Boolean hidden;

	private Integer sortIndex;

	private Boolean isSystem;

	private String description;

	private Integer menuItemId;

	private String uuid;

	private Boolean readOnly;

	private Integer menuItemStructureMapId;

	private List<MenuStructureDTO> children;
}
