package com.siteweb.admin.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/3/19 13:08
 * @description:
 **/
@Data
@NoArgsConstructor
public class SpecialtyGroupMapDTO {

    @ApiModelProperty(value = "主键ID", name = "id")
    private Integer id;

    @ApiModelProperty(value = "专业权限组ID", name = "specialtyGroupId", required = true)
    private Integer specialtyGroupId;

    @ApiModelProperty(value = "专业项id", name = "entryItemId", required = true)
    private Integer entryItemId;

    @ApiModelProperty(value = "操作名", name = "operation")
    private String operation;

    @ApiModelProperty(value = "专业项名称", name = "itemValue")
    private String itemValue;

    @ApiModelProperty(value = "专业项描述", name = "description")
    private String description;

    @ApiModelProperty(value = "设备种类", name = "entryName")
    private String entryName;
}
