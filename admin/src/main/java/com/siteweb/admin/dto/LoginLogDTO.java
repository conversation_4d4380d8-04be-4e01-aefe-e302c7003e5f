package com.siteweb.admin.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LoginLogDTO
 * @createTime 2022-05-11 10:37:08
 */
@Data
@NoArgsConstructor
public class LoginLogDTO {

    /**
     * 主键ID
     */
    private Integer loginLogId;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 操作时间
     */
    private Date operatingTime;
    /**
     * 操作类型
     */
    private String operatingType;
    /**
     * 客户端类型
     */
    private String clientType;
    /**
     * 客户端IP
     */
    private String clientIp;
}
