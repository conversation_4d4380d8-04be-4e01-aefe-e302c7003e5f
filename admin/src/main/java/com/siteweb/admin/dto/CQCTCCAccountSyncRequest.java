package com.siteweb.admin.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class CQCTCCAccountSyncRequest {

    /**
     * 请求流水，客户端保证每次请求都唯一。
     *  26位字符串（6位目标系统编码+14位时间戳（yyyyMMddHHmmss）+6位序列值）
     */
    @JsonProperty("transaction_id")
    public String transactionId;

    /**
     * 统一认证账号
     */
    @JsonProperty("main_account_code")
    public String mainAccountCode;

    /**
     *  身份证
     * 操作编码为A: 使用DES加密算法加密传输(不用密钥加密)，
     * 不需要身份证的目标系统，身份证信息被屏蔽，传送数据为一串****************
     * 2、操作编码为M、D：身份证信息被屏蔽，传送数据为一串****************
     */
    @JsonProperty("certi_number")
    public String certiNumber;

    /**
     * 手机号码
     * 使用DES加密算法加密传输(不用密钥加密)
     */
    @JsonProperty("phone_number")
    public String phoneNumber;
    /**
     * 姓名
     * 使用DES加密算法加密传输(不用密钥加密)
     */
    @JsonProperty("user_name")
    public String userName;
    /**
     * 所属组织信息
     */
    @JsonProperty("org_info")
    public List<CACTCCOrgInfo> orgInfo;
    /**
     * 操作编码
     * A：新增 (个别系统才会同步该操作)，M：修改  D：注销
     */
    @JsonProperty("action_code")
    public String actionCode;
    /**
     * 请求时间,格式：yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("request_time")
    public String requestTime;
    /**
     * 岗位id
     */
    @JsonProperty("post_id")
    public String postId;
    /**
     * 岗位名称
     */
    @JsonProperty("post_name")
    public String postName;
    /**
     * 岗位类型名称
     */
    @JsonProperty("post_type_name")
    public String postTypeName;
    /**
     * 岗位类型id
     */
    @JsonProperty("post_type_id")
    public String postTypeId;
    /**
     * 备注1
     */
    @JsonProperty("remark1")
    public String remark1;
    /**
     * 备注2
     */
    @JsonProperty("remark2")
    public String remark2;
    /**
     * 备注3
     */
    @JsonProperty("remark3")
    public String remark3;
    /**
     * 数字签名
     * 将请求报文中除sign节点以外的信息通过DES加密生成数字签名，加密密钥：475409c698a44585b7c19fd7294f5444
     */
    @JsonProperty("sign")
    public String sign;

    @Data
    public static class CACTCCOrgInfo{
        /**
         * 末级组织id
         */
        @JsonProperty("org_id")
        public String orgId;
        /**
         * 末级组织名称
         */
        @JsonProperty("org_name")
        public String orgName;
        /**
         * 单位组织id
         */
        @JsonProperty("company_id")
        public String companyId;
        /**
         * 单位组织名称
         */
        @JsonProperty("company_name")
        public String companyName;
        /**
         * 是否主职 T：主职，F：兼职
         */
        @JsonProperty("is_main")
        public String isMain;
    }
}
