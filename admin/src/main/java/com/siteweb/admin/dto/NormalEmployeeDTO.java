package com.siteweb.admin.dto;


import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description EmployeeDTO
 * @createTime 2022-07-15 08:34:23
 */
@Data
@NoArgsConstructor
public class NormalEmployeeDTO {

    private Integer employeeId;

    private Integer departmentId;
    private String employeeName;

    private Integer employeeType;

    private Integer employeeTitle;

    private String jobNumber;

    private Integer gender;

    private String mobile;

    private String phone;

    private String email;

    private String address;

    private String postAddress;

    private boolean enable;

    private String description;

    private boolean addTempUser;

    private Integer userValidTime;

    private String alias;
}
