package com.siteweb.admin.dto;

import com.siteweb.admin.entity.RolePermissionMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 更新角色权限dto
 *
 * <AUTHOR>
 * @date 2023/03/08
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UpdateRolePermissionDTO {
    /**
     * 角色id
     */
    private Integer roleId;
    /**
     * 权限类型
     */
    private Integer categoryId;
    /**
     * 菜单方案id
     */
    private Integer menuProfileId;
    /**
     * 角色权限集合
     */
    private List<RolePermissionMap> rolePermissionMapList;
}
