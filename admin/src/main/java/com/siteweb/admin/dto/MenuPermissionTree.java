package com.siteweb.admin.dto;

import com.siteweb.admin.entity.Permission;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/02/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MenuPermissionTree{
    public MenuPermissionTree(Permission permission) {
        this.caption = permission.getCaption();
        this.name = permission.getName();
        this.category = permission.getCategory();
        this.description = permission.getDescription();
    }

    public MenuPermissionTree(String name, String id, String parentId) {
        this.name = name;
        this.id = id;
        this.parentId = parentId;
    }

    /**
     * 权限id
     */
    private Integer permissionId;
    /**
     * 名称
     */
    private String name;

    /**
     * 名称
     */
    private String id;

    private String parentId;

    /**
     * 类型
     */
    private Integer category;
    /**
     * 类型
     */
    private String caption;

    /**
     * 描述
     */
    private String description;
    /**
     * 子集
     */
    private List<MenuPermissionTree> children = new ArrayList<>();


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        MenuPermissionTree that = (MenuPermissionTree) o;

        if (!id.equals(that.id)) {
            return false;
        }
        return parentId.equals(that.parentId);
    }

    @Override
    public int hashCode() {
        int result = id.hashCode();
        result = 31 * result + parentId.hashCode();
        return result;
    }
}
