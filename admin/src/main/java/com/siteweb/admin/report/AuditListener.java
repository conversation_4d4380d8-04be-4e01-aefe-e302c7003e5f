package com.siteweb.admin.report;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.utility.vo.AuditReportVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class AuditListener implements ApplicationListener<BaseSpringEvent<AuditReportVo>> {
    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    TokenUserUtil tokenUserUtil;
    @Override
    public void onApplicationEvent(BaseSpringEvent<AuditReportVo> event) {
        AuditReportVo data = event.getData();
        Authentication authentication = data.getAuthentication();
        String operationAccount = "";
        if (ObjectUtil.isNotNull(authentication)) {
            Object details = authentication.getDetails();
            if (details instanceof TokenUser tokenUser) {
                 operationAccount = tokenUser.getUsername();
            }
        }else{
            //获取不到操作人默认admin
            operationAccount = "admin";
        }
        securityAuditManager.recordAuditReport(operationAccount, data.getLevel(), data.getType(), event.getData().getDetails(), data.getClientIp(),null);
    }
}
