package com.siteweb.admin.report;

import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.entity.SecurityReport;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SmsSecurityService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SecuritySmsListener implements ApplicationListener<BaseSpringEvent<SecurityReport>> {

    @Autowired
    SmsSecurityService smsSecurityService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    SystemConfigService systemConfigService;

    @Override
    public void onApplicationEvent(BaseSpringEvent<SecurityReport> event) {
        SecurityReport SecurityReport = event.getData();
        if (SecurityReport == null) {
            return;
        }
        SystemConfig smsenable = systemConfigService.findBySystemConfigKey("report.securityreport.sms.enable");
        if (smsenable == null || smsenable.getSystemConfigValue().trim().isEmpty() || "false".equals(smsenable.getSystemConfigValue())) {
            return;
        }
        log.info(SecurityReport.getDetails());
        Employee admin = employeeService.findByEmployeeId(-1);
        smsSecurityService.sendSmsSecurity(admin.getMobile(), SecurityReport.getDetails());
    }
}
