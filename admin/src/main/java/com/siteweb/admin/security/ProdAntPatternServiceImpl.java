package com.siteweb.admin.security;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProdAntPatternServiceImpl implements AntPatternService {
    private final AntPatternProperties antPatternProperties;

    public ProdAntPatternServiceImpl(AntPatternProperties antPatternProperties) {
        this.antPatternProperties = antPatternProperties;
    }

    @Override
    public String[] getAntPatterns() {
        List<String> patterns = antPatternProperties.getExcludePaths();
        return patterns != null ? patterns.toArray(new String[0]) : new String[]{"/", "/resources/**", "/static/**", "/public/**", "/webapp/**"
                , "/h2-console/*", "/configuration/**", "/swagger-ui/**", "/swagger-resources/**", "/api-docs"
                , "/api-docs/**", "/v2/api-docs/**", "/**/*.html", "/**/*.css", "/**/*.js", "/**/*.png"
                , "/**/*.jpg", "/**/*.gif", "/**/*.svg", "/**/*.ttf", "/**/*.woff", "/assets/**", "/**/*.eot"
                , "/**/*.woff2", "/**/*.ico", "/fontawesome-webfont.*", "/ionicons.*", "/roboto-v15-latin-regular.*"
                , "/websocket/**", "/power.distribution/**", "/**/coreshadows/**", "/api/monitorunit/register"
                , "/api/serverstatus", "/api/timestamp", "/api/reportquerys/**", "/api/external/login", "/api/files/**"
                , "/api/simple/login", "/api/sso/login", "/api/keycloak/login","/api/codesso/login",
                "/files/**", "/**/health/**", "/**/info/**", "/**/covs/**", "/**/covacks/**", "/**/linkstates/**"
                , "/**/trace/**", "/**/heapdump/**", "/**/loggers/**", "/**/liquibase/**", "/**/logfile/**"
                , "/**/flyway/**", "/**/auditevents/**", "/**/metrics/**", "/**/jolokia/**", "/**/admin/**",
                "/**/togglz-console/**", "/api/captcha/**", "/api/sendsmscode", "/api/checksmscode", "/api/keycloak/login"
                , "/api/dimensionconfigures/**", "/api/files/dimension/**", "/api/dimensionmodels", "/api/checklicense", "/api/license","/api/webclientlog"
                ,"/api/energy/energyoverviewresourcestructurepuetimetype","/api/energy/energyapi/**","/api/foura/**","/api/systemconfig/tokenheader"
                ,"/api/systemconfig/accountterminaldevicebindenable","/api/accountterminaldevicemap","/api/accountterminaldevicemap/bind", "/api/uicore/*"
                ,"/api/cqctcc/login","/api/cqctccapp/getUserCallback","/api/v1/sync/bizOrder/accountSync","/api/accounts/forgetpassword"
                , "/api/accounts/passwordinvalidtime"};
    }
}
