package com.siteweb.admin.security;


import cn.hutool.core.util.StrUtil;
import com.siteweb.admin.ldap.LdapService;
import com.siteweb.admin.service.AccountPasswordErrRecordService;
import com.siteweb.common.util.EncryptUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SmsCodeService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.Collection;


@Component
public class UserAuthenticationManager implements AuthenticationManager {


    @Autowired
    TokenUserDetailsService tokenUserDetailService;

    @Autowired
    LdapService ldapService;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    AccountPasswordErrRecordService accountPasswordErrRecordService;

    @Autowired
    SmsCodeService smsCodeService;

    @Override
    public Authentication authenticate(Authentication authentication) {
        if (authentication == null) {
            throw new BadCredentialsException("user info abnormal");
        }
        String username = authentication.getName();
        String password = (String) authentication.getCredentials();
        String smsCode = "";
        if (password.split(":").length >= 2) {
            // 判断是否存在短信验证码，传入进来的形式是 passwrod:smscode 进行截取
            String[] split = password.split(":");
            password = split[0];
            smsCode = split[1];
        }
        TokenUser user = tokenUserDetailService.loadUserByUsername(username);
        if (user == null) {
            // 如果查询不出用户，将username当作手机号查询
            user = tokenUserDetailService.loadUserByMobile(username);
        }
        boolean adAuthenticateEnable = enableAdAuthenticate();
        if (adAuthenticateEnable) {
            boolean adAuthenticateResult = ldapService.adAuthenticate(username, password);
            if (adAuthenticateResult) {
                //AD认证成功
                if (user == null) {
                    //创建用户
                    ldapService.createEmployeeAndAccountByAdCommonName(username);
                    user = tokenUserDetailService.loadUserByUsername(username);
                }
                Collection<? extends GrantedAuthority> authorities = user.getAuthorities();
                return new UsernamePasswordAuthenticationToken(user, password, authorities);
            }
        }
        if (user == null) {
            throw new BadCredentialsException("Username not found.");
        }
        String appJsonValue = systemConfigService.findAppJsonValue("login.smsCode.enable");
        if ("true".equalsIgnoreCase(appJsonValue) && StrUtil.isBlank(smsCode)) {
            // 开启短信验证码，但是没有传入短信验证码
            throw new BadCredentialsException("smscode error");
        }
        if (StrUtil.isNotBlank(smsCode) && smsCodeService.checkSHA256SmsCode(username, smsCode) == -1) {
            throw new BadCredentialsException("smscode error");
        }
        if (!password.equals(EncryptUtil.changePasswordFormat(user.getPassword()))) {
            // 记录密码错误记录
            accountPasswordErrRecordService.passwordErrAction(user.getUserId());
            throw new BadCredentialsException("Wrong password");
        }
        Collection<? extends GrantedAuthority> authorities = user.getAuthorities();
        return new UsernamePasswordAuthenticationToken(user, password, authorities);
    }

    private boolean enableAdAuthenticate() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("ad.authenticate.enable");
        return (systemConfig != null && systemConfig.getSystemConfigValue() != null && "true".equalsIgnoreCase(systemConfig.getSystemConfigValue().trim()));
    }
}
