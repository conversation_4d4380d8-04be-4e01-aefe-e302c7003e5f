package com.siteweb.admin.security;

import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.IpUtil;
import com.siteweb.utility.service.SystemConfigService;
import io.jsonwebtoken.JwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class AccountHeartbeatFilter extends GenericFilterBean {
    private final SecurityProperties securityProperties;
    private final AccountService accountService;
    private final SystemConfigService systemConfigService;
    private final TokenUtil tokenUtil;

    public AccountHeartbeatFilter(SecurityProperties securityProperties, AccountService accountService, TokenUtil tokenUtil,SystemConfigService systemConfigService) {
        this.securityProperties = securityProperties;
        this.accountService = accountService;
        this.tokenUtil = tokenUtil;
        this.systemConfigService = systemConfigService;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        if (Objects.equals(securityProperties.getAccountHeartbeat(),false)) {
            chain.doFilter(request, response);
            return;
        }
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String tokenStr = httpRequest.getHeader(systemConfigService.findTokenHeader());
        if (tokenStr != null && !tokenStr.isEmpty()) {
            // 拿到token 获取到用户的信息
            String token = tokenStr.replace("Bearer", "").trim();
            String ipAddr = IpUtil.getIpAddr(httpRequest);
            Integer userId = getUserId(httpRequest);
            if (userId != null) {
                // token 作为主键<UserHeartBeat:token,userinfo>
                accountService.accountHeartbeat(token, userId, ipAddr);
            }
        }
        chain.doFilter(request, response);
    }

    private Integer getUserId(HttpServletRequest httpRequest) {
        try {
            Map<Integer, Authentication> integerAuthenticationMap = tokenUtil.verifyToken(httpRequest);
            if (integerAuthenticationMap.containsKey(1)) {
                Authentication authentication = integerAuthenticationMap.get(1);
                TokenUser details = (TokenUser) authentication.getDetails();
                return details.getUserId();
            }
        } catch (JwtException e) {
            log.warn("AccountHeartbeat JwtException:" + e.getMessage());
        }
        return null;
    }
}
