package com.siteweb.admin.security;

import com.siteweb.admin.dto.AccountDTO;
import org.springframework.security.core.authority.AuthorityUtils;


public class TokenUser extends org.springframework.security.core.userdetails.User {
    private AccountDTO user;

    //For returning a normal user
    public TokenUser(AccountDTO user) {
        super(user.getLogonId(), user.getPassword() != null ? user.getPassword() : "", AuthorityUtils.createAuthorityList(user.getRoleIds() != null ? user.getRoleIds() : ""));
        this.user = user;
    }

    public AccountDTO getUser() {
        return user;
    }

    public String getRole() {
        return user.getRoleIds();
    }

    public int getUserId() {
        return user.getUserId();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
