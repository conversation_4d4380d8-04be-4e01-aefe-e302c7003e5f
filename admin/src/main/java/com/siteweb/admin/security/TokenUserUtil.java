package com.siteweb.admin.security;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.exception.UserValidException;
import com.siteweb.common.response.ErrorCode;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Habits
 * @time: 2022/3/30 10:52
 * @description:
 **/
@Component
public class TokenUserUtil {


    /**
     * 获取Authentication
     */
    private static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 获取用户
     */
    public static TokenUser getUser() {
        Authentication authentication = getAuthentication();
        if (ObjectUtil.isNotNull(authentication)) {
            Object details = authentication.getDetails();
            if (details instanceof TokenUser tokenUser) {
                return tokenUser;
            }
        }
        throw new UserValidException(ErrorCode.USER_AUTHORIZE_FAIL.getReasonPhrase());
    }

    /*
     * 获取当前登录用户ID
     */
    public static Integer getLoginUserId() {
         return getUser().getUserId();
    }

    public static String getLoginUserName() {
        return getUser().getUsername();
    }

    /**
     * 获取用户角色信息
     *
     * @return 角色集合
     */
    public static List<Integer> getRoles() {
        List<Integer> roleIds = new ArrayList<>();
        TokenUser user = getUser();
        String role = user.getRole();
        String[] roleArray = role.split(",");
        for (String s : roleArray) {
            roleIds.add(Integer.valueOf(s));
        }
        return roleIds;
    }

    /**
     * 获取用户登录类型
     * @return
     */
    public static String getLoginType() {
        TokenUser user = getUser();
        return user.getUser().getLoginType();
    }
}
