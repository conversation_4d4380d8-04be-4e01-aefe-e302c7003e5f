package com.siteweb.admin.security;

import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.AccountPasswordErrRecordService;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service("tokenUserDetailService")
public class TokenUserDetailsService implements org.springframework.security.core.userdetails.UserDetailsService {

    @Autowired
    AccountService accountService;

    @Autowired
    AccountPasswordErrRecordService accountPasswordErrRecordService;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Override
    public final TokenUser loadUserByUsername(String username) {
        final List<AccountDTO> users = accountService.findByLogonId(username);
        if (users == null || users.isEmpty()) {
            return null;
        }
        AccountDTO user = users.get(0);
        int remainingSecond = accountPasswordErrRecordService.remainingSecond(username);
        boolean checkVolentHack = accountPasswordErrRecordService.checkVolentHack(username);
        if (user.isLocked()) {
            securityAuditManager.recordSecurityReport(username,String.format(localeMessageSourceUtil.getMessage("user.account.locked"), username), SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
            throw new LockedException("User account is locked");
        } else if (!user.isEnable()) {
            securityAuditManager.recordSecurityReport(username,String.format(localeMessageSourceUtil.getMessage("user.disabled"), username), SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
            throw new DisabledException("User is disabled");
        } else if (!isValidDate(user)) {
            securityAuditManager.recordSecurityReport(username,String.format(localeMessageSourceUtil.getMessage("user.account.expired"), username), SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
            throw new AccountExpiredException("User account has expired");
        } else if (!isPasswordValidDate(user)) {
            securityAuditManager.recordSecurityReport(username,String.format(localeMessageSourceUtil.getMessage("user.password.expired"), username), SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
            throw new AccountExpiredException("User password is expired");
        } else if (checkVolentHack) {
            throw new BadCredentialsException("User account brute force hack");
        } else if (remainingSecond != 0) {
            throw new BadCredentialsException("User account is freeze:" + remainingSecond);
        }
        return new TokenUser(user);
    }

    /**
     * 判断密码是否在有效期内
     *
     * @param user
     * @return
     */
    private boolean isPasswordValidDate(AccountDTO user) {
        if (user.getPasswordValidTime() == null) {
            return true;
        }
        return user.getPasswordValidTime().after(new Date());
    }

    private boolean isValidDate(AccountDTO user) {
        if (user.getValidTime() == null) {
            return true;
        }
        return user.getValidTime().after(new Date());
    }

    public final TokenUser loadUserByMobile(String mobile) {
        final List<AccountDTO> users = accountService.findByMobile(mobile);
        if (users == null || users.isEmpty()) {
            return null;
        }
        AccountDTO user = users.get(0);
        if (user.isLocked()) {
            throw new LockedException("User account is locked");
        } else if (!user.isEnable()) {
            throw new DisabledException("User is disabled");
        } else if (!isValidDate(user)) {
            throw new AccountExpiredException("User account has expired");
        } else if (!isPasswordValidDate(user)) {
            throw new AccountExpiredException("User password is expired");
        }
        return new TokenUser(user);
    }

}