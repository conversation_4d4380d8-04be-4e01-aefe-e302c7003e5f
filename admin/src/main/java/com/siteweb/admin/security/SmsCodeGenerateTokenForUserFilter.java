package com.siteweb.admin.security;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.common.util.JacksonUtil;
import org.apache.commons.io.IOUtils;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR> zhou
 * @description SmsCodeGenerateTokenForUserFilter
 * @createTime 2022-07-13 09:47:24
 */
public class SmsCodeGenerateTokenForUserFilter extends AbstractAuthenticationProcessingFilter {

    private TokenUtil tokenUtil;

    protected SmsCodeGenerateTokenForUserFilter(SmsCodeAuthenticationManager authenticationManager, TokenUtil tokenUtil) {
        super(new AntPathRequestMatcher("/smscodelogin"));
        setAuthenticationManager(authenticationManager);
        this.tokenUtil = tokenUtil;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {
        try {
            String encode = "UTF-8";
            String tmpStr = "";
            String jsonString = IOUtils.toString(request.getInputStream(), encode);
            if (jsonString.length() > 0) {
                tmpStr = new String(Base64.getDecoder().decode(jsonString), encode);
            }
            String requestId = getRequestId(request);
            if (!requestId.isEmpty()) {
                tmpStr = requestId;
            }
            SmsCodePhoneAuthenticationToken authToken = null;
            if (tmpStr.length() > 0 && tmpStr.indexOf(':') >= 1) {
                String username = tmpStr.substring(0, tmpStr.indexOf(':'));
                String password = tmpStr.substring(tmpStr.indexOf(':') + 1);
                authToken = new SmsCodePhoneAuthenticationToken(URLDecoder.decode(username, "UTF-8"), password);
            }
            return getAuthenticationManager().authenticate(authToken); // This will take to successfulAuthentication or faliureAuthentication function
        } catch (Exception e) {
            throw new AuthenticationServiceException(e.getMessage());
        }
    }

    private String getRequestId(HttpServletRequest request) {
        String parameterValue = request.getParameter("requestId");
        String characterPattern = "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$";
        if (!parameterValue.matches(characterPattern)) {
            return "";
        }
        return new String(Base64.getDecoder().decode(parameterValue), StandardCharsets.UTF_8);
    }

    private String getLoginType(HttpServletRequest request) {
        String parameterValue = request.getParameter("loginType");
        String characterPattern = "[0-9a-zA-Z]";
        if (!parameterValue.matches(characterPattern)) {
            return "";
        }
        return parameterValue;
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain, Authentication authToken) throws IOException, ServletException {
        try {
            String loginType = getLoginType(req);
            SecurityContextHolder.getContext().setAuthentication(authToken);
            ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
            ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
            TokenUser tokenUser = (TokenUser) authToken.getPrincipal();
            String newToken = this.tokenUtil.createTokenForUser(tokenUser, loginType);

            jsonResp.put("token", newToken);
            jsonResp.put("UserName", tokenUser.getUser().getLogonId());
            jsonResp.put("LogonId", tokenUser.getUser().getLogonId());
            jsonResp.put("role", tokenUser.getRole());
            jsonResp.put("userId", tokenUser.getUserId());
            jsonResp.put("personId", tokenUser.getUserId());
            jsonResp.put("themeName", tokenUser.getUser().getThemeName());
            jsonResp.put("needResetPwd", tokenUser.getUser().getNeedResetPwd());
            jsonArray.add(jsonResp);

            res.setStatus(HttpServletResponse.SC_OK);
            res.getWriter().write(jsonArray.toString());
            res.getWriter().flush();
            res.getWriter().close();
        } catch (Exception e) {
            throw new AuthenticationServiceException(e.getMessage());
        }
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response, AuthenticationException failed) throws IOException, ServletException {
        ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
        ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
        jsonResp.put("token", "");
        jsonResp.put("error", failed.getMessage());
        String respCode = "errorcode";
        if (failed instanceof AccountExpiredException) {
            jsonResp.put(respCode, 7);
        } else if (failed instanceof LockedException) {
            jsonResp.put(respCode, 11);
        } else if (failed instanceof DisabledException) {
            jsonResp.put(respCode, 12);
        } else {
            jsonResp.put(respCode, 3);
        }
        jsonArray.add(jsonResp);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(jsonArray.toString());
        response.getWriter().flush();
        response.getWriter().close();
    }
}
