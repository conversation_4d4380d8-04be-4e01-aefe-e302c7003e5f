package com.siteweb.admin.security;


import cn.hutool.core.text.CharSequenceUtil;
import io.jsonwebtoken.Claims;

public class ClaimsUtil {
    private ClaimsUtil(){}
    public static Integer getUserId(Claims claims) {
        return claims.get("userId",Integer.class);
    }

    public static String getLogonId(Claims claims) {
        String logonId = claims.get("logonId", String.class);
        if (CharSequenceUtil.isBlank(logonId)) {
            return claims.get("userName", String.class);
        }
        return logonId;
    }
    public static String getRoles(Claims claims){
        String roles = claims.get("role", String.class);
        if (CharSequenceUtil.isBlank(roles)) {
            return claims.get("roles", String.class);
        }
        return roles;
    }
    public static String getLoginType(Claims claims){
        String loginType = claims.get("loginType", String.class);
        if (CharSequenceUtil.isBlank(loginType)) {
            return "web";
        }
        return loginType;
    }

    public static String getTheme(Claims claims){
        String themeName = claims.get("themeName", String.class);
        if (CharSequenceUtil.isBlank(themeName)) {
            return "default";
        }
        return themeName;
    }

    public static String getType(Claims claims){
        return claims.get("type", String.class);
    }
}
