package com.siteweb.admin.security;

import com.siteweb.admin.sso.cqctcc.CqctccService;
import com.siteweb.common.properties.CqctccSSOProperties;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 重庆电信登录逻辑限制
 * id>0的账户原始登录增加开关禁用s6登录，默认不允许从s6登录
 */
@Slf4j
public class CqctccLoginFilter extends HttpFilter {

    private final CqctccSSOProperties cqctccSSOProperties;

    private final LocaleMessageSourceUtil messageSourceUtil;
    private final CqctccService cqctccService;

    public CqctccLoginFilter(CqctccSSOProperties cqctccSSOProperties, LocaleMessageSourceUtil messageSourceUtil, CqctccService cqctccService) {
        this.cqctccSSOProperties = cqctccSSOProperties;
        this.messageSourceUtil = messageSourceUtil;
        this.cqctccService = cqctccService;
    }

    @Override
    public void init(FilterConfig filterConfig) {
        log.info("Init CqctccLoginFilter");
    }

    @Override
    protected void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        // 如果没启用重庆单点登录，则直接退出该Filter
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            filterChain.doFilter(request, response);
            return;
        }
        // 只拦截s6原始登录接口
        if (!"/login".equals(request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return;
        }
        // 解密后的用户名密码
        String tmpStr = new String(Base64.getDecoder().decode(getRequestId(request)), StandardCharsets.UTF_8);
        int index;
        if (StringUtils.isNotEmpty(tmpStr) && (index = tmpStr.indexOf(":")) >= 1) {
            String username = URLDecoder.decode(tmpStr.substring(0, index), StandardCharsets.UTF_8);
            // 验证登录用户的标志位，存在才可登录，默认不能使用s6登录
            boolean enableFlag = cqctccService.checkUserLogin(username);
            if (enableFlag) {
                filterChain.doFilter(request, response);
                return;
            } else {
                returnFailed(String.valueOf(ErrorCode.CQCTCC_ORIGINAL_LOGIN_DENIED.value()), "Cqctcc Original Login denied", response);
            }
        }
        log.error("<cqctccLoginFilter>用户名密码获取失败");
        returnFailed(String.valueOf(ErrorCode.CQCTCC_ORIGINAL_LOGIN_DENIED.value()), "Cqctcc Original Login denied", response);
    }

    private void returnFailed(String errorCode, String errorMessage, ServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try (PrintWriter out = response.getWriter()) {
            ResponseResult result = new ResponseResult();
            result.setState(false);
            result.setErrCode(errorCode);
            result.setErrMsg(errorMessage);
            result.setTimestamp(System.currentTimeMillis());
            out.print(JacksonUtil.toJSONString(result));
            out.flush();
        }
    }

    private String getRequestId(HttpServletRequest request) {
        String parameterValue = request.getParameter("requestId");
        String characterPattern = "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$";
        if (!parameterValue.matches(characterPattern)) {
            return "";
        }
        return parameterValue;
    }

    @Override
    public void destroy() {
        log.info("Destroy CqctccLoginFilter");
    }
}
