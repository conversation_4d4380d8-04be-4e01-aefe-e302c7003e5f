package com.siteweb.admin.security;

import com.siteweb.utility.service.SmsCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR> zhou
 * @description SmsCodeAuthenticationManager
 * @createTime 2022-07-13 09:46:44
 */
@Component
public class SmsCodeAuthenticationManager implements AuthenticationManager {

    @Autowired
    TokenUserDetailsService tokenUserDetailsService;

    @Autowired
    SmsCodeService smsCodeService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String mobile = authentication.getName();
        String smsCode = (String) authentication.getCredentials();
        int result = smsCodeService.checkSmsCode(mobile, smsCode);
        TokenUser user = null;
        if (result > 0) {
            user = tokenUserDetailsService.loadUserByMobile(mobile);
            if (user == null) {
                // 当作用户帐号查询
                user = tokenUserDetailsService.loadUserByUsername(mobile);
            }
        } else {
            throw new BadCredentialsException("smscode error");
        }
        String password = user == null ? null : user.getPassword();
        if (user == null) {
            throw new BadCredentialsException("user not exist");
        }
        Collection<? extends GrantedAuthority> authorities = user.getAuthorities();
        return new SmsCodePhoneAuthenticationToken(user, password, authorities);
    }
}
