package com.siteweb.admin.security;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.HttpUtil;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SignatureUtil;
import com.siteweb.common.wrapper.RepeatableReadRequestWrapper;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashSet;
import java.util.Set;
import java.util.SortedMap;

/**
 * <AUTHOR> zhou
 * @description SignatureFilter
 * @createTime 2022-07-18 15:05:53
 */
@Slf4j
public class SignatureFilter implements Filter {

    private final SecurityProperties securityProperties;

    private final RedisUtil redisUtil;

    private final SecurityAuditManager securityAuditManager;

    private final LocaleMessageSourceUtil messageSourceUtil;

    private final SystemConfigService systemConfigService;

    public SignatureFilter(SecurityProperties securityProperties, RedisUtil redisUtil, SecurityAuditManager securityAuditManager, LocaleMessageSourceUtil messageSourceUtil, SystemConfigService systemConfigService){
        this.securityProperties = securityProperties;
        this.redisUtil = redisUtil;
        this.securityAuditManager = securityAuditManager;
        this.messageSourceUtil = messageSourceUtil;
        this.systemConfigService = systemConfigService;
    }

    @Override
    public void init(FilterConfig filterConfig) {
        log.info("Init SignatureFilter");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        //如果没启用防重放攻击，则直接退出该Filter
        if (!Boolean.TRUE.equals(securityProperties.antiReplayAttack)) {
            filterChain.doFilter(request, response);
            return;
        }
        // 防止流读取一次后就没有了, 所以需要将流继续写出去
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        Set<String> uriSet = new HashSet<>(securityProperties.getIgnoreSignUri());
        String requestUri = httpRequest.getRequestURI();
        //ignoreSign：true允许忽略签名，false需要签名验证，yml进行配置
        boolean ignoreSign = false;
        for (String uri : uriSet) {
            ignoreSign = requestUri.contains(uri);
            if (ignoreSign) {
                break;
            }
        }
        log.info("RequestURL: {}, ignoreSign: {}", httpRequest.getRequestURI(), ignoreSign);
        if (ignoreSign) {
            filterChain.doFilter(httpRequest, response);
            return;
        }

        RepeatableReadRequestWrapper requestWrapper = new RepeatableReadRequestWrapper(httpRequest);
        String sign = requestWrapper.getHeader("Sign");
        Long timestamp = Convert.toLong(requestWrapper.getHeader("Timestamp"));
        String nonceStr = requestWrapper.getHeader("Nonce");
        String token = requestWrapper.getHeader(systemConfigService.findTokenHeader());

        if (CharSequenceUtil.isEmpty(sign)) {
            returnFailed(String.valueOf(ErrorCode.SECURITY_SIGNATURE_IS_NULL.value()), "Signature is null", response);
            return;
        }

        if (timestamp == null) {
            returnFailed(String.valueOf(ErrorCode.SECURITY_TIMESTAMP_IS_NULL.value()), "Timestamp is null", response);
            return;
        }

        if (nonceStr == null) {
            returnFailed(String.valueOf(ErrorCode.SECURITY_NONCE_IS_NULL.value()), "Nonce is null", response);
            return;
        }

        //重放时间限制（单位分）
        Long difference = DateUtil.between(DateUtil.date(), DateUtil.date(timestamp * 1000), DateUnit.MINUTE);
        if (difference > securityProperties.getSignTimeout()) {
            returnFailed(String.valueOf(ErrorCode.SECURITY_SIGNATURE_OUT_OF_DATE.value()), "Signature is out of date", response);
            securityAuditManager.recordSecurityReport("admin", messageSourceUtil.getMessage("signature.Expired"),SecurityReportTypeEnum.ATTACK_DETECTION);
            log.info("Browser timestamp：{}, Server timestamp：{}", DateUtil.date(timestamp * 1000), DateUtil.date());
            return;
        }

        if (redisUtil.hasKey("nonce" + nonceStr)) {
            returnFailed(String.valueOf(ErrorCode.SECURITY_SIGNATURE_IS_INVALID.value()), "Signature is invalid", response);
            securityAuditManager.recordSecurityReport("admin", messageSourceUtil.getMessage("signature.invalid"),SecurityReportTypeEnum.ATTACK_DETECTION);
            log.info("Nonce：{} already exists", nonceStr);
            return;
        }

        boolean accept = switch (requestWrapper.getMethod()) {
            case "GET" -> {
                SortedMap<String, String> paramMap = HttpUtil.getUrlParams(requestWrapper);
                yield SignatureUtil.verifySignature(paramMap, sign, timestamp, nonceStr, token, requestUri);
            }
            case "POST", "PUT", "DELETE" -> {
                String bodyParams = HttpUtil.getBodyParams(requestWrapper);
                yield SignatureUtil.verifySignature(bodyParams, sign, timestamp, nonceStr, token, requestUri);
            }
            default -> true;
        };
        if (accept) {
            redisUtil.set("nonce" + nonceStr, nonceStr, securityProperties.getSignTimeout() * 60);
            filterChain.doFilter(requestWrapper, response);
        } else {
            securityAuditManager.recordSecurityReport("admin", messageSourceUtil.getMessage("signature.invalid"),SecurityReportTypeEnum.ATTACK_DETECTION);
            returnFailed(String.valueOf(ErrorCode.SECURITY_SIGNATURE_IS_INVALID.value()), "Signature is invalid", response);
        }
    }

    private void returnFailed(String errorCode, String errorMessage, ServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try (PrintWriter out = response.getWriter()) {
            ResponseResult result = new ResponseResult();
            result.setState(false);
            result.setErrCode(errorCode);
            result.setErrMsg(errorMessage);
            result.setTimestamp(System.currentTimeMillis());
            out.print(JacksonUtil.toJSONString(result));
            out.flush();
        }
    }

    @Override
    public void destroy() {
        log.info("Destroy SignatureFilter");
    }
}
