package com.siteweb.admin.security;

import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.*;
import com.siteweb.admin.sso.cqctcc.CqctccService;
import com.siteweb.common.properties.CqctccSSOProperties;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.util.CaptchaUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@Order(1)
@DependsOn("liquibase")
@RequiredArgsConstructor
public class SecurityConfig {

    private final TokenUtil tokenUtil;
    private final ProdAntPatternServiceImpl prodAntPatternService;
    private final SystemConfigService systemConfigService;
    private final SecurityProperties securityProperties;
    private final RedisUtil redisUtil;
    private final CaptchaUtil captchaUtil;
    private final LocaleMessageSourceUtil messageSourceUtil;
    private final AccountPasswordErrRecordService accountPasswordErrRecordService;
    private final AccountTimeSpanService accountTimeSpanService;
    private final IpFilterPolicyService ipFilterPolicyService;
    private final UserAuthenticationManager authenticationManager;
    private final SmsCodeAuthenticationManager smsCodeAuthenticationManager;
    private final AccountService accountService;
    private final SecurityAuditManager securityAuditManager;
    private final UserConfigService userConfigService;
    private final SecurityFileIntegrityService securityFileIntegrityService;
    private final CqctccSSOProperties cqctccSSOProperties;
    private final CqctccService cqctccService;

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        // Filters will not get executed for the resources
        return web -> web.ignoring().antMatchers(prodAntPatternService.getAntPatterns());
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity httpSecurity) throws Exception {
        return httpSecurity.exceptionHandling().and()
                .anonymous().and()
                // Disable Cross site references
                .csrf().disable()
                // Custom Token based authentication based on the header previously given to the client
                .addFilterBefore(new VerifyTokenFilter(tokenUtil), UsernamePasswordAuthenticationFilter.class)
                // custom JSON based authentication by POST of {"username":"<name>","password":"<password>"} which sets the token header upon authentication
                .addFilterBefore(new GenerateTokenForUserFilter(authenticationManager, tokenUtil, systemConfigService, captchaUtil, accountPasswordErrRecordService, accountTimeSpanService, ipFilterPolicyService, securityFileIntegrityService, userConfigService), UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(new CqctccLoginFilter(cqctccSSOProperties, messageSourceUtil, cqctccService), ChannelProcessingFilter.class)
                .addFilterBefore(new SmsCodeGenerateTokenForUserFilter(smsCodeAuthenticationManager, tokenUtil), UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(new SignatureFilter(securityProperties, redisUtil, securityAuditManager, messageSourceUtil, systemConfigService), ChannelProcessingFilter.class)
                .addFilterBefore(new AccountHeartbeatFilter(securityProperties, accountService, tokenUtil, systemConfigService), ChannelProcessingFilter.class)
                .addFilterBefore(new LanguageFilter(), ChannelProcessingFilter.class)
                .authorizeRequests()
                .anyRequest().authenticated().and().build();
    }
}
