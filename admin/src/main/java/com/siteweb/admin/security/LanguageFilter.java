package com.siteweb.admin.security;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.language.LanguageUtil;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class LanguageFilter extends GenericFilterBean {
    public static final ThreadLocal<String> LanguageThreadLocal = new ThreadLocal<>();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            LanguageThreadLocal.set(httpRequest.getHeader(LanguageUtil.LANGUAGE_HEADER));
            chain.doFilter(request, response);
        } finally {
            LanguageThreadLocal.remove();
        }
    }
    public static String getCurrentThreadLanguage(){
        String language = LanguageThreadLocal.get();
        if (CharSequenceUtil.isBlank(language)){
            return LanguageUtil.ZH_CN;
        }
        return language;
    }
}
