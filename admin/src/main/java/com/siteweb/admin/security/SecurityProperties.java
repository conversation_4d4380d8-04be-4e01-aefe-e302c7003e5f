package com.siteweb.admin.security;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SecurityProperties
 * @createTime 2022-07-19 08:45:08
 */
@Component
@ConfigurationProperties(prefix = "spring.security")
@Data
public class SecurityProperties {

    /**
     * 启用防重放攻击
     */
    Boolean antiReplayAttack;

    /**
     * 允许忽略签名地址
     */
    List<String> ignoreSignUri;

    /**
     * 签名超时时间(分)
     */
    Integer signTimeout;

    /**
     * 是否开启用户健康检查
     */
    Boolean accountHeartbeat;
}
