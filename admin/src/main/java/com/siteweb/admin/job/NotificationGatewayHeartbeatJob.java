package com.siteweb.admin.job;

import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.NotificationGatewayService;
import com.siteweb.admin.service.impl.NotificationGatewayServiceImpl;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NotificationGatewayHeartbeatJob implements BaseJob {

    @Autowired
    NotificationGatewayService notificationGatewayService;
    @Autowired
    HAStatusService haStatusService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            log.info("不是主机 退出定时任务");
            return;
        }
        boolean currentStatus = NotificationGatewayServiceImpl.gatewayState.get();
        boolean result = notificationGatewayService.gatewayHeartbeatState();
        if (currentStatus != result) {
            // 状态发生变化，记录日志，修改状态
            NotificationGatewayServiceImpl.gatewayState.set(result);
            notificationGatewayService.recordGatewayLog(result);
            log.info("NotificationGatewayHeartbeatJob update status [status={}]", result);
        }
    }
}
