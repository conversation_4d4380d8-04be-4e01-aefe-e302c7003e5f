package com.siteweb.admin.job;

import com.siteweb.admin.service.SecurityFileIntegrityService;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FileIntegrityCheckJob implements BaseJob {

    @Autowired
    SecurityFileIntegrityService securityFileIntegrityService;
    @Autowired
    HAStatusService haStatusService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            log.info("不是主机 退出定时任务");
            return;
        }
        securityFileIntegrityService.fileIntegrityCheck();
    }
}
