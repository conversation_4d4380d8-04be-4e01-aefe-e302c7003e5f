package com.siteweb.admin.sso.keycloak;

import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.admin.vo.AccountVO;
import com.siteweb.admin.vo.EmployeeVO;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description KeycloakUserServiceImpl
 * @createTime 2022-09-18 13:42:39
 */
@Service
public class KeycloakUserServiceImpl implements KeycloakUserService {

    private final Logger log = LoggerFactory.getLogger(KeycloakUserServiceImpl.class);

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    AccountService accountService;

    @Autowired
    UserRoleMapper userRoleMapper;

    @Override
    @Transactional
    public Integer createAccountByKeycloakUserNameIfNotExists(String casUserName) {
        Integer accountId = null;
        List<AccountDTO> accountDTOS = accountService.findByLogonId(casUserName);
        if (!accountDTOS.isEmpty()) {
            return accountId;
        }
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("keycloak.user.defaultDepartmentIdAndRoleId");
        if (systemConfig == null || systemConfig.getSystemConfigValue() == null || systemConfig.getSystemConfigValue().isEmpty()) {
            log.error("keycloak.user.defaultDepartmentIdAndRoleId 未配置");
            return accountId;
        }
        String[] tmpArray = systemConfig.getSystemConfigValue().split(",");
        if (tmpArray.length != 2) {
            log.error("keycloak.user.defaultDepartmentIdAndRoleId 配置错误");
            return accountId;
        }
        //新增人员
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setEmployeeName(casUserName);
        employeeVO.setDepartmentId(Integer.parseInt(tmpArray[0]));
        employeeVO.setJobNumber(casUserName);
        employeeVO.setEnable(true);
        int employeeId = employeeService.createEmployee(-1,employeeVO);
        if (employeeId < 0) {
            log.error("获取主键EmployeeId失败");
            return accountId;
        }
        //新增账号
        AccountVO accountVO = new AccountVO();
        accountVO.setUserId(employeeId);
        accountVO.setUserName(casUserName);
        accountVO.setLogonId(casUserName);
        accountVO.setPassword("");
        accountVO.setMaxError(null);
        accountVO.setValidTime(null);
        accountVO.setPasswordValidTime(null);
        accountVO.setEnable(true);
        accountVO.setLocked(false);
        accountVO.setRemote(false);
        accountVO.setDescription(null);
        accountVO.setNeedResetPwd(false);
        accountService.createAccount(-1,accountVO);
        //设置人员角色
        userRoleMapper.createUserRoleMap(employeeId, Integer.parseInt(tmpArray[1]));
        return accountId;
    }
}
