package com.siteweb.admin.sso.gaoyang;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@DependsOn("rabbitMQConfig")
@ConditionalOnProperty(name = "spring.rabbitmq.enable", havingValue = "true", matchIfMissing = false)
public class RabbitMessageService {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private GaoYangUserSyncService gaoYangUserSyncService;
    @RabbitListener(queues = "${spring.rabbitmq.queue}")
    public void receiveMessage(String message) {
        try {
            log.info("Receive raw rabbitmq message" + message);
            // 使用 ObjectMapper 将消息反序列化为目标类型
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(DeserializationFeature.FAIL_ON_MISSING_CREATOR_PROPERTIES, false);
            GaoYangUserMessageDTO dto = objectMapper.readValue(message, GaoYangUserMessageDTO.class);
            //校验必要字段
            dto.validate();
            // 处理 DTO
            String b = gaoYangUserSyncService.userSync(dto);
            if (StringUtils.isNotEmpty(b)){
                log.warn(dto + " failed sync: " + b);
            }
        } catch (Exception e) {
            //解析失败
            log.error("Failed to parse message: "+ message + "-" + e.getMessage() + " skip it!");
        }
    }
}

