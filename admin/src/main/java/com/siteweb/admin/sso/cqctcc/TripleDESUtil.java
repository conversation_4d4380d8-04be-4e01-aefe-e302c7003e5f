package com.siteweb.admin.sso.cqctcc;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DESede;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.BadPaddingException;

@Slf4j
@UtilityClass
public class TripleDESUtil {

    // 定义 TripleDES 密钥
    private static final byte[] desKeyData = {
            (byte) 0xF4, (byte) 0x88, (byte) 0xFD, (byte) 0x58,
            (byte) 0x4E, (byte) 0x49, (byte) 0xDB, (byte) 0xCD,
            (byte) 0x20, (byte) 0xB4, (byte) 0x9D, (byte) 0xE4,
            (byte) 0x91, (byte) 0x07, (byte) 0x36, (byte) 0x6B,
            (byte) 0x33, (byte) 0x6C, (byte) 0x38, (byte) 0x0D,
            (byte) 0x45, (byte) 0x1D, (byte) 0x0F, (byte) 0x7C
    };

    // 3DES 解密方法，只需要传入加密的字符串
    public static String decrypt(String encryptedStr) throws BadPaddingException {
        try {
            // 将加密后的字符串转换为字节数组（16进制字符串转换）
            byte[] encryptedBytes = HexUtil.decodeHex(encryptedStr);
            // 创建 DESede 对象 (3DES)
            DESede desede = SecureUtil.desede(desKeyData);
            // 使用密钥解密
            byte[] decryptedBytes = desede.decrypt(encryptedBytes);
            // 将解密后的字节数组转换为字符串并返回
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new BadPaddingException("DES解密失败");
        }
    }
}

