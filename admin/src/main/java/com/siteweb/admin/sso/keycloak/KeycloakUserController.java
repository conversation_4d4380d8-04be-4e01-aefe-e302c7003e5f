package com.siteweb.admin.sso.keycloak;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhou
 * @description KeycloakUserController
 * @createTime 2022-09-18 13:39:54
 */
@RestController
@RequestMapping("/api")
public class KeycloakUserController {

    @Autowired
    KeycloakUserService keycloakUserService;

    @Autowired
    TokenUtil tokenUtil;

    @Autowired
    AccountService accountService;

    private static final String KEYCLOAK_TOKEN_NAME = "Keycloak-Token";

    @PostMapping("/keycloak/login")
    public void keycloakUserLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String keycloakTokenHeader = request.getHeader(KEYCLOAK_TOKEN_NAME);
        if (null == keycloakTokenHeader) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        String keycloakUserName = null;
        try {
            byte[] inputStreamBytes = StreamUtils.copyToByteArray(request.getInputStream());
            Map<String, String> jsonRequest = new ObjectMapper().readValue(inputStreamBytes, Map.class);
            keycloakUserName = jsonRequest.get("userName");
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        //创建Keycloak用户，赋予默认角色
        keycloakUserService.createAccountByKeycloakUserNameIfNotExists(keycloakUserName);
        List<AccountDTO> accounts = accountService.findByLogonId(keycloakUserName);
        if (accounts.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        String loginType = "web";
        String currentLoginType = request.getParameter("loginType");
        if (currentLoginType != null) {
            loginType = currentLoginType;
        }
        //根据传入的用户名，生成jwt token并返回给前端，从而实现登录功能
        TokenUser tokenUser = new TokenUser(accounts.get(0));
        String newToken = this.tokenUtil.createTokenForUser(tokenUser, loginType);

        ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
        ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
        jsonResp.put("token", newToken);
        jsonResp.put("UserName", tokenUser.getUser().getLogonId());
        jsonResp.put("LogonId", tokenUser.getUser().getLogonId());
        jsonResp.put("role", tokenUser.getRole());
        jsonResp.put("userId", tokenUser.getUserId());
        jsonResp.put("personId", tokenUser.getUserId());
        jsonResp.put("themeName", tokenUser.getUser().getThemeName());
        jsonArray.add(jsonResp);

        response.setHeader("Content-Type","application/json;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_OK);
        response.getWriter().write(jsonArray.toString());
        response.getWriter().flush();
        response.getWriter().close();
    }
}
