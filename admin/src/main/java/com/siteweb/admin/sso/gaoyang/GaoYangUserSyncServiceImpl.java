package com.siteweb.admin.sso.gaoyang;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.mapper.EmployeeMapper;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.admin.vo.AccountVO;
import com.siteweb.admin.vo.EmployeeVO;
import com.siteweb.common.util.StringUtils;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 */
@Service
@Slf4j
public class GaoYangUserSyncServiceImpl implements GaoYangUserSyncService{
    @Autowired
    EmployeeService employeeService;

    @Autowired
    AccountService accountService;

    @Autowired
    UserRoleMapper userRoleMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    TokenUtil tokenUtil;
    @Override
   public String userSync(GaoYangUserMessageDTO messageDTO){
        if (ObjectUtil.isNull(messageDTO) || StringUtil.isNullOrEmpty(messageDTO.getType()) || StringUtil.isNullOrEmpty(messageDTO.getUsername())){
            return "Required fields cannot be null or empty";
        }
        String username = messageDTO.getUsername();
        List<AccountDTO> accountDTOS = accountService.findByLogonId(username);
        AccountVO accountVO = new AccountVO();
        EmployeeVO employeeVO = new EmployeeVO();
        Integer gender = getGenderBySex(messageDTO.getSex());
        switch (messageDTO.getType()){
            case "add":
                if (!accountDTOS.isEmpty()) {
                    //存在用户 不新增
                    return "Do not add existing users";
                }
                //新增人员
                employeeVO.setJobNumber("");
                employeeVO.setPhone(messageDTO.getPhone());
                employeeVO.setEmail(messageDTO.getEmail());
                employeeVO.setEmployeeName(messageDTO.getRealname());
                employeeVO.setGender(gender);

                employeeVO.setDepartmentId(null);
                employeeVO.setEnable(true);
                int employeeId = employeeService.createEmployee(-1,employeeVO);
                if (employeeId < 0) {
                    log.error("获取主键EmployeeId失败");
                    return "Failed to create employee id";
                }
                //新增账号
                accountVO.setUserId(employeeId);
                accountVO.setUserName(messageDTO.getRealname());
                accountVO.setLogonId(username);
                accountVO.setPassword("");
                accountVO.setMaxError(null);
                accountVO.setValidTime(null);
                accountVO.setPasswordValidTime(null);
                accountVO.setEnable(true);
                accountVO.setLocked(false);
                accountVO.setRemote(false);
                accountVO.setDescription(null);
                accountVO.setNeedResetPwd(false);
                accountService.createAccount(-1,accountVO);
                //设置人员角色
                userRoleMapper.createUserRoleMap(employeeId, -1);
                break;
            case "update":
                if (accountDTOS.size() != 1) {
                    //不存在该用户，不修改
                    return "Do not modify non-existing users";
                }
                employeeId = accountDTOS.get(0).getUserId();
                if (employeeId < 0 ){
                    return "The system initial user cannot be modified";
                }
                Employee byEmployeeId = employeeService.findByEmployeeId(employeeId);
                if (ObjectUtil.isNull(byEmployeeId)){
                    return "The corresponding employee does not exist and the update failed.";
                }
                employeeVO.setGender(gender);
                byEmployeeId.setJobNumber("");
                byEmployeeId.setPhone(messageDTO.getPhone());
                byEmployeeId.setEmail(messageDTO.getEmail());
                byEmployeeId.setEmployeeName(messageDTO.getRealname());

                byEmployeeId.setDepartmentId(null);
                byEmployeeId.setEnable(true);

                int updateEmployee = employeeMapper.updateById(byEmployeeId);
                if (updateEmployee != 1) {
                    return "Employee update failed";
                }
                //更新账号
                accountVO.setUserId(employeeId);
                accountVO.setUserName(messageDTO.getRealname());
                accountVO.setLogonId(username);
                accountVO.setPassword("");
                accountVO.setMaxError(null);
                accountVO.setValidTime(null);
                accountVO.setPasswordValidTime(null);
                accountVO.setEnable(true);
                accountVO.setLocked(false);
                accountVO.setRemote(false);
                accountVO.setDescription(null);
                accountVO.setNeedResetPwd(false);
                accountService.updateAccount(accountVO);
                //设置人员角色
                userRoleMapper.createUserRoleMap(employeeId, -1);
                break;
            case "del":
                if (accountDTOS.size() != 1) {
                    //不存在该用户，不删除
                    return "Do not delete non-existing users";
                }
                employeeId = accountDTOS.get(0).getUserId();
                if (employeeId < 0 ){
                    return "The system initial user cannot be deleted";
                }
                employeeMapper.deleteById(employeeId);
                accountService.deleteAccountByUserId(employeeId);
                break;
        }
        return "";
    }
    private Integer getGenderBySex(String sex) {
        if (StringUtils.isEmpty(sex)) {
            return null;
        }
        if (StringUtils.equals(sex, "0")) {
            return 0;
        }
        if (StringUtils.equals(sex, "1")) {
            return 1;
        }
        return null;
    }
}
