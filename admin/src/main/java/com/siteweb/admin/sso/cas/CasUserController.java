package com.siteweb.admin.sso.cas;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.JacksonUtil;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api")
public class CasUserController {

    @Autowired
    CasUserService casUserService;

    @Autowired
    TokenUtil tokenUtil;

    @Autowired
    AccountService accountService;

    @RequestMapping("/sso/login")
    public void casUserLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        AttributePrincipal principal = assertion.getPrincipal();
        //创建CAS用户，赋予管理员角色
        casUserService.createAccountByCasUserNameIfNotExists(principal.getName());
        List<AccountDTO> accounts = accountService.findByLogonId(principal.getName());
        if (accounts.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        String loginType = getLoginType(request);
        //根据传入的用户名，生成jwt token并返回给前端，从而实现登录功能
        TokenUser tokenUser = new TokenUser(accounts.get(0));
        String newToken = this.tokenUtil.createTokenForUser(tokenUser, loginType);

        ArrayNode jsonArray = JacksonUtil.getInstance().createArrayNode();
        ObjectNode jsonResp = JacksonUtil.getInstance().createObjectNode();
        jsonResp.put("token", newToken);
        jsonResp.put("UserName", tokenUser.getUser().getLogonId());
        jsonResp.put("LogonId", tokenUser.getUser().getLogonId());
        jsonResp.put("role", tokenUser.getRole());
        jsonResp.put("userId", tokenUser.getUserId());
        jsonResp.put("personId", tokenUser.getUserId());
        jsonResp.put("themeName", tokenUser.getUser().getThemeName());
        jsonArray.add(jsonResp);

        response.setHeader("Content-Type","application/json;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_OK);
        response.getWriter().write(jsonArray.toString());
        response.getWriter().flush();
        response.getWriter().close();
    }

    private String getLoginType(HttpServletRequest request) {
        String defaultLoginType = "web";
        String parameterValue = request.getParameter("loginType");
        String characterPattern = "[0-9a-zA-Z]";
        if (!parameterValue.matches(characterPattern)) {
            return defaultLoginType;
        }
        return parameterValue;
    }

    @RequestMapping("/sso/logout")
    public void casUserLogout(HttpServletRequest request) {
        request.getSession().invalidate();
    }
}
