package com.siteweb.admin.sso.cqctcc;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.SystemPropsUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;
import com.siteweb.admin.dto.PersonStaffInfoDTO;
import com.siteweb.admin.dto.RolePermissionInfoDTO;
import com.siteweb.admin.dto.StaffRoleInfoDTO;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.properties.CqctccSSOProperties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.*;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 重庆电信单点登录定时任务
 */
@Component
@Slf4j
public class CqctccQuartz {
    @Autowired
    CqctccSSOProperties cqctccSSOProperties;
    @Autowired
    AccountMapper accountMapper;
    private static String mainFilePath;

    {
        String os = SystemPropsUtil.get("os.name");
        String basePath = File.separator + "CQCTCC" + File.separator + "upload-dir" + File.separator + "ftp";
        if (CharSequenceUtil.isNotBlank(os) && os.toLowerCase().contains("win")) {
            // Windows 系统
            mainFilePath = "C:" + basePath;
        } else {
            // 其他系统 (Linux/Unix) 或 未知系统
            mainFilePath = basePath;
        }
    }


    /**
     * 同步上传文件
     * 每天凌晨4点之前上传完成
     */
    @Scheduled(cron = "0 0 2 * * ?")
    protected void syncDatFileJobExectue() {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            log.info("<syncDatFileJobExectue>重庆电信单点登录未启动");
            return;
        }
        // 先删除过时的本地文件，防止ftp连接失败后续不执行
        deleteOldFiles();
        syncPersonStaffInfo();
        syncStaffRoleInfo();
        syncRolePermissionInfo();
    }

    /**
     * 删除过时的本地文件
     */
    private void deleteOldFiles() {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            log.info("<deleteOldFiles>重庆电信单点登录未启动");
            return;
        }
        List<File> files = FileUtil.loopFiles(mainFilePath);
        for (File file : files) {
            long lastModified = file.lastModified();
            int ftpFileKeepDays = Objects.isNull(cqctccSSOProperties.getFtpFileKeepDays()) ? 30 : cqctccSSOProperties.getFtpFileKeepDays();
            DateTime dateTime = DateUtil.offsetDay(DateUtil.date(), -1 * ftpFileKeepDays);
            if (lastModified < dateTime.getTime()) {
                FileUtil.del(file);
            }
        }
    }

    /**
     * 信息系统帐号与统一认证帐号关系
     */
    protected void syncPersonStaffInfo() {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            log.info("<syncPersonStaffInfo>重庆电信单点登录未启动");
            return;
        }
        log.info("<syncPersonStaffInfo> PersonStaffInfo 定时上传文件任务开始");
        List<String> ret = getPersonStaffInfos();
        String timestamp = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        String fileName = mainFilePath + File.separator + "person_staff_info_" + timestamp + ".dat";
        log.info("<syncPersonStaffInfo>PersonStaffInfo dat 文件开始生成 fileName={}", fileName);
        File file = generateDatInfoFile(fileName, ret);
        log.info("<syncPersonStaffInfo>PersonStaffInfo dat 文件生成成功");
        String ftpFilePath = cqctccSSOProperties.getFtpFilePath();
        uploadFileToFTP(file, ftpFilePath);
        log.info("<syncPersonStaffInfo>ftp文件上传成功");
    }

    protected void syncStaffRoleInfo() {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            log.info("<syncStaffRoleInfo>重庆电信单点登录未启动");
            return;
        }
        log.info("<syncStaffRoleInfo> StaffRoleInfo 定时上传文件任务开始");
        List<String> ret = getStaffRoleInfos();
        String timestamp = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        String fileName = mainFilePath + File.separator + "staff_role_info_" + timestamp + ".dat";
        log.info("<syncStaffRoleInfo>StaffRoleInfo dat 文件开始生成 fileName={}", fileName);
        File file = generateDatInfoFile(fileName, ret);
        log.info("<syncStaffRoleInfo>StaffRoleInfo dat 文件生成成功");
        String ftpFilePath = cqctccSSOProperties.getFtpFilePath();
        uploadFileToFTP(file, ftpFilePath);
        log.info("<syncStaffRoleInfo>ftp文件上传成功");
    }

    protected void syncRolePermissionInfo() {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            log.info("<syncRolePermissionInfo>重庆电信单点登录未启动");
            return;
        }
        log.info("<syncRolePermissionInfo> RolePermissionInfo 定时上传文件任务开始");
        List<String> ret = getRolePermissionInfo();
        String timestamp = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN);
        String fileName = mainFilePath + File.separator + "role_permission_info_" + timestamp + ".dat";
        log.info("<syncRolePermissionInfo>RolePermissionInfo dat 文件开始生成 fileName={}", fileName);
        File file = generateDatInfoFile(fileName, ret);
        log.info("<syncRolePermissionInfo>RolePermissionInfo dat 文件生成成功");
        String ftpFilePath = cqctccSSOProperties.getFtpFilePath();
        uploadFileToFTP(file, ftpFilePath);
        log.info("<syncRolePermissionInfo>ftp文件上传成功");

    }

    private List<String> getRolePermissionInfo() {
        List<String> ret = new ArrayList<>();
        List<RolePermissionInfoDTO> rolePermissionInfoList = accountMapper.getRolePermissionInfoList();
        for (RolePermissionInfoDTO rolePermissionInfo : rolePermissionInfoList) {
            StringBuilder sb = new StringBuilder();
            sb.append(cqctccSSOProperties.getSysCode());
            sb.append("|$|");
            sb.append(rolePermissionInfo.getRoleName());
            sb.append("|$|");
            sb.append(rolePermissionInfo.getRoleId());
            sb.append("|$|");
            sb.append(rolePermissionInfo.getGroupName());
            sb.append("|$|");
            sb.append(rolePermissionInfo.getGroupId());
            ret.add(sb.toString());
        }
        return ret;
    }

    private List<String> getStaffRoleInfos() {
        List<String> ret = new ArrayList<>();
        List<StaffRoleInfoDTO> staffRoleInfoList = accountMapper.getStaffRoleInfo();
        for (StaffRoleInfoDTO staffRoleInfo : staffRoleInfoList) {
            StringBuilder sb = new StringBuilder();
            sb.append(staffRoleInfo.getLogonId());
            sb.append("|$|");
            sb.append(staffRoleInfo.getRoleId());
            sb.append("|$|");
            sb.append(staffRoleInfo.getRoleName());
            sb.append("|$|");
            sb.append("1100");
            sb.append("|$|");
            sb.append(cqctccSSOProperties.getSysCode());
            sb.append("|$||$||$||$|");
            ret.add(sb.toString());
        }
        return ret;
    }

    private List<String> getPersonStaffInfos() {
        List<String> ret = new ArrayList<>();
        List<PersonStaffInfoDTO> personStaffInfos = accountMapper.getPersonStaffInfo();
        String ftpDesKey = cqctccSSOProperties.getFtpDesKey();
        if (CharSequenceUtil.isBlank(ftpDesKey)) {
            throw new BusinessException("<syncPersonStaffInfo>ftpDesKey未配置");
        }
        for (PersonStaffInfoDTO personStaffInfo : personStaffInfos) {
            StringBuilder sb = new StringBuilder();
            if (CharSequenceUtil.isNotBlank(personStaffInfo.getDescription())
                    && "平台账户".equals(personStaffInfo.getDescription().trim())) {
                sb.append(personStaffInfo.getLogonId());
            }
            sb.append("|$|");
            sb.append(cqctccSSOProperties.getSysCode());
            sb.append("|$|");
            sb.append(personStaffInfo.getUserId());
            sb.append("|$|");
            sb.append(personStaffInfo.getUserName());
            sb.append("|$|");
            sb.append("1001");
            sb.append("|$|");
            if (Boolean.TRUE.equals(personStaffInfo.getEnable())) {
                sb.append("00");
            } else {
                sb.append("01");
            }
            sb.append("|$|");
            // 无身份证号
            sb.append("|$|");
            if ((CharSequenceUtil.isNotBlank(personStaffInfo.getPhone()))) {
                // DES加密
                DES des = SecureUtil.des(ftpDesKey.getBytes());
                sb.append(des.encryptBase64(personStaffInfo.getPhone()));
            }

            ret.add(sb.toString());
        }
        return ret;
    }

    /**
     * 生成dat文件
     */
    private File generateDatInfoFile(String fileName, List<String> dataList) {
        File file = FileUtil.touch(fileName);
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            for (String line : dataList) {
                writer.write(line);
                writer.newLine(); // 每个字符串写入一行
            }
        } catch (IOException e) {
            log.error("<syncDatFileJobExectue> 生成dat文件失败", e);
            throw new BusinessException("重庆电信生成dat文件失败");
        }
        return file;
    }

    /**
     * 上传文件到 FTP
     *
     * @param file    上传的文件
     * @param ftpPath 上传ftp的路径
     */
    @SneakyThrows
    public void uploadFileToFTP(File file, String ftpPath) {
        FTPClient ftpClient = new FTPClient();
        boolean uploaded = false;
        try {
            String ftpSeverIp = cqctccSSOProperties.getFtpSeverIp();
            String ftpServerPort = cqctccSSOProperties.getFtpServerPort();
            String ftpUserName = cqctccSSOProperties.getFtpUserName();
            String ftpPassword = cqctccSSOProperties.getFtpPassword();
            int ftpRetryTimes = Objects.isNull(cqctccSSOProperties.getFtpRetryTimes()) ? 3 : cqctccSSOProperties.getFtpRetryTimes();
            // 连接到 FTP 服务器
            ftpClient.connect(ftpSeverIp, Integer.parseInt(ftpServerPort));
            log.info("<uploadFileToFTP> connect ftp start==>");
            ftpClient.login(ftpUserName, ftpPassword);
            log.info("<uploadFileToFTP> connect ftp success ");
            // 切换到被动模式
            log.info("<uploadFileToFTP> 切换被动模式");
            ftpClient.enterLocalPassiveMode();
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.setDataTimeout(Duration.ofSeconds(30)); // 设置数据超时时间为 30秒
            // 切换到指定工作目录
            if (!changeDirectory(ftpClient, ftpPath)) {
                throw new BusinessException("重庆电信FTP切换目录失败");
            }
            // 尝试上传文件
            for (int i = 0; i < ftpRetryTimes; i++) {
                log.info("<uploadFileToFTP> 第{}次开始上传", i + 1);
                // 读取本地文件
                try (FileInputStream inputStream = new FileInputStream(file)) {
                    // 上传文件
                    uploaded = ftpClient.storeFile(file.getName(), inputStream);
                    if (uploaded) {
                        log.info("<uploadFileToFTP> File uploaded successfully:");
                        break;
                    } else {
                        log.error("<uploadFileToFTP> Failed to upload file, retrying...");
                    }
                } catch (IOException e) {
                    log.error("<uploadFileToFTP> upload Failed: ", e);
                    Thread.sleep(2000);
                }
            }
        } catch (IOException e) {
            log.error("<uploadFileToFTP> uploadFileToFTP Failed: ", e);
        } finally {
            ftpClient.logout();
            ftpClient.disconnect();
        }
        if (!uploaded) {
            throw new BusinessException("重庆电信上传FTP失败");
        }
    }

    // 切换到指定的 FTP 目录
    private boolean changeDirectory(FTPClient ftpClient, String ftpPath) {
        try {
            if (ftpClient.changeWorkingDirectory(ftpPath)) {
                log.info("<uploadFileToFTP> Changed to directory:{}", ftpPath);
                return true;
            } else {
                log.error("<uploadFileToFTP> Failed to change to directory:{}", ftpPath);
            }
        } catch (IOException e) {
            log.error(String.format("<uploadFileToFTP> Failed to change to directory:%s error: ", ftpPath), e);
        }
        return false;
    }

}
