package com.siteweb.admin.sso.cqctcc.dto;

import lombok.Data;

/**
 * 调用工作助手获取用户信息接口参数
 * Creation Date: 2025/3/26
 */
@Data
public class UserCallbackHttpRequest {
    private BaseInfo baseInfo;
    private BizInfo bizInfo;

    @Data
    public static class BaseInfo {
        /**
         * 时间戳(yyyyMMddHHmmss)
         */
        private String time;
        /**
         * md5(key+appId+time)，key 由工作助手为每个集约系统分配。
         */
        private String token;
        /**
         * 工作助手系统为每个集约系统分配的唯一标识
         */
        private String appId;
    }

    @Data
    public static class BizInfo {
        private VoAuth voAuth;
    }

    @Data
    public static class VoAuth {
        /**
         * 前置接口给单点系统传递的用户标识
         */
        private String ssoToken;
    }

    public static UserCallbackHttpRequest.BaseInfo createBaseInfo(String time, String token, String appId) {
        UserCallbackHttpRequest.BaseInfo baseInfo = new UserCallbackHttpRequest.BaseInfo();
        baseInfo.setTime(time);
        baseInfo.setToken(token);
        baseInfo.setAppId(appId);
        return baseInfo;
    }

    public static UserCallbackHttpRequest.BizInfo createBizInfo(String ssoToken) {
        UserCallbackHttpRequest.VoAuth voAuth = new UserCallbackHttpRequest.VoAuth();
        voAuth.setSsoToken(ssoToken);
        UserCallbackHttpRequest.BizInfo bizInfo = new UserCallbackHttpRequest.BizInfo();
        bizInfo.setVoAuth(voAuth);
        return bizInfo;
    }
}
