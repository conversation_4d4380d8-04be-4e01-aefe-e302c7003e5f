package com.siteweb.admin.sso.codesso;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 定制需求：中煤单点登录认证或其他通过请求第三方接口验证token返回账户名的免密登录场景
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class CodeSSOController {

    public static final String LOGIN_CODESSO_URL = "login.codesso.url";
    public static final String LOGIN_CODESSO_CODENAME = "login.codesso.codeName";
    public static final String LOGIN_CODESSO_USERNANE = "login.codesso.userName";
    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    TokenUtil tokenUtil;

    @Autowired
    AccountService accountService;
//    http://bms/sso.html?supos-token=xxx,前端解析supos-token
//    调用s6后端新写的接口/codesso/login?code=xxx，传入supos-token到code
//    后端接口调用第三方http://iip.chinacoalsx.com:8080/apps/ccsx-gatewayservice/api/sso/auth/userInfo?supos_token=xxxxx获取账号名
//    如果账号名在tbl_account表中存在，免密登录
    @GetMapping("/codesso/login")
    public ResponseEntity<ResponseResult> LoginCodeSSO(@RequestParam String code) {
        List<String> systemConfigKeyList = new ArrayList<>();
        systemConfigKeyList.add(LOGIN_CODESSO_CODENAME);
        systemConfigKeyList.add(LOGIN_CODESSO_USERNANE);
        systemConfigKeyList.add(LOGIN_CODESSO_URL);
        List<SystemConfig> systemConfigList = systemConfigService.findBySystemConfigKeys(systemConfigKeyList);
        SystemConfig codeName = systemConfigList.stream().filter(c -> StringUtils.equals(c.getSystemConfigKey(), LOGIN_CODESSO_CODENAME)).toList().get(0);
        SystemConfig userName = systemConfigList.stream().filter(c -> StringUtils.equals(c.getSystemConfigKey(), LOGIN_CODESSO_USERNANE)).toList().get(0);
        SystemConfig url = systemConfigList.stream().filter(c -> StringUtils.equals(c.getSystemConfigKey(), LOGIN_CODESSO_URL)).toList().get(0);

        if (codeName == null || userName == null || url == null ||
                codeName.getSystemConfigValue().trim().isEmpty() ||
                userName.getSystemConfigValue().trim().isEmpty() ||
                url.getSystemConfigValue().trim().isEmpty()
        ) {
            log.error("单点登录相关系统参数未设置");
            return ResponseHelper.failed("单点登录相关系统参数(login.codesso.url/codeName/userName)未设置");
        }
        //http://iip.chinacoalsx.com:8080/apps/ccsx-gatewayservice/api/sso/auth/userInfo?supos_token=xxxxx
        String response = HttpUtil.get(url.getSystemConfigValue(), Map.of(codeName.getSystemConfigValue(), code));
//        response
//        {
//            "code":10000,
//            "msg":"success",
//            "data":"{\"username\":\"caoyd1\",\"accountType\":0,\"lockStatus\":0,\"valid\":null,\"personCode\":\"152727198808101015\",\"personName\":\"曹彦东\",\"createTime\":\"2022-09-06T10:25:56.000+0000\",\"modifyTime\":\"2023-11-22T06:05:39.000+0000\",\"userRoleList\":[{\"name\":\"systemRole\",\"showName\":\"管理员角色\"},{\"name\":\"zjlxx\",\"showName\":\"总经理信箱\"},{\"name\":\"360_net_disk_user\",\"showName\":\"360网盘用户\"},{\"name\":\"jwxdsjfxpt\",\"showName\":\"检维修大数据分析平台\"},{\"name\":\"tzcglpt\",\"showName\":\"碳资产管理平台\"},{\"name\":\"rydwxt\",\"showName\":\"人员定位系统\"},{\"name\":\"rtls_staff_user_601\",\"showName\":\"人员定位系统普通员工\"},{\"name\":\"ca01111\",\"showName\":\"隐患上报全员\"},{\"name\":\"bgldzs\",\"showName\":\"特殊应用\"},{\"name\":\"000100\",\"showName\":\"信息管理部\"},{\"name\":\"cceeadmin\",\"showName\":\"管理员\"},{\"name\":\"sxgssykjkpt\",\"showName\":\"陕西公司双预控监控平台\"},{\"name\":\"juese1\",\"showName\":\"个人工作台\"},{\"name\":\"rtls_admin_201\",\"showName\":\"人员定位系统管理员\"},{\"name\":\"address_book_manager\",\"showName\":\"通讯录普通管理员\"},{\"name\":\"outsourcing\",\"showName\":\"外委人员\"},{\"name\":\"xxglb\",\"showName\":\"信息管理部\"}]}"
//        }
        log.info("远程调用api/codesso/login的响应为:{}", response);
        //解析
        final var jsonConfig = new JSONConfig();
        jsonConfig.setIgnoreNullValue(true);
        JSONObject jsonBody = JSONUtil.parseObj(response, jsonConfig);
        String data = jsonBody.getStr("data");
        JSONObject dataJsonObj = JSONUtil.parseObj(data, jsonConfig);
        String accountName = dataJsonObj.getStr(userName.getSystemConfigValue());
        List<AccountDTO> accounts = accountService.findByLogonId(accountName);
        if (CollUtil.isEmpty(accounts)) {
            log.error("单点登录失败，账号不存在");
            return ResponseHelper.failed("账号不存在");
        }
        Map<String, Object> tokenResult = new HashMap<>();
        TokenUser tokenUser = new TokenUser(accounts.get(0));
        String newToken = tokenUtil.createTokenForUser(tokenUser, "web");
        tokenResult.put("token", newToken);
        tokenResult.put("UserName", tokenUser.getUser().getLogonId());
        tokenResult.put("LogonId", tokenUser.getUser().getLogonId());
        tokenResult.put("role", tokenUser.getRole());
        tokenResult.put("userId", tokenUser.getUserId());
        tokenResult.put("personId", tokenUser.getUserId());
        tokenResult.put("themeName", tokenUser.getUser().getThemeName());
        return ResponseHelper.successful(tokenResult);
    }
}