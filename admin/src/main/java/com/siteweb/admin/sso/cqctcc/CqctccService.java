package com.siteweb.admin.sso.cqctcc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.symmetric.DES;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.dto.CQCTCCAccountSyncRequest;
import com.siteweb.admin.dto.CQCTCCAccountSyncResponse;
import com.siteweb.admin.entity.Account;
import com.siteweb.admin.entity.CqctccLogin;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.mapper.CqctccLoginMapper;
import com.siteweb.admin.mapper.EmployeeMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.UserConfigService;
import com.siteweb.admin.sso.cqctcc.dto.UserCallbackHttpRequest;
import com.siteweb.admin.sso.cqctcc.dto.UserCallbackHttpResponse;
import com.siteweb.admin.sso.cqctcc.dto.UserCallbackParamDTO;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.properties.CqctccSSOProperties;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.AESUtil;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 定制需求：重庆电信单点登录相关逻辑
 */
@Service
@Slf4j
public class CqctccService {
    @Autowired
    CqctccSSOProperties cqctccSSOProperties;
    @Lazy
    @Autowired
    AccountService accountService;
    @Autowired
    AccountMapper accountMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    TokenUtil tokenUtil;
    @Autowired
    CqctccLoginMapper cqctccLoginMapper;
    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    UserConfigService userConfigService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    AESUtil aesUtil;
    @Value("${aes-key}")
    String aesKey;
    private static final AtomicInteger counter = new AtomicInteger(0);
    private static final int MAX_COUNTER = 999999; // 最大限制值
    /**
     * 请求超时限制
     */
    public static final int REQUEST_TIMEOUT = 20000;
    /**
     * A：创建标识
     */
    public static final String ADD_ACTION = "A";
    /**
     * D：注销标识
     */
    public static final String DELETE_ACTION = "D";


    /**
     * 账户验证
     *
     * @param userName    人员姓名
     * @param phoneNumber 手机号
     */
    public ResponseEntity<ResponseResult> verifyAccount(@RequestParam String userName,
                                                        @RequestParam String phoneNumber) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        // 系统编码
        String sysCode = cqctccSSOProperties.getSysCode();
        String accountInfoSuffixUrl = cqctccSSOProperties.getAccountInfoUrl();
        String accountInfoUrl = String.format("%s", accountInfoSuffixUrl);
        String accountInfoDesKey = cqctccSSOProperties.getAccountInfoDesKey();
        log.info("<verifyAccount>accountInfoUrl===>{}, accountInfoDesKey==>{}", accountInfoUrl, accountInfoDesKey);
        String appId = cqctccSSOProperties.getAppId();
        String appKey = cqctccSSOProperties.getAppKey();
        log.info("<verifyAccount>appId==>{}, appKey==>{}", appId, appKey);
        // 校验参数
        ResponseEntity<ResponseResult> validParamResponse = verifyAccountParam(sysCode, accountInfoUrl, accountInfoDesKey, appId, appKey);
        if (validParamResponse != null) return validParamResponse;
        // 获取流水号
        String transactionId = getTransactionId(sysCode);
        log.info("<verifyAccount>transactionId===>{}", transactionId);
        // 构造参数
        String requestTime = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        Map<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("transaction_id", transactionId);
        paramMap.put("user_name", userName);
        paramMap.put("phone_number", phoneNumber);
        paramMap.put("target_sys_code", sysCode);
        paramMap.put("request_time", requestTime);
        // 将请求报文中除sign节点以外的信息通过DES加密生成数字签名
        DES des = SecureUtil.des(accountInfoDesKey.getBytes());
        String sign = des.encryptBase64(JSONUtil.toJsonStr(paramMap)); // 加密后使用Base64编码
        paramMap.put("sign", sign);
        String paramJson = JSONUtil.toJsonStr(paramMap);
        log.info("<verifyAccount>开始远程调用人员主账号信息查询接口,paramJson===>{}", paramJson);
        String response = null;
        try {
            response = HttpRequest.post(accountInfoUrl)
                    .header("X-APP-ID", appId)//头信息，多个头信息多次调用此方法即可
                    .header("X-APP-KEY", appKey)
                    .body(paramJson)
                    .timeout(REQUEST_TIMEOUT)//超时，毫秒
                    .execute().body();
        } catch (Exception e) {
            log.error("<verifyAccount>远程调用接口失败 accountInfoUrl:{}, paramJson: {}, exception:", accountInfoUrl, paramJson, e);
            throw new BusinessException("远程调用人主账户接口失败");
        }
        log.info("<verifyAccount>请求成功 response===>{}", response);
        JSONObject jsonBody = JSONUtil.parseObj(response);
        String rspCode = jsonBody.getStr("rsp_code");
        return switch (rspCode) {
            case "0000" -> {
                String result = jsonBody.getStr("result");
                JSONObject resultData = JSONUtil.parseObj(result);
                yield ResponseHelper.successful(resultData);
            }
            case "0002" -> {
                log.error("<verifyAccount>人主平台找不到此人信息 response==>{}", jsonBody);
                yield ResponseHelper.failed("人主平台找不到此人信息");
            }
            default -> {
                log.error("<verifyAccount>返回失败 response==>{}", jsonBody);
                yield ResponseHelper.failed("返回失败, " + jsonBody);
            }
        };
    }

    /**
     * 校验参数
     */
    private ResponseEntity<ResponseResult> verifyAccountParam(String sysCode, String accountInfoUrl, String accountInfoDesKey, String appId, String appKey) {
        boolean generalParam = verifyGeneralParam(sysCode, appId, appKey);
        if (!generalParam) {
            return ResponseHelper.failed("单点登录通用参数未设置");
        }
        if (CharSequenceUtil.hasBlank(accountInfoUrl, accountInfoDesKey)) {
            log.error("单点登录账户校验url相关参数未设置 accountInfoUrl:" + accountInfoUrl + ", accountInfoDesKey:" + accountInfoDesKey);
            return ResponseHelper.failed("单点登录账户校验url相关参数未设置");
        }
        return null;
    }

    /**
     * 获取流水号 客户端保证每次请求都唯一。
     * 26位字符串（六位目标系统编码+14位时间戳（YYYYMMDDHHmmss）+6位序列值）
     *
     * @param sysCode 系统编码
     */
    private String getTransactionId(String sysCode) {
        // 获取当前时间的14位时间戳
        String timestamp = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        // 生成一个6位的序列值，当计数器超过某个值时，重置为 0
        int sequence = counter.incrementAndGet();
        if (sequence > MAX_COUNTER) {
            counter.set(0);
            sequence = counter.incrementAndGet();
        }
        String sequenceNo = CharSequenceUtil.padPre(String.valueOf(sequence), 6, '0');
        return String.format("%s%s%s", sysCode, timestamp, sequenceNo);
    }

    private boolean verifyGeneralParam(String sysCode, String appId, String appKey) {
        if (CharSequenceUtil.isBlank(sysCode)) {
            log.error("单点登录系统编码参数未设置");
            return false;
        }
        if (CharSequenceUtil.hasBlank(appId, appKey)) {
            log.error("单点登录应用id,key未设置");
            return false;
        }
        return true;
    }

    public ResponseEntity<ResponseResult> loginCqctccSSO(String token, String account, String index) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        // 调用统一凭证校验接口远程验证
        ResponseEntity<ResponseResult> validParamResponse = validRemoteCQCTCC(token, account, index);
        if (validParamResponse != null) {
            return validParamResponse;
        }
        log.info("<loginCqctccSSO>远程验证成功，开始生成token");
        String tokenValidateDesKey = cqctccSSOProperties.getTokenValidateDesKey();
        if (CharSequenceUtil.isBlank(tokenValidateDesKey)) {
            log.error("<loginCqctccSSO>统一凭证校验接口DES密钥不存在");
            return ResponseHelper.failed("统一凭证校验接口DES密钥不存在");
        }
        // 使用base64解码，再用密钥解密就是登录名
        DES des = SecureUtil.des(tokenValidateDesKey.getBytes());
        log.info("<loginCqctccSSO>开始账户解密, account= " + account);
        String logonId = null;
        try {
            logonId = des.decryptStr(account);
        } catch (Exception e) {
            log.error(String.format("<loginCqctccSSO>账户解密失败, account=%s ", account), e);
            return ResponseHelper.failed("账户解密失败，请验证account字段和密钥");
        }
        log.info("<loginCqctccSSO>账户解密成功");
        List<AccountDTO> accounts = accountService.findByLogonId(logonId);
        if (CollUtil.isEmpty(accounts)) {
            log.error("<loginCqctccSSO>单点登录失败，账号不存在 logonId：" + logonId);
            return ResponseHelper.failed("账号不存在");
        }
        Map<String, Object> tokenResult = new HashMap<>();
        TokenUser tokenUser = new TokenUser(accounts.get(0));
        String newToken = tokenUtil.createTokenForUser(tokenUser, "web");
        tokenResult.put("token", newToken);
        tokenResult.put("UserName", tokenUser.getUser().getLogonId());
        tokenResult.put("LogonId", tokenUser.getUser().getLogonId());
        tokenResult.put("role", tokenUser.getRole());
        tokenResult.put("userId", tokenUser.getUserId());
        tokenResult.put("personId", tokenUser.getUserId());
        tokenResult.put("themeName", userConfigService.findUserTheme(tokenUser.getUserId()));
        return ResponseHelper.successful(tokenResult);
    }

    /**
     * 调用统一凭证校验接口远程验证
     * 远程调用成功返回如下
     * String response = """
     * {
     * "transaction_id": " CQBSN020181105134911123456",
     * "response_time": "2018-10-23 21:05:25",
     * "rsp_code": "0000",
     * "rsp_desc": "成功"
     * }
     * """;
     */
    private ResponseEntity<ResponseResult> validRemoteCQCTCC(String token, String account, String index) {
        // 系统编码
        String sysCode = cqctccSSOProperties.getSysCode();
        String apiBaseUrl = cqctccSSOProperties.getApiBaseUrl();
        String tokenValidateSuffixUrl = cqctccSSOProperties.getTokenValidateUrl();
        String tokenValidateUrl = String.format("%s%s", apiBaseUrl, tokenValidateSuffixUrl);
        log.info("<loginCqctccSSO>tokenValidateUrl===>{}", tokenValidateUrl);
        String appId = cqctccSSOProperties.getAppId();
        String appKey = cqctccSSOProperties.getAppKey();
        log.info("<loginCqctccSSO>appId==>{}, appKey==>{}", appId, appKey);
        // 校验参数
        ResponseEntity<ResponseResult> validParamResponse = verifyLoginCqctccParam(sysCode, tokenValidateUrl, appId, appKey);
        if (validParamResponse != null) return validParamResponse;
        // 获取流水号
        String transactionId = getTransactionId(sysCode);
        log.info("<loginCqctccSSO>transactionId===>{}", transactionId);
        // 构造参数
        String requestTime = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("transaction_id", transactionId);
        paramMap.put("token", token);
        paramMap.put("main_acct_code", account);
        paramMap.put("index", index);
        paramMap.put("request_time", requestTime);
        log.info("<loginCqctccSSO>paramMap===>{}", paramMap);
        log.info("<loginCqctccSSO>开始远程调用凭证校验接口===>");
        String response = null;
        try {
            response = HttpRequest.post(tokenValidateUrl)
                    .header("X-APP-ID", appId)//头信息，多个头信息多次调用此方法即可
                    .header("X-APP-KEY", appKey)
                    .body(JSONUtil.toJsonStr(paramMap))
                    .timeout(REQUEST_TIMEOUT)//超时，毫秒
                    .execute().body();
        } catch (Exception e) {
            log.error("<loginCqctccSSO>远程调用接口失败:", e);
            throw new BusinessException("远程调用接口失败");
        }
        log.info("<loginCqctccSSO>请求成功 response===>{}", response);
        JSONObject jsonBody = JSONUtil.parseObj(response);
        String rspCode = jsonBody.getStr("rsp_code");
        if (!"0000".equals(rspCode)) {
            // rsp_code不为0000时，返回响应编码的错误描述信息
            log.error("<loginCqctccSSO> response==>{}", response);
            return ResponseHelper.failed("票据认证异常： " + response);
        }
        return null;
    }

    /**
     * 校验登录参数
     */
    private ResponseEntity<ResponseResult> verifyLoginCqctccParam(String sysCode, String tokenValidateUrl, String appId, String appKey) {
        boolean generalParam = verifyGeneralParam(sysCode, appId, appKey);
        if (!generalParam) {
            return ResponseHelper.failed("单点登录通用参数未设置");
        }
        if (CharSequenceUtil.isBlank(tokenValidateUrl)) {
            log.error("单点登录校验url相关参数未设置 tokenValidateUrl:" + tokenValidateUrl);
            return ResponseHelper.failed("单点登录校验url相关参数未设置");
        }
        return null;
    }

    /**
     * 验证登录用户的标志位，存在才可登录，默认不能使用s6登录
     */
    public boolean checkUserLogin(String logonId) {
        List<AccountDTO> accounts = accountService.findByLogonId(logonId);
        if (CollUtil.isEmpty(accounts)) {
            throw new BusinessException("单点登录失败，账号不存在");
        }
        AccountDTO accountDTO = accounts.get(0);
        if (accountDTO.getUserId() > 0) {
            CqctccLogin cqctccLogin = cqctccLoginMapper.selectById(logonId);
            return Objects.nonNull(cqctccLogin) && cqctccLogin.getEnable();
        }
        return true;
    }

    /**
     * 修改重庆电信登录标志位
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAccoutLogin(CqctccLogin cqctccLogin) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        cqctccLoginMapper.deleteById(cqctccLogin.getLogonId());
        if (Objects.nonNull(cqctccLogin.getEnable())) {
            cqctccLoginMapper.insert(cqctccLogin);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<CQCTCCAccountSyncResponse> cqctccAccountSync(CQCTCCAccountSyncRequest request) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        String logonId = request.getMainAccountCode();
        if (CharSequenceUtil.isBlank(logonId)) {
            return CQCTCCAccountSyncResponse.failedRequest(request.getTransactionId(), "3001", "账户名不能为空");
        }
        Long count = accountMapper.selectCount(Wrappers.lambdaQuery(Account.class).eq(Account::getLogonId, logonId));
        if (count < 1) {
            log.warn(String.format("<cqctccAccountSync> account does not exist logonId:%s", logonId));
            return CQCTCCAccountSyncResponse.successful(request.getTransactionId(), "1001", "账号不存在");
        }
        if ("M".equals(request.getActionCode())) {
            // 修改
            String phoneNumber;
            String userName;
            try {
                phoneNumber = TripleDESUtil.decrypt(request.getPhoneNumber());
                userName = TripleDESUtil.decrypt(request.getUserName());
            } catch (Exception e) {
                log.error(String.format("<cqctccAccountSync> des error phoneNumber:%s,userName:%s", request.getPhoneNumber(), request.getUserName()), e);
                return CQCTCCAccountSyncResponse.failedRequest(request.getTransactionId(), "3001", "DES解密失败");
            }
            try {
                accountMapper.update(Wrappers.lambdaUpdate(Account.class)
                        .set(Account::getUserName, userName)
                        .eq(Account::getLogonId, logonId));
                employeeMapper.updateByCqctccLogonId(logonId, userName, phoneNumber);
                securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, String.format("CQCTCC修改账户: logonId=%s,userName=%s,phoneNumber=%s", logonId, userName, phoneNumber));
            } catch (Exception e) {
                log.error(String.format("<cqctccAccountSync> update Account error logonId:%s, phoneNumber:%s,userName:%s", logonId, phoneNumber, userName), e);
                return CQCTCCAccountSyncResponse.failedResponse(request.getTransactionId());
            }
        }
        if ("D".equals(request.getActionCode())) {
            // 注销
            try {
                accountMapper.update(Wrappers.lambdaUpdate(Account.class)
                        .set(Account::getEnable, false)
                        .eq(Account::getLogonId, logonId));
                securityAuditManager.recordAuditReport(AuditReportTypeEnum.ACCOUNT, String.format("CQCTCC注销账户: logonId=%s", logonId));
            } catch (Exception e) {
                log.error(String.format("<cqctccAccountSync> logout Account error logonId:%s", logonId), e);
                return CQCTCCAccountSyncResponse.failedResponse(request.getTransactionId());
            }
        }
        return CQCTCCAccountSyncResponse.successful(request.getTransactionId());
    }

    /**
     * 5.1.4. 主账号目标系统登录权限控制接口
     * 本接口用于创建或注销人员主数据平台主帐号登录各个目标系统的权限。
     *
     * @param actionCode A：创建，D：注销
     */
    public void loginPrivControl(String logonId, String actionCode) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        // 系统编码
        String sysCode = cqctccSSOProperties.getSysCode();
        String apiBaseUrl = cqctccSSOProperties.getApiBaseUrl();
        String appId = cqctccSSOProperties.getAppId();
        String appKey = cqctccSSOProperties.getAppKey();
        String loginPrivSuffixUrl = cqctccSSOProperties.getLoginPrivUrl();
        String loginPrivDesKey = cqctccSSOProperties.getLoginPrivDesKey();
        // 校验参数
        boolean generalParam = verifyGeneralParam(sysCode, appId, appKey);
        if (!generalParam && CharSequenceUtil.isBlank(loginPrivSuffixUrl) && CharSequenceUtil.isBlank(loginPrivDesKey)) {
            log.error(String.format("<loginPrivControl>loginPrivSuffixUrl:%s, loginPrivDesKey:%s", loginPrivSuffixUrl, loginPrivDesKey));
            throw new BusinessException("主账号目标系统登录权限控制接口通用参数未设置");
        }
        String loginPrivUrl = String.format("%s%s", apiBaseUrl, loginPrivSuffixUrl);
        // 获取流水号
        String transactionId = getTransactionId(sysCode);
        // 构造参数
        String requestTime = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("transaction_id", transactionId);
        paramMap.put("main_acct_code", logonId);
        paramMap.put("action_code", actionCode);
        paramMap.put("target_sys_code", sysCode);
        paramMap.put("request_time", requestTime);
        // 将请求报文中除sign节点以外的信息通过DES加密生成数字签名
        String paramJson = JSONUtil.toJsonStr(paramMap);
        log.info("<loginPrivControl>开始对报文进行加密 loginPrivDesKey===>{}", loginPrivDesKey);
        DES des = SecureUtil.des(loginPrivDesKey.getBytes());
        String sign = des.encryptBase64(paramJson); // 加密后使用Base64编码
        paramMap.put("sign", sign);
        String paramJsonStr = JSONUtil.toJsonStr(paramMap);
        log.info("<loginPrivControl>paramJsonStr===>{}", paramJsonStr);
        log.info("<loginPrivControl>开始远程调用接口===>");
        String response;
        try {
            response = HttpRequest.post(loginPrivUrl)
                    .header("X-APP-ID", appId)//头信息，多个头信息多次调用此方法即可
                    .header("X-APP-KEY", appKey)
                    .body(paramJsonStr)
                    .timeout(REQUEST_TIMEOUT)//超时，毫秒
                    .execute().body();
        } catch (HttpException e) {
            log.error("<loginPrivControl>远程调用接口失败:", e);
            throw new BusinessException("远程调用接口失败");
        }
        log.info("<loginPrivControl>请求成功 response===>{}", response);
        JSONObject jsonBody = JSONUtil.parseObj(response);
        String rspCode = jsonBody.getStr("rsp_code");
        if (!"0000".equals(rspCode)) {
            // rsp_code不为0000时，返回响应编码的错误描述信息
            throw new BusinessException("<loginPrivControl>人主控制登录权限异常：" + response);
        }
    }

    /**
     * 5.1.3. 统一登录认证接口
     *
     * @param mainAccountPwd 统一认证账号密码
     */
    public void loginValidate(String logonId, String mainAccountPwd) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        // 系统编码
        String sysCode = cqctccSSOProperties.getSysCode();
        String apiBaseUrl = cqctccSSOProperties.getApiBaseUrl();
        String appId = cqctccSSOProperties.getAppId();
        String appKey = cqctccSSOProperties.getAppKey();
        String loginValidateSuffixUrl = cqctccSSOProperties.getLoginValidateUrl();
        String loginValidateDesKey = cqctccSSOProperties.getLoginValidateDesKey();
        // 校验参数
        boolean generalParam = verifyGeneralParam(sysCode, appId, appKey);
        if (!generalParam && CharSequenceUtil.isBlank(loginValidateSuffixUrl) && CharSequenceUtil.isBlank(loginValidateDesKey)) {
            log.error(String.format("<loginValidate>loginValidateSuffixUrl:%s, loginValidateDesKey:%s", loginValidateSuffixUrl, loginValidateDesKey));
            throw new BusinessException("统一登录认证接口参数未设置");
        }
        String loginValidateUrl = String.format("%s%s", apiBaseUrl, loginValidateSuffixUrl);
        // 获取流水号
        String transactionId = getTransactionId(sysCode);
        log.info("<loginValidate>transactionId===>{}", transactionId);
        // 构造参数
        String requestTime = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("transaction_id", transactionId);
        paramMap.put("main_acct_code", logonId);
        paramMap.put("main_account_pwd", mainAccountPwd);
        paramMap.put("request_time", requestTime);
        // 将请求报文中除sign节点以外的信息通过DES加密生成数字签名
        String paramJson = JSONUtil.toJsonStr(paramMap);
        DES des = SecureUtil.des(loginValidateDesKey.getBytes());
        String sign = des.encryptBase64(paramJson); // 加密后使用Base64编码
        paramMap.put("sign", sign);
        log.info("<loginValidate>paramMap===>{}", paramMap);
        log.info("<loginValidate>开始远程调用统一登录认证接口===>");
        String response = HttpRequest.post(loginValidateUrl)
                .header("X-APP-ID", appId)//头信息，多个头信息多次调用此方法即可
                .header("X-APP-KEY", appKey)
                .body(JSONUtil.toJsonStr(paramMap))
                .timeout(REQUEST_TIMEOUT)//超时，毫秒
                .execute().body();
        log.info("<loginCqctccSSO>请求成功 response===>{}", response);
        JSONObject jsonBody = JSONUtil.parseObj(response);
        String rspCode = jsonBody.getStr("rsp_code");
        if (!"0000".equals(rspCode)) {
            // rsp_code不为0000时，返回响应编码的错误描述信息
            log.error("<loginCqctccSSO> response==>{}", response);
            throw new BusinessException("<loginValidate>票据认证异常：" + response);
        }
    }


    public Boolean getAccoutLogin(String logonId) {
        if (!Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            throw new BusinessException("重庆电信单点登录未启动");
        }
        if (CharSequenceUtil.isBlank(logonId)) {
            return null;
        }
        CqctccLogin cqctccLogin = cqctccLoginMapper.selectById(logonId);
        if (Objects.isNull(cqctccLogin)) {
            return null;
        }
        return cqctccLogin.getEnable();
    }

    public ResponseEntity<ResponseResult> getUserCallback(UserCallbackParamDTO userCallbackParamDTO) {
        if (CharSequenceUtil.isBlank(userCallbackParamDTO.getSsoToken())) {
            log.error("<getUserCallback> ssoToken不能为空");
            throw new BusinessException("ssoToken不能为空");
        }
        String userCallbackUrl = getSystemConfigValue(SystemConfigEnum.CQCTCC_APP_SSO_USERCALLBACKURL);
        if (CharSequenceUtil.isBlank(userCallbackUrl)) {
            log.error("<getUserCallback> ssoToken不能为空");
            throw new BusinessException("调用工作助手获取用户信息的URL未配置");
        }
        // 获取当前时间的14位时间戳
        String time = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
        String key = getSystemConfigValue(SystemConfigEnum.CQCTCC_APP_SSO_KEY);
        String appId = getSystemConfigValue(SystemConfigEnum.CQCTCC_APP_SSO_APPID);
        String unEnToken = key + appId + time;
        log.info("<getUserCallback> 未加密的token: {}", unEnToken);
        String token = DigestUtil.md5Hex(unEnToken);
        log.info("<getUserCallback> md5加密后的token: {}", token);
        // 构造传参
        UserCallbackHttpRequest userCallbackHttpRequest = new UserCallbackHttpRequest();
        UserCallbackHttpRequest.BaseInfo baseInfo = UserCallbackHttpRequest.createBaseInfo(time, token, appId);
        UserCallbackHttpRequest.BizInfo bizInfo = UserCallbackHttpRequest.createBizInfo(userCallbackParamDTO.getSsoToken());
        userCallbackHttpRequest.setBaseInfo(baseInfo);
        userCallbackHttpRequest.setBizInfo(bizInfo);
        String paramJson = JSONUtil.toJsonStr(userCallbackHttpRequest);
        log.info("<getUserCallback>开始远程工作助手获取用户信息接口===paramJson：{}", paramJson);
        String response;
        try {
            response = HttpRequest.post(userCallbackUrl)
                    .body(paramJson)
                    .timeout(REQUEST_TIMEOUT)//超时，毫秒
                    .execute().body();
        } catch (Exception e) {
            log.error("<getUserCallback> 远程调用接口失败 userCallbackUrl:{}, paramJson: {}, exception:", userCallbackUrl, paramJson, e);
            throw new BusinessException("远程调用接口失败");
        }
        log.info("<getUserCallback> 远程调用成功 接口返回 response===>{}", response);
        UserCallbackHttpResponse userResponse = JSONUtil.toBean(response, UserCallbackHttpResponse.class);
        if (!Boolean.TRUE.equals(userResponse.isSuccess())) {
            log.error("<getUserCallback> 远程调用接口响应异常 userCallbackUrl:{}, paramJson: {}, response:{}", userCallbackUrl, paramJson, response);
            return ResponseHelper.failed("远程调用接口响应异常： " + response);
        }
        UserCallbackHttpResponse.UserObject userObject = userResponse.getObject();
        if (Objects.isNull(userObject) || CharSequenceUtil.isBlank(userObject.getProductCode())) {
            log.error("<getUserCallback> 远程调用获取用户名为空 userCallbackUrl:{}, paramJson: {}, response:{}", userCallbackUrl, paramJson, response);
            return ResponseHelper.failed("远程调用获取用户名为空");
        }
        List<AccountDTO> accounts = accountService.findByLogonId(userObject.getProductCode());
        if (CollUtil.isEmpty(accounts)) {
            log.error("<getUserCallback> 单点登录失败，账号不存在 response:{}", response);
            return ResponseHelper.failed("账号不存在");
        }
        log.info("<getUserCallback> 获取用户信息成功，开始构造token");
        Map<String, Object> tokenResult = new HashMap<>();
        TokenUser tokenUser = new TokenUser(accounts.get(0));
        String newToken = tokenUtil.createTokenForUser(tokenUser, "app");
        tokenResult.put("token", newToken);
        tokenResult.put("UserName", tokenUser.getUser().getLogonId());
        tokenResult.put("LogonId", tokenUser.getUser().getLogonId());
        tokenResult.put("role", tokenUser.getRole());
        tokenResult.put("userId", tokenUser.getUserId());
        tokenResult.put("personId", tokenUser.getUserId());
        tokenResult.put("themeName", userConfigService.findUserTheme(tokenUser.getUserId()));
        tokenResult.put("key", aesUtil.encrypt(key, aesKey));
        tokenResult.put("userResponse", userResponse);
        return ResponseHelper.successful(tokenResult);
    }

    public String getSystemConfigValue(SystemConfigEnum systemConfigEnum) {
        SystemConfig alarmLightMessageSystemConfig = systemConfigService.findBySystemConfigKey(systemConfigEnum.getSystemConfigKey());
        if (Objects.nonNull(alarmLightMessageSystemConfig) && CharSequenceUtil.isNotBlank(alarmLightMessageSystemConfig.getSystemConfigValue())) {
            return alarmLightMessageSystemConfig.getSystemConfigValue().trim();
        }
        return null;
    }
}
