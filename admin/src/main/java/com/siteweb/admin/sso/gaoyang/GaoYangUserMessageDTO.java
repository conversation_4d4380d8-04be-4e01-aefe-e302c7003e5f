package com.siteweb.admin.sso.gaoyang;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParseException;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;
/**
 * @Description: 高阳单点登录定制：rabbit消息类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GaoYangUserMessageDTO {
    /**
     * 消息类型：操作类型，包括新增（add）、修改（update）、删除（del）。
     */
    private String type;

    /**
     * 用户ID。
     */
    private Integer userId;

    /**
     * 用户名。
     */
    private String username;

    /**
     * 用户手机号码。
     */
    private String phone;

    /**
     * 用户邮箱地址。
     */
    private String email;

    /**
     * 用户真实姓名。
     */
    private String realname;

    /**
     * 用户性别：0-男，1-女。
     */
    private String sex;

    /**
     * 校验必须包含的字段
     */
    public void validate() {
        if (StringUtils.isEmpty(type)) {
            throw new BusinessException("Field 'type' must not be null");
        }
        if (StringUtils.isEmpty(username)) {
            throw new BusinessException("Field 'username' must not be null");
        }
        if (StringUtils.isEmpty(realname)) {
            throw new BusinessException("Field 'realname' must not be null");
        }
        if (StringUtils.isEmpty(sex)) {
            throw new BusinessException("Field 'sex' must not be null");
        }
    }

}
