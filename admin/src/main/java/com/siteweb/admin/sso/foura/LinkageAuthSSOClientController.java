package com.siteweb.admin.sso.foura;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定制需求：新疆移动4A认证
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class LinkageAuthSSOClientController {

    //    public static final String LOGIN_FOUR_A_REDIRECT_URL = "login.4a.xinjiang.redirectUrl";
    public static final String LOGIN_FOUR_A_URL = "login.4a.xinjiang.url";
    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    TokenUtil tokenUtil;

    @Autowired
    AccountService accountService;

    @GetMapping("/foura/login.do")
    public ResponseEntity<ResponseResult> Login(@RequestParam(required = false) String MAIN_ID,
                                                @RequestParam(required = false) String op,
                                                @RequestParam(required = false) String loginName,
                                                @RequestParam(required = false) String code,
                                                @RequestParam(required = false) String pname) {
        // 判断是否有重定向地址
//        SystemConfig redirectUrl = systemConfigService.findBySystemConfigKey(LOGIN_FOUR_A_REDIRECT_URL);
        SystemConfig url = systemConfigService.findBySystemConfigKey(LOGIN_FOUR_A_URL);
//        if (Objects.isNull(url) || Objects.isNull(redirectUrl)) {
//            log.error("未配置4A项目的重定向地址");
//            return ResponseHelper.successful();
//        }
        String response;
        if (CharSequenceUtil.isBlank(pname)) {
            response = HttpUtil.get(url.getSystemConfigValue(), Map.of("MAIN_ID", MAIN_ID, "op", op, "loginName", loginName, "ticket", code));
        } else {
            response = HttpUtil.get(url.getSystemConfigValue(), Map.of("pname", pname));
        }
        log.info("远程调用api/foura/login的响应为:{}", response);
        //解析
        ResponseResult bean = JSONUtil.toBean(response, ResponseResult.class);
        Map<String, Object> loginMap = ((Map<String, Object>) bean.getData());
        if (loginMap.containsKey("enable")) {
            log.error("4A项目配置未开启或4A未启动");
            return ResponseHelper.failed("4A项目配置未开启或4A未启动");
        }
//        if (loginMap.containsKey("redirectUrl")) {
//            return ResponseHelper.successful(loginMap);
//        }
        List<AccountDTO> accounts = accountService.findByLogonId(loginMap.get("account").toString());
        if (CollUtil.isEmpty(accounts)) {
            log.error("4A项目单点登录失败，账号不存在");
            return ResponseHelper.failed("账号不存在");
        }
        Map<String, Object> tokenResult = new HashMap<>();
        TokenUser tokenUser = new TokenUser(accounts.get(0));
        String newToken = tokenUtil.createTokenForUser(tokenUser, "web");
        tokenResult.put("token", newToken);
        tokenResult.put("UserName", tokenUser.getUser().getLogonId());
        tokenResult.put("LogonId", tokenUser.getUser().getLogonId());
        tokenResult.put("role", tokenUser.getRole());
        tokenResult.put("userId", tokenUser.getUserId());
        tokenResult.put("personId", tokenUser.getUserId());
        tokenResult.put("themeName", tokenUser.getUser().getThemeName());
        return ResponseHelper.successful(tokenResult);
    }
}