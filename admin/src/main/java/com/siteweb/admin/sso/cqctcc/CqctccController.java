package com.siteweb.admin.sso.cqctcc;

import com.siteweb.admin.dto.CQCTCCAccountSyncRequest;
import com.siteweb.admin.dto.CQCTCCAccountSyncResponse;
import com.siteweb.admin.entity.CqctccLogin;
import com.siteweb.admin.sso.cqctcc.dto.UserCallbackParamDTO;
import com.siteweb.common.properties.CqctccSSOProperties;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 定制需求：重庆电信单点登录认证以及相关验证接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class CqctccController {
    @Autowired
    CqctccService cqctccService;
    @Autowired
    CqctccSSOProperties cqctccSSOProperties;

    @Autowired
    CqctccQuartz cqctccQuartz;

    /**
     * 获取重庆电信账户登录标志位
     */
    @GetMapping("/cqctcc/accountlogin")
    public ResponseEntity<ResponseResult> getAccoutLogin(String logonId) {
        return ResponseHelper.successful(cqctccService.getAccoutLogin(logonId));
    }

    /**
     * 新增修改重庆电信账户登录标志位
     */
    @PostMapping("/cqctcc/accountlogin")
    public ResponseEntity<ResponseResult> updateAccoutLogin(@RequestBody CqctccLogin cqctccLogin) {
        cqctccService.updateAccoutLogin(cqctccLogin);
        return ResponseHelper.successful();
    }

    /**
     * 账户验证接口
     */
    @GetMapping("/cqctcc/verifyaccount")
    public ResponseEntity<ResponseResult> verifyAccount(@RequestParam String userName,
                                                        @RequestParam String phoneNumber) {
        return cqctccService.verifyAccount(userName, phoneNumber);
    }

    /**
     * 重庆电信单点登录接口
     */
    @GetMapping("/cqctcc/login")
    public ResponseEntity<ResponseResult> loginCqctccSSO(String token,
                                                         @RequestParam String account,
                                                         String index) {
        return cqctccService.loginCqctccSSO(token, account, index);
    }

    /**
     * 重庆电信主账号信息同步接口 5.1.1 主账号信息同步接口
     */
    @PostMapping("/v1/sync/bizOrder/accountSync")
    public ResponseEntity<CQCTCCAccountSyncResponse> cqctccAccountSync(@RequestBody CQCTCCAccountSyncRequest request) {
        return cqctccService.cqctccAccountSync(request);
    }

    /**
     * ftp定时上传测试接口
     */
    @GetMapping("/cqctcc/ftptest")
    public ResponseEntity<ResponseResult> test() {
        cqctccQuartz.syncDatFileJobExectue();
        return ResponseHelper.successful("success");
    }

    /**
     * 重庆电信app回调用户信息(单点登录)
     */
    @PostMapping("/cqctccapp/getUserCallback")
    public ResponseEntity<ResponseResult> getUserCallback(@RequestBody UserCallbackParamDTO userCallbackParamDTO) {
        return cqctccService.getUserCallback(userCallbackParamDTO);
    }


}
