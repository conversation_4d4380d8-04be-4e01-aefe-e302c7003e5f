package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.entity.AccountTimeSpan;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.mapper.AccountTimeSpanMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.AccountTimeSpanService;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SmsSecurityService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountTimeSpanServiceImpl implements AccountTimeSpanService {

    @Autowired
    AccountTimeSpanMapper accountTimeSpanMapper;

    @Autowired
    AccountService accountService;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    SmsSecurityService smsSecurityService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    SecurityAuditManager securityAuditManager;

    private static final String ENABLE = "account.login.timeSpan.enable";

    @Override
    public List<AccountTimeSpan> findAccountTimeSpans() {
        List<AccountTimeSpan> accountTimeSpans = accountTimeSpanMapper.selectList(null);
        accountTimeSpans.forEach(a -> {
            AccountDTO accountDTO = accountService.findByUserId(a.getUserId());
            if (ObjectUtil.isNull(accountDTO)) {
                return;
            }
            a.setUserName(accountDTO.getUserName());
            parseWeekSpanChar(a);
        });
        return accountTimeSpans;
    }


    @Override
    public int createAccountTimeSpan(AccountTimeSpan accountTimeSpan) {
        parseWeekSpanList(accountTimeSpan);
        AccountDTO accountDTO = accountService.findByUserId(accountTimeSpan.getUserId());
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.SECURITY_SETTING, messageSourceUtil.getMessage("audit.report.addVisitTimeSetting") + "：" + accountDTO.getUserName());
        return accountTimeSpanMapper.insert(accountTimeSpan);
    }

    @Override
    public int updateAccountTimeSpan(AccountTimeSpan accountTimeSpan) {
        parseWeekSpanList(accountTimeSpan);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.SECURITY_SETTING, messageSourceUtil.getMessage("audit.report.updateVisitTimeSetting") + "：" + JSONUtil.toJsonStr(accountTimeSpan));
        return accountTimeSpanMapper.updateById(accountTimeSpan);
    }

    @Override
    public AccountTimeSpan findAccountTimeSpanByUserId(Integer userId) {
        AccountTimeSpan accountTimeSpan = accountTimeSpanMapper.selectOne(new QueryWrapper<AccountTimeSpan>().eq("UserId", userId));
        parseWeekSpanChar(accountTimeSpan);
        return accountTimeSpan;
    }

    @Override
    public List<AccountDTO> findNotConfigAcount() {
        List<AccountDTO> accounts = accountService.findAll();
        List<AccountTimeSpan> accountTimeSpans = accountTimeSpanMapper.selectList(new QueryWrapper<AccountTimeSpan>().isNotNull("UserId"));
        List<Integer> userIds = accountTimeSpans.stream().map(AccountTimeSpan::getUserId).toList();
        accounts = accounts.stream().filter(a -> !userIds.contains(a.getUserId())).toList();
        return accounts;
    }

    @Override
    public AccountTimeSpan findAccountTimeSpanById(Integer accountTimeSpanId) {
        return accountTimeSpanMapper.selectById(accountTimeSpanId);
    }

    @Override
    public int deleteAccountTimeSpanById(Integer accountTimeSpanId) {
        AccountTimeSpan accountTimeSpan = findAccountTimeSpanById(accountTimeSpanId);
        if (ObjectUtil.isNull(accountTimeSpan)) {
            return -1;
        }
        AccountDTO accountDTO = accountService.findByUserId(accountTimeSpan.getUserId());
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.SECURITY_SETTING, messageSourceUtil.getMessage("audit.report.deleteVisitTimeSetting") + "：" + accountDTO.getUserName());
        return accountTimeSpanMapper.deleteById(accountTimeSpanId);
    }

    @Override
    public boolean isTimeSpanByUserName(String UserName) {
        // 获取系统参数开关
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(ENABLE);
        if (systemConfig == null || StrUtil.isBlank(systemConfig.getSystemConfigValue()) || "false".equalsIgnoreCase(systemConfig.getSystemConfigValue())) {
            log.warn("isTimeSpanByUserName systemconfig.[config={}]", systemConfig);
            return true;
        }
        List<AccountDTO> account = accountService.findByLogonId(UserName);
        AccountTimeSpan accountTimeSpan = findAccountTimeSpanByUserId(CollUtil.isEmpty(account) ? 0 : account.get(0).getUserId());
        if (accountTimeSpan == null) {
            return true;
        }
        int week = DateUtil.dayOfWeek(new Date());
        List<Integer> weekSpanList = accountTimeSpan.getWeekSpanList();
        // 判断是否在可访问的时间段内
        if (weekSpanList.contains(week)) {
            // 插入审计报表
            //securityAuditManager.recordAuditReport(AuditReportTypeEnum.LOGIN, getSmsText(UserName), Boolean.FALSE);
            // 插入安全日志报表
            securityAuditManager.recordSecurityReport(UserName, getSmsText(UserName),SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
            return false;
        }
        return true;
    }

    private void parseWeekSpanList(AccountTimeSpan accountTimeSpan) {
        List<Integer> weekSpanList = accountTimeSpan.getWeekSpanList();
        String str = "";
        if (CollUtil.isNotEmpty(weekSpanList)) {
            str = weekSpanList.stream().map(String::valueOf).collect(Collectors.joining(","));
        }
        accountTimeSpan.setWeekSpanChar(str);
    }

    private void parseWeekSpanChar(AccountTimeSpan accountTimeSpan) {
        if (accountTimeSpan != null && StrUtil.isNotBlank(accountTimeSpan.getWeekSpanChar())) {
            List<String> split = StrUtil.split(accountTimeSpan.getWeekSpanChar(), ",");
            accountTimeSpan.setWeekSpanList(split.stream().map(Integer::parseInt).toList());
        }
    }

    private String getSmsText(String userName) {
        return String.format(messageSourceUtil.getMessage("account.login.timeSpan.sms.template"), userName);
    }
}
