package com.siteweb.admin.service;

import cn.hutool.core.lang.tree.Tree;
import com.siteweb.admin.entity.Permission;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

public interface PermissionService {

    Permission getPermissionById(Integer permissionId);

    int createPermission(Permission permission);

    int updatePermission(Permission permission);

    void deletePermission(Integer permissionId);

    void deletePermissions(List<Integer> permissionIds);

    /**
     * 删除菜单权限根据全id
     *
     * @param menuProfileId
     * @param fullId        全id
     */
    void deleteMenuPermissionByFullId(Integer menuProfileId, String fullId);
    void createMenuPermission(Integer menuProfileId, String fullId, String fullTitle);
    List<Permission> findPermissionsByCategory(Integer permissionCategory);

    List<Permission> getPermissionsByRoleIdAndCategory(Integer roleId, Integer permissionCategory);

    List<Permission> getPermissionsByRoleId(Integer roleId);

    List<Permission> getMenuPermissionsByRoleIds(List<Integer> roleIds);

    void syncMenuPermissions(Integer menuProfileId);
    int deleteMenuPermission(Integer menuProfileId);
    HashMap<String, String> findMenuProfilePermission(Integer menuProfileId);

    List<Permission> getPermissionByIds(List<Integer> permissionIds);

    Permission findByCondition(Permission permission);

    List<Tree<String>> findMenuPermissionTree(Integer menuProfileId);

    /**
     * @param categoryId 菜单类型id
     * @return {@link List}<{@link Permission}>
     */
    List<Permission> findCurrentScenePermissionByCategory(Integer categoryId);

    /**
     * 查找菜单权限根据菜单级联id
     * @param descriptions 菜单级联id
     * @return {@link List}<{@link Permission}>
     */
    List<Permission> findMenuPermissionByDescription(Collection<String> descriptions);
}
