package com.siteweb.admin.service;


import com.siteweb.admin.entity.UserRole;
import com.siteweb.admin.vo.UserRoleVO;

import java.util.List;

public interface UserRoleService {

    List<UserRole> findAllRoles();

    List<UserRole> findRolesByUserId(Integer userId);

    UserRole findByRoleId(Integer roleId);

    int deleteUserRoleByRoleId(Integer roleId);

    int updateUserRole(UserRoleVO userRoleVO);

    int createUserRole(UserRole userRole);

    /**
     * 是否拥有系统管理员角色
     * @param userId 用户id
     * @return boolean
     */
    boolean hasSystemAdministrator(Integer userId);
}
