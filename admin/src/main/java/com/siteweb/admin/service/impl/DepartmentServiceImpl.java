package com.siteweb.admin.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.Department;
import com.siteweb.admin.entity.DepartmentCodeMap;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.mapper.DepartmentCodeMapMapper;
import com.siteweb.admin.mapper.DepartmentMapper;
import com.siteweb.admin.mapper.EmployeeMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.DepartmentService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service("departmentService")
public class DepartmentServiceImpl implements DepartmentService {
    @Autowired
    DepartmentMapper departmentMapper;
    @Autowired
    DepartmentCodeMapMapper departmentCodeMapMapper;

    @Autowired
    EmployeeMapper employeeMapper;

    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Override
    public List<Department> findAll() {
        return departmentMapper.selectList(Wrappers.emptyWrapper());
    }
    @Override
    public List<Department> findDepartmentById(Integer departmentId) {
        return departmentMapper.findDepartmentById(departmentId);
    }

    @Override
    public List<Department> findDepartmentByIds(List<Integer> idList) {
        return departmentMapper.selectBatchIds(idList);
    }

    @Override
    public List<Department> findChildrenDepartments(Integer parentDepartmentId) {
        return departmentMapper.findChildrenDepartments(parentDepartmentId);
    }

    @Override
    public int createDepartment(Department department) {
        department.setDepartmentId(primaryKeyValueService.getGlobalIdentity("TBL_Department", 0));
        if (department.getDepartmentId() < 0) { // 主键分配出错
            return department.getDepartmentId();
        }
        department.setLastUpdateDate(new Date());
        int insert = departmentMapper.insert(department);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.DEPARTMENT, localeMessageSourceUtil.getMessage("audit.report.addDepartment") + "：" + JSONUtil.toJsonStr(department));
        return insert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateDepartment(List<Department> departmentList) {
        departmentList.forEach(d-> d.setLastUpdateDate(new Date()));
        int insert = departmentMapper.batchCreate(departmentList);
        return insert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateDepartmentCodeMap(List<DepartmentCodeMap> departmentCodeMapList) {
        int insert = departmentCodeMapMapper.batchCreateDepartmentCodeMap(departmentCodeMapList);
        return insert;
    }

    @Override
    public List<DepartmentCodeMap> getAllDepartmentCodeMap(){
        return departmentCodeMapMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    @Transactional
    public int deleteById(Integer departmentId) {
        int employeeCountsByDepartmentId = employeeMapper.getEmployeeCountsByDepartmentId(departmentId);
        if (employeeCountsByDepartmentId > 0) {
            //删除Department时，将关联的Employee的DepartmentId设置为0
            employeeMapper.setDefaultDepartmentIdForEmployees(departmentId);
        }
        Department department = departmentMapper.selectById(departmentId);
        if (department == null) {
            return -1;
        }
        int result = departmentMapper.deleteById(departmentId);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.DEPARTMENT, localeMessageSourceUtil.getMessage("audit.report.deleteDepartment") + "：" + department.getDepartmentName());
        return result;
    }

    @Override
    @Transactional
    public int batchDeleteByIds(List<Integer> idList) {
        int employeeCountsByDepartmentId = employeeMapper.getEmployeeCountsByDepartmentIds(idList);
        if (employeeCountsByDepartmentId > 0) {
            //删除Department时，将关联的Employee的DepartmentId设置为0
            employeeMapper.setDefaultDepartmentIdsForEmployees(idList);
        }
        List<Department> departmentList = this.findDepartmentByIds(idList);
        List<String> nameList = departmentList.stream().map(Department::getDepartmentName).toList();
        String names = StringUtils.join(nameList, ",");
        if (departmentList.size() == 0) {
            return -1;
        }
        int result = departmentMapper.deleteBatchIds(idList);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.DEPARTMENT, localeMessageSourceUtil.getMessage("audit.report.deleteDepartment") + "：" + names);
        return result;
    }

    @Override
    public int updateDepartment(Department department) {
        department.setLastUpdateDate(new Date());
        int result = departmentMapper.updateById(department);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.DEPARTMENT, localeMessageSourceUtil.getMessage("audit.report.updateDepartment") + "：" + JSONUtil.toJsonStr(department));
        return result;
    }

    @Override
    @Transactional
    public int deleteByIds(List<Integer> idList) {
        int result = departmentCodeMapMapper.deleteBatchIds(idList);
        return result;
    }
    @Override
    public int updateDepartmentCodeMap(DepartmentCodeMap departmentCodeMap){
        return departmentCodeMapMapper.updateById(departmentCodeMap);
    }
}
