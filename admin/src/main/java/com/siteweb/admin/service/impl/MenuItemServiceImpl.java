package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.MenuItemDTO;
import com.siteweb.admin.dto.RouterRedirectDTO;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.entity.MenuItemStructureMap;
import com.siteweb.admin.entity.Scene;
import com.siteweb.admin.entity.SceneMenuProfileMap;
import com.siteweb.admin.mapper.MenuItemMapper;
import com.siteweb.admin.service.*;
import com.siteweb.admin.vo.MenuItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MenuItemServiceImpl implements MenuItemService {

    /**
     * 嵌套踢路由的固定跳转前缀
     */
    private static final String ENTRY_EMBED_IFRAME_TARGET = "entry/embediframe?target=";
    @Autowired
    MenuItemMapper menuItemMapper;
    @Lazy
    @Autowired
    MenuStructureService menuStructureService;
    @Autowired
    private MenuItemStructureMapService menuItemStructureMapService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    SceneMenuProfileMapService sceneMenuProfileMapService;
    @Autowired
    SceneService sceneService;
    @Override
    public List<MenuItem> findMenuItemsWithChildren() {
        return menuItemMapper.findAllMenuItems();
    }

    @Override
    public List<MenuItem> findMenuItemList() {
        return menuItemMapper.selectList(null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int createMenuItem(MenuItemVO menuItemVO) {
        MenuItem menuItem = BeanUtil.toBean(menuItemVO, MenuItem.class);
        if (ObjectUtil.isNotNull(menuItemVO.getMenuItemId())) {
            //删除老权限点 当前端传ParentId字段为1时代表移除当前节点
            if (ObjectUtil.equals(menuItemVO.getParentId(), 1)) {
                this.deletePermission(menuItemVO);
            }
            return  menuItemMapper.updateById(menuItem);
        }
        SceneMenuProfileMap sceneMenuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuItemVO.getMenuProfileId());
        menuItem.setFeatureId(1);
        menuItem.setDescription(sceneMenuProfileMap.getSceneId().toString());
        return menuItemMapper.insert(menuItem);
    }
    @Override
    public int deleteById(Integer menuItemId) {
        return menuItemMapper.deleteById(menuItemId);
    }

    @Override
    public int updateMenuItem(MenuItem menuItem) {
        return menuItemMapper.updateById(menuItem);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateMenuItem(List<MenuItemVO> menuItemVOList) {
        for (MenuItemVO menuItemVO : menuItemVOList) {
            //更新菜单
            MenuItem menuItem = BeanUtil.toBean(menuItemVO, MenuItem.class);
            this.updateMenuItem(menuItem);
            //添加新权限点
            if (ObjectUtil.isNotNull(menuItemVO.getMenuProfileId())) {
                this.createPermission(menuItemVO);
            }
        }
        return menuItemVOList.size();
    }

    private void createPermission(MenuItemVO menuItemVO) {
        String fullId = menuStructureService.findFullId(menuItemVO.getMenuProfileId(), menuItemVO.getMenuItemId());
        String fullTitle = menuStructureService.findFullTitle(menuItemVO.getMenuProfileId(), menuItemVO.getMenuItemId());
        this.deletePermission(menuItemVO);
        permissionService.createMenuPermission(menuItemVO.getMenuProfileId(),fullId, fullTitle);
    }

    private void deletePermission(MenuItemVO menuItemVO) {
        String fullId = menuStructureService.findFullId(menuItemVO.getMenuProfileId(), menuItemVO.getMenuItemId());
        permissionService.deleteMenuPermissionByFullId(menuItemVO.getMenuProfileId(), fullId);
    }

    @Override
    public MenuItem findById(Integer menuItemId) {
        return menuItemMapper.findMenuItemById(menuItemId);
    }

    @Override
    public List<MenuItem> findByParentId(Integer parentId) {
        return menuItemMapper.selectList(Wrappers.lambdaQuery(MenuItem.class)
                                                 .eq(MenuItem::getParentId, parentId));
    }

    @Override
    public String findTopMenuItemPath() {
        return menuItemMapper.findTopMenuItemPath();
    }

    @Override
    public List<MenuItemDTO> getAllFirstLevelMenuItems(Integer menuProfileId) {
        //获取所有菜单 不区分场景
        List<MenuItem> menuItemList = menuItemMapper.findBySceneIdAndParentId();
        List<MenuItemStructureMap> menuItemStructureMaps = menuItemStructureMapService.findByMenuProfileId(menuProfileId);
        List<Scene> sceneList = sceneService.findScenes();
        Map<Integer, String> sceneMap = sceneList.stream()
                .collect(Collectors.toMap(Scene::getSceneId, Scene::getSceneName));
        List<MenuItemDTO> menuItemDTOList = new ArrayList<>();
        for (MenuItem menuItem : menuItemList) {
            boolean exist = menuItemStructureMaps.stream()
                                                 .anyMatch(oc -> ObjectUtil.equals(oc.getMenuItemId(), menuItem.getMenuItemId()));
            if (!exist) {
                MenuItemDTO menuItemDTO = BeanUtil.toBean(menuItem, MenuItemDTO.class);
                if (NumberUtil.isInteger(menuItem.getDescription())) {
                    menuItemDTO.setSceneName(sceneMap.getOrDefault(Integer.valueOf(menuItem.getDescription()), "通用"));
                } else {
                    menuItemDTO.setSceneName("通用");
                }
                menuItemDTOList.add(menuItemDTO);
            }
        }
        return buildTree(menuItemDTOList, 1);
    }

    public static List<MenuItemDTO> buildTree(List<MenuItemDTO> menuList, Integer rootParentId) {
        // Map：menuItemId -> MenuItemDTO，方便快速查找父节点
        Map<Integer, MenuItemDTO> idToMenuMap = new HashMap<>();
        for (MenuItemDTO menu : menuList) {
            idToMenuMap.put(menu.getMenuItemId(), menu);
        }
        List<MenuItemDTO> rootMenus = new ArrayList<>();
        for (MenuItemDTO menu : menuList) {
            Integer parentId = menu.getParentId();
            if (parentId != null && parentId.equals(rootParentId)) {
                // 根节点
                rootMenus.add(menu);
            } else {
                // 子节点，添加到对应父节点的 children 中
                MenuItemDTO parent = idToMenuMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(menu);
                }
            }
        }
        // 对每一层的 children 进行排序（包括根节点）
        sortMenus(rootMenus);
        return rootMenus;
    }

    private static void sortMenus(List<MenuItemDTO> menus) {
        if (menus == null) {
            return;
        }
        // sortIndex 升序排序，null 排最后
        menus.sort(Comparator.comparing(
                MenuItemDTO::getSortIndex,
                Comparator.nullsLast(Integer::compareTo)
        ));
        // 递归对子节点排序
        for (MenuItemDTO menu : menus) {
            sortMenus(menu.getChildren());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateMenuItemSort(List<MenuItemVO> menuItemVOList) {
        for (MenuItemVO menuItemVO : menuItemVOList) {
            menuItemMapper.update(null, Wrappers.lambdaUpdate(MenuItem.class)
                                                     .set(MenuItem::getLayoutPosition, menuItemVO.getLayoutPosition())
                                                     .eq(MenuItem::getMenuItemId, menuItemVO.getMenuItemId()));
        }
        return menuItemVOList.size();
    }

    @Override
    public RouterRedirectDTO findMenuItemAllPath(Integer id) {
        MenuItem menuItem = this.findById(id);
        RouterRedirectDTO redirectDTO = new RouterRedirectDTO(menuItem.getIsEmbed(), menuItem.getIsExternalWeb());
        String redirectPath = getRedirectPath(menuItem);
        redirectDTO.setPath(redirectPath);
        return redirectDTO;
    }


    /**
     * 获取路由跳转的path
     * @param menuItem 菜单信息
     * @return {@link StringBuilder }
     */
    private String getRedirectPath(MenuItem menuItem) {
        //嵌入的话逻辑不同
        if (Boolean.TRUE.equals(menuItem.getIsEmbed())) {
            return ENTRY_EMBED_IFRAME_TARGET + menuItem.getPath();
        }
        //其他菜单
        StringBuilder sb = new StringBuilder(menuItem.getPath());
        while (ObjectUtil.notEqual(menuItem.getParentId(), 0)) {
            menuItem = this.findById(menuItem.getParentId());
            if (CharSequenceUtil.isBlank(menuItem.getPath())) {
                continue;
            }
            sb.insert(0, menuItem.getPath() + "/");
        }
        return sb.toString();
    }
}
