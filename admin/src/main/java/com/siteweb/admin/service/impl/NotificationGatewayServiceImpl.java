package com.siteweb.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.siteweb.admin.dto.HeartbeatResponseDTO;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.job.NotificationGatewayHeartbeatJob;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.NotificationGatewayService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.model.TriggerModel;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;


@Service
@Slf4j
public class NotificationGatewayServiceImpl implements NotificationGatewayService {

    // 状态位
    public static final AtomicBoolean gatewayState = new AtomicBoolean(false);

    private static final String GATEWAY_HEARTBEAT_URL = "security.gateway.heartbeat.url";

    private static final String GATEWAY_HEARTBEAT_INTERVAL = "security.gateway.heartbeat.interval";

    private static final String JOB_NAME = "GatewayHeartbeatTask";

    private String heartbeatUrl;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    SchedulerJobService schedulerJobService;

    @PostConstruct
    private void GatewayHeartbeatTask() {
        try {
            // 开关
            SchedulerJob schedulerJob = new SchedulerJob();
            SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(GATEWAY_HEARTBEAT_INTERVAL);
            if (systemConfig == null || StrUtil.isBlank(systemConfig.getSystemConfigValue()) || "0".equals(systemConfig.getSystemConfigValue())) {
                log.warn("GatewayHeartbeatTask enable systemconfig warn.[config={}]", systemConfig);
                schedulerJob.setJobGroup(Scheduler.DEFAULT_GROUP);
                schedulerJob.setJobName(JOB_NAME);
                schedulerJobService.removeSchedulerJob(schedulerJob);
                return;
            }
            SystemConfig url = systemConfigService.findBySystemConfigKey(GATEWAY_HEARTBEAT_URL);
            if (url == null || StrUtil.isBlank(systemConfig.getSystemConfigValue())) {
                log.warn("GatewayHeartbeatTask url systemconfig warn.[config{}]", url);
                return;
            }
            heartbeatUrl = url.getSystemConfigValue();
            // 第一次执行获取状态
            Boolean firstResult = gatewayHeartbeatState();
            gatewayState.set(firstResult);
            // 记录审计日志
            recordGatewayLog(firstResult);
            // 执行quartz任务
            schedulerJob.setClassName(NotificationGatewayHeartbeatJob.class.getName());
            schedulerJob.setRepeatInterval(Long.valueOf(systemConfig.getSystemConfigValue().trim()));
            schedulerJob.setJobGroup(Scheduler.DEFAULT_GROUP);
            schedulerJob.setJobName(JOB_NAME);
            schedulerJob.setTriggerType(TriggerModel.SIMPLE_TRIGGER_TYPE);
            schedulerJobService.removeSchedulerJob(schedulerJob);
            schedulerJobService.addSchedulerJob(schedulerJob);
        } catch (Exception e) {
            log.error("fileIntegrityCheckTask is err {}", e.getMessage());
        }

    }

    public void recordGatewayLog(Boolean Result) {
        if (Boolean.FALSE.equals(Result)) {
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.THIRD_PARTY_ACCESS, localeMessageSourceUtil.getMessage("notification.gateway.access.failure"), Boolean.FALSE);
        } else {
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.THIRD_PARTY_ACCESS, localeMessageSourceUtil.getMessage("notification.gateway.access.success"), Boolean.TRUE);
        }
    }


    @Override
    public Boolean gatewayHeartbeatState() {
        boolean result = false;
        Map<String, Long> req = new HashMap<>();
        req.put("heartbeattime", new Date().getTime());
        log.info("gatewayHeartbeatState [Timestamp={}]", req.get("heartbeattime"));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map> httpEntity = new HttpEntity<>(req, httpHeaders);
        HeartbeatResponseDTO heartbeatResponseDTO = null;
        try {
            heartbeatResponseDTO = restTemplate.postForObject(heartbeatUrl, httpEntity, HeartbeatResponseDTO.class);
            log.info("gatewayHeartbeatState succeed. [heartbeattime={}, error_code={}, error_msg={}]", heartbeatResponseDTO.getHeartbeattime(), heartbeatResponseDTO.getError_code(), heartbeatResponseDTO.getError_msg());
            if (heartbeatResponseDTO.getError_code() == 0) {
                result = true;
            } else {
                result = false;
            }
        } catch (RestClientException e) {
            log.error("gatewayHeartbeatState failed：{} {}", req, e.getMessage());
            return false;
        }
        return result;
    }
}
