package com.siteweb.admin.service;

import com.siteweb.admin.dto.MenuStructureDTO;
import com.siteweb.admin.entity.MenuStructure;
import com.siteweb.admin.vo.MenuStructureVO;

import java.util.List;

public interface MenuStructureService {

    List<MenuStructure> findMenuStructures();

    int createMenuStructure(MenuStructure menuStructure);
    int createMenuStructure(List<MenuStructureVO> menuStructureVOList);

    int deleteById(Integer menuStructureId);

    int updateMenuStructure(MenuStructureVO menuStructureVO);

    MenuStructure findById(Integer menuStructureId);
    List<MenuStructure> findByIds(List<Integer> menuStructureIds);
    List<MenuStructure> findTopMenuStructuresByMenuProfileId(Integer menuProfileId);
    String findFullId(Integer menuProfileId, Integer menuItemId);
    String findFullTitle(Integer menuProfileId, Integer menuItemId);
    List<MenuStructureDTO> getParentMenuStructureByProfileId(Integer menuProfileId);
    void deleteMenuStructure(MenuStructureVO menuStructureVO);

    int deleteByMenuProfileId(Integer menuProfileId);

    /**
     * 更新目录的排序
     * @param menuStructureVOList
     * @return int
     */
    int updateMenuStructureSort(List<MenuStructureVO> menuStructureVOList);
}

