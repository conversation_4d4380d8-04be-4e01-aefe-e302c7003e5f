package com.siteweb.admin.service;

import com.siteweb.admin.entity.UserConfig;

import java.util.List;

public interface UserConfigService {
    List<UserConfig> findUserConfigByUserId(Integer userId);

    /**
     * 批量更新用户设置
     *
     * @param userConfigList 用户设置列表
     * @return int 更新成功的条数
     */
    int batchUpdate(List<UserConfig> userConfigList);

    /**
     * 是否开启tts语音播报
     * @param userId 用户id
     * @return boolean
     */
    boolean enableTts(Integer userId);

    String findUserTheme(Integer userId);

    List<UserConfig> findUserConfigByUserIdAndType(Integer userId, Integer configType);
}
