package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.entity.UserRole;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.service.PermissionService;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.service.UserRoleService;
import com.siteweb.admin.vo.UserRoleVO;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
@Transactional(rollbackFor = Exception.class)
public class UserRoleServiceImpl implements UserRoleService {

    @Autowired
    UserRoleMapper userRoleMapper;

    @Autowired
    RolePermissionMapService rolePermissionMapService;

    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    PermissionService permissionService;

    @Override
    public List<UserRole> findAllRoles() {
        return userRoleMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<UserRole> findRolesByUserId(Integer userId) {
        return userRoleMapper.findRolesByUserId(userId);
    }

    @Override
    public UserRole findByRoleId(Integer roleId) {
        UserRole userRole = userRoleMapper.selectById(roleId);
        if (Objects.isNull(userRole)) {
            return null;
        }
        List<RolePermissionMap> rolePermissionMaps = rolePermissionMapService.getRolePermissionMapsByRoleId(roleId);
        userRole.setRolePermissionMapList(rolePermissionMaps);
        setPermission(rolePermissionMaps);
        return userRole;
    }

    private void setPermission(List<RolePermissionMap> rolePermissionMaps) {
        if (CollUtil.isEmpty(rolePermissionMaps)) {
            return;
        }
        for (RolePermissionMap r : rolePermissionMaps) {
            if (PermissionCategoryEnum.REGION.getPermissionCategoryId().equals(r.getPermissionCategoryId())
                    || PermissionCategoryEnum.AREA.getPermissionCategoryId().equals(r.getPermissionCategoryId())
                    || PermissionCategoryEnum.MAJOR.getPermissionCategoryId().equals(r.getPermissionCategoryId())) {
                continue;
            }
            r.setPermission(permissionService.getPermissionById(r.getPermissionId()));
        }

    }

    @Override
    public int deleteUserRoleByRoleId(Integer roleId) {

        return userRoleMapper.deleteUserRoleByRoleId(roleId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateUserRole(UserRoleVO userRoleVO) {
        return userRoleMapper.updateById(BeanUtil.toBean(userRoleVO, UserRole.class));
    }

    @Override
    public int createUserRole(UserRole userRole) {
        userRole.setRoleId(
                primaryKeyValueService.getGlobalIdentity("TBL_UserRole", 0));
        if (userRole.getRoleId() < 0) {
            // 主键分配出错
            return userRole.getRoleId();
        }
        //OperationGroupVO operationGroupVO = new OperationGroupVO();
        //operationGroupVO.setGroupId(userRole.getRoleId());
        //operationGroupVO.setGroupName(userRole.getRoleName() + localeMessageSourceUtil.getMessage("nets.operation.group.name"));
        //operationGroupVO.setDescription(userRole.getRoleName() + localeMessageSourceUtil.getMessage("nets.operation.group.Description"));
        //rolePermissionMapService.createOperationGroup(operationGroupVO);
        return userRoleMapper.insert(userRole);
    }

    @Override
    public boolean hasSystemAdministrator(Integer userId) {
        return userRoleMapper.isSystemAdministrator(userId) > 0;
    }
}
