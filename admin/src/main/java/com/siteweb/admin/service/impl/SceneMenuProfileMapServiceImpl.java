package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.SceneMenuProfileMap;
import com.siteweb.admin.mapper.SceneMenuProfileMapMapper;
import com.siteweb.admin.service.SceneMenuProfileMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SceneMenuProfileMapServiceImpl implements SceneMenuProfileMapService {

    @Autowired
    SceneMenuProfileMapMapper sceneMenuProfileMapMapper;

    @Override
    public SceneMenuProfileMap findBySceneId(Integer sceneId) {
        return sceneMenuProfileMapMapper.selectOne(new QueryWrapper<SceneMenuProfileMap>().eq("SceneId", sceneId));
    }

    @Override
    public SceneMenuProfileMap findByMenuProfileId(Integer menuProfileId) {
        return sceneMenuProfileMapMapper.selectOne(Wrappers.lambdaQuery(SceneMenuProfileMap.class).eq(SceneMenuProfileMap::getMenuProfileId,menuProfileId));
    }

    @Override
    public void createSceneMenuProfileMap(Integer sceneId, Integer menuProfileId) {
        SceneMenuProfileMap sceneMenuProfileMap = new SceneMenuProfileMap();
        sceneMenuProfileMap.setSceneId(sceneId);
        sceneMenuProfileMap.setMenuProfileId(menuProfileId);
        sceneMenuProfileMapMapper.insert(sceneMenuProfileMap);
    }

    @Override
    public int deleteByMenuProfileId(Integer menuProfileId) {
        return sceneMenuProfileMapMapper.delete(Wrappers.lambdaQuery(SceneMenuProfileMap.class)
                                                 .eq(SceneMenuProfileMap::getMenuProfileId, menuProfileId));
    }
}
