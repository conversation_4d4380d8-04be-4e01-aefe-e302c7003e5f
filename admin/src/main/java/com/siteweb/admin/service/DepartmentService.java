package com.siteweb.admin.service;

import com.siteweb.admin.entity.Department;
import com.siteweb.admin.entity.DepartmentCodeMap;

import java.util.List;

public interface DepartmentService {
    /**
     * 获取所有部门
     * @return {@link List}<{@link Department}>
     */
    List<Department> findAll();

    List<Department> findDepartmentById(Integer departmentId);
    List<Department> findDepartmentByIds(List<Integer> idList);

    List<Department> findChildrenDepartments(Integer parentDepartmentId);

    int createDepartment(Department department);

    int batchCreateDepartment(List<Department> departmentList);
    int batchCreateDepartmentCodeMap(List<DepartmentCodeMap> departmentCodeMapList);

    int deleteById(Integer departmentId);
    int batchDeleteByIds(List<Integer> idList);

    int updateDepartment(Department department);

    List<DepartmentCodeMap> getAllDepartmentCodeMap();
    int deleteByIds(List<Integer> idList);

    int updateDepartmentCodeMap(DepartmentCodeMap departmentCodeMap);
}

