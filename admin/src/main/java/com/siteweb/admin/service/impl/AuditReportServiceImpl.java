package com.siteweb.admin.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.AuditReportParamDto;
import com.siteweb.admin.entity.AuditReport;
import com.siteweb.admin.mapper.AuditReportMapper;
import com.siteweb.admin.service.AuditReportService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;

@Slf4j
@Service
public class AuditReportServiceImpl implements AuditReportService {
    /**
     * 默认最大日志数量
     */
    private static final Integer DEFAULT_MAX_COUNT = 10000;
    @Autowired
    private AuditReportMapper auditReportMapper;
    @Autowired
    SystemConfigService systemConfigService;

    @Override
    public Integer findCurrentCount() {
        return auditReportMapper.selectCount(Wrappers.emptyWrapper()).intValue();
    }

    public List<AuditReport> findAll(){
        return auditReportMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<AuditReport> findAuditReport(AuditReportParamDto auditReportParamDto) {
        return auditReportMapper.findAuditReport(auditReportParamDto);
    }

    @Override
    public void saveAuditReport(AuditReport auditReport) {
        auditReportMapper.insert(auditReport);
    }

    @Override
    public void removeRecordCount(Integer count) {
        List<Integer> auditReportIds = auditReportMapper.getLatestRecord(count);
        auditReportMapper.deleteBatchIds(auditReportIds);
    }

    public Integer findAuditMaxCount(){
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.auditreport.maxcount");
        if (ObjectUtil.isNull(systemConfig)) {
            return DEFAULT_MAX_COUNT;
        }
        try {
            return Integer.parseInt(systemConfig.getSystemConfigValue());
        } catch (NumberFormatException e) {
            log.error("系统配置中审计报表最大记录数有误。");
            return DEFAULT_MAX_COUNT;
        }
    }

    @Override
    public HashSet<Integer> findAuditLevel() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.auditreport.level");
        HashSet<Integer> result = new HashSet<>();
        if (ObjectUtil.isNull(systemConfig)) {
            //默认全开启
            result.addAll(List.of(1,2,3,4));
            return result;
        }
        String[] levelSplit = systemConfig.getSystemConfigValue()
                                          .split(",");
        for (String level : levelSplit) {
            try {
                result.add(NumberUtil.parseInt(level));
            } catch (NumberFormatException e) {
                log.error("系统配置中审计级别设置有误。");
            }
        }
        return result;
    }

    @Override
    public Boolean isEnableAudit() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.auditreport.enable");
        if (ObjectUtil.isNull(systemConfig)) {
            return Boolean.FALSE;
        }
        String systemConfigValue = systemConfig.getSystemConfigValue();
        return Boolean.parseBoolean(systemConfigValue);
    }
}
