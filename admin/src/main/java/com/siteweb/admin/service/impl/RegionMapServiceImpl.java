package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.mapper.RegionMapMapper;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.vo.RegionMapCreteVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR> zhou
 * @description RegionMapServiceImpl
 * @createTime 2022-01-18 15:26:07
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RegionMapServiceImpl implements RegionMapService {

    @Autowired
    RegionMapMapper regionMapMapper;

    @Override
    public List<RegionMap> findByRegionId(Integer regionId) {
        return regionMapMapper.selectList(new QueryWrapper<RegionMap>().eq("RegionId", regionId));
    }

    @Override
    public List<RegionMap> findByUserId(Integer userId) {
        return regionMapMapper.findByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRegionMaps(RegionMapCreteVO regionMapCreteVO) {
        deleteByRegionId(regionMapCreteVO.getRegionId());
        if (CollUtil.isEmpty(regionMapCreteVO.getRegionMapList())) {
            return true;
        }
        List<RegionMap> regionMapsList = regionMapCreteVO.buildRegionMapList();
        return regionMapMapper.batchInsert(regionMapsList);
    }

    @Override
    public int deleteByRegionId(Integer regionId) {
        return regionMapMapper.delete(new QueryWrapper<RegionMap>().eq("RegionId", regionId));
    }

    @Override
    public List<RegionMap> findByRegionIds(Collection<Integer> regionIds) {
        if (CollUtil.isEmpty(regionIds)) {
            return Collections.emptyList();
        }
        return regionMapMapper.findByRegionIds(regionIds);
    }

    @Override
    public Set<Integer> findResourceStructureIdsByRegionIds(Collection<Integer> regionIds) {
        if (CollUtil.isEmpty(regionIds)) {
            return new HashSet<>();
        }
        return regionMapMapper.findResourceStructureIdsByRegionIds(regionIds);
    }
}
