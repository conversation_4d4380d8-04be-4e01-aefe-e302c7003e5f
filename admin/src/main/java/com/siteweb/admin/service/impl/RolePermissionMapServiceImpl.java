package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.entity.MenuProfile;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.entity.Scene;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.enums.SceneEnum;
import com.siteweb.admin.mapper.RolePermissionMapMapper;
import com.siteweb.admin.parse.PermissionParseManager;
import com.siteweb.admin.service.*;
import com.siteweb.admin.vo.UserRoleRightVO;
import com.siteweb.common.constants.GlobalConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@Service("RolePermissionMapService")
@Transactional(rollbackFor = Exception.class)
public class RolePermissionMapServiceImpl implements RolePermissionMapService {

    @Autowired
    RolePermissionMapMapper rolePermissionMapMapper;

    @Autowired
    SceneService sceneService;

    @Autowired
    @Lazy
    PermissionParseManager permissionParseManager;
    @Autowired
    MenuProfileService menuProfileService;
    @Autowired
    @Lazy
    PermissionService permissionService;
    @Autowired
    MenuPermissionGroupMapService menuPermissionGroupMapService;
    @Autowired
    OperationGroupMapService operationGroupMapService;
    @Override
    public List<RolePermissionMap> findRolePermissionMaps() {
        return rolePermissionMapMapper.selectList(null);
    }

    @Override
    public int createRolePermissionMap(RolePermissionMap rolePermissionMap) {
        return rolePermissionMapMapper.insert(rolePermissionMap);
    }

    @Override
    public int deleteById(Integer rolePermissionMapId) {
        return rolePermissionMapMapper.deleteById(rolePermissionMapId);
    }

    @Override
    public int updateRolePermissionMap(RolePermissionMap rolePermissionMap) {
        return rolePermissionMapMapper.updateById(rolePermissionMap);
    }

    @Override
    public RolePermissionMap findById(Integer rolePermissionMapId) {
        return rolePermissionMapMapper.selectById(rolePermissionMapId);
    }

    @Override
    public List<RolePermissionMap> getRolePermissionMapsByRoleId(Integer roleId) {
        List<RolePermissionMap> rolePermissionMapList = rolePermissionMapMapper.findRolePermissionMapByRoleId(roleId);
        Scene scene = sceneService.currentScene();
        // 电信场景
        if (SceneEnum.NETS.getValue().equals(scene.getSceneId())) {
            // 添加片区权限
            List<RolePermissionMap> areaRolePermissionMaps = getAreaRolePermissionMaps(roleId);
            // 添加专业权限
            List<RolePermissionMap> specialtyPermissionMaps = getSpecialtyPermissionMaps(roleId);
            rolePermissionMapList.addAll(areaRolePermissionMaps);
            rolePermissionMapList.addAll(specialtyPermissionMaps);
        }
        // 菜单权限
        List<RolePermissionMap> menuRolePermissionMap = getRolePermissionMapsByCategory(rolePermissionMapList, PermissionCategoryEnum.MENU,
                permissionGroupIds -> menuPermissionGroupMapService.findPermissionIdsByGroupIds(permissionGroupIds));
        rolePermissionMapList.addAll(menuRolePermissionMap);
        // 操作权限组
        List<RolePermissionMap> operationRolePermissionMap = getRolePermissionMapsByCategory(rolePermissionMapList,PermissionCategoryEnum.OPERATION,
                permissionGroupIds -> operationGroupMapService.findPermissionIdsByGroupIds(permissionGroupIds));
        rolePermissionMapList.addAll(operationRolePermissionMap);
        return rolePermissionMapList;
    }

    private List<RolePermissionMap> getRolePermissionMapsByCategory(List<RolePermissionMap> rolePermissionMapList, PermissionCategoryEnum permissionCategoryEnum, UnaryOperator<List<Integer>> function) {
        //获取菜单或操作权限组权限id
        List<RolePermissionMap> permissionGroups = rolePermissionMapList.stream()
                                                                           .filter(p -> permissionCategoryEnum.getPermissionCategoryId().equals(p.getPermissionCategoryId()))
                                                                           .toList();
        //移除组信息
        rolePermissionMapList.removeIf(p -> permissionCategoryEnum.getPermissionCategoryId().equals(p.getPermissionCategoryId()));
        //所有是否拥有所有的操作权限或者菜单权限
        if (permissionGroups.stream().map(RolePermissionMap::getPermissionId).anyMatch(permissionId -> Objects.equals(permissionId, -1))) {
            List<Permission> permissionsByCategory = permissionService.findPermissionsByCategory(permissionCategoryEnum.getPermissionCategoryId());
            return permissionsByCategory.stream()
                                        .map(RolePermissionMap::new)
                                        .toList();
        }
        //查找权限组的权限Ids
        List<Integer> permissionGroupIds = permissionGroups.stream()
                                                              .map(RolePermissionMap::getPermissionId)
                                                              .toList();
        List<Integer> permissionIds = function.apply(permissionGroupIds);
        List<Permission> permissions = permissionService.getPermissionByIds(permissionIds);
        return permissions.stream()
                          .map(RolePermissionMap::new)
                          .toList();
    }

    @Override
    public int batchCreateRolePermissionMap(List<RolePermissionMap> rolePermissionMapList) {
        if (CollUtil.isEmpty(rolePermissionMapList)) {
            return 0;
        }
        return rolePermissionMapMapper.batchCreateRolePermissionMap(rolePermissionMapList);
    }

    @Override
    public int batchCreateUserRoleRight(List<UserRoleRightVO> userRoleRightVOList) {
        if (CollUtil.isEmpty(userRoleRightVOList)) {
            return 0;
        }
        return rolePermissionMapMapper.batchCreateUserRoleRight(userRoleRightVOList);
    }

    @Override
    public int delUserRoleRightByRoleIdAndOperationType(Integer roleId, Integer operationType) {
        return rolePermissionMapMapper.delUserRoleRightByRoleIdAndOperationType(roleId, operationType);
    }

    @Override
    public int getUserRoleRightCount(Integer roleId, Integer operationId, Integer operationType) {
        return rolePermissionMapMapper.getUserRoleRightCount(roleId, operationId, operationType);
    }

    @Override
    public List<RolePermissionMap> getAreaRolePermissionMaps(Integer roleId) {
        return rolePermissionMapMapper.getAreaRolePermissionMaps(roleId);
    }

    @Override
    public List<RolePermissionMap> getSpecialtyPermissionMaps(Integer roleId) {
        return rolePermissionMapMapper.getSpecialtyPermissionMaps(roleId);
    }

    @Override
    public Set<Integer> findRolePermissionsByUserId(Integer userId, Integer permissionCategoryId) {
        return rolePermissionMapMapper.findRolePermissionsByUserId(userId, permissionCategoryId);
    }

    @Override
    public int deleteByRoleId(Integer roleId) {
        return rolePermissionMapMapper.delete(new QueryWrapper<RolePermissionMap>().eq("RoleId", roleId));
    }

    @Override
    public int deleteByRoleIdAndCascadeIds(Integer roleId, Collection<String> cascadeIdSet) {
        int deleteCount = rolePermissionMapMapper.delete(new QueryWrapper<RolePermissionMap>().eq("RoleId", roleId)
                                                                                              .ne("PermissionCategoryId", PermissionCategoryEnum.MENU.getPermissionCategoryId()));
        if (CollUtil.isEmpty(cascadeIdSet)) {
            return deleteCount;
        }
        deleteCount += rolePermissionMapMapper.deleteByCascadeId(roleId, cascadeIdSet);
        return deleteCount;
    }

    @Override
    public List<RolePermissionMap> findByRoleIdsAndCategoryId(List<Integer> roleIds, Integer permissionCategoryId) {
        return rolePermissionMapMapper.selectList(new QueryWrapper<RolePermissionMap>()
                .in(CollUtil.isNotEmpty(roleIds), "RoleId", roleIds)
                .eq(ObjectUtil.isNotEmpty(permissionCategoryId), "PermissionCategoryId", permissionCategoryId));
    }

    @Override
    public List<RolePermissionMap> getRolePermissionMapsByUserIdAndCategoryId(Integer userId, Integer permissionCategoryId) {
        return rolePermissionMapMapper.getRolePermissionMapsByUserIdAndCategoryId(userId, permissionCategoryId);
    }

    @Override
    public List<Integer> findRolePermissionMapsByUserId(Integer userId) {
        return rolePermissionMapMapper.findRolePermissionMapsByUserId(userId);
    }

    @Override
    public RolePermissionMap findByCondition(RolePermissionMap rolePermissionMap) {
        LambdaQueryWrapper<RolePermissionMap> queryWrapper = Wrappers.lambdaQuery(RolePermissionMap.class)
                                                           .eq(ObjectUtil.isNotNull(rolePermissionMap.getRoleId()), RolePermissionMap::getRoleId, rolePermissionMap.getRoleId())
                                                           .eq(ObjectUtil.isNotNull(rolePermissionMap.getPermissionCategoryId()), RolePermissionMap::getPermissionCategoryId, rolePermissionMap.getPermissionCategoryId())
                                                           .eq(ObjectUtil.isNotNull(rolePermissionMap.getPermissionId()), RolePermissionMap::getPermissionId, rolePermissionMap.getPermissionId());
        return rolePermissionMapMapper.selectOne(queryWrapper);
    }

    @Override
    public void deleteByCategoryIdAndPermissionId(Integer permissionCategoryId, Integer permissionId) {
        rolePermissionMapMapper.delete(Wrappers.lambdaQuery(RolePermissionMap.class)
                                               .eq(RolePermissionMap::getPermissionCategoryId, permissionCategoryId)
                                               .eq(RolePermissionMap::getPermissionId, permissionId));
    }

    @Override
    public int deleteByRoleIdAndCategoryId(Integer roleId, Integer categoryId) {
        if (ObjectUtil.isNull(roleId) || ObjectUtil.isNull(categoryId)) {
            return 0;
        }
        return rolePermissionMapMapper.delete(Wrappers.lambdaQuery(RolePermissionMap.class)
                                                      .eq(RolePermissionMap::getRoleId, roleId)
                                                      .eq(RolePermissionMap::getPermissionCategoryId, categoryId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean fillRolePermission(Integer roleId) {
        List<Integer> menuProfileIdList = menuProfileService.findMenuProfiles()
                                               .stream()
                                               .map(MenuProfile::getMenuProfileId)
                                               .toList();
        List<Integer> permissionCategoryIdList = Arrays.stream(PermissionCategoryEnum.values())
                                                       .map(PermissionCategoryEnum::getPermissionCategoryId)
                                                       .toList();
        for (Integer menuProfileId : menuProfileIdList) {
            for (Integer categoryId : permissionCategoryIdList) {
                List<RolePermissionMap> rolePermissionMapList = permissionParseManager.findByPermissionCategory(menuProfileId, categoryId)
                                                                     .stream()
                                                                     .map(p -> RolePermissionMap.builder().permissionId(p.getPermissionId()).roleId(roleId).permissionCategoryId(categoryId)
                                                                                                .build())
                                                                     .toList();
                permissionParseManager.updateRolePermission(new UpdateRolePermissionDTO(roleId, categoryId, menuProfileId, rolePermissionMapList));
            }
        }
       return true;
    }

    @Override
    public boolean isAllPermissionByCategory(List<Integer> roleIds, PermissionCategoryEnum permissionCategoryEnum) {
        if (CollUtil.isEmpty(roleIds)) {
            return false;
        }
        return rolePermissionMapMapper.exists(Wrappers.lambdaQuery(RolePermissionMap.class)
                                                      .eq(RolePermissionMap::getPermissionCategoryId, permissionCategoryEnum.getPermissionCategoryId())
                                                      .eq(RolePermissionMap::getPermissionId, GlobalConstants.SYSTEM_ADMINISTRATOR_ROLE)
                                                      .in(RolePermissionMap::getRoleId, roleIds));
    }


    @Override
    public Set<Integer> findUserOperationPermissions(Integer userId){
        Set<Integer> groupIdSet = rolePermissionMapMapper.findUserOperationPermissionGroups(userId);
        if (groupIdSet.contains(GlobalConstants.ALL_PERMISSION)) {
            return permissionService.findPermissionsByCategory(PermissionCategoryEnum.OPERATION.getPermissionCategoryId())
                    .stream()
                    .map(Permission::getPermissionId)
                    .collect(Collectors.toSet());
        }
        List<Integer> permissionIdsByGroupIds = operationGroupMapService.findPermissionIdsByGroupIds(new ArrayList<>(groupIdSet));
        return new HashSet<>(permissionIdsByGroupIds);
    }
}
