package com.siteweb.admin.service;

import com.siteweb.admin.dto.RoleGraphicPageMapDTO;
import com.siteweb.admin.entity.RoleGraphicPageMap;

import java.util.List;

public interface RoleGraphicPageMapService {
    RoleGraphicPageMap findByRoleId(Integer roleId);

    RoleGraphicPageMap create(RoleGraphicPageMap roleGraphicPageMap);

    RoleGraphicPageMap createOrUpdate(RoleGraphicPageMap roleGraphicPageMap);

    int deleteById(Integer id);

    List<RoleGraphicPageMapDTO> findAll();

    RoleGraphicPageMapDTO findById(Integer id);

    List<RoleGraphicPageMap> findByUserId(Integer id);
}
