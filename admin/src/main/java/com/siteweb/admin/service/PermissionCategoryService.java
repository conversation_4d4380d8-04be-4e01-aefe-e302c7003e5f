package com.siteweb.admin.service;

import com.siteweb.admin.entity.PermissionCategory;

import java.util.List;

public interface PermissionCategoryService {

    List<PermissionCategory> getAllPermissionCategories();

    /**
     * 获取当前场景下的权限类别
     * @return
     */
    List<PermissionCategory> findPermissionCategoryByScene();

    PermissionCategory getPermissionCategoryById(Integer permissionCategoryId);

    int createPermissionCategory(PermissionCategory permissionCategory);

    int updatePermissionCategory(PermissionCategory permissionCategory);

    void deletePermissionCategoryById(Integer permissionCategoryId);
}
