package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.entity.SpecialtyGroup;
import com.siteweb.admin.mapper.SpecialtyGroupMapper;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.service.SpecialtyGroupService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupServiceImpl
 * @createTime 2022-01-17 08:35:47
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SpecialtyGroupServiceImpl implements SpecialtyGroupService {

    @Autowired
    SpecialtyGroupMapper specialtyGroupMapper;

    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    RolePermissionMapService rolePermissionMapService;

    private static final Integer CATEGORY = 8;

    @Override
    public List<SpecialtyGroup> findAllSpecialtyGroupsByRole(List<Integer> roleIds) {
        // 查询权限点
        List<Integer> permissionIds = new ArrayList<>();
        List<RolePermissionMap> rolePermissionMaps = rolePermissionMapService.findByRoleIdsAndCategoryId(roleIds, CATEGORY);
        if (CollectionUtil.isEmpty(rolePermissionMaps)) {
            return new ArrayList<SpecialtyGroup>();
        }
        permissionIds = rolePermissionMaps.stream().map(RolePermissionMap::getPermissionId).toList();
        return specialtyGroupMapper.selectList(new QueryWrapper<SpecialtyGroup>().in("SpecialtyGroupId", permissionIds));
    }

    @Override
    public int createSpecialtyGroup(SpecialtyGroup specialtyGroup) {
        specialtyGroup.setSpecialtyGroupId(
                primaryKeyValueService.getGlobalIdentity("TBL_SpecialtyGroup", 0));
        if (specialtyGroup.getSpecialtyGroupId() < 0) {
            // 主键分配出错
            return specialtyGroup.getSpecialtyGroupId();
        }
        specialtyGroupMapper.insert(specialtyGroup);
        // 角色绑定专业权限
        RolePermissionMap rolePermissionMap = new RolePermissionMap();
        rolePermissionMap.setRoleId(-1);
        rolePermissionMap.setPermissionCategoryId(CATEGORY);
        rolePermissionMap.setPermissionId(specialtyGroup.getSpecialtyGroupId());
        rolePermissionMapService.createRolePermissionMap(rolePermissionMap);
        return specialtyGroup.getSpecialtyGroupId();
    }

    @Override
    public int deleteById(Integer specialtyGroupId) {
        return specialtyGroupMapper.deleteById(specialtyGroupId);
    }

    @Override
    public int updateSpecialtyGroup(SpecialtyGroup specialtyGroup) {
        return specialtyGroupMapper.updateById(specialtyGroup);
    }

    @Override
    public SpecialtyGroup findById(Integer specialtyGroupId) {
        return specialtyGroupMapper.selectById(specialtyGroupId);
    }

    @Override
    public List<SpecialtyGroup> findAllSpecialtyGroups() {
        return specialtyGroupMapper.selectList(null);
    }
}
