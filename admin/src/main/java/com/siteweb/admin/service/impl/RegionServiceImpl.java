package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.RegionMapper;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.vo.RegionVO;
import com.siteweb.common.constants.GlobalConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhou
 * @description RegionServiceImpl
 * @createTime 2022-01-18 14:10:49
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RegionServiceImpl implements RegionService {

    @Autowired
    RegionMapper regionMapper;

    @Autowired
    RegionMapService regionMapService;

    @Autowired
    RolePermissionMapService rolePermissionMapService;

    @Override
    public List<Region> findAllRegions() {
        return regionMapper.selectList(null);
    }

    @Override
    public int createRegion(RegionVO regionVO) {
        Region region = regionVO.build();
        regionMapper.insert(region);
        // 建立角色与区域权限关联
        regionVO.getRoleIds().forEach(a -> {
            RolePermissionMap rolePermissionMap = new RolePermissionMap();
            rolePermissionMap.setRoleId(a);
            rolePermissionMap.setPermissionCategoryId(PermissionCategoryEnum.REGION.getPermissionCategoryId());
            rolePermissionMap.setPermissionId(region.getRegionId());
            rolePermissionMapService.createRolePermissionMap(rolePermissionMap);
        });
        return region.getRegionId();
    }

    @Override
    public int deleteById(Integer regionId) {
        regionMapService.deleteByRegionId(regionId);
        return regionMapper.deleteById(regionId);
    }

    @Override
    public int updateRegion(Region region) {
        return regionMapper.updateById(region);
    }

    @Override
    public Region findById(Integer regionId) {
        return regionMapper.selectById(regionId);
    }

    @Override
    public List<Region> findAllRegionsByRole(List<Integer> roleIds) {
        List<Integer> permissionIds;
        List<RolePermissionMap> rolePermissionMapList = rolePermissionMapService.findByRoleIdsAndCategoryId(roleIds, PermissionCategoryEnum.REGION.getPermissionCategoryId());
        if (CollUtil.isEmpty(rolePermissionMapList)) {
            return new ArrayList<>();
        }
        permissionIds = rolePermissionMapList.stream().map(RolePermissionMap::getPermissionId).toList();
        return regionMapper.selectList(new QueryWrapper<Region>().in("RegionId", permissionIds));
    }

    @Override
    public List<Region> findAllRegionsByUserId(Integer userId) {
        return regionMapper.findByUserId(userId);
    }

    @Override
    public boolean isAllRegion(Integer userId) {
        List<Region> regionList = this.findAllRegionsByUserId(userId);
        //拥有所有区域的权限 RegionId中包含-1 代表拥有所有区域的权限
        return regionList.stream()
                         .anyMatch(region -> Objects.equals(region.getRegionId(), GlobalConstants.ALL_PERMISSION));
    }

    @Override
    public boolean hasRegionMapPermissions(Integer userId, Integer resourceStructureId, Integer equipmentId) {
        List<Region> regions = findAllRegionsByUserId(userId);

        // 如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (regions.stream().anyMatch(region -> region.getRegionId().equals(GlobalConstants.ALL_PERMISSION))) {
            return true;
        }

        List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
        List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);

        // 提取具有所有设备权限的层级 ID
        Set<Integer> resourceStructureIdsWithAllEquipments = regionMaps.stream()
                                                                       .filter(regionMap -> regionMap.getEquipmentId().equals(GlobalConstants.ALL_PERMISSION))
                                                                       .map(RegionMap::getResourceStructureId)
                                                                       .collect(Collectors.toSet());

        // 提取具有特定设备权限的设备 ID
        Set<Integer> equipmentIdsWithPermissions = regionMaps.stream()
                                                             .filter(regionMap -> regionMap.getEquipmentId() > 0)
                                                             .map(RegionMap::getEquipmentId)
                                                             .collect(Collectors.toSet());

        // 检查权限
        boolean hasResourceStructurePermission = resourceStructureIdsWithAllEquipments.contains(resourceStructureId);
        boolean hasEquipmentPermission = equipmentIdsWithPermissions.contains(equipmentId);

        return hasResourceStructurePermission || hasEquipmentPermission;
    }
}
