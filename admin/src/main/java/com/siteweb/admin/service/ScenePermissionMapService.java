package com.siteweb.admin.service;

import com.siteweb.admin.entity.ScenePermissionMap;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ScenePermissionMapService {

    /**
     * 根据场景ID获取ScenePermissionMap
     * @param sceneId
     * @return
     */
    List<ScenePermissionMap> findScenePermissionMapBySceneId(Integer sceneId);

    /**
     * 根据场景id获取PermissionId集合
     * @param sceneId
     * @return
     */
    List<Integer> findPermissionIdsBySceneId(Integer sceneId);

    /**
     * 批量创建权限
     * 批量添加
     *
     * @param permissionList 权限列表
     * @param sceneId        场景id
     * @return int
     */
    int batchCreatePermission(Integer sceneId,List<String> cascadeIdList);
    int deleteBySceneIdAndPermissionId(Integer sceneId,Integer permissionId);
}
