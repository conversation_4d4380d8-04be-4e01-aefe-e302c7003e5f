package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.MenuPermissionGroup;
import com.siteweb.admin.mapper.MenuPermissionGroupMapper;
import com.siteweb.admin.service.MenuPermissionGroupMapService;
import com.siteweb.admin.service.MenuPermissionGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MenuPermissionGroupServiceImpl implements MenuPermissionGroupService {
    @Autowired
    MenuPermissionGroupMapper menuPermissionGroupMapper;
    @Autowired
    MenuPermissionGroupMapService menuPermissionGroupMapService;
    @Override
    public List<MenuPermissionGroup> findAll() {
        return menuPermissionGroupMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public MenuPermissionGroup findById(Integer id) {
        return menuPermissionGroupMapper.selectById(id);
    }

    @Override
    public int create(MenuPermissionGroup menuPermissionGroup) {
        return menuPermissionGroupMapper.insert(menuPermissionGroup);
    }

    @Override
    public int deleteById(Integer id) {
        menuPermissionGroupMapService.deleteByGroupId(id);
        return menuPermissionGroupMapper.deleteById(id);
    }

    @Override
    public int updateById(MenuPermissionGroup menuPermissionGroup) {
        return menuPermissionGroupMapper.updateById(menuPermissionGroup);
    }
}

