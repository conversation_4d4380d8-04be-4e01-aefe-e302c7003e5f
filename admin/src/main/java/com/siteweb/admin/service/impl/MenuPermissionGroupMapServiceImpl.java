package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.MenuPermissionGroupMapCreateDTO;
import com.siteweb.admin.entity.MenuPermissionGroupMap;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.mapper.MenuPermissionGroupMapMapper;
import com.siteweb.admin.service.MenuPermissionGroupMapService;
import com.siteweb.admin.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class MenuPermissionGroupMapServiceImpl implements MenuPermissionGroupMapService {
    @Autowired
    MenuPermissionGroupMapMapper menuPermissionGroupMapMapper;
    @Autowired
    @Lazy
    PermissionService permissionService;
    @Override
    public List<MenuPermissionGroupMap> findByGroupId(Integer groupId) {
        return menuPermissionGroupMapMapper.selectList(Wrappers.lambdaQuery(MenuPermissionGroupMap.class)
                                                               .eq(MenuPermissionGroupMap::getMenuPermissionGroupId, groupId));
    }

    @Override
    public boolean batchCreate(MenuPermissionGroupMapCreateDTO dto) {
        //由于菜单权限下还有菜单方案隔离，所以删除时只删除菜单方案下的权限
        deleteMenuPermissionGroupMap(dto.getMenuPermissionGroupId(), dto.getMenuProfileId());
        if (CollUtil.isEmpty(dto.getPermissionIds())) {
            return true;
        }
        return menuPermissionGroupMapMapper.batchCreate(dto.getMenuPermissionGroupId(), dto.getPermissionIds());
    }

    private void deleteMenuPermissionGroupMap(Integer menuPermissionGroupId, Integer menuProfileId) {
        Set<String> permissionCascadeIdSet = permissionService.findMenuProfilePermission(menuProfileId)
                                                              .keySet();
        List<Integer> permissionIds = permissionService.findMenuPermissionByDescription(permissionCascadeIdSet)
                                                       .stream()
                                                       .map(Permission::getPermissionId)
                                                       .toList();
        menuPermissionGroupMapMapper.delete(Wrappers.lambdaQuery(MenuPermissionGroupMap.class)
                                                    .eq(MenuPermissionGroupMap::getMenuPermissionGroupId, menuPermissionGroupId)
                                                    .in(MenuPermissionGroupMap::getPermissionId, permissionIds));
    }

    @Override
    public void deleteByGroupId(Integer menuPermissionGroupId){
        menuPermissionGroupMapMapper.delete(Wrappers.lambdaQuery(MenuPermissionGroupMap.class)
                                                    .eq(MenuPermissionGroupMap::getMenuPermissionGroupId, menuPermissionGroupId));
    }

    @Override
    public List<MenuPermissionGroupMap> findByGroupIds(List<Integer> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return menuPermissionGroupMapMapper.selectList(Wrappers.lambdaQuery(MenuPermissionGroupMap.class)
                                                               .in(MenuPermissionGroupMap::getMenuPermissionGroupId, groupIds));
    }

    @Override
    public List<Integer> findPermissionIdsByGroupIds(List<Integer> groupIds) {
        return this.findByGroupIds(groupIds)
                   .stream()
                   .map(MenuPermissionGroupMap::getPermissionId)
                   .toList();
    }
}
