package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.OperationGroupMapCreateDTO;
import com.siteweb.admin.entity.OperationGroupMap;
import com.siteweb.admin.mapper.OperationGroupMapMapper;
import com.siteweb.admin.service.OperationGroupMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class OperationGroupMapServiceImpl implements OperationGroupMapService {
    @Autowired
    OperationGroupMapMapper operationGroupMapMapper;

    @Override
    public List<OperationGroupMap> findByGroupId(Integer groupId) {
        return operationGroupMapMapper.selectList(Wrappers.lambdaQuery(OperationGroupMap.class)
                                                   .eq(OperationGroupMap::getGroupId, groupId));
    }

    @Override
    public boolean create(OperationGroupMapCreateDTO operationGroupMapCreateDTO) {
        deleteByGroupId(operationGroupMapCreateDTO.getGroupId());
        if (CollUtil.isEmpty(operationGroupMapCreateDTO.getOperationIds())) {
            return true;
        }
        return operationGroupMapMapper.batchCreate(operationGroupMapCreateDTO.getGroupId(),operationGroupMapCreateDTO.getOperationIds());
    }

    @Override
    public void deleteByGroupId(Integer groupId) {
        operationGroupMapMapper.delete(Wrappers.lambdaQuery(OperationGroupMap.class)
                                               .eq(OperationGroupMap::getGroupId, groupId));
    }

    @Override
    public List<Integer> findPermissionIdsByGroupIds(List<Integer> permissionGroupIds) {
        if (CollUtil.isEmpty(permissionGroupIds)) {
            return Collections.emptyList();
        }
        return operationGroupMapMapper.findPermissionIdsByGroupIds(permissionGroupIds);
    }
}
