package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.admin.entity.Area;
import com.siteweb.admin.mapper.AreaMapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AreaService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.OperationDetail;
import com.siteweb.utility.enums.OperationObjectType;
import com.siteweb.utility.enums.OperationTypeEnum;
import com.siteweb.utility.service.OperationDetailService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: lzy
 * @Date: 2023/3/2 10:34
 */
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, Area> implements AreaService {
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;


    @Override
    public List<Area> findByUserId(Integer userId) {
        return getBaseMapper().findByUserId(userId);
    }

    @Override
    public Boolean isAllRegion(Integer userId) {
        List<Area> areaList = this.findByUserId(userId);
        //拥有所有片区的权限 areaId中包含-1 代表拥有所有片区的权限
        return areaList.stream()
                .anyMatch(region -> Objects.equals(region.getAreaId(), -1));
    }

    @Override
    public List<Area> findAll() {
        return list();
    }

    @Override
    public Integer generateAreaId() {
        return primaryKeyValueService.getGlobalIdentity("TBL_Area",0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Area createArea(Area area) {
        save(area);
        recordOperationLog(area,OperationTypeEnum.INSERT,null);
        return area;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAreaById(Integer areaId) {
        Area area = getById(areaId);
        if (Objects.isNull(area)) {
            return false;
        }
        boolean result = removeById(areaId);
        recordOperationLog(area, OperationTypeEnum.DELETE, area);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Area updateArea(Area area) {
        Area oldArea = getById(area.getAreaId());
        if (Objects.isNull(oldArea)) {
            throw new BusinessException("旧区域没有找到,areaId" + area.getAreaId());
        }
        updateById(area);
        // 使用 recordOperationLog 记录日志
        recordOperationLog(area, OperationTypeEnum.UPDATE, oldArea);
        return area;
    }

    @Override
    public Area findById(Integer areaId) {
        return getById(areaId);
    }

    private void recordOperationLog(Area area, OperationTypeEnum operationType,Area oldArea) {
        OperationDetail operationDetail = buildOperationDetail(area, operationType, oldArea);
        operationDetailService.createOperationDetail(operationDetail);
    }

    private OperationDetail buildOperationDetail(Area area, OperationTypeEnum operationType, Area oldArea) {
        String objectId = String.valueOf(area.getAreaId());
        String oldValue = "";
        String newValue = switch (operationType) {
            case INSERT -> area.getAreaName();
            case DELETE, UPDATE -> {
                oldValue = oldArea.getAreaName();
                yield area.getAreaName();
            }
        };

        return OperationDetail.builder()
                              .userId(TokenUserUtil.getLoginUserId())
                              .objectId(objectId)
                              .objectType(OperationObjectType.AREA.value())
                              .propertyName(messageSourceUtil.getMessage("common.areaGroup"))
                              .operationTime(new Date())
                              .operationType(messageSourceUtil.getMessage(operationType.getI18n()))
                              .oldValue(oldValue)
                              .newValue(newValue)
                              .build();
    }
}
