package com.siteweb.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.dto.FileIntegrityDTO;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.job.FileIntegrityCheckJob;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.SecurityFileIntegrityService;
import com.siteweb.admin.service.SecurityReportService;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.redis.RestTemplateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.model.TriggerModel;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@Slf4j
public class SecurityFileIntegrityServiceImpl implements SecurityFileIntegrityService {

    private static final String FILE_INTEGRITY_ENABLE = "security.file.integrity.enable";

    private static final String APPSIDECAR_URL = "siteweb6.appsidecar.url";

    private static final String JOB_GROUP = "SecurityFileIntegrity";

    private static final String JOB_NAME = "fileIntegrityCheckTask";

    private static final String SECURITY_FILE_INTEGRITY_INTERVAL = "security.file.integrity.interval";

    private String url = "";

    private boolean enableStatus = false;

    public static final AtomicBoolean FileIntegrityStatus = new AtomicBoolean(false);

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    RestTemplateUtil restTemplateUtil;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Autowired
    SchedulerJobService schedulerJobService;

    @Autowired
    SecurityReportService securityReportService;


    // 每十分钟
    @PostConstruct
    public void fileIntegrityCheckTask() {
        try {
            // 开关
            SchedulerJob schedulerJob = new SchedulerJob();
            SystemConfig enable = systemConfigService.findBySystemConfigKey(FILE_INTEGRITY_ENABLE);
            if (enable == null || StrUtil.isBlank(enable.getSystemConfigValue()) || "false".equals(enable.getSystemConfigValue())) {
                log.warn("fileIntegrityCheckTask enable systemconfig warn.[config={}]", enable);
                schedulerJob.setJobGroup(Scheduler.DEFAULT_GROUP);
                schedulerJob.setJobName(JOB_NAME);
                schedulerJobService.removeSchedulerJob(schedulerJob);
                return;
            }
            SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SECURITY_FILE_INTEGRITY_INTERVAL);
            if (systemConfig == null || StrUtil.isBlank(systemConfig.getSystemConfigValue())) {
                log.warn("fileIntegrityCheckTask interval systemconfig warn.[config={}]", systemConfig);
                return;
            }
            SystemConfig appsidecarUrl = systemConfigService.findBySystemConfigKey(APPSIDECAR_URL);
            if (appsidecarUrl == null || StrUtil.isBlank(appsidecarUrl.getSystemConfigValue())) {
                log.warn("fileIntegrityCheckTask url systemconfig warn.[config={}]", appsidecarUrl);
                return;
            }
            url = appsidecarUrl.getSystemConfigValue();
            enableStatus = Boolean.valueOf(enable.getSystemConfigValue());
            // 首次发起请求判断当前是否文件校验通过，不通过发送短信
            String content = checkContent();
            if (StrUtil.isNotBlank(content)) {
                String message = localeMessageSourceUtil.getMessage("security.file.integrity.log");
                securityAuditManager.recordSecurityReport("admin", String.format(message, content),SecurityReportTypeEnum.INTEGRITY_TEST);
            } else {
                FileIntegrityStatus.set(true);
            }
            // 执行quartz任务
            schedulerJob.setClassName(FileIntegrityCheckJob.class.getName());
            schedulerJob.setRepeatInterval(Long.valueOf(systemConfig.getSystemConfigValue().trim()));
            schedulerJob.setJobGroup(Scheduler.DEFAULT_GROUP);
            schedulerJob.setJobName(JOB_NAME);
            schedulerJob.setTriggerType(TriggerModel.SIMPLE_TRIGGER_TYPE);
            schedulerJobService.removeSchedulerJob(schedulerJob);
            schedulerJobService.addSchedulerJob(schedulerJob);
        } catch (Exception e) {
            log.error("fileIntegrityCheckTask is err", e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fileIntegrityCheck() {
        String content = checkContent();
        if (StrUtil.isNotBlank(content)) {
            if (FileIntegrityStatus.get() != Boolean.FALSE) {
                String message = localeMessageSourceUtil.getMessage("security.file.integrity.log");
                securityAuditManager.recordSecurityReport("admin", String.format(message, content), SecurityReportTypeEnum.INTEGRITY_TEST);
                FileIntegrityStatus.set(false);
            }
        } else {
            FileIntegrityStatus.set(true);
        }

    }

    @Override
    public boolean fileIntergrityStatus() {
        if (!enableStatus) {
            // 判断是否有开关
            return true;
        }
        // 判断当前文件校验是否通过
        return FileIntegrityStatus.get();
    }

    /**
     * 返回文件自校验的结果
     *
     * @return
     */
    private String checkContent() {
        StringBuffer strbuffer = new StringBuffer();
        try {
            String jsonstr = restTemplateUtil.doGet(url + "softwareversion", new HashMap<>());
            log.info("fileIntegrityCheck http result.[json{}]", jsonstr);
            JSONObject jsonObject = JSONUtil.parseObj(jsonstr);
            JSONArray data = jsonObject.getJSONArray("data");
            List<FileIntegrityDTO> fileIntegrityDTOS = data.toList(FileIntegrityDTO.class);
            int i = 0;
            for (FileIntegrityDTO fileIntegrityDTO : fileIntegrityDTOS) {
                if (fileIntegrityDTO.getFlag() == 1) {
                    // 文件校验不通过
                    if (i > 0) {
                        strbuffer.append("、");
                    }
                    i++;
                    String[] split = fileIntegrityDTO.getFile().split("/");
                    strbuffer.append(split[split.length - 1]).append("(" + fileIntegrityDTO.getErrmsg() + ")");
                }
            }
        } catch (Exception e) {
            log.error("checkContent err.[err]", e);
            return strbuffer.toString();
        }
        return strbuffer.toString();
    }
}
