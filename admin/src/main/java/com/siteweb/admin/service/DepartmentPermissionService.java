package com.siteweb.admin.service;

import com.siteweb.admin.dto.DepartmentTreeDTO;

import java.util.List;
import java.util.Set;

public interface DepartmentPermissionService {
    List<DepartmentTreeDTO> findDepartmentTreeByUserId(Integer userId);

    /**
     * 是否开启了部门权限
     * @return boolean true 是  false 否
     */
    boolean enableDepartmentPermission();


    /**
     * 查找拥有的部门权限
     * 【开启部门权限且配置了部门权限的：部门树上有权限的部门可以点击查看人员，没有权限节点置灰；
     * 开启部门权限但未配置部门权限的：部门树上所在部门及以下部门可点击，其他节点置灰】
     * 通过用户id查询拥有的部门权限id
     * @param userId 用户id
     * @return {@link Set}<{@link Integer}>
     */
    Set<Integer> findDepartmentPermissionByUserId(Integer userId);

    /**
     * 通过用户id获取拥有部门权限下的用户ids
     * @param userId 当前登录的用户id
     * @return {@link List}<{@link Integer}> 拥有部门权限下的用户ids
     */
    Set<Integer> findEmployeeIdsByUserId(Integer userId);
}
