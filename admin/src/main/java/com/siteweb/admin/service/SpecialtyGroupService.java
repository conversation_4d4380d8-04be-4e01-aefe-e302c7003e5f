package com.siteweb.admin.service;

import com.siteweb.admin.entity.SpecialtyGroup;
import com.siteweb.admin.vo.SpecialtyGroupVO;

import java.util.List;

/**
 * <AUTHOR> zhou @Description SpecialtyGroupService
 * @createTime 2022-01-15 16:14:28
 */
public interface SpecialtyGroupService {

    /**
     * 根据角色id集合获取专业权限组
     * @param roleIds
     * @return
     */
    List<SpecialtyGroup> findAllSpecialtyGroupsByRole(List<Integer> roleIds);

    int createSpecialtyGroup(SpecialtyGroup specialtyGroup);

    int deleteById(Integer specialtyGroupId);

    int updateSpecialtyGroup(SpecialtyGroup specialtyGroup);

    SpecialtyGroup findById(Integer specialtyGroupId);

    List<SpecialtyGroup> findAllSpecialtyGroups();
}
