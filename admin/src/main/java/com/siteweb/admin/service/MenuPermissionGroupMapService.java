package com.siteweb.admin.service;

import com.siteweb.admin.dto.MenuPermissionGroupMapCreateDTO;
import com.siteweb.admin.entity.MenuPermissionGroupMap;

import java.util.List;

public interface MenuPermissionGroupMapService {
    List<MenuPermissionGroupMap> findByGroupId(Integer groupId);

    boolean batchCreate(MenuPermissionGroupMapCreateDTO menuPermissionGroupMapCreateDTO);

    void deleteByGroupId(Integer menuPermissionGroupId);

    List<MenuPermissionGroupMap> findByGroupIds(List<Integer> groupIds);

    List<Integer> findPermissionIdsByGroupIds(List<Integer> groupIds);
}
