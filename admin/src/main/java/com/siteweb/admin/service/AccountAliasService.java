package com.siteweb.admin.service;

import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.entity.AccountAlias;

import java.util.List;

public interface AccountAliasService {


    /**
     * 根据用户ID查询别名列表
     *
     * @return
     */
    List<AccountAlias> findAllByUserId(Integer userId);

    /**
     * 查询当前用户ID使用别名
     *
     * @param userId
     * @return
     */
    AccountAlias currentAccountAlias(Integer userId);

    /**
     * 创建用户别名
     *
     * @param accountAlias
     * @return
     */
    int createAccountAlias(AccountAlias accountAlias);

    /**
     * 修改用户别名
     *
     * @param accountAlias
     * @return
     */
    int updateAccountAlias(AccountAlias accountAlias);

    /**
     * 批量删除用户别名
     *
     * @param ids
     * @return
     */
    int batchDeteleAccountAlias(List<Integer> ids);

    /**
     * 切换当前用户别名
     * @param accountAliasId
     * @param userId
     */
    void checkedAccountAliasByUserId(Integer accountAliasId, Integer userId);

}
