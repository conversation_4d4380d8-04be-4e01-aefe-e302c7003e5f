package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.MenuPermissionTree;
import com.siteweb.admin.entity.*;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.PermissionMapper;
import com.siteweb.admin.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class PermissionServiceImpl implements PermissionService {

    private final Logger log = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Autowired
    PermissionMapper permissionMapper;

    @Autowired
    @Lazy
    MenuStructureService menuStructureService;

    @Autowired
    @Lazy
    MenuItemService menuItemService;

    @Autowired
    MenuItemStructureMapService menuItemStructureMapService;

    @Autowired
    RolePermissionMapService rolePermissionMapService;

    @Autowired
    ScenePermissionMapService scenePermissionMapService;
    @Autowired
    SceneMenuProfileMapService sceneMenuProfileMapService;

    @Override
    public Permission getPermissionById(Integer permissionId) {
        return permissionMapper.selectById(permissionId);
    }

    @Override
    public int createPermission(Permission permission) {
        return permissionMapper.createPermission(permission);
    }

    @Override
    public int updatePermission(Permission permission) {
        return permissionMapper.updateById(permission);
    }

    @Override
    public void deletePermission(Integer permissionId) {
        permissionMapper.deleteById(permissionId);
    }

    @Override
    @Transactional
    public void deletePermissions(List<Integer> permissionIds) {
        permissionMapper.deleteBatchIds(permissionIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMenuPermissionByFullId(Integer menuProfileId, String fullId) {
        //理论上fullId是唯一的，不需要获取列表，但是由于历史原因有错误的脚本或者之前系统bug导致出现多个相同的fullId，所以这里获取list去删除，保证其兼容性
        List<Permission> permissionList = permissionMapper.selectList(Wrappers.lambdaQuery(Permission.class)
                                                                              .eq(Permission::getDescription, fullId)
                                                                              .eq(Permission::getCategory, PermissionCategoryEnum.MENU.getPermissionCategoryId())
                                                                              .eq(Permission::getCaption, PermissionCategoryEnum.MENU.getPermissionCategoryName()));
        if (CollUtil.isEmpty(permissionList)) {
            return;
        }
        for (Permission permission : permissionList) {
            permissionMapper.deleteById(permission);
            //删除相关权限
            rolePermissionMapService.deleteByCategoryIdAndPermissionId(PermissionCategoryEnum.MENU.getPermissionCategoryId(), permission.getPermissionId());
            //删除场景权限
            SceneMenuProfileMap sceneMenuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
            scenePermissionMapService.deleteBySceneIdAndPermissionId(sceneMenuProfileMap.getSceneId(), permission.getPermissionId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createMenuPermission(Integer menuProfileId, String fullId, String fullTitle) {
        Permission permission = new Permission();
        permission.setCaption(PermissionCategoryEnum.MENU.getPermissionCategoryName());
        permission.setName(fullTitle);
        permission.setDescription(fullId);
        permission.setCategory(PermissionCategoryEnum.MENU.getPermissionCategoryId());
        Permission permissionFromDb = this.findByCondition(permission);
        if (ObjectUtil.isNull(permissionFromDb)) {
            this.createPermission(permission);
        }
        //批量创建权限
        SceneMenuProfileMap sceneMenuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
        scenePermissionMapService.batchCreatePermission(sceneMenuProfileMap.getSceneId(), List.of(fullId));
    }

    @Override
    public List<Permission> findPermissionsByCategory(Integer permissionCategory) {
        return permissionMapper.selectList(new QueryWrapper<Permission>().eq("Category", permissionCategory));
    }

    @Override
    public List<Permission> getPermissionsByRoleIdAndCategory(Integer roleId, Integer permissionCategory) {
        return permissionMapper.getPermissionsByRoleIdAndCategory(roleId, permissionCategory);
    }

    @Override
    public List<Permission> getPermissionsByRoleId(Integer roleId) {
        return permissionMapper.getPermissionsByRoleId(roleId);
    }

    @Override
    public List<Permission> getMenuPermissionsByRoleIds(List<Integer> roleIds) {
        return permissionMapper.getMenuPermissionsByRoleIds(roleIds);
    }

    @Override
    @Transactional
    public void syncMenuPermissions(Integer menuProfileId) {
        HashMap<String, String> menuIdHashMap = this.findMenuProfilePermission(menuProfileId);
        //添加缺失的Permission数据
        List<Permission> menuPermissions = permissionMapper.selectList(new QueryWrapper<Permission>().eq("Category", PermissionCategoryEnum.MENU.getPermissionCategoryId()));;
        for (Map.Entry<String, String> entry : menuIdHashMap.entrySet()) {
            List<Permission> tmpList = menuPermissions.stream().filter(o -> entry.getKey().equalsIgnoreCase(o.getDescription())).toList();
            if (tmpList.isEmpty()) {
                Permission permission = new Permission();
                permission.setName(entry.getValue());
                permission.setCategory(PermissionCategoryEnum.MENU.getPermissionCategoryId());
                permission.setCaption(PermissionCategoryEnum.MENU.getPermissionCategoryName());
                permission.setDescription(entry.getKey());
                createPermission(permission);
            }
        }
        //取当前方案的场景设置权限
        SceneMenuProfileMap menuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
        scenePermissionMapService.batchCreatePermission(menuProfileMap.getSceneId(),new ArrayList<>(menuIdHashMap.keySet()));
    }

    @Override
    public int deleteMenuPermission(Integer menuProfileId) {
        Set<String> cascadeIdSet = this.findMenuProfilePermission(menuProfileId).keySet();
        if (CollUtil.isEmpty(cascadeIdSet)) {
            return 0;
        }
        return permissionMapper.delete(Wrappers.lambdaQuery(Permission.class)
                                        .in(Permission::getDescription, cascadeIdSet)
                                        .eq(Permission::getCategory, PermissionCategoryEnum.MENU.getPermissionCategoryId()));
    }


    /**
     * 获取指定菜单方案下所有的权限关系
     * @param menuProfileId 场景id
     * @return {@link HashMap}<{@link String}, {@link String}>
     */
    @Override
    public HashMap<String, String> findMenuProfilePermission(Integer menuProfileId) {
        List<MenuStructure> menuStructureList = menuStructureService.findTopMenuStructuresByMenuProfileId(menuProfileId);
        HashMap<Integer, String> menuStructureIdHashMap = new HashMap<>();
        HashMap<Integer, String> menuStructureTitleHashMap = new HashMap<>();
        for (MenuStructure menuStructure : menuStructureList) {
            if (menuStructure.getParentId() > 0) continue;
            if (menuStructure.getChildren().isEmpty()) {
                menuStructureIdHashMap.put(menuStructure.getMenuStructureId(), menuStructure.getMenuStructureId().toString());
                menuStructureTitleHashMap.put(menuStructure.getMenuStructureId(), menuStructure.getTitle());
            } else {
                setFullMenuStructureIdHashMap(menuStructure.getChildren(), menuStructure.getMenuStructureId().toString(), menuStructure.getTitle(), menuStructureIdHashMap, menuStructureTitleHashMap);
            }
        }
        List<MenuItemStructureMap> menuItemStructureMaps = menuItemStructureMapService.findByMenuProfileId(menuProfileId);
        HashMap<String, String> menuIdHashMap = new HashMap<>();
        for (MenuItemStructureMap menuItemStructureMap : menuItemStructureMaps) {
            MenuItem menuItem = menuItemService.findById(menuItemStructureMap.getMenuItemId());
            if (menuItem == null) {
                continue;
            }
            String fullMenuId = "";
            String fullMenuTitle = "";
            if (menuItemStructureMap.getMenuStructureId().equals(0)) {
                fullMenuId = "0-" + menuItem.getMenuItemId();
                fullMenuTitle = menuItem.getTitle();
            } else {
                if (menuStructureIdHashMap.containsKey(menuItemStructureMap.getMenuStructureId())) {
                    fullMenuId = menuStructureIdHashMap.get(menuItemStructureMap.getMenuStructureId()) + "-" + menuItem.getMenuItemId();
                    fullMenuTitle = menuStructureTitleHashMap.get(menuItemStructureMap.getMenuStructureId()) + "-" + menuItem.getTitle();
                }
            }
            if (menuItem.getChildren().isEmpty()) {
                menuIdHashMap.put(fullMenuId, fullMenuTitle);
            } else {
                setFullMenuIdHashMap(menuItem.getChildren(), fullMenuId, fullMenuTitle, menuIdHashMap);
            }
        }
        return menuIdHashMap;
    }

    @Override
    public List<Permission> getPermissionByIds(List<Integer> permissionIds) {
        if (CollUtil.isEmpty(permissionIds)) {
            return Collections.emptyList();
        }
        return permissionMapper.selectList(Wrappers.lambdaQuery(Permission.class)
                                                   .in(Permission::getPermissionId, permissionIds));
    }

    @Override
    public Permission findByCondition(Permission permission) {
        if (ObjectUtil.isNull(permission)) {
            return null;
        }
        LambdaQueryWrapper<Permission> queryWrapper = Wrappers.lambdaQuery(Permission.class)
                                                              .eq(ObjectUtil.isNotNull(permission.getName()), Permission::getName, permission.getName())
                                                              .eq(ObjectUtil.isNotNull(permission.getCategory()), Permission::getCategory, permission.getCategory())
                                                              .eq(ObjectUtil.isNotNull(permission.getCaption()), Permission::getCaption, permission.getCaption())
                                                              .eq(ObjectUtil.isNotNull(permission.getDescription()), Permission::getDescription, permission.getDescription());
        return permissionMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Tree<String>> findMenuPermissionTree(Integer menuProfileId) {
        Set<String> cascadeIdSet = this.findMenuProfilePermission(menuProfileId).keySet();
        List<Permission> permissionList = this.findPermissionsByCategory(PermissionCategoryEnum.MENU.getPermissionCategoryId())
                                              .stream()
                                              //菜单权限需判断是否是在当前方案中
                                              .filter(permission -> cascadeIdSet.contains(permission.getDescription()))
                                              .toList();
        Set<MenuPermissionTree> menuPermissionTreeSet = new HashSet<>();
        //创造id与parentId字段,用来构建树结构
        for (Permission permission : permissionList) {
            List<String> permissionNameSplit = CharSequenceUtil.split(permission.getName(), '-');
            //子节点
            for (int i = 0; i < permissionNameSplit.size(); i++) {
                String joinName = CollUtil.join(CollUtil.sub(permissionNameSplit, 0, i + 1), "-");
                MenuPermissionTree menuPermissionTree = new MenuPermissionTree(permission);
                String parentId = CharSequenceUtil.subBefore(joinName, "-", true);
                menuPermissionTree.setId(joinName);
                menuPermissionTree.setParentId(parentId);
                //当自己就是根节点时设置parentId为0
                if(Objects.equals(menuPermissionTree.getId(),menuPermissionTree.getParentId())){
                    menuPermissionTree.setParentId("0");
                }
                //只有叶子节点设置permissionId
                if (permissionNameSplit.size() == i + 1) {
                    menuPermissionTree.setPermissionId(permission.getPermissionId());
                }
                menuPermissionTreeSet.add(menuPermissionTree);
            }
        }
        //转树，Tree<>里面泛型为id的类型
        List<MenuPermissionTree> menuPermissionTreeList = new ArrayList<>(menuPermissionTreeSet);
        return TreeUtil.build(menuPermissionTreeList, "0", (node, tree) -> {
            tree.setId(node.getId());
            tree.setName(node.getId());
            if (node.getId()
                    .contains("-")) {
                tree.setName(CharSequenceUtil.subAfter(node.getId(), "-", true));
            }
            tree.setParentId(node.getParentId());
            tree.setWeight(node.getDescription());
            tree.putExtra("category", node.getCategory());
            tree.putExtra("caption", node.getCaption());
            tree.putExtra("description", node.getDescription());
            tree.putExtra("permissionId", node.getPermissionId());
        });
    }

    @Override
    public List<Permission> findCurrentScenePermissionByCategory(Integer categoryId) {
        return permissionMapper.findCurrentScenePermissionByCategory(categoryId);
    }

    @Override
    public List<Permission> findMenuPermissionByDescription(Collection<String> descriptions) {
        if (CollUtil.isEmpty(descriptions)) {
            return Collections.emptyList();
        }
        return permissionMapper.selectList(Wrappers.lambdaQuery(Permission.class)
                                                   .in(Permission::getDescription, descriptions));
    }

    private void setFullMenuStructureIdHashMap(List<MenuStructure> menuStructureList, String parentMenuStructureId, String parentMenuStructureTitle, HashMap<Integer, String> menuStructureIdHashMap, HashMap<Integer, String> menuStructureTitleHashMap) {
        for (MenuStructure menuStructure : menuStructureList) {
            String fullMenuStructureId = String.format("%s-%d", parentMenuStructureId, menuStructure.getMenuStructureId());
            String fullMenuStructureTitle = String.format("%s-%s", parentMenuStructureTitle, menuStructure.getTitle());
            menuStructureIdHashMap.put(menuStructure.getMenuStructureId(), fullMenuStructureId);
            menuStructureTitleHashMap.put(menuStructure.getMenuStructureId(), fullMenuStructureTitle);
            setFullMenuStructureIdHashMap(menuStructure.getChildren(), fullMenuStructureId, fullMenuStructureTitle, menuStructureIdHashMap, menuStructureTitleHashMap);
        }
    }

    private void setFullMenuIdHashMap(List<MenuItem> menuItemList, String parentMenuItemId, String parentMenuTitle, HashMap<String, String> menuIdHashMap) {
        for (MenuItem menuItem : menuItemList) {
            String fullMenuId = String.format("%s-%d", parentMenuItemId, menuItem.getMenuItemId());
            String fullMenuTitle = String.format("%s-%s", parentMenuTitle, menuItem.getTitle());
            if (menuItem.getChildren().isEmpty()) {
                menuIdHashMap.put(fullMenuId, fullMenuTitle);
            }
            setFullMenuIdHashMap(menuItem.getChildren(), fullMenuId, fullMenuTitle, menuIdHashMap);
        }
    }
}
