package com.siteweb.admin.service;

import com.siteweb.admin.entity.Region;
import com.siteweb.admin.vo.RegionVO;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description RegionService
 * @createTime 2022-01-18 14:08:56
 */
public interface RegionService {

    List<Region> findAllRegions();

    int createRegion(RegionVO regionVO);

    int deleteById(Integer regionId);

    int updateRegion(Region region);

    Region findById(Integer regionId);

    /**
     * 根据角色集合获取区域
     *
     * @param roleIds
     * @return
     */
    List<Region> findAllRegionsByRole(List<Integer> roleIds);

    List<Region> findAllRegionsByUserId(Integer userId);

    /**
     * 是否拥有所有区域的权限
     * @param userId 用户id
     * @return {@link Boolean}
     */
    boolean isAllRegion(Integer userId);

    /**
     * 是否拥有该层级或者该设备的区域权限
     * @param userId 用户id
     * @param resourceStructureId 层级id
     * @param equipmentId 设备id
     * @return boolean true 是 false 否
     */
    boolean hasRegionMapPermissions(Integer userId, Integer resourceStructureId, Integer equipmentId);
}
