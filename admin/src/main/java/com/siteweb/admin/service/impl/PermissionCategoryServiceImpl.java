package com.siteweb.admin.service.impl;

import com.siteweb.admin.entity.PermissionCategory;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.PermissionCategoryMapper;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.admin.service.PermissionCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PermissionCategoryServiceImpl implements PermissionCategoryService {

    @Autowired
    PermissionCategoryMapper permissionCategoryMapper;

    @Autowired
    DepartmentPermissionService departmentPermissionService;

    @Override
    public List<PermissionCategory> getAllPermissionCategories() {
        return permissionCategoryMapper.selectList(null);
    }

    @Override
    public List<PermissionCategory> findPermissionCategoryByScene() {
        List<PermissionCategory> permissionCategoryList = permissionCategoryMapper.findPermissionCategorieByScene();
        this.addDepartmentPermission(permissionCategoryList);
        return permissionCategoryList;
    }

    /**
     * 添加部门权限
     * @param permissionCategoryList
     */
    private void addDepartmentPermission(List<PermissionCategory> permissionCategoryList) {
        if (departmentPermissionService.enableDepartmentPermission()) {
            permissionCategoryList.add(permissionCategoryMapper.selectById(PermissionCategoryEnum.DEPARTMENT.getPermissionCategoryId()));
        }
    }

    @Override
    public PermissionCategory getPermissionCategoryById(Integer permissionCategoryId) {
        return permissionCategoryMapper.selectById(permissionCategoryId);
    }

    @Override
    public int createPermissionCategory(PermissionCategory permissionCategory) {
        return permissionCategoryMapper.createPermissionCategory(permissionCategory);
    }

    @Override
    public int updatePermissionCategory(PermissionCategory permissionCategory) {
        return permissionCategoryMapper.updateById(permissionCategory);
    }

    @Override
    @Transactional
    public void deletePermissionCategoryById(Integer permissionCategoryId) {
        //删除权限分类时，一并删除该权限分类下关联的权限点
        permissionCategoryMapper.deletePermissionCategoryById(permissionCategoryId);
    }
}
