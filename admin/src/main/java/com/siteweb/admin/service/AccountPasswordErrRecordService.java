package com.siteweb.admin.service;

import com.siteweb.admin.entity.AccountPasswordErrRecord;


public interface AccountPasswordErrRecordService {

    int createAccountPasswordErrRecord(AccountPasswordErrRecord accountPasswordErrRecord);

    int updateAccountPasswordErrRecord(AccountPasswordErrRecord accountPasswordErrRecord);

    AccountPasswordErrRecord findByUserId(Integer userId);

    /**
     * 密码错误记录操作
     */
    void passwordErrAction(Integer userId);

    /**
     * 密码正确操作
     */
    void passwordSuccessAction(Integer userId);


    /**
     * 返回解冻的剩余秒数
     * @param logonId
     * @return
     */
    int remainingSecond(String logonId);

    /**
     * 判断是否暴力破解
     * @param logonId
     * @return
     */
    boolean checkVolentHack(String logonId);
}
