package com.siteweb.admin.service;

import com.siteweb.admin.dto.MenuItemDTO;
import com.siteweb.admin.dto.RouterRedirectDTO;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.vo.MenuItemVO;

import java.util.List;

public interface MenuItemService {

    List<MenuItem> findMenuItemsWithChildren();

    List<MenuItem> findMenuItemList();

    int createMenuItem(MenuItemVO menuItemVO);

    int deleteById(Integer menuItemId);

    int updateMenuItem(MenuItem menuItem);

    int batchUpdateMenuItem(List<MenuItemVO> menuItemVOList);

    MenuItem findById(Integer menuItemId);

    List<MenuItem> findByParentId(Integer parentId);

    String findTopMenuItemPath();

    /**
     * @param menuProfileId 方案id
     * @return {@link List}<{@link MenuItemDTO}>
     */
    List<MenuItemDTO> getAllFirstLevelMenuItems(Integer menuProfileId);

    /**
     * 更新菜单项排序
     * @param menuItemVOList
     * @return int
     */
    int updateMenuItemSort(List<MenuItemVO> menuItemVOList);

    /**
     * 通过菜单id获取全路径
     *
     * @param id 菜单id
     * @return {@link String}
     */
    RouterRedirectDTO findMenuItemAllPath(Integer id);
}

