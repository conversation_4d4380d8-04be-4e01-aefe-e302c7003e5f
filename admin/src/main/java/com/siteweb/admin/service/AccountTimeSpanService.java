package com.siteweb.admin.service;

import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.entity.AccountTimeSpan;

import java.util.List;

public interface AccountTimeSpanService {

    List<AccountTimeSpan> findAccountTimeSpans();

    int createAccountTimeSpan(AccountTimeSpan accountTimeSpan);

    int updateAccountTimeSpan(AccountTimeSpan accountTimeSpan);

    AccountTimeSpan findAccountTimeSpanByUserId(Integer userId);

    /**
     * 查询未配置用户
     * @return
     */
    List<AccountDTO> findNotConfigAcount();

    AccountTimeSpan findAccountTimeSpanById(Integer accountTimeSpanId);

    int deleteAccountTimeSpanById(Integer accountTimeSpanId);

    /**
     * 用户是否在访问时间段内
     * @param UserName
     * @return
     */
    boolean isTimeSpanByUserName(String UserName);
}
