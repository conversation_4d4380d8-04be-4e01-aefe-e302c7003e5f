package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.StrUtil;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.entity.IpFilterPolicy;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.mapper.IpFilterPolicyMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.admin.service.IpFilterPolicyService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SmsSecurityService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IpFilterPolicyServiceImpl implements IpFilterPolicyService {

    @Autowired
    IpFilterPolicyMapper ipFilterPolicyMapper;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    SmsSecurityService smsSecurityService;

    @Autowired
    SecurityAuditManager securityAuditManager;

    private static final String ENABLE = "login.ip.filterpolicy.enable";


    @Override
    public List<IpFilterPolicy> findIpFilterPolicys() {
        List<IpFilterPolicy> ipFilterPolicies = ipFilterPolicyMapper.selectList(null);
        ipFilterPolicies.stream().forEach(a -> {
            if (StrUtil.isNotBlank(a.getWeekSpanChar())) {
                List<String> split = StrUtil.split(a.getWeekSpanChar(), ",");
                a.setWeekSpanList(split.stream().map(Integer::parseInt).toList());
            }
        });
        return ipFilterPolicies;
    }

    @Override
    public int createIpFilterPolicy(IpFilterPolicy ipFilterPolicy) {
        parseWeekSpanList(ipFilterPolicy);
        return ipFilterPolicyMapper.insert(ipFilterPolicy);
    }

    @Override
    public int updateIpFilterPolicy(IpFilterPolicy ipFilterPolicy) {
        parseWeekSpanList(ipFilterPolicy);
        return ipFilterPolicyMapper.updateById(ipFilterPolicy);
    }

    @Override
    public IpFilterPolicy findIpFilterPolicyById(Integer ipFilterPolicyId) {
        return ipFilterPolicyMapper.selectById(ipFilterPolicyId);
    }

    @Override
    public int deleteIpFilterPolicyById(Integer ipFilterPolicyId) {
        return ipFilterPolicyMapper.deleteById(ipFilterPolicyId);
    }

    @Override
    public boolean isIpTimeSpan(String ip) {
        log.info("check ip time span.[ip={}]", ip);
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(ENABLE);
        if (systemConfig == null || StrUtil.isBlank(systemConfig.getSystemConfigValue()) || "false".equalsIgnoreCase(systemConfig.getSystemConfigValue())) {
            log.warn("isIpTimeSpan systemconfig warn.[config={}]", systemConfig);
            return true;
        }
        List<IpFilterPolicy> ipFilterPolicys = findIpFilterPolicys();
        if (CollUtil.isEmpty(ipFilterPolicys)) {
            return true;
        }
        for (IpFilterPolicy ipFilterPolicy : ipFilterPolicys) {
            String ipAddrSet = ipFilterPolicy.getIpAddrSet();
            String[] ipRange = ipAddrSet.split("-");
            if (ipRange[0].equals(ipRange[1])) {
                // 单个IP
                if (ip.equals(ipRange[0])) {
                    boolean result = checkTimeSpan(ipFilterPolicy);
                    if(Boolean.FALSE.equals(result)) {
                        securityAuditManager.recordSecurityReport("admin", getSmsText(ip),SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
                        // sendSms(ip);
                    }
                    return result;
                }
            } else {
                // IP段 将ip区间转换为long
                long minIp = Ipv4Util.ipv4ToLong(ipRange[0]);
                long maxIp = Ipv4Util.ipv4ToLong(ipRange[1]);
                long currentIp = Ipv4Util.ipv4ToLong(ip);
                if (currentIp >= minIp && currentIp < maxIp) {
                    boolean result = checkTimeSpan(ipFilterPolicy);
                    if(Boolean.FALSE.equals(result)) {
                        securityAuditManager.recordSecurityReport("admin", getSmsText(ip),SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
                        // sendSms(ip);
                    }
                    return result;
                }
            }
        }
        return true;
    }

    private boolean checkTimeSpan(IpFilterPolicy ipFilterPolicy) {
        List<Integer> weekSpanList = ipFilterPolicy.getWeekSpanList();
        if (CollUtil.isEmpty(weekSpanList)) {
            return true;
        }
        int week = DateUtil.dayOfWeek(new Date());
        return weekSpanList.contains(week) ? false : true;
    }

    private void parseWeekSpanList(IpFilterPolicy ipFilterPolicy) {
        List<Integer> weekSpanList = ipFilterPolicy.getWeekSpanList();
        String str = "";
        if (CollUtil.isNotEmpty(weekSpanList)) {
            str = weekSpanList.stream().map(String::valueOf).collect(Collectors.joining(","));
        }
        ipFilterPolicy.setWeekSpanChar(str);
    }

    public void sendSms(String ip) {
        Employee admin = employeeService.findByEmployeeId(-1);
        smsSecurityService.sendSmsSecurity(admin.getPhone(), getSmsText(ip));
    }


    private String getSmsText(String ip) {
        return String.format(messageSourceUtil.getMessage("login.ip.filterpolicy.sms.template"), ip);
    }
}
