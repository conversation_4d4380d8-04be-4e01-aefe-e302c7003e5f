package com.siteweb.admin.service;

import com.siteweb.admin.entity.MenuItemStructureMap;

import java.util.List;

public interface MenuItemStructureMapService {

    List<MenuItemStructureMap> findMenuItemStructureMaps();

    int createMenuItemStructureMap(MenuItemStructureMap menuItemStructureMap);
    MenuItemStructureMap findMenuItemStructureMapByCondition(Integer menuProfileId, Integer menuStructureId, Integer menuItemId);

    int deleteById(Integer menuItemStructureMapId);

    int updateMenuItemStructureMap(MenuItemStructureMap menuItemStructureMap);

    MenuItemStructureMap findById(Integer menuItemStructureMapId);

    List<MenuItemStructureMap> findByMenuStructureIdAndMenuProfileId(Integer menuStructureId, Integer menuProfileId);

    List<MenuItemStructureMap> findByMenuProfileId(Integer menuProfileId);

    List<MenuItemStructureMap> findByMenuProfileIdAndStructureId(Integer menuProfileId, List<Integer> menuStructureIds);

    int deleteByMenuProfileId(Integer menuProfileId);
}

