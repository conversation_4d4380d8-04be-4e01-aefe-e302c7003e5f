package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.AccountTerminalDeviceMap;
import com.siteweb.admin.mapper.AccountTerminalDeviceMapMapper;
import com.siteweb.admin.service.AccountTerminalDeviceMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AccountTerminalDeviceMapServiceImpl implements AccountTerminalDeviceMapService {
    @Autowired
    AccountTerminalDeviceMapMapper accountTerminalDeviceMapMapper;

    @Override
    public AccountTerminalDeviceMap findById(Integer userId) {
        return accountTerminalDeviceMapMapper.selectById(userId);
    }

    @Override
    public AccountTerminalDeviceMap createAccountTerminalDeviceMap(AccountTerminalDeviceMap accountTerminalDeviceMap) {
        accountTerminalDeviceMap.setUpdateTime(new Date());
        accountTerminalDeviceMapMapper.insert(accountTerminalDeviceMap);
        return accountTerminalDeviceMap;
    }

    @Override
    public AccountTerminalDeviceMap updateAccountTerminalDeviceMap(AccountTerminalDeviceMap accountTerminalDeviceMap) {
        accountTerminalDeviceMap.setUpdateTime(new Date());
        accountTerminalDeviceMapMapper.update(null, Wrappers.lambdaUpdate(AccountTerminalDeviceMap.class)
                .set(AccountTerminalDeviceMap::getTerminalDeviceId, accountTerminalDeviceMap.getTerminalDeviceId())
                .set(AccountTerminalDeviceMap::getOperatorId, accountTerminalDeviceMap.getOperatorId())
                .set(AccountTerminalDeviceMap::getUpdateTime, accountTerminalDeviceMap.getUpdateTime())
                .eq(AccountTerminalDeviceMap::getUserId, accountTerminalDeviceMap.getUserId()));
        return accountTerminalDeviceMap;
    }

    @Override
    public int unbind(Integer id, Integer loginUserId) {
        return accountTerminalDeviceMapMapper.update(null, Wrappers.lambdaUpdate(AccountTerminalDeviceMap.class)
                .set(AccountTerminalDeviceMap::getTerminalDeviceId, null)
                .set(AccountTerminalDeviceMap::getOperatorId, loginUserId)
                .set(AccountTerminalDeviceMap::getUpdateTime, new Date())
                .eq(AccountTerminalDeviceMap::getUserId, id));
    }
}