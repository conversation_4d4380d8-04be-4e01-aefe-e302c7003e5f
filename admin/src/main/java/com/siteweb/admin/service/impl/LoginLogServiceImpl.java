package com.siteweb.admin.service.impl;

import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.dto.LoginLogDTO;
import com.siteweb.admin.entity.LoginLog;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.mapper.LoginLogMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.LoginLogService;
import com.siteweb.admin.vo.LoginLogVO;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LoginLogServiceImpl
 * @createTime 2022-05-11 10:15:36
 */
@Service
public class LoginLogServiceImpl implements LoginLogService {

    @Autowired
    LoginLogMapper loginLogMapper;

    @Autowired
    AccountService accountService;
    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    private static final String SITEWEB6 = "[SiteWeb6]";
    @Override
    public Page<LoginLogDTO> findByOperatingTimeBetween(Date startTime, Date endTime, Pageable pageable) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LoginLogDTO> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LoginLogDTO> loginLogDTOPage = loginLogMapper.findByOperatingTimeBetween(page, startTime, endTime);
        return new PageImpl<>(loginLogDTOPage.getRecords(), pageable, loginLogDTOPage.getTotal());
    }

    @Override
    public int createLoginLog(LoginLogVO loginLogVO, HttpServletRequest request) {
        LoginLog loginLog = new LoginLog();
        loginLog.setUserId(loginLogVO.getUserId());
        loginLog.setOperatingType(loginLogVO.getOperatingType());
        loginLog.setClientIp(loginLogVO.getClientIp());
        loginLog.setClientType(loginLogVO.getClientType());
        loginLog.setOperatingTime(new Date());
        loginLog.setClientIp(IpUtil.getIpAddr(request));
        this.recordAudit(loginLogVO.getUserId(), loginLogVO.getOperatingType());
        return loginLogMapper.insertLoginLog(loginLog);
    }

    /**
     * 记录登录登出审计日志
     * @param operatingType
     */
    public void recordAudit(Integer userId, String operatingType) {
        String operatingString = localeMessageSourceUtil.getMessage("audit.report.login");
        if ("logout".equals(operatingType)) {
            operatingString = localeMessageSourceUtil.getMessage("audit.report.logout");
        }
        AccountDTO user = accountService.findByUserId(userId);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.LOGIN, user.getLogonId(), operatingString);
        securityAuditManager.recordSecurityReport(user.getLogonId(), SITEWEB6 + user.getLogonId() + operatingString, SecurityReportTypeEnum.IDENTITY_AUTHENTICATION);
    }
}
