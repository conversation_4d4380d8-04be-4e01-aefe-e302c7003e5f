package com.siteweb.admin.service.impl;

import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.siteweb.admin.entity.HistoryPassword;
import com.siteweb.admin.service.HistoryPasswordService;
import com.siteweb.admin.mapper.HistoryPasswordMapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("historyPasswordService")
public class HistoryPasswordServiceImpl implements HistoryPasswordService {

    @Autowired
    HistoryPasswordMapper historyPasswordMapper;

    @Autowired
    SystemConfigService systemConfigService;

    @Override
    @Transactional
    public int insertHistoryPassword(HistoryPassword historyPassword) {
        int saveCounts = 0;
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("password.history.save.counts");
        if (systemConfig != null && !systemConfig.getSystemConfigValue().trim().isEmpty()) {
            saveCounts = Integer.parseInt(systemConfig.getSystemConfigValue());
        }
        if (saveCounts > 0) {
            List<HistoryPassword> historyPasswordList = historyPasswordMapper.findHistoryPasswordsByLogonId(historyPassword.getLogonId());
            if (historyPasswordList.size() >= saveCounts) {
                List<HistoryPassword> toDeleteHistoryPasswordList = historyPasswordList.subList(saveCounts - 1, historyPasswordList.size());
                for (HistoryPassword toDelHistoryPassword : toDeleteHistoryPasswordList) {
                    historyPasswordMapper.deleteById(toDelHistoryPassword.getId());
                }
            }
        }
        return historyPasswordMapper.insert(historyPassword);
    }

    @Override
    public List<HistoryPassword> findHistoryPasswordsByLogonId(String logonId) {
        return historyPasswordMapper.findHistoryPasswordsByLogonId(logonId);
    }
}
