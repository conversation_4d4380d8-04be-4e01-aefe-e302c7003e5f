package com.siteweb.admin.service;

import com.siteweb.admin.entity.AccountTerminalDeviceMap;

import java.util.List;

public interface AccountTerminalDeviceMapService {
    AccountTerminalDeviceMap findById(Integer userId);
    AccountTerminalDeviceMap createAccountTerminalDeviceMap(AccountTerminalDeviceMap accountTerminalDeviceMap);
    AccountTerminalDeviceMap updateAccountTerminalDeviceMap(AccountTerminalDeviceMap accountTerminalDeviceMap);
    int unbind(Integer id, Integer loginUserId);
}