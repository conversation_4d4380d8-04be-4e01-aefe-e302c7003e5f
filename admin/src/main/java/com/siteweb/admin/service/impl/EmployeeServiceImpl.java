package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.ExportEmployeeDTO;
import com.siteweb.admin.entity.Account;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.entity.UserRoleMap;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.mapper.EmployeeMapper;
import com.siteweb.admin.mapper.UserRoleMapMapper;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.admin.sso.cqctcc.CqctccService;
import com.siteweb.admin.vo.EmployeeVO;
import com.siteweb.common.properties.CqctccSSOProperties;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SensitiveInfoUtils;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.entity.OperationDetail;
import com.siteweb.utility.enums.OperationObjectType;
import com.siteweb.utility.service.OperationDetailService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    EmployeeMapper employeeMapper;

    @Autowired
    AccountMapper accountMapper;

    @Autowired
    UserRoleMapper userRoleMapper;

    @Autowired
    UserRoleMapMapper userRoleMapMapper;

    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    OperationDetailService operationDetailService;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Autowired
    SecurityAuditManager securityAuditManager;

    @Autowired
    DepartmentPermissionService departmentPermissionService;
    @Autowired
    CqctccService cqctccService;
    @Autowired
    CqctccSSOProperties cqctccSSOProperties;

    @Override
    public Employee findByEmployeeId(int employeeId) {
        return employeeMapper.selectById(employeeId);
    }

    @Override
    public List<String> findEmployeeNameByIds(List<Integer> employeeIds){
        if (CollUtil.isEmpty(employeeIds)) {
            return Collections.emptyList();
        }
        return employeeMapper.findEmployeeNameByIds(employeeIds);
    }

    @Override
    public List<String> findEmployeePhoneByIds(List<Integer> employeeIds){
        if (CollUtil.isEmpty(employeeIds)) {
            return Collections.emptyList();
        }
        return employeeMapper.findEmployeePhoneByIds(employeeIds);
    }

    @Override
    public List<String> findEmployeeEmailByIds(List<Integer> notifyPersonIds) {
        if (CollUtil.isEmpty(notifyPersonIds)) {
            return Collections.emptyList();
        }
        return employeeMapper.findEmployeeEmailByIds(notifyPersonIds);
    }

    @Override
    public List<Employee> findByEmployeeIds(List<Integer> employeeIdList) {
        if (CollUtil.isEmpty(employeeIdList)) {
            return Collections.emptyList();
        }
        return employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                                                 .in(Employee::getEmployeeId, employeeIdList));
    }

    @Override
    public List<Employee> findEmployeeByDepartmentPermission(Integer userId) {
        if (!departmentPermissionService.enableDepartmentPermission()) {
            return employeeMapper.selectList(Wrappers.emptyWrapper());
        }
        //部门权限过滤
        Set<Integer> departmentIds = departmentPermissionService.findDepartmentPermissionByUserId(userId);
        if (CollUtil.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return employeeMapper.getEmployeeByDepartmentIds(departmentIds);
    }

    @Override
    @Transactional
    public int createEmployee(Integer operatorId,EmployeeVO employeeVO) {
        employeeVO.setEmployeeId(primaryKeyValueService.getGlobalIdentity("TBL_Employee", 0));
        if (employeeVO.getEmployeeId() < 0) { // 主键分配出错
            return employeeVO.getEmployeeId();
        }
        Employee employee = employeeVO.build();
        employeeMapper.insert(employee);
        if (ObjectUtil.isNotNull(operatorId)) {
            OperationDetail operationDetail = new OperationDetail();
            operationDetail.setUserId(operatorId);
            operationDetail.setObjectId(employeeVO.getEmployeeId().toString());
            operationDetail.setObjectType(OperationObjectType.EMPLOYEE.value());
            operationDetail.setPropertyName(localeMessageSourceUtil.getMessage("admin.employeeName"));
            operationDetail.setOperationTime(new Date());
            operationDetail.setOperationType(localeMessageSourceUtil.getMessage("utility.operationObjectType.insert"));
            operationDetail.setOldValue(null);
            operationDetail.setNewValue(employee.getEmployeeName());
            operationDetailService.createOperationDetail(operationDetail);
        }
        Employee operator = Optional.ofNullable(this.findByEmployeeId(operatorId)).orElse(new Employee());
        securityAuditManager.recordAuditReport(operator.getEmployeeName(),AuditReportTypeEnum.EMPLOYEE.getLevel(),AuditReportTypeEnum.EMPLOYEE.getDescribe(), localeMessageSourceUtil.getMessage("audit.report.addEmployee") + "：" + employee.getEmployeeName(), IpUtil.getIpAddr(),"");
        return employeeVO.getEmployeeId();
    }

    @Override
    @Transactional
    public int batchCreateEmployee(Integer operatorId,List<Employee> employeeList) {
        int insert = employeeMapper.batchCreate(employeeList);
        if (ObjectUtil.isNotNull(operatorId)) {
            List<OperationDetail> operationDetailList = new ArrayList<>();
            for (Employee e : employeeList) {
                OperationDetail operationDetail = new OperationDetail();
                operationDetail.setUserId(operatorId);
                operationDetail.setObjectId(e.getEmployeeId().toString());
                operationDetail.setObjectType(OperationObjectType.EMPLOYEE.value());
                operationDetail.setPropertyName(localeMessageSourceUtil.getMessage("admin.employeeName"));
                operationDetail.setOperationTime(new Date());
                operationDetail.setOperationType(localeMessageSourceUtil.getMessage("utility.operationObjectType.insert"));
                operationDetail.setOldValue(null);
                operationDetail.setNewValue(e.getEmployeeName());
                operationDetailList.add(operationDetail);
            }
            operationDetailService.batchCreate(operationDetailList);
        }
        return insert;
    }

    @Override
    @Transactional
    public int updateEmployee(EmployeeVO employeeVO) {
        Employee oldEmployee = employeeMapper.selectById(employeeVO.getEmployeeId());
        return this.updateEmployeeAccount(employeeVO, oldEmployee);
    }

    public int updateEmployeeAccount(EmployeeVO employeeVO, Employee oldEmployee) {
        if (null == oldEmployee) {
            return -1;
        }
        Employee employee = employeeVO.build();
        //由于employeeVO相关字段有脱敏处理，所以更新时要比较其内容是否有变化
        if (SensitiveInfoUtils.chineseName(oldEmployee.getEmployeeName()).equals(employeeVO.getEmployeeName())) {
            employee.setEmployeeName(oldEmployee.getEmployeeName());
        }
        if (Objects.equals(SensitiveInfoUtils.idCardNum(oldEmployee.getMobile()), employeeVO.getMobile())) {
            employee.setMobile(oldEmployee.getMobile());
        }
        if (Objects.equals(SensitiveInfoUtils.fixedPhone(oldEmployee.getPhone()), employeeVO.getPhone())) {
            employee.setPhone(oldEmployee.getPhone());
        }
        if (Objects.equals(SensitiveInfoUtils.email(oldEmployee.getEmail()), employeeVO.getEmail())) {
            employee.setEmail(oldEmployee.getEmail());
        }
        if (Objects.equals(SensitiveInfoUtils.address(oldEmployee.getAddress(), 8), employeeVO.getAddress())) {
            employee.setAddress(oldEmployee.getAddress());
        }
        if (Objects.equals(SensitiveInfoUtils.address(oldEmployee.getPostAddress(), 8), employeeVO.getPostAddress())) {
            employee.setPostAddress(oldEmployee.getPostAddress());
        }
        Account account = accountMapper.selectById(employeeVO.getEmployeeId());
        if (null != account && !account.getUserName().equals(employee.getEmployeeName())) {
            account.setUserName(employee.getEmployeeName());
            accountMapper.updateById(account);
        }
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (!oldEmployee.getEmployeeName().equals(employee.getEmployeeName()) && null != loginUserId) {
            OperationDetail operationDetail = new OperationDetail();
            operationDetail.setUserId(loginUserId);
            operationDetail.setObjectId(employeeVO.getEmployeeId().toString());
            operationDetail.setObjectType(OperationObjectType.EMPLOYEE.value());
            operationDetail.setPropertyName(localeMessageSourceUtil.getMessage("admin.employeeName"));
            operationDetail.setOperationTime(new Date());
            operationDetail.setOperationType(localeMessageSourceUtil.getMessage("utility.operationObjectType.update"));
            operationDetail.setOldValue(oldEmployee.getEmployeeName());
            operationDetail.setNewValue(employee.getEmployeeName());
            operationDetailService.createOperationDetail(operationDetail);
        }
        int result = employeeMapper.updateById(employee);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.EMPLOYEE, localeMessageSourceUtil.getMessage("audit.report.updateEmployee") + "：" + JSONUtil.toJsonStr(employee));
        return result;
    }

    @Override
    @Transactional
    public int updateEmployeeByDescription(EmployeeVO employeeVO) {
        Employee oldEmployee = employeeMapper.selectOne(Wrappers.lambdaQuery(Employee.class)
                .eq(Employee::getDescription, employeeVO.getDescription()));
        return this.updateEmployeeAccount(employeeVO, oldEmployee);
    }

    @Override
    @Transactional
    public int deleteEmployeeById(int employeeId) {
        Employee employee = employeeMapper.selectById(employeeId);
        if (null == employee) {
            return -1;
        }
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null != loginUserId) {
            OperationDetail operationDetail = new OperationDetail();
            operationDetail.setUserId(loginUserId);
            operationDetail.setObjectId(employee.getEmployeeId().toString());
            operationDetail.setObjectType(OperationObjectType.EMPLOYEE.value());
            operationDetail.setPropertyName(localeMessageSourceUtil.getMessage("admin.employeeName"));
            operationDetail.setOperationTime(new Date());
            operationDetail.setOperationType(localeMessageSourceUtil.getMessage("utility.operationObjectType.delete"));
            operationDetail.setOldValue(employee.getEmployeeName());
            operationDetail.setNewValue(null);
            operationDetailService.createOperationDetail(operationDetail);
        }
        userRoleMapper.deleteUserRoleMapsByUserId(employeeId);
        if (Boolean.TRUE.equals(cqctccSSOProperties.getEnable())) {
            // 重庆电信开关启动，需要注销人主平台的权限
            Account account = accountMapper.selectById(employeeId);
            if (Objects.nonNull(account)) {
                cqctccService.loginPrivControl(account.getLogonId(), CqctccService.DELETE_ACTION);
            }

        }
        accountMapper.deleteById(employeeId);
        int result = employeeMapper.deleteById(employeeId);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.EMPLOYEE, localeMessageSourceUtil.getMessage("audit.report.deleteEmployee") + "：" + employee.getEmployeeName());
        return result;
    }

    @Override
    @Transactional
    public int deleteEmployeesByDescription(List<String> descriptionList) {
        List<Employee> employeeList = employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                .in(Employee::getDescription, descriptionList));;
        if ( employeeList == null || employeeList.size() == 0) {
            return -1;
        }
        List<String> employeeNameList = employeeList.stream().map(Employee::getEmployeeName).toList();
        List<Integer> idList = employeeList.stream().map(Employee::getEmployeeId).toList();
        String employeeNames = StringUtils.join(employeeNameList,',');

        Integer operatorId = TokenUserUtil.getLoginUserId();
        if (ObjectUtil.isNotNull(operatorId)) {
            List<OperationDetail> operationDetailList = new ArrayList<>();
            for (Employee employee : employeeList) {
                OperationDetail operationDetail = new OperationDetail();
                operationDetail.setUserId(operatorId);
                operationDetail.setObjectId(employee.getEmployeeId().toString());
                operationDetail.setObjectType(OperationObjectType.EMPLOYEE.value());
                operationDetail.setPropertyName(localeMessageSourceUtil.getMessage("admin.employeeName"));
                operationDetail.setOperationTime(new Date());
                operationDetail.setOperationType(localeMessageSourceUtil.getMessage("utility.operationObjectType.delete"));
                operationDetail.setOldValue(employee.getEmployeeName());
                operationDetail.setNewValue(null);
                operationDetailList.add(operationDetail);
            }
            operationDetailService.batchCreate(operationDetailList);
        }

        userRoleMapMapper.delete(Wrappers.lambdaQuery(UserRoleMap.class)
                .in(UserRoleMap::getUserId, idList));
        accountMapper.deleteBatchIds(idList);
        int result = employeeMapper.deleteBatchIds(idList);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.EMPLOYEE, localeMessageSourceUtil.getMessage("audit.report.deleteEmployee") + "：" + employeeNames);
        return result;
    }

    @Override
    public ExcelWriter exportEmployee(int departmentId, boolean isHidden,  String title) {
        List<Employee> employees = findEmployeesByDepartmentIds(List.of(departmentId));
        employees = employees.stream().filter(a -> !CollUtil.contains(Arrays.asList(-2,-3,-4), a.getEmployeeId())).toList();
        List<ExportEmployeeDTO> employeeDTOS = new ArrayList<>();
        for (Employee employee : employees) {
            ExportEmployeeDTO employeeDTO = new ExportEmployeeDTO(employee, isHidden,localeMessageSourceUtil);
            employeeDTOS.add(employeeDTO);
        }
        //localeMessageSourceUtil
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("employeeName", localeMessageSourceUtil.getMessage("common.name"));
        writer.addHeaderAlias("alias", localeMessageSourceUtil.getMessage("common.alias"));
        writer.addHeaderAlias("gender", localeMessageSourceUtil.getMessage("common.gender"));
        writer.addHeaderAlias("jobNumber", localeMessageSourceUtil.getMessage("common.jobNumber"));
        writer.addHeaderAlias("employeeTitle", localeMessageSourceUtil.getMessage("common.employeeTitle"));
        writer.addHeaderAlias("phone",localeMessageSourceUtil.getMessage("common.contactOne"));
        writer.addHeaderAlias("email", localeMessageSourceUtil.getMessage("common.email"));
        writer.addHeaderAlias("mobile",localeMessageSourceUtil.getMessage("common.contactTwo"));
        writer.addHeaderAlias("address",localeMessageSourceUtil.getMessage("common.address"));
        writer.addHeaderAlias("postAddress",localeMessageSourceUtil.getMessage("common.contactAddress"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.merge(3, title);
        // 中间跳一行
        writer.passRows(1);
        writer.write(employeeDTOS,true);
        return writer;
    }

    @Override
    public List<Employee> findEmployeesByDepartmentIds(Collection<Integer> departmentPermissionList) {
        if (CollUtil.isEmpty(departmentPermissionList)) {
            return Collections.emptyList();
        }
        return employeeMapper.getEmployeeByDepartmentIds(departmentPermissionList);
    }

    @Override
    public Integer findDepartmentIdByEmployeeId(Integer employeeId) {
        Employee employee = this.findByEmployeeId(employeeId);
        if (Objects.isNull(employee)) {
            return null;
        }
        return employee.getDepartmentId();
    }

    @Override
    public Set<Integer> findEmployeeIdsByDepartmentPermission(Integer employeeId) {
        return this.findEmployeeByDepartmentPermission(employeeId)
                   .stream()
                   .map(Employee::getEmployeeId)
                   .collect(Collectors.toSet());
    }

    @Override
    public int findCardCountByEmployeeId(Integer employeeId) {
        return employeeMapper.getCardCountByEmployeeId(employeeId);
    }

    @Override
    public List<String> findEmployeeJobNumberByIds(List<Integer> employeeIds) {
        if (CollUtil.isEmpty(employeeIds)) {
            return Collections.emptyList();
        }
        return employeeMapper.findEmployeeJobNumberByIds(employeeIds);
    }

    @Override
    public boolean updateNameById(Integer employeeId, String employeeName) {
        return employeeMapper.update(Wrappers.lambdaUpdate(Employee.class)
                                             .eq(Employee::getEmployeeId, employeeId)
                                             .set(Employee::getEmployeeName, employeeName)) > 0;
    }
}
