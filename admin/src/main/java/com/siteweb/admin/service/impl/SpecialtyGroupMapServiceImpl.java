package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.dto.SpecialtyDTO;
import com.siteweb.admin.dto.SpecialtyGroupMapDTO;
import com.siteweb.admin.entity.SpecialtyGroupMap;
import com.siteweb.admin.mapper.SpecialtyGroupMapMapper;
import com.siteweb.admin.service.SpecialtyGroupMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupMapServiceImpl
 * @createTime 2022-01-18 16:49:00
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SpecialtyGroupMapServiceImpl implements SpecialtyGroupMapService {

    @Autowired
    SpecialtyGroupMapMapper specialtyGroupMapMapper;

    @Override
    public List<SpecialtyGroupMap> findBySpecialtyGroupId(Integer specialtyGroupId) {
        return specialtyGroupMapMapper.selectList(new QueryWrapper<SpecialtyGroupMap>().eq("SpecialtyGroupId", specialtyGroupId));
    }

    @Override
    public int saveSpecialtyGroupMaps(List<SpecialtyGroupMap> specialtyGroupMaps) {
        if (specialtyGroupMaps == null || specialtyGroupMaps.isEmpty()) {
            return 0;
        }
        this.deleteBySpecialtyGroupId(specialtyGroupMaps.get(0).getSpecialtyGroupId());
        for (SpecialtyGroupMap specialtyGroupMap : specialtyGroupMaps) {
            specialtyGroupMapMapper.createSpecialtyGroupMap(specialtyGroupMap);
        }
        return specialtyGroupMaps.size();
    }

    @Override
    public int deleteBySpecialtyGroupId(Integer specialtyGroupId) {
        return specialtyGroupMapMapper.delete(
                new QueryWrapper<SpecialtyGroupMap>().eq("SpecialtyGroupId", specialtyGroupId));
    }

    @Override
    public int deleteByIds(List<Integer> specialtyGroupMapIds) {
        return specialtyGroupMapMapper.deleteBatchIds(specialtyGroupMapIds);
    }

    @Override
    public List<SpecialtyDTO> getSpecialty() {
        return specialtyGroupMapMapper.getSpecialty();
    }
}
