package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.AccountAlias;
import com.siteweb.admin.mapper.AccountAliasMapper;
import com.siteweb.admin.service.AccountAliasService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AccountAliasServiceImpl implements AccountAliasService {

    @Autowired
    AccountAliasMapper accountAliasMapper;


    @Override
    public List<AccountAlias> findAllByUserId(Integer userId) {
        return accountAliasMapper.selectList(new QueryWrapper<AccountAlias>().
                eq("UserId", userId));
    }

    @Override
    public AccountAlias currentAccountAlias(Integer userId) {
        return accountAliasMapper.selectOne(new QueryWrapper<AccountAlias>().
                eq("UserId", userId).
                eq("Checked", Boolean.TRUE));
    }

    @Override
    public int createAccountAlias(AccountAlias accountAlias) {
        // 判断别名是否存在
        Boolean checkAliasRepeat = checkAliasRepeat(accountAlias.getAlias(), null);
        if (checkAliasRepeat) {
            return -1;
        }
        accountAlias.setAccountAliasId(null);
        return accountAliasMapper.insert(accountAlias);
    }

    @Override
    public int updateAccountAlias(AccountAlias accountAlias) {
        // 判断别名是否存在
        AccountAlias oldAccountAlias = accountAliasMapper.selectById(accountAlias.getAccountAliasId());
        Boolean CheckAliasRepeat = checkAliasRepeat(accountAlias.getAlias(), oldAccountAlias.getAlias());
        if (CheckAliasRepeat) {
            return -1;
        }
        return accountAliasMapper.updateById(accountAlias);
    }

    @Override
    public int batchDeteleAccountAlias(List<Integer> ids) {
        return accountAliasMapper.deleteBatchIds(ids);
    }

    @Override
    public void checkedAccountAliasByUserId(Integer accountAliasId, Integer userId) {
        List<AccountAlias> accountAliases = findAllByUserId(userId);
        for (AccountAlias accountAlias : accountAliases) {
            accountAlias.setChecked(accountAliasId.equals(accountAlias.getAccountAliasId()));
            updateAccountAlias(accountAlias);
        }
    }

    /**
     * 判断别名是否重复
     *
     * @param newAlias
     * @param oldAlias
     */
    private Boolean checkAliasRepeat(String newAlias, String oldAlias) {
        // 新增
        List<AccountAlias> accountAlias = accountAliasMapper.selectList(new QueryWrapper<AccountAlias>()
                .eq("Alias", newAlias)
                .ne(StrUtil.isNotEmpty(oldAlias), "Alias", oldAlias));
        return CollectionUtil.isNotEmpty(accountAlias) ? Boolean.TRUE : Boolean.FALSE;
    }
}
