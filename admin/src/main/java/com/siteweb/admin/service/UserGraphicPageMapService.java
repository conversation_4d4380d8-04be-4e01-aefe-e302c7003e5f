package com.siteweb.admin.service;

import com.siteweb.admin.dto.UserGraphicPageMapDTO;
import com.siteweb.admin.entity.UserGraphicPageMap;

import java.util.List;

public interface UserGraphicPageMapService {
    UserGraphicPageMap findByUserId(Integer userId);

    UserGraphicPageMap create(UserGraphicPageMap userGraphicPageMap);

    UserGraphicPageMap createOrUpdate(UserGraphicPageMap userGraphicPageMap);

    int deleteById(Integer id);

    List<UserGraphicPageMapDTO> findAll();

    UserGraphicPageMapDTO findById(Integer id);
}
