package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.UserConfig;
import com.siteweb.admin.enums.UserConfigEnum;
import com.siteweb.admin.mapper.UserConfigMapper;
import com.siteweb.admin.service.UserConfigService;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserConfigServiceImpl implements UserConfigService {
    /**
     * 默认主题
     */
    private static final String DEFAULT_THEME = "default";
    @Autowired
    UserConfigMapper userConfigMapper;
    @Autowired
    SystemConfigService systemConfigService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserConfig> findUserConfigByUserId(Integer userId) {
        this.initUserConfigKey(userId, UserConfigEnum.getAllKey(), getUserConfigKey(findUserConfigs(userId)));
        return findUserConfigs(userId);
    }

    /**
     * 获取用户已经配置了的系统参数
     *
     * @param userId 使用者主键id
     * @return {@link List}<{@link UserConfig}>
     */
    private List<UserConfig> findUserConfigs(Integer userId) {
        return userConfigMapper.selectList(Wrappers.lambdaQuery(UserConfig.class)
                                                   .eq(UserConfig::getUserId, userId));
    }

    private Set<String> getUserConfigKey(Collection<UserConfig> userConfigCollection){
        return userConfigCollection.stream().map(UserConfig::getConfigKey).collect(Collectors.toSet());
    }

    /**
     * 初始化用户缺少的键
     *
     * @param userId         用户id
     * @param userConfigKey 用户设置键
     */
    public void initUserConfigKey(Integer userId, Set<String> allUserConfigKey, Set<String> userConfigKey){
        //取差集获取用户没有配置的key
        allUserConfigKey.removeAll(userConfigKey);
        if (CollUtil.isEmpty(allUserConfigKey)) {
            return;
        }
        List<UserConfig> configList = new ArrayList<>(allUserConfigKey.size());
        for (String configKey : allUserConfigKey) {
            UserConfigEnum userConfigEnum = UserConfigEnum.getByConfigKey(configKey);
            configList.add(new UserConfig(userId, userConfigEnum.getConfigType(), userConfigEnum.getConfigKey()));
        }
        userConfigMapper.batchInsert(userId,configList);
    }

    @Override
    public int batchUpdate(List<UserConfig> userConfigList) {
        if (CollUtil.isEmpty(userConfigList)) {
            return 0;
        }
        return userConfigMapper.batchUpdate(userConfigList);
    }

    /**
     * 获取用户配置信息
     * @param userId     用户id
     * @param configKey 配置键
     * @return {@link UserConfig}
     */
    private UserConfig findByUserIdAndKey(Integer userId, String configKey) {
        return userConfigMapper.selectOne(Wrappers.lambdaQuery(UserConfig.class)
                                                  .eq(UserConfig::getUserId, userId)
                                                  .eq(UserConfig::getConfigKey, configKey));
    }

    @Override
    public boolean enableTts(Integer userId) {
        UserConfig enableTtsUserConfig = findByUserIdAndKey(userId, UserConfigEnum.ENABLE_TTS.getConfigKey());
        if (Objects.isNull(enableTtsUserConfig) || CharSequenceUtil.isBlank(enableTtsUserConfig.getConfigValue())) {
            //是否开启了系统级的TTS
            String ttsSystemEnable = userConfigMapper.findTtsSystemEnable();
            if (CharSequenceUtil.isBlank(ttsSystemEnable)) {
                return false;
            }
            return Boolean.parseBoolean(ttsSystemEnable);
        }
        return Boolean.parseBoolean(enableTtsUserConfig.getConfigValue());
    }

    @Override
    public String findUserTheme(Integer userId) {
        UserConfig themeConfig = findByUserIdAndKey(userId, UserConfigEnum.THEME.getConfigKey());
        //用户没有配置主题
        if (Objects.isNull(themeConfig) || CharSequenceUtil.isBlank(themeConfig.getConfigValue())) {
            //使用全局主题
            SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.THEME.getSystemConfigKey());
            return Optional.ofNullable(systemConfig).map(SystemConfig::getSystemConfigValue).orElse(DEFAULT_THEME);
        }
        //使用用户自定义主题
        return themeConfig.getConfigValue();
    }

    @Override
    public List<UserConfig> findUserConfigByUserIdAndType(Integer userId, Integer configType) {
        this.initUserConfigKey(userId, UserConfigEnum.getKeyByType(configType), getUserConfigKey(findByUserIdAndType(userId, configType)));
        return findByUserIdAndType(userId,configType);
    }

    private List<UserConfig> findByUserIdAndType(Integer userId, Integer configType) {
        return userConfigMapper.selectList(Wrappers.lambdaQuery(UserConfig.class)
                                                   .eq(UserConfig::getUserId, userId)
                                                   .eq(UserConfig::getConfigType, configType));
    }
}
