package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.Scene;
import com.siteweb.admin.entity.SceneMenuProfileMap;
import com.siteweb.admin.enums.SceneEnum;
import com.siteweb.admin.mapper.SceneMapper;
import com.siteweb.admin.service.MenuProfileService;
import com.siteweb.admin.service.SceneMenuProfileMapService;
import com.siteweb.admin.service.SceneService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * @author: Habits
 * @time: 2022/5/17 10:14
 * @description:
 **/
@Service
@Transactional(rollbackFor = Exception.class)
public class SceneServiceImpl implements SceneService {

    @Autowired
    SceneMapper sceneMapper;

    @Autowired
    SceneMenuProfileMapService sceneMenuProfileMapService;

    @Autowired
    MenuProfileService menuProfileService;

    @Autowired
    private SystemConfigService systemConfigService;
    static final int checked = 1;

    @Override
    public Scene currentScene() {
        return sceneMapper.selectOne(new QueryWrapper<Scene>().eq("Checked", checked));
    }

    @Override
    public List<Scene> findScenes() {
        return sceneMapper.selectList(null);
    }

    @Override
    public int updateScene(Scene scene) {
        return sceneMapper.updateById(scene);
    }

    @Override
    public Scene findSceneById(Integer sceneId) {
        return sceneMapper.selectById(sceneId);
    }

    @Override
    public void checkedScene(Integer sceneId,Integer menuProfileId) {
        List<Scene> scenes = findScenes();
        for (Scene scene : scenes) {
            scene.setChecked(scene.getSceneId().equals(sceneId));
            updateScene(scene);
        }
        SceneMenuProfileMap sceneMenuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
        if(sceneMenuProfileMap != null){
            // 启用关联的场景菜单 先已通过菜单方案来主动切换场景
//            menuProfileService.updateCheckedStatus(sceneMenuProfileMap.getMenuProfileId());
            // 切换场景修改相关系统参数
            updateSceneSystemConfig(sceneId);
        }
    }

    private void updateSceneSystemConfig(Integer sceneId) {
        if (SceneEnum.NETS.getValue().equals(sceneId)) {
            systemConfigService.updateSystemValueByKey("system.tag", "s2");
            systemConfigService.updateSystemValueByKey("global.search.device.alarm", "false");
            return;
        } else if (SceneEnum.REBOT.getValue().equals(sceneId)) {
            systemConfigService.updateSystemValueByKey("alarmstatistics.show", "false");
            return;
        }
        systemConfigService.updateSystemValueByKey("system.tag", "siteweb6");
        systemConfigService.updateSystemValueByKey("global.search.device.alarm", "true");
    }
}
