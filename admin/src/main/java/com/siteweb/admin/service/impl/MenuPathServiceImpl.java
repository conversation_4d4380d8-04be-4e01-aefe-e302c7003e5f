package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.UserRole;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.service.*;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 菜单路径服务实现类
 * 负责处理用户菜单权限和路径相关的业务逻辑
 */
@Service
public class MenuPathServiceImpl implements MenuPathService {

    private static final String PATH_PREFIX = "/entry/";
    private static final String PATH_SEPARATOR = "/";
    private static final String MENU_LEVEL_SEPARATOR = "-";

    @Autowired
    private MenuItemService menuItemService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private RolePermissionMapService rolePermissionMapService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public Set<String> getCurrentMenuPathsByUserId(Integer userId) {
        List<MenuItem> menuItems = menuItemService.findMenuItemList();
        List<Permission> menuPermissions = getMenuPermissions(userId);

        return menuPermissions.stream()
                              .map(Permission::getDescription)
                              .filter(CharSequenceUtil::isNotBlank)
                              .map(description -> buildMenuPath(description, menuItems))
                              .filter(Objects::nonNull)
                              .collect(Collectors.toSet());
    }

    /**
     * 构建菜单路径
     * @param permissionDescription 权限描述，格式为: "x-y-z"，其中x,y,z为菜单path
     * @param menuItems 所有菜单项列表
     * @return 构建好的菜单路径，如果构建失败返回null
     */
    private String buildMenuPath(String permissionDescription, List<MenuItem> menuItems) {
        String[] menuIds = permissionDescription.split(MENU_LEVEL_SEPARATOR);
        if (menuIds.length <= 1) {
            return null;
        }

        StringBuilder pathBuilder = new StringBuilder(PATH_PREFIX);

        for (int i = 1; i < menuIds.length; i++) {
            Integer menuId = Integer.parseInt(menuIds[i]);
            String menuPath = findMenuPath(menuId, menuItems);
            if (menuPath != null) {
                pathBuilder.append(menuPath).append(PATH_SEPARATOR);
            }
        }

        // 移除最后一个分隔符
        return pathBuilder.length() > PATH_PREFIX.length() ? pathBuilder.substring(0, pathBuilder.length() - 1) : null;
    }

    /**
     * 根据菜单ID查找对应的路径
     */
    private String findMenuPath(Integer menuId, List<MenuItem> menuItems) {
        return menuItems.stream()
                        .filter(item -> item.getMenuItemId().equals(menuId))
                        .map(MenuItem::getPath)
                        .filter(CharSequenceUtil::isNotBlank)
                        .findFirst()
                        .orElse(null);
    }

    /**
     * 获取用户拥有的菜单权限
     * 根据系统配置和用户角色判断用户可访问的菜单
     *
     * @param userId 用户ID
     * @return 用户拥有的菜单权限列表
     */
    private List<Permission> getMenuPermissions(Integer userId) {
        List<Integer> roleIds = userRoleService.findRolesByUserId(userId)
                                               .stream()
                                               .map(UserRole::getRoleId)
                                               .toList();

        if (CollUtil.isEmpty(roleIds)) {
            return Collections.emptyList();
        }

        boolean isAllMenuPermission = rolePermissionMapService.isAllPermissionByCategory(roleIds, PermissionCategoryEnum.MENU);
        boolean enablePermission = systemConfigService.findBooleanValue(SystemConfigEnum.MENU_PERMISSION_ENABLE);

        // 未启用菜单权限或拥有所有菜单权限时，返回所有菜单权限
        if (!enablePermission || isAllMenuPermission) {
            return permissionService.findPermissionsByCategory(PermissionCategoryEnum.MENU.getPermissionCategoryId());
        }

        return permissionService.getMenuPermissionsByRoleIds(roleIds);
    }
}
