package com.siteweb.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.admin.entity.Area;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/2 10:34
 */
public interface AreaService extends IService<Area> {

    List<Area> findByUserId(Integer userId);
    Boolean isAllRegion(Integer userId);

    /**
     * 获取所有片区
     * @return {@link List }<{@link Area }> 所有区域
     */
    List<Area> findAll();

    /**
     * 生成片区id
     * @return {@link Integer } 生成好的片区id
     */
    Integer generateAreaId();

    Area createArea(Area area);

    boolean deleteAreaById(Integer areaId);

    Area updateArea(Area area);

    Area findById(Integer areaId);
}
