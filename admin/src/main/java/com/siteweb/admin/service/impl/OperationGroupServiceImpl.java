package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.OperationGroup;
import com.siteweb.admin.mapper.OperationGroupMapper;
import com.siteweb.admin.service.OperationGroupMapService;
import com.siteweb.admin.service.OperationGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class OperationGroupServiceImpl implements OperationGroupService {
    @Autowired
    OperationGroupMapper operationGroupMapper;
    @Autowired
    OperationGroupMapService operationGroupMapService;

    @Override
    public List<OperationGroup> findAll() {
        return operationGroupMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public OperationGroup findById(Integer id) {
        return operationGroupMapper.selectById(id);
    }

    @Override
    public int deleteById(Integer id) {
        operationGroupMapService.deleteByGroupId(id);
        return operationGroupMapper.deleteById(id);
    }

    @Override
    public int create(OperationGroup operationGroup) {
        int primaryKey = getPrimaryKey();
        operationGroup.setGroupId(primaryKey);
        return operationGroupMapper.insert(operationGroup);
    }

    @Override
    public int updateById(OperationGroup operationGroup) {
        return operationGroupMapper.updateById(operationGroup);
    }

    /**
     * 由于历史原因，操作权限表的主键并没有自增主键，暂时只是获取最大id并向上加一来当作主键。
     * @return int
     */
    public int getPrimaryKey(){
        Integer maxId = operationGroupMapper.getMaxId();
        if (Objects.isNull(maxId)) {
            return 1;
        }
        return ++maxId;
    }
}
