package com.siteweb.admin.service.impl;

import com.siteweb.admin.entity.SoftWareVersion;
import com.siteweb.admin.mapper.SoftWareVersionMapper;
import com.siteweb.admin.service.SoftWareVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/4/24 10:35
 */
@Service
public class SoftWareVersionServiceImpl implements SoftWareVersionService {

    @Autowired
    SoftWareVersionMapper softWareVersionMapper;

    @Override
    public List<SoftWareVersion> findSoftWareVersion() {
        return softWareVersionMapper.selectList(null);
    }
}
