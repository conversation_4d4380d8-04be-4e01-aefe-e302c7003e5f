package com.siteweb.admin.service;

import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.dto.AccountOnlineDTO;
import com.siteweb.admin.entity.Account;
import com.siteweb.admin.vo.AccountEnableVO;
import com.siteweb.admin.vo.AccountVO;
import com.siteweb.admin.vo.ForgetPasswordVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AccountService {

    List<AccountDTO> findByLogonId(String logonId);

    List<AccountDTO> findByMobile(String mobile);

    AccountDTO findByUserId(Integer userId);

    List<AccountDTO> findAll();

    List<Integer> findAllUserIds();

    int updateAccountPassword(Integer userId, String oldPassword, String newPassword);

    int forgetPassword(ForgetPasswordVO forgetPasswordVO);

    int resetAccountPassword(Integer userId, String password);

    int setAccountThemeName(Integer userId, String themeName);

    int createAccount(Integer operatorId,AccountVO accountVO);

    int updateAccount(AccountVO accountVO);

    int updateAccountEnable(AccountEnableVO accountEnableVO);

    int deleteAccountByUserId(int userId);

    void accountHeartbeat(String token, Integer userId, String ipAddr);

    List<AccountOnlineDTO> findAccountOnline();

    /**
     * 检查账号名是否重复
     * @param newLogonId
     * @param oldLogonId
     * @return
     */
    List<Account> checkRepeatByLogonId(String newLogonId, String oldLogonId);

    /**
     * 根据用户权限过滤用户列表
     * @param userId 用户id
     * @return {@link List}<{@link AccountDTO}>
     */
    List<AccountDTO> findAllByPermission(Integer userId);

    /**
     * 根据用户权限获取拥有权限的用户ids
     * @param userId 用户id
     * @return {@link List}<{@link AccountDTO}>
     */
    Set<Integer> findUserIdByPermission(Integer userId);

    /**
     * 验证用户密码是否正确
     * 用于发送控制命令中的密码校验
     * @param userId
     * @param password
     * @return boolean
     */
    boolean validAccountPassword(Integer userId, String password);

    /**
     * 通过部门id查找账号列表
     *
     * @param departmentId 部门id
     * @return {@link List}<{@link AccountDTO}>
     */
    List<AccountDTO> findByDepartmentId(Integer departmentId);

    /**
     * 通过token让用户强制离线 【仅在用户并发数为1时生效】
     * @param token 用户认证信息
     * @return boolean
     */
    boolean accountOffline(String token);

    int batchCreateAccount(Integer loginUserId, List<AccountVO> accountVOList);

    Integer findUserIdByLogonId(String logonId);

    int updatePassword(Integer userId, String password);

    Map<Integer,String> findUserNameMapByUserIds(List<Integer> userIds);

    /**
     * 根据登录名验证用户密码是否正确
     */
    boolean validAccountPasswordVO(String userName, String password);

    String findUserNameByLogonId(String logonId);

    String findUserNameByUserId(Integer userId);

    boolean passwordInValidTime(String userName);
}
