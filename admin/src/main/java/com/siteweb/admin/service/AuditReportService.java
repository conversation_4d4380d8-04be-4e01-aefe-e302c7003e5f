package com.siteweb.admin.service;

import com.siteweb.admin.dto.AuditReportParamDto;
import com.siteweb.admin.entity.AuditReport;

import java.util.HashSet;
import java.util.List;

public interface AuditReportService {
    /**
     * 获取当前所记录的总数量
     * @return {@link Long}
     */
    Integer findCurrentCount();
    /**
     * 查询所有报表记录
     * @return {@link List}<{@link AuditReport}> 审计报表记录
     */
    List<AuditReport> findAll();

    List<AuditReport> findAuditReport(AuditReportParamDto auditReportParamDto);
    /**
     * 添加审计报表记录
     * @param auditReport 审计报表记录
     */
    void saveAuditReport(AuditReport auditReport);

    /**
     * 删除最大记录数之后的记录
     * @param count 需要删除的记录数
     */
    void removeRecordCount(Integer count);

    /**
     * 审计日志最大存储数
     * @return {@link Integer}
     */
    Integer findAuditMaxCount();

    /**
     * 查看设置的审计级别
     * @return {@link HashSet}<{@link Integer}>
     */
    HashSet<Integer> findAuditLevel();

    /**
     * 是否开启审计功能
     * @return {@link Boolean}
     */
    Boolean isEnableAudit();
}
