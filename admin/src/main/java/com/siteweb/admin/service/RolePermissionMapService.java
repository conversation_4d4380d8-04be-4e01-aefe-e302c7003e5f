package com.siteweb.admin.service;

import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.vo.UserRoleRightVO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface RolePermissionMapService {

    List<RolePermissionMap> findRolePermissionMaps();

    int createRolePermissionMap(RolePermissionMap rolePermissionMap);


    int deleteByRoleId(Integer roleId);

    int deleteByRoleIdAndCascadeIds(Integer roleId, Collection<String> cascadeIdSet);

    /**
     * 根据角色ID集合以及权限类别,获取RolePermissionMap集合
     *
     * @param roleIds
     * @param permissionCategoryId
     * @return
     */
    List<RolePermissionMap> findByRoleIdsAndCategoryId(List<Integer> roleIds, Integer permissionCategoryId);

    int deleteById(Integer rolePermissionMapId);

    int updateRolePermissionMap(RolePermissionMap rolePermissionMap);

    RolePermissionMap findById(Integer rolePermissionMapId);

    /**
     * 根据角色id获取RolePermissionMap集合
     *
     * @param roleId
     * @return
     */
    List<RolePermissionMap> getRolePermissionMapsByRoleId(Integer roleId);

    /**
     * 批量保存
     *
     * @param rolePermissionMapList
     * @return
     */
    int batchCreateRolePermissionMap(List<RolePermissionMap> rolePermissionMapList);

    /**
     * 批量同步TBL_UserRoleRight
     * @param userRoleRightVOList
     * @return int
     */
    int batchCreateUserRoleRight(List<UserRoleRightVO> userRoleRightVOList);

    /**
     * 根据角色id和操作类型id删除UserRoleRight
     *
     * @param roleId
     * @param operationType
     * @return
     */
    int delUserRoleRightByRoleIdAndOperationType(Integer roleId, Integer operationType);

    /**
     * 统计UserRoleRight
     *
     * @param roleId
     * @param operationId
     * @param OperationType
     * @return
     */
    int getUserRoleRightCount(Integer roleId, Integer operationId, Integer OperationType);

    /**
     * 获取该角色拥有的片区权限组
     *
     * @param roleId
     * @return
     */
    List<RolePermissionMap> getAreaRolePermissionMaps(Integer roleId);

    /**
     * 获取该角色拥有的专业权限组
     *
     * @param roleId
     * @return
     */
    List<RolePermissionMap> getSpecialtyPermissionMaps(Integer roleId);

    /**
     * 根据用户id获取用户权限集合
     * @param userId 用户id
     * @param permissionCategoryId 权限类别
     * @return 角色权限
     */
    Set<Integer> findRolePermissionsByUserId(Integer userId, Integer permissionCategoryId);

    /**
     * 根据用户id和权限类别id 获取该用户下的所有角色权限信息
     *
     * @param userId
     * @return
     */
    List<RolePermissionMap> getRolePermissionMapsByUserIdAndCategoryId(Integer userId, Integer permissionCategoryId);

    /**
     * 根据用户id 获取该用户下的所有角色权限
     *
     * @param userId
     * @return rolePermission 用户权限id集合
     */
    List<Integer> findRolePermissionMapsByUserId(Integer userId);

    RolePermissionMap findByCondition(RolePermissionMap rolePermissionMap);

    void deleteByCategoryIdAndPermissionId(Integer permissionCategoryId, Integer permissionId);

    int deleteByRoleIdAndCategoryId(Integer roleId,Integer categoryId);
    /**
     * 填充角色可获取的所有权限
     * 【解决由于不允许修改管理员角色的权限，可部分老现场的管理员权限不满想修改的尴尬情况】
     * @param roleId
     * @return boolean
     */
    boolean fillRolePermission(Integer roleId);

    boolean isAllPermissionByCategory(List<Integer> roleIds, PermissionCategoryEnum permissionCategoryEnum);

    /**
     * 查找用户的操作权限
     *
     * @param userId 用户id
     * @return {@link Set }<{@link Integer }> 操作权限ids
     */
    Set<Integer> findUserOperationPermissions(Integer userId);
}

