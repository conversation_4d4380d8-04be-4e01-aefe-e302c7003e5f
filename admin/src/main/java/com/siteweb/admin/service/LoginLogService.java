package com.siteweb.admin.service;

import com.siteweb.admin.dto.LoginLogDTO;
import com.siteweb.admin.entity.LoginLog;
import com.siteweb.admin.vo.LoginLogVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LoginLogService
 * @createTime 2022-05-11 10:13:53
 */
public interface LoginLogService {

    Page<LoginLogDTO> findByOperatingTimeBetween(Date startTime, Date endTime, Pageable pageable);

    int createLoginLog(LoginLogVO loginLogVO, HttpServletRequest request);
}
