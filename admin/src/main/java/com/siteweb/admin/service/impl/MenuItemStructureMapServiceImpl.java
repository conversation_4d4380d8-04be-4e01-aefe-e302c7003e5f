package com.siteweb.admin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.MenuItemStructureMap;
import com.siteweb.admin.mapper.MenuItemStructureMapMapper;
import com.siteweb.admin.service.MenuItemStructureMapService;
import com.siteweb.admin.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MenuItemStructureMapServiceImpl implements MenuItemStructureMapService {

    @Autowired
    MenuItemStructureMapMapper menuItemStructureMapMapper;
    @Autowired
    @Lazy
    PermissionService permissionService;

    @Override
    public List<MenuItemStructureMap> findMenuItemStructureMaps() {
        return menuItemStructureMapMapper.selectList(null);
    }

    @Override
    public int createMenuItemStructureMap(MenuItemStructureMap menuItemStructureMap) {
        MenuItemStructureMap msm = this.findMenuItemStructureMapByCondition(menuItemStructureMap.getMenuProfileId(), menuItemStructureMap.getMenuStructureId(), menuItemStructureMap.getMenuItemId());
        //存在修改排序
        if (ObjectUtil.isNotNull(msm)) {
            msm.setSortIndex(menuItemStructureMap.getSortIndex());
            return this.updateMenuItemStructureMap(msm);
        }
        //不存在添加
        return menuItemStructureMapMapper.insert(menuItemStructureMap);
    }


    /**
     * @param menuProfileId 方案id
     * @param menuStructureId 菜单目录id
     * @param menuItemId 菜单id
     * @return {@link MenuItemStructureMap}
     */
    @Override
    public MenuItemStructureMap findMenuItemStructureMapByCondition(Integer menuProfileId, Integer menuStructureId, Integer menuItemId) {
        return menuItemStructureMapMapper.selectOne(Wrappers.lambdaQuery(MenuItemStructureMap.class)
                                                            .eq(MenuItemStructureMap::getMenuProfileId, menuProfileId)
                                                            .eq(ObjectUtil.isNotNull(menuStructureId),MenuItemStructureMap::getMenuStructureId, menuStructureId)
                                                            .eq(MenuItemStructureMap::getMenuItemId, menuItemId));
    }

    @Override
    public int deleteById(Integer menuItemStructureMapId) {
        return menuItemStructureMapMapper.deleteById(menuItemStructureMapId);
    }

    @Override
    public int updateMenuItemStructureMap(MenuItemStructureMap menuItemStructureMap) {
        return menuItemStructureMapMapper.updateById(menuItemStructureMap);
    }

    @Override
    public MenuItemStructureMap findById(Integer menuItemStructureMapId) {
        return menuItemStructureMapMapper.selectById(menuItemStructureMapId);
    }

    @Override
    public List<MenuItemStructureMap> findByMenuStructureIdAndMenuProfileId(Integer menuStructureId, Integer menuProfileId) {
        Map<String, Object> map = new HashMap<>();
        map.put("MenuStructureId", menuStructureId);
        map.put("MenuProfileId", menuProfileId);
        return menuItemStructureMapMapper.selectList(new QueryWrapper<MenuItemStructureMap>().allEq(map));
    }

    @Override
    public List<MenuItemStructureMap> findByMenuProfileId(Integer menuProfileId) {
        return menuItemStructureMapMapper.selectList(new QueryWrapper<MenuItemStructureMap>().eq("MenuProfileId", menuProfileId));
    }

    @Override
    public List<MenuItemStructureMap> findByMenuProfileIdAndStructureId(Integer menuProfileId, List<Integer> menuStructureIds) {
        return menuItemStructureMapMapper.selectList(Wrappers.lambdaQuery(MenuItemStructureMap.class)
                                                             .eq(MenuItemStructureMap::getMenuProfileId, menuProfileId)
                                                             .in(MenuItemStructureMap::getMenuStructureId, menuStructureIds));
    }

    @Override
    public int deleteByMenuProfileId(Integer menuProfileId) {
        return menuItemStructureMapMapper.delete(Wrappers.lambdaQuery(MenuItemStructureMap.class)
                                                         .eq(MenuItemStructureMap::getMenuProfileId, menuProfileId));
    }
}
