package com.siteweb.admin.service;

import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.vo.RegionMapCreteVO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description RegionMapService
 * @createTime 2022-01-18 15:21:03
 */
public interface RegionMapService {

    List<RegionMap> findByRegionId(Integer regionId);

    List<RegionMap> findByUserId(Integer userId);

    boolean saveRegionMaps(RegionMapCreteVO regionMaps);

    int deleteByRegionId(Integer regionId);

    List<RegionMap> findByRegionIds(Collection<Integer> regionIds);

    /**
     * 根据 regionId 集合查询层级ids集合
     * @param regionIds
     * @return resourceStructureIds 层级id集合
     */
    Set<Integer> findResourceStructureIdsByRegionIds(Collection<Integer> regionIds);

}
