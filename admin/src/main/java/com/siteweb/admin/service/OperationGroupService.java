package com.siteweb.admin.service;

import com.siteweb.admin.entity.OperationGroup;

import java.util.List;

public interface OperationGroupService {
    /**
     * 获取所有操作权限分组
     * @return {@link List}<{@link OperationGroup}>
     */
    List<OperationGroup> findAll();

    /**
     * 根据操作权限分组id查找权限分组详情
     * @param id 操作权限分组id
     * @return {@link OperationGroup}
     */
    OperationGroup findById(Integer id);

    /**
     * 根据操作权限分组id删除操作权限分组
     * @param id 操作权限分组id
     * @return int
     */
    int deleteById(Integer id);

    /**
     * 添加操作权限分组
     * @param operationGroup 操作分组实例
     * @return int
     */
    int create(OperationGroup operationGroup);

    /**
     * 更新操作权限组
     * @param operationGroup 操作权限实体
     * @return int 所影响的行数
     */
    int updateById(OperationGroup operationGroup);
}
