package com.siteweb.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.entity.ScenePermissionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.PermissionMapper;
import com.siteweb.admin.mapper.ScenePermissionMapMapper;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.service.ScenePermissionMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @author: Habits
 * @time: 2022/5/18 15:28
 * @description:
 **/
@Service
public class ScenePermissionMapServiceImpl implements ScenePermissionMapService {

    @Autowired
    ScenePermissionMapMapper scenePermissionMapMapper;
    @Autowired
    PermissionMapper permissionMapper;
    @Autowired
    RolePermissionMapService rolePermissionMapService;

    @Override
    public List<ScenePermissionMap> findScenePermissionMapBySceneId(Integer sceneId) {
        return scenePermissionMapMapper.selectList(new QueryWrapper<ScenePermissionMap>().eq("SceneId", sceneId));
    }

    @Override
    public List<Integer> findPermissionIdsBySceneId(Integer sceneId) {
        List<ScenePermissionMap> scenePermissionMaps = findScenePermissionMapBySceneId(sceneId);
        return scenePermissionMaps.stream().map(ScenePermissionMap::getPermissionId).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreatePermission(Integer sceneId,List<String> cascadeIdList) {
        //系统中存在的权限场景映射关系
        Set<String> existsScenePermissionSet = new HashSet<>(scenePermissionMapMapper.findExistsScenePermission(cascadeIdList, sceneId));
        List<String> notExitsDescription = cascadeIdList.stream()
                                                        .filter(description -> !existsScenePermissionSet.contains(description))
                                                        .toList();
        List<ScenePermissionMap> scenePermissionMapList = this.findPermissionIdByDescription(notExitsDescription, PermissionCategoryEnum.MENU.getPermissionCategoryId())
                                                              .stream()
                                                              .map(permissionId -> new ScenePermissionMap(null, permissionId, sceneId))
                                                              .toList();
        if (CollUtil.isEmpty(scenePermissionMapList)) {
            return 0;
        }
        //不存在的则新增场景权限映射关系
        scenePermissionMapMapper.batchInsert(scenePermissionMapList);
        //给管理员角色添加权限
        List<RolePermissionMap> rolePermissionMapList = new ArrayList<>(scenePermissionMapList.size());
        for (ScenePermissionMap scenePermissionMap : scenePermissionMapList) {
            RolePermissionMap rolePermissionMap = new RolePermissionMap();
            rolePermissionMap.setPermissionId(scenePermissionMap.getPermissionId());
            rolePermissionMap.setRoleId(-1);
            rolePermissionMap.setPermissionCategoryId(PermissionCategoryEnum.MENU.getPermissionCategoryId());
            rolePermissionMapList.add(rolePermissionMap);
        }
        rolePermissionMapService.batchCreateRolePermissionMap(rolePermissionMapList);
        return scenePermissionMapList.size();
    }

    @Override
    public int deleteBySceneIdAndPermissionId(Integer sceneId, Integer permissionId) {
        return scenePermissionMapMapper.delete(Wrappers.lambdaQuery(ScenePermissionMap.class)
                                                       .eq(ScenePermissionMap::getSceneId, sceneId)
                                                       .eq(ScenePermissionMap::getPermissionId, permissionId));
    }

    public List<Integer> findPermissionIdByDescription(List<String> description, Integer categoryId) {
        if (CollUtil.isEmpty(description)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Permission> wq = Wrappers.lambdaQuery(Permission.class)
                                                    .select(Permission::getPermissionId)
                                                    .eq(Permission::getCategory, categoryId)
                                                    .in(Permission::getDescription, description);
        List<Permission> permissions = permissionMapper.selectList(wq);
        return permissions.stream()
                          .map(Permission::getPermissionId)
                          .toList();
    }
}
