package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.RoleGraphicPageMapDTO;
import com.siteweb.admin.entity.RoleGraphicPageMap;
import com.siteweb.admin.mapper.RoleGraphicPageMapMapper;
import com.siteweb.admin.service.RoleGraphicPageMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class RoleGraphicPageMapServiceImpl implements RoleGraphicPageMapService {
    @Autowired
    RoleGraphicPageMapMapper roleGraphicPageMapMapper;

    @Override
    public RoleGraphicPageMap findByRoleId(Integer roleId) {
        return roleGraphicPageMapMapper.selectOne(Wrappers.lambdaQuery(RoleGraphicPageMap.class)
                .eq(RoleGraphicPageMap::getRoleId, roleId));
    }

    @Override
    public RoleGraphicPageMap create(RoleGraphicPageMap roleGraphicPageMap) {
        roleGraphicPageMapMapper.insert(roleGraphicPageMap);
        return roleGraphicPageMap;
    }

    @Override
    public RoleGraphicPageMap createOrUpdate(RoleGraphicPageMap roleGraphicPageMap) {
        if (!this.existsById(roleGraphicPageMap.getRoleId())) {
            return this.create(roleGraphicPageMap);
        }
        roleGraphicPageMapMapper.updateById(roleGraphicPageMap);
        return roleGraphicPageMap;
    }

    @Override
    public int deleteById(Integer id) {
        return roleGraphicPageMapMapper.deleteById(id);
    }

    @Override
    public List<RoleGraphicPageMapDTO> findAll() {
        return roleGraphicPageMapMapper.findAll();
    }

    @Override
    public RoleGraphicPageMapDTO findById(Integer id) {
        return roleGraphicPageMapMapper.findById(id);
    }

    @Override
    public List<RoleGraphicPageMap> findByUserId(Integer id) {
        return roleGraphicPageMapMapper.findByUserId(id);
    }

    private boolean existsById(Integer id){
        if (Objects.isNull(id)) {
            return false;
        }
        return roleGraphicPageMapMapper.exists(Wrappers.lambdaQuery(RoleGraphicPageMap.class)
                                                       .eq(RoleGraphicPageMap::getRoleId, id));
    }
}
