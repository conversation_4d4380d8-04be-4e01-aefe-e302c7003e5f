package com.siteweb.admin.service;

import com.siteweb.admin.dto.SecurityReportParamDto;
import com.siteweb.admin.entity.AuditReport;
import com.siteweb.admin.entity.SecurityReport;

import java.util.List;

public interface SecurityReportService {
    /**
     * 是否启用安全日志报表
     * @return {@link Boolean}
     */
    Boolean isEnableSecurity();

    /**
     * 保存安全日志记录
     */
    void saveSecurityReport(SecurityReport securityReport);

    /**
     * 查找最大安全日志报表数据
     * @return {@link Integer}
     */
    Integer findSecurityMaxCount();

    /**
     * 删除最大记录数之后的记录
     * @param securityMaxCount 最大记录数
     */
    void removeMaxCountRecord(Integer securityMaxCount);

    /**
     * 查询安全日志记录报表
     * @param securityReportParamDto 安全日志记录报表条件
     * @return {@link List}<{@link AuditReport}>
     */
    List<SecurityReport> findSecurityReport(SecurityReportParamDto securityReportParamDto);
}
