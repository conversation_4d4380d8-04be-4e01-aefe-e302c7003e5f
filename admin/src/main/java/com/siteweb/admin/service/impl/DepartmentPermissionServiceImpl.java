package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.DepartmentTreeDTO;
import com.siteweb.admin.entity.Department;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.mapper.DepartmentMapper;
import com.siteweb.admin.mapper.EmployeeMapper;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.admin.service.DepartmentService;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.service.UserRoleService;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DepartmentPermissionServiceImpl implements DepartmentPermissionService {
    @Autowired
    DepartmentMapper departmentMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    DepartmentService departmentService;
    @Autowired
    RolePermissionMapService rolePermissionMapService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    UserRoleService userRoleService;
    @Override
    public List<DepartmentTreeDTO> findDepartmentTreeByUserId(Integer userId) {
        List<Department> departmentList = departmentMapper.findAllDepartments();
        List<DepartmentTreeDTO> departmentTreeDTOS = BeanUtil.copyToList(departmentList, DepartmentTreeDTO.class);
        //拥有权限的部门
        Set<Integer> departmentPermissionSet = this.findDepartmentPermissionByUserId(userId);
        this.setPermissionFlag(departmentTreeDTOS, departmentPermissionSet);
        return departmentTreeDTOS;
    }

    /**
     * 递归设置部门权限标识，判断是否有该部门的权限
     * @param departmentList 部门列表
     * @param departmentPermissionSet 拥有的部门权限
     */
    private void setPermissionFlag(List<DepartmentTreeDTO> departmentList, Set<Integer> departmentPermissionSet) {
        if (CollUtil.isEmpty(departmentList)) {
            return;
        }
        for (DepartmentTreeDTO departmentNode : departmentList) {
            if (departmentPermissionSet.contains(departmentNode.getDepartmentId())) {
                departmentNode.setPermissionFlag(true);
            }
            this.setPermissionFlag(departmentNode.getChildren(), departmentPermissionSet);
        }
    }

    @Override
    public boolean enableDepartmentPermission(){
        SystemConfig systemConfigKey = systemConfigService.findBySystemConfigKey(
                SystemConfigEnum.DEPARTMENT_PERMISSION_ENABLE.getSystemConfigKey());
        if (ObjectUtil.isNull(systemConfigKey)) {
            return false;
        }
        return Boolean.parseBoolean(systemConfigKey.getSystemConfigValue());
    }
    @Override
    public Set<Integer> findDepartmentPermissionByUserId(Integer userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptySet();
        }
        //开启部门权限且配置了部门权限的：部门树上有权限的部门可以点击查看人员，没有权限节点置灰；
        //开启部门权限但未配置部门权限的：部门树上所在部门及以下部门可点击，其他节点置灰；
        //开启部门权限
        if (this.enableDepartmentPermission()) {
            if (userRoleService.hasSystemAdministrator(userId)) {
                return findAllDepartmentIds();
            }
            //配置了部门权限
            Set<Integer> departmentPermissionSet = rolePermissionMapService.findRolePermissionsByUserId(userId, PermissionCategoryEnum.DEPARTMENT.getPermissionCategoryId());
            if (CollUtil.isNotEmpty(departmentPermissionSet)) {
                return departmentPermissionSet;
            }
            //未配置部门权限拥有当前部门与其子部门的所有权限
            Employee employee = employeeMapper.selectById(userId);
            List<Department> departmentList = departmentService.findDepartmentById(employee.getDepartmentId());
            this.findChildrenDepartmentId(departmentList, departmentPermissionSet);
            return departmentPermissionSet;
        }
        return findAllDepartmentIds();
    }

    /**
     * 获取数据库中所有的部门id
     * @return {@link Set}<{@link Integer}> 所有的部门ids
     */
    private Set<Integer> findAllDepartmentIds() {
        return departmentMapper.selectList(Wrappers.lambdaQuery(Department.class).select(Department::getDepartmentId))
                               .stream().map(Department::getDepartmentId).collect(Collectors.toSet());
    }

    private void findChildrenDepartmentId(List<Department> departmentList, Set<Integer> departmentIdSet) {
        if (CollUtil.isEmpty(departmentList)) {
            return;
        }
        for (Department department : departmentList) {
            departmentIdSet.add(department.getDepartmentId());
            this.findChildrenDepartmentId(department.getChildren(), departmentIdSet);
        }
    }

    @Override
    public Set<Integer> findEmployeeIdsByUserId(Integer userId) {
        Set<Integer> departmentIds = findDepartmentPermissionByUserId(userId);
        if (CollUtil.isEmpty(departmentIds)) {
            return Collections.emptySet();
        }
        List<Employee> employees = employeeMapper.selectList(Wrappers.lambdaQuery(Employee.class)
                                                                     .select(Employee::getEmployeeId)
                                                                     .in(Employee::getDepartmentId, departmentIds));
        return employees.stream()
                        .map(Employee::getEmployeeId)
                        .collect(Collectors.toSet());
    }
}
