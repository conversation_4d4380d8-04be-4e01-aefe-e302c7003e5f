package com.siteweb.admin.service;

import com.siteweb.admin.dto.SpecialtyDTO;
import com.siteweb.admin.dto.SpecialtyGroupMapDTO;
import com.siteweb.admin.entity.SpecialtyGroupMap;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupMapService
 * @createTime 2022-01-18 16:46:17
 */
public interface SpecialtyGroupMapService {

    List<SpecialtyGroupMap> findBySpecialtyGroupId(Integer specialtyGroupId);

    int saveSpecialtyGroupMaps(List<SpecialtyGroupMap> specialtyGroupMaps);

    int deleteBySpecialtyGroupId(Integer specialtyGroupId);

    int deleteByIds(List<Integer> specialtyGroupMapIds);

    List<SpecialtyDTO> getSpecialty();
}
