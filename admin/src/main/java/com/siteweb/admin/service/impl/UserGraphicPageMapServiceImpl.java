package com.siteweb.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.UserGraphicPageMapDTO;
import com.siteweb.admin.entity.UserGraphicPageMap;
import com.siteweb.admin.mapper.UserGraphicPageMapMapper;
import com.siteweb.admin.service.UserGraphicPageMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class UserGraphicPageMapServiceImpl implements UserGraphicPageMapService {
    @Autowired
    UserGraphicPageMapMapper userGraphicPageMapMapper;

    @Override
    public UserGraphicPageMap findByUserId(Integer userId){
        return userGraphicPageMapMapper.selectOne(Wrappers.lambdaQuery(UserGraphicPageMap.class)
                                                          .eq(UserGraphicPageMap::getUserId, userId));
    }

    @Override
    public UserGraphicPageMap create(UserGraphicPageMap userGraphicPageMap) {
        userGraphicPageMapMapper.insert(userGraphicPageMap);
        return userGraphicPageMap;
    }

    @Override
    public UserGraphicPageMap createOrUpdate(UserGraphicPageMap userGraphicPageMap) {
        if (!this.existsById(userGraphicPageMap.getUserGraphicPageMapId())) {
            return this.create(userGraphicPageMap);
        }
        userGraphicPageMapMapper.updateById(userGraphicPageMap);
        return userGraphicPageMap;
    }

    @Override
    public int deleteById(Integer id) {
        return userGraphicPageMapMapper.deleteById(id);
    }

    @Override
    public List<UserGraphicPageMapDTO> findAll() {
        return userGraphicPageMapMapper.findAll();
    }

    @Override
    public UserGraphicPageMapDTO findById(Integer id) {
        return userGraphicPageMapMapper.findById(id);
    }

    private boolean existsById(Integer id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return userGraphicPageMapMapper.exists(Wrappers.lambdaQuery(UserGraphicPageMap.class)
                                                       .eq(UserGraphicPageMap::getUserGraphicPageMapId, id));
    }
}
