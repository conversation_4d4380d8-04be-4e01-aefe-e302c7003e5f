package com.siteweb.admin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.dto.SecurityReportParamDto;
import com.siteweb.admin.entity.SecurityReport;
import com.siteweb.admin.mapper.SecurityReportMapper;
import com.siteweb.admin.service.SecurityReportService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SecurityReportServiceImpl implements SecurityReportService {
    private static final Integer DEFAULT_MAX_COUNT = 10000;
    @Autowired
    SecurityReportMapper securityReportMapper;
    @Autowired
    SystemConfigService systemConfigService;
    @Override
    public Boolean isEnableSecurity() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.securityreport.enable");
        if (ObjectUtil.isNull(systemConfig)) {
            return Boolean.FALSE;
        }
        String systemConfigValue = systemConfig.getSystemConfigValue();
        return Boolean.parseBoolean(systemConfigValue);
    }

    @Override
    public void saveSecurityReport(SecurityReport securityReport) {
        securityReportMapper.insert(securityReport);
    }

    @Override
    public Integer findSecurityMaxCount(){
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.securityreport.maxcount");
        if (ObjectUtil.isNull(systemConfig)) {
            return DEFAULT_MAX_COUNT;
        }
        try {
            return Integer.parseInt(systemConfig.getSystemConfigValue());
        } catch (NumberFormatException e) {
            log.error("系统配置中审计报表最大记录数有误。");
            return DEFAULT_MAX_COUNT;
        }
    }

    @Override
    public void removeMaxCountRecord(Integer securityMaxCount) {
        securityReportMapper.removeMaxCountRecord(securityMaxCount);
    }

    @Override
    public List<SecurityReport> findSecurityReport(SecurityReportParamDto securityReportParamDto) {
        securityReportParamDto.setMaxCount(this.findSecurityMaxCount());
        return securityReportMapper.findSecurityReport(securityReportParamDto);
    }
}
