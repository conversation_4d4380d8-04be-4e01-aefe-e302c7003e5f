package com.siteweb.admin.service;

import com.siteweb.admin.entity.MenuProfile;
import com.siteweb.admin.vo.MenuProfileVO;

import java.util.List;

public interface MenuProfileService{

    List<MenuProfile> findMenuProfiles();

    int createMenuProfile(MenuProfileVO menuProfileVO);

    int deleteById(Integer menuProfileId);

    int updateMenuProfile(MenuProfile menuProfile);

    MenuProfile findById(Integer menuProfileId);

    MenuProfile getCurrentMenuProfile();

    void updateCheckedStatus(Integer menuProfileId);

    /**
     * 设置菜单方案
     * @param menuProfileId 方案id
     * @return {@link Boolean}
     */
    Boolean setCurrentMenuProfile(Integer menuProfileId);

    List<MenuProfile> findMenuProfileBySceneId(Integer sceneId);
}

