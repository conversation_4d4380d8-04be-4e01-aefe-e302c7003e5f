package com.siteweb.admin.service;


import com.siteweb.admin.entity.Scene;

import java.util.List;


public interface SceneService {

    /**
     * 获取当前场景
     * @return
     */
    Scene currentScene();

    /**
     * 获取所有场景
     * @return
     */
    List<Scene> findScenes();

    /**
     * 修改场景
     * @param scene
     */
    int updateScene(Scene scene);

    /**
     * 根据id查询
     * @param sceneId
     */
    Scene findSceneById(Integer sceneId);

    /**
     * 切换场景
     * @param sceneId
     */
    void checkedScene(Integer sceneId,Integer menuProfileId);
}

