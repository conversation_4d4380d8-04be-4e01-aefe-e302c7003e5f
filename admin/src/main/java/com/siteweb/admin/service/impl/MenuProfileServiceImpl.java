package com.siteweb.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.MenuItemStructureMap;
import com.siteweb.admin.entity.MenuProfile;
import com.siteweb.admin.entity.MenuStructure;
import com.siteweb.admin.entity.SceneMenuProfileMap;
import com.siteweb.admin.mapper.MenuProfileMapper;
import com.siteweb.admin.service.*;
import com.siteweb.admin.vo.MenuProfileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MenuProfileServiceImpl implements MenuProfileService {

    @Autowired
    MenuProfileMapper menuProfileMapper;
    @Autowired
    @Lazy
    MenuStructureService menuStructureService;
    @Autowired
    MenuItemStructureMapService menuItemStructureMapService;
    @Autowired
    @Lazy
    PermissionService permissionService;
    @Autowired
    SceneMenuProfileMapService sceneMenuProfileMapService;
    @Autowired
    @Lazy
    SceneService sceneService;

    @Override
    public List<MenuProfile> findMenuProfiles() {
        return menuProfileMapper.selectList(null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int createMenuProfile(MenuProfileVO menuProfileVO) {
        MenuProfile menuProfile = BeanUtil.toBean(menuProfileVO, MenuProfile.class);
        menuProfileMapper.insert(menuProfile);
        //添加方案场景映射关系
        sceneMenuProfileMapService.createSceneMenuProfileMap(menuProfileVO.getScene(), menuProfile.getMenuProfileId());
        //添加初始化菜单
        createInitMenuItem(menuProfile.getMenuProfileId());
        return menuProfile.getMenuProfileId();
    }


    /**
     * 添加初始化菜单
     * @param menuProfileId
     */
    @Transactional(rollbackFor = Exception.class)
    public void createInitMenuItem(Integer menuProfileId) {
        Map<Integer, Integer> oldNewStructureIdMap = new HashMap<>();
        List<MenuStructure> menuStructureList = menuStructureService.findByIds(List.of(1, 2, 3));
        for (MenuStructure menuStructure : menuStructureList) {
            Integer oldMenuStructureId = menuStructure.getMenuStructureId();
            menuStructure.setMenuStructureId(null);
            menuStructure.setMenuProfileId(menuProfileId);
            menuStructureService.createMenuStructure(menuStructure);
            oldNewStructureIdMap.put(oldMenuStructureId, menuStructure.getMenuStructureId());
        }
        List<MenuItemStructureMap> menuItemStructureMapList = menuItemStructureMapService.findByMenuProfileIdAndStructureId(1,List.of(1, 2, 3));
        for (MenuItemStructureMap menuItemStructureMap : menuItemStructureMapList) {
            menuItemStructureMap.setMenuItemStructureMapId(null);
            menuItemStructureMap.setMenuProfileId(menuProfileId);
            menuItemStructureMap.setMenuStructureId(oldNewStructureIdMap.get(menuItemStructureMap.getMenuStructureId()));
            menuItemStructureMap.setSortIndex(menuItemStructureMap.getSortIndex());
            menuItemStructureMapService.createMenuItemStructureMap(menuItemStructureMap);
        }
        //同步菜单方案的权限
        permissionService.syncMenuPermissions(menuProfileId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteById(Integer menuProfileId) {
        //删除菜单目录
        menuStructureService.deleteByMenuProfileId(menuProfileId);
        menuItemStructureMapService.deleteByMenuProfileId(menuProfileId);
        //删映射关系
        sceneMenuProfileMapService.deleteByMenuProfileId(menuProfileId);
        //删除菜单方案的权限
        permissionService.deleteMenuPermission(menuProfileId);
       return menuProfileMapper.deleteById(menuProfileId);
    }

    @Override
    public int updateMenuProfile(MenuProfile menuProfile) {
        return menuProfileMapper.updateById(menuProfile);
    }

    @Override
    public MenuProfile findById(Integer menuProfileId) {
        return menuProfileMapper.selectById(menuProfileId);
    }

    @Override
    public MenuProfile getCurrentMenuProfile() {
        return menuProfileMapper.selectOne(Wrappers.lambdaQuery(MenuProfile.class).eq(MenuProfile::getChecked, true));
    }

    @Override
    public void updateCheckedStatus(Integer menuProfileId) {
        menuProfileMapper.updateCheckedStatus(menuProfileId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean setCurrentMenuProfile(Integer menuProfileId) {
        MenuProfile menuProfile = this.findById(menuProfileId);
        if (Boolean.TRUE.equals(menuProfile.getChecked())) {
            return true;
        }
        //切换场景
        SceneMenuProfileMap menuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
        sceneService.checkedScene(menuProfileMap.getSceneId(), menuProfileId);
        //切换菜单方案
        this.updateCheckedFalse();
        menuProfile.setChecked(true);
        return this.updateMenuProfile(menuProfile) > 0;
    }

    @Override
    public List<MenuProfile> findMenuProfileBySceneId(Integer sceneId) {
        return menuProfileMapper.findMenuProfileBySceneId(sceneId);
    }

    private void updateCheckedFalse(){
        menuProfileMapper.update(null, Wrappers.lambdaUpdate(MenuProfile.class)
                                               .set(MenuProfile::getChecked, false));
    }
}
