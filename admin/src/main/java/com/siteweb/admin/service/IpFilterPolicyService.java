package com.siteweb.admin.service;

import com.siteweb.admin.entity.IpFilterPolicy;

import java.util.List;

public interface IpFilterPolicyService {

    List<IpFilterPolicy> findIpFilterPolicys();

    int createIpFilterPolicy(IpFilterPolicy ipFilterPolicy);

    int updateIpFilterPolicy(IpFilterPolicy ipFilterPolicy);

    IpFilterPolicy findIpFilterPolicyById(Integer ipFilterPolicyId);

    int deleteIpFilterPolicyById(Integer ipFilterPolicyId);

    /**
     * 判断ip是否符合访问时间段
     * @param ip
     */
    boolean isIpTimeSpan(String ip);
}
