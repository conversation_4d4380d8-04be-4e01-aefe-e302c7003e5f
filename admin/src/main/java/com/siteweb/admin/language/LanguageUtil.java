package com.siteweb.admin.language;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.dto.MenuDataItemDTO;
import com.siteweb.admin.dto.MenuLanguageBase;
import com.siteweb.admin.dto.MenuTreeDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/03/17
 */
public class LanguageUtil {
    private LanguageUtil(){}
    /**
     * 语言请求头
     */
    public static final String LANGUAGE_HEADER = "lan";

    /**
     * 英语常量
     */
    public static final String EN_US = "en-US";
    /**
     * 中文常量
     */
    public static final String ZH_CN = "zh-CN";

    /**
     * 根据请求头切换菜单的title与alias
     * @param menuTreeDTO 菜单
     * @param language 请求头
     */
    public static void menuSwitch(MenuTreeDTO menuTreeDTO, String language){
        if (CharSequenceUtil.isBlank(language)) {
            return;
        }
        MenuDataItemDTO menu = menuTreeDTO.getData().getMenu();
        if (EN_US.equals(language)) {
            menu.setTitle(menu.getAlias());
            menuChildSwitch(menuTreeDTO.getChildren(),language);
        }
    }

    public static void menuChildSwitch(List<MenuTreeDTO> child, String language) {
        if (CollUtil.isEmpty(child)) {
            return;
        }
        for (MenuTreeDTO menuTreeDTO : child) {
            MenuDataItemDTO menu = menuTreeDTO.getData().getMenu();
            if (EN_US.equals(language)) {
                menu.setTitle(menu.getAlias());
                menuChildSwitch(menuTreeDTO.getChildren(), language);
            }
        }
    }

    /**
     * 根据请求头切换title与alias
     * @param menuLanguageBaseList 菜单项
     * @param language 语言
     */
    public static <T extends MenuLanguageBase<T>> void menuLanguageBaseSwitch(List<T> menuLanguageBaseList, String language){
        if (CharSequenceUtil.isBlank(language) || ZH_CN.equals(language) || CollUtil.isEmpty(menuLanguageBaseList)) {
            return;
        }
        for (MenuLanguageBase<T> item : menuLanguageBaseList) {
            item.setTitle(item.getAlias());
            menuLanguageBaseSwitch(item.getChildren(), language);
        }
    }
}
