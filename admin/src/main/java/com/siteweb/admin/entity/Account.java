package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("tbl_account")
public class Account {

    @TableId(value = "userId", type = IdType.INPUT)
    private Integer userId;

    private String userName;

    private String logonId;

    private String password;

    private Boolean enable;

    private Integer maxError;

    private Boolean locked;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date validTime;

    private String description;

    @TableField("isRemote")
    private Boolean remote;

    private Integer centerId;

    private Date passwordValidTime;

    private String avatar;

    private String themeName;

    private Boolean needResetPwd;

}
