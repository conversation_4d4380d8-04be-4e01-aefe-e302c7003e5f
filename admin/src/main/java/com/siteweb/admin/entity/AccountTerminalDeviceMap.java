package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("accountterminaldevicemap")
public class AccountTerminalDeviceMap {

    @TableId(value = "UserId", type = IdType.INPUT)
    private Integer userId;

    private String terminalDeviceId;

    private Date updateTime;

    private Integer operatorId;
}
