package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("menupermissiongroupmap")
@Data
public class MenuPermissionGroupMap {
    public MenuPermissionGroupMap() {
    }

    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer menuPermissionGroupMapId;
    /**
     * 权限组id
     */
    private Integer menuPermissionGroupId;
    /**
     * 权限id
     */
    private Integer permissionId;
}
