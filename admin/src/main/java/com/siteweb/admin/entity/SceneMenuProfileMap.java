package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("scenemenuprofilemap")
public class SceneMenuProfileMap {

    @TableId(value = "SceneMenuProfileMapId", type = IdType.AUTO)
    private Integer sceneMenuProfileMapId;

    private Integer sceneId;

    private Integer menuProfileId;

}
