package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LoginLog
 * @createTime 2022-05-11 09:58:24
 */
@Data
@NoArgsConstructor
@TableName("loginlog")
public class LoginLog {

    /**
     * 主键ID
     */
    @TableId(value = "LoginLogId", type = IdType.AUTO)
    private Integer loginLogId;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 操作时间
     */
    private Date operatingTime;
    /**
     * 操作类型
     */
    private String operatingType;
    /**
     * 客户端类型
     */
    private String clientType;
    /**
     * 客户端IP
     */
    private String clientIp;
}
