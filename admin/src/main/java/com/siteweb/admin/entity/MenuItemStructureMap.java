package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * MenuItemStructureMap info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
@TableName("MenuItemStructureMap")
public class MenuItemStructureMap {

	/**
	 * 
	 */
	 @TableId(value="menuItemStructureMapId", type = IdType.AUTO)
	private Integer menuItemStructureMapId;
	/**
	 * 
	 */
	private Integer menuProfileId;
	/**
	 * 
	 */
	private Integer menuStructureId;
	/**
	 * 
	 */
	private Integer menuItemId;
	/**
	 * 
	 */
	private Integer sortIndex;
}
