package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("RoleGraphicPageMap")
public class RoleGraphicPageMap {
    /**
     * 主键id,角色id
     */
    @TableId(value = "RoleId", type = IdType.INPUT)
    private Integer roleId;
    /**
     * 组态id
     */
    private Integer graphicPageId;
    /**
     * 配置
     */
    private String config;
}
