package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 审计报表实体
 * <AUTHOR>
 * @date 2022/07/18
 */
@Data
@NoArgsConstructor
@TableName("AuditReport")
public class AuditReport {
    public AuditReport(String operationAccount, Integer level, String clientIp, String details, Date createTime) {
        this.operationAccount = operationAccount;
        this.level = level;
        this.clientIp = clientIp;
        this.details = details;
        this.createTime = createTime;
    }

    public AuditReport(Integer level, String details) {
        this.level = level;
        this.details = details;
    }

    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer auditReportId;

    /**
     * 操作账户
     */
    private String operationAccount;

    /**
     * 审计记录级别
     */
    private Integer level;
    /**
     * 事件类型
     */
    private String type;
    /**
     * 审计记录级别名称
     */
    @TableField(exist = false)
    private String levelName;
    /**
     * 操作客户端ip
     */
    private String clientIp;
    /**
     * 详情
     */
    private String details;
    /**
     * 事件结果
     */
    private String result;
    /**
     * 创建时间
     */
    private Date createTime;
}
