package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("userconfig")
public class UserConfig {
    public UserConfig(Integer userId, Integer configType, String configKey) {
        this.userId = userId;
        this.configType = configType;
        this.configKey = configKey;
    }

    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer userConfigId;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 配置类型  1:tts
     */
    private Integer configType;
    /**
     * 用户配置键
     */
    private String configKey;
    /**
     * 用户配置值
     */
    private String configValue;
}
