package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("menupermissiongroup")
public class MenuPermissionGroup {
    /**
     * 菜单权限分组id
     */
    @TableId(type = IdType.AUTO)
    private Integer menuPermissionGroupId;
    /**
     * 菜单权限分组名称
     */
    private String menuPermissionGroupName;
    /**
     * 描述
     */
    private String description;
}
