package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * department info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-04-01 12:00:10
 */
@Data
@NoArgsConstructor
@TableName("tbl_department")
public class Department {

    /**
     *
     */
    @TableId(value = "departmentId", type = IdType.INPUT)
    private Integer departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门等级
     */
    private String departmentLevel;

    /**
     * 部门职能
     */
    private String departmentFunction;

    /**
     * 父部门Id
     */
    private Integer parentDeprtId;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 修改时间
     */
    private Date lastUpdateDate;

    @TableField(exist = false)
    private List<Department> children;
}
