package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * MenuStructure info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
@TableName("MenuStructure")
public class MenuStructure {

    /**
     *菜单目录ID
     */
    @TableId(value = "menuStructureId", type = IdType.AUTO)
    private Integer menuStructureId;
    /**
     *菜单方案ID
     */
    private Integer menuProfileId;
    /**
     *父菜单目录ID
     */
    private Integer parentId;
    /**
     *菜单目录标题
     */
    private String title;
    /**
     *菜单目录图标
     */
    private String icon;
    /**
     *菜单目录是否默认选中
     */
    private Boolean selected;
    /**
     *菜单目录是否可展开
     */
    private Boolean expanded;
    /**
     *菜单目录是否隐藏显示
     */
    private Boolean hidden;
    /**
     *菜单目录排序
     */
    private Integer sortIndex;
    /**
     *是否系统内置
     */
    @TableField("isSystem")
    private Boolean isSystem;
    /**
     *备注
     */
    private String description;

    /**
     * 别名
     */
    private String alias;

    @TableField(exist = false)
    private List<MenuStructure> children;

    @TableField(exist = false)
    private List<MenuItemStructureMap> menuItemStructureMaps;
}
