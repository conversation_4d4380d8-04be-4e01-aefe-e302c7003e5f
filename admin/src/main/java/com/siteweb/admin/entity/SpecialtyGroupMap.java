package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupMap
 * @createTime 2022-01-18 16:29:45
 */
@Data
@NoArgsConstructor
@TableName("tbl_specialtygroupmap")
public class SpecialtyGroupMap {

  @TableId(value = "id", type = IdType.INPUT)
  private Integer id;

  private Integer specialtyGroupId;

  private Integer entryItemId;

  private String operation;
}
