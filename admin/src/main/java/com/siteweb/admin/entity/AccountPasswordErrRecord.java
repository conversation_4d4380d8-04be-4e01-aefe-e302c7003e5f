package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 账号密码错误记录
 */
@Data
@NoArgsConstructor
@TableName("accountpassworderrRecord")
public class AccountPasswordErrRecord {

    @TableId(value = "UserId", type = IdType.INPUT)
    private Integer userId;

    /**
     * 密码错误次数
     */
    private Integer passwordErrCnt;

    /**
     * 冻结目标时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date freezeTime;
}
