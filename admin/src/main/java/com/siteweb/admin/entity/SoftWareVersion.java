package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 版本信息实体
 *
 * @Author: lzy
 * @Date: 2022/4/24 10:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("softwareversion")
public class SoftWareVersion {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 模块名称
     */
    private String moduleName;
    /**
     * 版本号
     */
    private String version;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 版本特性
     */
    private String feature;
}
