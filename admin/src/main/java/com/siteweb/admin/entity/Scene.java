package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/17 10:05
 * @description:
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("Scene")
public class Scene {

    @TableId(value = "SceneId", type = IdType.AUTO)
    private Integer sceneId;

    private String sceneName;

    private Boolean checked;

    private String remark;
}
