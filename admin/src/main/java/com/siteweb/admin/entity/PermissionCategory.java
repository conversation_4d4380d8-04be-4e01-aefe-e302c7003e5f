package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("permissioncategory")
public class PermissionCategory {

    @TableId(value = "PermissionCategoryId", type = IdType.AUTO)
    private Integer permissionCategoryId;

    private String name;

    private String caption;

    private String description;
}
