package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("departmentcodemap")
public class DepartmentCodeMap {
    @TableId(value = "departmentId", type = IdType.INPUT)
    private Integer departmentId;

    private Integer parentDepartmentId;
    /**
     *  其他应用部门唯一标识
     */
    private String code;

    /**
     * 其他应用父部门唯一标识
     */
    private String parentCode;

    @TableField(exist = false)
    private String name;

    @TableField(exist = false)
    private String parentName;
}
