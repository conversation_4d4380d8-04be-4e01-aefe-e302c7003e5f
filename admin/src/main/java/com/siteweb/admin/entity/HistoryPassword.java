package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 历史密码表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-03-23 08:41:50
 */
@Data
@NoArgsConstructor
@TableName("historypassword")
public class HistoryPassword {


	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	/**
	 * 
	 */
	private Long id;

	/**
	 * 登录名
	 */
	private String logonId;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 更改时间
	 */
	private Date updateTime;
}
