package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@TableName("tbl_userrole")
public class UserRole {

    @TableId(value = "roleId", type = IdType.INPUT)
    private Integer roleId;

    private String roleName;

    private String description;

    @TableField(exist = false)
    List<RolePermissionMap> rolePermissionMapList;
}
