package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 安全记录报表实体
 *
 * <AUTHOR>
 * @date 2022/07/18
 */
@Data
@NoArgsConstructor
@TableName("SecurityReport")
public class SecurityReport {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer securityReportId;
    /**
     * 操作账户
     */
    private String operationAccount;
    /**
     * 类别
     */
    private Integer type;
    /**
     * 类别名称
     */
    @TableField(exist = false)
    private String typeName;
    /**
     * 操作客户端ip
     */
    private String clientIp;
    /**
     * 详情
     */
    private String details;
    /**
     * 创建时间
     */
    private Date createTime;
}
