package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description Region
 * @createTime 2022-01-15 15:58:59
 */
@Data
@NoArgsConstructor
@TableName("Region")
public class Region {

  @TableId(value = "RegionId", type = IdType.AUTO)
  private Integer regionId;

  private String regionName;

  private String description;
}
