package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@TableName("ipfilterpolicy")
public class IpFilterPolicy {

    @TableId(value="IpFilterPolicyId", type = IdType.AUTO)
    private Integer ipFilterPolicyId;

    /**
     * ip地址设置 单个IP:******* IP段：*******-*******
     */
    private String ipAddrSet;

    /**
     * 星期逗号分隔
     */
    @JsonIgnore
    private String weekSpanChar;

    /**
     * 备注
     */
    private String description;


    @TableField(exist = false)
    private List<Integer> weekSpanList;

}
