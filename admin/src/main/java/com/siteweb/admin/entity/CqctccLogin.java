package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("cqctcc_login")
public class CqctccLogin {

    @TableId(value = "logonId", type = IdType.INPUT)
    private String logonId;

    private Boolean enable;

}
