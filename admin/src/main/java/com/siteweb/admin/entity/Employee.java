package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("tbl_employee")
public class Employee {

    @TableId(value = "EmployeeId", type = IdType.INPUT)
    private Integer employeeId;

    private Integer departmentId;

    private String employeeName;

    private Integer employeeType;

    private Integer employeeTitle;

    private String jobNumber;

    private Integer gender;

    private String mobile;

    private String phone;

    private String email;

    private String address;

    private String postAddress;

    private Boolean enable;

    private String description;

    @TableField("IsAddTempUser")
    private Boolean addTempUser;

    private Integer userValidTime;

    @TableField(exist = false)
    private String alias;
}
