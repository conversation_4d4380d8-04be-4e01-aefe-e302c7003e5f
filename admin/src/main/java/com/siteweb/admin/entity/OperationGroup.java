package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("tbl_operationgroup")
@Data
public class OperationGroup {

    /**
     * 操作权限组主键id
     */
    @TableId(type = IdType.INPUT)
    private Integer groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 描述
     */
    private String description;
}
