package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("UserGraphicPageMap")
public class UserGraphicPageMap {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer userGraphicPageMapId;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 组态id
     */
    private Integer graphicPageId;
    /**
     * 配置
     */
    private String config;
}
