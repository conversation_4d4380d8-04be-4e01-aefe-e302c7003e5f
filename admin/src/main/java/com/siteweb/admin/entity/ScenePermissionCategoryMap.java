package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/19 9:55
 * @description:
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("scenepermissioncategorymap")
public class ScenePermissionCategoryMap {

    @TableId(type = IdType.AUTO)
    private Integer scenePermissionCategoryMapId;

    private Integer permissionCategoryId;

    private Integer sceneId;
}
