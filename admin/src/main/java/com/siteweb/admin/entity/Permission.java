package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("permission")
public class Permission {

    public Permission(Department department) {
        this.permissionId = department.getDepartmentId();
        this.name = department.getDepartmentName();
        this.category = PermissionCategoryEnum.DEPARTMENT.getPermissionCategoryId();
        this.caption = PermissionCategoryEnum.DEPARTMENT.getPermissionCategoryName();
    }

    public Permission(Region region) {
        this.permissionId = region.getRegionId();
        this.name = region.getRegionName();
        this.category = PermissionCategoryEnum.REGION.getPermissionCategoryId();
        this.caption = PermissionCategoryEnum.REGION.getPermissionCategoryName();
    }

    @TableId(value = "PermissionId", type = IdType.AUTO)
    private Integer permissionId;

    private String name;

    private Integer category;

    private String caption;

    private String description;

    private Date updateTime;
}
