package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @Description SpecialtyGroup
 * @createTime 2022-01-15 15:58:39
 */
@Data
@NoArgsConstructor
@TableName("tbl_specialtygroup")
public class SpecialtyGroup {

    @TableId(value = "specialtyGroupId", type = IdType.INPUT)
    private Integer specialtyGroupId;

    private String specialtyGroupName;

    private String description;
}
