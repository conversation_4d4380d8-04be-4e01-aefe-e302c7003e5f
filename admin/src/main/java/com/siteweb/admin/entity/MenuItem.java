package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * MenuItem info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
@TableName("MenuItem")
public class MenuItem {

    /**
     * 菜单项ID
     */
    @TableId(value = "menuItemId", type = IdType.AUTO)
    private Integer menuItemId;
    /**
     * 父菜单项ID
     */
    private Integer parentId;
    /**
     * 菜单路径
     */
    private String path;
    /**
     * 菜单标题
     */
    private String title;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 菜单是否默认选中
     */
    private Boolean selected;
    /**
     * 菜单是否可展开
     */
    private Boolean expanded;
    /**
     * 菜单匹配策略
     */
    private String pathMatch;
    /**
     * 菜单排序
     */
    private Integer layoutPosition;
    /**
     * 是否系统内置
     */
    @TableField("isSystemConfig")
    private Boolean isSystemConfig;
    /**
     * 是否外部URL链接
     */
    @TableField("isExternalWeb")
    private Boolean isExternalWeb;
    /**
     *
     */
    private Boolean menuHasNavigation;
    /**
     * 模块id
     */
    private Integer featureId;
    /**
     * 备注
     */
    private String description;

    /**
     * 标题别名
     */
    private String alias;
    /**
     * 排序字段
     */
    private Integer sortIndex;
    /**
     * 是否嵌入
     */
    @TableField("isEmbed")
    private Boolean isEmbed;

    @TableField(exist = false)
    private List<MenuItem> children;
}
