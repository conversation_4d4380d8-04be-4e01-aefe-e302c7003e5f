package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 片区
 * @Author: lzy
 * @Date: 2023/3/2 10:32
 */
@Data
@NoArgsConstructor
@TableName("tbl_area")
public class Area {
    @TableId(type = IdType.INPUT)
    private Integer areaId;
    private String areaName;
    private String description;
}
