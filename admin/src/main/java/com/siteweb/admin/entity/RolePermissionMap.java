package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Role, Permission map info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-03-26 13:08:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("rolepermissionmap")
public class RolePermissionMap {
    public RolePermissionMap(Permission permission) {
        this.permissionId = permission.getPermissionId();
        this.permissionCategoryId = permission.getCategory();
    }

    /**
     * 主键
     */
    @TableId(value = "rolepermissionmapid", type = IdType.AUTO)
    private Integer rolePermissionMapId;

    /**
     * 角色ID
     */
    private Integer roleId;

    /**
     * 权限分类ID
     */
    private Integer permissionCategoryId;

    /**
     * 权限ID
     */
    private Integer permissionId;

    @JsonIgnore
    @TableField(exist = false)
    private Permission permission;

    public String getPermissionCaption() {
        if (permission != null) {
            return permission.getCaption();
        }
        return null;
    }

}
