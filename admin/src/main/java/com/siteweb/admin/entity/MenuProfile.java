package com.siteweb.admin.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * MenuProfile info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
@TableName("MenuProfile")
public class MenuProfile {

	/**
	 * 菜单方案ID
	 */
	@TableId(value="menuProfileId", type = IdType.AUTO)
	private Integer menuProfileId;
	/**
	 * 菜单方案名称
	 */
	private String name;
	/**
	 * 是否启用
	 */
	private Boolean checked;
	/**
	 * 备注
	 */
	private String description;
}
