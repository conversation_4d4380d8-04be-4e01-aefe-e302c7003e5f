package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("tbl_userrolemap")
public class UserRoleMap {

    @TableId(value = "userId", type = IdType.INPUT)
    private Integer userId;

    private Integer roleId;
}