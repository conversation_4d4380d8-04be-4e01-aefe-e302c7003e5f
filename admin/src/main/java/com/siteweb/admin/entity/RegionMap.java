package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description RegionMap
 * @createTime 2022-01-18 15:09:09
 */
@Data
@NoArgsConstructor
@TableName("RegionMap")
public class RegionMap {

  @TableId(value = "RegionMapId", type = IdType.AUTO)
  private Integer regionMapId;

  private Integer resourceStructureId;

  private Integer equipmentId;

  private Integer regionId;
}
