package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("accountalias")
public class AccountAlias {

    @TableId(value = "AccountAliasId", type = IdType.AUTO)
    private Integer accountAliasId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 别名
     */
    private String alias;

    /**
     * 是否默认
     */
    private Boolean checked;
}
