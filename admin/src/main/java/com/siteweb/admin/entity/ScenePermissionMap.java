package com.siteweb.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/18 15:23
 * @description:
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("scenepermissionmap")
public class ScenePermissionMap {

    @TableId(type = IdType.AUTO)
    private Integer scenePermissionMapId;

    private Integer permissionId;

    private Integer sceneId;

}
