package com.siteweb.admin.vo;

import com.siteweb.admin.entity.Region;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description RegionVO
 * @createTime 2022-01-18 15:57:24
 */
@Data
@NoArgsConstructor
@ApiModel(value = "新增、保存区域对象", description = "新增、保存区域对象")
public class RegionVO {

    @ApiModelProperty(value = "区域ID", name = "regionId")
    private Integer regionId;

    @ApiModelProperty(value = "区域名称", name = "regionName")
    private String regionName;

    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    @ApiModelProperty(value = "角色id集合", name = "roleIds")
    private List<Integer> roleIds;

    public Region build() {
        Region region = new Region();
        BeanUtils.copyProperties(this, region);
        return region;
    }
}
