package com.siteweb.admin.vo;

import com.siteweb.admin.entity.MenuItem;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MenuItem info tableDTO
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
public class MenuItemVO {
    /**
     * 菜单项ID
     */
    private Integer menuItemId;

    /**
     * 父菜单项ID
     */
    private Integer parentId;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 菜单标题
     */
    private String title;
    /**
     * 菜单别名
     */
    private String alias;
    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单是否默认选中
     */
    private Boolean selected;

    /**
     * 菜单是否可展开
     */
    private Boolean expanded;

    /**
     * 路径匹配策略
     */
    private String pathMatch;

    /**
     * 菜单排序
     */
    private Integer layoutPosition;

    /**
     * 是否系统内置
     */
    private Boolean isSystemConfig;

    /**
     * 是否外部URL链接
     */
    private Boolean isExternalWeb;
    /**
     *
     */
    private Boolean menuHasNavigation;
    /**
     * 备注
     */
    private String description;
    /**
     * 菜单方案Id
     */
    private Integer menuProfileId;
    /**
     * 是否只读
     */
    private Boolean readOnly;
    /**
     * 模块id
     */
    private Integer featureId;
    /**
     * 是否嵌入
     */
    private Boolean isEmbed;
    /**
     * 排序字段
     */
    private Integer sortIndex;

    public MenuItem build() {
        MenuItem menuItem = new MenuItem();
        menuItem.setMenuItemId(this.menuItemId);
        menuItem.setParentId(this.parentId);
        menuItem.setPath(this.path);
        menuItem.setTitle(this.title);
        menuItem.setAlias(this.alias);
        menuItem.setIcon(this.icon);
        menuItem.setSelected(this.selected);
        menuItem.setExpanded(this.expanded);
        menuItem.setPathMatch(this.pathMatch);
        menuItem.setLayoutPosition(this.layoutPosition);
        menuItem.setIsSystemConfig(this.isSystemConfig);
        menuItem.setIsExternalWeb(this.isExternalWeb);
        menuItem.setMenuHasNavigation(this.menuHasNavigation);
        menuItem.setDescription(this.description);
        menuItem.setIsEmbed(this.isEmbed);
        return menuItem;
    }
}
