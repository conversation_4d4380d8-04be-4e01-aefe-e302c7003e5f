package com.siteweb.admin.vo;

import com.siteweb.admin.entity.MenuProfile;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MenuProfile info tableDTO
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
public class MenuProfileVO {
    /**
     * 菜单方案ID
     */
    private Integer menuProfileId;

    /**
     * 菜单方案名称
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean checked;

    /**
     * 备注
     */
    private String description;
    /**
     * 场景id
     */
    private Integer scene;
    public MenuProfile build() {
        MenuProfile menuProfile = new MenuProfile();
        menuProfile.setMenuProfileId(this.menuProfileId);
        menuProfile.setName(this.name);
        menuProfile.setChecked(this.checked);
        menuProfile.setDescription(this.description);
        return menuProfile;
    }
}
