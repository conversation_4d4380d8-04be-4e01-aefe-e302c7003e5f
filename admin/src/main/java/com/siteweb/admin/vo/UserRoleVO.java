package com.siteweb.admin.vo;

import com.siteweb.admin.entity.UserRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * @author: Habits
 * @time: 2022/4/9 13:29
 * @description:
 **/
@Data
@NoArgsConstructor
@ApiModel(value = "保存、修改角色对象", description = "保存、修改角色对象")
public class UserRoleVO {
    @ApiModelProperty(value = "菜单方案id", name = "menuProfileId")
    private Integer menuProfileId;
    @ApiModelProperty(value = "角色ID", name = "roleId")
    private Integer roleId;

    @ApiModelProperty(value = "角色名称", name = "roleName")
    private String roleName;

    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    public UserRole build() {
        UserRole userRole = new UserRole();
        BeanUtils.copyProperties(this, userRole);
        return userRole;
    }

}
