package com.siteweb.admin.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.siteweb.admin.entity.HistoryPassword;
import java.util.Date;

/**
 * 历史密码表VO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-03-23 08:41:50
 */
@Data
@NoArgsConstructor
@ApiModel(value="HistoryPassword实体",description="HistoryPassword实体")
public class HistoryPasswordVO {

	/**
	 * 
	 */
    @ApiModelProperty(value="",name="id")
    private Long id;

    /**
     * 登录名
     */
    @ApiModelProperty(value="登录名",name="logonId")
    private String logonId;

    /**
     * 密码
     */
    @ApiModelProperty(value="密码",name="password")
    private String password;

    /**
     * 更改时间
     */
    @ApiModelProperty(value="更改时间",name="updateTime")
    private Date updateTime;
	public HistoryPassword build() {
        HistoryPassword historyPassword = new HistoryPassword();
        historyPassword.setId (this.id);
        historyPassword.setLogonId (this.logonId);
        historyPassword.setPassword (this.password);
        historyPassword.setUpdateTime (this.updateTime);
        return historyPassword;
	}
}
