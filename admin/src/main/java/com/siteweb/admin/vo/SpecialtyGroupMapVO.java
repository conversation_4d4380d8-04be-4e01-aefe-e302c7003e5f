package com.siteweb.admin.vo;

import com.siteweb.admin.entity.SpecialtyGroupMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupMap
 * @createTime 2022-01-18 16:33:03
 */
@Data
@NoArgsConstructor
@ApiModel(value = "新增、保存专业权限Map对象", description = "新增、保存专业权限Map对象")
public class SpecialtyGroupMapVO {

  @ApiModelProperty(value = "主键ID", name = "id")
  private Integer id;

  @ApiModelProperty(value = "专业权限组ID", name = "specialtyGroupId", required = true)
  private Integer specialtyGroupId;

  @ApiModelProperty(value = "专业权限字典项ID", name = "entryItemId", required = true)
  private Integer entryItemId;

  @ApiModelProperty(value = "操作名", name = "operation")
  private String operation;

  public SpecialtyGroupMap build() {
    SpecialtyGroupMap specialtyGroupMap = new SpecialtyGroupMap();
    BeanUtils.copyProperties(this, specialtyGroupMap);
    return specialtyGroupMap;
  }
}
