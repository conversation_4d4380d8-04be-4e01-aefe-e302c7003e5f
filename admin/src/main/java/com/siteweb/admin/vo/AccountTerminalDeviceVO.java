package com.siteweb.admin.vo;

import com.siteweb.admin.entity.AccountTerminalDeviceMap;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


@Data
@NoArgsConstructor
public class AccountTerminalDeviceVO {

    private String logonId;

    private String terminalDeviceId;
    public AccountTerminalDeviceMap build() {
        AccountTerminalDeviceMap accountTerminalDeviceMap = new AccountTerminalDeviceMap();
        BeanUtils.copyProperties(this, accountTerminalDeviceMap, "logonId");
        return accountTerminalDeviceMap;
    }
}
