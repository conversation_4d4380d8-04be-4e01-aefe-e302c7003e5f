package com.siteweb.admin.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MenuStructure info tableDTO
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-20 17:02:39
 */
@Data
@NoArgsConstructor
public class MenuStructureVO {
    /**
     * 　菜单目录ID
     */
    private Integer menuStructureId;

    /**
     * 　菜单方案ID
     */
    private Integer menuProfileId;

    /**
     * 　父菜单目录ID
     */
    private Integer parentId;

    /**
     * 　菜单标题
     */
    private String title;
    /**
     * 别名
     */
    private String alias;

    /**
     * 　菜单图标
     */
    private String icon;

    /**
     * 　菜单目录是否可选中
     */
    private Boolean selected;

    /**
     * 　菜单目录是否可展开
     */
    private Boolean expanded;

    /**
     * 　菜单目录是否隐藏显示
     */
    private Boolean hidden;

    /**
     * 　菜单目录排序
     */
    private Integer sortIndex;

    /**
     * 　菜单目录是否系统内置
     */
    private Boolean system;
    /**
     * 　备注
     */
    private String description;
    /**
     * 菜单项id
     */
    private Integer menuItemId;
    /**
     * 菜单目录与菜单映射id
     */
    private Integer menuItemStructureMapId;
}
