package com.siteweb.admin.vo;

import com.siteweb.admin.entity.Account;
import com.siteweb.admin.entity.UserRoleMap;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description AccountVO
 * @createTime 2022-03-30 10:07:02
 */
@Data
@NoArgsConstructor
public class AccountVO {

    private Integer userId;

    private String userName;

    private String logonId;

    private String password;

    private boolean enable;

    private Integer maxError;

    private boolean locked;

    private Date validTime;

    private String description;

    private boolean remote;

    private Integer centerId;

    private Date passwordValidTime;

    private String avatar;

    private String themeName;

    private Boolean needResetPwd;

    private String roleIds;

    public Account build() {
        Account account = new Account();
        BeanUtils.copyProperties(this, account);
        return account;
    }

    public static UserRoleMap buildUserRoleMap(AccountVO accountVO) {
        UserRoleMap userRoleMap = new UserRoleMap();
        userRoleMap.setUserId(accountVO.getUserId());
        userRoleMap.setRoleId(Integer.parseInt(accountVO.getRoleIds()));
        return userRoleMap;
    }
}
