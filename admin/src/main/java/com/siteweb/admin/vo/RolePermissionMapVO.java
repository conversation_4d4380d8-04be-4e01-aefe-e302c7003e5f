package com.siteweb.admin.vo;

import com.siteweb.admin.entity.RolePermissionMap;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 * @email 
 * @date 2022-03-26 13:08:07
 */
@Data
@NoArgsConstructor
@ApiModel(value="RolePermissionMap实体",description="RolePermissionMap实体")
public class RolePermissionMapVO {

	/**
	 * 主键
	 */
    @ApiModelProperty(value="主键id",name="rolePermissionMapId")
    private Integer rolePermissionMapId;

    /**
     * 角色ID
     */
    @ApiModelProperty(value="角色ID",name="roleId")
    private Integer roleId;

    /**
     * 权限分类ID
     */
    @ApiModelProperty(value="权限分类ID",name="permissionCategoryId")
    private Integer permissionCategoryId;

    /**
     * 权限ID
     */
    @ApiModelProperty(value="权限ID",name="permissionId")
    private Integer permissionId;

	public RolePermissionMap build() {
        RolePermissionMap rolePermissionMap = new RolePermissionMap();
        rolePermissionMap.setRolePermissionMapId (this.rolePermissionMapId);
        rolePermissionMap.setRoleId(this.roleId);
        rolePermissionMap.setPermissionCategoryId(this.permissionCategoryId);
        rolePermissionMap.setPermissionId(this.permissionId);
        return rolePermissionMap;
	}
}
