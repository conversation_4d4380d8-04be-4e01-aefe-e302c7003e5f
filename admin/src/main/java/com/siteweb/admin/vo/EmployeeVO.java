package com.siteweb.admin.vo;

import com.siteweb.admin.entity.Employee;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description EmployeeVO
 * @createTime 2022-03-30 13:07:54
 */
@Data
@NoArgsConstructor
public class EmployeeVO {

    private Integer employeeId;

    private Integer departmentId;

    private String employeeName;

    private Integer employeeType;

    private Integer employeeTitle;

    private String jobNumber;

    private Integer gender;

    private String mobile;

    private String phone;

    private String email;

    private String address;

    private String postAddress;

    private boolean enable;

    private String description;

    private boolean addTempUser;

    private Integer userValidTime;

    public Employee build() {
        Employee employee = new Employee();
        BeanUtils.copyProperties(this, employee);
        return employee;
    }
}
