package com.siteweb.admin.vo;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.entity.RegionMap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description RegionMapVO
 * @createTime 2022-01-18 15:57:09
 */
@Data
@NoArgsConstructor
@ApiModel(value = "新增、保存区域Map对象", description = "新增、保存区域Map对象")
public class RegionMapCreteVO {
  @ApiModelProperty(value = "层级设备映射关系", name = "regionId", required = true)
  private List<ResourceStructureEquipmentMap> regionMapList;
  @ApiModelProperty(value = "区域ID", name = "regionId", required = true)
  private Integer regionId;

  @Data
  public static class ResourceStructureEquipmentMap {
    @ApiModelProperty(value = "资源组ID", name = "resourceStructureId", required = true)
    private Integer resourceStructureId;

    @ApiModelProperty(value = "设备ID", name = "equipmentId", required = true)
    private Integer equipmentId;
  }
  public List<RegionMap> buildRegionMapList() {
    if (CollUtil.isEmpty(this.regionMapList)) {
      return Collections.emptyList();
    }
    List<RegionMap> list = new ArrayList<>(this.regionMapList.size());
    for (ResourceStructureEquipmentMap resourceStructureEquipmentMap : this.regionMapList) {
      RegionMap regionMap = new RegionMap();
      regionMap.setRegionId(this.regionId);
      regionMap.setEquipmentId(resourceStructureEquipmentMap.getEquipmentId());
      regionMap.setResourceStructureId(resourceStructureEquipmentMap.getResourceStructureId());
      list.add(regionMap);
    }
    return list;
  }
}
