package com.siteweb.admin.vo;

import com.siteweb.admin.entity.Permission;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @Description PermissionVO
 * @createTime 2022-01-04 09:21:31
 */
@Data
@NoArgsConstructor
public class PermissionVO {

    private Integer permissionId;

    private String name;

    private Integer category;

    private String caption;

    private String description;

    private Date updateTime;

    public Permission build() {
        Permission permission = new Permission();
        BeanUtils.copyProperties(this, permission);
        return permission;
    }
}
