package com.siteweb.admin.vo;

import com.siteweb.admin.entity.SpecialtyGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR> z<PERSON>
 * @description SpecialtyGroupVO
 * @createTime 2022-01-17 09:41:15
 */
@Data
@NoArgsConstructor
@ApiModel(value = "新增、保存专业权限组对象", description = "新增、保存专业权限组对象")
public class SpecialtyGroupVO {

  @ApiModelProperty(value = "专业权限组ID", name = "specialtyGroupId")
  private Integer specialtyGroupId;

  @ApiModelProperty(value = "专业权限组名称", name = "specialtyGroupName")
  private String specialtyGroupName;

  @ApiModelProperty(value = "备注", name = "description")
  private String description;

  @ApiModelProperty(value = "角色id", name = "roleId")
  private List<Integer> roleIds;

  public SpecialtyGroup build() {
    SpecialtyGroup specialtyGroup = new SpecialtyGroup();
    BeanUtils.copyProperties(this, specialtyGroup);
    return specialtyGroup;
  }
}
