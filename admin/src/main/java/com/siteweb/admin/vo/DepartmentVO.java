package com.siteweb.admin.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.siteweb.admin.entity.Department;

import java.util.Date;

/**
 * department info tableVO
 *
 * <AUTHOR>
 * @email
 * @date 2022-04-01 12:00:10
 */
@Data
@NoArgsConstructor
@ApiModel(value = "Department实体", description = "Department实体")
public class DepartmentVO {

    /**
     *
     */
    @ApiModelProperty(value = "", name = "departmentId")
    private Integer departmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", name = "departmentName")
    private String departmentName;

    /**
     * 部门等级
     */
    @ApiModelProperty(value = "部门等级", name = "departmentLevel")
    private String departmentLevel;

    /**
     * 部门职能
     */
    @ApiModelProperty(value = "部门职能", name = "departmentFunction")
    private String departmentFunction;

    /**
     * 父部门Id
     */
    @ApiModelProperty(value = "父部门Id", name = "parentDeprtId")
    private Integer parentDeprtId;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息", name = "description")
    private String description;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "lastUpdateDate")
    private Date lastUpdateDate;

    public Department build() {
        Department department = new Department();
        department.setDepartmentId(this.getDepartmentId());
        department.setDepartmentName(this.getDepartmentName());
        department.setDepartmentLevel(this.getDepartmentLevel());
        department.setDepartmentFunction(this.getDepartmentFunction());
        department.setParentDeprtId(this.getParentDeprtId());
        department.setDescription(this.getDescription());
        return department;
    }
}
