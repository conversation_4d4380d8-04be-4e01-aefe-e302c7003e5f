package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.dto.AuditReportParamDto;
import com.siteweb.admin.entity.AuditReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuditReportMapper extends BaseMapper<AuditReport> {
    Integer removeRecordCount(Integer count);

    /**
     * 查找审计报表数据
     * @param auditReportParamDto
     * @return {@link List}<{@link AuditReport}>
     */
    List<AuditReport> findAuditReport(@Param("auditReportParamDto") AuditReportParamDto auditReportParamDto);

    List<Integer> getLatestRecord(@Param("count") Integer count);
}
