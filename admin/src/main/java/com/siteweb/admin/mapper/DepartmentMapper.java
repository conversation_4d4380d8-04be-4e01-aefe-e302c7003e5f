package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.Department;
import java.util.List;

public interface DepartmentMapper extends BaseMapper<Department> {

    List<Department> findAllDepartments();

    List<Department> findDepartmentById(Integer departmentId);

    List<Department> findChildrenDepartments(Integer parentDepartmentId);

    int batchCreate(List<Department> departmentList);
}

