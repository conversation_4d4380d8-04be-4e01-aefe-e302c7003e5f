package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.PermissionCategory;

import java.util.List;

public interface PermissionCategoryMapper extends BaseMapper<PermissionCategory> {

    int createPermissionCategory(PermissionCategory permissionCategory);
    void deletePermissionCategoryById(Integer permissionCategoryId);

    /**
     * 获取当前场景的权限类别
     * @return
     */
    List<PermissionCategory> findPermissionCategorieByScene();
}
