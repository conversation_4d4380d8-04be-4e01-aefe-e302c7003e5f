package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.Permission;

import java.util.List;

public interface PermissionMapper extends BaseMapper<Permission> {

    int createPermission(Permission permission);

    List<Permission> getPermissionsByRoleIdAndCategory(Integer roleId, Integer category);

    List<Permission> getMenuPermissionsByRoleIds(List<Integer> roleIds);

    List<Permission> getPermissionsByRoleId(Integer roleId);

    /**
     * 获取所有的片区组，转换为permission对象
     *
     * @return
     */
    List<Permission> getAllAreaPermission();

    /**
     * 获取所有的专业组，转换为permission对象
     *
     * @return
     */
    List<Permission> getAllSpecialtyGroupPermission();

    /**
     * 根据场景获得权限点
     */
    List<Permission> findPermissionByScene(Integer sceneId);

    /**
     * 根据场景获得权限点根据权限点类型
     *
     * @param category 权限点类型
     * @return {@link List}<{@link Permission}>
     */
    List<Permission> findCurrentScenePermissionByCategory(Integer category);


}
