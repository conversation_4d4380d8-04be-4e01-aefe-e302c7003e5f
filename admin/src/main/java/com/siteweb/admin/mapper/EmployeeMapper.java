package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.Employee;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface EmployeeMapper extends BaseMapper<Employee> {

    int getEmployeeCountsByDepartmentId(int departmentId);

    int getEmployeeCountsByDepartmentIds(List<Integer> idList);

    int getCardCountByEmployeeId(int employeeId);

    int setDefaultDepartmentIdForEmployees(int departmentId);

    int setDefaultDepartmentIdsForEmployees(List<Integer> idList);

    List<Employee> getEmployeeByDepartmentIds(Collection<Integer> departmentIds);

    int batchCreate(List<Employee> employeeList);

    List<String> findEmployeeNameByIds(List<Integer> employeeIds);

    List<String> findEmployeeEmailByIds(List<Integer> employeeIds);

    List<String> findEmployeePhoneByIds(List<Integer> employeeIds);

    List<String> findEmployeeJobNumberByIds(List<Integer> employeeIds);

    @Update("UPDATE TBL_Employee A\n" +
            "JOIN TBL_Account B ON A.EmployeeId = B.UserId\n" +
            "SET A.EmployeeName = #{userName}, A.Phone = #{phoneNumber}\n" +
            "WHERE B.LogonId = #{logonId}\n")
    void updateByCqctccLogonId(@Param("logonId") String logonId, @Param("userName") String userName, @Param("phoneNumber") String phoneNumber);
}
