package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.dto.PersonStaffInfoDTO;
import com.siteweb.admin.dto.RolePermissionInfoDTO;
import com.siteweb.admin.dto.StaffRoleInfoDTO;
import com.siteweb.admin.entity.Account;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface AccountMapper extends BaseMapper<Account> {

    List<Account> findByMobile(String mobile);

    String findUserNameByUserId(Integer userId);
    int batchInsert(List<Account> accountList);

    @Select("SELECT A.UserId, A.<PERSON>r<PERSON>, A.<PERSON>d, A.Enable, A.Description, B.Phone \n" +
            "                         FROM TBL_Account A \n" +
            "                        INNER JOIN TBL_Employee B ON A.UserId = B.EmployeeId \n" +
            "                        WHERE A.UserId > 0")
    List<PersonStaffInfoDTO> getPersonStaffInfo();

    @Select("SELECT B.LogonId, C.<PERSON>d,C.RoleName \n" +
            "FROM TBL_UserRoleMap A \n" +
            "INNER JOIN TBL_Account B ON A.UserId = B.UserId \n" +
            "INNER JOIN TBL_UserRole C ON C.RoleId = A.RoleId \n" +
            "WHERE B.UserId > 0")
    List<StaffRoleInfoDTO> getStaffRoleInfo();

    @Select("SELECT B.RoleName, B.RoleId, C.GroupName, C.GroupId \n" +
            "FROM TBL_UserRoleRight A \n" +
            "INNER JOIN TBL_UserRole B ON A.RoleId = B.RoleId \n" +
            "INNER JOIN TBL_OperationGroup C ON A.OperationId = C.GroupId AND A.OperationType = 1")
    List<RolePermissionInfoDTO> getRolePermissionInfoList();

    boolean passwordInValidTime(String userName);
}
