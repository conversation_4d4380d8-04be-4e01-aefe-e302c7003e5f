package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.MenuItemStructureMap;
import com.siteweb.admin.entity.MenuStructure;

import java.util.List;

public interface MenuStructureMapper extends BaseMapper<MenuStructure> {

    List<MenuStructure> findAllMenuStructures();

    MenuStructure findMenuStructureById(Integer menuStructureId);

    List<MenuStructure> findChildrenMenuStructures(Integer parentMenuStructureId);

    List<MenuStructure> findTopMenuStructuresByMenuProfileId(Integer menuProfileId);

    List<MenuItemStructureMap> findChildrenMenuItemStructureMap(Integer menuStructureId);

    List<MenuStructure> findByMenuProfileIdAndParentId(Integer menuProfileId, Integer parentId);

    MenuStructure findStructureTitleByMenuIdAndProfileId(Integer menuProfileId, Integer menuItemId);
}

