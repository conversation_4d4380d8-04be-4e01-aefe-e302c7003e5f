package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.ScenePermissionMap;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ScenePermissionMapMapper extends BaseMapper<ScenePermissionMap> {
    /**
     * @param cascadeIdList 权限列表
     * @param sceneId        场景标识
     * @return {@link List}<{@link Integer}>
     */
    List<String> findExistsScenePermission(List<String> cascadeIdList, Integer sceneId);

    int batchInsert(List<ScenePermissionMap> scenePermissionMapList);
}
