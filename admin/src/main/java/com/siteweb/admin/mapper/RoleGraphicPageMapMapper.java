package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.dto.RoleGraphicPageMapDTO;
import com.siteweb.admin.entity.RoleGraphicPageMap;

import java.util.List;

public interface RoleGraphicPageMapMapper extends BaseMapper<RoleGraphicPageMap> {
    List<RoleGraphicPageMapDTO> findAll();

    RoleGraphicPageMapDTO findById(Integer id);

    List<RoleGraphicPageMap> findByUserId(Integer id);
}
