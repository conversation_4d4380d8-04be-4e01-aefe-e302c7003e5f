package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.OperationGroupMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperationGroupMapMapper extends BaseMapper<OperationGroupMap> {
    boolean batchCreate(@Param("groupId") Integer groupId, @Param("operationIds") List<Integer> operationIds);

    List<Integer> findPermissionIdsByGroupIds(@Param("permissionGroupIds") List<Integer> permissionGroupIds);
}
