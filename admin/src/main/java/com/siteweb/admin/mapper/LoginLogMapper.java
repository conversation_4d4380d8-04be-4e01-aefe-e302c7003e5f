package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.dto.LoginLogDTO;
import com.siteweb.admin.entity.LoginLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LoginLogMapper
 * @createTime 2022-05-11 10:01:14
 */
public interface LoginLogMapper extends BaseMapper<LoginLog> {

    int insertLoginLog(LoginLog loginLog);

    Page<LoginLogDTO> findByOperatingTimeBetween(@Param("page") Page<LoginLogDTO> page, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
