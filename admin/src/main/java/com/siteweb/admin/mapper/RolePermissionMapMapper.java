package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.vo.UserRoleRightVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface RolePermissionMapMapper extends BaseMapper<RolePermissionMap> {

    int batchCreateRolePermissionMap(List<RolePermissionMap> list);


    /**
     * 根据角色id和操作类型id删除UserRoleRight
     *
     * @param roleId
     * @param operationType
     * @return
     */
    int delUserRoleRightByRoleIdAndOperationType(Integer roleId, Integer operationType);

    /**
     * 统计UserRoleRight
     *
     * @param roleId
     * @param operationId
     * @param operationType
     * @return
     */
    int getUserRoleRightCount(Integer roleId, Integer operationId, Integer operationType);

    /**
     * 获取该角色拥有的片区权限组
     *
     * @param roleId
     * @return
     */
    List<RolePermissionMap> getAreaRolePermissionMaps(Integer roleId);

    /**
     * 获取该角色拥有的专业权限组
     *
     * @param roleId
     * @return
     */
    List<RolePermissionMap> getSpecialtyPermissionMaps(Integer roleId);

    /**
     * 获取该用户id下当前权限类别的所有权限信息
     *
     * @param userId               用户id
     * @param permissionCategoryId 权限类别id
     * @return
     */
    List<RolePermissionMap> getRolePermissionMapsByUserIdAndCategoryId(Integer userId, Integer permissionCategoryId);

    /**
     * 获取该用户id下当前权限类别的所有权限信息
     *
     * @param userId 用户id
     * @return rolePermission 用户权限id集合
     */
    List<Integer> findRolePermissionMapsByUserId(Integer userId);


    /**
     * 根据用户id获取用户权限集合
     *
     * @param userId               用户id
     * @param permissionCategoryId 权限类别
     * @return 角色权限
     */
    Set<Integer> findRolePermissionsByUserId(Integer userId, Integer permissionCategoryId);

    /**
     * 根据当前场景过滤RolePermissionMap
     *
     * @return
     */
    List<RolePermissionMap> findRolePermissionMapByRoleId(Integer roleId);

    int deleteByCascadeId(Integer roleId, Collection<String> cascadeIdSet);

    /**
     * 批量同步TBL_UserRoleRight
     * @param userRoleRightVOList 权限集合
     * @return int
     */
    int batchCreateUserRoleRight(@Param("userRoleRightVOList") List<UserRoleRightVO> userRoleRightVOList);

    Set<Integer> findUserOperationPermissions(@Param("userId") Integer userId);

    Set<Integer> findUserOperationPermissionGroups(@Param("userId") Integer userId);
}

