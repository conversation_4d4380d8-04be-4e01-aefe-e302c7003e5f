package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.MenuPermissionGroupMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MenuPermissionGroupMapMapper extends BaseMapper<MenuPermissionGroupMap> {
    boolean batchCreate(@Param("menuPermissionGroupId") Integer menuPermissionGroupId, @Param("permissionIds") List<Integer> permissionIds);
}
