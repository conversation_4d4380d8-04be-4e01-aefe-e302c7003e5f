package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.RegionMap;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description RegionMapMapper
 * @createTime 2022-01-18 15:20:01
 */
public interface RegionMapMapper extends BaseMapper<RegionMap> {

    List<RegionMap> findByUserId(Integer userId);

    List<RegionMap> findByRegionIds(Collection<Integer> list);

    /**
     * 根据 regionId 集合查询层级ids集合
     * @param regionIds
     * @return resourceStructureIds 层级id集合
     */
    Set<Integer> findResourceStructureIdsByRegionIds(Collection<Integer> regionIds);

    boolean batchInsert(List<RegionMap> regionMaps);
}
