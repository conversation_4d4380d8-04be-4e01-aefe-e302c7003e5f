package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.UserRole;
import com.siteweb.admin.entity.UserRoleMap;

import java.util.List;


public interface UserRoleMapper extends BaseMapper<UserRole> {

    List<UserRole> findRolesByUserId(Integer userId);

    int deleteUserRoleMapsByUserId(Integer userId);

    int deleteUserRoleMapsByUserIdAndRoleId(Integer userId, Integer roleId);

    int createUserRoleMap(Integer userId, Integer roleId);

    /**
     * 删除角色、角色关联的权限点、账号关联角色
     * @param roleId
     * @return
     */
    int deleteUserRoleByRoleId(Integer roleId);

    /**
     * 是否拥有操作权限
     * @param operationId 操作id
     * @param permissionId 权限id
     * @return boolean
     */
    int hasAllPrivilege(Integer userId,Integer permissionId);

    int hasOperationPrivilege(int operationId,Integer userId);

    int isSystemAdministrator(Integer userId);
    int batchCreateUserRoleMap(List<UserRoleMap> userRoleMap);
}
