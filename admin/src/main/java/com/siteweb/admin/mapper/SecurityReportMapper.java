package com.siteweb.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.dto.SecurityReportParamDto;
import com.siteweb.admin.entity.SecurityReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SecurityReportMapper extends BaseMapper<SecurityReport> {
    void removeMaxCountRecord(Integer securityMaxCount);

    List<SecurityReport> findSecurityReport(@Param("securityReportParamDto") SecurityReportParamDto securityReportParamDto);
}
