package com.siteweb.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.admin.dto.OperationGroupCreateDTO;
import com.siteweb.admin.entity.OperationGroup;
import com.siteweb.admin.service.OperationGroupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "OperationGroupController", tags = {"操作权限分组接口"})
public class OperationGroupController {
    @Autowired
    OperationGroupService operationGroupService;

    @GetMapping("/operationgroup")
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(operationGroupService.findAll());
    }
    @GetMapping("/operationgroup/{id}")
    public ResponseEntity<ResponseResult> findById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(operationGroupService.findById(id));
    }

    @PostMapping("/operationgroup")
    public ResponseEntity<ResponseResult> create(@RequestBody OperationGroupCreateDTO operationGroupCreateDTO){
        return ResponseHelper.successful(operationGroupService.create(BeanUtil.copyProperties(operationGroupCreateDTO,OperationGroup.class)));
    }

    @DeleteMapping("/operationgroup/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(operationGroupService.deleteById(id));
    }

    @PutMapping("/operationgroup")
    public ResponseEntity<ResponseResult> update(@RequestBody OperationGroup operationGroup){
        return ResponseHelper.successful(operationGroupService.updateById(operationGroup));
    }
}
