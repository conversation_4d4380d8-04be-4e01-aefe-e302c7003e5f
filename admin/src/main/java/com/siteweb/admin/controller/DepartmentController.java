package com.siteweb.admin.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.Department;
import com.siteweb.admin.entity.DepartmentCodeMap;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.admin.service.DepartmentService;
import com.siteweb.admin.vo.DepartmentVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * department info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-04-01 12:00:10
 */
@RestController
@RequestMapping("/api")
@Api(value = "DepartmentController", tags = {"部门操作接口"})
public class DepartmentController {
    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    DepartmentService departmentService;

    @Autowired
    DepartmentPermissionService departmentPermissionService;

    /**
     * GET  /departments : get the Departments.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of Departments in body
     */
    @ApiOperation(value = "获取所有Department实体")
    @GetMapping("/departments")
    public ResponseEntity<ResponseResult> getDepartments() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(departmentPermissionService.findDepartmentTreeByUserId(loginUserId), HttpStatus.OK);
    }

    /**
     * GET  /departments/:id  get the Department by id.
     *
     * @param departmentId the DepartmentId
     * @return the ResponseEntity with status 200 (OK) and with body the Department, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个Department实体")
    @GetMapping("/departments/{departmentId}")
    public ResponseEntity<ResponseResult> getDepartmentById(@PathVariable("departmentId") @ApiParam(name = "departmentId", value = "唯一ID", required = true) Integer departmentId) {
        List<Department> departments = departmentService.findDepartmentById(departmentId);
        return Optional.ofNullable(departments)
                .map(result -> ResponseHelper.successful(departments, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /departments : create a new Department
     *
     * @param departmentVO the Department to create
     * @return the ResponseEntity with status 200 (OK) and with body the new Department,
     * or with status 400 (Bad Request) if the Department has already an ID
     */
    @ApiOperation(value = "新增Department实体")
    @ApiOperationSupport(ignoreParameters = {"departmentId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新增成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "departmentId必须为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "新增失败", content = @Content)
            })
    @PostMapping(value = "/departments")
    public ResponseEntity<ResponseResult> createDepartment(@Valid @RequestBody DepartmentVO departmentVO) {
        if (departmentVO.getDepartmentId() != null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "DepartmentId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = departmentService.createDepartment(departmentVO.build());
        if (result > 0) {
            return ResponseHelper.successful(departmentVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create department error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT  /departments : Updates an existing Department.
     *
     * @param departmentVO the Department to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated Department,
     * or with status 404 (Not Found) if the departmentId is not exists,
     */
    @ApiOperation(value = "更新Department实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "修改成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "departmentId不能为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "修改失败", content = @Content)
            })
    @PutMapping(value = "/departments")
    public ResponseEntity<ResponseResult> updateDepartment(@Valid @RequestBody DepartmentVO departmentVO) {
        if (departmentVO.getDepartmentId() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "DepartmentId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = departmentService.updateDepartment(departmentVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update department error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * DELETE  /departments/:id : delete the Department by id.
     *
     * @param id the id of the Department to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据唯一ID删除Department实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "找不到Department实体", content = @Content)
            })
    @DeleteMapping(value = "/departments/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDepartment(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {
        List<Department> departments = departmentService.findDepartmentById(id);
        if (departments.isEmpty()) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        departmentService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "批量删除Department实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "找不到Department实体", content = @Content)
            })
    @DeleteMapping(value = "/departmentsbatchdel/{ids}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteDepartment(@PathVariable @ApiParam(name = "ids", value = "IDs", required = true) String ids) {
        List<Integer> idList = Arrays.stream(ids.split(",")).map(Integer::valueOf).toList();
        int result = departmentService.batchDeleteByIds(idList);
        if (result > 0) {
            return ResponseHelper.successful(result);
        }
        return ResponseHelper.successful(HttpStatus.NOT_FOUND);
    }

    @ApiOperation(value = "全量同步部门，入参有departmentId")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新增成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "500", description = "新增失败", content = @Content)
            })
    @PostMapping(value = "/syncdepartments")
    public ResponseEntity<ResponseResult> syncDepartments(@Valid @RequestBody List<DepartmentVO> departmentVOList) {
        List<Department> departmentList = departmentVOList.stream().map(DepartmentVO::build).toList();
        int result = departmentService.batchCreateDepartment(departmentList);
        if (result > 0) {
            return ResponseHelper.successful(departmentList);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Batch Create department error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "全量同步departmentcodemap")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新增成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "500", description = "新增失败", content = @Content)
            })
    @PostMapping(value = "/syncdepartmentcodemaps")
    public ResponseEntity<ResponseResult> syncDepartmentCodeMaps(@Valid @RequestBody List<DepartmentCodeMap> departmentCodeMapList) {
        int result = departmentService.batchCreateDepartmentCodeMap(departmentCodeMapList);
        if (result > 0) {
            return ResponseHelper.successful(departmentCodeMapList);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Batch Create department error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "获取departmentcodemap列表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "获取成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "500", description = "获取失败", content = @Content)
            })
    @GetMapping(value = "/departmentcodemaps")
    public ResponseEntity<ResponseResult> getAllDepartmentCodeMaps() {
        return ResponseHelper.successful(departmentService.getAllDepartmentCodeMap());
    }

    @ApiOperation(value = "批量删除departmentcodemaps实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content})
            })
    @DeleteMapping(value = "/departmentcodemaps/{ids}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDepartmentCodeMaps(@PathVariable @ApiParam(name = "ids", value = "IDs", required = true) String ids) {
        List<Integer> idList = Arrays.stream(ids.split(",")).map(Integer::valueOf).toList();
        int result = departmentService.deleteByIds(idList);
        if (result > 0) {
            return ResponseHelper.successful(result);
        }
        return ResponseHelper.successful(HttpStatus.NOT_FOUND);
    }

    @ApiOperation(value = "更新departmentcodemaps实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "修改成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "500", description = "修改失败", content = @Content)
            })
    @PutMapping(value = "/departmentcodemaps")
    public ResponseEntity<ResponseResult> updateDepartmentCodeMap(@Valid @RequestBody DepartmentCodeMap departmentCodeMap) {
        if (departmentCodeMap.getDepartmentId() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "DepartmentId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = departmentService.updateDepartmentCodeMap(departmentCodeMap);
        if (result > 0) {
            return ResponseHelper.successful(result);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update department error",
                    HttpStatus.BAD_REQUEST);
        }
    }
}
