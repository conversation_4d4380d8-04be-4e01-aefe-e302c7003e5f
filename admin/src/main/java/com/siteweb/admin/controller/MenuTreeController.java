package com.siteweb.admin.controller;

import com.siteweb.admin.dto.MenuTreeDTO;
import com.siteweb.admin.language.LanguageUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.MenuTreeService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api")
@Api(value = "MenuTreeController", tags = {"菜单树操作接口"})
public class MenuTreeController {

    @Autowired
    MenuTreeService menuTreeService;

    /**
     * GET /menutrees/:userId : get the menu tree by userId.
     *
     * @param userId the userId
     * @return the ResponseEntity with status 200 (OK) and with body of the menu tree, or with status
     * 400 (Bad Request) if request error
     */
    @Operation(summary = "根据userId查询其菜单树")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "根据userId查询其菜单树",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "请求参数错误或菜单方案未配置", content = @Content)
            })
    @GetMapping(value = "/menutrees")
    public ResponseEntity<ResponseResult> getCurrentMenuTreesByUserId(HttpServletRequest request) {
        MenuTreeDTO menuTreeDTO = menuTreeService.getMenuTreeByUserId(TokenUserUtil.getLoginUserId());
        LanguageUtil.menuSwitch(menuTreeDTO, request.getHeader(LanguageUtil.LANGUAGE_HEADER));
        if (menuTreeDTO == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "menu tree is empty",
                    HttpStatus.BAD_REQUEST);
        } else {
            return ResponseHelper.successful(menuTreeDTO);
        }
    }
}
