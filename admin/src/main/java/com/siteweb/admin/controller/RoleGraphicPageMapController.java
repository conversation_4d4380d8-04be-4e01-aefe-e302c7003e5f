package com.siteweb.admin.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.entity.RoleGraphicPageMap;
import com.siteweb.admin.service.RoleGraphicPageMapService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "RoleGraphicPageMapController", tags = {"角色组态绑定接口"})
public class RoleGraphicPageMapController {
    @Autowired
    RoleGraphicPageMapService roleGraphicPageMapService;

    @Operation(summary = "获取所有角色组态映射关系信息")
    @GetMapping(value = "/rolegraphicpagemap")
    public ResponseEntity<ResponseResult> getAll(){
        return ResponseHelper.successful(roleGraphicPageMapService.findAll());
    }

    @Operation(summary = "通过id获取角色组态映射关系信息")
    @GetMapping(value = "/rolegraphicpagemap/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(roleGraphicPageMapService.findById(id));
    }

    @Operation(summary = "创建角色组态映射关系")
    @PostMapping(value = "/rolegraphicpagemap")
    public ResponseEntity<ResponseResult> create(@RequestBody RoleGraphicPageMap roleGraphicPageMap){
        RoleGraphicPageMap roleGraphicPageMapFromDb = roleGraphicPageMapService.findByRoleId(roleGraphicPageMap.getRoleId());
        if (!ObjectUtil.isNull(roleGraphicPageMapFromDb)) {
            return ResponseHelper.successful(roleGraphicPageMapService.createOrUpdate(roleGraphicPageMap));
        }
        return ResponseHelper.successful(roleGraphicPageMapService.create(roleGraphicPageMap));
    }

    @Operation(summary = "更新角色组态映射关系")
    @PutMapping(value = "/rolegraphicpagemap")
    public ResponseEntity<ResponseResult> update(@RequestBody RoleGraphicPageMap roleGraphicPageMap){
        return ResponseHelper.successful(roleGraphicPageMapService.createOrUpdate(roleGraphicPageMap));
    }

    @Operation(summary = "删除角色组态映射关系")
    @DeleteMapping(value = "/rolegraphicpagemap/{id}")
    public ResponseEntity<ResponseResult> update(@PathVariable("id") Integer id){
        return ResponseHelper.successful(roleGraphicPageMapService.deleteById(id));
    }
}
