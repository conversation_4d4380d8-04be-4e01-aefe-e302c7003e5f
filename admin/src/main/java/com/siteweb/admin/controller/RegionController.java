package com.siteweb.admin.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.RegionService;
import com.siteweb.admin.vo.RegionVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description RegionController
 * @createTime 2022-01-18 15:00:53
 */
@RestController
@RequestMapping("/api")
@Api(value = "RegionController", tags = {"区域操作接口"})
public class RegionController {

    @Autowired
    RegionService regionService;

    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @ApiOperation(value = "新增区域实体")
    @ApiOperationSupport(ignoreParameters = {"regionId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的Region已被创建",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "新的Region其regionId必须为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "新增失败", content = @Content)
            })
    @PostMapping(value = "/regions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createRegion(@Valid @RequestBody RegionVO regionVO) {
        List<Integer> roleIds = TokenUserUtil.getRoles();
        if (CollectionUtil.isEmpty(roleIds)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.ROLE_NOT_FOUND_ERROR.value()),
                    "regions error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        if (null != regionVO.getRegionId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "RegionId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        regionVO.setRoleIds(roleIds);
        Integer result = regionService.createRegion(regionVO);
        if (result > 0) {
            regionVO.setRegionId(result);
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.REGION,  localeMessageSourceUtil.getMessage("audit.report.addRegionPermission") + "：" + regionVO.getRegionName());
            return ResponseHelper.successful(regionVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create region error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation(value = "修改区域实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Region已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的Region其regionId为null或输入错误",
                            content = @Content)
            })
    @PutMapping(value = "/regions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateRegion(@Valid @RequestBody RegionVO regionVO) {
        if (null == regionVO.getRegionId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "RegionId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = regionService.updateRegion(regionVO.build());
        if (result > 0) {
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.REGION, localeMessageSourceUtil.getMessage("audit.report.updateRegionPermission") + "："  + JSONUtil.toJsonStr(regionVO));
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update region error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation(value = "获取所有Region实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有Region实体",
                            content = {@Content})
            })
    @GetMapping("/regions")
    public ResponseEntity<ResponseResult> getAllRegions() {
        return ResponseHelper.successful(regionService.findAllRegions(), HttpStatus.OK);
    }

    @ApiOperation(value = "根据角色获取所有Region实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有Region实体",
                            content = {@Content})
            })
    @GetMapping("/regions/role")
    public ResponseEntity<ResponseResult> getAllRegionsByRole() {
        List<Integer> roleIds = TokenUserUtil.getRoles();
        if (CollectionUtil.isEmpty(roleIds)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.ROLE_NOT_FOUND_ERROR.value()),
                    "regionsbyrole error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return ResponseHelper.successful(regionService.findAllRegionsByRole(roleIds), HttpStatus.OK);
    }

    @ApiOperation(value = "根据RegionID删除Region实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Region实体被删除",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "根据RegionID查询不到Region实体", content = @Content)
            })
    @DeleteMapping(value = "/regions/{regionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleterRegionByRegionId(
            @PathVariable @ApiParam(name = "regionId", value = "区域ID", required = true) Integer regionId) {
        Region region = regionService.findById(regionId);
        if (region == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        regionService.deleteById(regionId);
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.REGION, localeMessageSourceUtil.getMessage("audit.report.deleteRegionPermission") + "："+ region.getRegionName());
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
