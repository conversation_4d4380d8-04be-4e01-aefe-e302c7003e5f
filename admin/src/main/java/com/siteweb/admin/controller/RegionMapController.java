package com.siteweb.admin.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.vo.RegionMapCreteVO;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description RegionMapController
 * @createTime 2022-01-18 15:30:34
 */
@RestController
@RequestMapping("/api")
@Api(value = "RegionMapController", tags = {"区域Map操作接口"})
public class RegionMapController {

    @Autowired
    RegionMapService regionMapService;
    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @ApiOperation(value = "批量新增AreaMap实体")
    @ApiOperationSupport(ignoreParameters = {"regionMapId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的RegionMap实体已被创建",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "批量保存RegionMap实体报错", content = @Content)
            })
    @PostMapping(value = "/regionmaps", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveRegionMaps(@Valid @RequestBody RegionMapCreteVO regionMapCreteVOS) {
        return ResponseHelper.successful(regionMapService.saveRegionMaps(regionMapCreteVOS));
    }

    @ApiOperation(value = "根据RegionID查询RegionMap实体列表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据RegionID查询到的RegionMap实体列表",
                            content = {@Content})
            })
    @GetMapping("/regionmaps/{regionId}")
    public ResponseEntity<ResponseResult> getRegionMapsByRegionId(
            @PathVariable("regionId") @ApiParam(name = "regionId", value = "区域ID", required = true)
                    Integer regionId) {
        List<RegionMap> regionMaps = regionMapService.findByRegionId(regionId);
        return ResponseHelper.successful(regionMaps, HttpStatus.OK);
    }

    @ApiOperation(value = "根据RegionID删除RegionMap实体列表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "根据RegionId删除RegionMap删除成功",
                            content = {@Content})
            })
    @DeleteMapping("/regionmaps/{regionId}")
    public ResponseEntity<ResponseResult> del(
            @PathVariable("regionId") @ApiParam(name = "regionId", value = "区域ID", required = true)
                    Integer regionId) {
        List<RegionMap> regionMaps = regionMapService.findByRegionId(regionId);
        if (regionMaps.isEmpty()) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        regionMapService.deleteByRegionId(regionId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

}
