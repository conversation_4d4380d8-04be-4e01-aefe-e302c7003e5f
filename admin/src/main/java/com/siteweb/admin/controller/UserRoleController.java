package com.siteweb.admin.controller;

import cn.hutool.json.JSONUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.UserRole;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.UserRoleService;
import com.siteweb.admin.vo.UserRoleVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description UserRoleController
 * @createTime 2022-03-17 15:29:30
 */
@RestController
@RequestMapping("/api")
@Api(value = "UserRoleController", tags = {"角色操作接口"})
public class UserRoleController {

    @Autowired
    UserRoleService userRoleService;

    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    /**
     * GET /roles/:roleId get the UserRole by roleId.
     *
     * @param roleId the RoleId
     * @return the ResponseEntity with status 200 (OK) and with body the UserRole, or with status 404 (Not
     * Found)
     */
    @ApiOperation(value = "根据RoleID查询单个UserRole实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据RoleID查询到的UserRole实体",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "根据RoleID查询不到UserRole实体", content = @Content)
            })
    @GetMapping("/roles/{roleId}")
    public ResponseEntity<ResponseResult> getUserRoleByRoleId(
            @PathVariable("roleId") @ApiParam(name = "roleId", value = "角色ID", required = true)
                    Integer roleId) {
        UserRole userRole = userRoleService.findByRoleId(roleId);
        return Optional.ofNullable(userRole)
                .map(result -> ResponseHelper.successful(userRole, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @Operation(summary = "查询所有角色")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有角色",
                            content = {@Content})
            })
    @GetMapping(value = "/roles", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllUserRoles() {
        List<UserRole> userRoleList = userRoleService.findAllRoles();
        return ResponseHelper.successful(userRoleList, HttpStatus.OK);
    }

    @Operation(summary = "根据角色ID删除单个角色")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "角色被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "查找不到对应的角色",
                            content = @Content)
            })
    @DeleteMapping(value = "/roles/{roleId}")
    public ResponseEntity<ResponseResult> deleteUserRoleById(
            @ApiParam(name = "roleId", value = "角色ID", required = true) @PathVariable
                    Integer roleId) {
        UserRole userRole = userRoleService.findByRoleId(roleId);
        if (userRole == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        securityAuditManager.recordAuditReport(AuditReportTypeEnum.ROLE, localeMessageSourceUtil.getMessage("audit.report.deleteRole") + "：" + userRole.getRoleName());
        userRoleService.deleteUserRoleByRoleId(roleId);
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @Operation(summary = "修改一个角色")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "角色已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的角色其roleId不能为null",
                            content = @Content)
            })
    @PutMapping(value = "/roles", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateUserRole(
            @Valid @RequestBody UserRoleVO userRoleVO) {
        if (null == userRoleVO.getRoleId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "RoleId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = userRoleService.updateUserRole(userRoleVO);
        if (result > 0) {
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.ROLE, localeMessageSourceUtil.getMessage("audit.report.updateRole") + "："  + JSONUtil.toJsonStr(userRoleVO.build()));
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "RoleId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    @Operation(summary = "创建一个角色")
    @ApiOperationSupport(ignoreParameters = {"roleId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "201",
                            description = "新的角色已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "新的角色其RoleId必须为null",
                            content = @Content)
            })
    @PostMapping(value = "/roles", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createUserRole(
            @Valid @RequestBody UserRoleVO userRoleVO) {
        if (null != userRoleVO.getRoleId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "A new UserRole cannot have an RoleId",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = userRoleService.createUserRole(userRoleVO.build());
        if (result > 0) {
            userRoleVO.setRoleId(result);
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.ROLE, localeMessageSourceUtil.getMessage("audit.report.addRole") + "：" + userRoleVO.getRoleName());
            return ResponseHelper.successful(userRoleVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.PRIMARY_KEY_ASSIGN_ERROR.value()),
                    "Can not get RoleId",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
