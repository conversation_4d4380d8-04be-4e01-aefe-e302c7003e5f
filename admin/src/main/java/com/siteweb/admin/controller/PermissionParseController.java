package com.siteweb.admin.controller;

import com.siteweb.admin.dto.UpdateRolePermissionDTO;
import com.siteweb.admin.parse.PermissionParseManager;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "PermissionParseController", tags = {"角色权限点操作接口"})
public class PermissionParseController {
    @Autowired
    PermissionParseManager permissionParseManager;


    @ApiOperation(value = "通过权限类型获取权限项")
    @GetMapping(value = "/permbycategory")
    public ResponseEntity<ResponseResult> getPermissionByCategory(Integer menuProfile, Integer categoryId) {
        return ResponseHelper.successful(permissionParseManager.findByPermissionCategory(menuProfile, categoryId));
    }

    @ApiOperation(value = "通过权限类型与角色id获取拥有的权限项")
    @GetMapping(value = "/rolepermbycategory")
    public ResponseEntity<ResponseResult> getRolePermissionByCategory(Integer roleId, Integer categoryId, Integer menuProfileId) {
        return ResponseHelper.successful(permissionParseManager.findRolePermission(roleId, categoryId,menuProfileId));
    }

    @ApiOperation(value = "通过权限类型与角色id获取拥有的权限项")
    @PutMapping(value = "/rolepermbycategory")
    public ResponseEntity<ResponseResult> updateRolePermissionByCategory(@RequestBody UpdateRolePermissionDTO updateRolePermissionDTO) {
        return ResponseHelper.successful(permissionParseManager.updateRolePermission(updateRolePermissionDTO));
    }
}
