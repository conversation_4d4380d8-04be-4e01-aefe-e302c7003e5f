package com.siteweb.admin.controller;

import com.siteweb.admin.entity.IpFilterPolicy;
import com.siteweb.admin.service.IpFilterPolicyService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "IpFilterPolicyController", tags = {"IP过滤策略配置接口"})
public class IpFilterPolicyController {


    @Autowired
    IpFilterPolicyService ipFilterPolicyService;

    @ApiOperation(value = "查询IpFilterPolicy实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回查询到的IpFilterPolicy实体",
                            content = {@Content})
            })
    @GetMapping(value = "/ipfilterpolicys", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findIpFilterPolicys() {
        return ResponseHelper.successful(ipFilterPolicyService.findIpFilterPolicys(), HttpStatus.OK);
    }

    @ApiOperation(value = "新增IpFilterPolicy")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的IpFilterPolicy已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "新增IpFilterPolicy失败",
                            content = {@Content})
            })
    @PostMapping(value = "/ipfilterpolicys", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createIpFilterPolicy(@Valid @RequestBody IpFilterPolicy ipFilterPolicy) {
        Integer result = ipFilterPolicyService.createIpFilterPolicy(ipFilterPolicy);
        if (result > 0) {
            return ResponseHelper.successful(ipFilterPolicy);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create ipfilterpolicy error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @ApiOperation(value = "修改IpFilterPolicy")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "IpFilterPolicy已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的IpFilterPolicy其IpFilterPolicyId为null或输入错误",
                            content = @Content)
            })
    @PutMapping(value = "/ipfilterpolicys")
    public ResponseEntity<ResponseResult> updateIpFilterPolicy(@Valid @RequestBody IpFilterPolicy ipFilterPolicy) {
        if (ipFilterPolicy.getIpFilterPolicyId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "IpFilterPolicyId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = ipFilterPolicyService.updateIpFilterPolicy(ipFilterPolicy);
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "IpFilterPolicyId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation(value = "按主键ID删除IpFilterPolicy")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据主键Id找不到对应记录",
                            content = {@Content})
            })
    @DeleteMapping(value = "/ipfilterpolicys/{ipFilterPolicyId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteIpFilterPolicyById(@PathVariable @ApiParam(name = "ipFilterPolicyId", value = "主键ID", required = true) Integer ipFilterPolicyId) {
        IpFilterPolicy ipFilterPolicy = ipFilterPolicyService.findIpFilterPolicyById(ipFilterPolicyId);
        if (null == ipFilterPolicy) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        ipFilterPolicyService.deleteIpFilterPolicyById(ipFilterPolicyId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

}
