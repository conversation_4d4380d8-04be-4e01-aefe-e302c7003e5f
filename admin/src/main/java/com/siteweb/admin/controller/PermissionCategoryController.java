package com.siteweb.admin.controller;

import com.siteweb.admin.service.PermissionCategoryService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zhou @Description PermissionCategoryController
 * @createTime 2022-01-04 09:55:37
 */
@RestController
@RequestMapping(value = "/api")
@Api(value = "PermissionCategoryController", tags = {"权限分类操作接口"})
public class PermissionCategoryController {

    @Autowired
    PermissionCategoryService permissionCategoryService;


    @ApiOperation(value = "获取所有PermissionCategory实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有PermissionCategory实体",
                            content = {@Content})
            })
    @GetMapping(value = "/permcategories", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPermissionCategories() {
        return ResponseHelper.successful(permissionCategoryService.getAllPermissionCategories());
    }

    @ApiOperation(value = "自动根据场景获取所有PermissionCategory实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有PermissionCategory实体",
                            content = {@Content})
            })
    @GetMapping(value = "/permcategories/scene", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findPermissionCategoryByScene() {
        return ResponseHelper.successful(permissionCategoryService.findPermissionCategoryByScene());
    }

}
