package com.siteweb.admin.controller;


import cn.hutool.core.bean.BeanUtil;
import com.siteweb.admin.dto.MenuPermissionGroupCreateDTO;
import com.siteweb.admin.entity.MenuPermissionGroup;
import com.siteweb.admin.service.MenuPermissionGroupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "MenuPermissionGroupController", tags = {"菜单权限分组操作接口"})
public class MenuPermissionGroupController {
    @Autowired
    MenuPermissionGroupService menuPermissionGroupService;

    @GetMapping(value = "/menupermissiongroup")
    public ResponseEntity<ResponseResult> getByGroupId(){
        return ResponseHelper.successful(menuPermissionGroupService.findAll());
    }

    @GetMapping(value = "/menupermissiongroup/{id}")
    public ResponseEntity<ResponseResult> getByGroupId(@PathVariable("id") Integer id){
        return ResponseHelper.successful(menuPermissionGroupService.findById(id));
    }

    @PostMapping(value = "/menupermissiongroup")
    public ResponseEntity<ResponseResult> getByGroupId(@RequestBody MenuPermissionGroupCreateDTO menuPermissionGroupCreateDTO){
        return ResponseHelper.successful(menuPermissionGroupService.create(BeanUtil.copyProperties(menuPermissionGroupCreateDTO, MenuPermissionGroup.class)));
    }

    @DeleteMapping(value = "/menupermissiongroup/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(menuPermissionGroupService.deleteById(id));
    }

    @PutMapping(value = "/menupermissiongroup")
    public ResponseEntity<ResponseResult> deleteById(@RequestBody MenuPermissionGroup menuPermissionGroup){
        return ResponseHelper.successful(menuPermissionGroupService.updateById(menuPermissionGroup));
    }
}
