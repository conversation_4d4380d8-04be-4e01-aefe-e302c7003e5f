package com.siteweb.admin.controller;

import com.siteweb.admin.entity.RolePermissionMap;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.admin.vo.RolePermissionMapVO;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * Role, Permission map info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-03-26 13:08:07
 */
@RestController
@RequestMapping("/api")
@Api(value = "RolePermissionMapController", tags = {"角色权限点操作接口"})
public class RolePermissionMapController {

    @Autowired
    private RolePermissionMapService rolePermissionMapService;


    @ApiOperation(value = "获取所有RolePermissionMap实体")
    @GetMapping("/rolepermmaps")
    public ResponseEntity<ResponseResult> getRolePermissionMaps() {
        return ResponseHelper.successful(rolePermissionMapService.findRolePermissionMaps(), HttpStatus.OK);
    }


    @ApiOperation(value = "根据ID获取单个RolePermissionMap实体")
    @GetMapping("/rolepermmaps/{rolePermissionMapId}")
    public ResponseEntity<ResponseResult> getRolePermissionMapById(@PathVariable("rolePermissionMapId") @ApiParam(name = "rolePermissionMapId", value = "唯一ID", required = true) Integer rolePermissionMapId) {
        RolePermissionMap rolePermissionMap = rolePermissionMapService.findById(rolePermissionMapId);
        return Optional.ofNullable(rolePermissionMap)
                .map(result -> ResponseHelper.successful(rolePermissionMap, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }


    @ApiOperation(value = "新增RolePermissionMap实体")
    @PostMapping(value = "/rolepermmaps")
    public ResponseEntity<ResponseResult> createRolePermissionMap(@Valid @RequestBody RolePermissionMapVO rolePermissionMapVO) {
        if (rolePermissionMapVO.getRolePermissionMapId() != null) {
            return ResponseHelper.failed("-1", "A new RolePermissionMap  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }
        rolePermissionMapService.createRolePermissionMap(rolePermissionMapVO.build());
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @ApiOperation(value = "更新RolePermissionMap实体")
    @PutMapping(value = "/rolepermmaps")
    public ResponseEntity<ResponseResult> updateRolePermissionMap(@Valid @RequestBody RolePermissionMapVO rolePermissionMapVO) {
        if (rolePermissionMapVO.getRolePermissionMapId() == null) {
            return ResponseHelper.failed("-1", "RolePermissionMap Not Found.", HttpStatus.NOT_FOUND);
        }
        rolePermissionMapService.updateRolePermissionMap(rolePermissionMapVO.build());
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @ApiOperation(value = "根据唯一ID删除RolePermissionMap实体")
    @DeleteMapping(value = "/rolepermmaps/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteRolePermissionMap(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {
        RolePermissionMap rolePermissionMap = rolePermissionMapService.findById(id);
        if (rolePermissionMap == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        rolePermissionMapService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "填充角色Permission")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "同步Permission成功", content = {@Content})})
    @GetMapping(value = "/syncroleperm")
    public ResponseEntity<ResponseResult> syncRolePermission(Integer roleId) {
        //此接口用于赋予指定角色所有的系统权限。
        //背景：由于早期版本中管理员权限是可以编辑的，但是现在的版本中管理员权限不可编辑了，导致现场想给管理员添加权限添加不了。
        return ResponseHelper.successful(rolePermissionMapService.fillRolePermission(roleId));
    }
}
