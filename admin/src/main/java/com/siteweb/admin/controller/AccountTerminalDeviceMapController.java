package com.siteweb.admin.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.entity.AccountTerminalDeviceMap;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.AccountTerminalDeviceMapService;
import com.siteweb.admin.vo.AccountTerminalDeviceVO;
import com.siteweb.admin.vo.UserTerminalDeviceVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "AccountTerminalDeviceMapController", tags = {"账号终端绑定功能操作接口"})
public class AccountTerminalDeviceMapController {
    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    private AccountTerminalDeviceMapService accountTerminalDeviceMapService;

    @Autowired
    private AccountService accountService;

    @ApiOperation(value = "根据userId获取单个AccountTerminalDeviceMap实体")
    @GetMapping(value = "/accountterminaldevicemap", params = {"userId"})
    public ResponseEntity<ResponseResult> getAccountTerminalDeviceMapById(Integer userId)  {
        AccountTerminalDeviceMap accountTerminalDeviceMap = accountTerminalDeviceMapService.findById(userId);
        return Optional.ofNullable(accountTerminalDeviceMap)
                .map(result -> ResponseHelper.successful(accountTerminalDeviceMap, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据logonId获取单个AccountTerminalDeviceMap实体")
    @GetMapping(value = "/accountterminaldevicemap", params = {"logonId"})
    public ResponseEntity<ResponseResult> getAccountTerminalDeviceMapByLogonId(String logonId) {
        Integer userId = accountService.findUserIdByLogonId(logonId);
        if (userId == null) {
            return ResponseHelper.failed("-1", "logonId not exist", HttpStatus.BAD_REQUEST);
        }
        AccountTerminalDeviceMap accountTerminalDeviceMap = accountTerminalDeviceMapService.findById(userId);
        return Optional.ofNullable(accountTerminalDeviceMap)
                .map(result -> ResponseHelper.successful(accountTerminalDeviceMap, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }


    @ApiOperation(value = "账号终端绑定")
    @PostMapping(value = "/accountterminaldevicemap/bind")
    public ResponseEntity<ResponseResult> accountTerminalDeviceMapBind(@Valid @RequestBody AccountTerminalDeviceVO accountTerminalDeviceVO) {
        Integer userId = accountService.findUserIdByLogonId(accountTerminalDeviceVO.getLogonId());
        if (userId == null) {
            return ResponseHelper.failed("-1", "logonId not exist", HttpStatus.BAD_REQUEST);
        }
        AccountTerminalDeviceMap accountTerminalDeviceMap = accountTerminalDeviceVO.build();
        accountTerminalDeviceMap.setUserId(userId);
        accountTerminalDeviceMap.setOperatorId(userId);
        AccountTerminalDeviceMap existMap = accountTerminalDeviceMapService.findById(accountTerminalDeviceMap.getUserId());
        if (ObjectUtil.isNull(existMap)) {
            return ResponseHelper.successful(accountTerminalDeviceMapService.createAccountTerminalDeviceMap(accountTerminalDeviceMap));
        } else {
            return ResponseHelper.successful(accountTerminalDeviceMapService.updateAccountTerminalDeviceMap(accountTerminalDeviceMap));
        }
    }


    @ApiOperation(value = "账号终端解绑")
    @PutMapping(value = "/accountterminaldevicemap/unbindbyuserid")
    public ResponseEntity<ResponseResult> accountTerminalDeviceMapUnbindByUserId(@Valid @RequestBody UserTerminalDeviceVO userTerminalDeviceVO) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        AccountTerminalDeviceMap existMap = accountTerminalDeviceMapService.findById(userTerminalDeviceVO.getUserId());
        if (ObjectUtil.isNull(existMap) || existMap.getTerminalDeviceId() == null) {
            return ResponseHelper.failed("-1", "account bind no terminal device", HttpStatus.BAD_REQUEST);
        }

        if (!existMap.getTerminalDeviceId().equals(userTerminalDeviceVO.getTerminalDeviceId())) {
            return ResponseHelper.failed("-1", "wrong terminal device id", HttpStatus.BAD_REQUEST);
        }

        int result = accountTerminalDeviceMapService.unbind(userTerminalDeviceVO.getUserId(), loginUserId);
        if (result == 1) {
            return ResponseHelper.successful(HttpStatus.OK);
        }
        return ResponseHelper.failed("-1", "unbind failure", HttpStatus.BAD_REQUEST);
    }

    @ApiOperation(value = "账号终端解绑")
    @PutMapping(value = "/accountterminaldevicemap/unbindbylogonid")
    public ResponseEntity<ResponseResult> accountTerminalDeviceMapUnbindByLogonId(@Valid @RequestBody AccountTerminalDeviceVO accountTerminalDeviceVO) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        Integer userId = accountService.findUserIdByLogonId(accountTerminalDeviceVO.getLogonId());
        if (userId == null) {
            return ResponseHelper.failed("-1", "logonId not exist", HttpStatus.BAD_REQUEST);
        }
        AccountTerminalDeviceMap existMap = accountTerminalDeviceMapService.findById(userId);
        if (ObjectUtil.isNull(existMap) || existMap.getTerminalDeviceId() == null) {
            return ResponseHelper.failed("-1", "account bind no terminal device", HttpStatus.BAD_REQUEST);
        }

        if (!existMap.getTerminalDeviceId().equals(accountTerminalDeviceVO.getTerminalDeviceId())) {
            return ResponseHelper.failed("-1", "wrong terminal device id", HttpStatus.BAD_REQUEST);
        }

        int result = accountTerminalDeviceMapService.unbind(userId, loginUserId);
        if (result == 1) {
            return ResponseHelper.successful(HttpStatus.OK);
        }
        return ResponseHelper.failed("-1", "unbind failure", HttpStatus.BAD_REQUEST);
    }
}
