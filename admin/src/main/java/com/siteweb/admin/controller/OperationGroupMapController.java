package com.siteweb.admin.controller;

import com.siteweb.admin.dto.OperationGroupMapCreateDTO;
import com.siteweb.admin.service.OperationGroupMapService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "OperationGroupMapController", tags = {"操作权限分组映射接口"})
public class OperationGroupMapController {

    @Autowired
    OperationGroupMapService operationGroupMapService;

    @GetMapping(value = "/operationgroupmap",params = "groupId")
    public ResponseEntity<ResponseResult> findByGroupId(Integer groupId){
        return ResponseHelper.successful(operationGroupMapService.findByGroupId(groupId));
    }

    @PostMapping(value = "/operationgroupmap")
    public ResponseEntity<ResponseResult> create(@RequestBody OperationGroupMapCreateDTO operationGroupMapCreateDTO){
        return ResponseHelper.successful(operationGroupMapService.create(operationGroupMapCreateDTO));
    }
}
