package com.siteweb.admin.controller;

import com.siteweb.admin.entity.UserConfig;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.UserConfigService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户设置控制器
 *
 * <AUTHOR>
 * @date 2023/04/14
 */
@RestController
@RequestMapping("/api")
@Api(value = "UserConfigController", tags = {"用户配置控制器"})
public class UserConfigController {
    @Autowired
    UserConfigService userConfigService;

    @ApiOperation(value = "获取当前用户配置")
    @GetMapping(value = "/currentaccountconfig")
    public ResponseEntity<ResponseResult> getUserConfigByUserId(){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(userConfigService.findUserConfigByUserId(loginUserId));
    }

    @ApiOperation(value = "根据参数类型获取当前用户配置")
    @GetMapping(value = "/currentaccountconfig", params = "configtype")
    public ResponseEntity<ResponseResult> getUserConfigByUserId(@RequestParam("configtype") Integer configType){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(userConfigService.findUserConfigByUserIdAndType(loginUserId, configType));
    }

    @ApiOperation(value = "批量更新用户配置")
    @PutMapping(value = "/accountconfig")
    public ResponseEntity<ResponseResult> updateUserConfig(@RequestBody List<UserConfig> userConfigList){
        return ResponseHelper.successful(userConfigService.batchUpdate(userConfigList));
    }

    @ApiOperation(value = "是否开启tts语音播报")
    @GetMapping(value = "/enabletts")
    public ResponseEntity<ResponseResult> enableTts(){
        return ResponseHelper.successful(userConfigService.enableTts(TokenUserUtil.getLoginUserId()));
    }
}
