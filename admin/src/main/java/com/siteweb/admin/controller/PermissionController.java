package com.siteweb.admin.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.MenuProfile;
import com.siteweb.admin.entity.Permission;
import com.siteweb.admin.service.MenuProfileService;
import com.siteweb.admin.service.PermissionService;
import com.siteweb.admin.vo.PermissionVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description PermissionController
 * @createTime 2022-01-04 09:18:45
 */
@RestController
@RequestMapping("/api")
@Api(value = "PermissionController", tags = {"权限点操作接口"})
@Slf4j
public class PermissionController {

    @Autowired
    PermissionService permissionService;
    @Autowired
    MenuProfileService menuProfileService;

    /**
     * POST /permissions : Create a new Permission.
     *
     * @param permissionVO the permissionVO to create
     * @return the ResponseEntity with status 200 (OK) and with body the new permissionVO, or
     * with status 400 (Bad Request) if the permissionVO has already an ID
     */
    @ApiOperation(value = "新增Permission实体")
    @ApiOperationSupport(ignoreParameters = {"permissionId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的Permission已被创建",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "PermissionId必须为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "新增Permission报错", content = @Content)
            })
    @PostMapping(value = "/perm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createPermission(
            @Valid @RequestBody PermissionVO permissionVO) {
        if (null != permissionVO.getPermissionId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "PermissionId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = permissionService.createPermission(permissionVO.build());
        if (result > 0) {
            permissionVO.setPermissionId(result);
            return ResponseHelper.successful(permissionVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createPermission error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /permissions : Updates an existing permissionVO.
     *
     * @param permissionVO the permissionVO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated permissionVO, or with
     * status 400 (Bad Request) if the permissionVO is not valid, or with status 500 (Internal
     * Server Error) if the permissionVO couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "修改Permission实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Permission已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的Permission其permissionId为null或输入错误",
                            content = @Content)
            })
    @PutMapping(value = "/perm", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updatePermission(
            @Valid @RequestBody PermissionVO permissionVO) {
        if (null == permissionVO.getPermissionId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "PermissionId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = permissionService.updatePermission(permissionVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "PermissionId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * GET /permissions/:id : get the "id" permission.
     *
     * @param permissionId the id of the permission to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the permission, or with status
     * 404 (Not Found)
     */
    @ApiOperation(value = "根据PermissionId查询Permission实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permissionId", value = "permission Id", required = true, dataType = "Integer", paramType = "path", example = "1")
    })
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据PermissionId查询到的Permission实体",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据PermissionId查询不到Permission实体",
                            content = @Content)
            })
    @GetMapping(value = "/perm/{permissionId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPermissionById(@PathVariable Integer permissionId) {
        Permission permission = permissionService.getPermissionById(permissionId);
        return Optional.ofNullable(permission)
                .map(result -> ResponseHelper.successful(permission))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * DELETE /permissions/:id : delete the "id" permission.
     *
     * @param permissionId the id of the permission to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据PermissionId删除Permission实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Permission实体被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据PermissionId查询不到Permission实体",
                            content = @Content)
            })
    @DeleteMapping(value = "/perm/{permissionId}")
    public ResponseEntity<ResponseResult> deletePermissionById(@PathVariable Integer permissionId) {
        Permission permission = permissionService.getPermissionById(permissionId);
        if (permission == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        permissionService.deletePermission(permissionId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * DELETE /permissions : batch delete permissions.
     *
     * @param permissionIdList delete permissions by permissionIdList
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据PermissionId列表批量删除Permission实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Permission实体被删除",
                            content = {@Content})
            })
    @DeleteMapping(
            value = "/perm",
            params = {"permissionIdList"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeletePermissions(
            @RequestParam List<Integer> permissionIdList) {
        permissionService.deletePermissions(permissionIdList);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * /permissions: get permissions
     *
     * @param category: the category of permission
     * @return: the ResponseEntity with status 200 (OK) of Permission or status with 404 when not
     * found
     */
    @ApiOperation(value = "查询Permission实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "category", value = "permission category", dataType = "Integer", paramType = "query", example = "1")
    })
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Permission实体列表",
                            content = {@Content})
            })
    @GetMapping(
            value = "/perm",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPermissions(@RequestParam(value = "category", required = false) Integer category) {
        return ResponseHelper.successful(permissionService.findPermissionsByCategory(category));
    }

    @GetMapping(value = "/perm/current",params = "category")
    public ResponseEntity<ResponseResult> getCurrentScenePermissionByCategory(Integer category){
        return ResponseHelper.successful(permissionService.findCurrentScenePermissionByCategory(category));
    }

    @ApiOperation(value = "获取权限菜单树")
    @GetMapping(value = "/permmenutree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMenuPermissionTree(@RequestParam(value = "menuProfileId") Integer menuProfileId) {
        return ResponseHelper.successful(permissionService.findMenuPermissionTree(menuProfileId));
    }


    /**
     * /rolepermissions?roleId= get permissions by roleId
     *
     * @param roleId: the roleId of account
     * @return: the ResponseEntity with status 200 (OK) of Permissions
     */
    @ApiOperation(value = "根据roleId查找Permission实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "the roleId of account", required = true, dataType = "Integer", paramType = "query", example = "1")
    })
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Permission实体列表",
                            content = {@Content})
            })
    @GetMapping(
            value = "/roleperm",
            params = "roleId",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPermissionsByRoleId(@RequestParam Integer roleId) {
        List<Permission> permissions = permissionService.getPermissionsByRoleId(roleId);
        return ResponseHelper.successful(permissions);
    }

    /**
     * /syncpermissions synchronize permissions
     *
     * @return:
     */
    @ApiOperation(value = "同步Permission")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "同步Permission成功",
                            content = {@Content})
            })
    @GetMapping(value = "/syncperm")
    public ResponseEntity<ResponseResult> syncMenuPermissions() {
        List<MenuProfile> menuProfiles = menuProfileService.findMenuProfiles();
        for (MenuProfile menuProfile : menuProfiles) {
            permissionService.syncMenuPermissions(menuProfile.getMenuProfileId());
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
