package com.siteweb.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.MenuProfile;
import com.siteweb.admin.entity.SceneMenuProfileMap;
import com.siteweb.admin.service.MenuProfileService;
import com.siteweb.admin.service.SceneMenuProfileMapService;
import com.siteweb.admin.vo.MenuProfileVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * MenuProfile info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-30 17:00:36
 */
@RestController
@RequestMapping("/api")
@Api(value = "MenuProfileController", tags = {"菜单方案操作接口"})
public class MenuProfileController {

    @Autowired
    MenuProfileService menuProfileService;
    @Autowired
    SceneMenuProfileMapService sceneMenuProfileMapService;

    /**
     * GET /menuprofiles : get the MenuProfiles.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of MenuProfiles in body
     */
    @ApiOperation(value = "获取所有MenuProfile实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有MenuProfile实体",
                            content = {@Content})
            })
    @GetMapping("/menuprofiles")
    public ResponseEntity<ResponseResult> getMenuProfiles() {
        return ResponseHelper.successful(menuProfileService.findMenuProfiles());
    }

    @ApiOperation(value = "根据场景获取MenuProfile实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有MenuProfile实体",
                            content = {@Content})
            })
    @GetMapping(value = "/menuprofiles",params = {"sceneId"})
    public ResponseEntity<ResponseResult> getMenuProfilesBySceneId(Integer sceneId) {
        return ResponseHelper.successful(menuProfileService.findMenuProfileBySceneId(sceneId));
    }

    /**
     * GET /menuprofiles/:id get the MenuProfile by id.
     *
     * @param menuProfileId the MenuProfileId
     * @return the ResponseEntity with status 200 (OK) and with body the MenuProfile, or with status
     * 404 (Not Found)
     */
    @ApiOperation(value = "根据MenuProfileId查询MenuProfile实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据MenuProfileId查询到的MenuProfile实体",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据MenuProfileId查询不到MenuProfile实体",
                            content = @Content)
            })
    @GetMapping("/menuprofiles/{menuProfileId}")
    public ResponseEntity<ResponseResult> getMenuProfileById(
            @PathVariable("menuProfileId") Integer menuProfileId) {
        MenuProfile menuProfile = menuProfileService.findById(menuProfileId);
        SceneMenuProfileMap menuProfileMap = sceneMenuProfileMapService.findByMenuProfileId(menuProfileId);
        MenuProfileVO menuProfileVO = BeanUtil.toBean(menuProfile, MenuProfileVO.class);
        menuProfileVO.setScene(menuProfileMap.getSceneId());
        return Optional.ofNullable(menuProfile)
                .map(result -> ResponseHelper.successful(menuProfileVO, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/menuprofiles/select/{menuProfileId}")
    public ResponseEntity<Boolean> setCurrentMenuProfile(@PathVariable("menuProfileId" ) Integer menuProfileId) {
        Boolean ret = menuProfileService.setCurrentMenuProfile(menuProfileId);
        return Optional.ofNullable(ret)
                       .map(result -> new ResponseEntity<>(
                               result,
                               HttpStatus.OK))
                       .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /menuprofiles : create a new MenuProfile
     *
     * @param menuProfileVO the MenuProfile to create
     * @return the ResponseEntity with status 201 (Created) and with body the new MenuProfile, or with
     * status 400 (Bad Request) if the MenuProfile has already an ID
     */
    @ApiOperation(value = "新增MenuProfile实体")
    @ApiOperationSupport(ignoreParameters = {"menuProfileId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的MenuProfile已被创建",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "新增MenuProfile报错", content = @Content)
            })
    @PostMapping(value = "/menuprofiles")
    public ResponseEntity<ResponseResult> createMenuProfile(
            @Valid @RequestBody MenuProfileVO menuProfileVO) {
        if (menuProfileVO.getMenuProfileId() != null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "MenuProfileId should be null",
                    HttpStatus.BAD_REQUEST);
        }

        Integer result = menuProfileService.createMenuProfile(menuProfileVO);
        if (result > 0) {
            menuProfileVO.setMenuProfileId(result);
            return ResponseHelper.successful(menuProfileVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createMenuProfile error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /menuprofiles : Updates an existing MenuProfile.
     *
     * @param menuProfileVO the MenuProfile to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated MenuProfile, or with
     * status 400 if the menuProfileId is not exists or update error
     */
    @ApiOperation(value = "修改MenuProfile实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "MenuProfile已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的MenuProfile其menuProfileId为null或输入错误",
                            content = @Content)
            })
    @PutMapping(value = "/menuprofiles")
    public ResponseEntity<ResponseResult> updateMenuProfile(
            @Valid @RequestBody MenuProfileVO menuProfileVO) {

        if (menuProfileVO.getMenuProfileId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "MenuProfileId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = menuProfileService.updateMenuProfile(menuProfileVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "MenuProfileId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * DELETE /menuprofiles/:id : delete the MenuProfile by id.
     *
     * @param id the id of the MenuProfile to delete
     * @return the ResponseEntity with status 200 (OK) or status 404 (when not found)
     */
    @ApiOperation(value = "根据MenuProfileId删除MenuProfile实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "MenuProfile实体被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据MenuProfileId查询不到MenuProfile实体",
                            content = @Content)
            })
    @DeleteMapping(value = "/menuprofiles/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteMenuProfile(@PathVariable Integer id) {
        MenuProfile menuProfile = menuProfileService.findById(id);
        if (menuProfile == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        if (Boolean.TRUE.equals(menuProfile.getChecked())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "being used menuProfile not allow delete", HttpStatus.BAD_REQUEST);
        }
        menuProfileService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
