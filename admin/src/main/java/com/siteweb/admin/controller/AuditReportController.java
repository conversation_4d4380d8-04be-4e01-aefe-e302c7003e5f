package com.siteweb.admin.controller;

import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.IpUtil;
import com.siteweb.utility.vo.AuditReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api")
@Api(value = "AuditReportController", tags = {"审计日志记录配置接口"})
public class AuditReportController {
    @Autowired
    private SecurityAuditManager securityAuditManager;
    @ApiOperation(value = "添加审计记录")
    @PostMapping(value = "/audit", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAudit(@RequestBody AuditReportVo auditReportVo, HttpServletRequest request) {
        securityAuditManager.recordAuditReport(auditReportVo.getOperationAccount(), auditReportVo.getLevel(), auditReportVo.getType(), auditReportVo.getDetails(), IpUtil.getIpAddr(request),auditReportVo.getResult());
        return ResponseHelper.successful();
    }
}
