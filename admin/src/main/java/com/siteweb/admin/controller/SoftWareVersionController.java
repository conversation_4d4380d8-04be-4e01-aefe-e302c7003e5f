package com.siteweb.admin.controller;

import com.siteweb.admin.service.SoftWareVersionService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 版本信息
 *
 * @Author: lzy
 * @Date: 2022/4/24 10:30
 */
@RestController
@RequestMapping("/api")
public class SoftWareVersionController {

    @Autowired
    private SoftWareVersionService softWareVersionService;

    @GetMapping("/softwareversions")
    public ResponseEntity<ResponseResult> findSoftWareVersion() {
        return ResponseHelper.successful(softWareVersionService.findSoftWareVersion());
    }

}
