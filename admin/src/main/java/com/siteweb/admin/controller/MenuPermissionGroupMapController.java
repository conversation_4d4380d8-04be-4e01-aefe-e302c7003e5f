package com.siteweb.admin.controller;


import com.siteweb.admin.dto.MenuPermissionGroupMapCreateDTO;
import com.siteweb.admin.service.MenuPermissionGroupMapService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "MenuPermissionGroupMapController", tags = {"菜单权限分组映射操作接口"})
public class MenuPermissionGroupMapController {
    @Autowired
    MenuPermissionGroupMapService menuPermissionGroupMapService;

    @GetMapping(value = "/menupermissiongroupmap",params = {"groupId"})
    public ResponseEntity<ResponseResult> findByGroupId(Integer groupId){
        return ResponseHelper.successful(menuPermissionGroupMapService.findByGroupId(groupId));
    }

    @PostMapping(value = "/menupermissiongroupmap")
    public ResponseEntity<ResponseResult> create(@RequestBody MenuPermissionGroupMapCreateDTO menuPermissionGroupMapCreateDTO){
        return ResponseHelper.successful(menuPermissionGroupMapService.batchCreate(menuPermissionGroupMapCreateDTO));
    }
}
