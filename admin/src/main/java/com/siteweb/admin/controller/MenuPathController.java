package com.siteweb.admin.controller;

import com.siteweb.admin.service.MenuPathService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "MenuPathController", tags = {"菜单路径操作接口"})
public class MenuPathController {

    @Autowired
    MenuPathService menuPermissionService;

    /**
     * GET /menupaths/:userId : get the menu paths by userId.
     *
     * @param userId the userId
     * @return the ResponseEntity with status 200 (OK) and with body of the menu paths
     */
    @Operation(summary = "根据userId查询其菜单路由")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "根据userId查询其菜单路由")})
    @GetMapping(value = "/menupaths", params = "userId")
    public ResponseEntity<ResponseResult> getCurrentMenuPathsByAccountId(Integer userId) {
        return ResponseHelper.successful(menuPermissionService.getCurrentMenuPathsByUserId(userId));
    }
}
