package com.siteweb.admin.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.dto.SpecialtyDTO;
import com.siteweb.admin.entity.SpecialtyGroupMap;
import com.siteweb.admin.service.SpecialtyGroupMapService;
import com.siteweb.admin.vo.SpecialtyGroupMapVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupMapController
 * @createTime 2022-01-18 17:00:03
 */
@RestController
@RequestMapping("/api")
@Api(value = "SpecialtyGroupMapController", tags = {"专业权限Map操作接口"})
public class SpecialtyGroupMapController {

    @Autowired
    SpecialtyGroupMapService specialtyGroupMapService;

    /**
     * POST /specialtygroupmaps : batch save SpecialtyGroupMapVO list.
     *
     * @param specialtyGroupMapVOS the specialtyGroupMapVOS to save
     * @return the ResponseEntity with status 200 (Created) and with body of the saved
     * specialtyGroupMapVOS, or with status 400 (Bad Request) if saved error
     */
    @ApiOperation(value = "新增SpecialtyGroupMap实体")
    @ApiOperationSupport(ignoreParameters = {"id"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的SpecialtyGroupMap实体已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "批量保存SpecialtyGroupMap实体报错了",
                            content = @Content)
            })
    @PostMapping(value = "/specialtygroupmaps", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveSpecialtyGroupMaps(
            @Valid @RequestBody List<SpecialtyGroupMapVO> specialtyGroupMapVOS) {
        if (null == specialtyGroupMapVOS || specialtyGroupMapVOS.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "specialtyGroupMapVOS is empty",
                    HttpStatus.BAD_REQUEST);
        }
        List<SpecialtyGroupMap> specialtyGroupMaps = new ArrayList<>();
        for (SpecialtyGroupMapVO specialtyGroupMapVO : specialtyGroupMapVOS) {
            SpecialtyGroupMap specialtyGroupMap = new SpecialtyGroupMap();
            BeanUtils.copyProperties(specialtyGroupMapVO, specialtyGroupMap);
            specialtyGroupMaps.add(specialtyGroupMap);
        }
        Integer result = specialtyGroupMapService.saveSpecialtyGroupMaps(specialtyGroupMaps);
        if (result > 0) {
            return ResponseHelper.successful(specialtyGroupMaps);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.PRIMARY_KEY_ASSIGN_ERROR.value()),
                    "Batch save specialtyGroupMapVO list error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * GET /specialtygroupmaps/:specialtyGroupId get the SpecialtyGroupMap list by specialtyGroupId.
     *
     * @param specialtyGroupId the SpecialtyGroupId
     * @return the ResponseEntity with status 200 (OK) and with body of the SpecialtyGroupMap list
     */
    @ApiOperation(value = "根据SpecialtyGroupId查询SpecialtyGroupMap实体列表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据SpecialtyGroupId查询到的SpecialtyGroupMap实体列表",
                            content = {@Content})
            })
    @GetMapping("/specialtygroupmaps/{specialtyGroupId}")
    public ResponseEntity<ResponseResult> getSpecialtyGroupMapsById(
            @PathVariable("specialtyGroupId")
            @ApiParam(name = "specialtyGroupId", value = "专业权限组ID", required = true)
                    Integer specialtyGroupId) {
        List<SpecialtyGroupMap> specialtyGroupMaps =
                specialtyGroupMapService.findBySpecialtyGroupId(specialtyGroupId);
        return ResponseHelper.successful(specialtyGroupMaps, HttpStatus.OK);
    }

    /**
     * DELETE /specialtygroupmaps/:specialtyGroupId : delete the SpecialtyGroupMap list by
     * specialtyGroupId.
     *
     * @param specialtyGroupId the specialtyGroupId of the SpecialtyGroupMap list to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据SpecialtyGroupId删除SpecialtyGroupMap实体列表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "SpecialtyGroupMap实体列表被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据SpecialtyGroupId查询不到SpecialtyGroupMap实体",
                            content = @Content)
            })
    @DeleteMapping(
            value = "/specialtygroupmaps/{specialtyGroupId}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteSpecialtyGroupMapsById(
            @PathVariable @ApiParam(name = "specialtyGroupId", value = "专业权限组ID", required = true)
                    Integer specialtyGroupId) {
        List<SpecialtyGroupMap> specialtyGroupMaps =
                specialtyGroupMapService.findBySpecialtyGroupId(specialtyGroupId);
        if (specialtyGroupMaps == null || specialtyGroupMaps.isEmpty()) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        specialtyGroupMapService.deleteBySpecialtyGroupId(specialtyGroupId);
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @Operation(summary = "根据SpecialtyGroupMapId集合删除SpecialtyGroupMap实体列表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "专业权限实体被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "未传入集合id",
                            content = @Content)
            })
    @DeleteMapping(value = "/specialtygroups")
    public ResponseEntity<ResponseResult> deleteSpecialtyGroupById(
            @ApiParam(name = "specialtyGroupMapIds", value = "专业权限ID集合", required = true) @RequestParam
                    List<Integer> specialtyGroupMapIds) {
        if (CollectionUtil.isEmpty(specialtyGroupMapIds)) {
            return ResponseHelper.successful(HttpStatus.BAD_REQUEST);
        }
        specialtyGroupMapService.deleteByIds(specialtyGroupMapIds);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @Operation(summary = "查询所有的专业项")
    @GetMapping("/specialtygroupmaps/specialty")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "查询所有专业项",
                            content = {@Content})
            })
    public ResponseEntity<ResponseResult> getSpecialty() {
        List<SpecialtyDTO> specialtyDTOS = specialtyGroupMapService.getSpecialty();
        return ResponseHelper.successful(specialtyDTOS, HttpStatus.OK);
    }
}
