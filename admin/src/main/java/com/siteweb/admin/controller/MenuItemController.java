package com.siteweb.admin.controller;

import cn.hutool.core.collection.CollUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.dto.MenuItemDTO;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.language.LanguageUtil;
import com.siteweb.admin.service.MenuItemService;
import com.siteweb.admin.vo.MenuItemVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

/**
 * MenuItem info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-20 17:02:39
 */
@RestController
@RequestMapping("/api")
@Api(value = "MenuItemController", tags = {"菜单项操作接口"})
public class MenuItemController {

    @Autowired
    MenuItemService menuItemService;

    /**
     * GET /menuitems : get the MenuItems.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of MenuItems in body
     */
    @ApiOperation(value = "获取所有MenuItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有MenuItem实体",
                            content = {@Content})
            })
    @GetMapping("/menuitems")
    public ResponseEntity<ResponseResult> getMenuItems() {
        return ResponseHelper.successful(menuItemService.findMenuItemsWithChildren(), HttpStatus.OK);
    }

    @GetMapping(value="/menuitems/firstlevel", params = "menuProfileId")
    public ResponseEntity<ResponseResult> getAllFirstLevelMenuItems(HttpServletRequest request, Integer menuProfileId) {
        List<MenuItemDTO> allFirstLevelMenuItems = menuItemService.getAllFirstLevelMenuItems(menuProfileId);
        LanguageUtil.menuLanguageBaseSwitch(allFirstLevelMenuItems,request.getHeader(LanguageUtil.LANGUAGE_HEADER));
        return ResponseHelper.successful(allFirstLevelMenuItems);
    }

    /**
     * GET /menuitems/:id get the MenuItem by id.
     *
     * @param menuItemId the MenuItemId
     * @return the ResponseEntity with status 200 (OK) and with body the MenuItem, or with status 404
     * (Not Found)
     */
    @ApiOperation(value = "根据MenuItemId查询MenuItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据MenuItemId查询到的MenuItem实体",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据MenuItemId查询不到MenuItem实体",
                            content = @Content)
            })
    @GetMapping("/menuitems/{menuItemId}")
    public ResponseEntity<ResponseResult> getMenuItemById(
            @PathVariable("menuItemId") Integer menuItemId) {
        MenuItem menuItem = menuItemService.findById(menuItemId);
        return Optional.ofNullable(menuItem)
                .map(result -> ResponseHelper.successful(result, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /menuitems : create a new MenuItem
     *
     * @param menuItemVO the MenuItem to create
     * @return the ResponseEntity with status 201 (Created) and with body the new MenuItem, or with
     * status 400 (Bad Request) if the MenuItem has already an ID
     */
    @ApiOperation(value = "新增MenuItem实体")
    @ApiOperationSupport(ignoreParameters = {"menuItemId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的MenuItem已被创建",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "新增MenuItem报错", content = @Content)
            })
    @PostMapping(value = "/menuitems")
    public ResponseEntity<ResponseResult> createMenuItem(@RequestBody MenuItemVO menuItemVO) {
        int result = menuItemService.createMenuItem(menuItemVO);
        if (result > 0) {
            menuItemVO.setMenuItemId(result);
            return ResponseHelper.successful(menuItemVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createMenuItem error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /menuitems : Updates an existing MenuItem.
     *
     * @param menuItemVOList the MenuItem to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated MenuItem, or with
     * status 404 (Not Found) if the menuItemId is not exists,
     */
    @ApiOperation(value = "修改MenuItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "MenuItem已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的MenuItem其menuItemId为null或输入错误",
                            content = @Content)
            })
    @PutMapping(value = "/menuitems")
    public ResponseEntity<ResponseResult> updateMenuItem(@RequestBody List<MenuItemVO> menuItemVOList) {
        if (CollUtil.isEmpty(menuItemVOList)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "menuItemVOList is empty", HttpStatus.BAD_REQUEST);
        }
        Integer result = menuItemService.batchUpdateMenuItem(menuItemVOList);
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "MenuItemId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation(value = "修改MenuItem的排序")
    @PutMapping(value = "/menuitems/sort")
    public ResponseEntity<ResponseResult> updateMenuStructureSortIndex(@RequestBody List<MenuItemVO> menuItemVOList){
        return ResponseHelper.successful(menuItemService.updateMenuItemSort(menuItemVOList));
    }

    /**
     * DELETE /menuitems/:id : delete the MenuItem by id.
     *
     * @param id the id of the MenuItem to delete
     * @return the ResponseEntity with status 200 (OK) or status 404 (when not found)
     */
    @ApiOperation(value = "根据MenuItemId删除MenuItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "MenuItem实体被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据MenuItemId查询不到MenuItem实体",
                            content = @Content)
            })
    @DeleteMapping(value = "/menuitems/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteMenuItem(@PathVariable Integer id) {
        MenuItem menuItem = menuItemService.findById(id);
        if (menuItem == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        menuItemService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation("通过菜单id获取菜单全路径")
    @GetMapping(value = "/menuitempath/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMenuItemAllPath(@PathVariable Integer id) {
        return ResponseHelper.successful(menuItemService.findMenuItemAllPath(id));
    }
}
