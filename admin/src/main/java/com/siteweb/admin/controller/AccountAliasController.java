package com.siteweb.admin.controller;

import com.siteweb.admin.entity.AccountAlias;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AccountAliasService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "AccountAliasController", tags = {"用户别名操作接口"})
public class AccountAliasController {

    @Autowired
    AccountAliasService accountAliasService;

    @ApiOperation(value = "查询当前用户的别名列表")
    @GetMapping("/accountalias")
    public ResponseEntity<ResponseResult> findAllByUserId() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        List<AccountAlias> accountAliases = accountAliasService.findAllByUserId(loginUserId);
        return ResponseHelper.successful(accountAliases, HttpStatus.OK);
    }

    @ApiOperation(value = "新增用户别名")
    @PostMapping(value = "/accountalias", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAccountAlias(@Valid @RequestBody AccountAlias accountAlias) {
        int result = accountAliasService.createAccountAlias(accountAlias);
        if (result > 0) {
            return ResponseHelper.successful(accountAlias);
        } else if (result == -1) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.ACCOUNT_ALIAS_repeat.value()),
                    ErrorCode.ACCOUNT_ALIAS_repeat.getReasonPhrase(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create accountalias error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "修改用户别名")
    @PutMapping(value = "/accountalias", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAccountalias(@Valid @RequestBody AccountAlias accountAlias) {
        int result = accountAliasService.updateAccountAlias(accountAlias);
        if (result > 0) {
            return ResponseHelper.successful(accountAlias);
        } else if (result == -1) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.ACCOUNT_ALIAS_repeat.value()),
                    ErrorCode.ACCOUNT_ALIAS_repeat.getReasonPhrase(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "update accountalias error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "批量删除用户别名根据用户id")
    @DeleteMapping(value = "/accountalias", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAccountalias(@ApiParam(name = "ids", value = "用户别名Ids", required = true) @RequestParam List<Integer> ids) {
        return ResponseHelper.successful(accountAliasService.batchDeteleAccountAlias(ids));
    }


    @ApiOperation("切换当前用户别名")
    @PutMapping("/accountalias/checked/{accountAliasId}")
    public ResponseEntity<ResponseResult> checkedaccountalias(@ApiParam(name = "accountAliasId", required = true) @PathVariable Integer accountAliasId) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        accountAliasService.checkedAccountAliasByUserId(accountAliasId, loginUserId);
        return ResponseHelper.successful();
    }
}
