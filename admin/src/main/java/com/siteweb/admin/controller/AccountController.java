package com.siteweb.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.dto.NormalAccountDTO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.vo.*;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AccountController
 * @createTime 2022-03-17 15:10:14
 */
@RestController
@RequestMapping("/api")
@Api(value = "AccountController", tags = {"用户账号操作接口"})
public class AccountController {

    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    AccountService accountService;

    /**
     * GET /accounts/:userId get the Account by userId.
     *
     * @param userId the UserId
     * @return the ResponseEntity with status 200 (OK) and with body the Account, or with status 404 (Not
     * Found)
     */
    @ApiOperation(value = "根据UserID查询单个Account实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据UserID查询到的Account实体",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "根据UserID查询不到Account实体", content = @Content)
            })
    @GetMapping("/accounts/{userId}")
    public ResponseEntity<ResponseResult> getAccountByUserId(
            @PathVariable("userId") @ApiParam(name = "userId", value = "用户ID", required = true)
                    Integer userId, Boolean isHidden) {
        AccountDTO accountDTO = accountService.findByUserId(userId);
        if (Boolean.FALSE.equals(isHidden)) {
            return ResponseHelper.successful(BeanUtil.toBean(accountDTO, NormalAccountDTO.class));
        }
        return ResponseHelper.successful(accountDTO);
    }

    @ApiOperation(value = "获取所有用户账号")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Account实体",
                            content = {@Content}),
            })
    @GetMapping(value = "/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAccounts(Boolean isHidden) {
        List<AccountDTO> accountDTOS = accountService.findAllByPermission(TokenUserUtil.getLoginUserId());
        if (Boolean.FALSE.equals(isHidden)) {
            return ResponseHelper.successful(BeanUtil.copyToList(accountDTOS, NormalAccountDTO.class));
        }
        return ResponseHelper.successful(accountDTOS);
    }

    @ApiOperation(value = "通过部门id获取用户账号")
    @GetMapping(value = "/accounts", params = {"departmentId"},produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findByDepartmentId(Integer departmentId,Boolean isHidden){
        List<AccountDTO> accountDTOS = accountService.findByDepartmentId(departmentId);
        if (Boolean.FALSE.equals(isHidden)) {
            return ResponseHelper.successful(BeanUtil.copyToList(accountDTOS, NormalAccountDTO.class));
        }
        return ResponseHelper.successful(accountDTOS);
    }

    @ApiOperation(value = "获取所有用户账号(不脱敏明文)")
    @GetMapping(value = "/accounts/normal", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllNormalAccounts() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        List<AccountDTO> accountDTOS = accountService.findAllByPermission(loginUserId);
        return ResponseHelper.successful(BeanUtil.copyToList(accountDTOS, NormalAccountDTO.class));
    }

    /**
     * POST /accounts : Create a new Account.
     *
     * @param accountVO the accountVO to create
     * @return the ResponseEntity with status 200 (OK) and with body the new accountVO, or with
     * status 400 (Bad Request) if the accountVO has already an ID
     */
    @ApiOperation(value = "新增用户账号")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的用户账号已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "新增用户账号失败",
                            content = {@Content})
            })
    @PostMapping(value = "/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAccount(@Valid @RequestBody AccountVO accountVO) {
        //通过界面新增的用户账号，默认在首次登录成功后必须强制修改密码
        if (accountVO.getNeedResetPwd() == null) {
            accountVO.setNeedResetPwd(true);
        }
        Integer result = accountService.createAccount(TokenUserUtil.getLoginUserId(),accountVO);
        if (result > 0) {
            return ResponseHelper.successful(accountVO.build());
        } else if (result == -3) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.LogonId_repeat.value()),
                    ErrorCode.LogonId_repeat.getReasonPhrase(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create account error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /accounts : Update account.
     *
     * @param accountVO the accountVO to update
     * @return the ResponseEntity with status 200 (Updated) and with the body of the accountVO, or with
     * status 400 (Bad Request) if update error
     */
    @ApiOperation(value = "修改用户账号")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "用户账号修改成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "修改用户账号失败",
                            content = {@Content})
            })
    @PutMapping(value = "/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAccount(@Valid @RequestBody AccountVO accountVO) {
        Integer result = accountService.updateAccount(accountVO);
        if (result > 0) {
            return ResponseHelper.successful(accountVO.build());
        } else if (result == -3) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.LogonId_repeat.value()),
                    ErrorCode.LogonId_repeat.getReasonPhrase(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update account error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "停用或启用用户账号")
    @PutMapping(value = "/accounts/enable", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAccountEnable(@Valid @RequestBody AccountEnableVO accountEnableVO) {
        Integer result = accountService.updateAccountEnable(accountEnableVO);
        if (result > 0) {
            return ResponseHelper.successful();
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update account enable error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /accounts/updatepassword : Updates password of account.
     *
     * @param updatePasswordVO the updatePasswordVO to update
     * @return the ResponseEntity with status 200 (OK) and with body of the status code
     */
    @ApiOperation(value = "修改密码")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "操作成功",
                            content = {@Content})
            })
    @PutMapping(value = "/accounts/updatepassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updatePassword(@Valid @RequestBody UpdatePasswordVO updatePasswordVO) {
        int resultCode = accountService.updateAccountPassword(updatePasswordVO.getUserId(), updatePasswordVO.getOldPassword(), updatePasswordVO.getNewPassword());
        return ResponseHelper.successful(resultCode);
    }

    @ApiOperation(value = "忘记密码")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "操作成功",
                            content = {@Content})
            })
    @PutMapping(value = "/accounts/forgetpassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> forgetPassword(@Valid @RequestBody ForgetPasswordVO forgetPasswordVO) {
        int resultCode = accountService.forgetPassword(forgetPasswordVO);
        return ResponseHelper.successful(resultCode);
    }

    /**
     * PUT /accounts/resetpassword : Reset password of account.
     *
     * @param resetPasswordVO the resetPasswordVO to update
     * @return the ResponseEntity with status 200 (OK) and with body of the status code
     */
    @ApiOperation(value = "重置密码")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "操作成功",
                            content = {@Content})
            })
    @PutMapping(value = "/accounts/resetpassword", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> resetPassword(@Valid @RequestBody ResetPasswordVO resetPasswordVO) {
        int resultCode = accountService.resetAccountPassword(resetPasswordVO.getUserId(), resetPasswordVO.getPassword());
        return ResponseHelper.successful(resultCode);
    }

    /**
     * PUT /accounts/setthemename : set themeName of account.
     *
     * @param accountThemeVO the setAccountThemeVO to update
     * @return the ResponseEntity with status 200 (OK) and with body of the status code
     */
    @ApiOperation(value = "设置用户主题")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "操作成功",
                            content = {@Content})
            })
    @PutMapping(value = "/accounts/setthemename", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> setAccountThemeName(@Valid @RequestBody AccountThemeVO accountThemeVO) {
        int resultCode = accountService.setAccountThemeName(accountThemeVO.getUserId(), accountThemeVO.getThemeName());
        return ResponseHelper.successful(resultCode);
    }

    @ApiOperation("验证用户密码是否正确")
    @PostMapping("/accounts/validpassword")
    public ResponseEntity<ResponseResult> validAccountPassword(@RequestBody ValidPasswordVO validPassword) {
        return ResponseHelper.successful(accountService.validAccountPassword(validPassword.getUserId(), validPassword.getPassword()));
    }

    /**
     * DELETE /accounts/:userId : Delete account by userId.
     *
     * @param userId the userId of the account
     * @return the ResponseEntity with status 200 (OK) or status 404 (when userId not found)
     */
    @ApiOperation(value = "按用户ID删除用户账号")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "用户账号删除成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据UserId找不到对应记录",
                            content = {@Content})
            })
    @DeleteMapping(value = "/accounts/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAccountByUserId(@PathVariable @ApiParam(name = "userId", value = "用户账号ID", required = true) Integer userId) {
        AccountDTO accountDTO = accountService.findByUserId(userId);
        if (null == accountDTO) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        accountService.deleteAccountByUserId(userId);
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @ApiOperation(value = "获取所有在线用户")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Account实体",
                            content = {@Content}),
            })
    @GetMapping(value = "/accounts/online", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findAccountOnline() {
        return ResponseHelper.successful(accountService.findAccountOnline());
    }

    @ApiOperation(value = "强制用户离线")
    @GetMapping(value = "/accounts/offline", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> accountOffline(String token) {
        return ResponseHelper.successful(accountService.accountOffline(token));
    }


    @ApiOperation(value = "同步用户账号")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "同步账号成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "同步账号失败",
                            content = {@Content})
            })
    @PostMapping(value = "/syncaccounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> syncAccounts(@Valid @RequestBody List<AccountVO> accountVOList) {
        Integer result = accountService.batchCreateAccount(TokenUserUtil.getLoginUserId(),accountVOList);
        if (result > 0) {
            return ResponseHelper.successful(accountVOList);
        } else if (result == -3) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.LogonId_repeat.value()),
                    ErrorCode.LogonId_repeat.getReasonPhrase(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create account error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation("根据登录名验证用户密码是否正确")
    @PostMapping("/accounts/validaccountpassword")
    public ResponseEntity<ResponseResult> validAccountPasswordVO(@RequestBody ValidAccountPasswordVO validPassword) {
        return ResponseHelper.successful(accountService.validAccountPasswordVO(validPassword.getUserName(), validPassword.getPassword()));
    }

    @ApiOperation("是否在密码有效期")
    @GetMapping(value = "/accounts/passwordinvalidtime", params = "userName")
    public ResponseEntity<ResponseResult> passwordInValidTime(@RequestParam String userName) {
        return ResponseHelper.successful(accountService.passwordInValidTime(userName));
    }
}
