package com.siteweb.admin.controller;

import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * Creation Date: 2025/5/22
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Api(value = "LogoutController", tags = {"退出登录操作"})
public class LogoutController {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    SystemConfigService systemConfigService;
    private static final String LOGIN_USER_TOKEN = "LoginUserToken";

    @ApiOperation(value = "退出登录接口")
    @PostMapping("/logout")
    public ResponseEntity<ResponseResult> logoutUser(HttpServletRequest request) {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.MAX_CONCURRENT_LOGIN_COUNT.getSystemConfigKey());
        // 只有当最大用户并发数为1时才清除token
        if (systemConfig != null && Objects.equals("1", systemConfig.getSystemConfigValue().trim())) {
            TokenUser user = TokenUserUtil.getUser();
            String loginUserKey = user.getUser().getLogonId() + ":" + user.getUser().getLoginType();
            log.info("User [{}] logout.", loginUserKey);
            redisUtil.hdel(LOGIN_USER_TOKEN, loginUserKey);
        }
        return ResponseHelper.successful("success");
    }

}
