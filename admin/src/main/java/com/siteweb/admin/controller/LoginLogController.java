package com.siteweb.admin.controller;

import com.siteweb.admin.dto.LoginLogDTO;
import com.siteweb.admin.service.LoginLogService;
import com.siteweb.admin.vo.LoginLogVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LoginLogController
 * @createTime 2022-05-11 10:47:16
 */
@RestController
@RequestMapping("/api")
@Api(value = "LoginLogController", tags = {"登录日志操作接口"})
public class LoginLogController {

    @Autowired
    LoginLogService loginLogService;

    /**
     * GET  /loginlogs : get the LoginLogs.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of LoginLogs in body
     */
    @ApiOperation(value = "按时间段查询登录日志")
    @GetMapping("/loginlogs")
    public ResponseEntity<ResponseResult> findLoginLogs(@RequestParam(defaultValue = "1970-01-01 00:00:00", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime, @RequestParam(defaultValue = "2100-01-01 00:00:00", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime, Pageable pageable) {
        Page<LoginLogDTO> result = loginLogService.findByOperatingTimeBetween(startTime, endTime, pageable);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }

    /**
     * Post /loginlogs : create a new LoginLog
     *
     * @param loginLogVO the LoginLog to create
     * @return the ResponseEntity with status 201 (Created) and with body the new LoginLog,
     * or with status 500 (Internal server error) if create error
     */
    @ApiOperation(value = "新增登录日志")
    @PostMapping(value = "/loginlogs")
    public ResponseEntity<ResponseResult> createLoginLog(@Valid @RequestBody LoginLogVO loginLogVO, HttpServletRequest request) {
        int result = loginLogService.createLoginLog(loginLogVO, request);
        if (result > 0) {
            return ResponseHelper.successful(loginLogVO);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create loginLog error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
