package com.siteweb.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.dto.EmployeeDTO;
import com.siteweb.admin.dto.NormalEmployeeDTO;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.admin.vo.EmployeeVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "EmployeeController", tags = {"人员操作接口"})
@Slf4j
public class EmployeeController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    EmployeeService employeeService;

    /**
     * GET /employees/:id get the employee by employeeId.
     *
     * @param id the EmployeeId
     * @return the ResponseEntity with status 200 (OK) and with body of the employee, or with status
     * 404 (Not Found)
     */
    @ApiOperation(value = "根据EmployeeId查询单个Employee实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据EmployeeId查询到的Employee实体",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据EmployeeId查询不到Employee实体",
                            content = @Content)
            })
    @GetMapping(value = "/employees/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEmployeeById(@PathVariable Integer id,Boolean isHidden) {
        Employee employee = employeeService.findByEmployeeId(id);
        if (null == employee) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        if (Boolean.FALSE.equals(isHidden)) {
            return ResponseHelper.successful(BeanUtil.toBean(employee, NormalEmployeeDTO.class));
        }
        //考虑到数据脱敏，故将返回对象由Employee改为EmployeeDTO
        EmployeeDTO employeeDTO = new EmployeeDTO(employee);
        BeanUtils.copyProperties(employee, employeeDTO);
        return ResponseHelper.successful(employeeDTO, HttpStatus.OK);
    }

    /**
     * GET /employees get all employees.
     *
     * @return the ResponseEntity with status 200 (OK) and with body of the employee list
     */
    @ApiOperation(value = "查询Employee实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回查询到的Employee实体",
                            content = {@Content})
            })
    @GetMapping(value = "/employees", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllEmployees(@RequestParam(value = "departmentId", required = false) Integer departmentId,Boolean isHidden) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        List<Employee> employees;
        List<EmployeeDTO> employeeDTOs = new ArrayList<>();
        if (null == departmentId) {
            employees = employeeService.findEmployeeByDepartmentPermission(loginUserId);
        } else {
            employees = employeeService.findEmployeesByDepartmentIds(List.of(departmentId));
        }
        if (Boolean.FALSE.equals(isHidden)) {
            return ResponseHelper.successful(BeanUtil.copyToList(employees, NormalEmployeeDTO.class));
        }
        //考虑到数据脱敏，故将返回对象由Employee改为EmployeeDTO
        if (null != employees) {
            for (Employee employee : employees) {
                EmployeeDTO employeeDTO = new EmployeeDTO(employee);
                employeeDTOs.add(employeeDTO);
            }
        }
        return ResponseHelper.successful(employeeDTOs, HttpStatus.OK);
    }

    /**
     * POST /employees : Create a new Employee.
     *
     * @param employeeVO the employeeVO to create
     * @return the ResponseEntity with status 200 (OK) and with body of the new employeeVO, or with
     * status 400 (Bad Request) if the employeeVO has already an employeeId
     */
    @ApiOperation(value = "新增Employee")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的Employee已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "新增Employee失败",
                            content = {@Content})
            })
    @PostMapping(value = "/employees", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEmployee(@Valid @RequestBody EmployeeVO employeeVO) {
        Integer result = employeeService.createEmployee(TokenUserUtil.getLoginUserId(),employeeVO);
        if (result > 0) {
            return ResponseHelper.successful(employeeVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create employee error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /employees : Update employee.
     *
     * @param employeeVO the employeeVO to update
     * @return the ResponseEntity with status 200 (OK) and with body of the employeeVO, or with
     * status 400 (Bad Request) if update error
     */
    @ApiOperation(value = "修改Employee")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Employee修改成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "修改Employee失败",
                            content = {@Content})
            })
    @PutMapping(value = "/employees", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEmployee(@Valid @RequestBody EmployeeVO employeeVO) {
        Integer result = employeeService.updateEmployee(employeeVO);
        if (result > 0) {
            return ResponseHelper.successful(employeeVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update employee error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * DELETE /employees/:employeeId : Delete employee by employeeId.
     *
     * @param employeeId the employeeId of the employee
     * @return the ResponseEntity with status 200 (OK) or status 404 (when employeeId not found)
     */
    @ApiOperation(value = "按EmployeeId删除人员")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Employee删除成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据EmployeeId找不到对应记录",
                            content = {@Content})
            })
    @DeleteMapping(value = "/employees/{employeeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEmployeeById(@PathVariable @ApiParam(name = "employeeId", value = "人员ID", required = true) Integer employeeId) {
        int result = employeeService.deleteEmployeeById(employeeId);
        if (result < 0) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @GetMapping(value = "/employees/cardstatus")
    public ResponseEntity<ResponseResult> GetCardByEmployeeId(@RequestParam(value = "employeeId") Integer employeeId) {
        return employeeService.findCardCountByEmployeeId(employeeId) > 0 ? ResponseHelper.successful(Boolean.TRUE) : ResponseHelper.successful(Boolean.FALSE);
    }

    @ApiOperation(value = "根据部门ID导出人员列表excel")
    @GetMapping(value = "/employees/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<Resource> exportEmployees(@RequestParam(value = "departmentId") Integer departmentId,
                                                    @RequestParam(value = "isHidden") Boolean isHidden) {
        String dateFormat = "Vertiv_" + DateUtil.format(new Date(), "yyyy-MM-dd_HH:mm:ss");
        ExcelWriter writer = employeeService.exportEmployee(departmentId, isHidden, dateFormat);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {

            writer.flush(bos, true);
            Resource resource = new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + dateFormat + ".xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")).body(resource);
        }catch (Exception e) {
            log.error("employees export xlsx err.[err:{}]", e.getMessage());
        } finally {
            writer.close();
            IoUtil.close(bos);
        }
        return null;
    }

    @ApiOperation(value = "全量同步Employee，入参有EmployeeId")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "全量同步Employee成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "全量同步Employee失败",
                            content = {@Content})
            })
    @PostMapping(value = "/syncemployees", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> syncEmployees(@Valid @RequestBody List<EmployeeVO> employeeVOList) {
        List<Employee> employeeList = employeeVOList.stream().map(EmployeeVO::build).toList();
        Integer result = employeeService.batchCreateEmployee(TokenUserUtil.getLoginUserId(),employeeList);
        if (result > 0) {
            return ResponseHelper.successful(employeeList);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create employee error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "根据description修改Employee")//中煤用户唯一标识code存在description中
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Employee修改成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "修改Employee失败",
                            content = {@Content})
            })
    @PutMapping(value = "/employeesbydescription", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateEmployeeByDescription(@Valid @RequestBody EmployeeVO employeeVO) {
        int result = employeeService.updateEmployeeByDescription(employeeVO);
        if (result > 0) {
            return ResponseHelper.successful(result);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update employee error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @ApiOperation(value = "按description批量删除人员")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Employee批量删除成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Employee批量删除失败",
                            content = {@Content})
            })
    @DeleteMapping(value = "/employeesbydescription", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEmployeeByDescription(@Valid @RequestBody List<String> descriptionList) {
        int result = employeeService.deleteEmployeesByDescription(descriptionList);
        if (result <= 0) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(result);
    }

    @ApiOperation("通过员工id更新员工名称")
    @PutMapping("/employees/name")
    public ResponseEntity<ResponseResult> updateEmployeeNameByEmployeeId(@RequestBody EmployeeVO employeeVO) {
        return ResponseHelper.successful(employeeService.updateNameById(employeeVO.getEmployeeId(),employeeVO.getEmployeeName()));
    }
}
