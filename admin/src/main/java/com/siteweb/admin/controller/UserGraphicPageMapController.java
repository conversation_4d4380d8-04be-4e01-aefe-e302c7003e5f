package com.siteweb.admin.controller;

import com.siteweb.admin.entity.UserGraphicPageMap;
import com.siteweb.admin.service.UserGraphicPageMapService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "UserGraphicPageMapController", tags = {"用户组态绑定接口"})
public class UserGraphicPageMapController {
    @Autowired
    UserGraphicPageMapService userGraphicPageMapService;

    @Operation(summary = "获取所有用户组态映射关系信息")
    @GetMapping(value = "/accountgraphicpagemap")
    public ResponseEntity<ResponseResult> getAll(){

        return ResponseHelper.successful(userGraphicPageMapService.findAll());
    }

    @Operation(summary = "通过id获取用户组态映射关系信息")
    @GetMapping(value = "/accountgraphicpagemap/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(userGraphicPageMapService.findById(id));
    }

    @Operation(summary = "创建用户组态映射关系")
    @PostMapping(value = "/accountgraphicpagemap")
    public ResponseEntity<ResponseResult> create(@RequestBody UserGraphicPageMap userGraphicPageMap){
        return ResponseHelper.successful(userGraphicPageMapService.create(userGraphicPageMap));
    }

    @Operation(summary = "更新用户组态映射关系")
    @PutMapping(value = "/accountgraphicpagemap")
    public ResponseEntity<ResponseResult> update(@RequestBody UserGraphicPageMap userGraphicPageMap){
        return ResponseHelper.successful(userGraphicPageMapService.createOrUpdate(userGraphicPageMap));
    }

    @Operation(summary = "删除用户组态映射关系")
    @DeleteMapping(value = "/accountgraphicpagemap/{id}")
    public ResponseEntity<ResponseResult> update(@PathVariable("id") Integer id){
        return ResponseHelper.successful(userGraphicPageMapService.deleteById(id));
    }
}
