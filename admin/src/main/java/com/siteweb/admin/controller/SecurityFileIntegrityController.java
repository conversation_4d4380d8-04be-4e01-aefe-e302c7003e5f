package com.siteweb.admin.controller;

import com.siteweb.admin.service.SecurityFileIntegrityService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "SecurityFileIntegrityController", tags = {"安全认证文件自校验接口"})
public class SecurityFileIntegrityController {

    @Autowired
    SecurityFileIntegrityService securityFileIntegrityService;

    @ApiOperation(value = "安全认证文件自校验状态")
    @GetMapping(value = "/fileintergrity/status")
    public ResponseEntity<ResponseResult> getFileIntergrityStatus() {
        return ResponseHelper.successful(securityFileIntegrityService.fileIntergrityStatus());
    }
}
