package com.siteweb.admin.controller;

import com.siteweb.admin.service.SceneService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


/**
 * @author: Habits
 * @time: 2022/5/17 10:32
 * @description:
 **/
@RestController
@RequestMapping("/api")
@Api(value = "SceneController", tags = {"场景操作接口"})
@Slf4j
public class SceneController {

    @Autowired
    SceneService sceneService;

    @ApiOperation("获取所有场景")
    @GetMapping(value = "/scenes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findScenes() {
        return ResponseHelper.successful(sceneService.findScenes());
    }

    @ApiOperation("获取当前场景")
    @GetMapping(value = "/scenes/current", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findCurrentScenes() {
        return ResponseHelper.successful(sceneService.currentScene());
    }

    @ApiOperation("切换当前场景")
    @PutMapping("/scenes/checked")
    public ResponseEntity<ResponseResult> selectScene(@ApiParam(name = "sceneId", required = true) @RequestParam Integer sceneId) {
        //切换场景功能取消
        log.error("do not allow switching scene");
        //sceneService.checkedScene(sceneId);
        return ResponseHelper.failed("do not allow switching scene");
    }

}
