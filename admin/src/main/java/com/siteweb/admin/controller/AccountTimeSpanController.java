package com.siteweb.admin.controller;

import com.siteweb.admin.entity.AccountTimeSpan;
import com.siteweb.admin.service.AccountTimeSpanService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api")
@Api(value = "AccountTimeSpanController", tags = {"用户访问时间段配置接口"})
public class AccountTimeSpanController {
    @Autowired
    AccountTimeSpanService accountTimeSpanService;

    @ApiOperation(value = "查询AccountTimeSpan实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回查询到的AccountTimeSpan实体",
                            content = {@Content})
            })
    @GetMapping(value = "/accounttimespans", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findAccountTimeSpans() {
        return ResponseHelper.successful(accountTimeSpanService.findAccountTimeSpans(), HttpStatus.OK);
    }

    @ApiOperation(value = "查询未配置访问时间段用户")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回查询到的Account实体",
                            content = {@Content})
            })
    @GetMapping(value = "/accounttimespans/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findNotConfigAcount() {
        return ResponseHelper.successful(accountTimeSpanService.findNotConfigAcount(), HttpStatus.OK);
    }

    @ApiOperation(value = "新增AccountTimeSpan")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的AccountTimeSpan已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "新增AccountTimeSpan失败",
                            content = {@Content})
            })
    @PostMapping(value = "/accounttimespans", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAccountTimeSpan(@Valid @RequestBody AccountTimeSpan AccountTimeSpan) {
        Integer result = accountTimeSpanService.createAccountTimeSpan(AccountTimeSpan);
        if (result > 0) {
            return ResponseHelper.successful(AccountTimeSpan);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create AccountTimeSpan error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @ApiOperation(value = "修改AccountTimeSpan")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "AccountTimeSpan已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的AccountTimeSpan其AccountTimeSpanId为null或输入错误",
                            content = @Content)
            })
    @PutMapping(value = "/accounttimespans")
    public ResponseEntity<ResponseResult> updateAccountTimeSpan(@Valid @RequestBody AccountTimeSpan accountTimeSpan) {
        if (accountTimeSpan.getAccountTimeSpanId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "AccountTimeSpanId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = accountTimeSpanService.updateAccountTimeSpan(accountTimeSpan);
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "AccountTimeSpanId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation(value = "按主键ID删除AccountTimeSpan")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据主键Id找不到对应记录",
                            content = {@Content})
            })
    @DeleteMapping(value = "/accounttimespans/{accountTimeSpanId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAccountTimeSpanById(@PathVariable @ApiParam(name = "accountTimeSpanId", value = "主键ID", required = true) Integer accountTimeSpanId) {
        AccountTimeSpan accountTimeSpan = accountTimeSpanService.findAccountTimeSpanById(accountTimeSpanId);
        if (null == accountTimeSpan) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        accountTimeSpanService.deleteAccountTimeSpanById(accountTimeSpanId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

}
