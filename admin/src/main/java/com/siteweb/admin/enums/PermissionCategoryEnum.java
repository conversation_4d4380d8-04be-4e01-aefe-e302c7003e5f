package com.siteweb.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定义权限类别的枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PermissionCategoryEnum {
    /**
     * 用户操作权限
     */
    OPERATION(2, "OperationPermission"),
    /**
     * 报表权限
     */
    REPORT(5, "ReportPermission"),
    /**
     * 菜单权限
     */
    MENU(6, "MenuPermission"),
    /**
     * 专业权限
     */
    MAJOR(8, "MajorPermission"),
    /**
     * 片区权限
     */
    AREA(9, "AreaPermission"),
    /**
     * 区域权限
     */
    REGION(10, "RegionPermission"),
    /**
     * 部门权限
     */
    DEPARTMENT(11, "DepartmentPermission");

    private final Integer permissionCategoryId;

    private final String permissionCategoryName;
    private static final Map<Integer,String> categoryNameMap = Arrays.stream(values()).collect(Collectors.toMap(PermissionCategoryEnum::getPermissionCategoryId,PermissionCategoryEnum::getPermissionCategoryName));
    public static String findNameByCategory(Integer categoryId){
        return categoryNameMap.get(categoryId);
    }
}
