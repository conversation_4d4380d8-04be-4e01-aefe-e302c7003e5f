package com.siteweb.admin.enums;

public enum SceneEnum {
    // IDC
    IDC(1),
    // 网点
    NETS(2),
    REBOT(3);

    private Integer value;

    SceneEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static SceneEnum getSceneEnum(Integer type) {
        for (SceneEnum sceneEnum : values()) {
            if (type.equals(sceneEnum.getValue())) {
                return sceneEnum;
            }
        }
        return null;
    }
}
