package com.siteweb.admin.enums;

/**
 * 安全日志报表类型枚举
 * <AUTHOR>
 * @date 2022/11/06
 */
public enum SecurityReportTypeEnum {
    IDENTITY_AUTHENTICATION(1, "身份用户鉴别"),
    ATTACK_DETECTION(2, "攻击检测"),
    BRUTE_FORCE(3, "暴力破解"),
    INTEGRITY_TEST(4, "完整性检测");


    SecurityReportTypeEnum(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    /**
     * 级别 1.身份用户鉴别  2.攻击检测  3.暴力破解  4.完整性检测
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String describe;

    public Integer getType() {
        return this.type;
    }

    public String getDescribe() {
        return this.describe;
    }
}
