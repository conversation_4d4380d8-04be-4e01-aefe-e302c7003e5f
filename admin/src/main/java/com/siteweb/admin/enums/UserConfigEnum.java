package com.siteweb.admin.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum UserConfigEnum {
    ENABLE_TTS("enableTts", 1, "是否开启tts语音播报"),
    THEME("theme", 2, "用户自定义主题");
    /**
     * 用户配置键
     */
    private final String configKey;
    /**
     * 配置类型 1 TTS
     */
    private final Integer configType;
    /**
     * 用户配置键的含义
     */
    private final String configKeyDescribe;

    /**
     * 获取所有用户可配置的键
     *
     * @return {@link List}<{@link String}>
     */
    public static Set<String> getAllKey() {
        return Arrays.stream(values())
                     .map(v -> v.configKey)
                     .collect(Collectors.toSet());
    }

    /**
     * 获取可配置类型的键
     *
     * @return {@link List}<{@link String}>
     */
    public static Set<String> getKeyByType(Integer configType) {
        return Arrays.stream(values())
                     .filter(v -> Objects.equals(v.configType, configType))
                     .map(v -> v.configKey)
                     .collect(Collectors.toSet());
    }

    public static UserConfigEnum getByConfigKey(String configKey) {
        return Arrays.stream(values())
                     .filter(e -> e.configKey.equals(configKey))
                     .findAny()
                     .orElseThrow();
    }
}
