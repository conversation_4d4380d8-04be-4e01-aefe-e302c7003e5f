package com.siteweb.admin.enums;

/**
 * 审计报表类型枚举
 *
 * <AUTHOR>
 * @date 2022/07/19
 */
public enum AuditReportTypeEnum {
    DEPARTMENT(3, "部门模块"),
    EMPLOYEE(3, "员工模块"),
    ACCOUNT(3, "账户模块"),
    ROLE(3, "角色模块"),
    REGION(3, "区域权限模块"),
    LOGIN(2, "登录模块"),
    CONTROL(4,"控制模块"),
    SECURITY_SETTING(1,"安全配置模块"),

    SAMPLER_ACCESS(3, "采集器接入"),
    THIRD_PARTY_ACCESS(3, "第三方接入");





    AuditReportTypeEnum(Integer level, String describe) {
        this.level = level;
        this.describe = describe;
    }

    /**
     * 级别 1.最小级别  2.基本级别  3.详细级别  4.未定义级别
     */
    private final Integer level;

    /**
     * 描述
     */
    private final String describe;

    public Integer getLevel() {
        return this.level;
    }

    public String getDescribe() {
        return this.describe;
    }
}
