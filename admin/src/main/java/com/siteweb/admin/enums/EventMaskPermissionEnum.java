package com.siteweb.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum EventMaskPermissionEnum {
    Event_Mask(12, 0,"事件屏蔽"),
    EVENT_MASK_LEVEL_1(121, 1,"一级告警屏蔽"),
    EVENT_MASK_LEVEL_2(122, 2,"二级告警屏蔽"),
    EVENT_MASK_LEVEL_3(123,3, "三级告警屏蔽"),
    EVENT_MASK_LEVEL_4(124,4, "四级告警屏蔽"),
    EVENT_MASK_LEVEL_5(125, 5,"五级告警屏蔽"),
    EVENT_MASK_LEVEL_6(126, 6,"六级告警屏蔽"),
    EVENT_MASK_LEVEL_7(127, 7,"七级告警屏蔽"),
    EVENT_MASK_LEVEL_8(128, 8,"八级告警屏蔽"),
    EVENT_MASK_LEVEL_9(129, 9,"九级告警屏蔽"),
    EVENT_MASK_LEVEL_10(130, 10,"十级告警屏蔽");
    /**
     * 十级告警屏蔽权限集
     */
    public final static Set<EventMaskPermissionEnum> TEN_LEVEL_ALARM_MASK_PERMISSION_SET = Set.of(EVENT_MASK_LEVEL_1,EVENT_MASK_LEVEL_2,EVENT_MASK_LEVEL_3,EVENT_MASK_LEVEL_4,EVENT_MASK_LEVEL_5,EVENT_MASK_LEVEL_6,EVENT_MASK_LEVEL_7,EVENT_MASK_LEVEL_8,EVENT_MASK_LEVEL_9,EVENT_MASK_LEVEL_10);
    /**
     * 权限代码
     */
    private final int permissionId;

    /**
     * 事件级别
     */
    private final int eventLevel;

    /**
     * 权限描述
     */
    private final String name;

    public static Set<Integer> getEventLevelByPermissionIds(Set<Integer> permissionIdSet) {
        return Arrays.stream(values()).filter(i -> permissionIdSet.contains(i.getPermissionId())).map(EventMaskPermissionEnum::getEventLevel).collect(Collectors.toSet());
    }
}
