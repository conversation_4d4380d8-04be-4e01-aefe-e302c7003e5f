package com.siteweb.admin.init;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.mapper.MenuItemMapper;
import com.siteweb.common.properties.FeatureEnableProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;

@Component
@Slf4j
public class VideoMenuItemInit {
    @Autowired
    MenuItemMapper menuItemMapper;
    @Autowired
    FeatureEnableProperties featureEnableProperties;

    @PostConstruct
    public void initUpdateVideoMenuItem() {
        String serverIp = featureEnableProperties.getServerIp();
        if (CharSequenceUtil.isBlank(serverIp) || Objects.equals("#server-ip", serverIp)) {
            log.warn("server ip没有替换,{}", serverIp);
            return;
        }
        //~{video_address} 是占位符 需要使用真实的ip去替换该占位符
        menuItemMapper.updateVideoMenuItem(serverIp);
    }
}
