<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.UserRoleMapper">

    <resultMap id="userRoleResultMap" type="com.siteweb.admin.entity.UserRole">
        <id property="roleId" column="roleId"></id>
    </resultMap>

    <sql id="colums">
        RoleId, RoleName, Description
    </sql>
    <insert id="createUserRoleMap">
        INSERT INTO TBL_UserRoleMap(UserId, RoleId) values(#{userId}, #{roleId})
    </insert>
    <insert id="batchCreateUserRoleMap">
        INSERT INTO TBL_UserRoleMap(UserId, RoleId) VALUES
        <foreach collection="userRoleMap" item="item" separator=",">
            (#{item.userId},#{item.roleId})
        </foreach>
    </insert>
    <delete id="deleteUserRoleMapsByUserId">
        DELETE FROM tbl_userrolemap where userid=#{userId}
    </delete>
    <delete id="deleteUserRoleMapsByUserIdAndRoleId">
        DELETE FROM tbl_userrolemap where userid=#{userId} and roleId=#{roleId}
    </delete>

    <select id="findRolesByUserId" resultMap="userRoleResultMap">
        SELECT u.RoleId, u.RoleName, u.Description
        FROM tbl_userrole u left join tbl_userrolemap um
        on u.RoleId=um.RoleId
        where um.userId=#{userId}
    </select>
    <select id="hasAllPrivilege" resultType="java.lang.Integer">
        SELECT count(urr.OperationId)
        FROM TBL_Account ul
                 INNER JOIN TBL_UserRoleMap mp ON ul.UserId = mp.UserId
                 INNER JOIN TBL_UserRole ur ON mp.RoleId = ur.RoleId
                 INNER JOIN TBL_UserRoleRight urr ON ur.RoleId = urr.RoleId AND urr.OperationType = 1
                 INNER JOIN TBL_OperationGroup a ON a.GroupId = urr.OperationId
        WHERE ul.UserId = #{userId} AND a.GroupId = #{permissionId};
    </select>
    <select id="hasOperationPrivilege" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM   TBL_Account            ul
                   INNER JOIN  TBL_UserRoleMap        mp   ON ul.UserId       =   mp.UserId
                   INNER JOIN  TBL_UserRole           ur   ON mp.RoleId       =   ur.RoleId
                   INNER JOIN  TBL_UserRoleRight      urr  ON ur.RoleId       =   urr.RoleId        AND urr.OperationType = 1
                   INNER JOIN  TBL_OperationGroup		a   ON a.GroupId       =   urr.OperationId
                   INNER JOIN  TBL_OperationGroupMap  am   ON a.GroupId       =   am.GroupId
                   INNER JOIN  TBL_Operation          su   ON am.OperationId  =   su.OperationId
        WHERE ul.UserId = #{userId} AND su.OperationId = #{operationId};
    </select>
    <select id="isSystemAdministrator" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM tbl_userrole u
                 LEFT JOIN tbl_userrolemap um ON u.RoleId = um.RoleId
        WHERE um.userId = #{userId}
          AND u.RoleId = -1
    </select>

    <delete id="deleteUserRoleByRoleId">
        DELETE FROM tbl_userrole WHERE RoleId = #{roleId};
        DELETE FROM tbl_userrolemap WHERE RoleId = #{roleId};
        DELETE FROM rolepermissionmap WHERE RoleId = #{roleId};
        DELETE FROM TBL_OperationGroupMap where GroupId = #{roleId};
        DELETE FROM TBL_OperationGroup where GroupId =#{roleId};
        DELETE FROM TBL_UserRoleRight where RoleId=#{roleId};
    </delete>
</mapper>