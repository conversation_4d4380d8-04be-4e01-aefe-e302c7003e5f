<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.UserConfigMapper">
    <insert id="batchInsert">
        INSERT INTO userConfig(userid,configType ,"configKey",configValue) VALUES
        <foreach collection="allUserConfigKey" item="item" separator=",">
            (#{userId},#{item.configType},#{item.configKey},#{item.configValue})
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="userConfigList" item="item" separator=";">
            UPDATE userconfig SET configType = #{item.configType},configKey = #{item.configKey},configValue = #{item.configValue} where userconfigId = #{item.userConfigId}
        </foreach>
    </update>
    <select id="findTtsSystemEnable" resultType="java.lang.String">
        SELECT TtsConfigValue FROM ttsconfig WHERE TtsConfigKey = 'alarmtts.enable';
    </select>
</mapper>