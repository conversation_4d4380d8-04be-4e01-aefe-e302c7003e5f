<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.PermissionMapper">
    <insert id="createPermission" parameterType="com.siteweb.admin.entity.Permission" useGeneratedKeys="true" keyProperty="permissionId" keyColumn="permissionId">
        INSERT INTO Permission(Name, Category, Caption, Description, UpdateTime) VALUES(#{name}, #{category},#{caption}, #{description}, now())
    </insert>

    <select id="getPermissionsByRoleIdAndCategory" resultType="com.siteweb.admin.entity.Permission">
        SELECT a.PermissionId, a.Name, a.Category, a.Caption, a.Description, a.UpdateTime
        FROM Permission a LEFT JOIN RolePermissionMap b
        ON a.PermissionId = b.PermissionId and a.Category = b.PermissionCategoryId
        WHERE b.RoleId = #{roleId} and b.PermissionCategoryId = #{category}
    </select>

    <select id="getMenuPermissionsByRoleIds" resultType="com.siteweb.admin.entity.Permission">
        SELECT c.PermissionId, c.Name, c.Category, c.Caption, c.Description, c.UpdateTime
        FROM menupermissiongroup a
        INNER JOIN menupermissiongroupmap b ON a.MenuPermissionGroupId = b.MenuPermissionGroupId
        INNER JOIN Permission c ON c.PermissionId = b.PermissionId AND c.Category = 6
        INNER JOIN rolepermissionmap d ON d.PermissionId = c.PermissionId
        WHERE d.RoleId IN
        <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>
    <select id="getPermissionsByRoleId" resultType="com.siteweb.admin.entity.Permission">
        SELECT a.PermissionId, a.Name, a.Category, a.Caption, a.Description, a.UpdateTime
        FROM Permission a LEFT JOIN RolePermissionMap b
        ON a.PermissionId = b.PermissionId and a.Category = b.PermissionCategoryId
        WHERE b.RoleId = #{roleId}
    </select>

    <select id="getAllAreaPermission" resultType="com.siteweb.admin.entity.Permission">
        SELECT AreaId as PermissionId, AreaName as Name, 9 as Category, '片区权限' as Caption FROM tbl_area
    </select>

    <select id="getAllSpecialtyGroupPermission" resultType="com.siteweb.admin.entity.Permission">
        SELECT SpecialtyGroupId as PermissionId, SpecialtyGroupName as Name, 8 as Category, '专业权限' as Caption  FROM tbl_specialtygroup
    </select>

    <select id="findPermissionByScene" resultType="com.siteweb.admin.entity.Permission">
        SELECT p.PermissionId, p.Name, p.Category, p.Caption, p.Description, p.UpdateTime FROM Permission p INNER JOIN ScenePermissionMap spm ON p.PermissionId= spm.PermissionId
        INNER JOIN Scene ss ON ss.SceneId = spm.SceneId
        WHERE  ss.SceneId = #{sceneId}
    </select>
    <select id="findCurrentScenePermissionByCategory" resultType="com.siteweb.admin.entity.Permission">
        SELECT p.PermissionId, p.Name, p.Category, p.Caption, p.Description, p.UpdateTime
        FROM Permission p
                 INNER JOIN ScenePermissionMap spm ON p.PermissionId = spm.PermissionId
                 INNER JOIN Scene ss ON ss.SceneId = spm.SceneId
        WHERE ss.Checked = 1
          AND p.Category = #{category}
    </select>
</mapper>