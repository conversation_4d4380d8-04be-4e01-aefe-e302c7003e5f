<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.EmployeeMapper">
    <sql id="Base_Column_list">
        EmployeeId,DepartmentId,EmployeeName,EmployeeType,EmployeeTitle,JobNumber,Gender,Mobile,Phone,Email,Address,PostAddress,Enable,Description,IsAddTempUser,UserValidTime,Alias
    </sql>

    <insert id="batchCreate">
        INSERT INTO tbl_employee(EmployeeId,DepartmentId,EmployeeName,EmployeeType,EmployeeTitle,
        JobNumber,Gender,Mobile,Phone,Email,Address,PostAddress,Enable,Description,IsAddTempUser,UserValidTime) VALUES
        <foreach collection="employeeList" item="item" separator=",">
            (#{item.employeeId},#{item.departmentId},#{item.employeeName},
            #{item.employeeType},#{item.employeeTitle},#{item.jobNumber},
            #{item.gender},#{item.mobile},#{item.phone},
            #{item.email},#{item.address},#{item.postAddress},
            #{item.enable},#{item.description},#{item.addTempUser},
            #{item.userValidTime})
        </foreach>
    </insert>

    <update id="setDefaultDepartmentIdForEmployees">
        UPDATE tbl_employee set DepartmentId = 0 where DepartmentId = #{departmentId}
    </update>

    <update id="setDefaultDepartmentIdsForEmployees">
        UPDATE tbl_employee set DepartmentId = 0
        <if test="idList.size > 0 and idList != null">
            WHERE DepartmentId in
            <foreach collection="idList" separator="," open=" (" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="getEmployeeCountsByDepartmentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tbl_employee WHERE DepartmentId = #{departmentId}
    </select>

    <select id="getEmployeeCountsByDepartmentIds" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tbl_employee
        <if test="idList.size > 0 and idList != null">
            WHERE DepartmentId in
            <foreach collection="idList" separator="," open=" (" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getEmployeeByDepartmentIds" resultType="com.siteweb.admin.entity.Employee">
        SELECT
        <include refid="Base_Column_list"/>
        FROM tbl_employee a LEFT JOIN
        (SELECT Alias,UserId FROM accountalias WHERE checked = 1) b ON a.EmployeeId = b.UserId
        <where>
            <if test="departmentIds != null and departmentIds.size > 0">
                AND a.DepartmentId IN
                <foreach collection="departmentIds" item="departmentId" open="(" close=")" separator=",">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCardCountByEmployeeId" resultType="java.lang.Integer">
        select count(*) from tbl_card where UserId = #{employeeId}
    </select>
    <select id="findEmployeeNameByIds" resultType="java.lang.String">
        SELECT EmployeeName FROM tbl_employee WHERE EmployeeId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
    </select>
    <select id="findEmployeeEmailByIds" resultType="java.lang.String">
        SELECT Email FROM tbl_employee WHERE EmployeeId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
    </select>
    <select id="findEmployeePhoneByIds" resultType="java.lang.String">
        SELECT Phone FROM tbl_employee WHERE EmployeeId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
    </select>
    <select id="findEmployeeJobNumberByIds" resultType="java.lang.String">
        SELECT JobNumber FROM tbl_employee WHERE EmployeeId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
    </select>
</mapper>