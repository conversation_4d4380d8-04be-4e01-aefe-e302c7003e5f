<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.MenuProfileMapper">
    <update id="updateCheckedStatus">
        UPDATE MenuProfile
        SET checked = CASE
                          WHEN menuProfileId = #{menuProfileId} THEN 1
                          ELSE 0
            END;
    </update>
    <select id="findMenuProfileBySceneId" resultType="com.siteweb.admin.entity.MenuProfile">
        SELECT mp.MenuProfileId, mp.Name, mp.Checked, mp.Description
        FROM menuprofile mp
                 INNER JOIN scenemenuprofilemap smpm ON mp.MenuProfileId = smpm.MenuProfileId
        WHERE smpm.SceneId = #{sceneId}
    </select>
</mapper>