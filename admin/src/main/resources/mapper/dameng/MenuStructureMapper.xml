<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.MenuStructureMapper">
    <resultMap id="TopMenuStructureResultMap" type="com.siteweb.admin.entity.MenuStructure">
        <result column="menuStructureId" property="menuStructureId"/>
        <collection column="menuStructureId" property="children" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.MenuStructure" select="findChildrenMenuStructures"/>
    </resultMap>

    <resultMap id="ChildrenMenuStructureResultMap" type="com.siteweb.admin.entity.MenuStructure">
        <result column="menuStructureId" property="menuStructureId"/>
        <collection column="menuStructureId" property="children" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.MenuStructure" select="findChildrenMenuStructures"/>
        <collection column="menuStructureId" property="menuItemStructureMaps" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.MenuItemStructureMap" select="findChildrenMenuItemStructureMap"/>
    </resultMap>

    <sql id="Base_Column_List">
        MenuStructureId, MenuProfileId, ParentId, Title, Icon,
        Selected, Expanded, Hidden, SortIndex, IsSystem, Description,Alias
    </sql>

    <select id="findChildrenMenuStructures" resultMap="ChildrenMenuStructureResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuStructure
        WHERE parentId = #{parentMenuStructureId}
    </select>

    <select id="findMenuStructureById" resultMap="ChildrenMenuStructureResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuStructure
        WHERE menuStructureId = #{menuStructureId}
    </select>

    <select id="findAllMenuStructures" resultMap="TopMenuStructureResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuStructure
        WHERE parentId = 0
    </select>

    <select id="findTopMenuStructuresByMenuProfileId" resultMap="ChildrenMenuStructureResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuStructure
        WHERE parentId = 0 And menuProfileId = #{menuProfileId}
    </select>

    <select id="findChildrenMenuItemStructureMap" resultType="com.siteweb.admin.entity.MenuItemStructureMap">
        SELECT
        MenuItemStructureMapId, MenuProfileId,
        MenuStructureId, MenuItemId, SortIndex
        FROM menuItemStructureMap
        WHERE menuStructureId = #{menuStructureId}
    </select>
    <select id="findByMenuProfileIdAndParentId" resultMap="ChildrenMenuStructureResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM MenuStructure
        WHERE menuProfileId = #{menuProfileId}
          AND parentId = #{parentId}
        ORDER BY sortIndex
    </select>
    <select id="findStructureTitleByMenuIdAndProfileId" resultType="com.siteweb.admin.entity.MenuStructure">
        SELECT structure.ParentId,
               structure.menuStructureId,
               structure.title
        FROM menuitemstructuremap map
                 INNER JOIN menustructure structure ON map.MenuStructureId = structure.MenuStructureId AND structure.MenuProfileId = #{menuProfileId}
        WHERE map.MenuProfileId = #{menuProfileId}
          AND map.menuItemId = #{menuItemId}
    </select>
</mapper>