<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.RegionMapper">
    <select id="findByUserId" resultType="com.siteweb.admin.entity.Region">
        SELECT DISTINCT a.RegionId, a.RegionName from Region a INNER JOIN RolePermissionMap b ON b.PermissionId=a.RegionId and b.PermissionCategoryId=10
        INNER JOIN TBL_UserRoleMap c ON b.RoleId=c.RoleId
        WHERE c.UserId = #{userId}
    </select>
    <select id="findUserIds" resultType="java.lang.Integer">
        SELECT tu.UserId FROM rolepermissionmap r
        INNER JOIN tbl_userrolemap tu ON tu.RoleId = r.RoleId
        WHERE PermissionCategoryId = 10 AND PermissionId IN
        <foreach collection="regionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>