<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.SecurityReportMapper">
    <select id="removeMaxCountRecord">
        DELETE
        FROM securityreport
        WHERE SecurityReportId IN (SELECT result.SecurityReportId
                                FROM (SELECT a.SecurityReportId, ROW_NUMBER() OVER (ORDER BY a.SecurityReportId DESC) rn
                                      FROM securityreport a
                                      ORDER BY a.SecurityReportId DESC) result
                                WHERE result.rn > #{auditMaxCount})
    </select>
    <select id="findSecurityReport" resultType="com.siteweb.admin.entity.SecurityReport">
        SELECT security.securityReportId,
        security.OperationAccount,
        security.ClientIP,
        security.Details,
        security.CreateTime,
        security.Type,
        item.ItemValue AS typeName
        FROM securityreport security INNER JOIN tbl_dataitem item ON security.type = item.ItemId AND item.EntryId = 3002
        WHERE CreateTime &gt;= #{securityReportParamDto.startTime} AND CreateTime &lt;= #{securityReportParamDto.endTime}
        <if test="securityReportParamDto.operator != null and securityReportParamDto.operator != ''">
            AND security.OperationAccount LIKE CONCAT('%',#{securityReportParamDto.operator},'%')
        </if>
        <if test="securityReportParamDto.details != null and securityReportParamDto.details != ''">
            AND security.Details LIKE CONCAT('%', #{securityReportParamDto.details},'%')
        </if>
        <if test="securityReportParamDto.clientIp != null and securityReportParamDto.clientIp != ''">
            AND security.ClientIp LIKE CONCAT('%',#{securityReportParamDto.clientIp},'%')
        </if>
        <if test="securityReportParamDto.typeList != null and securityReportParamDto.typeList.size() > 0">
            AND security.type IN
            <foreach collection="securityReportParamDto.typeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY security.securityReportId DESC
        LIMIT #{securityReportParamDto.maxCount}
    </select>
</mapper>