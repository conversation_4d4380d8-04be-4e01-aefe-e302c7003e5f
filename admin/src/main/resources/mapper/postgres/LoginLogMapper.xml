<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.LoginLogMapper">
    <insert id="insertLoginLog" parameterType="com.siteweb.admin.entity.LoginLog" useGeneratedKeys="true" keyProperty="loginLogId" keyColumn="loginLogId">
        INSERT INTO LoginLog(UserId, OperatingTime, OperatingType, ClientType, ClientIp) VALUES(#{userId}, #{operatingTime}, #{operatingType}, #{clientType}, #{clientIp})
    </insert>
    <select id="findByOperatingTimeBetween" resultType="com.siteweb.admin.dto.LoginLogDTO">
        SELECT a.LoginLogId,
               a.UserId,
               a.OperatingTime,
               a.OperatingType,
               a.ClientType,
               a.ClientIp,
               b.UserName
        FROM LoginLog a
                 LEFT JOIN tbl_account b ON a.UserId = b.UserId
        ORDER BY a.OperatingTime DESC
    </select>
</mapper>