<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.MenuItemMapper">

    <resultMap id="TopMenuItemResultMap" type="com.siteweb.admin.entity.MenuItem">
        <result column="menuItemId" property="menuItemId"/>
        <collection column="menuItemId" property="children" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.MenuItem" select="findChildrenMenuItems"/>
    </resultMap>

    <resultMap id="ChildrenMenuItemResultMap" type="com.siteweb.admin.entity.MenuItem">
        <result column="menuItemId" property="menuItemId"/>
        <collection column="menuItemId" property="children" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.MenuItem" select="findChildrenMenuItems"/>
    </resultMap>

    <sql id="Base_Column_List">
        MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch,
        LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description,Alias,IsEmbed,SortIndex
    </sql>
    <update id="batchUpdate">
        <foreach collection="menuItemList" item="item" separator=";">
            UPDATE menuitem SET ParentId = #{item.parentId},path = #{item.path},title = #{item.title},icon = #{item.icon},
            selected = #{item.selected},expanded = #{item.expanded},pathMatch =#{item.pathMatch},layoutPosition = #{item.layoutPosition},
            isSystemConfig = #{item.systemConfig},isExternalWeb = #{item.externalWeb},menuHasNavigation = #{item.menuHasNavigation},description = #{item.description}
            where MenuItemId = #{item.menuItemId}
        </foreach>
    </update>
    <update id="updateVideoMenuItem">
        UPDATE menuitem
        SET path = REGEXP_REPLACE(path, '~{video_address}', #{serverIp}, 'g')
        WHERE path LIKE '%~{video_address}%';
    </update>

    <select id="findChildrenMenuItems" resultMap="ChildrenMenuItemResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuItem
        WHERE parentId = #{parentMenuItemId}
        ORDER BY SortIndex
    </select>

    <select id="findMenuItemById" resultMap="ChildrenMenuItemResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuItem
        WHERE menuItemId = #{menuItemId}
        ORDER BY SortIndex
    </select>

    <select id="findAllMenuItems" resultMap="TopMenuItemResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM menuItem
        WHERE parentId = 0
        ORDER BY SortIndex
    </select>

    <select id="findTopMenuItemPath" resultType="java.lang.String">
        SELECT Path FROM menuItem WHERE parentId = 0
    </select>
    <select id="findByParentId" resultType="com.siteweb.admin.entity.MenuItem">
        SELECT MenuItemId,
               ParentId,
               Path,
               Title,
               Icon,
               FeatureId,
               Selected,
               Expanded,
               PathMatch,
               LayoutPosition,
               IsSystemConfig,
               IsExternalWeb,
               MenuHasNavigation,
               Description
        FROM MenuItem
        WHERE parentId = #{parentId}
    </select>
    <select id="findBySceneIdAndParentId" resultType="com.siteweb.admin.entity.MenuItem">
        SELECT MenuItemId,
        ParentId,
        Path,
        Title,
        Icon,
        FeatureId,
        Selected,
        Expanded,
        PathMatch,
        LayoutPosition,
        IsSystemConfig,
        IsExternalWeb,
        MenuHasNavigation,
        Description,
        sortIndex,
        Alias
        FROM MenuItem
    </select>
</mapper>