<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.PermissionCategoryMapper">
    <insert id="createPermissionCategory" parameterType="com.siteweb.admin.entity.PermissionCategory" useGeneratedKeys="true" keyProperty="permissionCategoryId" keyColumn="permissionCategoryId">
        INSERT INTO PermissionCategory(Name, Caption, Description) VALUES(#{name}, #{caption}, #{description})
    </insert>
    <delete id="deletePermissionCategoryById">
        DELETE FROM RolePermissionMap WHERE PermissionCategoryId = #{permissionCategoryId};
        DELETE FROM Permission WHERE Category = #{permissionCategoryId};
        DELETE FROM PermissionCategory WHERE PermissionCategoryId = #{permissionCategoryId};
    </delete>

    <select id="findPermissionCategorieByScene" resultType="com.siteweb.admin.entity.PermissionCategory">
        SELECT a.PermissionCategoryId, a.NAME, a.Caption, a.DESCRIPTION FROM permissioncategory a
        LEFT JOIN scenepermissioncategorymap b ON  a.PermissionCategoryId = b.PermissionCategoryId
        LEFT JOIN scene c ON c.SceneId = b.SceneId
        WHERE c.Checked = 1 and a.PermissionCategoryId != 11
    </select>
</mapper>