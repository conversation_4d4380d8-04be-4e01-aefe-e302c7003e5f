<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.AuditReportMapper">
    <delete id="removeRecordCount">
        delete from auditreport where 1=1 order by AuditReportId limit #{count};
    </delete>
    <select id="findAuditReport" resultType="com.siteweb.admin.entity.AuditReport">
        SELECT auditReport.AuditReportId,
        auditReport.OperationAccount,
        auditReport.Level,
        auditReport.ClientIP,
        auditReport.Details,
        auditReport.type,
        auditReport.result,
        auditReport.CreateTime,
        item.ItemValue AS levelName
        FROM auditreport auditReport
        INNER JOIN tbl_dataitem item ON auditReport.Level = item.ItemId
        AND item.EntryId = 3001
        WHERE CreateTime &gt;= #{auditReportParamDto.startTime} AND CreateTime &lt;= #{auditReportParamDto.endTime}
        <if test="auditReportParamDto.operator != null and auditReportParamDto.operator != ''">
            AND auditReport.OperationAccount LIKE CONCAT('%',#{auditReportParamDto.operator},'%')
        </if>
        <if test="auditReportParamDto.details != null and auditReportParamDto.details != ''">
            AND auditReport.Details LIKE CONCAT('%', #{auditReportParamDto.details},'%')
        </if>
        <if test="auditReportParamDto.clientIp != null and auditReportParamDto.clientIp != ''">
            AND auditReport.ClientIp LIKE CONCAT('%',#{auditReportParamDto.clientIp},'%')
        </if>
        <if test="auditReportParamDto.levelList != null and auditReportParamDto.levelList.size() > 0">
            AND auditReport.level IN
            <foreach collection="auditReportParamDto.levelList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY auditReport.AuditReportId DESC
    </select>
    <select id="getLatestRecord" resultType="java.lang.Integer">
        SELECT auditReportId
        FROM auditreport
        ORDER BY AuditReportId
        LIMIT #{count};
    </select>
</mapper>