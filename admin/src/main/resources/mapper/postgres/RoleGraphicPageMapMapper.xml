<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.RoleGraphicPageMapMapper">
    <select id="findAll" resultType="com.siteweb.admin.dto.RoleGraphicPageMapDTO">
        SELECT role.RoleId, map.GraphicPageId, role.RoleName, page.Name AS graphicPageName,map.config
        FROM  tbl_userrole role
                  LEFT JOIN rolegraphicpagemap map ON map.RoleId = role.RoleId
                  LEFT JOIN graphicpage page ON map.GraphicPageId = page.Id
    </select>
    <select id="findById" resultType="com.siteweb.admin.dto.RoleGraphicPageMapDTO">
        SELECT role.RoleId, map.GraphicPageId, role.RoleName, page.Name AS graphicPageName,map.config
        FROM  tbl_userrole role
                  LEFT JOIN rolegraphicpagemap map ON map.RoleId = role.RoleId
                  LEFT JOIN graphicpage page ON map.GraphicPageId = page.Id
        WHERE role.RoleId = #{id}
    </select>

    <select id="findByUserId" resultType="com.siteweb.admin.entity.RoleGraphicPageMap">
        SELECT * from rolegraphicpagemap a INNER JOIN tbl_userrolemap b ON a.roleId = b.roleId
        WHERE userId = #{id}
    </select>
</mapper>