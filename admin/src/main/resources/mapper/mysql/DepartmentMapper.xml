<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.DepartmentMapper">

    <resultMap id="TopDepartmentResultMap" type="com.siteweb.admin.entity.Department">
        <result column="departmentId" property="departmentId"/>
        <collection column="departmentId" property="children" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.Department" select="findChildrenDepartments"/>
    </resultMap>

    <resultMap id="ChildrenDepartmentResultMap" type="com.siteweb.admin.entity.Department">
        <result column="departmentId" property="departmentId"/>
        <collection column="departmentId" property="children" javaType="java.util.ArrayList"
                    ofType="com.siteweb.admin.entity.Department" select="findChildrenDepartments"/>
    </resultMap>

    <sql id="Base_Column_List">
        DepartmentId, DepartmentName, DepartmentFunction, ParentDeprtId, Description, LastUpdateDate
    </sql>
    <insert id="batchCreate">
        INSERT INTO tbl_department(DepartmentId, DepartmentName, DepartmentFunction, ParentDeprtId, Description, LastUpdateDate) VALUES
        <foreach collection="departmentList" item="item" separator=",">
            (#{item.departmentId},#{item.departmentName},#{item.departmentFunction},
            #{item.parentDeprtId},#{item.description},#{item.lastUpdateDate})
        </foreach>
    </insert>

    <select id="findAllDepartments" resultMap="TopDepartmentResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_department
        WHERE parentDeprtId is NULL
    </select>

    <select id="findDepartmentById" resultMap="ChildrenDepartmentResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_department
        WHERE departmentId = #{departmentId}
    </select>

    <select id="findChildrenDepartments" resultMap="ChildrenDepartmentResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_department
        WHERE parentDeprtId = #{parentDepartmentId}
    </select>
</mapper>