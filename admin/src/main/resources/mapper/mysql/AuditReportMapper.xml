<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.AuditReportMapper">
    <delete id="removeRecordCount">
        delete from auditreport where 1=1 order by AuditReportId limit #{count};
    </delete>
    <select id="findAuditReport" resultType="com.siteweb.admin.entity.AuditReport">
        SELECT audit.AuditReportId,
        audit.OperationAccount,
        audit.`Level`,
        audit.ClientIP,
        audit.Details,
        audit.type,
        audit.result,
        audit.CreateTime,
        item.ItemValue AS levelName
        FROM auditreport audit
        INNER JOIN tbl_dataitem item ON audit.Level = item.ItemId
        AND item.EntryId = 3001
        WHERE CreateTime &gt;= #{auditReportParamDto.startTime} AND CreateTime &lt;= #{auditReportParamDto.endTime}
        <if test="auditReportParamDto.operator != null and auditReportParamDto.operator != ''">
            AND audit.OperationAccount LIKE CONCAT('%',#{auditReportParamDto.operator},'%')
        </if>
        <if test="auditReportParamDto.details != null and auditReportParamDto.details != ''">
            AND audit.Details LIKE CONCAT('%', #{auditReportParamDto.details},'%')
        </if>
        <if test="auditReportParamDto.clientIp != null and auditReportParamDto.clientIp != ''">
            AND audit.ClientIp LIKE CONCAT('%',#{auditReportParamDto.clientIp},'%')
        </if>
        <if test="auditReportParamDto.levelList != null and auditReportParamDto.levelList.size() > 0">
            AND audit.level IN
            <foreach collection="auditReportParamDto.levelList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY audit.AuditReportId DESC
    </select>
    <select id="getLatestRecord" resultType="java.lang.Integer">
        SELECT auditReportId
        FROM auditreport
        ORDER BY AuditReportId
        LIMIT #{count};
    </select>
</mapper>