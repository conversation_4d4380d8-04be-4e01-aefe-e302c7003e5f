<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.UserGraphicPageMapMapper">
    <select id="findAll" resultType="com.siteweb.admin.dto.UserGraphicPageMapDTO">
        SELECT map.UserGraphicPageMapId, account.UserId, map.GraphicPageId, account.UserName, page.Name AS graphicPageName, department.DepartmentName,map.config
        FROM  tbl_account account
                  LEFT JOIN usergraphicpagemap map ON map.UserId = account.UserId
                  LEFT JOIN graphicpage page ON map.GraphicPageId = page.Id
                  LEFT JOIN tbl_employee te ON te.EmployeeId = account.UserId
                  LEFT JOIN tbl_department department on te.DepartmentId = department.DepartmentId
    </select>
    <select id="findById" resultType="com.siteweb.admin.dto.UserGraphicPageMapDTO">
        SELECT map.UserGraphicPageMapId, account.UserId, map.GraphicPageId, account.UserName, page.Name AS graphicPageName, department.DepartmentName,map.config
        FROM  tbl_account account
                  LEFT JOIN usergraphicpagemap map ON map.UserId = account.UserId
                  LEFT JOIN graphicpage page ON map.GraphicPageId = page.Id
                  LEFT JOIN tbl_employee te ON te.EmployeeId = account.UserId
                  LEFT JOIN tbl_department department on te.DepartmentId = department.DepartmentId
        WHERE map.UserGraphicPageMapId = #{id}
    </select>
</mapper>