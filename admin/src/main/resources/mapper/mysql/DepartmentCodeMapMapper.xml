<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.DepartmentCodeMapMapper">
    <insert id="batchCreateDepartmentCodeMap">
        INSERT INTO departmentcodemap(DepartmentId, ParentDepartmentId, Code, ParentCode) VALUES
        <foreach collection="departmentCodeMapList" item="item" separator=",">
            (#{item.departmentId},#{item.parentDepartmentId},#{item.code}, #{item.parentCode})
        </foreach>
    </insert>
</mapper>