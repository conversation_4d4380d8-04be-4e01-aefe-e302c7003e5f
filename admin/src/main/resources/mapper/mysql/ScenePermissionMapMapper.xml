<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.ScenePermissionMapMapper">
    <insert id="batchInsert">
        INSERT INTO scenepermissionmap(permissionid, sceneid) VALUES
        <foreach collection="scenePermissionMapList" item="item" separator=",">
            (#{item.permissionId},#{item.sceneId})
        </foreach>
    </insert>
    <select id="findExistsScenePermission" resultType="java.lang.String">
        SELECT b.Description FROM ScenePermissionMap a INNER JOIN permission b on a.PermissionId = b.PermissionId INNER
        JOIN Scene c ON c.SceneId = a.SceneId
        WHERE b.Category = 6 AND a.SceneId = #{sceneId} and c.Checked = 1 AND b.Description IN
        <foreach collection="cascadeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>