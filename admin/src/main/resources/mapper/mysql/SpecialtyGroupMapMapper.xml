<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.SpecialtyGroupMapMapper">
    <insert id="createSpecialtyGroupMap" parameterType="com.siteweb.admin.entity.SpecialtyGroupMap"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO TBL_SpecialtyGroupMap(SpecialtyGroupId ,EntryItemId, Operation) VALUES(#{specialtyGroupId},#{entryItemId}, #{operation})
    </insert>

    <select id="getSpecialty" resultType="com.siteweb.admin.dto.SpecialtyDTO">
        SELECT a.EntryId,a.ItemId,b.EntryName,a.Item<PERSON>alue,a.Description
        FROM tbl_dataitem a
        LEFT JOIN tbl_dataentry b ON a.EntryId = b.EntryId
        WHERE a.EntryId = 7
    </select>
</mapper>