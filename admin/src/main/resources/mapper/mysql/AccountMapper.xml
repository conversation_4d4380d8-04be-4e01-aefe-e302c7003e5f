<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.AccountMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_account(UserId, UserName, LogonId, Password, Enable,
        MaxError, Locked, ValidTime, Description, IsRemote,
        CenterId, PasswordValidTime, Avatar, ThemeName, NeedResetPwd) VALUES
        <foreach collection="accountList" item="item" separator=",">
            (#{item.userId},#{item.userName},#{item.logonId},
            #{item.password},#{item.enable},#{item.maxError},
            #{item.locked},#{item.validTime},#{item.description},
            #{item.remote},#{item.centerId},#{item.passwordValidTime},
            #{item.avatar},#{item.themeName},#{item.needResetPwd})
        </foreach>
    </insert>
    <select id="findByMobile" resultType="com.siteweb.admin.entity.Account">
        SELECT UserId, UserName, LogonId, Password, Enable,
        MaxError, Locked, ValidTime, Description, IsRemote,
        CenterId, PasswordValidTime, Avatar, ThemeName, NeedResetPwd
        FROM TBL_Account WHERE UserId IN (SELECT EmployeeId FROM TBL_Employee WHERE Mobile = #{mobile})
    </select>
    <select id="findUserNameByUserId" resultType="java.lang.String">
        SELECT username
        FROM tbl_account
        WHERE UserId = #{userId}
    </select>
    <select id="passwordInValidTime" resultType="java.lang.Boolean">
        SELECT CASE
        WHEN NOW() &lt;= IFNULL(PasswordValidTime, '2999-01-01') THEN 1
        ELSE 0
        END AS is_valid
        FROM tbl_account
        WHERE LogonId = #{userName}
    </select>
</mapper>