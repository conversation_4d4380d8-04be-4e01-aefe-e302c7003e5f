<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.OperationGroupMapMapper">
    <insert id="batchCreate">
        INSERT INTO tbl_operationgroupmap(operationid, groupid) VALUES
        <foreach collection="operationIds" item="operationId" separator=",">
            (#{operationId},#{groupId})
        </foreach>
    </insert>
    <select id="findPermissionIdsByGroupIds" resultType="java.lang.Integer">
        SELECT OperationId FROM tbl_operationgroupmap WHERE GroupId IN
        <foreach collection="permissionGroupIds" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </select>
</mapper>