<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.RegionMapMapper">

    <resultMap id="userRoleResultMap" type="com.siteweb.admin.entity.RegionMap">
        <id property="regionMapId" column="RegionMapId"></id>
        <id property="resourceStructureId" column="resourceStructureId"></id>
        <id property="equipmentId" column="equipmentId"></id>
        <id property="regionId" column="RegionId"></id>
    </resultMap>

    <sql id="colums">
        regionMapId, resourceStructureId, equipmentId, regionId
    </sql>
    <insert id="batchInsert">
        INSERT INTO regionmap (resourcestructureid, equipmentid, regionid) VALUES
        <foreach collection="regionMaps" item="item"  separator=",">
          (#{item.resourceStructureId},#{item.equipmentId},#{item.regionId})
        </foreach>
    </insert>

    <select id="findByRegionIds" resultMap="userRoleResultMap">
        select t1.RegionMapId, t1.resourceStructureId, t1.equipmentId, t1.RegionId from regionmap t1
        inner join region t2 on t1.RegionId = t2.RegionId
        where t1.RegionId in
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="findByUserId" resultType="com.siteweb.admin.entity.RegionMap">
        SELECT DISTINCT a.RegionMapId, a.ResourceStructureId, a.EquipmentId, a.RegionId from RegionMap a INNER JOIN Region b ON a.RegionId=b.RegionId
        INNER JOIN RolePermissionMap c ON c.PermissionId=b.RegionId and c.PermissionCategoryId=10
        INNER JOIN TBL_UserRoleMap d ON c.RoleId=d.RoleId
        WHERE d.UserId = #{userId}
    </select>

    <select id="findResourceStructureIdsByRegionIds" resultType="int">
        select resourceStructureId from regionmap where RegionId in
        <foreach collection="regionIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>