<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.AreaMapper">
    <select id="findByUserId" resultType="com.siteweb.admin.entity.Area">
        SELECT a.AreaId, a.AreaName, a.Description
        FROM   TBL_Account            ul
                   INNER JOIN  TBL_UserRoleMap        mp   ON ul.UserId       =   mp.UserId
                   INNER JOIN  TBL_UserRole           ur   ON mp.RoleId       =   ur.RoleId
                   INNER JOIN  TBL_UserRoleRight      urr  ON ur.RoleId       =   urr.RoleId         AND urr.OperationType = 2
                   INNER JOIN  TBL_Area               a    ON a.AreaId        =   urr.OperationId
        WHERE ul.UserId = #{userId};
    </select>
</mapper>