<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.admin.mapper.HistoryPasswordMapper">

    <sql id="Base_Column_List">
        Id, LogonId, Password, UpdateTime
    </sql>

    <select id="findHistoryPasswordsByLogonId" resultType="com.siteweb.admin.entity.HistoryPassword">
        SELECT
        <include refid="Base_Column_List"/>
        FROM historypassword
        WHERE logonId = #{logonId} ORDER By updateTime desc
    </select>
</mapper>