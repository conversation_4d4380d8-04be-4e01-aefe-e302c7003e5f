package com.siteweb.monitoring.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.EventMask;
import com.siteweb.monitoring.vo.BatchEventMaskVO;
import com.siteweb.monitoring.vo.EventMaskVO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface EventMaskService {

    Page<EventMaskDTO> findEventMaskByKeywords(EventMaskFilterDTO eventMaskFilterDTO, Page<EventMaskDTO> pageable);

    void saveEventMask(EventMaskVO eventMaskVO, Integer userId);

    void batchCreateEventMasks(BatchEventMaskVO vo, Integer userId);

    void deleteEventMask(EventMask eventMask, Integer userId);

    void batchDeleteEventMasks(List<String> eventIds, Integer userId);

    void batchDeleteEventMasksByEquipmentId(Integer stationId, Integer equipmentId, Integer userId);

    void deleteAllEventMasks(Integer userId);

    EventMask findById(Integer stationId, Integer equipmentId, int eventId);

    EventMaskDTO findEventMaskDTOById(Integer stationId, Integer equipmentId, int eventId);

    /**
     * 通过设备id获取正在屏蔽生效中的事件id
     *
     * @param equipmentId 设备id
     * @return {@link Set}<{@link Integer}>
     */
    Set<Integer> findMaskEffective(Integer equipmentId);

    List<SimpleEventMaskDTO> findSimpleEventMaskDTOsByEquipmentId(Integer equipmentId);
    /**
     * 是否在屏蔽生效范围内
     *
     * @param stationId   站id
     * @param equipmentId 设备id
     * @param eventId     标识符
     * @return boolean
     */
    boolean isMaskEffective(Integer stationId, Integer equipmentId, Integer eventId);

    Page<ConditionEventMaskDTO> findSimpleEventMaskByBaseTypeIdsPage(EventMaskFilterDTO eventMaskFilterDTO, Page<ConditionEventMaskDTO> pageable);
    /**
     * 根据屏蔽保存结束事件
     * 对应存储过程：PNL_SaveEndEventByMask
     */
    void saveEndEventByMask(Integer stationId, Integer equipmentId, Integer eventId, String sequenceId, Date endTime);
    void deleteEventMaskByBaseTypeIds(EventMaskFilterDTO eventMaskFilterDTO);

    void createEventMaskByFilter(EventMaskFilterCreateDTO eventMaskFilterCreateDTO);

    void saveEndEventByEquipmentIds(List<Integer> equipmentIds);

    void saveEndEventByStationIds(List<Integer> stationIdList);

    /**
     * 批量保存告警屏蔽
     * @param userId 用户id
     * @param batchCreateEventMaskDTO 告警屏蔽基础信息
     */
    void batchSaveMask(Integer userId, BatchCreateEventMaskDTO batchCreateEventMaskDTO);
}

