package com.siteweb.monitoring.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.monitoring.dto.HistoryEventDTO;
import com.siteweb.monitoring.dto.HistoryEventPageDTO;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.vo.HisPowOffCountReqByStationId;
import com.siteweb.monitoring.vo.HistoryEventFilterVO;
import com.siteweb.monitoring.vo.HistoryPowerOffCountVO;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface HistoryEventService {

    List<HistoryEvent> findByBaseEquipmentIdAndStartTimeSpan(Integer baseEquipmentId, Date startDate, Date endDate);

    List<HistoryEvent> findByEquipmentIdAndStartTimeSpan(Integer equipmentId, Date startDate, Date endDate);

    List<HistoryEvent> findByEventIdAndStartTimeSpan(Integer eventId, Date startDate, Date endDate);

    List<HistoryEvent> findByStartTimeSpan(Date startDate, Date endDate);

    Map<Integer, Integer> groupHistoryEventsBySeverity(Date startDate, Date endDate, Integer userId);

    /**
     * 获取唯一历史告警
     * @param equipmentId 设备id
     * @param eventId   事件id
     * @param startTime 开始时间
     * @param eventConditionId 事件条件id
     * @param stationId 局站id
     * @return
     */
    HistoryEvent findByStartTimeAndEquipmentIdAndEventIdAndConditionId(Integer equipmentId, Integer eventId, Date startTime, Integer eventConditionId, Integer stationId);

    List<HistoryEventDTO> findHistoryEventDTOByEquipmentIdAndStartTimeSpan(Integer equipmentId, Date startDate, Date endDate);

    String findAlarmDurationByEquipmentIdAndEventId(Integer equipmentId, Integer eventId,Date startTime,Date endTime);

    Integer findAlarmCountByEquipmentIdAndEventId(Integer equipmentId, Integer eventId,Date startTime,Date endTime);
    List<HistoryEvent> findDurationByStationIdsAndEventCategoryId(List<Integer> stationIds, Integer eventCategoryId, Date startTime, Date endTime);

    /**
     * 判断一个设备在某一个时间点是否有告警
     * @param equipmentId 设备ID
     * @param time 时间
     * @return Boolean
     */
    Boolean isExistAlarmByEquipmentIdAndTime(Integer equipmentId, Date time);

    /**
     * 已结束且确认告警送历史告警表
     * 对应存储过程-> PNL_Ins_MidHistoryEvent
     */
    void insertMidHistoryEvent(String sequenceId, Date endTime, Date confirmTime, Integer confirmerId, String confirmerName,String note);

    List<HistoryEvent> findDurationByResourceStructureIdsAndEventCategoryId(List<Integer> resourceStructureIds, Integer eventCategoryId, Date startTime, Date endTime);

    List<HistoryPowerOffCountVO> getPowerOffCountByStationIds(HisPowOffCountReqByStationId hisPowOffCountReqByStationId);

    List<HistoryPowerOffCountVO> getOilEngineCountByStationIds(HisPowOffCountReqByStationId reqVO);

    void batchInsert(List<HistoryEvent> historyEventList);

    List<HistoryEvent> findAlarmHistoryByConditions(Integer equipmentId, Integer eventId, Date startTime, Date endTime);

    Long countByStartTimeSpan(Date startDate, Date endDate);

    List<Integer> listAlertEquipmentsByTimeSpan(Date startDate, Date endDate);

    /**
     * 历史告警筛选区域权限
     * @param historyEvents
     * @param userId
     * @return
     */
    List<HistoryEvent> findByUserId(List<HistoryEvent> historyEvents, Integer userId);

    // 新增的统计方法，支持权限过滤
    /**
     * 统计历史事件总数量（支持权限过滤）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID
     * @return 总数量
     */
    Long countHistoryEventsByUserId(Date startTime, Date endTime, Integer userId);

    /**
     * 按设备统计历史事件数量（支持权限过滤）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID
     * @return Map<设备ID, 数量>
     */
    List<Map<String, Object>> countHistoryEventsByEquipmentAndUserId(Date startTime, Date endTime, Integer userId);

    /**
     * 按事件等级统计历史事件数量（支持权限过滤）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID
     * @return Map<事件等级, 数量>
     */
    List<Map<String, Object>> countHistoryEventsByEventLevelAndUserId(Date startTime, Date endTime, Integer userId);

    /**
     * 按设备类型统计历史事件数量（支持权限过滤）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID
     * @return Map<设备类型, 数量>
     */
    List<Map<String, Object>> countHistoryEventsByEquipmentCategoryAndUserId(Date startTime, Date endTime, Integer userId);

    /**
     * 按资源结构ID统计历史事件数量（支持权限过滤）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID
     * @return Map<资源结构ID, 数量>
     */
    List<Map<String, Object>> countHistoryEventsByResourceStructureIdAndUserId(Date startTime, Date endTime, Integer userId);

    /**
     * 获取历史告警分页列表
     */
    IPage<HistoryEventPageDTO> queryHistoryEventPage(Integer userId, Pageable pageable, HistoryEventFilterVO historyEventFilterVO);

    /**
     * 查询历史告警统计
     */
    Map<Integer, Long> groupHistoryEventBySeverity(Integer userId, HistoryEventFilterVO historyEventFilterVO);
}

