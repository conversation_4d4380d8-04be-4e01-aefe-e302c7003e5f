package com.siteweb.monitoring.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.HexUtil;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.dto.StationMaskDTO;
import com.siteweb.monitoring.dto.TimeGroupSpanDTO;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.AlarmMaskLogMapper;
import com.siteweb.monitoring.mapper.StationMaskMapper;
import com.siteweb.monitoring.mapper.TimeGroupSpanMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.util.MaskUtil;
import com.siteweb.monitoring.vo.StationMaskVO;
import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("StationMaskService")
public class StationMaskServiceImpl implements StationMaskService {
    @Autowired
    StationMaskMapper stationMaskMapper;

    @Autowired
    AlarmMaskLogMapper alarmMaskLogMapper;

    @Autowired
    StationManager stationManager;
    @Autowired
    TimeGroupSpanMapper timeGroupSpanMapper;
    @Autowired
    EventMaskService eventMaskService;
    @Autowired
    EventMaskHistoryService eventMaskHistoryService;

    @Lazy
    @Autowired
    ActiveEventService activeEventService;
    @Lazy
    @Autowired
    StationMaskService stationMaskService;
    @Autowired
    TimeGroupSpanService timeGroupSpanService;
    @Autowired
    StationServiceImpl stationService;
    @Autowired
    AlarmMaskLogService alarmMaskLogService;

    @Override
    public List<StationMask> findByIds(List<Integer> ids){
        return stationMaskMapper.selectBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStationMask(StationMaskVO stationMaskVO, Integer userId) {
        Integer stationId = stationMaskVO.getStationId();
        Integer timeGroupId = stationMaskVO.getTimeGroupId();
        Date startTime = stationMaskVO.getStartTime();
        Date endTime = stationMaskVO.getEndTime();
        String reason = stationMaskVO.getReason();
        StringBuilder timeGroupChars = null;
        if (Integer.valueOf(1).equals(stationMaskVO.getTimeGroupCategory())) {//全时段屏蔽
            stationMaskService.saveStationMask(stationId, timeGroupId, startTime, endTime, userId, reason);
        } else { // 分时段屏蔽
            timeGroupChars = new StringBuilder();
            for (TimeGroupSpanVO timeGroupSpanVO : stationMaskVO.getTimeGroupSpans()) {
                String timeGroupChar = HexUtil.booleanListToHexString(timeGroupSpanVO.getTimeSpanBool());
                Integer week = timeGroupSpanVO.getWeek();
                stationMaskService.saveSeparateStationMask(stationId, timeGroupId, timeGroupChar, week, userId, reason);
                timeGroupChars.append(timeGroupSpanVO.getWeek());
                timeGroupChars.append(":");
                timeGroupChars.append(timeGroupChar);
                timeGroupChars.append(",");
            }
        }
        //operationType为1是指新增屏蔽
        stationMaskService.insertAlarmMaskLog(stationMaskVO.build(), 1, stationMaskVO.getTimeGroupCategory(), CharSequenceUtil.str(timeGroupChars), userId);
        stationManager.refreshStationByIds(List.of(stationMaskVO.getStationId()) );
    }

    @Override
    public void batchCreateStationMasks(List<StationMaskVO> vo, Integer userId) {
        for (StationMaskVO stationMaskVO:vo) {
            saveStationMask(stationMaskVO,userId);
        }
    }

    @Override
    public int deleteStationMask(Integer stationId, Integer userId) {
        StationMask stationMask = stationMaskMapper.selectById(stationId);
        if (ObjectUtil.isNotNull(stationMask)){
            stationMaskMapper.deleteStationMask(stationId);
        //operationType为2是指解除屏蔽
            insertAlarmMaskLog(stationMask, 2, null, null, userId);
        }
        return 1;
    }


    @Override
    public StationMaskDTO getStationMaskById(Integer stationId) {
        StationMaskDTO stationMaskDTO = stationMaskMapper.getStationMaskDTOByStationId(stationId);
        if (Objects.isNull(stationMaskDTO)) {
            return null;
        }
        List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.findByTimeGroupId(stationMaskDTO.getTimeGroupId());
        if (!timeGroupSpans.isEmpty()) {
            stationMaskDTO.setTimeGroupCategory(TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue());//分时段屏蔽
        } else {
            stationMaskDTO.setTimeGroupCategory(TimeGroupCategoryEnum.FULL_TIME_MASK.getValue());//全时段屏蔽
        }
        List<TimeGroupSpanDTO> timeGroupSpanDTOList = new ArrayList<>();
        for (TimeGroupSpan timeGroupSpan : timeGroupSpans) {
            TimeGroupSpanDTO timeGroupSpanDTO = MaskUtil.parserTimeGroupSpan(timeGroupSpan);
            timeGroupSpanDTOList.add(timeGroupSpanDTO);
        }
        stationMaskDTO.setTimeGroupSpans(timeGroupSpanDTOList);
        return stationMaskDTO;
    }

    /**
     * 判断当前局站是否处于屏蔽状态
     *
     * @param stationIds 局站ids
     * @return {@link Set }<{@link Integer }> 存在屏蔽的局站id
     */
    @Override
    public Set<Integer> isMaskEffective(List<Integer> stationIds){
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptySet();
        }
        Set<Integer> effectiveStationIds = new HashSet<>();
        //处于全时段屏蔽
        List<StationMask> stationMasks = stationMaskMapper.selectList(Wrappers.lambdaQuery(StationMask.class).in(StationMask::getStationId, stationIds));
        for (StationMask stationMask : stationMasks) {
            if (Objects.nonNull(stationMask.getStartTime()) && Objects.nonNull(stationMask.getEndTime()) && DateUtil.isIn(new Date(), stationMask.getStartTime(), stationMask.getEndTime())) {
                effectiveStationIds.add(stationMask.getStationId());
            }
        }
        List<Integer> timeGroupIds = stationMasks.stream().map(StationMask::getTimeGroupId).toList();
        if (CollUtil.isEmpty(timeGroupIds)) {
            return effectiveStationIds;
        }
        //分时段屏蔽
        Set<Integer> segmentedBlockSet = segmentedBlock(timeGroupIds, stationMasks);
        effectiveStationIds.addAll(segmentedBlockSet);
        return effectiveStationIds;
    }

    private Set<Integer> segmentedBlock(List<Integer> timeGroupIds, List<StationMask> stationMasks) {
        Set<Integer> segmentedBlockSet = new HashSet<>();
        List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.selectList(Wrappers.lambdaQuery(TimeGroupSpan.class)
                                                                                    .eq(TimeGroupSpan::getWeek, DateUtil.thisDayOfWeek())
                                                                                    .in(TimeGroupSpan::getTimeGroupId, timeGroupIds));
        Map<Integer, StationMask> stationMaskMap = stationMasks.stream().collect(Collectors.toMap(StationMask::getTimeGroupId, Function.identity()));
        for (TimeGroupSpan timeGroupSpan : timeGroupSpans) {
            //是分时段屏蔽
            stationMaskMap.computeIfPresent(timeGroupSpan.getTimeGroupId(), (key, value) -> {
                if (ObjectUtil.isNotNull(timeGroupSpan) && MaskUtil.isIntervalMask(timeGroupSpan.getTimeSpanChar())) {
                    segmentedBlockSet.add(value.getStationId());
                }
                return value;
            });
        }
        return segmentedBlockSet;
    }

    @Override
    public boolean isMaskEffective(Integer stationId) {
        StationMaskDTO stationMask = this.getStationMaskById(stationId);
        if (ObjectUtil.isNull(stationMask)) {
            return false;
        }
        List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.findByTimeGroupId(stationMask.getTimeGroupId());
        //处于全时段屏蔽
        if (CollUtil.isEmpty(timeGroupSpans) && DateUtil.isIn(new Date(), stationMask.getStartTime(), stationMask.getEndTime())) {
            return true;
        }
        //处于分时段屏蔽
        TimeGroupSpan timeGroupSpan = timeGroupSpanMapper.findByWeekAndTimeGroupId(DateUtil.thisDayOfWeek(), stationMask.getTimeGroupId());
        return ObjectUtil.isNotNull(timeGroupSpan) && MaskUtil.isIntervalMask(timeGroupSpan.getTimeSpanChar());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAlarmMaskLog(StationMask stationMask, int operationType, Integer timeGroupCategory, String timeGroupChars, Integer userId) {
        AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
        alarmMaskLog.setStationId(stationMask.getStationId());
        alarmMaskLog.setEquipmentId(-1);
        alarmMaskLog.setEventId(-1);
        alarmMaskLog.setUserId(userId);
        alarmMaskLog.setOperationType(operationType);
        alarmMaskLog.setOperationTime(new Date());
        alarmMaskLog.setTimeGroupCategory(timeGroupCategory);
        alarmMaskLog.setStartTime(stationMask.getStartTime());
        alarmMaskLog.setEndTime(stationMask.getEndTime());
        if (operationType == 1 && Integer.valueOf(2).equals(timeGroupCategory)) {//新增分时段屏蔽
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
        }
        alarmMaskLog.setComment(stationMask.getReason());
        alarmMaskLogMapper.insert(alarmMaskLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteStationMask(Integer userId, List<Integer> stationIds) {
        List<StationMask> stationMaskList = findByIds(stationIds);
        if (CollUtil.isEmpty(stationMaskList)) {
            return 0;
        }
        stationMaskMapper.deleteByIds(stationIds);
        timeGroupSpanService.deleteByTimeGroupIds(stationIds);
        //批量添加局站解除屏蔽日志
        alarmMaskLogService.stationMaskCancelLog(userId, stationMaskList);
        return stationMaskList.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSetStationMask(Integer userId, BatchSetStationMaskDTO dto) {
        //全时段屏蔽
        batchHandlerFullTimeMask(dto, userId);
        //分时段屏蔽
        if (Objects.equals(TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue(), dto.getTimeGroupCategory())) {
            //与全时段相比 多了一个批量处理分时段屏蔽字符串的表的逻辑
            timeGroupSpanService.batchInsertStationTimeGroupSpan(dto);
        }
        //添加屏蔽日志
        alarmMaskLogService.batchInsertStationMaskLog(dto, userId);
        return true;
    }

    private void batchHandlerFullTimeMask(BatchSetStationMaskDTO dto, Integer userId) {
        //先删除
        timeGroupSpanService.deleteByTimeGroupIds(dto.getStationIdList());
        stationMaskMapper.deleteByIds(dto.getStationIdList());
        // 再新增
        List<StationMask> insertList = new ArrayList<>();
        for (int i = 0; i < dto.getStationIdList().size(); i++) {
            Integer stationId = dto.getStationIdList().get(i);
            StationMask stationMask = stationMaskBuild(dto, stationId,userId);
            stationMask.setUserId(userId);
            insertList.add(stationMask);
        }
        //结束系统中当前的活动告警
        eventMaskService.saveEndEventByStationIds(dto.getStationIdList());
        //批量添加与插入
        batchInsert(insertList);
    }

    private void batchInsert(List<StationMask> stationMaskList) {
        if (CollUtil.isEmpty(stationMaskList)) {
            return;
        }
        stationMaskMapper.insert(stationMaskList);
    }

    private StationMask stationMaskBuild(BatchSetStationMaskDTO dto, Integer stationId, Integer userId) {
        //是否是分时段屏蔽
        boolean isTimeGroupSpan = Objects.equals(dto.getTimeGroupCategory(), 2);
        return StationMask.builder()
                          .stationId(stationId)
                          .timeGroupId(stationId)
                          .startTime(isTimeGroupSpan ? null : dto.getStartTime())
                          .endTime(isTimeGroupSpan ? null : dto.getEndTime())
                          .reason(dto.getReason())
                          .userId(userId)
                          .build();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStationMask(Integer stationId, Integer timeGroupId, Date startTime, Date endTime, Integer userId, String reason) {
        StationMaskDTO stationMaskDTO = this.getStationMaskById(stationId);
        if (ObjectUtil.isNotEmpty(stationMaskDTO)) {
            // 如果存在分时段屏蔽，则删除
            List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanMapper.findByStationId(stationId);
            if (CollUtil.isNotEmpty(timeGroupSpanList)) {
                List<Integer> timeGroupSpanIds = timeGroupSpanList.stream().map(TimeGroupSpan::getTimeSpanId).toList();
                timeGroupSpanMapper.deleteByIds(timeGroupSpanIds);
            }
        }
        stationMaskService.saveOrUpdateStationMask(stationId, timeGroupId, userId, reason, startTime, endTime);
        // 结束存量告警
        stationMaskService.endStockActiveEvent(stationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSeparateStationMask(Integer stationId, Integer timeGroupId, String timeGroupChar, Integer week, Integer userId, String reason) {
        stationMaskService.saveOrUpdateStationMask(stationId, timeGroupId, userId, reason, null, null);
        timeGroupSpanService.saveOrUpdateTimeGroupSpan(timeGroupId,timeGroupChar,week);
        // 结束存量活动告警
        stationMaskService.endStockActiveEvent(stationId);
    }

    /**
     * 存在则更新，不存在则新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateStationMask(Integer stationId, Integer timeGroupId, Integer userId, String reason, Date startTime, Date endTime) {
        StationMaskDTO stationMaskDTO = this.getStationMaskById(stationId);
        if (ObjectUtil.isNotEmpty(stationMaskDTO)) {
            LambdaUpdateWrapper<StationMask> updateWrapper = Wrappers.lambdaUpdate(StationMask.class)
                    .set(StationMask::getReason, reason)
                    .set(StationMask::getUserId, userId)
                    .set(StationMask::getStartTime, startTime)
                    .set(StationMask::getEndTime, endTime)
                    .set(StationMask::getTimeGroupId, timeGroupId)
                    .eq(StationMask::getStationId, stationId);
            stationMaskMapper.update(null, updateWrapper);
        }else {
            StationMask entity = new StationMask();
            entity.setStationId(stationId);
            entity.setReason(reason);
            entity.setUserId(userId);
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            entity.setTimeGroupId(timeGroupId);
            stationMaskMapper.insert(entity);
        }
    }

    /**
     * 结束存量活动告警
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endStockActiveEvent(Integer stationId) {
        List<ActiveEvent> activeEventList = activeEventService.getDBActiveEventByCondition(stationId, null,null);
        for (ActiveEvent activeEvent : activeEventList) {
            Integer equipmentId = activeEvent.getEquipmentId();
            Integer eventId = activeEvent.getEventId();
            String sequenceId = activeEvent.getSequenceId();
            Date currentTime = new Date();
            eventMaskService.saveEndEventByMask(stationId, equipmentId, eventId, sequenceId, currentTime);

            if (!eventMaskHistoryService.existsBySequenceId(sequenceId)) {
                eventMaskHistoryService.create(BeanUtil.toBean(activeEvent, EventMaskHistory.class));
            }
        }
    }
}
