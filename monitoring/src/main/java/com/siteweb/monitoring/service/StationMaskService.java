package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.dto.StationMaskDTO;
import com.siteweb.monitoring.entity.StationMask;
import com.siteweb.monitoring.vo.StationMaskVO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface StationMaskService {
    List<StationMask> findByIds(List<Integer> ids);

    void saveStationMask(StationMaskVO stationMaskVO, Integer userId);

    void batchCreateStationMasks(List<StationMaskVO> vo, Integer userId);

    int deleteStationMask(Integer stationId, Integer userId);

    StationMaskDTO getStationMaskById(Integer stationId);

    Set<Integer> isMaskEffective(List<Integer> stationIds);

    boolean isMaskEffective(Integer stationId);

    /**
     * 保存局站屏蔽（全时段）
     * 对应存储过程：PAM_SaveStationMask
     */
    void saveStationMask(Integer stationId, Integer timeGroupId, Date startTime, Date endTime, Integer userId, String reason);
    /**
     * 保存局站屏蔽（分时段）
     * 对应存储过程：PAM_SaveStationSeprateMask
     */
    void saveSeparateStationMask(Integer stationId, Integer timeGroupId, String timeGroupChar, Integer week, Integer userId, String reason);

    /**
     * 新增或更新
     */
    void saveOrUpdateStationMask(Integer stationId, Integer timeGroupId, Integer userId, String reason, Date startTime, Date endTime);

    /**
     * 结束存量告警
     */
    void endStockActiveEvent(Integer stationId);

    void insertAlarmMaskLog(StationMask build, int i, Integer timeGroupCategory, String str, Integer userId);

    /**
     * 批量删除局站屏蔽
     * @param userId 用户id
     * @param stationIds 局站ids
     * @return int
     */
    int batchDeleteStationMask(Integer userId, List<Integer> stationIds);

    /**
     * 批量设置局站屏蔽
     *
     * @param userId
     * @param batchSetStationMaskDTO 批量设置局站屏蔽信息DTO
     * @return int 成功的行数
     */
    boolean batchSetStationMask(Integer userId, BatchSetStationMaskDTO batchSetStationMaskDTO);
}
