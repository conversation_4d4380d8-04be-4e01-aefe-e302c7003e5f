package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.EquipmentBranch;
import com.siteweb.monitoring.mapper.EquipmentBranchMapper;
import com.siteweb.monitoring.service.EquipmentBranchService;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class EquipmentBranchServiceImpl implements EquipmentBranchService {
    @Autowired
    private EquipmentBranchMapper equipmentBranchMapper;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public List<EquipmentBranch> findAll() {
        return equipmentBranchMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public EquipmentBranch findById(Integer id) {
        return equipmentBranchMapper.selectById(id);
    }

    @Override
    public EquipmentBranch findByEquipmentIdAndBranchId(Integer equipmentId, Integer branchId) {
        return equipmentBranchMapper.selectOne(Wrappers.lambdaQuery(EquipmentBranch.class)
                                                       .eq(EquipmentBranch::getEquipmentId, equipmentId)
                                                       .eq(EquipmentBranch::getBranchId, branchId));
    }

    @Override
    public Integer createBranch(EquipmentBranch equipmentBranch) {
        return equipmentBranchMapper.insert(equipmentBranch);
    }

    @Override
    public List<ImportErrorInfoDTO> batchCreateBranch(List<EquipmentBranch> importEquipmentBranchList) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<EquipmentBranch> insertBranchList = new ArrayList<>();
        List<EquipmentBranch> updateBranchList = new ArrayList<>();
        for (int i = 0; i < importEquipmentBranchList.size(); i++) {
            EquipmentBranch equipmentBranch = importEquipmentBranchList.get(i);
            if (ObjectUtil.isNull(equipmentBranch.getBranchId()) || ObjectUtil.isNull(equipmentBranch.getEquipmentId())) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "branchId,equipmentId", messageSourceUtil.getMessage("common.field.cannotEmpty")));
                continue;
            }
            //数据库中已存在则覆盖
            EquipmentBranch equipmentBranchDb = this.findByEquipmentIdAndBranchId(equipmentBranch.getEquipmentId(), equipmentBranch.getBranchId());
            if (ObjectUtil.isNotNull(equipmentBranchDb)){
                equipmentBranch.setId(equipmentBranchDb.getId());
                updateBranchList.add(equipmentBranch);
                continue;
            }
            //否则添加
            insertBranchList.add(equipmentBranch);
        }
        //批量插入设备分支
        if (CollUtil.isNotEmpty(insertBranchList)) {
            equipmentBranchMapper.batchInsert(insertBranchList);
        }
        if (CollUtil.isNotEmpty(updateBranchList)) {
            equipmentBranchMapper.batchUpdate(updateBranchList);
        }
        return importErrorInfoList;
    }

    @Override
    public Integer deleteById(Integer id) {
        return equipmentBranchMapper.deleteById(id);
    }

    @Override
    public Integer updateEquipmentBranch(EquipmentBranch equipmentBranch) {
        return equipmentBranchMapper.updateById(equipmentBranch);
    }

    @Override
    public List<EquipmentBranch> findByEquipmentIdAndBranchIds(Integer equipmentId, List<Integer> branchIds) {
        if (CollUtil.isEmpty(branchIds)) {
            return Collections.emptyList();
        }
        return equipmentBranchMapper.selectList(Wrappers.lambdaQuery(EquipmentBranch.class)
                                                        .eq(EquipmentBranch::getEquipmentId, equipmentId)
                                                        .in(EquipmentBranch::getBranchId, branchIds));
    }

    /**
     * 通过设备id查询支路信息
     * @param equipmentId 设备id
     * @return {@link List}<{@link EquipmentBranch}>
     */
    @Override
    public List<EquipmentBranch> findByEquipmentId(Integer equipmentId) {
        return equipmentBranchMapper.selectList(Wrappers.lambdaQuery(EquipmentBranch.class)
                                                        .eq(EquipmentBranch::getEquipmentId, equipmentId));
    }
}
