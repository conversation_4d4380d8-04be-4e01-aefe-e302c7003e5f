package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.EquipmentSignalMeaningsDTO;
import com.siteweb.monitoring.entity.SignalMeanings;

import java.util.List;


public interface SignalMeaningsService {

    int insertSignalMeanings(SignalMeanings signalMeanings);

    int updateSignalMeanings(SignalMeanings signalMeanings);

    int deleteSignalMeaningsById(int equipmentTemplateId, int signalId, int stateValue);

    List<EquipmentSignalMeaningsDTO> findSignalsByEquipmentAndSignalIds(Integer equipmentId, List<Integer> signalIds);

    List<SignalMeanings> findSignalsByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchInsert(List<SignalMeanings> signalMeanings);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);
}

