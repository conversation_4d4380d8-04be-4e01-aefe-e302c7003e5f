package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.ActiveControlOfDoor;

import java.util.List;

public interface ActiveControlOfDoorService{

    List<ActiveControlOfDoor> findActiveControlOfDoors();

    int createActiveControlOfDoor(ActiveControlOfDoor activeControlOfDoor);

    int deleteById(Integer activeControlOfDoorId);

    int updateActiveControlOfDoor(ActiveControlOfDoor activeControlOfDoor);

    ActiveControlOfDoor findById(Integer activeControlOfDoorId);
}

