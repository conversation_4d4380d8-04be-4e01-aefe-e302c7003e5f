package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.SignalMapper;
import com.siteweb.monitoring.mapper.SignalMeaningsMapper;
import com.siteweb.monitoring.mapper.SignalPropertyMapper;
import com.siteweb.monitoring.model.ControlSignalRelation;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.vo.SignalBatchApplyVO;
import com.siteweb.utility.entity.OperationRecord;
import com.siteweb.utility.service.OperationRecordService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("signalService")
@RequiredArgsConstructor
public class SignalServiceImpl implements SignalService {

    private static final String SIGNAL_ID = "SignalId";
    private static final String OBJECT_ID = "ObjectId";
    private static final String CONFIG_ID = "ConfigId";
    private static final String EDIT_TYPE = "EditType";
    private static final String SIGNAL_NAME = "SignalName";
    private static final String STORE_INTERVAL = "StoreInterval";
    private static final String ABS_VALUE_THRESHOLD = "AbsValueThreshold";
    private static final String PERCENT_THRESHOLD = "PercentThreshold";
    private static final String STATICS_PERIOD = "StaticsPeriod";
    public static final String DYNAMIC_MEMO = "动态配置";

    private final SignalMapper signalMapper;
    private final ConfigSignalManager configSignalManager;
    private final DynamicConfigService dynamicConfigService;
    private final PrimaryKeyValueService primaryKeyValueService;
    private final ConfigChangeMacroLogService configChangeMacroLogService;
    private final SignalPropertyMapper signalPropertyMapper;
    private final SignalMeaningsMapper signalMeaningsMapper;
    private final EquipmentManager equipmentManager;
    private final OperationRecordService operationRecordService;
    private final ResourceStructureManager resourceStructureManager;
    private final EquipmentTemplateService equipmentTemplateService;
    private final MonitorUnitSignalService monitorUnitSignalService;
    private final SignalMeaningsService signalMeaningsService;
    private final SignalPropertyService signalPropertyService;
    private final EventService eventService;
    private final ControlService controlService;
    private final EquipmentService equipmentService;
    @Override
    public List<Signal> findSignalsByEquipmentId(Integer equipmentId) {
        return signalMapper.findSignalsByEquipmentId(equipmentId);
    }

    public HashMap<Integer, ControlSignalRelation> findSignalsAboutControlByEquipmentId(Integer equipmentId) {
        HashMap<Integer, ControlSignalRelation> result = new HashMap<>();
        List<ControlSignalRelation> css = signalMapper.findSignalsAboutControlByEquipmentId(equipmentId);
        for (ControlSignalRelation controlMeanings : css) {
            if (!result.containsKey(controlMeanings.getControlId())) {
                result.put(controlMeanings.getControlId(), controlMeanings);
            }
        }
        return result;
    }

    @Override
    public List<ConfigSignalItem> findEquipmentId(Integer equipmentId) {
        return configSignalManager.getConfigSignalByEquipmentId(equipmentId);
    }

    @Override
    public Map<Integer, List<ConfigSignalItem>> findConfigSignalMap(List<Integer> equipmentIdList) {
        return configSignalManager.getConfigSignalMapByEquipmentIds(equipmentIdList);
    }

    @Override
    public List<SimpleSignalDTO> findSimpleSignalDTOsByEquipmentId(Integer equipmentId) {
        return signalMapper.findSimpleSignalDTOsByEquipmentId(equipmentId);
    }

    @Override
    public List<SimpleSignalDTO> findSimpleSignalDTOsByBaseTypeIdsAndEquipmentIds(Collection<Integer> baseTypeIdList, Collection<Integer> equipmentIds) {
        if (CollUtil.isEmpty(baseTypeIdList)) {
            return Collections.emptyList();
        }
        return signalMapper.findSimpleSignalDTOsByBaseTypeIds(baseTypeIdList, equipmentIds);
    }

    @Override
    public ConfigSignalDTO findConfigSignalDTOBySignalId(int equipmentId, int signalId) {
        ConfigSignalDTO configSignalDTO = signalMapper.findConfigSignalDTOBySignalId(equipmentId, signalId);
        if (null == configSignalDTO) {
            return null;
        }
        String monitorUnitExpression = signalMapper.findMonitorUnitExpressionBySignalId(equipmentId, signalId);
        if (null != monitorUnitExpression) {
            configSignalDTO.setExpression(monitorUnitExpression);
        }
        configSignalDTO.setSignalPropertyIds(signalPropertyMapper.findSignalPropertyIdsBySignalId(equipmentId, signalId));
        configSignalDTO.setSignalMeanings(signalMeaningsMapper.findSignalMeaningDTOBySignalId(equipmentId, signalId));
        return configSignalDTO;
    }

    @Override
    @Transactional
    public int updateConfigSignalDTO(int userId, ConfigSignalDTO configSignalDTO) {
        //1.找到原有模板信号
        ConfigSignalDTO oldConfigSignalDTO = this.findConfigSignalDTOBySignalId(configSignalDTO.getEquipmentId(), configSignalDTO.getSignalId());
        //2.比较模板信号的变更点
        Map<String, Object> changedHashMap = compareConfigSignalDTO(configSignalDTO, oldConfigSignalDTO);
        //3.动态配置XML分发，即向TBL_DynamicConfig表中写入动态配置项
        dynamicConfigService.distributeConfigSignal(userId, configSignalDTO.getMonitorUnitId(), configSignalDTO, changedHashMap);
        //4.模板切换
        saveSignalTemplate(oldConfigSignalDTO.getEquipmentTemplateId(), configSignalDTO, changedHashMap, oldConfigSignalDTO);
        //5.生成MU配置同步计划
        dynamicConfigService.generateMUSyncPlan(configSignalDTO.getStationId(),configSignalDTO.getMonitorUnitId(),new Date());
        Equipment applyEquipment = equipmentManager.getEquipmentById(configSignalDTO.getEquipmentId());
        if (null != applyEquipment) {
            OperationRecord operationRecord = new OperationRecord();
            operationRecord.setUserId(userId);
            operationRecord.setOperation(15);//DynamicConfig
            operationRecord.setOperationType(2);//OperationLog
            operationRecord.setStationId(configSignalDTO.getStationId());
            operationRecord.setOperationContent(String.format("DynamicConfig Signal Equipment :%s.%s", applyEquipment.getEquipmentName(), configSignalDTO.getSignalName()));
            operationRecordService.saveOperationRecord(operationRecord);
        }
        return 1;
    }

    @Override
    @Transactional
    public int batchApplyConfigSignalDTOToEquipment(int userId, SignalBatchApplyVO signalBatchApplyVO) {
        List<ConfigSignalDTO> configSignalDTOList = new ArrayList<>();
        for (Integer signalId : signalBatchApplyVO.getSignalIds()) {
            ConfigSignalDTO configSignalDTO = findConfigSignalDTOBySignalId(signalBatchApplyVO.getEquipmentId(), signalId);
            if (null != configSignalDTO) {
                configSignalDTOList.add(configSignalDTO);
            }
        }
        for (String applyEquipmentIdStr : signalBatchApplyVO.getApplyEquipmentIds()) {
            for (ConfigSignalDTO configSignalDTO : configSignalDTOList) {
                List<Integer> applyEquipmentIdList = CharSequenceUtil.split(applyEquipmentIdStr, ",").stream().map(Integer::parseInt).toList();
                if (applyEquipmentIdList.size() != 2) {
                    continue;
                }
                configSignalDTO.setStationId(applyEquipmentIdList.get(0));
                configSignalDTO.setEquipmentId(applyEquipmentIdList.get(1));
                applyConfigSignalDTOToEquipment(userId, configSignalDTO, signalBatchApplyVO.getApplyItems());
                Equipment applyEquipment = equipmentManager.getEquipmentById(configSignalDTO.getEquipmentId());
                if (null != applyEquipment) {
                    OperationRecord operationRecord = new OperationRecord();
                    operationRecord.setUserId(userId);
                    operationRecord.setOperation(15);//DynamicConfig
                    operationRecord.setOperationType(2);//OperationLog
                    operationRecord.setStationId(configSignalDTO.getStationId());
                    operationRecord.setOperationContent(String.format("DynamicConfig Signal Equipment :%s.%s", applyEquipment.getEquipmentName(), configSignalDTO.getSignalName()));
                    operationRecordService.saveOperationRecord(operationRecord);
                }
            }
        }
        return 1;
    }

    @Override
    public List<Signal> findSignalsByTemplateIdAndSignalIds(Integer templateId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return new ArrayList<>();
        }
        return signalMapper.findSignalsByTemplateIdAndSignalIds(templateId,signalIds);
    }

    @Override
    public List<EquipmentSignalDto> findSignalsByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return Collections.emptyList();
        }
        return signalMapper.findSignalsByEquipmentIdAndSignalIds(equipmentId,signalIds);
    }

    @Override
    public void batchInsert(List<Signal> signalList) {
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        signalMapper.batchInsert(signalList);
    }

    @Override
    public Signal findSignalIdByTemplateIdAndSignalName(Integer equipmentTemplateId, String virtualSignalName) {
        return signalMapper.findSignalIdByTemplateIdAndSignalName(equipmentTemplateId, virtualSignalName);
    }

    @Override
    public Signal findSignalIdByTemplateIdAndChannel(Integer equipmentTemplateId, Integer channelNo) {
        return signalMapper.findSignalIdByTemplateIdAndChannel(equipmentTemplateId, channelNo);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        signalMapper.delete(Wrappers.<Signal>lambdaQuery()
                                    .eq(Signal::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public List<SimpleEventSignalDTO> findSignalEvent(Integer equipmentId) {
        return signalMapper.findSignalEvent(equipmentId);
    }

    @Override
    public List<SimpleSignalEquipmentDTO> findSimpleSignalDTOsByRealSignalKeys(List<String> realSignalKeys) {
        if (CollUtil.isEmpty(realSignalKeys)) {
            return Collections.emptyList();
        }
        List<SimpleSignalEquipmentDTO> result = new ArrayList<>(realSignalKeys.size());
        for (String realSignalKey : realSignalKeys) {
            //设备id.信号id
            String[] equipmentSignalSplit = realSignalKey.split("\\.");
            Integer equipmentId = NumberUtil.parseInt(equipmentSignalSplit[0]);
            Integer signalId = NumberUtil.parseInt(equipmentSignalSplit[1]);
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalId);
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            String fullResourceStructureName = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
            SimpleSignalEquipmentDTO dto = new SimpleSignalEquipmentDTO(equipmentId,equipment.getEquipmentName(),
                    fullResourceStructureName, configSignalItem.getSignalName(),
                    configSignalItem.getSignalId());
            result.add(dto);
        }
        return result;
    }

    private int applyConfigSignalDTOToEquipment(int userId, ConfigSignalDTO configSignalDTO, List<Integer> applyItems) {
        //1.找到原有模板信号
        ConfigSignalDTO oldConfigSignalDTO = this.findConfigSignalDTOBySignalId(configSignalDTO.getEquipmentId(), configSignalDTO.getSignalId());
        //2.根据Items比较模板信号的变更点
        //需要根据applyItems来判断更新configSignalDTO对应字段
        ConfigSignalDTO updatedConfigSignalDTO = new ConfigSignalDTO();
        BeanUtils.copyProperties(oldConfigSignalDTO, updatedConfigSignalDTO);
        Map<String, Object> changedHashMap = compareConfigSignalDTOWithItems(configSignalDTO, oldConfigSignalDTO, applyItems, updatedConfigSignalDTO);
        //3.动态配置XML分发，即向TBL_DynamicConfig表中写入动态配置项
        dynamicConfigService.distributeConfigSignal(userId, configSignalDTO.getMonitorUnitId(), updatedConfigSignalDTO, changedHashMap);
        //4.模板切换
        saveSignalTemplate(oldConfigSignalDTO.getEquipmentTemplateId(), updatedConfigSignalDTO, changedHashMap, oldConfigSignalDTO);
        //5.生成MU配置同步计划
        dynamicConfigService.generateMUSyncPlan(updatedConfigSignalDTO.getStationId(),updatedConfigSignalDTO.getMonitorUnitId(),new Date());
        return 1;
    }

    private Map<String, Object> compareConfigSignalDTO(ConfigSignalDTO configSignalDTO, ConfigSignalDTO oldConfigSignalDTO) {
        HashMap<String, Object> changedMap = new HashMap<>();
        if (!configSignalDTO.getSignalName().equals(oldConfigSignalDTO.getSignalName())) {
            changedMap.put(SIGNAL_NAME, configSignalDTO.getSignalName());
        }
        if (!Objects.equals(configSignalDTO.getExpression(), oldConfigSignalDTO.getExpression())) {
            changedMap.put("Expression", configSignalDTO.getExpression());
        }
        if (!Objects.equals(configSignalDTO.getShowPrecision(), oldConfigSignalDTO.getShowPrecision())) {
            changedMap.put("ShowPrecision", configSignalDTO.getShowPrecision());
        }
        if (!Objects.equals(configSignalDTO.getStoreInterval(), oldConfigSignalDTO.getStoreInterval())) {
            changedMap.put(STORE_INTERVAL, configSignalDTO.getStoreInterval());
        }
        if (!Objects.equals(configSignalDTO.getAbsValueThreshold(), oldConfigSignalDTO.getAbsValueThreshold())) {
            changedMap.put(ABS_VALUE_THRESHOLD, configSignalDTO.getAbsValueThreshold());
        }
        if (!Objects.equals(configSignalDTO.getPercentThreshold(), oldConfigSignalDTO.getPercentThreshold())) {
            changedMap.put(PERCENT_THRESHOLD, configSignalDTO.getPercentThreshold());
        }
        if (!Objects.equals(configSignalDTO.getStaticsPeriod(), oldConfigSignalDTO.getStaticsPeriod())) {
            changedMap.put(STATICS_PERIOD, configSignalDTO.getStaticsPeriod());
        }
        String signalPropertyIds = formatSignalPropertyId(configSignalDTO.getSignalPropertyIds());
        if (!signalPropertyIds.equals(formatSignalPropertyId(oldConfigSignalDTO.getSignalPropertyIds()))) {
            changedMap.put("SignalProperty", signalPropertyIds);
        }
        String signalMeanings = formatSignalMeanings(configSignalDTO.getSignalMeanings());
        if (!signalMeanings.equals(formatSignalMeanings(oldConfigSignalDTO.getSignalMeanings()))) {
            changedMap.put("SignalMeaning", signalMeanings);
        }
        return changedMap;
    }

    private Map<String, Object> compareConfigSignalDTOWithItems(ConfigSignalDTO configSignalDTO, ConfigSignalDTO oldConfigSignalDTO, List<Integer> applyItems, ConfigSignalDTO updatedConfigSignalDTO) {
        HashMap<String, Object> changedMap = new HashMap<>();
        //Item为0代表SignalName
        if (applyItems.contains(0) && !configSignalDTO.getSignalName().equals(oldConfigSignalDTO.getSignalName())) {
            changedMap.put(SIGNAL_NAME, configSignalDTO.getSignalName());
            updatedConfigSignalDTO.setSignalName(configSignalDTO.getSignalName());
        }
        //Item为1代表StoreInterval
        if (applyItems.contains(1) && !Objects.equals(configSignalDTO.getStoreInterval(), oldConfigSignalDTO.getStoreInterval())) {
            changedMap.put(STORE_INTERVAL, configSignalDTO.getStoreInterval());
            updatedConfigSignalDTO.setStoreInterval(configSignalDTO.getStoreInterval());
        }
        //Item为2代表AbsValueThreshold
        if (applyItems.contains(2) && !Objects.equals(configSignalDTO.getAbsValueThreshold(), oldConfigSignalDTO.getAbsValueThreshold())) {
            changedMap.put(ABS_VALUE_THRESHOLD, configSignalDTO.getAbsValueThreshold());
            updatedConfigSignalDTO.setAbsValueThreshold(configSignalDTO.getAbsValueThreshold());
        }
        //Item为3代表PercentThreshold
        if (applyItems.contains(3) && !Objects.equals(configSignalDTO.getPercentThreshold(), oldConfigSignalDTO.getPercentThreshold())) {
            changedMap.put(PERCENT_THRESHOLD, configSignalDTO.getPercentThreshold());
            updatedConfigSignalDTO.setPercentThreshold(configSignalDTO.getPercentThreshold());
        }
        //Item为4代表StaticsPeriod
        if (applyItems.contains(4) && !Objects.equals(configSignalDTO.getStaticsPeriod(), oldConfigSignalDTO.getStaticsPeriod())) {
            changedMap.put(STATICS_PERIOD, configSignalDTO.getStaticsPeriod());
            updatedConfigSignalDTO.setStaticsPeriod(configSignalDTO.getStaticsPeriod());
        }
        return changedMap;
    }

    private String formatSignalPropertyId(List<Integer> signalPropertyIds) {
        if (null == signalPropertyIds || signalPropertyIds.isEmpty()) {
            return "";
        }
        return signalPropertyIds.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
    }

    private String formatSignalMeanings(List<SignalMeaningDTO> signalMeaningDTOs) {
        if (null == signalMeaningDTOs || signalMeaningDTOs.isEmpty()) {
            return "";
        }
        return signalMeaningDTOs.stream().sorted(Comparator.comparing(SignalMeaningDTO::getStateValue)).map(SignalMeaningDTO::toString).collect(Collectors.joining(","));
    }

    private void saveSignalTemplate(int sourceTemplateId, ConfigSignalDTO configSignalDTO, Map<String, Object> changedHashMap, ConfigSignalDTO oldConfigSignalDTO) {
        int destTemplateId = primaryKeyValueService.getGlobalIdentity("TBL_EquipmentTemplate", 0);
        int result = this.saveSignalTemplate(configSignalDTO, sourceTemplateId, destTemplateId);
        if (result == 0) {
            destTemplateId = sourceTemplateId;
        }
        //记录设备变更
        String objectId = configSignalDTO.getStationId() + "." + configSignalDTO.getEquipmentId();
        configChangeMacroLogService.saveConfigChangeLog(objectId,3,2);
        //记录设备模板变更
        configChangeMacroLogService.saveConfigChangeLog(String.valueOf(destTemplateId),6,2);
        //刷新本地缓存
        configSignalManager.reloadSignalMap(List.of(destTemplateId));
        if (changedHashMap.containsKey("SignalProperty")) {
            saveSignalProperty(configSignalDTO, oldConfigSignalDTO, destTemplateId);
        }
        if (changedHashMap.containsKey("SignalMeaning")) {
            saveSignalMeanings(configSignalDTO, oldConfigSignalDTO, destTemplateId);
        }
    }

    private void saveSignalProperty(ConfigSignalDTO configSignalDTO, ConfigSignalDTO oldConfigSignalDTO, int destTemplateId) {
        List<Integer> list1 = configSignalDTO.getSignalPropertyIds();
        List<Integer> list2 = oldConfigSignalDTO.getSignalPropertyIds();
        //查找新增的信号属性
        List<Integer> addedList = list1.stream().filter(o -> !list2.contains(o)).toList();
        for (Integer signalPropertyId : addedList) {
            SignalProperty signalProperty = new SignalProperty();
            signalProperty.setEquipmentTemplateId(destTemplateId);
            signalProperty.setSignalId(configSignalDTO.getSignalId());
            signalProperty.setSignalPropertyId(signalPropertyId);
            signalPropertyMapper.insertSignalProperty(signalProperty);
        }
        //查找删除的信号属性
        List<Integer> deletedList = list2.stream().filter(o -> !list1.contains(o)).toList();
        for (Integer signalPropertyId : deletedList) {
            SignalProperty signalProperty = new SignalProperty();
            signalProperty.setEquipmentTemplateId(destTemplateId);
            signalProperty.setSignalId(configSignalDTO.getSignalId());
            signalProperty.setSignalPropertyId(signalPropertyId);
            signalPropertyMapper.deleteSignalProperty(signalProperty);
        }
        String objectId = destTemplateId + "." + configSignalDTO.getSignalId();
        configChangeMacroLogService.saveConfigChangeLog(objectId,7,2);
    }

    private int saveSignalTemplate(ConfigSignalDTO configSignalDTO,Integer sourceTemplateId,Integer destTemplateId){
        Signal signal = Signal.builder()
                              .signalName(configSignalDTO.getSignalName())
                              .expression(configSignalDTO.getExpression())
                              .showPrecision(configSignalDTO.getShowPrecision())
                              .storeInterval(configSignalDTO.getStoreInterval())
                              .absValueThreshold(configSignalDTO.getAbsValueThreshold())
                              .percentThreshold(configSignalDTO.getPercentThreshold())
                              .staticsPeriod(configSignalDTO.getStaticsPeriod())
                              .equipmentTemplateId(sourceTemplateId)
                              .signalId(configSignalDTO.getSignalId())
                              .build();
        this.updateSignal(signal);
        //处理monitorUnitSignalService
        monitorUnitSignalService.expressionHandler(sourceTemplateId,configSignalDTO.getEquipmentId(), configSignalDTO.getSignalId(), configSignalDTO.getExpression());
        //是否为动态配置模板
        if (equipmentTemplateService.dynamicTemplate(sourceTemplateId)) {
            return 0;
        }
        //更新模板
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(sourceTemplateId);
        equipmentTemplate.setMemo(DYNAMIC_MEMO);
        equipmentTemplate.setEquipmentTemplateId(destTemplateId);
        equipmentTemplateService.createEquipmentTemplate(equipmentTemplate);
        //更新monitorUnitSignalService
        //复制信号
        this.copyTemplateSignal(sourceTemplateId, destTemplateId);
        //复制事件
        eventService.copyTemplateEvent(sourceTemplateId, destTemplateId);
        //复制控制命令
        controlService.copyTemplateControl(sourceTemplateId, destTemplateId);
        equipmentService.updateEquipmentTemplateId(configSignalDTO.getEquipmentId(),destTemplateId);
        return 1;
    }

    @Override
    public void copyTemplateSignal(Integer sourceTemplateId, int destTemplateId) {
        //复制信号
        List<Signal> signalList = signalMapper.findSignalsByEquipmentTemplateId(sourceTemplateId);
        if (CollUtil.isEmpty(signalList)) {
            return;
        }
        signalList.forEach(s -> s.setEquipmentTemplateId(destTemplateId));
        this.batchInsert(signalList);
        //复制信号含义
        List<SignalMeanings> signalMeaningsList = signalMeaningsService.findSignalsByEquipmentTemplateId(sourceTemplateId);
        signalMeaningsList.forEach(s -> s.setEquipmentTemplateId(destTemplateId));
        signalMeaningsService.batchInsert(signalMeaningsList);
        //复制信号属性
        List<SignalProperty> signalPropertyList = signalPropertyService.findByEquipmentTemplateId(sourceTemplateId);
        signalPropertyList.forEach(s -> s.setEquipmentTemplateId(destTemplateId));
        signalPropertyService.batchInsert(signalPropertyList);
    }

    @Override
    public List<Signal> findSignalByTemplateIds(Collection<Integer> templateIds) {
        if (CollUtil.isEmpty(templateIds)){
            return Collections.emptyList();
        }
        return signalMapper.selectList(Wrappers.lambdaQuery(Signal.class).in(Signal::getEquipmentTemplateId,templateIds));
    }

    private void updateSignal(Signal signal){
        signalMapper.updateEntity(signal);
    }

    private void saveSignalMeanings(ConfigSignalDTO configSignalDTO, ConfigSignalDTO oldConfigSignalDTO, int destTemplateId) {
        List<Integer> list1 = configSignalDTO.getSignalMeanings().stream().map(SignalMeaningDTO::getStateValue).toList();
        List<Integer> list2 = oldConfigSignalDTO.getSignalMeanings().stream().map(SignalMeaningDTO::getStateValue).toList();
        //查找新增的信号含义
        List<Integer> addedList = list1.stream().filter(o -> !list2.contains(o)).toList();
        //查找删除的信号含义
        List<Integer> deletedList = list2.stream().filter(o -> !list1.contains(o)).toList();
        //查找修改的信号含义
        List<Integer> updatedList = list1.stream().filter(list2::contains).toList();
        for (SignalMeaningDTO signalMeaningDTO : configSignalDTO.getSignalMeanings()) {
            if (addedList.contains(signalMeaningDTO.getStateValue())) {//新增
                SignalMeanings signalMeanings = signalMeaningDTO.build();
                signalMeanings.setEquipmentTemplateId(destTemplateId);
                signalMeaningsMapper.insertSignalMeanings(signalMeanings);
            } else if (updatedList.contains(signalMeaningDTO.getStateValue())) {//修改
                SignalMeanings signalMeanings = signalMeaningDTO.build();
                signalMeanings.setEquipmentTemplateId(destTemplateId);
                signalMeaningsMapper.updateSignalMeanings(signalMeanings);
            } else if (deletedList.contains(signalMeaningDTO.getStateValue())) {//删除
                signalMeaningsMapper.deleteSignalMeaningsById(destTemplateId, signalMeaningDTO.getSignalId(), signalMeaningDTO.getStateValue());
            }
            String objectId = destTemplateId + "." + configSignalDTO.getSignalId() + "." + signalMeaningDTO.getStateValue();
            configChangeMacroLogService.saveConfigChangeLog(objectId,7,2);
        }
    }

}
