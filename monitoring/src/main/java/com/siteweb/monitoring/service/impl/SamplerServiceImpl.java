package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.Sampler;
import com.siteweb.monitoring.mapper.SamplerMapper;
import com.siteweb.monitoring.service.SamplerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("samplerService")
public class SamplerServiceImpl implements SamplerService {

    @Autowired
    private SamplerMapper samplerMapper;

    @Override
    public List<Sampler> findSamplers() {
        return samplerMapper.selectList(null);
    }

    @Override
    public int createSampler(Sampler sampler) {
        return samplerMapper.insert(sampler);
    }

    @Override
    public int deleteById(Integer samplerId) {
        return    samplerMapper.deleteById(samplerId);
    }

    @Override
    public int updateSampler(Sampler sampler) {
        return samplerMapper.updateById(sampler);
    }

    @Override
    public Sampler findById(Integer samplerId) {
        return samplerMapper.selectById(samplerId);
    }
}
