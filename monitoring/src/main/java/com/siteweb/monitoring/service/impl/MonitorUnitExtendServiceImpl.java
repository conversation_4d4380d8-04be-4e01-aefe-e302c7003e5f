package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.AESUtil;
import com.siteweb.monitoring.dto.UserCredentialsDTO;
import com.siteweb.monitoring.mapper.MonitorUnitExtendMapper;
import com.siteweb.monitoring.service.MonitorUnitExtendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MonitorUnitExtendServiceImpl implements MonitorUnitExtendService {
    @Autowired
    MonitorUnitExtendMapper monitorUnitExtendMapper;
    @Autowired
    AESUtil aesUtil;
    @Value("${aes-key}")
    private String aesKey;

    @Override
    public UserCredentialsDTO findUserCredentials(String ip) {
        String userCredentialsString = monitorUnitExtendMapper.findExtendFiled1ByIp(ip);
        if (CharSequenceUtil.isBlank(userCredentialsString)) {
            log.warn("ip:{},没有下发过配置，所以没有存储过远程用户名与密码", ip);
            return null;
        }
        String[] decrypt = aesUtil.decrypt(userCredentialsString, aesKey).split(":");
        String username = decrypt[0];
        String password = decrypt[1];
        Integer port = Integer.valueOf(decrypt[2]);
        String protocol = decrypt[3];
        return new UserCredentialsDTO(username, password, port, protocol);
    }
}
