package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.SamplerUnit;

import java.util.Collection;
import java.util.List;

public interface SamplerUnitService{

    List<SamplerUnit> findSamplerUnits();

    int createSamplerUnit(SamplerUnit samplerUnit);

    int deleteById(Integer samplerUnitId);
    int updateSamplerUnit(SamplerUnit samplerUnit);

    SamplerUnit findById(Integer id);

    SamplerUnit findBySamplerUnitId(Integer samplerUnitId);

    List<SamplerUnit> findByMonitUnitIdAndPortId(Integer monitUnitId, Integer portId);

    SamplerUnit findSamplerUnit(Integer monitorUnitId, String unitName,String portName);

    List<SamplerUnit> findByIds(Collection<Integer> sampleUnitIds);

    List<SamplerUnit> findSamplerUnitsByMonitorUnitIdAndSamplerUnitIdSql(Collection<String> batSql);

    List<SamplerUnit> findByEquipmentIds(Collection<Integer> integerStream);

    List<SamplerUnit> findSamplerUnitsByMonitorUnitId(Integer monitorUnitId);
}

