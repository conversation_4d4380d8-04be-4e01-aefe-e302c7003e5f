package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.ActiveControlOfDoor;
import com.siteweb.monitoring.mapper.ActiveControlOfDoorMapper;
import com.siteweb.monitoring.service.ActiveControlOfDoorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("activeControlOfDoorService")
public class ActiveControlOfDoorServiceImpl implements ActiveControlOfDoorService {

    @Autowired
    private ActiveControlOfDoorMapper activeControlOfDoorMapper;

    @Override
    public List<ActiveControlOfDoor> findActiveControlOfDoors() {
        return activeControlOfDoorMapper.selectList(null);
    }

    @Override
    public int createActiveControlOfDoor(ActiveControlOfDoor activeControlOfDoor) {
        return activeControlOfDoorMapper.insert(activeControlOfDoor);
    }

    @Override
    public int deleteById(Integer activeControlOfDoorId) {
        return    activeControlOfDoorMapper.deleteById(activeControlOfDoorId);
    }

    @Override
    public int updateActiveControlOfDoor(ActiveControlOfDoor activeControlOfDoor) {
        return activeControlOfDoorMapper.updateById(activeControlOfDoor);
    }

    @Override
    public ActiveControlOfDoor findById(Integer activeControlOfDoorId) {
        return activeControlOfDoorMapper.selectById(activeControlOfDoorId);
    }
}
