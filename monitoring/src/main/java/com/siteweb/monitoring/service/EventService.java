package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.EquipmentEventDto;
import com.siteweb.monitoring.dto.SimpleEventDTO;
import com.siteweb.monitoring.dto.SimpleEventSignalDTO;
import com.siteweb.monitoring.entity.Event;
import com.siteweb.monitoring.vo.EventRequestByCondition;
import com.siteweb.monitoring.vo.EventRequestBySignalId;
import com.siteweb.monitoring.vo.MultiEquipmentEventConditionBatchApplyVO;
import com.siteweb.monitoring.vo.SelfEquipmentEventConditionBatchApplyVO;

import java.util.List;

public interface EventService {

    List<ConfigEventDTO> findByStationIdAndEquipmentId(int stationId, int equipmentId);

    List<SimpleEventDTO> findAllSimpleEventDTOs();

    List<SimpleEventDTO> findSimpleEventDTOsByEquipmentId(int equipmentId);

    ConfigEventDTO findConfigEventDTOByEventId(int equipmentId, int eventId);

    ConfigEventDTO findConfigEventDTOBySignalId(int equipmentId, int signalId);

    int updateConfigEventDTO(int userId, ConfigEventDTO configEventDTO);

    List<Integer> batchApplyEventConditionToSelfEquipment(int userId, SelfEquipmentEventConditionBatchApplyVO selfEquipmentEventConditionBatchApplyVO);

    int batchApplyEventConditionToMultiEquipment(int userId, MultiEquipmentEventConditionBatchApplyVO multiEquipmentEventConditionBatchApplyVO);

    List<EquipmentEventDto> findEventsByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds);

    void batchInsert(List<Event> eventList);

    /**
     * 删除事件根据设备模板id
     * @param equipmentTemplateId 设备模板id
     */
    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    List<SimpleEventSignalDTO> findEventsByEquipmentIdAndSignalIds(EventRequestBySignalId eventRequestBySignalId);

    List<SimpleEventDTO> findSimpleEventDTOsByUserId(Integer userId);

    List<Event> findByEquipmentTemplateId(Integer equipmentTemplateId);

    /**
     *  复制模板事件相关数据，将源模板上的事件相关数据添加到目标模板上
     *
     * @param sourceTemplateId 源模板id
     * @param destTemplateId   目标目标
     */
    void copyTemplateEvent(Integer sourceTemplateId, Integer destTemplateId);

    List<SimpleEventDTO> findEventsByEventRequestByCondition(EventRequestByCondition eventRequestByCondition);
}

