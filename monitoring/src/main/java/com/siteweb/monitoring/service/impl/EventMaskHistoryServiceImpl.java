package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.EventMaskHistory;
import com.siteweb.monitoring.mapper.EventMaskHistoryMapper;
import com.siteweb.monitoring.service.EventMaskHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EventMaskHistoryServiceImpl implements EventMaskHistoryService {
    @Autowired
    EventMaskHistoryMapper eventMaskHistoryMapper;

    @Override
    public boolean existsBySequenceId(String sequenceId){
        return eventMaskHistoryMapper.exists(Wrappers.lambdaQuery(EventMaskHistory.class)
                                                     .eq(EventMaskHistory::getSequenceId, sequenceId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(EventMaskHistory eventMaskHistory) {
        eventMaskHistoryMapper.insert(eventMaskHistory);
    }

    @Override
    public Set<String> findNotInBySequenceIds(List<String> sequenceIds) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return Collections.emptySet();
        }
        List<EventMaskHistory> eventMaskHistoryList = eventMaskHistoryMapper.selectList(Wrappers.<EventMaskHistory>lambdaQuery()
                                                                                                .select(EventMaskHistory::getSequenceId)
                                                                                                .in(EventMaskHistory::getSequenceId, sequenceIds));
        Set<String> maskSequenceIdSet = eventMaskHistoryList.stream().map(EventMaskHistory::getSequenceId)
                                                  .collect(Collectors.toSet());
        Set<String> result = new HashSet<>();
        for (String sequenceId : sequenceIds) {
            if (!maskSequenceIdSet.contains(sequenceId)) {
                result.add(sequenceId);
            }
        }
        return result;
    }

    @Override
    public void batchInsert(List<EventMaskHistory> eventMaskHistoryInsertList) {
        if (CollUtil.isEmpty(eventMaskHistoryInsertList)) {
            return;
        }
        eventMaskHistoryMapper.batchInsert(eventMaskHistoryInsertList);
    }
}
