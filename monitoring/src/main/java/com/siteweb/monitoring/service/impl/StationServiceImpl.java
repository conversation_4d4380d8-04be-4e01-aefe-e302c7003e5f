package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.service.AreaService;
import com.siteweb.monitoring.dto.StationConditionFilterDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.StationMapper;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("stationService")
public class StationServiceImpl implements StationService {

    @Autowired
    private StationMapper stationMapper;
    @Autowired
    private StationManager stationManager;
    @Autowired
    private AreaService areaService;
    @Autowired
    private ResourceStructureService resourceStructureService;

    @Override
    public List<Station> findStations() {
        return stationMapper.selectList(null);
    }

    @Override
    public int createStation(Station station) {
        return stationMapper.insert(station);
    }

    @Override
    public int deleteById(Integer stationId) {
        return    stationMapper.deleteById(stationId);
    }

    @Override
    public int updateStation(Station station) {
        return stationMapper.updateById(station);
    }

    @Override
    public Station findById(Integer stationId) {
        return stationManager.findStationById(stationId);
    }

    @Override
    public List<Station> findByUserId(Integer userId) {
        boolean allRegion = areaService.isAllRegion(userId);
        // 所有片区权限
        if (allRegion) {
            return stationManager.findAll();
        }
        // 根据用户查询片区关联局站
        List<Integer> stationIds = stationMapper.findStationIdsByUserId(userId);
        return stationManager.findByStationIds(stationIds);
    }

    @Override
    public Set<Integer> findByRegionPermission(Integer userId) {
        List<ResourceStructure> resourceStructureByUserId = resourceStructureService.findResourceStructureByUserId(userId);
        return resourceStructureByUserId.stream()
                                        .filter(e -> Objects.equals(e.getStructureTypeId(), SourceType.STATION.value()))
                                        .map(ResourceStructure::getOriginId)
                                        .collect(Collectors.toSet());
    }

    @Override
    public Map<Integer, Set<Integer>> findStationGroupByRegionPermission(Integer userId) {
        Set<Integer> dxStructureType = Set.of(SourceType.SSCENTER.value(), SourceType.DISTRICT.value(), SourceType.STATION.value());
        List<ResourceStructure> resourceStructureByUserId = resourceStructureService.findResourceStructureByUserId(userId);
        return resourceStructureByUserId.stream()
                                        .filter(e -> dxStructureType.contains(e.getStructureTypeId()))
                                        .collect(Collectors.groupingBy(ResourceStructure::getStructureTypeId, Collectors.mapping(ResourceStructure::getOriginId, Collectors.toSet())));
    }

    @Override
    public List<Integer> findStationIdsByUserId(Integer userId) {
        List<Station> stationList = this.findByUserId(userId);
        if (CollUtil.isEmpty(stationList)) {
            return Collections.emptyList();
        }
        return stationList.stream().map(Station::getStationId).toList();
    }

    @Override
    public List<Station> findByIds(Collection<Integer> stationIds) {
        return stationManager.findByStationIds(stationIds);
    }

    @Override
    public List<Station> findByCondition(Integer userId, StationConditionFilterDTO stationConditionFilterDTO) {
        List<Integer> stationIds = findStationIdsByUserId(userId);
        stationConditionFilterDTO.setStationIds(new HashSet<>(stationIds));
        return stationManager.findByCondition(stationConditionFilterDTO);
    }

    @Override
    public Integer findStationBaseTypeByStationIdAndStandardVer(Integer stationId, Integer standardVer) {
        return stationMapper.findStationBaseTypeByStationIdAndStandardVer(stationId, standardVer);
    }

    @Override
    public int findMaintainState(Integer stationId, Date startTime) {
        Station station = this.findById(stationId);
        if (ObjectUtil.notEqual(station.getStationState(), 3)) {
            return 1;
        }
        if (ObjectUtil.isNotNull(station.getStartTime()) && DateUtil.isIn(startTime, station.getStartTime(), station.getEndTime())) {
            return 0;
        }
        return 1;
    }
}
