package com.siteweb.monitoring.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.HexUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.AlarmMaskLogMapper;
import com.siteweb.monitoring.mapper.EquipmentMaskMapper;
import com.siteweb.monitoring.mapper.TimeGroupSpanMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.util.MaskUtil;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import com.siteweb.monitoring.vo.EquipmentMaskVO;
import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service("equipmentMaskService")
public class EquipmentMaskServiceImpl implements EquipmentMaskService {
    public static final int MAX_CREATE_EQUIPMENT_MASK = 1500;

    @Autowired
    EquipmentMaskMapper equipmentMaskMapper;

    @Autowired
    AlarmMaskLogMapper alarmMaskLogMapper;

    @Autowired
    TimeGroupSpanMapper timeGroupSpanMapper;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    @Lazy
    EquipmentStateManager equipmentStateManager;
    @Autowired
    @Lazy
    EquipmentService equipmentService;
    @Lazy
    @Autowired
    EquipmentMaskService equipmentMaskService;
    @Lazy
    @Autowired
    ActiveEventService activeEventService;
    @Lazy
    @Autowired
    EventMaskService eventMaskService;
    @Autowired
    EventMaskHistoryService eventMaskHistoryService;
    @Autowired
    TimeGroupSpanService timeGroupSpanService;
    @Autowired
    AlarmMaskLogService alarmMaskLogService;

    public List<EquipmentMask> findByEquipmentIds(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return equipmentMaskMapper.selectList(Wrappers.<EquipmentMask>lambdaQuery().in(EquipmentMask::getEquipmentId, equipmentIds));
    }


    @Override
    @Transactional
    public void saveEquipmentMask(EquipmentMaskVO equipmentMaskVO, Integer userId) {
        Integer stationId = equipmentMaskVO.getStationId();
        Integer equipmentId = equipmentMaskVO.getEquipmentId();
        Integer timeGroupId = equipmentMaskVO.getTimeGroupId();
        Date startTime = equipmentMaskVO.getStartTime();
        Date endTime = equipmentMaskVO.getEndTime();
        String reason = equipmentMaskVO.getReason();
        StringBuilder timeGroupChars = null;
        if (Integer.valueOf(1).equals(equipmentMaskVO.getTimeGroupCategory())) {//全时段屏蔽
            equipmentMaskService.doSaveEquipmentMask(stationId, equipmentId, timeGroupId, startTime, endTime, userId, reason);
        } else {
            timeGroupChars = new StringBuilder();
            for (TimeGroupSpanVO timeGroupSpanVO : equipmentMaskVO.getTimeGroupSpans()) {
                Integer week = timeGroupSpanVO.getWeek();
                String timeGroupChar = HexUtil.booleanListToHexString(timeGroupSpanVO.getTimeSpanBool());
                equipmentMaskService.doSaveSeparateEquipmentMask(stationId, equipmentId, timeGroupId, timeGroupChar, week, userId, reason);
                timeGroupChars.append(week);
                timeGroupChars.append(":");
                timeGroupChars.append(timeGroupChar);
                timeGroupChars.append(",");
            }
        }
        //operationType为1是指新增屏蔽
        equipmentMaskService.insertAlarmMaskLog(equipmentMaskVO.build(), 1, equipmentMaskVO.getTimeGroupCategory(), CharSequenceUtil.str(timeGroupChars), userId);
    }


    @Override
    @Transactional
    public void batchCreateEquipmentMasks(BatchEquipmentMaskVO vo, Integer userId) {
        //全时段屏蔽
        batchHandlerFullTimeMask(vo, userId);
        if (Objects.equals(vo.getTimeGroupCategory(), TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue())) {
            //与全时段相比 多了一个批量处理分时段屏蔽字符串的表的逻辑
            timeGroupSpanService.batchInsertEquipmentTimeGroupSpan(vo);
        }
        //添加屏蔽日志
        alarmMaskLogService.batchInsertEquipmentMaskLog(vo, userId);
    }

    private void batchHandlerFullTimeMask(BatchEquipmentMaskVO vo, Integer userId) {
        // 先删除
        deleteTimeGroupSpanByEquipmentIds(vo.getEquipmentIds());
        equipmentMaskMapper.deleteByIds(vo.getEquipmentIds());
        // 再新增
        List<EquipmentMask> insertList = new ArrayList<>();
        for (int i = 0; i < vo.getEquipmentIds().size(); i++) {
            Integer stationId = vo.getStationIds().get(i);
            Integer equipmentId = vo.getEquipmentIds().get(i);
            EquipmentMask equipmentMask = equipmentMaskBuild(vo, equipmentId, stationId,userId);
            insertList.add(equipmentMask);
        }
        //结束系统中当前的活动告警
        eventMaskService.saveEndEventByEquipmentIds(vo.getEquipmentIds());
        //批量添加与插入
        batchInsert(insertList);
    }

    private void deleteTimeGroupSpanByEquipmentIds(List<Integer> equipmentIds) {
        List<Integer> timeGroupSpanIds = timeGroupSpanMapper.findTimeSpanIdsByEquipmentIds(equipmentIds);
        if (CollUtil.isEmpty(timeGroupSpanIds)) {
            return;
        }
        timeGroupSpanMapper.deleteByIds(timeGroupSpanIds);
    }

    private EquipmentMask equipmentMaskBuild(BatchEquipmentMaskVO vo, Integer equipmentId, Integer stationId, Integer userId) {
        //是否是分时段屏蔽
        boolean isTimeGroupSpan = Objects.equals(vo.getTimeGroupCategory(), TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue());
        return EquipmentMask.builder()
                            .equipmentId(equipmentId)
                            .stationId(stationId)
                            .timeGroupId(Integer.parseInt("1" + Math.abs(equipmentId)))
                            .startTime(isTimeGroupSpan ? null : vo.getStartTime())
                            .endTime(isTimeGroupSpan ? null : vo.getEndTime())
                            .reason(vo.getReason())
                            .userId(userId)
                            .build();
    }

    private void batchInsert(List<EquipmentMask> insertList) {
        if (CollUtil.isEmpty(insertList)) {
            return;
        }
        equipmentMaskMapper.batchInsert(insertList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAlarmMaskLog(EquipmentMask equipmentMask, int operationType, Integer timeGroupCategory, String timeGroupChars, int userId) {
        AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
        alarmMaskLog.setStationId(equipmentMask.getStationId());
        alarmMaskLog.setEquipmentId(equipmentMask.getEquipmentId());
        alarmMaskLog.setEventId(-1);
        alarmMaskLog.setUserId(userId);
        alarmMaskLog.setOperationType(operationType);
        alarmMaskLog.setOperationTime(new Date());
        alarmMaskLog.setTimeGroupCategory(timeGroupCategory);
        alarmMaskLog.setStartTime(equipmentMask.getStartTime());
        alarmMaskLog.setEndTime(equipmentMask.getEndTime());
        Equipment equipment = Optional.ofNullable(equipmentService.findById(equipmentMask.getEquipmentId())).orElse(new Equipment());
        alarmMaskLog.setResourceStructureId(equipment.getResourceStructureId());
        if (operationType == 1 && Integer.valueOf(2).equals(timeGroupCategory)) {//新增分时段屏蔽
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
        }
        alarmMaskLog.setComment(equipmentMask.getReason());
        alarmMaskLogMapper.insertEntity(alarmMaskLog);
    }

    @Override
    public Page<EquipmentMaskDTO> findEquipmentMaskByKeywords(EquipmentMaskFilterDTO equipmentMaskFilterDTO, Page<EquipmentMaskDTO> page) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return com.baomidou.mybatisplus.extension.plugins.pagination.Page.of(page.getCurrent(), page.getSize());
        }
        page.setSearchCount(false);
        equipmentMaskFilterDTO.setEquipmentIdList(equipmentIds);
        Page<EquipmentMaskDTO> maskDTOPage = equipmentMaskMapper.findEquipmentMaskByKeywordsPage(page,equipmentMaskFilterDTO);
        long total = equipmentMaskMapper.findEquipmentMaskByKeywordsPageCount(equipmentMaskFilterDTO);
        maskDTOPage.setTotal(total);
        return maskDTOPage;
    }

    @Override
    @Transactional
    public int deleteEquipmentMask(EquipmentMask equipmentMask, Integer userId) {
        int timeGroupId = Integer.parseInt("1" + equipmentMask.getEquipmentId());
        equipmentMaskMapper.deleteEquipmentMask(equipmentMask.getStationId(), equipmentMask.getEquipmentId(), timeGroupId);
        //operationType为2是指解除屏蔽
        insertAlarmMaskLog(equipmentMask, 2, null, null, userId);
        return 1;
    }

    @Override
    @Transactional
    public void batchDeleteEquipmentMasks(List<Integer> equipmentIds, Integer userId) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        List<EquipmentMask> equipmentMasks = findByEquipmentIds(equipmentIds);
        List<Integer> timeGroupIds = equipmentMasks.stream().map(e -> Integer.parseInt("1" + e.getEquipmentId())).toList();
        equipmentMaskMapper.delete(Wrappers.lambdaQuery(EquipmentMask.class).in(EquipmentMask::getTimeGroupId, timeGroupIds));
        timeGroupSpanService.deleteByTimeGroupIds(timeGroupIds);
        alarmMaskLogService.equipmentMaskCancelLog(userId, equipmentMasks);
    }

    @Override
    @Transactional
    public void batchDeleteEquipmentMasksByResourceStructureId(Integer resourceStructureId, Integer userId) {
        List<EquipmentMask> equipmentMasks = equipmentMaskMapper.findEquipmentMasksByResourceStructureId(resourceStructureId);
        if (null == equipmentMasks || equipmentMasks.isEmpty()) {
            return;
        }
        for (EquipmentMask equipmentMask : equipmentMasks) {
            int timeGroupId = Integer.parseInt("1" + equipmentMask.getEquipmentId());
            equipmentMaskMapper.deleteEquipmentMask(equipmentMask.getStationId(), equipmentMask.getEquipmentId(), timeGroupId);
            //operationType为2是指解除屏蔽
            insertAlarmMaskLog(equipmentMask, 2, null, null, userId);
        }
    }

    @Override
    @Transactional
    public void deleteAllEquipmentMasks(Integer userId) {
        List<EquipmentMask> equipmentMasks = equipmentMaskMapper.selectList(null);
        for (EquipmentMask equipmentMask : equipmentMasks) {
            int timeGroupId = Integer.parseInt("1" + equipmentMask.getEquipmentId());
            equipmentMaskMapper.deleteEquipmentMask(equipmentMask.getStationId(), equipmentMask.getEquipmentId(), timeGroupId);
            //operationType为2是指解除屏蔽
            insertAlarmMaskLog(equipmentMask, 2, null, null, userId);
        }
    }

    @Override
    public EquipmentMask findById(Integer equipmentId) {
        return equipmentMaskMapper.selectById(equipmentId);
    }

    @Override
    public EquipmentMaskDTO findEquipmentMaskDTOByEquipmentId(Integer equipmentId) {
        EquipmentMaskDTO equipmentMaskDTO = equipmentMaskMapper.findEquipmentMaskDTOByEquipmentId(equipmentId);
        if (null != equipmentMaskDTO) {
            List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.findByTimeGroupId(equipmentMaskDTO.getTimeGroupId());
            if (!timeGroupSpans.isEmpty()) {
                equipmentMaskDTO.setTimeGroupCategory(2);//分时段屏蔽
            } else {
                equipmentMaskDTO.setTimeGroupCategory(1);//全时段屏蔽
            }
            List<TimeGroupSpanDTO> timeGroupSpanDTOList = new ArrayList<>();
            for (TimeGroupSpan timeGroupSpan : timeGroupSpans) {
                TimeGroupSpanDTO timeGroupSpanDTO = MaskUtil.parserTimeGroupSpan(timeGroupSpan);
                timeGroupSpanDTOList.add(timeGroupSpanDTO);
            }
            equipmentMaskDTO.setTimeGroupSpans(timeGroupSpanDTOList);
        }
        return equipmentMaskDTO;
    }

    @Override
    public List<SimpleEquipmentMaskDTO> findSimpleEquipmentMaskDTOsByResourceStructureId(Integer resourceStructureId) {
        List<SimpleEquipmentMaskDTO> simpleEquipmentMaskDTOs = equipmentMaskMapper.findSimpleEquipmentMaskDTOsByResourceStructureId(resourceStructureId);
        this.setMaskState(simpleEquipmentMaskDTOs);
        return simpleEquipmentMaskDTOs;
    }

    private void setMaskState(List<SimpleEquipmentMaskDTO> simpleEquipmentMaskDTOs) {
        for (SimpleEquipmentMaskDTO simpleEquipmentMaskDTO : simpleEquipmentMaskDTOs) {
            if (Boolean.TRUE.equals(simpleEquipmentMaskDTO.getMask())) {
                //设置了屏蔽状态,判断是否在生效期
                Boolean equipmentMaskState = equipmentStateManager.getEquipmentMaskStatesById(simpleEquipmentMaskDTO.getEquipmentId());
                simpleEquipmentMaskDTO.setEffective(equipmentMaskState);
            }
        }
    }

    @Override
    public Set<Integer> findIntervalMaskEquipmentIds() {
        //分时段屏蔽
        Set<Integer> maskEquipmentIds = new HashSet<>();
        int week = DateUtil.thisDayOfWeek();
        List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanMapper.findEquipmentMaskByWeek(week);
        //获取当天的分钟数
        int minute = (DateUtil.hour(new Date(), true)) * 60 + DateUtil.thisMinute();
        int index = minute / 30;
        for (TimeGroupSpan timeGroupSpan : timeGroupSpanList) {
            //获取当前的时间，查看是否正在屏蔽状态
            List<Boolean> booleans = HexUtil.hexStringToBooleanList(timeGroupSpan.getTimeSpanChar());
            if (Boolean.TRUE.equals(booleans.get(index))){
                Integer equipmentId = Integer.valueOf(timeGroupSpan.getTimeGroupId().toString().substring(1));
                maskEquipmentIds.add(equipmentId);
            }
        }
        return maskEquipmentIds;
    }

    @Override
    public Page<SimpleEquipmentMaskDTO> findSimpleEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterDTO equipmentMaskFilterDTO, Page<SimpleEquipmentMaskDTO> page) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        equipmentMaskFilterDTO.setEquipmentIdList(equipmentIds);
        Set<Integer> childrenIds = resourceStructureManager.getAllChildrenId(StringUtils.splitToIntegerList(equipmentMaskFilterDTO.getResourceStructureIds()));
        equipmentMaskFilterDTO.setResourceStructureIdList(childrenIds);
        equipmentMaskFilterDTO.setEquipmentBaseTypeIdList(StringUtils.splitToIntegerList(equipmentMaskFilterDTO.getEquipmentBaseTypeIds()));
        equipmentMaskFilterDTO.setEquipmentCategoryIdList(StringUtils.splitToIntegerList(equipmentMaskFilterDTO.getEquipmentCategories()));
        page.setSearchCount(false);
        Page<SimpleEquipmentMaskDTO> maskDTOPage = equipmentMaskMapper.findSimpleEquipmentMaskByEquipmentBaseTypesPage(page, equipmentMaskFilterDTO);
        this.setMaskState(maskDTOPage.getRecords());
        long total = equipmentMaskMapper.findEquipmentMaskByEquipmentBaseTypesPageCount(equipmentMaskFilterDTO);
        maskDTOPage.setTotal(total);
        return maskDTOPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterDTO equipmentMaskFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        equipmentMaskFilterDTO.setEquipmentIdList(equipmentIds);
        Set<Integer> childrenIds = resourceStructureManager.getAllChildrenId(StringUtils.splitToIntegerList(equipmentMaskFilterDTO.getResourceStructureIds()));
        equipmentMaskFilterDTO.setResourceStructureIdList(childrenIds);
        equipmentMaskFilterDTO.setEquipmentBaseTypeIdList(StringUtils.splitToIntegerList(equipmentMaskFilterDTO.getEquipmentBaseTypeIds()));
        equipmentMaskFilterDTO.setEquipmentCategoryIdList(StringUtils.splitToIntegerList(equipmentMaskFilterDTO.getEquipmentCategories()));
        List<EquipmentMask> equipmentMaskList = equipmentMaskMapper.findEquipmentMaskByEquipmentBaseTypes(equipmentMaskFilterDTO);
        for (EquipmentMask equipmentMask : equipmentMaskList) {
            this.deleteEquipmentMask(equipmentMask, userId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterCreateDTO equipmentMaskFilterCreateDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        equipmentMaskFilterCreateDTO.setEquipmentIdList(equipmentIds);
        Set<Integer> childrenIds = resourceStructureManager.getAllChildrenId(equipmentMaskFilterCreateDTO.getResourceStructureIdList());
        equipmentMaskFilterCreateDTO.setResourceStructureIdList(childrenIds);
        EquipmentMaskFilterDTO equipmentMaskFilterDTO = BeanUtil.copyProperties(equipmentMaskFilterCreateDTO, EquipmentMaskFilterDTO.class);
        List<EquipmentMask> equipmentMaskList = equipmentMaskMapper.findEquipmentMaskCreateByEquipmentBaseTypes(equipmentMaskFilterDTO);
        if (CollUtil.size(equipmentMaskList) > MAX_CREATE_EQUIPMENT_MASK) {
            log.error("设备屏蔽数据量超过1500条，请缩小范围");
        }
        BatchEquipmentMaskVO batchEquipmentMaskVO = new BatchEquipmentMaskVO();
        List<Integer> maskStationIds = new ArrayList<>();
        List<Integer> maskEquipmentIds = new ArrayList<>();
        for (EquipmentMask equipmentMask : equipmentMaskList) {
            maskStationIds.add(equipmentMask.getStationId());
            maskEquipmentIds.add(equipmentMask.getEquipmentId());
        }
        batchEquipmentMaskVO.setStartTime(equipmentMaskFilterCreateDTO.getStartTime());
        batchEquipmentMaskVO.setEndTime(equipmentMaskFilterCreateDTO.getEndTime());
        batchEquipmentMaskVO.setReason(equipmentMaskFilterCreateDTO.getReason());
        batchEquipmentMaskVO.setTimeGroupCategory(equipmentMaskFilterCreateDTO.getTimeGroupCategory());
        batchEquipmentMaskVO.setStationIds(maskStationIds);
        batchEquipmentMaskVO.setEquipmentIds(maskEquipmentIds);
        batchEquipmentMaskVO.setTimeGroupSpans(equipmentMaskFilterCreateDTO.getTimeGroupSpans());
        batchCreateEquipmentMasks(batchEquipmentMaskVO, userId);
    }

    @Override
    public boolean isMaskEffective(Integer equipmentId) {
        EquipmentMaskDTO equipmentMask = equipmentMaskMapper.findEquipmentMaskDTOByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentMask)) {
            return false;
        }
        List<TimeGroupSpan> timeGroupSpans = timeGroupSpanMapper.findByTimeGroupId(equipmentMask.getTimeGroupId());
        //处于全时段屏蔽
        if (CollUtil.isEmpty(timeGroupSpans) && DateUtil.isIn(new Date(), equipmentMask.getStartTime(), equipmentMask.getEndTime())) {
            return true;
        }
        //处于分时段屏蔽
        TimeGroupSpan timeGroupSpan = timeGroupSpanMapper.findByWeekAndTimeGroupId(DateUtil.thisDayOfWeek(), equipmentMask.getTimeGroupId());
        return ObjectUtil.isNotNull(timeGroupSpan) && MaskUtil.isIntervalMask(timeGroupSpan.getTimeSpanChar());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSaveEquipmentMask(Integer stationId, Integer equipmentId, Integer timeGroupId, Date startTime, Date endTime, Integer userId, String reason) {
        EquipmentMask equipmentMask = this.findStationIdAndEqId(stationId, equipmentId);
        if (ObjectUtil.isNotEmpty(equipmentMask)) {
            // 如果存在分时段屏蔽，则删除
            List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanMapper.findByStationIdAndEquipmentId(stationId, equipmentId);
            if (CollUtil.isNotEmpty(timeGroupSpanList)) {
                List<Integer> timeGroupSpanIds = timeGroupSpanList.stream().map(TimeGroupSpan::getTimeSpanId).toList();
                timeGroupSpanMapper.deleteByIds(timeGroupSpanIds);
            }
        }
        equipmentMaskService.saveOrUpdateEventMask(stationId, equipmentId, timeGroupId, userId, reason, startTime, endTime);
        // 结束存量活动告警
        equipmentMaskService.endStockActiveEventByEventMask(stationId, equipmentId, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSaveSeparateEquipmentMask(Integer stationId, Integer equipmentId, Integer timeGroupId, String timeGroupChar, Integer week, Integer userId, String reason) {
        equipmentMaskService.saveOrUpdateEventMask(stationId, equipmentId, timeGroupId, userId, reason, null, null);
        timeGroupSpanService.saveOrUpdateTimeGroupSpan(timeGroupId,timeGroupChar,week);
        // 结束存量活动告警
        equipmentMaskService.endStockActiveEventByEventMask(stationId, equipmentId, true);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateEventMask(Integer stationId, Integer equipmentId, Integer timeGroupId, Integer userId, String reason, Date startTime, Date endTime) {
        EquipmentMask equipmentMask = this.findStationIdAndEqId(stationId, equipmentId);
        if (ObjectUtil.isNotEmpty(equipmentMask)) {
            LambdaUpdateWrapper<EquipmentMask> updateWrapper = Wrappers.lambdaUpdate(EquipmentMask.class)
                    .set(EquipmentMask::getStartTime, startTime)
                    .set(EquipmentMask::getEndTime, endTime)
                    .set(EquipmentMask::getReason, reason)
                    .set(EquipmentMask::getUserId, userId)
                    .set(EquipmentMask::getTimeGroupId, timeGroupId)
                    .eq(EquipmentMask::getStationId, stationId)
                    .eq(EquipmentMask::getEquipmentId, equipmentId);
            equipmentMaskMapper.update(null, updateWrapper);
        }else {
            EquipmentMask entity = new EquipmentMask();
            entity.setStationId(stationId);
            entity.setEquipmentId(equipmentId);
            entity.setTimeGroupId(timeGroupId);
            entity.setReason(reason);
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            entity.setUserId(userId);
            equipmentMaskMapper.insert(entity);
        }
    }

    @Override
    public EquipmentMask findStationIdAndEqId(Integer stationId, Integer equipmentId) {
        return equipmentMaskMapper.selectOne(Wrappers.lambdaQuery(EquipmentMask.class).eq(EquipmentMask::getStationId, stationId).eq(EquipmentMask::getEquipmentId, equipmentId));
    }

    /**
     * 结束存量活动告警
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endStockActiveEventByEventMask(Integer stationId, Integer equipmentId, boolean isSeparate) {
        List<ActiveEvent> activeEventList = activeEventService.getDBActiveEventByCondition(stationId, equipmentId,null);
        for (ActiveEvent activeEvent : activeEventList) {
            Integer eventId = activeEvent.getEventId();
            String sequenceId = activeEvent.getSequenceId();
            Date currentTime = new Date();
            eventMaskService.saveEndEventByMask(stationId, equipmentId, eventId, sequenceId, currentTime);
            // 全时段
            if (!isSeparate) {
                // 存在则跳过
                if (eventMaskHistoryService.existsBySequenceId(sequenceId)) {
                    continue;
                }
            }
            // 分时段或者全时段不存在时则插入
            eventMaskHistoryService.create(BeanUtil.toBean(activeEvent, EventMaskHistory.class));
        }
    }
}
