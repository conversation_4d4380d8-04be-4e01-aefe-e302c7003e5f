package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.vo.EquipmentFilterVo;
import com.siteweb.monitoring.vo.EquipmentStatisticsVO;
import com.siteweb.monitoring.vo.ToDynamicApplyEquipmentVO;

import java.util.List;
import java.util.Set;

public interface EquipmentService {

    /**
     * 查找所有设备
     *
     * @return {@link List}<{@link Equipment}>
     */
    List<Equipment> findEquipments();

    List<EquipmentDTO> findEquipmentDTOs();

    Equipment findById(Integer equipmentId);

    /**
     * 往数据库查新设备信息
     * @param equipmentId 设备id
     * @return {@link Equipment}
     */
    Equipment findByIdFromDB(Integer equipmentId);

    /**
     * 根据过滤条件查询设备
     *
     * @param filterVo 过滤条件dto
     * @return {@link List}<{@link EquipmentDTO}>
     */
    List<EquipmentDTO> findEquipmentDTOs(Integer userId,EquipmentFilterVo filterVo);

    /**
     * 通过层级Id获取设备信息
     *
     * @param resourceStructureId 层级Id
     * @return {@link List }<{@link Equipment }>
     */
    List<Equipment> findEquipmentsByResourceStructureId(Integer resourceStructureId);

    /**
     * 递归获取层级下所有的设备，包括子节点，子子节点【
     * @param resourceStructureIds 层级ids
     * @return {@link List }<{@link Equipment }> 层级下的设备信息
     */
    List<Equipment> recursiveFindEquipmentsByResourceStructureIds(List<Integer> resourceStructureIds);

    List<Equipment> findEquipmentsByResourceStructureIds(Set<Integer> resourceStructureIds);

    /**
     * 批量更新设备展示排序
     *
     * @param updateEquipmentOrderDtos 排序dto
     * @return {@link Integer}
     */
    Integer updateBatchEquipmentOrder(List<UpdateEquipmentOrderDto> updateEquipmentOrderDtos);

    /**
     * 获取设备的详细信息
     *
     * @param equipmentId 设备Id
     * @return 设备详情
     */
    EquipmentDetail getEquipmentDetail(Integer equipmentId);

    /**
     * 根据用户id获取所拥有权限的设备
     *
     * @param userId 用户id
     * @return {@link List}<{@link EquipmentDTO}>
     */
    List<EquipmentDTO> findEquipmentDTOsByUserId(Integer userId);

    Set<Integer> findEquipmentIdsByUserId(Integer userId);

    /**
     * 更新设备基础信息
     *
     * @param equipmentBasicDto 设备基础信息dto
     * @return {@link Boolean}
     */
    Boolean updateEquipmentBasicInfo(EquipmentBasicDto equipmentBasicDto);

    /**
     * 批量应用设备基础信息
     * @param batchApplyEquipmentDTO
     * @return {@link Boolean}
     */
    Boolean batchApplyEquipmentInfo(BatchApplyEquipmentDTO batchApplyEquipmentDTO);

    /**
     * 获取设备基础信息
     *
     * @param equipmentId 设备id
     * @return {@link EquipmentDetail}
     */
    EquipmentBasicDto findEquipmentBasic(Integer equipmentId);

    List<EquipmentDTO> findEquipmentsByBaseTypeId(Integer baseTypeId);

    List<EquipmentDTO> findEquipmentsByBaseTypeIds(String baseTypeIds);

    List<EquipmentDTO> findEquipmentsByEquipmentTemplateIds(List<Integer> equipmentTemplateIds);

    Integer getEquipmentStatisticsByReq(EquipmentStatisticsVO equipmentStatisticsVO);

    /**
     * 获取待批量应用信号动态配置的设备信息
     *
     * @param stationId   局站ID
     * @param equipmentId 设备ID
     * @return
     */
    List<ToDynamicApplyEquipmentVO> getToDynamicApplyEquipmentVOs(Integer stationId, Integer equipmentId);

    List<SimpleEquipmentDTO> findSimpleEquipmentDTOByStationId(Integer stationId);

    /**
     * 添加设备
     * @param equipment 设备实体
     * @return {@link Equipment}
     */
    Equipment saveEquipment(Equipment equipment);

    /**
     * 删除设备
     * @param id 设备id
     * @return {@link Equipment}
     */
    void deleteEquipment(Integer id);

    List<StationEquipmentDTO> findStationPageEquipmentDTOByStationId(Integer stationId);

    List<StationEquipmentDTO> findStationPageEquipmentDTOByResourceStructureId(Integer resourceStructureId);

    List<SimpleEquipmentDTO> findEquipmentsBySignalBaseTypeIdsAndResourceStructureIds(String signalBaseTypeIds,String resourceStructureIds);
    /**
     * 更新设备在线状态
     *
     * @param equipmentId 设备id
     * @param connectState 状态id
     */
    void updateConnectState(Integer equipmentId,Integer connectState);
    void updateEquipmentTemplateId(Integer equipmentId,Integer equipmentTemplateId);

    /**
     * 判断设备是否在线
     * @param equipmentId 设备id
     * @return boolean true 在线 false不在线
     */
    boolean isEquipmentOnlineById(Integer equipmentId);


    List<Equipment> findByMonitorUnitId(Integer monitorUnitId);

    /**
     * 按型号名称查询设备，如果型号名称为空，返回设备型号字段不为null不为空字符串的所有设备，
     * 如果型号名称不为空，按等值匹配查询
     * @param styleName 型号名称
     * @return
     */
    List<EquipmentLegderDTO> findEquipmentWithEquipmentStyle(String styleName);
}

