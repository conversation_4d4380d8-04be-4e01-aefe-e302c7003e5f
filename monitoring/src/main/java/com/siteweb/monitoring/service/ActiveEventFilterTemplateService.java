package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.ActiveEventFilterTemplate;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterTemplateService
 * @createTime 2022-04-27 17:20:02
 */
public interface ActiveEventFilterTemplateService {

    int createActiveEventFilterTemplate(ActiveEventFilterTemplate activeEventFilterTemplate);

    int updateActiveEventFilterTemplate(ActiveEventFilterTemplate activeEventFilterTemplate);

    void deleteActiveEventFilterTemplate(Integer activeEventFilterTemplateId);

    ActiveEventFilterTemplate findByActiveEventFilterTemplateId(Integer activeEventFilterTemplateId);

    List<ActiveEventFilterTemplate> findByUserIdAndFilterType(Integer userId, String filterType);

    /**
     * 保存或更新Template,在只有单条数据的情况下使用
     * @param activeEventFilterTemplate 动态模板
     * @return int
     */
    int createOrUpdateTemplate(ActiveEventFilterTemplate activeEventFilterTemplate);
}
