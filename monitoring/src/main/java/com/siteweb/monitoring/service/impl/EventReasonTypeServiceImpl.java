package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.EventReasonType;
import com.siteweb.monitoring.mapper.EventReasonTypeMapper;
import com.siteweb.monitoring.service.EventReasonTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Creation Date: 2025/3/6
 */
@Service
public class EventReasonTypeServiceImpl implements EventReasonTypeService {
    @Autowired
    EventReasonTypeMapper eventReasonTypeMapper;

    @Override
    public List<EventReasonType> listEventReasonType() {
        return eventReasonTypeMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public EventReasonType getEventReasonTypeById(Integer id) {
        return eventReasonTypeMapper.selectById(id);
    }

    @Override
    public void saveEventReasonType(EventReasonType eventReasonTypes) {
        eventReasonTypeMapper.insert(eventReasonTypes);
    }

    @Override
    public void updateEventReasonType(EventReasonType eventReasonTypes) {
        eventReasonTypeMapper.updateById(eventReasonTypes);
    }

    @Override
    public void deleteEventReasonType(String ids) {
        List<Integer> idList = CharSequenceUtil.split(ids, ",").stream().filter(Objects::nonNull)
                .map(Integer::valueOf).toList();
        eventReasonTypeMapper.deleteByIds(idList);
    }

    @Override
    public Map<Integer, String> getEventReasonTypeMap() {
        List<EventReasonType> eventReasonTypes = listEventReasonType();
        return eventReasonTypes.stream().collect(Collectors.toMap(EventReasonType::getId, EventReasonType::getName));
    }
}
