package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.enumeration.SourceType;

import java.util.List;

/**
 * 建议以前使用了globalResourceId的服务层都实现此接口,统一处理转换
 * <AUTHOR>
 * @date 2022/05/10
 */
public interface ResourceObjectService {
    SourceType[] getSourceTypes();
    /**
     * 查找相关资源
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    ResourceObjectEntity findResourceObjectEntity(Integer objectId);

    List<ResourceObjectEntity> findAllResourceObject();

    /**
     * 根据用户id获取对应的资源
     * @param userId 用户id
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    List<ResourceObjectEntity> findAllResourceObjectByUserId(Integer userId);
}
