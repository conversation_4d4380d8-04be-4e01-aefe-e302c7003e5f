package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.EquipmentTemplate;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.EquipmentTemplateMapper;
import com.siteweb.monitoring.service.EquipmentTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("equipmentTemplateService")
public class EquipmentTemplateServiceImpl implements EquipmentTemplateService {

    public static final String DYNAMIC_CONFIG = "动态配置";
    @Autowired
    private EquipmentTemplateMapper equipmentTemplateMapper;
    @Autowired
    private EquipmentManager equipmentManager;

    @Override
    public List<EquipmentTemplate> findEquipmentTemplates() {
        return equipmentTemplateMapper.selectList(null);
    }

    @Override
    public int createEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        return equipmentTemplateMapper.insert(equipmentTemplate);
    }

    @Override
    public int deleteById(Integer equipmentTemplateId) {
        return    equipmentTemplateMapper.deleteById(equipmentTemplateId);
    }

    @Override
    public int updateEquipmentTemplate(EquipmentTemplate equipmentTemplate) {
        return equipmentTemplateMapper.updateById(equipmentTemplate);
    }

    @Override
    public EquipmentTemplate findById(Integer equipmentTemplateId) {
        return equipmentTemplateMapper.selectById(equipmentTemplateId);
    }

    @Override
    public Optional<EquipmentTemplate>  findByEquipmentId(Integer equipmentId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return Optional.empty();
        }
        return Optional.ofNullable(findById(equipment.getEquipmentTemplateId()));
    }

    @Override
    public EquipmentTemplate findEquipmentTemplateByName(String equipmentTemplateName) {
        return equipmentTemplateMapper.selectList(Wrappers.<EquipmentTemplate>lambdaQuery().eq(EquipmentTemplate::getEquipmentTemplateName, equipmentTemplateName))
                                      .stream()
                                      .findFirst()
                                      .orElse(null);

    }

    @Override
    public Map<Integer, EquipmentTemplate> findNameByIds(Collection<Integer> templateIds) {
        if (CollUtil.isEmpty(templateIds)) {
            return new HashMap<>();
        }
        return equipmentTemplateMapper.findNameByIds(templateIds);
    }

    @Override
    public boolean dynamicTemplate(Integer equipmentTemplateId) {
        return equipmentTemplateMapper.exists(Wrappers.lambdaQuery(EquipmentTemplate.class)
                                                      .eq(EquipmentTemplate::getEquipmentTemplateId, equipmentTemplateId)
                                                      .eq(EquipmentTemplate::getMemo, DYNAMIC_CONFIG));
    }
}
