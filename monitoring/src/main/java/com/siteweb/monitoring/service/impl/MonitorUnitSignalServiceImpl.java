package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.MonitorUnitSignal;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.mapper.MonitorUnitSignalMapper;
import com.siteweb.monitoring.mapper.SignalMapper;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.MonitorUnitSignalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MonitorUnitSignalServiceImpl implements MonitorUnitSignalService {
    @Autowired
    private MonitorUnitSignalMapper monitorUnitSignalMapper;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private SignalMapper signalMapper;
    public void save(MonitorUnitSignal monitorUnitSignal){
        monitorUnitSignalMapper.insert(monitorUnitSignal);
    }

    @Override
    public MonitorUnitSignal findMonitorUnitSignal(Integer stationId, Integer equipmentId, Integer signalId) {
        return monitorUnitSignalMapper.findMonitorUnitSignal(stationId, equipmentId, signalId);
    }

    @Override
    public List<Integer> findSignalIdByEquipmentId(Integer equipmentId) {
        return monitorUnitSignalMapper.findSignalIdByEquipmentId(equipmentId);
    }

    @Override
    public void deleteByEquipmentId(Integer equipmentId) {
        monitorUnitSignalMapper.delete(Wrappers.<MonitorUnitSignal>lambdaQuery()
                                               .eq(MonitorUnitSignal::getEquipmentId, equipmentId));
    }

    @Override
    public void updateExpressionByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId, String expression) {
        monitorUnitSignalMapper.update(null, Wrappers.lambdaUpdate(MonitorUnitSignal.class)
                                                     .set(MonitorUnitSignal::getExpression, expression)
                                                     .eq(MonitorUnitSignal::getEquipmentId, equipmentId)
                                                     .eq(MonitorUnitSignal::getSignalId, signalId));
    }

    @Override
    public void create(MonitorUnitSignal monitorUnitSignal) {
        monitorUnitSignalMapper.insert(monitorUnitSignal);
    }

    @Override
    public void expressionHandler(Integer sourceTemplateId, Integer equipmentId, Integer signalId, String expression) {
        //更新表达式
        if (CharSequenceUtil.isBlank(expression) || CharSequenceUtil.contains(expression,"-1")) {
            signalMapper.update(null, Wrappers.lambdaUpdate(Signal.class)
                                              .set(Signal::getExpression, expression)
                                              .eq(Signal::getEquipmentTemplateId, sourceTemplateId)
                                              .eq(Signal::getSignalId, signalId));
        }
        if (existsByEquipmentIdAndSignalId(equipmentId, signalId)) {
           //更新表达式
            updateExpressionByEquipmentIdAndSignalId(equipmentId,signalId,expression);
            return;
        }
        //插入
        Equipment equipment = equipmentService.findById(equipmentId);
        Signal signal = signalMapper.selectOne(Wrappers.lambdaQuery(Signal.class)
                                                       .select(Signal::getChannelNo)
                                                       .eq(Signal::getEquipmentTemplateId, sourceTemplateId)
                                                       .eq(Signal::getSignalId, signalId));
        MonitorUnitSignal monitorUnitSignal = new MonitorUnitSignal(equipment.getStationId(), equipment.getMonitorUnitId(), equipmentId, signalId, equipment.getSamplerUnitId(), signal.getChannelNo(), expression, 2);
        this.create(monitorUnitSignal);
    }

    private boolean existsByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {
        return monitorUnitSignalMapper.exists(Wrappers.lambdaQuery(MonitorUnitSignal.class)
                                                      .eq(MonitorUnitSignal::getEquipmentId, equipmentId)
                                                      .eq(MonitorUnitSignal::getSignalId, signalId));
    }
}
