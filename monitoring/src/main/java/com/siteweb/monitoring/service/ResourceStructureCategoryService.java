package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ResourceStructureEquipmentTreeDTO;
import com.siteweb.monitoring.dto.tree.ResourceStructureEquipmentCategoryTreeDTO;

import java.util.List;

public interface ResourceStructureCategoryService {
    /**
     * @param userId     用户id,用于权限过滤
     * @param existAlarm 是否需要告警状态
     * @return {@link ResourceStructureEquipmentTreeDTO} 层级树
     */
    List<ResourceStructureEquipmentCategoryTreeDTO> getParkAlarmPositionOverviewInfo(Integer userId, Boolean existAlarm);
}
