package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.service.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ResourceServiceImpl implements ResourceService {
    @Autowired
    private ResourceObjectManager resourceObjectManager;
    @Override
    public List<ResourceObjectEntity> findResourceAll() {
        return resourceObjectManager.findAllResource();
    }

    @Override
    public List<ResourceObjectEntity> findResourceByObjectTypeId(Integer objectTypeId) {
        return resourceObjectManager.findEntityByObjectType(objectTypeId);
    }
}
