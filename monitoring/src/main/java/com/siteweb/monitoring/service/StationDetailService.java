package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.vo.StationFilterVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface StationDetailService {
    List<MonitorUnitDTO> getMonitorUnitByStationId(Integer stationId);

    List<SamplerUnitDTO>  getSamplerUnitDTOByStationId(Integer stationId);

    StationDetail getStationDetailByResourceStructureId(Integer resourceStructureId);

    StationDetail getStationDetailByStationId(Integer stationId);

    Page<StationDetail> queryPageableStationDetails(int userId, Pageable pageable, StationFilterVO stationFilterVO);

    StationDetailDTO queryStationDetails(int userId, StationFilterVO stationFilterVO);

    StationGroupStateDTO getStationGroupState(String stationIds);

    List<PowerOffStationDetail> queryPowerOffStationDetails(int userId);
}
