package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.mapper.StationStructureMapMapper;
import com.siteweb.monitoring.service.StationStructureMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StationStructureMapServiceImpl implements StationStructureMapService {
    @Autowired
    StationStructureMapMapper stationStructureMapMapper;

    @Override
    public Map<Integer, String> stationStructureNameMap() {
        List<IdValueDTO<Integer, String>> idValueDTOList = stationStructureMapMapper.findStationStructureNameMap();
        return idValueDTOList.stream().collect(Collectors.toMap(IdValueDTO::getValue, IdValueDTO::getLabel, (v1, v2) -> v2));
    }
}
