package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.ResourceStructureMask;

import java.util.List;

public interface ResourceStructureMaskService{

    List<ResourceStructureMask> findResourceStructureMasks();

    int createResourceStructureMask(ResourceStructureMask resourceStructureMask);

    int deleteById(Integer resourceStructureMaskId);

    int updateResourceStructureMask(ResourceStructureMask resourceStructureMask);

    ResourceStructureMask findById(Integer resourceStructureMaskId);
}

