package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.mapper.SARIsProcessMapper;
import com.siteweb.monitoring.service.SARIsProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SARIsProcessServiceImpl implements SARIsProcessService {
    @Autowired
    SARIsProcessMapper sarIsProcessMapper;
    @Override
    public Integer isProcess(){
        return sarIsProcessMapper.isProcess();
    }
}
