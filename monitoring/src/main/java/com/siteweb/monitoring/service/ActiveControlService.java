package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ActiveControlDTO;
import com.siteweb.monitoring.dto.ControlDTO;
import com.siteweb.monitoring.entity.ActiveControl;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.vo.ActiveControlOperationVO;
import com.siteweb.monitoring.vo.ControlCommandVO;
import com.siteweb.monitoring.vo.SendControlOperationDetailsDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ActiveControlService{

    List<ActiveControl> findActiveControls();

    int createActiveControl(ActiveControl activeControl);

    int deleteById(Integer activeControlId);

    int updateActiveControl(ActiveControl activeControl);

    ActiveControl findById(Integer activeControlId);

    List<ActiveControlDTO> getActiveControlByEquipmentId(Integer equipmentId);

    boolean confirmControlCommand(int userId, ActiveControlOperationVO activeControlOperationVO);

    ControlResultType sendControlCommand(ControlCommandVO controlCommandVO, int userId);

    ControlResultType sendControlCommandByEquipmentIdAndCmdToken(Integer userId, Integer equipmentId, String cmdToken,String value,String description);

    boolean reSendControlCommand(int userId, ActiveControlOperationVO activeControlOperationVO);

    /**
     * 获取所有控制记录 包含 当前的与历史的
     * @return {@link ControlDTO}
     */
    List<ControlDTO> findAllControl(Set<Integer> equipmentIdSet, Date startTime, Date endTime);

    void recordAudit(Integer userId, ControlCommandVO controlCommandVO);

    void recordAudit(Integer userId, String operating, List<Integer> serialNos);

    boolean sendControlOperationDetail(SendControlOperationDetailsDTO sendControlOperationDetailsDTO);
}

