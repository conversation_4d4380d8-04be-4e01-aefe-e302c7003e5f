package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.ConfigSignalDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> z<PERSON>
 * @description dynamicConfigService
 * @createTime 2022-06-13 13:13:43
 */
public interface DynamicConfigService {

    void distributeConfigSignal(int userId, int hostId, ConfigSignalDTO configSignalDTO, Map<String, Object> changedHashMap);

    void distributeConfigEvent(int userId, int hostId, ConfigEventDTO configEventDTO, Map<String, Object> eventChangedHashMap, Map<Integer, Map<String, Object>> addedConditionHashMap, Map<Integer, Map<String, Object>> updatedConditionHashMap, List<Integer> deletedConditionList);

    void generateMUSyncPlan(Integer stationId, Integer monitorUnitId, Date planTime);
}
