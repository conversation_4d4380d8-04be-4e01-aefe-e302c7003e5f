package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.EquipmentTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface EquipmentTemplateService{

    List<EquipmentTemplate> findEquipmentTemplates();

    int createEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    int deleteById(Integer equipmentTemplateId);

    int updateEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    EquipmentTemplate findById(Integer equipmentTemplateId);
    Optional<EquipmentTemplate> findByEquipmentId(Integer equipmentId);

    EquipmentTemplate findEquipmentTemplateByName(String equipmentTemplateName);
    /**
     * 通过ids获取模板名称 key为主键id value为模板名称
     * @param templateIds 模板ids
     * @return {@link Map}<{@link Integer}, {@link String}>
     */
    Map<Integer, EquipmentTemplate> findNameByIds(Collection<Integer> templateIds);
    boolean dynamicTemplate(Integer equipmentTemplateId);
}

