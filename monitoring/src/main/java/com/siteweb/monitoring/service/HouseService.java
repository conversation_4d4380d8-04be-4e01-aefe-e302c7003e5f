package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.HouseInfoDTO;
import com.siteweb.monitoring.entity.House;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface HouseService{

    List<House> findHouses();
    List<House> findHousesByStationids(Collection<Integer> stationIds);
    List<HouseInfoDTO> findAllHouseInfo(List<Integer> stationIds);

    int createHouse(House house);

    House findHouse(Integer stationId,String houseName);

    Map<String,String> houseIdNameMap();
}

