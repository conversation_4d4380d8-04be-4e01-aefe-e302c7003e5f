package com.siteweb.monitoring.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.EquipmentMaintainMapper;
import com.siteweb.monitoring.mapper.EquipmentMaskMapper;
import com.siteweb.monitoring.mapper.ProjectStateOperationMapper;
import com.siteweb.monitoring.service.EquipmentMaintainService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.ProjectStateHouseService;
import com.siteweb.monitoring.vo.BatchEquipmentProjectVO;
import com.siteweb.monitoring.vo.EquipmentProjectVO;
import com.siteweb.utility.entity.OperationRecord;
import com.siteweb.utility.service.OperationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EquipmentMaintainServiceImpl implements EquipmentMaintainService {
    private static final int ADD_MAINTAIN = 1;
    private static final int DELETE_MAINTAIN = 2;
    @Autowired
    private EquipmentMaintainMapper equipmentMaintainMapper;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private ProjectStateHouseService projectStateHouseService;
    @Autowired
    ProjectStateOperationMapper projectStateOperationMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    EquipmentMaskMapper equipmentMaskMapper;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    OperationRecordService operationRecordService;
    @Autowired
    StationManager stationManager;

    @Override
    public int findMaintainState(Integer stationId, Integer equipmentId, Date startTime) {
        //设备是否处于工程状态
        EquipmentMaintain equipmentMaintain = this.findByStationIdAndEquipmentId(stationId,equipmentId);
        if (ObjectUtil.isNull(equipmentMaintain)) {
            return 1;
        }
        if (ObjectUtil.notEqual(equipmentMaintain.getEquipmentState(), 3)) {
            return 1;
        }
        if (ObjectUtil.isNotNull(equipmentMaintain.getStartTime()) && DateUtil.isIn(startTime, equipmentMaintain.getStartTime(), equipmentMaintain.getEndTime())) {
            return 0;
        }
        //局房是否处于工程状态
        Equipment equipment = equipmentService.findById(equipmentId);
        ProjectStateHouse projectStateHouse = projectStateHouseService.findByStationIdAndHouseId(stationId, equipment.getHouseId());
        if (ObjectUtil.isNull(projectStateHouse)) {
            return 1;
        }
        if (ObjectUtil.isNotNull(projectStateHouse.getStartTime()) && DateUtil.isIn(startTime, projectStateHouse.getStartTime(), projectStateHouse.getEndTime())) {
            return 0;
        }
        return 1;
    }

    @Override
    public EquipmentMaintain findByStationIdAndEquipmentId(Integer stationId, Integer equipmentId) {
        return equipmentMaintainMapper.selectOne(Wrappers.lambdaQuery(EquipmentMaintain.class)
                                                         .eq(EquipmentMaintain::getStationId, stationId)
                                                         .eq(EquipmentMaintain::getEquipmentId, equipmentId));
    }


    @Override
    public ProjectOperationResult setEquipmentProject(EquipmentProjectVO equipmentProjectVO, Integer userId) {
        Integer equipmentId = equipmentProjectVO.getEquipmentId();
        Date startTime =  equipmentProjectVO.getStartTime();
        Date endTime = equipmentProjectVO.getEndTime();
        ProjectOperationResult result = new ProjectOperationResult();
        /* 检查参数 */
        if (ObjectUtil.isNull(equipmentId) || ObjectUtil.isNull(startTime) ||ObjectUtil.isNull(endTime)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }

        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(ObjectUtil.isNull(equipment)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }
        //获取设备的屏蔽时段
        EquipmentMaskDTO equipmentMaskDTO = equipmentMaskMapper.findEquipmentMaskDTOByEquipmentId(equipmentId);

        //如果屏蔽时段跟工程时间有交叉
        if (ObjectUtil.isNotNull(equipmentMaskDTO) && ObjectUtil.isNotNull(equipmentMaskDTO.getStartTime()) && ObjectUtil.isNotNull(equipmentMaskDTO.getEndTime())) {
            if (!(equipmentMaskDTO.getEndTime() .before(startTime) || equipmentMaskDTO.getStartTime().after(endTime))){
                result.setResult(false);
                result.setComment(messageSourceUtil.getMessage("projectstate.maskconflict"));
                return result;
            }
        }

        Date now = new Date();
        int equipmentState = now.after(startTime) && now.before(endTime) ? 3 : 1;
        EquipmentMaintain equipmentMaintain = equipmentMaintainMapper.selectById(equipmentId);

        ProjectStateOperation projectStateOperation = new ProjectStateOperation();
        projectStateOperation.setEquipmentId(equipmentId);
        projectStateOperation.setStationId(equipment.getStationId());
        projectStateOperation.setOperationDate(now);
        projectStateOperation.setStartTime(startTime);
        projectStateOperation.setEndTime(endTime);
        projectStateOperation.setReason(equipmentProjectVO.getReason());
        projectStateOperation.setUserId(userId);
        //如果为空则新增
        if(ObjectUtil.isNull(equipmentMaintain)){
            equipmentMaintain = new EquipmentMaintain(equipment.getStationId(),equipmentId,equipmentState,startTime,endTime,userId,equipmentProjectVO.getReason(),null);
            equipmentMaintainMapper.insert(equipmentMaintain);
            projectStateOperation.setOperationType(301);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.addequipmentstate"));

        }  else { //更新
            equipmentMaintain.setEquipmentState(equipmentState);
            equipmentMaintain.setStartTime(startTime);
            equipmentMaintain.setEndTime(endTime);
            equipmentMaintain.setDescription(equipmentProjectVO.getReason());
            equipmentMaintain.setUserId(userId);
            equipmentMaintainMapper.updateById(equipmentMaintain);
            projectStateOperation.setOperationType(302);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.uptequipmentstate"));
        }
        projectStateOperationMapper.insert(projectStateOperation);
        //记录日志
        recordOperationLog(projectStateOperation, ADD_MAINTAIN);
        result.setResult(true);
        result.setComment(messageSourceUtil.getMessage("projectstate.success"));
        return result;
    }

    /**
     * 记录设置设备工程态的操作日志
     * @param projectStateOperation 添加成功后的设备工程态信息
     */
    private void recordOperationLog(ProjectStateOperation projectStateOperation,Integer operationType) {
        String stationName = Optional.ofNullable(stationManager.findStationById(projectStateOperation.getStationId())).map(Station::getStationName).orElse("");
        String equipmentName = Optional.ofNullable(equipmentService.findById(projectStateOperation.getEquipmentId())).map(Equipment::getEquipmentName).orElse("");
        String content = "";
        if (Objects.equals(operationType, ADD_MAINTAIN)) {
            content = String.format(messageSourceUtil.getMessage("projectstate.equipment.addOperationLog"),
                    equipmentName,
                    DateUtil.format(projectStateOperation.getStartTime(), DatePattern.NORM_DATETIME_PATTERN),
                    DateUtil.format(projectStateOperation.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
        } else {
            content = String.format(messageSourceUtil.getMessage("projectstate.equipment.deleteOperationLog"), equipmentName);
        }
        OperationRecord operationRecord = OperationRecord.builder()
                                                         .userId(projectStateOperation.getUserId())
                                                         .stationId(projectStateOperation.getStationId())
                                                         .stationName(stationName)
                                                         .operation(projectStateOperation.getEquipmentId())
                                                         .operationTime(new Date())
                                                         .operationType(2)
                                                         .operationContent(content)
                                                         .build();
        operationRecordService.saveOperationRecord(operationRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ProjectOperationResult> setBatchEquipmentProjectStatus(BatchEquipmentProjectVO batchEquipmentProjectVO, Integer userId) {
        List<ProjectOperationResult> result = new ArrayList<>(batchEquipmentProjectVO.getEquipmentIds().size());
        for (Integer equipmentId : batchEquipmentProjectVO.getEquipmentIds()) {
            EquipmentProjectVO equipmentProjectVO = new EquipmentProjectVO();
            equipmentProjectVO.setEquipmentId(equipmentId);
            equipmentProjectVO.setStartTime(batchEquipmentProjectVO.getStartTime());
            equipmentProjectVO.setEndTime(batchEquipmentProjectVO.getEndTime());
            equipmentProjectVO.setReason(batchEquipmentProjectVO.getReason());
            ProjectOperationResult projectOperationResult = setEquipmentProject(equipmentProjectVO, userId);
            result.add(projectOperationResult);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectOperationResult deleteEquipmentProject(Integer equipmentId, Integer userId) {
        ProjectOperationResult result = new ProjectOperationResult();
        /* 检查参数 */
        if (ObjectUtil.isNull(equipmentId)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }

        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(ObjectUtil.isNull(equipment)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }


        EquipmentMaintain equipmentMaintain = equipmentMaintainMapper.selectById(equipmentId);
        if(ObjectUtil.isNotNull(equipmentMaintain)) {
            ProjectStateOperation projectStateOperation = new ProjectStateOperation();
            projectStateOperation.setEquipmentId(equipmentId);
            projectStateOperation.setStationId(equipmentMaintain.getStationId());
            projectStateOperation.setOperationDate(new Date());
            projectStateOperation.setStartTime(equipmentMaintain.getStartTime());
            projectStateOperation.setEndTime(equipmentMaintain.getEndTime());
            projectStateOperation.setReason(equipmentMaintain.getDescription());
            projectStateOperation.setUserId(userId);
            projectStateOperation.setOperationType(303);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.delequipmentstate"));
            equipmentMaintainMapper.deleteById(equipmentId);
            projectStateOperationMapper.insert(projectStateOperation);
            recordOperationLog(projectStateOperation, DELETE_MAINTAIN);
        }

        result.setResult(true);
        return result;
    }
    @Override
    public EquipmentMaintainDTO getEquipmentProjectById(Integer equipmentId) {
        Equipment equipment = equipmentService.findById(equipmentId);
        EquipmentMaintain equipmentMaintain = equipmentMaintainMapper.selectById(equipmentId);
        if (Objects.isNull(equipmentMaintain) || Objects.isNull(equipment)) {
            return null;
        }
        return EquipmentMaintainDTO.builder()
                                   .equipmentId(equipment.getEquipmentId())
                                   .equipmentName(equipment.getEquipmentName())
                                   .stationId(equipment.getStationId())
                                   .startTime(equipmentMaintain.getStartTime())
                                   .endTime(equipmentMaintain.getEndTime())
                                   .reason(equipmentMaintain.getDescription())
                                   .build();
    }

    @Override
    public void batchDeleteEquipmentProject(List<Integer> equipmentIds, Integer userId) {
        for (Integer equipmentId : equipmentIds) {
            deleteEquipmentProject(equipmentId, userId);
        }
    }
    @Override
    public List<EquipmentProjectDetail> queryEquipmentProjectDetails(int userId) {
        List<EquipmentDTO> equipmentDTOList=  equipmentService.findEquipmentDTOsByUserId(userId);
        List<EquipmentProjectDetail> equipmentProjectDetails = equipmentMaintainMapper.getProjectEquipmentList(3);
        Map<Integer, EquipmentDTO> realTimeSignalItemMap = equipmentDTOList.stream().collect(
                Collectors.toMap(EquipmentDTO::getEqId, p -> p));

        for(EquipmentProjectDetail equipmentMaintain:equipmentProjectDetails){
            EquipmentDTO equipmentDTO = realTimeSignalItemMap.get(equipmentMaintain.getEquipmentId());
            if(ObjectUtil.isNotNull(equipmentDTO) ) {
                Map<Integer, ResourceStructure> resourceStructureMap = resourceStructureManager.getAllParentStructureById(equipmentDTO.getRId());
                equipmentMaintain.setEquipmentName(equipmentDTO.getEqName());
                ResourceStructure resourceStructure1 = resourceStructureMap.get(102);
                if (resourceStructure1 != null) {
                    equipmentMaintain.setCenterName(resourceStructure1.getResourceStructureName());
                }
                ResourceStructure resourceStructure2 = resourceStructureMap.get(103);
                if (resourceStructure2 != null) {
                    equipmentMaintain.setGroupName(resourceStructure2.getResourceStructureName());
                }
            }
        }
        return  equipmentProjectDetails;
    }

    @Override
    public List<EquipmentMaintainDTO> findMaintainStateByResourceStructure(Integer resourceStructureId) {
        return equipmentMaintainMapper.findMaintainStateByResourceStructure(resourceStructureId);
    }
}
