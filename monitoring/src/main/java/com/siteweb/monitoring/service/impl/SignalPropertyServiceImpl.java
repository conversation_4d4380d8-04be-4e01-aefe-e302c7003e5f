package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.EquipmentSignalPropertyDTO;
import com.siteweb.monitoring.entity.SignalProperty;
import com.siteweb.monitoring.mapper.SignalPropertyMapper;
import com.siteweb.monitoring.service.SignalPropertyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service("signalPropertyService")
public class SignalPropertyServiceImpl implements SignalPropertyService {

    @Autowired
    SignalPropertyMapper signalPropertyMapper;

    @Override
    public int createSignalProperty(SignalProperty signalProperty) {
        return signalPropertyMapper.insertSignalProperty(signalProperty);
    }

    @Override
    public int deleteSignalProperty(SignalProperty signalProperty) {
        return signalPropertyMapper.deleteSignalProperty(signalProperty);
    }

    @Override
    public List<EquipmentSignalPropertyDTO> findSignalPropertiesByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return new ArrayList<>();
        }
        return signalPropertyMapper.findSignalPropertiesByEquipmentIdAndSignalIds(equipmentId,signalIds);
    }

    @Override
    public List<SignalProperty> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalPropertyMapper.selectList(Wrappers.lambdaQuery(SignalProperty.class)
                                                       .eq(SignalProperty::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void batchInsert(List<SignalProperty> signalProperties) {
        if (CollUtil.isEmpty(signalProperties)) {
            return;
        }
        signalPropertyMapper.batchInsert(signalProperties);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        signalPropertyMapper.delete(Wrappers.<SignalProperty>lambdaQuery()
                                            .eq(SignalProperty::getEquipmentTemplateId, equipmentTemplateId));
    }
}
