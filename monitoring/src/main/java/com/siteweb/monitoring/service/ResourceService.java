package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ResourceObjectEntity;

import java.util.List;

public interface ResourceService {
    /**
     * 获取所有资源
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    List<ResourceObjectEntity> findResourceAll();

    /**
     * 根据资源类型获取资源
     * @param objectTypeId 资源类型(sourceType枚举)
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    List<ResourceObjectEntity> findResourceByObjectTypeId(Integer objectTypeId);
}
