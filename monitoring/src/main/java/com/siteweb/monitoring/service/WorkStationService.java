package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.WorkStationNetworkTopologyDTO;
import com.siteweb.monitoring.entity.WorkStation;
import com.siteweb.monitoring.vo.WorkStationNetworkTopologyVO;

import java.util.List;

public interface WorkStationService{

    List<WorkStation> findWorkStations();

    Integer findWorkStationIdByType(int workStationType);

    int createWorkStation(WorkStation workStation);

    int deleteById(Integer workStationId);

    int updateWorkStation(WorkStation workStation);

    void updateWorkStationConnectStateById(int connectState, int workStationId);

    WorkStation findById(Integer workStationId);

    /**
     * @return {@link List }<{@link WorkStationNetworkTopologyDTO }>
     */
    List<WorkStationNetworkTopologyVO> networkTopology();
}

