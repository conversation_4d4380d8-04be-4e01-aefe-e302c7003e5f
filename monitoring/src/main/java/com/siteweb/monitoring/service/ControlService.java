package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.dto.SimpleControlDTO;
import com.siteweb.monitoring.entity.Control;

import java.util.List;

public interface ControlService {

    List<ConfigControlItem> findControlItemsByEquipmentId(Integer equipmentId);
    List<SimpleControlDTO> findSimpleControlDTOsByEquipmentId(Integer equipmentId);
    List<ConfigControlItem> findSimpleControlDTOsByEquipmentIdsAndBaseTypeId(List<Integer> equipmentIds, Long baseTypeId);
    ConfigControlItem findSimpleControlDTOsByEquipmentIdAndBaseTypeId(Integer equipmentId, Long baseTypeId);

    ConfigControlItem findSimpleControlDTOsByEquipmentIdAndControlId(Integer equipmentId, Integer controlId);
    /**
     *  复制模板控制命令相关数据
     *  将源模板上的控制命令相关数据添加到目标模板上
     *
     * @param sourceTemplateId 源模板id
     * @param destTemplateId   目标目标
     */
    void copyTemplateControl(Integer sourceTemplateId, Integer destTemplateId);

    void batchInsert(List<Control> controlList);

    /**
     * 获取控制命令id
     * @param stationId 局站id
     * @param equipmentId 设备id
     * @param commandCategory 命令类型
     * @return {@link Integer}
     */
    Integer findCommandIdByCommandCategory(Integer stationId, Integer equipmentId, Integer commandCategory);
}

