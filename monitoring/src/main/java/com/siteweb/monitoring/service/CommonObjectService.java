package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.CommonObject;

import java.util.List;

public interface CommonObjectService {
    /**
     * 获取所有通用对象
     * @return {@link List}<{@link CommonObject}>
     */
    List<CommonObject> findAll();

    CommonObject findById(Integer id);

    /**
     * 创建通用对象
     * @param commonObject 通用对象实体
     * @return {@link CommonObject}
     */
    CommonObject create(CommonObject commonObject);

    /**
     * 删除通用对象
     * @param id 通用对象id
     * @return {@link Boolean}
     */
    Boolean deleteById(Integer id);

    /**
     * 更新通用对象
     * @param commonObject 通用对象实体
     * @return {@link CommonObject}
     */
    CommonObject update(CommonObject commonObject);

    /**
     * 通用对象是否存在
     * @param id 通用对象id
     * @return {@link Boolean}
     */
    boolean exists(Integer id);
}
