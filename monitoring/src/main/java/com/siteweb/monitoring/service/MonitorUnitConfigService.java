package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.MonitorUnitConfig;

import java.util.List;

public interface MonitorUnitConfigService{

    List<MonitorUnitConfig> findMonitorUnitConfigs();

    int createMonitorUnitConfig(MonitorUnitConfig monitorUnitConfig);

    int deleteById(Integer monitorUnitConfigId);

    int updateMonitorUnitConfig(MonitorUnitConfig monitorUnitConfig);

    MonitorUnitConfig findById(Integer monitorUnitConfigId);
}

