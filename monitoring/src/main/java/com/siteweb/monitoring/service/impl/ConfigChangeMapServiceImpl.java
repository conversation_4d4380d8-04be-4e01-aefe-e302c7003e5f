package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.ConfigChangeMap;
import com.siteweb.monitoring.mapper.ConfigChangeMapMapper;
import com.siteweb.monitoring.service.ConfigChangeMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConfigChangeMapServiceImpl implements ConfigChangeMapService {
    @Autowired
    private ConfigChangeMapMapper configChangeMapMapper;

    @Override
    public ConfigChangeMap findByConfigIdAndEditType(Integer configId, Integer editType) {
        return configChangeMapMapper.findByConfigIdAndEditType(configId,editType);
    }
}
