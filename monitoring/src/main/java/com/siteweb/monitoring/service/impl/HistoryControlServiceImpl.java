package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.HistoryControl;
import com.siteweb.monitoring.mapper.HistoryControlMapper;
import com.siteweb.monitoring.service.HistoryControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("historyControlService")
public class HistoryControlServiceImpl implements HistoryControlService {

    @Autowired
    private HistoryControlMapper historyControlMapper;

    @Override
    public List<HistoryControl> findHistoryControls() {
        return historyControlMapper.selectList(null);
    }

    @Override
    public int createHistoryControl(HistoryControl historyControl) {
        return historyControlMapper.insert(historyControl);
    }

    @Override
    public int deleteById(Integer historyControlId) {
        return    historyControlMapper.deleteById(historyControlId);
    }

    @Override
    public int updateHistoryControl(HistoryControl historyControl) {
        return historyControlMapper.updateById(historyControl);
    }

    @Override
    public HistoryControl findById(Integer historyControlId) {
        return historyControlMapper.selectById(historyControlId);
    }
}
