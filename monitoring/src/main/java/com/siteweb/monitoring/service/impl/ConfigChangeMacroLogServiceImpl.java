package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.ConfigChangeMacroLog;
import com.siteweb.monitoring.entity.ConfigChangeMap;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.service.CConfigChangeMicroLogService;
import com.siteweb.monitoring.service.ConfigChangeMacroLogService;
import com.siteweb.monitoring.service.ConfigChangeMapService;
import com.siteweb.monitoring.service.ConfigChangeMicroLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

@Service
public class ConfigChangeMacroLogServiceImpl implements ConfigChangeMacroLogService {
    @Autowired
    private ConfigChangeMacroLogMapper configChangeMacroLogMapper;
    @Autowired
    private ConfigChangeMicroLogService configChangeMicroLogService;
    @Autowired
    private CConfigChangeMicroLogService cConfigChangeMicroLogService;
    @Autowired
    private ConfigChangeMapService configChangeMapService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfigChangeLog(String objectId, Integer configId, Integer editType){
        configChangeMicroLogService.saveChangeMicroLog(objectId,configId,editType);
        cConfigChangeMicroLogService.saveConfigChangeLog(objectId,configId,editType);
        ConfigChangeMap configChangeMap = configChangeMapService.findByConfigIdAndEditType(configId, editType);
        //如果不需要生成宏变更记录，则退出
        if (Objects.isNull(configChangeMap) || Objects.isNull(configChangeMap.getMacroConfigId()) || Objects.isNull(configChangeMap.getMacroEditType())) {
            return;
        }
        this.saveChangeMacroLog(objectId, configChangeMap.getMacroConfigId(), configChangeMap.getMacroEditType(), configChangeMap.getIdConvertRule());
    }

    private void saveChangeMacroLog(String objectId, Integer configId, Integer editType, String idConvertRule) {
        List<String> tmpIds = CharSequenceUtil.split(objectId, '.');
        List<String> tmpRules = CharSequenceUtil.split(idConvertRule, '.');
        StringJoiner macroObjectIdJoiner = new StringJoiner(".");
        for (String tmpRule : tmpRules) {
            int i = Integer.parseInt(tmpRule);
            macroObjectIdJoiner.add(tmpIds.get(i - 1));
        }
        //插入宏记录
        String macroObjectId = macroObjectIdJoiner.toString();
        if (this.exists(macroObjectId, configId, editType)) {
            this.updateTime(macroObjectId, configId, editType);
        }
        this.deleteByObjectIdAndEditType(macroObjectId, configId);
        this.create(macroObjectId, configId, editType);
    }

    private boolean exists(String objectId, Integer configId, Integer editType) {
        return configChangeMacroLogMapper.exists(Wrappers.lambdaQuery(ConfigChangeMacroLog.class)
                                                         .eq(ConfigChangeMacroLog::getObjectId, objectId)
                                                         .eq(ConfigChangeMacroLog::getConfigId, configId)
                                                         .eq(ConfigChangeMacroLog::getEditType, editType));
    }

    private void updateTime(String objectId, Integer configId, Integer editType) {
        configChangeMacroLogMapper.update(null, Wrappers.lambdaUpdate(ConfigChangeMacroLog.class)
                                                        .set(ConfigChangeMacroLog::getUpdateTime, new Date())
                                                        .eq(ConfigChangeMacroLog::getObjectId, objectId)
                                                        .eq(ConfigChangeMacroLog::getConfigId, configId)
                                                        .eq(ConfigChangeMacroLog::getEditType, editType));
    }

    private void deleteByObjectIdAndEditType(String objectId, Integer configId) {
        configChangeMacroLogMapper.delete(Wrappers.lambdaUpdate(ConfigChangeMacroLog.class)
                                                  .eq(ConfigChangeMacroLog::getObjectId, objectId)
                                                  .eq(ConfigChangeMacroLog::getConfigId, configId));
    }
    private void create(String objectId, Integer configId, Integer editType){
        ConfigChangeMacroLog configChangeMacroLog = new ConfigChangeMacroLog(objectId,configId,editType,new Date());
        configChangeMacroLogMapper.insert(configChangeMacroLog);
    }
}
