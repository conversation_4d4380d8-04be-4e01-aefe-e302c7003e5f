package com.siteweb.monitoring.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.entity.ControlCommandRecord;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/21 15:38
 */
public interface ControlCommandRecordService {
    ControlCommandRecord create(ControlCommandRecord controlCommandRecord);

    IPage<ControlCommandRecord> findReportListByPage(Page<ControlCommandRecord> page, Wrapper<ControlCommandRecord> wrappers);

    List<ControlCommandRecord> findByWrapper(Wrapper<ControlCommandRecord> wrapper);
}
