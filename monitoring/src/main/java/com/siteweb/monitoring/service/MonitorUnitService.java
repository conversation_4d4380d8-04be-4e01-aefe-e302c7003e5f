package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.MonitorUnitDTO;
import com.siteweb.monitoring.entity.MonitorUnit;
import com.siteweb.monitoring.vo.MonitorUnitRegisterVO;

import java.util.List;

public interface MonitorUnitService{

    List<MonitorUnit> findMonitorUnits();

    int createMonitorUnit(MonitorUnit monitorUnit);

    int deleteById(Integer monitorUnitId);

    int updateMonitorUnit(MonitorUnit monitorUnit);

    MonitorUnit findById(Integer monitorUnitId);

    List<MonitorUnitDTO> findMonitorUnitDTOs();

    List<MonitorUnitDTO> findMonitorUnitDTOsByIds(List<Integer> muIds);

    int registerMonitorUnit(MonitorUnitRegisterVO monitorUnitRegisterVO);

    /**
     * 同步监控单元状态
     *
     * @param monitorUnitId 监控单元id
     * @param isSync        同步状态
     * @return int
     */
    int syncMonitorUnit(Integer monitorUnitId,Integer isSync);

    /**
     * 根据设备id获取监控单元
     */
    MonitorUnitDTO getMonitorUnitByEquipmentId(Integer equipmentId);
}

