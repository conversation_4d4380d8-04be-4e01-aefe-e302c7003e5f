package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.EquipmentSignalPropertyDTO;
import com.siteweb.monitoring.entity.SignalProperty;

import java.util.List;


public interface SignalPropertyService {

    int createSignalProperty(SignalProperty signalProperty);

    int deleteSignalProperty(SignalProperty signalProperty);

    List<EquipmentSignalPropertyDTO> findSignalPropertiesByEquipmentIdAndSignalIds(Integer equipment, List<Integer> signalIds);
    List<SignalProperty> findByEquipmentTemplateId(Integer equipmentTemplateId);
    void batchInsert(List<SignalProperty> signalProperties);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);
}

