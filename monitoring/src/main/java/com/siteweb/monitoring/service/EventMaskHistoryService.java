package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.EventMaskHistory;

import java.util.List;
import java.util.Set;

public interface EventMaskHistoryService {
    /**
     * 通过序列化判断是否存在
     *
     * @param sequenceId 序列id
     * @return boolean
     */
    boolean existsBySequenceId(String sequenceId);

    void create(EventMaskHistory eventMaskHistory);

    /**
     * 获取不在屏蔽历史中的告警idSet
     * @param sequenceIds
     * @return {@link Set }<{@link String }>
     */
    Set<String> findNotInBySequenceIds(List<String> sequenceIds);

    void batchInsert(List<EventMaskHistory> eventMaskHistoryInsertList);
}
