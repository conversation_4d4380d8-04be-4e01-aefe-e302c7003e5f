package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.StationStructure;

import java.util.List;
import java.util.Map;

public interface StationStructureService {
    List<StationStructure> findAll();

    StationStructure findById(Integer id);

    StationStructure findByStationId(Integer stationId);

    Map<Integer, String> findStationStructureMap(List<Integer> stationIds);

    StationStructure findRoot();

    /**
     * 获取中心structure
     */
    StationStructure findPostalStructure();

    StationStructure findPostalStructureByType();

    List<StationStructure> findByStructureGroupId(Integer structureGroupId);
}
