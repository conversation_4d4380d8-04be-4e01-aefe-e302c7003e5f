package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.Port;

import java.util.List;

public interface PortService{

    List<Port> findPorts();

    int createPort(Port port);

    int deleteById(Integer portId);

    int updatePort(Port port);

    Port findById(Integer portId);

    Port findByPortIdAndMonitorUnitId(Integer portId, Integer monitUnitId);

    List<Port> findPortByMonitUnitId(Integer monitUnitId);
}

