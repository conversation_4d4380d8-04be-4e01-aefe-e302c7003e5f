package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;

import java.util.List;
/**
 * @Author: lzy
 * @Date: 2023/7/25 10:34
 */
public interface TimeGroupSpanService {

    void saveTimeGroupSpan(TimeGroupSpan timeGroupSpan);
    void updateLastUpdateDateAndTimeSpanCharByTimeGroupIdAndWeek(String timeMaskChar, Integer timeGroupId, Integer week);

    /**
     * 删除告警屏蔽中的分时段屏蔽信息
     * @param stationId   局站id
     * @param equipmentId 设备id
     * @param eventId     告警id
     */
    void deleteEventMask(Integer stationId, Integer equipmentId, Integer eventId);

    void saveOrUpdateTimeGroupSpan(Integer timeGroupId, String timeGroupChar, Integer week);
    /**
     * @param timeGroupId 准进组id
     * @param timeSpanStrs 准进组时间段字符 100:00-23:5900:00-00:0000:00-00:00,200:00-23:5900:00-00:0000:00-00:00 星期几+HH:mm-HH:mm*3时间段,星期几+HH:mm-HH:mm*3时间段
     * @return {@link Integer}
     */
    Integer createTimeSpans(Integer timeGroupId, String timeSpanStrs);

    Integer updateTimeSpans(Integer timeGroupId, String timeSpanStrs);

    Integer deleteByTimeGroupIds(List<Integer> timeGroupIdList);

    List<TimeGroupSpan> findByTimeGroupId(Integer timeGroupId);

    void batchInsertEquipmentTimeGroupSpan(BatchEquipmentMaskVO batchEquipmentMaskVO);

    void batchInsertStationTimeGroupSpan(BatchSetStationMaskDTO dto);

    List<TimeGroupSpan> findByTimeGroupIds(List<Integer> timeGroupIds);

    void batchInsertEventTimeGroupSpan(Integer currentTimeGroupId, BatchCreateEventMaskDTO batchCreateEventMaskDTO);
}
