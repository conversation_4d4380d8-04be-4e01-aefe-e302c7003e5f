package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.MonitorUnitManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.PortMapper;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.SampleTopologyService;
import com.siteweb.monitoring.service.SamplerUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("SampleTopologyService")
public class SampleTopologyServiceImpl implements SampleTopologyService {

    @Autowired
    ResourceStructureManager resourceManager;
    @Autowired
    MonitorUnitManager monitorUnitManager;

    @Autowired
    PortMapper portMapper;

    @Autowired
    SamplerUnitService samplerUnitService;

    @Autowired
    EquipmentStateManager equipmentStateManager;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    EquipmentService equipmentService;
    public TopologyBase getCenterTopology(Integer userId){
        Set<Integer> equipmentIds = equipmentService.findEquipmentDTOsByUserId(userId)
                                                    .stream()
                                                    .map(EquipmentDTO::getEqId)
                                                    .collect(Collectors.toSet());
        Map<String, List<SamplerUnitTopology>> portSuDictionary = getPortSamplerUnitDictionary(equipmentIds);
        Map<Integer, List<PortTopology>> monitorUnitPortDictionary = getMonitorUnitPortDictionary(portSuDictionary);
        Map<Integer, List<MonitorUnitTopology>> parkMonitorUnitDictionary = getParkMonitorUnitDictionary(monitorUnitPortDictionary);

        ResourceStructure rootStructure = resourceManager.getRoot();
        //如果是二级园区，直接返回
        if (rootStructure.getStructureTypeId() == 2) {
            ParkTopology parkTopology = new ParkTopology(rootStructure);
            List<MonitorUnitTopology> monitorUnitTopologies = parkMonitorUnitDictionary.values()
                                                                                       .stream()
                                                                                       .flatMap(Collection::stream)
                                                                                       .sorted(Comparator.comparing(MonitorUnitTopology::getName))
                                                                                       .toList();
            parkTopology.setChildren(monitorUnitTopologies);
            return parkTopology;
        }

        //如果存在三级园区，将二级园区挂到下面
        CenterTopology centerTopology= new CenterTopology(rootStructure);
        List<ResourceStructure> parks = resourceManager.getResourceStructureLstByParentId(rootStructure.getResourceStructureId());
        List<ParkTopology> parkTopologies = new ArrayList<>();
        for(ResourceStructure park:parks){
            ParkTopology parkTopology = new ParkTopology(park);
            List<MonitorUnitTopology> monitorUnitTopologies = parkMonitorUnitDictionary.computeIfAbsent(park.getResourceStructureId(), k -> new ArrayList<>());
            monitorUnitTopologies.sort(Comparator.comparing(MonitorUnitTopology::getName));
            parkTopology.setChildren(monitorUnitTopologies);
            parkTopologies.add(parkTopology);
        }
        parkTopologies.sort(Comparator.comparing(ParkTopology::getSortValue).thenComparing(ParkTopology::getName));
        centerTopology.setChildren(parkTopologies);
        return  centerTopology;
    }

    private  Map<Integer, List<MonitorUnitTopology>> getParkMonitorUnitDictionary(Map<Integer, List<PortTopology>> monitorUnitDictionary){
        Map<Integer, List<MonitorUnitTopology>> parkDictionary = new HashMap<>();
        List<MonitorUnitDTO> monitorUnits = monitorUnitManager.getALL();
        for(MonitorUnitDTO monitorUnit:monitorUnits){
            List<PortTopology> portTopologies = monitorUnitDictionary.computeIfAbsent(monitorUnit.getMonitorUnitId(), k -> new ArrayList<>());
            if (CollUtil.isEmpty(portTopologies)) {
                continue;
            }
            MonitorUnitTopology monitorUnitTopology = new MonitorUnitTopology(monitorUnit);
            portTopologies.sort(Comparator.comparing(PortTopology::getPortNo));
            monitorUnitTopology.setChildren(portTopologies);
            monitorUnitTopology.setChildState();
            parkDictionary.computeIfAbsent(monitorUnit.getCenterId(), k -> new ArrayList<>())
                          .add(monitorUnitTopology);
        }
        return  parkDictionary;
    }

    /**
     *
     * @param portDictionary
     * @return
     */
    private  Map<Integer, List<PortTopology>> getMonitorUnitPortDictionary(Map<String, List<SamplerUnitTopology>> portDictionary){
        Map<Integer, List<PortTopology>> monitorUnitDictionary = new HashMap<>();
        List<Port> ports = portMapper.selectList(null);
        for (Port port : ports) {
            List<SamplerUnitTopology> samplerUnitTopologies = portDictionary.computeIfAbsent(port.getKey(), k -> new ArrayList<>());
            if (CollUtil.isEmpty(samplerUnitTopologies)) {
                continue;
            }
            PortTopology portTopology = new PortTopology(port);
            samplerUnitTopologies.sort(Comparator.comparing(SamplerUnitTopology::getName));
            portTopology.setChildren(samplerUnitTopologies);
            monitorUnitDictionary.computeIfAbsent(port.getMonitorUnitId(), k -> new ArrayList<>())
                                 .add(portTopology);
        }
        return monitorUnitDictionary;
    }

    /**
     * 将采集单元挂到端口下
     * @return 端口采集单元映射
     */
    private  Map<String, List<SamplerUnitTopology>> getPortSamplerUnitDictionary(Set<Integer> equipmentIds){
        Map<String, List<SamplerUnitTopology>> portDictionary = new HashMap<>();
        Map<Integer, List<Equipment>> samplerUnitEquipmentMap = equipmentManager.getAllEquipments()
                                                                                .stream()
                                                                                .collect(Collectors.groupingBy(Equipment::getSamplerUnitId));
        List<SamplerUnit> samplerUnits = samplerUnitService.findByIds(samplerUnitEquipmentMap.keySet());
        for (SamplerUnit samplerUnit : samplerUnits) {
            List<Equipment> equipmentList = samplerUnitEquipmentMap.get(samplerUnit.getSamplerUnitId());
            for (Equipment equipment : equipmentList) {
                if(equipment != null && equipmentIds.contains(equipment.getEquipmentId())) {
                    EquipmentState equipmentState = equipmentStateManager.getEquipmentStateById(equipment.getEquipmentId());
                    SamplerUnitTopology samplerUnitTopology = new SamplerUnitTopology(equipment, equipmentState);
                    portDictionary.computeIfAbsent(samplerUnit.getParentKey(), r -> new ArrayList<>())
                                  .add(samplerUnitTopology);
                }
            }
        }
        return portDictionary;
    }
}
