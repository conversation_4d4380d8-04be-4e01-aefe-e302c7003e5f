package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.ActiveSignal;
import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.dto.SimpleControlDTO;
import com.siteweb.monitoring.entity.Control;
import com.siteweb.monitoring.entity.ControlMeanings;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.ControlMapper;
import com.siteweb.monitoring.model.RealTimeSignalKey;
import com.siteweb.monitoring.service.ControlMeaningsService;
import com.siteweb.monitoring.service.ControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import oshi.driver.windows.perfmon.PagingFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("controlService")
public class ControlServiceImpl implements ControlService {
    @Autowired
    ControlMapper controlMapper;

    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    ControlMeaningsService controlMeaningsService;

    @Override
    public List<ConfigControlItem> findControlItemsByEquipmentId(Integer equipmentId) {
        /* Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (ObjectUtil.isNull(equipment)) {
            return new ArrayList<>();
        } */
        List<ConfigControlItem> configControlItems = controlMapper.findControlItemByEquipmentId(equipmentId);
        if (configControlItems.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> signalIds = new ArrayList<>();
        for (ConfigControlItem configControlItem : configControlItems) {
            signalIds.add(configControlItem.getSignalId());
        }
        List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
        Map<Integer, SimpleActiveSignal> realTimeSignalItemMap = signalList.stream().collect(
                Collectors.toMap(SimpleActiveSignal::getSignalId, p -> p));

        for (ConfigControlItem configControlItem : configControlItems) {
            if (realTimeSignalItemMap.get(configControlItem.getSignalId()) != null) {
                configControlItem.setCurrentValue(realTimeSignalItemMap.get(configControlItem.getSignalId()).getOriginalValue());
                configControlItem.setUnit(realTimeSignalItemMap.get(configControlItem.getSignalId()).getUnit());
                configControlItem.setSignalName(realTimeSignalItemMap.get(configControlItem.getSignalId()).getSignalName());
                configControlItem.setSampleTime(realTimeSignalItemMap.get(configControlItem.getSignalId()).getSampleTime());
            }
        }

        return configControlItems;
    }


    @Override
    public List<SimpleControlDTO> findSimpleControlDTOsByEquipmentId(Integer equipmentId) {
        return controlMapper.findSimpleControlDTOsByEquipmentId(equipmentId);
    }

    @Override
    public List<ConfigControlItem> findSimpleControlDTOsByEquipmentIdsAndBaseTypeId(List<Integer> equipmentIds, Long baseTypeId) {
        List<ConfigControlItem> simpleControlDTOs = controlMapper.findSimpleControlDTOsByEquipmentIdsAndBaseTypeId(equipmentIds, baseTypeId);
        if (ObjectUtil.isNull(simpleControlDTOs) || simpleControlDTOs.size() == 0) {
            return null;
        }
        List<RealTimeSignalKey> redisKey = new ArrayList<>();
        for(ConfigControlItem configControlItem:simpleControlDTOs){
            redisKey.add(new RealTimeSignalKey(configControlItem.getEquipmentId(),configControlItem.getSignalId()));
        }

        /*从 redis获取实时数据 */
        List<ActiveSignal> activeSignalList = activeSignalManager.getActiveSignalsByKeys(redisKey);
        Map<String, ActiveSignal> realTimeSignalItemMap = new HashMap<>();
        for(ActiveSignal activeSignal:activeSignalList){
            if(ObjectUtil.isNotNull(activeSignal.getEquipmentId()) && ObjectUtil.isNotNull(activeSignal.getSignalId())){
                realTimeSignalItemMap.put(activeSignal.getRedisKey(), activeSignal);
            }
        }

        for (ConfigControlItem configControlItem : simpleControlDTOs) {
            if (realTimeSignalItemMap.get(configControlItem.getEquipmentId() + "." + configControlItem.getSignalId()) != null) {
                configControlItem.setPosition(resourceStructureManager.getFullPath(configControlItem.getResourceStructureId()));
                configControlItem.setCurrentValue(realTimeSignalItemMap.get(configControlItem.getEquipmentId() + "." + configControlItem.getSignalId()).getCurrentValue());
                configControlItem.setUnit(realTimeSignalItemMap.get(configControlItem.getEquipmentId() + "." + configControlItem.getSignalId()).getUnit());
                configControlItem.setSignalName(realTimeSignalItemMap.get(configControlItem.getEquipmentId() + "." + configControlItem.getSignalId()).getSignalName());
                configControlItem.setSampleTime(realTimeSignalItemMap.get(configControlItem.getEquipmentId() + "." + configControlItem.getSignalId()).getSampleTime());
            }
        }

        return simpleControlDTOs;
    }

    @Override
    public ConfigControlItem findSimpleControlDTOsByEquipmentIdAndBaseTypeId(Integer equipmentId, Long baseTypeId) {
        List<ConfigControlItem> simpleControlDTOs =   controlMapper.findSimpleControlDTOsByEquipmentIdAndBaseTypeId(equipmentId,baseTypeId);
        if (ObjectUtil.isNull(simpleControlDTOs) || simpleControlDTOs.size() == 0) {
            return null;
        }
        ConfigControlItem simpleControlDTO = simpleControlDTOs.get(0);
        List<Integer> signalIds = new ArrayList<>();
        signalIds.add(simpleControlDTO.getSignalId());

        List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
        Map<Integer, SimpleActiveSignal> realTimeSignalItemMap = signalList.stream().collect(
                Collectors.toMap(SimpleActiveSignal::getSignalId, p -> p));

        if (realTimeSignalItemMap.get(simpleControlDTO.getSignalId()) != null) {
            simpleControlDTO.setCurrentValue(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getOriginalValue());
            simpleControlDTO.setUnit(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getUnit());
            simpleControlDTO.setSignalName(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getSignalName());
            simpleControlDTO.setSampleTime(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getSampleTime());

        }

        return simpleControlDTO;

    }

    @Override
    public ConfigControlItem findSimpleControlDTOsByEquipmentIdAndControlId(Integer equipmentId, Integer controlId) {
        List<ConfigControlItem> simpleControlDTOs =   controlMapper.findSimpleControlDTOsByEquipmentIdAndControlId(equipmentId,controlId);
        if (CollUtil.isEmpty(simpleControlDTOs)) {
            return null;
        }
        ConfigControlItem simpleControlDTO = simpleControlDTOs.get(0);
        List<Integer> signalIds = new ArrayList<>();
        signalIds.add(simpleControlDTO.getSignalId());

        List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
        Map<Integer, SimpleActiveSignal> realTimeSignalItemMap = signalList.stream().collect(
                Collectors.toMap(SimpleActiveSignal::getSignalId, p -> p));

        if (realTimeSignalItemMap.get(simpleControlDTO.getSignalId()) != null) {
            simpleControlDTO.setCurrentValue(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getOriginalValue());
            simpleControlDTO.setUnit(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getUnit());
            simpleControlDTO.setSignalName(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getSignalName());
            simpleControlDTO.setSampleTime(realTimeSignalItemMap.get(simpleControlDTO.getSignalId()).getSampleTime());

        }

        return simpleControlDTO;
    }

    @Override
    public void copyTemplateControl(Integer sourceTemplateId, Integer destTemplateId) {
        List<Control> controlList = this.findByEquipmentTemplateId(sourceTemplateId);
        controlList.forEach(e->e.setEquipmentTemplateId(destTemplateId));
        this.batchInsert(controlList);
        List<ControlMeanings> controlMeaningsList =  controlMeaningsService.findByEquipmentTemplateId(sourceTemplateId);
        controlMeaningsList.forEach(e -> e.setEquipmentTemplateId(destTemplateId));
        controlMeaningsService.batchInsert(controlMeaningsList);
    }

    @Override
    public void batchInsert(List<Control> controlList) {
        if (CollUtil.isEmpty(controlList)) {
            return;
        }
        controlMapper.batchInsert(controlList);
    }

    private List<Control> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return controlMapper.findByEquipmentTemplateId(equipmentTemplateId);
    }

    @Override
    public Integer findCommandIdByCommandCategory(Integer stationId, Integer equipmentId, Integer commandCategory) {
        return controlMapper.findCommandIdByCommandCategory(stationId, equipmentId, commandCategory);
    }
}
