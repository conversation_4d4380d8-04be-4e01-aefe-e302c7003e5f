package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.ActiveEventFilterTemplate;
import com.siteweb.monitoring.mapper.ActiveEventFilterTemplateMapper;
import com.siteweb.monitoring.service.ActiveEventFilterTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterTemplateServiceImpl
 * @createTime 2022-04-27 17:30:26
 */
@Service
public class ActiveEventFilterTemplateServiceImpl implements ActiveEventFilterTemplateService {

    @Autowired
    ActiveEventFilterTemplateMapper activeEventFilterTemplateMapper;

    @Override
    public int createActiveEventFilterTemplate(ActiveEventFilterTemplate activeEventFilterTemplate) {
        return activeEventFilterTemplateMapper.createActiveEventFilterTemplate(activeEventFilterTemplate);
    }

    @Override
    public int updateActiveEventFilterTemplate(ActiveEventFilterTemplate activeEventFilterTemplate) {
        return activeEventFilterTemplateMapper.updateById(activeEventFilterTemplate);
    }

    @Override
    public void deleteActiveEventFilterTemplate(Integer activeEventFilterTemplateId) {
        activeEventFilterTemplateMapper.deleteById(activeEventFilterTemplateId);
    }

    @Override
    public ActiveEventFilterTemplate findByActiveEventFilterTemplateId(Integer activeEventFilterTemplateId) {
        return activeEventFilterTemplateMapper.selectById(activeEventFilterTemplateId);
    }

    @Override
    public List<ActiveEventFilterTemplate> findByUserIdAndFilterType(Integer userId, String filterType) {
        if (filterType.equals("SignalViewFilter") || filterType.equals("MaskTemplate") || filterType.equals("MaskTemplateEv") || "UbitTemplate".equals(filterType)) {
            return activeEventFilterTemplateMapper.selectList(new QueryWrapper<ActiveEventFilterTemplate>().eq("FilterType", filterType).
                    and(wrapper -> wrapper.eq("UserId", userId).or().isNull("UserId")));
        }
        return activeEventFilterTemplateMapper.selectList(new QueryWrapper<ActiveEventFilterTemplate>().eq("UserId", userId).eq("FilterType", filterType));
    }

    @Override
    public int createOrUpdateTemplate(ActiveEventFilterTemplate template) {
        List<ActiveEventFilterTemplate> activeEventFilterTemplate = getActiveEventFilterTemplate(template.getUserId(), template.getFilterType());
        //存在则更新
        if (CollUtil.isNotEmpty(activeEventFilterTemplate)) {
            template.setActiveEventFilterTemplateId(activeEventFilterTemplate.get(0).getActiveEventFilterTemplateId());
            return this.updateActiveEventFilterTemplate(template);
        }
        return this.createActiveEventFilterTemplate(template);
    }

    /**
     * 通过用户id和模板类型查找模板
     *
     * @param userId     用户id
     * @param filterType 模板类型
     * @return {@link ActiveEventFilterTemplate}
     */
    private List<ActiveEventFilterTemplate> getActiveEventFilterTemplate(Integer userId, String filterType) {
        LambdaQueryWrapper<ActiveEventFilterTemplate> countWrapper = Wrappers.<ActiveEventFilterTemplate>lambdaQuery()
                .eq(ActiveEventFilterTemplate::getUserId, userId)
                .eq(ActiveEventFilterTemplate::getFilterType, filterType);
        return activeEventFilterTemplateMapper.selectList(countWrapper);
    }
}
