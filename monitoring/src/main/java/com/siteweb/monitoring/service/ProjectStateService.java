package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.ProjectStateHouse;
import com.siteweb.monitoring.vo.*;

import java.util.List;


public interface ProjectStateService {
    ProjectOperationResult setStationProject(StationProjectVO stationProjectVO, Integer useId);
    ProjectOperationResult deleteStationProject(Integer stationId, Integer userId);
    List<StationProjectDetail> queryStationProjectDetails(int userId, StationFilterVO stationFilterVO);
    StationProjectVO getStationProject(Integer stationId);
    void batchDeleteStationProject(List<Integer> ids, Integer userId);
    List<HouseProjectDetail> queryHouseProjectDetails(int userId);
    ProjectOperationResult setHouseProjectState(HouseProjectVO houseProjectVO, Integer userId);
    ProjectOperationResult deleteHouseProjectState(String houseId, Integer userId);
    ProjectStateHouse getHouseProjectStateById(String houseId);
    void batchDeleteHouseProject(List<String> ids, Integer userId);

}
