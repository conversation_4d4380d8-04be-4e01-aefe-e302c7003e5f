package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.monitoring.dto.ActiveEventOperationLogDTO;
import com.siteweb.monitoring.entity.ActiveEventOperationLog;
import com.siteweb.monitoring.mapper.ActiveEventOperationLogMapper;
import com.siteweb.monitoring.service.ActiveEventOperationLogService;
import com.siteweb.monitoring.vo.BatchActiveEventOperationLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ActiveEventOperationLogServiceImpl
 * @createTime 2022-04-09 14:15:28
 */
@Service
public class ActiveEventOperationLogServiceImpl implements ActiveEventOperationLogService {
    @Autowired
    ActiveEventOperationLogMapper activeEventOperationLogMapper;

    @Override
    public int createOperationLog(int userId, ActiveEventOperationLog operationLog) {
        operationLog.setOperatorId(userId);
        operationLog.setOperationTime(new Date());
        return activeEventOperationLogMapper.insert(operationLog);
    }

    @Override
    public List<ActiveEventOperationLogDTO> findBySequenceId(String sequenceId) {
        return activeEventOperationLogMapper.findBySequenceId(sequenceId);
    }

    @Override
    @Transactional
    public int batchCreateOperationLog(int userId, BatchActiveEventOperationLogVO vo) {
        if (CollUtil.isEmpty(vo.getSequenceIds()))
            return -1;
        if (CollUtil.isEmpty(vo.getStationIds()))
            return -1;
        if (CollUtil.isEmpty(vo.getEquipmentIds()))
            return -1;
        if (CollUtil.isEmpty(vo.getEventIds()))
            return -1;
        if (CollUtil.isEmpty(vo.getEventConditionIds()))
            return -1;
        if (CollUtil.isEmpty(vo.getStartTimes()))
            return -1;
        Date operationTime = new Date();
        List<ActiveEventOperationLog> createLogList = new ArrayList<>(Math.min(vo.getEquipmentIds().size(), GlobalConstants.BATCH_INSERT_MAX_COUNT));
        for (int i = 0; i < vo.getEquipmentIds().size(); i++) {
            ActiveEventOperationLog operationLog = new ActiveEventOperationLog();
            operationLog.setSequenceId(vo.getSequenceIds().get(i));
            operationLog.setStationId(vo.getStationIds().get(i));
            operationLog.setEquipmentId(vo.getEquipmentIds().get(i));
            operationLog.setEventId(vo.getEventIds().get(i));
            operationLog.setEventConditionId(vo.getEventConditionIds().get(i));
            operationLog.setStartTime(vo.getStartTimes().get(i));
            operationLog.setOperation(vo.getOperation());
            operationLog.setOperatorId(userId);
            operationLog.setDescription(vo.getDescription());
            operationLog.setOperationTime(operationTime);
            createLogList.add(operationLog);
            //批量新增数量超过1000立即新增
            if (CollUtil.size(createLogList) >= GlobalConstants.BATCH_INSERT_MAX_COUNT){
                batchCreate(createLogList);
                createLogList.clear();
            }
        }
        batchCreate(createLogList);
        return 1;
    }

    private void batchCreate(List<ActiveEventOperationLog> activeEventOperationLogList){
        if (CollUtil.isEmpty(activeEventOperationLogList)) {
            return;
        }
        activeEventOperationLogMapper.batchInsert(activeEventOperationLogList);
    }
}
