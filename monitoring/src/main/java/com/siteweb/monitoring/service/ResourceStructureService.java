package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.CapacityTypeEnum;

import java.util.Collection;
import java.util.List;

public interface ResourceStructureService {

    List<ResourceStructure> findResourceStructures();

    int createResourceStructure(ResourceStructure resourceStructure);

    int deleteById(Integer resourceStructureId);

    int updateResourceStructure(ResourceStructure resourceStructure);

    ResourceStructure getResourceStructureById(Integer resourceStructureId);

    ResourceStructure findById(Integer resourceStructureId);


    /**
     * 获取层级树包含设备
     *
     * @return
     */
    ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentDTOTree();

    /**
     * 获取层级树，不包含设备
     *
     * @return
     */
    ResourceStructureDTO getResourceStructureDTOByParentId(int topParentResourceStructureId, Integer userId);

    /**
     * 根据当前用户获取层级树
     *
     * @param userId
     * @return
     */
    ResourceStructureTreeDTO findResourceStructureTreeByUserId(Integer userId);


    /**
     * @param userId     用户id,用于权限过滤
     * @param existAlarm 是否需要告警状态
     * @return {@link ResourceStructureEquipmentTreeDTO} 层级树
     */
    ResourceStructureEquipmentTreeDTO getParkAlarmPositionOverviewInfo(Integer userId, Boolean existAlarm);

    /**
     * 构建基本
     * @param resourceStructureList
     * @param equipmentList
     * @return {@link ResourceStructureEquipmentTreeDTO}
     */
    ResourceStructureEquipmentTreeDTO buildTree(List<ResourceStructureEquipmentTreeDTO> resourceStructureList, List<EquipmentDTO> equipmentList);

    /**
     * 根据objectTypeId和userId获取结构列表
     *
     * @param objectTypeIds
     * @param userId
     * @return
     */
    List<ResourceStructure> findByObjectTypeIdAndUserId(List<Integer> objectTypeIds, Integer userId);

    /**
     * 根据用户id获取所拥有的层级资源
     *
     * @param userId 用户id
     * @return {@link List}<{@link ResourceStructure}>
     */
    List<ResourceStructure> findResourceStructureByUserId(Integer userId);


    /**
     * 获取登录用户拥有的层级权限
     *
     * @param userId 登录用户id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findResourceStructureIdsByUserId(Integer userId);

    /**
     * 获取登录用户拥有的层级权限,会寻找子层级
     *
     * @param filterResourceStructureIds 层级过滤条件 可为空
     */
    List<Integer> findChildResourceStructureIdsByUserId(Integer userId, String filterResourceStructureIds);

    List<Integer> findThisHierarchyResourceStructureIdsByUserId(Integer userId);

    List<Integer> findResourceStructureIdsByUserIdAndResourceStructureIds(Integer userId, String filterResourceStructureIds);

    String findSubResourceStructureByEquipmentCategory(Integer rootId, Integer equipmentCategory);

    List<ResourceStructureAlarmState> getResourceStructureAlarmState(List<Integer> resourceStructureIds);

    ResourceStructure findResourceStructureByName(String resourceStructureName);

    /**
     * 通过父及id查找层级
     * @param parentIds 父层级ids
     * @param userId 登录用户id
     * @return {@link List}<{@link ResourceStructureDTO}>
     */
    List<ResourceStructureDTO> findResourceStructureByParentId(Integer userId,String parentIds);

    /**
     * 根据用户id获取所有层级权限，再根据传入的层级id，获取到用户具有权限的所有层级id（包含子孙）
     * 二次过滤，先根据用户权限过滤，再根据传入层级id，获取到具有权限的层级id集合
     */
    List<Integer> findResourceIdsByUserIdAndResourceStructureIds(Integer userId, Collection<Integer> resourceStructureIds);

    /**
     * 根据用户id获取所有层级权限，再根据传入的层级id，获取到用户具有权限的所有层级id（包含子孙）
     * 二次过滤，先根据用户权限过滤，再根据传入层级id，获取到具有权限的层级id集合
     */
    List<ResourceStructure> findResourceByUserIdAndResourceStructureIds(Integer userId, Collection<Integer> resourceStructureIds);

    /**
     * 根据层级id集合获取层级信息
     */
    List<ResourceStructure> findResourceByResourceStructureIds(Collection<Integer> resourceStructureIds);

    int updateBatchResourceStructureOrder(List<UpdateResourceStructureOrderDto> updateResourceStructureOrderDtos);

    ResourceStructureTreeDTO findResourceStructuresByCustomtype(Integer userId, String typeIds);

    /**
     * 获取层级扩展属性 容量
     */
    double getResourceStructureCapacity(Integer resourceStructureId, CapacityTypeEnum capacityTypeEnum);

    /**
     * 根据多个设备基础类型ID获取资源结构设备树
     *
     * @param equipmentBaseTypeIds 设备基础类型ID列表
     * @return {@link ResourceStructureEquipmentTreeDTO} 层级树
     */
    ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentTreeByEquipmentBaseTypeIds(List<Integer> equipmentBaseTypeIds);
}
