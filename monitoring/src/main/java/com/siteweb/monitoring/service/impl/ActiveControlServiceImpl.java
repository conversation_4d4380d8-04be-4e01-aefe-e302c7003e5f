package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ActiveControlDTO;
import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.dto.ControlDTO;
import com.siteweb.monitoring.entity.ActiveControl;
import com.siteweb.monitoring.entity.Control;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.mamager.ActiveControlManager;
import com.siteweb.monitoring.mamager.ConfigControlManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.ActiveControlMapper;
import com.siteweb.monitoring.mapper.ControlMapper;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.vo.ActiveControlOperationVO;
import com.siteweb.monitoring.vo.ControlCommandVO;
import com.siteweb.monitoring.vo.SendControlOperationDetailsDTO;
import com.siteweb.utility.entity.OperationDetail;
import com.siteweb.utility.service.OperationDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("activeControlService")
public class ActiveControlServiceImpl implements ActiveControlService {

    @Autowired
    private ActiveControlMapper activeControlMapper;

    @Autowired
    private EquipmentManager equipmentManager;

    @Autowired
    private ActiveControlManager activeControlManager;

    @Autowired
    ConfigControlManager configControlManager;
    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    AccountService accountService;
    @Autowired
    OperationDetailService operationDetailService;
    @Autowired
    private ControlMapper controlMapper;

    @Override
    public List<ActiveControl> findActiveControls() {
        return activeControlMapper.selectList(null);
    }

    @Override
    public int createActiveControl(ActiveControl activeControl) {
        return activeControlMapper.insert(activeControl);
    }

    @Override
    public int deleteById(Integer activeControlId) {
        return    activeControlMapper.deleteById(activeControlId);
    }

    @Override
    public int updateActiveControl(ActiveControl activeControl) {
        return activeControlMapper.updateById(activeControl);
    }

    @Override
    public ActiveControl findById(Integer activeControlId) {
        return activeControlMapper.selectById(activeControlId);
    }

    @Override
    public List<ActiveControlDTO> getActiveControlByEquipmentId(Integer equipmentId) {
        List<ActiveControlDTO> result = new ArrayList<>();
        List<ActiveControl> activeControls = activeControlMapper.getActiveControlByEquipmentId(equipmentId);
        for(ActiveControl activeControl:activeControls){
            ActiveControlDTO activeControlDTO = new ActiveControlDTO(activeControl);
            activeControlDTO.setEquipmentPosition(equipmentManager.getEquipmentPosition(equipmentId));
            result.add(activeControlDTO);
        }
        return  result;

    }

    @Override
    public boolean confirmControlCommand(int userId, ActiveControlOperationVO activeControlOperationVO) {
        for(Integer serialNo:activeControlOperationVO.getSerialNos()) {
            activeControlManager.confirmControlCommandBySeq(userId, serialNo);
        }
        this.recordAudit(userId,localeMessageSourceUtil.getMessage("audit.report.confirmControlCommand"), activeControlOperationVO.getSerialNos());
        return true;
    }

    @Override
    public ControlResultType sendControlCommand(ControlCommandVO controlCommandVO, int userId) {
        if (ObjectUtil.isNull(controlCommandVO)) {
            return ControlResultType.PARAM_ERROR;
        }
        Equipment  equipment = equipmentManager.getEquipmentById(controlCommandVO.getEquipmentId());
        if (ObjectUtil.isNull(equipment)) {
            return ControlResultType.PARAM_ERROR;
        }
        if(ObjectUtil.isNull(controlCommandVO.getStationId())){
            controlCommandVO.setStationId(equipment.getStationId());
        }
        controlCommandVO.setStationId(equipment.getStationId());
        this.recordAudit(userId,controlCommandVO);
        //发
        return activeControlManager.sendControl(controlCommandVO,userId);
    }

    @Override
    public ControlResultType sendControlCommandByEquipmentIdAndCmdToken(Integer userId, Integer equipmentId, String cmdToken, String value, String description) {
        List<ConfigControlItem> controlItemList = controlMapper.findSimpleControlDTOsByEquipmentIdAndCmdToken(equipmentId, cmdToken);
        if (CollUtil.isEmpty(controlItemList)) {
            log.error("没有查找到控制命令，equipmentId:{},cmdToken:{}", equipmentId, cmdToken);
            return ControlResultType.PARAM_ERROR;
        }
        if (CollUtil.size(controlItemList) > 1) {
            log.warn("equipmentId:{},cmdToken:{},查询到多条控制命令，默认取第一条", equipmentId, cmdToken);
        }
        ConfigControlItem controlItem = controlItemList.get(0);
        ControlCommandVO controlCommandVO = ControlCommandVO.builder()
                                                 .stationId(controlItem.getStationId())
                                                 .equipmentId(equipmentId)
                                                 .controlId(controlItem.getControlId())
                                                 .startTime(new Date())
                                                 .setValue(value)
                                                 .description(description)
                                                 .build();
        return sendControlCommand(controlCommandVO, userId);
    }

    public boolean reSendControlCommand(int userId, ActiveControlOperationVO activeControlOperationVO){
        for(Integer serialNo:activeControlOperationVO.getSerialNos()) {
            activeControlManager.reSendControlCommand(userId, serialNo);
        }
        this.recordAudit(userId, localeMessageSourceUtil.getMessage("audit.report.reSendControlCommand"), activeControlOperationVO.getSerialNos());
        return true;
    }

    @Override
    public List<ControlDTO> findAllControl(Set<Integer> equipmentIdSet, Date startTime, Date endTime) {
        if (CollUtil.isEmpty(equipmentIdSet)) {
            return Collections.emptyList();
        }
        return activeControlMapper.findAllControl(equipmentIdSet,startTime, endTime);
    }

    /**
     * 控制命令审计记录
     *
     * @param userId
     * @param operating 操作
     * @param serialNos 控制命令序列号
     */
    @Override
    public void recordAudit(Integer userId, String operating, List<Integer> serialNos){
        String userName = Optional.ofNullable(accountService.findByUserId(userId)).orElse(new AccountDTO()).getUserName();
        securityAuditManager.recordAuditReport(userName,AuditReportTypeEnum.CONTROL.getLevel(),AuditReportTypeEnum.CONTROL.getDescribe(), operating + "：" + this.findControlEquipmentNames(serialNos),IpUtil.getIpAddr(),"");
    }

    @Override
    public boolean sendControlOperationDetail(SendControlOperationDetailsDTO sendControlOperationDetailsDTO) {
        Control control = configControlManager.getControlByStationIdEquipmentIdControlId(sendControlOperationDetailsDTO.getStationId(), sendControlOperationDetailsDTO.getEquipmentId(), sendControlOperationDetailsDTO.getControlId());
        //device name发送控制命令：command name 操作员：user1 审核员：user2 控制值: command value 控制含义：command meaning
        String auditor = accountService.findUserNameByLogonId(sendControlOperationDetailsDTO.getGuardianAccount());
        String operator = accountService.findUserNameByUserId(sendControlOperationDetailsDTO.getSendUserId());
        String meanings = CharSequenceUtil.isBlank(sendControlOperationDetailsDTO.getMeanings()) ? "" : " " + String.format(localeMessageSourceUtil.getMessage("common.guardian.controlMeanings"), sendControlOperationDetailsDTO.getMeanings());
        String equipmentName = Optional.ofNullable(equipmentManager.getEquipmentById(sendControlOperationDetailsDTO.getEquipmentId())).map(Equipment::getEquipmentName).orElse("");
        OperationDetail operationDetail = OperationDetail.builder()
                                                         .userId(sendControlOperationDetailsDTO.getSendUserId())
                                                         .objectId(control.getEquipmentTemplateId() + "." + control.getControlId())
                                                         .objectType(17)
                                                         .propertyName(control.getControlName())
                                                         .operationTime(new Date())
                                                         .operationType(equipmentName + localeMessageSourceUtil.getMessage("audit.report.sendControlCommand") + ":")
                                                         .oldValue("")
                                                         .newValue(String.format(localeMessageSourceUtil.getMessage("common.guardian.sendControl"), operator, auditor,sendControlOperationDetailsDTO.getSetValue()) + meanings)
                                                         .build();
        operationDetailService.createOperationDetail(operationDetail);
        return true;
    }

    @Override
    public void recordAudit(Integer userId,ControlCommandVO  controlCommandVO){
        Control control = configControlManager.getControlByStationIdEquipmentIdControlId(controlCommandVO.getStationId(), controlCommandVO.getEquipmentId(), controlCommandVO.getControlId());
        Equipment equipment = equipmentManager.getEquipmentById(controlCommandVO.getEquipmentId());
        String userName = Optional.ofNullable(accountService.findByUserId(userId)).orElse(new AccountDTO()).getUserName();
        securityAuditManager.recordAuditReport(userName, AuditReportTypeEnum.CONTROL.getLevel(), AuditReportTypeEnum.CONTROL.getDescribe(), localeMessageSourceUtil.getMessage("audit.report.sendControlCommand") + "：" + equipment.getEquipmentName() + "." + control.getControlName(), IpUtil.getIpAddr(), "");
    }

    public String findControlEquipmentNames(List<Integer> serialNos) {
        if (CollUtil.isEmpty(serialNos)) {
            return "";
        }
        return activeControlMapper.selectList(Wrappers.<ActiveControl>lambdaQuery()
                                                      .select(ActiveControl::getEquipmentName,ActiveControl::getControlName)
                                                      .in(ActiveControl::getSerialNo, serialNos))
                                  .stream()
                                  .map(e -> e.getEquipmentName() + "." + e.getControlName())
                                  .collect(Collectors.joining("、"));
    }
}
