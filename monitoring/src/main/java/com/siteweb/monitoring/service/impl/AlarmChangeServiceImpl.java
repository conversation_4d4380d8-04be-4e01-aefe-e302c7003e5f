package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.mapper.AlarmChangeMapper;
import com.siteweb.monitoring.service.AlarmChangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("alarmChangeService")
public class AlarmChangeServiceImpl implements AlarmChangeService {

    @Autowired
    private AlarmChangeMapper alarmChangeMapper;

    @Override
    public List<AlarmChange> findAlarmChanges() {
        return alarmChangeMapper.selectList(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createAlarmChange(AlarmChange alarmChange) {
        return alarmChangeMapper.insert(alarmChange);
    }

    @Override
    public int deleteById(Long alarmChangeId) {
        return    alarmChangeMapper.deleteById(alarmChangeId);
    }

    @Override
    public int updateAlarmChange(AlarmChange alarmChange) {
        return alarmChangeMapper.updateById(alarmChange);
    }

    @Override
    public AlarmChange findById(Long alarmChangeId) {
        return alarmChangeMapper.selectById(alarmChangeId);
    }

    @Override
    public void batchInsert(List<AlarmChange> batchInsertAlarmChangeList) {
        if (CollUtil.isEmpty(batchInsertAlarmChangeList)) {
            return;
        }
        alarmChangeMapper.batchInsert(batchInsertAlarmChangeList);
    }
}
