package com.siteweb.monitoring.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.EquipmentMaskDTO;
import com.siteweb.monitoring.dto.EquipmentMaskFilterCreateDTO;
import com.siteweb.monitoring.dto.EquipmentMaskFilterDTO;
import com.siteweb.monitoring.dto.SimpleEquipmentMaskDTO;
import com.siteweb.monitoring.entity.EquipmentMask;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import com.siteweb.monitoring.vo.EquipmentMaskVO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface EquipmentMaskService {

    void saveEquipmentMask(EquipmentMaskVO equipmentMaskVO, Integer userId);

    void batchCreateEquipmentMasks(BatchEquipmentMaskVO vo, Integer userId);

    Page<EquipmentMaskDTO> findEquipmentMaskByKeywords(EquipmentMaskFilterDTO equipmentMaskFilterDTO, Page<EquipmentMaskDTO> page);

    int deleteEquipmentMask(EquipmentMask equipmentMask, Integer userId);

    void batchDeleteEquipmentMasks(List<Integer> equipmentIds, Integer userId);

    void batchDeleteEquipmentMasksByResourceStructureId(Integer resourceStructureId, Integer userId);

    void deleteAllEquipmentMasks(Integer userId);

    EquipmentMask findById(Integer equipmentId);

    EquipmentMaskDTO findEquipmentMaskDTOByEquipmentId(Integer equipmentId);

    List<SimpleEquipmentMaskDTO> findSimpleEquipmentMaskDTOsByResourceStructureId(Integer resourceStructureId);
    /**
     * 获取分时段屏蔽的设备id
     * @return {@link List}<{@link Integer}>
     */
    Set<Integer> findIntervalMaskEquipmentIds();
    boolean isMaskEffective(Integer equipmentId);

    /**
     * 保存设备屏蔽（全时段）
     * 对应存储过程：PAM_SaveEquipmentMask
     */
    void doSaveEquipmentMask(Integer stationId, Integer equipmentId, Integer timeGroupId, Date startTime, Date endTime, Integer userId, String reason);

    /**
     * 保存设备屏蔽（分时段）
     * 对应存储过程：PAM_SaveEquipmentSeprateMask
     * 通过设备基类id获取设备屏蔽列表
     *
     * @return {@link Page}<{@link EquipmentMaskDTO}>
     */
    void doSaveSeparateEquipmentMask(Integer stationId, Integer equipmentId, Integer timeGroupId, String timeGroupChar, Integer week, Integer userId, String reason);

    void insertAlarmMaskLog(EquipmentMask equipmentMask, int operationType, Integer timeGroupCategory, String timeGroupChars, int userId);

    void saveOrUpdateEventMask(Integer stationId, Integer equipmentId, Integer timeGroupId, Integer userId, String reason, Date startTime, Date endTime);

    EquipmentMask findStationIdAndEqId(Integer stationId, Integer equipmentId);

    void endStockActiveEventByEventMask(Integer stationId, Integer equipmentId, boolean isSeparate);
    Page<SimpleEquipmentMaskDTO> findSimpleEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterDTO equipmentMaskFilterDTO, Page<SimpleEquipmentMaskDTO> page);
    void deleteEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterDTO equipmentMaskFilterDTO);

    void createEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterCreateDTO equipmentMaskFilterCreateDTO);
}

