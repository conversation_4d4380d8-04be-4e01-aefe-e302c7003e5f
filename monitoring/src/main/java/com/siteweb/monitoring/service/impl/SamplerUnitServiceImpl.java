package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.mapper.SamplerUnitMapper;
import com.siteweb.monitoring.service.SamplerUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service("samplerUnitService")
public class SamplerUnitServiceImpl implements SamplerUnitService {

    @Autowired
    private SamplerUnitMapper samplerUnitMapper;

    @Override
    public List<SamplerUnit> findSamplerUnits() {
        return samplerUnitMapper.selectList(null);
    }

    @Override
    public int createSamplerUnit(SamplerUnit samplerUnit) {
        return samplerUnitMapper.insert(samplerUnit);
    }

    @Override
    public int deleteById(Integer samplerUnitId) {
        return    samplerUnitMapper.deleteById(samplerUnitId);
    }

    @Override
    public int updateSamplerUnit(SamplerUnit samplerUnit) {
        return samplerUnitMapper.updateById(samplerUnit);
    }

    @Override
    public SamplerUnit findById(Integer id) {
        return samplerUnitMapper.selectById(id);
    }

    @Override
    public SamplerUnit findBySamplerUnitId(Integer samplerUnitId) {
        return samplerUnitMapper.selectOne(Wrappers.<SamplerUnit>lambdaQuery()
                                                   .eq(SamplerUnit::getSamplerUnitId, samplerUnitId));
    }

    @Override
    public List<SamplerUnit> findByMonitUnitIdAndPortId(Integer monitUnitId, Integer portId) {
        return samplerUnitMapper.selectList(Wrappers.<SamplerUnit>lambdaQuery()
                                                    .eq(SamplerUnit::getMonitorUnitId, monitUnitId)
                                                    .eq(SamplerUnit::getPortId, portId));
    }

    @Override
    public SamplerUnit findSamplerUnit(Integer monitorUnitId, String samplerUnitName,String portName) {
        return samplerUnitMapper.findSamplerUnit(monitorUnitId, samplerUnitName, portName);
    }

    @Override
    public List<SamplerUnit> findByIds(Collection<Integer> sampleUnitIds) {
        if (CollUtil.isEmpty(sampleUnitIds)) {
            return new ArrayList<>();
        }
        LambdaUpdateWrapper<SamplerUnit> wrapper = Wrappers.lambdaUpdate(SamplerUnit.class);
        wrapper.in(SamplerUnit::getSamplerUnitId, sampleUnitIds);
        return samplerUnitMapper.selectList(wrapper);
    }

    @Override
    public List<SamplerUnit> findSamplerUnitsByMonitorUnitIdAndSamplerUnitIdSql(Collection<String> batSql) {
        if (CollUtil.isEmpty(batSql)) {
            return new ArrayList<>();
        }
        LambdaUpdateWrapper<SamplerUnit> wrapper = Wrappers.lambdaUpdate(SamplerUnit.class);
        wrapper.apply(CollUtil.join(batSql, " or "));
        return samplerUnitMapper.selectList(wrapper);
    }

    @Override
    public List<SamplerUnit> findByEquipmentIds(Collection<Integer> equipmentIds) {
        return samplerUnitMapper.findByEquipmentIds(equipmentIds);
    }

    @Override
    public List<SamplerUnit> findSamplerUnitsByMonitorUnitId(Integer monitorUnitId) {
        return samplerUnitMapper.selectList(Wrappers.<SamplerUnit>lambdaQuery().eq(SamplerUnit::getMonitorUnitId, monitorUnitId));
    }
}
