package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.CapacityTypeEnum;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.ResourceStructureMapper;
import com.siteweb.monitoring.service.ConfigChangeMacroLogService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.ResourceObjectService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.util.ExtendedFieldUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("resourceStructureService")
public class ResourceStructureServiceImpl implements ResourceStructureService, ResourceObjectService {

    private static final int CONFIG_ID = 27;
    private static final int EDIT_TYPE = 2;
    @Autowired
    ResourceStructureMapper resourceStructureMapper;

    @Autowired
    EquipmentService equipmentService;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    RolePermissionMapService rolePermissionMapService;

    @Autowired
    RegionMapService regionMapService;

    @Autowired
    RegionService regionService;

    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    ConfigChangeMacroLogService configChangeMacroLogService;
    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    Boolean byteDanceEnable;
    @Autowired
    ExtendedFieldUtil extendedFieldUtil;

    @Override
    public List<ResourceStructure> findResourceStructures() {
        return resourceStructureMapper.selectList(null)
                                      .stream()
                                      .sorted(Comparator.comparing(ResourceStructure::getSortValue).thenComparing(ResourceStructure::getResourceStructureName))
                                      .toList();
    }

    @Override
    public int createResourceStructure(ResourceStructure resourceStructure) {
        return resourceStructureMapper.insert(resourceStructure);
    }

    @Override
    public int deleteById(Integer resourceStructureId) {
        return resourceStructureMapper.deleteById(resourceStructureId);
    }

    @Override
    public int updateResourceStructure(ResourceStructure resourceStructure) {
        int result = resourceStructureMapper.updateEntity(resourceStructure);
        configChangeMacroLogService.saveConfigChangeLog(String.valueOf(resourceStructure.getResourceStructureId()), CONFIG_ID, EDIT_TYPE);
        return result;
    }

    @Override
    public ResourceStructure getResourceStructureById(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureMapper.selectById(resourceStructureId);
        if (Objects.isNull(resourceStructure)) {
            throw new BusinessException("不存在的层级");
        }
        if (Boolean.TRUE.equals(byteDanceEnable)) {
            // 字节开关打开，需要判断扩展字段是否存在额定容量属性
            resourceStructure.setExtendedField(extendedFieldUtil.ensureRequiredFields(resourceStructure.getExtendedField()));
        }
        return resourceStructure;
    }


    @Override
    public ResourceStructure findById(Integer resourceStructureId) {
        return resourceStructureMapper.selectById(resourceStructureId);
    }

    @Override
    public ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentDTOTree() {
        // 获取所有层级
        List<ResourceStructureEquipmentTreeDTO> resourceStructures = this.findResourceStructures()
                                                                         .stream()
                                                                         .map(ResourceStructureEquipmentTreeDTO::new)
                                                                         .toList();
        // 获取所有设备
        List<EquipmentDTO> equipmentList = equipmentService.findEquipmentDTOs();
        return this.buildTree(resourceStructures, equipmentList);
    }

    @Override
    public ResourceStructureDTO getResourceStructureDTOByParentId(int topParentResourceStructureId, Integer userId) {
        List<Region> regionList = regionService.findAllRegionsByUserId(userId);
        List<ResourceStructure> resourceStructures;
        int topResourceStructureId = 0;
        LinkedHashMap<Integer, ResourceStructureDTO> resourceStructureDTOLinkedHashMap = new LinkedHashMap<>();
        //RegionId为-1代表拥有所有区域的权限
        if (regionList.stream().anyMatch(region -> region.getRegionId().equals(-1))) {
            resourceStructures = this.findResourceStructures();
            for (ResourceStructure resourceStructure : resourceStructures) {
                ResourceStructureDTO resourceStructureDTO = new ResourceStructureDTO(resourceStructure);
                resourceStructureDTO.setAccessFlag(true);
                resourceStructureDTOLinkedHashMap.put(resourceStructure.getResourceStructureId(), resourceStructureDTO);
                if (resourceStructure.getParentResourceStructureId().equals(topParentResourceStructureId)) {
                    topResourceStructureId = resourceStructure.getResourceStructureId();
                }
            }
        } else {
            List<RegionMap> regionMapList = regionMapService.findByRegionIds(regionList.stream().map(Region::getRegionId).toList());
            for (RegionMap regionMap : regionMapList) {
                //获取所有父级节点
                List<ResourceStructureDTO> resourceStructureDTOS = resourceStructureManager.findAllParentStructureListById(regionMap.getResourceStructureId())
                                                                                           .stream()
                                                                                           .map(ResourceStructureDTO::new)
                                                                                           .toList();
                for (ResourceStructureDTO resourceStructureDTO : resourceStructureDTOS) {
                    if (regionMap.getResourceStructureId().equals(resourceStructureDTO.getId())) {
                        resourceStructureDTO.setAccessFlag(true);
                    }
                    resourceStructureDTOLinkedHashMap.putIfAbsent(resourceStructureDTO.getId(), resourceStructureDTO);
                    if (resourceStructureDTO.getParentId().equals(topParentResourceStructureId)) {
                        topResourceStructureId = resourceStructureDTO.getId();
                    }
                }
            }
        }
        List<ResourceStructureDTO> resourceStructureDTOList = resourceStructureDTOLinkedHashMap.values()
                                                                                               .stream()
                                                                                               .sorted(Comparator.comparing(ResourceStructureDTO::getSortValue).thenComparing(ResourceStructureDTO::getName))
                                                                                               .toList();
        for (ResourceStructureDTO resourceStructureDTO : resourceStructureDTOList) {
            if (!resourceStructureDTO.getParentId().equals(topParentResourceStructureId) && resourceStructureDTOLinkedHashMap.containsKey(resourceStructureDTO.getParentId())) {
                ResourceStructureDTO pResourceStructureDTO = resourceStructureDTOLinkedHashMap.get(resourceStructureDTO.getParentId());
                pResourceStructureDTO.getChildren().add(resourceStructureDTO);
            }
        }
        return resourceStructureDTOLinkedHashMap.get(topResourceStructureId);
    }

    @Override
    public ResourceStructureTreeDTO findResourceStructureTreeByUserId(Integer userId) {
        // 获取当前用户拥有权限集合
        Set<Integer> permissionIds = rolePermissionMapService.findRolePermissionsByUserId(userId, PermissionCategoryEnum.REGION.getPermissionCategoryId());

        // 判断当前用户是否拥有该组的所有权限,有则返回整棵树
        if (permissionIds.contains(-1)) {
            List<ResourceStructureTreeDTO> resourceStructureTreeDTOS = resourceStructureManager.getTreeDtoAll().stream().map(e -> {
                ResourceStructureTreeDTO resourceStructureTreeDTO = new ResourceStructureTreeDTO(e);
                resourceStructureTreeDTO.setAccessFlag(true);
                return resourceStructureTreeDTO;
            }).toList();
            return resourceStructureManager.resourceStructureTreeDTOBuildTree(resourceStructureTreeDTOS);
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        // 当前用户具有权限的 resourceStructureIds 集合
        Set<Integer> resourceStructureIds = regionMapList.stream().map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return null;
        }

        List<ResourceStructureTreeDTO> listRecord = new ArrayList<>(resourceStructureIds.size());
        Map<Integer, ResourceStructureTreeDTO> param = resourceStructureManager.getTreeDtoAll().stream().collect(Collectors.toMap(ResourceStructureTreeDTO::getId, ResourceStructureTreeDTO::new));

        if (CollUtil.isNotEmpty(param)) {
            // 循环
            Set<ResourceStructureTreeDTO> resourceStructureList = new HashSet<>(resourceStructureIds.size());
            // 父类id
            Set<Integer> parentResourceStructureIds = new HashSet<>(1024);

            // 查找当前层级及父层级id
            for (Integer id : resourceStructureIds) {
                ResourceStructureTreeDTO resourceStructureTreeDTO = param.get(id);
                if (resourceStructureTreeDTO != null) {
                    resourceStructureTreeDTO.setAccessFlag(true);
                    resourceStructureList.add(resourceStructureTreeDTO);
                    // 查询所有父类
                    ResourceStructure resourceStructureById = resourceStructureManager.getResourceStructureById(resourceStructureTreeDTO.getId());
                    // 使用levelOfPath获取父类所有id
                    parentResourceStructureIds.addAll(
                            Arrays.stream(resourceStructureById.getLevelOfPath().split("\\."))
                                    .map(Integer::parseInt).filter(e -> !Objects.equals(id, e)).collect(Collectors.toSet()));
                }
            }
            listRecord.addAll(resourceStructureList);
            listRecord.addAll(parentResourceStructureIds.stream().map(param::get).toList());
        }

        return resourceStructureManager.resourceStructureTreeDTOBuildTree(listRecord.stream()
                                                                                    .sorted(Comparator.comparing(ResourceStructureTreeDTO::getSortValue).thenComparing(ResourceStructureTreeDTO::getName))
                                                                                    .toList());
    }

    @Override
    public ResourceStructureEquipmentTreeDTO getParkAlarmPositionOverviewInfo(Integer userId, Boolean existAlarm) {
        List<ResourceStructureEquipmentTreeDTO> resourceStructureTreeDtos = this.findResourceStructureTreeDtoByUserId(userId);
        List<EquipmentDTO> equipmentDTOs = equipmentService.findEquipmentDTOsByUserId(userId);
        if (Boolean.TRUE.equals(existAlarm)) {
            //设置设备告警状态
            this.setEquipmentExtendState(equipmentDTOs);
        }
        //设置可点击的层级
        this.setResourceStructure(userId, resourceStructureTreeDtos);
        return this.buildTree(resourceStructureTreeDtos, equipmentDTOs);
    }

    /**
     * 设置层级是否可点击
     *
     * @param userId                    用户id
     * @param resourceStructureTreeDtos 层级集合
     */
    private void setResourceStructure(Integer userId, List<ResourceStructureEquipmentTreeDTO> resourceStructureTreeDtos) {
        //拥有所有区域的权限 获取所有层级资源都可点击
        if (Boolean.TRUE.equals(regionService.isAllRegion(userId))) {
            for (ResourceStructureEquipmentTreeDTO resourceStructureTreeDto : resourceStructureTreeDtos) {
                resourceStructureTreeDto.setAccessFlag(true);
            }
            return;
        }
        //部分资源可点击
        Map<Integer, ResourceStructureEquipmentTreeDTO> accessResource = resourceStructureTreeDtos.stream()
                                                                                                  .collect(Collectors.toMap(ResourceStructureEquipmentTreeDTO::getRId, v -> v));
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        for (RegionMap regionMap : regionMapList) {
            accessResource.computeIfPresent(regionMap.getResourceStructureId(), (key, value) -> {
                value.setAccessFlag(true);
                return value;
            });
        }
    }

    public ResourceStructureEquipmentTreeDTO buildTree(List<ResourceStructureEquipmentTreeDTO> resourceStructureList, List<EquipmentDTO> equipmentList) {
        if (CollUtil.isEmpty(resourceStructureList)) {
            return null;
        }

        int rootId = 0;
        LinkedHashMap<Integer, ResourceStructureEquipmentTreeDTO> resourceStructureDTOLinkedHashMap = new LinkedHashMap<>();
        for (ResourceStructureEquipmentTreeDTO resourceStructure : resourceStructureList) {
            resourceStructureDTOLinkedHashMap.put(resourceStructure.getRId(), resourceStructure);
            if (resourceStructure.getParentId() == 0) {
                rootId = resourceStructure.getRId();
            }
        }
        // 设备挂载
        if (CollUtil.isNotEmpty(equipmentList)) {
            for (EquipmentDTO equipment : equipmentList) {
                if (resourceStructureDTOLinkedHashMap.containsKey(equipment.getRId())) {
                    ResourceStructureEquipmentTreeDTO resourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(equipment.getRId());
                    resourceStructureEquipmentDTO.getEqChildren().add(equipment);
                }
            }
        }
        //层级挂载
        for (ResourceStructureEquipmentTreeDTO resourceStructureEquipmentDTO : resourceStructureDTOLinkedHashMap.values()) {
            if (resourceStructureEquipmentDTO.getParentId() != 0 && resourceStructureDTOLinkedHashMap.containsKey(resourceStructureEquipmentDTO.getParentId())) {
                ResourceStructureEquipmentTreeDTO pResourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(resourceStructureEquipmentDTO.getParentId());
                pResourceStructureEquipmentDTO.getChildren().add(resourceStructureEquipmentDTO);
            }
        }
        return resourceStructureDTOLinkedHashMap.get(rootId);
    }

    /**
     * 查找自己拥有权限的层级资源
     *
     * @param userId 用户id
     * @return {@link List}<{@link ResourceStructureEquipmentTreeDTO}>
     */
    private List<ResourceStructureEquipmentTreeDTO> findResourceStructureTreeDtoByUserId(Integer userId) {
        //拥有所有区域的权限 获取所有层级资源
        if (regionService.isAllRegion(userId)) {
            return resourceStructureManager.getAll()
                                           .stream()
                                           .map(ResourceStructureEquipmentTreeDTO::new)
                                           .toList();
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        Set<Integer> resourceStructureIdSet = regionMapList.stream().map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        Set<ResourceStructureEquipmentTreeDTO> resourceStructureList = new HashSet<>();
        for (Integer resourceStructureId : resourceStructureIdSet) {
            //获取所有父级节点
            List<ResourceStructureEquipmentTreeDTO> resourceStructureEquipmentTreeDTOS = resourceStructureManager.findAllParentStructureListById(resourceStructureId)
                                                                                                                 .stream()
                                                                                                                 .map(ResourceStructureEquipmentTreeDTO::new)
                                                                                                                 .toList();
            resourceStructureList.addAll(resourceStructureEquipmentTreeDTOS);
        }
        return resourceStructureList.stream()
                                    .sorted(Comparator.comparing(ResourceStructureEquipmentTreeDTO::getSortValue).thenComparing(ResourceStructureEquipmentTreeDTO::getRName))
                                    .toList();
    }

    /**
     * 根据userId，找到有权限的所有层级
     * @param userId 登录用户id
     * @return
     */
    public List<Integer> findResourceStructureIdsByUserId(Integer userId) {
        List<ResourceStructure> resourceStructureTreeDtoByUserId = this.findResourceStructureByUserId(userId);
        return resourceStructureTreeDtoByUserId.stream()
                .map(ResourceStructure::getResourceStructureId)
                .toList();
    }

    @Override
    public List<Integer> findChildResourceStructureIdsByUserId(Integer userId, String filterResourceStructureIds) {
        if (CharSequenceUtil.isNotBlank(filterResourceStructureIds)) {
            return findResourceIdsByUserIdAndResourceStructureIds(userId, StringUtils.splitToIntegerList(filterResourceStructureIds));
        }
        return findResourceStructureIdsByUserId(userId);
    }

    /**
     * 根据userId，找到有权限的所有层级，不获取有权限层级的父层级
     * @param userId 用户Id
     * @return
     */
    @Override
    public List<Integer> findThisHierarchyResourceStructureIdsByUserId(Integer userId) {
        //-1拥有所有区域的权限
        if (Boolean.TRUE.equals(regionService.isAllRegion(userId))) {
            return resourceStructureManager.getAll().stream().map(ResourceStructure::getResourceStructureId).toList();
        }
        return regionMapService.findByUserId(userId).stream().map(RegionMap::getResourceStructureId).distinct().toList();
    }

    @Override
    public String findSubResourceStructureByEquipmentCategory(Integer rootId, Integer equipmentCategory) {
        List<ResourceStructure> resourceStructures = resourceStructureMapper.findSubResourceStructureByEquipmentCategory(rootId, equipmentCategory);
        String result = "";
        for (ResourceStructure resourceStructure : resourceStructures) {
            result = result + resourceStructure.getResourceStructureId() + ',';
        }
        if (result != null && result.length() > 1) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    @Override
    public List<ResourceStructure> findResourceStructureByUserId(Integer userId) {
        //拥有所有区域的权限 获取所有层级资源
        if (regionService.isAllRegion(userId)) {
            return resourceStructureManager.getAll().stream().map(ResourceStructure::new).toList();
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        Set<Integer> resourceStructureIdSet = regionMapList.stream().map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        Set<Integer> resourceStructureParentIdSet = new HashSet<>();
        for (Integer resourceStructureId : resourceStructureIdSet) {
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
            if (Objects.isNull(resourceStructure)) {
                continue;
            }
            //获取所有父级节点ID
            HashSet<Integer> levelPathSet = StringUtils.splitToIntegerCollection(resourceStructure.getLevelOfPath(), "\\.", HashSet::new);
            resourceStructureParentIdSet.addAll(levelPathSet);
        }
        return new ArrayList<>(resourceStructureManager.getResourceStructureByIds(resourceStructureParentIdSet));
    }

    /**
     * 设置拓展状态
     *
     * @param equipmentDTOList 需要设置的设备集合
     */
    public void setEquipmentExtendState(List<EquipmentDTO> equipmentDTOList) {
        Set<Integer> equipmentEventSet = equipmentStateManager.getAllEquipmentAlarmState();
        Map<Integer, Integer> equipmentAlarmState = activeEventManager.getEquipmentAlarmState();
        for (EquipmentDTO equipmentDTO : equipmentDTOList) {
            //是否被屏蔽
            equipmentDTO.setMasked(equipmentStateManager.getEquipmentMaskStatesById(equipmentDTO.getEqId()));
            //是否有告警
            equipmentDTO.setAlarmState(equipmentEventSet.contains(equipmentDTO.getEqId()) ? AlarmState.ALARM : AlarmState.NORMAL);
            //最大设备告警等级
            equipmentDTO.setMaxEventSeverity(equipmentAlarmState.get(equipmentDTO.getEqId()));
        }
    }

    @Override
    public List<ResourceStructure> findByObjectTypeIdAndUserId(List<Integer> objectTypeIds, Integer userId) {
        List<ResourceStructure> structures = this.findByObjectType(objectTypeIds);
        // 用户权限
        Set<Integer> permissionIds = rolePermissionMapService.findRolePermissionsByUserId(userId, PermissionCategoryEnum.REGION.getPermissionCategoryId());

        if (CollUtil.isEmpty(permissionIds)) {
            return new ArrayList<>();
        }
        //拥有所有区域权限直接返回
        if (permissionIds.contains(-1)) {
            return structures;
        }
        // 当前用户权限资源
        Set<Integer> structureIdsByPersonId = regionMapService.findResourceStructureIdsByRegionIds(permissionIds);
        if (CollUtil.isNotEmpty(structureIdsByPersonId)) {
            // 过滤
            structures.removeIf(resourceStructure -> !structureIdsByPersonId.contains(resourceStructure.getResourceStructureId()));
        }

        return structures;
    }


    public List<ResourceStructure> findByObjectType(List<Integer> structureTypeIds) {
        LambdaQueryWrapper<ResourceStructure> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(structureTypeIds), ResourceStructure::getStructureTypeId, structureTypeIds);
        return resourceStructureMapper.selectList(queryWrapper);
    }

    @Override
    public SourceType[] getSourceTypes() {
        return new SourceType[]{SourceType.CENTER, SourceType.PARK, SourceType.BUILDING, SourceType.FLOOR, SourceType.ROOM, SourceType.MDC,
                SourceType.SCCENTER, SourceType.SSCENTER, SourceType.DISTRICT, SourceType.STATION, SourceType.HOUSE};
    }

    @Override
    public ResourceObjectEntity findResourceObjectEntity(Integer resourceStructureId) {
        ResourceStructure resourceStructure = this.findById(resourceStructureId);
        if (ObjectUtil.isNull(resourceStructure)) {
            return null;
        }
        Integer parentType = this.getResourceStructureType(resourceStructure.getParentResourceStructureId());
        return new ResourceObjectEntity(resourceStructure.getResourceStructureId(),
                resourceStructure.getStructureTypeId(), resourceStructure.getResourceStructureName(),
                resourceStructure.getResourceStructureId(), resourceStructure.getParentResourceStructureId(),
                parentType,
                resourceStructure.getLevelOfPath(),resourceStructure.getOriginId());
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObject() {
        return resourceStructureManager.getAll()
                .stream()
                .map(resourceStructure -> {
                    Integer parentType = this.getResourceStructureType(resourceStructure.getParentResourceStructureId());
                    return new ResourceObjectEntity(
                            resourceStructure.getResourceStructureId(),
                            resourceStructure.getStructureTypeId(),
                            resourceStructure.getResourceStructureName(),
                            resourceStructure.getResourceStructureId(),
                            resourceStructure.getParentResourceStructureId(),
                            parentType,
                            resourceStructure.getLevelOfPath(),
                            resourceStructure.getOriginId());
                })
                .toList();
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObjectByUserId(Integer userId) {
        List<ResourceStructure> resourceStructureList = this.findResourceStructureByUserId(userId);
        return resourceStructureList.stream()
                .map(resourceStructure -> {
                    Integer parentType = this.getResourceStructureType(resourceStructure.getParentResourceStructureId());
                    return new ResourceObjectEntity(
                            resourceStructure.getResourceStructureId(),
                            resourceStructure.getStructureTypeId(),
                            resourceStructure.getResourceStructureName(),
                            resourceStructure.getResourceStructureId(),
                            resourceStructure.getParentResourceStructureId(),
                            parentType,
                            resourceStructure.getLevelOfPath(),
                            resourceStructure.getOriginId());
                })
                .toList();
    }


    /**
     * 获取层级类型
     *
     * @param resourceStructureId 层级id
     * @return {@link Integer}
     */
    private Integer getResourceStructureType(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        //无类型时使用center(无真实父节点情况下)
        if (ObjectUtil.isNull(resourceStructure)) {
            return SourceType.CENTER.value();
        }
        return resourceStructure.getStructureTypeId();
    }

    public List<ResourceStructureAlarmState> getResourceStructureAlarmState(List<Integer> resourceStructureIds) {
        return resourceStructureMapper.getResourceStructureAlarmState(resourceStructureIds);
    }

    @Override
    public ResourceStructure findResourceStructureByName(String resourceStructureName) {
        return resourceStructureManager.getResourceStructureByName(resourceStructureName);
    }

    @Override
    public List<ResourceStructureDTO> findResourceStructureByParentId(Integer userId,String parentIds) {
        Map<Integer, List<ResourceStructure>> parentResourceGroup = this.findResourceStructureByUserId(userId)
                                                                        .stream()
                                                                        .collect(Collectors.groupingBy(ResourceStructure::getParentResourceStructureId));
        List<ResourceStructure> list = new ArrayList<>();
        for (String parentId : parentIds.split(",")) {
            List<ResourceStructure> resourceStructures = parentResourceGroup.get(Integer.parseInt(parentId));
            if (CollUtil.isEmpty(resourceStructures)) {
                continue;
            }
            list.addAll(resourceStructures);
        }

        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(ResourceStructureDTO::new).toList();
    }

    /**
     * 根据userId，找到所有权限的层级集合，再根据层级过滤条件进行过滤
     *
     * @param userId                     用户Id
     * @param filterResourceStructureIds 层级过滤条件（必须是有权限的）
     * @return
     */
    @Override
    public List<Integer> findResourceStructureIdsByUserIdAndResourceStructureIds(Integer userId, String filterResourceStructureIds) {
        if (CharSequenceUtil.isNotBlank(filterResourceStructureIds)) {
            return StringUtils.splitToIntegerList(filterResourceStructureIds);
        }
        return this.findThisHierarchyResourceStructureIdsByUserId(userId);
    }


    public List<Integer> findResourceIdsByUserIdAndResourceStructureIds(Integer userId, Collection<Integer> resourceStructureIds) {
        List<Integer> userResourceIds = this.findResourceStructureIdsByUserId(userId);
        Set<Integer> allChildrenId = resourceStructureManager.getAllChildrenId(resourceStructureIds);
        return userResourceIds.stream().filter(allChildrenId::contains).toList();
    }

    @Override
    public List<ResourceStructure> findResourceByUserIdAndResourceStructureIds(Integer userId, Collection<Integer> resourceStructureIds) {
        List<Integer> resourceStructurePermissionIds = findResourceIdsByUserIdAndResourceStructureIds(userId, resourceStructureIds);
        return resourceStructureManager.getResourceStructureByIds(resourceStructurePermissionIds)
                                       .stream()
                                       .map(ResourceStructure::new)
                                       .toList();
    }

    @Override
    public List<ResourceStructure> findResourceByResourceStructureIds(Collection<Integer> resourceStructureIds) {
        return resourceStructureManager.getResourceStructureByIds(resourceStructureIds);
    }

    @Override
    public int updateBatchResourceStructureOrder(List<UpdateResourceStructureOrderDto> updateResourceStructureOrderDtos) {
        if (CollUtil.isEmpty(updateResourceStructureOrderDtos)) {
            return 0;
        }
        resourceStructureMapper.updateBatchOrder(updateResourceStructureOrderDtos);
        //主动刷新内部缓存
        List<Integer> resourceStructureIds = updateResourceStructureOrderDtos.stream()
                                                                             .map(UpdateResourceStructureOrderDto::getResourceStructureId)
                                                                             .toList();
        resourceStructureManager.refreshResourceStructureByIds(resourceStructureIds);
        return updateResourceStructureOrderDtos.size();
    }

    @Override
    public ResourceStructureTreeDTO findResourceStructuresByCustomtype(Integer userId, String typeIds) {
        List<Integer> typeIdList = CharSequenceUtil.split(typeIds, ",").stream().map(Integer::parseInt).toList();
        // 获取当前用户拥有权限集合
        Set<Integer> permissionIds = rolePermissionMapService.findRolePermissionsByUserId(userId, PermissionCategoryEnum.REGION.getPermissionCategoryId());
        // 判断当前用户是否拥有该组的所有权限,有则返回整棵树
        if (permissionIds.contains(-1)) {
            List<ResourceStructureTreeDTO> resourceStructureTreeDTOS = resourceStructureManager.getTreeDtoAll().stream().filter(f-> typeIdList.contains(f.getTId())).map(e -> {
                ResourceStructureTreeDTO resourceStructureTreeDTO = new ResourceStructureTreeDTO(e);
                resourceStructureTreeDTO.setAccessFlag(true);
                return resourceStructureTreeDTO;
            }).toList();
            return resourceStructureManager.resourceStructureTreeDTOBuildTree(resourceStructureTreeDTOS);
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        // 当前用户具有权限的 resourceStructureIds 集合
        Set<Integer> resourceStructureIds = regionMapList.stream().map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return null;
        }
        List<ResourceStructureTreeDTO> listRecord = new ArrayList<>(resourceStructureIds.size());
        Map<Integer, ResourceStructureTreeDTO> param = resourceStructureManager.getTreeDtoAll().stream().collect(Collectors.toMap(ResourceStructureTreeDTO::getId, ResourceStructureTreeDTO::new));
        if (CollUtil.isNotEmpty(param)) {
            // 循环
            Set<ResourceStructureTreeDTO> resourceStructureList = new HashSet<>(resourceStructureIds.size());
            // 父类id
            Set<Integer> parentResourceStructureIds = new HashSet<>(1024);

            // 查找当前层级及父层级id
            for (Integer id : resourceStructureIds) {
                ResourceStructureTreeDTO resourceStructureTreeDTO = param.get(id);
                if (resourceStructureTreeDTO != null) {
                    resourceStructureTreeDTO.setAccessFlag(true);
                    resourceStructureList.add(resourceStructureTreeDTO);
                    // 查询所有父类
                    ResourceStructure resourceStructureById = resourceStructureManager.getResourceStructureById(resourceStructureTreeDTO.getId());
                    // 使用levelOfPath获取父类所有id
                    parentResourceStructureIds.addAll(
                            Arrays.stream(resourceStructureById.getLevelOfPath().split("\\."))
                                    .map(Integer::parseInt).filter(e -> !Objects.equals(id, e)).collect(Collectors.toSet()));
                }
            }
            listRecord.addAll(resourceStructureList);
            listRecord.addAll(parentResourceStructureIds.stream().map(param::get).toList());
        }
        return resourceStructureManager.resourceStructureTreeDTOBuildTree(listRecord.stream().filter(f-> typeIdList.contains(f.getTId()))
                .sorted(Comparator.comparing(ResourceStructureTreeDTO::getSortValue).thenComparing(ResourceStructureTreeDTO::getName))
                .toList());
    }

    @Override
    public double getResourceStructureCapacity(Integer resourceStructureId, CapacityTypeEnum capacityTypeEnum) {
        ResourceStructure resourceStructure = this.findById(resourceStructureId);
        if (Objects.isNull(resourceStructure)) {
            throw new BusinessException("不存在的层级");
        }
        JsonNode extendedField = resourceStructure.getExtendedField();
        if (Objects.isNull(extendedField)) {
            return 0.0;
        }
        for (JsonNode node : extendedField) {
            if (node.has("id") && capacityTypeEnum.getValue().equals(node.get("id").asText())) {
                return node.has("value") && !node.get("value").isNull() ? node.get("value").asDouble() : 0.0;
            }
        }
        return 0.0;
    }

    /**
     * 根据多个设备基础类型ID获取资源结构设备树
     *
     * @param equipmentBaseTypeIds 设备基础类型ID列表
     * @return 资源结构设备树
     */
    @Override
    public ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentTreeByEquipmentBaseTypeIds(List<Integer> equipmentBaseTypeIds){
        // 获取所有层级
        List<ResourceStructureEquipmentTreeDTO> resourceStructures = this.findResourceStructures()
                .stream()
                .map(ResourceStructureEquipmentTreeDTO::new)
                .toList();
        
        List<EquipmentDTO> filteredEquipmentList;
        
        if (equipmentBaseTypeIds == null || equipmentBaseTypeIds.isEmpty()) {
            // 如果未提供类型ID，返回所有设备
            filteredEquipmentList = equipmentService.findEquipmentDTOs();
        } else if (equipmentBaseTypeIds.size() == 1) {
            // 如果只有一个类型ID，使用单类型方法
            filteredEquipmentList = equipmentService.findEquipmentsByBaseTypeId(equipmentBaseTypeIds.get(0));
        } else {
            // 如果有多个类型ID，将列表转换为逗号分隔的字符串
            String baseTypeIdsStr = equipmentBaseTypeIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            filteredEquipmentList = equipmentService.findEquipmentsByBaseTypeIds(baseTypeIdsStr);
        }
        
        return this.buildTree(resourceStructures, filteredEquipmentList);
    }
}
