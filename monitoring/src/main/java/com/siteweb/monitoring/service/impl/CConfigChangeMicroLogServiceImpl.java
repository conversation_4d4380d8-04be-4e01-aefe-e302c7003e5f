package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.CConfigChangeMicroLog;
import com.siteweb.monitoring.mapper.CConfigChangeMicroLogMapper;
import com.siteweb.monitoring.service.CConfigChangeMicroLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class CConfigChangeMicroLogServiceImpl implements CConfigChangeMicroLogService {
    @Autowired
    private CConfigChangeMicroLogMapper cConfigChangeMicroLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfigChangeLog(String objectId, Integer configId, Integer editType) {
        //判断c_configchangemicrolog日志记录是否存在，存在则更新，不存在则删除并插入。
        if (exists(objectId, configId, editType)) {
           this.updateTime(objectId, configId, editType);
           return;
        }
        this.create(objectId, configId, editType);
    }

    private void updateTime(String objectId, Integer configId, Integer editType) {
        cConfigChangeMicroLogMapper.update(null, Wrappers.lambdaUpdate(CConfigChangeMicroLog.class)
                                                         .set(CConfigChangeMicroLog::getUpdateTime, new Date())
                                                         .eq(CConfigChangeMicroLog::getObjectId, objectId)
                                                         .eq(CConfigChangeMicroLog::getConfigId, configId)
                                                         .eq(CConfigChangeMicroLog::getEditType, editType));
    }
    private void create(String objectId, Integer configId, Integer editType){
        CConfigChangeMicroLog configChangeMicroLog = new CConfigChangeMicroLog(objectId, configId, editType, new Date());
        cConfigChangeMicroLogMapper.insert(configChangeMicroLog);
    }
    private boolean exists(String objectId, Integer configId, Integer editType) {
        return cConfigChangeMicroLogMapper.exists(Wrappers.lambdaQuery(CConfigChangeMicroLog.class)
                                                          .eq(CConfigChangeMicroLog::getObjectId, objectId)
                                                          .eq(CConfigChangeMicroLog::getConfigId, configId)
                                                          .eq(CConfigChangeMicroLog::getEditType, editType));
    }
}
