package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.mapper.PortMapper;
import com.siteweb.monitoring.service.PortService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("portService")
public class PortServiceImpl implements PortService {

    @Autowired
    private PortMapper portMapper;

    @Override
    public List<Port> findPorts() {
        return portMapper.selectList(null);
    }

    @Override
    public int createPort(Port port) {
        return portMapper.insert(port);
    }

    @Override
    public int deleteById(Integer portId) {
        return portMapper.deleteById(portId);
    }

    @Override
    public int updatePort(Port port) {
        return portMapper.updateById(port);
    }

    @Override
    public Port findById(Integer portId) {
        return portMapper.selectById(portId);
    }

    @Override
    public Port findByPortIdAndMonitorUnitId(Integer portId, Integer monitUnitId) {
        return portMapper.selectOne(Wrappers.<Port>lambdaQuery()
                .eq(Port::getPortId, portId)
                .eq(Port::getMonitorUnitId, monitUnitId));

    }

    @Override
    public List<Port> findPortByMonitUnitId(Integer monitUnitId) {
        return portMapper.selectList(Wrappers.<Port>lambdaQuery()
                .eq(Port::getMonitorUnitId, monitUnitId));
    }
}
