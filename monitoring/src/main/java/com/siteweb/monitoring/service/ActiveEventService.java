package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.dto.ActiveEventInstructionDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.monitoring.vo.ActiveEventOperationVO;
import com.siteweb.monitoring.vo.ConfigEventCondition;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.*;

public interface ActiveEventService {

    List<ActiveEvent> findActiveEvents();

    ActiveEventDTO getActiveEventDTOBySequenceId(String sequenceId);

    Page<ActiveEventDTO> queryActiveEvents(int userId, Pageable pageable, ActiveEventFilterVO activeEventFilterVO);

    Map<Integer, Integer> groupActiveEventsBySeverity(int userId, ActiveEventFilterVO activeEventFilterVO);

    List<ActiveEventDTO> queryUnConfirmActiveEvents(Integer userId);

    List<ActiveEventDTO> queryUnEndActiveEvents(Integer userId);

    boolean confirmActiveEvents(int userId, ActiveEventOperationVO vo);

    boolean cancelActiveEvents(int userId, ActiveEventOperationVO vo);

    void cancelActiveEventsRemoveProc(int userId, String userName, List<String> sequenceIds, String note);

    boolean addEventNote(int userId, ActiveEventOperationVO vo);

    void addEventNote(List<String> sequenceIds, String note);

    ActiveEvent getActiveEventBySequenceId(String sequenceId);

    /**
     * 通过设备ids获取告警信息(使用二位集合将不同的设备告警隔离开来)
     *
     * @param equipmentIds 设备id(多个用逗号隔开)
     * @param eventEnded
     * @return {@link List}<{@link ActiveEvent}>
     */
    Map<Integer, List<ActiveEventDTO>> findActiveEventsByEquipmentIds(String equipmentIds, Boolean eventEnded);

    /**
     * 获取单个设备当前告警
     *
     * @param equipmentId    设备Id
     * @param eventEnded
     * @param eventConfirmed
     * @return
     */
    List<ActiveEventDTO> findActiveEventsByEquipmentId(Integer equipmentId, Boolean eventEnded, Boolean eventConfirmed);

    List<ActiveEventDTO> findActiveEventsByResourceStructureIdSet(Set<Integer> resourceStructureIdSet, Boolean eventEnded);

    List<ActiveEventDTO> findActiveEventsByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);

    /**
     * 模拟告警测试
     *
     * @param configEventVO 告警配置实体
     * @return
     */
    Long simulateActiveEvent(ConfigEventCondition configEventVO);

    ActiveEventDTO findTopOneActiveEventByStartTimeAndUserId(Date startTime, Integer userId);

    List<ActiveEventDTO> findActiveEventDTOsByStartTimeAndUserId(Date startTime, Date endTime, Integer userId, boolean queryByEndTime);

    List<ActiveEventDTO> findActiveEventDTOsByUserIdAndEndTimeIsNull(Integer userId);

    List<ActiveEventDTO> findActiveEventDTOsByUserIdAndEventCategoryAndEndTimeIsNull(Integer userId, Integer eventCategory);

    /**
     * 根据序列号从DB查询活动告警
     */
    ActiveEvent getDBActiveEventBySequenceId(String sequenceId);

    int removeBySequenceId(String sequenceId);
    int removeBySequenceIds(List<String> sequenceIds);

    int updateEndTimeBySequenceId(Date endTime, String sequenceId);

    int updateEndTimeBySequenceIds(Date endTime, List<String> sequenceIds);

    /**
     * 更新确认确认时间、确认人、确认名称
     */
    int updateConfirmTimeAndConfirmerIdAndConfirmerNameIdBySequenceId(Date confirmTime, Integer confirmerId, String confirmerName, String sequenceId,String note);

    List<ActiveEvent> getDBActiveEventByCondition(Integer stationId, Integer equipmentId, Integer eventId);

    List<ActiveEvent> getActiveEventBySequenceIds(Collection<String> sequenceIds);

    /**
     * 确认告警
     * 对应存储过程：PAM_ConfirmedEvent
     *
     * @param userId
     * @param userName
     * @param subSequenceIds
     */
    void confirmActiveEvents(int userId, String userName, List<String> subSequenceIds, String note, Integer eventReasonType);

    void confirmActiveEventsRemoveProc(int userId, String userName, List<String> sequenceIds, String note, Integer eventReasonType);

    List<ActiveEventDTO> findActiveEventsByStationIds(List<Integer> stationIds, Boolean eventEnded);

    int updateActiveEventByActiveEventInstructionDTO(ActiveEventInstructionDTO activeEventInstructionDTO);

    List<ActiveEventDTO> getActiveEventByEquipmentEventKeys(Integer userId, String equipmentEventKeys);
}

