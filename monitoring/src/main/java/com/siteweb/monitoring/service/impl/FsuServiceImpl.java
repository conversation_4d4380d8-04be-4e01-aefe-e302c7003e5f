package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import org.springframework.data.domain.Page;
import com.siteweb.monitoring.mapper.FsuMapper;
import com.siteweb.monitoring.service.FsuService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.monitoring.vo.FsuFilterVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageImpl;

import java.util.*;

@Service("fsuService")
public class FsuServiceImpl implements FsuService {

    @Autowired
    private FsuMapper fsuMapper;

    @Autowired
    private StationService stationService;


    @Override
    public List<Map<String, String>> findFsuTypeList() {
        return fsuMapper.findFsuTypeList();
    }

    @Override
    public List<Map<String, String>> findCpuUsedList() {
        String[] list = {">50", "<=50"};
        return buildDic("CpuUsed", list);
    }

    @Override
    public List<Map<String, String>> findMemoryUsedList() {
        String[] list = {">50", "<=50"};
        return buildDic("MemUsage", list);
    }

    @Override
    public List<Map<String, String>> findFlashUsedList() {
        String[] list = {">90", "80-90", "70-80", "<70"};
        return buildDic("FlashUsedRate", list);
    }


    @Override
    public Page<IcsFsuDataNewInfoDTO> findAllFsu(Integer userId, Pageable pageable, FsuFilterVo filterVo) {
        List<Integer> stationIdList = stationService.findStationIdsByUserId(userId);
        if (stationIdList.isEmpty()) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        List<IcsFsuDataNewInfoDTO> dtoList = fsuMapper.findAllFsu(stationIdList, filterVo);
        List<IcsFsuDataNewInfoDTO> slice = dtoList.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        return new PageImpl<>(slice, pageable, dtoList.size());
    }

    private List<Map<String, String>> buildDic(String name, String[] dicList) {
        List<Map<String, String>> result = new ArrayList<>(dicList.length);
        for (String dic : dicList) {
            Map<String, String> m = new HashMap<>();
            m.put(name, dic);
            result.add(m);
        }
        return result;
    }
}
