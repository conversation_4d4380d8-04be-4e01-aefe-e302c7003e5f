package com.siteweb.monitoring.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.EquipmentTemplate;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.EquipmentTemplateService;
import com.siteweb.monitoring.service.ResourceObjectService;
import com.siteweb.monitoring.util.ExtendedFieldUtil;
import com.siteweb.monitoring.vo.EquipmentFilterVo;
import com.siteweb.monitoring.vo.EquipmentStatisticsVO;
import com.siteweb.monitoring.vo.ToDynamicApplyEquipmentVO;
import com.siteweb.utility.entity.DiskFileOperationType;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.DiskFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("equipmentService")
@Slf4j
public class EquipmentServiceImpl implements EquipmentService, ResourceObjectService {

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    EquipmentBaseTypeManager equipmentBaseTypeManager;

    @Autowired
    EquipmentMapper equipmentMapper;

    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    RegionMapService regionMapService;

    @Autowired
    RegionService regionService;

    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    DiskFileService diskFileService;
    @Autowired
    EquipmentTemplateService equipmentTemplateService;

    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    ActiveSignalManager activeSignalManager;

    @Autowired
    DataItemService dataItemService;
    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    Boolean byteDanceEnable;
    @Autowired
    ExtendedFieldUtil extendedFieldUtil;


    @Override
    public List<Equipment> findEquipments() {
        return equipmentManager.getAllEquipments();
    }

    @Override
    public List<EquipmentDTO> findEquipmentDTOs() {
        List<EquipmentDTO> result = new ArrayList<>();
        List<Equipment> equipmentList = equipmentManager.getAllEquipments();
        for (Equipment equipment : equipmentList) {
            result.add(this.findByEquipmentDto(equipment));
        }
        return result;
    }

    @Override
    public Equipment findById(Integer equipmentId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            log.error("equipment info is null {}", equipmentId);
            return null;
        }
        Integer onlineState = Optional.ofNullable(equipmentStateManager.getEquipmentOnlineStateById(equipmentId))
                                      .map(OnlineState::value).orElse(OnlineState.UNREGISTER.value());
        equipment.setConnectState(onlineState);
        return equipment;
    }

    @Override
    public Equipment findByIdFromDB(Integer equipmentId) {
        return equipmentMapper.selectById(equipmentId);
    }

    @Override
    public List<EquipmentDTO> findEquipmentDTOs(Integer userId, EquipmentFilterVo filterVo) {
        //权限过滤
        Set<Integer> equipmentIds = this.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        filterVo.setEquipmentIds(equipmentIds);
        filterVo.setRIds(StringUtils.splitToIntegerCollection(filterVo.getRId(),HashSet::new));
        return equipmentManager.getEquipmentsByEquipmentDto(filterVo)
                               .stream()
                               .map(this::findByEquipmentDto)
                               .toList();
    }

    @Override
    public List<Equipment> findEquipmentsByResourceStructureId(Integer resourceStructureId) {
        return equipmentManager.findEquipmentsByResourceStructureId(resourceStructureId);
    }

    @Override
    public List<Equipment> recursiveFindEquipmentsByResourceStructureIds(List<Integer> resourceStructureIds) {
        Set<Integer> allChildrenId = resourceStructureManager.getAllChildrenId(resourceStructureIds);
        return findEquipmentsByResourceStructureIds(allChildrenId);
    }

    @Override
    public List<Equipment> findEquipmentsByResourceStructureIds(Set<Integer> resourceStructureIds) {
        return equipmentManager.findEquipmentsByResourceStructureIds(resourceStructureIds);
    }

    @Override
    public Integer updateBatchEquipmentOrder(List<UpdateEquipmentOrderDto> updateEquipmentOrderDtos) {
        if (CollUtil.isEmpty(updateEquipmentOrderDtos)) {
            return 0;
        }
        equipmentMapper.updateBatchEquipmentOrder(updateEquipmentOrderDtos);
        //主动刷新内部缓存
        List<Integer> equipmentIds = updateEquipmentOrderDtos.stream()
                .map(UpdateEquipmentOrderDto::getEquipmentId)
                .toList();
        equipmentManager.refreshEquipmentByIds(equipmentIds);
        return updateEquipmentOrderDtos.size();
    }

    @Override
    public EquipmentDetail getEquipmentDetail(Integer equipmentId) {
        EquipmentDetail equipmentDetail = null;
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment != null) {

            equipmentDetail = new EquipmentDetail(equipment);
            equipmentDetail.setEquipmentPosition(resourceStructureManager.getFullPath(equipment.getResourceStructureId()));
            EquipmentBaseType equipmentBaseType = equipmentBaseTypeManager.getEquipmentBaseTypeFromCache(equipment.getEquipmentBaseType());
            equipmentDetail.setMasked(equipmentStateManager.getEquipmentMaskStatesById(equipmentId));
            if (equipment.getEquipmentCategory() != null) {
                Map<Integer, String> equipCategoryMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());
                equipmentDetail.setEquipmentCategory(equipCategoryMap.getOrDefault(equipment.getEquipmentCategory(), ""));
            }
            if (equipmentBaseType != null)
                equipmentDetail.setBaseEquipmentName(equipmentBaseType.getBaseEquipmentName());
            // 设置设备在线状态
            equipmentDetail.setOnlineState(equipmentStateManager.getEquipmentOnlineStateById(equipmentDetail.getEquipmentId()));
            // 设备告警状态
            equipmentDetail.setAlarmState(equipmentStateManager.getEquipmentAlarmStateById(equipmentDetail.getEquipmentId()));
            List<SimpleActiveSignal> simpleActiveSignalList = activeSignalManager.getActiveSignalsByEquipmentId(equipmentId);
            equipmentDetail.setSignalCount(simpleActiveSignalList.size());
        }
        return equipmentDetail;
    }

    @Override
    public List<EquipmentDTO> findEquipmentDTOsByUserId(Integer userId) {
        //拥有所有区域的权限 获取所有设备
        if (Boolean.TRUE.equals(regionService.isAllRegion(userId))) {
            return equipmentManager.getAllEquipments()
                                   .stream()
                                   .map(this::findByEquipmentDto)
                                   .toList();
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        Set<EquipmentDTO> equipmentSet = new HashSet<>();
        Set<Integer> resourceStructureIdSet = new HashSet<>();
        for (RegionMap regionMap : regionMapList) {
            //层级下所有设备权限
            if (regionMap.getEquipmentId().equals(-1)) {
                resourceStructureIdSet.add(regionMap.getResourceStructureId());
                continue;
            }
            Equipment equipment = equipmentManager.getEquipmentById(regionMap.getEquipmentId());
            equipmentSet.add(this.findByEquipmentDto(equipment));
        }
        //统一查询层级下的所有设备
        List<EquipmentDTO> equipmentListByResourceStructure = this.findEquipmentsByResourceStructureIds(resourceStructureIdSet)
                                                                  .stream()
                                                                  .map(this::findByEquipmentDto)
                                                                  .toList();
        equipmentSet.addAll(equipmentListByResourceStructure);
        return equipmentSet.stream()
                           .sorted(Comparator.comparing(EquipmentDTO::getDisplayIndex).thenComparing(EquipmentDTO::getEqName))
                           .toList();
    }

    @Override
    public Set<Integer> findEquipmentIdsByUserId(Integer userId) {
        if (ObjectUtil.isNull(userId)) {
            return Collections.emptySet();
        }
        return this.findEquipmentDTOsByUserId(userId)
                   .stream()
                   .map(EquipmentDTO::getEqId)
                   .collect(Collectors.toSet());
    }

    @Override
    public EquipmentBasicDto findEquipmentBasic(Integer equipmentId) {
        EquipmentBasicDto equipmentBasic = equipmentMapper.findEquipmentBasic(equipmentId);
        //获取层级信息
        String levelOfPathName = resourceStructureManager.getLevelOfPathName(equipmentBasic.getLevelOfPath());
        equipmentBasic.setLevelOfPath(levelOfPathName);
        if (Boolean.TRUE.equals(byteDanceEnable)) {
            // 字节开关打开，需要判断扩展字段是否存在额定容量属性
            equipmentBasic.setExtValue(extendedFieldUtil.ensureEquipmentRequiredFields(equipmentBasic.getExtValue()));
        }
        return equipmentBasic;
    }

    @Override
    public List<EquipmentDTO> findEquipmentsByBaseTypeId(Integer baseTypeId) {
        List<EquipmentDTO> result = new ArrayList<>();
        List<Equipment> equipmentList = equipmentManager.getEquipmentsByCategoryId(baseTypeId);

        for (Equipment equipment : equipmentList) {
            result.add(this.findByEquipmentDto(equipment));
        }
        return result;
    }

    @Override
    public List<EquipmentDTO> findEquipmentsByBaseTypeIds(String baseTypeIds) {
        if (CharSequenceUtil.isBlank(baseTypeIds)) {
            return Collections.emptyList();
        }
        List<EquipmentDTO> result = new ArrayList<>();
        Set<Integer> baseTypeIdList = StringUtils.splitToIntegerCollection(baseTypeIds, HashSet::new);
        List<Equipment> equipmentList = equipmentManager.findEquipmentsByBaseTypeIds(baseTypeIdList);
        for (Equipment equipment : equipmentList) {
            result.add(this.findByEquipmentDto(equipment));
        }
        return result;
    }

    @Override
    public List<EquipmentDTO> findEquipmentsByEquipmentTemplateIds(List<Integer> equipmentTemplateIds) {
        if (CollUtil.isEmpty(equipmentTemplateIds)) {
            return Collections.emptyList();
        }
        List<EquipmentDTO> result = new ArrayList<>();
        Set<Integer> templateIdSet = new HashSet<>(equipmentTemplateIds);
        List<Equipment> equipmentList = equipmentManager.getEquipmentsByTemplateId(templateIdSet);
        for (Equipment equipment : equipmentList) {
            result.add(this.findByEquipmentDto(equipment));
        }
        return result;
    }

    @Override
    public Integer getEquipmentStatisticsByReq(EquipmentStatisticsVO equipmentStatisticsVO) {
        List<Integer> deviceCategory = equipmentStatisticsVO.getDeviceCategoryIds();
        Integer count = 0;
        //层级类型与层级id为空查所有
        if (Objects.isNull(equipmentStatisticsVO.getPageCategory()) && Objects.isNull(equipmentStatisticsVO.getObjectId())) {
            count = CollUtil.size(equipmentManager.getAllEquipments());
        } else if (CollUtil.isNotEmpty(deviceCategory)) {
            count = equipmentMapper.getEquipmentStatisticsByResourceStructureAndEquipmentBaseType(equipmentStatisticsVO.getPageCategory(), deviceCategory, equipmentStatisticsVO.getObjectId());
        } else {
            count = equipmentMapper.getEquipmentStatisticsByReq(equipmentStatisticsVO.getPageCategory(), equipmentStatisticsVO.getObjectId());
        }
        return count;
    }

    @Override
    public List<ToDynamicApplyEquipmentVO> getToDynamicApplyEquipmentVOs(Integer stationId, Integer equipmentId) {
        return equipmentMapper.getToDynamicApplyEquipmentVOs(stationId, equipmentId);
    }

    @Override
    public List<SimpleEquipmentDTO> findSimpleEquipmentDTOByStationId(Integer stationId) {
        List<SimpleEquipmentDTO> result = new ArrayList<>();
        List<Equipment> equipmentList = equipmentManager.getAllEquipments().stream().filter(o -> o.getStationId().equals(stationId)).toList();
        for (Equipment equipment : equipmentList) {
            SimpleEquipmentDTO simpleEquipmentDTO = new SimpleEquipmentDTO();
            simpleEquipmentDTO.setStationId(equipment.getStationId());
            simpleEquipmentDTO.setEquipmentId(equipment.getEquipmentId());
            simpleEquipmentDTO.setEquipmentName(equipment.getEquipmentName());
            result.add(simpleEquipmentDTO);
        }
        return result;
    }

    @Override
    public Equipment saveEquipment(Equipment equipment) {
        equipmentMapper.insert(equipment);
        return equipment;
    }

    public void deleteEquipment(Integer id){
        equipmentMapper.deleteById(id);
        equipmentManager.deleteById(id);
    }

    @Override
    public List<StationEquipmentDTO> findStationPageEquipmentDTOByStationId(Integer stationId) {
        List<StationEquipmentDTO>  result = equipmentMapper.findStationPageEquipmentDTOByStationId(stationId);
        List<Integer> keys = new ArrayList<>();
        for(StationEquipmentDTO stationEquipmentDTO:result){
            keys.add(stationEquipmentDTO.getEquipmentId());
        }

        List<EquipmentAlarmStateDTO> equipmentAlarmState = activeEventManager.findEquipmentAlarmState(keys);
        Map<Integer, EquipmentAlarmStateDTO> equipmentAlarmStateDTOMap = equipmentAlarmState.stream().collect(
                Collectors.toMap(EquipmentAlarmStateDTO::getEquipmentId, p -> p));

        for(StationEquipmentDTO stationEquipmentDTO:result){
            EquipmentState equipmentState = equipmentStateManager.getEquipmentStateById(stationEquipmentDTO.getEquipmentId());
            if(equipmentState != null){
                stationEquipmentDTO.setOnlineStatus(equipmentState.getOnlineState());
                stationEquipmentDTO.setOnProject(equipmentState.getOnProject());
                stationEquipmentDTO.setMasked(equipmentState.getMasked());
            }
            EquipmentAlarmStateDTO equipmentAlarmStateDTO = equipmentAlarmStateDTOMap.get(stationEquipmentDTO.getEquipmentId());
            if(equipmentAlarmStateDTO != null){
                stationEquipmentDTO.setMaxEventSeverity(equipmentAlarmStateDTO.getEventLevel());
            }
            //拼接中心


            ResourceStructure resourceStructure1 =  resourceStructureManager.getParentResourceStructure(stationEquipmentDTO.getResourceStructureId(), 102);
            if(resourceStructure1 !=null && CharSequenceUtil.isNotBlank(resourceStructure1.getResourceStructureName())){
                stationEquipmentDTO.setCenterName(resourceStructure1.getResourceStructureName());
            } else {
                ResourceStructure resourceStructure = resourceStructureManager.getRoot();
                stationEquipmentDTO.setCenterName(resourceStructure.getResourceStructureName());

            }
            //拼接机房
            ResourceStructure resourceStructure2 =  resourceStructureManager.getParentResourceStructure(stationEquipmentDTO.getResourceStructureId(), 105);
            if(resourceStructure2 !=null){
                stationEquipmentDTO.setHouseName(resourceStructure2.getResourceStructureName());
            }
        }

        return  result;
    }

    @Override
    public List<StationEquipmentDTO> findStationPageEquipmentDTOByResourceStructureId(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if(resourceStructure ==null){
            return  new ArrayList<>();
        }
        return  findStationPageEquipmentDTOByStationId(resourceStructure.getOriginId());
    }

    @Override
    public List<SimpleEquipmentDTO> findEquipmentsBySignalBaseTypeIdsAndResourceStructureIds(String signalBaseTypeIds,String resourceStructureIds) {
        if (CharSequenceUtil.isBlank(signalBaseTypeIds) || CharSequenceUtil.isBlank(resourceStructureIds)) {
            return Collections.emptyList();
        }
        List<Integer> signalBaseTypeIdList = StringUtils.splitToIntegerList(signalBaseTypeIds);
        List<Integer> resourceStructureIdList = StringUtils.splitToIntegerList(resourceStructureIds);
        return equipmentMapper.findEquipmentsBySignalBaseTypeIds(signalBaseTypeIdList,resourceStructureIdList);
    }
    @Override
    public void updateConnectState(Integer equipmentId, Integer connectState) {
        //TODO 后续设备在线的状态往redis里面取，待DataHub更新可考虑写redis
        equipmentMapper.update(null, Wrappers.lambdaUpdate(Equipment.class)
                                             .set(Equipment::getConnectState, connectState)
                                             .eq(Equipment::getEquipmentId, equipmentId));
        equipmentManager.refreshEquipmentByIds(List.of(equipmentId));
    }

    @Override
    public void updateEquipmentTemplateId(Integer equipmentId, Integer equipmentTemplateId) {
        equipmentMapper.update(null, Wrappers.lambdaUpdate(Equipment.class)
                                             .set(Equipment::getEquipmentTemplateId, equipmentTemplateId)
                                             .eq(Equipment::getEquipmentId, equipmentId));
        equipmentManager.refreshEquipmentByIds(List.of(equipmentId));
    }

    @Override
    public boolean isEquipmentOnlineById(Integer equipmentId) {
        OnlineState onlineState = equipmentStateManager.getEquipmentOnlineStateById(equipmentId);
        return Objects.equals(onlineState, OnlineState.ONLINE);
    }

    @Override
    public List<Equipment> findByMonitorUnitId(Integer monitorUnitId) {
        return equipmentManager.getEquipmentsByMonitorUnitId(monitorUnitId);
    }

    @Override
    public List<EquipmentLegderDTO> findEquipmentWithEquipmentStyle(String styleName) {
        List<EquipmentLegderDTO> equipmentIdWithEquipmentStyleName = equipmentMapper.getEquipmentIdWithEquipmentStyleName(styleName);
        //筛选用户权限
        Set<Integer> equipmentIdsByUserId = findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        return equipmentIdWithEquipmentStyleName.stream().filter(a -> equipmentIdsByUserId.contains(a.getEquipmentId())).toList();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEquipmentBasicInfo(EquipmentBasicDto equipmentBasicDto) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentBasicDto.getEquipmentId());
        if (Objects.isNull(equipment)) {
            return Boolean.FALSE;
        }
        equipment = BeanUtil.copyProperties(equipmentBasicDto, Equipment.class);
        int result = equipmentMapper.updateByEquipmentBasicDto(equipment);
        //主动刷新内部缓存
        equipmentManager.refreshEquipmentByIds(List.of(equipmentBasicDto.getEquipmentId()));
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchApplyEquipmentInfo(BatchApplyEquipmentDTO batchApplyEquipmentDTO) {
        EquipmentBasicDto equipmentBasic = this.findEquipmentBasic(batchApplyEquipmentDTO.getOriginEquipmentId());
        EquipmentBasicDto applyEquipment = new EquipmentBasicDto();
        //反射字段值 给需要应用的字段赋值
        for (String field : batchApplyEquipmentDTO.getFieldList()) {
            Object fieldValue = ReflectUtil.getFieldValue(equipmentBasic, field);
            ReflectUtil.setFieldValue(applyEquipment, field, fieldValue);
        }
        List<Equipment> batchUpdateEquipmentList = new ArrayList<>(batchApplyEquipmentDTO.getDestEquipmentList().size());
        //批量应用值
        for (Integer destEquipmentId : batchApplyEquipmentDTO.getDestEquipmentList()) {
            Equipment destEquipment = this.findById(destEquipmentId);
            if (ObjectUtil.isNull(destEquipment)) {
                continue;
            }
            applyEquipment.setEquipmentId(destEquipmentId);
            batchUpdateEquipmentList.add(BeanUtil.copyProperties(applyEquipment, Equipment.class));
            //需要更新设备数量超过1000立即更新
            if ((CollUtil.size(batchUpdateEquipmentList) >= 1000)) {
                this.batchUpdate(batchUpdateEquipmentList);
                batchUpdateEquipmentList.clear();
            }
        }
        this.batchUpdate(batchUpdateEquipmentList);
        //刷新内部缓存
        equipmentManager.refreshEquipmentByIds(batchApplyEquipmentDTO.getDestEquipmentList());
        return Boolean.TRUE;
    }

    private int batchUpdate(List<Equipment> batchUpdateEquipmentList) {
        if (CollUtil.isEmpty(batchUpdateEquipmentList)) {
            return 0;
        }
        return equipmentMapper.batchUpdate(batchUpdateEquipmentList);
    }

    /**
     * 设备图片处理
     *
     * @param equipmentTemplateId 设备模板id
     * @param newPhoto            新图片路径
     */
    private void equipmentTemplatePhotoHandler(Integer equipmentTemplateId, String newPhoto) {
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(equipmentTemplateId);
        if (ObjectUtil.isNull(equipmentTemplate)) {
            return;
        }
        //更新模板图片
        String oldPhoto = equipmentTemplate.getPhoto();
        equipmentTemplate.setPhoto(newPhoto);
        equipmentTemplateService.updateEquipmentTemplate(equipmentTemplate);
        if (CharSequenceUtil.isBlank(oldPhoto) || ObjectUtil.equals(oldPhoto, newPhoto)) {
            return;
        }
        //删除旧文件
        diskFileService.updateDiskFile(oldPhoto, DiskFileOperationType.CLEAN_OLD_DISKFILE);
    }

    @Override
    public SourceType[] getSourceTypes() {
        return new SourceType[]{SourceType.EQUIPMENT};
    }

    @Override
    public ResourceObjectEntity findResourceObjectEntity(Integer equipmentId) {
        Equipment equipment = this.findById(equipmentId);
        if (ObjectUtil.isEmpty(equipment)) {
            return null;
        }
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipment.getResourceStructureId());
        return new ResourceObjectEntity(equipment.getEquipmentId(), SourceType.EQUIPMENT.value(),
                equipment.getEquipmentName(), equipment.getResourceStructureId(),
                equipment.getResourceStructureId(), resourceStructure.getStructureTypeId(),resourceStructure.getLevelOfPath(),
                resourceStructure.getOriginId());
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObject() {
        return equipmentMapper.getEquipmentResource();
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObjectByUserId(Integer userId) {
        //获取拥有权限的设备
        List<EquipmentDTO> equipmentDTOs = this.findEquipmentDTOsByUserId(userId);
        //构建返回对象
        List<ResourceObjectEntity> resourceObjectEntityList = new ArrayList<>(equipmentDTOs.size());
        for (EquipmentDTO equipmentDTO : equipmentDTOs) {
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipmentDTO.getRId());
            if (ObjectUtil.isNull(resourceStructure)) {
                continue;
            }
            ResourceObjectEntity resourceObjectEntity = new ResourceObjectEntity(equipmentDTO.getEqId(),
                    SourceType.EQUIPMENT.value(), equipmentDTO.getEqName(),
                    equipmentDTO.getRId(),
                    resourceStructure.getResourceStructureId(),
                    resourceStructure.getStructureTypeId(),resourceStructure.getLevelOfPath(),resourceStructure.getOriginId());
            resourceObjectEntityList.add(resourceObjectEntity);
        }
        return resourceObjectEntityList;
    }
    /**
     * 获取设备dto
     * 注意:equipmentManager中的缓存中的设备状态可能刷新不准,所以得去equipmentStateManager中取
     * @param equipment
     * @return {@link EquipmentDTO}
     */
    private EquipmentDTO findByEquipmentDto(Equipment equipment){
        OnlineState equipmentOnlineState = equipmentStateManager.getEquipmentOnlineStateById(equipment.getEquipmentId());
        String fullPath = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipment.getResourceStructureId());
        if (Objects.isNull(resourceStructure)) {
            log.error("设备对应的层级为null:{}", equipment);
        }
        String levelOfPath = resourceStructure.getLevelOfPath();
        return new EquipmentDTO(equipment, equipmentOnlineState, fullPath, levelOfPath);
    }
}
