package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.ConfigChangeMicroLog;
import com.siteweb.monitoring.mapper.ConfigChangeMicroLogMapper;
import com.siteweb.monitoring.service.ConfigChangeMicroLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ConfigChangeMicroLogServiceImpl implements ConfigChangeMicroLogService {
    @Autowired
    private ConfigChangeMicroLogMapper configChangeMicroLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveChangeMicroLog(String objectId, Integer configId, Integer editType) {
        //判断TBL_ConfigChangeMicroLog日志记录是否存在，存在则更新，不存在则删除并插入。
        if (this.exists(objectId, configId, editType)) {
            this.updateTime(objectId, configId, editType);
            return;
        }
        this.deleteByObjectIdAndConfigId(objectId, configId);
        this.create(objectId, configId, editType);
    }

    public boolean exists(String objectId, Integer configId, Integer editType) {
        return configChangeMicroLogMapper.exists(Wrappers.lambdaQuery(ConfigChangeMicroLog.class)
                                                         .eq(ConfigChangeMicroLog::getObjectId, objectId)
                                                         .eq(ConfigChangeMicroLog::getConfigId, configId)
                                                         .eq(ConfigChangeMicroLog::getEditType, editType));
    }

    private void create(String objectId, Integer configId, Integer editType) {
        ConfigChangeMicroLog configChangeMicroLog = new ConfigChangeMicroLog(objectId, configId, editType, new Date());
        configChangeMicroLogMapper.insert(configChangeMicroLog);
    }

    /**
     * 编辑更新时间为当前时间
     *
     * @param objectId 对象id
     * @param configId 配置id
     * @param editType 编辑类型
     */
    private void updateTime(String objectId, Integer configId, Integer editType) {
        configChangeMicroLogMapper.update(null, Wrappers.lambdaUpdate(ConfigChangeMicroLog.class)
                                                        .set(ConfigChangeMicroLog::getUpdateTime, new Date())
                                                        .eq(ConfigChangeMicroLog::getObjectId, objectId)
                                                        .eq(ConfigChangeMicroLog::getConfigId, configId)
                                                        .eq(ConfigChangeMicroLog::getEditType, editType));
    }

    /**
     * 删除表数据根据objectId与ConfigId
     *
     * @param objectId 对象id
     * @param configId 配置id
     */
    private void deleteByObjectIdAndConfigId(String objectId, Integer configId) {
        configChangeMicroLogMapper.delete(Wrappers.lambdaQuery(ConfigChangeMicroLog.class)
                                                  .eq(ConfigChangeMicroLog::getObjectId, objectId)
                                                  .eq(ConfigChangeMicroLog::getConfigId, configId));
    }
}
