package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ActiveEventOperationLogDTO;
import com.siteweb.monitoring.entity.ActiveEventOperationLog;
import com.siteweb.monitoring.vo.BatchActiveEventOperationLogVO;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ActiveEventOperationLogService
 * @createTime 2022-04-09 13:55:27
 */
public interface ActiveEventOperationLogService {

    int createOperationLog(int userId, ActiveEventOperationLog operationLog);

    List<ActiveEventOperationLogDTO> findBySequenceId(String sequenceId);

    int batchCreateOperationLog(int userId, BatchActiveEventOperationLogVO vo);
}
