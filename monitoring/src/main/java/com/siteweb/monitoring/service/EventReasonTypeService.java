package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.EventReasonType;

import java.util.List;
import java.util.Map;

public interface EventReasonTypeService {
    List<EventReasonType> listEventReasonType();

    EventReasonType getEventReasonTypeById(Integer id);

    void saveEventReasonType(EventReasonType eventConfirmType);

    void updateEventReasonType(EventReasonType eventConfirmType);

    void deleteEventReasonType(String ids);

    Map<Integer, String> getEventReasonTypeMap();
}
