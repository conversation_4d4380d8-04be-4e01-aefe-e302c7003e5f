package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Event;
import com.siteweb.monitoring.entity.MonitorUnitEvent;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.EventMapper;
import com.siteweb.monitoring.mapper.MonitorUnitEventMapper;
import com.siteweb.monitoring.service.MonitorUnitEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MonitorUnitEventServiceImpl implements MonitorUnitEventService {
    @Autowired
    MonitorUnitEventMapper monitorUnitEventMapper;
    @Autowired
    EventMapper eventMapper;
    @Autowired
    EquipmentManager equipmentManager;
    @Override
    public void expressionHandler(Integer sourceTemplateId, ConfigEventDTO configEventDTO) {
        if (isReferenceEquipment(configEventDTO.getStartExpression(), configEventDTO.getSuppressExpression())) {
            //更新表达式
            eventMapper.update(null, Wrappers.lambdaUpdate(Event.class)
                                             .set(Event::getStartExpression, configEventDTO.getStartExpression())
                                             .set(Event::getSuppressExpression, configEventDTO.getSuppressExpression())
                                             .eq(Event::getEquipmentTemplateId, sourceTemplateId)
                                             .eq(Event::getEventId, configEventDTO.getEventId()));
        }
        //更新
        if (existsByEquipmentIdAndEventId(configEventDTO.getEquipmentId(),configEventDTO.getEventId())) {
            monitorUnitEventMapper.update(null, Wrappers.lambdaUpdate(MonitorUnitEvent.class)
                                                        .set(MonitorUnitEvent::getStartExpression, configEventDTO.getStartExpression())
                                                        .set(MonitorUnitEvent::getSuppressExpression, configEventDTO.getSuppressExpression())
                                                        .eq(MonitorUnitEvent::getEquipmentId, configEventDTO.getEquipmentId())
                                                        .eq(MonitorUnitEvent::getEventId, configEventDTO.getEventId()));
            return;
        }
        //插入
        Equipment equipment = equipmentManager.getEquipmentById(configEventDTO.getEquipmentId());
        MonitorUnitEvent monitorUnitEvent = new MonitorUnitEvent(equipment.getStationId(), equipment.getMonitorUnitId(), equipment.getEquipmentId(), configEventDTO.getEventId(), configEventDTO.getStartExpression(), configEventDTO.getSuppressExpression());
        monitorUnitEventMapper.insert(monitorUnitEvent);
    }

    private boolean existsByEquipmentIdAndEventId(Integer equipmentId, Integer eventId) {
        return monitorUnitEventMapper.exists(Wrappers.lambdaQuery(MonitorUnitEvent.class)
                                                     .eq(MonitorUnitEvent::getEquipmentId, equipmentId)
                                                     .eq(MonitorUnitEvent::getEventId, eventId));
    }

    /**
     * 是引用了设备
     *
     * @param startExpression    开始表达式
     * @param suppressExpression 抑制表达式
     * @return boolean
     */
    private boolean isReferenceEquipment(String startExpression, String suppressExpression) {
        return (CharSequenceUtil.isBlank(startExpression) || CharSequenceUtil.contains(startExpression, "-1")) && (CharSequenceUtil.isBlank(suppressExpression) || CharSequenceUtil.contains(suppressExpression, "-1"));
    }
}
