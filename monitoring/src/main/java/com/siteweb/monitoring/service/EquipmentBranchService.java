package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.EquipmentBranch;
import com.siteweb.utility.dto.ImportErrorInfoDTO;

import java.util.List;

public interface EquipmentBranchService {
    List<EquipmentBranch> findAll();

    EquipmentBranch findById(Integer id);

    EquipmentBranch findByEquipmentIdAndBranchId(Integer equipmentId, Integer branchId);
    Integer createBranch(EquipmentBranch equipmentBranch);

    List<ImportErrorInfoDTO>  batchCreateBranch(List<EquipmentBranch> importEquipmentBranch);

    Integer deleteById(Integer id);

    Integer updateEquipmentBranch(EquipmentBranch equipmentBranch);

    List<EquipmentBranch> findByEquipmentIdAndBranchIds(Integer equipmentId, List<Integer> branchIds);

    List<EquipmentBranch> findByEquipmentId(Integer equipmentId);
}
