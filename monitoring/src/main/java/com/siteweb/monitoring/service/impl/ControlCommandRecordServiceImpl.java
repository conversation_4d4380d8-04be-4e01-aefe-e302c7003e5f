package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.entity.ControlCommandRecord;
import com.siteweb.monitoring.mapper.ControlCommandRecordMapper;
import com.siteweb.monitoring.service.ControlCommandRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/21 15:38
 */
@Service
public class ControlCommandRecordServiceImpl implements ControlCommandRecordService {

    @Autowired
    ControlCommandRecordMapper controlCommandRecordMapper;

    @Override
    public ControlCommandRecord create(ControlCommandRecord controlCommandRecord) {
        return controlCommandRecordMapper.insert(controlCommandRecord) > 0 ? controlCommandRecord : null;
    }

    @Override
    public IPage<ControlCommandRecord> findReportListByPage(Page<ControlCommandRecord> page, Wrapper<ControlCommandRecord> wrappers) {
        return controlCommandRecordMapper.selectPage(page, wrappers);
    }

    @Override
    public List<ControlCommandRecord> findByWrapper(Wrapper<ControlCommandRecord> wrapper) {
        return controlCommandRecordMapper.selectList(wrapper);
    }
}
