package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.StationConditionFilterDTO;
import com.siteweb.monitoring.entity.Station;

import java.util.*;

public interface StationService{

    List<Station> findStations();

    int createStation(Station station);

    int deleteById(Integer stationId);

    int updateStation(Station station);

    Station findById(Integer stationId);

    /**
     * 根据用户获取局站
     * @param userId 用户id
     * @return 局站列表
     */
    List<Station> findByUserId(Integer userId);

    /**
     * 通过区域权限获取拥有权限的局站id
     * @param userId 用户id
     * @return {@link Set }<{@link Integer }> 拥有权限的局站id
     */
    Set<Integer> findByRegionPermission(Integer userId);

    Map<Integer, Set<Integer>> findStationGroupByRegionPermission(Integer userId);

    /**
     * 根据用户获取局站
     * @param userId 用户id
     * @return 局站列表
     */
    List<Integer> findStationIdsByUserId(Integer userId);

    List<Station> findByIds(Collection<Integer> stationIds);

    List<Station> findByCondition(Integer userId, StationConditionFilterDTO stationConditionFilterDTO);
    Integer findStationBaseTypeByStationIdAndStandardVer(Integer stationId,Integer standardVer);

    /**
     * 获取局站工程状态
     *
     * @param stationId 站id
     * @param startTime
     * @return int 0 工程状态  1正常
     */
    int findMaintainState(Integer stationId, Date startTime);
}

