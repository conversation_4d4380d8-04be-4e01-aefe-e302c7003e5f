package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ActiveSignal;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.FocusSignalDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.model.RealTimeSignalKey;
import com.siteweb.monitoring.service.FocusSignalService;
import com.siteweb.monitoring.util.FocusSignalUtil;
import com.siteweb.monitoring.vo.FocusSignalFilterVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.support.PropertyComparator;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> zhou
 * @description FocusSignalServiceImpl
 * @createTime 2022-08-05 16:19:31
 */
@Service
public class FocusSignalServiceImpl implements FocusSignalService {

    private static final Integer MAX_QUERY_COUNTS = 10000;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    ConfigSignalManager configSignalManager;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    RegionService regionService;

    @Autowired
    RegionMapService regionMapService;

    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    SignalSubscribeManager signalSubscribeManager;

    @Override
    public Page<FocusSignalDTO> queryPageableFocusSignals(int userId, Pageable pageable, FocusSignalFilterVO focusSignalFilterVO) {
        List<FocusSignalDTO> dtoList = new ArrayList<>();
        List<FocusSignalDTO> focusSignalDTOs = findFocusSignalDTOs(userId, focusSignalFilterVO);
//        Map<Integer, List<FocusSignalDTO>> focusSignalDTOMap = findFocusSignalDTOMap(userId, focusSignalFilterVO);
        if (focusSignalDTOs.isEmpty()) {
            return new PageImpl<>(dtoList, pageable, 0);
        }
//        if (pageable.getSort().iterator().hasNext()) {
//            focusSignalDTOs = new SortUtil<FocusSignalDTO>().sort(pageable.getSort(), focusSignalDTOs);
//        }
//        int configFocusSignalCount = 0;
//        for (Map.Entry<Integer, List<FocusSignalDTO>> entry : focusSignalDTOMap.entrySet()) {
//            configFocusSignalCount += entry.getValue().size();
//        }
        if (focusSignalDTOs.size() > MAX_QUERY_COUNTS) {
            throw new BusinessException("reach max query counts");
        }
        List<String> compareValue1List = FocusSignalUtil.getCompareValueList(focusSignalFilterVO.getCompareValue1Str());
        List<String> compareValue2List = FocusSignalUtil.getCompareValueList(focusSignalFilterVO.getCompareValue2Str());
        List<FocusSignalDTO> resultList = new ArrayList<>();

        /* 获取redisKey */
        List<RealTimeSignalKey> redisKey = new ArrayList<>();
        for (FocusSignalDTO focusSignalDTO : focusSignalDTOs) {
            redisKey.add(new RealTimeSignalKey(focusSignalDTO.getEquipmentId(),focusSignalDTO.getSignalId()));
        }

        List<ActiveSignal> realTimeSignalItems =  activeSignalManager.getActiveSignalsByKeys(redisKey);

        Map<String, ActiveSignal> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(ActiveSignal::getRedisKey, p -> p));

        for (FocusSignalDTO focusSignalDTO : focusSignalDTOs) {
//            FocusSignalDTO tmpFocusSignalDTO = constructFocusSignalRealTimeData(focusSignalDTO, compareValue1List, compareValue2List);
            ActiveSignal activeSignal =  realTimeSignalItemMap.get(focusSignalDTO.getKey());
            if(ObjectUtil.isNotNull(activeSignal)) {
                FocusSignalDTO tmpFocusSignalDTO = constructFocusSignalRealTimeData(focusSignalDTO, activeSignal, compareValue1List, compareValue2List);
                if (null != tmpFocusSignalDTO) {
                    resultList.add(tmpFocusSignalDTO);
                }
            }
        }
        this.sort(resultList, focusSignalFilterVO.getField(), focusSignalFilterVO.getOrder());
        List<FocusSignalDTO> slice = resultList.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        for (FocusSignalDTO focusSignalDTO : slice) {
            dtoList.add(focusSignalDTO);
        }
        subscribeSignal(slice);
        return new PageImpl<>(dtoList, pageable, resultList.size());
    }
    /**
     * 订阅设备信号
     * @param records 订阅信息  key 设备id  value 对应设备的信号ids
     */
    private void subscribeSignal(List<FocusSignalDTO> records) {
        Map<Integer, List<Integer>> equipmentSignalsMap = records.stream()
                                                                 .collect(Collectors.groupingBy(FocusSignalDTO::getEquipmentId, Collectors.mapping(FocusSignalDTO::getSignalId, Collectors.toList())));
        signalSubscribeManager.sendSignalSubscribe(equipmentSignalsMap);
    }

    private void sort(List<FocusSignalDTO> resultList, String field, String order) {
        if (CollUtil.isEmpty(resultList) || CharSequenceUtil.isBlank(field)) {
            return;
        }
        resultList.sort(new PropertyComparator<>(field,false, GlobalConstants.ASC.equalsIgnoreCase(order)));
    }

    @Override
    public List<FocusSignalDTO> queryFocusSignals(int userId, FocusSignalFilterVO focusSignalFilterVO) {
        List<FocusSignalDTO> focusSignalDTOs = findFocusSignalDTOs(userId, focusSignalFilterVO);
        if (focusSignalDTOs.size() > MAX_QUERY_COUNTS) {
            throw new BusinessException("reach max query counts");
        }
        List<String> compareValue1List = FocusSignalUtil.getCompareValueList(focusSignalFilterVO.getCompareValue1Str());
        List<String> compareValue2List = FocusSignalUtil.getCompareValueList(focusSignalFilterVO.getCompareValue2Str());
        List<FocusSignalDTO> resultList = new ArrayList<>();
        /* 获取redisKey */
        List<RealTimeSignalKey> redisKey = new ArrayList<>();
        for (FocusSignalDTO focusSignalDTO : focusSignalDTOs) {
            redisKey.add(new RealTimeSignalKey(focusSignalDTO.getEquipmentId(),focusSignalDTO.getSignalId()));
        }
        List<ActiveSignal> realTimeSignalItems =  activeSignalManager.getActiveSignalsByKeys(redisKey);
        Map<String, ActiveSignal> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(ActiveSignal::getRedisKey, p -> p));
        for (FocusSignalDTO focusSignalDTO : focusSignalDTOs) {
            ActiveSignal activeSignal =  realTimeSignalItemMap.get(focusSignalDTO.getKey());
            if(ObjectUtil.isNotNull(activeSignal)) {
                FocusSignalDTO tmpFocusSignalDTO = constructFocusSignalRealTimeData(focusSignalDTO, activeSignal, compareValue1List, compareValue2List);
                if (null != tmpFocusSignalDTO) {
                    resultList.add(tmpFocusSignalDTO);
                }
            }
        }
        return resultList;
    }

    @Override
    public ExcelWriter findFocusSignalExcelWriter(Integer userId, FocusSignalFilterVO focusSignalFilterVO) {
        List<FocusSignalDTO> focusSignalDTOs = this.queryFocusSignals(userId, focusSignalFilterVO);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("equipmentPosition", messageSourceUtil.getMessage("common.report.csv.position"));
        writer.addHeaderAlias("equipmentName", messageSourceUtil.getMessage("common.report.csv.equipmentName"));
        writer.addHeaderAlias("signalName", messageSourceUtil.getMessage("common.report.csv.signalName"));
        writer.addHeaderAlias("currentValue", messageSourceUtil.getMessage("common.report.csv.signalValue"));
        writer.addHeaderAlias("originalValue", messageSourceUtil.getMessage("common.report.csv.originValue"));
        writer.addHeaderAlias("unit", messageSourceUtil.getMessage("common.report.csv.unit"));
        writer.addHeaderAlias("sampleTime", messageSourceUtil.getMessage("common.report.csv.collectTime"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(focusSignalDTOs,true);
        writer.autoSizeColumnAll();
        return writer;
    }

    private List<FocusSignalDTO> findFocusSignalDTOs(int userId, FocusSignalFilterVO focusSignalFilterVO) {
        List<FocusSignalDTO> focusSignalDTOList = new ArrayList<>();
        List<Equipment> equipments = findEquipmentsByFilterVO(userId, focusSignalFilterVO);
        List<Integer> signalCategoryList = getFilterSignalCategoryList(focusSignalFilterVO.getSignalCategories());
        List<Long> baseTypeIdList = getFilterBaseTypeIdList(focusSignalFilterVO.getBaseTypeIds());
        for (Equipment equipment : equipments) {
            List<FocusSignalDTO> tmpList = constructEquipmentFocusSignalDTO(focusSignalFilterVO, signalCategoryList, baseTypeIdList, equipment);
            focusSignalDTOList.addAll(tmpList);
        }
        return focusSignalDTOList;
    }
    private FocusSignalDTO constructFocusSignalRealTimeData(FocusSignalDTO focusSignalDTO, ActiveSignal realTimeSignalItem, List<String> compareValue1List, List<String> compareValue2List) {
        if (null == realTimeSignalItem) {
            return null;
        }
        if (!compareValue1List.isEmpty() && !FocusSignalUtil.matchRealTimeSignalItemByCompareValue(realTimeSignalItem, compareValue1List)) {
            return null;
        }
        if (!compareValue2List.isEmpty() && !FocusSignalUtil.matchRealTimeSignalItemByCompareValue(realTimeSignalItem, compareValue2List)) {
            return null;
        }
        focusSignalDTO.setCurrentValue(realTimeSignalItem.getCurrentValue());
        focusSignalDTO.setOriginalValue(realTimeSignalItem.getOriginalValue());
        focusSignalDTO.setSampleTime(realTimeSignalItem.getSampleTime());
        return focusSignalDTO;
    }

    private List<FocusSignalDTO> constructEquipmentFocusSignalDTO(FocusSignalFilterVO focusSignalFilterVO, List<Integer> signalCategoryList, List<Long> baseTypeIdList, Equipment equipment) {
        List<FocusSignalDTO> list = new ArrayList<>();
        List<ConfigSignalItem> configSignalItems = getConfigSignalItemsByEquipment(equipment.getEquipmentTemplateId(), signalCategoryList, baseTypeIdList, focusSignalFilterVO.getKeywords());
        String equipmentPosition = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
        for (ConfigSignalItem configSignalItem : configSignalItems) {
            FocusSignalDTO focusSignalDTO = constructFocusSignalDTO(equipment, equipmentPosition, configSignalItem);
            list.add(focusSignalDTO);
        }
        return list;
    }

    private FocusSignalDTO constructFocusSignalDTO(Equipment equipment, String equipmentPosition, ConfigSignalItem configSignalItem) {
        FocusSignalDTO focusSignalDTO = new FocusSignalDTO();
        focusSignalDTO.setEquipmentId(equipment.getEquipmentId());
        focusSignalDTO.setEquipmentName(equipment.getEquipmentName());
        focusSignalDTO.setResourceStructureId(equipment.getResourceStructureId());
        focusSignalDTO.setEquipmentPosition(equipmentPosition);
        focusSignalDTO.setBaseEquipmentId(equipment.getEquipmentBaseType());
        focusSignalDTO.setSignalId(configSignalItem.getSignalId());
        focusSignalDTO.setSignalName(configSignalItem.getSignalName());
        focusSignalDTO.setUnit(configSignalItem.getUnit());
        focusSignalDTO.setBaseTypeId(configSignalItem.getBaseTypeId());
        return focusSignalDTO;
    }

    private List<ConfigSignalItem> getConfigSignalItemsByEquipment(Integer equipmentTemplateId, List<Integer> signalCategoryList, List<Long> baseTypeIdList, String keywords) {
        List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalByEquipmentTemplateIdFromCache(equipmentTemplateId);
        if (!signalCategoryList.isEmpty()) {
            configSignalItems = configSignalItems.stream().filter(o -> signalCategoryList.contains(o.getSignalCategory())).toList();
        }
        if (!baseTypeIdList.isEmpty()) {
            configSignalItems = configSignalItems.stream().filter(o -> baseTypeIdList.contains(o.getBaseTypeId())).toList();
        }
        if (keywords != null) {
            configSignalItems = configSignalItems.stream().filter(o -> o.getSignalName().indexOf(keywords) >= 0).toList();
        }
        return configSignalItems;
    }

    private List<Integer> getFilterSignalCategoryList(String signalCategories) {
        if (signalCategories != null && !signalCategories.trim().isEmpty()) {
            return Arrays.stream(signalCategories.split(",")).map(Integer::parseInt).toList();
        }
        return new ArrayList<>();
    }

    private List<Long> getFilterBaseTypeIdList(String baseTypeIds) {
        if (baseTypeIds != null && !baseTypeIds.trim().isEmpty()) {
            return Arrays.stream(baseTypeIds.split(",")).map(Long::parseLong).toList();
        }
        return new ArrayList<>();
    }

    public List<Equipment> findEquipmentsByFilterVO(int userId, FocusSignalFilterVO focusSignalFilterVO) {
        List<Equipment> equipments = equipmentManager.getAllEquipments();
        if (focusSignalFilterVO.getResourceStructureIds() != null && !focusSignalFilterVO.getResourceStructureIds().trim().isEmpty()) {
            List<Integer> resourceStructureIdList = Arrays.stream(focusSignalFilterVO.getResourceStructureIds().split(",")).map(Integer::parseInt).toList();
            equipments = equipments.stream().filter(o -> resourceStructureIdList.contains(o.getResourceStructureId())).toList();
        }
        if (focusSignalFilterVO.getBaseEquipmentIds() != null && !focusSignalFilterVO.getBaseEquipmentIds().trim().isEmpty()) {
            List<Integer> baseEquipmentIdList = Arrays.stream(focusSignalFilterVO.getBaseEquipmentIds().split(",")).map(Integer::parseInt).toList();
            equipments = equipments.stream().filter(o -> o.getEquipmentBaseType() != null && baseEquipmentIdList.contains(o.getEquipmentBaseType())).toList();
        }
        if (focusSignalFilterVO.getEquipmentCategoryIds() != null && !focusSignalFilterVO.getEquipmentCategoryIds().trim().isEmpty()) {
            //-1前端传入代表全部
            List<Integer> equipmentCategoryList = Arrays.stream(focusSignalFilterVO.getEquipmentCategoryIds().split(",")).map(Integer::parseInt).filter(a -> !a.equals(-1)).toList();
            if (CollUtil.isNotEmpty(equipmentCategoryList)){
                equipments = equipments.stream().filter(o -> o.getEquipmentCategory() != null && equipmentCategoryList.contains(o.getEquipmentCategory())).toList();
            }
        }
        if (focusSignalFilterVO.getEquipmentIds() != null && !focusSignalFilterVO.getEquipmentIds().trim().isEmpty()) {
            List<Integer> equipmentIdList = Arrays.stream(focusSignalFilterVO.getEquipmentIds().split(",")).map(Integer::parseInt).toList();
            equipments = equipments.stream().filter(o -> equipmentIdList.contains(o.getEquipmentId())).toList();
        }
        List<Region> regions = regionService.findAllRegionsByUserId(userId);
        //如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (regions.stream().noneMatch(o -> o.getRegionId().equals(-1))) {
            List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
            List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);
            //EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
            List<Integer> resourceStructureIds = regionMaps.stream().filter(o -> o.getEquipmentId().equals(-1)).map(RegionMap::getResourceStructureId).toList();
            List<Integer> equipmentIds = regionMaps.stream().filter(o -> o.getEquipmentId() > 0).map(RegionMap::getEquipmentId).toList();
            equipments = equipments.stream().filter(o -> resourceStructureIds.contains(o.getResourceStructureId()) || equipmentIds.contains(o.getEquipmentId())).toList();
        }
        return equipments;
    }
}
