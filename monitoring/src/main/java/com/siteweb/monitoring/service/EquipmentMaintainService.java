package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.EquipmentMaintainDTO;
import com.siteweb.monitoring.dto.EquipmentProjectDetail;
import com.siteweb.monitoring.dto.ProjectOperationResult;
import com.siteweb.monitoring.entity.EquipmentMaintain;
import com.siteweb.monitoring.vo.BatchEquipmentProjectVO;
import com.siteweb.monitoring.vo.EquipmentProjectVO;

import java.util.Date;
import java.util.List;

public interface EquipmentMaintainService {
    /**
     * 获取局站工程状态
     *
     * @param equipmentId 设备id
     * @param startTime
     * @return int 0 工程状态  1正常
     */
    int findMaintainState(Integer stationId, Integer equipmentId, Date startTime);

    EquipmentMaintain findByStationIdAndEquipmentId(Integer stationId, Integer equipmentId);

    ProjectOperationResult setEquipmentProject(EquipmentProjectVO equipmentProjectVO, Integer userId);
    List<ProjectOperationResult> setBatchEquipmentProjectStatus(BatchEquipmentProjectVO batchEquipmentProjectVO, Integer userId);
    ProjectOperationResult deleteEquipmentProject(Integer equipmentId, Integer userId);
    EquipmentMaintainDTO getEquipmentProjectById(Integer equipmentId);
    void batchDeleteEquipmentProject(List<Integer> equipmentIds, Integer userId);
    List<EquipmentProjectDetail> queryEquipmentProjectDetails(int userId);

    /**
     * 通过层级id获取工程状态
     *
     * @param resourceStructure 层级id
     * @return {@link List }<{@link EquipmentMaintainDTO }>
     */
    List<EquipmentMaintainDTO> findMaintainStateByResourceStructure(Integer resourceStructureId);
}
