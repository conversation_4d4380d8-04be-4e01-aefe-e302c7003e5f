package com.siteweb.monitoring.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.WorkStationNetworkTopologyDTO;
import com.siteweb.monitoring.entity.WorkStation;
import com.siteweb.monitoring.enumeration.WorkStationTypeEnum;
import com.siteweb.monitoring.mapper.WorkStationMapper;
import com.siteweb.monitoring.service.WorkStationService;
import com.siteweb.monitoring.vo.WorkStationNetworkTopologyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service("workStationService")
public class WorkStationServiceImpl implements WorkStationService {

    @Autowired
    WorkStationMapper workStationMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public List<WorkStation> findWorkStations() {
        return workStationMapper.selectList(null);
    }

    @Override
    public Integer findWorkStationIdByType(int workStationType) {
        return workStationMapper.findWorkStationIdByType(workStationType);
    }

    @Override
    public int createWorkStation(WorkStation workStation) {
        return workStationMapper.insert(workStation);
    }

    @Override
    public int deleteById(Integer workStationId) {
        return workStationMapper.deleteById(workStationId);
    }

    @Override
    public int updateWorkStation(WorkStation workStation) {
        return workStationMapper.updateById(workStation);
    }

    @Override
    public void updateWorkStationConnectStateById(int connectState, int workStationId) {
        workStationMapper.updateWorkStationConnectStateById(connectState, workStationId);
    }

    @Override
    public WorkStation findById(Integer workStationId) {
        return workStationMapper.selectById(workStationId);
    }

    @Override
    public List<WorkStationNetworkTopologyVO> networkTopology() {
        List<WorkStationNetworkTopologyDTO> workStationNetworkTopologyDTOS = workStationMapper.networkTopology();
        //转换成与s2H5相同的数据格式 与之前as保持一致
        List<WorkStationNetworkTopologyVO> workStationNetworkTopologyVOS = new ArrayList<>();
        for (WorkStationNetworkTopologyDTO workStationNetworkTopologyDTO : workStationNetworkTopologyDTOS) {
            workStationNetworkTopologyVOS.add(convertVO(workStationNetworkTopologyDTO));
        }
        return workStationNetworkTopologyVOS;
    }

    public WorkStationNetworkTopologyVO convertVO(WorkStationNetworkTopologyDTO workStationNetworkTopologyDTO){
        WorkStationNetworkTopologyVO workStationNetworkTopologyVO = new WorkStationNetworkTopologyVO();
        workStationNetworkTopologyVO.setCenterName(workStationNetworkTopologyDTO.getCenterName());
        workStationNetworkTopologyVO.setName(workStationNetworkTopologyDTO.getWorkStationName());
        workStationNetworkTopologyVO.setWorkstationType(workStationNetworkTopologyDTO.getWorkStationType());
        workStationNetworkTopologyVO.setWorkstationTypeName(WorkStationTypeEnum.getName(workStationNetworkTopologyDTO.getWorkStationType()));
        workStationNetworkTopologyVO.setState(workStationNetworkTopologyDTO.getConnectState());
        workStationNetworkTopologyVO.setStatusText(messageSourceUtil.getMessage("api.stationStatus."+workStationNetworkTopologyDTO.getConnectState()));
        //只有这几个应用会采集CPU、内存等信息
        if (Objects.equals(workStationNetworkTopologyDTO.getWorkStationType(),WorkStationTypeEnum.APPLICATION_SERVER.getValue())||Objects.equals(workStationNetworkTopologyDTO.getWorkStationType(),WorkStationTypeEnum.DATA_SERVER.getValue())||Objects.equals(workStationNetworkTopologyDTO.getWorkStationType(),WorkStationTypeEnum.BUSINESS_SERVER.getValue())) {
            workStationNetworkTopologyVO.addWorkStationItems(String.format(messageSourceUtil.getMessage("workStation.netWorkTopology.cpu"),NumberUtil.roundStr(workStationNetworkTopologyDTO.getCpu(), 2)));
            workStationNetworkTopologyVO.addWorkStationItems(String.format(messageSourceUtil.getMessage("workStation.netWorkTopology.memory"),NumberUtil.roundStr(workStationNetworkTopologyDTO.getCpu(), 2)));
            workStationNetworkTopologyVO.addWorkStationItems(String.format(messageSourceUtil.getMessage("workStation.netWorkTopology.threads"),workStationNetworkTopologyDTO.getThreadCount()));
            workStationNetworkTopologyVO.addWorkStationItems(String.format(messageSourceUtil.getMessage("workStation.netWorkTopology.diskFree"),NumberUtil.roundStr(workStationNetworkTopologyDTO.getCpu(), 2)));
        }
        return workStationNetworkTopologyVO;
    }
}
