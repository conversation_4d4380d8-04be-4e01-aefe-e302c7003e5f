package com.siteweb.monitoring.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ActiveSignalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("ActiveSignalService")
public class ActiveSignalServiceImpl implements ActiveSignalService {
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    ActiveSignalManager activeSignalManager;
    /**
     * 获取基站的重要信号信息
     * @param resourceStructureId 基站Id
     * @return 重要信号列表
     */
    @Override
    public List<EquipmentActiveSignal> getStationImportantActiveSignalByResourceStructureId(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if(ObjectUtil.isNull(resourceStructure))
            return new ArrayList<>();
        return  activeSignalManager.getStationImportantActiveSignalByProperty(resourceStructure.getOriginId(), 25);
    }

    @Override
    public List<EquipmentActiveSignal> getStationImportantActiveSignalByStationId(Integer stationId) {
        return  activeSignalManager.getStationImportantActiveSignalByProperty(stationId,25);
    }
}
