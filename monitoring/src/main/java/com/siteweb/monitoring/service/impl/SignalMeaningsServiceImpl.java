package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.EquipmentSignalMeaningsDTO;
import com.siteweb.monitoring.entity.SignalMeanings;
import com.siteweb.monitoring.mapper.SignalMeaningsMapper;
import com.siteweb.monitoring.service.SignalMeaningsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service("signalMeaningsService")
public class SignalMeaningsServiceImpl implements SignalMeaningsService {

    @Autowired
    SignalMeaningsMapper signalMeaningsMapper;

    @Override
    public int insertSignalMeanings(SignalMeanings signalMeanings) {
        return signalMeaningsMapper.insertSignalMeanings(signalMeanings);
    }

    @Override
    public int updateSignalMeanings(SignalMeanings signalMeanings) {
        return signalMeaningsMapper.updateSignalMeanings(signalMeanings);
    }

    @Override
    public int deleteSignalMeaningsById(int equipmentTemplateId, int signalId, int stateValue) {
        return signalMeaningsMapper.deleteSignalMeaningsById(equipmentTemplateId, signalId, stateValue);
    }

    @Override
    public List<EquipmentSignalMeaningsDTO> findSignalsByEquipmentAndSignalIds(Integer equipmentId, List<Integer> signalIds) {
        if (CollUtil.isEmpty(signalIds)) {
            return new ArrayList<>();
        }
        return signalMeaningsMapper.findSignalsByEquipmentAndSignalIds(equipmentId, signalIds);
    }

    @Override
    public List<SignalMeanings> findSignalsByEquipmentTemplateId(Integer equipmentTemplateId) {
        return signalMeaningsMapper.selectList(Wrappers.lambdaQuery(SignalMeanings.class)
                                                       .eq(SignalMeanings::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void batchInsert(List<SignalMeanings> signalMeanings) {
        if (CollUtil.isEmpty(signalMeanings)) {
            return;
        }
        signalMeaningsMapper.batchInsert(signalMeanings);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        signalMeaningsMapper.delete(Wrappers.<SignalMeanings>lambdaQuery()
                                            .eq(SignalMeanings::getEquipmentTemplateId, equipmentTemplateId));
    }

}
