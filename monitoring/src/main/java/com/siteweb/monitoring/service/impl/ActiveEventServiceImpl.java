package com.siteweb.monitoring.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.properties.FeatureEnableProperties;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.dto.ActiveEventInstructionDTO;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.StandardAlarmNameDTO;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.kafka.KafkaUtil;
import com.siteweb.monitoring.kafka.dto.EventOperationMessage;
import com.siteweb.monitoring.kafka.enums.KafkaTopicEnum;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.*;
import com.siteweb.monitoring.model.ConfigEventItem;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.util.EventOperationHandler;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.monitoring.vo.ActiveEventOperationVO;
import com.siteweb.monitoring.vo.ConfigEventCondition;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.EventBaseDic;
import com.siteweb.utility.service.CoreEventSeverityService;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.EventBaseDicService;
import com.siteweb.utility.service.StandardVerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service("activeEventService")
@Slf4j
public class ActiveEventServiceImpl implements ActiveEventService {

    private static final int BATCH_OPERATION_ACTIVE_COUNT = 50;
    /**
     * 备注分隔符
     */
    private static final String NOTE_SPLIT_SYMBOL = ";";
    /**
     * 备注的最大长度
     */
    private static final int NOTE_MAX_LENGTH = 255;
    /**
     * 设备种类的字典id
     */
    private static final int EQUIPMENT_CATEGORY_ENTRY_ID = 7;

    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    AlarmChangeMapper alarmChangeMapper;
    @Autowired
    ActiveEventMapper activeEventMapper;
    @Autowired
    EventMaskHistoryService eventMaskHistoryService;
    @Autowired
    TSLActiveEventService tslActiveEventService;
    @Autowired
    SARAlarmQueueService sarAlarmQueueService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    EventMaskService eventMaskService;
    @Autowired
    StationMaskService stationMaskService;
    @Autowired
    EquipmentMaskService equipmentMaskService;
    @Autowired
    EquipmentMaintainService equipmentMaintainService;
    @Autowired
    StationService stationService;
    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    EventConditionService eventConditionService;
    @Autowired
    EventBaseDicService eventBaseDicService;
    @Autowired
    StandardVerService standardVerService;
    @Autowired
    StandardDicEventMapper standardDicEventMapper;
    @Autowired
    EventService eventService;
    @Autowired
    CoreEventSeverityService coreEventSeverityService;
    @Autowired
    HistoryEventService historyEventService;
    @Autowired
    AlarmChangeService alarmChangeService;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    AccountMapper accountMapper;
    @Autowired
    SARIsProcessMapper sarIsProcessMapper;
    @Autowired
    SARAlarmActiveRecordMapper sarAlarmActiveRecordMapper;
    @Autowired
    PlatformTransactionManager transactionManager;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    FeatureEnableProperties featureEnableProperties;
    @Autowired
    private MonitorUnitMapper monitorUnitMapper;
    /**
     * 如果没有启动kafka则该依赖为null
     */
    @Autowired(required = false)
    private KafkaUtil kafkaUtil;

    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    Boolean byteDanceEnable;

    @Override
    public List<ActiveEvent> findActiveEvents() {
        return activeEventManager.queryAllActiveEvents();
    }

    @Override
    public ActiveEventDTO getActiveEventDTOBySequenceId(String sequenceId) {
        return activeEventManager.getActiveEventDTOBySequenceId(sequenceId);
    }

    @Override
    public Page<ActiveEventDTO> queryActiveEvents(int userId, Pageable pageable, ActiveEventFilterVO activeEventFilterVO) {
        return activeEventManager.queryActiveEvents(userId, pageable, activeEventFilterVO);
    }

    @Override
    public Map<Integer, Integer> groupActiveEventsBySeverity(int userId, ActiveEventFilterVO activeEventFilterVO) {
        return activeEventManager.groupActiveEventsBySeverity(userId, activeEventFilterVO);
    }

    @Override
    public List<ActiveEventDTO> queryUnConfirmActiveEvents(Integer userId) {
        return activeEventManager.queryUnConfirmActiveEvents(userId);
    }

    @Override
    public List<ActiveEventDTO> queryUnEndActiveEvents(Integer userId) {
        return activeEventManager.queryUnEndActiveEvents(userId);
    }

    @Override
    public boolean confirmActiveEvents(int userId, ActiveEventOperationVO vo) {
        return processEventOperation(userId, vo, (sequenceIds, userName,note, postalCode) -> {
            confirmActiveEvents(userId, userName, sequenceIds, note, vo.getEventReasonType());
            sendEventOperationMsg(createEventOperationMessage(userId, userName, sequenceIds, note, postalCode, vo.getEventReasonType()), KafkaTopicEnum.EVENT_CONFIRM);
        });
    }

    @Override
    public boolean cancelActiveEvents(int userId, ActiveEventOperationVO vo) {
        return processEventOperation(userId, vo, (sequenceIds, userName, note, postalCode) -> {
            cancelActiveEvents(userId, userName, sequenceIds, note);
            sendEventOperationMsg(createEventOperationMessage(userId, userName, sequenceIds, note, postalCode, vo.getEventReasonType()), KafkaTopicEnum.EVENT_CANCEL);
        });
    }
    private EventOperationMessage createEventOperationMessage(int userId, String userName, List<String> sequenceIds, String note, Integer postalCode, Integer eventReasonType) {
        return new EventOperationMessage(userId, userName, sequenceIds, note, postalCode, eventReasonType);
    }

    private void cancelActiveEvents(int userId, String userName, List<String> sequenceIds, String note) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return;
        }
        activeEventManager.clearCache(sequenceIds);
        //是否使用去存储过程版本
        if (BooleanUtil.isTrue(featureEnableProperties.getEventRemoveProc())) {
            cancelActiveEventsRemoveProc(userId, userName, sequenceIds, note);
            return;
        }
        if (Boolean.TRUE.equals(byteDanceEnable)) {
            // 字节的需要使用新的存储过程应对增加的字段
            HashMap<String, Object> params = new HashMap<>(3);
            params.put("UserId", userId);
            params.put("Events", CollUtil.join(sequenceIds, ",", "'", "'"));
            params.put("Note", note);
            activeEventMapper.cancelBdActiveEvent(params);
        } else {
            HashMap<String, Object> params = new HashMap<>(3);
            params.put("UserId", userId);
            params.put("Events", CollUtil.join(sequenceIds, ",", "'", "'"));
            params.put("Note", note);
            activeEventMapper.cancelActiveEvent(params);
        }
    }

    @Override
    public void cancelActiveEventsRemoveProc(int userId, String userName, List<String> sequenceIds, String note) {
        // 手动开启事务  start
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            List<ActiveEvent> cancelAlarmList = activeEventMapper.findUnEndBySequenceIdsForUpdate(sequenceIds);
            Date endTime = new Date();
            Integer process = sarIsProcessMapper.isProcess();
            for (ActiveEvent event : cancelAlarmList) {
                //如果获取到的事件是设备通信状态事件，则更新设备表相应记录的通信状态字段
                if (Objects.equals(event.getEventId(), GlobalConstants.CONNECT_STATE_SIGNAL_ID)) {
                    equipmentService.updateConnectState(event.getEquipmentId(), OnlineState.ONLINE.value());
                }
                //TODO 20190508 对移动B接口而言,不能按-3判断,因为移动B接口中,设备中断告警是统一挂在FSU虚拟设备下的,且其对应的EventId也不是-3
                //更新TSL_ActiveEvent表
                tslActiveEventService.savePreActiveEvent(new ConfigEventItem(event, endTime));
                //需要处理关联告警
                if (Objects.nonNull(process) && process > 1) {
                    sarAlarmQueueService.deleteBySequenceId(event.getSequenceId());
                    SARAlarmActiveRecord sarAlarmActiveRecord = SARAlarmActiveRecord.builder()
                                                                                    .endTime(endTime)
                                                                                    .overturn(event.getReversalNum())
                                                                                    .meanings(event.getMeanings())
                                                                                    .eventValue(event.getEventValue())
                                                                                    .build();
                    sarAlarmActiveRecordMapper.updateBySequenceId(sarAlarmActiveRecord);
                }
                //更新active表的强制结束时间,为了移到历史表后有值
                String description = getNote(event.getDescription(), note);
                event.setDescription(description);
                activeEventMapper.updateCancelInfo(event.getSequenceId(), userId, userName, endTime, description);
                if (Objects.isNull(event.getConfirmTime())) {
                    event.setConfirmerId(userId);
                    event.setConfirmerName(userName);
                    event.setConfirmTime(endTime);
                }
                historyEventService.insertMidHistoryEvent(event.getSequenceId(), endTime, event.getConfirmTime(), event.getConfirmerId(), event.getConfirmerName(), description);
                //先结束
                AlarmChange alarmChange = BeanUtil.copyProperties(event, AlarmChange.class);
                alarmChange.setInsertTime(endTime);
                alarmChange.setOperationType(AlarmOperationTypeEnum.END.getValue());
                alarmChange.setEndTime(endTime);
                alarmChange.setCancelTime(endTime);
                alarmChange.setCancelUserId(userId);
                alarmChange.setCancelUserName(userName);
                alarmChange.setDescription(description);
                alarmChangeMapper.insert(alarmChange);
                //在确认
                alarmChange.setConfirmerId(event.getConfirmerId());
                alarmChange.setConfirmerName(event.getConfirmerName());
                alarmChange.setConfirmTime(event.getConfirmTime());
                alarmChange.setOperationType(AlarmOperationTypeEnum.CONFIRM.getValue());
                alarmChange.setSerialNo(null);
                alarmChangeMapper.insert(alarmChange);
            }
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("告警结束错误,{}", ExceptionUtil.stacktraceToString(e));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addEventNote(int userId, ActiveEventOperationVO vo) {
        return processEventOperation(userId, vo, (sequenceIds, userName,note, postalCode) -> {
            addEventNote(sequenceIds, note);
            sendEventOperationMsg(new EventOperationMessage(sequenceIds, note, postalCode), KafkaTopicEnum.EVENT_NOTE);
        });
    }
    private boolean processEventOperation(int userId, ActiveEventOperationVO vo, EventOperationHandler handler) {
        Integer postalCode = findPostalCodeBySequenceIds(vo.getSequenceIds());
        if (Objects.isNull(postalCode)) {
            log.error("没有获取到告警的postalCode,sequenceIds={}", vo.getSequenceIds());
            return false;
        }
        String userName = accountMapper.findUserNameByUserId(userId);
        return activeEventOperation(vo.getSequenceIds(), tempSequenceIds -> handler.handle(tempSequenceIds, userName, vo.getNote(), postalCode));
    }

    private void sendEventOperationMsg(EventOperationMessage eventOperationMessage, KafkaTopicEnum eventConfirm) {
        //如果没有开启kafka则无需发送消息
        Optional.ofNullable(kafkaUtil).ifPresent(kafka -> kafka.sendEventMsg(eventConfirm.getTopic(), eventOperationMessage));
    }

    public boolean activeEventOperation(List<String> sequenceIds, Consumer<List<String>> predicate) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return false;
        }
        int batchSize = Math.min(BATCH_OPERATION_ACTIVE_COUNT, sequenceIds.size());
        Lists.partition(sequenceIds, batchSize)
             .forEach(predicate);
        return true;
    }

    /**
     * 活动告警备注 对应存储过程：PAM_AddEventNote
     *
     * @param sequenceIds 告警流水号
     * @param note        注释
     */
    @Override
    public void addEventNote(List<String> sequenceIds, String note) {
        List<ActiveEvent> activeEventList = getActiveEventBySequenceIds(sequenceIds);
        LambdaUpdateWrapper<ActiveEvent> updateWrapper = Wrappers.lambdaUpdate(ActiveEvent.class);
        activeEventList.forEach(e -> {
            String description = getNote(e.getDescription(), note);
            updateWrapper.set(ActiveEvent::getDescription, description);
            updateWrapper.eq(ActiveEvent::getSequenceId, e.getSequenceId());

            activeEventMapper.update(null, updateWrapper);
            e.setDescription(description);
            AlarmChange alarmChange = BeanUtil.copyProperties(e, AlarmChange.class);
            alarmChange.setOperationType(AlarmOperationTypeEnum.NOTE.getValue());
            alarmChangeService.createAlarmChange(alarmChange);
            updateWrapper.clear();
        });
    }

    @Override
    public ActiveEvent getActiveEventBySequenceId(String sequenceId) {
        return activeEventManager.findActionEventBySequenceId(sequenceId);
    }

    @Override

    public Map<Integer, List<ActiveEventDTO>> findActiveEventsByEquipmentIds(String equipmentIds, Boolean eventEnded) {
        if (CharSequenceUtil.isBlank(equipmentIds)) {
            return Collections.emptyMap();
        }
        //构建条件
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEquipmentIds(equipmentIds);
        if (ObjectUtil.isNotNull(eventEnded)) {
            activeEventFilterVO.setEventEnded(eventEnded);
        }
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(-1, activeEventFilterVO);
        return activeEvents.stream()
                .map(event -> new ActiveEventDTO().from(event))
                .collect(Collectors.groupingBy(ActiveEventDTO::getEquipmentId));
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByEquipmentId(Integer equipmentId, Boolean eventEnded, Boolean eventConfirmed) {
        //构建条件
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEquipmentIds(equipmentId.toString());
        if (ObjectUtil.isNotNull(eventEnded)) {
            activeEventFilterVO.setEventEnded(eventEnded);
        }
        if (ObjectUtil.isNotNull(eventConfirmed)) {
            activeEventFilterVO.setEventConfirmed(eventConfirmed);
        }
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(-1, activeEventFilterVO);
        return activeEvents.stream()
                .map(event -> new ActiveEventDTO().from(event))
                .toList();
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByResourceStructureIdSet(Set<Integer> resourceStructureIdSet, Boolean eventEnded) {
        //构建条件
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setResourceStructureIds(resourceStructureIdSet.stream().map(Object::toString).collect(Collectors.joining(",")));
        if (ObjectUtil.isNotNull(eventEnded)) {
            activeEventFilterVO.setEventEnded(eventEnded);
        }
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(-1, activeEventFilterVO);
        return activeEvents.stream()
                .map(event -> new ActiveEventDTO().from(event))
                .toList();
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByEquipmentIdAndEventId(Integer equipmentId, Integer eventId) {
        return activeEventManager.getActiveEventsByEquipmentId(equipmentId)
                .stream()
                .filter(o -> o.getEventId().equals(eventId))
                .map(event -> new ActiveEventDTO().from(event))
                .toList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long simulateActiveEvent(ConfigEventCondition configEventVO) {
        ConfigEventItem configEvent = new ConfigEventItem(configEventVO);
        //Integer result = activeEventMapper.simulateActiveEvent(configEvent);
        //存放告警到TSL_ActiveEvent表
        int saveActiveEventResult = tslActiveEventService.savePreActiveEvent(configEvent);
        if (saveActiveEventResult < 0) {
            return -1L;
        }
        //告警关联处理
        Integer alarmQueue = sarAlarmQueueService.createAlarmQueue(configEvent);
        if (Objects.equals(alarmQueue, 1)) {
            return 0L;
        }
        //如果获取到的事件是设备通信状态事件，则更新设备表相应记录的通信状态字段
        if (ObjectUtil.equals(configEvent.getEventId(), GlobalConstants.CONNECT_STATE_SIGNAL_ID)) {
            equipmentService.updateConnectState(configEvent.getEquipmentId(), OnlineState.OFFLINE.value());
        }
        //屏蔽处理，如果告警被屏蔽，则将告警转移到屏蔽记录表
        int maskResult = this.maskHandler(configEvent);
        if (maskResult != 1) {
            return -3L;
        }
        //修改告警表
        if (ObjectUtil.isNotNull(this.getActiveEventBySequenceId(configEvent.getSequenceId()))) {
            return -1L;
        }
        int maintainState = this.getMaintainState(configEvent.getStationId(), configEvent.getEquipmentId(), configEvent.getStartTime());
        Optional<StationStructure> stationStructureOptional = Optional.ofNullable(stationStructureService.findByStationId(configEvent.getStationId()));
        Equipment equipment = equipmentService.findById(configEvent.getEquipmentId());
        Integer monitorUnitId = Optional.ofNullable(equipment).orElse(new Equipment()).getMonitorUnitId();
        MonitorUnit monitorUnit = monitorUnitMapper.selectById(monitorUnitId);
        EventCondition eventCondition = eventConditionService.findByEquipmentIdAndEventIdAndEventConditionId(configEvent.getEquipmentId(), configEvent.getEventId(), configEvent.getEventConditionId());
        CoreEventSeverity coreEventSeverity = coreEventSeverityService.findByEventSeverity(eventCondition.getEventSeverity());
        EventBaseDic eventBaseDic = Optional.ofNullable( eventBaseDicService.findById(configEventVO.getBaseTypeId())).orElse(new EventBaseDic());
        Station station = stationService.findById(configEvent.getStationId());
        StationStructure center = stationStructureService.findById(station.getCenterId());
        ConfigEventDTO eventConfig = eventService.findConfigEventDTOByEventId(equipment.getEquipmentId(), configEvent.getEventId());
        StandardAlarmNameDTO standardAlarmName = null;
        if (ObjectUtil.isNotNull(configEvent.getBaseTypeId())) {
            standardAlarmName = standardDicEventMapper.findStandardAlarmName(configEvent.getStationId(), standardVerService.getStandardVer(), configEvent.getBaseTypeId());
        }
        standardAlarmName = Optional.ofNullable(standardAlarmName).orElse(new StandardAlarmNameDTO());
        String equipmentCategoryName = Optional.ofNullable(dataItemService.findByEntryIdIdAndItemId(EQUIPMENT_CATEGORY_ENTRY_ID, equipment.getEquipmentCategory()))
                                               .map(DataItem::getItemValue).orElse("");
        ActiveEvent activeEvent = ActiveEvent.builder()
                                          .sequenceId(configEvent.getSequenceId())
                                          .stationId(station.getStationId())
                                          .stationName(station.getStationName())
                                          .equipmentId(equipment.getEquipmentId())
                                          .equipmentName(equipment.getEquipmentName())
                                          .eventId(configEventVO.getEventId())
                                          .eventName(eventConfig.getEventName())
                                          .eventConditionId(configEvent.getEventConditionId())
                                          .eventSeverity(coreEventSeverity.getSeverityName())
                                          .eventSeverityId(coreEventSeverity.getEventSeverity())
                                          .eventLevel(coreEventSeverity.getEventLevel())
                                          .startTime(configEvent.getStartTime())
                                          .endTime(configEvent.getEndTime())
                                          .eventValue(configEvent.getEventValue())
                                          .reversalNum(configEvent.getOverturn())
                                          .meanings(configEvent.getMeanings())
                                          .instructionStatus(0)
                                          .standardAlarmNameId(standardAlarmName.getStandardAlarmNameId())
                                          .standardAlarmName(standardAlarmName.getStandardAlarmName())
                                          .baseTypeId(eventBaseDic.getBaseTypeId())
                                          .baseTypeName(eventBaseDic.getBaseTypeName())
                                          .equipmentCategory(equipment.getEquipmentCategory())
                                          .equipmentCategoryName(equipmentCategoryName)
                                          .monitorUnitName(String.valueOf(monitorUnit.getMonitorUnitCategory()))
                                          .maintainState(maintainState)
                                          .signalId(eventConfig.getSignalId())
                                          .eventCategoryId(eventConfig.getEventCategory())
                                          .eventStateId(Boolean.TRUE.equals(eventConfig.getEnable()) ? 1 : 0)
                                          .centerId(center.getStructureId())
                                          .centerName(center.getStructureName())
                                          .structureId(stationStructureOptional.map(StationStructure::getStructureId).orElse(null))
                                          .structureName(stationStructureOptional.map(StationStructure::getStructureName).orElse(null))
                                          .stationCategoryId(station.getStationCategory())
                                          .equipmentVendor(equipment.getVendor())
                                          .resourceStructureId(equipment.getResourceStructureId())
                                          .baseEquipmentId(equipment.getEquipmentBaseType())
                                          .build();
        activeEventMapper.insert(activeEvent);
        AlarmChange alarmChange = BeanUtil.copyProperties(activeEvent, AlarmChange.class);
        alarmChange.setOperationType(AlarmOperationTypeEnum.START.getValue());
        alarmChangeMapper.insert(alarmChange);
        return alarmChange.getSerialNo();
    }

    private int getMaintainState(Integer stationId,Integer equipmentId,Date startTime) {
        int equipmentMaintainState = equipmentMaintainService.findMaintainState(stationId, equipmentId, startTime);
        int stationMaintainState = stationService.findMaintainState(stationId, startTime);
        int maintainState = 0;
        if (equipmentMaintainState == 1 && stationMaintainState == 1) {
            maintainState = 1;
        } else if (equipmentMaintainState == 0 && stationMaintainState == 1) {
            maintainState = 3;
        }else if (equipmentMaintainState == 1 && stationMaintainState == 0) {
            maintainState = 2;
        }
        return maintainState;
    }

    private int maskHandler(ConfigEventItem configEvent) {
        if (eventMaskHistoryService.existsBySequenceId(configEvent.getSequenceId())) {
            return -3;
        }
        //是否在告警屏蔽生效范围内
        if (eventMaskService.isMaskEffective(configEvent.getStationId(), configEvent.getEquipmentId(), configEvent.getEventId())) {
            eventMaskHistoryService.create(configEvent.buildEventMaskHistory());
            return -3;
        }
        //是否在设备屏蔽生效范围内
        if (equipmentMaskService.isMaskEffective(configEvent.getEquipmentId())) {
            eventMaskHistoryService.create(configEvent.buildEventMaskHistory());
            return -3;
        }
        //是否在局站屏蔽生效范围内
        if (stationMaskService.isMaskEffective(configEvent.getStationId())) {
            eventMaskHistoryService.create(configEvent.buildEventMaskHistory());
            return -3;
        }
        return 1;
    }

    @Override
    public ActiveEventDTO findTopOneActiveEventByStartTimeAndUserId(Date startTime, Integer userId) {
        return activeEventManager.findTopOneActiveEventByStartTimeAndUserId(startTime, userId);
    }

    @Override
    public List<ActiveEventDTO> findActiveEventDTOsByStartTimeAndUserId(Date startTime, Date endTime, Integer userId, boolean queryByEndTime) {
        return activeEventManager.findActiveEventDTOsByStartTimeAndUserId(startTime, endTime, userId, queryByEndTime);
    }

    @Override
    public List<ActiveEventDTO> findActiveEventDTOsByUserIdAndEndTimeIsNull(Integer userId) {
        return activeEventManager.findActiveEventDTOsByUserIdAndEndTimeIsNull(userId);
    }

    public List<ActiveEventDTO> findActiveEventDTOsByUserIdAndEventCategoryAndEndTimeIsNull(Integer userId, Integer eventCategory) {
        List<ActiveEventDTO> result = activeEventManager.findActiveEventDTOsByUserIdAndEndTimeIsNull(userId);
        return result.stream().filter(e -> e.getEventCategoryId() == eventCategory).toList();
    }

    @Override
    public ActiveEvent getDBActiveEventBySequenceId(String sequenceId) {
        return activeEventMapper.selectOne(Wrappers.lambdaQuery(ActiveEvent.class).eq(ActiveEvent::getSequenceId, sequenceId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeBySequenceId(String sequenceId) {
        return activeEventMapper.deleteById(sequenceId);
    }

    @Override
    public int removeBySequenceIds(List<String> sequenceIds) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return 0;
        }
        return activeEventMapper.deleteByIds(sequenceIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEndTimeBySequenceId(Date endTime, String sequenceId) {
        return activeEventMapper.update(null, Wrappers.lambdaUpdate(ActiveEvent.class).set(ActiveEvent::getEndTime, endTime).eq(ActiveEvent::getSequenceId, sequenceId));
    }
    @Override
    public int updateEndTimeBySequenceIds(Date endTime, List<String> sequenceIds) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return 0;
        }
        return activeEventMapper.update(null, Wrappers.lambdaUpdate(ActiveEvent.class).set(ActiveEvent::getEndTime, endTime).in(ActiveEvent::getSequenceId, sequenceIds));
    }

    @Override
    public int updateConfirmTimeAndConfirmerIdAndConfirmerNameIdBySequenceId(Date confirmTime, Integer confirmerId, String confirmerName, String sequenceId,String note) {
        return activeEventMapper.update(null, Wrappers.lambdaUpdate(ActiveEvent.class)
                                                      .set(ActiveEvent::getConfirmTime, confirmTime)
                                                      .set(ActiveEvent::getConfirmerId, confirmerId)
                                                      .set(ActiveEvent::getConfirmerName, confirmerName)
                                                      .set(ActiveEvent::getDescription, note)
                                                      .eq(ActiveEvent::getSequenceId, sequenceId));
    }

    @Override
    public List<ActiveEvent> getDBActiveEventByCondition(Integer stationId, Integer equipmentId, Integer eventId) {
        return activeEventMapper.selectList(Wrappers.lambdaQuery(ActiveEvent.class)
                                                    .eq(ActiveEvent::getStationId, stationId)
                                                    .eq(ObjectUtil.isNotEmpty(equipmentId), ActiveEvent::getEquipmentId, equipmentId)
                                                    .eq(ObjectUtil.isNotEmpty(eventId), ActiveEvent::getEventId, eventId)
                                                    .isNull(ActiveEvent::getEndTime));
    }

    @Override
    public List<ActiveEvent> getActiveEventBySequenceIds(Collection<String> sequenceIds) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return Collections.emptyList();
        }
        return activeEventMapper.selectList(Wrappers.lambdaQuery(ActiveEvent.class)
                                                    .in(ActiveEvent::getSequenceId, sequenceIds));
    }

    @Override
    public void confirmActiveEvents(int userId, String userName, List<String> sequenceIds, String note, Integer eventReasonType) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return;
        }
        activeEventManager.clearCache(sequenceIds);
        //是否使用去存储过程版本
        if (BooleanUtil.isTrue(featureEnableProperties.getEventRemoveProc())) {
            confirmActiveEventsRemoveProc(userId, userName, sequenceIds, note, eventReasonType);
            return;
        }
        if (Boolean.TRUE.equals(byteDanceEnable)) {
            // 字节的需要使用新的存储过程应对增加的字段
            Map<String, Object> params = new HashMap<>(4);
            params.put("Events", CollUtil.join(sequenceIds, ",","'","'"));
            params.put("ConfirmerId",userId);
            params.put("Note", note);
            params.put("EventReasonType", eventReasonType);
            activeEventMapper.confirmBdActiveEvent(params);
        } else {
            HashMap<String, Object> params = new HashMap<>(3);
            params.put("Events", CollUtil.join(sequenceIds, ",","'","'"));
            params.put("ConfirmerId",userId);
            params.put("Note", note);
            activeEventMapper.confirmActiveEvent(params);
        }

    }

    @Override
    public void confirmActiveEventsRemoveProc(int userId, String userName, List<String> sequenceIds, String note, Integer eventReasonType) {
        // 手动开启事务  start
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            List<ActiveEvent> activeEventList = activeEventMapper.findUnConfirmBySequenceIdsForUpdate(sequenceIds);
            Integer confirmerId = userId;
            Date confirmTime = new Date();
            List<ActiveEvent> batchUpdateList = new ArrayList<>(sequenceIds.size());
            List<HistoryEvent> batchRemoveList = new ArrayList<>(sequenceIds.size());
            List<AlarmChange> batchInsertAlarmChangeList = new ArrayList<>(sequenceIds.size());
            for (ActiveEvent activeEvent : activeEventList) {
                Date endTime = activeEvent.getEndTime();
                String sequenceId = activeEvent.getSequenceId();
                note = getNote(activeEvent.getDescription(), note);
                if (ObjectUtil.isNull(endTime)) {
                    ActiveEvent operationActiveEvent = ActiveEvent.builder()
                                                                  .confirmTime(confirmTime)
                                                                  .confirmerId(confirmerId)
                                                                  .confirmerName(userName)
                                                                  .sequenceId(sequenceId)
                                                                  .description(note)
                                                                  .endTime(endTime)
                                                                  .eventReasonType(eventReasonType)
                                                                  .build();
                    batchUpdateList.add(operationActiveEvent);
                } else {
                    //已经结束了还操作了确认，需要删除活动告警并且移动到历史告警中
                    HistoryEvent historyEvent = BeanUtil.copyProperties(activeEvent, HistoryEvent.class);
                    historyEvent.setConfirmTime(confirmTime);
                    historyEvent.setConfirmerId(confirmerId);
                    historyEvent.setConfirmerName(userName);
                    historyEvent.setDescription(note);
                    historyEvent.setEventReasonType(eventReasonType);
                    batchRemoveList.add(historyEvent);
                }
                AlarmChange alarmChange = BeanUtil.toBean(activeEvent, AlarmChange.class);
                alarmChange.setOperationType(AlarmOperationTypeEnum.CONFIRM.getValue());
                alarmChange.setEndTime(endTime);
                alarmChange.setConfirmTime(confirmTime);
                alarmChange.setConfirmerId(confirmerId);
                alarmChange.setConfirmerName(userName);
                alarmChange.setDescription(note);
                alarmChange.setInsertTime(confirmTime);
                alarmChange.setEventReasonType(eventReasonType);
                batchInsertAlarmChangeList.add(alarmChange);
            }
            //批量更新
            batchUpdateConfirmEvent(batchUpdateList);
            //既确认又结束了
            historyEventService.batchInsert(batchRemoveList);
            batchDeleteBySequenceId(batchRemoveList.stream().map(HistoryEvent::getSequenceId).toList());
            //更新操作日志
            alarmChangeService.batchInsert(batchInsertAlarmChangeList);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("告警确认错误,{}", ExceptionUtil.stacktraceToString(e));
        }
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByStationIds(List<Integer> stationIds, Boolean eventEnded) {
        List<Equipment> equipments = equipmentManager.getEquipmentByStationId(stationIds);

        String equipmentIds = equipments.stream()
                .map(equipment -> String.valueOf(equipment.getEquipmentId()))
                .collect(Collectors.joining(","));

        if (CharSequenceUtil.isBlank(equipmentIds)) {
            return Collections.emptyList();
        }
        //构建条件
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEquipmentIds(equipmentIds);
        if (ObjectUtil.isNotNull(eventEnded)) {
            activeEventFilterVO.setEventEnded(eventEnded);
        }
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(-1, activeEventFilterVO);
        return activeEvents.stream()
                .map(event -> new ActiveEventDTO().from(event))
                .toList();
    }

    private String getNote(String oldNote, String newNote) {
        if (CharSequenceUtil.length(newNote) > NOTE_MAX_LENGTH) {
            return oldNote;
        }
        if (CharSequenceUtil.isBlank(oldNote)) {
            return newNote;
        }
        if (CharSequenceUtil.isBlank(newNote)) {
            return oldNote;
        }
        String currentNote = oldNote + NOTE_SPLIT_SYMBOL + newNote;
        //注释长度超过255时将最前面的注释移除
        while (currentNote.length() > NOTE_MAX_LENGTH) {
            currentNote = CharSequenceUtil.subAfter(currentNote, NOTE_SPLIT_SYMBOL, false);
        }
        return currentNote;
    }
    @Override
    public int updateActiveEventByActiveEventInstructionDTO(ActiveEventInstructionDTO activeEventInstructionDTO){
        return activeEventMapper.updateActiveEventByActiveEventInstructionDTO(activeEventInstructionDTO.getSequenceId(), activeEventInstructionDTO.getInstructionId(),activeEventInstructionDTO.getInstructionStatus());
    }

    @Override
    public List<ActiveEventDTO> getActiveEventByEquipmentEventKeys(Integer userId, String equipmentEventKeys) {
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEquipmentEventKeys(equipmentEventKeys);
        return activeEventManager.queryActiveEvents(userId, activeEventFilterVO)
                                 .stream()
                                 .map(ActiveEventDTO::new)
                                 .toList();
    }

    private void batchDeleteBySequenceId(List<String> sequenceIds){
        if (CollUtil.isEmpty(sequenceIds)) {
            return;
        }
        activeEventMapper.deleteBatchIds(sequenceIds);
    }
    private void batchUpdateConfirmEvent(List<ActiveEvent> batchUpdateList){
        if (CollUtil.isEmpty(batchUpdateList)) {
            return;
        }
        activeEventMapper.batchUpdateConfirmEvent(batchUpdateList);
    }

    /**
     * 获取区号
     * @param sequenceIds 告警流水号
     * @return {@link Integer }
     */
    private Integer findPostalCodeBySequenceIds(List<String> sequenceIds) {
        for (String sequenceId : sequenceIds) {
            ActiveEvent activeEvent = activeEventManager.findActionEventBySequenceId(sequenceId);
            if (activeEvent != null) {
                return activeEvent.getEquipmentId() / GlobalConstants.ID_GENERATION_BASE;
            }
        }
        return null;
    }
}
