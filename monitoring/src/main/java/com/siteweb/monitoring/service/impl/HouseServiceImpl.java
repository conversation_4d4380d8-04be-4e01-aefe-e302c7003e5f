package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.HouseInfoDTO;
import com.siteweb.monitoring.entity.House;
import com.siteweb.monitoring.mapper.HouseMapper;
import com.siteweb.monitoring.service.HouseService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("houseService")
public class HouseServiceImpl implements HouseService {

    @Autowired
    HouseMapper houseMapper;

    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Override
    public List<House> findHouses() {
        return houseMapper.selectList(null);
    }
    public List<House> findHousesByStationids(Collection<Integer> stationIds) {
        if (CollUtil.isEmpty(stationIds)){
            return findHouses();
        }
        return houseMapper.selectList(new LambdaQueryWrapper<House>().in(House::getStationId,stationIds));
    }

    @Override
    public List<HouseInfoDTO> findAllHouseInfo(List<Integer> stationIds) {
        return houseMapper.findHouseInfo(stationIds);
    }

    @Override
    public int createHouse(House house) {
         return houseMapper.insert(house);
    }
    @Override
    public House findHouse(Integer stationId, String houseName) {
        return houseMapper.selectOne(Wrappers.<House>lambdaQuery()
                                             .eq(House::getStationId, stationId)
                                             .eq(House::getHouseName, houseName));
    }
    @Override
    public Map<String, String> houseIdNameMap() {
        return findHouses().stream()
                .collect(Collectors.toMap(h -> String.format("%s%s", h.getHouseId(), h.getStationId()), House::getHouseName));
    }
}
