package com.siteweb.monitoring.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.EventCondition;
import com.siteweb.monitoring.entity.SARAlarmActiveRecord;
import com.siteweb.monitoring.entity.SARAlarmQueue;
import com.siteweb.monitoring.mapper.SARAlarmActiveRecordMapper;
import com.siteweb.monitoring.mapper.SARAlarmQueueMapper;
import com.siteweb.monitoring.model.ConfigEventItem;
import com.siteweb.monitoring.service.EventConditionService;
import com.siteweb.monitoring.service.SARAlarmQueueService;
import com.siteweb.monitoring.service.SARIsProcessService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.utility.service.StandardVerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class SARAlarmQueueServiceImpl implements SARAlarmQueueService {
    @Autowired
    private SARAlarmQueueMapper sarAlarmQueueMapper;
    @Autowired
    private SARIsProcessService sarIsProcessService;
    @Autowired
    private StandardVerService standardVerService;
    @Autowired
    private StationService stationService;
    @Autowired
    private EventConditionService eventConditionService;
    @Autowired
    private SARAlarmActiveRecordMapper sarAlarmActiveRecordMapper;
    @Override
    public Integer createAlarmQueue(ConfigEventItem configEvent) {
        Integer process = sarIsProcessService.isProcess();
        if (ObjectUtil.isNull(process) || process < 1) {
            return 0;
        }
        int standardVer = standardVerService.getStandardVer();
        Integer stationBaseType = stationService.findStationBaseTypeByStationIdAndStandardVer(configEvent.getStationId(), standardVer);
        EventCondition eventCondition = eventConditionService.findByEquipmentIdAndEventIdAndEventConditionId(configEvent.getEquipmentId(), configEvent.getEventId(), configEvent.getEventConditionId());
        SARAlarmQueue alarmQueue = SARAlarmQueue.builder()
                                           .stationId(configEvent.getStationId())
                                           .stationCategoryId(stationBaseType)
                                           .equipmentId(configEvent.getEquipmentId())
                                           .eventId(configEvent.getEventId())
                                           .eventConditionId(configEvent.getEventConditionId())
                                           .sequenceId(configEvent.getSequenceId())
                                           .startTime(configEvent.getStartTime())
                                           .endTime(configEvent.getEndTime())
                                           .overturn(configEvent.getOverturn())
                                           .meanings(configEvent.getMeanings())
                                           .eventValue(configEvent.getEventValue())
                                           .baseTypeId(Optional.ofNullable(eventCondition).orElse(new EventCondition()).getBaseTypeId())
                                           .insertDateTime(new DateTime())
                                           .build();
        return sarAlarmQueueMapper.insert(alarmQueue);
    }

    @Override
    public void deleteBySequenceId(String sequenceId) {
        sarAlarmQueueMapper.delete(Wrappers.lambdaQuery(SARAlarmQueue.class)
                                           .eq(SARAlarmQueue::getSequenceId, sequenceId));
    }
}
