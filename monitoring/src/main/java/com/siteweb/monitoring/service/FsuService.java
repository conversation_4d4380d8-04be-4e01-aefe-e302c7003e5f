package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import org.springframework.data.domain.Page;
import com.siteweb.monitoring.vo.FsuFilterVo;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface FsuService {
    List<Map<String, String>> findFsuTypeList();

    List<Map<String, String>>  findCpuUsedList();

    List<Map<String, String>> findMemoryUsedList();

    List<Map<String, String>> findFlashUsedList();

    Page<IcsFsuDataNewInfoDTO> findAllFsu(Integer userId, Pageable pageable, FsuFilterVo filterVo);
}

