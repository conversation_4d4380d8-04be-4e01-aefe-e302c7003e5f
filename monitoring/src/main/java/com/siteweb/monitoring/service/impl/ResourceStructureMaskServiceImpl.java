package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.ResourceStructureMask;
import com.siteweb.monitoring.mapper.ResourceStructureMaskMapper;
import com.siteweb.monitoring.service.ResourceStructureMaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("resourceStructureMaskService")
public class ResourceStructureMaskServiceImpl implements ResourceStructureMaskService {

    @Autowired
    private ResourceStructureMaskMapper resourceStructureMaskMapper;

    @Override
    public List<ResourceStructureMask> findResourceStructureMasks() {
        return resourceStructureMaskMapper.selectList(null);
    }

    @Override
    public int createResourceStructureMask(ResourceStructureMask resourceStructureMask) {
        return resourceStructureMaskMapper.insert(resourceStructureMask);
    }

    @Override
    public int deleteById(Integer resourceStructureMaskId) {
        return    resourceStructureMaskMapper.deleteById(resourceStructureMaskId);
    }

    @Override
    public int updateResourceStructureMask(ResourceStructureMask resourceStructureMask) {
        return resourceStructureMaskMapper.updateById(resourceStructureMask);
    }

    @Override
    public ResourceStructureMask findById(Integer resourceStructureMaskId) {
        return resourceStructureMaskMapper.selectById(resourceStructureMaskId);
    }
}
