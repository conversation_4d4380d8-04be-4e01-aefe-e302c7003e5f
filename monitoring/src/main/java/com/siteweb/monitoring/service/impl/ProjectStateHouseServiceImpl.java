package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.ProjectStateHouse;
import com.siteweb.monitoring.mapper.ProjectStateHouseMapper;
import com.siteweb.monitoring.service.ProjectStateHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProjectStateHouseServiceImpl implements ProjectStateHouseService {
    @Autowired
    ProjectStateHouseMapper projectStateHouseMapper;

    @Override
    public ProjectStateHouse findByStationIdAndHouseId(Integer stationId, Integer houseId) {
        return projectStateHouseMapper.selectOne(Wrappers.lambdaQuery(ProjectStateHouse.class)
                                                         .eq(ProjectStateHouse::getStationId, stationId)
                                                         .eq(ProjectStateHouse::getHouseId, houseId));
    }
}
