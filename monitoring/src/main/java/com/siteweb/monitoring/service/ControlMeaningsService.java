package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.ControlMeaningsDTO;
import com.siteweb.monitoring.entity.ControlMeanings;

import java.util.HashMap;
import java.util.List;

public interface ControlMeaningsService {

    HashMap<Integer, List<ControlMeanings>> getControlMeaningsByEquipmentId(Integer equipmentId);

    List<ControlMeaningsDTO> findControlMeaningsDTOsByControlId(Integer equipmentId, Integer controlId);

    List<ControlMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchInsert(List<ControlMeanings> controlMeaningsList);
}

