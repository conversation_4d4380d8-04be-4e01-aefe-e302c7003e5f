package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.model.ControlSignalRelation;
import com.siteweb.monitoring.vo.SignalBatchApplyVO;

import java.util.*;

public interface SignalService {

    List<Signal> findSignalsByEquipmentId(Integer equipmentId);

    HashMap<Integer, ControlSignalRelation> findSignalsAboutControlByEquipmentId(Integer equipmentId);

    List<ConfigSignalItem> findEquipmentId(Integer equipmentId);

    Map<Integer, List<ConfigSignalItem>> findConfigSignalMap(List<Integer> equipmentIdList);

    List<SimpleSignalDTO> findSimpleSignalDTOsByEquipmentId(Integer equipmentId);

    List<SimpleSignalDTO> findSimpleSignalDTOsByBaseTypeIdsAndEquipmentIds(Collection<Integer> baseTypeIdList, Collection<Integer> equipmentIds);

    ConfigSignalDTO findConfigSignalDTOBySignalId(int equipmentId, int signalId);

    int updateConfigSignalDTO(int userId, ConfigSignalDTO configSignalDTO);

    int batchApplyConfigSignalDTOToEquipment(int userId, SignalBatchApplyVO signalBatchApplyVO);

    /**
     * 通过信号ids获取其信号配置
     * @param signalIds 信号配置ids
     * @return {@link List}<{@link Signal}>
     */
    List<Signal> findSignalsByTemplateIdAndSignalIds(Integer templateId, List<Integer> signalIds);
    /**
     * 通过信号ids获取其信号配置
     * @param signalIds 信号配置ids
     * @return {@link List}<{@link Signal}>
     */
    List<EquipmentSignalDto> findSignalsByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds);

    /**
     * 批量插入信号配置
     * @param signalList 信号配置ids
     */
    void batchInsert(List<Signal> signalList);

    Signal findSignalIdByTemplateIdAndSignalName(Integer equipmentTemplateId, String virtualSignalName);

    Signal findSignalIdByTemplateIdAndChannel(Integer equipmentTemplateId, Integer channelNo);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    List<SimpleEventSignalDTO> findSignalEvent(Integer equipmentId);

    List<SimpleSignalEquipmentDTO> findSimpleSignalDTOsByRealSignalKeys(List<String> realSignalKeys);

    /**
     *  复制模板信号相关数据，将源模板上的信号相关数据添加到目标模板上
     *
     * @param sourceTemplateId 源模板id
     * @param destTemplateId   目标模板
     */
    void copyTemplateSignal(Integer sourceTemplateId, int destTemplateId);

    List<Signal> findSignalByTemplateIds(Collection<Integer> templateIds);
}

