package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.HexUtil;
import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.entity.StationStructure;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.mapper.TimeGroupSpanMapper;
import com.siteweb.monitoring.service.StationStructureService;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author: lzy
 * @Date: 2023/7/25 10:34
 */
@Service
public class TimeGroupSpanServiceImpl implements TimeGroupSpanService {

    @Autowired
    TimeGroupSpanMapper timeGroupSpanMapper;
    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Override
    public void saveTimeGroupSpan(TimeGroupSpan timeGroupSpan) {
        timeGroupSpanMapper.batchInsert(List.of(timeGroupSpan));
    }

    @Override
    public void updateLastUpdateDateAndTimeSpanCharByTimeGroupIdAndWeek(String timeMaskChar, Integer timeGroupId, Integer week) {
        timeGroupSpanMapper.updateLastUpdateDateAndTimeSpanCharByTimeGroupIdAndWeek(timeMaskChar,timeGroupId,week,new Date());
    }
    @Override
    public void deleteEventMask(Integer stationId, Integer equipmentId, Integer eventId) {
        List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanMapper.findEventMaskTimeGroup(stationId, equipmentId, eventId);
        if (CollUtil.isEmpty(timeGroupSpanList)) {
            return;
        }
        List<Integer> timeSpanIdList = timeGroupSpanList.stream()
                                                        .map(TimeGroupSpan::getTimeSpanId)
                                                        .toList();
        timeGroupSpanMapper.deleteByIds(timeSpanIdList);
    }

    @Override
    public void saveOrUpdateTimeGroupSpan(Integer timeGroupId, String timeGroupChar, Integer week) {
        StationStructure postalStructure = Optional.ofNullable(stationStructureService.findPostalStructure())
                                                   .orElse(new StationStructure());
        if (ObjectUtil.isEmpty(postalStructure.getStructureId())) {
            throw new BusinessException("postalStructure is null");
        }
        TimeGroupSpan timeGroupSpan = timeGroupSpanMapper.findByWeekAndTimeGroupId(week, timeGroupId);
        if (ObjectUtil.isNotEmpty(timeGroupSpan)) {
            updateLastUpdateDateAndTimeSpanCharByTimeGroupIdAndWeek(timeGroupChar, timeGroupId, week);
            return;
        }
        int timeSpanId = primaryKeyValueService.getGlobalIdentity("TBL_TimeGroupSpan", postalStructure.getStructureId());
        timeGroupSpan = new TimeGroupSpan();
        timeGroupSpan.setTimeSpanId(timeSpanId);
        timeGroupSpan.setTimeGroupId(timeGroupId);
        timeGroupSpan.setStartTime(null);
        timeGroupSpan.setEndTime(null);
        timeGroupSpan.setWeek(week);
        timeGroupSpan.setTimeSpanChar(timeGroupChar);
        timeGroupSpan.setLastUpdateDate(new Date());
        saveTimeGroupSpan(timeGroupSpan);
    }
    @Override
    public Integer createTimeSpans(Integer timeGroupId, String timeSpanStrs) {
        //准进组时间段字符
        //100:00-23:5900:00-00:0000:00-00:00,200:00-23:5900:00-00:0000:00-00:00
        //星期几+HH:mm-HH:mm*3时间段,星期几+HH:mm-HH:mm*3时间段
        List<String> timeSpanList = CharSequenceUtil.split(timeSpanStrs, ",");
        List<TimeGroupSpan> timeGroupSpanList = new ArrayList<>();
        for (String timeSpan : timeSpanList) {
            Integer week = Integer.valueOf(CharSequenceUtil.sub(timeSpan, 0, 1));
            String timeSpanChar = CharSequenceUtil.sub(timeSpan, 1, timeSpan.length());
            int timeSpanId = primaryKeyValueService.getGlobalIdentity("TBL_TimeGroupSpan", 0);
            TimeGroupSpan timeGroupSpan = new TimeGroupSpan(timeSpanId, timeGroupId, week, timeSpanChar);
            timeGroupSpanList.add(timeGroupSpan);
        }
        timeGroupSpanMapper.batchInsert(timeGroupSpanList);
        return timeSpanList.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateTimeSpans(Integer timeGroupId, String timeSpanStrs) {
        this.deleteByTimeGroupIds(List.of(timeGroupId));
        return this.createTimeSpans(timeGroupId, timeSpanStrs);
    }

    @Override
    public Integer deleteByTimeGroupIds(List<Integer> timeGroupIdList) {
        if (CollUtil.isEmpty(timeGroupIdList)) {
            return 0;
        }
        return timeGroupSpanMapper.delete(Wrappers.lambdaQuery(TimeGroupSpan.class)
                                                  .in(TimeGroupSpan::getTimeGroupId, timeGroupIdList));
    }

    @Override
    public List<TimeGroupSpan> findByTimeGroupId(Integer timeGroupId) {
        return timeGroupSpanMapper.findByTimeGroupId(timeGroupId);
    }

    @Override
    public void batchInsertEquipmentTimeGroupSpan(BatchEquipmentMaskVO batchEquipmentMaskVO) {
        if (CollUtil.isEmpty(batchEquipmentMaskVO.getEquipmentIds()) || CollUtil.isEmpty(batchEquipmentMaskVO.getTimeGroupSpans())) {
            return;
        }
        List<Integer> timeGroupIds = batchEquipmentMaskVO.getEquipmentIds().stream().map(e -> Integer.valueOf("1" + Math.abs(e))).toList();
        //在新增
        generateTimePeriodMask(timeGroupIds, batchEquipmentMaskVO.getTimeGroupSpans());
    }

    @Override
    public void batchInsertStationTimeGroupSpan(BatchSetStationMaskDTO dto) {
        if (CollUtil.isEmpty(dto.getStationIdList()) || CollUtil.isEmpty(dto.getTimeGroupSpans())) {
            return;
        }
        List<Integer> timeGroupIds = dto.getStationIdList();
        //在新增
        generateTimePeriodMask(timeGroupIds, dto.getTimeGroupSpans());
    }

    private void generateTimePeriodMask(List<Integer> timeGroupIds, List<TimeGroupSpanVO> dto) {
        Date now = new Date();
        List<TimeGroupSpan> timeGroupSpans = new ArrayList<>();
        for (Integer timeGroupId : timeGroupIds) {
            for (TimeGroupSpanVO timeGroupSpanVo : dto) {
                //这个地方每次都要生成一个timeSpanId，比较的耗时!!!
                int timeSpanId = primaryKeyValueService.getGlobalIdentity("TBL_TimeGroupSpan", 0);
                TimeGroupSpan timeGroupSpan = new TimeGroupSpan();
                timeGroupSpan.setTimeSpanId(timeSpanId);
                timeGroupSpan.setTimeGroupId(timeGroupId);
                timeGroupSpan.setStartTime(null);
                timeGroupSpan.setEndTime(null);
                timeGroupSpan.setWeek(timeGroupSpanVo.getWeek());
                timeGroupSpan.setTimeSpanChar(HexUtil.booleanListToHexString(timeGroupSpanVo.getTimeSpanBool()));
                timeGroupSpan.setLastUpdateDate(now);
                timeGroupSpans.add(timeGroupSpan);
            }
        }
        timeGroupSpanMapper.batchInsert(timeGroupSpans);
    }

    @Override
    public List<TimeGroupSpan> findByTimeGroupIds(List<Integer> timeGroupIds) {
        if (CollUtil.isEmpty(timeGroupIds)) {
            return Collections.emptyList();
        }
        return timeGroupSpanMapper.selectList(Wrappers.lambdaQuery(TimeGroupSpan.class).in(TimeGroupSpan::getTimeGroupId, timeGroupIds).orderByAsc(TimeGroupSpan::getWeek));
    }

    @Override
    public void batchInsertEventTimeGroupSpan(Integer currentTimeGroupId, BatchCreateEventMaskDTO dto) {
        if (CollUtil.isEmpty(dto.getIds())) {
            return;
        }
        //在新增
        List<Integer> timeGroupIds = new ArrayList<>(dto.getIds().size());
        for (int i = 0; i < dto.getIds().size(); i++) {
            timeGroupIds.add(currentTimeGroupId++);
        }
        generateTimePeriodMask(timeGroupIds, dto.getTimeGroupSpans());
    }
}
