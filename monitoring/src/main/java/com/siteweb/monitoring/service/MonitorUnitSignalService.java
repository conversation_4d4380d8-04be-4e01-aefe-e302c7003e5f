package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.MonitorUnitSignal;

import java.util.List;

public interface MonitorUnitSignalService {

    void save(MonitorUnitSignal monitorUnitSignal);

    MonitorUnitSignal findMonitorUnitSignal(Integer stationId, Integer equipmentId, Integer signalId);

    List<Integer> findSignalIdByEquipmentId(Integer equipmentId);

    void deleteByEquipmentId(Integer equipmentId);
    void updateExpressionByEquipmentIdAndSignalId(Integer equipmentId,Integer signalId,String expression);
    void create(MonitorUnitSignal monitorUnitSignal);
    void expressionHandler(Integer sourceTemplateId, Integer equipmentId, Integer signalId, String expression);
}
