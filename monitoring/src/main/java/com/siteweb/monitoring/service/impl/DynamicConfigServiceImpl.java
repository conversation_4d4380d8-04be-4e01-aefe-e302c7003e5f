package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;
import com.siteweb.common.redis.dto.PubSubMessage;
import com.siteweb.common.redis.enums.MessageTypeEnum;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.ConfigSignalDTO;
import com.siteweb.monitoring.entity.DynamicConfig;
import com.siteweb.monitoring.enumeration.DynamicConfigObjectType;
import com.siteweb.monitoring.enumeration.DynamicConfigOperation;
import com.siteweb.monitoring.enumeration.MonitorValueType;
import com.siteweb.monitoring.kafka.KafkaUtil;
import com.siteweb.monitoring.kafka.enums.KafkaTopicEnum;
import com.siteweb.monitoring.mamager.RealtimeRoutingManager;
import com.siteweb.monitoring.mapper.DynamicConfigMapper;
import com.siteweb.monitoring.model.DynamicConfigAttribute;
import com.siteweb.monitoring.model.DynamicConfigRequest;
import com.siteweb.monitoring.model.DynamicConfigRequestItem;
import com.siteweb.monitoring.model.DynamicValue;
import com.siteweb.monitoring.service.DynamicConfigService;
import com.siteweb.monitoring.service.MUSyncTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> zhou
 * @description dynamicConfigServiceImpl
 * @createTime 2022-06-13 13:21:12
 */
@Service
public class DynamicConfigServiceImpl implements DynamicConfigService {

    /**
     * 动态配置通道
     */
    private static final String DYNAMIC_CONFIG_REQUEST_CHANNEL = "DynamicConfigRequest";
    private final Logger log = LoggerFactory.getLogger(DynamicConfigServiceImpl.class);

    @Autowired
    DynamicConfigMapper dynamicConfigMapper;
    @Autowired
    MUSyncTaskService muSyncTaskService;
    @Autowired
    RealtimeRoutingManager realtimeRoutingManager;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired(required = false)
    KafkaUtil kafkaUtil;

    @Override
    public void distributeConfigSignal(int userId, int hostId, ConfigSignalDTO configSignalDTO, Map<String, Object> changedHashMap) {
        DynamicConfigRequest dynamicConfigRequest = createDynamicConfig(configSignalDTO, changedHashMap);
        if (!dynamicConfigRequest.getItems().isEmpty()) {
            DynamicConfig dynamicConfig = new DynamicConfig();
            dynamicConfig.setUserId(userId);
            dynamicConfig.setStationId(configSignalDTO.getStationId());
            dynamicConfig.setHostId(hostId);
            dynamicConfig.setConfigTime(new Date());
            dynamicConfig.setSyncXml("");
            XmlMapper xmlMapper = new XmlMapper();
            dynamicConfigMapper.insert(dynamicConfig);
            xmlMapper.configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true);
            xmlMapper.enable(SerializationFeature.INDENT_OUTPUT);
            dynamicConfigRequest.setSequencecId(dynamicConfig.getId());
            try {
                dynamicConfig.setSyncXml(xmlMapper.writeValueAsString(dynamicConfigRequest));
            } catch (JsonProcessingException e) {
                log.error("Xml serialize failed.", e);
                return;
            }
            dynamicConfigMapper.update(null, Wrappers.lambdaUpdate(DynamicConfig.class)
                                                     .set(DynamicConfig::getSyncXml, dynamicConfig.getSyncXml())
                                                     .eq(DynamicConfig::getId, dynamicConfig.getId()));
            sendDynamicConfigToChannel(dynamicConfig);
        }
    }

    /**
     * 将动态配置发送到redis通道
     *
     * @param dynamicConfig 动态配置
     */
    private void sendDynamicConfigToChannel(DynamicConfig dynamicConfig) {
        Integer dataServerId = realtimeRoutingManager.getDataServerId(dynamicConfig.getHostId());
        if (Objects.isNull(dataServerId)) {
            return;
        }
        PubSubMessage pubSubMessage = PubSubMessage.builder()
                                                   .hostId(dataServerId)
                                                   .desHostId(dynamicConfig.getHostId())
                                                   .messageType(MessageTypeEnum.DYNAMIC_CONFIG_REQUEST.getValue())
                                                   .messageBody(dynamicConfig.getSyncXml())
                                                   .build();
        //kafka不为空优先发送kafka,其次才是redis
        if (kafkaUtil != null) {
            kafkaUtil.sendDynamicMsg(KafkaTopicEnum.DYNAMIC_CONFIG_REQUEST.getTopic(), dynamicConfig.getStationId() / 1000000, pubSubMessage.pubSubMessage());
            return;
        }
        stringRedisTemplate.convertAndSend(DYNAMIC_CONFIG_REQUEST_CHANNEL, pubSubMessage.pubSubMessage());
    }

    @Override
    public void distributeConfigEvent(int userId, int hostId, ConfigEventDTO configEventDTO, Map<String, Object> eventChangedHashMap, Map<Integer, Map<String, Object>> addedConditionHashMap, Map<Integer, Map<String, Object>> updatedConditionHashMap, List<Integer> deletedConditionList) {
        DynamicConfigRequest dynamicConfigRequest = createDynamicConfig(configEventDTO, eventChangedHashMap, addedConditionHashMap, updatedConditionHashMap, deletedConditionList);
        if (!dynamicConfigRequest.getItems().isEmpty()) {
            DynamicConfig dynamicConfig = new DynamicConfig();
            dynamicConfig.setUserId(userId);
            dynamicConfig.setStationId(configEventDTO.getStationId());
            dynamicConfig.setHostId(hostId);
            dynamicConfig.setConfigTime(new Date());
            dynamicConfig.setSyncXml("");
            dynamicConfigMapper.insert(dynamicConfig);
            XmlMapper xmlMapper = new XmlMapper();
            xmlMapper.configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true);
            xmlMapper.enable(SerializationFeature.INDENT_OUTPUT);
            dynamicConfigRequest.setSequencecId(dynamicConfig.getId());
            try {
                dynamicConfig.setSyncXml(xmlMapper.writeValueAsString(dynamicConfigRequest));
            } catch (JsonProcessingException e) {
                log.error("Xml serialize failed.", e);
                return;
            }
            dynamicConfigMapper.update(null, Wrappers.lambdaUpdate(DynamicConfig.class)
                                                     .set(DynamicConfig::getSyncXml, dynamicConfig.getSyncXml())
                                                     .eq(DynamicConfig::getId, dynamicConfig.getId()));
            sendDynamicConfigToChannel(dynamicConfig);
        }
    }

    @Override
    public void generateMUSyncPlan(Integer stationId, Integer monitorUnitId, Date planTime) {
        muSyncTaskService.generateMUSyncPlan(stationId, monitorUnitId, planTime);
    }

    DynamicConfigRequest createDynamicConfig(ConfigSignalDTO configSignalDTO, Map<String, Object> changedHashMap) {
        DynamicConfigRequest dynamicConfigRequest = new DynamicConfigRequest();
        dynamicConfigRequest.setConfigTime(new Date());
        dynamicConfigRequest.setMajorVersion("01234567890123456789012345678901");
        dynamicConfigRequest.setObjectType(DynamicConfigObjectType.Singnal);
        dynamicConfigRequest.setObjectId(configSignalDTO.getSignalId());
        dynamicConfigRequest.setStationId(configSignalDTO.getStationId());
        dynamicConfigRequest.setRdn(String.format("EquipmentId=%d", configSignalDTO.getEquipmentId()));
        List<DynamicConfigRequestItem> dynamicConfigRequestItems = new ArrayList<>();
        // 修改信号属性
        DynamicConfigRequestItem item = modifyAttribute(configSignalDTO.getSignalId(), changedHashMap, DynamicConfigObjectType.Singnal);
        if (item != null) {
            dynamicConfigRequestItems.add(item);
        }
        dynamicConfigRequest.setItems(dynamicConfigRequestItems);
        return dynamicConfigRequest;
    }

    DynamicConfigRequest createDynamicConfig(ConfigEventDTO configEventDTO, Map<String, Object> eventChangedHashMap, Map<Integer, Map<String, Object>> addedConditionHashMap, Map<Integer, Map<String, Object>> updatedConditionHashMap, List<Integer> deletedConditionList) {
        DynamicConfigRequest dynamicConfigRequest = new DynamicConfigRequest();
        dynamicConfigRequest.setConfigTime(new Date());
        dynamicConfigRequest.setMajorVersion("01234567890123456789012345678901");
        dynamicConfigRequest.setObjectType(DynamicConfigObjectType.Event);
        dynamicConfigRequest.setObjectId(configEventDTO.getEventId());
        dynamicConfigRequest.setStationId(configEventDTO.getStationId());
        dynamicConfigRequest.setRdn(String.format("EquipmentId=%d", configEventDTO.getEquipmentId()));
        List<DynamicConfigRequestItem> dynamicConfigRequestItems = new ArrayList<>();
        //修改事件属性
        DynamicConfigRequestItem item = modifyAttribute(configEventDTO.getEventId(), eventChangedHashMap, DynamicConfigObjectType.Event);
        if (item != null) {
            dynamicConfigRequestItems.add(item);
        }
        //增加事件条件
        for (Map.Entry<Integer, Map<String, Object>> mapEntry : addedConditionHashMap.entrySet()) {
            DynamicConfigRequestItem eventConditionItem = createEventConditionDynamicConfig(mapEntry.getKey(), mapEntry.getValue(), DynamicConfigOperation.Add);
            dynamicConfigRequestItems.add(eventConditionItem);
        }
        //修改事件条件
        for (Map.Entry<Integer, Map<String, Object>> mapEntry : updatedConditionHashMap.entrySet()) {
            DynamicConfigRequestItem eventConditionItem = createEventConditionDynamicConfig(mapEntry.getKey(), mapEntry.getValue(), DynamicConfigOperation.Modify);
            dynamicConfigRequestItems.add(eventConditionItem);
        }
        //删除事件条件
        for (Integer eventConditionId : deletedConditionList) {
            DynamicConfigRequestItem eventConditionItem = createEventConditionDynamicConfig(eventConditionId, null, DynamicConfigOperation.Delete);
            dynamicConfigRequestItems.add(eventConditionItem);
        }
        dynamicConfigRequest.setItems(dynamicConfigRequestItems);
        return dynamicConfigRequest;
    }

    DynamicConfigRequestItem createEventConditionDynamicConfig(Integer eventConditionId, Map<String, Object> attributeHashMap, DynamicConfigOperation op) {
        DynamicConfigRequestItem item = new DynamicConfigRequestItem();
        item.setChildType(DynamicConfigObjectType.Condition);
        item.setChildId(eventConditionId);
        item.setOp(op);
        if (op == DynamicConfigOperation.Add || op == DynamicConfigOperation.Modify) {
            List<DynamicConfigAttribute> attrsList = new ArrayList<>();
            for (Map.Entry<String, Object> mapEntry : attributeHashMap.entrySet()) {
                DynamicConfigAttribute attribute = createAttribute(mapEntry.getKey(), mapEntry.getValue());
                attrsList.add(attribute);
            }
            item.setAttrs(attrsList);
        }
        return item;
    }

    DynamicConfigRequestItem modifyAttribute(int objectId, Map<String, Object> attrs, DynamicConfigObjectType type) {
        DynamicConfigRequestItem item = new DynamicConfigRequestItem();
        item.setChildType(type);
        item.setChildId(objectId);
        item.setOp(DynamicConfigOperation.Modify);
        List<DynamicConfigAttribute> attrsList = new ArrayList<>();
        for (Map.Entry<String, Object> attr : attrs.entrySet()) {
            DynamicConfigAttribute dynamicConfigAttribute = createAttribute(attr.getKey(), attr.getValue());
            attrsList.add(dynamicConfigAttribute);
        }
        item.setAttrs(attrsList);
        return item;
    }

    DynamicConfigAttribute createAttribute(String name, Object value) {
        DynamicConfigAttribute attr = new DynamicConfigAttribute();
        attr.setName(name);
        attr.setValue(createDynamicValue(value));
        return attr;
    }

    DynamicValue createDynamicValue(Object value) {
        DynamicValue result;
        if (value instanceof Integer i) {
            result = new DynamicValue(MonitorValueType.Integer, i);
        } else if (value instanceof String str) {
            result = new DynamicValue(MonitorValueType.String, str);
        } else if (value instanceof Double d) {
            result = new DynamicValue(MonitorValueType.Float, d.floatValue());
        } else {
            result = null;
        }
        return result;
    }
}
