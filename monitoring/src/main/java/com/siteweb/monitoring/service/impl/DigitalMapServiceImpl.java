package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.monitoring.dto.StationTreeNode;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.DigitalMapService;
import com.siteweb.monitoring.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("DigitalMapService")
public class DigitalMapServiceImpl  implements DigitalMapService {
    @Autowired
    ResourceStructureServiceImpl resourceStructureService;
    @Autowired
    StationService stationService;

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    ResourceStructureManager resourceStructureManager;


    /**
     * 获取基站树
     * @param userId
     * @return
     */
    @Override
    public StationTreeNode getStationTreeNodes(Integer userId, String resourceStructureIds) {
        List<Integer> sourceTypeList = List.of(SourceType.STATION.value(), SourceType.DISTRICT.value(), SourceType.SSCENTER.value(), SourceType.SCCENTER.value(), SourceType.PARK.value());
        List<ResourceStructure> resourceStructureList = resourceStructureService.findByObjectType(sourceTypeList);
        //获取局站最大告警等级 从activeEventManager
        Map<Integer, Integer> alarmMap = activeEventManager.getStationAlarmState();
        List<Station> stations  = stationService.findByUserId(userId);
        //获取电信场景用户的权限
        Map<Integer,Station> stationIdSet = stations.stream().collect(Collectors.toMap(Station::getStationId, o->o));

        if (CharSequenceUtil.isNotEmpty(resourceStructureIds)) {
            Set<Integer> collect = Arrays.stream(resourceStructureIds.split(",")).map(Integer::valueOf).collect(Collectors.toSet());
            Set<Integer> allChildrenId = resourceStructureManager.getAllChildrenId(collect);
            collect.forEach(e -> allChildrenId.addAll(resourceStructureManager.findAllParentStructureListById(e).stream().map(ResourceStructure::getResourceStructureId).collect(Collectors.toSet())));
            resourceStructureList = resourceStructureManager.getResourceStructureByIds(allChildrenId);
            Set<Integer> resourceStationId = resourceStructureList.stream().map(ResourceStructure::getOriginId).collect(Collectors.toSet());
            stationIdSet = stationIdSet.entrySet().stream().filter(e -> resourceStationId.contains(e.getKey())).collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
        }

        //转为局站节点
        int rootId = 0;
        LinkedHashMap<Integer, StationTreeNode> resourceStructureTreeDTOLinkedHashMap = new LinkedHashMap<>();
        for (ResourceStructure treeDTO : resourceStructureList) {
            //是局站但是没有权限
            if (treeDTO.getStructureTypeId() == 104 && !stationIdSet.containsKey(treeDTO.getOriginId())) {
                continue;
            }
            StationTreeNode stationTreeNode = new StationTreeNode(treeDTO);
            if (treeDTO.getStructureTypeId() == 104) {
                Station station = stationIdSet.get(treeDTO.getOriginId());
                if (station != null) {
                    stationTreeNode.setConnectState(station.getConnectState());
                    stationTreeNode.setProjectState(station.getStationState());
                    stationTreeNode.setLatitude(station.getLatitude());
                    stationTreeNode.setLongitude(station.getLongitude());
                    stationTreeNode.setStationId(station.getStationId());
                    stationTreeNode.setMaxEventSeverity(alarmMap.get(treeDTO.getOriginId()));
                }
            } else {
                JsonNode extendedField = treeDTO.getExtendedField();
                if (extendedField != null) {
                    if (extendedField.get("longitude") != null) {
                        stationTreeNode.setLongitude(extendedField.get("longitude").asDouble());
                    }
                    if (extendedField.get("latitude") != null) {
                        stationTreeNode.setLongitude(extendedField.get("latitude").asDouble());
                    }
                }
            }
            resourceStructureTreeDTOLinkedHashMap.put(stationTreeNode.getId(), stationTreeNode);
            if (treeDTO.getParentResourceStructureId() == 0) {
                rootId = treeDTO.getResourceStructureId();
            }
        }
        //构建局站树
        for (StationTreeNode resourceStructure : resourceStructureTreeDTOLinkedHashMap.values()) {
            if (resourceStructure.getParentId() != 0 && resourceStructureTreeDTOLinkedHashMap.containsKey(resourceStructure.getParentId())) {
                StationTreeNode param = resourceStructureTreeDTOLinkedHashMap.get(resourceStructure.getParentId());
                param.getChildren().add(resourceStructure);
            }
        }

        return resourceStructureTreeDTOLinkedHashMap.get(rootId);
    }
}
