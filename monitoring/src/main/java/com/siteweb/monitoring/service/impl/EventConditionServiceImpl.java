package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.EquipmentEventConditionDTO;
import com.siteweb.monitoring.dto.EvenConditionDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.entity.EventCondition;
import com.siteweb.monitoring.mapper.EventConditionMapper;
import com.siteweb.monitoring.service.EventConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("eventConditionService")
public class EventConditionServiceImpl implements EventConditionService {

    @Autowired
    EventConditionMapper eventConditionMapper;

    @Override
    public List<EventConditionDTO> findEventConditionDTOByEventId(int equipmentId, int eventId) {
        return eventConditionMapper.findEventConditionDTOByEventId(equipmentId, eventId);
    }

    @Override
    public List<EventConditionDTO> findEventConditionDTOBySignalId(int equipmentId, int signalId) {
        return eventConditionMapper.findEventConditionDTOBySignalId(equipmentId, signalId);
    }

    @Override
    public List<EquipmentEventConditionDTO> findEventConditionByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds) {
        if (CollUtil.isEmpty(eventIds)) {
            return new ArrayList<>();
        }
        return eventConditionMapper.findEventConditionByEquipmentIdAndEventIds(equipmentId, eventIds);
    }

    @Override
    public List<EventCondition> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventConditionMapper.selectList(Wrappers.lambdaQuery(EventCondition.class)
                                                       .eq(EventCondition::getEquipmentTemplateId,
                                                               equipmentTemplateId));
    }

    @Override
    public void batchInsert(List<EventCondition> eventConditionList) {
        if (CollUtil.isEmpty(eventConditionList)) {
            return;
        }
        eventConditionMapper.batchInsert(eventConditionList);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        eventConditionMapper.delete(Wrappers.<EventCondition>lambdaQuery()
                                            .eq(EventCondition::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public List<EvenConditionDTO> findEventConditionByEquipmentId(Integer equipmentId) {
        return eventConditionMapper.findEventConditionByEquipmentId(equipmentId);
    }

    @Override
    public EventCondition findByEquipmentIdAndEventIdAndEventConditionId(Integer equipmentId, Integer eventId, Integer eventConditionId) {
        return eventConditionMapper.findByEquipmentIdAndEventIdAndEventConditionId(equipmentId,eventId,eventConditionId);
    }
}
