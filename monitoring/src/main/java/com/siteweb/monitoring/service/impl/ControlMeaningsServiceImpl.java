package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.ControlMeaningsDTO;
import com.siteweb.monitoring.entity.ControlMeanings;
import com.siteweb.monitoring.mapper.ControlMeaningsMapper;
import com.siteweb.monitoring.service.ControlMeaningsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service("controlMeaningsService")
public class ControlMeaningsServiceImpl implements ControlMeaningsService {

    @Autowired
    ControlMeaningsMapper controlMeaningsMapper;

    @Override
    public HashMap<Integer, List<ControlMeanings>> getControlMeaningsByEquipmentId(Integer equipmentId) {
        HashMap<Integer, List<ControlMeanings>> result = new HashMap<>();
        List<ControlMeanings> css = controlMeaningsMapper.getControlMeaningsByEquipmentId(equipmentId);
        for (ControlMeanings controlMeanings : css) {
            if (result.containsKey(controlMeanings.getControlId())) {
                result.get(controlMeanings.getControlId()).add(controlMeanings);
            } else {
                List<ControlMeanings> ls = new ArrayList<>();
                ls.add(controlMeanings);
                result.put(controlMeanings.getControlId(), ls);
            }
        }
        return result;
    }

    @Override
    public List<ControlMeaningsDTO> findControlMeaningsDTOsByControlId(Integer equipmentId, Integer controlId) {
        return controlMeaningsMapper.findControlMeaningsDTOsByControlId(equipmentId, controlId);
    }

    @Override
    public List<ControlMeanings> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return controlMeaningsMapper.selectList(Wrappers.lambdaQuery(ControlMeanings.class)
                                                        .eq(ControlMeanings::getEquipmentTemplateId, equipmentTemplateId));

    }

    @Override
    public void batchInsert(List<ControlMeanings> controlMeaningsList) {
        if (CollUtil.isEmpty(controlMeaningsList)) {
            return;
        }
        controlMeaningsMapper.batchInsert(controlMeaningsList);
    }
}
