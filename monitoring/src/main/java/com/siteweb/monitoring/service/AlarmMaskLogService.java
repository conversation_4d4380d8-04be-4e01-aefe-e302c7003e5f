package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.entity.EquipmentMask;
import com.siteweb.monitoring.entity.EventMask;
import com.siteweb.monitoring.entity.StationMask;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/5/25 19:14
 */
public interface AlarmMaskLogService {
    /**
     * 批量添加屏蔽日志
     * @param vo
     * @param userId
     */
    void batchInsertEquipmentMaskLog(BatchEquipmentMaskVO vo, Integer userId);

    /**
     * 批量记录解除局站屏蔽记录
     * @param userId 用户id
     * @param stationMaskList 局站屏蔽信息
     */
    void stationMaskCancelLog(Integer userId, List<StationMask> stationMaskList);

    void batchInsertStationMaskLog(BatchSetStationMaskDTO dto, Integer userId);

    /**
     * 批量记录解除设备屏蔽记录
     * @param userId 用户id
     * @param equipmentMaskList 设备ids
     */
    void equipmentMaskCancelLog(Integer userId, List<EquipmentMask> equipmentMaskList);

    void eventMaskCancelLog(List<EventMask> eventMaskList);

    /**
     * 批量添加告警屏蔽日志
     * @param batchCreateEventMaskDTO 批量插入的告警屏蔽信息
     * @param userId 用户id
     */
    void batchInsertEventMaskLog(BatchCreateEventMaskDTO batchCreateEventMaskDTO, Integer userId);
}
