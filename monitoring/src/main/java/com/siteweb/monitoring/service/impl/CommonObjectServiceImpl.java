package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.entity.CommonObject;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mapper.CommonObjectMapper;
import com.siteweb.monitoring.service.CommonObjectService;
import com.siteweb.utility.entity.DiskFileOperationType;
import com.siteweb.utility.service.DiskFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class CommonObjectServiceImpl implements CommonObjectService {
    @Autowired
    private CommonObjectMapper commonObjectMapper;
    @Autowired
    ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    DiskFileService diskFileService;

    @Override
    public List<CommonObject> findAll() {
        return commonObjectMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public CommonObject findById(Integer id) {
        return commonObjectMapper.selectById(id);
    }

    @Override
    public CommonObject create(CommonObject commonObject) {
        int result = commonObjectMapper.insert(commonObject);
        return result > 0 ? commonObject : null;
    }

    @Override
    public Boolean deleteById(Integer id) {
        CommonObject commonObject = findById(id);
        if (Objects.isNull(commonObject)) {
            return false;
        }
        int result = commonObjectMapper.deleteById(id);
        applicationEventPublisher.publishEvent(BaseSpringEvent.of(new ResourceObject(id, SourceType.COMMONOBJECT.value())));
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonObject update(CommonObject commonObject) {
        //删除旧图片
        this.removeOldPhoto(commonObject.getCommonObjectId(), commonObject.getPhoto());
        int result = commonObjectMapper.updateById(commonObject);
        return result > 0 ? commonObject : null;
    }

    /**
     * 删除磁盘上的旧图片
     *
     * @param id       通用对象id
     * @param newPhoto 新图片
     */
    private void removeOldPhoto(Integer id, String newPhoto) {
        CommonObject commonObject = this.findById(id);
        if (ObjectUtil.isNull(commonObject)) {
            return;
        }
        //无旧图片或者新旧图片相同不处理
        if (CharSequenceUtil.isBlank(commonObject.getPhoto()) || CharSequenceUtil.equals(newPhoto, commonObject.getPhoto())) {
            return;
        }
        //删除旧文件
        diskFileService.updateDiskFile(commonObject.getPhoto(), DiskFileOperationType.CLEAN_OLD_DISKFILE);
    }

    public boolean exists(Integer id) {
        return commonObjectMapper.exists(Wrappers.<CommonObject>lambdaQuery()
                                                 .eq(CommonObject::getCommonObjectId, id));
    }
}
