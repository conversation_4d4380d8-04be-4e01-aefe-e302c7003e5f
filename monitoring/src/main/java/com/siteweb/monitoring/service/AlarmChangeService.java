package com.siteweb.monitoring.service;

import com.siteweb.monitoring.entity.AlarmChange;

import java.util.List;

public interface AlarmChangeService{

    List<AlarmChange> findAlarmChanges();

    int createAlarmChange(AlarmChange alarmChange);

    int deleteById(Long alarmChangeId);

    int updateAlarmChange(AlarmChange alarmChange);

    AlarmChange findById(Long alarmChangeId);
    void batchInsert(List<AlarmChange> batchInsertAlarmChangeList);
}