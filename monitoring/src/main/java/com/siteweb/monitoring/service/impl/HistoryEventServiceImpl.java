package com.siteweb.monitoring.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.dto.HistoryEventDTO;
import com.siteweb.monitoring.dto.HistoryEventFilterGroupDTO;
import com.siteweb.monitoring.dto.HistoryEventPageDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.entity.StandardDicEvent;
import com.siteweb.monitoring.mamager.ConfigEventManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StandardDicEventManager;
import com.siteweb.monitoring.mapper.HistoryEventMapper;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.HistoryEventService;
import com.siteweb.monitoring.vo.HisPowOffCountReqByStationId;
import com.siteweb.monitoring.vo.HistoryEventFilterVO;
import com.siteweb.monitoring.vo.HistoryPowerOffCountVO;
import com.siteweb.utility.service.StandardVerService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("historyEventService")
public class HistoryEventServiceImpl implements HistoryEventService {

    @Autowired
    HistoryEventMapper historyEventMapper;
    @Lazy
    @Autowired
    ActiveEventService activeEventService;

    @Autowired
    RegionService regionService;

    @Autowired
    RegionMapService regionMapService;

    @Autowired
    EquipmentService equipmentService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    StandardDicEventManager standardDicEventManager;
    @Autowired
    StandardVerService standardVerService;
    @Autowired
    ConfigEventManager configEventManager;
    @Autowired
    SystemConfigService systemConfigService;
    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    private Boolean eventLevelFilterEnable;

    @Override
    public List<HistoryEvent> findByBaseEquipmentIdAndStartTimeSpan(Integer baseEquipmentId, Date startDate, Date endDate) {
        return historyEventMapper.findByBaseEquipmentIdAndStartTimeSpan(baseEquipmentId, startDate, endDate);
    }

    @Override
    public List<HistoryEvent> findByEquipmentIdAndStartTimeSpan(Integer equipmentId, Date startDate, Date endDate) {
        return historyEventMapper.findByEquipmentIdAndStartTimeSpan(equipmentId, startDate, endDate);
    }

    @Override
    public List<HistoryEvent> findByEventIdAndStartTimeSpan(Integer eventId, Date startDate, Date endDate) {
        return historyEventMapper.findByEventIdAndStartTimeSpan(eventId, startDate, endDate);
    }

    @Override
    public List<HistoryEvent> findByStartTimeSpan(Date startDate, Date endDate) {
        return historyEventMapper.findByStartTimeSpan(startDate, endDate);
    }
    @Override
    public List<Integer> listAlertEquipmentsByTimeSpan(Date startDate, Date endDate) {
        return historyEventMapper.listAlertEquipmentsByTimeSpan(startDate, endDate);
    }

    @Override
    public List<HistoryEvent> findByUserId(List<HistoryEvent> historyEvents, Integer userId) {
        if (null == userId) {
            return historyEvents;
        }
        List<Region> regions = regionService.findAllRegionsByUserId(userId);
        //如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (regions.stream().anyMatch(o -> o.getRegionId().equals(-1))) {
            return historyEvents;
        }
        List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
        List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);
        //EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
        Set<Integer> resourceStructureIds = regionMaps.stream().filter(o -> o.getEquipmentId().equals(-1)).map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        Set<Integer> equipmentIds = regionMaps.stream().filter(o -> o.getEquipmentId() > 0).map(RegionMap::getEquipmentId).collect(Collectors.toSet());
        return historyEvents.stream().filter(o -> resourceStructureIds.contains(o.getResourceStructureId()) || equipmentIds.contains(o.getEquipmentId())).collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取权限过滤后的设备ID和资源结构ID
     * @param userId 用户ID
     * @return 权限过滤结果，包含设备ID集合和资源结构ID集合
     */
    private PermissionFilterResult getPermissionFilterResult(Integer userId) {
        if (null == userId) {
            return new PermissionFilterResult(Collections.emptySet(), Collections.emptySet(), false);
        }

        List<Region> regions = regionService.findAllRegionsByUserId(userId);
        //如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (regions.stream().anyMatch(o -> o.getRegionId().equals(-1))) {
            return new PermissionFilterResult(Collections.emptySet(), Collections.emptySet(), true);
        }

        List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
        List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);
        //EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
        Set<Integer> resourceStructureIds = regionMaps.stream()
                .filter(o -> o.getEquipmentId().equals(-1))
                .map(RegionMap::getResourceStructureId)
                .collect(Collectors.toSet());
        Set<Integer> equipmentIds = regionMaps.stream()
                .filter(o -> o.getEquipmentId() > 0)
                .map(RegionMap::getEquipmentId)
                .collect(Collectors.toSet());

        return new PermissionFilterResult(equipmentIds, resourceStructureIds, false);
    }

    /**
     * 权限过滤结果内部类
     */
    private static class PermissionFilterResult {
        private final Set<Integer> equipmentIds;
        private final Set<Integer> resourceStructureIds;
        private final boolean hasAllPermission;

        public PermissionFilterResult(Set<Integer> equipmentIds, Set<Integer> resourceStructureIds, boolean hasAllPermission) {
            this.equipmentIds = equipmentIds;
            this.resourceStructureIds = resourceStructureIds;
            this.hasAllPermission = hasAllPermission;
        }

        public Set<Integer> getEquipmentIds() {
            return hasAllPermission ? Collections.emptySet() : equipmentIds;
        }

        public Set<Integer> getResourceStructureIds() {
            return hasAllPermission ? Collections.emptySet() : resourceStructureIds;
        }

        public boolean hasAllPermission() {
            return hasAllPermission;
        }
    }

    @Override
    public Map<Integer, Integer> groupHistoryEventsBySeverity(Date startDate, Date endDate, Integer userId) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (equipmentIds == null || equipmentIds.isEmpty()) {
            return new HashMap<>();
        }
        List<Map<Integer, Integer>> groupList = historyEventMapper.groupHistoryEventsBySeverity(startDate, endDate, equipmentIds);
        if (groupList.isEmpty()) {
            return new HashMap<>();
        }
        Map<Integer, Integer> result = new HashMap<>();
        for (Map<Integer, Integer> groupMap : groupList) {
            result.putAll(groupMap);
        }
        return result;
    }

    @Override
    public HistoryEvent findByStartTimeAndEquipmentIdAndEventIdAndConditionId(Integer equipmentId, Integer eventId, Date startTime, Integer eventConditionId, Integer stationId) {
        return historyEventMapper.findByStartTimeAndEquipmentIdAndEventIdAndConditionId(equipmentId, eventId, startTime, eventConditionId, stationId);
    }

    @Override
    public List<HistoryEventDTO> findHistoryEventDTOByEquipmentIdAndStartTimeSpan(Integer equipmentId, Date startDate, Date endDate) {
        List<HistoryEventDTO> historyEventDTOS = new ArrayList<>();
        List<HistoryEvent> historyEventList = historyEventMapper.findByEquipmentIdAndStartTimeSpan(equipmentId, startDate, endDate);
        for (HistoryEvent historyEvent : historyEventList) {
            historyEventDTOS.add(new HistoryEventDTO(historyEvent));
        }
        return historyEventDTOS;
    }


    @Override
    public String findAlarmDurationByEquipmentIdAndEventId(Integer equipmentId, Integer eventId, Date startTime, Date endTime) {
        List<HistoryEvent> historyEventList = historyEventMapper.findAlarmDurationByEquipmentIdAndEventId(equipmentId, eventId, startTime, endTime);
        if (CollUtil.isEmpty(historyEventList)) {
            return "0:0";
        }
        int alarmDurationMinute = historyEventList.stream()
                .map(event -> DateUtil.between(event.getStartTime(), event.getEndTime(), DateUnit.MINUTE))
                .mapToInt(Long::intValue)
                .sum();
        //计算小时与分钟
        int hour = alarmDurationMinute / 60;
        int minute = alarmDurationMinute % 60;
        return hour + ":" + minute;
    }

    @Override
    public Integer findAlarmCountByEquipmentIdAndEventId(Integer equipmentId, Integer eventId, Date startTime, Date endTime) {
        return historyEventMapper.findAlarmCountByEquipmentIdAndEventId(equipmentId, eventId, startTime, endTime);
    }

    @Override
    public List<HistoryEvent> findDurationByStationIdsAndEventCategoryId(List<Integer> stationIds, Integer eventCategoryId, Date startTime, Date endTime) {
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptyList();
        }
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        return historyEventMapper.findDurationByStationIdsAndEventCategoryId(stationIds, eventCategoryId, startTime, endTime, filterByEventLevel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertMidHistoryEvent(String sequenceId, Date endTime, Date confirmTime, Integer confirmerId, String confirmerName, String note) {
        ActiveEvent activeEvent = activeEventService.getDBActiveEventBySequenceId(sequenceId);
        if (ObjectUtil.isNotEmpty(activeEvent)) {
            HistoryEvent historyEvent = BeanUtil.toBean(activeEvent, HistoryEvent.class);
            historyEvent.setEndTime(endTime);
            historyEvent.setConfirmTime(confirmTime);
            historyEvent.setConfirmerId(confirmerId);
            historyEvent.setConfirmerName(confirmerName);
            historyEvent.setDescription(note);
            if (!existsHistoryEvent(activeEvent.getStationId(), activeEvent.getEquipmentId(), activeEvent.getStartTime(), activeEvent.getEventId(), activeEvent.getEventConditionId())) {
                historyEventMapper.insert(historyEvent);
            }
        }
        activeEventService.removeBySequenceId(sequenceId);
    }

    /**
     * 是否已经存在历史告警
     *
     * @return boolean
     */
    private boolean existsHistoryEvent(Integer stationId, Integer equipmentId, Date startTime, Integer eventId, Integer eventConditionId) {
        return historyEventMapper.exists(Wrappers.lambdaQuery(HistoryEvent.class)
                .eq(HistoryEvent::getStationId, stationId)
                .eq(HistoryEvent::getEquipmentId, equipmentId)
                .eq(HistoryEvent::getStartTime, startTime)
                .eq(HistoryEvent::getEventId, eventId)
                .eq(HistoryEvent::getEventConditionId, eventConditionId));
    }

    @Override
    public List<HistoryEvent> findDurationByResourceStructureIdsAndEventCategoryId(List<Integer> resourceStructureIds, Integer eventCategoryId, Date startTime, Date endTime) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        return historyEventMapper.findDurationByResourceStructureIdsAndEventCategoryId(resourceStructureIds, eventCategoryId, startTime, endTime, filterByEventLevel);
    }

    @Override
    public Boolean isExistAlarmByEquipmentIdAndTime(Integer equipmentId, Date time) {
        List<HistoryEvent> existAlarmByEquipmentIdAndTime = historyEventMapper.isExistAlarmByEquipmentIdAndTime(equipmentId, time);
        return !existAlarmByEquipmentIdAndTime.isEmpty();
    }

    @Override
    public List<HistoryPowerOffCountVO> getPowerOffCountByStationIds(HisPowOffCountReqByStationId reqVO) {
        return historyEventMapper.getPowerOffCountByStationIds(reqVO.getStationIds(), reqVO.getStartTime(), reqVO.getEndTime(), 10);
    }

    @Override
    public List<HistoryPowerOffCountVO> getOilEngineCountByStationIds(HisPowOffCountReqByStationId reqVO) {
        return historyEventMapper.getPowerOffCountByStationIds(reqVO.getStationIds(), reqVO.getStartTime(), reqVO.getEndTime(), 42);
    }

    @Override
    public void batchInsert(List<HistoryEvent> historyEventList) {
        if (CollUtil.isEmpty(historyEventList)) {
            return;
        }
        historyEventMapper.batchInsert(historyEventList);
    }

    @Override
    public List<HistoryEvent> findAlarmHistoryByConditions(Integer equipmentId, Integer eventId, Date startTime, Date endTime) {
        return historyEventMapper.findAlarmDurationByEquipmentIdAndEventId(equipmentId, eventId, startTime, endTime);
    }

    @Override
    public Long countByStartTimeSpan(Date startDate, Date endDate) {
        return 0L;
    }

    // 实现新的统计方法
    @Override
    public Long countHistoryEventsByUserId(Date startTime, Date endTime, Integer userId) {
        PermissionFilterResult filterResult = getPermissionFilterResult(userId);
        if (filterResult.hasAllPermission()) {
            // 如果有全部权限，使用原有的方法
            return historyEventMapper.countByStartTimeSpan(startTime, endTime);
        }
        return historyEventMapper.countHistoryEvents(startTime, endTime,
                filterResult.getEquipmentIds(), filterResult.getResourceStructureIds());
    }

    @Override
    public List<Map<String, Object>> countHistoryEventsByEquipmentAndUserId(Date startTime, Date endTime, Integer userId) {
        PermissionFilterResult filterResult = getPermissionFilterResult(userId);
        return historyEventMapper.countHistoryEventsByEquipment(startTime, endTime,
                filterResult.getEquipmentIds(), filterResult.getResourceStructureIds());
    }

    @Override
    public List<Map<String, Object>> countHistoryEventsByEventLevelAndUserId(Date startTime, Date endTime, Integer userId) {
        PermissionFilterResult filterResult = getPermissionFilterResult(userId);
        return historyEventMapper.countHistoryEventsByEventLevel(startTime, endTime,
                filterResult.getEquipmentIds(), filterResult.getResourceStructureIds());
    }

    @Override
    public List<Map<String, Object>> countHistoryEventsByEquipmentCategoryAndUserId(Date startTime, Date endTime, Integer userId) {
        PermissionFilterResult filterResult = getPermissionFilterResult(userId);
        return historyEventMapper.countHistoryEventsByEquipmentCategory(startTime, endTime,
                filterResult.getEquipmentIds(), filterResult.getResourceStructureIds());
    }

    @Override
    public List<Map<String, Object>> countHistoryEventsByResourceStructureIdAndUserId(Date startTime, Date endTime, Integer userId) {
        PermissionFilterResult filterResult = getPermissionFilterResult(userId);
        return historyEventMapper.countHistoryEventsByResourceStructureId(startTime, endTime,
                filterResult.getEquipmentIds(), filterResult.getResourceStructureIds());
    }

    @Override
    public IPage<HistoryEventPageDTO> queryHistoryEventPage(Integer userId, Pageable pageable, HistoryEventFilterVO historyEventFilterVO) {
        if (userId != null) {
            List<Region> regions = regionService.findAllRegionsByUserId(userId);
            boolean hasAllAccess = regions.stream().anyMatch(o -> o.getRegionId().equals(-1));
            // 如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
            if (!hasAllAccess) {
                List<Integer> regionIds = regions.stream()
                        .map(Region::getRegionId)
                        .toList();

                List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);

                Set<Integer> resourceStructureIds = regionMaps.stream()
                        .filter(o -> o.getEquipmentId().equals(-1))
                        .map(RegionMap::getResourceStructureId)
                        .collect(Collectors.toSet());

                Set<Integer> equipmentIds = regionMaps.stream()
                        .filter(o -> o.getEquipmentId() > 0)
                        .map(RegionMap::getEquipmentId)
                        .collect(Collectors.toSet());

                // 将权限信息塞入 VO 中
                historyEventFilterVO.setPermissionResourceStructureIds(resourceStructureIds);
                historyEventFilterVO.setPermissionEquipmentIds(equipmentIds);
            }
        }
        Sort sort = pageable.getSort();
        Page<Object> page = Page.of(pageable.getPageNumber(), pageable.getPageSize());
        IPage<HistoryEventPageDTO> resultPage = historyEventMapper.queryHistoryEventPage(page, historyEventFilterVO, sort);
        if (resultPage.getTotal() <= 0) {
            return resultPage;
        }
        int standardVer = standardVerService.getStandardVer();
        resultPage.getRecords().forEach(historyEvent -> {
            if (historyEvent.getResourceStructureId() != null) {
                String equipmentPosition = resourceStructureManager.getFullPath(historyEvent.getResourceStructureId());
                historyEvent.setEquipmentPosition(equipmentPosition);
            }
            // 组装标准化相关字段
            if (historyEvent.getStandardAlarmNameId() != null) {
                constructStandardInfo(standardVer, historyEvent);
            }
            // 添加告警触发值字段
            EventConditionDTO eventConditionDTO = configEventManager.findConditionByEquipmentIdAndEventId(historyEvent.getEquipmentId(), historyEvent.getEventId(), historyEvent.getEventConditionId());
            Optional.ofNullable(eventConditionDTO).ifPresent(condition -> historyEvent.setStartCompareValue(condition.getStartCompareValue()));
            historyEvent.setEventDescription(String.format("%s-%s-%s-%s", historyEvent.getEquipmentName(), historyEvent.getEventName(), historyEvent.getMeanings(), historyEvent.getEventValue()));
        });
        return resultPage;
    }

    private void constructStandardInfo(int standardVer, HistoryEventPageDTO dto) {
        StandardDicEvent standardDicEvent = standardDicEventManager.getStandardDicEvent(dto.getStandardAlarmNameId(), standardVer);
        if (null != standardDicEvent) {
            dto.setEquipmentLogicCategory(standardDicEvent.getEquipmentLogicClass());
            dto.setAlarmLogicCategory(standardDicEvent.getEventLogicClass());
            if (standardVer == 0) {
                dto.setStandardTypeName(dto.getBaseTypeName());
            } else {
                dto.setStandardTypeName(dto.getStandardAlarmName());
            }
            dto.setStdSignalDescription(standardDicEvent.getExtendFiled1());
            dto.setStdSignalMeanings(standardDicEvent.getMeanings());
            dto.setStdNote(standardDicEvent.getExtendFiled2());
        }
    }

    @Override
    public Map<Integer, Long> groupHistoryEventBySeverity(Integer userId, HistoryEventFilterVO historyEventFilterVO) {
        if (userId != null) {
            List<Region> regions = regionService.findAllRegionsByUserId(userId);
            boolean hasAllAccess = regions.stream().anyMatch(o -> o.getRegionId().equals(-1));
            // 如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
            if (!hasAllAccess) {
                List<Integer> regionIds = regions.stream()
                        .map(Region::getRegionId)
                        .toList();
                List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);

                Set<Integer> resourceStructureIds = regionMaps.stream()
                        .filter(o -> o.getEquipmentId().equals(-1))
                        .map(RegionMap::getResourceStructureId)
                        .collect(Collectors.toSet());

                Set<Integer> equipmentIds = regionMaps.stream()
                        .filter(o -> o.getEquipmentId() > 0)
                        .map(RegionMap::getEquipmentId)
                        .collect(Collectors.toSet());

                // 将权限信息塞入 VO 中
                historyEventFilterVO.setPermissionResourceStructureIds(resourceStructureIds);
                historyEventFilterVO.setPermissionEquipmentIds(equipmentIds);
            }
        }
        boolean standardAlarmNameIdIsNotNull = systemConfigService.standardAlarmNameIdIsNotNull();
        List<HistoryEventFilterGroupDTO> list = historyEventMapper.groupHistoryEventBySeverity(historyEventFilterVO, standardAlarmNameIdIsNotNull);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream()
                .collect(Collectors.toMap(
                        HistoryEventFilterGroupDTO::getEventLevel,
                        HistoryEventFilterGroupDTO::getCount
                ));
    }
}
