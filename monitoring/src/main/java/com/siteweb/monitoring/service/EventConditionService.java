package com.siteweb.monitoring.service;

import com.siteweb.monitoring.dto.EquipmentEventConditionDTO;
import com.siteweb.monitoring.dto.EvenConditionDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.entity.EventCondition;

import java.util.List;

public interface EventConditionService {

    List<EventConditionDTO> findEventConditionDTOByEventId(int equipmentId, int eventId);

    List<EventConditionDTO> findEventConditionDTOBySignalId(int equipmentId, int signalId);


    List<EquipmentEventConditionDTO> findEventConditionByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds);

    List<EventCondition> findByEquipmentTemplateId(Integer equipmentTemplateId);

    void batchInsert(List<EventCondition> eventConditionList);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);

    List<EvenConditionDTO> findEventConditionByEquipmentId(Integer equipmentId);

    EventCondition findByEquipmentIdAndEventIdAndEventConditionId(Integer equipmentId,Integer eventId,Integer eventConditionId);
}

