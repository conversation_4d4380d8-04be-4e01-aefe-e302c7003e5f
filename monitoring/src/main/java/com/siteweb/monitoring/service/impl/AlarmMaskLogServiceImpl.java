package com.siteweb.monitoring.service.impl;

import com.siteweb.common.util.HexUtil;
import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.dto.EventIdentityDTO;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.MaskOperationTypeEnum;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.AlarmMaskLogMapper;
import com.siteweb.monitoring.service.AlarmMaskLogService;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: lzy
 * @Date: 2022/5/25 19:15
 */
@Service
public class AlarmMaskLogServiceImpl implements AlarmMaskLogService {
    @Autowired
    AlarmMaskLogMapper alarmMaskLogMapper;
    @Autowired
    EquipmentManager equipmentManager;

    @Override
    public void batchInsertEquipmentMaskLog(BatchEquipmentMaskVO vo, Integer userId) {
        List<AlarmMaskLog> alarmMaskLogList = new ArrayList<>();
        String timeGroupChars = getTimeGroupChars(vo.getTimeGroupCategory(),vo.getTimeGroupSpans());
        Date now = new Date();
        boolean isTimeGroupSpan = Objects.equals(vo.getTimeGroupCategory(), 2);
        for (int i = 0; i < vo.getEquipmentIds().size(); i++) {
            Integer equipmentId = vo.getEquipmentIds().get(i);
            Integer stationId = vo.getStationIds().get(i);
            AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
            alarmMaskLog.setStationId(stationId);
            alarmMaskLog.setEquipmentId(equipmentId);
            alarmMaskLog.setEventId(-1);
            alarmMaskLog.setUserId(userId);
            alarmMaskLog.setOperationType(MaskOperationTypeEnum.ADD_MASK_OPERATION.getOperationType());
            alarmMaskLog.setOperationTime(now);
            alarmMaskLog.setTimeGroupCategory(vo.getTimeGroupCategory());
            alarmMaskLog.setStartTime(isTimeGroupSpan ? null : vo.getStartTime());
            alarmMaskLog.setEndTime(isTimeGroupSpan ? null : vo.getEndTime());
            Integer resourceStructureId = Optional.ofNullable(equipmentManager.getEquipmentById(equipmentId))
                                                  .map(Equipment::getResourceStructureId).orElse(null);
            alarmMaskLog.setResourceStructureId(resourceStructureId);
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
            alarmMaskLog.setComment(vo.getReason());
            alarmMaskLogList.add(alarmMaskLog);
        }
        alarmMaskLogMapper.batchInsert(alarmMaskLogList);
    }

    @Override
    public void stationMaskCancelLog(Integer userId, List<StationMask> stationMaskList) {
        List<AlarmMaskLog> alarmMaskLogList = new ArrayList<>();
        Date now = new Date();
        for (StationMask stationMask : stationMaskList) {
            AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
            alarmMaskLog.setStationId(stationMask.getStationId());
            alarmMaskLog.setEquipmentId(-1);
            alarmMaskLog.setEventId(-1);
            alarmMaskLog.setUserId(userId);
            alarmMaskLog.setOperationType(MaskOperationTypeEnum.CANCEL_MAST_OPERATION.getOperationType());
            alarmMaskLog.setOperationTime(now);
            alarmMaskLog.setStartTime(stationMask.getStartTime());
            alarmMaskLog.setEndTime(stationMask.getEndTime());
            alarmMaskLog.setComment(stationMask.getReason());
            alarmMaskLogList.add(alarmMaskLog);
        }
        alarmMaskLogMapper.batchInsert(alarmMaskLogList);
    }

    @Override
    public void batchInsertStationMaskLog(BatchSetStationMaskDTO dto, Integer userId) {
        List<AlarmMaskLog> alarmMaskLogList = new ArrayList<>();
        String timeGroupChars = getTimeGroupChars(dto.getTimeGroupCategory(), dto.getTimeGroupSpans());
        Date now = new Date();
        boolean isTimeGroupSpan = Objects.equals(dto.getTimeGroupCategory(), 2);
        for (int i = 0; i < dto.getStationIdList().size(); i++) {
            Integer stationId = dto.getStationIdList().get(i);
            AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
            alarmMaskLog.setStationId(stationId);
            alarmMaskLog.setEquipmentId(-1);
            alarmMaskLog.setEventId(-1);
            alarmMaskLog.setUserId(userId);
            alarmMaskLog.setOperationType(MaskOperationTypeEnum.ADD_MASK_OPERATION.getOperationType());
            alarmMaskLog.setOperationTime(now);
            alarmMaskLog.setTimeGroupCategory(dto.getTimeGroupCategory());
            alarmMaskLog.setStartTime(isTimeGroupSpan ? null : dto.getStartTime());
            alarmMaskLog.setEndTime(isTimeGroupSpan ? null : dto.getEndTime());
            alarmMaskLog.setResourceStructureId(stationId);
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
            alarmMaskLog.setComment(dto.getReason());
            alarmMaskLogList.add(alarmMaskLog);
        }
        alarmMaskLogMapper.batchInsert(alarmMaskLogList);
    }

    @Override
    public void equipmentMaskCancelLog(Integer userId, List<EquipmentMask> equipmentMaskList) {
        List<AlarmMaskLog> alarmMaskLogList = new ArrayList<>();
        Date now = new Date();
        for (EquipmentMask equipmentMask : equipmentMaskList) {
            AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
            alarmMaskLog.setStationId(equipmentMask.getStationId());
            alarmMaskLog.setEquipmentId(equipmentMask.getEquipmentId());
            alarmMaskLog.setEventId(-1);
            alarmMaskLog.setUserId(userId);
            alarmMaskLog.setOperationType(MaskOperationTypeEnum.CANCEL_MAST_OPERATION.getOperationType());
            alarmMaskLog.setOperationTime(now);
            alarmMaskLog.setStartTime(equipmentMask.getStartTime());
            alarmMaskLog.setEndTime(equipmentMask.getEndTime());
            alarmMaskLog.setComment(equipmentMask.getReason());
            alarmMaskLogList.add(alarmMaskLog);
        }
        alarmMaskLogMapper.batchInsert(alarmMaskLogList);
    }

    @Override
    public void eventMaskCancelLog(List<EventMask> eventMaskList) {
        List<AlarmMaskLog> alarmMaskLogList = new ArrayList<>();
        Date now = new Date();
        for (EventMask eventMask : eventMaskList) {
            AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
            alarmMaskLog.setStationId(eventMask.getStationId());
            alarmMaskLog.setEquipmentId(eventMask.getEquipmentId());
            alarmMaskLog.setEventId(eventMask.getEventId());
            Integer resourceStructureId = Optional.ofNullable(equipmentManager.getEquipmentById(eventMask.getEquipmentId()))
                                                  .map(Equipment::getResourceStructureId).orElse(null);
            alarmMaskLog.setResourceStructureId(resourceStructureId);
            alarmMaskLog.setUserId(eventMask.getUserId());
            alarmMaskLog.setOperationType(MaskOperationTypeEnum.CANCEL_MAST_OPERATION.getOperationType());
            alarmMaskLog.setOperationTime(now);
            alarmMaskLog.setStartTime(eventMask.getStartTime());
            alarmMaskLog.setEndTime(eventMask.getEndTime());
            alarmMaskLog.setComment(eventMask.getReason());
            alarmMaskLogList.add(alarmMaskLog);
        }
        alarmMaskLogMapper.batchInsert(alarmMaskLogList);
    }

    @Override
    public void batchInsertEventMaskLog(BatchCreateEventMaskDTO dto, Integer userId) {
        List<AlarmMaskLog> alarmMaskLogList = new ArrayList<>();
        String timeGroupChars = getTimeGroupChars(dto.getTimeGroupCategory(), dto.getTimeGroupSpans());
        Date now = new Date();
        boolean isTimeGroupSpan = Objects.equals(dto.getTimeGroupCategory(), 2);
        List<EventIdentityDTO> eventIdentityDTOList = EventIdentityDTO.from(dto.getIds());
        for (EventIdentityDTO eventIdentityDTO : eventIdentityDTOList) {
            AlarmMaskLog alarmMaskLog = new AlarmMaskLog();
            alarmMaskLog.setStationId(eventIdentityDTO.getStationId());
            alarmMaskLog.setEquipmentId(eventIdentityDTO.getEquipmentId());
            alarmMaskLog.setEventId(eventIdentityDTO.getEventId());
            alarmMaskLog.setUserId(userId);
            alarmMaskLog.setOperationType(MaskOperationTypeEnum.ADD_MASK_OPERATION.getOperationType());
            alarmMaskLog.setOperationTime(now);
            alarmMaskLog.setTimeGroupCategory(dto.getTimeGroupCategory());
            alarmMaskLog.setStartTime(isTimeGroupSpan ? null : dto.getStartTime());
            alarmMaskLog.setEndTime(isTimeGroupSpan ? null : dto.getEndTime());
            Integer resourceStructureId = Optional.ofNullable(equipmentManager.getEquipmentById(eventIdentityDTO.getEquipmentId()))
                                                  .map(Equipment::getResourceStructureId).orElse(null);
            alarmMaskLog.setResourceStructureId(resourceStructureId);
            alarmMaskLog.setTimeGroupChars(timeGroupChars);
            alarmMaskLog.setComment(dto.getReason());
            alarmMaskLogList.add(alarmMaskLog);
        }
        alarmMaskLogMapper.batchInsert(alarmMaskLogList);
    }

    private String getTimeGroupChars(Integer timeGroupCategory,List<TimeGroupSpanVO> timeGroupSpans) {
        //不是分时段屏蔽
        if (!Objects.equals(timeGroupCategory, TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue())) {
            return null;
        }
        StringJoiner sj = new StringJoiner(",");
        for (TimeGroupSpanVO timeGroupSpan : timeGroupSpans) {
            String timeGroupChar = HexUtil.booleanListToHexString(timeGroupSpan.getTimeSpanBool());
            sj.add(timeGroupSpan.getWeek() + ":" + timeGroupChar);
        }
        return sj.toString();
    }
}
