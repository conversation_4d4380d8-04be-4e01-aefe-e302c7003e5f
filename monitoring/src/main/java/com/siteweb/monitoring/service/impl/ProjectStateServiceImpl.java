package com.siteweb.monitoring.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.HouseProjectDetail;
import com.siteweb.monitoring.dto.ProjectOperationResult;
import com.siteweb.monitoring.dto.StationProjectDetail;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.ProjectStateHouseMapper;
import com.siteweb.monitoring.mapper.ProjectStateOperationMapper;
import com.siteweb.monitoring.mapper.StationMapper;
import com.siteweb.monitoring.mapper.StationMaskMapper;
import com.siteweb.monitoring.service.ProjectStateService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.vo.HouseProjectVO;
import com.siteweb.monitoring.vo.StationFilterVO;
import com.siteweb.monitoring.vo.StationProjectVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("ProjectStateService")
public class ProjectStateServiceImpl implements ProjectStateService {
    @Autowired
    ProjectStateOperationMapper projectStateOperationMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    StationMaskMapper stationMaskMapper;

    @Autowired
    StationMapper stationMapper;

    @Autowired
    ResourceStructureService resourceStructureService;

    @Autowired
    ProjectStateHouseMapper projectStateHouseMapper;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Override
    public ProjectOperationResult setStationProject(StationProjectVO stationProjectVO, Integer userId) {

        Integer stationId = stationProjectVO.getStationId();
        Date startTime =  stationProjectVO.getStartTime();
        Date endTime = stationProjectVO.getEndTime();
        ProjectOperationResult result = new ProjectOperationResult();

        if (ObjectUtil.isNull(stationId) || ObjectUtil.isNull(startTime) ||ObjectUtil.isNull(endTime)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }

        Station station = stationMapper.selectById(stationId);
        if(ObjectUtil.isNull(station)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }
        //获取设备的屏蔽时段
        StationMask stationMask = stationMaskMapper.selectById(stationId);

        //如果屏蔽时段跟工程时间有交叉
        if (ObjectUtil.isNotNull(stationMask) && ObjectUtil.isNotNull(stationMask.getStartTime()) && ObjectUtil.isNotNull(stationMask.getEndTime())) {
            if (!(stationMask.getEndTime() .before(startTime) || stationMask.getStartTime().after(endTime))){
                result.setResult(false);
                result.setComment(messageSourceUtil.getMessage("projectstate.maskconflict"));
                return result;
            }
        }

        Date now = new Date();
        int stationState = now.after(startTime) && now.before(endTime) ? 3 : 1;
        int operateType = ObjectUtil.isNotNull(station.getStartTime()) ? 102 : 101;
        stationMapper.setStationState(stationId, startTime, endTime, stationState);
        stationMapper.saveProjectStateStation(stationId, startTime,endTime,userId, stationProjectVO.getReason());
        ProjectStateOperation projectStateOperation= new ProjectStateOperation();
        projectStateOperation.setStationId(stationId);
        projectStateOperation.setOperationDate(now);
        projectStateOperation.setStartTime(startTime);
        projectStateOperation.setEndTime(endTime);
        projectStateOperation.setReason(stationProjectVO.getReason());
        projectStateOperation.setUserId(userId);
        if(operateType==102) {
            projectStateOperation.setOperationType(102);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.uptstationprojectstate"));
        } else {
            projectStateOperation.setOperationType(101);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.addstationprojectstate"));
        }
        projectStateOperationMapper.insert(projectStateOperation);
        result.setResult(true);
        return result;
    }

    @Override
    public ProjectOperationResult deleteStationProject(Integer stationId, Integer userId) {
        ProjectOperationResult result = new ProjectOperationResult();
        /* 检查参数 */
        if (ObjectUtil.isNull(stationId)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }

        StationProjectVO station = stationMapper.getStationProjectVO(stationId);
        if(ObjectUtil.isNull(station)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }
        ProjectStateOperation projectStateOperation = new ProjectStateOperation();
        projectStateOperation.setEquipmentId(-1);
        projectStateOperation.setStationId(stationId);
        projectStateOperation.setOperationDate(new Date());
        projectStateOperation.setStartTime(station.getStartTime());
        projectStateOperation.setEndTime(station.getEndTime());
        projectStateOperation.setReason(station.getReason());
        projectStateOperation.setUserId(userId);
        projectStateOperation.setOperationType(103);
        projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.delstationprojectstate"));

        stationMapper.setStationState(stationId, null, null, 1);
        stationMapper.saveProjectStateStation(stationId, null,null,userId, null);
        projectStateOperationMapper.insert(projectStateOperation);
        result.setResult(true);
        return result;
    }

    @Override
    public StationProjectVO getStationProject(Integer stationId) {
        StationProjectVO station =  stationMapper.getStationProjectVO(stationId);
        //方便前端判断方便
        if(ObjectUtil.isNotNull(station) && ObjectUtil.isNull(station.getStartTime()))
            return  null;
        return  station;
    }

    @Override
    public void batchDeleteStationProject(List<Integer> ids, Integer userId) {
        for(Integer stationId:ids){
            deleteStationProject(stationId, userId);
        }
    }

    @Override
    public ProjectOperationResult setHouseProjectState(HouseProjectVO houseProjectVO, Integer userId) {
        ProjectOperationResult result = new ProjectOperationResult();
        Integer stationId = houseProjectVO.getStationId();
        Integer houseId = houseProjectVO.getHouseId();
        Date startTime =  houseProjectVO.getStartTime();
        Date endTime = houseProjectVO.getEndTime();

        Date now = new Date();
        ProjectStateHouse projectStateHouse = projectStateHouseMapper.selectList(new QueryWrapper<ProjectStateHouse>().eq("StationId", stationId).eq("HouseId",houseId)).stream().findFirst().orElse(null);
        int operateType = ObjectUtil.isNotNull(projectStateHouse) ? 202 : 201;

        ProjectStateOperation projectStateOperation = new ProjectStateOperation();
        projectStateOperation.setEquipmentId(-1);
        projectStateOperation.setStationId(stationId);
        projectStateOperation.setHouseId(houseId);
        projectStateOperation.setOperationDate(new Date());
        projectStateOperation.setStartTime(startTime);
        projectStateOperation.setEndTime(endTime);
        projectStateOperation.setReason(houseProjectVO.getReason());
        projectStateOperation.setUserId(userId);
        projectStateOperation.setOperationType(operateType);

        if( ObjectUtil.isNotNull(projectStateHouse)){
            projectStateHouse.setStartTime(startTime);
            projectStateHouse.setEndTime(endTime);
            projectStateHouse.setReason(houseProjectVO.getReason());
            projectStateHouse.setUserId(userId);
            projectStateHouse.setLastUpdateDate(now);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.upthouseprojectstate"));
            projectStateHouseMapper.update(projectStateHouse,new QueryWrapper<ProjectStateHouse>().eq("StationId", stationId).eq("HouseId",houseId));
        }  else {
            projectStateHouse = new ProjectStateHouse();
            projectStateHouse.setStationId(stationId);
            projectStateHouse.setHouseId(houseId);
            projectStateHouse.setHouseName(houseProjectVO.getHouseName());
            projectStateHouse.setStartTime(startTime);
            projectStateHouse.setEndTime(endTime);
            projectStateHouse.setReason(houseProjectVO.getReason());
            projectStateHouse.setUserId(userId);
            projectStateHouse.setLastUpdateDate(now);
            projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.addhouseprojectstate"));
            projectStateHouseMapper.insert(projectStateHouse);
        }
        projectStateOperationMapper.insert(projectStateOperation);
        result.setResult(true);
        return result;
    }

    @Override
    public ProjectOperationResult deleteHouseProjectState(String houseId, Integer userId) {
        ProjectOperationResult result = new ProjectOperationResult();
        /* 检查参数 */
        if (ObjectUtil.isNull(houseId)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }
        String[]  ids = StringUtils.split(houseId,",");
        if(ids.length != 2){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("projectstate.invalidparam"));
            return  result;
        }
        Integer stationId = Integer.parseInt(ids[0]);
        Integer ihouseId = Integer.parseInt(ids[1]);
        ProjectStateHouse projectStateHouse = projectStateHouseMapper.selectList(new QueryWrapper<ProjectStateHouse>().eq("StationId", stationId).eq("HouseId",ihouseId)).stream().findFirst().orElse(null);
        if(ObjectUtil.isNull(projectStateHouse)){
            result.setResult(false);
            result.setComment(messageSourceUtil.getMessage("common.msg.disallowOperationEmptyRecord"));
            return  result;
        }
        ProjectStateOperation projectStateOperation = new ProjectStateOperation();
        projectStateOperation.setEquipmentId(-1);
        projectStateOperation.setStationId(stationId);
        projectStateOperation.setHouseId(ihouseId);
        projectStateOperation.setOperationDate(new Date());
        projectStateOperation.setStartTime(projectStateHouse.getStartTime());
        projectStateOperation.setEndTime(projectStateHouse.getEndTime());
        projectStateOperation.setReason(projectStateHouse.getReason());
        projectStateOperation.setUserId(userId);
        projectStateOperation.setOperationType(103);
        projectStateOperation.setOperation(messageSourceUtil.getMessage("projectstate.delstationprojectstate"));

        projectStateHouseMapper.delete(new QueryWrapper<ProjectStateHouse>().eq("StationId", stationId).eq("HouseId",ihouseId));
        projectStateOperationMapper.insert(projectStateOperation);
        result.setResult(true);
        return result;
    }

    @Override
    public ProjectStateHouse getHouseProjectStateById(String houseId) {

        String[]  ids = StringUtils.split(houseId,",");
        if(ids.length != 2){
            return  null;
        }
        Integer stationId = Integer.parseInt(ids[0]);
        Integer ihouseId = Integer.parseInt(ids[1]);

        ProjectStateHouse projectStateHouse = projectStateHouseMapper.selectList(new QueryWrapper<ProjectStateHouse>().eq("StationId", stationId).eq("HouseId",ihouseId)).stream().findFirst().orElse(null);

        return projectStateHouse;
    }

    @Override
    public void batchDeleteHouseProject(List<String> ids,Integer userId) {
        for(String houseId:ids){
            deleteHouseProjectState(houseId, userId);
        }
    }

    @Override
    public List<StationProjectDetail> queryStationProjectDetails(int userId, StationFilterVO stationFilterVO) {
        List<Integer> resourceStructureIdList = null;
        //获取局站信息
        if (stationFilterVO.getResourceStructureIds() != null && stationFilterVO.getResourceStructureIds().size() > 0) {
            resourceStructureIdList = stationFilterVO.getResourceStructureIds();
        }

        //获取全部局站
        if(ObjectUtil.isNull(resourceStructureIdList)){
            List<Integer> structureType= new ArrayList<>();
            structureType.add(104);
            List<ResourceStructure> resourceStructures = resourceStructureService.findByObjectTypeIdAndUserId(structureType,userId);
            resourceStructureIdList = new ArrayList<>();
            for(ResourceStructure resourceStructure:resourceStructures){
                resourceStructureIdList.add(resourceStructure.getResourceStructureId());
            }
        }

        List<StationProjectDetail> stationProjectItems = stationMapper.getAllProjectStations();
        Map<Integer, StationProjectDetail> realTimeSignalItemMap = stationProjectItems.stream().collect(
                Collectors.toMap(StationProjectDetail::getStationId, p -> p));

        for(Integer resourceStructureId :resourceStructureIdList){
            ResourceStructure detail = resourceStructureManager.getResourceStructureById(resourceStructureId);
            if(ObjectUtil.isNotNull(detail) && realTimeSignalItemMap.containsKey(detail.getOriginId())){
                StationProjectDetail stationProjectDetail =realTimeSignalItemMap.get(detail.getOriginId());
                Map<Integer, ResourceStructure> resourceStructureMap = resourceStructureManager.getAllParentStructureById(resourceStructureId);
                ResourceStructure resourceStructure1 =  resourceStructureMap.get(102);
                if(resourceStructure1 !=null){
                    stationProjectDetail.setCenterName(resourceStructure1.getResourceStructureName());
                }
                ResourceStructure resourceStructure2=  resourceStructureMap.get(103);
                if(resourceStructure2 !=null){
                    stationProjectDetail.setGroupName(resourceStructure2.getResourceStructureName());
                }
            }
        }
        return  stationProjectItems;
    }

    @Override
    public List<HouseProjectDetail> queryHouseProjectDetails(int userId) {
        List<HouseProjectDetail> houseProjectDetails = new ArrayList<>();

        HashMap<Integer,ResourceStructure> hashMap = new HashMap<>();
        List<ResourceStructure> resourceStructures = resourceStructureService.findResourceStructureByUserId(userId);
        for(ResourceStructure resourceStructure:resourceStructures){
            if(resourceStructure.getStructureTypeId() == 104){
                hashMap.put(resourceStructure.getOriginId(),resourceStructure);
            }
        }
        List<ProjectStateHouse> projectStateHouses =projectStateHouseMapper.getActiveHouseProject();
        for(ProjectStateHouse projectStateHouse:projectStateHouses){
            if(hashMap.containsKey(projectStateHouse.getStationId())){
                ResourceStructure  resourceStructure = hashMap.get(projectStateHouse.getStationId());
                HouseProjectDetail houseProjectDetail = new HouseProjectDetail(projectStateHouse);
                Map<Integer, ResourceStructure> resourceStructureMap = resourceStructureManager.getAllParentStructureById(resourceStructure.getResourceStructureId());
                ResourceStructure resourceStructure1 =  resourceStructureMap.get(102);
                if(resourceStructure1 !=null){
                    houseProjectDetail.setCenterName(resourceStructure1.getResourceStructureName());
                }
                ResourceStructure resourceStructure2=  resourceStructureMap.get(103);
                if(resourceStructure2 !=null){
                    houseProjectDetail.setGroupName(resourceStructure2.getResourceStructureName());
                }
                ResourceStructure resourceStructure3=  resourceStructureMap.get(104);
                if(resourceStructure3 !=null){
                    houseProjectDetail.setStationName(resourceStructure3.getResourceStructureName());
                }
                houseProjectDetails.add(houseProjectDetail);
            }
        }
        return houseProjectDetails;
    }
}
