package com.siteweb.monitoring.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ConfigEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.mapper.EventConditionMapper;
import com.siteweb.monitoring.mapper.EventMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.vo.*;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.OperationRecord;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.OperationRecordService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("eventService")
public class EventServiceImpl implements EventService {
    private static final String DYNAMIC_MEMO = "动态配置";
    private static final String OBJECT_ID = "ObjectId";
    private static final String CONFIG_ID = "ConfigId";
    private static final String EDIT_TYPE = "EditType";

    @Autowired
    ConfigEventManager configEventManager;

    @Autowired
    EventMapper eventMapper;

    @Autowired
    EventConditionMapper eventConditionMapper;

    @Autowired
    DynamicConfigService dynamicConfigService;

    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;

    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    EventConditionService eventConditionService;

    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    OperationRecordService operationRecordService;
    @Autowired
    ConfigChangeMacroLogService configChangeMacroLogService;
    @Autowired
    EventMaskService eventMaskService;
    @Autowired
    EquipmentTemplateService equipmentTemplateService;
    @Autowired
    MonitorUnitEventService monitorUnitEventService;
    @Autowired
    @Lazy
    SignalService signalService;
    @Autowired
    ControlService controlService;
    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    Boolean byteDanceEnable;
    @Autowired
    DataItemService dataItemService;

    @Override
    public List<ConfigEventDTO> findByStationIdAndEquipmentId(int stationId, int equipmentId) {
        List<ConfigEventDTO> configEventDTOList = new ArrayList<>();
        List<Event> configEvents = eventMapper.findEventsByStationIdAndEquipmentId(stationId, equipmentId);
        List<ActiveEvent> activeEvents = activeEventManager.getActiveEventsByStationIdAndEquipmentId(stationId, equipmentId);
        Set<Integer> maskEffective = eventMaskService.findMaskEffective(equipmentId);
        for (Event event : configEvents) {
            ConfigEventDTO dto = new ConfigEventDTO(event);
            List<ActiveEvent> activeEventList = activeEvents.stream().filter(o -> o.getEventId().equals(event.getEventId()) && Objects.isNull(o.getEndTime())).sorted(Comparator.comparing(ActiveEvent::getEventLevel)).toList();
            if (!activeEventList.isEmpty()) {
                //从当前活动告警中，取到等级最高的一条，将其告警等级名称、开始时间、事件含义组装返回给前端
                dto.setEventSeverity(activeEventList.get(0).getEventSeverity());
                dto.setStartTime(activeEventList.get(0).getStartTime());
                dto.setMeaning(activeEventList.get(0).getMeanings());
                dto.setEventLevel(activeEventList.get(0).getEventLevel());
            }
            if (maskEffective.contains(event.getEventId())) {
                dto.setMask(true);
            }
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            dto.setEquipmentName(equipment.getEquipmentName());
            configEventDTOList.add(dto);
        }
        if (Boolean.TRUE.equals(byteDanceEnable)) {
            // 字节开关打开，增加配置等级的返回
            buildCEventLevel(equipmentId, configEventDTOList);
        }
        return configEventDTOList;
        //return configEventManager.findByStationIdAndEquipmentId(stationId, equipmentId);
    }

    private void buildCEventLevel(int equipmentId, List<ConfigEventDTO> configEventDTOList) {
        List<Integer> eventIds = configEventDTOList.stream().map(ConfigEventDTO::getEventId).distinct().toList();
        List<EquipmentEventConditionDTO> eventConditions = eventConditionService.findEventConditionByEquipmentIdAndEventIds(equipmentId, eventIds);
        Map<Integer, Integer> eventLevelMap = dataItemService.findByEntryId(DataEntryEnum.EVENT_LEVEL.getValue())
                .stream().collect(Collectors.toMap(DataItem::getItemId, f -> Integer.valueOf(f.getExtendField4())));
        Map<Integer, Set<Integer>> eventMap = eventConditions
                .stream()
                .collect(Collectors.groupingBy(
                        EquipmentEventConditionDTO::getEventId,
                        Collectors.mapping(f -> eventLevelMap.get(f.getEventSeverity()), Collectors.toSet())
                ));
        configEventDTOList.forEach(configEventDTO -> configEventDTO.setCeventlevel(eventMap.get(configEventDTO.getEventId())));
    }

    @Override
    public List<SimpleEventDTO> findAllSimpleEventDTOs() {
        return eventMapper.findSimpleEventDTOs();
    }

    @Override
    public List<SimpleEventDTO> findSimpleEventDTOsByEquipmentId(int equipmentId) {
        return eventMapper.findSimpleEventDTOsByEquipmentId(equipmentId);
    }

    @Override
    public ConfigEventDTO findConfigEventDTOByEventId(int equipmentId, int eventId) {
        ConfigEventDTO configEventDTO = eventMapper.findConfigEventDTOByEventId(equipmentId, eventId);
        if (null == configEventDTO) {
            return null;
        }
        configEventDTO.setEventConditions(eventConditionMapper.findEventConditionDTOByEventId(equipmentId, eventId));
        return configEventDTO;
    }

    @Override
    public ConfigEventDTO findConfigEventDTOBySignalId(int equipmentId, int signalId) {
        ConfigEventDTO configEventDTO = eventMapper.findConfigEventDTOBySignalId(equipmentId, signalId);
        if (null == configEventDTO) {
            return null;
        }
        configEventDTO.setEventConditions(eventConditionMapper.findEventConditionDTOBySignalId(equipmentId, signalId));
        return configEventDTO;
    }

    @Override
    @Transactional
    public int updateConfigEventDTO(int userId, ConfigEventDTO configEventDTO) {
        //1.找到原有模板事件
        ConfigEventDTO oldConfigEventDTO = this.findConfigEventDTOByEventId(configEventDTO.getEquipmentId(), configEventDTO.getEventId());
        //2.比较模板事件的变更点
        Map<String, Object> eventChangedHashMap = compareConfigEventDTO(configEventDTO, oldConfigEventDTO);
        Map<Integer, Map<String, Object>> addedConditionHashMap = new HashMap<>();
        Map<Integer, Map<String, Object>> updatedConditionHashMap = new HashMap<>();
        List<Integer> deletedConditionList = new ArrayList<>();
        List<EventConditionDTO> oldEventConditionDTOs = eventConditionMapper.findEventConditionDTOByEventId(configEventDTO.getEquipmentId(), configEventDTO.getEventId());
        for (EventConditionDTO eventConditionDTO : configEventDTO.getEventConditions()) {
            Optional<EventConditionDTO> optionalEventConditionDTO = oldEventConditionDTOs.stream().filter(o -> o.getEventConditionId().equals(eventConditionDTO.getEventConditionId())).findFirst();
            if (optionalEventConditionDTO.isEmpty()) {//新增了事件条件
                addedConditionHashMap.put(eventConditionDTO.getEventConditionId(), getEventConditionChangedHashMap(eventConditionDTO));
            } else {//判断事件条件有无修改
                Map<String, Object> changedHashMap = compareEventConditionDTO(eventConditionDTO, optionalEventConditionDTO.get());
                if (!changedHashMap.isEmpty()) {
                    updatedConditionHashMap.put(eventConditionDTO.getEventConditionId(), getEventConditionChangedHashMap(eventConditionDTO));
                }
            }
        }
        for (EventConditionDTO oldEventConditionDTO : oldEventConditionDTOs) {
            Optional<EventConditionDTO> optionalEventConditionDTO = configEventDTO.getEventConditions().stream().filter(o -> o.getEventConditionId().equals(oldEventConditionDTO.getEventConditionId())).findFirst();
            if (optionalEventConditionDTO.isEmpty()) {//删除了事件条件
                deletedConditionList.add(oldEventConditionDTO.getEventConditionId());
            }
        }
        //3.动态配置XML分发，即向TBL_DynamicConfig表中写入动态配置项
        dynamicConfigService.distributeConfigEvent(userId, configEventDTO.getMonitorUnitId(), configEventDTO, eventChangedHashMap, addedConditionHashMap, updatedConditionHashMap, deletedConditionList);
        //4.模板切换
        saveEventTemplate(oldConfigEventDTO.getEquipmentTemplateId(), configEventDTO, eventChangedHashMap, addedConditionHashMap, updatedConditionHashMap, deletedConditionList);
        //5.生成MU配置同步计划
        dynamicConfigService.generateMUSyncPlan(configEventDTO.getStationId(),configEventDTO.getMonitorUnitId(),new Date());
        Equipment applyEquipment = equipmentManager.getEquipmentById(configEventDTO.getEquipmentId());
        if (null != applyEquipment) {
            OperationRecord operationRecord = new OperationRecord();
            operationRecord.setUserId(userId);
            operationRecord.setOperation(15);//DynamicConfig
            operationRecord.setOperationType(2);//OperationLog
            operationRecord.setStationId(configEventDTO.getStationId());
            operationRecord.setOperationContent(String.format("DynamicConfig Event Equipment :%s.%s", applyEquipment.getEquipmentName(), configEventDTO.getEventName()));
            operationRecordService.saveOperationRecord(operationRecord);
        }
        return 1;
    }

    @Override
    @Transactional
    //告警规则设备内批量应用
    public List<Integer> batchApplyEventConditionToSelfEquipment(int userId, SelfEquipmentEventConditionBatchApplyVO selfEquipmentEventConditionBatchApplyVO) {
        List<Integer> noEventSignalIdList = new ArrayList<>();
        List<EventConditionDTO> conditionDTOList = eventConditionMapper.findEventConditionDTOBySignalId(selfEquipmentEventConditionBatchApplyVO.getEquipmentId(), selfEquipmentEventConditionBatchApplyVO.getSignalId())
                .stream().filter(o -> selfEquipmentEventConditionBatchApplyVO.getEventConditionIds().contains(o.getEventConditionId())).toList();
        for (Integer toApplySignalId : selfEquipmentEventConditionBatchApplyVO.getToApplySignalIds()) {
            ConfigEventDTO configEventDTO = eventMapper.findConfigEventDTOBySignalId(selfEquipmentEventConditionBatchApplyVO.getEquipmentId(), toApplySignalId);
            if (null == configEventDTO) {
                noEventSignalIdList.add(toApplySignalId);
                continue;
            }
            List<EventConditionDTO> tmpEventConditionDTOList = new ArrayList<>();
            for (EventConditionDTO eventConditionDTO : conditionDTOList) {
                EventConditionDTO tmpEventConditionDTO = new EventConditionDTO();
                BeanUtils.copyProperties(eventConditionDTO, tmpEventConditionDTO);
                tmpEventConditionDTO.setEventId(configEventDTO.getEventId());
                tmpEventConditionDTOList.add(tmpEventConditionDTO);
            }
            configEventDTO.setEventConditions(tmpEventConditionDTOList);
            updateConfigEventDTO(userId, configEventDTO);
        }
        return noEventSignalIdList;
    }

    @Override
    @Transactional
    //告警规则跨设备批量应用
    public int batchApplyEventConditionToMultiEquipment(int userId, MultiEquipmentEventConditionBatchApplyVO multiEquipmentEventConditionBatchApplyVO) {
        for (EventConditionBatchApplyVO eventConditionBatchApplyVO : multiEquipmentEventConditionBatchApplyVO.getEventConditionVOs()) {
            List<EventConditionDTO> conditionDTOList = eventConditionMapper.findEventConditionDTOBySignalId(eventConditionBatchApplyVO.getEquipmentId(), eventConditionBatchApplyVO.getSignalId())
                    .stream().filter(o -> eventConditionBatchApplyVO.getEventConditionIds().contains(o.getEventConditionId())).toList();
            for (String toApplyEquipmentIdStr : multiEquipmentEventConditionBatchApplyVO.getToApplyEquipmentIds()) {
                List<String> equipmentIdList = CharSequenceUtil.split(toApplyEquipmentIdStr, ",");
                if (equipmentIdList.size() != 2) {
                    continue;
                }
                Integer equipmentId = Integer.parseInt(equipmentIdList.get(1));
                ConfigEventDTO configEventDTO = eventMapper.findConfigEventDTOBySignalId(equipmentId, eventConditionBatchApplyVO.getSignalId());
                if (null != configEventDTO) {
                    configEventDTO.setEventConditions(new ArrayList<>(conditionDTOList));
                    updateConfigEventDTO(userId, configEventDTO);
                }
            }
        }
        return 1;
    }

    @Override
    public List<EquipmentEventDto> findEventsByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds) {
        if (CollUtil.isEmpty(eventIds)) {
            return new ArrayList<>();
        }
        return eventMapper.findEventsByEquipmentIdAndEventIds(equipmentId,eventIds);
    }

    @Override
    public void batchInsert(List<Event> eventList) {
        if (CollUtil.isEmpty(eventList)) {
            return;
        }
        eventMapper.batchInsert(eventList);
    }

    @Override
    public void deleteByEquipmentTemplateId(Integer equipmentTemplateId) {
        eventMapper.delete(Wrappers.<Event>lambdaQuery()
                                   .eq(Event::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public List<SimpleEventSignalDTO> findEventsByEquipmentIdAndSignalIds(EventRequestBySignalId eventRequestBySignalId) {
        return eventMapper.findEventsByEquipmentIdAndSignalIds(eventRequestBySignalId);
    }

    @Override
    public List<SimpleEventDTO> findSimpleEventDTOsByUserId(Integer userId) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return eventMapper.findSimpleEventDTOsByEquipmentIds(equipmentIds);
    }

    @Override
    public List<Event> findByEquipmentTemplateId(Integer equipmentTemplateId) {
        return eventMapper.selectList(Wrappers.lambdaQuery(Event.class)
                                              .eq(Event::getEquipmentTemplateId, equipmentTemplateId));
    }

    @Override
    public void copyTemplateEvent(Integer sourceTemplateId, Integer destTemplateId) {
        List<Event> eventList = this.findByEquipmentTemplateId(sourceTemplateId);
        eventList.forEach(e->e.setEquipmentTemplateId(destTemplateId));
        this.batchInsert(eventList);
        List<EventCondition> eventConditionList = eventConditionService.findByEquipmentTemplateId(sourceTemplateId);
        eventConditionList.forEach(e -> e.setEquipmentTemplateId(destTemplateId));
        eventConditionService.batchInsert(eventConditionList);
    }

    @Override
    public List<SimpleEventDTO> findEventsByEventRequestByCondition(EventRequestByCondition eventRequestByCondition) {
        if (CollUtil.isEmpty(eventRequestByCondition.getEquipmentIds())) {
            eventRequestByCondition.setEquipmentIds(equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId()));
        }
        return eventMapper.findEventsByEventRequestByCondition(eventRequestByCondition);
    }

    private Map<String, Object> compareConfigEventDTO(ConfigEventDTO configEventDTO, ConfigEventDTO oldConfigEventDTO) {
        HashMap<String, Object> changedMap = new HashMap<>();
        if (!configEventDTO.getEventName().equals(oldConfigEventDTO.getEventName())) {
            changedMap.put("EventName", configEventDTO.getEventName());
        }
        if (!Objects.equals(configEventDTO.getStartExpression(), oldConfigEventDTO.getStartExpression())) {
            changedMap.put("StartExpression", configEventDTO.getStartExpression());
        }
        if (!Objects.equals(configEventDTO.getSuppressExpression(), oldConfigEventDTO.getSuppressExpression())) {
            changedMap.put("SuppressExpression", configEventDTO.getSuppressExpression());
        }
        if (!Objects.equals(configEventDTO.getDisplayIndex(), oldConfigEventDTO.getDisplayIndex())) {
            changedMap.put("DisplayIndex", configEventDTO.getDisplayIndex());
        }
        if (!Objects.equals(configEventDTO.getStartType(), oldConfigEventDTO.getStartType())) {
            changedMap.put("StartType", configEventDTO.getStartType());
        }
        if (!Objects.equals(configEventDTO.getEndType(), oldConfigEventDTO.getEndType())) {
            changedMap.put("EndType", configEventDTO.getEndType());
        }
        return changedMap;
    }

    private Map<String, Object> compareEventConditionDTO(EventConditionDTO eventConditionDTO, EventConditionDTO oldEventConditionDTO) {
        HashMap<String, Object> changedMap = new HashMap<>();
        if (!Objects.equals(eventConditionDTO.getEventSeverity(), oldEventConditionDTO.getEventSeverity())) {
            changedMap.put("EventSeverity", eventConditionDTO.getEventSeverity());
        }
        if (!Objects.equals(eventConditionDTO.getStartOperation(), oldEventConditionDTO.getStartOperation())) {
            changedMap.put("StartOperation", eventConditionDTO.getStartOperation());
        }
        if (!Objects.equals(eventConditionDTO.getStartCompareValue(), oldEventConditionDTO.getStartCompareValue())) {
            changedMap.put("StartCompareValue", eventConditionDTO.getStartCompareValue());
        }
        if (!Objects.equals(eventConditionDTO.getStartDelay(), oldEventConditionDTO.getStartDelay())) {
            changedMap.put("StartDelay", eventConditionDTO.getStartDelay());
        }
        if (!Objects.equals(eventConditionDTO.getEndOperation(), oldEventConditionDTO.getEndOperation())) {
            changedMap.put("EndOperation", eventConditionDTO.getEndOperation());
        }
        if (!Objects.equals(eventConditionDTO.getEndCompareValue(), oldEventConditionDTO.getEndCompareValue())) {
            changedMap.put("EndCompareValue", eventConditionDTO.getEndCompareValue());
        }
        if (!Objects.equals(eventConditionDTO.getEndDelay(), oldEventConditionDTO.getEndDelay())) {
            changedMap.put("EndDelay", eventConditionDTO.getEndDelay());
        }
        if (!Objects.equals(eventConditionDTO.getMeanings(), oldEventConditionDTO.getMeanings())) {
            changedMap.put("Meanings", eventConditionDTO.getMeanings());
        }
        return changedMap;
    }

    private Map<String, Object> getEventConditionChangedHashMap(EventConditionDTO eventConditionDTO) {
        HashMap<String, Object> changedMap = new HashMap<>();
        changedMap.put("EventSeverity", eventConditionDTO.getEventSeverity());
        changedMap.put("StartOperation", eventConditionDTO.getStartOperation());
        changedMap.put("StartCompareValue", eventConditionDTO.getStartCompareValue());
        if (null != eventConditionDTO.getStartDelay()) {
            changedMap.put("StartDelay", eventConditionDTO.getStartDelay());
        }
        if (null != eventConditionDTO.getEndOperation()) {
            changedMap.put("EndOperation", eventConditionDTO.getEndOperation());
        }
        if (null != eventConditionDTO.getEndCompareValue()) {
            changedMap.put("EndCompareValue", eventConditionDTO.getEndCompareValue());
        }
        if (null != eventConditionDTO.getEndDelay()) {
            changedMap.put("EndDelay", eventConditionDTO.getEndDelay());
        }
        if (null != eventConditionDTO.getMeanings()) {
            changedMap.put("Meanings", eventConditionDTO.getMeanings());
        }
        return changedMap;
    }

    private void saveEventTemplate(int sourceTemplateId, ConfigEventDTO configEventDTO, Map<String, Object> eventChangedHashMap, Map<Integer, Map<String, Object>> addedConditionHashMap, Map<Integer, Map<String, Object>> updatedConditionHashMap, List<Integer> deletedConditionList) {
        int destTemplateId = primaryKeyValueService.getGlobalIdentity("TBL_EquipmentTemplate", 0);
        int result = saveEventTemplate(configEventDTO, sourceTemplateId, destTemplateId);
        if (result == 0) {
            destTemplateId = sourceTemplateId;
        }
        saveEventCondition(configEventDTO, destTemplateId, addedConditionHashMap, updatedConditionHashMap, deletedConditionList);
        String objectId = configEventDTO.getStationId() + "." + configEventDTO.getEquipmentId();
        configChangeMacroLogService.saveConfigChangeLog(objectId, 3, 2);
        if (!eventChangedHashMap.isEmpty()) {
            objectId = destTemplateId + "." + configEventDTO.getEventId();
            configChangeMacroLogService.saveConfigChangeLog(objectId,10,2);
        }
        for (EventConditionDTO eventConditionDTO : configEventDTO.getEventConditions()) {
            objectId = destTemplateId + "." + eventConditionDTO.getEventId() + "." + eventConditionDTO.getEventConditionId();
            int editType = -1;
            //新增事件条件
            if (addedConditionHashMap.containsKey(eventConditionDTO.getEventConditionId())) {
                editType = 1;
            } else if (updatedConditionHashMap.containsKey(eventConditionDTO.getEventConditionId())) {//修改事件条件
                editType = 2;
            } else if (deletedConditionList.contains(eventConditionDTO.getEventConditionId())) {//删除事件条件
                editType = 3;
            }
            if (editType > 0) {
                configChangeMacroLogService.saveConfigChangeLog(objectId, 11, editType);
            }
        }
    }

    /**
     * 保存事件模板
     *
     * @param configEventDTO   配置事件dto
     * @param sourceTemplateId 原始模板id
     * @param destTemplateId   模板样板id
     * @return int
     */
    private int saveEventTemplate(ConfigEventDTO configEventDTO, Integer sourceTemplateId, Integer destTemplateId) {
        //更新事件
        updateEventByConfig(configEventDTO, sourceTemplateId);
        //处理表达式
        monitorUnitEventService.expressionHandler(sourceTemplateId, configEventDTO);
        //是否为动态配置模板
        if (equipmentTemplateService.dynamicTemplate(sourceTemplateId)) {
            return 0;
        }
        //更新模板
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.findById(sourceTemplateId);
        equipmentTemplate.setMemo(DYNAMIC_MEMO);
        equipmentTemplate.setEquipmentTemplateId(destTemplateId);
        equipmentTemplateService.createEquipmentTemplate(equipmentTemplate);
        //复制信号
        signalService.copyTemplateSignal(sourceTemplateId, destTemplateId);
        //复制事件
        this.copyTemplateEvent(sourceTemplateId, destTemplateId);
        //复制控制命令
        controlService.copyTemplateControl(sourceTemplateId, destTemplateId);
        equipmentService.updateEquipmentTemplateId(configEventDTO.getEquipmentId(), destTemplateId);
        return 1;
    }

    private void updateEventByConfig(ConfigEventDTO configEventDTO, Integer sourceTemplateId) {
        Event event = Event.builder()
                           .eventName(configEventDTO.getEventName())
                           .startType(configEventDTO.getStartType())
                           .endType(configEventDTO.getEndType())
                           .eventCategory(configEventDTO.getEventCategory())
                           .displayIndex(configEventDTO.getDisplayIndex())
                           .eventId(configEventDTO.getEventId())
                           .equipmentTemplateId(sourceTemplateId)
                           .build();
        eventMapper.updateEventByConfig(event);
    }

    private void saveEventCondition(ConfigEventDTO configEventDTO, int destTemplateId, Map<Integer, Map<String, Object>> addedConditionHashMap, Map<Integer, Map<String, Object>> updatedConditionHashMap, List<Integer> deletedConditionList) {
        for (EventConditionDTO eventConditionDTO : configEventDTO.getEventConditions()) {
            //新增事件条件
            if (addedConditionHashMap.containsKey(eventConditionDTO.getEventConditionId())) {
                EventCondition eventCondition = eventConditionDTO.build();
                eventCondition.setEquipmentTemplateId(destTemplateId);
                eventConditionMapper.insertEventCondition(eventCondition);
            } else if (updatedConditionHashMap.containsKey(eventConditionDTO.getEventConditionId())) {//修改事件条件
                EventCondition eventCondition = eventConditionDTO.build();
                eventCondition.setEquipmentTemplateId(destTemplateId);
                eventConditionMapper.updateEventCondition(eventCondition);
            } else if (deletedConditionList.contains(eventConditionDTO.getEventConditionId())) {//删除事件条件
                eventConditionMapper.deleteEventConditionById(destTemplateId, eventConditionDTO.getEventId(), eventConditionDTO.getEventConditionId());
            }
        }
    }
}
