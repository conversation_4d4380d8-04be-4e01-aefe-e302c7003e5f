package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.SARAlarmActiveRecord;
import com.siteweb.monitoring.mapper.SARAlarmActiveRecordMapper;
import com.siteweb.monitoring.service.SARAlarmActiveRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SARAlarmActiveRecordServiceImpl implements SARAlarmActiveRecordService {
    @Autowired
    SARAlarmActiveRecordMapper sarAlarmActiveRecordMapper;
    @Override
    public void updateBySequenceId(SARAlarmActiveRecord sarAlarmActiveRecord) {
        sarAlarmActiveRecordMapper.updateBySequenceId(sarAlarmActiveRecord);
    }
}
