package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.entity.MonitorUnitConfig;
import com.siteweb.monitoring.mapper.MonitorUnitConfigMapper;
import com.siteweb.monitoring.service.MonitorUnitConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("monitorUnitConfigService")
public class MonitorUnitConfigServiceImpl implements MonitorUnitConfigService {

    @Autowired
    private MonitorUnitConfigMapper monitorUnitConfigMapper;

    @Override
    public List<MonitorUnitConfig> findMonitorUnitConfigs() {
        return monitorUnitConfigMapper.selectList(null);
    }

    @Override
    public int createMonitorUnitConfig(MonitorUnitConfig monitorUnitConfig) {
        return monitorUnitConfigMapper.insert(monitorUnitConfig);
    }

    @Override
    public int deleteById(Integer monitorUnitConfigId) {
        return    monitorUnitConfigMapper.deleteById(monitorUnitConfigId);
    }

    @Override
    public int updateMonitorUnitConfig(MonitorUnitConfig monitorUnitConfig) {
        return monitorUnitConfigMapper.updateById(monitorUnitConfig);
    }

    @Override
    public MonitorUnitConfig findById(Integer monitorUnitConfigId) {
        return monitorUnitConfigMapper.selectById(monitorUnitConfigId);
    }
}
