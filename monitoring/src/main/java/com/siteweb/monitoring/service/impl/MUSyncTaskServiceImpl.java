package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.MUSyncTask;
import com.siteweb.monitoring.mapper.MUSyncTaskMapper;
import com.siteweb.monitoring.service.MUSyncTaskService;
import com.siteweb.monitoring.service.MonitorUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class MUSyncTaskServiceImpl implements MUSyncTaskService {
    @Autowired
    MUSyncTaskMapper muSyncTaskMapper;
    @Autowired
    MonitorUnitService monitorUnitService;

    @Override
    public void generateMUSyncPlan(Integer stationId, Integer monitorUnitId, Date planTime) {
        //设置监控单元同步状态为未同步
        monitorUnitService.syncMonitorUnit(monitorUnitId, 0);
        //如果有动态配置等需及时执行任务时，修改已有任务的执行时间
        if (existPendingTasks(stationId, monitorUnitId)) {
            this.updateSyncTask(stationId, monitorUnitId, planTime);
            return;
        }
        //如果没有待执行任务，则插入新任务
        this.createSyncTask(stationId, monitorUnitId, planTime);
    }

    /**
     * 是否存在待执行的任务
     *
     * @return boolean
     */
    private boolean existPendingTasks(Integer stationId, Integer monitorUnitId) {
        return muSyncTaskMapper.exists(Wrappers.lambdaQuery(MUSyncTask.class)
                                               .eq(MUSyncTask::getStationId, stationId)
                                               .eq(MUSyncTask::getMonitorUnitId, monitorUnitId)
                                               .eq(MUSyncTask::getSyncRule, 1)
                                               .in(MUSyncTask::getSyncState, List.of(0, 1, 5, 6))
        );
    }

    private void createSyncTask(Integer stationId, Integer monitorUnitId, Date planTime) {
        MUSyncTask syncTask = MUSyncTask.builder()
                                        .stationId(stationId)
                                        .monitorUnitId(monitorUnitId)
                                        .planTime(planTime)
                                        .syncState(0)
                                        .syncRule(1)
                                        .maxRetryCount(3)
                                        .retryCount(0)
                                        .description("")
                                        .build();
        muSyncTaskMapper.insert(syncTask);
    }

    private void updateSyncTask(Integer stationId, Integer monitorUnitId, Date planTime) {
        muSyncTaskMapper.update(null, Wrappers.lambdaUpdate(MUSyncTask.class)
                                              .set(MUSyncTask::getPlanTime, planTime)
                                              .set(MUSyncTask::getSyncState, 0)
                                              .eq(MUSyncTask::getStationId, stationId)
                                              .eq(MUSyncTask::getMonitorUnitId, monitorUnitId)
                                              .eq(MUSyncTask::getSyncRule, 1)
                                              .in(MUSyncTask::getSyncState, List.of(0, 1, 5, 6))
                                              .le(MUSyncTask::getPlanTime, planTime));
    }
}
