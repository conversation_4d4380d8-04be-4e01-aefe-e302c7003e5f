package com.siteweb.monitoring.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.monitoring.dto.FocusSignalDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.vo.FocusSignalFilterVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * <AUTHOR> zhou
 * @description FocusSignalService
 * @createTime 2022-08-05 15:31:26
 */
public interface FocusSignalService {

    Page<FocusSignalDTO> queryPageableFocusSignals(int userId, Pageable pageable, FocusSignalFilterVO focusSignalFilterVO);

    List<FocusSignalDTO> queryFocusSignals(int userId, FocusSignalFilterVO focusSignalFilterVO);

    ExcelWriter findFocusSignalExcelWriter(Integer userId, FocusSignalFilterVO focusSignalFilterVO);

    List<Equipment>  findEquipmentsByFilterVO(int userId, FocusSignalFilterVO focusSignalFilterVO);
}
