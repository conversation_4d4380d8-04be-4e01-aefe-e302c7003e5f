package com.siteweb.monitoring.kafka.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventOperationMessage {
    public EventOperationMessage(List<String> sequenceIds, String note,Integer postalCode) {
        this.sequenceIds = sequenceIds;
        this.note = note;
        this.postalCode = postalCode;
    }

    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 操作告警的序列号
     */
    private List<String> sequenceIds;
    /**
     * 备注
     */
    private String note;
    /**
     * 区号，用来判断两个二级需要消费该消息
     */
    private Integer postalCode;
    /**
     * 告警原因类型
     */
    private Integer eventReasonType;
}
