package com.siteweb.monitoring.kafka.consumer;

import cn.hutool.json.JSONUtil;
import com.siteweb.monitoring.kafka.KafkaUtil;
import com.siteweb.monitoring.kafka.dto.EventOperationMessage;
import com.siteweb.monitoring.service.ActiveEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Objects;


@Slf4j
@Component
@ConditionalOnProperty(name = "spring.kafka.properties.kafkaGroupId")
public class EventConsumer {
    @Autowired
    KafkaUtil kafkaUtil;
    @Autowired
    ActiveEventService activeEventService;

    /**
     * 告警确认
     */
    @KafkaListener(topics = "EventConfirm", groupId = "#{kafkaUtil.getConsumerGroupName()}")
    public void eventConfirm(ConsumerRecord<String, String> message) {
        String value = message.value();
        log.info("接收到告警确认的kafka消息:{}", value);
        EventOperationMessage eventOperationMessage = JSONUtil.toBean(value, EventOperationMessage.class);
        if (doesNotNeedConsumption(message.key(), message.headers())) return;
        activeEventService.confirmActiveEventsRemoveProc(eventOperationMessage.getUserId(), eventOperationMessage.getUserName(), eventOperationMessage.getSequenceIds(), eventOperationMessage.getNote(), eventOperationMessage.getEventReasonType());
    }

    /**
     * 告警结束
     */
    @KafkaListener(topics = "EventCancel", groupId = "#{kafkaUtil.getConsumerGroupName()}")
    public void eventCancel(ConsumerRecord<String, String> message) {
        String value = message.value();
        log.info("接收到告警结束的kafka消息:{}", value);
        EventOperationMessage eventOperationMessage = JSONUtil.toBean(value, EventOperationMessage.class);
        if (doesNotNeedConsumption(message.key(), message.headers())) return;
        activeEventService.cancelActiveEventsRemoveProc(eventOperationMessage.getUserId(), eventOperationMessage.getUserName(), eventOperationMessage.getSequenceIds(), eventOperationMessage.getNote());
    }


    /**
     * 告警备注
     */
    @KafkaListener(topics = "EventNote", groupId = "#{kafkaUtil.getConsumerGroupName()}")
    public void eventNote(ConsumerRecord<String, String> message) {
        String value = message.value();
        log.info("接收到告警备注的kafka消息:{}", value);
        EventOperationMessage eventOperationMessage = JSONUtil.toBean(value, EventOperationMessage.class);
        if (doesNotNeedConsumption(message.key(), message.headers())) return;
        activeEventService.addEventNote(eventOperationMessage.getSequenceIds(), eventOperationMessage.getNote());
    }


    /**
     * 是否不需要消费该消息
     *
     * @param key     消息key（区号）
     * @param headers
     * @return boolean true 不需要消费  false 需要消费
     */
    private boolean doesNotNeedConsumption(String key, Headers headers) {
        for (Header header : headers) {
            //自己发送的消息 自己无需消费
            if (Objects.equals(header.key(), KafkaUtil.SENDER) && Objects.equals(new String(header.value()), kafkaUtil.getConsumerGroupName())) {
                log.info("自己发送的消息 自己无需消费");
                return true;
            }
        }
        //如果消息分组为SS且消息的Key不匹配，则直接忽略该消息
        if (KafkaUtil.SS.equals(kafkaUtil.getKafkaGroupId()) && !Objects.equals(key, kafkaUtil.getKafkaMessageKey())) {
            log.info("如果消息分组为SS且消息的Key不匹配，则直接忽略该消息");
            return true;
        }
        return false;
    }
}
