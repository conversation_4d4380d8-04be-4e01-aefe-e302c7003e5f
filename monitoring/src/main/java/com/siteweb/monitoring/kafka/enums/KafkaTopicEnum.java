package com.siteweb.monitoring.kafka.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用来定义kafka消息发送的主题
 * <AUTHOR>
 * @date 2024/08/26
 */
@Getter
@AllArgsConstructor
public enum KafkaTopicEnum {
    EVENT_CONFIRM("EventConfirm","告警确认"),
    EVENT_CANCEL("EventCancel","告警结束"),
    EVENT_NOTE("EventNote","告警备注"),
    CONTROL_REQUEST("ControlRequest","控制命令"),
    DYNAMIC_CONFIG_REQUEST("DynamicConfigRequest", "动态配置");
    private final String topic;
    private final String describe;
}