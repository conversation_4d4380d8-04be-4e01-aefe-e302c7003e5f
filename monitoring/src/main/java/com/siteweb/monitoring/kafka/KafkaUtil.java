package com.siteweb.monitoring.kafka;

import cn.hutool.json.JSONUtil;
import com.siteweb.monitoring.kafka.dto.EventOperationMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.properties.kafkaGroupId")
public class KafkaUtil {
    /**
     * 三级标识
     */
    public static final String SC = "SC";
    /**
     * 二级标识
     */
    public static final String SS = "SS";
    public static final String SENDER = "sender";
    private static final String KAFKA_GROUP_ID_KEY = "kafkaGroupId";
    private static final String KAFKA_MESSAGE_KEY_KEY = "kafkaMessageKey";

    @Autowired
    private KafkaProperties kafkaProperties;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 发送事件消息至kafka
     *
     * @param topic 主题
     * @param eventOperationMessage 事件操作消息
     * @throws IllegalArgumentException 当区号为空时抛出
     */
    public void sendEventMsg(String topic, EventOperationMessage eventOperationMessage) {
        if (Objects.isNull(eventOperationMessage.getPostalCode())) {
            log.error("topic:{},{}发送kafka消息失败：区号不能为空", topic, eventOperationMessage);
            return;
        }

        String messageKey = String.valueOf(eventOperationMessage.getPostalCode());
        String messagePayload = JSONUtil.toJsonStr(eventOperationMessage);

        Message<String> message = buildKafkaMessage(topic, messageKey, messagePayload);
        sendMessageWithLogging(message, messageKey, topic, messagePayload);
    }

    /**
     * 发送控制命令消息至kafka
     *
     * @param topic 主题
     * @param messageKey 消息key（区号）
     * @param msg 需要发送的消息
     */
    public void sendControlMsg(String topic, Integer messageKey, String msg) {
        sendSimpleMessage(topic, messageKey, msg);
    }

    /**
     * 发送动态配置消息至kafka
     *
     * @param topic 主题
     * @param messageKey 消息key（区号）
     * @param msg 需要发送的消息
     */
    public void sendDynamicMsg(String topic, Integer messageKey, String msg) {
        sendSimpleMessage(topic, messageKey, msg);
    }

    /**
     * 获取消费者组的名称
     *
     * @return 消费者组名称，三级返回groupId，二级返回"groupId-messageKey"格式
     */
    public String getConsumerGroupName() {
        String groupId = getKafkaGroupId();
        return SC.equals(groupId) ? groupId : buildGroupName(groupId);
    }

    /**
     * 获取是二级还是三级的表示
     * @return SS表示二级 SC表示三级
     */
    public String getKafkaGroupId() {
        return getKafkaProperty(KAFKA_GROUP_ID_KEY);
    }

    /**
     * 获取区号
     */
    public String getKafkaMessageKey() {
        return getKafkaProperty(KAFKA_MESSAGE_KEY_KEY);
    }

    private Message<String> buildKafkaMessage(String topic, String messageKey, String payload) {
        return MessageBuilder.withPayload(payload)
                             .setHeader(KafkaHeaders.TOPIC, topic)
                             .setHeader(KafkaHeaders.MESSAGE_KEY, messageKey)
                             .setHeader(SENDER, getConsumerGroupName())
                             .build();
    }

    private void sendMessageWithLogging(Message<String> message, String messageKey, String topic, String payload) {
        try {
            kafkaTemplate.send(message);
            logSuccessMessage(messageKey, topic, payload);
        } catch (Exception e) {
            logErrorMessage(messageKey, topic, payload, e);
        }
    }

    private void sendSimpleMessage(String topic, Integer messageKey, String msg) {
        try {
            String stringKey = String.valueOf(messageKey);
            kafkaTemplate.send(topic, stringKey, msg);
            logSuccessMessage(stringKey, topic, msg);
        } catch (Exception e) {
            logErrorMessage(String.valueOf(messageKey), topic, msg, e);
        }
    }

    private void logSuccessMessage(String messageKey, String topic, String message) {
        log.info("发送kafka消息 key:{} topic:{} message:{}", messageKey, topic, message);
    }

    private void logErrorMessage(String messageKey, String topic, String message, Exception e) {
        log.error("Kafka消息发送失败 key:{} topic:{} message:{}", messageKey, topic, message, e);
    }

    private String buildGroupName(String groupId) {
        return String.format("%s-%s", groupId, getKafkaMessageKey());
    }

    private String getKafkaProperty(String key) {
        return kafkaProperties.getProperties().get(key);
    }
}