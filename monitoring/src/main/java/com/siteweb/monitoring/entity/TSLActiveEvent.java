package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.monitoring.model.ConfigEventItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tsl_activeevent")
public class TSLActiveEvent {
    public TSLActiveEvent(ConfigEventItem configEvent) {
        this.stationId = configEvent.getStationId();
        this.equipmentId = configEvent.getEquipmentId();
        this.eventId = configEvent.getEventId();
        this.eventConditionId = configEvent.getEventConditionId();
        this.sequenceId = configEvent.getSequenceId();
        this.startTime = configEvent.getStartTime();
        this.endTime = configEvent.getEndTime();
        this.eventValue = configEvent.getEventValue();
        this.reversalNum = configEvent.getOverturn();
        this.meanings = configEvent.getMeanings();
        this.baseTypeId = configEvent.getBaseTypeId();
    }

    private Integer stationId;
    private Integer equipmentId;
    private Integer eventId;
    private Integer eventConditionId;
    @TableId
    private String sequenceId;
    private Date startTime;
    private Date endTime;
    private String resetSequenceId;
    private Double eventValue;
    private Integer reversalNum;
    private String meanings;
    private Long baseTypeId;
}
