package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("TBL_SARAlarmActiveRecord")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SARAlarmActiveRecord {
    private Integer stationId;
    private Integer stationCategoryId;
    private Integer equipmentId;
    private Integer eventId;
    private Integer eventConditionId;
    private String sequenceId;
    private Date startTime;
    private Date endTime;
    private Integer overturn;
    private String meanings;
    private Double eventValue;
    private Double baseTypeId;
    private Integer standardId;
    private Date insertDateTime;
    private Integer relationType;
}
