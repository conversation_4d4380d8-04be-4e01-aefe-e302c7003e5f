package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 历史控制命令表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-08 15:09:21
 */
@Data
@NoArgsConstructor
@TableName("tbl_historycontrol")
public class HistoryControl {

	/**
	 * 局站ID
	 */
	private Integer stationId;
	/**
	 * 局站名
	 */
	private String stationName;
	/**
	 * 设备ID
	 */
	private Integer equipmentId;
	/**
	 * 设备名
	 */
	private String equipmentName;
	/**
	 * 控制命令ID
	 */
	private Integer controlId;
	/**
	 * 控制命令名
	 */
	private String controlName;
	/**
	 * 
	 */
	 @TableId(value="serialNo", type = IdType.INPUT)
	private Integer serialNo;
	/**
	 * 控制等级
	 */
	private Integer controlSeverity;
	/**
	 * 控制命令串
	 */
	private String cmdToken;
	/**
	 * 控制阶段
	 */
	private Integer controlPhase;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 确认时间
	 */
	private Date confirmTime;
	/**
	 * 确认人ID
	 */
	private Integer confirmerId;
	/**
	 * 确认人名
	 */
	private String confirmerName;
	/**
	 * 控制结果ID
	 */
	private Integer controlResultType;
	/**
	 * 控制结果
	 */
	private String controlResult;
	/**
	 * 执行人Id
	 */
	private Integer controlExecuterId;
	/**
	 * 执行人名
	 */
	private String controlExecuterIdName;
	/**
	 * 控制类型
	 */
	private Integer controlType;
	/**
	 * 联动ID
	 */
	private Integer actionId;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 重试次数
	 */
	private Integer retry;
	/**
	 * 控制基类ID
	 */
	private BigDecimal baseTypeId;
	/**
	 * 控制基类名
	 */
	private String baseTypeName;
	/**
	 * 控制参数
	 */
	private String parameterValues;
	/**
	 * 基类参数Id
	 */
	private BigDecimal baseCondId;

	/**
	 * 层级ID
	 */
	private Integer resourceStructureId;
}
