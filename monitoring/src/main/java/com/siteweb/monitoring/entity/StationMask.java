package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_StationMask")
public class StationMask {

    /**
     * 局站ID
     */
    @TableId(value = "stationId", type = IdType.INPUT)
    private Integer stationId;
    /**
     * 时间分组ID
     */
    private Integer timeGroupId;
    /**
     * 屏蔽原因
     */
    private String reason;
    /**
     * 屏蔽开始时间
     */
    private Date startTime;
    /**
     * 屏蔽结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人ID
     */
    private Integer userId;
}
