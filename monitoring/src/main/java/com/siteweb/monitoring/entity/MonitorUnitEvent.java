package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监视器单元事件
 *
 * <AUTHOR>
 * @date 2024/05/10
 */
@TableName("tsl_monitorunitevent")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MonitorUnitEvent {
    /**
     * 局站id
     */
    private int stationId;
    /**
     * 监控单元id
     */
    private int monitorUnitId;
    /**
     * 设备id
     */
    private int equipmentId;
    /**
     * 事件id
     */
    private int eventId;
    /**
     * 开始表达式
     */
    private String startExpression;
    /**
     * 抑制表达式
     */
    private String suppressExpression;
}
