package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_musynctask")
@Builder
public class MUSyncTask {
    @TableId(type = IdType.AUTO)
    private Integer taskId;
    private Integer stationId;
    private Integer monitorUnitId;
    private Integer syncState;
    private Integer syncRule;
    private Date planTime;
    private Date beginTime;
    private Date endTime;
    private Date updateTime;
    private Integer maxRetryCount;
    private Integer retryCount;
    private String description;
}
