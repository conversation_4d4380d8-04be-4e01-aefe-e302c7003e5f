package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_configchangemicrolog")
public class ConfigChangeMicroLog {
    private String objectId;
    private Integer configId;
    private Integer editType;
    private Date updateTime;
}
