package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * EventMask info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-29 16:38:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_EventMask")
public class EventMask {

    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 基站ID
     */
    private Integer stationId;
    /**
     * 告警ID
     */
    private Integer eventId;
    /**
     * 时间分组ID
     */
    private Integer timeGroupId;
    /**
     * 屏蔽原因
     */
    private String reason;
    /**
     * 屏蔽开始时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED,updateStrategy = FieldStrategy.IGNORED)
    private Date startTime;
    /**
     * 屏蔽结束时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED,updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;
    /**
     * 屏蔽人ID
     */
    private Integer userId;

    /**
     * 告警等级
     */
    @JsonIgnore
    @TableField(exist = false)
    private Integer eventSeverity;
}
