package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 门禁控制命令表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-08 15:09:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_activecontrolofdoor")
public class ActiveControlOfDoor {

	/**
	 * 局站ID
	 */
	private Integer stationId;
	/**
	 * 主机ID
	 */
	private Integer hostId;
	/**
	 * 设备ID
	 */
	private Integer equipmentId;
	/**
	 * 控制命令ID
	 */
	private Integer controlId;
	/**
	 * 用户名
	 */
	private Integer userId;
	/**
	 * 参数值
	 */
	private String parameterValues;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 最后更新时间
	 */
	private Date lastUpdate;
	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;

	/**
	 * 层级ID
	 */
	private Integer resourceStructureId;
}
