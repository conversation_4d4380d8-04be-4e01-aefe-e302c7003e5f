package com.siteweb.monitoring.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * @author: Habits
 * @time: 2022/5/14 10:24
 * @description:
 **/
@Data
@NoArgsConstructor
@Measurement(name = "historydatas")
public class HistorySignal {

    @Column(name = "time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Instant time;

    @Column(name = "BaseTypeId", tag = true)
    private String BaseTypeId;

    @Column(name = "DeviceId", tag = true)
    private String DeviceId;

    @Column(name = "SignalId", tag = true)
    private String SignalId;

    @Column(name = "StationId", tag = true)
    private String StationId;

    @Column(name = "PointValue")
    private String pointValue;
    private Object value;

    private String displayValue;

    private Integer sortIndex;


    public String getTime() {
        String tempTime = this.time.toString();
        if (tempTime != null && !tempTime.equals("")) {
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }

    public String getPointValue() {
        if (this.pointValue == null || this.pointValue.equals("")) {
            if (this.value == null || this.value.equals("")) {
                return "-";
            }
            this.pointValue = this.value.toString();
        }
        BigDecimal value =  new BigDecimal(this.pointValue);
        return value.toString();
    }

    public String getSampleTime() {
        return getTime();
    }
}
