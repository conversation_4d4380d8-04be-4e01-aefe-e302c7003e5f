package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tblSignalproperty info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:41
 */
@Data
@NoArgsConstructor
@TableName("tbl_SignalProperty")
public class SignalProperty {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 模板ID
	 */
	private Integer equipmentTemplateId;
	/**
	 * 信号ID
	 */
	private Integer signalId;
	/**
	 * 属性ID
	 */
	private Integer signalPropertyId;
}
