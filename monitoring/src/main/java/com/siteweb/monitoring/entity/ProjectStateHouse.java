package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tbl_projectstatehouse")
public class ProjectStateHouse {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 机房Id
     */
    private  Integer houseId;
    /**
     * 机房名称
     */
    private  String houseName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date    endTime;
    /**
     * 工程原因
     */
    private String  reason;
    /**
     *用户Id
     */
    private  Integer userId;
    /**
     * 结束时间
     */
    private Date   lastUpdateDate;
}
