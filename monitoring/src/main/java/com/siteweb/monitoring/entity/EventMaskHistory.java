package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("tbl_eventmaskhistory")
public class EventMaskHistory {
    private String sequenceId;
    private Integer stationId;
    private Integer equipmentId;
    private Integer eventId;
    private Integer eventConditionId;
    private Double eventValue;
    private Double endValue;
    private String meanings;
    private Double baseTypeId;
    private Date startTime;
    private Date endTime;
}
