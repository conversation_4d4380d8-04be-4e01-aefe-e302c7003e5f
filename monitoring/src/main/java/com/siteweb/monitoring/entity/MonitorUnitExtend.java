package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("tsl_monitorunitextend")
public class MonitorUnitExtend {

    /** 监控单元ID */
    private Integer monitorUnitId;

    /** 描述 */
    private String description;

    /**
     * 扩展字段1
     * 用来存储监控单元的账号密码端口协议,密码使用aes对称加密
     * ssh:root:123456:22
     * */
    private String extendFiled1;

    /** 扩展字段2 */
    private String extendFiled2;

    /** 扩展字段3 */
    private String extendFiled3;

    /** 扩展字段4 */
    private String extendFiled4;

    /** 扩展字段5 */
    private String extendFiled5;
}

