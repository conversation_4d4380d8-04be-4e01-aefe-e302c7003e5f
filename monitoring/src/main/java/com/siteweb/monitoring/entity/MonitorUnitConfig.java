package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tslMonitorunitconfig info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:36:02
 */
@Data
@NoArgsConstructor
@TableName("tsl_MonitorUnitConfig")
public class MonitorUnitConfig {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 配置ID
	 */
	private Integer appConfigId;
	/**
	 * 超时设置
	 */
	private Integer siteWebTimeOut;
	/**
	 * 重试次数
	 */
	private Integer retryTimes;
	/**
	 * 心跳周期
	 */
	private Integer heartBeat;
	/**
	 * 设备超时配置
	 */
	private Integer equipmentTimeOut;
	/**
	 * 端口中断配置
	 */
	private Integer portInterruptCount;
	/**
	 * 端口初始时间
	 */
	private Integer portInitializeInternal;
	/**
	 * 最大重试次数
	 */
	private Integer maxPortInitializeTimes;
	/**
	 * 端口轮询时间
	 */
	private Integer portQueryTimeOut;
	/**
	 * 数据保存时间
	 */
	private Integer dataSaveTimes;
	/**
	 * 历史数据保存时间
	 */
	private Integer historySignalSavedTimes;
	/**
	 * 电池数据保存时间
	 */
	private Integer historyBatterySavedTimes;
	/**
	 * 历史告警保存次数
	 */
	private Integer historyEventSavedTimes;
	/**
	 * 刷卡记录保存次数
	 */
	private Integer cardRecordSavedCount;
	/**
	 * 是否记录控制日志
	 */
	private Boolean controlLog;
	/**
	 * DS IP地址
	 */
	private String ipAddressDs;
}
