package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description StandardDicEvent
 * @createTime 2022-08-10 10:45:11
 */
@Data
@NoArgsConstructor
@TableName("TBL_StandardDicEvent")
public class StandardDicEvent {

    private Integer standardDicId;
    private Integer standardType;
    private Integer equipmentLogicClassId;
    private String equipmentLogicClass;
    private Integer eventLogicClassId;
    private String eventLogicClass;
    private String eventClass;
    private String eventStandardName;
    private String netManageId;
    private Integer eventSeverity;
    private String compareValue;
    private String startDelay;
    private String meanings;
    private String equipmentAffect;
    private String businessAffect;
    private Integer stationCategory;
    private Integer modifyType;
    private String description;
    private String extendFiled1;
    private String extendFiled2;
    private String extendFiled3;
}
