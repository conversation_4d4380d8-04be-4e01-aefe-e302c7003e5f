package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * WorkStation info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:41
 */
@Data
@NoArgsConstructor
@TableName("tbl_WorkStation")
public class WorkStation {

	/**
	 * 监控主机ID
	 */
	 @TableId(value="workStationId", type = IdType.INPUT)
	private Integer workStationId;
	/**
	 * 监控主机名
	 */
	private String workStationName;
	/**
	 * 监控主机类型
	 */
	private Integer workStationType;
	/**
	 * IP地址
	 */
	private String ipAddress;
	/**
	 * 父主机ID
	 */
	private Integer parentId;
	/**
	 * 在线状态
	 */
	private Integer connectState;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 是否投用
	 */
	private Boolean isUsed;
	/**
	 * CPU百分比
	 */
	private Double cpu;
	/**
	 * Memory占用百分比
	 */
	private Double memory;
	/**
	 * 线程数量
	 */
	private Integer threadCount;
	/**
	 * 磁盘剩余空间
	 */
	private Double diskFreeSpace;
	/**
	 * 数据库剩余控件
	 */
	private Double dbFreeSpace;
	/**
	 * 上次连接时间
	 */
	private Date lastCommTime;
	/**
	 * 描述信息
	 */
	private String comment;
}
