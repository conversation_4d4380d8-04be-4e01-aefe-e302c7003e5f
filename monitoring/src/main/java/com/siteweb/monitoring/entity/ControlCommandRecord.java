package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2023/3/21 15:36
 */
@Data
@TableName("controlcommandrecord")
public class ControlCommandRecord {
    @TableId(type = IdType.AUTO)
    private Integer ControlCommandRecordId;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 局站
     */
    private String stationName;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 设备
     */
    private String equipmentName;
    /**
     * 控制命令ID
     */
    private Integer controlId;
    /**
     * 控制命令
     */
    private String controlName;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 控制结果
     */
    private Integer controlResult;
    /**
     * 设置值
     */
    private String setValue;
    /**
     * 描述信息
     */
    private String description;
}
