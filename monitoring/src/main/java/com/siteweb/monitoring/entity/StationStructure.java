package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("tbl_stationstructure")
public class StationStructure {
    @TableId(type = IdType.INPUT)
    private Integer structureId;
    private Integer structureGroupId;
    private Integer parentStructureId;
    private String structureName;
    private Boolean isUngroup;
    private Integer structureType;
    private Double mapZoom;
    private Double longitude;
    private Double latitude;
    private String description;
    private String levelPath;
    private Boolean enable;
    @TableField(exist = false)
    private Integer stationid;
}
