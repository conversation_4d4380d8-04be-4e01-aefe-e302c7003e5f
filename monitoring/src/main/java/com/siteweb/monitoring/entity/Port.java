package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tslPort info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:36:02
 */
@Data
@NoArgsConstructor
@TableName("tsl_port")
public class Port {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 端口ID
	 */
	private Integer portId;
	/**
	 * 采集单元ID
	 */
	private Integer monitorUnitId;
	/**
	 * 端口号
	 */
	private Integer portNo;
	/**
	 * 端口名
	 */
	private String portName;
	/**
	 * 端口类型
	 */
	private Integer portType;
	/**
	 * 端口设置
	 */
	private String setting;
	/**
	 * 拨号号码
	 */
	private String phoneNumber;
	/**
	 * 关联采集单元ID
	 */
	private Integer linkSamplerUnitId;
	/**
	 * 描述信息
	 */
	private String description;

	public String getKey(){
		return monitorUnitId+"."+portId;
	}
}
