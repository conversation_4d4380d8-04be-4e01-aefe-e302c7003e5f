package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tblControlmeanings info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:39:11
 */
@Data
@NoArgsConstructor
@TableName("tbl_ControlMeanings")
public class ControlMeanings {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 设备模板ID
	 */
	private Integer equipmentTemplateId;
	/**
	 * 控制命令ID
	 */
	private Integer controlId;
	/**
	 * 控制值
	 */
	private Integer parameterValue;
	/**
	 * 命令含义
	 */
	private String meanings;
	/**
	 * 基类ID
	 */
	private Long baseCondId;
}
