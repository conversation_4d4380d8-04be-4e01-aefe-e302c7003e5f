package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description DynamicConfig
 * @createTime 2022-06-13 14:48:58
 */
@Data
@NoArgsConstructor
@TableName("tbl_dynamicconfig")
public class DynamicConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer userId;

    private Date configTime;

    private Integer stationId;

    private Integer hostId;

    private Integer syncFlag;

    private String syncXml;

    private Date syncTime;
}
