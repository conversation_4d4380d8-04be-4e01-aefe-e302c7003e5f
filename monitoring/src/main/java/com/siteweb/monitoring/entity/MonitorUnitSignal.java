package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tsl_MonitorUnitSignal")
public class MonitorUnitSignal {
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 监控单元id
     */
    private Integer monitorUnitId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 引用采集单元id
     */
    private Integer referenceSamplerUnitId;
    /**
     * 引用通道号
     */
    private Integer referenceChannelNo;
    /**
     * 表达式
     */
    private String expression;
    /**
     * 实列类型
     */
    private Integer instanceType;
}
