package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * MonitorUnit info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:36:02
 */
@Data
@NoArgsConstructor
@TableName("tsl_MonitorUnit")
public class MonitorUnit {

	/**
	 * 
	 */
	 @TableId(value="monitorUnitId", type = IdType.INPUT)
	private Integer monitorUnitId;
	/**
	 * 监控单元名
	 */
	private String monitorUnitName;
	/**
	 * 监控单元类型
	 */
	private Integer monitorUnitCategory;
	/**
	 * 监控单元编码
	 */
	private String monitorUnitCode;
	/**
	 * 监控主机ID
	 */
	private Integer workStationId;
	/**
	 * 局站ID
	 */
	private Integer stationId;
	/**
	 * IP地址
	 */
	private String ipAddress;
	/**
	 * 运行模式
	 */
	private Integer runMode;
	/**
	 * 配置文件MD5码
	 */
	private String configFileCode;
	/**
	 * 配置更新时间
	 */
	private Date configUpdateTime;
	/**
	 * 
	 */
	private String sampleConfigCode;
	/**
	 * 
	 */
	private String softwareVersion;
	/**
	 * 
	 */
	private String description;
	/**
	 * 
	 */
	private Date startTime;
	/**
	 * 
	 */
	private Date heartBeatTime;
	/**
	 * 
	 */
	private Integer connectState;
	/**
	 * 
	 */
	private Date updateTime;
	/**
	 * 
	 */
	private Boolean isSync;
	/**
	 * 
	 */
	private Date syncTime;
	/**
	 * 
	 */
	private Boolean isConfigOk;
	/**
	 * 
	 */
	private String configFileCode_Old;
	/**
	 * 
	 */
	private String sampleConfigCode_Old;
	/**
	 * 
	 */
	private Integer appCongfigId;
	/**
	 * 是否可分发
	 */
	private Boolean canDistribute;
	/**
	 * 是否可用
	 */
	private Boolean enable;
	/**
	 * 工程号
	 */
	private String projectName;
	/**
	 * 合同号
	 */
	private String contractNo;
	/**
	 * 安装时间
	 */
	private Date installTime;

	public Integer getCenterId(){
		return  (monitorUnitId/100000)*100000 + 1;
	}

	/**
	 * 设备基类名称
	 */
	@TableField(exist = false)
	private String monitorUnitCategoryName;

}
