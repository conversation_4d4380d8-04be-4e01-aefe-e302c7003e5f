package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_ProjectStateOperation")
public class ProjectStateOperation {
    @TableId(value = "operationId", type = IdType.AUTO)
    private Integer operationId;
    private Integer operationType;
    private String operation;
    private Integer stationId;
    private Integer houseId;
    private Integer equipmentId;
    private String reason;
    private Date startTime;
    private Date endTime;
    private Integer userId;
    private Date operationDate;
}
