package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description ActiveEventOperationLog
 * @createTime 2022-04-09 13:39:37
 */
@Data
@NoArgsConstructor
@TableName("activeeventoperationlog")
public class ActiveEventOperationLog {

    @TableId(value = "ActiveEventOperationLogId", type = IdType.AUTO)
    private Integer activeEventOperationLogId;

    private String sequenceId;

    private Integer stationId;

    private Integer equipmentId;

    private Integer eventId;

    private Integer eventConditionId;

    private Date startTime;

    private Integer operatorId;

    private String operation;

    private Date operationTime;

    private String description;

    @TableField(exist = false)
    private ActiveEvent activeEvent;

    @TableField(exist = false)
    private HistoryEvent historyEvent;

    @TableField(exist = false)
    private String userName;
}
