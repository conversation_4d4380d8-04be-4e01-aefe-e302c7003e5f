package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.common.util.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 告警变化表
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-08 15:09:21
 */
@Data
@NoArgsConstructor
@TableName("tbl_alarmchange")
public class AlarmChange {

    /**
     *
     */
    private String sequenceId;
    /**
     * 变化流水号(自增)
     */
    @TableId(value = "serialNo", type = IdType.AUTO)
    private Long serialNo;
    /**
     * 操作类型（开始，结束， 确认）
     */
    private Integer operationType;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 局站名
     */
    private String stationName;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 事件ID
     */
    private Integer eventId;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 条件ID
     */
    private Integer eventConditionId;
    /**
     * 告警等级Id
     */
    private Integer eventSeverityId;
    /**
     * 告警等级ID
     */
    private Integer eventLevel;
    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 取消时间
     */
    private Date cancelTime;
    /**
     * 取消人Id
     */
    private Integer cancelUserId;
    /**
     * 取消人名
     */
    private String cancelUserName;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 确认人ID
     */
    private Integer confirmerId;
    /**
     * 确认任命
     */
    private String confirmerName;
    /**
     * 触发值
     */
    private Double eventValue;
    /**
     * 翻转次数
     */
    private Integer reversalNum;
    /**
     * 告警涵义
     */
    private String meanings;
    /**
     * 告警录像文件路径
     */
    private String eventFilePath;
    /**
     * 告警注释
     */
    private String description;
    /**
     * 源主机Id
     */
    private Integer sourceHostId;
    /**
     * 派单号
     */
    private String instructionId;
    /**
     * 派单状态
     */
    private Integer instructionStatus;
    /**
     * 告警标准化Id
     */
    private Integer standardAlarmNameId;
    /**
     * 告警标准名
     */
    private String standardAlarmName;
    /**
     * 基类ID
     */
    private Long baseTypeId;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 设备种类ID
     */
    private Integer equipmentCategory;
    /**
     * 设备种类名
     */
    private String equipmentCategoryName;
    /**
     * 工程状态
     */
    private Integer maintainState;
    /**
     * 对应信号Id
     */
    private Integer signalId;
    /**
     * 关联告警流水号
     */
    private String relateSequenceId;
    /**
     * 告警分类ID
     */
    private Integer eventCategoryId;
    /**
     * 告警工程状态
     */
    private Integer eventStateId;
    /**
     * 中心ID
     */
    private Integer centerId;
    /**
     * 中心名
     */
    private String centerName;
    /**
     * 分组名
     */
    private String structureName;
    /**
     * 监控单元名称
     */
    private String monitorUnitName;
    /**
     * 分组ID
     */
    private Integer structureId;
    /**
     * 局站类型
     */
    private Integer stationCategoryId;
    /**
     * 设备厂商
     */
    private String equipmentVendor;
    /**
     * 写入时间
     */
    private Date insertTime;
    /**
     * 层级ID
     */
    private Integer resourceStructureId;
    /**
     * 设备基类ID
     */
    private Integer baseEquipmentId;

    /**
     * 设备基类名称
     */
    @TableField(exist = false)
    private String baseEquipmentName;

    /**
     * 结束触发值
     */
    private Double endValue;
    /**
     * 告警原因类型
     */
    private Integer eventReasonType;

    @Override
    public String toString() {
        return String.format("AlarmChange: %d.%d.%d.%d %s-%s-%s-%s %s %s %.2f %s %s %d %d %s", this.getStationId(), this.getEquipmentId(), this.getEventId(), this.getEventConditionId(),
                DateUtil.dateToString(this.getStartTime()), this.getEndTime() != null ? DateUtil.dateToString(this.getEndTime()) : "null", this.getConfirmTime() != null ? DateUtil.dateToString(this.getConfirmTime()) : "null", this.getCancelTime() != null ? DateUtil.dateToString(this.getCancelTime()) : "null",
                this.getEventSeverity(), this.getMeanings(), this.getEventValue(), this.getBaseTypeId(),
                this.getSequenceId(), this.getSerialNo(), this.getOperationType(), this.getInsertTime() != null ? DateUtil.dateToString(this.getInsertTime()) : "null");
    }

    public ActiveEvent toActiveEvent() {
        ActiveEvent activeEvent = new ActiveEvent();
        activeEvent.setSequenceId(this.getSequenceId());
        activeEvent.setStationId(this.getStationId());
        activeEvent.setStationName(this.getStationName());
        activeEvent.setEquipmentId(this.getEquipmentId());
        activeEvent.setEquipmentName(this.getEquipmentName());
        activeEvent.setEventId(this.getEventId());
        activeEvent.setEventName(this.getEventName());
        activeEvent.setEventConditionId(this.getEventConditionId());
        activeEvent.setEventLevel(this.getEventLevel());
        activeEvent.setEventSeverity(this.getEventSeverity());
        activeEvent.setStartTime(this.getStartTime());
        activeEvent.setEndTime(this.getEndTime());
        activeEvent.setCancelTime(this.getCancelTime());
        activeEvent.setCancelUserId(this.getCancelUserId());
        activeEvent.setCancelUserName(this.getCancelUserName());
        activeEvent.setConfirmTime(this.getConfirmTime());
        activeEvent.setConfirmerId(this.getConfirmerId());
        activeEvent.setConfirmerName(this.getConfirmerName());
        activeEvent.setEventValue(this.getEventValue());
        activeEvent.setReversalNum(this.getReversalNum());
        activeEvent.setMeanings(this.getMeanings());
        activeEvent.setEventFilePath(this.getEventFilePath());
        activeEvent.setDescription(this.getDescription());
        activeEvent.setSourceHostId(this.getSourceHostId());
        activeEvent.setInstructionId(this.getInstructionId());
        activeEvent.setInstructionStatus(this.getInstructionStatus());
        activeEvent.setStandardAlarmNameId(this.getStandardAlarmNameId());
        activeEvent.setStandardAlarmName(this.getStandardAlarmName());
        activeEvent.setBaseTypeId(this.getBaseTypeId());
        activeEvent.setBaseTypeName(this.getBaseTypeName());
        activeEvent.setEquipmentCategory(this.getEquipmentCategory());
        activeEvent.setEquipmentCategoryName(this.getEquipmentCategoryName());
        activeEvent.setMaintainState(this.getMaintainState());
        activeEvent.setSignalId(this.getSignalId());
        activeEvent.setRelateSequenceId(this.getRelateSequenceId());
        activeEvent.setEventCategoryId(this.getEventCategoryId());
        activeEvent.setEventStateId(this.getEventStateId());
        activeEvent.setCenterId(this.getCenterId());
        activeEvent.setCenterName(this.getCenterName());
        activeEvent.setStructureName(this.getStructureName());
        activeEvent.setMonitorUnitName(this.getMonitorUnitName());
        activeEvent.setStructureId(this.getStructureId());
        activeEvent.setStationCategoryId(this.getStationCategoryId());
        activeEvent.setEquipmentVendor(this.getEquipmentVendor());
        activeEvent.setResourceStructureId(this.getResourceStructureId());
        activeEvent.setBaseEquipmentId(this.getBaseEquipmentId());
        activeEvent.setBaseEquipmentName(this.getBaseEquipmentName());
        activeEvent.setEndValue(this.getEndValue());
        activeEvent.setEventSeverityId(this.eventSeverityId);
        activeEvent.setEventReasonType(this.eventReasonType);
        return activeEvent;
    }
}
