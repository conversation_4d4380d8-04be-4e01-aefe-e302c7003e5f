package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tblEventcondition info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:53
 */
@Data
@NoArgsConstructor
@TableName("tbl_EventCondition")
public class EventCondition {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 告警条件ID
	 */
	private Integer eventConditionId;
	/**
	 * 设备模板ID
	 */
	private Integer equipmentTemplateId;
	/**
	 * 告警ID
	 */
	private Integer eventId;
	/**
	 * 开始运算符
	 */
	private String startOperation;
	/**
	 * 开始比较值
	 */
	private Double startCompareValue;
	/**
	 * 开始延时
	 */
	private Integer startDelay;
	/**
	 * 结束运算符
	 */
	private String endOperation;
	/**
	 * 结束比较值
	 */
	private Double endCompareValue;
	/**
	 * 结束延时
	 */
	private Integer endDelay;
	/**
	 * 频次告警次数
	 */
	private Integer frequency;
	/**
	 * 频次告警时间
	 */
	private Integer frequencyThreshold;
	/**
	 * 告警涵义
	 */
	private String meanings;
	/**
	 * 告警设备状态
	 */
	private Integer equipmentState;
	/**
	 * 标准化ID
	 */
	private Long baseTypeId;
	/**
	 * 告警等级ID
	 */
	private Integer eventSeverity;
	/**
	 * 标准化ID（旧版，已经不用）
	 */
	private Integer standardName;
}
