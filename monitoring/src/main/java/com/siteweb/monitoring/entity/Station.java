package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * tblStation info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:41
 */
@Data
@NoArgsConstructor
@TableName("tbl_station")
public class Station {

	/**
	 * 
	 */
	 @TableId(value="stationId", type = IdType.INPUT)
	private Integer stationId;
	/**
	 * 局站名
	 */
	private String stationName;
	/**
	 * 经度
	 */
	private Double latitude;
	/**
	 * 纬度
	 */
	private Double longitude;
	/**
	 * 建站时间
	 */
	private Date setupTime;
	/**
	 * 代维公司ID
	 */
	private Integer companyId;
	/**
	 * 基站在线状态
	 */
	private Integer connectState;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 局站类型
	 */
	private Integer stationCategory;
	/**
	 * 局站等级
	 */
	private Integer stationGrade;
	/**
	 * 局站状态（工程，在建，联网）
	 */
	private Integer stationState;
	/**
	 * 联系人ID
	 */
	private Integer contactId;
	/**
	 * 电池支撑时间
	 */
	private Integer supportTime;
	/**
	 * 油机在途时间
	 */
	private Double onWayTime;
	/**
	 * 电池剩余时间
	 */
	private Double surplusTime;
	/**
	 * 楼层
	 */
	private String floorNo;
	/**
	 * 属性列表（来自资管系统）
	 */
	private String propList;
	/**
	 * 建筑面积
	 */
	private Double acreage;
	/**
	 * 建筑类型
	 */
	private Integer buildingType;
	/**
	 * 是否包含节点
	 */
	private Boolean containnode;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 负载数
	 */
	private Integer bordNumber;
	/**
	 * 所属中心
	 */
	private Integer centerId;
	/**
	 * 是否可用
	 */
	private Boolean enable;
	/**
	 * 工程开始时间
	 */
	private Date startTime;
	/**
	 * 工程结束时间
	 */
	private Date endTime;
	/**
	 * 工程名
	 */
	private String projectName;
	/**
	 * 合同号
	 */
	private String contractNo;
	/**
	 * 安装时间
	 */
	private Date installTime;
}
