package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * TBL_House info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-29 16:38:53
 */
@Data
@NoArgsConstructor
@TableName("tbl_house")
public class House {
//    @TableId(value = "id", type = IdType.INPUT)
//    private Integer id;
    /**
     * 局房名
     */
    private String houseName;
    /**
     * 局站ID
     */
    private Integer stationId;
    @TableField(exist = false)
    private String stationName;
    /**
     * 局房ID
     */
    private Integer houseId;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 更新时间
     */
    private Date lastUpdateDate;
    @TableField(exist = false)
    private Integer structureId;
}
