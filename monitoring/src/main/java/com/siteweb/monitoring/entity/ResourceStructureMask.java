package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ResourceStructureMask info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:39:26
 */
@Data
@NoArgsConstructor
@TableName("ResourceStructureMask")
public class ResourceStructureMask {

	/**
	 * 层级ID
	 */
	 @TableId(value="resourceStructureId", type = IdType.INPUT)
	private Integer resourceStructureId;
	/**
	 * 时间组ID
	 */
	private Integer timeGroupId;
	/**
	 * 屏蔽原因
	 */
	private String reason;
	/**
	 * 屏蔽开始时间
	 */
	private Date startTime;
	/**
	 * 屏蔽结束时间
	 */
	private Date endTime;
	/**
	 * 屏蔽人
	 */
	private Integer userId;
}
