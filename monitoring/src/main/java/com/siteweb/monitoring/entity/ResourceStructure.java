package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * resourceStructure info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-29 16:39:26
 */
@Data
@NoArgsConstructor
@TableName(value = "ResourceStructure",autoResultMap = true)
public class ResourceStructure {
	public ResourceStructure(ResourceStructure resourceStructure) {
		this.resourceStructureId = resourceStructure.resourceStructureId;
		this.sceneId = resourceStructure.sceneId;
		this.structureTypeId = resourceStructure.structureTypeId;
		this.resourceStructureName = resourceStructure.resourceStructureName;
		this.parentResourceStructureId = resourceStructure.parentResourceStructureId;
		this.photo = resourceStructure.photo;
		this.position = resourceStructure.position;
		this.levelOfPath = resourceStructure.levelOfPath;
		this.display = resourceStructure.display;
		this.sortValue = resourceStructure.sortValue;
		this.extendedField = resourceStructure.extendedField;
		this.originId = resourceStructure.originId;
		this.originParentId = resourceStructure.originParentId;
	}

	/**
	 * 层级ID
	 */
	 @TableId(value="resourceStructureId", type = IdType.AUTO)
	private Integer resourceStructureId;
	/**
	 * 场景ID
	 */
	private Integer sceneId;
	/**
	 * 资源组类型
	 */
	private Integer structureTypeId;
	/**
	 * 分组名
	 */
	private String resourceStructureName;
	/**
	 * 父分组Id
	 */
	private Integer parentResourceStructureId;
	/**
	 * 图片
	 */
	private String photo;
	/**
	 * 位置信息
	 */
	private String position;
	/**
	 * 连接路径
	 */
	private String levelOfPath;
	/**
	 * 是否显示
	 */
	private Integer display;
	/**
	 * 排序Index
	 */
	private Integer sortValue;
	/**
	 * 扩展信息
	 */
	//@TableField(typeHandler = JsonNodeTypeHandler.class)
	private JsonNode extendedField;
	/**
	 * 源对象ID
	 */
	private Integer originId;

	/**
	 * 源对象ID
	 */
	private Integer originParentId;

	@TableField(exist = false)
	List<ResourceStructure> children;

	public List<ResourceStructure> getChildren() {
		if(children == null){
			children = new ArrayList<>();
		}
		return children;
	}

}
