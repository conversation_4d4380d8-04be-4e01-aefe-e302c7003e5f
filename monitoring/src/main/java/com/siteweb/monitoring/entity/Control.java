package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tblControl info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:39:11
 */
@Data
@NoArgsConstructor
@TableName("tbl_control")
public class Control {

	/**
	 * 
	 */
	@TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 模板ID
	 */
	private Integer equipmentTemplateId;
	/**
	 * 控制ID
	 */
	private Integer controlId;
	/**
	 * 控制名
	 */
	private String controlName;
	/**
	 * 控制类型
	 */
	private Integer controlCategory;
	/**
	 * 控制命令串
	 */
	private String cmdToken;
	/**
	 * 控制基类ID
	 */
	private Long baseTypeId;
	/**
	 * 控制等级
	 */
	private Integer controlSeverity;
	/**
	 * 对应信号ID
	 */
	private Integer signalId;
	/**
	 * 超时时间
	 */
	private Integer timeOut;
	/**
	 * 重试次数
	 */
	private Integer retry;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 是否可用
	 */
	private Boolean enable;
	/**
	 * 是否可见
	 */
	private Boolean visible;
	/**
	 * 显示顺序
	 */
	private Integer displayIndex;
	/**
	 * 命令类型
	 */
	private Integer commandType;
	/**
	 * 控制类型
	 */
	private Integer controlType;
	/**
	 * 数据类型
	 */
	private Integer dataType;
	/**
	 * 最大值
	 */
	@TableField("`maxValue`")
	private Double maxValue;
	/**
	 * 最小值
	 */
	private Double minvalue;
	/**
	 * 默认值
	 */
	private Double defaultValue;
	/**
	 * 模块号
	 */
	private Integer moduleNo;


}
