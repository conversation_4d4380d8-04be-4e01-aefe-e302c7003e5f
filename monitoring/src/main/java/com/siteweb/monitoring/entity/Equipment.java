package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * tblEquipment info table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-29 16:38:53
 */
@Data
@NoArgsConstructor
@TableName("tbl_equipment")
public class Equipment {

    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    @TableId(value = "equipmentId", type = IdType.INPUT)
    private Integer equipmentId;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 设备编码
     */
    private String equipmentNo;
    /**
     * 设备模块
     */
    private String equipmentModule;
    /**
     * 设备型号
     */
    private String equipmentStyle;
    /**
     * 资产状态
     */
    private Integer assetState;
    /**
     * 资产价格
     */
    private Double price;
    /**
     * 资产寿命
     */
    private Double usedLimit;
    /**
     * 启用时间
     */
    private Date usedDate;
    /**
     * 购买日期
     */
    private Date buyDate;
    /**
     * 设备厂商
     */
    private String vendor;
    /**
     * 设备单位
     */
    private String unit;
    /**
     * 设备类型（非标准）
     */
    private Integer equipmentCategory;
    /**
     * 设备分类（自诊断，虚拟设备等）
     */
    private Integer equipmentType;
    /**
     * 设备大类
     */
    private Integer equipmentClass;
    /**
     * 设备状态(用于电池)
     */
    private Integer equipmentState;
    /**
     * 告警抑制表达式
     */
    private String eventExpression;
    /**
     * 告警开始延时
     */
    private Double startDelay;
    /**
     * 告警结束延时
     */
    private Double endDelay;
    /**
     * 设备属性
     */
    private String property;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    /**
     * 基类设备ID
     */
    @TableField(exist = false)
    private Integer equipmentBaseType;
    /**
     * 局房ID
     */
    private Integer houseId;
    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;
    /**
     * 监控主机ID
     */
    private Integer workStationId;
    /**
     * 采集单元ID
     */
    private Integer samplerUnitId;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 设备连接状态
     */
    private Integer connectState;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 父设备ID
     */
    private String parentEquipmentId;
    /**
     * 额定容量
     */
    private String ratedCapacity;
    /**
     * 装机模块
     */
    private String installedModule;
    /**
     * 工程名
     */
    private String projectName;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 安装时间
     */
    private Date installTime;
    /**
     * 设备条码
     */
    private String equipmentSn;
    /**
     * 采集协议库
     */
    private String so;

    /**
     * 层级ID
     */
    private Integer resourceStructureId;

    /**
     * 设备拓展属性
     */
    private JsonNode extValue;

    /**
     * 层级名称
     */
    @TableField(exist = false)
    private String resourceStructureName;

    /**
     * 设备基类名称
     */
    @TableField(exist = false)
    private String equipmentBaseTypeName;

    /**
     * 设备图片
     */
    private String photo;
}
