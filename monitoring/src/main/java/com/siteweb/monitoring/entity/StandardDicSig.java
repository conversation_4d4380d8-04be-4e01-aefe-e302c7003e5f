package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_StandardDicSig")
public class StandardDicSig {
    private Integer standardDicId;
    private Integer standardType;
    private Integer equipmentLogicClassId;
    private String equipmentLogicClass;
    private Integer signalLogicClassId;
    private String signalLogicClass;
    private String signalStandardName;
    private String netManageId;
    private Integer storeInterval;
    private Double absValueThreshold;
    private Integer statisticsPeriod;
    private Double percentThreshold;
    private Integer stationCategory;
    private Integer modifyType;
    private String description;
    private String extendFiled1;
    private String extendFiled2;
}
