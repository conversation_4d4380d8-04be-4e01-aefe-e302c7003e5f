package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description AlarmMaskLog
 * @createTime 2022-05-14 11:15:42
 */
@Data
@NoArgsConstructor
@TableName("alarmmasklog")
public class AlarmMaskLog {

    @TableId(value = "Id", type = IdType.AUTO)
    private Long id;

    private Integer stationId;

    private Integer equipmentId;

    private Integer eventId;

    private Integer resourceStructureId;

    private Integer userId;

    private Integer operationType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    private Integer timeGroupCategory;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    private String timeGroupChars;

    private String comment;
}
