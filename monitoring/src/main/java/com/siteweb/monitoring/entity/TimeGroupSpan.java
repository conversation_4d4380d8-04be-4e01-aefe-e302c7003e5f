package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description TimeGroupSpan
 * @createTime 2022-07-08 13:04:23
 */
@Data
@NoArgsConstructor
@TableName("tbl_timegroupspan")
public class TimeGroupSpan {
    public TimeGroupSpan(Integer timeSpanId, Integer timeGroupId, Integer week, String timeSpanChar) {
        this.timeSpanId = timeSpanId;
        this.timeGroupId = timeGroupId;
        this.week = week;
        this.timeSpanChar = timeSpanChar;
        this.lastUpdateDate = new Date();
    }

    @TableId(value = "timeSpanId", type = IdType.INPUT)
    private Integer timeSpanId;

    private Integer timeGroupId;

    private Date startTime;

    private Date endTime;

    private Integer week;

    private String timeSpanChar;

    private Date lastUpdateDate;
}
