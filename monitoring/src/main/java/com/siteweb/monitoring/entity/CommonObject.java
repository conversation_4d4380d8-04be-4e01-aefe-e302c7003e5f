package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@TableName("CommonObject")
public class CommonObject {
    /**
     * 通用对象id
     */
    @TableId(type = IdType.AUTO)
    private Integer commonObjectId;
    /**
     * 通用对象名称
     */
    @NotBlank(message = "通用对象名称必填")
    private String commonObjectName;
    /**
     * 描述
     */
    private String description;
    /**
     * 图片
     */
    private String photo;
}
