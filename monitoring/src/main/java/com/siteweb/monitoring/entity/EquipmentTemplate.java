package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Equipmenttemplate info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:53
 */
@Data
@NoArgsConstructor
@TableName("tbl_EquipmentTemplate")
public class EquipmentTemplate {

	/**
	 * 
	 */
	 @TableId(value="equipmentTemplateId", type = IdType.INPUT)
	private Integer equipmentTemplateId;
	/**
	 * 设备模板名
	 */
	private String equipmentTemplateName;
	/**
	 * 父模板Id
	 */
	private Integer parentTemplateId;
	/**
	 * 备注信息
	 */
	private String memo;
	/**
	 * 协议编码MD5
	 */
	private String protocolCode;
	/**
	 * 设备类型ID（内部）
	 */
	private Integer equipmentCategory;
	/**
	 * 设备分类（自诊断，虚拟设备等)
	 */
	private Integer equipmentType;
	/**
	 * 设备类型
	 */
	private String property;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 设备型号
	 */
	private String equipmentStyle;
	/**
	 * 设备单位
	 */
	private String unit;
	/**
	 * 设备厂商
	 */
	private String vendor;
	/**
	 * 图片
	 */
	private String photo;
	/**
	 * 基类设备ID
	 */
	private Integer equipmentBaseType;
	/**
	 * 局站类型(有些模板只能用在机房或者节点)
	 */
	private Integer stationCategory;
}
