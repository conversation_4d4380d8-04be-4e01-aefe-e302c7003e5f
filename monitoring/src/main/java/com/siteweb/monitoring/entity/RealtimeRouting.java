package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("tsl_realtimerouting")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RealtimeRouting {

    /**
     * 数据服务器id
     */
    private Integer dataServerId;
    /**
     * 监控单元id
     */
    private Integer monitorUnitId;
}
