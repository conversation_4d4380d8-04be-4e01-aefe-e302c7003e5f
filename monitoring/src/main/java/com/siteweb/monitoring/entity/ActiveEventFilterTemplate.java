package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterTemplate
 * @createTime 2022-04-27 17:07:09
 */
@Data
@NoArgsConstructor
@TableName("activeeventfiltertemplate")
public class ActiveEventFilterTemplate {

    @TableId(value = "activeEventFilterTemplateId", type = IdType.AUTO)
    private Integer activeEventFilterTemplateId;

    private Integer userId;

    private String filterType;

    private String templateName;

    private String content;

    private String description;
}
