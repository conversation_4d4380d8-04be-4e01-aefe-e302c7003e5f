package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * tslSamplerunit info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:36:02
 */
@Data
@NoArgsConstructor
@TableName("tsl_SamplerUnit")
public class SamplerUnit {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 采集单元ID
	 */
	private Integer samplerUnitId;
	/**
	 * 端口ID
	 */
	private Integer portId;
	/**
	 * 监控单元ID
	 */
	private Integer monitorUnitId;
	/**
	 * 采集器ID
	 */
	private Integer samplerId;
	/**
	 * 父采集单元ID
	 */
	private Integer parentSamplerUnitId;
	/**
	 * 采集器类型
	 */
	private Integer samplerType;
	/**
	 * 采集单元名称
	 */
	private String samplerUnitName;
	/**
	 * 采集单元地址
	 */
	private Integer address;
	/**
	 * 采集周期
	 */
	private Double spUnitInterval;
	/**
	 * 采集库地址
	 */
	private String dllPath;
	/**
	 * 采集单元连接状态
	 */
	private Integer connectState;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 电话号码
	 */
	private String phoneNumber;
	/**
	 * 描述信息
	 */
	private String description;

	public  String getParentKey(){
		return  monitorUnitId+"."+portId;
	}
}
