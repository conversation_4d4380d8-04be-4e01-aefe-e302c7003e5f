package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("TBL_EquipmentBranch")
public class EquipmentBranch {
    /**
     * 主键id自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分路id
     */
    private Integer branchId;

    /**
     * 支路名称
     */
    private String branchName;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
}
