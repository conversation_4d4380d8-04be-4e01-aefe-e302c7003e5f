package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tblSignalmeanings info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:41
 */
@Data
@NoArgsConstructor
@TableName("tbl_SignalMeanings")
public class SignalMeanings {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 设备模板ID
	 */
	private Integer equipmentTemplateId;
	/**
	 * 信号Id
	 */
	private Integer signalId;
	/**
	 * 信号值
	 */
	private Integer stateValue;
	/**
	 * 信号涵义
	 */
	private String meanings;
	/**
	 * 基类ID
	 */
	private Long baseCondId;
}
