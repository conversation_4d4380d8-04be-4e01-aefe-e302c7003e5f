package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("c_configchangemicrolog")
public class CConfigChangeMicroLog {
    private String objectId;
    private Integer configId;
    private Integer editType;
    private Date updateTime;
}
