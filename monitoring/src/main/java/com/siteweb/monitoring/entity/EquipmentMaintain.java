package com.siteweb.monitoring.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_EquipmentMaintain")
public class EquipmentMaintain {
    /**
     * 局站Id
     */
    private Integer stationId;
    /**
     * 设备Id
     */
    @TableId(value = "equipmentId", type = IdType.INPUT)
    private Integer equipmentId;
    /**
     * 设备状态
     */
    private Integer equipmentState;
    /**
     * 开始时间
     */
    private Date    startTime;
    /**
     * 结束时间
     */
    private Date    endTime;
    /**
     * 用户Id
     */
    private Integer userId;
    /**
     * 描述信息
     */
    private String  description;
    /**
     * 扩展信息
     */
    private String  extendFiled1;
}
