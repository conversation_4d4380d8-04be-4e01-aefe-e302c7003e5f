package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.siteweb.common.serializer.DoubleAllowNullSerializer;
import com.siteweb.common.serializer.DoubleNonNullSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 历史告警表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-08 15:09:21
 */
@Data
@NoArgsConstructor
@TableName("tbl_historyevent")
public class HistoryEvent {

	/**
	 * 
	 */
	 @TableId(value="sequenceId", type = IdType.INPUT)
	private String sequenceId;
	/**
	 * 局站ID
	 */
	private Integer stationId;
	/**
	 * 局站名
	 */
	private String stationName;
	/**
	 * 设备ID
	 */
	private Integer equipmentId;
	/**
	 * 设备名
	 */
	private String equipmentName;
	/**
	 * 事件ID
	 */
	private Integer eventId;
	/**
	 * 事件名
	 */
	private String eventName;
	/**
	 * 条件ID
	 */
	private Integer eventConditionId;
	/**
	 * 告警等级ID
	 */
	private Integer eventLevel;
	/**
	 * 告警等级名
	 */
	private String eventSeverity;
	/**
	 * 告警等级ID (0-四级，1-三级，2-二级，3-一级)
	 */
	private Integer eventSeverityId;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 取消时间
	 */
	private Date cancelTime;
	/**
	 * 取消人Id
	 */
	private Integer cancelUserId;
	/**
	 * 取消人名
	 */
	private String cancelUserName;
	/**
	 * 确认时间
	 */
	private Date confirmTime;
	/**
	 * 确认人ID
	 */
	private Integer confirmerId;
	/**
	 * 确认人名
	 */
	private String confirmerName;
	/**
	 * 触发值
	 */
	@JsonSerialize(using = DoubleNonNullSerializer.class)
	private Double eventValue;
	/**
	 * 翻转次数
	 */
	private Integer reversalNum;
	/**
	 * 告警含义
	 */
	private String meanings;
	/**
	 * 告警录像文件路径
	 */
	private String eventFilePath;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 源主机Id
	 */
	private Integer sourceHostId;
	/**
	 * 派单号
	 */
	private String instructionId;
	/**
	 * 派单状态
	 */
	private Integer instructionStatus;
	/**
	 * 告警标准化Id
	 */
	private Integer standardAlarmNameId;
	/**
	 * 告警标准名
	 */
	private String standardAlarmName;
	/**
	 * 基类ID
	 */
	private Long baseTypeId;
	/**
	 * 基类名
	 */
	private String baseTypeName;
	/**
	 * 设备种类ID
	 */
	private Integer equipmentCategory;
	/**
	 * 设备种类名
	 */
	private String equipmentCategoryName;
	/**
	 * 工程状态
	 */
	private Integer maintainState;
	/**
	 * 对应信号Id
	 */
	private Integer signalId;
	/**
	 * 关联告警流水号
	 */
	private String relateSequenceId;
	/**
	 * 告警分类ID
	 */
	private Integer eventCategoryId;
	/**
	 * 告警工程状态
	 */
	private Integer eventStateId;
	/**
	 * 中心ID
	 */
	private Integer centerId;
	/**
	 * 中心名
	 */
	private String centerName;
	/**
	 * 分组名
	 */
	private String structureName;
	/**
	 * 监控单元名称
	 */
	private String monitorUnitName;
	/**
	 * 分组ID
	 */
	private Integer structureId;
	/**
	 * 局站类型
	 */
	private Integer stationCategoryId;
	/**
	 * 设备厂商
	 */
	private String equipmentVendor;

	/**
	 * 结束触发值
	 */
	@JsonSerialize(using = DoubleAllowNullSerializer.class)
	private Double endValue;

	/**
	 * 层级ID
	 */
	private Integer resourceStructureId;

	/**
	 * 设备基类ID
	 */
	private Integer baseEquipmentId;

	/**
	 * 设备基类名
	 */
	@TableField(exist = false)
	private String baseEquipmentName;

	/**
	 * 收敛的事件Iｄ
	 */
	private Long  convergenceEventId;
	/**
	 * 告警原因类型
	 */
	private Integer eventReasonType;
}
