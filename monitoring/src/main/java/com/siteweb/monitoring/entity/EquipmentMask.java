package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Equipment Mask Record table
 *
 * <AUTHOR>
 * @email
 * @date 2021-12-29 16:38:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_EquipmentMask")
@Builder
public class EquipmentMask {

    /**
     * 设备ID
     */
    @TableId(value = "equipmentId", type = IdType.INPUT)
    private Integer equipmentId;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 时间分组ID
     */
    private Integer timeGroupId;
    /**
     * 屏蔽原因
     */
    private String reason;
    /**
     * 屏蔽开始时间
     */
    private Date startTime;
    /**
     * 屏蔽结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人ID
     */
    private Integer userId;
}
