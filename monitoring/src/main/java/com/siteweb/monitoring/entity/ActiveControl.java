package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 活动告警表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-08 15:09:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_activecontrol")
@Builder
public class ActiveControl {

	/**
	 * 局站ID
	 */
	private Integer stationId;
	/**
	 * 局站名
	 */
	private String stationName;
	/**
	 * 设备ID
	 */
	private Integer equipmentId;
	/**
	 * 设备名
	 */
	private String equipmentName;
	/**
	 * 控制命令ID
	 */
	private Integer controlId;
	/**
	 * 控制命令名
	 */
	private String controlName;
	/**
	 * 
	 */
	 @TableId(value="serialNo", type = IdType.AUTO)
	private Integer serialNo;
	/**
	 * 控制等级
	 */
	private Integer controlSeverity;
	/**
	 * 控制命令串
	 */
	private String cmdToken;
	/**
	 * 控制阶段
	 */
	private Integer controlPhase;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 确认时间
	 */
	private Date confirmTime;
	/**
	 * 确认人ID
	 */
	private Integer confirmerId;
	/**
	 * 确认人名
	 */
	private String confirmerName;
	/**
	 * 控制结果ID
	 */
	private Integer controlResultType;
	/**
	 * 控制结果
	 */
	private String controlResult;
	/**
	 * 执行人Id
	 */
	private Integer controlExecuterId;
	/**
	 * 执行人名
	 */
	private String controlExecuterIdName;
	/**
	 * 控制类型
	 */
	private Integer controlType;
	/**
	 * 联动ID
	 */
	private Integer actionId;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 重试次数
	 */
	private Integer retry;
	/**
	 * 控制基类ID
	 */
	private Long baseTypeId;
	/**
	 * 控制基类名
	 */
	private String baseTypeName;
	/**
	 * 控制参数
	 */
	private String parameterValues;
	/**
	 * 基类参数Id
	 */
	private Long baseCondId;

	/**
	 * 层级ID
	 */
	@TableField(exist = false)
	private Integer resourceStructureId;
}
