package com.siteweb.monitoring.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tslSampler info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:36:02
 */
@Data
@NoArgsConstructor
@TableName("tsl_sampler")
public class Sampler {

	/**
	 * 
	 */
	 @TableId(value="samplerId", type = IdType.AUTO)
	private Integer samplerId;
	/**
	 * 采集器名称
	 */
	private String samplerName;
	/**
	 * 采集器类型
	 */
	private Integer samplerType;
	/**
	 * 协议MD5码
	 */
	private String protocolCode;
	/**
	 * 采集库MD5码
	 */
	private String dllCode;
	/**
	 * 协议版本
	 */
	private String dllVersion;
	/**
	 * 协议文件路径
	 */
	private String protocolFilePath;
	/**
	 * 采集器路径
	 */
	private String dllFilePath;
	/**
	 * 采集器路径
	 */
	private String dllPath;
	/**
	 * 采集设置（端口号波特率等）
	 */
	private String setting;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * sow文件编码
	 */
	private String soCode;
	/**
	 * sow文件路径
	 */
	private String soPath;
}
