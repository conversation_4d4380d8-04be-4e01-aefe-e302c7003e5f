package com.siteweb.monitoring.vo;

import com.siteweb.monitoring.entity.ActiveEventFilterTemplate;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterTemplateVO
 * @createTime 2022-04-27 17:25:17
 */
@Data
@NoArgsConstructor
public class ActiveEventFilterTemplateVO {

    private Integer activeEventFilterTemplateId;

    private String filterType;

    private String templateName;

    private String content;

    private String description;

    private Integer personId;

    public ActiveEventFilterTemplate build(String filterType, Integer userId) {
        ActiveEventFilterTemplate activeEventFilterTemplate = new ActiveEventFilterTemplate();
        BeanUtils.copyProperties(this, activeEventFilterTemplate, "personId");
        // 关注信号、条件屏蔽下的设备屏蔽和告警屏蔽，以上3个模板有共享选项，personId传入userId，personId=null表示共享
        if (filterType.equals("SignalViewFilter") || filterType.equals("MaskTemplate") || filterType.equals("MaskTemplateEv") || "UbitTemplate".equals(filterType)) {
            activeEventFilterTemplate.setUserId(this.getPersonId());
        } else {//其他模板仍是用户级别的
            activeEventFilterTemplate.setUserId(userId);
        }
        return activeEventFilterTemplate;
    }
}
