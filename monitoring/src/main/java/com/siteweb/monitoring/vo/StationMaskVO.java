package com.siteweb.monitoring.vo;

import com.siteweb.monitoring.entity.StationMask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value = "局站屏蔽", description = "局站屏蔽实体")
public class StationMaskVO {
    /**
     * 局站ID
     */
    @ApiModelProperty(value = "局站Id", name = "stationId")
    private Integer stationId;

    /**
     * 时间分组ID
     */
    @ApiModelProperty(value = "时间分组Id", name = "timeGroupId")
    private Integer timeGroupId;

    /**
     * 屏蔽原因
     */
    @ApiModelProperty(value = "屏蔽原因", name = "reason")
    private String reason;

    /**
     * 屏蔽开始时间
     */
    @ApiModelProperty(value = "开始时间", name = "startTime")
    private Date startTime;

    /**
     * 屏蔽结束时间
     */
    @ApiModelProperty(value = "结束时间", name = "endTime")
    private Date endTime;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;

    public StationMask build() {
        StationMask equipmentMask = new StationMask();
        equipmentMask.setStationId(this.getStationId());
        equipmentMask.setStartTime(this.getStartTime());
        equipmentMask.setEndTime(this.getEndTime());
        equipmentMask.setReason(this.getReason());
        return equipmentMask;
    }
}
