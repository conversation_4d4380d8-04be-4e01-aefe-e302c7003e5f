package com.siteweb.monitoring.vo;

import com.siteweb.utility.util.BooleanUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.monitoring.entity.ResourceStructure;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * resourceStructure info tableVO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 11:57:09
 */
@Data
@NoArgsConstructor
@ApiModel(value="ResourceStructure实体",description="ResourceStructure实体")
public class ResourceStructureVO {

	/**
	 * 
	 */
    @ApiModelProperty(value="",name="resourceStructureId")
    private Integer resourceStructureId;

    /**
     * 场景ID
     */
    @ApiModelProperty(value="场景ID",name="sceneId")
    private Integer sceneId;

    /**
     * 资源组类型
     */
    @ApiModelProperty(value="资源组类型",name="structureTypeId")
    private Integer structureTypeId;

    /**
     * 分组名
     */
    @ApiModelProperty(value="分组名",name="resourceStructureName")
    private String resourceStructureName;

    /**
     * 父分组Id
     */
    @ApiModelProperty(value="父分组Id",name="ParentResourceStructureId")
    private Integer ParentResourceStructureId;

    /**
     * 图片
     */
    @ApiModelProperty(value="图片",name="photo")
    private String photo;

    /**
     * 位置信息
     */
    @ApiModelProperty(value="位置信息",name="position")
    private String position;

    /**
     * 连接路径
     */
    @ApiModelProperty(value="连接路径",name="levelOfPath")
    private String levelOfPath;

    /**
     * 是否显示
     */
    @ApiModelProperty(value="是否显示",name="display")
    private Boolean display;

    /**
     * 排序Index
     */
    @ApiModelProperty(value="排序Index",name="sortValue")
    private Integer sortValue;

    /**
     * 扩展信息
     */
    @ApiModelProperty(value="扩展信息",name="extendedField")
    private JsonNode extendedField;

    /**
     * 源对象ID
     */
    @ApiModelProperty(value="源对象ID",name="originObjectId")
    private Integer originId;

    /**
     * 源对象ID
     */
    @ApiModelProperty(value="源父对象ID",name="originParentId")
    private Integer originParentId;

	public ResourceStructure build() {
        ResourceStructure resourceStructure = new ResourceStructure();
        resourceStructure.setResourceStructureId (this.resourceStructureId);
        resourceStructure.setSceneId (this.sceneId);
        resourceStructure.setStructureTypeId (this.structureTypeId);
        resourceStructure.setResourceStructureName (this.resourceStructureName);
        resourceStructure.setParentResourceStructureId (this.ParentResourceStructureId);
        resourceStructure.setPhoto (this.photo);
        resourceStructure.setPosition (this.position);
        resourceStructure.setLevelOfPath (this.levelOfPath);
        resourceStructure.setDisplay (BooleanUtil.toInteger(this.display));
        resourceStructure.setSortValue (this.sortValue);
        resourceStructure.setExtendedField (this.extendedField);
        resourceStructure.setOriginId (this.originId);
        resourceStructure.setOriginParentId(this.originParentId);
        return resourceStructure;
	}
}
