package com.siteweb.monitoring.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class EquipmentFilterVo {
    /**
     * 设备名
     */
    @ApiModelProperty("设备名")
    private String eqName;
    /**
     * 层级ID
     */
    @ApiModelProperty("层级ID")
    private String rId;

    /**
     * 设备类别
     */
    @ApiModelProperty("设备类别")
    private Integer eqCategory;
    /**
     * 设备idSet
     */
    @ApiModelProperty("设备idSet")
    private Set<Integer> equipmentIds;
    @ApiModelProperty("层级idSet")
    private Set<Integer> rIds;
}
