package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description MultiEquipmentEventConditionBatchApplyVO
 * @createTime 2022-06-24 09:19:05
 */
@Data
@NoArgsConstructor
public class MultiEquipmentEventConditionBatchApplyVO {

    //0:增量应用选中的规则（新设备事件的原规则保留）
    //1:全覆盖选中的规则（新设备事件的原规则会删除）
    private Integer applyType;

    private List<EventConditionBatchApplyVO> eventConditionVOs;

    //toApplyEquipmentId的格式：StationId,EquipmentId
    private List<String> toApplyEquipmentIds;
}
