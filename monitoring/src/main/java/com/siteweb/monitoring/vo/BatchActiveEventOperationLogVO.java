package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description BatchActiveEventOperationLogVO
 * @createTime 2022-04-09 14:05:21
 */
@Data
@NoArgsConstructor
public class BatchActiveEventOperationLogVO {

    private String operation;

    private String description;

    private List<String> sequenceIds;

    private List<Integer> stationIds;

    private List<Integer> equipmentIds;

    private List<Integer> eventIds;

    private List<Integer> eventConditionIds;

    private List<Date> startTimes;
}
