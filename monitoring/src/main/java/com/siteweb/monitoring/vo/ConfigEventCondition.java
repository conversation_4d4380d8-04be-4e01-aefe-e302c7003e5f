package com.siteweb.monitoring.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConfigEventCondition {
    /**
     * 局站ID
     */
    @ApiModelProperty(value = "局站ID", name = "stationId")
    private Integer stationId;
    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", name = "equipmentId")
    private Integer equipmentId;
    /**
     * 事件ID
     */
    @ApiModelProperty(value = "事件ID", name = "eventId")
    private Integer eventId;
    /**
     * 条件ID
     */
    @ApiModelProperty(value = "条件ID", name = "eventConditionId")
    private Integer eventConditionId;
    /**
     * 基类ID
     */
    @ApiModelProperty(value = "基类设备Id", name = "baseTypeId")
    private Long baseTypeId;
    /**
     * 告警涵义
     */
    @ApiModelProperty(value = "告警涵义", name = "meanings")
    private String meanings;
    /**
     * 触发值
     */
    @ApiModelProperty(value = "触发值", name = "eventValue")
    private Double eventValue;

}
