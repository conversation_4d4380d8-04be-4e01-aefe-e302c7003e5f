package com.siteweb.monitoring.vo;

import lombok.Data;

import java.util.Collection;

@Data
public class EventRequestByCondition {
    /**
     * 层级ids
     */
    private Collection<Integer> resourceStructureIds;
    /**
     * 设备基类ids
     */
    private Collection<Integer> baseEquipmentIds;
    /**
     * 设备ids
     */
    private Collection<Integer> equipmentIds;
    /**
     * 告警基类ids
     */
    private Collection<Integer> baseTypeIds;
}
