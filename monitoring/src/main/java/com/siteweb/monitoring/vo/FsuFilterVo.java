package com.siteweb.monitoring.vo;

import com.siteweb.utility.dto.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class FsuFilterVo extends PageDTO {
    private String keywords;
    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 采集器型号
     */
    private String fsuType;
    /**
     * 硬件版本
     */
    private String hw;
    /**
     * SN码
     */
    private String sn;
    /**
     * MAC地址
     */
    private String mac;
    /**
     * IP地址
     */
    private String ip;
    /**
     * 系统版本
     */
    private String linux;
    /**
     * SiteUnit版本
     */
    private String siteVersion;
    /**
     * CPU使用率（%）
     */
    private String cpuUsage;
    /**
     * 内存使用率（%）
     */
    private String memUsage;
    /**
     * Flash使用率（%）
     */
    private String flashUsedRate;
    /**
     * 不包含的监控单元种类
     */
    private List<Integer> notMuCategoryList;
}