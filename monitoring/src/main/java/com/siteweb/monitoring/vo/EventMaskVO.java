package com.siteweb.monitoring.vo;

import com.siteweb.monitoring.entity.EventMask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * EventMask info tableVO
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 12:01:23
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EventMask实体", description = "EventMask实体")
public class EventMaskVO {

    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", name = "equipmentId")
    private Integer equipmentId;

    /**
     * 基站ID
     */
    @ApiModelProperty(value = "基站ID", name = "stationId")
    private Integer stationId;

    /**
     * 告警ID
     */
    @ApiModelProperty(value = "告警ID", name = "eventId")
    private Integer eventId;

    /**
     * 屏蔽原因
     */
    @ApiModelProperty(value = "屏蔽原因", name = "reason")
    private String reason;

    /**
     * 屏蔽开始时间
     */
    @ApiModelProperty(value = "屏蔽开始时间", name = "startTime")
    private Date startTime;

    /**
     * 屏蔽结束时间
     */
    @ApiModelProperty(value = "屏蔽结束时间", name = "endTime")
    private Date endTime;

    /**
     * 时间分组ID
     */
    private Integer timeGroupId;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;

    public EventMask build() {
        EventMask eventMask = new EventMask();
        eventMask.setEquipmentId(this.equipmentId);
        eventMask.setStationId(this.stationId);
        eventMask.setEventId(this.eventId);
        eventMask.setReason(this.reason);
        eventMask.setStartTime(this.startTime);
        eventMask.setEndTime(this.endTime);
        return eventMask;
    }
}
