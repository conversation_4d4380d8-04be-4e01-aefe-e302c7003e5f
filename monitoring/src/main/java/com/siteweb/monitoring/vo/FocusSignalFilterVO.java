package com.siteweb.monitoring.vo;

import com.siteweb.utility.dto.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description FocusSignalFilterVO
 * @createTime 2022-08-05 15:44:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class FocusSignalFilterVO extends PageDTO {

    private String resourceStructureIds;
    private String baseEquipmentIds;
    private String equipmentCategoryIds;
    private String equipmentIds;
    private String signalCategories;
    private String baseTypeIds;
    private String compareValue1Str;
    private String compareValue2Str;
    private String keywords;
}
