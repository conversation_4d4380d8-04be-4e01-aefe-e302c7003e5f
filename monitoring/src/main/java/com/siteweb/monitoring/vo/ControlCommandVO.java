package com.siteweb.monitoring.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 控制命令实体（前端传过来）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ControlCommandVO {
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 控制命令ID
     */
    private Integer controlId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 设置值
     */
    private String setValue;
    /**
     * 描述信息
     */
    private String description;

}
