package com.siteweb.monitoring.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class EventRequestBySignalId {
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Integer equipmentId;

    /**
     * 需要查询具体设备的信号的id(null查所有)
     */
    @ApiModelProperty("需要查询具体设备的信号的id")
    private List<Integer> signalIds;
}
