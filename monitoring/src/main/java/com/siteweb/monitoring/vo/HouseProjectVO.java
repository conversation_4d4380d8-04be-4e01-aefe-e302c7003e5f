package com.siteweb.monitoring.vo;

import lombok.Data;

import java.util.Date;

@Data
public class HouseProjectVO {
    /**
     * 局站Id
     */
    private Integer stationId;
    /**
     * 机房 Id
     */
    private Integer houseId;

    /**
     * 机房名称
     */
    private String houseName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date    endTime;
    /**
     * 工程原因
     */
    private String  reason;
}
