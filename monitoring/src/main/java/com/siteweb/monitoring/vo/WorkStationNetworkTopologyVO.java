package com.siteweb.monitoring.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WorkStationNetworkTopologyVO {
    private String centerName;
    private String name;
    private Integer workstationType;
    private String workstationTypeName;
    private Integer state;
    private String statusText;
    private List<WorkStationItemName> workStationItems = new ArrayList<>();

    public void addWorkStationItems(String name) {
        workStationItems.add(new WorkStationItemName(name));
    }
    @AllArgsConstructor
    @Data
    public static class WorkStationItemName{
        private String name;
    }
}
