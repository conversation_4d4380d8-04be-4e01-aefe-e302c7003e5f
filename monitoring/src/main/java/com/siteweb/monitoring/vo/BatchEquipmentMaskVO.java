package com.siteweb.monitoring.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> z<PERSON>
 * @description BatchEquipmentMaskVO
 * @createTime 2022-05-17 10:00:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchEquipmentMaskVO {

    /**
     * 局站id,设备id
     */
    private List<String> ids;

    private List<Integer> stationIds;

    private List<Integer> equipmentIds;

    private Date startTime;

    private Date endTime;

    private String reason;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;
}
