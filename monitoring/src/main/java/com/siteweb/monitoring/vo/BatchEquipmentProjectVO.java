package com.siteweb.monitoring.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchEquipmentProjectVO {
    /**
     * 设备Id
     */
    private List<Integer> equipmentIds;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 工程原因
     */
    private String reason;
}
