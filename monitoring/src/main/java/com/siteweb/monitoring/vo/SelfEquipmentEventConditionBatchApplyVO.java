package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SelfEquipmentEventConditionBatchApplyVO
 * @createTime 2022-06-23 10:33:35
 */
@Data
@NoArgsConstructor
public class SelfEquipmentEventConditionBatchApplyVO {

    //0:增量应用选中的规则（新事件的原规则保留）
    //1:全覆盖选中的规则（新事件的原规则会删除）
    private Integer applyType;

    private Integer stationId;

    private Integer equipmentId;

    private Integer signalId;

    private List<Integer> eventConditionIds;

    private List<Integer> toApplySignalIds;
}
