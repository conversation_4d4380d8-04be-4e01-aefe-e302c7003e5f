package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterVO
 * @createTime 2022-03-16 10:19:10
 */
@Data
@NoArgsConstructor
public class ActiveEventFilterVO {

    private String resourceStructureIds;
    /**
     * 设备类型ids
     */
    private String equipmentCategories;
    /**
     * 设备基类ids
     */
    private String baseEquipmentIds;
    /**
     * 设备ids
     */
    private String equipmentIds;
    /**
     * 告警等级
     */
    private String eventSeverityIds;
    /**
     * 是否确认
     */
    private Boolean eventConfirmed;
    /**
     * 是否结束
     */
    private Boolean eventEnded;
    /**
     * 是否收敛
     */
    private Boolean convergented;
    /**
     * 告警基类ids
     */
    private String baseTypeIds;
    /**
     * 关键字
     */
    private String keywords;
    /**
     * 关键字筛选列
     */
    private String focusPanelKeywords;
    /**
     * 开始时间的起始时间
     */
    private Date startTimeFrom;
    /**
     * 开始时间的结束时间
     */
    private Date startTimeTo;
    /**
     * 是否是工程态的告警
     */
    private Boolean maintainStated;
    /**
     * 设备告警ids
     * 格式为设备id.告警id 多个使用逗号割开
     */
    private String equipmentEventKeys;
    /**
     * 局站ids
     */
    private String stationIds;
    /**
     * 告警分类ID
     */
    private String eventCategoryIds;
    /**
     * 局站类型
     */
    private String stationCategoryIds;
    /**
     * 派单状态
     */
    private String instructionStatusIds;
    /**
     * 派单号
     */
    private String instructionId;
    /**
     * 标准化id
     */
    private String standardTypeEntryIds;
    /**
     * 告警注释
     */
    private String description;
    /**
     * 分组ids
     */
    private String structureIds;
}
