package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SignalBatchApplyVO
 * @createTime 2022-06-22 13:21:10
 */
@Data
@NoArgsConstructor
public class SignalBatchApplyVO {

    //0代表SignalName;1代表StoreInterval;2代表AbsValueThreshold;3代表PercentThreshold;4代表StaticsPeriod
    private List<Integer> applyItems;

    private Integer stationId;

    private Integer equipmentId;

    private List<Integer> signalIds;

    //applyEquipmentIds的格式：StationId,EquipmentId
    private List<String> applyEquipmentIds;
}
