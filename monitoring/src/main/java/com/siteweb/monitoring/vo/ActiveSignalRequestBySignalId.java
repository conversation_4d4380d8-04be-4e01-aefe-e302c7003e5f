package com.siteweb.monitoring.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class ActiveSignalRequestBySignalId {
    /**
     * 主要用于通过设备id查询所有的信号
     * @param equipmentId 设备id
     */
    public ActiveSignalRequestBySignalId(Integer equipmentId) {
        this.equipmentId = equipmentId;
    }

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Integer equipmentId;

    /**
     * 需要查询具体设备的信号的id(null查所有)
     */
    @ApiModelProperty("需要查询具体设备的信号的id(null查所有)")
    private List<Integer> signalIds;
}
