package com.siteweb.monitoring.vo;

import com.siteweb.monitoring.entity.AlarmChange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 告警变化表VO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 11:54:44
 */
@Data
@NoArgsConstructor
@ApiModel(value="AlarmChange实体",description="AlarmChange实体")
public class AlarmChangeVO {

    /**
     * 
     */
    @ApiModelProperty(value="",name="sequenceId")
    private String sequenceId;

	/**
	 * 变化流水号(自增)
	 */
    @ApiModelProperty(value="变化流水号(自增)",name="serialNo")
    private Long serialNo;

    /**
     * 操作类型（开始，结束， 确认）
     */
    @ApiModelProperty(value="操作类型（开始，结束， 确认）",name="operationType")
    private Integer operationType;

    /**
     * 局站ID
     */
    @ApiModelProperty(value="局站ID",name="stationId")
    private Integer stationId;

    /**
     * 局站名
     */
    @ApiModelProperty(value="局站名",name="stationName")
    private String stationName;

    /**
     * 设备ID
     */
    @ApiModelProperty(value="设备ID",name="equipmentId")
    private Integer equipmentId;

    /**
     * 设备名
     */
    @ApiModelProperty(value="设备名",name="equipmentName")
    private String equipmentName;

    /**
     * 事件ID
     */
    @ApiModelProperty(value="事件ID",name="eventId")
    private Integer eventId;

    /**
     * 事件名
     */
    @ApiModelProperty(value="事件名",name="eventName")
    private String eventName;

    /**
     * 条件ID
     */
    @ApiModelProperty(value="条件ID",name="eventConditionId")
    private Integer eventConditionId;

    /**
     * 告警等级ID
     */
    @ApiModelProperty(value="告警等级ID",name="eventSeverityId")
    private Integer eventSeverityId;

    /**
     * 告警等级名
     */
    @ApiModelProperty(value="告警等级名",name="eventSeverity")
    private String eventSeverity;

    /**
     * 开始时间
     */
    @ApiModelProperty(value="开始时间",name="startTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value="结束时间",name="endTime")
    private Date endTime;

    /**
     * 取消时间
     */
    @ApiModelProperty(value="取消时间",name="cancelTime")
    private Date cancelTime;

    /**
     * 取消人Id
     */
    @ApiModelProperty(value="取消人Id",name="cancelUserId")
    private Integer cancelUserId;

    /**
     * 取消人名
     */
    @ApiModelProperty(value="取消人名",name="cancelUserName")
    private String cancelUserName;

    /**
     * 确认时间
     */
    @ApiModelProperty(value="确认时间",name="confirmTime")
    private Date confirmTime;

    /**
     * 确认人ID
     */
    @ApiModelProperty(value="确认人ID",name="confirmerId")
    private Integer confirmerId;

    /**
     * 确认任命
     */
    @ApiModelProperty(value="确认任命",name="confirmerName")
    private String confirmerName;

    /**
     * 触发值
     */
    @ApiModelProperty(value="触发值",name="eventValue")
    private Double eventValue;

    /**
     * 翻转次数
     */
    @ApiModelProperty(value="翻转次数",name="reversalNum")
    private Integer reversalNum;

    /**
     * 告警涵义
     */
    @ApiModelProperty(value="告警涵义",name="meanings")
    private String meanings;

    /**
     * 告警录像文件路径
     */
    @ApiModelProperty(value="告警录像文件路径",name="eventFilePath")
    private String eventFilePath;

    /**
     * 描述信息
     */
    @ApiModelProperty(value="描述信息",name="description")
    private String description;

    /**
     * 源主机Id
     */
    @ApiModelProperty(value="源主机Id",name="sourceHostId")
    private Integer sourceHostId;

    /**
     * 派单号
     */
    @ApiModelProperty(value="派单号",name="instructionId")
    private String instructionId;

    /**
     * 派单状态
     */
    @ApiModelProperty(value="派单状态",name="instructionStatus")
    private Integer instructionStatus;

    /**
     * 告警标准化Id
     */
    @ApiModelProperty(value="告警标准化Id",name="standardAlarmNameId")
    private Integer standardAlarmNameId;

    /**
     * 告警标准名
     */
    @ApiModelProperty(value="告警标准名",name="standardAlarmName")
    private String standardAlarmName;

    /**
     * 基类ID
     */
    @ApiModelProperty(value="基类ID",name="baseTypeId")
    private Long baseTypeId;

    /**
     * 基类名
     */
    @ApiModelProperty(value="基类名",name="baseTypeName")
    private String baseTypeName;

    /**
     * 设备种类ID
     */
    @ApiModelProperty(value="设备种类ID",name="equipmentCategory")
    private Integer equipmentCategory;

    /**
     * 设备种类名
     */
    @ApiModelProperty(value="设备种类名",name="equipmentCategoryName")
    private String equipmentCategoryName;

    /**
     * 工程状态
     */
    @ApiModelProperty(value="工程状态",name="maintainState")
    private Integer maintainState;

    /**
     * 对应信号Id
     */
    @ApiModelProperty(value="对应信号Id",name="signalId")
    private Integer signalId;

    /**
     * 关联告警流水号
     */
    @ApiModelProperty(value="关联告警流水号",name="relateSequenceId")
    private String relateSequenceId;

    /**
     * 告警分类ID
     */
    @ApiModelProperty(value="告警分类ID",name="eventCategoryId")
    private Integer eventCategoryId;

    /**
     * 告警工程状态
     */
    @ApiModelProperty(value="告警工程状态",name="eventstateid")
    private Integer eventStateId;

    /**
     * 中心ID
     */
    @ApiModelProperty(value="中心ID",name="centerId")
    private Integer centerId;

    /**
     * 中心名
     */
    @ApiModelProperty(value="中心名",name="centerName")
    private String centerName;

    /**
     * 分组名
     */
    @ApiModelProperty(value="分组名",name="structureName")
    private String structureName;

    /**
     * 监控单元名称
     */
    @ApiModelProperty(value="监控单元名称",name="monitorUnitName")
    private String monitorUnitName;

    /**
     * 分组ID
     */
    @ApiModelProperty(value="分组ID",name="structureId")
    private Integer structureId;

    /**
     * 局站类型
     */
    @ApiModelProperty(value="局站类型",name="stationCategoryId")
    private Integer stationCategoryId;

    /**
     * 设备厂商
     */
    @ApiModelProperty(value="设备厂商",name="equipmentVendor")
    private String equipmentVendor;

    /**
     * 写入时间
     */
    @ApiModelProperty(value="写入时间",name="inserttime")
    private Date insertTime;

	public AlarmChange build() {
        AlarmChange alarmChange = new AlarmChange();
        alarmChange.setSequenceId (this.sequenceId);
        alarmChange.setSerialNo (this.serialNo);
        alarmChange.setOperationType (this.operationType);
        alarmChange.setStationId (this.stationId);
        alarmChange.setStationName (this.stationName);
        alarmChange.setEquipmentId (this.equipmentId);
        alarmChange.setEquipmentName (this.equipmentName);
        alarmChange.setEventId (this.eventId);
        alarmChange.setEventName (this.eventName);
        alarmChange.setEventConditionId (this.eventConditionId);
        alarmChange.setEventLevel(this.eventSeverityId);
        alarmChange.setEventSeverity (this.eventSeverity);
        alarmChange.setStartTime (this.startTime);
        alarmChange.setEndTime (this.endTime);
        alarmChange.setCancelTime (this.cancelTime);
        alarmChange.setCancelUserId (this.cancelUserId);
        alarmChange.setCancelUserName (this.cancelUserName);
        alarmChange.setConfirmTime (this.confirmTime);
        alarmChange.setConfirmerId (this.confirmerId);
        alarmChange.setConfirmerName (this.confirmerName);
        alarmChange.setEventValue (this.eventValue);
        alarmChange.setReversalNum (this.reversalNum);
        alarmChange.setMeanings (this.meanings);
        alarmChange.setEventFilePath (this.eventFilePath);
        alarmChange.setDescription (this.description);
        alarmChange.setSourceHostId (this.sourceHostId);
        alarmChange.setInstructionId (this.instructionId);
        alarmChange.setInstructionStatus (this.instructionStatus);
        alarmChange.setStandardAlarmNameId (this.standardAlarmNameId);
        alarmChange.setStandardAlarmName (this.standardAlarmName);
        alarmChange.setBaseTypeId (this.baseTypeId);
        alarmChange.setBaseTypeName (this.baseTypeName);
        alarmChange.setEquipmentCategory (this.equipmentCategory);
        alarmChange.setEquipmentCategoryName (this.equipmentCategoryName);
        alarmChange.setMaintainState (this.maintainState);
        alarmChange.setSignalId (this.signalId);
        alarmChange.setRelateSequenceId (this.relateSequenceId);
        alarmChange.setEventCategoryId (this.eventCategoryId);
        alarmChange.setEventStateId (this.eventStateId);
        alarmChange.setCenterId (this.centerId);
        alarmChange.setCenterName (this.centerName);
        alarmChange.setStructureName (this.structureName);
        alarmChange.setMonitorUnitName (this.monitorUnitName);
        alarmChange.setStructureId (this.structureId);
        alarmChange.setStationCategoryId (this.stationCategoryId);
        alarmChange.setEquipmentVendor (this.equipmentVendor);
        alarmChange.setInsertTime (this.insertTime);
        return alarmChange;
	}
}
