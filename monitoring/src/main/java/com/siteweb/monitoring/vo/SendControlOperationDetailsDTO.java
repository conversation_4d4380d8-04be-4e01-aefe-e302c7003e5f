package com.siteweb.monitoring.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SendControlOperationDetailsDTO {
    /**
     * 发送人id
     */
    private Integer sendUserId;
    /**
     * 监护人账号
     */
    private String guardianAccount;

    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 控制id
     */
    private Integer controlId;
    /**
     * 值
     */
    private String setValue;
    /**
     * 含义
     */
    private String meanings;
}
