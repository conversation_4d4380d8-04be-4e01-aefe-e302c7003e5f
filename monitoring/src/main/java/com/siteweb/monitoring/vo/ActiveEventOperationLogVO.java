package com.siteweb.monitoring.vo;

import com.siteweb.monitoring.entity.ActiveEventOperationLog;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR> z<PERSON>
 * @description ActiveEventOperationLogVO
 * @createTime 2022-04-09 15:22:33
 */
@Data
@NoArgsConstructor
public class ActiveEventOperationLogVO {

    private Integer activeEventOperationLogId;

    private String sequenceId;

    private Integer stationId;

    private Integer equipmentId;

    private Integer eventId;

    private Integer eventConditionId;

    private Date startTime;

    private String operation;

    private Date operationTime;

    private String description;

    public ActiveEventOperationLog build() {
        ActiveEventOperationLog operationLog = new ActiveEventOperationLog();
        BeanUtils.copyProperties(this, operationLog);
        return operationLog;
    }
}
