package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterVO
 * @createTime 2022-03-16 10:19:10
 */
@Data
@NoArgsConstructor
public class HistoryEventFilterVO {

    private List<Integer> resourceStructureIds;
    /**
     * 设备类型ids
     */
    private List<Integer> equipmentCategories;

    /**
     * 设备基类ids
     */
    private List<Integer> baseEquipmentIds;
    /**
     * 设备ids
     */
    private List<Integer> equipmentIds;
    /**
     * 告警等级
     */
    private List<Integer> eventSeverityIds;
    /**
     * 是否确认
     */
    private Boolean eventConfirmed;
    /**
     * 是否结束
     */
    private Boolean eventEnded;
    /**
     * 是否收敛
     */
    private Boolean convergented;
    /**
     * 告警基类ids
     */
    private List<Long> baseTypeIds;
    /**
     * 关键字
     */
    private String keywords;
    /**
     * 开始时间的起始时间
     */
    private Date startTimeFrom;
    /**
     * 开始时间的结束时间
     */
    private Date startTimeTo;
    /**
     * 是否是工程态的告警
     */
    private Boolean maintainStated;

    // 添加权限过滤字段
    private Set<Integer> permissionResourceStructureIds;
    private Set<Integer> permissionEquipmentIds;

    /**
     * 告警处理状态：1 未确认未恢复、2 未确认已恢复、3 已确认未恢复、4 已确认已恢复
     */
    private Integer handleStatus;
}
