package com.siteweb.monitoring.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description BatchEventMaskVO
 * @createTime 2022-05-17 10:22:34
 */
@Data
@NoArgsConstructor
public class BatchEventMaskVO {

    private List<Integer> stationIds;

    private List<Integer> equipmentIds;

    private List<Integer> eventIds;

    private Date startTime;

    private Date endTime;

    private String reason;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;
}
