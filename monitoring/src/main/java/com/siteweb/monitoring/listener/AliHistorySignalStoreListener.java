package com.siteweb.monitoring.listener;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/7/5
 */

import cn.hutool.core.date.DateUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.monitoring.dto.AlarmStatus;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.mamager.AliHistorySignalStoreManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 上海阿里告警历史数据存储任务，现场定制需求
 */
@Component
@Slf4j
public class AliHistorySignalStoreListener implements ApplicationListener<BaseSpringEvent<AlarmChange>> {



    @Autowired
    HAStatusService haStatusService;


    private static final Map<Integer, AlarmStatus> equipmentSaveTimeMap = new ConcurrentHashMap<>();


    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("AliHistorySignalStoreListener onApplicationEvent, HAStatus is BACKUP");
            return;
        }
        AlarmChange alarmChange = event.getData();
        if (alarmChange != null && checkBaseType(alarmChange.getEquipmentId(), alarmChange.getBaseTypeId())) {
            // 业务逻辑
            log.info("alarmChange record, equipmentId: {}, baseTypeId: {}", alarmChange.getEquipmentId(), alarmChange.getBaseTypeId());
            // 告警开始
            if (Objects.equals(alarmChange.getOperationType(), AlarmOperationTypeEnum.START.getValue())) {
                // 判断是否是该设备首次触发，equipmentSaveTimeMap中没有记录则为首次触发, value是date，默认设置为30分钟
                // 如果equipmentSaveTimeMap已存在equipmentid，则忽略
                if (equipmentSaveTimeMap.containsKey(alarmChange.getEquipmentId())) {
                    return;
                }
                AlarmStatus alarmStatus = new AlarmStatus();
                alarmStatus.setEquipmentId(alarmChange.getEquipmentId());
                alarmStatus.setStartTime(new Date().getTime());
                // 结束时间设置为开始时间加30分钟
                alarmStatus.setEndTime(new Date(System.currentTimeMillis() + AliHistorySignalStoreManager.DEFAULT_CYCLE_STORAGE_MAX_PERIOD * 1000L).getTime());
                equipmentSaveTimeMap.putIfAbsent(alarmChange.getEquipmentId(), alarmStatus);
                log.info("AliHistorySignalStoreListener onApplicationEvent, alarm start, equipmentId: {}, startTime: {}", alarmChange.getEquipmentId(), DateUtil.date(alarmStatus.getStartTime()).toString());

            } else if (Objects.equals(alarmChange.getOperationType(), AlarmOperationTypeEnum.CONFIRM.getValue())) {
                // 告警确认
                AlarmStatus alarmStatus = equipmentSaveTimeMap.get(alarmChange.getEquipmentId());
                if (alarmStatus == null) {
                    return;
                }
                alarmStatus.setEndTime(new Date(System.currentTimeMillis() + 15 * 60 * 1000).getTime());
                equipmentSaveTimeMap.put(alarmChange.getEquipmentId(), alarmStatus);
                log.info("AliHistorySignalStoreListener onApplicationEvent, alarm confirmed, equipmentId: {}, baseTypeId: {}, endTime: {}", alarmChange.getEquipmentId(), alarmChange.getBaseTypeId(), DateUtil.date(alarmStatus.getEndTime()).toString());
            }
        }
    }

    private Boolean checkBaseType(Integer equipmentId, Long baseTypeId) {
        List<Long> longs = AliHistorySignalStoreManager.equipmentIdBaseTypeIdMap.get(equipmentId);
        if (longs == null) {
            return false;
        }
        return longs.contains(baseTypeId);
    }

    // equipmentSaveTimeMap 通过key删除
    public void removeEquipmentSaveTimeMap(Integer equipmentId) {
        equipmentSaveTimeMap.remove(equipmentId);
    }

    // 获取所有的equipmentSaveTimeMap的key
    public Set<Integer> getEquipmentSaveTimeMapKeySet() {
        return equipmentSaveTimeMap.keySet();
    }

    // equipmentSaveTimeMap通过key获取value
    public AlarmStatus getEquipmentSaveTimeMapValue(Integer equipmentId) {
        return equipmentSaveTimeMap.get(equipmentId);
    }

}
