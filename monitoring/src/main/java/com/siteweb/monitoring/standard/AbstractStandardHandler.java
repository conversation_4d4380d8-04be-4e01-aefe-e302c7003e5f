package com.siteweb.monitoring.standard;

import com.siteweb.monitoring.entity.StandardDicEvent;

import java.util.Comparator;

public abstract class AbstractStandardHandler implements StandardHandler {
    protected Comparator<StandardDicEvent> compareByEquipmentAndEvent() {
        return Comparator.comparing(StandardDicEvent::getEquipmentLogicClass, Comparator.nullsFirst(Comparator.naturalOrder()))
                         .thenComparing(StandardDicEvent::getEventStandardName, Comparator.nullsFirst(Comparator.naturalOrder()));
    }
}
