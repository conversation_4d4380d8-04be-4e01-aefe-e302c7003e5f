package com.siteweb.monitoring.standard.impl;

import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.mapper.StandardDicEventMapper;
import com.siteweb.monitoring.mapper.StandardDicSigMapper;
import com.siteweb.monitoring.standard.AbstractStandardHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UniComStandardHandler extends AbstractStandardHandler {
    @Autowired
    StandardDicSigMapper sigMapper;
    @Autowired
    StandardDicEventMapper eventMapper;

    @Override
    public List<IdValueDTO<Long, String>> handleSignalStandard() {
        return sigMapper.findUniComStandardSig();
    }

    @Override
    public List<IdValueDTO<Long, String>> handleEventStandard() {
        return eventMapper.getAllStandardDicEvent().stream()
                          .filter(event -> event.getStandardType() == 3 && event.getStandardDicId() % 100 == 1)
                          .sorted(compareByEquipmentAndEvent())
                          .map(event -> new IdValueDTO<>(
                                  (long) event.getStandardDicId() / 1000,
                                  event.getEquipmentLogicClass() + "_" + event.getEventStandardName()))
                          .toList();
    }

    @Override
    public List<Signal> getSignalBySignalBaseEntryIds(List<Integer> entryIds) {
        return sigMapper.getUniComSignalIdBySignalBaseEntryIds(entryIds);
    }
}
