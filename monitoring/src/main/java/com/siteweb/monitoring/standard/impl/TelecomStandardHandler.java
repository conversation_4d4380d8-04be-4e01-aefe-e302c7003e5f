package com.siteweb.monitoring.standard.impl;

import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.entity.StandardDicEvent;
import com.siteweb.monitoring.mapper.StandardDicEventMapper;
import com.siteweb.monitoring.mapper.StandardDicSigMapper;
import com.siteweb.monitoring.standard.AbstractStandardHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TelecomStandardHandler extends AbstractStandardHandler {
    @Autowired
    StandardDicSigMapper sigMapper;
    @Autowired
    StandardDicEventMapper eventMapper;

    @Override
    public List<IdValueDTO<Long, String>> handleSignalStandard() {
        return sigMapper.findTelecomStandardSig();
    }

    @Override
    public List<IdValueDTO<Long, String>> handleEventStandard() {
        return eventMapper.getAllStandardDicEvent().stream()
                          .filter(this::isTelecomEvent)
                          .sorted(compareByEquipmentAndEvent())
                          .map(this::createTelecomDTO)
                          .toList();
    }

    @Override
    public List<Signal> getSignalBySignalBaseEntryIds(List<Integer> entryIds) {
        return sigMapper.getTelecomSignalIdBySignalBaseEntryIds(entryIds);
    }

    private boolean isTelecomEvent(StandardDicEvent event) {
        long standardDicId = event.getStandardDicId();
        long typePrefix = standardDicId / 100000000;
        return event.getStandardType() == 2
                && standardDicId % 1000 == 1
                && (typePrefix == 1 || typePrefix == 9);
    }

    private IdValueDTO<Long, String> createTelecomDTO(StandardDicEvent event) {
        String value = event.getEquipmentLogicClass() + "_" + event.getEventStandardName();
        if (event.getDescription() != null && event.getDescription().contains("有告警")) {
            value += "_" + event.getDescription();
        }
        return new IdValueDTO<>((long) ((event.getStandardDicId() % 100000000) / 1000), value);
    }
}
