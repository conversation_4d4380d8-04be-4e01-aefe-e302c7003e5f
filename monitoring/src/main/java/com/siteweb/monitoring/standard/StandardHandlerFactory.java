package com.siteweb.monitoring.standard;

import com.siteweb.monitoring.standard.impl.EmrStandardHandler;
import com.siteweb.monitoring.standard.impl.MobileStandardHandler;
import com.siteweb.monitoring.standard.impl.TelecomStandardHandler;
import com.siteweb.monitoring.standard.impl.UniComStandardHandler;
import com.siteweb.utility.constans.StandardCategoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class StandardHandlerFactory {
    @Autowired
    List<StandardHandler> standardHandlers;
    private final Map<StandardCategoryEnum, StandardHandler> handlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (StandardHandler standardHandler : standardHandlers) {
            if (standardHandler instanceof EmrStandardHandler emrStandardHandler) {
                handlerMap.put(StandardCategoryEnum.EMR, emrStandardHandler);
            }
            if (standardHandler instanceof MobileStandardHandler mobileStandardHandler) {
                handlerMap.put(StandardCategoryEnum.MOBILE, mobileStandardHandler);
            }
            if (standardHandler instanceof TelecomStandardHandler telecomStandardHandler) {
                handlerMap.put(StandardCategoryEnum.TELECOM, telecomStandardHandler);
            }
            if (standardHandler instanceof UniComStandardHandler uniComStandardHandler) {
                handlerMap.put(StandardCategoryEnum.UNICOM, uniComStandardHandler);
            }
        }
    }

    public StandardHandler getHandler(StandardCategoryEnum standardType) {
        return handlerMap.getOrDefault(standardType, handlerMap.get(StandardCategoryEnum.EMR));
    }
}
