package com.siteweb.monitoring.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.ActiveSignal;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@UtilityClass
public class FocusSignalUtil {
    public boolean matchRealTimeSignalItemByCompareValue(ActiveSignal realTimeSignalItem, List<String> compareValue1List) {
        String originalValue = realTimeSignalItem.getOriginalValue();
        if (StringUtils.isEmpty(originalValue)){
            return false;
        }
        if (compareValue1List.size() < 2) {
            return false;
        }
        String compareValue1String = compareValue1List.get(0);
        String compareValueString = compareValue1List.get(1);
        //原始值不为数值，失败
        if (!NumberUtil.isNumber(originalValue)){
            return false;
        }
        //比较值比较符不为数值，成功（用户输入无效，不执行匹配逻辑）
        if (!NumberUtil.isNumber(compareValue1String) || !NumberUtil.isNumber(compareValueString)) {
            return true;
        }
        double compareValue = Double.parseDouble(compareValueString);
        double originalValueDouble = Double.parseDouble(originalValue);

        return switch (compareValue1String) {
            case "1" -> // =
                    originalValueDouble == compareValue;
            case "2" -> // !=
                    originalValueDouble != compareValue;
            case "3" -> // >
                    originalValueDouble > compareValue;
            case "4" -> // >=
                    originalValueDouble >= compareValue;
            case "5" -> // <
                    originalValueDouble < compareValue;
            case "6" -> // <=
                    originalValueDouble <= compareValue;
            default -> false;
        };
    }

    public List<String> getCompareValueList(String compareValueStr) {
        if (CharSequenceUtil.isBlank(compareValueStr)) {
            return Collections.emptyList();
        }
        String[] compareValueArray = compareValueStr.split(",", 2);
        return Arrays.asList(compareValueArray);
    }
}
