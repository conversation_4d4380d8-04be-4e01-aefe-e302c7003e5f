package com.siteweb.monitoring.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.monitoring.enumeration.CapacityTypeEnum;
import com.siteweb.monitoring.enumeration.EquipmentExtValueEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 扩展字段工具类
 */
@Component
public class ExtendedFieldUtil {
    @Autowired
    ObjectMapper objectMapper;

    /**
     * 确保扩展字段存在必要的属性
     */
    public JsonNode ensureRequiredFields(JsonNode extendedField) {
        if (Objects.isNull(extendedField) || !extendedField.isArray()) {
            // 直接数据进行覆盖
            ArrayNode arrayNode = objectMapper.createArrayNode();
            for (CapacityTypeEnum type : CapacityTypeEnum.values()) {
                ObjectNode newNode = objectMapper.createObjectNode();
                newNode.put("id", type.getValue());
                newNode.put("tip", type.getName());
                newNode.put("name", type.getName());
                newNode.put("type", 2);
                newNode.put("isRequired", false);
                arrayNode.add(newNode);
            }
            return arrayNode;
        } else {
            ArrayNode arrayNode = (ArrayNode) extendedField;
            Set<String> existingIds = new HashSet<>();
            // 记录已有的 ID
            for (JsonNode node : arrayNode) {
                if (node.has("id")) {
                    existingIds.add(node.get("id").asText());
                }
            }
            // 遍历 CapacityTypeEnum，检查并补充缺失的字段
            for (CapacityTypeEnum type : CapacityTypeEnum.values()) {
                if (!existingIds.contains(type.getValue())) {
                    ObjectNode newNode = objectMapper.createObjectNode();
                    newNode.put("id", type.getValue());
                    newNode.put("tip", type.getName());
                    newNode.put("name", type.getName());
                    newNode.put("type", 2);
                    newNode.put("isRequired", false);
                    arrayNode.add(newNode);
                }
            }
            return arrayNode;
        }
    }

    /**
     * 确保扩展字段存在必要的属性
     */
    public JsonNode ensureEquipmentRequiredFields(JsonNode extendedField) {
        if (Objects.isNull(extendedField) || !extendedField.isArray()) {
            // 直接数据进行覆盖
            ArrayNode arrayNode = objectMapper.createArrayNode();
            for (EquipmentExtValueEnum type : EquipmentExtValueEnum.values()) {
                ObjectNode newNode = objectMapper.createObjectNode();
                newNode.put("id", type.getValue());
                newNode.put("tip", type.getName());
                newNode.put("name", type.getName());
                newNode.put("type", type.getType());
                newNode.put("isRequired", false);
                arrayNode.add(newNode);
            }
            return arrayNode;
        } else {
            ArrayNode arrayNode = (ArrayNode) extendedField;
            Set<String> existingIds = new HashSet<>();
            // 记录已有的 ID
            for (JsonNode node : arrayNode) {
                if (node.has("id")) {
                    existingIds.add(node.get("id").asText());
                }
            }
            // 遍历 EquipmentExtValueEnum，检查并补充缺失的字段
            for (EquipmentExtValueEnum type : EquipmentExtValueEnum.values()) {
                if (!existingIds.contains(type.getValue())) {
                    ObjectNode newNode = objectMapper.createObjectNode();
                    newNode.put("id", type.getValue());
                    newNode.put("tip", type.getName());
                    newNode.put("name", type.getName());
                    newNode.put("type", type.getType());
                    newNode.put("isRequired", false);
                    arrayNode.add(newNode);
                }
            }
            return arrayNode;
        }
    }
}
