package com.siteweb.monitoring.util;

import cn.hutool.core.date.DateUtil;
import com.siteweb.common.util.HexUtil;
import com.siteweb.monitoring.dto.TimeGroupSpanDTO;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import lombok.experimental.UtilityClass;

import java.util.Date;
import java.util.List;

@UtilityClass
public class MaskUtil {
    /**
     * 屏蔽是否在生效范围内
     *
     * @param timeSpanChar 分时段的字符串
     * @return boolean
     */
    public boolean isIntervalMask(String timeSpanChar){
        //获取当天的分钟数
        int minute = (DateUtil.hour(new Date(), true)) * 60 + DateUtil.thisMinute();
        int index = minute / 30;
        List<Boolean> booleans = HexUtil.hexStringToBooleanList(timeSpanChar);
        return Boolean.TRUE.equals(booleans.get(index));
    }

    public TimeGroupSpanDTO parserTimeGroupSpan(TimeGroupSpan timeGroupSpan){
        TimeGroupSpanDTO timeGroupSpanDTO = new TimeGroupSpanDTO();
        timeGroupSpanDTO.setTimeGroupId(timeGroupSpan.getTimeGroupId());
        timeGroupSpanDTO.setWeek(timeGroupSpan.getWeek());
        timeGroupSpanDTO.setTimeSpanBool(HexUtil.hexStringToBooleanList(timeGroupSpan.getTimeSpanChar()));
        return timeGroupSpanDTO;
    }
}
