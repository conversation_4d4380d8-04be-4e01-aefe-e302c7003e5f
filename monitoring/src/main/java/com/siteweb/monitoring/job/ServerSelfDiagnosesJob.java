package com.siteweb.monitoring.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.monitoring.dto.ServiceHostStateDTO;
import com.siteweb.monitoring.enumeration.WorkStationStateEnum;
import com.siteweb.monitoring.service.WorkStationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> zhou
 * @description ServerSelfDiagnosisJob
 * @createTime 2024-05-30 11:20:37
 */
@Component
@Slf4j
public class ServerSelfDiagnosesJob {
    private static final String WORK_STATION_STATE_PREFIX = "WorkStationState:";
    @Autowired
    WorkStationService workStationService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    private static final int WORK_STATION_TYPE = 22;


    @PostConstruct
    public void init() {
        Integer workStationId = workStationService.findWorkStationIdByType(WORK_STATION_TYPE);
        if (Objects.isNull(workStationId)) {
            log.info("tbl_workstation表中SiteWeb后台服务器数据暂未初始化");
            return;
        }
        workStationService.updateWorkStationConnectStateById(WorkStationStateEnum.ONLINE.getState(), workStationId);
    }

    /**
     * 每30秒上送自己的心跳至redis
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    public void sendHeartBeat() {
        try {
            Integer workStationId = workStationService.findWorkStationIdByType(WORK_STATION_TYPE);
            if (Objects.isNull(workStationId)) {
                log.info("tbl_workstation表中SiteWeb后台服务器数据暂未初始化");
                return;
            }
            String key = WORK_STATION_STATE_PREFIX + workStationId;
            ServiceHostStateDTO workStationStateDTO = null;
            ObjectMapper objectMapper = new ObjectMapper();
            Object workStationStateObj = stringRedisTemplate.opsForValue().get(key);
            if (null == workStationStateObj) {
                workStationStateDTO = new ServiceHostStateDTO();
                workStationStateDTO.setHostId(workStationId);
                workStationStateDTO.setState(1);
                workStationStateDTO.setHostType(WORK_STATION_TYPE);
            } else {
                workStationStateDTO = objectMapper.readValue(workStationStateObj.toString(), ServiceHostStateDTO.class);
            }
            workStationStateDTO.setHeartBeatTime(new Date());
            workStationStateDTO.setCpuRate(0f);
            workStationStateDTO.setMemRate(0f);
            workStationStateDTO.setDiskRate(0f);
            workStationStateDTO.setThreadCount(0);
            stringRedisTemplate.opsForValue().set(key, objectMapper.writeValueAsString(workStationStateDTO));
        } catch (JsonProcessingException e) {
            log.error("JsonProcessingException", e);
        }
    }
}
