package com.siteweb.monitoring.job;

import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.AlarmStatus;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.listener.AliHistorySignalStoreListener;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.Point;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/7/5
 */
@Slf4j
public class AliHistoryDataJob implements BaseJob {

    @Autowired
    private AliHistorySignalStoreListener aliHistorySignalStoreListener;

    @Autowired
    private InfluxDBManager influxDBManager;

    @Autowired
    private ActiveSignalManager activeSignalManager;

    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    HAStatusService haStatusService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            return;
        }
        log.trace("AliHistoryDataJob execute");
        Date scheduledFireTime = context.getScheduledFireTime();
        Set<Integer> equipmentSaveTimeMapKeySet = aliHistorySignalStoreListener.getEquipmentSaveTimeMapKeySet();
        for (Integer equipmentId : equipmentSaveTimeMapKeySet) {
            AlarmStatus alarmStatus = aliHistorySignalStoreListener.getEquipmentSaveTimeMapValue(equipmentId);
            if (alarmStatus == null) {
                continue;
            }
            // 判断当前时间是否超过结束时间
            long currentTime = System.currentTimeMillis();
            if (currentTime > alarmStatus.getEndTime()) {
                // 超过结束时间，删除缓存
                aliHistorySignalStoreListener.removeEquipmentSaveTimeMap(equipmentId);
                log.info("AliHistoryDataJob remove equipmentId:{}, startTime:{} end", equipmentId, DateUtil.dateToString(scheduledFireTime));
                continue;
            }
            // 开始存储influxdb
            log.info("AliHistoryDataJob execute equipmentId:{}, scheduledFireTime:{}", equipmentId, DateUtil.dateToString(scheduledFireTime));
            saveDataToInfluxDB(equipmentId, scheduledFireTime);
        }
    }

    public void saveDataToInfluxDB(Integer equipmentId, Date date) {
        List<SimpleActiveSignal> simpleActiveSignals = activeSignalManager.getActiveSignalsByEquipmentId(equipmentId);
        if (simpleActiveSignals == null || simpleActiveSignals.isEmpty()) {
            return;
        }
        List<Point> points = new ArrayList<>();
        for (SimpleActiveSignal simpleActiveSignal : simpleActiveSignals) {
            // 存储influxdb
            Point point = Point.measurement("historydatas")
                    .time(DateUtil.localToUTC(date).getTime(), TimeUnit.MILLISECONDS)
                    .tag("DeviceId", String.valueOf(equipmentId != null ? equipmentId : "0"))
                    .tag("BaseTypeId", String.valueOf(simpleActiveSignal.getBaseTypeId() != null ? simpleActiveSignal.getBaseTypeId() : "0"))
                    .tag("SignalId", simpleActiveSignal.getSignalId() != null && equipmentId != null ? equipmentId + "." + simpleActiveSignal.getSignalId() : "0")
                    // 这个原先由DS定义，20信号类型为告警触发
                    .tag("SignalType", "20")
                    .tag("stationId", String.valueOf(equipmentManager.getEquipmentById(equipmentId).getStationId() != null ? equipmentManager.getEquipmentById(equipmentId).getStationId() : "0"))
                    .addField("PointValue", Float.valueOf(simpleActiveSignal.getOriginalValue()))
                    .build();
            points.add(point);
        }
        influxDBManager.batchInsertPointRetry(points, "siteweb_v2");

    }
}
