package com.siteweb.monitoring.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.dto.SelfDiagnoseEventConfig;
import com.siteweb.monitoring.dto.ServiceHostState;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.enumeration.WorkStationStateEnum;
import com.siteweb.monitoring.enumeration.WorkStationTypeEnum;
import com.siteweb.monitoring.mapper.ActiveEventMapper;
import com.siteweb.monitoring.mapper.AlarmChangeMapper;
import com.siteweb.monitoring.mapper.EventMapper;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.monitoring.service.AlarmChangeService;
import com.siteweb.monitoring.service.WorkStationService;
import com.siteweb.monitoring.vo.ConfigEventCondition;
import com.siteweb.utility.dualhost.aspect.MasterHostOnly;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XxlJobDiagnoseJob implements ApplicationListener<BaseSpringEvent<HAStatusChanged>> {
    /**
     * 心跳间隔
     */
    public static final int HEARTBEAT_INTERVAL = 180;
    /**
     * redis心跳前缀
     */
    public static final String WORK_STATION_STATE_PREFIX = "WorkStationState:";
    @Value("${ha.task-delay:60}")
    private int defaultTaskDelay;
    /**
     * 下一次允许执行的时间戳
     */
    private volatile long nextAllowedExecutionTime = getNextAllowedExecutionTime();

    @Autowired
    private EventMapper eventMapper;
    @Autowired
    ActiveEventService activeEventService;
    @Autowired
    ActiveEventMapper activeEventMapper;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    AlarmChangeService alarmChangeService;
    @Autowired
    AlarmChangeMapper alarmChangeMapper;
    @Autowired
    WorkStationService workStationService;

    @Override
    public void onApplicationEvent(BaseSpringEvent<HAStatusChanged> event) {
        // 切机任务，需要让定时任务延迟一分钟执行
        log.info("收到HA状态变更事件，将暂停自诊断任务一分钟");
        // 记录当前时间加上一分钟作为允许执行的时间点
        nextAllowedExecutionTime = getNextAllowedExecutionTime();
    }

    /**
     * 每30秒判断一次xxl-job的心跳
     * 如果之前是正常变成了异常则上送产生一条活动告警
     * 如果之前是从异常变成了正常则结束活动告警
     * 如果一直是正常则无需处理
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 secon
    @Transactional(rollbackFor = Exception.class)
    @MasterHostOnly(logMessage = "xxl-job自诊断")
    public void diagnose() {
        // 检查是否需要延迟执行
        if (nextAllowedExecutionTime > System.currentTimeMillis()) {
            log.info("自诊断任务延迟执行中，将在{}后恢复", formatTimeRemaining(nextAllowedExecutionTime - System.currentTimeMillis()));
            return;
        }
        //读取xxl-job的自诊断告警配置
        Map<Integer, SelfDiagnoseEventConfig> selfDiagnoseEventConfigMap = getSelfDiagnoseEventConfigMap();
        //通过workStationId获取心跳
        List<ServiceHostState> serviceHostStateList = getWorkStationHeartbeat(selfDiagnoseEventConfigMap);
        //计算工作站的状态信息，得出离线，在线变化信息
        Date now = new Date();
        for (ServiceHostState serviceHostState : serviceHostStateList) {
            // 处理工作站状态
            handleServiceHostState(serviceHostState, selfDiagnoseEventConfigMap, now);
        }
    }

    /**
     * 处理工作站状态
     * @param serviceHostState 状态
     * @param configMap 告警配置map
     * @param now 当前时间
     */
    private void handleServiceHostState(ServiceHostState serviceHostState, Map<Integer, SelfDiagnoseEventConfig> configMap, Date now) {
        if (isAlwaysOnline(serviceHostState, now)) {
            endSelfDiagnosisEvent(serviceHostState, configMap, now);
            log.info("workstation一直正常：{}", serviceHostState.getHostId());
            return;
        }

        if (isOnlineToInterrupted(serviceHostState, now)) {
            handleOnlineToInterrupted(serviceHostState, configMap, now);
            log.info("workstation在线转离线，产生自诊断告警：{}", serviceHostState.getHostId());
            return;
        }

        if (isInterruptedToOnline(serviceHostState, now)) {
            handleInterruptedToOnline(serviceHostState, configMap, now);
            log.info("workstation离线转在线，结束自诊断告警：{}", serviceHostState.getHostId());
        }
    }

    /**
     * 一直在线
     * @param serviceHostState 状态
     * @param now 当前时间
     * @return boolean 是否一直在线
     */
    private boolean isAlwaysOnline(ServiceHostState serviceHostState, Date now) {
        return Objects.equals(serviceHostState.getState(), WorkStationStateEnum.ONLINE.getState()) && isNormal(serviceHostState.getHeartBeatTime(), now);
    }

    /**
     * 是否在线状态变成了中断状态
     * @param serviceHostState 状态
     * @param now 当前时间
     * @return boolean 是否从在线状态变成了中断状态
     */
    private boolean isOnlineToInterrupted(ServiceHostState serviceHostState, Date now) {
        return Objects.equals(serviceHostState.getState(), WorkStationStateEnum.ONLINE.getState()) && !isNormal(serviceHostState.getHeartBeatTime(), now) && CharSequenceUtil.isBlank(serviceHostState.getSequenceId());
    }

    /**
     * 是否从中断状态转换成了在线状态
     * @param serviceHostState 状态
     * @param now 当前时间
     * @return boolean 是否从中断状态转换成了在线状态
     */
    private boolean isInterruptedToOnline(ServiceHostState serviceHostState, Date now) {
        return Objects.equals(serviceHostState.getState(), WorkStationStateEnum.INTERRUPT.getState()) && isNormal(serviceHostState.getHeartBeatTime(), now);
    }

    private void handleOnlineToInterrupted(ServiceHostState serviceHostState, Map<Integer, SelfDiagnoseEventConfig> configMap, Date now) {
        // 产生告警
        SelfDiagnoseEventConfig config = configMap.get(serviceHostState.getHostId());
        ConfigEventCondition configEventCondition = createConfigEventCondition(config);
        Long serialNo = activeEventService.simulateActiveEvent(configEventCondition);
        workStationService.updateWorkStationConnectStateById(WorkStationStateEnum.INTERRUPT.getState(), serviceHostState.getHostId());
        // 更新工作站状态到redis
        String sequenceId = updateServiceHostStateToInterrupted(serviceHostState, serialNo, now);
        log.info("自诊断告警:{}，产生", sequenceId);
    }

    private void handleInterruptedToOnline(ServiceHostState serviceHostState, Map<Integer, SelfDiagnoseEventConfig> configMap, Date now) {
        // 结束自诊断告警
        endSelfDiagnosisEvent(serviceHostState, configMap, now);
        workStationService.updateWorkStationConnectStateById(WorkStationStateEnum.ONLINE.getState(), serviceHostState.getHostId());
        // 更新工作站状态
        updateServiceHostStateToOnline(serviceHostState);
    }

    private void endSelfDiagnosisEvent(ServiceHostState serviceHostState, Map<Integer, SelfDiagnoseEventConfig> configMap, Date now) {
        SelfDiagnoseEventConfig selfDiagnoseEventConfig = configMap.get(serviceHostState.getHostId());
        List<ActiveEventDTO> activeEventList = activeEventService.findActiveEventsByEquipmentIdAndEventId(selfDiagnoseEventConfig.getEquipmentId(), selfDiagnoseEventConfig.getEventId());
        for (ActiveEventDTO activeEventDTO : activeEventList) {
            endActiveEvent(activeEventDTO.getSequenceId(), now);
        }
    }

    private ConfigEventCondition createConfigEventCondition(SelfDiagnoseEventConfig config) {
        return ConfigEventCondition.builder()
                                   .stationId(config.getStationId())
                                   .equipmentId(config.getEquipmentId())
                                   .eventId(config.getEventId())
                                   .eventConditionId(config.getEventConditionId())
                                   .baseTypeId(config.getBaseTypeId())
                                   .meanings(config.getMeanings())
                                   .eventValue(1.0)
                                   .build();
    }

    private String updateServiceHostStateToInterrupted(ServiceHostState serviceHostState, Long serialNo, Date now) {
        AlarmChange alarmChange = alarmChangeService.findById(serialNo);
        serviceHostState.setSequenceId(alarmChange.getSequenceId());
        serviceHostState.setState(WorkStationStateEnum.INTERRUPT.getState());
        serviceHostState.setInterrupTime(now);
        stringRedisTemplate.opsForValue().set(WORK_STATION_STATE_PREFIX + serviceHostState.getHostId(), getServiceHostStateJson(serviceHostState));
        return alarmChange.getSequenceId();
    }

    private void endActiveEvent(String sequenceId, Date now) {
        List<ActiveEvent> unconfirmedEvents = activeEventMapper.findUnConfirmBySequenceIdsForUpdate(List.of(sequenceId));
        if (CollUtil.isEmpty(unconfirmedEvents)) {
            return;
        }
        ActiveEvent activeEvent = unconfirmedEvents.get(0);
        if (Objects.nonNull(activeEvent.getEndTime())) {
            return;
        }
        activeEvent.setEndTime(now);
        activeEvent.setEndValue(0.0);
        activeEventMapper.updateById(activeEvent);
        AlarmChange alarmChange = BeanUtil.toBean(activeEvent, AlarmChange.class);
        alarmChange.setOperationType(AlarmOperationTypeEnum.END.getValue());
        alarmChangeMapper.insert(alarmChange);
        log.info("自诊断告警sequenceId:{},结束", sequenceId);
    }

    private void updateServiceHostStateToOnline(ServiceHostState serviceHostState) {
        serviceHostState.setState(WorkStationStateEnum.ONLINE.getState());
        serviceHostState.setSequenceId("");
        serviceHostState.setInterrupTime(null);
        stringRedisTemplate.opsForValue().set(WORK_STATION_STATE_PREFIX + serviceHostState.getHostId(), getServiceHostStateJson(serviceHostState));
    }

    /**
     * 获取xxl-job的心跳信息
     * @param selfDiagnoseEventConfigMap 告警配置
     * @return {@link List }<{@link ServiceHostState }>
     */
    private List<ServiceHostState> getWorkStationHeartbeat(Map<Integer, SelfDiagnoseEventConfig> selfDiagnoseEventConfigMap) {
        List<String> workStationStateKeys = new ArrayList<>(selfDiagnoseEventConfigMap.size());
        for (SelfDiagnoseEventConfig selfDiagnoseEventConfig : selfDiagnoseEventConfigMap.values()) {
            workStationStateKeys.add(WORK_STATION_STATE_PREFIX + selfDiagnoseEventConfig.getWorkStationId());
        }
        List<String> serviceHostStateStrings = stringRedisTemplate.opsForValue().multiGet(workStationStateKeys);
        if (CollUtil.isEmpty(serviceHostStateStrings)) {
            return Collections.emptyList();
        }
        return ServiceHostState.cast(serviceHostStateStrings);
    }

    /**
     * 获取自诊断告警的配置【因为只守护xxl-job所以只获取xxl-job的告警配置即可】
     * @return {@link Map }<{@link Integer }, {@link SelfDiagnoseEventConfig }>
     */
    private Map<Integer, SelfDiagnoseEventConfig> getSelfDiagnoseEventConfigMap() {
        List<SelfDiagnoseEventConfig> selfDiagnoseEventConfigList = eventMapper.findSelfDiagnoseEventConfigByWorkStationType(WorkStationTypeEnum.BUSINESS_SERVER.getValue());
        // 检查是否为空，并记录日志
        if (CollUtil.isEmpty(selfDiagnoseEventConfigList)) {
            log.warn("未找到任何 SelfDiagnoseEventConfig 配置");
            return Collections.emptyMap();
        }
        return selfDiagnoseEventConfigList.stream().collect(Collectors.toMap(SelfDiagnoseEventConfig::getWorkStationId, Function.identity()));
    }

    private String getServiceHostStateJson(ServiceHostState serviceHostState) {
        return JSONUtil.toJsonStr(serviceHostState, new JSONConfig().setDateFormat(DatePattern.NORM_DATETIME_PATTERN));
    }

    /**
     * 是否正常
     *
     * @param heartBeatTime 心跳上送时间
     * @param now 当前时间
     * @return boolean true正常 false不正常
     */
    private boolean isNormal(Date heartBeatTime, Date now) {
        return DateUtil.offsetSecond(heartBeatTime, HEARTBEAT_INTERVAL).isAfter(now);
    }

    /**
     * 获取下一个允许执行时间
     *
     * @return long 下次允许执行时间的时间戳
     */
    private long getNextAllowedExecutionTime() {
        return System.currentTimeMillis() + defaultTaskDelay * 1000L;
    }

    /**
     * 格式化剩余时间的辅助方法
     *
     * @param milliseconds 毫秒
     * @return {@link String }
     */
    private String formatTimeRemaining(long milliseconds) {
        long seconds = milliseconds / 1000;
        if (seconds < 60) {
            return seconds + "秒";
        } else {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        }
    }
}
