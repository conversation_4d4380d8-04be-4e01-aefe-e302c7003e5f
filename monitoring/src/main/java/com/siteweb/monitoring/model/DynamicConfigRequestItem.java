package com.siteweb.monitoring.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.monitoring.enumeration.DynamicConfigObjectType;
import com.siteweb.monitoring.enumeration.DynamicConfigOperation;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description DynamicConfigRequestItem
 * @createTime 2022-06-11 16:49:14
 */
@Data
@NoArgsConstructor
@JacksonXmlRootElement(localName = "item")
public class DynamicConfigRequestItem {

    @JacksonXmlElementWrapper(localName = "attrs")
    @JacksonXmlProperty(localName = "attr")
    private List<DynamicConfigAttribute> attrs;
    @JacksonXmlProperty(isAttribute = true)
    private DynamicConfigObjectType childType;
    @JacksonXmlProperty(isAttribute = true)
    private Integer childId;
    @JacksonXmlProperty(isAttribute = true)
    private DynamicConfigOperation op;
}
