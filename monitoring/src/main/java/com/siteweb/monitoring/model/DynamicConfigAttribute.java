package com.siteweb.monitoring.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description DynamicConfigAttribute
 * @createTime 2022-06-11 16:50:18
 */
@Data
@NoArgsConstructor
@JacksonXmlRootElement(localName = "attr")
public class DynamicConfigAttribute {

    @JacksonXmlProperty(isAttribute = true)
    private String name;

    private DynamicValue value;
}
