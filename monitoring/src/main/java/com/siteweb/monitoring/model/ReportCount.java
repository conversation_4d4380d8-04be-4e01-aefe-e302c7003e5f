package com.siteweb.monitoring.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;

/**
 * @Author: lzy
 * @Date: 2022/5/21 14:32
 */
@Data
@NoArgsConstructor
public class ReportCount {
    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "count", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT)
    public Integer count;
}
