package com.siteweb.monitoring.model;

import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.Data;

@Data
public class EquipmentState {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 设备Id
     */
    private  Integer equipmentId;
    /**
     * 设备类型
     */
    private Integer equipmentCategory;
    /**
     * 设备在线状态（0：离线 1：在线  2:未注册
     */
    private OnlineState onlineState;

    /**
     * 是否工程态
     */
    private  Boolean onProject;

    /**
     *是否被屏蔽
     */
    private Boolean masked;

}
