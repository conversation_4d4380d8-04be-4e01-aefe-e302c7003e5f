package com.siteweb.monitoring.model;

import com.siteweb.monitoring.dto.ConfigSignalItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * 设备-信号 映射 用于去多个设备数据的场合
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentSignalMap {
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 设备名
     */
    private String  equipmentName;
    /**
     * 信号配置
     */
    private List<ConfigSignalItem>  configSignalItems;

    /**
     *  构建获取实时数据的键值
     * @return
     */
    public List<String> getRedisKey(){
        List<String> tmps = new ArrayList<String>();

        for (ConfigSignalItem configSignalItem:configSignalItems){
            tmps.add("RealTimeSignal:" + equipmentId + "." + configSignalItem.getSignalId());
        }
        return  tmps;
    }

}
