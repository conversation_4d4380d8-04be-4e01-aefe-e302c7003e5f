package com.siteweb.monitoring.model;

import lombok.Data;

import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2023/3/20 16:13
 */
@Data
public class ActiveControlRedisModel {
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 控制命令ID
     */
    private Integer controlId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 控制结果
     */
    private Integer controlResult;
    /**
     * 设置值
     */
    private String setValue;
    /**
     * 描述信息
     */
    private String description;
}
