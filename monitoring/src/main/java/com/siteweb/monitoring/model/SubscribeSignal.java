package com.siteweb.monitoring.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SubscribeSignal {
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 监控单元id
     */
    private Integer hostId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 订阅类型  默认0
     */
    private Integer subscribeType = 0;
    private Date lastSampleDateTime;
    private Date lastUpdateDateTime;
    private Date subscribeDateTime;
}
