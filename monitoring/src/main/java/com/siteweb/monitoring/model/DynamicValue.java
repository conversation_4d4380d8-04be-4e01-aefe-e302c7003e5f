package com.siteweb.monitoring.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.enumeration.MonitorValueType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DynamicValue {

    /**
     * 值类型
     */
    private MonitorValueType valueType;

    /**
     * 浮点值
     */
    private float floatValue;

    /**
     * 字符串值
     */
    private String stringValue;

    /**
     * 时间值
     */
    @JsonIgnore
    private Date dateTimeValue;

    /**
     * 字节型数值
     */
    @JsonIgnore
    private byte[] bytesValue;

    public DynamicValue(MonitorValueType valueType, float floatValue) {
        this.floatValue = floatValue;
        this.valueType = valueType;
    }

    public DynamicValue(MonitorValueType valueType, String stringValue) {
        this.stringValue = stringValue;
        this.valueType = valueType;
    }

    public DynamicValue(MonitorValueType valueType, Date dateTimeValue) {
        this.dateTimeValue = dateTimeValue;
        this.valueType = valueType;
    }

    @Override
    public String toString() {
        StringBuilder rtn = new StringBuilder();
        switch (valueType) {
            case Float:
                rtn.append(floatValue);
                break;
            case String:
                rtn.append(stringValue);
                break;
            case Integer:
                rtn.append(floatValue);
                break;
            case Byte:
                rtn.append(floatValue);
                break;
            case Char:
                rtn.append(stringValue);
                break;
            case NTPTime:
                rtn.append(dateTimeValue);
                break;
            case Binary:
                rtn.append(stringValue);
                break;
            case Object:
                rtn.append(valueType);
                break;
            case JPEG:
                rtn.append(bytesValue);
                break;
            default:
                break;
        }

        return rtn.toString();
    }
}
