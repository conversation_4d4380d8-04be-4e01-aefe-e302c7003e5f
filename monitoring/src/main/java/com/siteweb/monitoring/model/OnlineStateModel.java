package com.siteweb.monitoring.model;

import cn.hutool.core.util.NumberUtil;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class OnlineStateModel {
    /**
     * Id 可以是设备、局站、监控单元的id
     */
    private Integer id;

    /**
     * 设备在线状态（0：离线 1：在线  2:未注册
     */
    private OnlineState onlineState;

    public static OnlineStateModel parseFromStr(Object str) {
        if (Objects.isNull(str)) {
            return null;
        }
        OnlineStateModel onlineStateModel = new OnlineStateModel();
        //value = id~ConnectState， 例 755000101~1
        String[] res = str.toString().split("~");
        //必须都是数字
        if (NumberUtil.isInteger(res[0]) && NumberUtil.isInteger(res[1])) {
            onlineStateModel.setId(Integer.valueOf(res[0]));
            onlineStateModel.setOnlineState(OnlineState.valueOf(Integer.parseInt(res[1])));
            return onlineStateModel;
        }
        log.error("redis中解析在线状态异常,{}", str);
        return null;
    }
}
