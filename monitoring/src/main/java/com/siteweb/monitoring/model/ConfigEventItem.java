package com.siteweb.monitoring.model;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.EventMaskHistory;
import com.siteweb.monitoring.vo.ConfigEventCondition;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/**
 * 告警配置VO， 用于告警测试
 */
@Data
@NoArgsConstructor
public class ConfigEventItem {
    public ConfigEventItem(ConfigEventCondition configEventCondition) {
        Date eventStartTime = new Date();
        this.eventId = configEventCondition.getEventId();
        this.eventConditionId = configEventCondition.getEventConditionId();
        this.equipmentId = configEventCondition.getEquipmentId();
        this.stationId = configEventCondition.getStationId();
        this.baseTypeId = configEventCondition.getBaseTypeId();
        this.meanings = configEventCondition.getMeanings();
        this.startTime = eventStartTime;
        this.overturn = 0;
        this.eventValue = Objects.isNull(configEventCondition.getEventValue()) ? 8888.0 : configEventCondition.getEventValue();
        this.ret = -2;
        this.sequenceId = configEventCondition.getStationId().toString() + configEventCondition.getEquipmentId() + configEventCondition.getEventId() + configEventCondition.getEventConditionId() + DateUtil.dateToLong(eventStartTime);
    }


    public ConfigEventItem(ActiveEvent event,Date endTime) {
        this.eventId = event.getEventId();
        this.eventConditionId = event.getEventConditionId();
        this.equipmentId = event.getEquipmentId();
        this.stationId = event.getStationId();
        this.baseTypeId = event.getBaseTypeId();
        this.meanings = event.getMeanings();
        this.startTime = event.getStartTime();
        this.overturn = event.getReversalNum();
        this.eventValue = event.getEventValue();
        this.sequenceId = event.getSequenceId();
        this.endTime = endTime;
    }

    /**
     * 局站ID
     */
    private Integer stationId;

    /**
     * 设备ID
     */
    private Integer equipmentId;


    /**
     * 事件ID
     */
    private Integer eventId;


    /**
     * 条件ID
     */
    private Integer eventConditionId;

    /**
     * 告警流水号
     */
    private String sequenceId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 翻转次数
     */
    private Integer overturn;

    /**
     * 告警涵义
     */
    private String meanings;
    /**
     * 触发值
     */
    private Double eventValue;
    /**
     * 基类ID
     */
    private Long baseTypeId;
    /**
     * 执行返回值
     */
    private int ret;

    public EventMaskHistory buildEventMaskHistory(){
        EventMaskHistory eventMaskHistory = BeanUtil.copyProperties(this, EventMaskHistory.class);
        eventMaskHistory.setSequenceId(this.getSequenceId());
        return eventMaskHistory;
    }
}
