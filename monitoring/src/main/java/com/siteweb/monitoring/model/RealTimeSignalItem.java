package com.siteweb.monitoring.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实时信号部分（从redis获取）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealTimeSignalItem {
    /**
     * 设备Id
     */
    private  Integer equipmentId;
    /**
     * 当前值
     */
    private String currentValue;
    /**
     *  信号Id
     */
    private Integer signalId;
    /**
     * 采集时间
     */
    private String sampleTime;
    /**
     * 告警等级
     */
    private Integer eventLevel;
    /**
     * 有效值
     */
    private Integer signalValid;

    /**
     * 获取RedisKey
     * @return 键值
     */
    public String getKey()
    {
        return equipmentId + "." + signalId;
    }

    public static RealTimeSignalItem parseFromStr(String covStr){
        RealTimeSignalItem dp = new RealTimeSignalItem();
        String[] res = covStr.split("~");
        dp.equipmentId = Integer.parseInt(res[0]);
        dp.signalId = Integer.parseInt(res[1]);
        dp.currentValue = res[2];
        dp.sampleTime = res[3];
        dp.eventLevel = Integer.parseInt(res[4]);
        if (res.length == 6) {
            dp.signalValid = Integer.parseInt(res[5]);
        }
        return dp;
    }
}
