package com.siteweb.monitoring.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;

/**
 * @author: Habits
 * @time: 2022/4/14 14:03
 * @description:
 **/
@Data
@NoArgsConstructor
@Measurement(name = "historydatas")
public class HistorySignalGroupByTime {
    public static final long serialVersionUID = 1L;

    @Column(name = "time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Instant time;

    @Column(name = "PointValue")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String pointValue;

    @Column(name = "max", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String max;

    @Column(name = "min", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String min;

    @Column(name = "first", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String first;

    @Column(name = "last", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String last;

    @Column(name = "spread", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String spread;

    @Column(name = "count", tag = true)
    public Integer count;

    private Integer sortIndex;

    private String sampleTime;
    private String displayValue;
    private Integer equipmentId;
    private Integer signalId;

    public String getAvg() {
        return scale(this.pointValue);
    }

    public String getMax() {
        return scale(this.max);
    }

    public String getMin() {
        return scale(this.min);
    }

    public String getFirst() {
        return scale(this.first);
    }

    public String getLast() {
        return scale(this.last);
    }

    public String getSpread() {
        return scale(this.spread);
    }

    /**
     * 四舍五入保留两位
     * @param value
     * @return
     */
    public String scale(String value) {
        if (CharSequenceUtil.isEmpty(value)) {
            return "-";
        }
        return new BigDecimal(value).setScale(2, RoundingMode.HALF_UP).toString();
    }

    public String getShortTime() {
        String tempTime = this.time.toString();
        if (tempTime != null && !tempTime.equals("")) {
            tempTime = tempTime.substring(0, 10);
        }
        return tempTime;
    }

    public String getTime() {
        String tempTime = this.time.toString();
        if (tempTime != null && !tempTime.equals("")) {
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }

    public String getSampleTime() {
        return getTime();
    }

    public String getWeekDay() {
        String weekDay = this.time.toString();
        if (weekDay != null && !weekDay.equals("")) {
            weekDay = weekDay.substring(0, 10);
        }
        return weekDay;
    }

    public String getMonthDay() {
        String monthDay = this.time.toString();
        if (monthDay != null && !monthDay.equals("")) {
            monthDay = monthDay.substring(0, 7);
        }
        return monthDay;
    }
    public String getYearDay() {
        String monthDay = this.time.toString();
        if (monthDay != null && !monthDay.equals("")) {
            monthDay = monthDay.substring(0, 4);
        }
        return monthDay;
    }
}
