package com.siteweb.monitoring.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Measurement(name = "historydatas")
public class HistorySignalPointValue {
    @Column(name = "SignalId", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String SignalId;

    @Column(name = "PointValue")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String pointValue;
}
