package com.siteweb.monitoring.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.monitoring.enumeration.DynamicConfigObjectType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description DynamicConfigRequest
 * @createTime 2022-06-11 16:48:32
 */
@Data
@NoArgsConstructor
@JacksonXmlRootElement(localName = "dynamicConfig")
public class DynamicConfigRequest {

    @JsonIgnore
    private Date configTime;
    private String majorVersion;
    private DynamicConfigObjectType objectType;
    private Integer stationId;
    private Integer objectId;
    private String rdn;
    private Integer sequencecId;
    @JacksonXmlElementWrapper(localName = "items")
    @JacksonXmlProperty(localName = "item")
    private List<DynamicConfigRequestItem> items;
}
