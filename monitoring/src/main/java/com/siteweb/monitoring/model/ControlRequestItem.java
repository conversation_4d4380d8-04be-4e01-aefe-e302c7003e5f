package com.siteweb.monitoring.model;

import com.siteweb.monitoring.enumeration.ControlType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 控制命令实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ControlRequestItem {
    /**
     * 源主机Id
     */
    private Integer sourceHostId;
    /**
     *局站Id
     */
    private Integer stationId;
    /**
     * 设备Id
     */
    private Integer equipmentId;
    /**
     * 流水号
     */
    private Integer sequenceId;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 控制Id
     */
    private Integer controlId;
    /**
     * 用户Id
     */
    private Integer userId;
    /**
     * 控制类型
     */
    private ControlType controlType;
    /**
     * 控制开始执行时间
     */
    private Date startTime;
    /**
     * 控制优先级
     */
    private int priority;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 执行返回值
     */
    private  int ret;
    /**
     * 控制参数列表
     */
    private List<ParameterItem> parameterItems;

    public List<ParameterItem> getParameterItems() {
        if (this.parameterItems == null) {
            this.parameterItems = new ArrayList<>();
        }
        return parameterItems;
    }

    public String getParameterValues(){
        if(parameterItems.isEmpty()){
            return  "";
        }
        return String.format("%d,%s", parameterItems.get(0).getValue().getValueType().value(), parameterItems.get(0).getValue().toString());
    }

}
