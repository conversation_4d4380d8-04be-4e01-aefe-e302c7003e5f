package com.siteweb.monitoring.enumeration;


public enum SourceType {

    /**
     * ECC
     */
    CENTER(1),

    /**
     * 园区
     */
    PARK(2),

    /**
     * 大楼
     */
    BUILDING(3),

    /**
     * 楼层
     */
    FLOOR(4),

    /**
     * 房间
     */
    ROOM(5),

    /**
     * MDC
     */
    MDC(6),

    /**
     * 设备
     */
    EQUIPMENT(7),

    /**
     * 通用设备
     */
    COMMONOBJECT(8),

    /**
     * 机架
     */
    COMPUTERRACK(9),

    /**
     * IT设备
     */
    ITDEVICE(10),
    /**
     * 省监控中心
     */
    SCCENTER(101),
    /**
     * 地市监控中心
     */
    SSCENTER(102),
    /**
     * 区县
     */
    DISTRICT(103),
    /**
     * 局站
     */
    STATION(104),
    /**
     * 局方
     */
    HOUSE(105);

    private int value = 0;

    SourceType(int value) {     //必须是private的，否则编译错误
        this.value = value;
    }

    public static SourceType valueOf(int value) {    //手写的从int到enum的转换函数
        return switch (value) {
            case 1 -> CENTER;
            case 2 -> PARK;
            case 3 -> BUILDING;
            case 4 -> FLOOR;
            case 5 -> ROOM;
            case 6 -> MDC;
            case 7 -> EQUIPMENT;
            case 8 -> COMMONOBJECT;
            case 9 -> COMPUTERRACK;
            case 10 -> ITDEVICE;
            case 101 -> SCCENTER;
            case 102 -> SSCENTER;
            case 103 -> DISTRICT;
            case 104 -> STATION;
            case 105 -> HOUSE;
            default -> null;
        };
    }


    public int value() {
        return this.value;
    }
}
