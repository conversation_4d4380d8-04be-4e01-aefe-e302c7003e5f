package com.siteweb.monitoring.enumeration;

/**
 * 控制命令返回结果
 */
public enum ControlResultType {
    /**
     * 未知错误:如数据库异常
     */
    UNKNOWN_ERROR(-1),
    /**
     * 执行成功
     */
    SUCCESS(0),
    /**
     * 参数错误
     */
    PARAM_ERROR(1),
    /**
     * 活动控制命令已存在
     */
    COMMAND_EXISTS(2),
    /**
     * 局站或设备处于工程状态
     */
    IN_PROJECT(3),
    /**
     * 没有执行控制命令的权限
     */
    NO_PERMISSION(4),
    /**
     * 设备不在线
     */
    EQUIPMENT_OFFLINE(5);

    private int value = 0;

    private ControlResultType(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }

    public static ControlResultType valueOf(int value) {
        return switch (value) {
            case -1 -> UNKNOWN_ERROR;
            case 0 -> SUCCESS;
            case 1 -> PARAM_ERROR;
            case 2 -> COMMAND_EXISTS;
            case 3 -> IN_PROJECT;
            case 4 -> NO_PERMISSION;
            default -> null;
        };
    }
}
