package com.siteweb.monitoring.enumeration;

public enum MonitorValueType {
    /**
     * 浮点数
     */
    Float(0),

    /**
     * 字符串
     */
    String(1),

    /**
     * 整型
     */
    Integer(2),

    /**
     * 字节
     */
    Byte(3),

    /**
     * 字符
     */
    Char(4),

    /**
     * 网络时间
     */
    NTPTime(5),

    /**
     * 二进制
     */
    Binary(6),

    /**
     * 对象
     */
    Object(7),

    /**
     * JPEG图片
     */
    JPEG(10);


    private int value = 0;

    private MonitorValueType(int value) {
        this.value = value;
    }

    public static MonitorValueType valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return Float;
            case 1:
                return String;
            case 2:
                return Integer;
            case 3:
                return Byte;
            case 4:
                return Char;
            case 5:
                return NTPTime;
            case 6:
                return Binary;
            case 7:
                return Object;
            case 10:
                return JPEG;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }
}
