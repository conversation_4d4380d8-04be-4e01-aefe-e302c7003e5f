package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 容量类型定义 在层级ExtendedField中
 */
@Getter
@AllArgsConstructor
public enum CapacityTypeEnum {
    /**
     * 总电力额定容量
     */
    TOTAL_POWER_RATED_CAPACITY("TotalPowerRatedCapacity","总电力额定容量"),
    /**
     * IT电力额定功率
     */
    IT_POWER_RATED_CAPACITY("ItPowerRatedCapacity","IT电力额定功率"),
    /**
     * 制冷负荷额定容量
     */
    REFRIGERATION_RATED_CAPACITY("RefrigerationRatedCapacity","制冷负荷额定容量"),
    /**
     * 字节现场使用
     */
    TYPE("Type","类型"),
    ALIAS("Alias","别名"),
    ;

    private final String value;
    private final String name;
}
