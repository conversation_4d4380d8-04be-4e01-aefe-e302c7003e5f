package com.siteweb.monitoring.enumeration;

public enum AlarmState {

    ALARM(0),

    NORMAL(1);

    private int value = 1;

    private AlarmState(int value) {
        this.value = value;
    }

    public static AlarmState valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return ALARM;
            case 1:
                return NORMAL;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }
}