package com.siteweb.monitoring.enumeration;

/**
 * 控制命令种类
 */
public enum CommandCategory {
    /**
     * 通知控制
     */
    NotifyCommand(1),
    /**
     *  控制门开关
     */
    ControlDoorCommand(2),
    /**
     * 图像上移
     */
    ImageUpShift(3),
    /**
     * 图像下移
     */
    ImageDownShift(4),
    /**
     * 图像左移
     */
    ImageLeftShift(5),
    /**
     * 图像右移
     */
    ImageRightShift(6),
    /**
     * 图像前移
     */
    ImageZoomIn(7),
    /**
     * 图像后移
     */
    ImageZoomOut(8),
    /**
     * 图像明亮度信号
     */
    ImageBrightness(9),
    /**
     * 图像对比度信号
     */
    ImageContract(10),
    /**
     * 图像帧率
     */
    ImageFps (11),
    /**
     * 设置时间
     */
    SetTime(22),
    /**
     * 放电测试
     */
    Discharge (24),
    /**
     * 停止放电测试
     */
    StopDischarge (25),
    /**
     * 放电终止电压设置
     */
    DischargeStopV(27),
    /**
     * 放电终止时长设置
     */
    DischargeStopInterval(28);

    private int value = 0;

    private CommandCategory(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }
}
