package com.siteweb.monitoring.enumeration;

public enum  SamplerObjectType {
    CENTER(1),
    PARK(2),
    MU(3),
    PORT(4),
    SU(5);


    private int value = 0;

    private SamplerObjectType(int value) {     //必须是private的，否则编译错误
        this.value = value;
    }

    public static SamplerObjectType valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 1:
                return CENTER;
            case 2:
                return PARK;
            case 3:
                return MU;
            case 4:
                return PORT;
            case 5:
                return SU;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }
}
