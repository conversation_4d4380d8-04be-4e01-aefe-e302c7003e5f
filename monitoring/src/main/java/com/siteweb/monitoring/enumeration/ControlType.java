package com.siteweb.monitoring.enumeration;

public enum ControlType {
    /**
     * 手动执行
     */
    Mannul(1),

    /**
     * 定时执行
     */
    Timed(2),

    /**
     * 告警联动
     */
    Alarm(3),

    /**
     * 群控
     */
    Grouped(4);

    private int value = 0;

    private ControlType(int value) {
        this.value = value;
    }

    public static ControlType valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 1:
                return Mannul;
            case 2:
                return Timed;
            case 3:
                return Alarm;
            case 4:
                return Grouped;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }
}
