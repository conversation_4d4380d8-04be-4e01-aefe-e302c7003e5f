package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SignalTypeEnum {
    STATISTICS_MAX("统计数据－最大值", "0"),
    STATISTICS_MIN("统计数据－最小值", "1"),
    STATISTICS_AVG("统计数据－平均值", "2"),
    EVENT("事件", "3"),
    CHANGE_AMPLITUDE("变化幅值", "4"),
    STORAGE_PERIOD("存储周期", "5"),
    METER_READING("抄表数据", "6"),
    TIMED_STORAGE("定时存储", "7");

    private final String name;
    private final String value;
}
