package com.siteweb.monitoring.enumeration;

public enum EquipmentCategory {

    /**
     * 电表
     */
    Ammeter(12),

    /**
     * 开关电源
     */
    Rectifier(22),

    /**
     * 蓄电池
     */
    BatteryEquipment(24),

    /**
     * 环境设备
     */
    StationEnvironmentEquipment(53),

    /**
     * 门禁
     */
    DoorAccessEquipment(82),

    /**
     * ATM设备
     */
    ATMEquipment(84),

    /**
     * 图像设备
     */
    ImageEquipment(90),

    /**
     * 工作站自诊断设备
     */
    WorkstationDiagnosisEquipment(201),

    /**
     * 设备有效状态自诊断设备
     */
    EquipmentDiagnosisEquipment(202),

    /**
     * 监控单元通讯状态自诊断设备
     */
    SamplerUnitDiagnosisEquipment(203),

    /**
     * IDU自诊断设备
     */
    IDUDiagnosisEquipment(208),
    /**
     * IPLU自诊断设备
     */
    IPLUDiagnosisEquipment(212),
    /**
     * 交换机设备
     */
    SwitchEquipment(215),
    /**
     * 交换机设备
     */
    PumpingSlotEquipment(216),
    /**
     * EStone自诊断设备
     */
    EStoneDiagnosisEquipment(219),
    /**
     * EStone自诊断设备
     */
    IMUDiagnosisEquipment(223),
    /**
     * IDUX自诊断设备
     */
    IDUXDiagnosisEquipment(224);

    private int value = 0;

    private EquipmentCategory(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }
}
