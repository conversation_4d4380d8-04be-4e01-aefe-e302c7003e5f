package com.siteweb.monitoring.enumeration;

public enum OnlineState {

    OFFLINE(0),

    ONLINE(1),

    UNREGISTER(2);

    private int value = 0;

    private OnlineState(int value) {
        this.value = value;
    }

    public static OnlineState valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return OFFLINE;
            case 1:
                return ONLINE;
            case 2:
                return UNREGISTER;
            default:
                return null;
        }
    }

    public int  value() {
        return this.value;
    }
}
