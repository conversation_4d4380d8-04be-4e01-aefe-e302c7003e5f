package com.siteweb.monitoring.dto;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 设备基础信息Dto
 * <AUTHOR>
 * @date 2022/05/24
 */
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EquipmentBasicDto {
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Integer equipmentId;
    /**
     * 名称
     */
    @ApiModelProperty("设备名称")
    private String equipmentName;
    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String photo;
    /**
     * 设备基础类型
     */
    @ApiModelProperty("设备基础类型")
    private Integer equipmentBaseType;
    /**
     * 设备自定义类型
     */
    @ApiModelProperty("设备自定义类型")
    private Integer equipmentCategory;

    /**
     * 厂家
     */
    @ApiModelProperty("设备厂家")
    private String vendor;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 型号
     */
    @ApiModelProperty("型号")
    private String equipmentStyle;

    /**
     * 模块
     */
    @ApiModelProperty("模块")
    private String equipmentModule;
    /**
     * 资产编号
     */
    @ApiModelProperty("资产编号")
    private String equipmentNo;
    /**
     * 资产状态
     */
    @ApiModelProperty("资产状态")
    private Integer assetState;
    /**
     * 购买日期
     */
    @ApiModelProperty("购买日期")
    private Date buyDate;
    /**
     * 使用日期
     */
    @ApiModelProperty("使用日期")
    private Date usedDate;
    /**
     * 使用年限
     */
    @ApiModelProperty("使用年限")
    private Double usedLimit;
    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private Double price;
    /**
     * 额定容量
     */
    @ApiModelProperty("额定容量")
    private String ratedCapacity;
    /**
     * 工程名
     */
    @ApiModelProperty("工程名")
    private String projectName;
    /**
     * 层级级联信息
     */
    @ApiModelProperty("层级级联信息")
    private String levelOfPath;
    /**
     * 层级id
     */
    @ApiModelProperty("层级id")
    private String resourceStructureId;
    /**
     * 描述信息
     */
    @ApiModelProperty("描述信息")
    private String description;

    /**
     * 设备拓展属性
     */
    @ApiModelProperty("设备拓展属性")
    private JsonNode extValue;

    public String getPhoto(){
        if (ObjectUtil.isNull(this.photo)) {
            return "";
        }
        return this.photo;
    }
}
