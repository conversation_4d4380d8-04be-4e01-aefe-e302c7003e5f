package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @author: Habits
 * @time: 2022/3/31 13:05
 * @description:
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("设备传输实体")
public class EquipmentDTO {

    /**
     * 局站Id
     */
    @ApiModelProperty("局站Id")
    private Integer sId;
    /**
     * 设备id
     */
    @ApiModelProperty("设备Id")
    private Integer eqId;

    /**
     * 设备名
     */
    @ApiModelProperty("设备名")
    private String eqName;

    /**
     * 层级ID
     */
    @ApiModelProperty("层级ID")
    @JsonIgnore
    private Integer rId;

    /**
     * 设备类别
     */
    @ApiModelProperty("设备基类")
    private Integer eqCategory;

    /**
     * 告警状态
     */
    @ApiModelProperty("告警状态")
    private AlarmState alarmState;

    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    private Integer displayIndex;
    /**
     * 最大告警等级
     */
    @ApiModelProperty("最大告警等级")
    private Integer maxEventSeverity;

    /**
     * 设备在线状态
     */
    private OnlineState onlineState;

    /**
     * 是否被屏蔽
     */
    private Boolean masked;

    /**
     * 完整的层级路径
     */
    private String fullPath;
    /**
     * 设备所在房间ResourceStructureId对应的levelOfPath
     */
    private String lPath;

    /**
     * 设备类型
     */
    private Integer equipmentCategory;


    public EquipmentDTO(Equipment equipment, OnlineState onlineState, String fullPath, String lPath) {
        this.sId = equipment.getStationId();
        this.eqId = equipment.getEquipmentId();
        this.eqName = equipment.getEquipmentName();
        this.rId = equipment.getResourceStructureId();
        this.eqCategory = equipment.getEquipmentBaseType();
        this.displayIndex = equipment.getDisplayIndex();
        this.onlineState = onlineState;
        this.fullPath = fullPath;
        this.lPath = lPath;
        this.equipmentCategory = equipment.getEquipmentCategory();
    }
}
