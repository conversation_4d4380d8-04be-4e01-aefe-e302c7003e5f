package com.siteweb.monitoring.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 屏蔽时间段类型
 * <AUTHOR>
 * @date 2023/05/26
 */
@Data
public class AlarmMaskMakTimeGroupCategoryDTO {
    /**
     * [{"maskIntervalType":"1","desc":"全时段屏蔽"},{"maskIntervalType":"2","desc":"分时段屏蔽"}]
     */
    @ApiModelProperty("屏蔽时间段类型")
    private Integer timeGroupCategory;
    @ApiModelProperty("操作类型描述")
    private String desc;
}
