package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.MonitorUnit;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorUnitDTO {
    private  Integer monitorUnitId;
    private  String monitorUnitName;
    private  String  ipAddress;
    private  OnlineState onlineState;
    private Integer stationId;

    private String monitorUnitCategory;
    private Integer muCategoryId;
    public Integer getCenterId(){
        return  (monitorUnitId/100000)*100000 + 1;
    }

    public MonitorUnitDTO(MonitorUnit monitorUnit){
        this.monitorUnitId = monitorUnit.getMonitorUnitId();
        this.monitorUnitName = monitorUnit.getMonitorUnitName();
        this.onlineState = OnlineState.valueOf(monitorUnit.getConnectState());
        this.ipAddress = monitorUnit.getIpAddress();
        this.stationId = monitorUnit.getStationId();
        this.monitorUnitCategory = monitorUnit.getMonitorUnitCategoryName();
        this.muCategoryId = monitorUnit.getMonitorUnitCategory();
    }
}
