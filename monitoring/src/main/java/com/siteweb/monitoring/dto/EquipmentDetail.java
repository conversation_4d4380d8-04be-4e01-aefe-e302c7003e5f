package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentDetail {
    /**
     * 局站Id
     */
    private Integer stationId;
    /**
     * 设备Id
     */
    private  Integer equipmentId;
    /**
     * 设备名
     */
    private  String equipmentName;
    /**
     * 设备类型
     */
    private  String equipmentCategory;

    /**
     * 设备基类
     */
    private  String baseEquipmentName;
    /**
     * 设备品牌
     */
    private  String brand;
    /**
     * 设备型号
     */
    private  String equipmentStyle;
    /**
     * 额定容量
     */
    private  String ratedCapacity;
    /**
     * 设备位置
     */
    private  String equipmentPosition;
    /**
     * 设备是否被屏蔽
     */
    private  Boolean masked;
    /**
     * 设备图片
     */
    private String photo;
    /**
     * 告警状态
     */
    private AlarmState alarmState;
    /**
     * 在线状态
     */
    private OnlineState onlineState;
    /**
     * 信号数量(测点数量)
     */
    private Integer signalCount;

    public EquipmentDetail(Equipment equipment) {
        this.stationId = equipment.getStationId();
        this.equipmentId = equipment.getEquipmentId();
        this.equipmentName = equipment.getEquipmentName();
        this.equipmentStyle = equipment.getEquipmentStyle();
        this.ratedCapacity = equipment.getRatedCapacity();
        this.brand = equipment.getVendor();
        this.photo = equipment.getPhoto();
    }
}
