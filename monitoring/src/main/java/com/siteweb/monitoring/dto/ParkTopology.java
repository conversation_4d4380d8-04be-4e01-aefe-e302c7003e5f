package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SamplerObjectType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParkTopology extends  TopologyBase {

    @JsonIgnore
    private SamplerObjectType nodeCategory = SamplerObjectType.PARK;
    private List<MonitorUnitTopology> children;
    @JsonIgnore
    private  Integer sortValue;

    @JsonIgnore
    public ParkTopology(ResourceStructure park){
        this.id = park.getResourceStructureId();
        this.name = park.getResourceStructureName();
        this.edge_name = park.getResourceStructureName();
        this.sortValue =  park.getSortValue();
        this.state = OnlineState.ONLINE;
    }
}
