package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description ActiveEventOperationLogDTO
 * @createTime 2022-05-11 15:52:11
 */
@Data
@NoArgsConstructor
public class ActiveEventOperationLogDTO {

    private Integer activeEventOperationLogId;

    private String sequenceId;

    private Integer stationId;

    private Integer equipmentId;

    private Integer eventId;

    private Integer eventConditionId;

    private Date startTime;

    private Integer operatorId;

    private String operator;

    private String operation;

    private Date operationTime;

    private String description;
}
