package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.ActiveControl;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class ActiveControlDTO {
    /**
     * 控制命令流水号
     */
    private Integer serialNo;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 设备位置
     */
    private String equipmentPosition;

    /**
     * 基类名
     */
    private String baseTypeName;

    /**
     * 控制Id
     */
    private Integer controlId;

    /**
     * 控制名
     */
    private String controlName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 控制参数
     */
    private String parameterValues;

    /**
     * 控制结果
     */
    private String controlResult;
    /**
     * 控制命令结果Id
     */
    private Integer controlResultType;
    /**
     * 执行人Id
     */
    private Integer controlExecuterId;
    /**
     * 执行人名
     */
    private String controlExecuterIdName;

    public ActiveControlDTO(ActiveControl activeControl){
        this.serialNo = activeControl.getSerialNo();
        this.controlExecuterId = activeControl.getControlExecuterId();
        this.controlExecuterIdName = activeControl.getControlExecuterIdName();
        this.controlId =activeControl.getControlId();
        this.controlName = activeControl.getControlName();
        this.baseTypeName = activeControl.getBaseTypeName();
        this.confirmTime = activeControl.getConfirmTime();
        this.controlResult = activeControl.getControlResult();
        this.controlResultType = activeControl.getControlResultType();
        this.endTime = activeControl.getEndTime();
        this.equipmentId = activeControl.getEquipmentId();
        this.equipmentName = activeControl.getEquipmentName();
        this.startTime = activeControl.getStartTime();
        this.stationId = activeControl.getStationId();
        this.parameterValues = activeControl.getParameterValues();
    }

}
