package com.siteweb.monitoring.dto;


import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentAlarmStateDTO {
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 告警等级
     */
    private Integer eventLevel;
    /**
     * 设备在线状态
     */
    private OnlineState onlineState;
    /**
     * 是否有权限
     */
    private boolean filter;
}
