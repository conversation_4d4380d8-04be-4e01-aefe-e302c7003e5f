package com.siteweb.monitoring.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ServiceHostState {
    private int hostId;

    private int hostType;

    private int state;

    private Date heartBeatTime;

    private float cpuRate;

    private float memRate;

    private float diskRate;

    private int threadCount;

    private Date interrupTime;

    private String sequenceId;

    @NonNull
    public static List<ServiceHostState> cast(List<String> serviceHostStateStrings) {
        List<ServiceHostState> serviceHostStateList = new ArrayList<>();
        for (String serviceHostStateString : serviceHostStateStrings) {
            if (CharSequenceUtil.isBlank(serviceHostStateString)) {
                continue;
            }
            ServiceHostState serviceHostState = JSONUtil.toBean(serviceHostStateString, ServiceHostState.class);
            serviceHostStateList.add(serviceHostState);
        }
        return serviceHostStateList;
    }
}
