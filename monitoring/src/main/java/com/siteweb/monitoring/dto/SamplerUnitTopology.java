package com.siteweb.monitoring.dto;


import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SamplerObjectType;
import com.siteweb.monitoring.model.EquipmentState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SamplerUnitTopology extends  TopologyBase {
    private SamplerObjectType nodeCategory = SamplerObjectType.SU;
    private List<TopologyBase> children;

    /**
     * 是否被屏蔽
     */
    private Boolean masked;

    public SamplerUnitTopology(SamplerUnit samplerUnit){
        this.id = samplerUnit.getId();
        this.edge_name = samplerUnit.getSamplerUnitName();
        this.name = samplerUnit.getSamplerUnitName();
        this.children = new ArrayList<>();
        state = OnlineState.UNREGISTER;
    }

    public SamplerUnitTopology(Equipment equipment, EquipmentState equipmentState){
        this.id = equipment.getEquipmentId();
        this.edge_name = equipment.getEquipmentName();
        this.name = equipment.getEquipmentName();
        this.state = equipmentState.getOnlineState();
        this.masked = equipmentState.getMasked();
        this.children = new ArrayList<>();
    }

}
