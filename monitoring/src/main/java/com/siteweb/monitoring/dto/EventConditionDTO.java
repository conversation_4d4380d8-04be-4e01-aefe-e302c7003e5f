package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.EventCondition;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description EventConditionDTO
 * @createTime 2022-05-20 09:10:55
 */
@Data
@NoArgsConstructor
public class EventConditionDTO {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 告警条件ID
     */
    private Integer eventConditionId;

    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;

    /**
     * 告警ID
     */
    private Integer eventId;

    /**
     * 开始运算符
     */
    private String startOperation;

    /**
     * 开始比较值
     */
    private Double startCompareValue;

    /**
     * 开始延时
     */
    private Integer startDelay;

    /**
     * 结束运算符
     */
    private String endOperation;

    /**
     * 结束比较值
     */
    private Double endCompareValue;

    /**
     * 结束延时
     */
    private Integer endDelay;

    /**
     * 告警涵义
     */
    private String meanings;

    /**
     * 告警基类ID
     */
    private Long baseTypeId;

    /**
     * 告警基类名称
     */
    private String baseTypeName;

    /**
     * 告警等级ID
     */
    private Integer eventSeverity;

    /**
     * 告警等级名称
     */
    private String eventSeverityName;

    /**
     * 局站ID
     */
    private Integer stationId;

    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 电池设备状态 对应entryId 12
     */
    private Integer equipmentState;

    public EventCondition build() {
        EventCondition eventCondition = new EventCondition();
        BeanUtils.copyProperties(this, eventCondition);
        return eventCondition;
    }

}
