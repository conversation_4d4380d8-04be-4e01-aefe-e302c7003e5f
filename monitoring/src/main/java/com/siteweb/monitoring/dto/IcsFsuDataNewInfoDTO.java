package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class IcsFsuDataNewInfoDTO {

	/**
	 * 刷新时间
	 */
	private Date CollectTime;
	/**
	 * 站点名称
	 */
	private String SiteName;
	/**
	 * 采集器型号
	 */
	private String FsuType;
	/**
	 * 硬件版本
	 */
	private String Hw;
	/**
	 * SN码
	 */
	private String SN;
	/**
	 * MAC地址
	 */
	private String Mac;
	/**
	 * IP地址
	 */
	private String Ip;
	/**
	 * 内存配置
	 */
	private String MemTotal;
	/**
	 * Flash配置
	 */
	private String FlashSize;
	/**
	 * 系统版本
	 */
	private String Linux;
	/**
	 * SiteUnit版本
	 */
	private String SiteVersion;
	/**
	 * CPU使用率（%）
	 */
	private String CpuUsage;
	/**
	 * 内存使用率（%）
	 */
	private String MemUsage;
	/**
	 * Flash使用率（%）
	 */
	private String FlashUsedRate;
	/**
	 * 是否版本管理
	 */
	private Boolean isVersion;
}
