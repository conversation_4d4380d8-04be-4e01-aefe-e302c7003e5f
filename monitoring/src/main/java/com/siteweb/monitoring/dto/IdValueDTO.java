package com.siteweb.monitoring.dto;

import com.siteweb.utility.entity.DataItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdValueDTO<K, V> {

    private K value;

    private V label;

    public static IdValueDTO<Integer, String> fromDataItem(DataItem dataItem) {
        return new IdValueDTO<>(dataItem.getItemId(), dataItem.getItemValue());
    }
}
