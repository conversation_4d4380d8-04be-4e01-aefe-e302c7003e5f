package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentLedgerExtendFieldDTO {
    /**
     * 厂家联系人
     */
    private String manufacturerContactPerson;

    /**
     * 厂家联系方式
     */
    private String manufacturerContactNumber;

    /**
     * 设备出厂时间
     */
    private Date equipmentManufactureDate;

    /**
     * 设备质保时间
     */
    private Date warrantyStartDate;

    /**
     * 质保到期时间
     */
    private Date warrantyEndDate;

    /**
     * 上次保养时间
     */
    private Date lastMaintenanceDate;

    /**
     * 下次保养时间
     */
    private Date nextMaintenanceDate;

    /**
     * 资产归属部门ID
     */
    private Integer assetDepartmentId;

    /**
     * 资产归属部门名称
     */
    private String assetDepartmentName;

    /**
     * 资产负责人ID
     */
    private Integer assetResponsiblePersonId;

    /**
     * 资产负责人名称
     */
    private String assetResponsiblePersonName;

    /**
     * 负责人联系方式
     */
    private String responsiblePersonContact;
}
