package com.siteweb.monitoring.dto;

import com.siteweb.common.util.NumberUtil;
import lombok.Data;

@Data
public class StationGroupStateDTO {
    /**
     * 告警总数
     */
    private Integer totalCount;
    /**
     * 在线总数
     */
    private Integer onlineCount;
    /**
     * 离线数量
     */
    private Integer offlineCount;
    /**
     * 告警总数
     */
    private Integer alarmCount;
    /**
     * 告警率
     */
    public String  getAlarmRate(){
        return String.valueOf(NumberUtil.formatDouble((alarmCount*100)/(totalCount+0.0)));
    }
}
