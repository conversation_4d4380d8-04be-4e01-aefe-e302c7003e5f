package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.EquipmentMaintain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentProjectDetail {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 局站名称
     */
    private  String stationName;
    /**
     * 中心名称
     */
    private  String centerName;
    /**
     * 分组名称
     */
    private  String groupName;
    /**
     * 机房名称
     */
    private  String houseName;
    /**
     * 设备Id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private  String equipmentName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date    endTime;
    /**
     * 工程原因
     */
    private String  reason;
    /**
     * 工程人
     */
    private Integer userId;


    public EquipmentProjectDetail(EquipmentMaintain equipmentMaintain){
        equipmentId = equipmentMaintain.getEquipmentId();
        stationId = equipmentMaintain.getStationId();
        reason = equipmentMaintain.getDescription();
        startTime = equipmentMaintain.getStartTime();
        endTime = equipmentMaintain.getEndTime();
    }
}
