package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ConditionEventMaskDTO extends SimpleEventMaskDTO{
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 层级名称
     */
    private String resourceStructureName;
    /**
     * 告警等级
     */
    private Integer eventLevel;
    /**
     * 告警等级
     */
    private Integer eventSeverity;
}
