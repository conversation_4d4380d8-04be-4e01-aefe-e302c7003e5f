package com.siteweb.monitoring.dto.tree;

import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.utility.entity.DataItem;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class EquipmentCategoryTree {
    public EquipmentCategoryTree(DataItem dataItem) {
        this.parentItemId = dataItem.getParentItemId();
        this.itemId = dataItem.getItemId();
        this.itemValue = dataItem.getItemValue();
        this.categoryChildren = new ArrayList<>();
        this.eqChildren = new ArrayList<>();
    }

    /**
     * 父类id
     */
    private Integer parentItemId;
    /**
     * id
     */
    private Integer itemId;
    /**
     * 名称
     */
    private String itemValue;
    /**
     * 子级节点
     */
    private List<EquipmentCategoryTree> categoryChildren;
    /**
     * 最大告警等级
     */
    private Integer maxEventSeverity;
    /**
     * 层级底下的设备
     */
    List<EquipmentDTO> eqChildren;
    /**
     * 是否存在离线设备
     */
    private Boolean existsOfflineEquipment;

    public Integer getMaxEventSeverity() {
        if(maxEventSeverity != null){
            return maxEventSeverity;
        }

        Integer maxFromChildren = this.categoryChildren == null ? null : this.categoryChildren.stream()
                                                                              .map(EquipmentCategoryTree::getMaxEventSeverity)
                                                                              .filter(Objects::nonNull)
                                                                              .min(Integer::compareTo)
                                                                              .orElse(null);

        Integer maxFromEqu = eqChildren == null ? null : eqChildren.stream()
                                                                   .map(EquipmentDTO::getMaxEventSeverity)
                                                                   .filter(Objects::nonNull)
                                                                   .min(Integer::compareTo)
                                                                   .orElse(null);

        if (maxFromEqu == null) {
            return maxFromChildren;
        }
        if (maxFromChildren == null) {
            return maxFromEqu;
        }

        return Math.min(maxFromEqu,maxFromChildren);
    }

    public Boolean getExistsOfflineEquipment() {
        if (existsOfflineEquipment != null) {
            return existsOfflineEquipment;
        }

        boolean equipmentOfflineState = eqChildren.stream().anyMatch(e -> e.getOnlineState() == OnlineState.OFFLINE || e.getOnlineState() == OnlineState.UNREGISTER);
        if (equipmentOfflineState) {
            return Boolean.TRUE;
        }

        return this.categoryChildren.stream().anyMatch(EquipmentCategoryTree::getExistsOfflineEquipment);
    }
}
