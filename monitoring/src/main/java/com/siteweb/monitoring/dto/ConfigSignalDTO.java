package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ConfigSignal
 * @createTime 2022-06-09 14:13:34
 */
@Data
@NoArgsConstructor
public class ConfigSignalDTO {

    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 信号ID
     */
    private Integer signalId;
    /**
     * 是否可用
     */
    private Boolean enable;
    /**
     * 是否可视
     */
    private Boolean visible;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 信号名
     */
    private String signalName;
    /**
     * 信号分类
     */
    private Integer signalCategory;
    /**
     * 信号类型
     */
    private Integer signalType;
    /**
     * 通道号
     */
    private Integer channelNo;
    /**
     * 通道类型
     */
    private Integer channelType;
    /**
     * 计算表达式
     */
    private String expression;
    /**
     * 信号类型
     */
    private Integer dataType;
    /**
     * 显示精度
     */
    private String showPrecision;
    /**
     * 信号单位
     */
    private String unit;
    /**
     * 存储周期
     */
    private Double storeInterval;
    /**
     * 绝对值阈值
     */
    private Double absValueThreshold;
    /**
     * 百分比阈值（注：2 即为2%）
     */
    private Double percentThreshold;
    /**
     * 统计周期
     */
    private Integer staticsPeriod;
    /**
     * 信号基类ID
     */
    private Long baseTypeId;
    /**
     * 放电存储周期
     */
    private Double chargeStoreInterval;
    /**
     * 放电存储阈值
     */
    private Double chargeAbsValue;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * MDB信号ID
     */
    private Integer mdbSignalId;
    /**
     * 模块ID
     */
    private Integer moduleNo;

    /**
     * MonitorUnitId
     */
    private Integer monitorUnitId;

    /**
     * 信号属性
     */
    private List<Integer> signalPropertyIds;

    /**
     * 信号含义
     */
    private List<SignalMeaningDTO> signalMeanings;
}
