package com.siteweb.monitoring.dto;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.monitoring.entity.SignalMeanings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigSignalItem {
    /**
     * 设备Id
     */
    private  Integer equipmentTemplateId;
    /**
     * 信号Id
     */
    private  Integer signalId;
    /**
     * 信号种类
     */
    private Integer signalCategory;
    /**
     * 信号名
     */
    private  String  signalName;
    /**
     * 信号单位
     */
    private  String  unit;
    /**
     *基类Id
     */
    private Long baseTypeId;
    /**
     * 排序索引
     */
    private Integer displayIndex;
    /**
     * 显示精度
     */
    private String showPrecision;
    /**
     * 是否可见
     */
    private Boolean visible;
    /**
     * 信号含义
     */
    private List<SignalMeanings> meaningsList;


    /**
     * 信号属性
     */
    private String properties;

    /**
     * 是否为属性
     * @param propertyId
     * @return
     */
    public Boolean isProperty(Integer propertyId){
        if(ObjectUtil.isNull(this.properties))
            return  false;
        return properties.contains(propertyId.toString());
    }

}
