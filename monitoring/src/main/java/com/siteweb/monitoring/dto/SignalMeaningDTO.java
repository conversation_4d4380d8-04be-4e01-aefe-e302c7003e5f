package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.SignalMeanings;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description ConfigSignalMeaning
 * @createTime 2022-06-09 15:11:54
 */
@Data
@NoArgsConstructor
public class SignalMeaningDTO {

    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    /**
     * 信号Id
     */
    private Integer signalId;
    /**
     * 信号值
     */
    private Integer stateValue;
    /**
     * 信号涵义
     */
    private String meanings;
    /**
     * 基类ID
     */
    private Long baseCondId;

    @Override
    public String toString() {
        return this.stateValue + ":" + this.meanings;
    }

    public SignalMeanings build() {
        SignalMeanings signalMeanings = new SignalMeanings();
        signalMeanings.setEquipmentTemplateId(this.getEquipmentTemplateId());
        signalMeanings.setSignalId(this.getSignalId());
        signalMeanings.setStateValue(this.getStateValue());
        signalMeanings.setMeanings(this.getMeanings());
        signalMeanings.setBaseCondId(this.getBaseCondId());
        return signalMeanings;
    }
}
