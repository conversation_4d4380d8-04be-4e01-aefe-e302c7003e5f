package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class StationMaskDTO {

    private Integer stationId;
    /**
     * 时间分组ID
     */
    private Integer timeGroupId;
    /**
     * 屏蔽原因
     */
    private String reason;
    /**
     * 屏蔽开始时间
     */
    private Date startTime;
    /**
     * 屏蔽结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人ID
     */
    private Integer userId;
    /**
     * 屏蔽类型
     */
    private Integer timeGroupCategory;
    /**
     * 局站名
     */
    private String stationName;
    /**
     * 用户名
     */
    private String userName;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanDTO> timeGroupSpans;
}
