package com.siteweb.monitoring.dto;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: Habits
 * @time: 2022/3/31 12:58
 * @description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceStructureEquipmentTreeDTO {

    /**
     * 主键
     */
    private Integer rId;
    /**
     * 资源组类型
     */
    private Integer typeId;

    /**
     * 分组名
     */
    private String rName;

    /**
     * 父分组Id
     */
    @JsonIgnore
    private Integer parentId;

    /**
     * 子层级
     */
    List<ResourceStructureEquipmentTreeDTO> children;

    /**
     * 层级底下的设备
     */
    List<EquipmentDTO> eqChildren;

    /**
     * 告警状态
     */
    private AlarmState alarmState;

    /**
     * 最大告警等级
     */
    private Integer maxEventSeverity;

    /**
     * 是否可点击
     */
    private Boolean accessFlag;

    /**
     * 排序顺序
     */
    @JsonIgnore
    private Integer sortValue;
    /**
     * 是否存在离线设备
     */
    private Boolean existsOfflineEquipment;

    public Boolean getAccessFlag(){
        if (ObjectUtil.isNull(accessFlag)) {
            return false;
        }
        return accessFlag;
    }

    public AlarmState getAlarmState(){
        for (ResourceStructureEquipmentTreeDTO child : children) {
            if (child.getAlarmState() == AlarmState.ALARM) {
                return AlarmState.ALARM;
            }
        }
        for (EquipmentDTO eqChild : eqChildren) {
            if (eqChild.getAlarmState() == AlarmState.ALARM) {
                return AlarmState.ALARM;
            }
        }
        return AlarmState.NORMAL;
    }

    public Integer getMaxEventSeverity(){
        Integer maxEventSeverity = null;
        for (ResourceStructureEquipmentTreeDTO child : children) {
            if (maxEventSeverity == null && child.getMaxEventSeverity() != null) {
                maxEventSeverity = child.getMaxEventSeverity();
            }
            if (child.getMaxEventSeverity() != null && child.getMaxEventSeverity() < maxEventSeverity) {
                maxEventSeverity = child.getMaxEventSeverity();
            }
        }
        if (maxEventSeverity != null) {
            return maxEventSeverity;
        }
        for (EquipmentDTO eqChild : eqChildren) {
            if (maxEventSeverity == null && eqChild.getMaxEventSeverity() != null) {
                maxEventSeverity = eqChild.getMaxEventSeverity();
            }
            if (eqChild.getMaxEventSeverity() != null && eqChild.getMaxEventSeverity() < maxEventSeverity) {
                maxEventSeverity = eqChild.getMaxEventSeverity();
            }
        }
        return maxEventSeverity;
    }

    public List<ResourceStructureEquipmentTreeDTO> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public List<EquipmentDTO> getEqChildren() {
        if (eqChildren == null) {
            eqChildren = new ArrayList<>();
        }
        return eqChildren;
    }

    public ResourceStructureEquipmentTreeDTO(ResourceStructure resourceStructure) {
        this.rId = resourceStructure.getResourceStructureId();
        this.rName = resourceStructure.getResourceStructureName();
        this.typeId = resourceStructure.getStructureTypeId();
        this.parentId = resourceStructure.getParentResourceStructureId();
        this.children = new ArrayList<>();
        this.eqChildren = new ArrayList<>();
        this.sortValue = resourceStructure.getSortValue();
    }

    public Boolean getExistsOfflineEquipment() {
        if (existsOfflineEquipment != null) {
            return existsOfflineEquipment;
        }

        boolean equipmentOfflineState = eqChildren.stream().anyMatch(e -> e.getOnlineState() == OnlineState.OFFLINE || e.getOnlineState() == OnlineState.UNREGISTER);
        if (equipmentOfflineState) {
            return Boolean.TRUE;
        }

        return this.children.stream().anyMatch(ResourceStructureEquipmentTreeDTO::getExistsOfflineEquipment);
    }
}
