package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentEventIdDTO {
    public EquipmentEventIdDTO(SimpleEventMaskDTO simpleEventMaskDTO) {
        this.equipmentId = simpleEventMaskDTO.getEquipmentId();
        this.eventId = simpleEventMaskDTO.getEventId();
    }

    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 事件id
     */
    private Integer eventId;
}
