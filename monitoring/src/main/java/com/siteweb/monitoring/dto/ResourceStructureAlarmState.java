package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.ResourceStructure;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResourceStructureAlarmState {
    /**
     * 层级Id
     */
    private Integer resourceStructureId;
    /**
     *　层级名
     */
    private String resourceStructureName;
    /**
     *层级类型，用于组态跳转
     */
    private  Integer structureTypeId;
    /**
     * 告警等级
     */
    private Integer eventLevel;
    /**
     * 是否有权限
     */
    private boolean filter;

    public ResourceStructureAlarmState(ResourceStructure resourceStructure){
        this.resourceStructureId = resourceStructure.getResourceStructureId();
        this.resourceStructureName = resourceStructure.getResourceStructureName();
        this.structureTypeId = resourceStructure.getStructureTypeId();
        this.eventLevel = 0;
        this.filter = false;
    }
}
