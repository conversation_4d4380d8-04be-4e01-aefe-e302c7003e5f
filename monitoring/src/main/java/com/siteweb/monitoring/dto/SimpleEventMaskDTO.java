package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description SimpleEventMaskDTO
 * @createTime 2022-05-30 15:54:59
 */
@Data
@NoArgsConstructor
public class SimpleEventMaskDTO {

    private Integer stationId;

    private Integer equipmentId;

    private Integer eventId;

    private String eventName;

    /**
     * 屏蔽设置状态
     */
    private Boolean mask;
    /**
     * 屏蔽生效状态
     */
    private Boolean effective;

    public String getUniqueId() {
        return equipmentId + "." + eventId;
    }
}
