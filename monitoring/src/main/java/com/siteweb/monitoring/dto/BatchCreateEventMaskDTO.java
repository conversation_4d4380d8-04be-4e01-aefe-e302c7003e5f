package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class BatchCreateEventMaskDTO {

    /**
     * 告警ids 局站id,设备id,告警id
     */
    private List<String> ids;
    /**
     * 屏蔽原因
     */
    @ApiModelProperty(value = "屏蔽原因", name = "reason")
    private String reason;

    /**
     * 屏蔽开始时间
     */
    @ApiModelProperty(value = "屏蔽开始时间", name = "startTime")
    private Date startTime;

    /**
     * 屏蔽结束时间
     */
    @ApiModelProperty(value = "屏蔽结束时间", name = "endTime")
    private Date endTime;

    /**
     * 1全时段屏蔽 2分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;
}
