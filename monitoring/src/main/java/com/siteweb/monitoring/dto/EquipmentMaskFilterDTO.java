package com.siteweb.monitoring.dto;

import com.siteweb.utility.dto.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.Set;

/**
 * 事件面具过滤器dto
 *
 * <AUTHOR>
 * @date 2023/07/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EquipmentMaskFilterDTO extends PageDTO {
    private String keywords;
    private Set<Integer> equipmentIdList;
    private String resourceStructureIds;
    private Collection<Integer> resourceStructureIdList;
    private String equipmentBaseTypeIds;
    private Collection<Integer> equipmentBaseTypeIdList;
    private String equipmentCategories;
    private Collection<Integer> equipmentCategoryIdList;
}
