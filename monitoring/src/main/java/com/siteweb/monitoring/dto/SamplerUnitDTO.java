package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.Data;

/**
 *  采集单元详细信息
 */
@Data
public class SamplerUnitDTO {
    /**
     * 唯一Id
     */
    private  Integer id;
    /**
     * 监控单元名
     */
    private  String monitorUnitName;
    /**
     * 端口名
     */
    private  String portName;
    /**
     * 端口设置
     */
    private  String setting;
    /**
     * 采集单元名
     */
    private  String samplerUnitName;
    /**
     * 链接合租昂太
     */
    @JsonIgnore
    private  Integer connectState;
    /**
     * 协议库
     */
    private  String  dllPath;

     public OnlineState getStatus(){
         return  OnlineState.valueOf(this.connectState);
     }
}
