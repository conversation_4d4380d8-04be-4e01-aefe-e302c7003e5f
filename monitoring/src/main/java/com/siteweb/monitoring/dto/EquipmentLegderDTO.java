package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentLegderDTO extends EquipmentLedgerExtendFieldDTO{

        /**
         * 设备Id
         */
        private Integer equipmentId;

        /**
         * 设备名
         */
        private String equipmentName;

        /**
         * 设备类型
         */
        private Integer equipmentCategory;

        /**
         * 设备类型名
         */
        private String equipmentCategoryName;

        /**
         * 设备品牌
         */
        private String brand;

        /**
         * 设备型号
         */
        private String equipmentStyle;

        /**
         * 层级ID
         */
        private Integer resourceStructureId;

        /**
         * 层级路径
         */
        private String levelOfPath;

        /**
         * 设备位置
         */
        private String equipmentPosition;
        /**
         * 其他字段
         */
        private String extValue;
        /**
         * 采购时间
         */
        private Date buyDate;

        /**
         * 设备条码
         */
        private String equipmentSn;
    }

