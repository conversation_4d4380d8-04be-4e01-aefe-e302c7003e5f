package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.ProjectStateHouse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@NoArgsConstructor
public class HouseProjectDetail {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 局站名称
     */
    private  String stationName;
    /**
     * 中心名称
     */
    private  String centerName;
    /**
     * 分组名称
     */
    private  String groupName;
    /**
     * 机房Id
     */
    private  Integer houseId;
    /**
     * 机房名称
     */
    private  String houseName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date    endTime;
    /**
     * 工程原因
     */
    private String  reason;

    public HouseProjectDetail(ProjectStateHouse projectStateHouse){
        stationId = projectStateHouse.getStationId();
        houseId = projectStateHouse.getHouseId();
        houseName = projectStateHouse.getHouseName();
        startTime = projectStateHouse.getStartTime();
        endTime = projectStateHouse.getEndTime();
        reason = projectStateHouse.getReason();
    }
}
