package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SamplerObjectType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CenterTopology extends  TopologyBase {
    @JsonIgnore
    private SamplerObjectType nodeCategory = SamplerObjectType.CENTER;

    private List<ParkTopology> children;

    public CenterTopology(ResourceStructure center){
        this.id =1;
        this.name = center.getResourceStructureName();
        this.edge_name = center.getResourceStructureName();
        this.state = OnlineState.ONLINE;
    }
}
