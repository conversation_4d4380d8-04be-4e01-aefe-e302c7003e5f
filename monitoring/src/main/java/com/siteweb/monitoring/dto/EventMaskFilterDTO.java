package com.siteweb.monitoring.dto;

import com.siteweb.utility.dto.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.Set;

/**
 * 告警查询
 *
 * <AUTHOR>
 * @date 2023/07/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EventMaskFilterDTO extends PageDTO {
    private String keywords;
    private Set<Integer> equipmentIdList;
    private String resourceStructureIds;
    private Collection<Integer> resourceStructureIdList;
    private String equipmentBaseTypeIds;
    private Collection<Integer> equipmentBaseTypeIdList;
    private String baseTypeIds;
    private Collection<Integer> baseTypeIdList;
    private String equipmentCategories;
    private Collection<Integer> equipmentCategoryIdList;
    private String eventName;
}
