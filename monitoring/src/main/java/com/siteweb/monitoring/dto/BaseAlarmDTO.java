package com.siteweb.monitoring.dto;

import lombok.Data;

/**
 * 数据传输对象，用于表示告警信息。
 */
@Data
public class BaseAlarmDTO {
    /**
     * 基础类型ID。
     */
    private Integer baseTypeId;

    /**
     * 基础类型名称。
     */
    private String baseTypeName;

    /**
     * 标准化设备类型id
     */
    private Integer equipmentLogicClassId;

    /**
     * 标准化设备类型
     */
    private String equipmentLogicClass;

    /**
     * 标准化告警名称
     */
    private String eventStandardName;

    /**
     * 标准化告警id
     */
    private String netManageId;

    /**
     * 站点类别
     */
    private Integer stationCategory;

    /**
     * 扩展字段
     */
    private String baseNameExt;
    /**
     * 设备基类id
     */
    private Integer baseEquipmentId;
    /**
     * 设备基类名
     */
    private String  baseEquipmentName;


}

