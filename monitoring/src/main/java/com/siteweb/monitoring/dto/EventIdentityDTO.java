package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventIdentityDTO {

    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 告警id
     */
    private Integer eventId;
    
    public static List<EventIdentityDTO> from(List<String> deleteEventMaskStrings){
        List<EventIdentityDTO> result = new ArrayList<>();
        for (String deleteEventMaskString : deleteEventMaskStrings) {
            String[] ids = deleteEventMaskString.split(",");
            EventIdentityDTO eventIdentityDTO = new EventIdentityDTO(Integer.valueOf(ids[0]),Integer.valueOf(ids[1]),Integer.valueOf(ids[2]));
            result.add(eventIdentityDTO);
        }
        return result;
    }
}
