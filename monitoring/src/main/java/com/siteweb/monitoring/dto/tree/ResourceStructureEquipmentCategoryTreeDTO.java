package com.siteweb.monitoring.dto.tree;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.enumeration.AlarmState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * @author: Habits
 * @time: 2022/3/31 12:58
 * @description:
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceStructureEquipmentCategoryTreeDTO {

    /**
     * 主键
     */
    private Integer rId;
    /**
     * 资源组类型
     */
    private Integer typeId;

    /**
     * 分组名
     */
    private String rName;

    /**
     * 父分组Id
     */
    @JsonIgnore
    private Integer parentId;

    /**
     * 子层级
     */
    List<ResourceStructureEquipmentCategoryTreeDTO> children;

    /**
     * 设备类型树
     */
    List<EquipmentCategoryTree> equipmentCategoryChildren;

    /**
     * 告警状态
     */
    private AlarmState alarmState;

    /**
     * 最大告警等级
     */
    private Integer maxEventSeverity;

    /**
     * 是否可点击
     */
    private Boolean accessFlag;

    /**
     * 排序顺序
     */
    @JsonIgnore
    private Integer sortValue;
    /**
     * 是否存在离线设备
     */
    private Boolean existsOfflineEquipment;

    // 自定义 getter
    public Integer getMaxEventSeverity() {
        if (maxEventSeverity != null) {
            return maxEventSeverity;
        }

        // 从 equipmentCategoryChildren 计算
        Integer maxFromEquipment = equipmentCategoryChildren == null ? null :
                equipmentCategoryChildren.stream()
                                         .map(EquipmentCategoryTree::getMaxEventSeverity)
                                         .filter(Objects::nonNull)
                                         .min(Integer::compareTo)
                                         .orElse(null);

        // 从 children 计算
        Integer maxFromChildren = children == null ? null :
                children.stream()
                        .map(ResourceStructureEquipmentCategoryTreeDTO::getMaxEventSeverity)
                        .filter(Objects::nonNull)
                        .min(Integer::compareTo)
                        .orElse(null);

        if (maxFromEquipment == null) {
            return maxFromChildren;
        }
        if (maxFromChildren == null) {
            return maxFromEquipment;
        }

        return Math.min(maxFromEquipment, maxFromChildren);
    }

    public Boolean getExistsOfflineEquipment() {
        if (existsOfflineEquipment != null) {
            return existsOfflineEquipment;
        }

        boolean equipmentCategory = equipmentCategoryChildren.stream().anyMatch(EquipmentCategoryTree::getExistsOfflineEquipment);
        if (equipmentCategory) {
            return Boolean.TRUE;
        }

        return this.children.stream().anyMatch(ResourceStructureEquipmentCategoryTreeDTO::getExistsOfflineEquipment);
    }
}
