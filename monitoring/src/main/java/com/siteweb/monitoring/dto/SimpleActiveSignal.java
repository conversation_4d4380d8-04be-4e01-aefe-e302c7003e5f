package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  简单信号信息（只包括信号配置，信号实时信息， 极简模式使用，减少数据传输）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleActiveSignal {
    /**
     * 信号Id
     */
    private Integer signalId;
    /**
     *  信号名
     */
    private String  signalName;
    /**
     * 当前值
     */
    private String  currentValue;
    /**
     * 原始值
     */
    private  String originalValue;
    /**
     * 当前状态
     */
    private Integer currentState;
    /**
     * 信号类型
     */
    private Integer signalCategory;
    /**
     * 采集时间
     */
    private String sampleTime;
    /**
     * 拍训索引
     */
    private  Integer displayIndex;

    /**
     * 基类Id
     */
    private  Long baseTypeId;
    /**
     * 单位
     */
    private String unit;

    private Integer signalValid;

    /**
     * 信号含义拼接
     */
    private String signalMeanings;
}
