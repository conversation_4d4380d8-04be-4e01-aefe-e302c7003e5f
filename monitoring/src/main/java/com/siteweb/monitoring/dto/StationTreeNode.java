package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.ResourceStructure;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 电子地图专用局站数， 带经纬度,直到局站级
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StationTreeNode {

    /**
     * 层级ID
     */
    private Integer id;
    /**
     * 层级类型
     */
    private Integer tId;

    /**
     * 层级名称
     */
    private String name;

    /**
     * 父层级Id
     */
    @JsonIgnore
    private Integer parentId;

    /**
     * 最大告警等级
     */
    private Integer maxEventSeverity;

    /**
     *  链接状态
     */
    private Integer connectState;

    /**
     * 工程状态
     */
    private Integer projectState;


    /**
     * 经度
     */
    private Double longitude;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 子层级
     */
    private List<StationTreeNode> children;

    private Integer stationId;

    /**
     * 构建局站节点对象
     * @param resourceStructure
     */
    public StationTreeNode(ResourceStructure resourceStructure) {
        this.id = resourceStructure.getResourceStructureId();
        this.name = resourceStructure.getResourceStructureName();
        this.tId = resourceStructure.getStructureTypeId();
        this.parentId = resourceStructure.getParentResourceStructureId();
        this.maxEventSeverity = 0;
        this.children = new ArrayList<>();
    }
}
