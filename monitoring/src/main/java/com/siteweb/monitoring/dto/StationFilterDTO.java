package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StationFilterDTO {
    /**
     * 分组方式
     */
    private Integer stationGroupType;
    /**
     * 分组id
     */
    @JsonAlias({"mapObjectId","stationStructureList"})
    private List<Integer> stationStructureList;
    /**
     * 局站类型
     */
    @JsonAlias({"stationCategory","stationCategoryList"})
    private List<Integer> stationCategoryList;
    /**
     * 局站状态
     */
    private List<Integer> stationStateList;
    /**
     * 局站id
     */
    @JsonAlias({"stationId","stationIdList"})
    private Collection<Integer> stationIdList;
    /**
     * 设备类型
     */
    private Set<Integer> equipmentCategoryList;
    /**
     * 设备名称
     */
    private List<Integer> equipmentIdList;
    /**
     * 局站id_机房id
     */
    private List<String> houseIdList;
    /**
     * 工程状态 true=工程状态，false=非工程状态
     */
    private Boolean maintainState;
    /**
     * 连接状态 0 离线 1 在线 2 未注册
     */
    private Integer onlineState;

}