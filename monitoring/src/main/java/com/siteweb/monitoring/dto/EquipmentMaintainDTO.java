package com.siteweb.monitoring.dto;


import com.siteweb.monitoring.entity.EquipmentMaintain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EquipmentMaintainDTO {
    /**
     * 局站Id
     */
    private Integer stationId;
    /**
     * 设备Id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 工程原因
     */
    private String reason;

    public EquipmentMaintainDTO(EquipmentMaintain equipmentMaintain) {
        this.stationId = equipmentMaintain.getStationId();
        this.equipmentId = equipmentMaintain.getEquipmentId();
        this.startTime = equipmentMaintain.getStartTime();
        this.endTime = equipmentMaintain.getEndTime();
        this.reason = equipmentMaintain.getDescription();
    }
}
