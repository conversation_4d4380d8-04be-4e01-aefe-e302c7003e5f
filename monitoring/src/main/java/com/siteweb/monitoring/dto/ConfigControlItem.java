package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.ControlMeanings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 控制命令配置项，包括实时信号
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigControlItem {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     *设备Id
     */
    private  Integer equipmentId;
    /**
     * 层级Id
     */
    private Integer resourceStructureId;
    /**
     * 设备位置
     */
    private String position;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备模板Id
     */
    private  Integer equipmentTemplateId;
    /**
     * 控制Id
     */
    private  Integer controlId;
    /**
     * 控制名
     */
    private  String  controlName;
    /**
     * 控制命令 种类
     */
    private Integer commandType;
    /**
     * 信号Id
     */
    private  Integer signalId;
    /**
     * 对应当前的信号值
     */
    private  String  currentValue;
    /**
     * 信号单位
     */
    private  String  unit;
    /**
     *基类Id
     */
    private Long baseTypeId;
    /**
     * 最大值
     */
    private Double maxValue;
    /**
     * 最小值
     */
    private Double minValue;
    /**
     * 基类名称
     */
    private String baseTypeName;
    /**
     * 排序索引
     */
    private Integer displayIndex;

    /**
     * 对应信号名
     */
    private String signalName;
    /**
     * 采集时间
     */
    private  String sampleTime;
    /**
     * 控制命令涵义
     */
    private List<ControlMeanings> controlMeaningsList;

}
