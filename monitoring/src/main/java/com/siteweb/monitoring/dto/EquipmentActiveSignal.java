package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 设备信号信息（层级下通用，设备信息， 信号配置信息， 实时信号信息，适合中批量数据获取）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentActiveSignal {
    /**
     * 设备Id
     */
    private Integer equipmentId;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 实时信号列表
     */
    List<SimpleActiveSignal> activeSignals;
}
