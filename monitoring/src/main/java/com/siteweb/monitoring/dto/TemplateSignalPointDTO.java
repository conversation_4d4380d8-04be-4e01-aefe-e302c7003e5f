package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 信号点位
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateSignalPointDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 模板名称（标记类型）
     */
    private String equipmentTemplateName;
    /**
     * 设备类型名称
     */
    private String equipmentCategoryName;
    /**
     * 序号
     */
    private Integer index;
    /**
     * 测点名称
     */
    private String signalName;
    /**
     * 参数号
     */
    private Integer channelNo;
    /**
     * 参数ID
     */
    private Integer signalId;
    /**
     * 回路号
     */
    private Integer backTraceNo;
}
