package com.siteweb.monitoring.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SamplerObjectType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorUnitTopology extends  TopologyBase {
    @JsonIgnore
    private SamplerObjectType nodeCategory = SamplerObjectType.MU;

    private List<PortTopology> children;

    public MonitorUnitTopology(MonitorUnitDTO mu){
        this.id = mu.getMonitorUnitId();
        this.name = mu.getMonitorUnitName();
        this.edge_name = mu.getMonitorUnitName() + "(" + mu.getIpAddress()+")";
        this.state = mu.getOnlineState();
    }

    public void setChildState(){
        if(this.state == OnlineState.ONLINE){
            return;
        }
        if(children != null){
            for(PortTopology portTopology:children)
            {
                portTopology.state = this.state;
                portTopology.setChildState();
            }
        }
    }
}
