package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自诊断告警配置
 * <AUTHOR>
 * @date 2024/09/23
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SelfDiagnoseEventConfig {
    /**
     * 工作站id
     */
    private Integer workStationId;
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 告警id
     */
    private Integer eventId;
    /**
     * 告警条件id
     */
    private Integer eventConditionId;
    /**
     * 告警基类id
     */
    private Long baseTypeId;
    /**
     * 告警含义
     */
    private String meanings;
    /**
     * 连接状态
     */
    private Integer connectState;
}
