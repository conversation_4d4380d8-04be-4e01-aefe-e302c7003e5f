package com.siteweb.monitoring.dto;

import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * @Author: lzy
 * @Date: 2022/5/25 18:59
 */
@Data
public class AlarmMaskLogReportParam {
    /**
     * 操作开始时间
     */
    private Date startDate;
    /**
     * 操作结束时间
     */
    private Date endDate;
    /**
     * 事件id
     */
    private Map<Integer, Set<Integer>> eventIds;
    /**
     * 操作类型（具体可查看 AlarmMaskOperationTypeDTO）
     */
    private Integer operationType;
    /**
     * 屏蔽开始时间
     */
    private Date maskStartDate;
    /**
     * 屏蔽结束时间
     */
    private Date maskEndDate;
    /**
     * 操作人
     */
    private Set<Integer> operatorIds;
    /**
     * 设备ids
     */
    private Set<Integer> equipmentIds;
}
