package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.Equipment;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceDetail {

    private  Integer deviceCategoryId;

    private  String deviceCategoryName;

    private  Integer deviceId;

    private  String deviceName;

    private  Integer resourceStructureId;

    public DeviceDetail(Equipment equipment){
        this.deviceCategoryId = equipment.getEquipmentBaseType();
        this.deviceId = equipment.getEquipmentId();
        this.deviceName = equipment.getEquipmentName();
        this.resourceStructureId = equipment.getResourceStructureId();
    }

}
