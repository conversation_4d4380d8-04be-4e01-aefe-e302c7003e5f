package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 字节 设备状态（带信号点位数）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentStateSignalPointDTO{
    /**
     * 局站Id
     */
    private Integer stationId;
    /**
     * 设备Id
     */
    private  Integer equipmentId;
    /**
     * 设备名
     */
    private  String equipmentName;
    /**
     * 设备模板id
     */
    private  Integer equipmentTemplateId;
    /**
     * 设备类型
     */
    private  Integer equipmentCategory;
    /**
     * 设备类型名
     */
    private  String equipmentCategoryName;
    /**
     * 设备品牌
     */
    private  String brand;
    /**
     * 设备型号
     */
    private  String equipmentStyle;
    /**
     * 层级ID
     */
    private  Integer resourceStructureId;
    /**
     * 层级路径
     */
    private String levelOfPath;
    /**
     *  设备位置
     */
    private String equipmentPosition;
    /**
     *  信号点位数
     */
    private Integer signalPointCount;
    /**
     * 在线状态
     */
    private Integer onlineState;
    /**
     * 在线状态
     */
    private String onlineStateName;
    /**
     * 告警状态
     */
    private Integer alarmState;
    /**
     * 告警状态
     */
    private String alarmStateName;
    /**
     * 最大告警级别
     */
    private Integer maxEventLevel;
    /**
     * 最大告警级别名称
     */
    private String maxEventLevelName;

}
