package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 设备状态查询统计
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentStatusStatisticsDTO {
    /**
     * 设备总数
     */
    private Long equSumCount;
    /**
     * 在线告警设备总数
     */
    private Long onlineAlarmEquCount;
    /**
     * 在线设备数
     */
    private Long onlineEquCount;
    /**
     * 离线设备数
     */
    private Long offlineEquCount;
    /**
     * 点位总数
     */
    private Long sumPoint;
}
