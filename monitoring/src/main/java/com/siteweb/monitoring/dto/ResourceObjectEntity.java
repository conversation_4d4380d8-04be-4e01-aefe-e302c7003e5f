package com.siteweb.monitoring.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class ResourceObjectEntity {
    /**
     * 资源id
     */
    private Integer objectId;
    /**
     * 资源类型id  @{SourceType}
     */
    private Integer objectTypeId;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 层级id 为了兼容前端v1
     */
    private Integer resourceStructureId;
    /**
     * 父级层级id(层级类型为父层级id,其他类型为所在层级id) 为了兼容前端v1
     */
    private Integer parentResourceStructureId;
    /**
     * 父级层级类型id 为了兼容前端v1
     */
    private Integer parentResourceStructureTypeId;
    /**
     * 层级关系
     */
    private String levelOfPath;
    /**
     * 源对象ID
     */
    private Integer originId;
}
