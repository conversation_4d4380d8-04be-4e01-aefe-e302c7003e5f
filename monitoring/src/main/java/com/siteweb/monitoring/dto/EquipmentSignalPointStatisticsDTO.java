package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 设备点位统计
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentSignalPointStatisticsDTO {
    private Integer equipmentCategory;
    private String equipmentCategoryName;
    private Integer equipmentTemplateId;
    private String equipmentTemplateName;
    private Integer equipmentCount;
    private String equipmentOnLineRate;
    private Long signalPointCount;
    private Long signalPointCountSum;
    private String equipmentStyle;
}