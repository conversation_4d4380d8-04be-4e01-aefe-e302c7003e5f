package com.siteweb.monitoring.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ControlDTO {
    /**
     * 设备位置信息
     */
    String equipmentPosition;

    /**
     * 设备名称
     */
    String equipmentName;

    /**
     * 设备类型
     */
    String baseEquipmentName;

    /**
     * 控制名称
     */
    String controlName;

    /**
     * 控制时间
     */
    Date execTime;

    /**
     * 响应时间
     */
    Date  responseTime;

    /**
     * 状态
     */
    String execResult;

    /**
     * 执行人名
     */
    String  controlExecuterIdName;
}
