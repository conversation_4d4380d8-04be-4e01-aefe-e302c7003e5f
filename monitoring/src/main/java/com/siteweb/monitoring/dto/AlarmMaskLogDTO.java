package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.monitoring.mamager.AlarmMaskTypeManager;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 告警屏蔽操作日志dto
 *
 * @Author: lzy
 * @Date: 2022/5/25 19:34
 */
@Data
public class AlarmMaskLogDTO {
    private Long id;

    @ApiModelProperty("局站id")
    private Integer stationId;

    @ApiModelProperty("局站")
    private String stationName;

    @ApiModelProperty("设备id")
    private Integer equipmentId;

    @ApiModelProperty("设备名")
    private String equipmentName;

    @ApiModelProperty("事件id")
    private Integer eventId;

    @ApiModelProperty("事件名")
    private String eventName;

    @ApiModelProperty("层级id")
    private Integer resourceStructureId;

    @ApiModelProperty("层级")
    private String resourceStructureName;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 对应库中 operationType 字段
     */
    @ApiModelProperty("操作描述（1-新增屏蔽/2-接触屏蔽）")
    private String operationType;

    @ApiModelProperty("操作时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    @ApiModelProperty("屏蔽时间段类型（1-全时段屏蔽/2-分时段屏蔽）")
    private String timeGroupCategory;

    @ApiModelProperty("开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("分时段屏蔽字符串")
    private String timeGroupChars;

    @ApiModelProperty("屏蔽原因")
    private String comment;

    public void setOperationType(String operationType) {
        this.operationType = AlarmMaskTypeManager.getDescByOperationType(Integer.valueOf(operationType));
    }
    public void setTimeGroupCategory(String timeGroupCategory) {
        this.timeGroupCategory = AlarmMaskTypeManager.getDescByTimeGroupCategory(Integer.valueOf(timeGroupCategory));
    }
}
