package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.ResourceStructure;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/05
 */
@Data
@NoArgsConstructor
public class ResourceStructureTreeDTO {

    /**
     * 层级ID
     */
    private Integer id;
    /**
     * 层级类型
     */
    private Integer tId;
    /**
     * 层级名称
     */
    private String name;

    /**
     * 父层级Id
     */
    private Integer pId;
    /**
     * 场景ID
     */
    private Integer sceneId;

    /**
     * 排序Index
     */
    @JsonIgnore
    private Integer sortValue;

    /**
     * 是否可点击
     */
    private Boolean accessFlag;

    /**
     * 子层级
     */
    List<ResourceStructureTreeDTO> children;

    public ResourceStructureTreeDTO(ResourceStructure resourceStructure) {
        this.id = resourceStructure.getResourceStructureId();
        this.name = resourceStructure.getResourceStructureName();
        this.tId = resourceStructure.getStructureTypeId();
        this.pId = resourceStructure.getParentResourceStructureId();
        this.sceneId = resourceStructure.getSceneId();
        this.children = new ArrayList<>();
        this.sortValue = resourceStructure.getSortValue();
        this.accessFlag = false;
    }

    /** 为了解决 BeanUtils.copyProperties 值拷贝性能问题 */
    public ResourceStructureTreeDTO(ResourceStructureTreeDTO resourceStructureTreeDTO) {
        this.id = resourceStructureTreeDTO.getId();
        this.name = resourceStructureTreeDTO.getName();
        this.tId = resourceStructureTreeDTO.getTId();
        this.pId = resourceStructureTreeDTO.getPId();
        this.sceneId = resourceStructureTreeDTO.getSceneId();
        this.sortValue = resourceStructureTreeDTO.getSortValue();
        this.children = new ArrayList<>();
        this.accessFlag = false;
    }

    public String getResourceStructureName() {
        return name;
    }
    public Integer getResourceStructureId() {
        return id;
    }
}
