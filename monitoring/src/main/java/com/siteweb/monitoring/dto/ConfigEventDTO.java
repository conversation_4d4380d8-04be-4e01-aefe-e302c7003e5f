package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.Event;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description ConfigEventDTO
 * @createTime 2022-05-19 14:14:20
 */
@Data
@NoArgsConstructor
public class ConfigEventDTO {

    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 当前活动告警含义
     */
    private String meaning;
    /**
     * 当前活动告警等级
     */
    private String eventSeverity;
    /**
     * 告警等级ID
     */
    private Integer eventLevel;
    /**
     * 配置告警等级
     */
    private Set<Integer> ceventlevel;
    /**
     * 当前活动告警开始时间
     */
    private Date startTime;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 事件ID
     */
    private Integer eventId;
    /**
     * 事件屏蔽设置状态
     */
    private Boolean mask;
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 关联信号ID
     */
    private Integer signalId;
    /**
     * 开始类型
     */
    private Integer startType;
    /**
     * 结束类型
     */
    private Integer endType;
    /**
     * 告警开始表达式
     */
    private String startExpression;
    /**
     * 告警抑制表达式
     */
    private String suppressExpression;
    /**
     * 告警分类
     */
    private Integer eventCategory;
    /**
     * 是否可用
     */
    private Boolean enable;
    /**
     * 是否可视
     */
    private Boolean visible;
    /**
     * MonitorUnitId
     */
    private Integer monitorUnitId;
    /**
     * 事件条件
     */
    private List<EventConditionDTO> eventConditions;

    public ConfigEventDTO(Event event) {
        this.setEventId(event.getEventId());
        this.setEventName(event.getEventName());
        this.setDisplayIndex(event.getDisplayIndex());
        this.setEquipmentTemplateId(event.getEquipmentTemplateId());
        this.setSignalId(event.getSignalId());
        this.setStartExpression(event.getStartExpression());
        this.setSuppressExpression(event.getSuppressExpression());
        this.setStartType(event.getStartType());
        this.setEndType(event.getEndType());
        this.setEventCategory(event.getEventCategory());
        this.setEnable(event.getEnable());
        this.setVisible(event.getVisible());
        this.setMask(false);
    }
}
