package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@NoArgsConstructor
public class StationProjectDetail {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 局站名称
     */
    private  String stationName;
    /**
     * 中心名称
     */
    private  String centerName;
    /**
     * 分组名称
     */
    private  String groupName;
    /**
     * 局站类型
     */
    private  String stationCategory;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date    endTime;
    /**
     * 工程原因
     */
    private String  reason;
    private  Integer userId;

}
