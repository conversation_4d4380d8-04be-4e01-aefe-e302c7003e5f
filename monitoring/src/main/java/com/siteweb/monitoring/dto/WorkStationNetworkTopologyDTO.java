package com.siteweb.monitoring.dto;

import lombok.Data;

import java.util.Date;

@Data
public class WorkStationNetworkTopologyDTO {
    private Integer workStationId;
    private String workStationName;
    private Integer workStationType;
    private String ipAddress;
    private Integer parentId;
    private Integer connectState;
    private Date updateTime;
    private Integer isUsed;
    private double cpu;
    private double memory;
    private int threadCount;
    private double diskFreeSpace;
    private Double dbFreeSpace;
    private Double lastCommTime;
    private String centerName;
    private Integer centerId;
}
