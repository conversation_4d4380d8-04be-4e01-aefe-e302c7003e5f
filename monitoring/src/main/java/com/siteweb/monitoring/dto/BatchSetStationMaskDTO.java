package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class BatchSetStationMaskDTO {
    /**
     * 局站IDs
     */
    @ApiModelProperty(value = "局站Id", name = "stationIds")
    private List<Integer> stationIdList;

    /**
     * 屏蔽原因
     */
    @ApiModelProperty(value = "屏蔽原因", name = "reason")
    private String reason;

    /**
     * 屏蔽开始时间
     */
    @ApiModelProperty(value = "开始时间", name = "startTime")
    private Date startTime;

    /**
     * 屏蔽结束时间
     */
    @ApiModelProperty(value = "结束时间", name = "endTime")
    private Date endTime;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;
}
