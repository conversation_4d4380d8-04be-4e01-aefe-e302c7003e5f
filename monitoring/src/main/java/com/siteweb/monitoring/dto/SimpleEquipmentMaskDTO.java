package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description SimpleEquipmentMaskDTO
 * @createTime 2022-05-30 14:31:04
 */
@Data
@NoArgsConstructor
public class SimpleEquipmentMaskDTO {

    private Integer stationId;

    private Integer equipmentId;

    private String equipmentName;

    /**
     * 屏蔽设置状态
     */
    private Boolean mask;
    /**
     * 屏蔽生效状态
     */
    private Boolean effective;
}
