package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.ResourceStructure;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ResourceStructureDTO
 * @createTime 2022-04-11 16:00:13
 */
@Data
@NoArgsConstructor
public class ResourceStructureDTO {

    /**
     * 层级ID
     */
    private Integer id;
    /**
     * 层级类型
     */
    private Integer tId;

    /**
     * 层级名称
     */
    private String name;

    /**
     * 父层级Id
     */
    @JsonIgnore
    private Integer parentId;
    /**
     * 排序字段
     */
    @JsonIgnore
    private Integer sortValue;

    /**
     * 子层级
     */
    List<ResourceStructureDTO> children;

    /**
     * 是否可点击
     */
    private Boolean accessFlag;

    public ResourceStructureDTO(ResourceStructure resourceStructure) {
        this.id = resourceStructure.getResourceStructureId();
        this.name = resourceStructure.getResourceStructureName();
        this.tId = resourceStructure.getStructureTypeId();
        this.parentId = resourceStructure.getParentResourceStructureId();
        this.children = new ArrayList<>();
        this.sortValue = resourceStructure.getSortValue();
        this.accessFlag = false;
    }
}
