package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StationDetail {
    /**
     * 层级Id
     */
    private Integer resourceStructureId;
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 局站名称
     */
    private  String stationName;
    /**
     * 中心名称
     */
    private  String centerName;
    /**
     * 分组名称
     */
    private  String groupName;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 维度
     */
    private Double latitude;
    /**
     * 联网状态
     */
    private OnlineState connectState;
    /**
     * 局站类型
     */
    private String stationCategory;
    /**
     * 局站等级
     */
    private  String stationGrade;
    /**
     * 代维商
     */
    private String maintainer;
    /***
     * 联系人
     */
    private String contact;
    /**
     * 备注信息
     */
    private String description;

    /**
     * 工程状态
     * 1	联网运行
     * 2	测试状态
     * 3	工程状态
     * 4	屏蔽状态
     */
    private Integer stationState;

    /**
     * 状态名
     */
    private String stationStateName;

    public StationDetail(Station station, String stationCategory, String stationGrade, String stationStateName){
        this.connectState = OnlineState.valueOf(station.getConnectState());
        this.contact = station.getContractNo();
        this.description = station.getDescription();
        this.stationId = station.getStationId();
        this.latitude = station.getLatitude();
        this.longitude = station.getLongitude();
        this.stationCategory = stationCategory;
        this.stationGrade = stationGrade;
        this.stationName = station.getStationName();
        this.stationState = station.getStationState();
        this.stationStateName = stationStateName;
    }
}
