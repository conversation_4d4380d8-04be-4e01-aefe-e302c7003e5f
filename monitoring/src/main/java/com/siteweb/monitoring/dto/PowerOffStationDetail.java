package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class PowerOffStationDetail {
    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 局站名称
     */
    private  String stationName;
    /**
     * 中心名称
     */
    private  String centerName;
    /**
     * 分组名称
     */
    private  String groupName;
    /**
     * 设备Id
     */
    private  Integer equipmentId;
    /**
     * 设备名
     */
    private  String equipmentName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 持续时间
     */
    private  Integer duration;

    public PowerOffStationDetail(ActiveEventDTO activeEventDTO){
        this.stationId =activeEventDTO.getStationId();
        this.eventName = activeEventDTO.getEventName();
        this.equipmentId = activeEventDTO.getEquipmentId();
        this.equipmentName =activeEventDTO.getEquipmentName();
        this.startTime = activeEventDTO.getStartTime();
    }
}
