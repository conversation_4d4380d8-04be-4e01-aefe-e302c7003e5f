package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.Data;

/**
 * @Author: lzy
 * @Date: 2022/7/26 14:38
 */
@Data
public class StationEquipmentDTO {
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 最高告警等级
     */
    private Integer maxEventSeverity;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备基类类型
     */
    private String equipmentType;
    /**
     * 设备分类id
     */
    private Integer EquipmentCategoryId;
    /**
     * 设备分类名称
     */
    private String equipmentCategoryName;
    /**
     * 是否屏蔽
     */
    private Boolean masked;
    /**
     * 是否在线
     */
    private OnlineState onlineStatus;
    /**
     * 是否工程状态
     */
    private  Boolean onProject;
    /**
     * 中心名称
     */
    private String centerName;
    /**
     * 机房名称
     */
    private String houseName;

    /**
     * 基站名称
     */
    private String StationName;
    /**
     * 局站类型
     */
    private String stationCategory;
    /**
     * 局站等级
     */
    private String stationGrade;
    /**
     * 设备品牌
     */
    private String brand;
    /**
     * 设备型号
     */
    private String equipmentStyle;
    /**
     * 额定容量
     */
    private String ratedCapacity;
    /**
     * 设备厂家
     */
    private  String vendor;
    /**
     * 下挂层级Id
     */
    private Integer resourceStructureId;

    /**
     * 局站Id
     */
    private Integer stationId;
    private Integer houseId;
}
