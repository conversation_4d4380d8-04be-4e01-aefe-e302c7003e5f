package com.siteweb.monitoring.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Event info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:38:30
 */
@Data
@NoArgsConstructor
public class EquipmentEventDto {

	/**
	 * 
	 */
	private Integer id;
	/**
	 * 设备模板ID
	 */
	private Integer equipmentTemplateId;
	/**
	 * 事件ID
	 */
	private Integer eventId;
	/**
	 * 事件名称
	 */
	private String eventName;
	/**
	 * 开始类型
	 */
	private Integer startType;
	/**
	 * 结束类型
	 */
	private Integer endType;
	/**
	 * 告警开始表达式
	 */
	private String startExpression;
	/**
	 * 告警抑制表达式
	 */
	private String suppressExpression;
	/**
	 * 告警分类（分标准，内部用）
	 */
	private Integer eventCategory;
	/**
	 * 关联信号ID
	 */
	private Integer signalId;
	/**
	 * 是否可用
	 */
	private Boolean enable;
	/**
	 * 是否可视
	 */
	private Boolean visible;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 显示顺序
	 */
	private Integer displayIndex;
	/**
	 * 模块号
	 */
	private Integer moduleNo;
	/**
	 * 设备id
	 */
	private Integer equipmentId;
}
