package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.siteweb.common.serializer.DoubleAllowNullSerializer;
import com.siteweb.common.serializer.DoubleNonNullSerializer;
import com.siteweb.monitoring.entity.HistoryEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HistoryEventDTO {
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 事件名
     */
    private String eventName;

    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 告警等级
     */
    private Integer eventLevel;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 触发值
     */
    @JsonSerialize(using = DoubleNonNullSerializer.class)
    private Double eventValue;
    /**
     * 翻转次数
     */
    private Integer reversalNum;
    /**
     * 告警含义
     */
    private String meanings;
    /**
     * 结束触发值
     */
    @JsonSerialize(using = DoubleAllowNullSerializer.class)
    private Double endValue;

    public HistoryEventDTO(HistoryEvent historyEvent){
        this.equipmentName = historyEvent.getEquipmentName();
        this.eventName = historyEvent.getEventName();
        this.startTime = historyEvent.getStartTime();
        this.confirmTime = historyEvent.getConfirmTime();
        this.endTime = historyEvent.getEndTime();
        this.eventValue = historyEvent.getEventValue();
        this.endValue = historyEvent.getEndValue();
        this.eventSeverity = historyEvent.getEventSeverity();
        this.meanings = historyEvent.getMeanings();
        this.reversalNum = historyEvent.getReversalNum();
        this.eventLevel = historyEvent.getEventLevel();
    }
}
