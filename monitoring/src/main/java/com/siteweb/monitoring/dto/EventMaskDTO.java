package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description EventMaskDTO
 * @createTime 2022-05-14 16:36:57
 */
@Data
@NoArgsConstructor
public class EventMaskDTO {

    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 基站ID
     */
    private Integer stationId;
    /**
     * 告警ID
     */
    private Integer eventId;
    /**
     * 时间分组ID
     */
    private Integer timeGroupId;
    /**
     * 屏蔽原因
     */
    private String reason;
    /**
     * 屏蔽开始时间
     */
    private Date startTime;
    /**
     * 屏蔽结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人ID
     */
    private Integer userId;
    /**
     * 屏蔽人
     */
    private String userName;
    /**
     * 告警名称
     */
    private String eventName;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备位置
     */
    private String equipmentPosition;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanDTO> timeGroupSpans;
}
