package com.siteweb.monitoring.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SamplerObjectType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PortTopology extends  TopologyBase {
    @JsonIgnore
    private SamplerObjectType nodeCategory = SamplerObjectType.PORT;

    @JsonIgnore
    private  String key;
    /**
     * 端口
     */
    @JsonIgnore
    private Integer portNo;

    private List<SamplerUnitTopology> children;

    public PortTopology(Port port){
        this.id = port.getId();
        this.name = port.getPortName();
        this.edge_name = port.getPortName();
        this.key = port.getKey();
        this.portNo = port.getPortNo();
        this.state = OnlineState.ONLINE;
    }

    public void setChildState(){
        if(children != null){
            for(SamplerUnitTopology samplerUnitTopology:children)
            {
                samplerUnitTopology.state = this.state;
            }
        }
    }
}
