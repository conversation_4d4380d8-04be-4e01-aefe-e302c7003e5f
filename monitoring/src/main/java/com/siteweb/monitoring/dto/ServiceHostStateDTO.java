package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> z<PERSON>
 * @description ServiceHostStateDTO
 * @createTime 2024-05-30 11:08:56
 */
@Data
@NoArgsConstructor
public class ServiceHostStateDTO {

    private Integer hostId;

    private Integer hostType;

    private Integer state;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date heartBeatTime;

    private Float cpuRate;

    private Float memRate;

    private Float diskRate;

    private Integer threadCount;

    @JsonProperty("interrupTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date interruptTime;

    private String sequenceId;
}
