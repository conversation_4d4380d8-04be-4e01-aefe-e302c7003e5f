package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceStructureOverview {

    private  Integer id;

    private  String name;

    private  Integer parentId;

    private SourceType sourceType;

    private List<DeviceStructureOverview> children = new ArrayList<>();

    private  List<DeviceDetail> deviceChildren = new ArrayList<>();

    public DeviceStructureOverview(ResourceStructure resourceStructure){
        this.id = resourceStructure.getResourceStructureId();
        this.name = resourceStructure.getResourceStructureName();
        this.parentId = resourceStructure.getParentResourceStructureId();
        this.sourceType = SourceType.valueOf(resourceStructure.getStructureTypeId());
    }
}