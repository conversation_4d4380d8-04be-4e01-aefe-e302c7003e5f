package com.siteweb.monitoring.dto;

import com.siteweb.monitoring.vo.TimeGroupSpanVO;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;


@Data
public class EventMaskFilterCreateDTO {
    private String keywords;
    private String eventName;
    private Set<Integer> equipmentIdList;
    private Collection<Integer> resourceStructureIdList;
    private Collection<Integer> equipmentBaseTypeIdList;
    private Collection<Integer> baseTypeIdList;
    private Collection<Integer> equipmentCategoryIdList;
    private Date startTime;

    private Date endTime;

    private String reason;

    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;

    /**
     * 分时段屏蔽TimeGroupSpans
     */
    private List<TimeGroupSpanVO> timeGroupSpans;
}
