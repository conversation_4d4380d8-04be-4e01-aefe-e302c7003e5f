package com.siteweb.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全量信号信息（全局通用，包括局站信息，设备信息， 信号配置信息， 实时信号信息，适合小批量数据获取）
 */
@Data
@NoArgsConstructor
@ApiModel(value="ActiveSignal实体",description="ActiveSignal实体")
public class ActiveSignal {
    /**
     *
     */
    private Integer stationId;
    /**
     *
     */
    private String stationName;
    /**
     * 设备Id
     */
    private Integer equipmentId;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 信号Id
     */
    private Integer signalId;
    /**
     *  信号名
     */
    private String  signalName;
    /**
     * 当前值
     */
    private String  currentValue;
    /**
     * 原始值
     */
    private  String originalValue;
    /**
     * 当前状态
     */
    private Integer currentState;
    /**
     * 信号类型
     */
    private Integer signalCategory;
    /**
     * 采集时间
     */
    private String sampleTime;
    /**
     * 拍训索引
     */
    private  Integer displayIndex;

    /**
     * 基类Id
     */
    private  Long baseTypeId;
    /**
     * 单位
     */
    @JsonIgnore
    private String unit;

    public String getRedisKey(){
        return   equipmentId + "." + signalId;
    }
}
