package com.siteweb.monitoring.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description FocusSignalDTO
 * @createTime 2022-08-05 15:31:58
 */
@Data
@NoArgsConstructor
public class FocusSignalDTO {
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备名
     */
    private String equipmentName;

    /**
     * 层级ID
     */
    private Integer resourceStructureId;

    /**
     * 设备位置
     */
    private String equipmentPosition;

    /**
     * 设备基类ID
     */
    private Integer baseEquipmentId;

    /**
     * 信号Id
     */
    private Integer signalId;

    /**
     * 信号名
     */
    private String signalName;

    /**
     * 信号单位
     */
    private String unit;
    /**
     * 基类Id
     */
    private Long baseTypeId;

    /**
     * 当前值
     */
    private String currentValue;
    /**
     * 原始值
     */
    private String originalValue;
    /**
     * 采集时间
     */
    private String sampleTime;

    public String getKey()
    {
        return equipmentId + "." + signalId;
    }
}
