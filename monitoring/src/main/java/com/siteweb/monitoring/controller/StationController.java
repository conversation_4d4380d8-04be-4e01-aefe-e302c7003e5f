package com.siteweb.monitoring.controller;


import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.StationConditionFilterDTO;
import com.siteweb.monitoring.service.StationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/02/11
 */
@RestController
@RequestMapping("/api")
@Api(value="StationController",tags={"局站控制器"})
public class StationController {
    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    StationService stationService;

    @ApiOperation(value = "获取所有局站")
    @GetMapping(value = "/station")
    public ResponseEntity<ResponseResult> getAllStation() {
        return ResponseHelper.successful(stationService.findStations());
    }

    @ApiOperation(value = "获取局站根据id")
    @GetMapping(value = "/station/{id}")
    public ResponseEntity<ResponseResult> getAllStation(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(stationService.findById(id));
    }


    @ApiOperation(value = "根据条件查询局站信息")
    @GetMapping(value = "/stationbycondition")
    public ResponseEntity<ResponseResult> getStationByCondition(StationConditionFilterDTO stationConditionFilterDTO) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(stationService.findByCondition(loginUserId, stationConditionFilterDTO));
    }
}
