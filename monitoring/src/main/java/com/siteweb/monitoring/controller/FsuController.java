package com.siteweb.monitoring.controller;

import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import org.springframework.data.domain.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.FsuService;
import com.siteweb.monitoring.vo.FsuFilterVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;


@RestController
@RequestMapping("/api")
@Api(value = "FsuController", tags = {"采集器控制器"})
public class FsuController {
    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    FsuService fsuService;

    @ApiOperation(value = "获取采集器型号字典")
    @GetMapping(value = "/fsutypelist")
    public ResponseEntity<ResponseResult> getFsuTypeList() {
        return ResponseHelper.successful(fsuService.findFsuTypeList());
    }


    @ApiOperation(value = "获取CPU使用率字典")
    @GetMapping(value = "/cpuusedlist")
    public ResponseEntity<ResponseResult> getCpuUsedList() {
        return ResponseHelper.successful(fsuService.findCpuUsedList());
    }

    @ApiOperation(value = "获取内存使用率字典")
    @GetMapping(value = "/memoryusedlist")
    public ResponseEntity<ResponseResult> getMemoryUsedList() {
        return ResponseHelper.successful(fsuService.findMemoryUsedList());
    }

    @ApiOperation(value = "获取Flash使用率字典")
    @GetMapping(value = "/flashusedlist")
    public ResponseEntity<ResponseResult> getFlashUsedList() {
        return ResponseHelper.successful(fsuService.findFlashUsedList());
    }

    @ApiOperation("获取FSU采集器列表")
    @GetMapping(value = "/getfsuinfo",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllFsu(Pageable pageable, FsuFilterVo filterVo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        Page<IcsFsuDataNewInfoDTO> result = fsuService.findAllFsu(userId, pageable, filterVo);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }
}
