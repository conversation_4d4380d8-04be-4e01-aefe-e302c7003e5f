package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.service.EventService;
import com.siteweb.monitoring.vo.EventRequestByCondition;
import com.siteweb.monitoring.vo.EventRequestBySignalId;
import com.siteweb.monitoring.vo.MultiEquipmentEventConditionBatchApplyVO;
import com.siteweb.monitoring.vo.SelfEquipmentEventConditionBatchApplyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Event info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:39:36
 */
@RestController
@RequestMapping("/api")
@Api(value = "EventController", tags = {"Event操作接口"})
public class EventController {

    private static final String USER_ID_IS_NULL = "userId is null";

    @Autowired
    EventService eventService;

    @ApiOperation(value = "通过局站ID和设备ID查找事件配置")
    @GetMapping(value = "/cfgevents",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"stationId", "equipmentId"})
    public ResponseEntity<ResponseResult> getConfigEventDTOsByEquipmentId(@RequestParam int stationId, @RequestParam int equipmentId) {
        List<ConfigEventDTO> configEventDTOS = eventService.findByStationIdAndEquipmentId(stationId, equipmentId);
        return ResponseHelper.successful(configEventDTOS);
    }

    @ApiOperation(value = "查找事件配置，只返回少量字段")
    @GetMapping(value = "/simpleeventdtos",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getSimpleEventDTOS(@RequestParam(name = "equipmentId", required = false) Integer equipmentId) {
        if (null == equipmentId) {
            Integer loginUserId = TokenUserUtil.getLoginUserId();
            return ResponseHelper.successful(eventService.findSimpleEventDTOsByUserId(loginUserId));
        } else {
            return ResponseHelper.successful(eventService.findSimpleEventDTOsByEquipmentId(equipmentId));
        }
    }

    @ApiOperation(value = "根据EquipmentId、EventId或SignalId查找事件配置，组装了事件条件，其中EventId和SignalId赋值时二选一")
    @GetMapping(value = "/cfgeventdtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getConfigEventDTOByEventId(@RequestParam(name = "equipmentId") int equipmentId,
                                                                     @RequestParam(name = "eventId", required = false) Integer eventId,
                                                                     @RequestParam(name = "signalId", required = false) Integer signalId) {
        if (null != eventId) {
            return ResponseHelper.successful(eventService.findConfigEventDTOByEventId(equipmentId, eventId));
        } else if (null != signalId) {
            return ResponseHelper.successful(eventService.findConfigEventDTOBySignalId(equipmentId, signalId));
        }
        return ResponseHelper.successful(null);
    }

    @ApiOperation(value = "事件动态配置")
    @PutMapping(value = "/cfgeventdtos",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateConfigEvent(@Valid @RequestBody ConfigEventDTO configEventDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        //StartExpression和SuppressExpression在保存前先进行URL decode
        configEventDTO.setStartExpression(URLDecoder.decode(configEventDTO.getStartExpression(), StandardCharsets.UTF_8));
        configEventDTO.setSuppressExpression(URLDecoder.decode(configEventDTO.getSuppressExpression(), StandardCharsets.UTF_8));
        for (EventConditionDTO conditionDTO : configEventDTO.getEventConditions()) {
            conditionDTO.setStartOperation(URLDecoder.decode(conditionDTO.getStartOperation(), StandardCharsets.UTF_8));
            conditionDTO.setEndOperation(URLDecoder.decode(conditionDTO.getEndOperation(), StandardCharsets.UTF_8));
        }
        int result = eventService.updateConfigEventDTO(userId, configEventDTO);
        if (result > 0) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.DYNAMIC_UPDATE_CONFIG_SIGNAL_ERROR.value()),
                "dynamic update config event error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ApiOperation(value = "告警规则设备内批量应用")
    @PutMapping(value = "/cfgeventdtos/self",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchApplyEventConditionToSelfEquipment(@Valid @RequestBody SelfEquipmentEventConditionBatchApplyVO vo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        List<Integer> noEventSignalIdList = eventService.batchApplyEventConditionToSelfEquipment(userId, vo);
        return ResponseHelper.successful(noEventSignalIdList);
    }

    @ApiOperation(value = "告警规则跨设备批量应用")
    @PutMapping(value = "/cfgeventdtos/multi",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchApplyEventConditionToMultiEquipment(@Valid @RequestBody MultiEquipmentEventConditionBatchApplyVO vo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        eventService.batchApplyEventConditionToMultiEquipment(userId, vo);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "通过设备id和信号ids获取事件")
    @PostMapping("/cfgeventbysignalids")
    public ResponseEntity<ResponseResult> getEvent(@RequestBody EventRequestBySignalId eventRequestBySignalId){
        return ResponseHelper.successful(eventService.findEventsByEquipmentIdAndSignalIds(eventRequestBySignalId));
    }

    @ApiOperation(value = "通过各种条件获取告警配置")
    @PostMapping("/cfgeventbycondition")
    public ResponseEntity<ResponseResult> getEventByCondition(@RequestBody EventRequestByCondition eventRequestByCondition){
        return ResponseHelper.successful(eventService.findEventsByEventRequestByCondition(eventRequestByCondition));
    }
}
