package com.siteweb.monitoring.controller;

import com.siteweb.monitoring.dto.DeviceStructureOverview;
import com.siteweb.monitoring.service.DeviceStructureOverViewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
public class DeviceStructureOverviewController {
    @Autowired
    private DeviceStructureOverViewService deviceStructureOverViewService;

    @GetMapping(value = "/devicestructureoverviews",
            params = {"devicecategory"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public List<DeviceStructureOverview> getDeviceStructureOverviewByCategory(@RequestParam(value = "devicecategory", required = true) Integer devicecategory) {
        return deviceStructureOverViewService.getDeviceStructureByCategory(devicecategory);
    }
}
