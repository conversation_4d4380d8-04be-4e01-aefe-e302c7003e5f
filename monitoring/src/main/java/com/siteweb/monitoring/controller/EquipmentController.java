package com.siteweb.monitoring.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.vo.EquipmentFilterVo;
import com.siteweb.monitoring.vo.EquipmentStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * tblEquipment info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:37:43
 */
@RestController
@RequestMapping("/api")
@Api(value = "EquipmentController", tags = {"Equipment操作接口"})
public class EquipmentController {

    @Autowired
    EquipmentService equipmentService;

    @Autowired
    ResourceObjectManager resourceObjectManager;
    private static final String USER_ID_IS_NULL = "userid is null";

    /**
     * GET  /equipments : get the Equipments.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of Equipments in body
     */
    @ApiOperation(value = "获取所有Equipment实体(可根据过滤条件查询)")
    @GetMapping("/equipmentdtos")
    public ResponseEntity<ResponseResult> getEquipments(EquipmentFilterVo filterVo) {
        return ResponseHelper.successful(equipmentService.findEquipmentDTOs(TokenUserUtil.getLoginUserId(), filterVo), HttpStatus.OK);
    }

    /**
     * GET  /equipments/:id  get the Equipment by id.
     *
     * @param equipmentId the EquipmentId
     * @return the ResponseEntity with status 200 (OK) and with body the Equipment, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个Equipment实体")
    @GetMapping("/equipments/{equipmentId}")
    public ResponseEntity<ResponseResult> getEquipmentById(@PathVariable("equipmentId") @ApiParam(name = "equipmentId", value = "唯一ID", required = true) Integer equipmentId) {
        Equipment equipment = equipmentService.findById(equipmentId);
        return Optional.ofNullable(equipment)
                .map(result -> ResponseHelper.successful(equipment, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * GET  /equipments?resourceStructureId=1  get the List<Equipment> by resourceStructureId.
     *
     * @param resourceStructureId the resourceStructureId
     * @return the ResponseEntity with status 200 (OK) and with body the Equipment, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据资源ID获取设备列表")
    @GetMapping(value = "/equipments", params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> findEquipmentsByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        List<Equipment> equipment = equipmentService.findEquipmentsByResourceStructureId(resourceStructureId);
        return Optional.ofNullable(equipment)
                .map(result -> ResponseHelper.successful(equipment, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "批量更新设备的排序")
    @PutMapping("/batch/order")
    public ResponseEntity<ResponseResult> updateBatchEquipmentOrder(@RequestBody List<UpdateEquipmentOrderDto> updateEquipmentOrderDtos) {
        return ResponseHelper.successful(equipmentService.updateBatchEquipmentOrder(updateEquipmentOrderDtos));
    }

    /**
     * GET  /equipmentobjects/:id  get the EquipmentDetail by id.
     *
     * @param equipmentId the EquipmentId
     * @return the ResponseEntity with status 200 (OK) and with body the EquipmentDetail, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个Equipment详细实体")
    @GetMapping("/equipmentobjects/{equipmentId}")
    public ResponseEntity<ResponseResult> getEquipmentDetailById(@PathVariable("equipmentId") @ApiParam(name = "equipmentId", value = "唯一ID", required = true) Integer equipmentId) {
        EquipmentDetail equipment = equipmentService.getEquipmentDetail(equipmentId);
        return Optional.ofNullable(equipment)
                .map(result -> ResponseHelper.successful(equipment, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据设备ID获取设备基本信息")
    @GetMapping("/equipmentbasic/{equipmentId}")
    public ResponseEntity<ResponseResult> getEquipmentBasicById(@PathVariable("equipmentId") @ApiParam(name = "equipmentId", value = "唯一ID", required = true) Integer equipmentId) {
        EquipmentBasicDto equipmentBasic = equipmentService.findEquipmentBasic(equipmentId);
        return Optional.ofNullable(equipmentBasic)
                .map(result -> ResponseHelper.successful(equipmentBasic, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "更新设备基础信息")
    @PutMapping("/equipmentbasic")
    public ResponseEntity<ResponseResult> updateEquipmentBasicInfo(@RequestBody EquipmentBasicDto equipmentBasicDto) {
        Boolean flag = equipmentService.updateEquipmentBasicInfo(equipmentBasicDto);
        if (Boolean.TRUE.equals(flag)) {
            return ResponseHelper.successful("update success");
        }
        return ResponseHelper.failed("update fail");
    }

    @ApiOperation(value = "批量应用设备信息")
    @PutMapping("/equipmentbasic/batchapply")
    public ResponseEntity<ResponseResult> batchApplyEquipmentInfo(@RequestBody BatchApplyEquipmentDTO batchApplyEquipmentDTO) {
        if (CollUtil.isEmpty(batchApplyEquipmentDTO.getDestEquipmentList()) || CollUtil.isEmpty(batchApplyEquipmentDTO.getFieldList())) {
            return ResponseHelper.failed("please select batch apply data");
        }
        Boolean flag = equipmentService.batchApplyEquipmentInfo(batchApplyEquipmentDTO);
        if (Boolean.TRUE.equals(flag)) {
            return ResponseHelper.successful("batch apply success");
        }
        return ResponseHelper.failed("batch apply fail");
    }

    /**
     * GET  /equipments?baseTypeId=1  get the List<Equipment> by baseTypeId.
     *
     * @param baseTypeId the baseTypeId
     * @return the ResponseEntity with status 200 (OK) and with body the Equipment, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据基类ID获取设备列表")
    @GetMapping(value = "/equipments", params = {"baseTypeId"})
    public ResponseEntity<ResponseResult> findEquipmentsByBaseTypeId(@RequestParam("baseTypeId") Integer baseTypeId) {
        List<EquipmentDTO> equipment = equipmentService.findEquipmentsByBaseTypeId(baseTypeId);
        return Optional.ofNullable(equipment)
                .map(result -> ResponseHelper.successful(equipment, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据基类IDS获取设备列表")
    @GetMapping(value = "/equipments", params = {"baseTypeIds"})
    public ResponseEntity<ResponseResult> findEquipmentsByBaseTypeId(@RequestParam("baseTypeIds") String baseTypeIds) {
        List<EquipmentDTO> equipment = equipmentService.findEquipmentsByBaseTypeIds(baseTypeIds);
        return Optional.ofNullable(equipment)
                       .map(result -> ResponseHelper.successful(equipment, HttpStatus.OK))
                       .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据条件统计设备条数")
    @PostMapping(value = "/equipmentcount", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDeviceCount(@RequestBody EquipmentStatisticsVO equipmentStatisticsVO) {
        Integer count = equipmentService.getEquipmentStatisticsByReq(equipmentStatisticsVO);

        return Optional.ofNullable(count)
                .map(result -> ResponseHelper.successful(count, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "获取待批量应用信号动态配置的设备信息")
    @GetMapping(value = "/todynamicapplyequipmentvos", params = {"stationId", "equipmentId"})
    public ResponseEntity<ResponseResult> findEquipmentsByBaseTypeId(@RequestParam("stationId") Integer stationId, @RequestParam("equipmentId") Integer equipmentId) {
        return ResponseHelper.successful(equipmentService.getToDynamicApplyEquipmentVOs(stationId, equipmentId));
    }

    @ApiOperation(value = "根据StationId查找SimpleEquipmentDTO对象，仅返回少数几个字段")
    @GetMapping(value = "/simpleequipmentdtos", params = {"stationId"})
    public ResponseEntity<ResponseResult> getSimpleEquipmentDTOsByStationId(@RequestParam("stationId") Integer stationId) {
        return ResponseHelper.successful(equipmentService.findSimpleEquipmentDTOByStationId(stationId));
    }

    @ApiOperation(value = "根据信号基类IDS获取拥有该信号基类的设备列表")
    @GetMapping(value = "/equipments", params = {"signalBaseTypeIds","resourceStructureIds"})
    public ResponseEntity<ResponseResult> findEquipmentsBySignalBaseTypeId(String signalBaseTypeIds,String resourceStructureIds) {
        return ResponseHelper.successful(equipmentService.findEquipmentsBySignalBaseTypeIdsAndResourceStructureIds(signalBaseTypeIds,resourceStructureIds));
    }
}
