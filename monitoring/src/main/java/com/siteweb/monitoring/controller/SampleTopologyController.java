package com.siteweb.monitoring.controller;


import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.SampleTopologyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/api")
@RestController
@Api(value = "SampleTopologyController", tags = {"采集拓扑查询接口"})
public class SampleTopologyController {

    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    SampleTopologyService sampleTopologyService;

    @ApiOperation(value = "获取采集拓扑数据")
    @GetMapping(value = "/centertopologies",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCenterTopology() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful( sampleTopologyService.getCenterTopology(loginUserId));
    }

}