package com.siteweb.monitoring.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.EventMask;
import com.siteweb.monitoring.service.EventMaskService;
import com.siteweb.monitoring.vo.BatchEventMaskVO;
import com.siteweb.monitoring.vo.EventMaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * EventMask info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:41:28
 */
@RestController
@RequestMapping("/api")
@Api(value = "EventMaskController", tags = {"EventMask操作接口"})
public class EventMaskController {

    @Autowired
    EventMaskService eventMaskService;

    /**
     * Post /eventmasks : create a new EventMask
     *
     * @param eventMaskVO the EventMask to create
     * @return the ResponseEntity with status 201 (Created) and with body the new EventMask,
     * or with status 400 (Bad Request) if the EventMask has already an ID
     */
    @ApiOperation(value = "保存事件屏蔽")
    @PostMapping(value = "/eventmasks")
    public ResponseEntity<ResponseResult> saveEventMask(@Valid @RequestBody EventMaskVO eventMaskVO) {
        eventMaskService.saveEventMask(eventMaskVO, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @NotNull
    private String userIdIsNull() {
        return "UserId is null";
    }

    @ApiOperation(value = "批量保存事件屏蔽")
    @PostMapping(value = "/eventmasks/batchcreate",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchCreateEventMasks(@Valid @RequestBody BatchEventMaskVO batchEventMaskVO) {
        if (null == batchEventMaskVO || batchEventMaskVO.getEquipmentIds() == null || batchEventMaskVO.getEquipmentIds().isEmpty()
                || batchEventMaskVO.getStationIds() == null || batchEventMaskVO.getStationIds().isEmpty()
                || batchEventMaskVO.getEventIds() == null || batchEventMaskVO.getEventIds().isEmpty()) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "Ids can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        eventMaskService.batchCreateEventMasks(batchEventMaskVO, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful();
    }

    /**
     * DELETE  /eventmasks/:id : delete the EventMask by id.
     *
     * @param id the id of the EventMask to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据ID删除事件屏蔽，这里的ID为StationId,EquipmentId,EventId")
    @DeleteMapping(value = "/eventmasks/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEventMask(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) String id) {
        if (null == id || id.trim().isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "id can not be null or empty",
                    HttpStatus.BAD_REQUEST);
        }
        String[] ids = id.split(",");
        if (ids.length != 3) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "id input error",
                    HttpStatus.BAD_REQUEST);
        }
        EventMask eventMask = eventMaskService.findById(Integer.parseInt(ids[0]), Integer.parseInt(ids[1]), Integer.parseInt(ids[2]));
        if (eventMask == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        eventMaskService.deleteEventMask(eventMask, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "批量删除事件屏蔽")
    @PostMapping(value = "/eventmasks/batchdelete",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteEventMasks(@Valid @RequestBody List<String> ids) {
        if (null == ids || ids.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "Ids is null",
                    HttpStatus.BAD_REQUEST);
        }
        eventMaskService.batchDeleteEventMasks(ids, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "删除该设备下所有事件屏蔽")
    @DeleteMapping(value = "/eventmasks/delbyequipmentid",
            params = {"stationId", "equipmentId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteEventMasksByEquipmentId(@RequestParam Integer stationId, @RequestParam Integer equipmentId) {
        if (null == stationId || null == equipmentId) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "stationId or equipmentId is null",
                    HttpStatus.BAD_REQUEST);
        }
        eventMaskService.batchDeleteEventMasksByEquipmentId(stationId, equipmentId, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "删除所有事件屏蔽")
    @DeleteMapping(value = "/eventmasks/deleteall",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAllEventMasks() {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    userIdIsNull(),
                    HttpStatus.BAD_REQUEST);
        }
        eventMaskService.deleteAllEventMasks(userId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "条件屏蔽，删除事件屏蔽")
    @DeleteMapping(value = "/eventmasks/config",params = {"baseTypeIds","equipmentBaseTypeIds","resourceStructureIds","equipmentCategories","eventName"})
    public ResponseEntity<ResponseResult> deleteEventMaskByBaseTypeIds(EventMaskFilterDTO eventMaskFilterDTO){
        if (CharSequenceUtil.isBlank(eventMaskFilterDTO.getResourceStructureIds()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getEquipmentBaseTypeIds()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getBaseTypeIds()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getEquipmentCategories()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getEventName())) {
            return ResponseHelper.successful();
        }
        eventMaskService.deleteEventMaskByBaseTypeIds(eventMaskFilterDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "获取可按关键字分页查询的事件屏蔽列表")
    @GetMapping(value = "/eventmasks",
            params = "keywords",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findEventMasksByKeywords(EventMaskFilterDTO eventMaskFilterDTO,Page<EventMaskDTO> page) {
        Page<EventMaskDTO> eventMasks = eventMaskService.findEventMaskByKeywords(eventMaskFilterDTO, page);
        return ResponseHelper.successful(eventMasks, HttpStatus.OK);
    }

    @ApiOperation("条件屏蔽，批量查询告警屏蔽")
    @GetMapping(value = "/eventmasks/config",params = {"baseTypeIds","equipmentBaseTypeIds","resourceStructureIds","equipmentCategories","eventName"})
    public ResponseEntity<ResponseResult> findEventMaskByBaseTypeIds(EventMaskFilterDTO eventMaskFilterDTO, Page<ConditionEventMaskDTO> page){
        if (CharSequenceUtil.isBlank(eventMaskFilterDTO.getResourceStructureIds()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getEquipmentBaseTypeIds()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getBaseTypeIds()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getEquipmentCategories()) && CharSequenceUtil.isBlank(eventMaskFilterDTO.getEventName())) {
            return ResponseHelper.successful(Page.of(page.getCurrent(), page.getSize()));
        }
        Page<ConditionEventMaskDTO> eventMasks = eventMaskService.findSimpleEventMaskByBaseTypeIdsPage(eventMaskFilterDTO, page);
        return ResponseHelper.successful(eventMasks, HttpStatus.OK);
    }

    @ApiOperation("条件屏蔽，批量创建告警屏蔽")
    @PostMapping(value = "/eventmasks/config",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEventMaskByFilter(@RequestBody EventMaskFilterCreateDTO eventMaskFilterCreateDTO){
        if (CollUtil.isEmpty(eventMaskFilterCreateDTO.getResourceStructureIdList()) && CollUtil.isEmpty(eventMaskFilterCreateDTO.getEquipmentBaseTypeIdList()) && CollUtil.isEmpty(eventMaskFilterCreateDTO.getBaseTypeIdList()) && CollUtil.isEmpty(eventMaskFilterCreateDTO.getEquipmentCategoryIdList())&&CharSequenceUtil.isBlank(eventMaskFilterCreateDTO.getEventName())) {
            return ResponseHelper.successful();
        }
        eventMaskService.createEventMaskByFilter(eventMaskFilterCreateDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "获取单个事件屏蔽")
    @GetMapping(value = "/eventmasks/{eventMaskIds}")
    public ResponseEntity<ResponseResult> findEventMaskDTOById(@PathVariable("eventMaskIds") @ApiParam(name = "eventMaskIds", value = "StationId,EquipmentId,EventId", required = true) String eventMaskIds) {
        if (null == eventMaskIds || eventMaskIds.isEmpty()) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "eventMaskIds is null",
                    HttpStatus.BAD_REQUEST);
        }
        List<Integer> ids = Arrays.stream(eventMaskIds.split(",")).map(Integer::parseInt).toList();
        if (ids.size() != 3) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "eventMaskIds input error",
                    HttpStatus.BAD_REQUEST);
        }
        EventMaskDTO eventMaskDTO = eventMaskService.findEventMaskDTOById(ids.get(0), ids.get(1), ids.get(2));
        if (eventMaskDTO == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(eventMaskDTO, HttpStatus.OK);
    }

    @ApiOperation(value = "根据EquipmentId查询所关联事件的屏蔽设置状态")
    @GetMapping(value = "/simpleeventmaskdtos",
            params = "equipmentId",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSimpleEventMaskDTOsByEquipmentId(@RequestParam Integer equipmentId) {
        List<SimpleEventMaskDTO> result = eventMaskService.findSimpleEventMaskDTOsByEquipmentId(equipmentId);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }
}
