package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.service.EventConditionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * tblEventcondition info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:36:51
 */
@RestController
@RequestMapping("/api")
@Api(value = "EventConditionController", tags = {"EventCondition操作接口"})
public class EventConditionController {

    @Autowired
    EventConditionService eventConditionService;

    @ApiOperation(value = "通过设备ID、事件ID或信号ID查找事件条件，事件ID和信号ID赋值时二选一")
    @GetMapping(value = "/eventconditiondtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getEventConditionDTOsByEventId(@RequestParam int equipmentId, @RequestParam(required = false) Integer eventId, @RequestParam(required = false) Integer signalId) {
        List<EventConditionDTO> eventConditionDTOS = null;
        if (null != eventId) {
            eventConditionDTOS = eventConditionService.findEventConditionDTOByEventId(equipmentId, eventId);
        } else if (null != signalId) {
            eventConditionDTOS = eventConditionService.findEventConditionDTOBySignalId(equipmentId, signalId);
        }
        return ResponseHelper.successful(eventConditionDTOS);
    }

    @ApiOperation(value = "通过设备id查找事件条件")
    @GetMapping(value = "/eventconditionbyequipmentid")
    public ResponseEntity<ResponseResult> getEventConditionByEquipmentId(@RequestParam Integer equipmentId) {
        return ResponseHelper.successful(eventConditionService.findEventConditionByEquipmentId(equipmentId));
    }
}
