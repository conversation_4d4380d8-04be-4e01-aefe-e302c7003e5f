package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.entity.CommonObject;
import com.siteweb.monitoring.service.CommonObjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value="通用对象管理",tags={"通用对象管理"})
public class CommonObjectController {

    @Autowired
    private CommonObjectService commonObjectService;

    @ApiOperation("获取所有通用对象")
    @GetMapping("/commonobjects")
    public ResponseEntity<ResponseResult> getAll(){
        return ResponseHelper.successful(commonObjectService.findAll());
    }

    @ApiOperation("获取所有通用对象")
    @GetMapping("/commonobjects/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(commonObjectService.findById(id));
    }

    @ApiOperation("添加通用对象")
    @PostMapping("/commonobjects")
    public ResponseEntity<ResponseResult> createCommonObject(@Validated @RequestBody CommonObject commonObject){
        return ResponseHelper.successful(commonObjectService.create(commonObject));
    }

    @ApiOperation("删除通用对象")
    @DeleteMapping("/commonobjects/{id}")
    public ResponseEntity<ResponseResult> deleteCommonObject(@PathVariable("id") Integer id){
        if (!commonObjectService.exists(id)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),"common object not found", HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(commonObjectService.deleteById(id));
    }

    @ApiOperation("修改通用对象")
    @PutMapping("/commonobjects/{id}")
    public ResponseEntity<ResponseResult> updateCommonObject(@Validated @RequestBody CommonObject commonObject){
        if (!commonObjectService.exists(commonObject.getCommonObjectId())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),"common object not found", HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(commonObjectService.update(commonObject));
    }
}
