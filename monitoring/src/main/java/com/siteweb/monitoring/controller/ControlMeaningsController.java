package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.ControlMeaningsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * tblControlmeanings info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:05
 */
@RestController
@RequestMapping("/api")
@Api(value = "ControlMeaningsController", tags = {"ControlMeanings操作接口"})
public class ControlMeaningsController {

    @Autowired
    ControlMeaningsService controlMeaningsService;

    @ApiOperation(value = "根据EquipmentId和ControlId查找ControlMeaningsDTO")
    @GetMapping(value = "/controlmeaningsdtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId", "controlId"})
    public ResponseEntity<ResponseResult> getControlMeaningsDTOS(@RequestParam(name = "equipmentId") int equipmentId, @RequestParam(name = "controlId") int controlId) {
        return ResponseHelper.successful(controlMeaningsService.findControlMeaningsDTOsByControlId(equipmentId, controlId));
    }
}
