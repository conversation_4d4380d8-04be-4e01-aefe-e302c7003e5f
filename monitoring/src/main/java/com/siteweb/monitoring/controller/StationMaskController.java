package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.StationMaskService;
import com.siteweb.monitoring.vo.StationMaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value="StationMaskController",tags={"局站屏蔽接口"})
public class StationMaskController {
    @Autowired
    StationMaskService stationMaskService;


    @ApiOperation(value = "设置局站屏蔽")
    @PostMapping(value = "/stationmasks")
    public ResponseEntity<ResponseResult> setStationMask(@RequestBody StationMaskVO stationMaskVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        stationMaskService.saveStationMask(stationMaskVO,userId);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "删除局站屏蔽")
    @DeleteMapping(value = "/stationmasks/{stationId}")
    public ResponseEntity<ResponseResult> deleteStationMask(@PathVariable Integer stationId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(stationMaskService.deleteStationMask(stationId,userId));
    }

    @ApiOperation(value = "获取局站屏蔽")
    @GetMapping(value = "/stationmasks/{stationId}")
    public ResponseEntity<ResponseResult> getStationMaskById(@PathVariable Integer stationId) {
        return ResponseHelper.successful(stationMaskService.getStationMaskById(stationId));
    }
}
