package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.PortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * tslPort info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:42:36
 */
@RestController
@RequestMapping("/api")
@Api(value="PortController",tags={"Port操作接口"})
public class PortController {

    @Autowired
    PortService portService;
    @ApiOperation("通过监控单元id查询端口信息")
    @GetMapping(value = "/port", params = "monitUnitId")
     public ResponseEntity<ResponseResult> getPort(Integer monitUnitId){
          return ResponseHelper.successful(portService.findPortByMonitUnitId(monitUnitId));
     }
}
