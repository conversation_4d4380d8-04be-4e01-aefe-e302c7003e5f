package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.ActiveSignalService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.StationDetailService;
import com.siteweb.monitoring.vo.StationFilterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 局站详情页面接口一览
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:37:43
 */
@RestController
@RequestMapping("/api")
@Api(value="StationController",tags={"局站详情页面接口一览"})
public class StationDetailController {
    @Autowired
    EquipmentService equipmentService;

    @Autowired
    ActiveSignalService activeSignalService;

    @Autowired
    StationDetailService stationDetailService;

    @ApiOperation(value = "根据局站对应的层级Id,获取下挂的设备Id,局站详请页面专用")
    @GetMapping(value = "/stationequipmentlists", params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> getStationEquipmentListByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(equipmentService.findStationPageEquipmentDTOByResourceStructureId(resourceStructureId));
    }

    @ApiOperation(value = "根据局站对应的层级Id,获取下挂局站重要信号一览")
    @GetMapping(value = "/stationimportantsignals", params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> getStationImportantSignalByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(activeSignalService.getStationImportantActiveSignalByResourceStructureId(resourceStructureId));
    }

    @ApiOperation(value = "根据局站对应的层级Id,获取局站信息")
    @GetMapping(value = "/stationdetails", params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> getStationDetailsByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(stationDetailService.getStationDetailByResourceStructureId(resourceStructureId));
    }

    @ApiOperation(value = "根据局站对应的层级Id, 下挂监控单元列表")
    @GetMapping(value = "/stationmonitorunits", params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> getMonitorUnitslByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(stationDetailService.getMonitorUnitByStationId(resourceStructureId));
    }

    @ApiOperation(value = "根据局站对应的层级Id,获取下挂局站重要信号一览")
    @GetMapping(value = "/stationsamplerunits", params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> getSamplerUnitByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(stationDetailService.getSamplerUnitDTOByStationId(resourceStructureId));
    }

    @ApiOperation(value = "根据局站对应的局站Id,获取局站信息")
    @GetMapping(value = "/stationdetails", params = {"stationId"})
    public ResponseEntity<ResponseResult> getStationDetailsByStationId(@RequestParam("stationId") Integer stationId) {
        return ResponseHelper.successful(stationDetailService.getStationDetailByStationId(stationId));
    }

    @ApiOperation("局站关注信号列表")
    @GetMapping(value = "/stationimportantsignals", params = {"stationId"})
    public ResponseEntity<ResponseResult> getStationImportantSignalByStationId(@RequestParam("stationId") Integer stationId) {
        if (stationId == null) {
            throw new BusinessException("局站id或基类类型不能为空");
        }
        return ResponseHelper.successful(activeSignalService.getStationImportantActiveSignalByStationId(stationId));
    }

    @ApiOperation("局站状态一览")
    @PostMapping(value = "/stationdetails")
    public ResponseEntity<ResponseResult> getStationDetailsByFilter(@Valid @RequestBody StationFilterVO stationFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "Operator is Invalid",
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(stationDetailService.queryStationDetails(userId, stationFilterVO));
    }



    @ApiOperation("基站停电一览")
    @GetMapping(value = "/stationpowerofflists")
    public ResponseEntity<ResponseResult> getStationPowerOffList() {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "Operator is Invalid",
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(stationDetailService.queryPowerOffStationDetails(userId));
    }

    @ApiOperation("按组去获取局站状态数据")
    @GetMapping(value = "/stationstatusalarmformap", params = {"stationId"})
    public ResponseEntity<ResponseResult> getStationGroupState(@RequestParam("stationId") String stationId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "Operator is Invalid",
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(stationDetailService.getStationGroupState(stationId));
    }
}
