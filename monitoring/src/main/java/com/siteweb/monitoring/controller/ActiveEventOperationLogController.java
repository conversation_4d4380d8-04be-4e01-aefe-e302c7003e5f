package com.siteweb.monitoring.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.ActiveEventOperationLogService;
import com.siteweb.monitoring.vo.ActiveEventOperationLogVO;
import com.siteweb.monitoring.vo.BatchActiveEventOperationLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> zhou
 * @description ActiveEventOperationLogController
 * @createTime 2022-04-09 14:48:20
 */
@RestController
@RequestMapping("/api")
@Api(value = "ActiveEventOperationLogController", tags = {"ActiveEventOperationLog操作接口"})
public class ActiveEventOperationLogController {

    @Autowired
    ActiveEventOperationLogService activeEventOperationLogService;

    private static final String USER_ID_IS_NULL = "userid is null";

    /**
     * GET  /activeeventoperationlogs  get the ActiveEventOperationLogs by sequenceId.
     *
     * @param sequenceId the sequenceId of ActiveEventOperationLogs
     * @return the ResponseEntity with status 200 (OK) and with the body of ActiveEventOperationLogs, or with status 404 (Not Found)
     */
    @ApiOperation(value = "查询ActiveEventOperationLog实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "ActiveEventOperationLog实体列表",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "sequenceId不允许为null",
                            content = {@Content})
            })
    @GetMapping("/activeeventoperationlogs")
    public ResponseEntity<ResponseResult> getActiveEventOperationLogsByLiveEventId(@RequestParam(value = "sequenceId") String sequenceId) {
        if (sequenceId == null || sequenceId.trim().isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "SequenceId is null",
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(activeEventOperationLogService.findBySequenceId(sequenceId));
    }

    /**
     * POST /activeeventoperationlogs : Create a new ActiveEventOperationLog.
     *
     * @param vo : the ActiveEventOperationLogVO
     * @return the ResponseEntity with status 200 (OK) and with the body of new ActiveEventOperationLogVO, or
     * with status 400 (Bad Request) if ActiveEventOperationLogId is not null
     */
    @ApiOperation(value = "新增ActiveEventOperationLog实体")
    @ApiOperationSupport(ignoreParameters = {"activeEventOperationLogId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "ActiveEventOperationLog创建成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "请求参数不正确", content = @Content),
                    @ApiResponse(responseCode = "500", description = "新增ActiveEventOperationLog报错", content = @Content)
            })
    @PostMapping(value = "/activeeventoperationlogs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createActiveEventOperationLog(@Valid @RequestBody ActiveEventOperationLogVO vo) {
        if (null != vo.getActiveEventOperationLogId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "ActiveEventOperationLogId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        int result = activeEventOperationLogService.createOperationLog(userId, vo.build());
        if (result > 0) {
            vo.setActiveEventOperationLogId(result);
            return ResponseHelper.successful(vo.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createActiveEventOperationLog error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * POST /batchactiveeventoperationlogs : batch create ActiveEventOperationLog.
     *
     * @param vo : the BatchActiveEventOperationLogVO
     * @return the ResponseEntity with status 200 (OK) , or with status 400 (Bad Request) if request parameter is null
     */
    @ApiOperation(value = "批量新增ActiveEventOperationLog实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "批量创建ActiveEventOperationLog成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "请求参数不正确", content = @Content)
            })
    @PostMapping(value = "/batchactiveeventoperationlogs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchCreateActiveEventOperationLog(@Valid @RequestBody BatchActiveEventOperationLogVO vo) {
        int result = activeEventOperationLogService.batchCreateOperationLog(TokenUserUtil.getLoginUserId(), vo);
        if (result < 0) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "request parameter is null",
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful();
    }
}
