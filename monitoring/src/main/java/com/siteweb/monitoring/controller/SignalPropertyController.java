package com.siteweb.monitoring.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * tblSignalproperty info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:38:34
 */
@RestController
@RequestMapping("/api")
@Api(value="SignalPropertyController",tags={"SignalProperty操作接口"})
public class SignalPropertyController {

}
