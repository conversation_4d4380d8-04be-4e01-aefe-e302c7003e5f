package com.siteweb.monitoring.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.EquipmentMaskDTO;
import com.siteweb.monitoring.dto.EquipmentMaskFilterCreateDTO;
import com.siteweb.monitoring.dto.EquipmentMaskFilterDTO;
import com.siteweb.monitoring.dto.SimpleEquipmentMaskDTO;
import com.siteweb.monitoring.entity.EquipmentMask;
import com.siteweb.monitoring.service.EquipmentMaskService;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import com.siteweb.monitoring.vo.EquipmentMaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Equipment Mask Record table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:39:36
 */
@RestController
@RequestMapping("/api")
@Api(value = "EquipmentMaskController", tags = {"EquipmentMask操作接口"})
public class EquipmentMaskController {
    @Autowired
    EquipmentMaskService equipmentMaskService;

    @ApiOperation(value = "保存设备屏蔽")
    @PostMapping(value = "/equipmentmasks", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveEquipmentMask(
            @Valid @RequestBody EquipmentMaskVO equipmentMaskVO) {
        if (null == equipmentMaskVO.getEquipmentId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "A new EquipmentMask cannot have an EquipmentId",
                    HttpStatus.BAD_REQUEST);
        }
        equipmentMaskVO.setTimeGroupId(Integer.parseInt("1" + Math.abs(equipmentMaskVO.getEquipmentId())));
        equipmentMaskService.saveEquipmentMask(equipmentMaskVO, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "批量保存设备屏蔽")
    @PostMapping(value = "/equipmentmasks/batchcreate",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchCreateEquipmentMasks(@Valid @RequestBody BatchEquipmentMaskVO batchEquipmentMaskVO) {
        if (null == batchEquipmentMaskVO || batchEquipmentMaskVO.getEquipmentIds() == null || batchEquipmentMaskVO.getEquipmentIds().isEmpty()) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EquipmentIds can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        equipmentMaskService.batchCreateEquipmentMasks(batchEquipmentMaskVO, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "删除设备屏蔽")
    @DeleteMapping(value = "/equipmentmasks/{equipmentId}")
    public ResponseEntity<ResponseResult> deleteEquipmentMask(@PathVariable Integer equipmentId) {
        EquipmentMask equipmentMask = equipmentMaskService.findById(equipmentId);
        if (equipmentMask == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        int result = equipmentMaskService.deleteEquipmentMask(equipmentMask, TokenUserUtil.getLoginUserId());
        if (result < 0) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.DELETE_OBJECT_ERROR.value()),
                    "delete equipmentMask error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "批量删除设备屏蔽")
    @PostMapping(value = "/equipmentmasks/batchdelete",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteEquipmentMasks(@Valid @RequestBody List<Integer> ids) {
        if (null == ids || ids.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EquipmentIds is null",
                    HttpStatus.BAD_REQUEST);
        }
        equipmentMaskService.batchDeleteEquipmentMasks(ids, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "删除该ResourceStructureId下所有设备屏蔽")
    @DeleteMapping(value = "/equipmentmasks/delbyresourcestructureid",
            params = "resourceStructureId",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteEquipmentMasksByResourceStructureId(@RequestParam Integer resourceStructureId) {
        if (null == resourceStructureId) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "resourceStructureId is null",
                    HttpStatus.BAD_REQUEST);
        }
        equipmentMaskService.batchDeleteEquipmentMasksByResourceStructureId(resourceStructureId, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "删除所有设备屏蔽")
    @DeleteMapping(value = "/equipmentmasks/deleteall",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAllEquipmentMasks() {
        equipmentMaskService.deleteAllEquipmentMasks(TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "获取单个设备屏蔽")
    @GetMapping(value = "/equipmentmasks/{equipmentId}")
    public ResponseEntity<ResponseResult> findEquipmentMaskDTOById(@PathVariable("equipmentId") @ApiParam(name = "equipmentId", value = "唯一ID", required = true) Integer equipmentId) {
        EquipmentMaskDTO equipmentMaskDTO = equipmentMaskService.findEquipmentMaskDTOByEquipmentId(equipmentId);
        if (equipmentMaskDTO == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(equipmentMaskDTO, HttpStatus.OK);
    }

    @ApiOperation(value = "获取可按关键字分页查询的设备屏蔽列表")
    @GetMapping(value = "/equipmentmasks",
            params = "keywords",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findEquipmentMasksByKeywords(EquipmentMaskFilterDTO equipmentMaskFilterDTO, Page<EquipmentMaskDTO> page) {
        Page<EquipmentMaskDTO> equipmentMasks = equipmentMaskService.findEquipmentMaskByKeywords(equipmentMaskFilterDTO, page);
        return ResponseHelper.successful(equipmentMasks, HttpStatus.OK);
    }

    @ApiOperation("条件屏蔽，查询告警屏蔽")
    @GetMapping(value = "/equipmentmasks/config",params = {"equipmentBaseTypeIds","resourceStructureIds","keywords"})
    public ResponseEntity<ResponseResult> findEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterDTO equipmentMaskFilterDTO, Page<SimpleEquipmentMaskDTO> page){
        if (CharSequenceUtil.isBlank(equipmentMaskFilterDTO.getResourceStructureIds()) && CharSequenceUtil.isBlank(equipmentMaskFilterDTO.getEquipmentBaseTypeIds()) && CharSequenceUtil.isBlank(equipmentMaskFilterDTO.getEquipmentCategories())) {
            return ResponseHelper.successful(Page.of(page.getCurrent(),page.getSize()));
        }
        Page<SimpleEquipmentMaskDTO> equipmentMasks = equipmentMaskService.findSimpleEquipmentMaskByEquipmentBaseTypes(equipmentMaskFilterDTO, page);
        return ResponseHelper.successful(equipmentMasks, HttpStatus.OK);
    }

    @ApiOperation("条件屏蔽，删除告警屏蔽")
    @DeleteMapping(value = "/equipmentmasks/config",params = {"equipmentBaseTypeIds","resourceStructureIds","keywords"})
    public ResponseEntity<ResponseResult> deleteEquipmentMaskByEquipmentBaseTypes(EquipmentMaskFilterDTO equipmentMaskFilterDTO){
        if (CharSequenceUtil.isBlank(equipmentMaskFilterDTO.getResourceStructureIds()) && CharSequenceUtil.isBlank(equipmentMaskFilterDTO.getEquipmentBaseTypeIds()) && CharSequenceUtil.isBlank(equipmentMaskFilterDTO.getEquipmentCategories())) {
            return ResponseHelper.successful();
        }
        equipmentMaskService.deleteEquipmentMaskByEquipmentBaseTypes(equipmentMaskFilterDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation("条件屏蔽，添加告警屏蔽")
    @PostMapping(value = "/equipmentmasks/config",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createEquipmentMaskByEquipmentBaseTypes(@RequestBody EquipmentMaskFilterCreateDTO equipmentMaskFilterDTO){
        if (CollUtil.isEmpty(equipmentMaskFilterDTO.getResourceStructureIdList()) && CollUtil.isEmpty(equipmentMaskFilterDTO.getEquipmentBaseTypeIdList()) && CollUtil.isEmpty(equipmentMaskFilterDTO.getEquipmentCategoryIdList())) {
            return ResponseHelper.successful();
        }
        equipmentMaskService.createEquipmentMaskByEquipmentBaseTypes(equipmentMaskFilterDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "根据ResourceStructureId级联查询所关联设备的屏蔽设置状态")
    @GetMapping(value = "/simpleequipmentmaskdtos",
            params = "resourceStructureId",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSimpleEquipmentMaskDTOsByResourceStructureId(@RequestParam Integer resourceStructureId) {
        List<SimpleEquipmentMaskDTO> result = equipmentMaskService.findSimpleEquipmentMaskDTOsByResourceStructureId(resourceStructureId);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }
}
