package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.entity.ActiveEventFilterTemplate;
import com.siteweb.monitoring.service.ActiveEventFilterTemplateService;
import com.siteweb.monitoring.vo.ActiveEventFilterTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterTemplateController
 * @createTime 2022-04-27 19:20:33
 */
@RestController
@RequestMapping("/api")
@Api(value = "ActiveEventFilterTemplateController", tags = {"ActiveEventFilterTemplate操作接口"})
public class ActiveEventFilterTemplateController {

    @Autowired
    ActiveEventFilterTemplateService activeEventFilterTemplateService;

    private static final String USER_ID_IS_NULL = "userid is null";

    /**
     * GET /activeeventfiltertemplates : get activeEventFilterTemplates by userId and filterType.
     *
     * @param filterType filterType
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据UserId和FilterType查询ActiveEventFilterTemplate实体")
    @GetMapping(value = "/activeeventfiltertemplates",
            params = {"filterType"},
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<ResponseResult> getActiveEventFilterTemplateByUserIdAndFilterType(@RequestParam String filterType) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        List<ActiveEventFilterTemplate> activeEventFilterTemplates = activeEventFilterTemplateService.findByUserIdAndFilterType(userId, filterType);
        return ResponseHelper.successful(activeEventFilterTemplates, HttpStatus.OK);
    }

    /**
     * GET /activeeventfiltertemplates/:activeEventFilterTemplateId : get activeEventFilterTemplate by activeEventFilterTemplateId.
     *
     * @param activeEventFilterTemplateId activeEventFilterTemplateId
     * @return the ResponseEntity with status 200 (OK), or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ActiveEventFilterTemplateId查询ActiveEventFilterTemplate实体")
    @GetMapping(value = "/activeeventfiltertemplates/{activeEventFilterTemplateId}")
    public ResponseEntity<ResponseResult> getActiveEventFilterTemplateById(@PathVariable Integer activeEventFilterTemplateId) {
        ActiveEventFilterTemplate activeEventFilterTemplate = activeEventFilterTemplateService.findByActiveEventFilterTemplateId(activeEventFilterTemplateId);
        return Optional.ofNullable(activeEventFilterTemplate)
                .map(result -> ResponseHelper.successful(activeEventFilterTemplate, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * POST /activeeventfiltertemplates : Create a new activeEventFilterTemplate.
     *
     * @param activeEventFilterTemplateVO the activeEventFilterTemplateVO to create
     * @return the ResponseEntity with status 200 (OK) , or with status 500 (error)
     */
    @ApiOperation(value = "新增ActiveEventFilterTemplate实体")
    @PostMapping(value = "/activeeventfiltertemplates",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createActiveEventFilterTemplate(@Valid @RequestBody ActiveEventFilterTemplateVO activeEventFilterTemplateVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        ActiveEventFilterTemplate activeEventFilterTemplate = activeEventFilterTemplateVO.build(activeEventFilterTemplateVO.getFilterType(), userId);
        int result = activeEventFilterTemplateService.createActiveEventFilterTemplate(activeEventFilterTemplate);
        if (result > 0) {
            activeEventFilterTemplateVO.setActiveEventFilterTemplateId(result);
            return ResponseHelper.successful(activeEventFilterTemplate);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createActiveEventFilterTemplate error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /activeeventfiltertemplates : Update activeEventFilterTemplate.
     *
     * @param activeEventFilterTemplateVO the activeEventFilterTemplateVO to update
     * @return the ResponseEntity with status 200 (OK) , or with status 400 (error)
     */
    @ApiOperation(value = "修改ActiveEventFilterTemplate实体")
    @PutMapping(value = "/activeeventfiltertemplates",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateActiveEventFilterTemplate(@Valid @RequestBody ActiveEventFilterTemplateVO activeEventFilterTemplateVO) {
        if (activeEventFilterTemplateVO.getActiveEventFilterTemplateId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "ActiveEventFilterTemplateId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        ActiveEventFilterTemplate activeEventFilterTemplate = activeEventFilterTemplateVO.build(activeEventFilterTemplateVO.getFilterType(), userId);
        int result = activeEventFilterTemplateService.updateActiveEventFilterTemplate(activeEventFilterTemplate);
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "ActiveEventFilterTemplateId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * DELETE /activeeventfiltertemplates : delete activeEventFilterTemplate by activeEventFilterTemplateId.
     *
     * @param activeEventFilterTemplateId activeEventFilterTemplateId
     * @return the ResponseEntity with status 200 (OK) or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ActiveEventFilterTemplateId删除ActiveEventFilterTemplate实体")
    @DeleteMapping(value = "activeeventfiltertemplates/{activeEventFilterTemplateId}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteActiveEventFilterTemplateById(@PathVariable Integer activeEventFilterTemplateId) {
        ActiveEventFilterTemplate activeEventFilterTemplate = activeEventFilterTemplateService.findByActiveEventFilterTemplateId(activeEventFilterTemplateId);
        if (null == activeEventFilterTemplate) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        activeEventFilterTemplateService.deleteActiveEventFilterTemplate(activeEventFilterTemplateId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * POST /activeeventfiltertemplates : Create a new activeEventFilterTemplate.
     *
     * @param activeEventFilterTemplateVO the activeEventFilterTemplateVO to create
     * @return the ResponseEntity with status 200 (OK) , or with status 500 (error)
     */
    @ApiOperation(value = "新增或更新ActiveEventFilterTemplate实体,用于模板不是列表是单个的情况")
    @PutMapping(value = "/saveOrUpdateTempalte",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveOrUpdateTempalte(@Valid @RequestBody ActiveEventFilterTemplateVO activeEventFilterTemplateVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        ActiveEventFilterTemplate activeEventFilterTemplate = activeEventFilterTemplateVO.build(activeEventFilterTemplateVO.getFilterType(), userId);
        //定制列是用户级的
        activeEventFilterTemplate.setUserId(userId);
        int result = activeEventFilterTemplateService.createOrUpdateTemplate(activeEventFilterTemplate);
        if (result > 0) {
            activeEventFilterTemplateVO.setActiveEventFilterTemplateId(result);
            return ResponseHelper.successful(activeEventFilterTemplate);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "createActiveEventFilterTemplate error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
