package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.entity.EventReasonType;
import com.siteweb.monitoring.service.EventReasonTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 告警原因分类相关接口
 * Creation Date: 2025/3/6
 */
@RestController
@RequestMapping("/api")
@Api(value = "EventConfirmTypeController", tags = {"EventConfirmType操作接口"})
public class EventReasonTypeController {
    @Autowired
    EventReasonTypeService eventReasonTypeService;

    @ApiOperation(value = "查询告警原因分类列表")
    @GetMapping("/eventreasontypes")
    public ResponseEntity<ResponseResult> listEventReasonType() {
        List<EventReasonType> eventReasonTypes = eventReasonTypeService.listEventReasonType();
        return ResponseHelper.successful(eventReasonTypes);
    }

    @ApiOperation(value = "查询告警原因分类")
    @GetMapping("/eventreasontypes/{id}")
    public ResponseEntity<ResponseResult> getEventReasonTypeById(@PathVariable Integer id) {
        EventReasonType eventReasonType = eventReasonTypeService.getEventReasonTypeById(id);
        return ResponseHelper.successful(eventReasonType);
    }

    @ApiOperation(value = "新增告警原因分类")
    @PostMapping("/eventreasontypes")
    public ResponseEntity<ResponseResult> saveEventReasonType(@RequestBody EventReasonType eventReasonType) {
        eventReasonType.setId(null);
        eventReasonTypeService.saveEventReasonType(eventReasonType);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "修改告警原因分类")
    @PutMapping("/eventreasontypes")
    public ResponseEntity<ResponseResult> updateEventReasonType(@RequestBody EventReasonType eventReasonType) {
        eventReasonTypeService.updateEventReasonType(eventReasonType);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "删除告警原因分类")
    @DeleteMapping("/eventreasontypes/{ids}")
    public ResponseEntity<ResponseResult> deleteEventReasonType(@PathVariable String ids) {
        eventReasonTypeService.deleteEventReasonType(ids);
        return ResponseHelper.successful();
    }
}
