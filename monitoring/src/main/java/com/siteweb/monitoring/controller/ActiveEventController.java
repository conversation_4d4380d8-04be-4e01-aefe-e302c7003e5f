package com.siteweb.monitoring.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.monitoring.vo.ActiveEventOperationVO;
import com.siteweb.monitoring.vo.ConfigEventCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 活动告警表
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:05
 */
@RestController
@RequestMapping("/api")
@Api(value = "ActiveEventController", tags = {"ActiveEvent操作接口"})
public class ActiveEventController {

    @Autowired
    ActiveEventService activeEventService;

    private static final String USER_ID_IS_NULL = "userid is null";

    /**
     * GET  /activeeventdtos  query activeEventDTOs
     *
     * @param pageable            pageable
     * @param activeEventFilterVO activeEventFilterVO
     * @return the ResponseEntity with status 200 (OK) and with the body of the pageable activeEventDTOs
     */
    @ApiOperation(value = "根据ActiveEventFilterVO分页查询ActiveEventDTO实体")
    @GetMapping("/activeeventdtos")
    public ResponseEntity<ResponseResult> getActiveEventByFilterVO(Pageable pageable, ActiveEventFilterVO activeEventFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Page<ActiveEventDTO> activeEventDTOS = activeEventService.queryActiveEvents(userId, pageable, activeEventFilterVO);
        return ResponseHelper.successful(activeEventDTOS, HttpStatus.OK);
    }

    /**
     * GET  /activeevents/groupby  query activeEvents
     *
     * @param activeEventFilterVO activeEventFilterVO
     * @return the ResponseEntity with status 200 (OK) and with the body of the groupby result
     */
    @ApiOperation(value = "根据ActiveEventFilterVO查询活动告警统计")
    @GetMapping("/activeeventdtos/groupby")
    public ResponseEntity<ResponseResult> groupActiveEventByFilterVOAndSeverityId(ActiveEventFilterVO activeEventFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        Map<Integer, Integer> map = activeEventService.groupActiveEventsBySeverity(userId, activeEventFilterVO);
        return ResponseHelper.successful(map, HttpStatus.OK);
    }

    /**
     * GET  /activeeventdtos/{sequenceId}  query activeEventDTO by sequenceId
     *
     * @param sequenceId sequenceId
     * @return the ResponseEntity with status 200 (OK) and with the body of the activeEventDTO
     */
    @ApiOperation(value = "根据SequenceId查询ActiveEventDTO实体")
    @GetMapping("/activeeventdtos/{sequenceId}")
    public ResponseEntity<ResponseResult> getActiveEventBySequenceId(@PathVariable String sequenceId) {
        ActiveEventDTO activeEventDTO = activeEventService.getActiveEventDTOBySequenceId(sequenceId);
        if (null == activeEventDTO) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(activeEventDTO, HttpStatus.OK);
    }

    /**
     * 告警确认
     *
     * @param activeEventOperationVO activeEventOperationVO
     * @return
     */
    @ApiOperation(value = "确认活动告警")
    @PostMapping(value = "/activeevents/confirm",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> confirmActiveEvents(@Valid @RequestBody ActiveEventOperationVO activeEventOperationVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        boolean result = activeEventService.confirmActiveEvents(userId, activeEventOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.ACTIVE_EVENT_CONFIRM_ERROR.value()),
                "confirm active event error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 告警强制结束
     *
     * @param activeEventOperationVO activeEventOperationVO
     * @return
     */
    @ApiOperation(value = "强制结束活动告警")
    @PostMapping(value = "/activeevents/cancel",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> cancelActiveEvents(@Valid @RequestBody ActiveEventOperationVO activeEventOperationVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        boolean result = activeEventService.cancelActiveEvents(userId, activeEventOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.ACTIVE_EVENT_CANCEL_ERROR.value()),
                "cancel active event error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 告警备注
     *
     * @param activeEventOperationVO activeEventOperationVO
     * @return
     */
    @ApiOperation(value = "活动告警备注")
    @PostMapping(value = "/activeevents/note",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addEventNote(@Valid @RequestBody ActiveEventOperationVO activeEventOperationVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        boolean result = activeEventService.addEventNote(userId, activeEventOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.ADD_EVENT_NOTE_ERROR.value()),
                "add event note error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * GET  /unconfirmactiveeventdtos  query unConfirmed activeEventDTOs
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the unConfirmed activeEventDTOs
     */
    @ApiOperation(value = "查询所有未确认的ActiveEventDTO实体")
    @GetMapping("/unconfirmactiveeventdtos")
    public ResponseEntity<ResponseResult> getUnConfirmActiveEvents() {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        List<ActiveEventDTO> unConfirmActiveEvents = activeEventService.queryUnConfirmActiveEvents(userId);
        return ResponseHelper.successful(unConfirmActiveEvents, HttpStatus.OK);
    }


    /**
     * GET  /unendactiveeventdtos  query unEnd activeEventDTOs
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the unEnd activeEventDTOs
     */
    @ApiOperation(value = "查询所有未结束的ActiveEventDTO实体")
    @GetMapping("/unendactiveeventdtos")
    public ResponseEntity<ResponseResult> getUnEndActiveEvents() {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        List<ActiveEventDTO> unConfirmActiveEvents = activeEventService.queryUnEndActiveEvents(userId);
        return ResponseHelper.successful(unConfirmActiveEvents, HttpStatus.OK);
    }

    /**
     * GET  /top1activeeventgreaterthan  query latest activeEventDTO by startTime
     *
     * @param startTime startTime
     * @return the ResponseEntity with status 200 (OK) and with the body of the activeEventDTO
     */
    @ApiOperation(value = "根据startTime查询最近一条ActiveEventDTO实体")
    @GetMapping("/top1activeeventgreaterthan")
    public ResponseEntity<ResponseResult> findTopOneActiveEventByStartTime(@RequestParam Date startTime) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        ActiveEventDTO activeEventDTO = activeEventService.findTopOneActiveEventByStartTimeAndUserId(startTime, userId);
        if (null == activeEventDTO) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.successful(activeEventDTO, HttpStatus.OK);
    }
    @ApiOperation(value = "通过设备ids获取其所有告警")
    @PostMapping(value = "/activeeventsbyequipmentids", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getLiveEventsByDeviceIds(@RequestBody List<Integer> equipmentIds) {
        return ResponseHelper.successful(activeEventService.findActiveEventsByEquipmentIds(CollUtil.join(equipmentIds,","),null));
    }

    @ApiOperation(value = "通过设备ids获取其所有告警(多个用逗号隔开)")
    @GetMapping(value = "/active-events",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentIds"})
    public ResponseEntity<ResponseResult> getLiveEventsByDeviceIds(@RequestParam("equipmentIds") String equipmentIds, Boolean eventEnded) {
        return ResponseHelper.successful(activeEventService.findActiveEventsByEquipmentIds(equipmentIds, eventEnded));
    }

    @ApiOperation(value = "通过设备id获取其所有告警")
    @GetMapping(value = "/active-events",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getLiveEventsByDeviceId(@RequestParam(value = "equipmentId", required = true) Integer equipmentId, Boolean eventEnded,Boolean eventConfirmed) {
        List<ActiveEventDTO> result = activeEventService.findActiveEventsByEquipmentId(equipmentId, eventEnded, eventConfirmed);
        return ResponseHelper.successful(result);
    }
    @ApiOperation(value = "告警模拟")
    @PostMapping(value = "/active-events/simulate",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> simulateActiveEvent(@Valid @RequestBody ConfigEventCondition configEventCondition) {
        return ResponseHelper.successful(activeEventService.simulateActiveEvent(configEventCondition));
    }
    @ApiOperation(value = "通过局站ids获取其所有告警(多个用逗号隔开)")
    @PostMapping(value = "/active-events", produces = MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<ResponseResult> getLiveEventsByStationIds(@RequestBody List<Integer> stationIds) {
        List<ActiveEventDTO> result = activeEventService.findActiveEventsByStationIds(stationIds, null);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "通过设备id.告警id查询活动告警信息(多个用逗号隔开)")
    @GetMapping(value = "/active-events",params = "equipmentEventKeys")
    public ResponseEntity<ResponseResult> getActiveEventByEquipmentEventKeys(String equipmentEventKeys) {
        List<ActiveEventDTO> result = activeEventService.getActiveEventByEquipmentEventKeys(TokenUserUtil.getLoginUserId(),equipmentEventKeys);
        return ResponseHelper.successful(result);
    }
}
