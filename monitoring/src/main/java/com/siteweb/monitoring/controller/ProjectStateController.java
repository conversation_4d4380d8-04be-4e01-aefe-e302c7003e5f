package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.ProjectStateService;
import com.siteweb.monitoring.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value="ProjectStateController",tags={"工程状态接口"})
public class ProjectStateController {
    @Autowired
    ProjectStateService projectStateService;

    @ApiOperation(value = "设置局站工程状态")
    @PostMapping(value = "/stationprojectstatus")
    public ResponseEntity<ResponseResult> setStationProjectStatus(@RequestBody StationProjectVO stationProjectVO) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(projectStateService.setStationProject(stationProjectVO,userId));
    }

    @ApiOperation(value = "删除局站工程状态")
    @DeleteMapping(value = "/stationprojectstatus/{stationId}")
    public ResponseEntity<ResponseResult> deleteStationProjectStatus(@PathVariable Integer stationId) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(projectStateService.deleteStationProject(stationId,userId));
    }
    @ApiOperation(value = "获取局站工程状态")
    @GetMapping(value = "/stationprojectstatus/{stationId}")
    public ResponseEntity<ResponseResult> getStationProjectStatus(@PathVariable Integer stationId) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(projectStateService.getStationProject(stationId));
    }

    @ApiOperation("局站工程状态一览")
    @PostMapping(value = "/stationprojectdetails")
    public ResponseEntity<ResponseResult> getStationProjectDetailsByFilter(@Valid @RequestBody StationFilterVO stationFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(projectStateService.queryStationProjectDetails(userId, stationFilterVO));
    }

    @ApiOperation(value = "批量删除局站工程状态")
    @PostMapping(value = "/stationprojectstatus/batchdelete",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteStationProjects(@Valid @RequestBody List<Integer> ids) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == ids || ids.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "StationIds is null",
                    HttpStatus.BAD_REQUEST);
        }
        projectStateService.batchDeleteStationProject(ids, userId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation("机房工程状态一览")
    @GetMapping(value = "/houseprojectdetails")
    public ResponseEntity<ResponseResult> getHouseProjectDetail() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(projectStateService.queryHouseProjectDetails(userId));
    }

    @ApiOperation(value = "设置机房工程状态")
    @PostMapping(value = "/houseprojectstatus")
    public ResponseEntity<ResponseResult> setHouseProjectStatus(@RequestBody HouseProjectVO houseProjectVO) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(projectStateService.setHouseProjectState(houseProjectVO,userId));
    }

    @ApiOperation(value = "删除机房工程状态")
    @DeleteMapping(value = "/houseprojectstatus/{houseId}")
    public ResponseEntity<ResponseResult> deleteHouseProjectStatus(@PathVariable String  houseId) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(projectStateService.deleteHouseProjectState(houseId,userId));
    }
    @ApiOperation(value = "获取机房工程状态")
    @GetMapping(value = "/houseprojectstatus/{houseId}")
    public ResponseEntity<ResponseResult> getHouseProjectStatus(@PathVariable String  houseId) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(projectStateService.getHouseProjectStateById(houseId));
    }


    @ApiOperation(value = "批量删除机房工程状态")
    @PostMapping(value = "/houseprojectstatus/batchdelete",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteHouseProjects(@Valid @RequestBody List<String> ids) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == ids || ids.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "HouseIds is null",
                    HttpStatus.BAD_REQUEST);
        }
        projectStateService.batchDeleteHouseProject(ids, userId);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
