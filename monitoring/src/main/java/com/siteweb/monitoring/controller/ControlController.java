package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.service.ControlService;
import com.siteweb.monitoring.vo.ActiveControlOperationVO;
import com.siteweb.monitoring.vo.BatchRequestByBaseTypeId;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * tblControl info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:05
 */
@RestController
@RequestMapping("/api")
@Api(value = "ControlController", tags = {"Control操作接口"})
public class ControlController {

    @Autowired
    ControlService controlService;

    /**
     * GET  /cfgcontrols?equipmentId=1  get the List<ConfigControlItem> by equipmentId.
     *
     * @param equipmentId the equipmentId
     * @return the ResponseEntity with status 200 (OK) and with body the Equipment, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据equipmentId获取控制命令")
    @GetMapping(value = "/cfgcontrols", params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getConfigControlsByEquipmentId(@RequestParam("equipmentId") Integer equipmentId) {
        List<ConfigControlItem> configControlItems = controlService.findControlItemsByEquipmentId(equipmentId);
        return Optional.ofNullable(configControlItems)
                .map(result -> ResponseHelper.successful(configControlItems, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }
    @ApiOperation(value = "根据EquipmentId查找控制配置，只返回少量字段")
    @GetMapping(value = "/simplecontroldtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getSimpleControlDTOS(@RequestParam(name = "equipmentId") int equipmentId) {
        return ResponseHelper.successful(controlService.findSimpleControlDTOsByEquipmentId(equipmentId));
    }

    @ApiOperation(value = "根据EquipmentId,BaseTypeId查找控制配置，只返回少量字段")
    @GetMapping(value = "/simplecontroldtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId","baseTypeId"})
    public ResponseEntity<ResponseResult> findSimpleControlDTOsByEquipmentIdAndBaseTypeId(@RequestParam(name = "equipmentId") int equipmentId,@RequestParam(name = "baseTypeId") long baseTypeId) {
        return ResponseHelper.successful(controlService.findSimpleControlDTOsByEquipmentIdAndBaseTypeId(equipmentId,baseTypeId));
    }

    @ApiOperation(value = "根据EquipmentId列表,BaseTypeId查找控制配置，只返回少量字段")
    @PostMapping(value = "/simplecontroldtos",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSimpleControlDTOsByEquipmentIdsAndBaseTypeId(@Valid @RequestBody BatchRequestByBaseTypeId batchRequestByBaseTypeId) {
        return ResponseHelper.successful(controlService.findSimpleControlDTOsByEquipmentIdsAndBaseTypeId(batchRequestByBaseTypeId.getEquipmentIds(),batchRequestByBaseTypeId.getBaseTypeId()));
    }

    @ApiOperation(value = "根据EquipmentId,ControlId，只返回少量字段")
    @GetMapping(value = "/simplecontroldtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId","controlId"})
    public ResponseEntity<ResponseResult> findSimpleControlDTOsByEquipmentIdAndBaseTypeId(@RequestParam(name = "equipmentId") int equipmentId,@RequestParam(name = "controlId") int controlId) {
        return ResponseHelper.successful(controlService.findSimpleControlDTOsByEquipmentIdAndControlId(equipmentId,controlId));
    }
}
