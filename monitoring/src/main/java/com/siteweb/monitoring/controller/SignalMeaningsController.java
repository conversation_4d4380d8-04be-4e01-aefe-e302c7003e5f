package com.siteweb.monitoring.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * tblSignalmeanings info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:38:34
 */
@RestController
@RequestMapping("/api")
@Api(value="SignalMeaningsController",tags={"SignalMeanings操作接口"})
public class SignalMeaningsController {


}
