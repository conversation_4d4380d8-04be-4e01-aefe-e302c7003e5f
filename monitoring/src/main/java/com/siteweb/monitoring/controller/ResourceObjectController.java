package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value="ResourceObjectController",tags={"资源对象查询接口"})
@RequiredArgsConstructor
public class ResourceObjectController {
    @Autowired
    ResourceObjectManager resourceObjectManager;

    /**
     * GET  /resourceobjects?resourceType=1  get the List<ResourceObject> by resourceType.
     *
     * @param resourceType the resourceType
     * @return the ResponseEntity with status 200 (OK) and with body the ResourceObject, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据资源类型获取资源")
    @GetMapping(value = "/resourceobjects", params = {"resourceType"})
    public ResponseEntity<ResponseResult> getEquipmentId(@RequestParam("resourceType") Integer resourceType) {
        List<ResourceObjectEntity> resources = resourceObjectManager.findEntityByObjectType(resourceType);
        return Optional.ofNullable(resources)
                .map(result -> ResponseHelper.successful(resources, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation("获取所有资源")
    @GetMapping(value = "/resourceall",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceAll(){
        return ResponseHelper.successful(resourceObjectManager.findAllResource());
    }

    @ApiOperation("获取所有资源,有权限")
    @GetMapping(value = "/resourceall",
            produces = MediaType.APPLICATION_JSON_VALUE, params = {"id"})
    public ResponseEntity<ResponseResult> getResourceAllByUserId(Integer id) {
        return ResponseHelper.successful(resourceObjectManager.findAllResourceByUserId(id));
    }
}
