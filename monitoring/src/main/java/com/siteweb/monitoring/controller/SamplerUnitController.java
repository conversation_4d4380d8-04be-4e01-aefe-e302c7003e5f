package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.SamplerUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * tslSamplerunit info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:42:36
 */
@RestController
@RequestMapping("/api")
@Api(value = "SamplerUnitController", tags = {"SamplerUnit操作接口"})
public class SamplerUnitController {
    @Autowired
    SamplerUnitService samplerUnitService;

    @ApiOperation("通过监控单元id和端口id查询采集单元信息")
    @GetMapping(value = "/samplerunit", params = {"monitUnitId", "portId"})
    public ResponseEntity<ResponseResult> getSamplerUnit(Integer monitUnitId, Integer portId) {
        return ResponseHelper.successful(samplerUnitService.findByMonitUnitIdAndPortId(monitUnitId, portId));
    }
}
