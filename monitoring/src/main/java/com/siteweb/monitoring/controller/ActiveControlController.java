package com.siteweb.monitoring.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ActiveControlDTO;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.vo.ActiveControlOperationVO;
import com.siteweb.monitoring.vo.ControlCommandVO;
import com.siteweb.monitoring.vo.SendControlOperationDetailsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 活动告警表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:40:38
 */
@RestController
@RequestMapping("/api")
@Api(value="ActiveControlController",tags={"ActiveControl操作接口"})
public class ActiveControlController {


    @Autowired
    private ActiveControlService activeControlService;

    private static final String USER_ID_IS_NULL = "userid is null";
    /**
     *  执行控制命令
     * @param controlCommandVO 控制命令实体
     * @return 执行结果
     */
    @ApiOperation(value="发送控制命令")
    @PostMapping(value = "/activecontrols/send")
    public ResponseEntity<ResponseResult> createActiveControl(@Valid @RequestBody ControlCommandVO controlCommandVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        ControlResultType result = activeControlService.sendControlCommand(controlCommandVO,userId);
        if(result.equals(ControlResultType.SUCCESS))
            return ResponseHelper.successful(result.toString());
        else{
            return ResponseHelper.failed(String.valueOf(result.value()),
                    result.toString(),
                    HttpStatus.OK);
        }
    }

    /**
     * 确认活动控制命令
     *
     * @param activeControlOperationVO ActiveControlOperationVO
     * @return
     */
    @ApiOperation(value = "确认活动控制命令")
    @PostMapping(value = "/activecontrols/confirm",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> confirmActiveControls(@Valid @RequestBody ActiveControlOperationVO activeControlOperationVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId || ObjectUtil.isNull(activeControlOperationVO)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        boolean result = activeControlService.confirmControlCommand(userId, activeControlOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.SEND_CONTROL_FAIL.value()),
                "confirm active control error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }
    /**
     * 重发活动控制命令
     *
     * @param activeControlOperationVO ActiveControlOperationVO
     * @return
     */
    @ApiOperation(value = "重发活动控制命令")
    @PostMapping(value = "/activecontrols/resend",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> resendActiveControl(@Valid @RequestBody ActiveControlOperationVO activeControlOperationVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        boolean result = activeControlService.reSendControlCommand(userId, activeControlOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.SEND_CONTROL_FAIL.value()),
                "resend active control error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * GET  /activecontrols?equipmentId=1  get the List<ActiveControl> by equipmentId.
     *
     * @param equipmentId the equipmentId
     * @return the ResponseEntity with status 200 (OK) and with body the Equipment, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据equipmentId获取活动控制命令")
    @GetMapping(value = "/activecontrols", params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getActiveControlByEquipmentId(@RequestParam("equipmentId") Integer equipmentId) {
        List<ActiveControlDTO>  activeControls = activeControlService.getActiveControlByEquipmentId(equipmentId);
        return ResponseHelper.successful(activeControls, HttpStatus.OK);
    }

    @ApiOperation(value = "记录控制命令操作详情表")
    @PostMapping("/operationdetails/sendcontrol")
    public ResponseEntity<ResponseResult> createSendControlOperationDetail(@RequestBody SendControlOperationDetailsDTO sendControlOperationDetailsDTO) {
        return ResponseHelper.successful(activeControlService.sendControlOperationDetail(sendControlOperationDetailsDTO));
    }
}
