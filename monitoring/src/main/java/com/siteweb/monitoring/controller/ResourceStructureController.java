package com.siteweb.monitoring.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.service.ResourceObjectService;
import com.siteweb.monitoring.service.ResourceStructureCategoryService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.vo.ResourceStructureVO;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * resourceStructure info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:38
 */
@RestController
@RequestMapping("/api")
@Api(value = "ResourceStructureController", tags = {"ResourceStructure操作接口"})
public class ResourceStructureController {

    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    ResourceStructureCategoryService resourceStructureCategoryService;

    /**
     * GET  /resourcestructures : get the ResourceStructures.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of ResourceStructures in body
     */
    @ApiOperation(value = "获取所有ResourceStructure实体")
    @GetMapping("/resourcestructures")
    public ResponseEntity<ResponseResult> getResourceStructures() {
        return ResponseHelper.successful(resourceStructureService.findResourceStructures(), HttpStatus.OK);
    }

    /**
     * GET  /resourcestructures/:id  get the ResourceStructure by id.
     *
     * @param resourceStructureId the ResourceStructureId
     * @return the ResponseEntity with status 200 (OK) and with body the ResourceStructure, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个ResourceStructure实体")
    @GetMapping("/resourcestructures/{resourceStructureId}")
    public ResponseEntity<ResponseResult> getResourceStructureById(@PathVariable("resourceStructureId") @ApiParam(name = "resourceStructureId", value = "唯一ID", required = true) Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureService.getResourceStructureById(resourceStructureId);
        return ResponseHelper.successful(resourceStructure, HttpStatus.OK);
    }

    /**
     * Post /resourcestructures : create a new ResourceStructure
     *
     * @param resourceStructureVO the ResourceStructure to create
     * @return the ResponseEntity with status 201 (Created) and with body the new ResourceStructure,
     * or with status 400 (Bad Request) if the ResourceStructure has already an ID
     */
    @ApiOperation(value = "新增ResourceStructure实体")
    @PostMapping(value = "/resourcestructures")
    public ResponseEntity<ResponseResult> createResourceStructure(@Valid @RequestBody ResourceStructureVO resourceStructureVO) {
        if (resourceStructureVO.getResourceStructureId() != null) {
            return ResponseHelper.failed("-1", "A new ResourceStructure  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }

        resourceStructureService.createResourceStructure(resourceStructureVO.build());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * PUT  /resourcestructures : Updates an existing ResourceStructure.
     *
     * @param resourceStructureVO the ResourceStructure to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated ResourceStructure,
     * or with status 404 (Not Found) if the resourceStructureId is not exists,
     */
    @ApiOperation(value = "更新ResourceStructure实体")
    @PutMapping(value = "/resourcestructures")
    public ResponseEntity<ResponseResult> updateResourceStructure(@Valid @RequestBody ResourceStructureVO resourceStructureVO) {

        if (resourceStructureVO.getResourceStructureId() == null) {
            return ResponseHelper.failed("-1", "ResourceStructure   Not Found.", HttpStatus.NOT_FOUND);
        }
        resourceStructureService.updateResourceStructure(resourceStructureVO.build());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * DELETE  /resourcestructures/:id : delete the ResourceStructure by id.
     *
     * @param id the id of the ResourceStructure to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据唯一ID删除ResourceStructure实体")
    @DeleteMapping(value = "/resourcestructures/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteResourceStructure(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {

        ResourceStructure resourceStructure = resourceStructureService.findById(id);
        if (resourceStructure == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        resourceStructureService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "获取完整的结构树包含设备层级")
    @GetMapping("/resourcestructures/equipment")
    public ResponseEntity<ResponseResult> getResourceStructureEquipmentDTOTree() {
        ResourceStructureEquipmentTreeDTO resourceStructure = resourceStructureService.getResourceStructureEquipmentDTOTree();
        return ResponseHelper.successful(resourceStructure, HttpStatus.OK);
    }

    @ApiOperation(value = "根据parentResourceStructureId获取层级结构树，不包含设备")
    @GetMapping("/resourcestructuredtos")
    public ResponseEntity<ResponseResult> getResourceStructureDTOTree(@RequestParam(value = "parentResourceStructureId", required = false) @ApiParam(name = "parentResourceStructureId") Integer parentResourceStructureId) {
        if (null == parentResourceStructureId) {
            parentResourceStructureId = 0;
        }
        ResourceStructureDTO resourceStructureDTO = resourceStructureService.getResourceStructureDTOByParentId(parentResourceStructureId, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(resourceStructureDTO, HttpStatus.OK);
    }

    @ApiOperation(value = "根据objectTypeId获取结构列表")
    @GetMapping(value = "/resourcestructures", params = {"objectTypeId"})
    public ResponseEntity<ResponseResult> getResourceStructureByObjectTypeIdAndUserId(@RequestParam("objectTypeId") Integer objectTypeId) {
        List<ResourceStructure> resourceStructure = resourceStructureService.findByObjectTypeIdAndUserId(List.of(objectTypeId), TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(resourceStructure, HttpStatus.OK);
    }

    @ApiOperation(value = "根据objectTypeIds获取结构列表")
    @GetMapping(value = "/resourcestructures", params = {"objectTypeIds"})
    public ResponseEntity<ResponseResult> getResourceStructureByObjectTypeIdsAndUserId(@RequestParam("objectTypeIds") String objectTypeIds) {
        List<Integer> list = CharSequenceUtil.split(objectTypeIds, ",")
                .stream()
                .map(Integer::parseInt)
                .toList();
        List<ResourceStructure> resourceStructure = resourceStructureService.findByObjectTypeIdAndUserId(list, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(resourceStructure, HttpStatus.OK);
    }

    /**
     * 根据当前用户权限层级树
     *
     * @return
     */
    @ApiOperation(value = "根据当前用户权限层级树")
    @GetMapping(value = "/resourcestructuresbyuser")
    public ResponseEntity<ResponseResult> findResourcestructuresByUser() {
        ResourceStructureTreeDTO resourceStructureTreeDTO = resourceStructureService.findResourceStructureTreeByUserId(TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(resourceStructureTreeDTO, HttpStatus.OK);
    }

    @ApiOperation(value = "根据层级类型获取当前用户权限层级树")
    @GetMapping(value = "/resourcestructuresbycustomtype")
    public ResponseEntity<ResponseResult> findResourceStructuresByCustomtype(@RequestParam String typeIds) {
        ResourceStructureTreeDTO resourceStructureTreeDTO = resourceStructureService.findResourceStructuresByCustomtype(TokenUserUtil.getLoginUserId(), typeIds);
        return ResponseHelper.successful(resourceStructureTreeDTO, HttpStatus.OK);
    }


    @ApiOperation(value = "根据当前用户参数Id权限层级树")
    @GetMapping(value = "/resourcestructuresbyuser/{id}")
    public ResponseEntity<ResponseResult> findResourcestructuresByUser(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {
        ResourceStructureTreeDTO resourceStructureTreeDTO = resourceStructureService.findResourceStructureTreeByUserId(id);
        return ResponseHelper.successful(resourceStructureTreeDTO, HttpStatus.OK);
    }


    @ApiOperation(value = "获取完整的结构树包含设备层级(带告警、带权限、排序)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "existAlarm", value = "是否需要告警 默认为true"),
    })
    @GetMapping(value = "/parkalarmoverview", params = {"existAlarm"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getParkAlarmPositionOverviewInfo(@RequestParam(value = "existAlarm", required = false, defaultValue = "true") Boolean existAlarm) {
        ResourceStructureEquipmentTreeDTO resourceStructure = resourceStructureService.getParkAlarmPositionOverviewInfo(TokenUserUtil.getLoginUserId(), existAlarm);
        return ResponseHelper.successful(resourceStructure, HttpStatus.OK);
    }

    @GetMapping(value = "/resourcestructurelists", params = {"rootResourceStructureId", "equipmentCategory"})
    public ResponseEntity<ResponseResult> getResourceStructureByObjectTypeId(@RequestParam("rootResourceStructureId") Integer rootResourceStructureId, @RequestParam("equipmentCategory") Integer equipmentCategory) {
        return ResponseHelper.successful(resourceStructureService.findSubResourceStructureByEquipmentCategory(rootResourceStructureId, equipmentCategory));
    }

    @GetMapping(value = "/resourcestructurelists", params = {"parentId"})
    public ResponseEntity<ResponseResult> getResourceStructureByParentId(@RequestParam("parentId") String parentIds) {
        return ResponseHelper.successful(resourceStructureService.findResourceStructureByParentId(TokenUserUtil.getLoginUserId(), parentIds));
    }

    @ApiOperation(value = "获取用户拥有权限的层级集合(包括父层级)")
    @GetMapping(value = "/permissionrss")
    public ResponseEntity<ResponseResult> getResourceStructuresHasPermission(@RequestParam Integer userId) {
        List<ResourceObjectEntity> resourceStructure = ((ResourceObjectService)resourceStructureService).findAllResourceObjectByUserId(userId);
        return ResponseHelper.successful(resourceStructure);
    }

    @ApiOperation("通过用户id与层级id获取其拥有权限的层级与子级")
    @GetMapping(value = "/resourcestructure/per",params = "parentResourceStructure")
    public ResponseEntity<ResponseResult> getResourceStructuresIds(Integer parentResourceStructure){
        return ResponseHelper.successful(resourceStructureService.findResourceByUserIdAndResourceStructureIds(TokenUserUtil.getLoginUserId(), List.of(parentResourceStructure)));
    }

    @ApiOperation(value = "批量更新层级的排序")
    @PutMapping("/resourcestructure/batch/order")
    public ResponseEntity<ResponseResult> updateBatchResourceStructureOrder(@RequestBody List<UpdateResourceStructureOrderDto> updateResourceStructureOrderDtos){
        return ResponseHelper.successful(resourceStructureService.updateBatchResourceStructureOrder(updateResourceStructureOrderDtos));
    }
    @ApiOperation(value = "获取完整的结构树包含设备层级(带告警、带权限、排序、设备大类小类)")
    @GetMapping("/parkalarmoverviewcategory")
    public ResponseEntity<ResponseResult> getTree(@RequestParam(value = "existAlarm", required = false, defaultValue = "true") Boolean existAlarm){
        return ResponseHelper.successful(resourceStructureCategoryService.getParkAlarmPositionOverviewInfo(TokenUserUtil.getLoginUserId(),existAlarm));
    }
}
