package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.EquipmentMaintainDTO;
import com.siteweb.monitoring.service.EquipmentMaintainService;
import com.siteweb.monitoring.vo.BatchEquipmentProjectVO;
import com.siteweb.monitoring.vo.EquipmentProjectVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
public class EquipmentMaintainController {
    @Autowired
    EquipmentMaintainService equipmentMaintainService;

    @ApiOperation("通过层级id获取设备工程状态")
    @GetMapping(value = "/equipmentprojectstatus",params = "resourceStructureId")
    public ResponseEntity<ResponseResult> getEquipmentProjectByResourceStructure(Integer resourceStructureId){
        List<EquipmentMaintainDTO> result =  equipmentMaintainService.findMaintainStateByResourceStructure(resourceStructureId);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "批量设置设备工程状态")
    @PostMapping(value = "/equipmentprojectstatus/batchset")
    public ResponseEntity<ResponseResult> setBatchEquipmentProjectStatus(@RequestBody BatchEquipmentProjectVO batchEquipmentProjectVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(equipmentMaintainService.setBatchEquipmentProjectStatus(batchEquipmentProjectVO, userId));
    }

    @ApiOperation(value = "设置设备工程状态")
    @PostMapping(value = "/equipmentprojectstatus")
    public ResponseEntity<ResponseResult> setEquipmentProjectStatus(@RequestBody EquipmentProjectVO equipmentProjectVO) {
        Integer userId = TokenUserUtil.getLoginUserId();

        return ResponseHelper.successful(equipmentMaintainService.setEquipmentProject(equipmentProjectVO,userId));
    }

    @ApiOperation(value = "删除设备工程状态")
    @DeleteMapping(value = "/equipmentprojectstatus/{equipmentId}")
    public ResponseEntity<ResponseResult> deleteEquipmentProjectStatus(@PathVariable Integer equipmentId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(equipmentMaintainService.deleteEquipmentProject(equipmentId,userId));
    }

    @ApiOperation(value = "获取设备工程状态")
    @GetMapping(value = "/equipmentprojectstatus/{equipmentId}")
    public ResponseEntity<ResponseResult> getEquipmentProjectStatus(@PathVariable Integer equipmentId) {
        return ResponseHelper.successful(equipmentMaintainService.getEquipmentProjectById(equipmentId));
    }

    @ApiOperation(value = "批量删除设备工程状态")
    @PostMapping(value = "/equipmentprojectstatus/batchdelete",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteEquipmentProjects(@Valid @RequestBody List<Integer> equipmentIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == equipmentIds || equipmentIds.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EquipmentIds is null",
                    HttpStatus.BAD_REQUEST);
        }
        equipmentMaintainService.batchDeleteEquipmentProject(equipmentIds, userId);
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @ApiOperation("设备工程状态一览")
    @GetMapping(value = "/equipmentrojectdetails")
    public ResponseEntity<ResponseResult> getEquipmentProjectDetail() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(equipmentMaintainService.queryEquipmentProjectDetails(userId));
    }
}
