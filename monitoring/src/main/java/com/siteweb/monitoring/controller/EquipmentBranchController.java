package com.siteweb.monitoring.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.entity.EquipmentBranch;
import com.siteweb.monitoring.service.EquipmentBranchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "EquipmentBranchController", tags = {"设备支路操作接口"})
public class EquipmentBranchController {
    @Autowired
    private EquipmentBranchService equipmentBranchService;

    @ApiOperation("获取所有设备支路")
    @GetMapping("/equipmentbranch")
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(equipmentBranchService.findAll());
    }

    @ApiOperation("通过设备id获取设备支路")
    @GetMapping(value = "/equipmentbranch",params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getByEquipmentId(Integer equipmentId) {
        return ResponseHelper.successful(equipmentBranchService.findByEquipmentId(equipmentId));
    }

    @ApiOperation("获取设备支路通过id")
    @GetMapping("/equipmentbranch/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("id") Integer id) {
        EquipmentBranch equipmentBranch = equipmentBranchService.findById(id);
        if (ObjectUtil.isNull(equipmentBranch)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),"id not exist",HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(equipmentBranchService.findAll());
    }

    @ApiOperation("添加设备支路")
    @PostMapping("/equipmentbranch")
    public ResponseEntity<ResponseResult> createEquipmentBranch(@RequestBody EquipmentBranch equipmentBranch) {
        if (ObjectUtil.isNull(equipmentBranch.getBranchId()) || ObjectUtil.isNull(equipmentBranch.getEquipmentId())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "branchId and equipmentId not empty", HttpStatus.BAD_REQUEST);
        }
        if (ObjectUtil.isNotNull(equipmentBranchService.findByEquipmentIdAndBranchId(equipmentBranch.getEquipmentId(), equipmentBranch.getBranchId()))) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "branchId and equipmentId exists", HttpStatus.BAD_REQUEST);
        }
        Integer result = equipmentBranchService.createBranch(equipmentBranch);
        if (result > 0) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()), ErrorCode.CREATE_OBJECT_ERROR.getReasonPhrase(), HttpStatus.BAD_REQUEST);
    }

    @ApiOperation("批量导入设备支路")
    @PostMapping("/equipmentbranch/import")
    public ResponseEntity<ResponseResult> batchCreateEquipmentBranch(@RequestBody List<EquipmentBranch> importEquipmentBranchList) {
        return ResponseHelper.successful(equipmentBranchService.batchCreateBranch(importEquipmentBranchList));
    }
    @ApiOperation("删除设备支路")
    @DeleteMapping("/equipmentbranch/{id}")
    public ResponseEntity<ResponseResult> deleteEquipmentBranch(@PathVariable("id") Integer id) {
        EquipmentBranch equipmentBranch = equipmentBranchService.findById(id);
        if (ObjectUtil.isNull(equipmentBranch)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "id not exists", HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(equipmentBranchService.deleteById(id));
    }

    @ApiOperation("更新设备支路")
    @PutMapping("/equipmentbranch")
    public ResponseEntity<ResponseResult> updateEquipmentBranch(@RequestBody EquipmentBranch equipmentBranch) {
        if (ObjectUtil.isNull(equipmentBranch.getBranchId()) || ObjectUtil.isNull(equipmentBranch.getEquipmentId())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "branchId and equipmentId not empty", HttpStatus.BAD_REQUEST);
        }
        if (ObjectUtil.isNull(equipmentBranchService.findById(equipmentBranch.getId()))) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "id not exists", HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(equipmentBranchService.updateEquipmentBranch(equipmentBranch));
    }
}
