package com.siteweb.monitoring.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Equipmenttemplate info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:39:37
 */
@RestController
@RequestMapping("/api")
@Api(value="EquipmentTemplateController",tags={"EquipmentTemplate操作接口"})
public class EquipmentTemplateController {

}
