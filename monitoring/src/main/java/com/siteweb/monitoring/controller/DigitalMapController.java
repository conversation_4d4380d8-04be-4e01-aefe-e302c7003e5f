package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.StationTreeNode;
import com.siteweb.monitoring.service.DigitalMapService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class DigitalMapController {

    @Autowired
    DigitalMapService digitalMapService;
    private static final String USER_ID_IS_NULL = "userid is null";


    /**
     *  根据类型获取局站树
     * @return
     */
    @ApiOperation(value = "根据局站类型获取局站数")
    @GetMapping(value = "/stationtrees",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationTreeForDigitalMap(String resourceStructureIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        StationTreeNode stationTreeNode = digitalMapService.getStationTreeNodes(userId, resourceStructureIds);
        return ResponseHelper.successful(stationTreeNode, HttpStatus.OK);
    }
}
