package com.siteweb.monitoring.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.FocusSignalDTO;
import com.siteweb.monitoring.service.FocusSignalService;
import com.siteweb.monitoring.vo.FocusSignalFilterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR> zhou
 * @description FocusSignalController
 * @createTime 2022-08-06 13:51:10
 */
@RestController
@RequestMapping("/api")
@Api(value = "FocusSignalController", tags = {"关注信号查询接口"})
@Slf4j
public class FocusSignalController {

    @Autowired
    FocusSignalService focusSignalService;

    @ApiOperation(value = "根据FocusSignalFilterVO分页查询FocusSignalDTO实体")
    @PostMapping("/focussignaldtos/pageable")
    public ResponseEntity<ResponseResult> getPageableFocusSignalsByFilterVO(Pageable pageable,@RequestBody FocusSignalFilterVO focusSignalFilterVO) {
        try {
            Page<FocusSignalDTO> focusSignalDTOS = focusSignalService.queryPageableFocusSignals(TokenUserUtil.getLoginUserId(), pageable, focusSignalFilterVO);
            return ResponseHelper.successful(focusSignalDTOS, HttpStatus.OK);
        } catch (BusinessException ex) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "reach max query counts",
                    HttpStatus.BAD_REQUEST);
        }
    }
    @ApiOperation(value = "根据FocusSignalFilterVO分页查询FocusSignalDTO实体")
    @PostMapping("/focussignaldtos/equipments")
    public ResponseEntity<ResponseResult> getEquipmentsByFilterVO(@RequestBody FocusSignalFilterVO focusSignalFilterVO) {
        return ResponseHelper.successful(focusSignalService.findEquipmentsByFilterVO(TokenUserUtil.getLoginUserId(), focusSignalFilterVO));
    }

    @ApiOperation(value = "根据FocusSignalFilterVO导出关注信号excel")
    @PostMapping("/focussignaldtos/export")
    public ResponseEntity<Resource> getFocusSignalsExcelByFilterVO(@RequestBody FocusSignalFilterVO focusSignalFilterVO) {
        try (ExcelWriter writer = focusSignalService.findFocusSignalExcelWriter(TokenUserUtil.getLoginUserId(),
                focusSignalFilterVO); ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            writer.flush(bos, true);
            Resource resource = new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
            String fileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
            return ResponseEntity.ok()
                                 .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ".xlsx")
                                 .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                                 .body(resource);
        } catch (Exception e) {
            log.error("focusSignal export xlsx err.[err:{}]", ExceptionUtil.stacktraceToString(e));
            return null;
        }
    }


    @ApiOperation(value = "根据FocusSignalFilterVO查询所有FocusSignalDTO实体")
    @GetMapping("/focussignaldtos")
    public ResponseEntity<ResponseResult> getFocusSignalsByFilterVO(FocusSignalFilterVO focusSignalFilterVO) {
        try {
            List<FocusSignalDTO> focusSignalDTOList = focusSignalService.queryFocusSignals(TokenUserUtil.getLoginUserId(), focusSignalFilterVO);
            return ResponseHelper.successful(focusSignalDTOList, HttpStatus.OK);
        } catch (BusinessException ex) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "reach max query counts",
                    HttpStatus.BAD_REQUEST);
        }
    }
}
