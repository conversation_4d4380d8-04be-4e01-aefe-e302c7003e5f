package com.siteweb.monitoring.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mapper.AlarmChangeMapper;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.model.ActiveControlRedisModel;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: lzy
 * @Date: 2022/6/2 15:36
 */
@RestController("reportTestController")
@Slf4j
public class TestController {

    @Autowired
    private InfluxDBManager influxDBManager;

    @Value("${spring.influx.database}")
    private String database;

    @Autowired
    private InfluxDB influxDB;
    @Autowired
    RedisUtil redisUtil;
    private static final String activeDataKey = "RealTimeSignal:";
    private static final String complexIndexKey = "ComplexIndex:";
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    AlarmChangeMapper alarmChangeMapper;
    @Autowired
    ThreadPoolExecutor threadPoolExecutor;

    int CURRENT_INDEX = 0;

    double[] TEMPERATURE_VALUES = {27.1, 37.1, 47.2, 58.3, 68.4, 78.5, 88.5, 98.6, 108.6, 118.7, 128.8, 138.9};
    @Autowired
    private EquipmentMapper equipmentMapper;

    //    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void mockLiBatterySignals() {
        int batteryGroupEquipmentId = 755000150;
        int[] voltageSignalIds = {510000031, 510000061, 510000091, 510000121, 510000151, 510000181, 510000211, 510000241, 510000271, 510000301, 510000331, 510000361, 510000391, 510000421, 510000451,
                510000481, 510000511, 510000541, 510000571, 510000601, 510000631, 510000661, 510000691, 510000721, 510000751, 510000781, 510000811, 510000841, 510000871, 510000901,
                510000931, 510000961, 510000991, 510001021, 510001051, 510001081, 510001111, 510001141, 510001171, 510001201, 510001231, 510001261, 510001291, 510001321, 510001351,
                510001381, 510001411, 510001441, 510001471, 510001501, 510001531, 510001561, 510001591, 510001621, 510001651, 510001681, 510001711, 510001741, 510001771, 510001801,
                510001831, 510001861, 510001891, 510001921, 510001951, 510001981, 510002011, 510002041, 510002071, 510002101, 510002131, 510002161, 510002191, 510002221, 510002251,
                510002281, 510002311, 510002341, 510002371, 510002401, 510002431, 510002461, 510002491, 510002521, 510002551, 510002581, 510002611, 510002641, 510002671, 510002701,
                510002731, 510002761, 510002791, 510002821, 510002851, 510002881, 510002911, 510002941, 510002971, 510003001, 510003031, 510003061, 510003091, 510003121, 510003151,
                510003181, 510003211, 510003241, 510003271, 510003301, 510003331, 510003361, 510003391, 510003421, 510003451, 510003481, 510003511, 510003541, 510003571, 510003601,
                510003631, 510003661, 510003691, 510003721, 510003751, 510003781, 510003811, 510003841, 510003871, 510003901, 510003931, 510003961, 510003991, 510004021, 510004051,
                510004081, 510004111, 510004141, 510004171, 510004201, 510004231, 510004261, 510004291, 510004321, 510004351, 510004381, 510004411, 510004441, 510004471, 510004501,
                510004531, 510004561, 510004591, 510004621, 510004651, 510004681, 510004711, 510004741, 510004771, 510004801, 510004831, 510004861, 510004891, 510004921, 510004951,
                510004981, 510005011, 510005041, 510005071, 510005101, 510005131, 510005161, 510005191, 510005221, 510005251, 510005281, 510005311, 510005341, 510005371, 510005401,
                510005431, 510005461, 510005491, 510005521, 510005551, 510005581, 510005611, 510005641, 510005671, 510005701, 510005731, 510005761, 510005791, 510005821, 510005851};
        int[] temperatureSignalIds = {510000041, 510000071, 510000101, 510000131,
                510000491, 510000521, 510000551, 510000581,
                510000941, 510000971, 510001001, 510001031,
                510001391, 510001421, 510001451, 510001481,
                510001841, 510001871, 510001901, 510001931,
                510002291, 510002321, 510002351, 510002381,
                510002741, 510002771, 510002801, 510002831,
                510003191, 510003221, 510003251, 510003281,
                510003641, 510003671, 510003701, 510003731,
                510004091, 510004121, 510004151, 510004181,
                510004541, 510004571, 510004601, 510004631,
                510004991, 510005021, 510005051, 510005081,
                510005441, 510005471, 510005501, 510005531};
        Date now = new Date();
        String valueStr = "";
        for (int i = 0; i < voltageSignalIds.length; i++) {
            valueStr = String.format("%d~%d~3.4~%s~0", batteryGroupEquipmentId, voltageSignalIds[i], DateUtil.formatDateTime(now));
            redisUtil.set("RealTimeSignal:" + batteryGroupEquipmentId + "." + voltageSignalIds[i], valueStr);
        }
        for (int i = 0; i < temperatureSignalIds.length; i++) {
            if (Integer.valueOf(510000041).equals(temperatureSignalIds[i])) {
                if (CURRENT_INDEX >= 12) {
                    CURRENT_INDEX = 0;
                }
                valueStr = String.format("%d~%d~%.1f~%s~0", batteryGroupEquipmentId, temperatureSignalIds[i], TEMPERATURE_VALUES[CURRENT_INDEX], DateUtil.formatDateTime(now));
                CURRENT_INDEX++;
            } else {
                valueStr = String.format("%d~%d~27.1~%s~0", batteryGroupEquipmentId, temperatureSignalIds[i], DateUtil.formatDateTime(now));
            }
            redisUtil.set("RealTimeSignal:" + batteryGroupEquipmentId + "." + temperatureSignalIds[i], valueStr);
        }
        //第1组电压
        redisUtil.set("RealTimeSignal:" + batteryGroupEquipmentId + ".510000011", String.format("%d~510000011~663~%s~0", batteryGroupEquipmentId, DateUtil.formatDateTime(now)));
    }

    /**
     * 历史数据v2
     * @param time 开始时间
     * @param addTime 每次增加时间
     * @param dateTimeType 时间类型
     * @param foreach 循环粗出
     * @param addRange 数值随机增长范围
     */
    @GetMapping("/api/buildSingalData")
    private void buildSingalData(String equipmentId, String stationId, String signalId, String time, Integer addTime, String dateTimeType, int foreach, Double addRange) throws InterruptedException {
        int numThreads = Runtime.getRuntime().availableProcessors();
        final int threadTaskNum = foreach <= numThreads ? foreach : foreach / numThreads; // 每个线程的执行次数
        CountDownLatch countDownLatch = new CountDownLatch(numThreads);

        for (int i = 0; i < numThreads; i++) {
            // 执行任务
            final int threadIndex = i;
            final int threadTaskIndex = i == numThreads -1 ? threadTaskNum + foreach % numThreads : threadTaskNum;

            threadPoolExecutor.submit(() -> {
                try {
                    int threadSortIndex = 0;
                    Date threadStartDateTime = DateUtil.parse(time, com.siteweb.common.util.DateUtil.DETAILPATTERN);
                    int threadAddTime = Double.valueOf(threadIndex * threadTaskIndex * addTime).intValue();
                    if ("s".equals(dateTimeType)) {
                        threadStartDateTime = com.siteweb.common.util.DateUtil.dateAddSeconds(threadStartDateTime, threadAddTime);
                    }else if ("m".equals(dateTimeType)) {
                        threadStartDateTime = com.siteweb.common.util.DateUtil.dateAddMinutes(threadStartDateTime, threadAddTime);
                    }else if ("h".equals(dateTimeType)) {
                        threadStartDateTime = com.siteweb.common.util.DateUtil.dateAddHours(threadStartDateTime, threadAddTime);
                    }else if ("d".equals(dateTimeType)) {
                        threadStartDateTime = com.siteweb.common.util.DateUtil.dateAddMonth(threadStartDateTime, threadAddTime);
                    }

                    BigDecimal threadbigDecimal = new BigDecimal(threadIndex * threadTaskIndex * addRange);
                    Calendar threadCalendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
                    for (int k = 0; k < threadTaskIndex; k++) {
                        HashMap<String, String> tags = new HashMap<>();
                        tags.put("BaseTypeId", k + "");
                        tags.put("DeviceId", equipmentId);
                        tags.put("SignalId", equipmentId + "." + signalId);
                        tags.put("StationId", stationId);

                        HashMap<String, Object> fields = new HashMap<>();
                        threadbigDecimal = threadbigDecimal.add(BigDecimal.valueOf(RandomUtil.randomDouble(addRange))).setScale(2, RoundingMode.HALF_UP);
                        fields.put("PointValue", threadbigDecimal);
                        fields.put("value", threadbigDecimal);
                        fields.put("sortIndex", String.valueOf(threadSortIndex++));
                        if (threadSortIndex >= 100) {
                            threadSortIndex = 0;
                        }

                        if (CharSequenceUtil.isNotEmpty(time)) {
                            threadCalendar.setTime(threadStartDateTime);
                        }

                        // 昨天
                        if ("s".equals(dateTimeType)) {
                            threadCalendar.add(Calendar.SECOND, addTime);
                        }else if ("m".equals(dateTimeType)) {
                            threadCalendar.add(Calendar.MINUTE, addTime);
                        }else if ("h".equals(dateTimeType)) {
                            threadCalendar.add(Calendar.HOUR, addTime);
                        }else if ("d".equals(dateTimeType)) {
                            threadCalendar.add(Calendar.DAY_OF_WEEK, addTime);
                        }
                        threadStartDateTime = threadCalendar.getTime();
                        influxDBManager.insertWithTime("historydatas", tags, fields, threadStartDateTime.getTime(), database);
                        // influxDB.write(database, "", builder.build());
                    }
                }catch (Exception e){
                    log.error("出错了,{}", ExceptionUtil.stacktraceToString(e));
                }
                finally {
                    countDownLatch.countDown();
                }

            });
        }
        countDownLatch.await();
        // int index = 0;
        // Date startDateTime = DateUtil.parse(time, com.siteweb.common.util.DateUtil.DETAILPATTERN);
        // Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        // BigDecimal bigDecimal = new BigDecimal(0);
        // BigDecimal maxNum = new BigDecimal(10000);
        // for (int i = 0; i < foreach; i++) {
        //     HashMap<String, String> tags = new HashMap<>();
        //     tags.put("BaseTypeId", i + "");
        //     tags.put("DeviceId", equipmentId);
        //     tags.put("SignalId", equipmentId + "." + signalId);
        //     tags.put("StationId", stationId);
        //
        //     HashMap<String, Object> fields = new HashMap<>();
        //     // if (bigDecimal.intValue() >= 200) {
        //     //     bigDecimal = new BigDecimal(0);
        //     // }
        //     bigDecimal = bigDecimal.add(BigDecimal.valueOf(RandomUtil.randomDouble(addRange))).setScale(2, RoundingMode.HALF_UP);
        //     if (bigDecimal.compareTo(maxNum) > -1) {
        //         bigDecimal = new BigDecimal(0);
        //     }
        //     fields.put("PointValue", bigDecimal);
        //     fields.put("value", bigDecimal);
        //     fields.put("sortIndex", String.valueOf(index++));
        //     if (index >= 100) {
        //         index = 0;
        //     }
        //     Point.Builder builder = Point.measurement("historydatas");
        //     builder.tag(tags);
        //     builder.fields(fields);
        //
        //     if (CharSequenceUtil.isNotEmpty(time)) {
        //         calendar.setTime(startDateTime);
        //     }
        //
        //     // 昨天
        //     if ("s".equals(dateTimeType)) {
        //         calendar.add(Calendar.SECOND, addTime);
        //     }else if ("m".equals(dateTimeType)) {
        //         calendar.add(Calendar.MINUTE, addTime);
        //     }else if ("h".equals(dateTimeType)) {
        //         calendar.add(Calendar.HOUR, addTime);
        //     }else if ("d".equals(dateTimeType)) {
        //         calendar.add(Calendar.DAY_OF_WEEK, addTime);
        //     }
        //     startDateTime = calendar.getTime();
        //
        //     builder.time(startDateTime.getTime(), TimeUnit.MILLISECONDS);
        //     influxDB.write(database, "", builder.build());
        // }
    }

    /**
     * 历史数据v1
     */
    @GetMapping("/api/buildDataPoint")
    private void buildDataPoint() {
        int index = 0;
        for (int i = 0; i < 5; i++) {
            HashMap<String, String> tags = new HashMap<>();
            tags.put("CoreSourceId", i + "");
            tags.put("CorePointId", "120000010");
            tags.put("StandardId", "612000013");
            tags.put("SampleTime", "12313213");

            HashMap<String, Object> fields = new HashMap<>();
            double value = BigDecimal.valueOf(RandomUtil.randomDouble(100)).setScale(2, RoundingMode.HALF_UP).doubleValue();
            fields.put("PointValue", value);
            fields.put("sortIndex", String.valueOf(index++));
            if (index >= 100) {
                index = 0;
            }
            Point.Builder builder = Point.measurement("HistoryDataPoint");
            builder.tag(tags);
            builder.fields(fields);

            // 昨天
            DateFormat dateFormat=new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar= Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY,-48);

            builder.time(calendar.getTime().getTime(), TimeUnit.MILLISECONDS);
            influxDB.write(database, "", builder.build());
        }
    }

    /**
     * 历史指标v2
     */
    @GetMapping("/api/buildCompleinedxData")
    private void buildCompleinedxData(Integer complexIndexId, String startTime, Integer addTime, String dateTimeType, int foreach, Double startValue)  {
        int index = 0;

        BigDecimal bigDecimal = new BigDecimal(startValue);
        Date startDateTime = DateUtil.parse(startTime, com.siteweb.common.util.DateUtil.DETAILPATTERN);

        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        for (int i = 0; i < foreach; i++) {
            HashMap<String, String> tags = new HashMap<>();
            tags.put("ComplexIndexId", String.valueOf(complexIndexId));
            tags.put("CalcTime", "");
            tags.put("Abnormal", "0");

            HashMap<String, Object> fields = new HashMap<>();

            if (bigDecimal.intValue() >= 200) {
                bigDecimal = new BigDecimal(0);
            }
            bigDecimal = bigDecimal.add(BigDecimal.valueOf(RandomUtil.randomDouble(16.7))).setScale(2, RoundingMode.HALF_UP);
            fields.put("IndexValue", bigDecimal);
            fields.put("Unit", "");
            fields.put("sortIndex", String.valueOf(index++));
            if (index >= 100) {
                index = 0;
            }


            Point.Builder builder = Point.measurement("HistoryComplexIndex");
            builder.tag(tags);
            builder.fields(fields);

            if (CharSequenceUtil.isNotEmpty(startTime)) {
                calendar.setTime(startDateTime);
            }

            // 昨天
            if ("s".equals(dateTimeType)) {
                calendar.add(Calendar.SECOND, addTime);
            }else if ("m".equals(dateTimeType)) {
                calendar.add(Calendar.MINUTE, addTime);
            }else if ("h".equals(dateTimeType)) {
                calendar.add(Calendar.HOUR, addTime);
            }else if ("d".equals(dateTimeType)) {
                calendar.add(Calendar.DAY_OF_WEEK, addTime);
            }
            startDateTime = calendar.getTime();
            builder.time(startDateTime.getTime(), TimeUnit.MILLISECONDS);

            influxDB.write(database, "", builder.build());
        }
    }
    /**
     * 历史指标v1
     */
    @GetMapping("/api/buildCompleinedxDatav1")
    private void buildCompleinedxDatav1(Integer complexIndexId, String startTime, Integer addTime, String dateTimeType, int foreach)  {
        int index = 0;

        BigDecimal bigDecimal = new BigDecimal(0);
        Date startDateTime = DateUtil.parse(startTime, com.siteweb.common.util.DateUtil.DETAILPATTERN);

        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        for (int i = 0; i < foreach; i++) {
            HashMap<String, String> tags = new HashMap<>();
            tags.put("ComplexIndexId", String.valueOf(complexIndexId));
            tags.put("CalcTime", "");

            HashMap<String, Object> fields = new HashMap<>();

            if (bigDecimal.intValue() >= 200) {
                bigDecimal = new BigDecimal(0);
            }
            bigDecimal = bigDecimal.add(BigDecimal.valueOf(RandomUtil.randomDouble(100))).setScale(2, RoundingMode.HALF_UP);
            fields.put("IndexValue", bigDecimal);
            fields.put("Unit", "");
            fields.put("sortIndex", String.valueOf(index++));
            // fields.put("Abnormal", "0");
            if (index >= 100) {
                index = 0;
            }


            Point.Builder builder = Point.measurement("HistoryComplexIndex");
            builder.tag(tags);
            builder.fields(fields);

            if (CharSequenceUtil.isNotEmpty(startTime)) {
                calendar.setTime(startDateTime);
            }

            // 昨天
            if ("s".equals(dateTimeType)) {
                calendar.add(Calendar.SECOND, addTime);
            }else if ("m".equals(dateTimeType)) {
                calendar.add(Calendar.MINUTE, addTime);
            }else if ("h".equals(dateTimeType)) {
                calendar.add(Calendar.HOUR, addTime);
            }else if ("d".equals(dateTimeType)) {
                calendar.add(Calendar.DAY_OF_WEEK, addTime);
            }
            startDateTime = calendar.getTime();
            builder.time(startDateTime.getTime(), TimeUnit.MILLISECONDS);

            influxDB.write(database, "", builder.build());
        }
    }

    /**
     * 实时数据
     * @param signalId
     * @param equipmentId
     * @param time
     */
    @GetMapping("/api/buildActiveSignalData")
    private void buildActiveSignalData(Integer signalId, Integer equipmentId, String time, Boolean decimal) {
        String data = Boolean.TRUE.equals(decimal) ? BigDecimal.valueOf(RandomUtil.randomDouble(300)).setScale(2, RoundingMode.HALF_UP).toString() : String.valueOf(RandomUtil.randomInt(300));
        String value = equipmentId + "~" + signalId + "~" + data + "~" + time + "~0";
        redisUtil.set(activeDataKey + equipmentId + "." + signalId, value);
    }

    /**
     * 实时指标
     * @param complexIndexId
     * @param time
     */
    @GetMapping("/api/buildActiveComplexIndex")
    private void buildActiveComplexIndex(Integer complexIndexId, String time, Boolean decimal) {

        // ReportTimeJobExecService bean = SpringUtil.getBean(ReportTimeJobExecService.class);
        // ReportTimingTaskFile exec = bean.exec(1, 3, "-1w", "-1h");
        // System.out.println(exec);

        String data = Boolean.TRUE.equals(decimal) ? BigDecimal.valueOf(RandomUtil.randomDouble(300)).setScale(2, RoundingMode.HALF_UP).toString() : String.valueOf(RandomUtil.randomInt(300));
        String value = complexIndexId + "~" + data + "~" + time + "~" + "20" + "~kWh";
        redisUtil.set(complexIndexKey + complexIndexId, value);
    }

    @PostMapping("/api/sendcontrolcommand")
    private void sendControlCommand(@RequestBody ActiveControlRedisModel redisModel) {
        redisTemplate.convertAndSend("/bacontrol", redisModel);
    }

    @GetMapping("/api/sendnotification")
    private void sendNotification() {
        LambdaQueryWrapper<AlarmChange> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(AlarmChange::getSerialNo);
        List<AlarmChange> alarmChanges = alarmChangeMapper.selectList(wrapper);
        AlarmChange alarmChange = alarmChanges.get(0);
        alarmChange.setSerialNo(null);
        alarmChangeMapper.insert(alarmChange);
    }

    @GetMapping("/api/equipmentonline/mock")
    public void equipmentOnlineTest(){
        List<Equipment> allEquipment = equipmentMapper.getAllEquipment();
        List<String> redisKeys = allEquipment.stream().map(e -> "EqtConState:" + e.getEquipmentId()).toList();
        redisKeys.forEach(redisKey -> redisUtil.set(redisKey, redisKey.split(":")[1] + "~" + 1));
        //return ids.stream()
        //          .map(id -> dataPrefix + id)
        //          .toList();
    }

    // @GetMapping("/api/processConvergenceSecondary")
    // public void test(){
    //     SpringBeanUtil.getBean(PowerEquipmentConnectionImpl.class).constructLevelOfPath();
    // }
    public static void main(String[] args) {
        int i = 9 % 8;
        System.out.println(i);
    }
}
