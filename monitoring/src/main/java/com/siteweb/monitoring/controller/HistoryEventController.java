package com.siteweb.monitoring.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.HistoryEventPageDTO;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.service.HistoryEventService;
import com.siteweb.monitoring.vo.HisPowOffCountReqByStationId;
import com.siteweb.monitoring.vo.HistoryEventFilterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 历史告警表
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:39:36
 */
@RestController
@RequestMapping("/api")
@Api(value = "HistoryEventController", tags = {"HistoryEvent操作接口"})
public class HistoryEventController {

    @Autowired
    HistoryEventService historyEventService;

    /**
     * GET  /historyevents : query history events.
     *
     * @param startDate       history event startTime startDate
     * @param endDate         history event startTime endDate
     * @param baseEquipmentId baseEquipmentId
     * @param equipmentId     equipmentId
     * @param eventId         eventId
     * @return the ResponseEntity with status 200 (OK) and the list of HistoryEvent in body
     */
    @ApiOperation(value = "根据条件查询HistoryEvent实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "查询成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "StartDate和EndDate不允许为null", content = @Content)
            })
    @GetMapping(value = "/historyevents",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> queryHistoryEvents(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
                                                             @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
                                                             @RequestParam(value = "baseEquipmentId", required = false) Integer baseEquipmentId,
                                                             @RequestParam(value = "equipmentId", required = false) Integer equipmentId,
                                                             @RequestParam(value = "eventId", required = false) Integer eventId) {
        List<HistoryEvent> result;
        if (startDate == null || endDate == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "StartDate and EndDate can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        if (baseEquipmentId != null) {
            result = historyEventService.findByBaseEquipmentIdAndStartTimeSpan(baseEquipmentId, startDate, endDate);
            return ResponseHelper.successful(result, HttpStatus.OK);
        }
        if (equipmentId != null) {
            result = historyEventService.findByEquipmentIdAndStartTimeSpan(equipmentId, startDate, endDate);
            return ResponseHelper.successful(result, HttpStatus.OK);
        }
        if (eventId != null) {
            result = historyEventService.findByEventIdAndStartTimeSpan(eventId, startDate, endDate);
            return ResponseHelper.successful(result, HttpStatus.OK);
        }
        result = historyEventService.findByStartTimeSpan(startDate, endDate);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }

    @GetMapping(value = "/historyeventdtos",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> queryHistoryEvents(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
                                                             @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
                                                             @RequestParam(value = "equipmentId", required = false) Integer equipmentId) {
        return ResponseHelper.successful(historyEventService.findHistoryEventDTOByEquipmentIdAndStartTimeSpan(equipmentId,startDate,endDate), HttpStatus.OK);
    }

    @ApiOperation(value = "按条件查询历史告警记录")
    @GetMapping(value = "/historyeventdtos", params = {"equipmentId","eventId","startTime","endTime"})
    public ResponseEntity<ResponseResult> findAlarmHistoryByConditions(Integer equipmentId, Integer eventId, Date startTime, Date endTime) {
        return ResponseHelper.successful(historyEventService.findAlarmHistoryByConditions(equipmentId, eventId, startTime, endTime));
    }

    @ApiOperation(value = "根据设备id与事件id统计指定时间范围告警时长")
    @GetMapping(value = "/alarmduration", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAlarmDurationByEquipmentIdAndEventId(Integer equipmentId, Integer eventId,Date startTime,Date endTime) {
        return ResponseHelper.successful(historyEventService.findAlarmDurationByEquipmentIdAndEventId(equipmentId, eventId, startTime, endTime));
    }

    @ApiOperation(value = "根据设备id与事件id统计指定时间范围告警次数")
    @GetMapping(value = "/alarmcount", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAlarmCountByEquipmentIdAndEventId(Integer equipmentId, Integer eventId,Date startTime,Date endTime) {
        return ResponseHelper.successful(historyEventService.findAlarmCountByEquipmentIdAndEventId(equipmentId, eventId, startTime, endTime));
    }

    @ApiOperation(value = "通过局站ids获取其停电次数")
    @PostMapping(value = "/historypoweroffcount", produces = MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<ResponseResult> getPowerOffCountByStationIds(@RequestBody HisPowOffCountReqByStationId reqVO) {
        return ResponseHelper.successful(historyEventService.getPowerOffCountByStationIds(reqVO));
    }

    @ApiOperation(value = "通过局站ids获取油机发电次数")
    @PostMapping(value = "/historyoilenginecount", produces = MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<ResponseResult> getOilEngineCountByStationIds(@RequestBody HisPowOffCountReqByStationId reqVO) {
        return ResponseHelper.successful(historyEventService.getOilEngineCountByStationIds(reqVO));
    }

    @ApiOperation(value = "分页查询历史告警")
    @GetMapping("/historyevents/page")
    public ResponseEntity<ResponseResult> getHistoryEventByFilterVO(Pageable pageable, HistoryEventFilterVO historyEventFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        IPage<HistoryEventPageDTO> historyEventDTOS = historyEventService.queryHistoryEventPage(userId, pageable, historyEventFilterVO);
        return ResponseHelper.successful(historyEventDTOS, HttpStatus.OK);
    }

    @ApiOperation(value = "查询历史告警统计")
    @GetMapping("/historyevents/groupby")
    public ResponseEntity<ResponseResult> groupHistoryEventBySeverity(HistoryEventFilterVO historyEventFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Map<Integer, Long> map = historyEventService.groupHistoryEventBySeverity(userId, historyEventFilterVO);
        return ResponseHelper.successful(map, HttpStatus.OK);
    }

}
