package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.MonitorUnitDTO;
import com.siteweb.monitoring.service.MonitorUnitService;
import com.siteweb.monitoring.vo.MonitorUnitRegisterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * MonitorUnit info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:42:36
 */
@RestController
@RequestMapping("/api")
@Api(value="MonitorUnitController",tags={"MonitorUnit操作接口"})
public class MonitorUnitController {
    @Autowired
    MonitorUnitService monitorUnitService;
    /**
     * GET  /tslmonitorunits : get the TslMonitorunits.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of TslMonitorunits in body
     */
    @GetMapping("/monitorunits" )
    public ResponseEntity<ResponseResult> getTslMonitorunits() {
        List<MonitorUnitDTO>  result = monitorUnitService.findMonitorUnitDTOs();
        return Optional.ofNullable(result)
                .map(res -> ResponseHelper.successful(result, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @PostMapping(value = "/monitorunitstatesbyids",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getAllMonitorUnitStatesByIds(@Valid @RequestBody List<Integer> ids) {
        List<MonitorUnitDTO>  result =  monitorUnitService.findMonitorUnitDTOsByIds(ids);
        return Optional.ofNullable(result)
                .map(res -> ResponseHelper.successful(result, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @GetMapping(value = "/monitorunit/register",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  registerMonitorUnit(MonitorUnitRegisterVO monitorUnitRegisterVO) {
        int result =  monitorUnitService.registerMonitorUnit(monitorUnitRegisterVO);
        return Optional.ofNullable(result)
                .map(res -> ResponseHelper.successful(result, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据设备id获取监控单元")
    @GetMapping(value = "/monitorunits", params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getMonitorUnitByEquipmentId(Integer equipmentId) {
        MonitorUnitDTO monitorUnitDTOS = monitorUnitService.getMonitorUnitByEquipmentId(equipmentId);
        return ResponseHelper.successful(monitorUnitDTOS);
    }
}
