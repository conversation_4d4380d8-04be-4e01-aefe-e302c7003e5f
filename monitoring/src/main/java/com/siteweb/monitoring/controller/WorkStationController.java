package com.siteweb.monitoring.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.service.WorkStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * WorkStation info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:38:34
 */
@RestController
@RequestMapping("/api")
@Api(value="WorkStationController",tags={"WorkStation操作接口"})
public class WorkStationController {
    @Autowired
    private WorkStationService workStationService;

    @ApiOperation("获取服务器的网络拓扑图")
    @GetMapping("/workstation/networktopology")
    public ResponseEntity<ResponseResult> workstation(){
        return ResponseHelper.successful(workStationService.networkTopology());
    }
}
