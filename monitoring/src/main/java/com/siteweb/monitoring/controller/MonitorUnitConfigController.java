package com.siteweb.monitoring.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * tslMonitorunitconfig info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:42:36
 */
@RestController
@RequestMapping("/api")
@Api(value="MonitorUnitConfigController",tags={"MonitorUnitConfig操作接口"})
public class MonitorUnitConfigController {

}
