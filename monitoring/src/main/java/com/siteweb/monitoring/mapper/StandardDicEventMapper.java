package com.siteweb.monitoring.mapper;

import com.siteweb.monitoring.dto.BaseAlarmDTO;
import com.siteweb.monitoring.dto.StandardAlarmNameDTO;
import com.siteweb.monitoring.entity.StandardDicEvent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> z<PERSON>
 * @description StandardDicEventMapper
 * @createTime 2022-08-10 13:05:02
 */
public interface StandardDicEventMapper {

    List<StandardDicEvent> getAllStandardDicEvent();
    StandardAlarmNameDTO findStandardAlarmName(@Param("stationId") Integer stationId, @Param("standardVer") Integer standardVer, @Param("baseTypeId") Long baseTypeId);
    List<BaseAlarmDTO> findBaseAlarmByStandardVerAndBaseEquipmentid(@Param("standardVer") Integer standardVer,@Param("baseEquipmentId")Integer baseEquipmentId);
    List<BaseAlarmDTO> findBaseAlarmByStandardVerAndBaseAlarmId(@Param("standardVer") Integer standardVer,@Param("baseTypeId")Integer baseTypeId);
}
