package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.WorkStationNetworkTopologyDTO;
import com.siteweb.monitoring.entity.WorkStation;

import java.util.List;

public interface WorkStationMapper extends BaseMapper<WorkStation> {

    Integer findWorkStationIdByType(int workStationType);
    void updateWorkStationConnectStateById(int connectState, int workStationId);

    List<WorkStationNetworkTopologyDTO> networkTopology();
}

