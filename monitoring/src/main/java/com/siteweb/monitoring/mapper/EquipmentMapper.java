package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.vo.ToDynamicApplyEquipmentVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface EquipmentMapper extends BaseMapper<Equipment> {
    List<EquipmentState> getEquipmentState();

    /**
     * 批量更新设备排序
     *
     * @param updateEquipmentOrderDtos 设备更新排序dto
     * @return {@link Integer}
     */
    Integer updateBatchEquipmentOrder(@Param("list") List<UpdateEquipmentOrderDto> updateEquipmentOrderDtos);

    /**
     * 获取设备资源
     *
     * @return {@link ResourceObjectEntity}
     */
    List<ResourceObjectEntity> getEquipmentResource();

    /**
     * 获取设备基础信息
     *
     * @param equipmentId 设备id
     * @return {@link EquipmentBasicDto}
     */
    EquipmentBasicDto findEquipmentBasic(@Param("equipmentId") Integer equipmentId);

    /**
     * 根据层级Id设备类型统计设备
     *
     * @param pageCategory
     * @param equipmentBaseType
     * @param resourceStructureId
     * @return
     */
    Integer getEquipmentStatisticsByResourceStructureAndEquipmentBaseType(Integer pageCategory, @Param("list") List<Integer> equipmentBaseType, Integer resourceStructureId);

    Integer getEquipmentStatisticsByReq(Integer pageCategory, Integer resourceStructureId);


    List<Equipment> getAllEquipment();

    List<Equipment> getEquipmentsInBatch(@Param("list") List<Integer> equipmentId);

    Equipment findByEquipmentId(@Param("equipmentId") Integer equipmentId);

    /**
     * 获取待批量应用信号动态配置的设备信息
     *
     * @param stationId   局站ID
     * @param equipmentId 设备ID
     * @return
     */
    List<ToDynamicApplyEquipmentVO> getToDynamicApplyEquipmentVOs(Integer stationId, Integer equipmentId);

    List<StationEquipmentDTO> findStationPageEquipmentDTOByStationId(Integer stationId);

    /**
     * 批量更新设备信息
     * 仅可更新:equipmentCategory、vendor、unit、equipmentStyle、equipmentModule、
     *         assetState、buyDate、usedDate、usedLimit、price、ratedCapacity、
     *         projectName、description字段
     * @param batchUpdateEquipmentList 设备列表
     * @return int
     */
    int batchUpdate(List<Equipment> batchUpdateEquipmentList);

    List<Equipment> findByEquipmentTemplateIds(List<Integer> equipmentTemplateIdList);

    List<SimpleEquipmentDTO> findEquipmentsBySignalBaseTypeIds(List<Integer> signalBaseTypeIdList, List<Integer> resourceStructureIdList);

    int updateByEquipmentBasicDto(Equipment equipment);

    List<Equipment> getAirConditionlist();

    void batchUpdateEquipmentUseDate(Double useLimit, Date useDate, List<Integer> selectedEquips);

    List<EquipmentLegderDTO> getEquipmentIdWithEquipmentStyleName(String styleName);

    List<EquipmentStateSignalPointDTO> getEquipmentStateWithSignalPoint(Collection<Integer> equipmentIds);
}

