package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EquipmentMaintainDTO;
import com.siteweb.monitoring.dto.EquipmentProjectDetail;
import com.siteweb.monitoring.entity.EquipmentMaintain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EquipmentMaintainMapper   extends BaseMapper<EquipmentMaintain> {
    List<EquipmentProjectDetail> getProjectEquipmentList(Integer equipmentState);

    List<EquipmentMaintainDTO> findMaintainStateByResourceStructure(@Param("resourceStructureId") Integer resourceStructureId);
}
