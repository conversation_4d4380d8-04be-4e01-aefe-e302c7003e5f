package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EquipmentSignalPointStatisticsDTO;
import com.siteweb.monitoring.dto.TemplateSignalPointDTO;
import com.siteweb.monitoring.entity.EquipmentTemplate;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface EquipmentTemplateMapper extends BaseMapper<EquipmentTemplate> {

    /**
     * 通过ids查找设备名称,key为主键id，value为模板名称
     * @param templateIds 设备ids
     * @return {@link Map}<{@link Integer}, {@link String}>
     */
    @MapKey("equipmentTemplateId")
    Map<Integer, EquipmentTemplate> findNameByIds(Collection<Integer> templateIds);
    List<EquipmentSignalPointStatisticsDTO> getSignalPoint();
    List<TemplateSignalPointDTO> getSignalPointDetail(@Param("list") Collection<Integer> templateIds);
}

