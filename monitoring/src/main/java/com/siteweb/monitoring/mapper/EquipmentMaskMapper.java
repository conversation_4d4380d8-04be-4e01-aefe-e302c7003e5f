package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.EquipmentMaskDTO;
import com.siteweb.monitoring.dto.EquipmentMaskFilterDTO;
import com.siteweb.monitoring.dto.SimpleEquipmentMaskDTO;
import com.siteweb.monitoring.entity.EquipmentMask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EquipmentMaskMapper extends BaseMapper<EquipmentMask> {

    List<SimpleEquipmentMaskDTO> findSimpleEquipmentMaskDTOsByResourceStructureId(Integer resourceStructureId);

    List<EquipmentMask> findEquipmentMasksByResourceStructureId(Integer resourceStructureId);

    EquipmentMaskDTO findEquipmentMaskDTOByEquipmentId(Integer equipmentId);

    void deleteEquipmentMask(int stationId, int equipmentId, int timeGroupId);
    List<Integer> findMaskEquipmentIds();

    Page<EquipmentMaskDTO> findEquipmentMaskByKeywordsPage(@Param("page") Page<EquipmentMaskDTO> page, @Param("equipmentMaskFilterDTO") EquipmentMaskFilterDTO equipmentMaskFilterDTO);

    long findEquipmentMaskByKeywordsPageCount(@Param("equipmentMaskFilterDTO") EquipmentMaskFilterDTO equipmentMaskFilterDTO);

    Page<SimpleEquipmentMaskDTO> findSimpleEquipmentMaskByEquipmentBaseTypesPage(@Param("page") Page<SimpleEquipmentMaskDTO> page, @Param("equipmentMaskFilterDTO") EquipmentMaskFilterDTO equipmentMaskFilterDTO);
    long findEquipmentMaskByEquipmentBaseTypesPageCount(@Param("equipmentMaskFilterDTO") EquipmentMaskFilterDTO equipmentMaskFilterDTO);

    List<EquipmentMask> findEquipmentMaskByEquipmentBaseTypes(@Param("equipmentMaskFilterDTO") EquipmentMaskFilterDTO equipmentMaskFilterDTO);

    List<EquipmentMask> findEquipmentMaskCreateByEquipmentBaseTypes(@Param("equipmentMaskFilterDTO") EquipmentMaskFilterDTO equipmentMaskFilterDTO);

    void batchUpdate(@Param("updateList") List<EquipmentMask> updateList);

    void batchInsert(@Param("insertList") List<EquipmentMask> insertList);
}

