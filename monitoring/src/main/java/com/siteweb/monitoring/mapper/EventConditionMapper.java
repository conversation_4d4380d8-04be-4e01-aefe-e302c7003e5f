package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EquipmentEventConditionDTO;
import com.siteweb.monitoring.dto.EvenConditionDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.entity.EventCondition;

import java.util.List;

public interface EventConditionMapper extends BaseMapper<EventCondition> {

    List<EventConditionDTO> findEventConditionDTOByEventId(int equipmentId, int eventId);

    List<EventConditionDTO> findEventConditionDTOBySignalId(int equipmentId, int signalId);

    int insertEventCondition(EventCondition eventCondition);

    int updateEventCondition(EventCondition eventCondition);

    int deleteEventConditionById(int equipmentTemplateId, int eventId, int eventConditionId);

    List<EquipmentEventConditionDTO> findEventConditionByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds);

    void batchInsert(List<EventCondition> eventConditionList);

    List<EvenConditionDTO> findEventConditionByEquipmentId(Integer equipmentId);

    EventCondition findByEquipmentIdAndEventIdAndEventConditionId(Integer equipmentId, Integer eventId, Integer eventConditionId);
}

