package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.siteweb.monitoring.dto.AlarmMaskLogDTO;
import com.siteweb.monitoring.dto.AlarmMaskLogReportParam;
import com.siteweb.monitoring.entity.AlarmMaskLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmMaskLogMapper
 * @createTime 2022-05-14 11:22:11
 */
public interface AlarmMaskLogMapper extends BaseMapper<AlarmMaskLog> {
    List<AlarmMaskLogDTO> findAlarmMaskLogDTO(@Param(Constants.WRAPPER) LambdaQueryWrapper<AlarmMaskLog> queryWrapper);

    List<AlarmMaskLogDTO> findAlarmMaskLog(@Param("param") AlarmMaskLogReportParam param);

    /**
     * 因为comment在达梦中是关键字，所以不得不手写sql
     * 插入实体
     * @param alarmMaskLog 告警屏蔽记录
     */
    void insertEntity(@Param("alarmMaskLog") AlarmMaskLog alarmMaskLog);

    void batchInsert(@Param("alarmMaskLogList") List<AlarmMaskLog> alarmMaskLogList);
}
