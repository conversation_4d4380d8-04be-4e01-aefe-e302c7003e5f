package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.SamplerUnitDTO;
import com.siteweb.monitoring.entity.SamplerUnit;

import java.util.Collection;
import java.util.List;

public interface SamplerUnitMapper extends BaseMapper<SamplerUnit> {
    SamplerUnit findSamplerUnit(Integer monitorUnitId, String samplerUnitName,String portName);

    List<SamplerUnitDTO> getSamplerUnitDTOByStationId(Integer stationId);

    List<SamplerUnit> findByEquipmentIds(Collection<Integer> equipmentIds);
}

