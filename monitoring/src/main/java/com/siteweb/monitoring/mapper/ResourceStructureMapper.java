package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.ResourceStructureAlarmState;
import com.siteweb.monitoring.dto.UpdateResourceStructureOrderDto;
import com.siteweb.monitoring.entity.ResourceStructure;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ResourceStructureMapper extends BaseMapper<ResourceStructure> {
      List<ResourceStructure>  findSubResourceStructureByEquipmentCategory(Integer rootId, Integer equipmentCategory);

      List<ResourceStructureAlarmState> getResourceStructureAlarmState( @Param("list")  List<Integer> resourceStructureId);

    int updateEntity(@Param("resourceStructure") ResourceStructure resourceStructure);

    void updateBatchOrder(List<UpdateResourceStructureOrderDto> updateResourceStructureOrderDtos);
}

