package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.entity.StandardDicSig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StandardDicSigMapper extends BaseMapper<StandardDicSig> {
    List<IdValueDTO<Long, String>> findTelecomStandardSig();

    List<IdValueDTO<Long, String>> findErmStandardSig();

    List<IdValueDTO<Long, String>> findMobileStandardSig();

    List<IdValueDTO<Long, String>> findUniComStandardSig();

    List<Signal> getTelecomSignalIdBySignalBaseEntryIds(@Param("entryIds") List<Integer> entryIds);

    List<Signal> getUniComSignalIdBySignalBaseEntryIds(@Param("entryIds") List<Integer> entryIds);

    List<Signal> getMobileSignalIdBySignalBaseEntryIds(@Param("entryIds") List<Integer> entryIds);

    List<Signal> getErmSignalIdBySignalBaseEntryIds(@Param("entryIds") List<Integer> entryIds);

}

