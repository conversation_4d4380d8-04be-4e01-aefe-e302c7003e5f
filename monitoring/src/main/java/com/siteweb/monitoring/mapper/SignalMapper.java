package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.model.ControlSignalRelation;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

public interface SignalMapper extends BaseMapper<Signal> {

    List<Signal> findSignalsByEquipmentId(Integer equipmentId);

    List<Signal> findSignalsByEquipmentTemplateId(Integer equipmentId);

    List<ControlSignalRelation> findSignalsAboutControlByEquipmentId(Integer equipmentId);

    List<String> findRealTimeSignalKeyByEquipmentId(Integer equipmentId);

    List<String> findRealTimeSignalKeyByBaseTypeId(Long baseTypeId);

    List<String> findRealTimeSignalKeyByEquipmentCategoryAndBaseTypeId(Integer equipmentBaseTypeId, List<Long> baseTypeId);

    List<ConfigSignalItem> findConfigSignalByEquipmentTemplateId(Integer equipmentTemplateId);

    List<ConfigSignalItem> findAllConfigSignals();

    List<SimpleSignalDTO> findSimpleSignalDTOsByEquipmentId(int equipmentId);

    ConfigSignalDTO findConfigSignalDTOBySignalId(int equipmentId, int signalId);

    String findMonitorUnitExpressionBySignalId(int equipmentId, int signalId);
    /**
     * 获取模板下最大的信号ids
     * @param templateId 设备模板id
     * @return {@link Integer}
     */
    Integer findMaxSignalIdByTemplateId(Integer templateId);

    List<Signal> findSignalsByTemplateIdAndSignalIds(Integer templateId, List<Integer> signalIds);

    void batchInsert(List<Signal> signalList);

    Signal findSignalIdByTemplateIdAndSignalName(Integer equipmentTemplateId, String virtualSignalName);

    Signal findSignalIdByTemplateIdAndChannel(Integer equipmentTemplateId, Integer channelNo);

    List<SimpleEventSignalDTO> findSignalEvent(Integer equipmentId);

    List<EquipmentSignalDto> findSignalsByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds);

    List<SimpleSignalDTO> findSimpleSignalDTOsByBaseTypeId(Integer baseTypeId);

    List<SimpleSignalDTO> findSimpleSignalDTOsByBaseTypeIds(Collection<Integer> baseTypeIdList);

    void updateEntity(@Param("signal") Signal signal);
    List<SimpleSignalDTO> findSimpleSignalDTOsByBaseTypeIds(Collection<Integer> baseTypeIdList, Collection<Integer> equipmentIds);
}

