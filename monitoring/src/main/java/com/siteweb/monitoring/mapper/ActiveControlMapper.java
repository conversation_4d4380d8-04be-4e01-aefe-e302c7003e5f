package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.ControlDTO;
import com.siteweb.monitoring.entity.ActiveControl;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface ActiveControlMapper extends BaseMapper<ActiveControl> {

     List<ActiveControl> getActiveControlByEquipmentId(Integer equipmentId);

     /**
      * @param equipmentIds
      * @param startTime      开始时间
      * @param endTime        结束时间
      * @return {@link ControlDTO}
      */
     List<ControlDTO> findAllControl(@Param("equipmentIds") Collection<Integer> equipmentIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


     String getDoorAccessPassword(int stationId, int equipmentId);

     Integer findDoorControlType(@Param("equipmentId") Integer equipmentId);


     Boolean existsActiveControl(@Param("userId") Integer userId, @Param("equipmentId") Integer equipmentId, @Param("controlId") Integer controlId, @Param("parameterValues") String parameterValues);
}

