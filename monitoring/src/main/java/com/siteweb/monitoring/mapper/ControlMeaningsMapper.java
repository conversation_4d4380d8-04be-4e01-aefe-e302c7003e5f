package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.ControlMeaningsDTO;
import com.siteweb.monitoring.entity.ControlMeanings;

import java.util.List;

public interface ControlMeaningsMapper extends BaseMapper<ControlMeanings> {

    List<ControlMeanings> getControlMeaningsByEquipmentId(Integer equipmentId);

    List<ControlMeaningsDTO> findControlMeaningsDTOsByControlId(Integer equipmentId, Integer controlId);

    void batchInsert(List<ControlMeanings> controlMeaningsList);
}

