package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.entity.MonitorUnitSignal;

import java.util.Collection;
import java.util.List;

public interface MonitorUnitSignalMapper extends BaseMapper<MonitorUnitSignal> {
    void batchInsert(Collection<MonitorUnitSignal> monitorUnitSignals);

    MonitorUnitSignal findMonitorUnitSignal(Integer stationId, Integer equipmentId, Integer signalId);

    List<Integer> findSignalIdByEquipmentId(Integer equipmentId);
}
