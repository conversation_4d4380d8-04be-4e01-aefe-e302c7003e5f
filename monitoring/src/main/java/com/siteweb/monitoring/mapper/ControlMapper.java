package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.dto.ControlInfoDto;
import com.siteweb.monitoring.dto.SimpleControlDTO;
import com.siteweb.monitoring.entity.Control;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ControlMapper extends BaseMapper<Control> {

    List<Control> findControlsByEquipmentId(Integer equipmentId);

    List<Control> findControlsByEquipmentTemplateId(Integer equipmentId);

    List<Control> findControlByStationIdEquipmentIdControlId(Integer stationId, Integer equipmentId, Integer controlId);

    List<ConfigControlItem> findControlItemByEquipmentId(Integer equipmentId);

    List<SimpleControlDTO> findSimpleControlDTOsByEquipmentId(Integer equipmentId);

    List<ConfigControlItem> findSimpleControlDTOsByEquipmentIdAndBaseTypeId(Integer equipmentId, Long baseTypeId);

    List<ConfigControlItem> findSimpleControlDTOsByEquipmentIdsAndBaseTypeId(@Param("list") List<Integer>  equipmentIds, Long baseTypeId);

    List<ConfigControlItem> findSimpleControlDTOsByEquipmentIdAndControlId(Integer equipmentId,Integer controlId);

    void batchInsert(@Param("controlList") List<Control> controlList);

    ControlInfoDto findControlInfo(Integer controlId, Integer equipmentTemplateId);
    /**
     * 获取控制命令id
     *
     * @param stationId       局站id
     * @param equipmentId     设备id
     * @param commandCategory 命令类型
     * @return {@link Integer}
     */
    Integer findCommandIdByCommandCategory(Integer stationId, Integer equipmentId, Integer commandCategory);

    List<Control> findByEquipmentTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    List<ConfigControlItem> findSimpleControlDTOsByEquipmentIdAndCmdToken(Integer equipmentId, String cmdToken);
}

