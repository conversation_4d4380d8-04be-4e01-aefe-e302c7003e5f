package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.HistoryEventFilterGroupDTO;
import com.siteweb.monitoring.dto.HistoryEventPageDTO;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.vo.HistoryEventFilterVO;
import com.siteweb.monitoring.vo.HistoryPowerOffCountVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Sort;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface HistoryEventMapper extends BaseMapper<HistoryEvent> {

    List<HistoryEvent> findByBaseEquipmentIdAndStartTimeSpan(Integer baseEquipmentId, Date startDate, Date endDate);

    List<HistoryEvent> findByEquipmentIdAndStartTimeSpan(Integer equipmentId, Date startDate, Date endDate);

    List<HistoryEvent> findByEventIdAndStartTimeSpan(Integer eventId, Date startDate, Date endDate);

    List<HistoryEvent> findByStartTimeSpan(Date startDate, Date endDate);

    Long countByStartTimeSpan(Date startDate, Date endDate);

    List<Integer> listAlertEquipmentsByTimeSpan(Date startDate, Date endDate);

    @MapKey("eventLevel")
    List<Map<Integer, Integer>> groupHistoryEventsBySeverity(@Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate,
                                                       @Param("equipmentIds") Set<Integer> equipmentIds);

    List<HistoryEvent> findByConvergenceEventId(Long convergenceEventId);

    HistoryEvent findByStartTimeAndEquipmentIdAndEventIdAndConditionId(Integer equipmentId, Integer eventId, Date startDate, Integer eventConditionId, Integer stationId);

    List<HistoryEvent> findAlarmDurationByEquipmentIdAndEventId(Integer equipmentId, Integer eventId, Date startTime, Date endTime);

    List<HistoryEvent> findDurationByStationIdsAndEventCategoryId(List<Integer> stationIds, Integer eventCategoryId, Date startTime, Date endTime, boolean filterByEventLevel);

    List<HistoryEvent> findDurationByResourceStructureIdsAndEventCategoryId(List<Integer> resourceStructureIds, Integer eventCategoryId, Date startTime, Date endTime, boolean filterByEventLevel);

    List<HistoryEvent> isExistAlarmByEquipmentIdAndTime(Integer equipmentId, Date time);

    List<HistoryPowerOffCountVO> getPowerOffCountByStationIds(List<Integer> stationIds, Date startTime, Date endTime);
    List<HistoryPowerOffCountVO> getPowerOffCountByStationIds(List<Integer> stationIds,Date startTime,Date endTime,Integer eventCategoryId);

    Integer findAlarmCountByEquipmentIdAndEventId(Integer equipmentId, Integer eventId, Date startTime, Date endTime);

    void batchInsert(@Param("list") List<HistoryEvent> historyEventList);

    // 新增的统计方法，支持权限过滤
    /**
     * 统计历史事件总数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param equipmentIds 设备ID列表（权限过滤后）
     * @param resourceStructureIds 资源结构ID列表（权限过滤后）
     * @return 总数量
     */
    Long countHistoryEvents(@Param("startTime") Date startTime,
                           @Param("endTime") Date endTime,
                           @Param("equipmentIds") Set<Integer> equipmentIds,
                           @Param("resourceStructureIds") Set<Integer> resourceStructureIds);

    /**
     * 按设备统计历史事件数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param equipmentIds 设备ID列表（权限过滤后）
     * @param resourceStructureIds 资源结构ID列表（权限过滤后）
     * @return Map<设备ID, 数量>
     */
    @MapKey("equipmentId")
    List<Map<String, Object>> countHistoryEventsByEquipment(@Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime,
                                                           @Param("equipmentIds") Set<Integer> equipmentIds,
                                                           @Param("resourceStructureIds") Set<Integer> resourceStructureIds);

    /**
     * 按事件等级统计历史事件数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param equipmentIds 设备ID列表（权限过滤后）
     * @param resourceStructureIds 资源结构ID列表（权限过滤后）
     * @return Map<事件等级, 数量>
     */
    @MapKey("eventLevel")
    List<Map<String, Object>> countHistoryEventsByEventLevel(@Param("startTime") Date startTime,
                                                            @Param("endTime") Date endTime,
                                                            @Param("equipmentIds") Set<Integer> equipmentIds,
                                                            @Param("resourceStructureIds") Set<Integer> resourceStructureIds);

    /**
     * 按设备类型统计历史事件数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param equipmentIds 设备ID列表（权限过滤后）
     * @param resourceStructureIds 资源结构ID列表（权限过滤后）
     * @return Map<设备类型, 数量>
     */
    @MapKey("equipmentCategory")
    List<Map<String, Object>> countHistoryEventsByEquipmentCategory(@Param("startTime") Date startTime,
                                                                   @Param("endTime") Date endTime,
                                                                   @Param("equipmentIds") Set<Integer> equipmentIds,
                                                                   @Param("resourceStructureIds") Set<Integer> resourceStructureIds);

    /**
     * 按资源结构ID统计历史事件数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param equipmentIds 设备ID列表（权限过滤后）
     * @param resourceStructureIds 资源结构ID列表（权限过滤后）
     * @return Map<资源结构ID, 数量>
     */
    @MapKey("resourceStructureId")
    List<Map<String, Object>> countHistoryEventsByResourceStructureId(@Param("startTime") Date startTime,
                                                                      @Param("endTime") Date endTime,
                                                                      @Param("equipmentIds") Set<Integer> equipmentIds,
                                                                      @Param("resourceStructureIds") Set<Integer> resourceStructureIds);

    /**
     * 分页获取历史告警
     */
    IPage<HistoryEventPageDTO> queryHistoryEventPage(@Param("page") Page<Object> page,
                                                     @Param("filter") HistoryEventFilterVO historyEventFilterVO,
                                                     @Param("sort") Sort sort);
    /**
     * 获取历史告警数量统计
     */
    List<HistoryEventFilterGroupDTO> groupHistoryEventBySeverity(@Param("filter") HistoryEventFilterVO historyEventFilterVO, @Param("standardAlarmNameIdIsNotNull") boolean standardAlarmNameIdIsNotNull);
}

