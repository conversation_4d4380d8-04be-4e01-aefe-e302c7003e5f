package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.StationFilterDTO;
import com.siteweb.monitoring.dto.StationGroupStateDTO;
import com.siteweb.monitoring.dto.StationProjectDetail;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.model.StationState;
import com.siteweb.monitoring.vo.StationProjectVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface StationMapper extends BaseMapper<Station> {
     void setStationState(Integer stationId, Date startTime, Date endTime, Integer stationState);
     List<Station> getStationsInBatch(@Param("list") List<Integer> stationIds);

     void saveProjectStateStation(Integer stationId, Date startTime, Date endTime, Integer userId, String reason);

     StationProjectVO getStationProjectVO(Integer stationId);

     List<StationProjectDetail> getAllProjectStations();

     StationGroupStateDTO getStationGroupStateDTO(@Param("list") List<Integer> stationIds);

     List<StationState> getAllByStationStates();

     List<Integer> findStationIdsByUserId(@Param("userId") Integer userId);

    Integer findStationBaseTypeByStationIdAndStandardVer(Integer stationId, Integer standardVer);

     /**
      * 获取局站id根据过滤条件
      * @param stationGroupType 分组方式
      * @param stationStructureList 分组名称
      * @param stationCategoryList 局站类型
      * @param stationStateList 局站状态
      * @param stationIdList 局站ids
      * @return {@link List }<{@link Integer }>
      */
     Set<Integer> findIdsByFilterCondition(@Param("stationFilterDTO") StationFilterDTO stationFilterDTO);
}

