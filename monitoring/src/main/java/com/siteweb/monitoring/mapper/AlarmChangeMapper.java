package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.entity.AlarmChange;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface AlarmChangeMapper extends BaseMapper<AlarmChange> {

    Long getMaxSerialNo();

    List<AlarmChange> readAlarmChange(Long startSerialNo);


    void batchInsert(@Param("list") List<AlarmChange> batchInsertAlarmChangeList);

    Set<Integer> getFilterEventLevel();
}
