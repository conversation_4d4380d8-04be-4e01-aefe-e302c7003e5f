package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Event;
import com.siteweb.monitoring.vo.EventRequestByCondition;
import com.siteweb.monitoring.vo.EventRequestBySignalId;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface EventMapper extends BaseMapper<Event> {

    List<Event> findEventsByStationIdAndEquipmentId(Integer stationId, Integer equipmentId);

    List<ConfigEventDTO> findConfigEventByEquipmentTemplateId(Integer equipmentTemplateId);

    List<SimpleEventDTO> findSimpleEventDTOs();

    List<SimpleEventDTO> findSimpleEventDTOsByEquipmentId(int equipmentId);

    ConfigEventDTO findConfigEventDTOByEventId(int equipmentId, int eventId);

    ConfigEventDTO findConfigEventDTOBySignalId(int equipmentId, int signalId);

    Integer findMaxEventIdByTemplateId(Integer equipmentTemplateId);

    List<EquipmentEventDto> findEventsByEquipmentIdAndEventIds(Integer equipmentId, List<Integer> eventIds);

    void batchInsert(List<Event> eventList);

    List<SimpleEventSignalDTO> findEventsByEquipmentIdAndSignalIds(@Param("eventRequestBySignalId") EventRequestBySignalId eventRequestBySignalId);

    List<SimpleEventDTO> findSimpleEventDTOsByEquipmentIds(Collection<Integer> equipmentIds);

    List<SimpleEventDTO> findEventsByEventRequestByCondition(@Param("eventRequestByCondition") EventRequestByCondition eventRequestByCondition);

    List<ConfigEventDTO> findAllConfigEvents();

    void updateEventByConfig(@Param("event") Event event);

    /**
     * 获取自诊断告警配置
     * @return {@link List }<{@link SelfDiagnoseEventConfig }>
     */
    List<SelfDiagnoseEventConfig> findSelfDiagnoseEventConfigByWorkStationType(@Param("workstationType") Integer workstationType);
}

