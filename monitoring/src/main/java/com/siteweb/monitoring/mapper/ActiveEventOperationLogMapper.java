package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.ActiveEventOperationLogDTO;
import com.siteweb.monitoring.entity.ActiveEventOperationLog;
import com.siteweb.monitoring.querywrapper.ActiveEventOperationLogWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ActiveEventOperationLogMapper
 * @createTime 2022-04-09 13:52:45
 */
public interface ActiveEventOperationLogMapper extends BaseMapper<ActiveEventOperationLog> {

    int getCountBySequenceId(String sequenceId);

    List<ActiveEventOperationLogDTO> findBySequenceId(String sequenceId);

    List<ActiveEventOperationLog> findByWrapper(@Param("wrapper") ActiveEventOperationLogWrapper queryWrapper);

    void batchInsert(@Param("activeEventOperationLogList") List<ActiveEventOperationLog> activeEventOperationLogList);
}
