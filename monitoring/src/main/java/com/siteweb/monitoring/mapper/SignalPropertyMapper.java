package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EquipmentSignalPropertyDTO;
import com.siteweb.monitoring.entity.SignalProperty;

import java.util.List;

public interface SignalPropertyMapper extends BaseMapper<SignalProperty> {

    int insertSignalProperty(SignalProperty signalProperty);

    List<Integer> findSignalPropertyIdsBySignalId(int equipmentId, int signalId);

    int deleteSignalProperty(SignalProperty signalProperty);

    List<EquipmentSignalPropertyDTO> findSignalPropertiesByEquipmentIdAndSignalIds(Integer equipmentId, List<Integer> signalIds);

    void batchInsert(List<SignalProperty> signalProperties);
}

