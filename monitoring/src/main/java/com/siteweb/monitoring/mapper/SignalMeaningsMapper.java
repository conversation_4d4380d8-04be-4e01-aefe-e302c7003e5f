package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EquipmentSignalMeaningsDTO;
import com.siteweb.monitoring.dto.SignalMeaningDTO;
import com.siteweb.monitoring.entity.SignalMeanings;

import java.util.List;

public interface SignalMeaningsMapper extends BaseMapper<SignalMeanings> {

    int insertSignalMeanings(SignalMeanings signalMeanings);

    int updateSignalMeanings(SignalMeanings signalMeanings);

    List<SignalMeaningDTO> findSignalMeaningDTOBySignalId(int equipmentId, int signalId);

    int deleteSignalMeaningsById(int equipmentTemplateId, int signalId, int stateValue);

    List<EquipmentSignalMeaningsDTO> findSignalsByEquipmentAndSignalIds(Integer equipmentId, List<Integer> signalIds);

    void batchInsert(List<SignalMeanings> signalMeanings);
}

