package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EventIdentityDTO;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description TimeGroupSpanMapper
 * @createTime 2022-07-08 13:14:35
 */
public interface TimeGroupSpanMapper extends BaseMapper<TimeGroupSpan> {

    List<TimeGroupSpan> findByTimeGroupId(Integer timeGroupId);

    TimeGroupSpan findByWeekAndTimeGroupId(Integer week,Integer timeGroupId);

    List<TimeGroupSpan> findEquipmentMaskByWeek(int week);

    List<TimeGroupSpan> findEventMaskByWeekAndEquipmentId(int week, Integer equipmentId);

    List<TimeGroupSpan> findByWeekAndTimeGroupIds(int week, Collection<Integer> timeGroupIds);

    List<TimeGroupSpan> findByStationId(Integer stationId);

    List<TimeGroupSpan> findByStationIdAndEquipmentId(Integer stationId, Integer equipmentId);

    List<TimeGroupSpan> findEventMaskTimeGroup(@Param("stationId") Integer stationId, @Param("equipmentId") Integer equipmentId, @Param("eventId") Integer eventId);

    List<Integer> findTimeSpanIdsByEquipmentIds(@Param("equipmentIds") List<Integer> equipmentIds);

    void batchInsert(@Param("timeGroupSpans") List<TimeGroupSpan> timeGroupSpans);

    void deleteByEventMaksIdentities(List<EventIdentityDTO> eventIdentityDTOList);

    void updateLastUpdateDateAndTimeSpanCharByTimeGroupIdAndWeek(@Param("timeMaskChar") String timeMaskChar, @Param("timeGroupId") Integer timeGroupId, @Param("week") Integer week, @Param("lastUpdateDate") Date lastUpdateDate);
}
