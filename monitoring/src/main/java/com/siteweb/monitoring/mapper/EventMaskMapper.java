package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.EventMask;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface EventMaskMapper extends BaseMapper<EventMask> {

    EventMask findEventMaskByEventId(Integer stationId, Integer equipmentId, Integer eventId);

    EventMaskDTO findEventMaskDTOById(Integer stationId, Integer equipmentId, int eventId);

    List<EventMask> findEventMaskByEquipmentId(Integer stationId, Integer equipmentId);

    void deleteEventMask(int stationId, int equipmentId, int eventId);

    List<SimpleEventMaskDTO> findSimpleEventMaskDTOsByEquipmentId(Integer equipmentId);

    List<EventMask> findByTimeGroupIds(Collection<Integer> timeGroupIds);

    Page<EventMaskDTO> findEventMaskByKeywordsPage(@Param("page") Page<EventMaskDTO> page, @Param("eventMaskFilterDTO") EventMaskFilterDTO eventMaskFilterDTO);

    long findEventMaskByKeywordsPageCount(@Param("eventMaskFilterDTO") EventMaskFilterDTO eventMaskFilterDTO);

    Page<ConditionEventMaskDTO> findSimpleEventMaskByBaseTypeIdsPage(@Param("page") Page<ConditionEventMaskDTO> page, @Param("eventMaskFilterDTO") EventMaskFilterDTO eventMaskFilterDTO);

    long findEventMaskByBaseTypeIdsPageCount(@Param("eventMaskFilterDTO") EventMaskFilterDTO eventMaskFilterDTO);

    List<EventMask> findEventMaskByEquipmentEventIdIdList(@Param("equipmentEventIdList") List<EquipmentEventIdDTO> equipmentEventIdList);

    List<EventMask> findEventMaskByBaseTypeIds(@Param("eventMaskFilterDTO") EventMaskFilterDTO eventMaskFilterDTO);

    Integer findMaxTimeGroupId();

    List<EventMask> findEventMaskCreateByBaseTypeIds(@Param("eventMaskFilterDTO") EventMaskFilterDTO eventMaskFilterDTO);

    Integer findTimeGroupId(Integer stationId, Integer equipmentId, Integer eventId);

    List<EventMask> findByEventMaksIdentities(@Param("eventMaskIdentityDTOList") List<EventIdentityDTO> eventIdentityDTOList);

    void deleteByEventMaksIdentities(@Param("eventMaskIdentityDTOList") List<EventIdentityDTO> eventIdentityDTOList);
}

