package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.entity.ConfigChangeMacroLog;
import com.siteweb.monitoring.entity.ConfigChangeMicroLog;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description ConfigChangeMacroLogMapper
 * @createTime 2022-04-12 14:14:59
 */
public interface ConfigChangeMacroLogMapper extends BaseMapper<ConfigChangeMacroLog> {

    List<ConfigChangeMicroLog> findEquipmentChangeMicroLogByUpdateTime(Date startTime, Date endTime);

    List<Integer> findEquipmentTemplateChangeLogByUpdateTime(Date startTime, Date endTime);

    List<Integer> findResourceStructureChangeLogByUpdateTime(Date startTime, Date endTime);
    List<Integer> findStationChangeLogByUpdateTime(Date startTime, Date endTime);

    List<ConfigChangeMacroLog> findEquipmentChangeMacroLogByUpdateTime(Date startTime, Date endTime);
}
