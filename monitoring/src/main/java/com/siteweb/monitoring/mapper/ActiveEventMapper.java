package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.dto.EventIdentityDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ActiveEventMapper extends BaseMapper<ActiveEvent> {

    List<ActiveEvent> getAllActiveEvent();

    /**
     * 根据开启的告警等级对数据进行过滤
     */
    List<ActiveEvent> getAllActiveEventByEventLevel();


    /**
     * 告警强制结束
     *
     * @param params params
     */
    void cancelActiveEvent(HashMap<String, Object> params);

    /**
     * 字节告警强制结束
     */
    void cancelBdActiveEvent(HashMap<String, Object> params);


    List<ActiveEvent> findByConvergenceEventId(Long convergenceEventId);

    int updateActiveEventByActiveEventInstructionDTO(String sequenceId,String instructionId,Integer instructionStatus);

    void updateCancelInfo(@Param("sequenceId") String sequenceId, @Param("userId") int userId, @Param("userName") String userName, @Param("endTime") Date endTime, @Param("note") String note);

    /**
     * 确认活动告警
     *
     * @param params params
     */
    void confirmActiveEvent(HashMap<String, Object> params);

    /**
     * 字节确认活动告警
     */
    void confirmBdActiveEvent(Map<String, Object> params);


    void batchUpdateConfirmEvent(@Param("list") List<ActiveEvent> batchUpdateList);

    ActiveEvent findBySequenceIdForUpdate(@Param("sequenceId") String sequenceId);
    List<ActiveEvent> findBySequenceIdsForUpdate(@Param("sequenceId") List<String> sequenceIds);
    List<ActiveEvent> findUnConfirmBySequenceIdsForUpdate(@Param("sequenceIds") List<String> sequenceIds);
    List<ActiveEvent> findUnEndBySequenceIdsForUpdate(@Param("sequenceIds") List<String> sequenceIds);

    List<String> findSequenceIdsByIdentities(@Param("eventIdentityDTOList") List<EventIdentityDTO> eventIdentityDTOList);

}

