package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.InfluxDbUtil;
import com.siteweb.monitoring.dto.EquipmentSignalIndexDTO;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.model.HistorySignalGroupByTime;
import com.siteweb.monitoring.model.HistorySignalPointValue;
import com.siteweb.monitoring.model.ReportCount;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 历史信号管理类
 *
 * @author: Habits
 * @time: 2022/5/14 9:17
 * @description:
 **/
@Component
public class HistorySignalManager {

    @Autowired
    InfluxDBManager influxDBManager;

    @Autowired
    SystemConfigService systemConfigService;

    private static final String measurementName = "historydatas";

    private static boolean fiveMinutesStorage = false;

    String selectDuration = "select * from historydatas where time >='%s' and time <='%s' and SignalId = '%s' order by time asc";

    String selectDurationByBaseTypeId = "select * from historydatas where time >='%s' and time <='%s' and BaseTypeId = '%s' order by time asc";

    String selectMultipleWhereDuration = "select * from historydatas where time >='%s' and time <='%s' and (%s) order by time asc";


    // 计算数量
    String countDuration = "select count(PointValue) from historydatas where time >='%s' and time <='%s' and SignalId = '%s' order by time asc";
    String countDurationByBaseTypeId = "select count(PointValue) from historydatas where time >='%s' and time <='%s' and BaseTypeId = '%s' order by time asc";

    // 时间粒度
    String selectTimeGranularity = "SELECT spread(PointValue) as spread, first(PointValue) as first, last(PointValue) as last, mean(PointValue) as PointValue, max(PointValue) as max, min(PointValue) as min , count(PointValue) as count FROM historydatas WHERE time >= '%s' and time <= '%s' and SignalId = '%s' group by time(%s) fill(none)";
    // 和上面作用一样，不过会对null值填充0
    String selectTimeGranularityFull = "SELECT spread(PointValue) as spread, first(PointValue) as first, last(PointValue) as last, mean(PointValue) as PointValue, max(PointValue) as max, min(PointValue) as min , count(PointValue) as count FROM historydatas WHERE time >= '%s' and time <= '%s' and SignalId = '%s' group by time(%s) fill(0)";

    // 时间颗粒度为周
    String selectWeekTimeGranularity = "SELECT spread(PointValue) as spread, first(PointValue) as first, last(PointValue) as last, mean(PointValue) as PointValue, max(PointValue) as max, min(PointValue) as min , count(PointValue) as count FROM historydatas WHERE time >= '%s' and time <= '%s' and SignalId = '%s' group by time(%s, 4d) fill(none)";

    // 时间颗粒度为月
    String selectMonthDuration = "select spread(PointValue) as spread, first(PointValue) as first, last(PointValue) as last, mean(PointValue) as PointValue, max(PointValue) as max, min(PointValue) as min , count(PointValue) as count from historydatas where time >='%s' and time <='%s' and SignalId = '%s' fill(none)";

    // 分页，时间粒度
    String countTimeGranularity = "SELECT count(PointValue) as count FROM historydatas WHERE time >= '%s' and time <= '%s' and SignalId = '%s' group by time(%s) fill(none)";


    /**
     * 查询历史信号数据
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param equipmentId 设备ID
     * @param signalId    信号ID
     * @param page        分页参数
     * @return
     */
    public List<HistorySignal> findHistorySignalByDuration(Date startTime, Date endTime,Integer equipmentId, Integer signalId, Pageable page, Boolean switchDatabase2) {
        return findHistorySignalByDuration(startTime, endTime, equipmentId + "." + signalId, page, switchDatabase2);
    }

    /**
     * 查询历史信号数据
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param equipmentId 设备ID
     * @param signalId    信号ID
     * @param page        分页参数
     * @return
     */
    public List<HistorySignal> findHistorySignalByDurationAndSignalType(Date startTime, Date endTime,String signalType,Integer equipmentId, Integer signalId, Pageable page, Boolean switchDatabase2) {
        String sql = "select * from historydatas where time >= $startTime and time <= $endTime and SignalId = $realTimeKey $signalTypes order by time asc";
        sql = InfluxDbUtil.replaceSignalTypes(sql,"$signalTypes",signalType);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("realTimeKey", equipmentId + "." + signalId);
        if (page != null) {
            sql = "select * from historydatas where time >= $startTime and time <= $endTime and SignalId = $realTimeKey order by time asc limit $limit offset $offset";
            params.put("limit", page.getPageSize());
            params.put("offset", page.getPageNumber() * page.getPageSize());
        }
        return influxDBManager.list(sql, HistorySignal.class, switchDatabase2, params);
    }

    /**
     * 查询历史信号数据（默认根据系统参数过滤条数）
     *
     * @param startTime       开始是啊金
     * @param endTime         结束时间
     * @param signalTypes     存储类型
     * @param equipmentId     设备ID
     * @param signalId        信号ID
     * @param switchDatabase2 是否切换周期存储库
     * @return
     */
    public List<HistorySignal> findHistorySignalByDurationAndSignalType(Date startTime, Date endTime, String signalTypes, Integer equipmentId, Integer signalId, Boolean switchDatabase2) {
        String sql = "select * from historydatas where time >= $startTime and time <= $endTime and SignalId = $realTimeKey $signalTypes order by time asc";
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime",DateUtil.dateToString(startTime));
        params.put("endTime",DateUtil.dateToString(endTime));
        params.put("realTimeKey", equipmentId + "." + signalId);
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.history.signal.limit");
        if (systemConfig != null && CharSequenceUtil.isNotBlank(systemConfig.getSystemConfigValue()) && Integer.parseInt(systemConfig.getSystemConfigValue()) > 0) {
            sql = "select * from historydatas where time >= $startTime and time <= $endTime and SignalId = $realTimeKey $signalTypes order by time asc limit $limit";
            sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
            params.put("limit", Integer.valueOf(systemConfig.getSystemConfigValue()));
        }
        return influxDBManager.list(sql, HistorySignal.class, switchDatabase2, params);
    }

    /**
     * 查询历史信号数据
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param realTimeKey 设备ID，信号id组合key（"."分隔）
     * @param page        分页参数
     * @return
     */
    public List<HistorySignal> findHistorySignalByDuration(Date startTime, Date endTime, String realTimeKey, Pageable page, Boolean switchDatabase2) {
//        String sql = String.format(selectDuration, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime), realTimeKey);
        String sql = "select * from historydatas where time >= $startTime and time <= $endTime and SignalId = $realTimeKey order by time asc";
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("realTimeKey", realTimeKey);
        if (page != null) {
//            sql += " limit " + page.getPageSize() + " offset " + page.getPageNumber() * page.getPageSize();
            sql = "select * from historydatas where time >= $startTime and time <= $endTime and SignalId = $realTimeKey order by time asc limit $limit offset $offset";
            params.put("limit", page.getPageSize());
            params.put("offset", page.getPageNumber() * page.getPageSize());
        }
        return influxDBManager.list(sql, HistorySignal.class, switchDatabase2, params);
    }

    public List<HistorySignal> findLastHistorySignalByDuration(String realTimeKey, Date startTime, Date endTime, Boolean switchDatabase2) {
        String sql = "select last(PointValue) as PointValue from historydatas where time >= $startTime and time < $endTime and SignalId = $realTimeKey order by time asc";
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("realTimeKey", realTimeKey);

        return influxDBManager.list(sql, HistorySignal.class, switchDatabase2, params);
    }

    /**
     * 查询历史信号数据
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param baseTypeId 信号基类id
     * @param page       分页参数
     * @return
     */
    public List<HistorySignal> findHistorySignalByBaseTypeIdAndSignalType(Date startTime, Date endTime, String signalTypes,String baseTypeId, Pageable page, Boolean switchDatabase2) {
        String sql = "select * from historydatas where time >= $startTime and time <= $endTime and BaseTypeId = $baseTypeId $signalTypes order by time asc";
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        HashMap<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("baseTypeId", baseTypeId);
        if (page != null) {
            sql = "select * from historydatas where time >= $startTime and time <= $endTime and BaseTypeId = $baseTypeId order by time asc limit $limit offset $offset";
            params.put("limit", page.getPageSize());
            params.put("offset", page.getPageNumber() * page.getPageSize());
        }
        return influxDBManager.list(sql, HistorySignal.class, switchDatabase2, params);
    }


    public List<HistorySignal> findMultipleHistorySignalByDuration(Date startTime, Date endTime, List<EquipmentSignalIndexDTO> signals, Pageable page, Boolean switchDatabase2) {
        if (signals == null) {
            return new ArrayList<>();
        }
        List<String> signalList = signals.stream().map(e -> String.format(" SignalId = '%d.%d' ", e.getEquipmentId(), e.getSignalId())).collect(Collectors.toList());
        String where = String.join("or", signalList);
        String sql = String.format(selectMultipleWhereDuration, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime), where);
        if (page != null) {
            sql += " limit " + page.getPageSize() + " offset " + page.getPageNumber() * page.getPageSize();
        }
        return influxDBManager.list(sql, HistorySignal.class, switchDatabase2);
    }


    /**
     * 计算信号数量
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param signalTypes
     * @param equipmentId 设备id
     * @param signalId    信号id
     * @return
     */
    public Integer countHistorySignalByDurationAndSignalType(Date startTime, Date endTime, String signalTypes, Integer equipmentId, Integer signalId, Boolean switchDatabase2) {
        String sql = "select count(PointValue) from historydatas where time >= $startTime and time <= $endTime and SignalId = $realKey $signalTypes order by time asc";
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("realKey", equipmentId + "." + signalId);
        List<ReportCount> result = influxDBManager.list(sql, ReportCount.class, measurementName, switchDatabase2, params);
        return result.isEmpty() ? 0 : result.get(0).getCount();
    }


    public Integer countHistorySignalByBaseTypeIdAndSignalType(Date startTime, Date endTime, String signalTypes,String baseTypeId, Boolean switchDatabase2) {
        String sql = "select count(PointValue) from historydatas where time >= $startTime and time <= $endTime and BaseTypeId = $baseTypeId $signalTypes order by time asc";
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        HashMap<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("baseTypeId", baseTypeId);
        List<ReportCount> result = influxDBManager.list(sql, ReportCount.class, measurementName, switchDatabase2, params);
        return result.isEmpty() ? 0 : result.get(0).getCount();
    }


    /**
     * 按周来获取历史信号
     *
     * @param startTime            开始时间
     * @param endTime              结束时间
     * @param signalId             信号
     * @param valueRetrievalMethod
     * @return
     */
    public List<HistorySignalGroupByTime> findWeekHistorySignalGroupByTimeAndSignalType(Date startTime, Date endTime, String signalTypes, String signalId, Boolean switchDatabase2, String valueRetrievalMethod) {
        String sql = "SELECT $valueRetrievalMethod(PointValue) as PointValue,count(PointValue) as count FROM historydatas WHERE time >= $startTime and time <= $endTime and SignalId = $signalId $signalTypes group by time(1w, 4d) fill(none)";
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        sql = InfluxDbUtil.replaceValueRetrievalMethod(sql, "$valueRetrievalMethod", valueRetrievalMethod);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("signalId", signalId);
        return influxDBManager.list(sql, HistorySignalGroupByTime.class, switchDatabase2, params);
    }

    /**
     * 按月来获取历史信号
     *
     * @param startTime            开始时间
     * @param endTime              结束时间
     * @param signalKey            设备id.信号id
     * @param valueRetrievalMethod
     * @return
     */
    public List<HistorySignalGroupByTime> findMonthHistorySignalGroupByTimeAndSignalType(Date startTime, Date endTime, String signalTypes, String signalKey, Boolean switchDatabase2, String valueRetrievalMethod) {
        List<HistorySignalGroupByTime> result = new ArrayList<>();
        // 开始时间当月第一天和最后一天
        Date startTimeMonthOfFirstDay = DateUtil.getFirstDayOfMonth(startTime);
        Date startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTime);
        // 结束时间当月第一天
        Date endTimeMonthOfFirstDay = DateUtil.getFirstDayOfMonth(endTime);

        // 如果开始时间和结束时间都是同一个月，直接查询
        if (startTimeMonthOfFirstDay != null && startTimeMonthOfFirstDay.compareTo(endTimeMonthOfFirstDay) == 0) {
            result = getHistoryDataPointGroupByTimeListAndSignalType(startTime, endTime, signalTypes, signalKey, switchDatabase2,valueRetrievalMethod);
            return result;
        }
        // 查询开始时间到当月最后一天的数据
        result.addAll(getHistoryDataPointGroupByTimeListAndSignalType(startTime, startTimeMonthOfLastDay, signalTypes, signalKey, switchDatabase2,valueRetrievalMethod));
        // 从下一个月的第一天开始循环查询，直到结束时间
        startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTime);
        while (startTimeMonthOfFirstDay != null && !startTimeMonthOfFirstDay.after(endTime)) {
            startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTimeMonthOfFirstDay);
            result.addAll(getHistoryDataPointGroupByTimeListAndSignalType(startTimeMonthOfFirstDay, startTimeMonthOfLastDay, signalTypes, signalKey, switchDatabase2,valueRetrievalMethod));
            startTimeMonthOfFirstDay = DateUtil.getNextMonthFirstDay(startTimeMonthOfFirstDay);
        }
        return result;
    }

    public List<HistorySignalGroupByTime> getHistoryDataPointGroupByTimeListAndSignalType(Date startTime, Date endTime, String signalTypes, String signalKey, Boolean switchDatabase2, String valueRetrievalMethod) {
        String sql = "select $valueRetrievalMethod(PointValue) as PointValue,count(PointValue) as count from historydatas where time >= $startTime and time <= $endTime and SignalId = $signalKey $signalTypes fill(none)";
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        sql = InfluxDbUtil.replaceValueRetrievalMethod(sql, "$valueRetrievalMethod", valueRetrievalMethod);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("signalKey", signalKey);
        return influxDBManager.list(sql, HistorySignalGroupByTime.class, switchDatabase2, params);
    }


    public List<HistorySignalGroupByTime> findYearHistorySignalGroupByTimeAndSignalType(Date startTime, Date endTime, String signalTypes, String signalKey, Boolean switchDatabase2, String valueRetrievalMethod) {
        List<HistorySignalGroupByTime> result = new ArrayList<>();
        // 开始时间今年第一天和今年最后一天
        Date startTimeMonthOfFirstDay = DateUtil.getFirstDayOfYear(startTime);
        Date startTimeMonthOfLastDay = DateUtil.getLastDayOfYear(startTime);
        // 结束时间当年第一天
        Date endTimeMonthOfFirstDay = DateUtil.getFirstDayOfYear(endTime);

        // 如果开始时间和结束时间都是同一个年，直接查询
        if (startTimeMonthOfFirstDay != null && startTimeMonthOfFirstDay.compareTo(endTimeMonthOfFirstDay) == 0) {
            result = getHistoryDataPointGroupByTimeListAndSignalType(startTime, endTime, signalTypes, signalKey, switchDatabase2,
                    valueRetrievalMethod);
            return result;
        }
        // 查询开始时间到当年最后一天的数据
        result.addAll(getHistoryDataPointGroupByTimeListAndSignalType(startTime, startTimeMonthOfLastDay, signalTypes, signalKey, switchDatabase2,
                valueRetrievalMethod));
        // 从下一个年的第一天开始循环查询，直到结束时间
        startTimeMonthOfFirstDay = DateUtil.getNextYearFirstDay(startTime);
        while (startTimeMonthOfFirstDay != null && !startTimeMonthOfFirstDay.after(endTime)) {
            startTimeMonthOfLastDay = DateUtil.getLastDayOfMonth(startTimeMonthOfFirstDay);
            result.addAll(getHistoryDataPointGroupByTimeListAndSignalType(startTimeMonthOfFirstDay, startTimeMonthOfLastDay, signalTypes, signalKey, switchDatabase2,
                    valueRetrievalMethod));
            startTimeMonthOfFirstDay = DateUtil.getNextYearFirstDay(startTimeMonthOfFirstDay);
        }
        return result;
    }

    /**
     * 时间粒度查询
     *
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param signalKey       设备id.信号id
     * @param timeGranularity 时间粒度
     * @param pageable        分页对象
     * @return
     */
    public List<HistorySignalGroupByTime> findHistorySignalGroupByTime(Date startTime, Date endTime, String signalKey, String timeGranularity, Pageable pageable, Boolean switchDatabase2) {
        String sql = "SELECT spread(PointValue) as spread, first(PointValue) as first, last(PointValue) as last, mean(PointValue) as PointValue, max(PointValue) as max, min(PointValue) as min , count(PointValue) as count FROM historydatas WHERE time >= $startTime and time <= $endTime and SignalId = $signalKey group by time($timeGranularity) fill(none)";
        sql = InfluxDbUtil.replaceGranularity(sql, "$timeGranularity", timeGranularity);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("signalKey", signalKey);
        if (pageable != null) {
            sql = "SELECT spread(PointValue) as spread, first(PointValue) as first, last(PointValue) as last, mean(PointValue) as PointValue, max(PointValue) as max, min(PointValue) as min , count(PointValue) as count FROM historydatas WHERE time >= $startTime and time <= $endTime and SignalId = $signalKey group by time($timeGranularity) fill(none)  limit $limit offset $offset";
            sql = InfluxDbUtil.replaceGranularity(sql, "$timeGranularity", timeGranularity);
            params.put("limit", pageable.getPageSize());
            params.put("offset", pageable.getPageNumber() * pageable.getPageSize());
        }
        return influxDBManager.list(sql, HistorySignalGroupByTime.class, switchDatabase2, params);
    }


    public List<HistorySignalGroupByTime> findHistorySignalGroupByTimeAndSignalType(Date startTime, Date endTime, String signalTypes, String signalKey, String timeGranularity, Pageable pageable, Boolean switchDatabase2, String valueRetrievalMethod) {
        String sql = "SELECT $valueRetrievalMethod(PointValue) as PointValue,count(PointValue) as count FROM historydatas WHERE time >= $startTime and time <= $endTime and SignalId = $signalKey $signalTypes group by time($timeGranularity) fill(none)";
        sql = InfluxDbUtil.replaceGranularity(sql, "$timeGranularity", timeGranularity);
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        sql = InfluxDbUtil.replaceValueRetrievalMethod(sql, "$valueRetrievalMethod", valueRetrievalMethod);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("signalKey", signalKey);
        if (pageable != null) {
            sql = "SELECT $valueRetrievalMethod(PointValue) as PointValue,count(PointValue) as count FROM historydatas WHERE time >= $startTime and time <= $endTime and SignalId = $signalKey $signalTypes group by time($timeGranularity) fill(none)  limit $limit offset $offset";
            sql = InfluxDbUtil.replaceGranularity(sql, "$timeGranularity", timeGranularity);
            sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
            sql = InfluxDbUtil.replaceValueRetrievalMethod(sql, "$valueRetrievalMethod", valueRetrievalMethod);
            params.put("limit", pageable.getPageSize());
            params.put("offset", pageable.getPageNumber() * pageable.getPageSize());
        }
        return influxDBManager.list(sql, HistorySignalGroupByTime.class, switchDatabase2, params);
    }



    /**
     * 统计数量
     *
     * @param startTime
     * @param endTime
     * @param signalId
     * @param timeGranularity
     * @param valueRetrievalMethod
     * @return
     */
    public Integer countHistorySignalGroupByTimeAndSignalType(Date startTime, Date endTime, String signalTypes, String signalId, String timeGranularity, Boolean switchDatabase2) {
        String sql = "SELECT count(PointValue) as count FROM historydatas WHERE time >= $startTime and time <= $endTime and SignalId = $signalId $signalTypes group by time($timeGranularity) fill(none)";
        sql = InfluxDbUtil.replaceGranularity(sql, "$timeGranularity", timeGranularity);
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        params.put("signalId", signalId);
        List<ReportCount> result = influxDBManager.list(sql, ReportCount.class, measurementName, switchDatabase2, params);
        return result.isEmpty() ? 0 : result.get(0).getCount();
    }

    public List<HistorySignalGroupByTime> findFullHistorySignalsGroupByTime(Date startTime, Date endTime, String signalKey, String timeGranularity) {
        String sql = String.format(selectTimeGranularityFull, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime), signalKey, timeGranularity);
        return influxDBManager.list(sql, HistorySignalGroupByTime.class, fiveMinutesStorage);
    }

    public List<HistorySignal> findHistorySignalBySignalKeys(Date startTime, Date endTime, List<String> signalKeys, String signalTypes) {
        if (CollUtil.isEmpty(signalKeys)) {
            return Collections.emptyList();
        }
        String sql = "select * from historydatas where time >= $startTime and time <= $endTime $realTimeKey $signalTypes order by time asc";
        sql = InfluxDbUtil.replaceSignalKeys(sql, "$realTimeKey", signalKeys);
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        return influxDBManager.list(sql, HistorySignal.class, params);
    }

    /**
     * 获取指定时间段内历史信号的最大值与最小值
     * @param startTime 开始时间
     * @param endTime  结束时间
     * @param signalKeys 设备id.信号id
     * @return {@link List }<{@link HistorySignalPointValue }> 聚合结果
     */
    public List<HistorySignalPointValue> findHistorySignalBySignalKeys(Date startTime, Date endTime, List<String> signalKeys) {
        if (CollUtil.isEmpty(signalKeys)) {
            return Collections.emptyList();
        }
        String sql = "select SignalId,PointValue from historydatas where time >= $startTime and time <= $endTime $realTimeKey";
        sql = InfluxDbUtil.replaceSignalKeys(sql, "$realTimeKey", signalKeys);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        return influxDBManager.list(sql, HistorySignalPointValue.class, params);
    }

    /**
     * 获取历史信号分组 key为 设备id.信号id  values是历史信号值
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param signalKeys 信号ids
     * @return {@link Map }<{@link String }, {@link List }<{@link HistorySignalPointValue }>>
     */
    public Map<String, List<HistorySignalPointValue>> getHistorySignaltMap(DateTime startTime, Date endTime, List<String> signalKeys) {
        List<HistorySignalPointValue> historySignalPointValue = findHistorySignalBySignalKeys(startTime, endTime, signalKeys);
        return historySignalPointValue.stream().collect(Collectors.groupingBy(HistorySignalPointValue::getSignalId));
    }

    public long findHistorySignalCountBySignalKeys(Date startTime, Date endTime, List<String> signalKeys, String signalTypes) {
        if (CollUtil.isEmpty(signalKeys)) {
            return 0;
        }
        String sql = "select count(PointValue) from historydatas where time >= $startTime and time <= $endTime $realTimeKey $signalTypes";
        sql = InfluxDbUtil.replaceSignalKeys(sql, "$realTimeKey", signalKeys);
        sql = InfluxDbUtil.replaceSignalTypes(sql, "$signalTypes", signalTypes);
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtil.dateToString(startTime));
        params.put("endTime", DateUtil.dateToString(endTime));
        List<ReportCount> list = influxDBManager.list(sql, ReportCount.class, measurementName, params);
        return CollUtil.isEmpty(list) ? 0 : list.get(0).getCount();
    }

    @PostConstruct
    private void init() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.FIVE_MINUTES_STORAGE.getSystemConfigKey());
        if (Objects.nonNull(systemConfig)) {
            fiveMinutesStorage = Boolean.parseBoolean(systemConfig.getSystemConfigValue());
        }
    }
}
