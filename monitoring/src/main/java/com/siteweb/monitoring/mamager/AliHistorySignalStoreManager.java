package com.siteweb.monitoring.mamager;

import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.monitoring.job.AliHistoryDataJob;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.model.TriggerModel;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/1/23
 */
@Component
@Slf4j
public class AliHistorySignalStoreManager implements ApplicationListener<BaseSpringEvent<HAStatusChanged>> {

    @Autowired
    private FileServerConfig fileServerConfig;

    @Autowired
    SchedulerJobService schedulerService;

    @Autowired
    private SystemConfigService systemConfigService;


    // 定制阿里告警事件周期存储间隔时间，定义每隔多少秒进行一次存储
    public static final String ALI_CYCLE_STORAGE_INTERVAL_SECONDS = "ali.cycle.storage.interval.seconds";

    // 定制阿里告警事件指定数据存储的最长时间，单位秒
    public static final String ALI_CYCLE_STORAGE_MAX_PERIOD = "ali.cycle.storage.max.period";

    // 定制阿里告警事件周期存储开关
    public static final String ALI_CYCLE_STORAGE_ENABLE = "ali.cycle.storage.enable";


    public static  int DEFAULT_CYCLE_STORAGE_INTERVAL_SECONDS = 10;

    public static  int DEFAULT_CYCLE_STORAGE_MAX_PERIOD = 1800;


    public static final Map<Integer, List<Long>> equipmentIdBaseTypeIdMap = new ConcurrentHashMap<>();

    @Autowired
    private HAStatusService haStatusService;




    @PostConstruct
    public void init() {
        log.info("AliHistorySignalStoreJob init");

        if (!haStatusService.isMasterHost()) {
            log.info("AliHistorySignalStoreJob not started, HA not enabled or not master host");
            return;
        }

        SystemConfig intervalTime = systemConfigService.findBySystemConfigKey(ALI_CYCLE_STORAGE_INTERVAL_SECONDS);
        SystemConfig maxPeriod = systemConfigService.findBySystemConfigKey(ALI_CYCLE_STORAGE_MAX_PERIOD);
        SystemConfig enable = systemConfigService.findBySystemConfigKey(ALI_CYCLE_STORAGE_ENABLE);
        if (enable == null || enable.getSystemConfigValue() == null || "false".equalsIgnoreCase(enable.getSystemConfigValue())) {
            return;
        }
        if (intervalTime == null || maxPeriod == null || intervalTime.getSystemConfigValue() == null || maxPeriod.getSystemConfigValue() == null) {
            log.info("AliHistorySignalStoreListener init error, system config not exist");
            return;
        }

        DEFAULT_CYCLE_STORAGE_INTERVAL_SECONDS = Integer.parseInt(intervalTime.getSystemConfigValue());
        DEFAULT_CYCLE_STORAGE_MAX_PERIOD = Integer.parseInt(maxPeriod.getSystemConfigValue());

        String rootPath = fileServerConfig.getRootPath();
        String eventCsvPath = rootPath + "/historySignal/config.csv";
        File file = new File(eventCsvPath);
        if (!file.exists()) {
            log.error("AliHistorySignalStoreListener init error, file not exist: {}", eventCsvPath);
            return;
        }

        CsvReader csvRows = new CsvReader(file, new CsvReadConfig());
        csvRows.setContainsHeader(true);
        Iterator<CsvRow> iterator = csvRows.stream().iterator();
        while (iterator.hasNext()) {
            CsvRow row = iterator.next();
            String equipmentId = row.getByName("EquipmentId");
            String baseTypeId = row.getByName("BaseTypeId");
            if (equipmentId == null || baseTypeId == null || equipmentId.isEmpty() || baseTypeId.isEmpty() || "NULL".equalsIgnoreCase(equipmentId) || "NULL".equalsIgnoreCase(baseTypeId)) {
                continue;
            }
            log.trace("AliHistorySignalStoreListener init, equipmentId: {}, baseTypeId: {}", equipmentId, baseTypeId);
            if (equipmentIdBaseTypeIdMap.containsKey(Integer.parseInt(equipmentId))) {
                equipmentIdBaseTypeIdMap.get(Integer.parseInt(equipmentId)).add(Long.parseLong(baseTypeId));
            } else {
                ArrayList<Long> baseTypeIds = new ArrayList<>();
                baseTypeIds.add(Long.parseLong(baseTypeId));
                equipmentIdBaseTypeIdMap.put(Integer.parseInt(equipmentId), baseTypeIds);
            }
        }

        SchedulerJob schedulerJob = new SchedulerJob();
        schedulerJob.setClassName(AliHistoryDataJob.class.getName());
        schedulerJob.setRepeatInterval(Long.parseLong(intervalTime.getSystemConfigValue()));
        schedulerJob.setJobGroup("AliHistorySignalStoreJob");
        schedulerJob.setJobName("AliHistorySignalStoreJob");
        schedulerJob.setTriggerType(TriggerModel.SIMPLE_TRIGGER_TYPE);
        HashMap<String, Object> param = new HashMap<>();
        schedulerJob.setParams(param);

        try {
            schedulerService.removeSchedulerJob(schedulerJob);
            schedulerService.addSchedulerJob(schedulerJob);
        } catch (Exception e) {
            log.error("AliHistorySignalStoreListener init error", e);
        }
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<HAStatusChanged> event) {
        //如果未启用双机高可用部署，则直接退出
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = event.getData();
        log.info("{} receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", getClass().getSimpleName(), haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        init();
    }
}
