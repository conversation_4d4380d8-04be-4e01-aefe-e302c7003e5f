package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.entity.ConfigChangeMacroLog;
import com.siteweb.monitoring.entity.ConfigChangeMicroLog;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.vo.EquipmentFilterVo;
import com.siteweb.utility.service.HAStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;

/**
 * 设备管理
 */
@Component
public class EquipmentManager implements ApplicationListener<BaseSpringEvent<HAStatusChanged>> {
    private final Logger log = LoggerFactory.getLogger(EquipmentManager.class);
    @Autowired
    EquipmentMapper equipmentMapper;

    @Autowired
    StationManager stationManager;

    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    HAStatusService haStatusService;
    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    private static final ConcurrentHashMap<Integer, Equipment> EQUIPMENT_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    private boolean initialized = false;
    private Date lastUpdateTime;

    private Date lastTemplateUpdateTime;

    @PostConstruct
    public void init() {
        List<Equipment> equipmentList = loadEquipmentsFromDB();
        initialized = true;
        log.info("Init equipment from database, total {}", equipmentList.size());
    }

    public List<Equipment> loadEquipmentsFromDB() {
        EQUIPMENT_CONCURRENT_HASH_MAP.clear();
        List<Equipment> equipmentList = equipmentMapper.getAllEquipment();
        for (Equipment equipment : equipmentList) {
            EQUIPMENT_CONCURRENT_HASH_MAP.put(equipment.getEquipmentId(), equipment);
        }
        lastUpdateTime = new Date();
        lastTemplateUpdateTime = new Date();
        return equipmentList;
    }

    /**
     * 主动刷新设备的系统缓存 主要用于部分设备增删改需及时更新缓存使用
     *
     * @param equipmentIds 设备ids
     */
    public void refreshEquipmentByIds(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        equipmentIds.forEach(EQUIPMENT_CONCURRENT_HASH_MAP::remove);
        List<Equipment> equipmentList = equipmentMapper.getEquipmentsInBatch(equipmentIds);
        for (Equipment equipment : equipmentList) {
            EQUIPMENT_CONCURRENT_HASH_MAP.put(equipment.getEquipmentId(), equipment);
        }
        log.info("刷新设备缓存equipmentIds={}", equipmentIds);
    }

    /**
     * 通过设备模板id刷新设备本地缓存
     *
     * @param equipmentTemplateId
     */
    public void refreshEquipmentByEquipmentTemplateId(Set<Integer> equipmentTemplateId) {
        List<Equipment> equipment = this.getEquipmentsByTemplateId(equipmentTemplateId);
        this.refreshEquipmentByIds(equipment.stream().map(Equipment::getEquipmentId).toList());
    }

    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncEquipment() {
        if (!initialized) {
            return;
        }
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        //通过宏观日志刷新设备
        boolean macroLogResult = refreshEquipmentByMacroLog(startTime, endTime);
        if (!macroLogResult) {
            //通过微观日志刷新设备
            refreshEquipmentByMicroLog(startTime, endTime);
        }
        lastUpdateTime = new Date();
    }

    private boolean refreshEquipmentByMacroLog(Date startTime, Date endTime) {
        List<ConfigChangeMacroLog> configChangeMacroLogList = configChangeMacroLogMapper.findEquipmentChangeMacroLogByUpdateTime(startTime, endTime);
        if (CollUtil.isEmpty(configChangeMacroLogList)) {
            log.info("通过ConfigChangeMacroLog没有获取到更新数据");
            return false;
        }
        init();
        return true;
    }

    private void refreshEquipmentByMicroLog(Date startTime, Date endTime) {
        List<ConfigChangeMicroLog> configChangeMicroLogList = configChangeMacroLogMapper.findEquipmentChangeMicroLogByUpdateTime(startTime, endTime);
        //设备没有变更
        if (CollUtil.isEmpty(configChangeMicroLogList)) {
            log.info("微观日志没获取到设备变更信息");
            return;
        }
        //获取设备id
        List<Integer> equipmentIdList = new ArrayList<>();
        for (ConfigChangeMicroLog configChangeMicroLog : configChangeMicroLogList) {
            //StationId.EquipmentId
            String[] objectSplitStr = configChangeMicroLog.getObjectId().split("\\.");
            Integer equipmentId = null;
            //长度可能为1，仅仅只存了设备id
            if (objectSplitStr.length == 1) {
                equipmentId = Integer.valueOf(objectSplitStr[0]);
            }
            if (objectSplitStr.length >= 2) {
                equipmentId = Integer.valueOf(objectSplitStr[1]);
            }
            equipmentIdList.add(equipmentId);
        }
        refreshEquipmentByIds(equipmentIdList);
    }

    /**
     * 按模板刷新设备
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncEquipmentByTemplate() {
        if (!initialized) {
            return;
        }
        Date startTime = DateUtil.dateAddMinutes(lastTemplateUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastTemplateUpdateTime, 60);
        List<Integer> equipmentTemplateIdList = configChangeMacroLogMapper.findEquipmentTemplateChangeLogByUpdateTime(startTime, endTime);
        if (new Date().after(DateUtil.dateAddMinutes(lastTemplateUpdateTime, 60))) {
            lastTemplateUpdateTime = new Date();
        }
        //无变更不处理
        if (CollUtil.isEmpty(equipmentTemplateIdList)) {
            return;
        }
        refreshEquipmentByEquipmentTemplateId(new HashSet<>(equipmentTemplateIdList));
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<HAStatusChanged> event) {
        //如果未启用双机高可用部署，则直接退出
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = event.getData();
        log.info("equipmentCache reload,receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        //SiteWeb双机部署模式下，接收到HAStatusChanged事件后，重新初始化告警缓存
        initialized = false;
        init();
        Equipment equipment = new Equipment();
        BaseSpringEvent<Equipment> equipmentCacheFinishEvent = BaseSpringEvent.of(equipment);
        applicationEventPublisher.publishEvent(equipmentCacheFinishEvent);
    }

    public void deleteById(Integer id) {
        EQUIPMENT_CONCURRENT_HASH_MAP.remove(id);
    }

    /**
     * 根据设备ID获取设备
     *
     * @param equipmentId
     * @return
     */
    public Equipment getEquipmentById(Integer equipmentId) {
        if (ObjectUtil.isNull(equipmentId))
            return null;
        return EQUIPMENT_CONCURRENT_HASH_MAP.get(equipmentId);
    }

    /**
     * 根据设备ID集合获取设备
     */
    public List<Equipment> getEquipmentByIds(Collection<Integer> equipmentIds) {
        List<Equipment> result = new ArrayList<>();
        // 遍历每个 List，将所有的 key 合并成一个集合
        for (Integer key : equipmentIds) {
            // 从 Map 中获取值，可能为空
            Equipment equipment = EQUIPMENT_CONCURRENT_HASH_MAP.get(key);
            if (equipment != null) {
                result.add(equipment);
            }
        }
        return result;
    }

    /**
     * 返回所有设备列表
     *
     * @return
     */
    public List<Equipment> getAllEquipments() {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .sorted(Comparator.comparing(Equipment::getDisplayIndex).thenComparing(Equipment::getEquipmentName))
                .toList();
    }

    /**
     * 根据基类类型返回设备列表
     *
     * @param equipmentBaseType 基类设备类型
     * @return
     */
    public List<Equipment> getEquipmentsByEquipmentBaseType(Integer equipmentBaseType) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> Objects.equals(o.getEquipmentBaseType(), equipmentBaseType))
                .toList();
    }

    /**
     * 根据设备模板id返回设备列表
     *
     * @param equipmentTemplateId 设备模板id
     * @return
     */
    public List<Equipment> getEquipmentsByTemplateId(Set<Integer> equipmentTemplateId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> equipmentTemplateId.contains(o.getEquipmentTemplateId()))
                .toList();
    }

    /**
     * 根据设备名过滤
     *
     * @param equipmentName 基类设备类型
     * @return
     */
    public Equipment getEquipmentsByEquipmentName(String equipmentName) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> o.getEquipmentName()
                        .equals(equipmentName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据监控单元id过滤设备数据
     * @param monitorUnitId
     * @return
     */
    public List<Equipment> getEquipmentsByMonitorUnitId(Integer monitorUnitId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> o.getMonitorUnitId()
                        .equals(monitorUnitId))
                .toList();
    }

    public Equipment getEquipmentBySamplerUnitId(Integer samplerUnitId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> o.getSamplerUnitId()
                        .equals(samplerUnitId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据设备dto过滤设备数据
     *
     * @param filterVo 设备dto (传null为查询全部)
     * @return 过滤完后的设备集合
     */
    public List<Equipment> getEquipmentsByEquipmentDto(EquipmentFilterVo filterVo) {
        Stream<Equipment> equipmentDtoStream = EQUIPMENT_CONCURRENT_HASH_MAP.values().stream();
        //设备id过滤
        if (CollUtil.isNotEmpty(filterVo.getEquipmentIds())) {
            equipmentDtoStream = equipmentDtoStream.filter(equipment -> filterVo.getEquipmentIds().contains(equipment.getEquipmentId()));
        }
        //层级id过滤
        if (CollUtil.isNotEmpty(filterVo.getRIds())) {
            equipmentDtoStream = equipmentDtoStream.filter(equipment -> filterVo.getRIds().contains(equipment.getResourceStructureId()));
        }
        //设备名过滤
        if (CharSequenceUtil.isNotBlank(filterVo.getEqName())) {
            equipmentDtoStream = equipmentDtoStream.filter(equipment -> CharSequenceUtil.contains(equipment.getEquipmentName(), filterVo.getEqName()));
        }
        //设备基类
        if (ObjectUtil.isNotNull(filterVo.getEqCategory())) {
            equipmentDtoStream = equipmentDtoStream.filter(equipment -> Objects.equals(equipment.getEquipmentBaseType(), filterVo.getEqCategory()));
        }
        return equipmentDtoStream.sorted(Comparator.comparing(Equipment::getDisplayIndex).thenComparing(Equipment::getEquipmentName))
                .toList();
    }

    /**
     * 根据资源ID获取设备
     *
     * @param resourceStructureId 层级ID
     * @return 设备列表
     */
    public List<Equipment> findEquipmentsByResourceStructureId(Integer resourceStructureId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> Objects.equals(e.getResourceStructureId(), resourceStructureId)).toList();
    }

    /**
     * 根据层级id和设备名称获取设备
     */
    public Equipment findByResourceIdAndName(Integer resourceStructureId, String equipmentName) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> Objects.equals(o.getResourceStructureId(), resourceStructureId) && o.getEquipmentName().equals(equipmentName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据资源IDS获取设备
     *
     * @param resourceStructureIds 层级IDS
     * @return 设备列表
     */
    public List<Equipment> findEquipmentsByResourceStructureIds(Set<Integer> resourceStructureIds) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> resourceStructureIds.contains(e.getResourceStructureId())).toList();
    }

    /**
     * 根据设备类型获取设备列表
     */
    public List<Equipment> getEquipmentsByCategoryId(Integer categoryId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> Objects.equals(e.getEquipmentBaseType(), categoryId)).toList();
    }

    public List<Equipment> getEquipmentsByEqCategoryId(Integer categoryId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> Objects.equals(e.getEquipmentCategory(), categoryId)).toList();
    }

    /**
     * @param baseTypeIds 根据设备类型集合获取设备列表
     * @return {@link List}<{@link Equipment}>
     */
    public List<Equipment> findEquipmentsByBaseTypeIds(Set<Integer> baseTypeIds) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> baseTypeIds.contains(e.getEquipmentBaseType())).toList();
    }

    public List<Equipment> findEquipsByBaseTypeIds(Set<Integer> baseTypeIds) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> e.getEquipmentBaseType() != null && baseTypeIds.contains(e.getEquipmentBaseType())).toList();
    }

    public String getEquipmentPosition(Integer equipmentId) {
        Equipment equipment = getEquipmentById(equipmentId);
        if (ObjectUtil.isNotNull(equipment) && ObjectUtil.isNotNull(equipment.getResourceStructureId()))
            return resourceStructureManager.getFullPath(equipment.getResourceStructureId());

        return "";
    }

    /**
     * 获取使用指定设备模板的设备数量
     *
     * @param equipmentTemplateId 设备模板id
     * @return int
     */
    public long getEquipmentTemplateCount(Integer equipmentTemplateId) {
        return EQUIPMENT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(e -> Objects.equals(e.getEquipmentTemplateId(), equipmentTemplateId))
                .count();
    }

    public List<Equipment> getEquipmentByStationId(Integer stationId) {
        return getEquipmentByStationId(Collections.singletonList(stationId));
    }

    /**
     * 根据局站获取设备
     *
     * @param stationsIds 局站id集合
     * @return 设备列表
     */
    public List<Equipment> getEquipmentByStationId(Collection<Integer> stationsIds) {
        Set<Integer> stationIdSet = new HashSet<>(stationsIds);
        return EQUIPMENT_CONCURRENT_HASH_MAP.values().stream().filter(e -> stationIdSet.contains(e.getStationId())).toList();
    }


    /**
     * 查找局站下的设备
     *
     * @param stationName   设备名称
     * @param equipmentName 局站名称
     * @return {@link Boolean}
     */
    public Equipment findEquipmentByStationNameAndEquipmentName(String stationName, String equipmentName) {
        Station station = stationManager.findStationByName(stationName);
        if (ObjectUtil.isNull(station)) {
            return null;
        }
        return this.getAllEquipments()
                .stream()
                .filter(e -> Objects.equals(e.getStationId(), station.getStationId()))
                .filter(e -> Objects.equals(e.getEquipmentName(), equipmentName))
                .findFirst()
                .orElse(null);
    }
}
