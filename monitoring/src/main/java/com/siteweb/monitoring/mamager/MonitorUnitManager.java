package com.siteweb.monitoring.mamager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.report.SecurityAuditManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.MonitorUnitDTO;
import com.siteweb.monitoring.entity.MonitorUnit;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.RedisOnlineStateEnum;
import com.siteweb.monitoring.mapper.MonitorUnitMapper;
import com.siteweb.monitoring.vo.MonitorUnitRegisterVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MonitorUnitManager {
    private final Logger log = LoggerFactory.getLogger(MonitorUnitManager.class);

    @Autowired
    MonitorUnitMapper monitorUnitMapper;

    @Autowired
    StationManager stationManager;
    @Autowired
    SecurityAuditManager securityAuditManager;
    @Autowired
    ConnectStateManager connectStateManager;

    /**
     * 设备状态对象队列
     */
    private ConcurrentHashMap<Integer, MonitorUnit> MONITORUNIT_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    private boolean initialized = false;

    @PostConstruct
    public void init() {
        List<MonitorUnit> monitorUnits = loadMonitorUnitsFromDB();
        initialized = true;
        log.info("Init equipment state from database, total {}", monitorUnits.size());
    }

    @Scheduled(fixedDelay = 60 * 1000)
    private List<MonitorUnit> loadMonitorUnitsFromDB() {
        MONITORUNIT_CONCURRENT_HASH_MAP.clear();
        List<MonitorUnit> monitorUnits = monitorUnitMapper.getAllMonitorUnit();
        Map<Integer, OnlineState> monitorUnitConnectStateByIds = connectStateManager.findConnectStateByIds(monitorUnits.stream()
                                                                                                                           .map(MonitorUnit::getMonitorUnitId)
                                                                                                                           .toList(), RedisOnlineStateEnum.MONITOR_UNIT);
        for (MonitorUnit monitorUnit : monitorUnits) {
            //redis中从在值才从redis中取，否则依旧从数据库中取，避免与dataHub版本不一致
            if (monitorUnitConnectStateByIds.containsKey(monitorUnit.getMonitorUnitId())) {
                monitorUnit.setConnectState(monitorUnitConnectStateByIds.get(monitorUnit.getMonitorUnitId()).value());
            }
            MONITORUNIT_CONCURRENT_HASH_MAP.put(monitorUnit.getMonitorUnitId(), monitorUnit);
        }
        return monitorUnits;
    }

    public MonitorUnitDTO getById(Integer monitorUnitId){
        MonitorUnit monitorUnit =  MONITORUNIT_CONCURRENT_HASH_MAP.get(monitorUnitId);
        if(monitorUnit != null) {
            return new MonitorUnitDTO(monitorUnit);

        }
        return  null;
    }

    public List<MonitorUnitDTO> getALL(){
        List<MonitorUnitDTO> result = new ArrayList<>();
        for(MonitorUnit monitorUnit: MONITORUNIT_CONCURRENT_HASH_MAP.values()){
            MonitorUnitDTO monitorUnitDTO = new MonitorUnitDTO(monitorUnit);
            result.add(monitorUnitDTO);
        }
        return  result;
    }

    public List<MonitorUnitDTO> getByIds(List<Integer> muIds){
        List<MonitorUnitDTO> result = new ArrayList<>();
        for(Integer muId: muIds){
            MonitorUnitDTO dto = getById(muId);
            if(ObjectUtil.isNotNull(dto))
                result.add(dto);
        }
        return  result;
    }

    /**
     * 根据局站Id获取
     * @param stationId
     * @return
     */
    public List<MonitorUnitDTO> getByStationId(Integer stationId){
        List<MonitorUnitDTO> result = new ArrayList<>();
        List<MonitorUnit>  res = MONITORUNIT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(monitorUnit -> ObjectUtil.equals(monitorUnit.getStationId(), stationId)).toList();
        for(MonitorUnit monitorUnit: res){
            MonitorUnitDTO monitorUnitDTO = new MonitorUnitDTO(monitorUnit);
            result.add(monitorUnitDTO);
        }
        return  result;
    }

    public List<MonitorUnit> findByStationId(Integer stationId) {
        return MONITORUNIT_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(monitorUnit -> ObjectUtil.equals(monitorUnit.getStationId(), stationId)).toList();
    }

    public MonitorUnit findMonitorUnit(String stationName, String monitorUnitName) {
        Station station = stationManager.findStationByName(stationName);
        if (ObjectUtil.isNull(station)) {
            return null;
        }
        return MONITORUNIT_CONCURRENT_HASH_MAP.values()
                                              .stream()
                                              .filter(monitorUnit -> ObjectUtil.equals(monitorUnit.getStationId(), station.getStationId()))
                                              .filter(monitorUnit -> ObjectUtil.equals(monitorUnit.getMonitorUnitName(), monitorUnitName))
                                              .findFirst()
                                              .orElse(null);
    }

    public Integer registerMonitorUnit(MonitorUnitRegisterVO monitorUnitRegisterVO){
        MonitorUnit monitorUnit = MONITORUNIT_CONCURRENT_HASH_MAP.get(monitorUnitRegisterVO.getMonitorUnitId());
        if(ObjectUtil.isNull(monitorUnit)){
            return 0;
        }
        monitorUnit.setHeartBeatTime(new Date());
        if(monitorUnit.getConnectState() != 1) {
            monitorUnit.setConnectState(1);
            securityAuditManager.recordAuditReport(AuditReportTypeEnum.SAMPLER_ACCESS, monitorUnit.getMonitorUnitName() + " 于" + DateUtil.dateToString(new Date()) + " 接入");
        }

        if(ObjectUtil.isNull(monitorUnit)){
            return  0;
        }
        if(monitorUnit.getConfigFileCode().equals(monitorUnitRegisterVO.getProtocolCode())){
            return  1;
        } else {
            return 2;
        }
    }
}
