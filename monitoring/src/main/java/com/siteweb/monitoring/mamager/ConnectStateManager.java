package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.RedisOnlineStateEnum;
import com.siteweb.monitoring.model.OnlineStateModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线状态管理器
 * 负责从Redis中获取和管理设备/局站/监控单元的在线状态
 */
@Slf4j
@Component
public class ConnectStateManager {

    private static final OnlineState DEFAULT_STATE = OnlineState.UNREGISTER;

    @Value("${getRedisConnectState:false}")
    private boolean redisEnabled;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 查询指定ID列表的在线状态
     *
     * @param ids                 需要查询的ID列表
     * @param redisOnlineStateEnum 在线状态类型枚举
     * @return ID到在线状态的映射，若未启用Redis或未找到状态则返回UNREGISTER状态
     */
    public Map<Integer, OnlineState> findConnectStateByIds(List<Integer> ids, RedisOnlineStateEnum redisOnlineStateEnum) {
        if (!shouldQueryRedis(ids)) {
            return Collections.emptyMap();
        }

        Map<Integer, OnlineState> redisStates = queryRedisOnlineStates(ids, redisOnlineStateEnum.getPrefix());
        return ensureDefaultStates(ids, redisStates);
    }

    /**
     * 判断是否应该查询Redis
     */
    private boolean shouldQueryRedis(List<Integer> ids) {
        return redisEnabled && CollUtil.isNotEmpty(ids);
    }

    /**
     * 查询Redis中的在线状态
     */
    private Map<Integer, OnlineState> queryRedisOnlineStates(List<Integer> ids, String dataPrefix) {
        // 构建Redis键列表
        List<String> redisKeys = buildRedisKeys(ids, dataPrefix);

        // 批量查询Redis并解析结果
        return ListUtil.partition(redisKeys, GlobalConstants.REDIS_MAX_OPERATION_COUNT)
                       .stream()
                       .map(redisUtil::mget)
                       .flatMap(List::stream)
                       .map(OnlineStateModel::parseFromStr)
                       .filter(Objects::nonNull)
                       .collect(Collectors.toMap(
                               OnlineStateModel::getId,
                               OnlineStateModel::getOnlineState,
                               (existing, replacement) -> existing
                       ));
    }

    /**
     * 构建Redis键列表
     */
    private List<String> buildRedisKeys(List<Integer> ids, String dataPrefix) {
        return ids.stream()
                  .map(id -> dataPrefix + id)
                  .toList();
    }

    /**
     * 确保所有ID都有对应的状态，没有状态的赋予默认值
     */
    private Map<Integer, OnlineState> ensureDefaultStates(List<Integer> ids, Map<Integer, OnlineState> states) {
        ids.forEach(id -> states.putIfAbsent(id, DEFAULT_STATE));
        return states;
    }
}
