package com.siteweb.monitoring.mamager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.mapper.EquipmentBaseTypeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基类设备类型管理类--基类数据字典
 */
@Component
public class EquipmentBaseTypeManager {
    private final Logger log = LoggerFactory.getLogger(EquipmentBaseTypeManager.class);

    @Autowired
    EquipmentBaseTypeMapper equipmentBaseTypeMapper;

    private static final ConcurrentHashMap<Integer, EquipmentBaseType> EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        loadEquipmentBaseTypeFromDB();
        log.info("Init equipment base type  from database," );
    }

    private void  loadEquipmentBaseTypeFromDB() {
        EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP.clear();
        List<EquipmentBaseType> equipmentList = equipmentBaseTypeMapper.selectList(null);
        for (EquipmentBaseType equipmentBaseType : equipmentList) {
            EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP.put(equipmentBaseType.getBaseEquipmentId(), equipmentBaseType);
        }
    }

    /**
     * 获取基类设备名
     * @param baseEquipmentId
     * @return
     */
    public EquipmentBaseType getEquipmentBaseTypeFromCache(Integer baseEquipmentId) {
        if(ObjectUtil.isNull(baseEquipmentId)){
            return  null;
        }
        if(EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP.containsKey(baseEquipmentId)) {
            return EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP.get(baseEquipmentId);
        }
        EquipmentBaseType equipmentBaseType = equipmentBaseTypeMapper.selectById(baseEquipmentId);
        if(equipmentBaseType != null) {
            EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP.put(baseEquipmentId, equipmentBaseType);
        }
        return  equipmentBaseType;
    }

    public EquipmentBaseType getBaseEquipmentByName(String baseEquipmentName) {
        return  EQUIPMENTBASETYPE_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(o -> o.getBaseEquipmentName()
                        .equals(baseEquipmentName))
                .findFirst()
                .orElse(null);
    }
}
