package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.StationConditionFilterDTO;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.RedisOnlineStateEnum;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.mapper.StationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class StationManager {
    @Autowired
    private StationMapper stationMapper;
    private static final ConcurrentHashMap<Integer, Station> STATION_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    private Date lastUpdateTime;
    private boolean initialized = false;
    /**
     * 是否启用从redis中获取连接状态
     */
    @Value("${getRedisConnectState:false}")
    private boolean redisConnectionEnable;
    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;
    @Autowired
    ConnectStateManager connectStateManager;
    @PostConstruct
    public void init() {
        loadStationsFromDB();
        initialized = true;
    }

    private void loadStationsFromDB() {
        STATION_CONCURRENT_HASH_MAP.clear();
        List<Station> stations = stationMapper.selectList(Wrappers.emptyWrapper());
        for (Station station : stations) {
            STATION_CONCURRENT_HASH_MAP.put(station.getStationId(), station);
        }
        lastUpdateTime = new Date();
    }

    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncStation() {
        if (!initialized) {
            return;
        }
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        List<Integer> configChangeMacroLogs = configChangeMacroLogMapper.findStationChangeLogByUpdateTime(startTime, endTime);
        if (!configChangeMacroLogs.isEmpty()) {
            loadStationsFromDB();
        }
        updateStationConnectState();
    }

    /**
     * 更新局站连接状态
     */
    private void updateStationConnectState() {
        //是否从redis中获取局站在线状态
        if (redisConnectionEnable) {
            Collection<Station> stationList = STATION_CONCURRENT_HASH_MAP.values();
            Map<Integer, OnlineState> stationConnectStateByIds = connectStateManager.findConnectStateByIds(stationList.stream().map(Station::getStationId).toList(), RedisOnlineStateEnum.STATION);
            for (Station station : stationList) {
                //将redis中的局站在线状态放入缓存中
                stationConnectStateByIds.computeIfPresent(station.getStationId(), (k, v) -> {
                    station.setConnectState(v.value());
                    return v;
                });
            }
            return;
        }
        //不从redis中获取在线状态则从数据库获取局站在线状态
        List<Station> stationList = stationMapper.selectList(Wrappers.lambdaQuery(Station.class).select(Station::getStationId, Station::getConnectState));
        stationList.stream().collect(Collectors.toMap(Station::getStationId, Station::getConnectState))
                   .forEach((stationId, stationState) ->
                           STATION_CONCURRENT_HASH_MAP.computeIfPresent(stationId,(k, stationCache)->{stationCache.setConnectState(stationState);return stationCache;
                   }));
    }

    public Station findStationById(Integer stationId) {
        if (ObjectUtil.isNull(stationId)) {
            return null;
        }
        return STATION_CONCURRENT_HASH_MAP.get(stationId);
    }

    public Station findStationByName(String stationName){
        return STATION_CONCURRENT_HASH_MAP.values()
                                          .stream()
                                          .filter(station -> ObjectUtil.equals(station.getStationName(), stationName))
                                          .findFirst()
                                          .orElse(null);
    }
    public void refreshStationByIds(List<Integer> stationIds){
        if (CollUtil.isEmpty(stationIds)) {
            return;
        }
        List<Station> equipmentList = stationMapper.selectBatchIds(stationIds);
        for (Station station : equipmentList) {
            STATION_CONCURRENT_HASH_MAP.put(station.getStationId(), station);
        }
    }

    public List<Station> findByCondition(StationConditionFilterDTO stationConditionFilterDTO) {
        if (ObjectUtil.isNull(stationConditionFilterDTO)) {
            return Collections.emptyList();
        }
        Stream<Station> stationStream = STATION_CONCURRENT_HASH_MAP.values()
                                                                   .stream();
        //局站id过滤
        if (CollUtil.isNotEmpty(stationConditionFilterDTO.getStationIds())) {
            stationStream = stationStream.filter(station -> stationConditionFilterDTO.getStationIds().contains(station.getStationId()));
        }
        //局站名称过滤
        if (CharSequenceUtil.isNotBlank(stationConditionFilterDTO.getStationName())) {
            stationStream = stationStream.filter(station -> CharSequenceUtil.contains(station.getStationName(), stationConditionFilterDTO.getStationName()));
        }
        return stationStream.toList();
    }

    public List<Station> findByStationIds(Collection<Integer> stationIds) {
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptyList();
        }
        List<Station> stationList = new ArrayList<>(stationIds.size());
        for (Integer stationId : stationIds) {
            if (Objects.nonNull(STATION_CONCURRENT_HASH_MAP.get(stationId))) {
                stationList.add(STATION_CONCURRENT_HASH_MAP.get(stationId));
            }
        }
        return stationList;
    }

    public List<Station> findAll() {
        return STATION_CONCURRENT_HASH_MAP.values().stream().toList();
    }

    /**
     * 批量获取局站的在线状态
     *
     * @param stationIds 局站ID列表
     * @return 局站ID与在线状态的映射
     */
    public Map<Integer, Integer> findOnlineStates(List<Integer> stationIds) {
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptyMap();
        }
        return stationIds.stream()
                         .collect(Collectors.toMap(
                                 Function.identity(), // Key: 局站ID
                                 stationId -> Optional.ofNullable(STATION_CONCURRENT_HASH_MAP.get(stationId))
                                                      .map(Station::getConnectState)
                                                      .orElse(OnlineState.UNREGISTER.value()) // Value: 在线状态，默认为未注册
                         ));
    }
}
