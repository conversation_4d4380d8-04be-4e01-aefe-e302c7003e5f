package com.siteweb.monitoring.mamager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.SampleValueFormatUtil;
import com.siteweb.monitoring.dto.ActiveSignal;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.SignalMeanings;
import com.siteweb.monitoring.model.EquipmentSignalMap;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.model.RealTimeSignalKey;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import com.siteweb.monitoring.vo.ActiveSignalRequestBySignalId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实时信号处理类（配置数据+实时数据）
 */
@Component
public class ActiveSignalManager {

    @Autowired
    public ConfigSignalManager configSignalManager;

    @Autowired
    public RealTimeSignalManager realTimeSignalManager;

    @Autowired
    public EquipmentManager equipmentManager;
    @Autowired
    public SignalSubscribeManager signalSubscribeManager;

    private final String  dataPrefix ="RealTimeSignal:";

    /**
     * 根据设备Id获取信号列表
     * @param equipmentId
     * @return 实时信号列表
     */
    public List<SimpleActiveSignal> getActiveSignalsByEquipmentId(Integer equipmentId){

        List<SimpleActiveSignal> result = new ArrayList<>();
        //获取配置
        List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalByEquipmentId(equipmentId);
        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for(ConfigSignalItem configSignalItem:configSignalItems){
            redisKey.add(dataPrefix+equipmentId+ "." + configSignalItem.getSignalId());
        }
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);

        //转map便于查找
        Map<Integer, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getSignalId, p -> p));

        //拼接实时信号
        for(ConfigSignalItem configSignalItem : configSignalItems){
            RealTimeSignalItem realTimeSignalItem = realTimeSignalItemMap.get(configSignalItem.getSignalId());
            SimpleActiveSignal activeSignal =  constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
            result.add(activeSignal);

        }

        return  result;
    }

    /***
     * 根据
     * @param keys redisKeys
     * @return 实时信号列表
     */
    public List<ActiveSignal> getActiveSignalsByKeys(List<RealTimeSignalKey>  keys){

        List<ActiveSignal> result = new ArrayList<>();
        //获取配置
        Map<String,ConfigSignalItem> configSignalItems = new HashMap<>();

        List<String> redisKey = new ArrayList<>();
        for(RealTimeSignalKey realTimeSignalKey:keys){
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(realTimeSignalKey.getEquipmentId(),realTimeSignalKey.getSignalId());
            if(ObjectUtil.isNotNull(configSignalItem)) {
                configSignalItems.put(realTimeSignalKey.getRedisKey(),configSignalItem);
                redisKey.add(realTimeSignalKey.getRedisKey());
            }
        }
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);

        //转map便于查找
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));

        //拼接实时信号
        for(RealTimeSignalKey realTimeSignalKey : keys){
            ConfigSignalItem configSignalItem = configSignalItems.get(realTimeSignalKey.getRedisKey());
            RealTimeSignalItem realTimeSignalItem = realTimeSignalItemMap.get(realTimeSignalKey.getEquipmentId()+"." + realTimeSignalKey.getSignalId());

            if(ObjectUtil.isNotNull(configSignalItem)) {
                ActiveSignal activeSignal = constructActiveSignalByConfigAndRealTimeItem(realTimeSignalKey,configSignalItem, realTimeSignalItem);
                result.add(activeSignal);
            }

        }

        return  result;
    }
    /**
     *  根据设备跟基类信号获取实时信号值
     * @param equipmentId 设备ID
     * @param baseTypeIds 基类ID
     * @return 实时信号列表
     */
    public List<SimpleActiveSignal> getActiveSignalsByEquipmentIdAndBaseTypeId(Integer equipmentId, List<Long> baseTypeIds){
        List<SimpleActiveSignal> result = new ArrayList<>();
        //获取配置
        EquipmentSignalMap  equipmentSignalMap = getConfigSignalItemByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds);
        if(equipmentSignalMap == null){
            return  result;
        }

        //构建redisKey
        List<String> redisKey = equipmentSignalMap.getRedisKey();

        //从redis获取实时值
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<Integer, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getSignalId, p -> p));

        //拼装成实时信号返回值
        for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
            RealTimeSignalItem realTimeSignalItem = null;
            if (realTimeSignalItemMap.containsKey(configSignalItem.getSignalId())) {
                realTimeSignalItem = realTimeSignalItemMap.get(configSignalItem.getSignalId());
            }
            SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
            result.add(activeSignal);
        }

        return  result;
    }

    /**
     *  根据设备跟基类信号获取实时信号值
     * @param equipmentId 设备ID
     * @param signalIds 信号ID
     * @return 实时信号列表
     */
    public List<SimpleActiveSignal> getActiveSignalsByEquipmentIdAndSignalId(Integer equipmentId, List<Integer> signalIds){
        List<SimpleActiveSignal> result = new ArrayList<>();
        //获取配置
        EquipmentSignalMap  equipmentSignalMap = getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalIds);
        if(equipmentSignalMap == null){
            return  result;
        }

        //构建redisKey
        List<String> redisKey = equipmentSignalMap.getRedisKey();

        //从redis获取实时值
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<Integer, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getSignalId, p -> p));

        //拼装成实时信号返回值
        for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
            RealTimeSignalItem realTimeSignalItem = null;
            if (realTimeSignalItemMap.containsKey(configSignalItem.getSignalId())) {
                realTimeSignalItem = realTimeSignalItemMap.get(configSignalItem.getSignalId());
            }
            SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
            result.add(activeSignal);
        }

        return  result;
    }

    /**
     * 跟据设备Id及信号属性Id获取信号列表
     * @param equipmentId 设备Id
     * @param signalPropertyId 属性Id
     * @return 实时信号列表
     */
    public List<SimpleActiveSignal> getEquipmentActiveSignalByProperty(Integer equipmentId, Integer signalPropertyId) {
        List<SimpleActiveSignal> result = new ArrayList<>();
        EquipmentSignalMap equipmentSignalMap = getConfigSignalItemByEquipmentIdAndSignalPropertyId(equipmentId, signalPropertyId);
        if(equipmentSignalMap == null){
            return  result;
        }

        //构建redisKey
        List<String> redisKey = equipmentSignalMap.getRedisKey();

        //从redis获取实时值
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<Integer, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getSignalId, p -> p));

        //拼装成实时信号返回值
        for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
            RealTimeSignalItem realTimeSignalItem = null;
            if (realTimeSignalItemMap.containsKey(configSignalItem.getSignalId())) {
                realTimeSignalItem = realTimeSignalItemMap.get(configSignalItem.getSignalId());
            }
            SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
            result.add(activeSignal);
        }

        return  result;
    }

    /**
     * 根据设备类型，基类ID获取配置， 用于设备类组态
     * @param equipmentCategory 设备基类类型
     * @param baseTypeIds 基类ID
     * @return 实时信号列表
     */
    public List<EquipmentActiveSignal> getActiveSignalsByEquipmentCategoryAndBaseTypeId(Integer equipmentCategory, List<Long> baseTypeIds){
        List<EquipmentActiveSignal> result = new ArrayList<>();

        /* 获取信号配置 */
        List<EquipmentSignalMap> equipmentSignalMaps = getConfigSignalItemByEquipmentCategoryAndBaseTypeId(equipmentCategory, baseTypeIds);

        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for(EquipmentSignalMap equipmentSignalMap:equipmentSignalMaps){
            redisKey.addAll(equipmentSignalMap.getRedisKey());
        }

        /*从 redis获取实时数据 */
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));

        /* 组成显示的实时信号 */
        for(EquipmentSignalMap equipmentSignalMap : equipmentSignalMaps){
            List<SimpleActiveSignal> activeSignals = new ArrayList<>();
            for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
                RealTimeSignalItem realTimeSignalItem = null;
                if (realTimeSignalItemMap.containsKey(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId())) {
                    realTimeSignalItem = realTimeSignalItemMap.get(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId());
                }
                SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
                activeSignals.add(activeSignal);
            }
            EquipmentActiveSignal equipmentActiveSignal = new EquipmentActiveSignal(equipmentSignalMap.getEquipmentId(), equipmentSignalMap.getEquipmentName(), activeSignals);
            result.add(equipmentActiveSignal);

        }
        return  result;

    }

    /**
     * 根据设备ID，信号ID列表获取实时信号，主要用于数字地图，子系统组态
     * @param requestBySignalIds 请求ID
     * @return
     */
    public List<EquipmentActiveSignal> getEquipmentActiveSignalBySignalId(List<ActiveSignalRequestBySignalId> requestBySignalIds){
        List<EquipmentActiveSignal> result = new ArrayList<>();

        List<EquipmentSignalMap> equipmentSignalMaps = new ArrayList<>();
        for (ActiveSignalRequestBySignalId activeSignalRequestBySignalId : requestBySignalIds) {
            Equipment equipment = equipmentManager.getEquipmentById(activeSignalRequestBySignalId.getEquipmentId());
            if (equipment == null) {
                continue;
            }
            List<ConfigSignalItem> cs = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndSignalId(equipment.getEquipmentTemplateId(), activeSignalRequestBySignalId.getSignalIds());
            EquipmentSignalMap equipmentSignalMap = new EquipmentSignalMap(activeSignalRequestBySignalId.getEquipmentId(), equipment.getEquipmentName(),cs);
            equipmentSignalMaps.add(equipmentSignalMap);
        }
        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for (EquipmentSignalMap equipmentSignalMap : equipmentSignalMaps) {
            redisKey.addAll(equipmentSignalMap.getRedisKey());
        }

        /*从 redis获取实时数据 */
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));

        /* 组成显示的实时信号 */
        for(EquipmentSignalMap equipmentSignalMap : equipmentSignalMaps){

            List<SimpleActiveSignal> activeSignals = new ArrayList<>();
            for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
                RealTimeSignalItem realTimeSignalItem =  realTimeSignalItemMap.get(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId());
                SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
                activeSignals.add(activeSignal);
            }
            EquipmentActiveSignal equipmentActiveSignal = new EquipmentActiveSignal(equipmentSignalMap.getEquipmentId(), equipmentSignalMap.getEquipmentName(), activeSignals);
            result.add(equipmentActiveSignal);
          }

        return  result;
    }

    /**
     *根据设备ID，基类ID列表获取实时信号，主要用于设备类组态
     * @param requestByBaseTypeIds
     * @return
     */
    public List<EquipmentActiveSignal> getEquipmentActiveSignalByBaseTypeId(List<ActiveSignalRequestByBaseTypeId> requestByBaseTypeIds){
        List<EquipmentActiveSignal> result = new ArrayList<>();

        List<EquipmentSignalMap> equipmentSignalMaps = new ArrayList<>();
        for (ActiveSignalRequestByBaseTypeId activeSignalRequestBySignalId:requestByBaseTypeIds){
            Equipment equipment = equipmentManager.getEquipmentById(activeSignalRequestBySignalId.getEquipmentId());
            if (ObjectUtil.isNull(equipment)) {
                continue;
            }
            List<ConfigSignalItem> cs = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndBaseTypeId(equipment.getEquipmentTemplateId(), activeSignalRequestBySignalId.getBaseTypeId());
            EquipmentSignalMap equipmentSignalMap = new EquipmentSignalMap(activeSignalRequestBySignalId.getEquipmentId(), equipment.getEquipmentName(),cs);
            equipmentSignalMaps.add(equipmentSignalMap);
        }
        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for(EquipmentSignalMap equipmentSignalMap:equipmentSignalMaps){
            redisKey.addAll(equipmentSignalMap.getRedisKey());
        }
        /*从 redis获取实时数据 */
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));

        /* 组成显示的实时信号 */
        for(EquipmentSignalMap equipmentSignalMap : equipmentSignalMaps){
            List<SimpleActiveSignal> activeSignals = new ArrayList<>();
            for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
                RealTimeSignalItem realTimeSignalItem = null;
                if (realTimeSignalItemMap.containsKey(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId())) {
                    realTimeSignalItem = realTimeSignalItemMap.get(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId());
                }
                SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
                activeSignals.add(activeSignal);
            }
            EquipmentActiveSignal equipmentActiveSignal = new EquipmentActiveSignal(equipmentSignalMap.getEquipmentId(), equipmentSignalMap.getEquipmentName(), activeSignals);
            result.add(equipmentActiveSignal);
        }

        return  result;
    }

    /**
     * 获取基站下所有重要信号列表
     * @param stationId 基站Id
     * @param signalPropertyId 属性Id
     * @return 实行信号列表
     */
    public List<EquipmentActiveSignal> getStationImportantActiveSignalByProperty(Integer stationId, Integer signalPropertyId){
        List<EquipmentActiveSignal> result = new ArrayList<>();

        List<EquipmentSignalMap> equipmentSignalMaps = new ArrayList<>();

        List<Equipment> equipments = equipmentManager.getEquipmentByStationId(stationId);
        for (Equipment equipment:equipments){

            List<ConfigSignalItem> cs = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndProperty(equipment.getEquipmentTemplateId(), signalPropertyId);
            if(!cs.isEmpty()) {
                EquipmentSignalMap equipmentSignalMap = new EquipmentSignalMap(equipment.getEquipmentId(), equipment.getEquipmentName(), cs);
                equipmentSignalMaps.add(equipmentSignalMap);
            }
        }
        /* 获取redisKey */
        List<String> redisKey = new ArrayList<>();
        for(EquipmentSignalMap equipmentSignalMap:equipmentSignalMaps){
            redisKey.addAll(equipmentSignalMap.getRedisKey());
        }
        if (redisKey.isEmpty()) {
            return result;
        }
        /*从 redis获取实时数据 */
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));

        /* 组成显示的实时信号 */
        for(EquipmentSignalMap equipmentSignalMap : equipmentSignalMaps){
            List<SimpleActiveSignal> activeSignals = new ArrayList<>();
            for(ConfigSignalItem configSignalItem : equipmentSignalMap.getConfigSignalItems()) {
                RealTimeSignalItem realTimeSignalItem = null;
                if (realTimeSignalItemMap.containsKey(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId())) {
                    realTimeSignalItem = realTimeSignalItemMap.get(equipmentSignalMap.getEquipmentId() + "." + configSignalItem.getSignalId());
                }
                SimpleActiveSignal activeSignal = constructSimpleActiveSignalByConfigAndRealTimeItem(configSignalItem, realTimeSignalItem);
                activeSignals.add(activeSignal);
            }
            EquipmentActiveSignal equipmentActiveSignal = new EquipmentActiveSignal(equipmentSignalMap.getEquipmentId(), equipmentSignalMap.getEquipmentName(), activeSignals);
            result.add(equipmentActiveSignal);
        }
        Set<Integer> equipmentIdSet = result.stream().map(EquipmentActiveSignal::getEquipmentId).collect(Collectors.toSet());
        signalSubscribeManager.sendSignalSubscribe(equipmentIdSet);
        return  result;
    }

    /**
     * 根据设备类型，基类ID获取配置
     * @param equipmentCategory 设备基类类型
     * @param baseTypeIds 基类ID
     * @return
     */
    private List<EquipmentSignalMap> getConfigSignalItemByEquipmentCategoryAndBaseTypeId(Integer equipmentCategory, List<Long> baseTypeIds){
        //从redis获取的键值对ID
        List<EquipmentSignalMap> result = new ArrayList<>();

        //获取根据设备类型获取设备列表
        List<Equipment> equipments = equipmentManager.getEquipmentsByEquipmentBaseType(equipmentCategory);

        //循环获取所有基类信号配置
        for(Equipment equipment:equipments){
            List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndBaseTypeId(equipment.getEquipmentTemplateId(),baseTypeIds);
            EquipmentSignalMap equipmentSignalMap = new EquipmentSignalMap(equipment.getEquipmentId(), equipment.getEquipmentName(),configSignalItems);
            result.add(equipmentSignalMap);
        }
        return  result;
    }

    /**
     * 根据设备ID，基类ID获取配置
     * @param equipmentId 设备ID
     * @param baseTypeIds 基类ID
     * @return
     */
    private EquipmentSignalMap getConfigSignalItemByEquipmentIdAndBaseTypeId(Integer equipmentId, List<Long> baseTypeIds){
        //从redis获取的键值对ID
       EquipmentSignalMap result = null;

        //获取根据设备类型获取设备列表
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment != null) {
            List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndBaseTypeId(equipment.getEquipmentTemplateId(), baseTypeIds);
            if(!configSignalItems.isEmpty())
                result = new EquipmentSignalMap(equipment.getEquipmentId(), equipment.getEquipmentName(), configSignalItems);
        }

        return  result;
    }

    private EquipmentSignalMap getConfigSignalItemByEquipmentIdAndSignalId(Integer equipmentId, List<Integer> signalIds){
        //从redis获取的键值对ID
        EquipmentSignalMap result = null;

        //获取根据设备类型获取设备列表
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment != null) {
            List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndSignalId(equipment.getEquipmentTemplateId(), signalIds);
            if(!configSignalItems.isEmpty())
                result = new EquipmentSignalMap(equipment.getEquipmentId(), equipment.getEquipmentName(), configSignalItems);
        }

        return  result;
    }

    private EquipmentSignalMap getConfigSignalItemByEquipmentIdAndSignalPropertyId(Integer equipmentId, Integer signalPropertyId){
        //从redis获取的键值对ID
        EquipmentSignalMap result = null;

        //获取根据设备类型获取设备列表
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment != null) {
            List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalItemByEquipmentTemplateIdAndProperty(equipment.getEquipmentTemplateId(), signalPropertyId);
            if(!configSignalItems.isEmpty())
                result = new EquipmentSignalMap(equipment.getEquipmentId(), equipment.getEquipmentName(), configSignalItems);
        }

        return  result;
    }

    /**
     *  根据配置信息跟实时信息构建实时信号实体
     * @param configSignalItem
     * @param realTimeSignalItem
     * @return 实时信号列表
     */
    private SimpleActiveSignal constructSimpleActiveSignalByConfigAndRealTimeItem(ConfigSignalItem configSignalItem, RealTimeSignalItem realTimeSignalItem){
        SimpleActiveSignal simpleActiveSignal = new SimpleActiveSignal();
        simpleActiveSignal.setSignalId(configSignalItem.getSignalId());
        simpleActiveSignal.setSignalName(configSignalItem.getSignalName());
        simpleActiveSignal.setSignalCategory(configSignalItem.getSignalCategory());
        simpleActiveSignal.setDisplayIndex(configSignalItem.getDisplayIndex());
        simpleActiveSignal.setBaseTypeId(configSignalItem.getBaseTypeId());
        simpleActiveSignal.setUnit(configSignalItem.getUnit());
        if(realTimeSignalItem != null) {
            simpleActiveSignal.setCurrentState(realTimeSignalItem.getEventLevel());
            simpleActiveSignal.setCurrentValue(getCurrentValue(configSignalItem, realTimeSignalItem.getCurrentValue()));
            simpleActiveSignal.setOriginalValue(realTimeSignalItem.getCurrentValue());
            simpleActiveSignal.setSampleTime(realTimeSignalItem.getSampleTime());
            simpleActiveSignal.setSignalValid(realTimeSignalItem.getSignalValid());
        }
        return simpleActiveSignal;
    }
    private ActiveSignal constructActiveSignalByConfigAndRealTimeItem(RealTimeSignalKey realTimeSignalKey, ConfigSignalItem configSignalItem, RealTimeSignalItem realTimeSignalItem){

        ActiveSignal simpleActiveSignal = new ActiveSignal();
        simpleActiveSignal.setSignalId(configSignalItem.getSignalId());
        simpleActiveSignal.setSignalName(configSignalItem.getSignalName());
        simpleActiveSignal.setSignalCategory(configSignalItem.getSignalCategory());
        simpleActiveSignal.setDisplayIndex(configSignalItem.getDisplayIndex());
        simpleActiveSignal.setBaseTypeId(configSignalItem.getBaseTypeId());
        simpleActiveSignal.setUnit(configSignalItem.getUnit());
        simpleActiveSignal.setEquipmentId(realTimeSignalKey.getEquipmentId());
        if(realTimeSignalItem != null) {
            simpleActiveSignal.setEquipmentId(realTimeSignalItem.getEquipmentId());
            simpleActiveSignal.setCurrentState(realTimeSignalItem.getEventLevel());
            simpleActiveSignal.setCurrentValue(getCurrentValue(configSignalItem, realTimeSignalItem.getCurrentValue()));
            simpleActiveSignal.setOriginalValue(realTimeSignalItem.getCurrentValue());
            simpleActiveSignal.setSampleTime(realTimeSignalItem.getSampleTime());
        }
        return simpleActiveSignal;
    }
    /**
     *  设置信号值
     * @param configSignalItem 配置选项
     * @param originValue 原始值
     * @return 格式化后的值
     */
    public String getCurrentValue(ConfigSignalItem configSignalItem, String originValue){
        if (Objects.isNull(configSignalItem)) {
            return originValue;
        }
        String value = originValue;

        /* 如果是信号量，取含义*/
        if(configSignalItem.getSignalCategory()==2){
            Integer intValue = SampleValueFormatUtil.getIntValue(originValue);
            Optional<SignalMeanings> signalMeanings= configSignalItem.getMeaningsList().stream()
                    .filter(meaning -> ObjectUtil.isNotNull(meaning.getStateValue()) && meaning.getStateValue().equals(intValue)).findFirst();
            if (signalMeanings.isPresent()) {
                //调用get()返回Optional值。
                value = signalMeanings.get().getMeanings();
            }
        }
        else if(configSignalItem.getSignalCategory() ==1){
            if(ObjectUtil.isNull( configSignalItem.getUnit())){
                value = SampleValueFormatUtil.getFormatValue(configSignalItem.getShowPrecision(), value);
            } else {
                value = SampleValueFormatUtil.getFormatValue(configSignalItem.getShowPrecision(), value) + configSignalItem.getUnit();
            }
        }
        return  value;
    }

}
