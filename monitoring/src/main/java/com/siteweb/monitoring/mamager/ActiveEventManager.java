package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.common.util.SortUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.dto.ActiveEventInstructionDTO;
import com.siteweb.monitoring.dto.EquipmentAlarmStateDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.StandardDicEvent;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mapper.ActiveEventMapper;
import com.siteweb.monitoring.mapper.ActiveEventOperationLogMapper;
import com.siteweb.monitoring.mapper.AlarmChangeMapper;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.monitoring.vo.ActiveEventReportFilterVO;
import com.siteweb.utility.constans.StandardCategoryEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.StandardVerService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.CompareToBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhou
 * @description ActiveEventManager
 * @createTime 2022-02-14 16:41:55
 */
@Component
@Slf4j
public class ActiveEventManager implements ApplicationListener<BaseSpringEvent<HAStatusChanged>> {

    /**
     * 告警变更记录的redis发布订阅通道
     */
    private static final String ALARM_CHANGE_CHANNEL = "alarmChange";
    /**
     * 重试告警同步的次数  最大重试三次
     */
    private int retrySyncActiveEventCount = 0;
    @Autowired
    AlarmChangeMapper alarmChangeMapper;

    @Autowired
    ActiveEventMapper activeEventMapper;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    ActiveEventOperationLogMapper activeEventOperationLogMapper;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    RegionService regionService;

    @Autowired
    RegionMapService regionMapService;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    StandardVerService standardVerService;

    @Autowired
    StandardDicEventManager standardDicEventManager;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    ConfigEventManager configEventManager;

    @Autowired
    HAStatusService haStatusService;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    private Boolean bytedanceEnable;

    private static final ConcurrentHashMap<String, ActiveEvent> ACTIVE_EVENT_HASH_MAP = new ConcurrentHashMap<>();
    private boolean initialized = false;
    private long maxSerialNo = 0;

    @PostConstruct
    public void init() {
        maxSerialNo = Math.max(alarmChangeMapper.getMaxSerialNo(), maxSerialNo);
        List<ActiveEvent> activeEvents;
        if (Boolean.TRUE.equals(bytedanceEnable)) {
            // 字节开关打开，要用开启状态的告警等级对数据进行过滤
            activeEvents = activeEventMapper.getAllActiveEventByEventLevel();
        } else {
            activeEvents = activeEventMapper.getAllActiveEvent();
        }
        ACTIVE_EVENT_HASH_MAP.clear();
        for (ActiveEvent activeEvent : activeEvents) {
            ACTIVE_EVENT_HASH_MAP.put(activeEvent.getSequenceId(), activeEvent);
        }
        log.info("Init activeEvent from database, event total {}, max serialNo is {}", activeEvents.size(), maxSerialNo);
        initialized = true;
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<HAStatusChanged> event) {
        //如果未启用双机高可用部署，则直接退出
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = event.getData();
        log.info("receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        //SiteWeb双机部署模式下，接收到HAStatusChanged事件后，重新初始化告警缓存
        initialized = false;
        init();
    }

    @Scheduled(fixedDelay = 2 * 1000) // every 2 seconds
    protected void syncActiveEvent() {
        if (!initialized) {
            return;
        }
        List<AlarmChange> alarmChanges = alarmChangeMapper.readAlarmChange(maxSerialNo);
        if (isRetryNeeded(alarmChanges)) {
            log.info("alarmChange中serialNo有跳跃，重试次数：{}", retrySyncActiveEventCount);
            return;
        }
        if (CollUtil.isEmpty(alarmChanges)) {
            return;
        }
        // 字节开关打开，要用开启状态的告警等级对数据进行过滤
        boolean isEnableByteDanceSwitch = Boolean.TRUE.equals(bytedanceEnable);
        Set<Integer> eventLevels = alarmChangeMapper.getFilterEventLevel();
        log.info("Refresh alarmChange from database, currentSerialNo:{}, total records:{}", maxSerialNo, alarmChanges.size());
        for (AlarmChange alarmChange : alarmChanges) {
            //开启字节开关 并且不包含在启用告警之中，直接更新最大流水号并跳过
            if (isEnableByteDanceSwitch && !eventLevels.contains(alarmChange.getEventLevel())) {
                maxSerialNo = Math.max(alarmChange.getSerialNo(), maxSerialNo);
                log.info("None of the alarm levels meet the criteria. maxSerialNo is {}", this.maxSerialNo);
                continue;
            }
            try {
                updateHashMapByAlarmChange(alarmChange);
            } catch (Exception e) {
                log.error("update alarmChange failed", e);
                log.error("update alarmChange failed{}", alarmChange);
            }
            maxSerialNo = Math.max(alarmChange.getSerialNo(), maxSerialNo);
        }
    }

    /**
     * 是否需要重试
     *
     * @param alarmChanges 查询到的告警变化数量
     * @return boolean true 是  false 否
     */
    private boolean isRetryNeeded(List<AlarmChange> alarmChanges) {
        if (CollUtil.isEmpty(alarmChanges)) {
            retrySyncActiveEventCount = 0;
            return false;
        }
        //重试机制 如果有未提交的事务或者底端报错导致serialNo不连续则最多重试三次
        long min = alarmChanges.get(0).getSerialNo();
        long max = alarmChanges.get(alarmChanges.size() - 1).getSerialNo();
        //最大重试次数
        int retryMaxCount = 3;
        if ((maxSerialNo + 1 != min || max - min + 1 != alarmChanges.size()) && retryMaxCount > retrySyncActiveEventCount) {
            //上一次的最大值+1不等于本次的最小值 或者 数量不相等则说明刚开始不连续或者是中间不连续
            //并且重试次数小数3  则重试最大三次 (定时任务每两秒一次，3次则最大6秒)
            retrySyncActiveEventCount++;
            return true;
        }
        retrySyncActiveEventCount = 0;
        return false;
    }

    private void updateHashMapByAlarmChange(AlarmChange alarmChange) {
        log.info("Read alarmChange from database, {}", alarmChange);
        if (alarmChange.getOperationType().equals(1)) {//Start
            addActiveEvent(alarmChange);
        } else if (alarmChange.getOperationType().equals(2)) {//End
            if (alarmChange.getConfirmTime() == null) {
                updateActiveEvent(alarmChange);
            } else {
                deleteActiveEvent(alarmChange.getSequenceId());
            }
        } else if (alarmChange.getOperationType().equals(3)) {//Confirm
            if (alarmChange.getEndTime() == null) {
                updateActiveEvent(alarmChange);
            } else {
                deleteActiveEvent(alarmChange.getSequenceId());
            }
        } else {
            updateActiveEvent(alarmChange);
        }
        //publish alarmChange event
        BaseSpringEvent<AlarmChange> alarmChangeBaseSpringEvent = BaseSpringEvent.of(alarmChange);
        applicationEventPublisher.publishEvent(alarmChangeBaseSpringEvent);
        stringRedisTemplate.convertAndSend(ALARM_CHANGE_CHANNEL, JSONUtil.toJsonStr(alarmChange));
    }

    private void addActiveEvent(AlarmChange alarmChange) {
        log.info("AddOrUpdate activeEvent, {}", alarmChange);
        ActiveEvent activeEvent = alarmChange.toActiveEvent(); //如果开始告警有收敛，则保留收敛Id  w91251
        ACTIVE_EVENT_HASH_MAP.put(alarmChange.getSequenceId(), activeEvent);
        //publish activeEvent event
        BaseSpringEvent<ActiveEvent> activeEventBaseSpringEvent = new BaseSpringEvent<>(activeEvent);
        activeEventBaseSpringEvent.setData(activeEvent);
        applicationEventPublisher.publishEvent(activeEventBaseSpringEvent);
    }

    private void updateActiveEvent(AlarmChange alarmChange) {
        if (!ACTIVE_EVENT_HASH_MAP.containsKey(alarmChange.getSequenceId())) {
            return;
        }
        log.info("UpdateActiveEvent activeEvent, {}", alarmChange);
        ActiveEvent oldActiveEvent = ACTIVE_EVENT_HASH_MAP.get(alarmChange.getSequenceId());
        ActiveEvent activeEvent = alarmChange.toActiveEvent(); //如果开始告警有收敛，则保留收敛Id  w91251
        if (ObjectUtil.isNotNull(oldActiveEvent)) {
            activeEvent.setConvergenceEventId(oldActiveEvent.getConvergenceEventId());
        }
        ACTIVE_EVENT_HASH_MAP.put(alarmChange.getSequenceId(), activeEvent);
        //publish activeEvent event
        BaseSpringEvent<ActiveEvent> activeEventBaseSpringEvent = new BaseSpringEvent<>(activeEvent);
        activeEventBaseSpringEvent.setData(activeEvent);
        applicationEventPublisher.publishEvent(activeEventBaseSpringEvent);
    }

    private void deleteActiveEvent(String sequenceId) {
        ActiveEvent activeEvent = ACTIVE_EVENT_HASH_MAP.get(sequenceId);
        if (null == activeEvent) {
            return;
        }
        log.info("Delete activeEvent, {}", activeEvent);
        ACTIVE_EVENT_HASH_MAP.remove(sequenceId);
        //publish activeEvent
        BaseSpringEvent<ActiveEvent> activeEventBaseSpringEvent = new BaseSpringEvent<>(activeEvent);
        activeEventBaseSpringEvent.setData(activeEvent);
        applicationEventPublisher.publishEvent(activeEventBaseSpringEvent);
    }
    /**
     * 清理缓存
     * 如果告警已经不存在，则直接清理缓存!!!
     * @param sequenceIds 流水号id
     */
    public void clearCache(List<String> sequenceIds) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return;
        }

        // 数据库中存在的与缓存中存在的进行对比
        List<ActiveEvent> activeEvents = activeEventMapper.selectList(Wrappers.lambdaQuery(ActiveEvent.class)
                                                                              .select(ActiveEvent::getSequenceId)
                                                                              .in(ActiveEvent::getSequenceId, sequenceIds));

        Set<String> existingSequenceIds = activeEvents.stream()
                                                      .map(ActiveEvent::getSequenceId)
                                                      .collect(Collectors.toSet());

        // 检查是否有缺失的 ID
        Set<String> missingSequenceIds = new HashSet<>(sequenceIds);
        missingSequenceIds.removeAll(existingSequenceIds);

        // 删除缺失的活动事件
        missingSequenceIds.forEach(this::deleteActiveEvent);
    }


    public List<ActiveEvent> queryAllActiveEvents() {
        return ACTIVE_EVENT_HASH_MAP.values().stream().toList();
    }

    public List<ActiveEventDTO> queryUnConfirmActiveEvents(Integer userId) {
        List<ActiveEventDTO> dtoList = new ArrayList<>();
        List<Integer> alarmSeverityIds = getBatchAlarmWindowSeverityIds();
        List<ActiveEvent> activeEvents = queryAllActiveEvents();
        activeEvents = activeEvents.stream().filter(o -> alarmSeverityIds.contains(o.getEventLevel()) && o.getConfirmTime() == null).toList();
        activeEvents = queryActiveEventsByUserId(activeEvents, userId);
        for (ActiveEvent activeEvent : activeEvents) {
            ActiveEventDTO dto = new ActiveEventDTO();
            dto = dto.from(activeEvent);
            if (activeEvent.getResourceStructureId() != null) {
                dto.setEquipmentPosition(resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
            }
            dtoList.add(dto);
        }
        return dtoList;
    }

    public List<ActiveEventDTO> queryUnEndActiveEvents(Integer userId) {
        List<ActiveEventDTO> dtoList = new ArrayList<>();
        List<Integer> alarmSeverityIds = getBatchAlarmWindowSeverityIds();
        List<ActiveEvent> activeEvents = queryAllActiveEvents();
        activeEvents = activeEvents.stream().filter(o -> alarmSeverityIds.contains(o.getEventLevel()) && o.getEndTime() == null).toList();
        activeEvents = queryActiveEventsByUserId(activeEvents, userId);
        for (ActiveEvent activeEvent : activeEvents) {
            ActiveEventDTO dto = new ActiveEventDTO();
            dto = dto.from(activeEvent);
            if (activeEvent.getResourceStructureId() != null) {
                dto.setEquipmentPosition(resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
            }
            dtoList.add(dto);
        }
        return dtoList;
    }

    private List<Integer> getBatchAlarmWindowSeverityIds() {
        String alarmSeverityIds = "1,2,3,4";
        SystemConfig alarmSeveritySystemConfig = systemConfigService.findBySystemConfigKey("batchAlarmWindow.alarm.severity");
        if (alarmSeveritySystemConfig != null && alarmSeveritySystemConfig.getSystemConfigValue() != null) {
            alarmSeverityIds = alarmSeveritySystemConfig.getSystemConfigValue();
        }
        return Arrays.stream(alarmSeverityIds.split(",")).map(Integer::parseInt).toList();
    }

    public Page<ActiveEventDTO> queryActiveEvents(int userId, Pageable pageable, ActiveEventFilterVO activeEventFilterVO) {
        List<ActiveEventDTO> dtoList = new ArrayList<>();
        List<ActiveEvent> activeEvents = queryActiveEvents(userId, activeEventFilterVO);
        if (activeEvents.isEmpty()) {
            return new PageImpl<>(dtoList, pageable, 0);
        }
        if (pageable.getSort().iterator().hasNext()) {
            sort(pageable.getSort(), activeEvents);
        }
        List<ActiveEvent> slice = activeEvents.stream()
                .skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        int standardVer = standardVerService.getStandardVer();
        Map<Integer, String> positionMap = new HashMap<>();
        for (ActiveEvent activeEvent : slice) {
            ActiveEventDTO dto = new ActiveEventDTO();
            dto = dto.from(activeEvent);
            if (activeEvent.getResourceStructureId() != null) {
                String equipmentPosition = positionMap.computeIfAbsent(activeEvent.getResourceStructureId(), key -> resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
                dto.setEquipmentPosition(equipmentPosition);
            }
            //组装标准化相关字段
            if (dto.getStandardAlarmNameId() != null) {
                constructStandardInfo(standardVer, dto);
            }
            //备注数先不赋值，以下代码影响查询性能
            //dto.setRemarkCount(activeEventOperationLogMapper.getCountBySequenceId(activeEvent.getSequenceId()));
            //添加告警触发值字段
            EventConditionDTO eventConditionDTO = configEventManager.findConditionByEquipmentIdAndEventId(dto.getEquipmentId(), dto.getEventId(), dto.getEventConditionId());
            ActiveEventDTO finalDto = dto;
            Optional.ofNullable(eventConditionDTO).ifPresent(condition -> finalDto.setStartCompareValue(condition.getStartCompareValue()));
            dtoList.add(dto);
        }
        return new PageImpl<>(dtoList, pageable, activeEvents.size());
    }

    public List<ActiveEvent> sort(Sort sort, List<ActiveEvent> list) {
        Collections.sort(list, (ActiveEvent activeEventA, ActiveEvent activeEventB) -> {
            CompareToBuilder compToBuild = new CompareToBuilder();
            sort.stream().forEachOrdered(sc -> {
                switch (sc.getProperty()) {
                    case "startTime":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getStartTime(), activeEventB.getStartTime());
                        } else {
                            compToBuild.append(activeEventB.getStartTime(), activeEventA.getStartTime());
                        }
                        break;
                    case "eventLevel":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEventLevel(), activeEventB.getEventLevel());
                        } else {
                            compToBuild.append(activeEventB.getEventLevel(), activeEventA.getEventLevel());
                        }
                        break;
                    case "equipmentName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEquipmentName(), activeEventB.getEquipmentName());
                        } else {
                            compToBuild.append(activeEventB.getEquipmentName(), activeEventA.getEquipmentName());
                        }
                        break;
                    case "baseEquipmentName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getBaseEquipmentName(), activeEventB.getBaseEquipmentName());
                        } else {
                            compToBuild.append(activeEventB.getBaseEquipmentName(), activeEventA.getBaseEquipmentName());
                        }
                        break;
                    case "eventName":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEventName(), activeEventB.getEventName());
                        } else {
                            compToBuild.append(activeEventB.getEventName(), activeEventA.getEventName());
                        }
                        break;
                    case "meanings":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getMeanings(), activeEventB.getMeanings());
                        } else {
                            compToBuild.append(activeEventB.getMeanings(), activeEventA.getMeanings());
                        }
                        break;
                    case "eventValue":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEventValue(), activeEventB.getEventValue());
                        } else {
                            compToBuild.append(activeEventB.getEventValue(), activeEventA.getEventValue());
                        }
                        break;
                    case "confirmTime":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getConfirmTime(), activeEventB.getConfirmTime());
                        } else {
                            compToBuild.append(activeEventB.getConfirmTime(), activeEventA.getConfirmTime());
                        }
                        break;
                    case "endTime":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getEndTime(), activeEventB.getEndTime());
                        } else {
                            compToBuild.append(activeEventB.getEndTime(), activeEventA.getEndTime());
                        }
                        break;
                    case "description":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getDescription(), activeEventB.getDescription());
                        } else {
                            compToBuild.append(activeEventB.getDescription(), activeEventA.getDescription());
                        }
                        break;
                    case "reversalNum":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getReversalNum(), activeEventB.getReversalNum());
                        } else {
                            compToBuild.append(activeEventB.getReversalNum(), activeEventA.getReversalNum());
                        }
                    case "maintainState":
                        if (sc.getDirection().isAscending()) {
                            compToBuild.append(activeEventA.getMaintainState(), activeEventB.getMaintainState());
                        } else {
                            compToBuild.append(activeEventB.getMaintainState(), activeEventA.getMaintainState());
                        }
                        break;
                    default:
                        break;
                }
            });
            return compToBuild.toComparison();
        });
        return list;
    }

    private void constructStandardInfo(int standardVer, ActiveEventDTO dto) {
        StandardDicEvent standardDicEvent = standardDicEventManager.getStandardDicEvent(dto.getStandardAlarmNameId(), standardVer);
        if (null != standardDicEvent) {
            dto.setEquipmentLogicCategory(standardDicEvent.getEquipmentLogicClass());
            dto.setAlarmLogicCategory(standardDicEvent.getEventLogicClass());
            if (standardVer == 0) {
                dto.setStandardTypeName(dto.getBaseTypeName());
            } else {
                dto.setStandardTypeName(dto.getStandardAlarmName());
            }
            dto.setStdSignalDescription(standardDicEvent.getExtendFiled1());
            dto.setStdSignalMeanings(standardDicEvent.getMeanings());
            dto.setStdNote(standardDicEvent.getExtendFiled2());
        }
    }

    /**
     * 当前告警报表过滤
     */
    public List<ActiveEvent> reportFilterActiveEvents(int userId, Pageable pageable, ActiveEventReportFilterVO activeEventReportFilterVO, Consumer<PageImpl<ActiveEvent>> activeEventPageFun) {
        List<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values().stream().toList();
        List<ActiveEvent> result = new ArrayList<>(activeEvents);
        // 时间区间
        result = queryActiveEventByEventsInTimeRange(result, activeEventReportFilterVO.getStartDate(), activeEventReportFilterVO.getEndDate());
        // 用户
        result = queryActiveEventsByUserId(result, userId);
        // 设备基类id
        result = queryActiveEventsByBaseEquipmentIds(result, activeEventReportFilterVO.getBaseEquipmentId());
        // 设备类型
        result = queryActiveEventsByEquipmentCategories(result, activeEventReportFilterVO.getEquipmentCategories());
        // 设备id
        result = queryActiveEventsByEquipmentIds(result, activeEventReportFilterVO.getEquipmentIds());
        // 告警等级
        result = queryActiveEventsByEventSeverityIds(result, activeEventReportFilterVO.getEventSeverityIds());
        // 事件
       // result = queryActiveEventsByEvent(result, activeEventReportFilterVO.getEventIds());
        // 事件名
        result = queryActiveEventsByEventName(result, activeEventReportFilterVO.getEventName());
        // 关键字
        result = queryActiveEventsByKeywords(result, activeEventReportFilterVO.getKeyword());
        // 确认人
        result = queryActiveEventsByConfirmerId(result, activeEventReportFilterVO.getConfirmerIds());
        // 注释
        result = queryActiveEventsByDescription(result, activeEventReportFilterVO.getDescription());
        // 告警分类
        result = queryActiveEventsByReasonType(result, activeEventReportFilterVO.getEventReasonTypes());


        if (pageable == null) {
            return result;
        }
        // 排序
        if (pageable.getSort().iterator().hasNext()) {
            result = new SortUtil<ActiveEvent>().sort(pageable.getSort(), result);
        }
        // 分页
        List<ActiveEvent> slice = result.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        PageImpl<ActiveEvent> activeEventPage = new PageImpl<>(slice, pageable, result.size());
        activeEventPageFun.accept(activeEventPage);
        return slice;
    }

    public Map<Integer, Integer> groupActiveEventsBySeverity(int userId, ActiveEventFilterVO activeEventFilterVO) {
        Map<Integer, Integer> hashMap = new HashMap<>();
        List<ActiveEvent> activeEvents = queryActiveEvents(userId, activeEventFilterVO);
        if (activeEvents.isEmpty()) {
            return hashMap;
        }
        boolean standardAlarmNameIdIsNotNull = systemConfigService.standardAlarmNameIdIsNotNull();
        Map<Integer, List<ActiveEvent>> map = activeEvents.stream()
                                                          .filter(e -> !standardAlarmNameIdIsNotNull || Objects.nonNull(e.getStandardAlarmNameId()))
                                                          .collect(Collectors.groupingBy(ActiveEvent::getEventLevel));
        for (Map.Entry<Integer, List<ActiveEvent>> mapEntry : map.entrySet()) {
            hashMap.put(mapEntry.getKey(), mapEntry.getValue().size());
        }
        return hashMap;
    }

    public ActiveEventDTO getActiveEventDTOBySequenceId(String sequenceId) {
        if (!ACTIVE_EVENT_HASH_MAP.containsKey(sequenceId)) {
            return null;
        }
        ActiveEvent activeEvent = ACTIVE_EVENT_HASH_MAP.get(sequenceId);
        ActiveEventDTO dto = new ActiveEventDTO();
        dto = dto.from(activeEvent);
        if (activeEvent.getResourceStructureId() != null) {
            dto.setEquipmentPosition(resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
        }
        dto.setRemarkCount(activeEventOperationLogMapper.getCountBySequenceId(activeEvent.getSequenceId()));
        return dto;
    }

    public List<ActiveEvent> queryActiveEvents(int userId, ActiveEventFilterVO activeEventFilterVO) {
        List<ActiveEvent> result = new ArrayList<>(ACTIVE_EVENT_HASH_MAP.values());
        result = queryActiveEventsByResourceStructureIds(result, activeEventFilterVO.getResourceStructureIds());
        result = queryActiveEventsByEquipmentCategories(result, activeEventFilterVO.getEquipmentCategories());
        result = queryActiveEventsByBaseEquipmentIds(result, activeEventFilterVO.getBaseEquipmentIds());
        result = queryActiveEventsByEquipmentIds(result, activeEventFilterVO.getEquipmentIds());
        result = queryActiveEventsByEventSeverityIds(result, activeEventFilterVO.getEventSeverityIds());
        result = queryActiveEventsByEventConfirmed(result, activeEventFilterVO.getEventConfirmed());
        result = queryActiveEventsByEventEnded(result, activeEventFilterVO.getEventEnded());
        result = queryActiveEventsByUserId(result, userId);
        result = queryActiveEventsByBaseTypeIds(result, activeEventFilterVO.getBaseTypeIds());
        result = queryActiveEventsByKeywords(result, activeEventFilterVO.getKeywords());
        // 告警盯屏页面关键字筛选的列更少
        result = queryActiveEventsByFocusPanelKeywords(result, activeEventFilterVO.getFocusPanelKeywords());
        result = queryActiveEventsByConvergented(result, activeEventFilterVO.getConvergented());
        result = queryActiveEventsByStartTimeFrom(result, activeEventFilterVO.getStartTimeFrom());
        result = queryActiveEventsByStartTimeTo(result, activeEventFilterVO.getStartTimeTo());
        result = queryActiveEventsByMaintainStated(result, activeEventFilterVO.getMaintainStated());
        result = queryActiveEventsByEquipmentEventKeys(result, activeEventFilterVO.getEquipmentEventKeys());
        result = queryActiveEventsByStationIds(result, activeEventFilterVO.getStationIds());
        result = queryActiveEventsByStructureIds(result, activeEventFilterVO.getStructureIds());
        result = queryActiveEventsByEventCategoryIds(result, activeEventFilterVO.getEventCategoryIds());
        result = queryActiveEventsByStationCategoryIds(result, activeEventFilterVO.getStationCategoryIds());
        result = queryActiveEventsByInstructionStatusIds(result, activeEventFilterVO.getInstructionStatusIds());
        result = queryActiveEventsByInstructionId(result, activeEventFilterVO.getInstructionId());
        result = queryActiveEventsByStandardTypeEntryId(result, activeEventFilterVO.getStandardTypeEntryIds());
        result = queryActiveEventsByDescription(result, activeEventFilterVO.getDescription());
        return result;
    }

    private List<ActiveEvent> queryActiveEventsByStructureIds(List<ActiveEvent> result, String structureIds) {
        if (CharSequenceUtil.isBlank(structureIds)) {
            return result;
        }
        HashSet<Integer> structureIdSet = StringUtils.splitToIntegerCollection(structureIds, HashSet::new);
        return result.stream().filter(e -> structureIdSet.contains(e.getStructureId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByStandardTypeEntryId(List<ActiveEvent> result, String standardTypeEntryIds) {
        if (CharSequenceUtil.isBlank(standardTypeEntryIds)) {
            return result;
        }
        Set<Long> standardTypeEntryIdSet = Arrays.stream(standardTypeEntryIds.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toSet());
        int standardVer = standardVerService.getStandardVer();
        return result.stream().filter(e -> {
            if (Objects.isNull(e.getStandardAlarmNameId())) {
                return false;
            }
            if (Objects.equals(StandardCategoryEnum.EMR.getValue(), standardVer)) {
                return standardTypeEntryIdSet.contains(e.getBaseTypeId() / 1000);
            }
            Long standardAlarmNameId = Long.valueOf(e.getStandardAlarmNameId());
            if (Objects.equals(StandardCategoryEnum.MOBILE.getValue(), standardVer)) {
                return standardTypeEntryIdSet.contains(standardAlarmNameId);
            }
            return standardTypeEntryIdSet.contains(standardAlarmNameId % 100000000 / 1000);
        }).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByInstructionId(List<ActiveEvent> result, String instructionId) {
        if (CharSequenceUtil.isBlank(instructionId)) {
            return result;
        }
        return result.stream().filter(e -> CharSequenceUtil.isNotBlank(e.getInstructionId()) && e.getInstructionId().contains(instructionId)).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByInstructionStatusIds(List<ActiveEvent> result, String instructionStatusIds) {
        if (CharSequenceUtil.isBlank(instructionStatusIds)) {
            return result;
        }
        HashSet<Integer> instructionStatusIdSet = StringUtils.splitToIntegerCollection(instructionStatusIds, HashSet::new);
        return result.stream().filter(e -> instructionStatusIdSet.contains(e.getInstructionStatus())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByStationCategoryIds(List<ActiveEvent> result, String stationCategoryIds) {
        if (CharSequenceUtil.isBlank(stationCategoryIds)) {
            return result;
        }
        HashSet<Integer> stationCategoryIdSet = StringUtils.splitToIntegerCollection(stationCategoryIds, HashSet::new);
        return result.stream().filter(e -> stationCategoryIdSet.contains(e.getStationCategoryId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEventCategoryIds(List<ActiveEvent> result, String eventCategoryIds) {
        if (CharSequenceUtil.isBlank(eventCategoryIds)) {
            return result;
        }
        HashSet<Integer> eventCategoryIdSet = StringUtils.splitToIntegerCollection(eventCategoryIds, HashSet::new);
        return result.stream().filter(e -> eventCategoryIdSet.contains(e.getEventCategoryId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByStationIds(List<ActiveEvent> result, String stationIds) {
        if (CharSequenceUtil.isBlank(stationIds)) {
            return result;
        }
        HashSet<Integer> stationIdSet = StringUtils.splitToIntegerCollection(stationIds, HashSet::new);
        return result.stream().filter(e -> stationIdSet.contains(e.getStationId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEquipmentEventKeys(List<ActiveEvent> result, String equipmentEventKeys) {
        if (CharSequenceUtil.isBlank(equipmentEventKeys)) {
            return result;
        }
        Set<String> equipmentEventKeySet = Arrays.stream(equipmentEventKeys.split(",")).collect(Collectors.toSet());
        return result.stream().filter(o -> equipmentEventKeySet.contains(o.getEquipmentId() + "." + o.getEventId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByMaintainStated(List<ActiveEvent> result, Boolean maintainStated) {
        if (maintainStated == null) {
            return result;
        }
        //maintainState = 1是正常告警，否则都是工程状态下的告警
        return result.stream().filter(o -> maintainStated == (o.getMaintainState() != 1)).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByStartTimeFrom(List<ActiveEvent> result, Date startTime) {
        if (startTime == null) {
            return result;
        }
        return result.stream().filter(o -> o.getStartTime().after(startTime)).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByResourceStructureIds(List<ActiveEvent> activeEvents, String resourceStructureIds) {
        if (CharSequenceUtil.isBlank(resourceStructureIds)) {
            return activeEvents;
        }
        Set<Integer> resourceStructureIdList = Arrays.stream(resourceStructureIds.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> resourceStructureIdList.contains(o.getResourceStructureId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEquipmentCategories(List<ActiveEvent> activeEvents, String equipmentCategories) {
        if (equipmentCategories == null || equipmentCategories.trim().isEmpty()) {
            return activeEvents;
        }
        Set<Integer> equipmentCategoryList = Arrays.stream(equipmentCategories.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> equipmentCategoryList.contains(o.getEquipmentCategory())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByBaseEquipmentIds(List<ActiveEvent> activeEvents, String baseEquipmentIds) {
        if (baseEquipmentIds == null || baseEquipmentIds.trim().isEmpty()) {
            return activeEvents;
        }
        Set<Integer> baseEquipmentIdList = Arrays.stream(baseEquipmentIds.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> o.getBaseEquipmentId() != null && baseEquipmentIdList.contains(o.getBaseEquipmentId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEquipmentIds(List<ActiveEvent> activeEvents, String equipmentIds) {
        if (equipmentIds == null || equipmentIds.trim().isEmpty()) {
            return activeEvents;
        }
        Set<Integer> equipmentIdList = Arrays.stream(equipmentIds.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> equipmentIdList.contains(o.getEquipmentId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEventSeverityIds(List<ActiveEvent> activeEvents, String eventSeverityIds) {
        if (eventSeverityIds == null || eventSeverityIds.trim().isEmpty()) {
            return activeEvents;
        }
        Set<Integer> eventSeverityIdList = Arrays.stream(eventSeverityIds.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> eventSeverityIdList.contains(o.getEventLevel())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByDescription(List<ActiveEvent> result, String description) {
        if (CharSequenceUtil.isBlank(description)) {
            return result;
        }
        return result.stream().filter(e -> Optional.ofNullable(e.getDescription())
                .map(d -> d.contains(description))
                .orElse(false)).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEventConfirmed(List<ActiveEvent> activeEvents, Boolean eventConfirmed) {
        if (eventConfirmed != null) {
            if (Boolean.TRUE.equals(eventConfirmed)) {
                activeEvents = activeEvents.stream().filter(o -> o.getConfirmTime() != null).collect(Collectors.toList());
            } else {
                activeEvents = activeEvents.stream().filter(o -> o.getConfirmTime() == null).collect(Collectors.toList());
            }
            return activeEvents;
        }
        return activeEvents;
    }

    private List<ActiveEvent> queryActiveEventsByConvergented(List<ActiveEvent> activeEvents, Boolean convergented) {
        if (Objects.isNull(convergented)) {
            return activeEvents;
        }
        if (Boolean.TRUE.equals(convergented)) {
            return activeEvents.stream()
                    .filter(o -> o.getConvergenceEventId() != null && o.getConvergenceEventId() > 0)
                    .collect(Collectors.toList());
        }
        return activeEvents.stream()
                .filter(o -> o.getConvergenceEventId() == null || o.getConvergenceEventId() == 0)
                .collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEventEnded(List<ActiveEvent> activeEvents, Boolean eventEnded) {
        if (eventEnded != null) {
            if (Boolean.TRUE.equals(eventEnded)) {
                activeEvents = activeEvents.stream().filter(o -> o.getEndTime() != null).collect(Collectors.toList());
            } else {
                activeEvents = activeEvents.stream().filter(o -> o.getEndTime() == null).collect(Collectors.toList());
            }
        }
        return activeEvents;
    }

    /**
     * 按时间区间的事件查询活动事件
     */
    private List<ActiveEvent> queryActiveEventByEventsInTimeRange(List<ActiveEvent> activeEvents, Date startDate, Date endDate) {
        if (startDate == null && endDate == null) {
            return activeEvents;
        } else if (startDate != null && endDate == null) {
            return activeEvents.stream().filter(e -> e.getStartTime().getTime() >= startDate.getTime()).toList();
        } else if (startDate != null) {
            return activeEvents.stream().filter(e -> e.getStartTime().getTime() >= startDate.getTime() && e.getStartTime().getTime() <= endDate.getTime()).toList();
        } else {
            return activeEvents.stream().filter(e -> e.getStartTime().getTime() <= endDate.getTime()).toList();
        }
    }

    private List<ActiveEvent> queryActiveEventsByUserId(List<ActiveEvent> activeEvents, Integer userId) {
        if (null == userId) {
            return activeEvents;
        }
        List<Region> regions = regionService.findAllRegionsByUserId(userId);
        //如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (regions.stream().anyMatch(o -> o.getRegionId().equals(-1))) {
            return activeEvents;
        }
        List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
        List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);
        //EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
        Set<Integer> resourceStructureIds = regionMaps.stream().filter(o -> o.getEquipmentId().equals(-1)).map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        Set<Integer> equipmentIds = regionMaps.stream().filter(o -> o.getEquipmentId() > 0).map(RegionMap::getEquipmentId).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> resourceStructureIds.contains(o.getResourceStructureId()) || equipmentIds.contains(o.getEquipmentId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByBaseTypeIds(List<ActiveEvent> activeEvents, String baseTypeIds) {
        if (baseTypeIds == null || baseTypeIds.trim().isEmpty()) {
            return activeEvents;
        }
        Set<Long> baseTypeIdList = Arrays.stream(baseTypeIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        return activeEvents.stream().filter(o -> baseTypeIdList.contains(o.getBaseTypeId())).collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByKeywords(List<ActiveEvent> activeEvents, String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return activeEvents;
        }
        return activeEvents.stream()
                .filter(o -> CharSequenceUtil.contains(o.getStationName(), keywords)
                        || CharSequenceUtil.contains(o.getEquipmentName(), keywords)
                        || CharSequenceUtil.contains(o.getEventName(), keywords)
                        || CharSequenceUtil.contains(o.getBaseTypeName(), keywords)
                        || CharSequenceUtil.contains(o.getEquipmentCategoryName(), keywords)
                        || CharSequenceUtil.contains(o.getBaseEquipmentName(), keywords)
                        || CharSequenceUtil.contains(o.getEventSeverity(), keywords)
                        || CharSequenceUtil.contains(o.getMeanings(), keywords)
                        || CharSequenceUtil.contains(o.getDescription(), keywords))
                .collect(Collectors.toList());
    }

    /**
     * 告警盯屏页面展示字段少，筛选关键字考虑更少
     * @param activeEvents
     * @param keywords
     * @return
     */
    private List<ActiveEvent> queryActiveEventsByFocusPanelKeywords(List<ActiveEvent> activeEvents, String keywords) {
        if (keywords == null || keywords.trim().isEmpty()) {
            return activeEvents;
        }
        return activeEvents.stream()
                .filter(o -> CharSequenceUtil.contains(o.getEquipmentName(), keywords)
                        || CharSequenceUtil.contains(o.getEventName(), keywords)
                        || CharSequenceUtil.contains(o.getBaseEquipmentName(), keywords)
                        || CharSequenceUtil.contains(o.getEventSeverity(), keywords))
                .collect(Collectors.toList());
    }

    private List<ActiveEvent> queryActiveEventsByEventName(List<ActiveEvent> activeEvents, String eventName) {
        if (CharSequenceUtil.hasBlank(eventName)) {
            return activeEvents;
        }
        activeEvents = activeEvents.stream().filter(o -> o.getEventName() != null && o.getEventName().contains(eventName)).toList();
        return new ArrayList<>(activeEvents);
    }

    private List<ActiveEvent> queryActiveEventsByConfirmerId(List<ActiveEvent> activeEvents, Collection<Integer> confirmerIds) {
        if (CollUtil.isEmpty(confirmerIds)) {
            return activeEvents;
        }
        activeEvents = activeEvents.stream().filter(o -> confirmerIds.contains(o.getConfirmerId())).toList();
        return new ArrayList<>(activeEvents);
    }

    private List<ActiveEvent> queryActiveEventsByReasonType(List<ActiveEvent> activeEvents, Collection<Integer> eventReasonTypes) {
        if (CollUtil.isEmpty(eventReasonTypes)) {
            return activeEvents;
        }
        activeEvents = activeEvents.stream().filter(o -> eventReasonTypes.contains(o.getEventReasonType())).toList();
        return new ArrayList<>(activeEvents);
    }

    /**
     * 过滤事件
     *
     * @param activeEvents 事件集合
     * @param eventIds     事件条件 【设备id.事件id】 多个|分隔
     * @return
     */
    private List<ActiveEvent> queryActiveEventsByEvent(List<ActiveEvent> activeEvents, String eventIds) {
        if (CharSequenceUtil.hasBlank(eventIds)) {
            return activeEvents;
        }
        List<String> eventIdList = Arrays.stream(eventIds.split("\\|")).toList();
        activeEvents = activeEvents.stream().filter(e -> eventIdList.contains(e.getEquipmentId() + "." + e.getEventId())).toList();
        return new ArrayList<>(activeEvents);
    }

    public ActiveEvent findActionEventBySequenceId(String sequenceId) {
        return ACTIVE_EVENT_HASH_MAP.get(sequenceId);
    }

    public List<ActiveEvent> findActionEventBySequenceIds(List<String> sequenceIds) {
        if (CollUtil.isEmpty(sequenceIds)) {
            return Collections.emptyList();
        }
        List<ActiveEvent> result = new ArrayList<>(sequenceIds.size());
        for (String sequenceId : sequenceIds) {
            ActiveEvent activeEvent = ACTIVE_EVENT_HASH_MAP.get(sequenceId);
            if (Objects.nonNull(activeEvent)) {
                result.add(activeEvent);
            }
        }
        return result;
    }

    private List<ActiveEvent> queryActiveEventsByStartTimeTo(List<ActiveEvent> result, Date endTime) {
        if (endTime == null) {
            return result;
        }
        return result.stream().filter(o -> o.getStartTime().before(endTime)).collect(Collectors.toList());
    }

    public ActiveEventDTO findTopOneActiveEventByStartTimeAndUserId(Date startTime, Integer userId) {
        List<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values().stream().toList();
        List<ActiveEvent> result = new ArrayList<>(activeEvents);
        result = queryActiveEventsByUserId(result, userId);
        List<Integer> batchAlarmWindowSeverityIds = this.getBatchAlarmWindowSeverityIds();
        result = result.stream().filter(o -> o.getStartTime().after(startTime) && batchAlarmWindowSeverityIds.contains(o.getEventLevel())).sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).toList();
        if (result.isEmpty()) {
            return null;
        }
        ActiveEvent activeEvent = result.get(0);
        ActiveEventDTO dto = new ActiveEventDTO();
        dto = dto.from(activeEvent);
        if (activeEvent.getResourceStructureId() != null) {
            dto.setEquipmentPosition(resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
        }
        return dto;
    }

    /**
     * 通过设备id获取警告信息
     *
     * @param equipmentId 设备id
     * @return {@link List}<{@link ActiveEvent}>
     */
    public List<ActiveEvent> getActiveEventsByEquipmentId(Integer equipmentId) {
        if (ObjectUtil.isNull(equipmentId)) {
            return Collections.emptyList();
        }
        Collection<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values();
        activeEvents = activeEvents.stream()
                .filter(event -> Objects.equals(event.getEquipmentId(), equipmentId))
                .toList();
        return new ArrayList<>(activeEvents);
    }

    /**
     * 根据StationId和EquipmentId查找活动告警
     *
     * @param stationId
     * @param equipmentId
     * @return
     */
    public List<ActiveEvent> getActiveEventsByStationIdAndEquipmentId(Integer stationId, Integer equipmentId) {
        Collection<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values();
        activeEvents = activeEvents.stream()
                .filter(event -> Objects.equals(event.getStationId(), stationId) && Objects.equals(event.getEquipmentId(), equipmentId))
                .toList();
        return new ArrayList<>(activeEvents);
    }

    public List<ActiveEventDTO> findActiveEventDTOsByStartTimeAndUserId(Date startTime, Date endTime, Integer userId, boolean queryByEndTime) {
        List<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values().stream().toList();
        List<ActiveEvent> result = new ArrayList<>();
        result.addAll(activeEvents);
        result = queryActiveEventsByUserId(result, userId);
        if (!queryByEndTime) {
            result = result.stream().filter(o -> o.getStartTime().after(startTime)).sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).toList();
        } else {
            result = result.stream().filter(o -> (o.getEndTime() == null && o.getStartTime().after(startTime)) || (o.getEndTime() != null && o.getEndTime().after(endTime))).sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).toList();
        }
        List<ActiveEventDTO> dtoList = new ArrayList<>();
        for (ActiveEvent activeEvent : result) {
            ActiveEventDTO dto = new ActiveEventDTO();
            dto = dto.from(activeEvent);
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 根据UserId查找未结束告警
     *
     * @param userId
     * @return
     */
    public List<ActiveEventDTO> findActiveEventDTOsByUserIdAndEndTimeIsNull(Integer userId) {
        List<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values().stream().toList();
        List<ActiveEvent> result = new ArrayList<>();
        result.addAll(activeEvents);
        result = queryActiveEventsByUserId(result, userId);
        result = result.stream().filter(o -> o.getEndTime() == null).sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).toList();
        List<ActiveEventDTO> dtoList = new ArrayList<>();
        for (ActiveEvent activeEvent : result) {
            ActiveEventDTO dto = new ActiveEventDTO();
            dto = dto.from(activeEvent);
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 是否有告警信息根据设备id
     *
     * @param equipmentId 设备id
     * @return {@link Boolean} true 存在告警  false 不存在告警
     */
    public AlarmState existsActiveByEquipmentId(Integer equipmentId) {
        if (ObjectUtil.isNull(equipmentId)) {
            return AlarmState.NORMAL;
        }
        //设备id相同且告警未结束
        if (ACTIVE_EVENT_HASH_MAP.values()
                .stream()
                .anyMatch(activeEvent -> Objects.equals(activeEvent.getEquipmentId(), equipmentId) &&
                        ObjectUtil.isNull(activeEvent.getEndTime()))) {
            return AlarmState.ALARM;
        }
        return AlarmState.NORMAL;
    }


    /**
     * 是否有告警信息根据局站id
     *
     * @param stationId
     * @return {@link Boolean} true 存在告警  false 不存在告警
     */
    public AlarmState existsActiveByStationId(Integer stationId) {
        if (ObjectUtil.isNull(stationId)) {
            return AlarmState.NORMAL;
        }
        //设备id相同且告警未结束
        if (ACTIVE_EVENT_HASH_MAP.values()
                .stream()
                .anyMatch(activeEvent -> Objects.equals(activeEvent.getStationId(), stationId) &&
                        ObjectUtil.isNull(activeEvent.getEndTime()))) {
            return AlarmState.ALARM;
        }
        return AlarmState.NORMAL;
    }

    /**
     * 根据条件获取事件
     *
     * @param fun 条件函数
     * @return
     */
    public List<ActiveEvent> findActiveEventByCondition(Function<ActiveEvent, Boolean> fun) {
        return ACTIVE_EVENT_HASH_MAP.values().stream().filter(fun::apply).toList();
    }

    /**
     * 通过设备ids获取起当前的告警状态
     *
     * @param equipmentIds 设备ids
     * @return {@link Boolean} true 存在告警  false 不存在告警
     */
    public List<EquipmentAlarmStateDTO> findEquipmentAlarmState(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return new ArrayList<>();
        }
        String equipmentIdJoin = CharSequenceUtil.join(",", equipmentIds);
        Map<Integer, Optional<ActiveEvent>> equipmentAlarmHashMap = ACTIVE_EVENT_HASH_MAP.values()
                .stream()
                .filter(active -> equipmentIdJoin.contains(active.getEquipmentId().toString()) &&
                        ObjectUtil.isNull(active.getEndTime()))
                .collect(Collectors.groupingBy(ActiveEvent::getEquipmentId,
                        Collectors.minBy(Comparator.comparing(ActiveEvent::getEventLevel))));
        List<EquipmentAlarmStateDTO> result = new ArrayList<>();
        //补齐未告警的设备
        for (Integer equipmentId : equipmentIds) {
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if (equipment == null) {
                continue;
            }
            //设备存在告警
            if (equipmentAlarmHashMap.containsKey(equipmentId)) {
                ActiveEvent activeEvent = equipmentAlarmHashMap.get(equipmentId)
                        .get();
                result.add(new EquipmentAlarmStateDTO(equipment.getEquipmentId(), equipment.getEquipmentName(), activeEvent.getEventLevel(), OnlineState.ONLINE, true));
                continue;
            }
            //设备正常
            result.add(new EquipmentAlarmStateDTO(equipment.getEquipmentId(), equipment.getEquipmentName(), 0, OnlineState.ONLINE, true));
        }
        return result;
    }

    /***
     *  统计局站当前最高告警等级
     * @return
     */
    public Map<Integer, Integer> getStationAlarmState() {
        return ACTIVE_EVENT_HASH_MAP.values()
                .stream()
                .filter(active -> ObjectUtil.isNull(active.getEndTime()) && ObjectUtil.isNotNull(active.getEventLevel()))
                .collect(Collectors.toMap(ActiveEvent::getStationId, ActiveEvent::getEventLevel, Math::min));
    }

    /***
     *  统计设备当前最高告警等级
     * @return
     */
    public Map<Integer, Integer> getEquipmentAlarmState() {
        return ACTIVE_EVENT_HASH_MAP.values()
                .stream()
                .filter(active -> ObjectUtil.isNull(active.getEndTime()) && ObjectUtil.isNotNull(active.getEventLevel()))
                .collect(Collectors.toMap(ActiveEvent::getEquipmentId, ActiveEvent::getEventLevel, Math::min));
    }

    public List<ActiveEventDTO> findTopNActiveEventDTOsByUserIdAndEndTimeIsNull(Integer userId, Integer recordNum, Boolean orderByStartTime) {
        List<ActiveEvent> activeEvents = ACTIVE_EVENT_HASH_MAP.values().stream().toList();
        List<ActiveEvent> result = new ArrayList<>();
        result.addAll(activeEvents);
        result = queryActiveEventsByUserId(result, userId);
        if (orderByStartTime) {
            result = result.stream().filter(o -> o.getEndTime() == null).sorted((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())).limit(recordNum).toList();
        } else {
            result = result.stream().filter(o -> o.getEndTime() == null).sorted(Comparator.comparing(ActiveEvent::getEventLevel)).limit(recordNum).toList();
        }
        List<ActiveEventDTO> dtoList = new ArrayList<>();
        for (ActiveEvent activeEvent : result) {
            ActiveEventDTO dto = new ActiveEventDTO();
            dto = dto.from(activeEvent);
            if (activeEvent.getResourceStructureId() != null) {
                dto.setEquipmentPosition(resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
            }
            dtoList.add(dto);
        }
        return dtoList;
    }


    /**
     * 是否有告警信息根据层级id
     *
     * @param resourceStructureId 层级id
     * @return {@link Boolean} true 存在告警  false 不存在告警
     */
    public AlarmState existsActiveByResourceStructureId(Integer resourceStructureId) {
        if (ObjectUtil.isNull(resourceStructureId)) {
            return AlarmState.NORMAL;
        }
        //设备id相同且告警未结束
        if (ACTIVE_EVENT_HASH_MAP.values()
                .stream()
                .anyMatch(activeEvent -> Objects.equals(activeEvent.getResourceStructureId(), resourceStructureId) &&
                        ObjectUtil.isNull(activeEvent.getEndTime()))) {
            return AlarmState.ALARM;
        }
        return AlarmState.NORMAL;
    }

    public void syncConvergenceEventId(Long convergenceEventId, List<String> sequenceIds) {
        for (String sequenceId : sequenceIds) {
            ActiveEvent activeEvent = ACTIVE_EVENT_HASH_MAP.get(sequenceId);
            if (ObjectUtil.isNotNull(activeEvent))
                activeEvent.setConvergenceEventId(convergenceEventId);
        }
    }

    public void updateActiveEventByActiveEventInstructionDTO(ActiveEventInstructionDTO activeEventInstructionDTO) {
        if (!ACTIVE_EVENT_HASH_MAP.containsKey(activeEventInstructionDTO.getSequenceId())) {
            return;
        }
        log.info("UpdateActiveEvent activeEvent, {}", activeEventInstructionDTO);
        ActiveEvent activeEvent = ACTIVE_EVENT_HASH_MAP.get(activeEventInstructionDTO.getSequenceId());
        if (ObjectUtil.isNotNull(activeEvent)) {
            activeEvent.setInstructionId(activeEventInstructionDTO.getInstructionId());
            activeEvent.setInstructionStatus(activeEventInstructionDTO.getInstructionStatus());
        }
        ACTIVE_EVENT_HASH_MAP.put(activeEventInstructionDTO.getSequenceId(), activeEvent);
    }
}
