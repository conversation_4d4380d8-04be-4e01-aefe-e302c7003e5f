package com.siteweb.monitoring.mamager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.properties.FeatureEnableProperties;
import com.siteweb.common.redis.dto.ControlCommandMessage;
import com.siteweb.common.redis.dto.PubSubMessage;
import com.siteweb.common.redis.enums.MessageTypeEnum;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ControlInfoDto;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.*;
import com.siteweb.monitoring.kafka.KafkaUtil;
import com.siteweb.monitoring.kafka.enums.KafkaTopicEnum;
import com.siteweb.monitoring.mapper.*;
import com.siteweb.monitoring.model.ControlRequestItem;
import com.siteweb.monitoring.model.DynamicValue;
import com.siteweb.monitoring.model.ParameterItem;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.vo.ControlCommandVO;
import lombok.extern.slf4j.Slf4j;
import org.javatuples.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 活动控制命令处理类
 */
@Component
@Slf4j
public class ActiveControlManager {

    /**
     * 发送控制命令权限id
     */
    private static final Integer CONTROL_OPERATION_ID = 50;
    private static final Integer FAIL_CONTROL_OPERATION_ID = 9;
    private static final Integer CONFIRM_CONTROL_OPERATION_ID = 52;
    /**
     * 控制命令通道
     */
    private static final String CONTROL_COMMAND_CHANNEL = "ControlRequest";
    private static final int DEFAULT_RETRY_NUM = 0;

    @Autowired
    ConfigControlManager configControlManager;

    @Autowired
    EquipmentMapper equipmentMapper;

    @Autowired
    ActiveControlMapper activeControlMapper;
    @Autowired
    HistoryControlMapper historyControlMapper;
    @Autowired
    UserRoleMapper userRoleMapper;
    @Autowired
    ControlMapper controlMapper;
    @Autowired
    ControlMeaningsMapper controlMeaningsMapper;
    @Autowired
    StationManager stationManager;
    @Autowired
    AccountMapper accountMapper;
    @Autowired
    ActiveControlOfDoorMapper activeControlOfDoorMapper;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    RealtimeRoutingManager realtimeRoutingManager;
    @Autowired
    FeatureEnableProperties featureEnableProperties;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired(required = false)
    KafkaUtil kafkaUtil;

    /**
     * 目前一个命令只有一个参数和一个含义
     */
    public static final  Integer PARAMETERID = 1;
    /**
     * 目前一个命令只有一个参数和一个含义
     */
    public static final  Integer PARAMETERITEMID = 0;
    /**
     * 目前一个命令只有一个参数和一个含义
     */
    public static final  Integer PARAMETERVALUELENGTH = 2;

    /**
     * 发送控制命令
     * @param controlCommandVO 控制命令实体
     * @param userId
     */
    public ControlResultType sendControl(ControlCommandVO controlCommandVO, Integer userId){
        //是否拥有发送控制命令的操作权限
        if (!this.hasOperationPrivilege(userId,CONTROL_OPERATION_ID)) {
            log.error("用户id{},没有权限，控制命令发送失败", userId);
            return ControlResultType.NO_PERMISSION;
        }
        /* 获取控制命令实体 */
        Control control =configControlManager.getControlByStationIdEquipmentIdControlId(controlCommandVO.getStationId(), controlCommandVO.getEquipmentId(), controlCommandVO.getControlId());
        if(control == null){
            log.error("用户id{},未知原因，控制命令发送失败", userId);
            return  ControlResultType.UNKNOWN_ERROR;
        }

        /* 获取设备*/
        Equipment  equipment = equipmentMapper.selectById(controlCommandVO.getEquipmentId());
        if(equipment == null){
            log.error("用户id{},设备不存在，控制命令发送失败", userId);
            return ControlResultType.UNKNOWN_ERROR;
        }
        try {

            /*2. 构建控制命令实体*/
            ControlRequestItem controlRequestItem = getControlRequestItem(controlCommandVO, userId, control.getDataType());

            /* 进行预处理 */
            Pair<Boolean, Object> preResult = preProcessCommand(equipment, control);

            /* 如果需要修改参数则更改参数 */
            Boolean needMode = preResult.getValue0();
            if (needMode.equals(Boolean.TRUE)) {
                modifyControlRequestItemParameter(controlRequestItem, preResult.getValue1());
            }
            return sendControlResultType(userId, controlRequestItem, equipment);
        } catch (Exception ex) {
            log.error("failed to exec Command {} Error:{}", controlCommandVO, ex.toString());
            throw ex;

        }
    }


    private ControlResultType sendControlResultType(Integer userId, ControlRequestItem controlRequestItem, Equipment equipment) {
        //发送控制命令
        if (existsActiveControl(userId, controlRequestItem)) {
            log.error("userid:{},控制命令已经存在。",userId);
            return ControlResultType.COMMAND_EXISTS;
        }
        //获取控制信息
        Long baseCondId = null;
        ControlInfoDto controlInfoDto =  controlMapper.findControlInfo(controlRequestItem.getControlId(), equipment.getEquipmentTemplateId());
        if (Objects.nonNull(controlInfoDto.getBaseTypeId()) && controlInfoDto.getBaseTypeId().doubleValue() > 0 && (CharSequenceUtil.containsAny(controlRequestItem.getParameterValues(),","))){
            //获取控制参数逗号的后面几位并判断是否是数字
            String lastParameter = CharSequenceUtil.subAfter(controlRequestItem.getParameterValues(), ",", true);
            if (NumberUtil.isInteger(lastParameter)) {
                Integer parameterValue = Integer.valueOf(lastParameter);
                ControlMeanings controlMeanings = controlMeaningsMapper.selectOne(
                        Wrappers.lambdaQuery(ControlMeanings.class)
                                .eq(ControlMeanings::getControlId, controlRequestItem.getControlId())
                                .eq(ControlMeanings::getEquipmentTemplateId, equipment.getEquipmentTemplateId())
                                .eq(ControlMeanings::getParameterValue, parameterValue));
                if (Objects.nonNull(controlMeanings)) {
                    baseCondId = controlMeanings.getBaseCondId();
                }
            }
        }
        //杭州电信修改，如果是DDS门禁，开门命令的参数固定为'0,0.0', BEGIN
        Integer doorType =  activeControlMapper.findDoorControlType(equipment.getEquipmentId());
        if (Objects.equals(equipment.getEquipmentCategory(), EquipmentCategoryEnum.DOOR_ACCESS_CONTROL.getValue())) {
            ParameterItem parameterItem = controlRequestItem.getParameterItems().get(0);
            if (Objects.equals(doorType, 4) && Objects.equals(controlInfoDto.getCmdToken(), "22")) {
                parameterItem.setValue(new DynamicValue(parameterItem.getValue().getValueType(),"0,0.0"));
            }
            if (Objects.equals(doorType, 4) && Objects.equals(controlInfoDto.getCmdToken(), "23")) {
                parameterItem.setValue(new DynamicValue(parameterItem.getValue().getValueType(),"0,1"));
            }
        }
        //普通设备 或者 使用了redis发布订阅
        if (noDoorAccessEquipment(equipment.getEquipmentCategory(), controlInfoDto, doorType) || BooleanUtil.isTrue(featureEnableProperties.getRedisPublishSubscription())) {
            ActiveControl activeControl = ActiveControl.builder()
                                               .stationId(controlRequestItem.getStationId())
                                               .stationName(stationManager.findStationById(controlRequestItem.getStationId()).getStationName())
                                               .equipmentId(equipment.getEquipmentId())
                                               .equipmentName(equipment.getEquipmentName())
                                               .controlId(controlRequestItem.getControlId())
                                               .controlName(controlInfoDto.getControlName())
                                               .controlSeverity(controlInfoDto.getControlSeverity())
                                               .cmdToken(controlInfoDto.getCmdToken())
                                               .controlPhase(-1)
                                               .startTime(DateUtil.beginOfSecond(new Date()))
                                               .controlResultType(4)
                                               .controlExecuterId(userId)
                                               .controlExecuterIdName(accountMapper.findUserNameByUserId(userId))
                                               .controlType(controlInfoDto.getControlType())
                                               .description(controlRequestItem.getDescription())
                                               .retry(DEFAULT_RETRY_NUM)
                                               .baseTypeId(controlInfoDto.getBaseTypeId())
                                               .baseTypeName(controlInfoDto.getBaseTypeName())
                                               .parameterValues(controlRequestItem.getParameterValues())
                                               .baseCondId(baseCondId)
                                               .build();
            activeControlMapper.insert(activeControl);
            //设备不在线 修改控制命令的备注与状态
            if (!equipmentService.isEquipmentOnlineById(activeControl.getEquipmentId())) {
                activeControl.setControlResultType(ControlResultTypeEnum.FAILURE.getCode());
                activeControl.setControlResult(messageSourceUtil.getMessage(ControlResultTypeEnum.FAILURE.getI18nCode()));
                activeControl.setDescription(messageSourceUtil.getMessage("command.status.equipmentOffline"));
                activeControl.setEndTime(new Date());
                activeControlMapper.updateById(activeControl);
                log.error("equipmentId:{},不在线", activeControl.getEquipmentId());
                return ControlResultType.EQUIPMENT_OFFLINE;
            }
            sendControlCommandToChannel(equipment.getMonitorUnitId(), activeControl);
            return ControlResultType.SUCCESS;
        }
        //门禁设备
        ActiveControlOfDoor activeControlOfDoor = ActiveControlOfDoor.builder()
                                                                     .stationId(controlRequestItem.getStationId())
                                                                     .hostId(equipment.getMonitorUnitId())
                                                                     .equipmentId(equipment.getEquipmentId())
                                                                     .controlId(controlRequestItem.getControlId())
                                                                     .userId(userId)
                                                                     .parameterValues(controlRequestItem.getParameterValues())
                                                                     .description(controlRequestItem.getDescription())
                                                                     .lastUpdate(DateUtil.beginOfSecond(new Date()))
                                                                     .build();
        activeControlOfDoorMapper.insert(activeControlOfDoor);
        return ControlResultType.SUCCESS;
    }


    private void sendControlCommandToChannel(Integer monitorUnitId, ActiveControl activeControl) {
        Integer dataServerId = realtimeRoutingManager.getDataServerId(monitorUnitId);
        if (Objects.isNull(dataServerId)) {
            return;
        }
        ControlCommandMessage messageBody = ControlCommandMessage.builder()
                                                                 .stationId(activeControl.getStationId())
                                                                 .hostId(monitorUnitId)
                                                                 .equipmentId(activeControl.getEquipmentId())
                                                                 .controlId(activeControl.getControlId())
                                                                 .baseTypeId(activeControl.getBaseTypeId())
                                                                 .userId(activeControl.getControlExecuterId())
                                                                 .serialNo(activeControl.getSerialNo())
                                                                 .controlType(activeControl.getControlType())
                                                                 .priority(activeControl.getControlSeverity())
                                                                 .parameterValues(activeControl.getParameterValues())
                                                                 .startTime(activeControl.getStartTime())
                                                                 .build();
        PubSubMessage pubSubMessage = PubSubMessage.builder()
                                                   .hostId(dataServerId)
                                                   .desHostId(monitorUnitId)
                                                   .messageType(MessageTypeEnum.CONTROL_REQUEST.getValue())
                                                   .messageBody(JSONUtil.toJsonStr(messageBody, new JSONConfig().setDateFormat(DatePattern.UTC_SIMPLE_PATTERN)))//默认序列化成的是时间戳 要求需要ISO8601时间
                                                   .build();
        //kafka不为空优先发送kafka,其次才是redis
        if (kafkaUtil != null) {
            kafkaUtil.sendControlMsg(KafkaTopicEnum.CONTROL_REQUEST.getTopic(),activeControl.getEquipmentId()/1000000 ,pubSubMessage.pubSubMessage());
            return;
        }
        stringRedisTemplate.convertAndSend(CONTROL_COMMAND_CHANNEL, pubSubMessage.pubSubMessage());
    }

    /**
     * 是否不受门禁设备
     * @param equipmentCategory 设备类型
     * @param controlInfoDto
     * @param doorType
     * @return boolean
     */
    private boolean noDoorAccessEquipment(Integer equipmentCategory, ControlInfoDto controlInfoDto, Integer doorType) {
        //    	## 杭州电信修改，如果是DDS门禁，开门命令的参数固定为'0,0.0', END
        //##
        //##  或(直接新增)
        //##  或（不是门禁设备）（不是指纹机设备98）（不是人脸读头设备97）
        //##  或(为门禁设备（82）且是开门命令（2）或关门命令（44）)
        return (!Objects.equals(equipmentCategory, EquipmentCategoryEnum.DOOR_ACCESS_CONTROL.getValue()) && !Objects.equals(equipmentCategory,EquipmentCategoryEnum.FACE_READER.getValue()) && !Objects.equals(equipmentCategory,EquipmentCategoryEnum.FINGER_READER.getValue()))
                || (Objects.equals(equipmentCategory, EquipmentCategoryEnum.DOOR_ACCESS_CONTROL.getValue()) && (controlInfoDto.getControlType() == 44 || controlInfoDto.getControlType() == 2))
                || (Objects.equals(equipmentCategory, EquipmentCategoryEnum.DOOR_ACCESS_CONTROL.getValue()) && Objects.equals(controlInfoDto.getCmdToken(), "22") && doorType == 4)
                || (Objects.equals(equipmentCategory, EquipmentCategoryEnum.DOOR_ACCESS_CONTROL.getValue()) && Objects.equals(controlInfoDto.getCmdToken(), "23") && doorType == 4);
    }

    private boolean existsActiveControl(Integer userId, ControlRequestItem controlRequestItem) {
        return activeControlMapper.existsActiveControl(userId,controlRequestItem.getEquipmentId(),controlRequestItem.getControlId(),controlRequestItem.getParameterValues());
    }

    private boolean hasOperationPrivilege(Integer userId, Integer controlOperationId) {
        //是否系统管理员
        if (userRoleMapper.isSystemAdministrator(userId) > 0) {
            return true;
        }
        //拥有所有操作权限
        if (userRoleMapper.hasAllPrivilege(userId, GlobalConstants.SYSTEM_ADMINISTRATOR_ROLE) > 0) {
           return true;
        }
        //非系统管理员也没有所有的操作权限，查看是否有具体权限
        if (userRoleMapper.hasOperationPrivilege(controlOperationId,userId) > 0) {
            return true;
        }
        return false;
    }

    public ControlResultType sendControl(Integer stationId, Integer equipmentId, Integer commandId, String command, Integer userId) {
        Equipment equipment = equipmentMapper.selectById(equipmentId);
        if (Objects.isNull(equipment)) {
            log.error("发送控制命令失败，设备不存在，设备id：{}", equipmentId);
            return ControlResultType.PARAM_ERROR;
        }
        ControlRequestItem controlRequestItem = null;
        try {
            controlRequestItem = new ControlRequestItem();
            controlRequestItem.setStationId(stationId);
            controlRequestItem.setEquipmentId(equipmentId);
            controlRequestItem.setControlId(commandId);
            controlRequestItem.setUserId(userId);
            controlRequestItem.setSequenceId(commandId);
            ParameterItem parameter = new ParameterItem();
            parameter.setParamId(0);
            parameter.setValue(new DynamicValue(MonitorValueType.String, command));
            controlRequestItem.getParameterItems().add(parameter);

            return sendControlResultType(userId, controlRequestItem, equipment);
        } catch (Exception e) {
            log.error("failed to exec Command {} Error:{},{}", controlRequestItem, e, ExceptionUtil.stacktraceToString(e));
            throw e;
        }
    }

    /**
     * 修改门禁控制命令串
     * @param controlRequestItem 控制命令参数
     * @param param 需附加的部分参数
     */
    private void modifyControlRequestItemParameter(ControlRequestItem controlRequestItem, Object param){
        if (param.getClass() == DynamicValue.class){
            var parameter = new ParameterItem();
            parameter.setParamId(PARAMETERITEMID);
            parameter.setValue((DynamicValue)param);
            controlRequestItem.getParameterItems().add(parameter);
        }  else {
            var parameter = controlRequestItem.getParameterItems().get(0);
            if (MonitorValueType.String == parameter.getValue().getValueType()) {
                parameter.getValue().setStringValue(param.toString()+"," + parameter.getValue().getStringValue());
            } else if (MonitorValueType.Float == parameter.getValue().getValueType()){
                parameter.getValue().setStringValue(param.toString()+"," + parameter.getValue().getFloatValue());
                parameter.getValue().setValueType(MonitorValueType.String);
            }  else {
                //别的未知参数类型
            }
        }
    }

    /**
     *  针对门禁做预处理
     * @param equipment
     * @param control
     * @return
     */
    private Pair<Boolean, Object> preProcessCommand(Equipment equipment,  Control control){
        if (null != equipment &&  null != control){
            // 默认设为1111 后面集成了门径记得修改
            if (equipment.getEquipmentCategory() ==  EquipmentCategory.DoorAccessEquipment.value()){
                return new Pair<>(true, getDoorAccessPassWord(equipment.getStationId(),equipment.getEquipmentId()));
            }
            else if (control.getControlCategory() == CommandCategory.SetTime.value()){
                return new Pair<>(true,new DynamicValue(MonitorValueType.NTPTime, new Date()));
            }
        }
        return new Pair<>(false, null);
    }

    /**
     * 获取门禁访问密码
     * @param stationId 局站Id
     * @param equipmentId 设备Id
     * @return 密码
     */
    private  String getDoorAccessPassWord(int stationId, int equipmentId){
        return  activeControlMapper.getDoorAccessPassword(stationId,equipmentId);
    }

    /**
     * 构建控制命令项
     * @param controlCommandVO
     * @param userId
     * @return
     */
    private ControlRequestItem  getControlRequestItem(ControlCommandVO controlCommandVO, Integer userId, Integer dataType){
        ControlRequestItem controlRequestItem = new ControlRequestItem();
        controlRequestItem.setControlId(controlCommandVO.getControlId());
        controlRequestItem.setControlType(ControlType.Mannul);
        controlRequestItem.setDescription(controlCommandVO.getDescription());
        controlRequestItem.setEquipmentId(controlCommandVO.getEquipmentId());
        controlRequestItem.setStationId(controlCommandVO.getStationId());
        controlRequestItem.setSequenceId(controlCommandVO.getControlId());
        controlRequestItem.setStartTime(controlCommandVO.getStartTime());
        controlRequestItem.setUserId(userId);
        MonitorValueType controlDataType = null;
        if(dataType != null){
            controlDataType =MonitorValueType.valueOf(dataType);
        }

        if(controlDataType == null){
            controlDataType= MonitorValueType.valueOf(1);
        }
        var parameter = new ParameterItem();
        parameter.setParamId(0);
        if (controlDataType == MonitorValueType.String)        {
            parameter.setValue(new DynamicValue(controlDataType, controlCommandVO.getSetValue()));
        } else {
            parameter.setValue(new DynamicValue(controlDataType, Float.valueOf( controlCommandVO.getSetValue())));
        }
        List<ParameterItem> params = new ArrayList<>();
        params.add(parameter);
        controlRequestItem.setParameterItems(params);
        return  controlRequestItem;
    }

    /**
     * 重发控制命令
     * @param userId 执行人Id
     * @param seqNo 流水号
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int reSendControlCommand(Integer userId, Integer seqNo){
        ActiveControl activeControl = findBySeqNo(seqNo);
        if (Objects.isNull(activeControl)) {
            return -2;
        }
        ActiveControlManager bean = SpringUtil.getBean(ActiveControlManager.class);
        int result = bean.confirmControlCommandBySeq(userId, seqNo);
        if (result != 0) {
            return result;
        }
        //由于是重发命令，控制命令逗号前的参数类型需要去除，
        // sendControl方法里面会重新将参数类型拼接上去
        String parameterValues = CharSequenceUtil.subAfter(activeControl.getParameterValues(), ",", false);
        ControlResultType controlResultType = sendControl(activeControl.getStationId(), activeControl.getEquipmentId(), activeControl.getControlId(), parameterValues, userId);
        return controlResultType.value();
    }

    /**
     * 根据流水号确认控制命令
     * @param userId 用户Id
     * @param seqNo 流水号
     * @return 确认结果 -2 不存在该控制命令
     *                 -1 不可知错误
     *                 0 成功
     *                 1 没有确认控制命令的权限
     *                 2 没有确认失败控制命令的权限
     */
    @Transactional(rollbackFor = Exception.class)
    public int confirmControlCommandBySeq(Integer userId, Integer seqNo){
        ActiveControl activeControl = findBySeqNo(seqNo);
        //控制命令不存在
        if (Objects.isNull(activeControl)) {
            return -2;
        }
        if (Objects.equals(activeControl.getControlResultType(), 2) && !hasOperationPrivilege(userId, FAIL_CONTROL_OPERATION_ID)) {
            //是否有确认失败控制权限
            return 2;
        } else if (!hasOperationPrivilege(userId, CONFIRM_CONTROL_OPERATION_ID)) {
            //是否有确认控制权限
            return 1;
        }
        String userName = accountMapper.findUserNameByUserId(userId);
        activeControl.setConfirmerId(userId);
        activeControl.setConfirmerName(userName);
        activeControl.setConfirmTime(new Date());
        activeControlMapper.updateById(activeControl);
        if (Objects.nonNull(activeControl.getEndTime())) {
            historyControlMapper.insert(BeanUtil.copyProperties(activeControl, HistoryControl.class));
            deleteBySeqNo(seqNo);
        }
        return 0;
    }

    private ActiveControl findBySeqNo(Integer seqNo) {
        return activeControlMapper.selectOne(Wrappers.lambdaQuery(ActiveControl.class)
                                                     .eq(ActiveControl::getSerialNo, seqNo));
    }

    private void deleteBySeqNo(Integer seqNo) {
        activeControlMapper.delete(Wrappers.lambdaQuery(ActiveControl.class)
                                           .eq(ActiveControl::getSerialNo, seqNo));
    }
}
