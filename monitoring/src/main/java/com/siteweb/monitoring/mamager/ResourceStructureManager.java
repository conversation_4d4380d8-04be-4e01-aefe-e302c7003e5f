package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.mapper.ResourceStructureMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 管理层级结构树管理
 */
@Component
public class ResourceStructureManager {

    private final Logger log = LoggerFactory.getLogger(ResourceStructureManager.class);

    @Autowired
    ResourceStructureMapper resourceStructureMapper;
    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;

    private ConcurrentHashMap<Integer, ResourceStructure> resourceStructureHashMap = new ConcurrentHashMap<>();
    /**
     * 缩减版
     */
    private ConcurrentHashMap<Integer, ResourceStructureTreeDTO> resourceStructureTreeDTOHashMap = new ConcurrentHashMap<>();

    private boolean initialized = false;
    private Date lastUpdateTime;

    /**
     * 初始化数据
     */
    @PostConstruct
    public void init() {
        List<ResourceStructure> resourceStructures = loadResourceStructureFromDB();
        initialized = true;
        log.info("Init ResourceStructure from database, total {}", resourceStructures.size());
    }

    /**
     * 从数据库读取层级信息
     *
     * @return
     */
    private List<ResourceStructure> loadResourceStructureFromDB() {
        resourceStructureHashMap.clear();
        resourceStructureTreeDTOHashMap.clear();
        List<ResourceStructure> resourceStructureList = resourceStructureMapper.selectList(null);
        for (ResourceStructure resourceStructure : resourceStructureList) {
            resourceStructureHashMap.put(resourceStructure.getResourceStructureId(), resourceStructure);
            resourceStructureTreeDTOHashMap.put(resourceStructure.getResourceStructureId(), new ResourceStructureTreeDTO(resourceStructure));
        }
        initBuildTree();
        lastUpdateTime = new Date();
        return resourceStructureList;
    }

    /**
     * 定时同步层级信息
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncResourceStructure() {
        if (!initialized) {
            return;
        }
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        List<Integer> configChangeMacroLogs = configChangeMacroLogMapper.findResourceStructureChangeLogByUpdateTime(startTime, endTime);
        if (!configChangeMacroLogs.isEmpty()) {
            List<ResourceStructure> resourceStructureList = loadResourceStructureFromDB();
            log.info("Refresh ResourceStructure from database, total {}", resourceStructureList.size());
        }
    }


    /**
     * 主动刷新层级的系统缓存 主要用于部分层级增删改需及时更新缓存使用
     * @param resourceStructureIds 层级ids
     */
    public void refreshResourceStructureByIds(List<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return;
        }
        resourceStructureIds.forEach(resourceStructureHashMap::remove);
        resourceStructureIds.forEach(resourceStructureTreeDTOHashMap::remove);
        List<ResourceStructure> resourceStructureList = resourceStructureMapper.selectBatchIds(resourceStructureIds);
        for (ResourceStructure resourceStructure : resourceStructureList) {
            resourceStructureHashMap.put(resourceStructure.getResourceStructureId(), resourceStructure);
            resourceStructureTreeDTOHashMap.put(resourceStructure.getResourceStructureId(), new ResourceStructureTreeDTO(resourceStructure));
        }
        log.info("刷新层级缓存resourceStructureIds={}", resourceStructureIds);
    }

    /**
     * 根据层级类型获取层级列表
     *
     * @param objectTypeId 层级类型
     * @return 层级列表
     */
    public List<ResourceStructure> getResourceStructureLstByTypeId(Integer objectTypeId) {
        return resourceStructureHashMap.values()
                .stream()
                .filter(resourceStructure -> resourceStructure.getStructureTypeId().equals(objectTypeId))
                .sorted(Comparator.comparing(ResourceStructure::getResourceStructureName))
                .toList();
    }


    /**
     * 根据ID获取层级信息
     *
     * @param resourceStructureId 层级Id
     * @return 层级信息
     */
    public ResourceStructure getResourceStructureById(Integer resourceStructureId) {
        if (ObjectUtil.isNotNull(resourceStructureId) && resourceStructureHashMap.containsKey(resourceStructureId)) {
            return resourceStructureHashMap.get(resourceStructureId);
        } else {
            return null;
        }
    }

    /**
     * 根据IDS获取层级信息
     *
     * @param resourceStructureIds 层级Ids
     * @return 层级信息
     */
    public List<ResourceStructure> getResourceStructureByIds(Collection<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }
        List<ResourceStructure> resourceStructureList = new ArrayList<>(resourceStructureIds.size());
        for (Integer resourceStructureId : resourceStructureIds) {
            if (ObjectUtil.isNotNull(resourceStructureId) && resourceStructureHashMap.containsKey(resourceStructureId)) {
                ResourceStructure resourceStructure = resourceStructureHashMap.get(resourceStructureId);
                resourceStructureList.add(resourceStructure);
            }
        }
        return resourceStructureList;
    }

    /**
     * 根据名称获取层级信息
     *
     * @param resourceStructureName 层级名称
     * @return 层级信息
     */
    public ResourceStructure getResourceStructureByName(String resourceStructureName) {
        return resourceStructureHashMap.values().stream().filter(e -> e.getResourceStructureName().equals(resourceStructureName)).findFirst().orElse(null);
    }

    /**
     * 根据完整层级名称获取id
     * @param fullResourceStructureName 根据\划分层级
     */
    public ResourceStructure getByFullResourceStructureName(String fullResourceStructureName) {
        if (fullResourceStructureName.contains("\\")) {
            String[] names = fullResourceStructureName.split("\\\\");
            List<ResourceStructure> resourceStructureList = resourceStructureHashMap.values().stream()
                    .filter(e -> e.getResourceStructureName().equals(names[names.length - 1])).toList();
            if (resourceStructureList.size() <= 1) {
                return resourceStructureList.stream().findFirst().orElse(null);
            }
            // 最后一层名称查出多个说明名称重复了，从顶层节点往下查
            Optional<ResourceStructure> currentNode = resourceStructureHashMap.values().stream()
                    .filter(e -> e.getResourceStructureName().equals(names[0]))
                    .findFirst();
            if (currentNode.isEmpty()) {
                return null; // 顶级节点不存在
            }
            // 逐级查找
            for (int i = 1; i < names.length; i++) {
                final String currentName = names[i]; // 当前要匹配的层级名称
                final Integer parentId = currentNode.get().getResourceStructureId(); // 上一层的 ID
                currentNode = resourceStructureHashMap.values().stream()
                        .filter(e -> e.getResourceStructureName().equals(currentName) && e.getParentResourceStructureId().equals(parentId))
                        .findFirst();
                if (currentNode.isEmpty()) {
                    return null; // 找不到对应层级，路径无效
                }
            }
            return currentNode.orElse(null); // 返回最后一层匹配到的节点
        }
        return getResourceStructureByName(fullResourceStructureName);
    }

    /**
     * 获取下挂的子节点
     *
     * @param parentId 层级Id
     * @return 层级列表
     */
    public List<ResourceStructure> getResourceStructureLstByParentId(Integer parentId) {
        return resourceStructureHashMap.values()
                .stream()
                .filter(resourceStructure -> resourceStructure.getParentResourceStructureId().equals(parentId))
                .sorted(Comparator.comparing(ResourceStructure::getResourceStructureName))
                .toList();
    }

    /**
     * 获取所有子节点(包含子树节点)
     *
     * @param parentId 层级id
     * @param result   结果list
     */
    public void getAllFlatChildren(Integer parentId, List<ResourceStructure> result) {
        if (resourceStructureHashMap.containsKey(parentId)) {
            ResourceStructure resourceStructure = this.getResourceStructureById(parentId);
            if (resourceStructure != null && CollUtil.isNotEmpty(resourceStructure.getChildren())) {
                resourceStructure.getChildren().forEach(e -> {
                    if (CollUtil.isNotEmpty(e.getChildren())) {
                        getAllFlatChildren(e.getResourceStructureId(), result);
                    }
                    result.add(e);
                });
            }
        }
    }

    /**
     * 获取指定层级下所有的子节点Id
     * @param resourceStructureIdList
     * @return {@link Set}<{@link Integer}>
     */
    public Set<Integer> getAllChildrenId(Collection<Integer> resourceStructureIdList) {
        if (CollUtil.isEmpty(resourceStructureIdList)) {
            return Collections.emptySet();
        }
        List<ResourceStructure> resourceStructureList = new ArrayList<>();
        for (Integer resourceStructureId : resourceStructureIdList) {
            ResourceStructure resourceStructure = this.getResourceStructureById(resourceStructureId);
            if (ObjectUtil.isNull(resourceStructure)) {
                continue;
            }
            this.getAllFlatChildren(resourceStructureId, resourceStructureList);
            resourceStructureList.add(resourceStructure);
        }
        return resourceStructureList.stream()
                                    .map(ResourceStructure::getResourceStructureId)
                                    .collect(Collectors.toSet());
    }

    /**
     * 根据路径获取所有的层级信息
     *
     * @param leveOfPath 层级路径
     * @return 层级列表
     */
    public Map<Integer, ResourceStructure> getAllParentStructureByPath(String leveOfPath) {
        HashMap<Integer, ResourceStructure> structureHashMap = new HashMap<>();
        if (StringUtils.isEmpty(leveOfPath)) {
            return structureHashMap;
        }
        List<String> parentLst = com.siteweb.common.util.StringUtils.splitToList(".", leveOfPath);

        if (parentLst == null || parentLst.isEmpty()) {
            return structureHashMap;
        }

        for (String structureId : parentLst) {
            ResourceStructure resourceStructure = getResourceStructureById(Integer.parseInt(structureId));
            structureHashMap.put(resourceStructure.getStructureTypeId(), resourceStructure);
        }
        return structureHashMap;
    }

    /**
     * 获取自定层级的Id 如获取设备所在的楼层等
     *
     * @param resourceStructureId 层级Id
     * @param parentLevel         层级类型
     * @return 层级信息
     */
    public ResourceStructure getParentResourceStructure(Integer resourceStructureId, int parentLevel) {
        ResourceStructure resourceStructure = getResourceStructureById(resourceStructureId);
        if (resourceStructure == null || StringUtils.isEmpty(resourceStructure.getLevelOfPath())) {
            return null;
        }

        Map<Integer, ResourceStructure> fp = getAllParentStructureByPath(resourceStructure.getLevelOfPath());
        if (fp.containsKey(parentLevel)) {
            return fp.get(parentLevel);
        }
        return null;
    }

    public ResourceStructure getResourceStructureByOriginId(Integer originId, Integer structureTypeId) {
        return resourceStructureHashMap.values().stream().filter(e -> ObjectUtil.isNotNull(e.getOriginId()) && e.getOriginId().equals(originId) && e.getStructureTypeId().equals(structureTypeId)).findFirst().orElse(null);
    }

    /**
     * 获取上层所有节点信息
     *
     * @param resourceStructureId 层级Id
     * @return 上层层级列表
     */
    public Map<Integer, ResourceStructure> getAllParentStructureById(Integer resourceStructureId) {
        ResourceStructure resourceStructure = getResourceStructureById(resourceStructureId);
        if (resourceStructure == null || StringUtils.isEmpty(resourceStructure.getLevelOfPath())) {
            return new HashMap<>();
        }
        return getAllParentStructureByPath(resourceStructure.getLevelOfPath());
    }

    public List<ResourceStructure> findAllParentStructureListById(Integer resourceStructureId) {
        ResourceStructure resourceStructure = this.getResourceStructureById(resourceStructureId);
        if (resourceStructure == null || StringUtils.isEmpty(resourceStructure.getLevelOfPath())) {
            return Collections.emptyList();
        }
        return this.findAllParentStructureListByPath(resourceStructure.getLevelOfPath());
    }

    /**
     * 获取层级信息根据级联id
     *
     * @param levelOfPath 级连id
     * @return {@link List}<{@link ResourceStructure}>
     */
    public List<ResourceStructure> findAllParentStructureListByPath(String levelOfPath) {
        if (CharSequenceUtil.isBlank(levelOfPath)) {
            return Collections.emptyList();
        }
        List<Integer> resourceStructureIds = Arrays.stream(levelOfPath.split("\\."))
                .map(Integer::valueOf)
                .toList();
        return this.getResourceStructureByIds(resourceStructureIds);
    }

    /**
     * 拼接完整的层级路径
     *
     * @param resourceStructureId 层级Id
     * @return 路径名
     */
    public String getFullPath(Integer resourceStructureId) {
        if (Objects.isNull(resourceStructureId)) {
            return "";
        }
        ResourceStructure resourceStructure = resourceStructureHashMap.get(resourceStructureId);
        if (resourceStructure == null) {
            return "";
        }
        return this.getLevelOfPathName(resourceStructure.getLevelOfPath());
    }

    /**
     * 获取根节点
     *
     * @return {@link ResourceStructure}
     */
    public ResourceStructure getRoot() {
        return resourceStructureHashMap.values()
                .stream()
                .filter(resourceStructure -> resourceStructure.getParentResourceStructureId().equals(0))
                .findFirst()
                .orElseGet(ResourceStructure::new);
    }

    /**
     * 获取根节点 缩减版
     *
     * @return {@link ResourceStructure}
     */
    public ResourceStructureTreeDTO getTreeDtoRoot() {
        return resourceStructureTreeDTOHashMap.values()
                .stream()
                .filter(resourceStructure -> resourceStructure.getPId().equals(0))
                .findFirst()
                .orElseGet(ResourceStructureTreeDTO::new);
    }

    /**
     * 获取全部节点
     *
     * @return 层级列表
     */
    public Collection<ResourceStructure> getAll() {
        return resourceStructureHashMap.values()
                .stream()
                .sorted(Comparator.comparing(ResourceStructure::getSortValue).thenComparing(ResourceStructure::getResourceStructureName))
                .toList();
    }

    /**
     * 获取全部层级的objectId
     *
     * @return 全部层级Id
     */
    public List<Integer> getAllObjectIds() {
        return new ArrayList<>(resourceStructureHashMap.keySet());
    }

    /**
     * 获取全部节点
     *
     * @return 层级列表
     */
    public Collection<ResourceStructureTreeDTO> getTreeDtoAll() {
        return resourceStructureTreeDTOHashMap.values()
                .stream()
                .sorted(Comparator.comparing(ResourceStructureTreeDTO::getSortValue).thenComparing(ResourceStructureTreeDTO::getName))
                .toList();
    }

    /**
     * 转换层级路径
     * eg:getLevelOfPathName("431000001.431000002.431000003.431000004.431000011");
     * 结果为：吉林银行数据中心>同城数据中心>一层>数据中心1-1>微模块AB
     *
     * @param leveOfPath 层级节点级联顺序 431000001.431000002.431000003.431000004.431000011
     * @return
     */
    public String getLevelOfPathName(String leveOfPath) {
        leveOfPath = removeRootNote(leveOfPath);
        if (CharSequenceUtil.isBlank(leveOfPath)) {
            return CharSequenceUtil.EMPTY;
        }
        List<String> parentLst = CharSequenceUtil.split(leveOfPath, '.');
        if (CollUtil.isEmpty(parentLst)) {
            return CharSequenceUtil.EMPTY;
        }
        StringJoiner stringJoiner = new StringJoiner("_");
        for (String structureId : parentLst) {
            ResourceStructure resourceStructure = this.getResourceStructureById(Integer.parseInt(structureId));
            stringJoiner.add(resourceStructure.getResourceStructureName());
        }
        return stringJoiner.toString();
    }

    /**
     * 移除层级路径中的根路径
     * 431000001.431000002.431000003.431000004.431000011 结果为 431000002.431000003.431000004.431000011
     * 431000001 结果为 431000001
     * @param levePath 层级路径
     * @return {@link String } 移除根路径后的结果 如果路径中没有.则直接返回传入值
     */
    private String removeRootNote(String levePath) {
        if (CharSequenceUtil.isBlank(levePath)) {
            return CharSequenceUtil.EMPTY;
        }
        int indexOf = levePath.indexOf('.');
        //没有找到. 则返回原始值
        if (indexOf == -1) {
            return levePath;
        }
        //切割根节点
        return levePath.substring(indexOf + 1);
    }
    /**
     * 将resourceStructureTreeDTO集合转为tree的结构
     *
     * @param resourceStructureList
     * @return
     */
    public ResourceStructureTreeDTO resourceStructureTreeDTOBuildTree(List<ResourceStructureTreeDTO> resourceStructureList) {
        int rootId = 0;
        LinkedHashMap<Integer, ResourceStructureTreeDTO> resourceStructureTreeDTOLinkedHashMap = new LinkedHashMap<>();
        for (ResourceStructureTreeDTO treeDTO : resourceStructureList) {
            resourceStructureTreeDTOLinkedHashMap.put(treeDTO.getId(), treeDTO);
            if (treeDTO.getPId() == 0) {
                rootId = treeDTO.getId();
            }
        }
        for (ResourceStructureTreeDTO resourceStructure : resourceStructureTreeDTOLinkedHashMap.values()) {
            if (resourceStructure.getPId() != 0 && resourceStructureTreeDTOLinkedHashMap.containsKey(resourceStructure.getPId())) {
                ResourceStructureTreeDTO param = resourceStructureTreeDTOLinkedHashMap.get(resourceStructure.getPId());
                param.getChildren().add(resourceStructure);
            }
        }
        return resourceStructureTreeDTOLinkedHashMap.get(rootId);
    }

    /**
     * 根据层级类型id与层级id查找层级id
     * @param structureTypeId 层级类型id
     * @param resourceStructureId 层级id
     * @return {@link Set}<{@link Integer}>
     */
    public Set<Integer> findByTypeIdAndId(Integer structureTypeId, Integer resourceStructureId) {
        if (Objects.isNull(structureTypeId) && Objects.isNull(resourceStructureId)) {
            return new HashSet<>(resourceStructureHashMap.keySet());
        } else if (!Objects.isNull(structureTypeId) && Objects.isNull(resourceStructureId)) {
            return this.getResourceStructureLstByTypeId(structureTypeId)
                       .stream()
                       .map(ResourceStructure::getResourceStructureId)
                       .collect(Collectors.toSet());
        } else {
            return this.getAllChildrenId(List.of(resourceStructureId));
        }
    }

    public void initBuildTree() {
        // ResourceStructure 构建树
        for (ResourceStructure resourceStructureOverView : resourceStructureHashMap.values()) {
            if (resourceStructureOverView.getParentResourceStructureId() != 0 && resourceStructureHashMap.containsKey(resourceStructureOverView.getParentResourceStructureId())) {
                ResourceStructure preSourceStructureOverView = resourceStructureHashMap.get(resourceStructureOverView.getParentResourceStructureId());
                preSourceStructureOverView.getChildren().add(resourceStructureOverView);
            }
        }
        // ResourceStructureTreeDTO 构建树
        for (ResourceStructureTreeDTO resourceStructureTreeDto : resourceStructureTreeDTOHashMap.values()) {
            if (resourceStructureTreeDto.getPId() != 0 && resourceStructureTreeDTOHashMap.containsKey(resourceStructureTreeDto.getPId())) {
                ResourceStructureTreeDTO preResourceStructureTreeDto = resourceStructureTreeDTOHashMap.get(resourceStructureTreeDto.getPId());
                preResourceStructureTreeDto.getChildren().add(resourceStructureTreeDto);
            }
        }
    }

    /**
     * 层级levelofpath（id）转成levelofpath（name）
     * @param allLevelOfPath 层级id和其levelofpath的映射
     * @return 层级id和其levelofpathname的映射
     */
    public Map<Integer,String> getResourceFullPath(Map<Integer, String> allLevelOfPath){
        List<Integer> resourceStructureIds = allLevelOfPath.values().stream()
                .filter(StringUtils::isNotEmpty)
                .flatMap(s -> Stream.of(s.split("\\.")))
                .map(Integer::parseInt)
                .distinct()
                .toList();
        Map<Integer, String> resouceStuctureNameMap = getResourceStructureByIds(resourceStructureIds).stream()
                .collect(Collectors.toMap(ResourceStructure::getResourceStructureId, ResourceStructure::getResourceStructureName));
        return allLevelOfPath.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> Arrays.stream(entry.getValue().split("\\."))
                                .map(Integer::parseInt)
                                .map(resouceStuctureNameMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining("_"))
                ));
    }

    /**
     *
     * @param resourceStructureIds
     * @return 层级id与其levelofpath的映射
     */
    public Map<Integer, String> getResourceIdLevelOfPathMap(List<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)){
            return new HashMap<>();
        }
        return getResourceStructureByIds(resourceStructureIds).stream()
                .collect(Collectors.toMap(ResourceStructure::getResourceStructureId, ResourceStructure::getLevelOfPath));
    }
}
