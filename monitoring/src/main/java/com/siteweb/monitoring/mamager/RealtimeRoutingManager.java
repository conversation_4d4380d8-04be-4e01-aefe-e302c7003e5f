package com.siteweb.monitoring.mamager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.monitoring.entity.RealtimeRouting;
import com.siteweb.monitoring.mapper.RealtimeRoutingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class RealtimeRoutingManager {

    private static final String ROUTE_PREFIX = "MuRoute:";
    @Autowired
    private RealtimeRoutingMapper realtimeRoutingMapper;
    @Autowired
    RedisUtil redisUtil;

    public Integer getDataServerId(Integer monitorUnitId) {
        // 计算缓存键
        String cacheKey = ROUTE_PREFIX + monitorUnitId;
        Object dataServerId = redisUtil.get(cacheKey);
        if (Objects.isNull(dataServerId)) {
            // 从数据库获取数据
            RealtimeRouting realtimeRouting = realtimeRoutingMapper.selectOne(Wrappers.lambdaQuery(RealtimeRouting.class)
                                                                                      .eq(RealtimeRouting::getMonitorUnitId, monitorUnitId));
            if (Objects.isNull(realtimeRouting)) {
                log.warn("realtimeRouting is null,monitorUnitId={}", monitorUnitId);
                return null;
            }
            dataServerId = monitorUnitId + "." + realtimeRouting.getDataServerId();
            redisUtil.set(cacheKey, dataServerId);
            return realtimeRouting.getDataServerId();
        }
        // 从缓存值中解析并返回
        String[] res = dataServerId.toString().split("\\.");
        return Integer.parseInt(res[1]);
    }
}
