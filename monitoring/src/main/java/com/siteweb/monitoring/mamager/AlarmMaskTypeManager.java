package com.siteweb.monitoring.mamager;

import cn.hutool.json.JSONUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.AlarmMaskMakTimeGroupCategoryDTO;
import com.siteweb.monitoring.dto.AlarmMaskOperationTypeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 告警屏蔽操作类型
 *
 * @Author: lzy
 * @Date: 2022/5/27 9:36
 */
@Component
public class AlarmMaskTypeManager {
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    private static final Map<Integer, AlarmMaskOperationTypeDTO> typeMap = new HashMap<>();
    public static final Map<Integer, AlarmMaskMakTimeGroupCategoryDTO> categoryMap = new HashMap<>();

    /**
     * 获取所有操作类型
     *
     * @return 操作类型集合
     */
    public static Collection<AlarmMaskOperationTypeDTO> getAllAlarmMaskOperationType() {
        return typeMap.values();
    }

    /**
     * 获取操作类型描述
     *
     * @param operationType 操作类型
     * @return 描述
     */
    public static String getDescByOperationType(Integer operationType) {
        return Optional.ofNullable(typeMap.get(operationType)).orElse(new AlarmMaskOperationTypeDTO()).getDesc();
    }

    /**
     * 获取操作类型描述
     *
     * @param operationType 操作类型
     * @return 描述
     */
    public static String getDescByTimeGroupCategory(Integer operationType) {
        return Optional.ofNullable(categoryMap.get(operationType)).orElse(new AlarmMaskMakTimeGroupCategoryDTO()).getDesc();
    }

    @PostConstruct
    public void initData() {
        List<AlarmMaskOperationTypeDTO> operationTypeDTOList = JSONUtil.toList(messageSourceUtil.getMessage("alarm.mask.operationType"), AlarmMaskOperationTypeDTO.class);
        List<AlarmMaskMakTimeGroupCategoryDTO> alarmMaskMakTimeGroupCategoryDTOList = JSONUtil.toList(messageSourceUtil.getMessage("alarm.mask.timeGroupCategory"), AlarmMaskMakTimeGroupCategoryDTO.class);
        operationTypeDTOList.forEach(e -> typeMap.put(e.getOperationType(), e));
        alarmMaskMakTimeGroupCategoryDTOList.forEach(e -> categoryMap.put(e.getTimeGroupCategory(),e));
    }
}
