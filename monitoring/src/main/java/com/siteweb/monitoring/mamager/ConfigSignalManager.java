package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.mapper.SignalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 配置信号管理类--用于管理信号配置
 */
@Component
@Slf4j
public class ConfigSignalManager {

    @Autowired
    SignalMapper signalMapper;

    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;

    @Autowired
    @Lazy
    EquipmentManager equipmentManager;
    @Autowired
    EquipmentMapper equipmentMapper;
    /**
     * 是否初始化完毕
     */
    private boolean initialized = false;

    /**
     * 信号配置缓存队列
     */
    private static final ConcurrentHashMap<Integer, List<ConfigSignalItem>> SIGNAL_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, ConfigSignalItem> TEMPLATE_SIGNAL_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    /**
     * 缓存最后更新时间
     */
    private Date lastUpdateTime;

    @PostConstruct
    public void init() {
        log.info("Init Signal from database, start total 0");
        loadSignalFromDB();
        initialized = true;
        log.info("Init Signal from database, total {}", SIGNAL_CONCURRENT_HASH_MAP.size());
    }

    /**
     * 从DB 获取TBL_Signal 配置
     */
    private void loadSignalFromDB() {
        SIGNAL_CONCURRENT_HASH_MAP.clear();
        TEMPLATE_SIGNAL_CONCURRENT_HASH_MAP.clear();
        List<ConfigSignalItem> signals = signalMapper.findAllConfigSignals();
        for (ConfigSignalItem signal : signals) {
            SIGNAL_CONCURRENT_HASH_MAP.computeIfAbsent(signal.getEquipmentTemplateId(), key -> new ArrayList<>())
                                      .add(signal);
        }
        loadEquipmentSignalMap(signals);
        lastUpdateTime = new Date();
    }

    /**
     * 加载设备信号相关缓存
     * @param signals 信号
     */
    private void loadEquipmentSignalMap(List<ConfigSignalItem> signals) {
        if (CollUtil.isEmpty(signals)) {
            return;
        }
        TimeInterval timeInterval = new TimeInterval();
        timeInterval.start();
        for (ConfigSignalItem signal : signals) {
            TEMPLATE_SIGNAL_CONCURRENT_HASH_MAP.put(signal.getEquipmentTemplateId() + "." + signal.getSignalId(), signal);
        }
        log.info("本次共加载信号配置:{}个,共耗时{}毫秒", signals.size(), timeInterval.interval());
    }

    /**
     * 每个30S做一次同步
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncSignal() {
        if (!initialized) {
            return;
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        List<Integer> equipmentTemplateIdList = configChangeMacroLogMapper.findEquipmentTemplateChangeLogByUpdateTime(startTime, endTime);
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        //无变更不处理
        if (CollUtil.isEmpty(equipmentTemplateIdList)) {
            return;
        }
        //重新加载配置缓存
        reloadSignalMap(equipmentTemplateIdList);
        lastUpdateTime = new Date();
    }

    public void reloadSignalMap(List<Integer> equipmentTemplateIdList) {
        for (Integer equipmentTemplateId : equipmentTemplateIdList) {
            //先清理后面在添加
            clearTemplateSignalCache(equipmentTemplateId);
            SIGNAL_CONCURRENT_HASH_MAP.remove(equipmentTemplateId);
            //该模板已经不存在信号了
            List<ConfigSignalItem> configSignalItems = signalMapper.findConfigSignalByEquipmentTemplateId(equipmentTemplateId);
            if (CollUtil.isEmpty(configSignalItems)) {
                continue;
            }
            //重新添加缓存
            SIGNAL_CONCURRENT_HASH_MAP.put(equipmentTemplateId, configSignalItems);
            loadEquipmentSignalMap(configSignalItems);
        }
    }

    /**
     * 清理设备信号相关缓存
     *
     * @param equipmentTemplateId 设备模板id
     */
    private void clearTemplateSignalCache(Integer equipmentTemplateId) {
        List<ConfigSignalItem> configSignalItems = SIGNAL_CONCURRENT_HASH_MAP.getOrDefault(equipmentTemplateId, Collections.emptyList());
        for (ConfigSignalItem configSignalItem : configSignalItems) {
            TEMPLATE_SIGNAL_CONCURRENT_HASH_MAP.remove(equipmentTemplateId + "." + configSignalItem.getSignalId());
        }
    }

    /**
     * 根据设备ID获取设备信号配置（从DB）
     *
     * @param equipmentId
     * @return
     */
    public List<ConfigSignalItem> getConfigSignalByEquipmentId(Integer equipmentId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            return new ArrayList<>();
        }
        return getConfigSignalByEquipmentTemplateIdFromCache(equipment.getEquipmentTemplateId());
    }

    /**
     * 根据设备IDs获取设备信号配置（从DB）
     *
     * @param equipmentIdList
     * @return
     */
    public Map<Integer, List<ConfigSignalItem>> getConfigSignalMapByEquipmentIds(List<Integer> equipmentIdList) {
        Map<Integer, List<ConfigSignalItem>> resultMap = new HashMap<>();
        for (Integer equipmentId : equipmentIdList) {
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if (equipment == null) {
                resultMap.put(equipmentId, new ArrayList<>());
            } else {
                List<ConfigSignalItem> configSignalItem = getConfigSignalByEquipmentTemplateIdFromCache(equipment.getEquipmentTemplateId());
                resultMap.put(equipmentId, configSignalItem);
            }
        }
        return resultMap;
    }
    
    public List<ConfigSignalItem> getConfigSignalByEquipmentIdAndBaseTypeId(Integer equipmentId, List<Long> baseTypeIds) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            return new ArrayList<>();
        }
        return getConfigSignalItemByEquipmentTemplateIdAndBaseTypeId(equipment.getEquipmentTemplateId(), baseTypeIds);
    }

    /**
     * 根据模板ID获取信号配置（从cache）
     *
     * @param templateId
     * @return
     */
    public List<ConfigSignalItem> getConfigSignalByEquipmentTemplateIdFromCache(Integer templateId) {
        if (SIGNAL_CONCURRENT_HASH_MAP.containsKey(templateId)) {
            return SIGNAL_CONCURRENT_HASH_MAP.get(templateId).stream().filter(ConfigSignalItem::getVisible).sorted(Comparator.comparing(ConfigSignalItem::getDisplayIndex)).toList();
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 设备模板，基类ID获取配置
     *
     * @param equipmentTemplateId 设备基类类型
     * @param baseTypeIds         基类ID
     * @return
     */
    public List<ConfigSignalItem> getConfigSignalItemByEquipmentTemplateIdAndBaseTypeId(Integer equipmentTemplateId, List<Long> baseTypeIds) {
        //循环获取所有基类信号配置
        List<ConfigSignalItem> configSignalItems = getConfigSignalByEquipmentTemplateIdFromCache(equipmentTemplateId);
        //如果为空，默认取全部标准化的信号
        if (baseTypeIds == null || baseTypeIds.isEmpty()) {
            return configSignalItems.stream().filter(o -> o.getBaseTypeId() != null && o.getBaseTypeId() != 0 && o.getVisible()).toList();
        }
        return configSignalItems.stream().filter(o -> baseTypeIds.contains(o.getBaseTypeId()) && o.getVisible()).sorted(Comparator.comparing(ConfigSignalItem::getDisplayIndex)).toList();
    }

    /**
     * 设备模板，基类ID获取配置
     *
     * @param equipmentTemplateId 设备基类类型
     * @param signalIds           信号ID
     * @return
     */
    public List<ConfigSignalItem> getConfigSignalItemByEquipmentTemplateIdAndSignalId(Integer equipmentTemplateId, List<Integer> signalIds) {
        //循环获取所有基类信号配置
        List<ConfigSignalItem> configSignalItems = getConfigSignalByEquipmentTemplateIdFromCache(equipmentTemplateId);
        //如果为空，默认取全部标准化的信号
        if (signalIds == null || signalIds.isEmpty()) {
            return configSignalItems;
        }
        return configSignalItems.stream().filter(o -> signalIds.contains(o.getSignalId()) && o.getVisible()).sorted(Comparator.comparing(ConfigSignalItem::getDisplayIndex)).toList();
    }

    public List<ConfigSignalItem> getConfigSignalItemByEquipmentTemplateIdAndProperty(Integer equipmentTemplateId, Integer signalPropertyId) {
        //循环获取所有基类信号配置
        List<ConfigSignalItem> configSignalItems = getConfigSignalByEquipmentTemplateIdFromCache(equipmentTemplateId);
        //如果为空，默认取全部标准化的信号
        if (signalPropertyId == null) {
            return new ArrayList<>();
        }
        return configSignalItems.stream().filter(o -> o.isProperty(signalPropertyId)).sorted(Comparator.comparing(ConfigSignalItem::getDisplayIndex)).toList();
    }

    /**
     * 获取信号配置通过设备id与信号id
     *
     * @param equipmentId 设备id
     * @param signalId    信号id
     * @return {@link ConfigSignalItem}
     */
    public ConfigSignalItem getConfigSignalItemByEquipmentIdAndSignalId(Integer equipmentId, Integer signalId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (ObjectUtil.isNull(equipment)) {
            return null;
        }
        return TEMPLATE_SIGNAL_CONCURRENT_HASH_MAP.get(equipment.getEquipmentTemplateId() + "." + signalId);
    }

    public ConfigSignalItem getConfigSignalItemByEquipmentIdAndSignalName(Integer equipmentId, String signalName) {
        List<ConfigSignalItem> configSignals = getConfigSignalByEquipmentId(equipmentId);
        return configSignals.stream().filter(configSignalItem -> Objects.equals(configSignalItem.getSignalName(), signalName)).findFirst().orElse(null);
    }

    /**
     * 根据信号基类ID获取所有信号配置
     *
     * @param baseTypeIds 信号基类ID列表
     * @return 匹配的信号配置列表
     */
    public List<ConfigSignalItem> getConfigSignalsByBaseTypeIds(List<Long> baseTypeIds) {
        if (baseTypeIds == null || baseTypeIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<ConfigSignalItem> allSignals = new ArrayList<>();
        for (List<ConfigSignalItem> templateSignals : SIGNAL_CONCURRENT_HASH_MAP.values()) {
            for (ConfigSignalItem signal : templateSignals) {
                if (baseTypeIds.contains(signal.getBaseTypeId()) && signal.getVisible()) {
                    allSignals.add(signal);
                }
            }
        }
        return allSignals;
    }

}
