package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.mapper.EventMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事件配置管理
 */
@Slf4j
@Component
public class ConfigEventManager {
    /**
     * 是否初始化完毕
     */
    private boolean initialized = false;
    /**
     * 事件配置缓存队列
     */
    private static final ConcurrentHashMap<Integer, List<ConfigEventDTO>> EVENT_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    /**
     * 设备事件配置缓存队列
     * 暂时用不到，当后续有业务调整获取设备事件配置时，可以从缓存中获取
     */
    private static final ConcurrentHashMap<String, ConfigEventDTO> TEMPLATE_EVENT_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    /**
     * 设备事件条件配置缓存队列
     */
    private static final ConcurrentHashMap<String, EventConditionDTO> TEMPLATE_EVENT_CONDITION_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    /**
     * 缓存最后更新时间
     */
    private Date lastUpdateTime;
    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;
    @Autowired
    EventMapper eventMapper;
    @Autowired
    EquipmentMapper equipmentMapper;
    @Autowired
    @Lazy
    EquipmentManager equipmentManager;

    @PostConstruct
    public void init() {
        log.info("Init event config from database, start total 0");
        loadEventConfigFromDB();
        initialized = true;
        log.info("Init event config from database, total {}", EVENT_CONCURRENT_HASH_MAP.size());
    }
    /**
     * 每个30S做一次同步
     */
    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncEventConfig() {
        if (!initialized) {
            return;
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        //重新加载配置
        List<Integer> equipmentTemplateIdList = configChangeMacroLogMapper.findEquipmentTemplateChangeLogByUpdateTime(startTime, endTime);
        //无变更不处理
        if (CollUtil.isEmpty(equipmentTemplateIdList)) {
            return;
        }
        reloadEventMap(equipmentTemplateIdList);
        lastUpdateTime = new Date();
    }

    /**
     * 重新加载模板事件缓存 会先清理在重新添加
     *
     * @param equipmentTemplateIdList 设备样板主键id列表
     */
    public void reloadEventMap(List<Integer> equipmentTemplateIdList) {
        if (CollUtil.isEmpty(equipmentTemplateIdList)) {
            return;
        }
        for (Integer equipmentTemplateId : equipmentTemplateIdList) {
            clearTemplateEventCache(equipmentTemplateId);
            EVENT_CONCURRENT_HASH_MAP.remove(equipmentTemplateId);
            List<ConfigEventDTO> configEventDTOList = eventMapper.findConfigEventByEquipmentTemplateId(equipmentTemplateId);
            for (ConfigEventDTO configEvent : configEventDTOList) {
                EVENT_CONCURRENT_HASH_MAP.computeIfAbsent(configEvent.getEquipmentTemplateId(), key -> new ArrayList<>())
                                         .add(configEvent);
            }
            loadTemplateEventMap(configEventDTOList);
        }
    }

    private void clearTemplateEventCache(Integer equipmentTemplateId) {
        List<ConfigEventDTO> configEventDTOS = EVENT_CONCURRENT_HASH_MAP.getOrDefault(equipmentTemplateId, Collections.emptyList());
        for (ConfigEventDTO configEvent : configEventDTOS) {
            for (EventConditionDTO eventCondition : configEvent.getEventConditions()) {
                TEMPLATE_EVENT_CONDITION_CONCURRENT_HASH_MAP.remove(configEvent.getEquipmentTemplateId() + "." + configEvent.getEventId() + "." + eventCondition.getEventConditionId(), eventCondition);
            }
            TEMPLATE_EVENT_CONCURRENT_HASH_MAP.remove(configEvent.getEquipmentTemplateId() + "." + configEvent.getEventId());
        }
    }

    private void loadEventConfigFromDB() {
        TEMPLATE_EVENT_CONCURRENT_HASH_MAP.clear();
        TEMPLATE_EVENT_CONDITION_CONCURRENT_HASH_MAP.clear();
        EVENT_CONCURRENT_HASH_MAP.clear();
        List<ConfigEventDTO> configEventList = eventMapper.findAllConfigEvents();
        for (ConfigEventDTO configEvent : configEventList) {
            EVENT_CONCURRENT_HASH_MAP.computeIfAbsent(configEvent.getEquipmentTemplateId(), key -> new ArrayList<>())
                                     .add(configEvent);
        }
        loadTemplateEventMap(configEventList);
        lastUpdateTime = new Date();
    }

    /**
     * 加载设备事件配置信息
     * @param configEvents 事件配置列表
     */
    private void loadTemplateEventMap(List<ConfigEventDTO> configEvents) {
        if (CollUtil.isEmpty(configEvents)) {
            return;
        }
        for (ConfigEventDTO configEvent : configEvents) {
            TEMPLATE_EVENT_CONCURRENT_HASH_MAP.put(configEvent.getEquipmentTemplateId() + "." + configEvent.getEventId(), configEvent);
            for (EventConditionDTO eventCondition : configEvent.getEventConditions()) {
                //加载设备事件条件缓存
                TEMPLATE_EVENT_CONDITION_CONCURRENT_HASH_MAP.put(configEvent.getEquipmentTemplateId() + "." + configEvent.getEventId() + "." + eventCondition.getEventConditionId(), eventCondition);
            }
        }
    }

    public EventConditionDTO findConditionByEquipmentIdAndEventId(Integer equipmentId, Integer eventId,Integer conditionId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (ObjectUtil.isNull(equipment)) {
            return null;
        }
        String key = equipment.getEquipmentTemplateId() + "." + eventId + "." + conditionId;
        return TEMPLATE_EVENT_CONDITION_CONCURRENT_HASH_MAP.get(key);
    }

    public ConcurrentHashMap<Integer, List<ConfigEventDTO>> getAllEventMap() {
        return EVENT_CONCURRENT_HASH_MAP;
    }

    public ConcurrentHashMap<String, ConfigEventDTO> getAllEquipmentEventMap() {
        return TEMPLATE_EVENT_CONCURRENT_HASH_MAP;
    }


    public ConcurrentHashMap<String, EventConditionDTO> getAllEquipmentEventConditionMap() {
        return TEMPLATE_EVENT_CONDITION_CONCURRENT_HASH_MAP;
    }

    public ConfigEventDTO findConfigEventByEquipmentIdAndEventId(Integer equipmentId, Integer eventId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            return null;
        }
        return TEMPLATE_EVENT_CONCURRENT_HASH_MAP.get(equipment.getEquipmentTemplateId() + "." + eventId);
    }
}
