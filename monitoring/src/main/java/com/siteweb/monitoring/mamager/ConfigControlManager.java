package com.siteweb.monitoring.mamager;

import com.siteweb.common.redis.RedisUtil;
import com.siteweb.monitoring.entity.Control;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mapper.ControlMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 控制配置管理器
 */
@Component
public class ConfigControlManager {
    private final Logger log = LoggerFactory.getLogger(ConfigControlManager.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ControlMapper controlMapper;

    /**
     * 根据设备ID获取设备控制配置（从redis）
     * @param equipmentId
     * @return
     */
    public List<Control> getConfigEventsByEquipmentIdFromRedis(Integer equipmentId) {
        try {
            String key = "Equipment:" + equipmentId;
            Equipment equipment = redisUtil.getObjectByKeys(key, Equipment.class);
            if(equipment == null)
                return  new ArrayList<>();
            key = "Control:" + equipment.getEquipmentTemplateId() + "*";
            return redisUtil.getObjectsByPreKey(key, Control.class);
        } catch (IOException ex) {
            log.error("Fetch Control According Equipment Error:{0}" , ex);
        }
        return  new ArrayList<>();
    }

    /**
     * 根据模板ID获取设备控制配置（从redis）
     * @param templateId
     * @return
     */
    public List<Control> getConfigEventsByEquipmentTemplateIdFromRedis(Integer templateId) {
        try {
            String key = "Control:" + templateId + "*";
            return redisUtil.getObjectsByPreKey(key, Control.class);
        }catch (IOException ex) {
            log.error("Fetch Control According TemplateId Error:{0}" , ex);
        }
        return  new ArrayList<>();
    }

    /**
     * 根据设备ID获取设备控制配置（从DB）
     * @param equipmentId
     * @return
     */
    public List<Control> getConfigEventsByEquipmentIdFromDB(Integer equipmentId) {
        return controlMapper.findControlsByEquipmentId(equipmentId);
    }

    /**
     * 根据模板ID获取控制配置（从DB）
     * @param templateId
     * @return
     */
    public List<Control> getConfigEventsByEquipmentTemplateIdFromDB(Integer templateId)  {
        return controlMapper.findControlsByEquipmentTemplateId(templateId);
    }

    public Control getControlByStationIdEquipmentIdControlId(Integer stationId, Integer equipmentId, Integer controlId){
        List<Control> controls = controlMapper.findControlByStationIdEquipmentIdControlId(stationId, equipmentId, controlId);
        if(controls.isEmpty()){
            return  null;
        }
        return controls.get(0);
    }
}
