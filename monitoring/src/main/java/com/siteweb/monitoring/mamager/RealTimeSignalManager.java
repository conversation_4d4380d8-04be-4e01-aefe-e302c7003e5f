package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 实时信号协议处理类--主要从redis获取实时数据
 */
@Slf4j
@Component
public class RealTimeSignalManager {

    @Autowired
    @Qualifier("realTimeRedisUtil")
    RedisUtil redisUtil;
    @Autowired
    ConfigSignalManager configSignalManager;

    private final String  dataPrefix ="RealTimeSignal:";

    /**
     * 根据设备ID获取设备实时信号数据（协议数据）
     * @param equipmentId
     * @return
     */
    public List<RealTimeSignalItem> getRealTimeSignalByEquipmentId(Integer equipmentId){
        List<ConfigSignalItem> configSignalList = configSignalManager.getConfigSignalByEquipmentId(equipmentId);
        List<String> redisKeys = new ArrayList<>(configSignalList.size());
        for (ConfigSignalItem configSignalItem : configSignalList) {
            redisKeys.add(dataPrefix + equipmentId + "." + configSignalItem.getSignalId());
        }
        return getRealTimeSignalByKeys(redisKeys);
    }

    /**
     * 将redis内存数据转为实时信号对象
     * @param list
     * @return
     */
    public List<RealTimeSignalItem> cast(Collection<?> list) {
        List<RealTimeSignalItem> result = new ArrayList<>();
        if(list == null)
            return  result;
        for(Object o : list) {
            // throws casting exception
            if(o!= null) {
                RealTimeSignalItem realTimeSignalItem = RealTimeSignalItem.parseFromStr(o.toString());
                result.add(realTimeSignalItem);
            }
        }
        return result;
    }

    /**
     * 将redis内存数据转为实时信号对象
     * @param o
     * @return
     */
    public RealTimeSignalItem cast(Object o) {
        if(o == null)
            return  null;

        RealTimeSignalItem realTimeSignalItem = RealTimeSignalItem.parseFromStr(o.toString());
        return realTimeSignalItem;
    }

    /**
     * 根据信号key获取信号值
     * @param key redis 键值 设备id.信号id
     * @return
     */
    public RealTimeSignalItem getRealTimeSignalByKey(String key){
        return cast(redisUtil.get(dataPrefix + key));
    }

    /**
     * 根据信号组列表，获取所有的列表信息
     * @param keys redis 键值
     * @return
     */
    public List<RealTimeSignalItem> getRealTimeSignalByKeys(List<String> keys){
        if (CollUtil.isEmpty(keys)) {
            return Collections.emptyList();
        }
        keys = getRedisKey(keys);
        List<RealTimeSignalItem> realTimeSignalItemList = new ArrayList<>(keys.size());
        List<String> stagingKeys = new ArrayList<>(Math.min(GlobalConstants.REDIS_MAX_OPERATION_COUNT, keys.size()));
        for(String key : keys){
            stagingKeys.add(key);
            //最多10000个一批，避免数量过大阻塞redis
            if (stagingKeys.size() >= GlobalConstants.REDIS_MAX_OPERATION_COUNT) {
                List<RealTimeSignalItem> realTimeSignalItems = cast(redisUtil.mget(stagingKeys));
                realTimeSignalItemList.addAll(realTimeSignalItems);
                stagingKeys.clear();
            }
        }
        if (CollUtil.isNotEmpty(stagingKeys)) {
            List<RealTimeSignalItem> realTimeSignalItems = cast(redisUtil.mget(stagingKeys));
            realTimeSignalItemList.addAll(realTimeSignalItems);
        }
        return realTimeSignalItemList;
    }
    /**
     *  获取单条实时信号值
     * @param equipmentId 设备Id
     * @param signalId 信号Id
     * @return
     */
    public RealTimeSignalItem getRealTimeSignalBySignalId(Integer equipmentId, Integer signalId){
        String key = dataPrefix + equipmentId + "." + signalId;
        return cast(redisUtil.get(key));
    }

    /**
     * 去掉重复的rediskey
     * @param keys
     * @return
     */
    private List<String> getRedisKey(List<String> keys){
        return keys.stream().distinct().toList();
    }
}
