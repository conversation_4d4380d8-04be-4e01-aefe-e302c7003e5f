package com.siteweb.monitoring.mamager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.service.ResourceObjectService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Component
public class ResourceObjectManager {
    @Autowired
    private List<ResourceObjectService> resourceObjectServiceList;
    private final HashMap<Integer, ResourceObjectService> resourceObjectServiceHashMap = new HashMap<>();


    @SneakyThrows
    @PostConstruct
    public void init() {
        for (ResourceObjectService objectService : resourceObjectServiceList) {
            SourceType[] types = objectService.getSourceTypes();
            for (SourceType type : types) {
                ResourceObjectService service = resourceObjectServiceHashMap.get(type.value());
                if (service != null) {
                    String error = String.format("检测到对象类型[%s]重复映射：%s => %s", type.toString(), service.getClass().getName(), objectService.getClass().getName());
                    throw new Exception(error);
                }
                resourceObjectServiceHashMap.put(type.value(), objectService);
            }
        }
    }

    /**
     * 查找resourceObject
     *
     * @param resourceObject 资源对象
     * @return {@link ResourceObjectEntity}
     */
    public ResourceObjectEntity findEntityByObject(ResourceObject resourceObject) {
        ResourceObjectService resourceObjectService = resourceObjectServiceHashMap.get(resourceObject.getObjectTypeId());
        if (ObjectUtil.isNull(resourceObjectService)) {
            return null;
        }
        return resourceObjectService.findResourceObjectEntity(resourceObject.getObjectId());
    }

    /**
     * 获取所有资源
     *
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    public List<ResourceObjectEntity> findAllResource() {
        List<ResourceObjectEntity> resourceObjectEntityList = new ArrayList<>();
        //避免重复执行
        HashSet<ResourceObjectService> hashSet = new HashSet<>();
        for (ResourceObjectService service : resourceObjectServiceHashMap.values()) {
            if (hashSet.contains(service)) {
                continue;
            }
            List<ResourceObjectEntity> allResourceObject = service.findAllResourceObject();
            resourceObjectEntityList.addAll(allResourceObject);
            hashSet.add(service);
        }
        return resourceObjectEntityList;
    }

    /**
     * 获取资源通过用户id
     *
     * @param userId 用户id
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    public List<ResourceObjectEntity> findAllResourceByUserId(Integer userId) {
        List<ResourceObjectEntity> resourceObjectEntityList = new ArrayList<>();
        //避免重复执行
        HashSet<ResourceObjectService> hashSet = new HashSet<>();
        for (ResourceObjectService service : resourceObjectServiceHashMap.values()) {
            if (hashSet.contains(service)) {
                continue;
            }
            List<ResourceObjectEntity> allResourceObject = service.findAllResourceObjectByUserId(userId);
            resourceObjectEntityList.addAll(allResourceObject);
            hashSet.add(service);
        }
        return resourceObjectEntityList;
    }

    /**
     * 查找resourceObject
     *
     * @param objectType 资源对象类型
     * @return {@link ResourceObjectEntity}
     */
    public List<ResourceObjectEntity> findEntityByObjectType(Integer objectType) {
        ResourceObjectService resourceObjectService = resourceObjectServiceHashMap.get(objectType);
        if (ObjectUtil.isNull(resourceObjectService)) {
            return Collections.emptyList();
        }
        List<ResourceObjectEntity> allResourceObject = resourceObjectService.findAllResourceObject();
        if (isResourceStructure(objectType)){
            allResourceObject = allResourceObject.stream()
                                                 .filter(resource -> Objects.equals(resource.getObjectTypeId(), objectType))
                                                 .toList();
        }
        return allResourceObject;
    }

    /**
     * 是否是层级资源
     * @param objectType 资源类型
     * @return {@link Boolean}
     */
    private boolean isResourceStructure(Integer objectType) {
        if (Objects.isNull(objectType)) {
            return false;
        }
        return Objects.equals(objectType, SourceType.CENTER.value()) || Objects.equals(objectType, SourceType.PARK.value()) ||
                Objects.equals(objectType, SourceType.BUILDING.value()) || Objects.equals(objectType, SourceType.FLOOR.value()) ||
                Objects.equals(objectType, SourceType.ROOM.value()) || Objects.equals(objectType, SourceType.MDC.value()) ||
                Objects.equals(objectType, SourceType.SCCENTER.value()) || Objects.equals(objectType, SourceType.SSCENTER.value()) ||
                Objects.equals(objectType, SourceType.DISTRICT.value()) || Objects.equals(objectType, SourceType.STATION.value()) ||
                Objects.equals(objectType, SourceType.HOUSE.value());
    }
}
