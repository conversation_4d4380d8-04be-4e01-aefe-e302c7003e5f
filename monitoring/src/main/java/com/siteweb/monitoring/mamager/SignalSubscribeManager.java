package com.siteweb.monitoring.mamager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.redis.dto.PubSubMessage;
import com.siteweb.common.redis.enums.MessageTypeEnum;
import com.siteweb.monitoring.dto.MonitorUnitDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.MonitorUnitCategoryEnum;
import com.siteweb.monitoring.model.SubscribeSignal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/** 信号订阅管理器 */
@Slf4j
@Component
public class SignalSubscribeManager {

    private static final String SIGNAL_SUBSCRIBE_REQUEST = "SignalSubscribeRequest";
    private static final String EQUIPMENT_SIGNAL_SUBSCRIBE_PREFIX = "EqSignalSubscribe:";
    /** 普通设备的订阅时间间隔 */
    private static final int EQUIPMENT_SUBSCRIBE_INTERVAL = 30;
    /** B接口接入的设备的时间间隔 */
    private static final int B_INTERFACE_EQUIPMENT_SUBSCRIBE_INTERVAL = 5;
    /** 订阅所有信号标识 */
    private static final int SUBSCRIBE_ALL_SIGNAL = -1;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private MonitorUnitManager monitorUnitManager;
    @Autowired
    private RealtimeRoutingManager realtimeRoutingManager;

    /**
     * 发送信号订阅请求（针对单个设备）
     *
     * @param equipmentId 设备ID
     */
    @Async
    public void sendSignalSubscribe(Integer equipmentId) {
        if (equipmentId == null) {
            log.warn("设备ID为空，跳过订阅。");
            return;
        }

        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            log.error("设备信息为空，订阅设备信号失败，设备ID:{}", equipmentId);
            return;
        }
        if (isSelfDiagnosticEquipment(equipment)) {
            return;
        }

        if (isEquipmentSignalSubscribed(equipment, null)) {
            return;
        }

        SubscribeSignal subscribeSignal = createSubscribeSignal(equipment, SUBSCRIBE_ALL_SIGNAL);
        sendSubscribeSignals(List.of(subscribeSignal));
    }

    /**
     * 订阅设备信号
     *
     * @param equipmentIds 设备ID集合
     */
    @Async
    public void sendSignalSubscribe(Set<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            log.warn("设备ID列表为空，跳过订阅。");
            return;
        }

        List<SubscribeSignal> subscribeSignals =
                equipmentIds.stream()
                            .filter(Objects::nonNull)
                            .map(equipmentManager::getEquipmentById)
                            .filter(Objects::nonNull)
                            .filter(equipment -> !isSelfDiagnosticEquipment(equipment))
                            .filter(equipment -> !isEquipmentSignalSubscribed(equipment, null))
                            .map(equipment -> createSubscribeSignal(equipment, SUBSCRIBE_ALL_SIGNAL))
                            .toList();

        sendSubscribeSignals(subscribeSignals);
    }

    /**
     * 订阅设备信号，具体到信号
     *
     * @param equipmentSignalsMap 订阅信息，key：设备ID，value：对应设备的信号ID列表
     */
    @Async
    public void sendSignalSubscribe(Map<Integer, List<Integer>> equipmentSignalsMap) {
        if (CollUtil.isEmpty(equipmentSignalsMap)) {
            log.warn("设备信号映射为空，跳过订阅。");
            return;
        }
        List<SubscribeSignal> subscribeSignals = equipmentSignalsMap.entrySet()
                                                                    .stream()
                                                                    .flatMap(entry -> {
                                                                        Integer equipmentId = entry.getKey();
                                                                        List<Integer> signalIds = entry.getValue();
                                                                        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
                                                                        if (equipment == null) {
                                                                            log.warn("设备信息为空，跳过订阅，设备ID:{}", equipmentId);
                                                                            return Stream.empty();
                                                                        }

                                                                        if (CollUtil.isEmpty(signalIds)) {
                                                                            log.warn("信号ID列表为空，跳过订阅，设备ID:{}", equipmentId);
                                                                            return Stream.empty();
                                                                        }
                                                                        if (isSelfDiagnosticEquipment(equipment)) {
                                                                            log.warn("自诊断设备，跳过订阅，设备ID:{}", equipmentId);
                                                                            return Stream.empty();
                                                                        }

                                                                        return signalIds.stream()
                                                                                        .filter(signalId -> !isEquipmentSignalSubscribed(equipment, signalId))
                                                                                        .map(signalId -> createSubscribeSignal(equipment, signalId));
                                                                    }).toList();
        sendSubscribeSignals(subscribeSignals);
    }

    /**
     * 创建订阅信号
     *
     * @param equipment 设备对象
     * @param signalId 信号ID，如果为 {@link #SUBSCRIBE_ALL_SIGNAL} 则代表订阅该设备下所有的信号
     * @return 构建好的 SubscribeSignal 对象
     */
    private SubscribeSignal createSubscribeSignal(Equipment equipment, Integer signalId) {
        return SubscribeSignal.builder()
                              .stationId(equipment.getStationId())
                              .equipmentId(equipment.getEquipmentId())
                              .signalId(signalId)
                              .hostId(equipment.getMonitorUnitId())
                              .lastUpdateDateTime(new Date())
                              .build();
    }

    /**
     * 发送订阅信号列表
     *
     * @param subscribeSignals 信号订阅列表
     */
    private void sendSubscribeSignals(List<SubscribeSignal> subscribeSignals) {
        if (CollUtil.isEmpty(subscribeSignals)) {
            log.warn("没有创建有效的订阅信号，不发送任何内容。");
            return;
        }

        // 根据 监控单元 分组
        Map<Integer, List<SubscribeSignal>> hostSubscribeSignalMap = subscribeSignals.stream().collect(Collectors.groupingBy(SubscribeSignal::getHostId));

        hostSubscribeSignalMap.forEach(
                (hostId, subscribeSignalList) -> {
                    Integer dataServerId = realtimeRoutingManager.getDataServerId(hostId);
                    if (dataServerId == null) {
                        log.error("找不到对应的监控单元ID，hostId:{}", hostId);
                        return;
                    }

                    // 使用 Hutool 的 JSONConfig 设置日期格式
                    JSONConfig jsonConfig = new JSONConfig().setDateFormat(DatePattern.UTC_SIMPLE_PATTERN);
                    String messageBody = JSONUtil.toJsonStr(subscribeSignalList, jsonConfig);

                    PubSubMessage pubSubMessage = PubSubMessage.builder()
                                                               .hostId(dataServerId)
                                                               .desHostId(hostId)
                                                               .messageType(MessageTypeEnum.REAL_SIGNAL_REQUEST.getValue())
                                                               .messageBody(messageBody)
                                                               .build();

                    // 发送消息
                    stringRedisTemplate.convertAndSend(SIGNAL_SUBSCRIBE_REQUEST, pubSubMessage.pubSubMessage());
                });
    }

    /**
     * 检查设备信号是否已经被订阅
     *
     * @param equipment 设备对象
     * @param signalId 信号ID，可以为 null，表示检查整个设备是否已订阅
     * @return true 表示已订阅，false 表示未订阅
     */
    private boolean isEquipmentSignalSubscribed(Equipment equipment, Integer signalId) {
        int subscribeInterval = getSubscribeInterval(equipment);
        String redisKey = buildRedisKey(equipment.getEquipmentId(), signalId);

        Boolean result = stringRedisTemplate.opsForValue()
                                            .setIfAbsent(redisKey, "", subscribeInterval, TimeUnit.SECONDS);

        return !Boolean.TRUE.equals(result);
    }

    /**
     * 构建 Redis Key
     *
     * @param equipmentId 设备ID
     * @param signalId 信号ID，可以为 null
     * @return Redis Key
     */
    private String buildRedisKey(Integer equipmentId, Integer signalId) {
        return signalId == null
                ? EQUIPMENT_SIGNAL_SUBSCRIBE_PREFIX + equipmentId
                : EQUIPMENT_SIGNAL_SUBSCRIBE_PREFIX + equipmentId + "." + signalId;
    }

    /**
     * 获取设备的订阅间隔
     *
     * @param equipment 设备对象
     * @return 订阅间隔（秒）
     */
    private int getSubscribeInterval(Equipment equipment) {
        return isBInterfaceEquipment(equipment) ? B_INTERFACE_EQUIPMENT_SUBSCRIBE_INTERVAL : EQUIPMENT_SUBSCRIBE_INTERVAL;
    }

    /**
     * 判断是否为B接口接入的设备
     *
     * @param equipment 设备对象
     * @return true 表示是B接口设备，false 表示不是
     */
    private boolean isBInterfaceEquipment(Equipment equipment) {
        return Optional.ofNullable(monitorUnitManager.getById(equipment.getMonitorUnitId()))
                       .map(MonitorUnitDTO::getMuCategoryId)
                       .filter(categoryId -> Objects.equals(categoryId, MonitorUnitCategoryEnum.B_INTERFACE.getItemId()))
                       .isPresent();
    }

    /**
     * 是否是自诊断设备
     * 此类设备没有信号信息，不需要下发订阅
     * @param equipment 设备信息
     * @return boolean true 是  false 否
     */
    private boolean isSelfDiagnosticEquipment(Equipment equipment){
        return equipment.getStationId() < 0;
    }
}