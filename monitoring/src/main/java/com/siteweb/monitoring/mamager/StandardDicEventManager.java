package com.siteweb.monitoring.mamager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.monitoring.entity.StandardDicEvent;
import com.siteweb.monitoring.mapper.StandardDicEventMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> zhou
 * @description StandardDicEventManager
 * @createTime 2022-08-10 12:55:01
 */
@Component
public class StandardDicEventManager {

    private final Logger log = LoggerFactory.getLogger(StandardDicEventManager.class);
    private static final ConcurrentHashMap<Integer, List<StandardDicEvent>> STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    @Autowired
    StandardDicEventMapper standardDicEventMapper;

    @PostConstruct
    public void init() {
        List<StandardDicEvent> standardDicEvents = standardDicEventMapper.getAllStandardDicEvent();
        for (StandardDicEvent standardDicEvent : standardDicEvents) {
            List<StandardDicEvent> tmpList;
            if (STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.containsKey(standardDicEvent.getStandardDicId())) {
                tmpList = STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.get(standardDicEvent.getStandardDicId());
            } else {
                tmpList = new ArrayList<>();
            }
            tmpList.add(standardDicEvent);
            STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.put(standardDicEvent.getStandardDicId(), tmpList);
        }
        log.info("Init standardDicEvent from database, total {}", standardDicEvents.size());
    }

    public StandardDicEvent getStandardDicEvent(Integer standardDicId, Integer standardType) {
        List<StandardDicEvent> standardDicEvents = STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.get(standardDicId);
        if(ObjectUtil.isNotNull(standardDicEvents)) {
            for (StandardDicEvent standardDicEvent : standardDicEvents) {
                if (standardDicEvent.getStandardType().equals(standardType)) {
                    return standardDicEvent;
                }
            }
        }
        return null;
    }
}
