<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.video.mapper.CameraGroupMapper">
    <resultMap id="findByParentIdIsResultMap" type="com.siteweb.video.entity.CameraGroup">
        <id property="cameraGroupId" column="cameraGroupId"/>
        <id property="cameraGroupName" column="cameraGroupName"/>
        <id property="parentId" column="parentId"/>
        <id property="description" column="description"/>
        <id property="parentName" column="parentName"/>
        <!--        获取其摄像机组子集-->
        <collection property="children" column="parentId=cameraGroupId" select="findByParentIdIs">
        </collection>
    </resultMap>
    <select id="findByParentIdIs" resultMap="findByParentIdIsResultMap">
        SELECT cameraGroup.CameraGroupId                           AS cameraGroupId,
               cameraGroup.CameraGroupName                         AS cameraGroupName,
               cameraGroup.ParentId                                AS parentId,
               cameraGroup.Description                             AS "description",
               (SELECT parent.CameraGroupName FROM CameraGroup parent WHERE parent.cameraGroupId = cameraGroup.parentId) AS parentName
        FROM CameraGroup cameraGroup
        WHERE cameraGroup.ParentId = #{parentId}
    </select>
    <select id="findAll" resultMap="findByParentIdIsResultMap">
        SELECT cameraGroup.CameraGroupId                           AS cameraGroupId,
               cameraGroup.CameraGroupName                         AS cameraGroupName,
               cameraGroup.ParentId                                AS parentId,
               cameraGroup.Description                             AS "description",
               (SELECT parent.CameraGroupName FROM CameraGroup parent WHERE parent.cameraGroupId = cameraGroup.parentId) AS parentName
        FROM CameraGroup cameraGroup
    </select>
</mapper>