<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.video.mapper.CameraMapper">
    <insert id="saveBatch" useGeneratedKeys="true" keyProperty="cameraId">
        INSERT INTO
        Camera(CameraName,CameraIp,CameraPort,ChannelNumber,CameraGroupId,UserName,Password,VendorId,VendorName,CameraIndexCode,CameraType,CameraTypeName,UpdateTime,Description)
        VALUES
        <foreach collection="cameras" item="item" separator=",">
            (#{item.cameraName},#{item.cameraIp},#{item.cameraPort},#{item.channelNumber},#{item.cameraGroupId},#{item.userName},#{item.password},#{item.vendorId},#{item.vendorName},#{item.cameraIndexCode},#{item.cameraType},#{item.cameraTypeName},#{item.updateTime},#{item.description})
        </foreach>
    </insert>
    <update id="updateCamerasGroupIdNull">
        UPDATE Camera c
        SET c.cameraGroupId = NULL
        WHERE c.cameraGroupId = #{cameraGroupId}
    </update>

    <select id="findByCameraGroupId" resultType="com.siteweb.video.entity.Camera">
        SELECT camera.CameraId,
               camera.CameraName,
               camera.CameraIp,
               camera.CameraPort,
               camera.ChannelNumber,
               camera.CameraGroupId,
               camera.UserName,
               camera.Password,
               camera.VendorId,
               camera.VendorName,
               camera.CameraIndexCode,
               camera.CameraType,
               camera.CameraTypeName,
               camera.UpdateTime,
               camera.Description
        FROM Camera camera
        WHERE camera.CameraGroupId = #{cameraGroupId}
    </select>
    <select id="getNoGroupCameras" resultType="com.siteweb.video.entity.Camera">
        SELECT camera.CameraId,
               camera.CameraName,
               camera.CameraIp,
               camera.CameraPort,
               camera.ChannelNumber,
               camera.CameraGroupId,
               camera.UserName,
               camera.Password,
               camera.VendorId,
               camera.VendorName,
               camera.CameraIndexCode,
               camera.CameraType,
               camera.CameraTypeName,
               camera.UpdateTime,
               camera.Description
        FROM Camera camera
        WHERE camera.cameraGroupId IS NULL
           OR camera.cameraGroupId NOT IN (SELECT DISTINCT cameraGroupId FROM cameragroup)
    </select>
    <select id="findByCameraIndexCodeIn" resultType="com.siteweb.video.entity.Camera">
        SELECT camera.CameraId,
        camera.CameraName,
        camera.CameraIp,
        camera.CameraPort,
        camera.ChannelNumber,
        camera.CameraGroupId,
        camera.UserName,
        camera.Password,
        camera.VendorId,
        camera.VendorName,
        camera.CameraIndexCode,
        camera.CameraType,
        camera.CameraTypeName,
        camera.UpdateTime,
        camera.Description,
        (SELECT cameragroup.cameraGroupName FROM cameragroup WHERE cameragroup.CameraGroupId = camera.CameraGroupId) cameraGroupName
        FROM Camera camera WHERE camera.CameraIndexCode IN
        <foreach collection="cameraIndexCodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findALl" resultType="com.siteweb.video.entity.Camera">
        SELECT camera.CameraId,
               camera.CameraName,
               camera.CameraIp,
               camera.CameraPort,
               camera.ChannelNumber,
               camera.CameraGroupId,
               camera.UserName,
               camera.Password,
               camera.VendorId,
               camera.VendorName,
               camera.CameraIndexCode,
               camera.CameraType,
               camera.CameraTypeName,
               camera.UpdateTime,
               camera.Description,
               (SELECT cameragroup.cameraGroupName FROM cameragroup WHERE cameragroup.CameraGroupId = camera.CameraGroupId) cameraGroupName
        FROM Camera camera
    </select>
    <select id="findOne" resultType="com.siteweb.video.entity.Camera">
        SELECT camera.CameraId,
               camera.CameraName,
               camera.CameraIp,
               camera.CameraPort,
               camera.ChannelNumber,
               camera.CameraGroupId,
               camera.UserName,
               camera.Password,
               camera.VendorId,
               camera.VendorName,
               camera.CameraIndexCode,
               camera.CameraType,
               camera.CameraTypeName,
               camera.UpdateTime,
               camera.Description,
               (SELECT cameragroup.cameraGroupName FROM cameragroup WHERE cameragroup.CameraGroupId = camera.CameraGroupId) cameraGroupName
        FROM Camera camera WHERE camera.CameraId = #{cameraId}
    </select>
</mapper>