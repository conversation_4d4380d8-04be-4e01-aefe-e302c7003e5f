<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.video.mapper.CameraPollGroupMapper">
    <resultMap id="pollGroupMap" type="com.siteweb.video.entity.CameraPollGroup">
        <id property="cameraPollGroupId" column="CameraPollGroupId"/>
        <result property="cameraPollGroupName" column="CameraPollGroupName"/>
        <result property="pollInterval" column="PollInterval"/>
        <result property="description" column="Description"/>
        <collection property="cameraPollGroupMaps" column="CameraPollGroupId" select="findGroupMap"/>
    </resultMap>
    <resultMap id="groupMapMap" type="com.siteweb.video.entity.CameraPollGroupMap">
        <id property="cameraPollGroupMapId" column="CameraPollGroupMapId"/>
        <result property="cameraPollGroupId" column="CameraPollGroupId"/>
        <result property="cameraId" column="CameraId"/>
        <association property="camera" column="CameraId" select="com.siteweb.video.mapper.CameraMapper.findOne"/>
    </resultMap>
    <select id="findAll" resultMap="pollGroupMap">
        SELECT poll.CameraPollGroupId, poll.CameraPollGroupName, poll.PollInterval, poll.Description
        FROM CameraPollGroup poll
    </select>
    <select id="findGroupMap" resultMap="groupMapMap">
        SELECT groupMap.CameraPollGroupMapId,
               groupMap.CameraPollGroupId,
               groupMap.CameraId
        FROM CameraPollGroupMap groupMap
        WHERE groupMap.CameraPollGroupId = #{cameraPollGroupId}
    </select>
</mapper>