package com.siteweb.video.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.video.entity.Camera;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CameraMapper extends BaseMapper<Camera> {
    /**
     * 查询所有的摄像头信息
     * @return {@link List}<{@link Camera}>
     */
    List<Camera> findALl();

    /**
     * 查找摄像头根据组别id
     * @param cameraGroupId 摄像头组id
     * @return {@link List}<{@link Camera}>
     */
    List<Camera> findByCameraGroupId(@Param("cameraGroupId") Integer cameraGroupId);


    /**
     * 查找为分组的摄像头
     * @return {@link List}<{@link Camera}>
     */
    List<Camera> getNoGroupCameras();

    /**
     * 将某摄像机组下的摄像机全置为null
     * 主要用于删除某摄像机组
     * @param cameraGroupId 摄像头组id
     */
    void updateCamerasGroupIdNull(@Param("cameraGroupId") Integer cameraGroupId);

    /**
     * 根据摄像头uuid批量获取摄像头
     * @param cameraIndexCodeList 摄像头uuid集合
     * @return {@link List}<{@link Camera}>
     */
    List<Camera> findByCameraIndexCodeIn(@Param("cameraIndexCodeList") List<String> cameraIndexCodeList);

    /**
     * 通过id查找摄像头信息
     * @param cameraId 相机id
     * @return {@link Camera}
     */
    Camera findOne(@Param("cameraId") Integer cameraId);

    /**
     * 批量插入摄像头
     * @param cameras 摄像头集合
     */
    void saveBatch(@Param("cameras") List<Camera> cameras);
}
