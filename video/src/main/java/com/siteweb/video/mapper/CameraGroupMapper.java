package com.siteweb.video.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.video.entity.CameraGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CameraGroupMapper extends BaseMapper<CameraGroup> {
    /**
     * 查找摄像机组,根据摄像机组父id
     * @param parentId 父id
     * @return {@link List}<{@link CameraGroup}>
     */
    List<CameraGroup> findByParentIdIs(@Param("parentId") Integer parentId);

    /**
     *  获取所有摄像机组
     * @return {@link List}<{@link CameraGroup}>
     */
    List<CameraGroup> findAll();
}
