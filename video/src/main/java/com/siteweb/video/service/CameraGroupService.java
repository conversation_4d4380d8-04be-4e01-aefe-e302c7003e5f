package com.siteweb.video.service;


import com.siteweb.video.entity.CameraGroup;
import com.siteweb.video.dto.CameraGroupDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public interface CameraGroupService {

    /**
     * 根据摄像头id查找其摄像头组
     * @param cameraId 摄像头id
     * @return {@link CameraGroup} 摄像头足
     */
    CameraGroup findOne(Integer cameraId);

    /**
     * 查询所有摄像头组
     * @return {@link List}<{@link CameraGroup}>
     */
    List<CameraGroup> findAll();

    /**
     * 创建摄像头组
     * @param cameraGroup 摄像头组信息
     * @return {@link CameraGroup}
     */
    CameraGroup create(CameraGroup cameraGroup);

    /**
     * 更新摄像头组
     * @param cameraGroup 摄像头组信息
     * @return {@link CameraGroup}
     */
    CameraGroup update(CameraGroup cameraGroup);

    /**
     * 根据id删除摄像头组
     * @param cameraGroupId 摄像头组id
     */
    void delete(Integer cameraGroupId);

    /**
     * 获取摄像头组树形结构
     * @return {@link List}<{@link CameraGroupDTO}> 摄像头组树形结构
     */
    List<CameraGroupDTO> getCameraGroupTrees();

    /**
     * 查询摄像头组的子集
     * @param cameraGroupId 摄像头组id
     * @return {@link List}<{@link CameraGroup}>
     */
    List<CameraGroup> getCameraGroupChildrens(Integer cameraGroupId);

}
