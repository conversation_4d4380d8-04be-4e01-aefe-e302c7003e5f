package com.siteweb.video.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.siteweb.video.entity.Camera;
import com.siteweb.video.manager.HKVideoManager;
import com.siteweb.video.dto.HKCameraPlayBackRequest;
import com.siteweb.video.dto.HKCameraPlayBackResponse;
import com.siteweb.video.dto.HKCameraPreviewRequest;
import com.siteweb.video.dto.HKCameraResourceDTO;
import com.siteweb.video.service.CameraService;
import com.siteweb.video.service.HKCameraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class HKCameraServiceImpl implements HKCameraService {


    @Autowired
    HKVideoManager hkVideoManager;

    @Autowired
    CameraService cameraService;

    @Override
    public List<HKCameraResourceDTO> getHKCameraResources() {
        return hkVideoManager.getCameraResourceDTOs();
    }

    @Override
    public List<HKCameraResourceDTO> getHKCameraResourceDTOs() {
        List<HKCameraResourceDTO> HKCameraResourceList = new ArrayList<>();
        try {
            List<HKCameraResourceDTO> HKCameraResources = hkVideoManager.getCameraResourceDTOs();
            if (HKCameraResources.isEmpty()) {
                return HKCameraResourceList;
            }
            List<Camera> cameras = cameraService.findAll();
            if (cameras.isEmpty()) {
                return HKCameraResources;
            }
            for (HKCameraResourceDTO HKCameraResource : HKCameraResources) {
                long count = cameras.stream().filter(oc -> oc.getCameraIndexCode().equals(HKCameraResource.getCameraIndexCode())).count();
                if (count < 1) {
                    HKCameraResourceList.add(HKCameraResource);
                }
            }
        } catch (Exception ex) {
            log.error("HKCameraServiceImpl  Message: {} StackTrace: {}", ex.getMessage(), ExceptionUtil.stacktraceToString(ex));
        }
        return HKCameraResourceList;
    }

    @Override
    public String getCameraPreviewURL(HKCameraPreviewRequest hkCameraPreviewRequest) {
        if (hkCameraPreviewRequest == null || hkCameraPreviewRequest.getCameraIndexCode() == null ||
                hkCameraPreviewRequest.getCameraIndexCode().isEmpty()) {
            return null;
        }
        return hkVideoManager.getCameraPreviewURL(hkCameraPreviewRequest);
    }

    @Override
    public HKCameraPlayBackResponse getCameraPlaybackURL(HKCameraPlayBackRequest HKCameraPlayBackRequest) {
        if (HKCameraPlayBackRequest == null || HKCameraPlayBackRequest.getCameraIndexCode() == null ||
                HKCameraPlayBackRequest.getBeginDate() == null || HKCameraPlayBackRequest.getEndTime() == null) {
            return null;
        }
        HKCameraPlayBackResponse cameraPlayBackResponse = hkVideoManager.getCameraPlaybackURL(HKCameraPlayBackRequest);
        return cameraPlayBackResponse;
    }

    @Override
    public List<String> getCameraIndexCodes(List<HKCameraPreviewRequest> hkCameraPreviewRequests) {
        List<String> cameraPreviewURLs = new ArrayList<>();
        if (hkCameraPreviewRequests.isEmpty()) {
            return cameraPreviewURLs;
        }
        try {
            for (HKCameraPreviewRequest hkCameraPlayBackRequest : hkCameraPreviewRequests) {
                String url = getCameraPreviewURL(hkCameraPlayBackRequest);
                cameraPreviewURLs.add(url);
            }
        } catch (Exception ex) {
            log.error("HKCameraServiceImpl getCameraIndexCodes Message: {} StackTrace: {}", ex.getMessage(), ExceptionUtil.stacktraceToString(ex));
        }
        return cameraPreviewURLs;
    }
}
