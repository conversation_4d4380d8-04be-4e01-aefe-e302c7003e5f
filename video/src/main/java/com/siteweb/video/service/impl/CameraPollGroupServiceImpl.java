package com.siteweb.video.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.video.entity.Camera;
import com.siteweb.video.entity.CameraPollGroup;
import com.siteweb.video.entity.CameraPollGroupMap;
import com.siteweb.video.mapper.CameraPollGroupMapper;
import com.siteweb.video.dto.CameraPollGroupDTO;
import com.siteweb.video.service.CameraPollGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CameraPollGroupServiceImpl implements CameraPollGroupService {

    @Autowired
    CameraPollGroupMapper cameraPollGroupMapper;

    @Override
    public List<CameraPollGroup> findAll() {
        return cameraPollGroupMapper.findAll();
    }

    @Override
    public CameraPollGroup findOne(Integer cameraPollGroupId) {
        return cameraPollGroupMapper.selectById(cameraPollGroupId);
    }

    @Override
    public CameraPollGroup create(CameraPollGroup cameraPollGroup) {
        cameraPollGroupMapper.insert(cameraPollGroup);
        return cameraPollGroup;
    }

    @Override
    public CameraPollGroup update(CameraPollGroup cameraPollGroup) {
        cameraPollGroupMapper.insert(cameraPollGroup);
        return cameraPollGroup;
    }

    @Override
    public void delete(Integer cameraPollGroupId) {
        cameraPollGroupMapper.deleteById(cameraPollGroupId);
    }

    @Override
    public List<CameraPollGroupDTO> getCameraPollGroupDTOs() {
        List<CameraPollGroupDTO> cameraPollGroupDTOList = new ArrayList<>();
        List<CameraPollGroup> cameraPollGroups = cameraPollGroupMapper.selectList(Wrappers.emptyWrapper());
        if (CollUtil.isNotEmpty(cameraPollGroups)) {
            for (CameraPollGroup cameraPollGroup : cameraPollGroups) {
                CameraPollGroupDTO cameraPollGroupDTO = getCameraPollGroupMapsCameras(cameraPollGroup);
                cameraPollGroupDTOList.add(cameraPollGroupDTO);
            }
        }
        return cameraPollGroupDTOList;
    }

    @Override
    public CameraPollGroupDTO getCameraPollGroupDTOById(Integer cameraPollGroupId) {
        CameraPollGroupDTO cameraPollGroupDTO = new CameraPollGroupDTO();
        CameraPollGroup cameraPollGroup = cameraPollGroupMapper.selectById(cameraPollGroupId);
        if (cameraPollGroup != null) {
            cameraPollGroupDTO = getCameraPollGroupMapsCameras(cameraPollGroup);
        }
        return cameraPollGroupDTO;
    }

    private CameraPollGroupDTO getCameraPollGroupMapsCameras(CameraPollGroup cameraPollGroup) {
        List<Camera> cameras = new ArrayList<>();
        CameraPollGroupDTO cameraPollGroupDTO = new CameraPollGroupDTO();
        BeanUtils.copyProperties(cameraPollGroup, cameraPollGroupDTO);
        if (CollUtil.isNotEmpty(cameraPollGroup.getCameraPollGroupMaps())) {
            for (CameraPollGroupMap cameraPollGroupMap : cameraPollGroup.getCameraPollGroupMaps()) {
                if (cameraPollGroupMap.getCamera() != null) {
                    cameras.add(cameraPollGroupMap.getCamera());
                }
            }
            cameraPollGroupDTO.setCameras(cameras);
        }
        return cameraPollGroupDTO;
    }
}
