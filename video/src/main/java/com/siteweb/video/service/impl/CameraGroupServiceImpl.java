package com.siteweb.video.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.siteweb.video.dto.CameraGroupDTO;
import com.siteweb.video.entity.Camera;
import com.siteweb.video.entity.CameraGroup;
import com.siteweb.video.mapper.CameraGroupMapper;
import com.siteweb.video.service.CameraGroupService;
import com.siteweb.video.service.CameraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class CameraGroupServiceImpl implements CameraGroupService {

    @Autowired
    CameraGroupMapper cameraGroupMapper;

    @Autowired
    CameraService cameraService;

    @Override
    public List<CameraGroup> findAll() {
        return cameraGroupMapper.findAll();
    }

    @Override
    public List<CameraGroupDTO> getCameraGroupTrees() {
        List<CameraGroupDTO> cameraGroupTrees = new ArrayList<>();
        try {
            //查询所有摄像机组
            List<CameraGroup> cameraGroupList = cameraGroupMapper.findByParentIdIs(0);
            if (cameraGroupList.isEmpty()) {
                cameraGroupTrees = generateFirstLevelTrees();
                return cameraGroupTrees;
            }
            cameraGroupTrees = generateCameraGroupTrees(cameraGroupList);
            CameraGroupDTO cameraGroupDTO = generateNoGroupCameras();
            if (cameraGroupDTO != null) {
                cameraGroupTrees.add(cameraGroupDTO);
            }
        } catch (Exception ex) {
            log.error("CameraGroupService getCameraGroupTrees Message: {} StackTrace: {}", ex.getMessage(), ExceptionUtil.stacktraceToString(ex));
            return cameraGroupTrees;
        }
        return cameraGroupTrees;
    }

    private CameraGroupDTO generateNoGroupCameras() {
        CameraGroupDTO cameraGroupDTO = new CameraGroupDTO();
        List<Camera> cameras = cameraService.getNoGroupCameras();
        if (cameras.isEmpty()) {
            return null;
        }
        cameraGroupDTO.setCameraGroupId(-1);
        cameraGroupDTO.setCameraGroupName("未分组");
        cameraGroupDTO.setResourceType(1);
        cameraGroupDTO.setChildren(generateCameraGroupDTOFromCameras(cameras));
        return cameraGroupDTO;
    }

    private List<CameraGroupDTO> generateFirstLevelTrees() {
        List<CameraGroupDTO> cameraGroupTrees = new ArrayList<>();
        List<Camera> cameras = cameraService.findAll();
        if (cameras.isEmpty()) {
            return cameraGroupTrees;
        }
        CameraGroupDTO cameraGroupDTO = new CameraGroupDTO();
        cameraGroupDTO.setResourceType(1);
        cameraGroupDTO.setCameraGroupId(-1);
        cameraGroupDTO.setCameraGroupName("未分组");
        cameraGroupDTO.setChildren(generateCameraGroupDTOFromCameras(cameras));
        cameraGroupTrees.add(cameraGroupDTO);
        return cameraGroupTrees;
    }

    private List<CameraGroupDTO> generateCameraGroupTrees(List<CameraGroup> cameraGroupList) {
        if (cameraGroupList.isEmpty()) {
            return Collections.emptyList();
        }
        List<CameraGroupDTO> cameraGroupTrees = new ArrayList<>();
        for (CameraGroup cameraGroup : cameraGroupList) {
            List<CameraGroupDTO> childrens = new ArrayList<>();
            CameraGroupDTO cameraGroupDTO = new CameraGroupDTO();
            cameraGroupDTO.setResourceType(1);
            BeanUtils.copyProperties(cameraGroup, cameraGroupDTO);
            childrens.addAll(generateCameraGroupTrees(cameraGroup.getChildren()));
            List<Camera> cameras = cameraService.getByCameraGroupId(cameraGroup.getCameraGroupId());
            childrens.addAll(generateCameraGroupDTOFromCameras(cameras));
            cameraGroupDTO.setChildren(childrens);
            cameraGroupTrees.add(cameraGroupDTO);
        }
        return cameraGroupTrees;
    }

    private List<CameraGroupDTO> generateCameraGroupDTOFromCameras(List<Camera> cameras) {
        List<CameraGroupDTO> childrenGroups = new ArrayList<>();
        for (Camera camera : cameras) {
            CameraGroupDTO childCameraGroupDTO = new CameraGroupDTO();
            childCameraGroupDTO.setResourceType(2);
            BeanUtils.copyProperties(camera, childCameraGroupDTO);
            childrenGroups.add(childCameraGroupDTO);
        }
        return childrenGroups;
    }

    @Override
    public CameraGroup findOne(Integer cameraGroupId) {
        return cameraGroupMapper.selectById(cameraGroupId);
    }

    @Override
    public CameraGroup create(CameraGroup cameraGroup) {
        cameraGroupMapper.insert(cameraGroup);
        return cameraGroup;
    }

    @Override
    public CameraGroup update(CameraGroup cameraGroup) {
        cameraGroupMapper.updateById(cameraGroup);
        return cameraGroup;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Integer cameraGroupId) {
        cameraService.updateCamerasGroupIdNull(cameraGroupId);
        cameraGroupMapper.deleteById(cameraGroupId);
    }

    @Override
    public List<CameraGroup> getCameraGroupChildrens(Integer cameraGroupId) {
        return cameraGroupMapper.findByParentIdIs(cameraGroupId);
    }
}
