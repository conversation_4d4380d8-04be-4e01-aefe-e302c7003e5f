package com.siteweb.video.service;

import com.siteweb.video.entity.CameraPollGroup;
import com.siteweb.video.dto.CameraPollGroupDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public interface CameraPollGroupService {

    /**
     * 根据id获取摄像头采集周期
     * @param cameraPollGroupId 摄像头采集周期id
     * @return {@link CameraPollGroup}
     */
    CameraPollGroup findOne(Integer cameraPollGroupId);

    /**
     * 查询所有摄像头采集周期
     * @return {@link List}<{@link CameraPollGroup}> 摄像头采集周期
     */
    List<CameraPollGroup> findAll();

    /**
     * 创建摄像头采集周期信息
     * @param cameraPollGroup 摄像头采集周期信息
     * @return {@link CameraPollGroup}
     */
    CameraPollGroup create(CameraPollGroup cameraPollGroup);

    /**
     * 更新摄像头采集周期信息
     * @param cameraPollGroup 摄像头采集周期信息
     * @return {@link CameraPollGroup}
     */
    CameraPollGroup update(CameraPollGroup cameraPollGroup);

    /**
     * 根据id删除摄像头采集周期信息
     * @param cameraPollGroupId 摄像头采集周期信息Id
     */
    void delete(Integer cameraPollGroupId);

    /**
     * 获取摄像头采集周期与摄像头关系信息
     * @return {@link List}<{@link CameraPollGroupDTO}> 摄像头采集周期信息Dto
     */
    List<CameraPollGroupDTO> getCameraPollGroupDTOs();

    /**
     * 根据采集信息id获取摄像头采集周期信息
     * @param cameraPollGroupId 摄像头采集周期信息id
     * @return {@link CameraPollGroupDTO}
     */
    CameraPollGroupDTO getCameraPollGroupDTOById(Integer cameraPollGroupId);
}
