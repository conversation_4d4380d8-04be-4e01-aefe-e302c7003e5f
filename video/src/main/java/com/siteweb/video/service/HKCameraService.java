package com.siteweb.video.service;

import com.siteweb.video.dto.HKCameraPlayBackRequest;
import com.siteweb.video.dto.HKCameraPlayBackResponse;
import com.siteweb.video.dto.HKCameraPreviewRequest;
import com.siteweb.video.dto.HKCameraResourceDTO;

import java.util.List;

public interface HKCameraService {

    List<HKCameraResourceDTO> getHKCameraResources();

    List<HKCameraResourceDTO> getHKCameraResourceDTOs();

    String getCameraPreviewURL(HKCameraPreviewRequest hkCameraPreviewRequest);

    HKCameraPlayBackResponse getCameraPlaybackURL(HKCameraPlayBackRequest HKCameraPlayBackRequest);

    List<String> getCameraIndexCodes(List<HKCameraPreviewRequest> hkCameraPreviewRequests);
}
