package com.siteweb.video.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.video.dto.*;
import com.siteweb.video.entity.Camera;
import com.siteweb.video.entity.CameraGroup;
import com.siteweb.video.manager.HKVideoManager;
import com.siteweb.video.mapper.CameraMapper;
import com.siteweb.video.service.CameraGroupService;
import com.siteweb.video.service.CameraService;
import com.siteweb.video.service.HKCameraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CameraServiceImpl implements CameraService {


    @Autowired
    CameraMapper cameraMapper;

    @Autowired
    HKVideoManager hkVideoManager;

    @Autowired
    @Lazy
    HKCameraService hkCameraService;
    @Autowired
    @Lazy
    CameraGroupService cameraGroupService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Override
    public List<Camera> findAll() {
        return cameraMapper.findALl();
    }

    @Override
    public Camera findOne(Integer cameraId) {
        return cameraMapper.findOne(cameraId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportErrorInfoDTO> batchImport(List<CameraBatchImportDTO> cameras){
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>(cameras.size());
        Map<String, Integer> cameraGroupMap = cameraGroupService.findAll().stream().collect(Collectors.toMap(CameraGroup::getCameraGroupName, CameraGroup::getCameraGroupId, (v1, v2) -> v1));
        List<Camera> newCameras = new ArrayList<>(cameras.size());
        for (int i = 0; i < cameras.size(); i++) {
            CameraBatchImportDTO importDTO = cameras.get(i);
            Integer cameraGroupId = importDTO.getCameraGroupId();
            if (Objects.isNull(cameraGroupId) && !Objects.isNull(importDTO.getCameraGroupName())) {
                cameraGroupId = cameraGroupMap.get(importDTO.getCameraGroupName());
                if (Objects.isNull(cameraGroupId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "cameraGroupName", localeMessageSourceUtil.getMessage("common.notFound") + importDTO.getCameraGroupName()));
                    continue;
                }
            }
            Camera camera = BeanUtil.copyProperties(importDTO, Camera.class);
            camera.setCameraGroupId(cameraGroupId);
            newCameras.add(camera);
        }
        if (CollUtil.isNotEmpty(importErrorInfoList)) {
            return importErrorInfoList;
        }
        for (Camera camera : newCameras) {
            createOrUpdate(camera);
        }
        return Collections.emptyList();
    }

    @Override
    public Camera createOrUpdate(Camera camera) {
        if (ObjectUtil.isNull(camera)) {
            return null;
        }
        camera.setUpdateTime(new Date());
        //主键存在则更新
        if (ObjectUtil.isNotNull(camera.getCameraId())) {
            cameraMapper.updateById(camera);
            return camera;
        }
        //否则添加
        cameraMapper.insert(camera);
        return camera;
    }

    @Override
    public List<Camera> batchCreateCameras() {
        List<HKCameraResourceDTO> hkCameraResources = hkCameraService.getHKCameraResourceDTOs();
        if (CollUtil.isEmpty(hkCameraResources)) {
            return Collections.emptyList();
        }
        List<Camera> cameras = new ArrayList<>();
        for (HKCameraResourceDTO hkCameraResourceDTO : hkCameraResources) {
            Camera camera = beanCopyProperties(hkCameraResourceDTO);
            cameras.add(camera);
        }
        if (CollUtil.isNotEmpty(cameras)) {
            //批量添加 sql for
            cameraMapper.saveBatch(cameras);
        }
        return cameras;
    }

    @Override
    public Camera update(Camera camera) {
        if (camera != null && camera.getCameraId() != null) {
            camera.setUpdateTime(new Date());
            cameraMapper.updateById(camera);
        }
        return camera;
    }

    @Override
    public void delete(Integer cameraId) {
        cameraMapper.deleteById(cameraId);
    }

    @Override
    public List<Camera> getByCameraGroupId(Integer cameraGroupId) {
        return cameraMapper.findByCameraGroupId(cameraGroupId);
    }

    @Override
    public List<Camera> getNoGroupCameras() {
        return cameraMapper.getNoGroupCameras();
    }

    @Override
    public void updateCamerasGroupIdNull(Integer camerasGroupId) {
        cameraMapper.updateCamerasGroupIdNull(camerasGroupId);
    }

    @Override
    public List<CameraConnectStatusDTO> getCamerasConnectStatus() {
        List<CameraConnectStatusDTO> cameraConnectStatusDTOList = new ArrayList<>();
        List<Camera> cameras = cameraMapper.selectList(Wrappers.emptyWrapper());
        List<String> hkCameraIndexCodes = generateCameraConnectStatusDTO(cameras, cameraConnectStatusDTOList);
        //远程摄像头为空,默认所有摄像头不在线
        if (CollUtil.isEmpty(hkCameraIndexCodes)) {
            return cameraConnectStatusDTOList;
        }
        List<HKConnectStatusCamera> hkConnectStatusCameras = hkVideoManager.getCameraConnectStatus(hkCameraIndexCodes);
        updateCameraConnectStatus(cameraConnectStatusDTOList, hkConnectStatusCameras);
        return cameraConnectStatusDTOList;
    }

    @Override
    public  List<CameraConnectStatusDTO> getCameraConnectStatusByKey(List<String> cameraIndexCodeList){
        if (CollUtil.isEmpty(cameraIndexCodeList)) {
            return Collections.emptyList();
        }
        List<HKConnectStatusCamera> hkConnectStatusCameras = hkVideoManager.getCameraConnectStatus(cameraIndexCodeList);
        List<CameraConnectStatusDTO> cameraConnectStatusDTOList = getCameraConnectStatusDTOByIndexCode(cameraIndexCodeList);
        updateCameraConnectStatus(cameraConnectStatusDTOList, hkConnectStatusCameras);
        return cameraConnectStatusDTOList;
    }

    private  List<CameraConnectStatusDTO> getCameraConnectStatusDTOByIndexCode(List<String> cameraIndexCodeList) {
        if (CollUtil.isEmpty(cameraIndexCodeList)) {
            return Collections.emptyList();
        }
        List<Camera> cameras = cameraMapper.findByCameraIndexCodeIn(cameraIndexCodeList);
        if (CollUtil.isEmpty(cameras)) {
            return Collections.emptyList();
        }
        List<CameraConnectStatusDTO> cameraConnectStatusDTOList = new ArrayList<>();
        for (Camera camera : cameras) {
            cameraConnectStatusDTOList.add(newCameraConnectStatusDTO(camera));
        }
        return cameraConnectStatusDTOList;
    }

    /**
     * 更新摄像头连接状态
     *
     * @param cameraConnectStatusDTOList
     * @param hkConnectStatusCameras
     */
    private void updateCameraConnectStatus(List<CameraConnectStatusDTO> cameraConnectStatusDTOList, List<HKConnectStatusCamera> hkConnectStatusCameras) {
        if (CollUtil.isEmpty(hkConnectStatusCameras) || CollUtil.isEmpty(cameraConnectStatusDTOList)) {
            return;
        }
        for (CameraConnectStatusDTO cameraConnectStatusDTO : cameraConnectStatusDTOList) {
            Optional<HKConnectStatusCamera> connectStatusCamera = hkConnectStatusCameras.stream()
                                                                          .filter(oc -> oc.getIndexCode()
                                                                                          .equals(cameraConnectStatusDTO.getCameraIndexCode()))
                                                                          .findFirst();
            connectStatusCamera.ifPresent(hkConnectStatusCamera -> cameraConnectStatusDTO.setCameraOnlineState(CameraOnlineState.valueOf(hkConnectStatusCamera.getOnline())));
        }
    }

    private List<String> generateCameraConnectStatusDTO(List<Camera> cameras, List<CameraConnectStatusDTO> cameraConnectStatusDTOList) {
        if (CollUtil.isEmpty(cameras)) {
            return Collections.emptyList();
        }
        List<String> hkCameraIndexCodes = new ArrayList<>();
        for (Camera camera : cameras) {
            CameraConnectStatusDTO cameraConnectStatusDTO = newCameraConnectStatusDTO(camera);
            cameraConnectStatusDTOList.add(cameraConnectStatusDTO);
            if (camera.getVendorId() != null && camera.getVendorId() == 2) {
                hkCameraIndexCodes.add(camera.getCameraIndexCode());
            }
        }
        return hkCameraIndexCodes;
    }

    /**
     * 设置默认为不在线的摄像头
     *
     * @param camera 摄像头
     * @return {@link CameraConnectStatusDTO}
     */
    private CameraConnectStatusDTO newCameraConnectStatusDTO(Camera camera){
        CameraConnectStatusDTO cameraConnectStatusDTO = new CameraConnectStatusDTO();
        BeanUtils.copyProperties(camera, cameraConnectStatusDTO);
        cameraConnectStatusDTO.setCameraOnlineState(CameraOnlineState.UNREGISTER);
        return cameraConnectStatusDTO;
    }

    private Camera beanCopyProperties(HKCameraResourceDTO hkCameraResourceDTO) {
        Camera camera = new Camera();
        camera.setCameraName(hkCameraResourceDTO.getCameraName());
        camera.setCameraIndexCode(hkCameraResourceDTO.getCameraIndexCode());
        camera.setCameraIp(hkCameraResourceDTO.getIp());
        camera.setCameraPort(Integer.parseInt(hkCameraResourceDTO.getPort()));
        camera.setCameraType(Integer.parseInt(hkCameraResourceDTO.getCameraType()));
        camera.setCameraTypeName(hkCameraResourceDTO.getCameraTypeName());
        camera.setChannelNumber(hkCameraResourceDTO.getChannelNo());
        camera.setUserName(hkCameraResourceDTO.getUserName());
        camera.setPassword(hkCameraResourceDTO.getPassword());
        camera.setVendorId(2);
        camera.setVendorName(hkCameraResourceDTO.getManufacturer());
        return camera;
    }
}
