package com.siteweb.video.service;


import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.video.dto.CameraBatchImportDTO;
import com.siteweb.video.entity.Camera;
import com.siteweb.video.dto.CameraConnectStatusDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public interface CameraService {

    /**
     * 根据id查询摄像头
     * @param cameraId 摄像头id
     * @return {@link Camera}
     */
    Camera findOne(Integer cameraId);

    /**
     * 查询所有摄像头
     * @return {@link List}<{@link Camera}> 摄像头
     */
    List<Camera> findAll();

    /**
     * 批量创建摄像头
     * @param cameras 摄像头集合
     * @return {@link List}<{@link Camera}>
     */
    List<ImportErrorInfoDTO> batchImport(List<CameraBatchImportDTO> cameras);

    /**
     * 创建或更新摄像头信息(主要根据是否有主键id来选择是创建还是更新)
     * @param camera 摄像头信息
     * @return {@link Camera}
     */
    Camera createOrUpdate(Camera camera);

    /**
     * 更新摄像头
     * @param camera 摄像头信息
     * @return {@link Camera}
     */
    Camera update(Camera camera);

    /**
     * 根据id删除摄像头
     * @param cameraId 摄像头id
     */
    void delete(Integer cameraId);

    /**
     * 根据摄像头组查询该组下所有摄像头
     * @param cameraGroupId 摄像头组id
     * @return {@link List}<{@link Camera}>
     */
    List<Camera> getByCameraGroupId(Integer cameraGroupId);

    /**
     * 查询未分组的摄像头信息
     * @return {@link List}<{@link Camera}> 未分组的摄像头
     */
    List<Camera> getNoGroupCameras();

    /**
     * 将某摄像机组下的摄像机全置为null
     * 主要用于删除某摄像机组
     * @param camerasGroupId 目标摄像头组
     */
    void updateCamerasGroupIdNull(Integer camerasGroupId);

    /**
     * 获取摄像头连接状态
     * @return {@link List}<{@link CameraConnectStatusDTO}>
     */
    List<CameraConnectStatusDTO> getCamerasConnectStatus();

    /**
     * 根据摄像头uuid查询摄像头状态
     * @param cameraIndexCodeList 摄像头uuid集合
     * @return {@link List}<{@link CameraConnectStatusDTO}>
     */
    List<CameraConnectStatusDTO> getCameraConnectStatusByKey(List<String> cameraIndexCodeList);

    /**
     * 批量创建摄像头
     * @return {@link List}<{@link Camera}>
     */
    List<Camera> batchCreateCameras();
}
