package com.siteweb.video.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class HKCameraPlayBackResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final Logger log = LoggerFactory.getLogger(HKCameraPlayBackResponse.class);

    /**
     * 返回码，0:接口业务处理成功，
     */
    private String code;
    /**
     * 返回描述
     */
    private String msg;
    /**
     * 返回数据
     */
    private HKCameraPlayBackPage data;
}
