package com.siteweb.video.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * hkcamera资源
 *
 * <AUTHOR>
 * @date 2022/04/27
 */
@Data
public class HKCameraResource implements Serializable {
    /**
     * 海拔
     */
    private String altitude;
    /**
     * 监控点唯一标识
     */
	private String cameraIndexCode;
    /**
     * 监控点名称
     */
    private String cameraName;
    /**
     * 监控点类型
     */
    private String cameraType;
    /**
     * 监控点类型说明
     */
    private String cameraTypeName;
    /**
     * 设备能力集
     */
    private String capabilitySet;
    /**
     * 能力集说明
     */
    private String capabilitySetName;
    /**
     * 智能分析能力集，扩展字段，暂不使用
     */
    private  String intelligentSet;
    /**
     * 智能分析能力集说明，扩展字段，暂不使用
     */
    private String intelligentSetName;
    /**
     * 通道编号
     */
    private String  channelNo;
    /**
     * 通道类型
     */
    private String channelType;
    /**
     * 通道类型说明
     */
    private  String channelTypeName;
    /**
     * 所属编码设备唯一标识
     */
    private String  encodeDevIndexCode;
    /**
     * 所属设备类型，扩展字段，暂不使用
     */
    private String encodeDevResourceType;
    /**
     * 所属设备类型说明，扩展字段，暂不使用
     */
    private String encodeDevResourceTypeName;
    /**
     * 监控点国标编号，即外码编号externalIndexCode
     */
    private String gbIndexCode;
    /**
     * 安装位置
     */
    private String installLocation;
    /**
     * 键盘控制码
     */
    private String keyBoardCode;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 摄像机像素（1-普通像素，2-130万高清，3-200万高清，4-300万高清），扩展字段，暂不使用
     */
    private String pixel;
    /**
     * 云镜类型（1-全方位云台（带转动和变焦），2-只有变焦,不带转动，3-只有转动，不带变焦，4-无云台，无变焦），扩展字段，暂不使用
     */
    private String ptz;
    /**
     * 云镜类型说明，扩展字段，暂不使用
     */
    private String ptzName;
    /**
     * 云台控制(1-DVR，2-模拟矩阵，3-MU4000，4-NC600)，扩展字段，暂不使用
     */
    private String ptzController;
    /**
     * 云台控制说明，扩展字段，暂不使用
     */
    private String ptzControllerName;
    /**
     * 录像存储位置
     */
    private String recordLocation;
    /**
     * 录像存储位置说明
     */
    private String recordLocationName;
    /**
     * 所属区域唯一标识
     */
    private String regionIndexCode;
    /**
     * 在线状态（0-未知，1-在线，2-离线），扩展字段，暂不使用
     */
    private String status;
    /**
     * 状态说明
     */
    private String statusName;
    /**
     * 传输协议
     * 0:UDP
     * 1:TCP 默认是TCP
     */
    private String transType;
    /**
     * 传输协议类型说明
     */
    private String transTypeName;
    /**
     * 接入协议
     */
    private String treatyType;
    /**
     * 接入协议类型说明
     */
    private String treatyTypeName;
    /**
     * 可视域相关（JSON格式），扩展字段，暂不使用
     */
    private String viewshed;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
}
