package com.siteweb.video.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CameraBatchImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *  摄像机名称
     */
    private String cameraName;
    /**
     * 摄像机IP
     */
    private String cameraIp;
    /**
     * 摄像机端口号
     */
    private Integer cameraPort;
    /**
     * 管道号
     */
    private String channelNumber;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    private String password;
    /**
     * 厂商id
     */
    private Integer vendorId;
    /**
     * 厂商名称
     */
    private String vendorName;
    /**
     * 设备UUID
     */
    private String cameraIndexCode;
    /**
     *  型号id
     */
    private Integer cameraType;
    /**
     * 型号
     */
    private String cameraTypeName;
    /**
     * 更新时间
     */
    private Date updateTime;

    private String cameraGroupName;

    private Integer cameraGroupId;
    /**
     *  描述
     */
    private String description;
}
