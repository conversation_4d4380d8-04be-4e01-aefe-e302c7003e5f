package com.siteweb.video.dto;

import lombok.Data;

@Data
public class HKConnectStatusCamera {
    /**
     * 设备类型
     */
    private String deviceType;
    /**
     * 地区指数代码
     */
    private String regionIndexCode;
    /**
     * 收集时间
     */
    private String collectTime;
    /**
     * 设备唯一id
     */
    private String deviceIndexCode;
    /**
     * 端口
     */
    private String port;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 指数代码
     */
    private String indexCode;
    /**
     * 在线状态
     */
    private Integer online;
    private String cn;
    /**
     * 协议类型
     */
    private String treatyType;
    /**
     * 制造商
     */
    private String manufacturer;
}

