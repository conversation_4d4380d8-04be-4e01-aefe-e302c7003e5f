package com.siteweb.video.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class HKCameraResourceDTO implements Serializable {
    /**
     * 监控点唯一标识
     */
	private String cameraIndexCode;
    /**
     * 监控点名称
     */
    private String cameraName;
    /**
     * 监控点类型
     */
    private String cameraType;
    /**
     * 监控点类型说明
     */
    private String cameraTypeName;
    /**
     * 通道编号
     */
    private String  channelNo;
    /**
     * 通道类型
     */
    private String channelType;
    /**
     * 通道类型说明
     */
    private  String channelTypeName;
    /**
     * 所属编码设备唯一标识
     */
    private String  encodeDevIndexCode;
    /**
     * 所属区域唯一标识
     */
    private String regionIndexCode;
    /**
     * 在线状态（0-未知，1-在线，2-离线），扩展字段，暂不使用
     */
    private String status;
    /**
     * 状态说明
     */
    private String statusName;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 编码设备登录用户名
     */
    private  String userName;
    /**
     * 编码设备登录密码
     */
    private String  password;
    /**
     * IP地址
     */
    private String ip;
    /**
     * 厂商
     */
    private  String manufacturer;
    /**
     * 端口
     */
    private String port;
    /**
     * 设备系列
     */
    private String deviceType;
    /**
     * 设备序列号
     */
    private String devSerialNum;
}
