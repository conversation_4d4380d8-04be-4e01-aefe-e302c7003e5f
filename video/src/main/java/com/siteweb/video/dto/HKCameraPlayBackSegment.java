package com.siteweb.video.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
public class HKCameraPlayBackSegment {

    private String beginTime;

    private String endTime;

    private Long size;

    private Integer lockType;

    public Date getBeginDate() {
        Date beginDate = null;
        if (beginTime != null) {

            String timeDate = beginTime.substring(0, beginTime.lastIndexOf("+"));
            beginDate = stringToDate(timeDate);
        }
        return beginDate;
    }

    public Date getEndDate() {
        Date endDate = null;
        if (endTime != null) {
            String timeDate = endTime.substring(0, endTime.lastIndexOf("+"));
            endDate = stringToDate(timeDate);
        }
        return endDate;
    }

    private Date stringToDate(String timeDate){
        Date tdate = null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
        try {
            tdate = simpleDateFormat.parse(timeDate);
        } catch (ParseException e) {
            return null;
        }
        return tdate;
    }
}
