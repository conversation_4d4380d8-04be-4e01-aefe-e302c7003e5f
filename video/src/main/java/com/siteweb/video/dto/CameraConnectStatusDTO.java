package com.siteweb.video.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CameraConnectStatusDTO {

    private Integer cameraId;
    /**
     * 摄像机名称
     */
    private String cameraName;

    /**
     * 摄像头uuid
     */
    private String cameraIndexCode;
    /**
     * 厂商id
     */
    private Integer vendorId;
    /**
     * 厂商名称
     */
    private String vendorName;

    /**
     * 摄像头在线状态 1-在线，0-离线，-1-未检测
     */
    private CameraOnlineState cameraOnlineState;
}
