package com.siteweb.video.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class HKCameraPreviewRequest {

    /**
     * 监控点唯一标识
     */
    private String cameraIndexCode;

    /**
     * 码流类型，
     * 0:主码流
     * 1:子码流
     * 2:第三码流
     * 参数不填，默认为主码流
     */
    private Integer streamType;

    /**
     * 取流协议（应用层协议）
     * “rtsp”:RTSP协议,
     * “rtmp”:RTMP协议,
     * “hls”:HLS协议（HLS协议只支持海康SDK协议、EHOME协议、GB28181协议、ONVIF协议接入的设备；只支持H264视频编码和AAC音频编码）,参数不填，默认为RTSP协议
     */
    private String protocol;

    /**
     * 传输协议（传输层协议）
     * 0:UDP
     * 1:TCP 默认是TCP
     */
    private Integer transmode;

    /**
     * 标识扩展内容，格式：key=value，
     * 调用方根据其播放控件支持的解码格式选择相应的封装类型
     */
    private String expand;
}
