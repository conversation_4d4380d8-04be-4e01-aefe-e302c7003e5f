package com.siteweb.video.dto;

import com.siteweb.video.entity.Camera;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CameraPollGroupDTO {

    /**
     * 摄像头组id
     */
    private Integer cameraPollGroupId;
    /**
     * 摄像机名称
     */
    private String cameraPollGroupName;

    /**
     * 轮询间隔
     */
    private Integer pollInterval;
    /**
     * 描述
     */
    private String description;

    /**
     * 摄像头集合
     */
    private List<Camera> cameras;
}
