package com.siteweb.video.dto;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
public enum CameraOnlineState {

    // 1-在线，0-离线，-1-未检测
    ONLINE(1),

    OFFLINE(0),

    UNREGISTER(-1);

    private int value = 0;

    private CameraOnlineState(int value) {
        this.value = value;
    }

    public static CameraOnlineState valueOf(int value) {    //手写的从int到enum的转换函数
        return switch (value) {
            case 0 -> OFFLINE;
            case 1 -> ONLINE;
            case -1 -> UNREGISTER;
            default -> null;
        };
    }

    public int value() {
        return this.value;
    }
}
