package com.siteweb.video.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class HKCameraPlayBackRequest {

    /**
     * 存储类型,0：中心存储 1：设备存储 默认为中心存储
     */
    private Integer recordLocation;

    /**
     * 摄像头uuid
     */
    private String cameraIndexCode;

    private Date beginDate;

    private Date endDate;

    private String beginTime;

    private String endTime;
}
