package com.siteweb.video.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CameraGroupDTO {

    /**
     * 摄像头组id
     */
    private Integer cameraGroupId;
    /**
     * 摄像头组名称
     */
    private String cameraGroupName;

    /**
     * 父id
     */
    private Integer parentId;
    /**
     * 描述
     */
    private String description;

    /**
     * 资源类型
     */
    private Integer resourceType;

    /**
     *
     */
    private Integer cameraId;
    /**
     * 摄像头名称
     */
    private String cameraName;
    /**
     * 摄像机IP
     */
    private String cameraIp;
    /**
     * 摄像机端口号
     */
    private Integer cameraPort;
    /**
     * 管道号
     */
    private String channelNumber;
    /**
     * 摄像头UUID
     */
    private String cameraIndexCode;
    /**
     * 摄像头型号
     */
    private String cameraTypeName;
    /**
     * 厂商名称
     */
    private String vendorName;
    /**
     * 子集
     */
    private List<CameraGroupDTO> children;
}
