package com.siteweb.video.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.video.entity.CameraGroup;
import com.siteweb.video.service.CameraGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(value = "摄像头组管理",tags = "摄像头组管理")
@RequestMapping("/api")
@RestController
public class CameraGroupController {

    @Autowired
    CameraGroupService cameraGroupService;

    @ApiOperation("获取所有摄像头组")
    @GetMapping(value = "/cameragroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraGroups() {
        return ResponseHelper.successful(cameraGroupService.findAll());
    }

    @ApiOperation("获取所有摄像头组树")
    @GetMapping(value = "/cameragrouptrees", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraGroupTrees() {
        return ResponseHelper.successful(cameraGroupService.getCameraGroupTrees());
    }


    @ApiOperation("根据id获取摄像头组")
    @GetMapping(value = "/cameragroups/{cameraGroupId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraGroupById(@PathVariable Integer cameraGroupId) {
        CameraGroup cameraGroup = cameraGroupService.findOne(cameraGroupId);
        return Optional.ofNullable(cameraGroup)
                       .map(ResponseHelper::successful)
                       .orElse(ResponseHelper.failed("cameraGroupId错误"));
    }

    @ApiOperation("根据id获取摄像头组的子节点")
    @GetMapping(value = "/cameragroups/children", params = "cameraGroupId")
    public ResponseEntity<ResponseResult> getChildCameraGroups(@RequestParam Integer cameraGroupId) {
        List<CameraGroup> cameraGroups = cameraGroupService.getCameraGroupChildrens(cameraGroupId);
        return Optional.ofNullable(cameraGroups)
                       .map(ResponseHelper::successful)
                       .orElse(ResponseHelper.failed("cameraGroupId错误"));
    }

    @ApiOperation("创建摄像头组")
    @PostMapping(value = "/cameragroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createCameraGroups(@RequestBody CameraGroup cameraGroup) {
        if (ObjectUtil.isNotNull(cameraGroup) && StrUtil.isBlank(cameraGroup.getCameraGroupName())) {
            return ResponseHelper.failed("摄像头组名称不能为空");
        }
        return ResponseHelper.successful(cameraGroupService.create(cameraGroup));
    }

    @ApiOperation("更新摄像头组")
    @PutMapping(value = "/cameragroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateCameras(@Valid @RequestBody CameraGroup cameraGroup) {
        if (cameraGroup.getCameraGroupId() == null) {
            return ResponseHelper.successful(cameraGroupService.create(cameraGroup));
        }
        CameraGroup result = cameraGroupService.update(cameraGroup);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("更新摄像头组")
    @DeleteMapping(value = "/cameragroups/{cameraGroupId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteCamera(@PathVariable Integer cameraGroupId) {
        cameraGroupService.delete(cameraGroupId);
        return ResponseHelper.successful();
    }
}
