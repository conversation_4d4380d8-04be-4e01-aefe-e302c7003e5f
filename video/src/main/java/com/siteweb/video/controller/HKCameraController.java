package com.siteweb.video.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.video.dto.HKCameraPlayBackRequest;
import com.siteweb.video.dto.HKCameraPlayBackResponse;
import com.siteweb.video.dto.HKCameraPreviewRequest;
import com.siteweb.video.dto.HKCameraResourceDTO;
import com.siteweb.video.service.HKCameraService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Slf4j
@RequestMapping("/api")
@RestController
public class HKCameraController {
    @Autowired
    HKCameraService hkCameraService;

    @GetMapping(value = "/hkcameraresource", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getHKCameraResources() {
        log.debug("REST request to get all System Actions");
        List<HKCameraResourceDTO> HKCameraResources = hkCameraService.getHKCameraResources();
        return Optional.ofNullable(HKCameraResources)
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.failed("camera not found"));
    }

    @GetMapping(value = "/camera/hk/resource", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraResources() {
        log.debug("REST request to get all System Actions");
        List<HKCameraResourceDTO> HKCameraResources = hkCameraService.getHKCameraResourceDTOs();
        return Optional.ofNullable(HKCameraResources)
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.failed("resource not found"));
    }


    @PostMapping(value = "/camera/hk/resource", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraIndexCodes(@RequestBody List<HKCameraPreviewRequest> hkCameraPreviewRequests) {
        if (hkCameraPreviewRequests.isEmpty()) {
            return ResponseHelper.failed("RequestBody is empty");
        }
        List<String> result = hkCameraService.getCameraIndexCodes(hkCameraPreviewRequests);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/camera/hk/playbackurl",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> ********************ByPost(@RequestBody HKCameraPlayBackRequest hkCameraPlayBackRequest) {
        HKCameraPlayBackResponse response = hkCameraService.********************(hkCameraPlayBackRequest);
        return Optional.ofNullable(response)
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.failed("playback record not found"));
    }

    @PostMapping(value = "/camera/hk/previewurl",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraPreviewURLByPost(@Valid @RequestBody HKCameraPreviewRequest hkCameraPreviewRequest) {
        String response = hkCameraService.getCameraPreviewURL(hkCameraPreviewRequest);
        return Optional.ofNullable(response)
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.failed("camera preview record not found"));
    }
}
