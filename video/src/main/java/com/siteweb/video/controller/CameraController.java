package com.siteweb.video.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.video.dto.CameraBatchImportDTO;
import com.siteweb.video.dto.CameraConnectStatusDTO;
import com.siteweb.video.entity.Camera;
import com.siteweb.video.service.CameraService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(value = "摄像头接口管理",tags = "摄像头接口管理")
@RequestMapping("/api")
@RestController
public class CameraController {

    @Autowired
    CameraService cameraService;

    @ApiOperation("获取所有摄像头信息")
    @GetMapping(value = "/cameras", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameras() {
        return ResponseHelper.successful(cameraService.findAll());
    }

    @ApiOperation("获取所有摄像头连接状态")
    @GetMapping(value = "/cameraconnectstatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraConnectStatuss() {
        return ResponseHelper.successful(cameraService.getCamerasConnectStatus());
    }

    @ApiOperation("根据摄像头uuid获取摄像头信息")
    @PostMapping(value = "/cameraconnectstatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraConnectStatusByKey(@RequestBody List<String> cameraIndexCodeList) {
        if (CollUtil.isEmpty(cameraIndexCodeList)) {
            return ResponseHelper.failed("RequestBody is empty");
        }
        List<CameraConnectStatusDTO> result = cameraService.getCameraConnectStatusByKey(cameraIndexCodeList);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("根据id获取摄像头信息")
    @GetMapping(value = "/cameras/{cameraId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraById(@PathVariable Integer cameraId) {
        Camera camera = cameraService.findOne(cameraId);
        return Optional.ofNullable(camera)
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.failed("摄像头id错误"));
    }

    @ApiOperation("批量添加摄像头信息")
    @PostMapping(value = "/cameras", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createCameras(@RequestBody List<CameraBatchImportDTO> cameraBatchImportDTOList) {
        if (CollUtil.isEmpty(cameraBatchImportDTOList)) {
            return ResponseHelper.failed("摄像头信息不能为空");
        }
        return ResponseHelper.successful(cameraService.batchImport(cameraBatchImportDTOList));
    }

    @ApiOperation("远程批量添加摄像头信息")
    @GetMapping(value = "/batchaddcameras", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getBatchAddCameras() {
        return ResponseHelper.successful(cameraService.batchCreateCameras());
    }

    @ApiOperation("更新摄像头信息")
    @PutMapping(value = "/cameras", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateCameras(@Valid @RequestBody Camera camera) {
        Camera result = cameraService.createOrUpdate(camera);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("根据id删除摄像头信息")
    @DeleteMapping(value = "/cameras/{cameraId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteCamera(@PathVariable Integer cameraId) {
        cameraService.delete(cameraId);
        return ResponseHelper.successful();
    }
}
