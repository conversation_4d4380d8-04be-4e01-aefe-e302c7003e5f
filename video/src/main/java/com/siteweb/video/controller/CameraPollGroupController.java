package com.siteweb.video.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.video.dto.CameraPollGroupDTO;
import com.siteweb.video.entity.CameraPollGroup;
import com.siteweb.video.service.CameraPollGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(value = "摄像头采集周期管理",tags = "摄像头采集周期管理")
@RequestMapping("/api")
@RestController
public class CameraPollGroupController {

    @Autowired
    CameraPollGroupService cameraPollGroupService;

    @ApiOperation("获取所有采集周期")
    @GetMapping(value = "/camerapollgroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraPollGroups() {
        List<CameraPollGroup> result = cameraPollGroupService.findAll();
        return ResponseHelper.successful(result);
    }

    @ApiOperation("根据id获取采集周期")
    @GetMapping(value = "/camerapollgroups/{cameraPollGroupId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraPollGroupById(@PathVariable Integer cameraPollGroupId) {
        CameraPollGroup cameraPollGroup = cameraPollGroupService.findOne(cameraPollGroupId);
        return ResponseHelper.successful(cameraPollGroup);
    }

    @ApiOperation("创建采集周期")
    @PostMapping(value = "/camerapollgroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createCameraPollGroups(@Valid @RequestBody CameraPollGroup cameraPollGroup) {
        if (ObjectUtil.isNotNull(cameraPollGroup) && StrUtil.isBlank(cameraPollGroup.getCameraPollGroupName())) {
            return ResponseHelper.failed("A new Camera cannot already have an ID");
        }
        CameraPollGroup result = cameraPollGroupService.create(cameraPollGroup);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("更新采集周期")
    @PutMapping(value = "/camerapollgroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateCameras(@Valid @RequestBody CameraPollGroup cameraPollGroup) {
        if (ObjectUtil.isNull(cameraPollGroup) || ObjectUtil.isNull(cameraPollGroup.getCameraPollGroupId())) {
            CameraPollGroup result = cameraPollGroupService.create(cameraPollGroup);
            return ResponseHelper.successful(result);
        }
        CameraPollGroup result = cameraPollGroupService.update(cameraPollGroup);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("删除采集周期")
    @DeleteMapping(value = "/camerapollgroups/{cameraPollGroupId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteCamera(@PathVariable Integer cameraPollGroupId) {
        cameraPollGroupService.delete(cameraPollGroupId);
        return ResponseHelper.successful();
    }

    @ApiOperation("采集周期组与摄像头关系信息")
    @GetMapping(value = "/camerapollgroupshadows", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCameraPollGroupDTOs() {
        return ResponseHelper.successful(cameraPollGroupService.getCameraPollGroupDTOs());
    }

    @ApiOperation("根据id获取采集周期组与摄像头关系信息")
    @GetMapping(value="/camerapollgroupshadows", params = "cameraPollGroupId")
    public ResponseEntity<ResponseResult> getCameraPollGroupDTOById(Integer cameraPollGroupId) {
        //found
        CameraPollGroupDTO cameraPollGroupDTOS = cameraPollGroupService.getCameraPollGroupDTOById(cameraPollGroupId);
        return Optional.ofNullable(cameraPollGroupDTOS)
                       .map(ResponseHelper::successful)
                       .orElse(ResponseHelper.failed("cameraPollGroupId not found"));
    }
}
