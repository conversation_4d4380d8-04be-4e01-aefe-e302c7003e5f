package com.siteweb.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("Camera")
public class Camera implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.AUTO)
    private Integer cameraId;
    /**
     *  摄像机名称
     */
    private String cameraName;
    /**
     * 摄像机IP
     */
    private String cameraIp;
    /**
     * 摄像机端口号
     */
    private Integer cameraPort;
    /**
     * 管道号
     */
    private String channelNumber;
    /**
     * 所属组ID
     */
    private Integer cameraGroupId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    private String password;
    /**
     * 厂商id
     */
    private Integer vendorId;
    /**
     * 厂商名称
     */
    private String vendorName;
    /**
     * 设备UUID
     */
    private String cameraIndexCode;
    /**
     *  型号id
     */
    private Integer cameraType;
    /**
     * 型号
     */
    private String cameraTypeName;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 摄像头组名称
     */
    @TableField(exist = false)
    private String cameraGroupName;
    /**
     *  描述
     */
    private String description;
}
