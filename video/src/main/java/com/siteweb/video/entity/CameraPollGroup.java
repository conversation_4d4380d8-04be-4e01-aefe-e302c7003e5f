package com.siteweb.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("CameraPollGroup")
public class CameraPollGroup implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 摄像头采集组id
     */
    @TableId(type = IdType.AUTO)

    private Integer cameraPollGroupId;
    /**
     * 摄像头采集名称
     */
    private String cameraPollGroupName;

    /**
     * 采集周期
     */
    private Integer pollInterval;
    /**
     * 描述
     */
    private String description;

    @TableField(exist = false)
    private List<CameraPollGroupMap> cameraPollGroupMaps;
}
