package com.siteweb.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("CameraPollGroupMap")
public class CameraPollGroupMap implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer cameraPollGroupMapId;
    /**
     * 摄像机名称
     */
    private Integer cameraPollGroupId;

    /**
     * 摄像头id
     */
    private Integer cameraId;
    @TableField(exist = false)
    private Camera camera;
    /**
     * 摄像头名称
     */
    @TableField(exist = false)
    private String cameraName;

    public String getCameraName() {
        if (this.camera != null) {
            return this.camera.getCameraName();
        }
        return null;
    }
}
