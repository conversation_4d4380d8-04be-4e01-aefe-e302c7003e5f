package com.siteweb.video.manager;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.video.dto.HKCameraPlayBackRequest;
import com.siteweb.video.dto.HKCameraPlayBackResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CameraPlayBackURLManager {
    private final Logger log = LoggerFactory.getLogger(CameraPlayBackURLManager.class);
    private String ARTEMIS_PATH = "/artemis";
    private String HK_HTTP_PROTOCOL = "hk_http_protocolType";
    private String contentType = "application/json";
    private String playbackURLsAPI = "/api/video/v2/cameras/playbackURLs";

    @Autowired
    SystemConfigService systemConfigService;

    /**
     * 获取摄像机回放URL
     * @param httpAPIPrefix
     * @param HKCameraPlayBackRequest
     * @return {@link HKCameraPlayBackResponse}
     */
    public HKCameraPlayBackResponse getCenterCameraPlaybackURL(String httpAPIPrefix, HKCameraPlayBackRequest HKCameraPlayBackRequest) {
        HKCameraPlayBackResponse hkCameraPlayBackResponse = new HKCameraPlayBackResponse();
        try {
            String result = getStringResponseEntity(httpAPIPrefix, HKCameraPlayBackRequest);
            if(result == null){
                return hkCameraPlayBackResponse;
            }
            Gson gson = new Gson();
            hkCameraPlayBackResponse = gson.fromJson(result, HKCameraPlayBackResponse.class);
            if (hkCameraPlayBackResponse == null || !hkCameraPlayBackResponse.getCode().equals("0")) {
                System.out.println("CameraPlayBackURLManager getCenterCameraPlaybackURL getStatusCode()!= HttpStatus.OK");
                return hkCameraPlayBackResponse;
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        return hkCameraPlayBackResponse;
    }

    public HKCameraPlayBackResponse getDeviceCameraPlaybackURL(String httpAPIPrefix, HKCameraPlayBackRequest HKCameraPlayBackRequest) {
        HKCameraPlayBackResponse hkCameraPlayBackResponse = null;
        String searchUuid = "";
        try {
            do {
                String result = getStringResponseEntity(httpAPIPrefix, HKCameraPlayBackRequest);
                Gson gson = new Gson();
                HKCameraPlayBackResponse pageResponse = gson.fromJson(result, HKCameraPlayBackResponse.class);
                if (pageResponse == null || !pageResponse.getCode().equals("0")) {
                    System.out.println("CameraPlayBackURLManager getCenterCameraPlaybackURL getStatusCode()!= HttpStatus.OK");
                    return hkCameraPlayBackResponse;
                }
                if(hkCameraPlayBackResponse == null){
                    hkCameraPlayBackResponse = pageResponse;
                }else{
                    if(hkCameraPlayBackResponse.getData().getList() != null &&
                            pageResponse.getData() != null && !pageResponse.getData().getList().isEmpty()){
                        hkCameraPlayBackResponse.getData().getList().addAll(pageResponse.getData().getList());
                        searchUuid = pageResponse.getData().getUuid();
                    }else{
                        searchUuid = "";
                    }
                }
            } while (!searchUuid.isEmpty());
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        return hkCameraPlayBackResponse;
    }

    private String getStringResponseEntity(String httpAPIPrefix, HKCameraPlayBackRequest HKCameraPlayBackRequest) {
        String result = "";
        Map<String, String> path = getOpenAPIPath(httpAPIPrefix);
        try {
            SystemConfig protocolType = systemConfigService.findBySystemConfigKey(HK_HTTP_PROTOCOL);
            ObjectNode jsonBody = JacksonUtil.getInstance().createObjectNode();
            jsonBody.put("cameraIndexCode", HKCameraPlayBackRequest.getCameraIndexCode());
            jsonBody.put("recordLocation", HKCameraPlayBackRequest.getRecordLocation());
            jsonBody.put("protocol", protocolType != null && protocolType.getSystemConfigValue() != null ? protocolType.getSystemConfigValue() : "ws");
            jsonBody.put("transmode", 0);
            jsonBody.put("beginTime", HKCameraPlayBackRequest.getBeginTime());
            jsonBody.put("endTime", HKCameraPlayBackRequest.getEndTime());
            result = ArtemisHttpUtil.doPostStringArtemis(path, jsonBody.asText(), null, null, contentType, null);
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        return result;
    }

    private Map<String, String> getOpenAPIPath(String httpAPIPrefix) {
        String url = ARTEMIS_PATH + playbackURLsAPI;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put(httpAPIPrefix, url);//根据现场环境部署确认是 http 还是 https
            };
        };
        return path;
    }
}
