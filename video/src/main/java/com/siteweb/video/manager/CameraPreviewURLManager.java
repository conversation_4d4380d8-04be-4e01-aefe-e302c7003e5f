package com.siteweb.video.manager;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.video.dto.HKCameraPreviewRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CameraPreviewURLManager {
    private final Logger log = LoggerFactory.getLogger(CameraPreviewURLManager.class);
    private String ARTEMIS_PATH = "/artemis";
    private String HK_HTTP_PROTOCOL = "hk_http_protocolType";
    private String contentType = "application/json";
    private String previewURLsAPI = "/api/video/v1/cameras/previewURLs";

    @Autowired
    SystemConfigService systemConfigService;

    /// <summary>
    /// 获取摄像机预览URL
    /// </summary>
    public String getCameraPreviewURL(String httpAPIPrefix, HKCameraPreviewRequest hkCameraPreviewRequest) {
        String result = "";
        Map<String, String> path = getOpenAPIPath(httpAPIPrefix);
        try {
            SystemConfig protocolType = systemConfigService.findBySystemConfigKey(HK_HTTP_PROTOCOL);
            ObjectNode jsonBody = JacksonUtil.getInstance().createObjectNode();
            jsonBody.put("cameraIndexCode", hkCameraPreviewRequest.getCameraIndexCode());
            jsonBody.put("streamType",hkCameraPreviewRequest.getStreamType() != null ? hkCameraPreviewRequest.getStreamType() : 0);
            jsonBody.put("protocol", hkCameraPreviewRequest.getProtocol() != null ? hkCameraPreviewRequest.getProtocol() : "ws");
            jsonBody.put("transmode", hkCameraPreviewRequest.getTransmode() != null ? hkCameraPreviewRequest.getTransmode() : 1);
            jsonBody.put("expand", hkCameraPreviewRequest.getExpand() != null ? hkCameraPreviewRequest.getExpand() : "transcode=0");
//            jsonBody.put("streamform", "rtp");
            result = ArtemisHttpUtil.doPostStringArtemis(path, jsonBody.asText(), null, null, contentType, null);
        }catch(Exception ex){
            log.error(ex.getMessage());
        }
        return result;
    }

    private Map<String, String> getOpenAPIPath(String httpAPIPrefix) {
        String url = ARTEMIS_PATH + previewURLsAPI;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put(httpAPIPrefix, url);//根据现场环境部署确认是 http 还是 https
            };
        };
        return path;
    }
}
