package com.siteweb.video.manager;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.video.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.List;

@Component
public class HKVideoManager {
    private String HK_REGISTRATION_HOST = "hk_registration_host";
    private String HK_REGISTRATION_APPKEY = "hk_registration_appKey";
    private String HK_REGISTRATION_APPSECRET = "hk_registration_appSecret";
    private String HK_HTTP_APIPREFIX = "hk_http_apiPrefix";
    private String httpAPIPrefix = "hk_http_protocolType";
    private Integer PageSize = 1000;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    CameraPreviewURLManager cameraPreviewURLManager;

    @Autowired
    CameraPlayBackURLManager cameraPlayBackURLManager;

    @Autowired
    CameraConnectionManager cameraConnectionManager;

    @Autowired
    CameraResourceManager cameraResourceManager;

    @PostConstruct
    public void init() {
        initArtemisConfig();
    }

    /**
     * 27998720合作方Key
     * jEGpPycW9JHMsjeSwb8w
     */
    private void initArtemisConfig() {
        SystemConfig host = systemConfigService.findBySystemConfigKey(HK_REGISTRATION_HOST);
        if (host != null) {
            //"***********:443"; // artemis 网关服务器 ip 端口
            ArtemisConfig.host = host.getSystemConfigValue();
        }
        SystemConfig appKey = systemConfigService.findBySystemConfigKey(HK_REGISTRATION_APPKEY);
        if (appKey != null) {
            //"***********:443"; // artemis 网关服务器 ip 端口
            ArtemisConfig.appKey = appKey.getSystemConfigValue();
        }
        SystemConfig appSecret = systemConfigService.findBySystemConfigKey(HK_REGISTRATION_APPSECRET);
        if (appSecret != null) {
            // "XO0wCAYGi4KV70ybjznx"; 秘钥 appSecret
            ArtemisConfig.appSecret = appSecret.getSystemConfigValue();
        }
        SystemConfig httpPrefix = systemConfigService.findBySystemConfigKey(HK_HTTP_APIPREFIX);
        if (httpPrefix != null) {
            httpAPIPrefix = httpPrefix.getSystemConfigValue();
        }
    }

    /**
     * 获取监控点在线状态
     * @param cameraIndexCodes
     * @return {@link List}<{@link HKConnectStatusCamera}>
     */
    public List<HKConnectStatusCamera> getCameraConnectStatus(List<String> cameraIndexCodes) {
        return cameraConnectionManager.getCameraConnectStatus(cameraIndexCodes, httpAPIPrefix);
    }

    /**
     * 获取摄像机监控点设备
     * @return {@link List}<{@link HKCameraResourceDTO}>
     */
    public List<HKCameraResourceDTO> getCameraResourceDTOs() {
        return cameraResourceManager.getHKCameraResources(httpAPIPrefix, PageSize);
    }

    /**
     * 获取摄像机预览URL
     * @param hkCameraPreviewRequest
     * @return {@link String}
     */
    public String getCameraPreviewURL(HKCameraPreviewRequest hkCameraPreviewRequest) {
        return cameraPreviewURLManager.getCameraPreviewURL(httpAPIPrefix, hkCameraPreviewRequest);
    }

    /**
     *  获取摄像机回放URL
     * @param HKCameraPlayBackRequest
     * @return {@link HKCameraPlayBackResponse}
     */
    public HKCameraPlayBackResponse getCameraPlaybackURL(HKCameraPlayBackRequest HKCameraPlayBackRequest) {
        HKCameraPlayBackResponse cameraPlayBackResponse = new HKCameraPlayBackResponse();
        try {
            //2020-04-05T00:00:00.000 +0800
            String beginTime = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(HKCameraPlayBackRequest.getBeginDate()) + "+08:00";
            String endTime = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").format(HKCameraPlayBackRequest.getEndDate()) + "+08:00";
            HKCameraPlayBackRequest.setBeginTime(beginTime);
            HKCameraPlayBackRequest.setEndTime(endTime);
            if("0".equals(HKCameraPlayBackRequest.getRecordLocation().toString())){
                cameraPlayBackResponse = cameraPlayBackURLManager.getCenterCameraPlaybackURL(httpAPIPrefix, HKCameraPlayBackRequest);
            }else{
                cameraPlayBackResponse = cameraPlayBackURLManager.getDeviceCameraPlaybackURL(httpAPIPrefix, HKCameraPlayBackRequest);
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        return cameraPlayBackResponse;
    }
}
