package com.siteweb.video.manager;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.video.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CameraResourceManager {
    private final Logger log = LoggerFactory.getLogger(CameraResourceManager.class);
    private String ARTEMIS_PATH = "/artemis";
    private String contentType = "application/json";

    //获取编码设备接口
    private String encodeDeviceAPI = "/api/resource/v1/encodeDevice/get";
    //获取监控点信息接口
    private String cameraResourcesAPI = "/api/resource/v1/cameras";

    @Autowired
    SystemConfigService systemConfigService;

    public List<HKCameraResourceDTO> getHKCameraResources(String httpAPIPrefix, Integer pageSize) {
        List<HKCameraResourceDTO> hkCameraResourceDTOList = new ArrayList<>();
        try {
            List<HKEncodeDevice> hkEncodeDevices = getEncodeDevices(httpAPIPrefix, pageSize);
            List<HKCameraResource> hkCameraResources = getCameraResources(httpAPIPrefix, pageSize);
            if (hkEncodeDevices.isEmpty() || hkCameraResources.isEmpty()) {
                return hkCameraResourceDTOList;
            }
            for (HKCameraResource hkCameraResource : hkCameraResources) {
                HKCameraResourceDTO hkCameraResourceDTO = new HKCameraResourceDTO();
                BeanUtils.copyProperties(hkCameraResource, hkCameraResourceDTO);
                List<HKEncodeDevice> indexCodeDevices = hkEncodeDevices.stream()
                                                                       .filter(oc -> oc.getIndexCode()
                                                                                       .equals(hkCameraResource.getEncodeDevIndexCode()))
                                                                       .toList();
                if (!indexCodeDevices.isEmpty()) {
                    //编码设备登录用户名
                    hkCameraResourceDTO.setUserName(indexCodeDevices.get(0).getUserName());
                    //编码设备登录密码
                    hkCameraResourceDTO.setPassword(indexCodeDevices.get(0).getPassword());
                    //IP地址
                    hkCameraResourceDTO.setIp(indexCodeDevices.get(0).getIp());
                    //厂商
                    hkCameraResourceDTO.setManufacturer(indexCodeDevices.get(0).getManufacturer());
                    //端口
                    hkCameraResourceDTO.setPort(indexCodeDevices.get(0).getPort());
                    //设备系列
                    hkCameraResourceDTO.setDeviceType(indexCodeDevices.get(0).getDeviceType());
                    //设备序列号
                    hkCameraResourceDTO.setDevSerialNum(indexCodeDevices.get(0).getDevSerialNum());
                }
                hkCameraResourceDTOList.add(hkCameraResourceDTO);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        return hkCameraResourceDTOList;
    }

    private List<HKCameraResource> getCameraResources(String httpAPIPrefix, Integer pageSize) {
        List<HKCameraResource> hkCameraResources = new ArrayList<>();
        Map<String, String> path = getOpenAPIPath(httpAPIPrefix, cameraResourcesAPI);
        Integer pageNo = 0;
        Integer totalSize = 1;
        do {
            String stringResponseEntity = getPostStringArtemis(path, ++pageNo, pageSize);
            if(stringResponseEntity == null){
                return hkCameraResources;
            }
            Gson gson = new Gson();
            HKCameraResponse hkCameraResponse = gson.fromJson(stringResponseEntity, HKCameraResponse.class);
            if (hkCameraResponse == null || !"0".equals(hkCameraResponse.getCode())) {
                System.out.println("ArtemisHttpUtil.doPostStringArtemis getCameraResources getStatusCode()!= HttpStatus.OK");
                return hkCameraResources;
            }
            HKCameraPage cameraPage = hkCameraResponse.getData();
            totalSize = Integer.parseInt(cameraPage.getTotal());
            pageNo++;
            hkCameraResources.addAll(cameraPage.getList());
        } while (totalSize > pageNo * pageSize);

        return hkCameraResources;
    }

    private List<HKEncodeDevice> getEncodeDevices(String httpAPIPrefix, Integer pageSize) {
        List<HKEncodeDevice> hkEncodeDevices = new ArrayList<>();
        Map<String, String> path = getOpenAPIPath(httpAPIPrefix, encodeDeviceAPI);
        Integer pageNo = 0;
        Integer totalSize = 1;
        do {
            String stringResponseEntity = getPostStringArtemis(path, ++pageNo, pageSize);
            if(stringResponseEntity == null){
                return hkEncodeDevices;
            }
            Gson gson = new Gson();
            HKEncodeDeviceResponse hkEncodeDeviceResponse = gson.fromJson(stringResponseEntity, HKEncodeDeviceResponse.class);
            if (hkEncodeDeviceResponse == null || !hkEncodeDeviceResponse.getCode().equals("0")) {
                System.out.println("ArtemisHttpUtil.doPostStringArtemis getEncodeDevices getStatusCode()!= HttpStatus.OK");
                return hkEncodeDevices;
            }
            HKEncodeDevicePage cameraPage = hkEncodeDeviceResponse.getData();
            totalSize = Integer.parseInt(cameraPage.getTotal());
            pageNo++;
            hkEncodeDevices.addAll(cameraPage.getList());
        } while (totalSize > pageNo * pageSize);

        return hkEncodeDevices;
    }

    private Map<String, String> getOpenAPIPath(String httpAPIPrefix, String openAPI) {
        String url = ARTEMIS_PATH + openAPI;
        Map<String, String> path = new HashMap<>(2) {
            {
                put(httpAPIPrefix, url);//根据现场环境部署确认是 http 还是 https
            }
        };
        return path;
    }

    private String getPostStringArtemis(Map<String, String> path, Integer pageNo, Integer pageSize) {
        /*** STEP5：组装请求参数*/
        ObjectNode jsonBody = JacksonUtil.getInstance().createObjectNode();
        jsonBody.put("pageNo", pageNo);
        jsonBody.put("pageSize", pageSize);
        String body = jsonBody.asText();
        /*** STEP6：调用接口*/
        String stringResponseEntity = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, contentType, null);// post 请求 application/json 类型参数
        return stringResponseEntity;
    }

}
