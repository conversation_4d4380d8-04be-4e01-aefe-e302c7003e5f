package com.siteweb.video.manager;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.video.dto.HKConnectStatusCamera;
import com.siteweb.video.dto.HKConnectStatusResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class CameraConnectionManager {
    private final Logger log = LoggerFactory.getLogger(CameraConnectionManager.class);
    private String ARTEMIS_PATH = "/artemis";
    private String contentType = "application/json";
    private String cameraConnectStatusAPI = "/api/nms/v1/online/camera/get";

    @Autowired
    SystemConfigService systemConfigService;

    /**
     * 获取监控点在线状态
     * @param cameraIndexCodes
     * @param httpAPIPrefix
     * @return {@link List}<{@link HKConnectStatusCamera}>
     */
    public List<HKConnectStatusCamera> getCameraConnectStatus(List<String> cameraIndexCodes, String httpAPIPrefix) {
        if (ArtemisConfig.host == null || ArtemisConfig.host.isEmpty()) {
            return Collections.emptyList();
        }
        List<HKConnectStatusCamera> hkConnectStatusCameras = new ArrayList<>();
        try {
            int pageSize = 490;
            for (int i = 0; i < cameraIndexCodes.size(); i += pageSize) {
                if (i + pageSize > cameraIndexCodes.size()) {
                    // 注意下标问题
                    pageSize = cameraIndexCodes.size() - i;
                }
                List<String> subCameraIndexCodes = cameraIndexCodes.subList(i, i + pageSize);
                String stringResponseEntity = getCameraOnlinePostStringArtemis(subCameraIndexCodes, httpAPIPrefix);
                if(stringResponseEntity == null){
                    continue;
                }
                Gson gson = new Gson();
                HKConnectStatusResponse hkConnectStatusResponse = gson.fromJson(stringResponseEntity, HKConnectStatusResponse.class);
                if (hkConnectStatusResponse != null && hkConnectStatusResponse.getData() != null) {
                    hkConnectStatusCameras.addAll(hkConnectStatusResponse.getData().getList());
                }
            }
        }catch (Exception ex){
            log.error(ex.getMessage());
        }
        return hkConnectStatusCameras;
    }

    private String getCameraOnlinePostStringArtemis(List<String> cameraIndexCodes, String httpAPIPrefix) {
        Map<String, String> path = getOpenAPIPath(httpAPIPrefix);
        /*** STEP5：组装请求参数*/
        ObjectNode jsonBody = JacksonUtil.getInstance().createObjectNode();
        jsonBody.put("regionId", "root000000");   //"748d84750e3a4a5bbad3cd4af9ed5101"
        jsonBody.put("includeSubNode", 1);
        jsonBody.putPOJO("indexCodes", cameraIndexCodes);
        jsonBody.put("status", "1");
        jsonBody.put("pageNo", 1);
        jsonBody.put("pageSize", 1000);
        String body = jsonBody.asText();
        /*** STEP6：调用接口*/
        // post 请求 application/json 类型参数
        String stringResponseEntity = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, contentType, null);
        return stringResponseEntity;
    }

    private Map<String, String> getOpenAPIPath(String httpAPIPrefix) {
        String url = ARTEMIS_PATH + cameraConnectStatusAPI;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put(httpAPIPrefix, url);//根据现场环境部署确认是 http 还是 https
            };
        };
        return path;
    }
}
