<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.prealarm.mapper.PreAlarmMapper">
    <insert id="insertPreAlarmReportRecord">
        INSERT INTO PD_PrealarmReportRecord(PreAlarmId,PreAlarmPointId,PreAlarmSeverity,PreAlarmCategory,ObjectId,ObjectTypeId,
                    ResourceStructureId,TriggerValue,InsertTime,GenerateResult)
            VALUES(#{preAlarmId}, #{preAlarmPointId}, #{preAlarmSeverity}, #{preAlarmCategory}, #{objectId}, #{objectTypeId},
                    #{resourceStructureId}, #{triggerValue}, now(), 0)
    </insert>
    <select id="devicePreAlarmSeverityStatistics" resultType="com.siteweb.prealarm.dto.PreAlarmSeverityCount" >
        SELECT PreAlarmSeverityName,Color,COUNT(PreAlarmSeverity) as severityCount FROM PreAlarm WHERE objectId = #{objectId} AND PreAlarmCategory = #{preAlarmCategory} Group by PreAlarmSeverity
    </select>

    <select id="structurePreAlarmSeverityStatistics" resultType="com.siteweb.prealarm.dto.PreAlarmSeverityCount" >
        SELECT PreAlarmSeverityName,Color,COUNT(PreAlarmSeverity) as severityCount  FROM PreAlarm WHERE (ResourceStructureId=#{objectId} OR LevelOfPath like CONCAT('%',#{objectId},'%'))  AND PreAlarmCategory = #{preAlarmCategory} Group by PreAlarmSeverity
    </select>

    <select id="preAlarmSeverityStatistics" resultType="com.siteweb.prealarm.dto.PreAlarmSeverityCount">
        SELECT PreAlarmSeverityName, Color, COUNT(PreAlarmSeverity) as severityCount
        FROM PreAlarm
        GROUP BY PreAlarmSeverity, PreAlarmSeverityName, Color
    </select>
</mapper>