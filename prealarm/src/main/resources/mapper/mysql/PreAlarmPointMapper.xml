<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.prealarm.mapper.PreAlarmPointMapper">
    <insert id="insertPreAlarmPont" parameterType="com.siteweb.prealarm.entity.PreAlarmPoint" useGeneratedKeys="true"
            keyProperty="preAlarmPointId" keyColumn="preAlarmPointId">
        INSERT INTO Prealarmpoint(preAlarmPointName, meanings, expression, abnormalExpression,
        preAlarmCategory,uniqueId,objectId,objectName,objectTypeId,resourceStructureId,levelOfPath,levelOfPathName,executeCron,preAlarmSeverity,enable,unit,maskType,maskDuration,maskStartTime,maskEndTime,stateful,modifier,modifierName,modifyTime)
        VALUES(#{preAlarmPointName}, #{meanings}, #{expression}, #{abnormalExpression},
        #{preAlarmCategory},#{uniqueId},#{objectId},#{objectName},#{objectTypeId},#{resourceStructureId},#{levelOfPath},#{levelOfPathName},#{executeCron},#{preAlarmSeverity},#{enable},#{unit},#{maskType},#{maskDuration},#{maskStartTime},#{maskEndTime},#{stateful},#{modifier},#{modifierName},#{modifyTime})
    </insert>
    <insert id="batchInsert" keyProperty="preAlarmPointId" useGeneratedKeys="true">
        insert into prealarmpoint ( PreAlarmPointName,Meanings,Expression,AbnormalExpression,ExecuteCron,PreAlarmCategory,UniqueId,
        ObjectId,ObjectTypeId,ObjectName,ResourceStructureId,LevelOfPath,LevelOfPathName,PreAlarmSeverity,enable,unit,maskType,
        maskDuration,maskStartTime,maskEndTime,stateful,modifier,modifierName,modifyTime)
        values
        <foreach collection="needSaveToDB" item="item" separator=",">
            (#{item.preAlarmPointName},#{item.meanings},#{item.expression},#{item.abnormalExpression},#{item.executeCron}, #{item.preAlarmCategory},#{item.uniqueId},
            #{item.objectId},#{item.objectTypeId},#{item.objectName},#{item.resourceStructureId},#{item.levelOfPath},#{item.levelOfPathName},#{item.preAlarmSeverity},#{item.enable}, #{item.unit},#{item.maskType},
            #{item.maskDuration},#{item.maskStartTime},#{item.maskEndTime},#{item.stateful},#{item.modifier},#{item.modifierName},#{item.modifyTime})
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="needUpdateToDB" item="item" separator=";">
            UPDATE prealarmpoint SET expression = #{item.expression},abnormalExpression = #{item.abnormalExpression},executeCron = #{item.executeCron} ,
            enable = #{item.enable} ,unit = #{item.unit} ,maskDuration = #{item.maskDuration} ,maskStartTime = #{item.maskStartTime} ,maskEndTime = #{item.maskEndTime} ,maskType = #{item.maskType} ,
            modifier = #{item.modifier} ,modifierName = #{item.modifierName} ,modifyTime = #{item.modifyTime}
            where preAlarmPointId = #{item.preAlarmPointId}
        </foreach>
    </update>

    <select id="getGeneratorLastMaintenanceTime" resultType="java.lang.String">
        select MaintenanceTime from gene_maintenancerecord where GeneId = #{equipmentId} order by MaintenanceTime desc limit 1;
    </select>
    <select id="getExpressionRunTimeCompareValue" resultType="java.lang.Long">
        <![CDATA[
        select sum(RunDuration) from gene_powergenerationrecord where EquipmentId = #{equipmentId} and StartTime >= #{startDate} and EndTime <= #{currentDate};
        ]]>
    </select>
    <select id="getNoEndTimeRecord" resultType="com.siteweb.prealarm.dto.GenePowerGenerationRecordDTO">
        SELECT * FROM gene_powergenerationrecord
        WHERE EquipmentId = #{equipmentId}
        AND EquipmentBaseType = 301
        AND EndTime IS NULL;
    </select>

</mapper>