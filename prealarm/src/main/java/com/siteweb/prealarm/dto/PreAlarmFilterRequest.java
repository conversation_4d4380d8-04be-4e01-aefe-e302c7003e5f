package com.siteweb.prealarm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PreAlarmFilterRequest {
    @ApiModelProperty("objectId.objectTypeId多个用逗号隔开")
    private String resourceObjects;
    private String preAlarmCategory;
    private String preAlarmSeverity;
    private String resourceStructures;
    private Boolean preAlarmConfirmed;
    private Boolean preAlarmEnded;
    private String orderByKey;
    private String orderByDirection;
    private String keywords;
    private Integer size;
    private Integer number;
    private Integer userId;
    private Boolean containBelowLevel;


}
