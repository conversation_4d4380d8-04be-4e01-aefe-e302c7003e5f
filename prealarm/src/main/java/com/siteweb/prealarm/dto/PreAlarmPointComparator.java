package com.siteweb.prealarm.dto;

import com.siteweb.prealarm.entity.PreAlarmPoint;

import java.lang.reflect.Field;
import java.text.Collator;
import java.util.Comparator;
import java.util.Date;
import java.util.Locale;

public class PreAlarmPointComparator implements Comparator<PreAlarmPoint> {
    final String sortBy;
    final String sortOrder;

    public PreAlarmPointComparator(String sortBy, String sortOrder) {
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
    }

    @Override
    public int compare(PreAlarmPoint o1, PreAlarmPoint o2) {
        try {
            //获取需要排序的字段
            Field field1 = o1.getClass().getDeclaredField(sortBy);
            Field field2 = o2.getClass().getDeclaredField(sortBy);
            field1.setAccessible(true); // because the fields in Impianto entity has "private"
            field2.setAccessible(true);
            if (o1.getClass().getDeclaredField(sortBy).getType() == Long.class) {
                Long d1 =  field1.get(o1)==null ? 0L : (Long) field1.get(o1);
                Long d2 = field2.get(o2)==null ? 0L : (Long) field2.get(o2);
                return (sortOrder.equalsIgnoreCase("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);
            } else if (o1.getClass().getDeclaredField(sortBy).getType() == Double.class) {
                Double d1 = field1.get(o1)==null ? 0d : (Double) field1.get(o1);
                Double d2 = field2.get(o2)==null ? 0d :(Double) field2.get(o2);
                return (sortOrder.equalsIgnoreCase("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);
            } else if (o1.getClass().getDeclaredField(sortBy).getType() == Integer.class) {
                Integer d1 =field1.get(o1)==null ? 0 : (Integer) field1.get(o1);
                Integer d2 = field2.get(o2)==null ? 0 : (Integer) field2.get(o2);
                return (sortOrder.equalsIgnoreCase("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            } else if (o1.getClass().getDeclaredField(sortBy).getType() == Date.class) {
                Date d1 =field1.get(o1) == null ? new Date("1901-1-1") :  (Date) field1.get(o1);
                Date d2 =field2.get(o2) == null ? new Date("1901-1-1") :  (Date) field2.get(o2);
                return (sortOrder.equalsIgnoreCase("asc")) ? d1.compareTo(d2) : d2.compareTo(d1);

            } else {
                String d1 = field1.get(o1)== null ? "" : (String) field1.get(o1);
                String d2 = field2.get(o2)== null ? "" : (String) field2.get(o2);
                Collator instance = Collator.getInstance(Locale.CHINA);
                return (sortOrder.equalsIgnoreCase("asc")) ? instance.compare(d1,d2) : instance.compare(d2,d1);// d1.compareTo(d2) : d2.compareTo(d1);
            }
        } catch (SecurityException e) {
            throw new SecurityException(e);
        } catch (NoSuchFieldException e) {
            throw  new RuntimeException("Missing variable sortBy");
        } catch (ClassCastException e) {
            throw new ClassCastException("sortBy is not found in class list");
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
