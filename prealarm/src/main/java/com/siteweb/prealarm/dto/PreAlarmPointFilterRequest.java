package com.siteweb.prealarm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PreAlarmPointFilterRequest {
    @ApiModelProperty("objectId.objectTypeId多个用逗号隔开")
    private String resourceObjects;

    private String resourceStructureIds;

    @ApiModelProperty(" //0:显示全部，1，显示已屏蔽，2显示未屏蔽")
    private Integer displayMode;

    @ApiModelProperty("需要排序的字段关键字")
    private String orderByKey;
    /**
     * 排序规则 asc升序  desc降序
     */
    @ApiModelProperty("排序规则 asc升序  desc降序")
    private String orderByDirection;
    /**
     * 每页显示多少条
     */
    @ApiModelProperty("每页显示多少条 不传默认为：10")
    private Integer size = 10;
    /**
     * 第几页
     */
    @ApiModelProperty("第几页 不传默认为：1")
    private Integer number = 1;

    /**
     * 用户userId
     */
    @ApiModelProperty("用户userId")
    private Integer userId;
}
