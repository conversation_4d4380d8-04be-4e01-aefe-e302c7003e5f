package com.siteweb.prealarm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AlarmTextNotifyDTO {


    private String text;
    private int severityId;
    /**
     * 预警id
     */
    private Integer preAlarmId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date confirmTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date endTime;

    private String sequenceId;
    /**
     * 告警操作类型 1开始 2结束 3确认
     */
    private Integer operationType;
}

