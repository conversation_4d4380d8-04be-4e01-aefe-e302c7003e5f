package com.siteweb.prealarm.dto;

import lombok.Data;

import java.util.Date;

@Data
public class PreAlarmDTO {
    //   预警名称
    private String meanings;
    //   预警配置Id 约定这里负增长
    private Integer preAlarmPointId;
    //   预警等级
    private Integer preAlarmSeverity;
    //   objectId
    private Integer objectId;
    //   objectTypeId
    private Integer objectTypeId;
    //单位
    private String unit;
    private String uniqueName;
    //   触发值
    private String TriggerValue;
    //   开始时间
    private Date StartTime;
//  1 仅结束  0 新增
    private Integer flag;
    //用能类型
    private Integer businessTypeId;
}
