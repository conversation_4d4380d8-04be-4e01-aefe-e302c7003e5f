package com.siteweb.prealarm.function;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.manager.ComplexIndexCacheManager;
import org.jetbrains.annotations.NotNull;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.service.HistoryComplexIndexService;
import com.siteweb.complexindex.service.LiveComplexIndexService;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.prealarm.dto.ExpressionResult;
import com.siteweb.complexindex.service.EnergyPreAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class ExpressionParserFunction {

    private static String sampleTime;

    private static String triggerValue;

    @Autowired
    private RealTimeSignalManager autoRealTimeSignalManager;
    private static RealTimeSignalManager realRealTimeSignalManager;

    @Autowired
    private HistoryComplexIndexService autoHistoryComplexIndexService;
    private static HistoryComplexIndexService historyComplexIndexService;

    @Autowired
    private LiveComplexIndexService autoLiveComplexIndexService;
    private static LiveComplexIndexService liveComplexIndexService;

    @Autowired
    private CapacityAttributeService autoCapacityAttributeService;

    @Autowired
    private  EnergyPreAlarmService autoEnergyPreAlarmService;

    private static EnergyPreAlarmService energyPreAlarmService;
    private static CapacityAttributeService capacityAttributeService;
    @Autowired
    private ComplexIndexCacheManager autoComplexIndexCacheManager;
    private static ComplexIndexCacheManager complexIndexCacheManager;

    private SimpleDateFormat dateFormat;

    @PostConstruct
    public void init() {
        realRealTimeSignalManager = this.autoRealTimeSignalManager;
        historyComplexIndexService = this.autoHistoryComplexIndexService;
        liveComplexIndexService=this.autoLiveComplexIndexService;
        capacityAttributeService = this.autoCapacityAttributeService;
        energyPreAlarmService = this.autoEnergyPreAlarmService;
        complexIndexCacheManager = this.autoComplexIndexCacheManager;
    }

    @NotNull
    private static String getYesterday() {
        DateFormat dateFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//格式化一下时间

        Date dNow = new Date();

        Date dBefore;

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(dNow);

        calendar.add(Calendar.DAY_OF_MONTH, -1);

        dBefore = calendar.getTime();

        String defaultStartDate = dateFmt.format(dBefore);

        defaultStartDate = defaultStartDate.substring(0,10)+" 00:00:00";

        return defaultStartDate;
    }


    /**
     * 返回平均数
     *
     * @param data 逗号分割
     * @return
     */
    public static float avg(String data) {
        float sum = 0;
        int index = 0;
        String[] split = data.split(",");
        for (String s : split) {
            sum = sum + Float.valueOf(s);
            index++;
        }
        if (index > 0) {
            return (sum + 0.0f) / index;
        }
        triggerValue = String.valueOf(sum);
        return sum;
    }

    /**
     * 求最大值
     * @return 逗号分割
     */
    public static float max(String data) {
        float result = 0;
        String[] sArray = data.split(",");
        if(ArrayUtil.isEmpty(sArray)){
            return result;
        }
        String max = NumberUtil.max(sArray);
        result = Float.valueOf(max);
        triggerValue = String.valueOf(result);
        return result;
    }

    /**
     * 最小值
     * @param data 逗号分割
     * @return
     */
    public static float min(String data) {
        float result = 0;
        String[] sArray = data.split(",");
        if(ArrayUtil.isEmpty(sArray)){
            return result;
        }
        String min = NumberUtil.min(sArray);
        result = Float.valueOf(min);
        triggerValue = String.valueOf(result);
        return result;
    }

    public static Double cp(String key) {
        if(StringUtils.isEmpty(key)){
            triggerValue ="";
            sampleTime=null;
            return 0d;
        }
        String[] keys = key.split("\\.");
        RealTimeSignalItem realTimeSignalItem = realRealTimeSignalManager.getRealTimeSignalBySignalId(Integer.valueOf(keys[0]),Integer.valueOf(keys[1]));
        if(realTimeSignalItem == null){
            triggerValue ="";
            sampleTime=null;
            log.error(" ExpressionParserFunction cp - RealTimeSignalItem Is Not Exist key Is " + key);
            return 0d;
        }
        String result = StrUtil.isEmpty(realTimeSignalItem.getCurrentValue()) ? "0" : realTimeSignalItem.getCurrentValue();
        triggerValue = result;
        sampleTime =realTimeSignalItem.getSampleTime();
        return Double.valueOf(result);
    }
    public static String formatDoubleValue(Double source)
    {
        DecimalFormat format = new DecimalFormat("0.00");
        return format.format(source);
    }
    public static Double ci(Integer complexIndexId) {
        ComplexIndex complexIndex = complexIndexCacheManager.GetAllComplexIndex().stream().filter(Index-> Index.getComplexIndexId().equals(complexIndexId)).findFirst().orElse(null);
        if(complexIndex == null){
            triggerValue ="ComplexIndexNotExist";
            sampleTime=null;
            log.error(" ExpressionParserFunction ci - ComplexIndex Is Not Exist ComplexIndexId Is " + complexIndexId);
            return 0d;
        }
        LiveComplexIndex liveComplexIndex = liveComplexIndexService.getLiveComplexIndex(complexIndexId);
        if(liveComplexIndex == null){
            triggerValue ="";
            sampleTime=null;
            log.error(" ExpressionParserFunction ci - LiveComplexIndex Is Not Exist ComplexIndexId Is " + complexIndexId);
            return 0d;
        }
        String result = StrUtil.isEmpty(liveComplexIndex.getCurrentValue()) ? "0" : liveComplexIndex.getCurrentValue();
        triggerValue = result;
        Timestamp ts= Timestamp.valueOf(liveComplexIndex.getTimeStamp());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//定义格式，不显示毫秒
        sampleTime =df.format(ts);
        return Double.valueOf(result);
    }
    public static Double ca(Integer attributeId) {

        List<CapacityAttribute> capacityAttributes = capacityAttributeService.findAttributes(attributeId.toString());
        if(capacityAttributes == null || capacityAttributes.size()==0 ) {
            triggerValue ="";
            sampleTime=null;
            log.error(" ExpressionParserFunction ca - CapacityAttribute Is Not Exist CapacityAttributeId Is " + attributeId);
            return 0d;
        }
        String result = capacityAttributes.get(0).getPercent() == null ? "0" : capacityAttributes.get(0).getPercent().toString();
        triggerValue = result;
        sampleTime =capacityAttributes.get(0).getSampleTime();
        return Double.valueOf(result);
    }

    public static Double last(Integer complexIndexId) {
        String result = historyComplexIndexService.getLastComplexIndexValue(complexIndexId);
        if(StrUtil.isEmpty(result)){
            triggerValue="";
            return 0d;
        }
        triggerValue = result;
        return Double.valueOf(result);
    }

    public static Double abs(Double d){
        Double result =  Math.abs(d);
        triggerValue = result.toString();
        return result;
    }

    public static Double lastdayavg(Integer complexIndexId){
        Double value = energyPreAlarmService.findLastDayValueAvgOfComplexIndex(complexIndexId);
        triggerValue = value.toString();
        sampleTime = getYesterday();
        return value;
    }
    public static Double lastdayavgyrange(String params){
        Double value = 0.0;
        String[] sArray = params.split(",");
        if(ArrayUtil.isEmpty(sArray)){
            return value;
        }
        value = energyPreAlarmService.findLastDayValueAvgRangeOfComplexIndex(Integer.valueOf(sArray[0]),Integer.valueOf(sArray[1]));
        triggerValue = value.toString();
        sampleTime = getYesterday();
        return value;
    }
    public static Double lastdaysum(Integer complexIndexId){
        Double value = energyPreAlarmService.findLastDayValueOfComplexIndex(complexIndexId);
        triggerValue = value.toString();
        sampleTime = getYesterday();
        return value;
    }
    public static Double lastdaysumrange(String params){
        Double value = 0.0;
        String[] sArray = params.split(",");
        if(ArrayUtil.isEmpty(sArray)){
            return value;
        }
        value = energyPreAlarmService.findLastDayValueRangeOfComplexIndex(Integer.valueOf(sArray[0]),Integer.valueOf(sArray[1]));
        triggerValue = value.toString();
        sampleTime = getYesterday();
        return value;
    }
    /**
     * 将表达式转换(计算表达式)
     *
     * @param cronExpression 表达式
     * @return
     */
    public static String parseExpression(String cronExpression) {
        if (StringUtils.isEmpty(cronExpression)) {
            return cronExpression;
        }

        //将公式全部转化为小写，并去掉其中的空格

        cronExpression = cronExpression.replace(" ", "");
        cronExpression = cronExpression.replace("avg", "#avg");
        cronExpression = cronExpression.replace("cp", "#cp");
        cronExpression = cronExpression.replace("ci", "#ci");
        cronExpression = cronExpression.replace("ca", "#ca");
        cronExpression = cronExpression.replace("CP", "#cp");
        cronExpression = cronExpression.replace("CI", "#ci");
        cronExpression = cronExpression.replace("CA", "#ca");
        cronExpression = cronExpression.replace("max", "#max");
        cronExpression = cronExpression.replace("min", "#min");
        cronExpression = cronExpression.replace("last", "#last");
        cronExpression = cronExpression.replace("%", "");
        cronExpression = cronExpression.replace(",", "+','+");
        cronExpression = cronExpression.toLowerCase();
        if (cronExpression.contains("cp")){
            Pattern p = Pattern.compile("(?<=cp\\()[^\\)]+");
            Matcher m = p.matcher(cronExpression);
            StringBuffer sb = new StringBuffer();
            while(m.find()){
                m.appendReplacement(sb,"\""+m.group()+"\"");
            }
            m.appendTail(sb);
            cronExpression = sb.toString();
        }

        return cronExpression;
    }


    public static ExpressionResult eval(String expression) throws NoSuchMethodException {
        if (StrUtil.isEmpty(expression)){
            return null;
        }
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext();
        standardEvaluationContext.registerFunction("avg", ExpressionParserFunction.class.getDeclaredMethod("avg", new Class[]{String.class}));
        standardEvaluationContext.registerFunction("max", ExpressionParserFunction.class.getDeclaredMethod("max", new Class[]{String.class}));
        standardEvaluationContext.registerFunction("min", ExpressionParserFunction.class.getDeclaredMethod("min", new Class[]{String.class}));
        standardEvaluationContext.registerFunction("cp", ExpressionParserFunction.class.getDeclaredMethod("cp", new Class[]{String.class}));
        standardEvaluationContext.registerFunction("ci", ExpressionParserFunction.class.getDeclaredMethod("ci", new Class[]{Integer.class}));
        standardEvaluationContext.registerFunction("ca", ExpressionParserFunction.class.getDeclaredMethod("ca", new Class[]{Integer.class}));
        standardEvaluationContext.registerFunction("last", ExpressionParserFunction.class.getDeclaredMethod("last", new Class[]{Integer.class}));
        standardEvaluationContext.registerFunction("abs", ExpressionParserFunction.class.getDeclaredMethod("abs", new Class[]{Double.class}));
        standardEvaluationContext.registerFunction("lastdayavg", ExpressionParserFunction.class.getDeclaredMethod("lastdayavg", new Class[]{Integer.class}));
        standardEvaluationContext.registerFunction("lastdayavgyrange", ExpressionParserFunction.class.getDeclaredMethod("lastdayavgyrange", new Class[]{String.class}));
        standardEvaluationContext.registerFunction("lastdaysum", ExpressionParserFunction.class.getDeclaredMethod("lastdaysum", new Class[]{Integer.class}));
        standardEvaluationContext.registerFunction("lastdaysumrange", ExpressionParserFunction.class.getDeclaredMethod("lastdaysumrange", new Class[]{String.class}));
        Object obj= parser.parseExpression(expression).getValue(standardEvaluationContext);
        ExpressionResult result = new ExpressionResult();
        Boolean value = Boolean.valueOf(obj.toString());
        result.setCompareResult(value);
        if(triggerValue.equals("ComplexIndexNotExist")){
            result.setTriggerValue(triggerValue);
        }
        else {
            result.setTriggerValue(formatDoubleValue(Double.valueOf(triggerValue.equals("")?"0.00":triggerValue)));
        }
        result.setSampleTime(sampleTime);
        return result;
    }
}
