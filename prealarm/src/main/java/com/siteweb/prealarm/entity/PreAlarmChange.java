package com.siteweb.prealarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("prealarmchange")
public class PreAlarmChange {
    /**
     * 序列ID
     */
    private String sequenceId;
    /**
     * 变化流水号(自增)
     */
    @TableId(value = "serialNo", type = IdType.AUTO)
    private Long serialNo;
    /**
     * 操作类型（1:开始，2:结束，3:确认）
     */
    private Integer operationType;
    /**
     * 预警ID
     */
    private Integer preAlarmId;
    /**
     * 预警名称
     */
    private String preAlarmName;
    /**
     * 预警含义
     */
    private String meanings;
    /**
     * 触发值
     */
    private String triggerValue;
    /**
     * 预警配置ID
     */
    private Integer preAlarmPointId;
    /**
     * 预警类型ID
     */
    private Integer preAlarmCategory;
    /**
     * 预警类型名称
     */
    private String preAlarmCategoryName;
    /**
     * 预警等级ID
     */
    private Integer preAlarmSeverity;
    /**
     * 预警等级名称
     */
    private String preAlarmSeverityName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 确认人ID
     */
    private Integer confirmorId;
    /**
     * 确认人名称
     */
    private String confirmorName;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备类型ID
     */
    private Integer equipmentCategory;
    /**
     * 设备类型名称
     */
    private String equipmentCategoryName;
    /**
     * 设备品牌
     */
    private String equipmentVendor;
    /**
     * 中心ID
     */
    private Integer centerId;
    /**
     * 中心名称
     */
    private String centerName;
    /**
     * 分组ID
     */
    private Integer structureId;
    /**
     * 分组名称
     */
    private String structureName;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 局站类型ID
     */
    private Integer stationCategoryId;
    /**
     * 层级ID
     */
    private Integer resourceStructureId;
    /**
     * 层级路径
     */
    private String levelOfPathName;
    /**
     * 插入时间
     */
    private Date insertTime;


}
