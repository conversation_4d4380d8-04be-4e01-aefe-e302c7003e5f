package com.siteweb.prealarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.prealarm.dto.PreAlarmConst;
import com.siteweb.prealarm.dto.PreAlarmRulePoint;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create date 2021-09-24
 */

@Data
@NoArgsConstructor
@TableName("prealarmpoint")
@Slf4j
public class PreAlarmPoint implements Serializable, Cloneable {

    /**
     * 预警属性唯一ID
     */
    @TableId(value = "PreAlarmPointId", type = IdType.AUTO)
    private Integer preAlarmPointId;

    /**
     * 预警名称
     */

    private String preAlarmPointName;
    /**
     * 预警含义
     */

    private String meanings;

    /**
     * 预警表达式
     * 使用指标的解析方式
     */

    private String expression;
    /**
     * 预警抑制表达式
     * 对于消息型预警，当符合该表达式时结束预警
     */

    private String abnormalExpression;

    /**
     * 预警分类
     */

    private Integer preAlarmCategory;
    /**
     * 唯一编码
     */

    private String uniqueId;
    /**
     * 预警条件所属对象ID
     */

    private Integer objectId;

    /**
     * 预警条件所属对象ID
     */

    private String objectName;
    /**
     * 预警条件所属对象ID
     */


    private Integer objectTypeId;
    /**
     * 预警条件所属对象ID
     */
    private Integer resourceStructureId;
    /**
     * 预警条件所属资源类型ID
     */


    private String levelOfPath;

    /**
     * 预警条件所属对象ID
     */

    private String levelOfPathName;

    /**
     * 预警执行周期
     */

    private String executeCron;

    /**
     * 预警等级
     */

    private int preAlarmSeverity;


    /**
     * 预警点是否启用 （1：启用，2：禁用）
     */

    private Integer enable;

    /**
     * 预警点单位
     */

    private String unit;
    /**
     * 预警点抑制类型（1：时间段，2：时长）
     */

    private Integer maskType;


    /**
     * 预警点抑制时长48个字符，每个字符为1或者0，1代表屏蔽，o代表不屏蔽。即每半个小时代表一个字符
     */

    private String maskDuration;


    /**
     * 预警点抑制开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date maskStartTime;


    /**
     * 预警点抑制结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date maskEndTime;

    /**
     * 预警点是否带状态，如果不带状态，开始、结束时间一样,1不带状态，2带状态
     */

    private Integer stateful;

    /**
     * 预警点修改人
     */

    private Integer modifier;
    /**
     * 预警点修改人
     */

    private String modifierName;
    /**
     * 预警点修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    @TableField(exist = false)
    private List<Integer> selectEquipmentIds;

    /**
     * 转换
     */
    public static PreAlarmPoint transformFrom(PreAlarmRulePoint preAlarmRulePoint) {
        PreAlarmPoint preAlarmPoint = new PreAlarmPoint();
        preAlarmPoint.setPreAlarmPointId(preAlarmRulePoint.getPreAlarmPointId());
        preAlarmPoint.setPreAlarmPointName(preAlarmRulePoint.getPreAlarmPointName());
        preAlarmPoint.setMeanings(preAlarmRulePoint.getMeanings());
        preAlarmPoint.setExpression(preAlarmRulePoint.getExpression());
        preAlarmPoint.setAbnormalExpression(preAlarmRulePoint.getAbnormalExpression());
        preAlarmPoint.setExecuteCron(preAlarmRulePoint.getExecuteCron());
        preAlarmPoint.setPreAlarmCategory(preAlarmRulePoint.getPreAlarmCategory());
        preAlarmPoint.setUniqueId(preAlarmRulePoint.getUniqueId());
        preAlarmPoint.setObjectId(preAlarmRulePoint.getObjectId());
        preAlarmPoint.setObjectTypeId(preAlarmRulePoint.getObjectTypeId());
        preAlarmPoint.setObjectName(preAlarmRulePoint.getObjectName());
        preAlarmPoint.setResourceStructureId(preAlarmRulePoint.getResourceStructureId());
        preAlarmPoint.setLevelOfPath(preAlarmRulePoint.getLevelOfPath());
        preAlarmPoint.setLevelOfPathName(preAlarmRulePoint.getLevelOfPathName());
        preAlarmPoint.setPreAlarmSeverity(preAlarmRulePoint.getPreAlarmSeverity());
        preAlarmPoint.setEnable(preAlarmRulePoint.getEnable());
        preAlarmPoint.setUnit(preAlarmRulePoint.getUnit());
        preAlarmPoint.setMaskType(preAlarmRulePoint.getMaskType());
        preAlarmPoint.setMaskDuration(preAlarmRulePoint.getMaskDuration());
        preAlarmPoint.setMaskStartTime(preAlarmRulePoint.getMaskStartTime());
        preAlarmPoint.setMaskEndTime(preAlarmRulePoint.getMaskEndTime());
        preAlarmPoint.setStateful(preAlarmRulePoint.getStateful());
        preAlarmPoint.setModifier(preAlarmRulePoint.getModifier());
        preAlarmPoint.setModifierName(preAlarmRulePoint.getModifierName());
        preAlarmPoint.setModifyTime(preAlarmRulePoint.getModifyTime());
        return preAlarmPoint;
    }

    public void fillInsertPreAlarmPoint(PreAlarmPoint preAlarmPoint, Integer configType) {
        switch (configType) {
            case PreAlarmConst.CONFIG_TYPE_MASK:
            case PreAlarmConst.CONFIG_TYPE_ALL:
                fillInsertConfig(preAlarmPoint);
                fillMask(preAlarmPoint);
                break;
            default:
                fillInsertConfig(preAlarmPoint);
                break;
        }
        this.setModifier(preAlarmPoint.getModifier());
        this.setModifierName(preAlarmPoint.getModifierName());
        this.setModifyTime(preAlarmPoint.getModifyTime());
    }

    private void fillMask(PreAlarmPoint preAlarmPoint) {
        this.setMaskType(preAlarmPoint.getMaskType());
        this.setMaskDuration(preAlarmPoint.getMaskDuration());
        this.setMaskStartTime(preAlarmPoint.getMaskStartTime());
        this.setMaskEndTime(preAlarmPoint.getMaskEndTime());

    }

    private void fillInsertConfig(PreAlarmPoint preAlarmPoint) {
        this.setPreAlarmPointName(preAlarmPoint.getPreAlarmPointName());
        this.setMeanings(preAlarmPoint.getMeanings());
        this.setExecuteCron(preAlarmPoint.getExecuteCron());
        this.setPreAlarmCategory(preAlarmPoint.getPreAlarmCategory());
        this.setPreAlarmSeverity(preAlarmPoint.getPreAlarmSeverity());
        this.setEnable(preAlarmPoint.getEnable());
        this.setUnit(preAlarmPoint.getUnit());
        this.setStateful(preAlarmPoint.getStateful());
        this.setResourceStructureId(preAlarmPoint.getResourceStructureId());
        this.setLevelOfPath(preAlarmPoint.getLevelOfPath());
        this.setLevelOfPathName(preAlarmPoint.getLevelOfPathName());
    }

    public void fillUpdatePreAlarmPoint(PreAlarmPoint preAlarmPoint, Integer configType) {
        switch (configType) {
            case PreAlarmConst.CONFIG_TYPE_MASK:
                fillMask(preAlarmPoint);
                break;
            case PreAlarmConst.CONFIG_TYPE_ALL:
                fillUpdateConfig(preAlarmPoint);
                fillMask(preAlarmPoint);
                break;
            default:
                fillUpdateConfig(preAlarmPoint);
                break;
        }
        this.setModifier(preAlarmPoint.getModifier());
        this.setModifierName(preAlarmPoint.getModifierName());
        this.setModifyTime(preAlarmPoint.getModifyTime());
    }

    private void fillUpdateConfig(PreAlarmPoint preAlarmPoint) {
        this.setPreAlarmCategory(preAlarmPoint.getPreAlarmCategory());
        this.setMeanings(preAlarmPoint.getMeanings());
        this.setExecuteCron(preAlarmPoint.getExecuteCron());
        this.setPreAlarmSeverity(preAlarmPoint.getPreAlarmSeverity());
        this.setUnit(preAlarmPoint.getUnit());
        this.setResourceStructureId(preAlarmPoint.getResourceStructureId());
        this.setLevelOfPath(preAlarmPoint.getLevelOfPath());
        this.setLevelOfPathName(preAlarmPoint.getLevelOfPathName());
        this.setObjectId(preAlarmPoint.getObjectId());
        this.setObjectTypeId(preAlarmPoint.getObjectTypeId());
        this.setObjectTypeId(preAlarmPoint.getObjectTypeId());
    }

    //原型模式深拷贝
    public Object deepClone() {
        ByteArrayOutputStream bos = null;
        ObjectOutputStream oos = null;
        ByteArrayInputStream bis = null;
        ObjectInputStream ois = null;
        try {
            //序列化
            bos = new ByteArrayOutputStream();
            oos = new ObjectOutputStream(bos);
            oos.writeObject(this);

            //反序列化
            bis = new ByteArrayInputStream(bos.toByteArray());
            ois = new ObjectInputStream(bis);
            PreAlarmPoint cloneObj = (PreAlarmPoint) ois.readObject();
            return cloneObj;

        } catch (Exception ex) {
            log.error("PreAlarmPoint->deepClone", ex);
            return null;
        } finally {
            try {
                bos.close();
                oos.close();
                bis.close();
                ois.close();
            } catch (Exception ex1) {
                log.error("PreAlarmPoint deepClone exception: ", ex1);
            }
        }
    }

    public void clone(PreAlarmPoint preAlarmPoint) {
        this.setPreAlarmPointId(preAlarmPoint.getPreAlarmPointId());
        this.setPreAlarmPointName(preAlarmPoint.getPreAlarmPointName());
        this.setMeanings(preAlarmPoint.getMeanings());
        this.setExpression(preAlarmPoint.getExpression());
        this.setAbnormalExpression(preAlarmPoint.getAbnormalExpression());
        this.setExecuteCron(preAlarmPoint.getExecuteCron());
        this.setPreAlarmCategory(preAlarmPoint.getPreAlarmCategory());
        this.setUniqueId(preAlarmPoint.getUniqueId());
        this.setObjectId(preAlarmPoint.getObjectId());
        this.setObjectTypeId(preAlarmPoint.getObjectTypeId());
        this.setObjectName(preAlarmPoint.getObjectName());
        this.setResourceStructureId(preAlarmPoint.getResourceStructureId());
        this.setLevelOfPath(preAlarmPoint.getLevelOfPath());
        this.setLevelOfPathName(preAlarmPoint.getLevelOfPathName());
        this.setPreAlarmSeverity(preAlarmPoint.getPreAlarmSeverity());
        this.setEnable(preAlarmPoint.getEnable());
        this.setUnit(preAlarmPoint.getUnit());
        this.setMaskType(preAlarmPoint.getMaskType());
        this.setMaskDuration(preAlarmPoint.getMaskDuration());
        this.setMaskStartTime(preAlarmPoint.getMaskStartTime());
        this.setMaskEndTime(preAlarmPoint.getMaskEndTime());
        this.setStateful(preAlarmPoint.getStateful());
        this.setModifier(preAlarmPoint.getModifier());
        this.setModifierName(preAlarmPoint.getModifierName());
        this.setModifyTime(preAlarmPoint.getModifyTime());
    }
}
