package com.siteweb.prealarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;



@Data
@NoArgsConstructor
@TableName("prealarmcategory")
public class PreAlarmCategory {
    /**
     * 预警分类ID
     */

    @TableId(value = "CategoryId", type = IdType.AUTO)
    private Integer categoryId;


    private String categoryName;


    private Integer parentCategoryId;


    private String Description;

}
