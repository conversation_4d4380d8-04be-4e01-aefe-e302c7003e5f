package com.siteweb.prealarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 能耗电费配置操作日志类
 */
@Data
@NoArgsConstructor
@TableName("Energy_ElecFeeConfigOperateLog")
public class EnergyElecFeeConfigOperateLog {

    /**
     * 自增主键
     */
    @TableId(value="LogId", type = IdType.AUTO)
    private Integer logId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 变更日期
     */
    private Date updateDate;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 变动的对象(存储的是对象json串)
     */
    private String changeContent;


    private String extendField1;

}
