package com.siteweb.prealarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @create date 2021-09-24
 */

@Data
@TableName("prealarmseverity")
@NoArgsConstructor
public class PreAlarmSeverity {
    /**
     * 预警等级
     */
    @TableId(value = "PreAlarmSeverityId", type = IdType.AUTO)
    private Integer preAlarmSeverityId;
    /**
     * 预警等级名称
     */
    private String preAlarmSeverityName;

    /**
     * 预警颜色，16进制颜色
     */
    private String color;

    /**
     * 预警等级描述
     */
    private String description;
}
