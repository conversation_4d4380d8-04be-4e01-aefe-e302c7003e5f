package com.siteweb.prealarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.exception.CloneFailedException;


import java.util.Date;

/**
 * <AUTHOR>
 * @email
 * @date 2021-09-27
 */

@Data
@NoArgsConstructor
@TableName("prealarm")
public class PreAlarm {

    /**
     * 预警属性唯一ID
     */
    @TableId(value = "preAlarmId", type = IdType.AUTO)
    private Integer preAlarmId;
    /**
     * 预警编号
     */
    private Integer preAlarmPointId;
    /**
     * 预警名称
     */
    private String meanings;
    /**
     * 预警等级
     */
    private Integer preAlarmSeverity;

    /**
     * 预警等级名
     */
    private String preAlarmSeverityName;

    /**
     * 预警分类：指标：显示所有的指标列表。容量：显示所有的容量属性列表
     */
    private Integer preAlarmCategory;

    /**
     * 预警分类名
     */
    private String preAlarmCategoryName;

    /**
     * 预警颜色，16进制颜色
     */
    private String color;
    /**
     * 唯一编码
     */
    private String uniqueId;
    /**
     * 唯一编码
     */
    private String uniqueName;
    /**
     * 预警条件所属对象ID
     */
    private Integer objectId;

    private Integer objectTypeId;

    /**
     * 预警条件所属对象ID
     */
    private String objectName;
    /**
     * 预警条件所属对象ID
     */
    private Integer resourceStructureId;
    /**
     * 预警条件所属对象ID
     */

    private String levelOfPath;

    /**
     * 预警条件所属对象ID
     */

    private String levelOfPathName;


    /**
     * 触发值
     */


    private String triggerValue;
    /**
     * 单位
     */


    private String unit;
    /**
     * 产生预警时的数据采集时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sampleTime;


    /**
     * 预警开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 预警结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 预警确认时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /**
     * 预警确认人账号ID
     */

    private Integer confirmId;

    /**
     * 预警确认人
     */

    private String confirmName;

    /**
     * 备注信息
     */

    private String remark;

    private Integer businessTypeId;

    public void clone(PreAlarm preAlarm) {
        this.setPreAlarmId(preAlarm.getPreAlarmId());
        this.setPreAlarmPointId(preAlarm.getPreAlarmPointId());
        this.setMeanings(preAlarm.getMeanings());
        this.setPreAlarmSeverity(preAlarm.getPreAlarmSeverity());
        this.setPreAlarmSeverityName(preAlarm.getPreAlarmSeverityName());
        this.setPreAlarmCategory(preAlarm.getPreAlarmCategory());
        this.setPreAlarmCategoryName(preAlarm.getPreAlarmCategoryName());
        this.setColor(preAlarm.getColor());
        this.setUniqueId(preAlarm.getUniqueId());
        this.setUniqueName(preAlarm.getUniqueName());
        this.setObjectId(preAlarm.getObjectId());
        this.setObjectTypeId(preAlarm.getObjectTypeId());
        this.setObjectName(preAlarm.getObjectName());
        this.setResourceStructureId(preAlarm.getResourceStructureId());
        this.setLevelOfPath(preAlarm.getLevelOfPath());
        this.setLevelOfPathName(preAlarm.getLevelOfPathName());
        this.setTriggerValue(preAlarm.getTriggerValue());
        this.setUnit(preAlarm.getUnit());
        this.setSampleTime(preAlarm.getSampleTime());
        this.setStartTime(preAlarm.getStartTime());
        this.setEndTime(preAlarm.getEndTime());
        this.setConfirmTime(preAlarm.getConfirmTime());
        this.setConfirmId(preAlarm.getConfirmId());
        this.setConfirmName(preAlarm.getConfirmName());
        this.setRemark(preAlarm.getRemark());
        this.setBusinessTypeId(preAlarm.getBusinessTypeId());
    }

    public void preAlarmHistoryToPreAlarm(PreAlarmHistory preAlarm) {
        this.setPreAlarmId(preAlarm.getPreAlarmId());
        this.setPreAlarmPointId(preAlarm.getPreAlarmPointId());
        this.setMeanings(preAlarm.getMeanings());
        this.setPreAlarmSeverity(preAlarm.getPreAlarmSeverity());
        this.setPreAlarmSeverityName(preAlarm.getPreAlarmSeverityName());
        this.setPreAlarmCategory(preAlarm.getPreAlarmCategory());
        this.setPreAlarmCategoryName(preAlarm.getPreAlarmCategoryName());
        this.setColor(preAlarm.getColor());
        this.setUniqueId(preAlarm.getUniqueId());
        this.setUniqueName(preAlarm.getUniqueName());
        this.setObjectId(preAlarm.getObjectId());
        this.setObjectTypeId(preAlarm.getObjectTypeId());
        this.setObjectName(preAlarm.getObjectName());
        this.setResourceStructureId(preAlarm.getResourceStructureId());
        this.setLevelOfPath(preAlarm.getLevelOfPath());
        this.setLevelOfPathName(preAlarm.getLevelOfPathName());
        this.setTriggerValue(preAlarm.getTriggerValue());
        this.setUnit(preAlarm.getUnit());
        this.setSampleTime(preAlarm.getSampleTime());
        this.setStartTime(preAlarm.getStartTime());
        this.setEndTime(preAlarm.getEndTime());
        this.setConfirmTime(preAlarm.getConfirmTime());
        this.setConfirmId(preAlarm.getConfirmId());
        this.setConfirmName(preAlarm.getConfirmName());
        this.setRemark(preAlarm.getRemark());
        this.setBusinessTypeId(preAlarm.getBusinessTypeId());
    }
}