
package com.siteweb.prealarm.service;

import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.siteweb.prealarm.dto.MaskPreAlarmPoint;
import com.siteweb.prealarm.dto.PreAlarmPointFilterRequest;
import com.siteweb.prealarm.dto.PreAlarmRulePoint;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;


public interface PreAlarmPointService {

    Boolean checkExpression(String expression);
    /**
     * 获取某个全局对象的所有预警条件
     * @param objectId,objectTypeId 全局资源对象ID
     * @return
     */
    List<PreAlarmPoint> findPreAlarmPoints(Integer objectId,Integer objectTypeId);
    Page<PreAlarmPoint> findPreAlarmPoints(PreAlarmPointFilterRequest filterRequest);
//
    List<PreAlarmPoint> findPreAlarmPoints(Integer objectId,Integer objectTypeId, Integer categoryId);
    List<PreAlarmPoint> findMaskPreAlarmPoints();
    Boolean getResetScheduleFlag();
    void setResetScheduleFlag(Boolean value);

    /**
     * 获取 attributeId 的属性记录
     * @param preAlarmPointId
     * @return 单条属性，未找到则返回null
     */
    PreAlarmPoint findPreAlarmPoint(Integer preAlarmPointId);

    /**
     * 创建多条预警条件，如果已存在则返回已存在的记录
     * @return
     */
    List<PreAlarmPoint> batchCreatePreAlarmPoints(PreAlarmRulePoint preAlarmRulePoint);
    List<PreAlarmPoint> maskPreAlarmPoints(MaskPreAlarmPoint maskPreAlarmPoint);
    /**
     * 根据一个属性ID删除一条属性。
     * @param preAlarmPointId
     * @return
     */
    PreAlarmPoint deletePreAlarmPoint(Integer preAlarmPointId);

    /**
     * 根据执行周期将同一个cron的告警规则点实例聚合在一起
     */
    Map<String, List<PreAlarmPoint>> getPreAlarmPointsExecuteGroup();


    Equipment getPreAlarmPointEquipmentInfo(Integer equipmentId);
}