package com.siteweb.prealarm.service;



import com.siteweb.prealarm.entity.PreAlarmSeverity;

import java.util.List;

public interface PreAlarmSeverityService {
    List<PreAlarmSeverity> findAllPreAlarmSeverity();
    PreAlarmSeverity findByPreAlarmSeverityId(Integer preAlarmSeverityId);
    PreAlarmSeverity updatePreAlarmSeverity(PreAlarmSeverity preAlarmSeverity);
    Integer deleteByPreAlarmSeverityId(Integer preAlarmSeverityId);
}
