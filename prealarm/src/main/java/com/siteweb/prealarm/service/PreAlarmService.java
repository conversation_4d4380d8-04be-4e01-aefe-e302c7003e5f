
package com.siteweb.prealarm.service;

import com.siteweb.prealarm.dto.*;
import com.siteweb.prealarm.entity.*;
import org.springframework.data.domain.Page;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

public interface PreAlarmService {


    Page<PreAlarm> findAlarms(PreAlarmFilterRequest preAlarmFilterRequest);
    /**
     * 获取某个全局对象的所有预警条件
     * @param objectId,objectTypeId 全局资源对象ID
     * @return
     */
    List<PreAlarm> findAlarms(Integer objectId,Integer objectTypeId);
    List<PreAlarm> findAllActiveAlarm();
    List<PreAlarm> findAllPreAlarm();
    List<PreAlarm> findAllEndAndConfirmPreAlarms();
    /**
     * 给全局对象创建一条指定基类的属性，如果已存在则返回已存在的记录
     * @param preAlarmPoint 预警点表数据
     * @return
     */
    PreAlarm savePreAlarmToDb(PreAlarmPoint preAlarmPoint, String triggerValue,String sampleTime);
    PreAlarm createPreAlarmEntity(PreAlarmPoint preAlarmPoint, String triggerValue,String sampleTime);



        /**
         * 更新预警条件信息
         * @param preAlarmIds
         * @return
         */
    List<PreAlarm> endPreAlarms(List<Integer> preAlarmIds);

    PreAlarm endPreAlarm(Integer preAlarmId);

    void endExistPreAlarms(List<PreAlarm> preAlarmListreAlarms);
    /**
     * 更新多条预警条件信息
     * @param alarmConfirms
     * @return
     */
    List<PreAlarm> confirmPreAlarms(List<PreAlarmConfirm> alarmConfirms);

    List<PreAlarm> updatePreAlarmsRemark(List<PreAlarmConfirm> alarmConfirms);
    List<PreAlarm> findBeforePreAlarms(Integer differDays);
    /**
     * 根据一个属性ID删除一条属性。
     * @param preAlarmIds
     * @return
     */
    List<PreAlarm> deletePreAlarms(List<Integer> preAlarmIds);


    Integer deleteById(Integer preAlarmId);
    /**
     * 产生虚拟告警
     * @param param
     */
    List<PreAlarm> generateVirtualPreAlarm(VirtualPreAlarmParam param);

    /**
     * 定时执行告警产生的计算
     * @param preAlarmPoint
     */
    void scheduleGeneratePreAlarm(PreAlarmPoint preAlarmPoint);
    Integer contByPreAlarmCategory(Integer preAlarmCategory, Integer objectId, Integer objectTypeId);
    PreAlarm getMaxSeverityPreAlarmByCategory(Integer preAlarmCategory, String uniqueId);

    List<PreAlarmSeverityCount> findPreAlarmSeverityCount(Integer preAlarmCategory, Integer objectId, Integer objectType);

    List<PreAlarm> findByPreAlarmCategory(int preAlarmCategory);
    List<PreAlarmStatisticItem> findPreAlarmStatistics();
    PreAlarm createPreAlarmByEnergy(PreAlarmDTO preAlarmDTO);

    List<PreAlarm> findAlarmsByPowerDistributionPlayBack(Integer objectId, Integer objectTypeId, Date time);
    /** 得到最新创建的容量预警队列（每次有新的设备容量预警产生时，会被添加至此队列中） */
    ConcurrentLinkedQueue<Object[]> getNewEquipCapacityPrealarmQueue();
    /** 设置配电v2功能是否启动标记量 */
    void setPdV2EnableValue(boolean pdV2Enable);

    void addToCache(PreAlarm inputPreAlarm);

    void finishPreAlarmByPointId(Integer preAlarmPointId);
}