package com.siteweb.prealarm.service.impl;

import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.mapper.PreAlarmSeverityMapper;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("preAlarmSeverityService")
@EnableScheduling
public class PreAlarmSeverityServiceImpl implements PreAlarmSeverityService {

    @Autowired
    private PreAlarmSeverityMapper preAlarmSeverityMapper;

    @Override
    public List<PreAlarmSeverity> findAllPreAlarmSeverity() {
        return preAlarmSeverityMapper.selectList(null);
    }

    @Override
    public PreAlarmSeverity findByPreAlarmSeverityId(Integer preAlarmSeverityId) {
        return preAlarmSeverityMapper.selectById(preAlarmSeverityId);
    }

    @Override
    public PreAlarmSeverity updatePreAlarmSeverity(PreAlarmSeverity preAlarmSeverity) {
        if (preAlarmSeverity.getPreAlarmSeverityId() != null){
            preAlarmSeverityMapper.updateById(preAlarmSeverity);
        }else{
            preAlarmSeverityMapper.insert(preAlarmSeverity);
        }
        return preAlarmSeverity;
    }
    @Override
    public Integer deleteByPreAlarmSeverityId(Integer preAlarmSeverityId) {
         return preAlarmSeverityMapper.deleteById(preAlarmSeverityId);
    }
}
