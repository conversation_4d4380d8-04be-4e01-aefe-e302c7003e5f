package com.siteweb.prealarm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.service.BusinessDefinitionMapService;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.StationStructureMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.prealarm.dto.*;
import com.siteweb.prealarm.entity.*;
import com.siteweb.prealarm.function.ExpressionParserFunction;
import com.siteweb.prealarm.mapper.PreAlarmChangeMapper;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import com.siteweb.prealarm.mapper.PreAlarmPointMapper;
import com.siteweb.prealarm.service.PreAlarmCategoryService;
import com.siteweb.prealarm.service.PreAlarmPointService;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import com.siteweb.prealarm.service.*;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service("preAlarmService")
@EnableScheduling
@Slf4j
public class PreAlarmServiceImpl implements PreAlarmService , ApplicationListener<BaseSpringEvent<HAStatusChanged>> {

    private final ConcurrentLinkedQueue<Object[]> newCapacityPrealarmQueue = new ConcurrentLinkedQueue<>();
    private static final int EQUIPMENT_CATEGORY_ENTRY_ID = 7;
    @Autowired
    PreAlarmMapper preAlarmMapper;

    @Autowired
    EquipmentService equipmentService;

    @Autowired
    SignalService signalService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private PreAlarmPointService preAlarmPointService;


    @Autowired
    ResourceService resourceService;
    @Autowired
    ResourceObjectManager resourceObjectManager;
    @Autowired
    CapacityAttributeService capacityAttributeService;
    @Autowired
    private PreAlarmCategoryService preAlarmCategoryService;

    @Autowired
    private PreAlarmSeverityService preAlarmSeverityService;

    @Autowired
    private ResourceStructureService resourceStructureService;


    @Autowired
    private ComplexIndexService complexIndexService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    private PreAlarmHistoryService preAlarmHistoryService;

    @Autowired
    private HAStatusService haStatusService;

    private List<PreAlarm> preAlarms = new ArrayList<>();

    private Map<Integer, PreAlarm> mapPreAlarmIdMap;
    private Map<String, List<PreAlarm>> mapResourceObjectCache;

    private Map<String, List<PreAlarm>> mapUniqueIdCache;
    private Boolean needResetSchedule;
    private boolean pdV2Enable = false;

    @Autowired
    private BusinessDefinitionMapService businessDefinitionMapService;

    @Autowired
    private PreAlarmPointMapper preAlarmPointMapper;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private StationManager stationManager;
    @Autowired
    private StationStructureMapper stationStructureMapper;
    @Autowired
    private DataItemService dataItemService;
    @Autowired
    private StationStructureService stationStructureService;
    @Autowired
    private PreAlarmChangeMapper preAlarmChangeMapper;
    @Autowired
    private PreAlarmNotifyService preAlarmNotifyService;

    @PostConstruct
    public void init() {
        this.mapPreAlarmIdMap = new HashMap<>();
        this.mapResourceObjectCache = new HashMap<>();
        this.mapUniqueIdCache = new HashMap<>();
        this.preAlarms = preAlarmMapper.selectList(null);
        for (PreAlarm item : preAlarms) {
            this.addToCache(item);
        }
    }
    /**
     * 发生双机切换，重新初始化缓存
     * @param haStatusChangedEvent 双机切换事件
     */
    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<HAStatusChanged> haStatusChangedEvent) {
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = haStatusChangedEvent.getData();
        log.info("preAlarm Data Init, receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        init();
    }

    @NotNull
    private Date getNow() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = dateFormat.format(new Date(System.currentTimeMillis()));
        try {
            return dateFormat.parse(now);
        } catch (Exception e) {
            log.error("PreAlarmServiceImpl.getNow error:" + e.getMessage());
        }
        return new Date(System.currentTimeMillis());
    }

    @NotNull
    private Date getDate(String dateStr) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isEmpty(dateStr)) {
            return new Date(System.currentTimeMillis());
        }
        try {
            return dateFormat.parse(dateStr);
        } catch (Exception e) {
            log.error("PreAlarmServiceImpl.getDate error:"+ dateStr ,e);
        }
        return new Date(System.currentTimeMillis());
    }


    public void addToCache(PreAlarm inputPreAlarm) {
        needResetSchedule = true;
        List<PreAlarm> findFromAlarm = preAlarms.stream().filter(
                        item -> (item.getPreAlarmId().equals(inputPreAlarm.getPreAlarmId())))
                .collect(Collectors.toList());
        if (findFromAlarm.size() == 0) {
            preAlarms.add(inputPreAlarm);
        } else {
            findFromAlarm.get(0).clone(inputPreAlarm);
        }

        if (mapPreAlarmIdMap.get(inputPreAlarm.getPreAlarmId()) == null) {
            mapPreAlarmIdMap.put(inputPreAlarm.getPreAlarmId(), inputPreAlarm);
        } else {
            mapPreAlarmIdMap.get(inputPreAlarm.getPreAlarmId()).clone(inputPreAlarm);
        }


        List<PreAlarm> existPreAlarmList = this.mapResourceObjectCache.get(inputPreAlarm.getObjectId() + "." + inputPreAlarm.getObjectTypeId());
        if (existPreAlarmList == null) {
            this.mapResourceObjectCache.put(inputPreAlarm.getObjectId() + "." + inputPreAlarm.getObjectTypeId(), new ArrayList<PreAlarm>());
            this.mapResourceObjectCache.get(inputPreAlarm.getObjectId() + "." + inputPreAlarm.getObjectTypeId()).add(inputPreAlarm);
        } else {
            List<PreAlarm> findFromGlobalResourceCatch = existPreAlarmList.stream().filter(
                            item -> (item.getPreAlarmId().equals(inputPreAlarm.getPreAlarmId())))
                    .collect(Collectors.toList());
            if (findFromGlobalResourceCatch.size() == 0) {
                this.mapResourceObjectCache.get(inputPreAlarm.getObjectId() + "." + inputPreAlarm.getObjectTypeId()).add(inputPreAlarm);
            } else {
                findFromGlobalResourceCatch.get(0).clone(inputPreAlarm);
            }
        }


        List<PreAlarm> existUniqueIdMapList = this.mapUniqueIdCache.get(inputPreAlarm.getUniqueId());
        if (existUniqueIdMapList == null) {
            this.mapUniqueIdCache.put(inputPreAlarm.getUniqueId(), new ArrayList<>());
            this.mapUniqueIdCache.get(inputPreAlarm.getUniqueId()).add(inputPreAlarm);
        } else {
            List<PreAlarm> findFromGlobalResourceCatch = existUniqueIdMapList.stream().filter(
                            item -> (item.getPreAlarmId().equals(inputPreAlarm.getPreAlarmId())))
                    .collect(Collectors.toList());
            if (findFromGlobalResourceCatch.size() == 0) {
                this.mapUniqueIdCache.get(inputPreAlarm.getUniqueId()).add(inputPreAlarm);
            } else {
                findFromGlobalResourceCatch.get(0).clone(inputPreAlarm);
            }
        }
        preAlarmNotifyService.refreshPreAlarmCache(preAlarms);
    }

    private void deleteFromCache(PreAlarm inputPreAlarm) {
        needResetSchedule = true;
        for (PreAlarm point : preAlarms) {
            if (point.getPreAlarmId().equals(inputPreAlarm.getPreAlarmId())) {
                preAlarms.remove(point);
                break;
            }
        }
        List<PreAlarm> preAlarms = mapResourceObjectCache.get(inputPreAlarm.getObjectId() + "." + inputPreAlarm.getObjectTypeId());
        for (PreAlarm alarm : preAlarms) {
            if (alarm.getPreAlarmId().equals(inputPreAlarm.getPreAlarmId())) {
                preAlarms.remove(alarm);
                break;
            }
        }
        List<PreAlarm> preAlarmsByUniqueIdMap = mapUniqueIdCache.get(inputPreAlarm.getUniqueId());
        for (PreAlarm alarm : preAlarmsByUniqueIdMap) {
            if (alarm.getPreAlarmId().equals(inputPreAlarm.getPreAlarmId())) {
                preAlarmsByUniqueIdMap.remove(alarm);
                break;
            }
        }
        mapPreAlarmIdMap.remove(inputPreAlarm.getPreAlarmId());
    }

    private List<PreAlarm> getFilterResult(String columnName, List<PreAlarm> attributes, String filterString) {
        //如果时空字符串，就把源头返回回去
        if (StrUtil.isBlank(filterString)) return attributes;
        //如果过滤字符串不为空，查找
        List<PreAlarm> result = new ArrayList<>();
        //过滤条件根据逗号分割
        Set<String> uniqueFilterList = new HashSet<>(StrUtil.split(filterString, ','));
        for (String filter : uniqueFilterList) {
            if (StrUtil.isBlank(filter)) continue;
            switch (columnName) {
                case "resourceObjects" -> result.addAll(attributes.stream()
                        .filter(item -> {
                            String[] resourceObjectsSet = filter.split("\\.");
                            return (item.getObjectId().equals(Integer.parseInt(resourceObjectsSet[0])) && item.getObjectTypeId().equals(Integer.parseInt(resourceObjectsSet[1])));
                        })
                        .toList());
                case "objectIds" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getObjectId().equals(Integer.parseInt(filter))))
                        .toList());
                case "objectTypeIds" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getObjectTypeId().equals(Integer.parseInt(filter))))
                        .toList());
                case "resourceStructures" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getResourceStructureId().equals(Integer.parseInt(filter))))
                        .toList());
                case "resourceStructuresContainBelowLevel" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getLevelOfPath().contains(filter)))
                        .toList());
                case "preAlarmCategory" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getPreAlarmCategory().equals(Integer.parseInt(filter))))
                        .toList());
                case "preAlarmSeverity" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getPreAlarmSeverity().equals(Integer.parseInt(filter))))
                        .toList());
                case "preAlarmConfirmed" -> result.addAll(attributes.stream()
                        .filter(item -> {
                            if (filter.equals("true")) {
                                return (item.getConfirmTime() != null);
                            } else {
                                return (item.getConfirmTime() == null);
                            }
                        })
                        .toList());
                case "preAlarmEnded" -> result.addAll(attributes.stream()
                        .filter(item -> {
                            if (filter.equals("true")) {
                                return (item.getEndTime() != null);
                            } else {
                                return (item.getEndTime() == null);
                            }
                        })
                        .toList());
                case "keywords" -> result.addAll(attributes.stream()
                        .filter(item -> (item.getLevelOfPathName().contains(filterString) || item.getMeanings().contains(filterString)))
                        .toList());
            }
        }
        return result == null ? new ArrayList<>() : result;
    }

    private Page<PreAlarm> buildAttributePageList(List<PreAlarm> prealarmAttributeList, Integer number, Integer size) {
        Pageable pageable = PageRequest.of(number - 1, size);
        List<PreAlarm> records = prealarmAttributeList.stream()
                .skip((long) (number - 1) * size)
                .limit(size)
                .toList();
        return new PageImpl<>(records == null ? new ArrayList<>() : records, pageable, prealarmAttributeList.size());
    }

    private void sortList(List<PreAlarm> source, String orderByKey, String orderByDirection) {
        PreAlarmComparator comparatorTask = new PreAlarmComparator(orderByKey, orderByDirection);
        source.sort(comparatorTask);
    }

    @Override
    public Page<PreAlarm> findAlarms(PreAlarmFilterRequest preAlarmFilterRequest) {
//        根据userId获取资源
        if (preAlarmFilterRequest.getUserId() == null) {
            preAlarmFilterRequest.setUserId(-1);
        }
        List<ResourceObjectEntity> userCanAccessObjects = resourceObjectManager.findAllResourceByUserId(preAlarmFilterRequest.getUserId());
        List<PreAlarm> res = new ArrayList<>();
        if (userCanAccessObjects.size() > 0) {
            List<String> resourceObjectList = new ArrayList<>();
            for (ResourceObjectEntity item : userCanAccessObjects) {
                resourceObjectList.add(item.getObjectId().toString() + "." + item.getObjectTypeId().toString());
            }
            String resourceObjects = StringUtils.join(resourceObjectList, ',');
//            根据userId权限过滤
            List<PreAlarm> objectIdFilterResultByUser = this.getFilterResult("resourceObjects", preAlarms, resourceObjects);
//            根据传入ids过滤
            List<PreAlarm> objectIdFilterResultByRequestId = this.getFilterResult("resourceObjects", objectIdFilterResultByUser, preAlarmFilterRequest.getResourceObjects());
//            根据resourceStructureId层级过滤
            List<PreAlarm> resourceStructureIdFilterResult;
            if (preAlarmFilterRequest.getContainBelowLevel()) {
                resourceStructureIdFilterResult = this.getFilterResult("resourceStructuresContainBelowLevel", objectIdFilterResultByRequestId, preAlarmFilterRequest.getResourceStructures());
            } else {
                resourceStructureIdFilterResult = this.getFilterResult("resourceStructures", objectIdFilterResultByRequestId, preAlarmFilterRequest.getResourceStructures());
            }
//            根据preAlarmCategory业务分类过滤
            List<PreAlarm> preAlarmCategoryFilterResult = this.getFilterResult("preAlarmCategory", resourceStructureIdFilterResult, preAlarmFilterRequest.getPreAlarmCategory());
//            根据keywords业务分类过滤
            List<PreAlarm> preAlarmKeywordsFilterResult = this.getFilterResult("keywords", preAlarmCategoryFilterResult, preAlarmFilterRequest.getKeywords());

//            根据preAlarmCategory业务分类过滤
            List<PreAlarm> preAlarmSeverityFilterResult = this.getFilterResult("preAlarmSeverity", preAlarmKeywordsFilterResult, preAlarmFilterRequest.getPreAlarmSeverity());
//            根据preAlarmConfirmed是否确认过滤
            List<PreAlarm> preAlarmConfirmedFilterResult = this.getFilterResult("preAlarmConfirmed", preAlarmSeverityFilterResult, preAlarmFilterRequest.getPreAlarmConfirmed() == null ? "" : preAlarmFilterRequest.getPreAlarmConfirmed().toString());
//            根据preAlarmEnded是否结束过滤
            res = this.getFilterResult("preAlarmEnded", preAlarmConfirmedFilterResult, preAlarmFilterRequest.getPreAlarmEnded() == null ? "" : preAlarmFilterRequest.getPreAlarmEnded().toString());
            if (StringUtils.isNotEmpty(preAlarmFilterRequest.getOrderByKey())) {
                //排序
                this.sortList(res, preAlarmFilterRequest.getOrderByKey(), preAlarmFilterRequest.getOrderByDirection());
            }
            else {
                //默认按预警开始时间倒序排序
                this.sortList(res, "startTime", "DESC");
            }
        }
//        构建分页数据
        return buildAttributePageList(res, preAlarmFilterRequest.getNumber(), preAlarmFilterRequest.getSize());
    }

    private String resetPreAlarmSortPara(String originParam) {
        if (originParam.equalsIgnoreCase("preAlarmSeverityName")) {
            return "preAlarmSeverity";
        }
        if (originParam.equalsIgnoreCase("globalResourceName")) {
            return "globalResourceId";
        }
        if (originParam.equalsIgnoreCase("levelOfPathName")) {
            return "levelOfPath";
        }
        if (originParam.equalsIgnoreCase("preAlarmCategoryName")) {
            return "preAlarmCategory";
        }
        return originParam;
    }

    @Override
    public List<PreAlarm> findAlarms(Integer objectId, Integer objectTypeId) {
        List<PreAlarm> result = mapResourceObjectCache.get(objectId + "." + objectTypeId);
        return result == null ? new ArrayList<>() : result;
    }

    /**
     * @Description: 查找配电回放的预警
     * @Author: Li.Qupan.Pan
     * @Date: 2023/8/16 15:15
     **/
    public List<PreAlarm> findAlarmsByPowerDistributionPlayBack(Integer objectId, Integer objectTypeId, Date time) {
        List<PreAlarm> res = new ArrayList<>();

        // Get result from cache
        List<PreAlarm> result = mapResourceObjectCache.get(objectId + "." + objectTypeId);
        if (result != null) {
            res.addAll(result.stream()
                    .filter(item -> {
                        if (ObjectUtil.isNull(item.getEndTime())){
                            return item.getStartTime().before(time) && item.getPreAlarmCategory() == 3;
                        }else{
                            return item.getStartTime().before(time) && item.getEndTime().after(time) && item.getPreAlarmCategory() == 3;
                        }
                    })
                    .toList());  // Changed toList() to collect(Collectors.toList())
        }

        // No need for the redundant stream operation here - it was removed

        // Get alarms from service
        List<PreAlarm> alarmsByPowerDistributionPlayBack = preAlarmHistoryService.findAlarmsByPowerDistributionPlayBack(objectId, objectTypeId, time);
        if (alarmsByPowerDistributionPlayBack != null) {
            res.addAll(alarmsByPowerDistributionPlayBack);
        }

        return res;
    }

    @Override
    public List<PreAlarm> findAllActiveAlarm() {
        List<PreAlarm> activeAlarms = preAlarms.stream()
                .filter(item -> (item.getEndTime() == null))
                .toList();
        return activeAlarms == null ? new ArrayList<>() : activeAlarms;
    }

    @Override
    public List<PreAlarm> findAllPreAlarm() {
        return this.preAlarms;
    }

    @Override
    public List<PreAlarm> findBeforePreAlarms(Integer differDays) {
        Date beforeDate = DateUtil.dateAddDays(new Date(), differDays);
        List<PreAlarm> activeAlarms = preAlarms.stream()
                .filter(item -> (item.getConfirmTime() == null && item.getStartTime().before(beforeDate)))
                .toList();
        return activeAlarms == null ? new ArrayList<>() : activeAlarms;
    }

    @Override
    public List<PreAlarm> findAllEndAndConfirmPreAlarms() {
        List<PreAlarm> activeAlarms = preAlarms.stream()
                .filter(item -> (item.getEndTime() != null && item.getConfirmTime() != null))
                .toList();
        return activeAlarms == null ? new ArrayList<>() : activeAlarms;
    }

    @Override
    public PreAlarm savePreAlarmToDb(PreAlarmPoint preAlarmPoint, String triggerValue, String sampleTime) {
        //判断是否已经产生了该告警，如果已经产生，则返回该告警
        List<PreAlarm> preAlarm = preAlarms.stream()
                .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId()) && item.getEndTime() == null))
                .toList();
        if (preAlarm != null && preAlarm.size() > 0) {
            return preAlarm.get(0);
        }
        PreAlarm saveDate = createPreAlarmEntity(preAlarmPoint, triggerValue, sampleTime);
        preAlarmMapper.insert(saveDate);
        addToCache(saveDate);
        //将设备超期服役的预警加入preAlarmChange
        if(preAlarmPoint.getPreAlarmCategory().equals(10))
            insertPreAlarmChange(saveDate,AlarmOperationTypeEnum.START.getValue());
        if(pdV2Enable) {
            addNewEquipCapacityPrealarmToQueue(saveDate, preAlarmPoint);
        }
        preAlarmNotifyService.addPreAlarmMsg(saveDate);
        return saveDate;
    }

    @Override
    public PreAlarm createPreAlarmEntity(PreAlarmPoint preAlarmPoint, String triggerValue, String sampleTime) {
        PreAlarm createdAlarm = new PreAlarm();
        createdAlarm.setPreAlarmPointId(preAlarmPoint.getPreAlarmPointId());
        createdAlarm.setMeanings(preAlarmPoint.getMeanings());

        PreAlarmSeverity preAlarmSeverity = preAlarmSeverityService.findByPreAlarmSeverityId(preAlarmPoint.getPreAlarmSeverity());
        createdAlarm.setPreAlarmSeverity(preAlarmPoint.getPreAlarmSeverity());
        if (preAlarmSeverity != null) {
            createdAlarm.setPreAlarmSeverityName(preAlarmSeverity.getPreAlarmSeverityName());
            createdAlarm.setColor(preAlarmSeverity.getColor());
        }
        PreAlarmCategory preAlarmCategory = preAlarmCategoryService.findByCategoryId(preAlarmPoint.getPreAlarmCategory());
        createdAlarm.setPreAlarmCategory(preAlarmPoint.getPreAlarmCategory());
        if (preAlarmCategory != null) {
            createdAlarm.setPreAlarmCategoryName(preAlarmCategory.getCategoryName());
        }
        createdAlarm.setObjectId(preAlarmPoint.getObjectId());
        createdAlarm.setObjectTypeId(preAlarmPoint.getObjectTypeId());
        //查找资源的层级节点地址
        createdAlarm.setResourceStructureId(preAlarmPoint.getResourceStructureId());
        createdAlarm.setObjectName(preAlarmPoint.getObjectName());
        createdAlarm.setLevelOfPath(preAlarmPoint.getLevelOfPath());
        if(preAlarmPoint.getLevelOfPathName() == null){
            createdAlarm.setLevelOfPathName(resourceStructureManager.getLevelOfPathName(preAlarmPoint.getLevelOfPath()));
        }else {
            createdAlarm.setLevelOfPathName(preAlarmPoint.getLevelOfPathName());
        }

        if(preAlarmPoint.getPreAlarmCategory() < 6){
            createdAlarm.setUniqueId(preAlarmPoint.getUniqueId());
            createdAlarm.setUniqueName(getUniqueNames(preAlarmPoint.getUniqueId(), preAlarmPoint.getPreAlarmCategory()));
        }
        createdAlarm.setPreAlarmCategory(preAlarmPoint.getPreAlarmCategory());
        createdAlarm.setTriggerValue(triggerValue);
        createdAlarm.setUnit(preAlarmPoint.getUnit());
        if (sampleTime.length() > 10) {
            createdAlarm.setSampleTime(getDate(sampleTime));
        }

        createdAlarm.setStartTime(new Date());
        //如果不带状态，开始、结束时间一样
        if (preAlarmPoint.getStateful() != null && preAlarmPoint.getStateful() == PreAlarmConst.STATEFUL) {
            createdAlarm.setEndTime(new Date());
        }
        if (preAlarmPoint.getExpression().contains("lastDay")) {
            createdAlarm.setStartTime(getDate(sampleTime));
            createdAlarm.setEndTime(getDate(sampleTime.substring(0, 10) + " 23:59:59"));
        }
//        判断如果是能耗 解析表达式获取指标id拿到能耗类型
        if (preAlarmPoint.getPreAlarmCategory() == 2) {
            Pattern p = Pattern.compile("(?<=ci\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(preAlarmPoint.getExpression());
            Integer complexId = null;
            if (m.find()) {
                complexId = Integer.valueOf(m.group());
            }
            if (ObjectUtil.isNotNull(complexId)) {
                ComplexIndex findComplexIndex = complexIndexService.findByComplexIndexId(complexId);
                if (ObjectUtil.isNotNull(findComplexIndex)) {
                    createdAlarm.setBusinessTypeId(findComplexIndex.getBusinessTypeId());
                }
            }

        }
        return createdAlarm;
    }

    private String getUniqueNames(String uniqueId, Integer preAlarmCategory) {
        String result = "";
        String[] uniques = uniqueId.split(",");
        if (StringUtils.isNotEmpty(uniques)) {
            if (uniqueId.contains(".")) {
                for (String item : uniques) {
                    result += findSignalByEquipmentIdAndSignalId(item).getSignalName() + ",";
                }
            } else {
                switch (preAlarmCategory) {
                    case PreAlarmConst.CATEGORY_COMPLEX: //指标
                    case PreAlarmConst.CATEGORY_ENERGY: //能耗
                        for (String item : uniques) {
                            result += complexIndexService.findByComplexIndexId(Integer.parseInt(item)).getComplexIndexName() + ",";
                        }
                        break;
                    case PreAlarmConst.CATEGORY_CAPACITY: //容量
                        for (String item : uniques) {
                            result += capacityAttributeService.findAttribute(Integer.parseInt(item)).getAttributeName() + ",";
                        }
                        break;
                    case PreAlarmConst.CATEGORY_CORE_POINT: //测点
                        for (String item : uniques) {
                            result += findSignalByEquipmentIdAndSignalId(item).getSignalName() + ",";
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        return result.substring(0, result.length() - 1);
    }

    @Override
    public void endExistPreAlarms(List<PreAlarm> preAlarms) {

        for (PreAlarm alarm : preAlarms) {
            alarm.setEndTime(new Date());
            preAlarmMapper.updateById(alarm);
            addToCache(alarm);
        }
    }

    private Signal findSignalByEquipmentIdAndSignalId(String uniqueId) {
        String[] uniques = uniqueId.split("\\.");
        Integer equId = Integer.parseInt(uniques[0]);
        Integer signalId = Integer.parseInt(uniques[1]);
        List<Signal> signals = signalService.findSignalsByEquipmentId(equId);
        Signal res = new Signal();
        for (Signal item : signals) {
            if (item.getSignalId().equals(signalId)) {
                res = item;
            }
        }
        return res;
    }

    @Override
    public PreAlarm endPreAlarm(Integer preAlarmId) {
        PreAlarm preAlarm = mapPreAlarmIdMap.get(preAlarmId);
        if (preAlarm != null) {
            preAlarm.setEndTime(new Date());
            preAlarmMapper.updateById(preAlarm);
            addToCache(preAlarm);
        }
        return preAlarm;
    }

    @Override
    public List<PreAlarm> endPreAlarms(List<Integer> preAlarmIds) {
        List<PreAlarm> result = new ArrayList<>();
        for (Integer alarmId : preAlarmIds) {
            result.add(endPreAlarm(alarmId));
        }
        return result;
    }

    @Override
    public List<PreAlarm> confirmPreAlarms(List<PreAlarmConfirm> alarmConfirms) {

        List<PreAlarm> result = new ArrayList<>();
        for (PreAlarmConfirm alarmConfirm : alarmConfirms) {
            PreAlarm preAlarm = mapPreAlarmIdMap.get(alarmConfirm.getPreAlarmId());
            if (preAlarm != null) {
                preAlarm.setConfirmTime(new Date());
                preAlarm.setConfirmId(alarmConfirm.getConfirmId());
                AccountDTO account = accountService.findByUserId(alarmConfirm.getConfirmId());
                if (account != null) {
                    preAlarm.setConfirmName(account.getUserName());
                }
                preAlarm.setRemark(alarmConfirm.getRemark());
                preAlarmMapper.updateById(preAlarm);
                addToCache(preAlarm);
                result.add(preAlarm);
                //设备超期服役预警添加到preAlarmChange表
                if (preAlarm.getPreAlarmCategory().equals(10)) {
                    if (ObjectUtil.isNotNull(preAlarm.getRemark()) && preAlarm.getRemark().equals("程序自动确认3天前告警")) {
                        insertPreAlarmChangeAutoConfirm(preAlarm);
                    } else {
                        insertPreAlarmChange(preAlarm, AlarmOperationTypeEnum.CONFIRM.getValue());
                    }
                }
            }
        }
        return result;
    }

    private void insertPreAlarmChangeAutoConfirm(PreAlarm preAlarm) {
        try {
            String sequenceId = preAlarm.getResourceStructureId().toString() + preAlarm.getPreAlarmPointId().toString() + preAlarm.getPreAlarmId().toString();
            QueryWrapper<PreAlarmChange> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("sequenceId", sequenceId);
            List<PreAlarmChange> existPreAlarmChanges = preAlarmChangeMapper.selectList(queryWrapper);
            if (existPreAlarmChanges == null || existPreAlarmChanges.isEmpty()) {
                log.error("insertPreAlarmChange error operationType is 3,existPreAlarmChange is null");
                return;
            }
            //先取插入的记录
            PreAlarmChange existPreAlarmChange = existPreAlarmChanges.get(0);
            //说明已经有结束的记录了
            if (existPreAlarmChanges.size() > 1)
                existPreAlarmChange = existPreAlarmChanges.get(1);

            existPreAlarmChange.setOperationType(AlarmOperationTypeEnum.CONFIRM.getValue());
            existPreAlarmChange.setSerialNo(null);
            existPreAlarmChange.setConfirmorId(-2);
            existPreAlarmChange.setConfirmorName("系统自动确认");
            existPreAlarmChange.setConfirmTime(new Date());
            existPreAlarmChange.setInsertTime(new Date());
            preAlarmChangeMapper.insert(existPreAlarmChange);
        } catch (Exception e) {
            log.error("PreAlarmServiceImpl insertPreAlarmChangeAutoConfirm error: ", e);
        }
    }

    @Override
    public List<PreAlarm> updatePreAlarmsRemark(List<PreAlarmConfirm> alarmConfirms) {
        List<PreAlarm> result = new ArrayList<>();
        for (PreAlarmConfirm alarmConfirm : alarmConfirms) {
            PreAlarm preAlarm = mapPreAlarmIdMap.get(alarmConfirm.getPreAlarmId());
            if (preAlarm != null) {
                preAlarm.setRemark(alarmConfirm.getRemark());
                preAlarmMapper.updateById(preAlarm);
                addToCache(preAlarm);
                result.add(preAlarm);
            }
        }
        return result;
    }

    @Override
    public List<PreAlarm> deletePreAlarms(List<Integer> preAlarmIds) {

        List<PreAlarm> result = new ArrayList<>();
        for (Integer alarmId : preAlarmIds) {
            PreAlarm preAlarm = mapPreAlarmIdMap.get(alarmId);
            if (preAlarm != null) {
                this.deleteById(alarmId);
                result.add(preAlarm);
            }
        }
        return result;
    }

    @Override
    public Integer deleteById(Integer preAlarmId) {
        deleteFromCache(mapPreAlarmIdMap.get(preAlarmId));
        return preAlarmMapper.deleteById(preAlarmId);
    }

    @Override
    public List<PreAlarm> generateVirtualPreAlarm(VirtualPreAlarmParam param) {
        List<PreAlarmPoint> preAlarmPoints = preAlarmPointService.findPreAlarmPoints(param.getObjectId(), param.getObjectTypeId(), param.getCategoryId());
        preAlarmPoints = preAlarmPoints.stream().sorted(Comparator.comparing(PreAlarmPoint::getPreAlarmSeverity)).toList();
        List<PreAlarm> result = new ArrayList<>();
        for (PreAlarmPoint preAlarmPoint : preAlarmPoints) {
            try {
                String evalExpression = ExpressionParserFunction.parseExpression(preAlarmPoint.getExpression());
                evalExpression = getReplacedExpression(param, evalExpression);
                ExpressionResult eval = ExpressionParserFunction.eval(evalExpression);
                if (eval != null && eval.getCompareResult()) {
                    //创建预警
                    PreAlarm preAlarm = createPreAlarmEntity(preAlarmPoint, param.getSampleValue(), param.getSampleTime());
                    //检查是否满足抑制表达式，如果满足，则结束告警。
                    if (StringUtils.isNotEmpty(preAlarmPoint.getAbnormalExpression())) {
                        ExpressionResult abnormalExpressionCalc = preAlarmExpressionCalc(preAlarmPoint.getAbnormalExpression());
                        if (abnormalExpressionCalc != null && abnormalExpressionCalc.getCompareResult()) {
                            endPreAlarm(preAlarm.getPreAlarmId());
                        }
                    }
                    result.add(preAlarm);
                }
            } catch (Exception e) {
                log.error("PreAlarmServiceImpl.generateVirtualPreAlarm error:" + e.getMessage());
                // 表达式异常
                // e.printStackTrace();
                //log.error("complexIndex:" + complexIndex.getComplexIndexId() + "表达式 ： " + complexIndex.getExpression() + " 错误信息:" + e.getMessage());
            }

        }
        return result;
    }

    private String getReplacedExpression(VirtualPreAlarmParam param, String expression) {
        switch (param.getCategoryId()) {
            case PreAlarmConst.CATEGORY_CAPACITY:
                return expression.replace("#ca(" + param.getUniqueId() + ")", StringUtils.isEmpty(param.sampleValue) ? "0" : param.sampleValue);
            case PreAlarmConst.CATEGORY_CORE_POINT:
                return expression.replace("#cp(" + param.getUniqueId() + ")", StringUtils.isEmpty(param.sampleValue) ? "0" : param.sampleValue);
            default:
                return expression.replace("#ci(" + param.getUniqueId() + ")", StringUtils.isEmpty(param.sampleValue) ? "0" : param.sampleValue);
        }
    }

    @Override
    public void scheduleGeneratePreAlarm(PreAlarmPoint preAlarmPoint) {
        try {
            if (preAlarmPointService.getResetScheduleFlag()) return;
            //如果处于屏蔽期间，则不进行计算
            if (!preAlarmPointValid(preAlarmPoint)) {
                List<PreAlarm> _preAlarms = preAlarms.stream()
                        .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId()) && item.getEndTime() == null))
                        .toList();
                if (_preAlarms != null && _preAlarms.size() > 0) {
                    _preAlarms.forEach(item -> {
                        item.setEndTime(new Date());
                        preAlarmMapper.updateById(item);
                        addToCache(item);
                        //设备超期服役预警添加到preAlarmChange表
                        if (item.getPreAlarmCategory().equals(10)){
                            insertPreAlarmChange(item,AlarmOperationTypeEnum.END.getValue());
                        }
                    });
                }
                return;
            }
            String evalExpression ;
            ExpressionResult eval = new ExpressionResult();
            //单独处理油机保养的预警表达式
            if(preAlarmPoint.getPreAlarmCategory() == 6){
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 获取当前日期
                LocalDateTime currentDate = LocalDateTime.now();
                String currentTime = currentDate.format(formatter);
                String sampleTime = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
                eval.setSampleTime(sampleTime.substring(0, 13) + ":00:00");

                evalExpression = preAlarmPoint.getExpression();
                if(evalExpression == null || evalExpression.isEmpty()){
                    log.error("scheduleGeneratePreAlarm error oil evalExpression is null");
                    return;
                }
                else {
                    String[] expressionParams = evalExpression.split("\\|");
                    if(expressionParams.length == 3){
                        //间隔时间
                        int timeInterval = Integer.parseInt(expressionParams[0] == null ? "0" : expressionParams[0]);
                        //运行时间
                        int runTime = Integer.parseInt(expressionParams[1] == null ? "0" : expressionParams[1]);
                        runTime = runTime * 3600;//换算成秒

                        String startTimeStr;
                        //上次保养时间开始时间
                        String theLastMaintenanceTime = preAlarmPointMapper.getGeneratorLastMaintenanceTime(preAlarmPoint.getObjectId());
                        //如果上次保养时间为在保养表里查不到，则用默认的开始时间
                        if(theLastMaintenanceTime == null)
                            startTimeStr = expressionParams[2] == null ? currentTime : (expressionParams[2]);
                        else
                            startTimeStr = theLastMaintenanceTime;

                        LocalDateTime endDate = LocalDateTime.parse(currentTime, formatter);
                        LocalDateTime startDate = LocalDateTime.parse(startTimeStr, formatter);
                        Date startTime = dateFormat.parse(startTimeStr);
                        Date now = new Date();
                        //时间间隔
                        long daysDifference = ChronoUnit.DAYS.between(startDate, endDate);
                        //如果时间间隔超过了设定的范围直接产生告警
                        if(daysDifference >= timeInterval){
                            eval.setTriggerValue("间隔天数:" + daysDifference + "天");
                            eval.setCompareResult(true);
                        }
                        else {
                            //根据设备ID从数据库获取该油机的累计运行时间
                            int equipmentId = preAlarmPoint.getObjectId();
                            Long generatorRunTime = preAlarmPointMapper.getExpressionRunTimeCompareValue(equipmentId,startTimeStr,currentTime);
                            Long addTime = 0L;
                            List<GenePowerGenerationRecordDTO> noEndTimeResult = preAlarmPointMapper.getNoEndTimeRecord(equipmentId);
                            //如果存在没有EndTime的记录，则用当前时间减去开始时间累加起来
                            if(noEndTimeResult != null && noEndTimeResult.size() == 1){
                                Date geneStartTime = noEndTimeResult.get(0).getStartTime();
                                if(geneStartTime != null){
                                    //如果开机记录的时间在保养时间之后
                                    if (geneStartTime.compareTo(startTime) >= 0) {
                                        //当前时间在开机记录时间之后
                                        if (now.compareTo(geneStartTime) >= 0) {
                                            addTime = ((now.getTime() - geneStartTime.getTime()) / 1000);
                                        }
                                    } else {
                                        addTime = ((now.getTime() - startTime.getTime()) / 1000);
                                    }
                                }
                            }
                            generatorRunTime = generatorRunTime + addTime;
                            if(generatorRunTime >= runTime){
                                eval.setTriggerValue("累计运行时间:" + NumberUtil.doubleAccuracy((generatorRunTime / 3600.0),2) + "小时");
                                eval.setCompareResult(true);
                            }
                            else {
                                eval.setTriggerValue(null);
                                eval.setCompareResult(false);
                            }
                        }
                    }
                    else {
                        log.error("scheduleGeneratePreAlarm error oil maintenance expressionParams.length != 3");
                        eval.setTriggerValue(null);
                        eval.setCompareResult(false);
                    }
                }
            }
            //单独处理设备超期服役的预警表达式
            else if(preAlarmPoint.getPreAlarmCategory().equals(10)){
                //目前默认场景是只在设备上使用
                Integer equipmentId = preAlarmPoint.getObjectId();
                Equipment equipmentInfo = equipmentManager.getEquipmentById(equipmentId);
                if(equipmentInfo == null || equipmentInfo.getUsedDate() == null || equipmentInfo.getUsedLimit() == null ){
                    log.error("scheduleGeneratePreAlarm error equipment required information is null,equipmentId = " + equipmentId + " preAlarmPointId = " + preAlarmPoint.getPreAlarmPointId());
                    return;
                }
                // 获取当前日期，防止一天生成多条相同预警
                LocalDateTime currentDate = LocalDateTime.now();
                String sampleTime = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                eval.setSampleTime(sampleTime + " 00:00:00");

                String calExpression = preAlarmPoint.getExpression();
                if(calExpression == null || calExpression.isEmpty()){
                    log.error("scheduleGeneratePreAlarm error calExpression is null" + " preAlarmPointId = " + preAlarmPoint.getPreAlarmPointId());
                    return;
                }
                else {
                    String[] expressionParams = calExpression.split("\\|");
                    if (expressionParams.length == 3) {
                        //启用时间
//                        String useDateStr = expressionParams[0];
//                        //使用年限
//                        int useLimit = Integer.parseInt(expressionParams[1] == null ? "0" : expressionParams[1]);
                        Date useDate = equipmentInfo.getUsedDate();
                        Double useLimit = equipmentInfo.getUsedLimit();
                        long useLimitDays = (long) (useLimit * 365L);
                        //提前告知天数
                        int noticeDays = Integer.parseInt(expressionParams[2] == null ? "0" : expressionParams[2]);
                        long daysDiff = getDaysDifference(useDate,new Date());
//                        long daysDiff = 100L; //TODO 模拟结束告警，记得删除
                        Double yearDiff = NumberUtil.doubleAccuracy((daysDiff / 365D), 2);
                        if (daysDiff > useLimitDays) {
                            eval.setTriggerValue("启用时间：" + DateUtil.dateToString(useDate) + "，使用年限：" + useLimit + "，间隔年份：" + yearDiff);
                            eval.setCompareResult(true);
                        } else {
                            if (daysDiff > (useLimitDays - noticeDays)) {
                                eval.setTriggerValue("启用时间：" + DateUtil.dateToString(useDate) + "，使用年限：" + useLimit + "，间隔年份：" + yearDiff + "，提前告知天数：" + noticeDays);
                                eval.setCompareResult(true);
                            } else {
                                eval.setTriggerValue(null);
                                eval.setCompareResult(false);
                            }
                        }
                    }else {
                        log.error("scheduleGeneratePreAlarm error equipment out of service expressionParams.length != 3" + " preAlarmPointId = " + preAlarmPoint.getPreAlarmPointId());
                        return;
                    }
                }
            }
            else {
                evalExpression = ExpressionParserFunction.parseExpression(preAlarmPoint.getExpression());
                eval = ExpressionParserFunction.eval(evalExpression);
            }

            //达到产生预警的阈值
            if (eval != null && eval.getCompareResult()) {
                //这个预警配置是否产生了自诊断预警
                List<PreAlarm> selfNoticePreAlarmList = preAlarms.stream()
                        .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId()) && item.getPreAlarmCategory() == -1))
                        .toList();
                //如果预警配置的指标被删除，则结束确认且后续不产生此告警
                if(eval.getTriggerValue().equals("ComplexIndexNotExist")){
                    //已存在的预警配置出发的预警
                    List<PreAlarm> preAlarmList = preAlarms.stream()
                            .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId()) && item.getPreAlarmCategory() != -1 && item.getEndTime() == null))
                            .toList();
                    if (preAlarmList.size() > 0) {
                        preAlarmList.forEach(item -> {
                            item.setEndTime(new Date());
                            if(item.getConfirmTime() == null)
                                item.setConfirmTime(new Date());
                            if(item.getConfirmId()==null)
                                item.setConfirmId(-999);
                            if(item.getConfirmName() == null)
                                item.setConfirmName("System");
                            item.setRemark("该预警配置的指标已被删除，系统自动确认并结束此告警且后续不会生成，请检查配置！");
                            preAlarmMapper.updateById(item);
                            addToCache(item);
                        });
                    }
                    //生成一条自诊断预警
                    if(selfNoticePreAlarmList.isEmpty()){
                        createSelfNoticePreAlarm(preAlarmPoint);
                    }
                    return ;
                }
                //如果该配置有自诊断错误预警先结束自诊断预警
                if(!selfNoticePreAlarmList.isEmpty()){
                    selfNoticePreAlarmList.forEach(item -> {
                        item.setEndTime(new Date());
                        if(item.getConfirmTime() == null)
                            item.setConfirmTime(new Date());
                        if(item.getConfirmId() == null)
                            item.setConfirmId(-999);
                        if(item.getConfirmName() == null)
                            item.setConfirmName("System");
                        item.setRemark("该预警配置错误已修复，系统自动结束自诊断错误预警！");
                        preAlarmMapper.updateById(item);
                        addToCache(item);
                    });
                }
                //检查是否满足抑制表达式，如果满足，则结束告警。
                if (StringUtils.isNotEmpty(preAlarmPoint.getAbnormalExpression())) {
                    ExpressionResult abnormalExpressionCalc = preAlarmExpressionCalc(preAlarmPoint.getAbnormalExpression());
                    if (abnormalExpressionCalc != null && abnormalExpressionCalc.getCompareResult()) {
                        List<PreAlarm> preAlarmList = preAlarms.stream()
                                .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId()) && item.getEndTime() == null))
                                .toList();
                        if (preAlarmList.size() > 0) {
                            preAlarmList.forEach(item -> {
                                item.setEndTime(new Date());
                                preAlarmMapper.updateById(item);
                                addToCache(item);
                                //设备超期服役预警添加到preAlarmChange表
                                if (item.getPreAlarmCategory().equals(10)){
                                    insertPreAlarmChange(item,AlarmOperationTypeEnum.END.getValue());
                                }
                            });
                        }
                    } else {
                        //判断如果有采集时间及触发值一样的预警，则不产生新的预警
                        ExpressionResult finalEval = eval;
                        List<PreAlarm> samePreAlarms = preAlarms.stream()
                                .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId())
                                        && item.getTriggerValue().equals(finalEval.getTriggerValue())
                                        && item.getSampleTime().equals(getDate(finalEval.getSampleTime()))
                                        && item.getEndTime() == null))
                                .toList();

                        if (samePreAlarms.size() == 0) {
                            //创建预警
                            savePreAlarmToDb(preAlarmPoint, eval.getTriggerValue(), eval.getSampleTime());
                        }
                    }
                } else {
                    //判断如果有采集时间及触发值一样的预警，则不产生新的预警
                    ExpressionResult finalEval1 = eval;
                    List<PreAlarm> samePreAlarms = preAlarms.stream()
                            .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId())
                                    && item.getTriggerValue().equals(finalEval1.getTriggerValue())
                                    && item.getSampleTime().equals(getDate(finalEval1.getSampleTime()))
                                    && item.getEndTime() == null))
                            .toList();
                    if (samePreAlarms.size() == 0) {
                        //创建预警
                        savePreAlarmToDb(preAlarmPoint, eval.getTriggerValue(), eval.getSampleTime());
                    }
                }
            } else {
                //判断是否已经产生了该告警,如果不满足条件了，但是数据库中还有未结束的告警，那么结束这条告警。
                List<PreAlarm> preAlarmList = preAlarms.stream()
                        .filter(item -> (item.getPreAlarmPointId().equals(preAlarmPoint.getPreAlarmPointId()) && item.getEndTime() == null))
                        .toList();

                if (preAlarmList.size() > 0) {
                    preAlarmList.forEach(item -> {
                        item.setEndTime(new Date());
                        preAlarmMapper.updateById(item);
                        addToCache(item);
                        //设备超期服役预警添加到preAlarmChange表
                        if (item.getPreAlarmCategory().equals(10)){
                            insertPreAlarmChange(item,AlarmOperationTypeEnum.END.getValue());
                        }
                    });
                }
            }
        } catch (Exception e) {
            // 表达式异常
            //e.printStackTrace();
            log.error("PreAlarmServiceImpl.scheduleGeneratePreAlarm error:" , e);
        }
    }
    private long getDaysDifference(Date date1, Date date2) {
        // 将 Date 对象转换为 LocalDate 对象
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算两个 LocalDate 对象之间的天数差异
        return ChronoUnit.DAYS.between(localDate1, localDate2);
    }

    /**
     * 设备超期服役预警添加到preAlarmChange表
     * @param preAlarm 预警
     * @param operationType 操作类型
     */
    private void insertPreAlarmChange(PreAlarm preAlarm,Integer operationType){
        try {
            String sequenceId = preAlarm.getResourceStructureId().toString() + preAlarm.getPreAlarmPointId().toString() + preAlarm.getPreAlarmId().toString();
            //新增一条预警
            if (operationType.equals(AlarmOperationTypeEnum.START.getValue())) {
                PreAlarmChange preAlarmChange = new PreAlarmChange();
                preAlarmChange.setSequenceId(sequenceId);
                preAlarmChange.setOperationType(operationType);
                Equipment equipmentInfo = equipmentManager.getEquipmentById(preAlarm.getObjectId());
                if (equipmentInfo == null) {
                    log.error("insertPreAlarmChange error equipmentInfo is null");
                    return;
                }
                preAlarmChange.setEquipmentId(equipmentInfo.getEquipmentId());
                preAlarmChange.setEquipmentCategory(equipmentInfo.getEquipmentCategory());
                String equipmentCategoryName = Optional.ofNullable(dataItemService.findByEntryIdIdAndItemId(EQUIPMENT_CATEGORY_ENTRY_ID, equipmentInfo.getEquipmentCategory()))
                        .map(DataItem::getItemValue).orElse("");
                preAlarmChange.setEquipmentCategoryName(equipmentCategoryName);
                preAlarmChange.setEquipmentName(equipmentInfo.getEquipmentName());
                preAlarmChange.setEquipmentVendor(equipmentInfo.getVendor());

                preAlarmChange.setPreAlarmId(preAlarm.getPreAlarmId());
                preAlarmChange.setPreAlarmName(preAlarm.getMeanings());
                preAlarmChange.setMeanings(preAlarm.getTriggerValue());
                preAlarmChange.setTriggerValue(preAlarm.getTriggerValue());
                preAlarmChange.setPreAlarmPointId(preAlarm.getPreAlarmPointId());
                preAlarmChange.setPreAlarmCategory(preAlarm.getPreAlarmCategory());
                preAlarmChange.setPreAlarmCategoryName(preAlarm.getPreAlarmCategoryName());
                preAlarmChange.setPreAlarmSeverity(preAlarm.getPreAlarmSeverity());
                preAlarmChange.setPreAlarmSeverityName(preAlarm.getPreAlarmSeverityName());


                Station stationInfo = stationManager.findStationById(equipmentInfo.getStationId());
                if (stationInfo == null) {
                    log.error("insertPreAlarmChange error stationInfo is null");
                    return;
                }
                preAlarmChange.setStationId(stationInfo.getStationId());
                preAlarmChange.setStationName(stationInfo.getStationName());
                preAlarmChange.setStationCategoryId(stationInfo.getStationCategory());

                StationStructure center = stationStructureService.findRoot();
                preAlarmChange.setCenterId(center.getStructureId());
                preAlarmChange.setCenterName(center.getStructureName());

                StationStructure stationStructure = stationStructureService.findByStationId(equipmentInfo.getStationId());
                preAlarmChange.setStructureId(stationStructure.getStructureId());
                preAlarmChange.setStructureName(stationStructure.getStructureName());

                preAlarmChange.setStartTime(preAlarm.getStartTime());

                preAlarmChange.setLevelOfPathName(preAlarm.getLevelOfPathName());
                preAlarmChange.setResourceStructureId(preAlarm.getResourceStructureId());
                preAlarmChange.setInsertTime(new Date());
                preAlarmChange.setSerialNo(null);
                preAlarmChangeMapper.insert(preAlarmChange);

            } else if (operationType.equals(AlarmOperationTypeEnum.END.getValue())) {
                QueryWrapper<PreAlarmChange> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("sequenceId", sequenceId);
                List<PreAlarmChange> existPreAlarmChanges = preAlarmChangeMapper.selectList(queryWrapper);
                if (existPreAlarmChanges == null) {
                    log.error("insertPreAlarmChange error operationType is 2,existPreAlarmChange is null");
                    return;
                }
                //先取插入的记录
                PreAlarmChange existPreAlarmChange = existPreAlarmChanges.get(0);
                //说明已经有确认的记录了
                if (existPreAlarmChanges.size() > 1)
                    existPreAlarmChange = existPreAlarmChanges.get(1);

                existPreAlarmChange.setOperationType(AlarmOperationTypeEnum.END.getValue());
                existPreAlarmChange.setEndTime(new Date());
                existPreAlarmChange.setSerialNo(null);
                existPreAlarmChange.setInsertTime(new Date());
                preAlarmChangeMapper.insert(existPreAlarmChange);
            } else {
                QueryWrapper<PreAlarmChange> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("sequenceId", sequenceId);
                List<PreAlarmChange> existPreAlarmChanges = preAlarmChangeMapper.selectList(queryWrapper);
                if (existPreAlarmChanges == null || existPreAlarmChanges.isEmpty()) {
                    log.error("insertPreAlarmChange error operationType is 3,existPreAlarmChange is null");
                    return;
                }
                //先取插入的记录
                PreAlarmChange existPreAlarmChange = existPreAlarmChanges.get(0);
                //说明已经有结束的记录了
                if (existPreAlarmChanges.size() > 1)
                    existPreAlarmChange = existPreAlarmChanges.get(1);

                existPreAlarmChange.setOperationType(AlarmOperationTypeEnum.CONFIRM.getValue());
                existPreAlarmChange.setSerialNo(null);
                existPreAlarmChange.setConfirmorId(TokenUserUtil.getLoginUserId());
                existPreAlarmChange.setConfirmorName(TokenUserUtil.getLoginUserName());
                existPreAlarmChange.setConfirmTime(new Date());
                existPreAlarmChange.setInsertTime(new Date());
                preAlarmChangeMapper.insert(existPreAlarmChange);
            }
        }catch (Exception ex){
            log.error("PreAlarmServiceImpl InsertPreAlarmChange error: ",ex);
        }
    }

    private Boolean isDeviceType(int objectType) {
        return objectType == PreAlarmConst.OBJECT_TYPE_DEVICE ||
                objectType == PreAlarmConst.OBJECT_TYPE_COMMON ||
                objectType == PreAlarmConst.OBJECT_TYPE_RACK ||
                objectType == PreAlarmConst.OBJECT_TYPE_IT_DEVICE;
    }

    private Integer getGlobalResourceId(Integer objectId, Integer objectType) {
        ResourceObject resourceObject = new ResourceObject(objectId, objectType);
        ResourceObjectEntity globalResourceNodes = resourceObjectManager.findEntityByObject(resourceObject);
        if (globalResourceNodes != null) {
            return globalResourceNodes.getResourceStructureId();
        }
        return null;
    }

    @Override
    public Integer contByPreAlarmCategory(Integer preAlarmCategory, Integer objectId, Integer objectTypeId) {
        Integer result = 0;
        if (isDeviceType(objectTypeId)) {
            QueryWrapper<PreAlarm> wrapper = new QueryWrapper<>();
            wrapper.eq("objectId", objectId).eq("objectTypeId", objectTypeId).eq("preAlarmCategory", preAlarmCategory);
            result = preAlarmMapper.selectCount(wrapper).intValue();
        } else {
            QueryWrapper<PreAlarm> wrapper = new QueryWrapper<>();
            wrapper.eq("objectId", objectId).eq("objectTypeId", objectTypeId).or().like("LevelOfPath", objectId).and(wq -> wq.eq("PreAlarmCategory", preAlarmCategory));
            result = preAlarmMapper.selectCount(wrapper).intValue();
        }
        return result;
    }

    @Override
    public PreAlarm getMaxSeverityPreAlarmByCategory(Integer preAlarmCategory, String uniqueId) {
        List<PreAlarm> preAlarms = Optional.ofNullable(mapUniqueIdCache.get(uniqueId))
                .orElse(Collections.emptyList());
        PreAlarm result = preAlarms.stream()
                .filter(item -> item.getPreAlarmCategory().equals(preAlarmCategory))
                .max(Comparator.comparing(PreAlarm::getPreAlarmSeverity))
                .orElse(null);
        return result;

    }

    @Override
    public List<PreAlarmSeverityCount> findPreAlarmSeverityCount(Integer preAlarmCategory, Integer objectId, Integer objectType) {
        List<PreAlarmSeverityCount> result = new ArrayList<>();
        if (preAlarmCategory > 0 && objectId > 0) {
            if (isDeviceType(objectType)) {
                result = preAlarmMapper.devicePreAlarmSeverityStatistics(preAlarmCategory, getGlobalResourceId(objectId, objectType));
            } else {
                result = preAlarmMapper.structurePreAlarmSeverityStatistics(preAlarmCategory, objectId);
            }
        } else {
            result = preAlarmMapper.preAlarmSeverityStatistics();
        }
        return result;
    }

    public List<PreAlarmStatisticItem> findPreAlarmStatistics() {
        List<PreAlarmSeverity> preAlarmSeverities = preAlarmSeverityService.findAllPreAlarmSeverity();
        List<PreAlarmStatisticItem> result = new ArrayList<>();
        List<PreAlarmSeverityCount> preAlarmSeverityStatistics = preAlarmMapper.preAlarmSeverityStatistics();
        for (PreAlarmSeverity preAlarmSeverity : preAlarmSeverities) {
            PreAlarmStatisticItem preAlarmStatisticItem = new PreAlarmStatisticItem();
            preAlarmStatisticItem.setPreAlarmSeverityName(preAlarmSeverity.getPreAlarmSeverityName());
            preAlarmStatisticItem.setColor(preAlarmSeverity.getColor());
            List<PreAlarmSeverityCount> filteredCount = preAlarmSeverityStatistics.stream().filter(item -> (
                    item.getPreAlarmSeverityName().equals(preAlarmSeverity.getPreAlarmSeverityName())
            )).collect(Collectors.toList());
            preAlarmStatisticItem.setSeverityCount(filteredCount.size() > 0 ? filteredCount.get(0).getSeverityCount() : 0);
            result.add(preAlarmStatisticItem);
        }
        return result;
    }

    private ExpressionResult preAlarmExpressionCalc(String expression) {
        if (expression != null) {
            String evalExpression = ExpressionParserFunction.parseExpression(expression);
            ExpressionResult eval = null;
            try {
                eval = ExpressionParserFunction.eval(evalExpression);
            } catch (NoSuchMethodException e) {
                log.error("preAlarmExpressionCalc", e);
                return null;
            }
            return eval;
        }
        return null;
    }


    private Boolean preAlarmPointValid(PreAlarmPoint preAlarmPoint) {

        if (preAlarmPoint.getEnable() == null || preAlarmPoint.getEnable() == PreAlarmConst.ENABLE_FORBID) return false;
        if (preAlarmPoint.getMaskType() != null) {
            switch (preAlarmPoint.getMaskType()) {
                case PreAlarmConst.MASK_TYPE_TIME_SECTION:
                    if (preAlarmPoint.getMaskStartTime() != null && preAlarmPoint.getMaskEndTime() != null) {
                        if (this.getNow().before(preAlarmPoint.getMaskStartTime())) return true;
                        if (this.getNow().after(preAlarmPoint.getMaskEndTime())) return true;
                    }
                    return false;
            }
        }
        return true;
    }

    @Override
    public List<PreAlarm> findByPreAlarmCategory(int preAlarmCategory) {
        return preAlarms.stream()
                .filter(item -> (item.getPreAlarmCategory().equals(preAlarmCategory)))
                .toList();
    }

    private PreAlarm createPrealarmByEnergy(PreAlarmDTO preAlarmDTO) {
        ResourceStructure globalResourceNode = resourceStructureService.findById(preAlarmDTO.getObjectId());
        PreAlarm preAlarm = new PreAlarm();
        preAlarm.setPreAlarmPointId(preAlarmDTO.getPreAlarmPointId());
        preAlarm.setMeanings(preAlarmDTO.getMeanings());
        preAlarm.setPreAlarmSeverity(preAlarmDTO.getPreAlarmSeverity());
        preAlarm.setPreAlarmCategory(2);
        preAlarm.setObjectId(preAlarmDTO.getObjectId());
        preAlarm.setObjectTypeId(preAlarmDTO.getObjectTypeId());
        preAlarm.setResourceStructureId(preAlarmDTO.getObjectId());
        preAlarm.setTriggerValue(preAlarmDTO.getTriggerValue());
        preAlarm.setStartTime(preAlarmDTO.getStartTime());
        preAlarm.setUniqueName(preAlarmDTO.getUniqueName());
        preAlarm.setPreAlarmSeverityName(preAlarmSeverityService.findByPreAlarmSeverityId(preAlarmDTO.getPreAlarmSeverity()).getPreAlarmSeverityName());
        preAlarm.setColor(preAlarmSeverityService.findByPreAlarmSeverityId(preAlarmDTO.getPreAlarmSeverity()).getColor());
        preAlarm.setPreAlarmCategoryName(preAlarmCategoryService.findByCategoryId(preAlarm.getPreAlarmCategory()).getCategoryName());
        preAlarm.setObjectName(globalResourceNode.getResourceStructureName());
        preAlarm.setLevelOfPath(globalResourceNode.getLevelOfPath());
        preAlarm.setBusinessTypeId(preAlarmDTO.getBusinessTypeId());
        String levelOfPathName = resourceStructureManager.getLevelOfPathName(globalResourceNode.getLevelOfPath());
        //如果是IT设备的话，路径就把机架的名字加在房间的路径后边。
        if (globalResourceNode.getStructureTypeId() == PreAlarmConst.OBJECT_TYPE_IT_DEVICE) {
            ResourceStructure parentNode = resourceStructureService.findById(globalResourceNode.getParentResourceStructureId());
            levelOfPathName = levelOfPathName + "_" + parentNode.getResourceStructureName();
        }
        preAlarm.setLevelOfPathName(levelOfPathName);
        preAlarm.setUnit(preAlarmDTO.getUnit());
        return preAlarm;
    }

    private List<Date> getDateRangeByYearAndMonth(Integer year, Integer month) {
        List<Date> res = new ArrayList<>();
        Calendar start = Calendar.getInstance();
        start.set(Calendar.YEAR, year);
        start.set(Calendar.MONTH, month - 1);
        int firstDay = start.getActualMinimum(Calendar.DAY_OF_MONTH);
        start.set(Calendar.DAY_OF_MONTH, firstDay);
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        Date firstDayOfMonth = start.getTime();

        Calendar end = Calendar.getInstance();
        end.set(Calendar.YEAR, year);
        end.set(Calendar.MONTH, month - 1);
        int lastDay = end.getActualMaximum(Calendar.DAY_OF_MONTH);
        end.set(Calendar.DAY_OF_MONTH, lastDay);
        end.set(Calendar.HOUR_OF_DAY, 23);
        end.set(Calendar.MINUTE, 59);
        end.set(Calendar.SECOND, 59);
        Date lastDayOfMonth = end.getTime();
        res.add(firstDayOfMonth);
        res.add(lastDayOfMonth);
        return res;
    }

    private void endLastMonthPrealarm(Date startTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        Integer year = calendar.get(Calendar.YEAR);
        Integer month = calendar.get(Calendar.MONTH);
        List<Date> date = getDateRangeByYearAndMonth(year, month);
        for (PreAlarm item : preAlarms) {
            if (date.get(0).compareTo(item.getStartTime()) <= 0 && date.get(1).compareTo(item.getStartTime()) >= 0 && item.getEndTime() == null) {
                Calendar c = Calendar.getInstance();
                c.setTime(startTime);
                c.set(Calendar.SECOND, -1);
                item.setEndTime(c.getTime());
                preAlarmMapper.updateById(item);
                addToCache(item);
            }
        }
    }

    @Override
    public PreAlarm createPreAlarmByEnergy(PreAlarmDTO preAlarmDTO) {
        endLastMonthPrealarm(preAlarmDTO.getStartTime());
        if (preAlarmDTO.getFlag() == 0) {
            List<PreAlarm> same = preAlarms.stream()
                    .filter(item -> (item.getObjectId().equals(preAlarmDTO.getObjectId()) && item.getObjectTypeId().equals(preAlarmDTO.getObjectTypeId()) && item.getPreAlarmPointId().equals(preAlarmDTO.getPreAlarmPointId()) && item.getPreAlarmSeverity().equals(preAlarmDTO.getPreAlarmSeverity()) && item.getEndTime() == null))
                    .toList();
            if (same.size() != 0) {
                return same.get(0);
            }
            List<PreAlarm> sameNotByPreAlarmSeverity = preAlarms.stream()
                    .filter(item -> (item.getObjectId().equals(preAlarmDTO.getObjectId()) && item.getObjectTypeId().equals(preAlarmDTO.getObjectTypeId()) && item.getPreAlarmPointId().equals(preAlarmDTO.getPreAlarmPointId()) && item.getEndTime() == null))
                    .toList();
            if (sameNotByPreAlarmSeverity.size() != 0) {
                for (PreAlarm item : sameNotByPreAlarmSeverity) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(preAlarmDTO.getStartTime());
                    calendar.set(Calendar.SECOND, -1);
                    item.setEndTime(calendar.getTime());
                    preAlarmMapper.updateById(item);
                    addToCache(item);
                }
            }
            PreAlarm preAlarm = createPrealarmByEnergy(preAlarmDTO);
            preAlarmMapper.insert(preAlarm);
            addToCache(preAlarm);
            return preAlarm;
        } else {
            List<PreAlarm> same = preAlarms.stream()
                    .filter(item -> (item.getObjectId().equals(preAlarmDTO.getObjectId()) && item.getObjectTypeId().equals(preAlarmDTO.getObjectTypeId()) && item.getPreAlarmPointId().equals(preAlarmDTO.getPreAlarmPointId()) && item.getEndTime() == null))
                    .toList();
            if (same.size() != 0) {
                for (PreAlarm item : same) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(preAlarmDTO.getStartTime());
                    calendar.set(Calendar.SECOND, -1);
                    item.setEndTime(calendar.getTime());
                    preAlarmMapper.updateById(item);
                    addToCache(item);
                }
            }
            return null;
        }

    }

    private void addNewEquipCapacityPrealarmToQueue(PreAlarm capacityPrealarm, PreAlarmPoint preAlarmPoint) {

        try {
            if(!haStatusService.isMasterHost()) {
                log.info("addNewEquipCapacityPrealarmToQueue : HAStatus is BACKUP.");
                return;
            }
            if(capacityPrealarm != null && capacityPrealarm.getPreAlarmCategory() != null
                    && capacityPrealarm.getObjectTypeId() != null
                    && capacityPrealarm.getPreAlarmCategory() == 3  //容量预警
                    && capacityPrealarm.getObjectTypeId() == 7) { //只取设备的容量预警
                try {
                    preAlarmMapper.insertPreAlarmReportRecord(capacityPrealarm);
                } catch (Exception ex) {
                    log.error("init insertPreAlarmReportRecord throw Exception:", ex);
                }
                newCapacityPrealarmQueue.add(new Object[] { capacityPrealarm, preAlarmPoint });
            }
        } catch (Exception e) {
            log.error(" addNewEquipCapacityPrealarmToQueue throw Exception:", e);
        }
    }

    public void createSelfNoticePreAlarm(PreAlarmPoint preAlarmPoint){
        PreAlarmSeverity preAlarmSeverity = new PreAlarmSeverity();
        preAlarmSeverity.setPreAlarmSeverityId(4);
        preAlarmSeverity.setPreAlarmSeverityName("四级预警");
        preAlarmSeverity.setColor("#F8FF0F");

        PreAlarm preAlarm = new PreAlarm();
        preAlarm.setColor(preAlarmSeverity.getColor());
        preAlarm.setPreAlarmSeverity(preAlarmSeverity.getPreAlarmSeverityId());
        preAlarm.setPreAlarmSeverityName(preAlarmSeverity.getPreAlarmSeverityName());
        preAlarm.setLevelOfPathName(preAlarmPoint.getLevelOfPathName());
        preAlarm.setLevelOfPath(preAlarmPoint.getLevelOfPath());
        preAlarm.setObjectId(preAlarmPoint.getObjectId());
        preAlarm.setObjectTypeId(preAlarmPoint.getObjectTypeId());
        preAlarm.setObjectName(preAlarmPoint.getObjectName());
        preAlarm.setResourceStructureId(preAlarmPoint.getResourceStructureId());
        preAlarm.setPreAlarmCategory(-1);
        preAlarm.setPreAlarmCategoryName("自诊断预警");
        preAlarm.setMeanings("预警配置错误通知");
        preAlarm.setTriggerValue("预警配置名称为“" + preAlarmPoint.getPreAlarmPointName() + "”，配置ID为" + preAlarmPoint.getPreAlarmPointId() + "的配置中表达式采用的指标已被删除，请检查!");
        preAlarm.setSampleTime(new Date());
        preAlarm.setStartTime(new Date());
        preAlarm.setPreAlarmPointId(preAlarmPoint.getPreAlarmPointId());
        preAlarmMapper.insert(preAlarm);
        addToCache(preAlarm);
        preAlarmNotifyService.addPreAlarmMsg(preAlarm);
    }
    @Override
    public void finishPreAlarmByPointId(Integer preAlarmPointId){
        List<PreAlarm> existPreAlarm = preAlarms.stream().filter(item -> item.getPreAlarmPointId().equals(preAlarmPointId)).toList();
        if(existPreAlarm.size() > 0){
            existPreAlarm.forEach(item -> {
                item.setEndTime(new Date());
                if(item.getConfirmTime() == null)
                    item.setConfirmTime(new Date());
                if(item.getConfirmId() == null)
                    item.setConfirmId(-999);
                if(item.getConfirmName() == null)
                    item.setConfirmName("System");
                item.setRemark("该预警配置已被删除，其产生的预警,系统自动结束并确认！");
                preAlarmMapper.updateById(item);
                addToCache(item);
            });
        }
    }

    @Override
    public ConcurrentLinkedQueue<Object[]> getNewEquipCapacityPrealarmQueue() {
        return newCapacityPrealarmQueue;
    }

    @Override
    public void setPdV2EnableValue(boolean pdV2EnableValue) {
        this.pdV2Enable = pdV2EnableValue;
    }
}

