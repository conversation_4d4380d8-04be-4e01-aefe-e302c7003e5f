package com.siteweb.prealarm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceObjectManager;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.service.ResourceService;
import com.siteweb.prealarm.dto.PreAlarmPointComparator;
import com.siteweb.prealarm.dto.*;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.prealarm.entity.EnergyElecFeeConfigOperateLog;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.siteweb.prealarm.function.ExpressionParserFunction;
import com.siteweb.prealarm.mapper.PreAlarmPointOperationLogMapper;
import com.siteweb.prealarm.service.PreAlarmPointService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.utility.service.HAStatusService;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.prealarm.mapper.PreAlarmPointMapper;


@Service("preAlarmPointService")
@EnableScheduling
@Slf4j
public class PreAlarmPointServiceImpl implements PreAlarmPointService , ApplicationListener<BaseSpringEvent<HAStatusChanged>> {

    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private AccountService accountService;
    @Autowired
    private ResourceObjectManager resourceObjectManager;
    @Autowired
    private ResourceService resourceService;

    @Autowired
    private PreAlarmPointMapper preAlarmPointMapper;

    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private HAStatusService haStatusService;

    private SimpleDateFormat dateFormat;
    private List<PreAlarmPoint> alarmPoints;
    private Map<Integer, PreAlarmPoint> mapPreAlarmPointIdMap;
    private Map<String, List<PreAlarmPoint>> mapResourceObjectCache;
    private Boolean needResetSchedule;
    private final List<PreAlarmPoint> needSaveToDB = new ArrayList<>();
    private final List<PreAlarmPoint> needUpdateToDB = new ArrayList<>();
    @Autowired
    private EquipmentMapper equipmentMapper;
    @Autowired
    private PreAlarmPointOperationLogMapper preAlarmPointOperationLogMapper;

    public PreAlarmPointServiceImpl() {
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        this.alarmPoints = new ArrayList<>();
        this.mapPreAlarmPointIdMap = new HashMap<>();
        this.mapResourceObjectCache = new HashMap<>();
        needResetSchedule = false;

    }
    /**
     * 发生双机切换，重新初始化缓存
     * @param haStatusChangedEvent 双机切换事件
     */
    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<HAStatusChanged> haStatusChangedEvent) {
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = haStatusChangedEvent.getData();
        log.info("preAlarmPoint Data Init, receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        init();
    }

    private void sortList(List<PreAlarmPoint> source, String orderByKey, String orderByDirection) {
        PreAlarmPointComparator comparatorTask = new PreAlarmPointComparator(orderByKey, orderByDirection);
        source.sort(comparatorTask);
    }

    private Page<PreAlarmPoint> buildPageList(List<PreAlarmPoint> source, Integer number, Integer size) {
        Pageable pageable = PageRequest.of(number - 1, size);
        List<PreAlarmPoint> records = source.stream()
                .skip((long) (number - 1) * size)
                .limit(size)
                .toList();
        return new PageImpl<>(records, pageable, source.size());
    }

    /**
     * 获取当前时间字符串
     *
     * @return
     */
    @NotNull
    private Date getNow() {
        String now = this.dateFormat.format(new Date(System.currentTimeMillis()));
        try {
            return this.dateFormat.parse(now);
        } catch (Exception exception) {

        }
        return new Date(System.currentTimeMillis());
    }

    @PostConstruct
    public void init() {
        this.alarmPoints = preAlarmPointMapper.selectList(null);
        for (PreAlarmPoint preAlarmPoint : alarmPoints) {
            this.addToCache(preAlarmPoint);
        }
    }


    private void addToCache(PreAlarmPoint inputPreAlarmPoint) {
        needResetSchedule = true;
        List<PreAlarmPoint> findFromAlarmPoints = alarmPoints.stream().filter(
                        item -> (item.getPreAlarmPointId().equals(inputPreAlarmPoint.getPreAlarmPointId())))
                .collect(Collectors.toList());
        if (findFromAlarmPoints.size() == 0) {
            alarmPoints.add(inputPreAlarmPoint);
        } else {
            findFromAlarmPoints.get(0).clone(inputPreAlarmPoint);
        }

        if (mapPreAlarmPointIdMap.get(inputPreAlarmPoint.getPreAlarmPointId()) == null) {
            mapPreAlarmPointIdMap.put(inputPreAlarmPoint.getPreAlarmPointId(), inputPreAlarmPoint);
        } else {
            mapPreAlarmPointIdMap.get(inputPreAlarmPoint.getPreAlarmPointId()).clone(inputPreAlarmPoint);
        }


        List<PreAlarmPoint> existPreAlarmPointList = this.mapResourceObjectCache.get(inputPreAlarmPoint.getObjectId() + "." + inputPreAlarmPoint.getObjectTypeId());
        if (existPreAlarmPointList == null) {
            this.mapResourceObjectCache.put(inputPreAlarmPoint.getObjectId() + "." + inputPreAlarmPoint.getObjectTypeId(), new ArrayList<PreAlarmPoint>());
            this.mapResourceObjectCache.get(inputPreAlarmPoint.getObjectId() + "." + inputPreAlarmPoint.getObjectTypeId()).add(inputPreAlarmPoint);
        } else {
            List<PreAlarmPoint> findFromGlobalResourceCatch = existPreAlarmPointList.stream().filter(
                            item -> (item.getPreAlarmPointId().equals(inputPreAlarmPoint.getPreAlarmPointId())))
                    .collect(Collectors.toList());
            if (findFromGlobalResourceCatch.size() == 0) {
                this.mapResourceObjectCache.get(inputPreAlarmPoint.getObjectId() + "." + inputPreAlarmPoint.getObjectTypeId()).add(inputPreAlarmPoint);
            } else {
                findFromGlobalResourceCatch.get(0).clone(inputPreAlarmPoint);
            }
        }

    }

    private void deleteFromCache(PreAlarmPoint inputPreAlarmPoint) {
        needResetSchedule = true;
        for (PreAlarmPoint point : alarmPoints) {
            if (point.getPreAlarmPointId().equals(inputPreAlarmPoint.getPreAlarmPointId())) {
                alarmPoints.remove(point);
                break;
            }
        }
        List<PreAlarmPoint> preAlarmPoints = mapResourceObjectCache.get(inputPreAlarmPoint.getObjectId() + "." + inputPreAlarmPoint.getObjectTypeId());
        for (PreAlarmPoint alarmPoint : preAlarmPoints) {
            if (alarmPoint.getPreAlarmPointId().equals(inputPreAlarmPoint.getPreAlarmPointId())) {
                preAlarmPoints.remove(alarmPoint);
                break;
            }
        }
        mapPreAlarmPointIdMap.remove(inputPreAlarmPoint.getPreAlarmPointId());
    }

    @Override
    public PreAlarmPoint findPreAlarmPoint(Integer preAlarmPointId) {
        return mapPreAlarmPointIdMap.get(preAlarmPointId);
    }


    @Override
    public List<PreAlarmPoint> findPreAlarmPoints(Integer objectId, Integer objectTypeId) {
        String params = objectId + "." + objectTypeId;
        List<PreAlarmPoint> result= mapResourceObjectCache.get(params);
        return  result == null ? new ArrayList<>() : result;
    }

    @Override
    public Page<PreAlarmPoint> findPreAlarmPoints(PreAlarmPointFilterRequest filterRequest) {
        //权限
        if (filterRequest.getUserId() == null) filterRequest.setUserId(-1);
        List<ResourceObjectEntity> userCanAccessObjects = resourceObjectManager.findAllResourceByUserId(filterRequest.getUserId());
        String userCanAccessObjectsFilter = "";
        for (ResourceObjectEntity item : userCanAccessObjects) {
            userCanAccessObjectsFilter += item.getObjectId() + "." + item.getObjectTypeId() + ",";
        }
        userCanAccessObjectsFilter = userCanAccessObjectsFilter.substring(0, userCanAccessObjectsFilter.length() - 1);
        AccountDTO account = accountService.findByUserId(filterRequest.getUserId());
        if (account == null)
            return buildPageList(new ArrayList<>(), filterRequest.getNumber(), filterRequest.getSize());
        //根据字段 数据 条件 过滤容量列表
        List<PreAlarmPoint> resourceObjectResultByUser = this.getFilterResult("resourceObjects", alarmPoints, userCanAccessObjectsFilter);
        List<PreAlarmPoint> resourceObjectResult = this.getFilterResult("resourceObjects", resourceObjectResultByUser, filterRequest.getResourceObjects());
        List<PreAlarmPoint> resourceStructureIdsResult = this.getFilterResult("resourceStructureIds", resourceObjectResult, filterRequest.getResourceStructureIds());
        List<PreAlarmPoint> displayModeResult = this.getFilterResult("displayMode", resourceStructureIdsResult, String.valueOf(filterRequest.getDisplayMode()));
        if (StringUtils.isNotEmpty(filterRequest.getOrderByKey())) {
            sortList(displayModeResult, filterRequest.getOrderByKey(), filterRequest.getOrderByDirection());
        }
        //构建分页数据
        return buildPageList(displayModeResult, filterRequest.getNumber(), filterRequest.getSize());
    }

    /**
     * 过滤容量熟悉
     *
     * @param columnName     过滤字段
     * @param preAlarmPoints 容量列表
     * @param filterString   过滤条件
     * @return {@link List}<{@link PreAlarmPoint}>
     */
    private List<PreAlarmPoint> getFilterResult(String columnName, List<PreAlarmPoint> preAlarmPoints, String filterString) {
        //如果时空字符串，就把源头返回回去
        if (StrUtil.isBlank(filterString)) return preAlarmPoints;
        //如果过滤字符串不为空，查找
        List<PreAlarmPoint> result = new ArrayList<>();
        //过滤条件根据逗号分割
        Set<String> uniqueFilterList = new HashSet<>(StrUtil.split(filterString, ','));
        for (String filter : uniqueFilterList) {
            if (StrUtil.isBlank(filter)) continue;
            switch (columnName) {
                case "resourceObjects" -> result.addAll(preAlarmPoints.stream()
                        .filter(item -> {
                            String[] resourceObjectsSet = filter.split("\\.");
                            return (item.getObjectId().equals(Integer.parseInt(resourceObjectsSet[0])) && item.getObjectTypeId().equals(Integer.parseInt(resourceObjectsSet[1])));
                        })
                        .toList());
                case "resourceStructureIds" -> result.addAll(preAlarmPoints.stream()
                        .filter(item -> (item.getResourceStructureId().equals(Integer.parseInt(filter)) || item.getLevelOfPath().contains(filter)))
                        .toList());
                case "displayMode" -> result.addAll(preAlarmPoints.stream()
                        .filter(item -> {
                            if (filter.equals(String.valueOf(PreAlarmConst.MASK_TYPE_TIME_SECTION))) {
                                return (ObjectUtil.isNotNull(item.getMaskType()) && item.getMaskType() > 0);
                            } else if (filter.equals(String.valueOf(PreAlarmConst.MASK_TYPE_DURATION))) {
                                return (ObjectUtil.isNull(item.getMaskType()) || item.getMaskType() == 0);
                            }
                            return true;
                        })
                        .toList());

            }
        }
        return result  == null ? new ArrayList<>() : result;
    }

    @Override
    public List<PreAlarmPoint> findPreAlarmPoints(Integer objectId, Integer objectTypeId, Integer categoryId) {
        List<PreAlarmPoint> preAlarmPoints = mapResourceObjectCache.get(objectId + "." + objectTypeId);
        if(preAlarmPoints==null) return new ArrayList<>();
        List<PreAlarmPoint> result = preAlarmPoints.stream().filter(item -> (item.getPreAlarmCategory().equals(categoryId)))
                .collect(Collectors.toList());
        return result == null ? new ArrayList<>() : result;
    }

    @Override
    public List<PreAlarmPoint> findMaskPreAlarmPoints() {
        QueryWrapper<PreAlarmPoint> wrapper = new QueryWrapper<>();
        wrapper.gt("maskType", 0);
        List<PreAlarmPoint> preAlarmPoints = preAlarmPointMapper.selectList(wrapper);
        return preAlarmPoints  == null ? new ArrayList<>() : preAlarmPoints;
    }

    @Override
    public Boolean getResetScheduleFlag() {
        return this.needResetSchedule;
    }

    @Override
    public void setResetScheduleFlag(Boolean value) {
        this.needResetSchedule = value;
    }


    @Override
    public PreAlarmPoint deletePreAlarmPoint(Integer preAlarmPointId) {
        PreAlarmPoint cache = findPreAlarmPoint(preAlarmPointId);
        if (cache != null) {
            preAlarmPointMapper.deleteById(preAlarmPointId);
            deleteFromCache(cache);
            return cache;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PreAlarmPoint> batchCreatePreAlarmPoints(@NotNull PreAlarmRulePoint preAlarmRulePoint) {

        if (preAlarmRulePoint.getEnable()==null){
            preAlarmRulePoint.setEnable(2);
        }
        if (preAlarmRulePoint.getModifier() != null) {
            AccountDTO account = accountService.findByUserId(preAlarmRulePoint.getModifier());
            if (account != null) {
                preAlarmRulePoint.setModifierName(account.getUserName());
                preAlarmRulePoint.setModifyTime(new Date());
            }
        }
        if (preAlarmRulePoint.getConfigType() == null) {
            preAlarmRulePoint.setConfigType(0);
        }
        //设备超期服役预警配置特殊处理
        if (preAlarmRulePoint.getPreAlarmCategory().equals(10)) {
            preAlarmRulePoint.setBatchType(PreAlarmConst.BATCH_CURRENT_GRADE);
            List<Integer> selectedEquips = preAlarmRulePoint.getSelectEquipmentIds() == null ? new ArrayList<>() : preAlarmRulePoint.getSelectEquipmentIds();
            selectedEquips.remove(preAlarmRulePoint.getObjectId());
            StringBuilder equipmentIds = new StringBuilder();
            equipmentIds.append(preAlarmRulePoint.getObjectId());
            for (Integer equipmentId : selectedEquips) {
                equipmentIds.append(",");
                equipmentIds.append(equipmentId);
                //先查询是否存在设备超期预警
                List<PreAlarmPoint> savedPreAlarmPointList = mapResourceObjectCache.get(equipmentId + "." + SourceType.EQUIPMENT.value());
                if(savedPreAlarmPointList != null && !savedPreAlarmPointList.isEmpty()) {
                    PreAlarmPoint savedPreAlarmPoint = savedPreAlarmPointList.stream().filter(o -> o.getPreAlarmCategory().equals(10) && o.getPreAlarmSeverity() == preAlarmRulePoint.getPreAlarmSeverity()).findFirst().orElse(null);
                    if (savedPreAlarmPoint != null) {
                        savedPreAlarmPoint.setExpression(preAlarmRulePoint.getExpression());
                        savedPreAlarmPoint.setAbnormalExpression(preAlarmRulePoint.getAbnormalExpression());
                        savedPreAlarmPoint.setExecuteCron(preAlarmRulePoint.getExecuteCron());
                        savedPreAlarmPoint.setEnable(preAlarmRulePoint.getEnable());
                        savedPreAlarmPoint.setUnit(preAlarmRulePoint.getUnit());
                        savedPreAlarmPoint.setMaskDuration(preAlarmRulePoint.getMaskDuration());
                        savedPreAlarmPoint.setMaskType(preAlarmRulePoint.getMaskType());
                        savedPreAlarmPoint.setMaskStartTime(preAlarmRulePoint.getMaskStartTime());
                        savedPreAlarmPoint.setMaskEndTime(preAlarmRulePoint.getMaskEndTime());
                        savedPreAlarmPoint.setModifier(preAlarmRulePoint.getModifier());
                        savedPreAlarmPoint.setModifyTime(preAlarmRulePoint.getModifyTime());
                        savedPreAlarmPoint.setModifierName(preAlarmRulePoint.getModifierName());
                        needUpdateToDB.add(savedPreAlarmPoint);
                    }
                    else {
                        createAndAddPreAlarmPoint(equipmentId,preAlarmRulePoint,needSaveToDB);
                    }
                }else {
                    createAndAddPreAlarmPoint(equipmentId,preAlarmRulePoint,needSaveToDB);
                }
            }
            //批量插入预警配置
            if (!needSaveToDB.isEmpty()) {
                preAlarmPointMapper.batchInsert(needSaveToDB);
                for (PreAlarmPoint onePoint : needSaveToDB) {
                    addToCache(onePoint);
                }
            }
            //批量更新预警配置
            if (!needUpdateToDB.isEmpty()) {
                preAlarmPointMapper.batchUpdate(needUpdateToDB);
                for (PreAlarmPoint onePoint : needUpdateToDB) {
                    addToCache(onePoint);
                }
            }
            needUpdateToDB.clear();
            needSaveToDB.clear();
            //批量更新设备的服役时间
            selectedEquips.add(preAlarmRulePoint.getObjectId());
            String expressionStr = preAlarmRulePoint.getExpression();
            if (expressionStr != null && !expressionStr.isEmpty()) {
                String[] expressionParams = expressionStr.split("\\|");
                if (expressionParams.length == 3) {
                    //启用时间
                    String useDateStr = expressionParams[0];
                    //使用年限
                    Double useLimit = Double.valueOf(expressionParams[1] == null ? "0" : expressionParams[1]);
                    Date useDate = DateUtil.stringToDate(useDateStr);
                    equipmentMapper.batchUpdateEquipmentUseDate(useLimit, useDate, selectedEquips);
                    equipmentManager.refreshEquipmentByIds(selectedEquips);

                    //添加设备使用时间更新操作日志
                    EnergyElecFeeConfigOperateLog operateLog = new EnergyElecFeeConfigOperateLog();
                    operateLog.setOperatorId(TokenUserUtil.getLoginUserId());
                    operateLog.setOperator(TokenUserUtil.getLoginUserName());
                    operateLog.setUpdateDate(new Date());
                    operateLog.setOperationContent("PreAlarmPoint Update Equipment UsedDate And UsedLimit");
                    String changeContent = "Set Equipment usedDate = " + useDateStr + ",usedLimit = " + useLimit + ",Changed EquipmentIds [" + equipmentIds + "]";
                    operateLog.setChangeContent(changeContent);
                    operateLog.setExtendField1("preAlarmPoint");
                    preAlarmPointOperationLogMapper.insert(operateLog);

                } else {
                    log.error(" BatchCreatePreAlarmPoints BatchUpdate Equipment UseDate Error expressionParams Length Is Not 3");
                }
            }
        }

        List<PreAlarmPoint> preAlarmPoints;
        switch (preAlarmRulePoint.getBatchType()) {
            case PreAlarmConst.BATCH_SAME_GRADE:
                preAlarmPoints = sameGrade(preAlarmRulePoint);
                break;
            case PreAlarmConst.BATCH_SUB_GRADE:
                preAlarmPoints = subGrade(preAlarmRulePoint);
                break;
            default:
                preAlarmPoints = currentGrade(preAlarmRulePoint);
                break;
        }
        return preAlarmPoints;
    }
    private void createAndAddPreAlarmPoint(Integer equipmentId, PreAlarmRulePoint preAlarmRulePoint, List<PreAlarmPoint> needSaveToDB) {
        Equipment equipmentInfo = getPreAlarmPointEquipmentInfo(equipmentId);
        if (equipmentInfo == null) {
            return;
        }

        ResourceStructure resourceStructureInfo = resourceStructureManager.getResourceStructureById(equipmentInfo.getResourceStructureId());
        if (resourceStructureInfo == null) {
            return;
        }

        PreAlarmPoint preAlarmPoint = PreAlarmPoint.transformFrom(preAlarmRulePoint);
        preAlarmPoint.setPreAlarmPointName(equipmentInfo.getEquipmentName() + "超期服役预警");
        preAlarmPoint.setMeanings(equipmentInfo.getEquipmentName() + "超期服役预警");
        preAlarmPoint.setLevelOfPath(resourceStructureInfo.getLevelOfPath());
        preAlarmPoint.setLevelOfPathName(resourceStructureManager.getLevelOfPathName(resourceStructureInfo.getLevelOfPath()));
        preAlarmPoint.setObjectId(equipmentInfo.getEquipmentId());
        preAlarmPoint.setObjectName(equipmentInfo.getEquipmentName());
        preAlarmPoint.setResourceStructureId(equipmentInfo.getResourceStructureId());

        needSaveToDB.add(preAlarmPoint);
    }

    @Override
    public Equipment getPreAlarmPointEquipmentInfo(Integer equipmentId) {
        return equipmentManager.getEquipmentById(equipmentId);
    }
    @Override
    public List<PreAlarmPoint> maskPreAlarmPoints(MaskPreAlarmPoint maskPreAlarmPoint) {
        if (maskPreAlarmPoint.getPreAlarmPointIds() == null ||
                maskPreAlarmPoint.getPreAlarmPointIds().size() == 0 ||
                maskPreAlarmPoint.getPersonId() == null) return new ArrayList<>();
        List<PreAlarmPoint> result = new ArrayList<>();
        AccountDTO account = accountService.findByUserId(maskPreAlarmPoint.getPersonId());
        if (account == null) return new ArrayList<>();
        for (Integer item : maskPreAlarmPoint.getPreAlarmPointIds()) {
            PreAlarmPoint preAlarmPoint = mapPreAlarmPointIdMap.get(item);
            if (maskPreAlarmPoint.getSaveMode() == PreAlarmConst.SAVE_MODE_MASK) {
                preAlarmPoint.setMaskType(PreAlarmConst.MASK_TYPE_TIME_SECTION);
                preAlarmPoint.setMaskStartTime(maskPreAlarmPoint.getMaskStartTime());
                preAlarmPoint.setMaskEndTime(maskPreAlarmPoint.getMaskEndTime());
            } else {
                preAlarmPoint.setMaskType(0);
                preAlarmPoint.setMaskStartTime(null);
                preAlarmPoint.setMaskEndTime(null);
            }
            preAlarmPoint.setModifier(maskPreAlarmPoint.getPersonId());
            preAlarmPoint.setModifierName(account.getUserName());
            preAlarmPoint.setModifyTime(new Date());
            savePreAlarmPoint(preAlarmPoint);
            result.add(preAlarmPoint);
            addToCache(preAlarmPoint);

        }
        return result;
    }

    private void fillStructureInfo(PreAlarmPoint preAlarmPoint) {
        //查找资源的层级节点地址
        ResourceStructure globalResourceNode = resourceStructureService.findById(preAlarmPoint.getResourceStructureId());
        if (globalResourceNode != null) {
            preAlarmPoint.setResourceStructureId(globalResourceNode.getResourceStructureId());
            if(preAlarmPoint.getObjectTypeId() == 7){
                Equipment equipment = equipmentManager.getEquipmentById(preAlarmPoint.getObjectId());
                if(equipment != null)
                    preAlarmPoint.setObjectName(equipment.getEquipmentName());
            }else{
                preAlarmPoint.setObjectName(globalResourceNode.getResourceStructureName());
            }
            preAlarmPoint.setLevelOfPath(globalResourceNode.getLevelOfPath());
            String levelOfPathName = resourceStructureManager.getLevelOfPathName(globalResourceNode.getLevelOfPath());
            //如果是IT设备的话，路径就把机架的名字加在房间的路径后边。
            if (globalResourceNode.getStructureTypeId() == PreAlarmConst.OBJECT_TYPE_IT_DEVICE) {
                ResourceStructure parentNode = resourceStructureService.findById(globalResourceNode.getParentResourceStructureId());
                levelOfPathName = levelOfPathName + "_" + parentNode.getResourceStructureName();
            }
            preAlarmPoint.setLevelOfPathName(levelOfPathName);
            preAlarmPoint.setLevelOfPath(globalResourceNode.getLevelOfPath());
        }
    }


    private List<PreAlarmPoint> currentGrade(PreAlarmRulePoint preAlarmRulePoint) {
        PreAlarmPoint preAlarmPoint = PreAlarmPoint.transformFrom(preAlarmRulePoint);
        fillStructureInfo(preAlarmPoint);
        savePreAlarmPoint(preAlarmPoint);
        addToCache(preAlarmPoint);
        List<PreAlarmPoint> result = new ArrayList<>();
        result.add(preAlarmPoint);
        return result;
    }

    private PreAlarmPoint findSavedPreAlarmPoint(PreAlarmPoint compareAlarmPoint, Integer objectId, Integer objectTypeId, Integer configType) {
        PreAlarmPoint result = new PreAlarmPoint();
        //查找是否有对应的告警规则点
        List<PreAlarmPoint> cachedPreAlarmPointList = mapResourceObjectCache.get(objectId + "." + objectTypeId);
        if (cachedPreAlarmPointList != null) {
            PreAlarmPoint originPreAlarmPoint = null;
            if (compareAlarmPoint.getPreAlarmPointId() != null) {
                originPreAlarmPoint = mapPreAlarmPointIdMap.get(compareAlarmPoint.getPreAlarmPointId());
            }

            Integer preAlarmCategory = originPreAlarmPoint != null ? originPreAlarmPoint.getPreAlarmCategory() : compareAlarmPoint.getPreAlarmCategory();
            int preAlarmSeverity = originPreAlarmPoint != null ? originPreAlarmPoint.getPreAlarmSeverity() : compareAlarmPoint.getPreAlarmSeverity();
            String preAlarmPointName = originPreAlarmPoint != null ? originPreAlarmPoint.getPreAlarmPointName() : compareAlarmPoint.getPreAlarmPointName();
            List<PreAlarmPoint> filteredPreAlarmPoints = cachedPreAlarmPointList.stream().filter(
                    item -> (item.getPreAlarmCategory().equals(preAlarmCategory) &&
                            item.getPreAlarmSeverity() == preAlarmSeverity &&
                            item.getPreAlarmPointName().equals(preAlarmPointName))).toList();

            if (filteredPreAlarmPoints.size() > 0) {
                result.clone(filteredPreAlarmPoints.get(0));
                result.fillUpdatePreAlarmPoint(compareAlarmPoint, configType);
                return result;
            }
        }
        result.fillInsertPreAlarmPoint(compareAlarmPoint, configType);
        result.setResourceStructureId(objectId);
        result.setObjectId(objectId);
        result.setObjectTypeId(objectTypeId);
        return result;
    }

    /**
     * 批量同资源类型指标添加
     *
     * @param preAlarmRulePoint
     */
    private List<PreAlarmPoint> sameGrade(PreAlarmRulePoint preAlarmRulePoint) {
        // 根据对象类型id查询出所有的资源

        List<PreAlarmPoint> result = new ArrayList<>();
        Integer objectTypeId = preAlarmRulePoint.getObjectTypeId();
        if (objectTypeId == null)
            return result;
        List<ResourceObjectEntity> sameTypeResource = resourceObjectManager.findEntityByObjectType(objectTypeId);
        List<ResourceObjectEntity> sameGradeResourceNodes ;
        //资源类型为设备的节点
        if (objectTypeId > 6 && objectTypeId < 100) {
            sameGradeResourceNodes = sameTypeResource.stream().filter(o -> o.getResourceStructureId().equals(preAlarmRulePoint.getResourceStructureId())).toList();
        } else {
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(preAlarmRulePoint.getResourceStructureId());
            sameGradeResourceNodes = sameTypeResource.stream().filter((o -> o.getParentResourceStructureId().equals(resourceStructure.getParentResourceStructureId()))).toList();
        }

        PreAlarmPoint rulePoint = PreAlarmPoint.transformFrom(preAlarmRulePoint);
        //循环遍历所有的同级资源
        for (ResourceObjectEntity resourceObject : sameGradeResourceNodes) {
            //如果是传过来的资源预警规则点，则直接保存，否则需要进行修改。
            PreAlarmPoint needSavedPreAlarmPoint = new PreAlarmPoint();
            if (!resourceObject.getResourceStructureId().equals(rulePoint.getResourceStructureId())) {
                //查找是否有对应的告警规则点
                needSavedPreAlarmPoint.clone(findSavedPreAlarmPoint(rulePoint, resourceObject.getObjectId(), resourceObject.getObjectTypeId(), preAlarmRulePoint.getConfigType()));
            } else {
                needSavedPreAlarmPoint.clone(rulePoint);
            }
            fillStructureInfo(needSavedPreAlarmPoint);
            savePreAlarmPoint(needSavedPreAlarmPoint);
            addToCache(needSavedPreAlarmPoint);
            result.add(needSavedPreAlarmPoint);
        }
        return result;

    }

    private void savePreAlarmPoint(PreAlarmPoint preAlarmPoint) {
        if (preAlarmPoint.getPreAlarmPointId() == null) {
            preAlarmPointMapper.insertPreAlarmPont(preAlarmPoint);
        } else {
            preAlarmPointMapper.updateById(preAlarmPoint);
        }
    }

    private List<PreAlarmPoint> subGrade(PreAlarmRulePoint preAlarmRulePoint) {
        List<PreAlarmPoint> preAlarmPoints = new ArrayList<>();
        List<ResourceObjectEntity> children = getChildrenNodeById(preAlarmRulePoint.getObjectId(), preAlarmRulePoint.getObjectTypeId());
        if (children.size() > 0) {
            // 递归
            recursion(children, preAlarmPoints, preAlarmRulePoint);
        }
        List<PreAlarmPoint> result = new ArrayList<>();
        //插入当前点
        PreAlarmPoint needSavedPreAlarmPoint = new PreAlarmPoint();
        needSavedPreAlarmPoint.clone(preAlarmRulePoint);
        fillStructureInfo(needSavedPreAlarmPoint);
        preAlarmPoints.add(needSavedPreAlarmPoint);
        // 处理当前
        for (PreAlarmPoint item : preAlarmPoints) {
            savePreAlarmPoint(item);
            addToCache(item);
            result.add(item);
        }
        return result;
    }

    /**
     * 用递归遍历处理每个节点
     *
     * @param list
     * @param preAlarmPointList
     * @param preAlarmRulePoint
     */
    private void recursion(List<ResourceObjectEntity> list, List<PreAlarmPoint> preAlarmPointList, PreAlarmRulePoint preAlarmRulePoint) {
        // 遍历
        for (ResourceObjectEntity r : list) {
            PreAlarmPoint rulePoint = PreAlarmPoint.transformFrom(preAlarmRulePoint);
            //查找是否有对应的告警规则点
            PreAlarmPoint needSavedPreAlarmPoint = new PreAlarmPoint();
            if (!(r.getObjectId().equals(rulePoint.getObjectId()) && r.getObjectTypeId().equals(rulePoint.getObjectTypeId()))) {
                //查找是否有对应的告警规则点
                needSavedPreAlarmPoint.clone(findSavedPreAlarmPoint(rulePoint, r.getObjectId(), r.getObjectTypeId(), preAlarmRulePoint.getConfigType()));
            } else {
                needSavedPreAlarmPoint.clone(rulePoint);
            }
            fillStructureInfo(needSavedPreAlarmPoint);
            preAlarmPointList.add(needSavedPreAlarmPoint);
            List<ResourceObjectEntity> children = getChildrenNodeById(r.getObjectId(), r.getObjectTypeId());
            // 判断是否还有子节点
            if (children.size() == 0) {
                continue;
            }
            // 继续递归
            recursion(children, preAlarmPointList, preAlarmRulePoint);
        }
    }

    @Override
    public Map<String, List<PreAlarmPoint>> getPreAlarmPointsExecuteGroup() {
        Map<String, List<PreAlarmPoint>> map = new HashMap<>();
        for (PreAlarmPoint alarmPoint : alarmPoints) {
            if (StringUtils.isNotEmpty(alarmPoint.getExpression())) {
                if (map.containsKey(alarmPoint.getExecuteCron())) {
                    List<PreAlarmPoint> filteredPreAlarmPoints = map.get(alarmPoint.getExecuteCron()).stream().filter(
                                    item -> (item.getPreAlarmPointId().equals(alarmPoint.getPreAlarmPointId())))
                            .collect(Collectors.toList());
                    if (filteredPreAlarmPoints.size() == 0)
                        map.get(alarmPoint.getExecuteCron()).add(alarmPoint);
                } else {
                    map.put(alarmPoint.getExecuteCron(), new ArrayList<>());
                    map.get(alarmPoint.getExecuteCron()).add(alarmPoint);
                }
            }
        }
        return map;
    }

    @Override
    public Boolean checkExpression(String expression) {
        if (expression.contains(">") || expression.contains("<") || expression.contains("=")) {
            // 转换表达式
            String evalExpression = ExpressionParserFunction.parseExpression(expression);
            try {
                ExpressionParserFunction.eval(evalExpression);
                return true;
            } catch (Exception ex) {
                return false;
            }
        }
        return false;
    }

    private List<ResourceObjectEntity> getChildrenNodeById(Integer objectId, Integer objectTypeId) {
        List<ResourceObjectEntity> allList = resourceService.findResourceAll();
        List<ResourceObjectEntity> resList = new ArrayList<>();
        for (ResourceObjectEntity item : allList) {
            if (item.getParentResourceStructureId().equals(objectId) && item.getParentResourceStructureTypeId().equals(objectTypeId)) {
                resList.add(item);
            }
        }
        return resList;
    }


}
