
package com.siteweb.prealarm.service;



import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmHistory;

import java.util.Date;
import java.util.List;


public interface PreAlarmHistoryService {
    /*
     *根据预警编号获取历史预警信息
     */
    PreAlarmHistory findSameHistoryPreAlarm(Integer preAlarmId, Date startTime, Date endTime);

    /*
     *存储历史预警信息
     */
    PreAlarmHistory save(PreAlarmHistory preAlarmHistory);
    List<PreAlarmHistory> findPreAlarmHistorys(Integer objectId,Integer objectTypeId, Date startTime, Date endTime, Integer preAlarmCategory);

    List<PreAlarm> findAlarmsByPowerDistributionPlayBack(Integer objectId, Integer objectTypeId, Date time);
}