package com.siteweb.prealarm.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.prealarm.mapper.PreAlarmHistoryMapper;
import com.siteweb.prealarm.mapper.PreAlarmSeverityMapper;
import com.siteweb.prealarm.service.PreAlarmHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("preAlarmHistoryService")
@EnableScheduling
public class PreAlarmHistoryServiceImpl implements PreAlarmHistoryService {

    @Autowired
    PreAlarmHistoryMapper preAlarmHistoryMapper;

    @Override
    public PreAlarmHistory findSameHistoryPreAlarm(Integer preAlarmId, Date startTime, Date endTime) {
        QueryWrapper<PreAlarmHistory> wrapper = new QueryWrapper<>();
        wrapper.eq("PreAlarmId", preAlarmId);
        wrapper.eq("StartTime", startTime);
        wrapper.eq("EndTime", endTime);
        return preAlarmHistoryMapper.selectOne(wrapper);
    }

    @Override
    public PreAlarmHistory save(PreAlarmHistory preAlarmHistory) {
        preAlarmHistoryMapper.insert(preAlarmHistory);
        return preAlarmHistory;
    }

    @Override
    public List<PreAlarmHistory> findPreAlarmHistorys(Integer objectId, Integer objectTypeId, Date startTime, Date endTime, Integer preAlarmCategory) {
        QueryWrapper<PreAlarmHistory> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", objectId);
        wrapper.eq("objectTypeId", objectTypeId);
        wrapper.eq("PreAlarmCategory", preAlarmCategory);
        wrapper.between("StartTime", startTime, endTime);
        List<PreAlarmHistory> result =  preAlarmHistoryMapper.selectList(wrapper);
        return result == null ? new ArrayList<>() : result;
    }

    public List<PreAlarm> findAlarmsByPowerDistributionPlayBack(Integer objectId, Integer objectTypeId, Date time) {
        QueryWrapper<PreAlarmHistory> wrapper = new QueryWrapper<>();
        wrapper.eq("objectId", objectId);
        wrapper.eq("objectTypeId", objectTypeId);
        wrapper.le("StartTime", time);
        wrapper.ge("EndTime", time);
        wrapper.eq("PreAlarmCategory", 3);
        List<PreAlarmHistory> result =  preAlarmHistoryMapper.selectList(wrapper);
        List<PreAlarm> res = result.stream().map(item -> {
            PreAlarm preAlarm = new PreAlarm();
            preAlarm.preAlarmHistoryToPreAlarm(item);
            return preAlarm;
        }).collect(Collectors.toList());

        return result == null ? new ArrayList<>() : res;
    }
}
