package com.siteweb.prealarm.service.impl;

import com.siteweb.prealarm.entity.PreAlarmCategory;
import com.siteweb.prealarm.mapper.PreAlarmCategoryMapper;
import com.siteweb.prealarm.service.PreAlarmCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("preAlarmCategoryService")
@EnableScheduling
public class PreAlarmCategoryServiceImpl implements PreAlarmCategoryService {

    @Autowired
    private PreAlarmCategoryMapper preAlarmCategoryMapper;

    @Override
    public PreAlarmCategory findByCategoryId(Integer categoryId) {
        return preAlarmCategoryMapper.selectById(categoryId);
    }

    @Override
    public List<PreAlarmCategory> findAll() {

        List<PreAlarmCategory> result= preAlarmCategoryMapper.selectList(null);
        return result == null ? new ArrayList<>() : result;
    }

    @Override
    public PreAlarmCategory updatePreAlarmCategory(PreAlarmCategory preAlarmCategory) {
        if (preAlarmCategory.getCategoryId() != null){
            preAlarmCategoryMapper.updateById(preAlarmCategory);
        }else{
            preAlarmCategoryMapper.insert(preAlarmCategory);
        }
        return preAlarmCategory;
    }

    @Override
    public PreAlarmCategory deleteByPreAlarmCategoryId(Integer preAlarmCategoryId) {
        PreAlarmCategory res = preAlarmCategoryMapper.selectById(preAlarmCategoryId);
        preAlarmCategoryMapper.deleteById(preAlarmCategoryId);
        return res;
    }
}
