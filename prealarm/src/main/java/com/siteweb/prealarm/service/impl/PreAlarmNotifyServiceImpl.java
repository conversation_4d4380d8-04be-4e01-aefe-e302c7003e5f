package com.siteweb.prealarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;
import com.siteweb.eventnotification.enums.TtsConfigEnum;
import com.siteweb.eventnotification.service.TtsConfigService;
import com.siteweb.eventnotification.textnotify.AbstractTextNotifyService;
import com.siteweb.eventnotification.textnotify.NotifyTypeEnum;
import com.siteweb.eventnotification.textnotify.TextNotifyDTO;
import com.siteweb.prealarm.dto.AlarmTextNotifyDTO;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import com.siteweb.prealarm.service.PreAlarmNotifyService;
import com.siteweb.utility.mapper.SystemConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
@Slf4j
@Service
public class PreAlarmNotifyServiceImpl extends AbstractTextNotifyService<PreAlarm> implements PreAlarmNotifyService {

    @Autowired
    private RegionMapService regionMapService;
    @Autowired
    private RegionService regionService;
    @Autowired
    private PreAlarmMapper preAlarmMapper;
    @Autowired
    TtsConfigService ttsConfigService;

    private List<PreAlarm> allPreAlarm = new ArrayList<>();

    @Override
    public NotifyTypeEnum getNotifyType() {
        return NotifyTypeEnum.PRE_ALARM_TEXT_NOTIFY;
    }

    @Override
    public TextNotifyDTO<AlarmTextNotifyDTO> getCurrentTtsMsg(TtsMessageRequestDTO ttsMessageRequestDTO) {
        try {
            PreAlarm currentUserPreAlarm = getCurrentUserPreAlarm(ttsMessageRequestDTO.getUserId(), ttsMessageRequestDTO.getSessionId(), ttsMessageRequestDTO.getPreAlarmId());

            if (ObjectUtil.isNull(currentUserPreAlarm)) {
                return null;
            }

            AlarmTextNotifyDTO preAlarmNotify = new AlarmTextNotifyDTO();
            preAlarmNotify.setPreAlarmId(currentUserPreAlarm.getPreAlarmId());
            preAlarmNotify.setText("有预警" + currentUserPreAlarm.getMeanings());

            return new TextNotifyDTO<>(getNotifyType(), preAlarmNotify);
        } catch (Exception ex) {
            log.error("getCurrentTtsMsg error ", ex);
            return null;
        }
    }

    @Override
    public void processAfterConnection(Integer userId, String sessionId) {
        firstPushData(userId, sessionId);
    }

    /**
     * 首次连接推送数据
     */
    private void firstPushData(Integer userId, String sessionId) {
        allPreAlarm = preAlarmMapper.selectList(new QueryWrapper<>());

        List<PreAlarm> preAlarmTextNotifyList = allPreAlarm.stream()
                                                           .filter(o -> o.getEndTime() == null)
                                                           .sorted(Comparator.comparing(PreAlarm::getPreAlarmSeverity).thenComparing(PreAlarm::getStartTime))
                                                           .toList();

        batchPushMsg(getUniqueId(userId, sessionId), preAlarmTextNotifyList);
    }

    @Override
    protected List<String> extractMessageTexts(Set<String> messageList) {
        return messageList.stream()
                          .map(message -> {
                              PreAlarm preAlarm = JSONUtil.toBean(JSONUtil.toJsonStr(message), PreAlarm.class);
                              return "有预警" + preAlarm.getMeanings();
                          })
                          .toList();
    }

    @Override
    protected double calculateScore(PreAlarm preAlarm) {
        return preAlarm.getStartTime().getTime();
    }

    @Override
    public boolean continueBroadcast(TtsContinueBroadcastRequestDTO ttsContinueBroadcastRequestDTO) {
        PreAlarm preAlarmDTO = allPreAlarm.stream()
                                          .filter(o -> Objects.equals(o.getPreAlarmId(), ttsContinueBroadcastRequestDTO.getPreAlarmId()))
                                          .findFirst()
                                          .orElse(null);

        if (Objects.isNull(preAlarmDTO)) {
            return false;
        }

        // 已结束不播报
        if (Objects.nonNull(preAlarmDTO.getEndTime())) {
            return false;
        }
        boolean confirmedNoAnnouncement = ttsConfigService.findBooleanValue(TtsConfigEnum.PRE_ALARM_NOTIFY_CONFIRMED_NOTIFY.getTtsConfigKey());
        // 预警已经被确认 并且开启已确认不播报  则无需继续播报
        if (Objects.nonNull(preAlarmDTO.getConfirmTime()) && confirmedNoAnnouncement) {
            return false;
        }

        return true;
    }

    /**
     * 获取当前用户预警
     */
    private PreAlarm getCurrentUserPreAlarm(Integer userId, String sessionId, Integer preAlarmId) {
        String uniqueId = getUniqueId(userId, sessionId);
        String redisKey = getNotifyType().getRedisKeyPrefix() + uniqueId;

        while (true) {
            PreAlarm preAlarm = getFirstMessageFromQueue(redisKey, PreAlarm.class);

            if (preAlarm == null) {
                return null;
            }

            if (isPopMessage(preAlarm, preAlarmId)) {
                redisTemplate.opsForZSet().remove(redisKey, JSONUtil.toJsonStr(preAlarm));
                continue;
            }

            return preAlarm;
        }
    }

    /**
     * 判断是否需要弹出消息
     */
    private boolean isPopMessage(PreAlarm preAlarm, Integer preAlarmId) {
        // 第一次进来
        if (preAlarmId == null) {
            return false;
        }

        // 此预警已被播报
        if (Objects.equals(preAlarmId, preAlarm.getPreAlarmId())) {
            return true;
        }

        // 此预警已经结束且确认
        PreAlarm existPreAlarm = allPreAlarm.stream()
                                            .filter(o -> o.getPreAlarmId().equals(preAlarm.getPreAlarmId()))
                                            .findFirst()
                                            .orElse(null);

        return ObjectUtil.isEmpty(existPreAlarm) || (!ObjectUtil.isEmpty(existPreAlarm.getEndTime()) && !ObjectUtil.isEmpty(existPreAlarm.getConfirmTime()));
    }

    @Override
    public void addPreAlarmMsg(PreAlarm preAlarm) {
        Set<String> lastRequestTimeSet = redisTemplate.opsForZSet()
                                                      .range(GlobalConstants.REQUEST_MESSAGE_TIME, 0, -1);

        if (CollUtil.isEmpty(lastRequestTimeSet)) {
            return;
        }

        for (String lastRequestTime : lastRequestTimeSet) {
            String[] uniqueIdArray = lastRequestTime.split(":");
            int userId = Integer.parseInt(uniqueIdArray[0]);

            // 判断当前登录用户ID及设备权限
            if (!hasRegionMapPermissions(preAlarm, userId)) {
                continue;
            }

            pushMsg(lastRequestTime, preAlarm);
        }
    }

    @Override
    public void refreshPreAlarmCache(List<PreAlarm> preAlarms) {
        allPreAlarm = preAlarms;
    }

    /**
     * 检查区域权限
     */
    public boolean hasRegionMapPermissions(PreAlarm preAlarm, int userId) {
        List<Region> regions = regionService.findAllRegionsByUserId(userId);

        // 如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (regions.stream().anyMatch(o -> o.getRegionId().equals(-1))) {
            return true;
        }

        List<Integer> regionIds = regions.stream()
                                         .map(Region::getRegionId)
                                         .toList();

        List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);

        // EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
        Set<Integer> resourceStructureIds = regionMaps.stream()
                                                      .filter(o -> o.getEquipmentId().equals(-1))
                                                      .map(RegionMap::getResourceStructureId)
                                                      .collect(Collectors.toSet());

        Set<Integer> equipmentIds = regionMaps.stream()
                                              .map(RegionMap::getEquipmentId)
                                              .filter(equipmentId -> equipmentId > 0)
                                              .collect(Collectors.toSet());

        return resourceStructureIds.contains(preAlarm.getResourceStructureId()) || equipmentIds.contains(preAlarm.getObjectId());
    }
}
