
package com.siteweb.prealarm.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.siteweb.prealarm.dto.PreAlarmExpressionRequest;
import com.siteweb.prealarm.dto.PreAlarmPointFilterRequest;
import com.siteweb.prealarm.dto.MaskPreAlarmPoint;
import com.siteweb.prealarm.dto.PreAlarmRulePoint;
import com.siteweb.prealarm.service.PreAlarmPointService;
import com.siteweb.prealarm.service.PreAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "预警配置",tags = "预警配置管理")
public class PreAlarmPointController {

    @Autowired
    private PreAlarmPointService preAlarmPointService;
    @Autowired
    private PreAlarmService preAlarmService;


    /**
     * 检查表达式配置
     *
     * @param
     * @return
     */
    @ApiOperation("检查表达式配置(v1接口地址/prealarmpointcheckexpression)")
    @PostMapping("prealarmpoint/checkexpression")
    public ResponseEntity<ResponseResult> checkExpression(@RequestBody PreAlarmExpressionRequest request) {
        Boolean result= preAlarmPointService.checkExpression(request.getExpression());
        return ResponseHelper.successful(result);
    }
//
//
//
    @ApiOperation("根据id和typeId获取预警配置(v1:/prealarmpoints)")
    @GetMapping(value = "/prealarmpoint/values",
            params = {"objectId","objectTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPreAlarmPointsById(Integer objectId,Integer objectTypeId) {
        List<PreAlarmPoint> result= preAlarmPointService.findPreAlarmPoints(objectId,objectTypeId);
         return ResponseHelper.successful(result);
    }
    @ApiOperation("获取屏蔽类型大于0的预警配置(v1:/prealarmpoints)")
    @GetMapping(value = "/prealarmpoint/masktype",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllMaskPreAlarmPoints() {
        List<PreAlarmPoint> result= preAlarmPointService.findMaskPreAlarmPoints();
        return ResponseHelper.successful(result);
    }
    @ApiOperation("根据预警配置Id获取预警配置(v1:/prealarmpoint)")
    @GetMapping(value = "/prealarmpoint/values",
            params = {"preAlarmPointId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPreAlarmPointByPointId(@RequestParam(value = "preAlarmPointId") Integer preAlarmPointId) {
        PreAlarmPoint result = preAlarmPointService.findPreAlarmPoint(preAlarmPointId);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("获取预警配置分页(v1:/pageprealarmpoints)")
    @PostMapping(value = "/prealarmpoint/page")
        public ResponseEntity<ResponseResult> getPagePreAlarms(@RequestBody PreAlarmPointFilterRequest preAlarmPointFilterRequest){
        Page<PreAlarmPoint> result = preAlarmPointService.findPreAlarmPoints(preAlarmPointFilterRequest);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("更新预警屏蔽(v1:/maskprealarmpoints)")
    @PostMapping(value = "/prealarmpoint/maskinfo")
    public  ResponseEntity<ResponseResult> updatePreAlarmPointsMaskInfo(@RequestBody MaskPreAlarmPoint maskPreAlarmPoint) throws URISyntaxException, JsonProcessingException {
        List<PreAlarmPoint> result = preAlarmPointService.maskPreAlarmPoints(maskPreAlarmPoint);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("保存预警配置(v1:/prealarmpoints)")
    @PostMapping(value = "/prealarmpoint/values")
    public ResponseEntity<ResponseResult> batchCreatePreAlarmPoints(@RequestBody PreAlarmRulePoint preAlarmRulePoint) {
        List<PreAlarmPoint> result = preAlarmPointService.batchCreatePreAlarmPoints(preAlarmRulePoint);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("更新预警配置(v1:/prealarmpoint)")
    @PutMapping(value = "/prealarmpoint/values")
    public ResponseEntity<ResponseResult> batchUpdatePreAlarmPoint(@Valid @RequestBody PreAlarmRulePoint preAlarmRulePoint) {
        if (preAlarmRulePoint == null || preAlarmRulePoint.getPreAlarmPointId() == null) {
            return ResponseHelper.failed("preAlarmPoint Not Found");
        }
        List<PreAlarmPoint> result = preAlarmPointService.batchCreatePreAlarmPoints(preAlarmRulePoint);
        if(result == null){
            return ResponseHelper.failed("preAlarmPoint Not Found");
        }
        return ResponseHelper.successful(result);
    }
    @ApiOperation("删除预警配置(v1:/prealarmpoint)")
    @DeleteMapping(value = "/prealarmpoint/values",
            params = {"preAlarmPointId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deletePreAlarmPoint(@RequestParam(value = "preAlarmPointId") Integer preAlarmPointId) {
        PreAlarmPoint result = preAlarmPointService.deletePreAlarmPoint(preAlarmPointId);
        //结束此配置的预警
        preAlarmService.finishPreAlarmByPointId(preAlarmPointId);
        if(result == null){
            return ResponseHelper.failed("preAlarmPoint Not Found");
        }
        return ResponseHelper.successful(result);
    }
    @ApiOperation("获取设备详细信息")
    @GetMapping(value = "/prealarmpoint/equipmentinfo/{equipmentId}",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPreAlarmPointEquipmentInfo(@PathVariable(value = "equipmentId") @ApiParam(name = "equipmentId", value = "唯一ID", required = true) Integer equipmentId) {
        return ResponseHelper.successful(preAlarmPointService.getPreAlarmPointEquipmentInfo(equipmentId));
    }

}
