package com.siteweb.prealarm.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "预警等级管理",tags = "预警等级管理")
public class PreAlarmSeverityController {
    @Autowired
    private PreAlarmSeverityService preAlarmSeverityService;

    @ApiOperation("获取所有预警等级")
    @GetMapping(value = "/prealarmseveritys",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllPreAlarmSeverity(){
        List<PreAlarmSeverity> result = preAlarmSeverityService.findAllPreAlarmSeverity();
        return ResponseHelper.successful(result);
    }

    @ApiOperation("修改或新增预警等级 取决于是否有id")
    @PutMapping(value = "/prealarmseverity")
    public ResponseEntity<ResponseResult> updatePreAlarmSeverity(@RequestBody PreAlarmSeverity preAlarmSeverity) throws URISyntaxException, JsonProcessingException {
        PreAlarmSeverity result = preAlarmSeverityService.updatePreAlarmSeverity(preAlarmSeverity);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("根据id删除预警等级")
    @DeleteMapping(value = "/prealarmseverity",
            params = {"preAlarmSeverityId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deletePreAlarmSeverity(@RequestParam(value = "preAlarmSeverityId") Integer preAlarmSeverityId) throws URISyntaxException, JsonProcessingException {
        Integer result = preAlarmSeverityService.deleteByPreAlarmSeverityId(preAlarmSeverityId);
        return ResponseHelper.successful(result);
    }
}
