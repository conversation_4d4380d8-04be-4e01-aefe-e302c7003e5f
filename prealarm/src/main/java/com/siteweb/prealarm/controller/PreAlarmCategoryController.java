package com.siteweb.prealarm.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.prealarm.entity.PreAlarmCategory;
import com.siteweb.prealarm.service.PreAlarmCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "预警分类管理", tags = "预警分类管理")
public class PreAlarmCategoryController {
    @Autowired
    private PreAlarmCategoryService preAlarmCategoryService;

    @ApiOperation("获取预警分类")
    @GetMapping(value = "/prealarmcategorys",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllPreAlarmCategory(){
        List<PreAlarmCategory> result = preAlarmCategoryService.findAll();
        return ResponseHelper.successful(result);
    }
    @ApiOperation("更新预警分类")
    @PutMapping(value = "/prealarmcategory")
    public ResponseEntity<ResponseResult> updatePreAlarmCategory(@RequestBody PreAlarmCategory preAlarmCategory) throws URISyntaxException, JsonProcessingException {
        PreAlarmCategory result = preAlarmCategoryService.updatePreAlarmCategory(preAlarmCategory);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("删除预警分类")
    @DeleteMapping(value = "/prealarmcategory")
    public ResponseEntity<ResponseResult> deletePreAlarmCategory(@RequestBody Integer categoryId) throws URISyntaxException, JsonProcessingException {
        PreAlarmCategory result = preAlarmCategoryService.deleteByPreAlarmCategoryId(categoryId);
        return ResponseHelper.successful(result);
    }
}
