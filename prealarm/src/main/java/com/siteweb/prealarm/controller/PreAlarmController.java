package com.siteweb.prealarm.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.prealarm.dto.*;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.service.PreAlarmNotifyService;
import com.siteweb.prealarm.service.PreAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "预警列表", tags = "预警列表")
public class PreAlarmController {

    @Autowired
    private PreAlarmService preAlarmService;
    @Autowired
    private PreAlarmNotifyService preAlarmNotifyService;

    @ApiOperation("获取预警列表(v1:/simpleprealarms)")
    @PostMapping(value = "/prealarms/simple")
    public ResponseEntity<ResponseResult> getTopNPreAlarms(@RequestBody PreAlarmFilterRequest PreAlarmFilterRequest) {
        PreAlarmFilterRequest.setContainBelowLevel(true);
        Page<PreAlarm> res = preAlarmService.findAlarms(PreAlarmFilterRequest);
        return ResponseHelper.successful(res);
    }
    @ApiOperation("根据id和typeid获取预警列表(v1:/prealarms)")
    @GetMapping(value = "/prealarms",
            params = {"objectId", "objectTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getObjectIdPreAlarms(@RequestParam(value = "objectId") Integer objectId, @RequestParam(value = "objectTypeId") Integer objectTypeId) {
        return ResponseHelper.successful(preAlarmService.findAlarms(objectId,objectTypeId));
    }
    @ApiOperation("获取最高等级的预警(v1:/prealarms/maxSeverity)")
    @GetMapping(value = "/prealarms/maxSeverity",
            params = {"preAlarmCategory","uniqueId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMaxSeverityPreAlarmByCategory(@RequestParam(value = "preAlarmCategory") Integer preAlarmCategory,
                                                                     @RequestParam(value = "uniqueId") String uniqueId){
        PreAlarm result = preAlarmService.getMaxSeverityPreAlarmByCategory(preAlarmCategory, uniqueId);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("预警统计(v1:/prealarmcount)")
    @GetMapping(value = "/prealarms/count",
            params = {"preAlarmCategory","objectId","objectTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPreAlarmCount(@RequestParam(value = "preAlarmCategory") Integer preAlarmCategory,
                                                    @RequestParam(value = "objectId") Integer objectId,
                                                    @RequestParam(value = "objectTypeId") Integer objectTypeId){
        Integer result = preAlarmService.contByPreAlarmCategory(preAlarmCategory, objectId,objectTypeId);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("根据预警种类获取列表(v1:/prealarms)")
    @GetMapping(value = "/prealarms",
            params = {"preAlarmCategory","objectId","objectTypeId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPreAlarmSeverityCount(@RequestParam(value = "preAlarmCategory") Integer preAlarmCategory,
                                                                                @RequestParam(value = "objectId") Integer objectId,
                                                                                @RequestParam(value = "objectTypeId") Integer objectTypeId){
        List<PreAlarmSeverityCount> result = preAlarmService.findPreAlarmSeverityCount(preAlarmCategory,objectId,objectTypeId);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("根据预警等级统计(v1:/prealarmstatistics)")
    @GetMapping(value = "/prealarms/tatistics",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> preAlarmStatistics(){
        List<PreAlarmStatisticItem> result = preAlarmService.findPreAlarmStatistics();
        return ResponseHelper.successful(result);
    }
    @ApiOperation("结束预警(v1:/prealarms)")
    @PostMapping(value = "/prealarms/end")
    public ResponseEntity<ResponseResult> endPreAlarms(@RequestBody List<Integer> preAlarmIds) {
        List<PreAlarm> result = preAlarmService.endPreAlarms(preAlarmIds);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("预警列表分页(v1:/pageprealarms)")
    @PostMapping(value = "/prealarms/page")
    public ResponseEntity<ResponseResult> getPagePreAlarms(@RequestBody PreAlarmFilterRequest preAlarmFilterRequest){
        preAlarmFilterRequest.setContainBelowLevel(false);
        Page<PreAlarm> result = preAlarmService.findAlarms(preAlarmFilterRequest);
        return ResponseHelper.successful(result);
    }
    @ApiOperation("更新确认预警(v1:/prealarms)")
    @PutMapping(value = "/prealarms")
    public ResponseEntity<List<PreAlarm>> confirmPreAlarms(@RequestBody List<PreAlarmConfirm> params) throws URISyntaxException, JsonProcessingException {
        List<PreAlarm> result = preAlarmService.confirmPreAlarms(params);
        return ResponseEntity.ok().body(result);
    }
    @ApiOperation("更新预警备注(v1:/prealarms/updateremark)")
    @PutMapping(value = "/prealarms/updateremark")
    public ResponseEntity<List<PreAlarm>> updatePreAlarms(@RequestBody List<PreAlarmConfirm> params) throws URISyntaxException, JsonProcessingException {
        List<PreAlarm> result = preAlarmService.updatePreAlarmsRemark(params);
        return ResponseEntity.ok().body(result);
    }
    @ApiOperation("虚拟预警(v1:virtualprealarm)")
    @PutMapping(value = "/prealarms/virtual")
    public ResponseEntity<ResponseResult> generateVirtualPreAlarm(@RequestBody VirtualPreAlarmParam param) throws URISyntaxException, JsonProcessingException {
        List<PreAlarm> result = preAlarmService.findByPreAlarmCategory(1);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("获取所有预警")
    @GetMapping(value = "/prealarms",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findAllPreAlarm(){
        List<PreAlarm> result = preAlarmService.findAllPreAlarm();
        return ResponseHelper.successful(result);
    }
    //@ApiOperation(value = "获取当前用户应当播报的预警消息")
    //@GetMapping(value = "/prealarm/ttscurrentmsgs",produces = MediaType.APPLICATION_JSON_VALUE)
    //public ResponseEntity<ResponseResult> getUserTtsCurrentMsg(TtsMessageRequestDTO ttsMessageRequestDTO){
    //    return ResponseHelper.successful(preAlarmNotifyService.getCurrentTtsMsg(ttsMessageRequestDTO));
    //}
    //
    //@ApiOperation(value = "是否要继续播报当前预警的tts语音")
    //@GetMapping(value = "/prealarm/ttscontinuebroadcast",produces = MediaType.APPLICATION_JSON_VALUE)
    //public ResponseEntity<ResponseResult> getUserTtsCurrentMsg(String sequenceId){
    //    return ResponseHelper.successful(preAlarmNotifyService.continueBroadcast(sequenceId));
    //}
}
