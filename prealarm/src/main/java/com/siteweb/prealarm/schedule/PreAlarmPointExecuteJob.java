package com.siteweb.prealarm.schedule;

import com.siteweb.common.util.GetBeanUtil;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.siteweb.prealarm.service.PreAlarmPointService;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Slf4j
@Component
public class PreAlarmPointExecuteJob implements Job {

    @Autowired
    private PreAlarmService preAlarmService;

    private static  PreAlarmService staticPreAlarmService;
    @Autowired
    private PreAlarmPointService preAlarmPointService;

    private static  PreAlarmPointService staticPreAlarmPointService;
    private HAStatusService haStatusService = GetBeanUtil.getBean(HAStatusService.class);
    @PostConstruct
    public void init(){
        this.staticPreAlarmService = this.preAlarmService;
        this.staticPreAlarmPointService = this.preAlarmPointService;
    }
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if(!haStatusService.isMasterHost()) {
            log.info("AlarmChangeListener : HAStatus is BACKUP.");
            return;
        }
        log.info("---------执行告警规则点检查任务---------");
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        Object preAlarmPoint = jobDataMap.get("preAlarmPoint");
        List<PreAlarmPoint> preAlarmPointList = (List<PreAlarmPoint>) preAlarmPoint;
        if(preAlarmPointList != null && preAlarmPointList.size() > 0) {
            for(PreAlarmPoint item : preAlarmPointList){
                staticPreAlarmService.scheduleGeneratePreAlarm(item);
            };
        }
    }
}
