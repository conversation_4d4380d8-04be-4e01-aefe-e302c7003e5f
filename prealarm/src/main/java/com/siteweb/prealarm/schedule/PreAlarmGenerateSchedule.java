package com.siteweb.prealarm.schedule;

import cn.hutool.core.map.MapUtil;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.siteweb.prealarm.service.PreAlarmPointService;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@EnableScheduling
@Component
@Slf4j
public class PreAlarmGenerateSchedule {
    //@Autowired
    private Scheduler scheduler;

    @Autowired
    private PreAlarmPointService preAlarmPointService;
    private Boolean isFirstStart =true;
    @Autowired
    private HAStatusService haStatusService;
    @PostConstruct
    private void init() {
        StdSchedulerFactory sf = new StdSchedulerFactory();
        Properties props = new Properties();
        props.put("org.quartz.scheduler.instanceName", "preAlarmScheduler");
        props.put("org.quartz.scheduler.instanceId", "AUTO");
        props.put("spring.quartz.job-store-type", "memory");
        props.put("org.quartz.scheduler.batchTriggerAcquisitionMaxCount", "50");
        props.put("org.quartz.threadPool.threadCount", "10");
        props.put("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        props.put("org.quartz.jobStore.class", "org.quartz.simpl.RAMJobStore");
        props.put("org.quartz.jobStore.misfireThreshold", "600000");

        try {
            sf.initialize(props);
            scheduler = sf.getScheduler();
        } catch (SchedulerException e) {
            log.error("PreAlarmGenerateSchedule.init error:" + e.getMessage());
        }
    }
    @Scheduled(fixedDelay = 5 * 1000)
    protected void schedule() {
        try {
            if(!haStatusService.isMasterHost()) {
                log.info("AlarmChangeListener : HAStatus is BACKUP.");
                return;
            }
            if (preAlarmPointService.getResetScheduleFlag()) {
                log.info("---------预警定时计算任务启动---------");

                // this.scheduler.pauseAll();
                this.scheduler.clear();

                preAlarmPointService.setResetScheduleFlag(false);
                this.schedulePreAlarmCalcJob();
            }


        } catch (Exception e) {
            log.error("PreAlarmGenerateSchedule.schedule error:" + e.getMessage());
        }
    }

    /**
     * 创建预警计算任务
     */
    private void schedulePreAlarmCalcJob() throws SchedulerException {
        Map<String, List<PreAlarmPoint>> map = preAlarmPointService.getPreAlarmPointsExecuteGroup();
        if(MapUtil.isEmpty(map)){
            return;
        }
        scheduler.start();
        Iterator<Map.Entry<String, List<PreAlarmPoint>>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            try {
                Map.Entry<String, List<PreAlarmPoint>> entry = iterator.next();

                JobDetail jobDetail = JobBuilder
                        .newJob(PreAlarmPointExecuteJob.class)
                        .build();
                CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(entry.getKey());
                TriggerKey triggerKey = new TriggerKey("PreAlarmPoint" + entry.getKey(), "PreAlarmPointCalc");
                CronTrigger trigger = TriggerBuilder.newTrigger()
                        .withIdentity(triggerKey)
                        .withSchedule(cronScheduleBuilder)
                        .build();

                jobDetail.getJobDataMap().put("preAlarmPoint", entry.getValue());
                jobDetail.getJobDataMap().put("cronExpression", entry.getKey());
                scheduler.scheduleJob(jobDetail, trigger);
            }
            catch (Exception ex)
            {
                log.error("PreAlarmGenerateSchedule.schedulePreAlarmCalcJob:" + ex.getMessage());
            }
        }

    }
}
