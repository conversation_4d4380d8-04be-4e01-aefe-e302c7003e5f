package com.siteweb.prealarm.schedule;

import com.siteweb.prealarm.dto.PreAlarmConfirm;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.prealarm.service.PreAlarmHistoryService;
import com.siteweb.prealarm.service.PreAlarmPointService;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.utility.service.HAStatusService;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@EnableScheduling
@Component
@Slf4j
public class PreAlarmTransferSchedule {

    @Autowired
    private PreAlarmHistoryService preAlarmHistoryService;

    @Autowired
    private PreAlarmPointService preAlarmPointService;

    @Autowired
    private PreAlarmService preAlarmService;
    @Autowired
    private HAStatusService haStatusService;
    @Scheduled(fixedDelay = 5 * 1000)
    protected void schedule() {
        try {
            if(!haStatusService.isMasterHost()) {
                log.info("AlarmChangeListener : HAStatus is BACKUP.");
                return;
            }
            this.autoConfirmBeforePreAlarms();
            this.transferPreAlarm();
        } catch (Exception ex) {
            log.error("PreAlarmTransferSchedule->schedule", ex);
        }
    }
    private void autoConfirmBeforePreAlarms()
    {
        List<PreAlarm> preAlarms = preAlarmService.findBeforePreAlarms(-3);
        List<PreAlarmConfirm> confirms = new ArrayList<>();
        for (PreAlarm preAlarm : preAlarms) {
            PreAlarmConfirm preAlarmConfirm = new PreAlarmConfirm();
            preAlarmConfirm.setPreAlarmId(preAlarm.getPreAlarmId());
            String remark = "程序自动确认3天前告警";
            if(!StringUtil.isNullOrEmpty(preAlarm.getRemark()))
                remark= preAlarm.getRemark() + ";" + remark;
            preAlarmConfirm.setRemark(remark);
            preAlarmConfirm.setConfirmId(-1);
            confirms.add(preAlarmConfirm);
        }
        preAlarmService.confirmPreAlarms(confirms);
    }
    /**
     * 根据容量属性的percent值产生预警信息。
     */
    private void transferPreAlarm() {
        List<PreAlarm> preAlarms= preAlarmService.findAllEndAndConfirmPreAlarms();
        for (PreAlarm preAlarm : preAlarms) {
            //已结束已确认的告警插入历史表
            PreAlarmHistory preAlarmHistory = preAlarmHistoryService.findSameHistoryPreAlarm(preAlarm.getPreAlarmId(), preAlarm.getStartTime(),preAlarm.getEndTime());
            if( preAlarmHistory == null ) {
                preAlarmHistory = new PreAlarmHistory();
                preAlarmHistory.convert(preAlarm);
                preAlarmHistoryService.save(preAlarmHistory);
            }
            //从预警表中删除
            preAlarmService.deleteById(preAlarm.getPreAlarmId());

        }

    }
}
