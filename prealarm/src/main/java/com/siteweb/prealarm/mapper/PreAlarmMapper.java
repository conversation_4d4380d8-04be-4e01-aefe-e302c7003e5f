package com.siteweb.prealarm.mapper;
import com.siteweb.prealarm.entity.PreAlarm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.prealarm.dto.PreAlarmSeverityCount;

import java.util.List;

public interface PreAlarmMapper extends BaseMapper<PreAlarm> {

    List<PreAlarmSeverityCount> devicePreAlarmSeverityStatistics(Integer preAlarmCategory, Integer objectId);
    List<PreAlarmSeverityCount> structurePreAlarmSeverityStatistics(Integer preAlarmCategory, Integer objectId);

    List<PreAlarmSeverityCount> preAlarmSeverityStatistics();
    /** 插入一条预警报告生成记录(状态为等待生成) */
    void insertPreAlarmReportRecord(PreAlarm capacityPrealarm);
}
