
package com.siteweb.prealarm.mapper;

import com.siteweb.prealarm.dto.GenePowerGenerationRecordDTO;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.time.LocalDateTime;
import java.util.List;

public interface PreAlarmPointMapper extends BaseMapper<PreAlarmPoint> {

    void insertPreAlarmPont(PreAlarmPoint preAlarmPoint);

    Long getExpressionRunTimeCompareValue(int equipmentId, String startDate, String currentDate);
    String getGeneratorLastMaintenanceTime(int equipmentId);

    List<GenePowerGenerationRecordDTO> getNoEndTimeRecord(int equipmentId);

    void batchInsert(List<PreAlarmPoint> needSaveToDB);
    void batchUpdate(List<PreAlarmPoint> needUpdateToDB);
}

