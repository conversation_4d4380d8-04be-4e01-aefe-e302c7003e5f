package com.siteweb.externalapi.service;

import com.siteweb.externalapi.vo.ActiveControlVO;
import com.siteweb.externalapi.vo.ControlOperationVO;
import com.siteweb.externalapi.dto.ActiveControlResponseItem;
import com.siteweb.externalapi.dto.ConfigControlResponseItem;
import com.siteweb.monitoring.enumeration.ControlResultType;

import java.util.List;

public interface ControlCommandService {
    List<ConfigControlResponseItem> getConfigControls(Integer siteId, Integer deviceId);
    List<ActiveControlResponseItem>  getActiveControls(Integer siteId, Integer deviceId);
    ControlResultType sendControlCommand(ActiveControlVO activeControlVO,Integer userId);
    int resendControlCommand(ControlOperationVO activeControlDTO,Integer userId);
    int confirmControlCommand(ControlOperationVO activeControlDTO,Integer userId);
}
