package com.siteweb.externalapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.exception.BigDataException;
import com.siteweb.externalapi.dto.*;
import com.siteweb.externalapi.mapper.EventResponseItemMapper;
import com.siteweb.externalapi.service.AlarmService;
import com.siteweb.externalapi.service.ConfigQueryService;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("AlarmService")
public class AlarmServiceImpl implements AlarmService {
    private static final int ALL = -1;

    @Autowired
    EventResponseItemMapper eventResponseItemMapper;

    @Autowired
    ConfigQueryService configQueryService;
    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    EquipmentManager equipmentManager;

    /**
     * 根据设备ID获取当前的活动告警信息
     *
     * @param siteId   局站ID
     * @param deviceId 设备ID
     * @return 查询结果
     */
    @Override
    public List<EventStartResponseItem> getStartAlarms(Integer siteId, Integer deviceId) {
        List<EventStartResponseItem> items = eventResponseItemMapper.getEventStartResponseItems(siteId, deviceId);
        if (deviceId != -1) {
            String devicePosition = equipmentManager.getEquipmentPosition(deviceId);
            items.forEach(i -> i.setDevicePosition(devicePosition));
        } else {
            // 多个设备，使用缓存避免重复调用
            Map<Integer, String> positionCache = new HashMap<>();
            for (EventStartResponseItem item : items) {
                int id = item.getDeviceId();
                String pos = positionCache.computeIfAbsent(id, equipmentManager::getEquipmentPosition);
                item.setDevicePosition(pos);
            }
        }
        return items;
    }

    @Override
    public List<EventStartResponse> getStartAlarmsWithDeviceIds(Integer siteId, List<Integer> deviceIds) {
        ArrayList<EventStartResponse> eventStartResponses = new ArrayList<>();
        List<EventStartResponseItem> eventStartResponseItems = eventResponseItemMapper.getEventStartResponseItemsWithDeviceId(siteId, deviceIds);
        Map<Integer, List<EventStartResponseItem>> resultMap = eventStartResponseItems.stream().collect(Collectors.groupingBy(EventStartResponseItem::getDeviceId));
        for (Integer deviceId : deviceIds) {
            EventStartResponse eventStartResponse = new EventStartResponse();
            eventStartResponse.setDeviceId(deviceId);
            eventStartResponse.setSiteId(siteId);
            eventStartResponse.setItems(resultMap.get(deviceId));
            eventStartResponses.add(eventStartResponse);
        }
        return eventStartResponses;
    }

    /**
     * 获取告警的技术四化建
     *
     * @param startTime  开始时间
     * @param sequenceId 流水号
     * @return 查询结构体
     */
    @Override
    public List<EventEndResponseItem> getEndAlarms(Date startTime, String sequenceId) {
        return eventResponseItemMapper.getEventEndResponseItems(startTime, sequenceId);
    }

    /**
     * 获取设备事件配置
     *
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return 查询结构体
     */
    @Override
    public List<ConfigEventResponseItem> getConfigAlarms(Integer siteId, Integer deviceId) {
        List<ConfigEventConditionResult> configEventConditionResultList = eventResponseItemMapper.getConfigEventConditionResult(siteId, Collections.singletonList(deviceId));
        if (CollUtil.isEmpty(configEventConditionResultList)) {
            return Collections.emptyList();
        }
        List<ConfigEventResponseItem> configEventResponseItemList = new ArrayList<>();
        Map<Integer, List<ConfigEventConditionResult>> configEventResponseItemMap = configEventConditionResultList.stream().collect(Collectors.groupingBy(ConfigEventConditionResult::getAlarmId));
        for (List<ConfigEventConditionResult> resultList : configEventResponseItemMap.values()) {
            ConfigEventResponseItem configEventResponseItem = new ConfigEventResponseItem(resultList.get(0));
            configEventResponseItem.setAlarmBaseTypes(resultList.stream().map(i -> new ConfigEventConditionResponseItem(i)).collect(Collectors.toList()));
            configEventResponseItemList.add(configEventResponseItem);
        }
        return configEventResponseItemList;
    }

    @Override
    public List<ConfigEventResponse> getConfigAlarmsWithDeviceIds(Integer siteId, List<Integer> deviceIds) {
        List<ConfigEventConditionResult> configEventConditionResultList = eventResponseItemMapper.getConfigEventConditionResult(siteId, deviceIds);
        if (CollUtil.isEmpty(configEventConditionResultList)) {
            return Collections.emptyList();
        }
        Map<Integer, List<ConfigEventConditionResult>> configEventResponseItemMap = configEventConditionResultList.stream().collect(Collectors.groupingBy(ConfigEventConditionResult::getDeviceId));
        List<ConfigEventResponse> responseList = new ArrayList<>();
        configEventResponseItemMap.entrySet().parallelStream().forEach(entry -> {
            ConfigEventResponse configEventResponse = new ConfigEventResponse();
            configEventResponse.setDeviceId(entry.getKey());
            configEventResponse.setSiteId(siteId);
            List<ConfigEventConditionResult> value = entry.getValue();
            Map<Integer, List<ConfigEventConditionResult>> grouped = value.stream().collect(Collectors.groupingBy(ConfigEventConditionResult::getAlarmId));
            List<SimpleConfigEventItem> ConfigEventResponseItemList = new ArrayList<>();
            for (List<ConfigEventConditionResult> resultList : grouped.values()) {
                SimpleConfigEventItem configEventResponseItem = new SimpleConfigEventItem(resultList.get(0));
                configEventResponseItem.setAlarmBaseTypes(resultList.stream().map(i -> new ConfigEventConditionResponseItem(i)).collect(Collectors.toList()));
                ConfigEventResponseItemList.add(configEventResponseItem);
            }
            configEventResponse.setConfigEventResponseItemList(ConfigEventResponseItemList);
            responseList.add(configEventResponse);
        });
        return responseList;
    }


    @Override
    public List<AlarmSeverityResult> getCoreEventSeverities() {
        return eventResponseItemMapper.getCoreEventSeverities();
    }

    @Override
    public List<EventStartResponseItem> getHistoryAlarms(Date startTime, Date endTime) {
        Long count = eventResponseItemMapper.getHistoryAlarmCount(startTime, endTime);
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("external.api.limit");
        int countLimit;
        if (systemConfig == null || CharSequenceUtil.isBlank(systemConfig.getSystemConfigValue()) || Integer.parseInt(systemConfig.getSystemConfigValue()) < 0) {
            countLimit = 50000;
        } else {
            countLimit = Integer.parseInt(systemConfig.getSystemConfigValue());
        }
        if (count > countLimit) {
            throw new BigDataException("history alarms too much");
        }
        return eventResponseItemMapper.getHistoryAlarms(startTime, endTime);
    }

    @Override
    public IPage<EventStartResponseItem> getStartAlarmsPage(Integer siteId, Integer deviceId, Integer standardAlarmNameId, Integer cityId, Page<EventStartResponseItem> objectPage) {
        ArrayList<Integer> stationIds = new ArrayList<>(List.of(ALL));
        if (!Objects.equals(siteId, ALL)) {
            stationIds =new ArrayList<>(List.of(siteId));
        } else if (!Objects.equals(cityId, ALL) && Objects.equals(siteId, ALL)) {
            stationIds = (ArrayList<Integer>) configQueryService.findStationByCity(cityId).stream().map(IdNameDTO::getId).collect(Collectors.toList());
        }
        List<EventStartResponseItem> eventStartResponseItemList = eventResponseItemMapper.getEventStartResponseItemsPage(stationIds, deviceId, standardAlarmNameId);
        List<EventStartResponseItem> slice = eventStartResponseItemList.stream().skip((objectPage.getCurrent()-1) * objectPage.getSize())
                .limit(objectPage.getSize())
                .toList();

        Page<EventStartResponseItem> result = new Page<>(objectPage.getCurrent(), objectPage.getSize(), eventStartResponseItemList.size());
        result.setRecords(slice);
        return result;
    }
}
