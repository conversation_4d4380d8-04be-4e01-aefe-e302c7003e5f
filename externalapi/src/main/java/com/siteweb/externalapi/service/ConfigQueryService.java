package com.siteweb.externalapi.service;

import com.siteweb.externalapi.dto.*;

import java.util.List;

public interface ConfigQueryService {
    List<SiteResponseItem> getAllSites();
    List<RoomResponseItem> getAllRooms(Integer siteId);
    List<EquipmentResponseItem> getDevices(Integer siteId);
    List<BaseDeviceResult> findEquipmentBaseTypes();
    List<IdNameDTO> findCity();
    List<IdNameDTO> findStationByCity(Integer cityId);
    List<Integer> getBaseTypeIdByStandardTypeIdAndSignalStandardDicId(Integer standardTypeId, Integer signalStandardDicId);
}
