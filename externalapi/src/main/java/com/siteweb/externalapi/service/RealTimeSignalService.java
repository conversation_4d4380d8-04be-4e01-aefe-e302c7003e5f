package com.siteweb.externalapi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.externalapi.dto.*;
import io.swagger.models.auth.In;

import java.util.Date;
import java.util.List;

public interface RealTimeSignalService {
    List<ConfigSignalResponseItem>  getConfigSignals(Integer siteId, Integer deviceId);
    List<RealTimeSignalResponseItem> getRealTimeSignals(Integer siteId, Integer deviceId);

    List<RealTimeSignalResponse> getRealTimeSignalsWithDeviceIds(Integer siteId, List<Integer> deviceIds);

    List<HistorySignalResponseItem> getHistorySignalResponseItem(Date startTime, Date endTime, Integer siteId, Integer deviceId, Integer signalId);

    List<ConfigSignalsResponseItem> getConfigSignalsWithDeviceIds(Integer siteId, List<Integer> deviceIds);

    Page<EquipmentRealTimeSignalResponseItem> getRealTimeSignalsPage(Integer siteId, Integer deviceId, Integer signalStandardDicId, Integer standardTypeId, Integer cityId, Page<EquipmentRealTimeSignalResponseItem> objectPage);

}
