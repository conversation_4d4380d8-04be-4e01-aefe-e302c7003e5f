package com.siteweb.externalapi.service.impl;

import com.siteweb.externalapi.dto.*;
import com.siteweb.externalapi.mapper.ConfigResponseItemMapper;
import com.siteweb.externalapi.service.ConfigQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service("ConfigQueryService")
public class ConfigQueryServiceImpl implements ConfigQueryService {
    private static final int ALL = -1;

    @Autowired
    ConfigResponseItemMapper configResponseItemMapper;
    @Override
    public List<SiteResponseItem> getAllSites() {
        return configResponseItemMapper.getSiteResponseItem();
    }

    @Override
    public List<RoomResponseItem>  getAllRooms(Integer siteId) {
        return configResponseItemMapper.getRoomResponseItemBySiteId(siteId);
    }

    @Override
    public List<EquipmentResponseItem> getDevices(Integer siteId) {
        return configResponseItemMapper.getEquipmentResponseItemBySiteId(siteId);
    }


    @Override
    public List<BaseDeviceResult> findEquipmentBaseTypes() {
        return configResponseItemMapper.findEquipmentBaseTypes();
    }

    @Override
    public List<IdNameDTO> findCity() {
        return configResponseItemMapper.findCity();
    }

    @Override
    public List<IdNameDTO> findStationByCity(Integer cityId) {
        //获取所有局站
        if (Objects.equals(cityId, ALL)) {
            return configResponseItemMapper.findAllStation();
        }
        //通过地市id获取局站
        return configResponseItemMapper.findStationByCity(cityId);
    }

    @Override
    public List<Integer> getBaseTypeIdByStandardTypeIdAndSignalStandardDicId(Integer standardTypeId, Integer signalStandardDicId) {
        return configResponseItemMapper.getBaseTypeIdByStandardTypeIdAndSignalStandardDicId(standardTypeId, signalStandardDicId);
    }
}
