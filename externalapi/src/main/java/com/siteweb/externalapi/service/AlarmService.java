package com.siteweb.externalapi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.externalapi.dto.*;

import java.util.Date;
import java.util.List;

public interface AlarmService {
    List<EventStartResponseItem> getStartAlarms(Integer siteId, Integer deviceId);
    List<EventStartResponse> getStartAlarmsWithDeviceIds(Integer siteId, List<Integer> deviceIds);
    List<EventEndResponseItem> getEndAlarms(Date siteId, String sequenceId);
    List<ConfigEventResponseItem> getConfigAlarms(Integer siteId, Integer deviceId);
    List<ConfigEventResponse> getConfigAlarmsWithDeviceIds(Integer siteId, List<Integer> deviceIds);
    List<AlarmSeverityResult> getCoreEventSeverities();

    /**
     * 获取历史告警
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    List<EventStartResponseItem> getHistoryAlarms(Date startTime, Date endTime);
    IPage<EventStartResponseItem> getStartAlarmsPage(Integer siteId, Integer deviceId, Integer standardAlarmNameId, Integer cityId, Page<EventStartResponseItem> objectPage);
}
