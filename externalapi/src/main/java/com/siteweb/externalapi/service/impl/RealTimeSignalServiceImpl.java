package com.siteweb.externalapi.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.externalapi.dto.*;
import com.siteweb.externalapi.service.ConfigQueryService;
import com.siteweb.externalapi.service.RealTimeSignalService;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.mapper.SignalMapper;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("RealTimeSignalService")
public class RealTimeSignalServiceImpl implements RealTimeSignalService {
    @Autowired
    ConfigQueryService configQueryService;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    ActiveSignalManager activeSignalManager;

    @Autowired
    ConfigSignalManager configSignalManager;

    @Autowired
    HistorySignalManager historySignalManager;

    /**
     * 根据设备ID获取设备的信号配置
     * @param siteId
     * @param deviceId
     * @return
     */
    @Override
    public List<ConfigSignalResponseItem> getConfigSignals(Integer siteId, Integer deviceId) {
        List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalByEquipmentId(deviceId);
        List<ConfigSignalResponseItem> rs1 = new ArrayList<>();
        for(ConfigSignalItem signal :configSignalItems){
            ConfigSignalResponseItem signalResponseItem = new ConfigSignalResponseItem();
            signalResponseItem.setSignalId(signal.getSignalId());
            signalResponseItem.setSignalName(signal.getSignalName());
            signalResponseItem.setSignalType(signal.getSignalCategory());
            signalResponseItem.setBaseTypeId(signal.getBaseTypeId());
            signalResponseItem.setSiteId(siteId);
            signalResponseItem.setDeviceId(deviceId);
            signalResponseItem.setUnit(signal.getUnit());
            rs1.add(signalResponseItem);
        }
        return  rs1;
    }

    /**
     * 根据设备ID获取实时信号
     * @param siteId
     * @param deviceId
     * @return
     */
    @Override
    public List<RealTimeSignalResponseItem> getRealTimeSignals(Integer siteId, Integer deviceId) {
        List<SimpleActiveSignal> rs = activeSignalManager.getActiveSignalsByEquipmentId(deviceId);
        List<RealTimeSignalResponseItem> rs1 = new ArrayList<>();
        for(SimpleActiveSignal realTimeSignalItem :rs){
            RealTimeSignalResponseItem realTimeSignalResponseItem = getRealTimeSignalResponseItem(realTimeSignalItem);
            rs1.add(realTimeSignalResponseItem);
        }
        return  rs1;
    }

    @Override
    public List<RealTimeSignalResponse> getRealTimeSignalsWithDeviceIds(Integer siteId, List<Integer> deviceIds) {
        ArrayList<RealTimeSignalResponse> realTimeSignalResponses = new ArrayList<>();
        for (Integer deviceId : deviceIds) {
            RealTimeSignalResponse realTimeSignalResponse = new RealTimeSignalResponse();
            List<SimpleActiveSignal> simpleActiveSignals = activeSignalManager.getActiveSignalsByEquipmentId(deviceId);
            List<RealTimeSignalResponseItem> items = new ArrayList<>();
            for(SimpleActiveSignal realTimeSignalItem : simpleActiveSignals){
                RealTimeSignalResponseItem realTimeSignalResponseItem = new RealTimeSignalResponseItem();
                realTimeSignalResponseItem.setSignalId(realTimeSignalItem.getSignalId());
                realTimeSignalResponseItem.setValue(realTimeSignalItem.getCurrentValue());
                items.add(realTimeSignalResponseItem);
            }
            realTimeSignalResponse.setDeviceId(deviceId);
            realTimeSignalResponse.setSiteId(siteId);
            realTimeSignalResponse.setRealTimeSignals(items);
            realTimeSignalResponses.add(realTimeSignalResponse);
        }
        return realTimeSignalResponses;
    }

    @Override
    public List<HistorySignalResponseItem> getHistorySignalResponseItem(Date startTime, Date endTime, Integer siteId, Integer deviceId, Integer signalId) {
        List<HistorySignalResponseItem> result  = new ArrayList<>();
        List<HistorySignal> historySignals = historySignalManager.findHistorySignalByDuration(startTime,endTime, deviceId + "." + signalId, null, false);
        for(HistorySignal historySignal:historySignals){
            result.add(new HistorySignalResponseItem(historySignal));
        }
        return  result;
    }

    @Override
    public List<ConfigSignalsResponseItem> getConfigSignalsWithDeviceIds(Integer siteId, List<Integer> deviceIds) {
        Map<Integer, List<ConfigSignalItem>> configSignalMapByEquipmentIds = configSignalManager.getConfigSignalMapByEquipmentIds(deviceIds);
        List<ConfigSignalsResponseItem> responseItems = new ArrayList<>();

        for (Map.Entry<Integer, List<ConfigSignalItem>> entry : configSignalMapByEquipmentIds.entrySet()) {
            Integer deviceId = entry.getKey();
            List<ConfigSignalItem> configSignals = entry.getValue();
            ConfigSignalsResponseItem configSignalsResponseItem = new ConfigSignalsResponseItem();
            configSignalsResponseItem.setDeviceId(deviceId);
            configSignalsResponseItem.setSiteId(siteId);
            ArrayList<ConfigSignalResult> configSignalResults = new ArrayList<>();
            for (ConfigSignalItem signal : configSignals) {
                ConfigSignalResult configSignalResult = new ConfigSignalResult();
                configSignalResult.setSignalId(signal.getSignalId());
                configSignalResult.setSignalName(signal.getSignalName());
                configSignalResult.setSignalType(signal.getSignalCategory());
                configSignalResult.setBaseTypeId(signal.getBaseTypeId());
                configSignalResult.setUnit(signal.getUnit());
                configSignalResults.add(configSignalResult);
            }
            configSignalsResponseItem.setConfigSignals(configSignalResults);
            responseItems.add(configSignalsResponseItem);
        }
        return responseItems;
    }



    @Override
    public Page<EquipmentRealTimeSignalResponseItem> getRealTimeSignalsPage(Integer siteId, Integer deviceId, Integer signalStandardDicId, Integer standardTypeId, Integer cityId, Page<EquipmentRealTimeSignalResponseItem> objectPage) {
        List<EquipmentRealTimeSignalResponseItem> rs1 = new ArrayList<>();
        List<Long> baseTypeIds;
        if (standardTypeId != null && signalStandardDicId != null) {
            baseTypeIds = configQueryService.getBaseTypeIdByStandardTypeIdAndSignalStandardDicId(standardTypeId, signalStandardDicId).stream().map(Long::valueOf).toList();
        } else {
            baseTypeIds = new ArrayList<>();
        }
        if (deviceId != null) {
            List<SimpleActiveSignal> rs;
            if (!baseTypeIds.isEmpty()) {
                rs = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(deviceId, baseTypeIds);
            } else {
                rs = activeSignalManager.getActiveSignalsByEquipmentId(deviceId);
            }
            EquipmentRealTimeSignalResponseItem equipmentRealTimeSignalResponseItem = getEquipmentRealTimeSignalResponseItem(deviceId, rs);
            rs1.add(equipmentRealTimeSignalResponseItem);
        } else {
            ArrayList<Integer> stationIds = new ArrayList<>();
            if (siteId != null) {
                stationIds = new ArrayList<>(List.of(siteId));
            } else if (cityId != null) {
                stationIds = (ArrayList<Integer>) configQueryService.findStationByCity(cityId).stream().map(IdNameDTO::getId).collect(Collectors.toList());
            }
            List<Equipment> equipments = equipmentManager.getEquipmentByStationId(stationIds);
            if (!baseTypeIds.isEmpty()) {
                List<ActiveSignalRequestByBaseTypeId> requestByBaseTypeIds = new ArrayList<>();
                equipments.forEach(e -> {
                    ActiveSignalRequestByBaseTypeId requestByBaseTypeId = new ActiveSignalRequestByBaseTypeId(e.getEquipmentId(), baseTypeIds);
                    requestByBaseTypeIds.add(requestByBaseTypeId);
                });
                List<EquipmentActiveSignal> equipmentActiveSignalList = activeSignalManager.getEquipmentActiveSignalByBaseTypeId(requestByBaseTypeIds).stream().filter(i-> !i.getActiveSignals().isEmpty()).toList();
                for (EquipmentActiveSignal signal : equipmentActiveSignalList) {
                    EquipmentRealTimeSignalResponseItem equipmentRealTimeSignalResponseItem = getEquipmentRealTimeSignalResponseItem(signal.getEquipmentId(), signal.getActiveSignals());
                    rs1.add(equipmentRealTimeSignalResponseItem);
                }
            }
        }

        List<EquipmentRealTimeSignalResponseItem> slice = rs1.stream().skip((objectPage.getCurrent() - 1) * objectPage.getSize())
                .limit(objectPage.getSize())
                .toList();

        Page<EquipmentRealTimeSignalResponseItem> result = new Page<>(objectPage.getCurrent(), objectPage.getSize(), rs1.size());
        result.setRecords(slice);
        return result;
    }

    @NotNull
    private static EquipmentRealTimeSignalResponseItem getEquipmentRealTimeSignalResponseItem(Integer deviceId, List<SimpleActiveSignal> rs) {
        EquipmentRealTimeSignalResponseItem equipmentRealTimeSignalResponseItem = new EquipmentRealTimeSignalResponseItem();
        equipmentRealTimeSignalResponseItem.setEquipmentId(deviceId);
        List<RealTimeSignalResponseItem> realSignalList = new ArrayList<>();
        for (SimpleActiveSignal realTimeSignalItem : rs) {
            RealTimeSignalResponseItem realTimeSignalResponseItem = getRealTimeSignalResponseItem(realTimeSignalItem);
            realSignalList.add(realTimeSignalResponseItem);
        }
        equipmentRealTimeSignalResponseItem.setRealSignalList(realSignalList);
        return equipmentRealTimeSignalResponseItem;
    }

    @NotNull
    private static RealTimeSignalResponseItem getRealTimeSignalResponseItem(SimpleActiveSignal realTimeSignalItem) {
        RealTimeSignalResponseItem realTimeSignalResponseItem = new RealTimeSignalResponseItem();
        realTimeSignalResponseItem.setSignalId(realTimeSignalItem.getSignalId());
        realTimeSignalResponseItem.setValue(realTimeSignalItem.getCurrentValue());
        realTimeSignalResponseItem.setOriginalValue(realTimeSignalItem.getOriginalValue());
        realTimeSignalResponseItem.setUnit(realTimeSignalItem.getUnit());
        realTimeSignalResponseItem.setSampleTime(realTimeSignalItem.getSampleTime());
        return realTimeSignalResponseItem;
    }
}
