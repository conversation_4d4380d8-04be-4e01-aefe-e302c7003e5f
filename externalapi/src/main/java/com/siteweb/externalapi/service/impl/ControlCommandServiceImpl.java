package com.siteweb.externalapi.service.impl;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.externalapi.dto.*;
import com.siteweb.externalapi.mapper.ActiveControlResponseItemMapper;
import com.siteweb.externalapi.service.ControlCommandService;
import com.siteweb.externalapi.vo.ActiveControlVO;
import com.siteweb.externalapi.vo.ControlOperationVO;
import com.siteweb.monitoring.entity.Control;
import com.siteweb.monitoring.entity.ControlMeanings;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.mamager.ActiveControlManager;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.mapper.ControlMapper;
import com.siteweb.monitoring.model.ControlSignalRelation;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.service.ControlMeaningsService;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.monitoring.vo.ControlCommandVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("ControlCommandService")
public class ControlCommandServiceImpl implements ControlCommandService {
    @Autowired
    ControlMapper controlMapper;

    @Autowired
    ControlMeaningsService controlMeaningsService;

    @Autowired
    SignalService signalService;

    @Autowired
    RealTimeSignalManager realTimeSignalManager;

    @Autowired
    ActiveControlManager activeControlManager;

    @Autowired
    ActiveControlResponseItemMapper activeControlResponseItemMapper;

    @Autowired
    ActiveControlService activeControlService;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    /**
     * 获取设备控制命令配置
     * @param siteId 局站ID
     * @param deviceId 设备ID
     * @return
     */
    @Override
    public List<ConfigControlResponseItem>  getConfigControls(Integer siteId, Integer deviceId) {
        List<Control> controls = controlMapper.findControlsByEquipmentId(deviceId) ;
        HashMap<Integer, List<ControlMeanings>> css =controlMeaningsService.getControlMeaningsByEquipmentId(deviceId);
        HashMap<Integer, ControlSignalRelation> csr = signalService.findSignalsAboutControlByEquipmentId(deviceId);
        HashMap<Integer, String> controlValue =  getControlSignalValue(csr,deviceId);
        List<ConfigControlResponseItem> rs1 = new ArrayList<>();
        for(Control control :controls){
            ConfigControlResponseItem configControlResponseItem = new ConfigControlResponseItem();
            configControlResponseItem.setControlId(control.getControlId());
            configControlResponseItem.setControlName(control.getControlName());
            configControlResponseItem.setCommandType(control.getCommandType());
            configControlResponseItem.setBaseTypeId(control.getBaseTypeId());
            configControlResponseItem.setDefaultValue(control.getDefaultValue());
            configControlResponseItem.setDeviceId(deviceId);
            configControlResponseItem.setMaxValue(control.getMaxValue());
            configControlResponseItem.setMinValue(control.getMinvalue());
            if(css.containsKey(control.getControlId())) {
                ControlParameter cp = generateControlParameter(css.get(control.getControlId()));
                configControlResponseItem.setParameters(cp);
            }
            if(csr.containsKey(control.getControlId())){
                configControlResponseItem.setSignalUnit(csr.get(control.getControlId()).getSignalUnit());
                if(controlValue.containsKey(csr.get(control.getControlId()).getSignalId())){
                    configControlResponseItem.setSignalValue(controlValue.get(csr.get(control.getControlId()).getSignalId()));
                }
            }
            rs1.add(configControlResponseItem);
        }
        return  rs1;
    }

    /**
     * 根据设备Id获取控制相关的信号值
     * @param lsr
     * @param deviceId
     * @return
     */
    private HashMap<Integer, String> getControlSignalValue(HashMap<Integer, ControlSignalRelation> lsr,Integer deviceId){
        List<String> keys = new ArrayList<>();
        for(ControlSignalRelation csr:lsr.values()){
            keys.add("RealSignal:" + deviceId+ "." + csr.getSignalId());
        }
        List<RealTimeSignalItem>  lrs = realTimeSignalManager.getRealTimeSignalByKeys(keys);
        HashMap<Integer, String> result = new HashMap<>();
        for(RealTimeSignalItem rs:lrs){
            result.put(rs.getSignalId(), rs.getCurrentValue());
        }

        return  result;
    }

    /**
     * 创建控制命令参数包
     * @param css
     * @return
     */
    private ControlParameter generateControlParameter(List<ControlMeanings> css){
        List<ControlParameterItem> parameterItems = new ArrayList<>();
        for(ControlMeanings cs :css){
            ControlParameterItem controlParameterItem = new ControlParameterItem();
            controlParameterItem.setControlId(cs.getControlId());
            controlParameterItem.setParameterValue(cs.getParameterValue());
            controlParameterItem.setMeanings(cs.getMeanings());
            parameterItems.add(controlParameterItem);
        }
        ControlParameter controlParameter = new ControlParameter();
        controlParameter.setItems(parameterItems);
        return  controlParameter;
    }

    /**
     * 获取设备当前的实时控制
     * @param siteId 基站ID
     * @param deviceId 设备ID
     * @return
     */
    @Override
    public List<ActiveControlResponseItem>  getActiveControls(Integer siteId, Integer deviceId) {
        return  activeControlResponseItemMapper.getActiveControlsByDeviceId(siteId,deviceId);
    }

    /**
     * 发送控制命令
     * @param activeControlVO
     * @return
     */
    @Override
    public ControlResultType sendControlCommand(ActiveControlVO activeControlVO,Integer userId) {
        ControlCommandVO controlCommandVO = new ControlCommandVO();
        controlCommandVO.setControlId(activeControlVO.getControlId());
        controlCommandVO.setDescription(activeControlVO.getDescription());
        controlCommandVO.setEquipmentId(activeControlVO.getDeviceId());
        controlCommandVO.setStartTime(new Date());
        controlCommandVO.setSetValue(activeControlVO.getParameters());
        controlCommandVO.setStationId(activeControlVO.getSiteId());
        activeControlService.recordAudit(userId,controlCommandVO);
        return activeControlManager.sendControl(controlCommandVO, userId);
    }

    /**
     * 重发控制命令
     * @param activeControlDTO
     * @return
     */
    @Override
    public int resendControlCommand(ControlOperationVO activeControlDTO,Integer userId) {
        List<Integer> serialNos = new ArrayList<>();
        serialNos.add(activeControlDTO.getSerialNo());
        activeControlService.recordAudit(userId, localeMessageSourceUtil.getMessage("audit.report.reSendControlCommand"), serialNos);
        return activeControlManager.reSendControlCommand(userId, activeControlDTO.getSerialNo());
    }

    /**
     * 确认控制命令
     * @param activeControlDTO
     * @return
     */
    @Override
    public int confirmControlCommand(ControlOperationVO activeControlDTO,Integer userId) {
        List<Integer> serialNos = new ArrayList<>();
        serialNos.add(activeControlDTO.getSerialNo());
        activeControlService.recordAudit(userId,localeMessageSourceUtil.getMessage("audit.report.confirmControlCommand"), serialNos);
        return  activeControlManager.confirmControlCommandBySeq(userId, activeControlDTO.getSerialNo());
    }
}
