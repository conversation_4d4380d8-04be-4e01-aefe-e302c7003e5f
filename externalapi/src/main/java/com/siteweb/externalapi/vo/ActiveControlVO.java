package com.siteweb.externalapi.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="发送控制命令实体",description="发送控制命令实体")
public class ActiveControlVO {
    /**
     * 基站ID
     */
    @ApiModelProperty(value="基站Id",name="siteId")
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 设备ID
     */
    @ApiModelProperty(value="设备Id",name="deviceId")
    @JsonProperty("deviceid")
    private Integer deviceId;

    /**
     * 控制命令ID
     */
    @ApiModelProperty(value="控制命令Id",name="controlId")
    @JsonProperty("controlid")
    private Integer controlId;

    /**
     * 控制命令参数
     */
    @ApiModelProperty(value="控制命令参数",name="parameters")
    @JsonProperty("parameters")
    private String parameters;

    /**
     * 描述信息
     */
    @ApiModelProperty(value="备注信息",name="description")
    @JsonProperty("description")
    private String description;


}
