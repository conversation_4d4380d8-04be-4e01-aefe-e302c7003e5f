package com.siteweb.externalapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.externalapi.dto.AlarmSeverityResult;
import com.siteweb.externalapi.dto.ConfigEventConditionResult;
import com.siteweb.externalapi.dto.EventEndResponseItem;
import com.siteweb.externalapi.dto.EventStartResponseItem;

import java.util.Date;
import java.util.List;

public interface EventResponseItemMapper  extends BaseMapper<EventEndResponseItem> {
    List<EventStartResponseItem> getEventStartResponseItems(Integer siteId, Integer deviceId);
    List<EventStartResponseItem> getEventStartResponseItemsWithDeviceId(Integer siteId, List<Integer> deviceIds);
    List<EventEndResponseItem> getEventEndResponseItems(Date startTime, String sequenceId);
    List<ConfigEventConditionResult> getConfigEventConditionResult(Integer siteId, List<Integer> deviceIds);
    List<AlarmSeverityResult> getCoreEventSeverities();
    List<EventStartResponseItem> getHistoryAlarms(Date startTime, Date endTime);
    Long getHistoryAlarmCount(Date startTime, Date endTime);
    List<EventStartResponseItem> getEventStartResponseItemsPage(List<Integer> stationIds, Integer deviceId, Integer standardAlarmNameId);
}
