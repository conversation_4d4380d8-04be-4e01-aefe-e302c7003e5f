package com.siteweb.externalapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.externalapi.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ConfigResponseItemMapper extends BaseMapper<SiteResponseItem> {
    List<SiteResponseItem> getSiteResponseItem();
    List<RoomResponseItem> getRoomResponseItemBySiteId(Integer siteId);
    List<EquipmentResponseItem> getEquipmentResponseItemBySiteId(Integer siteId);
    List<BaseDeviceResult> findEquipmentBaseTypes();
    List<IdNameDTO> findCity();
    List<IdNameDTO> findAllStation();
    List<IdNameDTO> findStationByCity(@Param("cityId") Integer cityId);
    List<Integer> getBaseTypeIdByStandardTypeIdAndSignalStandardDicId(Integer standardTypeId, Integer signalStandardDicId);
}

