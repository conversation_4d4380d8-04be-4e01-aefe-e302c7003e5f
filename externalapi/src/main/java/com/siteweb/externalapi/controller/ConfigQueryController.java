package com.siteweb.externalapi.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.externalapi.service.ConfigQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  获取配置API
 */
@RestController
@Api(value=" 配置Controller",tags={"局站设备配置获取接口"})
@RequestMapping("/api")
public class ConfigQueryController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    ConfigQueryService configQueryService;
    /**
     *  获取所有局站列表
     * @return
     */
    @ApiOperation(value="获取所有的局站信息")
    @GetMapping("/sites" )
    public ResponseEntity<ResponseResult> getAllSites( ){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }

        return ResponseHelper.successful(configQueryService.getAllSites());
    }

    /**
     *  获取房间列表
     * @param siteId 基站ID
     * @return
     */
    @ApiOperation(value="根据基站ID获取机房列表")
    @GetMapping("/rooms" )
    public ResponseEntity<ResponseResult> getAllRooms(@RequestParam(value = "siteId", required = false)   @ApiParam(name="siteId",value="局站Id",required=true)    Integer siteId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return   ResponseHelper.successful(configQueryService.getAllRooms(siteId));
    }

    /**
     * 获取设备列表
     * @param siteId  基站ID
     * @return
     */
    @ApiOperation(value="根据站站ID获取设备列表")
    @GetMapping(value = "/devices",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDeices(@RequestParam(value = "siteId", required = true)  @ApiParam(name="siteId",value="局站Id",required=true)    Integer siteId){

        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return   ResponseHelper.successful(configQueryService.getDevices(siteId));
    }

    /**
     * 获取设备基类列表
     * @return
     */
    @ApiOperation(value = "获取设备基类列表")
    @GetMapping("/basedevices")
    public ResponseEntity<ResponseResult> getEquipmentBaseTypes() {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(configQueryService.findEquipmentBaseTypes(), HttpStatus.OK);
    }

    @ApiOperation("获取所有地市列表")
    @GetMapping("/city")
    public ResponseEntity<ResponseResult> getCity() {
        return ResponseHelper.successful(configQueryService.findCity());
    }
}
