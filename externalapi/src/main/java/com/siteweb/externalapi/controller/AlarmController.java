package com.siteweb.externalapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.externalapi.service.AlarmService;
import com.siteweb.externalapi.vo.SignalVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 获取告警API
 */
@RestController
@Api(value=" 告警Controller",tags={"设备告警获取接口"})
@RequestMapping("/api")
public class AlarmController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    AlarmService alarmService;

    /**
     * 活动告警查询
     * @param siteId 局站ID，-1表示取所有
     * @param deviceId 设备ID，-1表示取所有
     * @return
     */
    @ApiOperation(value="根据设备ID获取当前的活动告警")
    @GetMapping(value = "/alarms/device",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStartAlarms(@RequestParam(value = "siteId", required = true )  @ApiParam(name="siteId",value="局站Id",required=true)    Integer siteId,
                                                         @RequestParam(value = "deviceId", required = true) @ApiParam(name="deviceId",value="设备Id",required=true)  Integer deviceId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getStartAlarms(siteId, deviceId));
    }

    @ApiOperation(value="批量获取当前的活动告警")
    @PostMapping(value = "/alarms/device",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStartAlarms(@RequestBody SignalVO signalVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getStartAlarmsWithDeviceIds(signalVO.getSiteId(), signalVO.getDeviceIds()));
    }

    /**
     * 告警结束时间查询
     * @param startTime 告警开始时间
     * @param sequenceId 告警序列号 ID
     * @return
     */
    @ApiOperation(value="获取告警结束时间")
    @GetMapping(value = "/alarms/endtime",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEndAlarms(@RequestParam(value = "startTime", required = true)    @ApiParam(name="startTime",value="告警开始时间",required=true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                         @RequestParam(value = "sequenceId", required = true)  @ApiParam(name="sequenceId",value="告警流水号",required=true)  String sequenceId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getEndAlarms(startTime, sequenceId));
    }

    /**
     * 获取设备事件配置
     *
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return
     */
    @ApiOperation(value = "通过局站ID和设备ID查找事件配置")
    @GetMapping(value = "/alarms/cfg",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getConfigAlarms(@RequestParam(value = "siteId", required = false) @ApiParam(name = "siteId", value = "局站Id", required = true) Integer siteId,
                                                          @RequestParam(value = "deviceId", required = false) @ApiParam(name = "deviceId", value = "设备Id", required = true) Integer deviceId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getConfigAlarms(siteId, deviceId));
    }

    /**
     * 批量查找事件配置
     * @param signalVO
     * @return
     */
    @ApiOperation(value = "通过局站ID和设备IDs查找事件配置")
    @PostMapping(value = "/alarms/cfg",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getConfigAlarms(@RequestBody SignalVO signalVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getConfigAlarmsWithDeviceIds(signalVO.getSiteId(), signalVO.getDeviceIds()));
    }

    /**
     * 获取告警等级配置
     *
     * @return
     */
    @ApiOperation(value = "获取告警等级接口")
    @GetMapping(value = "/alarms/levels",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCoreEventSeverities() {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getCoreEventSeverities(), HttpStatus.OK);
    }

    /**
     * 获取历史告警
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @ApiOperation(value = "获取历史告警")
    @GetMapping(value = "/alarms/history",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getHistoryAlarms(@RequestParam(value = "startTime", required = true)
                                                           @ApiParam(name = "startTime", value = "查询开始时间", required = true)
                                                           @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                           @RequestParam(value = "endTime", required = true)
                                                           @ApiParam(name = "endTime", value = "查询结束时间", required = true)
                                                           @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        // 校验用户信息
        TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(alarmService.getHistoryAlarms(startTime, endTime));
    }

    /**
     * 分页查询活动告警
     * @param siteId 局站ID，-1表示取所有
     * @param deviceId 设备ID，-1表示取所有
     * @param alarmStandardDicId 告警标准化id，-1表示取所有
     * @param cityId 地市ID，-1表示取所有
     * @return
     */
    @ApiOperation(value="分页查询活动告警")
    @GetMapping(value = "/alarms/device/page",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStartAlarmsPage(@RequestParam(value = "siteId", required = true )  @ApiParam(name="siteId",value="局站Id",required=true)    Integer siteId,
                                                             @RequestParam(value = "deviceId", required = true) @ApiParam(name="deviceId",value="设备Id",required=true)  Integer deviceId,
                                                             @RequestParam(value = "alarmStandardDicId", required = true) @ApiParam(name="alarmStandardDicId",value="告警标准化Id",required=true)  Integer alarmStandardDicId,
                                                             @RequestParam(value = "cityId", required = true) @ApiParam(name="cityId",value="地市Id",required=true)  Integer cityId,
                                                             Pageable pageable){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmService.getStartAlarmsPage(siteId, deviceId,alarmStandardDicId,cityId,new Page<>(pageable.getPageNumber(), pageable.getPageSize())));
    }
}
