package com.siteweb.externalapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.externalapi.service.RealTimeSignalService;
import com.siteweb.externalapi.vo.SignalVO;
import com.siteweb.monitoring.mamager.SignalSubscribeManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 实时信号API
 */
@RestController
@Api(value="实时数据Controller",tags={"设备实时数据获取接口"})
@RequestMapping("/api")
public class RealTimeSignalController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    RealTimeSignalService realTimeSignalService;
    @Autowired
    SignalSubscribeManager signalSubscribeManager;

    /**
     * 获取设备配置信号信息
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return
     */
    @ApiOperation(value="根据设备ID获取当前的实时信号")
    @GetMapping(value = "/signals/cfg",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getConfigSignals(@RequestParam(value = "siteId", required = false)  @ApiParam(name="siteId",value="局站Id",required=true)     Integer siteId,
                                                           @RequestParam(value = "deviceId", required = false) @ApiParam(name="deviceId",value="设备Id",required=true)  Integer deviceId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(realTimeSignalService.getConfigSignals(siteId,deviceId));
    }

    /**
     * 批量获取设备配置信号信息
     * @param request
     * @return
     */
    @PostMapping(value = "/signals/cfg", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getConfigSignals(@RequestBody SignalVO request) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        Integer siteId = request.getSiteId();
        List<Integer> deviceIds = request.getDeviceIds();
        return ResponseHelper.successful(realTimeSignalService.getConfigSignalsWithDeviceIds(siteId, deviceIds));
    }

    /**
     * 获取设备配置信号信息
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return
     */
    @ApiOperation(value="根据设备ID获取当前的信号配置")
    @GetMapping(value = "/signals/realtime",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getRealTimeSignals(@RequestParam(value = "siteId", required = false)   @ApiParam(name="siteId",value="局站Id",required=true)     Integer siteId,
                                               @RequestParam(value = "deviceId", required = false) @ApiParam(name="deviceId",value="设备Id",required=true)   Integer deviceId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(realTimeSignalService.getRealTimeSignals(siteId,deviceId));
    }

    /**
     * 批量获取实时信号
     * @param signalVO
     * @return
     */
    @ApiOperation(value="批量获取实时信号")
    @PostMapping(value = "/signals/realtime",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getRealTimeSignals(@RequestBody SignalVO signalVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(realTimeSignalService.getRealTimeSignalsWithDeviceIds(signalVO.getSiteId(), signalVO.getDeviceIds()));
    }

    /**
     * 获取设备历史数据信息
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return
     */
    @ApiOperation(value="根据设备ID获取设备历史数据信息")
    @GetMapping(value = "/signals/history",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getRealTimeSignals(@RequestParam(value = "startTime", required = false)  @ApiParam(name="startTime",value="查询开始时间",required=true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                               @RequestParam(value = "endTime", required = false)    @ApiParam(name="endTime",value="查询结束时间",required=true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                               @RequestParam(value = "siteId", required = false)     @ApiParam(name="siteId",value="局站Id",required=true)     Integer siteId,
                                               @RequestParam(value = "deviceId", required = false)   @ApiParam(name="deviceId",value="设备Id",required=true)   Integer deviceId,
                                               @RequestParam(value = "signalId", required = false)   @ApiParam(name="signalId",value="信号Id",required=true)   Integer signalId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(realTimeSignalService.getHistorySignalResponseItem(startTime, endTime, siteId,deviceId,signalId));
    }

    /**
     * 分页查询信号信息
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @param standardTypeId 标准化的类型(移动、联调、电信等)
     * @param signalStandardDicId 信号标准化id
     * @param cityId 地市ID
     * @return
     */
    @ApiOperation(value="分页查询信号信息")
    @GetMapping(value = "/signals/realtime/page",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getRealTimeSignalsPage(@RequestParam(value = "siteId", required = false)   @ApiParam(name="siteId",value="局站Id",required=true)     Integer siteId,
                                                                  @RequestParam(value = "deviceId", required = false) @ApiParam(name="deviceId",value="设备Id",required=true)   Integer deviceId,
                                                                  @RequestParam(value = "signalStandardDicId", required = false) @ApiParam(name="signalStandardDicId",value="信号标准化Id",required=true)  Integer signalStandardDicId,
                                                                  @RequestParam(value = "standardTypeId", required = false) @ApiParam(name="standardTypeId",value="标准化的类型(移动、联调、电信等)",required=true)  Integer standardTypeId,
                                                                  @RequestParam(value = "cityId", required = false) @ApiParam(name="cityId",value="地市Id",required=true)  Integer cityId,
                                                                  Pageable pageable){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(realTimeSignalService.getRealTimeSignalsPage(siteId,deviceId,signalStandardDicId,standardTypeId,cityId,new Page<>(pageable.getPageNumber(), pageable.getPageSize())));
    }
}
