package com.siteweb.externalapi.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.externalapi.dto.CmdApiResponse;
import com.siteweb.externalapi.dto.OperateResult;
import com.siteweb.externalapi.dto.QueryApiResponse;
import com.siteweb.externalapi.dto.ResponseItem;
import com.siteweb.externalapi.service.ControlCommandService;
import com.siteweb.externalapi.vo.ActiveControlVO;
import com.siteweb.externalapi.vo.ControlOperationVO;
import com.siteweb.monitoring.enumeration.ControlResultType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 控制命令API
 */
@RestController
@Api(value=" 控制命令Controller",tags={"控制命令操作接口"})
@RequestMapping("/api")
public class ControlCommandController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    ControlCommandService controlCommandService;

    /**
     *
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return
     */
    @ApiOperation(value="根据设备ID获取控制命令配置列表")
    @GetMapping(value = "/controls/cfg",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getConfigControls(@RequestParam(value = "siteId", required = false) @ApiParam(name="siteId",value="局站Id",required=true) Integer siteId,
                                             @RequestParam(value = "deviceId", required = false) @ApiParam(name="deviceId",value="设备Id",required=true)   Integer deviceId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(controlCommandService.getConfigControls(siteId,deviceId));
    }

    /**
     *
     * @param siteId   基站ID
     * @param deviceId 设备ID
     * @return
     */
    @ApiOperation(value="根据设备ID获取活动控制命令列表")
    @GetMapping(value = "/controls/active",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> getActiveControls(@RequestParam(value = "siteId", required = false)   @ApiParam(name="siteId",value="局站Id",required=true)    Integer siteId,
                                              @RequestParam(value = "deviceId", required = false) @ApiParam(name="deviceId",value="设备Id",required=true)  Integer deviceId){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(controlCommandService.getActiveControls(siteId,deviceId));
    }

    /**
     * 发送控制命令对象
     * @param activeControlVO 控制命令详细信息
     * @return
     */
    @ApiOperation(value="发送控制命令")
    @PostMapping(value = "/controls/sendcontrol",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> sendControlCommand(@Valid @RequestBody ActiveControlVO activeControlVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        ControlResultType resultType =   controlCommandService.sendControlCommand(activeControlVO,userId);
        return  ResponseHelper.successful(new OperateResult(resultType.value()));
    }

    /**
     * 重新发送控制命令对象
     * @param controlOperationVO 控制命令详细信息
     * @return
     */
    @ApiOperation(value="重发控制命令")
    @PostMapping(value = "/controls/resendcontrol",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> resendControlCommand(@Valid @RequestBody ControlOperationVO controlOperationVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        int ret =   controlCommandService.resendControlCommand(controlOperationVO,userId);
        return ResponseHelper.successful(new OperateResult(ret));

    }

    /**
     * 确认控制命令
     * @param controlOperationVO 控制命令详细信息
     * @return
     */
    @ApiOperation(value="确认活动控制命令")
    @PostMapping(value = "/controls/confirmcontrol",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> confirmControlCommand(@Valid @RequestBody ControlOperationVO controlOperationVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        int ret =   controlCommandService.confirmControlCommand(controlOperationVO,userId);
        return ResponseHelper.successful(new OperateResult(ret));

    }
}
