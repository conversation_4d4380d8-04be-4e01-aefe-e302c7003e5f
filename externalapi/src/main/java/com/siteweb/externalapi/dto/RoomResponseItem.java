package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 房间查询结果
 */
@Data
public class RoomResponseItem {
    /**
     * 房间ID
     */
    @JsonProperty("roomid")
    private Integer roomId;
    /**
     * 房间名
     */
    @JsonProperty("roomname")
    private String  roomName;
    /**
     * 中断设备数
     */
    @JsonProperty("interruptcount")
    private Integer interruptCount;

    /**
     * 设备数
     */
    @JsonProperty("devicecount")
    private Integer  deviceCount;

    /**
     * 描述
     */
    @JsonProperty("description")
    private String description;
}
