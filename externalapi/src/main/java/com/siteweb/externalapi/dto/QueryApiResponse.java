package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询API返回结构类型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryApiResponse {

    /**
     * 错误消息
     */
    @JsonProperty("err_msg")
    private String errMsg;

    /**
     * 错误码
     */
    @JsonProperty("err_code")
    private Integer errCode;

    /**
     * 查询返回结果
     */
    @JsonProperty("data")
    private  ResponseItem<?> data;
}
