package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 告警配置结果
 */
@Data
public class ConfigEventResponse {
    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;
    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    @JsonProperty("configevents")
    private List<SimpleConfigEventItem> configEventResponseItemList;
}
