package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 告警开始查询结果
 */
@Data
public class EventStartResponseItem {
    /**
     * 基站名
     */
    @JsonProperty("sitename")
    private String siteName;
    /**
     * 房间名
     */
    @JsonProperty("roomname")
    private String roomName;

    /**
     * 设备名
     */
    @JsonProperty("devicename")
    private String deviceName;
    /**
     * 设备位置
     */
    @JsonProperty("deviceposition")
    private String devicePosition;

    /**
     * 告警名
     */
    @JsonProperty("alarmname")
    private String  alarmName;

    /**
     * 告警涵义
     */
    @JsonProperty("meanings")
    private String  meanings;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("starttime")
    private Date    startTime;

    /**
     * 告警等级
     */
    @JsonProperty("alarmseverity")
    private Integer alarmSeverity;

    /**
     * 告警触发值
     */
    @JsonProperty("trigvalue")
    private Double  trigValue;
    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 房间ID
     */
    @JsonProperty("roomid")
    private Integer roomId;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    /**
     * 告警ID
     */
    @JsonProperty("alarmid")
    private Integer alarmId;

    /**
     * 条件Id
     */
    @JsonProperty("conditionid")
    private Integer conditionId;

    /**
     * 基类设备类型
     */
    @JsonProperty("basedevicetype")
    private Integer baseDeviceType;

    /**
     * 告警基类ID
     */
    @JsonProperty("basetypeid")
    private Long baseTypeId;

    /**
     * 流水号
     */
    @JsonProperty("sequenceid")
    private String  sequenceId;
    
    /**
     * 中心Id
     */
    @JsonProperty("centerid")
    private  Integer centerId;
}
