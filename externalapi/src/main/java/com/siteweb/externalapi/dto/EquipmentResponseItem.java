package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备查询结果
 */
@Data
public class EquipmentResponseItem {

    /**
     * 房间ID
     */
    @JsonProperty("roomid")
    private Integer roomId;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    /**
     * 设备名
     */
    @JsonProperty("devicename")
    private String deviceName;

    /**
     * 设备基类ID
     */
    @JsonProperty("basedeviceid")
    private Integer baseDeviceId;

    /**
     * 设备基类名
     */
    @JsonProperty("basedevicename")
    private String baseDeviceName;

    /**
     * 设备连接状态
     */
    @JsonProperty("connectstate")
    private Integer connectState;

    /**
     * 告警条数
     */
    @JsonProperty("alarmcount")
    private Integer alarmCount;

    /**
     * 按照时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("installtime")
    private Date installTime;

    /**
     * 额定容量
     */
    @JsonProperty("ratedcapacity")
    private String ratedCapacity;
}
