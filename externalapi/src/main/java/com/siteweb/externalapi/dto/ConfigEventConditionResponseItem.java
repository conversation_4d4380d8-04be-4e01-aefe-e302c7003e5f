package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警条件结果
 */
@Data
@NoArgsConstructor
public class ConfigEventConditionResponseItem {
    /**
     * 告警条件ID
     */
    @JsonProperty("conditionid")
    private Integer conditionId;
    /**
     * 开始表达式
     */
    @JsonProperty("startexpression")
    private String startExpression;
    /**
     * 结束表达式
     */
    @JsonProperty("endexpression")
    private String endExpression;
    /**
     * 告警涵义
     */
    @JsonProperty("meanings")
    private String meanings;
    /**
     * 告警基类ID
     */
    @JsonProperty("basetypeid")
    private Long baseTypeId;
    /**
     * 告警基类名称
     */
    @JsonProperty("basetypename")
    private String baseTypeName;
    /**
     * 告警等级ID
     */
    @JsonProperty("alarmseverity")
    private Integer alarmSeverity;

    public ConfigEventConditionResponseItem(ConfigEventConditionResult r) {
        this.setConditionId(r.getConditionId());
        this.setMeanings(r.getMeanings());
        this.setBaseTypeId(r.getBaseTypeId());
        this.setBaseTypeName(r.getBaseTypeName());
        this.setAlarmSeverity(r.getAlarmSeverity());
        this.setStartExpression(new StringBuilder(r.getStartOperation()).append(r.getStartCompareValue()).toString());
        this.setEndExpression(new StringBuilder(r.getEndOperation()).append(r.getEndCompareValue()).toString());
    }
}
