package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ConfigSignalResult {
    /**
     * 信号ID
     */
    @JsonProperty("signalid")
    private Integer  signalId;

    /**
     * 信号名
     */
    @JsonProperty("signalname")
    private String  signalName;

    /**
     * 信号单位
     */
    @JsonProperty("unit")
    private String  unit;

    /**
     *  信号类型
     */
    @JsonProperty("signaltype")
    private Integer  signalType;

    /**
     * 信号基类ID
     */
    @JsonProperty("basetypeid")
    private Long baseTypeId;
}
