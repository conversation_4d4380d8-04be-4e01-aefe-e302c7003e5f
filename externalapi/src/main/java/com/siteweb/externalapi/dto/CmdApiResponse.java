package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  操作类API接口返回对象， 用于发送控制命令
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CmdApiResponse {
    /**
     * 错误消息
     */
    @JsonProperty("err_msg")
    private String errMsg;

    /**
     * 错误码
     */
    @JsonProperty("err_code")
    private Integer errCode;

    /**
     * 操作结果
     */
    @JsonProperty("data")
    private  OperateResult data;
}
