package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 活动控制命令
 */
@Data
public class ActiveControlResponseItem {

    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    /**
     * 控制命令ID
     */
    @JsonProperty("controlid")
    private Integer controlId;

    /**
     * 控制命令名
     */
    @JsonProperty("controlname")
    private String  controlName;

    /**
     * 控制基类ID
     */
    @JsonProperty("basetypeid")
    private Integer baseTypeId;

    /**
     * 控制流水号
     */
    @JsonProperty("serialno")
    private Integer serialNo;

    /**
     * 控制开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("starttime")
    private Date startTime;

    /**
     * 执行人ID
     */
    @JsonProperty("excuterid")
    private Integer  excuterId;

    /**
     * 控制阶段
     */
    @JsonProperty("controlphase")
    private Integer controlPhase;

    /**
     * 控制命令参数
     */
    @JsonProperty("parametervalues")
    private String  parameterValues;

    /**
     * 描述信息
     */
    @JsonProperty("description")
    private String  description;

    /**
     * 控制结果
     */
    @JsonProperty("resultcode")
    private String  resultCode;
}
