package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SimpleConfigEventItem {

    /**
     * 告警名
     */
    @JsonProperty("alarmname")
    private String alarmName;
    /**
     * 告警ID
     */
    @JsonProperty("alarmid")
    private Integer alarmId;
    /**
     * 关联信号ID
     */
    @JsonProperty("signalid")
    private Integer signalId;
    /**
     * 告警基类
     */
    @JsonProperty("alarmbasetypes")
    public List<ConfigEventConditionResponseItem> alarmBaseTypes;

    public SimpleConfigEventItem(ConfigEventConditionResult r) {
        this.setAlarmName(r.getAlarmName());
        this.setAlarmId(r.getAlarmId());
        this.setSignalId(r.getSignalId());
    }
}
