package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.monitoring.entity.HistorySignal;

/**
 * 历史数据查询返回接口
 */
public class HistorySignalResponseItem {
    /**
     * 信号Id
     */
    @JsonProperty("signalid")
    private Integer signalId;

    /**
     * 信号值
     */
    @JsonProperty("value")
    private String value;

    /**
     * 采集时间
     */
    @JsonProperty("sampletime")
    private String sampleTime;

    public HistorySignalResponseItem(HistorySignal historySignal) {
        String signalIdStr = historySignal.getSignalId();
        try {
            int dotIndex = signalIdStr.indexOf('.');
            if (dotIndex == -1 || dotIndex == signalIdStr.length() - 1) {
                throw new IllegalArgumentException("信号ID格式不正确: " + signalIdStr);
            }
            this.signalId = Integer.parseInt(signalIdStr.substring(dotIndex + 1));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("信号ID的数字部分无法解析为整数: " + signalIdStr, e);
        }

        this.value = historySignal.getPointValue();
        this.sampleTime = historySignal.getSampleTime();
    }
}
