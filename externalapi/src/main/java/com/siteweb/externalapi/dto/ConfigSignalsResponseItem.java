package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 实时信号配置信息
 */
@Data
public class ConfigSignalsResponseItem {
    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    @JsonProperty("configsignals")
   private List<ConfigSignalResult> configSignals;


}
