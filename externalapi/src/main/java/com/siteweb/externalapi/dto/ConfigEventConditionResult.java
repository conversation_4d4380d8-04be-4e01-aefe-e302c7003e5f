package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 告警条件查询结果
 */
@Data
public class ConfigEventConditionResult {
    /**
     * 告警条件ID
     */
    @JsonProperty("conditionid")
    private Integer conditionId;
    /**
     * 开始运算符
     */
    @JsonProperty("startoperation")
    private String startOperation;
    /**
     * 开始比较值
     */
    @JsonProperty("startcomparevalue")
    private Double startCompareValue;
    /**
     * 结束运算符
     */
    @JsonProperty("endoperation")
    private String endOperation;
    /**
     * 结束比较值
     */
    @JsonProperty("endcomparevalue")
    private Double endCompareValue;
    /**
     * 告警涵义
     */
    @JsonProperty("meanings")
    private String meanings;
    /**
     * 告警基类ID
     */
    @JsonProperty("basetypeid")
    private Long baseTypeId;
    /**
     * 告警基类名称
     */
    @JsonProperty("basetypename")
    private String baseTypeName;
    /**
     * 告警等级ID
     */
    @JsonProperty("alarmseverity")
    private Integer alarmSeverity;
    /**
     * 告警名
     */
    @JsonProperty("alarmname")
    private String alarmName;
    /**
     * 告警ID
     */
    @JsonProperty("alarmid")
    private Integer alarmId;
    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;
    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;
    /**
     * 关联信号ID
     */
    @JsonProperty("signalid")
    private Integer signalId;
}
