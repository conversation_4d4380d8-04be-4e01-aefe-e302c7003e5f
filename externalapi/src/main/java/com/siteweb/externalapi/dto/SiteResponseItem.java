package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
/**
 * 基站查询结果
 */
@Data
public class SiteResponseItem {

    /**
     * 分组ID
     */
    @JsonProperty("groupid")
    private Integer  groupId;

    /**
     * 分组名
     */
    @JsonProperty("groupname")
    private String   groupName;

    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer  siteId;

    /**
     * 基站名
     */
    @JsonProperty("sitename")
    private String   siteName;

    /**
     * 基站状态
     */
    @JsonProperty("connectstate")
    private Integer  connectState;

    /**
     * 房间数量
     */
    @JsonProperty("roomcount")
    private Integer  roomCount;

    /**
     * 设备数
     */
    @JsonProperty("devicecount")
    private Integer  deviceCount;

    /**
     * 告警条数
     */
    @JsonProperty("alarmcount")
    private Integer  alarmCount;
    /**
     * 中心Id
     */
    @JsonProperty("centerid")
    private  Integer centerId;

}
