package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 告警配置结果
 */
@Data
public class ConfigEventResponseItem {
    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;
    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;
    /**
     * 告警名
     */
    @JsonProperty("alarmname")
    private String alarmName;
    /**
     * 告警ID
     */
    @JsonProperty("alarmid")
    private Integer alarmId;
    /**
     * 关联信号ID
     */
    @JsonProperty("signalid")
    private Integer signalId;
    /**
     * 告警基类
     */
    @JsonProperty("alarmbasetypes")
    public List<ConfigEventConditionResponseItem> alarmBaseTypes;

    public ConfigEventResponseItem(ConfigEventConditionResult r) {
        this.setAlarmName(r.getAlarmName());
        this.setAlarmId(r.getAlarmId());
        this.setSiteId(r.getSiteId());
        this.setDeviceId(r.getDeviceId());
        this.setSignalId(r.getSignalId());
    }
}
