package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 告警结束查询结果
 */
@Data
public class EventEndResponseItem {
    /**
     * 流水号
     */
    @JsonProperty("sequenceid")
    private String  sequenceId;

    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 基站名
     */
    @JsonProperty("sitename")
    private String siteName;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    /**
     * 设备名
     */
    @JsonProperty("devicename")
    private String deviceName;

    /**
     * 告警ID
     */
    @JsonProperty("alarmid")
    private Integer alarmId;

    /**
     *告警名
     */
    @JsonProperty("alarmname")
    private String  alarmName;

    /**
     *告警等级
     */
    @JsonProperty("alarmseverity")
    private Integer alarmSeverity;

    /**
     *告警条件ID
     */
    @JsonProperty("conditionid")
    private Integer conditionId;

    /**
     *开似时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("starttime")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("endtime")
    private Date    endTime ;

    /**
     * 告警涵义
     */
    @JsonProperty("meanings")
    private String  meanings;

    /**
     * 中心Id
     */
    @JsonProperty("centerid")
    private  Integer centerId;
}
