package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 告警开始查询结果
 */
@Data
public class EventStartResponse {

    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    @JsonProperty("alarms")
    private List<EventStartResponseItem> items;

}
