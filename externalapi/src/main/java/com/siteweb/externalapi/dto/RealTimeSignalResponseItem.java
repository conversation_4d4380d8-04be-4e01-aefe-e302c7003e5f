package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 实时数据结果
 */
@Data
public class RealTimeSignalResponseItem {

    /**
     * 信号Id
     */
    @JsonProperty("signalid")
    private Integer signalId;

    /**
     * 信号值
     */
    @JsonProperty("value")
    private String value;

    /**
     * 原始值
     */
    @JsonProperty("originalvalue")
    private String originalValue;

    /**
     * 单位
     */
    @JsonProperty("unit")
    private String unit;

    /**
     * 采集时间
     */
    @JsonProperty("sampletime")
    private String sampleTime;
}
