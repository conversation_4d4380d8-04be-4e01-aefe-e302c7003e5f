package com.siteweb.externalapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 控制命令配置信息
 */
@Data
public class ConfigControlResponseItem {

    /**
     * 基站ID
     */
    @JsonProperty("siteid")
    private Integer siteId;

    /**
     * 设备ID
     */
    @JsonProperty("deviceid")
    private Integer deviceId;

    /**
     * 控制命令ID
     */
    @JsonProperty("controlid")
    private Integer controlId;

    /**
     * 控制命令名
     */
    @JsonProperty("controlname")
    private String  controlName;

    /**
     * 控制命令类型
     */
    @JsonProperty("commandtype")
    private Integer commandType;

    /**
     * 控制基类ID
     */
    @JsonProperty("basetypeid")
    private Long baseTypeId;

    /**
     * 控制值上限
     */
    @JsonProperty("maxvalue")
    private Double  maxValue;

    /**
     * 控制值上限
     */
    @JsonProperty("minvalue")
    private Double  minValue;

    /**
     * 默认设置值
     */
    @JsonProperty("defaultvalue")
    private Double defaultValue;

    /**
     * 信号值
     */
    @JsonProperty("signalvalue")
    private String signalValue;

    /**
     * 信号单位
     */
    @JsonProperty("signalunit")
    private String signalUnit;

    /**
     * 参数信息
     */
    @JsonProperty("parameters")
    private ControlParameter parameters;

}
