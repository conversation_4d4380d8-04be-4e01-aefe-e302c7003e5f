<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.externalapi.mapper.ActiveControlResponseItemMapper">
    <select id="getActiveControlsByDeviceId" resultType="com.siteweb.externalapi.dto.ActiveControlResponseItem">
        select ta.StationId AS siteId,  ta.EquipmentId AS deviceId, ta.ControlId AS controlId, ta.ControlName AS controlName,
        ta.BaseTypeId AS baseTypeId, ta.SerialNo AS serialNo, ta.StartTime AS startTime,ta.ControlExecuterId AS excuterId,
        ta.ControlPhase AS controlPhase, ta.ParameterValues AS parameterValues, ta.Description AS description,ta.ControlResult
        from tbl_activecontrol ta
        where ta.StationId = #{siteId} and ta.EquipmentId= #{deviceId};
    </select>
</mapper>