<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.externalapi.mapper.EventResponseItemMapper">
    <select id="getEventEndResponseItems" resultType="com.siteweb.externalapi.dto.EventEndResponseItem">
        select ta.sequenceid  sequenceid , ta.stationid siteId, ta.stationname  siteName,ta.equipmentid  deviceId, ta.equipmentname  deviceName, ta.eventid  alarmid, ta.eventname  alarmName,
        ta.meanings  meanings, ta.starttime  startTime, ta.eventseverityid alarmSeverity,
        ta.eventconditionid  conditionId, ta.endTime endtime,ta.CenterId centerId
        from tbl_activeevent ta
        where ta.starttime  = #{startTime} and ta.sequenceid  = #{sequenceId}
        union  all
        select ta.sequenceid  sequenceid , ta.stationid , ta.stationname  siteName,ta.equipmentid  deviceId, ta.equipmentname  deviceName, ta.eventid  alarmid, ta.eventname  alarmName,
        ta.meanings  meanings, ta.starttime  startTime, ta.eventseverityid alarmSeverity,
        ta.eventconditionid  conditionId, ta.endTime endtime, ta.CenterId centerId
        from tbl_historyevent ta
        where ta.starttime  = #{startTime} and ta.sequenceid  = #{sequenceId}
    </select>
    <select id="getEventStartResponseItems" resultType="com.siteweb.externalapi.dto.EventStartResponseItem">
        select ta.stationname  siteName, th.housename  roomName, ta.equipmentname  deviceName, ta.eventname  alarmName,
        ta.meanings  meanings, ta.starttime  startTime, ta.eventseverityid alarmSeverity, ta.eventvalue  trigValue,
        ta.stationid  siteId, th.houseid  roomId, ta.equipmentid  deviceId, ta.eventid  alarmId, ta.eventconditionid  conditionId,
        ta.baseEquipmentId  baseDeviceType, ta.basetypeid  baseTypeId, ta.sequenceid  sequenceId,ta.CenterId centerId
        from tbl_activeevent ta inner join tbl_equipment te  on ta.stationid  = te.stationid  and ta.equipmentid  = te.equipmentid
        inner join tbl_house th on te.stationid  = th.stationid and te.houseId = th.HouseId
        where ta.stationid  = case when #{siteId} = -1 then ta.stationId else #{siteId} end
        and ta.equipmentid  = case when #{deviceId} = -1 then ta.equipmentid else  #{deviceId} end
        and ta.endtime  is null
    </select>

    <select id="getEventStartResponseItemsWithDeviceId" resultType="com.siteweb.externalapi.dto.EventStartResponseItem">
        select ta.stationname  siteName, th.housename  roomName, ta.equipmentname  deviceName, ta.eventname  alarmName,
        ta.meanings  meanings, ta.starttime  startTime, ta.eventseverityid alarmSeverity, ta.eventvalue  trigValue,
        ta.stationid  siteId, th.houseid  roomId, ta.equipmentid  deviceId, ta.eventid  alarmId, ta.eventconditionid  conditionId,
        ta.baseEquipmentId  baseDeviceType, ta.basetypeid  baseTypeId, ta.sequenceid  sequenceId,ta.CenterId centerId
        from tbl_activeevent ta inner join tbl_equipment te  on ta.stationid  = te.stationid  and ta.equipmentid  = te.equipmentid
        inner join tbl_house th on te.stationid  = th.stationid and te.houseId = th.HouseId
        WHERE ta.stationid = CASE WHEN #{siteId} = -1 THEN ta.stationId ELSE #{siteId} END
        AND (ta.equipmentid IN
        <foreach item="id" collection="deviceIds" open="(" separator="," close=")">
            #{id}
        </foreach> OR #{deviceIds} IS NULL)
        AND ta.endtime IS NULL
    </select>


    <select id="getConfigEventConditionResult" resultType="com.siteweb.externalapi.dto.ConfigEventConditionResult">
        select tc.eventid alarmId,tt.eventname alarmName, te.stationid siteId, te.equipmentid deviceId, tt.signalid signalId,
        tc.EventConditionId conditionId, tc.StartOperation startOperation, tc.StartCompareValue startCompareValue,
        tc.EndOperation endOperation, tc.EndCompareValue endCompareValue, tc.meanings meanings, tc.BaseTypeId baseTypeId,
        tc.EventSeverity alarmSeverity, tb.BaseTypeName baseTypeName
        from tbl_eventcondition tc
        inner join tbl_equipment te on tc.EquipmentTemplateId = te.EquipmentTemplateId
        left join tbl_event tt on tc.EventId = tt.EventId and tt.EquipmentTemplateId = te.EquipmentTemplateId
        left join tbl_eventbasedic tb on tc.BaseTypeId = tb.BaseTypeId
        where te.StationId=#{siteId}
        and (
        te.EquipmentId IN <foreach item="id" collection="deviceIds" open="(" separator="," close=")">#{id}</foreach>
        )
        and tt.Visible = 1;
    </select>

    <select id="getCoreEventSeverities" resultType="com.siteweb.externalapi.dto.AlarmSeverityResult">
        select cast(ExtendField4 as INT) alarmLevel, ItemId alarmSeverity, ItemValue alarmSeverityName, ExtendField3 displayColor from tbl_dataitem where entryId= 23
        order by alarmLevel asc
    </select>
    <sql id="getHistoryAlarmsSql">
        select ta.stationname  siteName, th.housename  roomName, ta.equipmentname  deviceName, ta.eventname  alarmName,
        ta.meanings  meanings, ta.starttime  startTime, ta.eventseverityid alarmSeverity, ta.eventvalue  trigValue,
        ta.stationid  siteId, th.houseid  roomId, ta.equipmentid  deviceId, ta.eventid  alarmId, ta.eventconditionid  conditionId,
        ta.baseEquipmentId  baseDeviceType, ta.basetypeid  baseTypeId, ta.sequenceid  sequenceId,ta.CenterId centerId
        from tbl_historyevent ta inner join tbl_equipment te  on ta.stationid  = te.stationid  and ta.equipmentid  = te.equipmentid
        inner join tbl_house th on te.stationid  = th.stationid and te.houseId = th.HouseId
        WHERE ta.starttime &gt;= #{startTime} AND  ta.starttime &lt;= #{endTime}
    </sql>
    <select id="getHistoryAlarms" resultType="com.siteweb.externalapi.dto.EventStartResponseItem">
        <include refid="getHistoryAlarmsSql"/>
    </select>
    <select id="getHistoryAlarmCount" resultType="java.lang.Long">
        select count(*) from tbl_historyevent ta
        WHERE ta.starttime &gt;= #{startTime} AND  ta.starttime &lt;= #{endTime}
    </select>
    <select id="getEventStartResponseItemsPage" resultType="com.siteweb.externalapi.dto.EventStartResponseItem">
        select ta.stationname  siteName, th.housename  roomName, ta.equipmentname  deviceName, ta.eventname  alarmName,
        ta.meanings  meanings, ta.starttime  startTime, ta.eventseverityid alarmSeverity, ta.eventvalue  trigValue,
        ta.stationid  siteId, th.houseid  roomId, ta.equipmentid  deviceId, ta.eventid  alarmId, ta.eventconditionid  conditionId,
        ta.baseEquipmentId  baseDeviceType, ta.basetypeid  baseTypeId, ta.sequenceid  sequenceId,ta.CenterId centerId
        from tbl_activeevent ta inner join tbl_equipment te  on ta.stationid  = te.stationid  and ta.equipmentid  = te.equipmentid
        inner join tbl_house th on te.stationid  = th.stationid and te.houseId = th.HouseId
        WHERE (-1 IN
        <foreach item="id" collection="stationIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        OR ta.stationid IN
        <foreach item="id" collection="stationIds" open="(" separator="," close=")">
            #{id}
        </foreach>)
        AND (-1 = #{deviceId} OR ta.equipmentid = #{deviceId})
        AND (-1 = #{standardAlarmNameId} OR ta.StandardAlarmNameId = #{standardAlarmNameId})
        AND ta.endtime IS NULL
        ORDER BY ta.starttime DESC;
    </select>
</mapper>