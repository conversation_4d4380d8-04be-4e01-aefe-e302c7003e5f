<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.externalapi.mapper.ActiveControlResponseItemMapper">
    <select id="getActiveControlsByDeviceId" resultType="com.siteweb.externalapi.dto.ActiveControlResponseItem">
        select ta.StationId siteId,  ta.EquipmentId deviceId, ta.ControlId controlId, ta.ControlName controlName,
        ta.BaseTypeId baseTypeId, ta.SerialNo serialNo, ta.StartTime  startTime,ta.ControlExecuterId excuterId,
        ta.ControlPhase controlPhase, ta.ParameterValues parameterValues, ta.Description description,ta.ControlResult
        from tbl_activecontrol ta
        where ta.StationId = #{siteId} and ta.EquipmentId= #{deviceId};
    </select>
</mapper>