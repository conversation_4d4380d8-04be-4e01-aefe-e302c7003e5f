<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.externalapi.mapper.ConfigResponseItemMapper">
    <select id="getSiteResponseItem" resultType="com.siteweb.externalapi.dto.SiteResponseItem">
        select   st.ResourceStructureId groupId, st.ResourceStructureName groupName, st.StationId siteId, st.StationName siteName, st.connectState,
        COALESCE(a.roomCount,0) roomCount, COALESCE(b.deviceCount,0) deviceCount ,    COALESCE(c.alarmCount,0) alarmCount, st.CenterId
        from
        (select c.StructureId ResourceStructureId, c.StructureName ResourceStructureName, a.StationId, a.StationName, a.connectState, a.CenterId from tbl_station a inner join tbl_stationstructuremap b on a.StationId = b.StationId
        inner join tbl_stationStructure c on c.StructureId = b.StructureId and StructureGroupId=1) st
        inner join
        (select StationId , count(HouseId)  roomCount from tbl_House  group by StationId) a
        on st.StationId = a.StationId
        inner join
        (select StationId, count(equipmentId) deviceCount from TBL_Equipment group by StationId) b
        on st.StationId = b.StationId
        left join
        (select StationId, count(*) alarmCount from tbl_activeevent group by StationId) c
        on st.StationId = c.StationId
    </select>
    <select id="getRoomResponseItemBySiteId" resultType="com.siteweb.externalapi.dto.RoomResponseItem">
            select a.HouseId roomId , a.HouseName roomName, COALESCE(c.deviceCount,0) deviceCount, COALESCE(c.interruptCount,0) interruptCount, a.Description  from tbl_house a
            left join
            (select  StationId, HouseId, count(*) deviceCount, sum(Case when b.connectState = 1  then 0 else 1 end ) interruptCount from tbl_Equipment b where b.StationId = #{siteId} group by StationId, HouseId)  c
            on a.StationId = c.StationId and a.HouseId = c.HouseId
            where a.StationId = #{siteId}
    </select>
    <select id="getEquipmentResponseItemBySiteId" resultType="com.siteweb.externalapi.dto.EquipmentResponseItem">
        select a.HouseId roomId, b.EquipmentId deviceId ,b.equipmentName deviceName,   c.BaseEquipmentId baseDeviceId , c.baseEquipmentName baseDeviceName , b.InstallTime, b.RatedCapacity, COALESCE(c.AlarmCount,0) AlarmCount,b.ConnectState  from tbl_house a
        inner join tbl_equipment b on a.StationId = b.StationId and a.HouseId = b.HouseId and a.StationId = #{siteId}
        inner join tbl_equipmenttemplate d on b.EquipmentTemplateId = d.EquipmentTemplateId
        LEFT join tbl_EquipmentBaseType c on d.EquipmentBaseType= c.baseEquipmentId
        left join
        (select StationId,EquipmentId, count(*) alarmCount from tbl_activeevent where endTime is null  and stationId = #{siteId} group by StationId,EquipmentId) c
        on b.StationId = c.StationId and b.EquipmentId = c.EquipmentId
    </select>
    <select id="findEquipmentBaseTypes" resultType="com.siteweb.externalapi.dto.BaseDeviceResult">
        SELECT t.BaseEquipmentId baseDeviceId, t.BaseEquipmentName baseDeviceName from tbl_equipmentbasetype t
    </select>
    <select id="findCity" resultType="com.siteweb.externalapi.dto.IdNameDTO">
        SELECT a.StructureId id, a.StructureName name
        FROM TBL_StationStructure a
        INNER JOIN TBL_StationStructure b ON b.ParentStructureId = 0 AND a.ParentStructureId = b.StructureId
        WHERE a.IsUngroup = 0
    </select>
    <select id="findAllStation" resultType="com.siteweb.externalapi.dto.IdNameDTO">
        SELECT station.StationId   id,
        station.StationName name
        FROM TBL_Station station
        WHERE station.StationId > 0
        ORDER BY station.StationName
    </select>
    <select id="findStationByCity" resultType="com.siteweb.externalapi.dto.IdNameDTO">
        SELECT station.StationId   id,
        station.StationName name
        FROM TBL_Station station
        INNER JOIN TBL_StationStructureMap ssm ON ssm.StationId = station.StationId
        INNER JOIN TBL_StationStructure ss1 ON ssm.StructureId = ss1.StructureId
        INNER JOIN TBL_StationStructure ss2 ON ss1.ParentStructureId = ss2.StructureId
        WHERE ss2.StructureId = #{cityId}
        AND station.StationId > 0
        ORDER BY station.StationName
    </select>
    <select id="getBaseTypeIdByStandardTypeIdAndSignalStandardDicId" resultType="java.lang.Integer">
        select  distinct(basetypeId) from tbl_signalbasemap where StandardType = #{standardTypeId} and StandardDicId = #{signalStandardDicId}
    </select>
</mapper>