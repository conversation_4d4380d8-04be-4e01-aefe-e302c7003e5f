package com.siteweb.utility.vo;

import com.siteweb.utility.entity.DataItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * tbl_dataitemVO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 12:41:42
 */
@Data
@NoArgsConstructor
@ApiModel(value="DataItem实体",description="DataItem实体")
public class DataItemVO {

	/**
	 * 字典Id
	 */
    @ApiModelProperty(value="字典Id",name="entryItemId")
    private Integer entryItemId;

    /**
     * 父字典ID
     */
    @ApiModelProperty(value="父字典ID",name="parentEntryId")
    private Integer parentEntryId;

    /**
     * 父字典项ID
     */
    @ApiModelProperty(value="父字典项ID",name="parentItemId")
    private Integer parentItemId;

    /**
     * 字典ID
     */
    @ApiModelProperty(value="字典ID",name="entryId")
    private Integer entryId;

    /**
     * 字典项ID
     */
    @ApiModelProperty(value="字典项ID",name="itemId")
    private Integer itemId;

    /**
     * 字典项值
     */
    @ApiModelProperty(value="字典项值",name="itemValue")
    private String itemValue;

    /**
     * 字典项英文值
     */
    @ApiModelProperty(value="字典项英文值",name="itemAlias")
    private String itemAlias;

    /**
     * 是否可用
     */
    @ApiModelProperty(value="是否可用",name="enable")
    private Boolean enable;

    /**
     * 是否为系统
     */
    @ApiModelProperty(value="是否为系统",name="isSystem")
    private Boolean isSystem;

    /**
     * 是否为默认
     */
    @ApiModelProperty(value="是否为默认",name="isDefault")
    private Boolean isDefault;

    /**
     * 描述信息
     */
    @ApiModelProperty(value="描述信息",name="description")
    private String description;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value="扩展字段1",name="extendField1")
    private String extendField1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value="扩展字段2",name="extendField2")
    private String extendField2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value="扩展字段3",name="extendField3")
    private String extendField3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value="扩展字段4",name="extendField4")
    private String extendField4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value="扩展字段5",name="extendField5")
    private String extendField5;
	public DataItem build() {
        DataItem dataItem = new DataItem();
        dataItem.setEntryItemId (this.entryItemId);
        dataItem.setParentEntryId (this.parentEntryId);
        dataItem.setParentItemId (this.parentItemId);
        dataItem.setEntryId (this.entryId);
        dataItem.setItemId (this.itemId);
        dataItem.setItemValue (this.itemValue);
        dataItem.setItemAlias (this.itemAlias);
        dataItem.setEnable (this.enable);
        dataItem.setIsSystem (this.isSystem);
        dataItem.setIsDefault (this.isDefault);
        dataItem.setDescription (this.description);
        dataItem.setExtendField1 (this.extendField1);
        dataItem.setExtendField2 (this.extendField2);
        dataItem.setExtendField3 (this.extendField3);
        dataItem.setExtendField4 (this.extendField4);
        dataItem.setExtendField5 (this.extendField5);
        return dataItem;
	}
}
