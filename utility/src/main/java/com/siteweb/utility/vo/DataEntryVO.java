package com.siteweb.utility.vo;

import com.siteweb.utility.entity.DataEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Dictionary EntryVO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 12:41:42
 */
@Data
@NoArgsConstructor
@ApiModel(value="DataEntry实体",description="DataEntry实体")
public class DataEntryVO {

	/**
	 * 字典项ID
	 */
    @ApiModelProperty(value="字典项ID",name="entryId")
    private Integer entryId;

    /**
     * 字典类型
     */
    @ApiModelProperty(value="字典类型",name="entryCategory")
    private Integer entryCategory;

    /**
     * 字典名
     */
    @ApiModelProperty(value="字典名",name="entryName")
    private String entryName;

    /**
     * 字典标题
     */
    @ApiModelProperty(value="字典标题",name="entryTitle")
    private String entryTitle;

    /**
     * 字典英文名
     */
    @ApiModelProperty(value="字典英文名",name="entryAlias")
    private String entryAlias;

    /**
     * 是否可用
     */
    @ApiModelProperty(value="是否可用",name="enable")
    private Boolean enable;

    /**
     * 描述信息
     */
    @ApiModelProperty(value="描述信息",name="description")
    private String description;
	public DataEntry build() {
        DataEntry dataEntry = new DataEntry();
        dataEntry.setEntryId (this.entryId);
        dataEntry.setEntryCategory (this.entryCategory);
        dataEntry.setEntryName (this.entryName);
        dataEntry.setEntryTitle (this.entryTitle);
        dataEntry.setEntryAlias (this.entryAlias);
        dataEntry.setEnable (this.enable);
        dataEntry.setDescription (this.description);
        return dataEntry;
	}
}
