package com.siteweb.utility.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileItem implements Comparable<FileItem>{

    private String name;

    private String data;

    private String date;

    private String path;

    private String image;

    public int compareTo(FileItem o) {
        if(this.name != null)
            return this.name.toLowerCase().compareTo(o.getName().toLowerCase());
        else
            throw new IllegalArgumentException();
    }

}