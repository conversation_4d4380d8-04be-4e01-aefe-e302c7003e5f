package com.siteweb.utility.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.Authentication;

import java.util.Date;

/**
 * 审计报表实体
 * <AUTHOR>
 * @date 2022/07/18
 */
@Data
@NoArgsConstructor
public class AuditReportVo {
    public AuditReportVo(Integer level, String details) {
        this.level = level;
        this.details = details;
    }

    private Integer auditReportId;

    /**
     * 操作账户
     */
    private String operationAccount;
    /**
     * 审计记录级别
     */
    private Integer level;
    /**
     * 审计操作类型
     */
    private String type;
    /**
     * 操作客户端ip
     */
    private String clientIp;
    /**
     * 详情
     */
    private String details;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 操作结果
     */
    private String result;
    /**
     * 用户认证信息
     */
    private Authentication authentication;
}
