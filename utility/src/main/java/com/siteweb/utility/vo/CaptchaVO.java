package com.siteweb.utility.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: lzy
 * @Date: 2022/4/21 11:19
 */
@Data
@NoArgsConstructor
@ApiModel(value = "验证码响应体", description = "验证码响应体")
public class CaptchaVO {

    @ApiModelProperty(value = "验证码base64图片", name = "departmentId")
    private String imgBase64;

    @ApiModelProperty(value = "验证码校验key", name = "departmentId")
    private String imgKey;

}
