package com.siteweb.utility.quartz.model;

import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class SchedulerJob {

    private String jobName;

    private String jobGroup;

    private Date previousFireTime;

    private Date nextFireTime;

    private String triggerType;

    private Long repeatInterval;

    private  String cronExpression;

    private  String className;

    private Map<String, Object> params;

    @Override
    public String toString() {
        return "SchedulerJob{" +
                "jobName='" + jobName + '\'' +
                ", jobGroup='" + jobGroup + '\'' +
                ", previousFireTime=" + previousFireTime +
                ", nextFireTime=" + nextFireTime +
                '}';
    }
}
