package com.siteweb.utility.quartz.model;

import lombok.Data;

@Data
public class CronTriggerModel extends TriggerModel {

    private String cronExpression;

    @Override
    protected String getTaskTriggerType() {
        return TriggerModel.CRON_TRIGGER_TYPE;
    }

    @Override
    public String toString() {
        return "CronTriggerModel{" +
                "cronExpression='" + cronExpression + '\'' +
                "} " + super.toString();
    }

    public CronTriggerModel(String cronExpression){
        this.cronExpression = cronExpression;
    }
}
