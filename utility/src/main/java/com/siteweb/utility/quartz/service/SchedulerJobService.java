package com.siteweb.utility.quartz.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.siteweb.utility.quartz.factory.JobDetailFactory;
import com.siteweb.utility.quartz.factory.TriggerFactory;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.model.SimpleTriggerModel;
import com.siteweb.utility.quartz.model.TriggerModel;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

@Service
public class SchedulerJobService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulerJobService.class);

    @Autowired
    private Scheduler scheduler;

    private String jobStatus="jobStatus";

    public Trigger.TriggerState getTriggerState(SchedulerJob schedulerJob) throws SchedulerException {
        return scheduler.getTriggerState(new TriggerKey(schedulerJob.getJobName(), schedulerJob.getJobGroup()));
    }

    public void addSchedulerJob(SchedulerJob schedulerJob)throws  SchedulerException,ClassNotFoundException,InstantiationException, IllegalAccessException,NoSuchMethodException,InvocationTargetException{
        if(isJobWithNamePresent(schedulerJob.getJobName(),schedulerJob.getJobGroup())){
            return;
        }
        if(schedulerJob.getTriggerType()== TriggerModel.SIMPLE_TRIGGER_TYPE){
            addSimpleSchedulerJob(schedulerJob);
        }
        else{
            addCronSchedulerJob(schedulerJob);
        }
    }
    public  void  addCronSchedulerJob(SchedulerJob schedulerJob)throws  SchedulerException,ClassNotFoundException,InstantiationException, IllegalAccessException,NoSuchMethodException,InvocationTargetException{
        //构建job信息
        JobDetail jobDetail = JobBuilder.newJob(getClass(schedulerJob.getClassName()).getClass()).withIdentity(schedulerJob.getJobName(), schedulerJob.getJobGroup()).build();

        jobDetail.getJobDataMap().putAll(schedulerJob.getParams());
        //表达式调度构建器(即任务执行的时间)
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(schedulerJob.getCronExpression());

        //按新的cronExpression表达式构建一个新的trigger
        CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(schedulerJob.getJobName(), schedulerJob.getJobGroup())
                .withSchedule(scheduleBuilder).build();

        try {
            scheduler.scheduleJob(jobDetail, trigger);

        } catch (SchedulerException e) {
            LOGGER.error("创建定时任务失败: {}", ExceptionUtil.stacktraceToString(e));
            throw new SchedulerException("Job Create failure");
        }
    }

    public void  addSimpleSchedulerJob(SchedulerJob schedulerJob)throws SchedulerException,ClassNotFoundException,InstantiationException, IllegalAccessException,NoSuchMethodException,InvocationTargetException{
        JobDetail jobDetail = JobDetailFactory.createJobDetail(getClass(schedulerJob.getClassName()).getClass(), schedulerJob.getJobName());
        SimpleTriggerModel simpleTriggerModel = createTriggerModel(schedulerJob);

        Trigger.TriggerState triggerState = getTriggerState(schedulerJob);
        Trigger trigger = TriggerFactory.createSimpleTrigger(simpleTriggerModel, jobDetail);
        if (triggerState.equals(Trigger.TriggerState.NORMAL)) {
            LOGGER.info("{} has create yet", jobDetail.getKey().getName());
        } else if (triggerState.equals(Trigger.TriggerState.PAUSED)) {
            scheduler.resumeJob(jobDetail.getKey());
        } else {
            scheduler.deleteJob(jobDetail.getKey());
            scheduler.scheduleJob(jobDetail, trigger);
            LOGGER.info("create scheduler {}!", jobDetail);
        }
    }

    /**
     * 根据任务创建触发器
     * @param schedulerJob 定时任务信息
     * @return  定时触发器
     */
    private SimpleTriggerModel createTriggerModel(SchedulerJob schedulerJob) {
        SimpleTriggerModel simpleTriggerModel = new SimpleTriggerModel();
        simpleTriggerModel.setRepeatInterval(schedulerJob.getRepeatInterval());
        simpleTriggerModel.setTriggerName(schedulerJob.getJobName());
        return simpleTriggerModel;
    }

    /**
     * 删除定时任务
     */
    public void removeSchedulerJob(SchedulerJob schedulerJob) throws SchedulerException {
        scheduler.pauseTrigger(new TriggerKey(schedulerJob.getJobName(), schedulerJob.getJobGroup()));
        scheduler.unscheduleJob(new TriggerKey(schedulerJob.getJobName(), schedulerJob.getJobGroup()));
        scheduler.deleteJob(JobKey.jobKey(schedulerJob.getJobName(), schedulerJob.getJobGroup()));
    }

    /**
     * 暂停定时任务
     * @param schedulerJob
     * @throws Exception
     */
    public void pauseSchedulerJob(SchedulerJob schedulerJob) throws SchedulerException
    {
        scheduler.pauseJob(JobKey.jobKey(schedulerJob.getJobName(), schedulerJob.getJobGroup()));
    }

    /**
     * 恢复定时任务
     * @param schedulerJob
     * @throws Exception
     */
    public void resumeSchedulerJob(SchedulerJob schedulerJob) throws SchedulerException
    {
        scheduler.resumeJob(JobKey.jobKey(schedulerJob.getJobName(), schedulerJob.getJobGroup()));
    }

    /**
     * 更新定时任务
     * @param schedulerJob
     * @throws Exception
     */
    public void updateSchedulerJob(SchedulerJob schedulerJob) throws SchedulerException,ClassNotFoundException,InstantiationException, IllegalAccessException,NoSuchMethodException,InvocationTargetException    {
        if(!isJobWithNamePresent(schedulerJob.getJobName(),schedulerJob.getJobGroup())){
            addSchedulerJob(schedulerJob);
        }
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(schedulerJob.getJobName(),schedulerJob.getJobGroup());
            // 表达式调度构建器
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(schedulerJob.getCronExpression());

            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);

            // 按新的cronExpression表达式重新构建trigger
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();

            // 按新的trigger重新设置job执行
            scheduler.rescheduleJob(triggerKey, trigger);
        } catch (SchedulerException e) {
            LOGGER.error("更新定时任务失败:{}", ExceptionUtil.stacktraceToString(e));
            throw new SchedulerException("Job Create Failure");
        }
    }

    /**
     * 获取定时任务当前的状态
     * @param schedulerJob
     * @return 是否在运行
     */
    public boolean isJobRunning(SchedulerJob schedulerJob) {
        String jobKey = schedulerJob.getJobName();
        String groupKey =  schedulerJob.getJobGroup();

        return  isRunning(jobKey,groupKey);
    }

    /**
     *  根据分组名跟任务名判断任务状态
     * @param jobKey 任务名
     * @param groupKey 分组名
     * @return 是否在运行
     */
    private boolean isRunning(String jobKey,String groupKey){
        try {
            List<JobExecutionContext> currentJobs = scheduler.getCurrentlyExecutingJobs();
            if(currentJobs!=null){
                for (JobExecutionContext jobCtx : currentJobs) {
                    String jobNameDB = jobCtx.getJobDetail().getKey().getName();
                    String groupNameDB = jobCtx.getJobDetail().getKey().getGroup();
                    if (jobKey.equalsIgnoreCase(jobNameDB) && groupKey.equalsIgnoreCase(groupNameDB)) {
                        return true;
                    }
                }
            }
        } catch (SchedulerException e) {
            LOGGER.error("SchedulerException while checking job with key :{} is running. error message :{}",jobKey,ExceptionUtil.stacktraceToString(e));
            return false;
        }
        return false;
    }

    /**
     * 获取所有的任务列表
     * @return 任务信息
     */
    public List<Map<String, Object>> getAllJobs() {
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            for (String groupName : scheduler.getJobGroupNames()) {
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {

                    String jobName = jobKey.getName();
                    String jobGroup = jobKey.getGroup();
                    JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                    Map map1 = jobDetail.getJobDataMap();
                    //get job's trigger
                    List<Trigger> triggers = (List<Trigger>) scheduler.getTriggersOfJob(jobKey);
                    Date scheduleTime = triggers.get(0).getStartTime();
                    Date nextFireTime = triggers.get(0).getNextFireTime();
                    Date lastFiredTime = triggers.get(0).getPreviousFireTime();


                    Map<String, Object> map = new HashMap<>();
                    map.put("jobName", jobName);
                    map.put("groupName", jobGroup);
                    map.put("scheduleTime", scheduleTime);
                    map.put("lastFiredTime", lastFiredTime);
                    map.put("nextFireTime", nextFireTime);

                    if(isRunning(jobName,jobGroup)){
                        map.put(jobStatus, "RUNNING");
                    }else{
                        String jobState = getJobState(jobName,jobGroup);
                        map.put(jobStatus, jobState);
                    }
                    map.put("params",map1);
                    list.add(map);
                }

            }
        } catch (SchedulerException e) {
            LOGGER.error("SchedulerException while fetching all jobs. error message :{}", ExceptionUtil.stacktraceToString(e));
        }
        return list;
    }

    /**
     * 根据获取分组获取任务一栏
     * @param group
     * @return
     */
    public List<Map<String, Object>> getAllJobsByGroupName(String group) {
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            for (String groupName : scheduler.getJobGroupNames()) {
                if(!group.equals(groupName)){
                    continue;
                }
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {

                    String jobName = jobKey.getName();
                    String jobGroup = jobKey.getGroup();
                    JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                    Map map1 = jobDetail.getJobDataMap();
                    //get job's trigger
                    List<Trigger> triggers = (List<Trigger>) scheduler.getTriggersOfJob(jobKey);
                    Date scheduleTime = triggers.get(0).getStartTime();
                    Date nextFireTime = triggers.get(0).getNextFireTime();
                    Date lastFiredTime = triggers.get(0).getPreviousFireTime();

                    Map<String, Object> map = new HashMap<>();
                    map.put("jobName", jobName);
                    map.put("groupName", jobGroup);
                    map.put("scheduleTime", scheduleTime);
                    map.put("lastFiredTime", lastFiredTime);
                    map.put("nextFireTime", nextFireTime);
                    map.put("params",map1);
                    if(isRunning(jobName,jobGroup)){
                        map.put(jobStatus, "RUNNING");
                    }else{
                        String jobState = getJobState(jobName,jobGroup);
                        map.put(jobStatus, jobState);
                    }
                    list.add(map);
                }

            }
        } catch (SchedulerException e) {
            LOGGER.error("SchedulerException while fetching all jobs. error message :{}",e.getMessage());
        }
        return list;
    }

    /**
     *  根据分组名跟任务名判断任务状态
     * @param jobName
     * @param groupKey
     * @return
     */
    public String getJobState(String jobName,String groupKey) {
        try {
            JobKey jobKey = new JobKey(jobName, groupKey);

            JobDetail jobDetail = scheduler.getJobDetail(jobKey);

            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobDetail.getKey());
            if(triggers != null && !triggers.isEmpty()){
                for (Trigger trigger : triggers) {
                    Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());

                    if (Trigger.TriggerState.PAUSED.equals(triggerState)) {
                        return "PAUSED";
                    }else if (Trigger.TriggerState.BLOCKED.equals(triggerState)) {
                        return "BLOCKED";
                    }else if (Trigger.TriggerState.COMPLETE.equals(triggerState)) {
                        return "COMPLETE";
                    }else if (Trigger.TriggerState.ERROR.equals(triggerState)) {
                        return "ERROR";
                    }else if (Trigger.TriggerState.NONE.equals(triggerState)) {
                        return "NONE";
                    }else if (Trigger.TriggerState.NORMAL.equals(triggerState)) {
                        return "SCHEDULED";
                    }
                }
            }
        } catch (SchedulerException e) {
            LOGGER.error("SchedulerException while checking job with name and group exist:{}",e.getMessage());
        }
        return null;
    }

    public boolean stopJob(SchedulerJob schedulerJob) {
        try{
            String jobKey = schedulerJob.getJobName();
            String groupKey = schedulerJob.getJobGroup();

            JobKey jKey = new JobKey(jobKey, groupKey);

            return scheduler.interrupt(jKey);

        } catch (SchedulerException e) {
            LOGGER.error("SchedulerException while stopping job. error message :{}",e.getMessage());
        }
        return false;
    }

    public boolean isJobWithNamePresent(String jobName,String groupKey) {
        try {
            JobKey jobKey = new JobKey(jobName, groupKey);

            if (scheduler.checkExists(jobKey)){
                return true;
            }
        } catch (SchedulerException e) {
            LOGGER.error("SchedulerException while checking job with name and group exist:{}",e.getMessage());
        }
        return false;
    }

    public static BaseJob getClass(String classname) throws  NoSuchMethodException,ClassNotFoundException,InstantiationException,IllegalAccessException, InvocationTargetException
    {
        Class<?> class1 = Class.forName(classname);

        return (BaseJob)class1.getDeclaredConstructor().newInstance();
    }
}
