package com.siteweb.utility.quartz.model;

import lombok.Data;

@Data
public class SimpleTriggerModel extends TriggerModel {

    private Long repeatInterval;
    private Long repeatCount;

    @Override
    protected String getTaskTriggerType() {
        return TriggerModel.SIMPLE_TRIGGER_TYPE;
    }

    @Override
    public String toString() {
        return "SimpleTriggerModel{" +
                "repeatInterval=" + repeatInterval +
                ", repeatCount=" + repeatCount +
                "} " + super.toString();
    }
    public SimpleTriggerModel(){
    }

    public SimpleTriggerModel(Long repeatCount, Long repeatInterval){
        this.repeatCount = repeatCount;
        this.repeatInterval = repeatInterval;
    }
}
