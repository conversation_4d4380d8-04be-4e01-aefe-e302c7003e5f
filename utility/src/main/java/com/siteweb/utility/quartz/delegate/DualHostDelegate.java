package com.siteweb.utility.quartz.delegate;

import cn.hutool.extra.spring.SpringUtil;
import com.siteweb.utility.service.HAStatusService;
import org.quartz.TriggerKey;
import org.quartz.impl.jdbcjobstore.StdJDBCDelegate;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


public class DualHostDelegate extends StdJDBCDelegate {

    @Override
    public List<TriggerKey> selectTriggerToAcquire(Connection conn, long noLaterThan, long noEarlierThan, int maxCount) throws SQLException {
        HAStatusService haStatusService = SpringUtil.getBean(HAStatusService.class);
        //是主机正常查询job并执行
        if (haStatusService.isMasterHost()) {
            return super.selectTriggerToAcquire(conn, noLaterThan, noEarlierThan, maxCount);
        }
        //备机不需要执行 直接返回空
        return new ArrayList<>();
    }
}
