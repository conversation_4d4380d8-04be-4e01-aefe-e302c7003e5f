package com.siteweb.utility.quartz.controller;

import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class  SchedulerJobController {

    @Autowired
    private SchedulerJobService schedulerService;

    @PostMapping(value = "schedulerjobs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public void addSchedulerJob( @RequestBody SchedulerJob schedulerJob) throws SchedulerException,ClassNotFoundException,InstantiationException, IllegalAccessException,NoSuchMethodException, InvocationTargetException
    {
        schedulerService.addSchedulerJob(schedulerJob);
    }

    @PutMapping(value = "schedulerjobs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public void  updateSchedulerJob(@RequestBody SchedulerJob schedulerJob) throws  SchedulerException,ClassNotFoundException,InstantiationException, IllegalAccessException,NoSuchMethodException,InvocationTargetException   {
        schedulerService.updateSchedulerJob(schedulerJob);
    }

    @DeleteMapping(value = "schedulerjobs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public void  removeSchedulerJob( @RequestBody SchedulerJob schedulerJob) throws SchedulerException {
        schedulerService.removeSchedulerJob(schedulerJob);
    }

    @GetMapping(value = "schedulerjobs",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Map<String, Object>> getSchedulerJob() {
        return   schedulerService.getAllJobs();
    }

    @GetMapping(value = "schedulerjobs",
            params = {"groupName"} ,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Map<String, Object>>  getSchedulerJobByGroup(@RequestParam(value = "groupName",required =true) String groupName){
        return   schedulerService.getAllJobsByGroupName(groupName);
    }
}
