package com.siteweb.utility.quartz.model;

import lombok.Data;

import java.util.Date;

@Data
public abstract class TriggerModel {

    public static final String SIMPLE_TRIGGER_TYPE = "SIMPLE_TRIGGER";
    public static final String CRON_TRIGGER_TYPE = "CRON_TRIGGER";

    private String triggerName; // 触发器唯一名称
    private Date startTime;  // 开始时间
    private Date endTime;    // 结束时间

    public String getTriggerName() {
        return triggerName;
    }

    protected abstract String getTaskTriggerType();

    @Override
    public String toString() {
        return "TriggerModel{" +
                "triggerName='" + triggerName + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", taskTriggerType='" + getTaskTriggerType() + '\'' +
                '}';
    }
}