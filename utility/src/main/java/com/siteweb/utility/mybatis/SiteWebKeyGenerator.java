//package com.siteweb.utility.mybatis;
//
//import com.baomidou.mybatisplus.annotation.DbType;
//import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;
//import com.siteweb.utility.service.PrimaryKeyValueService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
///**
// * <AUTHOR> zhou
// * @Description SiteWebKeyGenerator
// * @createTime 2022-01-06 15:56:41
// */
//public class SiteWebKeyGenerator implements IKeyGenerator {
//
//    private final Logger log = LoggerFactory.getLogger(SiteWebKeyGenerator.class);
//
//    PrimaryKeyValueService primaryKeyValueService;
//
//    public SiteWebKeyGenerator() {
//        //
//    }
//
//    @Override
//    public String executeSql(String incrementerName) {
//        log.debug(incrementerName);
//        return String.format("select %d", primaryKeyValueService.getGlobalIdentity(incrementerName, 0));
//    }
//
//    @Override
//    public DbType dbType() {
//        return null;
//    }
//}
