//package com.siteweb.utility.mybatis;
//
//import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
//import com.siteweb.utility.service.PrimaryKeyValueService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR> zhou
// * @Description SiteWebIdGenerator
// * @createTime 2022-01-07 13:52:26
// */
//@Component
//public class SiteWebIdGenerator implements IdentifierGenerator {
//
//    @Autowired
//    PrimaryKeyValueService primaryKeyValueService;
//
//    @Override
//    public Number nextId(Object entity) {
//        String entityName = entity.getClass().getName();
//        Number globalId = null;
//        switch (entityName) {
//            case "House":
//                globalId = primaryKeyValueService.getGlobalIdentity("tbl_house", 0);
//                break;
//            case "Station":
//                globalId = primaryKeyValueService.getGlobalIdentity("tbl_station", 0);
//                break;
//            default:
//                break;
//        }
//        return globalId;
//    }
//}
