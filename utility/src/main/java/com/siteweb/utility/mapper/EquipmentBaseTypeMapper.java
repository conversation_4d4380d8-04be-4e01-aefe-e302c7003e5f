package com.siteweb.utility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.dto.EquipmentBaseTypeDTO;
import com.siteweb.utility.entity.EquipmentBaseType;

import java.util.List;

public interface EquipmentBaseTypeMapper extends BaseMapper<EquipmentBaseType> {

    List<EquipmentBaseType> findUsedEquipmentBaseTypes();

    EquipmentBaseType findExtFieldByEquipmentBaseTypeId(Integer baseEquipmentId);

    List<EquipmentBaseTypeDTO> findByEquipmentIds(List<Integer> equipmentIds);
}

