package com.siteweb.utility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.entity.ResourceStructureType;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/5/10 14:43
 */
public interface ResourceStructureTypeMapper extends BaseMapper<ResourceStructureType> {
    /**
     *
     * @return {@link List}<{@link ResourceStructureType}>
     */
    List<ResourceStructureType> findUsedStructureObjectType();
    ResourceStructureType findByResourceStructureTypeId(Integer resourceStructureTypeId);
}
