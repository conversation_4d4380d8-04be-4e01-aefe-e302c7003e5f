package com.siteweb.utility.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.vo.AuditReportVo;
import com.siteweb.utility.vo.SystemConfigVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SystemConfigMapper extends BaseMapper<SystemConfig> {

    /**
     * 分页查询系统参数
     * @param systemConfigVO
     * @return
     */
    List<SystemConfig> findSystemConfigList(@Param("systemConfigVO") SystemConfigVO systemConfigVO);

    SystemConfig findBySystemConfigKey(String systemConfigKey);

    void recordAuditReport(AuditReportVo auditReportVo);
}
