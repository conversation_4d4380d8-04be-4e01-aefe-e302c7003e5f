package com.siteweb.utility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.utility.dto.CoreEventSeverity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CoreEventSeverityMapper extends BaseMapper<CoreEventSeverity> {

    List<CoreEventSeverity> getCoreEventSeverities();

    void updateCoreEventSeverities(@Param("list") List<CoreEventSeverity> coreEventSeverityList);

    void updateCorePointSeverity(CoreEventSeverity coreEventSeverity);

    CoreEventSeverity findByEventSeverity(Integer eventSeverity);

    void updateCoreEventSeverityEnable(CoreEventSeverity coreEventSeverity);
}
