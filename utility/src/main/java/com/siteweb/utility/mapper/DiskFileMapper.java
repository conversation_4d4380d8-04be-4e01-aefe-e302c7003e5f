package com.siteweb.utility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.utility.entity.DiskFile;

import java.util.Date;
import java.util.List;

public interface DiskFileMapper extends BaseMapper<DiskFile> {
    void deleteDiskFilesByFilePathAndFileName(String filePath, String fileName);

    DiskFile findFirstByFilePathAndFileName(String filePath, String fileName);

    List<DiskFile> findByFilePath(String filePath);

    List<DiskFile> findByStatusAndCreateTimeBefore(int status, Date time);

    Page<DiskFile> findByFilePathPage(String filePath, Page<DiskFile> page);
}

