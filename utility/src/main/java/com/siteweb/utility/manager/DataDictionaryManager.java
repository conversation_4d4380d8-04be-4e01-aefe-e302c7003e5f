package com.siteweb.utility.manager;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.dto.DataItemDTO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.mapper.CoreEventSeverityMapper;
import com.siteweb.utility.mapper.DataItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;

/**
 * 数据字典管理类
 */
@Component
public class DataDictionaryManager {
    @Autowired
    CoreEventSeverityMapper coreEventSeverityMapper;
    @Autowired
    DataItemMapper dataItemMapper;
    private static final HashMap<Integer, CoreEventSeverity> EVENT_SEVERITY_HASH_MAP = new HashMap<>();

    private static final HashMap<String,  DataItemDTO> DATA_ITEM_HASH_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
         loadCoreEventSeverityFromDB();
    }

    /**
     * 从数据库获取告警等级
     */
    private void loadCoreEventSeverityFromDB() {
        List<CoreEventSeverity> coreEventSeverityList = coreEventSeverityMapper.getCoreEventSeverities();
        for (CoreEventSeverity coreEventSeverity : coreEventSeverityList) {
            EVENT_SEVERITY_HASH_MAP.put(coreEventSeverity.getEventLevel(), coreEventSeverity);
        }

        List<DataItem> dataItems =dataItemMapper.selectList(null);
        for(DataItem dataItem:dataItems){
            DataItemDTO dto = new DataItemDTO(dataItem);
            DATA_ITEM_HASH_MAP.put(dto.getKey(), dto);
        }
    }

    public List<CoreEventSeverity> getAllEventSeverities(){
        return  EVENT_SEVERITY_HASH_MAP.values().stream().toList();
    }

    public CoreEventSeverity getEventSeverityByLevel(Integer eventLevel){
        return EVENT_SEVERITY_HASH_MAP.get(eventLevel);
    }

    public DataItemDTO getDataItemDTO(Integer entryId,Integer itemId){
        DataItemDTO item = DATA_ITEM_HASH_MAP.get(entryId+"."+itemId);
        if(ObjectUtil.isNull(item)){
             DataItem dataItem = dataItemMapper.selectOne(new QueryWrapper<DataItem>().eq("EntryId", entryId).eq("ItemId",itemId));
             if(!ObjectUtil.isNull(item)){
                 item = new DataItemDTO(dataItem);
                 DATA_ITEM_HASH_MAP.put(item.getKey(), item);
             }
        }
        return  item;
    }

}
