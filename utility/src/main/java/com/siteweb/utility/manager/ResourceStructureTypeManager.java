package com.siteweb.utility.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.entity.ResourceStructureType;
import com.siteweb.utility.entity.RoomCategory;
import com.siteweb.utility.mapper.ResourceStructureTypeMapper;
import com.siteweb.utility.mapper.RoomCategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Component
@DependsOn("liquibase")
public class ResourceStructureTypeManager {
    @Autowired
    ResourceStructureTypeMapper resourceStructureTypeMapper;

    @Autowired
    RoomCategoryMapper roomCategoryMapper;
    private static final HashMap<Integer, ResourceStructureType> RESOURCE_TYPE_CONCURRENT_HASH_MAP = new HashMap<>();

    private static final HashMap<Integer, RoomCategory> ROOM_CATEGORY_HASH_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        List<ResourceStructureType> resourceStructures = loadResourceStructureTypeFromDB();
        loadRoomCategoryFromDB();
    }

    /**
     * 从数据库加载ResourceStructureType
     *
     * @return {@link List}<{@link ResourceStructureType}>
     */
    private List<ResourceStructureType> loadResourceStructureTypeFromDB() {
        List<ResourceStructureType> resourceStructureTypeList = resourceStructureTypeMapper.selectList(Wrappers.emptyWrapper());
        for (ResourceStructureType structureType : resourceStructureTypeList) {
            RESOURCE_TYPE_CONCURRENT_HASH_MAP.put(structureType.getResourceStructureTypeId(), structureType);
        }
        return resourceStructureTypeList;
    }

    private List<RoomCategory> loadRoomCategoryFromDB() {
        List<RoomCategory> roomCategoryList = roomCategoryMapper.selectList(Wrappers.emptyWrapper());
        for (RoomCategory roomCategory : roomCategoryList) {
            ROOM_CATEGORY_HASH_MAP.put(roomCategory.getRoomCategoryId(), roomCategory);
        }
        return roomCategoryList;
    }

    /**
     * 根据类型id获取类型
     *
     * @param resourceStructureTypeId 资源类型id
     * @return {@link ResourceStructureType}
     */
    public ResourceStructureType findById(Integer resourceStructureTypeId) {
        return RESOURCE_TYPE_CONCURRENT_HASH_MAP.get(resourceStructureTypeId);
    }

    /**
     * 根据房间类型获取配置数据
     *
     * @param roomCategoryId 房间类型Id
     * @return 房间类型
     */
    public RoomCategory findRoomCategoryById(Integer roomCategoryId) {
        return ROOM_CATEGORY_HASH_MAP.get(roomCategoryId);
    }

    /**
     * 根据场景id获取资源类型
     *
     * @param sceneId 场景id
     * @return {@link List}<{@link ResourceStructureType}>
     */
    public List<ResourceStructureType> findBySceneId(Integer sceneId) {
        return RESOURCE_TYPE_CONCURRENT_HASH_MAP.values()
                .stream()
                .filter(type -> Objects.equals(type.getSceneId(), sceneId))
                .toList();
    }
}
