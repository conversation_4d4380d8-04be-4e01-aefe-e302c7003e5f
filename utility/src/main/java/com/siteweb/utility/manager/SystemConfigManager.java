package com.siteweb.utility.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.mapper.SystemConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 系统配置参数缓存
 **/
@Component
public class SystemConfigManager {

    @Autowired
    SystemConfigMapper systemConfigMapper;

    // 默认缓存时长 60分钟
    private static final Long DEFAULT_TIMEOUT = 60 * 60 * 1000L;

    //创建缓存
    private static final TimedCache<String, SystemConfig> timedCache = CacheUtil.newTimedCache(DEFAULT_TIMEOUT);

    static {
        //启动定时任务，清理过期条目
        timedCache.schedulePrune(DEFAULT_TIMEOUT);
    }

    /**
     * 刷新缓存
     */
    public void refreshSystemConfig() {
        List<SystemConfig> systemConfigList = systemConfigMapper.selectList(Wrappers.emptyWrapper());
        if (CollUtil.isNotEmpty(systemConfigList)) {
            systemConfigList.forEach(a -> timedCache.put(a.getSystemConfigKey().trim(), a));
        }
    }

    public SystemConfig getSystemConfigByKey(String batteryBaseTypeKey) {
        if (CharSequenceUtil.isBlank(batteryBaseTypeKey)) {
            return null;
        }
        batteryBaseTypeKey = batteryBaseTypeKey.trim();
        if (timedCache.isEmpty()) {
            refreshSystemConfig();
        }
        SystemConfig systemConfig = timedCache.get(batteryBaseTypeKey, false);
        if (Objects.isNull(systemConfig)) {
            refreshSystemConfig();
        }
        return timedCache.get(batteryBaseTypeKey, false);
    }


}
