package com.siteweb.utility.manager;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.common.util.FileUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.configuration.SpringMailConfig;
import com.siteweb.utility.entity.EmailMessage;
import com.siteweb.utility.enums.EmailOperationResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

/**
 * 邮件
 *
 * @Author: lzy
 * @Date: 2022/6/20 9:52
 */
@Component
public class EmailMessageManager {

    private static final Logger log = LoggerFactory.getLogger(EmailMessageManager.class);
    private static final String EMAIL_TEMPLATE_IMG_URL = "email-templates/img/";

    @Autowired
    SpringMailConfig springMailConfig;

    public EmailOperationResponse sendMail(String to, String cc, String attachments, String message, String subject, String source) {
        EmailMessage emailMessage = new EmailMessage(springMailConfig.getSpringMailUsername(), to, subject, message, source);
        emailMessage.setCc(cc);
        emailMessage.setAttachments(attachments);
        emailMessage.setSubject(subject);
        return sendMail(emailMessage);
    }

    public EmailOperationResponse sendMail(EmailMessage emailMessage) {
        EmailOperationResponse operationResponse = new EmailOperationResponse();
        try {
            JavaMailSender javaMailSender = buildJavaMailSender();
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            // 设置基本信息
            setMessageBaseParams(helper, emailMessage);

            // 校验是否是纯文本
            if (checkIsPlainText(emailMessage)) {
                // 发送纯文本邮件
                sendPlainTextMail(helper, emailMessage);
            } else {
                // 发送富文本邮件
                sendRichMail(helper, emailMessage);
            }
            long startTime = System.currentTimeMillis();
            javaMailSender.send(message);
            if (log.isInfoEnabled()) {
                log.info("邮件发送成功！主题：{},发送人：{},收件人：[{}],抄送人：[{}],来源：{},发送时间：{},耗时：{}ms",
                        emailMessage.getSubject(),
                        emailMessage.getSender(),
                        emailMessage.getTo(),
                        StringUtils.formatNull(emailMessage.getCc()),
                        emailMessage.getSource(),
                        DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                        System.currentTimeMillis() - startTime);
            }
            operationResponse.setOperationStatus(EmailOperationResponse.ResponseStatusEnum.SUCCESS);
        } catch (Exception e) {
            String error = String.format("Could not send emailMessage to: %s ,Error: %s", emailMessage.getTo(), ExceptionUtil.stacktraceToString(e));
            operationResponse.setOperationStatus(EmailOperationResponse.ResponseStatusEnum.ERROR);
            operationResponse.setOperationMessage(error);
            log.error(error);
        }
        return operationResponse;
    }


    /**
     * 发送纯文本邮件
     *
     * @param helper       邮件消息助手
     * @param emailMessage 待发送消息对象
     */
    private void sendPlainTextMail(MimeMessageHelper helper, EmailMessage emailMessage) throws MessagingException {
        helper.setText(emailMessage.getMessage(), false);
    }

    /**
     * 发送富文本邮件
     *
     * @param helper       邮件消息助手
     * @param emailMessage 待发送消息对象
     * @throws MessagingException
     */
    private void sendRichMail(MimeMessageHelper helper, EmailMessage emailMessage) throws MessagingException {
        if (emailMessage.isHtml() || CharSequenceUtil.isNotEmpty(emailMessage.getAttachments())) {
            helper.setText(emailMessage.getMessage(), true);
        }
        setMessageInlineParams(helper, emailMessage);
        setMessageAttachmentsParams(helper, emailMessage);
    }

    /**
     * 设置基本信息
     *
     * @param helper       邮件消息设置助手
     * @param emailMessage 邮件发送对象
     * @throws MessagingException
     */
    private void setMessageBaseParams(MimeMessageHelper helper, EmailMessage emailMessage) throws MessagingException {
        if (CharSequenceUtil.isNotEmpty(emailMessage.getSender())) {
            helper.setReplyTo(emailMessage.getSender());
        } else {
            helper.setReplyTo(springMailConfig.getSpringMailUsername());
        }
        helper.setFrom(Optional.ofNullable(springMailConfig.getSpringMailFromAddress()).orElse(emailMessage.getSender()));
        helper.setSubject(emailMessage.getSubject());
        if (CharSequenceUtil.isNotEmpty(emailMessage.getTo())) {
            helper.setTo(StringUtils.strToArrBySymbol(emailMessage.getTo(), ";"));
        }
        if (CharSequenceUtil.isNotEmpty(emailMessage.getCc())) {
            helper.setCc(StringUtils.strToArrBySymbol(emailMessage.getCc(), ";"));
        }
    }

    /**
     * 设置附件内容
     *
     * @param helper       邮件消息设置助手
     * @param emailMessage 待发送邮件信息对象
     * @throws MessagingException
     */
    private void setMessageAttachmentsParams(MimeMessageHelper helper, EmailMessage emailMessage) throws MessagingException {
        try {
            if (CharSequenceUtil.isNotEmpty(emailMessage.getAttachments())) {
                for (String filePath : StringUtils.getStringToList(emailMessage.getAttachments())) {
                    File file;
                    if (checkIsAbsolutePath(filePath)) {
                        file = new File(filePath);
                        // 获取文件名
                        filePath = filePath.substring(filePath.lastIndexOf(File.separator) + 1);
                    } else {
                        file = getFileResource(filePath);
                    }
                    if (!file.exists()) {
                        if (log.isInfoEnabled()) {
                            log.info("邮件：{} 附件文件：{}不存在。", emailMessage.getSubject(), emailMessage.getAttachments());
                            return;
                        }
                    }
                    helper.addAttachment(filePath, new FileSystemResource(file));
                }
            }
        } catch (IOException e) {
            log.error("setMessageAttachmentsParams: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 设置内联内容
     *
     * @param helper       邮件消息设置助手
     * @param emailMessage 待发送邮件信息对象
     * @throws MessagingException
     */
    private void setMessageInlineParams(MimeMessageHelper helper, EmailMessage emailMessage) throws MessagingException {
        try {
            if (CharSequenceUtil.isNotEmpty(emailMessage.getInlines())) {
                Map<String, String> inlineMap = StringUtils.getStringToMap(emailMessage.getInlines());
                for (Map.Entry<String, String> entry : inlineMap.entrySet()) {
                    File file;
                    String filePath = entry.getValue();
                    if (checkIsAbsolutePath(filePath)) {
                        file = new File(filePath);
                    } else {
                        file = getFileResource(filePath);
                    }
                    helper.addInline(entry.getKey(), new FileSystemResource(file));
                }
            }
        } catch (IOException e) {
            log.error("setMessageInlineParams: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取资源根目录
     *
     * @return
     */
    private String getClassPath() {
        String path = "";
        try {
            URL resource = getClass().getClassLoader().getResource(EMAIL_TEMPLATE_IMG_URL);
            if (resource != null) {
                path = new File(resource.toURI()).getPath();
            }
        } catch (URISyntaxException ex) {
            log.error("resource is not found：", ex);
        }
        return path;
    }

    /**
     * 类路径或相对路径获取文件对象
     *
     * @param filePath
     * @return
     */
    private File getFileResource(String filePath) throws IOException {
        // 类路径获取
        File file = new File(getClassPath() + File.separator + filePath).getCanonicalFile();
        // 不存在直接则直接获取
        if (!file.exists()) {
            file = new File(FileUtil.filePathFilter(filePath)).getCanonicalFile();
        }
        return file;
    }

    /**
     * 校验是否是绝对路径
     *
     * @param path
     * @return ture:是，false：相对路径
     */
    private boolean checkIsAbsolutePath(String path) {
        return path.startsWith("/") || path.indexOf(":") > 0;
    }

    /**
     * 校验是否是纯文本邮件
     *
     * @return
     */
    private boolean checkIsPlainText(EmailMessage emailMessage) {
        return !(emailMessage.isHtml() || CharSequenceUtil.isNotEmpty(emailMessage.getAttachments()) || CharSequenceUtil.isNotEmpty(emailMessage.getInlines()));
    }


    private JavaMailSender buildJavaMailSender() {
        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        javaMailSender.setHost(springMailConfig.getSpringMailHost());
        javaMailSender.setPort(springMailConfig.getSpringMailPort());
        javaMailSender.setUsername(springMailConfig.getSpringMailUsername());
        javaMailSender.setPassword(StrUtil.blankToDefault(springMailConfig.getSpringMailPassword(), null));
        javaMailSender.setDefaultEncoding(springMailConfig.getSpringMailDefaultEncoding());
        javaMailSender.setProtocol(springMailConfig.getSpringMailProtocol());
        Properties p = new Properties();
        // 设置邮件发送超时时间
        p.setProperty("mail.smtp.connectiontimeout", "5000"); // 连接超时时间5秒
        p.setProperty("mail.smtp.timeout", "15000");          // 读取超时时间15秒
        p.setProperty("mail.smtp.writetimeout", "15000");     // 写入超时时间15秒
        // 邮件debug信息
        p.setProperty("mail.debug", Optional.ofNullable(springMailConfig.getSpringMailDebug()).orElse("false"));
        p.setProperty("mail.smtp.auth", Optional.ofNullable(springMailConfig.getMailSmtpAuth()).orElse("true"));
        if (Boolean.parseBoolean(springMailConfig.getMailSmtpSslEnable())) {
            p.setProperty("mail.smtp.ssl.enable", "true");
        }
        if (Boolean.parseBoolean(springMailConfig.getMailSmtpStarttlsEnable())) {
            p.setProperty("mail.smtp.starttls.enable", "true");
        }
        javaMailSender.setJavaMailProperties(p);
        return javaMailSender;
    }
}
