package com.siteweb.utility.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.utility.entity.GoCronExpression;
import com.siteweb.utility.mapper.GoCronExpressionMapper;
import com.siteweb.utility.service.GoCronExpressionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/4/25 19:35
 */
@Service
@RequiredArgsConstructor
public class GoCronExpressionServiceImpl implements GoCronExpressionService {

    private final GoCronExpressionMapper goCronExpressionMapper;

    @Override
    public List<GoCronExpression> findAll() {
        return goCronExpressionMapper.selectList(null);
    }

    @Override
    public List<GoCronExpression> findBySimpleExpression(String simpleExpression) {
        LambdaQueryWrapper<GoCronExpression> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoCronExpression::getSimpleExpression, simpleExpression);
        return goCronExpressionMapper.selectList(queryWrapper);
    }
}
