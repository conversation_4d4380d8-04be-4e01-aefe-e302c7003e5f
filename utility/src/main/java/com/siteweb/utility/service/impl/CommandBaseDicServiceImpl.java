package com.siteweb.utility.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.utility.dto.CommandBaseDicDTO;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.CommandBaseDic;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.mapper.CommandBaseDicMapper;
import com.siteweb.utility.service.CommandBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("commandBaseDicService")
public class CommandBaseDicServiceImpl implements CommandBaseDicService {

    @Autowired
    private CommandBaseDicMapper commandBaseDicMapper;

    @Override
    public List<CommandBaseDic> findCommandBaseDics() {
        return commandBaseDicMapper.selectList(null);
    }

    @Override
    public int createCommandBaseDic(CommandBaseDic commandBaseDic) {
        return commandBaseDicMapper.insert(commandBaseDic);
    }

    @Override
    public int deleteById(Long commandBaseDicId) {
        return    commandBaseDicMapper.deleteById(commandBaseDicId);
    }

    @Override
    public int updateCommandBaseDic(CommandBaseDic commandBaseDic) {
        return commandBaseDicMapper.updateById(commandBaseDic);
    }

    @Override
    public CommandBaseDic findById(Long commandBaseDicId) {
        return commandBaseDicMapper.selectById(commandBaseDicId);
    }

    @Override
    public List<CommandBaseDicDTO> findControlBaseDicByBaseEquipmentId(Integer baseEquipmentId) {
        List<CommandBaseDicDTO> result = new ArrayList<>();
        List<CommandBaseDic> eventBaseDics = commandBaseDicMapper.selectList(new QueryWrapper<CommandBaseDic>().eq("BaseEquipmentId", baseEquipmentId));
        for (CommandBaseDic eventBaseDic : eventBaseDics) {
            result.add(new CommandBaseDicDTO(eventBaseDic));
        }
        return result;
    }
}
