package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.utility.dto.EquipmentBaseTypeDTO;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.mapper.EquipmentBaseTypeMapper;
import com.siteweb.utility.service.EquipmentBaseTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service("equipmentBaseTypeService")
public class EquipmentBaseTypeServiceImpl implements EquipmentBaseTypeService {

    @Autowired
    EquipmentBaseTypeMapper equipmentBaseTypeMapper;

    @Override
    public List<EquipmentBaseType> findEquipmentBaseTypes() {
        return equipmentBaseTypeMapper.selectList(null);
    }

    @Override
    public List<EquipmentBaseTypeDTO> findUsedEquipmentBaseTypeDTOs() {
        List<EquipmentBaseTypeDTO> result = new ArrayList<>();
        List<EquipmentBaseType> usedEquipmentBaseTypes = equipmentBaseTypeMapper.findUsedEquipmentBaseTypes();
        for (EquipmentBaseType equipmentBaseType : usedEquipmentBaseTypes) {
            result.add(new EquipmentBaseTypeDTO(equipmentBaseType));
        }
        return result;
    }

    @Override
    public EquipmentBaseType findById(Integer equipmentBaseTypeId) {
        return equipmentBaseTypeMapper.selectById(equipmentBaseTypeId);
    }

    @Override
    public EquipmentBaseType findExtFieldByEquipmentBaseTypeId(Integer baseEquipmentId) {
        return equipmentBaseTypeMapper.findExtFieldByEquipmentBaseTypeId(baseEquipmentId);
    }

    @Override
    public List<EquipmentBaseTypeDTO> findByEquipmentIds(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return equipmentBaseTypeMapper.findByEquipmentIds(equipmentIds);
    }
}
