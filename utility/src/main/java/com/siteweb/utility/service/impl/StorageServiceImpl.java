package com.siteweb.utility.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.FileUtil;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.exception.StorageException;
import com.siteweb.utility.service.StorageService;
import com.siteweb.utility.vo.FileItem;
import com.siteweb.utility.vo.FileItemResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.*;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class StorageServiceImpl  implements StorageService {

    private static final String FILE_NOT_FOUND = "File NOT Found!";

    @Autowired
    FileServerConfig fileServerConfig;

    private String pathDelimiter = "/";

    @Override
    public void store(MultipartFile file, String uuid) {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath() + "/" + uuid);
        try {
            if (!rootLocation.toFile().exists()) {
                Files.createDirectories(rootLocation);
            }
            if (file.isEmpty()) {
                throw new StorageException("Failed to store empty file " + file.getOriginalFilename());
            }
            Files.copy(file.getInputStream(), rootLocation.resolve(file.getOriginalFilename()), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            throw new StorageException("Failed to store file " + file.getOriginalFilename(), e);
        }
    }

    @Override
    public void store(MultipartFile file, String uuid, String rename) {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath() + "/" + uuid);
        try {
            if (!rootLocation.toFile().exists()) {
                Files.createDirectories(rootLocation);
            }
            if (file.isEmpty()) {
                throw new StorageException("Failed to store empty file " + rename);
            }
            Files.copy(file.getInputStream(), rootLocation.resolve(rename), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            throw new StorageException("Failed to store file " + rename, e);
        }
    }


    @Override
    public List<Resource> loadFolderAsResources(String uuid) throws IOException {
        List<Resource> resources = new ArrayList<>();
        FileItemResponse itemResponse = getFileList(uuid);
        for (FileItem fileItem : itemResponse.getItems()) {
            Resource resource = loadAsResource(fileItem.getName(), uuid);
            if (resource != null) {
                resources.add(resource);
            }
        }
        return resources;
    }

    @Override
    public Path load(String filename, String uuid) {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath() + pathDelimiter + uuid);
        return rootLocation.resolve(filename);
    }

    @Override
    public Resource loadAsResource(String filename, String uuid) {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath() + pathDelimiter + uuid);
        try {
            Path file = rootLocation.resolve(filename);
            Resource resource = new UrlResource(file.toUri());
            if (resource.exists() || resource.isReadable()) {
                return resource;
            } else {
                if (filename.equals("logo.png")) {
                    return loadDefaultLogoResource();
                } else {
                    return null;
                    //throw new StorageException(FILE_NOT_FOUND);
                }
            }
        } catch (MalformedURLException e) {
            throw new StorageException("FAIL!");
        }
    }

    private Resource loadDefaultLogoResource() throws MalformedURLException {
        URL defaultLogoResource = getClass().getClassLoader().getResource("logo.png");
        if (null != defaultLogoResource) {
            try {
                return new UrlResource(defaultLogoResource.toURI());
            } catch (URISyntaxException e) {
                throw new StorageException(FILE_NOT_FOUND);
            }
        } else {
            throw new StorageException(FILE_NOT_FOUND);
        }
    }

    @PostConstruct
    public void init() {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath());
        try {
            if (!rootLocation.toFile().exists())
                Files.createDirectory(rootLocation);

        } catch (IOException e) {
            throw new StorageException("Could not initialize storage", e);
        }
    }

    @Override
    public void deleteAll() {
        //to be done
    }

    public boolean deleteFile(String filename, String uuid) throws IOException {
        String filePath = fileServerConfig.getRootPath() + pathDelimiter + uuid + pathDelimiter + filename;
        Path fp = Paths.get(filePath);
        //文件存在才删除
        if (Files.exists(fp)) {
            Files.delete(fp);
        }
        return true;
    }

    @Override
    public boolean deleteDirectoryItems(String uuid) throws IOException {
        if (CharSequenceUtil.isEmpty(uuid)) {
            return false;
        }
        File root = new File(fileServerConfig.getRootPath(), FileUtil.filePathFilter(uuid)).getCanonicalFile();
        if (!root.exists()) {
            return true;
        }
        File[] list = root.listFiles();

        for (File f : list) {
            if (!this.deleteDir(f))
                return false;
        }
        Path fp = root.toPath();
        Files.delete(fp);
        return true;
    }

    @Override
    public boolean deleteDir(File dir) throws IOException {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        Path fp = dir.toPath();
        Files.delete(fp);
        return true;
    }

    public boolean createDir(String dirName) throws IOException {
        String filePar = fileServerConfig.getRootPath() + "/" + dirName;// 文件夹路径
        File myPath = new File(FileUtil.filePathFilter(filePar)).getCanonicalFile();
        if (!myPath.exists()) {//若此目录不存在，则创建之
            myPath.mkdir();
        }
        return true;
    }

    @Override
    public boolean checkFileExits(String filename, String path) {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath() + pathDelimiter + path);
        Path file = rootLocation.resolve(filename);
        return Files.exists(rootLocation.resolve(file.getFileName()), LinkOption.NOFOLLOW_LINKS);
    }

    @Override
    public byte[] resourceToByte(Resource resource) {
        byte[] data = new byte[0];
        if (resource == null) {
            return data;
        }
        try {
            data = IoUtil.readBytes(resource.getInputStream());
        } catch (IOException e) {
            log.error("resource read exception：", e);
        }
        return data;
    }

    private void constructDir(ArrayList<FileItem> dir, File f, String dateModify) {
        File[] fbuf = f.listFiles();
        int buf = 0;
        if (fbuf != null) {
            buf = fbuf.length;
        } else {
            buf = 0;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(buf);
        if (buf == 1) {
            sb.append(" item");
        } else {
            sb.append(" items");
        }
        dir.add(new FileItem(f.getName(), sb.toString(), dateModify, f.getAbsolutePath(), "directory_icon"));
    }

    private void constructFls(ArrayList<FileItem> fls, File f, String dateModify) {
        String extenstion = "";
        if (CharSequenceUtil.isNotEmpty(f.getName())) {
            extenstion = FilenameUtils.getExtension(f.getName());
        }

        String lengthValue = "B";
        float length = f.length();
        if (f.length() > 1024 && f.length() < 1048576) {
            lengthValue = "KB";
            length = (float) f.length() / 1024;
        } else if (f.length() > 1048576 && f.length() < 1073741824) {
            lengthValue = "MB";
            length = (float) f.length() / 1048576;
        } else if (f.length() > 1073741824) {
            lengthValue = "GB";
            length = (float) f.length() / 1073741824;
        }
        fls.add(new FileItem(f.getName(), String.format("%.2f", length) + lengthValue, dateModify, f.getAbsolutePath(), extenstion));
    }

    @Override
    public FileItemResponse getFileList(String uuid) throws IOException {
        Path rootLocation = Paths.get(fileServerConfig.getRootPath() + pathDelimiter + "folder");
        if (CharSequenceUtil.isEmpty(uuid)) {
            return new FileItemResponse("server", new ArrayList<>());
        }
        File root = new File(fileServerConfig.getRootPath(), FileUtil.filePathFilter(uuid)).getCanonicalFile();
        if (!root.exists()) {
            return null;
        }
        File[] list = root.listFiles();

        ArrayList<FileItem> dir = new ArrayList<>();
        ArrayList<FileItem> fls = new ArrayList<>();

        for (File f : list) {
            String dateModify = DateUtil.format(new Date(f.lastModified()), DatePattern.NORM_DATETIME_PATTERN);
            if (f.isDirectory()) {
                constructDir(dir, f, dateModify);
            } else {
                constructFls(fls, f, dateModify);
            }
        }
        Collections.sort(dir);
        Collections.sort(fls);
        dir.addAll(fls);
        String path = (root.getAbsolutePath().equalsIgnoreCase(rootLocation.toFile().getAbsolutePath())) ? "server" : root.getParentFile().getAbsolutePath();
        return new FileItemResponse(path, dir);
    }
}
