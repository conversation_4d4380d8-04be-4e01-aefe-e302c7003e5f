package com.siteweb.utility.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.common.util.IpUtil;
import com.siteweb.utility.dto.WebClientLogDTO;
import com.siteweb.utility.entity.WebClientLog;
import com.siteweb.utility.mapper.WebClientLogMapper;
import com.siteweb.utility.service.WebClientLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WebClientLogServiceImpl implements WebClientLogService {
    @Autowired
    private WebClientLogMapper webClientLogMapper;

    @Override
    public int createWebClientLog(WebClientLogDTO webClientLogDTO) {
        WebClientLog webClientLog = BeanUtil.copyProperties(webClientLogDTO, WebClientLog.class);
        webClientLog.setClientIp(IpUtil.getIpAddr());
        return webClientLogMapper.insert(webClientLog);
    }
}
