package com.siteweb.utility.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.FileUtil;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.service.ZipService;
import com.siteweb.utility.util.UnZipUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

@Service
public class ZipServiceImpl implements ZipService {
    @Autowired
    FileServerConfig fileServerConfig;

    @Override
    public boolean unZip(String uuid, String zipName) {
        boolean ret = true;
        if (!serviceHasZipThenUnZip(uuid, zipName)) {
            ret = false;
        }
        return ret;
    }

    private boolean serviceHasZipThenUnZip(String uuid, String zipName) {
        try {
            if (CharSequenceUtil.isEmpty(uuid) || CharSequenceUtil.isEmpty(zipName)) {
                return false;
            }
            String split =  "/";
            String filePath = fileServerConfig.getRootPath() + split + FileUtil.filePathFilter(uuid) + split + FileUtil.filePathFilter(zipName);
            File file = new File(filePath).getCanonicalFile();
            if (!file.exists()) {
                return false;
            }
            if (UnZipUtil.isArchiveFile(file)) {
                UnZipUtil.unZipFiles(file, fileServerConfig.getRootPath() + "/" + uuid + "/");
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

}
