package com.siteweb.utility.service;

import com.siteweb.utility.dto.EventBaseDicDTO;
import com.siteweb.utility.entity.EventBaseDic;

import java.util.List;

public interface EventBaseDicService {

    List<EventBaseDic> findEventBaseDics();

    List<EventBaseDicDTO> findByBaseEquipmentId(Integer baseEquipmentId);

    EventBaseDic findById(Long eventBaseDicId);

    /**
     * 通过设备基类id获取告警基类
     *
     * @param baseEquipmentIdList 设备基类id
     * @return {@link List}<{@link EventBaseDicDTO}>
     */
    List<EventBaseDicDTO> findByBaseEquipmentIds(List<Integer> baseEquipmentIdList);
}

