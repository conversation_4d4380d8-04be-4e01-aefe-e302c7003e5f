package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.utility.service.HAStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description HAStatusServiceImpl
 * @createTime 2023-11-23 09:03:36
 */
@Service
public class HAStatusServiceImpl implements HAStatusService {

    /**
     * 主机标识
     */
    private static final String MASTER = "MASTER";
    @Autowired
    RedisUtil redisUtil;

    @Autowired
    Environment environment;

    @Override
    public boolean isMasterHost() {
        //如果未启用双机高可用部署，则按单机处理
        if (!isEnabled()) {
            return true;
        }
        return MASTER.equals(getHAStatus());
    }

    private String getHAStatus() {
        List<Object> haStatusList = redisUtil.getByPrefixKey("HAStatus:*");
        if (CollUtil.isEmpty(haStatusList)) {
            return MASTER;
        }
        //暂时不考虑redis里面有多个key的情况，以取到的第一个key的值为准
        return haStatusList.get(0).toString();
    }

    @Override
    public boolean isEnabled(){
        String haEnableProperty = environment.getProperty("ha.enable", "");
        return Boolean.parseBoolean(haEnableProperty);
    }
}
