package com.siteweb.utility.service;

import com.siteweb.utility.dto.DataItemTree;
import com.siteweb.utility.entity.DataItem;

import java.util.List;
import java.util.Map;

public interface DataItemService {

    List<DataItem> findDataItems();

    int createDataItem(DataItem dataItem);

    int deleteById(Integer dataItemId);

    int updateDataItem(DataItem dataItem);

    DataItem findById(Integer dataItemId);

    List<DataItem> findByEntryId(Integer entryId);

    Integer getCenterId();
    /**
     * 新增dataItem主要根据entryId与itemId判断是新增还是更新
     * @param entryId
     * @param itemValue
     * @return {@link Integer}
     */
    Integer createDataItem(Integer entryId, String itemValue);

    DataItem createDataItemAndReturn(Integer entryId, String itemValue);

    Integer updateDataItem(Integer entryId, Integer id, String name);

    boolean exists(Integer entryId, String value);

    int deleteByEntryIdAndItemId(Integer entryId, Integer itemId);

    DataItem findByEntryIdIdAndItemId(Integer entryId, Integer itemId);

    Map<Integer,String> findIdValueMapByEntryId(Integer entryId);

    List<DataItemTree> findDataItemTreeByEntryId(Integer entryId);
}

