package com.siteweb.utility.service.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import cn.hutool.core.util.IdUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.configuration.CaptchaConfig;
import com.siteweb.utility.service.CaptchaService;
import com.siteweb.utility.vo.CaptchaVO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 *
 * @Author: lzy
 * @Date: 2022/4/21 11:21
 */
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final LocaleMessageSourceUtil messageSourceUtil;
    private final CaptchaConfig captchaConfig;
    private final RedisUtil redisUtil;
    private static final Logger log = LoggerFactory.getLogger(CaptchaServiceImpl.class);

    @Override
    public CaptchaVO buildLoginCaptcha() {
        ShearCaptcha shearCaptcha = CaptchaUtil.createShearCaptcha(captchaConfig.getWidth(), captchaConfig.getHeight(), captchaConfig.getCodeCount(), captchaConfig.getThickness());
        CaptchaVO captchaVO = new CaptchaVO();
        shearCaptcha.setGenerator(new RandomGenerator("0123456789",4));
        String imageBase64Data = shearCaptcha.getImageBase64Data();
        captchaVO.setImgBase64(imageBase64Data);
        String imgKey = IdUtil.fastSimpleUUID();
        captchaVO.setImgKey(imgKey);

        boolean loop = redisUtil.set(captchaConfig.getCaptchaImgKey() + imgKey, shearCaptcha.getCode(), captchaConfig.getExpireTime());
        if (loop) {
            if (log.isInfoEnabled()) {
                log.info("The generated key: {}", imgKey);
                log.info("The generated code: {}", shearCaptcha.getCode());
            }
            return captchaVO;
        }
        throw new BusinessException(messageSourceUtil.getMessage("login.captcha.build.error"));
    }
}
