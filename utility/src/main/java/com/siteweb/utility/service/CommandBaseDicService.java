package com.siteweb.utility.service;

import com.siteweb.utility.dto.CommandBaseDicDTO;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.CommandBaseDic;

import java.util.List;

public interface CommandBaseDicService{

    List<CommandBaseDic> findCommandBaseDics();

    int createCommandBaseDic(CommandBaseDic commandBaseDic);

    int deleteById(Long commandBaseDicId);

    int updateCommandBaseDic(CommandBaseDic commandBaseDic);

    CommandBaseDic findById(Long commandBaseDicId);


    List<CommandBaseDicDTO> findControlBaseDicByBaseEquipmentId(Integer baseEquipmentId);
}

