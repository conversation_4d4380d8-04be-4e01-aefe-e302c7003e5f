package com.siteweb.utility.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.utility.entity.OperationRecord;
import com.siteweb.utility.mapper.OperationRecordMapper;
import com.siteweb.utility.service.OperationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> zhou
 * @description OperationRecordServiceImpl
 * @createTime 2022-06-25 09:44:20
 */
@Service
@Slf4j
public class OperationRecordServiceImpl implements OperationRecordService {

    @Autowired
    OperationRecordMapper operationRecordMapper;
    @Autowired
    NamedParameterJdbcTemplate jdbcTemplate;
    @Override
    public int saveOperationRecord(OperationRecord operationRecord) {
        if (illegalOperationRecord(operationRecord)) {
            return -1;
        }
        String stationName = getStationName(operationRecord.getStationId());
        operationRecord.setStationName(stationName);
        operationRecord.setOperationTime(new Date());
        return operationRecordMapper.insert(operationRecord);
    }

    private String getStationName(Integer stationId) {
        if(ObjectUtil.isNull(stationId))
            return null;
        String sql = "SELECT StationName FROM tbl_station WHERE StationId = :stationId";
        return jdbcTemplate.queryForObject(sql, Map.of("stationId", stationId), String.class);
    }

    private static boolean illegalOperationRecord(OperationRecord operationRecord) {
        if (Objects.isNull(operationRecord.getUserId()) || Objects.isNull(operationRecord.getOperation()) || Objects.isNull(operationRecord.getOperationType()) || Objects.isNull(operationRecord.getOperationContent())){
            log.error("记录操作日志异常，非法的参数 {}", operationRecord);
            return true;
        }
        return false;
    }
}
