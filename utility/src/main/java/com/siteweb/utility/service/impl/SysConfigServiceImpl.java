package com.siteweb.utility.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.entity.SysConfig;
import com.siteweb.utility.mapper.SysConfigMapper;
import com.siteweb.utility.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysConfigServiceImpl implements SysConfigService {
    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Override
    public SysConfig findByConfigKey(String configKey){
        return sysConfigMapper.selectOne(Wrappers.lambdaQuery(SysConfig.class)
                                          .eq(SysConfig::getConfigKey, configKey));
    }

    @Override
    public String findValueByConfigKey(String configKey) {
        SysConfig sysConfig = this.findByConfigKey(configKey);
        if (ObjectUtil.isNull(sysConfig)) {
            return null;
        }
        return sysConfig.getConfigValue();
    }
}
