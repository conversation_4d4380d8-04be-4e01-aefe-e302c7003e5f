package com.siteweb.utility.service;

import com.siteweb.utility.dto.EquipmentBaseTypeDTO;
import com.siteweb.utility.entity.EquipmentBaseType;

import java.util.List;

public interface EquipmentBaseTypeService {

    List<EquipmentBaseType> findEquipmentBaseTypes();

    List<EquipmentBaseTypeDTO> findUsedEquipmentBaseTypeDTOs();

    EquipmentBaseType findById(Integer equipmentBaseTypeId);

    EquipmentBaseType findExtFieldByEquipmentBaseTypeId(Integer baseEquipmentId);

    List<EquipmentBaseTypeDTO> findByEquipmentIds(List<Integer> equipmentIds);
}

