package com.siteweb.utility.service;

import com.siteweb.utility.entity.ResourceStructureType;

import java.util.List;

public interface ResourceStructureTypeService {
    /**
     * 查询层级树表所用的节点类型
     * @return {@link List}<{@link ResourceStructureType}>
     */
    List<ResourceStructureType> findResourceStructureObjectTypes();

    List<ResourceStructureType> findResourceStructureObjectTypes(Integer sceneId);

    /**
     * 获取已启用的资源类型
     * @return {@link List}<{@link ResourceStructureType}>
     */
    List<ResourceStructureType> findUsedStructureObjectType();
}
