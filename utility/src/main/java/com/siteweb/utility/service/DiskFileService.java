package com.siteweb.utility.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.utility.entity.DiskFile;
import com.siteweb.utility.vo.DiskFileVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

public interface DiskFileService{

    List<DiskFile> findDiskFiles();

    int createDiskFile(DiskFile diskFile);

    int deleteById(Long diskFileId) throws IOException;

    int updateDiskFile(DiskFile diskFile);

    DiskFile findById(Long diskFileId);

    void deleteDiskFilesByFilePathAndFileName(String filePath, String fileName);

    DiskFile getDiskFile(String filePath, String fileName);

    List<DiskFile> getDiskFile(String filePath);

    List<DiskFileVO> setDiskFileDTOList(List<DiskFile> diskFileList);

    void updateDiskFileStatus(String filePath, String fileName);

    DiskFile handleFileUpload(MultipartFile file, Integer operationType, String path);

    void updateDiskFile(String path, Integer operationType);

    List<DiskFile> findByFileIds(Collection<Long> ids);

    Page<DiskFileVO> getDiskFilePage(String filePath, Page<DiskFile> page, String column, boolean asc);

    int deleteByIds(String ids) throws IOException;
}

