package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.dto.DataItemTree;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.mapper.DataItemMapper;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("dataItemService")
public class DataItemServiceImpl implements DataItemService {

    @Autowired
    DataItemMapper dataItemMapper;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;

    @Override
    public List<DataItem> findDataItems() {
        return dataItemMapper.selectList(null);
    }

    @Override
    public int createDataItem(DataItem dataItem) {
        return dataItemMapper.insert(dataItem);
    }

    @Override
    public int deleteById(Integer dataItemId) {
        return dataItemMapper.deleteById(dataItemId);
    }

    @Override
    public int updateDataItem(DataItem dataItem) {
        return dataItemMapper.updateById(dataItem);
    }

    @Override
    public DataItem findById(Integer dataItemId) {
        return dataItemMapper.selectById(dataItemId);
    }

    @Override
    public List<DataItem> findByEntryId(Integer entryId) {
        return dataItemMapper.selectList(new QueryWrapper<DataItem>().eq("EntryId", entryId));
    }

    @Override
    public Integer getCenterId() {
        List<DataItem> dataItemList = this.findByEntryId(62);
        if (!dataItemList.isEmpty()) {
            return dataItemList.get(0)
                               .getItemId();
        }
        return null;
    }

    @Override
    public Integer createDataItem(Integer entryId, String itemValue) {
        //  字典表取的id增加会重复 primaryKeyValueService.getGlobalIdentity("TBL_DataItem", 0)
        Integer maxEntryItemId = this.getMaxEntryItemId();
        Integer maxDataItemId = this.getMaxDataItemId(entryId);
        DataItem dataItem = DataItem.builder()
                                    .entryItemId(maxEntryItemId)
                                    .parentEntryId(0)
                                    .parentItemId(0)
                                    .entryId(entryId)
                                    .itemId(++maxDataItemId)
                                    .itemValue(itemValue)
                                    .isSystem(false)
                                    .isDefault(false)
                                    .enable(true)
                                    .build();
        return dataItemMapper.insert(dataItem);
    }

    @Override
    public DataItem createDataItemAndReturn(Integer entryId, String itemValue) {
        Integer maxEntryItemId = this.getMaxEntryItemId();
        Integer maxDataItemId = this.getMaxDataItemId(entryId);
        DataItem dataItem = DataItem.builder()
                .entryItemId(maxEntryItemId)
                .parentEntryId(0)
                .parentItemId(0)
                .entryId(entryId)
                .itemId(++maxDataItemId)
                .itemValue(itemValue)
                .isSystem(false)
                .isDefault(false)
                .enable(true)
                .build();
        dataItemMapper.insert(dataItem);
        return dataItem;
    }

    private Integer getMaxEntryItemId() {
        Integer maxItemId = dataItemMapper.selectMaxItemId();
        if (Objects.isNull(maxItemId)) {
            return 1;
        }
        return maxItemId + 1;
    }


    private Integer getMaxDataItemId(Integer entryId) {
        Integer maxDataItemId = dataItemMapper.findMaxDataItemIdByEntryId(entryId);
        if (Objects.isNull(maxDataItemId)) {
            return 1;
        }
        return maxDataItemId;
    }

    @Override
    public Integer updateDataItem(Integer entryId, Integer itemId, String itemValue) {
        return dataItemMapper.update(null, Wrappers.lambdaUpdate(DataItem.class)
                                                   .set(DataItem::getItemValue, itemValue)
                                                   .eq(DataItem::getItemId, itemId)
                                                   .eq(DataItem::getEntryId, entryId));
    }

    @Override
    public boolean exists(Integer entryId, String value) {
        return dataItemMapper.exists(Wrappers.lambdaQuery(DataItem.class)
                                             .eq(DataItem::getEntryId, entryId)
                                             .eq(DataItem::getItemValue, value));
    }

    @Override
    public int deleteByEntryIdAndItemId(Integer entryId, Integer itemId) {
        return dataItemMapper.delete(Wrappers.lambdaQuery(DataItem.class)
                                             .eq(DataItem::getEntryId, entryId)
                                             .eq(DataItem::getItemId, itemId));
    }

    @Override
    public DataItem findByEntryIdIdAndItemId(Integer entryId, Integer itemId) {
        return dataItemMapper.selectOne(Wrappers.lambdaQuery(DataItem.class)
                                                .eq(DataItem::getEntryId, entryId)
                                                .eq(DataItem::getItemId, itemId));
    }

    @Override
    public Map<Integer,String> findIdValueMapByEntryId(Integer entryId) {
        List<DataItem> dataItems = dataItemMapper.selectList(Wrappers.lambdaQuery(DataItem.class)
                                                                     .select(DataItem::getItemId, DataItem::getItemValue)
                                                                     .eq(DataItem::getEntryId, entryId));
       return dataItems.stream().collect(Collectors.toMap(DataItem::getItemId,DataItem::getItemValue));
    }

    @Override
    public List<DataItemTree> findDataItemTreeByEntryId(Integer entryId) {
        // 1. 获取所有相关的 DataItem 数据
        List<DataItem> dataItemList = findByEntryId(entryId);

        // 2. 如果列表为空，直接返回空列表
        if (CollUtil.isEmpty(dataItemList)) {
            return Collections.emptyList();
        }

        // 3. 创建 Map 用于快速查找
        Map<Integer, DataItem> itemMap = dataItemList.stream()
                                                     .collect(Collectors.toMap(DataItem::getItemId, item -> item));

        // 4. 找到所有根节点
        List<DataItemTree> roots = new ArrayList<>();
        for (DataItem item : dataItemList) {
            if (Objects.equals(item.getParentItemId(),0)) {
                DataItemTree root = convertToTree(item, itemMap);
                roots.add(root);
            }
        }

        return roots;
    }

    /**
     * 递归地将 DataItem 转换为 DataItemTree，并构建子树
     */
    private DataItemTree convertToTree(DataItem dataItem, Map<Integer, DataItem> itemMap) {
        // 1. 创建当前节点
        DataItemTree treeNode = new DataItemTree(
                dataItem.getParentItemId(),
                dataItem.getItemId(),
                dataItem.getItemValue(),
                new ArrayList<>()
        );

        // 2. 找到所有子节点
        List<DataItem> children = itemMap.values().stream()
                                         .filter(item -> Objects.equals(item.getParentItemId(), dataItem.getItemId()))
                                         .collect(Collectors.toList());

        // 3. 递归处理每个子节点
        for (DataItem child : children) {
            DataItemTree childNode = convertToTree(child, itemMap);
            treeNode.getChildren().add(childNode);
        }

        return treeNode;
    }
}
