package com.siteweb.utility.service.impl;

import com.siteweb.utility.entity.OperationDetail;
import com.siteweb.utility.mapper.OperationDetailMapper;
import com.siteweb.utility.service.OperationDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("operationDetailService")
public class OperationDetailServiceImpl implements OperationDetailService {

    @Autowired
    OperationDetailMapper operationDetailMapper;

    @Override
    public List<OperationDetail> findOperationDetails() {
        return operationDetailMapper.selectList(null);
    }

    @Override
    public int createOperationDetail(OperationDetail operationDetail) {
        return operationDetailMapper.createOperationDetail(operationDetail);
    }

    @Override
    public int batchCreate(List<OperationDetail> operationDetailList) {
        return operationDetailMapper.batchCreate(operationDetailList);
    }

    @Override
    public OperationDetail findById(Long operationDetailId) {
        return operationDetailMapper.selectById(operationDetailId);
    }
}
