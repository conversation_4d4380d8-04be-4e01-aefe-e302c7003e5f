package com.siteweb.utility.service.impl;

import cn.hutool.core.util.StrUtil;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.common.util.EncryptUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.utility.dto.SmsMessageRequestDTO;
import com.siteweb.utility.dto.SmsMessageResponseDTO;
import com.siteweb.utility.dto.SmsUser;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SmsCodeService;
import com.siteweb.utility.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> zhou
 * @description SmsCodeServiceImpl
 * @createTime 2022-07-13 10:22:28
 */
@Service
public class SmsCodeServiceImpl implements SmsCodeService {

    private final Logger log = LoggerFactory.getLogger(SmsCodeServiceImpl.class);

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    JdbcTemplate jdbcTemplate;



    @Override
    public int sendSmsCode(String mobile) {
        int result = 0;
        SystemConfig smsMessageSystemConfig = systemConfigService.findBySystemConfigKey("sms.message.batch.apiURL");
        if (smsMessageSystemConfig == null || smsMessageSystemConfig.getSystemConfigValue().trim().isEmpty()) {
            log.error("SystemConfig key sms.message.batch.apiURL is null");
            return -1;
        }
        SmsMessageRequestDTO smsMessageRequestDTO = new SmsMessageRequestDTO();
        SmsUser smsUser = getSmsUserByLogonId(mobile);
        if (null == smsUser) {
            smsMessageRequestDTO.setTo(mobile);
        } else {
            // 判断是否有手机号 没有抛出异常判断
            if (StrUtil.isBlank(smsUser.getMobile())) {
                // 将结果-2返回给controller进行异常判断
                return -2;
            }
            smsMessageRequestDTO.setTo(smsUser.getMobile());
            mobile = smsUser.getLogonId();
        }
        String smsCode = NumberUtil.getRandomCode(6);
        redisUtil.set("verificationCode" + mobile, smsCode, 600);
        String smsText = getSmsText(smsCode);
        log.info(smsText);
        smsMessageRequestDTO.setText(smsText);
        smsMessageRequestDTO.setSender("admin");
        smsMessageRequestDTO.setSequenceId(UUID.randomUUID().toString());
        smsMessageRequestDTO.setTag("smsCode");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SmsMessageRequestDTO> httpEntity = new HttpEntity<>(smsMessageRequestDTO, httpHeaders);
        SmsMessageResponseDTO smsMessageResponseDTO = null;
        try {
            smsMessageResponseDTO = restTemplate.postForObject(smsMessageSystemConfig.getSystemConfigValue(), httpEntity, SmsMessageResponseDTO.class);
            log.info("sendSmsCode succeed：{} {}", smsMessageRequestDTO.getText(), smsMessageRequestDTO.getSequenceId());
        } catch (RestClientException e) {
            log.error("sendSmsCode failed：{} {} {}", smsMessageRequestDTO.getText(), smsMessageRequestDTO.getSequenceId(), e.getMessage());
        }
        if (smsMessageResponseDTO != null) {
            result = 1;
        }
        return result;
    }

    @Override
    public int checkSmsCode(String mobile, String smsCode) {
        Object redisValue = redisUtil.get("verificationCode" + mobile);
        String smsCodeInRedis = redisValue == null ? "" : redisValue.toString();
        if (smsCode.equals(smsCodeInRedis)) {
            return 1;
        } else {
            return -1;
        }
    }

    @Override
    public int checkSHA256SmsCode(String mobile, String sha256SmsCode) {
        Object redisValue = redisUtil.get("verificationCode" + mobile);
        String smsCodeInRedis = redisValue == null ? "" : EncryptUtil.sha256(redisValue.toString());
        if (sha256SmsCode.equals(smsCodeInRedis)) {
            return 1;
        } else {
            return -1;
        }
    }

    private String getSmsText(String smsCode) {
        return String.format(messageSourceUtil.getMessage("utility.sms.code.template"), smsCode);
    }

    /**
     * 根据用户名获取电话号码
     * 模块 utility 是模块 admin 的依赖所以使用jdbctemplate方式查询用户
     * @param userName
     * @return
     */
    private SmsUser getSmsUserByLogonId(String userName) {
        String sql = "SELECT * FROM tbl_account a LEFT JOIN tbl_employee b ON b.EmployeeId = a.UserId WHERE a.LogonId = ?";
//        sql = String.format(sql,userName);
        List<SmsUser> smsUserList = jdbcTemplate.query(sql, (rs, rowNum) -> {
            SmsUser smsUser = new SmsUser();
            smsUser.setUserName(rs.getString("UserName"));
            smsUser.setLogonId(rs.getString("LogonId"));
            smsUser.setMobile(rs.getString("Mobile"));
            return smsUser;
        },userName);
        if (smsUserList.isEmpty()) {
            return null;
        }
        return smsUserList.get(0);
    }

}
