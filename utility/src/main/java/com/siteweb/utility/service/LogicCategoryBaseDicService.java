package com.siteweb.utility.service;

import com.siteweb.utility.entity.LogicCategoryBaseDic;

import java.util.List;

public interface LogicCategoryBaseDicService{

    List<LogicCategoryBaseDic> findLogicCategoryBaseDics();

    int createLogicCategoryBaseDic(LogicCategoryBaseDic logicCategoryBaseDic);

    int deleteById(Integer logicCategoryBaseDicId);

    int updateLogicCategoryBaseDic(LogicCategoryBaseDic logicCategoryBaseDic);

    LogicCategoryBaseDic findById(Integer logicCategoryBaseDicId);
}

