package com.siteweb.utility.service;

import com.siteweb.utility.vo.FileItemResponse;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

public interface StorageService {
    void init();

    void store(MultipartFile file, String uuid);

    void store(MultipartFile file, String uuid, String rename);

    Path load(String filename , String uuid);

    Resource loadAsResource(String filename , String uuid);

    List<Resource> loadFolderAsResources(String uuid) throws IOException;

    void deleteAll();

    boolean deleteFile(String filename , String uuid) throws IOException;

    boolean deleteDirectoryItems(String uuid) throws IOException;

    FileItemResponse getFileList(String uuid) throws IOException;

    boolean deleteDir(File dir) throws IOException;

    boolean createDir(String dirName) throws IOException;

    boolean checkFileExits(String filename, String path);

    byte[] resourceToByte(Resource resource);
}
