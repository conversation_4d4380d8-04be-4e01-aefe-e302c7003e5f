package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.enums.SystemConfigTypeEnum;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.manager.SystemConfigManager;
import com.siteweb.utility.mapper.SystemConfigMapper;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.vo.AuditReportVo;
import com.siteweb.utility.vo.SystemConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@DependsOn("liquibase")
public class SystemConfigServiceImpl implements SystemConfigService, InitializingBean {
    private static final int SIGNAL_MAX_COUNT = 2000;
    /**
     *  安全日志登录配置
     */
    private static Set<String> LOGIN_CONFIG = Set.of("max.try.login.count","login.freeze.time","password.effective.days","password.history.save.counts","max.concurrentLogin.user.count","account.login.timeSpan.enable","login.ip.filterpolicy.enable","security.violence.login.duration");
    /**
     * 审计日志配置
     */
    private static Set<String> AUDIT_CONFIG = Set.of("report.auditreport.enable","report.auditreport.level","report.auditreport.maxcount");

    public String tokenHeader;
    @Value("${appjsonfile:/home/<USER>/assets/json/app.json}")
    private String AppJsonPath;
    @Autowired
    SystemConfigMapper systemConfigMapper;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    SystemConfigManager systemConfigManager;
    @Override
    public SystemConfig findBySystemConfigKey(String systemConfigKey) {
        return systemConfigMapper.selectOne(new QueryWrapper<SystemConfig>().eq("SystemConfigKey", systemConfigKey));
    }

    public List<SystemConfig> findBySystemConfigKeys(List<String> systemConfigKeys) {
        if (CollUtil.isEmpty(systemConfigKeys)) {
            return Collections.emptyList();
        }
        return systemConfigMapper.selectList(new QueryWrapper<SystemConfig>().in("SystemConfigKey", systemConfigKeys));
    }

    @Override
    public List<SystemConfig> findBySystemConfigList(SystemConfigVO systemConfigVO) {
        return systemConfigMapper.findSystemConfigList(systemConfigVO);
    }

    @Override
    public Integer updateSystemConfig(SystemConfig systemConfig) {
        int count = systemConfigMapper.updateById(systemConfig);
        systemConfigManager.refreshSystemConfig();
        return count;
    }

    @Override
    public int createSystemConfig(SystemConfig systemConfig) {
        systemConfig.setSystemConfigType(SystemConfigTypeEnum.CUSTOM.getSystemConfigType());
        int count = systemConfigMapper.insert(systemConfig);
        systemConfigManager.refreshSystemConfig();
        return count;
    }

    @Override
    public int deleteById(Integer systemConfigId) {
        return systemConfigMapper.deleteById(systemConfigId);
    }

    @Override
    public SystemConfig findById(Integer systemConfigId) {
        return systemConfigMapper.selectById(systemConfigId);
    }

    @Override
    public List<SystemConfig> findSystemConfigByNotInType(String notInSystemConfigType) {
        if (CharSequenceUtil.isEmpty(notInSystemConfigType)) {
            throw new BusinessException(HttpStatus.NOT_FOUND.getReasonPhrase());
        }
        List<Integer> typeIds = StringUtils.splitToIntegerList(notInSystemConfigType);
        if (CollUtil.isEmpty(typeIds)) {
            throw new BusinessException(HttpStatus.NOT_FOUND.getReasonPhrase());
        }
        return systemConfigMapper.selectList(Wrappers.<SystemConfig>lambdaQuery().notIn(SystemConfig::getSystemConfigType, typeIds));
    }

    @Override
    public int batchUpdateSystemconfig(List<SystemConfig> systemConfigs) {
        this.recordAuditReport(systemConfigs);
        int result = 0;
        for (SystemConfig s : systemConfigs) {
            int i = systemConfigMapper.updateById(s);
            result += i;
        }
        systemConfigManager.refreshSystemConfig();
        return result;
    }

    @Override
    public Integer updateSystemValueByKey(String systemConfigKey, String systemConfigValue) {
        return systemConfigMapper.update(null, Wrappers.lambdaUpdate(SystemConfig.class)
                                                       .set(SystemConfig::getSystemConfigValue, systemConfigValue)
                                                       .eq(SystemConfig::getSystemConfigKey, systemConfigKey));
    }

    @Override
    public boolean updateAppJson(String jsonStr) {
        boolean exist = FileUtil.exist(AppJsonPath);
        if (!exist) {
            return false;
        }
        JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
        FileUtil.writeUtf8String(jsonObject.toString(), AppJsonPath);
        return true;
    }

    @Override
    public String findAppJsonValue(String key) {
        boolean exist = FileUtil.exist(AppJsonPath);
        if (!exist) {
            return null;
        }
        String s = FileUtil.readUtf8String(AppJsonPath);
        if (StrUtil.isBlank(s)) {
            return null;
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONUtil.parseObj(s);
        } catch (JSONException e) {
            // 错误的json格式
            log.error("app.json Format error.[err={}]", e.getMessage());
            return null;
        }
        return jsonObject.get(key, String.class);
    }

    @Override
    public List<SystemConfig> findSystemConfigByType(String systemConfigType) {
        List<Integer> systemConfigTypeList = StringUtils.splitToIntegerList(systemConfigType);
        if (CollUtil.isEmpty(systemConfigTypeList)) {
            return Collections.emptyList();
        }
        return systemConfigMapper.selectList(Wrappers.lambdaQuery(SystemConfig.class)
                                                     .in(SystemConfig::getSystemConfigType, systemConfigTypeList));
    }

    @Override
    public List<SystemConfig> findSystemConfigByKey(String systemConfigKey) {
        List<Integer> systemConfigKeyList = StringUtils.splitToIntegerList(systemConfigKey);
        if (CollUtil.isEmpty(systemConfigKeyList)) {
            return Collections.emptyList();
        }
        return systemConfigMapper.selectList(Wrappers.lambdaQuery(SystemConfig.class)
                                                     .in(SystemConfig::getSystemConfigKey, systemConfigKeyList));
    }

    @Override
    public List<SystemConfig> findUnClassifiedSystemConfig() {
        return this.findSystemConfigByNotInType(CollUtil.join(SystemConfigTypeEnum.getClassifiedSystemConfigType(), ","));
    }

    @Override
    public String findTokenHeader() {
        return tokenHeader;
    }

    @Override
    public String findAccountTerminalDeviceBindEnable() {
        SystemConfig systemConfig = this.findBySystemConfigKey(SystemConfigEnum.ACCOUNT_TERMINAL_DEVICE_BIND_ENABLE.getSystemConfigKey());
        if (Objects.isNull(systemConfig) || CharSequenceUtil.isBlank(systemConfig.getSystemConfigValue())) {
            return "false";
        }
        return systemConfig.getSystemConfigValue();
    }

    @Override
    public int signalMaxCount() {
        SystemConfig systemConfig = this.findBySystemConfigKey(SystemConfigEnum.REPORT_HISTORY_SIGNAL_MAX_COUNT.getSystemConfigKey());
        if (Objects.isNull(systemConfig) || CharSequenceUtil.isBlank(systemConfig.getSystemConfigValue())) {
            return SIGNAL_MAX_COUNT;
        }
        return Integer.parseInt(systemConfig.getSystemConfigValue());
    }

    @Override
    public boolean standardAlarmNameIdIsNotNull() {
        SystemConfig systemConfig = this.findBySystemConfigKey(SystemConfigEnum.S2_GRAPHIC_PAGE_STANDARD_ALARM_NAME.getSystemConfigKey());
        if (Objects.isNull(systemConfig) ) {
            return false;
        }
        return Boolean.parseBoolean(systemConfig.getSystemConfigValue());
    }

    /**
     * 记录安全配置的审计日志(由于循环依赖问题故单独写一方法)
     * @param systemConfigs
     */
    public void recordAuditReport(List<SystemConfig> systemConfigs){
        SystemConfig systemConfigKey = this.findBySystemConfigKey("report.auditreport.enable");
        if (Objects.isNull(systemConfigKey) || !Boolean.parseBoolean(systemConfigKey.getSystemConfigValue())) {
            return;
        }
        List<String> configKeys = systemConfigs.stream()
                                               .map(SystemConfig::getSystemConfigKey)
                                               .toList();
        AuditReportVo auditReportVo = null;
        List<SystemConfig> originList = null;
        for (String key : configKeys) {
            if (LOGIN_CONFIG.contains(key)) {
                //记录安全日志登录配置审计
                originList = this.findBySystemConfigKeys(new ArrayList<>(LOGIN_CONFIG));
                auditReportVo = new AuditReportVo(1,messageSourceUtil.getMessage("audit.report.loginSetting"));
                break;
            }
            if (AUDIT_CONFIG.contains(key)) {
                //审计日志配置审计
                originList = this.findBySystemConfigKeys(new ArrayList<>(AUDIT_CONFIG));
                auditReportVo = new AuditReportVo(1,messageSourceUtil.getMessage("audit.report.auditSetting"));
                break;
            }
        }
        if (CollUtil.isEmpty(originList) || Objects.isNull(auditReportVo)) {
            return;
        }
        String diffString = this.diffSystemValue(originList, systemConfigs);
        auditReportVo.setDetails(auditReportVo.getDetails() + diffString);
        //由于在事件发布之后的后续操作都获取不到相关上下文信息，只能将相关上下文信息先保存起来在向下传递
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        auditReportVo.setAuthentication(authentication);
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String clientIp = IpUtil.getIpAddr(request);
        auditReportVo.setClientIp(clientIp);
        auditReportVo.setType(messageSourceUtil.getMessage("audit.report.securitySettingModule"));
        BaseSpringEvent<AuditReportVo> event = new BaseSpringEvent<>(auditReportVo);
        event.setData(auditReportVo);
        applicationContext.publishEvent(event);
    }

    /**
     * 对比两组参数之间哪些系统值被修改了
     * @param originList 源集合
     * @param destList 目标集合
     * @return {@link String}
     */
    private String diffSystemValue(List<SystemConfig> originList, List<SystemConfig> destList) {
        Map<Integer, SystemConfig> destMap = destList.stream()
                                                     .collect(Collectors.toMap(SystemConfig::getSystemConfigId, v -> v));
        StringJoiner sj = new StringJoiner("、");
        for (SystemConfig origin : originList) {
            SystemConfig dest = destMap.get(origin.getSystemConfigId());
            if (Objects.isNull(dest) || Objects.equals(dest.getSystemConfigValue(), origin.getSystemConfigValue())) {
                continue;
            }
            String meanings = this.systemValueMeanings(dest.getSystemConfigKey(), dest.getSystemConfigValue());
            if (CharSequenceUtil.isBlank(meanings)) {
                sj.add(dest.getDescription() + "：" + dest.getSystemConfigValue());
                continue;
            }
            sj.add(meanings);
        }
        return sj.toString();
    }

    /**
     * 解析value含义
     * @return {@link String}
     */
    private String systemValueMeanings(String key, String value) {
        //历史密码不重复次数
        if (Objects.equals(key,"password.history.save.counts")){
            return messageSourceUtil.getMessage("audit.report.historyPasswordCount") + value;
        }
        //登录连接尝试时长
        if (Objects.equals(key,"security.violence.login.duration")) {
            return String.format(messageSourceUtil.getMessage("audit.report.loginTryDuration"), value);
        }
        //登录冻结时间
        if (Objects.equals(key,"login.freeze.time")) {
            return String.format(messageSourceUtil.getMessage("audit.report.loginFreezeTime"), value);
        }
        //最大并发用户数
        if (Objects.equals(key,"max.concurrentLogin.user.count")) {
            value = Objects.equals(value, "0") ? messageSourceUtil.getMessage("audit.report.infinite") : messageSourceUtil.getMessage("audit.report.one");
            return messageSourceUtil.getMessage("audit.report.maxConcurrentUserCount") + value;
        }
        //用户访问时段配置
        if (Objects.equals(key,"account.login.timeSpan.enable")){
            value = Objects.equals(value, "true") ? messageSourceUtil.getMessage("audit.report.active") : messageSourceUtil.getMessage("audit.report.close");
            return messageSourceUtil.getMessage("audit.report.userVisitTimespanSetting") + value;
        }
        //IP段访问时段配置
        if (Objects.equals(key,"login.ip.filterpolicy.enable")){
            value = Objects.equals(value, "true") ? messageSourceUtil.getMessage("audit.report.active") : messageSourceUtil.getMessage("audit.report.close");
            return messageSourceUtil.getMessage("audit.report.ipVisitSetting") + value;
        }
        if (Objects.equals(key,"report.auditreport.enable")){
            value = Objects.equals(value, "true") ? messageSourceUtil.getMessage("audit.report.open") : messageSourceUtil.getMessage("audit.report.close");
            return messageSourceUtil.getMessage("audit.report.enableAuditReport") + value;
        }
        if (Objects.equals(key,"report.auditreport.level")){
            StringJoiner sj = new StringJoiner(",");
            if (CharSequenceUtil.contains(value,"1")) {
                //最小级
                sj.add(messageSourceUtil.getMessage("audit.report.minLevel"));
            }
            if (CharSequenceUtil.contains(value,"2")) {
                //基本级
                sj.add(messageSourceUtil.getMessage("audit.report.basicLevel"));
            }
            if (CharSequenceUtil.contains(value,"3")) {
                //详细级
                sj.add(messageSourceUtil.getMessage("audit.report.detailLevel"));
            }
            if (CharSequenceUtil.contains(value,"4")) {
                //未规定
                sj.add(messageSourceUtil.getMessage("audit.report.notStated"));
            }
            return messageSourceUtil.getMessage("audit.report.auditReportLevel") + sj;
        }
        return value;
    }

    @Override
    public boolean findBooleanValue(SystemConfigEnum systemConfigEnum) {
        SystemConfig systemConfig = findBySystemConfigKey(systemConfigEnum.getSystemConfigKey());
        if (Objects.isNull(systemConfig)) {
            return false;
        }
        return Boolean.parseBoolean(systemConfig.getSystemConfigValue());
    }

    @Override
    public Integer findIntegerValue(SystemConfigEnum systemConfigEnum) {
        SystemConfig systemConfig = findBySystemConfigKey(systemConfigEnum.getSystemConfigKey());
        if (Objects.isNull(systemConfig) || CharSequenceUtil.isBlank(systemConfig.getSystemConfigValue())) {
            return 0;
        }
        return Integer.valueOf(systemConfig.getSystemConfigValue());
    }

    @Override
    public void afterPropertiesSet(){
        SystemConfig systemConfig = this.findBySystemConfigKey(SystemConfigEnum.TOKEN_HEADER.getSystemConfigKey());
        if (Objects.isNull(systemConfig)) {
            tokenHeader = GlobalConstants.TOKEN_HEADER;
            return;
        }
        tokenHeader = systemConfig.getSystemConfigValue();
    }
}
