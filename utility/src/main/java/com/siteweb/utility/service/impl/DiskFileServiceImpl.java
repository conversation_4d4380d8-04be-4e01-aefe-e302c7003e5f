package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.exception.FileExistException;
import com.siteweb.common.exception.FileTypeIllegalException;
import com.siteweb.common.util.SortUtil;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.entity.DiskFile;
import com.siteweb.utility.entity.DiskFileOperationTypeEnum;
import com.siteweb.utility.mapper.DiskFileMapper;
import com.siteweb.utility.service.DiskFileService;
import com.siteweb.utility.service.StorageService;
import com.siteweb.utility.vo.DiskFileVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Stream;

@Service("diskFileService")
public class DiskFileServiceImpl implements DiskFileService {
    private final Logger log = LoggerFactory.getLogger(DiskFileServiceImpl.class);

    @Autowired
    private DiskFileMapper diskFileMapper;

    @Autowired
    StorageService storageService;

    @Autowired
    FileServerConfig fileServerConfig;

    private static final int TEMPORARY_FILE = 0;

    private static final int COVERAGE_SAME_NAME = 1;

    private static final int INQUIRY_COVERAGE = 2;

    private String pathDelimiter = "/";

    @Value("${fileserver.blackFileTypes:sh,js,exe}")
    private String[] blackFileTypes;
    @Override
    public List<DiskFile> findDiskFiles() {
        return diskFileMapper.selectList(null);
    }

    @Override
    public int createDiskFile(DiskFile diskFile) {
        return diskFileMapper.insert(diskFile);
    }

    @Override
    public int deleteById(Long diskFileId) throws IOException {
        DiskFile diskFile = diskFileMapper.selectById(diskFileId);
        if (ObjectUtil.isNull(diskFile)) {
            return 0;
        }
        int result = diskFileMapper.deleteById(diskFileId);
        storageService.deleteFile(diskFile.getFileName(), diskFile.getFilePath());
        return result;
    }

    @Override
    public int updateDiskFile(DiskFile diskFile) {
        return diskFileMapper.updateById(diskFile);
    }

    @Override
    public DiskFile findById(Long diskFileId) {
        return diskFileMapper.selectById(diskFileId);
    }


    public void deleteDiskFilesByFilePathAndFileName(String filePath, String fileName) {
        try {
            storageService.deleteFile(fileName, filePath);
        } catch (IOException e) {
            log.error("error occur when diskfile deleteDiskFilesByFilePathAndFileName : {}", e.getMessage());
        }
        diskFileMapper.deleteDiskFilesByFilePathAndFileName(filePath, fileName);
    }

    @Override
    public DiskFile getDiskFile(String filePath, String fileName) {
        return diskFileMapper.findFirstByFilePathAndFileName(filePath, fileName);
    }

    @Override
    public List<DiskFile> getDiskFile(String filePath) {
        return diskFileMapper.findByFilePath(filePath);
    }

    @Override
    public List<DiskFileVO> setDiskFileDTOList(List<DiskFile> diskFileList) {
        List<DiskFileVO> diskFileDTOList = new ArrayList<>();
        String rootPath = fileServerConfig.getRootPath();
        for (DiskFile diskFile : diskFileList) {
            DiskFileVO diskFileDTO = new DiskFileVO();
            BeanUtils.copyProperties(diskFile, diskFileDTO);
            long time = System.currentTimeMillis();
            String showPath = "/api/files/" + diskFile.getFilePath() + "/" + diskFile.getFileName() + "?type=inline&version=" + time;
            diskFileDTO.setShowPath(showPath);
            diskFileDTO.setSize(FileUtil.size(new File(rootPath + "/" + diskFile.getFilePath(),diskFile.getFileName())));
            diskFileDTOList.add(diskFileDTO);
        }
        return diskFileDTOList;
    }

    @Override
    public void updateDiskFileStatus(String filePath, String fileName) {
        DiskFile diskFile = diskFileMapper.findFirstByFilePathAndFileName(filePath, fileName);
        if (diskFile != null && diskFile.getStatus() != 1) {
            diskFile.setStatus(1);
            diskFileMapper.update(diskFile, null);
        }
    }

    @Override
    public DiskFile handleFileUpload(MultipartFile file, Integer operationType, String path) {
        if (checkFileType(file)) {
            throw new FileTypeIllegalException("File type Illegal" + file.getOriginalFilename());
        }
        DiskFile diskFile = null;
        switch (operationType == null ? 0 : operationType) {
            case COVERAGE_SAME_NAME://直接覆盖
                storageService.store(file, path);
                diskFile = getDiskFile(path, file.getOriginalFilename());
                if (diskFile == null) {
                    diskFile = new DiskFile(path, file.getOriginalFilename(), new Date(), 1);
                    createDiskFile(diskFile);
                }
                break;
            case INQUIRY_COVERAGE://存在返回409
                diskFile = getDiskFile(path, file.getOriginalFilename());
                if (diskFile == null) {
                    storageService.store(file, path);
                    diskFile = new DiskFile(path, file.getOriginalFilename(), new Date(), 0);
                    createDiskFile(diskFile);
                } else {
                    throw new FileExistException(file.getOriginalFilename() + ":文件已存在");
                }
                break;
            default:
                storageService.store(file, path);
                diskFile = new DiskFile(path, file.getOriginalFilename(), new Date(), 0);
                createDiskFile(diskFile);
                break;
        }

        return diskFile;
    }

    /**
     * 检查文件类型是否合法
     * @param file
     * @return
     */
    private boolean checkFileType(MultipartFile file) {
        // 获取文件类型
        String fileType = null;
        try {
            fileType = FileTypeUtil.getType(file.getInputStream(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("handleFileUpload is err.[err{}]", e.getMessage());
        }
        // 获取yml文件后缀白名单 不在白名单内则禁止上传
        return !fileServerConfig.getWhiteList().contains(fileType);
    }

    public void updateDiskFile(String path, Integer operationType) {
        StringBuilder filePath = new StringBuilder();
        String fileName = "";
        if (!path.contains("/api/files/")) {
            return;
        }
        String[] filePathString = path.split("/api/files/")[1].split(pathDelimiter);
        int length = filePathString.length;
        if (length > 1) {
            fileName = filePathString[length - 1];
            filePath.append(filePathString[0]);
        }
        for (int i = 1; i < length - 1; i++) {
            filePath.append(pathDelimiter + filePathString[i]);
        }
        DiskFileOperationTypeEnum diskFileOperationTypeEnum = DiskFileOperationTypeEnum.valueOf(operationType);
        switch (diskFileOperationTypeEnum) {
            case CLEAN_OLD_DISKFILE:
                deleteDiskFilesByFilePathAndFileName(filePath.toString(), fileName);
                break;
            case UPDATE_DISKFILE_STATUS:
                updateDiskFileStatus(filePath.toString(), fileName);
                break;
            default:
                break;
        }
    }

    @Override
    public List<DiskFile> findByFileIds(Collection<Long> ids) {
        return diskFileMapper.selectList(Wrappers.lambdaQuery(DiskFile.class).in(DiskFile::getFileId, ids));
    }

    @Override
    public Page<DiskFileVO> getDiskFilePage(String filePath, Page<DiskFile> page, String column, boolean asc) {
        List<DiskFile> diskFile = this.getDiskFile(filePath);
        SortUtil<DiskFileVO> sortUtil = new SortUtil<>();
        List<DiskFileVO> diskFileVOS = this.setDiskFileDTOList(diskFile)
                                           .stream()
                                           .sorted((o1, o2) -> sortUtil.sortByTypeAndField(o1, o2, column, asc))
                                           .skip((page.getCurrent() - 1) * page.getSize())
                                           .limit(page.getSize())
                                           .toList();
        Page<DiskFileVO> diskFileVOPage = new Page<>(page.getCurrent(), page.getSize(), CollUtil.size(diskFile));
        diskFileVOPage.setRecords(diskFileVOS);
        return diskFileVOPage;
    }

    @Override
    public int deleteByIds(String ids) throws IOException {
        if (CharSequenceUtil.isBlank(ids)) {
            return 0;
        }
        List<Integer> idList = Stream.of(ids.split(","))
                                     .map(Integer::valueOf)
                                     .toList();
        List<DiskFile> diskFiles = diskFileMapper.selectBatchIds(idList);
        diskFileMapper.deleteBatchIds(idList);
        for (DiskFile diskFile : diskFiles) {
            storageService.deleteFile(diskFile.getFileName(), diskFile.getFilePath());
        }
        return idList.size();
    }


    public void cleanDiskFile() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        List<DiskFile> diskFiles = diskFileMapper.findByStatusAndCreateTimeBefore(TEMPORARY_FILE, calendar.getTime());
        if (diskFiles == null) {
            return;
        }
        for (DiskFile diskFile : diskFiles) {
            try {
                storageService.deleteFile(diskFile.getFileName(), diskFile.getFilePath());
            } catch (IOException e) {
                log.error("cleanDiskFile : {}", e.getMessage());
            }
            diskFileMapper.deleteById(diskFile.getFileId());
        }
    }
}
