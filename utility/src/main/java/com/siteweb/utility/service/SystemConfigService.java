package com.siteweb.utility.service;

import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.vo.SystemConfigVO;

import java.util.List;

public interface SystemConfigService {

    SystemConfig findBySystemConfigKey(String systemConfigKey);

    List<SystemConfig> findBySystemConfigKeys(List<String> systemConfigKey);

    List<SystemConfig> findBySystemConfigList(SystemConfigVO systemConfigVO);

    Integer updateSystemConfig(SystemConfig systemConfig);

    int createSystemConfig(SystemConfig systemConfig);

    SystemConfig findById(Integer systemConfigId);

    int deleteById(Integer systemConfigId);

    List<SystemConfig> findSystemConfigByNotInType(String notInSystemConfigType);

    int batchUpdateSystemconfig(List<SystemConfig> systemConfigs);

    /**
     * 通过SystemConfigKey直接更新SystemConfigValue
     * @param systemConfigKey 系统参数键
     * @param systemConfigValue 系统参数值
     * @return {@link Integer}
     */
    Integer updateSystemValueByKey(String systemConfigKey, String systemConfigValue);

    /**
     * 修改本地文件app.json
     * 备注：本地开发前端自己手动修改文件内容，
     * 生产环境基于容器部署，前端发起post请求，后端通过宿主机映射路径到容器进行修改
     */
    boolean updateAppJson(String jsonStr);

    /**
     * 查找APP.json根据key获取value
     * @param key
     * @return
     */
    String findAppJsonValue(String key);

    List<SystemConfig> findSystemConfigByType(String systemConfigType);

    List<SystemConfig> findSystemConfigByKey(String systemConfigKey);

    /**
     * 获取未分类的系统参数
     * 【没被“业务模块配置”页面整理的系统参数,需求要求“系统参数”页面只展示未被“业务模块配置”页面整理的系统参数】
     * @return {@link List}<{@link SystemConfig}>
     */
    List<SystemConfig> findUnClassifiedSystemConfig();

    /**
     * 获取自定义token的key
     * @return {@link SystemConfig}
     */
    String findTokenHeader();

    String findAccountTerminalDeviceBindEnable();

    int signalMaxCount();
    /**
     * 电信场景组态页中的告警相关接口是否只统计标准化告警
     * @return boolean true是 false 否
     */
    boolean standardAlarmNameIdIsNotNull();

    boolean findBooleanValue(SystemConfigEnum systemConfigEnum);

    Integer findIntegerValue(SystemConfigEnum systemConfigEnum);
}
