package com.siteweb.utility.service.impl;

import com.siteweb.utility.entity.BaseUnit;
import com.siteweb.utility.mapper.BaseUnitMapper;
import com.siteweb.utility.service.BaseUnitService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/4/25 19:29
 */
@Service
@RequiredArgsConstructor
public class BaseUnitServiceImpl implements BaseUnitService {

    private final BaseUnitMapper baseUnitMapper;

    @Override
    public List<BaseUnit> findAll() {
        return baseUnitMapper.selectList(null);
    }
}
