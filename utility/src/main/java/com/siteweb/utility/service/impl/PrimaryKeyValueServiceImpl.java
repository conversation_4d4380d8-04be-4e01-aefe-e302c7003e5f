package com.siteweb.utility.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.PrimaryKeyIdentity;
import com.siteweb.utility.entity.PrimaryKeyValue;
import com.siteweb.utility.mapper.DataItemMapper;
import com.siteweb.utility.mapper.PrimaryKeyIdentityMapper;
import com.siteweb.utility.mapper.PrimaryKeyValueMapper;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class PrimaryKeyValueServiceImpl implements PrimaryKeyValueService {

    @Autowired
    PrimaryKeyValueMapper primaryKeyValueMapper;

    @Autowired
    PrimaryKeyIdentityMapper primaryKeyIdentityMapper;

    @Autowired
    DataItemMapper dataItemMapper;

    @Override
    @Transactional
    public int getGlobalIdentity(String tableName, int postalCode) {
        if (CharSequenceUtil.isBlank(tableName)) {
            return -1;
        }
        if (postalCode == 0) {
            DataItem centerIdDataItem = dataItemMapper.selectOne(new QueryWrapper<DataItem>().eq("EntryId", 62));
            if (null != centerIdDataItem) {
                postalCode = centerIdDataItem.getItemId();
            }
        }
        PrimaryKeyIdentity primaryKeyIdentity = primaryKeyIdentityMapper.selectOne(new QueryWrapper<PrimaryKeyIdentity>().eq("TableName", tableName));
        if (null == primaryKeyIdentity) {
            return -2;
        }
        if ("tbl_dataitem".equalsIgnoreCase(tableName)) {
            postalCode = 0;
        }
        PrimaryKeyValue primaryKeyValue = primaryKeyValueMapper.selectOne(new QueryWrapper<PrimaryKeyValue>().eq("TableId", primaryKeyIdentity.getTableId())
                                                                                                             .eq("PostalCode", postalCode));
        if (primaryKeyValue == null) {
            primaryKeyValue = new PrimaryKeyValue();
            primaryKeyValue.setTableId(primaryKeyIdentity.getTableId());
            primaryKeyValue.setPostalCode(postalCode);
            primaryKeyValue.setMinValue(1);
            primaryKeyValue.setCurrentValue(1);
            primaryKeyValueMapper.insert(primaryKeyValue);
        } else {
            primaryKeyValue.setCurrentValue(primaryKeyValue.getCurrentValue() + 1);
            primaryKeyValueMapper.update(null, Wrappers.lambdaUpdate(PrimaryKeyValue.class)
                                                       .set(PrimaryKeyValue::getCurrentValue, primaryKeyValue.getCurrentValue())
                                                       .eq(PrimaryKeyValue::getTableId, primaryKeyValue.getTableId())
                                                       .eq(PrimaryKeyValue::getPostalCode, primaryKeyValue.getPostalCode()));
        }
        return postalCode * GlobalConstants.ID_GENERATION_BASE + primaryKeyValue.getCurrentValue();
    }
}
