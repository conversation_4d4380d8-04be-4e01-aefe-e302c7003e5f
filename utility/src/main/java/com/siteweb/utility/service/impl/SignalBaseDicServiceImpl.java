package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.utility.dto.SignalBaseDicCategoryDTO;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.mapper.SignalBaseDicMapper;
import com.siteweb.utility.service.SignalBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("signalBaseDicService")
public class SignalBaseDicServiceImpl implements SignalBaseDicService {

    @Autowired
    SignalBaseDicMapper signalBaseDicMapper;

    @Override
    public List<SignalBaseDic> findSignalBaseDics() {
        return signalBaseDicMapper.selectList(null);
    }

    @Override
    public SignalBaseDic findById(Long signalBaseDicId) {
        return signalBaseDicMapper.selectById(signalBaseDicId);
    }

    @Override
    public List<SignalBaseDic> findByIds(List<Long> signalBaseDicIds) {
        return signalBaseDicMapper.selectBatchIds(signalBaseDicIds);
    }

    @Override
    public Map<Long, String> getIdNameMap(List<Long> baseTypeIds) {
        if (CollUtil.isEmpty(baseTypeIds)) {
            return Map.of();
        }
        List<SignalBaseDic> signalBaseDicList = this.findByIds(baseTypeIds);
        return signalBaseDicList.stream().collect(
                Collectors.toMap(SignalBaseDic::getBaseTypeId, SignalBaseDic::getBaseTypeName));
    }

    @Override
    public List<SignalBaseDicDTO> findByBaseEquipmentId(Integer baseEquipmentId) {
        List<SignalBaseDicDTO> result = new ArrayList<>();
        List<SignalBaseDic> eventBaseDics = signalBaseDicMapper.selectList(new QueryWrapper<SignalBaseDic>().eq("BaseEquipmentId", baseEquipmentId));
        Map<Long, Integer> baseTypeCategoryMap = this.getBaseTypeSignalMap(eventBaseDics.stream().map(SignalBaseDic::getBaseTypeId).toList());
        for (SignalBaseDic eventBaseDic : eventBaseDics) {
            SignalBaseDicDTO signalBaseDicDTO = new SignalBaseDicDTO(eventBaseDic);
            signalBaseDicDTO.setSignalCategory(baseTypeCategoryMap.get(signalBaseDicDTO.getBaseTypeId()));
            result.add(signalBaseDicDTO);
        }
        return result;
    }

    @Override
    public List<SignalBaseDicDTO> findBranchSignalByBaseEquipmentId(Integer baseEquipmentId) {
        List<SignalBaseDicDTO> result = new ArrayList<>();
        List<SignalBaseDic> eventBaseDics = signalBaseDicMapper.findBranchSignalByBaseEquipmentId(baseEquipmentId);
        Map<Long, Integer> baseTypeCategoryMap = this.getBaseTypeSignalMap(eventBaseDics.stream().map(SignalBaseDic::getBaseTypeId).toList());
        for (SignalBaseDic eventBaseDic : eventBaseDics) {
            SignalBaseDicDTO signalBaseDicDTO = new SignalBaseDicDTO(eventBaseDic);
            signalBaseDicDTO.setSignalCategory(baseTypeCategoryMap.get(signalBaseDicDTO.getBaseTypeId()));
            result.add(signalBaseDicDTO);
        }
        return result;
    }

    @Override
    public List<SignalBaseDicDTO> findSignalBaseDicByBaseEquipmentIds(String baseEquipmentIds) {
        List<SignalBaseDicDTO> result = new ArrayList<>();
        List<Integer> baseEquipmentIdList = Arrays.stream(baseEquipmentIds.split(",")).map(Integer::parseInt).toList();
        List<SignalBaseDic> eventBaseDics = signalBaseDicMapper.selectList(new QueryWrapper<SignalBaseDic>().in("BaseEquipmentId", baseEquipmentIdList));
        Map<Long, Integer> baseTypeCategoryMap = this.getBaseTypeSignalMap(eventBaseDics.stream().map(SignalBaseDic::getBaseTypeId).toList());
        for (SignalBaseDic eventBaseDic : eventBaseDics) {
            SignalBaseDicDTO signalBaseDicDTO = new SignalBaseDicDTO(eventBaseDic);
            signalBaseDicDTO.setSignalCategory(baseTypeCategoryMap.get(signalBaseDicDTO.getBaseTypeId()));
            result.add(signalBaseDicDTO);
        }
        return result;
    }

    @Override
    public List<SignalBaseDicDTO> findSignalBaseDicByEquipmentId(Integer equipmentId) {
        List<SignalBaseDicDTO> result = new ArrayList<>();
        List<SignalBaseDic> eventBaseDics = signalBaseDicMapper.findSignalBaseDicByEquipmentId(equipmentId);
        for (SignalBaseDic eventBaseDic : eventBaseDics) {
            result.add(new SignalBaseDicDTO(eventBaseDic));
        }
        return result;
    }

    @Override
    public List<SignalBaseDicCategoryDTO> findSignalBaseCategory(List<Long> baseTypeIds) {
        if (CollUtil.isEmpty(baseTypeIds)) {
            return Collections.emptyList();
        }
        return signalBaseDicMapper.findSignalBaseCategory(baseTypeIds);
    }

    @Override
    public List<SignalBaseDicDTO> findByBaseEquipmentIdAndEquipmentIds(Integer baseEquipmentId, Set<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return signalBaseDicMapper.findByBaseEquipmentIdAndEquipmentIds(baseEquipmentId, equipmentIds);
    }

    public Map<Long, Integer> getBaseTypeSignalMap(List<Long> baseTypeIds){
        return this.findSignalBaseCategory(baseTypeIds)
                   .stream()
                   .collect(Collectors.toMap(SignalBaseDicCategoryDTO::getBaseTypeId, SignalBaseDicCategoryDTO::getSignalCategory, (v1, v2) -> v1));
    }
}
