package com.siteweb.utility.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.entity.SceneStructure;
import com.siteweb.utility.mapper.SceneStructureMapper;
import com.siteweb.utility.service.SceneStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("sceneStructureService")
public class SceneStructureServiceImpl implements SceneStructureService {

    @Autowired
    private SceneStructureMapper sceneStructureMapper;

    @Override
    public List<SceneStructure> findSceneStructures() {
        return sceneStructureMapper.selectList(Wrappers.emptyWrapper());
    }
}
