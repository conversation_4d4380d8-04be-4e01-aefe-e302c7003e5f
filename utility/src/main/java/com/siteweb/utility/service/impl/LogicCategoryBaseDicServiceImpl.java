package com.siteweb.utility.service.impl;

import com.siteweb.utility.entity.LogicCategoryBaseDic;
import com.siteweb.utility.mapper.LogicCategoryBaseDicMapper;
import com.siteweb.utility.service.LogicCategoryBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("logicCategoryBaseDicService")
public class LogicCategoryBaseDicServiceImpl implements LogicCategoryBaseDicService {

    @Autowired
    private LogicCategoryBaseDicMapper logicCategoryBaseDicMapper;

    @Override
    public List<LogicCategoryBaseDic> findLogicCategoryBaseDics() {
        return logicCategoryBaseDicMapper.selectList(null);
    }

    @Override
    public int createLogicCategoryBaseDic(LogicCategoryBaseDic logicCategoryBaseDic) {
        return logicCategoryBaseDicMapper.insert(logicCategoryBaseDic);
    }

    @Override
    public int deleteById(Integer logicCategoryBaseDicId) {
        return    logicCategoryBaseDicMapper.deleteById(logicCategoryBaseDicId);
    }

    @Override
    public int updateLogicCategoryBaseDic(LogicCategoryBaseDic logicCategoryBaseDic) {
        return logicCategoryBaseDicMapper.updateById(logicCategoryBaseDic);
    }

    @Override
    public LogicCategoryBaseDic findById(Integer logicCategoryBaseDicId) {
        return logicCategoryBaseDicMapper.selectById(logicCategoryBaseDicId);
    }
}
