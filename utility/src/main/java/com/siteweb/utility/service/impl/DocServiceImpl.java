package com.siteweb.utility.service.impl;

import cn.hutool.core.io.FileUtil;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.dto.DownLoadFileDTO;
import com.siteweb.utility.service.DocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;

@Service
public class DocServiceImpl implements DocService {
    @Autowired
    FileServerConfig fileServerConfig;

    @Override
    public Resource downloadDoc(DownLoadFileDTO downLoadFileDTO) throws MalformedURLException {
        String filePath = fileServerConfig.getRootPath() + FileUtil.FILE_SEPARATOR + downLoadFileDTO.getPath() + FileUtil.FILE_SEPARATOR + downLoadFileDTO.getName();
        Path rootLocation = Paths.get(filePath);
        return new UrlResource(rootLocation.toUri());
    }
}
