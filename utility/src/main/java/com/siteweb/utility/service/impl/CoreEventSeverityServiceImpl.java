package com.siteweb.utility.service.impl;

import com.siteweb.common.exception.BusinessException;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.mapper.CoreEventSeverityMapper;
import com.siteweb.utility.service.CoreEventSeverityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("CoreEventSeverityService")
public class CoreEventSeverityServiceImpl implements CoreEventSeverityService {

    @Autowired
    CoreEventSeverityMapper coreEventSeverityMapper;

    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    private Boolean eventLevelFilterEnable;

    @Override
    public List<CoreEventSeverity> getCoreEventSeverities() {
        if (!Boolean.TRUE.equals(eventLevelFilterEnable)) {
            // 后台告警等级过滤开关未启动,按照原来的格式返回
            return coreEventSeverityMapper.getCoreEventSeverities();
        }
        // 开关打开，默认只返回告警等级打开的数据
        List<CoreEventSeverity> coreEventSeverities = coreEventSeverityMapper.getCoreEventSeverities();
        return coreEventSeverities.stream().filter(f -> Boolean.TRUE.equals(f.getEnable())).toList();
    }

    @Override
    public void updateCoreEventSeverities(List<CoreEventSeverity> coreEventSeverityList) {
        coreEventSeverityMapper.updateCoreEventSeverities(coreEventSeverityList);
    }

    @Override
    public void updateCorePointSeverity(CoreEventSeverity coreEventSeverity) {
        coreEventSeverityMapper.updateCorePointSeverity(coreEventSeverity);
    }

    @Override
    public CoreEventSeverity findByEventSeverity(Integer eventSeverity){
        return coreEventSeverityMapper.findByEventSeverity(eventSeverity);
    }

    @Override
    public void updateCoreEventSeverityEnable(CoreEventSeverity coreEventSeverity) {
        if (!Boolean.TRUE.equals(eventLevelFilterEnable)) {
            throw new BusinessException("后台告警等级过滤开关未启动");
        }
        coreEventSeverityMapper.updateCoreEventSeverityEnable(coreEventSeverity);
    }

    @Override
    public List<CoreEventSeverity> getAllCoreEventSeverities() {
        return coreEventSeverityMapper.getCoreEventSeverities();
    }
}
