package com.siteweb.utility.service;

import com.siteweb.utility.dto.CoreEventSeverity;

import java.util.List;

public interface CoreEventSeverityService {
    List<CoreEventSeverity> getCoreEventSeverities();
    void updateCoreEventSeverities(List<CoreEventSeverity> coreEventSeverityList);
    void updateCorePointSeverity (CoreEventSeverity coreEventSeverity);

    CoreEventSeverity findByEventSeverity(Integer eventSeverity);

    void updateCoreEventSeverityEnable(CoreEventSeverity coreEventSeverity);

    /**
     * 获取所有告警等级
     */
    List<CoreEventSeverity> getAllCoreEventSeverities();
}
