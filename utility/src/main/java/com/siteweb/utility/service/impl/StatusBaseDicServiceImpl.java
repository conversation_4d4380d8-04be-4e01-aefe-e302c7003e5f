package com.siteweb.utility.service.impl;

import com.siteweb.utility.entity.StatusBaseDic;
import com.siteweb.utility.mapper.StatusBaseDicMapper;
import com.siteweb.utility.service.StatusBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("statusBaseDicService")
public class StatusBaseDicServiceImpl implements StatusBaseDicService {

    @Autowired
    private StatusBaseDicMapper statusBaseDicMapper;

    @Override
    public List<StatusBaseDic> findStatusBaseDics() {
        return statusBaseDicMapper.selectList(null);
    }

    @Override
    public int createStatusBaseDic(StatusBaseDic statusBaseDic) {
        return statusBaseDicMapper.insert(statusBaseDic);
    }

    @Override
    public int deleteById(Integer statusBaseDicId) {
        return    statusBaseDicMapper.deleteById(statusBaseDicId);
    }

    @Override
    public int updateStatusBaseDic(StatusBaseDic statusBaseDic) {
        return statusBaseDicMapper.updateById(statusBaseDic);
    }

    @Override
    public StatusBaseDic findById(Integer statusBaseDicId) {
        return statusBaseDicMapper.selectById(statusBaseDicId);
    }
}
