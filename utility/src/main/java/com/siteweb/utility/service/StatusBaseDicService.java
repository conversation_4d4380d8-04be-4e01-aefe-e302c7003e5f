package com.siteweb.utility.service;

import com.siteweb.utility.entity.StatusBaseDic;

import java.util.List;

public interface StatusBaseDicService{

    List<StatusBaseDic> findStatusBaseDics();

    int createStatusBaseDic(StatusBaseDic statusBaseDic);

    int deleteById(Integer statusBaseDicId);

    int updateStatusBaseDic(StatusBaseDic statusBaseDic);

    StatusBaseDic findById(Integer statusBaseDicId);
}

