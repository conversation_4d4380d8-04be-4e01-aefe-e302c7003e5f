package com.siteweb.utility.service.impl;

import cn.hutool.core.util.StrUtil;
import com.siteweb.utility.dto.SmsMessageRequestDTO;
import com.siteweb.utility.dto.SmsMessageResponseDTO;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SmsSecurityService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.UUID;

@Service
@Slf4j
public class SmsSecurityServiceImpl implements SmsSecurityService {


    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    RestTemplate restTemplate;

    @Override
    public int sendSmsSecurity(String mobile, String text) {
        int result = 0;
        SystemConfig smsMessageSystemConfig = systemConfigService.findBySystemConfigKey("sms.message.batch.apiURL");
        if (smsMessageSystemConfig == null || smsMessageSystemConfig.getSystemConfigValue().trim().isEmpty()) {
            log.error("SystemConfig key sms.message.batch.apiURL is null");
            return -1;
        }
        if (StrUtil.isBlank(mobile)) {
            log.error("sendSmsSecurity mobile is null");
            return -1;
        }
        SmsMessageRequestDTO smsMessageRequestDTO = new SmsMessageRequestDTO();
        log.info(text);
        smsMessageRequestDTO.setText(text);
        smsMessageRequestDTO.setSender("admin");
        smsMessageRequestDTO.setTo(mobile);
        smsMessageRequestDTO.setSequenceId(UUID.randomUUID().toString());
        smsMessageRequestDTO.setTag("smsSecurity");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SmsMessageRequestDTO> httpEntity = new HttpEntity<>(smsMessageRequestDTO, httpHeaders);
        SmsMessageResponseDTO smsMessageResponseDTO = null;
        try {
            smsMessageResponseDTO = restTemplate.postForObject(smsMessageSystemConfig.getSystemConfigValue(), httpEntity, SmsMessageResponseDTO.class);
            log.info("sendSmsSecurity succeed：{} {}", smsMessageRequestDTO.getText(), smsMessageRequestDTO.getSequenceId());
        } catch (RestClientException e) {
            log.error("sendSmsSecurity failed：{} {} {}", smsMessageRequestDTO.getText(), smsMessageRequestDTO.getSequenceId(), e.getMessage());
        }
        if (smsMessageResponseDTO != null) {
            result = 1;
        }
        return result;
    }
}
