package com.siteweb.utility.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.dto.EventBaseDicDTO;
import com.siteweb.utility.entity.EventBaseDic;
import com.siteweb.utility.mapper.EventBaseDicMapper;
import com.siteweb.utility.service.EventBaseDicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service("eventBaseDicService")
public class EventBaseDicServiceImpl implements EventBaseDicService {

    @Autowired
    EventBaseDicMapper eventBaseDicMapper;

    @Override
    public List<EventBaseDic> findEventBaseDics() {
        return eventBaseDicMapper.selectList(null);
    }

    @Override
    public List<EventBaseDicDTO> findByBaseEquipmentId(Integer baseEquipmentId) {
        return this.findByBaseEquipmentIds(List.of(baseEquipmentId));
    }

    @Override
    public EventBaseDic findById(Long eventBaseDicId) {
        return eventBaseDicMapper.selectById(eventBaseDicId);
    }

    @Override
    public List<EventBaseDicDTO> findByBaseEquipmentIds(List<Integer> baseEquipmentIdList) {
        if (CollUtil.isEmpty(baseEquipmentIdList)) {
            return Collections.emptyList();
        }
        List<EventBaseDicDTO> result = new ArrayList<>();
        List<EventBaseDic> eventBaseDics = eventBaseDicMapper.selectList(Wrappers.lambdaQuery(EventBaseDic.class)
                                                                                 .select(EventBaseDic::getBaseTypeId, EventBaseDic::getBaseTypeName)
                                                                                 .in(EventBaseDic::getBaseEquipmentId, baseEquipmentIdList));
        for (EventBaseDic eventBaseDic : eventBaseDics) {
            result.add(new EventBaseDicDTO(eventBaseDic));
        }
        return result;
    }
}
