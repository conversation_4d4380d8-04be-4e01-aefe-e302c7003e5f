package com.siteweb.utility.service.impl;

import com.siteweb.utility.entity.ResourceStructureType;
import com.siteweb.utility.manager.ResourceStructureTypeManager;
import com.siteweb.utility.mapper.ResourceStructureTypeMapper;
import com.siteweb.utility.service.ResourceStructureTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ResourceStructureTypeServiceImpl implements ResourceStructureTypeService {
    /**
     * IDC场景
     */
    public static final int SCENE_IDC = 1;
    @Autowired
    ResourceStructureTypeManager resourceStructureTypeManager;
    @Autowired
    ResourceStructureTypeMapper resourceStructureTypeMapper;
    @Override
    public List<ResourceStructureType> findResourceStructureObjectTypes() {
        return resourceStructureTypeManager.findBySceneId(SCENE_IDC);
    }

    @Override
    public List<ResourceStructureType> findResourceStructureObjectTypes(Integer sceneId) {
        return resourceStructureTypeManager.findBySceneId(sceneId);
    }

    @Override
    public List<ResourceStructureType> findUsedStructureObjectType() {
        return resourceStructureTypeMapper.findUsedStructureObjectType();
    }
}
