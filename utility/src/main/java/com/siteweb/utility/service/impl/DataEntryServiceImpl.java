package com.siteweb.utility.service.impl;

import com.siteweb.utility.entity.DataEntry;
import com.siteweb.utility.mapper.DataEntryMapper;
import com.siteweb.utility.service.DataEntryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("dataEntryService")
public class DataEntryServiceImpl implements DataEntryService {

    @Autowired
    private DataEntryMapper dataEntryMapper;

    @Override
    public List<DataEntry> findDataEntrys() {
        return dataEntryMapper.selectList(null);
    }

    @Override
    public int createDataEntry(DataEntry dataEntry) {
        return dataEntryMapper.insert(dataEntry);
    }

    @Override
    public int deleteById(Integer dataEntryId) {
        return    dataEntryMapper.deleteById(dataEntryId);
    }

    @Override
    public int updateDataEntry(DataEntry dataEntry) {
        return dataEntryMapper.updateById(dataEntry);
    }

    @Override
    public DataEntry findById(Integer dataEntryId) {
        return dataEntryMapper.selectById(dataEntryId);
    }
}
