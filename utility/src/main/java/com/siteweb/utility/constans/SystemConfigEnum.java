package com.siteweb.utility.constans;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统参数常量
 * <AUTHOR>
 * @date 2023/03/01
 */
@Getter
@AllArgsConstructor
public enum SystemConfigEnum {
    DEPARTMENT_PERMISSION_ENABLE("departmentPermission.enable","是否开启部门权限"),
    ALARM_VIDEO_WINDOW_SHOW("alarm.video.window.show","告警视频联动开关"),
    SYSTEM_TOKEN_HEADER("system.token.header","系统自定义token的请求头"),
    PASSWORD_EFFECTIVE_DAYS("password.effective.days","密码有效时间（天）"),
    FIVE_MINUTES_STORAGE("fiveMinutes.storage.database","是否默认使用五分钟存储库查询历史信号数据"),
    MAX_CONCURRENT_LOGIN_COUNT("max.concurrentLogin.user.count","最大用户并发数（修改后需重启才生效）"),
    WE_COM_APPLY_API_URL("weCom.apply.apiURL","企业微信应用通知URL"),
    LARK_APPLY_API_URL("lark.apply.apiURL","飞书通知URL"),
    ALARM_LIGHT_MESSAGE_API_URL("alarmlight.message.apiURL","通知网关服务发送告警灯通知URL"),
    MESSAGE_BATCH_API_URL("sms.message.batch.apiURL", "网关服务发送批量短信URL"),
    PHONE_SMS_API_URL("phoneSms.message.apiURL", "网关服务发送电话语音(短信)通知URL"),
    TOKEN_HEADER("system.token.header", "token的自定义header"),
    REPORT_HISTORY_SIGNAL_MAX_COUNT("report.history.signal.maxcount", "限制最大的信号个数"),
    ALARM_BOX_MESSAGE_API_URL("alarmbox.message.apiURL","告警箱通知网关url"),
    ACCOUNT_TERMINAL_DEVICE_BIND_ENABLE("account.terminalDevice.bind.enable","是否开启账号终端绑定功能"),
    S2_GRAPHIC_PAGE_STANDARD_ALARM_NAME("s2.graphicPage.standardAlarmName","电信场景组态页中的告警相关接口是否只统计标准化告警"),
    THEME("theme","系统默认主题"),
    ALARM_CONVERGENCE_POPUP("alarmConvergence.popup.enable","告警收敛是否弹窗(默认关闭)"),
    LOGIN_TOKEN_EXPIRATION("login.token.expiration","登录的token过期时间(小时)"),
    MENU_PERMISSION_ENABLE("menuPermission.enable","是否启用菜单显示权限验证"),
    VERSION_MANAGER_FSU_BACKUP_ENABLE("versionManager.fsuBackup.enable","是否启用备份fsu的home目录功能"),
    // 重庆电信app sso
    CQCTCC_APP_SSO_USERCALLBACKURL("cqctcc.app.sso.userCallbackUrl","重庆电信app getUserCallback方法调用工作助手获取用户信息的URL"),
    CQCTCC_APP_SSO_APPID("cqctcc.app.sso.appId","重庆电信app 工作助手系统为每个集约系统分配的唯一标识"),
    CQCTCC_APP_SSO_KEY("cqctcc.app.sso.key","重庆电信app key由工作助手为每个集约系统分配"),
    // 自动巡检清除时间
    PATROL_TIMERTASKEXPIRED_DAYSEX("patrol.timerTaskExpiredDaysEx","清除自动巡检-异常记录的时间 范围在1-62"),
    PATROL_TIMERTASKEXPIRED_DAYSALL("patrol.timerTaskExpiredDaysAll","清除自动巡检-全量记录的时间 范围在1-365"),
    U_DEVICE_RESTORE_DELAY_SECOND("udevice.restoreDelay.second","查找it设备还原U位设备指示灯时间(秒)");
    ;
    /**
     * 系统配置键
     */
    private final String systemConfigKey;
    /**
     * 描述
     */
    private final String description;
}