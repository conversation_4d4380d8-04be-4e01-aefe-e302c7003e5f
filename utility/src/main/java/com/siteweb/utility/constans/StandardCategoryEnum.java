package com.siteweb.utility.constans;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum StandardCategoryEnum {
    EMR(0, "维谛标准"),
    MOBILE(1, "移动标准"),
    TELECOM(2, "电信标准"),
    UNICOM(3, "联通标准");

    /**
     * 值
     */
    private final int value;
    /**
     * 描述
     */
    private final String describe;

    public static StandardCategoryEnum getByValue(int value) {
        return Arrays.stream(values()).filter(e -> e.value == value).findFirst().orElse(EMR);
    }
}
