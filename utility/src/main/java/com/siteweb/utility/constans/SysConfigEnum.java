package com.siteweb.utility.constans;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SysConfigEnum {
    STANDAR_CATEGORY("StandardCategory", "标准类型"),
    STANDARD_VER("StandardVer", "标准化类型"),
    DATA_SERVICE_CENTER_PORT("DSCPort", "DS中心端口"),
    B_VERSION("BDicVersion","标准化字典版本信息");
    private final String configKey;
    private final String describe;
}
