package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.service.EquipmentBaseTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.Optional;

/**
 * TBL_EquipmentBaseType info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:44:53
 */
@RestController
@RequestMapping("/api")
@Api(value = "EquipmentBaseTypeController", tags = {"EquipmentBaseType操作接口"})
public class EquipmentBaseTypeController {

    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;

    /**
     * GET  /equipmentbasetypes : get the EquipmentBaseTypes.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of EquipmentBaseTypes in body
     */
    @ApiOperation(value = "获取所有EquipmentBaseType实体")
    @GetMapping("/equipmentbasetypes")
    public ResponseEntity<ResponseResult> getEquipmentBaseTypes() {
        return ResponseHelper.successful(equipmentBaseTypeService.findEquipmentBaseTypes(), HttpStatus.OK);
    }

    /**
     * GET  /equipmentbasetypes/used : get the used EquipmentBaseTypes.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of used EquipmentBaseTypes in body
     */
    @ApiOperation(value = "获取所有已启用的EquipmentBaseTypeDTO实体")
    @GetMapping("/equipmentbasetypedtos/used")
    public ResponseEntity<ResponseResult> getUsedEquipmentBaseTypes() {
        return ResponseHelper.successful(equipmentBaseTypeService.findUsedEquipmentBaseTypeDTOs(), HttpStatus.OK);
    }

    /**
     * GET  /equipmentbasetypes/:id  get the EquipmentBaseType by id.
     *
     * @param baseEquipmentId the EquipmentBaseTypeId
     * @return the ResponseEntity with status 200 (OK) and with body the EquipmentBaseType, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个EquipmentBaseType实体")
    @GetMapping("/equipmentbasetypes/{baseEquipmentId}")
    public ResponseEntity<ResponseResult> getEquipmentBaseTypeById(@PathVariable("baseEquipmentId") @ApiParam(name = "baseEquipmentId", value = "唯一ID", required = true) Integer baseEquipmentId) {
        EquipmentBaseType equipmentBaseType = equipmentBaseTypeService.findById(baseEquipmentId);
        return Optional.ofNullable(equipmentBaseType)
                .map(result -> ResponseHelper.successful(equipmentBaseType, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据ID获取单个EquipmentBaseType实体的设备拓展属性")
    @GetMapping("/equipmentbasetypes/extfield/{baseEquipmentId}")
    public ResponseEntity<ResponseResult> getExtFieldByEquipmentBaseTypeId(@PathVariable("baseEquipmentId") @ApiParam(name = "baseEquipmentId", value = "唯一ID", required = true) Integer baseEquipmentId) {
        EquipmentBaseType equipmentBaseType = equipmentBaseTypeService.findExtFieldByEquipmentBaseTypeId(baseEquipmentId);
        if (Objects.isNull(equipmentBaseType)) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.successful(equipmentBaseType.getExtField());
    }

    @ApiOperation(value = "通过设备ids获取其绑定的设备基类")
    @GetMapping(value = "/equipmentbasetypes", params = "equipmentIds")
    public ResponseEntity<ResponseResult> getEquipmentBaseTypeByIdByEquipmentIds(String equipmentIds) {
        return ResponseHelper.successful(equipmentBaseTypeService.findByEquipmentIds(StringUtils.splitToIntegerList(equipmentIds)));
    }
}
