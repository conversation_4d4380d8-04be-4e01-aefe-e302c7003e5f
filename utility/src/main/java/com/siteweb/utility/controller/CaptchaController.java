package com.siteweb.utility.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.service.CaptchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: lzy
 * @Date: 2022/4/21 9:42
 */
@RestController
@RequestMapping("/api/captcha")
@Api(value = "CaptchaController", tags = {"验证码接口"})
@RequiredArgsConstructor
public class CaptchaController {

    private final CaptchaService captchaService;

    @ApiOperation(value = "生成登录验证码")
    @ApiOperationSupport(ignoreParameters = {"areaId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "生成验证码",
                            content = {@Content}),
            })
    @GetMapping(value = "/buildLoginCaptcha", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> buildLoginCaptcha() {
        return ResponseHelper.successful(captchaService.buildLoginCaptcha());
    }

}
