package com.siteweb.utility.controller;


import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.ResourceStructureType;
import com.siteweb.utility.service.ResourceStructureTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "OperationDetailController", tags = {"资源层级操作接口"})
public class ResourceStructureTypeController {

    @Autowired
    private ResourceStructureTypeService resourceStructureTypeService;

    @ApiOperation("查询层级树表所用的节点类型")
    @GetMapping(value = "/resourcestructureobjecttypes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceStructureObjectTypes(@RequestParam(name = "sceneId") Integer sceneId) {
        List<ResourceStructureType> resourceStructureObjectTypes = resourceStructureTypeService.findResourceStructureObjectTypes(sceneId);
        return ResponseHelper.successful(resourceStructureObjectTypes);
    }

    @ApiOperation("获取已启用的资源类型")
    @GetMapping("/resourcestructureobjecttypes/used")
    public ResponseEntity<ResponseResult> getUsedStructureObjectType(){
         return ResponseHelper.successful(resourceStructureTypeService.findUsedStructureObjectType());
    }
}
