package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.LogicCategoryBaseDic;
import com.siteweb.utility.service.LogicCategoryBaseDicService;
import com.siteweb.utility.vo.LogicCategoryBaseDicVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * TBL_LogicCategoryBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:44:53
 */
@RestController
@RequestMapping("/api")
@Api(value="LogicCategoryBaseDicController",tags={"LogicCategoryBaseDic操作接口"})
public class LogicCategoryBaseDicController {

    private static final String ENTITY_NAME = "LogicCategoryBaseDic";

    @Autowired
    private LogicCategoryBaseDicService logicCategoryBaseDicService;

    /**
     * GET  /logiccategorybasedics : get the LogicCategoryBaseDics.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of LogicCategoryBaseDics in body
     */
    @ApiOperation(value="获取所有LogicCategoryBaseDic实体")
    @GetMapping("/logiccategorybasedics" )
    public ResponseEntity<ResponseResult>  getLogicCategoryBaseDics() {
        return  ResponseHelper.successful(logicCategoryBaseDicService.findLogicCategoryBaseDics(), HttpStatus.OK);
    }

    /**
     * GET  /logiccategorybasedics/:id  get the LogicCategoryBaseDic by id.
     *
     * @param  baseLogicCategoryId the LogicCategoryBaseDicId
     * @return the ResponseEntity with status 200 (OK) and with body the LogicCategoryBaseDic, or with status 404 (Not Found)
     */
    @ApiOperation(value="根据ID获取单个LogicCategoryBaseDic实体")
    @GetMapping("/logiccategorybasedics/{baseLogicCategoryId}")
    public ResponseEntity<ResponseResult>  getLogicCategoryBaseDicById(@PathVariable("baseLogicCategoryId" )  @ApiParam(name="baseLogicCategoryId",value="唯一ID",required=true) Integer baseLogicCategoryId) {
        LogicCategoryBaseDic logicCategoryBaseDic =logicCategoryBaseDicService.findById(baseLogicCategoryId);
        return Optional.ofNullable(logicCategoryBaseDic)
                .map(result -> ResponseHelper.successful(logicCategoryBaseDic,  HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /logiccategorybasedics : create a new LogicCategoryBaseDic
     *
     * @param logicCategoryBaseDicVO the LogicCategoryBaseDic to create
     * @return the ResponseEntity with status 201 (Created) and with body the new LogicCategoryBaseDic,
     * or with status 400 (Bad Request) if the LogicCategoryBaseDic has already an ID
     */
    @ApiOperation(value="新增LogicCategoryBaseDic实体")
    @PostMapping(value = "/logiccategorybasedics")
    public ResponseEntity<ResponseResult>  createLogicCategoryBaseDic(@Valid @RequestBody LogicCategoryBaseDicVO logicCategoryBaseDicVO){
        if (logicCategoryBaseDicVO.getBaseLogicCategoryId() != null) {
            return ResponseHelper.failed("-1", "A new LogicCategoryBaseDic  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }

        logicCategoryBaseDicService.createLogicCategoryBaseDic(logicCategoryBaseDicVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * PUT  /logiccategorybasedics : Updates an existing LogicCategoryBaseDic.
     *
     * @param logicCategoryBaseDicVO the LogicCategoryBaseDic to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated LogicCategoryBaseDic,
     * or with status 404 (Not Found) if the logicCategoryBaseDicId is not exists,
     */
    @ApiOperation(value="更新LogicCategoryBaseDic实体")
    @PutMapping(value = "/logiccategorybasedics")
    public ResponseEntity<ResponseResult>  updateLogicCategoryBaseDic(@Valid @RequestBody LogicCategoryBaseDicVO logicCategoryBaseDicVO) {

        if (logicCategoryBaseDicVO.getBaseLogicCategoryId() == null){
            return ResponseHelper.failed("-1", "LogicCategoryBaseDic   Not Found.", HttpStatus.NOT_FOUND);
        }
        logicCategoryBaseDicService.updateLogicCategoryBaseDic(logicCategoryBaseDicVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * DELETE  /logiccategorybasedics/:id : delete the LogicCategoryBaseDic by id.
     *
     * @param id the id of the LogicCategoryBaseDic to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value="根据唯一ID删除LogicCategoryBaseDic实体")
    @DeleteMapping(value = "/logiccategorybasedics/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  deleteLogicCategoryBaseDic(@PathVariable @ApiParam(name="id",value="唯一ID",required=true) Integer id) {

        LogicCategoryBaseDic logicCategoryBaseDic = logicCategoryBaseDicService.findById(id);
        if (logicCategoryBaseDic == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        logicCategoryBaseDicService.deleteById(id);
        return  ResponseHelper.successful(HttpStatus.OK);
    }

}
