package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.OperationDetail;
import com.siteweb.utility.service.OperationDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 详细操作记录表
 *
 * <AUTHOR>
 * @email
 * @date 2022-04-02 13:10:13
 */
@RestController
@RequestMapping("/api")
@Api(value = "OperationDetailController", tags = {"OperationDetail操作接口"})
public class OperationDetailController {

    @Autowired
    OperationDetailService operationDetailService;

    /**
     * GET  /operationdetails : get the OperationDetails.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of OperationDetails in body
     */
    @ApiOperation(value = "获取所有OperationDetail实体")
    @GetMapping("/operationdetails")
    public ResponseEntity<ResponseResult> getOperationDetails() {
        return ResponseHelper.successful(operationDetailService.findOperationDetails(), HttpStatus.OK);
    }

    /**
     * GET  /operationdetails/:id  get the OperationDetail by id.
     *
     * @param id the OperationDetailId
     * @return the ResponseEntity with status 200 (OK) and with body the OperationDetail, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个OperationDetail实体")
    @GetMapping("/operationdetails/{id}")
    public ResponseEntity<ResponseResult> getOperationDetailById(@PathVariable("id") @ApiParam(name = "id", value = "唯一ID", required = true) Long id) {
        OperationDetail operationDetail = operationDetailService.findById(id);
        return Optional.ofNullable(operationDetail)
                .map(result -> ResponseHelper.successful(operationDetail, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

}
