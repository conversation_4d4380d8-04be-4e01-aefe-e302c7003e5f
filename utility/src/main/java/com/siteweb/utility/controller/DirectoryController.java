package com.siteweb.utility.controller;

import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.service.StorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/api")
public class DirectoryController {

    @Autowired
    public StorageService storageService;

    @GetMapping(value = "/directories/{uuid}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>   getItem(@PathVariable String uuid) {
        try {
            return ResponseHelper.successful(storageService.getFileList(uuid), HttpStatus.OK);
        } catch (IOException e) {
            return ResponseHelper.failed(new BusinessException(e.getMessage()));
        }
    }
    @GetMapping(value = "/directories", params = "path", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getFileList(String path) {
        try {
            return ResponseHelper.successful(storageService.getFileList(path), HttpStatus.OK);
        } catch (IOException e) {
            return ResponseHelper.failed(new BusinessException(e.getMessage()));
        }
    }
    @DeleteMapping(value = "/directories/{uuid}")
    public ResponseEntity<ResponseResult> deleteDirectoryItems(@PathVariable String uuid) throws Exception {
        return  ResponseHelper.successful(storageService.deleteDirectoryItems(uuid), HttpStatus.OK);
    }
}

