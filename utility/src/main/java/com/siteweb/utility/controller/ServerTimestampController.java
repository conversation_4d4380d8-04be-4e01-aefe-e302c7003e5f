package com.siteweb.utility.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

/**
 * <AUTHOR> zhou
 * @description ServerTimestampController
 * @createTime 2022-07-22 16:21:15
 */
@RestController
@RequestMapping("/api")
public class ServerTimestampController {

    @GetMapping(value = "/timestamp")
    public String getServerTimestamp() {
        return String.valueOf(Instant.now().getEpochSecond());
    }
}
