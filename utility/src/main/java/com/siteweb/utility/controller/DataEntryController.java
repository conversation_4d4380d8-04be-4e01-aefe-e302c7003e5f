package com.siteweb.utility.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.DataEntry;
import com.siteweb.utility.service.DataEntryService;
import com.siteweb.utility.vo.DataEntryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * Data Dictionary Entry
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 12:41:42
 */
@RestController
@RequestMapping("/api")
@Api(value = "DataEntryController", tags = {"DataEntry操作接口"})
public class DataEntryController {

    @Autowired
    DataEntryService dataEntryService;

    /**
     * GET  /dataentrys : get the DataEntrys.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of DataEntrys in body
     */
    @ApiOperation(value = "获取所有DataEntry实体")
    @GetMapping("/dataentrys")
    public ResponseEntity<ResponseResult> getDataEntrys() {
        return ResponseHelper.successful(dataEntryService.findDataEntrys(), HttpStatus.OK);
    }

    /**
     * GET  /dataentrys/:id  get the DataEntry by id.
     *
     * @param entryId the DataEntryId
     * @return the ResponseEntity with status 200 (OK) and with body the DataEntry, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个DataEntry实体")
    @GetMapping("/dataentrys/{entryId}")
    public ResponseEntity<ResponseResult> getDataEntryById(@PathVariable("entryId") @ApiParam(name = "entryId", value = "唯一ID", required = true) Integer entryId) {
        DataEntry dataEntry = dataEntryService.findById(entryId);
        return Optional.ofNullable(dataEntry)
                .map(result -> ResponseHelper.successful(dataEntry, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /dataentrys : create a new DataEntry
     *
     * @param dataEntryVO the DataEntry to create
     * @return the ResponseEntity with status 201 (Created) and with body the new DataEntry,
     * or with status 400 (Bad Request) if the DataEntry has already an ID
     */
    @ApiOperation(value = "新增DataEntry实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新增成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "EntryId不能为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "新增失败", content = @Content)
            })
    @PostMapping(value = "/dataentrys")
    public ResponseEntity<ResponseResult> createDataEntry(@Valid @RequestBody DataEntryVO dataEntryVO) {
        if (dataEntryVO.getEntryId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EntryId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = dataEntryService.createDataEntry(dataEntryVO.build());
        if (result > 0) {
            return ResponseHelper.successful(dataEntryVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create dataEntry error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT  /dataentrys : Updates an existing DataEntry.
     *
     * @param dataEntryVO the DataEntry to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated DataEntry,
     * or with status 404 (Not Found) if the dataEntryId is not exists,
     */
    @ApiOperation(value = "更新DataEntry实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "修改成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "EntryItemId不能为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "修改失败", content = @Content)
            })
    @PutMapping(value = "/dataentrys")
    public ResponseEntity<ResponseResult> updateDataEntry(@Valid @RequestBody DataEntryVO dataEntryVO) {
        if (dataEntryVO.getEntryId() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EntryId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = dataEntryService.updateDataEntry(dataEntryVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update dataEntry error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * DELETE  /dataentrys/:id : delete the DataEntry by id.
     *
     * @param id the id of the DataEntry to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据唯一ID删除DataEntry实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "找不到DataEntry实体", content = @Content)
            })
    @DeleteMapping(value = "/dataentrys/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDataEntry(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {
        DataEntry dataEntry = dataEntryService.findById(id);
        if (dataEntry == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        dataEntryService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

}
