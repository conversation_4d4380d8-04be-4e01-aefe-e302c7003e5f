package com.siteweb.utility.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.service.SmsCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zhou
 * @description SmsCodeController
 * @createTime 2022-07-13 10:37:11
 */
@RestController
@RequestMapping("/api")
@Api(value = "SmsCodeController", tags = {"SmsCode操作接口"})
public class SmsCodeController {

    @Autowired
    SmsCodeService smsCodeService;

    @ApiOperation(value = "根据手机号或帐号发送短信验证码")
    @PostMapping(value = "/sendsmscode")
    public ResponseEntity<ResponseResult> sendMsg(String mobile) {
        if (CharSequenceUtil.isEmpty(mobile)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "Mobile input error",
                    HttpStatus.BAD_REQUEST);
        }
        int result = smsCodeService.sendSmsCode(mobile);
        if (-2 == result) {
            // 传入的参数是帐号，帐号手机号码为空，返回异常code和信息
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_MOBILE_IS_EMPTY.value()),
                    ErrorCode.USER_MOBILE_IS_EMPTY.getReasonPhrase(),
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(result, HttpStatus.OK);
    }

    @ApiOperation(value = "判定手机号和短信验证码是否有效")
    @PostMapping(value = "/checksmscode")
    public ResponseEntity<ResponseResult> checkSmsCode(String mobile, String smsCode) {
        if (CharSequenceUtil.isEmpty(mobile) || CharSequenceUtil.isEmpty(smsCode)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "Mobile or smsCode input error",
                    HttpStatus.BAD_REQUEST);
        }
        int result = smsCodeService.checkSmsCode(mobile, smsCode);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }
}
