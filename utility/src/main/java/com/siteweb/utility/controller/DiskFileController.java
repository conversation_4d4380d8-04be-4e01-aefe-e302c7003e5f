package com.siteweb.utility.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.DiskFile;
import com.siteweb.utility.service.DiskFileService;
import com.siteweb.utility.service.StorageService;
import com.siteweb.utility.vo.DiskFileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * DiskFile info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-03-10 09:21:00
 */
@RestController
@RequestMapping("/api")
@Api(value = "DiskFileController", tags = {"DiskFile操作接口"})
public class DiskFileController {

    private final Logger log = LoggerFactory.getLogger(DiskFileController.class);

    @Autowired
    private StorageService storageService;

    @Autowired
    private DiskFileService diskFileService;

    private static final String FILES = "files/";

    @GetMapping(value = "/files/**/{filename:.+}")
    public ResponseEntity<Resource> serveFile(@PathVariable String filename, HttpServletRequest request, String type) {
        String path = request.getServletPath().split(FILES)[1];
        path = path.substring(0, path.lastIndexOf('/') + 1);

        Resource file = storageService.loadAsResource(filename, path);

        try {
            filename = URLDecoder.decode(filename, "UTF-8");

            filename = new String(filename.getBytes("gbk"), StandardCharsets.ISO_8859_1);
        } catch (UnsupportedEncodingException exception) {
            log.error("serveFile : {}, {}", filename, exception.getMessage());
        }

        String headerValue = "attachment; filename=" + filename;//下载
        if (type != null && type.equals("inline")) {
            headerValue = "inline";//网页显示
        }
        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, headerValue)
                .body(file);
    }

    @GetMapping(value = "/files", params = "diskFileId")
    public ResponseEntity<Resource> getFileById(@RequestParam Long diskFileId) {
        DiskFile diskFile = diskFileService.findById(diskFileId);
        Resource file = storageService.loadAsResource(diskFile.getFileName(), diskFile.getFilePath());
        String filename = "";
        try {
            filename = URLDecoder.decode(filename, "UTF-8");

            filename = new String(filename.getBytes("gbk"), StandardCharsets.ISO_8859_1);
        } catch (UnsupportedEncodingException exception) {
            log.error("serveFile : {}, {}", filename, exception.getMessage());
        }

        String headerValue = "attachment; filename=" + filename;
        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, headerValue)
                .body(file);

    }

    @PostMapping(value = "/files/**/{filename:.+}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> handleFileUpload(@RequestParam("file") MultipartFile file,
                                                           Integer operationType, HttpServletRequest request) {
        String path = request.getServletPath().split(FILES)[1];

        return ResponseHelper.successful(diskFileService.handleFileUpload(file, operationType, path), HttpStatus.OK);
    }

    @DeleteMapping(value = "/files/**/{filename:.+}")
    public ResponseEntity<ResponseResult> handleFileDelete(@PathVariable String filename,
                                                           HttpServletRequest request) {
        String path = request.getServletPath().split(FILES)[1];
        path = path.substring(0, path.lastIndexOf('/') + 1);

        diskFileService.deleteDiskFilesByFilePathAndFileName(path, filename);
        return ResponseHelper.successful();
    }

    @DeleteMapping(value = "/files/{id}")
    public ResponseEntity<ResponseResult> deleteFileById(@PathVariable Long id) throws IOException {
        return ResponseHelper.successful(diskFileService.deleteById(id));
    }

    @DeleteMapping(value = "/files",params = {"ids"})
    public ResponseEntity<ResponseResult> deleteFileById(String ids) throws IOException {
        return ResponseHelper.successful(diskFileService.deleteByIds(ids));
    }

    @GetMapping(value = "/files", params = {"filePath", "fileName"})
    public ResponseEntity<ResponseResult> getFileById(@RequestParam String filePath, @RequestParam String fileName) {
        DiskFile diskFile = diskFileService.getDiskFile(filePath, fileName);
        if (diskFile == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        } else {
            return ResponseHelper.successful(diskFile);
        }

    }

    @GetMapping(value = "/files", params = {"filePath"})
    public ResponseEntity<ResponseResult> getFileById(@RequestParam String filePath) {
        List<DiskFile> diskFileList = diskFileService.getDiskFile(filePath);
        List<DiskFileVO> diskFileDTOList = diskFileService.setDiskFileDTOList(diskFileList);

        if (diskFileDTOList == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        } else {
            return ResponseHelper.successful(diskFileDTOList);
        }
    }

    @ApiOperation("查询文件夹下的所有文件（分页）")
    @GetMapping(value = "/filespage", params = {"filePath"})
    public ResponseEntity<ResponseResult> getFileByPathPage(@RequestParam String filePath, Page<DiskFile> page, String column, boolean asc) {
        Page<DiskFileVO> diskFileList = diskFileService.getDiskFilePage(filePath, page, column, asc);
        if (CollUtil.isEmpty(diskFileList.getRecords())) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        } else {
            return ResponseHelper.successful(diskFileList);
        }
    }

}
