package com.siteweb.utility.controller;

import com.siteweb.utility.entity.EmailMessage;
import com.siteweb.utility.enums.EmailOperationResponse;
import com.siteweb.utility.manager.EmailMessageManager;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: lzy
 * @Date: 2022/6/20 14:43
 */
@Api(tags = "邮件消息接口")
@RestController
@RequestMapping("/api")
public class EmailMessageController {

    @Autowired
    EmailMessageManager emailMessageManager;

    @PostMapping("/sendmail")
    public EmailOperationResponse sendMail(@RequestBody EmailMessage emailMessage) {
        if (emailMessage == null) {
            return null;
        }else {
            emailMessage.setSource("api");
            return emailMessageManager.sendMail(emailMessage);
        }
    }

}
