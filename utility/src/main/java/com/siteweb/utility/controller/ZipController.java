package com.siteweb.utility.controller;

import com.siteweb.utility.service.ZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class ZipController {

    @Autowired
    ZipService zipService;

    @GetMapping(value = "/unZips", params = {"uuid","zipName"})
    public boolean unZip(@RequestParam String uuid , @RequestParam String zipName) {
        return zipService.unZip(uuid,zipName);
    }

}