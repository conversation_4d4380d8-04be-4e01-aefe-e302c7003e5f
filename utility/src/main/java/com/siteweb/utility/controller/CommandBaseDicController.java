package com.siteweb.utility.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.dto.CommandBaseDicDTO;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.CommandBaseDic;
import com.siteweb.utility.service.CommandBaseDicService;
import com.siteweb.utility.vo.CommandBaseDicVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * TBL_CommandBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:44:54
 */
@RestController
@RequestMapping("/api")
@Api(value="CommandBaseDicController",tags={"CommandBaseDic操作接口"})
public class CommandBaseDicController {

    private static final String ENTITY_NAME = "CommandBaseDic";

    @Autowired
    private CommandBaseDicService commandBaseDicService;

    /**
     * GET  /commandbasedics : get the CommandBaseDics.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of CommandBaseDics in body
     */
    @ApiOperation(value="获取所有CommandBaseDic实体")
    @GetMapping("/commandbasedics" )
    public ResponseEntity<ResponseResult>  getCommandBaseDics() {
        return  ResponseHelper.successful(commandBaseDicService.findCommandBaseDics(), HttpStatus.OK);
    }

    /**
     * GET  /commandbasedics/:id  get the CommandBaseDic by id.
     *
     * @param  baseTypeId the CommandBaseDicId
     * @return the ResponseEntity with status 200 (OK) and with body the CommandBaseDic, or with status 404 (Not Found)
     */
    @ApiOperation(value="根据ID获取单个CommandBaseDic实体")
    @GetMapping("/commandbasedics/{baseTypeId}")
    public ResponseEntity<ResponseResult>  getCommandBaseDicById(@PathVariable("baseTypeId" )  @ApiParam(name="baseTypeId",value="唯一ID",required=true) Long baseTypeId) {
        CommandBaseDic commandBaseDic =commandBaseDicService.findById(baseTypeId);
        return Optional.ofNullable(commandBaseDic)
                .map(result -> ResponseHelper.successful(commandBaseDic,  HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /commandbasedics : create a new CommandBaseDic
     *
     * @param commandBaseDicVO the CommandBaseDic to create
     * @return the ResponseEntity with status 201 (Created) and with body the new CommandBaseDic,
     * or with status 400 (Bad Request) if the CommandBaseDic has already an ID
     */
    @ApiOperation(value="新增CommandBaseDic实体")
    @PostMapping(value = "/commandbasedics")
    public ResponseEntity<ResponseResult>  createCommandBaseDic(@Valid @RequestBody CommandBaseDicVO commandBaseDicVO){
        if (commandBaseDicVO.getBaseTypeId() != null) {
            return ResponseHelper.failed("-1", "A new CommandBaseDic  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }

        commandBaseDicService.createCommandBaseDic(commandBaseDicVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }



    /**
     * GET  /commandbasedics : get the 根据基类设备类型查询CommandBaseDic By BaseEquipmentId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of CommandBaseDicDTO in body
     */
    @ApiOperation(value = "根据基类设备类型查询CommandBaseDic")
    @GetMapping(value = "/commandbasedics", params = {"baseEquipmentId"})
    public ResponseEntity<ResponseResult> getControlBaseDicByBaseEquipmentId(@RequestParam(value = "baseEquipmentId") Integer baseEquipmentId) {
        if (null == baseEquipmentId) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "baseEquipmentId is null",
                    HttpStatus.BAD_REQUEST);
        }
        List<CommandBaseDicDTO> signalBaseDicDTOS = commandBaseDicService.findControlBaseDicByBaseEquipmentId(baseEquipmentId);
        return ResponseHelper.successful(signalBaseDicDTOS, HttpStatus.OK);
    }

}
