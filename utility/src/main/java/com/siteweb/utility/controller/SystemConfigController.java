package com.siteweb.utility.controller;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.AESUtil;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.vo.SystemConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * Data Dictionary Entry
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 12:41:42
 */
@RestController
@RequestMapping("/api")
@Api(value = "SystemConfigController", tags = {"系统参数操作接口"})
public class SystemConfigController {

    @Autowired
    AESUtil aesUtil;

    @Value("${aes-key}")
    private String aesKey;

    @Autowired
    private SystemConfigService systemConfigService;

    @ApiOperation(value = "获取所有系统配置实体分页")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有系统配置分页实体",
                            content = {@Content})
            })
    @GetMapping("/systemconfigs")
    public ResponseEntity<ResponseResult> getSystemConfigs(SystemConfigVO systemConfigVO, @RequestParam(value = "encrypt", required = false) String encrypt) {
        Object configValue = null;
        // 检查是否提供了系统配置键，如果有则根据键查找配置
        if (CharSequenceUtil.isNotBlank(systemConfigVO.getSystemConfigKey())) {
            configValue = systemConfigService.findBySystemConfigKey(systemConfigVO.getSystemConfigKey());
        }
        // 如果未找到，则根据系统配置对象查找配置列表
        if (configValue == null) {
            configValue = systemConfigService.findBySystemConfigList(systemConfigVO);
        }
        // 如果需要加密，则对配置值进行加密
        if (Boolean.parseBoolean(encrypt)) {
            String encryptedValue = aesUtil.encrypt(JSONUtil.toJsonStr(configValue), aesKey);
            return ResponseHelper.successful(encryptedValue);
        }
        // 返回未加密的配置值
        return ResponseHelper.successful(configValue);
    }

    @ApiOperation(value = "获取未分类的系统配置实体")
    @GetMapping("/systemconfigs/unclassified")
    public ResponseEntity<ResponseResult> getUnClassifiedSystemConfig(@RequestParam(value = "encrypt",required = false) String encrypt) {
        List<SystemConfig> systemConfigList = systemConfigService.findUnClassifiedSystemConfig();
        if (Boolean.parseBoolean(encrypt)) {
            return ResponseHelper.successful(aesUtil.encrypt(JSONUtil.toJsonStr(systemConfigList), aesKey));
        }
        return ResponseHelper.successful(systemConfigList);
    }

    @ApiOperation(value = "根据系统配置id查询")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "返回系统配置实体", content = {@Content})})
    @GetMapping("/systemconfigs/{systemConfigId}")
    public ResponseEntity<ResponseResult> findSystemConfigBySystemConfigId(@PathVariable("systemConfigId") Integer systemConfigId,@RequestParam(value = "encrypt",required = false) String encrypt) {
        SystemConfig systemConfig = systemConfigService.findById(systemConfigId);
        if (Boolean.parseBoolean(encrypt)) {
            return ResponseHelper.successful(aesUtil.encrypt(JSONUtil.toJsonStr(systemConfig), aesKey));
        }
        return ResponseHelper.successful(systemConfig);
    }

    @ApiOperation(value = "通过系统参数Key查询系统参数(多个用逗号隔开)")
    @GetMapping(value = "/systemconfigs", params = "systemconfigkey")
    public ResponseEntity<ResponseResult> findSystemConfigByKey(@RequestParam("systemconfigkey") String systemConfigKey,@RequestParam(value = "encrypt",required = false) String encrypt) {
        if (CharSequenceUtil.isBlank(systemConfigKey)) {
            return ResponseHelper.successful(Collections.emptyList());
        }
        List<SystemConfig> systemConfigList = systemConfigService.findSystemConfigByKey(systemConfigKey);

        if (Boolean.parseBoolean(encrypt)) {
            return ResponseHelper.successful(aesUtil.encrypt(JSONUtil.toJsonStr(systemConfigList), aesKey));
        }
        return ResponseHelper.successful(systemConfigList);
    }

    @ApiOperation(value = "通过系统参数类型查询系统参数(多个用逗号隔开)")
    @GetMapping(value = "/systemconfigs", params = "systemconfigtype")
    public ResponseEntity<ResponseResult> findSystemConfigByType(@RequestParam("systemconfigtype") String systemConfigType,@RequestParam(value = "encrypt",required = false) String encrypt) {
        if (CharSequenceUtil.isBlank(systemConfigType)) {
            return ResponseHelper.successful(Collections.emptyList());
        }
        List<SystemConfig> systemConfigList = systemConfigService.findSystemConfigByType(systemConfigType);
        if (Boolean.parseBoolean(encrypt)) {
            return ResponseHelper.successful(aesUtil.encrypt(JSONUtil.toJsonStr(systemConfigList), aesKey));
        }
        return ResponseHelper.successful(systemConfigList);
    }


    @ApiOperation(value = "查询指定排除类型的系统配置")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "返回系统配置实体", content = {@Content})})
    @GetMapping(value = "/systemconfigs", params = "notInSystemConfigType")
    public ResponseEntity<ResponseResult> findSystemConfigByNotInType(@RequestParam("notInSystemConfigType") String notInSystemConfigType,@RequestParam(value = "encrypt",required = false) String encrypt) {
        List<SystemConfig> systemConfigList = systemConfigService.findSystemConfigByNotInType(notInSystemConfigType);
        if (Boolean.parseBoolean(encrypt)) {
            return ResponseHelper.successful(aesUtil.encrypt(JSONUtil.toJsonStr(systemConfigList), aesKey));
        }
        return ResponseHelper.successful(systemConfigList);
    }


    @ApiOperation(value = "修改系统配置实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "系统配置已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "未寻找到需要修改的系统参数",
                            content = @Content)
            })
    @PutMapping(value = "/systemconfigs", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateSystemConfigs(@Valid @RequestBody SystemConfigVO systemConfigVO) {
        if (null == systemConfigVO.getSystemConfigId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "systemConfigId is null",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = systemConfigService.updateSystemConfig(systemConfigVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "systemConfigId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    @ApiOperation(value = "新增SystemConfig实体")
    @PostMapping(value = "/systemconfigs")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新的SystemConfig实体已被创建",
                            content = {@Content})
            })
    public ResponseEntity<ResponseResult> createSystemconfig(@Valid @RequestBody SystemConfigVO systemConfigVO) {
        if (systemConfigVO.getSystemConfigId() != null) {
            return ResponseHelper.failed("-1", "A new Systemconfig  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }
        systemConfigService.createSystemConfig(systemConfigVO.build());
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "根据唯一ID删除SystemConfig实体")
    @DeleteMapping(value = "/systemconfigs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "SystemConfig实体列表被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据SystemConfigId查询不到SystemConfig实体",
                            content = @Content)
            })
    public ResponseEntity<ResponseResult> deleteSystemconfig(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {

        SystemConfig systemConfig = systemConfigService.findById(id);
        if (systemConfig == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        systemConfigService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @ApiOperation(value = "批量修改系统配置实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "系统配置已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "未寻找到需要修改的系统参数",
                            content = @Content)
            })
    @PutMapping(value = "/systemconfigs/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchUpdateSystemConfig(@Valid @RequestBody List<SystemConfig> systemConfigs) {
        if (null == systemConfigs) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "systemConfigvos is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = systemConfigService.batchUpdateSystemconfig(systemConfigs);
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "systemConfigId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }
    @ApiOperation(value = "修改文件app.json")
    @PostMapping("/systemconfig/appjson")
    public ResponseEntity<ResponseResult> updateAppJson(@RequestBody String jsonStr) {
        boolean result = systemConfigService.updateAppJson(jsonStr);
        if (!result) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.APPJSON_NOT_FOUND.value()),
                    ErrorCode.APPJSON_NOT_FOUND.getReasonPhrase(),
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful();
    }

    @ApiOperation("获取自定义token的key")
    @GetMapping("/systemconfig/tokenheader")
    public ResponseEntity<ResponseResult> getTokenHeader(){
        return ResponseHelper.successful(systemConfigService.findTokenHeader());
    }

    @ApiOperation("获取是否开启账号终端绑定功能")
    @GetMapping("/systemconfig/accountterminaldevicebindenable")
    public ResponseEntity<ResponseResult> getAccountTerminalDeviceBindEnable(){
        return ResponseHelper.successful(systemConfigService.findAccountTerminalDeviceBindEnable());
    }
}
