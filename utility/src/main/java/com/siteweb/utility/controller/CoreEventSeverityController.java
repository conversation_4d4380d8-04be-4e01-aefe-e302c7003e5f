package com.siteweb.utility.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.service.CoreEventSeverityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api")
@Api(value = "CoreEventSeverityController", tags = {"告警等级设置接口"})
public class CoreEventSeverityController {
    @Autowired
    CoreEventSeverityService coreEventSeverityService;

    @ApiOperation(value = "获取所有告警等级接口")
    @GetMapping(value = "/coreeventseverities/all",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllCoreEventSeverities() {
        return ResponseHelper.successful(coreEventSeverityService.getAllCoreEventSeverities(), HttpStatus.OK);
    }

    @ApiOperation(value = "获取告警等级接口(返回告警等级打开的数据)")
    @GetMapping(value = "/coreeventseverities",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCoreEventSeverities() {
        return ResponseHelper.successful(coreEventSeverityService.getCoreEventSeverities(), HttpStatus.OK);
    }

    @ApiOperation(value = "告警等级设置接口")
    @PutMapping(value = "/coreeventseverities")
    public ResponseEntity<ResponseResult> updateCorePointSeverity(@Valid @RequestBody CoreEventSeverity coreEventSeverity) {
        if (coreEventSeverity.getEventLevel() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EventSeverityId is null",
                    HttpStatus.BAD_REQUEST);
        }
        coreEventSeverityService.updateCorePointSeverity(coreEventSeverity);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "告警等级状态开关接口")
    @PutMapping(value = "/coreeventseverities/enable")
    public ResponseEntity<ResponseResult> updateCoreEventSeverityEnable(@RequestBody CoreEventSeverity coreEventSeverity) {
        if (coreEventSeverity.getEventLevel() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EventSeverityId is null",
                    HttpStatus.BAD_REQUEST);
        }
        coreEventSeverityService.updateCoreEventSeverityEnable(coreEventSeverity);
        return ResponseHelper.successful(HttpStatus.OK);
    }

}
