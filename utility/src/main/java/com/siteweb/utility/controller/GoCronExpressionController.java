package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.service.GoCronExpressionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: lzy
 * @Date: 2022/4/25 19:33
 */
@RestController
@RequestMapping("/api")
@Api(value = "GoCronExpressionController", tags = {"go Cron表达式控制器"})
@RequiredArgsConstructor
public class GoCronExpressionController {

    private final GoCronExpressionService goCronExpressionService;

    @GetMapping(value = "/gocronexpressions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllGoCronExpressions() {
        return ResponseHelper.successful(goCronExpressionService.findAll());
    }

    @GetMapping(value = "/gocronexpressions", params = {"simpleExpression"})
    public ResponseEntity<ResponseResult> findBySimpleExpression(@RequestParam(value = "simpleExpression") String simpleExpression) {
        return ResponseHelper.successful(goCronExpressionService.findBySimpleExpression(simpleExpression));
    }

}
