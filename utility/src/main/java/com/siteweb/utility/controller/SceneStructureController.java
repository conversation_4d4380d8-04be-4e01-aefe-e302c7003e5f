package com.siteweb.utility.controller;

import com.siteweb.utility.entity.SceneStructure;
import com.siteweb.utility.service.SceneStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2021-03-29 13:20:12
 */
@RestController
@RequestMapping("/api" )
public class SceneStructureController {

    private static final String ENTITY_NAME = "SceneStructure";

    @Autowired
    private SceneStructureService sceneStructureService;

    /**
     * GET  /scenestructures : get the SceneStructures.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of SceneStructures in body
     */
    @GetMapping("/scenestructures" )
    public List<SceneStructure> getSceneStructures() {
        return sceneStructureService.findSceneStructures();
    }

}
