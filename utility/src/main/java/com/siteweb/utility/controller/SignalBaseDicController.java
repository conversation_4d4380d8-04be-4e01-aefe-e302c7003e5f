package com.siteweb.utility.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.dto.SignalBaseDicDTO;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.service.SignalBaseDicService;
import com.siteweb.utility.vo.SignalBaseDicFilterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * TBL_SignalBaseDic info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:44:53
 */
@RestController
@RequestMapping("/api")
@Api(value = "SignalBaseDicController", tags = {"SignalBaseDic操作接口"})
public class SignalBaseDicController {

    @Autowired
    SignalBaseDicService signalBaseDicService;

    /**
     * GET  /signalbasedics : get the SignalBaseDics.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of SignalBaseDics in body
     */
    @ApiOperation(value = "根据条件查询SignalBaseDic实体")
    @GetMapping("/signalbasedics")
    public ResponseEntity<ResponseResult> getSignalBaseDics(SignalBaseDicFilterVO signalBaseDicFilterVO) {
       if (signalBaseDicFilterVO.getBaseEquipmentId() != null && CharSequenceUtil.isNotBlank(signalBaseDicFilterVO.getEquipmentIds())){
           Set<Integer> equipmentIds = StringUtils.splitToIntegerCollection(signalBaseDicFilterVO.getEquipmentIds(),HashSet::new);
           return ResponseHelper.successful(signalBaseDicService.findByBaseEquipmentIdAndEquipmentIds(signalBaseDicFilterVO.getBaseEquipmentId(),equipmentIds));
       }else if (signalBaseDicFilterVO.getBaseEquipmentId() != null) {
            return ResponseHelper.successful(signalBaseDicService.findByBaseEquipmentId(signalBaseDicFilterVO.getBaseEquipmentId()), HttpStatus.OK);
        } else if (signalBaseDicFilterVO.getBaseEquipmentIds() != null) {
            return ResponseHelper.successful(signalBaseDicService.findSignalBaseDicByBaseEquipmentIds(signalBaseDicFilterVO.getBaseEquipmentIds()), HttpStatus.OK);
        } else if (signalBaseDicFilterVO.getEquipmentId() != null) {
            return ResponseHelper.successful(signalBaseDicService.findSignalBaseDicByEquipmentId(signalBaseDicFilterVO.getEquipmentId()), HttpStatus.OK);
        } else {
            return ResponseHelper.successful(signalBaseDicService.findSignalBaseDics(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "获取信号基类类型")
    @PostMapping("/signalbasediccategory")
    public ResponseEntity<ResponseResult> getSignalBaseCategory(@RequestBody List<Long> baseTypeIds){
        return ResponseHelper.successful(signalBaseDicService.findSignalBaseCategory(baseTypeIds));
    }

    /**
     * GET  /signalbasedics/:id  get the SignalBaseDic by id.
     *
     * @param baseTypeId the SignalBaseDicId
     * @return the ResponseEntity with status 200 (OK) and with body the SignalBaseDic, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个SignalBaseDic实体")
    @GetMapping("/signalbasedics/{baseTypeId}")
    public ResponseEntity<ResponseResult> getSignalBaseDicById(@PathVariable("baseTypeId") @ApiParam(name = "baseTypeId", value = "唯一ID", required = true) Long baseTypeId) {
        SignalBaseDic signalBaseDic = signalBaseDicService.findById(baseTypeId);
        return Optional.ofNullable(signalBaseDic)
                .map(result -> ResponseHelper.successful(signalBaseDic, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * GET  /signalbasedics : get the BranchSignalBaseDics By BaseEquipmentId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of SignalBaseDicDTO in body
     */
    @ApiOperation(value = "根据基类设备类型查询支路信号配置")
    @GetMapping(value = "/signalbasedics/multibranch", params = {"baseEquipmentId"})
    public ResponseEntity<ResponseResult> findBranchSignalByBaseEquipmentId(@RequestParam(value = "baseEquipmentId") Integer baseEquipmentId) {
        if (null == baseEquipmentId) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "baseEquipmentId is null",
                    HttpStatus.BAD_REQUEST);
        }
        List<SignalBaseDicDTO> signalBaseDicDTOS = signalBaseDicService.findBranchSignalByBaseEquipmentId(baseEquipmentId);
        return ResponseHelper.successful(signalBaseDicDTOS, HttpStatus.OK);
    }

}
