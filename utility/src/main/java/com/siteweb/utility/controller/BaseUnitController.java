package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.service.BaseUnitService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: lzy
 * @Date: 2022/4/25 19:30
 */
@RestController
@RequestMapping("/api")
@Api(value = "BaseUnitController", tags = {"基本单元控制器"})
@RequiredArgsConstructor
public class BaseUnitController {

    private final BaseUnitService baseUnitService;

    @GetMapping(value = "/baseunits", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllBaseUnits() {
        return ResponseHelper.successful(baseUnitService.findAll());
    }
}
