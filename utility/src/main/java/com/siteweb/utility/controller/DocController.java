package com.siteweb.utility.controller;

import com.siteweb.utility.dto.DownLoadFileDTO;
import com.siteweb.utility.service.DocService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.MalformedURLException;

@RestController
@RequestMapping("/api")
@Api(value = "DocController", tags = {"文档操作接口"})
public class DocController {
    @Autowired
    DocService docService;
    @PostMapping("/doc/download")
    public ResponseEntity<Resource> downloadDoc(@RequestBody DownLoadFileDTO downLoadFileDTO) throws MalformedURLException {
        Resource resource = docService.downloadDoc(downLoadFileDTO);
        // 确保文件存
        if (resource.exists() || resource.isReadable()) {
            return ResponseEntity.ok()
                                 .contentType(MediaType.APPLICATION_OCTET_STREAM) // 设置MIME类型
                                 .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"") // 强制下载
                                 .body(resource);
        }
        return ResponseEntity.notFound().build();
    }
}
