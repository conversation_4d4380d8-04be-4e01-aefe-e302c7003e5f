package com.siteweb.utility.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.dto.EventBaseDicDTO;
import com.siteweb.utility.entity.EventBaseDic;
import com.siteweb.utility.service.EventBaseDicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * TBL_EventBaseDic info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:44:53
 */
@RestController
@RequestMapping("/api")
@Api(value = "EventBaseDicController", tags = {"EventBaseDic操作接口"})
public class EventBaseDicController {

    @Autowired
    EventBaseDicService eventBaseDicService;

    /**
     * GET  /eventbasedics : get the EventBaseDics.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of EventBaseDics in body
     */
    @ApiOperation(value = "查找EventBaseDicDTO实体")
    @GetMapping("/eventbasedicdtos")
    public ResponseEntity<ResponseResult> getEventBaseDicDTOs(@RequestParam(value = "baseEquipmentId") Integer baseEquipmentId) {
        if (null == baseEquipmentId) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "baseEquipmentId is null",
                    HttpStatus.BAD_REQUEST);
        }
        List<EventBaseDicDTO> eventBaseDicDTOs = eventBaseDicService.findByBaseEquipmentId(baseEquipmentId);
        return ResponseHelper.successful(eventBaseDicDTOs, HttpStatus.OK);
    }

    @ApiOperation(value = "查找EventBaseDicDTO实体")
    @GetMapping(value = "/eventbasedicdtos",params = {"baseEquipmentIds"})
    public ResponseEntity<ResponseResult> getEventBaseDicDTOs(@RequestParam(value = "baseEquipmentIds") String baseEquipmentIds) {
        List<EventBaseDicDTO> eventBaseDicDTOs = eventBaseDicService.findByBaseEquipmentIds(StringUtils.splitToIntegerList(baseEquipmentIds));
        return ResponseHelper.successful(eventBaseDicDTOs, HttpStatus.OK);
    }
    /**
     * GET  /eventbasedics/:id  get the EventBaseDic by id.
     *
     * @param baseTypeId the EventBaseDicId
     * @return the ResponseEntity with status 200 (OK) and with body the EventBaseDic, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个EventBaseDic实体")
    @GetMapping("/eventbasedics/{baseTypeId}")
    public ResponseEntity<ResponseResult> getEventBaseDicById(@PathVariable("baseTypeId") @ApiParam(name = "baseTypeId", value = "唯一ID", required = true) Long baseTypeId) {
        EventBaseDic eventBaseDic = eventBaseDicService.findById(baseTypeId);
        return Optional.ofNullable(eventBaseDic)
                .map(result -> ResponseHelper.successful(eventBaseDic, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }


}
