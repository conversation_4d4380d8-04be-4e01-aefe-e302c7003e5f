package com.siteweb.utility.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.vo.DataItemVO;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * tbl_dataitem
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 12:41:42
 */
@RestController
@RequestMapping("/api")
@Api(value = "DataItemController", tags = {"DataItem操作接口"})
public class DataItemController {

    @Autowired
    DataItemService dataItemService;

    /**
     * GET  /dataitems/:id  get the DataItem by id.
     *
     * @param entryItemId the DataItemId
     * @return the ResponseEntity with status 200 (OK) and with body the DataItem, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据ID获取单个DataItem实体")
    @GetMapping("/dataitems/{entryItemId}")
    public ResponseEntity<ResponseResult> getDataItemById(@PathVariable("entryItemId") @ApiParam(name = "entryItemId", value = "唯一ID", required = true) Integer entryItemId) {
        DataItem dataItem = dataItemService.findById(entryItemId);
        return Optional.ofNullable(dataItem)
                .map(result -> ResponseHelper.successful(dataItem, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /dataitems : create a new DataItem
     *
     * @param dataItemVO the DataItem to create
     * @return the ResponseEntity with status 201 (Created) and with body the new DataItem,
     * or with status 400 (Bad Request) if the DataItem has already an ID
     */
    @ApiOperation(value = "新增DataItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新增成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "EntryItemId不能为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "新增失败", content = @Content)
            })
    @PostMapping(value = "/dataitems")
    public ResponseEntity<ResponseResult> createDataItem(@Valid @RequestBody DataItemVO dataItemVO) {
        if (dataItemVO.getEntryItemId() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EntryItemId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = dataItemService.createDataItem(dataItemVO.build());
        if (result > 0) {
            return ResponseHelper.successful(dataItemVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create dataItem error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT  /dataitems : Updates an existing DataItem.
     *
     * @param dataItemVO the DataItem to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated DataItem,
     * or with status 404 (Not Found) if the dataItemId is not exists,
     */
    @ApiOperation(value = "更新DataItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "修改成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "400", description = "EntryItemId不能为null", content = @Content),
                    @ApiResponse(responseCode = "500", description = "修改失败", content = @Content)
            })
    @PutMapping(value = "/dataitems")
    public ResponseEntity<ResponseResult> updateDataItem(@Valid @RequestBody DataItemVO dataItemVO) {
        if (dataItemVO.getEntryItemId() == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "EntryItemId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = dataItemService.updateDataItem(dataItemVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update dataItem error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * DELETE  /dataitems/:id : delete the DataItem by id.
     *
     * @param id the id of the DataItem to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "根据唯一ID删除DataItem实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "删除成功",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "找不到DataItem实体", content = @Content)
            })
    @DeleteMapping(value = "/dataitems/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDataItem(@PathVariable @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {
        DataItem dataItem = dataItemService.findById(id);
        if (dataItem == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        dataItemService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * 查询字典项实体
     *
     * @param entryId 字典ID
     * @return 字典项列表
     */
    @ApiOperation(value = "查询字典项实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "entryId", value = "字典ID", dataType = "Integer", paramType = "query", example = "1")
    })
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "字典项实体",
                            content = {@Content})
            })
    @GetMapping(value = "/dataitems",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDataItems(@RequestParam(value = "entryId", required = false) Integer entryId) {
        if (null == entryId) {
            return ResponseHelper.successful(dataItemService.findDataItems(), HttpStatus.OK);
        } else {
            return ResponseHelper.successful(dataItemService.findByEntryId(entryId), HttpStatus.OK);
        }
    }

    @GetMapping(value = "/dataitems/tree", params = "entryId")
    public ResponseEntity<ResponseResult> getDataItemTree(Integer entryId) {
        return ResponseHelper.successful(dataItemService.findDataItemTreeByEntryId(entryId));
    }
}
