package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.StatusBaseDic;
import com.siteweb.utility.service.StatusBaseDicService;
import com.siteweb.utility.vo.StatusBaseDicVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * TBL_StatusBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-01-18 13:44:53
 */
@RestController
@RequestMapping("/api")
@Api(value="StatusBaseDicController",tags={"StatusBaseDic操作接口"})
public class StatusBaseDicController {

    private static final String ENTITY_NAME = "StatusBaseDic";

    @Autowired
    private StatusBaseDicService statusBaseDicService;

    /**
     * GET  /statusbasedics : get the StatusBaseDics.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of StatusBaseDics in body
     */
    @ApiOperation(value="获取所有StatusBaseDic实体")
    @GetMapping("/statusbasedics" )
    public ResponseEntity<ResponseResult>  getStatusBaseDics() {
        return  ResponseHelper.successful(statusBaseDicService.findStatusBaseDics(), HttpStatus.OK);
    }

    /**
     * GET  /statusbasedics/:id  get the StatusBaseDic by id.
     *
     * @param  id the StatusBaseDicId
     * @return the ResponseEntity with status 200 (OK) and with body the StatusBaseDic, or with status 404 (Not Found)
     */
    @ApiOperation(value="根据ID获取单个StatusBaseDic实体")
    @GetMapping("/statusbasedics/{id}")
    public ResponseEntity<ResponseResult>  getStatusBaseDicById(@PathVariable("id" )  @ApiParam(name="id",value="唯一ID",required=true) Integer id) {
        StatusBaseDic statusBaseDic =statusBaseDicService.findById(id);
        return Optional.ofNullable(statusBaseDic)
                .map(result -> ResponseHelper.successful(statusBaseDic,  HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /statusbasedics : create a new StatusBaseDic
     *
     * @param statusBaseDicVO the StatusBaseDic to create
     * @return the ResponseEntity with status 201 (Created) and with body the new StatusBaseDic,
     * or with status 400 (Bad Request) if the StatusBaseDic has already an ID
     */
    @ApiOperation(value="新增StatusBaseDic实体")
    @PostMapping(value = "/statusbasedics")
    public ResponseEntity<ResponseResult>  createStatusBaseDic(@Valid @RequestBody StatusBaseDicVO statusBaseDicVO){
        if (statusBaseDicVO.getId() != null) {
            return ResponseHelper.failed("-1", "A new StatusBaseDic  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }

        statusBaseDicService.createStatusBaseDic(statusBaseDicVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * PUT  /statusbasedics : Updates an existing StatusBaseDic.
     *
     * @param statusBaseDicVO the StatusBaseDic to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated StatusBaseDic,
     * or with status 404 (Not Found) if the statusBaseDicId is not exists,
     */
    @ApiOperation(value="更新StatusBaseDic实体")
    @PutMapping(value = "/statusbasedics")
    public ResponseEntity<ResponseResult>  updateStatusBaseDic(@Valid @RequestBody StatusBaseDicVO statusBaseDicVO) {

        if (statusBaseDicVO.getId() == null){
            return ResponseHelper.failed("-1", "StatusBaseDic   Not Found.", HttpStatus.NOT_FOUND);
        }
        statusBaseDicService.updateStatusBaseDic(statusBaseDicVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * DELETE  /statusbasedics/:id : delete the StatusBaseDic by id.
     *
     * @param id the id of the StatusBaseDic to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value="根据唯一ID删除StatusBaseDic实体")
    @DeleteMapping(value = "/statusbasedics/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  deleteStatusBaseDic(@PathVariable @ApiParam(name="id",value="唯一ID",required=true) Integer id) {

        StatusBaseDic statusBaseDic = statusBaseDicService.findById(id);
        if (statusBaseDic == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        statusBaseDicService.deleteById(id);
        return  ResponseHelper.successful(HttpStatus.OK);
    }

}
