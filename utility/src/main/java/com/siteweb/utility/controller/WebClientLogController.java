package com.siteweb.utility.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.dto.WebClientLogDTO;
import com.siteweb.utility.service.WebClientLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "WebClientLogController", tags = {"客户端日志操作接口"})
public class WebClientLogController {
    @Autowired
    WebClientLogService webClientLogService;

    @ApiOperation("前端埋点接口")
    @PostMapping("/webclientlog")
    public ResponseEntity<ResponseResult> createWebClientLog(@RequestBody WebClientLogDTO webClientLogDTO) {
        return ResponseHelper.successful(webClientLogService.createWebClientLog(webClientLogDTO));
    }
}
