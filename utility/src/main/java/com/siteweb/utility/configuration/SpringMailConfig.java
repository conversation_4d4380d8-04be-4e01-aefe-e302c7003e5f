package com.siteweb.utility.configuration;

import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * @Author: lzy
 * @Date: 2022/6/20 14:09
 */
@Configuration
public class SpringMailConfig {

    @Autowired
    Environment environment;

    @Autowired
    SystemConfigService systemConfigService;

    public String getSpringMailDebug() {
        return findValueAndDefaultValueByKey("spring.mail.properties.mail.debug");
    }

    public String getSpringMailProtocol() {
        return findValueAndDefaultValueByKey("spring.mail.protocol");
    }

    public String getSpringMailFromAddress() {
        return findValueAndDefaultValueByKey("spring.mail.fromAddress");
    }

    public String getSpringMailHost() {
        return findValueAndDefaultValueByKey("spring.mail.host");
    }

    public Integer getSpringMailPort() {
        return Integer.parseInt(findValueAndDefaultValueByKey("spring.mail.port"));
    }

    public String getSpringMailUsername() {
        return findValueAndDefaultValueByKey("spring.mail.username");
    }

    public String getSpringMailPassword() {
        return findValueAndDefaultValueByKey("spring.mail.password");
    }

    public String getMailSmtpAuth() {
        return findValueAndDefaultValueByKey("spring.mail.properties.mail.smtp.auth");
    }

    public String getMailSmtpStarttlsEnable() {
        return findValueAndDefaultValueByKey("spring.mail.properties.mail.smtp.starttls.enable");
    }

    public String getMailSmtpSslEnable() {
        return findValueAndDefaultValueByKey("spring.mail.properties.mail.smtp.ssl.enable");
    }

    public String getSpringMailDefaultEncoding() {
        return findValueAndDefaultValueByKey("spring.mail.defaultEncoding");
    }

    public String findValueAndDefaultValueByKey(String key) {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(key);
        return systemConfig == null ? environment.getProperty(key) : systemConfig.getSystemConfigValue();
    }
}
