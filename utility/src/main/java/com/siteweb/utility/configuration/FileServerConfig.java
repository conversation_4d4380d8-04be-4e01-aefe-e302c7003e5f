package com.siteweb.utility.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Data
@Configuration
@ConfigurationProperties(prefix = "fileserver")
public class FileServerConfig {
    String rootPath;
    @Value("#{'${fileserver.whiteList:jpeg,jpg,png,gif,bmp,tiff,pdf,doc,xls,xlsx,ppt,pptx,txt,zip,rar,tar,gzip,mp3,wav,mp4,json,xml,ini,avi,glb,obj,gltf,fbx,uv3d,3ds,svg,webp}'.split(',')}")
    Set<String> whiteList = Set.of();
}
