package com.siteweb.utility.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 验证码配置
 *
 * @Author: lzy
 * @Date: 2022/4/21 13:53
 */
@Data
@Component
//@ConfigurationProperties(prefix = "captcha")
public class CaptchaConfig {

    /**
     * 验证码图片长度 默认：130
     */
    @Value("${captcha.width:190}")
    private Integer width;

    /**
     * 验证码图片长度 默认：48
     */
    @Value("${captcha.height:48}")
    private Integer height;

    /**
     * 字符个数 默认：5
     */
    @Value("${captcha.codeCount:5}")
    private Integer codeCount;

    /**
     * 干扰线长度 默认：5
     */
    @Value("${captcha.thickness:5}")
    private Integer thickness;

    /**
     * 验证码图片key：默认: LoginCaptchaImgKey:
     */
    @Value("${captcha.captchaImgKey:LoginCaptchaImgKey:}")
    private String captchaImgKey;

    /**
     * 验证码有效时长(单位秒) 默认：10分钟
     */
    @Value("${captcha.expireTime:600}")
    private Long expireTime;

}
