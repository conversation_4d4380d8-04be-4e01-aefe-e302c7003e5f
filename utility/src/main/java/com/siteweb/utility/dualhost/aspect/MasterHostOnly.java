package com.siteweb.utility.dualhost.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记只有在主机上才执行的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MasterHostOnly {
    /**
     * 可选参数，用于记录日志信息
     */
    String logMessage() default "";
}
