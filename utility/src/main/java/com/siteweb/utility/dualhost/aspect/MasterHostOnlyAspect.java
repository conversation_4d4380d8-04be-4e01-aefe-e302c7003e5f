package com.siteweb.utility.dualhost.aspect;

import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
public class MasterHostOnlyAspect {
    @Autowired
    private HAStatusService haStatusService;

    @Around("@annotation(com.siteweb.utility.dualhost.aspect.MasterHostOnly)")
    public Object checkMasterHost(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解信息
        MasterHostOnly annotation = method.getAnnotation(MasterHostOnly.class);
        String message = annotation.logMessage();

        // 检查是否为主机
        if (!haStatusService.isMasterHost()) {
            // 如果不是主机，记录日志并直接返回
            String logMessage = message.isEmpty() ? "HAStatus is BACKUP, skipping execution of " + method.getName() : message;
            log.info(logMessage);
            return null; // 对于void方法，返回null
        }

        // 如果是主机，则继续执行原方法
        return joinPoint.proceed();
    }
}
