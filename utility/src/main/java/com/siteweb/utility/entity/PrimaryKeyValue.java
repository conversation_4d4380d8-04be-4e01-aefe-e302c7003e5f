package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("tbl_primarykeyvalue")
public class PrimaryKeyValue {

    @TableId(value = "TableId", type = IdType.INPUT)
    private Integer tableId;

    private Integer postalCode;

    private Integer minValue;

    private Integer currentValue;
}
