package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TBL_CommandBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:58:14
 */
@Data
@NoArgsConstructor
@TableName("tbl_CommandBaseDic")
public class CommandBaseDic {

	/**
	 * 
	 */
	 @TableId(value="baseTypeId", type = IdType.INPUT)
	private Long baseTypeId;
	/**
	 * 控制基类名
	 */
	private String baseTypeName;
	/**
	 * 基类设备ID
	 */
	private Integer baseEquipmentId;
	/**
	 * 控制基类名-英文
	 */
	private String englishName;
	/**
	 * 控制逻辑分类
	 */
	private Integer baseLogicCategoryId;
	/**
	 * 控制类型
	 */
	private Integer commandType;
	/**
	 * 基类状态Id
	 */
	private Integer baseStatusId;
	/**
	 * 扩展字段1
	 */
	private String extendField1;
	/**
	 * 扩展字段2
	 */
	private String extendField2;
	/**
	 * 扩展字段3
	 */
	private String extendField3;
	/**
	 * 序号扩展
	 */
	private String baseNameExt;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 是否系统默认
	 */
	private Boolean isSystem;
}
