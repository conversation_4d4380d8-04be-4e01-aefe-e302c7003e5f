package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("webclientlog")
public class WebClientLog {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer webClientLogId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 业务模块
     */
    private String businessModule;
    /**
     * 内容
     */
    private String content;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
