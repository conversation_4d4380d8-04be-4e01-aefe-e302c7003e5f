package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TBL_EventBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:58:14
 */
@Data
@NoArgsConstructor
@TableName("tbl_EventBaseDic")
public class EventBaseDic {

	/**
	 * 
	 */
	 @TableId(value="baseTypeId", type = IdType.INPUT)
	private Long baseTypeId;
	/**
	 * 告警基类名
	 */
	private String baseTypeName;
	/**
	 * 基类设备ID
	 */
	private Integer baseEquipmentId;
	/**
	 * 告警基类名（英文）
	 */
	private String englishName;
	/**
	 * 告警等级ID
	 */
	private Integer eventSeverityId;
	/**
	 * 告警开始值
	 */
	private Double comparedValue;
	/**
	 * 告警逻辑分类
	 */
	private Integer baseLogicCategoryId;
	/**
	 * 告警开始延时
	 */
	private Integer startDelay;
	/**
	 * 告警结束延时
	 */
	private Integer endDelay;
	/**
	 * 扩展信息1
	 */
	private String extendField1;
	/**
	 * 扩展信息2
	 */
	private String extendField2;
	/**
	 * 扩展信息3
	 */
	private String extendField3;
	/**
	 * 扩展信息4
	 */
	private String extendField4;
	/**
	 * 扩展信息5
	 */
	private String extendField5;
	/**
	 * 扩展信息（用在多模块的场合）
	 */
	private String baseNameExt;
	/**
	 * 描述信息
	 */
	private String description;
	/**
	 * 是否为系统
	 */
	private Boolean isSystem;
}
