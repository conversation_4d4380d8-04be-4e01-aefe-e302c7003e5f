package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TBL_EquipmentBaseType info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:58:14
 */
@Data
@NoArgsConstructor
@TableName("tbl_EquipmentBaseType")
public class EquipmentBaseType {

	/**
	 * 
	 */
	 @TableId(value="baseEquipmentId", type = IdType.INPUT)
	private Integer baseEquipmentId;
	/**
	 * 基类设备名
	 */
	private String baseEquipmentName;
	/**
	 * 设备大类ID
	 */
	private Integer equipmentTypeId;
	/**
	 * 设备子类ID
	 */
	private Integer equipmentSubTypeId;
	/**
	 * 描述信息
	 */
	private String description;

	/**
	 * 设备拓展属性
	 */
	private JsonNode extField;
}
