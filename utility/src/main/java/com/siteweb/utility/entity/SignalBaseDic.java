package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TBL_SignalBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-30 13:16:35
 */
@Data
@NoArgsConstructor
@TableName("tbl_SignalBaseDic")
public class SignalBaseDic {

	/**
	 * 
	 */
	 @TableId(value="baseTypeId", type = IdType.INPUT)
	private Long baseTypeId;
	/**
	 * 基类信号名
	 */
	private String baseTypeName;
	/**
	 * 基类设备ID
	 */
	private Integer baseEquipmentId;
	/**
	 * 基类信号名（英文名）
	 */
	private String englishName;
	/**
	 * 信号逻辑分类
	 */
	private Integer baseLogicCategoryId;
	/**
	 * 存储周期
	 */
	private Integer storeInterval;
	/**
	 * 绝对值阈值
	 */
	private Double absValueThreshold;
	/**
	 * 百分比阈值
	 */
	private Double percentThreshold;
	/**
	 * 放电存储周期
	 */
	private Integer storeInterval2;
	/**
	 * 放电存储绝对值阈值
	 */
	private Double absValueThreshold2;
	/**
	 * 百分比存储阈值
	 */
	private Double percentThreshold2;
	/**
	 * 扩展字段1
	 */
	private String extendField1;
	/**
	 * 扩展字段2
	 */
	private String extendField2;
	/**
	 * 扩展字段3
	 */
	private String extendField3;
	/**
	 * 单位
	 */
	private Integer unitId;
	/**
	 * 
	 */
	private Integer baseStatusId;
	/**
	 * 
	 */
	private Double baseHysteresis;
	/**
	 * 
	 */
	private Integer baseFreqPeriod;
	/**
	 * 
	 */
	private Integer baseFreqCount;
	/**
	 * 
	 */
	private String baseShowPrecision;
	/**
	 * 
	 */
	private Integer baseStatPeriod;
	/**
	 * 
	 */
	private String cgElement;
	/**
	 * 
	 */
	private String baseNameExt;
	/**
	 * 
	 */
	private String description;
	/**
	 * 是否为系统
	 */
	private Boolean isSystem;
}
