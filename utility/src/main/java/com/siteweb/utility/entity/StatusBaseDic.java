package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TBL_StatusBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:58:14
 */
@Data
@NoArgsConstructor
@TableName("tbl_StatusBaseDic")
public class StatusBaseDic {

	/**
	 * 
	 */
	 @TableId(value="id", type = IdType.INPUT)
	private Integer id;
	/**
	 * 状态信号Id
	 */
	private Integer baseStatusId;
	/**
	 * 状态信号名
	 */
	private String baseStatusName;
	/**
	 * 基类条件ID
	 */
	private Integer baseCondId;
	/**
	 * 操作符
	 */
	private String operator;
	/**
	 * 状态值
	 */
	private Integer value;
	/**
	 * 状态涵义
	 */
	private String meaning;
	/**
	 * 描述信息
	 */
	private String description;
}
