package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("systemconfig")
public class SystemConfig {
    public SystemConfig(String systemConfigKey, String systemConfigValue) {
        this.systemConfigKey = systemConfigKey;
        this.systemConfigValue = systemConfigValue;
    }

    @TableId(value = "SystemConfigId", type = IdType.AUTO)
    Integer systemConfigId;

    String systemConfigKey;

    String systemConfigValue;

    String description;

    Integer systemConfigType;
}