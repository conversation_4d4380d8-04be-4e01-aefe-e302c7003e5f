package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 详细操作记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-04-02 13:10:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("tbl_operationdetail")
public class OperationDetail {
	/**
	 * 用户ID
	 */
	private Integer userId;

	/**
	 * 操作对象ID
	 */
	private String objectId;

	/**
	 * 操作对象类型
	 */
	private Integer objectType;

	/**
	 * 属性名
	 */
	private String propertyName;

	/**
	 * 操作时间
	 */
	private Date operationTime;

	/**
	 * 操作类型
	 */
	private String operationType;

	/**
	 * 更改前值
	 */
	private String oldValue;

	/**
	 * 更改后值
	 */
	private String newValue;
}
