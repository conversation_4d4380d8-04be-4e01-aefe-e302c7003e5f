package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TBL_LogicCategoryBaseDic info table
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-29 16:58:14
 */
@Data
@NoArgsConstructor
@TableName("tbl_LogicCategoryBaseDic")
public class LogicCategoryBaseDic {

	/**
	 * 
	 */
	 @TableId(value="baseLogicCategoryId", type = IdType.INPUT)
	private Integer baseLogicCategoryId;
	/**
	 * 设备基类ID
	 */
	private Integer baseEquipmentId;
	/**
	 * 告警逻辑大类
	 */
	private Integer baseLogicCategoryType;
	/**
	 * 逻辑分类名
	 */
	private String baseLogicCategoryName;
	/**
	 * 描述信息
	 */
	private String description;
}
