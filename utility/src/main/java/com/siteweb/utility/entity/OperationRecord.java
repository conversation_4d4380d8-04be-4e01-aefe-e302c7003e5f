package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> z<PERSON>
 * @description OperationRecord
 * @createTime 2022-06-25 09:00:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_operationrecord")
public class OperationRecord {

    private Integer userId;

    private Integer stationId;

    private String stationName;

    private Integer operation;

    private Date operationTime;

    private Integer operationType;

    private String operationContent;
}
