package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * The persistent class for the BaseUnit database table.
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("gocronexpression")
public class GoCronExpression implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer expressionId; //单位ID

    private String expression;//表达式

    private String simpleExpression;//简易表达式

    private String expressionDescription;//表达式解释

    private String expressionDescriptionEn;//表达式解释(英文)
}