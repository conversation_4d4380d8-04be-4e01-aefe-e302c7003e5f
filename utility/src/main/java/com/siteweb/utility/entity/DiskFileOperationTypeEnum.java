package com.siteweb.utility.entity;

public enum DiskFileOperationTypeEnum {

    CLEAN_OLD_DISKFILE(1),

    UPDATE_DISKFILE_STATUS(2);

    private int value = 0;

    private DiskFileOperationTypeEnum(int value) {
        this.value = value;
    }

    public static DiskFileOperationTypeEnum valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 1:
                return CLEAN_OLD_DISKFILE;
            case 2:
                return UPDATE_DISKFILE_STATUS;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }
}
