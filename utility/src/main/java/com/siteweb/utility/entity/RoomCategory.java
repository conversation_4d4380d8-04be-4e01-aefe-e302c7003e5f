package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("RoomCategory")
public class RoomCategory {

    @TableId(type = IdType.INPUT)
    private  Integer roomCategoryId;

    private  String  roomCategoryName;

    private  String  color;

}
