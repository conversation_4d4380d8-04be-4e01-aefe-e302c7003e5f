package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 资源对象类型定义
 *
 * @Author: lzy
 * @Date: 2022/5/10 14:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("ResourceStructureType")
public class ResourceStructureType implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer resourceStructureTypeId; //资源ID
    private Integer sceneId; //场景ID
    private String resourceStructureTypeName; //资源名称

    private String description;// 描述信息
}
