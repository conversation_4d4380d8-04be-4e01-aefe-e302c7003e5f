package com.siteweb.utility.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-03-29 13:20:12
 */
@Data
@NoArgsConstructor
@TableName("scenestructure")
public class SceneStructure {

	@TableId(type = IdType.AUTO)
	private Integer sceneStructureId;
	/**
	 *
	 */
	private Integer sceneId;
	/**
	 * 
	 */
	private Integer objectTypeId;
	/**
	 * 
	 */
	private Integer displayIndex;

	@JsonIgnore
	@TableField(exist = false)
	private ResourceStructureType resourceStructureType;

	public String getResourceStructureTypeName() {
		if (resourceStructureType != null) {
			return resourceStructureType.getResourceStructureTypeName();
		}
		return null;
	}
}
