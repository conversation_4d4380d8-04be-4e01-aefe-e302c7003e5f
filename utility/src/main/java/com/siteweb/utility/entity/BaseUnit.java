package com.siteweb.utility.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * The persistent class for the BaseUnit database table.
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("baseunit")
public class BaseUnit implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer baseUnitId; //单位ID

    private String baseUnitName;//标准量名称

    private String baseUnitNameEn;//标准量名称(英文)

    private String baseUnitSymbol;//标准单位符号

    private String baseUnitNameCode;//名称代号

    private String baseUnitSymbolName;//标准单位符号名称

    private String baseUnitNameDescription;//应用举例、说明
}