<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.SignalBaseDicMapper">
    <select id="findBranchSignalByBaseEquipmentId" resultType="com.siteweb.utility.entity.SignalBaseDic">
        SELECT BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, BaseLogicCategoryId, StoreInterval, AbsValueThreshold, PercentThreshold, StoreInterval2, AbsValueThreshold2, PercentThreshold2, ExtendField1, ExtendField2, ExtendField3, UnitId, BaseStatusId, BaseHysteresis, BaseFreqPeriod, BaseFreqCount, BaseShowPrecision, BaseStatPeriod, CGElement, BaseNameExt, Description, IsSystem
        FROM tbl_signalbasedic a
        where a.BaseEquipmentId = #{baseEquipmentId}
        and (a.BaseTypeId % 1000) = 1
        and a.BaseNameExt !='NULL'
        and a.BaseNameExt is not null  ;
    </select>
    <select id="findSignalBaseDicByEquipmentId" resultType="com.siteweb.utility.entity.SignalBaseDic">
        select * from tbl_signalbasedic a
        where a.baseTypeId in(
        select c.baseTypeId from tbl_Equipment b inner join tbl_Signal c on b.EquipmentTemplateId = c.EquipmentTemplateId
        where b.EquipmentId = #{equipmentId})
    </select>
    <select id="findSignalBaseCategory" resultType="com.siteweb.utility.dto.SignalBaseDicCategoryDTO">
        select
        distinct
        a.BaseTypeId as baseTypeId,
        b.SignalCategory as signalCategory
        from tbl_signalbasedic a
        inner join tbl_signal b on a.BaseTypeId = b.BaseTypeId
        where a.baseTypeId in
        <foreach collection="baseTypeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findByBaseEquipmentIdAndEquipmentIds" resultType="com.siteweb.utility.dto.SignalBaseDicDTO">
        SELECT a.BaseTypeId,a.BaseTypeName
        FROM tbl_signalbasedic a
        WHERE a.baseTypeId IN (SELECT c.baseTypeId
        FROM tbl_Equipment b
        INNER JOIN tbl_Signal c ON b.EquipmentTemplateId = c.EquipmentTemplateId
        WHERE b.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>)
        AND a.BaseEquipmentId = #{baseEquipmentId}
    </select>
</mapper>