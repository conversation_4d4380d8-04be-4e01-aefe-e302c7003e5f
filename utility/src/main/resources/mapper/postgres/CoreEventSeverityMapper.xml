<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.CoreEventSeverityMapper">
    <update id="updateCoreEventSeverities" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update tbl_dataitem
            <set>
                ExtendField3=#{item.displayColor}, ItemValue=#{item.severityName}
            </set>
            where ExtendField4  = #{item.eventLevel} and EntryId = 23
        </foreach>
    </update>
    <update id="updateCorePointSeverity">
        update tbl_dataitem set ItemValue=#{severityName}, ExtendField3 = #{displayColor} where cast(ExtendField4 as int) = #{eventLevel} and EntryId = 23
    </update>
    <select id="getCoreEventSeverities" resultType="com.siteweb.utility.dto.CoreEventSeverity">
        select ExtendField4 AS eventLevel, ItemId AS eventSeverity, ItemValue AS severityName, ExtendField3 AS displayColor, "enable" from tbl_dataitem where entryId= 23
        order by eventLevel asc
    </select>
    <select id="findByEventSeverity" resultType="com.siteweb.utility.dto.CoreEventSeverity">
        select ExtendField4 AS eventLevel, ItemId AS eventSeverity, ItemValue AS severityName, ExtendField3 AS displayColor from tbl_dataitem where entryId= 23
        AND ItemId = #{eventSeverity}
    </select>
    <update id="updateCoreEventSeverityEnable">
        update tbl_dataitem set "enable"=#{enable} where cast(ExtendField4 as int) = #{eventLevel} and EntryId = 23
    </update>
</mapper>