<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.PrimaryKeyValueMapper">

    <select id="findStructureIdByStructureType" resultType="java.lang.Integer">
        SELECT StructureId FROM TBL_StationStructure WHERE StructureType = #{structureType}
    </select>
</mapper>