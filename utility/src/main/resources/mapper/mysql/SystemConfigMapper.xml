<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.SystemConfigMapper">
    <insert id="recordAuditReport"></insert>
    <select id="findSystemConfigList" resultType="com.siteweb.utility.entity.SystemConfig">
        SELECT
        SystemConfigId,
        SystemConfigKey,
        SystemConfigValue,
        SystemConfigType,
        Description
        FROM systemconfig
        <where>
            <if test="systemConfigVO.systemConfigKey != null and systemConfigVO.systemConfigKey != ''">
                and SystemConfigKey = #{systemConfigVO.systemConfigKey}
            </if>
            <if test="systemConfigVO.keyWord != null and systemConfigVO.keyWord != ''">
                and (SystemConfigKey like concat('%',#{systemConfigVO.keyWord},'%') OR
                SystemConfigValue like concat('%',#{systemConfigVO.keyWord},'%') OR
                Description like concat('%',#{systemConfigVO.keyWord},'%')
                )
            </if>
        </where>
    </select>
    <select id="findBySystemConfigKey" resultType="com.siteweb.utility.entity.SystemConfig">
        SELECT
        SystemConfigId,
        SystemConfigKey,
        SystemConfigValue,
        SystemConfigType,
        Description
        FROM systemconfig where SystemConfigKey = #{systemConfigKey}
    </select>
</mapper>