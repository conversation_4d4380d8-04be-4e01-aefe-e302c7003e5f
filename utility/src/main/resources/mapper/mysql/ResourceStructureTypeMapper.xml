<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.ResourceStructureTypeMapper">
    <select id="findUsedStructureObjectType" resultType="com.siteweb.utility.entity.ResourceStructureType">
        SELECT a.ResourceStructureTypeId, a.SceneId, a.ResourceStructureTypeName, a.Description
        FROM resourcestructureType a
        WHERE EXISTS(SELECT 1
                     FROM resourcestructure b
                     WHERE a.resourcestructureTypeId = b.StructureTypeId)
    </select>
    <select id="findByResourceStructureTypeId" resultType="com.siteweb.utility.entity.ResourceStructureType">
        SELECT ResourceStructureTypeId, SceneId, ResourceStructureTypeName, Description
        FROM ResourceStructureType
        WHERE ResourceStructureTypeId = #{resourceStructureTypeId}
    </select>
</mapper>