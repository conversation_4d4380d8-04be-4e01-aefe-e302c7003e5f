<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.EquipmentBaseTypeMapper">
    <resultMap id="equipmentBaseTypeMap" type="com.siteweb.utility.entity.EquipmentBaseType">
        <result column="BaseEquipmentId" property="baseEquipmentId" />
        <result column="BaseEquipmentName" property="baseEquipmentName" />
        <result column="EquipmentTypeId" property="equipmentTypeId" />
        <result column="EquipmentSubTypeId" property="equipmentSubTypeId" />
        <result column="Description" property="description" />
        <result column="ExtField" property="extField"/>
    </resultMap>
    <select id="findUsedEquipmentBaseTypes" resultType="com.siteweb.utility.entity.EquipmentBaseType">
        SELECT * from tbl_equipmentbasetype where BaseEquipmentId in (    SELECT a.EquipmentBaseType from tbl_equipmentTemplate a
        where exists(select 1 from tbl_equipment b where a.EquipmentTemplateId = b.EquipmentTemplateId))
    </select>
    <select id="findExtFieldByEquipmentBaseTypeId" resultMap="equipmentBaseTypeMap">
        SELECT ExtField
        FROM tbl_equipmentbasetype
        WHERE BaseEquipmentId = #{baseEquipmentId}
    </select>
    <select id="findByEquipmentIds" resultType="com.siteweb.utility.dto.EquipmentBaseTypeDTO">
        SELECT distinct c.BaseEquipmentId, c.BaseEquipmentName
        FROM tbl_equipment a
        INNER JOIN tbl_equipmenttemplate b ON a.EquipmentTemplateid = b.EquipmentTemplateId
        INNER JOIN tbl_equipmentbasetype c ON b.EquipmentBaseType = c.BaseEquipmentId
        WHERE a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
</mapper>