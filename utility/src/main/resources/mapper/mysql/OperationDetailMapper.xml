<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.OperationDetailMapper">
    <insert id="createOperationDetail" parameterType="com.siteweb.utility.entity.OperationDetail">
        INSERT INTO TBL_OperationDetail(UserId, ObjectId, ObjectType, PropertyName, OperationTime, OperationType, OldValue, NewValue)
        VALUES(#{userId}, #{objectId}, #{objectType}, #{propertyName}, #{operationTime}, #{operationType}, #{oldValue}, #{newValue})
    </insert>
    <insert id="batchCreate">
        INSERT INTO tbl_operationdetail(UserId, ObjectId, ObjectType, PropertyName, OperationTime, OperationType, OldValue, NewValue) VALUES
        <foreach collection="operationDetailList" item="item" separator=",">
            (#{item.userId},#{item.objectId},#{item.objectType},
            #{item.propertyName},#{item.operationTime},#{item.operationType},
            #{item.oldValue},#{item.newValue})
        </foreach>
    </insert>
</mapper>