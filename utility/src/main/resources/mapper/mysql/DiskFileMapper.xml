<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.DiskFileMapper">
    <delete id="deleteDiskFilesByFilePathAndFileName">
          DELETE FROM DiskFile WHERE FilePath = #{filePath} AND FileName =#{fileName}
    </delete>
    <select id="findFirstByFilePathAndFileName" resultType="com.siteweb.utility.entity.DiskFile">
        SELECT fileId, filePath, fileName, status
        FROM DiskFile
        WHERE FilePath = #{filePath}
          AND FileName = #{fileName}
    </select>
    <select id="findByFilePath" resultType="com.siteweb.utility.entity.DiskFile">
        select fileId,filePath,fileName, status FROM DiskFile WHERE FilePath = #{filePath};
    </select>
    <select id="findByStatusAndCreateTimeBefore" resultType="com.siteweb.utility.entity.DiskFile">
        SELECT fileId,filePath,fileName, status FROM　DiskFile　WHERE Status = #{status} AND CreateTime &lt; #{time}
    </select>
    <select id="findByFilePathPage" resultType="com.siteweb.utility.entity.DiskFile">
        select fileId,filePath,fileName, status FROM DiskFile WHERE FilePath = #{filePath}
    </select>
</mapper>