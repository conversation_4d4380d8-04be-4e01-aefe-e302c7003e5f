<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.DataItemMapper">
    <select id="findMaxDataItemIdByEntryId" resultType="java.lang.Integer">
        SELECT MAX(t.ItemId)
        FROM TBL_DataItem t
        WHERE t.EntryId = #{entryId};
    </select>
    <select id="selectMaxItemId" resultType="java.lang.Integer">
        SELECT MAX(EntryItemId) FROM TBL_DataItem
    </select>
</mapper>