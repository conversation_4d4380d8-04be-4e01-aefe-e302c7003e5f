<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.utility.mapper.SceneStructureMapper">
    <resultMap type="com.siteweb.utility.entity.SceneStructure" id="SceneStructureMap">
        <result property="sceneStructureId" column="SceneStructureId" jdbcType="INTEGER"/>
        <result property="sceneId" column="SceneId" jdbcType="INTEGER"/>
        <result property="objectTypeId" column="ObjectTypeId" jdbcType="INTEGER"/>
        <result property="displayIndex" column="DisplayIndex" jdbcType="INTEGER"/>
        <association property="resourceStructureType"
                     javaType="com.siteweb.utility.entity.ResourceStructureType"
                     select="com.siteweb.utility.mapper.ResourceStructureTypeMapper.findByResourceStructureTypeId"
                     column="objectTypeId"/>
    </resultMap>

    <sql id="baseField">
        SceneStructureId, SceneId, ObjectTypeId, DisplayIndex
    </sql>

    <select id="selectList" resultMap="SceneStructureMap">
        select <include refid="baseField"/> from scenestructure
    </select>

</mapper>