package com.siteweb.tts.listencer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.eventnotification.textnotify.NotifyTypeEnum;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.tts.entity.TtsFilterConfig;
import com.siteweb.tts.strategy.TtsStrategyCacheManager;
import com.siteweb.tts.strategy.filter.AlarmStrategyFilter;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TTS事件监听器，负责处理告警变更事件并进行语音通知
 */
@Slf4j
@Component
public class AlarmTtsListener implements ApplicationListener<BaseSpringEvent<AlarmChange>> {
    /**
     * 最大的tts容量
     */
    private static final int TTS_MAX_CAPACITY = 100;
    /**
     * unique分隔符
     */
    private static final String UNIQUE_ID_DELIMITER = ":";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private TtsStrategyCacheManager ttsStrategyCacheManager;

    @Autowired
    private AlarmStrategyFilter alarmStrategyFilter;

    @Autowired
    private RegionService regionService;
    @Autowired
    private HAStatusService haStatusService;

    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP");
            return;
        }
        if (!isTtsStrategyEnabled()) {
            return;
        }

        AlarmChange alarmChange = event.getData();
        if (isAlarmFiltered(alarmChange)) {
            return;
        }
        processAlarmNotifications(alarmChange);
    }

    /**
     * 检查TTS策略是否启用
     */
    private boolean isTtsStrategyEnabled() {
        if (!ttsStrategyCacheManager.isEnabled()) {
            log.info("TTS strategy is disabled");
            return false;
        }
        return true;
    }

    /**
     * 检查告警是否被过滤
     */
    private boolean isAlarmFiltered(AlarmChange alarmChange) {
        ConcurrentHashMap<Integer, List<TtsFilterConfig>> filterConfigMap = ttsStrategyCacheManager.getFilterConfigConcurrentMap();
        if (CollUtil.isEmpty(filterConfigMap)) {
            return true;
        }
        //因为前端浏览器只能播报一次，如果配置了多个策略，只要不符合其中任意一个策略则过滤不播报
        return filterConfigMap.values()
                              .stream()
                              .anyMatch(filterConfigList -> alarmStrategyFilter.filter(filterConfigList, alarmChange));
    }

    /**
     * 处理告警通知
     */
    private void processAlarmNotifications(AlarmChange alarmChange) {
        Set<String> lastRequestTimeSet = getLastRequestTimeSet();
        if (CollUtil.isEmpty(lastRequestTimeSet)) {
            return;
        }

        lastRequestTimeSet.stream()
                          .filter(this::isValidUniqueId)  // 首先验证uniqueId格式
                          .filter(uniqueId -> hasUserPermission(getUseId(uniqueId), alarmChange))  // 检查用户权限
                          .forEach(uniqueId -> pushMsg(uniqueId, alarmChange));  // 使用完整的uniqueId推送消息
    }

    /**
     * 验证uniqueId格式并检查用户ID是否有效
     * @param uniqueId 用户唯一标识（格式：userId:sessionId）
     * @return 是否为有效的uniqueId
     */
    private boolean isValidUniqueId(String uniqueId) {
        try {
            String[] parts = uniqueId.split(UNIQUE_ID_DELIMITER);
            if (parts.length < 2) {  // 确保包含userId和sessionId
                log.warn("Invalid uniqueId format: {}", uniqueId);
                return false;
            }
            Integer.parseInt(parts[0]);  // 验证userId是否为有效数字
            return true;
        } catch (NumberFormatException e) {
            log.warn("Failed to parse userId from uniqueId: {}", uniqueId, e);
            return false;
        }
    }

    /**
     * 从uniqueId中提取用户ID
     * @param uniqueId 用户唯一标识（格式：userId:sessionId）
     * @return 用户ID
     */
    private int getUseId(String uniqueId) {
        return Integer.parseInt(uniqueId.split(UNIQUE_ID_DELIMITER)[0]);
    }


    /**
     * 获取最近请求时间集合
     */
    private Set<String> getLastRequestTimeSet() {
        return redisTemplate.opsForZSet().range(GlobalConstants.REQUEST_MESSAGE_TIME, 0, -1);
    }

    /**
     * 检查用户权限
     */
    private boolean hasUserPermission(int userId, AlarmChange alarmChange) {
        return regionService.hasRegionMapPermissions(userId, alarmChange.getResourceStructureId(), alarmChange.getEquipmentId());
    }

    /**
     * 推送消息到Redis
     */
    private void pushMsg(String uniqueId, AlarmChange alarmChange) {
        String redisKey = NotifyTypeEnum.ALARM_TEXT_NOTIFY.getRedisKeyPrefix() + uniqueId;

        Long messageCount = redisTemplate.opsForZSet().size(redisKey);
        if (isQueueFull(messageCount)) {
            log.info("Message queue full for uniqueId: {}, current count: {}", uniqueId, messageCount);
            return;
        }

        redisTemplate.opsForZSet().add(redisKey, JSONUtil.toJsonStr(alarmChange), calculateScore(alarmChange));
    }

    /**
     * 检查消息队列是否已满
     */
    private boolean isQueueFull(Long messageCount) {
        return Objects.nonNull(messageCount) && messageCount >= TTS_MAX_CAPACITY;
    }

    /**
     * 计算消息优先级分数
     */
    private double calculateScore(AlarmChange alarmChange) {
        return ttsStrategyCacheManager.isEnableSortByEventLevel()
                ? alarmChange.getEventLevel()  // 告警等级越高越优先
                : -System.currentTimeMillis();  // 时间顺序优先
    }
}

