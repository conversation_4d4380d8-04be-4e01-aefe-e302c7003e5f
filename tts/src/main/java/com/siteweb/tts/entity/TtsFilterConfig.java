package com.siteweb.tts.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Tts过滤条件配置表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("TtsFilterConfig")
public class TtsFilterConfig {

    /**
     * 主键自增id
     */
    @TableId(value = "TtsFilterConfigId", type = IdType.AUTO)
    private Integer ttsFilterConfigId;

    /**
     * 所属策略的id
     */
    private Integer ttsStrategyId;

    /**
     * 键名
     */
    private String ttsConfigKey;

    /**
     * 键值
     */
    private String ttsConfigValue;
}

