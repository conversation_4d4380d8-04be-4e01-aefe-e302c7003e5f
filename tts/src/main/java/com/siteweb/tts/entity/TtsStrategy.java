package com.siteweb.tts.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * TTS策略表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("TtsStrategy")
public class TtsStrategy {

    /**
     * 主键自增id
     */
    @TableId(value = "TtsStrategyId", type = IdType.AUTO)
    private Integer ttsStrategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 是否启用 0 否 1是
     */
    private Integer enable;

    /**
     * 生效开始时间
     */
    private Date effectiveStartTime;

    /**
     * 生效结束时间
     */
    private Date effectiveEndTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 策略过滤条件
     */
    @TableField(exist = false)
    private List<TtsFilterConfig> ttsFilterConfigs;
}

