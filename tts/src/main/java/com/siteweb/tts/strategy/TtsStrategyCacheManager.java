package com.siteweb.tts.strategy;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.enums.TtsConfigEnum;
import com.siteweb.eventnotification.service.TtsConfigService;
import com.siteweb.tts.entity.TtsFilterConfig;
import com.siteweb.tts.entity.TtsStrategy;
import com.siteweb.tts.mapper.TtsFilterConfigMapper;
import com.siteweb.tts.service.TtsStrategyService;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Component
public class TtsStrategyCacheManager {
    @Getter
    private ConcurrentHashMap<Integer, List<TtsFilterConfig>> filterConfigConcurrentMap = new ConcurrentHashMap<>();
    @Value("${byteDance.eventLevelFilterEnable:false}")
    @Getter
    private boolean isEnabled;
    @Getter
    private boolean enableSortByEventLevel;
    @Autowired
    TtsFilterConfigMapper ttsFilterConfigMapper;
    @Autowired
    TtsStrategyService tsStrategyService;
    @Autowired
    TtsConfigService tsConfigService;

    @Scheduled(cron = "0/10 * * * * ?")
    public void scheduled() {
        cacheFilterConfig();
        cacheStrategyEnable();
    }

    private void cacheStrategyEnable() {
        enableSortByEventLevel = tsConfigService.findBooleanValue(TtsConfigEnum.TTS_SORTBY_EVENTLEVEL.getTtsConfigKey());
    }

    private void cacheFilterConfig() {
        List<TtsStrategy> activeStrategyWithinEffectiveDate = tsStrategyService.getActiveStrategyWithinEffectiveDate();
        if (CollUtil.isEmpty(activeStrategyWithinEffectiveDate)) {
            filterConfigConcurrentMap = new ConcurrentHashMap<>();
            return;
        }
        List<Integer> strategyIdList = activeStrategyWithinEffectiveDate.stream().map(TtsStrategy::getTtsStrategyId).toList();
        List<TtsFilterConfig> ttsFilterConfigs = ttsFilterConfigMapper.selectList(Wrappers.lambdaQuery(TtsFilterConfig.class)
                                                                                          .in(TtsFilterConfig::getTtsStrategyId, strategyIdList));
        //分组过滤
        filterConfigConcurrentMap = ttsFilterConfigs.stream().collect(Collectors.groupingByConcurrent(TtsFilterConfig::getTtsStrategyId, ConcurrentHashMap::new, toList()));
    }

}
