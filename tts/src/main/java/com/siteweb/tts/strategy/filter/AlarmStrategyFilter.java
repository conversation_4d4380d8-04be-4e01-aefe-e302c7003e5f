package com.siteweb.tts.strategy.filter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.eventnotification.enums.TtsConfigEnum;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.tts.entity.TtsFilterConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class AlarmStrategyFilter {
    @Autowired
    ResourceStructureManager resourceStructureManager;

    /**
     * 是否被过滤
     * 过滤后的不需要播报
     * @param ttsFilterConfigList TTS策略
     * @param alarmChange 告警变更信息
     * @return boolean 是否被过滤 true 是  false 否
     */
    public boolean filter(List<TtsFilterConfig> ttsFilterConfigList, AlarmChange alarmChange) {
        Map<String, HashSet<String>> filterConditionRule = ttsFilterConfigList.stream()
                                                                        .filter(rule -> CharSequenceUtil.isNotBlank(rule.getTtsConfigValue()))
                                                                        .collect(Collectors.toMap(TtsFilterConfig::getTtsConfigKey, rule -> new HashSet<>(CharSequenceUtil.split(rule.getTtsConfigValue(), ","))));
        // 判断是否需要取反
        boolean negated = isNegated(filterConditionRule);
        // 是否过滤该条tts语音,默认不过滤
        boolean isFilter = false;
        // 判断各种条件规则
        for (Map.Entry<String, HashSet<String>> ruleEntry : filterConditionRule.entrySet()) {
            // 位置、告警等级、告警状态等过滤逻辑
            if (checkFilterCondition(ruleEntry, alarmChange)) {
                isFilter = true;
                break;
            }
        }
        // 根据 negated 决定是否反转结果
        return negated ? !isFilter : isFilter;
    }

    /**
     * 过滤条件是否取反
     * 就是页面上被选择到右边的条件是需要符合才播放  还是需要不符合才播放
     * @param filterConditionRule 过滤条件规则
     * @return boolean
     */
    public boolean isNegated(Map<String, HashSet<String>> filterConditionRule){
        HashSet<String> isNegatedSet = filterConditionRule.getOrDefault(TtsConfigEnum.TTS_FILTER_POSITION_IS_NEGATED.getTtsConfigKey(), new HashSet<>());
        if (CollUtil.isEmpty(isNegatedSet)){
            return false;
        }
        String isNegated = CollUtil.join(isNegatedSet, "");
        return Boolean.parseBoolean(isNegated);
    }

    private boolean checkFilterCondition(Map.Entry<String, HashSet<String>> ruleEntry, AlarmChange alarmChange) {
        // 各种过滤条件的判断
        return isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_POSITION, alarmChange.getResourceStructureId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.NOTIFICATION_HOST_ALARM_SEVERITY_SYSTEM_CONFIG_KEY, alarmChange.getEventLevel()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.ALARM_NOTIFICATION_STATUS, alarmChange.getOperationType()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_BASE_TYPE, alarmChange.getBaseTypeId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_BASE_EQUIPMENT, alarmChange.getBaseEquipmentId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_EVENT, alarmChange.getEventId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_EQUIPMENT, alarmChange.getEquipmentId()) ||
                checkProjectStatus(ruleEntry, alarmChange) ||
                checkKeywordFilter(ruleEntry, alarmChange);
    }
    /**
     * 通用的过滤方法
     */
    private boolean isFilterByKey(Map.Entry<String, HashSet<String>> ruleEntry, TtsConfigEnum configEnum, Object value) {
        return Objects.equals(ruleEntry.getKey(), configEnum.getTtsConfigKey()) && Objects.nonNull(value) && !ruleEntry.getValue().contains(String.valueOf(value));
    }
    /**
     * 工程状态过滤
     */
    private boolean checkProjectStatus(Map.Entry<String, HashSet<String>> ruleEntry, AlarmChange alarmChange) {
        return Objects.equals(ruleEntry.getKey(), TtsConfigEnum.TTS_PROJECT_STATUS_ENABLE.getTtsConfigKey()) && !isMaintenanceStatusMatching(alarmChange.getMaintainState(), ruleEntry.getValue());
    }
    /**
     * 关键字过滤
     */
    private boolean checkKeywordFilter(Map.Entry<String, HashSet<String>> ruleEntry, AlarmChange alarmChange) {
        return Objects.equals(ruleEntry.getKey(), TtsConfigEnum.TTS_FILTER_KEYWORD.getTtsConfigKey()) && isKeywordFilter(ruleEntry.getValue(), alarmChange);
    }
    /**
     * 关键字过滤详细实现
     */
    private boolean isKeywordFilter(HashSet<String> keywords, AlarmChange alarmChange) {
        String keyWord = CollUtil.join(keywords, "");

        if (CharSequenceUtil.isBlank(keyWord)) {
            return false;
        }

        String fullResourceStructureName = resourceStructureManager.getFullPath(alarmChange.getResourceStructureId());
        String operationTypeName = AlarmOperationTypeEnum.getOperationTypeNameByValue(alarmChange.getOperationType());

        return !(
                (fullResourceStructureName != null && fullResourceStructureName.contains(keyWord)) || // 位置
                        (alarmChange.getBaseEquipmentName() != null && alarmChange.getBaseEquipmentName().contains(keyWord)) || // 设备基类
                        (alarmChange.getEquipmentName() != null && alarmChange.getEquipmentName().contains(keyWord)) || // 设备名称
                        (alarmChange.getBaseTypeName() != null && alarmChange.getBaseTypeName().contains(keyWord)) || // 告警基类
                        (alarmChange.getEventName() != null && alarmChange.getEventName().contains(keyWord)) || // 告警/事件
                        (alarmChange.getEventSeverity() != null && alarmChange.getEventSeverity().contains(keyWord)) || // 告警等级
                        (operationTypeName != null && operationTypeName.contains(keyWord)) // 事件状态
        );
    }

    /**
     * 维护状态匹配
     */
    private boolean isMaintenanceStatusMatching(Integer maintainState, HashSet<String> maintainStateSet) {
        boolean isProjectEnabled = maintainStateSet.contains("true");
        boolean isInMaintainState = !Objects.equals(1, maintainState);

        return (isProjectEnabled && isInMaintainState) || (!isProjectEnabled && !isInMaintainState);
    }
}
