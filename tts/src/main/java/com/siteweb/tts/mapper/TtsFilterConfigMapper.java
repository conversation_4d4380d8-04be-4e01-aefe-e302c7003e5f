package com.siteweb.tts.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tts.entity.TtsFilterConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TtsFilterConfigMapper extends BaseMapper<TtsFilterConfig> {
    int batchInsert(@Param("ttsFilterConfigList") List<TtsFilterConfig> ttsFilterConfigList);
}
