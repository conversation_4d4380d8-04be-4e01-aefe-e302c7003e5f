package com.siteweb.tts.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tts.dto.TtsFilterConfigUpdateDTO;
import com.siteweb.tts.entity.TtsFilterConfig;
import com.siteweb.tts.mapper.TtsFilterConfigMapper;
import com.siteweb.tts.service.TtsFilterConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TtsFilterConfigServiceImpl implements TtsFilterConfigService {
    @Autowired
    TtsFilterConfigMapper ttsFilterConfigMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFilterConfig(TtsFilterConfigUpdateDTO ttsFilterConfigUpdateDTO) {
        ttsFilterConfigMapper.delete(Wrappers.lambdaQuery(TtsFilterConfig.class)
                                             .eq(TtsFilterConfig::getTtsStrategyId, ttsFilterConfigUpdateDTO.getTtsStrategyId()));
        //批量添加
        ttsFilterConfigMapper.batchInsert(ttsFilterConfigUpdateDTO.getTtsFilterConfigList());
        return true;
    }
}
