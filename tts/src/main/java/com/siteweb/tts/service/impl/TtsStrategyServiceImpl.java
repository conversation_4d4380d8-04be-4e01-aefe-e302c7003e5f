package com.siteweb.tts.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.tts.entity.TtsFilterConfig;
import com.siteweb.tts.entity.TtsStrategy;
import com.siteweb.tts.mapper.TtsFilterConfigMapper;
import com.siteweb.tts.mapper.TtsStrategyMapper;
import com.siteweb.tts.service.TtsStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class TtsStrategyServiceImpl implements TtsStrategyService {

    @Autowired
    TtsStrategyMapper ttsStrategyMapper;
    @Autowired
    TtsFilterConfigMapper ttsFilterConfigMapper;


    @Override
    public List<TtsStrategy> findAll() {
        return ttsStrategyMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public TtsStrategy findById(Integer id) {
        TtsStrategy ttsStrategy = ttsStrategyMapper.selectById(id);
        ttsStrategy.setTtsFilterConfigs(findByTtsStrategyId(id));
        return ttsStrategy;
    }

    public List<TtsFilterConfig> findByTtsStrategyId(Integer ttsStrategyId) {
        return ttsFilterConfigMapper.selectList(Wrappers.lambdaQuery(TtsFilterConfig.class)
                                                        .eq(TtsFilterConfig::getTtsStrategyId, ttsStrategyId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TtsStrategy create(TtsStrategy ttsStrategy) {
        closeAllStrategy(ttsStrategy.getEnable());
        ttsStrategy.setTtsStrategyId(null);
        ttsStrategyMapper.insert(ttsStrategy);
        return ttsStrategy;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TtsStrategy updateById(TtsStrategy ttsStrategy) {
        closeAllStrategy(ttsStrategy.getEnable());
        ttsStrategyMapper.updateById(ttsStrategy);
        return ttsStrategy;
    }

    @Override
    public List<TtsStrategy> getActiveStrategyWithinEffectiveDate() {
        return ttsStrategyMapper.getActiveStrategyWithinEffectiveDate();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteById(Integer id) {
        ttsStrategyMapper.deleteById(id);
        ttsFilterConfigMapper.delete(Wrappers.lambdaQuery(TtsFilterConfig.class)
                                             .eq(TtsFilterConfig::getTtsStrategyId, id));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEnable(Integer ttsStrategyId, Integer enable) {
        closeAllStrategy(enable);
        return ttsStrategyMapper.update(Wrappers.lambdaUpdate(TtsStrategy.class)
                                                .eq(TtsStrategy::getTtsStrategyId, ttsStrategyId)
                                                .set(TtsStrategy::getEnable, enable)) > 0;
    }

    /**
     * 关闭所有策略
     * 目前只能开启一个策略，如果新增的或者更新的策略是开启，则要关闭其他所有策略
     */
    private void closeAllStrategy(Integer enable){
        //新增或更新的策略不开启 则无需处理
        if (Objects.equals(enable, GlobalConstants.NO)) {
            return;
        }
        ttsStrategyMapper.update(Wrappers.lambdaUpdate(TtsStrategy.class)
                                         .eq(TtsStrategy::getEnable, GlobalConstants.YES)
                                         .set(TtsStrategy::getEnable, GlobalConstants.NO));
    }
}
