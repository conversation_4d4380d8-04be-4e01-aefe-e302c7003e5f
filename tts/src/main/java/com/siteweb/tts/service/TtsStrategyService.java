package com.siteweb.tts.service;

import com.siteweb.tts.entity.TtsStrategy;

import java.util.List;

public interface TtsStrategyService {
    List<TtsStrategy> findAll();

    TtsStrategy findById(Integer id);

    TtsStrategy create(TtsStrategy ttsStrategy);

    TtsStrategy updateById(TtsStrategy ttsStrategy);

    /**
     * 获取在生效期内的策略
     * @return {@link List }<{@link TtsStrategy }> 策略
     */
    List<TtsStrategy> getActiveStrategyWithinEffectiveDate();

    /**
     * 通过ID删除
     *
     * @param id ID
     * @return boolean 是否成功
     */
    boolean deleteById(Integer id);

    /**
     * 更新启用状态
     * 目前只允许启动一个策略，另外的其他的策略会关闭
     * @param ttsStrategyId TTS策略ID
     * @param enable        是否启用 1启用 0关闭
     * @return boolean
     */
    boolean updateEnable(Integer ttsStrategyId, Integer enable);
}
