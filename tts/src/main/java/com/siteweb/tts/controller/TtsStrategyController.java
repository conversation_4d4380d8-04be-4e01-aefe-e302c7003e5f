package com.siteweb.tts.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.tts.entity.TtsStrategy;
import com.siteweb.tts.service.TtsStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/ttsstrategy")
public class TtsStrategyController {
    @Autowired
    TtsStrategyService tsStrategyService;

    @GetMapping
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(tsStrategyService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(tsStrategyService.findById(id));
    }

    @PostMapping
    public ResponseEntity<ResponseResult> create(@RequestBody TtsStrategy ttsStrategy) {
        return ResponseHelper.successful(tsStrategyService.create(ttsStrategy));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateById(@RequestBody TtsStrategy ttsStrategy) {
        return ResponseHelper.successful(tsStrategyService.updateById(ttsStrategy));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(tsStrategyService.deleteById(id));
    }

    @PutMapping("/enable")
    public ResponseEntity<ResponseResult> updateEnable(@RequestBody TtsStrategy ttsStrategy) {
        return ResponseHelper.successful(tsStrategyService.updateEnable(ttsStrategy.getTtsStrategyId(),ttsStrategy.getEnable()));
    }
}
