package com.siteweb.tts.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.tts.dto.TtsFilterConfigUpdateDTO;
import com.siteweb.tts.service.TtsFilterConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/ttsfilterconfig")
public class TtsFilterConfigController {
    @Autowired
    TtsFilterConfigService ttsFilterConfigService;

    @PutMapping
    public ResponseEntity<ResponseResult> updateFilterConfig(@RequestBody  TtsFilterConfigUpdateDTO ttsFilterConfigUpdateDTO) {
        return ResponseHelper.successful(ttsFilterConfigService.updateFilterConfig(ttsFilterConfigUpdateDTO));
    }
}
