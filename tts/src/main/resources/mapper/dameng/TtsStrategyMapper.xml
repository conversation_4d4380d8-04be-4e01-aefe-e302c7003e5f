<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.tts.mapper.TtsStrategyMapper">
    <select id="getActiveStrategyWithinEffectiveDate" resultType="com.siteweb.tts.entity.TtsStrategy">
        SELECT ttsstrategyid, strategyname, enable, effectivestarttime, effectiveendtime, description
        FROM TtsStrategy
        WHERE Enable = 1
          AND NOW() BETWEEN EffectiveStartTime AND EffectiveEndTime
    </select>
</mapper>