<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.tts.mapper.TtsFilterConfigMapper">
    <insert id="batchInsert">
        INSERT INTO TtsFilterConfig (
        ttsStrategyId,
        ttsConfigKey,
        ttsConfigValue
        ) VALUES
        <foreach collection="ttsFilterConfigList" item="item" separator=",">
            (
            #{item.ttsStrategyId},
            #{item.ttsConfigKey},
            #{item.ttsConfigValue}
            )
        </foreach>
    </insert>
</mapper>