package com.siteweb.as.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum TransferProtocolEnum {
    /**
     * FTP (File Transfer Protocol) - 传统的文件传输协议，不加密
     */
    FTP("ftp", "File Transfer Protocol", 21),

    /**
     * SFTP (SSH File Transfer Protocol) - 基于 SSH 的安全文件传输协议
     */
    SFTP("sftp", "SSH File Transfer Protocol", 22);

    /**
     * 协议标识符（小写，用于 URL 或命令）
     */
    private final String protocol;
    /**
     * 协议描述
     */
    private final String description;
    /**
     * 默认端口号
     */
    private final int defaultPort;

    public static TransferProtocolEnum fromProtocol(String protocol) {
        return Arrays.stream(values()).filter(e -> e.protocol.equals(protocol)).findFirst().orElse(null);
    }
}
