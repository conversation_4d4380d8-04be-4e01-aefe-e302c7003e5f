package com.siteweb.as.enums;

import lombok.experimental.UtilityClass;

@UtilityClass
public class CustomPictures {

    public static final String House = "House.png";
    public static final String MonitoringCenter = "MonitoringCenter.png";
    public static final String StationGroup = "StationGroup.png";
    public static final String Equipment = "Equipment.png";
    public static final String Port = "Port.png";
    public static final String SamplerUnit = "SamplerUnit.png";
    public static final String Station = "Station.png";
    public static final String AllEventSeverity = "AllEventSeverity.png";
    public static final String Masking = "Masking.png";
    public static final String UnRegister = "ApplicationServer.png";
    public static final String EquipmentCategory = "EquipmentCategory.png";

    // Add by m93349 江苏版要求在工程状态下图标是异常的
    public static final String Building = "Building.png";

}
