package com.siteweb.as.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum BusinessObjectType {
    /**
     * 活动对象：局站分组
     */
    ActiveStationGroup(20001),
    /**
     * 活动对象：局站
     */
    ActiveStation(20002),   //局站
    ActiveEquipment (20005),//设备
    ActiveHouse(20011),//活动局房
    ActiveEquipmentCategory(20012),//活动局房
    /**
     * 配置对象：局站分组类型
     */
    ConfigStationGroupType(1),
    /**
     * 局站等级
     */
    EntryStationGrade(2),
    /**
     * 局站状态
     */
    EntryStationState(5),
    /**
     * 设备类型
     */
    EntryEquipmentCategory(7),
    /**
     * / 生产厂家
     */
    EntryVendor(14),
    /**
    * 信号种类
    */
    EntrySignalCategory(17),
    /**
     * 信号属性
     */
    EntrySignalProperty(21),
    /**
     * 事件种类
     */
    EntryEventCategory(24),
    /**
     *事件等级
     */
    EntryEventSeverity(23),
    /**
     * 屏蔽方式
     */
    EntryMaskMode(67),
    /**
     * 局站类型
     */
    EntryStationCategory(71),
    /**
     * 派单状态
     */
    EntryNotificationStatus(83),

    /**
    * 标准化事件
    */
    EntryStandardEventType(1013),

    /**
    * 配置对象：局站分组
    */
    ConfigStationGroup(10001),

    /**
    * 配置对象：局站
    */
    ConfigStation(10002),

    /**
    * 配置对象：设备
    */
    ConfigEquipment(10005),

    /**
     * 配置告警等级
     */
    ConfigEventSeverity(10012),
    /**
     * 配置设备类型
     */
    ConfigEquipmentCategory(10019),
    /**
     * 局站等级等级
     */
    ConfigStationGrade(10013),
    /**
     * 未知对象类型
     */
    Unknown(0),
    /**
     * 监控中心
     */
    ConfigMonitoringCenter(10018)
    ;



    @EnumValue
    private final Integer type;

    BusinessObjectType(int value) {
        type = value;
    }

    public int value() {
        return this.type;
    }

    /**
     * 根据枚举名称获取对应的数字值
     */
    public static Integer getValueByName(String name) {
        for (BusinessObjectType type : BusinessObjectType.values()) {
            if (type.name().equals(name)) {
                return type.getType();
            }
        }
        return null; // 返回 null 表示未找到
    }
}
