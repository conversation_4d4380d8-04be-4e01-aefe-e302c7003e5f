package com.siteweb.as.schedule;

import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Creation Date: 2025/1/9
 */
@Component
@EnableScheduling
@Slf4j
public class CleanPatrolDataSchedule {
    public static final String GROUP_NAME = "CleanPatrolDataJob";
    @Autowired
    private HAStatusService haStatusService;
    @Autowired
    private Scheduler scheduler;

    @PostConstruct
    public void initializeSampleJob() throws SchedulerException {
        if(!haStatusService.isMasterHost()) {
            log.info("SavePatrolDataSchedule : HAStatus is BACKUP.");
            return;
        }
        // 创建任务
        TriggerKey triggerKey = new TriggerKey("cleanPatrolDataTrigger", GROUP_NAME);
        if (scheduler.checkExists(triggerKey)) {
            return;
        }
        JobDetail job = JobBuilder.newJob(CleanPatrolDataJob.class)
                .withIdentity("cleanPatrolDataJob", GROUP_NAME)
                .build();
        // 创建触发器，每天凌晨三点执行
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("cleanPatrolDataTrigger", GROUP_NAME)
                .withSchedule(CronScheduleBuilder.cronSchedule("0 0 3 * * ?"))
                .build();
        scheduler.scheduleJob(job, trigger);
        log.info("CleanPatrolDataJob scheduled successfully.");
    }

}
