package com.siteweb.as.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.siteweb.as.dto.patrol.GroupParameterDTO;
import com.siteweb.as.dto.patrol.PatrolRuleJobDTO;
import com.siteweb.as.dto.patrol.PatrolSignalDTO;
import com.siteweb.as.dto.patrol.StationEventRecordDTO;
import com.siteweb.as.entity.PatrolAllRecord;
import com.siteweb.as.entity.PatrolExrecord;
import com.siteweb.as.entity.PatrolTask;
import com.siteweb.as.mapper.*;
import com.siteweb.common.util.GetBeanUtil;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 自动巡检定时任务
 */
@Component
@Slf4j
public class PatrolStandardTaskJob implements Job {
    private PatrolStandardTaskMapper patrolStandardTaskMapper = GetBeanUtil.getBean(PatrolStandardTaskMapper.class);
    private PatrolStandardGroupMapper patrolStandardGroupMapper = GetBeanUtil.getBean(PatrolStandardGroupMapper.class);
    private PatrolRuleMapper patrolRuleMapper = GetBeanUtil.getBean(PatrolRuleMapper.class);
    private RealTimeSignalManager realTimeSignalManager = GetBeanUtil.getBean(RealTimeSignalManager.class);
    private ActiveEventManager activeEventManager = GetBeanUtil.getBean(ActiveEventManager.class);
    private HAStatusService haStatusService = GetBeanUtil.getBean(HAStatusService.class);


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            log.info("PatrolStandardTaskJob : HAStatus is BACKUP.");
            return;
        }
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        Integer taskId = (Integer) jobDataMap.get(PatrolTask.Fields.taskId);
        Integer groupId = (Integer) jobDataMap.get(PatrolTask.Fields.groupId);
        Integer isPowerOffSave = (Integer) jobDataMap.get(PatrolTask.Fields.isPowerOffSave);
        log.info("<PatrolStandardTaskJob> TaskJob start taskId: {}", taskId);
        GroupParameterDTO groupParameterDTO = patrolStandardGroupMapper.getGroupParameter(groupId);
        String stationIds = Optional.ofNullable(groupParameterDTO).map(GroupParameterDTO::getStationIds).orElse(null);
        // 获取对应的信号数据
        List<PatrolSignalDTO> patrolSignalList = patrolStandardTaskMapper.selectPatrolStandardSignal(groupId, stationIds);
        if (CollUtil.isEmpty(patrolSignalList)) {
            log.warn("<PatrolStandardTaskJob> patrolSignalList is null taskId:{}", taskId);
            return;
        }
        // 获取过滤规则
        List<PatrolRuleJobDTO> patrolRuleJobDTOList = patrolRuleMapper.getPatrolRuleJobDTO(groupId);
        // 获取实时信号值
        boolean realTimeData = getRealTimeData(patrolSignalList, taskId);
        if (realTimeData || CollUtil.isEmpty(patrolSignalList)) {
            return;
        }
        // 数据规则判断以及数据初始化
        processDataWithRule(patrolSignalList, patrolRuleJobDTOList);
        // 异常数据筛选处理(是否停电引起，是否存库)
        processExData(patrolSignalList, isPowerOffSave);
        // resultall: 满足 IsSave == "1" 的记录
        List<PatrolAllRecord> resultall = patrolSignalList.stream()
                .filter(signal -> signal.getIsSave() == 1)
                .map(PatrolSignalDTO::convertAllrecord)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(resultall)) {
            SavePatrolDataSchedule.addAllRecords(resultall);
        }
        // resultex: 满足 IsSave == "1" 且 Reasonable == "0" 的记录
        List<PatrolExrecord> resultex = patrolSignalList.stream()
                .filter(signal -> signal.getIsSave() == 1 && signal.getReasonable() == 0)
                .map(PatrolSignalDTO::convertExrecord)
                .toList();
        if (CollUtil.isNotEmpty(resultex)) {
            SavePatrolDataSchedule.addExRecords(resultex);
        }

    }

    private boolean getRealTimeData(List<PatrolSignalDTO> patrolSignalList, Integer taskId) {
        // 组装redis key
        List<String> redisKey = new ArrayList<>();
        for (PatrolSignalDTO patrolSignalDTO : patrolSignalList) {
            redisKey.add("RealTimeSignal:" + patrolSignalDTO.getEquipmentId() + "." + patrolSignalDTO.getSignalId());
        }
        // 获取redis实时数据
        List<RealTimeSignalItem> realTimeSignalItems = realTimeSignalManager.getRealTimeSignalByKeys(redisKey);
        if (CollUtil.isEmpty(realTimeSignalItems)) {
            log.info("<PatrolTaskJob> realTimeSignalItems is null taskId:{}", taskId);
            return true;
        }
        // 转map便于查找
        Map<String, RealTimeSignalItem> realTimeSignalItemMap = realTimeSignalItems.stream().collect(
                Collectors.toMap(RealTimeSignalItem::getKey, p -> p));
        Iterator<PatrolSignalDTO> iterator = patrolSignalList.iterator();
        while (iterator.hasNext()) {
            PatrolSignalDTO patrolSignalDTO = iterator.next();
            RealTimeSignalItem realTimeSignalItem = realTimeSignalItemMap.get(patrolSignalDTO.getEquipmentId() + "." + patrolSignalDTO.getSignalId());
            if (Objects.nonNull(realTimeSignalItem)) {
                patrolSignalDTO.setSignalValue(realTimeSignalItem.getCurrentValue());
                patrolSignalDTO.setRecordTime(DateUtil.parse(realTimeSignalItem.getSampleTime()));
            } else {
                iterator.remove(); // 从列表中移除不符合条件的元素
            }
        }
        return false;
    }

    public List<PatrolSignalDTO> processExData(List<PatrolSignalDTO> signalTable, int isPowerOffSave) {
        // 获取当前停电断站的局站信息
        Set<Integer> stationIdSet = getPowerOffDisconnectStation();
        for (PatrolSignalDTO signal : signalTable) {
            if (signal.getReasonable() == null || signal.getReasonable() != 0) {
                continue;
            }
            boolean contains = stationIdSet.contains(signal.getStationId());
            if (Boolean.TRUE.equals(contains)) {
                signal.setIsPowerOffAlarm("是");
            } else {
                signal.setIsPowerOffAlarm("否");
            }

            if (Boolean.TRUE.equals(contains) && isPowerOffSave == 0) {
                signal.setIsSave(0);
            }
        }

        return signalTable;
    }

    @NotNull
    private Set<Integer> getPowerOffDisconnectStation() {
        List<StationEventRecordDTO> stationPowerOffRecordNew = getPatrolStationPowerOffRecordNew();
        List<StationEventRecordDTO> stationDisconnectRecordNew = getPatrolStationDisconnectRecordNew();
        // 将停电记录按 StationId 分组，方便后续匹配
        Map<Integer, List<StationEventRecordDTO>> powerOffRecordsByStationId =
                stationPowerOffRecordNew.stream().collect(Collectors.groupingBy(StationEventRecordDTO::getStationId));
        // 使用 Stream 处理断站记录
        return stationDisconnectRecordNew.stream()
                .flatMap(disconnect -> {
                    // 获取对应的停电记录列表
                    List<StationEventRecordDTO> powerOffRecords = powerOffRecordsByStationId.get(disconnect.getStationId());
                    if (powerOffRecords == null) {
                        return Stream.empty();
                    }
                    // 过滤并生成匹配的结果记录
                    return powerOffRecords.stream()
                            .filter(powerOff -> disconnect.getStartTime().after(powerOff.getStartTime())
                                    && (powerOff.getEndTime() == null || disconnect.getStartTime().before(powerOff.getEndTime())))
                            .map(powerOff -> disconnect.getStationId());
                })
                .collect(Collectors.toSet());
    }

    /**
     * CALL SP_PatrolStationPowerOffRecordNew('tt_StationPowerOffRecordNew', retVal);
     */
    private List<StationEventRecordDTO> getPatrolStationPowerOffRecordNew() {
        Set<Integer> StandardDicIdSet = Set.of(1018, 1037, 18011, 77001, 92001);
        List<Integer> stationIds = patrolStandardTaskMapper.getPatrolPowerOffStationId();
        List<StationEventRecordDTO> activeEventList = activeEventManager.queryAllActiveEvents()
                .stream()
                .filter(e -> Objects.nonNull(e.getStationId()) && stationIds.contains(e.getStationId()))
                .filter(e -> Objects.nonNull(e.getStandardAlarmNameId()) && StandardDicIdSet.contains(e.getStandardAlarmNameId()))
                .sorted(Comparator.comparing(ActiveEvent::getStationId, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(ActiveEvent::getStartTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .map(m -> StationEventRecordDTO.convert(m, ChronoUnit.SECONDS))
                .collect(Collectors.toList());
        Integer lastStationId = null;
        Date lastEndTime = null;
        // 使用 Iterator 遍历并处理列表
        Iterator<StationEventRecordDTO> iterator = activeEventList.iterator();
        while (iterator.hasNext()) {
            StationEventRecordDTO current = iterator.next();
            if (current.getStationId().equals(lastStationId)) {
                // 同一站点
                if (lastEndTime == null || !current.getStartTime().after(lastEndTime)) {
                    // 时间重叠或相邻，删除当前事件
                    iterator.remove();
                } else {
                    // 更新为新的时间段
                    lastStationId = current.getStationId();
                    lastEndTime = current.getEndTime();
                }
            } else {
                // 不同站点，更新为当前事件
                lastStationId = current.getStationId();
                lastEndTime = current.getEndTime();
            }
        }
        return activeEventList;
    }

    private List<StationEventRecordDTO> getPatrolStationDisconnectRecordNew() {
        List<Integer> stationIds = patrolStandardTaskMapper.getPatrolPowerOffStationId();
        List<Integer> stationIds2 = patrolStandardTaskMapper.getPatrolDisconnectRecordStationId();
        stationIds.removeAll(stationIds2);
        List<StationEventRecordDTO> activeEventList = activeEventManager.queryAllActiveEvents()
                .stream()
                .filter(e -> Objects.nonNull(e.getStationId()) && stationIds.contains(e.getStationId()))
                .filter(e -> Objects.nonNull(e.getBaseTypeId()) && 1301000000L < e.getBaseTypeId() && e.getBaseTypeId() < 1302000000L)
                .sorted(Comparator.comparing(ActiveEvent::getStationId, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(ActiveEvent::getStartTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .map(m -> StationEventRecordDTO.convert(m, ChronoUnit.MINUTES))
                .collect(Collectors.toList());
        List<StationEventRecordDTO> activeEventList2 = activeEventManager.queryAllActiveEvents()
                .stream()
                .filter(e -> Objects.nonNull(e.getStationId()) && stationIds2.contains(e.getStationId()))
                .filter(e -> Objects.nonNull(e.getBaseTypeId()) && 1301000000L < e.getBaseTypeId() && e.getBaseTypeId() < 1302000000L)
                .sorted(Comparator.comparing(ActiveEvent::getStationId, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(ActiveEvent::getStartTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .map(m -> StationEventRecordDTO.convert(m, ChronoUnit.MINUTES))
                .collect(Collectors.toList());
        Integer lastStationId = null;
        Date lastEndTime = null;
        // 使用 Iterator 遍历并处理列表
        Iterator<StationEventRecordDTO> iterator = activeEventList2.iterator();
        while (iterator.hasNext()) {
            StationEventRecordDTO current = iterator.next();
            if (current.getStationId().equals(lastStationId)) {
                // 同一站点
                if (lastEndTime == null || !current.getStartTime().after(lastEndTime)) {
                    // 时间重叠或相邻，删除当前事件
                    iterator.remove();
                } else {
                    // 更新为新的时间段
                    lastStationId = current.getStationId();
                    lastEndTime = current.getEndTime();
                }
            } else {
                // 不同站点，更新为当前事件
                lastStationId = current.getStationId();
                lastEndTime = current.getEndTime();
            }
        }
        activeEventList.addAll(activeEventList2);
        return activeEventList;
    }

    public void processDataWithRule(List<PatrolSignalDTO> signalTable, List<PatrolRuleJobDTO> ruleTable) {
        Date createTime = new Date();
        for (PatrolSignalDTO signal : signalTable) {
            signal.setLimitDown(null);
            signal.setLimitDownCalOpId(0);
            signal.setLimitUp(null);
            signal.setLimitUpCalOpId(0);
            signal.setWarningLevelId(null);
            signal.setByPercentage(0);
            signal.setRatedValue(null);
            signal.setReasonable(1); // 默认合理 (1:合理，0:不合理)
            signal.setCreateTime(createTime);
            signal.setIsSave(1);  //是否存库 (1: 存，0:不存)
            // 生成 UUID
            signal.setSn(UUID.randomUUID().toString());
            signal.setIsPowerOffAlarm("");
        }
        if (ruleTable == null || ruleTable.isEmpty()) {
            return;
        }
        // 规则按照等级排序
        ruleTable = ruleTable.stream()
                .filter(rule -> rule.getWarningLevelId() != null && rule.getWarningLevelId() >= 1 && rule.getWarningLevelId() <= 4)
                .sorted(Comparator.comparing(PatrolRuleJobDTO::getWarningLevelId))
                .toList();

        // 信号，规则校验
        for (PatrolSignalDTO signal : signalTable) {
            if (signal.getSignalCategory() == null || (signal.getSignalCategory() != 1 && signal.getSignalCategory() != 2)) {
                continue;
            }
            if (signal.getSignalValue() == null) {
                continue;
            }
            Double signalValue = Double.valueOf(signal.getSignalValue());
            for (PatrolRuleJobDTO rule : ruleTable) {
                Double limitDownCal = 0.0, limitUpCal = 0.0;
                Boolean limitDownRes = null, limitUpRes = null;
                if (rule.getByPercentage() != null && rule.getByPercentage() == 1 && rule.getRatedValue() == null) {
                    continue;
                }
                // 下限计算
                if (rule.getLimitDown() != null) {
                    limitDownCal = rule.getByPercentage() != null && rule.getByPercentage() == 1 && rule.getRatedValue() != null
                            ? rule.getLimitDown() * rule.getRatedValue() / 100.0
                            : rule.getLimitDown();

                    limitDownRes = compareValues(limitDownCal, signalValue, rule.getLimitDownCalOpId());
                }

                // 上限计算
                if (rule.getLimitUp() != null) {
                    limitUpCal = rule.getByPercentage() != null && rule.getByPercentage() == 1 && rule.getRatedValue() != null
                            ? rule.getLimitUp() * rule.getRatedValue() / 100.0
                            : rule.getLimitUp();

                    limitUpRes = compareValues(signalValue, limitUpCal, rule.getLimitUpCalOpId());
                }
                if ((limitDownRes != null && limitDownRes && limitUpRes != null && limitUpRes) ||
                        (limitDownRes == null && limitUpRes != null && limitUpRes) ||
                        (limitUpRes == null && limitDownRes != null && limitDownRes)) {
                    signal.setLimitDown(rule.getLimitDown());
                    signal.setLimitDownCalOpId(rule.getLimitDownCalOpId());
                    signal.setLimitUp(rule.getLimitUp());
                    signal.setLimitUpCalOpId(rule.getLimitUpCalOpId());
                    signal.setByPercentage(rule.getByPercentage());
                    signal.setRatedValue(rule.getRatedValue());
                    signal.setWarningLevelId(rule.getWarningLevelId());
                    signal.setReasonable(0);
                    break;
                }
            }
        }
    }

    private Boolean compareValues(Double value1, Double value2, Integer operationId) {
        if (operationId == null || value1 == null || value2 == null) {
            return null;
        }
        return switch (operationId) {
            case 1 -> // 小于
                    value1 < value2;
            case 2 -> // 小于等于
                    value1 <= value2;
            case 3 -> // 等于
                    value1.equals(value2);
            case 4 -> // 不等于
                    !value1.equals(value2);
            default -> null;
        };
    }

}
