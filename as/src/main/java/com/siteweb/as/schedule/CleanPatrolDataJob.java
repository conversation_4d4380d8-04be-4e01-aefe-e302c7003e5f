package com.siteweb.as.schedule;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.entity.PatrolExrecord;
import com.siteweb.as.mapper.PatrolAllRecordMapper;
import com.siteweb.as.mapper.PatrolExrecordMapper;
import com.siteweb.common.util.GetBeanUtil;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 定时清除自动巡检预警信息
 *
 * Creation Date: 2025/1/9
 */
@Component
@Slf4j
public class CleanPatrolDataJob implements Job {
    private PatrolExrecordMapper patrolExrecordMapper = GetBeanUtil.getBean(PatrolExrecordMapper.class);
    private PatrolAllRecordMapper patrolAllRecordMapper = GetBeanUtil.getBean(PatrolAllRecordMapper.class);
    private HAStatusService haStatusService = GetBeanUtil.getBean(HAStatusService.class);
    private SystemConfigService systemConfigService= GetBeanUtil.getBean(SystemConfigService.class);


    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            log.info("CleanPatrolDataJob : HAStatus is BACKUP.");
            return;
        }
        try {
            // 异常数据清除
            int expiredEx = 31;
            String expiredExStr = getSystemConfigValue(SystemConfigEnum.PATROL_TIMERTASKEXPIRED_DAYSEX);
            if (CharSequenceUtil.isNotBlank(expiredExStr)) {
                expiredEx = Integer.parseInt(expiredExStr);
                if (expiredEx <= 0 || expiredEx > 62) {
                    expiredEx = 31;
                }
            }
            DateTime dateTime = DateUtil.offsetDay(DateUtil.date(), -expiredEx);
            patrolExrecordMapper.delete(Wrappers.lambdaQuery(PatrolExrecord.class).lt(PatrolExrecord::getCreateTime, dateTime));
            // 全量数据清除
            cleaningDbRecordAll();
        } catch (Exception e) {
            // 记录错误日志
            log.error("CleanPatrolDataJob error:", e);
        }
    }

    public String getSystemConfigValue(SystemConfigEnum systemConfigEnum) {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(systemConfigEnum.getSystemConfigKey());
        if (Objects.nonNull(systemConfig) && CharSequenceUtil.isNotBlank(systemConfig.getSystemConfigValue())) {
            return systemConfig.getSystemConfigValue().trim();
        }
        return null;
    }


    public void cleaningDbRecordAll() {
        try {
            int expiredAll = 365;
            String expiredAllStr = getSystemConfigValue(SystemConfigEnum.PATROL_TIMERTASKEXPIRED_DAYSALL);
            if (CharSequenceUtil.isNotBlank(expiredAllStr)) {
                expiredAll = Integer.parseInt(expiredAllStr);
                if (expiredAll <= 0 || expiredAll > 365) {
                    expiredAll = 365;
                }
            }
            DateTime dateTime = DateUtil.offsetDay(DateUtil.date(), -expiredAll);
            String createTime = DateUtil.formatDateTime(dateTime);
            for (int i = 1; i <= 12; i++) {
                // 动态生成表名和 SQL 语句
                String tableName = "TBL_PatrolAllRecord_" + i;
                String sql = String.format(
                        "DELETE FROM %s WHERE CreateTime < '%s'",
                        tableName, createTime
                );
                // 调用 Mapper 方法执行 SQL
                patrolAllRecordMapper.cleanOldRecords(sql);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to clean old records", e);
        }
    }
}
