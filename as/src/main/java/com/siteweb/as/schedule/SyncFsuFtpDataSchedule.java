package com.siteweb.as.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.entity.IcsFsuFtpDownloadInfo;
import com.siteweb.as.mapper.IcsFsuFtpDownloadInfoMapper;
import com.siteweb.as.net.transfer.FileTransferClient;
import com.siteweb.as.net.transfer.FileTransferClientFactory;
import com.siteweb.as.util.DirectoryComparatorUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.properties.FeatureEnableProperties;
import com.siteweb.monitoring.dto.UserCredentialsDTO;
import com.siteweb.monitoring.mapper.MonitorUnitMapper;
import com.siteweb.monitoring.service.MonitorUnitExtendService;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.dualhost.aspect.MasterHostOnly;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

@Component
@Slf4j
public class SyncFsuFtpDataSchedule {
    /**
     * 文件排除列表
     */
    private static final List<String> FILES_TO_EXCLUDE = List.of("iptables", "lib", "fifo*");
    @Autowired
    FileServerConfig fileServerConfig;
    private static final String REMOTE_BACKUP_PATH = "/home/<USER>";
    private static final String LOCAL_DOWNLOAD_PATH = "/home/<USER>/FTP";
    @Autowired
    MonitorUnitMapper monitorUnitMapper;
    @Autowired
    MonitorUnitExtendService monitorUnitExtendService;
    @Autowired
    IcsFsuFtpDownloadInfoMapper icsFsuFtpDownloadInfoMapper;
    @Autowired
    FeatureEnableProperties featureEnableProperties;
    @Autowired
    SystemConfigService systemConfigService;



    @MasterHostOnly
    @Scheduled(cron = "0 0 6 * * *")
    public void syncFsuFile() {
        if (!systemConfigService.findBooleanValue(SystemConfigEnum.VERSION_MANAGER_FSU_BACKUP_ENABLE)) {
            log.info("没有启用fsu同步功能");
            return;
        }
        List<String> fsuIpList = monitorUnitMapper.getFsuIpList();
        if (CollUtil.isEmpty(fsuIpList)) {
            log.info("不存在fsu 无需同步fsu的文件信息");
            return;
        }
        Date now = new Date();
        String filePath = DateUtil.format(now, DatePattern.PURE_DATETIME_PATTERN);
        for (String ip : fsuIpList) {
            try {
                UserCredentialsDTO userCredentials = monitorUnitExtendService.findUserCredentials(ip);
                if (Objects.isNull(userCredentials)) {
                    log.error("{},配置没有下发保存过采集器的用户名与密码，无法备份采集器", ip);
                    continue;
                }
                //下载备份
                String localPath = fileServerConfig.getRootPath() + GlobalConstants.PATH_SEPARATOR + LOCAL_DOWNLOAD_PATH + GlobalConstants.PATH_SEPARATOR + ip + GlobalConstants.PATH_SEPARATOR + filePath;
                downLoadBackUp(ip, localPath, userCredentials);
                //与其他目录比较，如果都一样则直接continue,并删除刚刚备份下来的目录
                Path backupDir = Path.of(localPath);
                if (DirectoryComparatorUtil.isBackupDuplicate(backupDir)) {
                    FileUtil.del(backupDir);
                    continue;
                }
                recordBackUpInfo(ip, now, localPath, filePath);
                //只需要保留七个备份
                keepOnlySevenMostRecentBackups(ip);
            } catch (Exception e) {
                log.error("采集器Ip:{},同步失败", ip);
                log.error("同步错误具体信息", e);
            }
        }
    }


    private void downLoadBackUp(String ip, String localPath, UserCredentialsDTO userCredentials) {
        try (FileTransferClient fileTransferClient = FileTransferClientFactory.createClient(ip, userCredentials.getUsername(), userCredentials.getPassword(), userCredentials.getPort(), userCredentials.getProtocol())) {
            fileTransferClient.login();
            fileTransferClient.downloadDirectory(REMOTE_BACKUP_PATH, localPath, FILES_TO_EXCLUDE);
        } catch (IOException e) {
            log.error("备份ip:{}失败", ip);
            log.error("备份错误信息", e);
        }
    }

    /**
     * 只保留最近的七个备份
     * @param ip fsuIp
     */
    private void keepOnlySevenMostRecentBackups(String ip) {
        String beforeBackup = fileServerConfig.getRootPath() + GlobalConstants.PATH_SEPARATOR + LOCAL_DOWNLOAD_PATH + GlobalConstants.PATH_SEPARATOR + ip;
        File backPath = new File(beforeBackup);
        if (!FileUtil.exist(backPath)) {
            return;
        }

        // 列出目录下的所有文件和子目录
        File[] subDirs = FileUtil.ls(backPath.getAbsolutePath());

        // 如果备份数量不超过7个，则不需要删除
        if (subDirs.length <= 7) {
            return;
        }

        // 将目录按最后修改时间排序（从新到旧）
        Arrays.sort(subDirs, Comparator.comparing(File::lastModified).reversed());
        // 删除多余的备份目录（保留最新的7个）
        for (int i = 7; i < subDirs.length; i++) {
            FileUtil.del(subDirs[i]);
            icsFsuFtpDownloadInfoMapper.delete(Wrappers.lambdaQuery(IcsFsuFtpDownloadInfo.class)
                                                       .eq(IcsFsuFtpDownloadInfo::getFsuIp, ip)
                                                       .eq(IcsFsuFtpDownloadInfo::getFileName, subDirs[i].getName()));
        }
    }


    /**
     * 记录备份记录
     * @param ip fsuIp
     * @param now 备份事件
     * @param localPath 备份到的本地路径
     * @param directoryName 目录名称
     */
    private void recordBackUpInfo(String ip, Date now, String localPath, String directoryName) {
        IcsFsuFtpDownloadInfo icsFsuFtpDownloadInfo = IcsFsuFtpDownloadInfo.builder()
                                                                           .downLoadTime(now)
                                                                           .serverIp(featureEnableProperties.getServerIp())
                                                                           .fsuIp(ip)
                                                                           .downLoadPath(localPath)
                                                                           .fileName(directoryName)
                                                                           .fileSize("")
                                                                           .build();
        icsFsuFtpDownloadInfoMapper.insert(icsFsuFtpDownloadInfo);
    }
}
