package com.siteweb.as.schedule;

import com.siteweb.as.entity.PatrolAllRecord;
import com.siteweb.as.entity.PatrolExrecord;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Creation Date: 2025/1/9
 */
@Component
@EnableScheduling
@Slf4j
public class SavePatrolDataSchedule {
    // 使用线程安全的队列来存储共享数据
    private static final LinkedBlockingQueue<PatrolAllRecord> allDataQueue = new LinkedBlockingQueue<>();
    private static final LinkedBlockingQueue<PatrolExrecord> exDataQueue = new LinkedBlockingQueue<>();
    public static final String GROUP_NAME = "SavePatrolDataJob";
    @Autowired
    Scheduler scheduler;
    @Autowired
    private HAStatusService haStatusService;
    // 每次获取的批量大小
    private static final int BATCH_SIZE = 1000;

    // 模拟向队列中添加数据
    public static void addExRecords(List<PatrolExrecord> records) {
        exDataQueue.addAll(records);
    }

    public static void addAllRecords(List<PatrolAllRecord> records) {
        allDataQueue.addAll(records);
    }

    @PostConstruct
    public void initializeSampleJob() throws SchedulerException {
        if(!haStatusService.isMasterHost()) {
            log.info("SavePatrolDataSchedule : HAStatus is BACKUP.");
            return;
        }
        TriggerKey triggerKey = new TriggerKey("savePatrolDataTrigger", GROUP_NAME);
        if (scheduler.checkExists(triggerKey)) {
            return;
        }
        // 创建任务
        JobDetail job = JobBuilder.newJob(SavePatrolDataJob.class)
                .withIdentity("savePatrolDataJob", GROUP_NAME)
                .build();
        // 创建触发器
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("savePatrolDataTrigger", GROUP_NAME)
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInSeconds(30)
                        .repeatForever())
                .build();
        scheduler.scheduleJob(job, trigger);
    }

    // 从队列中批量取出数据
    public static List<PatrolExrecord> fetchExBatch() {
        List<PatrolExrecord> batch = new ArrayList<>(BATCH_SIZE); // 初始化容量为 batchSize
        exDataQueue.drainTo(batch, BATCH_SIZE); // 批量获取最多 batchSize 条记录
        return batch;
    }

    public static List<PatrolAllRecord> fetchAllBatch() {
        List<PatrolAllRecord> batch = new ArrayList<>(BATCH_SIZE); // 初始化容量为 batchSize
        allDataQueue.drainTo(batch, BATCH_SIZE); // 批量获取最多 batchSize 条记录
        return batch;
    }
}
