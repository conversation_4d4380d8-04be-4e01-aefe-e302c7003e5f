package com.siteweb.as.schedule;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.as.entity.PatrolAllRecord;
import com.siteweb.as.entity.PatrolExrecord;
import com.siteweb.as.mapper.PatrolAllRecordMapper;
import com.siteweb.as.mapper.PatrolExrecordMapper;
import com.siteweb.common.util.GetBeanUtil;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 对PatrolTaskJob的数据进行处理
 * Creation Date: 2025/1/9
 */
@Component
@Slf4j
public class SavePatrolDataJob implements Job {
    private PatrolExrecordMapper patrolExrecordMapper = GetBeanUtil.getBean(PatrolExrecordMapper.class);
    private PatrolAllRecordMapper patrolAllRecordMapper = GetBeanUtil.getBean(PatrolAllRecordMapper.class);
    private HAStatusService haStatusService = GetBeanUtil.getBean(HAStatusService.class);

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            log.info("SavePatrolDataJob : HAStatus is BACKUP.");
            return;
        }
        // 处理队列中的数据并存储到数据库
        try {
            // 异常数据存库
            List<PatrolExrecord> exBatch = SavePatrolDataSchedule.fetchExBatch();
            if (CollUtil.isNotEmpty(exBatch)) {
                saveExDatabase(exBatch);
            }
            // 全量数据存库
            List<PatrolAllRecord> patrolAllRecords = SavePatrolDataSchedule.fetchAllBatch();
            if (CollUtil.isNotEmpty(patrolAllRecords)) {
                saveAllDatabase(patrolAllRecords);
            }
        } catch (Exception e) {
            // 记录错误日志
            log.error("SavePatrolDataJob error:", e);
        }
    }

    // 批量插入
    public void saveAllDatabase(List<PatrolAllRecord> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        // 按月份分组，确保同一月份的记录插入到同一张表
        records.stream()
                .collect(Collectors.groupingBy(patrolAllRecord -> getTableName(patrolAllRecord.getCreateTime().toInstant()
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDateTime())))
                .forEach((tableName, groupedRecords) ->
                        patrolAllRecordMapper.batchInsert(tableName, groupedRecords));
    }

    // 根据时间获取动态表名
    private String getTableName(LocalDateTime createTime) {
        int month = createTime.getMonthValue();
        return "tbl_patrolallrecord_" + month;
    }


    // 保存异常数据
    private void saveExDatabase(List<PatrolExrecord> records) {
        patrolExrecordMapper.insert(records);
    }
}
