package com.siteweb.as.schedule;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.entity.PatrolCronExpression;
import com.siteweb.as.entity.PatrolTask;
import com.siteweb.as.mapper.PatrolCronExpressionMapper;
import com.siteweb.as.mapper.PatrolTaskMapper;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * 自动巡检定时任务
 */
@Component
@EnableScheduling
@Slf4j
public class PatrolTaskSchedule {
    @Autowired
    PatrolCronExpressionMapper patrolCronExpressionMapper;
    @Autowired
    PatrolTaskMapper patrolTaskMapper;
    private static final String GROUP_NAME = "PatrolTask";

    @Autowired
    private HAStatusService haStatusService;
    @Autowired
    Scheduler scheduler;

    @Value("${patrol.enableStandardSignal:#{null}}")
    Boolean standardSignal;

    /**
     * 初始化任务
     *
     * @throws SchedulerException 异常
     */
    @PostConstruct
    public void initializeSampleJob() throws SchedulerException {
        if (!haStatusService.isMasterHost()) {
            log.info("PatrolTaskSchedule : HAStatus is BACKUP.");
            return;
        }
        List<PatrolTask> patrolTasks = patrolTaskMapper.selectList(Wrappers.emptyWrapper());
        for (PatrolTask patrolTask : patrolTasks) {
            addJob(patrolTask);
        }
    }

    /**
     * 添加任务
     *
     * @throws SchedulerException 异常
     */
    public void addJob(PatrolTask patrolTask) throws SchedulerException {
        if (!haStatusService.isMasterHost()) {
            log.info("PatrolTaskSchedule : HAStatus is BACKUP.");
            return;
        }
        String jobName = patrolTask.getTaskId().toString();
        TriggerKey triggerKey = new TriggerKey(jobName, GROUP_NAME);
        if (scheduler.checkExists(triggerKey)) {
            return;
        }
        PatrolCronExpression patrolCronExpression = patrolCronExpressionMapper.selectById(patrolTask.getCronId());
        if (Objects.isNull(patrolCronExpression)) {
            throw new BusinessException("自动巡检巡检时间不能为空 taskId:" + patrolTask.getTaskId());
        }
        JobDataMap jobDataMap = new JobDataMap(BeanUtil.beanToMap(patrolTask));
        JobDetail jobDetail;
        if(Boolean.TRUE.equals(standardSignal)){
             jobDetail = JobBuilder.newJob(PatrolStandardTaskJob.class)
                    .withIdentity(jobName, GROUP_NAME)
                    .setJobData(jobDataMap)
                    .build();

        }else {
             jobDetail = JobBuilder.newJob(PatrolTaskJob.class)
                    .withIdentity(jobName, GROUP_NAME)
                    .setJobData(jobDataMap)
                    .build();
        }
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(jobName, GROUP_NAME)
                .withSchedule(CronScheduleBuilder.cronSchedule(patrolCronExpression.getCronExpression()))
                .build();
        scheduler.scheduleJob(jobDetail, trigger);
    }

    /**
     * 更新任务调度
     *
     * @throws SchedulerException 异常
     */
    public void updateJob(PatrolTask patrolTask) throws SchedulerException {
        if (!haStatusService.isMasterHost()) {
            log.info("PatrolTaskSchedule : HAStatus is BACKUP.");
            return;
        }
        String jobName = patrolTask.getTaskId().toString();
        TriggerKey triggerKey = new TriggerKey(jobName, GROUP_NAME);
        if (scheduler.checkExists(triggerKey)) {
            deleteJob(jobName);
            addJob(patrolTask);
        } else {
            // 如果触发器不存在，则创建任务
            addJob(patrolTask);
        }
    }

    /**
     * 删除任务
     *
     * @param jobName 任务名称
     * @throws SchedulerException 异常
     */
    public void deleteJob(String jobName) throws SchedulerException {
        if (!haStatusService.isMasterHost()) {
            log.info("PatrolTaskSchedule : HAStatus is BACKUP.");
            return;
        }
        JobKey jobKey = new JobKey(jobName, GROUP_NAME);
        scheduler.deleteJob(jobKey);
    }


    /**
     * 关闭调度器
     *
     * @throws SchedulerException 异常
     */
    public void shutdown() throws SchedulerException {
        if (!scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }

}
