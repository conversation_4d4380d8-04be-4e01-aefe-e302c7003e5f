package com.siteweb.as.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ObjectSelectorRequestVO {

    @JsonProperty("QueryInformations")
    private QueryInformations queryInformations;
    @Data
    public static class QueryInformations {
        @JsonProperty("ParameterQueryInformation")
        private List<ParameterQueryInformation> parameterQueryInformation;
    }
    @Data
    public static class ParameterQueryInformation {
        @JsonProperty("ParameterName")
        private String parameterName;

        @JsonProperty("QueryInformation")
        private QueryInformation queryInformation;
    }
    @Data
    public static class QueryInformation {
        @JsonAlias("QueryInformationId")
        private String queryInformationId;

        @JsonAlias("OrderByList")
        private List<OrderBy> orderByList;

        @JsonAlias("ObjectTypeName")
        private String objectTypeName;

        private List<QueryComparison> queryComparisons;

        @JsonAlias("MultiValueComparison")
        private List<MultiValueComparison> multiValueComparisons;

        @JsonAlias("SingleValueComparison")
        private List<SingleValueComparison> singleValueComparisons;

        @JsonAlias("PagingInformation")
        private PagingInformation pagingInformation;


    }
    @Data
    public static class OrderBy {
        private String propertyName;
        private String orderByType;
    }
    @Data
    public static class QueryComparison {
        private String propertyName;
        private String compareType;

        @JsonProperty("ValueType")
        private String ValueType;

        private String relationShip;
        private String relatedPropertyName;
        private QueryInformation queryInformation;
    }

    @Data
    public static class MultiValueComparison {
        @JsonAlias("PropertyName")
        private String propertyName;

        @JsonAlias("CompareType")
        private String compareType;

        @JsonProperty("ValueType")
        private String ValueType;

        @JsonAlias("ComparisonRelationShip")
        private String relationShip;

        @JsonProperty("Values")
        private String Values;
    }

    @Data
    public static class SingleValueComparison {
        @JsonAlias("PropertyName")
        private String propertyName;

        @JsonAlias("CompareType")
        private String compareType;

        @JsonProperty("ValueType")
        private String ValueType;

        @JsonAlias("ComparisonRelationShip")
        private String relationShip;

        @JsonProperty("Value")
        private Object Value;
    }

    @Data
    public static class PagingInformation {
        private int currentPageIndex;
        private int pageSize;
    }
}
