package com.siteweb.as.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 屏蔽时间组设置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaskTimeGroupVO {

    @JsonProperty("TimeGroupSpans")
    private List<TimeGroupSpan> timeGroupSpans;

    @JsonProperty("DataId")
    private Integer dataId;
    @JsonProperty("GroupCategory")
    private Integer groupCategory=1;
    @JsonAlias(value = {"Reason","reason"})
    @JsonProperty("Reason")
    private String reason;
    @JsonProperty("SequenceId")
    private Integer sequenceId=0;
    @JsonAlias(value = {"Type","type"})
    @JsonProperty("Type")
    private Integer type=1;
    @JsonAlias(value = {"Exception","exception"})
    @JsonProperty("Exception")
    private Boolean exception;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @JsonAlias(value = {"Starttime","startTime"})
    @JsonProperty("Starttime")
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @JsonAlias(value = {"Endtime","endTime"})
    @JsonProperty("Endtime")
    private Date endTime;
    @JsonProperty("UserId")
    private Integer userId=-1;
    @JsonProperty("UserName")
    private String userName;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeGroupSpan {
        @JsonProperty("TimeSpanBool")
        private List<Boolean> timeSpanBool;

        @JsonProperty("EndTime")
        private Date endTime;

        @JsonProperty("StartTime")
        private Date startTime;

        @JsonProperty("DataId")
        private Integer dataId=0;

        @JsonProperty("SequenceId")
        private Integer sequenceId=0;

        @JsonProperty("GroupId")
        private Integer groupId=0;
        /**
         * 星期设置
         * 星期一 == 0
         * 星期天 == 6
         */
        @JsonProperty("week")
        private Integer week;

    }
}