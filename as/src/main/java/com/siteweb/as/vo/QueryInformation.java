package com.siteweb.as.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class QueryInformation {
    @JsonAlias("QueryInformationId")
    private String queryInformationId;

    @JsonAlias("OrderByList")
    private List<OrderBy> orderByList;

    @JsonAlias("ObjectTypeName")
    private String objectTypeName;

    private List<QueryComparison> queryComparisons;

    @JsonAlias("MultiValueComparison")
    private List<MultiValueComparison> multiValueComparisons;

    @JsonAlias("SingleValueComparison")
    private List<SingleValueComparison> singleValueComparisons;

    private PagingInformation pagingInformation;
    Integer stationId;
    Integer equipmentId;
    Integer signalId;
    @Data
    public static class OrderBy {
        private String propertyName;
        private String orderByType;
    }
    @Data
    public static class QueryComparison {
        private String propertyName;
        private String compareType;

        @JsonProperty("ValueType")
        private String ValueType;

        private String relationShip;
        private String relatedPropertyName;
        private QueryInformation queryInformation;
    }

    @Data
    public static class MultiValueComparison {
        @JsonAlias("PropertyName")
        private String propertyName;

        @JsonAlias("CompareType")
        private String compareType;

        @JsonProperty("ValueType")
        private String ValueType;

        @JsonAlias("RelationShip")
        private String relationShip;

        @JsonAlias({"Values","values"})
        private List<String> Values; // 注意，这里原JSON中的"Values"应该是"values"，因为JSON属性名是区分大小写的
        public String GetStringByValueList()
        {
            String Ids = "";
            for(String str:Values) {
                if(Ids.length() == 0)
                    Ids += str;
                else
                    Ids += ","+str;
            }
            return Ids;
        }
    }

    @Data
    public static class SingleValueComparison {
        @JsonAlias("PropertyName")
        private String propertyName;

        @JsonAlias("CompareType")
        private String compareType;

        @JsonProperty("ValueType")
        @JsonAlias({"ValueType","valueType"})
        private String ValueType;

        @JsonAlias("RelationShip")
        private String relationShip;

        @JsonAlias({"Values","values","Value"})
        private String Value; // 注意，Value的类型取决于JSON中的实际值类型，这里使用Object作为通用类型
    }

    @Data
    public static class PagingInformation {
        private int currentPageIndex;
        private int pageSize;
    }

    public QueryInformation(ObjectSelectorRequestVO.QueryInformation information){
// 使用BeanUtils.copyProperties来复制相同字段
        BeanUtils.copyProperties(information, this);

        // 手动处理 MultiValueComparison 和 SingleValueComparison
        if (information.getMultiValueComparisons() != null) {
            this.multiValueComparisons = information.getMultiValueComparisons().stream().map(multiValue -> {
                MultiValueComparison mv = new MultiValueComparison();
                BeanUtils.copyProperties(multiValue, mv);
                // 处理 Values 字段
                if (multiValue.getValues() != null) {
                    mv.setValues(Arrays.asList(multiValue.getValues().split(",")));
                }
                return mv;
            }).collect(Collectors.toList());
        }

        if (information.getSingleValueComparisons() != null) {
            this.singleValueComparisons = information.getSingleValueComparisons().stream().map(singleValue -> {
                SingleValueComparison sv = new SingleValueComparison();
                BeanUtils.copyProperties(singleValue, sv);
                // 处理 Value 字段
                if (singleValue.getValue() != null) {
                    // 如果 Value 是 String，直接赋值；如果是其他类型，根据需要处理
                    sv.setValue((String) singleValue.getValue());
                }
                return sv;
            }).collect(Collectors.toList());
        }

        // 处理 PagingInformation
        if (information.getPagingInformation() != null) {
            this.pagingInformation = new PagingInformation();
            BeanUtils.copyProperties(information.getPagingInformation(), this.pagingInformation);
        }

    }
}
