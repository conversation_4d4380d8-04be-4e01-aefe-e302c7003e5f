package com.siteweb.as.vo;

import cn.hutool.core.lang.tree.Tree;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 */
@Data
@NoArgsConstructor
public class ValueLabelTreeNodeVO {
    Integer value;
    String label;
    List<ValueLabelTreeNodeVO> children;
    Integer parentId;
    public ValueLabelTreeNodeVO (Tree<Integer> treenode){
        this.value = treenode.getId();
        this.parentId = treenode.getParentId();
        this.label = (String) treenode.getName();
    }
}
