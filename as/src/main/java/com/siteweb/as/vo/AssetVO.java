package com.siteweb.as.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetVO {
    private String assetId;
    private String assetName;
    private String assetNo;
    private String assetTypeId;
    private String assetTypeName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date usedDate;
    private String capacity;
    private String style;
    private String brand;
    private String stateId;
    private String stateName;
    private String faultDesc;
    private String processMode;
    private String lifeTime;
}
