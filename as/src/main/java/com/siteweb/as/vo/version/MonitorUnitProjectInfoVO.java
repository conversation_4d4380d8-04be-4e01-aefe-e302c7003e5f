package com.siteweb.as.vo.version;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class MonitorUnitProjectInfoVO {
    /**
     * 站点 ID
     */
    private Integer stationId;
    /**
     * 站点名称
     */
    private String stationName;
    /**
     * 监控单元 ID
     */
    private Integer monitorUnitId;
    /**
     * 监控单元名称
     */
    private String monitorUnitName;
    /**
     * IP 地址
     */
    private String ipAddress;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 安装时间
     */
    private Date installTime;
}
