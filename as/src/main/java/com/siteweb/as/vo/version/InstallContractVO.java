package com.siteweb.as.vo.version;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class InstallContractVO {
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 站点数量
     */
    private Integer stationCount;
    /**
     * FSU数量
     */
    private Integer fsuCount;
    /**
     * 设备数量
     */
    private Integer equipmentCount;
    /**
     * 开始日期
     */
    private Date primaryDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 质保起点
     */
    private String qualityStartPoint;
    /**
     * 质保期(月)
     */
    private Integer qualityPeriod;
    /**
     * 质量条款
     */
    private String qualityTerms;
}
