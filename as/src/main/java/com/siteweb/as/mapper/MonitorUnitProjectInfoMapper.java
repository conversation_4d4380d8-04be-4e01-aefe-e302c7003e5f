package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.FSUProjectInfoDTO;
import com.siteweb.as.entity.MonitorUnitProjectInfo;
import com.siteweb.as.vo.version.MonitorUnitProjectInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface MonitorUnitProjectInfoMapper extends BaseMapper<MonitorUnitProjectInfo> {
    Page<MonitorUnitProjectInfoVO> findContractInfoPage(@Param("page") Page<MonitorUnitProjectInfoVO> page, @Param("stationIds") Collection<Integer> stationIds, @Param("fsuProjectInfoDTO") FSUProjectInfoDTO fsuProjectInfoDTO);

    MonitorUnitProjectInfoVO findContractInfoByMonitorUnitId(@Param("monitorUnitId") Integer monitorUnitId);

    List<MonitorUnitProjectInfoVO> findContractInfoList(@Param("stationIds") Collection<Integer> stationIds, @Param("fsuProjectInfoDTO") FSUProjectInfoDTO fsuProjectInfoDTO);

    int batchUpdateContractInfo(@Param("list") List<FSUProjectInfoDTO> fsuProjectInfoDTOList);
}
