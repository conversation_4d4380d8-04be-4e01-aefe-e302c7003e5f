package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.IcsPlatformQrcodeDTO;
import com.siteweb.as.entity.IcsPlatformQrCode;
import com.siteweb.as.vo.version.IcsPlatformQrcodeVO;
import org.apache.ibatis.annotations.Param;

public interface IcsPlatformQrCodeMapper extends BaseMapper<IcsPlatformQrCode> {
    Page<IcsPlatformQrcodeVO> findPage(@Param("page") Page<IcsPlatformQrcodeVO> page, @Param("icsPlatformQrcodeDTO") IcsPlatformQrcodeDTO icsPlatformQrcodeDTO);
}
