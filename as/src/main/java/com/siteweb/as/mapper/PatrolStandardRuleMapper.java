package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.dto.patrol.PatrolRuleDTO;
import com.siteweb.as.dto.patrol.PatrolStandardRuleDTO;
import com.siteweb.as.dto.patrol.StandardEquipmentDTO;
import com.siteweb.as.dto.patrol.StandardSignalDTO;
import com.siteweb.as.entity.PatrolRule;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;
import java.util.Map;

public interface PatrolStandardRuleMapper  extends BaseMapper<PatrolRule> {

    List<StandardSignalDTO> listStandardSignalByEquipment(@Param("equipmentLogicClassId") Integer equipmentLogicClassId,int standardVer);
    List<StandardEquipmentDTO> selectAllStandardEquipment(int standardVer);
    List<PatrolStandardRuleDTO> getAllPatrolStandardRule(int standardVer);
    PatrolStandardRuleDTO getPatrolStandardRule(int standardVer, Integer ruleId);

    List<PatrolStandardRuleDTO> getPatrolStandardRuleByGroup(int standardVer, Integer groupId);
}