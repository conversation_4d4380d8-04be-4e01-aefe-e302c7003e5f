package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.dto.shield.StationShieldDTO;
import com.siteweb.monitoring.entity.StationMask;
import com.siteweb.monitoring.vo.StationMaskVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StationShieldMapper extends BaseMapper<StationMask> {
    Page<StationShieldDTO> findStationShieldPage(@Param("page") Page<StationMaskVO> page, @Param("shieldFilterDTO") ShieldFilterDTO shieldFilterDTO);

    List<StationShieldDTO> findStationShield(@Param("shieldFilterDTO") ShieldFilterDTO shieldFilterDTO);
}
