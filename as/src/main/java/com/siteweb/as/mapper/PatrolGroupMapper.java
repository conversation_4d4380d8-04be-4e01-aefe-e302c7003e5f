package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.dto.patrol.GroupParameterDTO;
import com.siteweb.as.dto.patrol.PatrolGroupDTO;
import com.siteweb.as.dto.patrol.ValueLabelDTO;
import com.siteweb.as.entity.PatrolGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PatrolGroupMapper extends BaseMapper<PatrolGroup> {

    List<PatrolGroupDTO> findPatrolGroups();

    PatrolGroupDTO getPatrolGroup(Integer groupId);

    List<ValueLabelDTO> listStation(@Param("standardVer") int standardVer, @Param("stationTypeIds") int[] stationTypeIds);

    GroupParameterDTO getGroupParameter(Integer groupId);
}
