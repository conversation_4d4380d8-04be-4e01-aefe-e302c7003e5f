package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.dto.patrol.*;
import com.siteweb.as.entity.PatrolGroup;
import com.siteweb.as.entity.PatrolStandardGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PatrolStandardGroupMapper extends BaseMapper<PatrolStandardGroup> {

    List<PatrolStandardGroupDTO> findPatrolStandardGroups();

    PatrolStandardGroupDTO getPatrolStandardGroup(Integer groupId);

    List<ValueLabelDTO> listStation(@Param("standardVer") int standardVer, @Param("stationTypeIds") int[] stationTypeIds);

    GroupParameterDTO getGroupParameter(Integer groupId);
}
