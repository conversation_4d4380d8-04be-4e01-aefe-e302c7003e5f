package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.InstallContractDTO;
import com.siteweb.as.entity.IcsContractInstall;
import com.siteweb.as.vo.version.InstallContractVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IcsContractInstallMapper extends BaseMapper<IcsContractInstall> {
    Page<InstallContractVO> findContractInfoPage(@Param("objectPage") Page<InstallContractVO> objectPage, @Param("installContractDTO") InstallContractDTO installContractDTO);

    List<InstallContractVO> findContractInfoList(@Param("installContractDTO") InstallContractDTO installContractDTO);

    int batchUpdateContractInfo(@Param("list") List<IcsContractInstall> icsContractInstallList);
}
