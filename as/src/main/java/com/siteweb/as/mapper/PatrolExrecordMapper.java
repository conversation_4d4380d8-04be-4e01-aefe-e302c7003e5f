package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.patrol.AlarmInfoDTO;
import com.siteweb.as.dto.patrol.AlarmInfoQueryDTO;
import com.siteweb.as.entity.PatrolExrecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface PatrolExrecordMapper extends BaseMapper<PatrolExrecord> {

    IPage<AlarmInfoDTO> getAllAlarmInfo(@Param("page") Page<AlarmInfoQueryDTO> page, @Param("query") AlarmInfoQueryDTO query);

    List<AlarmInfoDTO> getAlarmInfoExport(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
