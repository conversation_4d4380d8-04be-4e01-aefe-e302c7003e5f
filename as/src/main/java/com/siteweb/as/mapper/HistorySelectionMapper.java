package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.entity.HistorySelection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HistorySelectionMapper extends BaseMapper<HistorySelection> {
    List<HistorySelection> findByUserIdAdnType(@Param("userId") Integer userId, @Param("selectionType") String selectionType);
}
