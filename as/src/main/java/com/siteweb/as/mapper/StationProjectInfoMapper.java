package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.StationProjectInfoDTO;
import com.siteweb.as.entity.StationProjectInfo;
import com.siteweb.as.vo.version.StationProjectInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface StationProjectInfoMapper extends BaseMapper<StationProjectInfo> {
    Page<StationProjectInfoVO> findContractInfoPage(@Param("page") Page<StationProjectInfoVO> page, @Param("stationIds") Collection<Integer> stationIds, @Param("stationProjectInfoDTO") StationProjectInfoDTO stationProjectInfoDTO);

    StationProjectInfoVO findContractInfoByStationId(@Param("stationId") Integer stationId);

    List<StationProjectInfoVO> findContractInfo(@Param("stationIds") Collection<Integer> stationIds, @Param("stationProjectInfoDTO") StationProjectInfoDTO stationProjectInfoDTO);

    int batchUpdate(@Param("list") List<StationProjectInfoDTO> stationProjectInfoDTOList);
}
