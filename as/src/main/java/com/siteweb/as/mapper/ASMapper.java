package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.OperationGroup;
import com.siteweb.admin.entity.SpecialtyGroup;
import com.siteweb.admin.entity.UserRoleMap;
import com.siteweb.as.dto.HouseInfoDto;
import com.siteweb.as.dto.StationStructure;
import com.siteweb.monitoring.entity.StationMask;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface ASMapper extends BaseMapper<StationMask> {

    List<StationStructure> getStationStructureByCategoryId(Integer treeCategoryId);
    List<StationStructure> getStationMapByCategoryId(@Param("treeCategoryId") Integer treeCategoryId, @Param("stationIds") Collection<Integer> stationIds);

    List<HouseInfoDto> getHouseInfoByStation(Integer stationId);


    List<Integer> getAllOperation();
    UserRoleMap getUserRoleMapByUserId(Integer userId);
    OperationGroup getOperationGroupByUserId(Integer userId);
   List<Integer> getOperationByGroupId(Integer groupId);
    SpecialtyGroup getSpecialtyGroupByUserId(Integer userId);
    List<Integer> getSpecialtyByGroupId(Integer groupId);

}

