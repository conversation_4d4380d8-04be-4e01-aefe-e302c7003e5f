package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.dto.patrol.PatrolSignalDTO;
import com.siteweb.as.dto.patrol.PatrolTaskDTO;
import com.siteweb.as.entity.PatrolTask;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PatrolStandardTaskMapper extends BaseMapper<PatrolTask> {


    List<PatrolTaskDTO> getAllPatrolStandardTask();

    PatrolTaskDTO getPatrolStandardTask(Integer taskId);

    List<PatrolSignalDTO> selectPatrolSignal(Integer groupId, String stationIds);

    /**
     * 获取局站id条件
     * CALL PRT_FetchReportCommonFields(15, '-1', '-1', '-1', '-1', '-1', '-1', '-1', '-1','tt_TEMPStation', '-1', '-1', '-1');
     */
    List<Integer> getPatrolPowerOffStationId();

    List<Integer> getPatrolDisconnectRecordStationId();

    List<PatrolSignalDTO> selectPatrolStandardSignal(Integer groupId, String stationIds);
}
