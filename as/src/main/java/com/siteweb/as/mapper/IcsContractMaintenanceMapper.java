package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.IcsContractMaintenanceDTO;
import com.siteweb.as.entity.IcsContractMaintenance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IcsContractMaintenanceMapper extends BaseMapper<IcsContractMaintenance> {
    Page<IcsContractMaintenance> findContractInfoPage(@Param("page") Page<IcsContractMaintenance> page, @Param("contractMaintenanceDTO") IcsContractMaintenanceDTO contractMaintenanceDTO);
    List<IcsContractMaintenance> findContractInfoList(@Param("contractMaintenanceDTO") IcsContractMaintenanceDTO contractMaintenanceDTO);
}
