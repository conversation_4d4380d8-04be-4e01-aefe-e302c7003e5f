package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.vo.area.AreaStationIdVO;
import com.siteweb.as.vo.area.AreaStationVO;
import com.siteweb.as.vo.area.StructureDataVO;
import com.siteweb.monitoring.entity.Station;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AreaStationMapper extends BaseMapper<Station> {

    List<AreaStationVO> getStationByGroupIdAndStructureId(@Param("structureGroupId") Integer structureGroupId, @Param("structureId") Integer structureId);

    List<StructureDataVO> getStructureData(@Param("structure") Integer structure);

    void batchSaveAreaMap(@Param("areaId") Integer areaId, @Param("stationIds") List<Integer> stationIds);

    void batchDeleteAreaMap(@Param("areaId") Integer areaId, @Param("stationIds") List<Integer> stationIds);

    List<AreaStationIdVO> findStationByAreaId(@Param("areaId") Integer areaId);
}
