package com.siteweb.as.mapper;

import com.siteweb.as.dto.config.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ConfigMapper {

    List<ConfigSamplerUnitDTO> getStationSUList(Integer stationId);

    List<ConfigMonitorUnitDTO> getStationRMUList(Integer stationId);

    String getMaintainInfo(Integer stationId);

    List<StationInfoDTO> getConfigObject(Integer stationId);

    StructureDTO selectCenter();

    List<ConfigHouseDTO> getHouseInfo(Integer stationId);
}
