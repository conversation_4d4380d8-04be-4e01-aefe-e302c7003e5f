package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.vo.AssetVO;
import com.siteweb.utility.entity.DataItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AssetMapper extends BaseMapper<DataItem> {
    List<AssetVO> findAssets();
    Integer addAsset(AssetVO asset);
    Integer putAsset(AssetVO asset);
    Integer delAsset(Integer assetId);

    Integer batchAddAsset(@Param("assetList") List<AssetVO> assetList);
}

