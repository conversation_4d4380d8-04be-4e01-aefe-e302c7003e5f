package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.shield.EventShieldDTO;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.monitoring.entity.EventMask;
import com.siteweb.monitoring.vo.StationMaskVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EventShieldMapper extends BaseMapper<EventMask> {

    Page<EventShieldDTO> findEventShieldPage(@Param("page") Page<StationMaskVO> page, @Param("shieldFilterDTO") ShieldFilterDTO shieldFilterDTO);

    List<EventShieldDTO> findEventShield(@Param("shieldFilterDTO") ShieldFilterDTO shieldFilterDTO);
}
