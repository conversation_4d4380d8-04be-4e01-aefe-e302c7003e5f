package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.EquipmentProjectInfoDTO;
import com.siteweb.as.entity.EquipmentProjectInfo;
import com.siteweb.as.vo.version.EquipmentProjectInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface EquipmentProjectInfoMapper extends BaseMapper<EquipmentProjectInfo> {
    Page<EquipmentProjectInfoVO> findContractInfoPage(@Param("page") Page<EquipmentProjectInfoVO> page, @Param("equipmentIds") Collection<Integer> equipmentIds, @Param("equipmentProjectInfoDTO") EquipmentProjectInfoDTO equipmentProjectInfoDTO);

    EquipmentProjectInfoVO findContractInfoByEquipmentId(@Param("equipmentId") Integer equipmentId);

    List<EquipmentProjectInfoVO> findContractInfo(@Param("equipmentIds") Collection<Integer> equipmentIds, @Param("equipmentProjectInfoDTO") EquipmentProjectInfoDTO equipmentProjectInfoDTO);

    int batchUpdateContractInfo(@Param("list") List<EquipmentProjectInfoDTO> equipmentProjectInfoDTOList);
}
