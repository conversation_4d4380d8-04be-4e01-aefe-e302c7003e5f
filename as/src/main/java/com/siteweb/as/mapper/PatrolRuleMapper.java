package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.dto.patrol.*;
import com.siteweb.as.entity.PatrolRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PatrolRuleMapper extends BaseMapper<PatrolRule> {

    List<PatrolRuleDTO> getAllPatrolRule(int standardVer);

    PatrolRuleDTO getPatrolRule(@Param("standardVer") int standardVer, @Param("ruleId") Integer ruleId);

    List<PatrolRuleDTO> getPatrolRuleByGroup(@Param("standardVer") int standardVer, @Param("groupId") Integer groupId);

    List<BaseEquipmentDTO> listBaseEquipment();

    List<SignalBaseDicDTO> listBaseSignal(Integer baseEquipmentId);

    List<StandardInfoDTO> listStandardInfo(Long baseTypeId);

    List<PatrolRuleJobDTO> getPatrolRuleJobDTO(Integer groupId);
}
