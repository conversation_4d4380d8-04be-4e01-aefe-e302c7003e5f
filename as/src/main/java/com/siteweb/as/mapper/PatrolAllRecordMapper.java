package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.as.entity.PatrolAllRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface PatrolAllRecordMapper extends BaseMapper<PatrolAllRecord> {

    void batchInsert(@Param("tableName") String tableName, @Param("records") List<PatrolAllRecord> records);

    @Update("${sql}")
    void cleanOldRecords(@Param("sql") String sql);
}
