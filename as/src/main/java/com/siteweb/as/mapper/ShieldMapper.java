package com.siteweb.as.mapper;

import com.siteweb.as.dto.MaskDetailsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ShieldMapper {

    List<MaskDetailsDTO> getEventMask(@Param("stationId") Integer stationId, @Param("equipmentId") Integer equipmentId, @Param("eventId") Integer eventId);

    List<MaskDetailsDTO> getEquipmentMask(@Param("stationId") Integer stationId, @Param("equipmentId") Integer equipmentId);

    List<MaskDetailsDTO> getStationMask(Integer stationId);
}
