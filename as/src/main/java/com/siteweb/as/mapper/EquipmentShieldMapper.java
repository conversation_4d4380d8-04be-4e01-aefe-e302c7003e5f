package com.siteweb.as.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.shield.EquipmentShieldDTO;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.monitoring.entity.EquipmentMask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EquipmentShieldMapper extends BaseMapper<EquipmentMask> {
    Page<EquipmentShieldDTO> findEquipmentShieldPage(@Param("page") Page<EquipmentShieldDTO> page, @Param("shieldFilterDTO") ShieldFilterDTO shieldFilterDTO);

    List<EquipmentShieldDTO> findEquipmentShield(@Param("shieldFilterDTO") ShieldFilterDTO shieldFilterDTO);
}
