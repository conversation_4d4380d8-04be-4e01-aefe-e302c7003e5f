package com.siteweb.as.net.transfer;

import cn.hutool.core.io.file.PathUtil;
import com.siteweb.common.constants.GlobalConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;

import java.io.*;
import java.net.SocketException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

@Slf4j
@Data
public class FtpClient implements FileTransferClient {
    private String server = "localhost";
    private int port = 21;
    private String username = "anonymous";
    private String password = "<EMAIL>";
    private String remotePath = ".";
    private String localFilePath = ".";
    private boolean binaryMode = true;
    private FTPClient apacheFtpClient;

    public FtpClient(String server, String username, String password,int port) {
        this.apacheFtpClient = new FTPClient();
        this.server = server;
        this.username = username;
        this.password = password;
        this.port = port;
    }

    /**
     * 连接并登录到FTP服务器
     */
    @Override
    public void login() throws IOException {
        try {
            // 设置字符编码为GBK
            apacheFtpClient.setControlEncoding("GBK");
            apacheFtpClient.connect(server, port);
            int replyCode = apacheFtpClient.getReplyCode();
            if (!apacheFtpClient.login(username, password)) {
                throw new IOException("登录失败，回复码: " + replyCode);
            }
            log.info("成功连接到FTP服务器: {}:{}", server, port);

            // 根据需要设置二进制模式
            if (binaryMode) {
                apacheFtpClient.setFileType(FTP.BINARY_FILE_TYPE);
            } else {
                apacheFtpClient.setFileType(FTP.ASCII_FILE_TYPE);
            }
            apacheFtpClient.enterLocalPassiveMode(); // 使用被动模式提高兼容性
        } catch (SocketException e) {
            throw new IOException("无法连接到FTP服务器: " + e.getMessage(), e);
        }
    }

    /**
     * 断开与FTP服务器的连接
     */
    @Override
    public void logout() {
        if (apacheFtpClient.isConnected()) {
            try {
                apacheFtpClient.logout();
                apacheFtpClient.disconnect();
                log.info("已断开与FTP服务器的连接。");
            } catch (IOException e) {
                log.error("断开FTP服务器时出错: {}", e.getMessage());
            }
        }
    }

    /**
     * 实现Closeable接口以支持try-with-resources模式
     */
    @Override
    public void close() {
        logout();
    }

    /**
     * 列出当前目录的文件
     *
     * @return 文件名列表
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public List<String> listFiles() throws IOException {
        FTPFile[] files = apacheFtpClient.listFiles(remotePath);
        List<String> fileNames = new ArrayList<>();
        for (FTPFile file : files) {
            fileNames.add(file.getName());
        }
        return fileNames;
    }

    /**
     * 从FTP服务器下载文件
     *
     * @param remoteFileName 远程文件名
     * @param localFilePath  本地保存文件路径
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public void downloadFile(String remoteFileName, String localFilePath) throws IOException {
        try (OutputStream outputStream = new FileOutputStream(localFilePath)) {
            boolean success = apacheFtpClient.retrieveFile(remoteFileName, outputStream);
            if (!success) {
                log.error("下载文件失败: " + remoteFileName);
                return;
            }
            log.info("文件下载成功: {}", remoteFileName);
        }
    }

    /**
     * 下载远程目录到本地，支持排除特定文件或目录
     *
     * @param remoteDirPath  远程目录路径
     * @param localParentDir 本地保存目录
     * @param excludePatterns 要排除的文件或目录的匹配模式列表（支持"*"通配符，如"*.log"）
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public void downloadDirectory(String remoteDirPath, String localParentDir, List<String> excludePatterns) throws IOException {
        FTPFile[] subFiles = apacheFtpClient.listFiles(remoteDirPath);
        if (Objects.isNull(subFiles)) {
            return;
        }
        for (FTPFile file : subFiles) {
            String fileName = file.getName();

            // 检查文件或目录是否应该被排除
            if (shouldExclude(fileName, excludePatterns)) {
                log.info("排除文件/目录: {}", fileName);
                continue;
            }

            String remoteFilePath = remoteDirPath + GlobalConstants.PATH_SEPARATOR + fileName;
            File localFile = new File(localParentDir + GlobalConstants.PATH_SEPARATOR + fileName);

            if (file.isDirectory()) {
                // 在本地创建目录
                if (!localFile.exists()) {
                    localFile.mkdirs();
                    log.info("目录创建: {}", fileName);
                }
                // 递归下载子目录
                downloadDirectory(remoteFilePath, localFile.getAbsolutePath(), excludePatterns);
            } else {
                PathUtil.mkdir(Path.of(localFile.getParent()));
                // 下载文件
                try (OutputStream outputStream = new FileOutputStream(localFile)) {
                    apacheFtpClient.retrieveFile(remoteFilePath, outputStream);
                    log.info("文件下载成功: {}", remoteFilePath);
                }
            }
        }
    }

    /**
     * 检查文件名是否匹配排除模式
     *
     * @param fileName 文件名
     * @param excludePatterns 排除模式列表
     * @return 如果文件应该被排除则返回true
     */
    private boolean shouldExclude(String fileName, List<String> excludePatterns) {
        if (excludePatterns == null || excludePatterns.isEmpty()) {
            return false;
        }

        for (String pattern : excludePatterns) {
            // 处理通配符 "*" 匹配
            if (pattern.contains("*")) {
                String regex = pattern.replace(".", "\\.").replace("*", ".*");
                if (fileName.matches(regex)) {
                    return true;
                }
            } else if (fileName.equals(pattern)) {
                // 精确匹配
                return true;
            }
        }

        return false;
    }


    /**
     * 上传目录到FTP服务器，支持特定规则
     *
     * @param localPath  本地目录路径
     * @param serverPath 远程目录路径
     * @param recurse    是否递归上传子目录
     * @param mask       文件过滤规则（如 "*.txt"）
     * @return 上传成功返回 true，否则返回 false
     */
    @Override
    public boolean uploadDirectory(String localPath, String serverPath, boolean recurse, String mask) {
        boolean flag = true;

        try {
            File localDir = new File(localPath);

            if (!localDir.exists() || !localDir.isDirectory()) {
                log.error("本地路径不是有效目录: {}", localPath);
                return false;
            }

            // 确保远程目录存在
            if (!existDir(serverPath)) {
                createDirectory(serverPath);
            }
            changeDirectory(serverPath);
            // 根据规则上传文件
            uploadFilesByRules(localDir, mask);

            // 递归上传子目录
            if (recurse) {
                File[] subDirs = localDir.listFiles(File::isDirectory);
                if (subDirs != null) {
                    for (File subDir : subDirs) {
                        String subDirName = subDir.getName();
                        uploadDirectory(subDir.getAbsolutePath(), serverPath + GlobalConstants.PATH_SEPARATOR + subDirName, true, mask);
                    }
                }
            }
            // 返回到父目录
            changeDirectory("..");
        } catch (Exception ex) {
            log.error("上传目录时出错: {}", ex.getMessage(), ex);
            flag = false;
        }
        return flag;
    }

    /**
     * 根据特定规则上传目录中的文件
     *
     * @param localDir 本地目录
     * @param mask     文件过滤规则（如 "*.txt"）
     */
    private void uploadFilesByRules(File localDir, String mask) {
        String localDirName = localDir.getName();

        // 定义特定目录的规则
        Predicate<String> fileFilter = switch (localDirName.toLowerCase()) {
            case "xmlcfg", "idu_script" -> name -> name.matches(mask.replace("*", ".*"));
            case "cmbcfg" -> name -> name.equalsIgnoreCase("cmb_init_list.ini");
            case "cubcfg" -> name -> name.equalsIgnoreCase("cub_init_list.ini");
            default -> name -> true; // 默认上传所有文件
        };
        // 上传符合规则的文件
        File[] files = localDir.listFiles((dir, name) -> fileFilter.test(name));
        if (Objects.isNull(files)) {
            return;
        }
        for (File file : files) {
            if (file.isFile()) {
                uploadFile(file.getAbsolutePath(), file.getName());
            }
        }
    }

    /**
     * 上传文件到FTP服务器
     *
     * @param localFilePath  本地文件路径
     * @param remoteFileName 远程文件名
     */
    @Override
    public void uploadFile(String localFilePath, String remoteFileName) {
        try (InputStream inputStream = new FileInputStream(localFilePath)) {
            apacheFtpClient.storeFile(remotePath + GlobalConstants.PATH_SEPARATOR + remoteFileName, inputStream);
            log.info("文件上传成功: {}", remoteFileName);
        } catch (IOException e) {
            log.error("上传文件失败: {}", remoteFileName, e);
        }
    }

    /**
     * 检查FTP服务器上的目录是否存在
     *
     * @param path 目录路径
     * @return 存在返回 true，否则返回 false
     * @throws IOException 如果发生I/O错误
     */
    private boolean existDir(String path) throws IOException {
        boolean result = apacheFtpClient.changeWorkingDirectory(path);
        apacheFtpClient.changeWorkingDirectory(this.remotePath);
        return result;
    }

    /**
     * 删除FTP服务器上的文件
     *
     * @param remoteFileName 远程文件名
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public void deleteFile(String remoteFileName) throws IOException {
        boolean success = apacheFtpClient.deleteFile(remotePath + GlobalConstants.PATH_SEPARATOR + remoteFileName);
        if (!success) {
            throw new IOException("删除文件失败: " + remoteFileName);
        }
        log.info("文件删除成功: {}", remoteFileName);
    }

    /**
     * 在FTP服务器上创建目录
     *
     * @param dirName 目录名
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public void createDirectory(String dirName) throws IOException {
        String currentPath = "";
        String[] directories = dirName.split(GlobalConstants.PATH_SEPARATOR);
        for (String dir : directories) {
            if (dir.isEmpty()) {
                continue; // 跳过空路径段
            }
            currentPath += GlobalConstants.PATH_SEPARATOR + dir;

            // 检查目录是否存在
            if (!existDir(currentPath)) {
                apacheFtpClient.makeDirectory(currentPath);
            }
        }
    }

    /**
     * 删除FTP服务器上的目录
     *
     * @param dirName 目录名
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public void removeDirectory(String dirName) throws IOException {
        boolean success = apacheFtpClient.removeDirectory(remotePath + GlobalConstants.PATH_SEPARATOR + dirName);
        if (!success) {
            throw new IOException("删除目录失败: " + dirName);
        }
        log.info("目录删除成功: {}", dirName);
    }

    /**
     * 切换FTP服务器的工作目录
     *
     * @param dirName 要切换到的目录名
     * @throws IOException 如果发生I/O错误
     */
    @Override
    public void changeDirectory(String dirName) throws IOException {
        boolean success = apacheFtpClient.changeWorkingDirectory(dirName);
        if (!success) {
            throw new IOException("切换目录失败: " + dirName);
        }
        this.remotePath = apacheFtpClient.printWorkingDirectory();
        log.info("工作目录已切换到: {}", this.remotePath);
    }
}