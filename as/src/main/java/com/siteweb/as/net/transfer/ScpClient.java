package com.siteweb.as.net.transfer;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ScpClient implements FileTransferClient, Closeable {
    private String server;
    private int port;
    private String username;
    private String password;
    private Session session;
    private String remotePath = ".";   // 当前工作目录

    public ScpClient(String server, String username, String password, int port) {
        this.server = server;
        this.username = username;
        this.password = password;
        this.port = port;
    }

    @Override
    public void login() {
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(username, server, port);
            session.setPassword(password);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect(10_000);
            // 切换到初始路径
            this.remotePath = execCommand("pwd").trim();
        } catch (JSchException e) {
            throw new RuntimeException("SSH 登录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void logout() {
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
    }

    @Override
    public void close() {
        logout();
    }

    /* ============ 辅助：执行远程 shell 命令 ============ */
    private String execCommand(String cmd) {
        ChannelExec ch = null;
        ByteArrayOutputStream bout = new ByteArrayOutputStream();
        try {
            ch = (ChannelExec) session.openChannel("exec");
            ch.setCommand(cmd);
            ch.setErrStream(System.err);
            InputStream in = ch.getInputStream();
            ch.connect();
            byte[] buf = new byte[1024];
            int len;
            while ((len = in.read(buf)) != -1) {
                bout.write(buf, 0, len);
            }
            return bout.toString();
        } catch (Exception e) {
            throw new RuntimeException("执行命令失败: `" + cmd + "`", e);
        } finally {
            if (ch != null) ch.disconnect();
        }
    }

    /* ============ 列表/目录操作 ============ */
    @Override
    public List<String> listFiles() {
        // 列出当前 remotePath 下所有文件（不含 . ..）
        String out = execCommand("ls -1 " + escape(remotePath));
        return Arrays.stream(out.split("\\r?\\n"))
                     .filter(s -> !s.isEmpty() && !s.equals(".") && !s.equals(".."))
                     .collect(Collectors.toList());
    }

    //@Override
    public boolean existDir(String path) {
        // test -d
        ChannelExec ch = null;
        try {
            ch = (ChannelExec) session.openChannel("exec");
            String full = buildRemotePath(path);
            ch.setCommand("test -d " + escape(full));
            ch.connect();
            int status;
            do {
                status = ch.getExitStatus();
            } while (status == -1);
            return status == 0;
        } catch (Exception e) {
            return false;
        } finally {
            if (ch != null) ch.disconnect();
        }
    }

    @Override
    public void createDirectory(String dirName) {
        execCommand("mkdir -p " + escape(buildRemotePath(dirName)));
    }

    @Override
    public void removeDirectory(String dirName) {
        execCommand("rmdir " + escape(buildRemotePath(dirName)));
    }

    @Override
    public void deleteFile(String remoteFileName) {
        execCommand("rm -f " + escape(buildRemotePath(remoteFileName)));
    }

    @Override
    public void changeDirectory(String dirName) {
        String tgt = buildRemotePath(dirName);
        execCommand("cd " + escape(tgt));
        this.remotePath = execCommand("pwd").trim();
    }

    //@Override
    public String pwd() {
        return this.remotePath;
    }

    /* ============ 单文件下载 ============ */
    @Override
    public void downloadFile(String remoteFileName, String localFilePath) throws IOException {
        String remoteFile = buildRemotePath(remoteFileName);
        String command = "scp -f " + escape(remoteFile);
        Channel channel = null;
        try {
            channel = session.openChannel("exec");

            ((ChannelExec) channel).setCommand(command);
            // 输入/输出流
            OutputStream out = channel.getOutputStream();
            InputStream in = channel.getInputStream();
            channel.connect();
            // 1) 发送初始确认
            out.write(0);
            out.flush();

            // 2) 读取文件头
            byte[] buf = new byte[1024];
            int c = in.read(); // 应当为 'C'
            if (c != 'C') {
                throw new IOException("SCP 协议异常，未收到文件头");
            }
            // 跳过权限字段
            in.read(buf, 0, 5);
            // 文件大小
            long fileSize = 0;
            while (true) {
                int b = in.read();
                if (b == ' ') break;
                fileSize = fileSize * 10 + (b - '0');
            }
            // 文件名（直到换行）
            StringBuilder fileName = new StringBuilder();
            while (true) {
                int b = in.read();
                if (b == '\n') break;
                fileName.append((char) b);
            }
            // 3) 确认收到头
            out.write(0);
            out.flush();

            // 4) 读取文件内容到本地
            try (FileOutputStream fos = new FileOutputStream(localFilePath)) {
                long remain = fileSize;
                while (remain > 0) {
                    int len = in.read(buf, 0, (int) Math.min(buf.length, remain));
                    if (len < 0) break;
                    fos.write(buf, 0, len);
                    remain -= len;
                }
            }

            // 5) 最终确认 & 断开
            if (in.read() != 0) {
                throw new IOException("SCP 未收到最终确认");
            }
            out.write(0);
            out.flush();

            channel.disconnect();

        } catch (JSchException e) {
            throw new RuntimeException(e);
        }
    }

    /* ============ 单文件上传 ============ */
    @Override
    public void uploadFile(String localFilePath, String remoteFileName) {
        File localFile = new File(localFilePath);
        if (!localFile.exists() || !localFile.isFile()) {
            throw new RuntimeException("本地文件无效: " + localFilePath);
        }
        String remoteFile = buildRemotePath(remoteFileName);
        String command = "scp -t " + escape(remoteFile);
        Channel channel = null;
        try {
            channel = session.openChannel("exec");
            ((ChannelExec) channel).setCommand(command);
            OutputStream out = channel.getOutputStream();
            InputStream in = channel.getInputStream();
            channel.connect();

            if (checkAck(in) != 0) {
                throw new IOException("SCP 协议握手失败");
            }
            // 发送文件头：C0644 filesize filename\n
            long filesize = localFile.length();
            String header = "C0644 " + filesize + " " + localFile.getName() + "\n";
            out.write(header.getBytes());
            out.flush();
            if (checkAck(in) != 0) {
                throw new IOException("SCP 协议头部确认失败");
            }
            // 发送文件内容
            try (FileInputStream fis = new FileInputStream(localFile)) {
                byte[] buf = new byte[1024];
                int len;
                while ((len = fis.read(buf)) != -1) {
                    out.write(buf, 0, len);
                }
            }
            // 文件结束符
            out.write(0);
            out.flush();
            if (checkAck(in) != 0) {
                throw new IOException("SCP 协议内容确认失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败: " + e.getMessage(), e);
        } finally {
            if (channel != null) channel.disconnect();
        }
    }

    /** SCP 协议握手 / 错误处理 */
    private int checkAck(InputStream in) throws IOException {
        int b = in.read();
        // 0 = success, 1 = error, 2 = fatal
        if (b == 1 || b == 2) {
            StringBuilder sb = new StringBuilder();
            int c;
            do {
                c = in.read();
                sb.append((char) c);
            } while (c != '\n');
            throw new IOException("SCP 错误: " + sb.toString());
        }
        return b;
    }

    /* ============ 目录递归（示例，重复调用上面单文件方法+列表） ============ */
    @Override
    public void downloadDirectory(String remoteDirPath,
                                  String localParentDir,
                                  List<String> excludePatterns) throws IOException {
        // 本地目录准备
        File localDir = new File(localParentDir);
        if (!localDir.exists() && !localDir.mkdirs()) {
            throw new IOException("无法创建本地目录：" + localParentDir);
        }
        List<String> cmd = Arrays.asList(
                "sshpass", "-p", this.password,
                "scp", "-p", "-r",
                String.format("%s@%s:%s",
                        this.username,
                        this.server,
                        buildRemotePath(remoteDirPath)),
                localParentDir
        );
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.inheritIO();
            Process p = pb.start();
            int exit = p.waitFor();
            if (exit != 0) {
                throw new IOException("scp 下载目录失败，退出码：" + exit);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("scp 下载被中断", e);
        }
    }

    @Override
    public boolean uploadDirectory(String localPath,
                                   String serverPath,
                                   boolean recurse,
                                   String mask) {
        // 构造 scp 命令
        // 示例用 sshpass 传递密码，如果你已做了密钥免密，这里可以删掉 sshpass 部分
        List<String> cmd = new ArrayList<>();
        cmd.add("sshpass");
        cmd.add("-p");
        cmd.add(this.password);
        cmd.add("scp");
        cmd.add("-p");                 // 保留权限／时间
        if (recurse) cmd.add("-r");    // 递归
        cmd.add(localPath);            // 本地目录
        // 远端目标：user@host:/absolute/remote/dir
        cmd.add(String.format("%s@%s:%s",
                this.username,
                this.server,
                buildRemotePath(serverPath)));
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.inheritIO();  // 把子进程的 stdout/stderr 也打印到当前控制台
            Process p = pb.start();
            int exit = p.waitFor();
            return exit == 0;
        } catch (Exception e) {
            log.error("scp 上传目录失败", e);
            return false;
        }
    }

    /* ============ 排除规则 ============ */
    private boolean shouldExclude(String name, List<String> patterns) {
        if (patterns == null) return false;
        for (String p : patterns) {
            if (p.contains("*")) {
                String regex = p.replace(".", "\\.").replace("*", ".*");
                if (name.matches(regex)) return true;
            } else if (name.equals(p)) {
                return true;
            }
        }
        return false;
    }

    /* ============ 路径拼接 & 转义 ============ */
    private String buildRemotePath(String path) {
        if (path.startsWith("/")) {
            return path;
        } else {
            if (remotePath.endsWith("/")) {
                return remotePath + path;
            } else {
                return remotePath + "/" + path;
            }
        }
    }
    private String escape(String path) {
        // 简单转义空格
        return path.replace(" ", "\\ ");
    }
}
