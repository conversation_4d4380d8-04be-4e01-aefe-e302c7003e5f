package com.siteweb.as.net.connect;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ReUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
public class TelnetClient implements AutoCloseable {
    private String ip;
    private int port = 23;
    private String username;
    private String password;
    private int loginWaitTime = 500; // 登录等待时间 (ms)
    private int recvWaitTime = 500;  // 接收等待时间 (ms)

    private final org.apache.commons.net.telnet.TelnetClient apacheTelnetClient;
    private InputStream in;
    private OutputStream out;

    public TelnetClient() {
        this.apacheTelnetClient = new org.apache.commons.net.telnet.TelnetClient();
    }

    public TelnetClient(String ip, String username, String password) {
        this();
        this.ip = ip;
        this.username = username;
        this.password = password;
    }

    public TelnetClient(String ip, String username, String password, int loginWaitTime, int recvWaitTime) {
        this(ip, username, password);
        this.loginWaitTime = loginWaitTime;
        this.recvWaitTime = recvWaitTime;
    }

    public void disconnect() {
        if (Objects.isNull(apacheTelnetClient)) {
            return;
        }
        try {
            if (apacheTelnetClient.isConnected()) {
                apacheTelnetClient.disconnect();
            }
        } catch (IOException e) {
            log.error(ExceptionUtil.stacktraceToString(e));
        }
    }

    public boolean connect() {
        try {
            apacheTelnetClient.connect(ip, port);
            in = apacheTelnetClient.getInputStream();
            out = apacheTelnetClient.getOutputStream();
            // 登录步骤
            if (!waitForPrompt(":")) return false;
            send(username);
            if (!waitForPrompt(":")) return false;
            send(password);
            return waitForPrompt("#", "$", ">");
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return false;
        }
    }

    public boolean exec(String command) {
        try {
            send(command);
            return waitForPrompt("#", "$", ">");
        } catch (Exception e) {
            return false;
        }
    }

    public String sendCommand(String command) {
        try {
            send(command);
            ThreadUtil.sleep(recvWaitTime);
            return receive();
        } catch (IOException e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return "";
        }
    }

    public String receive() {
        try {
            if (apacheTelnetClient == null || !apacheTelnetClient.isConnected()) return "";
            StringBuilder response = new StringBuilder();
            byte[] buffer = new byte[8192];
            int bytesRead;
            while (in.available() > 0) {
                bytesRead = in.read(buffer);
                if (bytesRead > 0) {
                    response.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
                }
                ThreadUtil.sleep(loginWaitTime);
            }
            return response.toString();
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return "";
        }
    }

    public void getIPInfo(StringBuilder ip, StringBuilder mask, StringBuilder gateway, StringBuilder mac) {
        String response = sendCommand("ifconfig");
        if (CharSequenceUtil.isBlank(response)) return;

        mac.append(ReUtil.get("HWaddr +([0-9a-fA-F:]+)", response, 1));
        ip.append(ReUtil.get("inet addr:([0-9\\.]+)", response, 1));
        mask.append(ReUtil.get("Mask:([0-9\\.]+)", response, 1));

        response = sendCommand("route");
        gateway.append(ReUtil.get("default\\s+([0-9\\.]+)", response, 1));
    }

    public String getDeviceType() {
        try {
            String response = sendCommand("/home/<USER>/main.sh");
            if (response == null) return "";

            if (response.contains("The model is")) {
                return ReUtil.get("The model is (.+?)\\s+", response, 1);
            } else if (CharSequenceUtil.isNotBlank(response)) {
                return "IDU";
            }
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
        }
        return "";
    }

    public String getVersion() {
        String response = sendCommand("cat /home/<USER>/version");
        if (CharSequenceUtil.isBlank(response)) return "";

        if (response.contains("Emerson") || response.contains("Vertiv")) {
            return "  " + response.strip();
        }
        return "";
    }

    public String getSpace() {
        String response = sendCommand("df");
        if (CharSequenceUtil.isBlank(response)) return "";

        int index = response.indexOf("[root");
        if (index != -1) {
            return response.substring(0, index).strip();
        }
        return "";
    }

    private boolean waitForPrompt(String... prompts) {
        ThreadUtil.sleep(loginWaitTime);
        String response = receive();
        log.info("{} ---- {}", ip, response);
        for (String prompt : prompts) {
            if (response.trim().endsWith(prompt)) {
                return true;
            }
        }
        return false;
    }

    private void send(String command) throws IOException {
        if (apacheTelnetClient == null || !apacheTelnetClient.isConnected()) return;
        command = command + "\r\n";
        out.write(command.getBytes(StandardCharsets.UTF_8));
        out.flush();
    }


    /**
     * 重启服务器
     * @return boolean 是否成功
     */
    public boolean reboot() {
        try {
            send("reboot");
            return waitForPrompt("#", "$", ">");
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return false;
        }
    }

    @Override
    public void close() throws Exception {
        disconnect();
    }
}

