package com.siteweb.as.net.transfer;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ssh.JschRuntimeException;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.siteweb.common.constants.GlobalConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Data
public class SftpClient implements FileTransferClient { // 实现Closeable接口以支持try-with-resources
    private String server = "localhost";
    private int port = 22; // SFTP默认端口
    private String username = "anonymous";
    private String password = "<EMAIL>";
    private String charset = "UTF-8";
    private String remotePath = "."; // 表示远程服务器上的当前工作目录
    private String localFilePath = ".";
    // private boolean binaryMode = true; // SFTP始终是二进制模式，此字段不太相关

    private Sftp hutoolSftp;

    public SftpClient(String server, String username, String password,int port) {
        this.server = server;
        this.username = username;
        this.password = password;
        this.port = port;
    }

    /**
     * 连接并登录到SFTP服务器
     * 初始化连接并切换到初始远程路径。
     */
    @Override
    public void login() {
        try {
            // Hutool的Sftp构造函数会建立连接 并设置文件编码为GBK
            this.hutoolSftp = JschUtil.createSftp(server, port, username, password);
            Class<?> cl = ChannelSftp.class;
            Field f = cl.getDeclaredField("server_version");
            f.setAccessible(true);
            f.set(this.hutoolSftp.getClient(), 2);
            this.hutoolSftp.getClient().setFilenameEncoding("GBK");
            log.info("成功连接到SFTP服务器并设置文件编码为GBK: {}:{}", server, port);

            // 如果指定了初始远程路径且该路径存在，则切换到该路径
            if (!".".equals(remotePath) && !"./".equals(remotePath)) {
                if (this.hutoolSftp.exist(remotePath)) {
                    this.hutoolSftp.cd(remotePath);
                    // cd后更新为绝对路径
                    this.remotePath = this.hutoolSftp.pwd();
                    log.info("工作目录已切换到: {}", this.remotePath);
                } else {
                    log.warn("指定的初始远程路径不存在: {}", remotePath);
                    // 保持remotePath为pwd()的值，可能是用户的主目录
                    this.remotePath = this.hutoolSftp.pwd();
                    log.info("当前工作目录: {}", this.remotePath);
                }
            } else {
                this.remotePath = this.hutoolSftp.pwd(); // 获取当前路径
                log.info("当前工作目录: {}", this.remotePath);
            }
        } catch (SftpException | NoSuchFieldException | IllegalAccessException e) {
            log.error("无法连接或登录到SFTP服务器: {}:{}", server, port, e);
            log.error("SFTP连接错误错误信息", e);
        }
    }

    /**
     * 断开与SFTP服务器的连接
     */
    @Override
    public void logout() {
        if (this.hutoolSftp != null) {
            this.hutoolSftp.close(); // 关闭底层会话和通道
            this.hutoolSftp = null; // 帮助垃圾回收
            log.info("已断开与SFTP服务器的连接。");
        }
    }

    /**
     * 实现Closeable接口以支持try-with-resources模式
     */
    @Override
    public void close() {
        logout();
    }

    /**
     * 列出当前目录的文件和目录名称
     *
     * @return 文件和目录名列表 (不含 . 和 ..)
     */
    @Override
    public List<String> listFiles() {
        try {
            // ls方法返回当前目录中的文件/目录名称列表
            return hutoolSftp.ls(this.remotePath).stream()
                             .filter(name -> !name.equals(".") && !name.equals(".."))
                             .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("列出远程目录 '{}' 文件失败", this.remotePath, e);
            throw new RuntimeException("列出文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从FTP服务器下载文件
     *
     * @param remoteFileName 远程文件名 (相对于当前 remotePath)
     * @param localFilePath  本地保存文件完整路径
     */
    @Override
    public void downloadFile(String remoteFileName, String localFilePath) throws IOException {
        String fullRemotePath = buildRemotePath(remoteFileName);
        File localFile = new File(localFilePath);
        // 确保本地父目录存在
        FileUtil.mkParentDirs(localFile);

        try (OutputStream outputStream = new FileOutputStream(localFile)) {
            hutoolSftp.download(fullRemotePath, outputStream);
            log.info("文件下载成功: {} -> {}", fullRemotePath, localFilePath);
        } catch (Exception e) {
            log.error("下载文件失败: {}", fullRemotePath, e);
            throw new IOException("下载文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载远程目录到本地 (递归)
     *
     * @param remoteDirPath  远程目录路径 (可以是绝对路径或相对于当前 remotePath)
     * @param localParentDir 本地保存目录
     */
    @Override
    public void downloadDirectory(String remoteDirPath, String localParentDir,List<String> excludePatterns) throws IOException {
        String fullRemotePath = buildRemotePath(remoteDirPath);
        File localDir = new File(localParentDir);

        // 确保本地目录存在
        if (!localDir.exists()) {
            if (!localDir.mkdirs()) {
                throw new IOException("无法创建本地目录: " + localParentDir);
            }
        }

        try {
            // 检查远程目录是否存在
            if (!hutoolSftp.exist(fullRemotePath) || !hutoolSftp.isDir(fullRemotePath)) {
                throw new IOException("远程目录不存在或不是目录: " + fullRemotePath);
            }

            // 获取远程目录中的所有文件和子目录
            List<String> items = hutoolSftp.ls(fullRemotePath);

            // 保存当前工作目录，以便操作完成后恢复
            String originalPath = hutoolSftp.pwd();

            try {
                // 切换到远程目标目录
                hutoolSftp.cd(fullRemotePath);

                // 处理每个文件和子目录
                for (String item : items) {
                    // 跳过 . 和 ..
                    if (".".equals(item) || "..".equals(item)) {
                        continue;
                    }
                    if (shouldExclude(item, excludePatterns)) {
                        continue;
                    }

                    String remoteItemPath = fullRemotePath + "/" + item;
                    String localItemPath = localParentDir + File.separator + item;

                    // 判断远程项目是文件还是目录
                    if (hutoolSftp.isDir(remoteItemPath)) {
                        // 是目录，递归下载
                        downloadDirectory(remoteItemPath, localItemPath, excludePatterns);
                    } else {
                        // 是文件，直接下载
                        File localFile = new File(localItemPath);
                        // 确保父目录存在
                        FileUtil.mkParentDirs(localFile);

                        try (OutputStream outputStream = new FileOutputStream(localFile)) {
                            hutoolSftp.download(remoteItemPath, outputStream);
                            log.info("文件下载成功: {} -> {}", remoteItemPath, localItemPath);
                        } catch (Exception e) {
                            log.warn("下载文件失败: {} -> {}, 错误: {}", remoteItemPath, localItemPath, e.getMessage());
                            // 可以选择继续处理其他文件，而不是直接抛出异常中断整个过程
                            // 或者根据需要抛出异常
                        }
                    }
                }
            } finally {
                // 恢复原始工作目录
                try {
                    hutoolSftp.cd(originalPath);
                } catch (Exception e) {
                    log.warn("无法返回原始工作目录: {}", originalPath, e);
                }
            }

            log.info("目录下载成功: {} -> {}", fullRemotePath, localParentDir);
        } catch (Exception e) {
            log.error("下载目录过程中发生错误: {}", fullRemotePath, e);
            throw new IOException("下载目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查文件名是否匹配排除模式
     *
     * @param fileName 文件名
     * @param excludePatterns 排除模式列表
     * @return 如果文件应该被排除则返回true
     */
    private boolean shouldExclude(String fileName, List<String> excludePatterns) {
        if (excludePatterns == null || excludePatterns.isEmpty()) {
            return false;
        }

        for (String pattern : excludePatterns) {
            // 处理通配符 "*" 匹配
            if (pattern.contains("*")) {
                String regex = pattern.replace(".", "\\.").replace("*", ".*");
                if (fileName.matches(regex)) {
                    return true;
                }
            } else if (fileName.equals(pattern)) {
                // 精确匹配
                return true;
            }
        }

        return false;
    }

    /**
     * 上传目录到FTP服务器，支持特定规则 (保持原有的递归和过滤逻辑)
     *
     * @param localPath  本地目录路径
     * @param serverPath 远程目标目录路径 (可以是绝对路径或相对于当前 remotePath)
     * @param recurse    是否递归上传子目录
     * @param mask       文件过滤规则（如 "*.txt"）
     * @return 上传成功返回 true，否则返回 false
     */
    @Override
    public boolean uploadDirectory(String localPath, String serverPath, boolean recurse, String mask) {
        boolean flag = true;
        String originalPath = pwd(); // 保存当前路径
        String targetServerPath = buildRemotePath(serverPath); // 解析目标路径

        try {
            File localDir = new File(localPath);
            if (!localDir.exists() || !localDir.isDirectory()) {
                log.error("本地路径不是有效目录: {}", localPath);
                return false;
            }

            // 确保远程目录存在
            if (!existDir(targetServerPath)) {
                createDirectory(targetServerPath); // 使用createDirectory处理路径
            }
            changeDirectory(targetServerPath); // 切换到目标目录

            // 根据规则上传当前目录的文件
            uploadFilesByRules(localDir, mask); // 这将使用Hutool的uploadFile

            // 递归上传子目录
            if (recurse) {
                File[] subDirs = localDir.listFiles(File::isDirectory);
                if (subDirs != null) {
                    for (File subDir : subDirs) {
                        String subDirName = subDir.getName();
                        // 递归调用使用当前远程路径 + 子目录名
                        // 不需要再次传递完整的serverPath，因为我们已经在其中了。
                        if (!uploadDirectory(subDir.getAbsolutePath(), subDirName, true, mask)) {
                            flag = false; // 传播失败
                        }
                        // 递归调用返回后，我们应该仍然在父远程目录中
                        // (uploadDirectory会切换回原目录)
                    }
                }
            }
        } catch (Exception ex) {
            log.error("上传目录 '{}' 到 '{}' 时出错: {}", localPath, targetServerPath, ex.getMessage(), ex);
            flag = false;
        } finally {
            // 重要：始终尝试切换回原始目录
            try {
                if (!Objects.equals(pwd(), originalPath)) {
                    changeDirectory(originalPath);
                }
            } catch (Exception e) {
                log.error("上传目录后无法返回原始路径 '{}'", originalPath, e);
                // 决定这有多重要。也许使客户端状态无效？
            }
        }
        return flag;
    }


    /**
     * 根据特定规则上传目录中的文件 (内部调用 Hutool uploadFile)
     *
     * @param localDir 本地目录
     * @param mask     文件过滤规则（如 "*.txt"）
     */
    private void uploadFilesByRules(File localDir, String mask) {
        String localDirName = localDir.getName();

        // 定义特定目录的规则
        Predicate<String> fileFilter = switch (localDirName.toLowerCase()) {
            case "xmlcfg", "idu_script" -> name -> name.matches(mask.replace("*", ".*"));
            case "cmbcfg" -> name -> name.equalsIgnoreCase("cmb_init_list.ini");
            case "cubcfg" -> name -> name.equalsIgnoreCase("cub_init_list.ini");
            default -> name -> true; // 默认上传所有文件
        };

        // 上传符合规则的文件
        File[] files = localDir.listFiles((dir, name) -> fileFilter.test(name) && new File(dir, name).isFile());
        if (Objects.isNull(files)) {
            return;
        }
        for (File file : files) {
            // uploadFile期望文件名相对于当前远程目录
            uploadFile(file.getAbsolutePath(), file.getName());
        }
    }

    /**
     * 上传文件到SFTP服务器的当前工作目录
     *
     * @param localFilePath  本地文件完整路径
     * @param remoteFileName 远程文件名 (将存储在当前 remotePath 下)
     */
    @Override
    public void uploadFile(String localFilePath, String remoteFileName) {
        File localFile = new File(localFilePath);
        if (!localFile.exists() || !localFile.isFile()) {
            log.error("本地文件不存在或不是文件: {}", localFilePath);
            throw new RuntimeException("本地文件无效: " + localFilePath);
        }
        // Hutool的upload需要目标目录和本地文件。
        // 它将使用本地文件的名称作为该目录中的远程名称。
        // 如果你想要一个不同的远程名称，你需要使用InputStream变体。
        // 假设remoteFileName是当前remotePath中所需的名称：
        String fullRemoteDestPath = buildRemotePath(remoteFileName);
        try {
            // 使用upload(destPath, file)，其中destPath是完整的远程文件路径
            hutoolSftp.upload(fullRemoteDestPath, localFile);
            // 如果需要更改名称，可以使用InputStream替代方法（对于简单重命名效率较低）：
            // try (InputStream inputStream = new FileInputStream(localFile)) {
            //     hutoolSftp.upload(this.remotePath, remoteFileName, inputStream); // 上传到remotePath，名称为remoteFileName
            // }
            log.info("文件上传成功: {} -> {}", localFilePath, fullRemoteDestPath);
        } catch (Exception e) {
            log.warn("程序正在运行上传文件失败: {} -> {}", localFilePath, fullRemoteDestPath);
            //throw new RuntimeException("上传文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查SFTP服务器上的目录是否存在
     *
     * @param path 目录路径 (可以是绝对路径或相对于当前 remotePath)
     * @return 存在返回 true，否则返回 false
     */
    public boolean existDir(String path) {
        try {
            // Hutool的isDir隐含地处理存在性检查
            return hutoolSftp.isDir(buildRemotePath(path));
        } catch (Exception e) {
            // 如果stat失败（例如，权限被拒绝或未找到），则认为它不存在或不可访问
            log.debug("检查目录 '{}' 是否存在时出错或不存在: {}", path, e.getMessage());
            return false;
        }
    }

    /**
     * 删除SFTP服务器上的文件
     *
     * @param remoteFileName 文件名 (相对于当前 remotePath)
     */
    @Override
    public void deleteFile(String remoteFileName) {
        String fullRemotePath = buildRemotePath(remoteFileName);
        try {
            hutoolSftp.delFile(fullRemotePath);
            log.info("文件删除成功: {}", fullRemotePath);
        } catch (Exception e) {
            log.error("删除文件失败: {}", fullRemotePath, e);
            throw new RuntimeException("删除文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 在SFTP服务器上创建目录 (包括所有父目录)
     *
     * @param dirName 目录路径 (可以是绝对路径或相对于当前 remotePath)
     */
    @Override
    public void createDirectory(String dirName) {
        String fullPath = buildRemotePath(dirName);
        try {
            // mkDirs创建不存在的父目录
            //hutoolSftp.mkdir(fullPath);
            hutoolSftp.getClient().mkdir(fullPath);
            log.info("目录创建成功 (或已存在): {}", fullPath);
        } catch (Exception e) {
            log.error("创建目录失败: {}", fullPath, e);
            throw new RuntimeException("创建目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除SFTP服务器上的目录 (必须为空，除非使用递归删除)
     * Hutool的delDir可能是递归的，取决于实现/版本，请查看文档。
     * 根据标准rmdir行为假设非递归。
     *
     * @param dirName 目录名 (相对于当前 remotePath)
     */
    @Override
    public void removeDirectory(String dirName) {
        String fullPath = buildRemotePath(dirName);
        try {
            // 注意：检查Hutool文档以确定delDir是否是递归的。
            // 标准SFTP rmdir要求目录为空。
            // 如果需要递归删除，Hutool可能有另一个方法或
            // 你需要手动实现它（ls, 删除文件, 删除子目录, 然后rmdir）。
            hutoolSftp.delDir(fullPath);
            log.info("目录删除成功: {}", fullPath);
        } catch (Exception e) {
            log.error("删除目录失败: {}", fullPath, e);
            throw new RuntimeException("删除目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 切换SFTP服务器的工作目录
     *
     * @param dirName 要切换到的目录名 (可以是绝对路径或相对于当前 remotePath)
     */
    @Override
    public void changeDirectory(String dirName) {
        try {
            hutoolSftp.cd(dirName); // cd处理相对和绝对路径
            this.remotePath = hutoolSftp.pwd(); // 更新当前路径状态
            log.info("工作目录已切换到: {}", this.remotePath);
        } catch (Exception e) {
            log.error("切换目录失败: {}", dirName, e);
            throw new RuntimeException("切换目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当前远程工作目录
     * @return 当前工作目录路径
     */
    public String pwd() {
        try {
            return hutoolSftp.pwd();
        } catch (JschRuntimeException e) {
            log.error("获取当前工作目录失败", e);
            throw new JschRuntimeException("获取当前工作目录失败: " + e.getMessage(), e);
        }
    }



    /**
     * 根据当前remotePath和给定的相对/绝对路径构建绝对远程路径。
     * 正确处理路径分隔符。
     *
     * @param relativeOrAbsolutePath 要解析的路径。
     * @return 远程服务器上的绝对路径。
     */
    private String buildRemotePath(String relativeOrAbsolutePath) {
        if (StrUtil.isEmpty(relativeOrAbsolutePath) || ".".equals(relativeOrAbsolutePath)) {
            return this.remotePath; // 如果路径为空或"."，则返回当前目录
        }
        // 使用Hutool的FileUtil或简单逻辑进行路径连接，尊重绝对路径
        // SFTP通常使用'/'作为分隔符，而不考虑操作系统
        if (relativeOrAbsolutePath.startsWith(GlobalConstants.PATH_SEPARATOR)) {
            return relativeOrAbsolutePath; // 它已经是一个绝对路径
        } else {
            // 连接当前远程路径和相对路径
            if (this.remotePath.endsWith(GlobalConstants.PATH_SEPARATOR)) {
                return this.remotePath + relativeOrAbsolutePath;
            } else {
                return this.remotePath + GlobalConstants.PATH_SEPARATOR + relativeOrAbsolutePath;
            }
        }
    }
}
