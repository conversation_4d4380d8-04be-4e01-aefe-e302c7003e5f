package com.siteweb.as.net.transfer;

import com.siteweb.as.enums.TransferProtocolEnum;

/**
 * 文件传输客户端工厂类
 * 用于创建不同类型的文件传输客户端实例
 */
public class FileTransferClientFactory {

    /**
     * 根据传入的字符串协议创建文件传输客户端
     *
     * @param host 主机地址
     * @param username 用户名
     * @param password 密码
     * @param port 端口号
     * @param protocol 协议字符串
     * @return 文件传输客户端实例
     */
    public static FileTransferClient createClient(String host, String username, String password, int port, String protocol) {
        // 将字符串协议转换为枚举类型
        TransferProtocolEnum transferProtocolEnum = TransferProtocolEnum.fromProtocol(protocol);
        return createClient(host, username, password, port, transferProtocolEnum);
    }

    /**
     * 根据传入的协议枚举创建文件传输客户端
     *
     * @param host 主机地址
     * @param username 用户名
     * @param password 密码
     * @param port 端口号
     * @param transferProtocolEnum 传输协议枚举
     * @return 文件传输客户端实例
     * @throws IllegalArgumentException 当协议不支持时抛出异常
     */
    public static FileTransferClient createClient(String host, String username, String password, int port, TransferProtocolEnum transferProtocolEnum) {
        // 根据不同协议类型创建相应的客户端实例
        if (transferProtocolEnum == TransferProtocolEnum.FTP) {
            return new FtpClient(host, username, password, port);
        } else if (transferProtocolEnum == TransferProtocolEnum.SFTP) {
            return new SftpClient(host, username, password, port);
        }
        // 如果协议不支持，抛出异常
        throw new IllegalArgumentException("Unsupported transfer protocol");
    }
}

