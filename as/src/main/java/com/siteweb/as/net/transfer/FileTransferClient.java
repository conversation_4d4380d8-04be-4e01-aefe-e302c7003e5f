package com.siteweb.as.net.transfer;

import com.jcraft.jsch.JSchException;

import java.io.Closeable;
import java.io.IOException;
import java.util.List;

/**
 * 文件传输客户端接口，定义了所有文件传输客户端应支持的基本操作
 */
public interface FileTransferClient extends Closeable {

    /**
     * 连接并登录到服务器
     *
     * @throws IOException 如果连接或登录过程中发生错误
     */
    void login() throws IOException;

    /**
     * 断开与服务器的连接
     */
    void logout();

    /**
     * 列出当前目录的文件和目录
     *
     * @return 文件和目录名称列表
     * @throws IOException 如果操作过程中发生错误
     */
    List<String> listFiles() throws IOException;

    /**
     * 从服务器下载文件
     *
     * @param remoteFileName 远程文件名
     * @param localFilePath  本地保存文件路径
     * @throws IOException 如果下载过程中发生错误
     */
    void downloadFile(String remoteFileName, String localFilePath) throws IOException, JSchException;

    /**
     * 下载远程目录到本地，支持排除特定文件或目录
     *
     * @param remoteDirPath  远程目录路径
     * @param localParentDir 本地保存目录
     * @param excludePatterns 要排除的文件或目录的匹配模式列表（支持"*"通配符，如"*.log"）
     * @throws IOException 如果下载过程中发生错误
     */
    void downloadDirectory(String remoteDirPath, String localParentDir,List<String> excludePatterns) throws IOException;

    /**
     * 上传目录到服务器
     *
     * @param localPath  本地目录路径
     * @param serverPath 远程目录路径
     * @param recurse    是否递归上传子目录
     * @param mask       文件过滤规则
     * @return 上传成功返回 true，否则返回 false
     */
    boolean uploadDirectory(String localPath, String serverPath, boolean recurse, String mask);

    /**
     * 上传文件到服务器
     *
     * @param localFilePath  本地文件路径
     * @param remoteFileName 远程文件名
     */
    void uploadFile(String localFilePath, String remoteFileName);

    /**
     * 删除服务器上的文件
     *
     * @param remoteFileName 远程文件名
     * @throws IOException 如果删除过程中发生错误
     */
    void deleteFile(String remoteFileName) throws IOException;

    /**
     * 在服务器上创建目录
     *
     * @param dirName 目录名
     * @throws IOException 如果创建过程中发生错误
     */
    void createDirectory(String dirName) throws IOException;

    /**
     * 删除服务器上的目录
     *
     * @param dirName 目录名
     * @throws IOException 如果删除过程中发生错误
     */
    void removeDirectory(String dirName) throws IOException;

    /**
     * 切换服务器的工作目录
     *
     * @param dirName 要切换到的目录名
     * @throws IOException 如果切换过程中发生错误
     */
    void changeDirectory(String dirName) throws IOException;
}

