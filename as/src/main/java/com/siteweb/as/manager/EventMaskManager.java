package com.siteweb.as.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.as.dto.MaskDetailsDTO;
import com.siteweb.as.dto.shield.MasktimeDTO;
import com.siteweb.as.dto.shield.TimeGroupSpanDTO;
import com.siteweb.as.mapper.ShieldMapper;
import com.siteweb.common.util.HexUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 * Creation Date: 2024/12/25
 */
@Component
public class EventMaskManager {
    @Autowired
    private ShieldMapper shieldMapper;

    public MasktimeDTO getConfigMaskTimeGroup(String uniqueId, String objectType) {
        List<MaskDetailsDTO> list;
        if (Objects.equals(objectType, "ConfigEvent") || Objects.equals(objectType, "ActiveEventMask")) {
            int[] ids = splitEquipmentChildUniqueId(uniqueId);
            list = shieldMapper.getEventMask(ids[0], ids[1], ids[2]);
        } else if (Objects.equals(objectType, "ConfigEquipment")) {
            int[] ids = splitEquipmentUniqueId(uniqueId);
            list = shieldMapper.getEquipmentMask(ids[0], ids[1]);
        } else if (Objects.equals(objectType, "ConfigStation")) {
            Integer stationId = Integer.valueOf(uniqueId);
            list = shieldMapper.getStationMask(stationId);
        } else {
            return null;
        }
        MasktimeDTO masktimeDTO = new MasktimeDTO();
        if (CollUtil.isNotEmpty(list)) {
            masktimeDTO.setDataId(ObjectUtil.defaultIfNull(list.get(0).getTimeGroupId(), 0));
            masktimeDTO.setStarttime(list.get(0).getStartTime());
            masktimeDTO.setEndtime(list.get(0).getEndTime());
            masktimeDTO.setReason(list.get(0).getReason());
            masktimeDTO.setUserId(ObjectUtil.defaultIfNull(list.get(0).getUserId(), 0));
            masktimeDTO.setUserName(list.get(0).getUserName());
            List<TimeGroupSpanDTO> timeGroupSpans = new ArrayList<>();
            for (MaskDetailsDTO maskDetailsDTO : list) {
                if (CharSequenceUtil.isNotBlank(maskDetailsDTO.getTimeSpanChar())) {
                    TimeGroupSpanDTO timeGroupSpan = new TimeGroupSpanDTO();
                    timeGroupSpan.setWeek(maskDetailsDTO.getWeek() == null ? 0 : maskDetailsDTO.getWeek());
                    timeGroupSpan.setEndTime(maskDetailsDTO.getEndTime());
                    timeGroupSpan.setStartTime(maskDetailsDTO.getStartTime());
                    timeGroupSpan.setWeek(maskDetailsDTO.getWeek());
                    timeGroupSpan.setTimeSpanBool(HexUtil.hexStringToBooleanList(maskDetailsDTO.getTimeSpanChar()));
                    timeGroupSpans.add(timeGroupSpan);
                }

            }
            masktimeDTO.setTimeGroupSpans(timeGroupSpans);
            if (timeGroupSpans.size() > 0) {
                masktimeDTO.setType(2);
                masktimeDTO.setGroupCategory(2);
            } else {
                masktimeDTO.setType(1);
                masktimeDTO.setGroupCategory(1);
            }
        }

        return masktimeDTO;
    }

    public static int[] splitEquipmentChildUniqueId(String uniqueId) throws IllegalArgumentException {
        int[] result = new int[3];
        // 确保 result 的长度为 3，用于存储 stationId, equipmentId, eventId
        // 按 "." 分割 uniqueId
        String[] vals = uniqueId.split("\\.");
        try {
            // 转换为整数并存储到 result 数组中
            result[0] = NumberUtil.parseInt(ArrayUtil.get(vals, 0)); // stationId
            result[1] = NumberUtil.parseInt(ArrayUtil.get(vals, 1)); // equipmentId
            result[2] = NumberUtil.parseInt(ArrayUtil.get(vals, 2)); // objectId
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("UniqueId must contain valid integers.", e);
        }
        return result;
    }

    public static int[] splitEquipmentUniqueId(String uniqueId) throws IllegalArgumentException {
        int[] result = new int[2];
        // 用于存储 stationId, equipmentId
        String[] vals = uniqueId.split("\\.");
        try {
            // 转换为整数并存储到 result 数组中
            result[0] = NumberUtil.parseInt(ArrayUtil.get(vals, 0)); // stationId
            result[1] = NumberUtil.parseInt(ArrayUtil.get(vals, 1)); // equipmentId
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("UniqueId must contain valid integers.", e);
        }
        return result;
    }
}
