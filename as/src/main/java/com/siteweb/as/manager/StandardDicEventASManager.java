package com.siteweb.as.manager;

import com.siteweb.monitoring.entity.StandardDicEvent;
import com.siteweb.monitoring.mapper.StandardDicEventMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> zhou
 * @description StandardDicEventManager
 * @createTime 2022-08-10 12:55:01
 */
@Component
public class StandardDicEventASManager {

    private final Logger log = LoggerFactory.getLogger(StandardDicEventASManager.class);
    private static final ConcurrentHashMap<Integer, StandardDicEvent> STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    @Autowired
    StandardDicEventMapper standardDicEventMapper;

    @PostConstruct
    public void init() {
        List<StandardDicEvent> standardDicEvents = standardDicEventMapper.getAllStandardDicEvent();
        for (StandardDicEvent standardDicEvent : standardDicEvents) {

            STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.put(standardDicEvent.getStandardDicId(), standardDicEvent);
        }
        log.info("Init standardDicEvent from database, total {}", standardDicEvents.size());
    }

    public StandardDicEvent getStandardDicEvent(Integer standardDicId, Integer standardType) {
        return STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.get(standardDicId);
    }
    public List<StandardDicEvent> getAll() {
        return STANDARD_DIC_EVENT_CONCURRENT_HASH_MAP.values().stream().toList();
    }
}
