package com.siteweb.as.manager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.util.StringUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Component
public class QueryInformationIdManager {

    private static final ConcurrentHashMap<String, QueryInformation> QUERYINFO_HASH_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentLinkedQueue<QueryInformation> QUERYINFO_LIST = new ConcurrentLinkedQueue<>();
    private long maxSerialNo = 1000;

    public void addOne(QueryInformation queryInfo) {
        if (ObjectUtil.isNull(queryInfo)){
            return;
        }
        if(StringUtils.isNotEmpty(queryInfo.getQueryInformationId())){
            QUERYINFO_HASH_MAP.put(queryInfo.getQueryInformationId(),queryInfo);
            QUERYINFO_LIST.add(queryInfo);
        }
        if(QUERYINFO_HASH_MAP.size() > maxSerialNo){
            QueryInformation info = QUERYINFO_LIST.poll();
            QUERYINFO_HASH_MAP.remove(info.getQueryInformationId());
        }
    }

    public QueryInformation getById(String queryInfoId) {
        return QUERYINFO_HASH_MAP.get(queryInfoId);
    }

}
