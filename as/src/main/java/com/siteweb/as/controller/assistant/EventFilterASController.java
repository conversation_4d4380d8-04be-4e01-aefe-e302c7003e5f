package com.siteweb.as.controller.assistant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.service.ActiveEventFilterService;
import com.siteweb.as.service.HistorySelectionService;
import com.siteweb.as.service.StationDetailASService;
import com.siteweb.as.vo.CustomTemplateVO;
import com.siteweb.as.vo.ObjectSelectorRequestVO;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.HouseInfoDTO;
import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.dto.StationFilterDTO;
import com.siteweb.monitoring.mapper.StationMapper;
import com.siteweb.monitoring.service.HouseService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.utility.enums.DataEntryEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR> zhou
 * @description ActiveEventFilterTemplateController
 * @createTime 2022-04-27 19:20:33
 */
@RestController
@RequestMapping("/api")
@Api(value = "ActiveEventFilterTemplateController", tags = {"ActiveEventFilterTemplate操作接口"})
public class EventFilterASController {
    @Autowired
    ActiveEventFilterService activeEventFilterService;
    @Autowired
    HistorySelectionService historySelectionService;
    @Autowired
    StationService stationService;
    @Autowired
    StationMapper stationMapper;
    @Autowired
    StationDetailASService stationDetailASService;
    @Autowired
    HouseService houseService;
    @ApiOperation(value = "保存告警过滤条件")
    @PostMapping(value = "/assistant/eventfilter")
    public ResponseEntity<ResponseResult> updateEventFilter(@RequestBody CustomTemplateVO customTemplate) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(historySelectionService.saveEventFilter(userId, customTemplate));
    }

    @ApiOperation(value = "获取告警过滤条件模板")
    @GetMapping(value = "/assistant/eventfilter", params = {"filterType"})
    public ResponseEntity<ResponseResult> getEventFilterTemplate(String filterType) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(historySelectionService.findByUserIdAndFilterType(userId, filterType));
    }
    @ApiOperation(value = "删除告警过滤条件模板")
    @DeleteMapping(value = "/assistant/eventfilter", params = {"id"})
    public ResponseEntity<ResponseResult> delEventFilterTemplate(Integer id) {
        historySelectionService.deleteById(id);
        return ResponseHelper.successful();
    }

    @ApiOperation(value = "查询过滤条件参数来源")
    @PostMapping(value = "/assistant/objectselector")
    public ResponseEntity<ResponseResult> objectselector(@RequestBody ObjectSelectorRequestVO objectSelectorRequestVO) {
        if (ObjectUtil.isNotNull(objectSelectorRequestVO.getQueryInformations())){
            List<ObjectSelectorRequestVO.ParameterQueryInformation> parameterQueryInformation = objectSelectorRequestVO.getQueryInformations().getParameterQueryInformation();
            if (CollUtil.isEmpty(parameterQueryInformation)){
                return ResponseHelper.successful();
            }
            ObjectSelectorRequestVO.ParameterQueryInformation parameterQuery = parameterQueryInformation.get(0);
            StationFilterDTO filterDTO = stationDetailASService.getFilterDTOfromQueryInformation(new QueryInformation(parameterQuery.getQueryInformation()));
            Integer loginUserId = TokenUserUtil.getLoginUserId();
            switch (parameterQuery.getParameterName()){
                case "Station":
                    Set<Integer> stationIdSetByUser = stationService.findByRegionPermission(loginUserId);
                    Set<Integer> stationIdSetByCondition = stationMapper.findIdsByFilterCondition(filterDTO);
                    Set<Integer> listStationIds =new HashSet<>(CollUtil.intersection(stationIdSetByUser, stationIdSetByCondition));
                    return ResponseHelper.successful(stationService.findByIds(listStationIds)
                            .stream()
                            .map(e -> new IdValueDTO<>(e.getStationId(), e.getStationName()))
                            .toList());
                case "StationGroupType":
                    return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.STATION_GROUP_TYPE));
                case "StationGroup":
                    Integer structureGroupId =Integer.parseInt ((String) parameterQuery.getQueryInformation().getSingleValueComparisons().get(0).getValue());
                    return ResponseHelper.successful(activeEventFilterService.findGroupingNameById(structureGroupId));
                case "StationCategory":
                    return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.STATION_CATEGORY));
                case "EquipmentCategory":
                    return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY));
                case "EquipmentState":
                    return ResponseHelper.successful(activeEventFilterService.findEquipmentName(loginUserId, filterDTO));

            }

        }

        return ResponseHelper.successful();
    }

    @ApiOperation(value = "查询过滤条件机房")
    @PostMapping(value = "/config/confighouse")
    public ResponseEntity<ResponseResult> confighouse(@RequestBody StationFilterDTO stationFilterDTO) {
        Set<Integer> stationIdSetByUser = stationService.findByRegionPermission(TokenUserUtil.getLoginUserId());
        Set<Integer> stationIdSetByCondition = stationMapper.findIdsByFilterCondition(stationFilterDTO);
        Collection<Integer> intersection = CollUtil.intersection(stationIdSetByUser, stationIdSetByCondition);

        if (CollUtil.isEmpty(intersection)){
            return ResponseHelper.successful(Collections.emptyList());
        }
        return ResponseHelper.successful(houseService.findAllHouseInfo(intersection.stream().toList()));
    }


}
