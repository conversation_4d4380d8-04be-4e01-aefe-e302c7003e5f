package com.siteweb.as.controller.version;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.version.FSUProjectInfoDTO;
import com.siteweb.as.entity.IcsContractInstall;
import com.siteweb.as.service.MonitorUnitProjectInfoService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/version/fsucontract")
public class FSUContractController {
    @Autowired
    MonitorUnitProjectInfoService monitorUnitProjectInfoService;

    @GetMapping("/page")
    public ResponseEntity<ResponseResult> getContractInfo(FSUProjectInfoDTO fsuProjectInfoDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(monitorUnitProjectInfoService.findContractInfoPage(userId, fsuProjectInfoDTO));
    }

    @GetMapping("/export")
    public ResponseEntity<Resource> exportExcel(FSUProjectInfoDTO fsuProjectInfoDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(monitorUnitProjectInfoService::exportContractInfo, fsuProjectInfoDTO, userId);
    }

    @GetMapping("/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getContractInfo(@PathVariable Integer monitorUnitId) {
        return ResponseHelper.successful(monitorUnitProjectInfoService.findContractInfoByMonitorUnitId(monitorUnitId));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateContractInfo(@RequestBody FSUProjectInfoDTO fsuProjectInfoDTO) {
        return ResponseHelper.successful(monitorUnitProjectInfoService.updateContractInfo(fsuProjectInfoDTO));
    }
    @PutMapping("/batch")
    public ResponseEntity<ResponseResult> batchUpdateContractInfo(@RequestBody List<FSUProjectInfoDTO> fsuProjectInfoDTOList) {
        return ResponseHelper.successful(monitorUnitProjectInfoService.batchUpdateContractInfo(fsuProjectInfoDTOList));
    }
}
