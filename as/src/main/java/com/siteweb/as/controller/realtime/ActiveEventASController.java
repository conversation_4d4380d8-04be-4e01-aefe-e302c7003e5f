package com.siteweb.as.controller.realtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ActiveEventASDTO;
import com.siteweb.as.service.ActiveEventASService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 活动告警表
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:05
 */
@RestController
@RequestMapping("/api")
@Slf4j
@Api(value = "ActiveEventASController", tags = {"ActiveEvent操作接口"})
public class ActiveEventASController {

    @Autowired
    ActiveEventASService activeEventASService;

    @ApiOperation(value = "查询告警列表")
    @GetMapping("/realtime/activeevent")
    public ResponseEntity<ResponseResult> getActiveEventByFilterVOAndSeverityId(Pageable pageable, ActiveEventFilterVO activeEventFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Page<ActiveEventASDTO> activeEventDTOS = activeEventASService.findActiveEvents(userId, pageable, activeEventFilterVO);
        return ResponseHelper.successful(activeEventDTOS);
    }

    @ApiOperation(value = "查询告警列表")
    @GetMapping(value = "/realtime/activeevent" , params = {"stationId", "equipmentId"})
    public ResponseEntity<ResponseResult> getActiveEventByStationAndEquipment(int stationId, int equipmentId) {
        return ResponseHelper.successful(activeEventASService.getActiveEventByStationAndEquipment(stationId, equipmentId));
    }

    @ApiOperation(value = "活动告警等级统计")
    @GetMapping("/realtime/activeevent/statistics")
    public ResponseEntity<ResponseResult> getActiveEventStatistics(ActiveEventFilterVO activeEventFilterVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(activeEventASService.findActiveEventStatistics(userId, activeEventFilterVO));
    }

    @ApiOperation(value = "导出告警列表")
    @GetMapping(value = "/realtime/activeevent/export",params = {"columns","titles"})
    public ResponseEntity<Resource> excelActiveevent(String columns, String titles) {

        List<String> columnList = CharSequenceUtil.split(columns,",");
        List<String> titileList = CharSequenceUtil.split(titles,",");
        if (CollUtil.isEmpty(columnList) || columnList.size() != titileList.size()) {
            return ResponseEntity.badRequest().body(null);
        }
        return ExcelExportUtil.exportExcel(activeEventASService, TokenUserUtil.getLoginUserId(), columnList, titileList);

    }
}
