package com.siteweb.as.controller.patrol_standard;

import com.siteweb.as.entity.PatrolTask;
import com.siteweb.as.service.PatrolTaskService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/patrol_standard")
@Api(value = "PatrolStandardTaskController", tags = {"巡检任务操作接口"})
@RequiredArgsConstructor
public class PatrolStandardTaskController {
    private final PatrolTaskService patrolTaskService;


    @ApiOperation("获取全部巡检任务")
    @GetMapping(value = "/task")
    public ResponseEntity<ResponseResult> getAllPatrolTask() {
        return ResponseHelper.successful(patrolTaskService.getAllPatrolStandardTask());
    }

    @ApiOperation("获取巡检任务")
    @GetMapping(value = "/task", params = {"taskId"})
    public ResponseEntity<ResponseResult> getPatrolTask(Integer taskId) {
        return ResponseHelper.successful(patrolTaskService.getPatrolStandardTask(taskId));
    }

    @ApiOperation("新增巡检任务")
    @PostMapping(value = "/task")
    public ResponseEntity<ResponseResult> savePatrolTask(@RequestBody PatrolTask patrolTask) {
        patrolTaskService.savePatrolTask(patrolTask);
        return ResponseHelper.successful();
    }

    @ApiOperation("修改巡检任务")
    @PutMapping(value = "/task")
    public ResponseEntity<ResponseResult> updatePatrolTask(@RequestBody PatrolTask patrolTask) {
        patrolTaskService.updatePatrolTask(patrolTask);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除巡检任务")
    @DeleteMapping(value = "/task/{taskId}")
    public ResponseEntity<ResponseResult> deletePatrolTask(@PathVariable Integer taskId) {
        patrolTaskService.deletePatrolTask(taskId);
        return ResponseHelper.successful();
    }

    @ApiOperation("获取Cron表达式")
    @GetMapping(value = "/cron")
    public ResponseEntity<ResponseResult> getCronExpression() {
        return ResponseHelper.successful(patrolTaskService.getCronExpression());
    }

}
