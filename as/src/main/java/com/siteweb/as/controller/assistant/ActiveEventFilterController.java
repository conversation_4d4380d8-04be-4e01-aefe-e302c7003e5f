package com.siteweb.as.controller.assistant;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.monitoring.dto.StationFilterDTO;
import com.siteweb.as.service.ActiveEventFilterService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.enums.DataEntryEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/activeevent/filter")
public class ActiveEventFilterController {
    @Autowired
    ActiveEventFilterService activeEventFilterService;


    @ApiOperation(value = "获取分组方式")
    @GetMapping(value = "/groupingmethod")
    public ResponseEntity<ResponseResult> findGroupingMethod() {
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.STATION_GROUP_TYPE));
    }

    @ApiOperation(value = "通过分组id查询局站分组")
    @GetMapping(value = "/stationstructure", params = {"structureGroupId"})
    public ResponseEntity<ResponseResult> findByStructureGroupId(Integer structureGroupId) {
        return ResponseHelper.successful(activeEventFilterService.findGroupingNameById(structureGroupId));
    }

    @ApiOperation(value = "获取局站类型")
    @GetMapping(value = "/stationcategory")
    public ResponseEntity<ResponseResult> findStationCategory() {
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.STATION_CATEGORY));
    }

    @ApiOperation(value = "获取局站状态")
    @GetMapping(value = "/stationstate")
    public ResponseEntity<ResponseResult> findStationState() {
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.STATION_STATE));
    }

    @ApiOperation(value = "获取局站名称")
    @PostMapping(value = "/stationname")
    public ResponseEntity<ResponseResult> findStationName(@RequestBody StationFilterDTO stationFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(activeEventFilterService.findStationName(userId,stationFilterDTO));
    }

    @ApiOperation(value = "获取设备类型")
    @GetMapping(value = "/equipmentcategory")
    public ResponseEntity<ResponseResult> findEquipmentCategory() {
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY));
    }

    @ApiOperation(value = "获取设备名称")
    @PostMapping(value = "/equipmentname")
    public ResponseEntity<ResponseResult> findEquipmentName(@RequestBody StationFilterDTO stationFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(activeEventFilterService.findEquipmentName(userId, stationFilterDTO));
    }

    @ApiOperation(value = "获取事件种类")
    @GetMapping(value = "/eventcategory")
    public ResponseEntity<ResponseResult> findEventCategory() {
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.EVENT_CATEGORY));
    }

    @ApiOperation(value = "获取事件标准名称")
    @GetMapping(value = "/eventstandardname")
    public ResponseEntity<ResponseResult> findStandardName() {
        return ResponseHelper.successful(activeEventFilterService.findEventStandardName());
    }

    @ApiOperation(value = "获取告警等级")
    @GetMapping(value = "/eventlevel")
    public ResponseEntity<ResponseResult> findEventLevel() {
        return ResponseHelper.successful(activeEventFilterService.findEventLevel());
    }
    @ApiOperation(value = "获取派单状态")
    @GetMapping(value = "/eomsstatus")
    public ResponseEntity<ResponseResult> findEomsStatus() {
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.DISPATCH_STATUS));
    }

    @ApiOperation("获取屏蔽方式")
    @GetMapping(value = "/shieldmethod")
    public ResponseEntity<ResponseResult> findShieldMethod(){
        return ResponseHelper.successful(activeEventFilterService.findShieldMethod());
    }

    @ApiOperation("获取信号种类")
    @GetMapping(value = "/signalcategory")
    public ResponseEntity<ResponseResult> findSignalCategory(){
        return ResponseHelper.successful(activeEventFilterService.findIdValueByEntryId(DataEntryEnum.SIGNAL_CATEGORY));
    }

    @ApiOperation("获取信号基类")
    @GetMapping(value = "/signalbase")
    public ResponseEntity<ResponseResult> findSignalBase(){
        return ResponseHelper.successful(activeEventFilterService.findSignalBase());
    }
}
