package com.siteweb.as.controller.realtime;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.realtime.ActiveStructureResult;
import com.siteweb.as.dto.realtime.PagingInfo;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.as.dto.StationDetailASDTO;
import com.siteweb.as.service.StationDetailASService;
import com.siteweb.as.vo.QueryInformation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
public class MaintainController {
    @Autowired
    StationDetailASService stationDetailService;

    @ApiOperation(value = "获取局站工程状态列表")
    @PostMapping(value = "/realtime/maintain/station")
    public ResponseEntity<ResponseResult> StationProjectGet(@RequestBody QueryInformation queryInfo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        int page = 0, size = 100000;
        if (queryInfo.getPagingInformation() != null) {
            page = queryInfo.getPagingInformation().getCurrentPageIndex()-1;
            size = queryInfo.getPagingInformation().getPageSize();
        }
        PageRequest pageable = PageRequest.of(page, size, Sort.unsorted());
        //计算耗时
        Page<StationDetailASDTO> stationDetailDTOS = stationDetailService.queryPageableStationDetails(userId, pageable, queryInfo,true);
        long time1 = System.currentTimeMillis();
        ActiveStructureResult result = new ActiveStructureResult();
        PagingInfo pagingInfo = new PagingInfo();
        result.setActiveStructure(stationDetailDTOS.getContent());
        result.setQueryTimeTicks(System.currentTimeMillis() - time1);

        pagingInfo.setPageSize(size);
        pagingInfo.setCurrentPageIndex(page);
        pagingInfo.setTotalPagesCount(stationDetailDTOS.getTotalPages());
        pagingInfo.setPaging(true);
        pagingInfo.setCount(stationDetailDTOS.getTotalElements());
        result.setPagingInfo(pagingInfo);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "获取机房工程状态列表")
    @PostMapping(value = "/realtime/maintain/house")
    public ResponseEntity<ResponseResult> HouseProjectPost(@RequestBody QueryInformation queryInfo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        int page = 0, size = 1000;
        if (ObjectUtil.isNotNull(queryInfo.getPagingInformation()) && ObjectUtil.isNotNull(queryInfo.getQueryInformationId())) {
            page = queryInfo.getPagingInformation().getCurrentPageIndex()-1;
            size = queryInfo.getPagingInformation().getPageSize();
        }
        PageRequest pageable = PageRequest.of(page, size, Sort.unsorted());
        //计算耗时
        long time1 = System.currentTimeMillis();
        Page<StationDetailASDTO> stationDetailDTOS = stationDetailService.queryPageableHouseDetails(userId, pageable,queryInfo);
        ActiveStructureResult result = new ActiveStructureResult();
        PagingInfo pagingInfo = new PagingInfo();
        result.setActiveStructure(stationDetailDTOS.getContent());
        result.setQueryTimeTicks(System.currentTimeMillis() - time1);

        pagingInfo.setPageSize(size);
        pagingInfo.setCurrentPageIndex(page);
        pagingInfo.setTotalPagesCount(stationDetailDTOS.getTotalPages());
        pagingInfo.setPaging(true);
        pagingInfo.setCount(stationDetailDTOS.getTotalElements());
        result.setPagingInfo(pagingInfo);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "获取设备工程状态列表")
    @PostMapping(value = "/realtime/maintain/equipment")
    public ResponseEntity<ResponseResult> EquipProjecttPost(@RequestBody QueryInformation queryInfo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        int page = 0, size = 1000;
        if (ObjectUtil.isNotNull(queryInfo.getPagingInformation()) && ObjectUtil.isNotNull(queryInfo.getQueryInformationId())) {
            page = queryInfo.getPagingInformation().getCurrentPageIndex()-1;
            size = queryInfo.getPagingInformation().getPageSize();
        }
        PageRequest pageable = PageRequest.of(page, size, Sort.unsorted());
        //计算耗时
        Page<StationDetailASDTO> stationDetailDTOS = stationDetailService.queryPageableEquipDetails(userId, pageable,queryInfo);

        long time1 = System.currentTimeMillis();
        ActiveStructureResult result = new ActiveStructureResult();
        PagingInfo pagingInfo = new PagingInfo();
        result.setActiveStructure(stationDetailDTOS.getContent());
        result.setQueryTimeTicks(System.currentTimeMillis() - time1);

        pagingInfo.setPageSize(size);
        pagingInfo.setCurrentPageIndex(page);
        pagingInfo.setTotalPagesCount(stationDetailDTOS.getTotalPages());
        pagingInfo.setPaging(true);
        pagingInfo.setCount(stationDetailDTOS.getTotalElements());
        result.setPagingInfo(pagingInfo);
        return ResponseHelper.successful(result);
    }
}
