package com.siteweb.as.controller.eventshield;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.service.EventShieldService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
@Api(value = "StationShieldController", tags = {"事件屏蔽操作接口"})
public class EventShieldController {

    @Autowired
    EventShieldService eventShieldService;

    /**
     *查询和设置局站屏蔽时间组设置
     * @return
     */
    @ApiOperation(value = "查询事件屏蔽列表")
    @PostMapping("/eventshield/page")
    public ResponseEntity<ResponseResult> findStationMaksPage(@RequestBody ShieldFilterDTO shieldFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(eventShieldService.findEventMaksPage(shieldFilterDTO.generatePaginationInfo(), shieldFilterDTO, userId));
    }


    @ApiOperation(value ="删除屏蔽设置")
    @DeleteMapping("/eventshield/batch")
    public ResponseEntity<ResponseResult> deleteShield(@RequestBody List<String> ids){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(eventShieldService.deleteShield(userId, ids));
    }

    @ApiOperation("批量添加告警屏蔽")
    @PostMapping("/eventshield/batch")
    public ResponseEntity<ResponseResult> batchSaveShield(@RequestBody BatchCreateEventMaskDTO batchCreateEventMaskDTO){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(eventShieldService.batchSaveShield(userId, batchCreateEventMaskDTO));
    }

    @ApiOperation(value = "导出事件屏蔽")
    @PostMapping("/eventshield/eventexcel")
    public ResponseEntity<Resource> exportShield(@RequestBody ShieldFilterDTO shieldFilterDTO){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(eventShieldService::exportShield, shieldFilterDTO, userId);
    }
}
