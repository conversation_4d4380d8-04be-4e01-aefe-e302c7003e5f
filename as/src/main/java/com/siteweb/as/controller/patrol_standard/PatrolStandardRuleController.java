package com.siteweb.as.controller.patrol_standard;

import com.siteweb.as.dto.patrol.PatrolRuleImportDTO;
import com.siteweb.as.dto.patrol.PatrolStandardRuleImportDTO;
import com.siteweb.as.entity.PatrolRule;
import com.siteweb.as.service.PatrolRuleService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/patrol_standard")
@Api(value = "RuleStandardController", tags = {"规则配置操作接口"})
@RequiredArgsConstructor
public class PatrolStandardRuleController {
    private final PatrolRuleService patrolRuleService;

    @ApiOperation("获取全部规则配置")
    @GetMapping(value = "/rule")
    public ResponseEntity<ResponseResult> getAllPatrolStandardRule() {
        return ResponseHelper.successful(patrolRuleService.getAllPatrolStandardRule());
    }

    @ApiOperation("获取规则配置")
    @GetMapping(value = "/rule", params = {"ruleId"})
    public ResponseEntity<ResponseResult> getPatrolStandardRule(Integer ruleId) {
        return ResponseHelper.successful(patrolRuleService.getPatrolStandardRule(ruleId));
    }

    @ApiOperation("根据分组获取规则配置")
    @GetMapping(value = "/rule", params = {"groupId"})
    public ResponseEntity<ResponseResult> getPatrolRuleByGroup(Integer groupId) {
        return ResponseHelper.successful(patrolRuleService.getPatrolStandardRuleByGroup(groupId));
    }

    @ApiOperation("新增规则配置")
    @PostMapping(value = "/rule")
    public ResponseEntity<ResponseResult> savePatrolRule(@RequestBody PatrolRule patrolRule) {
        patrolRuleService.savePatrolRule(patrolRule);
        return ResponseHelper.successful();
    }

    @ApiOperation("修改规则配置")
    @PutMapping(value = "/rule")
    public ResponseEntity<ResponseResult> updatePatrolRule(@RequestBody PatrolRule patrolRule) {
        patrolRuleService.updatePatrolRule(patrolRule);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除规则配置")
    @DeleteMapping(value = "/rule/{ruleId}")
    public ResponseEntity<ResponseResult> deletePatrolRule(@PathVariable Integer ruleId) {
        patrolRuleService.deletePatrolRule(ruleId);
        return ResponseHelper.successful();
    }

    @ApiOperation("获取计算操作信息")
    @GetMapping(value = "/calop")
    public ResponseEntity<ResponseResult> listPatrolCalop() {
        return ResponseHelper.successful(patrolRuleService.listPatrolCalop());
    }

    @ApiOperation("获取单位信息")
    @GetMapping(value = "/unit")
    public ResponseEntity<ResponseResult> listPatrolUnit() {
        return ResponseHelper.successful(patrolRuleService.listPatrolUnit());
    }



    @ApiOperation("获取标准化设备")
    @GetMapping(value = "/standardequipment")
    public ResponseEntity<ResponseResult> listStandardEquipment() {
        return ResponseHelper.successful(patrolRuleService.listStandardEquipment());
    }

    @ApiOperation("获取标准化信号")
    @GetMapping(value = "/standardsignal")
    public ResponseEntity<ResponseResult> listBaseSignal(Integer equipmentLogicClassId) {
        return ResponseHelper.successful(patrolRuleService.listStandardSignal(equipmentLogicClassId));
    }



    @ApiOperation("导入规则配置")
    @PostMapping(value = "/ruleImport", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importPatrolRules(@RequestBody List<PatrolStandardRuleImportDTO> patrolRuleImportDTOS) {
        return ResponseHelper.successful(patrolRuleService.importPatrolStandardRules(patrolRuleImportDTOS));
    }

}
