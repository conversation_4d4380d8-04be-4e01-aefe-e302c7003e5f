package com.siteweb.as.controller.permission;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.service.AuthorityService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "CurrentUserController", tags = {"CurrentUser操作接口"})
public class CurrentUserController {

    @Autowired
    AuthorityService authorityService;

    /**
     * 获取权限
     * @param permissionType 权限类型 1：操作权限；
     * @return
     */
    @ApiOperation(value = "获取权限")
    @GetMapping(value = "/permission/currentuser")
    public ResponseEntity<ResponseResult> getCurrentUser(int permissionType) {

        Integer userId = TokenUserUtil.getLoginUserId();
        List<Integer> info = authorityService.findOperationByUserId(userId);
        return ResponseHelper.successful(info);
    }
}
