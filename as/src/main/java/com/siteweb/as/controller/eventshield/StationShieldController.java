package com.siteweb.as.controller.eventshield;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.service.StationShieldService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.monitoring.dto.StationMaskDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api")
@Api(value = "StationShieldController", tags = {"局站屏蔽操作接口"})
public class StationShieldController {
    @Autowired
    StationShieldService stationShieldService;

    /**
     *查询和设置局站屏蔽时间组设置
     * @return
     */
    @ApiOperation(value = "查询局站屏蔽列表")
    @PostMapping("/stationshield/page")
    public ResponseEntity<ResponseResult> findStationMaksPage(@RequestBody ShieldFilterDTO shieldFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(stationShieldService.findStationMaksPage(shieldFilterDTO.generatePaginationInfo(), shieldFilterDTO, userId));
    }

    @ApiOperation("批量删除局站屏蔽")
    @DeleteMapping("/stationshield/batch")
    public ResponseEntity<ResponseResult> batchDeleteStationMask(@RequestBody List<Integer> stationIds) {
        return ResponseHelper.successful(stationShieldService.batchDeleteStationMask(TokenUserUtil.getLoginUserId(),stationIds));
    }

    @ApiOperation("批量设置局站屏蔽")
    @PostMapping("/stationshield/batch")
    public ResponseEntity<ResponseResult> batchSetStationMask(@RequestBody BatchSetStationMaskDTO batchSetStationMaskDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(stationShieldService.batchSetStationMask(userId,batchSetStationMaskDTO));
    }

    @ApiOperation(value = "导出局站屏蔽")
    @PostMapping("/stationshield/stationexcel")
    public ResponseEntity<Resource> exportShield(@RequestBody ShieldFilterDTO shieldFilterDTO){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(stationShieldService::exportShield,shieldFilterDTO,userId);
    }

    @ApiOperation(value = "获取站点屏蔽")
    @GetMapping(value = "/stationshield", params = {"stationId"})
    public ResponseEntity<ResponseResult> getStationShield(Integer stationId) {
        StationMaskDTO stationMask = stationShieldService.getStationMaskById(stationId);
        if (Objects.isNull(stationMask)) {
            return ResponseHelper.successful("0");
        }
        return ResponseHelper.successful(stationMask);
    }
}
