package com.siteweb.as.controller.realtime;

import cn.hutool.json.JSONUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.entity.CustomInfo;
import com.siteweb.as.service.CustomInfoService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 活动告警表
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:05
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustomeInfoController", tags = {"用户配置操作接口"})
public class CustomeInfoController {

    @Autowired
    CustomInfoService customInfoService;

    @ApiOperation(value = "获取用户设置")
    @GetMapping(value = "/realtime/custom")
    public ResponseEntity<ResponseResult> getCustomInfo(String customType) {
        Integer userId = TokenUserUtil.getLoginUserId();
        CustomInfo costomInfo = customInfoService.getCustomInfoAndUserId(userId, customType);
        if (Objects.isNull(costomInfo)) {
            return ResponseHelper.successful();
        }
        costomInfo.setCustomContent(JSONUtil.xmlToJson(costomInfo.getCustomContent()).toString());
        return ResponseHelper.successful(costomInfo);
    }

    @ApiOperation(value = "保存定制信息")
    @PostMapping(value = "/realtime/custom")
    public ResponseEntity<ResponseResult> saveCustom(@RequestBody CustomInfo customInfo){
        Integer userId = TokenUserUtil.getLoginUserId();
        customInfo.setUserId(userId);

        return ResponseHelper.successful(customInfoService.saveCustomInfo(customInfo));
    }
}
