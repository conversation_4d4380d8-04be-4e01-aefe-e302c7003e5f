package com.siteweb.as.controller.version;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.version.StationProjectInfoDTO;
import com.siteweb.as.schedule.SyncFsuFtpDataSchedule;
import com.siteweb.as.service.StationProjectInfoService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/version/stationcontract")
public class StationContractController {
    @Autowired
    StationProjectInfoService stationProjectInfoService;
    @Autowired
    SyncFsuFtpDataSchedule syncFsuFtpDataSchedule;

    @GetMapping("/page")
    public ResponseEntity<ResponseResult> getStationContractInfo(StationProjectInfoDTO stationProjectInfoDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(stationProjectInfoService.findContractInfoPage(userId,stationProjectInfoDTO));
    }

    @GetMapping("/export")
    public ResponseEntity<Resource> exportExcel(StationProjectInfoDTO stationProjectInfoDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(stationProjectInfoService::exportContractInfo, stationProjectInfoDTO, userId);
    }

    @GetMapping("/{stationId}")
    public ResponseEntity<ResponseResult> updateStationContractInfo(@PathVariable Integer stationId) {
        return ResponseHelper.successful(stationProjectInfoService.findContractInfoByStationId(stationId));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateStationContractInfo(@RequestBody StationProjectInfoDTO stationProjectInfoDTO) {
        return ResponseHelper.successful(stationProjectInfoService.updateContractInfo(stationProjectInfoDTO));
    }

    @PutMapping("/batch")
    public ResponseEntity<ResponseResult> batchUpdateContractInfo(@RequestBody List<StationProjectInfoDTO> stationProjectInfoDTOList) {
        return ResponseHelper.successful(stationProjectInfoService.batchUpdateContractInfo(stationProjectInfoDTOList));
    }

    @GetMapping("/ftpdownload")
    public ResponseEntity<ResponseResult> testSchedule() throws IOException {
        syncFsuFtpDataSchedule.syncFsuFile();
        return ResponseHelper.successful("ok");
    }
}
