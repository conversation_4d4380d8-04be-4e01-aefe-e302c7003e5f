package com.siteweb.as.controller.patrol;

import com.siteweb.as.dto.patrol.AlarmInfoQueryDTO;
import com.siteweb.as.service.AlarmInfoService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api/patrol")
@Api(value = "PatrolTaskController", tags = {"预警信息操作接口"})
@RequiredArgsConstructor
public class AlarmInfoController {
    private final AlarmInfoService alarmInfoService;

    @ApiOperation("获取全部预警信息")
    @GetMapping(value = "/alarminfo")
    public ResponseEntity<ResponseResult> getAllAlarmInfo(AlarmInfoQueryDTO alarmInfoQueryDTO) {
        return ResponseHelper.successful(alarmInfoService.getAllAlarmInfo(alarmInfoQueryDTO));
    }

    @ApiOperation(value = "导出当月预警信息")
    @GetMapping(value = "/alarminfoexport")
    public ResponseEntity<Resource> exportAlarmInfo() {
        return ExcelExportUtil.exportExcel(alarmInfoService::exportAlarmInfo);
    }

}
