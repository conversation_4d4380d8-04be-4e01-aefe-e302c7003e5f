package com.siteweb.as.controller.patrol;

import com.siteweb.as.dto.patrol.PatrolRuleImportDTO;
import com.siteweb.as.entity.PatrolRule;
import com.siteweb.as.service.PatrolRuleService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/patrol")
@Api(value = "RuleController", tags = {"规则配置操作接口"})
@RequiredArgsConstructor
public class PatrolRuleController {
    private final PatrolRuleService patrolRuleService;

    @ApiOperation("获取全部规则配置")
    @GetMapping(value = "/rule")
    public ResponseEntity<ResponseResult> getAllPatrolRule() {
        return ResponseHelper.successful(patrolRuleService.getAllPatrolRule());
    }

    @ApiOperation("获取规则配置")
    @GetMapping(value = "/rule", params = {"ruleId"})
    public ResponseEntity<ResponseResult> getPatrolRule(Integer ruleId) {
        return ResponseHelper.successful(patrolRuleService.getPatrolRule(ruleId));
    }

    @ApiOperation("根据分组获取规则配置")
    @GetMapping(value = "/rule", params = {"groupId"})
    public ResponseEntity<ResponseResult> getPatrolRuleByGroup(Integer groupId) {
        return ResponseHelper.successful(patrolRuleService.getPatrolRuleByGroup(groupId));
    }

    @ApiOperation("新增规则配置")
    @PostMapping(value = "/rule")
    public ResponseEntity<ResponseResult> savePatrolRule(@RequestBody PatrolRule patrolRule) {
        patrolRuleService.savePatrolRule(patrolRule);
        return ResponseHelper.successful();
    }

    @ApiOperation("修改规则配置")
    @PutMapping(value = "/rule")
    public ResponseEntity<ResponseResult> updatePatrolRule(@RequestBody PatrolRule patrolRule) {
        patrolRuleService.updatePatrolRule(patrolRule);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除规则配置")
    @DeleteMapping(value = "/rule/{ruleId}")
    public ResponseEntity<ResponseResult> deletePatrolRule(@PathVariable Integer ruleId) {
        patrolRuleService.deletePatrolRule(ruleId);
        return ResponseHelper.successful();
    }

    @ApiOperation("获取计算操作信息")
    @GetMapping(value = "/calop")
    public ResponseEntity<ResponseResult> listPatrolCalop() {
        return ResponseHelper.successful(patrolRuleService.listPatrolCalop());
    }

    @ApiOperation("获取单位信息")
    @GetMapping(value = "/unit")
    public ResponseEntity<ResponseResult> listPatrolUnit() {
        return ResponseHelper.successful(patrolRuleService.listPatrolUnit());
    }

    @ApiOperation("获取预警等级信息")
    @GetMapping(value = "/warninglevel")
    public ResponseEntity<ResponseResult> listPatrolWarningLevel() {
        return ResponseHelper.successful(patrolRuleService.listPatrolWarningLevel());
    }

    @ApiOperation("获取基类设备")
    @GetMapping(value = "/baseequipment")
    public ResponseEntity<ResponseResult> listBaseEquipment() {
        return ResponseHelper.successful(patrolRuleService.listBaseEquipment());
    }

    @ApiOperation("获取基类信号")
    @GetMapping(value = "/basesignal")
    public ResponseEntity<ResponseResult> listBaseSignal(Integer baseEquipmentId) {
        return ResponseHelper.successful(patrolRuleService.listBaseSignal(baseEquipmentId));
    }

    @ApiOperation("获取标准化信息")
    @GetMapping(value = "/standardinfo")
    public ResponseEntity<ResponseResult> listStandardInfo(Long baseTypeId) {
        return ResponseHelper.successful(patrolRuleService.listStandardInfo(baseTypeId));
    }

    @ApiOperation("导入规则配置")
    @PostMapping(value = "/ruleImport", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importPatrolRules(@RequestBody List<PatrolRuleImportDTO> patrolRuleImportDTOS) {
        return ResponseHelper.successful(patrolRuleService.importPatrolRules(patrolRuleImportDTOS));
    }

}
