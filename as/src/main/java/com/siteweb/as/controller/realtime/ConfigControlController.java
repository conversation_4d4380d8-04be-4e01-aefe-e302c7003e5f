package com.siteweb.as.controller.realtime;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.service.*;
import com.siteweb.as.dto.ConfigCtrolAS;
import com.siteweb.as.manager.QueryInformationIdManager;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.utility.entity.CommandBaseDic;
import com.siteweb.utility.service.CommandBaseDicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 设备组态的实时数据接口
 */
@RestController
@RequestMapping("/api")
@Api(value="GraphicRealDataController",tags={"组态实时数据接口"})
public class ConfigControlController {


    @Autowired
    ControlService controlService;
    @Autowired
    QueryInformationIdManager queryInfoIdManager;
    @Autowired
    private CommandBaseDicService commandBaseDicService;
    @ApiOperation(value = "获取设备的控制")
    @GetMapping(value = "/realtime/configcontrol", params = {"stationId","equipmentId"})
    public ResponseEntity<ResponseResult> getActiveControl(Integer stationId, Integer equipmentId) {

        List<ConfigControlItem> configControlItems = controlService.findControlItemsByEquipmentId(equipmentId);

        List<ConfigCtrolAS> configCtrlList = new ArrayList<>();
        for (ConfigControlItem item : configControlItems) {
            ConfigCtrolAS ctrl = new ConfigCtrolAS();
            ctrl.from(item);
            if(ctrl.getBaseTypeId() != null) {
                CommandBaseDic dic = commandBaseDicService.findById(ctrl.getBaseTypeId());
                if (dic != null)
                    ctrl.setStandardTypeName(dic.getBaseTypeName());
            }
            configCtrlList.add(ctrl);
        }
        HashMap resultMap = new HashMap();
        QueryInformation queryInfo = new QueryInformation();
        String uuid = UUID.randomUUID().toString();
        queryInfo.setQueryInformationId(uuid);
        queryInfo.setEquipmentId(equipmentId);
        queryInfoIdManager.addOne(queryInfo);

        resultMap.put("queryInformatoinId", uuid);
        resultMap.put("configControls", configCtrlList);
        return  ResponseHelper.successful(resultMap, HttpStatus.OK);
    }
    @ApiOperation(value = "获取设备的控制")
    @GetMapping(value = "/realtime/configcontrol", params = {"queryInformationId"})
    public ResponseEntity<ResponseResult> getActiveControlByqueryInformationId(String queryInformationId) {

        QueryInformation queryInfo = queryInfoIdManager.getById(queryInformationId);
        List<ConfigControlItem> configControlItems = controlService.findControlItemsByEquipmentId(queryInfo.getEquipmentId());
        List<ConfigCtrolAS> configCtrlList = new ArrayList<>();
        for (ConfigControlItem item : configControlItems) {
            ConfigCtrolAS ctrl = new ConfigCtrolAS();
            ctrl.from(item);
            if(ctrl.getBaseTypeId() != null) {
                CommandBaseDic dic = commandBaseDicService.findById(ctrl.getBaseTypeId());
                if (dic != null)
                    ctrl.setStandardTypeName(dic.getBaseTypeName());
            }
            configCtrlList.add(ctrl);
        }
        return ResponseHelper.successful(configCtrlList, HttpStatus.OK);
    }

}
