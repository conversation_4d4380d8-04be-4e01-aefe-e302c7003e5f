package com.siteweb.as.controller.version;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.as.dto.version.IcsContractMaintenanceDTO;
import com.siteweb.as.entity.IcsContractMaintenance;
import com.siteweb.as.service.IcsContractMaintenanceService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/maintenancecontract")
public class IcsContractMaintenanceController {
    @Autowired
    IcsContractMaintenanceService icsContractMaintenanceService;

    @GetMapping("/page")
    public ResponseEntity<ResponseResult> getContractInfoList(IcsContractMaintenanceDTO contractMaintenanceDTO) {
        return ResponseHelper.successful(icsContractMaintenanceService.findContractInfoPage(contractMaintenanceDTO));
    }
    @GetMapping("/export")
    public ResponseEntity<Resource> exportContractList(IcsContractMaintenanceDTO contractMaintenanceDTO) {
        return ExcelExportUtil.exportExcel(icsContractMaintenanceService::exportContractInfo, contractMaintenanceDTO);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> getContractInfoById(@PathVariable Integer id) {
        return ResponseHelper.successful(icsContractMaintenanceService.findContractInfoById(id));
    }

    @PostMapping
    public ResponseEntity<ResponseResult> createContractInfo(@RequestBody IcsContractMaintenanceDTO contractMaintenanceDTO) {
        IcsContractMaintenance icsContractMaintenance = BeanUtil.copyProperties(contractMaintenanceDTO, IcsContractMaintenance.class);
        return ResponseHelper.successful(icsContractMaintenanceService.createContractInfo(icsContractMaintenance));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseResult> deleteContractInfo(@PathVariable Integer id) {
        return ResponseHelper.successful(icsContractMaintenanceService.deleteContractInfoById(id));
    }

    @DeleteMapping(params = "ids")
    public ResponseEntity<ResponseResult> updateContractInfo(String ids) {
        List<Integer> idList = StringUtils.splitToIntegerList(ids);
        return ResponseHelper.successful(icsContractMaintenanceService.deleteContractInfoByIds(idList));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> editContractInfo(@RequestBody IcsContractMaintenance icsContractMaintenance) {
        return ResponseHelper.successful(icsContractMaintenanceService.updateContractInfo(icsContractMaintenance));
    }
}
