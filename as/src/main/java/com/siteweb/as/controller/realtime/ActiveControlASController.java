package com.siteweb.as.controller.realtime;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ActiveControlASDTO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.entity.ActiveControl;
import com.siteweb.monitoring.entity.ControlMeanings;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.mapper.ActiveControlMapper;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.service.ControlService;
import com.siteweb.monitoring.vo.ActiveControlOperationVO;
import com.siteweb.monitoring.vo.ControlCommandVO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 活动告警表
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:40:05
 * TODO 此类重复了，可以考虑删除，使用ActiveControlController
 */
@RestController
@RequestMapping("/api")
@Api(value = "ActiveControlASController", tags = {"ActiveControl操作接口"})
public class ActiveControlASController {
    @Autowired
    private ActiveControlMapper activeControlMapper;

    @Autowired
    private DataItemService dataItemService;
    @Autowired
    private ActiveControlService activeControlService;
    @Autowired
    ControlService controlService;
    @Deprecated
    @ApiOperation(value = "获取设备活动控制")
    @GetMapping(value = "/realtime/ActiveControl")
    public ResponseEntity<ResponseResult> getActiveControl(Integer equipmentId) {
        List<ActiveControl> activeControls = activeControlMapper.getActiveControlByEquipmentId(equipmentId);

        List<ActiveControlASDTO> dtolist = new ArrayList<>();
        for (ActiveControl dto : activeControls) {
            ActiveControlASDTO asdto = new ActiveControlASDTO();
            asdto.setSequenceId(dto.getSerialNo());
            asdto.setId(dto.getControlId());
            asdto.setName(dto.getControlName());
            asdto.setStartTime(dto.getStartTime());
            asdto.setUserName(dto.getControlExecuterIdName());

            ConfigControlItem cfg = controlService.findSimpleControlDTOsByEquipmentIdAndControlId(dto.getEquipmentId(),dto.getControlId());
            String[] par = dto.getParameterValues().split(",");
            if(par.length >= 2) {
                if (cfg.getCommandType() == 1) {
                    asdto.setSetValue(par[1]);
                } else {
                    Integer value = (int)Float.parseFloat(par[1]);
                    Optional<ControlMeanings> meanings = cfg.getControlMeaningsList().stream().filter(o -> o.getParameterValue() == value).findFirst();
                    if (meanings.isPresent()) {
                        asdto.setSetValue(meanings.get().getMeanings());
                    }
                }
            }
            asdto.setStandardTypeName(dto.getBaseTypeName());
            asdto.setResult(dto.getControlResultType());
            asdto.setIsExcuing(dto.getControlResultType() == 4);
            asdto.setStationName(dto.getStationName());
            asdto.setEquipmentName(dto.getEquipmentName());
            DataItem data = dataItemService.findByEntryIdIdAndItemId(31, dto.getControlType());
            if (data != null)
                asdto.setControlType(data.getItemValue());
            dtolist.add(asdto);
        }

        return ResponseHelper.successful(dtolist);
    }

    @ApiOperation(value = "确认活动控制")
    @DeleteMapping(value = "/realtime/ActiveControl", params = "id")
    public ResponseEntity<ResponseResult> delActiveControl(String id) {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<Integer> serialNos = StringUtils.splitToIntegerList(id);
        ActiveControlOperationVO activeControlOperationVO = new ActiveControlOperationVO();
        activeControlOperationVO.setSerialNos(serialNos);
        boolean result = activeControlService.confirmControlCommand(userId, activeControlOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.SEND_CONTROL_FAIL.value()),
                "confirm active control error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }
    @ApiOperation(value = "发送控制")
    @PostMapping(value = "/realtime/ActiveControl", params = {"stationId", "equipmentId", "controlid", "setvalue", "startTime", "description"})
    public ResponseEntity<ResponseResult> postActiveControl(int stationId, int equipmentId, int controlid,
           @RequestParam @DateTimeFormat(pattern ="yyyy-MM-dd'T'HH:mm:ss") Date startTime, String setvalue, String description) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "userid is null", HttpStatus.BAD_REQUEST);
        }
        ControlCommandVO controlCommandVO = new ControlCommandVO();
        controlCommandVO.setStationId(stationId);
        controlCommandVO.setEquipmentId(equipmentId);
        controlCommandVO.setControlId(controlid);
        controlCommandVO.setStartTime(startTime);
        controlCommandVO.setSetValue(setvalue);
        controlCommandVO.setDescription(description);
        ControlResultType result = activeControlService.sendControlCommand(controlCommandVO, userId);
        if (result.equals(ControlResultType.SUCCESS))
            return ResponseHelper.successful(result.toString());
        else {
            return ResponseHelper.failed(String.valueOf(result.value()),
                    result.toString(), HttpStatus.OK);
        }
    }
    @ApiOperation(value = "重发送控制")
    @PutMapping(value = "/realtime/ActiveControl", params = {"id"})
    public ResponseEntity<ResponseResult> putActiveControl(int id) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "userid is null",                    HttpStatus.BAD_REQUEST);
        }
        List<Integer> serialNos = new ArrayList<>();
        serialNos.add(id);
        ActiveControlOperationVO activeControlOperationVO = new ActiveControlOperationVO();
        activeControlOperationVO.setSerialNos(serialNos);
        boolean result = activeControlService.reSendControlCommand(userId, activeControlOperationVO);
        if (result) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.SEND_CONTROL_FAIL.value()),
                "resend active control error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
