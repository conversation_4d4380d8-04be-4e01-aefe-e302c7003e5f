package com.siteweb.as.controller.permission;

import com.siteweb.admin.entity.SpecialtyGroup;
import com.siteweb.admin.entity.SpecialtyGroupMap;
import com.siteweb.admin.service.SpecialtyGroupMapService;
import com.siteweb.admin.service.SpecialtyGroupService;
import com.siteweb.admin.vo.SpecialtyGroupVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou @Description SpecialtyGroupController
 * @createTime 2022-01-17 08:48:23
 */
@RestController
@RequestMapping("/api")
@Api(value = "SpecialtyGroupASController", tags = {"专业权限组操作接口"})
public class SpecialtyGroupASController {

    @Autowired
    SpecialtyGroupService specialtyGroupService;
    @Autowired
    SpecialtyGroupMapService specialtyGroupMapService;
    @Operation(summary = "查询所有专业权限组")
    @GetMapping(value = "/permission/specialtygroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllSpecialtyGroups() {
        List<SpecialtyGroup> specialtyGroups = specialtyGroupService.findAllSpecialtyGroups();
        return ResponseHelper.successful(specialtyGroups);
    }
    @Operation(summary = "产生一个id")
    @GetMapping(value = "/permission/specialtygroup", params = {"generateId"})
    public ResponseEntity<ResponseResult> createSpecialtyGroupId(Integer generateId)
    {
        return ResponseHelper.successful("0");
    }
    @Operation(summary = "创建一个专业权限组")
    @PostMapping(value = "/permission/specialtygroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createSpecialtyGroup(
            @Valid @RequestBody SpecialtyGroupVO specialtyGroupVO) {
        if (0 != specialtyGroupVO.getSpecialtyGroupId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "A new SpecialtyGroup cannot have an specialtyGroupId",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = specialtyGroupService.createSpecialtyGroup(specialtyGroupVO.build());
        if (result > 0) {
            specialtyGroupVO.setSpecialtyGroupId(result);
            return ResponseHelper.successful(specialtyGroupVO);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.PRIMARY_KEY_ASSIGN_ERROR.value()),
                    "Can not get specialtyGroupId",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @Operation(summary = "修改一个专业权限组")
    @PutMapping(value = "/permission/specialtygroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateSpecialtyGroup(
            @Valid @RequestBody SpecialtyGroupVO specialtyGroupVO) {
        if (null == specialtyGroupVO.getSpecialtyGroupId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "specialtyGroupId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = specialtyGroupService.updateSpecialtyGroup(specialtyGroupVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "SpecialtyGroupId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }
    @Operation(summary = "根据专业权限组ID删除单个专业权限组")
    @DeleteMapping(value = "/permission/specialtygroup", params = {"id"})
    public ResponseEntity<ResponseResult> deleteSpecialtyGroupById(Integer id) {
        SpecialtyGroup specialtyGroup = specialtyGroupService.findById(id);
        if (specialtyGroup == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        specialtyGroupService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }
    @ApiOperation(value = "根据SpecialtyGroupId查询SpecialtyGroupMap实体列表")
    @GetMapping(value = "/permission/specialtygroup", params = {"id"})
    public ResponseEntity<ResponseResult> getSpecialtyGroupMapsById(Integer id) {
        List<SpecialtyGroupMap> specialtyGroupMaps = new ArrayList<>();
        specialtyGroupMaps = specialtyGroupMapService.findBySpecialtyGroupId(id);

        return ResponseHelper.successful(specialtyGroupMaps);
    }
}
