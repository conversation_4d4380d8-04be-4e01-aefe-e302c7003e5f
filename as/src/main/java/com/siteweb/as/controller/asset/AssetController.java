package com.siteweb.as.controller.asset;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import com.siteweb.as.service.AssetService;
import com.siteweb.as.vo.AssetVO;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api")
@Api(value = "Asset", tags = {"资产管理"})
public class AssetController {

    @Autowired
    private AssetService assetService;
    @Autowired
    private DataItemService dataItemService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @ApiOperation(value = "获取资产表")
    @GetMapping(value = "/Asset/asset")
    public ResponseEntity<ResponseResult> getAsset() {
        return ResponseHelper.successful(assetService.findAsset());
    }

    @ApiOperation(value = "新增资产")
    @PostMapping(value = "/Asset/asset")
    public ResponseEntity<ResponseResult> addAsset(@RequestBody AssetVO asset) {
        Integer res = assetService.addAsset(asset);
        if (res == 0) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    ErrorCode.REQUEST_PARAMETER_INVALID.getReasonPhrase(), HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful("true");
    }
    @ApiOperation(value = "修改资产")
    @PutMapping(value = "/Asset/asset")
    public ResponseEntity<ResponseResult> updateAsset(@RequestBody AssetVO asset) {
        Integer res = assetService.putAsset(asset);
        if (res == 0) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    ErrorCode.REQUEST_PARAMETER_INVALID.getReasonPhrase(), HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful("true");
    }
    @ApiOperation(value = "删除资产")
    @DeleteMapping(value = "/Asset/asset", params = {"assetId"})
    public ResponseEntity<ResponseResult> delAsset(Integer assetId) {
        Integer res = assetService.delAsset(assetId);
        if (res == 0) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    ErrorCode.REQUEST_PARAMETER_INVALID.getReasonPhrase(), HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful("true");
    }
    @ApiOperation(value = "导入资产")
    @PostMapping(value = "/Asset/assetImport")
    public ResponseEntity<ResponseResult> importAsset(@RequestParam MultipartFile file) throws IOException {

        Workbook workbook = null;
        InputStream inputStream = null;
        //传入的MultipartFile类型的excel文件
        inputStream = file.getInputStream();
        String originalFilename = file.getOriginalFilename();

        //判断是否为2017版本,如果是xlsx,就是XSSFWorkbook,如果是xls,就是HSSFWorkbook
        if (originalFilename != null && originalFilename.matches("^(?i)(xlsx)$")) {
            workbook = new XSSFWorkbook(inputStream);
        }else {
            workbook = new HSSFWorkbook(inputStream);
        }
        //得到excel第一个工作表sheet对象
        Sheet sheet = workbook.getSheetAt(0);
        //得到总行数
        int rowNum = sheet.getPhysicalNumberOfRows();
        List<AssetVO> assetVOS = new ArrayList<>();
        for (int nrow = 1; nrow < rowNum; nrow++) {
            Row rowDate = sheet.getRow(nrow);
            AssetVO asset = new AssetVO();
            int cellno = 0;
            if (rowDate.getLastCellNum() < 10)
                continue;

            asset.setAssetName(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .filter(value -> !value.isEmpty())
                            .orElse(null)
            );

            // 如果 assetName 为空，则跳过当前行
            if (asset.getAssetName() == null) {
                continue;
            }

            asset.setAssetTypeId(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(cell -> (int) cell.getNumericCellValue())
                            .map(String::valueOf)
                            .orElseThrow(() -> new BusinessException(asset.getAssetName() +":"+ messageSourceUtil.getMessage("asset.import.assettypeid.invalid")))
            );
            asset.setCapacity(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .orElse("")
            );

            asset.setStyle(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .orElse("")
            );

            asset.setBrand(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .orElse("")
            );

            asset.setStateId(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(cell -> (int) cell.getNumericCellValue())
                            .map(String::valueOf)
                            .orElseThrow(() -> new BusinessException(asset.getAssetName() +":"+messageSourceUtil.getMessage("asset.import.assetstateid.invalid")))
            );
            asset.setFaultDesc(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .orElse("")
            );

            asset.setProcessMode(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .orElse("")
            );

            asset.setLifeTime(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .orElse("")
            );

            asset.setUsedDate(
                    Optional.ofNullable(rowDate.getCell(cellno++))
                            .map(Cell::getStringCellValue)
                            .filter(dateStr -> Pattern.matches(dateStr, DatePattern.NORM_DATE_PATTERN))
                            .map(dateStr -> DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN))
                            .orElseThrow(()->new BusinessException(asset.getAssetName() + ":" + messageSourceUtil.getMessage("asset.import.assetstateid.invalid")))
            );

            assetVOS.add(asset);
        }

        assetService.batchAddAsset(assetVOS);
        IoUtil.close(workbook);
        IoUtil.close(inputStream);

        return ResponseHelper.successful();
    }

    @ApiOperation(value = "字典通用")
    @GetMapping(value = "/config/dictionary")
    public ResponseEntity<ResponseResult> assertStatusItem(@RequestParam Integer entryId) throws IOException {
        if (entryId == null){
            return ResponseHelper.failed("entryid is null");
        }
        List<DataItem> byEntryId = dataItemService.findByEntryId(entryId);
        return ResponseHelper.successful(byEntryId);
    }
}
