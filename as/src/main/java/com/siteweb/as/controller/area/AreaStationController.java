package com.siteweb.as.controller.area;

import com.siteweb.as.dto.area.AreaStationChangeDTO;
import com.siteweb.as.service.AreaStationService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/areastation")
public class AreaStationController {
    @Autowired
    AreaStationService areaStationService;
    @GetMapping(params = "areaId")
    public ResponseEntity<ResponseResult> getStationByAreaId(Integer areaId) {
        return ResponseHelper.successful(areaStationService.findStationByAreaId(areaId));
    }

    @GetMapping(params = {"groupId", "structureId"})
    public ResponseEntity<ResponseResult> getStationByGroupIdAndStructureId(Integer groupId, Integer structureId) {
        return ResponseHelper.successful(areaStationService.getStationByGroupIdAndStructureId(groupId, structureId));
    }

    @GetMapping(value = "/structuredata", params = "structure")
    public ResponseEntity<ResponseResult> getStructureData(Integer structure) {
        return ResponseHelper.successful(areaStationService.getStructureData(structure));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateAreaMap(@RequestBody AreaStationChangeDTO areaStationChangeDTO) {
        return ResponseHelper.successful(areaStationService.updateAreaMap(areaStationChangeDTO));
    }
}
