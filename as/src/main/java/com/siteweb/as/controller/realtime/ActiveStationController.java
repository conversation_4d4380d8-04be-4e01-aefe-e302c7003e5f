package com.siteweb.as.controller.realtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.realtime.ActiveStructureResult;
import com.siteweb.as.dto.realtime.ExportStructureRequestDTO;
import com.siteweb.as.dto.realtime.PagingInfo;
import com.siteweb.as.dto.realtime.ProjectStateResult;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ProjectOperationResult;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.service.ProjectStateService;
import com.siteweb.monitoring.vo.StationProjectVO;
import com.siteweb.as.dto.StationDetailASDTO;
import com.siteweb.as.service.StationDetailASService;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.as.vo.StationProjectASVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "activestationController", tags = {"activestation操作接口"})
@Slf4j
public class ActiveStationController {
    @Autowired
    StationDetailASService stationDetailService;
    @Autowired
    ProjectStateService projectStateService;
    @Autowired
    StationManager stationManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    private static final String USER_ID_IS_NULL = "userid is null";

    @ApiOperation(value = "获取局站状态数量")
    @GetMapping(value = "/realtime/activestation")
    public ResponseEntity<ResponseResult> getActivestationNum() {
        List<Integer> onlineNums = stationDetailService.queryStationStatusNum();
        return ResponseHelper.successful(onlineNums);
    }
    @ApiOperation(value = "设置局站工程状态")
    @PostMapping(value = "/realtime/activestation")
    public ResponseEntity<ResponseResult> setStationProject(@RequestBody StationProjectASVO configObject) {
        Integer userId = TokenUserUtil.getLoginUserId();

        for(Integer id : configObject.getConfigObjectIds()){
            configObject.getMaskTimeGroup().setStationId(id);
            projectStateService.setStationProject(configObject.getMaskTimeGroup(), userId);
        }
        stationManager.refreshStationByIds(configObject.getConfigObjectIds());
        return ResponseHelper.successful(configObject.getMaskTimeGroup());
    }
    @ApiOperation(value = "删除局站工程状态")
    @DeleteMapping(value = "/realtime/activestation")
    public ResponseEntity<ResponseResult> delStationProject(@RequestBody List<Integer> configObjectIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<ProjectOperationResult> result = new ArrayList<>();


        for(Integer id : configObjectIds){
            ProjectOperationResult temp = projectStateService.deleteStationProject(id, userId);
            if (Boolean.FALSE.equals(temp.getResult())){
                temp.setCfgId(String.valueOf(id));
                result.add(temp);
            }
        }

        stationManager.refreshStationByIds(configObjectIds);
        if (CollUtil.isEmpty(result)){
            return ResponseHelper.successful("0");
        }
        return ResponseHelper.successful(result);
    }
    @ApiOperation(value = "导出局站状态列表")
    @PostMapping(value = "/realtime/activestation/excel")
    public ResponseEntity<Resource> ActiveStationExcel(@RequestBody ExportStructureRequestDTO exportStructureRequestDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();

        String[] columnArray = exportStructureRequestDTO.getColumns().split(",");
        String[] titleArray = exportStructureRequestDTO.getTitles().split(",");
        if(columnArray.length != titleArray.length || columnArray.length == 0){
            return ResponseEntity.badRequest().body(null);
        }

        QueryInformation queryInfo = exportStructureRequestDTO.getQueryInformation();

        return ExcelExportUtil.exportExcel(() -> stationDetailService.exportActiveStation(queryInfo, columnArray, titleArray, userId));

    }
    /**
     * 获取局站状态列表
     * @return
     */
    @Deprecated
    @ApiOperation(value = "获取局站状态列表")
    @PostMapping(value = "/realtime/activestation", params = {"isFirst"})
    public ResponseEntity<ResponseResult> getActivestationByFilter(@RequestBody QueryInformation queryInfo) {

        Integer userId = TokenUserUtil.getLoginUserId();
        int page = queryInfo.getPagingInformation().getCurrentPageIndex()-1;
        int size = queryInfo.getPagingInformation().getPageSize();
        PageRequest pageable = PageRequest.of(page, size, Sort.unsorted());
        //计算耗时
        long time1 = System.currentTimeMillis();
        Page<StationDetailASDTO> stationDetailDTOS = stationDetailService.queryPageableStationDetails(userId, pageable,queryInfo,false);

        ActiveStructureResult result = new ActiveStructureResult();
        PagingInfo pagingInfo = new PagingInfo();
        result.setActiveStructure(stationDetailDTOS.getContent());
        result.setQueryTimeTicks(System.currentTimeMillis() - time1);

        pagingInfo.setPageSize(size);
        pagingInfo.setCurrentPageIndex(page);
        pagingInfo.setTotalPagesCount(stationDetailDTOS.getTotalPages());
        pagingInfo.setPaging(true);
        pagingInfo.setCount(stationDetailDTOS.getTotalElements());
        result.setPagingInfo(pagingInfo);
        return ResponseHelper.successful(result);
    }
    @ApiOperation(value = "获取局站工程状态")
    @GetMapping(value = "/realtime/activestation", params = {"stationId"})
    public ResponseEntity<ResponseResult> getActiveStationBystationId(Integer stationId) {

        StationProjectVO station =  projectStateService.getStationProject(stationId);
//        MaskTimeGroupVO masktime = new MaskTimeGroupVO();
//        if (station != null) {
//            masktime.setSequenceId(0);
//            masktime.setDataId(station.getStationId());
//            masktime.setStartTime(station.getStartTime());
//            masktime.setEndTime(station.getEndTime());
//            masktime.setReason(station.getReason());
//        }
//        return ResponseHelper.successful(masktime);
        ProjectStateResult result = new ProjectStateResult();
        if (station != null) {
            result.setDataId(station.getStationId());
            result.setReason(station.getReason());
            result.setStartTime(station.getStartTime());
            result.setEndTime(station.getEndTime());
        }
        return ResponseHelper.successful(result);
    }
}
