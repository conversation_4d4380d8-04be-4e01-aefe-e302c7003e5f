package com.siteweb.as.controller.realtime;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ConfigSignalDTO;
import com.siteweb.monitoring.dto.SignalMeaningDTO;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.as.enums.BusinessObjectType;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "ConfigSignalController", tags = {"ActiveEvent操作接口"})
public class ConfigSignalController {
    @Autowired
    SignalService signalService;

    @Data
    private static class DataItem2 {
        private Integer objectId;         //字典项ID
        private String name;       //字典项值

        public DataItem2(Integer objectId, String name) {
            this.objectId = objectId;
            this.name = name;
        }
    }

    @Data
    public static class SignalMeaning {
        private Integer stateValue;
        private String measings;
    }
    @Data
    public static class SignalConfigList {
        private SignalConfig acConfigSignal;
    }
    @Data
    public static class SignalConfig {
        private String uniqueId;
        private String stationId;
        private String equipmentId;
        private Integer objectId;
        private String name;
        private Integer status;
        private Integer objectType;
        private Integer configSourceType;
        private Integer displayIndex;
        private Integer equipmentcategory;
        private Integer samplerId;
        private String signalTitle;
        private Integer dataType;
        private Boolean updateTemplate;
        private Integer monitoringUnitId;
        private Integer baseTypeId;
        private Integer standardTypeId;
        private Integer signalType;
        private String signalTypeName;
        private Integer signalCategory;
        private String signalCategoryName;
        private Integer channelNo;
        private Integer channelType;
        private List<DataItem2> channelDic;
        private String expression;
        private Integer valueType;
        private List<DataItem2> valueTypeDic;
        private String showPrecision;
        private Integer unit = 0;
        private String signalUnit;
        private Double maxValue;
        private Double minValue;
        private Double storeInterval;
        private Double absValueThreshold;
        private Double percentThreshold;
        private Integer staticsPeriod;
        private List<SignalMeaning> signalMeaning;
        private List<DataItem2> signalProperty;
        private String signalPropertyString;
        private Boolean visible;
        private Boolean enable;
    }

    @Autowired
    private DataItemService dataItemService;
    @Autowired
    private RealTimeSignalManager realTimeSignalManager;

    @ApiOperation(value = "属性页-信号-配置")
    @GetMapping(value = "/realtime/configsignal", params = {"stationId", "equipmentId", "signalId"})
    public ResponseEntity<ResponseResult> GetConfigSignals(int stationId, int equipmentId, int signalId) {
        ConfigSignalDTO dto = signalService.findConfigSignalDTOBySignalId(equipmentId, signalId);
        HashMap resultMap = new HashMap();
        SignalConfig signalConfig = new SignalConfig();
        signalConfig.setStationId(dto.getStationId().toString());
        signalConfig.setEquipmentId(dto.getEquipmentId().toString());
        signalConfig.setName(dto.getSignalName());
        signalConfig.setDisplayIndex(dto.getDisplayIndex());
//        signalConfig.setUniqueId(dto.getStationId() + "." + dto.getEquipmentId() + "." + dto.getSignalId());
        signalConfig.setObjectId(dto.getSignalId());
        signalConfig.setChannelNo(dto.getChannelNo());
        signalConfig.setChannelType(dto.getChannelType());
        List<DataItem2> channelDic = new ArrayList<>();
        {
            List<DataItem> itemlist = dataItemService.findByEntryId(22);
            for (DataItem item : itemlist) {
                channelDic.add(new DataItem2(item.getItemId(), item.getItemValue()));
            }
        }
        signalConfig.setChannelDic(channelDic);
        signalConfig.setSignalType(dto.getSignalType());
        signalConfig.setSignalTypeName(dataItemService.findByEntryIdIdAndItemId(18, dto.getSignalType()).getItemValue());
        signalConfig.setSignalCategory(dto.getSignalCategory());
        signalConfig.setSignalCategoryName(dataItemService.findByEntryIdIdAndItemId(17, dto.getSignalType()).getItemValue());
        signalConfig.setExpression(dto.getExpression());
        signalConfig.setValueType(dto.getDataType());
        List<DataItem2> valueTypeDic = new ArrayList<>();
        {
            List<DataItem> itemlist = dataItemService.findByEntryId(70);
            for (DataItem item : itemlist) {
                valueTypeDic.add(new DataItem2(item.getItemId(), item.getItemValue()));
            }
        }
        signalConfig.setValueTypeDic(valueTypeDic);
        signalConfig.setShowPrecision(dto.getShowPrecision());
        signalConfig.setSignalUnit(dto.getUnit());

        signalConfig.setStoreInterval(dto.getStoreInterval());
        signalConfig.setAbsValueThreshold(dto.getAbsValueThreshold());
        signalConfig.setStaticsPeriod(dto.getStaticsPeriod());
        signalConfig.setPercentThreshold(dto.getPercentThreshold());
        List<SignalMeaning> MeaningsList = new ArrayList<>();
        {
            for (SignalMeaningDTO item : dto.getSignalMeanings()) {

                SignalMeaning meaning = new SignalMeaning();
                meaning.setStateValue(item.getStateValue());
                meaning.setMeasings(item.getMeanings());
                MeaningsList.add(meaning);
            }
        }
        signalConfig.setSignalMeaning(MeaningsList);
        signalConfig.setVisible(dto.getVisible());
        signalConfig.setEnable(dto.getEnable());

        List<DataItem2> PropertyList = new ArrayList<>();
        {
            for (Integer item : dto.getSignalPropertyIds()) {
                PropertyList.add(new DataItem2(item, ""));
            }
        }
        signalConfig.setSignalProperty(PropertyList);
        String aa = dto.getSignalPropertyIds().stream().map(String::valueOf).collect(Collectors.joining("/"));
        signalConfig.setSignalPropertyString(aa);
        signalConfig.setMonitoringUnitId(dto.getMonitorUnitId());

        List<SignalConfig> list = new ArrayList<>();
        list.add(signalConfig);
        resultMap.put("signals", list);
        return ResponseHelper.successful(resultMap);
    }
    @ApiOperation(value = "属性页-信号-配置保存")
    @PostMapping(value = "/realtime/ConfigSignal")
    public ResponseEntity<ResponseResult> UpdateConfigSignal(@RequestBody SignalConfigList signalConfigList) {
        SignalConfig signalConfig = signalConfigList.getAcConfigSignal();
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "userId is null",
                    HttpStatus.BAD_REQUEST);
        }
        ConfigSignalDTO configSignalDTO = new ConfigSignalDTO();
        configSignalDTO.setStationId(Integer.parseInt(signalConfig.getStationId()));
        configSignalDTO.setEquipmentId(Integer.parseInt(signalConfig.getEquipmentId()));
        configSignalDTO.setSignalId(signalConfig.getObjectId());
        configSignalDTO.setExpression(signalConfig.getExpression());
        configSignalDTO.setSignalName(signalConfig.getName());
        configSignalDTO.setShowPrecision(signalConfig.getShowPrecision());
        configSignalDTO.setUnit(signalConfig.getSignalUnit());

        configSignalDTO.setStoreInterval(signalConfig.getStoreInterval());
        configSignalDTO.setAbsValueThreshold(signalConfig.getAbsValueThreshold());
        configSignalDTO.setStaticsPeriod(signalConfig.getStaticsPeriod());
        configSignalDTO.setPercentThreshold(signalConfig.getPercentThreshold());

        List<Integer> ids = Arrays.stream((signalConfig.getSignalPropertyString().split("/"))).map(Integer::valueOf).collect(Collectors.toList());
        configSignalDTO.setSignalPropertyIds(ids);

        List<SignalMeaningDTO> MeaningsList = new ArrayList<>();
        {
            for (SignalMeaning item : signalConfig.getSignalMeaning()) {

                SignalMeaningDTO meaning = new SignalMeaningDTO();
                meaning.setStateValue(item.getStateValue());
                meaning.setMeanings(item.getMeasings());
                MeaningsList.add(meaning);
            }
        }
        configSignalDTO.setSignalMeanings(MeaningsList);
        configSignalDTO.setMonitorUnitId(signalConfig.getMonitoringUnitId());

        int result = signalService.updateConfigSignalDTO(userId, configSignalDTO);
        return ResponseHelper.successful(result);
    }
    @ApiOperation(value = "属性页-信号-配置-获取信号属性表")
    @GetMapping(value = "/realtime/configsignal", params = {"objectTypeName"})
    public ResponseEntity<ResponseResult> getDictionaryData(String objectTypeName) {
        List<DataItem> itemlist = dataItemService.findByEntryId(BusinessObjectType.valueOf(objectTypeName).value());
        List<DataItem2> itemlist2 = new ArrayList<>();
        for (DataItem item : itemlist) {
            itemlist2.add(new DataItem2(item.getItemId(), item.getItemValue()));
        }

        HashMap resultMap = new HashMap();
        resultMap.put("objects", itemlist2);
        return ResponseHelper.successful(resultMap);
    }
}
