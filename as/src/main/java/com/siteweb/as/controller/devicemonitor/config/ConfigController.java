package com.siteweb.as.controller.devicemonitor.config;

import com.siteweb.as.service.ConfigService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ConfigController {
    private final ConfigService configService;

    @ApiOperation("获取局站相关单元")
    @GetMapping(value = "/config/configstation")
    public ResponseEntity<ResponseResult> getStationInfo(Integer stationId) {
        return ResponseHelper.successful(configService.getStationInfo(stationId));
    }

    @ApiOperation("获取局站相关信息")
    @GetMapping(value = "/config/configobject")
    public ResponseEntity<ResponseResult> getConfigObject(String objectType, String uniqueId) {
        return ResponseHelper.successful(configService.getConfigObject(objectType,uniqueId));
    }

    @ApiOperation("获取局站相关局房信息")
    @GetMapping(value = "/config/confighouse")
    public ResponseEntity<ResponseResult> getHouseInfo(Integer stationId) {
        return ResponseHelper.successful(configService.getHouseInfo(stationId));
    }

    @ApiOperation("获取字典相关信息")
    @GetMapping(value = "/config/commonobject")
    public ResponseEntity<ResponseResult> getCommonObject(String type) {
        return ResponseHelper.successful(configService.getCommonObject(type));
    }
}
