package com.siteweb.as.controller.realtime;

import com.siteweb.as.dto.ActiveSignalAS;
import com.siteweb.as.manager.ActiveSignalASManager;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateAddDays;

/**
 * 活动告警表
 *
 */
@RestController
@Slf4j
@RequestMapping("/api")
@Api(value = "LastHistorySignalController", tags = {"ActiveSignal操作接口"})
public class LastHistorySignalController {
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    ActiveSignalASManager activeSignalManagerAS;
    @Autowired
    public ConfigSignalManager configSignalManager;

    @Value("${spring.influx.database}")
    private String database;
    @ApiOperation(value = "获取最新历史数据")
    @GetMapping(value = "realtime/LastHistorySignal", params = {"stationId","equipmentId"})
    public ResponseEntity<ResponseResult> getLastHistorySignalByEquipId(Integer stationId, Integer equipmentId) {

        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            return null;
        }

        Date starttime = dateAddDays(new Date(), -1);
        List<HistorySignal> historySignals = getHistoryData(equipmentId.toString(),starttime);
        Map<String, HistorySignal> historySignalMap = historySignals.stream().collect(Collectors.toMap(HistorySignal::getSignalId, p -> p));

        //获取配置
        List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalByEquipmentId(equipmentId);

        //拼接实时信号
        List<ActiveSignalAS> signals = new ArrayList<>();
        for(ConfigSignalItem configSignalItem : configSignalItems){
            String id = equipmentId+"."+configSignalItem.getSignalId();
            if(historySignalMap.containsKey(id)) {
                HistorySignal his = historySignalMap.get(id);
                ActiveSignalAS activeSignal = activeSignalManagerAS.constructSimpleActiveSignalByConfigAndRealTimeItem3(configSignalItem, his);
                activeSignal.setEquipmentId(equipment.getEquipmentId());
                activeSignal.setEquipmentName(equipment.getEquipmentName());
                activeSignal.setStationId(equipment.getStationId());
                signals.add(activeSignal);
            }
        }

        HashMap resultMap = new HashMap();
        resultMap.put("signals", signals);
        return ResponseHelper.successful(resultMap);
    }
    @Autowired
    private InfluxDB influxDB;
    public List<HistorySignal> getHistoryData(String equipmentId, Date startTime) {

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        Date endTime = new Date();
        List<HistorySignal> tempQueryResult = new ArrayList<>();
        try {
            String sql = "SELECT * FROM historydatas  WHERE time >= $startTime and time <= $endTime and DeviceId = $equipmentId group by SignalId order by time desc limit 1";
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                    .forDatabase(database)
                    .bind("startTime", DateUtil.dateToString(startTime))
                    .bind("endTime", DateUtil.dateToString(endTime))
                    .bind("equipmentId", equipmentId)
                    .create();
            query =  influxDB.query(queryBuilder);

            if (query != null) {
                tempQueryResult = resultMapper.toPOJO(query,HistorySignal.class);
            }
            if (tempQueryResult == null || tempQueryResult.size() == 0)
                return new ArrayList<>();
            return tempQueryResult;
        }catch (Exception e){
            log.error("airGroupViewServiceImpl-getHistoryData error {}", e.getMessage());
            return new ArrayList<>();
        }
    }
}
