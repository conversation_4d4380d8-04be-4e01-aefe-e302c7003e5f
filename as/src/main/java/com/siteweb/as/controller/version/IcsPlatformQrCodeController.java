package com.siteweb.as.controller.version;

import com.siteweb.as.dto.version.IcsPlatformQrcodeDTO;
import com.siteweb.as.entity.IcsPlatformQrCode;
import com.siteweb.as.service.IcsPlatformQrCodeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/platformqrcode")
public class IcsPlatformQrCodeController {
    @Autowired
    IcsPlatformQrCodeService icsPlatformQrCodeService;

    @GetMapping("/page")
    public ResponseEntity<ResponseResult> findPage(IcsPlatformQrcodeDTO icsPlatformQrcodeDTO) {
        return ResponseHelper.successful(icsPlatformQrCodeService.findPage(icsPlatformQrcodeDTO));
    }

    @PostMapping
    public ResponseEntity<ResponseResult> create(@RequestBody IcsPlatformQrCode icsPlatformQrCode) {
        return ResponseHelper.successful(icsPlatformQrCodeService.create(icsPlatformQrCode));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> update(@RequestBody IcsPlatformQrCode icsPlatformQrCode) {
        return ResponseHelper.successful(icsPlatformQrCodeService.updateById(icsPlatformQrCode));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable Integer id) {
        return ResponseHelper.successful(icsPlatformQrCodeService.deleteByIds(List.of(id)));
    }

    @DeleteMapping(params = "ids")
    public ResponseEntity<ResponseResult> deleteByIds(String ids) {
        List<Integer> idList = StringUtils.splitToIntegerList(ids);
        return ResponseHelper.successful(icsPlatformQrCodeService.deleteByIds(idList));
    }
}
