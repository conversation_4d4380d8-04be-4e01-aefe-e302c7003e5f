package com.siteweb.as.controller.realtime;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ConfigEventASDTO;
import com.siteweb.as.dto.DynamicConfigEventDTO;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.service.EventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动告警表
 */
@RestController
@RequestMapping("/api")
@Api(value = "ConfigEventASController", tags = {"ConfigEvent操作接口"})
public class ConfigEventASController {
    @Autowired
    EventService eventService;
    @Autowired
    EquipmentManager equipmentManager;

    @ApiOperation(value = "获取设备的事件")
    @GetMapping(value = "/realtime/ConfigEvent", params = {"stationId", "equipmentId"})
    public ResponseEntity<ResponseResult> getConfigEvent(int stationId, int equipmentId) {

        List<ConfigEventDTO> configEventDTOS = eventService.findByStationIdAndEquipmentId(stationId, equipmentId);

        ConfigEventASDTO configEventDTO = new ConfigEventASDTO();
        List<ConfigEventASDTO.configEvents> configEventList = new ArrayList<>();
        for (ConfigEventDTO event : configEventDTOS) {
            ConfigEventASDTO.configEvents eventAS = new ConfigEventASDTO.configEvents();
            eventAS.from(event);
            configEventList.add(eventAS);
        }
        configEventDTO.setConfigEvents(configEventList);

        return new ResponseEntity(configEventDTO, HttpStatus.OK);
    }

    @ApiOperation(value = "获取动态配置事件")
    @GetMapping(value = "/realtime/ConfigEvent", params = {"stationId", "equipmentId", "eventid"})
    public ResponseEntity<ResponseResult> getDynamicConfigEventDTO(int stationId, int equipmentId, int eventid) {

        ConfigEventDTO cfgEvent = eventService.findConfigEventDTOByEventId(equipmentId, eventid);

        DynamicConfigEventDTO eventdto = new DynamicConfigEventDTO();
        eventdto.from(cfgEvent);
        return ResponseHelper.successful(eventdto);
    }

    @ApiOperation(value = "保存动态配置事件")
    @PostMapping(value = "/realtime/ConfigEvent")
    public ResponseEntity<ResponseResult> getDynamicConfigEventDTO(@RequestBody DynamicConfigEventDTO eventVo) {

        try {
            for (DynamicConfigEventDTO.Condition con : eventVo.getConditions()) {
                con.setStartOperation(CharSequenceUtil.isBlank(con.getStartOperation()) ? null : URLDecoder.decode(con.getStartOperation(), "utf-8"));
                con.setEndOperation(CharSequenceUtil.isBlank(con.getEndOperation()) ? null : URLDecoder.decode(con.getEndOperation(), "utf-8"));
            }
            eventVo.setStartExpression(CharSequenceUtil.isBlank(eventVo.getStartExpression()) ? null : URLDecoder.decode(eventVo.getStartExpression(), "utf-8"));
            eventVo.setSuppressExpression(CharSequenceUtil.isBlank(eventVo.getSuppressExpression()) ? null : URLDecoder.decode(eventVo.getSuppressExpression(), "utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        Equipment equipment = equipmentManager.getEquipmentById(eventVo.getEquipmentId());
        if (ObjectUtil.isNull(equipment)) {
            return ResponseHelper.failed("设备不存在，请检查设备id");
        }
        eventVo.setEquipmentTemplateId(equipment.getEquipmentId());
        ConfigEventDTO cfgEvent = new ConfigEventDTO();
        eventVo.to(cfgEvent);

        eventService.updateConfigEventDTO(TokenUserUtil.getLoginUserId(), cfgEvent);
        return ResponseHelper.successful("OK");
    }
}
