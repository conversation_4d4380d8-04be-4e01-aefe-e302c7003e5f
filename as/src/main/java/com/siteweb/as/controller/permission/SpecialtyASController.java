package com.siteweb.as.controller.permission;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.admin.dto.SpecialtyDTO;
import com.siteweb.admin.entity.SpecialtyGroupMap;
import com.siteweb.admin.mapper.SpecialtyGroupMapMapper;
import com.siteweb.admin.service.SpecialtyGroupMapService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SpecialtyGroupMapController
 * @createTime 2022-01-18 17:00:03
 */
@RestController
@RequestMapping("/api")
@Api(value = "SpecialtyGroupMapController", tags = {"专业权限Map操作接口"})
public class SpecialtyASController {

    @Autowired
    SpecialtyGroupMapService specialtyGroupMapService;

    @Data
    private static class SpecialtyItem {
        private Integer itemId;
    }
    @Data
    @NoArgsConstructor
    private static class SpecialtyEntry {
        private Integer groupId;
        @JsonProperty("AddSpecialty")
        private List<SpecialtyItem> addSpecialty;
        @JsonProperty("DeleteSpecialty")
        private List<SpecialtyItem> deleteSpecialty;
    }
    @Autowired
    SpecialtyGroupMapMapper specialtyGroupMapMapper;
    @ApiOperation(value = "设置专业权限")
    @PutMapping(value = "/permission/specialty", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveSpecialtyGroupMaps(
            @Valid @RequestBody SpecialtyEntry Entry) {
        if (null == Entry || Entry.groupId == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "specialtyGroupMapVOS is empty", HttpStatus.BAD_REQUEST);
        }
        Integer result = 0;
        if(Entry.addSpecialty.size() > 0) {
            List<SpecialtyGroupMap> specialtyGroupMaps = new ArrayList<>();
            for (SpecialtyItem item : Entry.addSpecialty) {
                SpecialtyGroupMap specialtyGroupMap = new SpecialtyGroupMap();
                specialtyGroupMap.setSpecialtyGroupId(Entry.getGroupId());
                specialtyGroupMap.setEntryItemId(item.getItemId());
                specialtyGroupMap.setOperation("8");
                specialtyGroupMaps.add(specialtyGroupMap);
            }
            result = specialtyGroupMapService.saveSpecialtyGroupMaps(specialtyGroupMaps);
            if (result == 0) {
                return ResponseHelper.failed(String.valueOf(ErrorCode.PRIMARY_KEY_ASSIGN_ERROR.value()),
                        "createSpecialtyGroupMap error", HttpStatus.BAD_REQUEST);
            }
        }
        if(Entry.deleteSpecialty.size() > 0) {
            List<Integer> specialtyGroupMapIds = Entry.deleteSpecialty.stream().map(SpecialtyItem::getItemId).toList();
            result = specialtyGroupMapService.deleteByIds(specialtyGroupMapIds);
            if (result == 0) {
                return ResponseHelper.failed(String.valueOf(ErrorCode.PRIMARY_KEY_ASSIGN_ERROR.value()),
                        "deleteByGroupIdItemId error", HttpStatus.BAD_REQUEST);
            }
        }
        return ResponseHelper.successful("true");
    }

    @Operation(summary = "查询所有的专业项")
    @GetMapping("/permission/specialty")
    public ResponseEntity<ResponseResult> getSpecialty() {
        List<SpecialtyDTO> specialtyDTOS = specialtyGroupMapService.getSpecialty();
        return ResponseHelper.successful(specialtyDTOS);
    }
}
