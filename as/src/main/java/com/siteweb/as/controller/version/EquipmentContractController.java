package com.siteweb.as.controller.version;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.version.EquipmentProjectInfoDTO;
import com.siteweb.as.service.EquipmentProjectInfoService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/version/equipmentcontract")
public class EquipmentContractController {
    @Autowired
    EquipmentProjectInfoService equipmentProjectInfoService;

    @GetMapping("/page")
    public ResponseEntity<ResponseResult> getStationContractInfo(EquipmentProjectInfoDTO equipmentProjectInfoDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(equipmentProjectInfoService.findContractInfoPage(userId,equipmentProjectInfoDTO));
    }

    @GetMapping("/export")
    public ResponseEntity<Resource> exportExcel(EquipmentProjectInfoDTO equipmentProjectInfoDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(equipmentProjectInfoService::exportContractInfo, equipmentProjectInfoDTO, userId);
    }

    @GetMapping("/{equipmentId}")
    public ResponseEntity<ResponseResult> getStationContractInfo(@PathVariable Integer equipmentId) {
        return ResponseHelper.successful(equipmentProjectInfoService.findContractInfoByEquipmentId(equipmentId));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateStationContractInfo(@RequestBody EquipmentProjectInfoDTO equipmentProjectInfoDTO) {
        return ResponseHelper.successful(equipmentProjectInfoService.updateContractInfo(equipmentProjectInfoDTO));
    }

    @PutMapping("/batch")
    public ResponseEntity<ResponseResult> batchUpdateContractInfo(@RequestBody List<EquipmentProjectInfoDTO> equipmentProjectInfoDTOList) {
        return ResponseHelper.successful(equipmentProjectInfoService.batchUpdateContractInfo(equipmentProjectInfoDTOList));
    }
}
