package com.siteweb.as.controller.patrol_standard;

import com.siteweb.as.dto.patrol.GroupParameterDTO;
import com.siteweb.as.dto.patrol.PatrolStandardGroupDTO;
import com.siteweb.as.service.PatrolGroupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/patrol_standard")
@Api(value = "PatrolStandardGroupController", tags = {"分组管理操作接口"})
@RequiredArgsConstructor
public class PatrolStandardGroupController {

    private final PatrolGroupService patrolGroupService;

    @ApiOperation("获取全部分组")
    @GetMapping(value = "/group")
    public ResponseEntity<ResponseResult> getAllPatrolGroup() {
        return ResponseHelper.successful(patrolGroupService.getAllPatrolStandardGroup());
    }

    @ApiOperation("获取分组详情")
    @GetMapping(value = "/group", params = {"groupId"})
    public ResponseEntity<ResponseResult> getPatrolGroup(Integer groupId) {
        return ResponseHelper.successful(patrolGroupService.getPatrolStandardGroup(groupId));
    }

    @ApiOperation("新增分组")
    @PostMapping(value = "/group")
    public ResponseEntity<ResponseResult> savePatrolGroup(@RequestBody PatrolStandardGroupDTO patrolGroupDTO) {
        patrolGroupService.savePatrolStandardGroup(patrolGroupDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation("修改分组")
    @PutMapping(value = "/group")
    public ResponseEntity<ResponseResult> updatePatrolGroup(@RequestBody PatrolStandardGroupDTO patrolGroupDTO) {
        patrolGroupService.updatePatrolStandardGroup(patrolGroupDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除规则配置")
    @DeleteMapping(value = "/group/{groupId}")
    public ResponseEntity<ResponseResult> deletePatrolGroup(@PathVariable Integer groupId) {
        patrolGroupService.deletePatrolStandardGroup(groupId);
        return ResponseHelper.successful();
    }

    @ApiOperation("获取局站类型")
    @GetMapping(value = "/stationtype")
    public ResponseEntity<ResponseResult> listStationType() {
        return ResponseHelper.successful(patrolGroupService.listStationtype());
    }

    @ApiOperation("获取局站")
    @GetMapping(value = "/station")
    public ResponseEntity<ResponseResult> listStation(String stationTypeId) {
        return ResponseHelper.successful(patrolGroupService.listStation(stationTypeId));
    }

    @ApiOperation("获取分组参数")
    @GetMapping(value = "/GroupParameter")
    public ResponseEntity<ResponseResult> getGroupParameter(@RequestParam Integer groupId) {
        return ResponseHelper.successful(patrolGroupService.getStandardGroupParameter(groupId));
    }

    @ApiOperation("新增分组参数")
    @PostMapping(value = "/GroupParameter")
    public ResponseEntity<ResponseResult> saveGroupParameter(@RequestBody GroupParameterDTO groupParameterDTO) {
        patrolGroupService.saveGroupParameter(groupParameterDTO);
        return ResponseHelper.successful();
    }

}
