package com.siteweb.as.controller.version;

import com.siteweb.as.dto.version.InstallContractDTO;
import com.siteweb.as.entity.IcsContractInstall;
import com.siteweb.as.service.IcsContractInstallService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/version/installationcontract")
public class InstallationContractController {
    @Autowired
    IcsContractInstallService icsContractInstallService;
    @GetMapping("/page")
    public ResponseEntity<ResponseResult> getContractList(InstallContractDTO installContractDTO) {
        return ResponseHelper.successful(icsContractInstallService.findContractInfoPage(installContractDTO));
    }

    @GetMapping("/export")
    public ResponseEntity<Resource> exportContractList(InstallContractDTO installContractDTO) {
        return ExcelExportUtil.exportExcel(icsContractInstallService::exportContractInfo, installContractDTO);
    }

    @GetMapping("/info")
    public ResponseEntity<ResponseResult> getContractInfo(String contractNo,String projectName) {
        return ResponseHelper.successful(icsContractInstallService.findContractInfo(contractNo, projectName));
    }

    @PutMapping
    public ResponseEntity<ResponseResult> updateContractInfo(@RequestBody IcsContractInstall icsContractInstall) {
        return ResponseHelper.successful(icsContractInstallService.createOrUpdateContractInfo(icsContractInstall));
    }
    @PutMapping("/batch")
    public ResponseEntity<ResponseResult> batchUpdateContractInfo(@RequestBody List<IcsContractInstall> icsContractInstallList) {
        return ResponseHelper.successful(icsContractInstallService.batchUpdateContractInfo(icsContractInstallList));
    }
}
