package com.siteweb.as.controller.area;

import com.siteweb.admin.entity.Area;
import com.siteweb.admin.service.AreaService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/area")
public class AreaController {
    @Autowired
    AreaService areaService;

    @GetMapping
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(areaService.findAll());
    }

    @GetMapping("/{areaId}")
    public ResponseEntity<ResponseResult> findAll(@PathVariable Integer areaId){
        return ResponseHelper.successful(areaService.findById(areaId));
    }
    @GetMapping("/generateid")
    public ResponseEntity<ResponseResult> generateAreaId(){
        return ResponseHelper.successful(areaService.generateAreaId());
    }

    @PostMapping
    public ResponseEntity<ResponseResult> createArea(@RequestBody Area area){
        return ResponseHelper.successful(areaService.createArea(area));
    }

    @DeleteMapping("/{areaId}")
    public ResponseEntity<ResponseResult> deleteAreaById(@PathVariable Integer areaId){
        return ResponseHelper.successful(areaService.deleteAreaById(areaId));
    }


    @PutMapping
    public ResponseEntity<ResponseResult> updateArea(@RequestBody Area area){
        return ResponseHelper.successful(areaService.updateArea(area));
    }
}
