package com.siteweb.as.controller.realtime;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.realtime.PagingInfo;
import com.siteweb.as.dto.realtime.PowerOffDTO;
import com.siteweb.as.dto.realtime.PowerOffResult;
import com.siteweb.as.manager.QueryInformationIdManager;
import com.siteweb.as.service.StationDetailASService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.PowerOffStationDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Slf4j
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
public class ActivePowerController {
    @Autowired
    StationDetailASService stationDetailService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    QueryInformationIdManager queryInfoIdManager;

    @ApiOperation(value = "获取停电列表")
    @PostMapping(value = "/realtime/activepower")
    public ResponseEntity<ResponseResult> ActivePowerGet(@RequestBody QueryInformation queryInfo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        queryInfoIdManager.addOne(queryInfo);

        int page = 0, size = 1000;
        if (queryInfo.getPagingInformation() != null) {
            page = queryInfo.getPagingInformation().getCurrentPageIndex()-1;
            size = queryInfo.getPagingInformation().getPageSize();
        }
        PageRequest pageable = PageRequest.of(page, size, Sort.unsorted());
        Page<PowerOffStationDetail> res = stationDetailService.queryPowerOffStationDetails(
                userId, pageable, queryInfo);

        PowerOffResult result = new PowerOffResult();

        // 构造停电事件列表
        List<PowerOffDTO> datalist = new ArrayList<>();
        for(PowerOffStationDetail data:res.getContent()) {
            PowerOffDTO dataInfo = new PowerOffDTO(data);
            // 将处理后的停电事件添加到列表
            datalist.add(dataInfo);
        }

        result.setActivePower(datalist);

        PagingInfo pagingInfo = new PagingInfo();
        pagingInfo.setPageSize(size);
        pagingInfo.setCurrentPageIndex(page + 1);
        pagingInfo.setTotalPagesCount(res.getTotalPages());
        pagingInfo.setPaging(true);
        pagingInfo.setCount(res.getTotalElements());

        result.setPagingInfo(pagingInfo);

        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "导出停电列表")
    @GetMapping(value = "/realtime/activepower/excel")
    public ResponseEntity<Resource> excelActivePower(String queryInformationId, String columns, String titles) {
        Integer userId = TokenUserUtil.getLoginUserId();
        String[] columnArray = columns.split(",");
        String[] titleArray = titles.split(",");
        if(columnArray.length != titleArray.length || columnArray.length == 0){
            return ResponseEntity.badRequest().body(null);
        }
        QueryInformation queryInfo = queryInfoIdManager.getById(queryInformationId);

        PageRequest pageable = PageRequest.of(0, 100000, Sort.unsorted());
        Page<PowerOffStationDetail> activeEventDTOS = stationDetailService.queryPowerOffStationDetails(
                userId, pageable, queryInfo);

        List<PowerOffDTO> datalist = new ArrayList<>();
        for(PowerOffStationDetail data : activeEventDTOS.getContent()) {
            PowerOffDTO dataInfo = new PowerOffDTO(data);
            // 将处理后的停电事件添加到列表
            datalist.add(dataInfo);
        }

        ExcelWriter writer = ExcelUtil.getWriter(true);
        for(int i = 0; i < columnArray.length;i++)
        {
            writer.addHeaderAlias(StringUtils.uncapitalize(columnArray[i]), titleArray[i]);
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(datalist,true);

        return ExcelExportUtil.exportExcel(() -> writer);
    }
}
