package com.siteweb.as.controller.version;

import com.siteweb.as.service.EquipmentTypeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/equipmenttype")
public class EquipmentTypeController {
    @Autowired
    private EquipmentTypeService equipmentTypeService;

    @GetMapping
    public ResponseEntity<ResponseResult> findTypeList() {
        return ResponseHelper.successful(equipmentTypeService.findTypeList());
    }
}
