package com.siteweb.as.controller.devicemonitor.shield;

import com.siteweb.as.manager.EventMaskManager;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/shieldcommon")
@RequiredArgsConstructor
public class ShieldCommonController {
    private final EventMaskManager eventMaskManager;


    @ApiOperation("获取局站相关信息")
    @GetMapping(value = "/shieldcommon")
    public ResponseEntity<ResponseResult> getStationInfo(String uniqueId, String objectType) {
        return ResponseHelper.successful(eventMaskManager.getConfigMaskTimeGroup(uniqueId, objectType));
    }


}
