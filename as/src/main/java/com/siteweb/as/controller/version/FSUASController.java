package com.siteweb.as.controller.version;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.service.FSUService;
import com.siteweb.as.service.IcsFsuSoInfoService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import com.siteweb.monitoring.service.FsuService;
import com.siteweb.monitoring.vo.FsuFilterVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/api/version/fsu")
public class FSUASController {

    @Autowired
    FsuService service;
    @Autowired
    FSUService fsuService;
    @Autowired
    IcsFsuSoInfoService icsFsuSoInfoService;

    @GetMapping("/page")
    public ResponseEntity<ResponseResult> getFsuPage(Page<IcsFsuDataNewInfoDTO> page, FsuFilterVo filterVo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(fsuService.findFsuPage(userId, page, filterVo));
    }

    @GetMapping("/soinfo")
    public ResponseEntity<ResponseResult> getFsuSoInfo(String sn, String mac) {
        return ResponseHelper.successful(icsFsuSoInfoService.findFsuSoInfo(sn,mac));
    }

    @GetMapping("/export")
    public ResponseEntity<Resource> getFsuPageExcel(FsuFilterVo filterVo) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(fsuService::exportFsu, filterVo, userId);
    }

    @ApiOperation(value = "获取采集器型号字典")
    @GetMapping(value = "/fsutypelist")
    public ResponseEntity<ResponseResult> getFsuTypeList() {
        return ResponseHelper.successful(service.findFsuTypeList());
    }

    @ApiOperation(value = "获取CPU使用率字典")
    @GetMapping(value = "/cpuusedlist")
    public ResponseEntity<ResponseResult> getCpuUsedList() {
        return ResponseHelper.successful(service.findCpuUsedList());
    }

    @ApiOperation(value = "获取内存使用率字典")
    @GetMapping(value = "/memoryusedlist")
    public ResponseEntity<ResponseResult> getMemoryUsedList() {
        return ResponseHelper.successful(service.findMemoryUsedList());
    }

    @ApiOperation(value = "获取Flash使用率字典")
    @GetMapping(value = "/flashusedlist")
    public ResponseEntity<ResponseResult> getFlashUsedList() {
        return ResponseHelper.successful(service.findFlashUsedList());
    }

    @GetMapping("/fsutypestatistics")
    public ResponseEntity<ResponseResult> getFsuTypeStatistics() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(fsuService.findFsuTypeStatistics(userId));
    }

    @GetMapping("/siteunitstatistics")
    public ResponseEntity<ResponseResult> getSiteUnitStatistics() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(fsuService.findSiteUnitStatistics(userId));
    }

    @GetMapping("/fsuflashusedstatistics")
    public ResponseEntity<ResponseResult> getFsuFlashUsedStatistics() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(fsuService.findFsuFlashUsedStatistics(userId));
    }

    @ApiOperation("根据FsuIp获取备份的列表")
    @GetMapping("/ftpdownloadfile")
    public ResponseEntity<ResponseResult> getFtpDownLoadFile(String ip){
        return ResponseHelper.successful(fsuService.findFtpDownLoadFile(ip));
    }

    @GetMapping("/filedirectoryexists")
    public ResponseEntity<ResponseResult> fileDirectoryExists(String fileDirectory){
        return ResponseHelper.successful(fsuService.fileDirectoryExists(fileDirectory));
    }

    @GetMapping("/downloadzip")
    public void downloadZip(String fileDirectory, HttpServletResponse response) throws IOException {
        // 检查文件夹路径是否存在
        File folder = new File(fileDirectory);
        if (!folder.exists() || !folder.isDirectory()) {
            throw new BusinessException("文件夹路径无效或不存在！");
        }
        // 创建临时ZIP文件
        File zipFile = new File(folder.getParent(), URLEncodeUtil.encode(folder.getName()) + ".zip");
        ZipUtil.zip(folder.getAbsolutePath(), zipFile.getAbsolutePath());
        // 设置响应头，返回ZIP文件
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFile.getName());
        response.setContentLengthLong(zipFile.length());
        // 将ZIP文件写入响应流
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            FileUtil.writeToStream(zipFile, outputStream);
        } finally {
            // 删除临时ZIP文件
            zipFile.delete();
        }
    }

    @GetMapping("/checkfsutype")
    public ResponseEntity<ResponseResult> checkFsuType(String ip,String fsuType){
        return ResponseHelper.successful(fsuService.checkFsuType(ip, fsuType));
    }

    @GetMapping("/resetfsu")
    public ResponseEntity<ResponseResult> resetFsu(String fsuIp,String fileDirectory) throws IOException {
        boolean result = fsuService.resetFsu(fsuIp, fileDirectory);
        return ResponseHelper.successful(result);
    }

    @ApiOperation(value = "统计fsu采集器版本管理状态")
    @GetMapping("/fsuversionstatusstatistics")
    public ResponseEntity<ResponseResult> getFsuVersionStatusStatistics() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(fsuService.findFsuVersionStatusStatistics(userId));
    }
}
