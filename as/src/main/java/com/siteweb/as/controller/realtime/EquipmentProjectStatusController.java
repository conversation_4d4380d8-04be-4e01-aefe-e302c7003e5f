package com.siteweb.as.controller.realtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.realtime.ExportStructureRequestDTO;
import com.siteweb.as.service.StationDetailASService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.as.vo.EquipProjectASVO;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ProjectOperationResult;
import com.siteweb.monitoring.entity.EquipmentMaintain;
import com.siteweb.monitoring.service.EquipmentMaintainService;
import com.siteweb.monitoring.service.ProjectStateService;
import com.siteweb.monitoring.vo.EquipmentProjectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
@Slf4j
public class EquipmentProjectStatusController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    StationDetailASService stationDetailService;
    @Autowired
    ProjectStateService projectStateService;
    @Autowired
    EquipmentMaintainService equipmentMaintainService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Data
    public class MaskTimeGroupDTO {
        private Integer dataId = 0;
        private Integer groupCategory = 0;
        private String reason;
        private Integer sequenceId = -2;
        private Integer type = 0;
        private Boolean exception;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
        private Date startTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
        private Date endTime;
    }
    @ApiOperation(value = "获取设备工程状态列表")
    @GetMapping(value = "/realtime/equipmentprojectstatus", params = {"stationid","equipmentid"})
    public ResponseEntity<ResponseResult> EquipProjecttGet(Integer stationid, Integer equipmentid) {
        EquipmentMaintain equipmentMaintain= equipmentMaintainService.findByStationIdAndEquipmentId(stationid, equipmentid);
        MaskTimeGroupDTO masktime = new MaskTimeGroupDTO();
        if (equipmentMaintain != null) {
            masktime.setSequenceId(0);
            masktime.setDataId(equipmentMaintain.getEquipmentId());
            masktime.setStartTime(equipmentMaintain.getStartTime());
            masktime.setEndTime(equipmentMaintain.getEndTime());
            masktime.setReason(equipmentMaintain.getDescription());
        }
        return ResponseHelper.successful(masktime);
    }
    @ApiOperation(value = "设置设备工程状态")
    @PostMapping(value = {"/realtime/EquipmentProjectStatus","/realtime/equipmentprojectstatus"})
    public ResponseEntity<ResponseResult> EquipProjactSet(@RequestBody EquipProjectASVO configObject) {
        Integer userId = TokenUserUtil.getLoginUserId();
        for(String id : configObject.getConfigObjectIds()){
            String[] ids = id.split("\\.");
            EquipmentProjectVO vo = new EquipmentProjectVO();
            vo.setStationId(Integer.parseInt(ids[0]));
            vo.setEquipmentId(Integer.parseInt(ids[1]));
            vo.setStartTime(configObject.getMaskTimeGroup().getStartTime());
            vo.setEndTime(configObject.getMaskTimeGroup().getEndTime());
            vo.setReason(configObject.getMaskTimeGroup().getReason());

            equipmentMaintainService.setEquipmentProject(vo, userId);
        }
        return ResponseHelper.successful(configObject.getMaskTimeGroup());
    }
    @ApiOperation(value = "删除设备工程状态")
    @DeleteMapping(value = "/realtime/EquipmentProjectStatus", params = {"ids"})
    public ResponseEntity<ResponseResult> EquipProjactDel( String ids) {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<ProjectOperationResult> resultList = new ArrayList<>();
        List<Integer> reslist = new ArrayList<>();
        String[] ids2 = ids.split(",");
        for(String id : ids2){
            String[] ids3 = id.split("\\.");
            ProjectOperationResult res = equipmentMaintainService.deleteEquipmentProject(Integer.parseInt(ids3[1]), userId);
            if (Boolean.FALSE.equals(res.getResult())){
                res.setCfgId(id);
                resultList.add(res);
            }
        }
        if (CollUtil.isNotEmpty(resultList)){
            return ResponseHelper.successful(resultList);
        }
        return ResponseHelper.successful("0");
    }

    @ApiOperation(value = "导出设备状态列表")
    @PostMapping(value = "/realtime/EquipmentProjectStatus/excel")
    public ResponseEntity<Resource> EquipProjactExcel(@RequestBody ExportStructureRequestDTO exportStructureRequestDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseEntity.badRequest().body(null);
        }
        String[] columnArray = exportStructureRequestDTO.getColumns().split(",");
        String[] titleArray = exportStructureRequestDTO.getTitles().split(",");
        if (columnArray.length != titleArray.length || columnArray.length == 0) {
            return ResponseEntity.badRequest().body(null);
        }

        QueryInformation queryInfo = exportStructureRequestDTO.getQueryInformation();

        return ExcelExportUtil.exportExcel(()->stationDetailService.exportEquip(queryInfo, columnArray, titleArray, userId));

    }
}
