package com.siteweb.as.controller.eventshield;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.service.EquipmentShieldService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
@Api(value = "EquipmentShieldController", tags = {"设备屏蔽操作接口"})
public class EquipmentShieldController {

    @Autowired
    EquipmentShieldService equipmentShieldService;

    @ApiOperation(value = "获取设备屏蔽列表")
    @PostMapping("/equipmentshield/page")
    public ResponseEntity<ResponseResult> getEquipmentShield(@RequestBody ShieldFilterDTO shieldFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(equipmentShieldService.findEquipmentShieldPage(shieldFilterDTO.generatePaginationInfo(), shieldFilterDTO, userId));
    }

    @ApiOperation(value ="删除屏蔽设置")
    @DeleteMapping("/equipmentshield/batch")
    public ResponseEntity<ResponseResult> deleteShield(@RequestBody List<Integer> equipmentIds){
        Integer userId = TokenUserUtil.getLoginUserId();
        Boolean ret = equipmentShieldService.deleteShield(equipmentIds, userId);
        return ResponseHelper.successful(ret);
    }

    @ApiOperation(value = "批量保存设备屏蔽")
    @PostMapping(value = "/equipmentshield/batch", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchCreateEquipmentMasks(@Valid @RequestBody BatchEquipmentMaskVO batchEquipmentMaskVO) {
        if (null == batchEquipmentMaskVO ||
                (CollUtil.isEmpty(batchEquipmentMaskVO.getEquipmentIds()) && CollUtil.isEmpty(batchEquipmentMaskVO.getIds()))) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "EquipmentIds can not be null", HttpStatus.BAD_REQUEST);
        }
        equipmentShieldService.saveShield(batchEquipmentMaskVO, TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful("0");
    }

    @ApiOperation(value = "导出设备屏蔽")
    @PostMapping("/equipmentshield/equipmentexcel")
    public ResponseEntity<Resource> exportShield(@RequestBody ShieldFilterDTO shieldFilterDTO){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ExcelExportUtil.exportExcel(equipmentShieldService::exportShield, shieldFilterDTO, userId);
    }
}
