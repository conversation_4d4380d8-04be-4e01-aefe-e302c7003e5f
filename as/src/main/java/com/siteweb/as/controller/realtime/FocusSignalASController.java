package com.siteweb.as.controller.realtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.FocusSignalFilterASDTO;
import com.siteweb.as.service.FocusSignalASService;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "FocusSignalASController", tags = {"关注信号操作接口"})
@Slf4j
public class FocusSignalASController {

    @Autowired
    FocusSignalASService focusSignalASServiceImpl;
    /**
     * 获取关注信号列表
     * @return
     */
    @ApiOperation(value = "获取关注信号列表")
    @PostMapping(value = "/realtime/focussignal")
    public ResponseEntity<ResponseResult> getFocusSignal(@RequestBody FocusSignalFilterASDTO dto) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(focusSignalASServiceImpl.queryPageableFocusSignals(userId, dto));
    }

    @ApiOperation(value = "根据FocusSignalFilterVO导出关注信号excel")
    @PostMapping(value = "/realtime/focussignal/excel",params = {"columns","titles"})
    public ResponseEntity<Resource> FocusSignalsExcel(@RequestBody FocusSignalFilterASDTO dto,String columns, String titles) {
        List<String> columnList = CharSequenceUtil.split(columns,",");
        List<String> titileList = CharSequenceUtil.split(titles,",");
        if (CollUtil.isEmpty(columnList) || columnList.size() != titileList.size()) {
            return ResponseEntity.badRequest().body(null);
        }
        return ExcelExportUtil.exportExcel(focusSignalASServiceImpl,TokenUserUtil.getLoginUserId(),dto,columnList,titileList);
    }

}
