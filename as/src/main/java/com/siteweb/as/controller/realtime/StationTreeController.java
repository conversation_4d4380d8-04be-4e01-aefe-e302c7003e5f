package com.siteweb.as.controller.realtime;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.StationStructure;
import com.siteweb.as.service.StationTreeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
public class StationTreeController {
    @Autowired
    StationTreeService stationTreeService;
    @ApiOperation(value = "获取站点结构树")
    @GetMapping(value = "/realtime/stationtree",params = "treeCategoryId")
    public ResponseEntity<ResponseResult> getTreeByCategory(Integer treeCategoryId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(stationTreeService.getTreeByCategory(userId,treeCategoryId));
    }

    @ApiOperation(value = "获取站点结构树")
    @GetMapping(value = "/realtime/stationtree")
    public ResponseEntity<ResponseResult> getStationTreeByCategory() {
        StationStructure stationHead = new StationStructure();
        return new ResponseEntity(stationHead, HttpStatus.OK);
    }
}
