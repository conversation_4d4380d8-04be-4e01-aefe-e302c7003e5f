package com.siteweb.as.controller.realtime;

import com.siteweb.as.dto.ActiveSignalAS;
import com.siteweb.as.manager.ActiveSignalASManager;
import com.siteweb.as.manager.QueryInformationIdManager;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "ImportAntSignalController", tags = {"ActiveEvent操作接口"})
public class ImportantSignalController {
    @Autowired
    QueryInformationIdManager queryInfoIdManager;
    @Autowired
    ActiveSignalASManager activeSignalManagerAS;
    @ApiOperation(value = "属性页-重要信号")
    @GetMapping(value = "/realtime/importantsignal",params = {"stationId"})
    public ResponseEntity<ResponseResult> getStationImportantSignal(int stationId) {

        QueryInformation queryInfo = new QueryInformation();
        String uuid = UUID.randomUUID().toString();
        queryInfo.setQueryInformationId(uuid);
        queryInfo.setStationId(stationId);
        queryInfoIdManager.addOne(queryInfo);

        List<EquipmentActiveSignal> Equipments = activeSignalManagerAS.getStationImportantActiveSignalByProperty(stationId,25);

        List<ActiveSignalAS> signalList = new ArrayList<>();

        for(EquipmentActiveSignal equipment:Equipments){
            for(SimpleActiveSignal signal:equipment.getActiveSignals()) {
                ActiveSignalAS signal1 = new ActiveSignalAS();
                signal1.setSignalId(signal.getSignalId());
                signal1.setSignalName(signal.getSignalName());
                signal1.setSignalCategory(signal.getSignalCategory());
                signal1.setBaseTypeId(signal.getBaseTypeId());
                signal1.setUnit(signal.getUnit());

                signal1.setEventLevel(signal.getCurrentState());
                signal1.setSignalCategory(signal.getSignalCategory());
                signal1.getCurrentValue().setValueType(1);
                signal1.getCurrentValue().setStringValue(signal.getCurrentValue());
                signal1.setSampleTime(signal.getSampleTime());
                signal1.setSignalMeaning(signal.getCurrentValue());
                signal1.setDisplayIndex(signal.getDisplayIndex());

                signal1.setEquipmentId(equipment.getEquipmentId());
                signal1.setEquipmentName(equipment.getEquipmentName());
                signalList.add(signal1);
            }
        }
        HashMap resultMap = new HashMap();
        resultMap.put("queryInformatoinId", uuid);
        resultMap.put("signals", signalList);
        return ResponseHelper.successful(resultMap);

    }
    @ApiOperation(value = "属性页-重要信号")
    @GetMapping(value = "/realtime/importantsignal", params = {"queryInformationId"})
    public ResponseEntity<ResponseResult> getStationImportantSignalByQueryId(String queryInformationId) {
        QueryInformation queryInfo = queryInfoIdManager.getById(queryInformationId);
        if(queryInfo != null)
            return getStationImportantSignal(queryInfo.getStationId());
        return ResponseHelper.successful(Collections.emptyList());
    }
}
