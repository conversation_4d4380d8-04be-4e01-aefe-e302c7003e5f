package com.siteweb.as.controller.permission;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.enums.PermissionCategoryEnum;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.RolePermissionMapService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@RestController
@RequestMapping("/api")
@Api(value = "OperationController", tags = {""})
public class OperationController {
    @Autowired
    RolePermissionMapService rolePermissionMapService;
    @ApiOperation(value = "获取用户的操作集合")
    @GetMapping(value = "/permission/operation")
    public ResponseEntity<ResponseResult> getOperation(int PageId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        Set<Integer> permissionIds = rolePermissionMapService.findRolePermissionsByUserId(userId, PermissionCategoryEnum.REGION.getPermissionCategoryId());
        if (CollUtil.isNotEmpty(permissionIds)) {
            return ResponseHelper.successful(permissionIds);
        }
        return ResponseHelper.failed("userid be null");
    }
}
