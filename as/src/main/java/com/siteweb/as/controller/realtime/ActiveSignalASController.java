package com.siteweb.as.controller.realtime;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.ActiveSignalAS;
import com.siteweb.as.manager.ActiveSignalASManager;
import com.siteweb.as.manager.QueryInformationIdManager;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.hmi.service.GraphicHistoryDataPointService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.mamager.EquipmentManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * 活动告警表
 *
 */
@RestController
@Slf4j
@RequestMapping("/api")
@Api(value = "ActiveSignalASController", tags = {"ActiveSignal操作接口"})
public class ActiveSignalASController {
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    ActiveSignalASManager activeSignalManagerAS;
    @Autowired
    GraphicHistoryDataPointService graphicHistoryDataPointService;
    @ApiOperation(value = "获取设备的信号")
    @GetMapping(value = "/realtime/ActiveSignal", params = {"stationId","equipmentId"})
    public ResponseEntity<ResponseResult> getActiveSignalByEquipId(Integer stationId, Integer equipmentId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (equipment == null) {
            log.error("获取设备的信号失败,设备信息为null:{}", equipmentId);
            return ResponseHelper.failed("获取设备的信号失败,设备信息为null:{}");
        }
        List<ActiveSignalAS> activeSignalList = activeSignalManagerAS.getActiveSignalsByEquipmentId(equipmentId);

        for (ActiveSignalAS signal : activeSignalList) {
            signal.setEquipmentId(equipment.getEquipmentId());
            signal.setEquipmentName(equipment.getEquipmentName());
            signal.setStationId(equipment.getStationId());
        }
        QueryInformation queryInfo = new QueryInformation();
        queryInfo.setQueryInformationId(UUID.randomUUID().toString());
        queryInfo.setStationId(stationId);
        queryInfo.setEquipmentId(equipmentId);
        queryInfoIdManager.addOne(queryInfo);

        HashMap resultMap = new HashMap();
        resultMap.put("queryInformatoinId", queryInfo.getQueryInformationId());
        resultMap.put("signals", activeSignalList);
        return ResponseHelper.successful(resultMap);
    }
    @Autowired
    QueryInformationIdManager queryInfoIdManager;
    @ApiOperation(value = "获取设备的信号")
    @GetMapping(value = {"realtime/ActiveSignal","realtime/activesignal"}, params = {"stationId","equipmentId","signalId"})
    public ResponseEntity<ResponseResult> getActiveSignalByEquipId(Integer stationId, Integer equipmentId, Integer signalId) {

        ActiveSignalAS activeSignal = activeSignalManagerAS.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId,signalId);
        List<ActiveSignalAS> activeSignalList = new ArrayList<>();
        activeSignalList.add(activeSignal);

        QueryInformation queryInfo = new QueryInformation();
        queryInfo.setQueryInformationId(UUID.randomUUID().toString());
        queryInfo.setStationId(stationId);
        queryInfo.setEquipmentId(equipmentId);
        queryInfo.setSignalId(signalId);
        queryInfoIdManager.addOne(queryInfo);

        HashMap resultMap = new HashMap();
        resultMap.put("queryInformatoinId", queryInfo.getQueryInformationId());
        resultMap.put("signals", activeSignalList);
        return ResponseHelper.successful(resultMap);
    }
    @ApiOperation(value = "获取设备的历史信号")
    @GetMapping(value = {"realtime/ActiveSignal","realtime/activesignal"}, params = {"startTime","endTime","stationId","equipmentId","signalId"})
    public ResponseEntity<ResponseResult> getActiveSignalByTime(Date startTime,Date endTime,Integer stationId, Integer equipmentId, Integer signalId) {

        List<HistorySignal> hissignals = graphicHistoryDataPointService.findEquipmentHistorySignal(startTime, endTime, equipmentId, signalId);
        List<ActiveSignalAS> signals = new ArrayList<>();

        for(HistorySignal s:hissignals){
            ActiveSignalAS signal = new ActiveSignalAS();
             signal.setSampleTime(s.getSampleTime());
            ActiveSignalAS.currentValues currentValue = new ActiveSignalAS.currentValues();
            currentValue.setValueType(1);
            currentValue.setStringValue(s.getPointValue());
            signal.setCurrentValue(currentValue);
            signals.add(signal);
        }
        return ResponseHelper.successful(signals);
    }
    @ApiOperation(value = "获取设备的信号")
    @GetMapping(value = {"realtime/ActiveSignal","realtime/activesignal"}, params = {"queryInformationId"})
    public ResponseEntity<ResponseResult> getActiveSignalByqueryInformationId(String queryInformationId) {

        QueryInformation queryInfo =queryInfoIdManager.getById(queryInformationId);
        if(queryInfo == null)
            return ResponseHelper.failed("get QueryInformation fail");

        if(queryInfo.getSignalId() != null){
            List<ActiveSignalAS> activeSignalList = new ArrayList<>();
            ActiveSignalAS activeSignal = activeSignalManagerAS.getConfigSignalItemByEquipmentIdAndSignalId(queryInfo.getEquipmentId(), queryInfo.getSignalId());
            activeSignalList.add(activeSignal);
            return ResponseHelper.successful(activeSignalList);
        }
        else {
            Equipment equipment = equipmentManager.getEquipmentById(queryInfo.getEquipmentId());
            if (equipment == null) {
                return ResponseHelper.failed("getEquipmentId fail");
            }
            List<ActiveSignalAS> activeSignalList = activeSignalManagerAS.getActiveSignalsByEquipmentId(queryInfo.getEquipmentId());

            for (ActiveSignalAS signal : activeSignalList) {
                signal.setEquipmentId(equipment.getEquipmentId());
                signal.setEquipmentName(equipment.getEquipmentName());
                signal.setStationId(equipment.getStationId());
            }
            return ResponseHelper.successful(activeSignalList);
        }
    }

    @ApiOperation(value = "导出告警列表")
    @GetMapping(value = "/realtime/ActiveSignal/Export")
    public ResponseEntity<Resource> excelActiveSignal(String queryInformationId, String columns, String titles) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseEntity.badRequest().body(null);
        }
        String[] columnArray = columns.split(",");
        String[] titleArray = titles.split(",");
        if(columnArray.length != titleArray.length || columnArray.length == 0){
            return ResponseEntity.badRequest().body(null);
        }

        QueryInformation queryInfo = queryInfoIdManager.getById(queryInformationId);

        Equipment equipment = equipmentManager.getEquipmentById(queryInfo.getEquipmentId());
        if (equipment == null) {
            return ResponseEntity.badRequest().body(null);
        }
        List<ActiveSignalAS> activeSignalList = activeSignalManagerAS.getActiveSignalsByEquipmentId(queryInfo.getEquipmentId());
        for (ActiveSignalAS signal : activeSignalList) {
            signal.setEquipmentId(equipment.getEquipmentId());
            signal.setEquipmentName(equipment.getEquipmentName());
            signal.setStationId(equipment.getStationId());
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        for(int i = 0; i < columnArray.length;i++)
        {
            writer.addHeaderAlias(StringUtils.uncapitalize(columnArray[i]), titleArray[i]);
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(activeSignalList,true);

        String dateFormat = DateUtil.format(new Date(), "yyyy-MM-dd_HH:mm:ss");
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            writer.flush(bos, true);
            Resource resource = new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + dateFormat + ".xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);
        }catch (Exception e) {
            log.error("ActiveSignal export xlsx err.[err:{}]", e.getMessage());
        } finally {
            writer.close();
            IoUtil.close(bos);
        }
        return null;
    }
}
