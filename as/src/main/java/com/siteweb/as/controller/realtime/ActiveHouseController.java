package com.siteweb.as.controller.realtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.realtime.ExportStructureRequestDTO;
import com.siteweb.as.dto.realtime.HouseProjectStateResult;
import com.siteweb.as.util.ExcelExportUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ProjectOperationResult;
import com.siteweb.monitoring.entity.House;
import com.siteweb.monitoring.entity.ProjectStateHouse;
import com.siteweb.monitoring.service.HouseService;
import com.siteweb.monitoring.service.ProjectStateService;
import com.siteweb.monitoring.vo.HouseProjectVO;
import com.siteweb.as.service.StationDetailASService;
import com.siteweb.as.vo.HouseProjectASVO;
import com.siteweb.as.vo.QueryInformation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * 机房工程状态接口（总体列表接口在maintaincontorller）
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
@Slf4j
public class ActiveHouseController {
    @Autowired
    StationDetailASService stationDetailService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    ProjectStateService projectStateService;
    @Autowired
    HouseService houseService;
    private static final String USER_ID_IS_NULL = "userid is null";
    @ApiOperation(value = "导出机房状态列表")
    @PostMapping(value = "/realtime/activehouse/excel")
    public ResponseEntity<Resource> HouseProjectExcel(@RequestBody ExportStructureRequestDTO exportStructureRequestDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        String[] columnArray = exportStructureRequestDTO.getColumns().split(",");
        String[] titleArray = exportStructureRequestDTO.getTitles().split(",");
        if(columnArray.length != titleArray.length || columnArray.length == 0){
            return ResponseEntity.badRequest().body(null);
        }

        QueryInformation queryInfo = exportStructureRequestDTO.getQueryInformation();

        return ExcelExportUtil.exportExcel(() -> stationDetailService.exportActivehouse(queryInfo, columnArray, titleArray, userId));
    }
    @ApiOperation(value = "设置机房工程状态")
    @PostMapping(value = "/realtime/activehouse")
    public ResponseEntity<ResponseResult> HouseProjectSet(@RequestBody HouseProjectASVO configObject) {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<House> houseList = houseService.findHouses();
        for(String id : configObject.getConfigObjectUniqueIds()){
            String[] ids = id.split("_");
            HouseProjectVO vo = new HouseProjectVO();
            vo.setStationId(Integer.parseInt(ids[0]));
            vo.setHouseId(Integer.parseInt(ids[1]));
            House house = houseList.stream().filter(o->o.getHouseId() == Integer.parseInt(ids[1])).findFirst().orElse(null);
            if (house == null) {
                continue;
            }
            vo.setHouseName(house.getHouseName());
            vo.setStartTime(configObject.getMaskTimeGroup().getStartTime());
            vo.setEndTime(configObject.getMaskTimeGroup().getEndTime());
            vo.setReason(configObject.getMaskTimeGroup().getReason());

            projectStateService.setHouseProjectState(vo, userId);
        }
        return ResponseHelper.successful(configObject.getMaskTimeGroup());
    }

    @ApiOperation(value = "删除机房工程状态")
    @DeleteMapping(value = "/realtime/activehouse")
    public ResponseEntity<ResponseResult> HouseProjectDel(@RequestBody List<String> configids) {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<ProjectOperationResult> result = new ArrayList<>();
        for(String id : configids){
            String newid = id.replace('_',',');
            ProjectOperationResult temp = projectStateService.deleteHouseProjectState(newid, userId);
            if (Boolean.FALSE.equals(temp.getResult())){
                temp.setCfgId(newid);
                result.add(temp);
            }
        }
        if (CollUtil.isEmpty(result)){
            return ResponseHelper.successful(0);
        }
        return ResponseHelper.successful(result);
    }
    @ApiOperation(value = "获取机房状态列表")
    @GetMapping(value = "/realtime/activehouse", params = "uniqueId")
    public ResponseEntity<ResponseResult> getHouseProject(String uniqueId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        ProjectStateHouse house = projectStateService.getHouseProjectStateById(uniqueId.replace("_",","));
        HouseProjectStateResult result = new HouseProjectStateResult();
        if (house != null && house.getStartTime() != null && house.getEndTime() != null)
        {
            result.setStartTime(DateUtil.format(house.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            result.setEndTime(DateUtil.format(house.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            result.setReason(house.getReason());
            result.setSequenceId(0);
            result.setDataId(house.getHouseId());
        }
        else
        {
            result.setSequenceId(-2);
            result.setDataId(0);
        }

        return ResponseHelper.successful(result);
    }
}
