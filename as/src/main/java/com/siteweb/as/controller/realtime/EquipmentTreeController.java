package com.siteweb.as.controller.realtime;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.as.dto.EquipmentTreeDTO;
import com.siteweb.as.dto.HouseInfoDto;
import com.siteweb.as.enums.BusinessObjectType;
import com.siteweb.as.enums.CustomPictures;
import com.siteweb.as.mapper.ASMapper;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.StationEquipmentDTO;
import com.siteweb.monitoring.service.EquipmentMaskService;
import com.siteweb.monitoring.service.EquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动告警表
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
public class EquipmentTreeController {
    @Autowired
    ASMapper asMapper;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    EquipmentMaskService equipmentMaskService;

    @ApiOperation(value = "获取树-设备")
    @GetMapping(value = "/realtime/equipmenttree")
    public ResponseEntity<ResponseResult> getEquipmentTree(int stationId, int categoryId) {

        List<StationEquipmentDTO> equipDTO = equipmentService.findStationPageEquipmentDTOByStationId(stationId);
        equipDTO = equipDTO.stream().sorted(Comparator.comparing(StationEquipmentDTO::getEquipmentCategoryId)).collect(Collectors.toList());
        List<EquipmentTreeDTO> equiptreelist = new ArrayList<>();
        EquipmentTreeDTO station = new EquipmentTreeDTO();
        station.setObjectType(BusinessObjectType.ActiveStation.value());
        station.setNodeId(stationId + "");
        station.setUniqueId(stationId + "");
        station.setObjectId(stationId);
        station.setState(0);
        equiptreelist.add(station);

        List<HouseInfoDto> houseList = new ArrayList<>();
        // 根据名称排序
        CollUtil.sort(equipDTO, Comparator.comparing(StationEquipmentDTO::getEquipmentName, Comparator.nullsLast(Comparator.naturalOrder())));
        HashMap<Integer, EquipmentTreeDTO> speciltyList = new HashMap();
        if (categoryId == 1) {
            houseList = asMapper.getHouseInfoByStation(stationId);
            for (HouseInfoDto cfg : houseList) {
                EquipmentTreeDTO cfgHouse = new EquipmentTreeDTO();
                cfgHouse.setObjectType(BusinessObjectType.ActiveHouse.value());
                cfgHouse.setParentNodeId(stationId + "");
                cfgHouse.setNodeId(stationId + "." + cfg.getHouseId());
                cfgHouse.setUniqueId(cfgHouse.getNodeId());
                cfgHouse.setState(3);
                cfgHouse.setObjectId(cfg.getHouseId());
                cfgHouse.setName(cfg.getHouseName());
                cfgHouse.setIcon(CustomPictures.House);

                equiptreelist.add(cfgHouse);
            }
        } else if (categoryId == 2) {
            for (StationEquipmentDTO equip : equipDTO) {
                if (!speciltyList.containsKey(equip.getEquipmentCategoryId())) {
                    EquipmentTreeDTO category = new EquipmentTreeDTO();
                    category.setObjectType(BusinessObjectType.ActiveEquipmentCategory.value());
                    category.setParentNodeId(stationId + "");
                    category.setNodeId(stationId + "." + equip.getEquipmentCategoryId());
                    category.setName(equip.getEquipmentCategoryName());
                    category.setUniqueId(equip.getEquipmentCategoryId() + "");
                    category.setObjectId(equip.getEquipmentCategoryId());
                    category.setState(3);
                    equiptreelist.add(category);
                    speciltyList.put(equip.getEquipmentCategoryId(), category);
                }
            }
        }
        for (StationEquipmentDTO e : equipDTO) {
            EquipmentTreeDTO equip = new EquipmentTreeDTO();
            if (e.getOnlineStatus() != null)
                equip.setStatus(e.getOnlineStatus().value());
            if (e.getOnProject() != null)
                equip.setProjectState(e.getOnProject() ? 3 : 0);
            if (e.getMasked())
                equip.setProjectState(4);

            equip.setObjectType(BusinessObjectType.ActiveEquipment.value());
            equip.setUniqueId(e.getStationId() + "." + e.getEquipmentId());
            equip.setNodeId(e.getEquipmentId() + "");
            equip.setObjectId(e.getEquipmentId());
            equip.setName(e.getEquipmentName());
            equip.setIcon(CustomPictures.Equipment);
            if (e.getOnlineStatus() != null)
                equip.setState(e.getOnlineStatus().value());
            if (e.getMaxEventSeverity() != null && e.getMaxEventSeverity() != 0) {
                equip.setMaxEventSeverity(4 - e.getMaxEventSeverity());
                equip.setMaxEventLevel(e.getMaxEventSeverity());
            }
            if (categoryId == 1) {
                Optional<EquipmentTreeDTO> house = equiptreelist.stream().filter(o -> o.getObjectId().equals(e.getHouseId())).findFirst();
                if (house.isPresent()) {
                    //调用get()返回Optional值。
                    equip.setParentNodeId(e.getStationId() + "." + house.get().getObjectId());
                    if (house.get().getMaxEventSeverity() < equip.getMaxEventSeverity()) {
                        house.get().setMaxEventSeverity(equip.getMaxEventSeverity());
                        house.get().setMaxEventLevel(equip.getMaxEventLevel());
                    }
                }
            } else if (categoryId == 2) {
                equip.setParentNodeId(e.getStationId() + "." + e.getEquipmentCategoryId());
                EquipmentTreeDTO category = speciltyList.get(e.getEquipmentCategoryId());
                if (category.getMaxEventSeverity() < equip.getMaxEventSeverity()) {
                    category.setMaxEventSeverity(equip.getMaxEventSeverity());
                    category.setMaxEventLevel(equip.getMaxEventLevel());
                }
            }

            equiptreelist.add(equip);
        }
        HashMap resultMap = new HashMap();
        resultMap.put("queryInformatoinId", "");
        resultMap.put("data", equiptreelist);

        return ResponseHelper.successful(resultMap);
    }
}
