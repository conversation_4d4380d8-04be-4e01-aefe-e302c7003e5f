package com.siteweb.as.controller.expert.advice;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.expertadvice.entity.Fault;
import com.siteweb.expertadvice.entity.FaultExpertMap;
import com.siteweb.expertadvice.mapper.FaultExpertMapMapper;
import com.siteweb.expertadvice.service.FaultService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/5/22 18:02
 */

@RequestMapping({"/api/expertAdvice","/api/expertadvice"})
@RestController
public class FaultASController {

    @Autowired
    FaultService faultService;
    @Autowired
    FaultExpertMapMapper faultExpertMapMapper;
    @ApiOperation("根据告警基类类型获取关联故障")
    @GetMapping(value = "/fault", params = {"expertId"})
    public ResponseEntity<ResponseResult> getFaultByExpertId(Integer expertId) {
        return ResponseHelper.successful(faultService.findFaultByExpertId(expertId));
    }

    @ApiOperation("获取关联故障按理根据告警基类id")
    @GetMapping(value = "/fault", params = {"baseAlarmId"})
    public ResponseEntity<ResponseResult> getAlarmByTypeId(Integer baseAlarmId) {
        return ResponseHelper.successful(faultService.findAlarmByTypeId(baseAlarmId));
    }

    @ApiOperation("新增和更新关联故障案例")
    @PutMapping(value = "/fault")
    public ResponseEntity<ResponseResult> updateFault(@RequestBody Fault fault) {
        if(fault.getFaultId() == null || fault.getFaultId() == 0){
            faultService.createFault(fault);
        }
        else {
            faultService.updateFault(fault);
        }
        return ResponseHelper.successful(fault);
    }

    @ApiOperation("删除关联故障案例")
    @DeleteMapping(value = "/fault", params = {"Ids"})
    public ResponseEntity<ResponseResult> deleteFault(String Ids) {
        List<String> idslist = Arrays.stream(Ids.split(",")).toList();
        faultExpertMapMapper.delete(Wrappers.lambdaQuery(FaultExpertMap.class)
                .in(FaultExpertMap::getFaultId, idslist));
        return ResponseHelper.successful("");
    }
}
