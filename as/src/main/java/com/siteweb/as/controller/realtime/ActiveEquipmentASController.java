package com.siteweb.as.controller.realtime;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.EquipmentBasicDto;
import com.siteweb.monitoring.dto.StationEquipmentDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.as.dto.shield.EquipmentInfoDTO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 活动告警表
 *
 */
@RestController
@RequestMapping("/api")
@Api(value = "CustemASController", tags = {"ActiveEvent操作接口"})
public class ActiveEquipmentASController {
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @ApiOperation(value = "属性页-设备信息")
    @GetMapping(value = "/realtime/activeequipment", params = {"stationId","equipmentId"})
    public ResponseEntity<ResponseResult> getEquipmentInfo(int stationId, int equipmentId) {
        Equipment equipInfo = equipmentService.findById(equipmentId);
        if (Objects.isNull(equipInfo)) {
            return ResponseHelper.failed("-1", "equipInfo Not Found.", HttpStatus.NOT_FOUND);
        }
        HashMap resultMap = new HashMap();
        resultMap.put("stationId", equipInfo.getStationId());
        resultMap.put("equipmentId", equipInfo.getEquipmentId());
        resultMap.put("vendor", equipInfo.getVendor());
        resultMap.put("unit", equipInfo.getUnit());
        resultMap.put("equipmentStyle", equipInfo.getEquipmentStyle());
        resultMap.put("equipmentModule", equipInfo.getEquipmentModule());
        resultMap.put("equipmentNo", equipInfo.getEquipmentNo());
        resultMap.put("buyDate", equipInfo.getBuyDate());
        resultMap.put("usedDate", equipInfo.getUsedDate());
        resultMap.put("price", equipInfo.getPrice());
        resultMap.put("ratedCapacity", equipInfo.getRatedCapacity());
        resultMap.put("assetState", equipInfo.getAssetState());
        DataItem item = dataItemService.findByEntryIdIdAndItemId(10, equipInfo.getAssetState());
        resultMap.put("assetStatename", item!=null?item.getItemValue():"");
        resultMap.put("projectName", equipInfo.getProjectName());

        return ResponseHelper.successful(equipInfo);
    }

    @Data
    private static class ValueInfo{
        @JsonProperty("DataType")
        private String dataType;
        @JsonProperty("Name")
        private String name;
        @JsonProperty("Value")
        private Object value;
    }
    @ApiOperation(value = "属性页-设备信息保存")
    @PutMapping(value = "/realtime/activeequipment")
    public ResponseEntity<ResponseResult> SaveEquipmentInfo(@RequestBody List<ValueInfo> valueList) {
        Integer equipmentId = null;
        for(ValueInfo value:valueList) {
            if (value.getValue() == null)
                continue;
            if (value.name.equals("EquipmentId"))
                equipmentId = (int) value.getValue();
        }
        if(equipmentId == null)
            return ResponseHelper.failed("no EquipmentId");

        EquipmentBasicDto dto = equipmentService.findEquipmentBasic(equipmentId);
        if(dto == null)
            return ResponseHelper.failed("not found equipmentBasic by equipmentId="+ equipmentId);
        for(ValueInfo value:valueList){
            if(value.getValue() == null)
                continue;
            if(value.name.equals("EquipmentId"))
                dto.setEquipmentId((int)value.getValue());
            if(value.name.equals("Vendor"))
                dto.setVendor((String) value.getValue());
            if(value.name.equals("Unit"))
                dto.setUnit((String)value.getValue());
            if(value.name.equals("EquipmentStyle"))
                dto.setEquipmentStyle((String)value.getValue());
            if(value.name.equals("EquipmentModule"))
                dto.setEquipmentModule((String)value.getValue());
            if(value.name.equals("EquipmentNo"))
                dto.setEquipmentNo((String)value.getValue());
            if(value.name.equals("AssetState"))
                dto.setAssetState((int)value.getValue());
            if(value.name.equals("BuyDate")) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date aa = simpleDateFormat.parse((String) value.getValue());
                    dto.setBuyDate(aa);
                } catch (ParseException e) {
                    ;
                }
            }
            if(value.name.equals("UsedDate")) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    dto.setUsedDate(simpleDateFormat.parse((String) value.getValue()));
                } catch (ParseException e) {
                    ;
                }
            }
            if(value.name.equals("UsedLimit"))
                dto.setUsedLimit(Double.valueOf((Integer)value.getValue()));
            if(value.name.equals("Price"))
                dto.setPrice(Double.valueOf((Integer)value.getValue()));
            if(value.name.equals("RatedCapacity"))
                dto.setRatedCapacity((String)value.getValue());
            if(value.name.equals("ProjectName"))
                dto.setProjectName((String)value.getValue());
        }
        boolean res = equipmentService.updateEquipmentBasicInfo(dto);

        return ResponseHelper.successful(res, HttpStatus.OK);
    }

    @ApiOperation(value = "属性页-信号")
    @GetMapping(value = "/realtime/activeequipment",params = {"stationId"})
    public ResponseEntity<ResponseResult> getEquipmentListByStationId(int stationId) {

        List<StationEquipmentDTO> equipDTO = equipmentService.findStationPageEquipmentDTOByStationId(stationId);

        List<EquipmentInfoDTO> equipInfolist = new ArrayList<>();
        for (StationEquipmentDTO e : equipDTO) {
            EquipmentInfoDTO equip = new EquipmentInfoDTO();
            equip.setStationId(e.getStationId());
            equip.setEquipmentId(e.getEquipmentId());
            equip.setCenterName(e.getCenterName());
            equip.setStationName(e.getStationName());

            equip.setStationGradeName(e.getStationGrade());
            equip.setStationCategoryName(e.getStationCategory());
            equip.setEquipmentName(e.getEquipmentName());
            equip.setHouseName(e.getHouseName());
            equip.setModel(e.getEquipmentStyle());
            equip.setState(messageSourceUtil.getMessage("api.stationStatus." + e.getOnlineStatus().value()));
            equip.setFactory(e.getVendor());
            equip.setEquipmentstate(e.getOnlineStatus().value());

            equipInfolist.add(equip);
        }
        HashMap resultMap = new HashMap();
        resultMap.put("data", equipInfolist);

        HashMap pagingInfo = new HashMap();
        pagingInfo.put("pageSize", 0);
        pagingInfo.put("currentPageIndex", 1);
        pagingInfo.put("totalPagesCount", 1);
        pagingInfo.put("isPaging", true);
        pagingInfo.put("count", 0);
        resultMap.put("pagingInfo", pagingInfo);

        return ResponseHelper.successful(resultMap);
    }
    @ApiOperation(value = "属性页-信号")
    @GetMapping(value = "/realtime/activeequipment", params = {"stationId","houseId"})
    public ResponseEntity<ResponseResult> getEquipmentListByStationIdHouseId(int stationId, int houseId) {

        List<StationEquipmentDTO> equipDTO = equipmentService.findStationPageEquipmentDTOByStationId(stationId);

        if(houseId > 0)
            equipDTO = equipDTO.stream().filter(o->o.getHouseId()==houseId).toList();
        List<HashMap> resultMap = new ArrayList<>();
        for (StationEquipmentDTO e : equipDTO) {
            HashMap equipMap = new HashMap();
            equipMap.put("houseId", e.getHouseId());
            equipMap.put("id", e.getEquipmentId());
            equipMap.put("name", e.getEquipmentName());
            equipMap.put("alarm", e.getMaxEventSeverity());
            equipMap.put("state", e.getOnlineStatus());

            resultMap.add(equipMap);
        }

        return ResponseHelper.successful(resultMap);
    }
}
