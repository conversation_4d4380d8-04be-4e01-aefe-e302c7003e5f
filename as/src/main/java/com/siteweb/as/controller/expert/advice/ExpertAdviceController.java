package com.siteweb.as.controller.expert.advice;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.expertadvice.entity.ExpertAdviceItem;
import com.siteweb.expertadvice.service.ExpertAdviceItemService;
import com.siteweb.monitoring.dto.BaseAlarmDTO;
import com.siteweb.monitoring.mapper.StandardDicEventMapper;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.service.EquipmentBaseTypeService;
import com.siteweb.utility.service.StandardVerService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * /api/expertAdvice/BaseEquipment 替换成 /api/equipmentbasetypes
 * @Author: lzy
 * @Date: 2023/5/22 17:22
 */
@RequestMapping(value = {"/api/expertAdvice","/api/expertadvice"})
@RestController
public class ExpertAdviceController {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    ExpertAdviceItemService expertAdviceItemService;
    @Autowired
    StandardDicEventMapper standardDicEventMapper;
    @Autowired
    StandardVerService standardVerService;
    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;
    @ApiOperation("获取所有知识库")
    @GetMapping("/expertadviceitem")
    public ResponseEntity<ResponseResult> getExpertAdvice() {
        return ResponseHelper.successful(expertAdviceItemService.getExpertAdvice());
    }

    @ApiOperation("新增知识库")
    @PostMapping("/expertadviceitem")
    public ResponseEntity<ResponseResult> saveExpertAdvice(@RequestBody ExpertAdviceItem expertAdviceItemEntity) {
        if (expertAdviceItemService.existsById(expertAdviceItemEntity.getBaseAlarmId())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID),String.format(messageSourceUtil.getMessage("common.field.expertAdviceExists"), expertAdviceItemEntity.getBaseAlarmName()), HttpStatus.BAD_REQUEST);
        }
        expertAdviceItemService.saveExpertAdvice(expertAdviceItemEntity);
        return ResponseHelper.successful(expertAdviceItemEntity);
    }

    @ApiOperation("基类id查询知识库")
    @GetMapping(value = "/expertadviceitem", params = {"baseAlarmId"})
    public ResponseEntity<ResponseResult> getExpertAdviceByBaseAlarmId(Integer baseAlarmId) {
        return ResponseHelper.successful(expertAdviceItemService.getExpertAdviceByBaseAlarmId(baseAlarmId));
    }

    @ApiOperation("新增或更新知识库")
    @PutMapping("/expertadviceitem")
    public ResponseEntity<ResponseResult> updateExpertAdvice(@RequestBody ExpertAdviceItem expertAdviceItemEntity) {
        if(expertAdviceItemEntity.getExpertId() == null)
        {
            if (expertAdviceItemService.existsById(expertAdviceItemEntity.getBaseAlarmId())) {
                ExpertAdviceItem item = expertAdviceItemService.getExpertAdvice().stream().filter(o->o.getBaseAlarmId().equals(expertAdviceItemEntity.getBaseAlarmId())).findFirst().orElse(null);
                if(item != null)
                    expertAdviceItemEntity.setExpertId(item.getExpertId());
            }
        }
        if(expertAdviceItemEntity.getExpertId() == null) {
            expertAdviceItemService.saveExpertAdvice(expertAdviceItemEntity);
            return ResponseHelper.successful(expertAdviceItemEntity.getExpertId());
        }
        else {
            expertAdviceItemService.updateExpertAdvice(expertAdviceItemEntity);
            return ResponseHelper.successful(expertAdviceItemEntity);
        }
    }

    @ApiOperation("删除知识库")
    @DeleteMapping(value = "/expertadviceitem",params = {"expertIds"})
    public ResponseEntity<ResponseResult> deleteExpertAdvice(@RequestParam("expertIds") String expertIds) {
        expertAdviceItemService.deleteExpertAdvice(List.of(expertIds.split(",")));
        return ResponseHelper.successful("true");
    }
    @ApiOperation("查询基类告警")
    @GetMapping(value = "/basealarm",params = {"baseequipmentid","stationcategory"})
    public ResponseEntity<ResponseResult> baseAlarm(@RequestParam("baseequipmentid") Integer baseEquipmentId, @RequestParam("stationcategory") Integer stationCategory) {
        int standardVer = standardVerService.getStandardVer();
        List<BaseAlarmDTO> baseAlarmList = standardDicEventMapper.findBaseAlarmByStandardVerAndBaseEquipmentid(standardVer, baseEquipmentId);
        Integer finalStationCategory = ObjectUtil.isNotNull(stationCategory) ? stationCategory : 1;
        return ResponseHelper.successful(baseAlarmList.stream().filter(a -> Objects.equals(a.getStationCategory(), finalStationCategory)).toList());
    }
    @ApiOperation("告警列表新建专家建议")
    @GetMapping(value = "/alarmtoexpert")
    public ResponseEntity<ResponseResult> baseAlarmByBaeAlarmId(@RequestParam("basetypeid") Integer basetypeid) {
        int standardVer = standardVerService.getStandardVer();
        List<BaseAlarmDTO> baseAlarmDTO = standardDicEventMapper.findBaseAlarmByStandardVerAndBaseAlarmId(standardVer, basetypeid).stream().distinct().toList();
        //填充设备名字段
        List<Integer> equipmetlist = baseAlarmDTO.stream().map(BaseAlarmDTO::getBaseEquipmentId).distinct().filter(Objects::nonNull).toList();
        Map<Integer, String> collect = equipmentBaseTypeService.findEquipmentBaseTypes().stream().filter(a -> equipmetlist.contains(a.getBaseEquipmentId())).collect(Collectors.toMap(EquipmentBaseType::getBaseEquipmentId, EquipmentBaseType::getBaseEquipmentName));
        baseAlarmDTO.forEach(a -> a.setBaseEquipmentName(collect.get(a.getBaseEquipmentId())));
        return ResponseHelper.successful(baseAlarmDTO);
    }
}
