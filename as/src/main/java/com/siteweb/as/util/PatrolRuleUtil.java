package com.siteweb.as.util;

import com.siteweb.as.dto.patrol.AlarmInfoDTO;
import com.siteweb.as.dto.patrol.PatrolRuleDTO;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PatrolRuleUtil {
    // 定义常量
    private static final String INSPECTION_VALUE = "巡检值";


    public static String calculateLimitMeaning(PatrolRuleDTO patrolRuleDTO) {
        return calculateLimitMeaning(
                patrolRuleDTO.getLimitDown(),
                patrolRuleDTO.getLimitDownCalOpId(),
                patrolRuleDTO.getLimitDownCalOperator(),
                patrolRuleDTO.getLimitUp(),
                patrolRuleDTO.getLimitUpCalOpId(),
                patrolRuleDTO.getLimitUpCalOperator(),
                patrolRuleDTO.getByPercentage(),
                patrolRuleDTO.getRatedValue()
        );
    }

    public static String calculateLimitMeaning(AlarmInfoDTO alarmInfoDTO) {
        return calculateLimitMeaning(
                alarmInfoDTO.getLimitDown(),
                alarmInfoDTO.getLimitDownCalOpId(),
                alarmInfoDTO.getLimitDownCalOperator(),
                alarmInfoDTO.getLimitUp(),
                alarmInfoDTO.getLimitUpCalOpId(),
                alarmInfoDTO.getLimitUpCalOperator(),
                alarmInfoDTO.getByPercentage(),
                alarmInfoDTO.getRatedValue()
        );
    }

    /**
     * 计算预警范围
     */
    public static String calculateLimitMeaning(
            Double limitDown,
            Integer limitDownCalOpId,
            String limitDownCalOperator,
            Double limitUp,
            Integer limitUpCalOpId,
            String limitUpCalOperator,
            Integer byPercentage,
            Double ratedValue
    ) {
        // 定义条件
        boolean hasLimitDown = limitDown != null && limitDownCalOpId != null && limitDownCalOpId != 0;
        boolean hasLimitUp = limitUp != null && limitUpCalOpId != null && limitUpCalOpId != 0;
        boolean usePercentage = byPercentage != null && byPercentage == 1 && ratedValue != null;
        // 当是否按百分比开启时用%，否则就是正常比较
        if (usePercentage) {
            if (hasLimitDown && hasLimitUp) {
                return "(" + limitDown + "% * " + ratedValue + ")" + limitDownCalOperator + INSPECTION_VALUE +
                        limitUpCalOperator + "(" + limitUp + "% * " + ratedValue + ")";
            } else if (hasLimitUp) {
                return INSPECTION_VALUE + limitUpCalOperator + "(" + limitUp + "% * " + ratedValue + ")";
            } else if (hasLimitDown) {
                return "(" + limitDown + "% * " + ratedValue + ")" + limitDownCalOperator + INSPECTION_VALUE;
            }
        } else {
            if (hasLimitDown && hasLimitUp) {
                return limitDown + limitDownCalOperator + INSPECTION_VALUE + limitUpCalOperator + limitUp;
            } else if (hasLimitUp) {
                return INSPECTION_VALUE + limitUpCalOperator + limitUp;
            } else if (hasLimitDown) {
                return limitDown + limitDownCalOperator + INSPECTION_VALUE;
            }
        }
        return "未配置";
    }
}
