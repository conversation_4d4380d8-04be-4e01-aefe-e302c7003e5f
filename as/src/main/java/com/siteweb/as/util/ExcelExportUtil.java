package com.siteweb.as.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.as.dto.FocusSignalFilterASDTO;
import com.siteweb.as.service.ActiveEventASService;
import com.siteweb.as.service.FocusSignalASService;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

@UtilityClass
@Slf4j
public class ExcelExportUtil {
    /**
     * 通用的 Excel 导出方法
     *
     * @param writerSupplier 提供 ExcelWriter 的逻辑
     * @return ResponseEntity<Resource>
     */
    public ResponseEntity<Resource> exportExcel(Supplier<ExcelWriter> writerSupplier) {
        String fileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        try (ExcelWriter writer = writerSupplier.get();
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            writer.flush(bos, true);
            Resource resource = new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
            return ResponseEntity.ok()
                                 .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ".xlsx")
                                 .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                                 .body(resource);
        } catch (Exception e) {
            log.error("Export xlsx error: {}", ExceptionUtil.stacktraceToString(e));
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 使用 activeEventASService 的导出方法
     */
    public ResponseEntity<Resource> exportExcel(ActiveEventASService activeEventASService,Integer userId, List<String> columnList, List<String> titileList) {
        return exportExcel(() -> activeEventASService.exportActiveEvents(userId, columnList, titileList));
    }
    /**
     * 使用 activeEventASService 的导出方法
     */
    public ResponseEntity<Resource> exportExcel(FocusSignalASService focusSignalASService, Integer userId, FocusSignalFilterASDTO dto , List<String> columnList, List<String> titileList) {
        return exportExcel(() -> focusSignalASService.findFocusSignalExcelWriter(userId, dto,columnList, titileList));
    }

    /**
     * 使用 Function 的导出方法
     */
    public <T> ResponseEntity<Resource> exportExcel(Function<T, ExcelWriter> exportFunction, T filterDTO) {
        return exportExcel(() -> exportFunction.apply(filterDTO));
    }

    /**
     * 使用 BiFunction 的导出方法
     */
    public <T> ResponseEntity<Resource> exportExcel(BiFunction<T, Integer, ExcelWriter> exportFunction, T filterDTO, Integer userId) {
        return exportExcel(() -> exportFunction.apply(filterDTO, userId));
    }
}
