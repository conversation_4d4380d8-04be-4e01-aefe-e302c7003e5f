package com.siteweb.as.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class DirectoryComparatorUtil {

    /**
     * 比较两个目录路径，返回它们是否相同
     *
     * @param dir1Path 第一个目录的路径字符串
     * @param dir2Path 第二个目录的路径字符串
     * @return 如果目录内容完全相同则返回true，否则返回false
     * @throws IOException 如果读取文件或目录时发生错误
     */
    public static boolean compareDirectories(String dir1Path, String dir2Path) throws IOException {
        return compareDirectories(Paths.get(dir1Path), Paths.get(dir2Path));
    }

    /**
     * 比较两个目录，返回它们是否相同
     *
     * @param dir1 第一个目录的Path对象
     * @param dir2 第二个目录的Path对象
     * @return 如果目录内容完全相同则返回true，否则返回false
     * @throws IOException 如果读取文件或目录时发生错误
     */
    public static boolean compareDirectories(Path dir1, Path dir2) throws IOException {
        // 确保目录存在
        if (!Files.exists(dir1) || !Files.isDirectory(dir1)) {
            throw new IOException("目录1不存在或不是有效目录: " + dir1);
        }
        if (!Files.exists(dir2) || !Files.isDirectory(dir2)) {
            throw new IOException("目录2不存在或不是有效目录: " + dir2);
        }

        // 获取目录1的所有文件及其哈希值
        Map<String, String> files1 = new HashMap<>();
        collectFiles(dir1, dir1, files1);

        // 获取目录2的所有文件及其哈希值
        Map<String, String> files2 = new HashMap<>();
        collectFiles(dir2, dir2, files2);

        // 比较文件数量
        if (files1.size() != files2.size()) {
            log.info("文件数量不同: 目录1有 {} 个文件，目录2有 {} 个文件", files1.size(), files2.size());
            printExtraFiles(files1, files2, "目录1中存在但目录2中不存在的文件");
            printExtraFiles(files2, files1, "目录2中存在但目录1中不存在的文件");
            return false;
        }

        // 比较每个文件的哈希值
        boolean allEqual = true;
        for (Map.Entry<String, String> entry : files1.entrySet()) {
            String relativePath = entry.getKey();
            String hash1 = entry.getValue();
            String hash2 = files2.get(relativePath);

            if (hash2 == null) {
                log.info("文件只存在于目录1中: {}", relativePath);
                allEqual = false;
            } else if (!hash1.equals(hash2)) {
                log.info("文件内容不同: {}", relativePath);
                allEqual = false;
            }
        }

        return allEqual;
    }

    /**
     * 检查指定的备份目录是否与其同级目录下的任何其他目录内容相同。
     *
     * @param currentBackupDir 要检查的备份目录的 Path 对象。
     * @return 如果找到至少一个内容完全相同的同级目录，则返回 true（表示重复）；否则返回 false（表示唯一）。
     * @throws IOException 如果读取文件或目录时发生错误。
     */
    public static boolean isBackupDuplicate(Path currentBackupDir) throws IOException {
        // 确保当前备份目录存在且是目录
        if (!Files.exists(currentBackupDir) || !Files.isDirectory(currentBackupDir)) {
            throw new IOException("指定的备份目录不存在或不是有效目录: " + currentBackupDir);
        }
        // 获取当前备份目录的父目录
        Path backupParentDir = currentBackupDir.getParent();
        if (backupParentDir == null) {
            // 如果没有父目录（例如是根目录），则无法比较，认为不重复
            log.warn("无法获取备份目录 '{}' 的父目录，无法进行重复检查。", currentBackupDir);
            return false;
        }
        if (!Files.exists(backupParentDir) || !Files.isDirectory(backupParentDir)) {
            throw new IOException("备份目录的父目录不存在或不是有效目录: " + backupParentDir);
        }
        log.info("开始检查备份目录 '{}' 是否在 '{}' 中存在重复内容...", currentBackupDir.getFileName(), backupParentDir);
        // 获取父目录下的所有其他目录
        List<Path> otherDirs;
        try (Stream<Path> pathStream = Files.list(backupParentDir)) {
            otherDirs = pathStream
                    .filter(Files::isDirectory) // 只选择目录
                    .filter(path -> !path.equals(currentBackupDir)) // 排除自身
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("无法列出父目录 '{}' 中的内容", backupParentDir, e);
            throw e; // 重新抛出异常
        }
        if (otherDirs.isEmpty()) {
            log.info("在 '{}' 中没有找到其他备份目录进行比较。", backupParentDir);
            return false; // 没有其他目录可比较，自然不重复
        }
        log.info("找到 {} 个其他目录进行比较: {}", otherDirs.size(), otherDirs.stream().map(Path::getFileName).collect(Collectors.toList()));
        // 将当前备份目录与每个其他目录进行比较
        for (Path otherDir : otherDirs) {
            log.debug("比较 '{}' 和 '{}'", currentBackupDir.getFileName(), otherDir.getFileName());
            try {
                boolean areEqual = compareDirectories(currentBackupDir, otherDir);
                if (areEqual) {
                    log.info("发现重复内容：备份目录 '{}' 与 '{}' 内容完全相同。", currentBackupDir.getFileName(), otherDir.getFileName());
                    return true; // 找到一个相同的，即可确定是重复的
                }
            } catch (IOException e) {
                // 如果某个目录比较失败，记录错误并继续比较下一个
                log.error("比较目录 '{}' 和 '{}' 时出错: {}", currentBackupDir.getFileName(), otherDir.getFileName(), e.getMessage(), e);
                // 根据策略，可以选择是跳过还是抛出异常，这里选择跳过继续检查其他目录
            }
        }
        // 如果遍历完所有其他目录都没有找到相同的，则认为是不重复的
        log.info("备份目录 '{}' 与所有其他 {} 个目录的内容都不同。", currentBackupDir.getFileName(), otherDirs.size());
        return false;
    }

    private static void collectFiles(Path baseDir, Path currentDir, Map<String, String> files) throws IOException {
        Files.walkFileTree(currentDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (!attrs.isDirectory()) {
                    String relativePath = baseDir.relativize(file).toString();
                    String hash = calculateFileHash(file);
                    files.put(relativePath, hash);
                }
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFileFailed(Path file, IOException exc) throws IOException {
                log.error("无法访问文件: {} - {}", file, exc.getMessage(), exc);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    private static String calculateFileHash(Path file) throws IOException {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] fileBytes = Files.readAllBytes(file);
            byte[] hashBytes = digest.digest(fileBytes);

            // 将哈希值转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte hashByte : hashBytes) {
                String hex = Integer.toHexString(0xff & hashByte);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    private static void printExtraFiles(Map<String, String> files1, Map<String, String> files2, String message) {
        log.info(message + ":");
        for (String file : files1.keySet()) {
            if (!files2.containsKey(file)) {
                log.info(" - {}", file);
            }
        }
    }
}
