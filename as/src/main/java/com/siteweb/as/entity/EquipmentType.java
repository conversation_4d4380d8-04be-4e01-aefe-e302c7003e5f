package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_IcsEquipmentType")
public class EquipmentType {
    /**
     * 设备类型ID
     */
    private Integer equipmentTypeId;

    /**
     * 设备类型名称
     */
    private String equipmentTypeName;

    /**
     * 是否可以输入ID
     */
    private Integer isCanInputId;
}
