package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("tbl_patrolstandardgroup")
public class PatrolStandardGroup {
    @TableId(type = IdType.AUTO)
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;

    private Integer EquipmentLogicClassId;

    private Integer StandardDicId;
    /**
     * 备注
     */
    private String note;

}

