package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_IcsContractMaintenance")
public class IcsContractMaintenance {
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 合同编号 */
    private String contractNo;

    /** 项目名称 */
    private String projectName;

    /** 开始日期 */
    private Date startDate;

    /** 结束日期 */
    private Date endDate;

    /** 维护条款 */
    private String maintenanceTerms;
}
