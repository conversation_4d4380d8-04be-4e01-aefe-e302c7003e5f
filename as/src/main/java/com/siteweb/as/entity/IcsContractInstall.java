package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("tbl_icscontractinstall")
public class IcsContractInstall {
    /** 合同编号 */
    private String contractNo;

    /** 项目名称 */
    private String projectName;

    /** 起始日期 */
    private Date primaryDate;

    /** 结束日期 */
    private Date endDate;

    /** 质保起始点 */
    private String qualityStartPoint;

    /** 质保期 */
    private Integer qualityPeriod;

    /** 质保条款 */
    private String qualityTerms;

    /** 站点数量 */
    private Integer stationCount = 0;

    /** FSU数量 */
    private Integer fsuCount = 0;

    /** 设备数量 */
    private Integer equipmentCount = 0;
}
