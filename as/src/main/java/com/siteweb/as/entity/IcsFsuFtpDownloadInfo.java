package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TableName("tbl_icsfsuftpdownloadinfo")
public class IcsFsuFtpDownloadInfo {
    /**
     * 下载时间
     */
    private Date downLoadTime;

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * FSU IP
     */
    private String fsuIp;

    /**
     * 下载路径
     */
    private String downLoadPath;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;
}
