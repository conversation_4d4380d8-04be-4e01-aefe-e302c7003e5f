package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("tbl_equipmentprojectinfo")
public class EquipmentProjectInfo {

    /** 站点ID */
    private Integer stationId;

    /** 监控单元ID */
    private Integer monitorUnitId;

    /** 设备ID */
    private Integer equipmentId;

    /** 项目名称 */
    private String projectName;

    /** 合同编号 */
    private String contractNo;

    /** 安装时间 */
    private Date installTime;

    /** 设备序列号 */
    private String equipmentSN;

    /** SO */
    private String so;
}
