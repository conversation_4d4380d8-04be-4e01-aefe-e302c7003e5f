package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("tbl_monitorunitprojectinfo")
public class MonitorUnitProjectInfo {
    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 安装时间
     */
    private Date installTime;
}
