package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("TBL_IcsPlatformQrcode")
public class IcsPlatformQrCode {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 设备类型ID
     */
    private Integer equipmentTypeId;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 操作员名称
     */
    private String operatorName;
}

