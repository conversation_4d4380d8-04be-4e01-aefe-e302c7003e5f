package com.siteweb.as.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("TBL_CustomInfo")
public class CustomInfo {
    @TableId(type = IdType.INPUT)
    private Integer customInfoId;
    private Integer userId;
    private String customType;
    /**
     * 自定义内容 保存时是xml 前端渲染时变成json 【很奇怪不知道为什么】
     */
    private String customContent;
    private Date createTime;
    private String description;
}