package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("tbl_patrolgroup")
public class PatrolGroup {
    @TableId(type = IdType.AUTO)
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;

    private Integer baseEquipmentId;

    private Integer baseTypeId;
    /**
     * 备注
     */
    private String note;

}

