package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@NoArgsConstructor
@TableName("TBL_HistorySelection")
public class HistorySelection {
    @TableId(type = IdType.AUTO)
    private Integer historySelectionId;
    private Integer userId;
    private String selectionType;
    private String selectionName;
    private String selectionContent;
    private String description;
    private Date createTime;
    private String queryInformation;
}
