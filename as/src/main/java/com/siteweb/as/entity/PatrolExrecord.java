package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


@Data
@TableName("tbl_patrolexrecord")
public class PatrolExrecord {
    private Integer centerId;
    private String centerName;
    private Integer groupId;
    private String groupName;
    private Integer stationId;
    private String stationName;
    private String equipmentCategoryName;
    private Integer equipmentId;
    private String equipmentName;
    private Integer signalId;
    private String signalName;
    private String signalValue;
    private Date recordTime;
    private String unit;
    private Double limitDown;
    private Integer limitDownCalOpId;
    private Double limitUp;
    private Integer limitUpCalOpId;
    private Integer byPercentage;
    private Double ratedValue;
    private Integer warningLevelId;
    private Integer reasonable;
    private String isPowerOffAlarm;
    private Date createTime;
    private String sn;

}

