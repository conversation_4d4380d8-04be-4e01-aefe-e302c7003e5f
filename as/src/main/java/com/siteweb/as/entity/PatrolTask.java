package com.siteweb.as.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.FieldNameConstants;


@Data
@TableName("tbl_patroltask")
@FieldNameConstants
public class PatrolTask {
    @TableId(type = IdType.AUTO)
    private Integer taskId;
    /**
     * 分组名称
     */
    private String taskName;
    /**
     * tbl_patrolcronexpression id
     */
    private Integer cronId;
    /**
     * 分组id
     */
    private Integer groupId;
    /**
     * 是否保存停电引起的告警
     */
    private Integer isPowerOffSave;
    /**
     * 备注
     */
    private String note;

}

