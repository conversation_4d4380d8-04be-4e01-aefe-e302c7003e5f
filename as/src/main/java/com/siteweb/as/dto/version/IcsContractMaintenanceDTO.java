package com.siteweb.as.dto.version;

import com.siteweb.as.dto.PageBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IcsContractMaintenanceDTO extends PageBase {
    /** 合同编号 */
    private String contractNo;

    /** 项目名称 */
    private String projectName;

    /** 开始日期 */
    private String startDate;

    /** 结束日期 */
    private String endDate;

    /** 维护条款 */
    private String maintenanceTerms;
}
