package com.siteweb.as.dto.patrol;

import lombok.Data;

import java.util.List;


@Data
public class PatrolStandardGroupDTO {
    /**
     * 主键
     */
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 标准化设备id
     */
    private Integer equipmentLogicClassId;
    /**
     * 标准化设备名称
     */
    private String standardEquipmentName;

    /**
     * 备注
     */
    private String note;
    /**
     * 任务引用次数
     */
    private Long taskCount;

    /**
     * 标准化信号
     */
    private List<StandardSignalDTO> standardSignalList;

}

