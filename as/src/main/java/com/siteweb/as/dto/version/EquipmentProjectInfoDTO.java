package com.siteweb.as.dto.version;

import com.siteweb.as.dto.PageBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentProjectInfoDTO extends PageBase {
    private Integer stationId;
    private String stationName;
    private String houseName;
    private Integer equipmentId;
    private String equipmentName;
    private String projectName;
    private String contractNo;
    private String installTime;
}
