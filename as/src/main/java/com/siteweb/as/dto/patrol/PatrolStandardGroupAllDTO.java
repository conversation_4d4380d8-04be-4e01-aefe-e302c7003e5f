package com.siteweb.as.dto.patrol;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;


@Data
public class PatrolStandardGroupAllDTO {
    /**
     * 主键
     */
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 基类设备id
     */
    private Integer equipmentLogicClassId;
    /**
     * 基类设备名称
     */
    private String standardEquipmentName;

    /**
     * 基类信号id，多个用,分割
     */
    private String standardDicId;
    /**
     * 基类信号名称，多个用,分割，空则为'不选基类信号'
     */
    private String standardSignalName;
    /**
     * 备注
     */
    private String note;
    /**
     * 任务引用次数
     */
    private Long taskCount;

    public static PatrolStandardGroupAllDTO toPatrolGroupAllDTO(PatrolStandardGroupDTO patrolGroupDTO) {
        PatrolStandardGroupAllDTO patrolGroupAllDTO = new PatrolStandardGroupAllDTO();
        patrolGroupAllDTO.setGroupId(patrolGroupDTO.getGroupId());
        patrolGroupAllDTO.setGroupName(patrolGroupDTO.getGroupName());
        patrolGroupAllDTO.setEquipmentLogicClassId(patrolGroupDTO.getEquipmentLogicClassId());
        patrolGroupAllDTO.setStandardEquipmentName(CharSequenceUtil.isBlank(patrolGroupDTO.getStandardEquipmentName()) ? "不选标准化设备" : patrolGroupDTO.getStandardEquipmentName());
        patrolGroupAllDTO.setNote(patrolGroupDTO.getNote());
        patrolGroupAllDTO.setTaskCount(patrolGroupDTO.getTaskCount());
        List<StandardSignalDTO> standardSignalList = patrolGroupDTO.getStandardSignalList();
        if (CollUtil.isEmpty(standardSignalList)) {
            patrolGroupAllDTO.setStandardSignalName("不选标准化信号");
        } else {
            patrolGroupAllDTO.setStandardDicId(standardSignalList.stream()
                    .map(info -> info.getStandardDicId().toString())
                    .collect(Collectors.joining(",")));
            patrolGroupAllDTO.setStandardSignalName(standardSignalList.stream()
                    .map(StandardSignalDTO::getSignalStandardName)
                    .collect(Collectors.joining(",")));
        }

        return patrolGroupAllDTO;
    }

}

