package com.siteweb.as.dto;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class StationStructure {
    private String name;
    private Integer id;
    private Integer maxEventLevel = 0;
    private Integer maxEventSeverity = -1;
    private Integer connectState;
    private String parentNodeId;
    @JsonIgnore
    private Integer parentNodeId2;
    private Integer nodeType;
    private Integer projectState;
    private Double longitude = 0.0;
    private Double latitude = 0.0;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    List<StationStructure> children;

    /**
     * 递归更新父节点的 maxEventLevel
     */
    public void updateMaxEvent() {
        if (CollUtil.isEmpty(children)) {
            return;
        }
        for (StationStructure child : children) {
            // 递归更新子节点的 maxEvent
            child.updateMaxEvent();
            // 比较并更新当前节点的 maxEvent
            if (child.getMaxEventLevel() != 0 && (maxEventLevel == 0 || child.getMaxEventLevel() < this.maxEventLevel)) {
                this.maxEventSeverity = child.getMaxEventSeverity();
                this.maxEventLevel = child.getMaxEventLevel();
            }
        }
    }
}