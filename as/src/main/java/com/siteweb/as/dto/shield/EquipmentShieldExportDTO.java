package com.siteweb.as.dto.shield;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentShieldExportDTO {

    public EquipmentShieldExportDTO(EquipmentShieldDTO equipmentShieldDTO, LocaleMessageSourceUtil localeMessageSourceUtil) {
        this.stationName = equipmentShieldDTO.getStationName();
        this.stationId = equipmentShieldDTO.getStationId();
        this.equipmentId = equipmentShieldDTO.getEquipmentId();
        this.equipmentName = equipmentShieldDTO.getEquipmentName();
        String timeGroupCategoryName = "";
        if (Objects.equals(equipmentShieldDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.FULL_TIME_MASK.getValue()) || Objects.equals(equipmentShieldDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue())) {
            timeGroupCategoryName = localeMessageSourceUtil.getMessage("shield.maskMethod." + equipmentShieldDTO.getTimeGroupCategory());
        }
        this.timeGroupCategory = timeGroupCategoryName;
        this.startTime = equipmentShieldDTO.getStartTime();
        this.endTime = equipmentShieldDTO.getEndTime();
        this.userName = equipmentShieldDTO.getUserName();
        this.reason = equipmentShieldDTO.getReason();
    }
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private String timeGroupCategory;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人Id
     */
    private Integer userId;
    /**
     * 屏蔽人
     */
    private String userName;
    /**
     * 屏蔽原因
     */
    private String reason;
}
