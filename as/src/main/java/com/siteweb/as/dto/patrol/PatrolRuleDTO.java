package com.siteweb.as.dto.patrol;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;


@Data
public class PatrolRuleDTO {
    private Integer ruleId;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 预警范围
     */
    private Double limitDown;
    /**
     * 预警范围比较符号
     */
    private Integer limitDownCalOpId;
    private String limitDownCalOperator;
    /**
     * 巡检值
     */
    private Double limitUp;
    /**
     * 巡检值比较符号
     */
    private Integer limitUpCalOpId;
    private String limitUpCalOperator;
    /**
     * 单位字典值
     */
    private Integer unitId;
    /**
     * 单位
     */
    private String unitSymbol;
    /**
     * 预警等级
     */
    private Integer warningLevelId;
    /**
     * 预警等级
     */
    private String warningMeaning;
    /**
     * 是否按百分比
     */
    private Integer byPercentage;
    /**
     * 是否按百分比
     */
    private String byPercentageMeaning;
    /**
     * 额定值
     */
    private Double ratedValue;
    /**
     * 基类设备id
     */
    private Long baseEquipmentId;
    /**
     * 基类设备名称
     */
    private String baseEquipmentName;
    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 基类信号名称
     */
    private String baseTypeName;
    /**
     * 备注
     */
    private String note;
    /**
     * 描述
     */
    private String description;
    /**
     * 预警范围 根据limitDown和limitUp拼接而成
     */
    private String limitMeaning;
    /**
     * 分组引用次数
     */
    private Integer groupCount;
    /**
     * 标准化信息 standardDicId : signalStandardName
     */
    private String standardInfo;

    @JsonIgnore
    private List<StandardInfo> standardInfos;

    @Data
    public static class StandardInfo {
        private String standardDicId;
        private String signalStandardName;
    }

}

