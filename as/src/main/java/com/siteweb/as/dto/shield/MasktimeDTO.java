package com.siteweb.as.dto.shield;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MasktimeDTO {
    @JsonProperty("DataId")
    private int dataId;

    @JsonProperty("SequenceId")
    private int sequenceId;

    /**
     * 按日期全天屏蔽 1 按星期全天屏蔽 2 例外屏蔽事件 3
     */
    @JsonProperty("GroupCategory")
    private int groupCategory;

    @JsonProperty("Reason")
    private String reason;

    @JsonProperty("Type")
    private int type;

    @JsonProperty("Starttime")
    private Date starttime;

    @JsonProperty("Endtime")
    private Date endtime;

    @JsonProperty("TimeGroupSpans")
    private List<TimeGroupSpanDTO> timeGroupSpans;

    @JsonProperty("Exception")
    private boolean exception;

    @JsonProperty("UserId")
    private int userId;

    @JsonProperty("UserName")
    private String userName;

}
