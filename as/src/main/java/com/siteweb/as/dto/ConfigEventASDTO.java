package com.siteweb.as.dto;

import com.siteweb.monitoring.dto.ConfigEventDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
@Data
@NoArgsConstructor
public class ConfigEventASDTO {
    private String queryInformatoinId;
    List<configEvents> configEvents;

    @Data
    public static class configEvents {
        private String eventName;
        private String equipmentName;
        private String meaning;
        private String eventSeverity;
        private Integer eventLevel=0;
        private Date startTime;
        private Integer displayIndex;
        private Integer eventId;
        private boolean mask;
        public configEvents from(ConfigEventDTO configEvent) {
            setEventName(configEvent.getEventName());
            setEquipmentName(configEvent.getEquipmentName());
            setMeaning(configEvent.getMeaning());
            setEventSeverity(configEvent.getEventSeverity());
            if(configEvent.getEventLevel() != null)
                setEventLevel(configEvent.getEventLevel());
            setStartTime(configEvent.getStartTime());
            setDisplayIndex(configEvent.getDisplayIndex());
            setEventId(configEvent.getEventId());
            setMask(configEvent.getMask());
            return this;
        }
    }
}