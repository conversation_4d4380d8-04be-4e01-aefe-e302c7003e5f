package com.siteweb.as.dto.shield;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class TimeGroupSpanDTO {

    @JsonProperty("DataId")
    private int dataId;

    @JsonProperty("SequenceId")
    private int sequenceId;

    @JsonProperty("GroupId")
    private int groupId;

    @JsonProperty("StartTime")
    private Date startTime;

    @JsonProperty("EndTime")
    private Date endTime;

    @JsonProperty("TimeSpanBool")
    private List<Boolean> timeSpanBool;

    @JsonProperty("week")
    private Integer week;
}
