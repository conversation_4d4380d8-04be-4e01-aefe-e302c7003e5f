package com.siteweb.as.dto.shield;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventShieldExportDTO {
    public EventShieldExportDTO(EventShieldDTO eventShieldDTO, LocaleMessageSourceUtil localeMessageSourceUtil) {
        this.stationName = eventShieldDTO.getStationName();
        this.equipmentName = eventShieldDTO.getEquipmentName();
        this.eventName = eventShieldDTO.getEventName();
        String timeGroupCategoryName = "";
        if (Objects.equals(eventShieldDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.FULL_TIME_MASK.getValue()) || Objects.equals(eventShieldDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue()))
        {
            timeGroupCategoryName = localeMessageSourceUtil.getMessage("shield.maskMethod." + eventShieldDTO.getTimeGroupCategory());
        }
        this.timeGroupCategory = timeGroupCategoryName;
        this.startTime = eventShieldDTO.getStartTime();
        this.endTime = eventShieldDTO.getEndTime();
        this.userName = eventShieldDTO.getUserName();
        this.reason = eventShieldDTO.getReason();
    }

    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private String timeGroupCategory;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人
     */
    private String userName;
    /**
     * 屏蔽原因
     */
    private String reason;
}
