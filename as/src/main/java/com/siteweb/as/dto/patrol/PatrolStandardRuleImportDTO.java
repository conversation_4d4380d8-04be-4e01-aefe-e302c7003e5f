package com.siteweb.as.dto.patrol;

import com.siteweb.as.entity.PatrolRule;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Objects;


@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class PatrolStandardRuleImportDTO {
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 范围下限
     */
    private Double limitDown;
    /**
     * 下限运算符ID
     */
    private Integer limitDownCalOpId;
    /**
     * 下限运算符
     */
    private String limitDownCalOperator;
    /**
     * 范围上限
     */
    private Double limitUp;
    /**
     * 上限运算符ID
     */
    private Integer limitUpCalOpId;
    /**
     * 上限运算符
     */
    private String limitUpCalOperator;
    /**
     * 单位ID
     */
    private Integer unitId;
    /**
     * 单位
     */
    private String unitSymbol;
    /**
     * 预警等级ID
     */
    private Integer warningLevelId;
    /**
     * 预警等级
     */
    private String warningMeaning;
    /**
     * 按百分比
     */
    private Integer byPercentage;
    /**
     * 是否按百分比 是 1 否 0
     */
    private String byPercentageMeaning;
    /**
     * 额定值
     */
    private Double ratedValue;
    /**
     * 标准化设备ID
     */
    private Integer equipmentLogicClassId;
    /**
     * 标准化设备名称
     */
    private String standardEquipmentName;
    /**
     * 标准化信号ID
     */
    private Integer standardDicId;
    /**
     * 标准化信号名称
     */
    private String standardSignalName;
    /**
     * 备注
     */
    private String note;
    /**
     * 描述
     */
    private String description;

    public PatrolRule toPatrolRule() {
        PatrolRule patrolRule = new PatrolRule();
        patrolRule.setRuleName(this.ruleName);
        patrolRule.setLimitDown(this.limitDown);
        patrolRule.setLimitUp(this.limitUp);
        patrolRule.setByPercentage(Objects.equals(this.byPercentageMeaning, "是") ? 1 : 0);
        patrolRule.setRatedValue(this.ratedValue);
        patrolRule.setNote(this.note);
        patrolRule.setDescription(this.description);
        return patrolRule;
    }
}

