package com.siteweb.as.dto;

import cn.hutool.json.JSONUtil;
import com.siteweb.as.entity.HistorySelection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventComplexFilterDto {
    public EventComplexFilterDto(HistorySelection o) {
        this.id = o.getHistorySelectionId();
        this.name = o.getSelectionName();
        this.description = o.getDescription();
        this.createTime = o.getCreateTime();
        this.selection = JSONUtil.xmlToJson(o.getSelectionContent()).toString();
        this.filterType = o.getSelectionType();
        this.userId = o.getUserId();
        this.queryInformation = o.getQueryInformation();
        this.currentUserId = o.getUserId();
    }

    private Integer id;
    private String name;
    private String description;
    private Date createTime;
    private Integer userId;
    private Integer currentUserId;
    private String filterType;
    private String selection;
    private String queryInformation;
}