package com.siteweb.as.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * as屏蔽过滤的条件dto
 * <AUTHOR>
 * @date 2024/10/28
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShieldFilterDTO extends PageBase {
    /**
     * 局站分组方式
     */
    private Integer stationGroupType;
    /**
     * 分组名称
     */
    private List<Integer> stationStructureList;
    /**
     * 局站ids
     */
    private Collection<Integer> stationIdList;
    /**
     * 设备ids
     */
    private Collection<Integer> equipmentIdList;
    /**
     * 局站类型
     */
    private List<Integer> stationCategoryList;
    /**
     * 设备类型
     */
    private List<Integer> equipmentCategoryList;
    /**
     * 事件种类 entryId = 24
     */
    private List<Integer> eventCategoryList;
    /**
     * 事件名称  模糊查询
     */
    private String eventName;
    /**
     * 屏蔽方式 1全时段  2分时段  3所有
     */
    private Integer shieldMethod;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
}
