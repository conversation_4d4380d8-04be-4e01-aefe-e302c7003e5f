package com.siteweb.as.dto.config;

import lombok.Data;

/**
 * 监控单元
 */
@Data
public class ConfigSamplerUnitDTO {
    /**
     * 站点ID。
     */
    private Long stationId;

    /**
     * 监控单元ID。
     */
    private Long monitorUnitId;

    /**
     * 采样器ID。
     */
    private Long samplerId;

    /**
     * 采样单元ID。
     */
    private Long samplerUnitId;

    /**
     * 端口ID。
     */
    private Long portId;

    /**
     * 端口号。
     */
    private Integer portNo;

    /**
     * 端口类型。
     */
    private Integer portType;

    /**
     * 端口名称。
     */
    private String portName;

    /**
     * 通信设置。
     */
    private String setting;

    /**
     * 采样单元名称。
     */
    private String samplerUnitName;

    /**
     * 采样器类型。
     */
    private Integer samplerType;

    /**
     * 地址。
     */
    private Integer address;

    /**
     * 动态链接库路径。
     */
    private String dllPath;

    /**
     * 连接状态。
     */
    private Integer connectState;

    /**
     * 电话号码。
     */
    private String phoneNumber;

    /**
     * 描述信息。
     */
    private String description;
}
