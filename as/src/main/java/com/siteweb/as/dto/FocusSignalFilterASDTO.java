package com.siteweb.as.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description BatchEventMaskVO
 * @createTime 2022-05-17 10:22:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class FocusSignalFilterASDTO extends PageBase {

    /**
     * 局站分组方式
     */
    private Integer stationGroupType;
    /**
     * 分组名称
     */
    private List<Integer> stationStructureList;
    /**
     * 局站类型
     */
    private List<Integer> stationCategoryList;
    /**
     * 局站状态
     */
    private List<Integer> stationStateList;
    /**
     * 局站ids
     */
    private Collection<Integer> stationIdList;
    /**
     * 设备类型
     */
    private Set<Integer> equipmentCategoryList;
    /**
     * 设备ids
     */
    private Set<Integer> equipmentIdList;
    /**
     * 信号种类 entryId = 17
     */
    private Set<Integer> signalCategoryList;
    /**
     * 信号基类
     */
    private Collection<Integer> baseTypeIdList;
    /**
     * 信号名称 模糊查询
     */
    private String signalName;
    /**
     * 信号值范围1
     */
    private String compareValue1Str;
    /**
     * 信号值范围2
     */
    private String compareValue2Str;
}
