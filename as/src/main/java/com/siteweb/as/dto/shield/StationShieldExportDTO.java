package com.siteweb.as.dto.shield;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class StationShieldExportDTO {
    public StationShieldExportDTO(StationShieldDTO stationShieldDTO, LocaleMessageSourceUtil localeMessageSourceUtil) {
        this.centerName = stationShieldDTO.getCenterName();
        this.stationName = stationShieldDTO.getStationName();
        this.timeGroupId = stationShieldDTO.getTimeGroupId();
        this.stationId = stationShieldDTO.getStationId();
        String timeGroupCategoryName = "";
        if (Objects.equals(stationShieldDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.FULL_TIME_MASK.getValue()) || Objects.equals(stationShieldDTO.getTimeGroupCategory(), TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue())) {
            timeGroupCategoryName = localeMessageSourceUtil.getMessage("shield.maskMethod." + stationShieldDTO.getTimeGroupCategory());
        }
        this.timeGroupCategory = timeGroupCategoryName;
        this.startTime = stationShieldDTO.getStartTime();
        this.endTime = stationShieldDTO.getEndTime();
        this.userName = stationShieldDTO.getUserName();
        this.reason = stationShieldDTO.getReason();
    }

    /**
     * 中心名称
     */
    private String centerName;
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 时间组id
     */
    private Integer timeGroupId;
    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private String timeGroupCategory;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人
     */
    private String userName;
    /**
     * 屏蔽原因
     */
    private String reason;
}
