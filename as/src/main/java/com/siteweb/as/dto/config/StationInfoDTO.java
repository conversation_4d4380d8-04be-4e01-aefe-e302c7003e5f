package com.siteweb.as.dto.config;

import lombok.Data;

import java.util.Date;

@Data
public class StationInfoDTO {
    private Long centerId;
    private String centerName;
    private Long groupId;
    private String groupName;
    private Integer stationId;
    private String stationName;
    private Double latitude;
    private Double longitude;
    private Date setupTime;
    private Long companyId;
    private Integer connectState;
    private Date updateTime;
    private Integer stationCategory;
    private Integer stationGrade;
    private Integer stationState;
    private Long contactId;
    private Integer supportTime;
    private Integer onWayTime;
    private Integer surplusTime;
    private Integer floorNo;
    private String propList;
    private Double acreage;
    private Integer buildingType;
    private String containNode;
    private String description;
    private Integer bordNumber;
    private Boolean enable;
    private Date startTime;
    private Date endTime;

    private String stationGradeName;
    private String stationGradeAlias;
    private String stationGradeImage;
    private String stationCategoryName;
    private String stationCategoryAlias;
    private String stationCategoryImage;
    private String stationStateName;
    private String stationStateAlias;
    private String stationStateImage;
    private String buildingTypeName;
    private String buildingTypeAlias;
    private String buildingTypeImage;

    private String reason;
}
