package com.siteweb.as.dto.patrol;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;


@Data
public class PatrolGroupAllDTO {
    /**
     * 主键
     */
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 基类设备id
     */
    private Integer baseEquipmentId;
    /**
     * 基类设备名称
     */
    private String baseEquipmentName;

    /**
     * 基类信号id，多个用,分割
     */
    private String baseTypeId;
    /**
     * 基类信号名称，多个用,分割，空则为'不选基类信号'
     */
    private String baseTypeName;
    /**
     * 备注
     */
    private String note;
    /**
     * 任务引用次数
     */
    private Long taskCount;

    public static PatrolGroupAllDTO toPatrolGroupAllDTO(PatrolGroupDTO patrolGroupDTO) {
        PatrolGroupAllDTO patrolGroupAllDTO = new PatrolGroupAllDTO();
        patrolGroupAllDTO.setGroupId(patrolGroupDTO.getGroupId());
        patrolGroupAllDTO.setGroupName(patrolGroupDTO.getGroupName());
        patrolGroupAllDTO.setBaseEquipmentId(patrolGroupDTO.getBaseEquipmentId());
        patrolGroupAllDTO.setBaseEquipmentName(CharSequenceUtil.isBlank(patrolGroupDTO.getBaseEquipmentName()) ? "不选基类设备" : patrolGroupDTO.getBaseEquipmentName());
        patrolGroupAllDTO.setNote(patrolGroupDTO.getNote());
        patrolGroupAllDTO.setTaskCount(patrolGroupDTO.getTaskCount());
        List<BaseSignalTypeInfo> baseSignalList = patrolGroupDTO.getBaseSignalList();
        if (CollUtil.isEmpty(baseSignalList)) {
            patrolGroupAllDTO.setBaseTypeName("不选基类信号");
        } else {
            patrolGroupAllDTO.setBaseTypeId(baseSignalList.stream()
                    .map(info -> info.getBaseTypeId().toString())
                    .collect(Collectors.joining(",")));
            patrolGroupAllDTO.setBaseTypeName(baseSignalList.stream()
                    .map(BaseSignalTypeInfo::getBaseTypeName)
                    .collect(Collectors.joining(",")));
        }

        return patrolGroupAllDTO;
    }

}

