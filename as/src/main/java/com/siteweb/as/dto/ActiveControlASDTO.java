package com.siteweb.as.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActiveControlASDTO {
    private Integer id;
    private String name;
    private String controlType;
    private String standardTypeName;
    private String setValue;
    private Integer result;
    private String userName;
    private Boolean isExcuing;
    private Integer sequenceId;
    private Date startTime;
    private String centerName;
    private String stationName;
    private String equipmentName;
}
