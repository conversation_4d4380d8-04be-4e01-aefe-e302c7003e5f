package com.siteweb.as.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

@Data
public class PageBase {
    private Integer size = 10;
    private Integer current = 1;
    private String orderField;
    private String orderDirection;

    public <E> Page<E> generatePaginationInfo() {
        Page<E> objectPage = Page.of(this.current, this.size);
        objectPage.setOptimizeCountSql(false);
        return objectPage;
    }
}
