package com.siteweb.as.dto;

import com.siteweb.monitoring.dto.ConfigControlItem;
import com.siteweb.monitoring.entity.ControlMeanings;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.service.SignalBaseDicService;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ConfigCtrolAS {



    private Integer controlId;
    private String controlName;
    private Long baseTypeId = 0L;
    private String standardTypeName="";
    private Integer standardTypeId=0;
    private Integer commandCategory;
    private String relativeSignalValue="0";
    private Double minValue;
    private Double maxValue;
    private Integer commandType;
    private String configSignalUnit="";
    List<Parameters> parameters = new ArrayList<>();

    public void from(ConfigControlItem configControl) {
        setControlId(configControl.getControlId());
        setControlName(configControl.getControlName());
        if(configControl.getBaseTypeId() != null)
            setBaseTypeId(configControl.getBaseTypeId());
        setCommandCategory(1);
        setCommandType(configControl.getCommandType());
        if(configControl.getCurrentValue() != null)
            setRelativeSignalValue(configControl.getCurrentValue());
        setMinValue(configControl.getMinValue());
        setMaxValue(configControl.getMaxValue());
        for (ControlMeanings mean : configControl.getControlMeaningsList()) {
            if(mean.getParameterValue() != null) {
                Parameters par = new Parameters();
                par.setMeanings(mean.getMeanings());
                par.setParameterValue(mean.getParameterValue().doubleValue());
                parameters.add(par);
            }
        }
        setConfigSignalUnit(configControl.getUnit());
    }

    @Data
    public static class Parameters {
        private Double parameterValue;
        private String meanings;
    }
}