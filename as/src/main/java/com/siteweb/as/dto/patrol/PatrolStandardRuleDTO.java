package com.siteweb.as.dto.patrol;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * @projectName: siteweb6-server
 * @package: com.siteweb.as.dto.patrol
 * @className: PatrolStandardRuleDTO
 * @author: baller_chen
 * @description: 标准化规则
 * @date: 2025/4/18 17:39
 * @version: 1.0
 */
@Data
public class PatrolStandardRuleDTO {
    private Integer ruleId;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 预警范围
     */
    private Double limitDown;
    /**
     * 预警范围比较符号
     */
    private Integer limitDownCalOpId;
    private String limitDownCalOperator;
    /**
     * 巡检值
     */
    private Double limitUp;
    /**
     * 巡检值比较符号
     */
    private Integer limitUpCalOpId;
    private String limitUpCalOperator;
    /**
     * 单位字典值
     */
    private Integer unitId;
    /**
     * 单位
     */
    private String unitSymbol;
    /**
     * 预警等级
     */
    private Integer warningLevelId;
    /**
     * 预警等级
     */
    private String warningMeaning;
    /**
     * 是否按百分比
     */
    private Integer byPercentage;
    /**
     * 是否按百分比
     */
    private String byPercentageMeaning;
    /**
     * 额定值
     */
    private Double ratedValue;
    /**
     * 标准化设备id
     */
    private Long equipmentLogicClassId;
    /**
     * 标准化设备名称
     */
    private String standardEquipmentName;
    /**
     * 标准化id
     */
    private Long standardDicId;
    /**
     * 标准化信号名称
     */
    private String standardSignalName;
    /**
     * 备注
     */
    private String note;
    /**
     * 描述
     */
    private String description;
    /**
     * 预警范围 根据limitDown和limitUp拼接而成
     */
    private String limitMeaning;
    /**
     * 分组引用次数
     */
    private Integer groupCount;



}
