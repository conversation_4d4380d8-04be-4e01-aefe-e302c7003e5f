package com.siteweb.as.dto.config;

import com.siteweb.monitoring.entity.MonitorUnit;
import lombok.Data;

/**
 * 监控单元信息
 * Creation Date: 2024/12/23
 */
@Data
public class ConfigMonitorUnitDTO {
    /**
     * 监控单元ID。
     */
    private Integer monitorUnitId;

    /**
     * 监控单元名称。
     */
    private String monitorUnitName;

    /**
     * IP地址。
     */
    private String ipAddress;

    /**
     * 描述信息。
     */
    private String description;

    /**
     * 监控单元编码。
     */
    private String monitorUnitCode;

    /**
     * 采样配置编码。
     */
    private String sampleConfigCode;

    /**
     * 配置文件编码。
     */
    private String configFileCode;

    /**
     * 工作站ID。
     */
    private Integer workStationId;

    /**
     * 站点ID。
     */
    private Integer stationId;

    /**
     * 监控单元类别。
     */
    private Integer monitorUnitCategory;

    public static ConfigMonitorUnitDTO toConfigMonitorUnitDTO(MonitorUnit monitorUnit) {
        ConfigMonitorUnitDTO configMonitorUnitDTO = new ConfigMonitorUnitDTO();
        configMonitorUnitDTO.setMonitorUnitId(monitorUnit.getMonitorUnitId());
        configMonitorUnitDTO.setMonitorUnitName(monitorUnit.getMonitorUnitName());
        configMonitorUnitDTO.setIpAddress(monitorUnit.getIpAddress());
        configMonitorUnitDTO.setDescription(monitorUnit.getDescription());
        configMonitorUnitDTO.setMonitorUnitCode(monitorUnit.getMonitorUnitCode());
        configMonitorUnitDTO.setSampleConfigCode(monitorUnit.getSampleConfigCode());
        configMonitorUnitDTO.setConfigFileCode(monitorUnit.getConfigFileCode());
        configMonitorUnitDTO.setWorkStationId(monitorUnit.getWorkStationId());
        configMonitorUnitDTO.setStationId(monitorUnit.getStationId());
        configMonitorUnitDTO.setMonitorUnitCategory(monitorUnit.getMonitorUnitCategory());
        return configMonitorUnitDTO;
    }
}
