package com.siteweb.as.dto.patrol;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.as.entity.PatrolAllRecord;
import com.siteweb.as.entity.PatrolExrecord;
import lombok.Data;

import java.util.Date;

@Data
public class PatrolSignalDTO {

    private Integer centerId;
    private String centerName;
    private Integer groupId;
    private String groupName;
    private Integer stationId;
    private String stationName;
    private Integer stationCategory;
    private Integer houseId;
    private String houseName;
    private Integer equipmentTemplateId;
    private Integer equipmentId;
    private String equipmentName;
    private String description;
    private Integer equipmentCategory;
    private String equipmentCategoryName;
    private Integer equipmentBaseTypeId;
    private Integer signalId;
    private String signalName;
    private String signalValue;
    private Date recordTime;
    private String unit;
    private Integer signalCategory;
    private Integer signalType;
    private Integer dataType;
    private Long baseTypeId;

    private Double limitDown;
    private Integer limitDownCalOpId;
    private Double limitUp;
    private Integer limitUpCalOpId;
    private Integer byPercentage;
    private Double ratedValue;
    private Integer warningLevelId;
    /**
     * 默认合理 (1:合理，0:不合理)
     */
    private Integer reasonable;
    private String isPowerOffAlarm;
    private Date createTime;
    /**
     * 是否存库 (1: 存，0:不存)
     */
    private Integer isSave;
    private String sn;

    @JsonIgnore
    public PatrolExrecord convertExrecord() {
        PatrolExrecord patrolExrecord = new PatrolExrecord();
        patrolExrecord.setCenterId(this.getCenterId());
        patrolExrecord.setCenterName(this.getCenterName());
        patrolExrecord.setGroupId(this.getGroupId());
        patrolExrecord.setGroupName(this.getGroupName());
        patrolExrecord.setStationId(this.getStationId());
        patrolExrecord.setStationName(this.getStationName());
        patrolExrecord.setEquipmentCategoryName(this.getEquipmentCategoryName());
        patrolExrecord.setEquipmentId(this.getEquipmentId());
        patrolExrecord.setEquipmentName(this.getEquipmentName());
        patrolExrecord.setSignalId(this.getSignalId());
        patrolExrecord.setSignalName(this.getSignalName());
        patrolExrecord.setSignalValue(this.getSignalValue());
        patrolExrecord.setRecordTime(this.getRecordTime());
        patrolExrecord.setUnit(this.getUnit());
        patrolExrecord.setLimitDown(this.getLimitDown());
        patrolExrecord.setLimitDownCalOpId(this.getLimitDownCalOpId());
        patrolExrecord.setLimitUp(this.getLimitUp());
        patrolExrecord.setLimitUpCalOpId(this.getLimitUpCalOpId());
        patrolExrecord.setByPercentage(this.getByPercentage());
        patrolExrecord.setRatedValue(this.getRatedValue());
        patrolExrecord.setWarningLevelId(this.getWarningLevelId());
        patrolExrecord.setReasonable(this.getReasonable());
        patrolExrecord.setIsPowerOffAlarm(this.getIsPowerOffAlarm());
        patrolExrecord.setCreateTime(this.getCreateTime());
        patrolExrecord.setSn(this.getSn());
        return patrolExrecord;
    }

    @JsonIgnore
    public PatrolAllRecord convertAllrecord() {
        PatrolAllRecord patrolAllRecord = new PatrolAllRecord();
        patrolAllRecord.setCenterId(this.getCenterId());
        patrolAllRecord.setCenterName(this.getCenterName());
        patrolAllRecord.setGroupId(this.getGroupId());
        patrolAllRecord.setGroupName(this.getGroupName());
        patrolAllRecord.setStationId(this.getStationId());
        patrolAllRecord.setStationName(this.getStationName());
        patrolAllRecord.setEquipmentCategoryName(this.getEquipmentCategoryName());
        patrolAllRecord.setEquipmentId(this.getEquipmentId());
        patrolAllRecord.setEquipmentName(this.getEquipmentName());
        patrolAllRecord.setSignalId(this.getSignalId());
        patrolAllRecord.setSignalName(this.getSignalName());
        patrolAllRecord.setSignalValue(this.getSignalValue());
        patrolAllRecord.setRecordTime(this.getRecordTime());
        patrolAllRecord.setUnit(this.getUnit());
        patrolAllRecord.setLimitDown(this.getLimitDown());
        patrolAllRecord.setLimitDownCalOpId(this.getLimitDownCalOpId());
        patrolAllRecord.setLimitUp(this.getLimitUp());
        patrolAllRecord.setLimitUpCalOpId(this.getLimitUpCalOpId());
        patrolAllRecord.setByPercentage(this.getByPercentage());
        patrolAllRecord.setRatedValue(this.getRatedValue());
        patrolAllRecord.setWarningLevelId(this.getWarningLevelId());
        patrolAllRecord.setReasonable(this.getReasonable());
        patrolAllRecord.setIsPowerOffAlarm(this.getIsPowerOffAlarm());
        patrolAllRecord.setCreateTime(this.getCreateTime());
        patrolAllRecord.setSn(this.getSn());
        return patrolAllRecord;
    }
}
