package com.siteweb.as.dto.realtime;

import com.siteweb.monitoring.dto.PowerOffStationDetail;
import lombok.Data;

import java.util.Date;

/**
 * @Description:
 */
@Data
public class PowerOffDTO {

    /**
     * 局站Id
     */
    private  Integer stationId;
    /**
     * 局站名称
     */
    private  String stationName;
    /**
     * 中心名称
     */
    private  String centerName;
    /**
     * 分组名称
     */
    private  String defaultStationGroupName;
    /**
     * 设备名
     */
    private  String equipmentName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 开始时间
     */
    private Date createTime;
    /**
     * 持续时间
     */
    private  Integer duration;
    /**
     * 持续时间
     */
    private  String durTime;

    public PowerOffDTO(PowerOffStationDetail detail){
        if (detail.getDuration() != null){
            if (detail.getDuration() > 60) {
                this.durTime =  detail.getDuration() / 60 + "h" + detail.getDuration() % 60 + "min";
            } else {
                this.durTime =  detail.getDuration() + "min";
            }
        }
        this.stationId = detail.getStationId();
        this.centerName = detail.getCenterName();
        this.stationName = detail.getStationName();
        this.defaultStationGroupName = detail.getGroupName();
        this.equipmentName = detail.getEquipmentName();
        this.eventName = detail.getEventName();
        this.createTime = detail.getStartTime();
    }
}
