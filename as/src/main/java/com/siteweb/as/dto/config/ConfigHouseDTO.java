package com.siteweb.as.dto.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ConfigHouseDTO {
    @JsonProperty("stationId")
    private Long stationId;

    @JsonProperty("stationName")
    private String stationName;

    @JsonProperty("houseId")
    private Integer houseId;

    @JsonProperty("houseName")
    private String houseName;

    @JsonProperty("description")
    private String description;

    @JsonProperty("lastUpdateDate")
    private Date lastUpdateDate;
}
