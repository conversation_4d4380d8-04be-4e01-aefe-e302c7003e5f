package com.siteweb.as.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor

public class EquipmentTreeDTO {

    private boolean visible=true;
    private Integer status=1;
    private Integer projectState=0;
    private Integer objectType;
    private String parentNodeId;
    private String nodeId;
    private String uniqueId;
    private Integer objectId;
    private String name;
    private String initialName;
    private String icon;
    private Integer state=0;
    private Integer maxEventSeverity=-1;
    private Integer isBInterface=0;
    private Integer maxEventLevel=0;
}