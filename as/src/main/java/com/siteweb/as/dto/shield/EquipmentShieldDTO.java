package com.siteweb.as.dto.shield;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentShieldDTO {
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 屏蔽时间段类型：1为全时段屏蔽，2为分时段屏蔽
     */
    private Integer timeGroupCategory;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 屏蔽人Id
     */
    private Integer userId;
    /**
     * 屏蔽人
     */
    private String userName;
    /**
     * 屏蔽原因
     */
    private String reason;
}
