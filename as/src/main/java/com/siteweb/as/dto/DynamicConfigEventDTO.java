package com.siteweb.as.dto;

import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class DynamicConfigEventDTO {
    private Integer displayIndex;
    private Integer startType;
    private Integer endType;
    private String startExpression;
    private String suppressExpression;
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;

    //        private Integer channelNo;
    private Integer monitoringUnitId;
    //        private Integer reversal;
    private Integer eventCategory;
    private List<Condition> conditions;
    //        private Integer operationType;
//        private Integer monitorUnitId;
//        private Integer centerId;
//        private Integer stationGradeId;
//        private Integer stationCategoryId;
//        private Integer stationStateId;
//        private Integer configSourceType;
//        private String centerName;
//        private String defaultStationGroupName;
    private Integer stationId;
    //        private String stationName;
//        private String stationCategoryName;
//        private Any stationGradeName;
    private Integer equipmentId;
    //        private String equipmentName;
//        private Integer venderId;
//        private Map<String, Any> listChanged;
//        private Any imageUrl;
//        private Any orginalUniqueId;
    private Boolean visible;
    private Boolean enable;
    //        private String description;
//        private String initialName;
//        private Integer objectType;
//        private String tag;
    private String createTime;
    private Date updateTime;
    private Date deleteTime;
    //        private String uniqueId;
    private Integer objectId;
    private String name;
//        private Integer status;

    @Data
    public static class Condition {
        //        private Integer entityState;
//        private String name;
//        private Integer standardNameId;
        private Integer eventConditionId;
        private Integer eventSeverity;
        private String meanings;
        private String startOperation;
        private Double startCompareValue;
        private Integer startDelay;
        private String endOperation;
        private Double endCompareValue;
        private Integer endDelay;

        //        private Any frequency;
//        private Any frequencyThreshold;
//        private Integer baseTypeId;
        public void from(EventConditionDTO conDto) {
            setEventConditionId(conDto.getEventConditionId());
            setStartOperation(conDto.getStartOperation());
            setStartCompareValue(conDto.getStartCompareValue());
            setStartDelay(conDto.getStartDelay());
            setEndDelay(conDto.getEndDelay());
            setEventSeverity(conDto.getEventSeverity());
            setMeanings(conDto.getMeanings());
            setEndOperation(conDto.getEndOperation());
            setEndCompareValue(conDto.getEndCompareValue());
        }
        public void to(EventConditionDTO conDto) {
            conDto.setEventConditionId(getEventConditionId());
            conDto.setStartOperation(getStartOperation());
            conDto.setStartCompareValue(getStartCompareValue());
            conDto.setStartDelay(getStartDelay());
            conDto.setEndDelay(getEndDelay());
            conDto.setEventSeverity(getEventSeverity());
            conDto.setMeanings(getMeanings());
            conDto.setEndOperation(getEndOperation());
            conDto.setEndCompareValue(getEndCompareValue());
        }       
    }
    public void from(ConfigEventDTO cfgEvent){
        setObjectId(cfgEvent.getEventId());
        setName(cfgEvent.getEventName());
        setDisplayIndex(cfgEvent.getDisplayIndex());
        setMonitoringUnitId(cfgEvent.getMonitorUnitId());
        setEventCategory(cfgEvent.getEventCategory());
        setStartType(cfgEvent.getStartType());

        setEndType(cfgEvent.getEndType());
        setStartExpression(cfgEvent.getStartExpression());
        setSuppressExpression(cfgEvent.getSuppressExpression());
        setVisible(cfgEvent.getVisible());
        setEnable(cfgEvent.getEnable());
        setStationId(cfgEvent.getStationId());
        setEquipmentId(cfgEvent.getEquipmentId());

        List<Condition> conditions = new ArrayList<>();
        for(EventConditionDTO conDto:cfgEvent.getEventConditions()){
            Condition con = new Condition();
            con.from(conDto);
            conditions.add(con);
        }
        setConditions(conditions);    
    }
    public void to(ConfigEventDTO cfgEvent){
        cfgEvent.setEventId(getObjectId());
        cfgEvent.setEventName(getName());
        cfgEvent.setDisplayIndex(getDisplayIndex());
        cfgEvent.setMonitorUnitId(getMonitoringUnitId());
        cfgEvent.setEventCategory(getEventCategory());
        cfgEvent.setStartType(getStartType());

        cfgEvent.setEndType(getEndType());
        cfgEvent.setStartExpression(getStartExpression());
        cfgEvent.setSuppressExpression(getSuppressExpression());
        cfgEvent.setVisible(getVisible());
        cfgEvent.setEnable(getEnable());
        cfgEvent.setStationId(getStationId());
        cfgEvent.setEquipmentId(getEquipmentId());

        List<EventConditionDTO> conditions = new ArrayList<>();
        for(Condition conDto:getConditions()){
            EventConditionDTO con = new EventConditionDTO();
            conDto.to(con);
            con.setEquipmentTemplateId(getEquipmentTemplateId());
            con.setEquipmentId(getEquipmentId());
            con.setEventId(getObjectId());
            conditions.add(con);
        }
        cfgEvent.setEventConditions(conditions);
    }  
}