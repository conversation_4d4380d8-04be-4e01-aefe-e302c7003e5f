package com.siteweb.as.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description FocusSignalDTO
 * @createTime 2022-08-05 15:31:58
 */
@Data
@NoArgsConstructor
public class FocusSignalASDTO {
    /**
     * 中心名称
     */
    private String centerName;
    /**
     * 分组名称
     */
    private String stationStructure;
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 信号名称
     */
    private String signalName;
    /**
     * 当前值
     */
    private String currentValue;
    /**
     * 原始值
     */
    private Double value;
    /**
     * 单位
     */
    private String signalUnit;
    /**
     * 采集时间
     */
    private String sampleTime;
}
