package com.siteweb.as.dto.patrol;

import com.siteweb.monitoring.entity.ActiveEvent;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Data
public class StationEventRecordDTO {
    private Integer centerId;
    private String centerName;
    private Integer groupId;
    private String groupName;
    private Integer stationId;
    private String stationName;
    private Integer equipmentId;
    private String equipmentName;
    private Date startTime; // 告警开始时间
    private Date endTime;   // 告警结束时间
    private Double duringTime;   // 告警持续时间

    public static StationEventRecordDTO convert(ActiveEvent activeEvent, ChronoUnit chronoUnit) {
        StationEventRecordDTO stationEventRecordDTO = new StationEventRecordDTO();
        stationEventRecordDTO.setCenterId(activeEvent.getCenterId());
        stationEventRecordDTO.setCenterName(activeEvent.getCenterName());
        stationEventRecordDTO.setGroupId(activeEvent.getStructureId());
        stationEventRecordDTO.setGroupName(activeEvent.getStructureName());
        stationEventRecordDTO.setStationId(activeEvent.getStationId());
        stationEventRecordDTO.setStationName(activeEvent.getStationName());
        stationEventRecordDTO.setEquipmentId(activeEvent.getEquipmentId());
        stationEventRecordDTO.setEquipmentName(activeEvent.getEquipmentName());
        stationEventRecordDTO.setStartTime(activeEvent.getStartTime());
        stationEventRecordDTO.setEndTime(activeEvent.getEndTime());
        stationEventRecordDTO.setDuringTime(calculateDuringTime(activeEvent.getStartTime(), activeEvent.getEndTime(),chronoUnit));
        return stationEventRecordDTO;
    }

    public static double calculateDuringTime(Date startTime, Date endTime, ChronoUnit chronoUnit) {
        if (startTime == null) {
            return 0.0;
        }
        // 将 Date 转换为 LocalDateTime
        LocalDateTime startLocalDateTime = LocalDateTime.ofInstant(startTime.toInstant(), ZoneId.systemDefault());
        LocalDateTime endLocalDateTime = (endTime != null) ? LocalDateTime.ofInstant(endTime.toInstant(), ZoneId.systemDefault()) : null;
        if (endLocalDateTime != null && startLocalDateTime.isAfter(endLocalDateTime)) {
            return 0.0;
        }
        LocalDateTime referenceEndTime = (endLocalDateTime == null) ? LocalDateTime.now() : endLocalDateTime;
        // 根据单位计算时间差
        return switch (chronoUnit) {
            case SECONDS -> {
                long seconds = startLocalDateTime.until(referenceEndTime, ChronoUnit.SECONDS);
                yield Math.round((seconds / 60.0) * 100.0) / 100.0;
            }
            case MINUTES -> startLocalDateTime.until(referenceEndTime, ChronoUnit.MINUTES);
            default -> throw new IllegalArgumentException("Unsupported TemporalUnit. Use SECONDS or MINUTES.");
        };

    }

}
