package com.siteweb.as.dto.patrol;

import lombok.Data;

import java.util.List;


@Data
public class PatrolGroupDTO {
    /**
     * 主键
     */
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 基类设备id
     */
    private Integer baseEquipmentId;
    /**
     * 基类设备名称
     */
    private String baseEquipmentName;

    /**
     * 备注
     */
    private String note;
    /**
     * 任务引用次数
     */
    private Long taskCount;

    /**
     * 基类信号
     */
    private List<BaseSignalTypeInfo> baseSignalList;

}

