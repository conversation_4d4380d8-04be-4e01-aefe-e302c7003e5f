package com.siteweb.as.dto.version;

import com.siteweb.as.dto.PageBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class IcsPlatformQrcodeDTO extends PageBase {
    private String equipmentTypeName;
    private String serialNumber;
    private String equipmentName;
    private Date createDate;
    private String operatorName;
}
