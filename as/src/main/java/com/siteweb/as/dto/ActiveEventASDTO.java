package com.siteweb.as.dto;

import com.siteweb.monitoring.entity.ActiveEvent;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> z<PERSON>
 * @description ActiveEventDTO
 * @createTime 2022-04-11 14:39:54
 */
@Data
@NoArgsConstructor
public class ActiveEventASDTO {
    public ActiveEventASDTO(ActiveEvent activeEvent) {
        setEventSeverity(4 - activeEvent.getEventLevel());
        setSeverityName(activeEvent.getEventSeverity());
        setIsConfirmed(activeEvent.getConfirmTime() != null);

        setCenterName(activeEvent.getCenterName());              //四川移动2024中心
        setDefaultStationGroupName(activeEvent.getStructureName()); // "成都市"
        setStationName(activeEvent.getStationName());             //"春熙路
        //        setStationStateName(activeEvent.get());       //"联网状态"
        //        setStationTypeName(activeEvent.getBaseTypeName());     //"基站"
        setMonitoringUnitName(activeEvent.getMonitorUnitName());      //"春熙路-1#",
        setEquipmentName(activeEvent.getEquipmentName());
        //        setEventCategoryName(activeEvent.getev());      //设备事件
        setName(activeEvent.getEventName());    //告警名称
        setStandardTypeName(activeEvent.getStandardAlarmName());        //事件基类名称
        setMeaning(activeEvent.getMeanings());     //告警含义
        setOverturn(activeEvent.getReversalNum());   //翻转次数
        setStartTime(activeEvent.getStartTime());     //开始时间
        setConfirmTime(activeEvent.getConfirmTime());   //确认时间
        setEndTime(activeEvent.getEndTime());       //结束时间
        setInstructionId(activeEvent.getInstructionId());
        setTriggerValue(activeEvent.getEventValue().toString());        //触发值
        setConfirmUserName(activeEvent.getConfirmerName());
        setMemo(activeEvent.getDescription());
        setUniqueId(activeEvent.getSequenceId());    //流水号
        setStatus(activeEvent.getMaintainState());
        setStationId(activeEvent.getStationId());//局站ID
        setEquipmentId(activeEvent.getEquipmentId());    //设备ID
        setEventId(activeEvent.getEventId());    //事件ID
        setBaseTypeId(activeEvent.getBaseTypeId());    //事件基类ID
        setEventLevel(activeEvent.getEventLevel());
        setStandardAlarmNameId(activeEvent.getStandardAlarmNameId());
    }

    private Integer eventSeverity;      //告警等级ID
    private String severityName;        //告警等级名称
    Boolean isConfirmed;
    private String centerName;              //四川移动2024中心
    private String defaultStationGroupName; // "成都市"
    private String stationName;             //"春熙路
    private String stationStateName;       //"联网状态"
    private String stationTypeName;     //"基站"
    private String monitoringUnitName;      //"春熙路-1#",
    private String equipmentName;      //GFSU自诊断设备-春熙路-1#
    private String equipmentStateName = "联网状态";      //联网状态
    private String eventCategoryName;      //设备事件
    private String name;    //告警名称
    private String standardTypeName;        //事件基类名称
    private String meaning;     //告警含义
    private Integer overturn;   //翻转次数
    private Date startTime;     //开始时间
    private Date confirmTime;   //确认时间
    private Date endTime;       //结束时间
    private String instructionStatus;
    private String instructionId;

    private String triggerValue;        //触发值
    private String confirmUserName;
    private String memo;
    private String stdSignalDescription;
    private String stdSignalMeanings;
    private String stdNote;
    private String equipmentLogicCategory;
    private String alarmLogicCategory;
    private String uniqueId;    //流水号
    private String houseName;
    private Integer status;
    private Integer stationId;//局站ID
    private Integer equipmentId;    //设备ID
    private Integer eventId;    //事件ID
    private Long baseTypeId;    //事件基类ID
    private Integer eventLevel;
    /**
     * 告警标准化Id
     */
    private Integer standardAlarmNameId;
}
