package com.siteweb.as.dto.patrol;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AlarmInfoQueryDTO {

    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String sortKey;
    private String sortValue;

    /**
     * 监控中心
     */
    private String centerName;

    /**
     * 区域名称
     */
    private String groupName;

    /**
     * 局站名称
     */
    private String stationName;

    /**
     * 设备类型
     */
    private String equipmentCategoryName;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 信号名称
     */
    private String signalName;
    /**
     * 预警等级
     */
    private Integer warningLevelId;
    /**
     * 按百分比
     */
    private Integer byPercentage;
    /**
     * 是否停电引起
     */
    private String isPowerOffAlarm;
}
