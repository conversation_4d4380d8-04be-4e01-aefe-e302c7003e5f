package com.siteweb.as.dto.patrol;

import lombok.Data;


@Data
public class PatrolRuleJobDTO {
    private Integer ruleId;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 预警范围
     */
    private Double limitDown;
    /**
     * 预警范围比较符号
     */
    private Integer limitDownCalOpId;
    /**
     * 巡检值
     */
    private Double limitUp;
    /**
     * 巡检值比较符号
     */
    private Integer limitUpCalOpId;
    /**
     * 单位
     */
    private Integer unitId;
    /**
     * 预警等级
     */
    private Integer warningLevelId;
    /**
     * 是否按百分比
     */
    private Integer byPercentage;
    /**
     * 额定值
     */
    private Double ratedValue;
    /**
     * 基类设备id
     */
    private Integer baseEquipmentId;
    /**
     * 基类id
     */
    private Long baseTypeId;
    /**
     * 备注
     */
    private String note;
    /**
     * 描述
     */
    private String description;
}

