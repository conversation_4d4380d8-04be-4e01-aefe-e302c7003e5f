package com.siteweb.as.dto.config;

import lombok.Data;

@Data
public class StructureDTO {
    private Integer structureId;        // 结构ID
    private Integer structureGroupId;   // 结构组ID
    private Integer parentStructureId;  // 父结构ID
    private String structureName;       // 结构名称
    private Boolean isUngroup;          // 是否未分组
    private Integer structureType;      // 结构类型
    private Double mapZoom;             // 地图缩放
    private Double longitude;           // 经度
    private Double latitude;            // 纬度
    private String description;         // 描述
    private Boolean enable;             // 是否启用
}
