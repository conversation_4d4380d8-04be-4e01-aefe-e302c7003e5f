package com.siteweb.as.dto;

import com.siteweb.monitoring.entity.Station;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class StationDetailASDTO{

    // 联网状态
    private Integer state;
    private String statusText;

    private Integer centerId;
    private String centerName;          //中心名称
    private Integer stationId;             //局站Id
    private String stationName;         //局站名称
    private Integer stationGroupId;
    private String stationCategoryName; //局站类型  stationCategory
    private String defaultStationGroupName; //分组名称
    private Integer houseId;
    private String houseName; //默认局房

    private Integer equipmentId;
    private String equipmentName;      //EDM30E门禁控制器",
    private String equipmentCategory;   //智能门禁",

    private Date startTime;
    private Date endTime;
    private String reason;          //备注信息 description;

    private String userName;      //联系人      contact;
    private Integer resourceStructureId;

//    private Integer stationState;     //工程状态
//    private String stationStateName;  //状态名
    private String structurePath;
    public StationDetailASDTO(Station station){
        setCenterId(station.getCenterId());
        setState(station.getConnectState()==1 ? 1:0);
        setStationId(station.getStationId());
        setStationName(station.getStationName());
    }
}
