package com.siteweb.as.dto.config;

import com.siteweb.as.enums.BusinessObjectType;
import com.siteweb.as.enums.CustomPictures;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

@Data
public class ConfigStation {
    private Integer connectState;
    private Double longitude;
    private Double latitude;
    private Integer eomsStationId;
    private Integer floorNo;
    private String setupTime;
    private String propList;
    private String containNode;
    private String reason;
    private Double acreage;
    private CommonSource stationGradeSource;
    private Integer stationGrade;
    private CommonSource stationCategorySource;
    private Integer stationCategoryId;
    private CommonSource stationStateSource;
    private Integer stationState;
    private Long defaultGroupId;
    private String defaultStationGroupName;
    private String vendorNames;
    private List<String> vendors;
    private Integer bordNumber;
    private Integer buildingTypeId;
    private CommonSource buildingTypeSource;
    private CommonSource center;
    private String imageUrl;
    private Boolean visible;
    private Boolean enable;
    private String description;
    private String initialName;
    private Integer objectType;
    private String tag;
    private String uniqueId;
    private Integer objectId;
    private String name;
    private Integer status = 1;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CommonSource {
        private String name;
        private String foreignName;
        private String imageUrl;
        private Boolean visible;
        private Boolean enable;
        private String description;
        private String initialName;
        private int objectType;
        private String tag;
        private String uniqueId;
        private int objectId;
        private Integer status;

        public static CommonSourceBuilder defaultBuilder() {
            return CommonSource.builder()
                    .visible(true)
                    .enable(true)
                    .status(1);
        }
    }

    public static ConfigStation toConfigStation(StationInfoDTO stationInfoDTO, StructureDTO center) {
        ConfigStation configStation = new ConfigStation();
        configStation.setConnectState(stationInfoDTO.getConnectState());
        configStation.setLongitude(stationInfoDTO.getLongitude());
        configStation.setLatitude(stationInfoDTO.getLatitude());
        configStation.setEomsStationId(0);
        configStation.setFloorNo(stationInfoDTO.getFloorNo());
        configStation.setSetupTime(String.valueOf(stationInfoDTO.getSetupTime()));
        configStation.setPropList(stationInfoDTO.getPropList());
        configStation.setContainNode(stationInfoDTO.getContainNode());
        configStation.setReason(stationInfoDTO.getReason());
        configStation.setAcreage(stationInfoDTO.getAcreage());
        configStation.setStationGradeSource(CommonSource.defaultBuilder()
                .objectId(Optional.ofNullable(stationInfoDTO.getStationGrade()).orElse(0))
                .objectType(BusinessObjectType.ConfigStationGrade.value())
                .uniqueId(String.format("%s", Optional.ofNullable(stationInfoDTO.getStationGrade()).orElse(0)))
                .name(stationInfoDTO.getStationGradeName())
                .initialName(stationInfoDTO.getStationGradeName())
                .foreignName(stationInfoDTO.getStationGradeAlias())
                .imageUrl(stationInfoDTO.getStationGradeImage())
                .build());
        configStation.setStationGrade(stationInfoDTO.getStationGrade());
        new CommonSource().setName("");
        configStation.setStationCategorySource(CommonSource.defaultBuilder()
                .objectId(Optional.ofNullable(stationInfoDTO.getStationCategory()).orElse(0))
                .objectType(BusinessObjectType.Unknown.value())
                .uniqueId(String.format("%d-%d", BusinessObjectType.Unknown.value(), Optional.ofNullable(stationInfoDTO.getStationCategory()).orElse(0)))
                .name(stationInfoDTO.getStationCategoryName())
                .initialName(stationInfoDTO.getStationCategoryName())
                .foreignName(stationInfoDTO.getStationCategoryAlias())
                .imageUrl(stationInfoDTO.getStationCategoryImage())
                .build());
        configStation.setStationCategoryId(stationInfoDTO.getStationCategory());
        configStation.setStationStateSource(CommonSource.defaultBuilder()
                .objectId(Optional.ofNullable(stationInfoDTO.getStationState()).orElse(0))
                .objectType(BusinessObjectType.Unknown.value())
                .uniqueId(String.format("%d-%d", BusinessObjectType.Unknown.value(), Optional.ofNullable(stationInfoDTO.getStationState()).orElse(0)))
                .name(stationInfoDTO.getStationStateName())
                .initialName(stationInfoDTO.getStationStateName())
                .build());
        configStation.setStationState(stationInfoDTO.getStationState());
        configStation.setDefaultGroupId(stationInfoDTO.getGroupId());
        configStation.setDefaultStationGroupName(stationInfoDTO.getGroupName());
        configStation.setVendorNames(null);
        configStation.setVendors(List.of());
        configStation.setBordNumber(stationInfoDTO.getBordNumber());
        configStation.setBuildingTypeId(stationInfoDTO.getBuildingType());
        configStation.setBuildingTypeSource(CommonSource.defaultBuilder()
                .objectId(Optional.ofNullable(stationInfoDTO.getBuildingType()).orElse(0))
                .objectType(BusinessObjectType.Unknown.value())
                .uniqueId(String.format("%d-%d", BusinessObjectType.Unknown.value(), Optional.ofNullable(stationInfoDTO.getBuildingType()).orElse(0)))
                .name(stationInfoDTO.getBuildingTypeName())
                .initialName(stationInfoDTO.getBuildingTypeName())
                .foreignName(stationInfoDTO.getBuildingTypeAlias())
                .imageUrl(stationInfoDTO.getBuildingTypeImage())
                .build());
        configStation.setCenter(CommonSource.defaultBuilder()
                .objectId(Optional.ofNullable(center.getStructureId()).orElse(0))
                .objectType(BusinessObjectType.ConfigMonitoringCenter.value())
                .uniqueId(String.format("%d", Optional.ofNullable(center.getStructureId()).orElse(0)))
                .name(center.getStructureName())
                .initialName(center.getStructureName())
                .imageUrl(CustomPictures.MonitoringCenter)
                .build());
        configStation.setImageUrl(CustomPictures.Station);
        configStation.setVisible(true);
        configStation.setEnable(stationInfoDTO.getEnable());
        configStation.setDescription(stationInfoDTO.getDescription());
        configStation.setObjectType(BusinessObjectType.ConfigStation.value());
        configStation.setTag(null);
        configStation.setObjectId(stationInfoDTO.getStationId());
        configStation.setUniqueId(String.format("%d", configStation.getObjectId()));
        configStation.setName(stationInfoDTO.getStationName());
        configStation.setInitialName(configStation.getName());
        return configStation;
    }
}
