package com.siteweb.as.dto.patrol;

import lombok.Data;


@Data
public class PatrolTaskDTO {
    private Integer taskId;
    /**
     * 分组名称
     */
    private String taskName;
    /**
     * tbl_patrolcronexpression id
     */
    private Integer cronId;
    /**
     * 巡检时间表达式
     */
    private String cronExpression;
    /**
     * 巡检时间
     */
    private String meaning;
    /**
     * 分组id
     */
    private Integer groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 是否保存停电引起的告警
     */
    private Integer isPowerOffSave;
    /**
     * 备注
     */
    private String note;

}

