package com.siteweb.as.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ActiveSignalAS {

    private Integer signalId;
    private String signalName;
    private Integer signalCategory;
    private Long baseTypeId=0L;
    private String unit;
    private String showPrecision="0.0";
    private Integer eventSeverity=255;
    private Integer eventLevel;
    private Integer currentState=1;
    private String sampleTime;
    private String signalMeaning;
    private String standardTypeName="";
    private Integer stationId;
    private String stationName;
    private Integer equipmentId;
    private String equipmentName;
    private Integer displayIndex;
    private Integer channelNo;
    private currentValues currentValue = new currentValues();
    @Data
    public static class currentValues {
        private Integer valueType=0;
        private float floatValue;
        private String stringValue;
    }
}