package com.siteweb.as.dto.patrol;

import lombok.Data;

import java.util.Date;

@Data
public class AlarmInfoDTO {
    private Integer centerId;
    private String centerName;
    private Integer groupId;
    private String groupName;
    private Integer stationId;
    private String stationName;
    private String equipmentCategoryName;
    private Integer equipmentId;
    private String equipmentName;
    private Integer signalId;
    private String signalName;
    private String signalValue;
    private Date recordTime;
    private String unit;
    private Double limitDown;
    private Integer limitDownCalOpId;
    private String limitDownCalOperator;
    private Double limitUp;
    private Integer limitUpCalOpId;
    private String limitUpCalOperator;
    private Integer byPercentage;
    private String byPercentageMeaning;
    private Double ratedValue;
    private Integer warningLevelId;
    private String warningMeaning;
    private String isPowerOffAlarm;
    private String isPowerOffAlarmMeaning;
    private Date createTime;
    private String limitMeaning;
}
