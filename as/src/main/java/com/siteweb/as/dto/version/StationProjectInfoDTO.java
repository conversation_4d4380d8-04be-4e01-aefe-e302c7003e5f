package com.siteweb.as.dto.version;

import com.siteweb.as.dto.PageBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StationProjectInfoDTO extends PageBase {
    private Integer stationId;
    private String stationName;
    private String projectName;
    private String contractNo;
    private String installTime;
}
