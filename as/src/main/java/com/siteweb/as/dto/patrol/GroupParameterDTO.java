package com.siteweb.as.dto.patrol;

import com.siteweb.as.entity.PatrolGroupParameters;
import lombok.Data;

@Data
public class GroupParameterDTO {
    /**
     * 分组ID
     */
    private Integer groupId;
    /**
     * 分组类型 1 - 基类，0 - 原始
     */
    private Integer groupType;
    /**
     * 站点类型ID，多个用逗号分隔
     */
    private String stationTypeIds;
    /**
     * 站点ID，多个用逗号分隔
     */
    private String stationIds;
    /**
     * 设备ID，多个用逗号分隔
     */
    private String equipmentIds;
    /**
     * 信号ID，多个用逗号分隔
     */
    private String signalIds;
    /**
     * 规则ID，多个用逗号分隔
     */
    private String ruleIds;

    public PatrolGroupParameters toPatrolGroupParameters() {
        PatrolGroupParameters patrolGroupParameters = new PatrolGroupParameters();
        patrolGroupParameters.setGroupId(this.getGroupId());
        patrolGroupParameters.setStationTypeIds(this.getStationTypeIds());
        patrolGroupParameters.setStationIds(this.getStationIds());
        patrolGroupParameters.setEquipmentIds(this.getEquipmentIds());
        patrolGroupParameters.setSignalIds(this.getSignalIds());
        return patrolGroupParameters;
    }
}
