package com.siteweb.as.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.entity.IcsFsuSoInfo;
import com.siteweb.as.mapper.IcsFsuSoInfoMapper;
import com.siteweb.as.service.IcsFsuSoInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IcsFsuSoInfoServiceImpl implements IcsFsuSoInfoService {
    @Autowired
    IcsFsuSoInfoMapper icsFsuSoInfoMapper;

    @Override
    public List<IcsFsuSoInfo> findFsuSoInfo(String sn, String mac) {
        return icsFsuSoInfoMapper.selectList(Wrappers.lambdaQuery(IcsFsuSoInfo.class).eq(IcsFsuSoInfo::getSn, sn).eq(IcsFsuSoInfo::getMac, mac));
    }
}
