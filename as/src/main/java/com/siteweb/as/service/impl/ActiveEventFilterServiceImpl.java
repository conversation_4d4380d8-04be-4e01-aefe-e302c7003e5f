package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import com.siteweb.as.service.ActiveEventFilterService;
import com.siteweb.as.vo.ValueLabelTreeNodeVO;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.dto.StationFilterDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.entity.StationStructure;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mapper.StationMapper;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.monitoring.service.StationStructureService;
import com.siteweb.monitoring.service.impl.EquipmentServiceImpl;
import com.siteweb.monitoring.standard.StandardHandlerFactory;
import com.siteweb.utility.constans.StandardCategoryEnum;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.StandardVerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ActiveEventFilterServiceImpl implements ActiveEventFilterService {
    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    StationService stationService;
    @Autowired
    EquipmentServiceImpl equipmentService;
    @Autowired
    StandardVerService standardVerService;
    @Autowired
    StationMapper stationMapper;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    StandardHandlerFactory standardHandlerFactory;

    @Override
    public List<IdValueDTO<Integer, String>> findIdValueByEntryId(DataEntryEnum dataEntryEnum) {
        List<DataItem> dataItemList = dataItemService.findByEntryId(dataEntryEnum.getValue());
        return dataItemList.stream().map(IdValueDTO::fromDataItem).toList();
    }


    @Override
    public List<IdValueDTO<Integer, String>> findEventLevel() {
        List<DataItem> dataItemList = dataItemService.findByEntryId(DataEntryEnum.EVENT_LEVEL.getValue());
        return dataItemList.stream()
                           .map(e -> new IdValueDTO<>(Integer.valueOf(e.getExtendField4()), e.getItemValue()))
                           .toList();
    }

    @Override
    public List<IdValueDTO<Integer, String>> findShieldMethod() {
        List<IdValueDTO<Integer,String>> result = new ArrayList<>();
        for (TimeGroupCategoryEnum value : TimeGroupCategoryEnum.values()) {
            result.add(new IdValueDTO<>(value.getValue(), value.getDescribe()));
        }
        return result;
    }

    @Override
    public List<IdValueDTO<Long, String>> findSignalBase() {
        StandardCategoryEnum currentStandardType = standardVerService.getCurrentStandardType();
        return standardHandlerFactory.getHandler(currentStandardType)
                                     .handleSignalStandard();
    }

    @Override
    public List<Signal> findSignalBySignalBaseEntryIds(List<Integer> signalBaseEntryIds) {
        StandardCategoryEnum currentStandardType = standardVerService.getCurrentStandardType();
        return standardHandlerFactory.getHandler(currentStandardType)
                .getSignalBySignalBaseEntryIds(signalBaseEntryIds).stream().distinct().toList();
    }
    @Override
    public List<Tree<Integer>> findGroupingNameById(Integer structureGroupId) {
        List<StationStructure> stationStructureList = stationStructureService.findByStructureGroupId(structureGroupId);
        //根节点的StructureGroupId等于0,上面查询不到，这里查询根节点后给他添加进去，构造成树
        StationStructure root = stationStructureService.findRoot();
        stationStructureList.add(root);
        return TreeUtil.build(stationStructureList, root.getParentStructureId(), ((stationStructure, node) -> {
            node.setId(stationStructure.getStructureId());
            node.setName(stationStructure.getStructureName());
            node.setParentId(stationStructure.getParentStructureId());
        }));
    }

    private ValueLabelTreeNodeVO convertToValueLabelTreeNodeVO(Tree<Integer> node) {
        ValueLabelTreeNodeVO newNode = new ValueLabelTreeNodeVO(node);
        if (CollUtil.isNotEmpty(node.getChildren())) {
            List<ValueLabelTreeNodeVO> children = new ArrayList<>();
            for (Tree<Integer> child : node.getChildren()) {
                // 递归调用，转换子节点
                children.add(convertToValueLabelTreeNodeVO(child));
            }
            newNode.setChildren(children);
        }
        return newNode;
    }

    @Override
    public List<IdValueDTO<Integer, String>> findStationName(Integer userId, StationFilterDTO stationFilterDTO) {
        Set<Integer> stationIdSetByUser = stationService.findByRegionPermission(userId);
        Set<Integer> stationIdSetByCondition = stationMapper.findIdsByFilterCondition(stationFilterDTO);
        Collection<Integer> intersection = CollUtil.intersection(stationIdSetByUser, stationIdSetByCondition);
        return stationService.findByIds(intersection)
                             .stream()
                             .map(e -> new IdValueDTO<>(e.getStationId(), e.getStationName()))
                             .toList();
    }

    @Override
    public List<IdValueDTO<Integer, String>> findEquipmentName(Integer userId, StationFilterDTO stationFilterDTO) {
        List<EquipmentDTO> equipmentDTOList = equipmentService.findEquipmentDTOsByUserId(userId);
        Set<Integer> stationIdSetByCondition = stationMapper.findIdsByFilterCondition(stationFilterDTO);
        Stream<EquipmentDTO> equipmentStream = equipmentDTOList.stream();
        //通过设备类型过滤设备信息
        if (CollUtil.isNotEmpty(stationFilterDTO.getEquipmentCategoryList())) {
            Set<Integer> equipmentIdSet = equipmentManager.getAllEquipments().stream()
                                                          .filter(e -> stationFilterDTO.getEquipmentCategoryList().contains(e.getEquipmentCategory()))
                                                          .map(Equipment::getEquipmentId).collect(Collectors.toSet());
            equipmentStream = equipmentStream.filter(e -> equipmentIdSet.contains(e.getEqId()));
        }
        return equipmentStream.filter(e -> stationIdSetByCondition.contains(e.getSId()))
                              .map(e -> new IdValueDTO<>(e.getEqId(), e.getEqName()))
                              .toList();
    }

    @Override
    public List<IdValueDTO<Long, String>> findEventStandardName() {
        StandardCategoryEnum currentStandardType = standardVerService.getCurrentStandardType();
        return standardHandlerFactory.getHandler(currentStandardType)
                                     .handleEventStandard();
    }
}
