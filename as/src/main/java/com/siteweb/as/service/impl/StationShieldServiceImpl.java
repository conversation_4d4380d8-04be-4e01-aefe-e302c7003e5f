package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.service.AccountService;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.dto.shield.StationShieldExportDTO;
import com.siteweb.as.mapper.StationShieldMapper;
import com.siteweb.as.service.StationShieldService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.as.dto.shield.StationShieldDTO;
import com.siteweb.monitoring.dto.StationMaskDTO;
import com.siteweb.monitoring.entity.StationStructure;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.service.StationMaskService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.monitoring.service.StationStructureService;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import com.siteweb.monitoring.vo.StationMaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
@Service("stationShieldService")
public class StationShieldServiceImpl implements StationShieldService {
    @Autowired
    StationShieldMapper stationShieldMapper;
    @Autowired
    StationMaskService stationMaskService;
    @Autowired
    TimeGroupSpanService timeGroupSpanService;
    @Autowired
    StationService stationService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    AccountService accountService;
    @Autowired
    StationStructureService stationStructureService;

    @Override
    public ExcelWriter exportShield(ShieldFilterDTO shieldFilterDTO, Integer userId) {

        List<StationShieldDTO> masks = findStationMaks(shieldFilterDTO, userId);

        //localeMessageSourceUtil
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("centerName", localeMessageSourceUtil.getMessage("shield.centerName"));
        writer.addHeaderAlias("stationName", localeMessageSourceUtil.getMessage("shield.stationName"));
        writer.addHeaderAlias("userName", localeMessageSourceUtil.getMessage("shield.userName"));
        writer.addHeaderAlias("startTime", localeMessageSourceUtil.getMessage("shield.startTime"));
        writer.addHeaderAlias("endTime", localeMessageSourceUtil.getMessage("shield.endTime"));
        writer.addHeaderAlias("timeGroupCategory",localeMessageSourceUtil.getMessage("shield.shieldTypeName"));
        writer.addHeaderAlias("description", localeMessageSourceUtil.getMessage("shield.description"));
        writer.autoSizeColumnAll(); // 设置列宽
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        List<StationShieldExportDTO> list = masks.stream()
                                                 .map(e -> new StationShieldExportDTO(e, localeMessageSourceUtil))
                                                 .toList();
        writer.write(list);
        return writer;
    }

    private List<StationShieldDTO> findStationMaks(ShieldFilterDTO shieldFilterDTO, Integer userId) {
        if (CollUtil.isEmpty(shieldFilterDTO.getStationIdList())) {
            shieldFilterDTO.setStationIdList(stationService.findByRegionPermission(userId));
        }
        //没有权限直接 返回
        if (CollUtil.isEmpty(shieldFilterDTO.getStationIdList())) {
            return Collections.emptyList();
        }
        List<StationShieldDTO> stationMaksPage = stationShieldMapper.findStationShield(shieldFilterDTO);
        setExtendedInfo(stationMaksPage);
        return stationMaksPage;
    }

    @Override
    public StationMaskDTO getStationMaskById(Integer stationId) {
        return stationMaskService.getStationMaskById(stationId);
    }

    @Override
    public Page<StationShieldDTO> findStationMaksPage(Page<StationMaskVO> page, ShieldFilterDTO shieldFilterDTO, Integer userId) {
        if (CollUtil.isEmpty(shieldFilterDTO.getStationIdList())) {
            shieldFilterDTO.setStationIdList(stationService.findByRegionPermission(userId));
        }
        //没有权限直接 返回
        if (CollUtil.isEmpty(shieldFilterDTO.getStationIdList())) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        page.setOptimizeCountSql(false);
        Page<StationShieldDTO> stationMaksPage = stationShieldMapper.findStationShieldPage(page, shieldFilterDTO);
        setExtendedInfo(stationMaksPage.getRecords());
        return stationMaksPage;
    }

    /**
     * 设备屏蔽方式
     * @param stationMaks 告警屏蔽列表
     */
    private void setExtendedInfo(List<StationShieldDTO> stationMaks) {
        List<Integer> stationIds = stationMaks.stream().map(StationShieldDTO::getStationId).toList();
        List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanService.findByTimeGroupIds(stationIds);
        Set<Integer> stationIdSet = timeGroupSpanList.stream().map(TimeGroupSpan::getTimeGroupId).collect(Collectors.toSet());
        Map<Integer, String> userNameMap = accountService.findUserNameMapByUserIds(stationMaks.stream().map(
                StationShieldDTO::getUserId).toList());
        StationStructure center = stationStructureService.findPostalStructure();
        stationMaks.forEach(e -> {
            if (stationIdSet.contains(e.getStationId())) {
                e.setTimeGroupCategory(TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue());
            }
            if (Objects.nonNull(e.getStartTime())) {
                e.setTimeGroupCategory(TimeGroupCategoryEnum.FULL_TIME_MASK.getValue());
            }
            e.setUserName(userNameMap.get(e.getUserId()));
            e.setCenterName(center.getStructureName());
        });
    }

    @Override
    public int batchDeleteStationMask(Integer userId, List<Integer> stationIds) {
        return stationMaskService.batchDeleteStationMask(userId, stationIds);
    }

    @Override
    public boolean batchSetStationMask(Integer userId, BatchSetStationMaskDTO batchSetStationMaskDTO) {
        return stationMaskService.batchSetStationMask(userId, batchSetStationMaskDTO);
    }
}
