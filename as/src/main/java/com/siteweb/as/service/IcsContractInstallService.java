package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.InstallContractDTO;
import com.siteweb.as.entity.IcsContractInstall;
import com.siteweb.as.vo.version.InstallContractVO;

import java.util.List;

public interface IcsContractInstallService {
    Page<InstallContractVO> findContractInfoPage(InstallContractDTO installContractDTO);

    IcsContractInstall findContractInfo(String contractNo, String projectName);

    boolean createOrUpdateContractInfo(IcsContractInstall icsContractInstall);

    ExcelWriter exportContractInfo(InstallContractDTO installContractDTO);

    boolean batchUpdateContractInfo(List<IcsContractInstall> icsContractInstallList);
}
