package com.siteweb.as.service;

import com.siteweb.as.dto.area.AreaStationChangeDTO;
import com.siteweb.as.vo.area.AreaStationIdVO;
import com.siteweb.as.vo.area.AreaStationVO;
import com.siteweb.as.vo.area.StructureDataVO;

import java.util.List;

public interface AreaStationService {
    List<AreaStationVO> getStationByGroupIdAndStructureId(Integer groupId, Integer structureId);

    List<StructureDataVO> getStructureData(Integer structure);

    boolean updateAreaMap(AreaStationChangeDTO areaStationChangeDTO);

    List<AreaStationIdVO> findStationByAreaId(Integer areaId);
}
