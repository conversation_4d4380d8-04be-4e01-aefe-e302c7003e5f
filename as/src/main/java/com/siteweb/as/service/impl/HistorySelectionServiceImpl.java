package com.siteweb.as.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.dto.EventComplexFilterDto;
import com.siteweb.as.entity.HistorySelection;
import com.siteweb.as.mapper.HistorySelectionMapper;
import com.siteweb.as.service.HistorySelectionService;
import com.siteweb.as.vo.CustomTemplateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HistorySelectionServiceImpl implements HistorySelectionService {
    @Autowired
    HistorySelectionMapper historySelectionMapper;

    @Override
    public Integer saveEventFilter(Integer userId, CustomTemplateVO customTemplate) {
        //先删除
        deleteByTypeAndName(customTemplate.getSelectionType(), customTemplate.getSelectionName());
        HistorySelection historySelection = new HistorySelection();
        historySelection.setCreateTime(new Date());
        historySelection.setDescription(customTemplate.getDescription());
        historySelection.setUserId(customTemplate.getUserId());
        historySelection.setSelectionContent(JSONUtil.toXmlStr(JSONUtil.parseObj(customTemplate.getSelectionContent())));
        historySelection.setQueryInformation(customTemplate.getQueryInformation());
        historySelection.setSelectionName(customTemplate.getSelectionName());
        historySelection.setSelectionType(customTemplate.getSelectionType());
        //在插入
        return insertEventFilter(historySelection);
    }

    @Override
    public List<EventComplexFilterDto> findByUserIdAndFilterType(Integer userId, String filterType) {
        return findByUserIdAdnType(userId, filterType)
                .stream()
                .map(EventComplexFilterDto::new)
                .toList();
    }

    @Override
    public int deleteById(Integer historySelectionId) {
        return historySelectionMapper.deleteById(historySelectionId);
    }

    private List<HistorySelection> findByUserIdAdnType(Integer userId, String selectionType) {
        return historySelectionMapper.findByUserIdAdnType(userId, selectionType);
    }

    private int insertEventFilter(HistorySelection historySelection) {
        return historySelectionMapper.insert(historySelection);
    }

    private void deleteByTypeAndName(String selectionType, String selectionName) {
         historySelectionMapper.delete(Wrappers.lambdaQuery(HistorySelection.class)
                                                         .eq(HistorySelection::getSelectionType, selectionType)
                                                         .eq(HistorySelection::getSelectionName, selectionName));
    }

}
