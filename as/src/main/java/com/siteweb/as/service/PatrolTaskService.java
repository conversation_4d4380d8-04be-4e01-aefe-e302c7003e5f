package com.siteweb.as.service;

import com.siteweb.as.dto.patrol.PatrolTaskDTO;
import com.siteweb.as.entity.PatrolCronExpression;
import com.siteweb.as.entity.PatrolTask;

import java.util.List;

public interface PatrolTaskService {

    List<PatrolTaskDTO> getAllPatrolTask();

    PatrolTaskDTO getPatrolTask(Integer taskId);

    void savePatrolTask(PatrolTask patrolTask);

    void updatePatrolTask(PatrolTask patrolTask);

    void deletePatrolTask(Integer taskId);

    List<PatrolCronExpression> getCronExpression();

    List<PatrolTaskDTO> getAllPatrolStandardTask();

    PatrolTaskDTO getPatrolStandardTask(Integer taskId);

}
