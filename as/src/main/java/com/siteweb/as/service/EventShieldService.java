package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.shield.EventShieldDTO;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import com.siteweb.monitoring.vo.StationMaskVO;

import java.util.List;

public interface EventShieldService {

    boolean deleteShield(Integer userId, List<String> ids);

    ExcelWriter exportShield(ShieldFilterDTO shieldFilterDTO, Integer userId);

    Page<EventShieldDTO> findEventMaksPage(Page<StationMaskVO> page, ShieldFilterDTO shieldFilterDTO, Integer userId);

    boolean batchSaveShield(Integer userId, BatchCreateEventMaskDTO batchCreateEventMaskDTO);
}
