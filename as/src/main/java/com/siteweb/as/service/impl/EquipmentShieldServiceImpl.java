package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.service.AccountService;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.dto.shield.EquipmentShieldDTO;
import com.siteweb.as.dto.shield.EquipmentShieldExportDTO;
import com.siteweb.as.mapper.EquipmentShieldMapper;
import com.siteweb.as.service.EquipmentShieldService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.service.EquipmentMaskService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("equipmentShieldService")
public class EquipmentShieldServiceImpl implements EquipmentShieldService {

    @Autowired
    EquipmentShieldMapper equipmentShieldMapper;
    @Autowired
    EquipmentMaskService equipmentMaskService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    TimeGroupSpanService timeGroupSpanService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    AccountService accountService;

    @Override
    public Integer saveShield(BatchEquipmentMaskVO equipmentMaskVO, Integer userId) {
        // 把ids转成局站ids和设备ids
        List<String> idList = equipmentMaskVO.getIds();
        if (CollUtil.isNotEmpty(idList)) {
            List<Integer> stationIds = new ArrayList<>(idList.size());
            List<Integer> equipmentIds = new ArrayList<>(idList.size());
            for (String idString : idList) {
                String[] ids = idString.split(",");
                stationIds.add(Integer.valueOf(ids[0]));
                equipmentIds.add(Integer.valueOf(ids[1]));
            }
            equipmentMaskVO.setStationIds(stationIds);
            equipmentMaskVO.setEquipmentIds(equipmentIds);
        }
        equipmentMaskService.batchCreateEquipmentMasks(equipmentMaskVO,userId);
        return userId;
    }

    @Override
    public Boolean deleteShield(List<Integer> equipmentIds, Integer userId) {
        equipmentMaskService.batchDeleteEquipmentMasks(equipmentIds,userId);
        return true;
    }

    @Override
    public ExcelWriter exportShield(ShieldFilterDTO shieldFilterDTO, Integer userId) {
        List<EquipmentShieldDTO> equipmentShield = findEquipmentShield(shieldFilterDTO, userId);
        List<EquipmentShieldExportDTO> equipmentShieldExportDTOList = equipmentShield.stream().map(e -> new EquipmentShieldExportDTO(e, localeMessageSourceUtil)).toList();
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("stationName", localeMessageSourceUtil.getMessage("shield.stationName"));
        writer.addHeaderAlias("equipmentName", localeMessageSourceUtil.getMessage("shield.equipmentName"));
        writer.addHeaderAlias("userName", localeMessageSourceUtil.getMessage("shield.userName"));
        writer.addHeaderAlias("startTime", localeMessageSourceUtil.getMessage("shield.startTime"));
        writer.addHeaderAlias("endTime", localeMessageSourceUtil.getMessage("shield.endTime"));
        writer.addHeaderAlias("timeGroupCategory",localeMessageSourceUtil.getMessage("shield.shieldTypeName"));
        writer.addHeaderAlias("description", localeMessageSourceUtil.getMessage("shield.description"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(equipmentShieldExportDTOList);
        return writer;
    }

    @Override
    public Page<EquipmentShieldDTO> findEquipmentShieldPage(Page<EquipmentShieldDTO> page, ShieldFilterDTO shieldFilterDTO, Integer userId) {
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            shieldFilterDTO.setEquipmentIdList(equipmentService.findEquipmentIdsByUserId(userId));
        }
        //没有权限直接 返回
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        page.setOptimizeCountSql(false);
        Page<EquipmentShieldDTO> equipmentShieldPage = equipmentShieldMapper.findEquipmentShieldPage(page, shieldFilterDTO);
        setExtendedInfo(equipmentShieldPage.getRecords());
        return equipmentShieldPage;
    }

    public List<EquipmentShieldDTO> findEquipmentShield(ShieldFilterDTO shieldFilterDTO, Integer userId) {
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            shieldFilterDTO.setEquipmentIdList(equipmentService.findEquipmentIdsByUserId(userId));
        }
        //没有权限直接 返回
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            return Collections.emptyList();
        }
        List<EquipmentShieldDTO> equipmentShield = equipmentShieldMapper.findEquipmentShield(shieldFilterDTO);
        setExtendedInfo(equipmentShield);
        return equipmentShield;
    }

    /**
     * 设备屏蔽方式
     * @param equipmentMask 告警屏蔽列表
     */
    private void setExtendedInfo(List<EquipmentShieldDTO> equipmentMask) {
        List<Integer> timeGroupIds = equipmentMask.stream()
                                                  .map(e -> Integer.valueOf("1" + e.getEquipmentId()))
                                                  .toList();
        List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanService.findByTimeGroupIds(timeGroupIds);
        Set<Integer> equipmentIdSet = timeGroupSpanList.stream()
                                                       .map(e -> removeFirstDigit(e.getTimeGroupId()))
                                                       .collect(Collectors.toSet());
        Map<Integer, String> userNameMap = accountService.findUserNameMapByUserIds(equipmentMask.stream().map(
                EquipmentShieldDTO::getUserId).toList());
        equipmentMask.forEach(e -> {
            if (equipmentIdSet.contains(e.getEquipmentId())) {
                e.setTimeGroupCategory(TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue());
            }
            if (Objects.nonNull(e.getStartTime())) {
                e.setTimeGroupCategory(TimeGroupCategoryEnum.FULL_TIME_MASK.getValue());
            }
            e.setUserName(userNameMap.get(e.getUserId()));
        });
    }

    public int removeFirstDigit(Integer number) {
        // 将整数转换为字符串
        String numberStr = Integer.toString(number);
        // 去掉首个字符
        String withoutFirstDigit = numberStr.substring(1);
        // 将结果转换回整数
        return Integer.parseInt(withoutFirstDigit);
    }
}
