package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.as.dto.ActiveEventASDTO;
import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ActiveEventASService {

    Page<ActiveEventASDTO> findActiveEvents(int userId, Pageable pageable, ActiveEventFilterVO activeEventFilterVO);

    ExcelWriter exportActiveEvents(Integer userId,List<String> columns, List<String> titles);

    List<IdValueDTO<Integer, Long>> findActiveEventStatistics(Integer userId, ActiveEventFilterVO activeEventFilterVO);

    List<ActiveEventASDTO> getActiveEventByStationAndEquipment(int stationId, int equipmentId);
}

