package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.EquipmentProjectInfoDTO;
import com.siteweb.as.entity.EquipmentProjectInfo;
import com.siteweb.as.mapper.EquipmentProjectInfoMapper;
import com.siteweb.as.service.EquipmentProjectInfoService;
import com.siteweb.as.vo.version.EquipmentProjectInfoVO;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class EquipmentProjectInfoServiceImpl implements EquipmentProjectInfoService {
    @Autowired
    EquipmentProjectInfoMapper equipmentProjectInfoMapper;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public Page<EquipmentProjectInfoVO> findContractInfoPage(Integer userId, EquipmentProjectInfoDTO equipmentProjectInfoDTO) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return equipmentProjectInfoDTO.generatePaginationInfo();
        }
        return equipmentProjectInfoMapper.findContractInfoPage(equipmentProjectInfoDTO.generatePaginationInfo(),equipmentIds,equipmentProjectInfoDTO);
    }

    @Override
    public EquipmentProjectInfoVO findContractInfoByEquipmentId(Integer equipmentId) {
        return equipmentProjectInfoMapper.findContractInfoByEquipmentId(equipmentId);
    }

    @Override
    public boolean updateContractInfo(EquipmentProjectInfoDTO equipmentProjectInfoDTO) {
        return equipmentProjectInfoMapper.update(Wrappers.lambdaUpdate(EquipmentProjectInfo.class)
                                                       .set(EquipmentProjectInfo::getContractNo, equipmentProjectInfoDTO.getContractNo())
                                                       .set(EquipmentProjectInfo::getProjectName, equipmentProjectInfoDTO.getProjectName())
                                                       .eq(EquipmentProjectInfo::getStationId, equipmentProjectInfoDTO.getStationId())
                                                       .eq(EquipmentProjectInfo::getEquipmentId, equipmentProjectInfoDTO.getEquipmentId())) > 0;
    }

    private List<EquipmentProjectInfoVO> findContractInfo(Integer userId, EquipmentProjectInfoDTO equipmentProjectInfoDTO) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return equipmentProjectInfoMapper.findContractInfo(equipmentIds, equipmentProjectInfoDTO);
    }
    @Override
    public ExcelWriter exportContractInfo(EquipmentProjectInfoDTO equipmentProjectInfoDTO, Integer userId) {
        List<EquipmentProjectInfoVO> equipmentProjectInfoVOList = findContractInfo(userId, equipmentProjectInfoDTO);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("stationName", messageSourceUtil.getMessage("version.fsu.siteName"));
        writer.addHeaderAlias("houseName", messageSourceUtil.getMessage("version.contract.houseName"));
        writer.addHeaderAlias("equipmentName", messageSourceUtil.getMessage("version.contract.equipmentName"));
        writer.addHeaderAlias("projectName", messageSourceUtil.getMessage("version.contract.projectName"));
        writer.addHeaderAlias("contractNo", messageSourceUtil.getMessage("version.contract.contractNo"));
        writer.addHeaderAlias("installTime", messageSourceUtil.getMessage("version.contract.installTime"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(equipmentProjectInfoVOList);
        return writer;
    }

    @Override
    public boolean batchUpdateContractInfo(List<EquipmentProjectInfoDTO> equipmentProjectInfoDTOList) {
        if (CollUtil.isEmpty(equipmentProjectInfoDTOList)) {
            return false;
        }
        return equipmentProjectInfoMapper.batchUpdateContractInfo(equipmentProjectInfoDTOList) > 0;
    }
}
