package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.EquipmentProjectInfoDTO;
import com.siteweb.as.vo.version.EquipmentProjectInfoVO;

import java.util.List;

public interface EquipmentProjectInfoService {
    Page<EquipmentProjectInfoVO> findContractInfoPage(Integer userId, EquipmentProjectInfoDTO equipmentProjectInfoDTO);

    EquipmentProjectInfoVO findContractInfoByEquipmentId(Integer equipmentId);

    boolean updateContractInfo(EquipmentProjectInfoDTO equipmentProjectInfoDTO);

    ExcelWriter exportContractInfo(EquipmentProjectInfoDTO equipmentProjectInfoDTO, Integer userId);

    boolean batchUpdateContractInfo(List<EquipmentProjectInfoDTO> equipmentProjectInfoDTOList);
}
