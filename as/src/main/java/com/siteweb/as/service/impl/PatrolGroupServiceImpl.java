package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.dto.patrol.*;
import com.siteweb.as.entity.*;
import com.siteweb.as.mapper.*;
import com.siteweb.as.service.PatrolGroupService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.StringUtils;
import com.siteweb.utility.service.StandardVerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class PatrolGroupServiceImpl implements PatrolGroupService {
    private final PatrolGroupMapper patrolGroupMapper;
    private final PatrolGroupBaseSignalMapMapper patrolGroupBaseSignalMapMapper;
    private final PatrolTaskMapper patrolTaskMapper;
    private final PatrolGroupParametersMapper patrolGroupParametersMapper;
    private final PatrolGroupRuleMapMapper patrolGroupRuleMapMapper;
    private final StandardVerService standardVerService;
    private final StationBaseTypeMapper stationBaseTypeMapper;
    private final PatrolStandardGroupMapper patrolStandardGroupMapper;
    private final PatrolGroupStandardSignalMapMapper patrolGroupStandardSignalMapMapper;

    @Override
    public List<PatrolGroupAllDTO> getAllPatrolGroup() {
        List<PatrolGroupDTO> patrolGroupDTOS = patrolGroupMapper.findPatrolGroups();
        return patrolGroupDTOS.stream().map(PatrolGroupAllDTO::toPatrolGroupAllDTO).toList();
    }

    @Override
    public PatrolGroupDTO getPatrolGroup(Integer groupId) {
        return patrolGroupMapper.getPatrolGroup(groupId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePatrolGroup(PatrolGroupDTO patrolGroupDTO) {
        if (CharSequenceUtil.isBlank(patrolGroupDTO.getGroupName())) {
            throw new BusinessException("分组名称不能为空");
        }
        Long count = patrolGroupMapper.selectCount(Wrappers.lambdaQuery(PatrolGroup.class).eq(PatrolGroup::getGroupName, patrolGroupDTO.getGroupName()));
        if (count > 0) {
            throw new BusinessException("分组名称不能重复");
        }
        PatrolGroup patrolGroup = new PatrolGroup();
        patrolGroup.setGroupName(patrolGroupDTO.getGroupName());
        patrolGroup.setNote(patrolGroupDTO.getNote());
        patrolGroupMapper.insert(patrolGroup);
        List<BaseSignalTypeInfo> baseSignalList = patrolGroupDTO.getBaseSignalList();
        if (CollUtil.isNotEmpty(baseSignalList)) {
            List<PatrolGroupBaseSignalMap> list = baseSignalList.stream().map(baseSignal -> {
                PatrolGroupBaseSignalMap patrolGroupBaseSignalMap = new PatrolGroupBaseSignalMap();
                patrolGroupBaseSignalMap.setGroupId(patrolGroup.getGroupId());
                patrolGroupBaseSignalMap.setBaseEquipmentId(patrolGroupDTO.getBaseEquipmentId());
                patrolGroupBaseSignalMap.setBaseTypeId(baseSignal.getBaseTypeId());
                return patrolGroupBaseSignalMap;
            }).toList();
            patrolGroupBaseSignalMapMapper.insert(list);
        }
    }

    @Override
    public void updatePatrolGroup(PatrolGroupDTO patrolGroupDTO) {
        if (CharSequenceUtil.isBlank(patrolGroupDTO.getGroupName())) {
            throw new BusinessException("分组名称不能为空");
        }
        Long count = patrolGroupMapper.selectCount(Wrappers.lambdaQuery(PatrolGroup.class)
                .eq(PatrolGroup::getGroupName, patrolGroupDTO.getGroupName())
                .ne(PatrolGroup::getGroupId, patrolGroupDTO.getGroupId())
        );
        if (count > 0) {
            throw new BusinessException("分组名称不能重复");
        }
        PatrolGroup patrolGroup = new PatrolGroup();
        patrolGroup.setGroupId(patrolGroupDTO.getGroupId());
        patrolGroup.setGroupName(patrolGroupDTO.getGroupName());
        patrolGroup.setNote(patrolGroupDTO.getNote());
        patrolGroupMapper.updateById(patrolGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePatrolGroup(Integer groupId) {
        Long count = patrolTaskMapper.selectCount(Wrappers.lambdaQuery(PatrolTask.class).eq(PatrolTask::getGroupId, groupId));
        if (count > 0) {
            throw new BusinessException("有关联的任务，未进行删除");
        }
        patrolGroupMapper.deleteById(groupId);
        patrolGroupBaseSignalMapMapper.delete(Wrappers.lambdaQuery(PatrolGroupBaseSignalMap.class).eq(PatrolGroupBaseSignalMap::getGroupId, groupId));
        patrolGroupParametersMapper.delete(Wrappers.lambdaQuery(PatrolGroupParameters.class).eq(PatrolGroupParameters::getGroupId, groupId));
        patrolGroupRuleMapMapper.delete(Wrappers.lambdaQuery(PatrolGroupRuleMap.class).eq(PatrolGroupRuleMap::getGroupId, groupId));
    }

    @Override
    public List<ValueLabelDTO> listStationtype() {
        int standardVer = standardVerService.getStandardVer();
        if (standardVer == 0) {
            return List.of(ValueLabelDTO.builder()
                    .value(0)
                    .label("适应所有类型")
                    .build());
        }
        List<StationBaseType> stationBaseTypes = stationBaseTypeMapper.selectList(Wrappers.lambdaQuery(StationBaseType.class).eq(StationBaseType::getStandardId, standardVer));
        return stationBaseTypes.stream().map(stationBaseType -> ValueLabelDTO.builder()
                .value(stationBaseType.getId())
                .label(stationBaseType.getType())
                .build()).toList();
    }

    @Override
    public List<ValueLabelDTO> listStation(String stationTypeId) {
        int[] stationTypeIds;
        if (CharSequenceUtil.isBlank(stationTypeId) || stationTypeId.contains("0")) {
            // stationTypeId包含0，则查全部
            stationTypeIds = null;
        } else {
            stationTypeIds = CharSequenceUtil.splitToInt(stationTypeId, ",");
        }
        int standardVer = standardVerService.getStandardVer();
        return patrolGroupMapper.listStation(standardVer, stationTypeIds);
    }

    @Override
    public GroupParameterDTO getGroupParameter(Integer groupId) {
        GroupParameterDTO groupParameterDTO = patrolGroupMapper.getGroupParameter(groupId);
        if (Objects.isNull(groupParameterDTO)) {
            return null;
        }
        Long totalCount = patrolGroupBaseSignalMapMapper.selectCount(Wrappers.lambdaQuery(PatrolGroupBaseSignalMap.class)
                .eq(PatrolGroupBaseSignalMap::getGroupId, groupId)
                .eq(PatrolGroupBaseSignalMap::getBaseEquipmentId, -1)
                .eq(PatrolGroupBaseSignalMap::getBaseTypeId, -1)
        );
        if (totalCount > 0) {
            groupParameterDTO.setGroupType(0);
        } else {
            groupParameterDTO.setGroupType(1);
        }
        List<PatrolGroupRuleMap> patrolGroupRuleMaps = patrolGroupRuleMapMapper.selectList(Wrappers.lambdaQuery(PatrolGroupRuleMap.class)
                .eq(PatrolGroupRuleMap::getGroupId, groupId));
        if (CollUtil.isNotEmpty(patrolGroupRuleMaps)) {
            String ruleIds = patrolGroupRuleMaps.stream()
                    .map(info -> info.getRuleId() + "")
                    .collect(Collectors.joining(","));
            groupParameterDTO.setRuleIds(ruleIds);
        }
        return groupParameterDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveGroupParameter(GroupParameterDTO groupParameterDTO) {
        patrolGroupParametersMapper.delete(Wrappers.lambdaUpdate(PatrolGroupParameters.class).eq(PatrolGroupParameters::getGroupId, groupParameterDTO.getGroupId()));
        PatrolGroupParameters patrolGroupParameter = groupParameterDTO.toPatrolGroupParameters();
        patrolGroupParametersMapper.insert(patrolGroupParameter);
        patrolGroupRuleMapMapper.delete(Wrappers.lambdaQuery(PatrolGroupRuleMap.class).eq(PatrolGroupRuleMap::getGroupId, groupParameterDTO.getGroupId()));
        String ruleIds = groupParameterDTO.getRuleIds();
        if (CharSequenceUtil.isNotBlank(ruleIds)) {
            List<Integer> ruleIdList = StringUtils.splitToIntegerList(ruleIds);
            List<PatrolGroupRuleMap> patrolGroupRuleMaps = ruleIdList.stream().map(ruleId -> {
                PatrolGroupRuleMap patrolGroupRuleMap = new PatrolGroupRuleMap();
                patrolGroupRuleMap.setGroupId(groupParameterDTO.getGroupId());
                patrolGroupRuleMap.setRuleId(ruleId);
                return patrolGroupRuleMap;
            }).toList();
            patrolGroupRuleMapMapper.insert(patrolGroupRuleMaps);
        }

    }

    @Override
    public List<PatrolStandardGroupAllDTO> getAllPatrolStandardGroup() {
        List<PatrolStandardGroupDTO> patrolGroupDTOS = patrolStandardGroupMapper.findPatrolStandardGroups();
        return patrolGroupDTOS.stream().map(PatrolStandardGroupAllDTO::toPatrolGroupAllDTO).toList();
    }

    @Override
    public PatrolStandardGroupDTO getPatrolStandardGroup(Integer groupId) {
        return patrolStandardGroupMapper.getPatrolStandardGroup(groupId);
    }



    @Override
    public GroupParameterDTO getStandardGroupParameter(Integer groupId) {
        GroupParameterDTO groupParameterDTO = patrolStandardGroupMapper.getGroupParameter(groupId);
        if (Objects.isNull(groupParameterDTO)) {
            return null;
        }
        Long totalCount = patrolGroupStandardSignalMapMapper.selectCount(Wrappers.lambdaQuery(PatrolGroupStandardSignalMap.class)
                .eq(PatrolGroupStandardSignalMap::getGroupId, groupId)
                .eq(PatrolGroupStandardSignalMap::getEquipmentLogicClassId, -1)
                .eq(PatrolGroupStandardSignalMap::getStandardDicId, -1)
        );
        if (totalCount > 0) {
            groupParameterDTO.setGroupType(0);
        } else {
            groupParameterDTO.setGroupType(1);
        }
        List<PatrolGroupRuleMap> patrolGroupRuleMaps = patrolGroupRuleMapMapper.selectList(Wrappers.lambdaQuery(PatrolGroupRuleMap.class)
                .eq(PatrolGroupRuleMap::getGroupId, groupId));
        if (CollUtil.isNotEmpty(patrolGroupRuleMaps)) {
            String ruleIds = patrolGroupRuleMaps.stream()
                    .map(info -> info.getRuleId() + "")
                    .collect(Collectors.joining(","));
            groupParameterDTO.setRuleIds(ruleIds);
        }
        return groupParameterDTO;
    }

    @Override
    public void savePatrolStandardGroup(PatrolStandardGroupDTO patrolGroupDTO) {
        if (CharSequenceUtil.isBlank(patrolGroupDTO.getGroupName())) {
            throw new BusinessException("分组名称不能为空");
        }
        Long count = patrolStandardGroupMapper.selectCount(Wrappers.lambdaQuery(PatrolStandardGroup.class).eq(PatrolStandardGroup::getGroupName, patrolGroupDTO.getGroupName()));
        if (count > 0) {
            throw new BusinessException("分组名称不能重复");
        }
        PatrolStandardGroup patrolStandardGroup = new PatrolStandardGroup();
        patrolStandardGroup.setGroupName(patrolGroupDTO.getGroupName());
        patrolStandardGroup.setNote(patrolGroupDTO.getNote());
        patrolStandardGroupMapper.insert(patrolStandardGroup);
        List<StandardSignalDTO> standardSignalList = patrolGroupDTO.getStandardSignalList();
        if (CollUtil.isNotEmpty(standardSignalList)) {
            List<PatrolGroupStandardSignalMap> list = standardSignalList.stream().map(standardSignal -> {
                PatrolGroupStandardSignalMap patrolGroupStandardSignalMap = new PatrolGroupStandardSignalMap();
                patrolGroupStandardSignalMap.setGroupId(patrolStandardGroup.getGroupId());
                patrolGroupStandardSignalMap.setEquipmentLogicClassId(patrolGroupDTO.getEquipmentLogicClassId());
                patrolGroupStandardSignalMap.setStandardDicId(standardSignal.getStandardDicId());
                return patrolGroupStandardSignalMap;
            }).toList();
            patrolGroupStandardSignalMapMapper.insert(list);
        }
    }

    @Override
    public void updatePatrolStandardGroup(PatrolStandardGroupDTO patrolGroupDTO) {
        if (CharSequenceUtil.isBlank(patrolGroupDTO.getGroupName())) {
            throw new BusinessException("分组名称不能为空");
        }
        Long count = patrolStandardGroupMapper.selectCount(Wrappers.lambdaQuery(PatrolStandardGroup.class)
                .eq(PatrolStandardGroup::getGroupName, patrolGroupDTO.getGroupName())
                .ne(PatrolStandardGroup::getGroupId, patrolGroupDTO.getGroupId())
        );
        if (count > 0) {
            throw new BusinessException("分组名称不能重复");
        }
        PatrolStandardGroup patrolStandardGroup = new PatrolStandardGroup();
        patrolStandardGroup.setGroupId(patrolGroupDTO.getGroupId());
        patrolStandardGroup.setGroupName(patrolGroupDTO.getGroupName());
        patrolStandardGroup.setNote(patrolGroupDTO.getNote());
        patrolStandardGroupMapper.updateById(patrolStandardGroup);
    }

    @Override
    public void deletePatrolStandardGroup(Integer groupId) {
        Long count = patrolTaskMapper.selectCount(Wrappers.lambdaQuery(PatrolTask.class).eq(PatrolTask::getGroupId, groupId));
        if (count > 0) {
            throw new BusinessException("有关联的任务，未进行删除");
        }
        patrolStandardGroupMapper.deleteById(groupId);
        patrolGroupStandardSignalMapMapper.delete(Wrappers.lambdaQuery(PatrolGroupStandardSignalMap.class).eq(PatrolGroupStandardSignalMap::getGroupId, groupId));
        patrolGroupParametersMapper.delete(Wrappers.lambdaQuery(PatrolGroupParameters.class).eq(PatrolGroupParameters::getGroupId, groupId));
        patrolGroupRuleMapMapper.delete(Wrappers.lambdaQuery(PatrolGroupRuleMap.class).eq(PatrolGroupRuleMap::getGroupId, groupId));
    }
}
