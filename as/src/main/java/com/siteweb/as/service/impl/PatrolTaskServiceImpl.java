package com.siteweb.as.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.dto.patrol.PatrolTaskDTO;
import com.siteweb.as.entity.PatrolCronExpression;
import com.siteweb.as.entity.PatrolTask;
import com.siteweb.as.mapper.PatrolCronExpressionMapper;
import com.siteweb.as.mapper.PatrolStandardTaskMapper;
import com.siteweb.as.mapper.PatrolTaskMapper;
import com.siteweb.as.schedule.PatrolTaskSchedule;
import com.siteweb.as.service.PatrolTaskService;
import com.siteweb.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PatrolTaskServiceImpl implements PatrolTaskService {
    private final PatrolTaskMapper patrolTaskMapper;
    private final PatrolCronExpressionMapper patrolCronExpressionMapper;
    private final PatrolTaskSchedule patrolTaskSchedule;
    private final PatrolStandardTaskMapper patrolStandardTaskMapper;

    @Override
    public List<PatrolTaskDTO> getAllPatrolTask() {
        return patrolTaskMapper.getAllPatrolTask();
    }

    @Override
    public PatrolTaskDTO getPatrolTask(Integer taskId) {
        return patrolTaskMapper.getPatrolTask(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePatrolTask(PatrolTask patrolTask) {
        if (CharSequenceUtil.isBlank(patrolTask.getTaskName())) {
            throw new BusinessException("任务名称不能为空");
        }
        Long count = patrolTaskMapper.selectCount(Wrappers.lambdaQuery(PatrolTask.class).eq(PatrolTask::getTaskName, patrolTask.getTaskName()));
        if (count > 0) {
            throw new BusinessException("任务名称不能重复");
        }
        patrolTaskMapper.insert(patrolTask);
        try {
            patrolTaskSchedule.addJob(patrolTask);
        } catch (SchedulerException e) {
            throw new BusinessException("添加定时任务失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePatrolTask(PatrolTask patrolTask) {
        if (CharSequenceUtil.isBlank(patrolTask.getTaskName())) {
            throw new BusinessException("任务名称不能为空");
        }
        Long count = patrolTaskMapper.selectCount(Wrappers.lambdaQuery(PatrolTask.class).eq(PatrolTask::getTaskName, patrolTask.getTaskName())
                .ne(PatrolTask::getTaskId, patrolTask.getTaskId()));
        if (count > 0) {
            throw new BusinessException("任务名称不能重复");
        }
        patrolTaskMapper.updateById(patrolTask);
        try {
            patrolTaskSchedule.updateJob(patrolTask);
        } catch (SchedulerException e) {
            throw new BusinessException("修改定时任务失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePatrolTask(Integer taskId) {
        patrolTaskMapper.deleteById(taskId);
        try {
            patrolTaskSchedule.deleteJob(taskId.toString());
        } catch (SchedulerException e) {
            throw new BusinessException("删除定时任务失败");
        }
    }

    @Override
    public List<PatrolCronExpression> getCronExpression() {
        return patrolCronExpressionMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<PatrolTaskDTO> getAllPatrolStandardTask() {
        return patrolStandardTaskMapper.getAllPatrolStandardTask();
    }

    @Override
    public PatrolTaskDTO getPatrolStandardTask(Integer taskId) {
        return patrolStandardTaskMapper.getPatrolStandardTask(taskId);
    }


}
