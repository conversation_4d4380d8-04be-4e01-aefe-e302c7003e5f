package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.InstallContractDTO;
import com.siteweb.as.entity.IcsContractInstall;
import com.siteweb.as.mapper.IcsContractInstallMapper;
import com.siteweb.as.service.IcsContractInstallService;
import com.siteweb.as.vo.version.InstallContractVO;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class IcsContractInstallServiceImpl implements IcsContractInstallService {
    @Autowired
    IcsContractInstallMapper icsContractInstallMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public Page<InstallContractVO> findContractInfoPage(InstallContractDTO installContractDTO) {
        return icsContractInstallMapper.findContractInfoPage(installContractDTO.generatePaginationInfo(),installContractDTO);
    }

    public List<InstallContractVO> findContractInfoList(InstallContractDTO installContractDTO) {
        return icsContractInstallMapper.findContractInfoList(installContractDTO);
    }

    @Override
    public IcsContractInstall findContractInfo(String contractNo, String projectName) {
        return icsContractInstallMapper.selectOne(Wrappers.lambdaQuery(IcsContractInstall.class)
                                                          .eq(IcsContractInstall::getContractNo, contractNo)
                                                          .eq(IcsContractInstall::getProjectName, projectName));
    }

    @Override
    public boolean createOrUpdateContractInfo(IcsContractInstall icsContractInstall) {
        IcsContractInstall contractInfo = findContractInfo(icsContractInstall.getContractNo(), icsContractInstall.getProjectName());
        //创建
        if (Objects.isNull(contractInfo)) {
            return icsContractInstallMapper.insert(icsContractInstall) > 0;
        }
        //更新
        return updateContractInfo(icsContractInstall);
    }

    @Override
    public ExcelWriter exportContractInfo(InstallContractDTO installContractDTO) {
        List<InstallContractVO> contractInfoList = findContractInfoList(installContractDTO);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("contractNo", messageSourceUtil.getMessage("version.contract.contractNo"));
        writer.addHeaderAlias("projectName", messageSourceUtil.getMessage("version.contract.projectName"));
        writer.addHeaderAlias("stationCount", messageSourceUtil.getMessage("version.contract.stationCount"));
        writer.addHeaderAlias("fsuCount", messageSourceUtil.getMessage("version.contract.fsuCount"));
        writer.addHeaderAlias("equipmentCount", messageSourceUtil.getMessage("version.contract.equipmentCount"));
        writer.addHeaderAlias("primaryDate",messageSourceUtil.getMessage("version.contract.primaryDate"));
        writer.addHeaderAlias("endDate", messageSourceUtil.getMessage("version.contract.endDate"));
        writer.addHeaderAlias("qualityStartPoint", messageSourceUtil.getMessage("version.contract.qualityStartPoint"));
        writer.addHeaderAlias("qualityPeriod", messageSourceUtil.getMessage("version.contract.qualityPeriod"));
        writer.addHeaderAlias("qualityTerms", messageSourceUtil.getMessage("version.contract.qualityTerms"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(contractInfoList);
        return writer;
    }

    @Override
    public boolean batchUpdateContractInfo(List<IcsContractInstall> icsContractInstallList) {
        if (CollUtil.isEmpty(icsContractInstallList)) {
            return false;
        }
        return icsContractInstallMapper.batchUpdateContractInfo(icsContractInstallList) > 0;
    }

    private boolean updateContractInfo(IcsContractInstall icsContractInstall) {
        return icsContractInstallMapper.update(Wrappers.lambdaUpdate(IcsContractInstall.class)
                                                       .set(IcsContractInstall::getPrimaryDate, icsContractInstall.getPrimaryDate())
                                                       .set(IcsContractInstall::getEndDate, icsContractInstall.getEndDate())
                                                       .set(IcsContractInstall::getQualityStartPoint, icsContractInstall.getQualityStartPoint())
                                                       .set(IcsContractInstall::getQualityPeriod, icsContractInstall.getQualityPeriod())
                                                       .set(IcsContractInstall::getQualityTerms, icsContractInstall.getQualityTerms())
                                                       .eq(IcsContractInstall::getContractNo, icsContractInstall.getContractNo())
                                                       .eq(IcsContractInstall::getProjectName, icsContractInstall.getProjectName())) > 0;
    }
}
