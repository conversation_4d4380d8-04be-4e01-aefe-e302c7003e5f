package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.IcsContractMaintenanceDTO;
import com.siteweb.as.entity.IcsContractMaintenance;

import java.util.List;

public interface IcsContractMaintenanceService {
    Page<IcsContractMaintenance> findContractInfoPage(IcsContractMaintenanceDTO contractMaintenanceDTO);

    IcsContractMaintenance findContractInfoById(Integer id);

    IcsContractMaintenance createContractInfo(IcsContractMaintenance icsContractMaintenance);

    boolean deleteContractInfoById(Integer id);

    IcsContractMaintenance updateContractInfo(IcsContractMaintenance icsContractMaintenance);

    ExcelWriter exportContractInfo(IcsContractMaintenanceDTO contractMaintenanceDTO);

    boolean deleteContractInfoByIds(List<Integer> idList);
}
