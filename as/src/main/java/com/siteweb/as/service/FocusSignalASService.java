package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.as.dto.FocusSignalASDTO;
import com.siteweb.as.dto.FocusSignalFilterASDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface FocusSignalASService {
    Page<FocusSignalASDTO> queryPageableFocusSignals(Integer userId,FocusSignalFilterASDTO focusSignalFilterASDTO);

    ExcelWriter findFocusSignalExcelWriter(Integer userId, FocusSignalFilterASDTO focusSignalFilterASDTO, List<String> columns, List<String> titles);
}
