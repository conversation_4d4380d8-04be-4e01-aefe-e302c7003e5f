package com.siteweb.as.service;

import com.siteweb.as.dto.EventComplexFilterDto;
import com.siteweb.as.vo.CustomTemplateVO;

import java.util.List;

public interface HistorySelectionService {
    Integer saveEventFilter(Integer userId, CustomTemplateVO customTemplate);
    List<EventComplexFilterDto> findByUserIdAndFilterType(Integer userId, String filterType);
    int deleteById(Integer historySelectionId);
}
