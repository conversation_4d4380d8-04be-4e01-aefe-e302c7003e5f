package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.monitoring.dto.BatchSetStationMaskDTO;
import com.siteweb.as.dto.shield.StationShieldDTO;
import com.siteweb.monitoring.dto.StationMaskDTO;
import com.siteweb.monitoring.vo.StationMaskVO;

import java.util.List;

public interface StationShieldService {
    ExcelWriter exportShield(ShieldFilterDTO shieldFilterDTO, Integer userId);

    /**
     * 获取局站屏蔽信息
     * @param stationId 局站id
     * @return {@link StationMaskDTO }
     */
    StationMaskDTO getStationMaskById(Integer stationId);

    /**
     * 获取分页的局站屏蔽信息
     *
     * @param page            分页信息
     * @param shieldFilterDTO
     * @param userId          用户id
     * @return {@link Page }<{@link StationShieldDTO }>
     */
    Page<StationShieldDTO> findStationMaksPage(Page<StationMaskVO> page, ShieldFilterDTO shieldFilterDTO, Integer userId);

    /**
     * 批量删除局站屏蔽信息
     * @param userId 用户id
     * @param stationIds 局站ids
     * @return int
     */
    int batchDeleteStationMask(Integer userId, List<Integer> stationIds);

    boolean batchSetStationMask(Integer userId, BatchSetStationMaskDTO batchSetStationMaskDTO);
}
