package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.FocusSignalASDTO;
import com.siteweb.as.dto.FocusSignalFilterASDTO;
import com.siteweb.as.service.ActiveEventFilterService;
import com.siteweb.as.service.FocusSignalASService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.monitoring.dto.ActiveSignal;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.StationFilterDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.entity.StationStructure;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.mapper.StationMapper;
import com.siteweb.monitoring.model.RealTimeSignalKey;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.StationStructureService;
import com.siteweb.monitoring.util.FocusSignalUtil;
import com.siteweb.monitoring.vo.EquipmentFilterVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR> zhou
 * @description FocusSignalServiceImpl
 * @createTime 2022-08-05 16:19:31
 */
@Slf4j
@Service
public class FocusSignalASServiceImpl implements FocusSignalASService {
    private static final Integer MAX_QUERY_COUNTS = 10000;

    @Autowired
    EquipmentService equipmentService;
    @Autowired
    StationMapper stationMapper;
    @Autowired
    StationManager stationManager;
    @Autowired
    ConfigSignalManager configSignalManager;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    SignalSubscribeManager signalSubscribeManager;
    @Autowired
    ActiveEventFilterService activeEventFilterService;

    @Override
    public Page<FocusSignalASDTO> queryPageableFocusSignals(Integer userId,FocusSignalFilterASDTO focusSignalFilterASDTO) {
        List<FocusSignalASDTO> list = findFocusSignalASDTO(userId, focusSignalFilterASDTO);
        List<FocusSignalASDTO> records = list.stream()
                                             .skip((focusSignalFilterASDTO.getCurrent() - 1L) * focusSignalFilterASDTO.getSize())
                                             .limit(focusSignalFilterASDTO.getSize())
                                             .toList();
        Page<FocusSignalASDTO> page = focusSignalFilterASDTO.generatePaginationInfo();
        page.setTotal(list.size());
        page.setRecords(records);
        subscribeSignal(records);
        return page;
    }

    /**
     * 订阅设备信号
     * @param records 订阅信息  key 设备id  value 对应设备的信号ids
     */
    private void subscribeSignal(List<FocusSignalASDTO> records) {
        Map<Integer, List<Integer>> equipmentSignalsMap = records.stream()
                                                                 .collect(Collectors.groupingBy(FocusSignalASDTO::getEquipmentId, Collectors.mapping(FocusSignalASDTO::getSignalId, Collectors.toList())));
        signalSubscribeManager.sendSignalSubscribe(equipmentSignalsMap);
    }

    public List<FocusSignalASDTO> findFocusSignalASDTO(Integer userId,FocusSignalFilterASDTO focusSignalFilterASDTO){
        Set<Integer> equipmentIdsByUserId = equipmentService.findEquipmentIdsByUserId(userId);
        List<Equipment> equipmentDTOList = equipmentManager.getEquipmentByIds(equipmentIdsByUserId);
        if (CollUtil.isEmpty(equipmentDTOList)) {
            return Collections.emptyList();
        }
        List<Integer> equipmentIds = getEquipmentIdsByFilterCondition(focusSignalFilterASDTO, equipmentDTOList);
        Map<Integer, List<ConfigSignalItem>> configSignalMapByEquipmentIds = configSignalManager.getConfigSignalMapByEquipmentIds(equipmentIds);
        Map<Integer, List<ConfigSignalItem>> equipmentSignalConfigMap = new HashMap<>();
        List<Signal> signalIdBySignalBaseEntryIds = CollUtil.isNotEmpty(focusSignalFilterASDTO.getBaseTypeIdList()) ?
                activeEventFilterService.findSignalBySignalBaseEntryIds(focusSignalFilterASDTO.getBaseTypeIdList().stream().toList()): Collections.emptyList();
        configSignalMapByEquipmentIds.forEach((equipmentId, value) -> {
            Stream<ConfigSignalItem> stream = value.stream();
            if (CollUtil.isNotEmpty(focusSignalFilterASDTO.getSignalCategoryList())) {
                stream = stream.filter(config -> focusSignalFilterASDTO.getSignalCategoryList().contains(config.getSignalCategory()));
            }
            if (CharSequenceUtil.isNotBlank(focusSignalFilterASDTO.getSignalName())) {
                stream = stream.filter(config -> config.getSignalName().contains(focusSignalFilterASDTO.getSignalName()));
            }
            if (CollUtil.isNotEmpty(focusSignalFilterASDTO.getBaseTypeIdList())) {
                stream = stream.filter(config -> signalIdBySignalBaseEntryIds.stream()
                        .anyMatch(a -> ObjectUtil.equals(a.getSignalId(), config.getSignalId())
                                && ObjectUtil.equals(a.getEquipmentTemplateId(), config.getEquipmentTemplateId())
                                && ObjectUtil.isNotNull(config.getBaseTypeId())));
            }
            List<ConfigSignalItem> filteredValue = stream.toList();
            if (CollUtil.isNotEmpty(filteredValue)) {
                equipmentSignalConfigMap.put(equipmentId, filteredValue);
            }
        });
        int count = equipmentSignalConfigMap.values().stream().mapToInt(List::size).sum();
        if (count > MAX_QUERY_COUNTS) {
            throw new BusinessException("reach max query counts");
        }
        List<FocusSignalASDTO> result = new ArrayList<>(count);
        Map<String, ActiveSignal> equipmentActiveSignalMap = findMapByConfigSignalItems(equipmentSignalConfigMap,focusSignalFilterASDTO.getCompareValue1Str(),focusSignalFilterASDTO.getCompareValue2Str());
        List<String> compareValue1List = FocusSignalUtil.getCompareValueList(focusSignalFilterASDTO.getCompareValue1Str());
        List<String> compareValue2List = FocusSignalUtil.getCompareValueList(focusSignalFilterASDTO.getCompareValue2Str());
        Boolean compareValueExist = CollUtil.isNotEmpty(compareValue1List) || CollUtil.isNotEmpty(compareValue2List);
        StationStructure postalStructure = stationStructureService.findPostalStructure();
        Map<Integer, String> stationStructureMap = getStationStructureMap(equipmentIds);
        //stationStructureService.findByStationIds()
        equipmentSignalConfigMap.forEach((equipmentId, configSignalItems) -> {
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            Station station = stationManager.findStationById(equipment.getStationId());
            for (ConfigSignalItem configSignalItem : configSignalItems) {
                FocusSignalASDTO focusSignalASDTO = new FocusSignalASDTO();
                if (Boolean.TRUE.equals(compareValueExist) && !equipmentActiveSignalMap.containsKey(equipment.getEquipmentId() + "." + configSignalItem.getSignalId())){
                    //筛选模板筛选了信号值范围，跳过不存在
                    continue;
                }
                ActiveSignal activeSignal = equipmentActiveSignalMap.get(equipment.getEquipmentId() + "." + configSignalItem.getSignalId());
                focusSignalASDTO.setEquipmentId(equipmentId);
                focusSignalASDTO.setEquipmentName(equipment.getEquipmentName());
                focusSignalASDTO.setSignalId(configSignalItem.getSignalId());
                focusSignalASDTO.setSignalName(configSignalItem.getSignalName());
                focusSignalASDTO.setCenterName(postalStructure.getStructureName());
                focusSignalASDTO.setStationStructure(stationStructureMap.get(equipment.getStationId()));
                focusSignalASDTO.setStationId(station.getStationId());
                focusSignalASDTO.setStationName(station.getStationName());
                if (ObjectUtil.isNotNull(activeSignal)){
                    //避免activeSignal为空报错
                    focusSignalASDTO.setValue(Objects.isNull(activeSignal.getOriginalValue()) ? null : Double.valueOf(activeSignal.getOriginalValue()));
                    focusSignalASDTO.setCurrentValue(activeSignal.getCurrentValue());
                    focusSignalASDTO.setSignalUnit(activeSignal.getUnit());
                    focusSignalASDTO.setSampleTime(activeSignal.getSampleTime());
                }
                result.add(focusSignalASDTO);
            }
        });
        return result;
    }

    private Map<Integer, String> getStationStructureMap(List<Integer> equipmentIds) {
        EquipmentFilterVo equipmentFilterVo = new EquipmentFilterVo();
        equipmentFilterVo.setEquipmentIds(new HashSet<>(equipmentIds));
        List<Equipment> equipmentList = equipmentManager.getEquipmentsByEquipmentDto(equipmentFilterVo);
        List<Integer> stationIds = equipmentList.stream().map(Equipment::getStationId).toList();
        return stationStructureService.findStationStructureMap(stationIds);
    }

    private Map<String,ActiveSignal> findMapByConfigSignalItems(Map<Integer, List<ConfigSignalItem>> equipmentSignalsMap, String compareValue1Str, String compareValue2Str){
        List<RealTimeSignalKey> keys = new ArrayList<>();
        equipmentSignalsMap.forEach((equipmentId, configSignalItemList) -> {
            for (ConfigSignalItem configSignalItem : configSignalItemList) {
                keys.add(new RealTimeSignalKey(equipmentId, configSignalItem.getSignalId()));
            }
        });
        List<ActiveSignal> activeSignalsByKeys = activeSignalManager.getActiveSignalsByKeys(keys);
        List<String> compareValue1List = FocusSignalUtil.getCompareValueList(compareValue1Str);
        List<String> compareValue2List = FocusSignalUtil.getCompareValueList(compareValue2Str);
        Stream<ActiveSignal> stream = activeSignalsByKeys.stream();
        if (CollUtil.isNotEmpty(compareValue1List)) {
            stream = stream.filter(e -> FocusSignalUtil.matchRealTimeSignalItemByCompareValue(e, compareValue1List));
        }
        if (CollUtil.isNotEmpty(compareValue2List)) {
            stream = stream.filter(e -> FocusSignalUtil.matchRealTimeSignalItemByCompareValue(e, compareValue2List));
        }
        return stream.collect(Collectors.toMap(ActiveSignal::getRedisKey, Function.identity()));
    }

    private List<Integer> getEquipmentIdsByFilterCondition(FocusSignalFilterASDTO focusSignalFilterASDTO, List<Equipment> equipmentDTOList) {
        StationFilterDTO stationFilterDTO = getStationFilterDTO(focusSignalFilterASDTO);
        Set<Integer> stationIds = stationMapper.findIdsByFilterCondition(stationFilterDTO);
        Stream<Equipment> stream = equipmentDTOList.stream().filter(e -> stationIds.contains(e.getStationId()));
        if (CollUtil.isNotEmpty(focusSignalFilterASDTO.getEquipmentCategoryList())) {
            stream = stream.filter(e -> focusSignalFilterASDTO.getEquipmentCategoryList().contains(e.getEquipmentCategory()));
        }
        if (CollUtil.isNotEmpty(focusSignalFilterASDTO.getEquipmentIdList())) {
            stream = stream.filter(e -> focusSignalFilterASDTO.getEquipmentIdList().contains(e.getEquipmentId()));
        }
        return stream.map(Equipment::getEquipmentId).toList();
    }

    private StationFilterDTO getStationFilterDTO(FocusSignalFilterASDTO focusSignalFilterASDTO) {
        return StationFilterDTO.builder()
                               .stationGroupType(focusSignalFilterASDTO.getStationGroupType())
                               .stationStructureList(focusSignalFilterASDTO.getStationStructureList())
                               .stationCategoryList(focusSignalFilterASDTO.getStationCategoryList())
                               .stationStateList(focusSignalFilterASDTO.getStationStateList())
                               .stationIdList(focusSignalFilterASDTO.getStationIdList())
                               .build();
    }

    @Override
    public ExcelWriter findFocusSignalExcelWriter(Integer userId, FocusSignalFilterASDTO focusSignalFilterASDTO, List<String> columns, List<String> titles) {
        List<FocusSignalASDTO> focusSignalASDTO = findFocusSignalASDTO(userId, focusSignalFilterASDTO);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        for (int i = 0; i < columns.size(); i++) {
            writer.addHeaderAlias(columns.get(i), titles.get(i));
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(focusSignalASDTO,true);
        return writer;
    }

}
