package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.StationProjectInfoDTO;
import com.siteweb.as.vo.version.StationProjectInfoVO;

import java.util.List;

public interface StationProjectInfoService {
    Page<StationProjectInfoVO> findContractInfoPage(Integer userId, StationProjectInfoDTO stationProjectInfoDTO);

    StationProjectInfoVO findContractInfoByStationId(Integer stationId);

    boolean updateContractInfo(StationProjectInfoDTO stationProjectInfoDTO);

    ExcelWriter exportContractInfo(StationProjectInfoDTO stationProjectInfoDTO, Integer userId);

    boolean batchUpdateContractInfo(List<StationProjectInfoDTO> stationProjectInfoDTOList);
}
