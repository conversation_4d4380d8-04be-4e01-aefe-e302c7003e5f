package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.entity.IcsFsuFtpDownloadInfo;
import com.siteweb.as.mapper.IcsFsuFtpDownloadInfoMapper;
import com.siteweb.as.net.connect.SshClient;
import com.siteweb.as.net.connect.TelnetClient;
import com.siteweb.as.net.transfer.FileTransferClient;
import com.siteweb.as.net.transfer.FileTransferClientFactory;
import com.siteweb.as.service.FSUService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.IcsFsuDataNewInfoDTO;
import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.dto.UserCredentialsDTO;
import com.siteweb.monitoring.mapper.FsuMapper;
import com.siteweb.monitoring.service.MonitorUnitExtendService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.monitoring.vo.FsuFilterVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class FSUServiceImpl implements FSUService {
    private static final String HOME = "/home/<USER>";
    @Autowired
    FsuMapper fsuMapper;
    @Autowired
    IcsFsuFtpDownloadInfoMapper icsFsuFtpDownloadInfoMapper;
    @Autowired
    StationService stationService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    MonitorUnitExtendService monitorUnitExtendService;

    @Override
    public List<IdValueDTO<String, Integer>> findFsuTypeStatistics(Integer userId) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return Collections.emptyList();
        }
        return fsuMapper.findFsuTypeStatistics(stationIdSet);
    }

    @Override
    public List<IdValueDTO<String, Integer>> findSiteUnitStatistics(Integer userId) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return Collections.emptyList();
        }
        return fsuMapper.findSiteUnitStatistics(stationIdSet);
    }

    @Override
    public List<IdValueDTO<String, Integer>> findFsuFlashUsedStatistics(Integer userId) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return Collections.emptyList();
        }
        return fsuMapper.findFsuFlashUsedStatistics(stationIdSet);
    }

    @Override
    public Page<IcsFsuDataNewInfoDTO> findFsuPage(Integer userId, Page<IcsFsuDataNewInfoDTO> page, FsuFilterVo filterVo) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        filterVo.setNotMuCategoryList(List.of(0, 1, 3, 24));
        return fsuMapper.findFsuPage(page,stationIdSet, filterVo);
    }

    @Override
    public ExcelWriter exportFsu(FsuFilterVo filterVo, Integer userId) {
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("SiteName", messageSourceUtil.getMessage("version.fsu.siteName"));
        writer.addHeaderAlias("FsuType", messageSourceUtil.getMessage("version.fsu.fsuType"));
        writer.addHeaderAlias("Hw", messageSourceUtil.getMessage("version.fsu.hw"));
        writer.addHeaderAlias("SN", messageSourceUtil.getMessage("version.fsu.sn"));
        writer.addHeaderAlias("Mac",messageSourceUtil.getMessage("version.fsu.mac"));
        writer.addHeaderAlias("Ip", messageSourceUtil.getMessage("version.fsu.ip"));
        writer.addHeaderAlias("MemTotal", messageSourceUtil.getMessage("version.fsu.memTotal"));
        writer.addHeaderAlias("FlashSize", messageSourceUtil.getMessage("version.fsu.flashSize"));
        writer.addHeaderAlias("Linux", messageSourceUtil.getMessage("version.fsu.linux"));
        writer.addHeaderAlias("SiteVersion", messageSourceUtil.getMessage("version.fsu.siteVersion"));
        writer.addHeaderAlias("CpuUsage", messageSourceUtil.getMessage("version.fsu.cpuUsage"));
        writer.addHeaderAlias("MemUsage", messageSourceUtil.getMessage("version.fsu.memUsage"));
        writer.addHeaderAlias("FlashUsedRate", messageSourceUtil.getMessage("version.fsu.flashUsedRate"));
        writer.addHeaderAlias("CollectTime", messageSourceUtil.getMessage("version.fsu.collectTime"));
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return writer;
        }
        List<IcsFsuDataNewInfoDTO> allFsu = fsuMapper.findAllFsu(stationIdSet, filterVo);
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(allFsu);
        return writer;
    }

    @Override
    public List<IcsFsuFtpDownloadInfo> findFtpDownLoadFile(String ip) {
        return icsFsuFtpDownloadInfoMapper.selectList(Wrappers.lambdaQuery(IcsFsuFtpDownloadInfo.class)
                                                              .eq(IcsFsuFtpDownloadInfo::getFsuIp, ip)
                                                              .orderByAsc(IcsFsuFtpDownloadInfo::getDownLoadTime));
    }

    @Override
    public boolean fileDirectoryExists(String fileDirectory) {
        return PathUtil.exists(Path.of(fileDirectory), true);
    }


    /**
     * 检查指定IP的设备类型是否与期望的FSU类型匹配
     *
     * @param ip 设备IP地址
     * @param expectedFsuType 期望的FSU类型
     * @return 如果设备类型与期望类型匹配则返回true，否则返回false
     */
    @Override
    public boolean checkFsuType(String ip, String expectedFsuType) {
        // 获取用户凭证
        UserCredentialsDTO userCredentials = monitorUnitExtendService.findUserCredentials(ip);
        String username = userCredentials.getUsername();
        String password = userCredentials.getPassword();

        try {
            // 尝试通过Telnet获取设备类型
            String deviceType = tryGetDeviceTypeViaTelnet(ip, username, password);

            // 如果Telnet失败，尝试通过SSH获取设备类型
            if (deviceType == null) {
                deviceType = tryGetDeviceTypeViaSsh(ip, username, password);
            }

            // 如果无法获取设备类型，返回false
            if (deviceType == null) {
                return false;
            }

            // 比较设备类型与期望类型
            return CharSequenceUtil.equalsAnyIgnoreCase(expectedFsuType, deviceType);
        } catch (Exception e) {
            log.error("检查设备类型异常 - IP: {}, 期望FSU类型: {}", ip, expectedFsuType, e);
            return false;
        }
    }

    /**
     * 尝试通过Telnet连接获取设备类型
     *
     * @return 成功返回设备类型，失败返回null
     */
    private String tryGetDeviceTypeViaTelnet(String ip, String username, String password) {
        try (TelnetClient telnetClient = new TelnetClient(ip, username, password)) {
            if (telnetClient.connect()) {
                return telnetClient.getDeviceType();
            }
        } catch (Exception e) {
            log.debug("Telnet连接失败 - IP: {}", ip, e);
        }
        return null;
    }

    /**
     * 尝试通过SSH连接获取设备类型
     *
     * @return 成功返回设备类型，失败返回null
     */
    private String tryGetDeviceTypeViaSsh(String ip, String username, String password) {
        try (SshClient sshClient = new SshClient(ip, username, password)) {
            if (sshClient.connect()) {
                return sshClient.getDeviceType();
            }
        } catch (Exception e) {
            log.debug("SSH连接失败 - IP: {}", ip, e);
        }
        return null;
    }


    @Override
    public boolean resetFsu(String fsuIp, String fileDirectory) throws IOException {
        UserCredentialsDTO userCredentials = monitorUnitExtendService.findUserCredentials(fsuIp);
        if (Objects.isNull(userCredentials)) {
            log.error("{},配置没有下发保存过采集器的用户名与密码，无法还原fsu", fsuIp);
            return false;
        }
        boolean result = uploadDirectory(fsuIp, fileDirectory, userCredentials);
        rebootFsu(fsuIp,userCredentials);
        return result;
    }

    @Override
    public List<IdValueDTO<String, Integer>> findFsuVersionStatusStatistics(Integer userId) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return Collections.emptyList();
        }
        return fsuMapper.findFsuVersionStatusStatistics(stationIdSet);
    }

    private boolean uploadDirectory(String fsuIp, String fileDirectory, UserCredentialsDTO userCredentials) throws IOException {
        boolean result;
        try (FileTransferClient fileTransferClient = FileTransferClientFactory.createClient(fsuIp, userCredentials.getUsername(), userCredentials.getPassword(), userCredentials.getPort(),userCredentials.getProtocol())) {
            fileTransferClient.login();
            fileDirectory = normalizeFilePath(fileDirectory);
            result = fileTransferClient.uploadDirectory(fileDirectory, HOME, true, "*");
        }
        return result;
    }

    /**
     * 重启指定IP的FSU设备（采集器）
     * 首先尝试通过Telnet连接重启，如果失败则尝试SSH连接重启
     *
     * @param fsuIp 要重启的FSU设备IP地址
     * @param userCredentials 连接设备所需的用户凭证
     */
    private void rebootFsu(String fsuIp, UserCredentialsDTO userCredentials) {
        String username = userCredentials.getUsername();
        String password = userCredentials.getPassword();

        log.info("开始尝试重启FSU设备，IP: {}", fsuIp);

        // 先尝试Telnet方式重启
        boolean telnetSuccess = tryRebootViaTelnet(fsuIp, username, password);

        // 如果Telnet方式失败，尝试SSH方式重启
        if (!telnetSuccess) {
            boolean sshSuccess = tryRebootViaSsh(fsuIp, username, password);

            if (!sshSuccess) {
                log.error("FSU设备重启失败，Telnet和SSH连接均失败，IP: {}", fsuIp);
            }
        }
    }

    /**
     * 尝试通过Telnet连接重启FSU设备
     *
     * @param ip 设备IP地址
     * @param username 用户名
     * @param password 密码
     * @return 重启操作是否成功
     */
    private boolean tryRebootViaTelnet(String ip, String username, String password) {
        try (TelnetClient telnetClient = new TelnetClient(ip, username, password)) {
            if (telnetClient.connect()) {
                log.info("通过Telnet连接重启FSU设备，IP: {}", ip);
                telnetClient.reboot();
                return true;
            }
            log.info("Telnet连接FSU设备失败，IP: {}", ip);
        } catch (Exception e) {
            log.error("通过Telnet重启FSU设备异常，IP: {}", ip, e);
        }
        return false;
    }

    /**
     * 尝试通过SSH连接重启FSU设备
     *
     * @param ip 设备IP地址
     * @param username 用户名
     * @param password 密码
     * @return 重启操作是否成功
     */
    private boolean tryRebootViaSsh(String ip, String username, String password) {
        try (SshClient sshClient = new SshClient(ip, username, password)) {
            if (sshClient.connect()) {
                log.info("通过SSH连接重启FSU设备，IP: {}", ip);
                sshClient.reboot();
                return true;
            }
            log.info("SSH连接FSU设备失败，IP: {}", ip);
        } catch (Exception e) {
            log.error("通过SSH重启FSU设备异常，IP: {}", ip, e);
        }
        return false;
    }


    /**
     * Normalize a file path by replacing "\\" and "\" with the standard file separator.
     *
     * @param path The original file path.
     * @return The normalized file path.
     */
    public static String normalizeFilePath(String path) {
        if (path == null || path.isEmpty()) {
            throw new IllegalArgumentException("Path cannot be null or empty");
        }

        // Replace "\\" and "\" with the standard file separator

        return path.replace("\\\\", "/").replace("\\", "/");
    }
}
