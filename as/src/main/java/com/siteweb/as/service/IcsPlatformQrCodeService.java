package com.siteweb.as.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.IcsPlatformQrcodeDTO;
import com.siteweb.as.entity.IcsPlatformQrCode;
import com.siteweb.as.vo.version.IcsPlatformQrcodeVO;

import java.util.List;

public interface IcsPlatformQrCodeService{
    Page<IcsPlatformQrcodeVO> findPage(IcsPlatformQrcodeDTO icsPlatformQrcodeDTO);

    IcsPlatformQrCode create(IcsPlatformQrCode icsPlatformQrCode);

    boolean deleteByIds(List<Integer> ids);

    IcsPlatformQrCode updateById(IcsPlatformQrCode icsPlatformQrCode);
}
