package com.siteweb.as.service;

import com.siteweb.as.dto.patrol.*;
import com.siteweb.as.entity.*;
import com.siteweb.utility.dto.ImportErrorInfoDTO;

import java.util.List;

public interface PatrolRuleService {

    /**
     * 获取全部规则配置
     */
    List<PatrolRuleDTO> getAllPatrolRule();

    /**
     * 获取规则配置详情
     */
    PatrolRuleDTO getPatrolRule(Integer ruleId);

    /**
     * 新增
     */
    void savePatrolRule(PatrolRule patrolRule);

    /**
     * 修改
     */
    void updatePatrolRule(PatrolRule patrolRule);

    void deletePatrolRule(Integer ruleId);

    /**
     * 获取计算操作符号
     */
    List<PatrolCalop> listPatrolCalop();
    /**
     * 获取单位信息
     */
    List<PatrolUnit> listPatrolUnit();
    /**
     * 获取预警等级信息
     */
    List<PatrolWarningLevel> listPatrolWarningLevel();
    /**
     * 获取基类设备
     */
    List<BaseEquipmentDTO> listBaseEquipment();

    /**
     * 获取基类信号
     */
    List<SignalBaseDicDTO> listBaseSignal(Integer baseEquipmentId);

    /**
     * 获取标准化信息
     */
    String listStandardInfo(Long baseTypeId);

    List<PatrolRuleDTO> getPatrolRuleByGroup(Integer groupId);

    /**
     * 导入
     */
    List<ImportErrorInfoDTO> importPatrolRules(List<PatrolRuleImportDTO> patrolRuleImportDTOS);

    /**
     * 获取标准化设备
     */
    List<StandardEquipmentDTO> listStandardEquipment();


    List<StandardSignalDTO>listStandardSignal(Integer equipmentLogicClassId);

    List<PatrolStandardRuleDTO> getAllPatrolStandardRule();

    PatrolStandardRuleDTO getPatrolStandardRule(Integer ruleId);

    List<PatrolStandardRuleDTO> getPatrolStandardRuleByGroup(Integer groupId);

    List<ImportErrorInfoDTO> importPatrolStandardRules(List<PatrolStandardRuleImportDTO> patrolRuleImportDTOS);
}
