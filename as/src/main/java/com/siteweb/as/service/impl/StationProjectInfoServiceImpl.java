package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.StationProjectInfoDTO;
import com.siteweb.as.entity.StationProjectInfo;
import com.siteweb.as.mapper.StationProjectInfoMapper;
import com.siteweb.as.service.StationProjectInfoService;
import com.siteweb.as.vo.version.StationProjectInfoVO;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class StationProjectInfoServiceImpl implements StationProjectInfoService {
    @Autowired
    StationProjectInfoMapper stationProjectInfoMapper;
    @Autowired
    StationService stationService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public Page<StationProjectInfoVO> findContractInfoPage(Integer userId, StationProjectInfoDTO stationProjectInfoDTO) {
        Set<Integer> stationIds = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIds)) {
            return stationProjectInfoDTO.generatePaginationInfo();
        }
        return stationProjectInfoMapper.findContractInfoPage(stationProjectInfoDTO.generatePaginationInfo(),stationIds,stationProjectInfoDTO);
    }

    @Override
    public StationProjectInfoVO findContractInfoByStationId(Integer stationId) {
        return stationProjectInfoMapper.findContractInfoByStationId(stationId);
    }

    @Override
    public boolean updateContractInfo(StationProjectInfoDTO stationProjectInfoDTO) {
        return stationProjectInfoMapper.update(Wrappers.lambdaUpdate(StationProjectInfo.class)
                                                       .set(StationProjectInfo::getContractNo, stationProjectInfoDTO.getContractNo())
                                                       .set(StationProjectInfo::getProjectName, stationProjectInfoDTO.getProjectName())
                                                       .eq(StationProjectInfo::getStationId, stationProjectInfoDTO.getStationId())) > 0;
    }

    private List<StationProjectInfoVO> findStationContractInfo(StationProjectInfoDTO stationProjectInfoDTO, Integer userId) {
        Set<Integer> stationIds = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptyList();
        }
        return stationProjectInfoMapper.findContractInfo(stationIds, stationProjectInfoDTO);
    }

    @Override
    public ExcelWriter exportContractInfo(StationProjectInfoDTO stationProjectInfoDTO, Integer userId) {
        List<StationProjectInfoVO> stationContractInfo = findStationContractInfo(stationProjectInfoDTO, userId);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("stationName", messageSourceUtil.getMessage("version.fsu.siteName"));
        writer.addHeaderAlias("projectName", messageSourceUtil.getMessage("version.contract.projectName"));
        writer.addHeaderAlias("contractNo", messageSourceUtil.getMessage("version.contract.contractNo"));
        writer.addHeaderAlias("installTime", messageSourceUtil.getMessage("version.contract.installTime"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(stationContractInfo);
        return writer;
    }

    @Override
    public boolean batchUpdateContractInfo(List<StationProjectInfoDTO> stationProjectInfoDTOList) {
        if (CollUtil.isEmpty(stationProjectInfoDTOList)) {
            return false;
        }
        return stationProjectInfoMapper.batchUpdate(stationProjectInfoDTOList) > 0;
    }
}
