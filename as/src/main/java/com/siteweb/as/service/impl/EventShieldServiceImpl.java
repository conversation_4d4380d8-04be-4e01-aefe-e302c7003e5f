package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.service.AccountService;
import com.siteweb.as.dto.shield.EventShieldDTO;
import com.siteweb.as.dto.shield.EventShieldExportDTO;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.as.mapper.EventShieldMapper;
import com.siteweb.as.service.EventShieldService;
import com.siteweb.as.vo.MaskTimeGroupVO;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.BatchCreateEventMaskDTO;
import com.siteweb.monitoring.dto.EventMaskDTO;
import com.siteweb.monitoring.dto.TimeGroupSpanDTO;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.enumeration.TimeGroupCategoryEnum;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.EventMaskService;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import com.siteweb.monitoring.vo.StationMaskVO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("signalShieldService")
public class EventShieldServiceImpl implements EventShieldService {
    @Autowired
    EventShieldMapper eventShieldMapper;

    @Autowired
    EventMaskService eventMaskService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    TimeGroupSpanService timeGroupSpanService;
    @Autowired
    AccountService accountService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Override
    public boolean deleteShield(Integer userId, List<String> ids) {
        eventMaskService.batchDeleteEventMasks(ids, userId);
        return true;
    }

    @Override
    public ExcelWriter exportShield(ShieldFilterDTO shieldFilterDTO, Integer userId) {
        List<EventShieldDTO> eventShieldList = findEventShield(shieldFilterDTO,userId);
        List<EventShieldExportDTO> eventShieldExportDTOList = eventShieldList.stream().map(e-> new EventShieldExportDTO(e,localeMessageSourceUtil)).toList();
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("centerName", localeMessageSourceUtil.getMessage("shield.centerName"));
        writer.addHeaderAlias("stationName", localeMessageSourceUtil.getMessage("shield.stationName"));
        writer.addHeaderAlias("equipmentName", localeMessageSourceUtil.getMessage("shield.equipmentName"));
        writer.addHeaderAlias("eventName", localeMessageSourceUtil.getMessage("shield.eventName"));
        writer.addHeaderAlias("userName", localeMessageSourceUtil.getMessage("shield.userName"));
        writer.addHeaderAlias("startTime", localeMessageSourceUtil.getMessage("shield.startTime"));
        writer.addHeaderAlias("endTime", localeMessageSourceUtil.getMessage("shield.endTime"));
        writer.addHeaderAlias("shieldTypeName",localeMessageSourceUtil.getMessage("shield.shieldTypeName"));
        writer.addHeaderAlias("description", localeMessageSourceUtil.getMessage("shield.description"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(eventShieldExportDTOList);
        return writer;
    }


    public List<EventShieldDTO> findEventShield(ShieldFilterDTO shieldFilterDTO, Integer userId) {
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            shieldFilterDTO.setEquipmentIdList(equipmentService.findEquipmentIdsByUserId(userId));
        }
        //没有权限直接 返回
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            return Collections.emptyList();
        }
        List<EventShieldDTO> eventShield = eventShieldMapper.findEventShield(shieldFilterDTO);
        //附加信息
        setExtendedInfo(eventShield);
        return eventShield;
    }

    @Override
    public Page<EventShieldDTO> findEventMaksPage(Page<StationMaskVO> page, ShieldFilterDTO shieldFilterDTO, Integer userId) {
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            shieldFilterDTO.setEquipmentIdList(equipmentService.findEquipmentIdsByUserId(userId));
        }
        if (CollUtil.isEmpty(shieldFilterDTO.getEquipmentIdList())) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        page.setOptimizeCountSql(false);
        //附加信息
        Page<EventShieldDTO> eventMaskPage = eventShieldMapper.findEventShieldPage(page, shieldFilterDTO);
        setExtendedInfo(eventMaskPage.getRecords());
        return eventMaskPage;
    }

    @Override
    public boolean batchSaveShield(Integer userId, BatchCreateEventMaskDTO batchCreateEventMaskDTO) {
        eventMaskService.batchSaveMask(userId,batchCreateEventMaskDTO);
        return true;
    }

    private void setExtendedInfo(List<EventShieldDTO> records) {
        List<Integer> timeGroupIds = records.stream().map(EventShieldDTO::getTimeGroupId).toList();
        List<TimeGroupSpan> timeGroupSpanList = timeGroupSpanService.findByTimeGroupIds(timeGroupIds);
        Set<Integer> timeGroupSpanIdSet = timeGroupSpanList.stream().map(TimeGroupSpan::getTimeGroupId).collect(Collectors.toSet());
        Map<Integer, String> userNameMap = accountService.findUserNameMapByUserIds(records.stream().map(
                EventShieldDTO::getUserId).toList());
        records.forEach(e -> {
            if (timeGroupSpanIdSet.contains(e.getTimeGroupId())) {
                e.setTimeGroupCategory(TimeGroupCategoryEnum.TIME_PERIOD_MASK.getValue());
            }
            if (Objects.nonNull(e.getStartTime())) {
                e.setTimeGroupCategory(TimeGroupCategoryEnum.FULL_TIME_MASK.getValue());
            }
            e.setUserName(userNameMap.get(e.getUserId()));
        });
    }

    public static MaskTimeGroupVO convertSignalMaskDTOToMaskTimeGroupVO(EventMaskDTO dto) {
        if (dto == null) {
            return null;
        }

        MaskTimeGroupVO maskTimeGroupVO = new MaskTimeGroupVO();
        maskTimeGroupVO.setDataId(dto.getTimeGroupId());
        maskTimeGroupVO.setGroupCategory(dto.getTimeGroupCategory());
        maskTimeGroupVO.setReason(dto.getReason());
        maskTimeGroupVO.setException(false);
        maskTimeGroupVO.setType(null);
        maskTimeGroupVO.setSequenceId(null);

        maskTimeGroupVO.setStartTime(dto.getStartTime());
        maskTimeGroupVO.setEndTime(dto.getEndTime());

        // 转换timeGroupSpans列表
        List<MaskTimeGroupVO.TimeGroupSpan> timeGroupSpansVO = new ArrayList<>();
        if (dto.getTimeGroupSpans() != null) {
            for (TimeGroupSpanDTO timeGroupSpanDTO : dto.getTimeGroupSpans()) {
                MaskTimeGroupVO.TimeGroupSpan timeGroupSpanVO = getTimeGroupSpan(timeGroupSpanDTO);
                timeGroupSpansVO.add(timeGroupSpanVO);
            }
        }
        maskTimeGroupVO.setTimeGroupSpans(timeGroupSpansVO);

        return maskTimeGroupVO;
    }

    @NotNull
    private static MaskTimeGroupVO.TimeGroupSpan getTimeGroupSpan(TimeGroupSpanDTO timeGroupSpanDTO) {
        MaskTimeGroupVO.TimeGroupSpan timeGroupSpanVO = new MaskTimeGroupVO.TimeGroupSpan();
        var span = timeGroupSpanDTO.getTimeSpanBool();
        Collections.reverse(span);
        timeGroupSpanVO.setTimeSpanBool(span);
        timeGroupSpanVO.setWeek(timeGroupSpanDTO.getWeek());
        return timeGroupSpanVO;
    }
}
