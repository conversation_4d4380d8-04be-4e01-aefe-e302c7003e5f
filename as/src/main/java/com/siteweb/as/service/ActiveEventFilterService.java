package com.siteweb.as.service;

import cn.hutool.core.lang.tree.Tree;
import com.siteweb.as.vo.ValueLabelTreeNodeVO;
import com.siteweb.monitoring.dto.StationFilterDTO;
import com.siteweb.monitoring.dto.IdValueDTO;
import com.siteweb.monitoring.entity.Signal;
import com.siteweb.utility.enums.DataEntryEnum;

import javax.xml.crypto.dsig.keyinfo.KeyValue;
import java.util.List;

public interface ActiveEventFilterService {

    /**
     * 获取分组方式
     * @return {@link List }<{@link KeyValue }<{@link Integer },{@link String }>>
     */
    List<IdValueDTO<Integer, String>> findIdValueByEntryId(DataEntryEnum dataEntryEnum);
    /**
     * 获取局站分组名称 通过分组id
     *
     * @param structureGroupId 分组id
     * @return {@link List }<{@link IdValueDTO }<{@link Integer }, {@link String }>>
     */
    List<Tree<Integer>> findGroupingNameById(Integer structureGroupId);
    /**
     * @param userId            用户id
     * @param stationCategoryId 局站等级
     * @param stationState      局站状态
     * @param stationGroup      局站分组
     * @return {@link List }<{@link IdValueDTO }<{@link Integer }, {@link String }>>
     */
    List<IdValueDTO<Integer, String>> findStationName(Integer userId, StationFilterDTO stationFilterDTO);

    List<IdValueDTO<Integer,String>> findEquipmentName(Integer userId, StationFilterDTO stationFilterDTO);

    List<IdValueDTO<Long,String>> findEventStandardName();

    List<IdValueDTO<Integer,String>> findEventLevel();

    List<IdValueDTO<Integer,String>> findShieldMethod();

    List<IdValueDTO<Long,String>> findSignalBase();

    List<Signal> findSignalBySignalBaseEntryIds(List<Integer> signalBaseEntryIds);

}
