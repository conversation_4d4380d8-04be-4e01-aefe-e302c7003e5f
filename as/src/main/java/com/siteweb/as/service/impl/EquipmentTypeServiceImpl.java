package com.siteweb.as.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.entity.EquipmentType;
import com.siteweb.as.mapper.EquipmentTypeMapper;
import com.siteweb.as.service.EquipmentTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EquipmentTypeServiceImpl implements EquipmentTypeService {
    @Autowired
    EquipmentTypeMapper equipmentTypeMapper;

    @Override
    public List<EquipmentType> findTypeList(){
        return equipmentTypeMapper.selectList(Wrappers.emptyWrapper());
    }
}
