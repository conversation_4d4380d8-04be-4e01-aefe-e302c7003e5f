package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.FSUProjectInfoDTO;
import com.siteweb.as.vo.version.MonitorUnitProjectInfoVO;

import java.util.List;

public interface MonitorUnitProjectInfoService {
    Page<MonitorUnitProjectInfoVO> findContractInfoPage(Integer userId, FSUProjectInfoDTO fsuProjectInfoDTO);

    MonitorUnitProjectInfoVO findContractInfoByMonitorUnitId(Integer stationId);

    boolean updateContractInfo(FSUProjectInfoDTO fsuProjectInfoDTO);

    ExcelWriter exportContractInfo(FSUProjectInfoDTO fsuProjectInfoDTO, Integer userId);

    boolean batchUpdateContractInfo(List<FSUProjectInfoDTO> fsuProjectInfoDTOList);
}
