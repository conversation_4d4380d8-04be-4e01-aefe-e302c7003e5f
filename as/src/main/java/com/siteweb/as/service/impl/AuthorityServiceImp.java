package com.siteweb.as.service.impl;

import com.siteweb.admin.entity.OperationGroup;
import com.siteweb.admin.entity.SpecialtyGroup;
import com.siteweb.admin.entity.UserRoleMap;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.as.enums.BusinessObjectType;
import com.siteweb.as.mapper.ASMapper;
import com.siteweb.as.service.AuthorityService;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
@Service("authorityService")
public class AuthorityServiceImp implements AuthorityService {

    public static final int ADMIN_ID = -1;
    public static final int ADMIN_ROLE_ID = -1;
    public static final int ALL_SPECIALTY_ID = -1;
    public static final int ALL_STATION_ID = -1;
    @Autowired
    UserRoleMapper userRoleMapper;
    @Autowired
    private DataItemService dataItemService;

    @Autowired
    private StationService stationService;

    @Autowired
    ASMapper asMapper;
    @Override
    public List<Integer> findSpecialtyByUserId(Integer userId) {
        if(userId == ADMIN_ID){
            return dataItemService.findByEntryId(BusinessObjectType.EntryEquipmentCategory.value())
                    .stream()
                    .map(DataItem::getItemId)
                    .toList();
        }

        UserRoleMap userRoleMap = asMapper.getUserRoleMapByUserId(userId);
        if(userRoleMap == null){
            return Collections.emptyList();
        }
        if(userRoleMap.getRoleId().equals(ADMIN_ROLE_ID)){
            return dataItemService.findByEntryId(BusinessObjectType.EntryEquipmentCategory.value())
                    .stream()
                    .map(DataItem::getItemId)
                    .toList();
        }

        SpecialtyGroup specialtyGroup = asMapper.getSpecialtyGroupByUserId(userId);
        if(specialtyGroup == null){
            return Collections.emptyList();
        }
        if(specialtyGroup.getSpecialtyGroupId().equals(ALL_SPECIALTY_ID)){
            return dataItemService.findByEntryId(BusinessObjectType.EntryEquipmentCategory.value())
                    .stream()
                    .map(DataItem::getItemId)
                    .toList();
        }

        return asMapper.getSpecialtyByGroupId(specialtyGroup.getSpecialtyGroupId());
    }

    @Override
    public List<Station> findStationByUserId(Integer userId) {
        return stationService.findByUserId(userId);
    }

    @Override
    public List<Integer> findOperationByUserId(Integer userId) {

        if(userId.equals(ADMIN_ID)){
            return asMapper.getAllOperation();
        }

        UserRoleMap userRoleMap = asMapper.getUserRoleMapByUserId(userId);
        if(userRoleMap == null){
            return Collections.emptyList();
        }
        if(userRoleMap.getRoleId().equals(ADMIN_ROLE_ID)){
            return asMapper.getAllOperation();
        }

        OperationGroup operationGroup = asMapper.getOperationGroupByUserId(userId);
        if(operationGroup == null){
            return Collections.emptyList();
        }
        if(operationGroup.getGroupId().equals(ALL_STATION_ID)){
            return asMapper.getAllOperation();
        }

        return asMapper.getOperationByGroupId(operationGroup.getGroupId());
    }
}
