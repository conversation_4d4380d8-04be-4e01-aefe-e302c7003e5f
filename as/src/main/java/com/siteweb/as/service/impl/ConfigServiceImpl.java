package com.siteweb.as.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.as.dto.config.*;
import com.siteweb.as.enums.BusinessObjectType;
import com.siteweb.as.mapper.ConfigMapper;
import com.siteweb.as.service.ConfigService;
import com.siteweb.monitoring.entity.MonitorUnit;
import com.siteweb.monitoring.mamager.MonitorUnitManager;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;


@Service
@RequiredArgsConstructor
public class ConfigServiceImpl implements ConfigService {
    private final ConfigMapper configMapper;
    private final MonitorUnitManager monitorUnitManager;
    private final DataItemService dataItemService;

    @Override
    public Map<String, Object> getStationInfo(Integer stationId) {
        // 获取采集单元
        List<ConfigSamplerUnitDTO> samplerUnits = configMapper.getStationSUList(stationId);
        // 获取监控单元
        List<ConfigMonitorUnitDTO> rmuList = configMapper.getStationRMUList(stationId);
        List<MonitorUnit> mus = monitorUnitManager.findByStationId(stationId);
        Map<Integer, String> desItemMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.MONITOR_UNIT_TYPE.getValue());
        List<ConfigMonitorUnitDTO> muList = mus.stream().map(m -> {
            ConfigMonitorUnitDTO configMonitorUnitDTO = ConfigMonitorUnitDTO.toConfigMonitorUnitDTO(m);
            if (CharSequenceUtil.isBlank(configMonitorUnitDTO.getDescription())) {
                configMonitorUnitDTO.setDescription(desItemMap.get(configMonitorUnitDTO.getMonitorUnitCategory()));
            }
            return configMonitorUnitDTO;
        }).toList();
        List<ConfigMonitorUnitDTO> monitorUnits = Stream.concat(muList.stream(), rmuList.stream()).toList();
        // 获取局站维护公司 局站维护人
        String maintainInfo = configMapper.getMaintainInfo(stationId);
        String[] maintainInfoResult = new String[2];
        if (CharSequenceUtil.isNotBlank(maintainInfo)) {
            maintainInfoResult = maintainInfo.split(",");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("maintainInfo", maintainInfoResult);
        result.put("monitorUnits", monitorUnits);
        result.put("samplerUnits", samplerUnits);
        return result;
    }

    @Override
    public Object getConfigObject(String objectType, String uniqueId) {
        if ("ConfigStation".equals(objectType)) {
            StructureDTO structureDTO = configMapper.selectCenter();
            List<StationInfoDTO> stationInfoDTOList = configMapper.getConfigObject(Integer.valueOf(uniqueId));
            return stationInfoDTOList.stream().map(m->ConfigStation.toConfigStation(m,structureDTO)).toList();
        }
        return null;
    }

    @Override
    public Map<String, Object> getHouseInfo(Integer stationId) {
        List<ConfigHouseDTO> houseinfolist = configMapper.getHouseInfo(stationId);
        Map<String, Object> result = new HashMap<>();
        result.put("houseinfolist", houseinfolist);
        return result;
    }

    @Override
    public Object getCommonObject(String type) {
        Integer value = BusinessObjectType.getValueByName(type);
        List<DataItem> dataItemList = dataItemService.findByEntryId(value);
        return dataItemList.stream().map(d -> {
            CommonObjectDTO commonObjectDTO = new CommonObjectDTO();
            commonObjectDTO.setId(d.getItemId());
            commonObjectDTO.setName(d.getItemValue());
            commonObjectDTO.setInitialName(d.getItemValue());
            return commonObjectDTO;
        }).toList();
    }
}
