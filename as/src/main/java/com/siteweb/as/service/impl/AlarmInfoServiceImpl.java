package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.patrol.AlarmInfoDTO;
import com.siteweb.as.dto.patrol.AlarmInfoQueryDTO;
import com.siteweb.as.mapper.PatrolExrecordMapper;
import com.siteweb.as.service.AlarmInfoService;
import com.siteweb.as.util.PatrolRuleUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@RequiredArgsConstructor
public class AlarmInfoServiceImpl implements AlarmInfoService {
    private final PatrolExrecordMapper patrolExrecordMapper;
    private final LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public Map<String, Object> getAllAlarmInfo(AlarmInfoQueryDTO alarmInfoQueryDTO) {
        IPage<AlarmInfoDTO> allAlarmInfo = patrolExrecordMapper.getAllAlarmInfo(Page.of(alarmInfoQueryDTO.getPageNum(), alarmInfoQueryDTO.getPageSize()), alarmInfoQueryDTO);
        if (allAlarmInfo.getTotal() < 1) {
            Map<String, Object> result = new HashMap<>();
            result.put("Data", List.of());
            result.put("Total", allAlarmInfo.getTotal());
            return result;
        }
        allAlarmInfo.getRecords().forEach(alarmInfoDTO -> alarmInfoDTO.setLimitMeaning(PatrolRuleUtil.calculateLimitMeaning(alarmInfoDTO)));
        Map<String, Object> result = new HashMap<>();
        result.put("Data", allAlarmInfo.getRecords());
        result.put("Total", allAlarmInfo.getTotal());
        return result;
    }

    @Override
    public ExcelWriter exportAlarmInfo() {
        Date date = new Date();
        DateTime startTime = DateUtil.beginOfMonth(date);
        DateTime endTime = DateUtil.endOfMonth(date);
        List<AlarmInfoDTO> allAlarmInfos = patrolExrecordMapper.getAlarmInfoExport(startTime, endTime);
        if (CollUtil.isNotEmpty(allAlarmInfos)) {
            allAlarmInfos.forEach(alarmInfoDTO -> alarmInfoDTO.setLimitMeaning(PatrolRuleUtil.calculateLimitMeaning(alarmInfoDTO)));
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("centerName", messageSourceUtil.getMessage("patrol.alarm.centerName"));
        writer.addHeaderAlias("groupName", messageSourceUtil.getMessage("patrol.alarm.groupName"));
        writer.addHeaderAlias("stationName", messageSourceUtil.getMessage("patrol.alarm.stationName"));
        writer.addHeaderAlias("equipmentCategoryName", messageSourceUtil.getMessage("patrol.alarm.equipmentCategoryName"));
        writer.addHeaderAlias("equipmentId", messageSourceUtil.getMessage("patrol.alarm.equipmentId"));
        writer.addHeaderAlias("equipmentName", messageSourceUtil.getMessage("patrol.alarm.equipmentName"));
        writer.addHeaderAlias("signalName", messageSourceUtil.getMessage("patrol.alarm.signalName"));
        writer.addHeaderAlias("signalValue", messageSourceUtil.getMessage("patrol.alarm.signalValue"));
        writer.addHeaderAlias("recordTime", messageSourceUtil.getMessage("patrol.alarm.recordTime"));
        writer.addHeaderAlias("unit", messageSourceUtil.getMessage("patrol.alarm.unit"));
        writer.addHeaderAlias("byPercentageMeaning", messageSourceUtil.getMessage("patrol.alarm.byPercentageMeaning"));
        writer.addHeaderAlias("ratedValue", messageSourceUtil.getMessage("patrol.alarm.ratedValue"));
        writer.addHeaderAlias("warningMeaning", messageSourceUtil.getMessage("patrol.alarm.warningMeaning"));
        writer.addHeaderAlias("isPowerOffAlarm", messageSourceUtil.getMessage("patrol.alarm.isPowerOffAlarm"));
        writer.addHeaderAlias("createTime", messageSourceUtil.getMessage("patrol.alarm.createTime"));
        writer.addHeaderAlias("limitMeaning", messageSourceUtil.getMessage("patrol.alarm.limitMeaning"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        String month = DateUtil.format(date, DatePattern.NORM_MONTH_FORMAT);
        writer.renameSheet(String.format("%s%s%s%s", month, "月预警信息(->", DateUtil.format(date, "yyyyMMdd HHmm"), ")"));
        writer.write(allAlarmInfos);
        return writer;
    }
}
