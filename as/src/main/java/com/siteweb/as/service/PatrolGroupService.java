package com.siteweb.as.service;

import com.siteweb.as.dto.patrol.*;

import java.util.List;

public interface PatrolGroupService {
    List<PatrolGroupAllDTO> getAllPatrolGroup();

    PatrolGroupDTO getPatrolGroup(Integer groupId);

    void savePatrolGroup(PatrolGroupDTO patrolGroupDTO);

    void updatePatrolGroup(PatrolGroupDTO patrolGroupDTO);

    void deletePatrolGroup(Integer groupId);

    List<ValueLabelDTO> listStationtype();

    /**
     * 获取局站
     */
    List<ValueLabelDTO> listStation(String stationTypeId);

    /**
     * 获取分组参数
     */
    GroupParameterDTO getGroupParameter(Integer groupId);

    /**
     * 新增分组参数
     */
    void saveGroupParameter(GroupParameterDTO groupParameterDTO);


    List<PatrolStandardGroupAllDTO> getAllPatrolStandardGroup();

    PatrolStandardGroupDTO getPatrolStandardGroup(Integer groupId);


    void savePatrolStandardGroup(PatrolStandardGroupDTO patrolGroupDTO);

    void updatePatrolStandardGroup(PatrolStandardGroupDTO patrolGroupDTO);

    void deletePatrolStandardGroup(Integer groupId);

    GroupParameterDTO getStandardGroupParameter(Integer groupId);
}
