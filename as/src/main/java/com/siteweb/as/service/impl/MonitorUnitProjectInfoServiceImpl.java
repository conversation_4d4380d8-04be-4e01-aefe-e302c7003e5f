package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.FSUProjectInfoDTO;
import com.siteweb.as.entity.MonitorUnitProjectInfo;
import com.siteweb.as.mapper.MonitorUnitProjectInfoMapper;
import com.siteweb.as.service.MonitorUnitProjectInfoService;
import com.siteweb.as.vo.version.MonitorUnitProjectInfoVO;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.service.StationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class MonitorUnitProjectInfoServiceImpl implements MonitorUnitProjectInfoService {
    @Autowired
    MonitorUnitProjectInfoMapper monitorUnitProjectInfoMapper;
    @Autowired
    StationService stationService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public Page<MonitorUnitProjectInfoVO> findContractInfoPage(Integer userId, FSUProjectInfoDTO fsuProjectInfoDTO) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return fsuProjectInfoDTO.generatePaginationInfo();
        }
        return monitorUnitProjectInfoMapper.findContractInfoPage(fsuProjectInfoDTO.generatePaginationInfo(), stationIdSet, fsuProjectInfoDTO);
    }

    @Override
    public MonitorUnitProjectInfoVO findContractInfoByMonitorUnitId(Integer monitorUnitId) {
        return monitorUnitProjectInfoMapper.findContractInfoByMonitorUnitId(monitorUnitId);
    }

    @Override
    public boolean updateContractInfo(FSUProjectInfoDTO fsuProjectInfoDTO) {
        return monitorUnitProjectInfoMapper.update(Wrappers.lambdaUpdate(MonitorUnitProjectInfo.class)
                                                         .set(MonitorUnitProjectInfo::getContractNo, fsuProjectInfoDTO.getContractNo())
                                                         .set(MonitorUnitProjectInfo::getProjectName, fsuProjectInfoDTO.getProjectName())
                                                         .eq(MonitorUnitProjectInfo::getStationId, fsuProjectInfoDTO.getStationId())
                                                         .eq(MonitorUnitProjectInfo::getMonitorUnitId, fsuProjectInfoDTO.getMonitorUnitId())) > 0;
    }

    private List<MonitorUnitProjectInfoVO> findContractInfoList(Integer userId, FSUProjectInfoDTO fsuProjectInfoDTO) {
        Set<Integer> stationIdSet = stationService.findByRegionPermission(userId);
        if (CollUtil.isEmpty(stationIdSet)) {
            return Collections.emptyList();
        }
        return monitorUnitProjectInfoMapper.findContractInfoList(stationIdSet, fsuProjectInfoDTO);
    }

    @Override
    public ExcelWriter exportContractInfo(FSUProjectInfoDTO fsuProjectInfoDTO, Integer userId) {
        List<MonitorUnitProjectInfoVO> contractInfoList = findContractInfoList(userId, fsuProjectInfoDTO);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("stationName", messageSourceUtil.getMessage("version.fsu.siteName"));
        writer.addHeaderAlias("monitorUnitName", messageSourceUtil.getMessage("version.contract.FsuName"));
        writer.addHeaderAlias("ipAddress", messageSourceUtil.getMessage("version.contract.FsuIp"));
        writer.addHeaderAlias("projectName", messageSourceUtil.getMessage("version.contract.projectName"));
        writer.addHeaderAlias("contractNo", messageSourceUtil.getMessage("version.contract.contractNo"));
        writer.addHeaderAlias("installTime", messageSourceUtil.getMessage("version.contract.installTime"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(contractInfoList);
        return writer;
    }

    @Override
    public boolean batchUpdateContractInfo(List<FSUProjectInfoDTO> fsuProjectInfoDTOList) {
        if (CollUtil.isEmpty(fsuProjectInfoDTOList)) {
            return false;
        }
        return monitorUnitProjectInfoMapper.batchUpdateContractInfo(fsuProjectInfoDTOList) > 0;
    }
}
