package com.siteweb.as.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.entity.CustomInfo;
import com.siteweb.as.mapper.CustomInfoMapper;
import com.siteweb.as.service.CustomInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service
public class CustomInfoServiceImpl implements CustomInfoService {
    @Autowired
    CustomInfoMapper customInfoMapper;
    @Override
    public CustomInfo getCustomInfoAndUserId(Integer userId, String customType) {
        return customInfoMapper.selectOne(Wrappers.lambdaQuery(CustomInfo.class)
                                                   .eq(CustomInfo::getCustomType, customType)
                                                   .eq(CustomInfo::getUserId, userId));
    }

    @Override
    public Integer saveCustomInfo(CustomInfo customInfo) {
        //先删除
        deleteByUserIdAndCustomType(customInfo.getUserId(), customInfo.getCustomType());
        //后添加
        customInfo.setCreateTime(new Date());
        customInfo.setCustomContent(JSONUtil.toXmlStr(JSONUtil.parseObj(customInfo.getCustomContent())));
        Integer customInfoId = findIncrementCustomInfoId();
        customInfo.setCustomInfoId(customInfoId);
        customInfoMapper.insert(customInfo);
        return 1;
    }

    private Integer findIncrementCustomInfoId() {
        Integer customInfoId = customInfoMapper.findMaxCustomInfoId();
        if (Objects.isNull(customInfoId)) {
            return 1;
        }
        return ++customInfoId;
    }

    private void deleteByUserIdAndCustomType(Integer userId, String customType) {
        customInfoMapper.delete(Wrappers.lambdaQuery(CustomInfo.class)
                                        .eq(CustomInfo::getUserId, userId)
                                        .eq(CustomInfo::getCustomType, customType));
    }
}
