package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.IcsContractMaintenanceDTO;
import com.siteweb.as.entity.IcsContractMaintenance;
import com.siteweb.as.mapper.IcsContractMaintenanceMapper;
import com.siteweb.as.service.IcsContractMaintenanceService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IcsContractMaintenanceServiceImpl implements IcsContractMaintenanceService {
    @Autowired
    IcsContractMaintenanceMapper icsContractMaintenanceMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public Page<IcsContractMaintenance> findContractInfoPage(IcsContractMaintenanceDTO contractMaintenanceDTO) {
        return icsContractMaintenanceMapper.findContractInfoPage(contractMaintenanceDTO.generatePaginationInfo(), contractMaintenanceDTO);
    }

    @Override
    public IcsContractMaintenance findContractInfoById(Integer id) {
        return icsContractMaintenanceMapper.selectById(id);
    }

    @Override
    public IcsContractMaintenance createContractInfo(IcsContractMaintenance icsContractMaintenance) {
        icsContractMaintenanceMapper.insert(icsContractMaintenance);
        return icsContractMaintenance;
    }

    @Override
    public boolean deleteContractInfoById(Integer id) {
        return icsContractMaintenanceMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteContractInfoByIds(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return false;
        }
        return icsContractMaintenanceMapper.deleteByIds(idList) > 0;
    }

    @Override
    public IcsContractMaintenance updateContractInfo(IcsContractMaintenance icsContractMaintenance) {
        icsContractMaintenanceMapper.updateById(icsContractMaintenance);
        return icsContractMaintenance;
    }

    @Override
    public ExcelWriter exportContractInfo(IcsContractMaintenanceDTO contractMaintenanceDTO) {
        List<IcsContractMaintenance> contractInfoList = icsContractMaintenanceMapper.findContractInfoList(contractMaintenanceDTO);
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.addHeaderAlias("contractNo", messageSourceUtil.getMessage("version.contract.contractNo"));
        writer.addHeaderAlias("projectName", messageSourceUtil.getMessage("version.contract.projectName"));
        writer.addHeaderAlias("startDate", messageSourceUtil.getMessage("version.contract.maintenanceStartDate"));
        writer.addHeaderAlias("endDate", messageSourceUtil.getMessage("version.contract.maintenanceEndDate"));
        writer.addHeaderAlias("maintenanceTerms", messageSourceUtil.getMessage("version.contract.maintenanceTerms"));
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.autoSizeColumnAll();
        writer.write(contractInfoList);
        return writer;
    }
}
