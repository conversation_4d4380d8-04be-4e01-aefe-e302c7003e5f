package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.as.dto.area.AreaStationChangeDTO;
import com.siteweb.as.mapper.AreaStationMapper;
import com.siteweb.as.service.AreaStationService;
import com.siteweb.as.vo.area.AreaStationIdVO;
import com.siteweb.as.vo.area.AreaStationVO;
import com.siteweb.as.vo.area.StructureDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class AreaStationServiceImpl implements AreaStationService {
    @Autowired
    AreaStationMapper areaStationMapper;

    @Override
    public List<AreaStationVO> getStationByGroupIdAndStructureId(Integer groupId, Integer structureId) {
        return areaStationMapper.getStationByGroupIdAndStructureId(groupId, structureId);
    }

    @Override
    public List<StructureDataVO> getStructureData(Integer structure) {
        return areaStationMapper.getStructureData(structure);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAreaMap(AreaStationChangeDTO areaStationChangeDTO) {
        deleteAreaMap(areaStationChangeDTO.getAreaId(),areaStationChangeDTO.getDeleteStationIds());
        saveAreaMap(areaStationChangeDTO.getAreaId(),areaStationChangeDTO.getAddStationIds());
        return true;
    }

    @Override
    public List<AreaStationIdVO> findStationByAreaId(Integer areaId) {
        return areaStationMapper.findStationByAreaId(areaId);
    }

    private void saveAreaMap(Integer areaId, List<Integer> addStationIds) {
        if (CollUtil.isEmpty(addStationIds) || Objects.isNull(areaId)) {
            return;
        }
        areaStationMapper.batchSaveAreaMap(areaId,addStationIds);
    }

    private void deleteAreaMap(Integer areaId, List<Integer> deleteStationIds) {
        if (CollUtil.isEmpty(deleteStationIds) || Objects.isNull(areaId)) {
            return;
        }
        areaStationMapper.batchDeleteAreaMap(areaId,deleteStationIds);
    }
}
