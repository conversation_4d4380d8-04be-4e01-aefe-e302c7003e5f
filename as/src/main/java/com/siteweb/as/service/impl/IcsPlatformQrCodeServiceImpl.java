package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.version.IcsPlatformQrcodeDTO;
import com.siteweb.as.entity.IcsPlatformQrCode;
import com.siteweb.as.mapper.IcsPlatformQrCodeMapper;
import com.siteweb.as.service.IcsPlatformQrCodeService;
import com.siteweb.as.vo.version.IcsPlatformQrcodeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IcsPlatformQrCodeServiceImpl implements IcsPlatformQrCodeService {
    @Autowired
    IcsPlatformQrCodeMapper icsPlatformQrCodeMapper;

    @Override
    public Page<IcsPlatformQrcodeVO> findPage(IcsPlatformQrcodeDTO icsPlatformQrcodeDTO) {
        return icsPlatformQrCodeMapper.findPage(icsPlatformQrcodeDTO.generatePaginationInfo(),icsPlatformQrcodeDTO);
    }

    @Override
    public IcsPlatformQrCode create(IcsPlatformQrCode icsPlatformQrCode) {
        icsPlatformQrCode.setId(null);
        icsPlatformQrCodeMapper.insert(icsPlatformQrCode);
        return icsPlatformQrCode;
    }

    @Override
    public boolean deleteByIds(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        return icsPlatformQrCodeMapper.deleteByIds(ids) > 0;
    }

    @Override
    public IcsPlatformQrCode updateById(IcsPlatformQrCode icsPlatformQrCode) {
        icsPlatformQrCodeMapper.updateById(icsPlatformQrCode);
        return icsPlatformQrCode;
    }
}
