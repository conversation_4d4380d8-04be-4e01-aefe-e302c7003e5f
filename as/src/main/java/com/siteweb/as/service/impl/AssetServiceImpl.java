package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.as.mapper.AssetMapper;
import com.siteweb.as.service.AssetService;
import com.siteweb.as.vo.AssetVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AssetServiceImpl implements AssetService {
    @Autowired
    AssetMapper assetMapper;
    @Override
    public List<AssetVO> findAsset(){
        return assetMapper.findAssets();
    }

    @Override
    public Integer addAsset(AssetVO asset){
        return assetMapper.addAsset(asset);
    }

    @Override
    public Integer batchAddAsset(List<AssetVO> assetList){
        if (CollUtil.isEmpty(assetList)){
            return 0;
        }
        return assetMapper.batchAddAsset(assetList);
    }
    @Override
    public Integer putAsset(AssetVO asset){
        return assetMapper.putAsset(asset);
    }

    @Override
    public Integer delAsset(Integer assetId){
        return assetMapper.delAsset(assetId);
    }
}
