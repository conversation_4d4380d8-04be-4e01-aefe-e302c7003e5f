package com.siteweb.as.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.as.dto.shield.EquipmentShieldDTO;
import com.siteweb.as.dto.ShieldFilterDTO;
import com.siteweb.monitoring.vo.BatchEquipmentMaskVO;

import java.util.List;

public interface EquipmentShieldService {


    Integer saveShield(BatchEquipmentMaskVO saveInfo, Integer userId);

    Boolean deleteShield(List<Integer> equipmentIds, Integer userId);

    ExcelWriter exportShield(ShieldFilterDTO shieldFilterDTO, Integer userId);

    Page<EquipmentShieldDTO> findEquipmentShieldPage(Page<EquipmentShieldDTO> page, ShieldFilterDTO shieldFilterDTO, Integer userId);
}
