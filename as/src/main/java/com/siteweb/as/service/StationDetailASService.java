package com.siteweb.as.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.as.dto.StationDetailASDTO;
import com.siteweb.as.vo.QueryInformation;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.*;
import com.siteweb.monitoring.service.*;
import com.siteweb.utility.dto.DataItemDTO;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.manager.DataDictionaryManager;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("StationDetailASService")
public class StationDetailASService {
    @Autowired
    StationManager stationManager;

    @Autowired
    DataDictionaryManager dataDictionaryManager;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    StationMapper stationMapper;
    @Autowired
    HouseService houseService;
    @Autowired
    ActiveEventService activeEventService;
    @Autowired
    EquipmentMaintainMapper equipmentMaintainMapper;
    @Autowired
    AccountMapper accountMapper;
    @Autowired
    ProjectStateHouseMapper projectStateHouseMapper;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    StationStructureMapper stationStructureMapper;

    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    StationService stationService;

    private class PagePar{
        List<StationDetailASDTO> dtoList;
        Integer nTotal;
    }


    /**
     * 获取局站状态数量
     * @return
     */
    public List<Integer> queryStationStatusNum() {
        List<Station> stations = stationManager.findByStationIds(getVisibleStation(TokenUserUtil.getLoginUserId(),new StationFilterDTO()));
        //管理局站设置为在线
        stations.stream().filter(a -> a.getStationId() < 0).forEach(a -> a.setConnectState(OnlineState.ONLINE.value()));
        List<Integer> Nums = new ArrayList<>();
        int onlines = (int)stations.stream().filter(o->o.getConnectState() == OnlineState.ONLINE.value()).count();
        Nums.add(onlines);
        Nums.add(stations.size()-onlines);
        return Nums;
    }

    /**
     * 导出局站 局站状态和局站工程状态导出共用一个接口，用columns是否包含StartTime来区分
     * @return
     */
    public ExcelWriter exportActiveStation(QueryInformation queryInfo, String[] columns, String[] titles, Integer userId) {
        PagePar pagePar ;
        if (Arrays.asList(columns).contains("StartTime")){
            //局站工程状态导出
            pagePar = queryStation(userId, null, queryInfo,true);
        }
        else{
            pagePar = queryStation(userId, null, queryInfo,false);
        }
        ExcelWriter writer = ExcelUtil.getWriter(true);
        for(int i = 0; i < columns.length;i++)
        {
            writer.addHeaderAlias(StringUtils.uncapitalize(columns[i]), titles[i]);
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(pagePar.dtoList,true);
        return writer;
    }
    /**
     * 导出机房
     * @return
     */
    public ExcelWriter exportActivehouse(QueryInformation queryInfo, String[] columns, String[] titles, Integer userId) {

        PagePar pagePar = queryHouse(userId, null, queryInfo);

        ExcelWriter writer = ExcelUtil.getWriter(true);
        for(int i = 0; i < columns.length;i++)
        {
            writer.addHeaderAlias(StringUtils.uncapitalize(columns[i]), titles[i]);
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(pagePar.dtoList,true);
        return writer;
    }
    /**
     * 导出设备
     * @return
     */
    public ExcelWriter exportEquip(QueryInformation queryInfo, String[] columns, String[] titles, Integer userId) {

        PagePar pagePar = queryEquip(userId, null, queryInfo);

        ExcelWriter writer = ExcelUtil.getWriter(true);
        for(int i = 0; i < columns.length;i++) {
            writer.addHeaderAlias(StringUtils.uncapitalize(columns[i]), titles[i]);
        }
        // 不写入未添加别名的字段
        writer.setOnlyAlias(true);
        writer.write(pagePar.dtoList,true);
        return writer;
    }

    /**
     * 查询局站
     * @param userId 用户id
     * @param pageable 分页标志
     * @param queryInfo 查询条件
     * @param isProjectList 是否是局站工程状态列表，工程状态列表需要去掉管理局站
     * @return
     */
    public PagePar queryStation(int userId,Pageable pageable, QueryInformation queryInfo, Boolean isProjectList) {

        StationFilterDTO vo = getFilterDTOfromQueryInformation(queryInfo);
        Set<Integer> listStationIds = getVisibleStation(userId, vo);
        List<Station> listStation = stationManager.findByStationIds(listStationIds);
        //管理局站设置为在线
        listStation.stream().filter(a -> a.getStationId() < 0).forEach(a -> a.setConnectState(OnlineState.ONLINE.value()));
        List<StationProjectDetail> projectList = stationMapper.getAllProjectStations();
        List<Integer> activeprojectStationIds = projectList.stream().map(StationProjectDetail::getStationId).toList();
        if (ObjectUtil.isNotNull(vo.getMaintainState())){
            listStation = listStation.stream()
                    .filter(o -> vo.getMaintainState() == (activeprojectStationIds.contains(o.getStationId())))
                    .toList();
        }


        Map<Integer,StationProjectDetail> projectMap = projectList.stream().collect(Collectors.toMap(StationProjectDetail::getStationId,e -> e));
        Map<Integer, String> stationCategoryMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_CATEGORY.getValue());
        List<StationDetailASDTO> dtoList = new ArrayList<>();

        for (Station station : listStation) {
            StationDetailASDTO asDto = new StationDetailASDTO(station);
            if (isProjectList && station.getStationId() < 0){
                //标识为局站工程状态列表，不处理stationid小于0的虚拟局站
                continue;
            }
            //都为1或都不为1
            if (vo.getOnlineState() != null && !vo.getOnlineState().equals(asDto.getState())){
                continue;
            }
            switch (asDto.getState()){
                case 0:
                    asDto.setStatusText(localeMessageSourceUtil.getMessage("api.stationStatus.0"));
                    break;
                case 1:
                    asDto.setStatusText(localeMessageSourceUtil.getMessage("api.stationStatus.1"));
                    break;
                case 2:
                    asDto.setStatusText(localeMessageSourceUtil.getMessage("api.stationStatus.2"));
                    break;
                default:
                    asDto.setStatusText(localeMessageSourceUtil.getMessage("api.stationStatus.0"));
                    break;
            }
            asDto.setStationCategoryName(stationCategoryMap.get(station.getStationCategory()));

            asDto.setStationId(station.getStationId());

            StationProjectDetail project = projectMap.get(station.getStationId());
            if(project != null) {
                asDto.setStartTime(project.getStartTime());
                asDto.setEndTime(project.getEndTime());
                asDto.setReason(project.getReason());
                asDto.setUserName(accountMapper.findUserNameByUserId(project.getUserId()));
            }
            dtoList.add(asDto);
        }

        //中心名称 局站类型
        dtoList = populateCenterNameAndDefaultStationGroupName(dtoList,vo.getStationGroupType());
        sortListByFields(queryInfo,dtoList);
        List<StationDetailASDTO> slice = dtoList;
        if(pageable != null) {
            slice = dtoList.stream().skip(pageable.getPageNumber() * pageable.getPageSize())
                    .limit(pageable.getPageSize()).toList();
        }
        PagePar pagePar = new PagePar();
        pagePar.dtoList = slice;
        pagePar.nTotal = dtoList.size();
        return pagePar;
    }

    public PagePar queryHouse(int userId,Pageable pageable, QueryInformation queryInfo) {
        StationFilterDTO vo = getFilterDTOfromQueryInformation(queryInfo);
        Set<Integer> listStationIds = getVisibleStation(userId,vo);
        List<Station> listStation = stationManager.findByStationIds(listStationIds);
        //机房工程列表
        List<ProjectStateHouse> projectStateHouses = projectStateHouseMapper.getActiveHouseProject();
        Map<String, ProjectStateHouse> houseMap = projectStateHouses.stream()
                .collect(Collectors.toMap(
                        // 使用 houseId 和 stationId 拼接字符串作为键
                        e -> e.getHouseId() + "_" + e.getStationId(),
                        e -> e
                ));

        Map<Integer, Station> stationMap = listStation.stream().collect(Collectors.toMap(Station::getStationId, e -> e));

        List<House> houseList = houseService.findHousesByStationids(listStationIds);
        //过滤权限,站点，工程状态
        if (ObjectUtil.isNotNull(vo.getMaintainState()) ){
            houseList = houseList.stream().filter(o -> vo.getMaintainState() == houseMap.containsKey(o.getHouseId() + "_" + o.getStationId())).toList();
        }
        if (CollUtil.isNotEmpty(vo.getHouseIdList())){
            houseList =  houseList.stream().filter(o -> vo.getHouseIdList().contains(o.getStationId() + "_" + o.getHouseId())).toList();
        }
        List<StationDetailASDTO> dtoList = new ArrayList<>();
        for (House house : houseList) {
            if (house.getStationId() < 0){
                continue;
            }
            StationDetailASDTO asDto = new StationDetailASDTO();
            asDto.setStationId(house.getStationId());

            //机房名称
            asDto.setHouseId(house.getHouseId());
            asDto.setHouseName(house.getHouseName());
            ProjectStateHouse houseProject = houseMap.get(house.getHouseId() +"_" +house.getStationId());
            if (houseProject != null) {
                asDto.setStartTime(houseProject.getStartTime());
                asDto.setEndTime(houseProject.getEndTime());
                asDto.setReason(houseProject.getReason());
                asDto.setUserName(accountMapper.findUserNameByUserId(houseProject.getUserId()));
            }
            Station station = stationMap.get(house.getStationId());

            if (ObjectUtil.isNotNull(station)){
                DataItemDTO dataItemDTO = dataDictionaryManager.getDataItemDTO(71, station.getStationCategory());
                if (dataItemDTO != null){
                    asDto.setStationCategoryName(dataItemDTO.getItemValue());
                }
                asDto.setStationName(station.getStationName());
                asDto.setCenterId(station.getCenterId());
            }
            dtoList.add(asDto);
        }
        dtoList = populateCenterNameAndDefaultStationGroupName(dtoList,vo.getStationGroupType());
        sortListByFields(queryInfo,dtoList);
        List<StationDetailASDTO> slice = dtoList;
        if(pageable != null) {
            slice = dtoList.stream().skip(pageable.getPageNumber() * pageable.getPageSize())
                    .limit(pageable.getPageSize()).toList();
        }
        PagePar pagePar = new PagePar();
        pagePar.dtoList = slice;
        pagePar.nTotal = dtoList.size();
        return pagePar;
    }

    public PagePar queryEquip(int userId, Pageable pageable, QueryInformation queryInfo) {
        StationFilterDTO vo = getFilterDTOfromQueryInformation(queryInfo);
        Set<Integer> listStationIds = getVisibleStation(userId,vo);
        List<Station> listStation = stationManager.findByStationIds(listStationIds);
        Map<Integer,Station> stationMap = listStation.stream().collect(Collectors.toMap(Station::getStationId,e -> e));

        List<EquipmentProjectDetail> equipmentMaintainList = equipmentMaintainMapper.getProjectEquipmentList(3);
        Map<Integer,EquipmentProjectDetail> equipmentMaintainMap = equipmentMaintainList.stream().collect(Collectors.toMap(EquipmentProjectDetail::getEquipmentId,e -> e));

        List<Equipment> equipmentDTOList =  equipmentService.findEquipments();
        equipmentDTOList = equipmentDTOList.stream().filter(o->stationMap.containsKey(o.getStationId())).toList();
        if (ObjectUtil.isNotNull(vo.getMaintainState())){
            equipmentDTOList = equipmentDTOList.stream().filter(o -> vo.getMaintainState() == equipmentMaintainMap.containsKey(o.getEquipmentId())).toList();
        }
        if (CollUtil.isNotEmpty(vo.getEquipmentCategoryList())){
            equipmentDTOList = equipmentDTOList.stream().filter(o->vo.getEquipmentCategoryList().contains(o.getEquipmentCategory())).toList();
        }
        if (CollUtil.isNotEmpty(vo.getEquipmentIdList())){
            equipmentDTOList = equipmentDTOList.stream().filter(o->vo.getEquipmentIdList().contains(o.getEquipmentId())).toList();
        }

        List<StationDetailASDTO> dtoList = new ArrayList<>();
        List<House> houses = houseService.findHouses();
        Map<String, House> houseMap = houses.stream().collect(Collectors.toMap(
                h -> h.getStationId() + "_" + h.getHouseId(),
                h -> h,
                (existing, replacement) -> existing
        ));
        Map<Integer, String> stationCategoryMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_CATEGORY.getValue());
        Map<Integer, String> equipCategoryMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());

        for (Equipment equip : equipmentDTOList) {
            Station station = stationMap.get(equip.getStationId());
            if (ObjectUtil.isNull(station)){
                continue;
            }
            StationDetailASDTO asDto = new StationDetailASDTO(station);
            asDto.setStationName(station.getStationName());
            asDto.setStationCategoryName(stationCategoryMap.get(station.getStationCategory()));

            //机房信息
            House house = houseMap.get(equip.getStationId()+"_"+equip.getHouseId());
            if (ObjectUtil.isNotNull(house)){
                asDto.setHouseId(house.getHouseId());
                asDto.setHouseName(house.getHouseName());
            }
            EquipmentProjectDetail equipProject = equipmentMaintainMap.get(equip.getEquipmentId());
            if(equipProject != null) {
                asDto.setStartTime(equipProject.getStartTime());
                asDto.setEndTime(equipProject.getEndTime());
                asDto.setReason(equipProject.getReason());
                asDto.setUserName(accountMapper.findUserNameByUserId(equipProject.getUserId()));
            }
            //设备名称
            asDto.setEquipmentId(equip.getEquipmentId());
            asDto.setEquipmentName(equip.getEquipmentName());
            asDto.setEquipmentCategory(equipCategoryMap.get(equip.getEquipmentCategory()));
            dtoList.add(asDto);
        }

        dtoList = populateCenterNameAndDefaultStationGroupName(dtoList,vo.getStationGroupType());
        sortListByFields(queryInfo,dtoList);
        List<StationDetailASDTO> slice = dtoList;
        if(pageable != null) {
            slice = dtoList.stream().skip(pageable.getPageNumber() * pageable.getPageSize())
                    .limit(pageable.getPageSize()).toList();
        }
        PagePar pagePar = new PagePar();
        pagePar.dtoList = slice;
        pagePar.nTotal = dtoList.size();
        return pagePar;
    }

    public Page<StationDetailASDTO> queryPageableStationDetails(int userId, Pageable pageable, QueryInformation queryInfo,Boolean isProjectList) {

        PagePar pagePar = queryStation(userId, pageable, queryInfo,isProjectList);
        return new PageImpl<>(pagePar.dtoList, pageable, pagePar.nTotal);
    }

    public Page<StationDetailASDTO> queryPageableHouseDetails(int userId, Pageable pageable, QueryInformation queryInfo) {

        PagePar pagePar = queryHouse(userId, pageable, queryInfo);

        return new PageImpl<>(pagePar.dtoList, pageable, pagePar.nTotal);
    }

    public Page<StationDetailASDTO> queryPageableEquipDetails(int userId, Pageable pageable, QueryInformation queryInfo) {

        PagePar pagePar = queryEquip(userId, pageable, queryInfo);

        return new PageImpl<>(pagePar.dtoList, pageable, pagePar.nTotal);
    }



    /**
     * 分页查询停电状态列表
     * @return
     */
    public Page<PowerOffStationDetail> queryPowerOffStationDetails(int userId, Pageable pageable, QueryInformation queryInformation) {
        List<PowerOffStationDetail> result = new ArrayList<>();
        List<ActiveEventDTO> activeEventDTOS = activeEventService.findActiveEventDTOsByUserIdAndEventCategoryAndEndTimeIsNull(userId, 10);
        if (CollUtil.isEmpty(activeEventDTOS)){
            return new PageImpl<>(result, pageable, activeEventDTOS.size());
        }

        List<Integer> stationIds = activeEventDTOS.stream().map(ActiveEventDTO::getStationId).toList();

        Map<Integer, Station> stationMap = stationManager.findByStationIds(stationIds).stream().collect(Collectors.toMap(Station::getStationId, Function.identity()));
        Map<Integer, StationStructure> stationStructureMapBuStructureId = stationStructureService.findAll().stream().collect(Collectors.toMap(StationStructure::getStructureId, Function.identity()));
        int defaultGroupType = 1;
        Map<Integer, StationStructure> stationStructureMapBuStationId = stationStructureMapper.findStationStructures(stationIds, defaultGroupType);

        for (ActiveEventDTO activeEventDTO : activeEventDTOS) {
            PowerOffStationDetail powerOffStationDetail = new PowerOffStationDetail(activeEventDTO);
            Integer duration = DateUtil.differentSecondsByMillisecond(powerOffStationDetail.getStartTime(), new Date()) / 60;
            powerOffStationDetail.setDuration(duration);
            powerOffStationDetail.setStationName(stationMap.getOrDefault(activeEventDTO.getStationId(),new Station()).getStationName());
            StationStructure stationStructure = stationStructureMapBuStationId.get(activeEventDTO.getStationId());
            if (ObjectUtil.isNull(stationStructure)){
                continue;
            }

            powerOffStationDetail.setGroupName(stationStructure.getStructureName());
            String levelPath = stationStructure.getLevelPath();
            if (StringUtils.isNotEmpty(levelPath)){
                Integer centerId =Integer.parseInt(levelPath.split("\\.")[0]);
                powerOffStationDetail.setCenterName(stationStructureMapBuStructureId.getOrDefault(centerId,new StationStructure()).getStructureName());
            }
            result.add(powerOffStationDetail);
        }
        //排序
        sortListByFields(queryInformation, result);


        List<PowerOffStationDetail> slice = result.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();

        return new PageImpl<>(slice, pageable, activeEventDTOS.size());
    }

    /**
     * 排序
     * @param queryInfo 综合查询排序参数
     * @param list 预排序列表
     * @param <T>
     */
    public static <T> void sortListByFields(QueryInformation queryInfo, List<T> list) {
        if (ObjectUtil.isNull(queryInfo) || CollUtil.isEmpty(queryInfo.getOrderByList())) {
            return;
        }

        List<QueryInformation.OrderBy> orderlist = queryInfo.getOrderByList();
        Map<String, Method> methodCache = new HashMap<>();

        // 使用泛型类型 T 的类对象
        Class<?> clazz = list.get(0).getClass();

        orderlist.forEach(order -> {
            String name = order.getPropertyName().split("\\.")[1];
            if ("DefaultStationGroupName".equals(name)) {
                name = "groupName";
            }
            if ("Name".equals(name) || "name".equals(name)) {
                name = "stationName";
            }

            String funcName = "get" + name.substring(0, 1).toUpperCase() + name.substring(1);
            try {
                Method method = clazz.getMethod(funcName);
                methodCache.put(order.getPropertyName(), method);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException("Method " + funcName + " not found in class " + clazz.getName(), e);
            }
        });

        Comparator<T> comparator = (a, b) -> 0;
        for (QueryInformation.OrderBy order : orderlist) {
            Method method = methodCache.get(order.getPropertyName());
            Comparator<T> tempComparator = (A, B) -> {
                try {
                    Comparable valueA = (Comparable) method.invoke(A);
                    Comparable valueB = (Comparable) method.invoke(B);
                    if (valueA == null && valueB == null) {
                        return 0; // 两者都为 null，认为相等
                    }
                    if (valueA == null) {
                        return 1; // valueA 为 null，排在后面
                    }
                    if (valueB == null) {
                        return -1; // valueB 为 null，排在前面
                    }
                    return order.getOrderByType().equals("ASC") ? valueA.compareTo(valueB) : valueB.compareTo(valueA);
                } catch (Exception e) {
                    throw new RuntimeException("Error invoking method " + method.getName(), e);
                }
            };
            comparator = comparator.thenComparing(tempComparator);
        }

        list.sort(comparator);
    }

    /**
     * 填充中心名称和局站分组
     * @param oriDTOList 已经填充过stationid centerid基本信息
     * @return
     */
    private List<StationDetailASDTO> populateCenterNameAndDefaultStationGroupName(List<StationDetailASDTO> oriDTOList, Integer groupType) {
        List<StationDetailASDTO> result = new ArrayList<>();
        if (CollUtil.isEmpty(oriDTOList)) {
            return result;
        }
        Map<Integer, Integer> stationIdResourceStructureIdMap = resourceStructureManager.getAll().stream().filter(a -> ObjectUtil.equals(SourceType.STATION.value(),a.getStructureTypeId())).collect(Collectors.toMap(ResourceStructure::getOriginId, ResourceStructure::getResourceStructureId,(existing, replacement) -> existing));
        List<Integer> stationIds = oriDTOList.stream().map(StationDetailASDTO::getStationId).distinct().toList();
        List<Integer> adminStationIds = stationIds.stream().filter(a -> a < 0).toList();

        // 预加载所有 stationStructure 数据
        Map<Integer, StationStructure> stationStructureMapByStructureId = stationStructureService.findAll().stream()
                .collect(Collectors.toMap(StationStructure::getStructureId, Function.identity()));
        //默认采用按行政区分组的方式
        groupType = (groupType == null) ? 1 : groupType;
        Map<Integer, StationStructure> stationStructureMapByStationId = stationStructureMapper.findStationStructures(stationIds, groupType);
        //管理类局站在stationstructure表中只有groupid=0的类型
        Map<Integer, StationStructure> adminStationStructureMapByStationId = stationStructureMapper.findStationStructures(adminStationIds, 0);
        // 遍历数据列表
        for (StationDetailASDTO asDto : oriDTOList) {
            StationStructure stationStructure ;
            asDto.setResourceStructureId(stationIdResourceStructureIdMap.get(asDto.getStationId()));
            if (asDto.getStationId() < 0){
                stationStructure = adminStationStructureMapByStationId.get(asDto.getStationId());
            }
            else {
                stationStructure = stationStructureMapByStationId.get(asDto.getStationId());
            }
            if (ObjectUtil.isNull(stationStructure)){
                continue;
            }
            asDto.setDefaultStationGroupName(stationStructure.getStructureName());
            String levelPath = stationStructure.getLevelPath();
            if (StringUtils.isNotEmpty(levelPath)){
                Integer centerId =Integer.parseInt(levelPath.split("\\.")[0]);
                asDto.setCenterName(stationStructureMapByStructureId.getOrDefault(centerId,new StationStructure()).getStructureName());
            }
            result.add(asDto);
        }

        return result;
    }


    /**
     * 类型转换
     * @param queryInfo
     * @return
     */
    public StationFilterDTO getFilterDTOfromQueryInformation(QueryInformation queryInfo){
        StationFilterDTO vo = new StationFilterDTO();
        if (ObjectUtil.isNull(queryInfo)) {
            //修复为空情况下的报错
            return vo;
        }
        if(queryInfo.getSingleValueComparisons() != null)
        {
            List<QueryInformation.SingleValueComparison> singleValueComparisons = queryInfo.getSingleValueComparisons();
            for(QueryInformation.SingleValueComparison item:singleValueComparisons){
                if (item.getPropertyName().contains("SetMaintain")) {
                    vo.setMaintainState(Boolean.parseBoolean(item.getValue()));
                }
            }
        }
        if(queryInfo.getQueryComparisons() != null) {
            for (QueryInformation.QueryComparison campar : queryInfo.getQueryComparisons()) {
                if (campar.getQueryInformation() == null)
                    continue;
                List<QueryInformation.MultiValueComparison> multiValueComparisons = campar.getQueryInformation().getMultiValueComparisons();
                if (multiValueComparisons != null) {
                    for (QueryInformation.MultiValueComparison item : multiValueComparisons) {
                        if (item.getPropertyName().contains("StationGroupId")) {
                            vo.setStationStructureList(item.getValues().stream().map(Integer::valueOf).collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
        if(queryInfo.getMultiValueComparisons() != null) {
            List<QueryInformation.MultiValueComparison> multiValueComparisons = queryInfo.getMultiValueComparisons();
            for (QueryInformation.MultiValueComparison item : multiValueComparisons) {
                //局站分组方式 前端筛选时不传递
                if (item.getPropertyName().contains("StationGroupType")){
                    vo.setStationGroupType(Integer.valueOf(item.getValues().get(0)));
                }
                //分组名称
                if ("mapObjectId".equals(item.getPropertyName()) || "StationStructureId".equals(item.getPropertyName())) {
                    vo.setStationStructureList(item.getValues().stream().map(Integer::valueOf).collect(Collectors.toList()));
                }
                //工程状态在single里
                //局站类型
                if ("StationCategoryId".equals(item.getPropertyName()) || "ConfigObject.StationCategoryId".equals(item.getPropertyName())) {
                    vo.setStationCategoryList(item.getValues().stream().map(Integer::valueOf).collect(Collectors.toList()));
                }
                //局站名称
                if ("stationId".equals(item.getPropertyName()) || "ConfigObject.ObjectId".equals(item.getPropertyName()) ) {
                    vo.setStationIdList( item.getValues().stream().map(Integer::valueOf).collect(Collectors.toList()));
                }
                //机房id（一般是局站和机房id的_连接
                if ("ConfigObject.UniqueId".equals(item.getPropertyName())&& "ActiveHouse".equals(queryInfo.getObjectTypeName())){
                    Collection<Integer> stationList = vo.getStationIdList() == null ? new ArrayList<>() : vo.getStationIdList();
                    item.getValues().stream()
                            .filter(a -> StringUtils.isNotEmpty(a) && a.contains("_")) // 过滤无效值
                            .forEach(a -> {
                                String[] parts = a.split("_");
                                if (parts.length == 2) { // 确保分割后有两个部分
                                    stationList.add(Integer.parseInt(parts[0])); // 添加到 stationList
                                }
                            });
                    vo.setStationIdList(stationList); // 设置 stationList
                    vo.setHouseIdList(item.getValues());
                }
                //局站状态
                if ("ConfigObject.ConnectState".equals(item.getPropertyName())) {
                    vo.setOnlineState("true".equals(item.getValues().get(0))?1:0);
                }
                //设备类型
                if ("ConfigObject.EquipmentCategoryId".equals(item.getPropertyName()) || "EquipmentCategoryId".equals(item.getPropertyName()) ){
                    vo.setEquipmentCategoryList(item.getValues().stream().map(Integer::valueOf).collect(Collectors.toSet()));
                }
                //设备id
                if ("ConfigObject.EquipmentId".equals(item.getPropertyName()) || "EquipmentCategoryId".equals(item.getPropertyName()) ){
                    vo.setEquipmentIdList(item.getValues().stream().map(Integer::valueOf).collect(Collectors.toList()));
                }
            }
        }
        return vo;
    }

    public Set<Integer> getVisibleStation(Integer userId, StationFilterDTO dto){
        Set<Integer> stationIdSetByUser = stationService.findByRegionPermission(userId);
        Set<Integer> stationIdSetByCondition = stationMapper.findIdsByFilterCondition(dto);
        Collection<Integer> intersection = CollUtil.intersection(stationIdSetByUser, stationIdSetByCondition);
        return new HashSet<>(intersection);
    }
}
