<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.HistorySelectionMapper">
    <select id="findByUserIdAdnType" resultType="com.siteweb.as.entity.HistorySelection">
        SELECT historyselectionid,
               userid,
               selectiontype,
               selectionname,
               selectioncontent,
               description,
               createtime,
               queryinformation
        FROM TBL_HistorySelection
        WHERE SelectionType = #{selectionType} AND (UserId = #{userId} OR QueryInformation = 'share')
    </select>
</mapper>