<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.PatrolRuleMapper">

    <resultMap id="RuleResultMap" type="com.siteweb.as.dto.patrol.PatrolRuleDTO">
        <id property="ruleId" column="RuleId"/>
        <result property="ruleName" column="RuleName"/>
        <result property="limitDown" column="LimitDown"/>
        <result property="limitDownCalOpId" column="LimitDownCalOpId"/>
        <result property="limitDownCalOperator" column="LimitDownCalOperator"/>
        <result property="limitUp" column="LimitUp"/>
        <result property="limitUpCalOpId" column="LimitUpCalOpId"/>
        <result property="limitUpCalOperator" column="LimitUpCalOperator"/>
        <result property="unitId" column="UnitId"/>
        <result property="unitSymbol" column="UnitSymbol"/>
        <result property="warningLevelId" column="WarningLevelId"/>
        <result property="warningMeaning" column="WarningMeaning"/>
        <result property="byPercentage" column="ByPercentage"/>
        <result property="byPercentageMeaning" column="ByPercentageMeaning"/>
        <result property="ratedValue" column="RatedValue"/>
        <result property="baseEquipmentId" column="BaseEquipmentId"/>
        <result property="baseEquipmentName" column="BaseEquipmentName"/>
        <result property="baseTypeId" column="BaseTypeId"/>
        <result property="baseTypeName" column="BaseTypeName"/>
        <result property="note" column="Note"/>
        <result property="description" column="Description"/>
        <result property="groupCount" column="GroupCount"/>
        <!-- Collection mapping for Standard information -->
        <collection property="standardInfos" ofType="com.siteweb.as.dto.patrol.PatrolRuleDTO$StandardInfo" column="BaseTypeId" notNullColumn="BaseTypeId">
            <result property="standardDicId" column="StandardDicId"/>
            <result property="signalStandardName" column="SignalStandardName"/>
        </collection>
    </resultMap>

    <select id="getAllPatrolRule" resultMap="RuleResultMap">
        SELECT
        r.RuleId AS RuleId,
        r.RuleName AS RuleName,
        CAST(r.LimitDown AS FLOAT) AS LimitDown,
        r.LimitDownCalOpId AS LimitDownCalOpId,
        codown.CalOperator AS LimitDownCalOperator,
        CAST(r.LimitUp AS FLOAT) AS LimitUp,
        r.LimitUpCalOpId AS LimitUpCalOpId,
        IFNULL(coup.CalOperator, '''') AS LimitUpCalOperator,
        r.UnitId AS UnitId,
        IFNULL(u.UnitSymbol, '''') AS UnitSymbol,
        r.WarningLevelId AS WarningLevelId,
        IFNULL(wl.Meaning, '''') AS WarningMeaning,
        r.ByPercentage AS ByPercentage,
        CASE
        WHEN r.ByPercentage = 1 THEN '是'
        ELSE '否'
        END AS ByPercentageMeaning,
        CAST(r.RatedValue AS FLOAT) AS RatedValue,
        IFNULL(r.BaseEquipmentId, -1) AS BaseEquipmentId,
        IFNULL(e.BaseEquipmentName, '不选基类设备') AS BaseEquipmentName,
        IFNULL(r.BaseTypeId, -1) AS BaseTypeId,
        IFNULL(s.BaseTypeName, '不选基类信号') AS BaseTypeName,
        r.Note AS Note,
        r.Description AS Description,
        (select count(*)
        from TBL_PatrolGroupRuleMap m
        WHERE m.RuleId = r.RuleId) GroupCount,
        std.StandardDicId,
        std.SignalStandardName
        FROM
        TBL_PatrolRule r
        LEFT JOIN
        TBL_PatrolCalOp codown ON codown.CalOpId = r.LimitDownCalOpId
        LEFT JOIN
        TBL_PatrolCalOp coup ON coup.CalOpId = r.LimitUpCalOpId
        LEFT JOIN
        TBL_PatrolUnit u ON u.UnitId = r.UnitId
        LEFT JOIN
        TBL_PatrolWarningLevel wl ON wl.LevelId = r.WarningLevelId
        LEFT JOIN
        TBL_EquipmentBaseType e ON e.BaseEquipmentId = r.BaseEquipmentId
        LEFT JOIN
        TBL_SignalBaseDic s ON s.BaseTypeId = r.BaseTypeId
        LEFT JOIN TBL_SignalBaseMap map ON map.BaseTypeId  = r.BaseTypeId and map.StandardType = #{standardVer}
        LEFT JOIN TBL_StandardDicSig std ON std.StandardDicId = map.StandardDicId and std.StandardType = #{standardVer}
    </select>
    <sql id="PatrolRuleField">
        r.RuleId AS RuleId,
        r.RuleName AS RuleName,
        r.LimitDown AS LimitDown,
        r.LimitDownCalOpId AS LimitDownCalOpId,
        codown.CalOperator AS LimitDownCalOperator,
        r.LimitUp AS LimitUp,
        r.LimitUpCalOpId AS LimitUpCalOpId,
        IFNULL(coup.CalOperator, '''') AS LimitUpCalOperator,
        r.UnitId AS UnitId,
        IFNULL(u.UnitSymbol, '''') AS UnitSymbol,
        r.WarningLevelId AS WarningLevelId,
        IFNULL(wl.Meaning, '''') AS WarningMeaning,
        r.ByPercentage AS ByPercentage,
        CASE
        WHEN r.ByPercentage = 1 THEN '是'
        ELSE '否'
        END AS ByPercentageMeaning,
        r.RatedValue AS RatedValue,
        IFNULL(r.BaseEquipmentId, -1) AS BaseEquipmentId,
        IFNULL(e.BaseEquipmentName, '不选基类设备') AS BaseEquipmentName,
        IFNULL(r.BaseTypeId, -1) AS BaseTypeId,
        IFNULL(s.BaseTypeName, '不选基类信号') AS BaseTypeName,
        r.Note AS Note,
        r.Description AS Description,
        (select count(*)
        from TBL_PatrolGroupRuleMap m
        WHERE m.RuleId = r.RuleId) GroupCount,
        std.StandardDicId,
        std.SignalStandardName
    </sql>
    <select id="getPatrolRule" resultMap="RuleResultMap">
        SELECT
        <include refid="PatrolRuleField"/>
        FROM
        TBL_PatrolRule r
        LEFT JOIN
        TBL_PatrolCalOp codown ON codown.CalOpId = r.LimitDownCalOpId
        LEFT JOIN
        TBL_PatrolCalOp coup ON coup.CalOpId = r.LimitUpCalOpId
        LEFT JOIN
        TBL_PatrolUnit u ON u.UnitId = r.UnitId
        LEFT JOIN
        TBL_PatrolWarningLevel wl ON wl.LevelId = r.WarningLevelId
        LEFT JOIN
        TBL_EquipmentBaseType e ON e.BaseEquipmentId = r.BaseEquipmentId
        LEFT JOIN
        TBL_SignalBaseDic s ON s.BaseTypeId = r.BaseTypeId
        LEFT JOIN TBL_SignalBaseMap map ON map.BaseTypeId  = r.BaseTypeId and map.StandardType = #{standardVer}
        LEFT JOIN TBL_StandardDicSig std ON std.StandardDicId = map.StandardDicId and std.StandardType = #{standardVer}
        where r.RuleId = #{ruleId}
    </select>
    <select id="getPatrolRuleByGroup" resultMap="RuleResultMap">
        SELECT
        <include refid="PatrolRuleField"/>
        FROM
        TBL_PatrolGroupBaseSignalMap bsm
        INNER JOIN TBL_PatrolRule r ON
        r.BaseTypeId = bsm.BaseTypeId
        LEFT JOIN TBL_PatrolCalOp codown ON
        codown.CalOpId = r.LimitDownCalOpId
        LEFT JOIN TBL_PatrolCalOp coup ON
        coup.CalOpId = r.LimitUpCalOpId
        LEFT JOIN TBL_PatrolUnit u ON
        u.UnitId = r.UnitId
        LEFT JOIN TBL_PatrolWarningLevel wl ON
        wl.LevelId = r.WarningLevelId
        LEFT JOIN TBL_EquipmentBaseType e ON
        e.BaseEquipmentId = r.BaseEquipmentId
        LEFT JOIN TBL_SignalBaseDic s ON
        s.BaseTypeId = r.BaseTypeId
        LEFT JOIN TBL_SignalBaseMap map ON map.BaseTypeId  = r.BaseTypeId and map.StandardType = #{standardVer}
        LEFT JOIN TBL_StandardDicSig std ON std.StandardDicId = map.StandardDicId and std.StandardType = #{standardVer}
        WHERE
        bsm.GroupId = #{groupId}
        ORDER BY
        r.BaseTypeId
    </select>
    <select id="listBaseEquipment" resultType="com.siteweb.as.dto.patrol.BaseEquipmentDTO">
        select
        BaseEquipmentId,
        BaseEquipmentName
        from TBL_EquipmentBaseType
        union
        select -1, '不选基类设备'
    </select>
    <select id="listBaseSignal" resultType="com.siteweb.as.dto.patrol.SignalBaseDicDTO">
        select
        BaseTypeId,
        BaseTypeName
        from TBL_SignalBaseDic dic
        where dic.BaseEquipmentId = #{baseEquipmentId}
    </select>
    <select id="listStandardInfo" resultType="com.siteweb.as.dto.patrol.StandardInfoDTO">
        select distinct std.StandardDicId ,std.SignalStandardName
        from TBL_StandardDicSig std
        inner join TBL_SignalBaseMap map on std.StandardDicId = map.StandardDicId
        inner join TBL_StandardType stdtype on stdtype.StandardId = map.StandardType
        inner join TBL_SysConfig sc on stdtype.StandardAlias = sc.ConfigValue and sc.ConfigKey = 'StandardVer'
        where map.BaseTypeId = #{baseTypeId}
    </select>
    <select id="getPatrolRuleJobDTO" resultType="com.siteweb.as.dto.patrol.PatrolRuleJobDTO">
        SELECT
        r.RuleId,
        r.RuleName,
        r.LimitDown,
        r.LimitDownCalOpId,
        r.LimitUp,
        r.LimitUpCalOpId,
        r.UnitId,
        r.WarningLevelId,
        r.ByPercentage,
        r.RatedValue,
        r.BaseEquipmentId,
        r.BaseTypeId,
        r.Note,
        r.Description
        FROM
        TBL_PatrolRule r
        INNER JOIN TBL_PatrolGroupRuleMap grm ON
        grm.RuleId = r.RuleId
        WHERE
        grm.GroupId = #{groupId}
        ORDER BY
        r.RuleId
    </select>
</mapper>