<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.ShieldMapper">

    <select id="getEventMask" resultType="com.siteweb.as.dto.MaskDetailsDTO">
        SELECT mask.StartTime, mask.EndTime, mask.Reason, mask.TimeGroupId, span.Week, span.TimeSpanChar, account.UserId, account.UserName
        FROM TBL_EventMask mask
        LEFT JOIN TBL_TimeGroupSpan span ON  mask.TimeGroupId = span.TimeGroupId
        left join TBL_Account account on mask.UserId = account.UserId
        WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId} AND EventId = #{eventId}
    </select>
    <select id="getEquipmentMask" resultType="com.siteweb.as.dto.MaskDetailsDTO">
        SELECT mask.StartTime, mask.EndTime, mask.Reason, mask.TimeGroupId, span.Week, span.TimeSpanChar, account.UserId, account.UserName
        FROM TBL_EquipmentMask mask left join TBL_Account account on mask.UserId = account.UserId
        LEFT JOIN TBL_TimeGroupSpan span ON  mask.TimeGroupId = span.TimeGroupId
        WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId}
    </select>
    <select id="getStationMask" resultType="com.siteweb.as.dto.MaskDetailsDTO">
        SELECT mask.StartTime, mask.EndTime, mask.Reason, mask.TimeGroupId, span.Week, span.TimeSpanChar, account.UserId, account.UserName
        FROM TBL_StationMask mask left join TBL_Account account on mask.UserId = account.UserId
        LEFT JOIN TBL_TimeGroupSpan span ON  mask.TimeGroupId = span.TimeGroupId
        WHERE StationId = #{stationId}
    </select>
</mapper>