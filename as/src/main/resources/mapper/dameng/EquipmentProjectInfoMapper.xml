<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.EquipmentProjectInfoMapper">
    <sql id="findContractInfoSql">
        SELECT s.StationId,
        s.StationName,
        p.MonitorUnitId,
        h.HouseId,
        h.HouseName,
        p.EquipmentId,
        e.EquipmentName,
        p.ProjectName,
        p.ContractNo,
        p.InstallTime
        FROM TBL_EquipmentProjectInfo p
        INNER JOIN tbl_station s ON p.StationId = s.StationId
        INNER JOIN TBL_Equipment e ON p.StationId = e.StationId AND p.MonitorUnitId = e.MonitorUnitId AND p.EquipmentId = e.EquipmentId
        INNER JOIN tbl_equipment f ON f.EquipmentId = e.EquipmentId
        INNER JOIN TBL_House h ON e.StationId = h.StationId AND e.HouseId = h.HouseId
        WHERE e.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        <if test="equipmentProjectInfoDTO.stationName != null and equipmentProjectInfoDTO.stationName != ''">
            AND s.StationName LIKE CONCAT('%',#{equipmentProjectInfoDTO.stationName},'%')
        </if>
        <if test="equipmentProjectInfoDTO.houseName != null and equipmentProjectInfoDTO.houseName != ''">
            AND h.HouseName LIKE CONCAT('%',#{equipmentProjectInfoDTO.houseName},'%')
        </if>
        <if test="equipmentProjectInfoDTO.equipmentName != null and equipmentProjectInfoDTO.equipmentName != ''">
            AND e.EquipmentName LIKE CONCAT('%',#{equipmentProjectInfoDTO.equipmentName},'%')
        </if>
        <if test="equipmentProjectInfoDTO.contractNo != null and equipmentProjectInfoDTO.contractNo != ''">
            AND p.ContractNo LIKE CONCAT('%',#{equipmentProjectInfoDTO.contractNo},'%')
        </if>
        <if test="equipmentProjectInfoDTO.projectName != null and equipmentProjectInfoDTO.projectName != ''">
            AND p.ProjectName LIKE CONCAT('%',#{equipmentProjectInfoDTO.projectName},'%')
        </if>
        <if test="equipmentProjectInfoDTO.installTime != null and equipmentProjectInfoDTO.installTime != ''">
            AND p.InstallTime LIKE CONCAT('%',#{equipmentProjectInfoDTO.installTime},'%')
        </if>
        <if test="equipmentProjectInfoDTO.orderField != null and equipmentProjectInfoDTO.orderDirection != null and equipmentProjectInfoDTO.orderField != '' and equipmentProjectInfoDTO.orderDirection != ''">
            ORDER BY ${equipmentProjectInfoDTO.orderField} ${equipmentProjectInfoDTO.orderDirection}
        </if>
    </sql>
    <update id="batchUpdateContractInfo">
        <foreach collection="list" item="item" separator=";">
            UPDATE TBL_EquipmentProjectInfo SET ContractNo = #{item.contractNo},ProjectName = #{item.projectName}
            WHERE StationId = #{item.stationId} AND EquipmentId = #{item.equipmentId}
        </foreach>
    </update>
    <select id="findContractInfoPage" resultType="com.siteweb.as.vo.version.EquipmentProjectInfoVO">
        <include refid="findContractInfoSql"/>
    </select>
    <select id="findContractInfoByEquipmentId" resultType="com.siteweb.as.vo.version.EquipmentProjectInfoVO">
        SELECT s.StationId,
               s.StationName,
               p.MonitorUnitId,
               h.HouseId,
               h.HouseName,
               p.EquipmentId,
               e.EquipmentName,
               p.ProjectName,
               p.ContractNo,
               p.InstallTime
        FROM TBL_EquipmentProjectInfo p
                 INNER JOIN tbl_station s ON p.StationId = s.StationId
                 INNER JOIN TBL_Equipment e ON p.StationId = e.StationId AND p.MonitorUnitId = e.MonitorUnitId AND p.EquipmentId = e.EquipmentId
                 INNER JOIN tbl_equipment f ON f.EquipmentId = e.EquipmentId
                 INNER JOIN TBL_House h ON e.StationId = h.StationId AND e.HouseId = h.HouseId
        WHERE e.EquipmentId = #{equipmentId}
    </select>
    <select id="findContractInfo" resultType="com.siteweb.as.vo.version.EquipmentProjectInfoVO">
        <include refid="findContractInfoSql"/>
    </select>
</mapper>