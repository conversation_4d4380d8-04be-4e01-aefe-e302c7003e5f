<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.PatrolAllRecordMapper">

    <insert id="batchInsert">
        INSERT INTO ${tableName} (
        centerId, centerName, groupId, groupName, stationId, stationName,
        equipmentCategoryName, equipmentId, equipmentName, signalId, signalName,
        signalValue, recordTime, unit, limitDown, limitDownCalOpId,
        limitUp, limitUpCalOpId, byPercentage, ratedValue,
        warningLevelId, reasonable, isPowerOffAlarm, createTime, sn
        )
        VALUES
        <foreach collection="records" item="record" separator=",">
            (
            #{record.centerId}, #{record.centerName}, #{record.groupId}, #{record.groupName},
            #{record.stationId}, #{record.stationName}, #{record.equipmentCategoryName},
            #{record.equipmentId}, #{record.equipmentName}, #{record.signalId}, #{record.signalName},
            #{record.signalValue}, #{record.recordTime}, #{record.unit}, #{record.limitDown},
            #{record.limitDownCalOpId}, #{record.limitUp}, #{record.limitUpCalOpId},
            #{record.byPercentage}, #{record.ratedValue}, #{record.warningLevelId},
            #{record.reasonable}, #{record.isPowerOffAlarm}, #{record.createTime}, #{record.sn}
            )
        </foreach>
    </insert>

</mapper>