<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.as.mapper.PatrolStandardRuleMapper">
    <!-- 内容与MySQL版本相同，IFNULL改为COALESCE，CAST语法适配 -->
    <resultMap id="RuleResultMap" type="com.siteweb.as.dto.patrol.PatrolStandardRuleDTO">
        <id property="ruleId" column="RuleId"/>
        <result property="ruleName" column="RuleName"/>
        <result property="limitDown" column="LimitDown"/>
        <result property="limitDownCalOpId" column="LimitDownCalOpId"/>
        <result property="limitDownCalOperator" column="LimitDownCalOperator"/>
        <result property="limitUp" column="LimitUp"/>
        <result property="limitUpCalOpId" column="LimitUpCalOpId"/>
        <result property="limitUpCalOperator" column="LimitUpCalOperator"/>
        <result property="unitId" column="UnitId"/>
        <result property="unitSymbol" column="UnitSymbol"/>
        <result property="warningLevelId" column="WarningLevelId"/>
        <result property="warningMeaning" column="WarningMeaning"/>
        <result property="byPercentage" column="ByPercentage"/>
        <result property="byPercentageMeaning" column="ByPercentageMeaning"/>
        <result property="ratedValue" column="RatedValue"/>
        <result property="equipmentLogicClassId" column="EquipmentLogicClassId"/>
        <result property="standardEquipmentName" column="StandardEquipmentName"/>
        <result property="standardDicId" column="StandardDicId"/>
        <result property="standardSignalName" column="StandardSignalName"/>
        <result property="note" column="Note"/>
        <result property="description" column="Description"/>
    </resultMap>


    <select id="getAllPatrolStandardRule" resultMap="RuleResultMap">
        SELECT
        r.RuleId AS RuleId,
        r.RuleName AS RuleName,
        CAST(r.LimitDown AS FLOAT) AS LimitDown,
        r.LimitDownCalOpId AS LimitDownCalOpId,
        codown.CalOperator AS LimitDownCalOperator,
        CAST(r.LimitUp AS FLOAT) AS LimitUp,
        r.LimitUpCalOpId AS LimitUpCalOpId,
        COALESCE(coup.CalOperator, '') AS LimitUpCalOperator,
        r.UnitId AS UnitId,
        COALESCE(u.UnitSymbol, '') AS UnitSymbol,
        r.WarningLevelId AS WarningLevelId,
        COALESCE(wl.Meaning, '') AS WarningMeaning,
        r.ByPercentage AS ByPercentage,
        CASE
        WHEN r.ByPercentage = 1 THEN '是'
        ELSE '否'
        END AS ByPercentageMeaning,
        CAST(r.RatedValue AS FLOAT) AS RatedValue,
        COALESCE(r.equipmentLogicClassId, -1) AS EquipmentLogicClassId,
        COALESCE(std.EquipmentLogicClass, '不选标准化设备') AS StandardEquipmentName,
        r.StandardDicId AS StandardDicId,
        std.SignalStandardName AS StandardSignalName,
        r.Note AS Note,
        r.Description AS Description,
        (SELECT count(*)
        FROM TBL_PatrolGroupRuleMap m
        WHERE m.RuleId = r.RuleId) GroupCount
        FROM
        TBL_PatrolRule r
        LEFT JOIN
        TBL_PatrolCalOp codown ON codown.CalOpId = r.LimitDownCalOpId
        LEFT JOIN
        TBL_PatrolCalOp coup ON coup.CalOpId = r.LimitUpCalOpId
        LEFT JOIN
        TBL_PatrolUnit u ON u.UnitId = r.UnitId
        LEFT JOIN
        TBL_PatrolWarningLevel wl ON wl.LevelId = r.WarningLevelId
        LEFT JOIN
        TBL_StandardDicSig std ON std.StandardDicId = r.StandardDicId AND std.StandardType = #{standardVer}
        WHERE r.StandardDicId IS NOT NULL
    </select>


    <sql id="PatrolRuleField">
        r.RuleId AS RuleId,
        r.RuleName AS RuleName,
        CAST(r.LimitDown AS FLOAT) AS LimitDown,
        r.LimitDownCalOpId AS LimitDownCalOpId,
        codown.CalOperator AS LimitDownCalOperator,
        CAST(r.LimitUp AS FLOAT) AS LimitUp,
        r.LimitUpCalOpId AS LimitUpCalOpId,
        COALESCE(coup.CalOperator, '') AS LimitUpCalOperator,
        r.UnitId AS UnitId,
        COALESCE(u.UnitSymbol, '') AS UnitSymbol,
        r.WarningLevelId AS WarningLevelId,
        COALESCE(wl.Meaning, '') AS WarningMeaning,
        r.ByPercentage AS ByPercentage,
        CASE
        WHEN r.ByPercentage = 1 THEN '是'
        ELSE '否'
        END AS ByPercentageMeaning,
        CAST(r.RatedValue AS FLOAT) AS RatedValue,
        COALESCE(std.equipmentLogicClassId, -1) AS EquipmentLogicClassId,
        COALESCE(std.EquipmentLogicClass, '不选基类设备') AS StandardEquipmentName,
        COALESCE(r.StandardDicId, -1) AS StandardDicId,
        COALESCE(std.SignalStandardName, '不选基类信号') AS StandardSignalName,
        r.Note AS Note,
        r.Description AS Description,
        (SELECT count(*)
        FROM TBL_PatrolGroupRuleMap m
        WHERE m.RuleId = r.RuleId) GroupCount
    </sql>


    <select id="getPatrolStandardRule" resultMap="RuleResultMap">
        SELECT
        <include refid="PatrolRuleField"/>
        FROM
        TBL_PatrolRule r
        LEFT JOIN
        TBL_PatrolCalOp codown ON codown.CalOpId = r.LimitDownCalOpId
        LEFT JOIN
        TBL_PatrolCalOp coup ON coup.CalOpId = r.LimitUpCalOpId
        LEFT JOIN
        TBL_PatrolUnit u ON u.UnitId = r.UnitId
        LEFT JOIN
        TBL_PatrolWarningLevel wl ON wl.LevelId = r.WarningLevelId
        LEFT JOIN
        TBL_StandardDicSig std ON std.StandardDicId = r.StandardDicId AND std.StandardType = #{standardVer}
        WHERE r.RuleId = #{ruleId}
    </select>


    <select id="selectAllStandardEquipment" resultType="com.siteweb.as.dto.patrol.StandardEquipmentDTO">
        SELECT DISTINCT
        EquipmentLogicClassId,
        EquipmentLogicClass
        FROM tbl_standarddicsig WHERE StandardType = #{standardVer}
    </select>


    <select id="listStandardSignalByEquipment" resultType="java.util.Map">
        SELECT StandardDicId, SignalStandardName
        FROM tbl_standarddicsig
        WHERE EquipmentLogicClassId = #{equipmentLogicClassId} AND StandardType = #{standardVer}
    </select>


    <select id="getPatrolStandardRuleByGroup" resultMap="RuleResultMap">
        SELECT
        <include refid="PatrolRuleField"/>
        FROM
        TBL_PatrolGroupStandardSignalMap bsm
        INNER JOIN TBL_PatrolRule r ON
        r.StandardDicId = bsm.StandardDicId
        LEFT JOIN TBL_PatrolCalOp codown ON
        codown.CalOpId = r.LimitDownCalOpId
        LEFT JOIN TBL_PatrolCalOp coup ON
        coup.CalOpId = r.LimitUpCalOpId
        LEFT JOIN TBL_PatrolUnit u ON
        u.UnitId = r.UnitId
        LEFT JOIN TBL_PatrolWarningLevel wl ON
        wl.LevelId = r.WarningLevelId
        LEFT JOIN TBL_StandardDicSig std ON std.StandardDicId = r.StandardDicId AND std.StandardType = #{standardVer}
        WHERE
        bsm.GroupId = #{groupId}
        ORDER BY
        r.StandardDicId
    </select>

    
</mapper>