<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.AssetMapper">
    <select id="findAssets" resultType="com.siteweb.as.vo.AssetVO">
        SELECT a.AssetId,a.Asset<PERSON>ame,a.AssetNo,a.AssetTypeId, b.ItemValue AssetTypeName,a.UsedDate,a.Capacity,a.Style,a.Brand
        ,a.StateId, c.ItemValue StateName,a.FaultDesc,a.ProcessMode,a.LifeTime
        FROM TBL_Asset a
        INNER JOIN TBL_DataItem b ON a.AssetTypeId = b.ItemId and b.EntryId = 2022
        INNER JOIN TBL_DataItem c ON a.StateId = c.ItemId and c.EntryId = 10
    </select>
    <insert id="addAsset">
        INSERT INTO TBL_Asset(AssetName,AssetNo,AssetTypeId,UsedDate,Capacity,Style,Brand,StateId,FaultDesc,ProcessMode,LifeTime)
        VALUES (#{assetName},#{assetNo},#{assetTypeId},#{usedDate},#{capacity},#{style},#{brand},#{stateId},#{faultDesc},#{processMode},#{lifeTime})
    </insert>
    <insert id="batchAddAsset">
        INSERT INTO tbl_asset (
        AssetName, AssetNo, AssetTypeId, UsedDate,
        Capacity, Style, Brand, StateId,
        FaultDesc, ProcessMode, LifeTime
        ) VALUES
        <foreach collection="assetList" item="asset" separator=",">
            (
            #{asset.assetName}, #{asset.assetNo}, #{asset.assetTypeId}, #{asset.usedDate},
            #{asset.capacity}, #{asset.style}, #{asset.brand}, #{asset.stateId},
            #{asset.faultDesc}, #{asset.processMode}, #{asset.lifeTime}
            )
        </foreach>
    </insert>
    <update id="putAsset">
        UPDATE TBL_Asset T
        SET T.AssetName = #{assetName},        T.AssetNo = #{assetNo},
        T.AssetTypeId = #{assetTypeId},        T.UsedDate = #{usedDate},
        T.Capacity = #{capacity},        T.Style = #{style},
        T.Brand = #{brand},        T.StateId = #{stateId},
        T.FaultDesc = #{faultDesc},        T.ProcessMode = #{processMode},
        T.LifeTime = #{lifeTime}
        WHERE T.AssetId = #{assetId}
    </update>
    <delete id="delAsset">
        DELETE FROM TBL_Asset T WHERE T.AssetId = #{assetId}
    </delete>
</mapper>