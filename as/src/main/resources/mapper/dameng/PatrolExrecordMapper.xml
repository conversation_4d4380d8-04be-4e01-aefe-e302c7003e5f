<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.PatrolExrecordMapper">

    <sql id="AlarmInfoSql">
        SELECT
        r.CenterId,
        r.CenterName,
        r.GroupId,
        r.GroupName,
        r.Station<PERSON>d,
        r.StationName,
        r.EquipmentCategoryName,
        r.EquipmentId,
        r.EquipmentName,
        r.SignalId,
        r.SignalName,
        r.Signal<PERSON>alue,
        r.RecordTime,
        r.Unit,
        r.LimitDown,
        r.LimitDownCalOpId,
        codown.CalOperator AS limitDownCalOperator,
        r.LimitUp,
        r.LimitUpCalOpId,
        coup.CalOperator AS limitUpCalOperator,
        r.ByPercentage,
        CASE when r.ByPercentage = 1 THEN '是'
        ELSE '否' end ByPercentageMeaning ,
        r.Rated<PERSON>,
        r.<PERSON>,
        wl.Meaning AS warningMeaning,
        r.<PERSON>,
        CASE when r.IsPowerOffAlarm = '1' THEN '是'
        ELSE '否' end isPowerOffAlarmMeaning ,
        r.CreateTime
        from TBL_PatrolExRecord r
        left join TBL_PatrolCalOp			codown	on codown.CalOpId	= r.LimitDownCalOpId
        left join TBL_PatrolCalOp			coup	on coup.CalOpId		= r.LimitUpCalOpId
        left join TBL_PatrolWarningLevel	wl		on wl.LevelId		= r.WarningLevelId
    </sql>
    <select id="getAllAlarmInfo" resultType="com.siteweb.as.dto.patrol.AlarmInfoDTO">
        <include refid="AlarmInfoSql"/>
        <where>
            <!-- 中心名称 -->
            <if test="query.centerName != null and query.centerName != ''">
                and r.CenterName like CONCAT('%', #{query.centerName}, '%')
            </if>
            <!-- 区划 -->
            <if test="query.groupName != null and query.groupName != ''">
                and r.GroupName like CONCAT('%', #{query.groupName}, '%')
            </if>
            <!-- 局站 -->
            <if test="query.stationName != null and query.stationName != ''">
                and r.StationName like CONCAT('%', #{query.stationName}, '%')
            </if>
            <!-- 设备类型 -->
            <if test="query.equipmentCategoryName != null and query.equipmentCategoryName != ''">
                and r.EquipmentCategoryName like CONCAT('%', #{query.equipmentCategoryName}, '%')
            </if>
            <!-- 设备 -->
            <if test="query.equipmentName != null and query.equipmentName != ''">
                and r.EquipmentName like CONCAT('%', #{query.equipmentName}, '%')
            </if>
            <!-- 信号 -->
            <if test="query.signalName != null and query.signalName != ''">
                and r.SignalName like CONCAT('%', #{query.signalName}, '%')
            </if>
            <!-- 预警等级 -->
            <if test="query.warningLevelId != null and query.warningLevelId >= 1 and query.warningLevelId &lt;= 4">
                and r.WarningLevelId = #{query.warningLevelId}
            </if>
            <!-- 按百分比 -->
            <if test="query.byPercentage != null and (query.byPercentage == 1 or query.byPercentage == 0)">
                and r.ByPercentage = #{query.byPercentage}
            </if>
            <!-- 是否停电引起 -->
            <if test="query.isPowerOffAlarm != null and query.isPowerOffAlarm != ''">
                and r.IsPowerOffAlarm like CONCAT('%', #{query.isPowerOffAlarm}, '%')
            </if>
        </where>
        <if test="query.sortKey != null and query.sortValue != null and query.sortKey != '' and query.sortValue != ''">
            ORDER BY ${query.sortKey} ${query.sortValue}
        </if>
    </select>
    <select id="getAlarmInfoExport" resultType="com.siteweb.as.dto.patrol.AlarmInfoDTO">
        <include refid="AlarmInfoSql"/>
        where r.CreateTime >= #{startTime} and r.CreateTime &lt; #{endTime}
        order by r.CreateTime desc limit 1000000
    </select>
</mapper>