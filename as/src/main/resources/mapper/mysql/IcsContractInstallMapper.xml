<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.IcsContractInstallMapper">
    <sql id="findContractInfoSql">
        SELECT s.ContractNo,
        s.ProjectName,
        SUM(s.StationCount)   AS stationCount,
        SUM(s.FsuCount)       AS fsuCount,
        SUM(s.EquipmentCount) AS equipmentCount,
        c.<PERSON>ate,
        c.<PERSON>ate,
        c.QualityStartPoint,
        c.QualityPeriod,
        c.QualityTerms
        FROM (SELECT ContractNo,
        ProjectName,
        COUNT(*) AS StationCount,
        0        AS FsuCount,
        0        AS EquipmentCount
        FROM TBL_StationProjectInfo
        GROUP BY ContractNo, ProjectName
        UNION ALL
        SELECT ContractNo,
        ProjectName,
        0        AS StationCount,
        COUNT(*) AS FsuCount,
        0        AS EquipmentCount
        FROM TBL_MonitorUnitProjectInfo
        GROUP BY ContractNo, ProjectName
        UNION ALL
        SELECT ContractNo,
        ProjectName,
        0        AS StationCount,
        0        AS FsuCount,
        COUNT(*) AS EquipmentCount
        FROM TBL_EquipmentProjectInfo
        GROUP BY ContractNo, ProjectName) s
        LEFT JOIN TBL_IcsContractInstall c
        ON s.ContractNo = c.ContractNo AND s.ProjectName = c.ProjectName
        <where>
            <if test="installContractDTO.contractNo != null and installContractDTO.contractNo != ''">
                AND s.ContractNo LIKE CONCAT('%',#{installContractDTO.contractNo},'%')
            </if>
            <if test="installContractDTO.projectName != null and installContractDTO.projectName != ''">
                AND s.ProjectName LIKE CONCAT('%',#{installContractDTO.projectName},'%')
            </if>
        </where>
        GROUP BY s.ContractNo,
        s.ProjectName,
        c.PrimaryDate,
        c.EndDate,
        c.QualityStartPoint,
        c.QualityPeriod,
        c.QualityTerms
        <if test="installContractDTO.orderField != null and installContractDTO.orderDirection != null and installContractDTO.orderField != '' and installContractDTO.orderDirection != ''">
            ORDER BY ${installContractDTO.orderField} ${installContractDTO.orderDirection}
        </if>
    </sql>
    <update id="batchUpdateContractInfo">
        <foreach collection="list" item="item" separator=";">
            UPDATE TBL_IcsContractInstall SET PrimaryDate = #{item.primaryDate},EndDate =
            #{item.endDate},QualityStartPoint = #{item.qualityStartPoint},QualityPeriod =
            #{item.qualityPeriod},QualityTerms = #{item.qualityTerms}
            WHERE ContractNo = #{item.contractNo} AND ProjectName = #{item.projectName}
        </foreach>
    </update>
    <select id="findContractInfoPage" resultType="com.siteweb.as.vo.version.InstallContractVO">
        <include refid="findContractInfoSql"/>
    </select>
    <select id="findContractInfoList" resultType="com.siteweb.as.vo.version.InstallContractVO">
        <include refid="findContractInfoSql"/>
    </select>
</mapper>