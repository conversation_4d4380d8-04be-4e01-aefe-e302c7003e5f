<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.IcsPlatformQrCodeMapper">
    <select id="findPage" resultType="com.siteweb.as.vo.version.IcsPlatformQrcodeVO">
        SELECT p.Id,
               p.EquipmentTypeId,
               e.EquipmentTypeName,
               p.SerialNumber,
               p.EquipmentName,
               p.CreateDate,
               p.OperatorName,
               e.IsCanInputId
        FROM TBL_IcsPlatformQrcode p
                 INNER JOIN TBL_IcsEquipmentType e ON
            p.EquipmentTypeId = e.EquipmentTypeId
        <where>
            <if test="icsPlatformQrcodeDTO.equipmentTypeName != null and icsPlatformQrcodeDTO.equipmentTypeName != ''">
               AND e.EquipmentTypeName LIKE concat('%',#{icsPlatformQrcodeDTO.equipmentTypeName},'%')
            </if>
            <if test="icsPlatformQrcodeDTO.equipmentName != null and icsPlatformQrcodeDTO.equipmentName != ''">
                AND p.EquipmentName LIKE concat('%',#{icsPlatformQrcodeDTO.equipmentName},'%')
            </if>
            <if test="icsPlatformQrcodeDTO.serialNumber != null and icsPlatformQrcodeDTO.serialNumber != ''">
                AND p.SerialNumber LIKE concat('%',#{icsPlatformQrcodeDTO.serialNumber},'%')
            </if>
            <if test="icsPlatformQrcodeDTO.createDate != null and icsPlatformQrcodeDTO.createDate != ''">
                AND p.CreateDate LIKE concat('%',#{icsPlatformQrcodeDTO.createDate},'%')
            </if>
            <if test="icsPlatformQrcodeDTO.operatorName != null and icsPlatformQrcodeDTO.operatorName != ''">
                AND p.OperatorName LIKE concat('%',#{icsPlatformQrcodeDTO.operatorName},'%')
            </if>
        </where>
        <if test="icsPlatformQrcodeDTO.orderField != null and icsPlatformQrcodeDTO.orderDirection != null and icsPlatformQrcodeDTO.orderField != '' and icsPlatformQrcodeDTO.orderDirection != ''">
            ORDER BY ${icsPlatformQrcodeDTO.orderField} ${icsPlatformQrcodeDTO.orderDirection}
        </if>
    </select>
</mapper>