<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.MonitorUnitProjectInfoMapper">
    <sql id="findContractInfoSql">
        SELECT p.StationId,
        s.StationName,p.MonitorUnitId,m.MonitorUnitName,m.IpAddress,p.ProjectName,p.ContractNo,p.InstallTime
        FROM TBL_MonitorUnitProjectInfo p
        INNER JOIN TSL_MonitorUnit m ON p.StationId = m.StationId AND p.MonitorUnitId=m.MonitorUnitId
        INNER JOIN tbl_station s ON s.StationId = p.StationId
        WHERE s.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        <if test="fsuProjectInfoDTO.stationName != null and fsuProjectInfoDTO.stationName != ''">
            AND s.StationName LIKE CONCAT('%',#{fsuProjectInfoDTO.stationName},'%')
        </if>
        <if test="fsuProjectInfoDTO.monitorUnitName != null and fsuProjectInfoDTO.monitorUnitName != ''">
            AND m.MonitorUnitName LIKE CONCAT('%',#{fsuProjectInfoDTO.monitorUnitName},'%')
        </if>
        <if test="fsuProjectInfoDTO.ipAddress != null and fsuProjectInfoDTO.ipAddress != ''">
            AND m.IpAddress LIKE CONCAT('%',#{fsuProjectInfoDTO.ipAddress},'%')
        </if>
        <if test="fsuProjectInfoDTO.contractNo != null and fsuProjectInfoDTO.contractNo != ''">
            AND p.ContractNo LIKE CONCAT('%',#{fsuProjectInfoDTO.contractNo},'%')
        </if>
        <if test="fsuProjectInfoDTO.projectName != null and fsuProjectInfoDTO.projectName != ''">
            AND p.ProjectName LIKE CONCAT('%',#{fsuProjectInfoDTO.projectName},'%')
        </if>
        <if test="fsuProjectInfoDTO.installTime != null and fsuProjectInfoDTO.installTime != ''">
            AND p.InstallTime::varchar LIKE CONCAT('%',#{fsuProjectInfoDTO.installTime},'%')
        </if>
        <if test="fsuProjectInfoDTO.orderField != null and fsuProjectInfoDTO.orderDirection != null and fsuProjectInfoDTO.orderField != '' and fsuProjectInfoDTO.orderDirection != ''">
            ORDER BY ${fsuProjectInfoDTO.orderField} ${fsuProjectInfoDTO.orderDirection}
        </if>
    </sql>
    <update id="batchUpdateContractInfo">
        <foreach collection="list" item="item" separator=";">
            UPDATE TBL_MonitorUnitProjectInfo set ContractNo = #{item.contractNo},ProjectName = #{item.projectName}
            WHERE StationId = #{item.stationId} AND MonitorUnitId = #{item.monitorUnitId}
        </foreach>
    </update>
    <select id="findContractInfoPage" resultType="com.siteweb.as.vo.version.MonitorUnitProjectInfoVO">
     <include refid="findContractInfoSql"/>
    </select>
    <select id="findContractInfoByMonitorUnitId" resultType="com.siteweb.as.vo.version.MonitorUnitProjectInfoVO">
        SELECT p.StationId,
               s.StationName,p.MonitorUnitId,m.MonitorUnitName,m.IpAddress,p.ProjectName,p.ContractNo,p.InstallTime
        FROM TBL_MonitorUnitProjectInfo p
                 INNER JOIN TSL_MonitorUnit m ON p.StationId = m.StationId AND p.MonitorUnitId = m.MonitorUnitId
                 INNER JOIN tbl_station s ON s.StationId = p.StationId
        WHERE m.MonitorUnitId = #{monitorUnitId}
    </select>
    <select id="findContractInfoList" resultType="com.siteweb.as.vo.version.MonitorUnitProjectInfoVO">
        <include refid="findContractInfoSql"/>
    </select>
</mapper>