<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.ASMapper">
    <select id="getStationStructureByCategoryId" resultType="com.siteweb.as.dto.StationStructure">
        SELECT a.StructureId AS id,
        a.StructureName AS name,
        1 AS connectState,
        1 AS projectState,
        a.ParentStructureId AS parentNodeId2,
        20001 AS nodeType,
        1 AS StationCategory,
        a.Latitude AS Latitude,
        a.Longitude AS Longitude
        FROM TBL_StationStructure a
        WHERE a.StructureGroupId IN ( #{treeCategoryId},0);
    </select>
    <select id="getStationMapByCategoryId" resultType="com.siteweb.as.dto.StationStructure">
        SELECT DISTINCT A.StationId AS id,
        A.StationName AS name,A.Con<PERSON>, <PERSON><PERSON> AS projectState,
        B.StructureId AS parentNodeId2,
        20002 AS NodeType,A.StationCategory,
        A.Latitude,A.Longitude
        FROM TBL_Station A,TBL_StationStructureMap B,TBL_StationStructure C
        WHERE A.StationId = B.StationId
        AND B.StructureId = C.StructureId
        AND A.StationCategory != 3
        AND C.StructureGroupId IN ( #{treeCategoryId},0)
        AND A.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ORDER BY A.StationName
    </select>
    <select id="getHouseInfoByStation" resultType="com.siteweb.as.dto.HouseInfoDto">
        select s.StationId, s.StationName, h.HouseId, h.HouseName, h.Description, h.LastUpdateDate
        from TBL_House h inner join TBL_Station s on h.StationId = s.StationId
        where h.StationId = #{stationId}
    </select>
    <select id="getAllOperation" resultType="java.lang.Integer">
        SELECT DISTINCT OperationId
        FROM TBL_Operation
        ORDER BY OperationName;
    </select>
    <select id="getUserRoleMapByUserId" resultType="com.siteweb.admin.entity.UserRoleMap">
        SELECT *
        FROM TBL_UserRoleMap
        WHERE UserId = #{userId}
    </select>
    <select id="getOperationGroupByUserId" resultType="com.siteweb.admin.entity.OperationGroup">
        SELECT DISTINCT
        a.GroupId, a.GroupName, a.Description
        FROM TBL_Account ul
        INNER JOIN TBL_UserRoleMap mp ON ul.UserId = mp.UserId
        INNER JOIN TBL_UserRole ur ON mp.RoleId = ur.RoleId
        INNER JOIN TBL_UserRoleRight urr ON ur.RoleId = urr.RoleId AND urr.OperationType = 1
        INNER JOIN TBL_OperationGroup a ON a.GroupId = urr.OperationId
        WHERE ul.UserId = #{userId};
    </select>
    <select id="getOperationByGroupId" resultType="java.lang.Integer">
        SELECT DISTINCT
        su.OperationId
        FROM TBL_OperationGroupMap am ON a.GroupId = am.GroupId
        INNER JOIN TBL_Operation su ON am.OperationId = su.OperationId
        WHERE am.GroupId = #{groupId};
    </select>
    <select id="getSpecialtyGroupByUserId" resultType="com.siteweb.admin.entity.SpecialtyGroup">
        SELECT DISTINCT
        a.GroupId, a.GroupName, a.Description
        FROM TBL_Account ul
        INNER JOIN TBL_UserRoleMap mp ON ul.UserId = mp.UserId
        INNER JOIN TBL_UserRole ur ON mp.RoleId = ur.RoleId
        INNER JOIN TBL_UserRoleRight urr ON ur.RoleId = urr.RoleId AND urr.OperationType = 3
        INNER JOIN TBL_SpecialtyGroup a ON a.GroupId = urr.OperationId
        WHERE ul.UserId = #{userId};
    </select>
    <select id="getSpecialtyByGroupId" resultType="java.lang.Integer">
        SELECT DISTINCT AS EntryItemId
        FROM TBL_SpecialtyGroupMap
        WHERE SpecialtyGroupId = #{groupId};
    </select>
</mapper>