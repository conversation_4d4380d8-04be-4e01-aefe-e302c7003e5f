<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.as.mapper.PatrolStandardGroupMapper">

    <resultMap id="GroupResultMap" type="com.siteweb.as.dto.patrol.PatrolStandardGroupDTO">
        <id column="GroupId" property="groupId" />
        <result column="GroupName" property="groupName" />
        <result column="Note" property="note" />
        <result column="EquipmentLogicClassId" property="equipmentLogicClassId" />
        <result column="StandardEquipmentName" property="standardEquipmentName" />
        <result column="taskCount" property="taskCount" />
        <collection property="standardSignalList" ofType="com.siteweb.as.dto.patrol.StandardSignalDTO">
            <result column="StandardDicId" property="standardDicId" />
            <result column="SignalStandardName" property="signalStandardName" />
        </collection>
    </resultMap>

    <sql id="PatrolGroupSql">
        SELECT
        pg.GroupId AS GroupId,
        pg.GroupName AS GroupName,
        pg.Note AS Note,
        pm.EquipmentLogicClassId AS EquipmentLogicClassId,
        COALESCE(std.EquipmentLogicClass, '不选标准化设备') StandardEquipmentName,
        pm.StandardDicId AS StandardDicId,
        std.SignalStandardName AS SignalStandardName,
        (select count(*) TaskCount from TBL_PatrolTask task WHERE task.GroupId = pg.GroupId) taskCount
        FROM
        TBL_PatrolStandardGroup pg
        INNER JOIN tbl_patrolGroupStandardSignalMap pm ON
        pg.GroupId = pm.GroupId
        LEFT JOIN TBL_StandardDicSig std ON
        std.StandardDicId = pm.StandardDicId
    </sql>

    <select id="findPatrolStandardGroups" resultMap="GroupResultMap">
        <include refid="PatrolGroupSql"/>
    </select>
    <select id="getPatrolStandardGroup" resultMap="GroupResultMap">
        <include refid="PatrolGroupSql"/>
        where pg.GroupId = #{groupId}
    </select>
    <select id="getGroupParameter" resultType="com.siteweb.as.dto.patrol.GroupParameterDTO">
        select g.GroupId,
        COALESCE(gp.StationTypeIds,'') StationTypeIds,
        COALESCE(gp.StationIds,'') StationIds,
        COALESCE(gp.EquipmentIds,'') EquipmentIds,
        COALESCE(gp.SignalIds,'') SignalIds
        from TBL_PatrolStandardGroup g
        left join  TBL_PatrolGroupParameters gp on gp.GroupId = g.GroupId
        where g.GroupId = #{groupId}
    </select>
</mapper>