<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.IcsContractMaintenanceMapper">
    <sql id="findContractInfoSql">
        SELECT ics.Id,
        ics.ContractNo,
        ics.ProjectName,
        ics.StartDate,
        ics.EndDate,
        ics.MaintenanceTerms
        FROM TBL_IcsContractMaintenance ics
        <where>
            <if test="contractMaintenanceDTO.contractNo != null and contractMaintenanceDTO.contractNo != ''">
                AND ics.contractNo LIKE concat('%',#{contractMaintenanceDTO.contractNo},'%')
            </if>
            <if test="contractMaintenanceDTO.projectName != null and contractMaintenanceDTO.projectName != ''">
                AND ics.projectName LIKE concat('%',#{contractMaintenanceDTO.projectName},'%')
            </if>
            <if test="contractMaintenanceDTO.startDate != null and contractMaintenanceDTO.startDate != ''">
                AND ics.StartDate::varchar LIKE concat('%',#{contractMaintenanceDTO.startDate},'%')
            </if>
            <if test="contractMaintenanceDTO.endDate != null and contractMaintenanceDTO.endDate != ''">
                AND ics.EndDate::varchar LIKE concat('%',#{contractMaintenanceDTO.endDate},'%')
            </if>
            <if test="contractMaintenanceDTO.maintenanceTerms != null and contractMaintenanceDTO.maintenanceTerms != ''">
                AND ics.MaintenanceTerms LIKE concat('%',#{contractMaintenanceDTO.maintenanceTerms},'%')
            </if>
        </where>
        <if test="contractMaintenanceDTO.orderField != null and contractMaintenanceDTO.orderDirection != null and contractMaintenanceDTO.orderField != '' and contractMaintenanceDTO.orderDirection != ''">
            ORDER BY ${contractMaintenanceDTO.orderField} ${contractMaintenanceDTO.orderDirection}
        </if>
    </sql>
    <select id="findContractInfoPage" resultType="com.siteweb.as.entity.IcsContractMaintenance">
       <include refid="findContractInfoSql"/>
    </select>
    <select id="findContractInfoList" resultType="com.siteweb.as.entity.IcsContractMaintenance">
        <include refid="findContractInfoSql"/>
    </select>
</mapper>