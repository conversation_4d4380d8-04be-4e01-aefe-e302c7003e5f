<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.PatrolTaskMapper">

    <sql id="PatrolTaskSql">
        SELECT
        t.TaskId,
        t.TaskName,
        t.CronId,
        c.CronExpression,
        c.Meaning,
        t.GroupId,
        g.GroupName,
        t.IsPowerOffSave,
        CASE
        WHEN t.IsPowerOffSave = 1 THEN '是'
        ELSE '否'
        END IsPowerOffSaveMeaning,
        t.Note
        FROM
        TBL_PatrolTask t
        INNER JOIN TBL_PatrolCronExpression c ON
        c.CronId = t.CronId
        INNER JOIN TBL_PatrolGroup g ON
        g.GroupId = t.GroupId
    </sql>
    <select id="getAllPatrolTask" resultType="com.siteweb.as.dto.patrol.PatrolTaskDTO">
        <include refid="PatrolTaskSql"/>
    </select>
    <select id="getPatrolTask" resultType="com.siteweb.as.dto.patrol.PatrolTaskDTO">
        <include refid="PatrolTaskSql"/>
        where t.TaskId = #{taskId}
    </select>
    <select id="selectPatrolSignal" resultType="com.siteweb.as.dto.patrol.PatrolSignalDTO">
        SELECT
        su.CenterId,
        su.CenterName,
        su.GroupId,
        su.GroupName,
        su.StationId,
        su.StationName,
        su.StationCategory,
        hs.HouseId,
        hs.HouseName,
        es.EquipmentTemplateId,
        ett.EquipmentId,
        ett.EquipmentName,
        ett.Description,
        ett.EquipmentCategory,
        di.ItemValue AS EquipmentCategoryName,
        ete.EquipmentBaseType AS EquipmentBaseTypeId,
        es.SignalId,
        es.SignalName,
        es.Unit,
        es.SignalCategory,
        es.SignalType,
        es.DataType,
        es.BaseTypeId
        FROM  (SELECT
        su.CenterId ,ss.StructureName CenterName,sc.StructureId GroupId,sc.StructureName GroupName,su.StationId,su.StationName,su.StationCategory
        FROM  TBL_Station  su
        INNER JOIN TBL_StationStructureMap sp ON su.StationId =  sp.StationId
        INNER JOIN TBL_StationStructure sc ON sp.StructureId =  sc.StructureId
        INNER JOIN TBL_StationStructure ss ON su.CenterId =  ss.StructureId
        WHERE sc.StructureGroupId IN (0,1)) su
        INNER JOIN TBL_Equipment ett ON ett.StationId = su.StationId
        INNER JOIN TBL_House hs ON ett.StationId = hs.StationId AND ett.HouseId = hs.HouseId
        INNER JOIN TBL_EquipmentTemplate ete ON ett.EquipmentTemplateId = ete.EquipmentTemplateId
        INNER JOIN TBL_Signal es ON ett.EquipmentTemplateId = es.EquipmentTemplateId
        INNER JOIN TBL_DataItem di ON di.EntryId = 7 AND ett.EquipmentCategory = di.ItemId
        INNER JOIN TBL_PatrolGroupBaseSignalMap gbsm on gbsm.BaseTypeId = es.BaseTypeId and gbsm.GroupId = #{groupId}
        <where>
            <if test="stationIds != null and stationIds != ''">
                AND su.StationId  IN (${stationIds})
            </if>
        </where>
    </select>
    <select id="getPatrolPowerOffStationId" resultType="java.lang.Integer">
        SELECT
        su.StationId
        FROM  TBL_Station  su
        INNER JOIN TBL_StationStructureMap sp ON su.StationId =  sp.StationId
        INNER JOIN TBL_StationStructure sc ON sp.StructureId =  sc.StructureId
        INNER JOIN TBL_StationStructure ss ON su.CenterId =  ss.StructureId
        WHERE sc.StructureGroupId IN (0,1)
    </select>
    <select id="getPatrolDisconnectRecordStationId" resultType="java.lang.Integer">
        WITH TEMPCommonStations AS (
        SELECT
        su.StationId
        FROM
        TBL_Station su
        INNER JOIN TBL_StationStructureMap sp ON su.StationId = sp.StationId
        INNER JOIN TBL_StationStructure sc ON sp.StructureId = sc.StructureId
        INNER JOIN TBL_StationStructure ss ON su.CenterId = ss.StructureId
        WHERE
        sc.StructureGroupId IN (0, 1)
        )
        SELECT A.*
        FROM TEMPCommonStations A
        INNER JOIN (
        SELECT a.StationId ,count(b.EquipmentId) Count
        FROM TEMPCommonStations a
        INNER JOIN TBL_Equipment b ON a.StationId = b.StationId
        WHERE b.EquipmentCategory =99
        GROUP BY a.StationId
        HAVING count(b.EquipmentId)>1
        ) B ON A.StationId=B.StationId;
    </select>
</mapper>