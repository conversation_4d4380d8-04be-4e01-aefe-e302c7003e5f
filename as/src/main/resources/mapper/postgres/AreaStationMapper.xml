<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.AreaStationMapper">
    <insert id="batchSaveAreaMap">
        INSERT INTO tbl_areamap (StationId, AreaId)
        VALUES
        <foreach collection="stationIds" item="stationId" separator=",">
            (#{stationId}, #{areaId})
        </foreach>
    </insert>
    <delete id="batchDeleteAreaMap">
        DELETE FROM tbl_areamap
        WHERE AreaId = #{areaId}
        AND StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </delete>


    <select id="getStationByGroupIdAndStructureId" resultType="com.siteweb.as.vo.area.AreaStationVO">
        SELECT a.StationId, a.StationName, c.StructureId, c.StructureName
        FROM TBL_Station a
        INNER JOIN TBL_StationStructureMap b ON a.StationId = b.StationId
        INNER JOIN TBL_StationStructure c ON b.StructureId = c.StructureId
        <where>
            c.StructureGroupId = #{structureGroupId}
            <if test="structureId != null and structureId != -1">
                AND (c.StructureId = #{structureId} OR c.StructureId IN (SELECT StructureId FROM TBL_StationStructure
                WHERE ParentStructureId = #{structureId}))
            </if>
        </where>
        ORDER BY c.StructureId, a.StationName
    </select>
    <select id="getStructureData" resultType="com.siteweb.as.vo.area.StructureDataVO">
        SELECT a.StructureId,
               (COALESCE(b.StructureName, '') || a.StructureName) AS StructureName,
               a.ParentStructureId
        FROM TBL_StationStructure a
                 LEFT JOIN
             TBL_StationStructure b ON a.ParentStructureId = b.StructureId
        WHERE a.StructureGroupId = #{structure};
    </select>
    <select id="findStationByAreaId" resultType="com.siteweb.as.vo.area.AreaStationIdVO">
        SELECT stationid, areaid
        FROM TBL_AreaMap
        WHERE AreaId = #{areaId}
    </select>
</mapper>