<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.StationProjectInfoMapper">
    <sql id="findStationContractInfoSql">
        SELECT t.StationId,
        s.StationName,
        t.ProjectName,
        t.ContractNo,
        t.InstallTime
        FROM TBL_StationProjectInfo t
        INNER JOIN tbl_station s ON s.StationId = t.StationId
        WHERE s.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        <if test="stationProjectInfoDTO.stationName != null and  stationProjectInfoDTO.stationName != ''">
            AND s.StationName LIKE concat('%',#{stationProjectInfoDTO.stationName},'%')
        </if>
        <if test="stationProjectInfoDTO.contractNo != null and  stationProjectInfoDTO.contractNo != ''">
            AND t.ContractNo LIKE concat('%',#{stationProjectInfoDTO.contractNo},'%')
        </if>
        <if test="stationProjectInfoDTO.projectName != null and  stationProjectInfoDTO.projectName != ''">
            AND t.ProjectName LIKE concat('%',#{stationProjectInfoDTO.projectName},'%')
        </if>
        <if test="stationProjectInfoDTO.installTime != null and  stationProjectInfoDTO.installTime != ''">
            AND t.InstallTime::varchar LIKE concat('%',#{stationProjectInfoDTO.installTime},'%')
        </if>
        <if test="stationProjectInfoDTO.orderField != null and stationProjectInfoDTO.orderDirection != null and stationProjectInfoDTO.orderField != '' and stationProjectInfoDTO.orderDirection != ''">
            ORDER BY ${stationProjectInfoDTO.orderField} ${stationProjectInfoDTO.orderDirection}
        </if>
    </sql>
    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE TBL_StationProjectInfo set ProjectName = #{item.projectName},ContractNo = #{item.contractNo}
            where StationId = #{item.stationId}
        </foreach>
    </update>
    <select id="findContractInfoPage" resultType="com.siteweb.as.vo.version.StationProjectInfoVO">
       <include refid="findStationContractInfoSql"/>
    </select>
    <select id="findContractInfoByStationId" resultType="com.siteweb.as.vo.version.StationProjectInfoVO">
        SELECT t.StationId,
               s.StationName,
               t.ProjectName,
               t.ContractNo,
               t.InstallTime
        FROM TBL_StationProjectInfo t
                 INNER JOIN tbl_station s ON s.StationId = t.StationId
        WHERE s.StationId = #{stationId}
    </select>
    <select id="findContractInfo" resultType="com.siteweb.as.vo.version.StationProjectInfoVO">
        <include refid="findStationContractInfoSql"/>
    </select>
</mapper>