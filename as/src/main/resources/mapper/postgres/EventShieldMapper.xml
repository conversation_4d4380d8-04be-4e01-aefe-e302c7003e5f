<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.EventShieldMapper">
    <sql id="eventMaskSql">
        SELECT c.StationId,c.StationName,b.EquipmentId,b.EquipmentName,a.EventId,a.EventName,d.StartTime,d.EndTime,d.UserId,d.Reason,d.TimeGroupId
        FROM tbl_event a
        INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        INNER JOIN tbl_station c ON c.StationId = b.StationId
        LEFT JOIN tbl_eventmask d ON d.EquipmentId = b.EquipmentId AND d.StationId = b.StationId AND a.EventId = d.EventId
        INNER JOIN (SELECT DISTINCT e.stationId FROM tbl_stationstructuremap e INNER JOIN tbl_stationstructure f ON e.StructureId = f.StructureId
        <where>
            <if test="shieldFilterDTO.stationGroupType != null">
                AND f.StructureType = #{shieldFilterDTO.stationGroupType}
            </if>
            <if test="shieldFilterDTO.stationStructureList != null and shieldFilterDTO.stationStructureList.size > 0">
                AND f.StructureId IN
                <foreach collection="shieldFilterDTO.stationStructureList" item="stationStructure" open="(" close=")"
                         separator=",">
                    #{stationStructure}
                </foreach>
            </if>
        </where>
        ) g ON g.stationId = c.StationId
        WHERE b.EquipmentId IN
        <foreach collection="shieldFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        <if test="shieldFilterDTO.startTime != null">
            AND d.StartTime &gt;= #{shieldFilterDTO.startTime}
        </if>
        <if test="shieldFilterDTO.endTime != null">
            AND d.StartTime &lt;= #{shieldFilterDTO.startTime}
        </if>
        <if test="shieldFilterDTO.stationIdList != null and shieldFilterDTO.stationIdList.size > 0">
            AND c.StationId IN
            <foreach collection="shieldFilterDTO.stationIdList" item="stationId" open="(" close=")" separator=",">
                #{stationId}
            </foreach>
        </if>
        <if test="shieldFilterDTO.eventCategoryList != null and shieldFilterDTO.eventCategoryList.size > 0">
            AND a.EventCategory IN
            <foreach collection="shieldFilterDTO.eventCategoryList" item="eventCategory" open="(" close=")" separator=",">
                #{eventCategory}
            </foreach>
        </if>
        <if test="shieldFilterDTO.eventName != null and shieldFilterDTO.eventName != ''">
            AND a.EventName LIKE concat('%',#{shieldFilterDTO.eventName}'%')
        </if>
        <if test="shieldFilterDTO.stationCategoryList != null and shieldFilterDTO.stationCategoryList.size > 0">
            AND b.equipmentCategory IN
            <foreach collection="shieldFilterDTO.stationCategoryList" item="stationCategory" open="(" close=")" separator=",">
                #{stationCategory}
            </foreach>
        </if>
        <if test="shieldFilterDTO.shieldMethod != null and shieldFilterDTO.shieldMethod == 1">
            AND d.StartTime IS NOT NULL
        </if>
        <if test="shieldFilterDTO.shieldMethod != null and shieldFilterDTO.shieldMethod == 2">
            AND exists (select 1 from tbl_timegroupspan span where span.TimeGroupId = d.TimeGroupId)
        </if>
        <if test="shieldFilterDTO.shieldMethod != null and shieldFilterDTO.shieldMethod == 3">
            AND d.TimeGroupId IS NOT NULL
        </if>
        <if test="shieldFilterDTO.orderField != null and shieldFilterDTO.orderDirection != null and shieldFilterDTO.orderField != '' and shieldFilterDTO.orderDirection != ''">
            ORDER BY ${shieldFilterDTO.orderField} ${shieldFilterDTO.orderDirection}
        </if>
    </sql>
    <select id="findEventShieldPage" resultType="com.siteweb.as.dto.shield.EventShieldDTO">
        <include refid="eventMaskSql"/>
    </select>
    <select id="findEventShield" resultType="com.siteweb.as.dto.shield.EventShieldDTO">
        <include refid="eventMaskSql"/>
    </select>
</mapper>