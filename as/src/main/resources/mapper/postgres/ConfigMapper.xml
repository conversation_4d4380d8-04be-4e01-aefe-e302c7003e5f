<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.ConfigMapper">

    <select id="getStationSUList" resultType="com.siteweb.as.dto.config.ConfigSamplerUnitDTO">
        SELECT
        mu.StationId,
        ss.SamplerUnitId,
        ss.PortId,
        ss.MonitorUnitId,
        ss.SamplerId,
        ss.ParentSamplerUnitId,
        ss.SamplerType,
        ss.SamplerUnitName,
        ss.Address,
        ss.SpUnitInterval,
        ss.DllPath,
        ss.ConnectState,
        ss.UpdateTime,
        ss.PhoneNumber,
        ss.Description,
        pt.PortNo,
        pt.PortName,
        pt.PortType,
        pt.Setting
        FROM
        TSL_SamplerUnit ss
        INNER JOIN TSL_MonitorUnit mu ON mu.MonitorUnitId = ss.MonitorUnitId
        INNER JOIN TSL_Port pt ON ss.PortId = pt.PortId
        AND ss.MonitorUnitId = pt.MonitorUnitId
        WHERE
        mu.StationId = #{stationId} AND mu.MonitorUnitCategory != 10;
    </select>
    <select id="getStationRMUList" resultType="com.siteweb.as.dto.config.ConfigMonitorUnitDTO">
        SELECT ws.WorkStationId AS monitorUnitId, 10 AS HostType,ws.WorkStationName AS monitorUnitName, '' AS MonitorUnitCode,
        '' AS  ConfigFileCode, '' AS SampleConfigCode,
        now() AS StartupTime, ws.ConnectState AS State, ws.WorkStationId AS WorkStationId, mu.StationId,
        '' AS IpAddress, 'RMU' AS Description, -1 AS MonitorUnitCategory
        FROM TBL_WorkStation ws
        INNER JOIN TSL_MonitorUnit mu ON ws.WorkStationId = mu.WorkStationId
        WHERE mu.StationId = #{stationId}
    </select>
    <select id="getMaintainInfo" resultType="java.lang.String">
        SELECT
        (
        (SELECT de.DepartmentName
        FROM TBL_Station su
        INNER JOIN TBL_Department de ON su.CompanyId = de.DepartmentId
        WHERE su.StationId = #{stationId})
        || ',' ||
        (SELECT ee.EmployeeName
        FROM TBL_Station su
        INNER JOIN TBL_Employee ee ON su.ContactId = ee.EmployeeId
        WHERE su.StationId = #{stationId})
        ) AS a
    </select>

    <select id="getConfigObject" resultType="com.siteweb.as.dto.config.StationInfoDTO">
        SELECT
        ts.CenterId,
        ts.CenterName,
        ts.GroupId,
        ts.GroupName,
        su.StationId,
        su.StationName,
        su.Latitude,
        su.Longitude,
        su.SetupTime,
        su.CompanyId,
        su.ConnectState,
        su.UpdateTime,
        su.StationCategory,
        su.StationGrade,
        su.StationState,
        su.ContactId,
        su.SupportTime,
        su.OnWayTime,
        su.SurplusTime,
        su.FloorNo,
        su.PropList,
        su.Acreage,
        su.BuildingType,
        su.ContainNode,
        su.Description,
        su.BordNumber,
        su.CenterId,
        su.Enable,
        su.StartTime,
        su.EndTime,
        di.ItemValue AS StationGradeName,
        di.ItemAlias AS StationGradeAlias,
        di.ExtendField1 AS StationGradeImage,
        dc.ItemValue AS StationCategoryName,
        dc.ItemAlias AS StationCategoryAlias,
        dc.ExtendField1 AS StationCategoryImage,
        ds.ItemValue AS StationStateName,
        ds.ItemAlias AS StationStateAlias,
        ds.ExtendField1 AS StationStateImage,
        db.ItemValue AS BuildingTypeName,
        db.ItemAlias AS BuildingTypeAlias,
        db.ExtendField1 AS BuildingTypeImage,
        pss.Reason
        FROM (SELECT sc.StructureId AS CenterId, sc.StructureName AS CenterName,
        ss.StructureId AS GroupId, ss.StructureName AS GroupName,
        su.StationId, su.StationName,
        su.StationState, su.StationCategory,
        su.StationGrade
        FROM TBL_Station su
        INNER JOIN TBL_StationStructure sc ON su.CenterId = sc.StructureId
        INNER JOIN TBL_StationStructureMap sm ON su.StationId = sm.StationId
        INNER JOIN TBL_StationStructure ss ON sm.StructureId = ss.StructureId AND ss.StructureGroupId IN (0, 1)
        WHERE 1 = 1
        AND su.StationId = #{stationId}) ts
        INNER JOIN TBL_Station su ON ts.StationId = su.StationId
        LEFT JOIN TBL_ProjectStateStation pss ON su.StationId = pss.StationId
        INNER JOIN TBL_DataItem di ON su.StationGrade = di.ItemId AND di.EntryId = 2
        INNER JOIN TBL_DataItem dc ON su.StationCategory = dc.ItemId AND dc.EntryId = 71
        INNER JOIN TBL_DataItem ds ON su.StationState = ds.ItemId AND ds.EntryId = 5
        LEFT JOIN TBL_DataItem db ON su.BuildingType = db.ItemId AND db.EntryId = 1001
    </select>
    <select id="selectCenter" resultType="com.siteweb.as.dto.config.StructureDTO">
        SELECT
        StructureId,
        StructureGroupId,
        ParentStructureId,
        StructureName,
        IsUngroup,
        StructureType,
        MapZoom,
        Longitude,
        Latitude,
        Description,
        "enable"
        FROM TBL_StationStructure
        WHERE StructureGroupId = 0 AND ParentStructureId = 0 AND "enable" = 1
    </select>
    <select id="getHouseInfo" resultType="com.siteweb.as.dto.config.ConfigHouseDTO">
        select s.StationId, s.StationName, h.HouseId, h.HouseName, h.Description, h.LastUpdateDate
        from TBL_House h inner join TBL_Station s on h.StationId = s.StationId
        where h.StationId =  #{stationId}
    </select>

</mapper>