<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.PatrolGroupMapper">

    <resultMap id="GroupResultMap" type="com.siteweb.as.dto.patrol.PatrolGroupDTO">
        <id column="GroupId" property="groupId" />
        <result column="GroupName" property="groupName" />
        <result column="Note" property="note" />
        <result column="BaseEquipmentId" property="baseEquipmentId" />
        <result column="BaseEquipmentName" property="baseEquipmentName" />
        <result column="taskCount" property="taskCount" />
       <collection property="baseSignalList" ofType="com.siteweb.as.dto.patrol.BaseSignalTypeInfo">
            <result column="BaseTypeId" property="baseTypeId" />
            <result column="BaseTypeName" property="baseTypeName" />
       </collection>
    </resultMap>

    <sql id="PatrolGroupSql">
        SELECT
        pg.GroupId AS GroupId,
        pg.GroupName AS GroupName,
        pg.Note AS Note,
        pm.BaseEquipmentId AS BaseEquipmentId,
        COALESCE(e.BaseEquipmentName, '不选基类设备') BaseEquipmentName,
        pm.BaseTypeId AS BaseTypeId,
        s.BaseTypeName AS BaseTypeName,
        (select count(*) TaskCount from TBL_PatrolTask task WHERE task.GroupId = pg.GroupId) taskCount
        FROM
        TBL_PatrolGroup pg
        INNER JOIN TBL_PatrolGroupBaseSignalMap pm ON
        pg.GroupId = pm.GroupId
        LEFT JOIN TBL_EquipmentBaseType e ON
        e.BaseEquipmentId = pm.BaseEquipmentId
        LEFT JOIN TBL_SignalBaseDic s ON s.BaseTypeId = pm.BaseTypeId
    </sql>
    <select id="findPatrolGroups" resultMap="GroupResultMap">
        <include refid="PatrolGroupSql"/>
    </select>
    <select id="getPatrolGroup" resultMap="GroupResultMap">
        <include refid="PatrolGroupSql"/>
        where pg.GroupId = #{groupId}
    </select>

    <select id="listStation" resultType="com.siteweb.as.dto.patrol.ValueLabelDTO">
        <choose>
            <when test="stationTypeIds == null">
                select StationId AS value, StationName AS label
                from
                TBL_Station order by StationId
            </when>
            <otherwise>
                select s.StationId AS value, s.StationName AS label
                from TBL_Station s
                inner join TBL_StationBaseMap sbm on sbm.StationCategory = s.StationCategory and sbm.StandardType = #{standardVer}
                and sbm.StationBaseType IN
                <foreach item="id" collection="stationTypeIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
                order by StationId
            </otherwise>
        </choose>
    </select>
    <select id="getGroupParameter" resultType="com.siteweb.as.dto.patrol.GroupParameterDTO">
        select g.GroupId,
        COALESCE(gp.StationTypeIds,'') AS StationTypeIds,
        COALESCE(gp.StationIds,'') AS StationIds,
        COALESCE(gp.EquipmentIds,'') AS EquipmentIds,
        COALESCE(gp.SignalIds,'') AS SignalIds
        from TBL_PatrolGroup g
        left join  TBL_PatrolGroupParameters gp on gp.GroupId = g.GroupId
        where g.GroupId = #{groupId}
    </select>
</mapper>