<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.as.mapper.StationShieldMapper">
    <sql id="stationShieldSql">
        SELECT a.stationId,a.StationName,b.TimeGroupId, b.Reason, b.StartTime, b.EndTime
        FROM tbl_station a
        LEFT JOIN tbl_stationMask b on b.StationId = a.StationId
        INNER JOIN (SELECT DISTINCT d.stationId from tbl_stationstructuremap d INNER JOIN tbl_stationstructure e ON e.StructureId = d.StructureId
        <where>
            <if test="shieldFilterDTO.stationGroupType != null">
                AND e.StructureType = #{shieldFilterDTO.stationGroupType}
            </if>
            <if test="shieldFilterDTO.stationStructureList != null and shieldFilterDTO.stationStructureList.size > 0">
                AND e.StructureId IN
                <foreach collection="shieldFilterDTO.stationStructureList" item="stationStructure" open="(" close=")" separator=",">
                    #{stationStructure}
                </foreach>
            </if>
        </where>
        ) f ON f.stationId = a.StationId
        WHERE a.StationId IN
        <foreach collection="shieldFilterDTO.stationIdList" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        <if test="shieldFilterDTO.startTime != null">
            AND b.StartTime &gt;= #{shieldFilterDTO.startTime}
        </if>
        <if test="shieldFilterDTO.endTime != null">
            AND b.StartTime &lt;= #{shieldFilterDTO.startTime}
        </if>
        <if test="shieldFilterDTO.stationCategoryList != null and shieldFilterDTO.stationCategoryList.size > 0">
            AND a.StationCategory IN
            <foreach collection="shieldFilterDTO.stationCategoryList" item="stationCategory" open="(" close=")" separator=",">
                #{stationCategory}
            </foreach>
        </if>
        <if test="shieldFilterDTO.shieldMethod != null and shieldFilterDTO.shieldMethod == 1">
            AND b.StartTime IS NOT NULL
        </if>
        <if test="shieldFilterDTO.shieldMethod != null and shieldFilterDTO.shieldMethod == 2">
            AND exists (select 1 from tbl_timegroupspan span where span.TimeGroupId = b.TimeGroupId)
        </if>
        <if test="shieldFilterDTO.shieldMethod != null and shieldFilterDTO.shieldMethod == 3">
            AND b.TimeGroupId IS NOT NULL
        </if>
        <if test="shieldFilterDTO.orderField != null and shieldFilterDTO.orderDirection != null and shieldFilterDTO.orderField != '' and shieldFilterDTO.orderDirection != ''">
            ORDER BY ${shieldFilterDTO.orderField} ${shieldFilterDTO.orderDirection}
        </if>
    </sql>
    <select id="findStationShieldPage" resultType="com.siteweb.as.dto.shield.StationShieldDTO">
        <include refid="stationShieldSql"/>
    </select>
    <select id="findStationShield" resultType="com.siteweb.as.dto.shield.StationShieldDTO">
        <include refid="stationShieldSql"/>
    </select>
</mapper>