package com.siteweb.airconditioncontrol.enumeration;

/** 手动群控类型 */
public enum BatchControlType {

    /** 不支持的类型 */
    UN_SUPPORT(-999),
    /** 远程开机 */
    TURN_ON(1),
    /** 远程关机 */
    TURN_OFF(2),
    /** 设置运行温度 */
    SET_TEMPERATURE(3),
    /** 远程运行模式 */
    SET_WORK_MODE(4);

    private Integer value = -1;
    BatchControlType(int value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

    public static BatchControlType valueOf(Integer value) {
        return switch (value) {
            case 1 -> TURN_ON;
            case 2 -> TURN_OFF;
            case 3 -> SET_TEMPERATURE;
            case 4 -> SET_WORK_MODE;
            default -> UN_SUPPORT;
        };
    }
}
