package com.siteweb.airconditioncontrol.enumeration;

/**
 * 定时策略包含的实体种类
 */
public enum ZoneStrategyEntityType {
    /**
     * 定时策略对象
     */
    SCHEME(1),
    /**
     * 操作序列对象
     */
    OPERATION(2);
    private Integer value = -1;

    ZoneStrategyEntityType(int value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

    public static ZoneStrategyEntityType valueOf(Integer value) {
        return switch (value) {
            case 1 -> SCHEME;
            case 2 -> OPERATION;
            default -> null;
        };
    }

}
