package com.siteweb.airconditioncontrol.enumeration;

/** 与群控设备分组所关联的各个模块 */
public enum AutoControlGroupModule {

    /** 分组本身 */
    GROUP(1),
    /** 基础参数 */
    BASIC_PARAM(2),
    /** 关联温度设备 */
    MAP_TEMPERATURE(3),
    /** 关联风机设备 */
    MAP_FAN(4),
    /** 关联定时策略(包括时间策略和操作序列) */
    MAP_ZONE_STRATEGY(5),
    /** 关联能耗指标 */
    MAP_ENERGY_COMPLEXINDEX(6);

    private Integer value = -1;

    AutoControlGroupModule(int value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

    public static AutoControlGroupModule valueOf(Integer value) {
        return switch (value) {
            case 1 -> GROUP;
            case 2 -> BASIC_PARAM;
            case 3 -> MAP_TEMPERATURE;
            case 4 -> MAP_FAN;
            case 5 -> MAP_ZONE_STRATEGY;
            case 6 -> MAP_ENERGY_COMPLEXINDEX;
            default -> null;
        };
    }
}
