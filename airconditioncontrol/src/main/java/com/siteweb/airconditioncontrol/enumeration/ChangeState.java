package com.siteweb.airconditioncontrol.enumeration;

/** 记录变更状态 */
public enum ChangeState {

    /** 未变更 */
    UNCHANGE(0),
    /** 新增 */
    NEW(1),
    /** 修改 */
    UPDATE(2),
    /** 删除 */
    DELETE(3);

    private Integer value = -1;

    ChangeState(int value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

    public static ChangeState valueOf(Integer value) {
        return switch (value) {
            case 0 -> UNCHANGE;
            case 1 -> NEW;
            case 2 -> UPDATE;
            case 3 -> DELETE;
            default -> null;
        };
    }
}
