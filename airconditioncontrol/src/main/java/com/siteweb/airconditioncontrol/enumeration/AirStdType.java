package com.siteweb.airconditioncontrol.enumeration;

/** 标准化设备类型 */
public enum AirStdType {

    /** 普通空调 */
    AIR_COMMON(1),
    /** 专用空调 */
    AIR_SPECIAL(2),
    /** 一拖二空调 */
    AIR_COMMON2(3),
    /** 温湿度采集设备 */
    TEMP_EQUIP(4),
    /** 风机设备 */
    FAN_EQUIP(5);

    private Integer value = -1;

    AirStdType(int value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

    public static AirStdType valueOf(Integer value) {
        return switch (value) {
            case 1 -> AIR_COMMON;
            case 2 -> AIR_SPECIAL;
            case 3 -> AIR_COMMON2;
            case 4 -> TEMP_EQUIP;
            case 5 -> FAN_EQUIP;
            default -> null;
        };
    }
}
