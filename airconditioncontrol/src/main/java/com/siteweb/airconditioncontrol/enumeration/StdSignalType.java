package com.siteweb.airconditioncontrol.enumeration;

/**
 * 需要展示的信号类型
 */
public enum StdSignalType {
    /**
     * 普通空调温度
     */
    NORMALAIRTEMP(2),
    /**
     * 普通空调开关机状态
     */
    NORMALAIRSTATE(4),
    /**
     * 专用空调温度
     */
    SPECIALAIRTEMP(1),
    /**
     * 专用空调开关机状态
     */
    SPECIALAIRSTATE(3),
    /**
     * 一拖二空调1温度
     */
    ONETOTWOAIR1TEMP(1),
    /**
     * 一拖二空调1开关机状态
     */
    ONETOTWOAIR1STATE(3),
    /**
     * 一拖二空调2温度
     */
    ONETOTWOAIR2TEMP(17),
    /**
     * 一拖二空调2开关机状态
     */
    ONETOTWOAIR2STATE(19),
    /**
     * 温湿度设备温度
     */
    TEMPANDHUMIDITYTEMPVALUE(1),
    /**
     * 风机设备开关机状态
     */
    FANSTATE(15);
    private Integer value = -1;

    StdSignalType(int value) {
        this.value = value;
    }

    public Integer value() {
        return this.value;
    }

}
