package com.siteweb.airconditioncontrol.controller;

import com.siteweb.airconditioncontrol.dto.EnergyStatisticsParameter;
import com.siteweb.airconditioncontrol.service.AirGroupReportService;
import com.siteweb.airconditioncontrol.service.AirGroupViewService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirGroupReportController",tags = "分组报表相关接口")
public class AirGroupReportController {

    @Autowired
    private AirGroupViewService airGroupViewService;
    @Autowired
    private AirGroupReportService airGroupReportService;


    @ApiOperation(value = "获取群控分组设备指标统计")
    @PostMapping(value = "/getVirtualComplexSum",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getVirtualComplexYearSum(@RequestBody EnergyStatisticsParameter para) {
        if (para.getTimeType().toLowerCase().equals("y"))
            return ResponseHelper.successful(airGroupReportService.EnergyStatisticsYearReport(para));
        else if (para.getTimeType().toLowerCase().equals("m"))
            return ResponseHelper.successful(airGroupReportService.EnergyStatisticsMonthReport(para));
        else if (para.getTimeType().toLowerCase().equals("d"))
            return ResponseHelper.successful(airGroupReportService.EnergyStatisticsDayReport(para));
        else
            return ResponseHelper.failed("error timeType:"+para.getTimeType());
    }

    @ApiOperation(value = "获取局房分组树")
    @GetMapping(value = "/getStationGroupTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationGroupTree() {
        return ResponseHelper.successful(airGroupReportService.getStationGroupTree());
    }

    @ApiOperation(value = "根据局房分组ID获取下属局房")
    @GetMapping(value = "/getStationByStationGroupId",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationByStationGroupId(@RequestParam Integer stationGroupId) {
        return ResponseHelper.successful(airGroupReportService.getStationsByStationGroupId(stationGroupId));
    }

}
