package com.siteweb.airconditioncontrol.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.dto.AirStdTypeMap;
import com.siteweb.airconditioncontrol.dto.AirconFilter;
import com.siteweb.airconditioncontrol.dto.StructureTreeNode;
import com.siteweb.airconditioncontrol.service.AirBatchControlService;
import com.siteweb.airconditioncontrol.service.AirEquipTemplateService;
import com.siteweb.airconditioncontrol.service.BasicDataService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ResourceStructureEquipmentTreeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "TemplateMapController",tags = "模板映射模块相关接口")
public class TemplateMapController {
    private final Logger log = LoggerFactory.getLogger(TemplateMapController.class);
    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AirEquipTemplateService airEquipTemplateService;
    @Autowired
    private AirBatchControlService airBatchControlService;


    @ApiOperation(value = "得到所有标准空调类型集合")
    @GetMapping(value = "/airStdTypes",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAirStdTypes(){
        return ResponseHelper.successful(basicDataService.getAllAirStdType());
    }
    @ApiOperation(value = "得到温度采集设备类型集合")
    @GetMapping(value = "/temperatureType",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTemperatureType(){
        return ResponseHelper.successful(basicDataService.getTemperatureType());
    }
    @ApiOperation(value = "得到风机类型集合")
    @GetMapping(value = "/fanType",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getFanType(){
        return ResponseHelper.successful(basicDataService.getFanType());
    }
    @ApiOperation(value = "得到所有标准信号的类型集合")
    @GetMapping(value = "/airStdSignalTypes",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAirStdSignalTypes(){
        return ResponseHelper.successful(basicDataService.getAllAirStdSignalType());
    }

    @ApiOperation(value = "得到标准信号集合")
    @GetMapping(value = "/airStdSignals",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAirStdSignalByType(@RequestParam Integer typeId){
        return ResponseHelper.successful(basicDataService.getSignalByType(typeId));
    }

    @ApiOperation(value = "得到所有已经创建有设备的空调类设备模板集合")
    @PostMapping(value = "/airEquipTemplates",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAirEquipTemplates(@RequestBody AirconFilter filter) {
        return ResponseHelper.successful(airEquipTemplateService.getAllAirEquipTemplates(filter));
    }

    @ApiOperation(value = "得到所有已经创建有设备的温湿度采集类设备模板集合")
    @PostMapping(value = "/tempEquipTemplates",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllTempTemplates(@RequestBody AirconFilter filter) {
        return ResponseHelper.successful(airEquipTemplateService.getAllTempEquipTemplates(filter));
    }

    @ApiOperation(value = "得到所有已经创建有设备的风机类设备模板集合")
    @PostMapping(value = "/fanEquipTemplates",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllFanEquipTemplates(@RequestBody AirconFilter filter) {
        return ResponseHelper.successful(airEquipTemplateService.getAllFanEquipTemplates(filter));
    }

    @ApiOperation(value = "通过模板id得到所有信号记录")
    @GetMapping(value = "/signals",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getSignalsByEquipmentTemplateId(@RequestParam Integer equipmentTemplateId){
        return ResponseHelper.successful(airEquipTemplateService.getSignalsByEquipmentTemplateId(equipmentTemplateId));
    }

    @ApiOperation(value = "通过模板id得到所有控制记录")
    @GetMapping(value = "/controls",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getControlsByEquipmentTemplateId(@RequestParam Integer equipmentTemplateId){
        return ResponseHelper.successful(airEquipTemplateService.getControlsByEquipmentTemplateId(equipmentTemplateId));
    }

    @ApiOperation(value = "通过模板id得到所有告警记录")
    @GetMapping(value = "/events",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEventsByEquipmentTemplateId(@RequestParam Integer equipmentTemplateId){
        return ResponseHelper.successful(airEquipTemplateService.getEventsByEquipmentTemplateId(equipmentTemplateId));
    }

    @ApiOperation(value = "获取模板对应的标准信号及映射信号集合")
    @GetMapping(value = "/stdmapsignals",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStdMapSignals(@RequestParam Integer equipmentTemplateId,
                                                           @RequestParam Integer typeId){
        return ResponseHelper.successful(airEquipTemplateService.getStdMapSignals(equipmentTemplateId, typeId));
    }

    @ApiOperation(value = "保存映射信息")
    @PutMapping(value = "/signalmaps",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> setSignalMaps(@RequestBody AirStdTypeMap airStdTypeMap) {

        try {
            ResponseEntity<ResponseResult> result = airEquipTemplateService.saveSignalMaps(airStdTypeMap);
            return result;
        } catch(Exception ex) {
            log.error("setSignalMaps throw Exception:", ex);
            if(airStdTypeMap != null && airStdTypeMap.getEquipmentTemplateId() != null) {
                airEquipTemplateService.cacheUpdate(airStdTypeMap.getEquipmentTemplateId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }

    }

    @ApiOperation(value = "获取局站、监控单元、设备树")
    @GetMapping(value = "/stationEquipmentTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationEquipmentTree(@RequestParam Integer templateTypeId) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        StructureTreeNode equipTree = new StructureTreeNode();
        if(templateTypeId == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "templateTypeId is null", HttpStatus.BAD_REQUEST);
        } else if(templateTypeId.equals(0)) {//空调类设备
            equipTree = airBatchControlService.getStationAirconEquipTree(loginUserId);
        } else if(templateTypeId.equals(1)) {//温度采集类设备
            equipTree = airBatchControlService.getStationTempEquipTree(loginUserId);
        } else if(templateTypeId.equals(2)) {//风机类设备
            equipTree = airBatchControlService.getStationFanEquipTree(loginUserId);
        }
        return ResponseHelper.successful(equipTree);
    }
    @ApiOperation(value = "(新)获取局站、监控单元、设备树")
    @GetMapping(value = "/resStationEquipmentTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResStationEquipmentTree(@RequestParam Integer templateTypeId) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        ResourceStructureEquipmentTreeDTO equipTree = new ResourceStructureEquipmentTreeDTO();
        if(templateTypeId == null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "templateTypeId is null", HttpStatus.BAD_REQUEST);
        } else if(templateTypeId.equals(0)) {//空调类设备
            equipTree = airBatchControlService.getStationAirConEquipResTree(loginUserId);
        } else if(templateTypeId.equals(1)) {//温度采集类设备
            equipTree = airBatchControlService.getStationTempEquipResTree(loginUserId);
        } else if(templateTypeId.equals(2)) {//风机类设备
            equipTree = airBatchControlService.getStationFanEquipResTree(loginUserId);
        }
        return ResponseHelper.successful(equipTree);
    }

}
