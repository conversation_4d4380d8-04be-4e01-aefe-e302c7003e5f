package com.siteweb.airconditioncontrol.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.dto.GroupDistributeParam;
import com.siteweb.airconditioncontrol.service.AirGroupIniService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirGroupConfigOperationController",tags = "分组配置文件操作相关接口")
public class AirGroupConfigOperationController {
    private final Logger log = LoggerFactory.getLogger(AirGroupConfigOperationController.class);

    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    private AirGroupIniService airGroupIniService;


    @ApiOperation(value = "生成并下发某一管理分组的INI配置文件以及SO文件")
    @PutMapping(value = "/groupIniDistribute",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> distributeGroupIni(@RequestParam Integer stationId
                                , @RequestParam Integer virtualEquipmentId, @RequestParam String userName,@RequestParam String passWord) {

        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        if(StringUtils.isBlank(userName) || StringUtils.isBlank(passWord)) {
            return ResponseHelper.failed("-8", "userName or passWord is Empty", HttpStatus.OK);
        }
        return airGroupIniService.distributeGroupIni(stationId, virtualEquipmentId, userId,userName,passWord);
    }

    @ApiOperation(value = "批量生成并下发所选分组的INI配置文件以及SO文件")
    @PutMapping(value = "/groupIniDistributeBatch",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> distributeGroupIniBatch(@RequestBody GroupDistributeParam groupDistributeParam) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        if(groupDistributeParam == null || groupDistributeParam.getStationId() == null || groupDistributeParam.getIds() == null
           || groupDistributeParam.getIds().size() < 1) {
            return ResponseHelper.failed("-10", "Param is Empty", HttpStatus.OK);
        }
        if(StringUtils.isBlank(groupDistributeParam.getUserName()) || StringUtils.isBlank(groupDistributeParam.getPassWord())) {
            return ResponseHelper.failed("-8", "userName or passWord is Empty", HttpStatus.OK);
        }
        return airGroupIniService.distributeGroupIniBatch(groupDistributeParam.getStationId(), groupDistributeParam.getIds(), userId,groupDistributeParam.getUserName(),groupDistributeParam.getPassWord());
    }

    @ApiOperation(value = "校验此分组下的空调、温度、风机设备是否已完成信号配置") 
    @GetMapping(value = "/checkSignalMap",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> checkSignalMap(@RequestParam Integer stationId
            , @RequestParam Integer virtualEquipmentId) {
        return airGroupIniService.checkSignalMapInfo(stationId, virtualEquipmentId);
    }




}
