package com.siteweb.airconditioncontrol.controller;

import com.siteweb.airconditioncontrol.dto.AirGroupParams;
import com.siteweb.airconditioncontrol.dto.AirStdTypeMap;
import com.siteweb.airconditioncontrol.service.AirGroupParamService;
import com.siteweb.airconditioncontrol.service.AutoControlEquipService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirGroupParamController",tags = "分组参数配置相关接口")
public class AirGroupParamController {
    private final Logger log = LoggerFactory.getLogger(AirGroupParamController.class);

    @Autowired
    private AutoControlEquipService autoControlEquipService;
    @Autowired
    private AirGroupParamService airGroupParamService;

    @ApiOperation(value = "根据id获取分组参数")
    @GetMapping(value = "/airGroupParam",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirGroupParam(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId) {
        return ResponseHelper.successful(airGroupParamService.getAirGroupParam(stationId, virtualEquipmentId));
    }

    @ApiOperation(value = "启用或关闭空调管理设备的策略")
    @PutMapping(value = "/turnOnOff",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> turnOnOff(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId, @RequestParam Integer stateValue) {

        try {
            if(stateValue != 0 && stateValue != 1) {
                return ResponseHelper.failed("-1", "invalid stateValue", HttpStatus.OK);
            }
            return airGroupParamService.turnOnOff(stationId, virtualEquipmentId, stateValue);
        } catch (Exception ex) {
            log.error("turnOnOff throw Exception:", ex);
            autoControlEquipService.cacheUpdate(virtualEquipmentId);
            return ResponseHelper.failed("-1", "UnKnow Exception", HttpStatus.OK);
        }
    }

    @ApiOperation(value = "更新空调管理设备的全部参数值")
    @PutMapping(value = "/setParams",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> setParams(@RequestBody AirGroupParams airGroupParams) {

        try {
            if(airGroupParams == null) {
                return ResponseHelper.failed("-1", "Param is null", HttpStatus.OK);
            }
            if(airGroupParams.getStationId() == null || airGroupParams.getVirtualEquipmentId() == null) {
                return ResponseHelper.failed("-1", "Keys is null", HttpStatus.OK);
            }
            if(airGroupParams.getParaEnable() == null || (airGroupParams.getParaEnable() != 0 && airGroupParams.getParaEnable() != 1)) {
                return ResponseHelper.failed("-1", "Enable is invalid", HttpStatus.OK);
            }
            if(airGroupParams.getUpdaterId() == null) {
                return ResponseHelper.failed("-1", "UpdaterId is null", HttpStatus.OK);
            }
            if( airGroupParams.getTempCoolAll() == null ||
                    airGroupParams.getTempCoolStart() == null ||
                    airGroupParams.getTempDiff() == null ||
                    airGroupParams.getRollingCount() == null ||
                    airGroupParams.getRunPeriod() == null ||
                    airGroupParams.getOperationInterval() == null ||
                    airGroupParams.getFanInstall() == null ||
                    airGroupParams.getOutInTempDiff() == null ||
                    airGroupParams.getTempFanStart() == null ||
                    airGroupParams.getTempInFanStop() == null ||
                    airGroupParams.getTempOutFanStop() == null ||
                    airGroupParams.getEnableWarm() == null ||
                    airGroupParams.getTempHotAll() == null ||
                    airGroupParams.getTempHotStart() == null) {
                return ResponseHelper.failed("-1", "Value can not be null", HttpStatus.OK);
            }
            return airGroupParamService.updateParams(airGroupParams);
        } catch (Exception ex) {
            log.error("setParams throw Exception:", ex);
            if(airGroupParams != null && airGroupParams.getVirtualEquipmentId() != null) {
                autoControlEquipService.cacheUpdate(airGroupParams.getVirtualEquipmentId());
            }
            return ResponseHelper.failed("-1", "UnKnow Exception", HttpStatus.OK);
        }
    }



}
