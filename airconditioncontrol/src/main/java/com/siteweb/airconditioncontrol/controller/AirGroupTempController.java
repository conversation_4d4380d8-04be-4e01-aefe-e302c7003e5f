package com.siteweb.airconditioncontrol.controller;

import com.siteweb.airconditioncontrol.dto.AirVirtualEquip;
import com.siteweb.airconditioncontrol.function.CommonUtils;
import com.siteweb.airconditioncontrol.service.AirGroupTempService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirGroupTempController",tags = "分组温湿度设备选择相关接口")
public class AirGroupTempController {
    private final Logger log = LoggerFactory.getLogger(AirGroupTempController.class);

    @Autowired
    private AirGroupTempService airGroupTempService;

    @ApiOperation(value = "获取分组已分配温湿度设备的列表")
    @GetMapping(value = "/airGroupTempEquips",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirGroupTempEquips(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId, Integer tempType) {
        tempType = initTempType(tempType);
        return ResponseHelper.successful(airGroupTempService.getAirGroupTempEquips(stationId, virtualEquipmentId, tempType));
    }

    @ApiOperation(value = "查询得到当前空调分组已选温湿度设备集合")
    @GetMapping(value = "/airGroupTempEquips/selected",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirTempEquipmentsSelected(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId, Integer tempType) {
        tempType = initTempType(tempType);
        return ResponseHelper.successful(airGroupTempService.getAirTempEquipmentsSelected(stationId, virtualEquipmentId, tempType));
    }

    @ApiOperation(value = "查询得到选中监控单元下还未被分配的温湿度类设备集合")
    @GetMapping(value = "/airGroupTempEquips/unselected",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirTempEquipmentsNotSelected(@RequestParam Integer stationId, @RequestParam Integer monitorUnitId) {
        return ResponseHelper.successful(airGroupTempService.getAirTempEquipmentsNotSelected(stationId, monitorUnitId));
    }

    @ApiOperation(value = "保存空调管理虚拟设备关联的温湿度类设备")
    @PutMapping(value = "/airGroupTempEquips",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveAirTempEquipments(@RequestBody AirVirtualEquip mngEquipInfo) {

        try {
            mngEquipInfo.setTempType(initTempType(mngEquipInfo.getTempType()));
            ResponseEntity<ResponseResult> result = airGroupTempService.saveAirTempEquipments(mngEquipInfo);
            return result;
        } catch (Exception ex) {
            log.error("saveAirTempEquipments throw Exception:", ex);
            if(mngEquipInfo != null && mngEquipInfo.getAirMngEquipId() != null) {
                airGroupTempService.cacheUpdate(mngEquipInfo.getAirMngEquipId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    /** 统一处理温度采集器类型(0-室内温度采集器；1-室外温度采集器。null或非法值统一认为是0) */
    private Integer initTempType(Integer tempType) {
        return java.util.Objects.equals(CommonUtils.OUT_TEMP_FLAG,tempType) ? tempType : 0;
    }

}
