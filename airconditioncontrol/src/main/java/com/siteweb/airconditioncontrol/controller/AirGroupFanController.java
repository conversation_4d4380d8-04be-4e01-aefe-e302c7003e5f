package com.siteweb.airconditioncontrol.controller;

import com.siteweb.airconditioncontrol.dto.AirVirtualEquip;
import com.siteweb.airconditioncontrol.service.AirGroupFanService;
import com.siteweb.airconditioncontrol.service.AirGroupTempService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirGroupFanController",tags = "分组风机设备选择相关接口")
public class AirGroupFanController {
    private final Logger log = LoggerFactory.getLogger(AirGroupFanController.class);

    @Autowired
    private AirGroupFanService airGroupFanService;

    @ApiOperation(value = "获取分组已分配风机设备的列表")
    @GetMapping(value = "/airGroupFanEquips",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirGroupTempEquips(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId) {
        return ResponseHelper.successful(airGroupFanService.getAirGroupFanEquips(stationId, virtualEquipmentId));
    }

    @ApiOperation(value = "查询得到当前空调分组已选风机设备集合")
    @GetMapping(value = "/airGroupFanEquips/selected",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirFanEquipmentsSelected(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId) {
        return ResponseHelper.successful(airGroupFanService.getAirFanEquipmentsSelected(stationId, virtualEquipmentId));
    }

    @ApiOperation(value = "查询得到选中监控单元下还未被分配的风机类设备集合")
    @GetMapping(value = "/airGroupFanEquips/unselected",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirFanEquipmentsNotSelected(@RequestParam Integer stationId, @RequestParam Integer monitorUnitId) {
        return ResponseHelper.successful(airGroupFanService.getAirFanEquipmentsNotSelected(stationId, monitorUnitId));
    }

    @ApiOperation(value = "保存空调管理虚拟设备关联的风机类设备")
    @PutMapping(value = "/airGroupFanEquips",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveAirFanEquipments(@RequestBody AirVirtualEquip mngEquipInfo) {

        try {
            ResponseEntity<ResponseResult> result = airGroupFanService.saveAirFanEquipments(mngEquipInfo);
            return result;
        } catch (Exception ex) {
            log.error("saveAirFanEquipments throw Exception:", ex);
            if(mngEquipInfo != null && mngEquipInfo.getAirMngEquipId() != null) {
                airGroupFanService.cacheUpdate(mngEquipInfo.getAirMngEquipId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

}
