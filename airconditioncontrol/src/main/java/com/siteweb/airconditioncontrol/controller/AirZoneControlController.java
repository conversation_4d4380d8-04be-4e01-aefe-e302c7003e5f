package com.siteweb.airconditioncontrol.controller;

import com.siteweb.airconditioncontrol.dto.ZoneOperation;
import com.siteweb.airconditioncontrol.dto.ZoneScheme;
import com.siteweb.airconditioncontrol.service.AirZoneControlService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirZoneControlController",tags = "定时策略相关接口")
public class AirZoneControlController {
    private final Logger log = LoggerFactory.getLogger(AirZoneControlController.class);

    @Autowired
    private AirZoneControlService airZoneControlService;

    @ApiOperation(value = "获取定时策略的列表")
    @GetMapping(value = "/schemes",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getSchemes(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId) {
        return ResponseHelper.successful(airZoneControlService.getSchemes(stationId, virtualEquipmentId));
    }

    @ApiOperation(value = "获取操作序列的列表")
    @GetMapping(value = "/operations",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOperations(@RequestParam String id) {
        return ResponseHelper.successful(airZoneControlService.getOperations(id));
    }

    @ApiOperation(value = "新增一条定时策略")
    @PostMapping(value = "/scheme",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addScheme(@RequestBody ZoneScheme oneScheme) {

        try {
            ResponseEntity<ResponseResult> result = airZoneControlService.addScheme(oneScheme);
            return result;
        } catch (Exception ex) {
            if(oneScheme != null && oneScheme.getVirtualEquipmentId() != null) {
                airZoneControlService.cacheUpdateZoneScheme(oneScheme.getVirtualEquipmentId());
            }
            log.error("addAirScheme throw Exception:", ex);
            return ResponseHelper.failed("-1", ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "新增一条操作序列")
    @PostMapping(value = "/operation",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addOperation(@RequestBody ZoneOperation oneOperation) {
        try {
            ResponseEntity<ResponseResult> result = airZoneControlService.addOperation(oneOperation);
            return result;
        } catch (Exception ex) {
            if(oneOperation != null && oneOperation.getSchemeId() != null) {
                airZoneControlService.cacheUpdateZoneOperation(oneOperation.getSchemeId());
            }
            log.error("addAirOperation throw Exception:", ex);
            return ResponseHelper.failed("-1", ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一条定时策略")
    @DeleteMapping(value = "/scheme",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delScheme(@RequestParam Integer virtualEquipmentId, @RequestParam String schemeId) {

        try {
            ResponseEntity<ResponseResult> result = airZoneControlService.delScheme(virtualEquipmentId, schemeId);
            return result;
        } catch (Exception ex) {
            if(virtualEquipmentId != null) {
                airZoneControlService.cacheUpdateZoneScheme(virtualEquipmentId);
            }
            if(schemeId != null) {
                airZoneControlService.cacheUpdateZoneOperation(schemeId);
            }
            log.error("delScheme throw Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一条操作序列")
    @DeleteMapping(value = "/operation",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delOperation(@RequestParam String schemeId, @RequestParam Integer operationId) {

        try {
            ResponseEntity<ResponseResult> result = airZoneControlService.delOperation(schemeId, operationId);
            return result;
        } catch (Exception ex) {
            if(schemeId != null) {
                airZoneControlService.cacheUpdateZoneOperation(schemeId);
            }
            log.error("delOperation throw Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }


    @ApiOperation(value = "更新一条定时策略")
    @PutMapping(value = "/scheme",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateScheme(@RequestBody ZoneScheme oneScheme) {

        try {
            ResponseEntity<ResponseResult> result = airZoneControlService.updateScheme(oneScheme);
            return result;
        } catch (Exception ex) {
            if(oneScheme != null && oneScheme.getVirtualEquipmentId() != null) {
                airZoneControlService.cacheUpdateZoneScheme(oneScheme.getVirtualEquipmentId());
            }
            log.error("updateScheme throw Exception:", ex);
            return ResponseHelper.failed("-1", ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "更新一条操作序列")
    @PutMapping(value = "/operation",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateOperation(@RequestBody ZoneOperation oneOperation) {
        try {
            ResponseEntity<ResponseResult> result = airZoneControlService.updateOperation(oneOperation);
            return result;
        } catch (Exception ex) {
            if(oneOperation != null && oneOperation.getSchemeId() != null) {
                airZoneControlService.cacheUpdateZoneOperation(oneOperation.getSchemeId());
            }
            log.error("updateOperation throw Exception:", ex);
            return ResponseHelper.failed("-1", ex.getMessage(), HttpStatus.OK);
        }
    }

}
