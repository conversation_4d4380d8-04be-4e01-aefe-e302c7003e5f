package com.siteweb.airconditioncontrol.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.dto.AirVirtualEquip;
import com.siteweb.airconditioncontrol.entity.AirZoneControlScheme;
import com.siteweb.airconditioncontrol.model.AirZoneSchemeInfo;
import com.siteweb.airconditioncontrol.service.*;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirGroupManagerController",tags = "群控设备相关接口")
public class AirGroupManagerController {
    private final Logger log = LoggerFactory.getLogger(AirGroupManagerController.class);
    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private AutoControlEquipService autoControlEquipService;
    @Autowired
    private AirGroupTempService airGroupTempService;
    @Autowired
    private AirGroupFanService airGroupFanService;
    @Autowired
    private AirZoneControlService airZoneControlService;
    @Autowired
    private AirBatchControlService airBatchControlService;

    @ApiOperation(value = "得到局站、监控单元树")
    @GetMapping(value = "/stationTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationTree(){
        return ResponseHelper.successful(basicDataService.getStructureTree(null, null));
    }
    @ApiOperation(value = "(新)得到局站、监控单元树")
    @GetMapping(value = "/resStationTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResStationTree(){
        return ResponseHelper.successful(airBatchControlService.getResourceMonitorOrStationTree(TokenUserUtil.getLoginUserId(),true,false));
    }

    @ApiOperation(value = "查询空调群控设备列表集合")
    @GetMapping(value = "/airVirtualEquipments",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirVirtualEquipments(@RequestParam Integer stationId, @RequestParam Integer monitorUnitId) {
        return ResponseHelper.successful(autoControlEquipService.getAirVirtualEquipments(stationId, monitorUnitId));
    }

    //@ApiOperation(value = "查询得到选中监控单元下所有空调类设备集合")
    //@GetMapping(value = "/airEquipments",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirEquipments(@RequestParam Integer stationId, @RequestParam Integer monitorUnitId) {
        return ResponseHelper.successful(autoControlEquipService.getAirEquipments(stationId, monitorUnitId));
    }

    @ApiOperation(value = "查询得到选中监控单元下还未被分配的空调类设备集合")
    @GetMapping(value = "/airEquipments/unselected",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirEquipmentsNotSelected(@RequestParam Integer stationId, @RequestParam Integer monitorUnitId) {
        return ResponseHelper.successful(autoControlEquipService.getAirEquipmentsNotSelected(stationId, monitorUnitId));
    }

    @ApiOperation(value = "查询得到当前空调分组已选空调类设备集合")
    @GetMapping(value = "/airEquipments/selected",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirEquipmentsSelected(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId) {
        return ResponseHelper.successful(autoControlEquipService.getAirEquipmentsSelected(stationId, virtualEquipmentId));
    }

    @ApiOperation(value = "为监控单元批量创建空调管理设备分组")
    @PostMapping(value = "/airVirtualEquipment/batch",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addOneAirVirtualEquipmentBatch(@RequestParam Integer countOfGroup,@RequestParam Integer monitorUnitId,
                                             @RequestParam Integer ignoreHouse,@RequestParam Integer stationId,@RequestParam Integer updateId) {
        try {
            if(countOfGroup == null || stationId == null || monitorUnitId == null || updateId == null || ignoreHouse == null) {
                return ResponseHelper.failed("-1", "Param is null", HttpStatus.OK);
            }
            if(countOfGroup < 1) {
                return ResponseHelper.failed("-1", "countOfGroup < 1", HttpStatus.OK);
            }
            if(ignoreHouse != 1 && ignoreHouse != 0) {
                return ResponseHelper.failed("-1", "invalid ignoreHouse", HttpStatus.OK);
            }
            ResponseEntity<ResponseResult> result = autoControlEquipService.addVirtualEquipmentBatch(countOfGroup, stationId, monitorUnitId, ignoreHouse, updateId);
            return result;
        } catch (Exception ex) {
            log.error("addOneAirVirtualEquipmentBatch throw Exception:", ex);
            if(stationId != null && monitorUnitId != null) {
                autoControlEquipService.cacheUpdateByMonitorUnitId(stationId, monitorUnitId);
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }

    }

    @ApiOperation(value = "新增一个空调群控设备")
    @PostMapping(value = "/airVirtualEquipment",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addOneAirVirtualEquipment(@RequestBody AirVirtualEquip mngEquipInfo) {

        try {
            ResponseEntity<ResponseResult> result = autoControlEquipService.addOneVirtualEquipment(mngEquipInfo);
            return result;
        } catch (Exception ex) {
            log.error("addOneAirVirtualEquipment throw Exception:", ex);
            if(mngEquipInfo != null && mngEquipInfo.getAirMngEquipId() != null) {
                autoControlEquipService.cacheUpdate(mngEquipInfo.getAirMngEquipId());
                airGroupTempService.cacheUpdate(mngEquipInfo.getAirMngEquipId());
                airGroupFanService.cacheUpdate(mngEquipInfo.getAirMngEquipId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "编辑一个空调群控设备")
    @PutMapping(value = "/airVirtualEquipment",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateOneAirVirtualEquipment(@RequestBody AirVirtualEquip mngEquipInfo) {

        try {
            ResponseEntity<ResponseResult> result = autoControlEquipService.updateOneVirtualEquipment(mngEquipInfo);
            return result;
        } catch (Exception ex) {
            log.error("updateOneAirVirtualEquipment throw Exception:", ex);
            if(mngEquipInfo != null && mngEquipInfo.getAirMngEquipId() != null) {
                autoControlEquipService.cacheUpdate(mngEquipInfo.getAirMngEquipId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一个空调群控设备")
    @DeleteMapping(value = "/airVirtualEquipment",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteOneAirVirtualEquipment(@RequestParam Integer stationId, @RequestParam Integer virtualEquipmentId) {

        try {
            ResponseEntity<ResponseResult> result = autoControlEquipService.deleteOneAirVirtualEquipment(stationId, virtualEquipmentId);
            return result;
        } catch (Exception ex) {
            log.error("deleteOneAirVirtualEquipment throw Exception:", ex);
            autoControlEquipService.cacheUpdate(virtualEquipmentId);
            airGroupTempService.cacheUpdate(virtualEquipmentId);
            airGroupFanService.cacheUpdate(virtualEquipmentId);
            airZoneControlService.cacheUpdateZoneScheme(virtualEquipmentId);
            AirZoneSchemeInfo airZoneSchemeInfo = airZoneControlService.getAllZoneSchemeCacheInfo().get(virtualEquipmentId);
            if(airZoneSchemeInfo != null && airZoneSchemeInfo.getSchemeLists() != null && airZoneSchemeInfo.getSchemeLists().size() > 0) {
                for (AirZoneControlScheme scheme : airZoneSchemeInfo.getSchemeLists()) {
                    airZoneControlService.cacheUpdateZoneOperation(scheme.getSchemeId());
                }
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "获得所有待选空调电量指标")
    @GetMapping(value = "/allAirconComplexindexs",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAirconComplexindexs() {
        return ResponseHelper.successful(autoControlEquipService.getAllAirconComplexindexs());
    }

    @ApiOperation(value = "通过分组id获取其关联指标")
    @GetMapping(value = "/airGroupComplexindexs",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirGroupComplexindexs(@RequestParam Integer stationId,
                                                                   @RequestParam Integer virtualEquipmentId) {
        return ResponseHelper.successful(autoControlEquipService.getAirconComplexindex(stationId, virtualEquipmentId));
    }

    @ApiOperation(value = "关联分组电量指标")
    @PutMapping(value = "/mapAirconComplexindex",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> mapAirconComplexindex(@RequestParam Integer stationId,
         @RequestParam Integer virtualEquipmentId, @RequestParam Integer complexIndexId, Integer userId) {
        try {
            ResponseEntity<ResponseResult> result = autoControlEquipService.mapAirconComplexindex(stationId, virtualEquipmentId, complexIndexId, userId);
            return result;
        } catch (Exception ex) {
            log.error("mapAirconComplexindex throw Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一条分组关联指标")
    @DeleteMapping(value = "/mapAirconComplexindex",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteMapById(@RequestParam String mapId) {
        try {
            ResponseEntity<ResponseResult> result = autoControlEquipService.delMapById(mapId);
            return result;
        } catch (Exception ex) {
            log.error("deleteMapById throw Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

}
