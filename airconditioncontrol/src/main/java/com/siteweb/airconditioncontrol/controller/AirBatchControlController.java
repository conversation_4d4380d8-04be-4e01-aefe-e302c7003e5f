package com.siteweb.airconditioncontrol.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.dto.AirBatchControlGroupInfo;
import com.siteweb.airconditioncontrol.dto.BatchControlCommand;
import com.siteweb.airconditioncontrol.service.AirBatchControlService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** 空调手动群控分组相关接口 */
@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "AirBatchControlController",tags = "手动群控分组相关接口")
public class AirBatchControlController {

    private final Logger log = LoggerFactory.getLogger(AirBatchControlController.class);

    @Autowired
    private AirBatchControlService airBatchControlService;


    @ApiOperation(value = "获取局站、监控单元、空调设备树")
    @GetMapping(value = "/stationAirconEquipmentTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationAirconEquipmentTree() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), "userid is null", HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(airBatchControlService.getStationAirconEquipTree(loginUserId));
    }
    @ApiOperation(value = "(新)获取局站、监控单元、空调设备树")
    @GetMapping(value = "/resStationAirconEquipmentTree",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResStationAirconEquipmentTree() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), "userid is null", HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(airBatchControlService.getStationAirConEquipResTree(loginUserId));
    }

    @ApiOperation(value = "获取所有手动群控分组")
    @GetMapping(value = "/batchControlGroups",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllBatchControlGroups() {
        return ResponseHelper.successful(airBatchControlService.getAllBatchControlGroups());
    }

    @ApiOperation(value = "新增一个手动群控分组")
    @PostMapping(value = "/batchControlGroup",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addOneAirBatchControlGroup(@RequestBody AirBatchControlGroupInfo batchControlGroupInfo) {
        try {
            ResponseEntity<ResponseResult> result = airBatchControlService.addOneAirBatchControlGroup(batchControlGroupInfo);
            return result;
        } catch (Exception ex) {
            log.error("addOneAirBatchControlGroup throw Exception:", ex);
            //更新缓存
            if(batchControlGroupInfo != null && batchControlGroupInfo.getGroupId() != null) {
                airBatchControlService.cacheUpdate(batchControlGroupInfo.getGroupId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一个手动群控分组")
    @DeleteMapping(value = "/batchControlGroup",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteOneAirBatchControlGroup(@RequestParam String groupId) {
        try {
            ResponseEntity<ResponseResult> result = airBatchControlService.deleteOneAirBatchControlGroup(groupId);
            return result;
        } catch (Exception ex) {
            log.error("deleteOneAirBatchControlGroup throw Exception:", ex);
            //更新缓存
            airBatchControlService.cacheUpdate(groupId);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "更新一个手动群控分组")
    @PutMapping(value = "/batchControlGroup",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateOneAirBatchControlGroup(@RequestBody AirBatchControlGroupInfo batchControlGroupInfo) {
        try {
            ResponseEntity<ResponseResult> result = airBatchControlService.updateOneAirBatchControlGroup(batchControlGroupInfo);
            return result;
        } catch (Exception ex) {
            log.error("updateOneAirBatchControlGroup throw Exception:", ex);
            //更新缓存
            if(batchControlGroupInfo != null && batchControlGroupInfo.getGroupId() != null) {
                airBatchControlService.cacheUpdate(batchControlGroupInfo.getGroupId());
            }
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "根据分组id获取该分组信息")
    @GetMapping(value = "/batchControlGroup",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGroupInfoByGroupId(@RequestParam String groupId) {
        return ResponseHelper.successful(airBatchControlService.getGroupInfoByGroupId(groupId));
    }

    @ApiOperation(value = "根据分组id获取空调设备列表")
    @GetMapping(value = "/batchControlGroupEquipments",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirconListByGroupId(@RequestParam String groupId) {
        return ResponseHelper.successful(airBatchControlService.getAirconListByGroupId(groupId));
    }

    @ApiOperation(value = "批量下发空调控制命令")
    @PutMapping(value = "/batchControlSend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchControlSend(@RequestBody BatchControlCommand batchControlCommand) {
        try {
            Integer userId = TokenUserUtil.getLoginUserId();
            if (null == userId) {
                return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                        "user id is null", HttpStatus.BAD_REQUEST);
            }
            ResponseEntity<ResponseResult> result = airBatchControlService.batchControlSend(batchControlCommand, userId);
            return result;
        } catch (Exception ex) {
            log.error("batchControlSend throw Exception:", ex);
            return ResponseHelper.failed("-1", "Unknow Exception", HttpStatus.OK);
        }
    }

    @ApiOperation(value = "根据分组id获取该分组下设备的控制命令执行情况记录")
    @GetMapping(value = "/batchControlResults",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getControlResults(@RequestParam String groupId) {
        return ResponseHelper.successful(airBatchControlService.getControlResultsByGroupId(groupId));
    }

    @ApiOperation(value = "获取当前分组下所有活动控制命令的执行结果数量数据")
    @GetMapping(value = "/batchControlExecResult",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getControlExecResult(@RequestParam String groupId) {
        return ResponseHelper.successful(airBatchControlService.batchControlExecResult(groupId));
    }

}
