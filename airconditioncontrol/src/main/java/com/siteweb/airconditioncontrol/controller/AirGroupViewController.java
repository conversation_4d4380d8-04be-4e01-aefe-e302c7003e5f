package com.siteweb.airconditioncontrol.controller;


import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.service.AirBatchControlService;
import com.siteweb.airconditioncontrol.service.AirGroupViewService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.GraphicRealDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/airconditioncontrol")
@Api(value = "ViewController",tags = "视图接口")
public class AirGroupViewController {

    @Autowired
    private AirGroupViewService airGroupViewService;

    @Autowired
    private GraphicRealDataService graphicRealDataService;

    @Autowired
    private AirBatchControlService airBatchControlService;

    @ApiOperation(value = "概要视图得到局站树")
    @GetMapping(value = "/outLineView/getStationTree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationTree(){
        return ResponseHelper.successful(airGroupViewService.getTree(false,false));
    }
    @ApiOperation(value = "(新)概要视图得到监控单元树")
    @GetMapping(value = "/outLineView/getResStationTree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResStationTree(){
        return ResponseHelper.successful(airBatchControlService.getResourceMonitorOrStationTree(TokenUserUtil.getLoginUserId(),true,true));
    }

    @ApiOperation(value = "概要视图查询局站下监控单元的空调群控设备列表")
    @GetMapping(value = "/outLineView/getMonitorUnitEquipment", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMonitorUnitEquipment(@RequestParam Integer stationId, @RequestParam(required = false) Integer monitorUnitId){
        return ResponseHelper.successful(airGroupViewService.getGroupsEquipmentsByStationAndMonitorUnitId(stationId,monitorUnitId));
    }

    @ApiOperation(value = "概要视图查询局站下空调群控设备列表")
    @GetMapping(value = "/outLineView/getStationAllEquipment", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationAllEquipment(@RequestParam Integer stationId){
        return ResponseHelper.successful(airGroupViewService.getGroupsEquipmentsByStationId(stationId));
    }

    @ApiOperation(value = "概要视图根据id获取设备的状态信息")
    @GetMapping(value = "/outLineView/getEquipmentSignals", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSignals(@RequestParam Integer equipmentId){
        return ResponseHelper.successful(graphicRealDataService.getEquipmentGraphicDataByDeviceId(equipmentId));
    }

    @ApiOperation(value = "拓扑视图")
    @GetMapping(value = "/topologyView/getTopologyView", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTopologyView(){
        return ResponseHelper.successful(airGroupViewService.getTree(true,false));
    }
    @ApiOperation(value = "状态视图得到实时温湿度以及空调开关机状态")
    @GetMapping(value = "/stateView/getTempAndAirState", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTempAndAirState(@RequestParam Integer virtualEquipmentId){
        return ResponseHelper.successful(airGroupViewService.getTempAndAirState(virtualEquipmentId));
    }
    @ApiOperation(value = "状态视图得到空调群控参数")
    @GetMapping(value = "/stateView/getParameterData", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getParameterData(@RequestParam Integer virtualEquipmentId){
        return ResponseHelper.successful(airGroupViewService.getParameterData(virtualEquipmentId));
    }
    @ApiOperation(value = "状态视图得到温度曲线")
    @GetMapping(value = "/stateView/getTempCurve", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTempCurve(@RequestParam Integer virtualEquipmentId,@RequestParam Integer  date,@RequestParam Integer flag){
        return ResponseHelper.successful(airGroupViewService.getTempCurve(virtualEquipmentId,date,flag));
    }
    @ApiOperation(value = "状态视图得到能耗曲线曲线")
    @GetMapping(value = "/stateView/getEnergyCurve", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyCurve(@RequestParam Integer virtualEquipmentId,@RequestParam Integer  date,@RequestParam Integer flag){
        return ResponseHelper.successful(airGroupViewService.getEnergyCurve(virtualEquipmentId,date,flag));
    }
    @ApiOperation(value = "状态视图查询监控单元下分组列表")
    @GetMapping(value = "/stateView/getMonitorUnitGroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> stateViewGetMonitorUnitGroups(@RequestParam Integer stationId, @RequestParam Integer monitorUnitId){
        return ResponseHelper.successful(airGroupViewService.getGroupsByStationAndMonitorUnitId(stationId,monitorUnitId));
    }

    @ApiOperation(value = "状态视图查询局站下分组列表")
    @GetMapping(value = "/stateView/getStationGroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> stateViewGetStationGroups(@RequestParam Integer stationId){
        return ResponseHelper.successful(airGroupViewService.getGroupsByStationId(stationId));
    }

    @ApiOperation(value = "统计视图空调资产和故障空调统计")
    @GetMapping(value = "/statisticsView/getAirAmountStatistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAirAmountStatistics(){
        return ResponseHelper.successful(airGroupViewService.getAirAmountStatistics());
    }
    @ApiOperation(value = "统计视图节能布防运行统计")
    @GetMapping(value = "/statisticsView/getVirtualRunStatistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getVirtualRunStatistics(){
        return ResponseHelper.successful(airGroupViewService.getVirtualRunStatistics());
    }
    @ApiOperation(value = "统计视图一周温度统计运行统计")
    @GetMapping(value = "/statisticsView/getWeeklyTempStatistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getWeeklyTempStatistics(@RequestParam Integer pageName, @RequestParam Integer pageSize, String pathStr,String groupNameStr){
        return ResponseHelper.successful(airGroupViewService.getWeeklyTempStatistics(pageName,pageSize,pathStr,groupNameStr));
    }
    @ApiOperation(value = "统计视图得到一周温度曲线")
    @GetMapping(value = "/statisticsView/getTempCurve", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTempCurve(@RequestParam Integer virtualEquipmentId){
        return ResponseHelper.successful(airGroupViewService.getTempCurve(virtualEquipmentId,7,1));
    }

    @ApiOperation(value = "统计视图群组设备参数运行统计")
    @GetMapping(value = "/statisticsView/getVirtualParamStatistics", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getVirtualParamStatistics(@RequestParam Integer state){
        return ResponseHelper.successful(airGroupViewService.getVirtualParamStatistics(state));
    }

    @ApiOperation(value = "地图看板获取局站树")
    @GetMapping(value = "/mapBoard/getStationTree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMapStationTree(){
        return ResponseHelper.successful(airGroupViewService.getTree(false,true));
    }
    @ApiOperation(value = "（新）地图看板获取局站树")
    @GetMapping(value = "/mapBoard/getResStationTree", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResMapStationTree(){
        return ResponseHelper.successful(airBatchControlService.getResourceMonitorOrStationTree(TokenUserUtil.getLoginUserId(),false,false));
    }

    @ApiOperation(value = "地图看板获取局站布防告警详情")
    @GetMapping(value = "/mapBoard/getStationAlarmInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMapStationAlarmInfo(@RequestParam Integer stationId){
        return ResponseHelper.successful(airGroupViewService.getStationsEquipmentStatistics(stationId));
    }

}
