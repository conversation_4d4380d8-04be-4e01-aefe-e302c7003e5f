<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirBatchControlMapper">

    <resultMap id="AirBatchControlGroupWithIds" type="com.siteweb.airconditioncontrol.dto.AirBatchControlGroupInfo">
        <result column="GroupId" property="groupId"/>
        <result column="GroupName" property="groupName"/>
        <result column="UpdaterId" property="updaterId"/>
        <collection property="airEquipIdList" ofType="String" column="GroupId">
            <result column="EquipmentKey" />
        </collection>
    </resultMap>

    <resultMap id="AirBatchControlGroupInfoMap" type="com.siteweb.airconditioncontrol.dto.AirBatchControlGroupInfo">
        <result column="GroupId" property="groupId"/>
        <result column="GroupName" property="groupName"/>
        <result column="UpdaterId" property="updaterId"/>
        <collection property="airEquipList" ofType="com.siteweb.airconditioncontrol.dto.EquipSampleTblRow" column="GroupId">
            <result column="StationName" property="stationName"/>
            <result column="StationId" property="stationId"/>
            <result column="HouseName" property="houseName"/>
            <result column="EquipmentId" property="equipmentId"/>
            <result column="EquipmentName" property="equipmentName"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="EquipmentTemplateName" property="equipmentTemplateName"/>
            <result column="MonitorUnitId" property="monitorUnitId"/>
            <result column="MonitorUnitName" property="monitorUnitName"/>
        </collection>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO Aircon_BatchControlEquipmentMap(GroupId, StationId, EquipmentId, UpdateId, UpdateTime) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.groupId},#{item.stationId},#{item.equipmentId},#{item.updateId},#{item.updateTime})
        </foreach>
    </insert>
    <update id="updateGroupNameById">
        UPDATE Aircon_BatchControlGroup SET GroupName = #{groupName}, UpdateId = #{updateId}, UpdateTime = #{curDate} WHERE GroupId = #{groupId}
    </update>
    <delete id="delMapByGroupId">
        DELETE FROM Aircon_BatchControlEquipmentMap WHERE GroupId = #{groupId}
    </delete>

    <select id="findAirconEquipmentBaseIds" resultType="java.lang.Integer">
        SELECT BaseEquipmentId FROM TBL_EquipmentBaseType WHERE EquipmentTypeId = 7
    </select>
    <select id="getCountByGroupName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Aircon_BatchControlGroup WHERE GroupName = #{groupName} <if test="excludeId != null"> AND GroupId != #{excludeId}</if>
    </select>
    <select id="findGroupInfoById" resultMap="AirBatchControlGroupWithIds">
        SELECT a.*, CONCAT(b.StationId, '.', b.EquipmentId) AS EquipmentKey FROM Aircon_BatchControlGroup a LEFT JOIN Aircon_BatchControlEquipmentMap b ON a.GroupId = b.GroupId
        WHERE a.GroupId = #{groupId}
    </select>
    <select id="findBatchControlGroupInfo" resultMap="AirBatchControlGroupInfoMap">
        SELECT a.*, ts.StationId, ts.StationName, th.HouseId, th.HouseName, b.EquipmentId, e.EquipmentName,
                e.EquipmentTemplateId, et.EquipmentTemplateName, e.MonitorUnitId, tmu.MonitorUnitName
        FROM Aircon_BatchControlGroup a
        LEFT JOIN Aircon_BatchControlEquipmentMap b ON a.GroupId = b.GroupId
        LEFT JOIN TBL_Equipment e ON b.StationId = e.StationId AND b.EquipmentId = e.EquipmentId
        LEFT JOIN TSL_MonitorUnit tmu ON e.MonitorUnitId = tmu.MonitorUnitId
        LEFT JOIN TBL_Station ts ON e.StationId = ts.StationId
        LEFT JOIN TBL_House th ON e.StationId = th.StationId AND e.houseId = th.houseId
        LEFT JOIN TBL_EquipmentTemplate et ON e.EquipmentTemplateId = et.EquipmentTemplateId
        <if test="groupId != null"> WHERE a.GroupId = #{groupId}</if>
        ORDER BY a.GroupId, b.StationId, th.HouseId, e.MonitorUnitId, b.EquipmentId
    </select>
    <select id="findAllBatchControlGroups"
            resultType="com.siteweb.airconditioncontrol.entity.AirBatchControlGroup">
        SELECT * FROM Aircon_BatchControlGroup ORDER BY CreateTime
    </select>
    <select id="findControlResultsByGroupId" resultType="com.siteweb.airconditioncontrol.dto.AirActiveControlDTO">
        SELECT tacTbl.*, tmu.MonitorUnitName, th.HouseName, e.EquipmentName
        FROM
        (
            SELECT  map.StationId, map.EquipmentId,
                    bcr.StdControlId ControlType, bcr.StdWorkModeFlag WorkModeValue,
                    bcr.AirStdTypeId, bcr.InsertTime RecordTime,
                    tac.StationName,tac.EquipmentName TacEquipmentName,tac.ControlId,tac.ControlName,tac.SerialNo,tac.ControlSeverity,
                    tac.CmdToken,tac.ControlPhase,tac.StartTime,tac.EndTime,tac.ConfirmTime,tac.ConfirmerId,
                    tac.ConfirmerName,tac.ControlResultType,tac.ControlResult,tac.ControlExecuterId,tac.ControlExecuterIdName,
                    tac.ActionId,tac.Description,tac.Retry,tac.BaseTypeId,tac.BaseTypeName,tac.ParameterValues, tac.BaseCondId
        FROM Aircon_BatchControlEquipmentMap map
                INNER JOIN Aircon_BatchControlRecord bcr ON map.StationId = bcr.StationId AND map.EquipmentId = bcr.EquipmentId
                INNER JOIN TBL_ActiveControl tac ON bcr.SerialNo = tac.SerialNo
            WHERE map.GroupId = #{groupId}
        ) tacTbl
        LEFT JOIN TBL_Equipment e ON tacTbl.StationId = e.StationId AND tacTbl.EquipmentId = e.EquipmentId
        LEFT JOIN TSL_MonitorUnit tmu ON e.MonitorUnitId = tmu.MonitorUnitId
        LEFT JOIN TBL_House th ON e.StationId = th.StationId AND e.houseId = th.houseId
        ORDER BY tacTbl.RecordTime, tacTbl.StartTime
    </select>
    <select id="findActiveControlsByUUIDs" resultType="com.siteweb.monitoring.entity.ActiveControl">
        SELECT * FROM TBL_ActiveControl WHERE Description IN (${uuidInStr})
    </select>
    <select id="findMonitorUnitInfoByEquipment" resultType="com.siteweb.monitoring.entity.Equipment">
        select b.MonitorUnitName AS equipmentName, min(a.ResourceStructureId) AS ResourceStructureId,b.MonitorUnitId AS EquipmentId,min(b.stationId) AS stationId
            from tbl_equipment a
                inner join tsl_monitorunit b on b.MonitorUnitId = a.MonitorUnitId
            where a.ResourceStructureId is not null and a.ResourceStructureId != 0
                group by b.MonitorUnitId,b.MonitorUnitName;
    </select>
</mapper>