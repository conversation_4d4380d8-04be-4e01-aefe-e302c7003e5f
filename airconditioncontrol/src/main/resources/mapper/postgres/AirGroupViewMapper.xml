<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirGroupViewMapper">


    <select id="getAllGroupsByStations" resultType="com.siteweb.airconditioncontrol.dto.AirGroupInfo">

        SELECT AAP.VirtualEquipmentId, AAP.StationId, AAP.GroupName, AAP.SamplerAddress, AAP.MonitorUnitId,
        AAP.ParaEnable, cp.ComplexIndexId,
        AAP.LastSuccussDeployTime, AAP.LastDeployState, AAP.LastDeployTime, TM.MonitorUnitName, TS.StationName
        FROM Aircon_AutoControlPara AAP
        INNER JOIN TSL_monitorunit TM ON TM.MonitorUnitId = AAP.MonitorUnitId
        INNER JOIN TBL_Station TS ON TS.StationId = #{stationId}
        LEFT JOIN aircon_complexindexmap cp on AAP.VirtualEquipmentId = cp.VirtualEquipmentId
        WHERE AAP.StationId = #{stationId}
        ORDER BY AAP.SamplerAddress

    </select>
    <select id="findAirGroupInfoByMonitorUnitId" resultType="com.siteweb.airconditioncontrol.dto.AirGroupInfo">
        SELECT AAP.VirtualEquipmentId, AAP.StationId, AAP.GroupName, AAP.SamplerAddress, AAP.MonitorUnitId,
        AAP.ParaEnable, cp.ComplexIndexId,
        AAP.LastSuccussDeployTime, AAP.LastDeployState, AAP.LastDeployTime, TM.MonitorUnitName
        FROM Aircon_AutoControlPara AAP
        INNER JOIN TSL_monitorunit TM ON TM.MonitorUnitId = AAP.MonitorUnitId
        LEFT JOIN aircon_complexindexmap cp on AAP.VirtualEquipmentId = cp.VirtualEquipmentId
        WHERE AAP.MonitorUnitId = #{monitorUnitId} AND AAP.StationId = #{stationId}
        ORDER BY AAP.SamplerAddress
    </select>

    <resultMap id="MonitorUnitGroupsMap" type="com.siteweb.airconditioncontrol.dto.StructureTreeNode">
        <result column="MonitorUnitId" property="nodeId"/>
        <result column="MonitorUnitName" property="nodeName"/>
        <result column="MonitorUnitNodeType" property="nodeType"/>
        <result column="MonitorUnitStationId" property="parentNodeId"/>
        <result column="MonitorUnitParentNodeType" property="parentNodeType"/>
        <collection property="children" ofType="com.siteweb.airconditioncontrol.dto.StructureTreeNode"
                    column="MonitorUnitId">
            <result column="VirtualEquipmentId" property="nodeId"/>
            <result column="GroupName" property="nodeName"/>
            <result column="GroupNodeType" property="nodeType"/>
            <result column="MonitorUnitId" property="parentNodeId"/>
            <result column="GroupParentNodeType" property="parentNodeType"/>
        </collection>
    </resultMap>
    <resultMap id="StationStructureMap" type="com.siteweb.airconditioncontrol.dto.StructureTreeNode">
        <result column="StationId" property="nodeId"/>
        <result column="StationName" property="nodeName"/>
        <result column="Longitude" property="longitude"/>
        <result column="Latitude" property="latitude"/>
        <result column="NodeType" property="nodeType"/>
        <result column="parentId" property="parentNodeId"/>
        <result column="parentName" property="parentNodeName"/>
    </resultMap>
    <select id="findAllGroups" resultMap="MonitorUnitGroupsMap">
        SELECT AAP.VirtualEquipmentId,AAP.MonitorUnitId,AAP.GroupName,AAP.StationId,AAP.MonitorUnitId AS GroupMonitorId,
        CASE WHEN AAP.VirtualEquipmentId is null THEN null ELSE 4 END AS GroupNodeType,
        CASE WHEN AAP.VirtualEquipmentId is null THEN null ELSE 3 END AS GroupParentNodeType
        FROM Aircon_AutoControlPara AAP LEFT JOIN TSL_MonitorUnit tmu ON AAP.MonitorUnitId = tmu.MonitorUnitId ;
    </select>
    <select id="findAllGroupsInfo" resultType="com.siteweb.airconditioncontrol.dto.AirGroupInfo">
        SELECT tsm.StructureId,ts.StationName,ts.StationId,tmu.MonitorUnitName,tmu.MonitorUnitId,
        aap.GroupName,aap.VirtualEquipmentId,aap.ParaEnable,aap.TempCoolAll,aap.TempCoolStart,aap.TempInFanStop,
        aap.TempBottomLow,aap.WorkTemp,aap.RunPeriod,aap.OperationInterval,aap.TempDiff,aap.RollingCount
        FROM aircon_autocontrolpara aap
        LEFT JOIN TSL_MonitorUnit tmu ON aap.MonitorUnitId = tmu.MonitorUnitId
        LEFT JOIN tbl_station ts ON tmu.StationId = ts.StationId
        LEFT JOIN tbl_stationstructuremap tsm ON ts.StationId = tsm.StationId AND tsm.StructureId IN (SELECT StructureId
        FROM tbl_stationstructure WHERE StructureGroupId IN (0,1)) order by VirtualEquipmentId;
    </select>
    <select id="getAllAirEquipments" resultType="com.siteweb.airconditioncontrol.dto.AirBaseTypeInfo">
        SELECT eb.BaseEquipmentName, count(eb.BaseEquipmentName) AS counts FROM tbl_equipment te
                                                                                 LEFT JOIN TBL_EquipmentTemplate et ON te.EquipmentTemplateId = et.EquipmentTemplateId AND et.EquipmentTemplateId
            NOT IN (${airMngEquipTemplateIds})
                                                                                 LEFT JOIN tbl_equipmentbasetype eb ON et.EquipmentBaseType = eb.BaseEquipmentId where te.StationId IN (${stationIdStr})
                                                                                                                                                                   and eb.EquipmentTypeId = '7'group by eb.BaseEquipmentName;
    </select>
    <select id="getAllRunErrorAir" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT count(te.EquipmentId) AS equipmentId  FROM tbl_equipment te
                                                           LEFT JOIN TBL_EquipmentTemplate et ON te.EquipmentTemplateId = et.EquipmentTemplateId AND et.EquipmentTemplateId
            NOT IN (${airMngEquipTemplateIds})
                                                           LEFT JOIN tbl_equipmentbasetype eb ON et.EquipmentBaseType = eb.BaseEquipmentId
                                                           INNER JOIN tbl_activeevent ae ON te.EquipmentId = ae.EquipmentId
        where te.stationId IN (${stationIdStr})
          and eb.EquipmentTypeId = '7' and ae.EndTime is null group by ae.EquipmentId;
    </select>
    <select id="getStationStructureInfo" resultType="com.siteweb.airconditioncontrol.dto.StationStructureInfo">
        select * from tbl_stationstructure;
    </select>
    <select id="getWeeklyTempStatistics" resultType="com.siteweb.airconditioncontrol.dto.TempStatisticsData">
        SELECT min(MinValue) AS minValues, max(MaxValue) AS maxValues,avg(avgValue) AS avgValues FROM
        tbl_signalstatistics where EquipmentId IN
        (${equipmentsIdListIds}) and SignalId IN (${signalListIds}) and StatisticsTime > NOW() - INTERVAL '7 days';
    </select>
    <select id="getComplexIndexIdByVirtualId" resultType="java.lang.String">
        SELECT ComplexIndexId FROM aircon_complexindexmap WHERE VirtualEquipmentId = #{virtualEquipmentId};
    </select>

    <resultMap id="StationMonitorUnitMap" type="com.siteweb.airconditioncontrol.dto.StructureTreeNode">
        <result column="StationId" property="nodeId"/>
        <result column="StationName" property="nodeName"/>
        <result column="Longitude" property="longitude"/>
        <result column="Latitude" property="latitude"/>
        <result column="StationNodeType" property="nodeType"/>
        <result column="ParentNodeId" property="parentNodeId"/>
        <result column="ParentNodeType" property="parentNodeType"/>
    </resultMap>
    <select id="findAllStations" resultMap="StationMonitorUnitMap">
        SELECT *,2 AS StationNodeType FROM tbl_station WHERE StationCategory != 3;
    </select>
    <select id="getAllAirEquipmentsByStationId" resultType="com.siteweb.airconditioncontrol.dto.AirBaseTypeInfo">
        SELECT eb.BaseEquipmentName, count(eb.BaseEquipmentName) AS counts FROM tbl_equipment te
        LEFT JOIN TBL_EquipmentTemplate et ON te.EquipmentTemplateId = et.EquipmentTemplateId AND et.EquipmentTemplateId
        NOT IN (${airMngEquipTemplateIds})
        LEFT JOIN tbl_equipmentbasetype eb ON et.EquipmentBaseType = eb.BaseEquipmentId where eb.EquipmentTypeId =
        '7' and te.StationId = #{stationId} group by eb.BaseEquipmentName;
    </select>
    <select id="getAllRunErrorAirByStationId" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT count(te.StationId) AS counts, ae.EventName,te.* FROM tbl_equipment te
        LEFT JOIN TBL_EquipmentTemplate et ON te.EquipmentTemplateId = et.EquipmentTemplateId AND et.EquipmentTemplateId
        NOT IN (${airMngEquipTemplateIds})
        LEFT JOIN tbl_equipmentbasetype eb ON et.EquipmentBaseType = eb.BaseEquipmentId
        INNER JOIN tbl_activeevent ae ON te.EquipmentId = ae.EquipmentId
        where eb.EquipmentTypeId = '7' and ae.EndTime is null and te.StationId = #{stationId} group by EquipmentId;
    </select>
    <select id="findALlStationByStationGroupId" resultMap="StationStructureMap">
        SELECT *, 2 AS NodeType,tsa.structureId AS parentId,tsa.structureName AS parentName  FROM TBL_Station
        inner join TBL_StationStructure tsa on tsa.structureId = #{stationGroupId}
        WHERE StationId in
        (SELECT res.StationId FROM (SELECT tss.StructureId, tss.StructureName, tssm.StationId FROM
        (SELECT * FROM TBL_StationStructure WHERE StructureGroupId = 1 OR ParentStructureId = 0) tss
        LEFT JOIN TBL_StationStructureMap tssm ON tss.StructureId = tssm.StructureId) res where res.StructureId = #{stationGroupId});
    </select>
    <select id="getAllGroupsByMonitorUnitIds" resultType="com.siteweb.airconditioncontrol.dto.AirGroupInfo">
        SELECT AAP.VirtualEquipmentId, AAP.StationId, AAP.GroupName, AAP.SamplerAddress, AAP.MonitorUnitId,
        AAP.ParaEnable, cp.ComplexIndexId,
        AAP.LastSuccussDeployTime, AAP.LastDeployState, AAP.LastDeployTime, TM.MonitorUnitName
        FROM Aircon_AutoControlPara AAP
        INNER JOIN TSL_monitorunit TM ON TM.MonitorUnitId = AAP.MonitorUnitId
        LEFT JOIN aircon_complexindexmap cp on AAP.VirtualEquipmentId = cp.VirtualEquipmentId
        WHERE AAP.MonitorUnitId IN (${monitorUnitIds})
        ORDER BY AAP.SamplerAddress;
    </select>

    <select id="getAllMonitorUnit" resultType="com.siteweb.airconditioncontrol.dto.AirGroupInfo">
        select MonitorUnitId,MonitorUnitName from tsl_monitorunit where MonitorUnitCategory IN (2,4,6,7,8,9,12)
    </select>
    <select id="getMonitorUnitInfoBymonitorUnitIds"
            resultType="com.siteweb.airconditioncontrol.dto.AirGroupInfo">
        select MonitorUnitId,MonitorUnitName from tsl_monitorunit where MonitorUnitId IN (${monitorUnitIds})
    </select>
</mapper>