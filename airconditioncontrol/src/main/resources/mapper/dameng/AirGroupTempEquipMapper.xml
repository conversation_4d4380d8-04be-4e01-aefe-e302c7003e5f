<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirGroupTempEquipMapper">

    <resultMap id="AirVirtualEquipIdInfoMap" type="com.siteweb.airconditioncontrol.dto.AirVirtualEquip">
        <result column="VirtualEquipmentId" property="airMngEquipId"/>
        <result column="GroupName" property="airMngEquipName"/>
        <result column="StationId" property="stationId"/>
        <result column="MonitorUnitId" property="monitorUnitId"/>
        <collection property="airEquipIdList" ofType="Integer" column="StationId, VirtualEquipmentId">
            <result column="EquipmentId" />
        </collection>
    </resultMap>
    <resultMap id="AirTempEquipInfoMap" type="com.siteweb.airconditioncontrol.model.AirEquipMapInfo">
        <result column="VirtualEquipmentId" property="airMngEquipId"/>
        <result column="GroupName" property="airMngEquipName"/>
        <result column="StationId" property="stationId"/>
        <result column="MonitorUnitId" property="monitorUnitId"/>
        <result column="Operationor" property="updaterId"/>
        <collection property="airEquipList" ofType="com.siteweb.airconditioncontrol.dto.EquipSample" column="StationId, VirtualEquipmentId">
            <result column="equipStationId" property="stationId"/>
            <result column="EquipmentId" property="equipmentId"/>
            <result column="EquipmentName" property="equipmentName"/>
            <result column="EquipmentCategory" property="equipmentCategory"/>
            <result column="EquipmentType" property="equipmentType"/>
            <result column="EquipmentClass" property="equipmentClass"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="EquipmentBaseType" property="equipmentBaseType"/>
            <result column="HouseId" property="houseId"/>
            <result column="EquipMonitorUnitId" property="monitorUnitId"/>
            <result column="SamplerUnitId" property="samplerUnitId"/>
            <result column="Address" property="address"/>
            <result column="PortNo" property="portNo"/>
            <result column="BeOutTemp" property="beOutTemp"/>
        </collection>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO Aircon_TempControlPara(VirtualEquipmentId, EquipmentId, StationId, MonitorUnitId, InsertTime, Operationor, BeOutTemp, ExtendField) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.virtualEquipmentId},#{item.equipmentId},#{item.stationId},#{item.monitorUnitId},#{item.insertTime},#{item.operationor},#{item.beOutTemp},#{item.extendField})
        </foreach>
    </insert>

    <delete id="deleteByVirEquipmentId">
        DELETE FROM Aircon_TempControlPara WHERE VirtualEquipmentId = #{virtualEquipmentId}
    </delete>
    <delete id="deleteByVirEquipmentIdAndType">
        DELETE FROM Aircon_TempControlPara WHERE VirtualEquipmentId = #{virtualEquipmentId}
        <if test="tempType == 1"> AND BeOutTemp=1 </if>
        <if test="tempType != 1"> AND (BeOutTemp IS NULL OR BeOutTemp!=1) </if>
    </delete>

    <select id="findTempEquipsById" resultType="com.siteweb.airconditioncontrol.dto.EquipSampleTblRow">
        SELECT ts.StationName, th.HouseName, atcp.EquipmentId, e.equipmentName, e.EquipmentTemplateId,
                e.MonitorUnitId, atcp.InsertTime UpdateTime, atcp.Operationor UpdaterId, ta.UserName UpdaterName,
                et.EquipmentTemplateName
          FROM
            Aircon_TempControlPara atcp LEFT JOIN TBL_Equipment e ON atcp.StationId = e.StationId AND atcp.EquipmentId = e.EquipmentId
            LEFT JOIN TBL_Station ts ON e.StationId = ts.StationId
            LEFT JOIN TBL_House th ON e.StationId = th.StationId AND e.houseId = th.houseId
            LEFT JOIN TBL_Account ta ON atcp.Operationor = ta.UserId
            LEFT JOIN TBL_EquipmentTemplate et ON e.EquipmentTemplateId = et.EquipmentTemplateId
        WHERE atcp.VirtualEquipmentId = #{virtualEquipmentId} AND atcp.StationId = #{stationId}
            <if test="tempType == 1"> AND BeOutTemp=1 </if>
            <if test="tempType != 1"> AND (BeOutTemp IS NULL OR BeOutTemp!=1) </if>
        ORDER BY atcp.InsertTime DESC, atcp.EquipmentId
    </select>
    <select id="findTempEquipsSelectedById" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT equip.StationId, equip.EquipmentId, equip.EquipmentName, equip.EquipmentCategory, equip.HouseId, equip.MonitorUnitId,
                equip.SamplerUnitId, equip.EquipmentType, equip.EquipmentClass, equip.EquipmentTemplateId, et.EquipmentBaseType
        FROM
            (SELECT EquipmentId FROM Aircon_TempControlPara WHERE StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId}
                <if test="tempType == 1"> AND BeOutTemp=1 </if>
                <if test="tempType != 1"> AND (BeOutTemp IS NULL OR BeOutTemp!=1) </if>) tcp
            INNER JOIN TBL_Equipment equip ON tcp.EquipmentId = equip.EquipmentId
            LEFT JOIN TBL_EquipmentTemplate et ON equip.EquipmentTemplateId = et.EquipmentTemplateId
        ORDER BY equip.EquipmentId
    </select>
    <select id="findAirTempEquipmentsNotSelected" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT equip.StationId, equip.EquipmentId, equip.EquipmentName, equip.EquipmentCategory, equip.HouseId, equip.MonitorUnitId,
                equip.SamplerUnitId, equip.EquipmentType, equip.EquipmentClass, equip.EquipmentTemplateId, et.EquipmentBaseType
        FROM
            (SELECT EquipmentTemplateId, EquipmentTemplateName,EquipmentBaseType FROM TBL_EquipmentTemplate WHERE EquipmentBaseType IN (1004,1006,1009)) et
            INNER JOIN TBL_Equipment equip ON equip.StationId = #{stationId} AND equip.MonitorUnitId = #{monitorUnitId} AND et.EquipmentTemplateId = equip.EquipmentTemplateId
            LEFT JOIN Aircon_TempControlPara tcp ON equip.EquipmentId = tcp.EquipmentId
        WHERE tcp.EquipmentId is null
        ORDER BY equip.EquipmentId
    </select>
    <select id="findAirGroupTempEquipInfoWithIds" resultMap="AirVirtualEquipIdInfoMap">
        SELECT acp.VirtualEquipmentId, acp.GroupName, acp.StationId, acp.MonitorUnitId, tcp.EquipmentId
            FROM Aircon_AutoControlPara acp LEFT JOIN Aircon_TempControlPara tcp
                ON acp.VirtualEquipmentId = tcp.VirtualEquipmentId <if test="tempType == 1"> AND tcp.BeOutTemp=1 </if><if test="tempType != 1"> AND (tcp.BeOutTemp IS NULL OR tcp.BeOutTemp!=1) </if>
        WHERE acp.VirtualEquipmentId = #{virtualEquipmentId}
    </select>
    <select id="findHasBeUsedEquipment" resultType="java.lang.Integer">
        SELECT EquipmentId FROM Aircon_TempControlPara WHERE EquipmentId IN (${equipmentIds}) <if test="virtualEquipmentId != null"> AND VirtualEquipmentId != #{virtualEquipmentId} </if>
    </select>
    <select id="findHasMapTempEquipInfo" resultMap="AirTempEquipInfoMap">
        SELECT acp.VirtualEquipmentId, acp.GroupName, acp.StationId, acp.MonitorUnitId, acp.Operationor,
                e.StationId equipStationId, e.EquipmentId, e.EquipmentName, e.EquipmentCategory, e.EquipmentType, e.EquipmentClass, e.EquipmentTemplateId,
                et.EquipmentBaseType, e.HouseId, e.MonitorUnitId EquipMonitorUnitId, e.SamplerUnitId,
                tsu.Address, tsp.PortNo, tcp.BeOutTemp
        FROM Aircon_AutoControlPara acp LEFT JOIN Aircon_TempControlPara tcp ON acp.VirtualEquipmentId = tcp.VirtualEquipmentId
            LEFT JOIN TBL_Equipment e ON tcp.StationId = e.StationId AND tcp.EquipmentId = e.EquipmentId
            LEFT JOIN TBL_EquipmentTemplate et ON e.EquipmentTemplateId = et.EquipmentTemplateId
            LEFT JOIN TSL_SamplerUnit tsu ON e.MonitorUnitId = tsu.MonitorUnitId AND e.SamplerUnitId = tsu.SamplerUnitId
            LEFT JOIN TSL_Port tsp ON tsu.MonitorUnitId = tsp.MonitorUnitId AND tsu.PortId = tsp.PortId
        WHERE tcp.EquipmentId is not null <if test="virtualEquipmentId != null"> AND acp.VirtualEquipmentId = #{virtualEquipmentId} </if>
    </select>
    <select id="findHasBeUsedEquipmentByCurrentMngEquip" resultType="java.lang.Integer">
        SELECT EquipmentId FROM Aircon_TempControlPara WHERE EquipmentId IN (${equipmentIds}) AND VirtualEquipmentId = #{virtualEquipmentId}
        <if test="tempType != 1"> AND BeOutTemp=1 </if><if test="tempType == 1"> AND (BeOutTemp IS NULL OR BeOutTemp!=1) </if>
    </select>

</mapper>