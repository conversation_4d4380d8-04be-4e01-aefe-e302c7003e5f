<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirTemplateStdTypeMapper">

    <select id="getAllAirEquipTemplates" resultType="com.siteweb.airconditioncontrol.dto.EquipTemplate">
        SELECT et.*, ec.ItemValue EquipmentCategoryName, ts.TypeId, ts.TypeName
        FROM TBL_EquipmentTemplate et
          INNER JOIN (SELECT DISTINCT EquipmentTemplateId FROM TBL_Equipment) eq ON et.EquipmentTemplateId = eq.EquipmentTemplateId
          LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
          LEFT JOIN (SELECT ItemId, ItemValue FROM TBL_DataItem WHERE EntryId = 7) ec ON et.EquipmentCategory = ec.ItemId
        WHERE et.EquipmentBaseType IN (SELECT BaseEquipmentId FROM TBL_EquipmentBaseType WHERE EquipmentTypeId = 7)
        ORDER BY et.EquipmentTemplateId
    </select>
    <select id="getAllTempEquipTemplates" resultType="com.siteweb.airconditioncontrol.dto.EquipTemplate">
        SELECT et.*, ec.ItemValue EquipmentCategoryName, ts.TypeId, ts.TypeName
        FROM TBL_EquipmentTemplate et
            INNER JOIN (SELECT DISTINCT EquipmentTemplateId FROM TBL_Equipment) eq ON et.EquipmentTemplateId = eq.EquipmentTemplateId
            LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
            LEFT JOIN (SELECT ItemId, ItemValue FROM TBL_DataItem WHERE EntryId = 7) ec ON et.EquipmentCategory = ec.ItemId
        WHERE et.EquipmentBaseType IN (1004,1006,1009)
        ORDER BY et.EquipmentTemplateId
    </select>
    <select id="getAllFanEquipTemplates" resultType="com.siteweb.airconditioncontrol.dto.EquipTemplate">
        SELECT et.*, ec.ItemValue EquipmentCategoryName, ts.TypeId, ts.TypeName
        FROM TBL_EquipmentTemplate et
            INNER JOIN (SELECT DISTINCT EquipmentTemplateId FROM TBL_Equipment) eq ON et.EquipmentTemplateId = eq.EquipmentTemplateId
            LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
            LEFT JOIN (SELECT ItemId, ItemValue FROM TBL_DataItem WHERE EntryId = 7) ec ON et.EquipmentCategory = ec.ItemId
        WHERE et.EquipmentBaseType IN (801)
        ORDER BY et.EquipmentTemplateId
    </select>

    <select id="getAllAirEquipTemplates2" resultType="com.siteweb.airconditioncontrol.dto.EquipTemplate">
        SELECT et.*, ts.typeId, ts.typeName FROM TBL_EquipmentTemplate et LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
        WHERE et.EquipmentCategory IN (41,42,43,44,45) <!-- 41-中央空调(风冷) 42-中央空调(水冷) 43-专用空调(风冷) 44-专用空调(水冷) 45-分体空调  (EntryId = 7)-->
        ORDER BY et.EquipmentTemplateId
    </select>
    <select id="getTemplateStdTypeById" resultType="com.siteweb.airconditioncontrol.entity.AirTemplateStdType">
        SELECT et.EquipmentTemplateId, ts.TypeId, ts.TypeName
        FROM TBL_EquipmentTemplate et LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
        WHERE et.EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY et.EquipmentTemplateId
    </select>
</mapper>