<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.SwSelectMapper">

    <resultMap id="SignalItemMap" type="com.siteweb.airconditioncontrol.dto.SignalItem">
        <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="SignalId" property="signalId"/>
        <result column="SignalName" property="signalName"/>
        <result column="SignalCategory" property="signalCategory"/>
        <result column="SignalCategoryName" property="signalCategoryName"/>
        <result column="SignalType" property="signalType"/>
        <result column="SignalTypeName" property="signalTypeName"/>
        <result column="Unit" property="unit"/>
        <result column="DisplayIndex" property="displayIndex"/>
        <result column="ShowPrecision" property="showPrecision"/>
        <result column="BaseTypeId" property="baseTypeId"/>
        <result column="Visible" property="visible"/>
        <result column="ChannelType" property="channelType"/>
        <result column="ChannelNo" property="channelNo"/>
        <result column="Expression" property="expression"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="meaningsList" ofType="com.siteweb.monitoring.entity.SignalMeanings"
                    column="EquipmentTemplateId,SignalId">
            <id column="MeaningId" property="id"/>
            <result column="SmEquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="SmSignalId" property="signalId"/>
            <result column="StateValue" property="stateValue"/>
            <result column="Meanings" property="meanings"/>
        </collection>
    </resultMap>
    <resultMap id="ControlItemMap" type="com.siteweb.airconditioncontrol.dto.ControlItem">
        <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="ControlId" property="controlId"/>
        <result column="ControlName" property="controlName"/>
        <result column="CommandType" property="commandType"/>
        <result column="CommandTypeName" property="commandTypeName"/>
        <result column="DisplayIndex" property="displayIndex"/>
        <result column="BaseTypeId" property="baseTypeId"/>
        <result column="BaseTypeName" property="baseTypeName"/>
        <result column="SignalId" property="signalId"/>
        <result column="MaxValue" property="maxValue"/>
        <result column="Minvalue" property="minvalue"/>

        <result column="CmdToken" property="cmdToken"/>
        <result column="DataType" property="dataType"/>
        <result column="ControlSeverity" property="controlSeverity"/>
        <result column="DefaultValue" property="defaultValue"/>
        <result column="Visible" property="visible"/>
        <result column="Enable" property="enable"/>
        <result column="ControlType" property="controlType"/>
        <result column="ChannelNo" property="channelNo"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="controlMeaningsList" ofType="com.siteweb.monitoring.entity.ControlMeanings"
                    column="EquipmentTemplateId,ControlId">
            <id column="MeaningId" property="id"/>
            <result column="CmEquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="CmControlId" property="controlId"/>
            <result column="ParameterValue" property="parameterValue"/>
            <result column="Meanings" property="meanings"/>
        </collection>
    </resultMap>

    <resultMap id="EventItemMap" type="com.siteweb.airconditioncontrol.dto.EventItem">
        <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="Enable" property="enable"/>
        <result column="Visible" property="visible"/>
        <result column="DisplayIndex" property="displayIndex"/>
        <result column="EventId" property="eventId"/>
        <result column="EventName" property="eventName"/>
        <result column="StartType" property="startType"/>
        <result column="EndType" property="endType"/>
        <result column="StartExpression" property="startExpression"/>
        <result column="SuppressExpression" property="suppressExpression"/>
        <result column="SignalId" property="signalId"/>
        <result column="EventCategory" property="eventCategory"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="eventConditions" ofType="com.siteweb.monitoring.entity.EventCondition"
                    column="EquipmentTemplateId,EventId">
            <id column="ConditionId" property="id"/>
            <id column="EventConditionId" property="eventConditionId"/>
            <id column="EcEquipmentTemplateId" property="equipmentTemplateId"/>
            <id column="EcEventId" property="eventId"/>
            <id column="StartOperation" property="startOperation"/>
            <id column="StartCompareValue" property="startCompareValue"/>
            <id column="StartDelay" property="startDelay"/>
            <id column="EndOperation" property="endOperation"/>
            <id column="EndCompareValue" property="endCompareValue"/>
            <id column="EndDelay" property="endDelay"/>
            <id column="Frequency" property="frequency"/>
            <id column="FrequencyThreshold" property="frequencyThreshold"/>
            <id column="Meanings" property="meanings"/>
            <id column="EquipmentState" property="equipmentState"/>
            <id column="BaseTypeId" property="baseTypeId"/>
            <id column="EventSeverity" property="eventSeverity"/>
            <id column="StandardName" property="standardName"/>
        </collection>
    </resultMap>

    <resultMap id="StationStructureMap" type="com.siteweb.airconditioncontrol.dto.StructureTreeNode">
        <result column="StructureId" property="nodeId"/>
        <result column="StructureName" property="nodeName"/>
        <result column="Longitude" property="longitude"/>
        <result column="Latitude" property="latitude"/>
        <result column="NodeType" property="nodeType"/>
        <result column="ParentStructureId" property="parentNodeId"/>
        <result column="ParentNodeType" property="parentNodeType"/>
        <collection property="structureChildren" ofType="Integer"
                    column="StructureId">
            <result column="StationId" />
        </collection>
    </resultMap>

    <resultMap id="StationMonitorUnitMap" type="com.siteweb.airconditioncontrol.dto.StructureTreeNode">
        <result column="StationId" property="nodeId"/>
        <result column="StationName" property="nodeName"/>
        <result column="StationNodeType" property="nodeType"/>
        <result column="ParentNodeId" property="parentNodeId"/>
        <result column="ParentNodeType" property="parentNodeType"/>
        <collection property="children" ofType="com.siteweb.airconditioncontrol.dto.StructureTreeNode"
                    column="StationId">
            <result column="MonitorUnitId" property="nodeId"/>
            <result column="MonitorUnitName" property="nodeName"/>
            <result column="MonitorUnitNodeType" property="nodeType"/>
            <result column="MonitorUnitStationId" property="parentNodeId"/>
            <result column="MonitorUnitParentNodeType" property="parentNodeType"/>
        </collection>
    </resultMap>

    <select id="findSignalsByEquipmentTemplateId" resultMap="SignalItemMap">
        select a.EquipmentTemplateId, a.SignalId,a.SignalName, a.SignalCategory,a.SignalType,a.Unit, a.BaseTypeId, a.ShowPrecision,
            a.channelType, a.channelNo,a.DisplayIndex, a.Visible, a.Expression,
            sc.ItemValue SignalCategoryName, st.ItemValue SignalTypeName,
            sm.Id MeaningId, sm.EquipmentTemplateId SmEquipmentTemplateId, sm.SignalId SmSignalId,sm.StateValue, sm.Meanings
        from tbl_signal a
        left join (SELECT ItemId, ItemValue FROM TBL_DataItem WHERE EntryId = 17) sc on a.SignalCategory = sc.ItemId
        left join (SELECT ItemId, ItemValue FROM TBL_DataItem WHERE EntryId = 18) st on a.SignalType = st.ItemId
        left join tbl_signalMeanings sm on a.EquipmentTemplateId = sm.EquipmentTemplateId and a.SignalId = sm.SignalId
        where a.EquipmentTemplateId = #{equipmentTemplateId} AND a.Visible = 1 AND a.ChannelNo != -2
          ORDER BY a.DisplayIndex
    </select>

    <select id="findControlsByEquipmentTemplateId" resultMap="ControlItemMap">
        SELECT a.EquipmentTemplateId, a.ControlId,a.ControlName, a.CommandType, a.ControlType, a.SignalId, a.Visible, a.Enable
            , a.BaseTypeId, a.Minvalue, a.MaxValue, a.DisplayIndex, a.CmdToken, a.DataType, a.ControlSeverity, a.DefaultValue
            , s.ChannelNo, ct.ItemValue CommandTypeName
            , cm.Id MeaningId, cm.ParameterValue, cm.Meanings, cm.EquipmentTemplateId CmEquipmentTemplateId, cm.ControlId CmControlId
        FROM tbl_Control a
            INNER JOIN TBL_Signal s ON  a.EquipmentTemplateId = s.EquipmentTemplateId AND a.SignalId = s.SignalId
            left join tbl_controlMeanings cm on a.EquipmentTemplateId = cm.EquipmentTemplateId and a.ControlId = cm.ControlId
            left join (SELECT ItemId, ItemValue FROM TBL_DataItem WHERE EntryId = 32) ct on a.CommandType = ct.ItemId
        WHERE a.EquipmentTemplateId =#{equipmentTemplateId} AND a.Visible = 1
        ORDER BY a.DisplayIndex
    </select>

    <select id="findEventsByEquipmentTemplateId" resultMap="EventItemMap">
        SELECT a.*
        , ec.Id ConditionId, ec.EventConditionId, ec.StartOperation, ec.StartCompareValue, ec.StartDelay
        , ec.EndOperation, ec.EndCompareValue, ec.EndDelay, ec.Frequency, ec.FrequencyThreshold, ec.Meanings
        , ec.EquipmentState, ec.BaseTypeId, ec.EventSeverity, ec.StandardName
        , ec.EquipmentTemplateId EcEquipmentTemplateId, ec.EventId EcEventId
         FROM TBL_Event a LEFT JOIN TBL_EventCondition ec ON a.EquipmentTemplateId = ec.EquipmentTemplateId AND a.EventId = ec.EventId
        WHERE a.EquipmentTemplateId =#{equipmentTemplateId} AND a.Visible = 1
         ORDER BY a.DisplayIndex
    </select>

    <select id="findAllStationStructure" resultMap="StationStructureMap">
        SELECT tss.StructureId, tss.StructureName,tss.Longitude,tss.Latitude, 1 NodeType, tss.ParentStructureId, 1 ParentNodeType, tssm.StationId
            FROM (SELECT * FROM TBL_StationStructure WHERE StructureGroupId = 1 OR ParentStructureId = 0) tss
                LEFT JOIN TBL_StationStructureMap tssm ON tss.StructureId = tssm.StructureId
        ORDER BY tss.StructureId, tssm.StationId
    </select>
    <select id="findAllStationMonitorUnit" resultMap="StationMonitorUnitMap">
        SELECT ts.StationId, ts.StationName, 2 StationNodeType,
            tmu.MonitorUnitId, tmu.MonitorUnitName, tmu.StationId MonitorUnitStationId,
            CASE WHEN tmu.MonitorUnitId is null THEN null ELSE 3 END MonitorUnitNodeType,
            CASE WHEN tmu.MonitorUnitId is null THEN null ELSE 2 END MonitorUnitParentNodeType
        FROM TBL_Station ts LEFT JOIN TSL_MonitorUnit tmu ON ts.StationId = tmu.StationId AND tmu.MonitorUnitCategory IN (2,4,6,7,8,9,12,16,18) <!-- 只查询智能采集器(16是workstation) -->
        WHERE ts.StationCategory != 3
    </select>

</mapper>