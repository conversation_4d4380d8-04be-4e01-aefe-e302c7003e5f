<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirAutoControlParaMapper">

    <resultMap id="AirVirtualEquipIdInfoMap" type="com.siteweb.airconditioncontrol.dto.AirVirtualEquip">
        <result column="VirtualEquipmentId" property="airMngEquipId"/>
        <result column="GroupName" property="airMngEquipName"/>
        <result column="StationId" property="stationId"/>
        <result column="MonitorUnitId" property="monitorUnitId"/>
        <collection property="airEquipIdList" ofType="Integer" column="StationId, VirtualEquipmentId">
            <result column="EquipmentId" />
        </collection>
    </resultMap>

    <resultMap id="AirVirtualEquipInfoMap" type="com.siteweb.airconditioncontrol.model.AirVirtualEquipInfo">
        <result column="VirtualEquipmentId" property="airMngEquipId"/>
        <result column="GroupName" property="airMngEquipName"/>
        <result column="StationId" property="stationId"/>
        <result column="MonitorUnitId" property="monitorUnitId"/>
        <result column="Operationor" property="updaterId"/>
        <result column="SamplerAddress" property="samplerAddress"/>

        <result column="paraEnable" property="paraEnable"/>
        <result column="tempComputeMode" property="tempComputeMode"/>
        <result column="tempCoolAll" property="tempCoolAll"/>
        <result column="tempCoolStart" property="tempCoolStart"/>
        <result column="tempDiff" property="tempDiff"/>
        <result column="rollingCount" property="rollingCount"/>
        <result column="runPeriod" property="runPeriod"/>
        <result column="operationInterval" property="operationInterval"/>
        <result column="fanInstall" property="fanInstall"/>
        <result column="outInTempDiff" property="outInTempDiff"/>
        <result column="tempFanStart" property="tempFanStart"/>
        <result column="tempInFanStop" property="tempInFanStop"/>
        <result column="tempOutFanStop" property="tempOutFanStop"/>
        <result column="enableWarm" property="enableWarm"/>
        <result column="tempHotAll" property="tempHotAll"/>
        <result column="tempHotStart" property="tempHotStart"/>
        <result column="tempHot" property="tempHot"/>
        <result column="tempBottomLow" property="tempBottomLow"/>
        <result column="workTemp" property="workTemp"/>
        <result column="tempSetting" property="tempSetting"/>
        <result column="lastSuccussDeployTime" property="lastSuccussDeployTime"/>
        <result column="lastDeployState" property="lastDeployState"/>
        <result column="lastDeployTime" property="lastDeployTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="operationor" property="operationor"/>
        <result column="description" property="description"/>
        <result column="extendField" property="extendField"/>
        <collection property="airEquipList" ofType="com.siteweb.airconditioncontrol.dto.EquipSample" column="StationId, VirtualEquipmentId">
            <result column="equipStationId" property="stationId"/>
            <result column="EquipmentId" property="equipmentId"/>
            <result column="EquipmentName" property="equipmentName"/>
            <result column="EquipmentCategory" property="equipmentCategory"/>
            <result column="EquipmentType" property="equipmentType"/>
            <result column="EquipmentClass" property="equipmentClass"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="EquipmentBaseType" property="equipmentBaseType"/>
            <result column="HouseId" property="houseId"/>
            <result column="EquipMonitorUnitId" property="monitorUnitId"/>
            <result column="SamplerUnitId" property="samplerUnitId"/>
            <result column="Address" property="address"/>
            <result column="PortNo" property="portNo"/>
        </collection>
    </resultMap>

    <update id="updateEquipmentName">
        UPDATE Aircon_AutoControlPara SET GroupName = #{newGroupName}, VirtualEquipmentName = #{newAirMngEquipName}, Operationor = #{updateId}, UpdateTime = #{updateTime} WHERE VirtualEquipmentId = #{airMngEqupId}
    </update>
    <update id="updateSWEquipmentName">
        UPDATE TBL_Equipment SET EquipmentName = #{newEquipmentName} WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId}
    </update>
    <update id="changeEnableState">
        UPDATE Aircon_AutoControlPara SET ParaEnable = #{stateValue} WHERE StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId}
    </update>
    <update id="updateParams">
        UPDATE Aircon_AutoControlPara SET
            ParaEnable = #{paraEnable},
            TempCoolAll = #{tempCoolAll},
            TempCoolStart = #{tempCoolStart},
            TempDiff = #{tempDiff},
            RollingCount = #{rollingCount},
            RunPeriod = #{runPeriod},
            OperationInterval = #{operationInterval},
            FanInstall = #{fanInstall},
            OutInTempDiff = #{outInTempDiff},
            TempFanStart = #{tempFanStart},
            TempInFanStop = #{tempInFanStop},
            TempOutFanStop = #{tempOutFanStop},
            EnableWarm = #{enableWarm},
            TempHotAll = #{tempHotAll},
            TempHotStart = #{tempHotStart},
            UpdateTime = sysdate,
            Operationor = #{updaterId}
        WHERE StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId}
    </update>
    <update id="updateDeployStateSuccess">
        UPDATE Aircon_AutoControlPara SET LastSuccussDeployTime = #{curDate}, LastDeployState = 1, LastDeployTime = #{curDate}
        WHERE VirtualEquipmentId = #{airMngEquipId} AND StationId = #{stationId};
    </update>
    <update id="updateDeployStateFail">
        UPDATE Aircon_AutoControlPara SET LastDeployState = 0, LastDeployTime = #{curDate}
        WHERE VirtualEquipmentId = #{airMngEquipId} AND StationId = #{stationId};
    </update>
    <delete id="delEquipBy">
        DELETE FROM TBL_Equipment WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId}
    </delete>
    <delete id="delPortBy">
        DELETE FROM TSL_Port WHERE MonitorUnitId = #{monitorUnitId} AND PortId = #{portId}
    </delete>

    <select id="findMaxSamplerAddress" resultType="java.lang.Integer">
        SELECT MAX(SamplerAddress) FROM Aircon_AutoControlPara WHERE StationId = #{stationId} AND MonitorUnitId = #{monitorUnitId}
    </select>
    <select id="findMinHouseId" resultType="java.lang.Integer">
        SELECT MIN(HouseId) FROM TBL_House WHERE StationId = #{stationId}
    </select>
    <select id="findSamplerByDllPath" resultType="com.siteweb.monitoring.entity.Sampler">
        SELECT * FROM TSL_Sampler WHERE DLLPath = #{tempDLLName} OR DLLPath = 'ACLogCtrl.so' OR DLLPath = 'ACLogCtrl.SO'
    </select>
    <select id="findAirPortInfo" resultType="com.siteweb.monitoring.entity.Port">
        SELECT * FROM TSL_Port WHERE MonitorUnitId = #{monitorUnitId} AND Description = #{airDes}
    </select>
    <select id="findMaxPortNo" resultType="java.lang.Integer">
        SELECT MAX(PortNo) FROM TSL_Port WHERE MonitorUnitId = #{monitorUnitId}
    </select>
    <select id="findEqiupBy" resultType="com.siteweb.monitoring.entity.Equipment">
        SELECT * FROM TBL_Equipment WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId}
    </select>
    <select id="findSamplerUnitBy" resultType="com.siteweb.monitoring.entity.SamplerUnit">
        SELECT * FROM TSL_SamplerUnit WHERE SamplerUnitId = #{samplerUnitId} AND MonitorUnitId = #{monitorUnitId}
    </select>
    <select id="getSamplerUnitCountsOtherUsed" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM TSL_SamplerUnit WHERE MonitorUnitId = #{monitorUnitId} AND PortId = #{portId} AND Id != #{pkId}
    </select>
    <select id="getCountByGroupName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Aircon_AutoControlPara WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId}
            AND GroupName = #{airMngEquipName} <if test="excludeId != null"> AND VirtualEquipmentId != #{excludeId}</if>
    </select>
    <select id="findByMonitorUnitId" resultType="com.siteweb.airconditioncontrol.entity.AirAutoControlPara">
        SELECT * FROM Aircon_AutoControlPara WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId} ORDER BY SamplerAddress
    </select>

    <select id="findAirVirtualEquipInfoWithIds" resultMap="AirVirtualEquipIdInfoMap">
        SELECT acp.*, ecp.EquipmentId FROM Aircon_AutoControlPara acp LEFT JOIN Aircon_EquipmentControlPara ecp ON acp.VirtualEquipmentId = ecp.VirtualEquipmentId
        WHERE acp.VirtualEquipmentId = #{virtualEquipmentId}
    </select>

    <select id="findHasMapVirtualEquipInfo" resultMap="AirVirtualEquipInfoMap">
        SELECT acp.*, e.StationId equipStationId, e.EquipmentId, e.EquipmentName, e.EquipmentCategory, e.EquipmentType, e.EquipmentClass, e.EquipmentTemplateId,
                et.EquipmentBaseType, e.HouseId, e.MonitorUnitId EquipMonitorUnitId, e.SamplerUnitId,
                tsu.Address, tsp.PortNo
        FROM Aircon_AutoControlPara acp LEFT JOIN Aircon_EquipmentControlPara ecp ON acp.VirtualEquipmentId = ecp.VirtualEquipmentId
              LEFT JOIN TBL_Equipment e ON ecp.StationId = e.StationId AND ecp.EquipmentId = e.EquipmentId
              LEFT JOIN TBL_EquipmentTemplate et ON e.EquipmentTemplateId = et.EquipmentTemplateId
              LEFT JOIN TSL_SamplerUnit tsu ON e.MonitorUnitId = tsu.MonitorUnitId AND e.SamplerUnitId = tsu.SamplerUnitId
              LEFT JOIN TSL_Port tsp ON tsu.MonitorUnitId = tsp.MonitorUnitId AND tsu.PortId = tsp.PortId
        <if test="virtualEquipmentId != null"> WHERE acp.VirtualEquipmentId = #{virtualEquipmentId} </if>
    </select>
    <select id="findAirMngEquipTemplate" resultType="com.siteweb.monitoring.entity.EquipmentTemplate">
        SELECT * FROM tbl_EquipmentTemplate WHERE EquipmentTemplateName = #{manageEquipTemplateName} AND EquipmentCategory=45 <!-- AND ProtocolCode='AACCtrl6-00' -->
    </select>
    <select id="findSampleObjByIdList" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT EquipmentId, EquipmentName, MonitorUnitId FROM TBL_Equipment WHERE EquipmentId IN (${equipmentIds})
    </select>
    <select id="findAirGroupByMonitorUnitId" resultType="com.siteweb.airconditioncontrol.dto.AirGroup">
        SELECT VirtualEquipmentId, StationId, GroupName, SamplerAddress, MonitorUnitId, ParaEnable,
                LastSuccussDeployTime, LastDeployState, LastDeployTime
        FROM Aircon_AutoControlPara
        WHERE MonitorUnitId = #{monitorUnitId} AND StationId = #{stationId}
        ORDER BY SamplerAddress
    </select>
    <select id="findAirGroupParams" resultType="com.siteweb.airconditioncontrol.dto.AirGroupParams">
        SELECT StationId, MonitorUnitId, VirtualEquipmentId, ParaEnable,
                tempCoolAll, tempCoolStart, tempDiff, rollingCount, runPeriod, operationInterval, fanInstall,
                outInTempDiff, tempFanStart, tempInFanStop, tempOutFanStop, enableWarm, tempHotAll, tempHotStart
        FROM Aircon_AutoControlPara
        WHERE VirtualEquipmentId = #{virtualEquipmentId} AND StationId = #{stationId}
    </select>
    <select id="findReloadConfigControlId" resultType="java.lang.Integer">
        SELECT ControlId FROM TBL_Control
        WHERE EquipmentTemplateId IN (SELECT EquipmentTemplateId FROM TBL_Equipment WHERE EquipmentId = #{airMngEquipId} AND StationId = #{stationId})
              AND ControlName = #{reloadConfigControlName}
    </select>
    <select id="findLastSuccessDeployTime" resultType="java.util.Date">
        SELECT MAX(LastSuccussDeployTime) FROM Aircon_AutoControlPara WHERE MonitorUnitId = #{monitorUnitId}
    </select>
    <select id="findAllAirconComplexindex" resultType="com.siteweb.airconditioncontrol.dto.AirComplexindex">
        SELECT ci.ComplexIndexId, ci.ComplexIndexName, cid.ComplexIndexDefinitionName, rs.ResourceStructureName
        FROM
        (
        SELECT innerCi.* FROM ComplexIndex innerCi LEFT JOIN Aircon_ComplexindexMap innerAcm ON innerCi.ComplexIndexId = innerAcm.ComplexIndexId
            WHERE innerCi.BusinessTypeId = #{businessTypeId} AND innerCi.ComplexIndexDefinitionId
                IN (SELECT ComplexIndexDefinitionId FROM Energy_BusinessDefinitionMap
                        WHERE BusinessTypeId = #{businessTypeId} AND ComplexIndexDefinitionTypeId = #{complexIndexDefinitionTypeId})
                AND innerAcm.ComplexIndexId is null
        ) ci
        LEFT JOIN ComplexIndexDefinition cid ON ci.ComplexIndexDefinitionId = cid.ComplexIndexDefinitionId
        LEFT JOIN ResourceStructure rs ON ci.ObjectTypeId = rs.StructureTypeId AND ci.ObjectId = rs.ResourceStructureId
        ORDER BY ci.ComplexIndexId
    </select>
    <select id="findAirconComplexindex" resultType="com.siteweb.airconditioncontrol.dto.AirComplexindex">
        SELECT acm.MapId, ci.ComplexIndexId, ci.ComplexIndexName, cid.ComplexIndexDefinitionName, rs.ResourceStructureName
        FROM
        ( SELECT * FROM Aircon_ComplexindexMap WHERE
        StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId}
        ) acm
        LEFT JOIN ComplexIndex ci ON acm.ComplexIndexId = ci.ComplexIndexId
        LEFT JOIN ComplexIndexDefinition cid ON ci.ComplexIndexDefinitionId = cid.ComplexIndexDefinitionId
        LEFT JOIN ResourceStructure rs ON ci.ObjectTypeId = rs.StructureTypeId AND ci.ObjectId = rs.ResourceStructureId
        ORDER BY acm.UpdateTime DESC
    </select>
    <select id="findAirconComplexindexById" resultType="com.siteweb.airconditioncontrol.dto.AirComplexindex">
        SELECT ci.ComplexIndexId, ci.ComplexIndexName, cid.ComplexIndexDefinitionName, rs.ResourceStructureName
        FROM
        ( SELECT * FROM ComplexIndex WHERE ComplexIndexId = #{complexIndexId} ) ci
        LEFT JOIN ComplexIndexDefinition cid ON ci.ComplexIndexDefinitionId = cid.ComplexIndexDefinitionId
        LEFT JOIN ResourceStructure rs ON ci.ObjectTypeId = rs.StructureTypeId AND ci.ObjectId = rs.ResourceStructureId
        ORDER BY ci.ComplexIndexId
    </select>
    <delete id="delAirconComplexindexMapById">
        DELETE FROM Aircon_ComplexindexMap WHERE StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId}
    </delete>
    <delete id="delMapById">
        DELETE FROM Aircon_ComplexindexMap WHERE MapId = #{mapId}
    </delete>
    <delete id="delMapByVirtualEquipmentId">
        DELETE FROM Aircon_ComplexindexMap WHERE VirtualEquipmentId = #{virtualEquipmentId}
    </delete>
    <insert id="insertOneAirconComplexindexMap">
        INSERT INTO Aircon_ComplexindexMap (MapId, StationId, VirtualEquipmentId, ComplexIndexId, UpdateTime, UpdateId)
            VALUES(#{mapId}, #{stationId}, #{virtualEquipmentId}, #{complexIndexId}, #{curDate}, #{userId})
    </insert>
    <select id="findAirComplexindexMapById" resultType="com.siteweb.airconditioncontrol.entity.AirComplexindexMap">
        SELECT * FROM Aircon_ComplexindexMap WHERE MapId = #{mapId}
    </select>
</mapper>