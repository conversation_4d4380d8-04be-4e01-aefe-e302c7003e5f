<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirTemplateStdSignalMapMapper">

    <resultMap id="AirStdTypeMap" type="com.siteweb.airconditioncontrol.dto.AirStdTypeMap">
        <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="TypeId" property="typeId"/>
        <result column="TypeName" property="typeName"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="signalList" ofType="com.siteweb.airconditioncontrol.dto.AirStdSignalMap"
                    column="EquipmentTemplateId, TypeId">
            <!-- 标准信号的字段 -->
            <id column="SignalMapTypeId" property="typeId"/>
            <id column="StdSignalId" property="stdSignalId"/>
            <id column="StdSignalName" property="stdSignalName"/>
            <id column="StdSignalUnit" property="stdSignalUnit"/>
            <id column="StdSignalRemark" property="stdSignalRemark"/>
            <id column="StdSignalType" property="stdSignalType"/>
            <id column="NeedShow" property="needShow"/>
            <id column="CommandColor" property="commandColor"/>
            <id column="BusinessTypeId" property="businessTypeId"/>
            <id column="SignalTypeName" property="signalTypeName"/>
            <id column="MapRequirement" property="mapRequirement"/>
            <!-- 映射信号的字段 -->
            <result column="SignalMapEquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="defaultValue" property="defaultValue"/>
            <result column="swSignalId" property="swSignalId"/>
            <result column="swSignalName" property="swSignalName"/>
            <result column="swSignalChanelNum" property="swSignalChanelNum"/>
            <result column="swCmdToken" property="swCmdToken"/>
            <result column="swParam" property="swParam"/>
            <result column="swOperator" property="swOperator"/>
            <result column="swCmpValue" property="swCmpValue"/>
            <result column="extendField1" property="extendField1"/>
            <result column="extendField2" property="extendField2"/>
            <result column="extendField3" property="extendField3"/>
        </collection>
    </resultMap>

    <select id="findHasMapTemplate" resultMap="AirStdTypeMap">
        SELECT st.*,
            ass.TypeId SignalMapTypeId, ass.StdSignalId, ass.StdSignalName, ass.StdSignalUnit, ass.StdSignalRemark, ass.StdSignalType, ast.SignalTypeName, ass.NeedShow, ass.CommandColor, ass.BusinessTypeId,
            atss.EquipmentTemplateId SignalMapEquipmentTemplateId, atss.defaultValue, atss.swSignalId, atss.swSignalName, atss.swSignalChanelNum, atss.swCmdToken, atss.swParam, atss.swOperator, atss.swCmpValue, atss.extendField1, atss.extendField2, atss.extendField3
        FROM
            (SELECT EquipmentTemplateId,TypeId,TypeName FROM Aircon_TemplateStdType WHERE TypeId IS NOT NULL <if test="equipmentTemplateId != null"> AND EquipmentTemplateId=#{equipmentTemplateId} </if>) st
            LEFT JOIN Aircon_TemplateStdSignalMap atss ON st.EquipmentTemplateId = atss.EquipmentTemplateId AND st.TypeId = atss.TypeId
            LEFT JOIN Aircon_StdSignal ass ON st.TypeId = ass.TypeId AND atss.StdSignalId = ass.StdSignalId
            LEFT JOIN Aircon_StdSignalType ast ON ass.StdSignalType = ast.SignalTypeId
    </select>

    <select id="findStdMapSignalsByTemplateId" resultType="com.siteweb.airconditioncontrol.dto.AirStdSignalMap">
        SELECT ass.*, ast.SignalTypeName, atss.EquipmentTemplateId,
            atss.DefaultValue, atss.SwSignalId, atss.SwSignalName,
            atss.SwSignalChanelNum, atss.SwCmdToken, atss.SwParam,
            atss.SwOperator, atss.SwCmpValue
        FROM (select * from Aircon_TemplateStdType WHERE EquipmentTemplateId = #{equipmentTemplateId}) aet
            INNER JOIN Aircon_StdSignal ass ON aet.TypeId = ass.TypeId
            LEFT JOIN Aircon_StdSignalType ast ON ass.StdSignalType = ast.SignalTypeId
            LEFT JOIN Aircon_TemplateStdSignalMap atss ON aet.EquipmentTemplateId = atss.EquipmentTemplateId AND ass.TypeId = atss.TypeId AND ass.StdSignalId = atss.StdSignalId
        WHERE ass.StdSignalType != 5 AND ass.NeedShow = 1
        ORDER BY ass.MapRequirement DESC, ass.StdSignalType
    </select>

    <select id="findStdMapSignalsByTypeId" resultType="com.siteweb.airconditioncontrol.dto.AirStdSignalMap">
        SELECT ass.*, ast.SignalTypeName, ${equipmentTemplateId} EquipmentTemplateId,
            atss.DefaultValue, atss.SwSignalId, atss.SwSignalName,
            atss.SwSignalChanelNum, atss.SwCmdToken, atss.SwParam,
            atss.SwOperator, atss.SwCmpValue
        FROM (SELECT * FROM Aircon_StdSignal WHERE TypeId = #{typeId}) ass
          LEFT JOIN Aircon_StdSignalType ast ON ass.StdSignalType = ast.SignalTypeId
          LEFT JOIN Aircon_TemplateStdSignalMap atss ON atss.EquipmentTemplateId = ${equipmentTemplateId} AND atss.TypeId = #{typeId} AND ass.StdSignalId = atss.StdSignalId
        WHERE ass.StdSignalType != 5 AND ass.NeedShow = 1
        ORDER BY ass.MapRequirement DESC, ass.StdSignalType
    </select>
    <select id="findCountByPk" resultType="java.lang.Integer">
        SELECT Count(*) FROM Aircon_TemplateStdSignalMap WHERE EquipmentTemplateId = #{equipmentTemplateId} AND TypeId = #{typeId} AND StdSignalId = #{stdSignalId}
    </select>

    <delete id="deleteByEquipmentTemplateId">
        DELETE FROM Aircon_TemplateStdSignalMap WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </delete>
    <delete id="deleteByPk">
        DELETE FROM Aircon_TemplateStdSignalMap WHERE EquipmentTemplateId = #{equipmentTemplateId} AND TypeId = #{typeId} AND StdSignalId = #{stdSignalId}
    </delete>
    <delete id="deleteByEquipmentTemplateIdAndTypeId">
        DELETE FROM Aircon_TemplateStdSignalMap WHERE EquipmentTemplateId = #{equipmentTemplateId} AND TypeId = #{typeId}
    </delete>

    <update id="updateByPk">
        UPDATE Aircon_TemplateStdSignalMap
        SET DefaultValue=#{defaultValue},SwSignalId=#{swSignalId},SwSignalName=#{swSignalName},
            SwSignalChanelNum=#{swSignalChanelNum},SwCmdToken=#{swCmdToken},SwParam=#{swParam},
            SwOperator=#{swOperator},SwCmpValue=#{swCmpValue},UpdateDate=#{updateDate},UpdaterId=#{updaterId},
            UpdaterName=#{updaterName},ExtendField1=#{extendField1},ExtendField2=#{extendField2},ExtendField3=#{extendField3}
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND TypeId = #{typeId} AND StdSignalId = #{stdSignalId}
    </update>
</mapper>