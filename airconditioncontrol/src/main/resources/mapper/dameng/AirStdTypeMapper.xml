<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirStdTypeMapper">

    <select id="getAll" resultType="com.siteweb.airconditioncontrol.entity.AirStdType">
        select * from Aircon_StdType
    </select>
    <select id="getAllAirType" resultType="com.siteweb.airconditioncontrol.entity.AirStdType">
        select * from Aircon_StdType WHERE TypeId IN (1,2,3)
    </select>
    <select id="getTemperatureType" resultType="com.siteweb.airconditioncontrol.entity.AirStdType">
        select * from Aircon_StdType WHERE TypeId IN (4)
    </select>
    <select id="getFanType" resultType="com.siteweb.airconditioncontrol.entity.AirStdType">
        select * from Aircon_StdType WHERE TypeId IN (5)
    </select>
</mapper>