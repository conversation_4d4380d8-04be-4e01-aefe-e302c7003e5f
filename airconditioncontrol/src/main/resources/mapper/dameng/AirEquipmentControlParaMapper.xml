<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirEquipmentControlParaMapper">


    <resultMap id="EquipSampleGroupByHouseIdMap" type="com.siteweb.airconditioncontrol.model.EquipSampleGroupByHouse">
        <result column="StationIdGroupBy" property="stationId"/>
        <result column="MonitorUnitIdGroupBy" property="monitorUnitId"/>
        <result column="HouseIdGroupBy" property="houseId"/>
        <collection property="equipList" ofType="com.siteweb.airconditioncontrol.dto.EquipSample"
                    column="StationIdGroupBy, MonitorUnitIdGroupBy, HouseIdGroupBy">
            <result column="StationId" property="stationId"/>
            <result column="EquipmentId" property="equipmentId"/>
            <result column="EquipmentName" property="equipmentName"/>
            <result column="EquipmentCategory" property="equipmentCategory"/>
            <result column="EquipmentType" property="equipmentType"/>
            <result column="EquipmentClass" property="equipmentClass"/>
            <result column="EquipmentTemplateId" property="equipmentTemplateId"/>
            <result column="EquipmentBaseType" property="equipmentBaseType"/>
            <result column="HouseId" property="houseId"/>
            <result column="MonitorUnitId" property="monitorUnitId"/>
            <result column="SamplerUnitId" property="samplerUnitId"/>
        </collection>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO Aircon_EquipmentControlPara(VirtualEquipmentId, EquipmentId, StationId, MonitorUnitId, InsertTime, Operationor, ExtendField) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.virtualEquipmentId},#{item.equipmentId},#{item.stationId},#{item.monitorUnitId},#{item.insertTime},#{item.operationor},#{item.extendField})
        </foreach>
    </insert>
    <delete id="deleteByVirEquipmentId">
        DELETE FROM Aircon_EquipmentControlPara WHERE VirtualEquipmentId = #{virtualEquipmentId}
    </delete>

    <select id="findAirEquipments" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT equip.StationId, equip.EquipmentId, equip.EquipmentName, equip.EquipmentCategory, equip.HouseId, equip.MonitorUnitId,
                equip.SamplerUnitId, equip.EquipmentType, equip.EquipmentClass, equip.EquipmentTemplateId, et.EquipmentBaseType
        FROM
        (SELECT EquipmentTemplateId, EquipmentTemplateName,EquipmentBaseType FROM TBL_EquipmentTemplate WHERE EquipmentBaseType IN
                (SELECT BaseEquipmentId FROM TBL_EquipmentBaseType WHERE EquipmentTypeId = 7) AND EquipmentTemplateId NOT IN (${airMngEquipTemplateIds})) et
          INNER JOIN TBL_Equipment equip ON equip.StationId = #{stationId} AND equip.MonitorUnitId = #{monitorUnitId} AND et.EquipmentTemplateId = equip.EquipmentTemplateId
        ORDER BY equip.EquipmentId
    </select>
    <select id="findAirEquipmentsSelected" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT equip.StationId, equip.EquipmentId, equip.EquipmentName, equip.EquipmentCategory, equip.HouseId, equip.MonitorUnitId,
                equip.SamplerUnitId, equip.EquipmentType, equip.EquipmentClass, equip.EquipmentTemplateId, et.EquipmentBaseType
        FROM
        (SELECT EquipmentId FROM Aircon_EquipmentControlPara WHERE StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId}) ecp
            INNER JOIN TBL_Equipment equip ON ecp.EquipmentId = equip.EquipmentId
            LEFT JOIN TBL_EquipmentTemplate et ON equip.EquipmentTemplateId = et.EquipmentTemplateId
        ORDER BY equip.EquipmentId
    </select>
    <select id="findAirEquipmentsNotSelected" resultType="com.siteweb.airconditioncontrol.dto.EquipSample">
        SELECT equip.StationId, equip.EquipmentId, equip.EquipmentName, equip.EquipmentCategory, equip.HouseId, equip.MonitorUnitId,
                equip.SamplerUnitId, equip.EquipmentType, equip.EquipmentClass, equip.EquipmentTemplateId, et.EquipmentBaseType
        FROM
        (SELECT EquipmentTemplateId, EquipmentTemplateName,EquipmentBaseType FROM TBL_EquipmentTemplate WHERE EquipmentBaseType IN
                (SELECT BaseEquipmentId FROM TBL_EquipmentBaseType WHERE EquipmentTypeId = 7) AND EquipmentTemplateId NOT IN (${airMngEquipTemplateIds})) et
          INNER JOIN TBL_Equipment equip ON equip.StationId = #{stationId} AND equip.MonitorUnitId = #{monitorUnitId} AND et.EquipmentTemplateId = equip.EquipmentTemplateId
          LEFT JOIN Aircon_EquipmentControlPara ecp ON equip.EquipmentId = ecp.EquipmentId
        WHERE ecp.EquipmentId is null
        ORDER BY equip.EquipmentId
    </select>

    <select id="findCompleteMapTemplateIds" resultType="java.lang.Integer">
        SELECT tbl_result.EquipmentTemplateId
        FROM
        (
            SELECT tbl.EquipmentTemplateId, SUM(tbl.UnMapFlag) UnMapCount
            FROM
            (
                SELECT et.EquipmentTemplateId, ts.TypeId, ts.TypeName, ss.StdSignalId, ss.StdSignalName, ecp.SwSignalId,
                        CASE WHEN ecp.SwSignalId IS NULL THEN 1 ELSE 0 END UnMapFlag
                FROM
                    (SELECT EquipmentTemplateId, EquipmentTemplateName FROM TBL_EquipmentTemplate WHERE EquipmentTemplateId IN (${needEquipTemplateIds})) et
                    LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
                    LEFT JOIN Aircon_StdSignal ss ON ts.TypeId = ss.TypeId AND ss.MapRequirement = 2
                    LEFT JOIN Aircon_TemplateStdSignalMap ecp ON et.EquipmentTemplateId = ecp.EquipmentTemplateId AND ss.TypeId = ecp.TypeId AND ss.StdSignalId = ecp.StdSignalId
            ) tbl
            GROUP BY tbl.EquipmentTemplateId
            HAVING SUM(tbl.UnMapFlag) = 0
        ) tbl_result;
    </select>
    <select id="findAirEquipmentsNotSelectedGroupByHouse" resultMap="EquipSampleGroupByHouseIdMap">
        SELECT equip.StationId StationIdGroupBy, equip.MonitorUnitId MonitorUnitIdGroupBy, equip.HouseId HouseIdGroupBy,
            equip.StationId, equip.EquipmentId, equip.EquipmentName, equip.EquipmentCategory, equip.HouseId, equip.MonitorUnitId,
            equip.SamplerUnitId, equip.EquipmentType, equip.EquipmentClass, equip.EquipmentTemplateId, et.EquipmentBaseType
        FROM
            (SELECT EquipmentTemplateId, EquipmentTemplateName,EquipmentBaseType FROM TBL_EquipmentTemplate WHERE EquipmentBaseType IN
                (SELECT BaseEquipmentId FROM TBL_EquipmentBaseType WHERE EquipmentTypeId = 7) AND EquipmentTemplateId NOT IN (${airMngEquipTemplateIds})) et
            INNER JOIN TBL_Equipment equip ON equip.StationId = #{stationId} AND equip.MonitorUnitId = #{monitorUnitId} AND et.EquipmentTemplateId = equip.EquipmentTemplateId
            LEFT JOIN Aircon_EquipmentControlPara ecp ON equip.EquipmentId = ecp.EquipmentId
        WHERE ecp.EquipmentId is null
        ORDER BY equip.EquipmentId
    </select>

    <select id="findHasBeUsedEquipment" resultType="java.lang.Integer">
        SELECT EquipmentId FROM Aircon_EquipmentControlPara WHERE EquipmentId IN (${equipmentIds}) <if test="virtualEquipmentId != null"> AND VirtualEquipmentId != #{virtualEquipmentId} </if>
    </select>
    <select id="findUnCompleteMapTemplateIds" resultType="java.lang.Integer">
        SELECT tbl_result.EquipmentTemplateId
        FROM
        (
            SELECT tbl.EquipmentTemplateId, SUM(tbl.UnMapFlag) UnMapCount
            FROM
            (
                SELECT et.EquipmentTemplateId, ts.TypeId, ts.TypeName, ss.StdSignalId, ss.StdSignalName, ecp.SwSignalId,
                        CASE WHEN ecp.SwSignalId IS NULL THEN 1 ELSE 0 END UnMapFlag
                FROM
                    (SELECT EquipmentTemplateId, EquipmentTemplateName FROM TBL_EquipmentTemplate WHERE EquipmentTemplateId IN (${needEquipTemplateIds})) et
                    LEFT JOIN Aircon_TemplateStdType ts ON et.EquipmentTemplateId = ts.EquipmentTemplateId
                    LEFT JOIN Aircon_StdSignal ss ON ts.TypeId = ss.TypeId AND ss.MapRequirement = 2
                    LEFT JOIN Aircon_TemplateStdSignalMap ecp ON et.EquipmentTemplateId = ecp.EquipmentTemplateId AND ss.TypeId = ecp.TypeId AND ss.StdSignalId = ecp.StdSignalId
            ) tbl
            GROUP BY tbl.EquipmentTemplateId
            HAVING SUM(tbl.UnMapFlag) > 0
        ) tbl_result;
    </select>

</mapper>