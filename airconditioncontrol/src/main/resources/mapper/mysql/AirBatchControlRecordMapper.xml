<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirBatchControlRecordMapper">

    <insert id="batchInsert">
        INSERT INTO Aircon_BatchControlRecord(StationId, EquipmentId, ControlId, StdControlId, StdWorkModeFlag, AirStdTypeId,
                        AirCommon2No, SerialNo, Uuid, InsertTime, ExtendField) VALUES
        <foreach collection="recordList" item="item" separator=",">
            (#{item.stationId},#{item.equipmentId},#{item.controlId},#{item.stdControlId},#{item.stdWorkModeFlag},#{item.airStdTypeId},
            #{item.airCommon2No},#{item.serialNo},#{item.uuid},#{item.insertTime},#{item.extendField})
        </foreach>
    </insert>
    <select id="calchControlExecResultByGroupId" resultType="com.siteweb.airconditioncontrol.dto.BatchControlResultSum">
      SELECT COUNT(tbl.SerialNo) TotalSum,SUM(tbl.SuccessFlag) SuccessSum,SUM(tbl.FailFlag) FailSum,SUM(tbl.SendingFlag) SendingSum
      FROM (
        SELECT CASE WHEN tac.ControlResultType = 1 THEN 1 ELSE 0 END SuccessFlag,
                CASE WHEN tac.ControlResultType IN (2,3,5,6,7) THEN 1 ELSE 0 END FailFlag,
                CASE WHEN tac.ControlResultType = 4 THEN 1 ELSE 0 END SendingFlag,
                tac.SerialNo
        FROM Aircon_BatchControlEquipmentMap map
            INNER JOIN Aircon_BatchControlRecord bcr ON map.StationId = bcr.StationId AND map.EquipmentId = bcr.EquipmentId
            INNER JOIN TBL_ActiveControl tac ON bcr.SerialNo = tac.SerialNo
        WHERE map.GroupId = #{groupId}
      ) tbl
    </select>

</mapper>