<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirZoneControlOperationMapper">

    <resultMap id="AirZoneOperationInfoMap" type="com.siteweb.airconditioncontrol.model.AirZoneOperationInfo">
        <result column="SchemeId" property="schemeId"/>
        <collection property="operationLists" ofType="com.siteweb.airconditioncontrol.entity.AirZoneControlOperation"
                    column="SchemeId">

            <result column="OperationId" property="operationId"/>
            <result column="BelongSchemeId" property="schemeId"/>

            <result column="OperationTime" property="operationTime"/>
            <result column="OperationCmdId" property="operationCmdId"/>
            <result column="OperationCmdName" property="operationCmdName"/>
            <result column="Params" property="params"/>
            <result column="UpdateTime" property="updateTime"/>
            <result column="UpdateId" property="updateId"/>
            <result column="ExtendField" property="extendField"/>
        </collection>
    </resultMap>

    <update id="updateBySchemeId">
        UPDATE Aircon_ZoneControlOperation
            SET OperationTime = #{operationTime}, OperationCmdId = #{operationCmdId}, OperationCmdName = #{operationCmdName}
                , Params = #{params}, UpdateId = #{updateId}, UpdateTime = #{updateTime}
          WHERE SchemeId = #{schemeId} AND OperationId = #{operationId}
    </update>
    <delete id="delBySchemeId">
        DELETE FROM Aircon_ZoneControlOperation WHERE SchemeId = #{id}
    </delete>
    <delete id="delById">
        DELETE FROM Aircon_ZoneControlOperation WHERE OperationId = #{operationId} AND SchemeId = #{schemeId}
    </delete>
    <delete id="delByVirtualEquipmentId">
        DELETE FROM Aircon_ZoneControlOperation WHERE SchemeId IN
        (SELECT SchemeId FROM Aircon_ZoneControlScheme WHERE VirtualEquipmentId = #{virtualEquipmentId})
    </delete>

    <select id="findBySchemeId" resultType="com.siteweb.airconditioncontrol.dto.ZoneOperation">
        SELECT * FROM Aircon_ZoneControlOperation WHERE SchemeId = #{id} ORDER BY OperationId
    </select>

    <select id="findZoneOperationInfo"  resultMap="AirZoneOperationInfoMap">
        SELECT t.*, t.schemeId BelongSchemeId FROM Aircon_ZoneControlOperation t
        <if test="schemeId != null"> WHERE t.SchemeId = #{schemeId} </if>
    </select>
    <select id="countOfExistOperation" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Aircon_ZoneControlOperation WHERE SchemeId = #{schemeId}
    </select>
</mapper>