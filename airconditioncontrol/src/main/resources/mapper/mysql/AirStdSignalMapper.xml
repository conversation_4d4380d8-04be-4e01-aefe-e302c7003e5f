<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirStdSignalMapper">

    <select id="getAllValidSignal" resultType="com.siteweb.airconditioncontrol.entity.AirStdSignal">
        SELECT s.*, t.SignalTypeName from Aircon_StdSignal s LEFT JOIN Aircon_StdSignalType t ON s.StdSignalType = t.SignalTypeId
            WHERE s.StdSignalType != 5 AND s.NeedShow = 1
    </select>
    <select id="getSignalByType" resultType="com.siteweb.airconditioncontrol.entity.AirStdSignal">
        SELECT s.*, t.SignalTypeName from Aircon_StdSignal s LEFT JOIN Aircon_StdSignalType t ON s.StdSignalType = t.SignalTypeId
            WHERE s.TypeId = #{typeId} AND s.StdSignalType != 5 AND s.NeedShow = 1
    </select>
</mapper>