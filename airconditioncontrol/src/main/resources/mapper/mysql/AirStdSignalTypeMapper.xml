<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirStdSignalTypeMapper">

    <select id="getValidType" resultType="com.siteweb.airconditioncontrol.entity.AirStdSignalType">
        select * from Aircon_StdSignalType WHERE SignalTypeId IN (1,2,3,4)
    </select>
</mapper>