package com.siteweb.license.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RequestLicenseDto {
    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String clientName;
    /**
     * 合同编号
     */
    @NotBlank(message = "合同编号不能为空")
    private String contractNo;
    /**
     * 产品编码Id
     */
    private Integer productId = 1;
    /**
     * 产品编码
     */
    private String product = "SiteWeb基础设施管理系统 V6.0";
    /**
     * 加密唯一信息
     */
    private String uniqueInfo;
}
