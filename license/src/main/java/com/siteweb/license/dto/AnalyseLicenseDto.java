package com.siteweb.license.dto;

import com.siteweb.license.entity.LicenseFeature;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/06/24
 */
@Data
public class AnalyseLicenseDto {
    /**
     * 许可类型 1试用版 2正式版
     */
    private Integer licenseType;
    /**
     * 加密唯一信息
     */
    private String uniqueInfo;
    /**
     * 产品名称
     */
    private String product;
    /**
     * 过期时间
     */
    private Date limitTime;
    /**
     * 授权模块信息集合
     */
    private List<LicenseFeature> features;
}
