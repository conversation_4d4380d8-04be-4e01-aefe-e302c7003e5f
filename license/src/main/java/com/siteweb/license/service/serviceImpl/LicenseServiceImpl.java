package com.siteweb.license.service.serviceImpl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.oshi.OshiUtil;
import com.siteweb.common.exception.LicenseException;
import com.siteweb.common.util.AESUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.license.dto.AnalyseLicenseDto;
import com.siteweb.license.dto.RequestLicenseDto;
import com.siteweb.license.entity.License;
import com.siteweb.license.entity.LicenseFeature;
import com.siteweb.license.enums.LicenseState;
import com.siteweb.license.mapper.LicenseMapper;
import com.siteweb.license.service.LicenseFeatureService;
import com.siteweb.license.service.LicenseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import oshi.util.Constants;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class LicenseServiceImpl implements LicenseService {
    /**
     * 试用版license
     */
    private static final int TRIAL_LICENSE = 1;

    /**
     * 正式版license
     */
    private static final int OFFICIAL_LICENSE = 2;
    private static final String NOT = "Not";
    @Autowired
    private LicenseMapper licenseMapper;
    @Autowired
    private LicenseFeatureService licenseFeatureService;
    @Autowired
    private AESUtil aesUtil;
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Value("${aes-key}")
    private String aesKey;

    @Override
    public License findLicense() {
        License license = licenseMapper.findLicense();
        List<LicenseFeature> licenseFeatureList = licenseFeatureService.findAll();
        license.setLicenseFeatures(licenseFeatureList);
        return license;
    }

    @Override
    public String requestLicense(RequestLicenseDto requestLicenseDto) {
        String uniqueInfo = getUniqueInfo();
        requestLicenseDto.setUniqueInfo(uniqueInfo);
        return aesUtil.encrypt(JSONUtil.toJsonStr(requestLicenseDto), aesKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analyseLicense(String decryptLicenseCode) {
        JSONObject jsonObject = JSONUtil.parseObj(aesUtil.decrypt(decryptLicenseCode, aesKey), new JSONConfig().setIgnoreCase(true));
        AnalyseLicenseDto analyseLicenseDto = JSONUtil.toBean(jsonObject, AnalyseLicenseDto.class);
        //激活码有误 正式版license才校验系统uuid
        if (Objects.equals(analyseLicenseDto.getLicenseType(), OFFICIAL_LICENSE) && !this.compareSystemUUID(analyseLicenseDto.getUniqueInfo())) {
            log.error("importLicenseError:System UUID ERROR,{}", analyseLicenseDto.getUniqueInfo());
            throw new LicenseException(localeMessageSourceUtil.getMessage("license.message.licenseerror"));
        }
        //激活模块
        licenseFeatureService.activeFeature(analyseLicenseDto.getFeatures());
        //激活-激活码
        this.activeLicense(analyseLicenseDto);
    }

    @Override
    public LicenseState checkLicense() {
        License license = this.findLicense();
        //试用版
        if (Objects.equals(license.getLicenseType(),TRIAL_LICENSE)) {
            return LicenseState.TRIAL_VERSION;
        }
        //已经到期
        Date nowDate = new Date();
        if (nowDate.after(license.getLimitTime())) {
            return LicenseState.ALREADY_EXPIRING;
        }
        //还有七天快过期
        DateTime dateTime = DateUtil.offsetDay(license.getLimitTime(), -7);
        if (dateTime.before(new Date())) {
            return LicenseState.PREPARE_EXPIRING;
        }
        //license异常
        if (CharSequenceUtil.isNotBlank(license.getUniqueInfo()) && !this.compareSystemUUID(license.getUniqueInfo())) {
            return LicenseState.LICENSE_EXCEPTION;
        }
        //正常
        return LicenseState.NORMAL;
    }

    /**
     * 激活授权码
     *
     * @param analyseLicenseDto license的激活信息
     */
    private void activeLicense(AnalyseLicenseDto analyseLicenseDto) {
        License license = this.findLicense();
        //已经包含该机器的唯一id
        if (CharSequenceUtil.isNotBlank(license.getUniqueInfo()) && license.getUniqueInfo().contains(analyseLicenseDto.getUniqueInfo())) {
            return;
        }
        license.setActiveTime(new Date());
        license.setLicenseType(analyseLicenseDto.getLicenseType());
        license.setLimitTime(analyseLicenseDto.getLimitTime());
        license.setProduct(analyseLicenseDto.getProduct());
        //正式版才需要系统uuid
        if (Objects.equals(analyseLicenseDto.getLicenseType(), OFFICIAL_LICENSE) && CharSequenceUtil.isBlank(license.getUniqueInfo())) {
            license.setUniqueInfo(analyseLicenseDto.getUniqueInfo());
            licenseMapper.updateById(license);
            return;
        }
        //双机热备情下将两台机器的唯一id用逗号分割都存入其中
        license.setUniqueInfo(license.getUniqueInfo() + "," + analyseLicenseDto.getUniqueInfo());
        licenseMapper.updateById(license);
    }

    /**
     * 加密后的uuid是否与系统的一致
     * @param encryptUUID 加厚后的uuid
     * @return boolean true 一致  false 不一致
     */
    private boolean compareSystemUUID(String encryptUUID){
        if(CharSequenceUtil.isBlank(encryptUUID)){
            return false;
        }
        String[] encryptUUIDSplit = encryptUUID.split(",");
        for (String decryptStr : encryptUUIDSplit) {
            String uuid = aesUtil.decrypt(decryptStr, aesKey);
            if (this.getHardwareUUID().equalsIgnoreCase(uuid) || CharSequenceUtil.equalsIgnoreCase(uuid, Constants.UNKNOWN) || CharSequenceUtil.containsIgnoreCase(uuid,NOT)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 获取唯一编码
     *
     * @return {@link String}
     */
    private String getUniqueInfo() {
        String uuid = this.getHardwareUUID();
        return aesUtil.encrypt(uuid, aesKey);
    }

    private String getHardwareUUID() {
        //获取系统的uuid
        //linux命令: dmidecode -s system-uuid
        //windeows命令: wmic csproduct get uuid
        return OshiUtil.getSystem().getHardwareUUID();
    }
}
