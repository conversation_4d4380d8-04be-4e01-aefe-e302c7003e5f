package com.siteweb.license.service;

import com.siteweb.license.dto.RequestLicenseDto;
import com.siteweb.license.entity.License;
import com.siteweb.license.enums.LicenseState;

public interface LicenseService {
    /**
     * 获取授权码
     * @return {@link License}
     */
    License findLicense();

    /**
     * 申请授权码
     * @return {@link String}
     */
    String requestLicense(RequestLicenseDto requestLicenseDto);

    /**
     * 解析激活码并激活
     * @param decryptLicenseCode 加密的授权码
     */
    void analyseLicense(String decryptLicenseCode);

    /**
     * 检查license的当前状态:比如 快到期 已到期 license不正确.......
     */
    LicenseState checkLicense();
}
