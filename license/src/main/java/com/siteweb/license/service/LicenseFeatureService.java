package com.siteweb.license.service;

import com.siteweb.license.dto.NoActivePathDto;
import com.siteweb.license.entity.LicenseFeature;

import java.util.List;

public interface LicenseFeatureService {
    List<LicenseFeature> findAll();

    /**
     * 激活模块
     * @param features 模块信息
     */
    void activeFeature(List<LicenseFeature> features);

    /**
     * 获取未激活模块的前端路由path
     *
     * @return {@link List}<{@link NoActivePathDto}>
     */
    List<NoActivePathDto> findNoActivePath();

    /**
     * 获取模块的扩展信息
     * @param id 模块id
     * @return {@link String}
     */
    String findFeatureData(Integer id);
}
