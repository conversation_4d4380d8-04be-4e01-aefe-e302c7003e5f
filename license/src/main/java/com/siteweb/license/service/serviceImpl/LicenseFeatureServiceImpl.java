package com.siteweb.license.service.serviceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.MenuItem;
import com.siteweb.admin.mapper.MenuItemMapper;
import com.siteweb.license.dto.NoActivePathDto;
import com.siteweb.license.entity.LicenseFeature;
import com.siteweb.license.mapper.LicenseFeatureMapper;
import com.siteweb.license.service.LicenseFeatureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LicenseFeatureServiceImpl implements LicenseFeatureService {
    private static final String ENTRY_PATH = "entry/";
    @Autowired
    LicenseFeatureMapper licenseFeatureMapper;
    @Autowired
    MenuItemMapper menuItemMapper;
    @Override
    public List<LicenseFeature> findAll() {
        return licenseFeatureMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public void activeFeature(List<LicenseFeature> features) {
        this.closeAllFeature();
        //激活模块
        if (CollUtil.isEmpty(features)) {
            return;
        }
        licenseFeatureMapper.activeFeature(features);
    }

    @Override
    public List<NoActivePathDto> findNoActivePath() {
        List<NoActivePathDto> noActivePath = licenseFeatureMapper.findNoActivePath();
        //没有未激活的模块
        if (CollUtil.isEmpty(noActivePath)) {
            return Collections.emptyList();
        }
        Map<Integer, MenuItem> menuItemMap = menuItemMapper.selectList(Wrappers.emptyWrapper())
                                                           .stream()
                                                           .collect(Collectors.toMap(MenuItem::getMenuItemId, Function.identity()));
        for (NoActivePathDto noActivePathDto : noActivePath) {
            String menuPath = getMenuPath(menuItemMap, noActivePathDto.getMenuItemId());
            noActivePathDto.setPath(menuPath);
        }
        noActivePath.removeIf(item -> CharSequenceUtil.isBlank(item.getPath()) || ENTRY_PATH.equals(item.getPath()));
        return noActivePath;
    }

    /**
     * 获取菜单的全路径
     * @param menuItemMap 所有的从菜单项
     * @param itemId 需要获取的菜单全路径的itemId
     * @return {@link String}
     */
    private String getMenuPath(Map<Integer, MenuItem> menuItemMap, Integer itemId) {
        MenuItem menuItem = menuItemMap.get(itemId);
        if (Objects.isNull(menuItem)) {
            return "";
        }
        if (Objects.isNull(menuItem.getParentId()) || menuItem.getParentId() == 0) {
            return menuItem.getPath();
        }
        StringBuilder path = new StringBuilder(menuItem.getPath());
        while (menuItem.getParentId() > 0) {
            menuItem = menuItemMap.get(menuItem.getParentId());
            if (CharSequenceUtil.isNotBlank(menuItem.getPath())) {
                path.insert(0, '/').insert(0, menuItem.getPath());
            }
        }
        return path.toString();
    }

    @Override
    public String findFeatureData(Integer id) {
        LicenseFeature licenseFeature = licenseFeatureMapper.selectById(id);
        return licenseFeature.getData();
    }



    /**
     * 关闭所有模块
     */
    private void closeAllFeature() {
        LambdaUpdateWrapper<LicenseFeature> updateWrapper = Wrappers.<LicenseFeature>lambdaUpdate()
                                                                    .set(LicenseFeature::getIsActive, 0);
        licenseFeatureMapper.update(null, updateWrapper);
    }
}
