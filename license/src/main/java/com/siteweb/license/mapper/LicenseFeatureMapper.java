package com.siteweb.license.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.license.dto.NoActivePathDto;
import com.siteweb.license.entity.LicenseFeature;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LicenseFeatureMapper extends BaseMapper<LicenseFeature> {
    void activeFeature(@Param("list") List<LicenseFeature> features);

    List<NoActivePathDto> findNoActivePath();
}
