package com.siteweb.license.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * license的当前状态，包括正常、快到期、已到期、license异常等
 *
 * <AUTHOR>
 * @date 2022/07/06
 */
public enum LicenseState {
    NORMAL(1, "正常"),
    TRIAL_VERSION(2, "试用版"),
    PREPARE_EXPIRING(3, "快到期"),
    ALREADY_EXPIRING(4, "已到期"),
    LICENSE_EXCEPTION(5, "license异常");

    LicenseState(Integer state, String describe) {
        this.state = state;
        this.describe = describe;
    }

    /**
     * 状态
     */
    @JsonValue
    private Integer state;
    /**
     * 描述
     */
    private String describe;
}
