package com.siteweb.license.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.FileUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.license.dto.RequestLicenseDto;
import com.siteweb.license.service.LicenseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * license管理
 * <AUTHOR>
 * @date 2022/06/06
 */
@RestController
@RequestMapping("/api")
@Api(value = "license接口管理",tags = "license接口管理")
public class LicenseController {
    public static final String V_2_C = "v2c";
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    private LicenseService licenseService;
    @ApiOperation("获取授权码信息")
    @GetMapping("/license")
    public ResponseEntity<ResponseResult> getLicense(){
        return ResponseHelper.successful(licenseService.findLicense());
    }

    @ApiOperation("生成申请码信息")
    @PostMapping("/requestlicense")
    public void getRequestLicense(HttpServletResponse response, @Validated @RequestBody RequestLicenseDto requestLicenseDto){
        String encoder = licenseService.requestLicense(requestLicenseDto);
        FileUtil.exportTxt(response,encoder,"requestlicense.C2V");
    }

    @ApiOperation("解析激活码信息")
    @PostMapping("/analyselicense")
    public ResponseEntity<ResponseResult> getAnalyseLicense(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), localeMessageSourceUtil.getMessage("license.message.fileEmpty"), HttpStatus.OK);
        }
        String extName = cn.hutool.core.io.FileUtil.extName(file.getOriginalFilename());
        if (!V_2_C.equalsIgnoreCase(extName)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), localeMessageSourceUtil.getMessage("license.message.typeError"), HttpStatus.OK);
        }
        byte[] bytes = file.getInputStream().readAllBytes();
        String license = new String(bytes);
        licenseService.analyseLicense(license);
        return ResponseHelper.successful();
    }

    @ApiOperation("检查license的当前状态")
    @GetMapping("/checklicense")
    public ResponseEntity<ResponseResult> checkLicense(){
        return ResponseHelper.successful(licenseService.checkLicense());
    }
}
