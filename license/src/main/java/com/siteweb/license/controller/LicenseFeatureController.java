package com.siteweb.license.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.license.service.LicenseFeatureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "licenseFeature接口管理",tags = "licenseFeature接口管理")
public class LicenseFeatureController {
    @Autowired
    private LicenseFeatureService licenseFeatureService;

    @ApiOperation("获取未激活的url")
    @GetMapping("/noactivepath")
    public ResponseEntity<ResponseResult> getNoActivePath()  {
        return ResponseHelper.successful(licenseFeatureService.findNoActivePath());
    }


    @ApiOperation("获取模块的扩展信息")
    @GetMapping("/featuredata/{id}")
    public ResponseEntity<ResponseResult> getEquipmentAccessNum(@PathVariable Integer id){
        return ResponseHelper.successful(licenseFeatureService.findFeatureData(id));
    }
}
