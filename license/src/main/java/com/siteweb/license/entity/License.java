package com.siteweb.license.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("License")
public class License {
    /**
     * 授权码id
     */
    @TableId(type = IdType.AUTO)
    private Integer licenseId;
    /**
     * 产品名称
     */
    private String product;
    /**
     * 系统唯一uuid
     */
    private String uniqueInfo;
    /**
     * 授权码类型 1试用版 2正式版
     */
    private Integer licenseType;
    /**
     * 激活时间
     */
    private Date activeTime;
    /**
     * 过期时间
     */
    private Date limitTime;
    @TableField(exist = false)
    private List<LicenseFeature> licenseFeatures;
}
