package com.siteweb.license.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("LicenseFeature")
@Data
public class LicenseFeature {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer featureId;
    /**
     * 模块名称
     */
    private String name;
    /**
     * 是否激活了该模块 0否 1是
     */
    private Integer isActive;
    /**
     * 附加数据
     */
    private String data;
}
