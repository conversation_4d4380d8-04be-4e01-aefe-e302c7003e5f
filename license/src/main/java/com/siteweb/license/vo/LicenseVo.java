package com.siteweb.license.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class LicenseVo {
    /**
     * 授权码id
     */
    @TableId(type = IdType.AUTO)
    private Integer licenseId;
    /**
     * 产品名称
     */
    private String product;
    /**
     * 授权码类型 1试用版 2正式版
     */
    private Integer licenseType;
    /**
     * 激活时间
     */
    private Date activeTime;
    /**
     * 过期时间
     */
    private Date limitTime;
}
