<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.license.mapper.LicenseFeatureMapper">
    <update id="activeFeature">
       <foreach collection="list" item="item">
           UPDATE licensefeature SET IsActive = 1,Data = #{item.data} where FeatureId = #{item.featureId};
       </foreach>
    </update>
    <select id="findNoActivePath" resultType="com.siteweb.license.dto.NoActivePathDto">
        SELECT menu.MenuItemId, menu.Title
        FROM licensefeature feature
                 INNER JOIN menuitem menu ON feature.FeatureId = menu.FeatureId
        WHERE feature.IsActive = 0 AND menu.Path != ''
    </select>
</mapper>