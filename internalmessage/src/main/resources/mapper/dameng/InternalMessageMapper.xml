<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.internal.mapper.InternalMessageMapper">
    <select id="findCurrentUserMessage" resultType="com.siteweb.internal.vo.InternalMessageVO">
        SELECT a.InternalMessageId,
               a.body,
               a.messageType,
               a.createTime,
               b.messageStatus
        FROM InternalMessage a
                 INNER JOIN MessageStatus b ON
            a.InternalMessageId = b.InternalMessageId
        WHERE b.userId = #{userId}
    </select>
</mapper>