package com.siteweb.internal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.internal.entity.InternalMessage;
import com.siteweb.internal.vo.InternalMessageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InternalMessageMapper extends BaseMapper<InternalMessage> {
    Page<InternalMessageVO> findCurrentUserMessage(@Param("page") Page<InternalMessageVO> page, @Param("userId") Integer userId);
}
