package com.siteweb.internal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MessageStatus 实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("messagestatus")
public class MessageStatus {
    public MessageStatus(Integer internalMessageId, Integer userId, Integer messageStatus) {
        this.internalMessageId = internalMessageId;
        this.userId = userId;
        this.messageStatus = messageStatus;
    }

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer messageStatusId;

    /**
     * 消息ID
     */
    private Integer internalMessageId;

    /**
     * 用户接收ID
     */
    private Integer userId;

    /**
     * 消息状态  0未读  1已读
     */
    private Integer messageStatus;
}
