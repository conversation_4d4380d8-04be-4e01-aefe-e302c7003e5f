package com.siteweb.internal.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * InternalMessageType 实体类
 */
@Data
@TableName("internalmessagetype")
public class InternalMessageType {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer internalMessageTypeId;

    /**
     * 消息类型
     */
    private String typeName;

    /**
     * 是否启用TTS播报
     */
    private Integer enableTts;
}

