package com.siteweb.internal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * InternalMessage 实体类
 */
@Data
@TableName("internalmessage")
public class InternalMessage {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer internalMessageId;

    /**
     * 消息内容
     */
    private String body;

    /**
     * 消息类型
     */
    private Integer messageType;

    /**
     * 创建时间
     */
    private Date createTime;
}

