package com.siteweb.internal.textnotify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;
import com.siteweb.eventnotification.textnotify.AbstractTextNotifyService;
import com.siteweb.eventnotification.textnotify.NotifyTypeEnum;
import com.siteweb.eventnotification.textnotify.TextNotifyDTO;
import com.siteweb.internal.dto.InternalMessageTextNotifyDTO;
import com.siteweb.internal.entity.InternalMessage;
import com.siteweb.internal.service.InternalMessageTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
@Slf4j
public class InternalMessageTextNotifyService extends AbstractTextNotifyService<InternalMessage> {
    @Autowired
    private InternalMessageTypeService internalMessageTypeService;

    @Override
    public NotifyTypeEnum getNotifyType() {
        return NotifyTypeEnum.INTERNAL_MESSAGE;
    }

    @Override
    public void processAfterConnection(Integer userId, String sessionId) {
        // 站内消息暂时不需要首次连接处理
    }

    @Override
    public TextNotifyDTO<InternalMessageTextNotifyDTO> getCurrentTtsMsg(TtsMessageRequestDTO ttsMessageRequestDTO) {
        InternalMessage internalMessage = getCurrentUserInternalMessage(ttsMessageRequestDTO.getUserId(), ttsMessageRequestDTO.getSessionId(), ttsMessageRequestDTO.getInternalMessageId());
        if (Objects.isNull(internalMessage)) {
            return null;
        }
        InternalMessageTextNotifyDTO notifyText = new InternalMessageTextNotifyDTO(internalMessage.getInternalMessageId(), internalMessage.getBody());
        return new TextNotifyDTO<>(getNotifyType(), notifyText);
    }

    /**获取前用户的站内消息
     */
    private InternalMessage getCurrentUserInternalMessage(Integer userId, String sessionId, Integer internalMessageId) {
        String uniqueId = getUniqueId(userId, sessionId);
        String redisKey = getNotifyType().getRedisKeyPrefix() + uniqueId;

        while (true) {
            //列中分 获取队值最小的元素
            InternalMessage internalMessage = getFirstMessageFromQueue(redisKey, InternalMessage.class);

            // 队列为空，直接返回
            if (internalMessage == null) {
                return null;
            }

            // 判断是否需要弹出并跳过当前消息
            if (isPopMessage(internalMessage, internalMessageId)) {
                redisTemplate.opsForZSet().remove(redisKey, JSONUtil.toJsonStr(internalMessage));
                continue;
            }

            return internalMessage;
        }
    }

    /**
     * 判断是否需要弹出消息
     */
    private boolean isPopMessage(InternalMessage internalMessage, Integer internalMessageId) {
        return Objects.equals(internalMessage.getInternalMessageId(), internalMessageId);
    }

    @Override
    protected List<String> extractMessageTexts(Set<String> messageList) {
        return messageList.stream()
                          .map(message -> {
                              InternalMessage internalMessage = JSONUtil.toBean(JSONUtil.toJsonStr(message), InternalMessage.class);
                              return internalMessage.getBody();
                          })
                          .toList();
    }

    @Override
    protected double calculateScore(InternalMessage message) {
        // 对于站内消息，使用消息创建时间作为排序依据
        return message.getCreateTime() != null ? message.getCreateTime().getTime() : System.currentTimeMillis();
    }

    @Override
    public boolean continueBroadcast(TtsContinueBroadcastRequestDTO ttsContinueBroadcastRequestDTO) {
        // 站内消息默认不需要继续播报
        return false;
    }

    /**
     * 为指定用户添加站内消息
     *
     * @param userIds 用户ID列表
     * @param internalMessage 站内消息
     */
    public void addTtsMessageForUserId(List<Integer> userIds, InternalMessage internalMessage) {
        if (!shouldSendMessage(internalMessage)) {
            return;
        }

        // 获取当前在线用户集合
        Set<String> requestOnlineUserSet = redisTemplate.opsForZSet()
                                                        .range(GlobalConstants.REQUEST_MESSAGE_TIME, 0, -1);

        if (CollUtil.isEmpty(requestOnlineUserSet)) {
            return;
        }
        // 遍历用户ID和在线用户，匹配并添加消息
        for (Integer userId : userIds) {
            requestOnlineUserSet.stream()
                                .filter(onlineUser -> onlineUser.startsWith(String.valueOf(userId)))
                                .forEach(onlineUser ->
                                        // 添加消息到Redis有序集合
                                        pushMsg(onlineUser, internalMessage)
                                );
        }
    }

    /**
     * 消息是否应该发送
     *
     * @param internalMessage 站内消息
     * @return 是否应该发送
     */
    public boolean shouldSendMessage(InternalMessage internalMessage) {
        return internalMessageTypeService.shouldSendMessage(internalMessage.getMessageType());
    }
}

