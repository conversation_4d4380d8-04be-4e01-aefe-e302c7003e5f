package com.siteweb.internal.dto;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.internal.entity.InternalMessage;
import lombok.Data;

import java.util.Date;

@Data
public class InternalMessageCreateDTO {
    /**
     * 接受消息的用户
     */
    private Integer userId;
    /**
     * 消息内容
     */
    private String body;

    /**
     * 消息类型
     */
    private Integer messageType;

    /**
     * 创建时间
     */
    private Date createTime;
    public InternalMessage buildEntity() {
        return BeanUtil.copyProperties(this, InternalMessage.class);
    }
}
