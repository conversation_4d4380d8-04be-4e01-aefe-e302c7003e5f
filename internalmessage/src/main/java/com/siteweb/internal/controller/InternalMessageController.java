package com.siteweb.internal.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.internal.dto.InternalMessageCreateDTO;
import com.siteweb.internal.service.InternalMessageService;
import com.siteweb.internal.vo.InternalMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/internalmessage")
public class InternalMessageController {
    @Autowired
    InternalMessageService internalMessageService;

    @PostMapping
    public ResponseEntity<ResponseResult> createInternalMessage(@RequestBody InternalMessageCreateDTO message) {
        return ResponseHelper.successful(internalMessageService.addMessageForAll(message.buildEntity()));
    }

    @PostMapping(value = "/sendforuser")
    public ResponseEntity<ResponseResult> createInternalMessageByUserId(@RequestBody InternalMessageCreateDTO message) {
        return ResponseHelper.successful(internalMessageService.addMessageForUserId(message.getUserId(), message.buildEntity()));
    }

    @GetMapping("/currentuser")
    public ResponseEntity<ResponseResult> getCurrentUserMessages(Page<InternalMessageVO> page) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(internalMessageService.findCurrentUserMessage(page, userId));
    }
}
