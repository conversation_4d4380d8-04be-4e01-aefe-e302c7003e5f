package com.siteweb.internal.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.internal.service.InternalMessageTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/internalmessagetype")
public class InternalMessageTypeController {
    @Autowired
    InternalMessageTypeService internalMessageTypeService;

    @GetMapping
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(internalMessageTypeService.findAll());
    }
}
