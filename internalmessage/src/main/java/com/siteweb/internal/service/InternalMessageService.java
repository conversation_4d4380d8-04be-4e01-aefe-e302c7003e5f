package com.siteweb.internal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.internal.entity.InternalMessage;
import com.siteweb.internal.vo.InternalMessageVO;

public interface InternalMessageService {
    /**
     * 给系统中所有人添加内部消息
     * @param internalMessage 消息体
     * @return {@link InternalMessage } 内部消息
     */
    InternalMessage addMessageForAll(InternalMessage internalMessage);

    /**
     * 给指定的用户添加消息
     * @param userId 用户id
     * @param internalMessage 内部消息
     * @return {@link InternalMessage } 成功后的实体
     */
    InternalMessage addMessageForUserId(Integer userId, InternalMessage internalMessage);

    Page<InternalMessageVO> findCurrentUserMessage(Page<InternalMessageVO> page, Integer userId);

}
