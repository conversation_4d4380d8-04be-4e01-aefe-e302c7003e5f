package com.siteweb.internal.service;

import com.siteweb.internal.entity.InternalMessageType;

import java.util.List;

public interface InternalMessageTypeService {
    /**
     * 获取所有的消息类型
     * @return {@link List }<{@link InternalMessageType }> 消息类型
     */
    List<InternalMessageType> findAll();

    /**
     * 消息类型是否需要播报TTS语音
     * @param messageType 消息类型
     * @return boolean true 是 false 否
     */
    boolean shouldSendMessage(Integer messageType);
}
