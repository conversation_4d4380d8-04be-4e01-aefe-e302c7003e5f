package com.siteweb.internal.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.service.AccountService;
import com.siteweb.internal.entity.InternalMessage;
import com.siteweb.internal.mapper.InternalMessageMapper;
import com.siteweb.internal.service.InternalMessageService;
import com.siteweb.internal.service.MessageStatusService;
import com.siteweb.internal.textnotify.InternalMessageTextNotifyService;
import com.siteweb.internal.vo.InternalMessageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class InternalMessageServiceImpl implements InternalMessageService {
    @Autowired
    InternalMessageMapper internalMessageMapper;
    @Autowired
    MessageStatusService messageStatusService;
    @Autowired
    AccountService accountService;
    @Autowired
    InternalMessageTextNotifyService internalMessageTextNotifyService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InternalMessage addMessageForAll(InternalMessage internalMessage) {
        internalMessageMapper.insert(internalMessage);
        List<Integer> userIds = accountService.findAllUserIds();
        messageStatusService.addMessageStatusForUserIds(internalMessage.getInternalMessageId(), userIds);
        internalMessageTextNotifyService.addTtsMessageForUserId(userIds,internalMessage);
        return internalMessage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InternalMessage addMessageForUserId(Integer userId, InternalMessage internalMessage) {
        internalMessageMapper.insert(internalMessage);
        messageStatusService.addMessageStatusForUserIds(internalMessage.getInternalMessageId(), List.of(userId));
        internalMessageTextNotifyService.addTtsMessageForUserId(List.of(userId), internalMessage);
        return internalMessage;
    }

    @Override
    public Page<InternalMessageVO> findCurrentUserMessage(Page<InternalMessageVO> page, Integer userId) {
        return internalMessageMapper.findCurrentUserMessage(page, userId);
    }
}
