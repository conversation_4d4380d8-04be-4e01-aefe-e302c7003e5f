package com.siteweb.internal.service;

import java.util.List;

public interface MessageStatusService {

    /**
     * 给指定的用户添加消息
     *
     * @param messageId 消息id
     * @param userIds   用户ids
     */
    void addMessageStatusForUserIds(Integer messageId, List<Integer> userIds);

    /**
     * 读取站内信消息
     * @param userId 用户id
     * @param internalMessageId 站内信消息id
     * @return boolean
     */
    String readMessage(Integer userId, Integer internalMessageId);
}
