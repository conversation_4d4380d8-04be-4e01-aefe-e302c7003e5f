package com.siteweb.internal.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.internal.entity.InternalMessageType;
import com.siteweb.internal.mapper.InternalMessageTypeMapper;
import com.siteweb.internal.service.InternalMessageTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class InternalMessageTypeServiceImpl implements InternalMessageTypeService {
    @Autowired
    InternalMessageTypeMapper internalMessageTypeMapper;

    @Override
    public List<InternalMessageType> findAll() {
        return internalMessageTypeMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public boolean shouldSendMessage(Integer messageType) {
        InternalMessageType internalMessageType = internalMessageTypeMapper.selectOne(Wrappers.lambdaQuery(InternalMessageType.class)
                                                                                              .select(InternalMessageType::getEnableTts)
                                                                                              .eq(InternalMessageType::getInternalMessageTypeId, messageType));
        return Objects.equals(internalMessageType.getEnableTts(), GlobalConstants.YES);
    }
}
