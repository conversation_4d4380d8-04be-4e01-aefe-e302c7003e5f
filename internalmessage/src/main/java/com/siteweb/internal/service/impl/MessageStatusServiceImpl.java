package com.siteweb.internal.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.internal.entity.InternalMessage;
import com.siteweb.internal.entity.MessageStatus;
import com.siteweb.internal.mapper.InternalMessageMapper;
import com.siteweb.internal.mapper.MessageStatusMapper;
import com.siteweb.internal.service.MessageStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class MessageStatusServiceImpl implements MessageStatusService {
    @Autowired
    MessageStatusMapper messageStatusMapper;
    @Autowired
    InternalMessageMapper internalMessageMapper;

    @Override
    public void addMessageStatusForUserIds(Integer internalMessageId, List<Integer> userIds) {
        List<MessageStatus> messageStatusList = new ArrayList<>(userIds.size());
        for (Integer userId : userIds) {
            MessageStatus messageStatus = new MessageStatus(internalMessageId, userId, GlobalConstants.NO);
            messageStatusList.add(messageStatus);
        }
        messageStatusMapper.batchInsert(messageStatusList);
    }

    @Override
    public String readMessage(Integer userId, Integer internalMessageId) {
         messageStatusMapper.update(Wrappers.lambdaUpdate(MessageStatus.class)
                                                  .eq(MessageStatus::getUserId, userId)
                                                  .eq(MessageStatus::getInternalMessageId, internalMessageId)
                                                  .set(MessageStatus::getMessageStatus, GlobalConstants.YES));
        InternalMessage internalMessage = internalMessageMapper.selectById(internalMessageId);
        return internalMessage.getBody();
    }
}
