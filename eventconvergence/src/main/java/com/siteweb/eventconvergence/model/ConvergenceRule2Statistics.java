package com.siteweb.eventconvergence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConvergenceRule2Statistics {
    /**
     * 设备类型ID
     */
    private  Integer equipmentCategory;
    /**
     * 规则Id
     */
    private  Integer ruleId;
    /**
     * 规则名
     */
    private  String ruleName;
    /**
     * 告警条数
     */
    private  int alarmCount;
    /**
     * 配置触发的条数
     */
    private  int  configCount;

    public boolean canConvergence(){
        return  alarmCount >= configCount ? true:false;
    }
}
