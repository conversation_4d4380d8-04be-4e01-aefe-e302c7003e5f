package com.siteweb.eventconvergence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConvergenceRule1Statistics {
    /**
     * 设备类型
     */
    private  Integer equipmentCategory;
    /**
     * 规则Id
     */
    private  Integer ruleId;
    /**
     * 设备Id
     */
    private  Integer equipmentId;
    /**
     *  设备名
     */
    private  String equipmentName;
    /**
     * 告警条数
     */
    private  int alarmCount;
    /**
     * 触发收敛的条数
     */
    private  int configCount;

    public boolean canConvergence(){
        return  alarmCount >= configCount ? true:false;
    }
}
