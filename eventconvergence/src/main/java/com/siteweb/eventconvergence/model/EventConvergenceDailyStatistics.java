package com.siteweb.eventconvergence.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.DecimalFormat;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventConvergenceDailyStatistics {
    /**
     * 统计日期
     */
    private String statisticsDate;
    /**
     * 总条数
     */
    private int totalCount;
    /**
     * 收敛条数
     */
    private int convergenceCount;

    /**
     * 总条数新增
     * @param count
     */
    public void incTotalCount(int count) {
        this.totalCount += count;
    }

    /**
     *  收敛条数新增
     * @param count
     */
    public void incConvergenceCount(int count) {
        this.convergenceCount += count;
    }

    /**
     * 收敛率技术计算
     * @return 收敛率
     */
    public String getConvergenceRate() {
        if (this.totalCount == 0) {
            return "0.00";
        } else {
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format((double) ((float) (this.convergenceCount * 100) / (float) this.totalCount));
        }
    }
}