package com.siteweb.eventconvergence.model;

public enum PowerState {
    UNKNOWN(0),

    ON(1),

    OFF(2),

    LACK_A(3),

    LACK_B(4),

    LACK_C(5),

    LACK_AB(6),

    LACK_AC(7),

    LACK_BC(8);

    private int value = 0;

    private PowerState(int value) {
        this.value = value;
    }

    public static PowerState valueOf(int value) {    //手写的从int到enum的转换函数
        switch (value) {
            case 0:
                return UNKNOWN;
            case 1:
                return ON;
            case 2:
                return OFF;
            case 3:
                return LACK_A;
            case 4:
                return LACK_B;
            case 5:
                return LACK_C;
            case 6:
                return LACK_AB;
            case 7:
                return LACK_AC;
            case 8:
                return LACK_BC;
            default:
                return null;
        }
    }

    public int value() {
        return this.value;
    }
}
