package com.siteweb.eventconvergence.listener;


import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.eventconvergence.manager.TopConvergenceManager;
import com.siteweb.monitoring.entity.AlarmChange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
/*
@Component("EventConvergenceListener")
public class EventConvergenceListener   extends MessageListenerAdapter {
    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Autowired
    GenericJackson2JsonRedisSerializer genericJackson2JsonRedisSerializer;

    @Autowired
    TopConvergenceManager topConvergenceManager;

    private final Logger log = LoggerFactory.getLogger(EventConvergenceListener.class);
    @Override
    public void onMessage(Message message, byte[] pattern) {
        String topic = redisTemplate.getStringSerializer().deserialize(pattern);
        if ("alarmchange".equalsIgnoreCase(topic)) {
            Object deviceLiveEventObj = genericJackson2JsonRedisSerializer.deserialize(message.getBody());
            AlarmChange activeEvent = (AlarmChange) deviceLiveEventObj;
            //如果告警不足以引发收敛，则丢弃
            if (activeEvent == null || activeEvent.getEquipmentId() < 0 || !checkEventCategory(activeEvent.getBaseTypeId()))
                return;

            log.debug("EventConvergenceListener DeviceLivePoint arrived: {}", activeEvent);
            try {
                topConvergenceManager.processConvergenceAlarm(activeEvent);
            } catch (Exception ex) {
                log.error(String.format("EventConvergenceListener update: %s StackTrace: %s", ex.getMessage(), ex.getStackTrace()));
            }
        }
    }

    private boolean checkEventCategory(long baseTypeId){
        if(baseTypeId == 101305001 || baseTypeId == 101027001 || baseTypeId == 101029001 || baseTypeId == 101031001){
            return  true;
        }
        if(baseTypeId == 1601303001)
            return  true;
        if (baseTypeId == 201152001 || baseTypeId == 201027001 ||  baseTypeId == 201029001 || baseTypeId == 201031001 ){
            return  true;
        }
        return  false;
    }

}
*/


@Component
public class EventConvergenceListener  implements ApplicationListener<BaseSpringEvent<AlarmChange>> {
    private final Logger log = LoggerFactory.getLogger(EventConvergenceListener.class);

    @Autowired
    TopConvergenceManager topConvergenceManager;

    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> eventData) {

        AlarmChange alarmChange = (AlarmChange)eventData.getSource();
        log.info("EventConvergenceListener DeviceLivePoint arrived: {}", alarmChange);
        if (alarmChange == null || alarmChange.getEquipmentId() < 0 || ObjectUtil.isNull(alarmChange.getBaseTypeId()) || !checkEventCategory(alarmChange.getBaseTypeId() ))
            return;
        try {
            topConvergenceManager.processConvergenceAlarm(alarmChange);
        } catch (Exception ex) {
            log.error(String.format("EventConvergenceListener update: %s StackTrace: %s", ex.getMessage(), ex.getStackTrace()));
        }

    }

    /**
     * 判断告警基类类型是否匹配
     * @param baseTypeId
     * @return 是否匹配
     */
    private boolean checkEventCategory(long baseTypeId){
        if(ObjectUtil.isNull(baseTypeId)){
            return  false;
        }
        if(baseTypeId == 101305001 || baseTypeId == 101027001 || baseTypeId == 101029001 || baseTypeId == 101031001){
            return  true;
        }
        if(baseTypeId == 1601303001) {
            return true;
        }
        if (baseTypeId == 201152001 || baseTypeId == 201027001 ||  baseTypeId == 201029001 || baseTypeId == 201031001){
            return  true;
        }


        if (baseTypeId == 1703152001 || baseTypeId == 1703027001 ||  baseTypeId == 1703029001 || baseTypeId == 1703031001){
            return  true;
        }
        if (baseTypeId == 1501152001 || baseTypeId == 1501027001 ||  baseTypeId == 1501029001 || baseTypeId == 1501031001){
            return  true;
        }
        return  false;
    }
}
