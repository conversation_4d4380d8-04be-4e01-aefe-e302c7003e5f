package com.siteweb.eventconvergence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("EventConvergenceRule")
public class EventConvergenceRule {

    /**
     *规则ID
     */
    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    /**
     *规则名
     */
    private String ruleName;
    /**
     *收敛类型
     * 1. 单个设备告警收敛
     * 2. 多个设备同样类型告警收敛
     * 3. 单条高频次告警收敛
     * 4. 多层次拓扑收敛
     */
    private Integer convergenceType;
    /**
     *收敛的设备类型
     */
    private Integer equipmentCategory;
    /**
     *开始条件表达式
     */
    private String startExpression;
    /**
     *过滤条件
     */
    private String filterCondition;
    /**
     *收敛规则
     */
    private Integer convergenceInterval;
    /**
     *告警开始条数
     */
    private Integer startCount;
    /**
     *告警结束条数
     */
    private Integer endCount;
    /**
     *可能原因
     */
    private String possibleCauses;

    /**
     * 父级收敛规则Id， 适用于规则4
     */
    private Integer parentRuleId;

    /**
     * 收敛路径， 适用于规则4
     */
    private String  LevelOfPath;
}