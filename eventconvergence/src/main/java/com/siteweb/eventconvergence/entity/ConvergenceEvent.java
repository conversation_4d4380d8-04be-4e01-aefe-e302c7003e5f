package com.siteweb.eventconvergence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("ConvergenceEvent")
public class ConvergenceEvent {

    /**
     *事件ID
     */
    @TableId(value="id", type = IdType.AUTO)
    private Long id;
    /**
     *事件名
     */
    private String eventName;
    /**
     *开始时间
     */
    private Date birthTime;
    /**
     *收敛条数
     */
    private Integer convergenceCount;
    /**
     *事件状态
     */
    private int status;
    /**
     *确认时间
     */
    private Date confirmTime;
    /**
     *可能原因
     */
    private String possibleCauses;
    /**
     * 收敛规则类型
     */
    private int convergenceType;
    /**
     * 收敛规则Id
     */
    private Integer convergenceRuleId;

    /**
     * 设备Id， 适用于规则1的情况
     */
    private Integer equipmentId;

    /**
     * 测点Id， 适用于规则3
     */
    private Integer eventId;

    /**
     * 测点Id， 适用于规则3
     */
    private Integer eventConditionId;


    /**
     * 结束时间
     */
    private Date clearTime;

    /**
     * 确认人Id
     */
    private Integer confirmerId;

    /**
     * 确认人名
     */
    private String  confirmerName;
}
