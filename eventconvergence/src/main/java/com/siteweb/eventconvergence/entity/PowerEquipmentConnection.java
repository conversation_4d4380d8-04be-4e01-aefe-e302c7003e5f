package com.siteweb.eventconvergence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("PowerEquipmentConnection")
@AllArgsConstructor
@NoArgsConstructor
public class PowerEquipmentConnection {

    /**
     * 唯一ID
     */
    @TableId(value="id", type = IdType.AUTO)
    private  Long id;

    /**
     * 父设备Id
     */
    @JsonProperty("from")
    private  Integer parentEquipmentId;

    /**
     * 子设备Id
     */
    @JsonProperty("to")
    private  Integer equipmentId;

    /**
     * 连接路径
     */
    @JsonIgnore
    private String levelOfPath;
}