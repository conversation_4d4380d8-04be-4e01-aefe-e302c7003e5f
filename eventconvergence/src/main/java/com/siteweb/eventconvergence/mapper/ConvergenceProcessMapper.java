package com.siteweb.eventconvergence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import com.siteweb.eventconvergence.model.ConvergenceEquipmentCategoryStatistics;
import com.siteweb.eventconvergence.model.ConvergenceEquipmentStatistics;
import com.siteweb.eventconvergence.model.ConvergenceRule1Statistics;
import com.siteweb.eventconvergence.model.ConvergenceRule2Statistics;

import java.util.List;

public interface ConvergenceProcessMapper  extends BaseMapper<ConvergenceEvent> {
    List<ConvergenceRule1Statistics> getConvergenceRule1Statistics(String filterString);
    void updateParentLiveEventIdByRule1(String filterString);
    List<ConvergenceEquipmentStatistics> getConvergenceEquipmentStatistics(String filterString);

    List<ConvergenceRule2Statistics> getConvergenceRule2Statistics();

    void updateParentLiveEventIdByRule2();

    List<ConvergenceEquipmentCategoryStatistics> getConvergenceEquipmentCategoryStatistics();
}
