package com.siteweb.eventconvergence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PowerEquipmentConnectionMapper extends BaseMapper<PowerEquipmentConnection> {
    List<PowerEquipmentConnection> findByLevelOfPathStartsWith(@Param("levelOfPath") String  levelOfPath);
    PowerEquipmentConnection findByParentEquipmentIdAndEquipmentId(Integer from, Integer to);
    List<Integer> getRootNodeList();

    void constructLevelOfPath1();
    int constructLevelOfPath2();

}
