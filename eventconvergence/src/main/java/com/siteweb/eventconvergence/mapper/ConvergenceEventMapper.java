package com.siteweb.eventconvergence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.eventconvergence.dto.ConvergedEventDTO;
import com.siteweb.eventconvergence.dto.ConvergenceEventDTO;
import com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics;
import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface ConvergenceEventMapper  extends BaseMapper<ConvergenceEvent>{

    List<ConvergenceEvent> findConvergenceEventsByConvergenceTypeAndStatus(@Param("convergenceType") Integer convergenceType , @Param("status") Integer status);

    void batchConfirmEvent(Integer userId, String userName, @org.apache.ibatis.annotations.Param("list")  List<Integer> eventIds);

    List<EventConvergenceDailyStatistics> getEventConvergenceDailyStatisticsFromActiveEvent(Date startDay, Date endDay);

    List<EventConvergenceDailyStatistics> getEventConvergenceDailyStatisticsFromHistoryEvent(Date startDay, Date endDay);
    List<String> getActiveEventSequenceIdByConvergenceEventId(Long convergenceEventId);

    Page<ConvergenceEventDTO> findConvergenceEvents(@Param("page") Page<ConvergenceEventDTO> page, @Param("startDay") Date startDay, @Param("endDay") Date endDay);

    Page<ConvergedEventDTO> findConvergedEventsPageable(@Param("page") Page<ConvergedEventDTO> page, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("keywords") String keywords);

    int processConvergenceSecondary(@Param("id") Long id, @Param("equipmentId") String equipmentId, @Param("eventTypeIds") String eventTypeIds, @Param("batteryFilterSql") String batteryFilterSql, @Param("birthTime") String birthTime);
}



