package com.siteweb.eventconvergence.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.DecimalFormat;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventConvergenceDailyStatistics {
    private String statisticsDate;
    private int totalCount;
    private int convergenceCount;

    public void incTotalCount(int count) {
        this.totalCount += count;
    }

    public void incConvergenceCount(int count) {
        this.convergenceCount += count;
    }

    public String getConvergenceRate() {
        if (this.totalCount == 0) {
            return "0.00";
        } else {
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format((double) ((float) (this.convergenceCount * 100) / (float) this.totalCount));
        }
    }
}
