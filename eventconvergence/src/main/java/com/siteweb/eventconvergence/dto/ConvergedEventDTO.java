package com.siteweb.eventconvergence.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ConvergedEventDTO{
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date birthTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date endTime;

    private String FilterRule;

    private String equipmentName;

    private String meanings;

    private String eventType;

    private String eventName;

    private Long convergenceEventId;
}
