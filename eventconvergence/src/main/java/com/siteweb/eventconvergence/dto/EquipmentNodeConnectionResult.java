package com.siteweb.eventconvergence.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

@Data
public class EquipmentNodeConnectionResult {
    private List<EquipmentNode> equipmentNodes;
    private List<PowerEquipmentConnection> connectionList;

    @JsonIgnore
    public HashMap<Integer,EquipmentNode> getAllNodesHash(){
        HashMap<Integer,EquipmentNode> map = new HashMap<>();
        for(EquipmentNode resourceNode:equipmentNodes){
            map.put(resourceNode.getEquipmentId(), resourceNode);
        }
        return  map;
    }
}