package com.siteweb.eventconvergence.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConvergenceEventDTO {

    /**
     *事件ID
     */
    private Long id;
    /**
     *事件名
     */
    private String eventName;
    /**
     *开始时间
     */
    private Date birthTime;
    /**
     *收敛条数
     */
    private Integer convergenceCount;
    /**
     *事件状态
     */
    private int status;
    /**
     *确认时间
     */
    private Date confirmTime;
    /**
     *可能原因
     */
    private String possibleCauses;
    /**
     * 收敛规则类型
     */
    private int convergenceType;
    /**
     * 收敛规则Id
     */
    private Integer convergenceRuleId;

    /**
     * 设备Id， 适用于规则1的情况
     */
    private Integer equipmentId;

    /**
     * 测点Id， 适用于规则3
     */
    private Integer eventId;

    /**
     * 测点Id， 适用于规则3
     */
    private Integer eventConditionId;


    /**
     * 结束时间
     */
    private Date clearTime;

    /**
     * 确认人Id
     */
    private Integer confirmerId;

    /**
     * 确认人名
     */
    private String  confirmerName;

    /**
     * 当前活动告警树
     */
    private int liveCount;

}
