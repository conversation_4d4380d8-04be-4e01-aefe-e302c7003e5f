package com.siteweb.eventconvergence.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.HistoryEvent;
import lombok.Data;

import java.util.Date;

@Data
public class SimpleLiveEvent {
    /**
     * 事件ID
     */
    private Integer eventId;
    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date birthTime;
    /**
     * 设备位置
     */
    private String devicePosition;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 基类名
     */
    private String baseTypeName;
    /**
     * 触发值
     */
    private String occurValue;
    /**
     * 告警等级
     */
    private int severityId;
    /**
     * 等级名
     */
    private String severityName;
    /**
     * 告警涵义
     */
    private  String occurRemark;
    /**
     * 设备类型
     */
    private  String deviceCategoryName;
    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private  Date endTime;

    public SimpleLiveEvent(ActiveEvent liveEvent){
        this.eventId = liveEvent.getEventId();
        this.birthTime = liveEvent.getStartTime();
        this.equipmentName = liveEvent.getEquipmentName();
        this.eventName = liveEvent.getEventName();
        this.baseTypeName = liveEvent.getBaseTypeName();
        this.occurValue = String.valueOf(liveEvent.getEventValue());
        this.severityId = liveEvent.getEventLevel();
        this.severityName = liveEvent.getEventSeverity();
        this.occurRemark  =  liveEvent.getMeanings();
        this.endTime = liveEvent.getEndTime();
        this.deviceCategoryName = liveEvent.getEquipmentCategoryName();
    }
    public SimpleLiveEvent(HistoryEvent liveEvent){
        this.eventId = liveEvent.getEventId();
        this.birthTime = liveEvent.getStartTime();
        this.equipmentName  = liveEvent.getEquipmentName();
        this.eventName = liveEvent.getEventName();
        this.baseTypeName = liveEvent.getBaseTypeName();
        this.occurValue = String.valueOf(liveEvent.getEventValue());
        this.severityId = liveEvent.getEventLevel();
        this.severityName = liveEvent.getEventSeverity();
        this.occurRemark  =  liveEvent.getMeanings();
        this.endTime = liveEvent.getEndTime();
        this.deviceCategoryName = liveEvent.getEquipmentCategoryName();
    }
}
