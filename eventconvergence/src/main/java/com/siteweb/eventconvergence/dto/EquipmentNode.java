package com.siteweb.eventconvergence.dto;

import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.AlarmState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentNode {
    private Long id;

    private Integer equipmentId;

    private String equipmentName;

    private Integer equipmentCategoryId;

    private String equipmentCategoryName;

    private AlarmState alarmState;

    public EquipmentNode(Equipment equipment){
        this.equipmentId = equipment.getEquipmentId();
        this.equipmentName = equipment.getEquipmentName();
        this.equipmentCategoryId = equipment.getEquipmentBaseType();
        this.alarmState = AlarmState.NORMAL;

    }
}