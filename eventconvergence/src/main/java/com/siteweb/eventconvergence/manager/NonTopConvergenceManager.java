package com.siteweb.eventconvergence.manager;

import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import com.siteweb.eventconvergence.entity.EventConvergenceRule;
import com.siteweb.eventconvergence.mapper.ConvergenceEventMapper;
import com.siteweb.eventconvergence.mapper.ConvergenceProcessMapper;
import com.siteweb.eventconvergence.model.*;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/***
 *  非根因收敛功能
 */
@Slf4j
@Component
public class NonTopConvergenceManager {

    @Autowired
    ConvergenceEventMapper convergenceEventMapper;

    @Autowired
    EventConvergenceManager eventConvergenceManager;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    ActiveSignalManager  activeSignalManager;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    ConvergenceProcessMapper convergenceProcessMapper;

    @Autowired
    HAStatusService haStatusService;
    @Autowired
    AlarmConvergencePopupManager alarmConvergencePopupManager;
    /**
     * 处理规则1.同一设备同类告警的收敛
     */
    @Scheduled(fixedDelay = 1 * 15 * 1000)
    public void processRule1(){
        if (!haStatusService.isMasterHost()) {
            return;
        }
        //获取这个告警规则下，活动的收敛告警
        HashMap<Integer, ConvergenceEvent> currentEvents = eventConvergenceManager.getCurrentConvergenceEventsByRule(1);
        //获取当前所有设备此类告警的统计结果
        List<ConvergenceRule1Statistics> convergenceRule1Statistics =  getConvergenceRule1Statistics();
        //获取告警收敛规则
        ConcurrentHashMap<Integer, EventConvergenceRule> rules = eventConvergenceManager.getEventConvergenceRuleMapByType(1);
        //逐条处理统计结果
        for (ConvergenceRule1Statistics convergenceRule1Statistics1 : convergenceRule1Statistics) {
            //1.如果设备统计结果大于配置开始的，没有收敛告警，写入新的收敛告警
            if(convergenceRule1Statistics1.canConvergence() && !currentEvents.containsKey(convergenceRule1Statistics1.getEquipmentId()) && getEquipmentPowerState(convergenceRule1Statistics1.getEquipmentId()) == PowerState.ON){
                EventConvergenceRule rule = rules.get(convergenceRule1Statistics1.getRuleId());
                ConvergenceEvent event =  createNewConvergenceEvent1(rule,convergenceRule1Statistics1);
                convergenceEventMapper.insert(event);
                alarmConvergencePopupManager.popup(event);
                currentEvents.put(event.getEquipmentId(), event);
            }
        }

        //更新liveEvent 的parentId
        updateParentLiveEventIdByRule1();

        //获取当前设备内同类告警的统计情况
        HashMap<Integer, ConvergenceEquipmentStatistics> convergenceEquipmentStatistics =  getConvergenceEquipmentStatistics();
        //便利当前的收敛告警
        for(ConvergenceEvent convergenceEvent:currentEvents.values()){
            //如果不存在，则直接结束
            if(!convergenceEquipmentStatistics.containsKey(convergenceEvent.getEquipmentId())){
                convergenceEvent.setClearTime(new Date());
                convergenceEvent.setStatus(2);
            }
            //如果当前活动条数小于配置结束的条数，更新并结束
            else if(convergenceEquipmentStatistics.get(convergenceEvent.getEquipmentId()).getLiveCount() <= rules.get(convergenceEvent.getConvergenceRuleId()).getEndCount()){
                //结束当前的告警，更新收敛条数
                convergenceEvent.setStatus(2);
                convergenceEvent.setClearTime(new Date());
                convergenceEvent.setConvergenceCount(convergenceEquipmentStatistics.get(convergenceEvent.getEquipmentId()).getTotalCount());
            }
            else{
                //其余场景更新收敛条数即可
                convergenceEvent.setConvergenceCount(convergenceEquipmentStatistics.get(convergenceEvent.getEquipmentId()).getTotalCount());
            }
            convergenceEventMapper.updateById(convergenceEvent);
            eventConvergenceManager.syncConvergenceEventId(convergenceEvent.getId());
        }
    }

    /**
     *更新规则1收敛的告警的活动事件信息
     */
    private void updateParentLiveEventIdByRule1(){
        String filterStr = eventConvergenceManager.getBatteryFilterString(1);
        convergenceProcessMapper.updateParentLiveEventIdByRule1(filterStr);
    }

    /**
     * 获取当前的规则1告警统计信息
     * @return
     */
    private List<ConvergenceRule1Statistics> getConvergenceRule1Statistics() {

        List<ConvergenceRule1Statistics> liveEventDTOs = convergenceProcessMapper.getConvergenceRule1Statistics( eventConvergenceManager.getBatteryFilterString(1));
        return  liveEventDTOs;
    }

    /**
     * 获取规则1包括的设备告警统计
     * @return
     */
    private HashMap<Integer, ConvergenceEquipmentStatistics> getConvergenceEquipmentStatistics(){

        List<ConvergenceEquipmentStatistics> liveEventDTOs = convergenceProcessMapper.getConvergenceEquipmentStatistics(eventConvergenceManager.getBatteryFilterString(1));
        HashMap<Integer, ConvergenceEquipmentStatistics> result = new HashMap<>();
        for (ConvergenceEquipmentStatistics convergenceEquipmentStatistics : liveEventDTOs) {
            result.put(convergenceEquipmentStatistics.getEquipmentId(), convergenceEquipmentStatistics);
        }
        return  result;
    }

    /**
     * 根据规则1跟告警统计结果，生成新的告警
     * @param eventConvergenceRule
     * @param convergenceRule1Statistics
     * @return
     */
    private ConvergenceEvent createNewConvergenceEvent1(EventConvergenceRule eventConvergenceRule, ConvergenceRule1Statistics convergenceRule1Statistics){
        ConvergenceEvent convergenceEvent = new ConvergenceEvent();
        convergenceEvent.setConvergenceCount(convergenceRule1Statistics.getAlarmCount());
        convergenceEvent.setConvergenceType(eventConvergenceRule.getConvergenceType());
        convergenceEvent.setConvergenceRuleId(eventConvergenceRule.getId());
        convergenceEvent.setEventName(convergenceRule1Statistics.getEquipmentName()+ ":" + eventConvergenceRule.getRuleName());
        convergenceEvent.setConvergenceCount(convergenceRule1Statistics.getAlarmCount());
        convergenceEvent.setEquipmentId(convergenceRule1Statistics.getEquipmentId());
        convergenceEvent.setBirthTime(new Date());
        convergenceEvent.setStatus(1);
        convergenceEvent.setPossibleCauses(eventConvergenceRule.getPossibleCauses());
        return  convergenceEvent;
    }

    /**
     * 处理规则2. 不同设备同类告警的收敛
     */
    @Scheduled(fixedDelay = 1 * 15 * 1000)
    public void processRule2(){
        if (!haStatusService.isMasterHost()) {
            return;
        }
        //获取这个告警规则2下，活动的收敛告警
        HashMap<Integer, ConvergenceEvent> currentEvents = eventConvergenceManager.getCurrentConvergenceEventsByRule(2);
        //获取设备同一设备类型，同类告警的统计结果
        List<ConvergenceRule2Statistics> convergenceRule2Statistics =  getConvergenceRule2Statistics();
        //获取告警收敛规则
        ConcurrentHashMap<Integer, EventConvergenceRule> rules = eventConvergenceManager.getEventConvergenceRuleMapByType(2);
        //逐条处理统计结果
        for(ConvergenceRule2Statistics convergenceRule2Statistics1:convergenceRule2Statistics) {
            //1.如果设备统计结果大于配置开始的，没有收敛告警，写入新的收敛告警
            if(convergenceRule2Statistics1.canConvergence() && !currentEvents.containsKey(convergenceRule2Statistics1.getRuleId())){
                EventConvergenceRule rule = rules.get(convergenceRule2Statistics1.getRuleId());
                ConvergenceEvent event =  createNewConvergenceEvent2(rule,convergenceRule2Statistics1);
                convergenceEventMapper.insert(event);
                alarmConvergencePopupManager.popup(event);
                currentEvents.put(event.getConvergenceRuleId(), event);
            }
        }
        updateParentLiveEventIdByRule2();

        //获取当前设备内同类告警的统计情况
        HashMap<Integer, ConvergenceEquipmentCategoryStatistics> equipmentCategoryStatistics =  getConvergenceEquipmentCategoryStatistics();
        //便利当前的收敛告警
        for(ConvergenceEvent convergenceEvent:currentEvents.values()){
            //如果不存在，则直接结束
            if(!equipmentCategoryStatistics.containsKey(convergenceEvent.getConvergenceRuleId())){
                convergenceEvent.setStatus(2);
                convergenceEvent.setClearTime(new Date());
            }
            //如果当前活动条数小于配置结束的条数，更新并结束
            else if(equipmentCategoryStatistics.get(convergenceEvent.getConvergenceRuleId()).getLiveCount() <= rules.get(convergenceEvent.getConvergenceRuleId()).getEndCount()){
                //结束当前的告警，更新收敛条数
                convergenceEvent.setStatus(2);
                convergenceEvent.setClearTime(new Date());
                convergenceEvent.setConvergenceCount(equipmentCategoryStatistics.get(convergenceEvent.getConvergenceRuleId()).getTotalCount());
            }
            else{
                //其余场景更新收敛条数即可
                convergenceEvent.setConvergenceCount(equipmentCategoryStatistics.get(convergenceEvent.getConvergenceRuleId()).getTotalCount());
            }
            convergenceEventMapper.updateById(convergenceEvent);
            eventConvergenceManager.syncConvergenceEventId(convergenceEvent.getId());
        }
        //保存数据
        //convergenceEventRepository.saveAll(currentEvents.values());
    }

    /**
     * 获取当前的规则2告警统计信息
     * @return
     */
    private List<ConvergenceRule2Statistics> getConvergenceRule2Statistics() {
        List<ConvergenceRule2Statistics> liveEventDTOs = convergenceProcessMapper.getConvergenceRule2Statistics();
        return  liveEventDTOs;
    }

    /**
     * 根据规则2跟告警统计结果，生成新的告警
     * @param eventConvergenceRule
     * @param convergenceRule2Statistics
     * @return
     */
    private ConvergenceEvent createNewConvergenceEvent2(EventConvergenceRule eventConvergenceRule, ConvergenceRule2Statistics convergenceRule2Statistics) {
        ConvergenceEvent convergenceEvent = new ConvergenceEvent();
        convergenceEvent.setConvergenceType(eventConvergenceRule.getConvergenceType());
        convergenceEvent.setConvergenceRuleId(eventConvergenceRule.getId());
        convergenceEvent.setEventName(eventConvergenceRule.getRuleName());
        convergenceEvent.setConvergenceCount(convergenceRule2Statistics.getAlarmCount());
        convergenceEvent.setBirthTime(new Date());
        convergenceEvent.setStatus(1);
        convergenceEvent.setPossibleCauses(eventConvergenceRule.getPossibleCauses());
        return  convergenceEvent;
    }

    /**
     *更新规则2收敛的告警的活动事件信息
     */
    private void updateParentLiveEventIdByRule2(){
        convergenceProcessMapper.updateParentLiveEventIdByRule2();
    }

    /**
     * 获取当前系统同类告警的统计信息，包括活动个数及总个数
     * @return
     */
    private HashMap<Integer, ConvergenceEquipmentCategoryStatistics> getConvergenceEquipmentCategoryStatistics(){
        List<ConvergenceEquipmentCategoryStatistics> liveEventDTOs = convergenceProcessMapper.getConvergenceEquipmentCategoryStatistics();
        HashMap<Integer, ConvergenceEquipmentCategoryStatistics> result = new HashMap<>();
        for (ConvergenceEquipmentCategoryStatistics equipmentCategoryStatistics : liveEventDTOs) {
            result.put(equipmentCategoryStatistics.getRuleId(), equipmentCategoryStatistics);
        }
        return  result;
    }


    /**
     * 判断高压直流是否存在事件故障
     * @param equipmentId
     * @return
     */
    private PowerState processHVDCPowerState(int equipmentId){
        List<Long> baseTypeIds=new ArrayList<>();
        baseTypeIds.add( Long.valueOf(402309001));
        List<SimpleActiveSignal>  activeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );
        Optional<SimpleActiveSignal> s= activeSignal.stream().findFirst();
        SimpleActiveSignal simpleActiveSignal=null;
        if (s.isPresent()) {
            //调用get()返回Optional值。
            simpleActiveSignal = s.get();
        }

        if (simpleActiveSignal != null && simpleActiveSignal.getCurrentState() > 0)
            return PowerState.OFF;

        return  PowerState.ON;
    }

    /**
     * 判断电池流是否存在事件故障
     * @param equipmentId
     * @return
     */
    private PowerState processBatteryPowerState(int equipmentId){
        List<Long> baseTypeIds=new ArrayList<>();
        baseTypeIds.add( Long.valueOf(1101155001));
        List<SimpleActiveSignal>  activeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );
        Optional<SimpleActiveSignal> s= activeSignal.stream().findFirst();
        SimpleActiveSignal simpleActiveSignal=null;
        if (s.isPresent()) {
            //调用get()返回Optional值。
            simpleActiveSignal = s.get();
        }

        if (simpleActiveSignal != null && simpleActiveSignal.getCurrentState() != null &&simpleActiveSignal.getCurrentState() > 0)
            return PowerState.OFF;

        return  PowerState.ON;
    }

    /**
     * 获取设备当前的市电状态
     * @param equipmentId
     * @return
     */
    private PowerState getEquipmentPowerState(int equipmentId){
        //设备不存在则直接返回
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment == null){
            return PowerState.UNKNOWN;
        }
        //判断电池设备的状态
        if (equipment.getEquipmentBaseType() == 1101){
            return  processBatteryPowerState(equipmentId);
        }

        //判断高压直流设备状态
        if(equipment.getEquipmentBaseType() == 402){
            return  processHVDCPowerState(equipmentId);
        }
        return  PowerState.ON;
    }
}
