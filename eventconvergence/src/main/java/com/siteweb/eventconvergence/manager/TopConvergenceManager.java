package com.siteweb.eventconvergence.manager;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import com.siteweb.eventconvergence.entity.EventConvergenceRule;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import com.siteweb.eventconvergence.mapper.ConvergenceEventMapper;
import com.siteweb.eventconvergence.mapper.PowerEquipmentConnectionMapper;
import com.siteweb.eventconvergence.model.EquipmentPowerState;
import com.siteweb.eventconvergence.model.PowerState;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/***
 *  根因收敛管理器
 */
@Slf4j
@Component
public class TopConvergenceManager {

    @Autowired
    ConvergenceEventMapper convergenceEventMapper;


    @Autowired
    EquipmentConnectionManager equipmentConnectionManager;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    PowerEquipmentConnectionMapper powerEquipmentConnectionMapper;

    @Autowired
    EventConvergenceManager eventConvergenceManager;

    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    HAStatusService haStatusService;
    @Autowired
    AlarmConvergencePopupManager alarmConvergencePopupManager;

    /**
     * 当前可能引发收敛的告警设备一览
     */
    private  ConcurrentHashMap<Integer, Date> currentAlarmEquipment = new ConcurrentHashMap<>();

    /**
     * 处理规则4，具体算法是先在单设备按规则1收敛，然后根据设备层级关系处理逐层收敛
     */
    @Scheduled(fixedDelay = 15 * 1000)
    public  void processRule4(){
        if (!haStatusService.isMasterHost()) {
            return;
        }
        //每十秒钟分析一下当前的收敛情况,如果发生了更高一一级设备的, 则更新设备ID及可能原因
        //long startTime=System.currentTimeMillis();
        //先计算当前的设备告警是否可以继续往上收敛
        HashMap<Integer, ConvergenceEvent> convergenceEvents = eventConvergenceManager.getCurrentConvergenceEventsByRule(4);
        List<Integer> needMove = new ArrayList<>();
        //判断当前告警设备的收敛情况
        for (Map.Entry<Integer, Date> entry : currentAlarmEquipment.entrySet()) {
            if(DateUtil.differentSecondsByMillisecond(entry.getValue(), new Date()) < 15){
                continue;
            }
            EquipmentPowerState dp = findRootEquipmentIdByAlarmEquipmentId(entry.getKey());
            //System.out.println("rootDeviceId:" + dp.getDeviceId());
            //如果为null,则说明自己就是根设备
            if(dp == null){
                dp = getPowerEquipmentState(entry.getKey());
            }
            //判断当前设备的告警是否存在，如果不存在则直接新增
            if(!convergenceEvents.containsKey(dp.getEquipmentId()) && dp.getPowerState() != PowerState.ON){
                ConvergenceEvent convergenceEvent = createNewConvergenceEvent4(dp);
                if(convergenceEvent != null){
                    convergenceEventMapper.insert(convergenceEvent);
                    alarmConvergencePopupManager.popup(convergenceEvent);
                    convergenceEvents.put(convergenceEvent.getEquipmentId(), convergenceEvent);
                }
            }
            else{
                needMove.add(entry.getKey());
            }
        }
        //删除无效
        for(Integer equipmentId:needMove){
            currentAlarmEquipment.remove(equipmentId);
        }
        //更新活动告警表
        for (ConvergenceEvent convergenceEvent:convergenceEvents.values()){
            if(convergenceEvent.getId() == null)
                continue;
            int count = processConvergenceSecondary(convergenceEvent);
            //System.out.println("update count:" + count);
            int currentCount = count + convergenceEvent.getConvergenceCount();
            convergenceEvent.setConvergenceCount(currentCount);
            EquipmentPowerState equipmentPowerState = getPowerEquipmentState(convergenceEvent.getEquipmentId());
            if(equipmentPowerState.getPowerState() ==PowerState.ON){
                convergenceEvent.setStatus(2);
                convergenceEvent.setClearTime(new Date());
            }
            convergenceEventMapper.updateById(convergenceEvent);
            eventConvergenceManager.syncConvergenceEventId(convergenceEvent.getId());
        }

        //判断当前告警是否结束
        //convergenceEventRepository.saveAll(convergenceEvents.values());
        //long endTime=System.currentTimeMillis();
        //float excTime=(float)(endTime-startTime)/1000;

        //System.out.println("拓扑执行时间："+excTime+"s");
    }

    /**
     * 根据规则4跟告警统计结果，生成新的告警
     * @param dp 当前设备电状态
     * @return 收敛事件
     */
    private ConvergenceEvent createNewConvergenceEvent4(EquipmentPowerState dp) {
        //获取设备的收敛规则
        List<EventConvergenceRule> rules = eventConvergenceManager.getEventConvergenceRuleListByType(4);
        EventConvergenceRule eventConvergenceRule = getEventConvergenceRuleByEquipmentId(dp.getEquipmentId(),rules);
        //分析根因
        String reason = eventConvergenceManager.getConvergenceReason(dp);
        if(eventConvergenceRule == null)
            return null;
        ConvergenceEvent convergenceEvent = new ConvergenceEvent();
        convergenceEvent.setConvergenceType(eventConvergenceRule.getConvergenceType());
        convergenceEvent.setConvergenceRuleId(eventConvergenceRule.getId());
        convergenceEvent.setEventName(eventConvergenceRule.getRuleName());
        convergenceEvent.setConvergenceCount(0);
        convergenceEvent.setBirthTime(new Date());
        convergenceEvent.setPossibleCauses(reason);
        convergenceEvent.setEquipmentId(dp.getEquipmentId());
        convergenceEvent.setStatus(1);
        convergenceEvent.setConvergenceCount(0);
        //convergenceEvent.setPossibleCauses(eventConvergenceRule.getPossibleCauses());
        return  convergenceEvent;
    }

    /**
     * 获取设备收敛的规则ID
     * @param equipmentId 设备Id
     * @param rules 收敛规则
     * @return 收敛规则
     */
    private EventConvergenceRule getEventConvergenceRuleByEquipmentId(Integer equipmentId, Collection<EventConvergenceRule> rules){
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment == null){
            return  null;
        }
        for(EventConvergenceRule eventConvergenceRule: rules){
            if(eventConvergenceRule.getEquipmentCategory().equals(equipment.getEquipmentBaseType())){
                return  eventConvergenceRule;
            }
        }
        return  null;
    }

    /**
     * 获取根因设备
     * @param alarmEquipmentId 根设备Id
     * @return  收敛规则
     */
    private EquipmentPowerState findRootEquipmentIdByAlarmEquipmentId(int alarmEquipmentId){
        List<Integer> levelPath = equipmentConnectionManager.getEquipmentConnectSeq(alarmEquipmentId);
        if (levelPath == null || levelPath.isEmpty()){
            return  null;
        }
        for (Integer equipmentId:levelPath){
            EquipmentPowerState powerState = getPowerEquipmentState(equipmentId);
            if(powerState.getPowerState() != PowerState.ON){
                return  powerState;
            }
        }
        return  null;
    }

    /**
     * 处理信新来的告警信息
     * @param alarmChange 告警变化
     */
     public void processConvergenceAlarm(AlarmChange alarmChange){
         if (!haStatusService.isMasterHost()) {
             return;
         }
         //如果是告警开始且设备未在收敛设备列表内，则新增
        if(alarmChange.getOperationType() == 1 ) {
            if (currentAlarmEquipment.containsKey(alarmChange.getEquipmentId())) {
                return;
            }
            currentAlarmEquipment.put(alarmChange.getEquipmentId(), new Date());
            return;
        }

        if(alarmChange.getOperationType() != 2){
            currentAlarmEquipment.remove(alarmChange.getEquipmentId());
        }
    }

    /**
     * 更新收敛告警的父ID
     * @param convergenceEvent 收敛事件
     * @return 收敛的条数
     */
    private int processConvergenceSecondary(ConvergenceEvent convergenceEvent){
        //构建收敛的设备ID过滤字符串
        String deviceIds = getConvergenceEquipmentId(convergenceEvent.getEquipmentId());
        //构建告警基类过滤字符串
        String eventTypeIds = getConvergenceEventType();
        String birthTime = DateUtil.dateToString(convergenceEvent.getBirthTime());
        String batteryFilterSql = eventConvergenceManager.getBatteryFilterString(4);
        return convergenceEventMapper.processConvergenceSecondary(convergenceEvent.getId(), deviceIds, eventTypeIds, batteryFilterSql, birthTime);
    }

    /**
     * 获取电表设备的用电状态 1. 正常， 2. 停电。 3. 缺相
     * @param equipmentId
     * @return
     */
    public EquipmentPowerState getPowerEquipmentState(int equipmentId){
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        //系统处于初始化阶段，返回为未知
        if(equipment == null){
            return  new EquipmentPowerState(equipmentId, PowerState.UNKNOWN);
        }
        //检查设备类型
        boolean isValid = checkEquipmentCategory(equipment.getEquipmentBaseType());
        if (!isValid) {
            return  new EquipmentPowerState(equipmentId, PowerState.ON);
        }
        //获取设备的用电状态
        return new EquipmentPowerState(equipmentId,getPowerStateByShadows(equipment));
    }

    /**
     * 判断设备类型是否合规，目前值判断高压配电，变压器，低压配电
     * @param equipmentCategoryId
     * @return
     */
    private  boolean checkEquipmentCategory(int equipmentCategoryId){
        return equipmentCategoryId == 101 || equipmentCategoryId == 1601 || equipmentCategoryId == 201 || equipmentCategoryId == 1501 || equipmentCategoryId==1703;
    }

    /**
     * 根据设备Shadow获取设备的用电状态
     * @param equipment
     * @return
     */
    private PowerState getPowerStateByShadows(Equipment  equipment){

        //判断高压设备有没有停电或者缺相
        if (equipment.getEquipmentBaseType() == 101){
            return  processHvEquipment(equipment.getEquipmentId());
        }

        //判断变压器有没有超温跳闸事件
        if(equipment.getEquipmentBaseType()  == 1601){
            return  processTsEquipment(equipment.getEquipmentId());
        }

        //判断低压设备有没有停电或者缺相
        if (equipment.getEquipmentBaseType() == 201){
            return processLvEquipment(equipment.getEquipmentId());
        }

        if (equipment.getEquipmentBaseType() == 1501){
            return processEMEquipment(equipment.getEquipmentId());
        }

        if (equipment.getEquipmentBaseType() == 1703){
            return processPDEquipment(equipment.getEquipmentId());
        }
        return  PowerState.ON;
    }

    /**
     * 获取高压设备的用电状态
     * @param equipmentId
     * @return
     */
    private  PowerState processHvEquipment(Integer equipmentId){

        List<Long> baseTypeIds=new ArrayList<>();
        baseTypeIds.add( Long.valueOf(101156001));
        baseTypeIds.add( Long.valueOf(101026001));
        baseTypeIds.add( Long.valueOf(101028001));
        baseTypeIds.add( Long.valueOf(101030001));

        List<SimpleActiveSignal>  activeSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );

        Boolean lackA = true;
        Boolean lackB = true;
        Boolean lackC = true;
        for(SimpleActiveSignal activeSignal:activeSignals) {
            if(ObjectUtil.isNotNull(activeSignal.getCurrentState()) && activeSignal.getCurrentState() > 0) {
                if (activeSignal.getBaseTypeId()  == 101156001) {
                    return PowerState.OFF;
                }

                if (activeSignal.getBaseTypeId()  == 101026001) {
                    lackA = false;
                }

                if (activeSignal.getBaseTypeId() == 101028001) {
                    lackB = false;
                }

                if (activeSignal.getBaseTypeId()  == 101030001) {
                    lackC = false;
                }
            }
        }

        if(!lackA && lackB && lackC){
            return  PowerState.LACK_A;
        }
        if(lackA && !lackB && lackC){
            return  PowerState.LACK_B;
        }
        if(lackA && lackB && !lackC){
            return  PowerState.LACK_C;
        }
        if(!lackA && !lackB && lackC){
            return  PowerState.LACK_AB;
        }
        if(!lackA && lackB && !lackC){
            return  PowerState.LACK_AC;
        }
        if(lackA && !lackB && !lackC){
            return  PowerState.LACK_BC;
        }
        if(!lackA && !lackB && !lackC){
            return  PowerState.OFF;
        }
        return  PowerState.ON;
    }

    /**
     * 判断低压设备是否停电或者缺相
     * @param equipmentId
     * @return
     */
    private  PowerState processLvEquipment(Integer equipmentId){

        List<Long> baseTypeIds=new ArrayList<>();
        baseTypeIds.add( Long.valueOf(201156001));
        baseTypeIds.add( Long.valueOf(201026001));
        baseTypeIds.add( Long.valueOf(201028001));
        baseTypeIds.add( Long.valueOf(201030001));

        List<SimpleActiveSignal>  activeSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );


        boolean lackA = true;
        boolean lackB = true;
        boolean lackC = true;

        //通过信号来判断
        for(SimpleActiveSignal activeSignal:activeSignals) {
            if (ObjectUtil.isNotNull(activeSignal.getCurrentState()) &&  activeSignal.getCurrentState() > 0){
                if (activeSignal.getBaseTypeId() == 201156001) {
                    return PowerState.OFF;
                }
                if (activeSignal.getBaseTypeId() == 201026001) {
                    lackA = false;
                }
                if (activeSignal.getBaseTypeId() == 201028001) {
                    lackB = false;
                }
                if (activeSignal.getBaseTypeId() == 201030001) {
                    lackC = false;
                }
            }
        }
        if(!lackA && lackB && lackC){
            return  PowerState.LACK_A;
        }
        if(lackA && !lackB && lackC){
            return  PowerState.LACK_B;
        }
        if(lackA && lackB && !lackC){
            return  PowerState.LACK_C;
        }
        if(!lackA && !lackB && lackC){
            return  PowerState.LACK_AB;
        }
        if(!lackA && lackB && !lackC){
            return  PowerState.LACK_AC;
        }
        if(lackA && !lackB && !lackC){
            return  PowerState.LACK_BC;
        }
        if(!lackA && !lackB && !lackC){
            return  PowerState.OFF;
        }
        return  PowerState.ON;
    }


    /**
     * 判断列头柜是否停电或者缺相
     * @param equipmentId
     * @return
     */
    private  PowerState processPDEquipment(Integer equipmentId){

        List<Long> baseTypeIds=new ArrayList<>();
        //baseTypeIds.add( Long.valueOf(201156001));
        baseTypeIds.add( Long.valueOf(1703026001));
        baseTypeIds.add( Long.valueOf(1703028001));
        baseTypeIds.add( Long.valueOf(1703030001));
        List<SimpleActiveSignal>  activeSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );


        boolean lackA = true;
        boolean lackB = true;
        boolean lackC = true;

        //通过信号来判断
        for(SimpleActiveSignal activeSignal:activeSignals) {
            if (ObjectUtil.isNotNull(activeSignal.getCurrentState()) &&  activeSignal.getCurrentState() > 0){
                if (activeSignal.getBaseTypeId() == 1703152001) {
                    return PowerState.OFF;
                }
                if (activeSignal.getBaseTypeId() == 1703026001) {
                    lackA = false;
                }
                if (activeSignal.getBaseTypeId() == 1703028001) {
                    lackB = false;
                }
                if (activeSignal.getBaseTypeId() == 1703030001) {
                    lackC = false;
                }
            }
        }
        if(!lackA && lackB && lackC){
            return  PowerState.LACK_A;
        }
        if(lackA && !lackB && lackC){
            return  PowerState.LACK_B;
        }
        if(lackA && lackB && !lackC){
            return  PowerState.LACK_C;
        }
        if(!lackA && !lackB && lackC){
            return  PowerState.LACK_AB;
        }
        if(!lackA && lackB && !lackC){
            return  PowerState.LACK_AC;
        }
        if(lackA && !lackB && !lackC){
            return  PowerState.LACK_BC;
        }
        if(!lackA && !lackB && !lackC){
            return  PowerState.OFF;
        }
        return  PowerState.ON;
    }



    /**
     * 判断电表设备是否停电或者缺相
     * @param equipmentId
     * @return
     */
    private  PowerState processEMEquipment(Integer equipmentId){

        List<Long> baseTypeIds=new ArrayList<>();
        baseTypeIds.add( Long.valueOf(1501152001));
        baseTypeIds.add( Long.valueOf(1501026001));
        baseTypeIds.add( Long.valueOf(1501028001));
        baseTypeIds.add( Long.valueOf(1501030001));

        List<SimpleActiveSignal>  activeSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );


        boolean lackA = true;
        boolean lackB = true;
        boolean lackC = true;

        //通过信号来判断
        for(SimpleActiveSignal activeSignal:activeSignals) {
            if (ObjectUtil.isNotNull(activeSignal.getCurrentState()) &&  activeSignal.getCurrentState() > 0){
                if (activeSignal.getBaseTypeId() == 1501152001) {
                    return PowerState.OFF;
                }
                if (activeSignal.getBaseTypeId() == 1501026001) {
                    lackA = false;
                }
                if (activeSignal.getBaseTypeId() == 1501028001) {
                    lackB = false;
                }
                if (activeSignal.getBaseTypeId() == 1501030001) {
                    lackC = false;
                }
            }
        }
        if(!lackA && lackB && lackC){
            return  PowerState.LACK_A;
        }
        if(lackA && !lackB && lackC){
            return  PowerState.LACK_B;
        }
        if(lackA && lackB && !lackC){
            return  PowerState.LACK_C;
        }
        if(!lackA && !lackB && lackC){
            return  PowerState.LACK_AB;
        }
        if(!lackA && lackB && !lackC){
            return  PowerState.LACK_AC;
        }
        if(lackA && !lackB && !lackC){
            return  PowerState.LACK_BC;
        }
        if(!lackA && !lackB && !lackC){
            return  PowerState.OFF;
        }
        return  PowerState.ON;
    }


    /**
     * 判断变压其设备是否跳闸
     * @param equipmentId
     * @return
     */
    private  PowerState processTsEquipment(Integer equipmentId){
        List<Long> baseTypeIds=new ArrayList<>();
        baseTypeIds.add( Long.valueOf(1601302001));
        List<SimpleActiveSignal>  activeSignal = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, baseTypeIds );
        Optional<SimpleActiveSignal> s= activeSignal.stream().findFirst();
        SimpleActiveSignal simpleActiveSignal=null;
        if (s.isPresent()) {
            //调用get()返回Optional值。
            simpleActiveSignal = s.get();
        }

        if (simpleActiveSignal != null && simpleActiveSignal.getCurrentState() > 0)
            return PowerState.OFF;

        return  PowerState.ON;

    }

    /**
     * 构建电池以外设备的基类过滤字符串
     * @return
     */
    private String getConvergenceEventType(){
        ConcurrentHashMap<Integer, EventConvergenceRule> rules = eventConvergenceManager.getEventConvergenceRuleMapByType(4);
        StringBuilder sb = new StringBuilder();
        sb.append('(');
        for(EventConvergenceRule eventConvergenceRule1:rules.values()){
            sb.append(eventConvergenceRule1.getFilterCondition());
            sb.append(",");
        }
        String strEventTypeId = sb.toString();
        String eventTypeId="";
        if(StringUtils.endsWith(strEventTypeId, ",")) {
            eventTypeId = strEventTypeId.substring(0, sb.toString().length() - 1);
            eventTypeId = eventTypeId + ")";
        } else{
            eventTypeId = strEventTypeId + ")";
        }

        return  eventTypeId;
    }

    /**
     * 构建收敛是的设备Id集合
     * @param equipmentId
     * @return
     */
    private String getConvergenceEquipmentId(int equipmentId){
        String path = equipmentConnectionManager.getEquipmentConnectPath(equipmentId);
        boolean isRoot = false;
        if(path == null){
            path = String.valueOf(equipmentId);
            isRoot = true;
        }
        path = path+"%";
        //查找收敛设备下游设备
        List<PowerEquipmentConnection> lst = powerEquipmentConnectionMapper.findByLevelOfPathStartsWith(path);
        // 构建设备过滤字符串
        StringBuilder sb = new StringBuilder();
        sb.append('(');
        if (isRoot){
            sb.append(equipmentId).append(',');
        }
        for (int i = 0; i < lst.size(); i++) {
            sb.append(lst.get(i).getEquipmentId()).append(',');
        }
        String strEquipment = sb.toString();
        String equipmentIds="";
        if(StringUtils.endsWith(strEquipment,",")) {
            equipmentIds = strEquipment.substring(0, sb.toString().length() - 1);
            equipmentIds = equipmentIds+ ")";
        }else {
            equipmentIds = strEquipment + ")";
        }

        return  equipmentIds;
    }
}
