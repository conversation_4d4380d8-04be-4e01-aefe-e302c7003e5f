package com.siteweb.eventconvergence.manager;

import com.siteweb.common.util.StringUtils;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import com.siteweb.eventconvergence.mapper.PowerEquipmentConnectionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备连接管理
 */
@Component
public class EquipmentConnectionManager {
    @Autowired
    PowerEquipmentConnectionMapper powerEquipmentConnectionMapper;

    private ConcurrentHashMap<Integer, String> equipmentConnectionMap = new ConcurrentHashMap<>();

    /**
     * 启动时初始化
     */
    @PostConstruct
    public void init() {
        initPowerEquipmentConnectionList();
    }

    /**
     * 实例化设备的用电连接关系
     */
    private void initPowerEquipmentConnectionList(){
        equipmentConnectionMap.clear();
        List<PowerEquipmentConnection> powerEquipmentConnections = powerEquipmentConnectionMapper.selectList(null);
        for(PowerEquipmentConnection powerEquipmentConnection :powerEquipmentConnections) {
            if(!equipmentConnectionMap.containsKey(powerEquipmentConnection.getEquipmentId())){
                equipmentConnectionMap.put(powerEquipmentConnection.getEquipmentId(),powerEquipmentConnection.getLevelOfPath());
            }
        }
    }

    /**
     * 根据设备ID获取其供电根设备
     * @param equipmentId
     * @return
     */
    public Integer getRootEquipmentId(int equipmentId){
        if(equipmentConnectionMap.containsKey(equipmentId) && equipmentConnectionMap.get(equipmentId) != null ){
            List<Integer> devs = StringUtils.getIntegerListByString(equipmentConnectionMap.get(equipmentId), ".");
            return devs.get(0);
        }
        return  null;
    }

    /**
     * 获取设备的上级设备ID列表
     * @param equipmentId
     * @return
     */
    public  List<Integer> getEquipmentConnectSeq(int equipmentId){
        if(equipmentConnectionMap.containsKey(equipmentId) && equipmentConnectionMap.get(equipmentId) != null ){
            List<Integer> devs = StringUtils.getIntegerListByString(equipmentConnectionMap.get(equipmentId), ".");
            return devs;
        }
        return  new ArrayList<>();
    }

    /**
     *  根据设备Id，生成连接路径
     * @param equipmentId
     * @return
     */
    public String getEquipmentConnectPath(int equipmentId){
        if(equipmentConnectionMap.containsKey(equipmentId) && equipmentConnectionMap.get(equipmentId) != null ){
            return  equipmentConnectionMap.get(equipmentId);
        }
        return  null;
    }
}
