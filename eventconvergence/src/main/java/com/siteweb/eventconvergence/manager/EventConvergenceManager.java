package com.siteweb.eventconvergence.manager;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import com.siteweb.eventconvergence.entity.EventConvergenceRule;
import com.siteweb.eventconvergence.mapper.ConvergenceEventMapper;
import com.siteweb.eventconvergence.mapper.EventConvergenceRuleMapper;
import com.siteweb.eventconvergence.model.EquipmentPowerState;
import com.siteweb.eventconvergence.model.PowerState;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 告警收敛功能通用接口
 */
@Component
@Slf4j
public class EventConvergenceManager {

    @Autowired
    ConvergenceEventMapper convergenceEventMapper;

    @Autowired
    EventConvergenceRuleMapper eventConvergenceRuleMapper;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    private ConcurrentHashMap<Integer, EventConvergenceRule> convergenceRuleMap1 = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, EventConvergenceRule> convergenceRuleMap2 = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Integer, EventConvergenceRule> convergenceRuleMap4 = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        initEventConvergenceRule();
    }

    /***
     * 获取规则1下所有的活动收敛告警
     * @param convergenceType
     * @return
     */
    public HashMap<Integer, ConvergenceEvent> getCurrentConvergenceEventsByRule(int convergenceType){
        List<ConvergenceEvent> currentEvents = convergenceEventMapper.findConvergenceEventsByConvergenceTypeAndStatus(convergenceType, 1);
        HashMap<Integer, ConvergenceEvent> result = new HashMap<>();
        for(ConvergenceEvent convergenceEvent :currentEvents){
            if(convergenceType == 1){
                if(!result.containsKey(convergenceEvent.getEquipmentId())){
                    result.put(convergenceEvent.getEquipmentId(),convergenceEvent);
                }
            } else if(convergenceType == 2){
                if(!result.containsKey(convergenceEvent.getConvergenceRuleId())){
                    result.put(convergenceEvent.getConvergenceRuleId(),convergenceEvent);
                }
            } else if(convergenceType == 4){
                if(!result.containsKey(convergenceEvent.getEquipmentId())){
                    result.put(convergenceEvent.getEquipmentId(),convergenceEvent);
                }
            }
        }
        return  result;
    }

    /**
     *  初始化过滤规则
     */
    public void initEventConvergenceRule() {
        List<EventConvergenceRule> rules = eventConvergenceRuleMapper.selectList(null);

        for (EventConvergenceRule eventConvergenceRule : rules) {
            if (eventConvergenceRule.getConvergenceType() == 1) {
                convergenceRuleMap1.put(eventConvergenceRule.getId(), eventConvergenceRule);
            } else if (eventConvergenceRule.getConvergenceType() == 2) {
                convergenceRuleMap2.put(eventConvergenceRule.getId(), eventConvergenceRule);
            }  else if (eventConvergenceRule.getConvergenceType() == 4) {
                convergenceRuleMap4.put(eventConvergenceRule.getId(), eventConvergenceRule);
            }
        }
    }

    /**
     *  根据类型获取告警收敛规则
     * */
    public ConcurrentHashMap<Integer, EventConvergenceRule>  getEventConvergenceRuleMapByType(int convergenceType) {
        if(convergenceType == 1 ) {
            return convergenceRuleMap1;
        }else if(convergenceType == 2){
            return convergenceRuleMap2;
        }else if(convergenceType == 4){
            return convergenceRuleMap4;
        }
        return  new ConcurrentHashMap<>();
    }

    /**
     *  根据收敛类型获取规则
     * @param convergenceType
     * @return
     */
    public List<EventConvergenceRule> getEventConvergenceRuleListByType(int convergenceType) {
        if(convergenceType == 1 ) {
            return new ArrayList<>(convergenceRuleMap1.values());
        }else if(convergenceType == 2){
            return new ArrayList<>(convergenceRuleMap2.values());
        }else if(convergenceType == 4){
            return new ArrayList<>(convergenceRuleMap4.values());
        }
        return  new ArrayList<>();
    }

    /**
     * 根据设备状态，获取收敛原因
     * @param dp
     * @return
     */
    public String getConvergenceReason(EquipmentPowerState dp ){
        Equipment equipment = equipmentManager.getEquipmentById(dp.getEquipmentId());
        if(dp.getPowerState() == PowerState.ON){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.poweron");
        } else if(dp.getPowerState() == PowerState.OFF){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.poweroff");
        } else if (dp.getPowerState() == PowerState.LACK_A){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.lacka");
        } else if (dp.getPowerState() == PowerState.LACK_B){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.lackb");
        } else if (dp.getPowerState() == PowerState.LACK_C){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.lackc");
        } else if (dp.getPowerState() == PowerState.LACK_AB){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.lackab");
        } else if (dp.getPowerState() == PowerState.LACK_AC){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.lackac");
        }else if (dp.getPowerState() == PowerState.LACK_BC){
            return  equipment.getEquipmentName() + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.lackbc");
        }
        return equipment + ":" +localeMessageSourceUtil.getMessage("eventconvergence.convergencereason.unkown");
    }

    /**
     * 获取电池过滤字符串
     * @param convergenceType
     * @return
     */
    public String getBatteryFilterString(int convergenceType){
        //12V单体电压低过滤
        String sql1 = " (a.BaseTypeId >= 1101191001 AND a.BaseTypeId <= 1101191040) ";
        //2V单体电压低过滤
        String sql2 = " (a.BaseTypeId >= 1101192001 AND a.BaseTypeId <= 1101192120) ";
        //单体内阻增高告警
        String sql3 = " (a.BaseTypeId >= 1101193001 AND a.BaseTypeId <= 1101193120) ";
        //12V单体电压高过滤
        String sql4 = " (a.BaseTypeId >= 1101352001 AND a.BaseTypeId <= 1101352040) ";
        //2V单体电压高过滤
        String sql5 = " (a.BaseTypeId >= 1101355001 and   a.BaseTypeId <=  1101355120) ";
        //单体温度告警
        String sql7 = " (a.BaseTypeId >=  1101310001 AND a.BaseTypeId <= 1101310120) ";
        // 其他单体电压告警
        String sql6 = " a.BaseTypeId = 1101998001 ";

        if(convergenceType == 1){
            return  "(" + sql1 + " OR " + sql2 + " OR " + sql3 + " OR " + sql4 + " OR "  + sql5 + " OR " + sql7 + " OR "+ sql6 + ")";
        }
        if(convergenceType == 4){
            return  sql1 + " OR " + sql2 + " OR " + sql3;
        }
        return  "";
    }

    /**
     *  将告警收敛ID同步到缓存
     * @param convergenceEventId　收敛事件Id
     */
    public void syncConvergenceEventId(Long convergenceEventId){
        List<String> sequenceIds = convergenceEventMapper.getActiveEventSequenceIdByConvergenceEventId(convergenceEventId);
        activeEventManager.syncConvergenceEventId(convergenceEventId, sequenceIds);
    }
}
