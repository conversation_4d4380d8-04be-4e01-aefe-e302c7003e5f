package com.siteweb.eventconvergence.manager;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.websocket.dto.WebSocketCommonMessageBody;
import com.siteweb.common.websocket.enums.WebSocketBusinessTypeEnum;
import com.siteweb.common.websocket.manager.impl.CommonMsgWebSocketManager;
import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 告警收敛弹窗管理
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
@Slf4j
@Component
public class AlarmConvergencePopupManager {
    @Autowired
    private CommonMsgWebSocketManager commonMsgWebSocketManager;
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 向所有在线用户弹出告警收敛的弹窗
     *
     * @param event 事件
     */
    public void popup(ConvergenceEvent event) {
        if (!enableAlarmConvergence()) {
            log.info("没有开启告警收敛弹窗");
            return;
        }
        commonMsgWebSocketManager.sendAllUserMessage(WebSocketCommonMessageBody.of(WebSocketBusinessTypeEnum.ALARM_CONVERGENCE, event));
    }

    /**
     * 是否开启了告警收敛功能
     *
     * @return boolean
     */
    private boolean enableAlarmConvergence() {
        SystemConfig alarmConvergenceConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.ALARM_CONVERGENCE_POPUP.getSystemConfigKey());
        if (Objects.isNull(alarmConvergenceConfig) || CharSequenceUtil.isBlank(alarmConvergenceConfig.getSystemConfigValue())) {
            //大部分现场都不使用该功能，所以默认关闭
            return false;
        }
        return Boolean.parseBoolean(alarmConvergenceConfig.getSystemConfigValue().trim());
    }
}
