package com.siteweb.eventconvergence.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.eventconvergence.dto.PowerEquipmentConnectionDTO;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import com.siteweb.eventconvergence.manager.EquipmentConnectionManager;
import com.siteweb.eventconvergence.mapper.PowerEquipmentConnectionMapper;
import com.siteweb.eventconvergence.service.PowerEquipmentConnectionService;
import com.siteweb.eventconvergence.vo.PowerEquipmentImport;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.EquipmentManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
@Service("PowerEquipmentConnectionService")
public class PowerEquipmentConnectionImpl implements PowerEquipmentConnectionService {
    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    PowerEquipmentConnectionMapper powerEquipmentConnectionMapper;

    @Autowired
    EquipmentConnectionManager equipmentConnectionManager;

    @Override
    public List<PowerEquipmentConnectionDTO> findAll() {
        List<PowerEquipmentConnectionDTO> result = new ArrayList<>();
        List<PowerEquipmentConnection> powerEquipmentConnections =  powerEquipmentConnectionMapper.selectList(null);
        for(PowerEquipmentConnection powerEquipmentConnection:powerEquipmentConnections){
            PowerEquipmentConnectionDTO powerEquipmentConnectionDTO = new PowerEquipmentConnectionDTO();
            powerEquipmentConnectionDTO.setFromId(powerEquipmentConnection.getParentEquipmentId());
            powerEquipmentConnectionDTO.setId(powerEquipmentConnection.getId());
            powerEquipmentConnectionDTO.setToId(powerEquipmentConnection.getEquipmentId());
            Equipment parentEquipment = equipmentManager.getEquipmentById(powerEquipmentConnection.getParentEquipmentId());
            if(parentEquipment != null) {
                powerEquipmentConnectionDTO.setFromName(parentEquipment.getEquipmentName());
            }
            Equipment toEquipment = equipmentManager.getEquipmentById(powerEquipmentConnection.getEquipmentId());
            if(toEquipment != null) {
                powerEquipmentConnectionDTO.setToName(toEquipment.getEquipmentName());
            }
            result.add(powerEquipmentConnectionDTO);
        }
        return  result;
    }

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    public PowerEquipmentConnection findOne(Long id) {
        return  powerEquipmentConnectionMapper.selectById(id);
    }

    @Override
    public void delete(Long id) {
        PowerEquipmentConnection powerDeviceConnection = powerEquipmentConnectionMapper.selectById(id);
        if (powerDeviceConnection != null) {
            delete(powerDeviceConnection);
            checkToIdConnection(powerDeviceConnection.getEquipmentId());
        }
    }

    public void delete(PowerEquipmentConnection powerEquipmentConnection) {
        powerEquipmentConnectionMapper.deleteById(powerEquipmentConnection.getId());
    }


    @Override
    public int  save(PowerEquipmentConnection powerDeviceConnection) {
         return powerEquipmentConnectionMapper.insert(powerDeviceConnection);
    }

    @Override
    public List<PowerEquipmentConnection> save(List<PowerEquipmentConnection> powerDeviceConnectionList) {
        return null;
    }

    @Override
    public List<PowerEquipmentConnectionDTO> importPowerDeviceConnection(List<PowerEquipmentImport> powerEquipmentImports) {
        //powerDeviceConnectionRepository.deleteAll();

        List<PowerEquipmentConnectionDTO> powerDeviceConnectionList = new ArrayList<>();
        if (Objects.isNull(powerEquipmentImports) || powerEquipmentImports.isEmpty()) {
            return powerDeviceConnectionList;
        }

        Integer from;
        Integer to;

        for (PowerEquipmentImport powerEquipmentImport : powerEquipmentImports) {
            Equipment fromEquipment = equipmentManager.getEquipmentsByEquipmentName(powerEquipmentImport.getFromName());
            from = fromEquipment == null ? null : fromEquipment.getEquipmentId();
            if (from == null) {
                continue;
            }
            to = getPowerEquipmentConnectionFlowTo(powerEquipmentImport);
            if (to != null) {
                powerDeviceConnectionList.add(createPowerEquipmentConnection(from, to, powerEquipmentImport));
            }
        }
        constructLevelOfPath();
        //重新构建连接顺序
        equipmentConnectionManager.init();
        return powerDeviceConnectionList;
    }

    public void constructLevelOfPath(){
        powerEquipmentConnectionMapper.constructLevelOfPath1();
        for(int i = 0; i<10; i++){
            int affectRow = powerEquipmentConnectionMapper.constructLevelOfPath2();
            if(affectRow == 0){
                break;
            }
        }
    }

    private Integer getPowerEquipmentConnectionFlowTo(PowerEquipmentImport powerEquipmentImport) {
        Equipment toEquipment = equipmentManager.getEquipmentsByEquipmentName(powerEquipmentImport.getToName());
        if (toEquipment == null) {
            return null;
        } else {
            return toEquipment.getEquipmentId();
        }
    }

    @Override
    public PowerEquipmentConnection update(PowerEquipmentConnection powerEquipmentConnection) {
        if (powerEquipmentConnection.getId() == null) {
            return null;
        }
        PowerEquipmentConnection temp = findOne(powerEquipmentConnection.getId());
        if (temp.getParentEquipmentId().equals(powerEquipmentConnection.getParentEquipmentId()) && temp.getEquipmentId().equals(powerEquipmentConnection.getEquipmentId())) {
            return null;
        }
        Integer oldToId = temp.getEquipmentId();
        save(powerEquipmentConnection);
        if (!oldToId.equals(powerEquipmentConnection.getEquipmentId())) {
            checkToIdConnection(oldToId);
        }
        return powerEquipmentConnection;
    }

    @Override
    public List<PowerEquipmentConnection> createPowerDeviceConnection(PowerEquipmentConnectionDTO powerEquipmentConnectionDTO) {
        List<PowerEquipmentConnection> powerDeviceConnectionList = new ArrayList<>();
        String fromIds = powerEquipmentConnectionDTO.getFromIds();
        String toIds = powerEquipmentConnectionDTO.getToIds();
        if(fromIds==null || fromIds.equals("") || toIds==null || toIds.equals("")){
            return powerDeviceConnectionList;
        }
        if(fromIds.contains(",") && toIds.contains(",")){
            return powerDeviceConnectionList;
        }
        String[] fromIdList = fromIds.split(",");
        String[] toIdList = toIds.split(",");
        if(fromIdList.length==1){
            for(int i=0; i<toIdList.length; i++){
                PowerEquipmentConnection powerDeviceConnection = new PowerEquipmentConnection();
                powerDeviceConnection.setParentEquipmentId(Integer.parseInt(fromIdList[0]));
                powerDeviceConnection.setEquipmentId(Integer.parseInt(toIdList[i]));
                save(powerDeviceConnection);
                powerDeviceConnectionList.add(powerDeviceConnection);
            }
        }else{
            for(int i=0; i<fromIdList.length; i++){
                PowerEquipmentConnection powerDeviceConnection = new PowerEquipmentConnection();
                powerDeviceConnection.setParentEquipmentId(Integer.parseInt(fromIdList[i]));
                powerDeviceConnection.setEquipmentId(Integer.parseInt(toIdList[0]));
                save(powerDeviceConnection);
                powerDeviceConnectionList.add(powerDeviceConnection);
            }
        }
        return powerDeviceConnectionList;
    }

    /**
     * 1.当修改toId时，如果原toId有其他fromId时，删除当前关系就行，
     * 2.否则，该toId无任何关联关系， 需要移除以toId作为fromId的关联关系
     * 3.重复1，2步
     *
     * @param toId
     */
    private void checkToIdConnection(Integer toId) {
        List<PowerEquipmentConnection> powerEquipmentConnections = powerEquipmentConnectionMapper.selectList(new QueryWrapper<PowerEquipmentConnection>().eq("EquipmentId", toId));
        if (Objects.isNull(powerEquipmentConnections) || powerEquipmentConnections.isEmpty()) {
            powerEquipmentConnections = powerEquipmentConnectionMapper.selectList(new QueryWrapper<PowerEquipmentConnection>().eq("ParentEquipmentId", toId));
            if (Objects.nonNull(powerEquipmentConnections) && !powerEquipmentConnections.isEmpty()) {
                for (PowerEquipmentConnection powerEquipmentConnection : powerEquipmentConnections) {
                    Integer tempToId = powerEquipmentConnection.getEquipmentId();
                    delete(powerEquipmentConnection);
                    checkToIdConnection(tempToId);
                }
            }
        }
    }

    private PowerEquipmentConnectionDTO createPowerEquipmentConnection(Integer from, Integer to, PowerEquipmentImport powerEquipmentImport) {
        PowerEquipmentConnection powerEquipmentConnection = powerEquipmentConnectionMapper.findByParentEquipmentIdAndEquipmentId(from, to);
        if (powerEquipmentConnection == null) {
            powerEquipmentConnection = new PowerEquipmentConnection();
            powerEquipmentConnection.setParentEquipmentId(from);
            powerEquipmentConnection.setEquipmentId(to);
            powerEquipmentConnection.setLevelOfPath(from.toString() + "." + to.toString());
            powerEquipmentConnectionMapper.insert(powerEquipmentConnection);

            PowerEquipmentConnectionDTO powerEquipmentConnectionDTO = new PowerEquipmentConnectionDTO();
            powerEquipmentConnectionDTO.setFromId(from);
            powerEquipmentConnectionDTO.setToId(to);
            powerEquipmentConnectionDTO.setFromName(powerEquipmentImport.getFromName());
            powerEquipmentConnectionDTO.setToName(powerEquipmentImport.getToName());
            powerEquipmentConnectionDTO.setToType(powerEquipmentImport.getToType());
            return powerEquipmentConnectionDTO;
        }
        return null;
    }
}
