package com.siteweb.eventconvergence.service;

import com.siteweb.eventconvergence.dto.ConvergedEventDTO;
import com.siteweb.eventconvergence.dto.ConvergenceEventDTO;
import com.siteweb.eventconvergence.dto.ConvergenceResult;
import com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics;
import com.siteweb.eventconvergence.vo.BatchConfirmConvergenceEventVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.Date;

public interface ConvergenceEventService {
    void batchConfirmEvent(BatchConfirmConvergenceEventVO batchConfirmConvergenceEventVO);

    Collection<EventConvergenceDailyStatistics> getEventConvergenceDailyStatistics(Date startDay, Date endDay);

    Page<ConvergenceEventDTO> findConvergenceEvents(Date startDay, Date endDay, Pageable pageable);

    ConvergenceResult findConvergenceEventList(long eventId);

    Page<ConvergedEventDTO> findConvergedEventsPageable(String startTime, String endTime, String keywords, Pageable pageable);
}
