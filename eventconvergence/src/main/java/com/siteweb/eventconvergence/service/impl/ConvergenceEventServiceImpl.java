package com.siteweb.eventconvergence.service.impl;

import com.siteweb.common.util.DateUtil;
import com.siteweb.eventconvergence.dto.*;
import com.siteweb.eventconvergence.mapper.ConvergenceEventMapper;
import com.siteweb.eventconvergence.service.ConvergenceEventService;
import com.siteweb.eventconvergence.vo.BatchConfirmConvergenceEventVO;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.mapper.ActiveEventMapper;
import com.siteweb.monitoring.mapper.HistoryEventMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("ConvergenceEventService")
public class ConvergenceEventServiceImpl implements ConvergenceEventService {
    @Autowired
    ConvergenceEventMapper convergenceEventMapper;

    @Autowired
    ActiveEventMapper activeEventMapper;

    @Autowired
    HistoryEventMapper historyEventMapper;


    @Override
    public void batchConfirmEvent(BatchConfirmConvergenceEventVO batchConfirmConvergenceEventVO) {
        convergenceEventMapper.batchConfirmEvent(batchConfirmConvergenceEventVO.getUserId(), batchConfirmConvergenceEventVO.getUserName(), batchConfirmConvergenceEventVO.getEventIds());
    }

    @Override
    public Collection<EventConvergenceDailyStatistics> getEventConvergenceDailyStatistics(Date startDay, Date endDay) {
        List<String> dateStrings = DateUtil.getDayDatesDuringTime(startDay,endDay);
        LinkedHashMap<String, EventConvergenceDailyStatistics> eventConvergenceDailyStatisticsLinkedHashMap = new LinkedHashMap<>();
        for(String dateString:dateStrings){
            EventConvergenceDailyStatistics statistics = new EventConvergenceDailyStatistics(dateString, 0, 0);
            eventConvergenceDailyStatisticsLinkedHashMap.put(dateString, statistics);
        }
        List<EventConvergenceDailyStatistics> eventConvergenceDailyStatisticss = convergenceEventMapper.getEventConvergenceDailyStatisticsFromActiveEvent(startDay, endDay);

        for (EventConvergenceDailyStatistics eventConvergenceDailyStatistics :eventConvergenceDailyStatisticss){
            if(!eventConvergenceDailyStatisticsLinkedHashMap.containsKey(eventConvergenceDailyStatistics.getStatisticsDate())){
                continue;
            }
            EventConvergenceDailyStatistics e= eventConvergenceDailyStatisticsLinkedHashMap.get(eventConvergenceDailyStatistics.getStatisticsDate());
            e.incTotalCount(eventConvergenceDailyStatistics.getTotalCount());
            e.incConvergenceCount(eventConvergenceDailyStatistics.getConvergenceCount());
        }
        List<EventConvergenceDailyStatistics> eventConvergenceDailyStatisticss2 = convergenceEventMapper.getEventConvergenceDailyStatisticsFromHistoryEvent(startDay, endDay);

       for (EventConvergenceDailyStatistics eventConvergenceDailyStatistics :eventConvergenceDailyStatisticss2){
            if(!eventConvergenceDailyStatisticsLinkedHashMap.containsKey(eventConvergenceDailyStatistics.getStatisticsDate())){
                continue;
            }
            EventConvergenceDailyStatistics e= eventConvergenceDailyStatisticsLinkedHashMap.get(eventConvergenceDailyStatistics.getStatisticsDate());
            e.incTotalCount(eventConvergenceDailyStatistics.getTotalCount());
            e.incConvergenceCount(eventConvergenceDailyStatistics.getConvergenceCount());
        }
        return  eventConvergenceDailyStatisticsLinkedHashMap.values();
    }

    @Override
    public Page<ConvergenceEventDTO> findConvergenceEvents(Date startDay, Date endDay, Pageable pageable) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ConvergenceEventDTO> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ConvergenceEventDTO> convergenceEventDTOPage = convergenceEventMapper.findConvergenceEvents(page, startDay, endDay);
        return new PageImpl<>(convergenceEventDTOPage.getRecords(), pageable, convergenceEventDTOPage.getTotal());
    }

    @Override
    public ConvergenceResult findConvergenceEventList(long eventId) {
        List<SimpleLiveEvent> simpleLiveEvents = new ArrayList<>();
        List<ActiveEvent> liveEvents =  activeEventMapper.findByConvergenceEventId(eventId);
        for(ActiveEvent liveEvent:liveEvents){
            simpleLiveEvents.add(new SimpleLiveEvent(liveEvent));
        }
        List<HistoryEvent> historyEvents = historyEventMapper.findByConvergenceEventId(eventId);
        for(HistoryEvent historyEvent:historyEvents){
            simpleLiveEvents.add(new SimpleLiveEvent(historyEvent));
        }
        Map<String, Long> statistics = simpleLiveEvents.stream().collect(Collectors.groupingBy(SimpleLiveEvent::getDeviceCategoryName, Collectors.counting()));
        ConvergenceResult result = new ConvergenceResult();
        result.setEventList(simpleLiveEvents);
        result.setStatistics(statistics);
        return  result;
    }

    @Override
    public Page<ConvergedEventDTO> findConvergedEventsPageable(String startTime, String endTime, String keywords, Pageable pageable) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ConvergedEventDTO> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ConvergedEventDTO> convergenceEventDTOPage = convergenceEventMapper.findConvergedEventsPageable(page, startTime, endTime,keywords);
        return new PageImpl<>(convergenceEventDTOPage.getRecords(), pageable, convergenceEventDTOPage.getTotal());
    }
}
