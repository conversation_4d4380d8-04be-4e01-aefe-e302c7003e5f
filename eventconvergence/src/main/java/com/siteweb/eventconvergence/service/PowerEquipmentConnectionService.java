package com.siteweb.eventconvergence.service;

import com.siteweb.eventconvergence.dto.PowerEquipmentConnectionDTO;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import com.siteweb.eventconvergence.vo.PowerEquipmentImport;

import java.util.List;

public interface PowerEquipmentConnectionService {
    List<PowerEquipmentConnectionDTO> findAll();

    PowerEquipmentConnection findOne(Long id);

    void delete(Long id);

    int save(PowerEquipmentConnection powerDeviceConnection);

    List<PowerEquipmentConnection> save(List<PowerEquipmentConnection> powerDeviceConnectionList);

    List<PowerEquipmentConnectionDTO> importPowerDeviceConnection(List<PowerEquipmentImport> powerDeviceImportList);

    PowerEquipmentConnection update(PowerEquipmentConnection powerDeviceConnection);

    List<PowerEquipmentConnection> createPowerDeviceConnection(PowerEquipmentConnectionDTO powerDeviceConnectionDTO);
}
