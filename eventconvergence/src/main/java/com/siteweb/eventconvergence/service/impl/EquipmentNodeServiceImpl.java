package com.siteweb.eventconvergence.service.impl;

import com.siteweb.eventconvergence.dto.EquipmentNode;
import com.siteweb.eventconvergence.dto.EquipmentNodeConnectionResult;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import com.siteweb.eventconvergence.manager.EquipmentConnectionManager;
import com.siteweb.eventconvergence.mapper.PowerEquipmentConnectionMapper;
import com.siteweb.eventconvergence.service.EquipmentNodeService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.mamager.EquipmentBaseTypeManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.utility.entity.EquipmentBaseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

@Service("EquipmentNodeService")
public class EquipmentNodeServiceImpl implements EquipmentNodeService {
    @Autowired
    EquipmentConnectionManager equipmentConnectionManager;

    @Autowired
    PowerEquipmentConnectionMapper powerEquipmentConnectionMapper;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    EquipmentStateManager equipmentStateManager;

    @Autowired
    EquipmentBaseTypeManager equipmentBaseTypeManager;

    @Override
    public EquipmentNodeConnectionResult getEquipmentNodeConnectionByDeviceId(Integer equipmentId) {
        EquipmentNodeConnectionResult  result = new EquipmentNodeConnectionResult();
        String path = equipmentConnectionManager.getEquipmentConnectPath(equipmentId);
        boolean isRoot = false;
        if(path == null){
            path = equipmentId.toString();
            isRoot = true;
        }
        path = path+"%";
        List<PowerEquipmentConnection> lst = powerEquipmentConnectionMapper.findByLevelOfPathStartsWith(path);
        List<EquipmentNode> nodeList = new ArrayList<>();
        if(isRoot){
            EquipmentNode deviceNode = createEquipmentNodeByEquipmentId(equipmentId);
            if(deviceNode !=null)
                nodeList.add(deviceNode);
        }
        for (PowerEquipmentConnection powerDeviceConnection :lst){
            EquipmentNode equipmentNode = createEquipmentNodeByEquipmentId(powerDeviceConnection.getEquipmentId());
            if(equipmentNode !=null)
                nodeList.add(equipmentNode);
        }
        result.setEquipmentNodes(nodeList);
        constructRelationShipForNode(result,lst);
        setNodeAlarmState(result);

        return  result;
    }

    @Override
    public List<EquipmentNode> getRootNodeList() {
        List<EquipmentNode> result = new ArrayList<>();
        List<Integer> rootIds = powerEquipmentConnectionMapper.getRootNodeList();
        for (Integer equipmentId :rootIds){
            EquipmentNode deviceNode = createEquipmentNodeByEquipmentId(equipmentId);
            if(deviceNode !=null)
                result.add(deviceNode);
        }
        return  result;
    }


    private EquipmentNode createEquipmentNodeByEquipmentId(Integer equipmentId){
        EquipmentNode equipmentNode = null;
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment != null){
            equipmentNode = new EquipmentNode(equipment);
            EquipmentBaseType equipmentBaseType =  equipmentBaseTypeManager.getEquipmentBaseTypeFromCache(equipment.getEquipmentBaseType());
            if(equipmentBaseType != null){
                equipmentNode.setEquipmentCategoryName(equipmentBaseType.getBaseEquipmentName());
            }
        }
        return  equipmentNode;
    }

    private  EquipmentNode constructEquipmentNode(Equipment equipment){
        EquipmentNode equipmentNode = new EquipmentNode();
        equipmentNode.setEquipmentId(equipment.getEquipmentId());
        equipmentNode.setEquipmentName(equipment.getEquipmentName());
        equipmentNode.setEquipmentCategoryId(equipment.getEquipmentBaseType());
        EquipmentBaseType equipmentBaseType = equipmentBaseTypeManager.getEquipmentBaseTypeFromCache(equipment.getEquipmentBaseType());
        if(equipmentBaseType != null)
            equipmentNode.setEquipmentCategoryName(equipmentBaseType.getBaseEquipmentName());
        return  equipmentNode;
    }

    private void constructRelationShipForNode( EquipmentNodeConnectionResult result, List<PowerEquipmentConnection> lst){
        if(result == null || result.getEquipmentNodes() == null)
            return;
        List<PowerEquipmentConnection> powerConnections = new ArrayList<>();
        HashMap<Integer,EquipmentNode> map = result.getAllNodesHash();
        if(lst != null) {
            for (PowerEquipmentConnection powerEquipmentConnection : lst) {
                if (map.containsKey(powerEquipmentConnection.getParentEquipmentId()) && map.containsKey(powerEquipmentConnection.getEquipmentId())) {
                    powerConnections.add(powerEquipmentConnection);
                }
            }
        }
        result.setConnectionList(powerConnections);
    }

    public void setNodeAlarmState(EquipmentNodeConnectionResult result){
        if(result == null || result.getAllNodesHash() == null)
            return;
        /*HashMap<Integer, DeviceAlarmState> alarmStateHashMap = liveEventService.getDeviceAlarmAlarmState();*/
        Set<Integer> allEquipmentAlarmState = equipmentStateManager.getAllEquipmentAlarmState();
        for(EquipmentNode resourceNode:result.getEquipmentNodes()){
            resourceNode.setAlarmState(allEquipmentAlarmState.contains(resourceNode.getEquipmentId()) ? AlarmState.ALARM : AlarmState.NORMAL);
        }
    }
}
