package com.siteweb.eventconvergence.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value="设备连接实体",description="设备连接实体")
public class PowerDeviceConnectionVO {
    /**
     * 唯一Id
     */
    private Long id;
    /**
     * 父设备Id
     */
    private Integer fromId;
    /**
     * 子设备Id
     */
    private Integer toId;
    /**
     * 起始设备名
     */
    private String fromName;
    /**
     * 终止设备名
     */
    private String toName;
    /**
     *  连接类型
     */
    private String toType;
    /**
     * 父设备列表
     */
    private String fromIds;
    /**
     * 子设备列表
     */
    private String toIds;
}
