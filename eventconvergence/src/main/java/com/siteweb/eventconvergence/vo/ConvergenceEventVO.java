package com.siteweb.eventconvergence.vo;

import com.siteweb.eventconvergence.entity.ConvergenceEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@NoArgsConstructor
@ApiModel(value="收敛事件实体",description="收敛事件实体")
public class ConvergenceEventVO {

    /**
     *事件ID
     */
    @ApiModelProperty(value="唯一ID",name="id")
    private Long id;
    /**
     *事件名
     */
    @ApiModelProperty(value="事件名",name="eventName")
    private String eventName;
    /**
     *开始时间
     */
    @ApiModelProperty(value="开始时间",name="birthTime")
    private Date birthTime;
    /**
     *收敛条数
     */
    @ApiModelProperty(value="收敛条数",name="convergenceCount")
    private Integer convergenceCount;
    /**
     *事件状态
     */
    @ApiModelProperty(value="事件状态",name="status")
    private int status;
    /**
     *确认时间
     */
    @ApiModelProperty(value="确认时间",name="confirmTime")
    private Date confirmTime;
    /**
     *可能原因
     */
    @ApiModelProperty(value="可能原因",name="possibleCauses")
    private String possibleCauses;
    /**
     * 收敛规则类型
     */
    @ApiModelProperty(value="收敛规则类型",name="convergenceType")
    private int convergenceType;
    /**
     * 收敛规则Id
     */
    @ApiModelProperty(value="收敛规则Id",name="convergenceRuleId")
    private Integer convergenceRuleId;

    /**
     * 设备Id， 适用于规则1的情况
     */
    @ApiModelProperty(value="设备Id",name="equipmentId")
    private Integer equipmentId;

    /**
     * 测点Id， 适用于规则3
     */
    @ApiModelProperty(value="事件Id",name="eventId")
    private Integer eventId;

    /**
     * 测点Id， 适用于规则3
     */
    @ApiModelProperty(value="条件Id",name="eventConditionId")
    private Integer eventConditionId;


    /**
     * 结束时间
     */
    @ApiModelProperty(value="结束时间",name="clearTime")
    private Date clearTime;

    /**
     * 确认人Id
     */
    @ApiModelProperty(value="确认人Id",name="confirmerId")
    private Integer confirmerId;

    /**
     * 确认人名
     */
    @ApiModelProperty(value="确认人Id",name="confirmerName")
    private String  confirmerName;

    public ConvergenceEvent build() {
        ConvergenceEvent convergenceevent = new ConvergenceEvent();
        convergenceevent.setId (this.id);
        convergenceevent.setEventName (this.eventName);
        convergenceevent.setBirthTime (this.birthTime);
        convergenceevent.setConvergenceCount (this.convergenceCount);
        convergenceevent.setStatus (this.status);
        convergenceevent.setConfirmTime (this.confirmTime);
        convergenceevent.setPossibleCauses (this.possibleCauses);
        convergenceevent.setConvergenceType(this.convergenceType);
        convergenceevent.setConvergenceRuleId(this.convergenceRuleId);
        convergenceevent.setEquipmentId(this.equipmentId);
        convergenceevent.setEventId(this.eventId);
        convergenceevent.setEventConditionId(this.eventConditionId);
        convergenceevent.setConfirmerName(this.confirmerName);
        convergenceevent.setConfirmerId( this.confirmerId);
        convergenceevent.setClearTime(this.clearTime);
        return convergenceevent;
    }
}
