package com.siteweb.eventconvergence.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.HeaderUtil;
import com.siteweb.eventconvergence.dto.EquipmentNode;
import com.siteweb.eventconvergence.dto.EquipmentNodeConnectionResult;
import com.siteweb.eventconvergence.dto.PowerEquipmentConnectionDTO;
import com.siteweb.eventconvergence.entity.PowerEquipmentConnection;
import com.siteweb.eventconvergence.manager.TopConvergenceManager;
import com.siteweb.eventconvergence.service.EquipmentNodeService;
import com.siteweb.eventconvergence.service.PowerEquipmentConnectionService;
import com.siteweb.eventconvergence.vo.PowerEquipmentImport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value="PowerDeviceConnectionController",tags={"设备连接操作接口"})
public class PowerEquipmentConnectionController {


    private static final String ENTITY_NAME = "PowerEquipmentConnection";

    @Autowired
    private PowerEquipmentConnectionService powerEquipmentConnectionService;

    @Autowired
    private EquipmentNodeService equipmentNodeService;

    @Autowired
    private TopConvergenceManager topConvergenceManager;

    @GetMapping("/powerequipmentconnections")
    public List<PowerEquipmentConnectionDTO> getPowerDeviceConnections() {
        return powerEquipmentConnectionService.findAll();
    }

    @GetMapping("/powerequipmentconnections/{id}")
    public  ResponseEntity<ResponseResult>  getPowerDeviceConnectionById(@PathVariable("id") Long id) {
        PowerEquipmentConnection pwc = powerEquipmentConnectionService.findOne(id);
        if(pwc == null){
            return  ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(pwc);
    }


    @PostMapping(value = "/powerequipmentconnections")
    public  ResponseEntity<ResponseResult>  createPowerDeviceConnection(@Valid @RequestBody PowerEquipmentConnectionDTO powerDeviceConnectionDTO) throws URISyntaxException {
        return ResponseHelper.successful(powerEquipmentConnectionService.createPowerDeviceConnection(powerDeviceConnectionDTO));
    }

    @PostMapping(value = "/powerequipmentconnections/import")
    public  ResponseEntity<ResponseResult>  createPowerDeviceConnection(@Valid @RequestBody List<PowerEquipmentImport> powerDeviceImportList) {
        return ResponseHelper.successful(powerEquipmentConnectionService.importPowerDeviceConnection(powerDeviceImportList));
    }

    @PutMapping(value = "/powerequipmentconnections")
    public  ResponseEntity<ResponseResult>  updatePowerDeviceConnection(@Valid @RequestBody PowerEquipmentConnection powerDeviceConnection) {
        if (powerDeviceConnection.getId() == null) {
            return ResponseHelper.failed("-1", "Id is null", HttpStatus.BAD_REQUEST);
        }
        PowerEquipmentConnection result = powerEquipmentConnectionService.update(powerDeviceConnection);
        return ResponseHelper.successful(result);
    }

    @DeleteMapping(value = "/powerequipmentconnections/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult>  deletePowerDeviceConnection(@PathVariable Long id) {
        powerEquipmentConnectionService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }
    @ApiOperation(value = "获取设备的下挂节点关系")
    @GetMapping(value = "/equipmentnoderelations/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult>  getDeviceConnection(@PathVariable Integer id){
        EquipmentNodeConnectionResult deviceNodes =  equipmentNodeService.getEquipmentNodeConnectionByDeviceId(id);
        return  ResponseHelper.successful(deviceNodes);
    }
    @ApiOperation(value = "获取设备的用电状态")
    @GetMapping(value = "/equipmentpowerstate/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult>  getDevicePowerState(@PathVariable Integer id){
        return ResponseHelper.successful(topConvergenceManager.getPowerEquipmentState(id));
    }
    @ApiOperation(value = "获取根节点")
    @GetMapping(value = "/rootdequipmentnodes", produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult>  getDeviceConnection(){
        List<EquipmentNode>  deviceNodes =  equipmentNodeService.getRootNodeList();
        return  ResponseHelper.successful(deviceNodes);
    }

}
