package com.siteweb.eventconvergence.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics;
import com.siteweb.eventconvergence.service.ConvergenceEventService;
import com.siteweb.eventconvergence.vo.BatchConfirmConvergenceEventVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.Date;

@RestController
@RequestMapping("/api")
@Api(value="ConvergenceEventController",tags={"收敛事件操作接口"})
public class ConvergenceEventController {

    @Autowired
    private ConvergenceEventService convergenceEventService;

    /**
     * 按时间统计告警收敛情况
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @ApiOperation(value = "按时间统计告警收敛情况")
    @GetMapping(
            value = {"/convergenceeventstatistics"},
            params = {"startTime", "endTime"},
            produces = {"application/json"}
    )
    public ResponseEntity<ResponseResult>  getEventConvergenceDailyStatistics(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        Date sd = DateUtil.stringToSimpleDate(startTime);
        Date ed = DateUtil.stringToDate(endTime);
        Collection<EventConvergenceDailyStatistics> eventConvergenceDailyStatistics = convergenceEventService.getEventConvergenceDailyStatistics(sd, ed);
        return ResponseHelper.successful(eventConvergenceDailyStatistics, HttpStatus.OK);
    }

    /**
     * 统计当天的告警收敛情况
     * @return 统计结果
     */
    @ApiOperation(value = "统计当天的告警收敛情况")
    @GetMapping(
            value = {"/currentconvergenceeventstatistics"},
            produces = {"application/json"}
    )
    public ResponseEntity<ResponseResult>  getEventConvergenceDailyStatistics() {
        Date sd = DateUtil.stringToDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
        Date ed = DateUtil.stringToDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd 23:59:59"));

        EventConvergenceDailyStatistics eventConvergenceDailyStatistics = (EventConvergenceDailyStatistics)this.convergenceEventService.getEventConvergenceDailyStatistics(sd, ed).stream().findFirst().orElse(null);
        return ResponseHelper.successful(eventConvergenceDailyStatistics, HttpStatus.OK);
    }

    /**
     * 分页显示收敛事件详情
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页信息
     * @return 收敛事件详情
     */
    @ApiOperation(value = "分页显示收敛事件详情")
    @GetMapping(
            value = {"/convergenceevents"},
            params = {"startTime", "endTime"},
            produces = {"application/json"}
    )
    public ResponseEntity<ResponseResult>  getCurrentEventConvergence(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime, Pageable pageable) {
        Date sd = DateUtil.stringToSimpleDate(startTime);
        Date ed = DateUtil.stringToDate(endTime);
        return  ResponseHelper.successful(convergenceEventService.findConvergenceEvents(sd, ed,pageable), HttpStatus.OK);
    }

    /**
     *  分析单个收敛事件
     * @param convergenceEventId 事件Id
     * @return 分析结果
     */
    @ApiOperation(value = "告警收敛事件分析")
    @GetMapping(value = "/convergenceevents/analysis",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"convergenceEventId"})
    public ResponseEntity<ResponseResult>  getConvergenceResult(Integer convergenceEventId) {
        //获取当前全部活动/结束的告警
        return  ResponseHelper.successful(convergenceEventService.findConvergenceEventList(convergenceEventId));
    }

    /**
     * 按照管键字，时间段来分页查询被收敛的事件信息
     * @param keywords 关键字
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页信息
     * @return 被收敛的事件列表
     */
    @ApiOperation(value = "被收敛的事件一览")
    @GetMapping(value = "/convergedevents",
            params = {"keywords"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult>  findConvergedEventsPageable(@RequestParam String keywords, @RequestParam String startTime,@RequestParam String endTime ,Pageable pageable) {
        return  ResponseHelper.successful(convergenceEventService.findConvergedEventsPageable(startTime,endTime, keywords, pageable));

    }

    /**
     * Post /convergenceevents : create a new convergenceEvent
     *
     * @param convergenceEventDTO the convergenceEvent to create
     * @return the ResponseEntity with status 201 (Created) and with body the new convergenceEvent,
     * or with status 400 (Bad Request) if the convergenceEvent has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping(value = "/convergenceevents")
    public ResponseEntity<ResponseResult>  createConvergenceEvent(@Valid @RequestBody BatchConfirmConvergenceEventVO convergenceEventDTO) throws URISyntaxException {
        convergenceEventService.batchConfirmEvent(convergenceEventDTO);
        return  ResponseHelper.successful(HttpStatus.OK );
    }

}
