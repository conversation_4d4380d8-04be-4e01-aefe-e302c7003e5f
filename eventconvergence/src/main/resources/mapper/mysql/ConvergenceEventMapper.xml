<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.ConvergenceEventMapper">
    <update id="batchConfirmEvent">
            update ConvergenceEvent
            set confirmTime=now(), confirmerId=#{userId}, confirmerName =#{userName}
            where id in
            <foreach collection="list" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
    </update>

    <select id="findConvergenceEventsByConvergenceTypeAndStatus"
            resultType="com.siteweb.eventconvergence.entity.ConvergenceEvent">
        SELECT *
        FROM convergenceevent
        WHERE ConvergenceType=#{convergenceType} AND Status= #{status};
    </select>
    <select id="getEventConvergenceDailyStatisticsFromActiveEvent"
            resultType="com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics">
        select DATE_FORMAT(startTime,'%m%d') statisticsDate,Sum(1)  as totalCount,sum(case when ConvergenceEventId= 0 then 0 else 1 end) as  convergenceCount  from TBL_ActiveEvent
        where startTime &gt;= #{startDay} and startTime &lt; #{endDay}
        group by StatisticsDate;
    </select>
    <select id="getEventConvergenceDailyStatisticsFromHistoryEvent"
            resultType="com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics">
        select DATE_FORMAT(startTime,'%m%d') statisticsDate,Sum(1)  as totalCount,sum(case when ConvergenceEventId= 0 then 0 else 1 end) as  convergenceCount  from TBL_HistoryEvent
        where startTime &gt;= #{startDay} and startTime &lt;  #{endDay}
        group by StatisticsDate;
    </select>

    <select id="getActiveEventSequenceIdByConvergenceEventId" resultType="java.lang.String">
        select SequenceId from TBL_ActiveEvent where ConvergenceEventId = #{convergenceEventId}
    </select>
    <select id="findConvergenceEvents" resultType="com.siteweb.eventconvergence.dto.ConvergenceEventDTO">
        SELECT a.Id,
               a.EventName,
               a.ConvergenceType,
               a.ConvergenceRuleId,
               a.BirthTime,
               a.ConvergenceCount,
               a.Status,
               a.ConfirmTime,
               a.PossibleCauses,
               a.EquipmentId,
               a.EventId,
               a.EventConditionId,
               a.ClearTime,
               a.ConfirmerId,
               a.ConfirmerName,
               sum(case when b.SequenceId is not null then 1 else 0 end) as liveCount
        FROM ConvergenceEvent a
        LEFT JOIN tbl_activeEvent b ON a.Id = b.ConvergenceEventId and b.endTime is null
        WHERE a.birthTime &gt; #{startDay}
          AND a.birthTime &lt;= #{endDay}
          AND a.ConvergenceCount &gt; 0
        GROUP BY a.Id, a.EventName, a.ConvergenceType, a.ConvergenceRuleId, a.BirthTime, a.ConvergenceCount, a.Status, a.ConfirmTime, a.PossibleCauses, a.EquipmentId, a.EventId, a.EventConditionId, a.ClearTime, a.ConfirmerId, a.ConfirmerName
        ORDER BY a.birthTime DESC
    </select>
    <select id="findConvergedEventsPageable" resultType="com.siteweb.eventconvergence.dto.ConvergedEventDTO">
        SELECT startTime as birthTime,EndTime, EquipmentName,EventName,BaseTypeName,Meanings, ConvergenceEventId
        FROM ((SELECT startTime, EndTime, EquipmentName, EventName, Meanings, BaseTypeName, ConvergenceEventId
               FROM tbl_activeevent
               WHERE ConvergenceEventId &gt; 0
                 AND startTime &gt;= #{startTime}
                 AND startTime &lt;= #{endTime}
                 AND (EquipmentName LIKE CONCAT('%', #{keywords}, '%') OR
                      EventName LIKE CONCAT('%', #{keywords}, '%') OR BaseTypeName LIKE CONCAT('%', #{keywords}, '%'))
               ORDER BY startTime DESC)
              UNION ALL
              (SELECT startTime, EndTime, EquipmentName, EventName, BaseTypeName, Meanings, ConvergenceEventId
               FROM TBL_historyevent
               WHERE ConvergenceEventId &gt; 0
                 AND startTime &gt;= #{startTime}
                 AND startTime &lt;= #{endTime}
                 AND (EquipmentName LIKE CONCAT('%', #{keywords}, '%') OR
                      EventName LIKE CONCAT('%', #{keywords}, '%') OR
                      BaseTypeName LIKE CONCAT('%', #{keywords}, '%'))
               ORDER BY startTime DESC)) AS a
        order by startTime desc
    </select>

    <update id="processConvergenceSecondary">
        update TBL_ActiveEvent a set a.ConvergenceEventId = #{id}
            where a.ConvergenceEventId = 0 and a.EquipmentId in ${equipmentId}
            and a.StartTime > #{birthTime} - interval 1 minute
            and a.StartTime &lt;= #{birthTime} + interval 5 minute
    </update>

</mapper>