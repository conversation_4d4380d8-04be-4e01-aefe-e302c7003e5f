<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.ConvergenceProcessMapper">
    <select id="getConvergenceRule1Statistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceRule1Statistics">
        select te.EquipmentBaseType equipmentCategory ,c.Id as ruleId,b.EquipmentId,b.equipmentName,c.StartCount as configCount, count(a.SequenceId) as alarmCount from TBL_Equipment b inner join tbl_equipmenttemplate te on b.EquipmentTemplateId  = te.EquipmentTemplateId  Inner join  eventconvergencerule c on te.EquipmentBaseType = c.EquipmentCategory
        LEFT JOIN TBL_ActiveEvent a on b.EquipmentId  = a.EquipmentId
        where a.ConvergenceEventId =0 and  c.convergenceType = 1 and te.EquipmentBaseType != 1101 and a.EndTime is  null and a.StartTime >= now()-interval c.ConvergenceInterval minute
        and case when c.FilterCondition = '-1 ' then 1 =1 else  find_in_set(a.BaseTypeId, c.FilterCondition)  end
        group by te.EquipmentBaseType,c.Id,b.EquipmentId,b.equipmentName,c.StartCount
        union all
        select te.EquipmentBaseType equipmentCategory ,c.Id as ruleId,b.EquipmentId,b.equipmentName,c.StartCount as configCount, count(a.SequenceId) as alarmCount from TBL_Equipment b inner join tbl_equipmenttemplate te on b.EquipmentTemplateId  = te.EquipmentTemplateId Inner join  eventconvergencerule c on te.EquipmentBaseType = c.EquipmentCategory
        LEFT JOIN TBL_ActiveEvent a on b.EquipmentId  = a.EquipmentId
        where a.ConvergenceEventId =0 and  c.convergenceType = 1 and te.EquipmentBaseType = 1101 and a.EndTime is  null and a.StartTime >= now()-interval c.ConvergenceInterval minute
        and ${filterString}
        group by te.EquipmentBaseType,c.Id,b.EquipmentId,b.equipmentName,c.StartCount
    </select>
    <select id="getConvergenceEquipmentStatistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceEquipmentStatistics">
        select  c.EquipmentId, sum(case when a.EndTime is null then 1 else 0 end) liveCount, sum(1) totalCount
        from ConvergenceEvent c Inner join  eventconvergencerule b on c.convergenceRuleId = b.Id
        INNER JOIN TBL_ActiveEvent a on a.EquipmentId  = c.EquipmentId
        where b.convergenceType = 1 and b.EquipmentCategory != 1101  and c.status = 1 and a.StartTime >= c.birthTime - interval b.ConvergenceInterval minute
        and case when b.FilterCondition = '-1 ' then 1 = 1 else  find_in_set(a.BaseTypeId, b.FilterCondition)  end
        group by c.EquipmentId
        UNION ALL
        select  c.EquipmentId, sum(case when a.EndTime is null then 1 else 0 end) liveCount, sum(1) totalCount
        from ConvergenceEvent c Inner join  eventconvergencerule b on c.convergenceRuleId = b.Id
        INNER JOIN TBL_ActiveEvent a on a.EquipmentId  = c.EquipmentId
        where b.convergenceType = 1 and b.EquipmentCategory = 1101  and c.status = 1 and a.StartTime >= c.birthTime - interval b.ConvergenceInterval minute
        and  ${filerString}
        group by c.EquipmentId;
    </select>
    <select id="getConvergenceRule2Statistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceRule2Statistics">
        select a.EquipmentCategory, a.Id as ruleId, a.ruleName, a.StartCount as configCount, count(b.SequenceId) as alarmCount  from  eventconvergencerule a
        inner join tbl_EquipmentTemplate d on a.EquipmentCategory = d.equipmentbasetype
        inner join tbl_Equipment c on d.EquipmentTemplateId = c.EquipmentTemplateId
        LEFT JOIN tbl_activeevent b on c.equipmentId  =  b.EquipmentId
        where a.convergenceType = 2 and b.endTime is null  and b.StartTime >= now()-interval a.ConvergenceInterval minute
        and case when a.FilterCondition = '-1 ' then 1 =1 else  find_in_set(b.BaseTypeId, a.FilterCondition)  end
        group by a.EquipmentCategory,a.Id,a.ruleName,a.StartCount;
    </select>
    <select id="getConvergenceEquipmentCategoryStatistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceEquipmentCategoryStatistics">
        select  a. convergenceRuleId ruleId, sum(case when c.EndTime is null then 1 else 0 end) liveCount,
        sum(1) totalCount  from ConvergenceEvent a Inner join  eventconvergencerule b on a.convergenceRuleId = b.Id
        inner join tbl_equipmenttemplate te  on te.EquipmentBaseType  = b.EquipmentCategory
        inner join  tbl_Equipment d on te.EquipmentTemplateId  = d.EquipmentTemplateId
        INNER JOIN tbl_activeevent c on d.EquipmentId  = c.EquipmentId
        where b.convergenceType = 2 and a.status = 1 and c.StartTime >= a.BirthTime - interval b.ConvergenceInterval minute
        and case when b.FilterCondition = '-1 ' then 1 =1 else  find_in_set(c.BaseTypeId, b.FilterCondition)  end
        group by a.convergenceRuleId;
    </select>
    <update id="updateParentLiveEventIdByRule1">
        update TBL_ActiveEvent a inner join ConvergenceEvent b on a.EquipmentId = b.EquipmentId and b.convergenceType = 1 and b.Status = 1 and a.ConvergenceEventId =0
        INNER JOIN eventconvergencerule c on  b.convergenceRuleId = c.Id and c.EquipmentCategory != 1101
        and case when c.FilterCondition = '-1 ' then 1 =1 else  find_in_set(a.BaseTypeId, c.FilterCondition)  end
        and a.StartTime >= b.Birthtime - interval c.ConvergenceInterval minute
        set a.ConvergenceEventId = b.Id;
        update TBL_ActiveEvent a inner join ConvergenceEvent b on a.EquipmentId = b.EquipmentId and b.convergenceType = 1 and b.Status = 1 and a.ConvergenceEventId =0
        INNER JOIN eventconvergencerule c on  b.convergenceRuleId = c.Id and c.EquipmentCategory = 1101
        and ${filterString}
        and a.StartTime >= b.Birthtime - interval c.ConvergenceInterval minute
        set a.ConvergenceEventId = b.Id;
    </update>
    <update id="updateParentLiveEventIdByRule2">
        update TBL_ActiveEvent a  inner join tbl_Equipment d on d.EquipmentId = a.equipmentId inner join tbl_equipmenttemplate e on d.EquipmentTemplateId = e.EquipmentTemplateId
        inner JOIN eventconvergencerule b on  e.equipmentbasetype = b.EquipmentCategory and  b.convergenceType = 2 and a.ConvergenceEventId=0
        inner join ConvergenceEvent c on b.Id = c.convergenceRuleId and c.Status = 1
        and case when b.FilterCondition = '-1 ' then 1 =1 else  find_in_set(a.BaseTypeId, b.FilterCondition)  end
        and a.StartTime >= c.Birthtime - interval b.ConvergenceInterval minute
        set a.ConvergenceEventId = c.Id;
    </update>
</mapper>