<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.PowerEquipmentConnectionMapper">
    <select id="findByLevelOfPathStartsWith" resultType="com.siteweb.eventconvergence.entity.PowerEquipmentConnection">
        select id,parentEquipmentId,equipmentId,levelOfPath from PowerEquipmentConnection where levelOfPath like #{levelOfPath}
    </select>
    <select id="findByParentEquipmentIdAndEquipmentId"
            resultType="com.siteweb.eventconvergence.entity.PowerEquipmentConnection">
        select id,parentEquipmentId,equipmentId,levelOfPath from PowerEquipmentConnection where parentEquipmentId = #{from} and EquipmentId=#{to}
    </select>
    <select id="getRootNodeList" resultType="java.lang.Integer">
        select distinct(ParentEquipmentId)  from powerequipmentconnection c where not exists(select 1 from powerequipmentconnection b where c.ParentEquipmentId = b.equipmentId)
    </select>

    <update id="constructLevelOfPath1">
        update PowerEquipmentConnection set LevelOfPath =  concat(CAST(ParentEquipmentId AS CHAR),'.', CAST(EquipmentId AS CHAR))
        where EquipmentId  in (
            select * from (select c.EquipmentId from PowerEquipmentConnection c
                           where not exists(select 1 from PowerEquipmentConnection b where c.ParentEquipmentId = b.EquipmentId)) tmp
        );
    </update>

    <update id="constructLevelOfPath2">
        update PowerEquipmentConnection a inner join PowerEquipmentConnection b on a.ParentEquipmentId = b.EquipmentId
        set a.LevelOfPath =  concat(b.LevelOfPath,'.', CAST(a.EquipmentId AS CHAR))
    </update>
</mapper>