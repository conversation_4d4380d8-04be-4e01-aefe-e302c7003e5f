<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.ConvergenceProcessMapper">
    <select id="getConvergenceRule1Statistics" resultType="com.siteweb.eventconvergence.model.ConvergenceRule1Statistics">
        SELECT te.EquipmentBaseType AS equipmentCategory,
               c.Id AS ruleId,
               b.EquipmentId,
               b.equipmentName,
               c.StartCount AS configCount,
               COUNT(a.SequenceId) AS alarmCount
        FROM TBL_Equipment b
                 INNER JOIN tbl_equipmenttemplate te ON b.EquipmentTemplateId = te.EquipmentTemplateId
                 INNER JOIN eventconvergencerule c ON te.EquipmentBaseType = c.EquipmentCategory
                 LEFT JOIN TBL_ActiveEvent a ON b.EquipmentId = a.EquipmentId
        WHERE a.ConvergenceEventId = 0
          AND c.convergenceType = 1
          AND te.EquipmentBaseType != 1101
          AND a.EndTime IS NULL
          AND a.StartTime >= TIMESTAMPADD(MINUTE, -c.ConvergenceInterval, SYSDATE)
          AND (c.FilterCondition = '-1 ' OR INSTR(',' || c.FilterCondition || ',', ',' || a.BaseTypeId || ',') > 0)
        GROUP BY te.EquipmentBaseType, c.Id, b.EquipmentId, b.equipmentName, c.StartCount
        UNION ALL
        SELECT te.EquipmentBaseType AS equipmentCategory,
               c.Id AS ruleId,
               b.EquipmentId,
               b.equipmentName,
               c.StartCount AS configCount,
               COUNT(a.SequenceId) AS alarmCount
        FROM TBL_Equipment b
                 INNER JOIN tbl_equipmenttemplate te ON b.EquipmentTemplateId = te.EquipmentTemplateId
                 INNER JOIN eventconvergencerule c ON te.EquipmentBaseType = c.EquipmentCategory
                 LEFT JOIN TBL_ActiveEvent a ON b.EquipmentId = a.EquipmentId
        WHERE a.ConvergenceEventId = 0
          AND c.convergenceType = 1
          AND te.EquipmentBaseType = 1101
          AND a.EndTime IS NULL
          AND a.StartTime >= TIMESTAMPADD(MINUTE, -c.ConvergenceInterval, SYSDATE)
          AND ${filterString}
        GROUP BY te.EquipmentBaseType, c.Id, b.EquipmentId, b.equipmentName, c.StartCount;
    </select>
    <select id="getConvergenceEquipmentStatistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceEquipmentStatistics">
        SELECT c.EquipmentId,
               SUM(CASE WHEN a.EndTime IS NULL THEN 1 ELSE 0 END) AS liveCount,
               COUNT(*) AS totalCount
        FROM ConvergenceEvent c
                 INNER JOIN eventconvergencerule b ON c.convergenceRuleId = b.Id
                 INNER JOIN TBL_ActiveEvent a ON a.EquipmentId = c.EquipmentId
        WHERE b.convergenceType = 1
          AND b.EquipmentCategory != 1101
          AND c.status = 1
          AND a.StartTime >= TIMESTAMPADD(MINUTE, -b.ConvergenceInterval, c.birthTime)
          AND (b.FilterCondition = '-1 ' OR INSTR(',' || b.FilterCondition || ',', ',' || a.BaseTypeId || ',') > 0)
        GROUP BY c.EquipmentId
        UNION ALL
        SELECT c.EquipmentId,
               SUM(CASE WHEN a.EndTime IS NULL THEN 1 ELSE 0 END) AS liveCount,
               COUNT(*) AS totalCount
        FROM ConvergenceEvent c
                 INNER JOIN eventconvergencerule b ON c.convergenceRuleId = b.Id
                 INNER JOIN TBL_ActiveEvent a ON a.EquipmentId = c.EquipmentId
        WHERE b.convergenceType = 1
          AND b.EquipmentCategory = 1101
          AND c.status = 1
          AND a.StartTime >= TIMESTAMPADD(MINUTE, -b.ConvergenceInterval, c.birthTime)
          AND ${filterString}
        GROUP BY c.EquipmentId;
    </select>
    <select id="getConvergenceRule2Statistics" resultType="com.siteweb.eventconvergence.model.ConvergenceRule2Statistics">
        SELECT a.EquipmentCategory,
               a.Id                AS ruleId,
               a.ruleName,
               a.StartCount        AS configCount,
               COUNT(b.SequenceId) AS alarmCount
        FROM eventconvergencerule a
                 INNER JOIN tbl_EquipmentTemplate d ON a.EquipmentCategory = d.equipmentbasetype
                 INNER JOIN tbl_Equipment c ON d.EquipmentTemplateId = c.EquipmentTemplateId
                 LEFT JOIN tbl_activeevent b ON c.equipmentId = b.EquipmentId
        WHERE a.convergenceType = 2
          AND b.endTime IS NULL
          AND b.StartTime >= TIMESTAMPADD(MINUTE, -a.ConvergenceInterval, SYSDATE)
          AND (a.FilterCondition = '-1 ' OR INSTR(',' || a.FilterCondition || ',', ',' || b.BaseTypeId || ',') &gt; 0)
        GROUP BY a.EquipmentCategory, a.Id, a.ruleName, a.StartCount;
    </select>
    <select id="getConvergenceEquipmentCategoryStatistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceEquipmentCategoryStatistics">
        SELECT a.convergenceRuleId                                AS ruleId,
               SUM(CASE WHEN c.EndTime IS NULL THEN 1 ELSE 0 END) AS liveCount,
               COUNT(*)                                           AS totalCount
        FROM ConvergenceEvent a
                 INNER JOIN eventconvergencerule b ON a.convergenceRuleId = b.Id
                 INNER JOIN tbl_equipmenttemplate te ON te.EquipmentBaseType = b.EquipmentCategory
                 INNER JOIN tbl_Equipment d ON te.EquipmentTemplateId = d.EquipmentTemplateId
                 INNER JOIN tbl_activeevent c ON d.EquipmentId = c.EquipmentId
        WHERE b.convergenceType = 2
          AND a.status = 1
          AND c.StartTime >= TIMESTAMPADD(MINUTE, -b.ConvergenceInterval, a.BirthTime)
          AND (b.FilterCondition = '-1 ' OR INSTR(',' || b.FilterCondition || ',', ',' || c.BaseTypeId || ',') > 0)
        GROUP BY a.convergenceRuleId;
    </select>
    <update id="updateParentLiveEventIdByRule1">
        UPDATE TBL_ActiveEvent a
            INNER JOIN ConvergenceEvent b ON a.EquipmentId = b.EquipmentId
                AND b.convergenceType = 1
                AND b.Status = 1
                AND a.ConvergenceEventId = 0
            INNER JOIN eventconvergencerule c ON b.convergenceRuleId = c.Id
                AND c.EquipmentCategory != 1101
                AND (c.FilterCondition = '-1 ' OR INSTR(',' || c.FilterCondition || ',', ',' || a.BaseTypeId || ',') > 0)
                AND a.StartTime >= TIMESTAMPADD(MINUTE, -c.ConvergenceInterval, b.Birthtime)
        SET a.ConvergenceEventId = b.Id;

        UPDATE TBL_ActiveEvent a
            INNER JOIN ConvergenceEvent b ON a.EquipmentId = b.EquipmentId
                AND b.convergenceType = 1
                AND b.Status = 1
                AND a.ConvergenceEventId = 0
            INNER JOIN eventconvergencerule c ON b.convergenceRuleId = c.Id
                AND c.EquipmentCategory = 1101
                AND ${filterString}
                AND a.StartTime >= TIMESTAMPADD(MINUTE, -c.ConvergenceInterval, b.Birthtime)
        SET a.ConvergenceEventId = b.Id;
    </update>
    <update id="updateParentLiveEventIdByRule2">
        UPDATE TBL_ActiveEvent a
        SET a.ConvergenceEventId = c.Id
            FROM TBL_ActiveEvent a
            INNER JOIN tbl_Equipment d ON d.EquipmentId = a.equipmentId
            INNER JOIN tbl_equipmenttemplate e ON d.EquipmentTemplateId = e.EquipmentTemplateId
            INNER JOIN eventconvergencerule b ON e.equipmentbasetype = b.EquipmentCategory AND b.convergenceType = 2 AND a.ConvergenceEventId = 0
            INNER JOIN ConvergenceEvent c ON b.Id = c.convergenceRuleId AND c.Status = 1
        WHERE (b.FilterCondition = '-1 ' OR INSTR(',' || b.FilterCondition || ',', ',' || a.BaseTypeId || ',') > 0)
          AND a.StartTime &gt;= TIMESTAMPADD(MINUTE, -b.ConvergenceInterval, c.Birthtime)
    </update>
</mapper>