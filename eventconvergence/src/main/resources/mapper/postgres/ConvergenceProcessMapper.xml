<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.ConvergenceProcessMapper">
    <select id="getConvergenceRule1Statistics" resultType="com.siteweb.eventconvergence.model.ConvergenceRule1Statistics">
        SELECT te.EquipmentBaseType AS equipmentCategory,
               c.Id AS ruleId,
               b.EquipmentId,
               b.equipmentName,
               c.StartCount AS configCount,
               COUNT(a.SequenceId) AS alarmCount
        FROM TBL_Equipment b
                 INNER JOIN tbl_equipmenttemplate te ON b.EquipmentTemplateId = te.EquipmentTemplateId
                 INNER JOIN eventconvergencerule c ON te.EquipmentBaseType = c.EquipmentCategory
                 LEFT JOIN TBL_ActiveEvent a ON b.EquipmentId = a.EquipmentId
        WHERE a.ConvergenceEventId = 0
          AND c.convergenceType = 1
          AND te.EquipmentBaseType != 1101
          AND a.EndTime IS NULL
          AND a.StartTime >= NOW() - INTERVAL '1 minute' * c.ConvergenceInterval
            AND (CASE WHEN c.FilterCondition = '-1 ' THEN TRUE ELSE a.BaseTypeId::text = ANY(string_to_array(c.FilterCondition, ',')) END)
        GROUP BY te.EquipmentBaseType, c.Id, b.EquipmentId, b.equipmentName, c.StartCount
        UNION ALL
        SELECT te.EquipmentBaseType AS equipmentCategory,
               c.Id AS ruleId,
               b.EquipmentId,
               b.equipmentName,
               c.StartCount AS configCount,
               COUNT(a.SequenceId) AS alarmCount
        FROM TBL_Equipment b
                 INNER JOIN tbl_equipmenttemplate te ON b.EquipmentTemplateId = te.EquipmentTemplateId
                 INNER JOIN eventconvergencerule c ON te.EquipmentBaseType = c.EquipmentCategory
                 LEFT JOIN TBL_ActiveEvent a ON b.EquipmentId = a.EquipmentId
        WHERE a.ConvergenceEventId = 0
          AND c.convergenceType = 1
          AND te.EquipmentBaseType = 1101
          AND a.EndTime IS NULL
          AND a.StartTime >= NOW() - INTERVAL '1 minute' * c.ConvergenceInterval
            AND ${filterString}
        GROUP BY te.EquipmentBaseType, c.Id, b.EquipmentId, b.equipmentName, c.StartCount;
    </select>
    <select id="getConvergenceEquipmentStatistics" resultType="com.siteweb.eventconvergence.model.ConvergenceEquipmentStatistics">
        SELECT c.EquipmentId,
               SUM(CASE WHEN a.EndTime IS NULL THEN 1 ELSE 0 END) AS liveCount,
               COUNT(*) AS totalCount
        FROM ConvergenceEvent c
                 INNER JOIN eventconvergencerule b ON c.convergenceRuleId = b.Id
                 INNER JOIN TBL_ActiveEvent a ON a.EquipmentId = c.EquipmentId
        WHERE b.convergenceType = 1
          AND b.EquipmentCategory != 1101
          AND c.status = 1
          AND a.StartTime >= c.birthTime - INTERVAL '1 minute' * b.ConvergenceInterval
            AND (CASE WHEN b.FilterCondition = '-1 ' THEN TRUE ELSE a.BaseTypeId::text = ANY(string_to_array(b.FilterCondition, ',')) END)
        GROUP BY c.EquipmentId
        UNION ALL
        SELECT c.EquipmentId,
               SUM(CASE WHEN a.EndTime IS NULL THEN 1 ELSE 0 END) AS liveCount,
               COUNT(*) AS totalCount
        FROM ConvergenceEvent c
                 INNER JOIN eventconvergencerule b ON c.convergenceRuleId = b.Id
                 INNER JOIN TBL_ActiveEvent a ON a.EquipmentId = c.EquipmentId
        WHERE b.convergenceType = 1
          AND b.EquipmentCategory = 1101
          AND c.status = 1
          AND a.StartTime >= c.birthTime - INTERVAL '1 minute' * b.ConvergenceInterval
            AND ${filterString}
        GROUP BY c.EquipmentId;
    </select>
    <select id="getConvergenceRule2Statistics" resultType="com.siteweb.eventconvergence.model.ConvergenceRule2Statistics">
        SELECT a.EquipmentCategory,
               a.Id AS ruleId,
               a.ruleName,
               a.StartCount AS configCount,
               COUNT(b.SequenceId) AS alarmCount
        FROM eventconvergencerule a
                 INNER JOIN tbl_EquipmentTemplate d ON a.EquipmentCategory = d.equipmentbasetype
                 INNER JOIN tbl_Equipment c ON d.EquipmentTemplateId = c.EquipmentTemplateId
                 LEFT JOIN tbl_activeevent b ON c.equipmentId = b.EquipmentId
        WHERE a.convergenceType = 2
          AND b.endTime IS NULL
          AND b.StartTime >= NOW() - INTERVAL '1 minute' * a.ConvergenceInterval
            AND (CASE WHEN a.FilterCondition = '-1 ' THEN TRUE ELSE b.BaseTypeId::text = ANY(string_to_array(a.FilterCondition, ',')) END)
        GROUP BY a.EquipmentCategory, a.Id, a.ruleName, a.StartCount;
    </select>
    <select id="getConvergenceEquipmentCategoryStatistics"
            resultType="com.siteweb.eventconvergence.model.ConvergenceEquipmentCategoryStatistics">
        SELECT a.convergenceRuleId AS ruleId,
               SUM(CASE WHEN c.EndTime IS NULL THEN 1 ELSE 0 END) AS liveCount,
               COUNT(*) AS totalCount
        FROM ConvergenceEvent a
                 INNER JOIN eventconvergencerule b ON a.convergenceRuleId = b.Id
                 INNER JOIN tbl_equipmenttemplate te ON te.EquipmentBaseType = b.EquipmentCategory
                 INNER JOIN tbl_Equipment d ON te.EquipmentTemplateId = d.EquipmentTemplateId
                 INNER JOIN tbl_activeevent c ON d.EquipmentId = c.EquipmentId
        WHERE b.convergenceType = 2
          AND a.status = 1
          AND c.StartTime >= a.BirthTime - INTERVAL '1 minute' * b.ConvergenceInterval
            AND ( b.FilterCondition = '-1 ' OR c.BaseTypeId::text = ANY(string_to_array(b.FilterCondition, ',')))
        GROUP BY a.convergenceRuleId;
    </select>
    <update id="updateParentLiveEventIdByRule1">
        UPDATE TBL_ActiveEvent a
        SET ConvergenceEventId = b.Id FROM ConvergenceEvent b JOIN eventconvergencerule c ON b.convergenceRuleId = c.Id
        WHERE a.EquipmentId = b.EquipmentId
          AND b.convergenceType = 1
          AND b.Status = 1
          AND a.ConvergenceEventId = 0
          AND c.EquipmentCategory != 1101
          AND (c.FilterCondition = '-1 '
           OR a.BaseTypeId::text = ANY (string_to_array(c.FilterCondition, ',')))
          AND a.StartTime >= b.Birthtime - INTERVAL '1 minute' * c.ConvergenceInterval;
        UPDATE TBL_ActiveEvent a
        SET ConvergenceEventId = b.Id FROM ConvergenceEvent b JOIN eventconvergencerule c ON b.convergenceRuleId = c.Id
        WHERE a.EquipmentId = b.EquipmentId
          AND b.convergenceType = 1
          AND b.Status = 1
          AND a.ConvergenceEventId = 0
          AND c.EquipmentCategory = 1101
          AND ${filterString}
          AND a.StartTime >= b.Birthtime - INTERVAL '1 minute' * c.ConvergenceInterval;
    </update>
    <update id="updateParentLiveEventIdByRule2">
        UPDATE TBL_ActiveEvent a
        SET ConvergenceEventId = c.Id
            FROM tbl_Equipment d JOIN tbl_equipmenttemplate e ON d.EquipmentTemplateId = e.EquipmentTemplateId
            JOIN eventconvergencerule b ON e.equipmentbasetype = b.EquipmentCategory
            JOIN ConvergenceEvent c ON b.Id = c.convergenceRuleId
        WHERE d.EquipmentId = a.equipmentId
          AND b.convergenceType = 2
          AND a.ConvergenceEventId = 0
          AND c.Status = 1
          AND (b.FilterCondition = '-1 ' OR a.BaseTypeId::text = ANY(string_to_array(b.FilterCondition, ',')))
          AND a.StartTime >= c.Birthtime - INTERVAL '1 minute' * b.ConvergenceInterval;
    </update>
</mapper>