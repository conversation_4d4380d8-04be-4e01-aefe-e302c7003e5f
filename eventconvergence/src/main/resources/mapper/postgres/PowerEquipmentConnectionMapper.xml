<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.PowerEquipmentConnectionMapper">
    <select id="findByLevelOfPathStartsWith" resultType="com.siteweb.eventconvergence.entity.PowerEquipmentConnection">
        select id,parentEquipmentId,equipmentId,levelOfPath from PowerEquipmentConnection where levelOfPath like #{levelOfPath}
    </select>
    <select id="findByParentEquipmentIdAndEquipmentId"
            resultType="com.siteweb.eventconvergence.entity.PowerEquipmentConnection">
        select id,parentEquipmentId,equipmentId,levelOfPath from PowerEquipmentConnection where parentEquipmentId = #{from} and EquipmentId=#{to}
    </select>
    <select id="getRootNodeList" resultType="java.lang.Integer">
        select distinct(ParentEquipmentId)  from powerequipmentconnection c where not exists(select 1 from powerequipmentconnection b where c.ParentEquipmentId = b.equipmentId)
    </select>

    <update id="constructLevelOfPath1">
        UPDATE PowerEquipmentConnection
        SET LevelOfPath = ParentEquipmentId::text || '.' || EquipmentId::text
        WHERE EquipmentId IN (
            SELECT c.EquipmentId
            FROM PowerEquipmentConnection c
            WHERE NOT EXISTS (
            SELECT 1
            FROM PowerEquipmentConnection b
            WHERE c.ParentEquipmentId = b.EquipmentId));
    </update>

    <update id="constructLevelOfPath2">
        UPDATE PowerEquipmentConnection a
        SET LevelOfPath = b.LevelOfPath || '.' || a.EquipmentId::text
        FROM PowerEquipmentConnection b
        WHERE a.ParentEquipmentId = b.EquipmentId;
    </update>
</mapper>