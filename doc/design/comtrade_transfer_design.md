# COMTRADE 多协议传输支持设计方案

> 目标：在保持旧项目向后兼容的前提下，为新厂商设备提供 **SCP** 下载能力，同时保留现有 **FTP** 实现。

---

## 1. 新增系统参数
| 序号 | 键 | 示例值 | 说明 |
| ---- | --------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- |
| 214  | `comtrade.vendor.transfer.config` | `{"DEFAULT":{"protocol":"FTP","directory":"/comtrade"},"VendorA":{"protocol":"FTP","port":21,"username":"userA","password":"passA","directory":"/comtrade"},"VendorB":{"protocol":"SCP","port":22,"username":"userB","password":"passB","directory":"/opt/comtrade"}}` | 以 **单行 JSON** 存储，各字段含义：`protocol`（FTP/SCP）、`port`、`username`、`password`、`directory`。`DEFAULT` 为兜底配置。 |

> **兼容策略**：若该参数不存在或无法解析，代码自动退回至原全局 `comtrade.ftp.*` 三键逻辑。

---

## 2. 主要改动点一览

| # | 文件 | 位置(行) | 方法 / 区域 | 调整要点 |
| - | ---- | -------- | ----------- | -------- |
| 1 | `core/src/main/resources/db/changelog/siteweb/utility.mysql.init.sql` | 文件末尾 | \- | 追加 214 插入脚本，上表所示参数 |
| 2 | `core/src/main/resources/db/changelog/siteweb/mysql/sql/122-utility.mysql.init.sql` | 文件末尾 | \- | 追加 214 插入脚本，上表所示参数 |
| 3 | `core/src/main/resources/db/changelog/siteweb/postgres/sql/122-utility.mysql.init.sql` | 文件末尾 | \- | 同一脚本已被 postgres 引用，追加相同插入语句 |
| 4 | `core/src/main/resources/db/changelog/siteweb/opengauss/sql/122-utility.mysql.init.sql` | 文件末尾 | \- | openGauss 同上 |
| 5 | `core/src/main/resources/db/changelog/siteweb/dameng/sql/122-utility.mysql.init.xml` | 文件末尾 | \- | 达梦数据库使用 XML 形式，需添加 `<insert>` 节点写入 214 参数 |
| 6 | `common/src/main/java/com/siteweb/common/service/SystemConfigService.java` | 新增 | `getVendorTransferConfig()` | 读取并缓存 214 参数，返回 `Map<String, VendorTransferCfg>` |
| 7 | `common/src/main/java/com/siteweb/common/dto/VendorTransferCfg.java` | 新建 | \- | 字段：`protocol`、`port`、`username`、`password`、`directory` |
| 4 | `hmi/src/main/java/com/siteweb/hmi/service/impl/ComtradeFtpServiceImpl.java` | 113 起 |
  * 方法开头：根据 `equipmentId → EquipmentTemplate.vendor` 取得厂商；
  * 使用 `SystemConfigService#getVendorTransferConfig()` 选择配置；
  * `protocol == FTP`：沿用现逻辑，端口/目录/账号/密码来自配置；
  * `protocol == SCP`：改用 `ScpClient` 递归下载；下载后复用现有清理逻辑；
  * 未知协议或异常：fallback 至全局 FTP 参数并记录告警。 |
| 5 | `hmi/src/main/java/com/siteweb/hmi/service/ComtradeFtpService.java` | 18 | 接口说明 | 若协议为 SCP，方法仍复用同名接口，无需变动 |
| 6 | `hmi/src/main/java/com/siteweb/hmi/job/ComtradeFileJob.java` | 124-136 | 调用 `comtradeFtpService.downloadLatestComtradeFiles` | 不变，因 Service 内部已兼容多协议 |
| 7 | `hmi/src/main/java/com/siteweb/hmi/runner/ComtradeFTPRunner.java` | 126-131 | 初始化下载调用 | 同上 |
| 8 | 其余 `Comtrade*` 类 | \- | 检查结果 | 本次功能仅影响文件获取流程；解析/分析/清理等逻辑与传输协议无关，无需修改。 |

---

## 3. 详细 TODO 列表
1. **数据库**
   - [ ] 在 changelog 脚本中追加 214 插入语句并提交。
2. **DTO & Service**
   - [ ] 创建 `VendorTransferCfg` DTO。
   - [ ] 为 `SystemConfigService` 添加 `getVendorTransferConfig()`（带缓存）。
3. **核心下载服务**
   - [ ] 重构 `ComtradeFtpServiceImpl`：
     - [ ] 注入 `EquipmentTemplateService`（或 Mapper）获取厂商。
     - [ ] 解析厂商转移配置，封装共用下载入口。
     - [ ] 引入 `ScpClient` 执行 SCP 下载，保持文件成对校验及清理逻辑。
4. **异常处理与日志**
   - [ ] 未配置 214 或无匹配厂商时，打印 INFO 并使用全局 FTP。
   - [ ] 遇到未知 `protocol` 抛出 `IllegalArgumentException` 并告警。
5. **测试场景建议**
   - 场景0：未配置 214 时自动回退至全局 FTP 逻辑
   - 场景1：厂商 A（FTP）下载成功，文件数量与内容校验通过
   - 场景2：厂商 B（SCP）下载成功，文件数量与内容校验通过
   - 场景3：配置未知 protocol 值 → 触发 fallback 并产生告警日志
   - 场景4：网络中断或认证失败 → 下载失败并按策略重试 / 抛出异常
   - 场景5：下载完成但 CFG/DAT 文件不成对 → 触发清理并记录错误

6. **文档与运维**
   - [ ] 更新运维手册说明 214 参数格式及新增厂商步骤。
   - [ ] 发布版本更新说明，提醒旧项目无需操作即可保持原行为。

---

## 4. Comtrade 相关文件检查
已检索 `Comtrade*` Java 文件，涉及解析、谐波、清理、管理、控制器等模块。只有 **`ComtradeFtpServiceImpl`** 涉及文件传输协议，其他类均依赖服务输出，故无需修改。

> **结论**：本方案所有改动集中于参数脚本、配置解析、下载服务及其依赖 DTO，确保最小化影响并向后兼容。