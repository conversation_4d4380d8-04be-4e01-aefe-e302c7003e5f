<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.asset.mapper.AssetDeviceMapper">
    <resultMap id="baseMap" type="com.siteweb.asset.entity.AssetDevice">
        <id column="assetDeviceId" property="assetDeviceId"/>
        <result column="assetCode" property="assetCode"/>
        <result column="assetName" property="assetName"/>
        <result column="assetCategoryId" property="assetCategoryId"/>
        <result column="brand" property="brand"/>
        <result column="model" property="model"/>
        <result column="capacityParameter" property="capacityParameter"/>
        <result column="settingPosition" property="settingPosition"/>
        <result column="serialNumber" property="serialNumber"/>
        <result column="manufactor" property="manufactor"/>
        <result column="tableName" property="tableName"/>
        <result column="assetCategoryName" property="assetCategoryName"/>
<!--        <collection-->
<!--                property="extValueConfigurationList" javaType="list"-->
<!--                select="com.siteweb.asset.mapper.ExtValueConfigurationMapper.selectByTableNameAndTablePkId"-->
<!--                column="{tableName=tableName,tablePkId=assetDeviceId}"-->
<!--        />-->
    </resultMap>

    <sql id="baseSql">
        select * from (
              select a.AssetDeviceId, SortIndex, AssetCode, AssetName, a.AssetCategoryId,
                     Brand, Model, CapacityParameter, SettingPosition, SerialNumber,
                     Manufactor, TableName, b.AssetCategoryName
              from assetdevice a
                       left join assetcategory b on a.AssetCategoryId = b.AssetCategoryId
          ) t
    </sql>
    <insert id="saveBatch" parameterType="com.siteweb.asset.entity.AssetDevice">
        insert into assetdevice(SortIndex, AssetCode, AssetName, AssetCategoryId, Brand, Model, CapacityParameter, SettingPosition, SerialNumber, Manufactor, TableName) value
        <foreach collection="assetDevices" item="item" separator=",">
            (#{item.sortIndex},#{item.assetCode},#{item.assetName},#{item.assetCategoryId},#{item.brand},#{item.model},#{item.capacityParameter},#{item.settingPosition},#{item.serialNumber},#{item.manufactor},#{item.tableName})
        </foreach>
    </insert>
    <insert id="saveBatchWithId" parameterType="com.siteweb.asset.entity.AssetDevice">
        insert into assetdevice(assetdeviceid, SortIndex, AssetCode, AssetName, AssetCategoryId, Brand, Model, CapacityParameter, SettingPosition, SerialNumber, Manufactor, TableName) value
        <foreach collection="assetDevices" item="item" separator=",">
            (#{item.assetDeviceId},#{item.sortIndex},#{item.assetCode},#{item.assetName},#{item.assetCategoryId},#{item.brand},#{item.model},#{item.capacityParameter},#{item.settingPosition},#{item.serialNumber},#{item.manufactor},#{item.tableName})
        </foreach>
    </insert>
    <select id="selectList" resultMap="baseMap">
        <include refid="baseSql"/>
          ${ew.customSqlSegment}
    </select>

    <select id="selectPage" resultMap="baseMap">
        <include refid="baseSql"/>
        ${ew.customSqlSegment}
    </select>
    <select id="findAllIdAndCode" resultType="com.siteweb.asset.entity.AssetDevice">
        SELECT AssetDeviceId,AssetCode FROM assetdevice;
    </select>
    <select id="findAllByModel" resultType="com.siteweb.asset.entity.AssetDevice">
        SELECT a.*, b.AssetCategoryName
        FROM assetdevice a
        LEFT JOIN assetcategory b ON a.AssetCategoryId = b.AssetCategoryId
        <where>
            <if test="styleName != null and styleName != ''">
                <choose>
                    <when test="styleName == '未分组'">
                        a.Model IS NULL OR TRIM(a.Model) = ''
                    </when>
                    <otherwise>
                        a.Model = #{styleName}
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <select id="getAssetDeviceByCodesAndTableName" resultType="com.siteweb.asset.entity.AssetDevice">
        SELECT AssetDeviceId, AssetCode FROM assetdevice
        <where>
            <if test="tableName != null and tableName != ''">
                TableName = #{tableName}
            </if>
            <if test="assetCodes != null and !assetCodes.isEmpty()">
                AND AssetCode IN
                <foreach collection="assetCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>