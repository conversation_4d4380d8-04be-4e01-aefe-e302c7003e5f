<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.asset.mapper.ExtFieldConfigurationMapper">
    <resultMap id="baseMap" type="com.siteweb.asset.entity.ExtFieldConfiguration">
        <id column="extId" property="extId"/>
        <result column="extTable" property="extTable"/>
        <result column="extCode" property="extCode"/>
        <result column="extName" property="extName"/>
        <result column="extDesc" property="extDesc"/>
        <result column="extOrder" property="extOrder"/>
        <result column="extNecessary" property="extNecessary"/>
        <result column="extDataType" property="extDataType"/>
        <result column="extDataSource" property="extDataSource"/>
    </resultMap>

    <select id="selectByExtId" resultMap="baseMap">
        select a.ExtId, ExtTable, ExtCode, ExtName, ExtDesc, ExtOrder, ExtNecessary, ExtDataType, ExtDataSource from extfieldconfiguration a
        where ExtId = #{extId,jdbcType=INTEGER}
    </select>

    <select id="loadExtFieldOptionData" resultType="map">
        select * from ${table}
        <if test="labelValue != null and labelValue != ''">
            where ${label} like concat('%', #{labelValue}, '%')
        </if>
        <if test="offset != null and pageSize != null">
            limit #{offset} offset #{pageSize}
        </if>
    </select>


</mapper>