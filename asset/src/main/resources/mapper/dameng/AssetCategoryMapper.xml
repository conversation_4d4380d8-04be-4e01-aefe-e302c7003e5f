<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.asset.mapper.AssetCategoryMapper">
    <select id="findByAssetCategoryName" resultType="com.siteweb.asset.entity.AssetCategory">
        SELECT * FROM assetcategory where AssetCategoryName like CONCAT('%',#{name},'%');
    </select>
    <insert id="saveBatch" parameterType="com.siteweb.asset.entity.AssetCategory">
        INSERT INTO assetcategory (assetCategoryId, assetCategoryName, remarks) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.assetCategoryId}, #{item.assetCategoryName}, #{item.remarks})
        </foreach>
    </insert>

    <insert id="syncFromEquipmentCategory">
        INSERT INTO assetcategory (AssetCategoryId, AssetCategoryName)
        SELECT di.itemid AS AssetCategoryId,
        di.ItemValue AS AssetCategoryName
        FROM tbl_dataitem di
        LEFT JOIN assetcategory ac ON di.itemid = ac.AssetCategoryId
        WHERE di.entryId = 7
        AND ac.AssetCategoryId IS NULL;
    </insert>
</mapper>