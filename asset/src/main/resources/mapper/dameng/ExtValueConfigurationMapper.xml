<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.asset.mapper.ExtValueConfigurationMapper">
    <resultMap id="baseMap" type="com.siteweb.asset.entity.ExtValueConfiguration">
        <id column="extId" property="extId"/>
        <result column="extId" property="extId"/>
        <result column="extTablePkId" property="extTablePkId"/>
        <result column="extValue" property="extValue"/>
        <result column="extTable" property="extTable"/>
        <association property="extFieldConfiguration"
                     select="com.siteweb.asset.mapper.ExtFieldConfigurationMapper.selectByExtId"
                     javaType="com.siteweb.asset.entity.ExtFieldConfiguration"
                     column="extId"
        />
    </resultMap>
    <insert id="saveBatch" parameterType="com.siteweb.asset.entity.ExtValueConfiguration">
        INSERT INTO extvalueconfiguration (ExtId, ExtTable, ExtTablePkId, ExtValue) VALUES
        <foreach collection="list" item="item" separator=",">
                (#{item.extId}, #{item.extTable}, #{item.extTablePkId}, #{item.extValue})
        </foreach>
    </insert>
    <insert id="saveBatchWithId" parameterType="com.siteweb.asset.entity.ExtValueConfiguration">
        INSERT INTO extvalueconfiguration (ExtValId, ExtId, ExtTable, ExtTablePkId, ExtValue) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.extValId},#{item.extId}, #{item.extTable}, #{item.extTablePkId}, #{item.extValue})
        </foreach>
    </insert>

    <select id="selectByTableNameAndTablePkId" resultMap="baseMap">
        select a.ExtValId, ExtId, ExtTable, ExtTablePkId, ExtValue from extvalueconfiguration a
        where a.ExtTable = #{tableName,jdbcType=VARCHAR} and ExtTablePkId = #{tablePkId,jdbcType=INTEGER}
    </select>
    <!-- 查询指定表名和表主键ID对应的扩展值配置 -->
    <select id="selectMapByTableNameAndTablePkIds" resultMap="extValueConfigurationMap">
        SELECT
        ExtValId,
        ExtId,
        ExtTable,
        ExtTablePkId,
        ExtValue
        FROM extvalueconfiguration
        WHERE ExtTable = #{tableName}
        AND ExtTablePkId IN
        <foreach collection="tablePkId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 定义 resultMap，将结果映射到对象 -->
    <resultMap id="extValueConfigurationMap" type="com.siteweb.asset.entity.ExtValueConfiguration">
        <id property="extValId" column="ExtValId"/>
        <result property="extId" column="ExtId"/>
        <result property="extTable" column="ExtTable"/>
        <result property="extTablePkId" column="ExtTablePkId"/>
        <result property="extValue" column="ExtValue"/>
    </resultMap>
</mapper>