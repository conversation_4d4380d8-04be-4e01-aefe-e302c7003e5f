package com.siteweb.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.entity.ExtValueConfiguration;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: lzy
 * @Date: 2023/3/13 18:46
 */
public interface ExtValueConfigurationMapper extends BaseMapper<ExtValueConfiguration> {

    ExtValueConfiguration selectByTableNameAndTablePkId(@Param("tableName") String tableName, @Param("tablePkId") Integer tablePkId);

    int saveBatch(@Param("list") Collection<ExtValueConfiguration> list);
    int saveBatchWithId(@Param("list") Collection<ExtValueConfiguration> list);

    List<ExtValueConfiguration> selectMapByTableNameAndTablePkIds(@Param("tableName") String tableName, @Param("tablePkId") List<Integer> tablePkIds);


}
