package com.siteweb.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.asset.entity.AssetDevice;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/10 10:22
 */
public interface AssetDeviceMapper extends BaseMapper<AssetDevice> {
    int saveBatch(@Param("assetDevices") Collection<AssetDevice> assetDevices);
    int saveBatchWithId(@Param("assetDevices") Collection<AssetDevice> assetDevices);
    List<AssetDevice> findAllIdAndCode();
    List<AssetDevice> findAllByModel(@Param("styleName") String styleName);
    List<AssetDevice> getAssetDeviceByCodesAndTableName(@Param("assetCodes") Collection<String> assetCodes, @Param("tableName") String tableName);
}
