package com.siteweb.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.asset.entity.ExtFieldConfiguration;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Author: lzy
 * @Date: 2023/3/10 13:24
 */
public interface ExtFieldConfigurationMapper extends BaseMapper<ExtFieldConfiguration> {

    ExtFieldConfiguration selectByExtId(@Param("extId") String extId);

    List<Map<String, Object>> loadExtFieldOptionData(@Param("table") String table,
                                                     @Param("label") String label,
                                                     @Param("labelValue") String labelValue,
                                                     @Param("offset") Integer offset,
                                                     @Param("pageSize") Integer pageSize);
}
