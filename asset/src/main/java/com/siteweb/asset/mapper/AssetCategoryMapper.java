package com.siteweb.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.asset.entity.AssetCategory;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AssetCategoryMapper extends BaseMapper<AssetCategory> {

    List<AssetCategory> findByAssetCategoryName(String name);
    int saveBatch(@Param("list") Collection<AssetCategory> list);
    int syncFromEquipmentCategory();
}
