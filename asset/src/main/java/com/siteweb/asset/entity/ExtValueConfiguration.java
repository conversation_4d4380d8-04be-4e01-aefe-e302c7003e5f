package com.siteweb.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 扩展字段值表
 * <AUTHOR>
 * @email 
 * @date 2019-10-23 15:17:18
 */
@Data
@TableName("extvalueconfiguration")
@NoArgsConstructor
@AllArgsConstructor
public class ExtValueConfiguration implements Serializable {

	private static final long serialVersionUID = 1905122041950251207L;
	/**
	 * 扩展字段值id
	 */
	@TableId(type = IdType.AUTO)
	private Integer extValId;
	/**
	 * 扩展字段id
	 */
	private Integer extId;
	/**
	 * 扩展关联表主键id
	 */
	private Integer extTablePkId;
	/**
	 * 扩展值
	 */
	private String extValue;

	private String extTable;

	@TableField(exist = false)
	private ExtFieldConfiguration extFieldConfiguration;

	//接收前端传过来的字段类型值（原来前端就已经传了，只是这个对象里没有接收，在此加一下字段接收一下）
	@TableField(exist = false)
	private String extDataType;

	public ExtValueConfiguration(Integer extId,String extValue,String extTable){
		this.extId=extId;
		this.extValue = extValue;
		this.extTable = extTable;
	}
	public ExtValueConfiguration(Integer extTablePkId, Integer extId,String extValue,String extTable){
		this.extTablePkId = extTablePkId;
		this.extId=extId;
		this.extValue = extValue;
		this.extTable = extTable;
	}
}
