package com.siteweb.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@TableName("assetdevice")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetDevice implements Serializable{
    private static final long serialVersionUID = 1905122041950251207L;
    /**
     * 资产id
     */
    @TableId(type = IdType.AUTO)
    private Integer assetDeviceId;
    /**
     * 序号
     */
    private Integer sortIndex;
    /**
     * 资产编号
     */
    private String assetCode;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 资产分类id
     */
    private Integer assetCategoryId;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 容量参数
     */
    private String capacityParameter;
    /**
     * 安装位置
     */
    private String settingPosition;
    /**
     * 序列号
     */
    private String serialNumber;
    /**
     * 厂家
     */
    private String manufactor;

    private String tableName;

    @TableField(exist = false)
    private String assetCategoryName;



    /** 下面字段在资产列表界面都没有用上，暂时注释 **/
    // private Integer objectId; // 资产对象id
    // private Integer objectType; // 资产对象类型
    // private Long globalResourceId;



    @TableField(exist = false)
    private List<ExtValueConfiguration> extValueConfigurationList = new ArrayList<>();

}
