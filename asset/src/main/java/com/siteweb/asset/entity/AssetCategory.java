package com.siteweb.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 资产类型
 * <AUTHOR>
 * @email
 * @date 2019-10-23 15:17:18
 */
@TableName("assetcategory")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssetCategory implements Serializable {
	private static final long serialVersionUID = 1905122041950251207L;

	@TableId(type = IdType.AUTO)
	private Integer assetCategoryId;

	/**
	 * 资产类型名称
	 */
	@TableField("AssetCategoryName")
	private String assetCategoryName;
	/**
	 * 备注
	 */
	@TableField("Remarks")
	private String remarks;
}
