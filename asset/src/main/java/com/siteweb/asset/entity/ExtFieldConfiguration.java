package com.siteweb.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 扩展字段配置表
 * @author: lzy
 * @creat: 2023/3/10 13:23
 */
@Data
@TableName("extfieldconfiguration")
@NoArgsConstructor
public class ExtFieldConfiguration {
	/**
	 * 扩展字段编号
	 */
	@TableId(type = IdType.AUTO)
	private Integer extId;
	/**
	 * 扩展字段关联表名
	 */
	private String extTable;
	/**
	 * 扩展字段编码
	 */
	private String extCode;
	/**
	 * 扩展字段名称
	 */
	private String extName;
	/**
	 * 扩展字段描述
	 */
	private String extDesc;
	/**
	 * 扩展字段序号
	 */
	private Integer extOrder;
	/**
	 * 扩展字段是否必须
	 */
	private Boolean extNecessary;
	/**
	 * 扩展字段数据类型
	 */
	private String extDataType;
	/**
	 * 扩展字段数据来源
	 */
	private String extDataSource;

}
