package com.siteweb.asset.controller;

import com.siteweb.asset.dto.AssetCategoryDTO;
import com.siteweb.asset.entity.AssetCategory;
import com.siteweb.asset.service.AssetCategoryService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * @Author: lzy
 * @Date: 2023/3/10 9:31
 */
@RequestMapping("/api")
@RestController
public class AssetCategoryController {

    @Autowired
    private AssetCategoryService assetCategoryService;

    @ApiOperation("获取所有资产类型")
    @GetMapping("/idcassetcategorys" )
    public ResponseEntity<ResponseResult> getAssetCategorys() {
        return ResponseHelper.successful(assetCategoryService.findAll());
    }

    @ApiOperation("获取单个资产类型详情")
    @GetMapping("/idcassetcategorys/{assetCategoryId}")
    public ResponseEntity<ResponseResult> getAssetCategoryById(@PathVariable("assetCategoryId") Integer assetCategoryId) {
        return Optional.ofNullable(assetCategoryService.findById(assetCategoryId))
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation("新增资产类型")
    @PostMapping(value = "/idcassetcategorys")
    public ResponseEntity<ResponseResult> createAssetCategory(@Valid @RequestBody AssetCategoryDTO assetCategoryDTO) {
        AssetCategory assetCategory = assetCategoryDTO.build();
        assetCategoryService.create(assetCategory);
        return ResponseHelper.successful(assetCategory);
    }

    @ApiOperation("修改资产类型")
    @PutMapping(value = "/idcassetcategorys")
    public ResponseEntity<ResponseResult> updateAssetCategory(@Valid @RequestBody AssetCategoryDTO assetCategoryDTO) {
        if (assetCategoryDTO.getAssetCategoryId() == null){
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        AssetCategory assetCategory = assetCategoryDTO.build();
        assetCategoryService.updateById(assetCategory);
        return ResponseHelper.successful(assetCategory);
    }

    @ApiOperation("删除单个资产类型")
    @DeleteMapping(value = "/idcassetcategorys/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAssetCategory(@PathVariable Integer id) {
        AssetCategory assetCategory = assetCategoryService.findById(id);
        if (assetCategory == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        assetCategoryService.deleteById(id);
        return ResponseHelper.successful();
    }

}
