package com.siteweb.asset.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.asset.dto.BatchImportDTO;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.service.AssetDeviceService;
import com.siteweb.asset.vo.AssetDeviceFilterVo;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.excel.util.ExportExcelUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * @Author: lzy
 * @Date: 2023/3/10 10:24
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class AssetDeviceController {

    @Autowired
    private AssetDeviceService assetDeviceService;

    @ApiOperation("新增资产")
    @PostMapping(value = "/idcassetdevices",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAssetDevice(@Valid @RequestBody AssetDevice assetDevice) {
        if (assetDevice.getAssetDeviceId() != null) {
            return ResponseHelper.failed("A new AssetDevice cannot already have an ID");
        }
        AssetDevice res = assetDeviceService.create(assetDevice);
        if(res == null)
            return ResponseHelper.failed("This Equipment Has Been Bound");
        assetDeviceService.saveOperationLog("add",res,null);
        return ResponseHelper.successful(res);
    }

    @ApiOperation("批量新增")
    @PostMapping(value = "/idcassetdevices/list",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createAssetDevice(@Valid @RequestBody BatchImportDTO batchImportDTO){
        return ResponseHelper.successful(assetDeviceService.createBatch(batchImportDTO));
    }

    @ApiOperation("条件查询资产")
    @PostMapping(value = "/idcassetdevices/search",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findAssetDeviceByParam(@Valid @RequestBody AssetDevice assetDevice){
        return ResponseHelper.successful(assetDeviceService.findAssetDeviceByParam(assetDevice));
    }

    @ApiOperation("更新资产")
    @PutMapping(value = "/idcassetdevices", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAssetDevice(@Valid @RequestBody AssetDevice assetDevice) {
        if (assetDevice.getAssetDeviceId() == null) {
            return createAssetDevice(assetDevice);
        }
        AssetDevice res = assetDeviceService.updateById(assetDevice);
        if(ObjectUtil.isNotNull(res))
            return ResponseHelper.successful(res);
        return ResponseHelper.failed("This Equipment Has Been Bound");
    }

    @ApiOperation("获取列表")
    @GetMapping(value = "/idcassetdevices",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAssetDevices() {
        return ResponseHelper.successful(assetDeviceService.findAll());
    }

    @ApiOperation("获取分页列表")
    @GetMapping(value = "/idcassetdevices/page",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllAssetDevices(Pageable pageable, AssetDeviceFilterVo filterVo) {
        return ResponseHelper.successful(assetDeviceService.page(new Page<>(pageable.getPageNumber(), pageable.getPageSize()), filterVo));
    }

    @ApiOperation(value = "导出全部资产列表")
    @PostMapping("/idcassetdevices/export")
    public ResponseEntity<Resource> exportShield(){
        return ExportExcelUtils.exportExcel(assetDeviceService::exportAllAssetDevices);
    }


    @ApiOperation("获取单个资产")
    @GetMapping(value = "/idcassetdevices/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAssetDevice(@PathVariable Integer id) {
        return Optional.ofNullable(assetDeviceService.findById(id))
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation("删除资产")
    @DeleteMapping(value = "/idcassetdevices/{id}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteAssetDevice(@PathVariable Integer id) {
        if(assetDeviceService.deleteById(id)){
            AssetDevice assetDevice = new AssetDevice();
            assetDevice.setAssetDeviceId(id);
            assetDeviceService.saveOperationLog("delete",assetDevice,null);
        }
        return ResponseHelper.successful();
    }

    @ApiOperation("批量删除资产")
    @PostMapping(value = "/batchdelete",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteAssetDevice(@RequestBody List<Integer> ids) {
        assetDeviceService.batchDeleteByIds(ids);
        return ResponseHelper.successful();
    }

    @ApiOperation("从设备同步资产")
    @GetMapping(value = "/idcassetdevices/equsync",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> equsync() {
        String result = assetDeviceService.syncFromEquipment();
        if (ObjectUtil.isNull(result)) {
            return ResponseHelper.failed("同步失败，请联系管理员重置资产扩展字段配置");
        }
        return ResponseHelper.successful(result);
    }

}
