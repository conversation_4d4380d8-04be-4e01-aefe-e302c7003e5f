package com.siteweb.asset.controller;

import com.siteweb.asset.dto.ExtFieldConfigurationDTO;
import com.siteweb.asset.entity.ExtFieldConfiguration;
import com.siteweb.asset.service.ExtFieldConfigurationService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * @Author: lzy
 * @Date: 2023/3/10 13:25
 */
@Api(tags = "资产扩展字段控制器")
@RestController
@RequestMapping("/api" )
public class ExtFieldConfigurationController {

    @Autowired
    private ExtFieldConfigurationService extFieldConfigurationService;

    @ApiOperation("获取单个扩展字段配置")
    @GetMapping("/extfieldconfigurations/{extId}")
    public ResponseEntity<ResponseResult> getExtFieldConfigurationById(@PathVariable("extId") Integer extId) {
        return Optional.ofNullable(extFieldConfigurationService.findById(extId))
                .map(ResponseHelper::successful)
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }


    @GetMapping(value = "/extfieldconfigurations", params = {"extTable", "extTablePkId"})
    public ResponseEntity<ResponseResult> getExtFieldConfigurationWithExtValueByExtTableAndExtTablePkId(@RequestParam String extTable, @RequestParam Integer extTablePkId) {
        return ResponseHelper.successful(extFieldConfigurationService.findByExtTableWithExtValue(extTable, extTablePkId));
    }

    @ApiOperation("查询所有")
    @GetMapping("/extfieldconfigurations" )
    public ResponseEntity<ResponseResult> getExtFieldConfigurations() {
        return ResponseHelper.successful(extFieldConfigurationService.findAll());
    }

    @ApiOperation("新增")
    @PostMapping(value = "/extfieldconfigurations")
    public ResponseEntity<ResponseResult> createExtFieldConfiguration(@Valid @RequestBody ExtFieldConfigurationDTO extFieldConfigurationDTO) {
        return ResponseHelper.successful(extFieldConfigurationService.create(extFieldConfigurationDTO.build()));
    }

    @ApiOperation("更新")
    @PutMapping(value = "/extfieldconfigurations")
    public ResponseEntity<ResponseResult> updateExtFieldConfiguration(@Valid @RequestBody ExtFieldConfigurationDTO extFieldConfigurationDTO) {
        if (extFieldConfigurationDTO.getExtId() == null){
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(extFieldConfigurationService.updateById(extFieldConfigurationDTO.build()));
    }

    @ApiOperation("删除单个")
    @DeleteMapping(value = "/extfieldconfigurations/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteExtFieldConfiguration(@PathVariable Integer id) {
        ExtFieldConfiguration extFieldConfiguration = extFieldConfigurationService.findById(id);
        if (extFieldConfiguration == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        extFieldConfigurationService.deleteById(id);
        return ResponseHelper.successful();
    }

    @ApiOperation("加载扩展字段选项数据")
    @GetMapping(value = "/loadextfieldoptiondata")
    public ResponseEntity<ResponseResult> loadExtFieldOptionData(@RequestParam Integer extFieldId,
                                                                 @RequestParam(required = false) String labelValue,
                                                                 @RequestParam(required = false) Integer pageNumber,
                                                                 @RequestParam(required = false) Integer pageSize) {
        return ResponseHelper.successful(extFieldConfigurationService.loadExtFieldOptionData(extFieldId, labelValue, pageNumber, pageSize));
    }

    @ApiOperation("获取扩展字段选项表label，扩展字段类型为table，数据库存储value，页面需要回显label")
    @GetMapping(value = "/getextfieldvaluelabeldatabyextvalid")
    public ResponseEntity<ResponseResult> getExtFieldValueLabelDataByExtValId(@RequestParam Integer extValId) {
        return ResponseHelper.successful(extFieldConfigurationService.getExtFieldValueLabelDataByExtValId(extValId));
    }

}
