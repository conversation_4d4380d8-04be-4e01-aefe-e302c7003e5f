package com.siteweb.asset.dto;

import com.siteweb.asset.entity.ExtFieldConfiguration;
import com.siteweb.asset.entity.ExtValueConfiguration;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO
 *
 * <AUTHOR>
 * @email 
 * @date 2019-10-23 15:17:18
 */
@Data
@NoArgsConstructor
public class ExtFieldConfigurationDTO {
    /**
     * 扩展字段编号
     */
    private Integer extId;

    /**
     * 扩展字段关联表名
     */
    private String extTable;

    /**
     * 扩展字段编码
     */
    private String extCode;

    /**
     * 扩展字段名称
     */
    private String extName;

    /**
     * 扩展字段描述
     */
    private String extDesc;

    /**
     * 扩展字段序号
     */
    private Integer extOrder;

    /**
     * 扩展字段是否必须
     */
    private Boolean extNecessary;

    /**
     * 扩展字段数据类型
     */
    private String extDataType;

    /**
     * 扩展字段数据来源
     */
    private String extDataSource;

	public ExtFieldConfiguration build() {
        ExtFieldConfiguration extFieldConfiguration = new ExtFieldConfiguration();
        extFieldConfiguration.setExtId (this.extId);
        extFieldConfiguration.setExtTable (this.extTable);
        extFieldConfiguration.setExtCode (this.extCode);
        extFieldConfiguration.setExtName (this.extName);
        extFieldConfiguration.setExtDesc (this.extDesc);
        extFieldConfiguration.setExtOrder (this.extOrder);
        extFieldConfiguration.setExtNecessary (this.extNecessary);
        extFieldConfiguration.setExtDataType (this.extDataType);
        extFieldConfiguration.setExtDataSource (this.extDataSource);
        return extFieldConfiguration;
	}

	private ExtValueConfiguration extValueConfiguration;
}
