package com.siteweb.asset.dto;

import com.siteweb.asset.entity.AssetCategory;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AssetCategoryDTO {
    /**
     * 资产类型id
     */
    private Integer assetCategoryId;

    /**
     * 资产类型名称
     */
    private String assetCategoryName;

    /**
     * 备注
     */
    private String remarks;

	public AssetCategory build() {
        AssetCategory assetCategory = new AssetCategory();
        assetCategory.setAssetCategoryId (this.assetCategoryId);
        assetCategory.setAssetCategoryName (this.assetCategoryName);
        assetCategory.setRemarks (this.remarks);
        return assetCategory;
	}
}
