package com.siteweb.asset.dto;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.asset.entity.AssetCategory;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.entity.ExtValueConfiguration;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Data
@NoArgsConstructor
public class AssetDeviceDTO {
    private Integer assetDeviceId;//资产id

    private Integer objectId;//资产对象id
    private Integer objectType;//资产对象类型
    private Long globalResourceId;
    private Integer sortIndex;//序号
    private String assetCode;//资产编号
    private String assetName;//资产名称
    private Integer assetCategoryId;//资产分类id
    private String brand;//品牌
    private String model;//型号
    private String capacityParameter;//容量参数
    private String settingPosition;//安装位置
    private String serialNumber;//序列号
    private String manufactor;//厂家
    private String tableName;
    private String assetCategoryName;

    private AssetCategory assetCategory;

    private List<ExtValueConfiguration> extValueConfigurationList;

    public AssetDevice build() {
        AssetDevice assetDevice = new AssetDevice();
        BeanUtils.copyProperties(this, assetDevice);
        if (CollUtil.isNotEmpty(assetDevice.getExtValueConfigurationList())) {
            for(ExtValueConfiguration extValueConfiguration : assetDevice.getExtValueConfigurationList()){
                extValueConfiguration.setExtTable(assetDevice.getTableName());
                extValueConfiguration.setExtTablePkId(assetDevice.getAssetDeviceId());
            }
        }
        return assetDevice;
    }

}
