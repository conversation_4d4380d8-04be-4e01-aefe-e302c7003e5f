package com.siteweb.asset.dto;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.entity.ExtValueConfiguration;
import com.siteweb.asset.vo.ByteDanceAssetExtFieldEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 设备台账DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssetLedgerDTO {
    /**
     * 设备Id
     */
    private String equipmentId;

    /**
     * 设备名
     */
    private String equipmentName;

    /**
     * 设备类型
     */
    private Integer equipmentCategory;

    /**
     * 设备类型名
     */
    private String equipmentCategoryName;

    /**
     * 厂商
     */
    private String vendor;

    /**
     * 设备型号
     */
    private String equipmentStyle;


    /**
     * 层级路径
     */
    private String levelOfPath;

    /**
     * 设备位置
     */
    private String equipmentPosition;
    /**
     * 采购时间
     */
    private String buyDate;

    /**
     * 设备条码
     */
    private String equipmentSn;
    /**
     * 厂家联系人
     */
    private String manufacturerContactPerson;

    /**
     * 厂家联系方式
     */
    private String manufacturerContactNumber;

    /**
     * 设备出厂时间
     */
    private String equipmentManufactureDate;

    /**
     * 设备质保时间
     */
    private String warrantyStartDate;

    /**
     * 质保到期时间
     */
    private String warrantyEndDate;

    /**
     * 上次保养时间
     */
    private String lastMaintenanceDate;

    /**
     * 下次保养时间
     */
    private String nextMaintenanceDate;

    /**
     * 资产归属部门名称
     */
    private String assetDepartmentName;

    /**
     * 资产负责人名称
     */
    private String assetResponsiblePersonName;

    /**
     * 负责人联系方式
     */
    private String responsiblePersonContact;

    public AssetLedgerDTO (AssetDevice assetDevice, List<ExtValueConfiguration> valueList){
        Map<Integer, String> valueMap = new HashMap<>();
        if (CollUtil.isNotEmpty(valueList)){
            valueMap = valueList.stream().collect(Collectors.toMap(ExtValueConfiguration::getExtId, ExtValueConfiguration::getExtValue));
        }
        this.equipmentId = assetDevice.getAssetCode();
        this.equipmentName = assetDevice.getAssetName();
        this.equipmentCategory = assetDevice.getAssetCategoryId();
        this.equipmentCategoryName = assetDevice.getAssetCategoryName();
        this.vendor = assetDevice.getManufactor();
        this.equipmentStyle = assetDevice.getModel();
        this.levelOfPath = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.LEVEL_OF_PATH_NOSHOW.getExtId(),null);
        this.equipmentPosition = assetDevice.getSettingPosition();
        this.buyDate = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.PURCHASE_DATE.getExtId(),null);
        this.equipmentSn = assetDevice.getSerialNumber();
        this.manufacturerContactPerson = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.MANUFACTURER_CONTACT.getExtId(),null);
        this.manufacturerContactNumber = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.MANUFACTURER_CONTACT_INFO.getExtId(),null);
        this.equipmentManufactureDate = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.MANUFACTURE_DATE.getExtId(),null);
        this.warrantyStartDate = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.WARRANTY_PERIOD.getExtId(),null);
        this.warrantyEndDate = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.WARRANTY_EXPIRY_DATE.getExtId(),null);
        this.lastMaintenanceDate = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.LAST_MAINTENANCE_DATE.getExtId(),null);
        this.nextMaintenanceDate = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.NEXT_MAINTENANCE_DATE.getExtId(),null);
        this.assetDepartmentName = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.ASSET_BELONGING_DEPARTMENT.getExtId(),null);
        this.assetResponsiblePersonName = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.ASSET_OWNER.getExtId(),null);
        this.responsiblePersonContact = valueMap.getOrDefault(ByteDanceAssetExtFieldEnum.OWNER_CONTACT_INFO.getExtId(),null);
    }

}
