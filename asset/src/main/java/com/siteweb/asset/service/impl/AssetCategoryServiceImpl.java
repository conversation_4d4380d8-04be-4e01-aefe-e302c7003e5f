package com.siteweb.asset.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.asset.entity.AssetCategory;
import com.siteweb.asset.mapper.AssetCategoryMapper;
import com.siteweb.asset.service.AssetCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/10 9:30
 */
@Service
public class AssetCategoryServiceImpl implements AssetCategoryService {

    @Autowired
    AssetCategoryMapper assetCategoryMapper;

    @Override
    public List<AssetCategory> findAll() {
        return assetCategoryMapper.selectList(Wrappers.lambdaQuery());
    }

    @Override
    public AssetCategory findById(Integer assetCategoryId) {
        return assetCategoryMapper.selectById(assetCategoryId);
    }

    @Override
    public AssetCategory create(AssetCategory assetCategory) {
        return assetCategoryMapper.insert(assetCategory) > 0 ? assetCategory : null;
    }

    @Override
    public AssetCategory updateById(AssetCategory assetCategory) {
        return assetCategoryMapper.updateById(assetCategory) > 0 ? assetCategory : null;
    }

    @Override
    public void deleteById(Integer id) {
        assetCategoryMapper.deleteById(id);
    }
}
