package com.siteweb.asset.service;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.asset.dto.BatchImportDTO;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.vo.AssetDeviceFilterVo;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/10 10:22
 */
public interface AssetDeviceService {


    /**
     * 批量新增
     * @param batchImportDTO
     * @return
     */
    List<AssetDevice> createBatch(BatchImportDTO batchImportDTO);

    /**
     * 条件查询资产
     * @param assetDevice
     * @return
     */
    List<AssetDevice> findAssetDeviceByParam(AssetDevice assetDevice);

    /**
     * 新增资产
     * @param assetDevice
     * @return
     */
    AssetDevice create(AssetDevice assetDevice);

    /**
     * 更新资产
     * @param assetDevice
     * @return
     */
    AssetDevice updateById(AssetDevice assetDevice);

    /**
     * 查询所有资产
     * @return
     */
    List<AssetDevice> findAll();

    /**
     * 查询资产分页
     * @param objectPage
     * @return
     */
    IPage<AssetDevice> page(Page<AssetDevice> objectPage, AssetDeviceFilterVo filterVo);

    /**
     * 获取单个资产
     * @param id
     * @return
     */
    AssetDevice findById(Integer id);

    /**
     * 删除资产
     * @param id
     * @return
     */
    boolean deleteById(Integer id);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    void batchDeleteByIds(List<Integer> ids);
    void saveOperationLog(String operateType,AssetDevice assetDevice,AssetDevice oldAssetDevice);

    /**
     * 字节定制-将设备同步导入到资产中
     */
    String syncFromEquipment();

    /**
     * 导出资产列表
     */
    ExcelWriter exportAllAssetDevices();
}
