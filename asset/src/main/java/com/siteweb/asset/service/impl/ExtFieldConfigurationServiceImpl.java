package com.siteweb.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.asset.dto.ExtFieldConfigurationDTO;
import com.siteweb.asset.entity.ExtFieldConfiguration;
import com.siteweb.asset.entity.ExtValueConfiguration;
import com.siteweb.asset.mapper.ExtFieldConfigurationMapper;
import com.siteweb.asset.service.ExtFieldConfigurationService;
import com.siteweb.asset.service.ExtValueConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author: lzy
 * @Date: 2023/3/10 13:24
 */
@Slf4j
@Service
public class ExtFieldConfigurationServiceImpl implements ExtFieldConfigurationService {

    @Autowired
    ExtFieldConfigurationMapper extFieldConfigurationMapper;
    @Autowired
    ExtValueConfigurationService extValueConfigurationService;
    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    public List<ExtFieldConfiguration> findByExtTable(String extTable) {
        LambdaQueryWrapper<ExtFieldConfiguration> wrapper = Wrappers.lambdaQuery(ExtFieldConfiguration.class);
        wrapper.eq(ExtFieldConfiguration::getExtTable, extTable);
        return extFieldConfigurationMapper.selectList(wrapper);
    }

    @Override
    public List<ExtFieldConfigurationDTO> findByExtTableWithExtValue(String extTable, Integer extTablePkId) {
        List<ExtFieldConfiguration> extFieldConfigurations = this.findByExtTable(extTable);
        List<ExtFieldConfigurationDTO> extFieldConfigurationDTOList = new ArrayList<>(extFieldConfigurations.size());
        for(ExtFieldConfiguration extFieldConfiguration : extFieldConfigurations){
            ExtFieldConfigurationDTO extFieldConfigurationDTO = new ExtFieldConfigurationDTO();
            BeanUtils.copyProperties(extFieldConfiguration, extFieldConfigurationDTO);
            ExtValueConfiguration extValueConfiguration = extValueConfigurationService.findByExtTablePkIdAndExtTableAndExtId(extTablePkId, extFieldConfiguration.getExtTable(), extFieldConfiguration.getExtId());
            extFieldConfigurationDTO.setExtValueConfiguration(extValueConfiguration);
            extFieldConfigurationDTOList.add(extFieldConfigurationDTO);
        }
        return extFieldConfigurationDTOList;
    }

    @Override
    public ExtFieldConfiguration findById(Integer extId) {
        return extFieldConfigurationMapper.selectById(extId);
    }

    @Override
    public List<ExtFieldConfiguration> findAll() {
        return extFieldConfigurationMapper.selectList(Wrappers.lambdaQuery());
    }

    @Override
    public ExtFieldConfiguration create(ExtFieldConfiguration extFieldConfiguration) {
        return extFieldConfigurationMapper.insert(extFieldConfiguration) > 0 ? extFieldConfiguration : null;
    }

    @Override
    public ExtFieldConfiguration updateById(ExtFieldConfiguration extFieldConfiguration) {
        return extFieldConfigurationMapper.updateById(extFieldConfiguration) > 0 ? extFieldConfiguration : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        extFieldConfigurationMapper.deleteById(id);
        extValueConfigurationService.deleteByExtId(id);
    }

    @Override
    public void deleteByIds(Collection<Integer> extFieldIds) {
        extFieldConfigurationMapper.deleteBatchIds(extFieldIds);
    }

    @Override
    public List<ExtFieldConfiguration> findByExtTableWithExtIds(String extTable, Collection<Integer> extIds) {
        if (CollUtil.isEmpty(extIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ExtFieldConfiguration> wrapper = Wrappers.lambdaQuery(ExtFieldConfiguration.class);
        wrapper.eq(ExtFieldConfiguration::getExtTable, extTable);
        wrapper.in(ExtFieldConfiguration::getExtId, extIds);
        return extFieldConfigurationMapper.selectList(wrapper);
    }

    @Override
    public Map<Integer, Map<String, String>> findExtFieldTableDataMap() {
        List<ExtFieldConfiguration> extFieldConfigurationTableList = extFieldConfigurationMapper.selectList(Wrappers.lambdaQuery(ExtFieldConfiguration.class).eq(ExtFieldConfiguration::getExtDataType, "table"));
        if (CollUtil.isEmpty(extFieldConfigurationTableList)) {
            return Collections.emptyMap();
        }
        Map<Integer, Map<String, String>> result = new HashMap<>();
        for (ExtFieldConfiguration extFieldConfiguration : extFieldConfigurationTableList) {
            String extDataSource = extFieldConfiguration.getExtDataSource();
            if (CharSequenceUtil.isEmpty(extDataSource)) {
                continue;
            }
            JSONObject extDataSourceConfig = JSONUtil.parseObj(extDataSource);
            String table = extDataSourceConfig.getStr("table");
            String label = extDataSourceConfig.getStr("label");
            String value = extDataSourceConfig.getStr("value");
            if (CharSequenceUtil.hasEmpty(table, label, value)) {
                continue;
            }
            Map<String, String> data = new HashMap<>();
            try {
                jdbcTemplate.query("select " + label + "," + value + " from " + table, (rs, rowNum) -> {
                    data.put(rs.getString(value), rs.getString(label));
                    return null;
                });
            }catch (Exception e) {
                log.error("资产扩展扩展字段，{}表或字段`{}`，`{}`不存在", table, label, value);
            }
            result.put(extFieldConfiguration.getExtId(), data);
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> loadExtFieldOptionData(Integer extFieldId, String labelValue, Integer pageNumber, Integer pageSize) {
        ExtFieldConfiguration extFieldConfiguration = this.findById(extFieldId);
        String extDataSource = extFieldConfiguration.getExtDataSource();
        if (CharSequenceUtil.isEmpty(extDataSource)) {
            return Collections.emptyList();
        }
        JSONObject extDataSourceConfig = JSONUtil.parseObj(extDataSource);
        String table = extDataSourceConfig.getStr("table");
        if (CharSequenceUtil.hasEmpty(table)) {
            return Collections.emptyList();
        }
        String label = extDataSourceConfig.getStr("label");
        int offset = 0;
        if (Objects.nonNull(pageNumber)) {
            offset = (pageNumber - 1) * pageSize;
        }
        try {
            return extFieldConfigurationMapper.loadExtFieldOptionData(table, label, labelValue, offset, pageSize);
        }catch (Exception e) {
            log.error("资产扩展扩展字段，{}表或字段`{}`不存在", table, label);
        }
        return Collections.emptyList();
    }

    @Override
    public String getExtFieldValueLabelDataByExtValId(Integer extValId) {
        ExtValueConfiguration extValueConfiguration = extValueConfigurationService.selectById(extValId);
        if (Objects.isNull(extValueConfiguration)) {
            return null;
        }
        ExtFieldConfiguration extFieldConfiguration = extFieldConfigurationMapper.selectById(extValueConfiguration.getExtId());;
        if (Objects.isNull(extFieldConfiguration)) {
            return null;
        }
        if (CharSequenceUtil.isEmpty(extFieldConfiguration.getExtDataSource())) {
            return null;
        }
        JSONObject extDataSourceConfig = JSONUtil.parseObj(extFieldConfiguration.getExtDataSource());
        String table = extDataSourceConfig.getStr("table");
        if (CharSequenceUtil.hasEmpty(table)) {
            return null;
        }
        String label = extDataSourceConfig.getStr("label");
        String value = extDataSourceConfig.getStr("value");
        try {
            return jdbcTemplate.queryForObject(CharSequenceUtil.format("select {} from {} where {} = ?", label, table, value), String.class, extValueConfiguration.getExtValue());
        }catch (Exception e) {
            log.error("资产扩展扩展字段，{}表或字段`{}`，`{}`不存在", table, label, value);
        }
        return null;
    }
}
