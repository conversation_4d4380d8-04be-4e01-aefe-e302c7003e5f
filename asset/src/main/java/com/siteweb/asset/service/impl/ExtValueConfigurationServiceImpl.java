package com.siteweb.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.asset.entity.ExtValueConfiguration;
import com.siteweb.asset.mapper.ExtValueConfigurationMapper;
import com.siteweb.asset.service.ExtFieldConfigurationService;
import com.siteweb.asset.service.ExtValueConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/13 18:46
 */
@Service
public class ExtValueConfigurationServiceImpl implements ExtValueConfigurationService {

    @Autowired
    ExtValueConfigurationMapper extValueConfigurationMapper;
    @Autowired
    @Lazy
    ExtFieldConfigurationService extFieldConfigurationService;

    @Override
    public ExtValueConfiguration selectById(Integer extValId) {
        return extValueConfigurationMapper.selectById(extValId);
    }

    @Override
    public ExtValueConfiguration findByExtTablePkIdAndExtTableAndExtId(Integer extTablePkId, String extTable, Integer extId) {
        LambdaQueryWrapper<ExtValueConfiguration> wrapper = Wrappers.lambdaQuery(ExtValueConfiguration.class);
        wrapper.eq(ExtValueConfiguration::getExtTablePkId, extTablePkId);
        wrapper.eq(ExtValueConfiguration::getExtTable, extTable);
        wrapper.eq(ExtValueConfiguration::getExtId, extId);
        return extValueConfigurationMapper.selectOne(wrapper);
    }

    @Override
    public List<ExtValueConfiguration> findByExtTablePkIdsAndExtTable(Collection<Integer> extTablePkIds, String extTable) {
        if (CollUtil.isEmpty(extTablePkIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ExtValueConfiguration> wrapper = Wrappers.lambdaQuery(ExtValueConfiguration.class);
        wrapper.in(ExtValueConfiguration::getExtTablePkId, extTablePkIds);
        wrapper.eq(ExtValueConfiguration::getExtTable, extTable);
        return extValueConfigurationMapper.selectList(wrapper);
    }

    @Override
    public ExtValueConfiguration create(ExtValueConfiguration extValueConfiguration) {
        return extValueConfigurationMapper.insert(extValueConfiguration) > 0 ? extValueConfiguration : null;
    }

    @Override
    public void updateById(ExtValueConfiguration extValueConfiguration) {
        LambdaUpdateWrapper<ExtValueConfiguration> wrapper = Wrappers.lambdaUpdate(ExtValueConfiguration.class);
        wrapper.eq(ExtValueConfiguration::getExtValId, extValueConfiguration.getExtValId());
        wrapper.set(ExtValueConfiguration::getExtValue, extValueConfiguration.getExtValue());
        extValueConfigurationMapper.update(wrapper);
    }

    @Override
    public void deleteByExtId(Integer id) {
        LambdaQueryWrapper<ExtValueConfiguration> wrapper = Wrappers.lambdaQuery(ExtValueConfiguration.class);
        wrapper.eq(ExtValueConfiguration::getExtId, id);
        extValueConfigurationMapper.delete(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByExtTableAndExtTablePkId(String tableName, Integer id) {
        LambdaQueryWrapper<ExtValueConfiguration> wrapper = Wrappers.lambdaQuery(ExtValueConfiguration.class);
        wrapper.eq(ExtValueConfiguration::getExtTable, tableName);
        wrapper.eq(ExtValueConfiguration::getExtTablePkId, id);
        List<ExtValueConfiguration> extValueConfigurationList = extValueConfigurationMapper.selectList(wrapper);
        if (CollUtil.isEmpty(extValueConfigurationList)) {
            return;
        }
        List<Integer> valueIds = extValueConfigurationList.stream().map(ExtValueConfiguration::getExtValId).toList();
        extValueConfigurationMapper.deleteBatchIds(valueIds);
    }
}
