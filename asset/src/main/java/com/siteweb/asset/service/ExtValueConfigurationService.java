package com.siteweb.asset.service;

import com.siteweb.asset.entity.ExtValueConfiguration;

import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/13 18:45
 */
public interface ExtValueConfigurationService {
    ExtValueConfiguration selectById(Integer extValId);

    ExtValueConfiguration findByExtTablePkIdAndExtTableAndExtId(Integer extTablePkId, String extTable, Integer extId);

    List<ExtValueConfiguration> findByExtTablePkIdsAndExtTable(Collection<Integer> extTablePkIds, String extTable);

    ExtValueConfiguration create(ExtValueConfiguration extValueConfiguration);

    void updateById(ExtValueConfiguration extValueConfiguration);

    void deleteByExtId(Integer id);

    void deleteByExtTableAndExtTablePkId(String tableName, Integer id);
}
