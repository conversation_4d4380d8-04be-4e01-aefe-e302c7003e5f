package com.siteweb.asset.vo;


/**
 * <AUTHOR>
 * @date 2025-02-25
 */
public enum ByteDanceAssetExtFieldEnum {
    MANUFACTURER_CONTACT(1, "ManufacturerContact"),
    MANUFACTURER_CONTACT_INFO(2, "ManufacturerContactInfo"),
    MANUFACTURE_DATE(3, "ManufactureDate"),
    PURCHASE_DATE(4, "PurchaseDate"),
    WARRANTY_PERIOD(5, "WarrantyPeriod"),
    WARRANTY_EXPIRY_DATE(6, "WarrantyExpiryDate"),
    LAST_MAINTENANCE_DATE(7, "LastMaintenanceDate"),
    NEXT_MAINTENANCE_DATE(8, "NextMaintenanceDate"),
    ASSET_BELONGING_DEPARTMENT(9, "AssetBelongingDepartment"),
    ASSET_OWNER(10, "AssetOwner"),
    OWNER_CONTACT_INFO(11, "OwnerContactInfo"),
    LEVEL_OF_PATH_NOSHOW(12, "LevelOfPath_noshow");

    private final int extId;
    private final String extCode;

    ByteDanceAssetExtFieldEnum(int extId, String extCode) {
        this.extId = extId;
        this.extCode = extCode;
    }

    public int getExtId() {
        return extId;
    }

    public String getExtCode() {
        return extCode;
    }

    public static ByteDanceAssetExtFieldEnum getByExtCode(String extCode) {
        for (ByteDanceAssetExtFieldEnum field : values()) {
            if (field.getExtCode().equals(extCode)) {
                return field;
            }
        }
        return null;
    }
}
