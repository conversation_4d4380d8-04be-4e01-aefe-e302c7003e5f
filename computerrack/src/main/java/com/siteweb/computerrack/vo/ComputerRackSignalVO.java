package com.siteweb.computerrack.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComputerRackSignalVO {
    /**
     * 机架id
     */
    private Integer computerRackId;
    /**
     * 机架名称
     */
    private String computerRackName;
    /**
     * 机架编号
     */
    private String computerRackNumber;
    /**
     * 机架位置
     */
    private String computerRackPosition;
    /**
     * 开通率表达式 绑定设备id.信号id 支持表达式
     */
    private String openExpression;
    /**
     * 功率表达式 绑定设备id.信号id 支持表达式
     */
    private String powerExpression;

    /**
     * 开通率表达式
     */
    private String openExpressionStr;
    /**
     * 功率表达式
     */
    private String powerExpressionStr;

}