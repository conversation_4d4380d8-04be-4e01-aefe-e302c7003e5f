package com.siteweb.computerrack.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.computerrack.dto.ComputerRackEquipmentDTO;
import com.siteweb.monitoring.dto.EquipmentSimpleDTO;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import java.util.List;

@Data
public class ComputerRackEquipmentVO {
    private Integer computerRackId;
    private String computerRackName;
    private String computerRackNumber;
    @JsonProperty("computerRackPosition")
    private String position;
    private List<EquipmentSimpleDTO> equipments;
    public ComputerRackEquipmentVO (ComputerRackEquipmentDTO r) {
        BeanUtils.copyProperties(r, this, "equipmentId");
    }
}