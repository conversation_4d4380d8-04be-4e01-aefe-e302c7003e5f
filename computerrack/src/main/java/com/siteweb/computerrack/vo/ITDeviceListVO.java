package com.siteweb.computerrack.vo;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.entity.ITDeviceModel;
import com.siteweb.computerrack.entity.UTag;
import com.siteweb.monitoring.enumeration.SourceType;
import lombok.Data;

import java.util.Date;

@Data
public class ITDeviceListVO {

    private Integer iTDeviceId;//IT设备ID

    /**
     * IT设备名称
     */
    private String iTDeviceName;
    /**
     * 属于哪个IT设备模型
     */
    private Integer iTDeviceModelId;
    /**
     * 属于哪个机架
     */
    @TableField(value = "computerRackId", updateStrategy = FieldStrategy.IGNORED)
    private Integer computerRackId;
    /**
     * 客户名
     */
    private String customer;
    /**
     * 业务
     */
    private String business;
    /**
     * 购买日期
     */
    private Date purchaseDate;
    /**
     * 启动日期
     */
    private Date launchDate;

    /**
     * 起始U位
     */
    private Integer uIndex;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 备注
     */
    private String remark;

    public ITDeviceListVO(ITDevice e) {
        BeanUtil.copyProperties(e, this);
    }

    public Integer getObjectId() {
        return iTDeviceId;
    }

    public Integer getObjectType() {
        return SourceType.ITDEVICE.value();
    }


    public Integer getUnitHeight() {
        if (itDeviceModel == null) return 0;
        return itDeviceModel.getUnitHeight();
    }

    public String getModelName(){
        if (itDeviceModel == null) return null;
        return itDeviceModel.getITDeviceModelName();
    }

    /**
     * 属于哪个机架
     */
    private String computerRackName;

    /**
     * it设备模型
     */
    private ITDeviceModel itDeviceModel;

    private UTag uTag;



}
