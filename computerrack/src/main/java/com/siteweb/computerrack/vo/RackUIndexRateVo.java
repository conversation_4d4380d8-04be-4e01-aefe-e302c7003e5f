package com.siteweb.computerrack.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 机架u位使用率
 * <AUTHOR>
 * @date 2022/06/14
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RackUIndexRateVo {
    /**
     * 机架名称
     */
    private String computerRackName;
    /**
     * 资源位置
     */
    private String position;
    /**
     * 额定功率
     */
    private Integer ratedPower;
    /**
     * 用电量
     */
    private Integer electricPowerConsumption;

    /**
     * u位使用率
     */
    private Double uIndexPercent;
}
