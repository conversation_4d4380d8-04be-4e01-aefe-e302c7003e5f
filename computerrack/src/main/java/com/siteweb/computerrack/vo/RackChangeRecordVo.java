package com.siteweb.computerrack.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 机架变更记录
 * <AUTHOR>
 * @date 2022/06/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RackChangeRecordVo {
    /**
     * 机架位置
     */
    private String position;
    /**
     * 机架名称
     */
    private String computerRackName;
    /**
     * IT设备名称
     */
    private String iTDeviceName;
    /**
     * U位
     */
    private Integer uIndex;
    /**
     * 上下架状态 1上架  2下架
     */
    private Integer operateState;

    /**
     * 客户
     */
    private String customer;

    /**
     * 业务
     */
    private String business;

    /**
     * 购买日期
     */
    private Date purchaseDate;

    /**
     * 变更时间/操作时间
     */
    private Date operateTime;
}
