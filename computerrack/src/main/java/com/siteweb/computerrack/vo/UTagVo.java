package com.siteweb.computerrack.vo;

import lombok.Data;

import java.util.Date;

/**
 * tag展示列表
 * <AUTHOR>
 * @date 2022/06/10
 */
@Data
public class UTagVo {
    /**
     * 标签id
     */
    private Integer uTagId;
    /**
     * 标签唯一标识码
     */
    private String tagValue;
    /**
     * IT设备名称
     */
    private String iTDeviceName;
    /**
     * 机架名称
     */
    private String computerRackName;
    /**
     * 资源位置
     */
    private String position;
    /**
     * IT设备模型名称
     */
    private String iTDeviceModelName;
    /**
     * 绑定日期
     */
    private Date bindTime;
    /**
     * 上架u位
     */
    private Integer uPosition;
    /**
     * 设备名称
     */
    private String equipmentName;
    public Integer getuTagId() {
        return uTagId;
    }
}
