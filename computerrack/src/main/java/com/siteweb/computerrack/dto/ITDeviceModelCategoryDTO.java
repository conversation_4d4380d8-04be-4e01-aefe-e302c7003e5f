package com.siteweb.computerrack.dto;

import com.siteweb.utility.entity.DataItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ITDeviceModelCategoryDTO {
    public ITDeviceModelCategoryDTO(DataItem dataItem) {
        this.id = dataItem.getItemId();
        this.name = dataItem.getItemValue();
    }

    private Integer id;
    private String name;
}
