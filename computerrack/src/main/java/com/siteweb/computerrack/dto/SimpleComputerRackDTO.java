package com.siteweb.computerrack.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.enumeration.SourceType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (2024-11-08)
 **/
@Data
public class SimpleComputerRackDTO {
    @JsonProperty("computerRackId")
    private Integer computerRackId;
    @JsonProperty("computerRackName")
    private String computerRackName;
    @JsonProperty("computerRackNumber")
    private String computerRackNumber;
    @JsonProperty("itDevices")
    private List<SimpleITDeviceDTO> itDevices;
    @JsonProperty("uHeight")
    private Integer uHeight;


    @JsonIgnore
    public ResourceObject getGlobal() {
        ResourceObject obj = new ResourceObject();
        obj.setObjectId(this.computerRackId);
        obj.setObjectTypeId(SourceType.COMPUTERRACK.value());
        return obj;
    }

}
