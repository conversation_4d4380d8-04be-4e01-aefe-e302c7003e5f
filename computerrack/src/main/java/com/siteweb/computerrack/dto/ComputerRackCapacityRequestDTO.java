package com.siteweb.computerrack.dto;

import lombok.Data;

@Data
public class ComputerRackCapacityRequestDTO {
    /**
     * 机架id
     */
    private String ids;
    /**
     * U位使用容量
     */
    private ComputerRackCapacityInnerDTO rackSpace;
    /**
     * 结构承重容量
     */
    private ComputerRackCapacityInnerDTO rackWeight;
    /**
     * 制冷冷却容量
     */
    private ComputerRackCapacityInnerDTO rackCooling;
    /**
     * 功率负载容量
     */
    private ComputerRackCapacityInnerDTO comPower;


    @Data
    public static class ComputerRackCapacityInnerDTO {
        /**
         * 额定容量
         */
        private Double ratedCapacity;

        /**
         * 容量单位
         */
        private String unit;
    }


}
