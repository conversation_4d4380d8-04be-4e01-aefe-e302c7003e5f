package com.siteweb.computerrack.dto;

import com.siteweb.computerrack.entity.ITDeviceModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class ITDeviceModelmportDTO {

    /**
     * IT设备类型名
     */
    private String iTDeviceModelName;
    /**
     * U高
     */
    private Integer unitHeight;
    /**
     * 厂家
     */
    private String manufactor;
    /**
     * 模型
     */
    private String model;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 长度
     */
    private Double length;
    /**
     * 宽度
     */
    private Double width;
    /**
     * 高度
     */
    private Double height;
    /**
     * 额定功率
     */
    private Double ratePower;
    /**
     * 冷量
     */
    private Double rateCooling;
    /**
     * 重量
     */
    private Double rateWeight;
    /**
     * 模型类型id
     */
    private Integer categoryId;
    /**
     * 模型类型名称
     */
    private String categoryName;

    public ITDeviceModel toITDeviceModel() {
        ITDeviceModel iTDeviceModel = new ITDeviceModel();
        iTDeviceModel.setITDeviceModelName(this.iTDeviceModelName);
        iTDeviceModel.setUnitHeight(this.unitHeight);
        iTDeviceModel.setManufactor(this.manufactor);
        iTDeviceModel.setModel(this.model);
        iTDeviceModel.setBrand(this.brand);
        iTDeviceModel.setLength(this.length);
        iTDeviceModel.setWidth(this.width);
        iTDeviceModel.setHeight(this.height);
        iTDeviceModel.setRatePower(this.ratePower);
        iTDeviceModel.setRateCooling(this.rateCooling);
        iTDeviceModel.setRateWeight(this.rateWeight);
        iTDeviceModel.setCategoryId(this.categoryId);
        iTDeviceModel.setCategoryName(this.categoryName);
        return iTDeviceModel;
    }
}