package com.siteweb.computerrack.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.siteweb.common.serializer.DoubleNonNullSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 机架容量计算的DTO
 * <AUTHOR>
 * @date 2024/08/06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComputerRackCapacityCalculateDTO {
    public ComputerRackCapacityCalculateDTO(String attributeName, String unit) {
        this.attributeName = attributeName;
        this.unit = unit;
    }

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * Rack的总容量
     */
    private double totalCapacity;

    /**
     * Rack剩余的容量
     */
    private double freeCapacity;

    /**
     * Rack已经使用的容量
     */
    private double usedCapacity;

    /**
     * Rack的使用率
     */
    @JsonSerialize(using = DoubleNonNullSerializer.class)
    private double percent;

    /**
     * 容量单位，比如：U,kg,kVA
     */
    private String unit;

    /**
     * 计算使用率
     * @return {@link ComputerRackCapacityCalculateDTO }
     */
    public ComputerRackCapacityCalculateDTO calculatePercent() {
        if (this.usedCapacity > 0 && this.totalCapacity > 0) {
            this.percent = this.usedCapacity / this.totalCapacity * 100;
        }
        return this;
    }

    /**
     * 更新总量、剩余、使用容量
     * @param total 总量
     * @param free 剩余
     * @param used 使用
     */
    public void updateCapacities(double total, double free, double used) {
        this.totalCapacity += total;
        this.freeCapacity += free;
        this.usedCapacity += used;
    }
}
