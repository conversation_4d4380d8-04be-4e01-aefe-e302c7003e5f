package com.siteweb.computerrack.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class ComputerRackSignaImportDTO {

    /**
     * 机架名称
     */
    private String computerRackName;
    /**
     * 开通率表达式 绑定设备id.信号id 支持表达式
     */
    private String openExpression;
    /**
     * 功率表达式 绑定设备id.信号id
     */
    private String powerExpression;

}