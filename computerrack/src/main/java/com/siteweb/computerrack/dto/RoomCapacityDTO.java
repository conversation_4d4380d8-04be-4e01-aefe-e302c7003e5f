package com.siteweb.computerrack.dto;

import com.siteweb.capacity.dto.CapacityAttributeValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@NoArgsConstructor
public class RoomCapacityDTO {

    public RoomCapacityDTO(String roomName) {
        this.room = roomName;
        this.rated = 0;
        this.used = 0;
    }

    public void AddAttribute(CapacityAttributeValue value){
        if(value == null) return ;
        if(value.getRatedCapacity() != null  && !Double.isNaN(value.getRatedCapacity())){
            this.rated+= value.getRatedCapacity().intValue();
        }
        if(value.getUsedCapacity() != null  && !Double.isNaN(value.getUsedCapacity())){
            this.used+= value.getUsedCapacity().intValue();
        }
    }


    public Double getPercent(){
        if(rated == 0d) return 0d;
        Double value = new Double(used) / new Double(rated) * 100d;
        BigDecimal b = BigDecimal.valueOf(value).setScale(2, BigDecimal.ROUND_HALF_UP);
        return b.doubleValue();
    }


    private String room;

    private Integer rated;

    private Integer used;
}
