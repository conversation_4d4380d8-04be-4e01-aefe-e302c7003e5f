package com.siteweb.computerrack.dto;

import com.siteweb.capacity.dto.CapacityAttributeValue;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.computerrack.entity.ComputerRack;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class DetailedComputerRackDTO extends ComputerRackDTO {
    private List<SimpleITDeviceDTO> simpleItDevices;
    private Integer uHeight;

    public static DetailedComputerRackDTO build(ComputerRack computerRack, CapacityAttributeService capacityAttributeService) {
        if(computerRack == null) return null;
        DetailedComputerRackDTO dto = new DetailedComputerRackDTO();
        dto.setComputerRackId(computerRack.getComputerRackId());
        dto.setComputerRackName(computerRack.getComputerRackName());
        dto.setComputerRackNumber(computerRack.getComputerRackNumber());
        dto.setBusiness(computerRack.getBusiness());
        dto.setCustomer(computerRack.getCustomer());
        dto.setStartTime(computerRack.getStartTime());
        dto.setRemark(computerRack.getRemark());
        dto.setItDevices(computerRack.getItDevices());
        dto.setResourceStructureId(computerRack.getResourceStructureId());
        dto.setPosition(computerRack.getPosition());
        List<CapacityAttribute> attributes = capacityAttributeService.findAttributes(computerRack.getGlobal());
        if (attributes != null && attributes.size() > 0) {
            for (CapacityAttribute attribute : attributes) {
                CapacityAttributeValue value = CapacityAttributeValue.fromAttribute(attribute);
                dto.getCapacity().put(value.getAttributeName(), value);
            }
        }
        return dto;
    }

}
