package com.siteweb.computerrack.dto;

import com.siteweb.capacity.entity.LogicType;
import com.siteweb.computerrack.entity.ComputerRack;
import lombok.Data;


@Data
public class ComputerRackImportDTO {
    private String computerRackNumber;
    private String computerRackName;
    private String roomName;

    private Integer ratedUHeight;
    private Double ratedPower;
    private Double ratedCooling;
    private Double ratedWeight;

    private String customer;
    private String business;
    private String remark;
    private String powerSource;
    /**
     * u位设备唯一编号
     */
    private String uDeviceName;

    public LogicType getLogicType() {
        return "COMPLEX_INDEX".equals(this.powerSource) ? LogicType.COMPLEX_INDEX : LogicType.PASSIVE_UPDATE;
    }

    public ComputerRack toComputerRack(){
        ComputerRack computerRack = new ComputerRack();
        computerRack.setComputerRackName(this.getComputerRackName());
        computerRack.setComputerRackNumber(this.getComputerRackNumber());
        computerRack.setBusiness(this.getBusiness());
        computerRack.setCustomer(this.getCustomer());
        computerRack.setRemark(this.getRemark());


        return computerRack;

    }




}
