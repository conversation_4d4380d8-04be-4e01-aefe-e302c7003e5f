package com.siteweb.computerrack.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class RackRecordParamDto {

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 资源位置
     */
    private String position;

    /**
     * 机架名称
     */
    private String computerRackName;

    /**
     * IT设备名称
     */
    private String iTDeviceName;

    /**
     * 状态 0全部 1上架 2下架
     */
    private Integer operateState;

    /**
     * 层级id
     */
    private List<Integer> resourceStructureIds;
}
