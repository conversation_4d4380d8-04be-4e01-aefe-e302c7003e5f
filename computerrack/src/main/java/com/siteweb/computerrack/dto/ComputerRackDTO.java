package com.siteweb.computerrack.dto;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.capacity.dto.CapacityAttributeValue;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.enumeration.SourceType;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ComputerRackDTO {
    private Integer computerRackId;
    private String computerRackName;
    private String computerRackNumber;
    private String customer;
    private String business;
    private Date startTime;
    private String remark;
    private String position;
    private Integer resourceStructureId;
    private List<ITDevice> itDevices;

    /**
     * u位管理设备唯一编码
     */
    private String uDeviceNumber;
    /**
     * u位设备名称
     */
    private String uDeviceName;
    private Map<String, CapacityAttributeValue> capacity = new HashMap<>();



    @JsonIgnore
    public ResourceObject getGlobal() {
        ResourceObject obj = new ResourceObject();
        obj.setObjectId(this.computerRackId);
        obj.setObjectTypeId(SourceType.COMPUTERRACK.value());
        return obj;
    }



    public ComputerRack toRack(){
        ComputerRack rack = new ComputerRack();
        rack.copyDTO(this);
        return rack;
    }





    public static ComputerRackDTO merage(ComputerRack computerRack, ComputerRackImportDTO computerRackImportDTO){
        ComputerRackDTO dto = new ComputerRackDTO();
        dto.setComputerRackId(computerRack.getComputerRackId());
        dto.setComputerRackName(computerRack.getComputerRackName());
        dto.setComputerRackNumber(computerRack.getComputerRackNumber());
        dto.setBusiness(computerRack.getBusiness());
        dto.setCustomer(computerRack.getCustomer());
        dto.setStartTime(computerRack.getStartTime());
        dto.setRemark(computerRack.getRemark());
//        dto.setRatedCooling(computerRackImportDTO.getRatedCooling());
//        dto.setRatedPower(computerRackImportDTO.getRatedPower());
//        dto.setRatedUHeight(computerRackImportDTO.getRatedUHeight());
//        dto.setRatedWeight(computerRackImportDTO.getRatedWeight());
        dto.setResourceStructureId(computerRack.getResourceStructureId());
        dto.setPosition(computerRack.getPosition());
        return dto;
    }





    public static ComputerRackDTO build(ComputerRack computerRack, CapacityAttributeService capacityAttributeService) {
        if(computerRack == null) return null;
        ComputerRackDTO dto = new ComputerRackDTO();
        dto.setComputerRackId(computerRack.getComputerRackId());
        dto.setComputerRackName(computerRack.getComputerRackName());
        dto.setComputerRackNumber(computerRack.getComputerRackNumber());
        dto.setBusiness(computerRack.getBusiness());
        dto.setCustomer(computerRack.getCustomer());
        dto.setStartTime(computerRack.getStartTime());
        dto.setRemark(computerRack.getRemark());
        dto.setItDevices(computerRack.getItDevices());
        dto.setResourceStructureId(computerRack.getResourceStructureId());
        dto.setPosition(computerRack.getPosition());
        List<CapacityAttribute> attributes = capacityAttributeService.findAttributes(computerRack.getGlobal());
        if (CollUtil.isNotEmpty(attributes)) {
            for (CapacityAttribute attribute : attributes) {
                CapacityAttributeValue value = CapacityAttributeValue.fromAttribute(attribute);
                dto.capacity.put(value.getAttributeName(), value);
            }
        }
        return dto;
    }

    public static ComputerRackDTO build(ComputerRackDTO computerRack, CapacityAttributeService capacityAttributeService) {
        List<CapacityAttribute> attributes = capacityAttributeService.findAttributes(computerRack.getGlobal());
        if (CollUtil.isNotEmpty(attributes)) {
            for (CapacityAttribute attribute : attributes) {
                CapacityAttributeValue value = CapacityAttributeValue.fromAttribute(attribute);
                computerRack.capacity.put(value.getAttributeName(), value);
            }
        }
        return computerRack;
    }
}