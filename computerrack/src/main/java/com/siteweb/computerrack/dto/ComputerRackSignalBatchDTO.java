package com.siteweb.computerrack.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComputerRackSignalBatchDTO {

    /**
     * 机架id
     */
    private List<Integer> computerRackIds;
    /**
     * 开通率表达式 绑定设备id.信号id 支持表达式
     */
    private String openExpression;
    /**
     * 功率表达式 绑定设备id.信号id
     */
    private String powerExpression;

}