package com.siteweb.computerrack.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.computerrack.entity.ITDevice;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ITDeviceImportDTO {
    public String computerRackName;
    public String itDeviceName;
    public String itDeviceModelName;
    public String customer;
    public String business;
    public String ipaddr;
    public String serialNumber;
    public String assetCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date purchaseDate;
    public String remark;
    public Integer uIndex;

    public ITDevice toITDevice() {
        ITDevice itDevice = new ITDevice();
        itDevice.setITDeviceName(itDeviceName);
        itDevice.setITDeviceName(itDeviceName);
        itDevice.setCustomer(customer);
        itDevice.setBusiness(business);
        itDevice.setSerialNumber(serialNumber);
        itDevice.setPurchaseDate(purchaseDate);
        itDevice.setIpaddr(ipaddr);
        itDevice.setRemark(remark);
        return itDevice;
    }

}
