package com.siteweb.computerrack.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * IT机柜展板参数
 */
@Data
public class ComputerRackITScreenParam {
    /**
     * 机架id
     */
    private Integer rackId;
    /**
     * 日期类型 1：年，2：月，3：季度，4：自定义，5：季度显示日
     */
    private Integer dataType;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
