package com.siteweb.computerrack.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("ITDeviceModel")
public class ITDeviceModel implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer iTDeviceModelId;
    /**
     * IT设备类型名
     */
    private String iTDeviceModelName;
    /**
     * U高
     */
    private Integer unitHeight;
    /**
     * 厂家
     */
    private String manufactor;
    /**
     * 模型
     */
    private String model;
    /**
     * 品牌
     */
    private String brand;

    private Double length;
    private Double width;
    private Double height;
    private Double ratePower;
    private Double rateCooling;
    private Double rateWeight;

    /**
     * 3D模型文件
     */
    private String modelFile;

    /**
     * 模型类型id
     */
    private Integer categoryId;

    /**
     * 模型类型名称
     */
    @TableField(exist = false)
    private String categoryName;
}