package com.siteweb.computerrack.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;



@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("computerrackequipmentemap")
public class ComputerRackEquipmentMap implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer computerRackEquipmentMapId;
    private Integer computerRackId;
    private Integer equipmentId;
}