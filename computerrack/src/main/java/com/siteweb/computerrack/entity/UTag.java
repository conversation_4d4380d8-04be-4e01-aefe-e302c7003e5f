package com.siteweb.computerrack.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("tbl_utag")
@NoArgsConstructor
@AllArgsConstructor
public class UTag {
    public UTag(String tagValue, Integer asserId, Date createTime, Date updateTime) {
        this.tagValue = tagValue;
        this.asserId = asserId;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    /**
     * u位标签主键
     *
     */
    @TableId(value = "UTagId", type = IdType.AUTO)
    private Integer uTagId;
    /**
     * u位标签唯一标识码
     */
    private String tagValue;
    /**
     * 绑定资产id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer asserId;

    private Integer isOnline;
    /**
     * u位管理设备id
     */
    private Integer uDeviceId;

    /**
     * 所处u位
     */
    private Integer uPosition;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 绑定时间
     */
    private Date updateTime;
}
