package com.siteweb.computerrack.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.io.Serializable;


/**
 * 机架信号定时记录表 (InfluxDB)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Measurement(name = "computerracksignalrecord")
public class ComputerRackSignalRecord implements Serializable {

    /**
     * 机架id (Tag)
     */
    @Column(name = "computerRackId", tag = true)
    private String computerRackId;

    /**
     * 层级id (Tag)
     */
    @Column(name = "resourceStructureId", tag = true)
    private String resourceStructureId;

    /**
     * 开通状态 (Field)
     */
    @Column(name = "openValue")
    private Boolean openValue;

    /**
     * 功率值 (Field)
     */
    @Column(name = "powerValue")
    private Double powerValue;

    @Column(name = "time")
    public String time;

}