package com.siteweb.computerrack.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.computerrack.dto.ComputerRackDTO;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.enumeration.SourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("ComputerRack")
public class ComputerRack implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer computerRackId;
    /**
     * 机架名称
     */
    private String computerRackName;
    /**
     * 机架编号
     */
    private String computerRackNumber;
    /**
     * 层级ID
     */
    private Integer resourceStructureId;
    /**
     * 机架位置
     */
    private String position;
    /**
     * 客户名称
     */
    private String customer;
    /**
     * 业务
     */
    private String business;
    /**
     * 启用时间
     */
    private Date startTime;
    /**
     * 描述
     */
    private String remark;

    @TableField(exist = false)
    private List<ITDevice> itDevices;

    public void copyDTO(ComputerRackDTO rackDTO) {
        if (rackDTO.getComputerRackId() != null) this.computerRackId = rackDTO.getComputerRackId();
        this.computerRackName = rackDTO.getComputerRackName();
        this.computerRackNumber = rackDTO.getComputerRackNumber();
        this.resourceStructureId = rackDTO.getResourceStructureId();
        this.position =  rackDTO.getPosition();
        this.startTime = rackDTO.getStartTime();
        this.customer = rackDTO.getCustomer();
        this.business = rackDTO.getBusiness();
        this.remark = rackDTO.getRemark();
    }


    public Integer getObjectId() {
        return computerRackId;
    }

    public Integer getObjectType() {
        return SourceType.COMPUTERRACK.value();
    }

    @JsonIgnore
    public ResourceObject getGlobal() {
        ResourceObject obj = new ResourceObject();
        obj.setObjectId(this.computerRackId);
        obj.setObjectTypeId(SourceType.COMPUTERRACK.value());
        return obj;
    }

}