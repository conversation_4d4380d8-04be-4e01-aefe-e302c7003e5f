package com.siteweb.computerrack.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("computerracksignalmap")
public class ComputerRackSignalMap implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer computerRackSignalMapId;
    /**
     * 机架id
     */
    private Integer computerRackId;
    /**
     * 开通率表达式 绑定设备id.信号id 支持表达式
     */
    private String openExpression;
    /**
     * 功率表达式 绑定设备id.信号id 暂时不支持表达式
     */
    private String powerExpression;
}