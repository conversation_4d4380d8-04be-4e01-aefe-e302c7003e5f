package com.siteweb.computerrack.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tbl_rackmountrecord")
public class RackMountRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("RackId")
    private Integer computerRackId;

    @TableField("RackDeviceId")
    private Integer iTDeviceId;

    @TableField("RackPosition")
    private Integer uIndex;

    @TableField("OperateTime")
    private Date operateTime;

    @TableField("OperateState")
    private Integer operateState;

    @TableField("Expired")
    private Boolean expired;
}