package com.siteweb.computerrack.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.enumeration.SourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("ITDevice")
public class ITDevice implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer iTDeviceId;//IT设备ID

    /**
     * IT设备名称
     */
    private String iTDeviceName;
    /**
     * 属于哪个IT设备模型
     */
    private Integer iTDeviceModelId;
    /**
     * 属于哪个机架
     */
    @TableField(value = "computerRackId", updateStrategy = FieldStrategy.IGNORED)
    private Integer computerRackId;
    /**
     * 客户名
     */
    private String customer;
    /**
     * 业务
     */
    private String business;
    /**
     * 购买日期
     */
    private Date purchaseDate;
    /**
     * 启动日期
     */
    private Date launchDate;

    /**
     * 起始U位
     */

    @TableField(value = "uIndex", updateStrategy = FieldStrategy.IGNORED)
    private Integer uIndex;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 备注
     */
    private String remark;

    public Integer getObjectId() {
        return iTDeviceId;
    }

    public Integer getObjectType() {
        return SourceType.ITDEVICE.value();
    }


    public Integer getUnitHeight() {
        if (itDeviceModel == null) return 0;
        return itDeviceModel.getUnitHeight();
    }

    public String getModelName(){
        if (itDeviceModel == null) return null;
        return itDeviceModel.getITDeviceModelName();
    }


    /**
     * 属于哪个机架
     */
    @TableField(exist = false)
    private String computerRackName;


    @TableField(exist = false)
    private ITDeviceModel itDeviceModel;
    //绑定资产id
    private Integer assetDeviceId;

    /**
     * IP地址
     */
    private String ipaddr;

    @JsonIgnore
    public ResourceObject getGlobal() {
        ResourceObject obj = new ResourceObject();
        obj.setObjectId(this.iTDeviceId);
        obj.setObjectTypeId(SourceType.ITDEVICE.value());
        return obj;
    }
}