package com.siteweb.computerrack.service.impl;

import com.siteweb.computerrack.dto.ComputerRackDTO;
import com.siteweb.computerrack.dto.RoomCapacityDTO;
import com.siteweb.computerrack.service.ComputerRackCapacityService;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ComputerRackCapacityServiceImpl implements ComputerRackCapacityService {
    @Autowired
    private ComputerRackService computerRackService;

    @Autowired
    private ResourceStructureManager resourceStructureManager;


    @Override
    public Map<String, List<RoomCapacityDTO>> ByBuilding() {
        Map<String, List<RoomCapacityDTO>> result = new HashMap();
        List<ResourceStructure> rooms = resourceStructureManager.getAll().stream().filter(e -> e.getStructureTypeId() == SourceType.BUILDING.value()).collect(Collectors.toList());
        List<RoomCapacityDTO> coolings = new ArrayList<>();
        List<RoomCapacityDTO> weights = new ArrayList<>();
        List<RoomCapacityDTO> powers = new ArrayList<>();
        List<RoomCapacityDTO> spaces = new ArrayList<>();
        for (ResourceStructure room : rooms) {
            RoomCapacityDTO cooling = new RoomCapacityDTO(room.getResourceStructureName());
            RoomCapacityDTO weight = new RoomCapacityDTO(room.getResourceStructureName());
            RoomCapacityDTO power = new RoomCapacityDTO(room.getResourceStructureName());
            RoomCapacityDTO space = new RoomCapacityDTO(room.getResourceStructureName());
            List<ComputerRackDTO> racks = computerRackService.findComputerRackDTOs(room.getResourceStructureId(), true);
            for (ComputerRackDTO rack : racks) {
                if (rack.getCapacity().size() > 0) {
                    cooling.AddAttribute(rack.getCapacity().get("rackCooling"));
                    weight.AddAttribute(rack.getCapacity().get("rackWeight"));
                    power.AddAttribute(rack.getCapacity().get("comPower"));
                    space.AddAttribute(rack.getCapacity().get("rackSpace"));
                }
            }
            coolings.add(cooling);
            weights.add(weight);
            powers.add(power);
            spaces.add(space);
        }

        result.put("冷量", coolings);
        result.put("重量", weights);
        result.put("空间", spaces);
        result.put("功率", powers);
        return result;
    }

    @Override
    public Map<String, List<RoomCapacityDTO>> ByRoom() {
        Map<String, List<RoomCapacityDTO>> result = new HashMap();
        List<ResourceStructure> rooms = computerRackService.findAllResourceStructure();
        List<RoomCapacityDTO> coolings = new ArrayList<>();
        List<RoomCapacityDTO> weights = new ArrayList<>();
        List<RoomCapacityDTO> powers = new ArrayList<>();
        List<RoomCapacityDTO> spaces = new ArrayList<>();
        for (ResourceStructure room : rooms) {
            RoomCapacityDTO cooling = new RoomCapacityDTO(room.getResourceStructureName());
            RoomCapacityDTO weight = new RoomCapacityDTO(room.getResourceStructureName());
            RoomCapacityDTO power = new RoomCapacityDTO(room.getResourceStructureName());
            RoomCapacityDTO space = new RoomCapacityDTO(room.getResourceStructureName());
            List<ComputerRackDTO> racks = computerRackService.findComputerRackDTOs(room.getResourceStructureId(), false);
            for (ComputerRackDTO rack : racks) {
                if (rack.getCapacity().size() > 0) {
                    cooling.AddAttribute(rack.getCapacity().get("rackCooling"));
                    weight.AddAttribute(rack.getCapacity().get("rackWeight"));
                    power.AddAttribute(rack.getCapacity().get("comPower"));
                    space.AddAttribute(rack.getCapacity().get("rackSpace"));
                }
            }
            coolings.add(cooling);
            weights.add(weight);
            powers.add(power);
            spaces.add(space);
        }

        result.put("冷量", coolings);
        result.put("重量", weights);
        result.put("空间", spaces);
        result.put("功率", powers);
        return result;
    }

    @Override
    public Map<String, List<RoomCapacityDTO>> ByResourceStructures(List<Integer> rids) {
        Map<String, List<RoomCapacityDTO>> result = new HashMap();
        List<RoomCapacityDTO> coolings = new ArrayList<>();
        List<RoomCapacityDTO> weights = new ArrayList<>();
        List<RoomCapacityDTO> powers = new ArrayList<>();
        List<RoomCapacityDTO> spaces = new ArrayList<>();
        for (Integer resourceStructureId : rids) {
            ResourceStructure room = resourceStructureManager.getResourceStructureById(resourceStructureId);
            if (room != null) {
                RoomCapacityDTO cooling = new RoomCapacityDTO(room.getResourceStructureName());
                RoomCapacityDTO weight = new RoomCapacityDTO(room.getResourceStructureName());
                RoomCapacityDTO power = new RoomCapacityDTO(room.getResourceStructureName());
                RoomCapacityDTO space = new RoomCapacityDTO(room.getResourceStructureName());
                List<ComputerRackDTO> racks = computerRackService.findComputerRackDTOs(room.getResourceStructureId(), false);
                for (ComputerRackDTO rack : racks) {
                    if (rack.getCapacity().size() > 0) {
                        cooling.AddAttribute(rack.getCapacity().get("rackCooling"));
                        weight.AddAttribute(rack.getCapacity().get("rackWeight"));
                        power.AddAttribute(rack.getCapacity().get("comPower"));
                        space.AddAttribute(rack.getCapacity().get("rackSpace"));
                    }
                }
                coolings.add(cooling);
                weights.add(weight);
                powers.add(power);
                spaces.add(space);
            }
        }
        result.put("冷量", coolings);
        result.put("重量", weights);
        result.put("空间", spaces);
        result.put("功率", powers);
        return result;
    }

}
