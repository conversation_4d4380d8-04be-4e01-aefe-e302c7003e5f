package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.ITDeviceImportDTO;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.enumeration.ITDeviceOperatingStatus;
import com.siteweb.utility.dto.ImportErrorInfoDTO;

import java.util.List;

public interface ITDeviceOperateService {

    List<ImportErrorInfoDTO> importList(List<ITDeviceImportDTO> itDeviceOperateTemplateList);


    ITDeviceOperatingStatus putInShelf(Integer itDeviceId, Integer computerRackId, Integer uIndex);

    ITDeviceOperatingStatus putInShelf(String itDeviceName, String computerRackName, Integer uIndex);

    ITDeviceOperatingStatus putInShelf(ITDevice itDevice, ComputerRack computerRack, Integer uIndex);


    boolean canPutShelf(Integer computerRackId, Integer uIndex, Integer uHeight);

    boolean canPutShelf(String computerRackName, Integer uIndex, Integer uHeight);

    boolean canPutShelf(ComputerRack computerRack, Integer uIndex, Integer uHeight);


    ITDeviceOperatingStatus takeFromShelf(ITDevice itDevice);

    ITDeviceOperatingStatus takeFromShelf(List<ITDevice> itDeviceList);

    ITDeviceOperatingStatus takeFromShelf(String itDeviceName);

    ITDeviceOperatingStatus takeFromShelf(Integer itDeviceId);

    ITDeviceOperatingStatus takeFromShelf(Integer computerRackId, Integer uIndex);


}

