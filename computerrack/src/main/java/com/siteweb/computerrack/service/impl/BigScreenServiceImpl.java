package com.siteweb.computerrack.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.admin.security.LanguageFilter;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeQuartzRecordService;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.enumeration.ComputerRackCapacity;
import com.siteweb.computerrack.service.BigScreenService;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.computerrack.service.UTagService;
import com.siteweb.computerrack.vo.RackUIndexRateVo;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.vo.NameValueVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.DoubleAdder;
import java.util.stream.Collectors;

@Service
public class BigScreenServiceImpl implements BigScreenService {

    /**
     * 因为在饼图中颜色没有那么多，暂时只能展示前 8 个
     */
    private static final int PIE_CHART_TOP = 8;
    /**
     * 默认topN
     */
    private static final int DEFAULT_TOP_N = 10;
    @Autowired
    ITDeviceService itDeviceService;
    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    ComputerRackService computerRackService;
    @Autowired
    CapacityAttributeService capacityAttributeService;
    @Autowired
    UTagService uTagService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    CapacityAttributeQuartzRecordService capacityAttributeQuartzRecordService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public List<NameValueVO> findITDeviceTypePieChart(Integer userId, String resourceStructureId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        List<NameValueVO> result = itDeviceService.findITDeviceTypePieChartByResourceStructureIds(resourceStructureIds);
        return getPieChartTop8(result);
    }

    /**
     * 如果返回的饼图长度大于8，则需要将超出的长度归为其他
     * @param result 需要返回的结果
     * @return {@link List}<{@link NameValueVO}>
     */
    private  List<NameValueVO> getPieChartTop8(List<NameValueVO> result) {
        if (result.size() <= PIE_CHART_TOP) {
            return result;
        }
        List<NameValueVO> list = result.stream().sorted(Comparator.comparing(NameValueVO::getValue)).limit(PIE_CHART_TOP).collect(Collectors.toList());
        Double otherCount = result.stream().sorted(Comparator.comparing(NameValueVO::getValue).reversed())
                                  .limit(result.size() - PIE_CHART_TOP).mapToDouble(NameValueVO::getValue).sum();
        list.add(new NameValueVO(messageSourceUtil.getMessage("common.other"), otherCount));
        return list;
    }

    @Override
    public List<NameValueVO> findITDeviceBusinessPieChart(Integer userId, String resourceStructureId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        //根据层级id,获取it设备
        List<ITDevice> itDeviceList = itDeviceService.findITDeviceByResourceStructureIds(resourceStructureIds);
        Map<String, Double> itDeviceBusinessMap = itDeviceList.stream()
                                                              .collect(Collectors.groupingBy(ITDevice::getBusiness, Collectors.summingDouble(e -> 1)));
        List<NameValueVO> result = itDeviceBusinessMap.entrySet()
                                                      .stream()
                                                      .map(entry -> new NameValueVO(entry.getKey(), entry.getValue()))
                                                      .toList();
        return getPieChartTop8(result);
    }

    @Override
    public List<NameValueVO> getRackCountByCustomer(Integer userId, String filterResourceStructureIds) {
        //获取拥有权限和过滤层级后的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findResourceStructureIdsByUserIdAndResourceStructureIds(userId, filterResourceStructureIds);
        //根据层级id,获取机架
        List<ComputerRack> computerRackList = computerRackService.findComputerRacksByResourceStructureIds(resourceStructureIds);
        Map<String, Double> collect = computerRackList.stream().filter(i-> !StrUtil.isEmpty(i.getCustomer()))
                .collect(Collectors.groupingBy(ComputerRack::getCustomer, Collectors.summingDouble(e -> 1)));
        return collect.entrySet()
                .stream()
                .map(entry -> new NameValueVO(entry.getKey(), entry.getValue()))
                .toList();
    }

    @Override
    public List<NameValueVO> getRackCountByBusiness(Integer userId, String filterResourceStructureIds) {
        //获取拥有权限和过滤层级后的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findResourceStructureIdsByUserIdAndResourceStructureIds(userId, filterResourceStructureIds);
        //根据层级id,获取机架
        List<ComputerRack> computerRackList = computerRackService.findComputerRacksByResourceStructureIds(resourceStructureIds);
        Map<String, Double> collect = computerRackList.stream().filter(i-> !StrUtil.isEmpty(i.getBusiness()))
                .collect(Collectors.groupingBy(ComputerRack::getBusiness, Collectors.summingDouble(e -> 1)));
        return collect.entrySet()
                .stream()
                .map(entry -> new NameValueVO(entry.getKey(), entry.getValue()))
                .toList();
    }
    @Override
    public List<NameValueVO> findCurrentUIndexChange(Integer userId, String resourceStructureId) {
        // 获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        List<ComputerRack> computerRackList = computerRackService.findComputerRacksByResourceStructureIds(resourceStructureIds);
        HashMap<String, DoubleAdder> hashMap = new LinkedHashMap<>(4);
        for (ComputerRack computerRack : computerRackList) {
            CapacityAttribute attribute = this.findRackUIndexAttribute(computerRack.getComputerRackId());
            hashMap.computeIfAbsent(messageSourceUtil.getMessageByHeader("common.field.usedUIndex",LanguageFilter.getCurrentThreadLanguage()),a -> new DoubleAdder())
                           .add(attribute.getUsedCapacity());
            hashMap.computeIfAbsent(messageSourceUtil.getMessageByHeader("common.field.freeUIndex",LanguageFilter.getCurrentThreadLanguage()), a -> new DoubleAdder())
                   .add(attribute.getFreeCapacity());
        }
        List<NameValueVO> result = hashMap.entrySet()
                                                .stream()
                                                .map(entry -> new NameValueVO(entry.getKey(), entry.getValue().doubleValue()))
                                                .collect(Collectors.toList());
        //标签绑定率
        NameValueVO tagRate = uTagService.findTagRateByResourceStructureIds(resourceStructureIds);
        result.add(tagRate);
        return result;
    }

    @Override
    public List<NameValueVO> findRoomUIndexRate(Integer userId, String resourceStructureId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        List<ComputerRack> computerRacks = computerRackService.findComputerRacksByResourceStructureIds(resourceStructureIds);
        //根据房间分组
        Map<Integer, List<ComputerRack>> collect = computerRacks.stream()
                                                                .collect(Collectors.groupingBy(ComputerRack::getResourceStructureId));
        List<NameValueVO> result = new ArrayList<>(collect.size());
        //统计每个房间的u位使用率
        for (Map.Entry<Integer, List<ComputerRack>> entry : collect.entrySet()) {
            double sum = 0.0;
            double used = 0.0;
            for (ComputerRack computerRack : entry.getValue()) {
                CapacityAttribute attribute = this.findRackUIndexAttribute(computerRack.getComputerRackId());
                sum += attribute.getRatedCapacity();
                used += attribute.getUsedCapacity();
            }
            String roomName = resourceStructureService.findById(entry.getKey()).getResourceStructureName();
            //u位使用率保留两位小数
            double value = NumberUtil.div(used * 100, sum, 2);
            result.add(new NameValueVO(roomName,value));
        }
        return result.stream()
                     .sorted(Comparator.comparing(NameValueVO::getValue).reversed())
                     .limit(this.findTopN("bigscreen.roomuindexrate.top"))
                     .toList();
    }

    @Override
    public List<RackUIndexRateVo> findRackUIndexRate(Integer userId, String resourceStructureId) {
        Integer topN = this.findTopN("bigscreen.rackuindexrate.top");
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        //通过层级ids查找机架容量的topN
        return computerRackService.findRackUIndexAttribute(resourceStructureIds,topN);
    }

    /**
     * 通过机架id获取起u位容量属性
     * @param rackId 机架id
     * @return {@link CapacityAttribute}
     */
    private CapacityAttribute findRackUIndexAttribute(Integer rackId){
        ResourceObject resourceObject = new ResourceObject(rackId, SourceType.COMPUTERRACK.value());
        CapacityAttribute attribute = capacityAttributeService.findAttribute(resourceObject, ComputerRackCapacity.RACK_SPACE.getValue());
        //没有u位使用率则设置为0
        if (ObjectUtil.isNull(attribute.getPercent())) {
            attribute.setPercent(0.0);
        }
        //没有已使用u位则设置为0
        if (ObjectUtil.isNull(attribute.getUsedCapacity())) {
            attribute.setUsedCapacity(0.0);
        }
        //没有剩余u位则设置为额定u位
        if (ObjectUtil.isNull(attribute.getFreeCapacity())) {
            attribute.setFreeCapacity(attribute.getRatedCapacity());
        }
        return attribute;
    }


    public Integer findTopN(String configKey){
        if (CharSequenceUtil.isBlank(configKey)) {
            return DEFAULT_TOP_N;
        }
        SystemConfig bySystemConfigKey = systemConfigService.findBySystemConfigKey(configKey);
        if (ObjectUtil.isNull(bySystemConfigKey)) {
            return DEFAULT_TOP_N;
        }
        try {
            return Integer.valueOf(bySystemConfigKey.getSystemConfigValue());
        } catch (NumberFormatException e) {
            return DEFAULT_TOP_N;
        }
    }

    @Override
    public List<NameValueVO> getSumUIndexRate(Integer userId, String resourceStructureId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        //统计近六个月的总u位变化率
        List<Date> everyMonthOfFirstDay = DateUtil.getEveryMonthOfFirstDay(new Date(), 6);
        List<NameValueVO> sumUIndexRate = capacityAttributeQuartzRecordService.findSumUIndexRate(resourceStructureIds, everyMonthOfFirstDay);
        Set<String> dateStringSet = sumUIndexRate.stream()
                                           .map(NameValueVO::getName)
                                           .collect(Collectors.toSet());
        for (Date date : everyMonthOfFirstDay) {
            String dateString = DateUtil.dateToDayString(date);
            if (dateStringSet.contains(dateString)) {
                continue;
            }
            sumUIndexRate.add(new NameValueVO(dateString, 0d));
        }
        sumUIndexRate.sort(Comparator.comparing(NameValueVO::getName));
        return sumUIndexRate;
    }
}
