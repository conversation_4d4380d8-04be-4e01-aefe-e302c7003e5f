package com.siteweb.computerrack.service;


import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.computerrack.dto.*;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.vo.RackUIndexRateVo;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.utility.dto.ImportErrorInfoDTO;

import java.util.Collection;
import java.util.List;

public interface ComputerRackService {


    List<ComputerRack> findComputerRacks();

    List<ComputerRack> findComputerRacks(Integer resourceStructureId, boolean isRecursion);

    ComputerRack  findComputerRack(Integer computerRackId);

    ComputerRack findByComputerRackName(String computerRackName);

    List<ComputerRack> findByComputerRackIdList(String computerRackIdList);



    List<SimpleComputerRackDTO> findSimpleComputerRacks(List<Integer> computerRackIds);


    List<ComputerRackDTO> findComputerRackDTOs();

    List<DetailedComputerRackDTO> findAllDetailedComputerRacks();

    List<ComputerRackDTO> findComputerRackDTOsByUserId(Integer userId);

    List<ComputerRackDTO> findComputerRackDTOs(Integer resourceStructureId, boolean isRecursion);

    ComputerRackDTO findComputerRackDTO(Integer computerRackId);

    ComputerRackDTO findDTOByComputerRackName(String computerRackName);

    List<ComputerRackDTO> findDTOByComputerRackIdList(String computerRackIdList);


    List<ResourceStructure> findAllResourceStructure();

    ComputerRackDTO updateComputerRack(ComputerRackDTO computerRack);

    List<ImportErrorInfoDTO> createComputerRackList(List<ComputerRackImportDTO> computerRackTemplateDTOList);

    ComputerRack createComputerRack(ComputerRackImportDTO computerRack);

    void updateCapacityValueByComputerRackIds(List<Integer> computerIds);
    void updateCapacityValueByComputerRackId(Integer computerId);

    void updateCapacityValues(Collection<Integer> computerRackIdList);

    void updateCapacityValues(ComputerRack computerRack);

    void deleteById(Integer computerRackId);

    List<ComputerRack> findComputerRacksByCustomer(String customer);

    /**
     * 根据层级ids获取机架信息
     * @param resourceStructureIds 层级ids
     * @return {@link List}<{@link ComputerRack}>
     */
    List<ComputerRack> findComputerRacksByResourceStructureIds(List<Integer> resourceStructureIds);

    /**
     * 查找机架u位使用率
     * @param resourceStructureIds 层级ids
     * @param topN 前几条
     * @return {@link List}<{@link RackUIndexRateVo}>
     */
    List<RackUIndexRateVo> findRackUIndexAttribute(List<Integer> resourceStructureIds, Integer topN);

    List<ComputerRack> batchUpdateCustomer(ComputerRackCustomerRequestDTO computerRackCustomerRequestDTO);

    List<String> findAllCustomersByUserId(Integer userId);

    List<String> findAllBusinessByUserId(Integer userId);

    List<ComputerRackDTO>  findComputerRackDTO();

    List<ComputerRack> batchUpdatePosition(ComputerRackPositionRequestDTO computerRackPositionRequestDTO);

    /**
     * 批量设置容量
     */
    void batchUpdateCapacity(ComputerRackCapacityRequestDTO computerRackCapacityRequestDTO);

    /**
     * 获取容量信息，根据层级ids
     *
     * @param resourceStructureIds 层级信息
     * @return {@link List }<{@link CapacityAttribute }>
     */
    List<ComputerRackCapacityCalculateDTO> findAttributesByResourceStructureIds(String resourceStructureIds);

    /**
     * 批量删除机架
     */
    void deleteBatch(List<Integer> ids);
}

