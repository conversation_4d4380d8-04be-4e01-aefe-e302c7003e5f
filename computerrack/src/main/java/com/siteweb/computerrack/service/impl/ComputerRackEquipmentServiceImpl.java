package com.siteweb.computerrack.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.computerrack.dto.*;
import com.siteweb.computerrack.entity.ComputerRackEquipmentMap;
import com.siteweb.computerrack.mapper.ComputerRackEquipmentMapper;
import com.siteweb.computerrack.service.ComputerRackEquipmentService;
import com.siteweb.computerrack.vo.ComputerRackEquipmentVO;
import com.siteweb.monitoring.dto.EquipmentSimpleDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service()
public class ComputerRackEquipmentServiceImpl implements ComputerRackEquipmentService {
    @Autowired
    private ComputerRackEquipmentMapper computerRackEquipmentRepository;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Override
    public List<ComputerRackEquipmentVO> findComputerRackEquipmentVOsByUserId(Integer userId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findThisHierarchyResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }
        List<ComputerRackEquipmentDTO> computerRackEquipmentDTOList = computerRackEquipmentRepository.findComputerRackEquipmentDTOsByResourceStructureIds(resourceStructureIds);
        return this.constructComputerRackEquipmentVOList(computerRackEquipmentDTOList);
    }

    @Override
    public List<ComputerRackEquipmentVO> findComputerRackEquipmentVOsById(Integer id) {
        List<ComputerRackEquipmentDTO> computerRackEquipmentDTOList = computerRackEquipmentRepository.findComputerRackEquipmentDTOsById(id);
        return this.constructComputerRackEquipmentVOList(computerRackEquipmentDTOList);
    }

    private List<ComputerRackEquipmentVO> constructComputerRackEquipmentVOList(List<ComputerRackEquipmentDTO> computerRackEquipmentDTOList) {
        if (CollUtil.isEmpty(computerRackEquipmentDTOList)) {
            return Collections.emptyList();
        }
        List<ComputerRackEquipmentVO> computerRackEquipmentVOList = new ArrayList<>();
        Map<Integer, List<ComputerRackEquipmentDTO>> ComputerRackEquipmentDTOMap = computerRackEquipmentDTOList.stream().collect(Collectors.groupingBy(ComputerRackEquipmentDTO::getComputerRackId));
        for (List<ComputerRackEquipmentDTO> resultList : ComputerRackEquipmentDTOMap.values()) {
            ComputerRackEquipmentVO computerRackEquipmentVO = new ComputerRackEquipmentVO(resultList.get(0));
            List<Integer> equipmentIdList = resultList.stream().map(ComputerRackEquipmentDTO::getEquipmentId).filter(Objects::nonNull).collect(Collectors.toList());
            List<EquipmentSimpleDTO> equipmentSimpleDTOList = new ArrayList<>();
            if (CollUtil.isNotEmpty(equipmentIdList)) {
                for (Integer id : equipmentIdList) {
                    Equipment equipment = equipmentManager.getEquipmentById(id);
                    EquipmentSimpleDTO equipmentSimpleDTO = this.constructSimpleEquipmentDTO(equipment);
                    equipmentSimpleDTOList.add(equipmentSimpleDTO);
                }
            }
            computerRackEquipmentVO.setEquipments(equipmentSimpleDTOList);
            computerRackEquipmentVOList.add(computerRackEquipmentVO);
        }
        return computerRackEquipmentVOList;
    }

    private EquipmentSimpleDTO constructSimpleEquipmentDTO(Equipment equipment) {
        EquipmentSimpleDTO equipmentSimpleDTO = new EquipmentSimpleDTO();
        equipmentSimpleDTO.setEquipmentId(equipment.getEquipmentId());
        equipmentSimpleDTO.setEquipmentName(equipment.getEquipmentName());
        String equipmentPosition = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
        equipmentSimpleDTO.setEquipmentPosition(equipmentPosition);
        return equipmentSimpleDTO;
    }

    private List<ComputerRackEquipmentParamDTO> constructComputerRackEquipmentParamDTOList(ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO) {
        List<Integer> computerRackIdList = StrUtil.split(computerRackEquipmentRequestDTO.getComputerRackIds(), ",").stream().filter(Objects::nonNull).map(i->Integer.parseInt(i)).collect(Collectors.toList());
        List<Integer> equipmentIdList = StrUtil.split(computerRackEquipmentRequestDTO.getEquipmentIds(), ",").stream().filter(Objects::nonNull).map(i->Integer.parseInt(i)).collect(Collectors.toList());
        if (CollUtil.isEmpty(computerRackIdList) | CollUtil.isEmpty(equipmentIdList)) {
            return Collections.emptyList();
        }
        List<ComputerRackEquipmentParamDTO> computerRackEquipmentParamDTOList = new ArrayList<>();
        for (Integer rackId : computerRackIdList) {
            for (Integer eqId : equipmentIdList) {
                ComputerRackEquipmentParamDTO dto = new ComputerRackEquipmentParamDTO(rackId, eqId);
                computerRackEquipmentParamDTOList.add(dto);
            }
        }
        return computerRackEquipmentParamDTOList;
    }

    @Override
    public List<ComputerRackEquipmentParamDTO> createComputerRacksEquipments(ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO) {
        List<ComputerRackEquipmentParamDTO> computerRackEquipmentParamDTOList = this.constructComputerRackEquipmentParamDTOList(computerRackEquipmentRequestDTO);
        computerRackEquipmentRepository.batchInsert(computerRackEquipmentParamDTOList);
        return computerRackEquipmentParamDTOList;
    }


    @Override
    public List<ComputerRackEquipmentParamDTO> updateComputerRacksEquipments(ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO) {
        this.deleteByIds(computerRackEquipmentRequestDTO.getComputerRackIds());
        return this.createComputerRacksEquipments(computerRackEquipmentRequestDTO);
    }

    @Override
    public void deleteByIds(String ids) {
        List<Integer> idList = StrUtil.split(ids, ",").stream().filter(Objects::nonNull).map(i->Integer.parseInt(i)).collect(Collectors.toList());
        computerRackEquipmentRepository.delete(Wrappers.lambdaQuery(ComputerRackEquipmentMap.class)
                .in(ComputerRackEquipmentMap::getComputerRackId, idList));
    }
}