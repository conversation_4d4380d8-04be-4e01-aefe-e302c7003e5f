package com.siteweb.computerrack.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.computerrack.entity.UDevice;
import com.siteweb.computerrack.mapper.UDeviceMapper;
import com.siteweb.computerrack.service.UDeviceService;
import com.siteweb.computerrack.vo.UDeviceSelect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class UDeviceServiceImpl implements UDeviceService {
    @Autowired
    UDeviceMapper uDeviceMapper;
    @Override
    public List<UDevice> findAll() {
        return uDeviceMapper.findAll();
    }

    @Override
    public UDevice findByRackId(Integer rackId) {
        return uDeviceMapper.selectOne(Wrappers.<UDevice>emptyWrapper()
                                               .lambda()
                                               .eq(UDevice::getRackId, rackId));
    }

    @Override
    public String findUDeviceNumberByRackId(Integer rackId) {
        LambdaQueryWrapper<UDevice> queryWrapper = Wrappers.lambdaQuery(UDevice.class)
                                                 .select(UDevice::getUDeviceNumber)
                                                 .eq(UDevice::getRackId, rackId);
        UDevice uDevice = uDeviceMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(uDevice)) {
            return null;
        }
        return uDevice.getUDeviceNumber();
    }


    @Override
    public List<UDeviceSelect> findSelect() {
        return uDeviceMapper.findSelect();
    }


    @Override
    public void updateRackNumber(String uDeviceNumber, Integer rackId) {
        if (CharSequenceUtil.isBlank(uDeviceNumber)) {
            return;
        }
        LambdaQueryWrapper<UDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UDevice::getUDeviceNumber, uDeviceNumber);
        UDevice uDevice = uDeviceMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNull(uDevice)) {
            return;
        }
        setUDeviceRackIdIsNull(rackId);
        uDevice.setUpdateTime(new Date());
        uDevice.setRackId(rackId);
        uDeviceMapper.updateById(uDevice);
    }

    /**
     * 设置u位管理器的绑定的机架为null(由于一对一关系需要把以前绑定的机架id置为null)
     * @param rackId 机架id
     */
    @Override
    public Boolean setUDeviceRackIdIsNull(Integer rackId) {
        if (ObjectUtil.isNull(rackId)) {
            return Boolean.FALSE;
        }
        return uDeviceMapper.update(null, Wrappers.<UDevice>lambdaUpdate()
                                           .eq(UDevice::getRackId, rackId)
                                           .set(UDevice::getRackId, null)
                                           .set(UDevice::getUpdateTime, new Date())) > 0;
    }

    @Override
    public boolean uDeviceBindRack(String uDeviceNumber, Integer rackId) {
        this.setUDeviceRackIdIsNull(rackId);
        this.updateRackNumber(uDeviceNumber,rackId);
        return true;
    }
}
