package com.siteweb.computerrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.dto.ITDeviceModelmportDTO;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.entity.ITDeviceModel;
import com.siteweb.computerrack.mapper.ITDeviceModelMapper;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceModelService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.entity.DataItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.Pageable;

import java.util.*;
import java.util.stream.Collectors;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service()
//@RequiredArgsConstructor
public class ITDeviceModelServiceImpl implements ITDeviceModelService {

    private static final String COMMON_FIELD_CANNOT_EMPTY = "common.field.cannotEmpty";
    @Autowired
    private ITDeviceModelMapper iTDeviceModelRepository;

    @Autowired
    @Lazy
    private ComputerRackService computerRackService;

    @Autowired
    @Lazy
    private ITDeviceService iTDeviceService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private DataItemService dataItemService;

    @Override
    public boolean itDeviceModelNameExist(Integer iTDeviceModelId, String iTDeviceModelName) {
        return iTDeviceModelRepository.selectCount(Wrappers.lambdaQuery(ITDeviceModel.class)
                .eq(ITDeviceModel::getITDeviceModelName, iTDeviceModelName)
                .ne(ObjectUtil.isNotNull(iTDeviceModelId), ITDeviceModel::getITDeviceModelId, iTDeviceModelId)) > 0;
    }

    @Override
    public ITDeviceModel getDeviceModelByName(String iTDeviceModelName) {
        return iTDeviceModelRepository.selectOne(Wrappers.lambdaQuery(ITDeviceModel.class)
                .eq(ITDeviceModel::getITDeviceModelName, iTDeviceModelName));
    }

    @Override
    public List<ITDeviceModel> findITDeviceModels() {
        return iTDeviceModelRepository.findAll();
    }

    @Override
    public Page<ITDeviceModel> findITDeviceModelsByPage(Pageable pageable, String keyword) {
        // 获取所有IT设备模型
        List<ITDeviceModel> allModels = iTDeviceModelRepository.findAll();

        // 首先补充类型名称
        List<DataItem> categoryItems = dataItemService.findByEntryId(DataEntryEnum.IT_EQUIPMENT_MODEL_TYPE.getValue());
        Map<Integer, String> categoryMap = categoryItems.stream()
                .collect(Collectors.toMap(DataItem::getItemId, DataItem::getItemValue, (v1, v2) -> v1));

        allModels.forEach(model -> {
            if (model.getCategoryId() != null) {
                model.setCategoryName(categoryMap.get(model.getCategoryId()));
            }
        });

        // 根据关键字过滤数据
        List<ITDeviceModel> filteredModels = allModels.stream()
                .filter(model -> {
                    if (CharSequenceUtil.isBlank(keyword)) {
                        return true;
                    }

                    boolean nameMatch = CharSequenceUtil.containsIgnoreCase(model.getITDeviceModelName(), keyword);

                    boolean categoryNameMatch = CharSequenceUtil.isNotBlank(model.getCategoryName()) &&
                            CharSequenceUtil.containsIgnoreCase(model.getCategoryName(), keyword);

                    boolean modelNameMatch = CharSequenceUtil.isNotBlank(model.getModel()) &&
                            CharSequenceUtil.containsIgnoreCase(model.getModel(), keyword);

                    boolean manufactorMatch = CharSequenceUtil.isNotBlank(model.getManufactor()) &&
                            CharSequenceUtil.containsIgnoreCase(model.getManufactor(), keyword);

                    boolean brandMatch = CharSequenceUtil.isNotBlank(model.getBrand()) &&
                            CharSequenceUtil.containsIgnoreCase(model.getBrand(), keyword);

                    return nameMatch || categoryNameMatch || manufactorMatch || brandMatch || modelNameMatch;
                })
                .toList();

        List<ITDeviceModel> sortedModels = applySorting(filteredModels, pageable.getSort());

        Page<ITDeviceModel> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize(), sortedModels.size(), false);

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), sortedModels.size());

        List<ITDeviceModel> pageContent = start < end ?
                sortedModels.subList(start, end) :
                Collections.emptyList();
        page.setRecords(pageContent);
        page.setCurrent(page.getCurrent() - 1L);
        return page;
    }

    @Override
    public ITDeviceModel createITDeviceModel(ITDeviceModel iTDeviceModel) {
        iTDeviceModelRepository.insert(iTDeviceModel);
        return iTDeviceModel;
    }

    @Override
    public List<ImportErrorInfoDTO> importITDeviceModels(List<ITDeviceModelmportDTO> itDeviceModels) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<ITDeviceModel> itDeviceModelInsertList = new ArrayList<>();
        List<ITDeviceModel> itDeviceModelUpdateList = new ArrayList<>();
        List<DataItem> categoryNameItems = dataItemService.findByEntryId(DataEntryEnum.IT_EQUIPMENT_MODEL_TYPE.getValue());
        for (int i = 0; i < itDeviceModels.size(); i++) {
            ITDeviceModelmportDTO itDeviceModelDto = itDeviceModels.get(i);
            if (verifyDataExist(itDeviceModelDto, importErrorInfoList, i)) continue;
            Integer categoryId = categoryNameItems.stream().filter(f -> Objects.equals(f.getItemValue(), itDeviceModelDto.getCategoryName()))
                    .findFirst().map(DataItem::getItemId).orElse(null);
            if (Objects.isNull(categoryId)) {
                // 当模型分类不存在时，则创建
                DataItem dataItemAndReturn = dataItemService.createDataItemAndReturn(DataEntryEnum.IT_EQUIPMENT_MODEL_TYPE.getValue(), itDeviceModelDto.getCategoryName());
                categoryId = dataItemAndReturn.getItemId();
            }
            itDeviceModelDto.setCategoryId(categoryId);
            ITDeviceModel itDeviceModelDb = getDeviceModelByName(itDeviceModelDto.getITDeviceModelName());
            ITDeviceModel itDeviceModel = itDeviceModelDto.toITDeviceModel();
            if (Objects.isNull(itDeviceModelDb)) {
                // 新增
                itDeviceModelInsertList.add(itDeviceModel);
            } else {
                // 修改
                itDeviceModel.setITDeviceModelId(itDeviceModelDb.getITDeviceModelId());
                itDeviceModel.setModelFile(itDeviceModelDb.getModelFile());
                itDeviceModelUpdateList.add(itDeviceModel);
            }
        }
        iTDeviceModelRepository.insert(itDeviceModelInsertList);
        iTDeviceModelRepository.updateById(itDeviceModelUpdateList);
        return importErrorInfoList;
    }

    /**
     * 校验数据是否存在
     */
    private boolean verifyDataExist(ITDeviceModelmportDTO itDeviceModelDto, List<ImportErrorInfoDTO> importErrorInfoList, int i) {
        boolean flag = false;
        if (CharSequenceUtil.isBlank(itDeviceModelDto.getITDeviceModelName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.iTDeviceModelName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(itDeviceModelDto.getCategoryName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.categoryName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(itDeviceModelDto.getModel())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.model, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (ObjectUtil.isNull(itDeviceModelDto.getUnitHeight())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.unitHeight, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (ObjectUtil.isNull(itDeviceModelDto.getRatePower())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.ratePower, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (ObjectUtil.isNull(itDeviceModelDto.getRateCooling())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.rateCooling, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (ObjectUtil.isNull(itDeviceModelDto.getRateWeight())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ITDeviceModelmportDTO.Fields.rateWeight, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        return flag;
    }

    @Override
    public void deleteById(Integer iTDeviceModelId) {
        List<ITDevice> itDevices = iTDeviceService.findITDevicesByModelId(iTDeviceModelId);
        if (itDevices.size() == 0) {
            iTDeviceModelRepository.deleteById(iTDeviceModelId);
        }
    }

    @Override
    public boolean findReferences(Integer iTDeviceModelId) {
        List<ITDevice> itDevices = iTDeviceService.findITDevicesByModelId(iTDeviceModelId);
        return itDevices.size() > 0;
    }


    @Override
    public ITDeviceModel updateITDeviceModel(ITDeviceModel iTDeviceModel) {
        List<Integer> computerRackIds = new ArrayList<>();
        iTDeviceModelRepository.updateById(iTDeviceModel);
        List<ITDevice> itDevices = iTDeviceService.findITDevicesByModelId(iTDeviceModel.getITDeviceModelId());
        for (ITDevice itDevice : itDevices) {
            if (itDevice.getComputerRackId() != null && !computerRackIds.contains(itDevice.getComputerRackId())) {
                computerRackIds.add(itDevice.getComputerRackId());
            }
        }
        if (CollUtil.isNotEmpty(computerRackIds)) {
            computerRackService.updateCapacityValueByComputerRackIds(computerRackIds);
        }
        return iTDeviceModel;
    }

    @Override
    public ITDeviceModel findById(Integer iTDeviceModelId) {
        return iTDeviceModelRepository.findByiITDeviceModelId(iTDeviceModelId);
    }

    @Override
    public ITDeviceModel findByITDeviceModelName(String itDeviceModelName) {
        return iTDeviceModelRepository.findByITDeviceModelName(itDeviceModelName);
    }

    @Override
    public Integer findReferencesCount(Integer iTDeviceModelId) {
        return iTDeviceModelRepository.findReferencesCount(iTDeviceModelId);
    }

    @Override
    public long countByCategoryId(Integer categoryId) {
        return iTDeviceModelRepository.selectCount(
                Wrappers.lambdaQuery(ITDeviceModel.class).eq(ITDeviceModel::getCategoryId, categoryId));
    }

    /**
     * 应用排序到模型列表
     *
     * @param models 需要排序的模型列表
     * @param sort 排序参数
     * @return 排序后的列表
     */
    private List<ITDeviceModel> applySorting(List<ITDeviceModel> models, org.springframework.data.domain.Sort sort) {
        if (sort == null || !sort.iterator().hasNext()) {
            // 默认按模型名称排序
            return models.stream()
                    .sorted(Comparator.comparing(ITDeviceModel::getITDeviceModelName))
                    .collect(Collectors.toList());
        }

        List<ITDeviceModel> result = new ArrayList<>(models);

        // 处理排序参数
        sort.forEach(order -> {
            String property = order.getProperty();
            boolean isAscending = order.getDirection().isAscending();

            Comparator<ITDeviceModel> comparator = null;

            // 根据属性名称选择比较器
            switch (property.toLowerCase()) {
                case "itdevicemodelname":
                    comparator = Comparator.comparing(ITDeviceModel::getITDeviceModelName,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
                case "categoryname":
                    comparator = Comparator.comparing(
                            model -> model.getCategoryName() != null ? model.getCategoryName() : "",
                            String::compareToIgnoreCase);
                    break;
                case "unitheight":
                    comparator = Comparator.comparing(ITDeviceModel::getUnitHeight,
                            Comparator.nullsLast(Integer::compareTo));
                    break;
                case "manufactor":
                    comparator = Comparator.comparing(
                            model -> model.getManufactor() != null ? model.getManufactor() : "",
                            String::compareToIgnoreCase);
                    break;
                case "brand":
                    comparator = Comparator.comparing(
                            model -> model.getBrand() != null ? model.getBrand() : "",
                            String::compareToIgnoreCase);
                    break;
                case "ratepower":
                    comparator = Comparator.comparing(ITDeviceModel::getRatePower,
                            Comparator.nullsLast(Double::compareTo));
                    break;
                case "ratecooling":
                    comparator = Comparator.comparing(ITDeviceModel::getRateCooling,
                            Comparator.nullsLast(Double::compareTo));
                    break;
                case "rateweight":
                    comparator = Comparator.comparing(ITDeviceModel::getRateWeight,
                            Comparator.nullsLast(Double::compareTo));
                    break;
                default:
                    // 如果属性不支持，则使用默认的模型名称排序
                    comparator = Comparator.comparing(ITDeviceModel::getITDeviceModelName,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
            }

            // 如果是降序，则反转比较器
            if (!isAscending) {
                comparator = comparator.reversed();
            }

            // 应用排序
            result.sort(comparator);
        });

        return result;
    }
}
