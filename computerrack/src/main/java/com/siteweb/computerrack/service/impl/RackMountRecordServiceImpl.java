package com.siteweb.computerrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.entity.RackMountRecord;
import com.siteweb.computerrack.enumeration.ITDeviceOperatingStatus;
import com.siteweb.computerrack.mapper.RackMountRecordMapper;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceOperateService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.computerrack.vo.RackChangeRecordVo;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.service.HAStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;

@Service()
@Component()
@EnableScheduling
//@RequiredArgsConstructor
public class RackMountRecordServiceImpl {

    /**
     * 上架
     */
    @SuppressWarnings("SpellCheckingInspection")
    public static final Integer PUTINSHELF = 1;
    /**
     * 下架
     */
    @SuppressWarnings("SpellCheckingInspection")
    public static final Integer TAKEFROMSHELF = 2;
    private final Logger log = LoggerFactory.getLogger(RackMountRecordServiceImpl.class);
    @Autowired
    private RackMountRecordMapper rackMountRecordRepository;
    @Autowired
    @Lazy
    private ITDeviceService iTDeviceService;
    @Autowired
    @Lazy
    private ITDeviceOperateService iTDeviceOperateService;
    @Autowired
    @Lazy
    private ComputerRackService computerRackService;
    @Autowired
    HAStatusService haStatusService;
    @Autowired
    ResourceStructureService resourceStructureService;
    @Scheduled(fixedDelay = 60 * 1000)
    protected void schedule() {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP:机架上下架定时任务退出");
            return;
        }
        List<RackMountRecord> records = this.rackMountRecordRepository.findUnexpired();
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Set<Integer> rackIds = new HashSet<>();
        for (RackMountRecord mountRecord : records) {
            if (mountRecord.getOperateState().equals(TAKEFROMSHELF)) {
                // 下架
                this.takeFromShelf(mountRecord);
            } else if (mountRecord.getOperateState().equals(PUTINSHELF)) {
                // 上架
                this.putInShelf(mountRecord);
            }
            // 设置已处理，过期
            mountRecord.setExpired(true);
            this.rackMountRecordRepository.updateById(mountRecord);
            rackIds.add(mountRecord.getComputerRackId());
        }
        //更新机架容量
        computerRackService.updateCapacityValueByComputerRackIds(new ArrayList<>(rackIds));
    }


    /**
     * 下架处理
     *
     * @param mountRecord
     */
    private void takeFromShelf(RackMountRecord mountRecord) {
        log.info("处理下架记录 => {}", mountRecord.getId());
        ComputerRack computerRack = this.computerRackService.findComputerRack(mountRecord.getComputerRackId());
        if (computerRack == null) {
            // 机架不存在
            log.error("U位下架失败，记录Id:{}：未找到id为{}的机架对象。", mountRecord.getId(), mountRecord.getComputerRackId());
            return;
        }
        ITDevice itDevice = iTDeviceService.findByComputerRackPos(mountRecord.getComputerRackId(),mountRecord.getITDeviceId(), mountRecord.getUIndex());
        if (itDevice == null) {
            log.error("下架失败,rackId:{},itDeviceId:{},uIndex:{},不存在", mountRecord.getComputerRackId(), mountRecord.getITDeviceId(), mountRecord.getUIndex());
            return;
        }
        log.info("开始下架，{} <= {}[{}]", itDevice.getITDeviceName(), computerRack.getComputerRackName(), mountRecord.getUIndex());
        ITDeviceOperatingStatus status = this.iTDeviceOperateService.takeFromShelf(mountRecord.getComputerRackId(), mountRecord.getUIndex());
        if (!status.equals(ITDeviceOperatingStatus.SUCCESS)) {
            String message = String.format("U位下架失败，返回值：%s", status);
            log.error(message);
        }
    }


    /**
     * 上架处理
     *
     * @param mountRecord
     */
    private void putInShelf(RackMountRecord mountRecord) {
        log.info("处理上架记录 => {}", mountRecord.getId());
        ComputerRack computerRack = this.computerRackService.findComputerRack(mountRecord.getComputerRackId());
        ITDevice itDevice = this.iTDeviceService.findITDevice(mountRecord.getITDeviceId());

        if (computerRack == null) {
            log.error("U位上架失败，记录Id:{}：未找到id为{}的机架对象。", mountRecord.getId(), mountRecord.getComputerRackId());
            return;
        }

        if (itDevice == null) {
            log.error("U位上架失败，记录Id:{}：未找到id为{}的IT设备对象。", mountRecord.getId(), mountRecord.getComputerRackId());
            return;
        }
        log.info("开始上架，{} => {}[{}]", itDevice.getITDeviceName(), computerRack.getComputerRackName(), mountRecord.getUIndex());
        ITDeviceOperatingStatus status = this.iTDeviceOperateService.putInShelf(mountRecord.getITDeviceId(), mountRecord.getComputerRackId(), mountRecord.getUIndex());
        if (!status.equals(ITDeviceOperatingStatus.SUCCESS)) {
            log.error("U位上架失败，返回值：{}", status);
        }
    }

    public List<RackChangeRecordVo> findTopN(Integer userId, Integer top, String resourceStructureId){
        List<Integer> resourceStructureIds = resourceStructureService.findChildResourceStructureIdsByUserId(userId, resourceStructureId);
        return rackMountRecordRepository.findTopN(top, resourceStructureIds);
    }

    /**
     * 机架上架记录
     * @param rackId 机架id
     * @param itDeviceId it设备id
     * @param uIndex 起始u位
     */
    public void putInShelfRecord(Integer rackId, Integer itDeviceId, Integer uIndex) {
        this.mountRecord(rackId, itDeviceId, uIndex, PUTINSHELF);
    }

    /**
     * 机架下架记录
     * @param rackId 机架id
     * @param itDeviceId it设备id
     * @param uIndex 起始u位
     */
    public void takeFromShelfRecord(Integer rackId, Integer itDeviceId, Integer uIndex) {
        this.mountRecord(rackId, itDeviceId, uIndex, TAKEFROMSHELF);
    }

    /**
     * 上下架记录生成
     *
     * @param rackId     机架id
     * @param itDeviceId it设备id
     * @param uIndex     起始u位
     * @param state      状态 1上架  2下架
     */
    private void mountRecord(Integer rackId, Integer itDeviceId, Integer uIndex, Integer state) {
        if (existsUntreatedRecord(rackId,itDeviceId,uIndex,state)) {
            return;
        }
        RackMountRecord rackMountRecord = new RackMountRecord();
        rackMountRecord.setComputerRackId(rackId);
        rackMountRecord.setExpired(true);
        rackMountRecord.setITDeviceId(itDeviceId);
        rackMountRecord.setOperateState(state);
        rackMountRecord.setOperateTime(new Date());
        rackMountRecord.setUIndex(uIndex);
        rackMountRecordRepository.insert(rackMountRecord);
    }

    /**
     * 是否存在未处理的上下架记录，
     * 如果存在说明it设备被自动上下架了，则无需重复插入上下架记录
     * @param rackId 机架id
     * @param itDeviceId 设备id
     * @param uIndex 起始U位
     * @param state 状态 1上架  2下架
     * @return boolean true存在 false不存在
     */
    private boolean existsUntreatedRecord(Integer rackId, Integer itDeviceId, Integer uIndex, Integer state) {
        return rackMountRecordRepository.exists(
                Wrappers.lambdaQuery(RackMountRecord.class)
                        .eq(RackMountRecord::getComputerRackId, rackId)
                        .eq(RackMountRecord::getITDeviceId, itDeviceId)
                        .eq(RackMountRecord::getUIndex, uIndex)
                        .eq(RackMountRecord::getOperateState, state)
                        .isNull(RackMountRecord::getExpired));
    }
}
