package com.siteweb.computerrack.service.impl;

import cn.hutool.core.date.DateUtil;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.computerrack.dto.ComputerRackITScreenParam;
import com.siteweb.computerrack.entity.ComputerRackSignalMap;
import com.siteweb.computerrack.entity.ComputerRackSignalRecord;
import com.siteweb.computerrack.enumeration.ComputerRackCapacity;
import com.siteweb.computerrack.enumeration.DateTypeEnum;
import com.siteweb.computerrack.manager.ComputerRackSignalRecordManage;
import com.siteweb.computerrack.service.ComputerRackITScreenService;
import com.siteweb.computerrack.service.ComputerRackSignalService;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.utility.vo.NameValueVO;
import com.siteweb.utility.vo.RacksPowerLoadRateVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@RequiredArgsConstructor
public class ComputerRackITScreenServiceImpl implements ComputerRackITScreenService {
    private final ComputerRackSignalService computerRackSignalService;
    private final CapacityAttributeService capacityAttributeService;
    private final ComputerRackSignalRecordManage computerRackSignalRecordManage;


    @Override
    public RacksPowerLoadRateVO getComputerRacksPowerLoadRate(Integer rackId) {
        // 获取机柜额定功率
        ResourceObject resourceObject = new ResourceObject(rackId, SourceType.COMPUTERRACK.value());
        CapacityAttribute attribute = capacityAttributeService.findAttribute(resourceObject, ComputerRackCapacity.RACK_POWER.getValue());
        RacksPowerLoadRateVO racksPowerLoadRateVO = new RacksPowerLoadRateVO();
        Double ratedPower = Optional.ofNullable(attribute).map(CapacityAttribute::getRatedCapacity).orElse(0.0);
        racksPowerLoadRateVO.setRatedPower(NumberUtil.roundTo2DecimalPlaces(ratedPower));
        // 获取当前功率
        double currentActivePower = getCurrentActivePower(rackId);
        racksPowerLoadRateVO.setCurrentActivePower(NumberUtil.roundTo2DecimalPlaces(currentActivePower));
        // 计算电力负载率
        double ratedCapacity = Optional.ofNullable(attribute).map(CapacityAttribute::getRatedCapacity).orElse(0.0);
        racksPowerLoadRateVO.setPowerLoadRate(NumberUtil.calculatePercentage(currentActivePower, ratedCapacity));
        return racksPowerLoadRateVO;
    }

    /**
     * 获取当前功率
     */
    private double getCurrentActivePower(Integer rackId) {
        // 通过绑定的功率表达式计算当前功率
        double currentActivePower = 0.0;
        ComputerRackSignalMap computerRackSignalMap = computerRackSignalService.findByRackId(rackId);
        if (Objects.nonNull(computerRackSignalMap) && Objects.nonNull(computerRackSignalMap.getPowerExpression())) {
            currentActivePower = computerRackSignalService.calculatePowerExpression(computerRackSignalMap.getPowerExpression());
        }
        return currentActivePower;
    }


    @Override
    public List<NameValueVO> getComputerRackPowerTrend(ComputerRackITScreenParam param) {
        if (Objects.isNull(param.getStartTime()) || Objects.isNull(param.getEndTime())) {
            throw new BusinessException("日期不能为空");
        }
        if (param.getStartTime().after(param.getEndTime())) {
            throw new BusinessException("日期格式异常 起始时间不能大于结束时间");
        }
        DateTypeEnum dateTypeEnum = DateTypeEnum.getByValue(param.getDataType());
        if (Objects.isNull(dateTypeEnum)) {
            throw new BusinessException("非法的日期类型");
        }
        // 获取机架信号历史数据
        Map<Object, ComputerRackSignalRecord> recordMap = computerRackSignalRecordManage.getComputerRackPowerTrendByDateType(
                param.getRackId(), param.getStartTime(), param.getEndTime(), dateTypeEnum);
        List<NameValueVO> results = new ArrayList<>();
        switch (dateTypeEnum) {
            case QUARTERLY -> {
                // 结果要显示到季度
                int startQuarter = DateUtil.quarter(param.getStartTime());
                int endQuarter = DateUtil.quarter(param.getEndTime());
                for (int quarter = startQuarter; quarter <= endQuarter; quarter++) {
                    NameValueVO nameValueVO = new NameValueVO();
                    nameValueVO.setName(String.format("第%d季度", quarter));
                    ComputerRackSignalRecord rackSignalRecord = recordMap.get(quarter);
                    nameValueVO.setValue(rackSignalRecord != null ? NumberUtil.roundTo2DecimalPlaces(rackSignalRecord.getPowerValue()) : null);
                    results.add(nameValueVO);
                }
            }
            case QUARTER_DAY -> {
                // 在同一个月 结果要显示到日 取每天最后一条记录出来
                LocalDate startDate = param.getStartTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                LocalDate endDate = param.getEndTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM月dd日");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    NameValueVO nameValueVO = new NameValueVO();
                    nameValueVO.setName(date.format(formatter));
                    ComputerRackSignalRecord rackSignalRecord = recordMap.get(date);
                    nameValueVO.setValue(rackSignalRecord != null ? NumberUtil.roundTo2DecimalPlaces(rackSignalRecord.getPowerValue()) : null);
                    results.add(nameValueVO);
                }
            }
            default -> {
                int monthDifference = DateUtil.month(param.getEndTime()) - DateUtil.month(param.getStartTime());
                if (monthDifference == 0) {
                    // 在同一个月 结果要显示到日 取每天最后一条记录出来
                    int startDay = DateUtil.dayOfMonth(param.getStartTime());
                    int endDay = DateUtil.dayOfMonth(param.getEndTime());
                    for (int day = startDay; day <= endDay; day++) {
                        NameValueVO nameValueVO = new NameValueVO();
                        nameValueVO.setName(String.format("%02d日", day));
                        ComputerRackSignalRecord rackSignalRecord = recordMap.get(day);
                        nameValueVO.setValue(rackSignalRecord != null ? NumberUtil.roundTo2DecimalPlaces(rackSignalRecord.getPowerValue()) : null);
                        results.add(nameValueVO);
                    }
                } else {
                    // 跨月 要显示到月 取每月最后一条记录出来
                    int startMonth = DateUtil.month(param.getStartTime()) + 1;
                    int endMonth = DateUtil.month(param.getEndTime()) + 1;
                    for (int month = startMonth; month <= endMonth; month++) {
                        NameValueVO nameValueVO = new NameValueVO();
                        nameValueVO.setName(String.format("%02d月", month));
                        ComputerRackSignalRecord rackSignalRecord = recordMap.get(month);
                        nameValueVO.setValue(rackSignalRecord != null ?  NumberUtil.roundTo2DecimalPlaces(rackSignalRecord.getPowerValue()) : null);
                        results.add(nameValueVO);
                    }
                }
            }
        }

        return results;
    }

    @Override
    public List<NameValueVO> getComputerRackPower(ComputerRackITScreenParam param) {
        List<NameValueVO> results = new ArrayList<>();
        List<ComputerRackSignalRecord> computerRackSignalRecords = computerRackSignalRecordManage.getComputerRackSignalRecords(param.getRackId(), param.getStartTime(), param.getEndTime());
        OptionalDouble max = computerRackSignalRecords.stream().filter(f -> Objects.nonNull(f.getPowerValue())).mapToDouble(ComputerRackSignalRecord::getPowerValue).max();
        OptionalDouble min = computerRackSignalRecords.stream().filter(f -> Objects.nonNull(f.getPowerValue())).mapToDouble(ComputerRackSignalRecord::getPowerValue).min();
        // 获取当前功率
        Double currentActivePower = getCurrentActivePower(param.getRackId());
        results.add(NameValueVO.builder()
                .name("当前功率")
                .value(NumberUtil.roundTo2DecimalPlaces(currentActivePower))
                .build());
        results.add(NameValueVO.builder()
                .name("最大功率")
                .value(NumberUtil.roundTo2DecimalPlaces(max.orElse(0)))
                .build());
        results.add(NameValueVO.builder()
                .name("最小功率")
                .value(NumberUtil.roundTo2DecimalPlaces(min.orElse(0)))
                .build());
        return results;
    }

    @Override
    public List<NameValueVO> getComputerRackPowerLoadRate(ComputerRackITScreenParam param) {
        DateTypeEnum dateTypeEnum = DateTypeEnum.getByValue(param.getDataType());
        if (Objects.isNull(param.getStartTime()) || Objects.isNull(param.getEndTime())) {
            throw new BusinessException("日期不能为空");
        }
        if (param.getStartTime().after(param.getEndTime())) {
            throw new BusinessException("日期格式异常 起始时间不能大于结束时间");
        }
        if (Objects.isNull(dateTypeEnum)) {
            throw new BusinessException("非法的日期类型");
        }
        // 获取机柜额定功率
        ResourceObject resourceObject = new ResourceObject(param.getRackId(), SourceType.COMPUTERRACK.value());
        CapacityAttribute attribute = capacityAttributeService.findAttribute(resourceObject, ComputerRackCapacity.RACK_POWER.getValue());
        double ratedPower = Optional.ofNullable(attribute).map(CapacityAttribute::getRatedCapacity).orElse(0.0);
        // 获取机架信号历史数据
        Map<Object, ComputerRackSignalRecord> recordMap = computerRackSignalRecordManage.getComputerRackPowerTrendByDateType(
                param.getRackId(), param.getStartTime(), param.getEndTime(), dateTypeEnum);
        return switch (dateTypeEnum) {
            case QUARTERLY -> {
                List<NameValueVO> results = new ArrayList<>();
                // 按季度分组 获取每季度最新的数据
                int startQuarter = DateUtil.quarter(param.getStartTime());
                int endQuarter = DateUtil.quarter(param.getEndTime());
                for (int quarter = startQuarter; quarter <= endQuarter; quarter++) {
                    NameValueVO nameValueVO = new NameValueVO();
                    nameValueVO.setName(String.format("第%d季度", quarter));
                    ComputerRackSignalRecord rackSignalRecord = recordMap.get(quarter);
                    double power = rackSignalRecord != null ? rackSignalRecord.getPowerValue() : 0.0;
                    nameValueVO.setValue(NumberUtil.calculatePercentage(power, ratedPower));
                    results.add(nameValueVO);
                }
                yield results;
            }
            case QUARTER_DAY -> {
                List<NameValueVO> results = new ArrayList<>();
                //  结果要显示到日 取每天最后一条记录出来
                LocalDate startDate = param.getStartTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                LocalDate endDate = param.getEndTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM月dd日");
                for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                    NameValueVO nameValueVO = new NameValueVO();
                    nameValueVO.setName(date.format(formatter));
                    ComputerRackSignalRecord rackSignalRecord = recordMap.get(date);
                    double power = rackSignalRecord != null ? rackSignalRecord.getPowerValue() : 0.0;
                    nameValueVO.setValue(NumberUtil.calculatePercentage(power, ratedPower));
                    results.add(nameValueVO);
                }
                yield results;
            }
            default -> {
                List<NameValueVO> results = new ArrayList<>();
                int monthDifference = DateUtil.month(param.getEndTime()) - DateUtil.month(param.getStartTime());
                if (monthDifference == 0) {
                    // 在同一个月 结果要显示到日 取每天最后一条记录出来
                    int startDay = DateUtil.dayOfMonth(param.getStartTime());
                    int endDay = DateUtil.dayOfMonth(param.getEndTime());
                    for (int day = startDay; day <= endDay; day++) {
                        NameValueVO nameValueVO = new NameValueVO();
                        nameValueVO.setName(String.format("%02d日", day));
                        ComputerRackSignalRecord rackSignalRecord = recordMap.get(day);
                        double power = rackSignalRecord != null ? rackSignalRecord.getPowerValue() : 0.0;
                        nameValueVO.setValue(NumberUtil.calculatePercentage(power, ratedPower));
                        results.add(nameValueVO);
                    }

                } else {
                    // 跨月 要显示到月 取每月最后一条记录出来
                    int startMonth = DateUtil.month(param.getStartTime()) + 1;
                    int endMonth = DateUtil.month(param.getEndTime()) + 1;
                    for (int month = startMonth; month <= endMonth; month++) {
                        NameValueVO nameValueVO = new NameValueVO();
                        nameValueVO.setName(String.format("%02d月", month));
                        ComputerRackSignalRecord rackSignalRecord = recordMap.get(month);
                        double power = rackSignalRecord != null ? rackSignalRecord.getPowerValue() : 0.0;
                        nameValueVO.setValue(NumberUtil.calculatePercentage(power, ratedPower));
                        results.add(nameValueVO);
                    }
                }
                yield results;
            }
        };
    }


}
