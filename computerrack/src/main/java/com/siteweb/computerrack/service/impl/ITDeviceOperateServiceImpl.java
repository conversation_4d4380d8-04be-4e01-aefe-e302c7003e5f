package com.siteweb.computerrack.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.asset.entity.AssetDevice;
import com.siteweb.asset.mapper.AssetDeviceMapper;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.computerrack.dto.ComputerRackDTO;
import com.siteweb.computerrack.dto.ITDeviceImportDTO;
import com.siteweb.computerrack.dto.ITDeviceOperateResultDTO;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.entity.ITDeviceModel;
import com.siteweb.computerrack.enumeration.ComputerRackCapacity;
import com.siteweb.computerrack.enumeration.ITDeviceOperatingStatus;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceModelService;
import com.siteweb.computerrack.service.ITDeviceOperateService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * IT设备导入与上下架
 */
@Service()
//@RequiredArgsConstructor
public class ITDeviceOperateServiceImpl implements ITDeviceOperateService {

    private static final Integer SHEET1 = 1;
    private static final String UINDEX_NAME = "uindex";
    private static final String IT_DEVICE_MODEL_NAME_ERROR_INFO = "未找到IT设备模板。";
    private static final String IT_DEVICE_MODEL_NAME = "itDeviceModelName";
    private static final String ASSET_CODE = "assetCode";
    private static final String ASSET_CODE_NO_FIND = "assetCode No Find";
    private static final String COMPUTER_RACK_NAME = "computerRackName";
    Map<String, ITDeviceModel> itDeviceModelMap = new HashMap<>();
    Map<String, ITDevice> itDeviceMap = new HashMap<>();
    @Autowired
    @Lazy
    private ComputerRackService computerRackService;
    @Autowired
    @Lazy
    private ITDeviceService itDeviceService;
    @Autowired
    @Lazy
    private ITDeviceModelService itDeviceModelService;
    @Autowired
    @Lazy
    private CapacityAttributeService capacityAttributeService;
    @Autowired
    private RackMountRecordServiceImpl rackMountRecordService;
    @Autowired
    RackMountRecordServiceImpl RackMountRecordServiceImpl;
    @Autowired
    AssetDeviceMapper assetDeviceMapper;

    /**
     * IT设备导入
     *
     * @param itDeviceOperateTemplateList
     * @return
     */
    @Override
    @Transactional
    public List<ImportErrorInfoDTO> importList(List<ITDeviceImportDTO> itDeviceOperateTemplateList) {
        Map<String, ITDeviceModel> modelMap = itDeviceModelService.findITDeviceModels().stream().collect(Collectors.toMap(ITDeviceModel::getITDeviceModelName, v -> v));
        Map<String, Integer> assetMap = assetDeviceMapper.findAllIdAndCode().stream().collect(Collectors.toMap(AssetDevice::getAssetCode, AssetDevice::getAssetDeviceId));
        List<ImportErrorInfoDTO> importErrorInfoDTOList = new ArrayList<>();
        List<ComputerRack> needUpdateComputerRacks = new ArrayList<>();
        int index = 0;
        for (ITDeviceImportDTO template : itDeviceOperateTemplateList) {
            if (template.getComputerRackName() == null || template.getUIndex() == null) {
                template.setComputerRackName(null);
                template.setUIndex(null);
            }
            ITDevice itDevice = itDeviceService.findITDevice(template.getItDeviceName());
            // 设备模型
            ITDeviceModel itDeviceModel = modelMap.get(template.getItDeviceModelName());
            if (itDeviceModel == null) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(index, IT_DEVICE_MODEL_NAME, IT_DEVICE_MODEL_NAME_ERROR_INFO));
                index++;
                continue;
            }
            //资产绑定
            if (CharSequenceUtil.isNotBlank(template.getAssetCode()) && !assetMap.containsKey(template.getAssetCode())) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(index, ASSET_CODE, ASSET_CODE_NO_FIND));
                index++;
                continue;
            }

            if (itDevice == null) {
                // 新建IT设备对象
                ITDevice _itDevice = template.toITDevice();
                _itDevice.setLaunchDate(template.getPurchaseDate());
                _itDevice.setITDeviceModelId(itDeviceModel.getITDeviceModelId());
                _itDevice.setAssetDeviceId(assetMap.get(template.getAssetCode()));
                itDevice = itDeviceService.createITDevice(_itDevice);
            } else {
                // 修改IT设备对象
                itDevice.setITDeviceModelId(itDeviceModel.getITDeviceModelId());
                itDevice.setITDeviceName(template.getItDeviceName());
                itDevice.setBusiness(template.getBusiness());
                itDevice.setRemark(template.getRemark());
                itDevice.setSerialNumber(template.getSerialNumber());
                itDevice.setCustomer(template.getCustomer());
                itDevice.setIpaddr(template.getIpaddr());
                itDevice.setAssetDeviceId(assetMap.get(template.getAssetCode()));
                itDeviceService.updateITDevice(itDevice);
            }
            if (itDevice == null) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(index, COMPUTER_RACK_NAME, "创建IT设备失败,数据库操作异常。"));
                index++;
                continue;
            }
            //it设备绑定的机架位置是否有变化
            Integer newRackId = Optional.ofNullable(computerRackService.findByComputerRackName(template.getComputerRackName()))
                    .orElse(new ComputerRack())
                    .getComputerRackId();
            if (!positionIsChange(itDevice.getComputerRackId(), itDevice.getUIndex(), newRackId, template.getUIndex())) {
                index++;
                continue;
            }
            // 先下架
            if (itDevice.getComputerRackId() != null && itDevice.getUIndex() != null) {
                ITDevice itDeviceImport = itDeviceService.findITDevice(itDevice.getITDeviceId());
                ComputerRack computerRack = computerRackService.findComputerRack(itDevice.getComputerRackId());
                if (this.takeFromShelf(itDevice).equals(ITDeviceOperatingStatus.SUCCESS) && computerRack != null) {
                    //下架记录
                    rackMountRecordService.takeFromShelfRecord(itDeviceImport.getComputerRackId(), itDeviceImport.getITDeviceId(), itDeviceImport.getUIndex());
                    needUpdateComputerRacks.add(computerRack);
                }
            }
            // 再上架
            if (template.getComputerRackName() != null && template.getUIndex() != null) {
                ComputerRack computerRack = computerRackService.findByComputerRackName(template.getComputerRackName());
                if (computerRack != null) {
                    needUpdateComputerRacks.add(computerRack);
                    ITDeviceOperatingStatus status = this.putInShelf(itDevice, computerRack, template.getUIndex());
                    if (!status.equals(ITDeviceOperatingStatus.SUCCESS)) {
                        ITDeviceOperateResultDTO dor = new ITDeviceOperateResultDTO();
                        dor.setStatus(status);
                        dor.setMsg(status.getDescription());
                        importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(index, status.getField(), "上架失败:" + dor.getMsg()));
                    } else {
                        //上架成功记录上架记录
                        rackMountRecordService.putInShelfRecord(itDevice.getComputerRackId(), itDevice.getITDeviceId(), itDevice.getUIndex());
                    }
                }
            }
            index++;
        }

        if (!needUpdateComputerRacks.isEmpty()) {
            needUpdateComputerRacks = needUpdateComputerRacks.stream().distinct().collect(Collectors.toList());
            for (ComputerRack computerRack : needUpdateComputerRacks) {
                computerRackService.updateCapacityValues(computerRack);
            }
        }
        return importErrorInfoDTOList;
    }

    /**
     * it设备绑定的机架位置是否有变化
     *
     * @param oldRackId   旧机架id
     * @param oldUIndex   旧起始位置
     * @param newRackId   新机架id
     * @param newUIndexId 新起始位置
     * @return boolean it设备机架位置是否发生改变 true改变 false未改变
     */
    private boolean positionIsChange(Integer oldRackId, Integer oldUIndex, Integer newRackId, Integer newUIndexId) {
        //it设备机架位置是否发生改变
        return !(Objects.equals(oldRackId, newRackId) && Objects.equals(oldUIndex, newUIndexId));
    }


    /**
     * IT设备上架
     *
     * @param itDeviceId
     * @param computerRackId
     * @param uIndex
     * @return
     */
    @Override
    public ITDeviceOperatingStatus putInShelf(Integer itDeviceId, Integer computerRackId, Integer uIndex) {
        ITDevice itDevice = this.itDeviceService.findITDevice(itDeviceId);
        if (itDevice == null) return ITDeviceOperatingStatus.NOT_FOUND_ITDEVICE;
        ComputerRackDTO computerRack = computerRackService.findComputerRackDTO(computerRackId);
        if (computerRack == null) return ITDeviceOperatingStatus.NOT_FOUND_COMPUTERRACK;
        return this.putInShelf(itDevice, computerRack.toRack(), uIndex);
    }

    /**
     * IT设备上架
     *
     * @param itDeviceName
     * @param computerRackName
     * @param uIndex
     * @return
     */
    @Override
    public ITDeviceOperatingStatus putInShelf(String itDeviceName, String computerRackName, Integer uIndex) {
        ITDevice itDevice = this.itDeviceService.findITDevice(itDeviceName);
        if (itDevice == null) return ITDeviceOperatingStatus.NOT_FOUND_ITDEVICE;
        ComputerRackDTO computerRack = this.computerRackService.findDTOByComputerRackName(computerRackName);
        if (computerRack == null) return ITDeviceOperatingStatus.NOT_FOUND_COMPUTERRACK;
        return this.putInShelf(itDevice, computerRack.toRack(), uIndex);
    }

    /**
     * IT设备上架
     *
     * @param itDevice
     * @param computerRack
     * @param uIndex
     * @return
     */
    @Override
    public ITDeviceOperatingStatus putInShelf(ITDevice itDevice, ComputerRack computerRack, Integer uIndex) {
        if (uIndex <= 0) {
            // u位必须大于0
            return ITDeviceOperatingStatus.U_INDEX_MUST_GREATER_0;
        }
        if (computerRack == null) {
            // 没有找到机架
            return ITDeviceOperatingStatus.NOT_FOUND_COMPUTERRACK;
        }
        if (itDevice.getComputerRackId() != null && itDevice.getUIndex() != null) {
            // IT设备必须是闲置的
            return ITDeviceOperatingStatus.ITDEVICE_MUST_BE_IDLE;
        }

        Integer maxHeight = 42;
        CapacityAttribute attribute = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_SPACE.getValue());
        if (attribute != null) {
            maxHeight = attribute.getRatedCapacity().intValue();
        }
        if ((uIndex + itDevice.getUnitHeight() - 1) > maxHeight) {
            // u位超出最大范围
            return ITDeviceOperatingStatus.ITDEVICE_LOCATION_OUT_OF_RANGE;
        }
        // 判断 机柜上是否可以放置IT设备
        if (!this.canPutShelf(computerRack, uIndex, itDevice.getUnitHeight())) {
            // 该位置已有IT设备
            return ITDeviceOperatingStatus.U_POSITION_MUST_EMPTY;
        }
        try {
            //增加上架记录
            RackMountRecordServiceImpl.putInShelfRecord(computerRack.getComputerRackId(), itDevice.getITDeviceId(), uIndex);
            itDevice.setComputerRackId(computerRack.getComputerRackId());
            itDevice.setUIndex(uIndex);
            itDeviceService.updateITDevice(itDevice);
            return ITDeviceOperatingStatus.SUCCESS;
        } catch (Exception ex) {
            itDevice.setComputerRackId(null);
            itDevice.setUIndex(null);
            // 未知原因造成上架失败
            return ITDeviceOperatingStatus.UNKNOWN_ERROR;
        }
    }

    /**
     * IT设备下架
     *
     * @param itDeviceName
     * @return
     */
    @Override
    public ITDeviceOperatingStatus takeFromShelf(String itDeviceName) {
        ITDevice itDevice = this.itDeviceService.findITDevice(itDeviceName);
        if (itDevice == null) return ITDeviceOperatingStatus.NOT_FOUND_ITDEVICE;
        return this.takeFromShelf(itDevice);

    }

    /**
     * IT设备下架
     *
     * @param itDeviceId
     * @return
     */
    @Override
    public ITDeviceOperatingStatus takeFromShelf(Integer itDeviceId) {
        ITDevice itDevice = this.itDeviceService.findITDevice(itDeviceId);
        if (itDevice == null) return ITDeviceOperatingStatus.NOT_FOUND_ITDEVICE;
        return this.takeFromShelf(itDevice);
    }


    @Override
    public ITDeviceOperatingStatus takeFromShelf(Integer computerRackId, Integer uIndex) {
        ITDevice itDevice = this.findITDeviceFromUIndex(computerRackId, uIndex);
        if (itDevice == null) return ITDeviceOperatingStatus.U_POSITION_MUST_EMPTY;
        return this.takeFromShelf(itDevice);
    }


    public ITDevice findITDeviceFromUIndex(Integer computerRackId, Integer uIndex) {
        if (uIndex <= 0) return null;
        ComputerRackDTO computerRack = computerRackService.findComputerRackDTO(computerRackId);
        if (computerRack == null) return null;
        CapacityAttribute _space = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_SPACE.getValue());
        if (_space == null) return null;
        if (_space.getRatedCapacity() < uIndex) return null;
        List<ITDevice> group = this.itDeviceService.findByComputerRackId(computerRack.getComputerRackId());
        if (group != null) {
            for (ITDevice itdevice : group) {
                // 当前机架上的设备
                int start1 = itdevice.getUIndex();
                int end1 = itdevice.getUIndex() + itdevice.getUnitHeight() - 1;
                if (uIndex >= start1 && uIndex <= end1) {
                    return itdevice;
                }
            }
        }
        return null;
    }


    /**
     * IT设备下架
     *
     * @param itDevice
     * @return
     */
    @Override
    public ITDeviceOperatingStatus takeFromShelf(ITDevice itDevice) {
        if (itDevice.getComputerRackId() == null || itDevice.getUIndex() == null) {
            //设备没有在机架上
            return ITDeviceOperatingStatus.ITDEVICE_NOT_IN_RACK;
        }
        try {
            //增加下架记录
            RackMountRecordServiceImpl.takeFromShelfRecord(itDevice.getComputerRackId(), itDevice.getITDeviceId(), itDevice.getUIndex());
            itDevice.setComputerRackId(null);
            itDevice.setUIndex(null);
            itDeviceService.updateITDevice(itDevice);
            return ITDeviceOperatingStatus.SUCCESS;
        } catch (Exception e) {
            return ITDeviceOperatingStatus.UNKNOWN_ERROR;
        }
    }

    /**
     * IT设备批量下架
     *
     * @param itDeviceList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ITDeviceOperatingStatus takeFromShelf(List<ITDevice> itDeviceList) {
        if (itDeviceList.isEmpty()) return ITDeviceOperatingStatus.NOT_FOUND_ITDEVICE;
        if (itDeviceList.stream().anyMatch(d -> d.getComputerRackId() == null || d.getUIndex() == null)) {
            //设备没有在机架上
            return ITDeviceOperatingStatus.ITDEVICE_NOT_IN_RACK;
        }
        try {
            //增加下架记录
            for (ITDevice itDevice : itDeviceList) {
                Integer computerRackId = itDevice.getComputerRackId();
                RackMountRecordServiceImpl.takeFromShelfRecord(computerRackId, itDevice.getITDeviceId(), itDevice.getUIndex());
                itDevice.setComputerRackId(null);
                itDevice.setUIndex(null);
                itDeviceService.updateITDevice(itDevice);
                computerRackService.updateCapacityValueByComputerRackId(computerRackId);
            }
            return ITDeviceOperatingStatus.SUCCESS;
        } catch (Exception e) {
            return ITDeviceOperatingStatus.UNKNOWN_ERROR;
        }
    }


    @Override
    public boolean canPutShelf(Integer computerRackId, Integer uIndex, Integer uHeight) {
        ComputerRackDTO computerRack = computerRackService.findComputerRackDTO(computerRackId);
        if (computerRack == null) return false;
        return this.canPutShelf(computerRack.toRack(), uIndex, uHeight);
    }

    @Override
    public boolean canPutShelf(String computerRackName, Integer uIndex, Integer uHeight) {
        ComputerRackDTO computerRack = computerRackService.findDTOByComputerRackName(computerRackName);
        if (computerRack == null) return false;
        return this.canPutShelf(computerRack.toRack(), uIndex, uHeight);
    }


    @Override
    public boolean canPutShelf(ComputerRack computerRack, Integer uIndex, Integer uHeight) {
        if (computerRack == null || uIndex <= 0) {
            return false;
        }
        int targetStart = uIndex;
        int targetEnd = uIndex + uHeight - 1;
        CapacityAttribute space = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_SPACE.getValue());
        if (space == null) return false;
        if (space.getRatedCapacity() < targetEnd) return false;
        List<ITDevice> group = this.itDeviceService.findByComputerRackId(computerRack.getComputerRackId());
        if (group != null) {
            for (ITDevice itdevice : group) {
                // 当前机架上的设备
                int start1 = itdevice.getUIndex();
                int end1 = itdevice.getUIndex() + itdevice.getUnitHeight() - 1;
                if ((targetStart >= start1 && targetStart <= end1) || (targetEnd >= start1 && targetEnd <= end1)) {
                    return false;
                }
            }
        }
        return true;
    }


}