package com.siteweb.computerrack.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.computerrack.dto.ITDeviceModelmportDTO;
import com.siteweb.computerrack.entity.ITDeviceModel;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ITDeviceModelService {

    /**
     * IT设备模型名称是否已存在
     * @param iTDeviceModelId 设备模型id(主要更新的时候查看是否已经被占用)
     * @param iTDeviceModelName 设备模型名称
     * @return boolean
     */
    boolean itDeviceModelNameExist(Integer iTDeviceModelId, String iTDeviceModelName);

    /**
     * 根据名称获取IT设备模型
     */
    ITDeviceModel getDeviceModelByName(String iTDeviceModelName);

    /**
     * 查询所有IT设备模型
     * @return IT设备模型列表
     */
    List<ITDeviceModel> findITDeviceModels();

    /**
     * 分页查询IT设备模型
     * @param pageable 分页参数
     * @param keyword 关键字（可选，用于搜索模型名称或类型名称）
     * @return 分页结果
     */
    Page<ITDeviceModel> findITDeviceModelsByPage(Pageable pageable, String keyword);

    ITDeviceModel createITDeviceModel(ITDeviceModel iTDeviceModel);

    void deleteById(Integer iTDeviceModelId);

    boolean findReferences(Integer iTDeviceModelId);

    ITDeviceModel updateITDeviceModel(ITDeviceModel iTDeviceModel);

    ITDeviceModel findById(Integer iTDeviceModelId);

    ITDeviceModel findByITDeviceModelName(String itDeviceModelName);


    Integer findReferencesCount(Integer iTDeviceModelId);

    long countByCategoryId(Integer categoryId);

    /**
     * 批量导入IT设备模型
     */
    List<ImportErrorInfoDTO> importITDeviceModels(List<ITDeviceModelmportDTO> itDeviceModels);
}

