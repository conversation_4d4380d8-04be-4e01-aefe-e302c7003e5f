package com.siteweb.computerrack.service;

import com.siteweb.computerrack.vo.RackUIndexRateVo;
import com.siteweb.utility.vo.NameValueVO;

import java.util.List;

public interface BigScreenService {
    /**
     * IT设备类型分类
     * @param userId 用户id
     * @return {@link List}<{@link NameValueVO}>
     */
    List<NameValueVO> findITDeviceTypePieChart(Integer userId, String resourceStructureId);

    /**
     * IT设备业务属性分类
     * @param userId 用户id
     * @return {@link List}<{@link NameValueVO}>
     */
    List<NameValueVO> findITDeviceBusinessPieChart(Integer userId, String resourceStructureId);

    /**
     * 获取当前u位使用率 包含(已使用u位 剩余u位 标签绑定率)
     * @param userId 用户id
     * @param resourceStructureId 机房id
     * @return {@link List}<{@link NameValueVO}>
     */
    List<NameValueVO> findCurrentUIndexChange(Integer userId, String resourceStructureId);

    /**
     * 获取机房u位使用率
     * @param userId 用户id
     * @return {@link List}<{@link NameValueVO}>
     */
    List<NameValueVO> findRoomUIndexRate(Integer userId, String resourceStructureId);

    /**
     * 机架u位使用率
     * @param userId
     * @return {@link List}<{@link RackUIndexRateVo}>
     */
    List<RackUIndexRateVo> findRackUIndexRate(Integer userId, String resourceStructureId);

    /**
     *  根据系统变量找到对应topN
     *
     * @param configKey 配置key
     * @return {@link Integer}
     */
    Integer findTopN(String configKey);

    List<NameValueVO> getSumUIndexRate(Integer userId, String resourceStructureId);

    List<NameValueVO> getRackCountByCustomer(Integer userId, String filterResourceStructureIds);

    List<NameValueVO> getRackCountByBusiness(Integer userId, String filterResourceStructureIds);

}
