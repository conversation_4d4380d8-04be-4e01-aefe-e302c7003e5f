package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.UTagDto;
import com.siteweb.computerrack.entity.UTag;
import com.siteweb.computerrack.vo.UTagVo;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.vo.NameValueVO;

import java.util.List;

public interface UTagService {
    /**
     * 获取标签列表
     *
     * @return {@link List}<{@link UTagVo}>
     */
    List<UTagVo> findAll();

    /**
     * 添加标签
     * @param uTagDto 标签dto
     * @return {@link UTag}
     */
    UTag createTag(UTagDto uTagDto);

    /**
     * 查找标签根据标签值 不根据标签id
     * @param tagValue 唯一标签值
     * @return {@link Boolean}
     */
    boolean existsByTagValueNoTagId(String tagValue, Integer tagId);

    /**
     * 根据标签id删除标签
     * @param id 标签id
     * @return {@link UTag}
     */
    UTag deleteTag(Integer id);

    /**
     * 更新标签
     * @param uTagDto 标签dto
     * @return {@link UTag}
     */
    UTag updateTag(UTagDto uTagDto);

    /**
     * 查询标签根据id
     * @param id 标签id
     * @return {@link UTagDto}
     */
    UTagDto findTagById(Integer id);

    /**
     * 批量添加标签
     * @param uTagDtoList dto集合
     * @return {@link List}<{@link ImportErrorInfoDTO}>
     */
    List<ImportErrorInfoDTO> createTagList(List<UTagDto> uTagDtoList);

    NameValueVO findTagRateByResourceStructureIds(List<Integer> resourceStructureIds);

    UTag createTagBindItDevice(UTagDto uTagDto);

    void setAsserIdIsNull(Integer asserId);
}
