package com.siteweb.computerrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.dto.ItdeviceQueryDTO;
import com.siteweb.computerrack.dto.SimpleITDeviceDTO;
import com.siteweb.computerrack.dto.UTagDeviceSelectDto;
import com.siteweb.computerrack.entity.*;
import com.siteweb.computerrack.enumeration.ItDeviceControlResultType;
import com.siteweb.computerrack.enumeration.UDeviceLightStatus;
import com.siteweb.computerrack.mapper.ComputerRackMapper;
import com.siteweb.computerrack.mapper.ITDeviceMapper;
import com.siteweb.computerrack.mapper.UDeviceMapper;
import com.siteweb.computerrack.mapper.UTagMapper;
import com.siteweb.computerrack.service.ITDeviceModelService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.computerrack.vo.ITDeviceListVO;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.service.ResourceObjectService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.service.impl.ActiveControlServiceImpl;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.vo.NameValueVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ITDeviceServiceImpl implements ITDeviceService, ResourceObjectService {
    @Autowired
    private ITDeviceMapper iTDeviceRepository;
    @Autowired
    private ComputerRackMapper computerRackMapper;
    @Autowired
    @Lazy
    private ITDeviceModelService itDeviceModelService;

    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private UTagMapper uTagMapper;
    @Autowired
    private UDeviceMapper uDeviceMapper;
    @Autowired
    private ActiveControlServiceImpl activeControlService;
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    private TaskScheduler taskScheduler;
    @Autowired
    private SystemConfigService systemConfigService;


    @Override
    public List<SimpleITDeviceDTO> getOnShelfITDevices(List<Integer> computerRackIds) {
        return iTDeviceRepository.getOnShelfITDevices(computerRackIds);
    }

    @Override
    public Map<Integer, List<SimpleITDeviceDTO>> getOnShelfITDeviceMap(List<Integer> computerRackIds) {
        var result = iTDeviceRepository.getOnShelfITDevices(computerRackIds);
        return result.stream()
                .collect(Collectors.groupingBy(
                        SimpleITDeviceDTO::getComputerRackId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparingInt(SimpleITDeviceDTO::getUIndex))
                                        .collect(Collectors.toList())
                        )
                ));
    }

    @Override
    public List<ITDevice> findITDevices() {
        return iTDeviceRepository.findITDevices();
    }


    @Override
    public Page<ITDeviceListVO> findITDevicesByQuery(ItdeviceQueryDTO queryDTO) {
        UpdateWrapper<ITDevice> wrapper = Wrappers.update();
        wrapper.lambda().like(CharSequenceUtil.isNotEmpty(queryDTO.getItDeviceName()), ITDevice::getITDeviceName, queryDTO.getItDeviceName())
                .isNotNull(Objects.equals(queryDTO.getState(), RackMountRecordServiceImpl.PUTINSHELF), ITDevice::getUIndex)
                .isNotNull(Objects.equals(queryDTO.getState(), RackMountRecordServiceImpl.PUTINSHELF), ITDevice::getComputerRackId)
                .isNull(Objects.equals(queryDTO.getState(), RackMountRecordServiceImpl.TAKEFROMSHELF), ITDevice::getUIndex)
                .isNull(Objects.equals(queryDTO.getState(), RackMountRecordServiceImpl.TAKEFROMSHELF), ITDevice::getComputerRackId);
        if (ObjectUtil.isNotEmpty(queryDTO.getComputerRackId())) {
            wrapper.lambda().eq(ObjectUtil.isNotEmpty(queryDTO.getComputerRackId()), ITDevice::getComputerRackId, queryDTO.getComputerRackId());
            wrapper.lambda().orderByDesc(ITDevice::getUIndex);
            wrapper.lambda().orderByAsc(ITDevice::getITDeviceName);
        } else {
            wrapper.lambda().orderByAsc(ITDevice::getITDeviceName);
        }
        Page<ITDevice> page = new Page<>(1, -1);
        if (ObjectUtil.isAllNotEmpty(queryDTO.getPageSize(), queryDTO.getPageNumber())) {
            page = new Page<>(queryDTO.getPageNumber(), queryDTO.getPageSize());
        }
        Map<Integer, UTag> uTagMap = uTagMapper.selectList(Wrappers.lambdaUpdate(UTag.class).isNotNull(UTag::getAsserId)).stream().collect(Collectors.toMap(UTag::getAsserId, e -> e));
        Page<ITDevice> pageResult = iTDeviceRepository.findITDevicesByQuery(page, wrapper);
        List<ITDeviceListVO> result = pageResult.getRecords().stream().map(e -> {
            ITDeviceListVO itDeviceListVO = new ITDeviceListVO(e);
            itDeviceListVO.setUTag(uTagMap.get(e.getITDeviceId()));
            return itDeviceListVO;
        }).toList();
        Page<ITDeviceListVO> objectPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        objectPage.setRecords(result);
        return objectPage;
    }

    @Override
    public ITDevice findIdDeviceByuTagValue(String uTagValue) {
        return iTDeviceRepository.findIdDeviceByuTagValue(uTagValue);
    }

    @Override
    public ITDevice findITDevice(Integer iTDeviceId) {
        return iTDeviceRepository.findById(iTDeviceId);
    }

    @Override
    public List<ITDevice> findITDeviceByIds(List<Integer> iTDeviceIdList) {
        return iTDeviceRepository.findITDeviceByIds(iTDeviceIdList);
    }

    @Override
    public ITDevice findITDevice(String objectName) {
        return iTDeviceRepository.findByITDeviceName(objectName);
    }


    @Override
    public List<ITDevice> findByComputerRackId(Integer computerRackId) {
        return iTDeviceRepository.findByComputerRackId(computerRackId);
    }


    @Override
    public ITDevice findByComputerRackPos(Integer rackId, Integer itDeviceId, Integer uIndex) {
        return iTDeviceRepository.findByComputerRackPos(rackId, itDeviceId, uIndex);
    }

    @Override
    public List<ITDevice> findITDeviceByResourceStructureIds(List<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return new ArrayList<>();
        }
        return iTDeviceRepository.findITDeviceByResourceStructureId(resourceStructureIds);
    }

    @Override
    public List<NameValueVO> findITDeviceTypePieChartByResourceStructureIds(List<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return new ArrayList<>();
        }
        //需求优化使用tbl_dataitem表的类型统计it设备类型
        List<NameValueVO> result = iTDeviceRepository.findITDeviceModelCategoryPieChartByResourceStructureIds(resourceStructureIds);
        if (CollUtil.isEmpty(result)) {
            //如果使用tbl_dataitem表查不到内容则使用原来的逻辑
            result = iTDeviceRepository.findITDeviceTypePieChartByResourceStructureIds(resourceStructureIds);
        }
        return result;
    }

    @Override
    public List<ITDevice> findITDevices(Integer state) {
        LambdaQueryWrapper<ITDevice> queryWrapper = Wrappers.<ITDevice>lambdaQuery()
                .isNotNull(Objects.equals(state, RackMountRecordServiceImpl.PUTINSHELF), ITDevice::getUIndex)
                .isNotNull(Objects.equals(state, RackMountRecordServiceImpl.PUTINSHELF), ITDevice::getComputerRackId)
                .isNull(Objects.equals(state, RackMountRecordServiceImpl.TAKEFROMSHELF), ITDevice::getUIndex)
                .isNull(Objects.equals(state, RackMountRecordServiceImpl.TAKEFROMSHELF), ITDevice::getComputerRackId);
        return iTDeviceRepository.selectList(queryWrapper);
    }

    @Override
    public List<UTagDeviceSelectDto> findUTagDeviceSelect() {
        return iTDeviceRepository.findUTagDeviceSelect();
    }


    @Override
    public List<ITDevice> findITDevicesByModelId(Integer iTDeviceModelId) {
        return iTDeviceRepository.findITDevicesByModelId(iTDeviceModelId);
    }

    @Override
    public ITDevice createITDevice(ITDevice iTDevice) {
        if (iTDevice.getITDeviceId() != null) return null;
        ITDeviceModel itDeviceModel = itDeviceModelService.findById(iTDevice.getITDeviceModelId());
        if (itDeviceModel == null) {
            return null;
        }
        iTDeviceRepository.insert(iTDevice);
        iTDevice.setItDeviceModel(itDeviceModel);
        return iTDevice;
    }


    @Override
    public void deleteById(Integer iTDeviceId) {
        iTDeviceRepository.deleteById(iTDeviceId);
    }

    @Override
    public void batchDelete(List<Integer> iTDeviceIdList) {
        iTDeviceRepository.delete(Wrappers.lambdaQuery(ITDevice.class)
                .in(ITDevice::getITDeviceId, iTDeviceIdList));
    }

    @Override
    public List<ImportErrorInfoDTO> createITDeviceList(List<ITDevice> itDeviceDTOList) {
        List<ImportErrorInfoDTO> ImportErrorInfoDTOList = new ArrayList<>();
        if (Objects.isNull(itDeviceDTOList) || itDeviceDTOList.isEmpty()) {
            return ImportErrorInfoDTOList;
        }
        ITDevice itDevice = null;
        for (int i = 0; i < itDeviceDTOList.size(); i++) {
            ITDevice itDeviceDTO = itDeviceDTOList.get(i);
            ImportErrorInfoDTO ImportErrorInfoDTO = new ImportErrorInfoDTO();
            ImportErrorInfoDTO.setRowIndex(i);
            if (itDeviceDTO.getITDeviceId() != null) {
                itDevice = this.findITDevice(itDeviceDTO.getITDeviceId());
            }
            if (itDevice == null) {
                itDevice = itDeviceDTO;
            }
            this.createITDevice(itDeviceDTO);
        }
        return ImportErrorInfoDTOList;
    }


    @Override
    public ITDevice updateITDevice(ITDevice iTDevice) {
        iTDeviceRepository.updateById(iTDevice);
        return iTDevice;
    }


    @Override
    public SourceType[] getSourceTypes() {
        return new SourceType[]{SourceType.ITDEVICE};
    }

    @Override
    public ResourceObjectEntity findResourceObjectEntity(Integer objectId) {
        return iTDeviceRepository.findResourceObjectByITDeviceId(objectId);
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObject() {
        List<Integer> deviceIds = this.findITDevices()
                .stream()
                .map(ITDevice::getITDeviceId)
                .toList();
        if (CollUtil.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        return iTDeviceRepository.findResourceObjectByITDeviceIds(deviceIds);
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObjectByUserId(Integer userId) {
        List<ResourceStructure> resourceStructureList = resourceStructureService.findResourceStructureByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureList)) {
            return new ArrayList<>();
        }
        List<Integer> resourceStructureIds = resourceStructureList.stream()
                .map(ResourceStructure::getResourceStructureId)
                .toList();
        return iTDeviceRepository.findResourceObjectByResourceStructureIds(resourceStructureIds);
    }

    @Override
    public boolean existsByName(Integer itDeviceId,String name) {
        if (Objects.isNull(itDeviceId)) {
            return iTDeviceRepository.exists(Wrappers.lambdaQuery(ITDevice.class).eq(ITDevice::getITDeviceName, name));
        }
        return iTDeviceRepository.exists(Wrappers.lambdaQuery(ITDevice.class).ne(ITDevice::getITDeviceId,itDeviceId).eq(ITDevice::getITDeviceName, name));
    }

    @Override
    public Map<Integer,ItDeviceControlResultType> findItDevicesByCommand(Integer userId, List<Integer> itDeviceIdList) {
        Integer restoreDelaySec = systemConfigService.findIntegerValue(SystemConfigEnum.U_DEVICE_RESTORE_DELAY_SECOND);
        return itDeviceIdList.parallelStream()
                             .collect(Collectors.toMap(
                                     Function.identity(),
                                     itDeviceId -> findItDeviceByCommand(userId, itDeviceId, restoreDelaySec),
                                     (existing, replacement) -> replacement,
                                     ConcurrentHashMap::new
                             ));
    }

    public ItDeviceControlResultType findItDeviceByCommand(Integer userId,Integer itDeviceId,Integer restoreDelaySec) {
        ITDevice itDevice = iTDeviceRepository.findById(itDeviceId);
        if (Objects.isNull(itDevice)) {
            log.error("查找IT设备异常,ITDevice在数据库中不存在,Id:{}", itDeviceId);
            return ItDeviceControlResultType.IT_DEVICE_NOT_FOUND;
        }
        ComputerRack computerRack = computerRackMapper.findByComputerRackId(itDevice.getComputerRackId());
        if (Objects.isNull(computerRack)) {
            log.error("查找IT设备异常,机架信息在数据库中不存在,ItDeviceId:{},computerRackId:{}", itDeviceId, itDevice.getComputerRackId());
            return ItDeviceControlResultType.RACK_INFO_NOT_FOUND;
        }
        UDevice uDevice = uDeviceMapper.selectOne(Wrappers.lambdaQuery(UDevice.class).eq(UDevice::getRackId, computerRack.getComputerRackId()));
        if (Objects.isNull(uDevice)) {
            log.error("查找IT设备异常,U位设备不存在,ItDeviceId:{},computerRackId:{}", itDeviceId, itDevice.getComputerRackId());
            return ItDeviceControlResultType.U_POSITION_DEVICE_NOT_FOUND;
        }
        ControlResultType controlResultType = null;
        //根据it设备的U高来发送多个控制命令
        for (int i = 0; i < itDevice.getUnitHeight(); i++) {
            //cmdToken = 当前的上架位置 + 10
            String cmdToken = String.valueOf((itDevice.getUIndex() + i + 10));
            //发送控制命令
            controlResultType = activeControlService.sendControlCommandByEquipmentIdAndCmdToken(userId, uDevice.getSwEquipmentId(), cmdToken, UDeviceLightStatus.ALARM.getCode(), localeMessageSourceUtil.getMessage("search.it.device.title"));
            // 延迟 指定时间（秒）后在发送控制命令将其还原 因为不还原灯的话，一直会变成红色，变不回来了
            taskScheduler.schedule(
                    () -> {
                        try {
                            ControlResultType retryResult = activeControlService.sendControlCommandByEquipmentIdAndCmdToken(userId, uDevice.getSwEquipmentId(), cmdToken, UDeviceLightStatus.DETECT_TAG.getCode(), localeMessageSourceUtil.getMessage("search.it.device.title"));
                            log.info("查找it设备延迟重置({}s后):itDeviceId={}, cmdToken={}, retryResult={}", restoreDelaySec, itDeviceId, cmdToken, retryResult);
                        } catch (Exception ex) {
                            log.error("延迟重试异常,itDeviceId={}, cmdToken={}", itDeviceId, cmdToken, ex);
                        }
                    },
                    Instant.now().plusSeconds(restoreDelaySec)
            );
        }

        return ItDeviceControlResultType.valueOf(controlResultType.value());
    }


    @Override
    public Page<ITDevice> findITDevicesByPage(Pageable pageable, String keyword) {
        // 获取所有IT设备
        List<ITDevice> allDevices = iTDeviceRepository.findITDevices();

        // 根据关键字过滤数据
        List<ITDevice> filteredDevices = allDevices.stream()
                .filter(device -> {
                    if (CharSequenceUtil.isBlank(keyword)) {
                        return true;
                    }

                    boolean nameMatch = CharSequenceUtil.containsIgnoreCase(device.getITDeviceName(), keyword);

                    boolean modelNameMatch = device.getItDeviceModel() != null &&
                            CharSequenceUtil.isNotBlank(device.getItDeviceModel().getITDeviceModelName()) &&
                            CharSequenceUtil.containsIgnoreCase(device.getItDeviceModel().getITDeviceModelName(), keyword);

                    boolean customerMatch = CharSequenceUtil.isNotBlank(device.getCustomer()) &&
                            CharSequenceUtil.containsIgnoreCase(device.getCustomer(), keyword);
                    boolean ipAddressMatch = CharSequenceUtil.containsIgnoreCase(device.getIpaddr(), keyword);

                    boolean businessMatch = CharSequenceUtil.isNotBlank(device.getBusiness()) &&
                            CharSequenceUtil.containsIgnoreCase(device.getBusiness(), keyword);

                    boolean serialNumberMatch = CharSequenceUtil.isNotBlank(device.getSerialNumber()) &&
                            CharSequenceUtil.containsIgnoreCase(device.getSerialNumber(), keyword);

                    boolean computerRackNameMatch = CharSequenceUtil.isNotBlank(device.getComputerRackName()) &&
                            CharSequenceUtil.containsIgnoreCase(device.getComputerRackName(), keyword);

                    return nameMatch || modelNameMatch || customerMatch || ipAddressMatch || businessMatch || serialNumberMatch || computerRackNameMatch;
                })
                .toList();

        List<ITDevice> sortedDevices = applySorting(filteredDevices, pageable.getSort());
        Page<ITDevice> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize(), sortedDevices.size(), false);

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), sortedDevices.size());

        List<ITDevice> pageContent = start < end ?
                sortedDevices.subList(start, end) :
                Collections.emptyList();
        page.setRecords(pageContent);
        page.setCurrent(page.getCurrent() - 1L);
        return page;
    }

    /**
     * 应用排序到IT设备列表
     *
     * @param devices 需要排序的IT设备列表
     * @param sort 排序参数
     * @return 排序后的列表
     */
    private List<ITDevice> applySorting(List<ITDevice> devices, org.springframework.data.domain.Sort sort) {
        if (sort == null || !sort.iterator().hasNext()) {
            // 默认不排序
            return devices;
        }

        List<ITDevice> result = new ArrayList<>(devices);

        // 处理排序参数
        sort.forEach(order -> {
            String property = order.getProperty();
            boolean isAscending = order.getDirection().isAscending();

            Comparator<ITDevice> comparator = null;

            // 根据属性名称选择比较器
            switch (property.toLowerCase()) {
                case "itdevicename":
                    comparator = Comparator.comparing(ITDevice::getITDeviceName,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
                case "itdevicemodelid":
                    comparator = Comparator.comparing(ITDevice::getITDeviceModelId,
                            Comparator.nullsLast(Integer::compareTo));
                    break;
                case "modelname":
                    comparator = Comparator.comparing(
                            device -> device.getItDeviceModel() != null ? device.getItDeviceModel().getITDeviceModelName() : "",
                            String::compareToIgnoreCase);
                    break;
                case "customer":
                    comparator = Comparator.comparing(
                            device -> device.getCustomer() != null ? device.getCustomer() : "",
                            String::compareToIgnoreCase);
                    break;
                case "business":
                    comparator = Comparator.comparing(
                            device -> device.getBusiness() != null ? device.getBusiness() : "",
                            String::compareToIgnoreCase);
                    break;
                case "serialnumber":
                    comparator = Comparator.comparing(
                            device -> device.getSerialNumber() != null ? device.getSerialNumber() : "",
                            String::compareToIgnoreCase);
                    break;
                case "purchasedate":
                    comparator = Comparator.comparing(ITDevice::getPurchaseDate,
                            Comparator.nullsLast(Date::compareTo));
                    break;
                case "launchdate":
                    comparator = Comparator.comparing(ITDevice::getLaunchDate,
                            Comparator.nullsLast(Date::compareTo));
                    break;
                default:
                    // 如果属性不支持，则使用默认的设备名称排序
                    comparator = Comparator.comparing(ITDevice::getITDeviceName,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
            }

            // 如果是降序，则反转比较器
            if (!isAscending) {
                comparator = comparator.reversed();
            }

            // 应用排序
            result.sort(comparator);
        });

        return result;
    }
}
