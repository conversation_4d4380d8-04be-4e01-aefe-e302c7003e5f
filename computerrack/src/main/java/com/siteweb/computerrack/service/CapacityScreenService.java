package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.CapacityScreenParam;
import com.siteweb.utility.vo.CapacityRateVO;
import com.siteweb.utility.vo.ComputerRackOpenRateVO;
import com.siteweb.utility.vo.NameValueVO;

import java.util.List;

public interface CapacityScreenService {

    CapacityRateVO getCapacityTotalPowerRate(Integer resourceStructureId);

    CapacityRateVO getCapacityItPowerRate(Integer resourceStructureId);

    CapacityRateVO getCapacityRefrigerationPowerRate(Integer resourceStructureId);

    ComputerRackOpenRateVO getComputerRackOpenRate(Integer resourceStructureId);

    List<NameValueVO> getMainsPowerInletLoad(Integer resourceStructureId);

    List<NameValueVO> getRoomItPowerRank(CapacityScreenParam param);

    List<NameValueVO> getRoomRefrigerationRank(CapacityScreenParam param);

    List<NameValueVO>  getRoomRackOpenRank(CapacityScreenParam param);
}
