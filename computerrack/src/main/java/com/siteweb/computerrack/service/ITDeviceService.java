package com.siteweb.computerrack.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.computerrack.dto.ItdeviceQueryDTO;
import com.siteweb.computerrack.dto.SimpleITDeviceDTO;
import com.siteweb.computerrack.dto.UTagDeviceSelectDto;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.enumeration.ItDeviceControlResultType;
import com.siteweb.computerrack.vo.ITDeviceListVO;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.vo.NameValueVO;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface ITDeviceService {


    List<SimpleITDeviceDTO> getOnShelfITDevices(List<Integer> computerRackIds);
    Map<Integer,List<SimpleITDeviceDTO>> getOnShelfITDeviceMap(List<Integer> computerRackIds);


    List<ITDevice> findITDevices();

    /**
     * 创建ITdevice
     * 此时 ITDevice已入库 但 globalresource尚未更新至ITDEVICE表，需要调用submit提交更新
     *
     * @param iTDevice
     * @return
     */
    ITDevice createITDevice(ITDevice iTDevice);

    void deleteById(Integer iTDeviceId);
    void batchDelete(List<Integer> iTDeviceIdList);

    /**
     * 修改ITdevice至缓存
     * 此时ITDevice 尚未入库，需要调用submit提交更新
     *
     * @param iTDevice
     * @return
     */
    ITDevice updateITDevice(ITDevice iTDevice);

    ITDevice findITDevice(Integer iTDeviceId);
    List<ITDevice> findITDeviceByIds(List<Integer> iTDeviceIdList);

    List<ITDevice> findITDevicesByModelId(Integer iTDeviceModelId);


    /**
     * 批量创建ITDevice列表
     *
     * @param itDeviceDTOList
     * @return
     */
    List<ImportErrorInfoDTO> createITDeviceList(List<ITDevice> itDeviceDTOList);

    ITDevice findITDevice(String objectName);

    List<ITDevice> findByComputerRackId(Integer computerRackId);

    ITDevice findByComputerRackPos(Integer rackId, Integer itDeviceId, Integer uIndex);

    /**
     * 获取it设备，根据层级id
     * @param resourceStructureId
     * @return {@link List}<{@link ITDevice}>
     */
    List<ITDevice> findITDeviceByResourceStructureIds(List<Integer> resourceStructureIds);

    /**
     * 查找it设备饼图根据层级ids
     *
     * @param resourceStructureIds 层级ids
     * @return {@link List}<{@link ITDevice}>
     */
    List<NameValueVO> findITDeviceTypePieChartByResourceStructureIds(List<Integer> resourceStructureIds);

    /**
     * 通过it设备状态查询it设备
     * @param state 上下架状态 1上架 2下架
     * @return {@link List}<{@link ITDevice}>
     */
    List<ITDevice> findITDevices(Integer state);

    /**
     * 获取标签与it设备的绑定状态
     * @return {@link List}<{@link UTagDeviceSelectDto}>
     */
    List<UTagDeviceSelectDto> findUTagDeviceSelect();

    Page<ITDeviceListVO> findITDevicesByQuery(ItdeviceQueryDTO queryDTO);

    ITDevice findIdDeviceByuTagValue(String uTagValue);
    boolean existsByName(Integer itDeviceId,String name);

    Map<Integer, ItDeviceControlResultType> findItDevicesByCommand(Integer userId, List<Integer> itDeviceIdList);

    /**
     * 分页查询IT设备列表
     * @param pageable 分页参数
     * @param keyword 关键字（可选，用于搜索设备名称、模型名称、客户名或业务）
     * @return 分页结果
     */
    Page<ITDevice> findITDevicesByPage(Pageable pageable, String keyword);
}

