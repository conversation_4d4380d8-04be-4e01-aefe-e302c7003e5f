package com.siteweb.computerrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.dto.ComplexIndexValueDTO;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.enumeration.CIDefinitionEnum;
import com.siteweb.complexindex.manager.HistoryComplexIndexManager;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.computerrack.dto.CapacityScreenParam;
import com.siteweb.computerrack.dto.ComputerRackOpenCountDTO;
import com.siteweb.computerrack.dto.RoomComplexIndexDTO;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ComputerRackSignalMap;
import com.siteweb.computerrack.manager.ComputerRackSignalRecordManage;
import com.siteweb.computerrack.mapper.ComputerRackMapper;
import com.siteweb.computerrack.service.CapacityScreenService;
import com.siteweb.computerrack.service.ComputerRackSignalService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.CapacityTypeEnum;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.vo.CapacityRateVO;
import com.siteweb.utility.vo.ComputerRackOpenRateVO;
import com.siteweb.utility.vo.NameValueVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CapacityScreenServiceImpl implements CapacityScreenService {
    private final ComputerRackSignalService computerRackSignalService;
    private final ResourceStructureService resourceStructureService;
    private final LiveComplexIndexManager liveComplexIndexManager;
    private final ResourceStructureManager resourceStructureManager;
    private final ComputerRackMapper computerRackRepository;
    private final ComplexIndexService complexIndexService;
    private final HistoryComplexIndexManager historyComplexIndexManager;
    private final SystemConfigService systemConfigService;
    private final ComputerRackSignalRecordManage computerRackSignalRecordManage;
    /**
     * 默认topN
     */
    private static final int DEFAULT_TOP_N = 5;

    @Override
    public CapacityRateVO getCapacityTotalPowerRate(Integer resourceStructureId) {
        CapacityRateVO capacityRateVO = new CapacityRateVO();
        // 额定容量
        double ratedCapacity = resourceStructureService.getResourceStructureCapacity(resourceStructureId, CapacityTypeEnum.TOTAL_POWER_RATED_CAPACITY);
        // 当前容量 通过指标从redis获取实时值
        capacityRateVO.setRatedCapacity(ratedCapacity);
        LiveComplexIndex liveComplexIndex = liveComplexIndexManager.findByObjectIdAndDefinitionId(resourceStructureId, CIDefinitionEnum.TOTAL_POWER.getValue());
        String currentCapacity = Optional.ofNullable(liveComplexIndex).map(LiveComplexIndex::getCurrentValue).orElse(null);
        capacityRateVO.setCurrentCapacity(Convert.toDouble(currentCapacity, 0.0));
        capacityRateVO.setFreeCapacity(capacityRateVO.getRatedCapacity() - capacityRateVO.getCurrentCapacity());
        capacityRateVO.setPercentage(NumberUtil.calculatePercentage(capacityRateVO.getCurrentCapacity(), capacityRateVO.getRatedCapacity()));
        return capacityRateVO;
    }

    @Override
    public CapacityRateVO getCapacityItPowerRate(Integer resourceStructureId) {
        CapacityRateVO capacityRateVO = new CapacityRateVO();
        // 额定容量
        double ratedCapacity = resourceStructureService.getResourceStructureCapacity(resourceStructureId, CapacityTypeEnum.IT_POWER_RATED_CAPACITY);
        // 当前容量 通过指标从redis获取实时值
        capacityRateVO.setRatedCapacity(ratedCapacity);
        LiveComplexIndex liveComplexIndex = liveComplexIndexManager.findByObjectIdAndDefinitionId(resourceStructureId, CIDefinitionEnum.IT_POWER.getValue());
        String currentCapacity = Optional.ofNullable(liveComplexIndex).map(LiveComplexIndex::getCurrentValue).orElse(null);
        capacityRateVO.setCurrentCapacity(Convert.toDouble(currentCapacity, 0.0));
        capacityRateVO.setFreeCapacity(capacityRateVO.getRatedCapacity() - capacityRateVO.getCurrentCapacity());
        capacityRateVO.setPercentage(NumberUtil.calculatePercentage(capacityRateVO.getCurrentCapacity(), capacityRateVO.getRatedCapacity()));
        return capacityRateVO;
    }

    @Override
    public CapacityRateVO getCapacityRefrigerationPowerRate(Integer resourceStructureId) {
        CapacityRateVO capacityRateVO = new CapacityRateVO();
        // 额定容量
        double ratedCapacity = resourceStructureService.getResourceStructureCapacity(resourceStructureId, CapacityTypeEnum.REFRIGERATION_RATED_CAPACITY);
        // 当前容量 通过指标从redis获取实时值
        capacityRateVO.setRatedCapacity(ratedCapacity);
        LiveComplexIndex liveComplexIndex = liveComplexIndexManager.findByObjectIdAndDefinitionId(resourceStructureId, CIDefinitionEnum.REFRIGERATION.getValue());
        String currentCapacity = Optional.ofNullable(liveComplexIndex).map(LiveComplexIndex::getCurrentValue).orElse(null);
        capacityRateVO.setCurrentCapacity(Convert.toDouble(currentCapacity, 0.0));
        capacityRateVO.setFreeCapacity(capacityRateVO.getRatedCapacity() - capacityRateVO.getCurrentCapacity());
        capacityRateVO.setPercentage(NumberUtil.calculatePercentage(capacityRateVO.getCurrentCapacity(), capacityRateVO.getRatedCapacity()));
        return capacityRateVO;
    }

    @Override
    public ComputerRackOpenRateVO getComputerRackOpenRate(Integer resourceStructureId) {
        // 根据层级id拿到下面所有层级id
        Set<Integer> resourceStructureIds = resourceStructureManager.getAllChildrenId(List.of(resourceStructureId));
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return null;
        }
        ComputerRackOpenRateVO computerRackOpenRateVO = new ComputerRackOpenRateVO();
        // 获取相关机架
        List<ComputerRack> computerRackList = computerRackRepository.findByResourceStructureIdIn(List.copyOf(resourceStructureIds));
        if (CollUtil.isEmpty(computerRackList)) {
            return computerRackOpenRateVO;
        }
        int rackSumCount = computerRackList.size();
        computerRackOpenRateVO.setRackSumCount(rackSumCount);
        Set<Integer> computerRackIdList = computerRackList.stream().map(ComputerRack::getComputerRackId).collect(Collectors.toSet());
        // 计算开通率
        List<ComputerRackSignalMap> computerRackSignalMaps = computerRackSignalService.findByComputerRackIds(computerRackIdList);
        long openCount = computerRackSignalMaps.stream().filter(f -> computerRackSignalService.calculateOpenExpression(f.getOpenExpression())).count();
        computerRackOpenRateVO.setOpenCount(openCount);
        computerRackOpenRateVO.setFreeCount(computerRackOpenRateVO.getRackSumCount() - computerRackOpenRateVO.getOpenCount());
        computerRackOpenRateVO.setPercentage(NumberUtil.calculatePercentage(computerRackOpenRateVO.getOpenCount(), computerRackOpenRateVO.getRackSumCount()));
        return computerRackOpenRateVO;
    }

    @Override
    public List<NameValueVO> getMainsPowerInletLoad(Integer resourceStructureId) {
        List<ComplexIndexValueDTO> complexIndexValueDTOS = liveComplexIndexManager.listComplexIndexValue(resourceStructureId, CIDefinitionEnum.MAINS_POWER_INLET_LOAD.getValue());
        return complexIndexValueDTOS.stream().map(c -> NameValueVO.builder().name(c.getComplexIndexName()).value(c.getCurrentValue()).build()).toList();
    }

    @Override
    public List<NameValueVO> getRoomItPowerRank(CapacityScreenParam param) {
        // 根据层级id拿到下面所有层级id
        Set<Integer> resourceStructureIds = resourceStructureManager.getAllChildrenId(List.of(param.getResourceStructureId()));
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return List.of();
        }
        // 获取机房信息
        List<ResourceStructure> roomStructures = resourceStructureManager.getResourceStructureByIds(resourceStructureIds)
                .stream().filter(f -> Objects.equals(SourceType.ROOM.value(), f.getStructureTypeId())).toList();
        if (CollUtil.isEmpty(roomStructures)) {
            return List.of();
        }
        // 获取指标map key是层级id value是指标id
        Map<Integer, Integer> complexIndexIdMap = complexIndexService.listComplexIndexIdMap(roomStructures.stream()
                .map(ResourceStructure::getResourceStructureId).toList(), CIDefinitionEnum.IT_POWER.getValue());
        Collection<Integer> complexIndexIds = complexIndexIdMap.values();
        if (CollUtil.isEmpty(complexIndexIds)) {
            return List.of();
        }
        Map<Integer, List<HistoryComplexIndex>> lastHistoryComplexIndexMap = historyComplexIndexManager.findLastHistoryComplexIndexMap(param.getStartTime(), param.getEndTime(), complexIndexIds.stream().toList());
        List<RoomComplexIndexDTO> roomComplexIndexList = new ArrayList<>();
        for (ResourceStructure roomStructure : roomStructures) {
            Integer complexIndexId = complexIndexIdMap.get(roomStructure.getResourceStructureId());
            if (Objects.nonNull(complexIndexId)) {
                RoomComplexIndexDTO roomComplexIndexDTO = new RoomComplexIndexDTO();
                roomComplexIndexDTO.setResourceStructureId(roomStructure.getResourceStructureId());
                roomComplexIndexDTO.setResourceStructureName(roomStructure.getResourceStructureName());
                roomComplexIndexDTO.setComplexIndexId(complexIndexId);
                // 获取房间的额定功率
                roomComplexIndexDTO.setRatedCapacity(getResourceCapacity(roomStructure.getExtendedField(), CapacityTypeEnum.IT_POWER_RATED_CAPACITY));
                // 获取指标值
                List<HistoryComplexIndex> historyComplexIndexList = lastHistoryComplexIndexMap.get(complexIndexId);
                if (CollUtil.isNotEmpty(historyComplexIndexList)) {
                    roomComplexIndexDTO.setComplexIndexValue(Convert.toDouble(historyComplexIndexList.get(0).getIndexValue(), 0.0));
                }
                // 计算利用率
                roomComplexIndexDTO.setPercentage(NumberUtil.calculatePercentage(roomComplexIndexDTO.getComplexIndexValue(), roomComplexIndexDTO.getRatedCapacity()));
                roomComplexIndexList.add(roomComplexIndexDTO);
            }
        }
        return roomComplexIndexList.stream()
                .sorted(Comparator.comparingDouble(RoomComplexIndexDTO::getPercentage).reversed())
                .limit(findTopN())
                .map(m -> NameValueVO.builder().name(m.getResourceStructureName()).value(m.getPercentage()).build()).toList();
    }

    @Override
    public List<NameValueVO> getRoomRefrigerationRank(CapacityScreenParam param) {
        // 根据层级id拿到下面所有层级id
        Set<Integer> resourceStructureIds = resourceStructureManager.getAllChildrenId(List.of(param.getResourceStructureId()));
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return List.of();
        }
        // 获取机房信息
        List<ResourceStructure> roomStructures = resourceStructureManager.getResourceStructureByIds(resourceStructureIds)
                .stream().filter(f -> Objects.equals(SourceType.ROOM.value(), f.getStructureTypeId())).toList();
        if (CollUtil.isEmpty(roomStructures)) {
            return List.of();
        }
        // 获取指标map key是层级id value是指标id
        Map<Integer, Integer> complexIndexIdMap = complexIndexService.listComplexIndexIdMap(roomStructures.stream()
                .map(ResourceStructure::getResourceStructureId).toList(), CIDefinitionEnum.REFRIGERATION.getValue());
        Collection<Integer> complexIndexIds = complexIndexIdMap.values();
        if (CollUtil.isEmpty(complexIndexIds)) {
            return List.of();
        }
        Map<Integer, List<HistoryComplexIndex>> lastHistoryComplexIndexMap = historyComplexIndexManager.findLastHistoryComplexIndexMap(param.getStartTime(), param.getEndTime(), complexIndexIds.stream().toList());
        List<RoomComplexIndexDTO> roomComplexIndexList = new ArrayList<>();
        for (ResourceStructure roomStructure : roomStructures) {
            Integer complexIndexId = complexIndexIdMap.get(roomStructure.getResourceStructureId());
            if (Objects.nonNull(complexIndexId)) {
                RoomComplexIndexDTO roomComplexIndexDTO = new RoomComplexIndexDTO();
                roomComplexIndexDTO.setResourceStructureId(roomStructure.getResourceStructureId());
                roomComplexIndexDTO.setResourceStructureName(roomStructure.getResourceStructureName());
                roomComplexIndexDTO.setComplexIndexId(complexIndexId);
                // 获取房间的额定功率
                roomComplexIndexDTO.setRatedCapacity(getResourceCapacity(roomStructure.getExtendedField(), CapacityTypeEnum.REFRIGERATION_RATED_CAPACITY));
                // 获取指标值
                List<HistoryComplexIndex> historyComplexIndexList = lastHistoryComplexIndexMap.get(complexIndexId);
                if (CollUtil.isNotEmpty(historyComplexIndexList)) {
                    roomComplexIndexDTO.setComplexIndexValue(Convert.toDouble(historyComplexIndexList.get(0).getIndexValue(), 0.0));
                }
                // 计算利用率
                roomComplexIndexDTO.setPercentage(NumberUtil.calculatePercentage(roomComplexIndexDTO.getComplexIndexValue(), roomComplexIndexDTO.getRatedCapacity()));
                roomComplexIndexList.add(roomComplexIndexDTO);
            }
        }
        return roomComplexIndexList.stream()
                .sorted(Comparator.comparingDouble(RoomComplexIndexDTO::getPercentage).reversed())
                .limit(findTopN())
                .map(m -> NameValueVO.builder().name(m.getResourceStructureName()).value(m.getPercentage()).build()).toList();
    }

    @Override
    public List<NameValueVO> getRoomRackOpenRank(CapacityScreenParam param) {
        // 根据层级id拿到下面所有层级id
        Set<Integer> resourceStructureIds = resourceStructureManager.getAllChildrenId(List.of(param.getResourceStructureId()));
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return List.of();
        }
        // 获取机房信息
        List<ResourceStructure> roomStructures = resourceStructureManager.getResourceStructureByIds(resourceStructureIds)
                .stream().filter(f -> Objects.equals(SourceType.ROOM.value(), f.getStructureTypeId())).toList();
        if (CollUtil.isEmpty(roomStructures)) {
            return List.of();
        }
        // 拿机架信号记录表每个机架id时间范围最后一条数据出来
        // 使用InfluxDB查询机架开通数量
        List<ComputerRackOpenCountDTO> rackOpenCountList = computerRackSignalRecordManage.getRackOpenCount(
                roomStructures.stream().map(ResourceStructure::getResourceStructureId).toList(),
                param.getStartTime(),
                param.getEndTime()
        );
        Map<Integer, Double> rackOpenCountMap = rackOpenCountList.stream().collect(Collectors.toMap(ComputerRackOpenCountDTO::getResourceStructureId,
                t -> NumberUtil.calculatePercentage(Double.valueOf(t.getOpenCount()), t.getRackCount())));
        Set<Integer> topStructureIds = rackOpenCountMap.keySet();
        // 按开通率降序排序并限制条数
        return topStructureIds.stream()
                .map(id -> {
                    ResourceStructure rs = roomStructures.stream()
                            .filter(r -> r.getResourceStructureId().equals(id))
                            .findFirst()
                            .orElse(null);
                    return rs == null ? null : NameValueVO.builder()
                            .name(rs.getResourceStructureName())
                            .value(rackOpenCountMap.getOrDefault(id, 0.0))
                            .build();
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparingDouble(NameValueVO::getValue).reversed())
                .limit(findTopN())
                .toList();
    }

    private double getResourceCapacity(JsonNode extendedField, CapacityTypeEnum capacityTypeEnum) {
        if (Objects.isNull(extendedField)) {
            return 0.0;
        }
        for (JsonNode node : extendedField) {
            if (node.has("id") && capacityTypeEnum.getValue().equals(node.get("id").asText())) {
                return node.has("value") && !node.get("value").isNull() ? node.get("value").asDouble() : 0.0;
            }
        }
        return 0.0;
    }

    private int findTopN() {
        String configKey = "bytedance.capacityscreen.top";
        if (CharSequenceUtil.isBlank(configKey)) {
            return DEFAULT_TOP_N;
        }
        SystemConfig bySystemConfigKey = systemConfigService.findBySystemConfigKey(configKey);
        if (ObjectUtil.isNull(bySystemConfigKey)) {
            return DEFAULT_TOP_N;
        }
        try {
            int value = Integer.parseInt(bySystemConfigKey.getSystemConfigValue());
            // 限制范围在 1 到 10 之间
            return Math.min(Math.max(value, 1), 10);
        } catch (NumberFormatException e) {
            // 配置值无法解析为整数时，使用默认值
            return DEFAULT_TOP_N;
        }
    }
}
