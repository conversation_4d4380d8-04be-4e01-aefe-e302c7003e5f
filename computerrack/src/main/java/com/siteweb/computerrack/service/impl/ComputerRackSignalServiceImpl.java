package com.siteweb.computerrack.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.dto.BoolStringResponse;
import com.siteweb.computerrack.dto.ComputerRackSignaImportDTO;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ComputerRackSignalMap;
import com.siteweb.computerrack.mapper.ComputerRackMapper;
import com.siteweb.computerrack.mapper.ComputerRackSignalMapper;
import com.siteweb.computerrack.service.ComputerRackSignalService;
import com.siteweb.computerrack.vo.ComputerRackSignalVO;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Pageable;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ComputerRackSignalServiceImpl implements ComputerRackSignalService {
    private static final String COMMON_FIELD_CANNOT_EMPTY = "common.field.cannotEmpty";
    private final ComputerRackSignalMapper computerRackSignalMapper;
    private final ResourceStructureService resourceStructureService;
    private final EquipmentManager equipmentManager;
    private final ConfigSignalManager configSignalManager;
    private final ComputerRackMapper computerRackRepository;
    private final LocaleMessageSourceUtil messageSourceUtil;
    private final TransactionTemplate transactionTemplate;
    private final EquipmentStateManager equipmentStateManager;
    private final ActiveSignalManager activeSignalManager;

    @Override
    public List<ComputerRackSignalVO> findComputerRackSignalByUserId(Integer userId) {
        // 获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findThisHierarchyResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }
        List<ComputerRackSignalVO> computerRackSignalList = computerRackSignalMapper.findComputerRackSignalList(resourceStructureIds);
        for (ComputerRackSignalVO computerRackSignalVO : computerRackSignalList) {
            String openExpression = computerRackSignalVO.getOpenExpression();
            if (Objects.nonNull(openExpression)) {
                String result = replaceEquipmentAndSignalName(openExpression);
                computerRackSignalVO.setOpenExpressionStr(result);
            }
            String powerExpression = computerRackSignalVO.getPowerExpression();
            if (Objects.nonNull(powerExpression)) {
                String result = replaceEquipmentAndSignalName(powerExpression);
                computerRackSignalVO.setPowerExpressionStr(result);
            }
        }
        return computerRackSignalList;
    }

    @Override
    public Page<ComputerRackSignalVO> findComputerRackSignalByPage(Pageable pageable, String keyword, Integer userId) {
        // 获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findThisHierarchyResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIds)) {
            // 返回空的分页结果
            return new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize(), 0, false);
        }

        // 获取机架信号映射列表
        List<ComputerRackSignalVO> computerRackSignalList = computerRackSignalMapper.findComputerRackSignalList(resourceStructureIds);

        // 处理表达式中的设备和信号名称
        for (ComputerRackSignalVO computerRackSignalVO : computerRackSignalList) {
            String openExpression = computerRackSignalVO.getOpenExpression();
            if (Objects.nonNull(openExpression)) {
                String result = replaceEquipmentAndSignalName(openExpression);
                computerRackSignalVO.setOpenExpressionStr(result);
            }
            String powerExpression = computerRackSignalVO.getPowerExpression();
            if (Objects.nonNull(powerExpression)) {
                String result = replaceEquipmentAndSignalName(powerExpression);
                computerRackSignalVO.setPowerExpressionStr(result);
            }
        }

        // 根据关键字过滤数据
        List<ComputerRackSignalVO> filteredList = computerRackSignalList.stream()
                .filter(signal -> {
                    if (CharSequenceUtil.isBlank(keyword)) {
                        return true; // 如果关键字为空，返回所有数据
                    }

                    // 检查机架名称是否包含关键字
                    boolean nameMatch = CharSequenceUtil.containsIgnoreCase(signal.getComputerRackName(), keyword);
                    // 检查表达式是否包含关键字
                    boolean openExpressionStrMatch = CharSequenceUtil.containsIgnoreCase(signal.getOpenExpressionStr(), keyword);
                    boolean powerExpressionStrMatch = CharSequenceUtil.containsIgnoreCase(signal.getPowerExpressionStr(), keyword);
                    // 只要满足任一条件即可
                    return nameMatch || openExpressionStrMatch || powerExpressionStrMatch ;
                })
                .toList();

        List<ComputerRackSignalVO> sortedList = applySorting(filteredList, pageable.getSort());

        Page<ComputerRackSignalVO> page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize(), sortedList.size(), false);

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), sortedList.size());

        List<ComputerRackSignalVO> pageContent = start < end ?
                sortedList.subList(start, end) :
                Collections.emptyList();
        page.setRecords(pageContent);
        page.setCurrent(page.getCurrent() - 1L);

        return page;
    }

    /**
     * 获取表达式中的id，替换名称进去
     *
     * @param expression 表达式 [设备id\信号id] > 10 && [设备Bid\信号Yid] < 20
     */
    @NotNull
    private String replaceEquipmentAndSignalName(String expression) {
        // 正则匹配 [设备id\信号id] 形式的表达式
        Pattern pattern = Pattern.compile("\\[(.+?)]");
        Matcher matcher = pattern.matcher(expression);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String original = matcher.group(1); // 设备id\信号id
            String[] parts = original.split("\\\\"); // 按照 "\" 分割
            // 依次替换每个部分
            StringBuilder replaced = new StringBuilder();
            Integer equipmentId = Integer.valueOf(parts[0]);
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            replaced.append(Optional.ofNullable(equipment).map(Equipment::getEquipmentName).orElse("null"));
            replaced.append("\\"); // 保持原本的 `\` 结构
            Integer signalId = Integer.valueOf(parts[1]);
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalId);
            replaced.append(Optional.ofNullable(configSignalItem).map(ConfigSignalItem::getSignalName).orElse("null"));
            // 替换匹配项
            matcher.appendReplacement(result, "[" + replaced.toString().replace("\\", "\\\\") + "]");
        }
        matcher.appendTail(result);
        return result.toString();
    }

    @Override
    public void editComputerRacksSignals(ComputerRackSignalMap computerRackSignalMap) {
        ComputerRackSignalMap crSignalMap = computerRackSignalMapper.selectOne(Wrappers.lambdaQuery(ComputerRackSignalMap.class)
                .eq(ComputerRackSignalMap::getComputerRackId, computerRackSignalMap.getComputerRackId()));
        BoolStringResponse openResponse = checkOpenExpression(computerRackSignalMap.getOpenExpression());
        if (!Boolean.TRUE.equals(openResponse.valid())) {
            throw new BusinessException("开通率" + openResponse.message());
        }
        BoolStringResponse powerResponse = checkPowerExpression(computerRackSignalMap.getPowerExpression());
        if (!Boolean.TRUE.equals(powerResponse.valid())) {
            throw new BusinessException("功率" + powerResponse.message());
        }
        if (Objects.isNull(crSignalMap)) {
            // 新增
            computerRackSignalMapper.insert(computerRackSignalMap);
        } else {
            // 修改
            crSignalMap.setOpenExpression(computerRackSignalMap.getOpenExpression());
            crSignalMap.setPowerExpression(computerRackSignalMap.getPowerExpression());
            computerRackSignalMapper.updateById(crSignalMap);
        }
    }


    @Override
    public void deleteByRackIds(String ids) {
        List<Integer> idList = CharSequenceUtil.split(ids, ",").stream().filter(Objects::nonNull)
                .map(Integer::valueOf).toList();
        computerRackSignalMapper.delete(Wrappers.lambdaUpdate(ComputerRackSignalMap.class).in(ComputerRackSignalMap::getComputerRackId, idList));
    }


    @Override
    public List<ImportErrorInfoDTO> importComputerRacksSignals(List<ComputerRackSignaImportDTO> computerRackSignaImports) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<Integer> computerRackSignaDeleteList = new ArrayList<>();
        List<ComputerRackSignalMap> computerRackSignaInsertList = new ArrayList<>();
        List<ComputerRack> computerRacks = computerRackRepository.selectList(Wrappers.lambdaQuery(ComputerRack.class)
                .select(ComputerRack::getComputerRackId, ComputerRack::getComputerRackName));
        Map<String, Integer> computerRackMap = computerRacks.stream().collect(Collectors.toMap(ComputerRack::getComputerRackName, ComputerRack::getComputerRackId, (k1, k2) -> k1));
        for (int i = 0; i < computerRackSignaImports.size(); i++) {
            ComputerRackSignaImportDTO computerRackSignaImportDTO = computerRackSignaImports.get(i);
            // 校验数据
            if (verifyDataExist(computerRackSignaImportDTO, importErrorInfoList, i)) {
                continue;
            }
            // 通过机架名称寻找id
            Integer computerRackId = computerRackMap.get(computerRackSignaImportDTO.getComputerRackName());
            if (Objects.isNull(computerRackId)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ComputerRackSignaImportDTO.Fields.computerRackName, computerRackSignaImportDTO.getComputerRackName() + messageSourceUtil.getMessage("common.notFound")));
                continue;
            }
            // 解析开通率表达式
            String openExpression = computerRackSignaImportDTO.getOpenExpression();
            String parseOpenExpression = parseExpression(openExpression, importErrorInfoList, i, ComputerRackSignaImportDTO.Fields.openExpression, this::validateOpenExpression);
            if (CharSequenceUtil.isBlank(parseOpenExpression)) {
                continue;
            }
            // 解析功率表达式
            String powerExpression = computerRackSignaImportDTO.getPowerExpression();
            String parsePowerExpression = parseExpression(powerExpression, importErrorInfoList, i, ComputerRackSignaImportDTO.Fields.powerExpression, this::validatePowerExpression);
            if (CharSequenceUtil.isBlank(parsePowerExpression)) {
                continue;
            }
            ComputerRackSignalMap computerRackSignalMap = new ComputerRackSignalMap();
            computerRackSignalMap.setComputerRackId(computerRackId);
            computerRackSignalMap.setOpenExpression(parseOpenExpression);
            computerRackSignalMap.setPowerExpression(parsePowerExpression);
            computerRackSignaDeleteList.add(computerRackId);
            computerRackSignaInsertList.add(computerRackSignalMap);
        }
        transactionTemplate.execute(status -> {
            if (CollUtil.isNotEmpty(computerRackSignaDeleteList)) {
                computerRackSignalMapper.delete(Wrappers.lambdaUpdate(ComputerRackSignalMap.class).in(ComputerRackSignalMap::getComputerRackId, computerRackSignaDeleteList));
                computerRackSignalMapper.insert(computerRackSignaInsertList);
            }
            return null; // 返回成功才会提交事务
        });
        return importErrorInfoList;
    }

    @Override
    public ComputerRackSignalMap findByRackId(Integer rackId) {
        return computerRackSignalMapper.selectOne(Wrappers.lambdaQuery(ComputerRackSignalMap.class)
                .eq(ComputerRackSignalMap::getComputerRackId, rackId));
    }

    @Override
    public Double calculatePowerExpression(String powerExpression) {
        // 正则匹配 [设备id\信号id] 形式的表达式
        Pattern pattern = Pattern.compile("\\[(.+?)]");
        Matcher matcher = pattern.matcher(powerExpression);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String original = matcher.group(1); // 设备id\信号id
            String[] parts = original.split("\\\\"); // 按照 "\" 分割
            // 依次替换每个部分
            Integer equipmentId = Integer.valueOf(parts[0]);
            Integer signalId = Integer.valueOf(parts[1]);
            OnlineState onlineState = equipmentStateManager.getEquipmentOnlineStateById(equipmentId);
            if (Objects.equals(onlineState, OnlineState.ONLINE)) {
                // 设备在线 用信号值替换匹配项
                List<Integer> signalIds = Collections.singletonList(signalId);
                List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
                if (CollUtil.isNotEmpty(signalList) && Objects.nonNull(signalList.get(0).getOriginalValue())) {
                    String currentValue = signalList.get(0).getOriginalValue();
                    matcher.appendReplacement(result, currentValue);
                } else {
                    matcher.appendReplacement(result, "0");
                }
            } else {
                // 设备没在线，不参与计算，当0处理
                matcher.appendReplacement(result, "0");
            }
        }
        matcher.appendTail(result);
        // 计算返回值
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        // 直接获取 Double 类型的值，避免额外的转换
        return parser.parseExpression(result.toString()).getValue(context, Double.class);
    }

    @Override
    public Boolean calculateOpenExpression(String openExpression) {
        if (CharSequenceUtil.isBlank(openExpression)) {
            return Boolean.FALSE;
        }
        // 正则匹配 [设备id\信号id] 形式的表达式
        Pattern pattern = Pattern.compile("\\[(.+?)]");
        Matcher matcher = pattern.matcher(openExpression);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String original = matcher.group(1); // 设备id\信号id
            String[] parts = original.split("\\\\"); // 按照 "\" 分割
            // 依次替换每个部分
            Integer equipmentId = Integer.valueOf(parts[0]);
            Integer signalId = Integer.valueOf(parts[1]);
            // 设备在线 用信号值替换匹配项
            OnlineState onlineState = equipmentStateManager.getEquipmentOnlineStateById(equipmentId);
            if (Objects.equals(onlineState, OnlineState.ONLINE)) {
                List<Integer> signalIds = Collections.singletonList(signalId);
                List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
                if (CollUtil.isNotEmpty(signalList) && Objects.nonNull(signalList.get(0).getOriginalValue())) {
                    String currentValue = signalList.get(0).getOriginalValue();
                    matcher.appendReplacement(result, currentValue);
                } else {
                    // 没有信号值时，这一处计算默认为false
                    matcher.appendReplacement(result, "T(java.lang.Double).NaN");
                }
            } else {
                // 设备没在线，不参与计算，这处计算为false
                matcher.appendReplacement(result, "T(java.lang.Double).NaN");
            }

        }
        matcher.appendTail(result);
        // 计算返回值
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        return parser.parseExpression(result.toString()).getValue(context, Boolean.class);
    }

    @Override
    public List<ComputerRackSignalMap> findByComputerRackIds(Collection<Integer> computerRackIds) {
        return computerRackSignalMapper.selectList(Wrappers.lambdaQuery(ComputerRackSignalMap.class)
                .in(ComputerRackSignalMap::getComputerRackId, computerRackIds));
    }

    @Override
    public BoolStringResponse checkOpenExpression(String openExpression) {
        Pattern pattern = Pattern.compile("\\[(.+?)]");
        Matcher matcher = pattern.matcher(openExpression);
        StringBuilder result = new StringBuilder();
        boolean flag = false;
        while (matcher.find()) {
            flag = true;
            String original = matcher.group(1); // 设备名称\信号名称
            String[] parts = original.split("\\\\"); // 按照 "\" 分割
            if (parts.length != 2) {
                return BoolStringResponse.fail("设备名称，信号名称未按照正确格式`\\`切分");
            }
            // 依次替换每个部分
            StringBuilder replaced = new StringBuilder();
            Integer equipmentId = Integer.valueOf(parts[0]);
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if (Objects.isNull(equipment)) {
                return BoolStringResponse.fail("设备名称未找到");
            }
            replaced.append(equipment.getEquipmentName());
            replaced.append("\\\\"); // 保持原本的 `\` 结构
            Integer signalId = Integer.valueOf(parts[1]);
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipment.getEquipmentId(), signalId);
            if (Objects.isNull(configSignalItem)) {
                return BoolStringResponse.fail("信号名称未找到");
            }
            replaced.append(configSignalItem.getSignalName());
            // 替换匹配项
            matcher.appendReplacement(result, "[" + replaced + "]");
        }
        if (!flag) {
            return BoolStringResponse.fail("设备名称，信号名称未正确放在[]中");
        }
        String evaluateExpression = openExpression.replaceAll("\\[(.+?)]", "1");
        // 校验计算逻辑
        boolean isValid = validateOpenExpression(evaluateExpression);
        if (!isValid) {
            return BoolStringResponse.fail("表达式异常,返回结果不是布尔类型,解析失败");
        }
        matcher.appendTail(result);
        return BoolStringResponse.ok(result.toString());
    }

    @Override
    public BoolStringResponse checkPowerExpression(String powerExpression) {
        Pattern pattern = Pattern.compile("\\[(.+?)]");
        Matcher matcher = pattern.matcher(powerExpression);
        StringBuilder result = new StringBuilder();
        boolean flag = false;
        while (matcher.find()) {
            flag = true;
            String original = matcher.group(1); // 设备名称\信号名称
            String[] parts = original.split("\\\\"); // 按照 "\" 分割
            if (parts.length != 2) {
                return BoolStringResponse.fail("设备名称，信号名称未按照正确格式`\\`切分");
            }
            // 依次替换每个部分
            StringBuilder replaced = new StringBuilder();
            Integer equipmentId = Integer.valueOf(parts[0]);
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if (Objects.isNull(equipment)) {
                return BoolStringResponse.fail("设备名称未找到");
            }
            replaced.append(equipment.getEquipmentName());
            replaced.append("\\\\"); // 保持原本的 `\` 结构
            Integer signalId = Integer.valueOf(parts[1]);
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipment.getEquipmentId(), signalId);
            if (Objects.isNull(configSignalItem)) {
                return BoolStringResponse.fail("信号名称未找到");
            }
            replaced.append(configSignalItem.getSignalName());
            // 替换匹配项
            matcher.appendReplacement(result, "[" + replaced + "]");
        }
        if (!flag) {
            return BoolStringResponse.fail("设备名称，信号名称未正确放在[]中");
        }
        String evaluateExpression = powerExpression.replaceAll("\\[(.+?)]", "1");
        // 校验计算逻辑
        boolean isValid = validatePowerExpression(evaluateExpression);
        if (!isValid) {
            return BoolStringResponse.fail("表达式异常,返回结果不是数字类型,解析失败");
        }
        matcher.appendTail(result);
        return BoolStringResponse.ok(result.toString());
    }

    /**
     * 转换表达式 将名称替换为id [设备id\信号id]
     */
    private String parseExpression(String expression, List<ImportErrorInfoDTO> importErrorInfoList, int i, String columnName, Predicate<String> validateFunction) {
        // 正则匹配 [设备名称\信号名称] 形式的表达式
        Pattern pattern = Pattern.compile("\\[(.+?)]");
        Matcher matcher = pattern.matcher(expression);
        StringBuilder result = new StringBuilder();
        boolean flag = false;
        while (matcher.find()) {
            flag = true;
            String original = matcher.group(1); // 设备名称\信号名称
            String[] parts = original.split("\\\\"); // 按照 "\" 分割
            if (parts.length != 2) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, columnName, "设备名称，信号名称未按照正确格式`\\`切分"));
                return null;
            }
            // 依次替换每个部分
            StringBuilder replaced = new StringBuilder();
            String equipmentName = parts[0];
            Equipment equipment = equipmentManager.getEquipmentsByEquipmentName(equipmentName);
            if (Objects.isNull(equipment)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, columnName, equipmentName + messageSourceUtil.getMessage("common.notFound")));
                return null;
            }
            replaced.append(equipment.getEquipmentId());
            replaced.append("\\"); // 保持原本的 `\` 结构
            String signalName = parts[1];
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalName(equipment.getEquipmentId(), signalName);
            if (Objects.isNull(configSignalItem)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, columnName, signalName + messageSourceUtil.getMessage("common.notFound")));
                return null;
            }
            replaced.append(configSignalItem.getSignalId());
            // 替换匹配项
            matcher.appendReplacement(result, "[" + replaced.toString().replace("\\", "\\\\") + "]");
        }
        if (!flag) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, columnName, "设备名称，信号名称未正确放在[]中"));
            return null;
        }
        String evaluateExpression = expression.replaceAll("\\[(.+?)]", "1");
        // 校验计算逻辑
        boolean isValid = validateFunction.test(evaluateExpression);
        if (!isValid) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, columnName, expression + " 表达式异常,解析失败"));
            return null;
        }
        matcher.appendTail(result);
        String parseExpression = result.toString();
        if (CharSequenceUtil.isBlank(parseExpression)) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, columnName, "表达式异常,解析后表达式为空"));
            return null;
        }
        return result.toString();
    }

    /**
     * 校验开通率表达式是否正常 开通率表达式计算结果必须为布尔
     *
     * @param expression 表达式 id转成了1
     */
    private boolean validateOpenExpression(String expression) {
        try {
            ExpressionParser parser = new SpelExpressionParser();
            StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext();
            // 开通率计算返回值只需要布尔类型
            Object obj = parser.parseExpression(expression).getValue(standardEvaluationContext);
            return obj instanceof Boolean; // 只有 Boolean 类型才返回 true，否则返回 false
        } catch (Exception e) {
            return false; // 解析失败，表达式无效
        }
    }

    /**
     * 校验功率表达式是否正常 功率表达式中间用+分隔
     */
    private boolean validatePowerExpression(String expression) {
        try {
            ExpressionParser parser = new SpelExpressionParser();
            StandardEvaluationContext context = new StandardEvaluationContext();
            // 直接获取 Double 类型的值，避免额外的转换
            Double value = parser.parseExpression(expression).getValue(context, Double.class);
            return value != null; // 只要解析为 Double 不为 null 就认为是有效表达式
        } catch (Exception e) {
            return false; // 解析失败，表达式无效
        }
    }

    /**
     * 校验数据是否合法
     */
    private boolean verifyDataExist(ComputerRackSignaImportDTO computerRackSignaImportDTO, List<ImportErrorInfoDTO> importErrorInfoList, int i) {
        boolean flag = false;
        if (CharSequenceUtil.isBlank(computerRackSignaImportDTO.getComputerRackName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ComputerRackSignaImportDTO.Fields.computerRackName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(computerRackSignaImportDTO.getOpenExpression())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ComputerRackSignaImportDTO.Fields.openExpression, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(computerRackSignaImportDTO.getPowerExpression())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, ComputerRackSignaImportDTO.Fields.powerExpression, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        return flag;
    }

    /**
     * 应用排序到机架信号映射列表
     *
     * @param signals 需要排序的机架信号映射列表
     * @param sort 排序参数
     * @return 排序后的列表
     */
    private List<ComputerRackSignalVO> applySorting(List<ComputerRackSignalVO> signals, org.springframework.data.domain.Sort sort) {
        if (sort == null || !sort.iterator().hasNext()) {
            // 默认按机架名称排序
            return signals.stream()
                    .sorted(Comparator.comparing(ComputerRackSignalVO::getComputerRackName, Comparator.nullsLast(String::compareToIgnoreCase)))
                    .collect(Collectors.toList());
        }

        List<ComputerRackSignalVO> result = new ArrayList<>(signals);

        // 处理排序参数
        sort.forEach(order -> {
            String property = order.getProperty();
            boolean isAscending = order.getDirection().isAscending();

            Comparator<ComputerRackSignalVO> comparator = null;

            // 根据属性名称选择比较器
            switch (property.toLowerCase()) {
                case "computerrackid":
                    comparator = Comparator.comparing(ComputerRackSignalVO::getComputerRackId,
                            Comparator.nullsLast(Integer::compareTo));
                    break;
                case "computerrackname":
                    comparator = Comparator.comparing(ComputerRackSignalVO::getComputerRackName,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
                case "computerracknumber":
                    comparator = Comparator.comparing(ComputerRackSignalVO::getComputerRackNumber,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
                case "computerrackposition":
                    comparator = Comparator.comparing(
                            signal -> signal.getComputerRackPosition() != null ? signal.getComputerRackPosition() : "",
                            String::compareToIgnoreCase);
                    break;
                default:
                    // 如果属性不支持，则使用默认的机架名称排序
                    comparator = Comparator.comparing(ComputerRackSignalVO::getComputerRackName,
                            Comparator.nullsLast(String::compareToIgnoreCase));
                    break;
            }

            // 如果是降序，则反转比较器
            if (!isAscending) {
                comparator = comparator.reversed();
            }

            // 应用排序
            result.sort(comparator);
        });

        return result;
    }
}