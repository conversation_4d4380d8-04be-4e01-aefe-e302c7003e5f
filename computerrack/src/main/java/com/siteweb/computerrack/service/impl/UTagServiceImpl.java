package com.siteweb.computerrack.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.security.LanguageFilter;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.dto.UTagDto;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.entity.UTag;
import com.siteweb.computerrack.exception.TagExceptionCode;
import com.siteweb.computerrack.mapper.UTagMapper;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.computerrack.service.UTagService;
import com.siteweb.computerrack.vo.UTagVo;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.vo.NameValueVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UTagServiceImpl implements UTagService {
    @Autowired
    private UTagMapper uTagMapper;
    @Autowired
    private ITDeviceService itDeviceService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Lazy
    @Autowired
    private UTagService uTagService;
    @Override
    public List<UTagVo> findAll() {
        return uTagMapper.findAll();
    }

    @Override
    public UTag createTag(UTagDto uTagDto) {
        //由于一对一关系。需要把以前绑定的资产置为null
        this.setAsserIdIsNull(uTagDto.getAsserId());
        UTag uTag = BeanUtil.copyProperties(uTagDto, UTag.class);
        uTag.setCreateTime(new Date());
        uTag.setUpdateTime(new Date());
        uTagMapper.insert(uTag);
        return uTag;
    }
    private UTag findByTagValue(String tagValue){
        if (CharSequenceUtil.isBlank(tagValue)) {
            return null;
        }
        return uTagMapper.selectOne(Wrappers.<UTag>lambdaQuery().eq(UTag::getTagValue,tagValue));
    }
    @Override
    public boolean existsByTagValueNoTagId(String tagValue, Integer tagId) {
        return uTagMapper.exists(Wrappers.<UTag>lambdaQuery()
                                         .eq(CharSequenceUtil.isNotBlank(tagValue), UTag::getTagValue, tagValue)
                                         .ne(ObjectUtil.isNotNull(tagId), UTag::getUTagId, tagId));
    }

    @Override
    public UTag deleteTag(Integer id) {
        UTag uTag = uTagMapper.selectById(id);
        if (ObjectUtil.isNull(uTag)) {
            return null;
        }
        uTagMapper.deleteById(id);
        return uTag;
    }

    @Override
    public UTag updateTag(UTagDto uTagDto) {
        //由于一对一关系。需要把以前绑定的资产置为null
        this.setAsserIdIsNull(uTagDto.getAsserId());
        UTag utag = BeanUtil.copyProperties(uTagDto, UTag.class);
        utag.setUpdateTime(new Date());
        uTagMapper.updateById(utag);
        return utag;
    }

    @Override
    public UTagDto findTagById(Integer id) {
        return uTagMapper.findTagById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportErrorInfoDTO> createTagList(List<UTagDto> uTagDtoList) {
        if (CollUtil.isEmpty(uTagDtoList)) {
            return Collections.emptyList();
        }
        Set<Integer> itDeviceSet = findAllBindITDeviceIds();
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        for (int i = 0; i < uTagDtoList.size(); i++) {
            UTagDto uTagDto = uTagDtoList.get(i);
            if (CharSequenceUtil.isBlank(uTagDto.getTagValue())) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "tagValue", messageSourceUtil.getMessage("udevice.utag.codeEmpty")));
                continue;
            }
            ITDevice itDevice = itDeviceService.findITDevice(uTagDto.getItDeviceName());
            if (ObjectUtil.isNotNull(itDevice) && !itDeviceSet.contains(itDevice.getITDeviceId())) {
                uTagDto.setAsserId(itDevice.getITDeviceId());
            }
            this.createOrUpdate(uTagDto.getTagValue(), uTagDto.getAsserId());
        }
        return importErrorInfoList;
    }

    /**
     * 获取已经被绑定的IT设备IDS
     * @return {@link Set }<{@link Integer }> 已经被绑定的IT设备IdSet
     */
    private Set<Integer> findAllBindITDeviceIds() {
        List<UTag> uTags = uTagMapper.selectList(Wrappers.lambdaQuery(UTag.class)
                                                         .select(UTag::getAsserId)
                                                         .isNotNull(UTag::getAsserId));
        return uTags.stream().map(UTag::getAsserId).collect(Collectors.toSet());
    }

    public void createOrUpdate(String tagValue,Integer asserId){
        UTag byTagValue = findByTagValue(tagValue);
        //存在则更新
        if (ObjectUtil.isNotNull(byTagValue)) {
            byTagValue.setAsserId(asserId);
            byTagValue.setUpdateTime(new Date());
            uTagMapper.updateById(byTagValue);
            return;
        }
        //不存在添加
        UTag uTag = new UTag(tagValue, asserId, new Date(), new Date());
        uTagMapper.insert(uTag);
    }

    @Override
    public NameValueVO findTagRateByResourceStructureIds(List<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return new NameValueVO(messageSourceUtil.getMessageByHeader("udevice.utag.bindingrate", LanguageFilter.getCurrentThreadLanguage()), 0.0);
        }
        NameValueVO tagReta = uTagMapper.findTagRateByResourceStructureIds(resourceStructureIds);
        if (ObjectUtil.isNull(tagReta.getValue())) {
            tagReta.setValue(0.0);
        }
        tagReta.setName(messageSourceUtil.getMessageByHeader("udevice.utag.bindingrate",LanguageFilter.getCurrentThreadLanguage()));
        return tagReta;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UTag createTagBindItDevice(UTagDto uTagDto) {
        UTag uTag = findByTagValue(uTagDto.getTagValue());
        String tagValue = uTagDto.getTagValue();
        Integer asserId = uTagDto.getAsserId();


        if (uTagMapper.exists(Wrappers.lambdaUpdate(UTag.class).eq(UTag::getAsserId, asserId))) {
            if (!uTagDto.getForce()) {
                throw new BusinessException(new TagExceptionCode("该设备已绑定标签", "1"));
            }// 清理设备绑定标签数据
            uTagService.setAsserIdIsNull(asserId);
        }

        if (ObjectUtil.isNotNull(uTag)) {
            // 1. 标签是否已绑定设备
            if (ObjectUtil.isNotEmpty(uTag.getAsserId())) {
                if (!uTagDto.getForce()) {
                    throw new BusinessException(new TagExceptionCode("该标签已绑定it设备", "2"));
                }
            }
            uTag.setAsserId(asserId);
            uTag.setUpdateTime(new Date());
            uTagMapper.updateById(uTag);
            return uTag;
        }else {
            uTag = new UTag(tagValue, asserId, new Date(), new Date());
            uTagMapper.insert(uTag);
            return uTag;
        }
    }

    /**
     * 设置标签绑定的资产为null,主要用于增加或者修改覆盖
     * @param asserId 资产id
     */
    @Override
    public void setAsserIdIsNull(Integer asserId){
        if (ObjectUtil.isNull(asserId)) {
            return;
        }
        uTagMapper.update(null, Wrappers.<UTag>lambdaUpdate()
                                        .eq(UTag::getAsserId, asserId)
                                        .set(UTag::getUpdateTime, new Date())
                                        .set(UTag::getAsserId, null));
    }
}
