package com.siteweb.computerrack.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.capacity.dto.CapacityAttributeCreateItem;
import com.siteweb.capacity.dto.CapacityAttributeCreatedParam;
import com.siteweb.capacity.dto.CapacityUpdateAttributeValue;
import com.siteweb.capacity.entity.CapacityAttribute;
import com.siteweb.capacity.entity.LogicType;
import com.siteweb.capacity.service.CapacityAttributeConfigureListener;
import com.siteweb.capacity.service.CapacityAttributeService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.StringUtils;
import com.siteweb.computerrack.dto.*;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.enumeration.CapacityBaseAttributeEnum;
import com.siteweb.computerrack.enumeration.ComputerRackCapacity;
import com.siteweb.computerrack.mapper.ComputerRackMapper;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.computerrack.service.UDeviceService;
import com.siteweb.computerrack.vo.RackUIndexRateVo;
import com.siteweb.monitoring.dto.ResourceObject;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceObjectService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service()
//@Component("computerRackService")
//@DependsOn({"capacityAttributeService"})
//@RequiredArgsConstructor
public class ComputerRackServiceImpl implements ComputerRackService, ResourceObjectService, CapacityAttributeConfigureListener {

    private static final String NOT_FOUND = "找不到";

    @Autowired
    private ComputerRackMapper computerRackRepository;

    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    @Lazy
    private CapacityAttributeService capacityAttributeService;

    @Autowired
    @Lazy
    private ITDeviceService itDeviceService;

    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    private UDeviceService uDeviceService;
    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    SystemConfigService systemConfigService;

    @Override
    public void onUpdateCapacityAttributeConfigyre(CapacityAttribute _attribute) {
        if (_attribute.getObjectId().equals(SourceType.COMPUTERRACK)) {
            ComputerRack computerRack = this.findComputerRack(_attribute.getObjectId());
            if (computerRack == null) return;
            this.updateCapacityValues(computerRack);
        }
    }


    @Override
    public List<ComputerRack> findComputerRacks() {
        return computerRackRepository.selectList(null);
    }


    @Override
    public List<ComputerRack> findComputerRacks(Integer resourceStructureId, boolean isRecursion) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if (resourceStructure == null) return new ArrayList<>();
        List<Integer> structureIds = new ArrayList<Integer>();
        structureIds.add(resourceStructureId);
        if (isRecursion) {
            getSubStructures(resourceStructure, structureIds);
        }
        return computerRackRepository.findByResourceStructureIdIn(structureIds);
    }

    @Override
    public ComputerRack findComputerRack(Integer computerRackId) {
        return computerRackRepository.findByComputerRackId(computerRackId);
    }

    @Override
    public ComputerRack findByComputerRackName(String computerRackName) {
        return computerRackRepository.findByComputerRackName(computerRackName);
    }

    @Override
    public List<ComputerRack> findByComputerRackIdList(String computerRackIdList) {
        List<Integer> computerRackIds = StringUtils.getIntegerListByString(computerRackIdList);
        return computerRackRepository.findByComputerRackIdIn(computerRackIds);
    }

    @Override
    public List<SimpleComputerRackDTO> findSimpleComputerRacks(List<Integer> computerRackIds) {
        if(computerRackIds == null || computerRackIds.isEmpty()) return  List.of();
        var racks = computerRackRepository.findSimpleComputerRacks(computerRackIds);
        var rackITDevicesMap  = itDeviceService.getOnShelfITDeviceMap(computerRackIds);
        for(var rack : racks){
            var attribute = capacityAttributeService.findAttribute(rack.getGlobal(),"rackSpace");
            if(attribute != null && attribute.getRatedCapacity() != null){
                rack.setUHeight(attribute.getRatedCapacity().intValue());
            }
            var itDevices =rackITDevicesMap.get(rack.getComputerRackId());
            if (itDevices != null){
                rack.setItDevices(itDevices);
            }
        }
        return racks;
    }

    @Override
    public List<DetailedComputerRackDTO> findAllDetailedComputerRacks() {
        List<Integer> computerRackIds = this.findComputerRacks().stream().map(ComputerRack::getComputerRackId).toList();
        List<SimpleComputerRackDTO> simpleComputerRackDTOList = this.findSimpleComputerRacks(computerRackIds);
        Map<Integer, SimpleComputerRackDTO> simpleComputerRackDTOMap =
                simpleComputerRackDTOList.stream().collect(Collectors.toMap(SimpleComputerRackDTO::getComputerRackId, dto -> dto));
        return this.findComputerRacks().stream().parallel().map(rack -> {
            DetailedComputerRackDTO detailedDTO = DetailedComputerRackDTO.build(rack, this.capacityAttributeService);
            detailedDTO.setPosition(resourceStructureManager.getFullPath(rack.getResourceStructureId()));
            detailedDTO.setSimpleItDevices(simpleComputerRackDTOMap.get(rack.getComputerRackId()).getItDevices());
            detailedDTO.setUHeight(simpleComputerRackDTOMap.get(rack.getComputerRackId()).getUHeight());
            return detailedDTO;
        }).sorted(Comparator.comparing(DetailedComputerRackDTO::getComputerRackId)).toList();
    }

    @Override
    public List<ResourceStructure> findAllResourceStructure() {

        return this.findComputerRacks().stream()
                .map(e -> resourceStructureManager.getResourceStructureById(e.getResourceStructureId()))
                .filter(e -> e != null)
                .distinct()
                .collect(Collectors.toList());

    }

    @Override
    public List<ComputerRackDTO> findComputerRackDTOs() {
        return computerRackRepository.findComputerRackDTO()
                                     .stream()
                                     .parallel()
                                     .map(rack -> {
                                         ComputerRackDTO dto = ComputerRackDTO.build(rack, this.capacityAttributeService);
                                         dto.setPosition(resourceStructureManager.getFullPath(rack.getResourceStructureId()));
                                         return dto;
                                     })
                                     .toList();
    }

    @Override
    public List<ComputerRackDTO> findComputerRackDTOsByUserId(Integer userId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findThisHierarchyResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }
        return this.findComputerRacksByResourceStructureIds(resourceStructureIds)
                .stream()
                .map(rack -> {
                    ComputerRackDTO dto = ComputerRackDTO.build(rack, this.capacityAttributeService);
                    //设置u位设备的唯一编码
                    String uDeviceNumber = uDeviceService.findUDeviceNumberByRackId(rack.getComputerRackId());
                    dto.setUDeviceNumber(uDeviceNumber);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取单个机架
     *
     * @param computerRackId 机架ID
     * @return
     */
    @Override
    public ComputerRackDTO findComputerRackDTO(Integer computerRackId) {
        ComputerRackDTO computerRackDTO = ComputerRackDTO.build(this.findComputerRack(computerRackId),
                this.capacityAttributeService);
        //设置u位设备的唯一编码
        String uDeviceNumber = uDeviceService.findUDeviceNumberByRackId(computerRackId);
        if (CharSequenceUtil.isNotBlank(uDeviceNumber)) {
            computerRackDTO.setUDeviceNumber(uDeviceNumber);
            computerRackDTO.setUDeviceName(equipmentManager.getEquipmentById(Integer.valueOf(uDeviceNumber)).getEquipmentName());
        }
        computerRackDTO.setPosition(resourceStructureManager.getFullPath(computerRackDTO.getResourceStructureId()));
        return computerRackDTO;
    }


    @Override
    public List<ComputerRackDTO> findDTOByComputerRackIdList(String computerRackIdList) {
        return this.findByComputerRackIdList(computerRackIdList).stream()
                .map(rack -> ComputerRackDTO.build(rack, this.capacityAttributeService))
                .collect(Collectors.toList());
    }


    /**
     * 获取层级结构下所有机架
     *
     * @param resourceStructureId 层级结构ID
     * @param isRecursion         是否递归
     * @return
     */
    @Override
    public List<ComputerRackDTO> findComputerRackDTOs(Integer resourceStructureId, boolean isRecursion) {
        return this.findComputerRacks(resourceStructureId, isRecursion).stream()
                .map(rack -> ComputerRackDTO.build(rack, this.capacityAttributeService))
                .collect(Collectors.toList());

    }


    private void getSubStructures(ResourceStructure resourceStructure, List<Integer> out) {
        for (ResourceStructure structure : resourceStructure.getChildren()) {
            this.getSubStructures(structure, out);
            out.add(structure.getResourceStructureId());
        }
    }


    @Override
    public ComputerRackDTO findDTOByComputerRackName(String computerRackName) {
        ComputerRack rack = this.findByComputerRackName(computerRackName);
        return ComputerRackDTO.build(rack, this.capacityAttributeService);
    }


    @Override
    public List<ComputerRack> findComputerRacksByCustomer(String customer) {
        return computerRackRepository.findByCustomer(customer);
    }

    @Override
    public List<ComputerRack> findComputerRacksByResourceStructureIds(List<Integer> resourceStructureIds) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }
        return computerRackRepository.selectList(Wrappers.<ComputerRack>lambdaQuery()
                .in(ComputerRack::getResourceStructureId, resourceStructureIds));
    }

    @Override
    public List<RackUIndexRateVo> findRackUIndexAttribute(List<Integer> resourceStructureIds, Integer topN) {
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return new ArrayList<>();
        }
        if (ObjectUtil.isNull(topN)) {
            topN = 10;
        }
        return computerRackRepository.findRackUIndexAttribute(resourceStructureIds, topN);
    }


    @Override
    public void deleteById(Integer computerRackId) {
        ResourceObject object = new ResourceObject();
        object.setObjectId(computerRackId);
        object.setObjectTypeId(SourceType.COMPUTERRACK.value());
        capacityAttributeService.deleteByObject(object);
        computerRackRepository.deleteById(computerRackId);
    }


    @Override
    public ComputerRack createComputerRack(ComputerRackImportDTO rackDTO) {
        // 检测机架编号
        ComputerRack rack = this.computerRackRepository.selectOne(Wrappers.lambdaQuery(ComputerRack.class)
                                                                          .eq(ComputerRack::getComputerRackNumber,rackDTO.getComputerRackNumber()));
        if (rack != null) {
            throw new BusinessException("机架编号重复");
        }
        // 检测摆放位置。
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureByName(rackDTO.getRoomName());
        if (resourceStructure == null) {
            throw new BusinessException("摆放位置不存在");
        }
        // 填充 位置
        ComputerRack computerRack = rackDTO.toComputerRack();
        computerRack.setResourceStructureId(resourceStructure.getResourceStructureId());
        computerRack.setPosition(resourceStructureManager.getLevelOfPathName(resourceStructure.getLevelOfPath()));
        computerRack.setStartTime(new Date());
        computerRackRepository.insert(computerRack);
        this.initRatedAttribute(computerRack, rackDTO);
        return computerRack;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ComputerRackDTO updateComputerRack(ComputerRackDTO rackDTO) {
        if (rackDTO.getComputerRackId() == null) return null;
        ComputerRack computerRack = new ComputerRack();
        computerRack.copyDTO(rackDTO);
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(rackDTO.getResourceStructureId());
        if (resourceStructure == null) return null;
        computerRack.setPosition(resourceStructureManager.getLevelOfPathName(resourceStructure.getLevelOfPath()));
        computerRackRepository.updateById(computerRack);
        this.updateCapacityValues(computerRack);
        //更新U位管理的机架id
        uDeviceService.updateRackNumber(rackDTO.getUDeviceNumber(), rackDTO.getComputerRackId());
        return rackDTO;
    }


    private void checkData(List<ComputerRackDTO> computerRacks) {


    }


    private void fullCapacityAttribute(ComputerRack computerRack) {

    }


    @Override
    @Transactional
    public List<ImportErrorInfoDTO> createComputerRackList(List<ComputerRackImportDTO> computerRackTemplateDTOList) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        Map<String, Equipment> equipmentMap = equipmentManager.getAllEquipments().stream().collect(Collectors.toMap(Equipment::getEquipmentName, e -> e, (v1, v2) -> v1));
        for (int i = 0; i < computerRackTemplateDTOList.size(); i++) {
            ComputerRackImportDTO rackImportDTO = computerRackTemplateDTOList.get(i);
            try {
                // 检测摆放位置。
                ResourceStructure resourceStructure = resourceStructureManager.getByFullResourceStructureName(rackImportDTO.getRoomName());
                if (resourceStructure == null) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "roomName", NOT_FOUND + rackImportDTO.getRoomName()));
                    continue;
                }
                if (CharSequenceUtil.isNotBlank(rackImportDTO.getUDeviceName()) && !equipmentMap.containsKey(rackImportDTO.getUDeviceName())) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "udeviceName", NOT_FOUND + rackImportDTO.getUDeviceName()));
                    continue;
                }
                // 检测机架编号
                ComputerRack computerRack = this.computerRackRepository.findByComputerRackNumber(rackImportDTO.getComputerRackNumber());
                if (computerRack == null) {
                    computerRack = rackImportDTO.toComputerRack();
                    computerRack.setStartTime(new Date());
                    computerRack.setResourceStructureId(resourceStructure.getResourceStructureId());
                    computerRack.setPosition(resourceStructureManager.getLevelOfPathName(resourceStructure.getLevelOfPath()));
                    computerRackRepository.insert(computerRack);
                    this.initRatedAttribute(computerRack, rackImportDTO);
                } else {
                    computerRack.setComputerRackName(rackImportDTO.getComputerRackName());
                    computerRack.setCustomer(rackImportDTO.getCustomer());
                    computerRack.setRemark(rackImportDTO.getRemark());
                    computerRack.setBusiness(rackImportDTO.getBusiness());
                    computerRack.setResourceStructureId(resourceStructure.getResourceStructureId());
                    computerRack.setPosition(resourceStructureManager.getLevelOfPathName(resourceStructure.getLevelOfPath()));
                    computerRackRepository.updateById(computerRack);
                    this.updateRatedAttributes(computerRack, rackImportDTO);
                    this.updateCapacityValues(computerRack);
                }
                //更新u位设备绑定的机架 通过equipmentName获取equipmentId;
                if (CharSequenceUtil.isNotBlank(rackImportDTO.getUDeviceName())) {
                    Integer uDeviceNumber = equipmentMap.get(rackImportDTO.getUDeviceName()).getEquipmentId();
                    uDeviceService.updateRackNumber(uDeviceNumber.toString(), computerRack.getComputerRackId());
                }
            } catch (Exception ex) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(null, "Exception", ex.getMessage()));
            }
        }
        return importErrorInfoList;
    }




    /**
     * 初始化 机架的容量属性。
     * 机架必须已存在
     *
     * @param importDTO
     */

    private void updateRatedAttributes(ComputerRack computerRack, ComputerRackImportDTO importDTO) {
        List<CapacityAttribute> needUpdates = new ArrayList<>();
        // 空间容量
        CapacityAttribute _space = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_SPACE.getValue());
        if (importDTO.getRatedUHeight() != null && _space != null && !_space.getRatedCapacity().equals(importDTO.getRatedUHeight().doubleValue())) {
            _space.setRatedCapacity(importDTO.getRatedUHeight().doubleValue());
            needUpdates.add(_space);
        }
        // 承重容量
        CapacityAttribute _weight = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_WEIGHT.getValue());
        if (importDTO.getRatedWeight() != null && _weight != null && !_weight.getRatedCapacity().equals(importDTO.getRatedWeight())) {
            _weight.setRatedCapacity(importDTO.getRatedWeight());
            needUpdates.add(_weight);
        }
        // 制冷容量
        CapacityAttribute _cooling = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_COOLING.getValue());
        if (importDTO.getRatedCooling() != null && _cooling != null && !_cooling.getRatedCapacity().equals(importDTO.getRatedCooling())) {
            _cooling.setRatedCapacity(importDTO.getRatedCooling());
            needUpdates.add(_cooling);
        }
        // 电力容量
        CapacityAttribute _power = capacityAttributeService.findAttribute(computerRack.getGlobal(), ComputerRackCapacity.RACK_POWER.getValue());
        if (_power != null) {
            if (importDTO.getRatedPower() != null && !_power.getRatedCapacity().equals(importDTO.getRatedPower())) {
                _power.setRatedCapacity(importDTO.getRatedPower());
            }
//            if (computerRack.getPowerLogicType() != null && !_power.getLogicType().equals(computerRack.getPowerLogicType())) {
//                _power.setLogicType(computerRack.getPowerLogicType());
//            }
            needUpdates.add(_power);
        }
        if (needUpdates.size() > 0) {
            capacityAttributeService.updateAttributes(needUpdates);
        }
    }

    @Override
    public void updateCapacityValueByComputerRackIds(List<Integer> computerIds) {
        for (Integer computerRackId : computerIds) {
            this.updateCapacityValueByComputerRackId(computerRackId);
        }
    }

    @Override
    public void updateCapacityValueByComputerRackId(Integer computerId) {
        ComputerRack computerRack = this.findComputerRack(computerId);
        if (computerRack != null) {
            this.updateCapacityValues(computerRack);
        }
    }

    @Override
    public void updateCapacityValues(Collection<Integer> computerRackIdList) {
        if (CollUtil.isEmpty(computerRackIdList)) {
            return;
        }
        List<ComputerRack> computerRackList = this.findByComputerRackIdList(CollUtil.join(computerRackIdList, ","));
        for (ComputerRack computerRack : computerRackList) {
            this.updateCapacityValues(computerRack);
        }
    }

    /**
     * 更新机柜的所有容量属性值到容量系统
     *
     * @param computerRack
     */
    @Override
    public void updateCapacityValues(ComputerRack computerRack) {
        ITDevice lastDevice = null;
        Integer airUHeight = getAirUHeight();
        List<ITDevice> itDevices = itDeviceService.findByComputerRackId(computerRack.getComputerRackId());
        itDevices = itDevices.stream().sorted(Comparator.comparing(ITDevice::getUIndex)).toList();
        CapacityUpdateAttributeValue usedSpace = this.getUpdateAttributeValue(computerRack, ComputerRackCapacity.RACK_SPACE, 0d);
        CapacityUpdateAttributeValue usedWeight = this.getUpdateAttributeValue(computerRack, ComputerRackCapacity.RACK_WEIGHT, 0d);
        CapacityUpdateAttributeValue usedCooling = this.getUpdateAttributeValue(computerRack, ComputerRackCapacity.RACK_COOLING, 0d);
        CapacityUpdateAttributeValue usedPower = this.getUpdateAttributeValue(computerRack, ComputerRackCapacity.RACK_POWER, 0d);
        for (ITDevice itDevice : itDevices) {
            if (usedSpace != null) {
                usedSpace.setOriginValue(usedSpace.getOriginValue() + itDevice.getItDeviceModel().getUnitHeight());
                if (airUHeight > 0 && lastDevice != null) {
                    if (itDevice.getUIndex() - (lastDevice.getUIndex() + lastDevice.getUnitHeight()) >= airUHeight) {
                        usedSpace.setOriginValue(usedSpace.getOriginValue() + airUHeight);
                    }
                }
            }
            if (usedWeight != null)
                usedWeight.setOriginValue(usedWeight.getOriginValue() + itDevice.getItDeviceModel().getRateWeight());
            if (usedCooling != null)
                usedCooling.setOriginValue(usedCooling.getOriginValue() + itDevice.getItDeviceModel().getRateCooling());
            if (usedPower != null)
                usedPower.setOriginValue(usedPower.getOriginValue() + itDevice.getItDeviceModel().getRatePower());
            lastDevice = itDevice;
        }
        if (usedSpace != null && lastDevice != null) {
            usedSpace.setOriginValue(usedSpace.getOriginValue() + airUHeight);
        }
        List<CapacityUpdateAttributeValue> needUpdates = new ArrayList<>();
        if (usedSpace != null) needUpdates.add(usedSpace);
        if (usedWeight != null) needUpdates.add(usedWeight);
        if (usedCooling != null) needUpdates.add(usedCooling);
        if (usedPower != null) needUpdates.add(usedPower);
        if (CollUtil.isNotEmpty(needUpdates)) {
            capacityAttributeService.updateValues(needUpdates);
        }
    }


    public Integer getAirUHeight() {
        try {
            SystemConfig bySystemConfigKey = systemConfigService.findBySystemConfigKey("computerrack.airspace.height");
            if (!Objects.isNull(bySystemConfigKey) && StringUtils.isNotEmpty(bySystemConfigKey.getSystemConfigValue())) {
                return Integer.valueOf(bySystemConfigKey.getSystemConfigValue());
            }
            return 0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }


    private CapacityUpdateAttributeValue getUpdateAttributeValue(ComputerRack computerRack, ComputerRackCapacity capacity, Double originValue) {
        CapacityAttribute spaceCapacity = capacityAttributeService.findAttribute(computerRack.getGlobal(), capacity.getValue());
        if (spaceCapacity != null && spaceCapacity.getLogicType().equals(LogicType.PASSIVE_UPDATE)) {
            CapacityUpdateAttributeValue value = new CapacityUpdateAttributeValue();
            value.setAttributeId(spaceCapacity.getAttributeId());
            value.setOriginValue(originValue);
            return value;
        }
        return null;
    }


    private void initRatedAttribute(ComputerRack computerRack, ComputerRackImportDTO rackDTO) {
        List<CapacityAttributeCreateItem> attributes = new ArrayList<>();
        attributes.add(this.generateCapacityAttribute(ComputerRackCapacity.RACK_SPACE, rackDTO.getRatedUHeight().doubleValue(), null)); // 空间容量
        attributes.add(this.generateCapacityAttribute(ComputerRackCapacity.RACK_WEIGHT, rackDTO.getRatedWeight(), null)); // 承重容量
        attributes.add(this.generateCapacityAttribute(ComputerRackCapacity.RACK_COOLING, rackDTO.getRatedCooling(), null)); // 制冷容量
        attributes.add(this.generateCapacityAttribute(ComputerRackCapacity.RACK_POWER, rackDTO.getRatedPower(), rackDTO.getLogicType())); // 电力容量
        CapacityAttributeCreatedParam param = new CapacityAttributeCreatedParam();
        param.setObjectId(computerRack.getComputerRackId());
        param.setObjectTypeId(SourceType.COMPUTERRACK.value());
        param.setAttributes(attributes);
        List<CapacityAttributeCreatedParam> objects = new ArrayList<>();
        objects.add(param);
        capacityAttributeService.createAttributes(objects);
    }

    /**
     * 生成机柜容量属性
     *
     * @param capacity
     * @param ratedCapacity
     * @return
     */
    private CapacityAttributeCreateItem generateCapacityAttribute(ComputerRackCapacity capacity, Double ratedCapacity, LogicType logicType) {
        CapacityAttributeCreateItem attr = new CapacityAttributeCreateItem();
        attr.setBaseAttributeId(capacity.getValue());
        attr.setRatedCapacity(ratedCapacity);
        if (logicType != null) {
            attr.setLogicType(logicType);
        }
        return attr;
    }

    @Override
    public SourceType[] getSourceTypes() {
        return new SourceType[]{SourceType.COMPUTERRACK};
    }

    @Override
    public ResourceObjectEntity findResourceObjectEntity(Integer objectId) {
        ComputerRack rack = this.findComputerRack(objectId);
        if (rack == null) return null;
        ResourceObjectEntity entity = new ResourceObjectEntity();
        entity.setObjectId(rack.getComputerRackId());
        entity.setObjectTypeId(SourceType.COMPUTERRACK.value());
        entity.setResourceStructureId(rack.getResourceStructureId());
        entity.setResourceName(rack.getComputerRackName());
        ResourceStructure resourceStructureById = resourceStructureManager.getResourceStructureById(rack.getResourceStructureId());
        if (resourceStructureById == null) return null;
        entity.setParentResourceStructureId(resourceStructureById.getResourceStructureId());
        entity.setParentResourceStructureTypeId(resourceStructureById.getResourceStructureId());
        return entity;
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObject() {
        return this.findComputerRacks().stream().map(rack -> {
            ResourceObjectEntity entity = new ResourceObjectEntity();
            entity.setObjectId(rack.getComputerRackId());
            entity.setObjectTypeId(SourceType.COMPUTERRACK.value());
            entity.setResourceStructureId(rack.getResourceStructureId());
            entity.setResourceName(rack.getComputerRackName());
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(rack.getResourceStructureId());
            entity.setParentResourceStructureId(resourceStructure.getResourceStructureId());
            entity.setParentResourceStructureTypeId(resourceStructure.getStructureTypeId());
            return entity;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ResourceObjectEntity> findAllResourceObjectByUserId(Integer userId) {
        //通过用户id获取所拥有的权限层级
        List<ResourceStructure> resourceStructureByUserId = resourceStructureService.findResourceStructureByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureByUserId)) {
            return new ArrayList<>();
        }
        //通过层级获取机架
        List<Integer> resourceIds = resourceStructureByUserId.stream()
                .map(ResourceStructure::getResourceStructureId)
                .toList();
        List<ComputerRack> rackList = computerRackRepository.findByResourceStructureIdIn(resourceIds);
        return rackList.stream().map(rack -> {
            ResourceObjectEntity entity = new ResourceObjectEntity();
            entity.setObjectId(rack.getComputerRackId());
            entity.setObjectTypeId(SourceType.COMPUTERRACK.value());
            entity.setResourceStructureId(rack.getResourceStructureId());
            entity.setResourceName(rack.getComputerRackName());
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(rack.getResourceStructureId());
            entity.setParentResourceStructureId(resourceStructure.getResourceStructureId());
            entity.setParentResourceStructureTypeId(resourceStructure.getStructureTypeId());
            return entity;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ComputerRack> batchUpdateCustomer(ComputerRackCustomerRequestDTO computerRackCustomerRequestDTO) {
        String customer = computerRackCustomerRequestDTO.customer;
        String ids = computerRackCustomerRequestDTO.ids;
        List<Integer> computerrackIdList = StringUtils.splitToIntegerList(ids);
        computerRackRepository.batchUpdateCustomerByIds(computerrackIdList, customer);
        return computerRackRepository.findByComputerRackIdIn(computerrackIdList);
    }

    @Override
    public List<String> findAllCustomersByUserId(Integer userId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }

        return computerRackRepository.findAllCustomersByResourceStructureIds(resourceStructureIds);
    }

    @Override
    public List<String> findAllBusinessByUserId(Integer userId) {
        //获取拥有权限的层级id
        List<Integer> resourceStructureIds = resourceStructureService.findResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIds)) {
            return Collections.emptyList();
        }

        return computerRackRepository.findAllBusinessByResourceStructureIds(resourceStructureIds);
    }

    @Override
    public List<ComputerRackDTO> findComputerRackDTO() {
        return computerRackRepository.findComputerRackDTO();
    }

    @Override
    public List<ComputerRack> batchUpdatePosition(ComputerRackPositionRequestDTO computerRackPositionRequestDTO) {
        List<Integer> computerrackIdList = StringUtils.splitToIntegerList(computerRackPositionRequestDTO.getIds());
        String resourceStructureFullPath = resourceStructureManager.getFullPath(computerRackPositionRequestDTO.getResourcestructureId());
        computerRackRepository.batchUpdatePositionByIds(computerrackIdList, computerRackPositionRequestDTO.getResourcestructureId(), resourceStructureFullPath);
        return computerRackRepository.findByComputerRackIdIn(computerrackIdList);
    }

    @Override
    public void batchUpdateCapacity(ComputerRackCapacityRequestDTO computerRackCapacityRequestDTO) {
        List<Integer> computerRackIdList = StringUtils.splitToIntegerList(computerRackCapacityRequestDTO.getIds());
        List<ResourceObject> resourceObjects = computerRackIdList.stream().map(computerRackId -> {
            ResourceObject resourceObject = new ResourceObject();
            resourceObject.setObjectId(computerRackId);
            resourceObject.setObjectTypeId(SourceType.COMPUTERRACK.value());
            return resourceObject;
        }).toList();
        List<CapacityAttribute> capacityAttributes = capacityAttributeService.findAttributes(resourceObjects);
        List<CapacityAttribute> capacityAttributeUpdates = new ArrayList<>();
        for (CapacityAttribute capacityAttribute : capacityAttributes) {
            CapacityBaseAttributeEnum capacityBaseAttributeEnum = CapacityBaseAttributeEnum.getByValue(capacityAttribute.getAttributeName());
            if (Objects.isNull(capacityBaseAttributeEnum)) {
                continue;
            }
            switch (capacityBaseAttributeEnum) {
                case RACK_SPACE -> {
                    if (Objects.isNull(computerRackCapacityRequestDTO.getRackSpace())) {
                        continue;
                    }
                    buildCapacity(capacityAttribute, computerRackCapacityRequestDTO.getRackSpace());
                }
                case RACK_WEIGHT -> {
                    if (Objects.isNull(computerRackCapacityRequestDTO.getRackWeight())) {
                        continue;
                    }
                    buildCapacity(capacityAttribute, computerRackCapacityRequestDTO.getRackWeight());
                }
                case RACK_COOLING -> {
                    if (Objects.isNull(computerRackCapacityRequestDTO.getRackCooling())) {
                        continue;
                    }
                    buildCapacity(capacityAttribute, computerRackCapacityRequestDTO.getRackCooling());
                }
                case COM_POWER -> {
                    if (Objects.isNull(computerRackCapacityRequestDTO.getComPower())) {
                        continue;
                    }
                    buildCapacity(capacityAttribute, computerRackCapacityRequestDTO.getComPower());
                }
                default -> {
                    continue;
                }
            }
            capacityAttributeUpdates.add(capacityAttribute);
        }
        capacityAttributeService.updateAttributes(capacityAttributeUpdates);
    }


    @Override
    public List<ComputerRackCapacityCalculateDTO> findAttributesByResourceStructureIds(String resourceStructureIds) {
        List<ResourceObject> resourceObjectList = getResourceObjectsByResourceStructureIds(resourceStructureIds);
        List<CapacityAttribute> attributeList = capacityAttributeService.findAttributes(resourceObjectList);
        Map<String, ComputerRackCapacityCalculateDTO> collect = Arrays.stream(CapacityBaseAttributeEnum.values())
                                                                      .collect(Collectors.toMap(CapacityBaseAttributeEnum::getValue, e -> new ComputerRackCapacityCalculateDTO(e.getName(), e.getUnit())));
        attributeList.forEach(attr -> collect.get(attr.getAttributeName())
                                             .updateCapacities(getDoubleValue(attr.getRatedCapacity()), getDoubleValue(attr.getFreeCapacity()), getDoubleValue(attr.getUsedCapacity())));
        return collect.values().stream()
                      .map(ComputerRackCapacityCalculateDTO::calculatePercent)
                      .sorted(Comparator.comparing(ComputerRackCapacityCalculateDTO::getUnit))
                      .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            this.deleteById(id);
        }
    }

    private double getDoubleValue(Double value) {
        return Optional.ofNullable(value).orElse(0.0);
    }

    /**
     * 获取层级下所有机架的ResourceObject对象
     * @param resourceStructureIds 层级ids
     * @return {@link List }<{@link ResourceObject }> 资源对象
     */
    private List<ResourceObject> getResourceObjectsByResourceStructureIds(String resourceStructureIds) {
        List<Integer> resourceStructureIdList = StringUtils.splitToIntegerList(resourceStructureIds);
        Set<Integer> allChildrenIds = resourceStructureManager.getAllChildrenId(resourceStructureIdList);
        List<ComputerRack> computerRacksByResourceStructureIds = findComputerRacksByResourceStructureIds(new ArrayList<>(allChildrenIds));
        return computerRacksByResourceStructureIds.stream()
                                                  .map(ComputerRack::getGlobal)
                                                  .toList();
    }

    private void buildCapacity(CapacityAttribute capacityAttribute, ComputerRackCapacityRequestDTO.ComputerRackCapacityInnerDTO computerRackCapacityRequestDTO) {
        Double ratedCapacity = Optional.ofNullable(computerRackCapacityRequestDTO)
                .map(ComputerRackCapacityRequestDTO.ComputerRackCapacityInnerDTO::getRatedCapacity).orElse(null);
        if (Objects.nonNull(ratedCapacity)) {
            capacityAttribute.setRatedCapacity(ratedCapacity);
        }
        String unit = Optional.ofNullable(computerRackCapacityRequestDTO)
                .map(ComputerRackCapacityRequestDTO.ComputerRackCapacityInnerDTO::getUnit).orElse(null);
        if (CharSequenceUtil.isNotBlank(unit)) {
            capacityAttribute.setUnit(unit);
        }
    }
}
