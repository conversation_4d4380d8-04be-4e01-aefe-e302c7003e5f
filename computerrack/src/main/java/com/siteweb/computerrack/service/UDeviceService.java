package com.siteweb.computerrack.service;

import com.siteweb.computerrack.vo.UDeviceSelect;
import com.siteweb.computerrack.entity.UDevice;

import java.util.List;

public interface UDeviceService {
    /**
     * 找到所有u位管理设备
     *
     * @return {@link List}<{@link UDevice}>
     */
    List<UDevice> findAll();

    /**
     * 查询u位设备根据机架id
     * @param rackId 机架id
     * @return {@link UDevice}
     */
    UDevice findByRackId(Integer rackId);

    /**
     * 获取u位设备的唯一编码根据机架id
     * @param rackId 架id
     * @return {@link String}
     */
    String findUDeviceNumberByRackId(Integer rackId);
    /**
     * 获取u位管理设备的下拉选项(用于绑定机架)
     * @return {@link List}<{@link UDeviceSelect}>
     */
    List<UDeviceSelect> findSelect();

    /**
     * 更新u位管理设备的绑定机架
     *
     * @param uDeviceNumber u设备唯一编号
     * @param rackId    机架id
     */
    void updateRackNumber(String uDeviceNumber,Integer rackId);

    /**
     * 解除u位管理器机架的绑定
     * @param rackId
     */
    Boolean setUDeviceRackIdIsNull(Integer rackId);

    boolean uDeviceBindRack(String uDeviceNumber, Integer rackId);
}
