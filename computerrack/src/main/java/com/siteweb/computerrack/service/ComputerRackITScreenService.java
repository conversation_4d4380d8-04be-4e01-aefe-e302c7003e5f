package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.ComputerRackITScreenParam;
import com.siteweb.utility.vo.NameValueVO;
import com.siteweb.utility.vo.RacksPowerLoadRateVO;

import java.util.List;

public interface ComputerRackITScreenService {

    RacksPowerLoadRateVO getComputerRacksPowerLoadRate(Integer rackId);

    List<NameValueVO> getComputerRackPowerTrend(ComputerRackITScreenParam param);

    List<NameValueVO> getComputerRackPower(ComputerRackITScreenParam param);

    List<NameValueVO> getComputerRackPowerLoadRate(ComputerRackITScreenParam param);
}