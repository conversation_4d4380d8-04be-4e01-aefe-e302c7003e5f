package com.siteweb.computerrack.service.impl;

import com.siteweb.computerrack.dto.ITDeviceModelCategoryDTO;
import com.siteweb.computerrack.service.ITDeviceModelCategoryService;
import com.siteweb.computerrack.service.ITDeviceModelService;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ITDeviceModelCategoryServiceImpl implements ITDeviceModelCategoryService {
    private static final Integer ENTRY_ID = 3003;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    ITDeviceModelService itDeviceModelService;

    @Override
    public List<ITDeviceModelCategoryDTO> findAll() {
        List<DataItem> dataItemList = dataItemService.findByEntryId(ENTRY_ID);
        return dataItemList.stream().map(ITDeviceModelCategoryDTO::new).toList();
    }

    @Override
    public boolean existsName(String name) {
        return dataItemService.exists(ENTRY_ID, name);
    }

    @Override
    public int create(ITDeviceModelCategoryDTO itDeviceModelCategoryDTO) {
        itDeviceModelCategoryDTO.setId(null);
        return dataItemService.createDataItem(ENTRY_ID,itDeviceModelCategoryDTO.getName());
    }

    @Override
    public int deleteById(Integer id) {
        return dataItemService.deleteByEntryIdAndItemId(ENTRY_ID, id);
    }

    @Override
    public int updateById(ITDeviceModelCategoryDTO itDeviceModelCategoryDTO) {
        return dataItemService.updateDataItem(ENTRY_ID,itDeviceModelCategoryDTO.getId(),itDeviceModelCategoryDTO.getName());
    }

    @Override
    public boolean existsDependency(Integer id) {
        return itDeviceModelService.countByCategoryId(id) > 0;
    }
}
