package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.ComputerRackEquipmentParamDTO;
import com.siteweb.computerrack.dto.ComputerRackEquipmentRequestDTO;
import com.siteweb.computerrack.vo.ComputerRackEquipmentVO;

import java.util.List;

public interface ComputerRackEquipmentService {
    List<ComputerRackEquipmentVO> findComputerRackEquipmentVOsByUserId(Integer userId);

    List<ComputerRackEquipmentParamDTO> createComputerRacksEquipments(ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO);

    List<ComputerRackEquipmentParamDTO> updateComputerRacksEquipments(ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO);

    void deleteByIds(String ids);

    List<ComputerRackEquipmentVO> findComputerRackEquipmentVOsById(Integer id);
}