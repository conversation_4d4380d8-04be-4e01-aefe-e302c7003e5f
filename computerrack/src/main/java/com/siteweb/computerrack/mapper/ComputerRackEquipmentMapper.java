package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.dto.ComputerRackEquipmentDTO;
import com.siteweb.computerrack.dto.ComputerRackEquipmentParamDTO;
import com.siteweb.computerrack.entity.ComputerRackEquipmentMap;

import java.util.List;

public interface ComputerRackEquipmentMapper extends BaseMapper<ComputerRackEquipmentMap> {
    List<ComputerRackEquipmentDTO> findComputerRackEquipmentDTOsByResourceStructureIds(List<Integer> resourceStructureIds);

    void batchInsert(List<ComputerRackEquipmentParamDTO> computerRackEquipmentParamDTOList);

    List<ComputerRackEquipmentDTO> findComputerRackEquipmentDTOsById(Integer id);
}