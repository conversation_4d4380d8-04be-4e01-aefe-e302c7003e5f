package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.entity.ITDeviceModel;

import java.util.List;

public interface ITDeviceModelMapper extends BaseMapper<ITDeviceModel> {

    ITDeviceModel findByiITDeviceModelId(Integer iTDeviceModelId);

    ITDeviceModel findByITDeviceModelName(String itDeviceModelName);

    Integer findReferencesCount(Integer iTDeviceModelId);

    List<ITDeviceModel> findAll();
}
