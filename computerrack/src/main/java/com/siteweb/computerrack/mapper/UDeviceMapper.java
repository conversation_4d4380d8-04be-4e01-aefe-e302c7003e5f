package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.vo.UDeviceSelect;
import com.siteweb.computerrack.entity.UDevice;

import java.util.List;

public interface UDeviceMapper extends BaseMapper<UDevice> {
    /**
     * 获取u位管理设备的下拉选项(用于绑定机架)
     * @return {@link List}<{@link UDeviceSelect}>
     */
    List<UDeviceSelect> findSelect();

    List<UDevice> findAll();
}
