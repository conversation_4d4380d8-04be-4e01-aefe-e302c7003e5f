package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.dto.RackRecordParamDto;
import com.siteweb.computerrack.entity.RackMountRecord;
import com.siteweb.computerrack.vo.RackChangeRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface RackMountRecordMapper extends BaseMapper<RackMountRecord> {

    List<RackMountRecord> findAll();

    List<RackMountRecord> findUnexpired();

    void batchUpdate(@Param("records") List<RackMountRecord> attributeList);

    List<RackChangeRecordVo> findTopN(Integer top, List<Integer> resourceStructureIds);

    List<RackChangeRecordVo> findRackChangeRecord(@Param("param") RackRecordParamDto param);
}
