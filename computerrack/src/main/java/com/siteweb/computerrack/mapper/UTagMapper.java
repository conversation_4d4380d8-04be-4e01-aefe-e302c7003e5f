package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.dto.UTagDto;
import com.siteweb.computerrack.entity.UTag;
import com.siteweb.utility.vo.NameValueVO;
import com.siteweb.computerrack.vo.UTagVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UTagMapper extends BaseMapper<UTag> {
    /**
     * 找到所有标签列表
     *
     * @return {@link List}<{@link UTag}>
     */
    List<UTagVo> findAll();

    /**
     * 根据标签id查询单个标签
     * @param id 标签id
     * @return {@link UTagDto}
     */
    UTagDto findTagById(@Param("id") Integer id);

    NameValueVO findTagRateByResourceStructureIds(List<Integer> resourceStructureIds);
}
