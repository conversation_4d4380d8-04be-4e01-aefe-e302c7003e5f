package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.computerrack.dto.SimpleITDeviceDTO;
import com.siteweb.computerrack.dto.UTagDeviceSelectDto;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.monitoring.dto.ResourceObjectEntity;
import com.siteweb.utility.vo.NameValueVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ITDeviceMapper extends BaseMapper<ITDevice> {




    List<SimpleITDeviceDTO> getOnShelfITDevices(List<Integer> computerRackIds);

    List<ITDevice> findITDevices();


    ITDevice findByITDeviceName(String objectName);

    List<ITDevice> findByComputerRackId(Integer computerRackId);


    ITDevice findByComputerRackPos(@Param("rackId") Integer rackId, @Param("itDeviceId") Integer itDeviceId, @Param("uIndex") Integer uIndex);

    List<ITDevice> findITDevicesByModelId(Integer iTDeviceModelId);

    /**
     * 获取it设备的ResourceObjectEntity实体
     * @param iTDeviceIds it设备ids
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    List<ResourceObjectEntity> findResourceObjectByITDeviceIds(List<Integer> iTDeviceIds);

    /**
     * 获取it设备的ResourceObjectEntity实体
     * @param iTDeviceId it设备id
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    ResourceObjectEntity findResourceObjectByITDeviceId(Integer iTDeviceId);

    /**
     * 通过层级id获取it设备资源
     * @param resourceStructureIds 层级ids
     * @return {@link List}<{@link ResourceObjectEntity}>
     */
    List<ResourceObjectEntity> findResourceObjectByResourceStructureIds(List<Integer> resourceStructureIds);

    /**
     * 通过层级id获取it设备
     * @param resourceStructureId 层级id
     * @return {@link List}<{@link ITDevice}>
     */
    List<ITDevice> findITDeviceByResourceStructureId(List<Integer> resourceStructureIds);

    List<NameValueVO> findITDeviceModelCategoryPieChartByResourceStructureIds(List<Integer> resourceStructureIds);
    List<NameValueVO> findITDeviceTypePieChartByResourceStructureIds(List<Integer> resourceStructureIds);

    List<UTagDeviceSelectDto> findUTagDeviceSelect();

    ITDevice findById(Integer iTDeviceId);
    List<ITDevice> findITDeviceByIds(List<Integer> iTDeviceIdList);

    Page<ITDevice> findITDevicesByQuery(Page<ITDevice> page, @Param("ew") Wrapper wrapper);

    ITDevice findIdDeviceByuTagValue(String uTagValue);
}
