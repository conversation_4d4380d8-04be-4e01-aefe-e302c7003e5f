package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.vo.ComputerRackSignalVO;
import com.siteweb.computerrack.entity.ComputerRackSignalMap;

import java.util.List;

public interface ComputerRackSignalMapper extends BaseMapper<ComputerRackSignalMap> {

    List<ComputerRackSignalVO> findComputerRackSignalList(List<Integer> resourceStructureIds);
}