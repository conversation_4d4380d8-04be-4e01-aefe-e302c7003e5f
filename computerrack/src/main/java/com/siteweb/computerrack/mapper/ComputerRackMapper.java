package com.siteweb.computerrack.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.computerrack.dto.ComputerRackDTO;
import com.siteweb.computerrack.dto.SimpleComputerRackDTO;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.vo.RackUIndexRateVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface ComputerRackMapper extends BaseMapper<ComputerRack> {


    ComputerRack findByComputerRackNumber(String computerRackNumber);
    ComputerRack findByComputerRackId(Integer computerRackId);
    ComputerRack findByComputerRackName(String computerRackName);

    List<SimpleComputerRackDTO> findSimpleComputerRacks(List<Integer> computerRackIds);


    List<ComputerRack> findByResourceStructureId(Integer resourceStructureId);

    //    @Query(nativeQuery=true, value = "select DISTINCT business from computerrack;")
    List<String> getBusinessList();

    //    @Query(nativeQuery=true, value = "select DISTINCT customer from computerrack;")
    List<String> getCustomerList();

    List<ComputerRack> findByCustomer(String customer);

    // @Cacheable(value = "ComputerRacksCache", key = "#username")
    List<ComputerRack> findByResourceStructureIdIn(List<Integer> resourceStructureIdList);

    List<ComputerRack> findByComputerRackIdIn(List<Integer> computerrackIdList);

//    @Query(nativeQuery = true, value = "select * from ComputerRack where ComputerRackId in (select distinct ComputerRackId from ColumnCabinetRefComputerRack where columnCabinetId=?1)")
//    List<ComputerRack> findComputerRacksByColumnCabinetId(Integer columnCabinetId);


    void batchUpdate(@Param("records") List<ComputerRack> attributeList);

    /**
     * 查找机架u位使用率
     * @param resourceStructureIds 层级ids
     * @param topN 前几条
     * @return {@link List}<{@link RackUIndexRateVo}>
     */
    List<RackUIndexRateVo> findRackUIndexAttribute(List<Integer> resourceStructureIds, Integer topN);

    void batchUpdateCustomerByIds(List<Integer> computerrackIdList, String customer);

    List<String> findAllCustomersByResourceStructureIds(List<Integer> resourceStructureIds);

    List<String> findAllBusinessByResourceStructureIds(List<Integer> resourceStructureIds);

    List<ComputerRackDTO> findComputerRackDTO();

    void batchUpdatePositionByIds(List<Integer> computerrackIdList, Integer resourceStructureId, String fullPath);
}
