---
trigger: always_on
---

# SiteWeb6 Server 开发指南

## 构建与测试命令
- **完整构建**: `mvn clean install`
- **单模块构建**: `mvn clean install -pl <module-name> -am`
- **运行测试**: `mvn test`
- **单个测试**: `mvn test -Dtest=ClassName#methodName`
- **跳过测试构建**: `mvn clean install -DskipTests`
- **启动应用**: `mvn spring-boot:run -pl core`
- **Docker构建**: `mvn clean package docker:build`

## 架构与模块结构
**核心模块**: `core` - Spring Boot主应用入口，集成所有业务模块
**公共模块**: `common` - 通用工具类、注解、响应封装等
**基础服务**: `admin`(权限管理)、`utility`(基础工具)、`monitoring`(监控核心)
**业务模块**: `energy`(能耗)、`powerdistribution`(配电)、`battery`(电池)、`airconditioncontrol`(空调控制)等
**数据库**: MySQL(主)、PostgreSQL、DaMeng、InfluxDB(时序)、MongoDB、Redis(缓存)
**技术栈**: Spring Boot 2.7.10 + Java 17 + MyBatis Plus + Spring Security

## 代码风格规范
- **包命名**: `com.siteweb.<module>.<layer>` (controller/service/mapper/entity)
- **注解使用**: `@RequiredArgsConstructor`(替代@Autowired)、`@Slf4j`(日志)、Lombok注解
- **Service层**: 接口+实现类模式，`@Service("beanName")`命名
- **Controller层**: `@RestController` + `@RequestMapping("/api")`，使用ResponseHelper封装响应
- **Entity层**: `@Data` + `@TableName`，MyBatis Plus注解
- **异常处理**: 统一ResponseResult封装，详细日志记录
- **命名约定**: 驼峰命名，方法名体现业务含义(find/create/update/delete前缀)
- **文档**: Swagger注解(@ApiOperation)，完整JavaDoc注释 

## 双机高可用使用规范
- **双机高可用判断**: 使用 `haStatusService.isMasterHost()` 判断当前服务是否为主机
- **定时任务控制**: 所有定时任务必须在执行前检查 `haStatusService.isMasterHost()`，确保任务只在主机上执行
- **初始化控制**: 在 `@PostConstruct` 标记的初始化方法中检查主机状态，避免双机重复初始化
- **注解简化**: 对于简单方法，使用 `@MasterHostOnly` 注解代替直接调用 `haStatusService.isMasterHost()`
- **状态变更处理**: 实现 `ApplicationListener<BaseSpringEvent<HAStatusChanged>>` 接口，在双机切换时重新初始化组件
- **高可用监听**: 在 `onApplicationEvent` 方法中首先检查 `haStatusService.isEnabled()`，如未启用则直接返回
- **默认单机处理**: 当未启用高可用时，`isMasterHost()` 默认返回 true，确保单机环境正常工作