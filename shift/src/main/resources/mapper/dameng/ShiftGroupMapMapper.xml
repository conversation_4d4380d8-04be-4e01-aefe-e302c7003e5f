<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.shift.mapper.ShiftGroupMapMapper">
    <insert id="batchInsert" keyProperty="shiftGroupMapId" useGeneratedKeys="true">
        INSERT INTO shiftgroupmap(shiftgroupid, employeeid, displayIndex) VALUES
        <foreach collection="shiftGroupMapList" item="item" separator=",">
            (#{item.shiftGroupId},#{item.employeeId},#{item.displayIndex})
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="shiftGroupMapList" item="item" separator=";">
            UPDATE ShiftGroupMap SET ShiftGroupId = #{item.shiftGroupId},employeeId = #{item.employeeId},displayIndex = #{item.displayIndex}
            WHERE shiftGroupMapId = #{item.shiftGroupMapId}
        </foreach>
    </update>
    <select id="findByCondition" resultType="com.siteweb.shift.dto.ShiftGroupMapDTO">
        SELECT map.ShiftGroupMapId, map.DisplayIndex,shiftGroup.ShiftGroupId,shiftGroup.ShiftGroupName,
        department.DepartmentName,te.EmployeeId,te.EmployeeName, te.Phone,te.Mobile, te.EmployeeTitle
        FROM shiftgroupmap map
        INNER JOIN tbl_employee te ON map.EmployeeId = te.EmployeeId
        INNER JOIN shiftgroup shiftGroup ON map.ShiftGroupId = shiftGroup.ShiftGroupId
        INNER join tbl_department department on te.DepartmentId = department.DepartmentId
        <where>
            <if test="shiftGroupId != null">
                AND map.ShiftGroupId = #{shiftGroupId}
            </if>
            <if test="departmentName != null and departmentName != ''">
                AND department.DepartmentName like concat('%',#{departmentName},'%')
            </if>
            <if test="employeeName != null and employeeName != ''">
                AND te.EmployeeName like concat('%',#{employeeName},'%')
            </if>
        </where>
        order by map.DisplayIndex asc
    </select>
</mapper>