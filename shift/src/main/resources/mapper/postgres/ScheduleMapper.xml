<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.shift.mapper.ScheduleMapper">
    <select id="findScheduleCalendar" resultType="com.siteweb.shift.vo.ScheduleCalendarVO">
        SELECT a.ScheduleId,a.ScheduleTime,a.shiftId,b.ShiftGroupMapId,d.ShiftId,d.ShiftColor,d.ShiftName
        FROM schedule a
        INNER JOIN shiftgroupmap b ON a.ShiftGroupMapId = b.ShiftGroupMapId
        INNER JOIN shiftgroup c ON b.ShiftGroupId = c.ShiftGroupId
        INNER join shift d ON d.ShiftId = a.shiftId
        WHERE
        a.scheduleTime &gt;=#{startTime} AND a.scheduleTime &lt;= #{endTime}
        AND a.ShiftGroupMapId IN
        <foreach collection="shiftGroupMapIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="shiftGroupId != null">
            AND c.shiftGroupId = #{shiftGroupId}
        </if>
        <if test="shiftId != null">
            AND d.shiftId = #{shiftId}
        </if>
        order by a.ShiftGroupMapId
    </select>
    <select id="findDutyPersonByShiftGroupIdList" resultType="java.lang.Integer">
        SELECT map.EmployeeId
        FROM shift shift
        INNER JOIN schedule schedule ON shift.ShiftId = schedule.shiftId
        INNER JOIN shiftgroupmap map ON map.shiftgroupmapId = schedule.ShiftGroupMapId
        WHERE schedule.ScheduleTime = CURRENT_DATE
        AND CURRENT_TIME &gt;= shift.ShiftStartTime
        AND CURRENT_TIME &lt;= shift.ShiftEndTime
        AND map.ShiftGroupId IN
        <foreach collection="shiftGroupList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="daySpanShiftIdList != null and daySpanShiftIdList.size > 0">
            UNION
            SELECT map.EmployeeId
            FROM shift shift
            INNER JOIN schedule schedule ON
            shift.ShiftId = schedule.shiftId
            INNER JOIN shiftgroupmap map ON
            map.shiftgroupmapId = schedule.ShiftGroupMapId
            WHERE ((schedule.ScheduleTime + INTERVAL '1 day' = CURRENT_DATE AND CURRENT_TIME &lt;= shift.ShiftEndTime)
            OR (schedule.ScheduleTime = CURRENT_DATE AND CURRENT_TIME &gt;= shift.ShiftStartTime))
            AND map.ShiftGroupId IN
            <foreach collection="shiftGroupList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND shift.shiftId IN
            <foreach collection="daySpanShiftIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findByShiftGroupMapIdAndTime" resultType="com.siteweb.shift.entity.Schedule">
        SELECT scheduleid      AS scheduleId,
               shiftgroupmapid AS shiftGroupMapId,
               shiftid         AS shiftId,
               scheduletime    AS scheduleTime
        FROM schedule
        WHERE ShiftGroupMapId = #{shiftGroupMapId}
          AND scheduleTime = TO_TIMESTAMP(#{scheduleTime}, 'YYYY-MM-DD HH24:MI:SS');
    </select>
</mapper>