package com.siteweb.complexindex.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 指标以及值
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComplexIndexValueDTO implements Serializable {

    /**
     * 指标id
     */
    private Integer complexIndexId;
    /**
     * 指标名称
     */
    private String complexIndexName;
    /**
     * 当前值
     */
    private Double currentValue;


}