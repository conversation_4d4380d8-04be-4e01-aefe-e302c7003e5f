package com.siteweb.complexindex.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ComplexIndexDTO {

    /**
     * 指标id
     */
    private Integer complexIndexId;

    /**
     * 指标名称
     */
    private String complexIndexName;

    /**
     * 计算周期
     */
    private String calcCron;

    /**
     * 是否差值
     */
    private Integer calcType;

    /**
     * 依赖计算指标
     */
    private String afterCalc;

    /**
     * 存储周期
     */
    private String saveCron;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 单位
     */
    private String unit;

    /**
     * 精度
     */
    private String accuracy;

    /**
     * 资源类别
     */
    private Integer objectTypeId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 指标定义id
     */
    private Integer complexIndexDefinitionId;

    /**
     * 业务分类id
     */
    private Integer businessTypeId;

    /**
     * 场景id
     */
    private Integer sceneId;

    /**
     * 批量标识（当前-0、同级添加-1、当前和下级-2）
     */
    private Integer batchType;

    /**
     * 层级资源id
     */
    private Integer resourceStructureId;

    private String complexIndexDefinitionName;

    /**
     * 异常表达式
     */
    private String checkExpression;

    /**
     * 需要批量应用的属性
     */
    private String fields;

    /**
     * 统一资源ids
     */
    private List<Integer> objectIds;

    /**
     * 统一资源ids
     */
    private Integer objectId;
}
