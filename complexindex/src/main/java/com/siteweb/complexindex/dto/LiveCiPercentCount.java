package com.siteweb.complexindex.dto;

import lombok.Data;

@Data
public class LiveCiPercentCount {
    /**
     * 最小限度
     */
    private Double minimum;

    /**
     * 最大限度
     */
    private Double maximum;


    /**
     * 计数
     */
    private Integer count;

    public Integer getCount() {
        Integer count = this.count;
        if (count == null) {
            return 0;
        }
        return count;
    }
}
