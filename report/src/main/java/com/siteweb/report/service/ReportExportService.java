package com.siteweb.report.service;

import com.siteweb.report.dto.HistorySignalExportDTO;
import com.siteweb.report.parser.model.HistorySignalReportParam;
import org.springframework.http.HttpStatus;

import java.util.List;

public interface ReportExportService {
    /**
     * 查询历史信号
     *
     * @param userId                   用户id
     * @param param 历史信号参数
     * @return {@link HttpStatus}
     */
    List<HistorySignalExportDTO> findHistorySignal(Integer userId, HistorySignalReportParam param);

    /**
     * 历史信号报表查询数量预估
     * 使用需要查询数据的进一个小时的数量估算，用仅一个小时的数量*24*所需天数 = 估算结果
     * @param userId 用户id
     * @param param 历史信号报表查询参数
     * @return long
     */
    long historySignalReportCountSample(Integer userId, HistorySignalReportParam param);
}
