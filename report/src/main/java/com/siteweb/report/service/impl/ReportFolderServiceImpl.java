package com.siteweb.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.report.dto.FolderWithReportsDTO;
import com.siteweb.report.dto.ReportFolderTreeDTO;
import com.siteweb.report.entity.ReportFolder;
import com.siteweb.report.entity.ReportFolderMap;
import com.siteweb.report.mapper.ReportFolderMapMapper;
import com.siteweb.report.mapper.ReportFolderMapper;
import com.siteweb.report.service.ReportFolderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@RequiredArgsConstructor
public class ReportFolderServiceImpl implements ReportFolderService {

    private final ReportFolderMapMapper reportFolderMapMapper;
    private final ReportFolderMapper reportFolderMapper;

    @Override
    public List<ReportFolderTreeDTO> findReportFolderTree() {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<ReportFolder> folders = reportFolderMapper.selectList(Wrappers.lambdaQuery(ReportFolder.class)
                .orderByAsc(ReportFolder::getSortIndex)
        );
        // 转换 folder 为 tree 节点
        Map<Integer, ReportFolderTreeDTO> folderMap = new HashMap<>();
        for (ReportFolder folder : folders) {
            ReportFolderTreeDTO node = new ReportFolderTreeDTO();
            node.setId(folder.getFolderId());
            node.setName(folder.getFolderName());
            node.setSortIndex(folder.getSortIndex());
            node.setType("folder");
            folderMap.put(folder.getFolderId(), node);
        }
        // 挂载 folder 的父子关系
        List<ReportFolderTreeDTO> roots = new ArrayList<>();
        for (ReportFolder folder : folders) {
            ReportFolderTreeDTO node = folderMap.get(folder.getFolderId());
            if (Objects.equals(folder.getParentId(), 0)) {
                // 根节点
                roots.add(node);
            } else {
                folderMap.get(folder.getParentId()).getChildren().add(node);
            }
        }

        // 获取报表挂载关系
        List<Integer> folderIds = folders.stream().map(ReportFolder::getFolderId).toList();
        if (!folderIds.isEmpty()) {
            List<FolderWithReportsDTO> mappings = reportFolderMapMapper.selectReportMappings(folderIds);
            List<FolderWithReportsDTO> mappingTimeReports = reportFolderMapMapper.selectTimeReportMappings(folderIds, userId);
            for (FolderWithReportsDTO mapping : mappings) {
                Integer folderId = mapping.getFolderId();
                folderMap.get(folderId).getChildren().addAll(mapping.getReports());
            }
            // 添加定时报表到对应文件夹
            for (FolderWithReportsDTO mapping : mappingTimeReports) {
                Integer folderId = mapping.getFolderId();
                folderMap.get(folderId).getChildren().addAll(mapping.getReports());
            }
        }
        // 查询未挂载报表
        List<ReportFolderTreeDTO> unassigned = reportFolderMapMapper.selectUnassignedReports();
        List<ReportFolderTreeDTO> unassignedTimeReports = reportFolderMapMapper.selectUnassignedTimeReports(userId);
        if (CollUtil.isNotEmpty(unassigned) || CollUtil.isNotEmpty(unassignedTimeReports)) {
            ReportFolderTreeDTO uncategorized = new ReportFolderTreeDTO();
            uncategorized.setId(0);
            uncategorized.setName("未分类");
            uncategorized.setType("folder");
            // 合并两个列表
            if (CollUtil.isNotEmpty(unassigned)) {
                uncategorized.getChildren().addAll(unassigned);
            }
            if (CollUtil.isNotEmpty(unassignedTimeReports)) {
                uncategorized.getChildren().addAll(unassignedTimeReports);
            }
            roots.add(0, uncategorized);
        }
        return roots;
    }

    @Override
    public void saveReportFolder(ReportFolder reportFolder) {
        reportFolder.setFolderId(null);
        reportFolder.setCreateTime(new Date());
        reportFolderMapper.insert(reportFolder);
    }

    @Override
    public void updateReportFolder(ReportFolder reportFolder) {
        reportFolderMapper.updateById(reportFolder);
    }

    @Override
    public ReportFolder findReportFolder(Integer folderId) {
        return reportFolderMapper.selectById(folderId);
    }

    @Override
    public void deleteReportFolder(Integer folderId) {
        Long count = reportFolderMapMapper.selectCount(Wrappers.lambdaQuery(ReportFolderMap.class).eq(ReportFolderMap::getFolderId, folderId));
        if (count > 0) {
            throw new BusinessException("文件夹下还有报表，不允许删除");
        }
        reportFolderMapper.deleteById(folderId);
    }

    @Override
    public void saveOrUpdateReportFolderMap(ReportFolderMap reportFolderMap) {
        if (reportFolderMap.getReportType() == null) {
            throw new BusinessException("报表类型不能为空");
        }
        // 一个报表只能在挂一个文件夹下
        if (reportFolderMap.getFolderId() == null || reportFolderMap.getFolderId() == 0) {
            deleteReportFolderMapByReportId(reportFolderMap.getReportId(), reportFolderMap.getReportType());
        } else {
            ReportFolder reportFolder = reportFolderMapper.selectById(reportFolderMap.getFolderId());
            if (reportFolder == null) {
                throw new BusinessException("所选文件夹不存在");
            }
            deleteReportFolderMapByReportId(reportFolderMap.getReportId(), reportFolderMap.getReportType());
            reportFolderMapMapper.insert(reportFolderMap);
        }

    }

    @Override
    public void deleteReportFolderMapByReportId(Integer reportId, Integer reportType) {
        reportFolderMapMapper.delete(Wrappers.lambdaQuery(ReportFolderMap.class).eq(ReportFolderMap::getReportId, reportId)
                .eq(ReportFolderMap::getReportType, reportType));
    }
}
