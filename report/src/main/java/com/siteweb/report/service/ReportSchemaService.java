package com.siteweb.report.service;

import com.siteweb.report.entity.ReportSchema;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface ReportSchemaService {

    /**
     * 根据报表的类别
     *
     * @param userId
     * @param reportSchemaCategoryId
     * @return
     */
    List<ReportSchema> findReportSchemasByUserIdAndCategoryId(Integer userId, Integer reportSchemaCategoryId);

    ReportSchema findReportSchemaByUserIdAndReportSchemaId(Integer userId, Integer reportSchemaId);
}
