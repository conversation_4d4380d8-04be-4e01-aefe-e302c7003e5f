package com.siteweb.report.service.impl;

import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.entity.ReportTimingTaskManagement;
import com.siteweb.report.job.ReportTimeJobManager;
import com.siteweb.report.mapper.ReportTimingTaskManagementMapper;
import com.siteweb.report.service.ReportExportParameterPresetService;
import com.siteweb.report.service.ReportFolderService;
import com.siteweb.report.service.ReportTimingTaskManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * @Author: lzy
 * @Date: 2022/6/13 10:00
 */
@Service
public class ReportTimingTaskManagementImpl implements ReportTimingTaskManagementService {

    private final Logger log = LoggerFactory.getLogger(ReportTimingTaskManagementImpl.class);

    @Autowired
    ReportTimingTaskManagementMapper mapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    ReportTimeJobManager reportTimeJobManager;
    @Autowired
    ReportExportParameterPresetService reportExportParameterPresetService;
    @Autowired
    ReportFolderService reportFolderService;

    @Override
    public ReportTimingTaskManagement findOne(Integer id) {
        ReportTimingTaskManagement reportTimingTaskManagement = mapper.findReportTimingTaskManagementById(id);
        List<ReportExportParameterPreset> reportExportParameterPresetList = reportExportParameterPresetService.findReportExportParameterPresetByReportId(reportTimingTaskManagement.getReportId());
        reportTimingTaskManagement.setReportExportParameterPresetList(reportExportParameterPresetList);
        return reportTimingTaskManagement;
    }

    @Override
    public ReportTimingTaskManagement create(ReportTimingTaskManagement entity) {
        int insert = mapper.create(entity);
        if (insert > 0 && Boolean.TRUE.equals(entity.getStatus())) {
            try {
                reportTimeJobManager.addJob(entity);
            } catch (Exception e) {
                log.error("{}：", messageSourceUtil.getMessage("common.timeJobMsg.addJobException"), e);
            }
        }
        return insert > 0 ? entity : null;
    }

    @Override
    public ReportTimingTaskManagement update(ReportTimingTaskManagement entity) {
        validId(entity.getReportTimingTaskManagementId());
        int update = mapper.update(entity);
        if (update > 0) {
            ReportTimingTaskManagement oldReportTimingTaskManagement = findOne(entity.getReportTimingTaskManagementId());
            if (oldReportTimingTaskManagement != null) {
                Boolean nowStatus = entity.getStatus();
                Boolean oldStatus = oldReportTimingTaskManagement.getStatus();
                try {
                    if (Boolean.FALSE.equals(oldStatus) && Boolean.TRUE.equals(nowStatus)) {
                        reportTimeJobManager.addJob(entity);
                    } else if (Boolean.FALSE.equals(nowStatus)) {
                        reportTimeJobManager.deleteJob(entity);
                    } else {
                        reportTimeJobManager.deleteJob(entity);
                        reportTimeJobManager.addJob(entity);
                        // reportTimeJobManager.updateJob(entity);
                    }
                } catch (Exception e) {
                    log.error("{}: ", messageSourceUtil.getMessage("common.timeJobMsg.updateJobException"), e);
                }
            }
        }
        return update > 0 ? entity : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Integer id) {
        validId(id);
        ReportTimingTaskManagement reportTimingTaskManagement = findOne(id);
        //定时任务报表不存在或者是开启状态的不允许删除
        if (Objects.isNull(reportTimingTaskManagement) || Boolean.TRUE.equals(reportTimingTaskManagement.getStatus())) {
            log.error("id:{},定时任务报表删除失败，定时任务报表不存在或者是开启状态", id);
            return false;
        }
        try {
            reportTimeJobManager.deleteJob(reportTimingTaskManagement);
        } catch (Exception e) {
            log.error("删除报表定时任务Id:{},{}：", messageSourceUtil.getMessage("common.timeJobMsg.removeJobException"), id, e);
            return Boolean.FALSE;
        }
        reportFolderService.deleteReportFolderMapByReportId(id, 2);
        return mapper.deleteById(id) > 0;
    }

    @Override
    public List<ReportTimingTaskManagement> findByUserId(Integer userId) {
        return mapper.findByUserId(userId);
    }

    public void validId(Integer id) {
        if (id == null) {
            throw new BusinessException(messageSourceUtil.getMessage("common.msg.idIsNotEmpty"));
        }
        ReportTimingTaskManagement reportTimingTaskManagement = findOne(id);
        if (reportTimingTaskManagement == null) {
            throw new BusinessException(messageSourceUtil.getMessage("common.msg.disallowOperationEmptyRecord"));
        }
    }
}
