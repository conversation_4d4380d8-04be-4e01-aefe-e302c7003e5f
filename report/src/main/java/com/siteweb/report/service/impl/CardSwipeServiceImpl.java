package com.siteweb.report.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.siteweb.report.entity.CardSwipe;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.mapper.CardSwipeMapper;
import com.siteweb.report.parser.model.CardSwipeParam;
import com.siteweb.report.service.CardSwipeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CardSwipeServiceImpl implements CardSwipeService {

    @Autowired
    CardSwipeMapper cardSwipeMapper;

    public List<CardSwipe> getPageCardSwipe(CardSwipeParam cardSwipeParam, Pageable pageable, JSONObject jsonObject) {
        if (ObjectUtil.isNotNull(pageable)) {
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<CardSwipe> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<CardSwipe> cardSwipePage = cardSwipeMapper.getPageCardSwipe(page, cardSwipeParam);
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), cardSwipePage.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), cardSwipePage.getTotal());
            return cardSwipePage.getRecords();
        }
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CardSwipe> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, -1);
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CardSwipe> cardSwipePage = cardSwipeMapper.getPageCardSwipe(page, cardSwipeParam);
        return cardSwipePage.getRecords();
    }
}
