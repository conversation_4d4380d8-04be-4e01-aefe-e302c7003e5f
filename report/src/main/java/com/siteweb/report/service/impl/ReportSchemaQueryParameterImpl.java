package com.siteweb.report.service.impl;

import com.siteweb.report.entity.ReportSchemaQueryParameter;
import com.siteweb.report.mapper.ReportSchemaQueryParameterMapper;
import com.siteweb.report.service.ReportSchemaQueryParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Habits
 * @time: 2022/5/9 13:55
 * @description:
 **/
@Service
public class ReportSchemaQueryParameterImpl implements ReportSchemaQueryParameterService {

    @Autowired
    ReportSchemaQueryParameterMapper reportSchemaQueryParameterMapper;


    @Override
    public List<ReportSchemaQueryParameter> findReportSchemaQueryParameterBySchemaId(Integer reportSchemaId) {
        return reportSchemaQueryParameterMapper.findByByReportSchemaId(reportSchemaId);
    }

    @Override
    public ReportSchemaQueryParameter findReportSchemaQueryParameterById(Integer reportSchemaQueryParameterId) {
        return reportSchemaQueryParameterMapper.findById(reportSchemaQueryParameterId);
    }
}
