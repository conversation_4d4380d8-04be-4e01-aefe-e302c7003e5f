package com.siteweb.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.report.entity.ReportSchema;
import com.siteweb.report.mapper.ReportSchemaMapper;
import com.siteweb.report.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Habits
 * @time: 2022/4/29 13:13
 * @description:
 **/
@Service
public class ReportSchemaServiceImpl implements ReportSchemaService {

    @Autowired
    ReportSchemaMapper reportSchemaMapper;

    @Autowired
    ReportDataSourceService reportDataSourceService;

    @Autowired
    ReportService reportService;

    @Autowired
    ReportSchemaQueryParameterService reportSchemaQueryParameterService;

    @Autowired
    ReportSchemaExportParameterSevice reportSchemaExportParameterSevice;

    @Override
    public List<ReportSchema> findReportSchemasByUserIdAndCategoryId(Integer userId, Integer reportSchemaCategoryId) {
        List<ReportSchema> reportSchemas = reportSchemaMapper.selectList(Wrappers.lambdaQuery(ReportSchema.class)
                                                                                 .eq(ReportSchema::getReportSchemaCategoryId, reportSchemaCategoryId)
                                                                                 .eq(ReportSchema::getViewControlId, GlobalConstants.YES));
        reportSchemas.forEach(schema -> this.getReportConfigInfo(userId, schema));
        return reportSchemas;
    }
    @Override
    public ReportSchema findReportSchemaByUserIdAndReportSchemaId(Integer userId, Integer reportSchemaId) {
        LambdaQueryWrapper<ReportSchema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReportSchema::getReportSchemaId, reportSchemaId);
        ReportSchema reportSchema = reportSchemaMapper.selectOne(queryWrapper);
        getReportConfigInfo(userId,reportSchema);
        return reportSchema;
    }
    /**
     * 获取报表场景配置信息
     */
    public void getReportConfigInfo(Integer userId,ReportSchema reportSchema) {
        reportSchema.setReportDataSource(reportDataSourceService.findReportDataSourceById(reportSchema.getReportDataSourceId()));
        reportSchema.setReportList(reportService.findReportByUserIdAndSchemaId(userId, reportSchema.getReportSchemaId()));
        reportSchema.setReportSchemaQueryParameterList(reportSchemaQueryParameterService.findReportSchemaQueryParameterBySchemaId(reportSchema.getReportSchemaId()));
        reportSchema.setReportSchemaExportParameterList(reportSchemaExportParameterSevice.findReportSchemaExportParameterBySchemaId(reportSchema.getReportSchemaId()));
    }
}
