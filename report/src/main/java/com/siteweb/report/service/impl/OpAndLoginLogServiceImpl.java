package com.siteweb.report.service.impl;

import com.siteweb.report.entity.OpAndLoginLog;
import com.siteweb.report.mapper.OpAndLoginLogMapper;
import com.siteweb.report.service.OpAndLoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/8/25 16:33
 */
@Service
public class OpAndLoginLogServiceImpl implements OpAndLoginLogService {

    @Autowired
    OpAndLoginLogMapper opAndLoginLogMapper;

    @Override
    public List<OpAndLoginLog> findOperationRecordByDataAndLogIds(Date startTime, Date endTime, List<Integer> userIds) {
        return opAndLoginLogMapper.findOperationRecordByDataAndLogIds(startTime, endTime, userIds);
    }

    @Override
    public List<OpAndLoginLog> findOperationDetailByDataAndLogIds(Date startTime, Date endTime, List<Integer> userIds) {
        return opAndLoginLogMapper.findOperationDetailByDataAndLogIds(startTime, endTime, userIds);
    }
}
