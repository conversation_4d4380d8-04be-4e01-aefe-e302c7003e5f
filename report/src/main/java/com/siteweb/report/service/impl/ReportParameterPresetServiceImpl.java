package com.siteweb.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.report.entity.ReportParameterPreset;
import com.siteweb.report.mapper.ReportParameterPresetMapper;
import com.siteweb.report.service.ReportParameterPresetSevice;
import com.siteweb.report.service.ReportSchemaQueryParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Habits
 * @time: 2022/5/10 10:19
 * @description:
 **/
@Service
public class ReportParameterPresetServiceImpl implements ReportParameterPresetSevice {

    @Autowired
    ReportParameterPresetMapper reportParameterPresetMapper;

    @Autowired
    ReportSchemaQueryParameterService reportSchemaQueryParameterService;

    @Override
    public int batchCreateReportParameterPreset(List<ReportParameterPreset> list) {
        if (CollectionUtil.isEmpty(list)) {
            return 0;
        }
        return reportParameterPresetMapper.batchCreateReportParameterPreset(list);
    }

    @Override
    public List<ReportParameterPreset> findReportParameterPresetByReportId(Integer reportId) {
        List<ReportParameterPreset> reportParameterPresets = reportParameterPresetMapper.selectList(new QueryWrapper<ReportParameterPreset>()
                .eq("ReportId", reportId));
        reportParameterPresets.forEach(a -> a.setReportSchemaQueryParameter(reportSchemaQueryParameterService.findReportSchemaQueryParameterById(a.getReportSchemaQueryParameterId())));
        return reportParameterPresets.stream().sorted(Comparator.comparingInt(e -> e.getReportSchemaQueryParameter().getSortIndex())).collect(Collectors.toList());
    }

    @Override
    public int deleteReportParameterPresetByReportId(Integer reportId) {
        return reportParameterPresetMapper.delete(new QueryWrapper<ReportParameterPreset>().eq("ReportId", reportId));
    }

    @Override
    public int batchUpdateReportParameterPreset(List<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return 0;
        }
        return reportParameterPresetMapper.batchUpdateReportParameterPreset(reportParameterPresetList);
    }
}
