package com.siteweb.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.exception.BigDataException;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.SimpleSignalDTO;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.HistorySignalManager;
import com.siteweb.monitoring.model.HistorySignalGroupByTime;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.report.dto.HistorySignalExportDTO;
import com.siteweb.report.dto.HistorySignalParamEquipmentSignalIdDTO;
import com.siteweb.report.enums.TimeGranularityTypeEnum;
import com.siteweb.report.parser.model.HistorySignalReportParam;
import com.siteweb.report.service.ReportExportService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ReportExportServiceImpl implements ReportExportService {
    private final ThreadLocal<HashMap<String, ConfigSignalItem>> mapThreadLocal = ThreadLocal.withInitial(HashMap::new);
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    ConfigSignalManager configSignalManager;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    SignalService signalService;
    @Autowired
    SystemConfigService systemConfigService;
    @Override
    public List<HistorySignalExportDTO> findHistorySignal(Integer userId, HistorySignalReportParam param) {
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIdSet)) {
            return Collections.emptyList();
        }
        List<HistorySignalParamEquipmentSignalIdDTO> querySignalList = null;
        if (CharSequenceUtil.isNotBlank(param.getSignalIds())) {
            querySignalList = JSONUtil.toList(param.getSignalIds(), HistorySignalParamEquipmentSignalIdDTO.class);
        } else if (CollUtil.isNotEmpty(param.getBaseTypeIds())){
            querySignalList = signalService.findSimpleSignalDTOsByBaseTypeIdsAndEquipmentIds(param.getBaseTypeIds(), param.getEquipmentIds())
                                           .stream()
                                           .map(HistorySignalParamEquipmentSignalIdDTO::new)
                                           .toList();
        } else {
            return Collections.emptyList();
        }
        String timeGranularity = param.getTimeGranularity();
        if (CharSequenceUtil.isEmpty(timeGranularity) || Objects.equals("0", timeGranularity)) {
            //默认查询原始数据
            return queryOriginHistoryData(param, equipmentIdSet, querySignalList);
        }
        //时间颗粒度查询
        return queryDataByTimeGranularity(param, equipmentIdSet, querySignalList, timeGranularity);
    }


    @Override
    public long historySignalReportCountSample(Integer userId, HistorySignalReportParam params) {
        //估算的开始时间(昨天的现在这个点)与结束时间(昨天的现在这个点加一个小时)
        Date sampleStartTime = DateUtil.offsetDay(new Date(),-1);
        Date sampleEndTime = DateUtil.offsetHour(sampleStartTime,1);
        //查询信号数据 获取仅一个小时的数据量估算
        if (CharSequenceUtil.isNotBlank(params.getSignalIds())) {
            List<HistorySignalParamEquipmentSignalIdDTO> querySignalList = JSONUtil.toList(params.getSignalIds(), HistorySignalParamEquipmentSignalIdDTO.class);
            return getSignalSampleCount(params, querySignalList, sampleStartTime, sampleEndTime);
        }
        //查询信号基类数据 获取仅一个小时的数据量估算
        if (CollUtil.isNotEmpty(params.getBaseTypeIds())) {
            return getBaseTypeSampleCount(params, params.getBaseTypeIds(), sampleStartTime, sampleEndTime);
        }
        return 0;
    }

    /**
     * 获取信号的预估数量
     * @param params 查询参数
     * @param baseTyeIdList 需要查询的信号基类Ids
     * @param sampleStartTime 开始时间
     * @param sampleEndTime 结束时间
     * @return long 预估的数量
     */
    private long getBaseTypeSampleCount(HistorySignalReportParam params, List<Integer> baseTyeIdList, Date sampleStartTime, Date sampleEndTime) {
        if (CollUtil.isEmpty(baseTyeIdList)) {
            return 0;
        }
        List<SimpleSignalDTO> simpleSignalDTOS = signalService.findSimpleSignalDTOsByBaseTypeIdsAndEquipmentIds(baseTyeIdList, params.getEquipmentIds());
        if (systemConfigService.signalMaxCount() < simpleSignalDTOS.size()) {
            //信号数量超过设置的最大值
            throw new BigDataException("signal too much");
        }
        if (CharSequenceUtil.isEmpty(params.getTimeGranularity()) || Objects.equals("0", params.getTimeGranularity())) {
            //查询原始数据 获取仅一个小时的数据量估算
            List<String> realSignalKeyList = simpleSignalDTOS.stream().map(SimpleSignalDTO::getRealSignalKey).toList();
            List<HistorySignal> historySignals = historySignalManager.findHistorySignalBySignalKeys(sampleStartTime, sampleEndTime, realSignalKeyList, params.getSignalTypes());
            long lineNum = getLineNumByHistorySignalList(historySignals);
            long hourNum = DateUtil.between(params.getStartTime(), params.getEndTime(), DateUnit.HOUR);
            return hourNum * lineNum * simpleSignalDTOS.size();
        }
        //其他时间颗粒度
        return calculateGranularitySignalCount(simpleSignalDTOS.size(), params.getTimeGranularity(), params.getStartTime(), params.getEndTime());
    }

    /**
     * 获取信号的预估数量
     * @param params 查询参数
     * @param querySignalList 需要查询的信号参数
     * @param sampleStartTime 开始时间
     * @param sampleEndTime 结束时间
     * @return long 预估的数量
     */
    private long getSignalSampleCount(HistorySignalReportParam params, List<HistorySignalParamEquipmentSignalIdDTO> querySignalList, Date sampleStartTime, Date sampleEndTime) {
        if (CollUtil.isEmpty(querySignalList)) {
            return 0;
        }
        if (systemConfigService.signalMaxCount() < querySignalList.size()) {
            //信号数量超过设置的最大值
            throw new BigDataException("signal too much");
        }
        //原始颗粒度
        if (CharSequenceUtil.isEmpty(params.getTimeGranularity()) || Objects.equals("0", params.getTimeGranularity())) {
            long lineNum = 0;
            for (HistorySignalParamEquipmentSignalIdDTO historySignalParamEquipmentSignalIdDTO : querySignalList) {
                List<HistorySignal> historySignals = historySignalManager.findHistorySignalByDurationAndSignalType(sampleStartTime, sampleEndTime, params.getSignalTypes(), historySignalParamEquipmentSignalIdDTO.getEquipmentId(), historySignalParamEquipmentSignalIdDTO.getSignalId(), params.isSwitchDatabase2());
                lineNum = getLineNumByHistorySignalList(historySignals);
            }
            //预估数量 = 一个小时的行数 * 开始时间与结束时间的时间差值(小时) * 列数
            long hourNum = DateUtil.between(params.getStartTime(), params.getEndTime(), DateUnit.HOUR);
            return lineNum * hourNum * querySignalList.size();
        }
        //其他时间颗粒度
        return calculateGranularitySignalCount(querySignalList.size(), params.getTimeGranularity(), params.getStartTime(), params.getEndTime());
    }

    private long getLineNumByHistorySignalList(List<HistorySignal> historySignals) {
        if (CollUtil.isEmpty(historySignals)) {
            return 0;
        }
        //有多少个时间点代表有多少行
        return historySignals.stream()
                             .map(HistorySignal::getTime)
                             .distinct().count();
    }

    /**
     * 使用时间颗粒度预估数据量
     * 所需查询信号条数 * 查询时间长度  = 预估数量
     * @param signalCount 信号条数
     * @param timeGranularity 时间颗粒度
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return long
     */
    public long calculateGranularitySignalCount(int signalCount,String timeGranularity,Date startTime,Date endTime){
        long count;
        long timeInterval;
        switch (timeGranularity) {
            case "1m-start" -> {
                //一分钟颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.MINUTE);
                count = timeInterval * signalCount;
            }
            case "5m-start" -> {
                //五分钟颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.MINUTE);
                count = (timeInterval / 5) * signalCount;
            }
            case "10m-start" -> {
                //十分钟颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.MINUTE);
                count = (timeInterval / 10) * signalCount;
            }
            case "30m-start" -> {
                //三十分钟颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.MINUTE);
                count = (timeInterval / 30) * signalCount;
            }
            case "1h-start" -> {
                //一小时颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.HOUR);
                count = timeInterval * signalCount;
            }
            case "3h-start" -> {
                //三小时颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.HOUR);
                count = (timeInterval / 3) * signalCount;
            }
            case "6h-start" -> {
                //六小时颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.HOUR);
                count = (timeInterval / 6) * signalCount;
            }
            case "12h-start" -> {
                //12小时颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.HOUR);
                count = (timeInterval / 12) * signalCount;
            }
            case "1d-start" -> {
                //一天颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.DAY);
                count = timeInterval * signalCount;
            }
            case "1w-start" -> {
                //一周颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.DAY);
                count = (timeInterval / 7) * signalCount;
            }
            case "month-start" -> {
                //一月颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.DAY);
                count = (timeInterval / 30) * signalCount;
            }
            case "year-start" -> {
                //一年颗粒度
                timeInterval = DateUtil.between(startTime, endTime, DateUnit.DAY);
                count = (timeInterval / 365) * signalCount;
            }
            default -> count = 0;
        }
        return count;
    }

    private List<HistorySignalExportDTO> queryDataByTimeGranularity(HistorySignalReportParam param, Set<Integer> equipmentIdSet, List<HistorySignalParamEquipmentSignalIdDTO> querySignalList, String timeGranularity) {
        Boolean minMaxAvgEnable = checkMinMaxAvgEnable(timeGranularity);
        timeGranularity = Boolean.TRUE.equals(minMaxAvgEnable) ? timeGranularity : timeGranularity.split("-")[0];
        List<HistorySignalGroupByTime> historySignalGroupByTimeList = new ArrayList<>(100000);
        Map<String, ConfigSignalItem> map = new HashMap<>();
        for (HistorySignalParamEquipmentSignalIdDTO historySignalParamEquipmentSignalIdDTO : querySignalList) {
            if (!equipmentIdSet.contains(historySignalParamEquipmentSignalIdDTO.getEquipmentId())) {
                continue;
            }
            List<HistorySignalGroupByTime> historySignalGroupByTime;
            if (timeGranularity.contains("w")) {
                // 周查询
                 historySignalGroupByTime = historySignalManager.findWeekHistorySignalGroupByTimeAndSignalType(param.getStartTime(), param.getEndTime(), param.getSignalTypes(),
                         historySignalParamEquipmentSignalIdDTO.getSignalKey(), param.isSwitchDatabase2(), param.getValueRetrievalMethod());
            } else if (timeGranularity.contains("month")) {
                // 月查询
                 historySignalGroupByTime = historySignalManager.findMonthHistorySignalGroupByTimeAndSignalType(param.getStartTime(), param.getEndTime(),  param.getSignalTypes(),
                         historySignalParamEquipmentSignalIdDTO.getSignalKey(), param.isSwitchDatabase2(),
                         param.getValueRetrievalMethod());
            } else {
                 historySignalGroupByTime = historySignalManager.findHistorySignalGroupByTimeAndSignalType(param.getStartTime(), param.getEndTime(), param.getSignalTypes(),
                         historySignalParamEquipmentSignalIdDTO.getSignalKey(), timeGranularity, null, param.isSwitchDatabase2(),
                         param.getValueRetrievalMethod());
            }
            historySignalGroupByTime.forEach(e -> {
                e.setEquipmentId(historySignalParamEquipmentSignalIdDTO.getEquipmentId());
                e.setSignalId(historySignalParamEquipmentSignalIdDTO.getSignalId());
            });
            historySignalGroupByTimeList.addAll(historySignalGroupByTime);
            map.put(historySignalParamEquipmentSignalIdDTO.getSignalKey(),configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(
                    historySignalParamEquipmentSignalIdDTO.getEquipmentId(), historySignalParamEquipmentSignalIdDTO.getSignalId()));
        }
        List<HistorySignalExportDTO> exportDTOS = new ArrayList<>(100000);
        for (HistorySignalGroupByTime historySignalGroupByTime : historySignalGroupByTimeList) {
            String equipmentName = equipmentManager.getEquipmentById(historySignalGroupByTime.getEquipmentId()).getEquipmentName();
            ConfigSignalItem configSignalItem = map.get(historySignalGroupByTime.getEquipmentId() + "." + historySignalGroupByTime.getSignalId());
            HistorySignalExportDTO historySignalExportDTO = new HistorySignalExportDTO(historySignalGroupByTime.getTime(), equipmentName, configSignalItem.getSignalName(), NumberUtil.roundStr(historySignalGroupByTime.getPointValue(), 2));
            exportDTOS.add(historySignalExportDTO);
        }
        return exportDTOS;
    }
    /**
     * 获取原始数据
     *
     * @param param           参数
     * @param equipmentIdSet  设备id设置
     * @param querySignalList 查询信号列表
     * @return {@link List}<{@link HistorySignalExportDTO}>
     */
    private List<HistorySignalExportDTO> queryOriginHistoryData(HistorySignalReportParam param, Set<Integer> equipmentIdSet, List<HistorySignalParamEquipmentSignalIdDTO> querySignalList) {
        List<HistorySignal> historySignalList = new ArrayList<>(100000);
        Map<String, ConfigSignalItem> map = new HashMap<>();
        for (HistorySignalParamEquipmentSignalIdDTO historySignalParamEquipmentSignalIdDTO : querySignalList) {
            //无权限
            if (!equipmentIdSet.contains(historySignalParamEquipmentSignalIdDTO.getEquipmentId())) {
                continue;
            }
            List<HistorySignal> historySignals = historySignalManager.findHistorySignalByDurationAndSignalType(param.getStartTime(), param.getEndTime(), param.getSignalTypes(), historySignalParamEquipmentSignalIdDTO.getEquipmentId(), historySignalParamEquipmentSignalIdDTO.getSignalId(), param.isSwitchDatabase2());
            historySignalList.addAll(historySignals);
            map.put(historySignalParamEquipmentSignalIdDTO.getSignalKey(),configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(
                    historySignalParamEquipmentSignalIdDTO.getEquipmentId(), historySignalParamEquipmentSignalIdDTO.getSignalId()));
        }
        List<HistorySignalExportDTO> exportDTOS = new ArrayList<>(100000);
        for (HistorySignal historySignal : historySignalList) {
            String equipmentName = equipmentManager.getEquipmentById(Integer.valueOf(historySignal.getDeviceId())).getEquipmentName();
            ConfigSignalItem configSignalItem = map.get(historySignal.getSignalId());
            HistorySignalExportDTO historySignalExportDTO = new HistorySignalExportDTO(historySignal.getTime(), equipmentName, configSignalItem.getSignalName(), NumberUtil.roundStr(historySignal.getPointValue(),2));
            exportDTOS.add(historySignalExportDTO);
        }
        return exportDTOS;
    }
    private static Boolean checkMinMaxAvgEnable(String timeGranularity) {
        if (timeGranularity.contains("-")) {
            TimeGranularityTypeEnum timeGranularityTypeEnum = TimeGranularityTypeEnum.getTimeGranularityTypeEnum(timeGranularity.split("-")[1]);
            return timeGranularityTypeEnum == null;
        }
        return true;
    }
}
