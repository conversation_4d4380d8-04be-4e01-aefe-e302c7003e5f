package com.siteweb.report.service.impl;

import com.siteweb.report.entity.ReportSchemaExportParameter;
import com.siteweb.report.mapper.ReportSchemaExportParameterMapper;
import com.siteweb.report.service.ReportSchemaExportParameterSevice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Habits
 * @time: 2022/5/9 13:52
 * @description:
 **/
@Service
public class ReportSchemaExportParameterSeviceImpl implements ReportSchemaExportParameterSevice {

    @Autowired
    ReportSchemaExportParameterMapper reportSchemaExportParameterMapper;

    @Override
    public List<ReportSchemaExportParameter> findReportSchemaExportParameterBySchemaId(Integer reportSchemaId) {
        return reportSchemaExportParameterMapper.findByReportSchemaId(reportSchemaId);
    }

    @Override
    public ReportSchemaExportParameter findReportSchemaExportParameterById(Integer reportSchemaExportParameterId) {
        return reportSchemaExportParameterMapper.findById(reportSchemaExportParameterId);
    }
}
