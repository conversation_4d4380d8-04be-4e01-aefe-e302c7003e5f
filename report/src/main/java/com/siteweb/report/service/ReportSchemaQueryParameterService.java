package com.siteweb.report.service;

import com.siteweb.report.entity.ReportSchemaQueryParameter;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportSchemaQueryParameterService {

    List<ReportSchemaQueryParameter> findReportSchemaQueryParameterBySchemaId(Integer reportSchemaId);

    ReportSchemaQueryParameter findReportSchemaQueryParameterById(Integer reportSchemaQueryParameterId);
}
