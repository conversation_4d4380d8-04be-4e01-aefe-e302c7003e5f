package com.siteweb.report.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.FileUtil;
import com.siteweb.report.entity.ReportTimingTaskFile;
import com.siteweb.report.entity.ReportTimingTaskFileWorkbookData;
import com.siteweb.report.entity.ReportTimingTaskManagement;
import com.siteweb.report.mapper.ReportTimingTaskFileMapper;
import com.siteweb.report.service.ReportTimingTaskFileService;
import com.siteweb.report.service.ReportTimingTaskManagementService;
import com.siteweb.utility.configuration.FileServerConfig;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.excel.entity.ReportSystemConfig;
import com.siteweb.utility.excel.util.CustomExcelDataProcessUtils;
import com.siteweb.utility.manager.EmailMessageManager;
import com.siteweb.utility.service.SystemConfigService;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: lzy
 * @Date: 2022/6/21 20:35
 */
@Service
public class ReportTimingTaskFileServiceImpl implements ReportTimingTaskFileService {

    private static final Logger logger = LoggerFactory.getLogger(ReportTimingTaskFileServiceImpl.class);
    private static final String REPORT_PATH = "upload-dir/report/";

    @Autowired
    ReportTimingTaskFileMapper reportTimingTaskFileMapper;
    @Autowired
    FileServerConfig fileServerConfig;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    ReportTimingTaskManagementService reportTimingTaskManagementService;
    @Autowired
    EmailMessageManager emailMessageManager;

    @Override
    public IPage<ReportTimingTaskFile> findByReportTimingTaskManagementId(Integer reportTimingTaskManagementId, Integer pageSize, Integer pageNum, String keyword, String order) {
        LambdaQueryWrapper<ReportTimingTaskFile> queryWrapper = Wrappers.lambdaQuery(ReportTimingTaskFile.class)
                .eq(ReportTimingTaskFile::getReportTimingTaskManagementId, reportTimingTaskManagementId)
                .like(CharSequenceUtil.isNotBlank(keyword), ReportTimingTaskFile::getCreateTime, keyword)
                .orderBy(true, GlobalConstants.ASC.equalsIgnoreCase(order), ReportTimingTaskFile::getCreateTime);
        Page<ReportTimingTaskFile> page = Page.of(pageNum, pageSize);
        return reportTimingTaskFileMapper.selectPage(page, queryWrapper);
    }

    @Override
    public ReportTimingTaskFile findById(Integer reportTimingTaskFileId) {
        return reportTimingTaskFileMapper.selectById(reportTimingTaskFileId);
    }

    @Override
    public boolean updateById(ReportTimingTaskFile reportTimingTaskFile) {
        return reportTimingTaskFileMapper.updateById(reportTimingTaskFile) > 0;
    }

    @Override
    @Transactional
    public synchronized void sendReportTimingTaskFileByCustomExcels(ReportTimingTaskFileWorkbookData workbookData) {
        ByteArrayOutputStream bos = null;
        if (workbookData == null) {
            return;
        }

        ReportSystemConfig reportSystemConfig = initReportSystemConfig(systemConfigService);
        String fileName = workbookData.getFileName();
        bos = CustomExcelDataProcessUtils.processExcelData(workbookData, fileServerConfig.getRootPath(), reportSystemConfig);
        if (workbookData.getReportTimingTaskFileId() != null) {
            String path = uploadFile(bos, fileName);
            sendAutoReportEmail(workbookData.getReportTimingTaskFileId(), path, fileName);
        }
    }

    @Override
    public void deleteReportTimingTaskFile(List<Integer> reportTimingTaskFileIds) {
        reportTimingTaskFileIds.forEach(reportTimingTaskFileId -> {
            ReportTimingTaskFile reportTimingTaskFile = reportTimingTaskFileMapper.selectById(reportTimingTaskFileId);
            if (Objects.nonNull(reportTimingTaskFile) && CharSequenceUtil.isNotBlank(reportTimingTaskFile.getFilePath())) {
                File file = new File( reportTimingTaskFile.getFilePath());
                if (file.exists()) {
                    file.delete();
                }
            }
        });
        reportTimingTaskFileMapper.deleteByIds(reportTimingTaskFileIds);
    }

    public static ReportSystemConfig initReportSystemConfig(SystemConfigService systemConfigService) {
        ReportSystemConfig reportSystemConfig = new ReportSystemConfig();

        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("chart.anchor");
        if (systemConfig != null && systemConfig.getSystemConfigValue() != null && systemConfig.getSystemConfigValue().split(",").length == 4) {
            reportSystemConfig.setChartAnchor(systemConfig.getSystemConfigValue());
        }

        reportSystemConfig.setCellWidthSetting(false);
        SystemConfig cellWidthSettingSystemConfig = systemConfigService.findBySystemConfigKey("excel.export.cell.width.setting");
        if (cellWidthSettingSystemConfig != null && cellWidthSettingSystemConfig.getSystemConfigValue().equals("true")) {
            reportSystemConfig.setCellWidthSetting(true);
        }

        reportSystemConfig.setCellHeightSetting(false);
        SystemConfig cellHeightSettingSystemConfig = systemConfigService.findBySystemConfigKey("excel.export.cell.height.setting");
        if (cellHeightSettingSystemConfig != null && cellHeightSettingSystemConfig.getSystemConfigValue().equals("true")) {
            reportSystemConfig.setCellHeightSetting(true);
        }
        return reportSystemConfig;
    }

    private String uploadFile(ByteArrayOutputStream bos, String fileName) {
        File outfile = null;
        String path = REPORT_PATH;
        outfile = new File(path);
        try {
            if (!outfile.exists()) {
                outfile.mkdirs();
            }
            String[] fileNames = fileName.split("\\.");
            String suffix = (fileNames.length >= 2 ? (fileNames[0] + DateUtil.dateToString(new Date()) + "." + fileNames[1]) : fileName);
            path += suffix.replace("-", "").replace(" ", "").replace(":", "");
            outfile = new File(FileUtil.filePathFilter(path)).getCanonicalFile();
            if (!outfile.exists()) {
                outfile.createNewFile();
            }
            FileOutputStream stream = FileUtils.openOutputStream(outfile);
            bos.writeTo(stream);
            stream.close();
        } catch (Exception e) {
            logger.error("CustomExcelExportController uploadFile exception{}", e.getMessage());
        }
        return path;
    }

    private void sendAutoReportEmail(Integer reportTimingTaskFileId, String path, String fileName) {
        ReportTimingTaskFile reportTimingTaskFile = findById(reportTimingTaskFileId);
        if (reportTimingTaskFile != null && reportTimingTaskFile.getReportTimingTaskFileId() != null) {
            ReportTimingTaskManagement reportTimingTaskManagement = reportTimingTaskManagementService.findOne(reportTimingTaskFile.getReportTimingTaskManagementId());
            if (reportTimingTaskManagement != null && path != null && CharSequenceUtil.isEmpty(reportTimingTaskFile.getFilePath())) {
                reportTimingTaskFile.setFilePath(path);
                int update = reportTimingTaskFileMapper.updateById(reportTimingTaskFile);
                if (update > 0) {
                    // 如果未设置未设置发送人，则跳过
                    if (StrUtil.isEmpty(reportTimingTaskManagement.getTo())) {
                        return;
                    }
                    File file = new File(reportTimingTaskFile.getFilePath());
                    emailMessageManager.sendMail(reportTimingTaskManagement.getTo(), reportTimingTaskManagement.getCc(), file.getPath(), file.getPath(), fileName, "reportTimingTaskJob");
                }
            }
        }
    }

}
