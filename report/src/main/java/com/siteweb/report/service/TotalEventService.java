package com.siteweb.report.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.report.dto.AllEventDTO;
import com.siteweb.report.dto.EventClassificationStatisticalDTO;
import com.siteweb.report.dto.HistoryEventStatisticsDTO;
import com.siteweb.report.parser.model.AllEventParam;
import com.siteweb.report.parser.model.EventClassificationStatisticalParam;
import com.siteweb.report.vo.EventCount;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 专门用户统计查询事件的服务
 * <AUTHOR>
 * @date 2022/05/26
 */
public interface TotalEventService {
    /**
     * 查询活动告警和历史告警中有告警记录的层级id
     *
     * @param startDate            开始时间
     * @param endDate              结束时间
     * @param resourceStructureIds
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findResourceStructureIdBirthDuration(LocalDateTime startDate, LocalDateTime endDate, List<Integer> resourceStructureIds);

    /**
     * 查找层级资源类型下的活动告警数量
     * @param resourceType 层级类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List}<{@link EventCount}>
     */
    List<EventCount> findEventCountInResourceId(List<Integer> eventResourceStructureIds, Collection<Integer> equipmentIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找层级资源类型下的历史告警数量
     * @param resourceType 层级类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List}<{@link EventCount}>
     */
    List<EventCount> findHistoryEventCountInResourceId(List<Integer> eventResourceStructureIds, Collection<Integer> equipmentIds, LocalDateTime startTime, LocalDateTime endTime);

    Page<AllEventDTO> findAllEvent(AllEventParam param,Page<AllEventDTO> page);

    long findAllEventCount(AllEventParam param);

    List<HistoryEventStatisticsDTO> findHistoryEventBetweenStartTime(LocalDateTime startDate, LocalDateTime endDate, List<Integer> equipmentIds);

    /**
     * 获取告警分类统计数据
     */
    List<EventClassificationStatisticalDTO> findEventClassificationStatistical(EventClassificationStatisticalParam param);
}
