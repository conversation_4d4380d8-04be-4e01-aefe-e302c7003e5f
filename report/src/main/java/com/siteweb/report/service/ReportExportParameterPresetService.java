package com.siteweb.report.service;

import com.siteweb.report.entity.ReportExportParameterPreset;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportExportParameterPresetService {

    /**
     * 批量创建报表导出参数预设
     *
     * @param list
     * @return
     */
    int batchCreateReportExportParameterPreset(List<ReportExportParameterPreset> list);

    /**
     * 根据报表ID查询所有导出参数预设值
     *
     * @param reportId
     * @return
     */
    List<ReportExportParameterPreset> findReportExportParameterPresetByReportId(Integer reportId);

    int deleteReportExportParameterPresetByReportId(Integer reportId);

    int batchUpdateReportExportParameterPreset(List<ReportExportParameterPreset> reportExportParameterPresetList);
}
