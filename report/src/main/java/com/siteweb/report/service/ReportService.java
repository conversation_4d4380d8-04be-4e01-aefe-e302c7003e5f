package com.siteweb.report.service;


import com.siteweb.report.entity.Report;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportService {

    List<Report> findReportByUserIdAndSchemaId(Integer userId, Integer reportSchemaId);

    Integer createReport(Report report);

    Report findByReportId(Integer reportId);

    int deleteReportById(Integer reportId);

    int updateReport(Report report);
}
