package com.siteweb.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.report.entity.Report;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.entity.ReportParameterPreset;
import com.siteweb.report.mapper.ReportMapper;
import com.siteweb.report.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * @author: Habits
 * @time: 2022/4/29 13:13
 * @description:
 **/
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ReportServiceImpl implements ReportService {

    @Autowired
    ReportMapper reportMapper;

    @Autowired
    ReportParameterPresetSevice reportParameterPresetSevice;

    @Autowired
    ReportExportParameterPresetService reportExportParameterPresetService;

    @Autowired
    ReportSchemaExportParameterSevice reportSchemaExportParameterSevice;

    @Autowired
    ReportSchemaQueryParameterService reportSchemaQueryParameterService;
    @Autowired
    ReportFolderService reportFolderService;

    @Override
    public List<Report> findReportByUserIdAndSchemaId(Integer userId, Integer reportSchemaId) {
        return reportMapper.findReportByUserIdAndSchemaId(userId, reportSchemaId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createReport(Report report) {
        int result = reportMapper.insert(report);
        List<ReportExportParameterPreset> reportExportParameterPresetList = report.getReportExportParameterPresetList();
        List<ReportParameterPreset> reportParameterPresetList = report.getReportParameterPresetList();
        this.setReportIdToReportExportParameterPreset(report, reportExportParameterPresetList);
        setReportIdToReportParameterPreset(report, reportParameterPresetList);
        reportParameterPresetSevice.batchCreateReportParameterPreset(report.getReportParameterPresetList());
        reportExportParameterPresetService.batchCreateReportExportParameterPreset(report.getReportExportParameterPresetList());
        return result;
    }

    private void setReportIdToReportParameterPreset(Report report, List<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        reportParameterPresetList.forEach(parameter -> parameter.setReportId(report.getReportId()));
    }

    private void setReportIdToReportExportParameterPreset(Report report, List<ReportExportParameterPreset> reportExportParameterPresetList) {
        if (CollUtil.isEmpty(reportExportParameterPresetList)) {
            return;
        }
        reportExportParameterPresetList.forEach(parameter -> parameter.setReportId(report.getReportId()));
    }

    @Override
    public Report findByReportId(Integer reportId) {
        Report report = reportMapper.selectById(reportId);
        if (Objects.isNull(report)) {
            log.error("定时报表出错了,{}",reportId);
        }
        report.setReportParameterPresetList(reportParameterPresetSevice.findReportParameterPresetByReportId(reportId));
        report.setReportExportParameterPresetList(reportExportParameterPresetService.findReportExportParameterPresetByReportId(reportId));
        return report;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteReportById(Integer reportId) {
        Report report = reportMapper.selectById(reportId);
        if (report == null) {
            return -1;
        }
        reportParameterPresetSevice.deleteReportParameterPresetByReportId(reportId);
        reportExportParameterPresetService.deleteReportExportParameterPresetByReportId(reportId);
        reportFolderService.deleteReportFolderMapByReportId(reportId, 1);
        return reportMapper.deleteById(reportId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateReport(Report report) {
        //更新报表参数
        List<ReportParameterPreset> reportParameterPresetList = report.getReportParameterPresetList();
        reportParameterPresetSevice.batchUpdateReportParameterPreset(reportParameterPresetList);
        //更新导出参数
        List<ReportExportParameterPreset> reportExportParameterPresetList = report.getReportExportParameterPresetList();
        reportExportParameterPresetService.batchUpdateReportExportParameterPreset(reportExportParameterPresetList);
        return reportMapper.updateById(report);
    }
}
