package com.siteweb.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.report.dto.AllEventDTO;
import com.siteweb.report.dto.EventClassificationStatisticalDTO;
import com.siteweb.report.dto.HistoryEventStatisticsDTO;
import com.siteweb.report.mapper.TotalEventMapper;
import com.siteweb.report.parser.model.AllEventParam;
import com.siteweb.report.parser.model.EventClassificationStatisticalParam;
import com.siteweb.report.service.TotalEventService;
import com.siteweb.report.vo.EventCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
public class TotalEventServiceImpl implements TotalEventService {

    @Autowired
    private TotalEventMapper totalEventMapper;

    @Override
    public List<Integer> findResourceStructureIdBirthDuration(LocalDateTime startDate, LocalDateTime endDate, List<Integer> resourceStructureIds) {
        return totalEventMapper.findResourceStructureIdBirthDuration(startDate, endDate, resourceStructureIds);
    }

    @Override
    public List<EventCount> findEventCountInResourceId(List<Integer> eventResourceStructureIds, Collection<Integer> equipmentIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (CollUtil.isEmpty(eventResourceStructureIds) || CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return totalEventMapper.findEventCountInResourceId(eventResourceStructureIds, equipmentIds, startTime, endTime);
    }

    @Override
    public List<EventCount> findHistoryEventCountInResourceId(List<Integer> eventResourceStructureIds, Collection<Integer> equipmentIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (CollUtil.isEmpty(eventResourceStructureIds) || CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return totalEventMapper.findHistoryEventCountInResourceId(eventResourceStructureIds, equipmentIds, startTime, endTime);
    }

    @Override
    public Page<AllEventDTO> findAllEvent(AllEventParam param, Page<AllEventDTO> page) {
        return totalEventMapper.findAllEvent(param,page);
    }

    @Override
    public long findAllEventCount(AllEventParam param) {
        return totalEventMapper.findAllEventCount(param);
    }

    @Override
    public List<HistoryEventStatisticsDTO> findHistoryEventBetweenStartTime(LocalDateTime startDate, LocalDateTime endDate, List<Integer> equipmentIds) {
        return totalEventMapper.findHistoryEventBetweenStartTimeAndEquipmentIds(startDate, endDate, equipmentIds);
    }

    @Override
    public List<EventClassificationStatisticalDTO> findEventClassificationStatistical(EventClassificationStatisticalParam param) {
        return totalEventMapper.findEventClassificationStatistical(param);
    }
}
