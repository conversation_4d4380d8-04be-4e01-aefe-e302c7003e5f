package com.siteweb.report.service.impl;

import com.siteweb.report.entity.ReportDataSource;
import com.siteweb.report.mapper.ReportDataSourceMapper;
import com.siteweb.report.service.ReportDataSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @author: Habits
 * @time: 2022/4/29 13:13
 * @description:
 **/
@Service
public class ReportDataSourceServiceImpl implements ReportDataSourceService {

    @Autowired
    ReportDataSourceMapper reportDataSourceMapper;

    @Override
    public ReportDataSource findReportDataSourceById(Integer reportDataSourceId) {
        return reportDataSourceMapper.selectById(reportDataSourceId);
    }
}
