package com.siteweb.report.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.report.entity.ReportSchemaCategory;
import com.siteweb.report.mapper.ReportSchemaCategoryMapper;
import com.siteweb.report.service.ReportSchemaCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Habits
 * @time: 2022/4/29 13:13
 * @description:
 **/
@Service
public class ReportSchemaCategoryServiceImpl implements ReportSchemaCategoryService {

    @Autowired
    ReportSchemaCategoryMapper reportSchemaCategoryMapper;

    @Override
    public List<ReportSchemaCategory> findReportSchemaCategorys() {
        return reportSchemaCategoryMapper.selectList(Wrappers.emptyWrapper());
    }
}
