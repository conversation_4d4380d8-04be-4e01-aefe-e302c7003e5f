package com.siteweb.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.report.entity.ReportTimingTaskFile;
import com.siteweb.report.entity.ReportTimingTaskFileWorkbookData;

import java.util.List;


/**
 * @Author: lzy
 * @Date: 2022/6/21 20:34
 */
public interface ReportTimingTaskFileService {

    IPage<ReportTimingTaskFile> findByReportTimingTaskManagementId(Integer reportTimingTaskManagementId, Integer pageSize, Integer pageNum, String keyword, String order);

    ReportTimingTaskFile findById(Integer reportTimingTaskFileId);

    boolean updateById(ReportTimingTaskFile reportTimingTaskFile);

    void sendReportTimingTaskFileByCustomExcels(ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData);

    void deleteReportTimingTaskFile(List<Integer> reportTimingTaskFileIds);
}
