package com.siteweb.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.mapper.ReportExportParameterPresetMapper;
import com.siteweb.report.service.ReportExportParameterPresetService;
import com.siteweb.report.service.ReportSchemaExportParameterSevice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Habits
 * @time: 2022/5/10 10:30
 * @description:
 **/
@Service
public class ReportExportParameterPresetServiceImpl implements ReportExportParameterPresetService {

    @Autowired
    ReportExportParameterPresetMapper reportExportParameterPresetMapper;

    @Autowired
    ReportSchemaExportParameterSevice reportSchemaExportParameterSevice;

    @Override
    public int batchCreateReportExportParameterPreset(List<ReportExportParameterPreset> list) {
        if (CollectionUtil.isEmpty(list)) {
            return 0;
        }
        return reportExportParameterPresetMapper.batchCreateReportExportParameterPreset(list);
    }

    @Override
    public List<ReportExportParameterPreset> findReportExportParameterPresetByReportId(Integer reportId) {
        List<ReportExportParameterPreset> reportExportParameterPresets = reportExportParameterPresetMapper
                .selectList(new QueryWrapper<ReportExportParameterPreset>()
                        .eq("ReportId", reportId));
        reportExportParameterPresets.forEach(a -> a.setReportSchemaExportParameter(reportSchemaExportParameterSevice.findReportSchemaExportParameterById(a.getReportSchemaExportParameterId())));
        return reportExportParameterPresets;
    }

    @Override
    public int deleteReportExportParameterPresetByReportId(Integer reportId) {
        return reportExportParameterPresetMapper.delete(new QueryWrapper<ReportExportParameterPreset>().eq("ReportId", reportId));
    }

    @Override
    public int batchUpdateReportExportParameterPreset(List<ReportExportParameterPreset> reportExportParameterPresetList) {
        if (CollUtil.isEmpty(reportExportParameterPresetList)) {
            return 0;
        }
        return reportExportParameterPresetMapper.batchUpdateReportExportParameterPreset(reportExportParameterPresetList);
    }
}
