package com.siteweb.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.report.entity.ReportTimingTaskTimeType;
import com.siteweb.report.mapper.ReportTimingTaskTimeTypeMapper;
import com.siteweb.report.service.ReportTimingTaskTimeTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/13 9:31
 */
@Service
public class ReportTimingTaskTimeTypeServiceImpl implements ReportTimingTaskTimeTypeService {

    @Autowired
    ReportTimingTaskTimeTypeMapper mapper;

    @Override
    public List<ReportTimingTaskTimeType> findAll() {
        return mapper.selectList(new QueryWrapper<>());
    }
}
