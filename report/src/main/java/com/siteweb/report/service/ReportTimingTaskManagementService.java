package com.siteweb.report.service;

import com.siteweb.report.entity.ReportTimingTaskManagement;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/13 9:59
 */
public interface ReportTimingTaskManagementService {

    ReportTimingTaskManagement findOne(Integer id);

    ReportTimingTaskManagement create(ReportTimingTaskManagement build);

    ReportTimingTaskManagement update(ReportTimingTaskManagement build);

    Boolean delete(Integer id);

    List<ReportTimingTaskManagement> findByUserId(Integer userId);
}
