package com.siteweb.report.service;

import com.siteweb.report.entity.ReportParameterPreset;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportParameterPresetSevice {

    /**
     * 批量创建报表参数预设
     *
     * @param list
     * @return
     */
    int batchCreateReportParameterPreset(List<ReportParameterPreset> list);

    /**
     * 根据报表id查询报表参数预设
     *
     * @param reportId
     * @return
     */
    List<ReportParameterPreset> findReportParameterPresetByReportId(Integer reportId);

    int deleteReportParameterPresetByReportId(Integer reportId);

    int batchUpdateReportParameterPreset(@Param("reportParameterPresetList") List<ReportParameterPreset> reportParameterPresetList);
}
