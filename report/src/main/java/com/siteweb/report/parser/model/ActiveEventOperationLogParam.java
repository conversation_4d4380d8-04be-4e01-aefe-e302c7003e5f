package com.siteweb.report.parser.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * @Author: lzy
 * @Date: 2022/5/23 10:53
 */
@Data
public class ActiveEventOperationLogParam {

    private static final Logger log = LoggerFactory.getLogger(ActiveEventOperationLogParam.class);

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 设备id
     */
    private Collection<Integer> equipmentIds;
    /**
     * 事件id json体需包含equipmentId和eventId
     */
    private Map<Integer, Set<Integer>> eventIds;
    /**
     * 告警操作
     */
    private String operation;
    /**
     * 操作人id
     */
    private List<Integer> operatorIds;

    private static final String OPERATION_ALL_VALUE = "0";

    public ActiveEventOperationLogParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "equipmentIds":
                        this.equipmentIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                        break;
                    case "eventIds":
                        this.eventIds = ReportParamParserUtils.parseEventStrToMap(reportParameterPreset.getValue());
                        break;
                    case "operatorIds":
                        this.operatorIds = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    case "operation":
                        this.operation = OPERATION_ALL_VALUE.equals(reportParameterPreset.getValue()) ? "" : reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 ActiveEventOperationLogParam 异常：", e);
            throw e;
        }
    }
}
