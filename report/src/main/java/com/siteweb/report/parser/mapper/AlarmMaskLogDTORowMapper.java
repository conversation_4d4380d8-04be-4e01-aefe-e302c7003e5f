package com.siteweb.report.parser.mapper;

import com.siteweb.monitoring.dto.AlarmMaskLogDTO;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Author: lzy
 * @Date: 2022/6/6 15:14
 */
public class AlarmMaskLogDTORowMapper implements RowMapper<AlarmMaskLogDTO> {
    @Override
    public AlarmMaskLogDTO mapRow(ResultSet rs, int rowNum) throws SQLException {
        AlarmMaskLogDTO alarmMaskLogDTO = new AlarmMaskLogDTO();
        alarmMaskLogDTO.setId(rs.getLong("id"));
        alarmMaskLogDTO.setEquipmentId(rs.getInt("EquipmentId"));
        alarmMaskLogDTO.setEventId(rs.getInt("EventId"));
        alarmMaskLogDTO.setEventName(rs.getString("EventName"));
        alarmMaskLogDTO.setResourceStructureId(rs.getInt("ResourceStructureId"));
        alarmMaskLogDTO.setResourceStructureName(rs.getString("ResourceStructureName"));
        alarmMaskLogDTO.setUserId(rs.getInt("UserId"));
        alarmMaskLogDTO.setUserName(rs.getString("UserName"));
        alarmMaskLogDTO.setOperationType(rs.getString("OperationType"));
        alarmMaskLogDTO.setOperationTime(rs.getTimestamp("operationTime"));
        alarmMaskLogDTO.setStartTime(rs.getTimestamp("StartTime"));
        alarmMaskLogDTO.setEndTime(rs.getTimestamp("EndTime"));
        alarmMaskLogDTO.setComment(rs.getString("Comment"));
        alarmMaskLogDTO.setTimeGroupCategory(rs.getString("timeGroupCategory"));
        alarmMaskLogDTO.setTimeGroupChars(rs.getString("timeGroupChars"));
        return alarmMaskLogDTO;
    }
}
