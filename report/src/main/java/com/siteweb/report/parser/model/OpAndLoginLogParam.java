package com.siteweb.report.parser.model;

import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Slf4j
public class OpAndLoginLogParam {

    private Date startDate;
    private Date endDate;
    private List<Integer> operatorIds;

    private static final String OPERATION_ALL_VALUE = "0";

    public OpAndLoginLogParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null || reportParameterPreset.getValue().equals("")) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "operatorIds":
                        this.operatorIds = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 OpAndLoginLogParam 异常: ", e);
            throw e;
        }
    }
}
