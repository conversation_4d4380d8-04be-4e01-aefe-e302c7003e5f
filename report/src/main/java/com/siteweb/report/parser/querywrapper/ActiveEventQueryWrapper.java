package com.siteweb.report.parser.querywrapper;

import lombok.Data;

import java.util.*;

/**
 */
@Data
public class ActiveEventQueryWrapper {
    private Date startDate;
    private Date endDate;
    private Set<Integer> baseEquipmentIdList;
    /**
     * 设备类型
     */
    private Set<Integer> equipmentCategories;
    private Set<Integer> eventLevelList;
    private String eventName;
    private String keyword;
    private List<Integer> operatorIds;
    private Set<Integer> equipmentIdList;
    private String description;
    /**
     * 告警分类(告警原因类型)
     */
    private List<Integer> eventReasonTypes;
    private List<String> sql = new ArrayList<>();
}
