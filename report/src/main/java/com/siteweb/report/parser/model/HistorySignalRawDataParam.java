package com.siteweb.report.parser.model;

import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@Slf4j
public class HistorySignalRawDataParam {
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 信号
     */
    private String signalIds;
    /**
     * 信号基类
     */
    private List<Integer> baseTypeIds;

    /**
     * 信号类型
     */
    private String signalTypes;
    /**
     * 是否带单位
     */
    private String unit;
    /**
     * 设备id
     */
    private Set<Integer> equipmentIds;

    public HistorySignalRawDataParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime":
                        this.startTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endTime" :
                        this.endTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "signalIds":
                        this.signalIds = reportParameterPreset.getValue();
                        break;
                    case "baseTypeIds":
                        this.baseTypeIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.BASE_TYPE_ID);
                        break;
                    case "signalTypes":
                        this.signalTypes = reportParameterPreset.getValue();
                        break;
                    case "unit":
                        this.unit = reportParameterPreset.getValue();
                        break;
                    case "equipmentIds":
                        this.equipmentIds = ReportParamParserUtils.jsonToSet(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 HistorySignalRawDataParam 异常: ", e);
            throw e;
        }
    }
}
