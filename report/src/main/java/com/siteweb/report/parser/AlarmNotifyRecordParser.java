package com.siteweb.report.parser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyRecordDto;
import com.siteweb.eventnotification.service.AlarmNotifyRecordService;
import com.siteweb.eventnotification.vo.AlarmNotifyRecordVO;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.AlarmNotifyRecordParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

/**
 * 告警通知记录报表
 *
 * <AUTHOR>
 * @date 2022/08/29
 */
@Component
public class AlarmNotifyRecordParser extends ReportParser {
    protected AlarmNotifyRecordParser() {
        super(ReportDataSourceEnum.ALARMNOTIFYRECORD.getValue());
    }

    @Autowired
    private AlarmNotifyRecordService alarmNotifyRecordService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private EquipmentService equipmentService;
    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        AlarmNotifyRecordParam alarmNotifyRecordParam = new AlarmNotifyRecordParam(reportVO.getReportParameterPresetList());
        JSONArray bodyJsonObject = this.getTableBody(reportVO.getUserId(),alarmNotifyRecordParam, reportVO.getPageable(), resultJsonObject);
        //创建表头 添加表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return resultJsonObject;
    }

    private JSONArray getTableBody(Integer userId,AlarmNotifyRecordParam alarmNotifyRecordParam, Pageable pageable, JSONObject jsonObject) {
        AlarmNotifyRecordVO recordParam = BeanUtil.copyProperties(alarmNotifyRecordParam, AlarmNotifyRecordVO.class);
        JSONArray jsonArray = new JSONArray();
        List<AlarmNotifyRecordDto> alarmNotifyRecordDtos = null;
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (pageable != null) {
            Page<AlarmNotifyRecordDto> page = new Page<>(pageable.getPageNumber() +1, pageable.getPageSize(),false);
            IPage<AlarmNotifyRecordDto> result = alarmNotifyRecordService.findByReportParamPage(page, recordParam, equipmentIds);
            result.setTotal(alarmNotifyRecordService.findByReportParamCount(recordParam, equipmentIds));
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), result.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), result.getTotal());
            alarmNotifyRecordDtos = result.getRecords();
        } else {
            alarmNotifyRecordDtos = alarmNotifyRecordService.findByReportParam(recordParam, equipmentIds);
        }
        for (AlarmNotifyRecordDto dto : alarmNotifyRecordDtos) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), dto.getEquipmentName());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), dto.getEventName());
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), dto.getEventMeanings());
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), dto.getEventSeverity());
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), dto.getAlarmStartTime());
            thJsonObject.set(ReportStructureEnum.COLUMN6.value(), dto.getReceiver());
            thJsonObject.set(ReportStructureEnum.COLUMN7.value(), dto.getSendTime());
            thJsonObject.set(ReportStructureEnum.COLUMN8.value(), dto.getContent());
            thJsonObject.set(ReportStructureEnum.COLUMN9.value(), dto.getSendType());
            thJsonObject.set(ReportStructureEnum.COLUMN10.value(), dto.getSendResult());
            jsonArray.add(thJsonObject);
        }
        return jsonArray;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.eventName"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.eventMeanings"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.eventSeverity"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.alarmTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.receiver"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.sendTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.sendContent"));
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.sendType"));
        defaultColumns.put(ReportStructureEnum.COLUMN10.value(), messageSourceUtil.getMessage("common.report.form.sendResult"));
        return defaultColumns;
    }


}
