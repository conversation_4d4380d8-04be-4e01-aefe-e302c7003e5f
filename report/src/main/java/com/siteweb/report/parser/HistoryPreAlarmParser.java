package com.siteweb.report.parser;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.mapper.HistoryPreAlarmParserMapper;
import com.siteweb.report.parser.model.HistoryPreAlarmReportParam;
import com.siteweb.report.parser.querywrapper.HistoryPreAlarmQueryWrapper;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

@Component
public class HistoryPreAlarmParser extends ReportParser {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private HistoryPreAlarmParserMapper historyPreAlarmParserMapper;

    protected HistoryPreAlarmParser() {
        super(ReportDataSourceEnum.PREALARM_HISTORY.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        HistoryPreAlarmReportParam reportParam = new HistoryPreAlarmReportParam(reportVO.getReportParameterPresetList());
        List<PreAlarmHistory> historyPreAlarmList = new ArrayList<>();

        JSONObject jsonObject = new JSONObject(true);

        Pageable pageable = reportVO.getPageable();
        historyPreAlarmList = findPreAlarmHistoryPage(reportParam, pageable, jsonObject);

        // 设置表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());

        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        JSONArray jsonArray = new JSONArray();

        // 设置内容
        Integer index = pageable == null ? 1 : 1 + pageable.getPageNumber() * pageable.getPageSize();
        JSONObject tdJsonObject;
        for (PreAlarmHistory preAlarmHistory : historyPreAlarmList) {
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), preAlarmHistory.getPreAlarmSeverityName());
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), preAlarmHistory.getLevelOfPathName());
            tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), preAlarmHistory.getObjectName());
            tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), preAlarmHistory.getMeanings());
            tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), preAlarmHistory.getPreAlarmCategoryName());
            tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), preAlarmHistory.getTriggerValue());
            tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), Objects.isNull(preAlarmHistory.getStartTime()) ? null : DateUtil.dateToString(preAlarmHistory.getStartTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), Objects.isNull(preAlarmHistory.getConfirmTime()) ? null : DateUtil.dateToString(preAlarmHistory.getConfirmTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), Objects.isNull(preAlarmHistory.getEndTime()) ? null : DateUtil.dateToString(preAlarmHistory.getEndTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN10.value(), preAlarmHistory.getConfirmName());

            jsonArray.add(tdJsonObject);
        }

        jsonObject.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return jsonObject;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("prealarm.report.form.preAlarmSeverity"));  // 预警等级
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("prealarm.report.form.levelOfPath"));        // 资源层级
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("prealarm.report.form.objectName"));         // 资源名称
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("prealarm.report.form.preAlarmName"));       // 预警名称
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("prealarm.report.form.preAlarmCategory"));   // 预警分类
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("prealarm.report.form.triggerValue"));       // 触发值
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("prealarm.report.form.startTime"));          // 开始时间
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("prealarm.report.form.confirmTime"));        // 确认时间
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("prealarm.report.form.endTime"));            // 结束时间
        defaultColumns.put(ReportStructureEnum.COLUMN10.value(), messageSourceUtil.getMessage("prealarm.report.form.confirmName"));       // 确认人
        return defaultColumns;
    }


    public List<PreAlarmHistory> findPreAlarmHistoryPage(HistoryPreAlarmReportParam eventReportParam, Pageable pageable, JSONObject jsonObject) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (loginUserId == null) {
            return new ArrayList<>();
        }

        HistoryPreAlarmQueryWrapper historyPreAlarmQueryWrapper = new HistoryPreAlarmQueryWrapper();

        if (ObjectUtil.isNotNull(eventReportParam.getStartDate())) {
            String startDate = DateUtil.dateToString(eventReportParam.getStartDate());
            historyPreAlarmQueryWrapper.setStartDate(startDate);
        }
        if (ObjectUtil.isNotNull(eventReportParam.getEndDate())) {
            String endDate = DateUtil.dateToString(eventReportParam.getEndDate());
            historyPreAlarmQueryWrapper.setEndDate(endDate);
        }
        if (ObjectUtil.isNotNull(eventReportParam.getPreAlarmCategory())) {
            historyPreAlarmQueryWrapper.setPreAlarmCategory(eventReportParam.getPreAlarmCategory());
        }
        if (ObjectUtil.isNotNull(eventReportParam.getPreAlarmSeverity())) {
            historyPreAlarmQueryWrapper.setPreAlarmSeverity(eventReportParam.getPreAlarmSeverity());
        }
        if (StrUtil.isNotEmpty(eventReportParam.getLevelOfPath())) {
            historyPreAlarmQueryWrapper.setLevelOfPathName(eventReportParam.getLevelOfPath());
        }
        if (StrUtil.isNotEmpty(eventReportParam.getResourceName())) {
            historyPreAlarmQueryWrapper.setObjectName(eventReportParam.getResourceName());
        }

        Page<PreAlarmHistory> page = new Page<>(1, -1);//无page参数，默认取全部
        if (ObjectUtil.isNotNull(pageable)) {
            page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize()); //current从1开始计数
        }

        IPage<PreAlarmHistory> result = historyPreAlarmParserMapper.findHistoryDataByPage(page, historyPreAlarmQueryWrapper);
        if (ObjectUtil.isNotNull(page)) {
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), result.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), result.getTotal());
        }

        return result.getRecords();
    }
}
