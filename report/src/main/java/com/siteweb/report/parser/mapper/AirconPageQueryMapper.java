package com.siteweb.report.parser.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.airconditioncontrol.dto.AirHistoryControlDTO;
import com.siteweb.airconditioncontrol.entity.AirAutoControlEquipmentChangeLog;
import com.siteweb.airconditioncontrol.entity.AirBatchControlGroupChangeLog;
import com.siteweb.report.parser.model.AirAutoControlEquipmentChangeLogParam;
import com.siteweb.report.parser.model.BatchControlCmdHistoryParam;
import com.siteweb.report.parser.model.BatchControlGroupChangeLogParam;

public interface AirconPageQueryMapper {

    /** 分页查询批量控制分组变更日志记录 */
    IPage<AirBatchControlGroupChangeLog> findBatchControlGroupChangeLogDataByPage(Page<AirBatchControlGroupChangeLog> page, BatchControlGroupChangeLogParam param);
    /** 分页查询批量控制历史下发命令记录 */
    IPage<AirHistoryControlDTO> findBatchControlCmdHistoryByPage(Page<AirHistoryControlDTO> page, BatchControlCmdHistoryParam param);
    /** 分页查询群控设备变更日志记录 */
    IPage<AirAutoControlEquipmentChangeLog> findAirAutoControlEquipmentChangeLogByPage(Page<AirAutoControlEquipmentChangeLog> page, AirAutoControlEquipmentChangeLogParam param);
}
