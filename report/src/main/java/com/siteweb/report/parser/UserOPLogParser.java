package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.entity.OpAndLoginLog;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.parser.model.OpAndLoginLogParam;
import com.siteweb.report.service.OpAndLoginLogService;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/8/25 11:00
 */
@Component
public class UserOPLogParser extends ReportParser {

    @Autowired
    OpAndLoginLogService opAndLoginLogService;
    @Autowired
    AccountService accountService;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        OpAndLoginLogParam opAndLoginLogParam = new OpAndLoginLogParam(reportVO.getReportParameterPresetList());
        if (CollUtil.isEmpty(opAndLoginLogParam.getOperatorIds())) {
            List<Integer> operationIds = new ArrayList<>(accountService.findUserIdByPermission(reportVO.getUserId()));
            opAndLoginLogParam.setOperatorIds(operationIds);
        }

        List <OpAndLoginLog> maintenancePerformanceDetailList = opAndLoginLogService.findOperationDetailByDataAndLogIds(opAndLoginLogParam.getStartDate(), opAndLoginLogParam.getEndDate(), opAndLoginLogParam.getOperatorIds());

        List<OpAndLoginLog> maintenancePerformanceList = opAndLoginLogService.findOperationRecordByDataAndLogIds(opAndLoginLogParam.getStartDate(), opAndLoginLogParam.getEndDate(), opAndLoginLogParam.getOperatorIds());


        maintenancePerformanceDetailList.addAll(maintenancePerformanceList);

        JSONArray jsonArray = new JSONArray();
        for(OpAndLoginLog opAndLoginLog : maintenancePerformanceDetailList){
            JSONObject tdJsonObject = new JSONObject(true);
            tdJsonObject.set("column1", opAndLoginLog.getUserId());
            tdJsonObject.set("column2", opAndLoginLog.getName());
            tdJsonObject.set("column3", opAndLoginLog.getJobNumber());
            if(opAndLoginLog.getObjectType() != null && StrUtil.isNotBlank(opAndLoginLog.getOldValue()) && opAndLoginLog.getNewValue() != null){
                tdJsonObject.set("column4", String.format("对象ID:%s 对象类型:%s 修改前的值:%s 修改后的值:%s", opAndLoginLog.getObjectId(), opAndLoginLog.getPropertyName(), opAndLoginLog.getOldValue(), opAndLoginLog.getNewValue()));
            } else {
                tdJsonObject.set("column4", opAndLoginLog.getContent());
            }
            tdJsonObject.set("column5", opAndLoginLog.getOperationTime());
            jsonArray.add(tdJsonObject);
        }

        JSONObject result = new JSONObject(true);
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        result.set("title", thJsonObject);
        result.set("result", jsonArray);
        return result;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put("column1", messageSourceUtil.getMessage("userOpLog.report.loginName"));
        defaultColumns.put("column2", messageSourceUtil.getMessage("userOpLog.report.userName"));
        defaultColumns.put("column3", messageSourceUtil.getMessage("userOpLog.report.jobNumber"));
        defaultColumns.put("column4", messageSourceUtil.getMessage("userOpLog.report.operatingContent"));
        defaultColumns.put("column5", messageSourceUtil.getMessage("userOpLog.report.operatingTime"));
        return defaultColumns;
    }



    protected UserOPLogParser() {
        super(ReportDataSourceEnum.USER_LOGS.getValue());
    }
}
