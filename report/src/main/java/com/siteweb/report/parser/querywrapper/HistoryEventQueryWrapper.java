package com.siteweb.report.parser.querywrapper;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/12/24 14:36
 */
@Data
public class HistoryEventQueryWrapper {
    private String startTime;
    private String endDate;
    private Integer baseEquipmentId;
    /**
     * 设备类型
     */
    private List<Integer> equipmentCategories;
    private List<Integer> eventLevelList;
    private String eventName;
    private String keyword;
    private List<Integer> operatorIds;
    private Collection<Integer> equipmentIdList;
    private String description;
    private List<String> sql = new ArrayList<>();
    /**
     * 告警分类(告警原因类型)
     */
    private List<Integer> eventReasonTypes;
}
