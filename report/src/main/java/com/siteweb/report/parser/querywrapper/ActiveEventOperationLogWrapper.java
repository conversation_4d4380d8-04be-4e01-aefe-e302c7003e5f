package com.siteweb.report.parser.querywrapper;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class ActiveEventOperationLogWrapper {
    /**
     * 操作
     */
    private String operation;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 操作人
     */
    private Collection<Integer> operatorIds;
    /**
     * 设备id
     */
    private Collection<Integer> equipmentIds;
    /**
     * 操作sql
     */
    private List<String> sql = new ArrayList<>();
}
