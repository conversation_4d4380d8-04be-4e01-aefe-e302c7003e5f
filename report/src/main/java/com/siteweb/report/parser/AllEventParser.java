package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.entity.EventReasonType;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.EventReasonTypeService;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.dto.AllEventDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.AllEventParam;
import com.siteweb.report.service.TotalEventService;
import com.siteweb.report.util.EventUtil;
import com.siteweb.report.vo.ReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTBoolean;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTDLbls;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPieSer;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 所有告警报表
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Slf4j
@Component
public class AllEventParser extends ReportParser {
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    TotalEventService totalEventService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    EventReasonTypeService eventReasonTypeService;
    @Autowired
    AccountService accountService;
    @Autowired
    EventUtil eventUtil;

    protected AllEventParser() {
        super(ReportDataSourceEnum.ALL_EVENT.getValue());
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.serialNo"));
        defaultColumns.put(AllEventDTO.Fields.eventSeverity, messageSourceUtil.getMessage("common.report.form.eventSeverity"));
        defaultColumns.put(AllEventDTO.Fields.equipmentBaseName, messageSourceUtil.getMessage("common.report.form.baseEquipmentName"));
        defaultColumns.put(AllEventDTO.Fields.equipmentName, messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(AllEventDTO.Fields.equipmentCategoryName, messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.eventDescription"));
        defaultColumns.put(AllEventDTO.Fields.meanings, messageSourceUtil.getMessage("eventNotification.activeEvent.meanings"));
        defaultColumns.put(AllEventDTO.Fields.startTime, messageSourceUtil.getMessage("common.report.form.startTime"));
        defaultColumns.put(AllEventDTO.Fields.endTime, messageSourceUtil.getMessage("common.report.form.endTime"));
        defaultColumns.put(AllEventDTO.Fields.reversalNum, messageSourceUtil.getMessage("eventNotification.activeEvent.reversalNum"));
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.duration"));
        defaultColumns.put(AllEventDTO.Fields.resourceStructureId, messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(AllEventDTO.Fields.confirmTime, messageSourceUtil.getMessage("common.report.form.confirmTime"));
        defaultColumns.put(AllEventDTO.Fields.handleStatus, messageSourceUtil.getMessage("common.report.form.alarmProcessStatus"));
        defaultColumns.put(AllEventDTO.Fields.eventReasonType, messageSourceUtil.getMessage("common.report.form.eventReasonTypeName"));
        defaultColumns.put(AllEventDTO.Fields.description, messageSourceUtil.getMessage("common.report.form.Comment"));
        defaultColumns.put(AllEventDTO.Fields.confirmerName, messageSourceUtil.getMessage("common.report.form.confirmerName"));
        defaultColumns.put(AllEventDTO.Fields.eventValue, messageSourceUtil.getMessage("common.report.form.eventValue"));
        defaultColumns.put(AllEventDTO.Fields.eventName, messageSourceUtil.getMessage("common.report.form.eventName"));
        return defaultColumns;
    }


    @Override
    public JSONObject parser(ReportVO reportVO) {
        AllEventParam param = new AllEventParam(reportVO.getReportParameterPresetList());
        JSONObject thJsonObject = new JSONObject(true);
        List<AllEventDTO> tableList = this.getTableList(reportVO.getUserId(), param, reportVO.getPageable(), thJsonObject);
        eventUtil.fillHandlerStatus(tableList, AllEventDTO::getConfirmTime, AllEventDTO::getEndTime, AllEventDTO::setHandleStatus);
        JSONArray bodyJsonObject = this.getTableBody(tableList);
        // 添加表头
        JSONObject tableHead = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        thJsonObject.set(ReportStructureEnum.TITLE.value(), tableHead);
        thJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        if (ObjectUtil.isNull(reportVO.getPageable())){
            thJsonObject.set(ReportStructureEnum.CHARTS.value(),exportChartExcel(DateUtil.dateToStringAndValidIsNull(param.getStartDate()) + "-" + DateUtil.dateToStringAndValidIsNull(param.getEndDate())  , tableHead,bodyJsonObject, tableList));
        }
        return thJsonObject;
    }

    private List<AllEventDTO> getTableList(Integer userId, AllEventParam param, Pageable pageable,JSONObject thJsonObject) {
        //设备过滤
        if (CollUtil.isEmpty(param.getEquipmentIds())) {
            Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
            param.setEquipmentIds(new ArrayList<>(equipmentIds));
        }
        Page<AllEventDTO> allEventList = null;
        int index = 1;
        if (ObjectUtil.isNotNull(pageable)) {
            index = (pageable.getPageNumber() * pageable.getPageSize()) + 1;
            Page<AllEventDTO> page = new Page<>(pageable.getPageNumber() + 1L, pageable.getPageSize(),false);
            allEventList = totalEventService.findAllEvent(param, page);
            //查询总数
            page.setTotal(totalEventService.findAllEventCount(param));
            thJsonObject.set(ReportStructureEnum.TOTALPAGES.value(), page.getPages());
            thJsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), page.getTotal());
        }else{
            Page<AllEventDTO> page = new Page<>(1, -1,false);
            allEventList = totalEventService.findAllEvent(param,page);
        }
        return allEventList.getRecords();
    }
    private JSONArray getTableBody(List<AllEventDTO> allEventList) {
        JSONArray jsonArray = new JSONArray();
        int index = 1;
        for (AllEventDTO event : allEventList) {
            JSONObject tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), index++);
            tdJsonObject.set(AllEventDTO.Fields.eventSeverity, event.getEventSeverity());
            tdJsonObject.set(AllEventDTO.Fields.equipmentName, event.getEquipmentName());
            tdJsonObject.set(AllEventDTO.Fields.resourceStructureId, resourceStructureManager.getFullPath(event.getResourceStructureId()));
            tdJsonObject.set(AllEventDTO.Fields.equipmentBaseName, event.getEquipmentBaseName());
            tdJsonObject.set(AllEventDTO.Fields.equipmentCategoryName, event.getEquipmentCategoryName());
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), String.format("%s - %s - %s - %s", event.getEquipmentName(), event.getEventName(), event.getMeanings(), NumberUtil.roundTo2DecimalPlaces(event.getEventValue())));
            tdJsonObject.set(AllEventDTO.Fields.eventName, event.getEventName());
            tdJsonObject.set(AllEventDTO.Fields.eventValue, ReportParamParserUtils.getTwoDecimal(event.getEventValue()));
            tdJsonObject.set(AllEventDTO.Fields.meanings, event.getMeanings());
            tdJsonObject.set(AllEventDTO.Fields.startTime, DateUtil.dateToStringAndValidIsNull(event.getStartTime()));
            tdJsonObject.set(AllEventDTO.Fields.confirmTime, DateUtil.dateToStringAndValidIsNull(event.getConfirmTime()));
            tdJsonObject.set(AllEventDTO.Fields.confirmerName, event.getConfirmerName());
            tdJsonObject.set(AllEventDTO.Fields.endTime, DateUtil.dateToStringAndValidIsNull(event.getEndTime()));
            tdJsonObject.set(AllEventDTO.Fields.reversalNum, event.getReversalNum());
            tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), DateUtil.getTimeDifference(event.getStartTime(), event.getEndTime()));
            tdJsonObject.set(AllEventDTO.Fields.description, event.getDescription());
            tdJsonObject.set(AllEventDTO.Fields.eventReasonType, event.getEventReasonTypeName());
            tdJsonObject.set(AllEventDTO.Fields.handleStatus, event.getHandleStatus());

            jsonArray.add(tdJsonObject);
        }
        return jsonArray;
    }

    private String exportChartExcel(String analysisTitle, JSONObject header, JSONArray list,List<AllEventDTO> oriList) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {

            List<String> reasonNames = eventReasonTypeService.listEventReasonType().stream().map(EventReasonType::getName).toList();
            List<AllEventDTO> analysisList = oriList.stream().filter(a -> StringUtils.isNotEmpty(a.getEventReasonTypeName())).toList();
            long totalCount = analysisList.size();

            if (CollUtil.isNotEmpty(reasonNames) && CollUtil.isNotEmpty(analysisList)){
                // 原因为空或者带原因的告警数量为空则不增加分析sheet
                //分析表格 标题样式
                CellStyle analysisTitleStyle = workbook.createCellStyle();
                analysisTitleStyle.setAlignment(HorizontalAlignment.CENTER);
                analysisTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                Font font = workbook.createFont();
                font.setFontName("等线");
                font.setFontHeightInPoints((short) 18);
                analysisTitleStyle.setFont(font);

                //百分比样式
                XSSFCellStyle percentageStyle = workbook.createCellStyle();
                XSSFDataFormat format = workbook.createDataFormat();
                percentageStyle.setDataFormat(format.getFormat("0.00%"));
                percentageStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());
                percentageStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                percentageStyle.setAlignment(HorizontalAlignment.CENTER);
                percentageStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                //蓝色靠左样式
                XSSFCellStyle leftBlueStyle = workbook.createCellStyle();
                leftBlueStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
                leftBlueStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                leftBlueStyle.setAlignment(HorizontalAlignment.LEFT);
                leftBlueStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                //浅绿居中样式
                XSSFCellStyle centerGreenStyle = workbook.createCellStyle();
                centerGreenStyle.setFillForegroundColor(IndexedColors.LIME.getIndex());
                centerGreenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                centerGreenStyle.setAlignment(HorizontalAlignment.CENTER);
                centerGreenStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                //浅浅绿样式
                XSSFCellStyle lightGreenStyle = workbook.createCellStyle();
                lightGreenStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                lightGreenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                //合计格的浅灰居中样式
                XSSFCellStyle totalGrayStyle = workbook.createCellStyle();
                totalGrayStyle.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
                totalGrayStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                totalGrayStyle.setAlignment(HorizontalAlignment.CENTER);
                totalGrayStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                //深绿居中样式
                XSSFCellStyle faultTitleStyle = workbook.createCellStyle();
                faultTitleStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
                faultTitleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                faultTitleStyle.setAlignment(HorizontalAlignment.CENTER);
                faultTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                faultTitleStyle.setFont(font);
                //默认居中样式
                XSSFCellStyle defaultCenterStyle = workbook.createCellStyle();
                defaultCenterStyle.setAlignment(HorizontalAlignment.CENTER);
                defaultCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                XSSFSheet sheetAnalysis = workbook.createSheet(messageSourceUtil.getMessage("common.report.form.alarmAnalysis"));
                Row titleHeaderRow = sheetAnalysis.createRow(0);
                titleHeaderRow.setHeightInPoints(36);
                sheetAnalysis.addMergedRegion(new CellRangeAddress(0, 0, 0, reasonNames.size()));
                Cell analysisTitleCell = titleHeaderRow.createCell(0);
                analysisTitleCell.setCellValue(analysisTitle + messageSourceUtil.getMessage("common.report.form.alarmAnalysis"));
                analysisTitleCell.setCellStyle(analysisTitleStyle);

                titleHeaderRow.createCell(reasonNames.size() + 1).setCellValue(messageSourceUtil.getMessage("common.report.form.responsiblePerson") + ":");
                titleHeaderRow.getCell(reasonNames.size() + 1).setCellStyle(defaultCenterStyle);

                titleHeaderRow.createCell(reasonNames.size() + 2).setCellValue(accountService.findUserNameByUserId(TokenUserUtil.getLoginUserId()));
                titleHeaderRow.getCell(reasonNames.size() + 2).setCellStyle(defaultCenterStyle);

                sheetAnalysis.addMergedRegion(new CellRangeAddress(0, 1, reasonNames.size() + 3, reasonNames.size() + 11));
                Cell faultReasonStyleCell = titleHeaderRow.createCell(reasonNames.size() + 3);
                faultReasonStyleCell.setCellValue(messageSourceUtil.getMessage("common.report.form.failureReason"));
                faultReasonStyleCell.setCellStyle(faultTitleStyle);

                Row analysisHeaderRow = sheetAnalysis.createRow(1);
                Cell cell0 = analysisHeaderRow.createCell(0);
                cell0.setCellValue(messageSourceUtil.getMessage("common.report.form.deviceType"));
                cell0.setCellStyle(centerGreenStyle);

                int herarCol = 1;
                for (String reasonName : reasonNames) {
                    Cell cell = analysisHeaderRow.createCell(herarCol++);
                    cell.setCellValue(reasonName);
                    cell.setCellStyle(centerGreenStyle);
                }
                Cell totalCell = analysisHeaderRow.createCell(herarCol++);
                totalCell.setCellValue(messageSourceUtil.getMessage("common.report.form.total"));
                totalCell.setCellStyle(centerGreenStyle);
                Cell percentageCell = analysisHeaderRow.createCell(herarCol);
                percentageCell.setCellValue(messageSourceUtil.getMessage("common.report.form.proportion"));
                percentageCell.setCellStyle(centerGreenStyle);
                //各设备类型各告警结束原因的数量
                Map<String, Map<String, Long>> groupMap = oriList.stream()
                        .filter(a -> StringUtils.isNotEmpty(a.getEventReasonTypeName()))
                        .collect(Collectors.groupingBy(
                                AllEventDTO::getEquipmentCategoryName,
                                Collectors.groupingBy(
                                        AllEventDTO::getEventReasonTypeName,
                                        Collectors.counting()
                                )
                        ));
                //故障原因
                String[] CIRCLED_NUMBERS = {
                        "①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩",
                        "⑪", "⑫", "⑬", "⑭", "⑮", "⑯", "⑰", "⑱", "⑲", "⑳"
                };
                Map<String, String> commentMap = oriList.stream()
                        .filter(a -> StringUtils.isNotEmpty(a.getEventReasonTypeName()) && StringUtils.isNotEmpty(a.getDescription()))
                        .collect(Collectors.groupingBy(
                                AllEventDTO::getEquipmentCategoryName,
                                Collectors.collectingAndThen(
                                        Collectors.mapping(AllEventDTO::getDescription, Collectors.toSet()),
                                        commentList -> IntStream.range(0, commentList.size())
                                                .mapToObj(i -> (i < CIRCLED_NUMBERS.length ? CIRCLED_NUMBERS[i] : (i + 1) + ".")
                                                        + commentList.toArray(new String[0])[i])
                                                .collect(Collectors.joining("；"))
                                )
                        ));



                int sheet2RowIndex = 2;
                Map<String, Long> totalSeverityCount = new HashMap<>();
                for (String reasonName : reasonNames) {
                    totalSeverityCount.put(reasonName, 0L);
                }
                for (Map.Entry<String, Map<String, Long>> entry : groupMap.entrySet()) {
                    String equipmentType = entry.getKey();
                    Map<String, Long> severityCountMap = entry.getValue();
                    Row row = sheetAnalysis.createRow(sheet2RowIndex);
                    Cell cell = row.createCell(0);
                    cell.setCellValue(equipmentType);
                    cell.setCellStyle(leftBlueStyle);

                    long typeTotal = 0;
                    for (int i = 0; i < reasonNames.size(); i++) {
                        String reasonName = reasonNames.get(i);
                        long count = severityCountMap.getOrDefault(reasonName, 0L);
                        if (count != 0L){
                            row.createCell(i + 1).setCellValue(count);
                            row.getCell(i + 1).setCellStyle(defaultCenterStyle);
                        }
                        typeTotal += count;
                        totalSeverityCount.put(reasonName, totalSeverityCount.get(reasonName) + count);
                    }

                    Cell typeTotalCell = row.createCell(reasonNames.size() + 1);
                    typeTotalCell.setCellValue(typeTotal);
                    typeTotalCell.setCellStyle(totalGrayStyle);

                    row.createCell(reasonNames.size() + 2).setCellValue((double) typeTotal / totalCount);
                    row.getCell(reasonNames.size() + 2).setCellStyle(percentageStyle);
                    sheetAnalysis.addMergedRegion(new CellRangeAddress(sheet2RowIndex, sheet2RowIndex, reasonNames.size() + 3, reasonNames.size() + 11));
                    Cell faultReasonCell = row.createCell(reasonNames.size() + 3);
                    faultReasonCell.setCellStyle(lightGreenStyle);
                    if (commentMap.containsKey(equipmentType)){
                        faultReasonCell.setCellValue(equipmentType + ":" + commentMap.get(equipmentType));
                    }
                    else {
                        faultReasonCell.setCellValue(messageSourceUtil.getMessage("common.report.form.noFailureReason"));
                    }
                    sheet2RowIndex++;
                }
                // 合计行
                Row totalRow = sheetAnalysis.createRow(sheet2RowIndex++);
                totalRow.createCell(0).setCellValue(messageSourceUtil.getMessage("common.report.form.total"));
                long grandTotal = 0;
                for (int i = 0; i < reasonNames.size(); i++) {
                    long severityTotal = totalSeverityCount.get(reasonNames.get(i));
                    Cell cell = totalRow.createCell(i + 1);
                    cell.setCellValue(severityTotal);
                    cell.setCellStyle(totalGrayStyle);
                    grandTotal += severityTotal;
                }
                totalRow.createCell(reasonNames.size() + 1).setCellValue(grandTotal);
                totalRow.getCell(reasonNames.size() + 1).setCellStyle(totalGrayStyle);
                totalRow.createCell(reasonNames.size() + 2).setCellValue(1d);
                totalRow.getCell(reasonNames.size() + 2).setCellStyle(percentageStyle);
                //占比行
                Row percentageRow = sheetAnalysis.createRow(sheet2RowIndex++);
                percentageRow.createCell(0).setCellValue(messageSourceUtil.getMessage("common.report.form.proportion"));
                for (int i = 0; i < reasonNames.size(); i++) {
                    long severityTotal = totalSeverityCount.get(reasonNames.get(i));
                    percentageRow.createCell(i + 1).setCellValue((double) severityTotal / totalCount);
                    percentageRow.getCell(i + 1).setCellStyle(percentageStyle);
                }
                percentageRow.createCell(reasonNames.size() + 1).setCellValue(1d);
                percentageRow.getCell(reasonNames.size() + 1).setCellStyle(percentageStyle);

                //  柱状图
                XSSFDrawing drawing = sheetAnalysis.createDrawingPatriarch();
                XSSFClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 0, sheet2RowIndex+1, reasonNames.size() + 3, sheet2RowIndex * 3);

                XSSFChart chart = drawing.createChart(anchor);
                chart.setTitleText(messageSourceUtil.getMessage("common.report.form.alarmDistribution"));
                chart.setTitleOverlay(false);
                chart.getCTChart().getTitle().getTx().getRich().getPArray(0).getRArray(0).getRPr().setSz(1800);


                XDDFChartLegend legend = chart.getOrAddLegend();
                legend.setPosition(LegendPosition.BOTTOM);
                XDDFCategoryAxis xAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
                xAxis.setTitle(messageSourceUtil.getMessage("common.report.form.deviceType"));

                XDDFValueAxis yAxis = chart.createValueAxis(AxisPosition.LEFT);
                yAxis.setTitle(messageSourceUtil.getMessage("common.report.form.alarmCount"));

                XDDFDataSource<String> categoryData = XDDFDataSourcesFactory.fromStringCellRange(sheetAnalysis,
                        new CellRangeAddress(2, sheet2RowIndex - 3, 0, 0));
                XDDFChartData chartData = chart.createData(ChartTypes.BAR, xAxis, yAxis);

                for (int i = 0; i < reasonNames.size(); i++) {
                    XDDFNumericalDataSource<Double> seriesData = XDDFDataSourcesFactory.fromNumericCellRange(sheetAnalysis,
                            new CellRangeAddress(2, sheet2RowIndex - 3, i + 1, i + 1));

                    XDDFChartData.Series series = chartData.addSeries(categoryData, seriesData);
                    series.setTitle(reasonNames.get(i), null);
                }

                ((XDDFBarChartData) chartData).setBarDirection(BarDirection.BAR);
                ((XDDFBarChartData) chartData).setGapWidth(5);

                yAxis.setCrosses(AxisCrosses.AUTO_ZERO);
                yAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
                chart.plot(chartData);

                //饼图
                XSSFDrawing pieDrawing = sheetAnalysis.createDrawingPatriarch();
                XSSFClientAnchor pieAnchor = pieDrawing.createAnchor(0, 0, 0, 0, reasonNames.size() + 3, sheet2RowIndex +1, reasonNames.size() + 12, sheet2RowIndex *3);
                XSSFChart pieChart = pieDrawing.createChart(pieAnchor);
                pieChart.setTitleText(messageSourceUtil.getMessage("common.report.form.alarmProportion"));
                pieChart.setTitleOverlay(false);
                XDDFChartLegend pieLegend = pieChart.getOrAddLegend();
                pieLegend.setPosition(LegendPosition.TOP_RIGHT);

                XDDFDataSource<String> piecategoryNames = XDDFDataSourcesFactory.fromStringCellRange(sheetAnalysis,
                        new CellRangeAddress(2, sheet2RowIndex - 3, 0, 0));
                XDDFNumericalDataSource<Double> totalAlarmCount = XDDFDataSourcesFactory.fromNumericCellRange(sheetAnalysis,
                        new CellRangeAddress(2, sheet2RowIndex - 3, reasonNames.size() + 2, reasonNames.size() + 2));

                XDDFChartData pieData = pieChart.createData(ChartTypes.PIE, null, null);
                XDDFChartData.Series pieSeries = pieData.addSeries(piecategoryNames, totalAlarmCount);
                pieSeries.setTitle(messageSourceUtil.getMessage("common.report.form.alarmProportion"), null);
                pieSeries.setShowLeaderLines(false);
                CTPlotArea plotArea = pieChart.getCTChart().getPlotArea();
                for (CTPieSer ser : plotArea.getPieChartArray(0).getSerList()){
                    CTBoolean ctBoolean = CTBoolean.Factory.newInstance();
                    ctBoolean.setVal(false);
                    CTDLbls ctdLbls = ser.addNewDLbls();
                    ctdLbls.addNewShowVal().setVal(true);
                    ctdLbls.addNewNumFmt().setFormatCode("#");
                    ctdLbls.setShowBubbleSize(ctBoolean);
                    ctdLbls.setShowCatName(ctBoolean);
                    ctdLbls.setShowLeaderLines(ctBoolean);
                    ctdLbls.setShowLegendKey(ctBoolean);
                    ctdLbls.setShowSerName(ctBoolean);
                    ctdLbls.setShowPercent(ctBoolean);
                }
                pieChart.getCTChart().getTitle().getTx().getRich().getPArray(0).getRArray(0).getRPr().setSz(1800);
                pieChart.plot(pieData);

                for (int i = 0; i < reasonNames.size()+11; i++) {
                    sheetAnalysis.setColumnWidth(i, 12*256);
                }
            }

            // 告警列表sheet
            List<String> keyList = new ArrayList<>(header.keySet());
            List<String> headers = new ArrayList<>();
            for (String key : keyList) {
                headers.add(header.getStr(key));
            }
            XSSFSheet sheet1 = workbook.createSheet(messageSourceUtil.getMessage("common.report.form.alarmList"));

            Row headerRow = sheet1.createRow(0);
            for (int i = 0; i < headers.size(); i++) {
                headerRow.createCell(i).setCellValue(headers.get(i));
            }

            for (int rowIndex = 0; rowIndex < list.size(); rowIndex++) {
                JSONObject rowData = list.getJSONObject(rowIndex);
                Row row = sheet1.createRow(rowIndex + 1);
                for (int colIndex = 0; colIndex < keyList.size(); colIndex++) {
                    String key = keyList.get(colIndex);
                    String value = rowData.getStr(key, null);
                    row.createCell(colIndex).setCellValue(value);
                }
            }

            sheet1.setColumnWidth(2, 25*256);
            sheet1.setColumnWidth(3, 20*256);
            sheet1.setColumnWidth(5, 25*256);
            sheet1.setColumnWidth(6, 20*256);
            sheet1.setColumnWidth(9, 18*256);
            sheet1.setColumnWidth(10, 18*256);
            sheet1.setColumnWidth(12, 18*256);
            sheet1.setColumnWidth(13, 18*256);
            sheet1.setColumnWidth(15, 12*256);
            sheet1.setColumnWidth(16, 30*256);

            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            byte[] bytes = bos.toByteArray();
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("File export operation error",e);
        }
        return null;
    }

}
