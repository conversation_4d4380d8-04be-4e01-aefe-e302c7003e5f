package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 批量控制历史下发命令记录查询参数类
 */
@Data
public class BatchControlCmdHistoryParam {

    private static final Logger log = LoggerFactory.getLogger(BatchControlCmdHistoryParam.class);

    private Date startDate;
    private Date endDate;
    private Integer cmdType;
    private List<Integer> equipmentIdList;

    private String equipmentIds;

    public BatchControlCmdHistoryParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (StringUtils.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate" -> this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "endDate" -> this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "equipmentIds" -> {
                        this.equipmentIdList = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                        if(equipmentIdList != null && equipmentIdList.size() > 0) {
                            equipmentIds = CollUtil.join(equipmentIdList, ",");
                        }
                    }
                    case "cmdType" -> {
                        int tmp = Integer.parseInt(reportParameterPreset.getValue());
                        this.cmdType = tmp != -1 ? tmp : null; //-1表示全部
                    }
                    default -> { }
                }
            }
        } catch (Exception e) {
            log.error("Construct BatchControlCmdHistoryParam throw Exception: ", e);
        }
    }
}
