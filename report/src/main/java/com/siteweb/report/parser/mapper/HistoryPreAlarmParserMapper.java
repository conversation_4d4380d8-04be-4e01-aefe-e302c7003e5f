package com.siteweb.report.parser.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.report.parser.querywrapper.HistoryPreAlarmQueryWrapper;

public interface HistoryPreAlarmParserMapper {

    IPage<PreAlarmHistory> findHistoryDataByPage(Page<PreAlarmHistory> page, HistoryPreAlarmQueryWrapper wrapper);

}
