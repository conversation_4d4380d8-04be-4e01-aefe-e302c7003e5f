package com.siteweb.report.parser.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.vo.ActiveEventReportFilterVO;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Data
public class ActiveEventReportFilterVOBuild {

    private static final Logger log = LoggerFactory.getLogger(ActiveEventReportFilterVOBuild.class);

    public static ActiveEventReportFilterVO build(Collection<ReportParameterPreset> reportParameterPresetList){
        ActiveEventReportFilterVO vo = new ActiveEventReportFilterVO();
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return vo;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        vo.setStartDate(DateUtil.stringToDate(reportParameterPreset.getValue()));
                        break;
                    case "endDate":
                        vo.setEndDate(DateUtil.stringToDate(reportParameterPreset.getValue()));
                        break;
                    case "baseEquipmentIds":
                        vo.setBaseEquipmentId(reportParameterPreset.getValue());
                        break;
                    case "equipmentCategories":
                        vo.setEquipmentCategories(ReportParamParserUtils.jsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_CATEGORY_ID));
                        break;
                    case "equipmentIds":
                        vo.setEquipmentIds(ReportParamParserUtils.jsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID));
                        break;
                    case "eventLevels":
                        vo.setEventSeverityIds(reportParameterPreset.getValue());
                        break;
                    case "eventName":
                        vo.setEventName(reportParameterPreset.getValue());
                        break;
                    case "keyword":
                        vo.setKeyword(reportParameterPreset.getValue());
                        break;
                    case "confirmerIds":
                        vo.setConfirmerIds(ReportParamParserUtils.strToList(reportParameterPreset.getValue()));
                        break;
                    case "eventIds":
                        vo.setEventIds(ReportParamParserUtils.parseEventStrToMap(reportParameterPreset.getValue()));
                        break;
                    case "description":
                        vo.setDescription(reportParameterPreset.getValue());
                        break;
                    case "eventReasonTypes":
                        vo.setEventReasonTypes(ReportParamParserUtils.strToList(reportParameterPreset.getValue()));
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 ActiveEventFilterVOBuild 异常：", e);
            throw e;
        }
        return vo;
    }

    private ActiveEventReportFilterVOBuild() {

    }
}
