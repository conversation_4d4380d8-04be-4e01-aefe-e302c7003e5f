package com.siteweb.report.parser.model;

import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2022/5/21 14:08
 */
@Data
@NoArgsConstructor
public class HistoryComplexIndexReportParam {

    private static final Logger log = LoggerFactory.getLogger(HistoryComplexIndexReportParam.class);

    private Date startTime;
    private Date endTime;
    private String complexIndexIds;
    private String timeGranularity;

    public HistoryComplexIndexReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime":
                        this.startTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endTime":
                        this.endTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "complexIndexIds":
                        this.complexIndexIds = reportParameterPreset.getValue();
                        break;
                    case "timeGranularity":
                        this.timeGranularity = reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 HistoryComplexIndexReportParam 异常：", e);
            throw e;
        }
    }
}
