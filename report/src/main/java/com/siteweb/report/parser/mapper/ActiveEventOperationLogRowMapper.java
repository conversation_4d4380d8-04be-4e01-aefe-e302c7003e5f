package com.siteweb.report.parser.mapper;

import com.siteweb.monitoring.entity.ActiveEventOperationLog;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Author: lzy
 * @Date: 2022/6/18 9:55
 */
public class ActiveEventOperationLogRowMapper implements RowMapper<ActiveEventOperationLog> {
    @Override
    public ActiveEventOperationLog mapRow(ResultSet rs, int rowNum) throws SQLException {
        ActiveEventOperationLog activeEventOperationLog = new ActiveEventOperationLog();
        activeEventOperationLog.setActiveEventOperationLogId(rs.getInt("ActiveEventOperationLogId"));
        activeEventOperationLog.setSequenceId(rs.getString("SequenceId"));
        activeEventOperationLog.setStationId(rs.getInt("StationId"));
        activeEventOperationLog.setEquipmentId(rs.getInt("EquipmentId"));
        activeEventOperationLog.setEventId(rs.getInt("EventId"));
        activeEventOperationLog.setEventConditionId(rs.getInt("EventConditionId"));
        activeEventOperationLog.setStartTime(rs.getTimestamp("StartTime"));
        activeEventOperationLog.setOperatorId(rs.getInt("OperatorId"));
        activeEventOperationLog.setOperation(rs.getString("Operation"));
        activeEventOperationLog.setUserName(rs.getString("UserName"));
        activeEventOperationLog.setOperationTime(rs.getTimestamp("OperationTime"));
        activeEventOperationLog.setDescription(rs.getString("Description"));
        return activeEventOperationLog;
    }
}
