package com.siteweb.report.parser.mapper;

import com.siteweb.report.entity.MonitorUnitPortInfo;
import com.siteweb.report.entity.EquipmentPortInfo;
import com.siteweb.report.entity.EquipmentPortInfoQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/12/24
 */
public interface EquipmentSerialPortInfoMapper {

    List<MonitorUnitPortInfo> findMonitorUnitPortInfo(List<Integer> stationIds, String monitorUnitName, String portName);
    
    /**
     * 获取设备端口详细信息，包括区域层级结构、设备信息、监控单元、端口和采样器等相关信息
     * 
     * @param query 查询参数对象，包含区域层级、设备信息、监控单元、端口等相关查询条件
     * @return 设备端口信息列表
     */
    List<EquipmentPortInfo> findEquipmentPortInfo(@Param("query") EquipmentPortInfoQuery query);
}
