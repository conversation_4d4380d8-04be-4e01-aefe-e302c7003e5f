package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Data
@Slf4j
public class BatteryDischargeRecordParam {

    private static final String EQUIPMENT_ID = "eqId";


    /**
     * 设备基类ID
     */
    private String baseEquipmentId;


    /**
     * 设备id
     */
    private Set<Integer> equipmentIds;


    public BatteryDischargeRecordParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "baseEquipmentId":
                        this.baseEquipmentId = reportParameterPreset.getValue();
                        break;
                    case "equipmentIds":
                        this.equipmentIds = ReportParamParserUtils.jsonToSet(reportParameterPreset.getValue(), EQUIPMENT_ID);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 BatteryDischargeRecordParam 异常：", e);
            throw e;
        }
    }

}
