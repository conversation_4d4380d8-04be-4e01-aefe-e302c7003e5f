package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.dto.EventClassificationStatisticalDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.EventClassificationStatisticalParam;
import com.siteweb.report.service.TotalEventService;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

/**
 * 告警分类统计报表
 *
 * <AUTHOR>
 * Creation Date: 2024/6/18
 */
@Component
public class EventClassificationStatisticalParser extends ReportParser{
    @Autowired
    private TotalEventService totalEventService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    EquipmentService equipmentService;

    private EventClassificationStatisticalParser() {
        super(ReportDataSourceEnum.EVENT_CLASSIFICATION_STATISTICAL.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        // 构造参数
        EventClassificationStatisticalParam param = new EventClassificationStatisticalParam(reportVO.getReportParameterPresetList());
        // 区域权限过滤
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(reportVO.getUserId());
        if (CollUtil.isNotEmpty(equipmentIds)) {
            param.setPermissionEquipmentIds(equipmentIds);
        }
        JSONObject result = new JSONObject(true);
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        result.set(ReportStructureEnum.TITLE.value(),  thJsonObject);
        result.set(ReportStructureEnum.RESULT.value(), getBodyArray(param));
        return result;
    }

    /**
     * 获取内容
     */
    private JSONArray getBodyArray(EventClassificationStatisticalParam param) {
        List<EventClassificationStatisticalDTO> list = totalEventService.findEventClassificationStatistical(param);
        // 获取内容
        JSONArray jsonArray = new JSONArray();
        for (EventClassificationStatisticalDTO eventStatisticsDTO : list) {
            JSONObject tdJsonObject = getBodyObject(eventStatisticsDTO);
            jsonArray.add(tdJsonObject);
        }
        return jsonArray;
    }

    private JSONObject getBodyObject(EventClassificationStatisticalDTO eventStatisticsDTO) {
        JSONObject tdJsonObject = new JSONObject(true);
        tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), eventStatisticsDTO.getBaseEquipmentName());
        tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), eventStatisticsDTO.getEquipmentNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), eventStatisticsDTO.getAllEventNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), eventStatisticsDTO.getAbnormalEventNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), eventStatisticsDTO.getAbnormalHistoryEventNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), eventStatisticsDTO.getAbnormalActiveEventNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), eventStatisticsDTO.getConstructEventNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), eventStatisticsDTO.getConstructHistoryEventNum());
        tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), eventStatisticsDTO.getConstructActiveEventNum());
        return tdJsonObject;
    }

    /**
     * 获取表头
     */
    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.baseEquipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.equipmentCount"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.allAlarm"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.abnormalAlarm"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.abnormalHistoryAlarm"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.abnormalActiveAlarm"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.constructAlarm"));
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.constructHistoryAlarm"));
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.constructActiveAlarm"));
        return defaultColumns;
    }


}
