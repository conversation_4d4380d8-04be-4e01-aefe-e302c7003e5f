package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;

/**
 * 批量控制分组变更报表查询参数类
 */
@Data
public class BatchControlGroupChangeLogParam {

    private static final Logger log = LoggerFactory.getLogger(BatchControlGroupChangeLogParam.class);

    private Date startDate;
    private Date endDate;
    private Integer changeType;

    public BatchControlGroupChangeLogParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (StringUtils.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate" -> this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "endDate" -> this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "changeType" -> {
                        int tmp = Integer.parseInt(reportParameterPreset.getValue());
                        this.changeType = tmp != -1 ? tmp : null; //-1表示全部
                    }
                }
            }
        } catch (Exception e) {
            log.error("Construct BatchControlGroupChangeLogParam throw Exception: ", e);
        }
    }
}
