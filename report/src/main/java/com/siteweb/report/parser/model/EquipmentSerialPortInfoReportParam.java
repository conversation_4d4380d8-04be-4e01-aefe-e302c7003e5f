package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.EquipmentPortInfoQuery;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/12/24
 */
@Data
@Slf4j
public class EquipmentSerialPortInfoReportParam {

    private List<Integer> stationIds;

    // 楼栋IDs (一级区域的层级ID集合）
    private List<Integer> resourceStructureIds;

    // 楼层IDs (二级区域的层级ID集合）
    private List<Integer> floorIds;

    // 房间IDs (三级区域的层级ID集合）
    private List<Integer> roomIds;
    
    // 设备相关字段
    private String equipmentName;
    private Set<Integer> equipmentCategories;
    
    // 监控单元字段
    private String monitorUnitName;
    private String ipAddress;
    
    // 端口字段
    private String portName;
    
    // 采样器相关字段
    private String samplerUnitName;
    
    // 状态和模板字段
    private Integer equipmentState;
    private String equipmentTemplateName;
    // 是否显示ID
    private String displayId;

    public EquipmentSerialPortInfoReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }

        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (StringUtils.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "stationIds" -> this.stationIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.STATION_ID);
                    case "resourceStructureIds" -> this.resourceStructureIds = StringUtils.splitToIntegerList(ReportParamParserUtils.mapJsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.RESOURCESTRUCTURE_ID));
                    case "equipmentName" -> this.equipmentName = reportParameterPreset.getValue();
                    case "equipmentCategories" -> this.equipmentCategories = ReportParamParserUtils.jsonToSet(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_CATEGORY_ID);
                    case "monitorUnitName" -> this.monitorUnitName = reportParameterPreset.getValue();
                    case "ipAddress" -> this.ipAddress = reportParameterPreset.getValue();
                    case "portName" -> this.portName = reportParameterPreset.getValue();
                    case "samplerUnitName" -> this.samplerUnitName = reportParameterPreset.getValue();
                    case "equipmentState" -> this.equipmentState = StringUtils.isBlank(reportParameterPreset.getValue()) ? null : Integer.parseInt(reportParameterPreset.getValue());
                    case "equipmentTemplateName" -> this.equipmentTemplateName = reportParameterPreset.getValue();
                    case "displayId" -> this.displayId = reportParameterPreset.getValue();
                    default -> { }
                }
            }
        } catch (Exception e) {
            log.error("Construct EquipmentSerialPortInfoReportParam throw Exception: ", e);
        }
    }
    
    /**
     * 将参数转换为查询对象
     * @return EquipmentPortInfoQuery
     */
    public EquipmentPortInfoQuery toQuery() {
        return EquipmentPortInfoQuery.builder()
                .resourceStructureIds(this.resourceStructureIds)
                .equipmentName(this.equipmentName)
                .equipmentCategory(this.equipmentCategories)
                .monitorUnitName(this.monitorUnitName)
                .ipAddress(this.ipAddress)
                .portName(this.portName)
                .samplerUnitName(this.samplerUnitName)
                .equipmentState(this.equipmentState)
                .equipmentTemplateName(this.equipmentTemplateName)
                .build();
    }
}
