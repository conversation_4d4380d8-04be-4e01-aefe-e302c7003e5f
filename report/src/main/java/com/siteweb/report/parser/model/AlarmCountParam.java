package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class AlarmCountParam {
    private static final Logger log = LoggerFactory.getLogger(AlarmCountParam.class);

    /**
     * 开始时间
     */
    private LocalDateTime startDate;
    /**
     * 结束时间
     */
    private LocalDateTime endDate;
    /**
     * 告警等级 最高十级 1 一级
     */
    private List<Integer> eventLevelList;
    /**
     * 告警等级以及名称映射
     */
    private Map<Integer, String> eventLevelMap;
    /**
     * 设备类型
     */
    private List<Integer> equipmentCategories;
    /**
     * 设备ID
     */
    private List<Integer> equipmentIds;

    /**
     * 层级
     */
    private List<Integer> resourceStructureIds;
    /**
     * 统计类型 1 房间级(默认) 2 设备级 3 设备类型级
     */
    private Integer statisticType = 1;

    public AlarmCountParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate" -> this.startDate = LocalDateTime.parse(reportParameterPreset.getValue(), DatePattern.NORM_DATETIME_FORMATTER);
                    case "endDate" -> this.endDate = LocalDateTime.parse(reportParameterPreset.getValue(), DatePattern.NORM_DATETIME_FORMATTER);
                    case "statisticType" -> this.statisticType = Integer.parseInt(reportParameterPreset.getValue());
                    case "equipmentIds"-> equipmentIds = StringUtils.splitToIntegerList(ReportParamParserUtils.jsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID));
                    case "resourceStructureIds" -> this.resourceStructureIds = StringUtils.splitToIntegerList(ReportParamParserUtils.mapJsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.RESOURCESTRUCTURE_ID));
                    case "equipmentCategories" -> this.equipmentCategories = StringUtils.splitToIntegerList(ReportParamParserUtils.mapJsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.RESOURCESTRUCTURE_ID));
                    case "eventLevels" ->
                            this.eventLevelList = CharSequenceUtil.isBlank(reportParameterPreset.getValue()) ? List.of() : ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                    default -> log.error("告警统计报表参数不存在，{}", reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName());
                }
            }
        } catch (Exception e) {
            log.error("构建 AlarmCountParam 异常: ", e);
            throw e;
        }
    }
}
