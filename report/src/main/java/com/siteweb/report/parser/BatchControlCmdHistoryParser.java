package com.siteweb.report.parser;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.dto.AirHistoryControlDTO;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.mapper.AirconPageQueryMapper;
import com.siteweb.report.parser.model.BatchControlCmdHistoryParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Component
public class BatchControlCmdHistoryParser extends ReportParser {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private AirconPageQueryMapper airconPageQueryMapper;

    protected BatchControlCmdHistoryParser() {
        super(ReportDataSourceEnum.BATCH_CONTROL_CMD_HISTORY.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        BatchControlCmdHistoryParam reportParam = new BatchControlCmdHistoryParam(reportVO.getReportParameterPresetList());
        List<AirHistoryControlDTO> resultList = new ArrayList<>();

        JSONObject jsonObject = new JSONObject(true);

        Pageable pageable = reportVO.getPageable();
        resultList = findResultByPage(reportParam, pageable, jsonObject);

        // 设置表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());

        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        JSONArray jsonArray = new JSONArray();

        // 设置内容
        JSONObject tdJsonObject;
        for (AirHistoryControlDTO record : resultList) {
            record.setAirStdTypeName(messageSourceUtil);
            record.setControlTypeName(messageSourceUtil);
            record.setControlResultTypeLabel(messageSourceUtil);
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), record.getStationName());
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), record.getHouseName());
            tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), record.getMonitorUnitName());
            tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), record.getEquipmentName());
            tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), record.getAirStdTypeName());
            tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), record.getControlTypeName());
            tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), record.getControlName());
            tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), record.getControlParamValue());
            tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), record.getControlResultTypeLabel());
            tdJsonObject.set(ReportStructureEnum.COLUMN10.value(), record.getStartTime());
            tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), record.getControlExecuterIdName());
            jsonArray.add(tdJsonObject);
        }

        jsonObject.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return jsonObject;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("airConditionControl.report.common.stationName")); // 站点名称
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("airConditionControl.report.common.houseName")); // 机房名称
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("airConditionControl.report.common.monitorUnitName")); // 监控单元名称
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("airConditionControl.report.common.equipmentName")); // 设备名称
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("airConditionControl.report.batchControlCmdHistory.stdAirconType")); // 空调类型
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("airConditionControl.report.batchControlCmdHistory.stdControlCmd")); // 标准控制指令
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("airConditionControl.report.batchControlCmdHistory.controlCmd")); // 实际控制指令
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("airConditionControl.report.batchControlCmdHistory.controlParam")); // 控制参数
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("airConditionControl.report.batchControlCmdHistory.controlResult")); // 控制结果
        defaultColumns.put(ReportStructureEnum.COLUMN10.value(), messageSourceUtil.getMessage("airConditionControl.report.common.operateTime")); // 操作时间
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("airConditionControl.report.common.operator")); // 操作人
        return defaultColumns;
    }


    public List<AirHistoryControlDTO> findResultByPage(BatchControlCmdHistoryParam reportParam, Pageable pageable, JSONObject jsonObject) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (loginUserId == null) {
            return new ArrayList<>();
        }

        Page<AirHistoryControlDTO> page = new Page<>(1, -1);//无page参数，默认取全部
        if (ObjectUtil.isNotNull(pageable)) {
            page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize()); //current从1开始计数
        }

        IPage<AirHistoryControlDTO> result = airconPageQueryMapper.findBatchControlCmdHistoryByPage(page, reportParam);
        if (ObjectUtil.isNotNull(page)) {
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), result.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), result.getTotal());
        }

        return result.getRecords();
    }
}
