package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/27 10:44
 */
@Data
@Slf4j
public class ControlCommandRecordParam {

    /**
     * 设备id
     */
    private List<Integer> equipmentIds;
    /**
     * 控制名称
     */
    private String controlName;

    public ControlCommandRecordParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (StringUtils.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "equipmentIds" -> this.equipmentIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                    case "controlName" -> this.controlName = reportParameterPreset.getValue();
                    default -> { }
                }
            }
        } catch (Exception e) {
            log.error("Construct ControlCommandRecordParam throw Exception: ", e);
        }
    }
}
