package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class RackRecordParam {
    private static final Logger log = LoggerFactory.getLogger(RackRecordParam.class);

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 资源位置
     */
    private String position;

    /**
     * 机架名称
     */
    private String computerRackName;

    /**
     * IT设备名称
     */
    private String iTDeviceName;

    /**
     * 状态 0全部 1上架 2下架
     */
    private Integer operateState;

    /**
     * 层级id
     */
    private List<Integer> resourceStructureIds;

    public RackRecordParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                //预设值为空跳过
                if (CharSequenceUtil.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime" -> this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "endTime" -> this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "position" -> this.position = reportParameterPreset.getValue();
                    case "computerRackName" -> this.computerRackName = reportParameterPreset.getValue();
                    case "iTDeviceName" -> this.iTDeviceName = reportParameterPreset.getValue();
                    case "operateState" -> this.operateState = Integer.valueOf(reportParameterPreset.getValue());
                    default -> log.error("机架统计报表参数不存在");
                }
            }
        } catch (Exception e) {
            log.error("构建 AlarmCountParam 异常: ", e);
            throw e;
        }
    }
}
