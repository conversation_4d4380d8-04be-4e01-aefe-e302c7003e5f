package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.battery.entity.BatteryDischargeRecord;
import com.siteweb.battery.service.BatteryDischargeRecordService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.BatteryDischargeRecordParam;
import com.siteweb.common.util.PredicateUtil;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 电池放电报表
 * <AUTHOR>
 * @date 2023/03/10
 */
@Component
public class BatteryDischargeRecordParser extends ReportParser {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    BatteryDischargeRecordService batteryDischargeRecordService;
    @Autowired
    EquipmentService equipmentService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        BatteryDischargeRecordParam batteryDischargeRecordParam = new BatteryDischargeRecordParam(reportVO.getReportParameterPresetList());
        JSONObject result = new JSONObject(true);
        JSONObject thJsonObject = new JSONObject(true);
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.startTime"));
        thJsonObject.set(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.endTime"));
        // 获取数据
        // 1. 查询当前用户可查看的设备(具有权限)
        if (ObjectUtil.isNotNull(reportVO.getUserId()) && CollUtil.isEmpty(batteryDischargeRecordParam.getEquipmentIds())) {
            Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(reportVO.getUserId());
            batteryDischargeRecordParam.setEquipmentIds(equipmentIds);
        }
        List<BatteryDischargeRecord> batteryDischargeRecord = batteryDischargeRecordService.findFinishedRecordByeqIds(batteryDischargeRecordParam.getEquipmentIds());
        //  当选择多个设备时，返回每个设备最近一次放电记录，增加设备名称显示，单个设备数据获取则不变
        if (CollUtil.isNotEmpty(batteryDischargeRecordParam.getEquipmentIds()) && batteryDischargeRecordParam.getEquipmentIds().size() > 1) {
            batteryDischargeRecord = batteryDischargeRecord.stream().sorted(Comparator.comparing(BatteryDischargeRecord::getStartDischargeTime).reversed())
                    .filter(PredicateUtil.distinctByKey(BatteryDischargeRecord::getEquipmentId)).toList();
        }
        JSONArray jsonArray = new JSONArray();
        JSONObject tdJsonObject;
        for (BatteryDischargeRecord a : batteryDischargeRecord) {
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set("id", a.getBatteryDischargeRecordId());
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), DateUtil.dateToStringAndValidIsNull(a.getStartDischargeTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), DateUtil.dateToStringAndValidIsNull(a.getEndDischargeTime()));
            jsonArray.add(tdJsonObject);
        }
        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        result.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return result;
    }

    protected BatteryDischargeRecordParser() {
        super(ReportDataSourceEnum.BATTERY_DISCHARGERECORD.getValue());
    }

}
