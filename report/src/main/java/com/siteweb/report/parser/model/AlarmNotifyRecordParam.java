package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Slf4j
@Data
public class AlarmNotifyRecordParam {
    /**
     * 告警时间起
     */
    private Date alarmStartTimeFrom;
    /**
     * 告警时间止
     */
    private Date alarmStartTimeTo;
    /**
     * 发送时间起
     */
    private Date sendTimeFrom;
    /**
     * 发送时间止
     */
    private Date sendTimeTo;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 发送方式
     */
    private String sendType;
    /**
     * 发送结果
     */
    private String sendResult;

    public AlarmNotifyRecordParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                //预设值为空跳过
                if (CharSequenceUtil.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "alarmStartTimeFrom" -> this.alarmStartTimeFrom = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "alarmStartTimeTo" -> this.alarmStartTimeTo = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "sendTimeFrom" -> this.sendTimeFrom = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "sendTimeTo" -> this.sendTimeTo = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "receiver" -> this.receiver = reportParameterPreset.getValue();
                    case "sendType" -> this.sendType = reportParameterPreset.getValue();
                    case "sendResult" -> this.sendResult = reportParameterPreset.getValue();
                    default -> log.error("告警通知发送记录参数不存在");
                }
            }
        } catch (Exception e) {
            log.error("构建 AlarmNotifyRecordParam 异常: ", e);
            throw e;
        }
    }
}
