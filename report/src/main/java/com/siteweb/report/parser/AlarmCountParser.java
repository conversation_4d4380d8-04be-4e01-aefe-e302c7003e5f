package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.google.common.collect.Sets;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.alarmCount.AlarmCountEquipmentCategoryParser;
import com.siteweb.report.parser.alarmCount.AlarmCountEquipmentParser;
import com.siteweb.report.parser.model.AlarmCountParam;
import com.siteweb.report.service.TotalEventService;
import com.siteweb.report.vo.EventCount;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.service.CoreEventSeverityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 告警数量统计报表(房间级)
 *
 * <AUTHOR>
 * @date 2022/05/31
 */
@Component
@Slf4j
public class AlarmCountParser extends ReportParser {
    private AlarmCountParser() {
        super(ReportDataSourceEnum.NUMBER_OF_EVENTS.getValue());
    }

    @Autowired
    private TotalEventService totalEventService;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private CoreEventSeverityService coreEventSeverityService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    AlarmCountEquipmentParser alarmCountEquipmentParser;
    @Autowired
    AlarmCountEquipmentCategoryParser alarmCountEquipmentCategoryParser;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        if (CollUtil.isEmpty(reportVO.getReportParameterPresetList())) {
            return resultJsonObject;
        }
        AlarmCountParam alarmCountParam = new AlarmCountParam(reportVO.getReportParameterPresetList());
        // 如果输出参数不为空则走原来的逻辑，否则走新的动态告警等级逻辑
        boolean dynamicFlag = false;
        if (CollUtil.isEmpty(reportVO.getReportExportParameterPresetList())) {
            dynamicFlag = true;
            List<CoreEventSeverity> allCoreEventSeverities = coreEventSeverityService.getAllCoreEventSeverities();
            Map<Integer, String> severityNameMap = allCoreEventSeverities.stream()
                    .collect(Collectors.toMap(CoreEventSeverity::getEventLevel, CoreEventSeverity::getSeverityName));
            List<Integer> eventLevelList = alarmCountParam.getEventLevelList();
            if (CollUtil.isEmpty(eventLevelList)) {
                // 构建告警等级名称映射（新增）
                eventLevelList = allCoreEventSeverities.stream().map(CoreEventSeverity::getEventLevel).toList();
                alarmCountParam.setEventLevelList(eventLevelList);
            }
            // 设置名称映射（仅包含用到的等级）
            Map<Integer, String> filteredSeverityNameMap = eventLevelList.stream()
                    .filter(severityNameMap::containsKey)
                    .collect(Collectors.toMap(
                            Function.identity(),
                            severityNameMap::get,
                            (a, b) -> b,
                            LinkedHashMap::new // 保持顺序
                    ));

            alarmCountParam.setEventLevelMap(filteredSeverityNameMap);
        }
        JSONArray bodyArrayJson = null;
        JSONObject thJsonObject = null;
        switch (alarmCountParam.getStatisticType()) {
            case 1:
                //获取房间级的告警统计
                Set<Integer> resourceStructureIds = resourceStructureManager.getAllChildrenId(alarmCountParam.getResourceStructureIds());
                if (dynamicFlag) {
                    thJsonObject = getTableHeadDynamic(alarmCountParam);
                    bodyArrayJson = this.getTableBodyDynamic(reportVO.getUserId(), alarmCountParam, new ArrayList<>(resourceStructureIds));

                } else {
                    bodyArrayJson = this.getTableBody(reportVO.getUserId(), reportVO.getReportExportParameterPresetList(), alarmCountParam.getStartDate(), alarmCountParam.getEndDate(), new ArrayList<>(resourceStructureIds));
                    thJsonObject = this.getTableHead(reportVO.getReportExportParameterPresetList());
                }
                break;
            case 2:
                //获取设备级的告警统计
                thJsonObject = alarmCountEquipmentParser.getTableHead(alarmCountParam);
                bodyArrayJson = alarmCountEquipmentParser.getTableBody(reportVO.getReportExportParameterPresetList(), alarmCountParam);
                break;
            case 3:
                thJsonObject = alarmCountEquipmentCategoryParser.getTableHead();
                bodyArrayJson = alarmCountEquipmentCategoryParser.getTableBody(alarmCountParam);
                break;
            default:
                log.error("未定义该级别的查询类型,{}", alarmCountParam.getStatisticType());
                break;
        }
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyArrayJson);
        return resultJsonObject;
    }

    /**
     * 获取动态告警等级数据 最高支持十级
     */
    private JSONArray getTableBodyDynamic(Integer userId, AlarmCountParam alarmCountParam, ArrayList<Integer> resourceStructureIds) {
        LocalDateTime startTime = alarmCountParam.getStartDate();
        LocalDateTime endTime = alarmCountParam.getEndDate();
        //有告警信息的层级
        List<Integer> eventResourceStructureIds = totalEventService.findResourceStructureIdBirthDuration(startTime, endTime, resourceStructureIds);
        eventResourceStructureIds = resourceStructureManager.getResourceStructureByIds(eventResourceStructureIds)
                .stream()
                .map(ResourceStructure::getResourceStructureId)
                .toList();
        // 过滤权限
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId);
        // 告警
        List<EventCount> eventCounts = totalEventService.findEventCountInResourceId(eventResourceStructureIds, equipmentIdSet, startTime, endTime);
        List<EventCount> historyEventCounts = totalEventService.findHistoryEventCountInResourceId(eventResourceStructureIds, equipmentIdSet, startTime, endTime);
        // 获取表中数据
        return this.getThBodyDynamic(alarmCountParam.getEventLevelList(), eventCounts, historyEventCounts);
    }

    /**
     * 获取每一行的数据
     *
     * @return {@link JSONObject}
     */
    private JSONArray getTableBody(Integer userId, Collection<ReportExportParameterPreset> reportExportParameterPresetList, LocalDateTime startTime, LocalDateTime endTime, List<Integer> resourceStructureIds) {
        //有告警信息的层级
        List<Integer> eventResourceStructureIds = totalEventService.findResourceStructureIdBirthDuration(startTime, endTime, resourceStructureIds);
        eventResourceStructureIds = resourceStructureManager.getResourceStructureByIds(eventResourceStructureIds)
                .stream()
                .map(ResourceStructure::getResourceStructureId)
                .toList();
        //过滤权限
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId);
        //告警
        List<EventCount> eventCountByResourceType = totalEventService.findEventCountInResourceId(eventResourceStructureIds, equipmentIdSet, startTime, endTime);
        List<EventCount> historyEventCountByResourceType = totalEventService.findHistoryEventCountInResourceId(eventResourceStructureIds, equipmentIdSet, startTime, endTime);
        //所有事件等级
        Set<Integer> eventLevelList = coreEventSeverityService.getCoreEventSeverities()
                .stream()
                .map(CoreEventSeverity::getEventLevel)
                .collect(Collectors.toSet());
        //填充缺少项 层级与告警等级都需补齐
        List<EventCount> eventCounts = this.fillEventLevel(eventCountByResourceType, eventResourceStructureIds, eventLevelList);
        List<EventCount> historyEventCounts = this.fillEventLevel(historyEventCountByResourceType, eventResourceStructureIds, eventLevelList);
        //获取表中数据
        return this.getThBodyDynamic(reportExportParameterPresetList, eventCounts, historyEventCounts);
    }

    /**
     * @param reportExportParameterPresetList 需要显示的列表参数
     * @param eventCounts                     当前活动告警
     * @param historyEventCounts              历史告警
     * @return {@link JSONObject}
     */
    private JSONArray getThBodyDynamic(Collection<ReportExportParameterPreset> reportExportParameterPresetList,
                                       List<EventCount> eventCounts,
                                       List<EventCount> historyEventCounts) {
        JSONArray jsonArray = new JSONArray();
        // 用于存储所有唯一的 resourceStructureId，供后续迭代使用
        Set<Integer> resourceStructureIds = new LinkedHashSet<>();
        // 将 eventCounts 转换为 Map 结构，方便通过 resourceStructureId 和 eventLevel 查找
        Map<Integer, Map<Integer, EventCount>> eventCountMap = new HashMap<>();
        for (EventCount eventCount : eventCounts) {
            eventCountMap.computeIfAbsent(eventCount.getResourceStructureId(), k -> new HashMap<>())
                    .put(eventCount.getEventLevel(), eventCount);
            resourceStructureIds.add(eventCount.getResourceStructureId()); // 收集唯一的 resourceStructureId
        }
        // 将 historyEventCounts 转换为 Map 结构
        Map<Integer, Map<Integer, EventCount>> historyEventCountMap = new HashMap<>();
        for (EventCount eventCount : historyEventCounts) {
            historyEventCountMap.computeIfAbsent(eventCount.getResourceStructureId(), k -> new HashMap<>())
                    .put(eventCount.getEventLevel(), eventCount);
            // 如果 historyEventCounts 中有 eventCounts 中没有的 resourceStructureId，也收集起来
            resourceStructureIds.add(eventCount.getResourceStructureId());
        }
        // 初始化用于保存总计的 JSONObject，并设置总计行的COLUMN1
        JSONObject tableTotalJsonObject = new JSONObject(true);
        tableTotalJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.total"));
        tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(), 0); // 总计的COLUMN2初始化为0
        // 遍历所有唯一的 resourceStructureId 来生成每一行数据并累加总计
        for (Integer currentResourceStructureId : resourceStructureIds) {
            JSONObject thJsonObject = new JSONObject(true);
            String currentResourceStructureName = null;
            if (eventCountMap.containsKey(currentResourceStructureId)) {
                Optional<EventCount> anyEventCount = eventCountMap.get(currentResourceStructureId).values().stream().findFirst();
                if (anyEventCount.isPresent()) {
                    currentResourceStructureName = anyEventCount.get().getResourceStructureName();
                }
            } else if (historyEventCountMap.containsKey(currentResourceStructureId)) {
                Optional<EventCount> anyHistoryEventCount = historyEventCountMap.get(currentResourceStructureId).values().stream().findFirst();
                if (anyHistoryEventCount.isPresent()) {
                    currentResourceStructureName = anyHistoryEventCount.get().getResourceStructureName();
                }
            }
            if (currentResourceStructureName == null) {
                System.err.println("Warning: resourceStructureName not found for ID: " + currentResourceStructureId);
                continue;
            }

            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), currentResourceStructureName);
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), 0);

            Map<Integer, EventCount> currentEventCounts = eventCountMap.get(currentResourceStructureId);
            Map<Integer, EventCount> currentHistoryEventCounts = historyEventCountMap.get(currentResourceStructureId);

            // 处理 "first" 告警级别
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "first")) {
                EventCount currentEvent = (currentEventCounts != null) ? currentEventCounts.get(1) : null;
                EventCount currentHistoryEvent = (currentHistoryEventCounts != null) ? currentHistoryEventCounts.get(1) : null;

                int eventCountVal = (currentEvent != null) ? currentEvent.getCount() : 0;
                int historyEventCountVal = (currentHistoryEvent != null) ? currentHistoryEvent.getCount() : 0;
                int currentLevelTotal = eventCountVal + historyEventCountVal;

                thJsonObject.set(ReportStructureEnum.COLUMN3.value(), currentLevelTotal);
                thJsonObject.set(ReportStructureEnum.COLUMN4.value(), eventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN5.value(), historyEventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN2.value(), thJsonObject.getInt(ReportStructureEnum.COLUMN2.value()) + currentLevelTotal);

                // 累加到总计行
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN3.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN3.value(), 0) + currentLevelTotal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN4.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN4.value(), 0) + eventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN5.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN5.value(), 0) + historyEventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN2.value(), 0) + currentLevelTotal);
            }

            // 处理 "second" 告警级别
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "second")) {
                EventCount currentEvent = (currentEventCounts != null) ? currentEventCounts.get(2) : null;
                EventCount currentHistoryEvent = (currentHistoryEventCounts != null) ? currentHistoryEventCounts.get(2) : null;

                int eventCountVal = (currentEvent != null) ? currentEvent.getCount() : 0;
                int historyEventCountVal = (currentHistoryEvent != null) ? currentHistoryEvent.getCount() : 0;
                int currentLevelTotal = eventCountVal + historyEventCountVal;

                thJsonObject.set(ReportStructureEnum.COLUMN6.value(), currentLevelTotal);
                thJsonObject.set(ReportStructureEnum.COLUMN7.value(), eventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN8.value(), historyEventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN2.value(), thJsonObject.getInt(ReportStructureEnum.COLUMN2.value()) + currentLevelTotal);

                // 累加到总计行
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN6.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN6.value(), 0) + currentLevelTotal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN7.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN7.value(), 0) + eventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN8.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN8.value(), 0) + historyEventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN2.value(), 0) + currentLevelTotal);
            }

            // 处理 "third" 告警级别
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "third")) {
                EventCount currentEvent = (currentEventCounts != null) ? currentEventCounts.get(3) : null;
                EventCount currentHistoryEvent = (currentHistoryEventCounts != null) ? currentHistoryEventCounts.get(3) : null;

                int eventCountVal = (currentEvent != null) ? currentEvent.getCount() : 0;
                int historyEventCountVal = (currentHistoryEvent != null) ? currentHistoryEvent.getCount() : 0;
                int currentLevelTotal = eventCountVal + historyEventCountVal;

                thJsonObject.set(ReportStructureEnum.COLUMN9.value(), currentLevelTotal);
                thJsonObject.set(ReportStructureEnum.COLUMN10.value(), eventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN11.value(), historyEventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN2.value(), thJsonObject.getInt(ReportStructureEnum.COLUMN2.value()) + currentLevelTotal);

                // 累加到总计行
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN9.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN9.value(), 0) + currentLevelTotal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN10.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN10.value(), 0) + eventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN11.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN11.value(), 0) + historyEventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN2.value(), 0) + currentLevelTotal);
            }

            // 处理 "fourth" 告警级别
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "fourth")) {
                EventCount currentEvent = (currentEventCounts != null) ? currentEventCounts.get(4) : null;
                EventCount currentHistoryEvent = (currentHistoryEventCounts != null) ? currentHistoryEventCounts.get(4) : null;

                int eventCountVal = (currentEvent != null) ? currentEvent.getCount() : 0;
                int historyEventCountVal = (currentHistoryEvent != null) ? currentHistoryEvent.getCount() : 0;
                int currentLevelTotal = eventCountVal + historyEventCountVal;

                thJsonObject.set(ReportStructureEnum.COLUMN12.value(), currentLevelTotal);
                thJsonObject.set(ReportStructureEnum.COLUMN13.value(), eventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN14.value(), historyEventCountVal);
                thJsonObject.set(ReportStructureEnum.COLUMN2.value(), thJsonObject.getInt(ReportStructureEnum.COLUMN2.value()) + currentLevelTotal);

                // 累加到总计行
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN12.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN12.value(), 0) + currentLevelTotal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN13.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN13.value(), 0) + eventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN14.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN14.value(), 0) + historyEventCountVal);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(), tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN2.value(), 0) + currentLevelTotal);
            }
            jsonArray.add(thJsonObject); // 将当前资源结构行添加到结果数组
        }
        jsonArray.add(tableTotalJsonObject); // 在所有行生成完毕后，添加总计行
        return jsonArray;
    }


    /**
     * 填充层级或事件等级 避免出现事件等级断层情况
     * 例如:某层级只有1级 2级 4级告警，缺少三级告警，需补齐三级告警为0
     *
     * @return {@link List}<{@link EventCount}>
     */
    private List<EventCount> fillEventLevel(List<EventCount> eventCountList, List<Integer> eventResourceStructureIds, Set<Integer> eventLevelList) {
        if (CollUtil.isEmpty(eventResourceStructureIds)) {
            return Collections.emptyList();
        }
        Map<Integer, List<EventCount>> eventCountMap = eventCountList.stream()
                .collect(Collectors.groupingBy(EventCount::getResourceStructureId));
        //层级差 填充层级
        for (Integer eventResourceStructureId : eventResourceStructureIds) {
            if (ObjectUtil.isNull(eventResourceStructureId)) {
                continue;
            }
            //key(层级)不存在默认补一个一级告警
            eventCountMap.computeIfAbsent(eventResourceStructureId, resourceStructureId -> {
                ResourceStructure resourceStructure = resourceStructureService.findById(resourceStructureId);
                return CollUtil.newArrayList(new EventCount(resourceStructure.getResourceStructureId(), resourceStructure.getResourceStructureName(), 1, 0));
            });
        }
        //所有的事件等级
        for (Map.Entry<Integer, List<EventCount>> integerListEntry : eventCountMap.entrySet()) {
            Set<Integer> resourceEventLevel = integerListEntry.getValue()
                    .stream()
                    .map(EventCount::getEventLevel)
                    .collect(Collectors.toSet());
            //告警等级的差集 需要填充的告警
            Sets.SetView<Integer> eventLevelDiff = Sets.difference(eventLevelList, resourceEventLevel);
            for (Integer eventLevel : eventLevelDiff) {
                EventCount eventCount = integerListEntry.getValue().get(0);
                integerListEntry.getValue()
                        .add(new EventCount(eventCount.getResourceStructureId(), eventCount.getResourceStructureName(), eventLevel, 0));
            }
        }
        return eventCountMap.values()
                .stream()
                .flatMap(Collection::stream)
                .sorted(Comparator.comparing(EventCount::getResourceStructureId).thenComparing(EventCount::getEventLevel))
                .toList();
    }

    /**
     * 根据前端传递的需要的列表头来创建列表头
     *
     * @param reportExportParameterPresetList 需要的列表头
     * @return {@link JSONObject}
     */
    private JSONObject getTableHead(Collection<ReportExportParameterPreset> reportExportParameterPresetList) {
        JSONObject thJsonObject = new JSONObject(true);
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.roomName"));
        thJsonObject.set(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.alarmCount"));
        if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "first")) {
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.oneLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.currentOneLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.historyOneLevelAlarm"));
        }
        if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "second")) {
            thJsonObject.set(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.twoLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.currentTwoLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.historyTwoLevelAlarm"));
        }
        if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "third")) {
            thJsonObject.set(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.threeLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN10.value(), messageSourceUtil.getMessage("common.report.form.currentThreeLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.historyThreeLevelAlarm"));
        }
        if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "fourth")) {
            thJsonObject.set(ReportStructureEnum.COLUMN12.value(), messageSourceUtil.getMessage("common.report.form.fourLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN13.value(), messageSourceUtil.getMessage("common.report.form.currentFourLevelAlarm"));
            thJsonObject.set(ReportStructureEnum.COLUMN14.value(), messageSourceUtil.getMessage("common.report.form.historyFourLevelAlarm"));
        }
        return thJsonObject;
    }

    /**
     * 是否需要展示对应的告警等级
     *
     * @param reportExportParameterPresetList
     * @param level                           告警等级 first 一级告警  second 二级告警 third 三级告警 fourth 四级告警
     * @return {@link Boolean}
     */
    private Boolean isDisplayAlarmLevel(Collection<ReportExportParameterPreset> reportExportParameterPresetList, String level) {
        for (ReportExportParameterPreset reportExportParameterPreset : reportExportParameterPresetList) {
            if (Boolean.TRUE.equals((reportExportParameterPreset.getDisplay())) && Objects.equals(reportExportParameterPreset.getReportSchemaExportParameter().getReportSchemaExportParameterName(), level)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取动态表头 最高支持十级
     */
    private JSONObject getTableHeadDynamic(AlarmCountParam alarmCountParam) {
        List<Integer> eventLevelList = alarmCountParam.getEventLevelList();
        Map<Integer, String> eventLevelMap = alarmCountParam.getEventLevelMap();
        JSONObject thJsonObject = new JSONObject(true);
        // 固定前两列
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.roomName"));
        thJsonObject.set(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.alarmCount"));
        int columnIndex = 3;
        for (Integer level : eventLevelList) {
            if (level == null || level < 1 || level > 10) continue;
            thJsonObject.set(getColumnKey(columnIndex++), eventLevelMap.get(level));         // 一级告警
            thJsonObject.set(getColumnKey(columnIndex++), messageSourceUtil.getMessage("common.report.form.current") + eventLevelMap.get(level)); // 当前一级
            thJsonObject.set(getColumnKey(columnIndex++), messageSourceUtil.getMessage("common.report.form.history") + eventLevelMap.get(level)); // 历史一级
        }
        return thJsonObject;
    }

    private JSONArray getThBodyDynamic(List<Integer> eventLevelList,
                                       List<EventCount> eventCounts,
                                       List<EventCount> historyEventCounts) {
        JSONArray jsonArray = new JSONArray();
        Set<Integer> resourceStructureIds = new LinkedHashSet<>();
        // 当前告警 Map
        Map<Integer, Map<Integer, EventCount>> eventCountMap = new HashMap<>();
        for (EventCount eventCount : eventCounts) {
            eventCountMap.computeIfAbsent(eventCount.getResourceStructureId(), k -> new HashMap<>())
                    .put(eventCount.getEventLevel(), eventCount);
            resourceStructureIds.add(eventCount.getResourceStructureId());
        }
        // 历史告警 Map
        Map<Integer, Map<Integer, EventCount>> historyEventCountMap = new HashMap<>();
        for (EventCount eventCount : historyEventCounts) {
            historyEventCountMap.computeIfAbsent(eventCount.getResourceStructureId(), k -> new HashMap<>())
                    .put(eventCount.getEventLevel(), eventCount);
            resourceStructureIds.add(eventCount.getResourceStructureId());
        }
        // 总计行初始化
        JSONObject tableTotalJsonObject = new JSONObject(true);
        tableTotalJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.total"));
        tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(), 0);
        for (Integer resourceStructureId : resourceStructureIds) {
            JSONObject rowObject = new JSONObject(true);

            String name = Optional.ofNullable(eventCountMap.get(resourceStructureId))
                    .flatMap(map -> map.values().stream().findFirst())
                    .map(EventCount::getResourceStructureName)
                    .orElseGet(() ->
                            Optional.ofNullable(historyEventCountMap.get(resourceStructureId))
                                    .flatMap(map -> map.values().stream().findFirst())
                                    .map(EventCount::getResourceStructureName)
                                    .orElse(null)
                    );
            if (name == null) {
                continue;
            }
            rowObject.set(ReportStructureEnum.COLUMN1.value(), name);
            rowObject.set(ReportStructureEnum.COLUMN2.value(), 0);
            Map<Integer, EventCount> currentMap = eventCountMap.get(resourceStructureId);
            Map<Integer, EventCount> historyMap = historyEventCountMap.get(resourceStructureId);
            int columnIndex = 3;
            for (Integer level : eventLevelList) {
                if (level == null || level < 1 || level > 10) continue;
                EventCount cur = (currentMap != null) ? currentMap.get(level) : null;
                EventCount his = (historyMap != null) ? historyMap.get(level) : null;

                int curCount = (cur != null) ? cur.getCount() : 0;
                int hisCount = (his != null) ? his.getCount() : 0;
                int total = curCount + hisCount;
                // 设置数据列
                rowObject.set(getColumnKey(columnIndex++), total);     // COLUMNx: 总数
                rowObject.set(getColumnKey(columnIndex++), curCount);  // COLUMNx+1: 当前
                rowObject.set(getColumnKey(columnIndex++), hisCount);  // COLUMNx+2: 历史
                // 累加到当前行总数
                rowObject.set(ReportStructureEnum.COLUMN2.value(), rowObject.getInt(ReportStructureEnum.COLUMN2.value()) + total);
                // 累加到总计行
                tableTotalJsonObject.set(getColumnKey(columnIndex - 3),
                        tableTotalJsonObject.getInt(getColumnKey(columnIndex - 3), 0) + total);
                tableTotalJsonObject.set(getColumnKey(columnIndex - 2),
                        tableTotalJsonObject.getInt(getColumnKey(columnIndex - 2), 0) + curCount);
                tableTotalJsonObject.set(getColumnKey(columnIndex - 1),
                        tableTotalJsonObject.getInt(getColumnKey(columnIndex - 1), 0) + hisCount);
                tableTotalJsonObject.set(ReportStructureEnum.COLUMN2.value(),
                        tableTotalJsonObject.getInt(ReportStructureEnum.COLUMN2.value(), 0) + total);
            }

            jsonArray.add(rowObject);
        }

        jsonArray.add(tableTotalJsonObject);
        return jsonArray;
    }


    private String getColumnKey(int index) {
        return String.format("column%d", index);
    }

}
