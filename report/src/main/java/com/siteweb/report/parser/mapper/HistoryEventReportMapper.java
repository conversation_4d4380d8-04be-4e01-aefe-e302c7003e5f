package com.siteweb.report.parser.mapper;

import com.siteweb.monitoring.entity.HistoryEvent;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Author: lzy
 * @Date: 2022/6/6 10:38
 */
public class HistoryEventReportMapper implements RowMapper<HistoryEvent> {
    @Override
    public HistoryEvent mapRow(ResultSet rs, int rowNum) throws SQLException {
        HistoryEvent historyEvent = new HistoryEvent();
        historyEvent.setEventSeverity(rs.getString("EventSeverity"));
        historyEvent.setEquipmentName(rs.getString("EquipmentName"));
        historyEvent.setStationName(rs.getString("StationName"));
        historyEvent.setEquipmentCategoryName(rs.getString("EquipmentCategoryName"));
        historyEvent.setBaseEquipmentName(rs.getString("BaseEquipmentName"));
        historyEvent.setEventName(rs.getString("EventName"));
        historyEvent.setEventValue(rs.getDouble("EventValue"));
        historyEvent.setMeanings(rs.getString("Meanings"));
        historyEvent.setStartTime(rs.getTimestamp("StartTime"));
        historyEvent.setConfirmTime(rs.getTimestamp("ConfirmTime"));
        historyEvent.setConfirmerName(rs.getString("ConfirmerName"));
        historyEvent.setEndTime(rs.getTimestamp("EndTime"));
        historyEvent.setCancelUserName(rs.getString("CancelUserName"));
        historyEvent.setDescription(rs.getString("Description"));
        return historyEvent;
    }
}
