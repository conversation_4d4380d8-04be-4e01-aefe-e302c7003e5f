package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.monitoring.mamager.HistorySignalManager;
import com.siteweb.monitoring.model.HistorySignalGroupByTime;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.enums.TimeGranularityTypeEnum;
import com.siteweb.report.parser.model.HistorySignalReportParam;
import com.siteweb.report.util.ReportUtil;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * 用电统计
 *
 * @Author: lzy
 * @Date: 2022/6/7 16:56
 */
@Component
public class PowerUsageParse extends ReportParser {

    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    EquipmentService equipmentService;
    private static final Map<TimeGranularityTypeEnum, BiConsumer<HistorySignalGroupByTime, HistorySignalGroupByTime>> TIME_GRANULARITY_FUN = new EnumMap<>(TimeGranularityTypeEnum.class);
    // 当type=original时，代表前端使用原始数据样式，否则会根据输出参数拆分成最大值，最小值，平均值。
    private static final String ORIGINAL = "original";

    @Override
    public JSONObject parser(ReportVO reportVO) {
        HistorySignalReportParam params = new HistorySignalReportParam(reportVO.getReportParameterPresetList());

        JSONObject jsonObject = new JSONObject(true);
        JSONArray thJsonArray = new JSONArray();
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(reportVO.getUserId());
        // 图表数据
        JSONArray chartArray = new JSONArray();
        if (CharSequenceUtil.isNotEmpty(params.getSignalIds())) {
            JSONArray jsonNode = JSONUtil.parseArray(params.getSignalIds());
            // 生成报表对象数据源
            List<HistorySignalGroupByTime> historySignalGroupByTimeList = new ArrayList<>();
            for (int i = 0; i < jsonNode.size(); i++) {
                final int index = i;
                JSONObject object = jsonNode.getJSONObject(i);
                Integer equipmentId = object.getInt(ReportParamParserUtils.EQUIPMENT_ID);
                if (!equipmentIdSet.contains(equipmentId)) {
                    continue;
                }
                Integer signalId = object.getInt(ReportParamParserUtils.SIGNALS_ID);
                List<HistorySignalGroupByTime> historyDataList = historySignalManager.findHistorySignalGroupByTime(params.getStartTime(), params.getEndTime(),
                        equipmentId + "." + signalId, "1d", null, false);

                if (CollUtil.isNotEmpty(historyDataList)) {
                    historyDataList.forEach(e -> {
                        e.setSortIndex(index);
                        e.setDisplayValue(e.getPointValue());
                    });
                }

                // 根据时间颗粒度处理需要获取的数据
                historySignalGroupByTimeList.addAll(handlerByTimeGranularity(historyDataList, params.getTimeGranularity()));

                // 由于时间粒度查询有可能某段时间内是没数据的，根据判断temp.count是否为0进行一个过滤
                historyDataList.removeIf(e -> e.count == 0);

                // chartObject，用于前端echarts图例解析
                JSONObject chartObject = new JSONObject(true);
                chartObject.set(ReportParamParserUtils.SIGNALS_ID, object.get(ReportParamParserUtils.SIGNALS_ID));
                chartObject.set(ReportParamParserUtils.SIGNALS_NAME, object.get(ReportParamParserUtils.SIGNALS_NAME));
                chartObject.set(ReportParamParserUtils.EQUIPMENT_NAME, object.get(ReportParamParserUtils.EQUIPMENT_NAME));
                chartObject.set(ReportParamParserUtils.POSITION, object.get(ReportParamParserUtils.POSITION));
                chartObject.set(ReportParamParserUtils.UNIT, object.get(ReportParamParserUtils.UNIT));
                chartObject.set(ReportParamParserUtils.VALUES, historyDataList);
                chartArray.add(chartObject);

                // 报表数据内容
                JSONObject thJsonObject = new JSONObject(true);
                thJsonObject.set(ReportParamParserUtils.EQUIPMENT_NAME, object.get(ReportParamParserUtils.EQUIPMENT_NAME));
                thJsonObject.set(ReportParamParserUtils.POSITION, object.get(ReportParamParserUtils.POSITION));
                thJsonObject.set(ReportParamParserUtils.SIGNALS_NAME, object.get(ReportParamParserUtils.SIGNALS_NAME));
                thJsonObject.set(ReportParamParserUtils.UNIT, object.get(ReportParamParserUtils.UNIT));
                thJsonArray.add(thJsonObject);
            }
            jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonArray);
            jsonObject.set(ReportStructureEnum.RESULT.value(), getPowerUsageResultJsonArray(historySignalGroupByTimeList, jsonNode.size(),reportVO.getLanguage()));
            jsonObject.set(ReportStructureEnum.CHARTS.value(), chartArray);
            // 返回原始数据格式
            jsonObject.set("type", ORIGINAL);
        }
        return jsonObject;
    }

    /**
     * 处理根据时间颗粒度
     * BiConsumer<HistorySignalGroupByTime, HistorySignalGroupByTime> = BiConsumer<当前记录, 下一条记录>
     *
     * @param historyDataList 数据
     * @param timeGranularity 时间颗粒度
     * @return
     */
    private List<HistorySignalGroupByTime> handlerByTimeGranularity(List<HistorySignalGroupByTime> historyDataList, String timeGranularity) {
        if (CollUtil.isEmpty(historyDataList)) {
            return historyDataList;
        }

        TimeGranularityTypeEnum timeGranularityTypeEnum = TimeGranularityTypeEnum.getTimeGranularityTypeEnum(timeGranularity);
        // 根据类型获取处理器
        BiConsumer<HistorySignalGroupByTime, HistorySignalGroupByTime> fun = TIME_GRANULARITY_FUN.get(timeGranularityTypeEnum);
        if (fun == null) {
            return historyDataList;
        }

        int size = historyDataList.size();
        if(size == 1) {
            fun.accept(historyDataList.get(0), null);
        }else {
            for (int i = 0; i <= size - 1; i++) {
                // 最后一个
                if (i + 1 == size) {
                    fun.accept(historyDataList.get(i), null);
                }
                // 当前元素和下一元素【当天和下一天比较】
                else {
                    fun.accept(historyDataList.get(i), historyDataList.get(i + 1));
                }
            }
        }

        return historyDataList;
    }

    /**
     * 处理返回的数据集
     */
    private static JSONArray getPowerUsageResultJsonArray(List<HistorySignalGroupByTime> historyDataPointGroupByTimeList, Integer sortIndexLength,String language) {
        if (CollUtil.isEmpty(historyDataPointGroupByTimeList)) {
            return new JSONArray();
        }
        // 将sortIndexLength个测点的数据进行排序,先按时间排序,再按下标排序
        historyDataPointGroupByTimeList.sort((arg0, arg1) -> arg0.getTime().compareTo(arg1.getTime()) == 0 ? arg0.getSortIndex().compareTo(arg1.getSortIndex()) : arg0.getTime().compareTo(arg1.getTime()));

        JSONArray tdJsonArray = new JSONArray();
        int count = 0;
        String initTime = historyDataPointGroupByTimeList.get(0).getTime();
        while (count < historyDataPointGroupByTimeList.size()) {
            JSONObject tdJsonObject = new JSONObject(true);
            String tempTime = historyDataPointGroupByTimeList.get(count).getTime();

            tdJsonObject.set("time", tempTime);


            JSONArray dataJsonArray = new JSONArray();
            for (int i = 0; i < sortIndexLength; i++) {
                JSONObject dataJsonObject = new JSONObject(true);
                dataJsonObject.set(ReportParamParserUtils.VALUE, "-");
                dataJsonObject.set(ReportParamParserUtils.DISPLAY_VALUE,"-");
                dataJsonArray.add(dataJsonObject);
            }

            // 同个时间范围的归为一列
            while (tempTime.equals(initTime)) {
                Integer sortIndex = historyDataPointGroupByTimeList.get(count).getSortIndex();
                JSONObject objectNode = dataJsonArray.getJSONObject(sortIndex);
                objectNode.set(ReportParamParserUtils.VALUE, NumberUtil.doubleAccuracy(Double.parseDouble(historyDataPointGroupByTimeList.get(count).getPointValue()), 2));
                objectNode.set(ReportParamParserUtils.DISPLAY_VALUE, NumberUtil.doubleAccuracy(Double.parseDouble(historyDataPointGroupByTimeList.get(count).getPointValue()), 2));
                if (++count >= historyDataPointGroupByTimeList.size()) {
                    break;
                }
                tempTime = historyDataPointGroupByTimeList.get(count).getTime();
            }
            tdJsonObject.set("data", dataJsonArray);
            ReportUtil.setRowCount(dataJsonArray, tdJsonObject);
            tdJsonArray.add(tdJsonObject);

            if (count < historyDataPointGroupByTimeList.size()) {
                initTime = tempTime;
            } else {
                break;
            }
        }
        if (!tdJsonArray.isEmpty()) {
            ReportUtil.countColumnJsonArray(tdJsonArray, ReportParamParserUtils.VALUE);
        }
        return tdJsonArray;
    }

    @PostConstruct
    public void init() {
        // 取最老的值
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.START, (first, next) -> first.setPointValue(first.getFirst()));
        // 取最新的值
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.END, (first, next) -> first.setPointValue(first.getLast()));
        // 取最小的值
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.MIN, (first, next) -> first.setPointValue(first.getMin()));
        // 取最大的值
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.MAX, (first, next) -> first.setPointValue(first.getMax()));
        // 两个时间范围内的第一个值相减，使用场景：今天凌晨的日用电量减去昨天凌晨的日用电量，可得出昨天一天的日用电量
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.DIFFERENCE, (first, next) -> {
            if (next == null) {
                // 当前的用量（当前还没过完）
                first.setPointValue(new BigDecimal(first.getLast()).subtract(new BigDecimal(first.getFirst())).toString());
            }else {
                first.setPointValue(new BigDecimal(next.getFirst()).subtract(new BigDecimal(first.getFirst())).toString());
            }
        });
        // 取最大的值
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.SPREAD, (first, next) -> first.setPointValue(first.getSpread()));
        // 取平均值
        TIME_GRANULARITY_FUN.put(TimeGranularityTypeEnum.AVG, (first, next) -> first.setPointValue(first.getAvg()));
    }


    public PowerUsageParse() {
        super(ReportDataSourceEnum.POWER_USAGE.getValue());
    }
}
