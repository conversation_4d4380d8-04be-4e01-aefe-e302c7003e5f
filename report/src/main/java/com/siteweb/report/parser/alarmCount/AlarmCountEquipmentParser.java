package com.siteweb.report.parser.alarmCount;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.dto.HistoryEventStatisticsDTO;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.AlarmCountParam;
import com.siteweb.report.service.TotalEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 告警数量统计报表(设备级)
 * <AUTHOR>
 * @date 2022/05/31
 */
@Component
@Slf4j
public class AlarmCountEquipmentParser {
    /**
     * column常量用于动态生成列头，响应给前端
     */
    private static final String COLUMN = "column";
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private TotalEventService totalEventService;

    /**
     * 获取设备级告警统计列头
     *
     * @param alarmCountParam 报表导出参数预置列表
     * @return {@link JSONObject }
     */
    public JSONObject getTableHead(AlarmCountParam alarmCountParam) {
        JSONObject thJsonObject = new JSONObject(true);
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("eventNotification.activeEvent.eventSeverity"));
        //动态设置时间列头
        LocalDateTime startDateTime = alarmCountParam.getStartDate();
        LocalDateTime endDateTime = alarmCountParam.getEndDate();
        long betweenDay = LocalDateTimeUtil.between(startDateTime, endDateTime, ChronoUnit.DAYS);
        int columnNumber = 1;
        if (betweenDay >= 365) {
            // 按年展示
            int startYear = startDateTime.getYear();
            int endYear = endDateTime.getYear();
            for (int year = startYear; year <= endYear; year++) {
                // 将年份添加到X轴坐标
                columnNumber++;
                thJsonObject.set(COLUMN + columnNumber, year);
            }
        } else if (betweenDay >= 30) {
            // 按月展示
            YearMonth startYearMonth = YearMonth.from(startDateTime);
            YearMonth endYearMonth = YearMonth.from(endDateTime);
            while (startYearMonth.isBefore(endYearMonth) || startYearMonth.equals(endYearMonth)) {
                String yearMonth = startYearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                columnNumber++;
                thJsonObject.set(COLUMN + columnNumber, yearMonth);
                startYearMonth = startYearMonth.plusMonths(1);
            }
        } else {
            // 按日展示
            LocalDate startDate = startDateTime.toLocalDate();
            LocalDate endDate = endDateTime.toLocalDate();
            while (!startDate.isAfter(endDate)) {
                String day = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
                columnNumber++;
                thJsonObject.set(COLUMN + columnNumber, day);
                startDate = startDate.plusDays(1);
            }
        }
        return thJsonObject;
    }

    public JSONArray getTableBody(Collection<ReportExportParameterPreset> reportExportParameterPresetList,
                                  AlarmCountParam alarmCountParam) {
        List<HistoryEventStatisticsDTO> list = totalEventService.findHistoryEventBetweenStartTime(
                alarmCountParam.getStartDate(),
                alarmCountParam.getEndDate(),
                alarmCountParam.getEquipmentIds());

        JSONArray jsonArray = new JSONArray();
        List<Integer> levelList;
        boolean usePresetList = reportExportParameterPresetList != null && !reportExportParameterPresetList.isEmpty();
        // 如果输出参数不为空则走原来的逻辑，否则走新的动态告警等级逻辑
        if (usePresetList) {
            levelList = new ArrayList<>();
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "first")) levelList.add(1);
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "second")) levelList.add(2);
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "third")) levelList.add(3);
            if (this.isDisplayAlarmLevel(reportExportParameterPresetList, "fourth")) levelList.add(4);
        } else {
            levelList = alarmCountParam.getEventLevelList() != null ? alarmCountParam.getEventLevelList() : Collections.emptyList();
        }
        Map<Integer, String> eventLevelMap = alarmCountParam.getEventLevelMap();
        for (Integer level : levelList) {
            if (level == null || level < 1 || level > 10) continue;
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), eventLevelMap.get(level));
            int finalLevel = level;
            groupByStartTime(thJsonObject,
                    alarmCountParam.getStartDate(),
                    alarmCountParam.getEndDate(),
                    list,
                    t -> Objects.equals(t.getEventLevel(), finalLevel));
            jsonArray.add(thJsonObject);
        }

        // 合计行
        JSONObject totalRows = new JSONObject(true);
        totalRows.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.total"));
        for (Object o : jsonArray) {
            JSONObject jsonObject = (JSONObject) o;
            for (int i = 2; i <= jsonObject.size(); i++) {
                String columnName = COLUMN + i;
                Integer value = jsonObject.getInt(columnName);
                Integer current = totalRows.getInt(columnName, 0);
                totalRows.set(columnName, current + (value != null ? value : 0));
            }
        }
        jsonArray.add(totalRows);
        return jsonArray;
    }


    public void groupByStartTime(JSONObject thJsonObject, LocalDateTime start, LocalDateTime end, List<HistoryEventStatisticsDTO> list, Predicate<HistoryEventStatisticsDTO> historyEventStatisticsDTOPredicate) {
        long daysBetween = ChronoUnit.DAYS.between(start, end);
        if (daysBetween > 365) {
            groupByYear(thJsonObject,start, end, list,historyEventStatisticsDTOPredicate);
        } else if (daysBetween > 30) {
            groupByMonth(thJsonObject,start, end, list,historyEventStatisticsDTOPredicate);
        } else {
            groupByDay(thJsonObject,start, end, list,historyEventStatisticsDTOPredicate);
        }
    }

    private void groupByYear(JSONObject thJsonObject, LocalDateTime start, LocalDateTime end, List<HistoryEventStatisticsDTO> list,Predicate<HistoryEventStatisticsDTO> historyEventStatisticsDTOPredicate) {
        Map<Integer, Long> countMap = list.stream()
                                          .filter(historyEventStatisticsDTOPredicate)
                                          .collect(
                                                  Collectors.groupingBy(obj -> obj.getStartTime().getYear(), Collectors.counting()));

        int startYear = start.getYear();
        int endYear = end.getYear();
        int columnNumber = 1;
        for (int year = startYear; year <= endYear; year++) {
            columnNumber++;
            thJsonObject.set(COLUMN + columnNumber, countMap.getOrDefault(year, 0L));
        }
    }

    private void groupByMonth(JSONObject thJsonObject, LocalDateTime start, LocalDateTime end, List<HistoryEventStatisticsDTO> list, Predicate<HistoryEventStatisticsDTO> historyEventStatisticsDTOPredicate) {

        Map<YearMonth, Long> countMap = list.stream()
                                            .filter(historyEventStatisticsDTOPredicate)
                                            .collect(Collectors.groupingBy(obj -> YearMonth.from(obj.getStartTime()), Collectors.counting()));

        YearMonth startMonth = YearMonth.from(start);
        YearMonth endMonth = YearMonth.from(end);
        int columnNumber = 1;
        while (!startMonth.isAfter(endMonth)) {
            columnNumber++;
            thJsonObject.set(COLUMN + columnNumber, countMap.getOrDefault(startMonth, 0L));
            startMonth = startMonth.plusMonths(1);
        }
    }

    private void groupByDay(JSONObject thJsonObject, LocalDateTime start, LocalDateTime end, List<HistoryEventStatisticsDTO> list, Predicate<HistoryEventStatisticsDTO> historyEventStatisticsDTOPredicate) {
        Map<LocalDate, Long> countMap = list.stream()
                                            .filter(historyEventStatisticsDTOPredicate)
                                            .collect(Collectors.groupingBy(obj -> obj.getStartTime().toLocalDate(), Collectors.counting()));

        LocalDate startDate = start.toLocalDate();
        LocalDate endDate = end.toLocalDate();
        int columnNumber = 1;
        while (!startDate.isAfter(endDate)) {
            columnNumber++;
            thJsonObject.set(COLUMN + columnNumber, countMap.getOrDefault(startDate, 0L));
            startDate = startDate.plusDays(1);
        }
    }

    /**
     * 是否需要展示对应的告警等级
     *
     * @param reportExportParameterPresetList
     * @param level 告警等级 first 一级告警  second 二级告警 third 三级告警 fourth 四级告警
     * @return {@link Boolean}
     */
    private Boolean isDisplayAlarmLevel(Collection<ReportExportParameterPreset> reportExportParameterPresetList, String level) {
        for (ReportExportParameterPreset reportExportParameterPreset : reportExportParameterPresetList) {
            if (Boolean.TRUE.equals((reportExportParameterPreset.getDisplay())) && Objects.equals(reportExportParameterPreset.getReportSchemaExportParameter().getReportSchemaExportParameterName(),level)) {
                return true;
            }
        }
        return false;
    }
}
