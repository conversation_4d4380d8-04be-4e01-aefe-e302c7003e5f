package com.siteweb.report.parser.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * @Author: lzy
 * @Date: 2022/5/25 18:59
 */
@Data
public class AlarmMaskLogReportParam {
    private static final Logger log = LoggerFactory.getLogger(AlarmMaskLogReportParam.class);

    /**
     * 操作开始时间
     */
    private Date startDate;
    /**
     * 操作结束时间
     */
    private Date endDate;
    /**
     * 事件id
     */
    private Map<Integer, Set<Integer>> eventIds;
    /**
     * 操作类型（具体可查看 AlarmMaskOperationTypeDTO）
     */
    private Integer operationType;
    /**
     * 屏蔽开始时间
     */
    private Date maskStartDate;
    /**
     * 屏蔽结束时间
     */
    private Date maskEndDate;
    /**
     * 操作人
     */
    private Set<Integer> operatorIds;
    private Set<Integer> equipmentIds;
    public AlarmMaskLogReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "eventIds":
                        this.eventIds = ReportParamParserUtils.parseEventStrToMap(reportParameterPreset.getValue());
                        break;
                    case "operationType":
                        this.operationType = Integer.parseInt(reportParameterPreset.getValue());
                        break;
                    case "maskStartDate":
                        this.maskStartDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "maskEndDate":
                        this.maskEndDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "operatorIds":
                        this.operatorIds = ReportParamParserUtils.strToSet(reportParameterPreset.getValue());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 AlarmMaskLogReportParam 异常：", e);
            throw e;
        }
    }

}
