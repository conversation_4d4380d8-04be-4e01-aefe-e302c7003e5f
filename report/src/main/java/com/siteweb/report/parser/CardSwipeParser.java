package com.siteweb.report.parser;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.report.entity.CardSwipe;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.CardSwipeParam;
import com.siteweb.report.service.CardSwipeService;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
public class CardSwipeParser extends ReportParser{

    @Autowired
    CardSwipeService swapCardRecordService;

    protected CardSwipeParser() {
        super(ReportDataSourceEnum.CARD_SWIPE.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        CardSwipeParam cardSwipeParam = new CardSwipeParam(reportVO.getReportParameterPresetList());
        Pageable pageable = reportVO.getPageable();
        JSONObject resultJsonObject = new JSONObject(true);
        JSONObject thJsonObject = this.getTableHead(reportVO.getReportExportParameterPresetList());
        JSONArray bodyJsonObject = this.getTableBody(cardSwipeParam, resultJsonObject, pageable);
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return resultJsonObject;
    }


    private JSONArray getTableBody(CardSwipeParam cardSwipeParam, JSONObject jsonObject, Pageable pageable) {
        List<CardSwipe> cardSwipeList = swapCardRecordService.getPageCardSwipe(cardSwipeParam, pageable, jsonObject);
        JSONArray jsonArray = new JSONArray();
        for (CardSwipe cardSwipe : cardSwipeList) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), cardSwipe.getEquipmentName());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), cardSwipe.getAreaName());
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), cardSwipe.getCardUserName());
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), cardSwipe.getCardCode());
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), cardSwipe.getCardName());
            thJsonObject.set(ReportStructureEnum.COLUMN6.value(), cardSwipe.getCardGroupName());
            thJsonObject.set(ReportStructureEnum.COLUMN7.value(), cardSwipe.getCardStatusName());
            thJsonObject.set(ReportStructureEnum.COLUMN8.value(), cardSwipe.getDoorNo());
            thJsonObject.set(ReportStructureEnum.COLUMN9.value(), cardSwipe.getEnter());
            thJsonObject.set(ReportStructureEnum.COLUMN10.value(), cardSwipe.getValidName());
            thJsonObject.set(ReportStructureEnum.COLUMN11.value(), cardSwipe.getRecordTime());
            jsonArray.add(thJsonObject);
        }
        return jsonArray;
    }

    private JSONObject getTableHead(Collection<ReportExportParameterPreset> reportExportParameterPresetList) {
        JSONObject thJsonObject = new JSONObject(true);
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), "设备名称");
        thJsonObject.set(ReportStructureEnum.COLUMN2.value(), "区域名称");
        thJsonObject.set(ReportStructureEnum.COLUMN3.value(), "持卡人");
        thJsonObject.set(ReportStructureEnum.COLUMN4.value(), "卡号");
        thJsonObject.set(ReportStructureEnum.COLUMN5.value(), "卡名称");
        thJsonObject.set(ReportStructureEnum.COLUMN6.value(), "卡分组");
        thJsonObject.set(ReportStructureEnum.COLUMN7.value(), "卡状态");
        thJsonObject.set(ReportStructureEnum.COLUMN8.value(), "门号");
        thJsonObject.set(ReportStructureEnum.COLUMN9.value(), "进出门标志");
        thJsonObject.set(ReportStructureEnum.COLUMN10.value(), "刷卡或开门状态");
        thJsonObject.set(ReportStructureEnum.COLUMN11.value(), "刷卡时间");
        return thJsonObject;
    }
}
