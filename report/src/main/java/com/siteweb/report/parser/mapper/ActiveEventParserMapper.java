package com.siteweb.report.parser.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.report.parser.querywrapper.ActiveEventQueryWrapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: shj
 */
public interface ActiveEventParserMapper {
    IPage<ActiveEvent> findActiveEventByPage(@Param("page") Page<ActiveEventQueryWrapper> page, @Param("wrapper") ActiveEventQueryWrapper wrapper);

    /**
     * 查询历史告警的总数
     * 用于分页
     *  由于mybatis-plus的分页插件存在性能问题 固自定义一个查询总数的方法
     * @param wrapper 包装器
     * @return long
     */
    long findActiveEventCount(@Param("wrapper") ActiveEventQueryWrapper wrapper);

}
