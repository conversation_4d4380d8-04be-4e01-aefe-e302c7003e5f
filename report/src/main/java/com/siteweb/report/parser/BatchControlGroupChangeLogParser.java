package com.siteweb.report.parser;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.airconditioncontrol.entity.AirBatchControlGroupChangeLog;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.mapper.AirconPageQueryMapper;
import com.siteweb.report.parser.model.BatchControlGroupChangeLogParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

@Component
public class BatchControlGroupChangeLogParser extends ReportParser {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private AirconPageQueryMapper airconPageQueryMapper;

    protected BatchControlGroupChangeLogParser() {
        super(ReportDataSourceEnum.BATCH_CONTROL_GROUP_CHANGE_LOG.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        BatchControlGroupChangeLogParam reportParam = new BatchControlGroupChangeLogParam(reportVO.getReportParameterPresetList());
        List<AirBatchControlGroupChangeLog> resultList = new ArrayList<>();

        JSONObject jsonObject = new JSONObject(true);

        Pageable pageable = reportVO.getPageable();
        resultList = findResultByPage(reportParam, pageable, jsonObject);

        // 设置表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        JSONArray jsonArray = new JSONArray();

        // 设置内容
        JSONObject tdJsonObject;
        for (AirBatchControlGroupChangeLog record : resultList) {
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), StringUtils.isBlank(record.getGroupNameNew()) ? record.getGroupName() : record.getGroupNameNew());
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), record.getOperateTypeLabel());
            tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), record.getChangeContent());
            tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), Objects.isNull(record.getInsertTime()) ? "" : DateUtil.dateToString(record.getInsertTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), record.getOperator());
            jsonArray.add(tdJsonObject);
        }

        jsonObject.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return jsonObject;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("airConditionControl.report.common.groupName")); // 分组名称
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("airConditionControl.report.common.changeType")); // 变更类型
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("airConditionControl.report.common.detail")); // 变更详情
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("airConditionControl.report.common.changeTime")); // 变更时间
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("airConditionControl.report.common.operator")); // 操作人
        return defaultColumns;
    }


    public List<AirBatchControlGroupChangeLog> findResultByPage(BatchControlGroupChangeLogParam reportParam, Pageable pageable, JSONObject jsonObject) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (loginUserId == null) {
            return new ArrayList<>();
        }

        Page<AirBatchControlGroupChangeLog> page = new Page<>(1, -1);//无page参数，默认取全部
        if (ObjectUtil.isNotNull(pageable)) {
            page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize()); //current从1开始计数
        }

        IPage<AirBatchControlGroupChangeLog> result = airconPageQueryMapper.findBatchControlGroupChangeLogDataByPage(page, reportParam);
        if (ObjectUtil.isNotNull(page)) {
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), result.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), result.getTotal());
        }

        return result.getRecords();
    }
}
