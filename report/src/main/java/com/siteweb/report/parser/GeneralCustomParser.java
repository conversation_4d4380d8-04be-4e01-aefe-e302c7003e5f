package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.HistoryComplexIndexGroupByTime;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.manager.HistoryComplexIndexManager;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.complexindex.service.LiveComplexIndexService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.HistorySignalManager;
import com.siteweb.monitoring.model.HistorySignalGroupByTime;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.TimeGranularityTypeEnum;
import com.siteweb.report.parser.model.GeneralCustomParam;
import com.siteweb.report.util.WorkbookUtil;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * 通用定制报表
 *
 * @Author: lzy
 * @Date: 2022/6/9 10:12
 */
@Component
@SuppressWarnings("all")
public class GeneralCustomParser extends ReportParser {

    private static final String TODAY = "today";
    private static final String LASTDAY = "lastday";
    private static final String WORK_SHEETS = "workSheets";
    private static final String WORK_SHEETS_INDEX = "workSheetIndex";
    private static final String BINDING_ITEMS = "bindingItems";
    private static final String DESCRIPTION = "description";
    private static final String REFERENCE = "reference";
    private static final String BINDINGSTR = "bindingStr";
    private static final String BINDINGTYPE = "bindingType";
    private static final String CUSTOM_REPORT_DOWNLOAD_URL = "customReport";
    private static final String pattern = "yyyyMMdd";

    private static final Logger log = LoggerFactory.getLogger(GeneralCustomParser.class);

    @Autowired
    LiveComplexIndexService liveComplexIndexService;
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    HistoryComplexIndexManager historyComplexIndexManager;
    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    SystemConfigService systemConfigService;
    @Value("${fileserver.rootPath}")
    private String fileserverRootPath;
    private final ThreadLocal<Boolean> unitThreadLocal = ThreadLocal.withInitial(() -> true);
    private final ThreadLocal<Map<String, CellStyle>> accuracyCellStyleMap = ThreadLocal.withInitial(HashMap::new);
    private final ThreadLocal<Map<String, CellStyle>> precisionCellStyleMap = ThreadLocal.withInitial(HashMap::new);
    private static final ThreadLocal<Map<Integer, String>> complexIndexAccuracyMap = new ThreadLocal<>(); // key => 指标id， value => 精度位数
    private static final ThreadLocal<String> headerTimeFormatMonth = new ThreadLocal<>();
    private static final ThreadLocal<String> headerTimeFormatDay = new ThreadLocal<>();
    @Autowired
    ConfigSignalManager configSignalManager;
    @Autowired
    ComplexIndexService complexIndexService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        try {
            GeneralCustomParam params = new GeneralCustomParam(reportVO.getReportParameterPresetList());

            JSONObject jsonObject = new JSONObject(true);
            SystemConfig systemConfigValue = systemConfigService.findBySystemConfigKey("report.generalCustomParser.activeSignal.ignoreUnit");
            unitThreadLocal.set(ObjectUtil.isNotEmpty(systemConfigValue) && ObjectUtil.isNotEmpty(systemConfigValue.getSystemConfigValue()) && Boolean.parseBoolean(systemConfigValue.getSystemConfigValue()));
            complexIndexAccuracyMap.set(new HashMap<>());
            headerTimeFormatMonth.set("MM月");
            headerTimeFormatDay.set("MMdd");
            Optional.ofNullable(systemConfigService.findBySystemConfigKey("report.generalCustomParser.headerTimeFormat.month")).ifPresent(e -> headerTimeFormatMonth.set(e.getSystemConfigValue()));
            Optional.ofNullable(systemConfigService.findBySystemConfigKey("report.generalCustomParser.headerTimeFormat.day")).ifPresent(e -> headerTimeFormatDay.set(e.getSystemConfigValue()));

            if (CharSequenceUtil.isEmpty(params.getUploadExcel()) || CharSequenceUtil.isEmpty(params.getUploadJson())) {
                return jsonObject;
            }

            // 读取excel模板
            File excelFile = new File(fileserverRootPath + "/" + params.getUploadExcel());
            // 读取json文件
            File jsonFile = new File(fileserverRootPath + "/" + params.getUploadJson());
            if (ObjectUtil.isNull(excelFile) || ObjectUtil.isNull(jsonFile)) {
                return jsonObject;
            }

            TimeGranularityTypeEnum timeGranularityEnum = TimeGranularityTypeEnum.getTimeGranularityTypeEnum(params.getTimeGranularity());

            // excel对象
            Workbook workbook = WorkbookUtil.getWorkBook(excelFile);

            if (workbook == null) {
                return jsonObject;
            }

            // json配置文件信息
            JSONObject uploadJson = JSONUtil.parseObj(FileReader.create(jsonFile).readString());
            // excel页
            JSONArray workSheetsConfig = uploadJson.getJSONArray(WORK_SHEETS);

            // 处理单元格绑定数据
            doHandle(workbook, workSheetsConfig, params, timeGranularityEnum);

            // 获取下载地址
            String downLoadUrl = handlerDownLoadUrl(params, uploadJson, reportVO, jsonObject, FileUtil.extName(excelFile));

            // 内容写入excel
            handlerWriteExcelFile(downLoadUrl, workbook);

            return jsonObject;
        }finally {
            unitThreadLocal.remove();
            complexIndexAccuracyMap.remove();
            headerTimeFormatMonth.remove();
            headerTimeFormatDay.remove();
            accuracyCellStyleMap.remove();
            precisionCellStyleMap.remove();
        }
    }

    private void doHandle(Workbook workbook, JSONArray workSheetsConfig, GeneralCustomParam params, TimeGranularityTypeEnum timeGranularityEnum) {
        // 没有其他的逻辑作用，只是为了填充表格日期（例如横向填充日期格式）
        List<Date> excelFormDate = handlerExcelFormDate(timeGranularityEnum, params);

        // 循环excel页
        for (Object o : workSheetsConfig) {
            // 获取当前sheet页待处理的配置信息
            JSONObject workSheetConfig = (JSONObject) o;
            // 获取当前sheet页所在excel具体的index位置
            Integer workSheetsIndex = workSheetConfig.getInt(WORK_SHEETS_INDEX);
            // 获取配置信息中具体单元格绑定信息
            JSONArray bindingItems = workSheetConfig.getJSONArray(BINDING_ITEMS);

            // 真实excel处理对象
            Sheet sheet = workbook.getSheetAt(workSheetsIndex);
            sheet.setForceFormulaRecalculation(true);

            // 循环当前页绑定的单元格配置
            for (Object obj : bindingItems) {
                JSONObject bindingItem = (JSONObject) obj;

                // 单元格备注
                String description = bindingItem.getStr(DESCRIPTION);

                // 填了备注
                if (CharSequenceUtil.isNotEmpty(description)) {
                    switch (description) {
                        case "starttime":
                            startTimeFillDeal(bindingItem, sheet, excelFormDate, timeGranularityEnum);
                            break;
                        case "historydata":
                            queryHistoryDataByType(bindingItem, sheet, params, timeGranularityEnum);
                            break;
                        default:
                            // 下面逻辑基本用不到，v1迁移过来，发现v1的逻辑也是有点问题的，先放着
                            defaultQueryDataByType(sheet, params, bindingItem, timeGranularityEnum);
                            break;
                    }
                } else {
                    // 默认按实时数据处理
                    Cell cell = WorkbookUtil.getCellByReference(sheet, bindingItem.getStr(REFERENCE));
                    if (cell == null) {
                        continue;
                    }
                    WorkbookUtil.setCellValueStringAndDouble(cell, this.queryActiveByType(bindingItem.getInt(BINDINGTYPE), bindingItem.getStr(BINDINGSTR), cell));
                }
            }
        }
    }

    private void defaultQueryDataByType(Sheet sheet, GeneralCustomParam params, JSONObject bindingItem, TimeGranularityTypeEnum typeEnum) {
        Date today;

        // excel位置
        String initialPosition = bindingItem.getStr(REFERENCE).split(":")[0];
        Integer rowVale = WorkbookUtil.getRowValue(initialPosition);
        Integer columnValue = WorkbookUtil.getColumnValue(initialPosition);

        // 备注
        String description = bindingItem.getStr(DESCRIPTION);

        try {
            // 将today替换成开始日期
            if (description.contains(TODAY) || description.contains(LASTDAY) || (description.length() >= 10 && DateUtil.isValidDate(description.substring(0, 10)))) {
                today = getToday(description, params.getQueryStartTime(), params.getQueryEndTime());
            } else if (description.contains("func=")) {
                // 函数解析
                WorkbookUtil.getCell(sheet, rowVale - 1, columnValue - 1).setCellFormula(description.replace("func=", ""));
                return;
            } else {
                return;
            }
        } catch (Exception e) {
            log.error("报表默认解析格式错误：{} => 异常: ", description, e);
            return;
        }

        /**
         * 举例，这里加一天，这时today=2022-9-15，nextDay=2022-9-16，influxdb查询时，是按照 2022-9-15 00:00:00 ~ 2022-9-16 00:00:00 进行查询，这时会丢失16号整天的数据
         * 这里加两天是因为，该方法只是把天数加了一天，没有把时间调整到23:59:59，所以将查询时间调整到 2022-9-15 00:00:00 ~ 2022-9-17 00:00:00
         * influxdb会返回三条计算结果，但是下面只取1、2条记录进行计算操作
         */
        Date nextDay = DateUtil.dateAddDays(today, 2);
        /**
         * 举例不同备注情况下，查询的开始时间(today)和结束时间(nextDay)
         * 举例，报表实例中 startTime为 2022-9-15, endTime为2022-9-17
         * description: 2022-09-15, today = 2022-9-14, nextDay = 2022-9-16
         * description: today,     today = 2022-9-14, nextDay = 2022-9-16
         * description: today-1,   today = 2022-9-13, nextDay = 2022-9-15
         * description: today+1,   today = 2022-9-15, nextDay = 2022-9-17
         * description: lastday,   today = 2022-9-16, nextDay = 2022-9-18
         * description: lastday-1, today = 2022-9-15, nextDay = 2022-9-17
         * description: lastday+1, today = 2022-9-17, nextDay = 2022-9-19
         */
        String bindingStr = bindingItem.getStr(BINDINGSTR);
        String[] bindingStrArr = bindingStr.split("\\.");
        Integer bindingType = bindingItem.getInt(BINDINGTYPE);
        Object cellValue = null;
        Cell cell = WorkbookUtil.getCell(sheet, rowVale - 1, columnValue - 1);
        switch (bindingType) {
            case 1:
                Integer complexIndexId = Integer.parseInt(bindingStrArr[bindingStrArr.length - 1]);
                List<HistoryComplexIndexGroupByTime> historyComplexIndexGroupByTimeList = historyComplexIndexManager.findFullHistoryComplexIndexsGroupByTime(today, nextDay, complexIndexId, "1d");
                if (CollectionUtil.size(historyComplexIndexGroupByTimeList) >= 2) {
                    Double historyComplexIndexValue = this.getHistoryComplexIndexValue(historyComplexIndexGroupByTimeList.get(0), historyComplexIndexGroupByTimeList.get(1), typeEnum);
                    cellValue = this.getDisplayValueByComplexIndexId(complexIndexId, historyComplexIndexValue, cell);
                } else {
                    cellValue = 0d;
                }
                break;
            case 2:
            case 3:
                int equipmentId = Integer.parseInt(bindingStrArr[bindingStrArr.length - 2]);
                int signalId = Integer.parseInt(bindingStrArr[bindingStrArr.length - 1]);
                String eventKey = equipmentId + "." + signalId;
                List<HistorySignalGroupByTime> historySignalGroupByTimeList = historySignalManager.findFullHistorySignalsGroupByTime(today, nextDay, eventKey, "1d");
                if (CollectionUtil.size(historySignalGroupByTimeList) >= 2) {
                    Double historySignalValue = this.getHistorySignalValue(historySignalGroupByTimeList.get(0), historySignalGroupByTimeList.get(1), typeEnum);
                    cellValue = this.getDisplayValue(equipmentId, signalId, historySignalValue, cell);
                } else {
                    cellValue = 0d;
                }
                break;
            default:
                break;
        }
        WorkbookUtil.setCellValueStringAndDouble(cell, cellValue);
    }

    /**
     * 历史信号/指标处理逻辑
     * 因为时间颗粒中有计算差值的选项，所以针对这种情况，所有的历史信号/指标查询的`开始时间都会减一天`、`结束时间都会加一天`
     * 在 getHistoryComplexIndexValue()/getHistorySignalValue() 方法中，next就是开始时间+1天查询出来的数据（需要返回给前端的数据）
     * @param bindingItem
     * @param sheet
     * @param params
     * @param typeEnum
     */
    private void queryHistoryDataByType(JSONObject bindingItem, Sheet sheet, GeneralCustomParam params, TimeGranularityTypeEnum typeEnum) {
        List<String> monthDate = getMonthDate(params, typeEnum);

        // 绑定内容
        String bindingStr = bindingItem.getStr(BINDINGSTR);
        String[] bindingStrArr = bindingStr.split("\\.");
        // 绑定类型
        Integer bindingType = bindingItem.getInt(BINDINGTYPE);
        String initialPosition = bindingItem.getStr(REFERENCE).split(":")[0];
        // 绑定位置
        Integer rowVale = WorkbookUtil.getRowValue(initialPosition) - 1;
        Integer columnValue = WorkbookUtil.getColumnValue(initialPosition) - 1;

        switch (bindingType) {
            case 1:
                Integer complexIndexId = Integer.parseInt(bindingStrArr[bindingStrArr.length - 1]);
                // 指标
                List<HistoryComplexIndexGroupByTime> historyComplexIndexGroupByTimeList = historyComplexIndexManager.findFullHistoryComplexIndexsGroupByTime(params.getQueryStartTime(), params.getQueryEndTime(), complexIndexId, "1d");
                Double historyComplexIndexValue = null;
                Double historyComplexIndexValuetotal = 0d;
                /**
                 * -2 是因为在查询的是时候是根据 开始时间-1天,结束时间+1天 来查询的，
                 * 下面每次遍历都便利当前条和下一条的数据，又因为上面是加了两天并且根据天分组查询的，所以为了不处理 （开始时间-1） 和 （结束时间+1） 的数据，需要少循环两次
                 * 例如查询 06-01~06-03 的数据，这是上面为了兼容计算差值的场景，所以需要获取前天和后一天的数据才可计算出差值，所以统一对开始时间-1和结束时间+1来处理
                 * 所以下面会循环 05-30~06-04 的数据，为了避免在不需要计算差值的场景下，所以 getHistoryComplexIndexValue() 方法中大部分逻辑都是使用next来进行处理，因为 05-30的next就是06-01的数据（这才用户需要展示的数据）
                 */
                for (int historyComplexIndex = 0; historyComplexIndex < historyComplexIndexGroupByTimeList.size() - 2; historyComplexIndex++, columnValue++) {
                    historyComplexIndexValue = getHistoryComplexIndexValue(historyComplexIndexGroupByTimeList.get(historyComplexIndex), historyComplexIndexGroupByTimeList.get(historyComplexIndex + 1), typeEnum);
                    historyComplexIndexValuetotal += Optional.ofNullable(historyComplexIndexValue).orElse(0d);
                    Cell cell = WorkbookUtil.getCell(sheet, rowVale, columnValue);
                    WorkbookUtil.setCellValueStringAndDouble(cell, this.getDisplayValueByComplexIndexId(complexIndexId, historyComplexIndexValue, cell));
                }
                Cell totalCell = WorkbookUtil.getCell(sheet, rowVale, columnValue);
                WorkbookUtil.setCellValueStringAndDouble(totalCell, this.getDisplayValueByComplexIndexId(complexIndexId, historyComplexIndexValuetotal, totalCell));
                break;
            case 2:
            case 3:
                List<String> monthDates = getMonthDate(params, typeEnum);
                // 信号
                List<HistorySignalGroupByTime> historySignalGroupByTimeList = new ArrayList<>();
                Double historySignalValue = null;
                Double historySignalValueTotal = 0d;
                Integer equipmentId = Integer.valueOf(bindingStrArr[bindingStrArr.length - 2]);
                Integer signalId = Integer.valueOf(bindingStrArr[bindingStrArr.length - 1]);
                String eventKey = equipmentId + "." + signalId;
                if (CollUtil.isNotEmpty(monthDates)) {
                    // 因为数据是递增的，所以用结束时间的值减去开始时间的值即可查询月差值
                    for (int i = 0; i < monthDates.size(); i += 2) {
                        List<HistorySignalGroupByTime> paramList = historySignalManager.findFullHistorySignalsGroupByTime(DateUtil.stringToDate(monthDates.get(i)), DateUtil.stringToDate(monthDates.get(i + 1)), eventKey, "1d");
                        if (CollectionUtil.isNotEmpty(paramList)) {
                            historySignalGroupByTimeList.add(paramList.get(0));
                        } else {
                            HistorySignalGroupByTime historySignalGroupByTime = new HistorySignalGroupByTime();
                            historySignalGroupByTime.setFirst("0.0");
                            historySignalGroupByTime.setLast("0.0");
                            historySignalGroupByTimeList.add(historySignalGroupByTime);
                        }
                    }
                    for (int historydataIndex = 0; historydataIndex < historySignalGroupByTimeList.size(); historydataIndex += 2, columnValue++) {
                        historySignalValue = getHistorySignalValue(historySignalGroupByTimeList.get(historydataIndex), historySignalGroupByTimeList.get(historydataIndex + 1), typeEnum);
                        historySignalValueTotal += historySignalValue;
                        Cell cell = WorkbookUtil.getCell(sheet, rowVale, columnValue, null, CellType.STRING);
                        WorkbookUtil.setCellValueStringAndDouble(cell, this.getDisplayValue(equipmentId, signalId, historySignalValue, cell));
                    }
                } else {
                    historySignalGroupByTimeList = historySignalManager.findFullHistorySignalsGroupByTime(params.getQueryStartTime(), params.getQueryEndTime(), eventKey, "1d");
                    for (int index = 0; index < historySignalGroupByTimeList.size() - 2; index++, columnValue++) {
                        historySignalValue = getHistorySignalValue(historySignalGroupByTimeList.get(index), historySignalGroupByTimeList.get(index + 1), typeEnum);
                        historySignalValueTotal += historySignalValue;
                        Cell cell = WorkbookUtil.getCell(sheet, rowVale, columnValue, null, CellType.STRING);
                        WorkbookUtil.setCellValueStringAndDouble(cell, this.getDisplayValue(equipmentId, signalId, historySignalValue, cell));
                    }
                    if (CollUtil.isEmpty(historySignalGroupByTimeList)) {
                        WorkbookUtil.setCellValueStringAndDouble(WorkbookUtil.getCell(sheet, rowVale, columnValue, null, CellType.STRING), 0d);
                    }
                }
                // 开始时间和结束时间为同一天不统计累计值
                // if (!cn.hutool.core.date.DateUtil.isSameDay(params.getStartTime(), params.getEndTime())) {
                //     WorkbookUtil.setCellValue(WorkbookUtil.getCell(sheet, rowVale, columnValue, null, CellType.NUMERIC), NumberUtil.doubleAccuracy(historySignalValueTotal, 2).toString());
                // }
                break;
        }
    }

    /**
     * 查询实时数据根据类型
     *
     * @param bindingType 1-指标，2/3-信号
     * @param bindingStr  绑定数据
     * @return 实时值（实时指标/实时信号）
     */
    private Object queryActiveByType(int bindingType, String bindingStr, Cell cell) {
        String[] bindStr = bindingStr.split("\\.");
        Object currentValue = null;
        switch (bindingType) {
            case 1:
                // 指标id
                Integer complexIndexId = Integer.parseInt(bindStr[bindStr.length - 1]);
                String currentValueStr = Optional.ofNullable(liveComplexIndexService.getLiveComplexIndex(complexIndexId)).orElse(new LiveComplexIndex()).getCurrentValue();
                if (CharSequenceUtil.isNotEmpty(currentValueStr)) {
                    currentValue = cn.hutool.core.util.NumberUtil.parseNumber(currentValueStr);
                }
                return this.getDisplayValueByComplexIndexId(complexIndexId, currentValue, cell);
            case 2:
            case 3:
                // 设备id和信号id拼接
                Integer equipmentId = Integer.parseInt(bindStr[bindStr.length - 2]);
                Integer signalId = Integer.parseInt(bindStr[bindStr.length - 1]);
                List<SimpleActiveSignal> signals = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, List.of(signalId));
                if (CollUtil.isNotEmpty(signals)) {
                    SimpleActiveSignal signal = signals.get(0);
                    if (CharSequenceUtil.isNotEmpty(signal.getOriginalValue())) {
                        currentValue = cn.hutool.core.util.NumberUtil.parseNumber(signal.getOriginalValue());
                    }
                    return this.getDisplayValue(equipmentId, signalId, currentValue, cell);
                }
                break;
            default:
                break;
        }
        return null;
    }

    /**
     * 写入excel
     *
     * @param downLoadUrl
     * @param workbook
     */
    private void handlerWriteExcelFile(String downLoadUrl, Workbook workbook) {
        File outfile = FileUtil.touch(new File(fileserverRootPath + "/" + CUSTOM_REPORT_DOWNLOAD_URL + downLoadUrl));
        try {
            FileOutputStream stream = FileUtils.openOutputStream(outfile);
            workbook.write(stream);
            stream.close();
        } catch (IOException e) {
            log.error("通用定制写入异常：", e);
        }
    }

    /**
     * 处理excel下载文件名
     *
     * @param param
     * @param uploadJson
     * @param report
     * @return
     */
    private String handlerDownLoadUrl(GeneralCustomParam param, JSONObject uploadJson, ReportVO report, JSONObject jsonObject, String fileExtName) {
        // 1-历史数据（文件名需要 开始时间-结束时间） 2-每日数据（文件名需要 开始时间）
        Integer reportType = getReportType(uploadJson);
        Date startTime = param.getStartTime();
        // 后缀
        String timeSuffix;
        if (reportType == 1) {
            Date endTime = param.getEndTime();
            timeSuffix = DateUtil.dateToStringByPattern(startTime, pattern) + "-" + DateUtil.dateToStringByPattern(endTime, pattern);
        } else if (reportType == 2) {
            timeSuffix = DateUtil.dateToStringByPattern(startTime, pattern);
        } else {
            timeSuffix = DateUtil.dateToStringByPattern(new Date(), pattern);
        }
        String pathSuffix = "/" + report.getReportName().replace("/", "") + timeSuffix + "." + fileExtName;
        jsonObject.set("url", "customReport" + pathSuffix);
        return pathSuffix;
    }

    /**
     * 以绑点为起始单元格，横向日期填充，格式mm-dd
     *
     * @param bindingItem
     * @param sheet
     * @param dayDates
     */
    private void startTimeFillDeal(JSONObject bindingItem, Sheet sheet, List<Date> dayDates, TimeGranularityTypeEnum timeGranularityEnum) {
        String reference = bindingItem.getStr(REFERENCE);
        String initialPosition = reference.split(":")[0];
        // 行和列的位置都是从0开始计算，所以需要-1
        Integer rowVale = WorkbookUtil.getRowValue(initialPosition) - 1;
        Integer columnValue = WorkbookUtil.getColumnValue(initialPosition) - 1;

        // 创建单元格格式
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
        cellStyle.setDataFormat(sheet.getWorkbook().createDataFormat().getFormat(CharSequenceUtil.contains(timeGranularityEnum.getTimeGranularityType(), "month") ? headerTimeFormatMonth.get() : headerTimeFormatDay.get()));
        for (int dayDateIndex = 0; dayDateIndex < dayDates.size(); dayDateIndex++, columnValue++) {
            // 设置单元格为文本格式，避免双击单元格后，数值发生变化问题
            Cell cell = WorkbookUtil.getCell(sheet, rowVale, columnValue);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(dayDates.get(dayDateIndex));
        }
        //WorkbookUtil.setCellValueStringAndDouble(WorkbookUtil.getCell(sheet, rowVale, columnValue, null, CellType.STRING), "总计");
    }

    /**
     * 历史指标值解析，TIME_MIN时间粒度的最小值（可理解为时间粒度内用电量的最先采集到的值）， TIME_MAX时间粒度的最大值（可理解为时间粒度内用电量的最后采集到的值），TIME_AVG时间粒度内采集到的数据的平均值，
     * TIME_START时间粒度内的第一个值，TIME_END时间粒度内的最后一个值，TIME_SPREAD时间粒度内的差值
     * TIME_DIFFERENCE时间粒度内的最大值和最小值的差值（可理解为求时间粒度内用电量的差值，常用于求日用电量，如果能取到第二天最先采集到的值，就用第二天最先采集到的值减去当天最先采集到的值，否则用当天最后采集到的值减去当天最先采集到的值）
     *
     * @param current
     * @param next
     * @param typeEnum
     * @return
     */
    private static Double getHistoryComplexIndexValue(HistoryComplexIndexGroupByTime current, HistoryComplexIndexGroupByTime next, TimeGranularityTypeEnum typeEnum) {
        // 下面使用next赋值，是因为在普通查询的情况下，会根据开始时间的前一天开始查询，所以在正常情况下应该是next查询
        Double historyComplexIndexValue = null;
        switch (typeEnum) {
            case START:
                historyComplexIndexValue = Double.parseDouble(next.getFirst());
                break;
            case END:
                historyComplexIndexValue = Double.parseDouble(next.getLast());
                break;
            case SPREAD:
                historyComplexIndexValue = Double.parseDouble(next.getSpread());
                break;
            case MIN:
                historyComplexIndexValue = Double.parseDouble(next.getMin());
                break;
            case MAX:
                historyComplexIndexValue = Double.parseDouble(next.getMax());
                break;
            case AVG:
                historyComplexIndexValue = Double.parseDouble(next.getIndexValue());
                break;
            case DIFFERENCE:
                Double nextDateEnd = "0.0".equals(next.getMin()) ? Double.parseDouble(current.getMax()) : Double.parseDouble(next.getMin());
                Double nextDateStart = Double.parseDouble(current.getMin());
                historyComplexIndexValue = nextDateEnd - nextDateStart;
                break;
            case MAX_DIFFERENCE:
                historyComplexIndexValue = Double.parseDouble(current.getMax()) - Double.parseDouble(next.getMax());
            default:
                break;
        }
        return historyComplexIndexValue;
    }

    /**
     * 历史测点值解析，时间粒度解析原理同上
     *
     * @param current
     * @param next
     * @param typeEnum
     * @return
     */
    private static Double getHistorySignalValue(HistorySignalGroupByTime current, HistorySignalGroupByTime next, TimeGranularityTypeEnum typeEnum) {
        Double historyDataPointValue = null;
        switch (typeEnum) {
            case START:
                historyDataPointValue = Double.parseDouble(next.getFirst());
                break;
            case END:
                historyDataPointValue = Double.parseDouble(next.getLast());
                break;
            case SPREAD:
                historyDataPointValue = Double.parseDouble(next.getSpread());
                break;
            case MIN:
                historyDataPointValue = Double.parseDouble(next.getMin());
                break;
            case MAX:
                historyDataPointValue = Double.parseDouble(next.getMax());
                break;
            case AVG:
                historyDataPointValue = Double.parseDouble(next.getAvg());
                break;
            case DIFFERENCE:
                Double nextDateEnd = "0.0".equals(next.getMin()) ? Double.parseDouble(current.getMax()) : Double.parseDouble(next.getMin());
                Double nextDateStart = Double.parseDouble(current.getMin());
                historyDataPointValue = nextDateEnd - nextDateStart;
                break;
            case MAX_DIFFERENCE:
                if ("0.00".equals(next.getMax())) {
                    historyDataPointValue = 0d;
                } else {
                    Double start = Double.parseDouble(current.getMax());
                    Double end = Double.parseDouble(next.getMax());
                    historyDataPointValue = end - start;
                }
                break;
            case MONTH_DIFFERENCE:
                Double firstValue = Double.parseDouble(current.getFirst());
                Double lastValue = Double.parseDouble(next.getLast());
                historyDataPointValue = lastValue - firstValue;
                break;
            default:
                break;
        }
        return NumberUtil.doubleAccuracy(historyDataPointValue, 2);
    }

    /**
     * 没有其他的逻辑作用，只是为了获取填充表格的日期（例如横向填充日期格式）
     *
     * @return 返回横向填充日期的日期集合，例如横向填充[0608,0609]
     */
    public List<Date> handlerExcelFormDate(TimeGranularityTypeEnum timeGranularityEnum, GeneralCustomParam params) {
        List<Date> excelFormDate = new ArrayList<>();
        Date startTime = params.getStartTime();
        Date endTime = params.getEndTime();
        DateField dateField;
        params.setQueryStartTime(startTime);
        params.setQueryEndTime(endTime);
        if (timeGranularityEnum != null && CharSequenceUtil.contains(timeGranularityEnum.getTimeGranularityType(), "month")) {
            dateField = DateField.MONTH;
        } else {
            dateField = DateField.DAY_OF_MONTH;
            // 获取开始时间的前一天
            params.setQueryStartTime(DateUtil.dateAddDays(startTime, -1));
            // 获取开始时间的后一天
            params.setQueryEndTime(DateUtil.dateAddDays(endTime, 1));
        }
        cn.hutool.core.date.DateUtil.range(startTime, endTime, dateField).forEach(e -> excelFormDate.add(e.toJdkDate()));
        return excelFormDate;
    }

    /**
     * 开始时间到结束时间内，list[每个月的第一天的开始和结束时间，及每个月的最后最后一天的开始和结束时间]
     *
     * @param param
     * @param timeGranularityEnum
     * @return
     */
    private List<String> getMonthDate(GeneralCustomParam param, TimeGranularityTypeEnum timeGranularityEnum) {
        List<String> monthDates = new ArrayList<>();
        if (CharSequenceUtil.contains(timeGranularityEnum.getTimeGranularityType(), "month")) {
            // 获取开始时间到结束时间的之间的月份
            List<String> monthBetween = DateUtil.getMonthBetween(param.getStartTime(), param.getEndTime(), DateUtil.DETAILPATTERN);
            // 结束日期可能是未来的某一天所以直接查询开始时间的值和结束时间的值，遍历的某个月也有可能是零值取不到的值
            for (String date : monthBetween) {
                // 拿到每个月第一天的开始时间和结束时间，每个月最后一天的开始时间和结束时间
                // 月的第一天
                Date firstDateOfMonth = DateUtil.getFirstDateOfMonth(DateUtil.stringToDate(date));
                DateTime dateTime1 = cn.hutool.core.date.DateUtil.beginOfDay(firstDateOfMonth);
                DateTime dateTime2 = cn.hutool.core.date.DateUtil.endOfDay(firstDateOfMonth);
                // 月的最后一天
                Date lastDateOfMonth = DateUtil.getLastDateOfMonth(DateUtil.stringToDate(date));
                // 最后一天的开始时间
                DateTime dateTime3 = cn.hutool.core.date.DateUtil.beginOfDay(lastDateOfMonth);
                // 最后一天的结束时间
                DateTime dateTime4 = cn.hutool.core.date.DateUtil.endOfDay(lastDateOfMonth);
                if (DateUtil.isSameMonth(lastDateOfMonth, new Date())) {
                    // 是一个月
                    dateTime3 = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
                    dateTime4 = new DateTime();
                }
                monthDates.add(DateUtil.dateToString(dateTime1));
                monthDates.add(DateUtil.dateToString(dateTime2));
                monthDates.add(DateUtil.dateToString(dateTime3));
                monthDates.add(DateUtil.dateToString(dateTime4));
            }
        }
        return monthDates;
    }

    /**
     * 判断解析类型
     *
     * @param uploadJson
     * @return
     */
    private static Integer getReportType(JSONObject uploadJson) {
        // 默认为求实时数据，影响导出格式
        int reportType = 0;
        if (uploadJson.toString().contains("starttime") || uploadJson.toString().contains("historydata")) {
            //求历史测点和历史指标
            reportType = 1;
        } else if (uploadJson.toString().contains(TODAY) || uploadJson.toString().contains(LASTDAY)) {
            //求日用电量，今天凌晨和前一天凌晨值相减，得出前一天的日用电量
            reportType = 2;
        }
        return reportType;
    }

    /**
     * description格式为today+N 或者today-N, 将startTime替换today,并在startTime的基础上加减N天
     *
     * @param description
     * @param startTime
     * @param endTime
     * @return
     */
    private static Date getToday(String description, Date startTime, Date endTime) {
        Date today = null;
        String operator = null;
        String days = null;
        Integer length = description.length();

        if (description.contains(TODAY)) {
            today = DateUtil.dateFirstTimeFormat(startTime != null ? startTime : new Date());
            operator = length > 5 ? description.substring(5, 6) : null;
            days = length > 6 ? description.substring(6, length) : null;
        } else if (description.contains(LASTDAY)) {
            // 考虑到可能会计算差值的场景，所以结束时间往后加了一天（为了计算对比计算差值），又因为如果以结束时间作为需要查询的时间，那么还需另外减一天，所以下面减了两天
            today = DateUtil.dateFirstTimeFormat(endTime != null ? (DateUtil.IsToDay(endTime) ? endTime : DateUtil.dateAddDays(endTime, -2)) : new Date());
            operator = length > 7 ? description.substring(7, 8) : null;
            days = length > 8 ? description.substring(8, length) : null;
        } else if (description.length() >= 10 && DateUtil.isValidDate(description.substring(0, 10))) {
            /**
             * 这里只做整点处理
             * 因为在正常查询的情况下，可能会选择时间颗粒为差值，这时就需要获取到前一天和后一天的数据，又因为这里的时间不是根据页面中报表实例中`params.getStartTime()`的值
             * 取的是由报表工具备注中填写的时间格式，所以这里做 -1天 处理
             * 备注：这里没有做 +1天 处理，是因为+1天不是在这个方法逻辑中做的，具体查看 `defaultQueryDataByType() > nextDay属性`，以默认做了+1天处理
             */
            today = DateUtil.dateAddDays(DateUtil.dateFirstTimeFormat(DateUtil.stringToSimpleDate(description.substring(0, 10))), -1);
            operator = length > 10 ? description.substring(10, 11) : null;
            days = length > 11 ? description.substring(11, length) : null;
        }


        if (checkOperator(operator) && checkDays(days)) {
            if (operator.equals("+")) {
                today = DateUtil.dateAddDays(today, Integer.parseInt(days));
            } else if (operator.equals("-")) {
                today = DateUtil.dateAddDays(today, -Integer.parseInt(days));
            }
        }

        return today;
    }

    /**
     * 检查操作符是否有效
     *
     * @param operator
     * @return
     */
    private static boolean checkOperator(String operator) {
        return operator != null && (operator.equals("-") || operator.equals("+"));
    }

    /**
     * 检查字符串是否为正整数
     *
     * @param days
     * @return
     */
    private static boolean checkDays(String days) {
        Integer day = 0;
        try {
            day = Integer.parseInt(days);
        } catch (Exception e) {
            return false;
        }
        return day > 0;
    }

    private Object getDisplayValue(Integer equipmentId, Integer signalId, Object pointValue, Cell cell) {
        if (Objects.isNull(pointValue)) {
            return null;
        }
        ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, signalId);
        if (Objects.isNull(configSignalItem)) {
            log.error("通用定制报表异常, 找不到设备信号配置, 设备id:{}, 信号id:{}, 原始值:{}", equipmentId, signalId, pointValue);
            return pointValue;
        }
        // 忽略单位
        if (Boolean.TRUE.equals(unitThreadLocal.get())) {
            if (CharSequenceUtil.isNotEmpty(configSignalItem.getShowPrecision())) {
                Map<String, CellStyle> styleMap = precisionCellStyleMap.get();
                String precision = configSignalItem.getShowPrecision();
                CellStyle cellStyle = styleMap.get(precision);
                if (Objects.isNull(cellStyle)) {
                    cellStyle = cell.getSheet().getWorkbook().createCellStyle();
                    cellStyle.setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat(configSignalItem.getShowPrecision()));
                    styleMap.put(precision, cellStyle);
                }
                cell.setCellStyle(cellStyle);
            }
            return pointValue;
        }
        return activeSignalManager.getCurrentValue(configSignalItem,pointValue.toString());
    }

    /**
     * 转换指标值（默认精度为2）
     * @param
     * @param indexValue
     * @param cell
     * @return
     */
    private Object getDisplayValueByComplexIndexId(Integer complexIndexId, Object indexValue, Cell cell) {
        if (Objects.isNull(indexValue)) {
            return null;
        }
        String accuracy = complexIndexAccuracyMap.get().get(complexIndexId);
        if (Objects.isNull(accuracy)) {
            accuracy = "0.00";
            ComplexIndex complexIndex = complexIndexService.findByComplexIndexId(complexIndexId);
            if (Objects.nonNull(complexIndex) && Objects.nonNull(complexIndex.getAccuracy())) {
                accuracy = complexIndex.getAccuracy();
            }
            complexIndexAccuracyMap.get().put(complexIndexId, accuracy);
        }
        // 样式缓存优化
        Map<String, CellStyle> styleMap = accuracyCellStyleMap.get();
        CellStyle cellStyle = styleMap.get(accuracy);
        if (cellStyle == null) {
            cellStyle = cell.getSheet().getWorkbook().createCellStyle();
            cellStyle.setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat(accuracy));
            styleMap.put(accuracy, cellStyle);
        }
        cell.setCellStyle(cellStyle);
        return indexValue;
    }

    private GeneralCustomParser() {
        super(ReportDataSourceEnum.GENERAL_CUSTOM.getValue());
    }
}
