package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Set;

/**
 * @Author: YJY
 * @Date: 2023/9/17 10:44
 */
@Data
@Slf4j
public class EquipmentReportParam {
    /**
     * 设备基类ID
     */
    private String baseEquipmentIds;
    /**
     * 设备类型ids
     */
    private Set<Integer> equipmentCategories;
    /**
     * 层级
     */
    private String resourceStructureIds;

    /**
     * 设备状态
     */
    private Integer equipmentState;

    public EquipmentReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (StringUtils.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "baseEquipmentIds" -> this.baseEquipmentIds = reportParameterPreset.getValue();
                    case "equipmentCategories" -> this.equipmentCategories = ReportParamParserUtils.jsonToSet(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_CATEGORY_ID);
                    case "resourceStructureIds" -> this.resourceStructureIds = ReportParamParserUtils.mapJsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.RESOURCESTRUCTURE_ID);
                    case "equipmentState" -> this.equipmentState = Integer.valueOf(reportParameterPreset.getValue());
                    default -> { }
                }
            }
        } catch (Exception e) {
            log.error("Construct EquipmentReportParam throw Exception: ", e);
        }
    }
}