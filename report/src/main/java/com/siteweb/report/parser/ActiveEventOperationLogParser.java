package com.siteweb.report.parser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.ActiveEventOperationLog;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.ActiveEventOperationLogMapper;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.HistoryEventService;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.ActiveEventOperationLogParam;
import com.siteweb.report.parser.querywrapper.ActiveEventOperationLogWrapper;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 当前告警操作日志
 *
 * @Author: lzy
 * @Date: 2022/5/23 10:11
 */
@Component
public class ActiveEventOperationLogParser extends ReportParser {

    @Autowired
    HistoryEventService historyEventService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    ActiveEventOperationLogMapper activeEventOperationLogMapper;
    @Autowired
    AccountService accountService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        ActiveEventOperationLogParam param = new ActiveEventOperationLogParam(reportVO.getReportParameterPresetList());

        Integer userId = reportVO.getUserId();

        // 1. 查询当前用户可查看的设备(具有权限)
        if (ObjectUtil.isNotNull(userId) && CollUtil.isEmpty(param.getEquipmentIds())) {
            Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
            param.setEquipmentIds(equipmentIds);
        }
        //部门权限过滤
        if (ObjectUtil.isNotNull(userId) && CollUtil.isEmpty(param.getOperatorIds())) {
            List<Integer> operatorIds = new ArrayList<>(accountService.findUserIdByPermission(userId));
            param.setOperatorIds(operatorIds);
        }
        // 2. 查询当前告警操作日志
        List<ActiveEventOperationLog> liveEventOperationLogList = findByReportParam(param.getEquipmentIds(), param.getEventIds(), param.getOperatorIds(), param.getOperation(), param.getStartDate(), param.getEndDate());
        JSONArray jsonArray = new JSONArray();
        if (CollUtil.isNotEmpty(liveEventOperationLogList)) {
            setJsonArray(liveEventOperationLogList, jsonArray);
        }

        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        JSONObject result = new JSONObject(true);
        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        result.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return result;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.eventSeverity"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.eventName"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.eventValue"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.operationTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.startTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.confirmTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN10.value(), messageSourceUtil.getMessage("common.report.form.endTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.operatorName"));
        defaultColumns.put(ReportStructureEnum.COLUMN12.value(), messageSourceUtil.getMessage("common.report.form.operationType"));
        defaultColumns.put(ReportStructureEnum.COLUMN13.value(), messageSourceUtil.getMessage("common.report.form.operationContent"));
        return defaultColumns;
    }


    private void setJsonArray(List<ActiveEventOperationLog> liveEventOperationLogList, JSONArray jsonArray) {
        for (ActiveEventOperationLog activeEventOperationLogReport : liveEventOperationLogList) {
            JSONObject tdJsonObject;
            ActiveEvent activeEvent = activeEventOperationLogReport.getActiveEvent();
            if (Objects.nonNull(activeEvent)) {
                tdJsonObject = setByActiveEvent(activeEvent, activeEventOperationLogReport);
                jsonArray.add(tdJsonObject);
            } else {
                HistoryEvent historyEvent = activeEventOperationLogReport.getHistoryEvent();
                if (Objects.nonNull(historyEvent)) {
                    tdJsonObject = setByHistoryEvent(historyEvent, activeEventOperationLogReport);
                    jsonArray.add(tdJsonObject);
                }
            }
        }
    }

    /**
     * 获取活动告警
     */
    private JSONObject setByActiveEvent(ActiveEvent activeEvent, ActiveEventOperationLog activeEventOperationLog) {
        JSONObject tdJsonObject = new JSONObject(true);
        tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), activeEvent.getEventSeverity());
        tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), activeEvent.getEquipmentName());
        tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
        tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), activeEvent.getEquipmentCategoryName());
        tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), activeEvent.getEventName());
        tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), ReportParamParserUtils.getTwoDecimal(activeEvent.getEventValue()));
        tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), DateUtil.dateToStringAndValidIsNull(activeEventOperationLog.getOperationTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), DateUtil.dateToStringAndValidIsNull(activeEventOperationLog.getStartTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), DateUtil.dateToStringAndValidIsNull(activeEvent.getConfirmTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN10.value(), DateUtil.dateToStringAndValidIsNull(activeEvent.getEndTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), Optional.ofNullable(activeEventOperationLog.getUserName()).orElse(""));
        tdJsonObject.set(ReportStructureEnum.COLUMN12.value(), activeEventOperationLog.getOperation());
        tdJsonObject.set(ReportStructureEnum.COLUMN13.value(), activeEventOperationLog.getDescription());
        return tdJsonObject;
    }

    /**
     * 获取历史告警
     */
    private JSONObject setByHistoryEvent(HistoryEvent historyEvent, ActiveEventOperationLog activeEventOperationLog) {
        JSONObject tdJsonObject = new JSONObject(true);
        tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), historyEvent.getEventSeverity());
        tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), historyEvent.getEquipmentName());
        tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), resourceStructureManager.getFullPath(historyEvent.getResourceStructureId()));
        tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), historyEvent.getEquipmentCategoryName());
        tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), historyEvent.getEventName());
        tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), ReportParamParserUtils.getTwoDecimal(historyEvent.getEventValue()));
        tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), DateUtil.dateToStringAndValidIsNull(activeEventOperationLog.getOperationTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), DateUtil.dateToStringAndValidIsNull(historyEvent.getStartTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), DateUtil.dateToStringAndValidIsNull(historyEvent.getConfirmTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN10.value(), DateUtil.dateToStringAndValidIsNull(historyEvent.getEndTime()));
        tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), Optional.ofNullable(activeEventOperationLog.getUserName()).orElse(""));
        tdJsonObject.set(ReportStructureEnum.COLUMN12.value(), activeEventOperationLog.getOperation());
        tdJsonObject.set(ReportStructureEnum.COLUMN13.value(), activeEventOperationLog.getDescription());
        return tdJsonObject;
    }

    /**
     * 条件查询活动告警操作日志
     *
     * @param equipmentIds 设备id
     * @param eventMap     key => equipmetId, value => Set[eventId]
     * @param operatorIds  操作人id
     * @param operation    操作内容
     * @param startDate    开始时间
     * @param endDate      结束时间
     * @return
     */
    List<ActiveEventOperationLog> findByReportParam(Collection<Integer> equipmentIds, Map<Integer, Set<Integer>> eventMap, Collection<Integer> operatorIds,
                                                    String operation, Date startDate, Date endDate) {
        ActiveEventOperationLogWrapper wrapper = new ActiveEventOperationLogWrapper();
        wrapper.setOperation(operation);
        wrapper.setStartDate(startDate);
        wrapper.setEndDate(endDate);
        wrapper.setEquipmentIds(equipmentIds);
        wrapper.setOperatorIds(operatorIds);
        if (CollUtil.isNotEmpty(eventMap)) {
            List<String> entryList = new ArrayList<>();
            for (Map.Entry<Integer, Set<Integer>> entry : eventMap.entrySet()) {
                for (Integer val : entry.getValue()) {
                    entryList.add(String.format("(t1.EquipmentId=%s and t1.EventId=%s)", entry.getKey(), val));
                }
            }
            // 拼接后为 ( (EquipmentId=123 and EventId=456) or (EquipmentId=567 and EventId=789) )
            String sql = "and (" + CollUtil.join(entryList, " or ") + ")";
            wrapper.getSql().add(sql);
        }
        List<ActiveEventOperationLog> activeEventOperationLogs = activeEventOperationLogMapper.findByWrapper(BeanUtil.copyProperties(wrapper, com.siteweb.monitoring.querywrapper.ActiveEventOperationLogWrapper.class));
        activeEventOperationLogs.parallelStream().forEach(e -> {
            // 这里需要注意假数据时间不能一致，这里根据时间、设备id、时间id确认唯一唯一历史告警 TODO 这个地方比较耗时
            e.setHistoryEvent(historyEventService.findByStartTimeAndEquipmentIdAndEventIdAndConditionId(e.getEquipmentId(), e.getEventId(), e.getStartTime(), e.getEventConditionId(), e.getStationId()));
            e.setActiveEvent(activeEventManager.findActionEventBySequenceId(e.getSequenceId()));
        });
        return activeEventOperationLogs;
    }

    private ActiveEventOperationLogParser() {
        super(ReportDataSourceEnum.ACTIVE_EVENTS_LOG.getValue());
    }

}
