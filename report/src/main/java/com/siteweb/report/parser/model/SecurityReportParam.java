package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class SecurityReportParam {
    private static final Logger log = LoggerFactory.getLogger(SecurityReportParam.class);

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 操作人
     */
    private String operator;
    /**
     * 类别
     */
    private List<String> typeList;

    private String clientIp;
    /**
     * 描述
     */
    private String details;

    public SecurityReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                //预设值为空跳过
                if (CharSequenceUtil.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime" -> this.startTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "endTime" -> this.endTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "operator" -> this.operator = reportParameterPreset.getValue();
                    case "details" -> this.details = reportParameterPreset.getValue();
                    case "clientIp" -> this.clientIp = reportParameterPreset.getValue();
                    case "type" -> this.typeList = Arrays.stream(reportParameterPreset.getValue().split(",")).toList();
                    default -> log.error("安全日志记录报表参数不存在");
                }
            }
        } catch (Exception e) {
            log.error("构建 SecurityReportParam 异常: ", e);
            throw e;
        }
    }
}
