package com.siteweb.report.parser.mapper;

import com.siteweb.prealarm.entity.PreAlarmHistory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class HistoryPreAlarmReportMapper  implements RowMapper<PreAlarmHistory> {

    @Override
    public PreAlarmHistory mapRow(ResultSet rs, int rowNum) throws SQLException {
        PreAlarmHistory historyPreAlarm = new PreAlarmHistory();

        historyPreAlarm.setPreAlarmSeverityName(rs.getString("PreAlarmSeverityName"));
        historyPreAlarm.setLevelOfPathName(rs.getString("LevelOfPathName"));
        historyPreAlarm.setObjectName(rs.getString("ObjectName"));
        historyPreAlarm.setPreAlarmCategoryName(rs.getString("PreAlarmCategoryName"));
        historyPreAlarm.setTriggerValue(rs.getString("TriggerValue"));
        historyPreAlarm.setStartTime(rs.getTimestamp("StartTime"));
        historyPreAlarm.setConfirmTime(rs.getTimestamp("ConfirmTime"));
        historyPreAlarm.setEndTime(rs.getTimestamp("EndTime"));
        historyPreAlarm.setConfirmName(rs.getString("ConfirmName"));

        return historyPreAlarm;
    }
}
