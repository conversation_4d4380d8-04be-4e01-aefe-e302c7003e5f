package com.siteweb.report.parser.model;

import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Date;

@Data
@NoArgsConstructor
public class ComputerRackHistoryDataPointReportParam {

    private Date startTime;
    private Date endTime;


    public ComputerRackHistoryDataPointReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime":
                        this.startTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endTime":
                        this.endTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            e.getMessage();
        }
    }
}
