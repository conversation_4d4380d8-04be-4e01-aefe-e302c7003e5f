package com.siteweb.report.parser.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Data
public class CardSwipeParam {

    private static final Logger log = LoggerFactory.getLogger(CardSwipeParam.class);


    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 设备id
     */
    private Collection<Integer> equipmentIds;

    private String cardCode;

    private String cardName;

    private Integer cardGroup;

    private Integer cardStatus;

    private String inOutSigns;

    private String validName;

    private Integer doorAreaId;


    /**
     * 操作人id
     */
    private List<Integer> operatorIds;


    public CardSwipeParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "equipmentIds":
                        this.equipmentIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                        break;
                    case "operatorIds":
                        this.operatorIds = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    case "doorAreaId":
                        this.doorAreaId = Integer.valueOf(reportParameterPreset.getValue());
                        break;
                    case "cardCode":
                        this.cardCode = reportParameterPreset.getValue();
                        break;
                    case "cardName":
                        this.cardName = reportParameterPreset.getValue();
                        break;
                    case "cardGroup":
                        this.cardGroup = Integer.valueOf(reportParameterPreset.getValue());
                        break;
                    case "cardStatus":
                        this.cardStatus = Integer.valueOf(reportParameterPreset.getValue());
                        break;
                    case "inOutSigns":
                        this.inOutSigns = reportParameterPreset.getValue();
                        break;
                    case "validName":
                        this.validName = reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("build CardSwipeParam err：", e);
            throw e;
        }
    }
}
