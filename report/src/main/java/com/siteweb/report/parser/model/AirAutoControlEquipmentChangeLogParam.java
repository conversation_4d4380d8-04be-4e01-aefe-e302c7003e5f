package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 空调群控设备分组变更日志记录查询参数
 */
@Data
public class AirAutoControlEquipmentChangeLogParam {

    public static final String STATION_NODE_ID = "eqId";

    private static final Logger log = LoggerFactory.getLogger(AirAutoControlEquipmentChangeLogParam.class);

    private Date startDate;
    private Date endDate;

    private List<Integer> stationIdList;
    private String stationIds;

    private Integer changeType;

    public AirAutoControlEquipmentChangeLogParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (StringUtils.isBlank(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate" -> this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "endDate" -> this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                    case "stationIds" -> {
                        this.stationIdList = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), "stationId");//第二个参数是与前端约定叫这个的
                        if(stationIdList != null && stationIdList.size() > 0) {
                            stationIds = CollUtil.join(stationIdList, ",");
                        }
                    }
                    case "changeType" -> {
                        int tmp = Integer.parseInt(reportParameterPreset.getValue());
                        this.changeType = tmp != -1 ? tmp : null; //-1表示全部
                    }
                }
            }
        } catch (Exception e) {
            log.error("Construct AirAutoControlEquipmentChangeLogParam throw Exception: ", e);
        }
    }
}
