package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.exception.BigDataException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.HistorySignalManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.report.dto.HistorySignalExportDTO;
import com.siteweb.report.dto.HistorySignalParamEquipmentSignalIdDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.HistorySignalRawDataParam;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史数据(原始数据颗粒度)
 *
 * @Author: lxm
 * @Date: 2023/10/10 14:49
 */
@Component
@Slf4j
public class HistorySignalRawDataParser extends ReportParser {

    protected HistorySignalRawDataParser() {
        super(ReportDataSourceEnum.HISTORY_SIGNAL_RAW_DATA.getValue());
    }
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    ConfigSignalManager configSignalManager;
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    SignalService signalService;
    @Autowired
    SystemConfigService systemConfigService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        HistorySignalRawDataParam param = new HistorySignalRawDataParam(reportVO.getReportParameterPresetList());
        JSONArray bodyJsonObject = this.getTableBody(reportVO.getUserId(), param);
        //创建表头 添加表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return resultJsonObject;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.csv.collectTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.csv.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.csv.signalName"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.csv.signalValue"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.csv.originValue"));
        return defaultColumns;
    }



    private JSONArray getTableBody(Integer userId,HistorySignalRawDataParam param) {
        JSONArray jsonArray = new JSONArray();
        List<String> signalKeyList = this.getSignalKeyList(userId, param);
        if (CollUtil.isEmpty(signalKeyList)) {
            return jsonArray;
        }
        List<HistorySignal> historySignalList = historySignalManager.findHistorySignalBySignalKeys(param.getStartTime(), param.getEndTime(), signalKeyList, param.getSignalTypes());
        Map<String, String> equipmentNameMap = new HashMap<>();
        Map<String, ConfigSignalItem> signalConfigMap = new HashMap<>();
        for (HistorySignal historySignal : historySignalList) {
            JSONObject thJsonObject = new JSONObject(true);
            ConfigSignalItem configSignalItem = this.getConfigSignalItem(signalConfigMap,historySignal.getSignalId());
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), historySignal.getTime());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), this.getEquipmentName(equipmentNameMap,historySignal.getDeviceId()));//设备名称
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), configSignalItem.getSignalName());//信号名称
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), activeSignalManager.getCurrentValue(configSignalItem, historySignal.getPointValue()));//信号含义值
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), historySignal.getPointValue());
            jsonArray.add(thJsonObject);
        }
        return jsonArray;
    }

    private ConfigSignalItem getConfigSignalItem(Map<String, ConfigSignalItem> signalNameMap, String signalKey) {
        return signalNameMap.computeIfAbsent(signalKey,
                key -> {
                    String[] equipmentSignalSplit = signalKey.split("\\.");
                    return configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(Integer.valueOf(equipmentSignalSplit[0]), Integer.valueOf(equipmentSignalSplit[1]));
                });
    }

    private String getEquipmentName(Map<String, String> equipmentNameMap, String equipmentId) {
        return equipmentNameMap.computeIfAbsent(equipmentId,
                key -> Optional.ofNullable(equipmentService.findById(Integer.valueOf(equipmentId)))
                               .map(Equipment::getEquipmentName)
                               .orElse(CharSequenceUtil.EMPTY));
    }

    @Override
    public long countSample(ReportVO reportVO) {
        HistorySignalRawDataParam param = new HistorySignalRawDataParam(reportVO.getReportParameterPresetList());
        List<String> signalKeyList = getSignalKeyList(reportVO.getUserId(), param);
        if (CollUtil.isEmpty(signalKeyList)) {
            return 0;
        }
        if (systemConfigService.signalMaxCount() < signalKeyList.size()) {
            //信号数量超过设置的最大值
            throw new BigDataException("signal too much");
        }
        return historySignalManager.findHistorySignalCountBySignalKeys(param.getStartTime(), param.getEndTime(), signalKeyList, param.getSignalTypes());
    }
    @Override
    public long export(ReportVO reportVO, String filePath) {
        try (CsvWriter writer = CsvUtil.getWriter(Paths.get(filePath).toUri().getPath(), StandardCharsets.UTF_8, false)) {
            HistorySignalRawDataParam param = new HistorySignalRawDataParam(reportVO.getReportParameterPresetList());
            List<String> signalKeyList = this.getSignalKeyList(reportVO.getUserId(), param);
            if (CollUtil.isEmpty(signalKeyList)) {
                writer.writeHeaderLine(messageSourceUtil.getMessage("common.report.csv.collectTime"), messageSourceUtil.getMessage("common.report.csv.equipmentName"), messageSourceUtil.getMessage("common.report.csv.signalName"), messageSourceUtil.getMessage("common.report.csv.signalValue"));
                return 0;
            }
            List<HistorySignal> historySignalList = historySignalManager.findHistorySignalBySignalKeys(param.getStartTime(), param.getEndTime(), signalKeyList, param.getSignalTypes());
            List<HistorySignalExportDTO> exportDTOS = new ArrayList<>(100000);
            Map<String, ConfigSignalItem> signalConfigMap = new HashMap<>();
            for (HistorySignal historySignal : historySignalList) {
                String equipmentName = equipmentService.findById(Integer.valueOf(historySignal.getDeviceId())).getEquipmentName();
                ConfigSignalItem configSignalItem = this.getConfigSignalItem(signalConfigMap, historySignal.getSignalId());
                exportDTOS.add(new HistorySignalExportDTO(historySignal.getTime(),equipmentName,configSignalItem.getSignalName(),historySignal.getPointValue()));
            }
            writer.writeHeaderLine(messageSourceUtil.getMessage("common.report.csv.collectTime"), messageSourceUtil.getMessage("common.report.csv.equipmentName"), messageSourceUtil.getMessage("common.report.csv.signalName"), messageSourceUtil.getMessage("common.report.csv.signalValue"));
            writer.write(exportDTOS);
            return exportDTOS.size();
        }
    }


    /**
     * 获取需要具体查询的信号id集合
     *
     * @param userId 用户id
     * @param param 历史信号原始数据报表参数
     * @return {@link List}<{@link String}>
     */
    public List<String> getSignalKeyList(Integer userId,HistorySignalRawDataParam param){
        List<HistorySignalParamEquipmentSignalIdDTO> querySignalList = null;
        //选择了信号
        if (CharSequenceUtil.isNotBlank(param.getSignalIds())) {
            querySignalList = JSONUtil.toList(param.getSignalIds(), HistorySignalParamEquipmentSignalIdDTO.class);
        } else if (CollUtil.isNotEmpty(param.getBaseTypeIds())) {
            //选择了信号基类
            querySignalList = signalService.findSimpleSignalDTOsByBaseTypeIdsAndEquipmentIds(param.getBaseTypeIds(), param.getEquipmentIds())
                                           .stream()
                                           .map(HistorySignalParamEquipmentSignalIdDTO::new)
                                           .collect(Collectors.toList());
        }
        //信号基类与信号都没选择直接返回空
        if (CollUtil.isEmpty(querySignalList)) {
            return Collections.emptyList();
        }
        //没有权限则移除部分没有权限的设备信号
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIdSet)) {
            return Collections.emptyList();
        }
        querySignalList.removeIf(equipmentSignalId -> !equipmentIdSet.contains(equipmentSignalId.getEquipmentId()));
        return querySignalList.stream()
                              .map(HistorySignalParamEquipmentSignalIdDTO::getSignalKey)
                              .toList();
    }
}

