package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.ControlCommandRecord;
import com.siteweb.monitoring.service.ControlCommandRecordService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.ControlCommandRecordParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

/**
 * ba控制命令记录
 *
 * @Author: lzy
 * @Date: 2023/3/27 9:57
 */
@Component
public class BaControlCommandParser extends ReportParser {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    ControlCommandRecordService controlCommandRecordService;
    @Autowired
    EquipmentService equipmentService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject result = new JSONObject(true);

        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());

        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        ControlCommandRecordParam reportParam = new ControlCommandRecordParam(reportVO.getReportParameterPresetList());
        List<ControlCommandRecord> resultList = findResultByPage(reportParam, reportVO, result);

        JSONArray list = new JSONArray();
        JSONObject tdJsonObject;
        for (ControlCommandRecord record : resultList) {
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), record.getEquipmentId());
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), record.getEquipmentName());
            tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), record.getControlName());
            tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), record.getSetValue());
            tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), record.getStartTime());
            list.add(tdJsonObject);
        }

        result.set(ReportStructureEnum.RESULT.value(), list);
        return result;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("baControlCommand.report.equipmentId"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.controlName"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("baControlCommand.report.controlValue"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.controlTime"));
        return defaultColumns;
    }



    public List<ControlCommandRecord> findResultByPage(ControlCommandRecordParam reportParam, ReportVO reportVO, JSONObject jsonObject) {

        Page<ControlCommandRecord> page = new Page<>(1, -1); // 无page参数，默认取全部
        Pageable pageable = reportVO.getPageable();
        if (ObjectUtil.isNotNull(pageable)) {
            page = new Page<>(pageable.getPageNumber() + 1, pageable.getPageSize()); // current从1开始计数
        }
        LambdaQueryWrapper<ControlCommandRecord> wrapper = Wrappers.lambdaQuery(ControlCommandRecord.class);
        wrapper.in(CollUtil.isNotEmpty(reportParam.getEquipmentIds()), ControlCommandRecord::getEquipmentId, reportParam.getEquipmentIds());
        wrapper.like(CharSequenceUtil.isNotEmpty(reportParam.getControlName()), ControlCommandRecord::getControlName, reportParam.getControlName());
        //设备权限统一过滤
        Set<Integer> allEquipmentIds = equipmentService.findEquipmentIdsByUserId(reportVO.getUserId());
        if (CollUtil.isNotEmpty(allEquipmentIds)) {
            wrapper.in(ControlCommandRecord::getEquipmentId, allEquipmentIds);
        }

        IPage<ControlCommandRecord> result = controlCommandRecordService.findReportListByPage(page, wrapper);
        if (ObjectUtil.isNotNull(page)) {
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), result.getPages());
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), result.getTotal());
        }

        return result.getRecords();
    }

    protected BaControlCommandParser() {
        super(ReportDataSourceEnum.BA_CONTROL_COMMAND.getValue());
    }
}
