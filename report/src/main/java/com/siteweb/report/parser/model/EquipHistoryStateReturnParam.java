package com.siteweb.report.parser.model;

import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class EquipHistoryStateReturnParam {
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 局站Id
     */
    private Integer stationId;
    /**
     * 状态信号列表
     */
    private Integer stateSignal;
    /**
     * 监控单元名称
     */
    private String monitorUnitName;
    /**
     * 群控设备名称
     */
    private String virtualEquipment;


    public EquipHistoryStateReturnParam(String equipmentName, Integer equipmentId, Integer stationId, String stationName, Integer swSignalId, String virtualEquipment, String monitorUnit) {
        this.equipmentName = equipmentName;
        this.equipmentId = equipmentId;
        this.stateSignal = swSignalId;
        this.stationId = stationId;
        this.stationName = stationName;
        this.monitorUnitName = monitorUnit;
        this.virtualEquipment = virtualEquipment;
    }
}
