package com.siteweb.report.parser.model;

import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2022/6/9 10:17
 */
@Data
public class GeneralCustomParam {

    private static final Logger log = LoggerFactory.getLogger(GeneralCustomParam.class);

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 上传excel路径
     */
    private String uploadExcel;
    /**
     * 上传json配置文件路径
     */
    private String uploadJson;
    /**
     * 报表查询时间颗粒度
     */
    private String timeGranularity;
    /**
     * 查询开始时间（startTime - 1天）
     */
    private Date queryStartTime;
    /**
     * 查询结束时间（startTime + 1天）
     */
    private Date queryEndTime;

    public GeneralCustomParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime":
                        this.startTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endTime":
                        this.endTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "uploadExcel":
                        this.uploadExcel = reportParameterPreset.getValue();
                        break;
                    case "uploadJson":
                        this.uploadJson = reportParameterPreset.getValue();
                        break;
                    case "timeGranularity":
                        this.timeGranularity = reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 GeneralCustomParam 异常：", e);
            throw e;
        }
    }

    public GeneralCustomParam() {
    }
}
