package com.siteweb.report.parser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.dto.SecurityReportParamDto;
import com.siteweb.admin.entity.SecurityReport;
import com.siteweb.admin.service.SecurityReportService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.SecurityReportParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class SecurityReportParser extends ReportParser {
    private SecurityReportParser() {
        super(ReportDataSourceEnum.SECURITY_REPORT.getValue());
    }

    @Autowired
    private SecurityReportService securityReportService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        SecurityReportParam securityReportParam = new SecurityReportParam(reportVO.getReportParameterPresetList());
        JSONArray bodyJsonObject = this.getTableBody(securityReportParam);
        //创建表头 添加表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return resultJsonObject;
    }

    private JSONArray getTableBody(SecurityReportParam securityReportParam) {
        SecurityReportParamDto securityReportParamDto = BeanUtil.copyProperties(securityReportParam, SecurityReportParamDto.class);
        List<SecurityReport> auditReportList = securityReportService.findSecurityReport(securityReportParamDto);
        JSONArray jsonArray = new JSONArray();
        for (SecurityReport audit : auditReportList) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), audit.getOperationAccount());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), audit.getTypeName());
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), audit.getClientIp());
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), audit.getDetails());
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), audit.getCreateTime());
            jsonArray.add(thJsonObject);
        }
        return jsonArray;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("security.report.operationAccount"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("security.report.type"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("security.report.clientIp"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("security.report.details"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("security.report.operationTime"));
        return defaultColumns;
    }

}
