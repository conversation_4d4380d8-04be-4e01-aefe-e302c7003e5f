package com.siteweb.report.parser.model;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

@Data
@NoArgsConstructor
@Slf4j
public class EventClassificationStatisticalParam {
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 设备基类
     */
    private List<Integer> baseEquipmentIds;
    /**
     * 设备id
     */
    private Set<Integer> equipmentIds;
    /**
     * 权限设备id
     */
    private Set<Integer> permissionEquipmentIds;


    public EventClassificationStatisticalParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "baseEquipmentIds":
                        baseEquipmentIds = StringUtils.splitToIntegerList(reportParameterPreset.getValue());
                        break;
                    case "equipmentIds":
                        equipmentIds = StringUtils.splitToIntegerCollection(ReportParamParserUtils.jsonToString(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID), HashSet::new);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 EventStatisticsParam 异常：", e);
            throw e;
        }
    }
}
