package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.EventReasonTypeService;
import com.siteweb.monitoring.vo.ActiveEventReportFilterVO;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.mapper.ActiveEventParserMapper;
import com.siteweb.report.parser.model.ActiveEventReportFilterVOBuild;
import com.siteweb.report.parser.querywrapper.ActiveEventQueryWrapper;
import com.siteweb.report.util.EventUtil;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Habits
 * @time: 2022/5/11 10:03
 * @description:
 **/
@Component
public class ActiveEventParser extends ReportParser {
    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    ActiveEventParserMapper activeEventParserMapper;
    @Autowired
    EventReasonTypeService eventReasonTypeService;
    @Autowired
    EventUtil eventUtil;

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.serialNo"));
        defaultColumns.put(ActiveEvent.Fields.eventSeverity, messageSourceUtil.getMessage("common.report.form.eventSeverity"));
        defaultColumns.put(ActiveEvent.Fields.baseEquipmentName, messageSourceUtil.getMessage("common.report.form.baseEquipmentName"));
        defaultColumns.put(ActiveEvent.Fields.equipmentName, messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ActiveEvent.Fields.equipmentCategoryName, messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.eventDescription"));
        defaultColumns.put(ActiveEvent.Fields.meanings, messageSourceUtil.getMessage("eventNotification.activeEvent.meanings"));
        defaultColumns.put(ActiveEvent.Fields.startTime, messageSourceUtil.getMessage("common.report.form.startTime"));
        defaultColumns.put(ActiveEvent.Fields.endTime, messageSourceUtil.getMessage("common.report.form.endTime"));
        defaultColumns.put(ActiveEvent.Fields.reversalNum, messageSourceUtil.getMessage("eventNotification.activeEvent.reversalNum"));
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.duration"));
        defaultColumns.put(ActiveEvent.Fields.resourceStructureId, messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(ActiveEvent.Fields.confirmTime, messageSourceUtil.getMessage("common.report.form.confirmTime"));
        defaultColumns.put(ActiveEvent.Fields.handleStatus, messageSourceUtil.getMessage("common.report.form.alarmProcessStatus"));
        defaultColumns.put(ActiveEvent.Fields.eventReasonType, messageSourceUtil.getMessage("common.report.form.eventReasonTypeName"));
        defaultColumns.put(ActiveEvent.Fields.description, messageSourceUtil.getMessage("common.report.form.Comment"));
        defaultColumns.put(ActiveEvent.Fields.confirmerName, messageSourceUtil.getMessage("common.report.form.confirmerName"));
        defaultColumns.put(ActiveEvent.Fields.eventValue, messageSourceUtil.getMessage("common.report.form.eventValue"));
        defaultColumns.put(ActiveEvent.Fields.eventName, messageSourceUtil.getMessage("common.report.form.eventName"));
        return defaultColumns;
    }



    @Override
    public JSONObject parser(ReportVO reportVO) {
        ActiveEventReportFilterVO activeEventReportFilterParam = ActiveEventReportFilterVOBuild.build(reportVO.getReportParameterPresetList());

        Integer userId = Optional.ofNullable(reportVO.getUserId()).orElseThrow(() -> new BusinessException("userId is null"));

        JSONObject result = new JSONObject(true);

        Pageable pageable = reportVO.getPageable();
        // 从缓存改到用数据库查询 可以不受开关影响查到五六级告警
        Page<ActiveEventQueryWrapper> page;
        if (ObjectUtil.isNull(pageable)) {
            page = new Page<>(1, -1, false);
        } else {
            page = new Page<>(pageable.getPageNumber() + 1L, pageable.getPageSize(), false);
        }
        ActiveEventQueryWrapper queryWrapper = getActiveEvenQueryWrapper(userId, activeEventReportFilterParam);
        IPage<ActiveEvent> pageResult = activeEventParserMapper.findActiveEventByPage(page, queryWrapper);
        pageResult.setTotal(activeEventParserMapper.findActiveEventCount(queryWrapper));
        result.set(ReportStructureEnum.TOTALPAGES.value(), pageResult.getPages());
        result.set(ReportStructureEnum.TOTALELEMENTS.value(), pageResult.getTotal());
        List<ActiveEvent> liveEventList = pageResult.getRecords();
        eventUtil.fillHandlerStatus(liveEventList, ActiveEvent::getConfirmTime, ActiveEvent::getEndTime, ActiveEvent::setHandleStatus);

        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        // 添加表头
        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        int index;
        if(reportVO.getPageable() == null) {
            index = 1;
        }else {
            index = (pageable.getPageNumber() * pageable.getPageSize()) + 1;
        }
        JSONArray content = new JSONArray();

        JSONObject tempObject;
        Map<Integer, String> eventReasonTypeMap = eventReasonTypeService.getEventReasonTypeMap();
        for (ActiveEvent activeEvent : liveEventList) {
            tempObject = new JSONObject(true);
            tempObject.set(ReportStructureEnum.COLUMN1.value(), index++);
            tempObject.set(ActiveEvent.Fields.eventSeverity, activeEvent.getEventSeverity());
            tempObject.set(ActiveEvent.Fields.equipmentName, activeEvent.getEquipmentName());
            tempObject.set(ActiveEvent.Fields.resourceStructureId, resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
            tempObject.set(ActiveEvent.Fields.baseEquipmentName, activeEvent.getBaseEquipmentName());
            tempObject.set(ActiveEvent.Fields.equipmentCategoryName, activeEvent.getEquipmentCategoryName());
            tempObject.set(ReportStructureEnum.COLUMN2.value(), String.format("%s - %s - %s - %s", activeEvent.getEquipmentName(), activeEvent.getEventName(), activeEvent.getMeanings(), NumberUtil.roundTo2DecimalPlaces(activeEvent.getEventValue())));
            tempObject.set(ActiveEvent.Fields.eventName, activeEvent.getEventName());
            tempObject.set(ActiveEvent.Fields.eventValue, ReportParamParserUtils.getTwoDecimal(activeEvent.getEventValue()));
            tempObject.set(ActiveEvent.Fields.meanings, activeEvent.getMeanings());
            tempObject.set(ActiveEvent.Fields.startTime, DateUtil.dateToStringAndValidIsNull(activeEvent.getStartTime()));
            tempObject.set(ActiveEvent.Fields.endTime, DateUtil.dateToStringAndValidIsNull(activeEvent.getEndTime()));
            tempObject.set(ActiveEvent.Fields.reversalNum, activeEvent.getReversalNum());
            tempObject.set(ActiveEvent.Fields.confirmTime, DateUtil.dateToStringAndValidIsNull(activeEvent.getConfirmTime()));
            tempObject.set(ActiveEvent.Fields.confirmerName, activeEvent.getConfirmerName());
            tempObject.set(ReportStructureEnum.COLUMN11.value(), DateUtil.getTimeDifference(activeEvent.getStartTime(), activeEvent.getEndTime()));
            tempObject.set(ActiveEvent.Fields.description, activeEvent.getDescription());
            tempObject.set(ActiveEvent.Fields.eventReasonType, eventReasonTypeMap.get(activeEvent.getEventReasonType()));
            tempObject.set(ActiveEvent.Fields.handleStatus, activeEvent.getHandleStatus());

            content.add(tempObject);
        }

        // 添加内容
        result.set(ReportStructureEnum.RESULT.value(), content);

        return result;
    }


    private ActiveEventQueryWrapper getActiveEvenQueryWrapper(Integer userId, ActiveEventReportFilterVO params) {
        ActiveEventQueryWrapper queryWrapper = new ActiveEventQueryWrapper();
        // 开始时间
        queryWrapper.setStartDate(params.getStartDate());
        // 结束时间
        queryWrapper.setEndDate(params.getEndDate());
        // 设备基类id
        queryWrapper.setBaseEquipmentIdList(getIntegerSet(params.getBaseEquipmentId()));
        // 设备类型ids
        queryWrapper.setEquipmentCategories(getIntegerSet(params.getEquipmentCategories()));
        // 告警等级
        queryWrapper.setEventLevelList(getIntegerSet(params.getEventSeverityIds()));
        // 设备id
        queryWrapper.setEquipmentIdList(getIntegerSet(params.getEquipmentIds()));
        // 事件名
        queryWrapper.setEventName(params.getEventName());
        // 关键字
        queryWrapper.setKeyword(params.getKeyword());
        // 确认人
        queryWrapper.setOperatorIds(params.getConfirmerIds());
        // 注释
        queryWrapper.setDescription(params.getDescription());
        // 告警分类
        queryWrapper.setEventReasonTypes(params.getEventReasonTypes());
        Map<Integer, Set<Integer>> frontEndEventIds = params.getEventIds();
        // 前端传入了事件,则根据事件查询
        if (CollUtil.isNotEmpty(frontEndEventIds)) {
            queryWrapper.getSql().add("and (" + ReportParamParserUtils.buildEventSql(frontEndEventIds) + ")");
        }
        //设备权限统一过滤
        Set<Integer> allEquipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isNotEmpty(allEquipmentIds)) {
            queryWrapper.getSql().add(String.format("and a.EquipmentId in (%s)", CollUtil.join(allEquipmentIds, ",")));
        }
        return queryWrapper;
    }

    private static Set<Integer> getIntegerSet(String str) {
        if (str == null || str.trim().isEmpty()) {
            return Collections.emptySet();
        }
        return Arrays.stream(str.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
    }

    private ActiveEventParser() {
        super(ReportDataSourceEnum.LIVE_EVENTS.getValue());
    }
}
