package com.siteweb.report.parser.alarmCount;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.hmi.dto.StatisticsResult;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.mapper.TotalEventMapper;
import com.siteweb.report.parser.model.AlarmCountParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 告警数量统计报表(设备类型级)
 * <AUTHOR>
 * @date 2022/05/31
 */
@Component
@Slf4j
public class AlarmCountEquipmentCategoryParser {
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    TotalEventMapper totalEventMapper;
    public JSONObject getTableHead() {
        JSONObject thJsonObject = new JSONObject(true);
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.equipmentType"));
        thJsonObject.set(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.alarmCount"));
        return thJsonObject;
    }

    public JSONArray getTableBody(AlarmCountParam alarmCountParam) {
        List<StatisticsResult> results = totalEventMapper.findHistoryEventBetweenStartTimeAndCategories(alarmCountParam.getStartDate(),
                alarmCountParam.getEndDate(), alarmCountParam.getEquipmentCategories(), alarmCountParam.getEventLevelList());
        JSONArray bodyArray = new JSONArray();
        for (StatisticsResult result : results) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), result.getName());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), result.getValue());
            bodyArray.add(thJsonObject);
        }
        return bodyArray;
    }
}
