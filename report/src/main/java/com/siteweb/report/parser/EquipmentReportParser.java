package com.siteweb.report.parser;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SortUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.FocusSignalService;
import com.siteweb.monitoring.vo.FocusSignalFilterVO;
import com.siteweb.report.dto.EquipmentDTO;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.EquipmentReportParam;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.impl.DataItemServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @author: YJY
 * @time: 2023/9/16 15:44
 * @description:
 **/
@Component
public class EquipmentReportParser extends ReportParser {
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    FocusSignalService focusSignalService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    private DataItemServiceImpl dataItemService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject result = new JSONObject(true);
        Integer userId = Optional.of(TokenUserUtil.getLoginUserId()).orElseThrow(() -> new BusinessException("userId is null"));
        // title
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        // result/totalElements/totalPages
        EquipmentReportParam equipmentReportParam = new EquipmentReportParam(reportVO.getReportParameterPresetList());
        FocusSignalFilterVO reportParam = new FocusSignalFilterVO();
        Set<Integer> childrenIds = resourceStructureManager.getAllChildrenId(StringUtils.splitToIntegerList(equipmentReportParam.getResourceStructureIds()));
        reportParam.setResourceStructureIds(StringUtils.join(childrenIds, ','));
        reportParam.setBaseEquipmentIds(equipmentReportParam.getBaseEquipmentIds());
        List<EquipmentDTO> EquipmentDTOList = findResultByPage(reportParam,equipmentReportParam, reportVO.getPageable(), userId, (pageImpl) -> {
            result.set(ReportStructureEnum.TOTALPAGES.value(), pageImpl.getTotalPages());
            result.set(ReportStructureEnum.TOTALELEMENTS.value(), pageImpl.getTotalElements());
        });
        // 序号
        int index;
        if (reportVO.getPageable() == null) {
            index = 1;
        } else {
            index = (reportVO.getPageable().getPageNumber() * reportVO.getPageable().getPageSize()) + 1;
        }
        JSONArray list = new JSONArray();
        JSONObject tdJsonObject;
        for (EquipmentDTO equipmentDTO : EquipmentDTOList) {
            tdJsonObject = new JSONObject(true);
            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), index++);
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), equipmentDTO.getEquipmentPosition());
            tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), equipmentDTO.getBaseEquipmentName());
            tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), equipmentDTO.getEquipmentCategoryName());
            tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), equipmentDTO.getEquipmentName());
            OnlineState onlineState = equipmentDTO.getEquipmentState();
            if (ObjectUtil.isNotNull(onlineState)){
                switch (onlineState){
                    case ONLINE -> tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("api.stationStatus.1"));
                    case OFFLINE -> tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("api.stationStatus.0"));
                    case UNREGISTER -> tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("api.stationStatus.2"));
                }
            }
            list.add(tdJsonObject);
        }
        result.set(ReportStructureEnum.RESULT.value(), list);
        return result;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.serialNo"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.baseEquipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.equipmentState"));
        return defaultColumns;
    }


    private List<EquipmentDTO> findResultByPage(FocusSignalFilterVO reportParam, EquipmentReportParam equipmentReportParam, Pageable pageable, Integer userId, Consumer<PageImpl<EquipmentDTO>> equipmentDTOPageFun) {
        List<Equipment> equipments = focusSignalService.findEquipmentsByFilterVO(userId, reportParam);
        HashMap<Integer, EquipmentState> equipmentStateByIds = equipmentStateManager.getEquipmentStateByIds(equipments.stream().map(Equipment::getEquipmentId).toList());
        Collection<EquipmentState> equipmentStates = equipmentStateByIds.values();
        if (equipmentReportParam.getEquipmentState() != null) {
            Set<Integer> equipmentIds = equipmentStates.stream().filter(a -> ObjectUtil.equals(a.getOnlineState().value(), equipmentReportParam.getEquipmentState())).map(EquipmentState::getEquipmentId).collect(Collectors.toSet());
            equipments = equipments.stream().filter(o -> equipmentIds.contains(o.getEquipmentId())).toList();
        }
        if (CollUtil.isNotEmpty(equipmentReportParam.getEquipmentCategories())) {
            equipments = equipments.stream().filter(o -> equipmentReportParam.getEquipmentCategories().contains(o.getEquipmentCategory())).toList();
        }
        List<EquipmentDTO> result = new ArrayList<>();
        Map<Integer, String> equipmentCategoryMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());
        for (Equipment equipment : equipments) {
            EquipmentDTO tmpList = this.constructEquipmentDTO(equipment,equipmentCategoryMap);
            tmpList.setEquipmentState(equipmentStateByIds.getOrDefault(equipment.getEquipmentId(),new EquipmentState()).getOnlineState());
            result.add(tmpList);
        }
        if (pageable == null) {
            return result;
        }
        // 排序
        if (pageable.getSort().iterator().hasNext()) {
            result = new SortUtil<EquipmentDTO>().sort(pageable.getSort(), result);
        }
        // 分页
        List<EquipmentDTO> slice = result.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        PageImpl<EquipmentDTO> equipmentDTOPageFunPage = new PageImpl<>(slice, pageable, result.size());
        equipmentDTOPageFun.accept(equipmentDTOPageFunPage);
        return slice;
    }


    private EquipmentDTO constructEquipmentDTO(Equipment equipment, Map<Integer, String> equipmentCategoryMap) {
        EquipmentDTO equipmentDTO = new EquipmentDTO();
        String equipmentPosition = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
        equipmentDTO.setEquipmentPosition(equipmentPosition);
        equipmentDTO.setBaseEquipmentName(equipment.getEquipmentBaseTypeName());
        equipmentDTO.setEquipmentName(equipment.getEquipmentName());
        equipmentDTO.setEquipmentCategoryName(equipmentCategoryMap.get(equipment.getEquipmentCategory()));
        return equipmentDTO;
    }

    private EquipmentReportParser() {
        super(ReportDataSourceEnum.EQUIPMENT_REPORT.getValue());
    }
}
