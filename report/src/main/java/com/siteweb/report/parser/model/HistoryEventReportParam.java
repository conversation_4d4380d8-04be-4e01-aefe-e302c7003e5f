package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * @Author: lzy
 * @Date: 2022/5/23 19:27
 */
@Data
public class HistoryEventReportParam {

    private static final Logger log = LoggerFactory.getLogger(HistoryEventReportParam.class);

    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束事件
     */
    private Date endDate;
    /**
     * 设备基类id
     */
    private Integer baseEquipmentId;
    /**
     * 设备类型
     */
    private List<Integer> equipmentCategories;
    /**
     * 设备id
     */
    private List<Integer> equipmentIds;
    /**
     * 告警等级id
     */
    private List<Integer> eventLevelList;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 操作人id
     */
    private List<Integer> operatorIds;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 事件id
     */
    private Map<Integer, Set<Integer>> eventIds = new HashMap<>();
    /**
     * 告警注释
     */
    private String description;
    /**
     * 告警分类(告警原因类型)
     */
    private List<Integer> eventReasonTypes;

    public HistoryEventReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "baseEquipmentId":
                        this.baseEquipmentId = ReportParamParserUtils.strToInteger(reportParameterPreset.getValue());
                        break;
                    case "equipmentCategories":
                        this.equipmentCategories = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_CATEGORY_ID);
                        break;
                    case "equipmentIds":
                        this.equipmentIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                        break;
                    case "eventLevel":
                        this.eventLevelList = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    case "eventName":
                        this.eventName = reportParameterPreset.getValue();
                        break;
                    case "operatorIds":
                        this.operatorIds = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    case "keyword":
                        this.keyword = reportParameterPreset.getValue();
                        break;
                    case "eventIds":
                        this.eventIds = ReportParamParserUtils.parseEventStrToMap(reportParameterPreset.getValue());
                        break;
                    case "description":
                        this.description = reportParameterPreset.getValue();
                        break;
                    case "eventReasonTypes":
                        this.eventReasonTypes = ReportParamParserUtils.strToList(reportParameterPreset.getValue());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 HistoryEventReportParam 异常：", e);
            throw e;
        }
    }
}
