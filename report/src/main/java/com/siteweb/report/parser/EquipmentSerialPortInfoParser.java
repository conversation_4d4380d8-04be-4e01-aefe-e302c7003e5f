package com.siteweb.report.parser;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SortUtil;
import com.siteweb.report.entity.EquipmentPortInfo;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.mapper.EquipmentSerialPortInfoMapper;
import com.siteweb.report.parser.model.EquipmentSerialPortInfoReportParam;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * Description: Equipment Serial Port Info Parser
 * Author: <EMAIL>
 * Creation Date: 2024/12/24
 */
@Component
public class EquipmentSerialPortInfoParser extends ReportParser {

    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    private EquipmentSerialPortInfoMapper equipmentSerialPortInfoMapper;

    @Autowired
    private EquipmentStateManager equipmentStateManager;

    protected EquipmentSerialPortInfoParser() {
        super(ReportDataSourceEnum.EQUIPMENT_SERIALPORT_INFO.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject result = new JSONObject(true);
        Integer userId = Optional.of(TokenUserUtil.getLoginUserId()).orElseThrow(() -> new BusinessException("userId is null"));



        EquipmentSerialPortInfoReportParam reportParam = new EquipmentSerialPortInfoReportParam(reportVO.getReportParameterPresetList());
        // Set up title
        JSONObject thJsonObject = setupTitle(reportParam.getDisplayId());
        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);

        List<EquipmentPortInfo> equipmentPortInfoList = findResultByPage(reportParam, reportVO.getPageable(), (pageImpl) -> {
            result.set(ReportStructureEnum.TOTALPAGES.value(), pageImpl.getTotalPages());
            result.set(ReportStructureEnum.TOTALELEMENTS.value(), pageImpl.getTotalElements());
        });

        JSONArray jsonArray = new JSONArray();
        for (EquipmentPortInfo info : equipmentPortInfoList) {
            JSONObject tdJsonObject = constructRowData(info);
            jsonArray.add(tdJsonObject);
        }

        result.set(ReportStructureEnum.RESULT.value(), jsonArray);

        return result;
    }

    private List<EquipmentPortInfo> findResultByPage(EquipmentSerialPortInfoReportParam reportParam, Pageable pageable, Consumer<PageImpl<EquipmentPortInfo>> pageInfoConsumer) {
        List<EquipmentPortInfo> allResults = equipmentSerialPortInfoMapper.findEquipmentPortInfo(reportParam.toQuery());

        if (pageable == null) {
            pageInfoConsumer.accept(new PageImpl<>(allResults));
            return allResults;
        }

        // 排序
        if (pageable.getSort().iterator().hasNext()) {
            allResults = new SortUtil<EquipmentPortInfo>().sort(pageable.getSort(), allResults);
        }

        // 分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), allResults.size());
        List<EquipmentPortInfo> pageContent = allResults.subList(start, end);

        PageImpl<EquipmentPortInfo> page = new PageImpl<>(pageContent, pageable, allResults.size());
        pageInfoConsumer.accept(page);

        return pageContent;
    }

    private JSONObject setupTitle(String displayId) {
        JSONObject thJsonObject = new JSONObject(true);
        // 是否显示系统id
        boolean showExtra = Objects.equals("1", displayId);
        if (showExtra) {
            thJsonObject.set(EquipmentPortInfo.Fields.resourceStructureId, messageSourceUtil.getMessage("common.report.form.resourceStructureId"));
        }
        thJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.resourceStructureName"));
        if (showExtra) {
            thJsonObject.set(EquipmentPortInfo.Fields.equipmentId, messageSourceUtil.getMessage("common.report.form.equipmentId"));
        }
        thJsonObject.set(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        thJsonObject.set(ReportStructureEnum.COLUMN15.value(), messageSourceUtil.getMessage("common.report.form.equipmentTemplateName"));
        thJsonObject.set(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.equipmentCategory"));
        if (showExtra) {
            thJsonObject.set(EquipmentPortInfo.Fields.stationId, messageSourceUtil.getMessage("common.report.form.stationId"));
        }
        thJsonObject.set(EquipmentPortInfo.Fields.stationName, messageSourceUtil.getMessage("common.report.form.stationName"));
        if (showExtra) {
            thJsonObject.set(EquipmentPortInfo.Fields.monitorUnitId, messageSourceUtil.getMessage("common.report.form.monitorUnitId"));
        }
        thJsonObject.set(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.monitorUnitName"));
        thJsonObject.set(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.ipAddress"));
        thJsonObject.set(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.serialPort"));
        thJsonObject.set(ReportStructureEnum.COLUMN16.value(), messageSourceUtil.getMessage("common.report.form.serialPortParameters"));
        thJsonObject.set(ReportStructureEnum.COLUMN12.value(), messageSourceUtil.getMessage("common.report.form.samplerUnitName"));
        thJsonObject.set(ReportStructureEnum.COLUMN13.value(), messageSourceUtil.getMessage("common.report.form.address"));
        thJsonObject.set(ReportStructureEnum.COLUMN14.value(), messageSourceUtil.getMessage("common.report.form.equipmentState"));

        return thJsonObject;
    }

    private JSONObject constructRowData(EquipmentPortInfo info) {
        JSONObject tdJsonObject = new JSONObject(true);
        
        tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), info.getResourceStructureName());
        tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), info.getEquipmentName());
        tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), info.getEquipmentCategory());
        tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), info.getMonitorUnitName());
        tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), info.getIpAddress());
        tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), info.getPortName());
        tdJsonObject.set(ReportStructureEnum.COLUMN12.value(), info.getSamplerUnitName());
        tdJsonObject.set(ReportStructureEnum.COLUMN13.value(), info.getAddress());

        // 从 EquipmentStateManager 获取设备的在线状态
        OnlineState onlineState = equipmentStateManager.getEquipmentOnlineStateById(info.getEquipmentId());
        if (onlineState != null) {
            if (onlineState.equals(OnlineState.ONLINE)) {
                tdJsonObject.set(ReportStructureEnum.COLUMN14.value(), messageSourceUtil.getMessage("api.stationStatus.1"));
            } else if (onlineState.equals(OnlineState.OFFLINE)) {
                tdJsonObject.set(ReportStructureEnum.COLUMN14.value(), messageSourceUtil.getMessage("api.stationStatus.0"));
            } else if (onlineState.equals(OnlineState.UNREGISTER)) {
                tdJsonObject.set(ReportStructureEnum.COLUMN14.value(), messageSourceUtil.getMessage("api.stationStatus.2"));
            } else {
                tdJsonObject.set(ReportStructureEnum.COLUMN14.value(), onlineState.value());
            }
        } else {
            tdJsonObject.set(ReportStructureEnum.COLUMN14.value(), "");
        }
        tdJsonObject.set(ReportStructureEnum.COLUMN15.value(), info.getEquipmentTemplateName());
        tdJsonObject.set(ReportStructureEnum.COLUMN16.value(), info.getSetting());
        tdJsonObject.set(EquipmentPortInfo.Fields.resourceStructureId, info.getResourceStructureId());
        tdJsonObject.set(EquipmentPortInfo.Fields.equipmentId, info.getEquipmentId());
        tdJsonObject.set(EquipmentPortInfo.Fields.stationId, info.getStationId());
        tdJsonObject.set(EquipmentPortInfo.Fields.stationName, info.getStationName());
        tdJsonObject.set(EquipmentPortInfo.Fields.monitorUnitId, info.getMonitorUnitId());
        return tdJsonObject;
    }
}