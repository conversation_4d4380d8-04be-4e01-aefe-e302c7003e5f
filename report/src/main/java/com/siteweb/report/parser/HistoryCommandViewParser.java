package com.siteweb.report.parser;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.ControlDTO;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.ComputerRackHistoryDataPointReportParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

/**
 * 控制记录报表
 * <AUTHOR>
 * @date 2023/03/10
 */
@Component
public class HistoryCommandViewParser extends ReportParser{
    private HistoryCommandViewParser() {
        super(ReportDataSourceEnum.HISTORY_COMMAND_LOG.getValue());
    }

    @Autowired
    private ActiveControlService activeControlService;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        ComputerRackHistoryDataPointReportParam computerRackHistoryDataPointReportParam = new ComputerRackHistoryDataPointReportParam(reportVO.getReportParameterPresetList());
        JSONArray bodyArrayJson = this.getTableBody(reportVO.getUserId(),computerRackHistoryDataPointReportParam.getStartTime(), computerRackHistoryDataPointReportParam.getEndTime());
        //创建表头 添加表头 添加数据
        JSONObject titleJson = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        resultJsonObject.set(ReportStructureEnum.TITLE.value(),titleJson);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyArrayJson);
        return resultJsonObject;
    }

    /**
     * 获取表格数据
     *
     * @param userId
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    private JSONArray getTableBody(Integer userId, Date startTime, Date endTime) {
        Set<Integer> equipmentIdSet = equipmentService.findEquipmentIdsByUserId(userId);
        //获取所有的控制记录
        List<ControlDTO> allControl = activeControlService.findAllControl(equipmentIdSet, startTime, endTime);
        //填充表格内容
        JSONArray bodyArrayJson = new JSONArray();
        for (ControlDTO controlDTO : allControl) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), controlDTO.getEquipmentName());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), controlDTO.getBaseEquipmentName());
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), controlDTO.getEquipmentPosition());
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), controlDTO.getControlName());
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), controlDTO.getControlExecuterIdName());
            thJsonObject.set(ReportStructureEnum.COLUMN6.value(), controlDTO.getExecTime());
            thJsonObject.set(ReportStructureEnum.COLUMN7.value(), controlDTO.getResponseTime());
            thJsonObject.set(ReportStructureEnum.COLUMN8.value(), controlDTO.getExecResult());
            bodyArrayJson.add(thJsonObject);
        }
        return bodyArrayJson;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.equipmentType"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.controlName"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.operatorName"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.controlTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.responseTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.state"));
        return defaultColumns;
    }

}
