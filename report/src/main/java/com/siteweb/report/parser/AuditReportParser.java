package com.siteweb.report.parser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.dto.AuditReportParamDto;
import com.siteweb.admin.entity.AuditReport;
import com.siteweb.admin.service.AuditReportService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.AuditReportParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

@Component
public class AuditReportParser extends ReportParser {
    private AuditReportParser() {
        super(ReportDataSourceEnum.AUDIT_REPORT.getValue());
    }

    @Autowired
    private AuditReportService auditReportService;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        AuditReportParam auditReportParam = new AuditReportParam(reportVO.getReportParameterPresetList());
        JSONArray bodyJsonObject = this.getTableBody(auditReportParam);
        //创建表头 添加表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return resultJsonObject;
    }

    private JSONArray getTableBody(AuditReportParam auditReportParam) {
        AuditReportParamDto auditReportParamDto = BeanUtil.copyProperties(auditReportParam, AuditReportParamDto.class);
        List<AuditReport> auditReportList = auditReportService.findAuditReport(auditReportParamDto);
        JSONArray jsonArray = new JSONArray();
        for (AuditReport audit : auditReportList) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), audit.getOperationAccount());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), audit.getLevelName());
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), audit.getType());
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), audit.getClientIp());
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), audit.getDetails());
            thJsonObject.set(ReportStructureEnum.COLUMN6.value(), audit.getResult());
            thJsonObject.set(ReportStructureEnum.COLUMN7.value(), audit.getCreateTime());
            jsonArray.add(thJsonObject);
        }
        return jsonArray;
    }


    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("audit.report.operationAccount"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("audit.report.auditLevel"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("audit.report.eventType"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("audit.report.clientIp"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("audit.report.content"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("audit.report.eventResult"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("audit.report.operationTime"));
        return defaultColumns;
    }

}
