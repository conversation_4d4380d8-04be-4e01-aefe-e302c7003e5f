package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class EquipHistoryStateReportParam {
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束事件
     */
    private Date endDate;
    /**
     * 群控设备分组id
     */
    private String virtualInfo;

    public EquipHistoryStateReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (CharSequenceUtil.isEmpty(reportParameterPreset.getValue())) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "virtualInfo":
                        this.virtualInfo = reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            e.getMessage();
            throw e;
        }
    }
}
