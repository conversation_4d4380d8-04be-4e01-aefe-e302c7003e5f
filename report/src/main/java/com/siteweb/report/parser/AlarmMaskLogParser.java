package com.siteweb.report.parser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.AlarmMaskLogDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mapper.AlarmMaskLogMapper;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.AlarmMaskLogReportParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 告警屏蔽日志报表
 *
 * @Author: lzy
 * @Date: 2022/5/25 15:04
 */
@Component
public class AlarmMaskLogParser extends ReportParser {
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    AlarmMaskLogMapper alarmMaskLogMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    AccountService accountService;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        AlarmMaskLogReportParam param = new AlarmMaskLogReportParam(reportVO.getReportParameterPresetList());

        JSONArray jsonArray = new JSONArray();

        List<AlarmMaskLogDTO> alarmMaskLogDTOList = findAlarmMaskLogDTO(reportVO.getUserId(),param);

        if (CollUtil.isNotEmpty(alarmMaskLogDTOList)) {
            setJsonArray(alarmMaskLogDTOList, jsonArray);
        }

        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        JSONObject result = new JSONObject(true);
        result.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        result.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return result;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.equipmentPosition"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.equipmentName"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.eventName"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.alarmOperatorName"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.operationTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.operationType"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.alarmStartTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.alarmEndTime"));
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.timeGroupCategory"));
        defaultColumns.put(ReportStructureEnum.COLUMN10.value(), messageSourceUtil.getMessage("common.report.form.timeGroupChars"));
        defaultColumns.put(ReportStructureEnum.COLUMN11.value(), messageSourceUtil.getMessage("common.report.form.reason"));
        return defaultColumns;
    }



    private static void setJsonArray(List<AlarmMaskLogDTO> alarmMaskLogList, JSONArray jsonArray) {
        JSONObject tdJsonObject;
        for (AlarmMaskLogDTO alarmMaskLog : alarmMaskLogList) {
            tdJsonObject = new JSONObject(true);

            tdJsonObject.set(ReportStructureEnum.COLUMN1.value(), ReportParamParserUtils.getStrValue(alarmMaskLog.getResourceStructureName()));
            tdJsonObject.set(ReportStructureEnum.COLUMN2.value(), ReportParamParserUtils.getStrValue(alarmMaskLog.getEquipmentName()));
            tdJsonObject.set(ReportStructureEnum.COLUMN3.value(), ReportParamParserUtils.getStrValue(alarmMaskLog.getEventName()));
            tdJsonObject.set(ReportStructureEnum.COLUMN4.value(), alarmMaskLog.getUserName());
            tdJsonObject.set(ReportStructureEnum.COLUMN5.value(), DateUtil.dateToStringAndValidIsNull(alarmMaskLog.getOperationTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN6.value(), alarmMaskLog.getOperationType());
            tdJsonObject.set(ReportStructureEnum.COLUMN7.value(), DateUtil.dateToStringAndValidIsNull(alarmMaskLog.getStartTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN8.value(), DateUtil.dateToStringAndValidIsNull(alarmMaskLog.getEndTime()));
            tdJsonObject.set(ReportStructureEnum.COLUMN9.value(), alarmMaskLog.getTimeGroupCategory());
            tdJsonObject.set(ReportStructureEnum.COLUMN10.value(), alarmMaskLog.getTimeGroupChars());

            Date date = new Date();
            long time = date.getTime();
            if (alarmMaskLog.getEndTime() != null && time > alarmMaskLog.getEndTime().getTime()) {
                tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), "系统到时自动结束");
            } else {
                tdJsonObject.set(ReportStructureEnum.COLUMN11.value(), alarmMaskLog.getComment());
            }
            jsonArray.add(tdJsonObject);
        }
    }

    public List<AlarmMaskLogDTO> findAlarmMaskLogDTO(Integer userId, AlarmMaskLogReportParam param) {
        //区域权限过滤
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        param.setEquipmentIds(equipmentIds);
        //部门权限过滤
        if (CollUtil.isEmpty(param.getOperatorIds())) {
            Set<Integer> operatorIds = accountService.findUserIdByPermission(userId);
            param.setOperatorIds(operatorIds);
        }
        com.siteweb.monitoring.dto.AlarmMaskLogReportParam alarmMaskLogReportParam = BeanUtil.copyProperties(param, com.siteweb.monitoring.dto.AlarmMaskLogReportParam.class);
        List<AlarmMaskLogDTO> alarmMaskLogDTOList =   alarmMaskLogMapper.findAlarmMaskLog(alarmMaskLogReportParam);
        findSpecificInfo(alarmMaskLogDTOList);
        return alarmMaskLogDTOList;
    }

    /**
     * 缓存中查询具体信息（eventName，equipmentName等）
     *
     * @param alarmMaskLogDTOList
     */
    public void findSpecificInfo(List<AlarmMaskLogDTO> alarmMaskLogDTOList) {
        if (CollUtil.isEmpty(alarmMaskLogDTOList)) {
            return;
        }
        alarmMaskLogDTOList.forEach(e -> {
            e.setEquipmentName(Optional.ofNullable(equipmentService.findById(e.getEquipmentId())).orElse(new Equipment()).getEquipmentName());
            e.setResourceStructureName(Optional.ofNullable(resourceStructureManager.getResourceStructureById(e.getResourceStructureId())).orElse(new ResourceStructure()).getResourceStructureName());
        });
    }

    protected AlarmMaskLogParser() {
        super(ReportDataSourceEnum.ALARM_MASK.getValue());
    }
}
