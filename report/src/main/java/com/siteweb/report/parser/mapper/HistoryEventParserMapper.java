package com.siteweb.report.parser.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.report.dto.HistoryEventReportDTO;
import com.siteweb.report.parser.querywrapper.HistoryEventQueryWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/12/24 14:33
 */
public interface HistoryEventParserMapper {

    IPage<HistoryEventReportDTO> findHistoryEventByPage(@Param("page") Page<HistoryEventReportDTO> page, @Param("wrapper") HistoryEventQueryWrapper wrapper);

    /**
     * 查询历史告警的总数
     * 用于分页
     *  由于mybatis-plus的分页插件存在性能问题 固自定义一个查询总数的方法
     * @param wrapper 包装器
     * @return long
     */
    long findHistoryEventCount(@Param("wrapper") HistoryEventQueryWrapper wrapper);

    List<HistoryEventReportDTO> findHistoryEvent(@Param("wrapper") HistoryEventQueryWrapper wrapper);
}
