package com.siteweb.report.parser;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.dto.RackRecordParamDto;
import com.siteweb.computerrack.mapper.RackMountRecordMapper;
import com.siteweb.computerrack.vo.RackChangeRecordVo;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.RackRecordParam;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 机架上下架报表
 *
 * <AUTHOR>
 * @date 2022/06/14
 */
@Component
public class RackRecordParser extends ReportParser {
    private RackRecordParser() {
        super(ReportDataSourceEnum.RACK_RECORD.getValue());
    }
    /**
     * 上架
     */
    public static final Integer PUTINSHELF = 1;
    /**
     * 下架
     */
    public static final Integer TAKEFROMSHELF = 2;

    @Autowired
    RackMountRecordMapper rackMountRecordMapper;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public JSONObject parser(ReportVO reportVO) {
        JSONObject resultJsonObject = new JSONObject(true);
        RackRecordParam rackRecordParam = new RackRecordParam(reportVO.getReportParameterPresetList());
        JSONArray bodyJsonObject = this.getTableBody(reportVO.getUserId(),rackRecordParam);
        //创建表头 添加表头
        JSONObject thJsonObject = buildTableHeader(reportVO.getColumnConfig(), getDefaultColumnMap());
        resultJsonObject.set(ReportStructureEnum.TITLE.value(), thJsonObject);
        resultJsonObject.set(ReportStructureEnum.RESULT.value(), bodyJsonObject);
        return resultJsonObject;
    }

    /**
     * 获取报表信息
     *
     * @param userId
     * @param rackRecordParam 查询参数
     * @return {@link JSONObject}
     */
    private JSONArray getTableBody(Integer userId, RackRecordParam rackRecordParam) {
        JSONArray jsonArray = new JSONArray();
        List<Integer> resourceStructureIdsByUserId = resourceStructureService.findResourceStructureIdsByUserId(userId);
        if (CollUtil.isEmpty(resourceStructureIdsByUserId)) {
            return jsonArray;
        }
        rackRecordParam.setResourceStructureIds(resourceStructureIdsByUserId);
        RackRecordParamDto param = BeanUtil.copyProperties(rackRecordParam, RackRecordParamDto.class);
        List<RackChangeRecordVo> rackChangeRecord = rackMountRecordMapper.findRackChangeRecord(param);
        for (RackChangeRecordVo vo : rackChangeRecord) {
            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(ReportStructureEnum.COLUMN1.value(), vo.getPosition());
            thJsonObject.set(ReportStructureEnum.COLUMN2.value(), vo.getComputerRackName());
            thJsonObject.set(ReportStructureEnum.COLUMN3.value(), vo.getITDeviceName());
            thJsonObject.set(ReportStructureEnum.COLUMN4.value(), vo.getUIndex());
            thJsonObject.set(ReportStructureEnum.COLUMN5.value(), vo.getCustomer());
            thJsonObject.set(ReportStructureEnum.COLUMN6.value(), vo.getBusiness());
            thJsonObject.set(ReportStructureEnum.COLUMN7.value(), vo.getPurchaseDate());
            thJsonObject.set(ReportStructureEnum.COLUMN8.value(), this.getOperateState(vo.getOperateState()));
            thJsonObject.set(ReportStructureEnum.COLUMN9.value(), vo.getOperateTime());
            jsonArray.add(thJsonObject);
        }
        return jsonArray;
    }

    /**
     * 创建表头
     *
     * @return {@link JSONObject}
     */
    @Override
    public LinkedHashMap<String, String> getDefaultColumnMap() {
        LinkedHashMap<String, String> defaultColumns = new LinkedHashMap<>();
        defaultColumns.put(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("common.report.form.resourcePosition"));
        defaultColumns.put(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("common.report.form.rackName"));
        defaultColumns.put(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("common.report.form.ITDeviceName"));
        defaultColumns.put(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("common.report.form.uIndex"));
        defaultColumns.put(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("common.report.form.customer"));
        defaultColumns.put(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("common.report.form.business"));
        defaultColumns.put(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("common.report.form.purchaseDate"));
        defaultColumns.put(ReportStructureEnum.COLUMN8.value(), messageSourceUtil.getMessage("common.report.form.state"));
        defaultColumns.put(ReportStructureEnum.COLUMN9.value(), messageSourceUtil.getMessage("common.report.form.changeDate"));
        return defaultColumns;
    }



    /**
     * 上下架状态转换
     * @param operateState it设备上下架状态
     * @return {@link String}
     */
    private String getOperateState(Integer operateState){
        //上架
        if (PUTINSHELF.equals(operateState)) {
            return messageSourceUtil.getMessage("common.report.form.putInShelf");
        }
        //下架
        return messageSourceUtil.getMessage("common.report.form.takeFormShelf");
    }
}
