package com.siteweb.report.parser.model;

import com.siteweb.common.util.DateUtil;
import com.siteweb.report.commonutils.ReportParamParserUtils;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author: lzy
 * @Date: 2022/5/18 19:02
 */
@Data
@NoArgsConstructor
public class HistorySignalReportParam {

    private static final Logger log = LoggerFactory.getLogger(HistorySignalReportParam.class);

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 信号
     */
    private String signalIds;
    /**
     * 时间颗粒
     */
    private String timeGranularity;
    /**
     * 信号基类
     */
    private List<Integer> baseTypeIds;
    /**
     * 信号类型
     */
    private String signalTypes;

    /**
     * 五分钟存储还是周期存储报表
     */
    private boolean switchDatabase2;
    /**
     * 是否带单位
     */
    private String unit;
    /**
     * 设备id
     */
    private Set<Integer> equipmentIds;
    /**
     * 取值方式  max min mean first last
     */
    private String valueRetrievalMethod;

    public HistorySignalReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollectionUtils.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startTime":
                        this.startTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endTime" :
                        this.endTime = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "signalIds":
                        this.signalIds = reportParameterPreset.getValue();
                        break;
                    case "timeGranularity":
                        this.timeGranularity = reportParameterPreset.getValue();
                        break;
                    case "baseTypeIds":
                        this.baseTypeIds = ReportParamParserUtils.jsonToList(reportParameterPreset.getValue(), ReportParamParserUtils.BASE_TYPE_ID);
                        break;
                    case "signalTypes":
                        this.signalTypes = reportParameterPreset.getValue();
                        break;
                    case "unit":
                        this.unit = reportParameterPreset.getValue();
                        break;
                    case "equipmentIds":
                        this.equipmentIds = ReportParamParserUtils.jsonToLinkedSet(reportParameterPreset.getValue(), ReportParamParserUtils.EQUIPMENT_ID);
                        break;
                    case "valueRetrievalMethod":
                        this.valueRetrievalMethod = reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("构建 HistorySignalReportParam 异常: ", e);
            throw e;
        }
    }
}
