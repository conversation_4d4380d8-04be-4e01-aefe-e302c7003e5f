package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.entity.HistoryComplexIndexGroupByTime;
import com.siteweb.complexindex.manager.HistoryComplexIndexManager;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.HistoryComplexIndexReportParam;
import com.siteweb.report.util.ReportUtil;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 历史指标
 *
 * @Author: lzy
 * @Date: 2022/5/21 13:22
 */
@Component
public class HistoryComplexIndexParser extends ReportParser {

    @Autowired
    HistoryComplexIndexManager historyComplexIndexManager;
    @Autowired
    ComplexIndexService complexIndexService;
    private static final ThreadLocal<Map<String, Integer>> complexIndexAccuracyMap = new ThreadLocal<>(); // key => 指标id， value => 精度位数

    private static final String POSITION = "position";
    private static final String COMPLEX_INDEX_ID = "complexIndexId";
    private static final String COMPLEX_INDEX_NAME = "complexIndexName";
    private static final String UNIT = "unit";
    private static final String VALUES = "values";
    private static final String VALUE = "value";
    private static final String DISPLAY_VALUE = "displayValue";

    @Override
    public JSONObject parser(ReportVO reportVO) {
        HistoryComplexIndexReportParam param = new HistoryComplexIndexReportParam(reportVO.getReportParameterPresetList());
        complexIndexAccuracyMap.set(new HashMap<>());
        JSONObject result = new JSONObject(true);
        try {
            String complexIndexIds = param.getComplexIndexIds();
            if (CharSequenceUtil.isEmpty(complexIndexIds)) {
                return result;
            }
            JSONArray historyComplexIndexJsonArray = JSONUtil.parseArray(complexIndexIds);

            String timeGranularity = param.getTimeGranularity();
            if (CharSequenceUtil.isEmpty(timeGranularity) || Objects.equals("0", timeGranularity)) {
                // 查原数据
                result = queryOriginalData(historyComplexIndexJsonArray, param, reportVO.getPageable(),reportVO.getLanguage());
            } else {
                // 时间颗粒度查询
                result = queryDataByTimeGranularity(historyComplexIndexJsonArray, param, timeGranularity,reportVO.getLanguage());
            }
        }finally {
            complexIndexAccuracyMap.remove();
        }
        return result;
    }

    /**
     * 时间格式查询
     *
     * @param historyComplexIndexJsonArray 历史指标json数组
     * @param param                        查询参数
     * @param timeGranularity              分页对象
     * @return 报表数据
     */
    private JSONObject queryDataByTimeGranularity(JSONArray historyComplexIndexJsonArray, HistoryComplexIndexReportParam param, String timeGranularity,String language) {

        JSONObject jsonObject = new JSONObject(true);
        JSONArray chartArray = new JSONArray();
        JSONArray thJsonArray = new JSONArray();

        List<HistoryComplexIndexGroupByTime> historyComplexIndexGroupByTimeList = new ArrayList<>();
        int maxAccuracy = 0;
        for (int i = 0; i < historyComplexIndexJsonArray.size(); i++) {
            final int index = i;
            JSONObject object = historyComplexIndexJsonArray.getJSONObject(i);
            List<HistoryComplexIndexGroupByTime> historyComplexIndexList;
            Integer complexIndexId = object.getInt(COMPLEX_INDEX_ID);
            Date startTime = param.getStartTime();
            Date endTime = param.getEndTime();
            if (timeGranularity.contains("w")) {
                // 按周查询
                historyComplexIndexList = historyComplexIndexManager.findWeekHistoryComplexIndexGroupByTime(startTime, endTime, complexIndexId, timeGranularity);
            } else if ("month".equals(timeGranularity)) {
                // 按月查询
                historyComplexIndexList = historyComplexIndexManager.findMonthHistoryComplexIndexGroupByTime(startTime, endTime, complexIndexId, timeGranularity);
//            } else if ("quarter".equals(timeGranularity)) {
                // 按季度查询
//                historyComplexIndexList = historyComplexIndexManager.findQuarterHistoryComplexIndexGroupByTime(startTime, endTime, complexIndexId, timeGranularity);
                //} else if ("year".equals(timeGranularity)) {
                // 按年查询
                //historyComplexIndexList = historyComplexIndexManager.findYearHistoryComplexIndexGroupByTime(startTime, endTime, complexIndexId, timeGranularity);
            } else {
                historyComplexIndexList = historyComplexIndexManager.findHistoryComplexIndexGroupByTime(startTime, endTime, complexIndexId, timeGranularity);
                this.getDisplayValue(String.valueOf(complexIndexId), "0");
            }

            // sort Index
            if (CollUtil.isNotEmpty(historyComplexIndexList)) {
                int accuracy = Optional.ofNullable(complexIndexAccuracyMap.get().get(String.valueOf(complexIndexId))).orElse(2);
                if (accuracy > maxAccuracy) {
                    maxAccuracy = accuracy;
                }
                historyComplexIndexList.forEach(e -> {
                    e.setSortIndex(index);
                    e.setDisplayValue(new BigDecimal(e.getIndexValue()).setScale(accuracy, RoundingMode.UP).toPlainString());
                });
            }

            historyComplexIndexGroupByTimeList.addAll(historyComplexIndexList);

            historyComplexIndexList.removeIf(e -> e.count == 0);

            JSONObject chartObject = new JSONObject(true);
            chartObject.set(COMPLEX_INDEX_ID, object.getStr(COMPLEX_INDEX_ID));
            chartObject.set(COMPLEX_INDEX_NAME, object.getStr(COMPLEX_INDEX_NAME));
            chartObject.set(POSITION, object.getStr(POSITION));
            chartObject.set(UNIT, object.getStr(UNIT));
            chartObject.set(VALUES, historyComplexIndexList);
            chartArray.add(chartObject);

            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(COMPLEX_INDEX_NAME, object.getStr(COMPLEX_INDEX_NAME));
            thJsonObject.set(POSITION, object.getStr(POSITION));
            thJsonObject.set(UNIT, object.getStr(UNIT));
            thJsonArray.add(thJsonObject);
        }

        jsonObject.set(ReportStructureEnum.TITLE.value(), thJsonArray);
        jsonObject.set(ReportStructureEnum.RESULT.value(), getTimeGranularityResultJsonArray(historyComplexIndexGroupByTimeList, historyComplexIndexJsonArray.size(), timeGranularity, maxAccuracy,language));
        jsonObject.set(ReportStructureEnum.CHARTS.value(), chartArray);

        return jsonObject;
    }

    /**
     * 查询原数据
     *
     * @param historyComplexIndexJsonArray 历史指标json数组
     * @param param                        查询参数
     * @param pageable                     分页对象
     * @return
     */
    private JSONObject queryOriginalData(JSONArray historyComplexIndexJsonArray, HistoryComplexIndexReportParam param, Pageable pageable,String language) {

        // 响应结果
        JSONObject result = new JSONObject(true);
        // 表数据
        JSONArray chartArray = new JSONArray();
        // 表头信息
        JSONArray thJsonArray = new JSONArray();
        List<HistoryComplexIndex> allHistoryComplexIndex = new ArrayList<>();

        for (int i = 0; i < historyComplexIndexJsonArray.size(); i++) {
            final int index = i;
            JSONObject object = historyComplexIndexJsonArray.getJSONObject(i);
            Date startTime = param.getStartTime();
            Date endTime = param.getEndTime();
            String complexIndexId = object.getStr(COMPLEX_INDEX_ID);
            // 查询历史数据
            List<HistoryComplexIndex> historyComplexIndexList = historyComplexIndexManager.findHistoryComplexIndexByIdAndDuration(startTime, endTime, Integer.valueOf(complexIndexId), pageable);
            // 计算数量
            Integer tempTotalElements = historyComplexIndexManager.countHistoryComplexIndexByIdAndDuration(startTime, endTime, complexIndexId);
            // 更新页数
            ReportUtil.updateTotalElement(result, tempTotalElements, pageable);
            // 更新 sortIndex
            if (CollUtil.isNotEmpty(historyComplexIndexList)) {
                historyComplexIndexList.forEach(e -> {
                    e.setSortIndex(index);
                    e.setDisplayValue(this.getDisplayValue(e.complexIndexId, e.getIndexValue()));
                });
            }
            allHistoryComplexIndex.addAll(historyComplexIndexList);
            // 添加表信息

            JSONObject chartObject = new JSONObject(true);
            chartObject.set(COMPLEX_INDEX_ID, object.getStr(COMPLEX_INDEX_ID));
            chartObject.set(UNIT, object.getStr(UNIT));
            chartObject.set(COMPLEX_INDEX_NAME, object.getStr(COMPLEX_INDEX_NAME));
            chartObject.set(POSITION, object.getStr(POSITION));
            chartObject.set(VALUES, historyComplexIndexList);
            chartArray.add(chartObject);

            JSONObject thJsonObject = new JSONObject(true);
            thJsonObject.set(POSITION, object.getStr(POSITION));
            thJsonObject.set(UNIT, object.getStr(UNIT));
            thJsonObject.set(COMPLEX_INDEX_NAME, object.getStr(COMPLEX_INDEX_NAME));
            thJsonArray.add(thJsonObject);
        }

        result.set(ReportStructureEnum.TITLE.value(), thJsonArray);
        result.set(ReportStructureEnum.RESULT.value(), getOriginalDataResultJsonArray(allHistoryComplexIndex, historyComplexIndexJsonArray.size(),language));
        result.set(ReportStructureEnum.CHARTS.value(), chartArray);
        result.set("type", "original");

        return result;
    }

    private static JSONArray getOriginalDataResultJsonArray(List<HistoryComplexIndex> historyComplexIndexList, Integer sortIndexLength,String language) {
        if (CollUtil.isEmpty(historyComplexIndexList)) {
            return new JSONArray();
        }
        historyComplexIndexList.sort((arg0, arg1) -> arg0.getTime().compareTo(arg1.getTime()) == 0 ? arg0.getSortIndex().compareTo(arg1.getSortIndex()) : arg0.getTime().compareTo(arg1.getTime()));

        JSONArray tdjsonArray = new JSONArray();
        int count = 0;
        String initTime = historyComplexIndexList.get(0).getTime();
        Integer maxAccuracy = 0;
        while (count < historyComplexIndexList.size()) {
            JSONObject tdJsonObject = new JSONObject(true);
            String tempTime = historyComplexIndexList.get(count).getTime();
            tdJsonObject.set("time", tempTime);
            JSONArray dataJsonArray = initOriginalDataRowDataJsonArray(sortIndexLength);
            while (tempTime.equals(initTime)) {
                Integer sortIndex = historyComplexIndexList.get(count).getSortIndex();
                JSONObject objectNode = dataJsonArray.getJSONObject(sortIndex);
                HistoryComplexIndex complexIndex = historyComplexIndexList.get(count);
                Integer accuracy = complexIndexAccuracyMap.get().get(complexIndex.getComplexIndexId()); // 精度
                if (maxAccuracy == 0 || accuracy > maxAccuracy) {
                    maxAccuracy = accuracy;
                }
                String value = new BigDecimal(complexIndex.getIndexValue()).setScale(accuracy, RoundingMode.UP).toPlainString();
                objectNode.set(VALUE, value);
                objectNode.set(DISPLAY_VALUE, value);
                if (++count < historyComplexIndexList.size()) {
                    tempTime = historyComplexIndexList.get(count).getTime();
                } else {
                    break;
                }
            }
            tdJsonObject.set("data", dataJsonArray);
            ReportUtil.setRowCount(dataJsonArray, tdJsonObject, maxAccuracy);
            tdjsonArray.add(tdJsonObject);

            if (count < historyComplexIndexList.size()) {
                initTime = tempTime;
            } else {
                break;
            }
        }
        if (!tdjsonArray.isEmpty()) {
            //原始数据统计行支持首值和尾值
            ReportUtil.countColumnJsonArrayConfig(tdjsonArray, VALUE,"max|min|avg|sum|first|last", maxAccuracy);
        }
        return tdjsonArray;
    }

    private static JSONArray initOriginalDataRowDataJsonArray(Integer sortIndexLength) {
        JSONArray dataJsonArray = new JSONArray();
        for (int i = 0; i < sortIndexLength; i++) {
            JSONObject dataJsonObject = new JSONObject(true);
            dataJsonObject.set(VALUE, "-");
            dataJsonArray.add(dataJsonObject);
        }
        return dataJsonArray;
    }

    /**
     * 获取时间粒度的元数据
     *
     * @param historyComplexIndexGroupByTimeList
     * @param sortIndexLength
     * @param timeGranularity
     * @return
     */
    private static JSONArray getTimeGranularityResultJsonArray(List<HistoryComplexIndexGroupByTime> historyComplexIndexGroupByTimeList, Integer sortIndexLength, String timeGranularity, Integer maxAccuracy,String language) {
        JSONArray tdJsonArray = new JSONArray();
        if (CollUtil.isEmpty(historyComplexIndexGroupByTimeList)) {
            return tdJsonArray;
        }
        //将sortIndexLength个点的数据进行排序,先按时间排序,再按下标排序
        historyComplexIndexGroupByTimeList.sort((arg0, arg1) -> arg0.getTime().compareTo(arg1.getTime()) == 0 ? arg0.getSortIndex().compareTo(arg1.getSortIndex()) : arg0.getTime().compareTo(arg1.getTime()));

        int count = 0;
        String initTime = historyComplexIndexGroupByTimeList.get(0).getTime();
        while (count < historyComplexIndexGroupByTimeList.size()) {
            JSONObject tdJsonObject = new JSONObject(true);
            String tempTime = historyComplexIndexGroupByTimeList.get(count).getTime();

            setTimeByTimeGranularity(tdJsonObject, timeGranularity, historyComplexIndexGroupByTimeList, count, tempTime);

            JSONArray dataJsonArray = initTimeGranularityRowDataJsonArray(sortIndexLength);
            //同个时间范围的归为一列
            while (tempTime.equals(initTime)) {
                Integer sortIndex = historyComplexIndexGroupByTimeList.get(count).getSortIndex();
                JSONObject itemJson = dataJsonArray.getJSONObject(sortIndex);
                itemJson.set("max", new BigDecimal(historyComplexIndexGroupByTimeList.get(count).getMax()).setScale(maxAccuracy, RoundingMode.UP).toPlainString());
                itemJson.set("min", new BigDecimal(historyComplexIndexGroupByTimeList.get(count).getMin()).setScale(maxAccuracy, RoundingMode.UP).toPlainString());
                itemJson.set("avg", new BigDecimal(historyComplexIndexGroupByTimeList.get(count).getAvg()).setScale(maxAccuracy, RoundingMode.UP).toPlainString());
                itemJson.set("sum", new BigDecimal(historyComplexIndexGroupByTimeList.get(count).getSum()).setScale(maxAccuracy, RoundingMode.UP).toPlainString());
                itemJson.set("first", new BigDecimal(historyComplexIndexGroupByTimeList.get(count).getFirst()).setScale(maxAccuracy, RoundingMode.UP).toPlainString());
                itemJson.set("last", new BigDecimal(historyComplexIndexGroupByTimeList.get(count).getLast()).setScale(maxAccuracy, RoundingMode.UP).toPlainString());
                if (++count >= historyComplexIndexGroupByTimeList.size()) {
                    break;
                }
                tempTime = historyComplexIndexGroupByTimeList.get(count).getTime();
            }
            tdJsonObject.set("data", dataJsonArray);

            ReportUtil.countTimeGranularityRowJsonArray(tdJsonObject, dataJsonArray, maxAccuracy);

            tdJsonArray.add(tdJsonObject);
            if (count < historyComplexIndexGroupByTimeList.size()) {
                initTime = tempTime;
            } else {
                break;
            }
        }
        if (!tdJsonArray.isEmpty()) {
            ReportUtil.countColumnJsonArrayConfig(tdJsonArray, "max|min|avg|sum|first|last","max|min|avg|sum", maxAccuracy);
        }
        return tdJsonArray;
    }

    /**
     * 当某个时间段至少有一个点的值不为空时,便需要显示该列,同列中值为空的用"-"代替,初始化该结构
     *
     * @param sortIndexLength 下标长度,既测点数量
     */
    private static JSONArray initTimeGranularityRowDataJsonArray(Integer sortIndexLength) {
        JSONArray dataJsonArray = new JSONArray();
        for (int i = 0; i < sortIndexLength; i++) {
            JSONObject dataJsonObject = new JSONObject(true);
            dataJsonObject.set("max", "-");
            dataJsonObject.set("min", "-");
            dataJsonObject.set("avg", "-");
            dataJsonObject.set("sum", "-");
            dataJsonObject.set("first", "-");
            dataJsonObject.set("last", "-");
            dataJsonArray.add(dataJsonObject);
        }
        return dataJsonArray;
    }

    private static void setTimeByTimeGranularity(JSONObject tdJsonObject, String timeGranularity, List<HistoryComplexIndexGroupByTime> historyComplexIndexGroupByTimeList, Integer count, String tempTime) {
        switch (timeGranularity) {
            case "1w":
                tdJsonObject.set("time", historyComplexIndexGroupByTimeList.get(count).getWeekDay());
                break;
            case "month":
                tdJsonObject.set("time", historyComplexIndexGroupByTimeList.get(count).getMonthDay());
                break;
            default:
                tdJsonObject.set("time", tempTime);
                break;
        }
    }

    /**
     * 精度处理(查询指标精度长度，默认两位)
     * @param complexIndexId 指标id
     * @param indexValue 指标值
     * @return 处理后的值
     */
    private String getDisplayValue(String complexIndexId, String indexValue) {
        Integer accuracy = complexIndexAccuracyMap.get().get(complexIndexId);
        if (Objects.isNull(accuracy)) {
            accuracy = 2;
            ComplexIndex complexIndex = complexIndexService.findByComplexIndexId(Integer.parseInt(complexIndexId));
            if (Objects.nonNull(complexIndex) && ObjectUtil.isNotEmpty(complexIndex.getAccuracy())) {
                int index = complexIndex.getAccuracy().indexOf(".");
                if (index > 0) {
                    accuracy = complexIndex.getAccuracy().substring(index).length() - 1;
                    if (accuracy <= 0) {
                        accuracy = 2;
                    }
                }
            }
            complexIndexAccuracyMap.get().put(complexIndexId, accuracy);
        }
        return new BigDecimal(indexValue).setScale(accuracy, RoundingMode.UP).toPlainString();
    }


    private HistoryComplexIndexParser() {
        super(ReportDataSourceEnum.HISTORY_COMPLEXINDEX.getValue());
    }
}
