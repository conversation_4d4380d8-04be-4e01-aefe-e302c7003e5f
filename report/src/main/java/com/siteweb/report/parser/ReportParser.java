package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.siteweb.report.vo.ColumnConfig;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.dto.IdValueDTO;

import java.util.*;

/**
 * @Author: lzy
 * @Date: 2022/5/23 14:31
 */
@SuppressWarnings("all")
public abstract class ReportParser {

    private static final Map<Integer, ReportParser> reportDataSourceMap = new HashMap<>();

    protected ReportParser(Integer parseNum) {
        /**
         * 初始化分发逻辑，替代IF-ELSE部分
         * Key ReportDataSourceId 报表数据源ID
         * Value lambda表达式，最终获取到查询数据
         */
        ReportParser reportParser = reportDataSourceMap.get(parseNum);
        if (reportParser != null) {
            throw new RuntimeException("重复的报表解析器");
        }
        reportDataSourceMap.put(parseNum, this);
    }

    public abstract JSONObject parser(ReportVO reportVO);

    public static JSONObject doParse(ReportVO reportVO) {
        ReportParser parse = reportDataSourceMap.get(reportVO.getReportDataSourceId());
        if (parse != null) {
            return parse.parser(reportVO);
        }
        return new JSONObject();
    }

    protected JSONObject buildTableHeader(List<ColumnConfig> customOrder, LinkedHashMap<String, String> defaultColumnMap) {
        LinkedHashMap<String, String> usedColumns;
        if (CollUtil.isNotEmpty(customOrder)) {
            // 按照 customOrder 顺序构建
            usedColumns = new LinkedHashMap<>();
            for (ColumnConfig columnConfig : customOrder) {
                if (defaultColumnMap.containsKey(columnConfig.getName()) && Boolean.TRUE.equals(columnConfig.getDisplay())) {
                    usedColumns.put(columnConfig.getName(), defaultColumnMap.get(columnConfig.getName()));
                }
            }
        } else {
            usedColumns = defaultColumnMap;
        }
        JSONObject thJsonObject = new JSONObject(true);
        for (Map.Entry<String, String> entry : usedColumns.entrySet()) {
            thJsonObject.set(entry.getKey(), entry.getValue());
        }
        return thJsonObject;
    }

    /**
     * 数量采样方法，主要用于统计部分报表所需查询的总数量，避免导出过大的数据量导致OOM
     * 如果子类未重写该方法表示导出无需判断可直接导出数据
     * @param reportVO
     * @return long
     */
    public long countSample(ReportVO reportVO){
        return 0;
    };

    public static long doCountSample(ReportVO reportVO) {
        ReportParser parse = reportDataSourceMap.get(reportVO.getReportDataSourceId());
        if (parse != null) {
            return parse.countSample(reportVO);
        }
        return 0;
    }

    /**
     * 导出报表数据，避免数据量过大查询超时或导致OOM，使用特殊的方法处理导出逻辑
     * 如果子类未重写该方法表示导出无需判断可直接导出数据
     *
     * @param reportVO 报表查询条件
     * @return long 一共导出数据的条数
     */
    public long export(ReportVO reportVO,String filePath) {
        return 0;
    }

    public static long doExport(ReportVO reportVO,String filePath) {
        ReportParser parse = reportDataSourceMap.get(reportVO.getReportDataSourceId());
        if (parse != null) {
            return parse.export(reportVO, filePath);
        }
        return 0;
    }


    /**
     * 获取报表列配置
     */
    public LinkedHashMap<String, String> getDefaultColumnMap(){
        return null;
    };

    public static List<IdValueDTO<String,String>> doColumnConfig(Integer reportDataSourceId) {
        ReportParser parse = reportDataSourceMap.get(reportDataSourceId);
        if (parse != null) {
            return buildColumnConfig(parse.getDefaultColumnMap());
        }
        return null;
    }

    private static List<IdValueDTO<String,String>> buildColumnConfig(LinkedHashMap<String, String> defaultColumnMap) {
        if (CollUtil.isEmpty(defaultColumnMap)) {
            return null;
        }
        List<IdValueDTO<String,String>> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : defaultColumnMap.entrySet()) {
            list.add(new IdValueDTO<>(entry.getKey(), entry.getValue()));
        }
        return list;
    }
}
