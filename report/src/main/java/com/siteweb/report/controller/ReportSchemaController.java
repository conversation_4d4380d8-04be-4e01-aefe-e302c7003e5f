package com.siteweb.report.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.service.ReportSchemaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @author: Habits
 * @time: 2022/4/29 13:15
 * @description:
 **/
@RestController
@RequestMapping("/api")
@Api(value = "ReportSchemaController", tags = {"报表Schema操作接口"})
public class ReportSchemaController {


    @Autowired
    ReportSchemaService reportSchemaService;


    @ApiOperation(value = "获取所有reportschema实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有reportschema实体",
                            content = {@Content})
            })
    @GetMapping("/reportschemas")
    public ResponseEntity<ResponseResult> getAllReportSchemas(@RequestParam("reportSchemaCategoryId")
                                                              @ApiParam(name = "reportSchemaCategoryId", value = "报表类型ID", required = true) Integer reportSchemaCategoryId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(reportSchemaService.findReportSchemasByUserIdAndCategoryId(userId, reportSchemaCategoryId), HttpStatus.OK);
    }

    @ApiOperation(value = "获取单个reportschema实体")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "返回单个reportschema实体", content = {@Content})})
    @GetMapping("/reportschemas/{reportSchemaId}")
    public ResponseEntity<ResponseResult> findReportSchemaByReportSchemaId(@PathVariable("reportSchemaId") @ApiParam(name = "reportSchemaId", value = "报表id", required = true) Integer reportSchemaId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(reportSchemaService.findReportSchemaByUserIdAndReportSchemaId(userId,reportSchemaId), HttpStatus.OK);
    }
}
