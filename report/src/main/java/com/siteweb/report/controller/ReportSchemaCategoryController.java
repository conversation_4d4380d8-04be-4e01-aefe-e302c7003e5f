package com.siteweb.report.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.service.ReportSchemaCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: Habits
 * @time: 2022/4/29 13:15
 * @description:
 **/
@RestController
@RequestMapping("/api")
@Api(value = "ReportSchemaCategoryController", tags = {"报表Schema类型操作接口"})
public class ReportSchemaCategoryController {


    @Autowired
    ReportSchemaCategoryService reportSchemaCategoryService;


    @ApiOperation(value = "获取所有reportschemacategory实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有reportschemacategory实体",
                            content = {@Content})
            })
    @GetMapping("/reportschemacategorys")
    public ResponseEntity<ResponseResult> getAllReportSchemaCategorys() {
        return ResponseHelper.successful(reportSchemaCategoryService.findReportSchemaCategorys(), HttpStatus.OK);
    }
}
