package com.siteweb.report.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.service.ReportExportParameterPresetService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "ReportExportParameterPresetController", tags = {"报表输出参数展示操作接口"})
public class ReportExportParameterPresetController {
    @Autowired
    ReportExportParameterPresetService reportExportParameterPresetService;
    @GetMapping("/reportschemaexportparameter")
    public ResponseEntity<ResponseResult> getReportSchemaExportParameter(Integer reportId){
        return ResponseHelper.successful(reportExportParameterPresetService.findReportExportParameterPresetByReportId(reportId));
    }
}
