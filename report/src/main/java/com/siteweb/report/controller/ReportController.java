package com.siteweb.report.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.entity.Report;
import com.siteweb.report.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * @author: Habits
 * @time: 2022/5/9 16:12
 * @description:
 **/
@RestController
@RequestMapping("/api")
@Api(value = "ReportController", tags = {"报表操作接口"})
public class ReportController {

    @Autowired
    ReportService reportService;

    @ApiOperation(value = "创建报表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "新报表已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "创建报表失败",
                            content = {@Content})
            })
    @PostMapping(value = "/reports", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createReport(@Valid @RequestBody Report report) {
        report.setCreateUserId(TokenUserUtil.getLoginUserId());
        Integer result = reportService.createReport(report);
        if (result > 0) {
            return ResponseHelper.successful(report);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create report error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "根据reportId查询单个Report实体")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回根据reportId查询到的Report实体",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "根据reportID查询不到report实体", content = @Content)
            })
    @GetMapping("/reports/{reportId}")
    public ResponseEntity<ResponseResult> findReportByReportId(
            @PathVariable("reportId") @ApiParam(name = "reportId", value = "报表ID", required = true)
                    Integer reportId) {
        Report report = reportService.findByReportId(reportId);
        return Optional.ofNullable(report)
                .map(result -> ResponseHelper.successful(report, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "按ReportId删除报表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "report删除成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据reportId找不到对应记录",
                            content = {@Content})
            })
    @DeleteMapping(value = "/reports/{reportId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteReportById(@PathVariable @ApiParam(name = "reportId", value = "报表ID", required = true) Integer reportId) {
        int result = reportService.deleteReportById(reportId);
        if (result < 0) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "修改报表")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "报表修改成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "修改报表失败",
                            content = {@Content})
            })
    @PutMapping(value = "/reports", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateReport(@Valid @RequestBody Report report) {
        report.setUpdateUserId(TokenUserUtil.getLoginUserId());
        int result = reportService.updateReport(report);
        if (result > 0) {
            return ResponseHelper.successful(report);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update report error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
