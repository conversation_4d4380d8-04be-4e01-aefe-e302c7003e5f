package com.siteweb.report.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.service.ReportTimingTaskTimeTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 报表定时任务时间类型
 * @Author: lzy
 * @Date: 2022/6/13 9:29
 */
@RestController
@RequestMapping("/api")
@Api(tags = "报表定时任务时间类型")
public class ReportTimingTaskTimeTypeController {

    @Autowired
    ReportTimingTaskTimeTypeService reportTimingTaskTimeTypeService;

    @ApiOperation("获取所有报表定时任务时间类型")
    @GetMapping(value = "/reporttimingtasktimetypes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findReportTimingTaskTimeTypeList() {
        return ResponseHelper.successful(reportTimingTaskTimeTypeService.findAll());
    }

}
