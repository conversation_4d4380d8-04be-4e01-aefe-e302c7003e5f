package com.siteweb.report.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.dto.ReportFolderTreeDTO;
import com.siteweb.report.entity.ReportFolder;
import com.siteweb.report.entity.ReportFolderMap;
import com.siteweb.report.service.ReportFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * author: john
 **/
@RestController
@RequestMapping("/api")
@Api(value = "ReportFolderController", tags = {"报表文件夹操作接口"})
@RequiredArgsConstructor
public class ReportFolderController {
    private final ReportFolderService reportFolderService;

    @ApiOperation(value = "获取报表树")
    @GetMapping("/reportfolders/tree")
    public ResponseEntity<ResponseResult> findReportFolderTree() {
        List<ReportFolderTreeDTO> list = reportFolderService.findReportFolderTree();
        return ResponseHelper.successful(list, HttpStatus.OK);
    }

    @ApiOperation(value = "新增reportfolder实体")
    @PostMapping("/reportfolders")
    public ResponseEntity<ResponseResult> saveReportFolder(@RequestBody ReportFolder reportFolder) {
        reportFolderService.saveReportFolder(reportFolder);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "修改reportfolder实体")
    @PutMapping("/reportfolders")
    public ResponseEntity<ResponseResult> updateReportFolder(@RequestBody ReportFolder reportFolder) {
        reportFolderService.updateReportFolder(reportFolder);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "获取reportfolder详情")
    @GetMapping("/reportfolders/{folderId}")
    public ResponseEntity<ResponseResult> findReportFolder(@PathVariable("folderId") Integer folderId) {
        ReportFolder reportFolder = reportFolderService.findReportFolder(folderId);
        return ResponseHelper.successful(reportFolder, HttpStatus.OK);
    }

    @ApiOperation(value = "删除reportfolder实体")
    @DeleteMapping("/reportfolders/{folderId}")
    public ResponseEntity<ResponseResult> deleteReportFolder(@PathVariable("folderId") Integer folderId) {
        reportFolderService.deleteReportFolder(folderId);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation(value = "新增修改报表和文件夹的挂载关系")
    @PostMapping("/reportfoldermap")
    public ResponseEntity<ResponseResult> saveOrUpdateReportFolderMap(@RequestBody ReportFolderMap reportFolderMap) {
        reportFolderService.saveOrUpdateReportFolderMap(reportFolderMap);
        return ResponseHelper.successful(HttpStatus.OK);
    }


}
