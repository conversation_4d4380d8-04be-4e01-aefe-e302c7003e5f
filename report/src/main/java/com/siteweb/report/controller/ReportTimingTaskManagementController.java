package com.siteweb.report.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.dto.ReportTimingTaskManagementDTO;
import com.siteweb.report.service.ReportTimingTaskManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: lzy
 * @Date: 2022/6/13 9:58
 */
@RestController
@RequestMapping("/api")
@Api(tags = "报表定时任务管理控制器")
public class ReportTimingTaskManagementController {

    @Autowired
    ReportTimingTaskManagementService reportTimingTaskManagementService;

    @ApiOperation("根据用户权限获取报表定时任务通")
    @GetMapping("/reporttimingtaskmanagements")
    public ResponseEntity<ResponseResult> findReportTimingTaskManagementList() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(reportTimingTaskManagementService.findByUserId(userId));
    }

    @ApiOperation("获取单个报表定时任务")
    @GetMapping("/reporttimingtaskmanagements/{id}")
    public ResponseEntity<ResponseResult> findReportTimingTaskManagement(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(reportTimingTaskManagementService.findOne(id));
    }

    @ApiOperation("新增报表定时任务")
    @PostMapping(value = "/reporttimingtaskmanagements")
    public ResponseEntity<ResponseResult> createReportTimingTaskManagement(@RequestBody ReportTimingTaskManagementDTO reportTimingTaskManagementDTO) {
        reportTimingTaskManagementDTO.setCreateUserId(TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful(reportTimingTaskManagementService.create(reportTimingTaskManagementDTO.build()));
    }

    @ApiOperation("修改报表定时任务")
    @PutMapping(value = "/reporttimingtaskmanagements")
    public ResponseEntity<ResponseResult> updateReportTimingTaskManagement(@RequestBody ReportTimingTaskManagementDTO reportTimingTaskManagementDTO) {
        return ResponseHelper.successful(reportTimingTaskManagementService.update(reportTimingTaskManagementDTO.build()));
    }

    @ApiOperation("删除报表定时任务")
    @DeleteMapping(value = "/reporttimingtaskmanagements/{id}")
    public ResponseEntity<ResponseResult> deleteReportTimingTaskManagement(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(reportTimingTaskManagementService.delete(id));
    }

}
