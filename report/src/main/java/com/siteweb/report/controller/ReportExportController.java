package com.siteweb.report.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.job.ReportExportJob;
import com.siteweb.report.manager.ReportDataManager;
import com.siteweb.report.service.ReportExportService;
import com.siteweb.report.vo.ReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api")
@Api(value = "ReportExportController", tags = {"报表数据导出接口"})
public class ReportExportController {
    @Autowired
    ReportExportService reportExportService;
    @Autowired
    ReportExportJob reportExportJob;
    @Autowired
    ReportDataManager reportDataManager;
    @ApiOperation("报表数量采样估算")
    @PostMapping(value = "/reportcountsample")
    public ResponseEntity<ResponseResult> historySignalReportCountSample(@Valid @RequestBody ReportVO reportVO) {
        reportVO.setUserId(TokenUserUtil.getLoginUserId());
        long countSample = reportDataManager.findCountSample(reportVO);
        return ResponseHelper.successful(countSample);
    }

    @SneakyThrows
    @ApiOperation(value = "报表导出csv格式")
    @PostMapping("/portexport/csv")
    public ResponseEntity<ResponseResult> historySignalCsvExport(@Valid @RequestBody ReportVO reportVO) {
        reportVO.setUserId(TokenUserUtil.getLoginUserId());
        reportExportJob.addReportTask(reportVO);
        return ResponseHelper.successful();
    }
}
