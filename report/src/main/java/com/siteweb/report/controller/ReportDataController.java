package com.siteweb.report.controller;

import com.siteweb.admin.language.LanguageUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.mamager.AlarmMaskTypeManager;
import com.siteweb.report.manager.ReportDataManager;
import com.siteweb.report.vo.ReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @author: Habits
 * @time: 2022/5/11 12:57
 * @description:
 **/
@RestController
@RequestMapping("/api")
@Api(value = "ReportController", tags = {"报表数据接口"})
public class ReportDataController {

    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    ReportDataManager reportDataManager;

    @ApiOperation(value = "报表数据查询")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "查询成功",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "500",
                            description = "查询失败",
                            content = {@Content})
            })
    @PostMapping(value = "/reportdatas", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> reportQuery(HttpServletRequest request, @Valid @RequestBody ReportVO reportVO) {
        reportVO.setUserId(TokenUserUtil.getLoginUserId());
        reportVO.setLanguage(request.getHeader(LanguageUtil.LANGUAGE_HEADER));
        return ResponseHelper.successful(reportDataManager.findResult(reportVO));
    }

    @ApiOperation(value = "获取告警屏蔽操作类型")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "查询成功", content = {@Content})})
    @GetMapping(value = "/findalarmmaskoperationtype")
    public ResponseEntity<ResponseResult> findAlarmMaskOperationType() {
        return ResponseHelper.successful(AlarmMaskTypeManager.getAllAlarmMaskOperationType());
    }

    @ApiOperation(value = "获取报表全部列配置")
    @ApiResponses(value = {@ApiResponse(responseCode = "200", description = "查询成功", content = {@Content})})
    @GetMapping(value = "/reportcolumnconfig")
    public ResponseEntity<ResponseResult> findAllReportColumnConfig(Integer reportDataSourceId) {
        return ResponseHelper.successful((reportDataManager.findAllReportColumnConfig(reportDataSourceId)));
    }

}
