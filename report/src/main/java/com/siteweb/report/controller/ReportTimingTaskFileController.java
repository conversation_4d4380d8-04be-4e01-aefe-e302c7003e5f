package com.siteweb.report.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.report.service.ReportTimingTaskFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/21 20:38
 */
@Api(tags = "报表定时任务文件")
@RestController
@RequestMapping("/api")
public class ReportTimingTaskFileController {

    @Autowired
    ReportTimingTaskFileService reportTimingTaskFileService;

    @ApiOperation("根据报表定时任务id查询任务文件")
    @GetMapping(value = "/reporttimingtaskfiles", params = {"reportTimingTaskManagementId"})
    public ResponseEntity<ResponseResult> findByReportTimingTaskManagementId(@RequestParam("reportTimingTaskManagementId") Integer reportTimingTaskManagementId,
                                                                             @RequestParam("pageSize") Integer pageSize,
                                                                             @RequestParam("pageNum") Integer pageNum,
                                                                             String keyword,
                                                                             @RequestParam(value="order", required = false, defaultValue = "DESC") String order
                                                                             ) {
        return ResponseHelper.successful(reportTimingTaskFileService.findByReportTimingTaskManagementId(reportTimingTaskManagementId, pageSize, pageNum, keyword, order));
    }

    @ApiOperation("根据任务文件id查询")
    @GetMapping("/reporttimingtaskfiles/{reportTimingTaskFileId}")
    public ResponseEntity<ResponseResult> getReportTimingTaskFileById(@PathVariable("reportTimingTaskFileId") Integer reportTimingTaskFileId) {
        return ResponseHelper.successful(reportTimingTaskFileService.findById(reportTimingTaskFileId));
    }

    @ApiOperation("根据任务文件id删除")
    @DeleteMapping("/reporttimingtaskfiles")
    public ResponseEntity<ResponseResult> deleteReportTimingTaskFile(@RequestParam List<Integer> reportTimingTaskFileIds) {
        reportTimingTaskFileService.deleteReportTimingTaskFile(reportTimingTaskFileIds);
        return ResponseHelper.successful();
    }

}
