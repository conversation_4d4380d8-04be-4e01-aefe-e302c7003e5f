package com.siteweb.report.enums;

@SuppressWarnings("all")
public enum TimeGranularityTypeEnum {
    START("start"),
    END("end"),
    AVG("avg"),
    MAX("max"),
    MIN("min"),
    SPREAD("spread"),
    DIFFERENCE("difference"),
    MAX_DIFFERENCE("maxdifference"),
    MONTH_DIFFERENCE("monthdifference");

    private String value;

    TimeGranularityTypeEnum(String value) {
        this.value = value;
    }

    public String getTimeGranularityType() {
        return value;
    }

    public static TimeGranularityTypeEnum getTimeGranularityTypeEnum(String timeGranularityType) {
        for (TimeGranularityTypeEnum timeGranularityTypeEnum : values()) {
            if (timeGranularityType.equals(timeGranularityTypeEnum.getTimeGranularityType())) {
                return timeGranularityTypeEnum;
            }
        }
        return null;
    }
}
