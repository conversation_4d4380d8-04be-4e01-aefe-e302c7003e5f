package com.siteweb.report.enums;

/**
 * 报表数据源枚举
 *
 * <AUTHOR>
 */
public enum ReportDataSourceEnum {
    // 当前告警
    LIVE_EVENTS(1),
    // 历史告警
    HISTORY_EVENTS(2),
    // 历史信号数据
    HISTORY_SIGNALS(3),
    // 用户操作日志
    USER_LOGS(4),
    // 告警操作日志
    ACTIVE_EVENTS_LOG(7),
    // 历史放电
    BATTERY_DISCHARGERECORD(8),
    // 历史指标
    HISTORY_COMPLEXINDEX(9),
    // 每日用电量
    DAILY_ELECTRICITY_CONSUMPTION(10),
    // 控制记录
    HISTORY_COMMAND_LOG(11),
    // 告警数量
    NUMBER_OF_EVENTS(12),
    // 日常巡检
    CURRENT_HEALTHY_STATE(13),
    // 告警屏蔽日志
    ALARM_MASK(15),
    // 告警发送通知
    ALARMNOTIFYRECORD(19),
    // 历史预警数据查询
    PREALARM_HISTORY(20),
    // 用电统计
    POWER_USAGE(21),
    // 通用定制
    GENERAL_CUSTOM(22),
    // 机架上下架报表
    RACK_RECORD(23),
    /**
     * 审计报表
     */
    AUDIT_REPORT(24),
    /**
     * 安全日志记录报表
     */
    SECURITY_REPORT(25),
    /**
     * 门禁报表
     */
    CARD_SWIPE(16),
    /**
     * 设备历史状态报表
     */
    EQUIPSTATE_REPORT(26),
    /**
     * 批量控制分组变更报表
     */
    BATCH_CONTROL_GROUP_CHANGE_LOG(27),
    /**
     * 批量下发控制命令历史记录报表
     */
    BATCH_CONTROL_CMD_HISTORY(28),
    /**
     * 群控设备分组历史变更报表
     */
    AUTO_CONTROL_EQUIP_CHANGE_LOG(29),
    /**
     * BA控制命令报表
     */
    BA_CONTROL_COMMAND(30),
    /**
     * 所有告警报表
     */
    ALL_EVENT(31),
    /**
     * 设备报表
     */
    EQUIPMENT_REPORT(32),
    /**
     * 历史数据(原始颗粒度)
     */
    HISTORY_SIGNAL_RAW_DATA(33),
    /**
     * 告警分类统计
     */
    EVENT_CLASSIFICATION_STATISTICAL(34),

    EQUIPMENT_SERIALPORT_INFO(35);
    private Integer value;

    ReportDataSourceEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static ReportDataSourceEnum getReportDataSourceEnum(Integer type) {
        for (ReportDataSourceEnum reportDataSourceEnum : values()) {
            if (type.equals(reportDataSourceEnum.getValue())) {
                return reportDataSourceEnum;
            }
        }
        return null;
    }
}
