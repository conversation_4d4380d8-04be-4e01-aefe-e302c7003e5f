package com.siteweb.report.enums;

import cn.hutool.json.JSONObject;
import org.springframework.data.domain.Pageable;

@SuppressWarnings("all")
public enum ReportStructureEnum {
    COLUMN1("column1"),
    COLUMN2("column2"),
    COLUMN3("column3"),
    COLUMN4("column4"),
    COLUMN5("column5"),
    COLUMN6("column6"),
    COLUMN7("column7"),
    COLUMN8("column8"),
    COLUMN9("column9"),
    COLUMN10("column10"),
    COLUMN11("column11"),
    COLUMN12("column12"),
    COLUMN13("column13"),
    COLUMN14("column14"),
    COLUM<PERSON>15("column15"),
    COLUMN16("column16"),
    COLUMN17("column17"),
    COLUMN18("column18"),
    COLUMN19("column19"),

    TITLE("title"),
    RESULT("result"),
    CHARTS("charts"),
    TOTALPAGES("totalPages"),
    TOTALELEMENTS("totalElements");

    private String value = "";

    ReportStructureEnum(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }

    public static ReportStructureEnum getReportStructureEnum(Integer index) {
        ReportStructureEnum reportStructureEnum = null;
        switch (index) {
            case 1:
                reportStructureEnum = COLUMN1;
                break;
            case 2:
                reportStructureEnum = COLUMN2;
                break;
            case 3:
                reportStructureEnum = COLUMN3;
                break;
            case 4:
                reportStructureEnum = COLUMN4;
                break;
            case 5:
                reportStructureEnum = COLUMN5;
                break;
            case 6:
                reportStructureEnum = COLUMN6;
                break;
            case 7:
                reportStructureEnum = COLUMN7;
                break;
            case 8:
                reportStructureEnum = COLUMN8;
                break;
            case 9:
                reportStructureEnum = COLUMN9;
                break;
            case 10:
                reportStructureEnum = COLUMN10;
                break;
            case 11:
                reportStructureEnum = COLUMN11;
                break;
            case 12:
                reportStructureEnum = COLUMN12;
                break;
            case 13:
                reportStructureEnum = COLUMN13;
                break;
            case 14:
                reportStructureEnum = COLUMN14;
                break;
            default:
                break;
        }
        return reportStructureEnum;
    }


    /**
     * 更新总数
     *
     * @param jsonObject        待返回的jsonObject
     * @param tempTotalElements 计算总数
     * @param pageable          页对象
     */
    public static void updateTotalElement(JSONObject jsonObject, Integer tempTotalElements, Pageable pageable) {
        if (pageable != null) {
            jsonObject.set(TOTALELEMENTS.value(), tempTotalElements);
            jsonObject.set(TOTALPAGES.value(), tempTotalElements / pageable.getPageSize() + 1);
        }
    }
}
