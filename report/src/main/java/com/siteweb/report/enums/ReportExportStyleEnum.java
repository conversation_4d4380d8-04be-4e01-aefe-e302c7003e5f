package com.siteweb.report.enums;

/**
 * 报表导出风格枚举
 *
 * <AUTHOR>
 */
public enum ReportExportStyleEnum {
    // 通用
    BASE_DATA(1),
    // 历史数据，历史指标
    INFLUXDB_DATA(2),
    // 电池
    BATTERY_DATA(3),
    // 自定义
    CUSTOM_DATA(4),
    // 指标
    COMPLEXINDEX(5);

    private Integer value;

    ReportExportStyleEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static ReportExportStyleEnum getReportExportStyleEnum(Integer type) {
        for (ReportExportStyleEnum reportExportStyleEnum : values()) {
            if (type.equals(reportExportStyleEnum.getValue())) {
                return reportExportStyleEnum;
            }
        }
        return null;
    }
}
