package com.siteweb.report.enums;


/**
 * 分页枚举
 *
 * <AUTHOR>
 */
public enum ReportDisplayStyleEnum {
    // 前端分页
    BASE_DATA_FONT_PAGING(1),
    // 后端分页
    BASE_DATA_BACKEND_PAGING(2),
    // 历史数据，历史指标
    INFLUXDB_DATA(3),
    // 电池
    BATTERY_DATA(4),
    // 即时报表
    CUSTOM_DATA_SIMPLE(5),
    // 通用自定义报表
    CUSTOM_DATA_COMPLEX(6);

    private Integer value;

    ReportDisplayStyleEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static ReportDisplayStyleEnum getReportDisplayStyleEnum(Integer type) {
        for (ReportDisplayStyleEnum reportDisplayStyleEnum : values()) {
            if (type.equals(reportDisplayStyleEnum.getValue())) {
                return reportDisplayStyleEnum;
            }
        }
        return null;
    }
}
