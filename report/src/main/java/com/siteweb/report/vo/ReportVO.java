package com.siteweb.report.vo;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.admin.language.LanguageUtil;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @author: Habits
 * @time: 2022/5/11 10:40
 * @description:
 **/
@Data
@NoArgsConstructor
public class ReportVO {

    private Integer reportId;

    private String reportName;

    private String reportDescription;

    private Integer reportSchemaId;

    private Integer reportSchemaCategoryId;

    private Long updateUserId;

    private Date updateTime;

    private Integer reportDataSourceId;

    @JsonProperty("page") // 序列化page转current，前端不用修改参数名
    private Integer current;

    private Integer size;

    private String sort;

    private Integer userId;

    /**
     * 语言
     */
    private String language;

    private Collection<ReportParameterPreset> reportParameterPresetList;

    private Collection<ReportExportParameterPreset> reportExportParameterPresetList;

    private List<ColumnConfig> columnConfig;

    public Pageable getPageable() {
        Pageable pageable = null;
        if (this.current != null && this.size != null) {
            if (CharSequenceUtil.isNotEmpty(sort)) {
                Sort sortObj = Sort.by(Sort.Direction.DESC, this.sort);
                pageable = PageRequest.of(current, size, sortObj);
            }else {
                pageable = PageRequest.of(current, size);
            }
        }
        return pageable;
    }

    public String getLanguage() {
        if (CharSequenceUtil.isBlank(this.language)) {
            return LanguageUtil.ZH_CN;
        }
        return language;
    }
}
