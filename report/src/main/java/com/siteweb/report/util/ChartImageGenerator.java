package com.siteweb.report.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 从JSON数据生成图表图像的工具类
 */
@Slf4j
@Component
public class ChartImageGenerator {

    private static LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    public void setMessageSourceUtil(LocaleMessageSourceUtil messageSourceUtil) {
        ChartImageGenerator.messageSourceUtil = messageSourceUtil;
    }

    private static final int WIDTH = 1800;
    private static final int HEIGHT = 1200;
    private static final int PADDING = 60;
    private static final int BOTTOM_PADDING = 250; // 底部留出空间用于图例
    private static final int LABEL_PADDING = 30;
    private static final int POINT_SIZE = 5;
    private static final int Y_DIVISIONS = 10;
    private static final Font TITLE_FONT = new Font("Microsoft YaHei", Font.BOLD, 18);
    private static final Font LABEL_FONT = new Font("Microsoft YaHei", Font.PLAIN, 12);

    // 备选字体，如果Microsoft YaHei不可�?
    private static final String[] CHINESE_FONTS = {
        "Microsoft YaHei", "SimSun", "NSimSun", "FangSong", "SimHei", "KaiTi", "STXihei", "STKaiti", "STSong", "STFangsong"
    };
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.##");

    /**
     * 从提供的图表数据生成折线图图�?
     *
     * @param chartData JSON对象，包含图表数�?
     * @param title 图表标题
     * @param type 要提取的值类�?("max", "min", "sum", "first", "last", "avg", �?"displayValue")
     * @return 包含渲染后图表的BufferedImage
     */
    public static BufferedImage generateLineChart(JSONObject chartData, String title, String type) {
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = image.createGraphics();

        // 设置渲染提示以提高质�?
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 填充背景
        g2.setColor(Color.WHITE);
        g2.fillRect(0, 0, WIDTH, HEIGHT);

        // 绘制标题
        g2.setColor(Color.BLACK);
        // 使用支持中文的字�?
        Font chineseTitleFont = getChineseFont(TITLE_FONT.getSize());
        g2.setFont(chineseTitleFont);
        FontMetrics titleMetrics = g2.getFontMetrics();
        int titleWidth = titleMetrics.stringWidth(title);
        g2.drawString(title, (WIDTH - titleWidth) / 2, titleMetrics.getHeight());

        // 获取图表数据
        JSONArray charts = chartData.getJSONArray("charts");
        if (charts == null || charts.isEmpty()) {
            g2.dispose();
            return image;
        }

        // 提取数据
        List<Series> allSeries = extractSeriesData(charts, type);
        if (allSeries.isEmpty()) {
            g2.dispose();
            return image;
        }

        // 找出缩放的最小和最大�?
        double minValue = Double.MAX_VALUE;
        double maxValue = Double.MIN_VALUE;
        List<String> timeLabels = new ArrayList<>();

        for (Series series : allSeries) {
            for (DataPoint point : series.getPoints()) {
                minValue = Math.min(minValue, point.getValue());
                maxValue = Math.max(maxValue, point.getValue());
                if (!timeLabels.contains(point.getTime())) {
                    timeLabels.add(point.getTime());
                }
            }
        }

        // Adjust min/max for better visualization
        double range = maxValue - minValue;
        minValue = Math.max(0, minValue - range * 0.1);
        maxValue = maxValue + range * 0.1;

        // Draw axes
        g2.setColor(Color.BLACK);
        // 使用支持中文的字�?
        Font chineseLabelFont = getChineseFont(LABEL_FONT.getSize());
        g2.setFont(chineseLabelFont);
        FontMetrics metrics = g2.getFontMetrics();

        // Draw Y-axis
        int chartHeight = HEIGHT - PADDING - BOTTOM_PADDING; // 考虑底部更大的padding
        int chartWidth = WIDTH - 2 * PADDING;

        // Draw Y-axis labels and grid lines
        g2.setColor(Color.LIGHT_GRAY);
        for (int i = 0; i <= Y_DIVISIONS; i++) {
            int y = HEIGHT - BOTTOM_PADDING - (i * chartHeight / Y_DIVISIONS); // 考虑底部更大的padding
            double value = minValue + (i * (maxValue - minValue) / Y_DIVISIONS);
            String label = DECIMAL_FORMAT.format(value);

            // Grid line
            g2.drawLine(PADDING, y, WIDTH - PADDING, y);

            // Label
            g2.setColor(Color.BLACK);
            g2.drawString(label, PADDING - metrics.stringWidth(label) - 5, y + metrics.getHeight() / 2 - 3);
            g2.setColor(Color.LIGHT_GRAY);
        }

        // Draw X-axis labels and grid lines
        if (!timeLabels.isEmpty()) {
            // 判断是否所有时间都在同一�?
            boolean allSameDay = true;
            String firstDay = null;

            if (timeLabels.size() > 1) {
                // 假设时间格式�?"yyyy-MM-dd HH:mm:ss" 或类似格�?
                firstDay = timeLabels.get(0).split(" ")[0]; // 获取第一个时间的日期部分

                for (int i = 1; i < timeLabels.size(); i++) {
                    String currentDay = timeLabels.get(i).split(" ")[0];
                    if (!firstDay.equals(currentDay)) {
                        allSameDay = false;
                        break;
                    }
                }
            }

            // 根据是否同一天决定标签数量和显示格式
            int availableWidth = chartWidth;
            int minSpaceBetweenLabels;
            int maxLabels;

            if (allSameDay && timeLabels.size() > 4) {
                // 如果是同一天，可以显示更多标签，因为只显示时间部分
                minSpaceBetweenLabels = 150; // 每个标签至少需�?60 像素的空�?
                maxLabels = Math.max(4, availableWidth / minSpaceBetweenLabels);
            } else {
                // 如果不是同一天，需要显示完整日期，所以标签更�?
                minSpaceBetweenLabels = 250; // 每个标签至少需�?100 像素的空�?
                maxLabels = Math.max(2, availableWidth / minSpaceBetweenLabels);
            }

            int numLabels = Math.min(timeLabels.size(), maxLabels);
            int step = Math.max(1, timeLabels.size() / numLabels);
            boolean showFirstAndLast = true;

            // 绘制所有的网格�?
            for (int i = 0; i < timeLabels.size(); i++) {
                int x;
                if (timeLabels.size() == 1) {
                    // 如果只有一个时间点，将其放在中心位置
                    x = PADDING + chartWidth / 2;
                } else {
                    x = PADDING + (i * chartWidth / (timeLabels.size() - 1));
                }
                g2.setColor(Color.LIGHT_GRAY);
                g2.drawLine(x, PADDING, x, HEIGHT - BOTTOM_PADDING); // 考虑底部更大的padding
            }

            // 绘制选定的标�?
            for (int i = 0; i < timeLabels.size(); i += step) {
                int x;
                if (timeLabels.size() == 1) {
                    // 如果只有一个时间点，将其放在中心位置
                    x = PADDING + chartWidth / 2;
                } else {
                    x = PADDING + (i * chartWidth / (timeLabels.size() - 1));
                }
                String originalLabel = timeLabels.get(i);
                String label;

                // 如果是同一天，只显示时间部�?
                if (allSameDay && originalLabel.contains(" ") && timeLabels.size() > 4) {
                    label = originalLabel.split(" ")[1]; // 只显示时间部�?
                } else {
                    label = originalLabel;
                }

                // 标签
                g2.setColor(Color.BLACK);
                int labelWidth = metrics.stringWidth(label);
                g2.drawString(label, x - labelWidth / 2, HEIGHT - BOTTOM_PADDING + metrics.getHeight() + 2);
            }

            // 确保显示最后一个标签（如果没有被步长选中�?
            if (showFirstAndLast && timeLabels.size() > 1 && (timeLabels.size() - 1) % step != 0) {
                int lastIndex = timeLabels.size() - 1;
                int x;
                if (timeLabels.size() == 1) {
                    // 如果只有一个时间点，将其放在中心位置
                    x = PADDING + chartWidth / 2;
                } else {
                    x = PADDING + (lastIndex * chartWidth / (timeLabels.size() - 1));
                }
                String originalLabel = timeLabels.get(lastIndex);
                String label;

                // 如果是同一天，只显示时间部�?
                if (allSameDay && originalLabel.contains(" ")) {
                    label = originalLabel.split(" ")[1]; // 只显示时间部�?
                } else {
                    label = originalLabel;
                }

                g2.setColor(Color.BLACK);
                int labelWidth = metrics.stringWidth(label);
                g2.drawString(label, x - labelWidth / 2, HEIGHT - BOTTOM_PADDING + metrics.getHeight() + 2);
            }

            // 如果是同一天，在图表下方显示日�?
            if (allSameDay && firstDay != null) {
                g2.setColor(Color.BLACK);
                Font dateFont = getChineseFont(LABEL_FONT.getSize() + 1);
                g2.setFont(dateFont);
                String dateLabel = messageSourceUtil.getMessage("report.chartImageGenerator.date.format", new Object[]{firstDay});
                int dateLabelWidth = g2.getFontMetrics().stringWidth(dateLabel);
                g2.drawString(dateLabel, PADDING, HEIGHT - BOTTOM_PADDING + metrics.getHeight() + 30);
                g2.setFont(chineseLabelFont); // 恢复字体
            }
        }

        // Draw axes
        g2.setColor(Color.BLACK);
        g2.drawLine(PADDING, PADDING, PADDING, HEIGHT - BOTTOM_PADDING);
        g2.drawLine(PADDING, HEIGHT - BOTTOM_PADDING, WIDTH - PADDING, HEIGHT - BOTTOM_PADDING); // X-axis

        // Draw axis labels - 确保使用支持中文的字�?
        Font axisLabelFont = getChineseFont(LABEL_FONT.getSize() + 2);
        g2.setFont(axisLabelFont);
        g2.drawString(messageSourceUtil.getMessage("report.chartImageGenerator.axis.time"), WIDTH / 2, HEIGHT - BOTTOM_PADDING + 40); // 考虑底部更大的padding

        // Rotate and draw Y-axis label
        g2.translate(PADDING / 3, HEIGHT / 2 - BOTTOM_PADDING / 4);
        g2.rotate(-Math.PI / 2);
        g2.drawString(messageSourceUtil.getMessage("report.chartImageGenerator.axis.value"), 0, 0);
        g2.rotate(Math.PI / 2);
        g2.translate(-PADDING / 3, -(HEIGHT / 2 - BOTTOM_PADDING / 4));

        // 恢复到原来的字体
        g2.setFont(chineseLabelFont);

        // 绘制数据系列
        Random random = new Random(123); // 固定种子以保持颜色一致�?
        for (Series series : allSeries) {
            List<DataPoint> points = series.getPoints();
            // 允许处理只有一个点的数据系列
            if (points.isEmpty()) continue;

            // Generate a random color for this series
            Color seriesColor = new Color(
                    random.nextInt(200),
                    random.nextInt(200),
                    random.nextInt(200)
            );
            g2.setColor(seriesColor);

            // Draw lines between points
            if (points.size() > 1 && timeLabels.size() > 1) {
                for (int i = 0; i < points.size() - 1; i++) {
                    DataPoint current = points.get(i);
                    DataPoint next = points.get(i + 1);

                    int currentTimeIndex = timeLabels.indexOf(current.getTime());
                    int nextTimeIndex = timeLabels.indexOf(next.getTime());

                    if (currentTimeIndex >= 0 && nextTimeIndex >= 0) {
                        int x1 = PADDING + (currentTimeIndex * chartWidth / (timeLabels.size() - 1));
                        int x2 = PADDING + (nextTimeIndex * chartWidth / (timeLabels.size() - 1));
                        int y1 = HEIGHT - BOTTOM_PADDING - (int)((current.getValue() - minValue) * chartHeight / (maxValue - minValue));
                        int y2 = HEIGHT - BOTTOM_PADDING - (int)((next.getValue() - minValue) * chartHeight / (maxValue - minValue));

                        g2.drawLine(x1, y1, x2, y2);
                    }
                }
            }

            // Draw points
            for (int i = 0; i < points.size(); i++) {
                DataPoint point = points.get(i);
                int timeIndex = timeLabels.indexOf(point.getTime());

                if (timeIndex >= 0) {
                    int x;
                    if (timeLabels.size() == 1) {
                        // 如果只有一个时间点，将其放在中心位置
                        x = PADDING + chartWidth / 2;
                    } else {
                        x = PADDING + (timeIndex * chartWidth / (timeLabels.size() - 1));
                    }
                    int y = HEIGHT - BOTTOM_PADDING - (int)((point.getValue() - minValue) * chartHeight / (maxValue - minValue));

                    // 如果只有一个时间点，绘制更大的点，使其更加明显
                    if (timeLabels.size() == 1) {
                        // 绘制更大的点
                        int largerPointSize = POINT_SIZE * 3;
                        g2.fillOval(x - largerPointSize / 2, y - largerPointSize / 2, largerPointSize, largerPointSize);

                        // 绘制点周围的圆环增强效果
                        g2.setColor(Color.WHITE);
                        g2.fillOval(x - largerPointSize / 3, y - largerPointSize / 3, largerPointSize * 2/3, largerPointSize * 2/3);
                        g2.setColor(seriesColor);
                        g2.fillOval(x - POINT_SIZE, y - POINT_SIZE, POINT_SIZE * 2, POINT_SIZE * 2);
                    } else {
                        // 正常绘制点
                        g2.fillOval(x - POINT_SIZE / 2, y - POINT_SIZE / 2, POINT_SIZE, POINT_SIZE);
                    }
                }
            }
        }

        // Draw legend
        drawLegend(g2, allSeries);

        g2.dispose();
        return image;
    }

    /**
     * 绘制图表图例
     */
    private static void drawLegend(Graphics2D g2, List<Series> allSeries) {
        if (allSeries.isEmpty()) return;

        // 确保使用支持中文的字�?
        Font originalFont = g2.getFont();
        Font chineseFont = getChineseFont(originalFont.getSize());
        g2.setFont(chineseFont);
        FontMetrics metrics = g2.getFontMetrics();

        // 图例项的基本参数
        int lineLength = 20;
        int lineSpacing = 25; // 行间�?
        int horizontalSpacing = 30; // 图例项之间的水平间距
        int minItemWidth = 100; // 最小图例项宽度

        // 计算每个图例项的宽度（基于文本长度）
        int[] itemWidths = new int[allSeries.size()];
        int maxItemWidth = minItemWidth;

        for (int i = 0; i < allSeries.size(); i++) {
            String name = allSeries.get(i).getName();
            int textWidth = metrics.stringWidth(name);
            itemWidths[i] = textWidth + lineLength + 15; // 线段长度 + 文本宽度 + 间距
            maxItemWidth = Math.max(maxItemWidth, itemWidths[i]);
        }

        // 计算每行可以放置的图例项数量
        int availableWidth = WIDTH - 2 * PADDING;
        int itemsPerRow = Math.max(1, availableWidth / (maxItemWidth + horizontalSpacing));

        // 如果图例项太多，尝试更紧凑的布局
        if (allSeries.size() > 10 && itemsPerRow == 1) {
            // 使用动态宽度而不是最大宽�?
            int totalWidth = 0;
            for (int width : itemWidths) {
                totalWidth += width + horizontalSpacing;
            }

            // 重新计算每行的图例项数量
            itemsPerRow = Math.max(2, Math.min(allSeries.size(), availableWidth * allSeries.size() / totalWidth));
        }

        // 计算行数
        int rows = (int) Math.ceil((double) allSeries.size() / itemsPerRow);

        // 计算图例区域
        int legendWidth = Math.min(WIDTH - 2 * PADDING, availableWidth);
        int legendHeight = rows * lineSpacing + 20; // 增加高度以确保有足够空间

        // 计算图例区域的位置（底部中央
        int legendX = (WIDTH - legendWidth) / 2;
        int legendY = HEIGHT - BOTTOM_PADDING + 70; // 将图例往下移动，避免遮挡X轴标�?
        // 绘制图例背景
        g2.setColor(new Color(255, 255, 255, 200));
        g2.fillRect(legendX, legendY, legendWidth, legendHeight);
        g2.setColor(Color.GRAY);
        g2.drawRect(legendX, legendY, legendWidth, legendHeight);

        // 绘制图例�?
        Random random = new Random(123); // 使用固定种子以保持颜色一致�?
        for (int i = 0; i < allSeries.size(); i++) {
            Series series = allSeries.get(i);

            // 计算行和�?
            int row = i / itemsPerRow;
            int col = i % itemsPerRow;

            // 计算当前图例项的位置
            int itemX = legendX + 10 + col * (legendWidth / itemsPerRow);
            int itemY = legendY + 15 + row * lineSpacing;

            // 使用与系列相同的颜色生成逻辑
            Color seriesColor = new Color(
                    random.nextInt(200),
                    random.nextInt(200),
                    random.nextInt(200)
            );
            g2.setColor(seriesColor);

            // 绘制线段
            g2.drawLine(itemX, itemY, itemX + lineLength, itemY);

            // 绘制�?
            g2.fillOval(itemX + lineLength/2 - POINT_SIZE/2, itemY - POINT_SIZE/2, POINT_SIZE, POINT_SIZE);

            // 绘制标签
            g2.setColor(Color.BLACK);
            g2.drawString(series.getName(), itemX + lineLength + 5, itemY + 5);
        }

        // 恢复原始字体
        g2.setFont(originalFont);
    }

    /**
     * 获取支持中文的字�?
     *
     * @param fontSize 字体大小
     * @return 支持中文的字�?
     */
    private static Font getChineseFont(int fontSize) {
        // 首先尝试使用微软雅黑
        Font font = new Font("Microsoft YaHei", Font.PLAIN, fontSize);

        // 如果微软雅黑不可用，尝试其他中文字体
        String testText = messageSourceUtil.getMessage("report.chartImageGenerator.font.test");
        if (font.canDisplayUpTo(testText) != -1) {
            for (String fontName : CHINESE_FONTS) {
                font = new Font(fontName, Font.PLAIN, fontSize);
                if (font.canDisplayUpTo(testText) == -1) {
                    break;
                }
            }
        }

        return font;
    }

    /**
     * 使用指定的值类型从JSON数组中提取数据系�?
     */
    private static List<Series> extractSeriesData(JSONArray charts, String type) {
        // 根据类型创建数据点提取器
        DataPointExtractor extractor = (valueObj) -> {
            String time = valueObj.getStr("time");
            Double value = null;

            // 尝试使用不同的可能键获取
            switch (type) {
                case "max":
                    value = valueObj.getDouble("max");
                    break;
                case "min":
                    value = valueObj.getDouble("min");
                    break;
                case "sum":
                    value = valueObj.getDouble("sum");
                    break;
                case "first":
                    value = valueObj.getDouble("first");
                    break;
                case "last":
                    value = valueObj.getDouble("last");
                    break;
                case "avg":
                default:
                    value = valueObj.getDouble("displayValue");
                    break;
            }

            return (time != null && value != null) ? new DataPoint(time, value) : null;
        };

        // 使用通用提取方法
        return extractSeriesDataGeneric(charts, extractor);
    }

    /**
     * 使用自定义提取器从JSON数组中提取数据系列的通用方法
     *
     * @param charts 包含图表数据的JSON数组
     * @param extractor 从每个值对象中提取数据点的函数
     * @return 包含提取数据的Series对象列表
     */
    public static List<Series> extractSeriesDataGeneric(JSONArray charts, DataPointExtractor extractor) {
        List<Series> result = new ArrayList<>();

        for (int i = 0; i < charts.size(); i++) {
            JSONObject chartObj = charts.getJSONObject(i);
            String seriesName = getSeriesName(chartObj);

            // 先尝试获取系列数组（对于嵌套结构�?
            JSONArray seriesArray = chartObj.getJSONArray("series");

            if (seriesArray != null && !seriesArray.isEmpty()) {
                // 处理嵌套系列结构（如BatteryChartGenerator中的�?
                for (int j = 0; j < seriesArray.size(); j++) {
                    JSONObject seriesObj = seriesArray.getJSONObject(j);
                    String nestedSeriesName = seriesObj.getStr("name");
                    if (nestedSeriesName != null && !nestedSeriesName.isEmpty()) {
                        seriesName = nestedSeriesName;
                    }

                    JSONArray values = seriesObj.getJSONArray("values");
                    if (values == null || values.isEmpty()) continue;

                    List<DataPoint> points = extractDataPoints(values, extractor);

                    if (!points.isEmpty()) {
                        result.add(new Series(seriesName, points));
                    }
                }
            } else {
                // 处理扁平结构（直接值数组）
                JSONArray values = chartObj.getJSONArray("values");
                if (values == null || values.isEmpty()) continue;

                List<DataPoint> points = extractDataPoints(values, extractor);

                if (!points.isEmpty()) {
                    result.add(new Series(seriesName, points));
                }
            }
        }

        return result;
    }

    /**
     * 使用提供的提取器从值数组中提取数据点的辅助方法
     */
    private static List<DataPoint> extractDataPoints(JSONArray values, DataPointExtractor extractor) {
        List<DataPoint> points = new ArrayList<>();

        for (int j = 0; j < values.size(); j++) {
            JSONObject valueObj = values.getJSONObject(j);
            DataPoint point = extractor.extract(valueObj);

            if (point != null) {
                points.add(point);
            }
        }

        return points;
    }

    /**
     * 从图表对象中获取系列名称，处理不同的可能键结�?
     */
    private static String getSeriesName(JSONObject chartObj) {
        StringBuilder name = new StringBuilder();

        // 尝试不同的可能名称键
        if (chartObj.containsKey("complexIndexName")) {
            name.append(chartObj.getStr("complexIndexName"));
        } else if (chartObj.containsKey("pointName")) {
            name.append(chartObj.getStr("pointName"));
        } else if (chartObj.containsKey("signalName")) {
            name.append(chartObj.getStr("signalName"));
        }

        // 如果可用，添加位�?设备名称
        if (chartObj.containsKey("position") && chartObj.getStr("position") != null) {
            name.append("(").append(chartObj.getStr("position")).append(")");
        } else if (chartObj.containsKey("equipmentName") && chartObj.getStr("equipmentName") != null) {
            name.append("(").append(chartObj.getStr("equipmentName")).append(")");
        }

        return name.toString();
    }

    /**
     * 表示数据系列的类
     */
    private static class Series {
        private final String name;
        private final List<DataPoint> points;

        public Series(String name, List<DataPoint> points) {
            this.name = name;
            this.points = points;
        }

        public String getName() {
            return name;
        }

        public List<DataPoint> getPoints() {
            return points;
        }
    }

    /**
     * 表示数据点的�?
     */
    private static class DataPoint {
        private final String time;
        private final double value;
        private final double xValue; // 用于具有自定义X轴值的图表（例如，电压与容量）

        public DataPoint(String time, double value) {
            this.time = time;
            this.value = value;
            this.xValue = 0;
        }

        public DataPoint(String time, double value, double xValue) {
            this.time = time;
            this.value = value;
            this.xValue = xValue;
        }

        public String getTime() {
            return time;
        }

        public double getValue() {
            return value;
        }

        public double getXValue() {
            return xValue;
        }
    }

    /**
     * 用于自定义数据点提取的函数式接口
     */
    @FunctionalInterface
    public interface DataPointExtractor {
        DataPoint extract(JSONObject valueObj);
    }
}

