package com.siteweb.report.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.admin.security.LanguageFilter;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.JsonArrayUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.report.enums.ReportStructureEnum;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Component
public class ReportUtil {

    private static final String VALUE = "value";
    private static final String DISPLAY_VALUE = "displayValue";

    private ReportUtil() {
    }

    /**
     * 计算每一行的最大值，最小值，平均值，累加值
     *
     * @param dataJsonArray 原始数据报表
     * @param tdJsonObject  报表表体的行对象
     */
    public static void setRowCount(JSONArray dataJsonArray, JSONObject tdJsonObject) {
        setRowCount(dataJsonArray, tdJsonObject, 2);
    }

    /**
     * 计算每一行的最大值，最小值，平均值，累加值
     *
     * @param dataJsonArray 原始数据报表
     * @param tdJsonObject  报表表体的行对象
     * @param accuracy  精度
     */
    public static void setRowCount(JSONArray dataJsonArray, JSONObject tdJsonObject, Integer accuracy) {
        List<BigDecimal> dataList = JsonArrayUtil.jsonArrayToDoubleList(dataJsonArray, VALUE);
        if (!dataList.isEmpty()) {
            tdJsonObject.set("max", NumberUtil.getBigDecimalMaxData(dataList, accuracy).toPlainString());
            tdJsonObject.set("min", NumberUtil.getBigDecimalMinData(dataList, accuracy).toPlainString());
            BigDecimal sum = NumberUtil.getBigDecimalSumData(dataList, accuracy);
            tdJsonObject.set("sum", sum.toPlainString());
            tdJsonObject.set("avg", sum.divide(new BigDecimal(dataJsonArray.size()), RoundingMode.UP).toPlainString());
        } else {
            tdJsonObject.set("max", "-");
            tdJsonObject.set("min", "-");
            tdJsonObject.set("sum", "-");
            tdJsonObject.set("avg", "-");
        }
    }

    /**
     * 获取每一列的最大值，最小值，平均值，累加值
     *
     * @param tdJsonArray 报表结构对象
     */
    public static void countColumnJsonArray(JSONArray tdJsonArray, String key) {
        ReportUtil.countColumnJsonArray(tdJsonArray, key, 2);
    }

    /**
     * 获取每一列的最大值，最小值，平均值，累加值
     *
     * @param tdJsonArray 报表结构对象
     */
    public static void countColumnJsonArray(JSONArray tdJsonArray, String key, Integer accuracy) {
        Map<String, JSONObject> countResult = getColumnCountResult(tdJsonArray, key, accuracy);
        tdJsonArray.add(countResult.get("max"));
        tdJsonArray.add(countResult.get("min"));
        tdJsonArray.add(countResult.get("sum"));
        tdJsonArray.add(countResult.get("avg"));
    }

    /**
     * 获取每一纵列的最大值, 最小值，平均值，累加值
     *
     * @param tdJsonArray 报表结构对象
     * @return
     */
    private static Map<String, JSONObject> getColumnCountResult(JSONArray tdJsonArray, String key, Integer accuracy) {
        Map<String, JSONObject> countResult = new HashMap<>(4);
        JSONObject maxJsonObject = new JSONObject();
        JSONObject minJsonObject = new JSONObject();
        JSONObject sumJsonObject = new JSONObject();
        JSONObject avgJsonObject = new JSONObject();
        MessageSource messageSource = SpringUtil.getBean(MessageSource.class);
        String[] split = LanguageFilter.getCurrentThreadLanguage().split("-");
        Locale locale = new Locale(split[0], split[1]);
        maxJsonObject.set("time", messageSource.getMessage("common.report.data.max",null ,locale));
        minJsonObject.set("time", messageSource.getMessage("common.report.data.min",null ,locale));
        sumJsonObject.set("time", messageSource.getMessage("common.report.data.sum",null ,locale));
        avgJsonObject.set("time", messageSource.getMessage("common.report.data.avg",null ,locale));
        JSONArray maxDataJsonArray = new JSONArray();
        JSONArray minDataJsonArray = new JSONArray();
        JSONArray sumDataJsonArray = new JSONArray();
        JSONArray avgDataJsonArray = new JSONArray();
        countColumnByKey(tdJsonArray, maxDataJsonArray, minDataJsonArray, sumDataJsonArray, avgDataJsonArray, key, accuracy);
        maxJsonObject.set("data", maxDataJsonArray);
        minJsonObject.set("data", minDataJsonArray);
        sumJsonObject.set("data", sumDataJsonArray);
        avgJsonObject.set("data", avgDataJsonArray);
        countResult.put("max", maxJsonObject);
        countResult.put("min", minJsonObject);
        countResult.put("sum", sumJsonObject);
        countResult.put("avg", avgJsonObject);
        return countResult;
    }

    /**
     * 根据key值（原始数据的value或者时间粒度的最大值，最小值，平均值的任意组合）获取对应列的最大值，最小值，平均值，累加值
     *
     * @param tdJsonArray      报表结构对象
     * @param maxDataJsonArray 纵向每一列的最大值数组
     * @param minDataJsonArray 纵向每一列的最小值数组
     * @param sumDataJsonArray 纵向每一列的累加值数组
     * @param avgDataJsonArray 纵向每一列的平均值数组
     * @param key              统计类型：value原始值， max最大值， min最小值， avg平均值， 组合条件用|，比如max|min|avg
     */
    private static void countColumnByKey(JSONArray tdJsonArray, JSONArray maxDataJsonArray, JSONArray minDataJsonArray, JSONArray sumDataJsonArray, JSONArray avgDataJsonArray, String key, Integer accuracy) {
        JSONArray jsonNode = tdJsonArray.getJSONObject(0).getJSONArray("data");
        Integer dataLength = jsonNode.size();

        for (int i = 0; i < dataLength; i++) {
            JSONObject maxDataJsonObject = new JSONObject();
            JSONObject minDataJsonObject = new JSONObject();
            JSONObject sumDataJsonObject = new JSONObject();
            JSONObject avgDataJsonObject = new JSONObject();

            if (key.contains(VALUE)) {
                setDataJsonObjectByKey(tdJsonArray, i, key, maxDataJsonObject, minDataJsonObject, sumDataJsonObject, avgDataJsonObject, accuracy);
            } else {
                if (key.contains("max")) {
                    setDataJsonObjectByKey(tdJsonArray, i, "max", maxDataJsonObject, minDataJsonObject, sumDataJsonObject, avgDataJsonObject, accuracy);
                }
                if (key.contains("min")) {
                    setDataJsonObjectByKey(tdJsonArray, i, "min", maxDataJsonObject, minDataJsonObject, sumDataJsonObject, avgDataJsonObject, accuracy);
                }
                if (key.contains("avg")) {
                    setDataJsonObjectByKey(tdJsonArray, i, "avg", maxDataJsonObject, minDataJsonObject, sumDataJsonObject, avgDataJsonObject, accuracy);
                }
                if (key.contains("sum")) {
                    setDataJsonObjectByKey(tdJsonArray, i, "sum", maxDataJsonObject, minDataJsonObject, sumDataJsonObject, avgDataJsonObject, accuracy);
                }
            }
            //为value属性添加displayValue
            maxDataJsonObject.set(DISPLAY_VALUE,maxDataJsonObject.get(VALUE));
            maxDataJsonArray.add(maxDataJsonObject);
            minDataJsonObject.set(DISPLAY_VALUE,minDataJsonObject.get(VALUE));
            minDataJsonArray.add(minDataJsonObject);
            sumDataJsonObject.set(DISPLAY_VALUE,sumDataJsonObject.get(VALUE));
            sumDataJsonArray.add(sumDataJsonObject);
            avgDataJsonObject.set(DISPLAY_VALUE,avgDataJsonObject.get(VALUE));
            avgDataJsonArray.add(avgDataJsonObject);
        }
    }

    /**
     * 或取下标index列的每一列对应key的值，找出最大值，最小值，平均值，累加值
     *
     * @param tdJsonArray       报表结构对象
     * @param index             列的下标，从0开始
     * @param key               键值： value 原始数据， max最大值， min最小值， avg平均值， sum累加值
     * @param maxDataJsonObject 当前列的最大值对象
     * @param minDataJsonObject 当前列的最小值对象
     * @param sumDataJsonObject 当前列的平均值对象
     * @param avgDataJsonObject 当前列的累加值对象
     * @param accuracy 精度
     */
    private static void setDataJsonObjectByKey(JSONArray tdJsonArray, int index, String key, JSONObject maxDataJsonObject, JSONObject minDataJsonObject, JSONObject sumDataJsonObject, JSONObject avgDataJsonObject, Integer accuracy) {
        JSONArray tempDataJsonArray = tdJsonArray.getJSONObject(0).getJSONArray("data");
        BigDecimal max = null;
        BigDecimal min = null;
        BigDecimal sum = null;
        int hasValueCount = 0;
        if (!"-".equals(tempDataJsonArray.getJSONObject(index).getStr(key))) {
            max = tempDataJsonArray.getJSONObject(index).getBigDecimal(key);
            min = max;
            sum = max;
            hasValueCount++;
        }

        for (int j = 1; j < tdJsonArray.size(); j++) {
            if (!"-".equals(tdJsonArray.getJSONObject(j).getJSONArray("data").getJSONObject(index).getStr(key))) {
                hasValueCount++;
                BigDecimal temp = tdJsonArray.getJSONObject(j).getJSONArray("data").getJSONObject(index).getBigDecimal(key);
                if (max == null) {
                    max = min = sum = temp;
                    continue;
                }
                if (max.compareTo(temp) < 0) {
                    max = temp;
                }
                if (min.compareTo(temp) > 0) {
                    min = temp;
                }
                sum = sum.add(temp);
            }
        }

        if (max != null) {
            max = max.setScale(accuracy, RoundingMode.UP);
            min = min.setScale(accuracy, RoundingMode.UP);
            sum = sum.setScale(accuracy, RoundingMode.UP);

            maxDataJsonObject.set(key, max.toPlainString());
            minDataJsonObject.set(key, min.toPlainString());
            sumDataJsonObject.set(key, sum.toPlainString());
            if (hasValueCount != 0) {
                avgDataJsonObject.set(key, sum.divide(new BigDecimal(hasValueCount), RoundingMode.UP).setScale(accuracy, RoundingMode.UP).toPlainString());
            } else {
                avgDataJsonObject.set(key, "-");
            }
        } else {
            maxDataJsonObject.set(key, "-");
            minDataJsonObject.set(key, "-");
            sumDataJsonObject.set(key, "-");
            avgDataJsonObject.set(key, "-");
        }
    }

    /**
     * 计算时间粒度每一行的最大值，最小值，平均值，累加值
     *
     * @param tdJsonObject
     * @param dataJsonArray
     * @param accuracy 精度
     * @return
     */
    public static JSONObject countTimeGranularityRowJsonArray(JSONObject tdJsonObject, JSONArray dataJsonArray, Integer accuracy) {
        List<BigDecimal> maxDataList = JsonArrayUtil.jsonArrayToDoubleList(dataJsonArray, "max");
        List<BigDecimal> minDataList = JsonArrayUtil.jsonArrayToDoubleList(dataJsonArray, "min");
        List<BigDecimal> avgDataList = JsonArrayUtil.jsonArrayToDoubleList(dataJsonArray, "avg");
        List<BigDecimal> sumDataList = JsonArrayUtil.jsonArrayToDoubleList(dataJsonArray, "sum");

        if (!maxDataList.isEmpty()) {
            tdJsonObject.set("max", NumberUtil.getBigDecimalMaxData(maxDataList, accuracy).toPlainString());
        }
        if (!minDataList.isEmpty()) {
            tdJsonObject.set("min", NumberUtil.getBigDecimalMinData(minDataList, accuracy).toPlainString());
        }
        if (CollUtil.isNotEmpty(sumDataList)) {
            tdJsonObject.set("sum", NumberUtil.getBigDecimalSumData(sumDataList, accuracy).toPlainString());
        }
        if (CollUtil.isNotEmpty(avgDataList)) {
            tdJsonObject.set("avg", NumberUtil.getBigDecimalSumData(avgDataList, accuracy).divide(new BigDecimal(avgDataList.size()), RoundingMode.UP).setScale(accuracy, RoundingMode.UP).toPlainString());
        }
        return tdJsonObject;
    }

    /**
     * 更新分页信息
     *
     * @param jsonObject
     * @param tempTotalElements
     * @param pageable
     */
    public static void updateTotalElement(JSONObject jsonObject, Integer tempTotalElements, Pageable pageable) {
        if (pageable != null) {
            jsonObject.set(ReportStructureEnum.TOTALELEMENTS.value(), tempTotalElements);
            jsonObject.set(ReportStructureEnum.TOTALPAGES.value(), tempTotalElements / pageable.getPageSize() + 1);
        }
    }

    /**
     * 是否拥有设备权限
     * @param equipmentIdSet 设备IdSet
     * @param equipmentId 设备id
     * @return boolean true 有权限  false 无权限
     */
    public static boolean hasEquipmentPermission(Set<Integer> equipmentIdSet,Integer equipmentId){
        return equipmentIdSet.contains(equipmentId);
    }

    /**
     * 对某些列的进行某种统计
     *
     * @param tdJsonArray 报表结构对象
     * @param key 要统计的列目标 value为源数据，max min 等为时间段聚合后的统计key
     * @param showRowKey 要展示的统计行 max min sum avg first last
     */
    public static void countColumnJsonArrayConfig(JSONArray tdJsonArray, String key, String showRowKey, Integer accuracy) {
        // 目前支持的统计列key
        Set<String> ableColKeySet = new HashSet<>(Arrays.asList("max", "min", "sum", "avg", "first", "last", "value"));
        // 目前支持的显示行列key
        Set<String> ableRowKeySet = new HashSet<>(Arrays.asList("max", "min", "sum", "avg", "first", "last"));

        String[] targetTypes = key.split("\\|");
        String[] targetRowTypes = showRowKey.split("\\|");

        if( !Arrays.stream(targetTypes).allMatch(ableColKeySet::contains) || !Arrays.stream(targetRowTypes).allMatch(ableRowKeySet::contains)){
            throw new BusinessException("Unexpected statistic type");
        }
        Map<String, JSONObject> countResult = getColumnCountResultDynamic(tdJsonArray, key,showRowKey, accuracy);

        for (String type : targetRowTypes){
            tdJsonArray.add(countResult.get(type));
        }
    }
    /**
     * 获取每一纵列的最大值, 最小值，平均值，累加值，首值，尾值(动态配置）
     *
     * @param tdJsonArray 报表结构对象
     * @param key 要统计的类型，用竖线 | 隔开
     * @return
     */
    private static Map<String, JSONObject> getColumnCountResultDynamic(JSONArray tdJsonArray, String key,String showRowKey, Integer accuracy) {
        // 统计结果的map，动态的key值
        Map<String, JSONObject> countResult = new HashMap<>();

        // 使用Map来存储每个统计类型的JSONObject
        Map<String, JSONObject> statisticJsonObjects = new HashMap<>();

        // MessageSource 获取多语言信息
        MessageSource messageSource = SpringUtil.getBean(MessageSource.class);
        String[] split = LanguageFilter.getCurrentThreadLanguage().split("-");
        Locale locale = new Locale(split[0], split[1]);

        String[] targetRowTypes = showRowKey.split("\\|");

        // 为每个统计类型设置其对应的JSON对象
        for (String type : targetRowTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("time", messageSource.getMessage("common.report.data." + type, null, locale));
            jsonObject.set("data", new JSONArray());
            statisticJsonObjects.put(type, jsonObject);
        }

        // 调用方法计算每个统计值
        countColumnByKeyMap(tdJsonArray, statisticJsonObjects, key, accuracy);

        for (String type : targetRowTypes) {
            countResult.put(type, statisticJsonObjects.get(type));
        }

        return countResult;
    }

    private static void countColumnByKeyMap(JSONArray tdJsonArray, Map<String, JSONObject> statisticJsonObjects, String key, Integer accuracy) {
        JSONArray jsonNode = tdJsonArray.getJSONObject(0).getJSONArray("data");
        Integer dataLength = jsonNode.size();
        String[] statisticTypes = {"max", "min", "sum", "avg", "first", "last"};

        for (int i = 0; i < dataLength; i++) {
            Map<String, JSONObject> tempResults = new HashMap<>();

            for (String type : statisticTypes) {
                tempResults.put(type, new JSONObject());
            }
            String[] keyColsTypes = key.split("\\|");
            for (String type : keyColsTypes) {
                setDataJsonObjectMapByKey(tdJsonArray, i, type, tempResults, accuracy);
            }
            for (String type : statisticJsonObjects.keySet()) {
                JSONObject resultJson = tempResults.get(type);
                resultJson.set(DISPLAY_VALUE, resultJson.get(VALUE));
                statisticJsonObjects.get(type).getJSONArray("data").add(resultJson);
            }
        }
    }

    private static void setDataJsonObjectMapByKey(JSONArray tdJsonArray, int index, String key, Map<String, JSONObject> tempResults, Integer accuracy) {
        JSONArray tempDataJsonArray = tdJsonArray.getJSONObject(0).getJSONArray("data");

        BigDecimal max = null;
        BigDecimal min = null;
        BigDecimal sum = null;
        BigDecimal first = null;
        BigDecimal last = null;
        int hasValueCount = 0;

        if (!"-".equals(tempDataJsonArray.getJSONObject(index).getStr(key))) {
            max = tempDataJsonArray.getJSONObject(index).getBigDecimal(key);
            min = max;
            sum = max;
            first = max;
            last = max;
            hasValueCount++;
        }

        for (int j = 1; j < tdJsonArray.size(); j++) {
            if (!"-".equals(tdJsonArray.getJSONObject(j).getJSONArray("data").getJSONObject(index).getStr(key))) {
                if (ObjectUtil.isNull(first)) {
                    first = tdJsonArray.getJSONObject(j).getJSONArray("data").getJSONObject(index).getBigDecimal(key);
                }
                last = tdJsonArray.getJSONObject(j).getJSONArray("data").getJSONObject(index).getBigDecimal(key);

                hasValueCount++;
                BigDecimal temp = tdJsonArray.getJSONObject(j).getJSONArray("data").getJSONObject(index).getBigDecimal(key);
                if (max == null) {
                    max = min = sum = temp;
                    continue;
                }
                if (max.compareTo(temp) < 0) {
                    max = temp;
                }
                if (min.compareTo(temp) > 0) {
                    min = temp;
                }
                sum = sum.add(temp);
            }
        }

        if (max != null) {
            max = max.setScale(accuracy, RoundingMode.UP);
            min = min.setScale(accuracy, RoundingMode.UP);
            sum = sum.setScale(accuracy, RoundingMode.UP);
            first = first.setScale(accuracy, RoundingMode.UP);
            last = last.setScale(accuracy,RoundingMode.UP);

            tempResults.get("max").set(key, max.toPlainString());
            tempResults.get("min").set(key, min.toPlainString());
            tempResults.get("sum").set(key, sum.toPlainString());
            if (hasValueCount != 0) {
                tempResults.get("avg").set(key, sum.divide(new BigDecimal(hasValueCount), RoundingMode.UP).setScale(accuracy, RoundingMode.UP).toPlainString());
            } else {
                tempResults.get("avg").set(key, "-");
            }
            tempResults.get("first").set(key, ObjectUtil.isNotNull(first) ? first.toPlainString() : "-");
            tempResults.get("last").set(key, ObjectUtil.isNotNull(last) ? last.toPlainString() : "-");
        } else {
            tempResults.get("max").set(key, "-");
            tempResults.get("min").set(key, "-");
            tempResults.get("sum").set(key, "-");
            tempResults.get("avg").set(key, "-");
            tempResults.get("first").set(key, "-");
            tempResults.get("last").set(key, "-");
        }
    }

}
