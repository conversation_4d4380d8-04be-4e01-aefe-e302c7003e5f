package com.siteweb.report.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.siteweb.battery.entity.BatteryCellSignal;
import com.siteweb.battery.entity.BatteryDischargeSignal;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.excel.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * 电池放电图表生成工具类
 */
@Slf4j
@Component
public class BatteryDischargeSheetCreator {

    private static LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    public void setMessageSourceUtil(LocaleMessageSourceUtil messageSourceUtil) {
        BatteryDischargeSheetCreator.messageSourceUtil = messageSourceUtil;
    }

    /**
     * 创建电池放电数据表格，包含四个图表：
     * 1. 放电电压与容量关系
     * 2. 放电电流
     * 3. 单体电压
     * 4. 单体温度
     *
     * @param sheetDatas 要添加新表格的表格数据列表
     * @param batteryHistoryDischargeSignals 电池放电信号列表
     * @param recordName 放电记录名称
     * @param eqName 设备名称
     */
    public static void createBatteryDischargeSheet(List<SheetData> sheetDatas,
                                                   List<BatteryDischargeSignal> batteryHistoryDischargeSignals,
                                                   String recordName, String eqName) {
        if (batteryHistoryDischargeSignals == null || batteryHistoryDischargeSignals.isEmpty()) {
            log.warn("No battery discharge signals provided for record: {}", recordName);
            return;
        }

        // Sort signals by sample time
        batteryHistoryDischargeSignals.sort(Comparator.comparing(BatteryDischargeSignal::getSampleTime));

        // Create data sheet
        SheetData dataSheet = createDataSheet(batteryHistoryDischargeSignals, recordName, eqName);
        sheetDatas.add(dataSheet);

        // Create a single sheet with all four charts
        createCombinedChartSheet(sheetDatas, batteryHistoryDischargeSignals, recordName, eqName);
    }

    /**
     * 创建包含所有电池放电数据的数据表
     * 时间用作列标题，每一行代表不同的数据项
     */
    private static SheetData createDataSheet(List<BatteryDischargeSignal> batteryHistoryDischargeSignals, String recordName, String eqName) {

        // 声明在方法开始处，确保在整个方法中都可以访问
        String timeRangeText = "";
        SheetData dataSheet = new SheetData();
        dataSheet.setSheetName(recordName + " " + messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.data"));
        dataSheet.setDefaultTitleCellStyleId(3);
        dataSheet.setDefaultDataCellStyleId(1);
        dataSheet.setCustomCellStyles(createCustomCellStyles());

        // Sort signals by sample time
        batteryHistoryDischargeSignals.sort(Comparator.comparing(BatteryDischargeSignal::getSampleTime));

        // Create data rows
        ArrayList<ArrayList<Object>> datas = new ArrayList<>();

        // Add title row
        ArrayList<Object> titleRow = new ArrayList<>();
        titleRow.add(eqName + recordName + " " + messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeData"));
        datas.add(titleRow);

        // Add time range row (will be right-aligned)
        ArrayList<Object> timeRangeRow = new ArrayList<>();

        // Get the first and last sample time
        Date firstSampleTime = null;
        Date lastSampleTime = null;
        if (!batteryHistoryDischargeSignals.isEmpty()) {
            firstSampleTime = batteryHistoryDischargeSignals.get(0).getSampleTime();
            lastSampleTime = batteryHistoryDischargeSignals.get(batteryHistoryDischargeSignals.size() - 1).getSampleTime();
        }

        if (firstSampleTime != null && lastSampleTime != null) {
            timeRangeText = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.timeRange") + ": " +
                    DateUtil.dateToString(firstSampleTime) + " ~ " + DateUtil.dateToString(lastSampleTime);
        }

        timeRangeRow.add(timeRangeText);
        datas.add(timeRangeRow);

        datas.add(new ArrayList<>());  // Empty row for spacing

        // Determine the maximum number of cells
        int maxCellCount = 0;
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            if (signal.getBatteryCellSignals() != null) {
                maxCellCount = Math.max(maxCellCount, signal.getBatteryCellSignals().size());
            }
        }

        // Add time header row
        ArrayList<Object> timeHeaderRow = new ArrayList<>();
        timeHeaderRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.name")); // First cell is empty or contains a label

        // Add time values as column headers
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            timeHeaderRow.add(DateUtil.dateToString(signal.getSampleTime()));
        }
        datas.add(timeHeaderRow);

        // Add total voltage row
        ArrayList<Object> totalVoltageRow = new ArrayList<>();
        totalVoltageRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalVoltage") + "(V)");
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            totalVoltageRow.add(signal.getTotalVoltage());
        }
        datas.add(totalVoltageRow);

        // Add total current row
        ArrayList<Object> totalCurrentRow = new ArrayList<>();
        totalCurrentRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCurrent") + "(A)");
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            totalCurrentRow.add(signal.getTotalCurrent());
        }
        datas.add(totalCurrentRow);

        // Add discharged capacity row
        ArrayList<Object> dischargedCapacityRow = new ArrayList<>();
        dischargedCapacityRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargedCapacity") + "(Ah)");
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            dischargedCapacityRow.add(signal.getTotalDischargeCapacity());
        }
        datas.add(dischargedCapacityRow);

        // Add rated capacity row
        ArrayList<Object> ratedCapacityRow = new ArrayList<>();
        ratedCapacityRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCapacity") + "(Ah)");
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            ratedCapacityRow.add(signal.getRatedCapacity());
        }
        datas.add(ratedCapacityRow);

        // Add remaining capacity rate row
        ArrayList<Object> remainingCapacityRateRow = new ArrayList<>();
        remainingCapacityRateRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.remainingCapacityRate") + "(%)");
        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
            remainingCapacityRateRow.add(signal.getRemainCapacityRate());
        }
        datas.add(remainingCapacityRateRow);

        // Add empty row as separator
        datas.add(new ArrayList<>());

        // Add cell voltage rows
        for (int cellIndex = 0; cellIndex < maxCellCount; cellIndex++) {
            ArrayList<Object> cellVoltageRow = new ArrayList<>();
            cellVoltageRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cell") + (cellIndex + 1) +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.voltage") + "(V)");

            for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
                if (signal.getBatteryCellSignals() != null &&
                        cellIndex < signal.getBatteryCellSignals().size()) {
                    BatteryCellSignal cellSignal = signal.getBatteryCellSignals().get(cellIndex);
                    cellVoltageRow.add(cellSignal.getCellVoltageValue());
                } else {
                    cellVoltageRow.add(""); // Empty cell if no data
                }
            }
            datas.add(cellVoltageRow);
        }

        // Add empty row as separator
        datas.add(new ArrayList<>());

        // Add cell temperature rows
        for (int cellIndex = 0; cellIndex < maxCellCount; cellIndex++) {
            ArrayList<Object> cellTemperatureRow = new ArrayList<>();
            cellTemperatureRow.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cell") + (cellIndex + 1) +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.temperature") + "(℃)");

            for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
                if (signal.getBatteryCellSignals() != null &&
                        cellIndex < signal.getBatteryCellSignals().size()) {
                    BatteryCellSignal cellSignal = signal.getBatteryCellSignals().get(cellIndex);
                    cellTemperatureRow.add(cellSignal.getCellTemperatureValue());
                } else {
                    cellTemperatureRow.add(""); // Empty cell if no data
                }
            }
            datas.add(cellTemperatureRow);
        }

        dataSheet.setDatas(datas);

        // Set column widths - all columns should be the same width except the first one
        List<CustomColumnWidth> columnWidths = new ArrayList<>();
        columnWidths.add(new CustomColumnWidth(0, 25));  // Parameter name column

        // Set width for time columns
        int columnCount = batteryHistoryDischargeSignals.size() + 1; // +1 for the parameter name column
        for (int i = 1; i < columnCount; i++) {
            columnWidths.add(new CustomColumnWidth(i, 15));  // Time columns
        }
        dataSheet.setCustomColumnWidths(columnWidths);

        // Set merge ranges for title and time range
        List<MergeCellRange> mergeCellRanges = new ArrayList<>();

        // Title merge (centered by default)
        MergeCellRange titleMerge = new MergeCellRange();
        titleMerge.setCellRangeFirstRow(0);
        titleMerge.setCellRangeLastRow(0);
        titleMerge.setCellRangeFirstCol(0);
        titleMerge.setCellRangeLastCol(Math.max(1, batteryHistoryDischargeSignals.size())); // Merge across all time columns
        mergeCellRanges.add(titleMerge);

        // Time range merge (will be right-aligned)
        MergeCellRange timeRangeMerge = new MergeCellRange();
        timeRangeMerge.setCellRangeFirstRow(1);
        timeRangeMerge.setCellRangeLastRow(1);
        timeRangeMerge.setCellRangeFirstCol(0);
        timeRangeMerge.setCellRangeLastCol(Math.max(1, batteryHistoryDischargeSignals.size())); // Merge across all time columns
        mergeCellRanges.add(timeRangeMerge);

        dataSheet.setMergeCellRanges(mergeCellRanges);

        // Set custom cell styles for right alignment of time range using CustomCellRule
        List<CustomCellRule> customCellRules = new ArrayList<>();
        CustomCellRule timeRangeStyle = new CustomCellRule();
        timeRangeStyle.setRow(1); // Second row (index 1)
        timeRangeStyle.setCol(0); // First column (index 0)
        timeRangeStyle.setCustomCellStyleId(4); // Using style ID 4 for right-aligned text
        // Keep the existing text
        timeRangeStyle.setName(timeRangeText);
        customCellRules.add(timeRangeStyle);
        dataSheet.setCustomCellRules(customCellRules);

        return dataSheet;
    }

    public enum ChartType {
        VOLTAGE,        // 总电压
        CURRENT,        // 总电流
        CAPACITY,       // 总容量
        CELL_TEMPERATURE, // 单体温度
        CELL_VOLTAGE   // 单体电压
    }

    /**
     * 通用生成电池图表的方法
     * 根据图表类型生成相应的图表
     */
    public static BufferedImage generateBatteryChart(
            List<BatteryDischargeSignal> batteryHistoryDischargeSignals,
            String recordName,
            ChartType chartType
    ) {
        try {
            JSONObject chartData = new JSONObject();
            JSONArray charts = new JSONArray();
            JSONObject chart = new JSONObject();

            JSONArray series = new JSONArray();
            String yAxisTitle;
            String chartTitle;
            String complexIndexName;

            switch (chartType) {
                case VOLTAGE:
                    chartTitle = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalVoltage");
                    yAxisTitle = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.voltage") + " (V)";
                    complexIndexName = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalVoltage");

                    JSONObject voltageSeries = new JSONObject();
                    voltageSeries.set("name", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalVoltage"));
                    voltageSeries.set("complexIndexName", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalVoltage"));

                    JSONArray voltageValues = new JSONArray();
                    for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
                        if (signal.getTotalVoltage() != null && signal.getSampleTime() != null) {
                            try {
                                JSONObject point = new JSONObject();
                                point.set("displayValue", Double.parseDouble(signal.getTotalVoltage()));
                                point.set("time", DateUtil.dateToString(signal.getSampleTime()));
                                voltageValues.add(point);
                            } catch (NumberFormatException e) {
                                log.warn("Invalid voltage value: {}", signal.getTotalVoltage());
                            }
                        }
                    }
                    voltageSeries.set("values", voltageValues);
                    series.add(voltageSeries);
                    break;

                case CURRENT:
                    chartTitle = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCurrent");
                    yAxisTitle = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.current") + " (A)";
                    complexIndexName = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCurrent");

                    JSONObject currentSeries = new JSONObject();
                    currentSeries.set("name", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCurrent"));
                    currentSeries.set("complexIndexName", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCurrent"));

                    JSONArray currentValues = new JSONArray();
                    for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
                        if (signal.getTotalCurrent() != null && signal.getSampleTime() != null) {
                            try {
                                JSONObject point = new JSONObject();
                                point.set("displayValue", Double.parseDouble(signal.getTotalCurrent()));
                                point.set("time", DateUtil.dateToString(signal.getSampleTime()));
                                currentValues.add(point);
                            } catch (NumberFormatException e) {
                                log.warn("Invalid current value: {}", signal.getTotalCurrent());
                            }
                        }
                    }
                    currentSeries.set("values", currentValues);
                    series.add(currentSeries);
                    break;

                case CAPACITY:
                    chartTitle = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCapacity");
                    yAxisTitle = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.capacity") + " (Ah)";
                    complexIndexName = messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCapacity");

                    JSONObject capacitySeries = new JSONObject();
                    capacitySeries.set("name", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCapacity"));
                    capacitySeries.set("complexIndexName", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.totalCapacity"));

                    JSONArray capacityValues = new JSONArray();
                    for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
                        if (signal.getTotalDischargeCapacity() != null && signal.getSampleTime() != null) {
                            try {
                                JSONObject point = new JSONObject();
                                point.set("displayValue", signal.getTotalDischargeCapacity());
                                point.set("time", DateUtil.dateToString(signal.getSampleTime()));
                                capacityValues.add(point);
                            } catch (NumberFormatException e) {
                                log.warn("Invalid capacity value: {}", signal.getTotalDischargeCapacity());
                            }
                        }
                    }
                    capacitySeries.set("values", capacityValues);
                    series.add(capacitySeries);
                    break;

                case CELL_TEMPERATURE:
                case CELL_VOLTAGE:
                    boolean isTemperature = chartType == ChartType.CELL_TEMPERATURE;
                    chartTitle = isTemperature ?
                            messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cellTemperature") :
                            messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cellVoltage");
                    yAxisTitle = isTemperature ?
                            messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.temperature") :
                            messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.voltage");
                    complexIndexName = chartTitle;

                    Optional<BatteryDischargeSignal> signalWithCells = batteryHistoryDischargeSignals.stream()
                            .filter(s -> s.getBatteryCellSignals() != null && !s.getBatteryCellSignals().isEmpty())
                            .findFirst();

                    if (!signalWithCells.isPresent()) {
                        log.warn("No cell data found for record: {}", recordName);
                        return new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
                    }

                    int cellCount = signalWithCells.get().getBatteryCellSignals().size();
                    for (int cellIndex = 0; cellIndex < cellCount; cellIndex++) {
                        JSONObject cellSeries = new JSONObject();
                        cellSeries.set("name", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cell") + (cellIndex + 1));
                        cellSeries.set("complexIndexName", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cell") + (cellIndex + 1));

                        JSONArray cellValues = new JSONArray();
                        for (BatteryDischargeSignal signal : batteryHistoryDischargeSignals) {
                            if (signal.getBatteryCellSignals() != null &&
                                    cellIndex < signal.getBatteryCellSignals().size()) {

                                BatteryCellSignal cellSignal = signal.getBatteryCellSignals().get(cellIndex);
                                if (cellSignal != null) {
                                    String valueStr = isTemperature ? cellSignal.getCellTemperatureValue() : cellSignal.getCellVoltageValue();
                                    if (StrUtil.isNotEmpty(valueStr)) {
                                        try {
                                            JSONObject point = new JSONObject();
                                            point.set("displayValue", Double.parseDouble(valueStr));
                                            point.set("time", DateUtil.dateToString(signal.getSampleTime()));
                                            cellValues.add(point);
                                        } catch (NumberFormatException e) {
                                            log.warn("Invalid {} value: {}", isTemperature ? "temperature" : "resistance", valueStr);
                                        }
                                    }
                                }
                            }
                        }

                        cellSeries.set("values", cellValues);
                        series.add(cellSeries);
                    }
                    break;

                default:
                    throw new IllegalArgumentException("Unsupported chart type: " + chartType);
            }

            chart.set("title", chartTitle);
            chart.set("xAxisTitle", messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.time"));
            chart.set("yAxisTitle", yAxisTitle);
            chart.set("complexIndexName", complexIndexName);
            chart.set("series", series);

            charts.add(chart);
            chartData.set("charts", charts);

            return ChartImageGenerator.generateLineChart(
                    chartData,
                    recordName + " - " + complexIndexName,
                    "displayValue"
            );

        } catch (Exception e) {
            log.error("Error creating chart for record {}: {}", recordName, e.getMessage(), e);
            return new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
        }
    }


    // 定义图表布局相关的常量
    private static final int CHART_START_COL = 1; // 图表起始列
    private static final int CHART_END_COL = 12; // 图表结束列
    private static final int CHART_WIDTH = CHART_END_COL - CHART_START_COL; // 图表宽度
    private static final int CHART_HEIGHT = 19; // 每个图表的高度
    private static final double CHART_SCALE = 1.0; // 图表缩放比例
    private static final int TITLE_ROW_HEIGHT = 1; // 标题行高度
    private static final int EMPTY_ROWS_BETWEEN_CHARTS = 1; // 图表之间的空行数
    private static final int CHART_COUNT = 5; // 图表总数

    /**
     * 创建包含所有五个电池放电图表的单个表格
     */
    private static void createCombinedChartSheet(List<SheetData> sheetDatas,
                                                 List<BatteryDischargeSignal> batteryHistoryDischargeSignals,
                                                 String recordName, String eqName) {
        try {
            // Generate all five chart images in the specified order:
            // 1. 放电电压, 2. 已放容量, 3. 放电电流, 4. 单体电压, 5. 单体温度
            BufferedImage voltageChart = generateBatteryChart(batteryHistoryDischargeSignals,recordName, ChartType.VOLTAGE);
            BufferedImage dischargeCapacityChart = generateBatteryChart(batteryHistoryDischargeSignals,recordName, ChartType.CAPACITY);
            BufferedImage currentChart = generateBatteryChart(batteryHistoryDischargeSignals,recordName, ChartType.CURRENT);
            BufferedImage cellVoltageChart = generateBatteryChart(batteryHistoryDischargeSignals,recordName, ChartType.CELL_VOLTAGE);
            BufferedImage cellTemperatureChart = generateBatteryChart(batteryHistoryDischargeSignals,recordName, ChartType.CELL_TEMPERATURE);

            // Convert images to base64
            String base64Voltage = convertImageToBase64(voltageChart);
            String base64DischargeCapacity = convertImageToBase64(dischargeCapacityChart);
            String base64Current = convertImageToBase64(currentChart);
            String base64CellVoltage = convertImageToBase64(cellVoltageChart);
            String base64CellTemperature = convertImageToBase64(cellTemperatureChart);

            // Create chart sheet
            SheetData chartSheet = new SheetData();
            chartSheet.setSheetName(recordName + " - " + messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeCharts"));
            chartSheet.setDefaultTitleCellStyleId(3);
            chartSheet.setDefaultDataCellStyleId(1);
            chartSheet.setCustomCellStyles(createCustomCellStyles());

            // Create image data list
            List<Base64ImageData> imageDataList = new ArrayList<>();

            // 计算图表位置
            int chart1StartRow = 3; // 第一个图表的起始行
            int chart2StartRow = chart1StartRow + CHART_HEIGHT + TITLE_ROW_HEIGHT + EMPTY_ROWS_BETWEEN_CHARTS;
            int chart3StartRow = chart2StartRow + CHART_HEIGHT + TITLE_ROW_HEIGHT + EMPTY_ROWS_BETWEEN_CHARTS;
            int chart4StartRow = chart3StartRow + CHART_HEIGHT + TITLE_ROW_HEIGHT + EMPTY_ROWS_BETWEEN_CHARTS;
            int chart5StartRow = chart4StartRow + CHART_HEIGHT + TITLE_ROW_HEIGHT + EMPTY_ROWS_BETWEEN_CHARTS;

            // Add voltage chart (first chart - top)
            Base64ImageData voltageImageData = new Base64ImageData();
            voltageImageData.setImgName(eqName + recordName + " - " +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeVoltage") + ".png");
            voltageImageData.setImgBase64Info(base64Voltage);
            voltageImageData.setImageResizeWidthRatio(CHART_SCALE);
            voltageImageData.setImageResizeHightRatio(CHART_SCALE);
            voltageImageData.setImageDrawStartAnchorCol(CHART_START_COL);
            voltageImageData.setImageDrawStartAnchorRow(chart1StartRow);
            voltageImageData.setImageDrawEndAnchorCol(CHART_END_COL);
            voltageImageData.setImageDrawEndAnchorRow(chart1StartRow + CHART_HEIGHT - 1);
            imageDataList.add(voltageImageData);

            // Add discharge capacity chart (second chart)
            Base64ImageData dischargeCapacityImageData = new Base64ImageData();
            dischargeCapacityImageData.setImgName(eqName + recordName + " - " +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargedCapacity") + ".png");
            dischargeCapacityImageData.setImgBase64Info(base64DischargeCapacity);
            dischargeCapacityImageData.setImageResizeWidthRatio(CHART_SCALE);
            dischargeCapacityImageData.setImageResizeHightRatio(CHART_SCALE);
            dischargeCapacityImageData.setImageDrawStartAnchorCol(CHART_START_COL);
            dischargeCapacityImageData.setImageDrawStartAnchorRow(chart2StartRow);
            dischargeCapacityImageData.setImageDrawEndAnchorCol(CHART_END_COL);
            dischargeCapacityImageData.setImageDrawEndAnchorRow(chart2StartRow + CHART_HEIGHT - 1);
            imageDataList.add(dischargeCapacityImageData);

            // Add current chart (third chart)
            Base64ImageData currentImageData = new Base64ImageData();
            currentImageData.setImgName(eqName + recordName + " - " +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeCurrent") + ".png");
            currentImageData.setImgBase64Info(base64Current);
            currentImageData.setImageResizeWidthRatio(CHART_SCALE);
            currentImageData.setImageResizeHightRatio(CHART_SCALE);
            currentImageData.setImageDrawStartAnchorCol(CHART_START_COL);
            currentImageData.setImageDrawStartAnchorRow(chart3StartRow);
            currentImageData.setImageDrawEndAnchorCol(CHART_END_COL);
            currentImageData.setImageDrawEndAnchorRow(chart3StartRow + CHART_HEIGHT - 1);
            imageDataList.add(currentImageData);

            // Add cell voltage chart (fourth chart)
            Base64ImageData cellVoltageImageData = new Base64ImageData();
            cellVoltageImageData.setImgName(eqName + recordName + " - " +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cellVoltage") + ".png");
            cellVoltageImageData.setImgBase64Info(base64CellVoltage);
            cellVoltageImageData.setImageResizeWidthRatio(CHART_SCALE);
            cellVoltageImageData.setImageResizeHightRatio(CHART_SCALE);
            cellVoltageImageData.setImageDrawStartAnchorCol(CHART_START_COL);
            cellVoltageImageData.setImageDrawStartAnchorRow(chart4StartRow);
            cellVoltageImageData.setImageDrawEndAnchorCol(CHART_END_COL);
            cellVoltageImageData.setImageDrawEndAnchorRow(chart4StartRow + CHART_HEIGHT - 1);
            imageDataList.add(cellVoltageImageData);

            // Add cell temperature chart (fifth chart - bottom)
            Base64ImageData cellTemperatureImageData = new Base64ImageData();
            cellTemperatureImageData.setImgName(eqName + recordName + " - " +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cellTemperature") + ".png");
            cellTemperatureImageData.setImgBase64Info(base64CellTemperature);
            cellTemperatureImageData.setImageResizeWidthRatio(CHART_SCALE);
            cellTemperatureImageData.setImageResizeHightRatio(CHART_SCALE);
            cellTemperatureImageData.setImageDrawStartAnchorCol(CHART_START_COL);
            cellTemperatureImageData.setImageDrawStartAnchorRow(chart5StartRow);
            cellTemperatureImageData.setImageDrawEndAnchorCol(CHART_END_COL);
            cellTemperatureImageData.setImageDrawEndAnchorRow(chart5StartRow + CHART_HEIGHT - 1);
            imageDataList.add(cellTemperatureImageData);

            chartSheet.setBase64ImageDatas(imageDataList);

            // Add chart titles as data
            ArrayList<ArrayList<Object>> chartSheetData = new ArrayList<>();

            // Main title
            ArrayList<Object> titleRow = new ArrayList<>();
            titleRow.add(eqName + recordName + " " +
                    messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeCharts"));
            chartSheetData.add(titleRow);
            chartSheetData.add(new ArrayList<>()); // Empty row

            // 定义每个图表标题的行号
            int firstChartTitleRow = chart1StartRow - 1;
            int secondChartTitleRow = chart2StartRow - 1;
            int thirdChartTitleRow = chart3StartRow - 1;
            int fourthChartTitleRow = chart4StartRow - 1;
            int fifthChartTitleRow = chart5StartRow - 1;

            // 计算需要填充的空单元格数
            int emptyCellsCount = CHART_WIDTH + 1;

            // First chart title - 放电电压
            ArrayList<Object> firstChartTitleRowData = new ArrayList<>();
            firstChartTitleRowData.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeVoltage"));
            for (int i = 1; i <= emptyCellsCount; i++) {
                firstChartTitleRowData.add(""); // Empty cells
            }

            // 填充数据行直到第一个图表标题行
            while (chartSheetData.size() < firstChartTitleRow) {
                chartSheetData.add(new ArrayList<>());
            }
            chartSheetData.add(firstChartTitleRowData);

            // 填充空行直到第二个图表标题行
            while (chartSheetData.size() < secondChartTitleRow) {
                chartSheetData.add(new ArrayList<>());
            }

            // Second chart title - 已放容量
            ArrayList<Object> secondChartTitleRowData = new ArrayList<>();
            secondChartTitleRowData.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargedCapacity"));
            for (int i = 1; i <= emptyCellsCount; i++) {
                secondChartTitleRowData.add(""); // Empty cells
            }
            chartSheetData.add(secondChartTitleRowData);

            // 填充空行直到第三个图表标题行
            while (chartSheetData.size() < thirdChartTitleRow) {
                chartSheetData.add(new ArrayList<>());
            }

            // Third chart title - 放电电流
            ArrayList<Object> thirdChartTitleRowData = new ArrayList<>();
            thirdChartTitleRowData.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.dischargeCurrent"));
            for (int i = 1; i <= emptyCellsCount; i++) {
                thirdChartTitleRowData.add(""); // Empty cells
            }
            chartSheetData.add(thirdChartTitleRowData);

            // 填充空行直到第四个图表标题行
            while (chartSheetData.size() < fourthChartTitleRow) {
                chartSheetData.add(new ArrayList<>());
            }

            // Fourth chart title - 单体电压
            ArrayList<Object> fourthChartTitleRowData = new ArrayList<>();
            fourthChartTitleRowData.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cellVoltage"));
            for (int i = 1; i <= emptyCellsCount; i++) {
                fourthChartTitleRowData.add(""); // Empty cells
            }
            chartSheetData.add(fourthChartTitleRowData);

            // 填充空行直到第五个图表标题行
            while (chartSheetData.size() < fifthChartTitleRow) {
                chartSheetData.add(new ArrayList<>());
            }

            // Fifth chart title - 单体温度
            ArrayList<Object> fifthChartTitleRowData = new ArrayList<>();
            fifthChartTitleRowData.add(messageSourceUtil.getMessage("report.batteryDischargeSheetCreator.cellTemperature"));
            for (int i = 1; i <= emptyCellsCount; i++) {
                fifthChartTitleRowData.add(""); // Empty cells
            }
            chartSheetData.add(fifthChartTitleRowData);

            chartSheet.setDatas(chartSheetData);

            // Set column widths
            List<CustomColumnWidth> columnWidths = new ArrayList<>();
            // 设置所有列的宽度
            for (int i = 0; i <= CHART_END_COL; i++) {
                columnWidths.add(new CustomColumnWidth(i, 10)); // 设置列宽为10
            }
            chartSheet.setCustomColumnWidths(columnWidths);

            // Set merge ranges for title
            List<MergeCellRange> mergeCellRanges = new ArrayList<>();
            int rowMerge = CHART_END_COL ;
            // Main title merge
            MergeCellRange titleMerge = new MergeCellRange();
            titleMerge.setCellRangeFirstRow(0);
            titleMerge.setCellRangeLastRow(0);
            titleMerge.setCellRangeFirstCol(0);
            titleMerge.setCellRangeLastCol(rowMerge);
            mergeCellRanges.add(titleMerge);

            // First chart title merge
            MergeCellRange firstChartTitleMerge = new MergeCellRange();
            firstChartTitleMerge.setCellRangeFirstRow(firstChartTitleRow);
            firstChartTitleMerge.setCellRangeLastRow(firstChartTitleRow);
            firstChartTitleMerge.setCellRangeFirstCol(0);
            firstChartTitleMerge.setCellRangeLastCol(rowMerge);
            mergeCellRanges.add(firstChartTitleMerge);

            // Second chart title merge
            MergeCellRange secondChartTitleMerge = new MergeCellRange();
            secondChartTitleMerge.setCellRangeFirstRow(secondChartTitleRow);
            secondChartTitleMerge.setCellRangeLastRow(secondChartTitleRow);
            secondChartTitleMerge.setCellRangeFirstCol(0);
            secondChartTitleMerge.setCellRangeLastCol(rowMerge);
            mergeCellRanges.add(secondChartTitleMerge);

            // Third chart title merge
            MergeCellRange thirdChartTitleMerge = new MergeCellRange();
            thirdChartTitleMerge.setCellRangeFirstRow(thirdChartTitleRow);
            thirdChartTitleMerge.setCellRangeLastRow(thirdChartTitleRow);
            thirdChartTitleMerge.setCellRangeFirstCol(0);
            thirdChartTitleMerge.setCellRangeLastCol(rowMerge);
            mergeCellRanges.add(thirdChartTitleMerge);

            // Fourth chart title merge
            MergeCellRange fourthChartTitleMerge = new MergeCellRange();
            fourthChartTitleMerge.setCellRangeFirstRow(fourthChartTitleRow);
            fourthChartTitleMerge.setCellRangeLastRow(fourthChartTitleRow);
            fourthChartTitleMerge.setCellRangeFirstCol(0);
            fourthChartTitleMerge.setCellRangeLastCol(rowMerge);
            mergeCellRanges.add(fourthChartTitleMerge);

            // Fifth chart title merge
            MergeCellRange fifthChartTitleMerge = new MergeCellRange();
            fifthChartTitleMerge.setCellRangeFirstRow(fifthChartTitleRow);
            fifthChartTitleMerge.setCellRangeLastRow(fifthChartTitleRow);
            fifthChartTitleMerge.setCellRangeFirstCol(0);
            fifthChartTitleMerge.setCellRangeLastCol(rowMerge);
            mergeCellRanges.add(fifthChartTitleMerge);

            chartSheet.setMergeCellRanges(mergeCellRanges);

            // Add chart sheet to workbook
            sheetDatas.add(chartSheet);

            log.info("Added combined chart sheet for record: {}", recordName);
        } catch (Exception e) {
            log.error("Error creating combined chart sheet for record {}: {}", recordName, e.getMessage(), e);
        }
    }

    /**
     * 将BufferedImage转换为base64字符串的辅助方法
     */
    private static String convertImageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 为表格创建自定义单元格样式
     */
    private static List<CustomCellStyle> createCustomCellStyles() {
        List<CustomCellStyle> customCellStyles = new ArrayList<>();

        // Style 1: Normal data style
        CustomCellStyle style1 = new CustomCellStyle();
        style1.setCustomCellStyleId(1);
        style1.setFontName("宋体");
        style1.setFontSize(11);
        style1.setTextAlignment(1); // HorizontalAlignment.CENTER
        style1.setBottomBorder(1); // BorderStyle.THIN
        style1.setLeftBorder(1);
        style1.setRightBorder(1);
        style1.setTopBorder(1);
        style1.setBottomBorderColor(0); // Black color
        style1.setLeftBorderColor(0);
        style1.setRightBorderColor(0);
        style1.setTopBorderColor(0);
        style1.setFontBold(false);
        customCellStyles.add(style1);

        // Style 2: Bold style
        CustomCellStyle style2 = new CustomCellStyle();
        style2.setCustomCellStyleId(2);
        style2.setFontName("宋体");
        style2.setFontSize(11);
        style2.setFontBold(true);
        style2.setTextAlignment(1); // HorizontalAlignment.CENTER
        style2.setBottomBorder(1); // BorderStyle.THIN
        style2.setLeftBorder(1);
        style2.setRightBorder(1);
        style2.setTopBorder(1);
        style2.setBottomBorderColor(0); // Black color
        style2.setLeftBorderColor(0);
        style2.setRightBorderColor(0);
        style2.setTopBorderColor(0);
        customCellStyles.add(style2);

        // Style 3: Title style
        CustomCellStyle style3 = new CustomCellStyle();
        style3.setCustomCellStyleId(3);
        style3.setFontName("宋体");
        style3.setFontSize(14);
        style3.setFontBold(true);
        style3.setTextAlignment(1); // HorizontalAlignment.CENTER
        customCellStyles.add(style3);

        // Style 4: Right-aligned text style
        CustomCellStyle style4 = new CustomCellStyle();
        style4.setCustomCellStyleId(4);
        style4.setFontName("宋体");
        style4.setFontSize(11);
        style4.setFontBold(false);
        style4.setTextAlignment(3); // HorizontalAlignment.RIGHT
        customCellStyles.add(style4);

        return customCellStyles;
    }
}

