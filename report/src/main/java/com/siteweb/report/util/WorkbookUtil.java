package com.siteweb.report.util;

import com.siteweb.report.enums.ExcelVersionEnum;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;

public class WorkbookUtil {

    private static final Logger log = LoggerFactory.getLogger(WorkbookUtil.class);

    private WorkbookUtil() {
    }

    public static Workbook getWorkBook(File file) {
        if (!file.exists()) {
            return null;
        }
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
        } catch (IOException e) {
            log.error("CustomReportHistoryServiceImpl getWorkBook reportPath {}", e.getMessage());
        }

        return getWorkBook(file.getName(), fileInputStream);
    }

    public static Workbook getWorkBook(String fileName, FileInputStream fileInputStream) {

        //创建Workbook工作薄对象，表示整个excel
        Workbook workbook = null;
        try {
            //根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith(ExcelVersionEnum.XLS.getVersion())) {
                //2003
                workbook = new HSSFWorkbook(fileInputStream);
            } else if (fileName.endsWith(ExcelVersionEnum.XLSX.getVersion())) {
                //2007
                workbook = new XSSFWorkbook(fileInputStream);
            }
        } catch (IOException e) {
            log.error("CustomReportHistoryServiceImpl getWorkBook MultipartFile {}", e.getMessage());
        }
        return workbook;
    }

    public static Cell getCell(Sheet sheet, Integer rowVale, Integer columnValue) {
        return getCell(sheet, rowVale, columnValue, null, null);
    }

    public static Cell getCell(Sheet sheet, Integer rowVale, Integer columnValue, CellStyle cellStyle, CellType cellType) {
        Row row = sheet.getRow(rowVale);
        if (row == null) {
            row = sheet.createRow(rowVale);
        }
        Cell cell = row.getCell(columnValue);
        if (cell == null) {
            cell = row.createCell(columnValue);
        }
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }
        if (cellType != null) {
            cell.setCellType(cellType);
        }
        return cell;
    }

    public static void setCellValueStringAndDouble(Cell cell, Object value) {
        if (value == null || value.equals("")) {
            return;
        }
        if (value instanceof String) {
            cell.setCellValue((String) value);
            return;
        }
        if (value instanceof BigDecimal doubleValue) {
            cell.setCellValue(doubleValue.doubleValue());
            return;
        }
        // 浮点
        cell.setCellValue((Double) value);
    }

    public static Cell setCellValue(Cell cell, String value) {
        if (value == null || value.equals("")) {
            return cell;
        }
        switch (cell.getCellType()) {
            case BLANK://空白单元格类型
                cell.setCellValue(value);
                break;
            case BOOLEAN://布尔单元格类型
                cell.setCellValue(Boolean.parseBoolean(value));
                break;
            case ERROR://错误单元格类型
                break;
            case FORMULA://公式单元格类型
                break;
            case NUMERIC://数字单元格类型（整数，小数位数，日期）
                cell.setCellValue(Double.parseDouble(value));
                break;
            case STRING://字符串（文本）单元格类型
                cell.setCellValue(value);
                break;
            default:
        }
        return cell;
    }

    public static Cell setCellValue(Sheet sheet, Integer rowIndex, Integer columnIndex, String value) {
        return setCellValue(WorkbookUtil.getCell(sheet, rowIndex, columnIndex), value);
    }

    public static Integer getRowValue(String initialPosition) {
        StringBuilder columnString = new StringBuilder("");
        initialPosition = initialPosition.replace("$", "");
        for (int i = 0; i < initialPosition.length(); i++) {
            if (initialPosition.charAt(i) <= 57 && initialPosition.charAt(i) >= 48) {
                columnString.append(initialPosition.charAt(i));
            }
        }
        return Integer.parseInt(columnString.toString());
    }

    public static Integer getColumnValue(String initialPosition) {
        initialPosition = initialPosition.replace("$", "");
        Integer index = 0;
        StringBuilder rowString = new StringBuilder("");
        while (initialPosition.charAt(index) >= 65 && initialPosition.charAt(index) <= 90) {
            rowString.insert(0, initialPosition.charAt(index));
            index++;
        }
        Integer rowValue = rowString.charAt(0) - 64;
        for (int i = 1; i < rowString.length(); i++) {
            Integer chatIntegerValue = rowString.charAt(i) - 64;
            rowValue += 26 * i * chatIntegerValue;
        }
        return rowValue;
    }

    public static Cell getCellByReference(Sheet sheet, String reference) {
        String initialPosition = reference.split(":")[0];
        try {
            Integer rowVale = getRowValue(initialPosition);
            Integer columnValue = getColumnValue(initialPosition);
            return getCell(sheet, --rowVale, --columnValue);
        } catch (Exception e) {
            log.error("getCellByReference reference:{}", reference);
            return null;
        }
    }
}
