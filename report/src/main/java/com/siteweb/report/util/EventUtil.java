package com.siteweb.report.util;


import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class EventUtil {
    private final LocaleMessageSourceUtil messageSourceUtil;

    /**
     * 计算告警原因类型
     */
    public <T> void fillHandlerStatus(List<T> list,
                                      Function<T, Date> getConfirmTime,
                                      Function<T, Date> getEndTime,
                                      BiConsumer<T, String> setHandleStatus) {
        Map<String, String> statusMap = Map.of(
                "true-true", messageSourceUtil.getMessage("common.report.form.unconfirmedUnrecovered"),
                "true-false", messageSourceUtil.getMessage("common.report.form.unconfirmedRecovered"),
                "false-true", messageSourceUtil.getMessage("common.report.form.confirmedUnrecovered"),
                "false-false", messageSourceUtil.getMessage("common.report.form.confirmedRecovered")
        );
        list.forEach(event -> {
            boolean isConfirmTimeNull = ObjectUtil.isNull(getConfirmTime.apply(event));
            boolean isEndTimeNull = ObjectUtil.isNull(getEndTime.apply(event));
            setHandleStatus.accept(event, statusMap.get(isConfirmTimeNull + "-" + isEndTimeNull));
        });
    }
}
