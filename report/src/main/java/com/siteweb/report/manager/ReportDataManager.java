package com.siteweb.report.manager;


import cn.hutool.json.JSONObject;
import com.siteweb.report.parser.ReportParser;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.dto.IdValueDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 报表数据查询
 *
 * <AUTHOR>
 */
@Component
public class ReportDataManager {

    /**
     * 查询报表数据
     *
     * @return 报表数据
     */
    public JSONObject findResult(ReportVO reportVO) {
        return ReportParser.doParse(reportVO);
    }

    /**
     * 报表数据采样统计
     * @param reportVO 报表查询条件
     * @return long 需要查询的总数量
     */
    public long findCountSample(ReportVO reportVO){
        return ReportParser.doCountSample(reportVO);
    }

    /**
     * 导出报表数据
     * @param reportVO 报表查询条件
     */
    public long export(ReportVO reportVO,String filePath){
        return ReportParser.doExport(reportVO, filePath);
    }

    public List<IdValueDTO<String,String>> findAllReportColumnConfig(Integer reportDataSourceId) {
        return ReportParser.doColumnConfig(reportDataSourceId);
    }
}
