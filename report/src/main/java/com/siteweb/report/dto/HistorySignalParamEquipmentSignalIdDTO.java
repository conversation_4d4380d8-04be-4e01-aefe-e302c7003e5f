package com.siteweb.report.dto;

import com.siteweb.monitoring.dto.SimpleSignalDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class HistorySignalParamEquipmentSignalIdDTO {
    public HistorySignalParamEquipmentSignalIdDTO(SimpleSignalDTO simpleSignalDTO) {
        this.equipmentId = simpleSignalDTO.getEquipmentId();
        this.signalId = simpleSignalDTO.getSignalId();
    }

    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 信号id
     */
    private Integer signalId;

    public String getSignalKey(){
        return this.equipmentId + "." + this.signalId;
    }
}
