package com.siteweb.report.dto;

import com.siteweb.monitoring.enumeration.OnlineState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentDTO {
    /**
     * 设备名
     */
    private String equipmentName;

    /**
     * 设备位置
     */
    private String equipmentPosition;

    /**
     * 设备基类名
     */
    private String baseEquipmentName;

    /**
     * 设备类型
     */
    private String equipmentCategoryName;

    /**
     *设备状态
     */
    private OnlineState equipmentState;
}
