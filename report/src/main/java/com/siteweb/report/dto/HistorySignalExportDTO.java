package com.siteweb.report.dto;

import cn.hutool.core.convert.TypeConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Type;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HistorySignalExportDTO implements TypeConverter {
    /**
     * 信号采集时间
     */
    private String time;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 信号名字
     */
    private String signalName;
    /**
     * 信号值
     */
    private String signalValue;

    @Override
    public String[] convert(Type targetType, Object value) {
        return new String[]{time,equipmentName,signalName,signalValue};
    }
}
