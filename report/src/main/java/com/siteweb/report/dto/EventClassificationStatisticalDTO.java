package com.siteweb.report.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警分类统计报表返回结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventClassificationStatisticalDTO {
    /**
     * 设备基类名(设备类型)
     */
    private String baseEquipmentName;

    /**
     * 设备数量
     */
    private Long equipmentNum;
    /**
     * 全部告警
     */
    private Long allEventNum;
    /**
     * 异常告警
     */
    private Long abnormalEventNum;
    /**
     * 异常-历史告警
     */
    private Long abnormalHistoryEventNum;
    /**
     * 异常-活动告警
     */
    private Long abnormalActiveEventNum;
    /**
     * 施工告警
     */
    private Long constructEventNum;
    /**
     * 施工-历史告警
     */
    private Long constructHistoryEventNum;
    /**
     * 施工-活动告警
     */
    private Long constructActiveEventNum;
}
