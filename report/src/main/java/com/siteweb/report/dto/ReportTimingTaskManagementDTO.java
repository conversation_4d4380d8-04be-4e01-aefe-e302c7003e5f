package com.siteweb.report.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.siteweb.report.entity.ReportExportParameterPreset;
import com.siteweb.report.entity.ReportTimingTaskManagement;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.influxdb.annotation.Column;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/13 10:15
 */
@Data
public class ReportTimingTaskManagementDTO {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("任务id")
    @NotNull(message = "任务id不能为空")
    private Integer reportTimingTaskManagementId;

    @ApiModelProperty("任务名称")
    private String reportTimingTaskManagementName;

    @ApiModelProperty("报表id")
    @NotNull(message = "报表不能为空")
    private Integer reportId;

    @ApiModelProperty("报表名称")
    private String reportName;
    /**
     * cron表达式
     * 每小时 0 0 0/1 * * ? *
     * 每12小时 0 0 0/12 * * ? *
     * 每天 0 0 0 * * ? *
     * 每周 0 0 0 ? * 2/7 (周一为第一天)
     * 每月 0 0 0 1 1/1 ?
     */
    @ApiModelProperty("cron表达式")
    @NotEmpty(message = "存储周期不能为空")
    private String storageCycle;
    /**
     * 0 当前时间
     * -1h 一小时前
     * -1d 一天前
     * -1w 一周前
     * -1m 一月前
     */
    @ApiModelProperty("开始时间类型")
    private String startTimeType;
    /**
     * 0 当前时间
     * -1h 一小时前
     * -1d 一天前
     * -1w 一周前
     * -1m 一月前
     */
    @ApiModelProperty("开始时间类型")
    private String endTimeType;
    /**
     * false 未启用
     * true 启用
     */
    @ApiModelProperty("状态")
    @NotEmpty(message = "启用状态不能为空")
    private Boolean status;

    @Column(name = "`to`")
    @ApiModelProperty("收件人")
    private String to;

    @ApiModelProperty("抄送人")
    private String cc;
    @ApiModelProperty("创建人id")
    private Integer createUserId;
    /**
     * 导出参数实体
     */
    private List<ReportExportParameterPreset> reportExportParameterPresetList;
    /**
     * 是否公开
     */
    @ApiModelProperty("是否公开")
    private Boolean overt;
    public ReportTimingTaskManagement build() {
        ReportTimingTaskManagement reportTimingTaskManagement = new ReportTimingTaskManagement();
        BeanUtils.copyProperties(this, reportTimingTaskManagement);
        return reportTimingTaskManagement;
    }
}
