package com.siteweb.report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.report.enums.ReportDisplayStyleEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 报表文件夹树形结构
 */
@Data
public class ReportFolderTreeDTO {
    /**
     * id
     */
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * "folder" 或 "report"
     */
    private String type;
    /**
     * 排序值
     */
    private Integer sortIndex;
    /**
     * 报表SchemaID
     */
    private Integer reportSchemaId;

    /**
     * 报表Schema类别ID
     */
    private Integer reportSchemaCategoryId;

    private List<ReportFolderTreeDTO> children = new ArrayList<>();

    /**
     * 解析类型
     * 1：通用
     * 2：历史数据，历史指标
     * 3：电池
     * 4：定制报表
     */
    @JsonProperty
    public Integer getReportType() {
        if (this.reportSchemaId == null) {
            return null;
        }
        return switch (this.reportSchemaId) {
            case 1, 2, 4, 25, 26, 27, 32, 33, 34, 35, 37, 38 ->
                    ReportDisplayStyleEnum.BASE_DATA_BACKEND_PAGING.getValue();
            case 3, 10, 16, 20, 28 -> ReportDisplayStyleEnum.INFLUXDB_DATA.getValue();
            case 9 -> ReportDisplayStyleEnum.BATTERY_DATA.getValue();
            case 14 -> ReportDisplayStyleEnum.CUSTOM_DATA_SIMPLE.getValue();
            case 17, 18, 19, 22 -> ReportDisplayStyleEnum.CUSTOM_DATA_COMPLEX.getValue();
            case -1 -> -1;  // 定时报表
            default -> ReportDisplayStyleEnum.BASE_DATA_FONT_PAGING.getValue();
        };
    }

    @JsonProperty
    public String getKey() {
        return String.format("%s-%s-%s",
                id != null ? id : "",
                name != null ? name : "",
                getReportType() != null ? getReportType() : "");
    }

}