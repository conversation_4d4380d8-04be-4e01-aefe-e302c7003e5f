package com.siteweb.report.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.battery.entity.BatteryDischargeSignal;
import com.siteweb.battery.service.BatteryDischargeSignalService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.report.entity.*;
import com.siteweb.report.enums.ReportExportStyleEnum;
import com.siteweb.report.service.ReportService;
import com.siteweb.report.service.ReportTimingTaskFileService;
import com.siteweb.report.util.BatteryDischargeSheetCreator;
import com.siteweb.report.util.ChartImageGenerator;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.excel.entity.*;
import com.siteweb.utility.excel.util.CustomExportExcelUtils;
import com.siteweb.utility.manager.EmailMessageManager;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.SneakyThrows;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.report.commonutils.ReportParamParserUtils.*;
import static com.siteweb.utility.excel.util.CustomExportExcelUtils.*;

/**
 * @Author: lzy
 * @Date: 2022/6/13 15:29
 */
public class ReportTimeJob implements BaseJob {

    private final Logger log = LoggerFactory.getLogger(ReportTimeJob.class);
    private static final String EMAIL_SOURCE = "reportTimeJob";

    @Autowired
    ReportTimeJobExecService reportTimeJobExecService;

    @Autowired
    EmailMessageManager emailMessageManager;
    @Autowired
    ReportTimingTaskFileService reportTimingTaskFileService;
    @Autowired
    ReportService reportService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    BatteryDischargeSignalService batteryDischargeSignalService;

    @Autowired
    private Environment env;
    @Autowired
    HAStatusService haStatusService;

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext context) {
        if (!haStatusService.isMasterHost()) {
            log.info("不是主机 退出定时任务");
            return;
        }
        JobDataMap params = context.getJobDetail().getJobDataMap();
        if (params.isEmpty()) {
            return;
        }
        Integer reportTimingTaskManagementId = params.getInt("reportTimingTaskManagementId");
        Integer reportId = params.getInt("reportId");
        // 基于 ReportTimingTaskTimeType 表中的 【0 -1h -1d -1w -1m】来决定查询的开始时间
        String startTimeType = params.getString("startTimeType");
        String endTimeType = params.getString("endTimeType");
        ReportTimingTaskResult reportTimingTaskResult = reportTimeJobExecService.exec(reportTimingTaskManagementId, reportId, startTimeType, endTimeType);
        if (reportTimingTaskResult == null) {
            return;
        }
        ReportTimingTaskFile reportTimingTaskFile = reportTimingTaskResult.getReportTimingTaskFile();
        if (reportTimingTaskFile == null) {
            return;
        }

        String to = params.getString("to");
        String cc = params.getString("cc");
        Report report = reportService.findByReportId(reportId);
        JSONObject jsonObject = reportTimingTaskResult.getResult();
        ReportExportStyleEnum styleEnum = ReportExportStyleEnum.getReportExportStyleEnum(reportTimingTaskFile.getType());
        if (styleEnum != null) {
            switch (styleEnum) {
                case BASE_DATA:
                    baseDataDeal(jsonObject, report, reportTimingTaskFile);
                    break;
                case INFLUXDB_DATA:
                    String type = jsonObject.getStr(TYPE);
                    if (ORIGINAL.equals(type)) {
                        baseDataDeal(jsonObject, report, reportTimingTaskFile);
                    }
                    //messagingTemplate.convertAndSend("/topic/reportexport", "{\"reportTimingTaskFileId\":\"" + reportTimingTaskFile.getReportTimingTaskFileId()+"\"}");
                    break;
                case BATTERY_DATA:
                    batterySheetDeal(jsonObject, report, reportTimingTaskFile);
                    break;
                case COMPLEXINDEX:
                    String reportType = jsonObject.getStr(TYPE);
                    if (reportType != null && reportType.equals("original")) {
                        // 原始数据
                        sendComplexIndexReport("original", jsonObject, report, reportTimingTaskFile);
                    } else {
                        // 时间粒度，聚合格式
                        sendComplexIndexReport("", jsonObject, report, reportTimingTaskFile);
                    }
                    break;
                case CUSTOM_DATA:
                    try {
                        String url = jsonObject.getStr("url");
                        if (CharSequenceUtil.isEmpty(url)) {
                            return;
                        }
                        String[] urls = url.split("/");
                        urls = urls[urls.length - 1].split("\\.");
                        reportTimingTaskFile.setFilePath(url);
                        boolean update = reportTimingTaskFileService.updateById(reportTimingTaskFile);
                        if (update) {
                            emailMessageManager.sendMail(to, cc, env.getProperty("fileserver.rootPath") + File.separator + url, urls[0], urls[0], EMAIL_SOURCE);
                        }
                    } catch (Exception e) {
                        log.error("ReportAutoDownJob execute CUSTOM_DATA：{}", e.getMessage());
                    }
                    break;
                default:
                    break;
            }
        }

    }


    /**
     * 发送指标报表
     *
     * @param jsonObject           报表查询返回json数据
     * @param report               报表对象
     * @param reportTimingTaskFile 报表定时发送文件对象
     */
    private void sendComplexIndexReport(String type, JSONObject jsonObject, Report report, ReportTimingTaskFile reportTimingTaskFile) {
        // 构建一个报表定时任务文件excel对象数据
        ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData = new ReportTimingTaskFileWorkbookData();
        JSONObject jsonObj = JSONUtil.parseObj(jsonObject.toString());
        if (StrUtil.isBlank(jsonObj.getStr("result"))) {
            // 没有结果就不发送
            return;
        }
        if (type.equals("original")) {
            setSheetDatas(reportTimingTaskFileWorkbookData, jsonObj, report);
        } else {
            setSheetDatasByGroup(reportTimingTaskFileWorkbookData, jsonObj, report);
        }
        reportTimingTaskFileWorkbookData.setFileName(report.getReportName() + ".xlsx");
        reportTimingTaskFileWorkbookData.setReportTimingTaskFileId(reportTimingTaskFile.getReportTimingTaskFileId());
        reportTimingTaskFileService.sendReportTimingTaskFileByCustomExcels(reportTimingTaskFileWorkbookData);
    }

    /**
     * 设置 ReportTimingTaskFileWorkbookData 对象的 sheetdatas(原始)
     *
     * @param reportTimingTaskFileWorkbookData
     * @param jsonObj
     * @param report
     */
    private void setSheetDatas(ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData, JSONObject jsonObj, Report report) {
        List<SheetData> sheetDatas = new ArrayList<>();
        SheetData sheetData = new SheetData();
        sheetData.setSheetName(report.getReportName());
        sheetData.setDefaultTitleCellStyleId(3);
        sheetData.setDefaultDataCellStyleId(1);
        List<CustomCellStyle> customCellStyles = createCustomCellStyles();
        sheetData.setCustomCellStyles(customCellStyles);
        // 设置列的宽度，判断有多少行
        JSONArray result = jsonObj.getJSONArray("result");
        List<CustomColumnWidth> customColumnWidths = createCustomColumnWidth(result.size() + 1);
        sheetData.setCustomColumnWidths(customColumnWidths);
        // 设置需要合并的
        List<MergeCellRange> mergeCellRanges = createMergeCellRange(result.size());
        sheetData.setMergeCellRanges(mergeCellRanges);
        ArrayList<ArrayList<Object>> datas = new ArrayList<>();
        datas.add(new ArrayList<>(Arrays.asList(report.getReportName())));
        datas.add(new ArrayList<>());
        // 首行时间
        ArrayList<Object> timeRow = new ArrayList<Object>();
        timeRow.add("");
        for (int i = 0; i < result.size(); i++) {
            JSONObject item = result.getJSONObject(i);
            String time = item.get("time", String.class);
            timeRow.add(time);
        }
        datas.add(timeRow);
        // 数据行
        JSONArray title = jsonObj.getJSONArray("title");
        for (int i = 0; i < title.size(); i++) {
            ArrayList<Object> dataRow = new ArrayList<>();
            JSONObject item = title.getJSONObject(i);
            String name = item.get("complexIndexName", String.class);
            String position = item.get("position", String.class);
            dataRow.add(name + "(" + position + ")");
            for (int j = 0; j < result.size(); j++) {
                JSONObject context = result.getJSONObject(j);
                JSONArray dataArray = context.getJSONArray("data");
                JSONObject data = dataArray.getJSONObject(i);
                dataRow.add(data.get("value"));
            }
            datas.add(dataRow);
        }
        // 循环导出展示值
        List<ReportExportParameterPreset> exportParmList = report.getReportExportParameterPresetList();
        for (ReportExportParameterPreset r : exportParmList) {
            // 最大值、最小值。。。。
            String reportExportTitle = r.getReportSchemaExportParameter().getReportSchemaExportParameterTitle();
            // max、min。。。
            String reportExportName = r.getReportSchemaExportParameter().getReportSchemaExportParameterName();
            if(StringUtils.equals(reportExportTitle,"首值") || StringUtils.equals(reportExportTitle,"尾值")){
                continue;
            }
            ArrayList<Object> dataRow = new ArrayList<>();
            dataRow.add(reportExportTitle);
            for (int j = 0; j < result.size(); j++) {
                JSONObject context = result.getJSONObject(j);
                dataRow.add(context.get(reportExportName));
            }
            datas.add(dataRow);
        }

        List<Chart> charts = new ArrayList<>();
        JSONArray ogCharts = jsonObj.getJSONArray("charts");
        JSONObject c = ogCharts.getJSONObject(0);
        JSONArray values = c.getJSONArray("values");
        Chart chart = new Chart();
        // 因为第一个空一列，所以从第二列开始
        chart.setDataStartCol(1);
        chart.setDataEndCol(values.size());
        // 默认从第四行开始
        chart.setDataStartRow(3);
        // 省略前两行所以需要+2
        chart.setDataEndRow(2 + title.size());
        chart.setBottomAxisTitle("时间");
        chart.setLeftAxisTitle("值");
        charts.add(chart);
        sheetData.setCharts(charts);
        sheetData.setDatas(datas);
        sheetDatas.add(sheetData);

        // Create chart sheet with image
        if (jsonObj.containsKey("charts") && !jsonObj.getJSONArray("charts").isEmpty()) {
            try {
                // Generate chart image
                BufferedImage chartImage = ChartImageGenerator.generateLineChart(jsonObj, report.getReportName(),"avg");

                // Convert image to base64
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(chartImage, "png", baos);
                byte[] imageBytes = baos.toByteArray();
                String base64Image = "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);

                // Create chart sheet
                SheetData chartSheet = new SheetData();
                chartSheet.setSheetName(report.getReportName() + " 图表");
                chartSheet.setDefaultTitleCellStyleId(3);
                chartSheet.setDefaultDataCellStyleId(1);
                chartSheet.setCustomCellStyles(createCustomCellStyles());

                // Create image data
                List<Base64ImageData> imageDataList = new ArrayList<>();
                Base64ImageData imageData = new Base64ImageData();
                imageData.setImgName(report.getReportName() + ".png");
                imageData.setImgBase64Info(base64Image);
                // 调整图表在Excel中的尺寸，使其更大
                imageData.setImageResizeWidthRatio(1.2); // 增加宽度比例
                imageData.setImageResizeHightRatio(1.2); // 增加高度比例
                imageData.setImageDrawStartAnchorCol(1); // 起始列保持不变
                imageData.setImageDrawStartAnchorRow(1); // 起始行保持不变
                imageData.setImageDrawEndAnchorCol(20); // 结束列保持不变
                imageData.setImageDrawEndAnchorRow(38); // 结束行保持不变
                imageDataList.add(imageData);
                chartSheet.setBase64ImageDatas(imageDataList);

                // Add empty data (required for sheet creation)
                ArrayList<ArrayList<Object>> chartSheetData = new ArrayList<>();
                chartSheetData.add(new ArrayList<>(List.of(report.getReportName() + " 图表")));
                chartSheetData.add(new ArrayList<>());
                chartSheet.setDatas(chartSheetData);

                // Add chart sheet to workbook
                sheetDatas.add(chartSheet);

                log.info("Added chart image to report: {}", report.getReportName());
            } catch (Exception e) {
                log.error("Error generating chart image: {}", e.getMessage(), e);
            }
        }

        reportTimingTaskFileWorkbookData.setSheetDatas(sheetDatas);
    }

    /**
     * 设置 ReportTimingTaskFileWorkbookData 对象的 sheetdatas(聚合)
     *
     * @param jsonObj
     * @param report
     * @return
     */
    private void setSheetDatasByGroup(ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData, JSONObject jsonObj, Report report) {
        // 先完成纵向，聚合数据
        List<SheetData> sheetDatas = new ArrayList<>();

        List<ReportExportParameterPreset> exportParmList = report.getReportExportParameterPresetList();

        boolean hasCharts = jsonObj.containsKey("charts") && !jsonObj.getJSONArray("charts").isEmpty();
        SheetData finalSheetData = new SheetData();
        finalSheetData.setSheetName(report.getReportName());
        finalSheetData.setDefaultTitleCellStyleId(3);
        finalSheetData.setDefaultDataCellStyleId(1);
        List<CustomCellStyle> customCellStyles = createCustomCellStyles();
        finalSheetData.setCustomCellStyles(customCellStyles);
        // 设置列的宽度，判断有多少行
        JSONArray result = jsonObj.getJSONArray("result");
        JSONArray title = jsonObj.getJSONArray("title");

        // 计算总列数：第一列是时间点，后面是每个title对应的导出参数数量
        int totalColumns = 1 + title.size() * exportParmList.size() + 4; // +exportParmList.size()是为了最后的统计列
        List<CustomColumnWidth> customColumnWidths = createCustomColumnWidth(totalColumns);
        finalSheetData.setCustomColumnWidths(customColumnWidths);

        // 创建合并单元格配置
        List<MergeCellRange> mergeCellRanges = new ArrayList<>();

        // 第一行标题合并所有列
        MergeCellRange titleMerge = new MergeCellRange();
        titleMerge.setCellRangeFirstRow(0);
        titleMerge.setCellRangeLastRow(0);
        titleMerge.setCellRangeFirstCol(0);
        titleMerge.setCellRangeLastCol(totalColumns - 1);
        mergeCellRanges.add(titleMerge);

        // 第二行的title合并单元格，每个title合并exportParmList.size()个单元格
        int colIndex = 1; // 从第二列开始，第一列是时间点
        for (int i = 0; i < title.size(); i++) {
            MergeCellRange titleCellMerge = new MergeCellRange();
            titleCellMerge.setCellRangeFirstRow(1);
            titleCellMerge.setCellRangeLastRow(1);
            titleCellMerge.setCellRangeFirstCol(colIndex);
            titleCellMerge.setCellRangeLastCol(colIndex + exportParmList.size() - 1);
            mergeCellRanges.add(titleCellMerge);
            colIndex += exportParmList.size();
        }

        // 最后一列的统计标题合并
        MergeCellRange statsMerge = new MergeCellRange();
        statsMerge.setCellRangeFirstRow(1);
        statsMerge.setCellRangeLastRow(1);
        statsMerge.setCellRangeFirstCol(colIndex);
        statsMerge.setCellRangeLastCol(totalColumns - 1);
        mergeCellRanges.add(statsMerge);

        finalSheetData.setMergeCellRanges(mergeCellRanges);

        // 创建数据表格
        ArrayList<ArrayList<Object>> datas = new ArrayList<>();

        // 第一行：整体标题
        datas.add(new ArrayList<>(Arrays.asList(report.getReportName())));

        // 第二行：每个指标的标题（需要合并单元格）
        ArrayList<Object> headerRow = new ArrayList<>();
        headerRow.add("时间点"); // 第一列是时间点

        // 添加每个title作为列标题
        for (int i = 0; i < title.size(); i++) {
            JSONObject item = title.getJSONObject(i);
            String name = item.get("complexIndexName", String.class);
            String position = item.get("position", String.class);
            String titleText = name + "(" + position + ")";

            // 为每个title添加一个标题，后面会合并单元格
            headerRow.add(titleText);

            // 为每个title填充空值，以便后面合并单元格
            for (int j = 1; j < exportParmList.size(); j++) {
                headerRow.add("");
            }
        }

        // 添加统计列标题
        headerRow.add("统计");
        for (int j = 1; j < exportParmList.size(); j++) {
            headerRow.add("");
        }

        datas.add(headerRow);

        // 第三行：导出参数标题行
        ArrayList<Object> paramRow = new ArrayList<>();
        paramRow.add(""); // 第一列是时间点，此处留空

        // 为每个title添加导出参数标题
        for (int i = 0; i < title.size(); i++) {
            for (ReportExportParameterPreset r : exportParmList) {
                String paramTitle = r.getReportSchemaExportParameter().getReportSchemaExportParameterTitle();
                paramRow.add(paramTitle);
            }
        }

        // 添加统计列的参数标题
        for (ReportExportParameterPreset r : exportParmList) {
            String paramTitle = r.getReportSchemaExportParameter().getReportSchemaExportParameterTitle();
            if(StringUtils.equals(paramTitle,"首值") || StringUtils.equals(paramTitle,"尾值")){
                continue;
            }
            paramRow.add(paramTitle);
        }

        datas.add(paramRow);

        // 数据行：每个时间点一行
        for (int i = 0; i < result.size(); i++) {
            JSONObject timePoint = result.getJSONObject(i);
            ArrayList<Object> dataRow = new ArrayList<>();

            // 添加时间点
            String time = timePoint.get("time", String.class);
            if(CharSequenceUtil.isNotEmpty(time) && (StringUtils.equals(time,"first") || StringUtils.equals(time,"last"))){
                continue;
            }
            dataRow.add(time);

            // 为每个title添加对应的数据
            JSONArray dataArray = timePoint.getJSONArray("data");
            for (int j = 0; j < title.size(); j++) {
                JSONObject dataItem = dataArray.getJSONObject(j);

                // 添加每个导出参数的值
                for (ReportExportParameterPreset r : exportParmList) {
                    String paramName = r.getReportSchemaExportParameter().getReportSchemaExportParameterName();
                    Object value = dataItem.getDouble(paramName);
                    dataRow.add(value);
                }
            }

            // 添加统计列的值
            for (ReportExportParameterPreset r : exportParmList) {
                String paramName = r.getReportSchemaExportParameter().getReportSchemaExportParameterName();
                if(StringUtils.equals(paramName,"first") || StringUtils.equals(paramName,"last")){
                    continue;
                }
                Object value = timePoint.getDouble(paramName);
                dataRow.add(value);
            }

            datas.add(dataRow);
        }

        finalSheetData.setDatas(datas);
        sheetDatas.add(finalSheetData);
        for (ReportExportParameterPreset r : exportParmList) {
            ReportSchemaExportParameter param = r.getReportSchemaExportParameter();
            String paramName = param.getReportSchemaExportParameterName(); // max, min, avg, sum
            String paramTitle = param.getReportSchemaExportParameterTitle(); // 最大值, 最小值, 平均值, 累加值

            // 2. 如果有图表数据，为当前聚合参数创建图表工作表
            if (hasCharts) {
                try {
                    BufferedImage chartImage = ChartImageGenerator.generateLineChart(jsonObj,
                            report.getReportName() + " " + paramTitle, paramName);

                    // 转换为base64
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    ImageIO.write(chartImage, "png", baos);
                    byte[] imageBytes = baos.toByteArray();
                    String base64Image = "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);

                    // 创建图表工作表
                    SheetData chartSheet = new SheetData();
                    chartSheet.setSheetName(report.getReportName() + " " + paramTitle + " 图表");
                    chartSheet.setDefaultTitleCellStyleId(3);
                    chartSheet.setDefaultDataCellStyleId(1);
                    chartSheet.setCustomCellStyles(createCustomCellStyles());

                    // 创建图像数据
                    List<Base64ImageData> imageDataList = new ArrayList<>();
                    Base64ImageData imageData = new Base64ImageData();
                    imageData.setImgName(report.getReportName() + "_" + paramName + ".png");
                    imageData.setImgBase64Info(base64Image);
                    // 调整图表在Excel中的尺寸，使其更大
                    imageData.setImageResizeWidthRatio(1.3);
                    imageData.setImageResizeHightRatio(1.3);
                    imageData.setImageDrawStartAnchorCol(1);
                    imageData.setImageDrawStartAnchorRow(1);
                    imageData.setImageDrawEndAnchorCol(15);
                    imageData.setImageDrawEndAnchorRow(30);
                    imageDataList.add(imageData);
                    chartSheet.setBase64ImageDatas(imageDataList);

                    // 添加空数据（工作表创建所需）
                    ArrayList<ArrayList<Object>> chartSheetData = new ArrayList<>();
                    chartSheetData.add(new ArrayList<>(Arrays.asList(report.getReportName() + " " + paramTitle + " 图表")));
                    chartSheetData.add(new ArrayList<>());
                    chartSheet.setDatas(chartSheetData);

                    // 将图表工作表添加到工作簿
                    sheetDatas.add(chartSheet);

                    log.info("Added {} chart image to report: {}", paramTitle, report.getReportName());
                } catch (Exception e) {
                    log.error("Error generating {} chart image: {}", paramTitle, e.getMessage(), e);
                }
            }
        }


        reportTimingTaskFileWorkbookData.setSheetDatas(sheetDatas);
    }

    /**
     * 处理电池放电数据，生成报表和图表
     * 每个电池放电记录放到一个sheet中
     *
     * @param jsonObject 报表查询返回json数据
     * @param report 报表对象
     * @param reportTimingTaskFile 报表定时发送文件对象
     */
    private void batterySheetDeal(JSONObject jsonObject, Report report, ReportTimingTaskFile reportTimingTaskFile) {
        // 构建一个报表定时任务文件excel对象数据
        ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData = new ReportTimingTaskFileWorkbookData();
        JSONObject jsonObj = JSONUtil.parseObj(jsonObject.toString());
        JSONArray result = jsonObj.getJSONArray("result");
        if (result == null || result.isEmpty()) {
            log.info("No battery discharge records found");
            return;
        }
        //默认只生成最新时间段的
        List<JSONObject> sortedList = result.stream()
                .map(obj -> (JSONObject) obj)
                .sorted(Comparator.comparing(
                        (JSONObject item) -> item.get("column2",String.class),
                        Comparator.nullsLast(String::compareTo)
                ).reversed())
                .limit(1)
                .collect(Collectors.toList());
        result = new JSONArray(sortedList);
        List<SheetData> sheetDatas = new ArrayList<>();
        //设备
        ReportParameterPreset reportParameterPreset = report.getReportParameterPresetList().stream().filter(item -> item.getReportSchemaQueryParameterId() == 25).findFirst().orElse(null);
        // 处理每个电池放电记录
        for (int i = 0; i < result.size(); i++) {
            JSONObject recordObj = result.getJSONObject(i);
            Integer recordId = recordObj.getInt("id");
            String recordName = recordObj.getStr("name", recordId +"充放电记录" );
            // 从设备参数中获取设备名称并添加到记录名称前
            String eqName = Optional.ofNullable(reportParameterPreset)
                    .map(ReportParameterPreset::getValue)
                    .filter(StrUtil::isNotBlank)
                    .map(JSONUtil::parseArray)
                    .filter(arr -> !arr.isEmpty())
                    .map(arr -> arr.getJSONObject(0))
                    .map(obj -> obj.getStr("eqName", "")).orElse("");

            // 获取放电历史数据
            List<BatteryDischargeSignal> batteryHistoryDischargeSignals = batteryDischargeSignalService.findBatteryHistoryDischargeSignals(recordId);
            if (batteryHistoryDischargeSignals == null || batteryHistoryDischargeSignals.isEmpty()) {
                log.info("No battery discharge signals found for record ID: {}", recordId);
                continue;
            }

            // 创建一个sheet页用于展示当前放电记录的数据和图表
            BatteryDischargeSheetCreator.createBatteryDischargeSheet(sheetDatas, batteryHistoryDischargeSignals, recordName, eqName);
        }

        reportTimingTaskFileWorkbookData.setSheetDatas(sheetDatas);
        reportTimingTaskFileWorkbookData.setFileName(report.getReportName() + ".xlsx");
        reportTimingTaskFileWorkbookData.setReportTimingTaskFileId(reportTimingTaskFile.getReportTimingTaskFileId());
        reportTimingTaskFileService.sendReportTimingTaskFileByCustomExcels(reportTimingTaskFileWorkbookData);
    }
    /**
     * 处理基本数据，将json数据转换成 ReportTimingTaskFileWorkbookData 对象，并调用 sendReportTimingTaskFileByCustomExcels 接口，该接口是报表导出的接口
     *
     * @param jsonObject
     * @param report
     * @param reportTimingTaskFile
     */
    private void baseDataDeal(JSONObject jsonObject, Report report, ReportTimingTaskFile reportTimingTaskFile) {
        ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData = createReportTimingTaskFileWorkbookData(jsonObject, report.getReportName(), report);
        reportTimingTaskFileWorkbookData.setFileName(report.getReportName() + ".xlsx");
        reportTimingTaskFileWorkbookData.setReportTimingTaskFileId(reportTimingTaskFile.getReportTimingTaskFileId());
        reportTimingTaskFileService.sendReportTimingTaskFileByCustomExcels(reportTimingTaskFileWorkbookData);
    }

    /**
     * 创建 ReportTimingTaskFileWorkbookData 对象
     *
     * @param jsonObject
     * @param reportName
     * @param report
     * @return
     */
    private ReportTimingTaskFileWorkbookData createReportTimingTaskFileWorkbookData(JSONObject jsonObject, String reportName, Report report) {
        // 创建 ReportTimingTaskFileWorkbookData 并处理sheet页数据
        ReportTimingTaskFileWorkbookData reportTimingTaskFileWorkbookData = new ReportTimingTaskFileWorkbookData();
        // 总sheet页对象
        List<SheetData> sheetDatas = new ArrayList<>();
        SheetData sheetData = new SheetData();
        // 设置页名称
        sheetData.setSheetName(reportName);

        /**
         * 页数据  行<列>
         *     datas的数据结构
         *               第一列         第二列
         *     第一行     设备           时间1        时间2      时间三     最大值     最小值等...
         *     第二行     设备/信号值       值          值         值        值         值
         *     第三行     最大值          ...
         *     第四行     最小值          ...
         *     第五行     ...
         */
        ArrayList<ArrayList<Object>> datas = new ArrayList<>();

        // 第一行设置报表名称
        datas.add(new ArrayList<>(List.of(reportName)));
        datas.add(new ArrayList<>());

        int titleSize = 0;
        if (jsonObject != null) {
            String type = null;
            if (CharSequenceUtil.isNotEmpty(jsonObject.getStr(TYPE))) {
                type = jsonObject.getStr(TYPE);
            }
            String exportParameterString = "";
            JSONArray result = jsonObject.getJSONArray("result");
            if (result != null) {
                if (ORIGINAL.equals(type)) {
                    // 从系统参数中获取报表数据导出模式
                    SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("report.timeJob.export.mode");
                    // 将请求展示的参数名全部拼接起来
                    exportParameterString = getExportParameterString(report);

                    JSONArray title = jsonObject.getJSONArray(TITLE);

                    /** 横向导出 */
                    if (systemConfig != null && "1".equals(systemConfig.getSystemConfigValue())) {
                        // 获取第一行标题数据
                        datas.add(getThValueDatasByTitle(title, exportParameterString));
                        // 将结果添加到数据集合中
                        for (int i = 0; i < result.size(); i++) {
                            datas.add(getOriginalValueDatas(result.getJSONObject(i), exportParameterString));
                        }
                    } else {
                        /** 纵向导出，默认导出 */

                        // 1.添加第一行时间格式列
                        datas.add(getThValueDatasByResult(result));

                        // 遍历选中的信号/设备
                        for (int i = 0; i < title.size(); i++) {
                            // 2. 添加当前信号/数据的所有数据（包含最大值、最小值等）
                            ArrayList<Object> originalValueDatas = getOriginalValueDatas(title.getJSONObject(i), result, i, exportParameterString);
                            // 3. 将当前这一`行`数据添加到页数据中
                            datas.add(originalValueDatas);
                        }
                        // 4. 最后添加信号/设备的最大，最小值等信息
                        getOriginalValueDatas(datas, result, exportParameterString);
                    }
                    // 获取信号/设备的长度(用于后面计算宽度或单元格合并范围)
                    titleSize = datas.get(2).size() - 1;

                } else {
                    JSONObject title = jsonObject.getJSONObject(TITLE);
                    datas.add(getTitleDatas(title));
                    titleSize = title.size();

                    for (int i = 0; i < result.size(); i++) {
                        datas.add(getValueDatas(result.getJSONObject(i), titleSize));
                    }
                }

            }
        }


        // 将页数据添加到当前页
        sheetData.setDatas(datas);

        // 添加单元格样式
        sheetData.setCustomCellStyles(CustomExportExcelUtils.createCustomCellStyles());
        // 添加单元格宽度样式
        sheetData.setCustomColumnWidths(CustomExportExcelUtils.createCustomColumnWidth(titleSize));
        // 添加单元格合并范围
        sheetData.setMergeCellRanges(CustomExportExcelUtils.createMergeCellRange(titleSize));
        // 默认标题单元格样式Id
        sheetData.setDefaultTitleCellStyleId(3);
        // 默认数据单元格样式Id
        sheetData.setDefaultDataCellStyleId(1);
        sheetDatas.add(sheetData);

        // Create chart sheet with image if charts data exists
        if (jsonObject.containsKey("charts") && !jsonObject.getJSONArray("charts").isEmpty()) {
            try {
                // Generate chart image
                BufferedImage chartImage = ChartImageGenerator.generateLineChart(jsonObject, reportName, "");

                // Convert image to base64
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(chartImage, "png", baos);
                byte[] imageBytes = baos.toByteArray();
                String base64Image = "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);

                // Create chart sheet
                SheetData chartSheet = new SheetData();
                chartSheet.setSheetName(reportName + " 图表");
                chartSheet.setDefaultTitleCellStyleId(3);
                chartSheet.setDefaultDataCellStyleId(1);
                chartSheet.setCustomCellStyles(createCustomCellStyles());

                // Create image data
                List<Base64ImageData> imageDataList = new ArrayList<>();
                Base64ImageData imageData = new Base64ImageData();
                imageData.setImgName(reportName + ".png");
                imageData.setImgBase64Info(base64Image);
                // 调整图表在Excel中的尺寸，使其更大
                imageData.setImageResizeWidthRatio(1.2); // 增加宽度比例
                imageData.setImageResizeHightRatio(1.2); // 增加高度比例
                imageData.setImageDrawStartAnchorCol(1); // 起始列保持不变
                imageData.setImageDrawStartAnchorRow(1); // 起始行保持不变
                imageData.setImageDrawEndAnchorCol(15); // 增加结束列
                imageData.setImageDrawEndAnchorRow(30); // 增加结束行
                imageDataList.add(imageData);
                chartSheet.setBase64ImageDatas(imageDataList);

                // Add empty data (required for sheet creation)
                ArrayList<ArrayList<Object>> chartSheetData = new ArrayList<>();
                chartSheetData.add(new ArrayList<>(Arrays.asList(reportName + " 图表")));
                chartSheetData.add(new ArrayList<>());
                chartSheet.setDatas(chartSheetData);

                // Add chart sheet to workbook
                sheetDatas.add(chartSheet);

                log.info("Added chart image to report: {}", reportName);
            } catch (Exception e) {
                log.error("Error generating chart image: {}", e.getMessage(), e);
            }
        }

        reportTimingTaskFileWorkbookData.setSheetDatas(sheetDatas);
        return reportTimingTaskFileWorkbookData;
    }

    /**
     * 获取 ReportSchemaExportParameterName 报表场景导出参数名
     *
     * @param report
     * @return
     */
    private String getExportParameterString(Report report) {
        List<ReportExportParameterPreset> reportExportParameterPresetList = report.getReportExportParameterPresetList();
        StringBuilder exportParameterString = new StringBuilder();
        if (CollUtil.isNotEmpty(reportExportParameterPresetList)) {
            for (ReportExportParameterPreset reportExportParameterPreset : reportExportParameterPresetList) {
                if (Boolean.TRUE.equals(reportExportParameterPreset.getDisplay())) {
                    exportParameterString.append(reportExportParameterPreset.getReportSchemaExportParameter().getReportSchemaExportParameterName());
                }
            }
        }
        return exportParameterString.toString();
    }

    /**
     * 获取标题表头数据
     *
     * @param jsonArray             json数据
     * @param exportParameterString 导出参数字符串
     * @return
     */
    private ArrayList<Object> getThValueDatasByTitle(JSONArray jsonArray, String exportParameterString) {
        ArrayList<Object> objects = new ArrayList<>();
        objects.add("");
        for (int i = 0; i < jsonArray.size(); i++) {
            objects.add(getDetailName(jsonArray.getJSONObject(i)));
        }

        return addExportParameterByExportParameterString(objects, exportParameterString);
    }

    private ArrayList<Object> getTitleDatas(JSONObject jsonObject) {
        ArrayList<Object> objects = new ArrayList<>();
        for (int i = 1; i <= jsonObject.size(); i++) {
            String value = jsonObject.getStr("column" + i);
            objects.add(CharSequenceUtil.isEmpty(value) ? "-" : value);
        }
        return objects;
    }

    private ArrayList<Object> getValueDatas(JSONObject jsonObject, Integer titleSize) {
        ArrayList<Object> objects = new ArrayList<>();
        for (int i = 1; i <= titleSize; i++) {
            String value = jsonObject.getStr("column" + i);
            objects.add(CharSequenceUtil.isEmpty(value) ? "-" : value);
        }
        return objects;
    }

    /**
     * 获取原始数据
     *
     * @param jsonObject
     * @param exportParameterString
     * @return
     */
    private ArrayList<Object> getOriginalValueDatas(JSONObject jsonObject, String exportParameterString) {
        ArrayList<Object> objects = new ArrayList<>();
        // 获取result数据
        JSONArray dataJsonArray = jsonObject.getJSONArray(DATA);
        objects.add(jsonObject.get(TIME));
        // 获取data里面的value数据
        for (int i = 0; i < dataJsonArray.size(); i++) {
            String value = dataJsonArray.getJSONObject(i).getStr(VALUE);
            objects.add(CharSequenceUtil.isEmpty(value) ? "-" : value);
        }
        // 获取最大值、最小值、平均值、累加值
        return addExportParameterByExportParameterString(jsonObject, objects, exportParameterString);
    }

    /**
     * 历史数据报表纵向导出y轴单元格创建（不含最大值最小值累加值平均值）
     *
     * @param titleJsonObject
     * @param resultJsonArray
     * @param index
     * @param exportParameterString
     * @return
     */
    private ArrayList<Object> getOriginalValueDatas(JSONObject titleJsonObject, JSONArray resultJsonArray, int index, String exportParameterString) {
        ArrayList<Object> objects = new ArrayList<>();
        objects.add(getDetailName(titleJsonObject));
        JSONObject jsonObject;
        String value;
        for (int i = 0; i < resultJsonArray.size() - 4; i++) {
            jsonObject = resultJsonArray.getJSONObject(i);
            value = jsonObject.getJSONArray(DATA).getJSONObject(index).getStr(VALUE);
            objects.add(CharSequenceUtil.isEmpty(value) ? "-" : value);
        }
        // 添加当前信号/数据的最大值、最小值、平均值、累计值等
        return addExportParameterByExportParameterString(resultJsonArray, index, objects, exportParameterString);
    }

    /**
     * 历史数据报表纵向导出y轴单元格创建（最大值最小值累加值平均值的处理）
     *
     * @param datas
     * @param resultJsonArry
     * @param exportParameterString
     * @return
     */
    private ArrayList<ArrayList<Object>> getOriginalValueDatas(ArrayList<ArrayList<Object>> datas, JSONArray resultJsonArry, String exportParameterString) {
        if (exportParameterString.contains(MAX)) {
            datas.add(getOriginalValueDatas(messageSourceUtil.getMessage("common.report.data.max"), MAX, resultJsonArry));
        }
        if (exportParameterString.contains(MIN)) {
            datas.add(getOriginalValueDatas(messageSourceUtil.getMessage("common.report.data.min"), MIN, resultJsonArry));
        }
        if (exportParameterString.contains(SUM)) {
            datas.add(getOriginalValueDatas(messageSourceUtil.getMessage("common.report.data.sum"), SUM, resultJsonArry));
        }
        if (exportParameterString.contains(AVG)) {
            datas.add(getOriginalValueDatas(messageSourceUtil.getMessage("common.report.data.avg"), AVG, resultJsonArry));
        }
        return datas;
    }

    /**
     * 历史数据报表纵向导出（最大值最小值累加值平均值的行处理）
     *
     * @param rowName
     * @param type
     * @param resultJsonArr
     * @return
     */
    private ArrayList<Object> getOriginalValueDatas(String rowName, String type, JSONArray resultJsonArr) {
        ArrayList<Object> objects = new ArrayList<>();
        objects.add(rowName);
        for (int i = 0; i < resultJsonArr.size(); i++) {
            objects.add(resultJsonArr.getJSONObject(i).get(type));
        }
        return objects;
    }

    /**
     * 获取标题信息 设备名-信号名(单位)/位置-指标名称
     *
     * @param jsonObject
     * @return
     */
    private String getDetailName(JSONObject jsonObject) {
        String complexIndexName = jsonObject.getStr(COMPLEX_INDEX_NAME);
        if (CharSequenceUtil.isNotEmpty(complexIndexName)) {
            // 处理指标
            return jsonObject.getStr(POSITION) + "-" + complexIndexName;
        } else {
            // 处理信号
            String signalName = jsonObject.getStr(SIGNALS_NAME);
            String equipmentName = jsonObject.getStr(EQUIPMENT_NAME);
            String unit = jsonObject.getStr(UNIT);
            unit = CharSequenceUtil.isEmpty(unit) ? "" : "(" + unit + ")";
            return equipmentName + "-" + signalName + unit;
        }
    }

    private ArrayList<Object> addExportParameterByExportParameterString(ArrayList<Object> objects, String exportParameterString) {
        if (exportParameterString.contains(MAX)) {
            objects.add(messageSourceUtil.getMessage("common.report.data.max"));
        }
        if (exportParameterString.contains(MIN)) {
            objects.add(messageSourceUtil.getMessage("common.report.data.min"));
        }
        if (exportParameterString.contains(AVG)) {
            objects.add(messageSourceUtil.getMessage("common.report.data.avg"));
        }
        if (exportParameterString.contains(SUM)) {
            objects.add(messageSourceUtil.getMessage("common.report.data.sum"));
        }
        return objects;
    }

    private ArrayList<Object> addExportParameterByExportParameterString(JSONObject jsonObject, ArrayList<Object> objects, String exportParameterString) {
        if (exportParameterString.contains(MAX)) {
            objects.add(jsonObject.get(MAX));
        }
        if (exportParameterString.contains(MIN)) {
            objects.add(jsonObject.get(MIN));
        }
        if (exportParameterString.contains(AVG)) {
            objects.add(jsonObject.get(AVG));
        }
        if (exportParameterString.contains(SUM)) {
            objects.add(jsonObject.get(SUM));
        }
        return objects;
    }

    /**
     * 历史报表纵向导出每一行的最大值最小值平均值累加值的单元格创建
     *
     * @param resultJsonArray
     * @param index
     * @param objects
     * @param exportParameterString
     * @return
     */
    private ArrayList<Object> addExportParameterByExportParameterString(JSONArray resultJsonArray, int index, ArrayList<Object> objects, String exportParameterString) {
        if (resultJsonArray != null && resultJsonArray.size() >= 4) {
            int size = resultJsonArray.size();
            if (exportParameterString.contains(MAX)) {
                objects.add(resultJsonArray.getJSONObject(size - 4).getJSONArray(DATA).getJSONObject(index).getStr(VALUE));
            }
            if (exportParameterString.contains(MIN)) {
                objects.add(resultJsonArray.getJSONObject(size - 3).getJSONArray(DATA).getJSONObject(index).getStr(VALUE));
            }
            if (exportParameterString.contains(SUM)) {
                objects.add(resultJsonArray.getJSONObject(size - 2).getJSONArray(DATA).getJSONObject(index).getStr(VALUE));
            }
            if (exportParameterString.contains(AVG)) {
                objects.add(resultJsonArray.getJSONObject(size - 1).getJSONArray(DATA).getJSONObject(index).getStr(VALUE));
            }
        }
        return objects;
    }

    /**
     * 获取时间数据行
     *
     * @param jsonArray
     * @return
     */
    private ArrayList<Object> getThValueDatasByResult(JSONArray jsonArray) {
        ArrayList<Object> objects = new ArrayList<>();
        objects.add("");
        for (int i = 0; i < jsonArray.size(); i++) {
            objects.add(jsonArray.getJSONObject(i).get(TIME));
        }

        return objects;
    }
}
