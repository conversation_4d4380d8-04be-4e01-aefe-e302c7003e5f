package com.siteweb.report.job;

import com.siteweb.report.entity.ReportTimingTaskManagement;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lzy
 * @Date: 2022/6/13 15:26
 */
@Component
public class ReportTimeJobManager {
    @Autowired
    SchedulerJobService schedulerService;

    private static final String JOB_GROUP = "ReportTimeJob";

    public void addJob(ReportTimingTaskManagement reportTimingTaskManagement) throws ClassNotFoundException, InstantiationException, SchedulerException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        if(reportTimingTaskManagement == null){
            return;
        }
        SchedulerJob schedulerJob = getJobFromReportTimingTaskManagement(reportTimingTaskManagement);
        schedulerService.addSchedulerJob(schedulerJob);
    }

    public void updateJob(ReportTimingTaskManagement reportTimingTaskManagement) throws ClassNotFoundException, InstantiationException, SchedulerException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        if(reportTimingTaskManagement == null){
            return;
        }
        SchedulerJob schedulerJob = getJobFromReportTimingTaskManagement(reportTimingTaskManagement);
        schedulerService.updateSchedulerJob(schedulerJob);
    }

    public void deleteJob(ReportTimingTaskManagement reportTimingTaskManagement) throws SchedulerException {
        if(reportTimingTaskManagement == null){
            return;
        }
        SchedulerJob schedulerJob = getJobFromReportTimingTaskManagement(reportTimingTaskManagement);
        schedulerService.removeSchedulerJob(schedulerJob);
    }

    private SchedulerJob getJobFromReportTimingTaskManagement(ReportTimingTaskManagement reportTimingTaskManagement) {
        SchedulerJob schedulerJob = new SchedulerJob();
        schedulerJob.setClassName(ReportTimeJob.class.getName());
        schedulerJob.setCronExpression(reportTimingTaskManagement.getStorageCycle());
        schedulerJob.setJobGroup(JOB_GROUP);
        schedulerJob.setJobName(JOB_GROUP+reportTimingTaskManagement.getReportTimingTaskManagementId());
        Map<String, Object> params = new HashMap<>();
        params.put("reportTimingTaskManagementId", reportTimingTaskManagement.getReportTimingTaskManagementId());
        params.put("reportId", reportTimingTaskManagement.getReportId());
        params.put("startTimeType", reportTimingTaskManagement.getStartTimeType());
        params.put("endTimeType", reportTimingTaskManagement.getEndTimeType());
        params.put("to", reportTimingTaskManagement.getTo());
        params.put("cc", reportTimingTaskManagement.getCc());
        schedulerJob.setParams(params);
        return schedulerJob;
    }
}
