package com.siteweb.report.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.Report;
import com.siteweb.report.entity.ReportParameterPreset;
import com.siteweb.report.entity.ReportTimingTaskFile;
import com.siteweb.report.entity.ReportTimingTaskResult;
import com.siteweb.report.manager.ReportDataManager;
import com.siteweb.report.mapper.ReportTimingTaskFileMapper;
import com.siteweb.report.service.ReportService;
import com.siteweb.report.vo.ReportVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2022/6/13 15:33
 */
@Service
public class ReportTimeJobExecService {

    @Autowired
    ReportService reportService;
    @Autowired
    ReportDataManager reportDataManager;
    @Autowired
    ReportTimingTaskFileMapper reportTimingTaskFileMapper;

    public ReportTimingTaskResult exec(Integer reportTimingTaskManagementId, Integer reportId, String startTimeType, String endTimeType) {
        Report report = reportService.findByReportId(reportId);
        if(report == null){
            return null;
        }
        if(!report.getReportParameterPresetList().isEmpty()){
            for(ReportParameterPreset reportParameterPreset : report.getReportParameterPresetList()){
                if("1".equals(reportParameterPreset.getReportSchemaQueryParameter().getParameterControlId())){
                    reportParameterPreset.setValue(getDateByTimeType(startTimeType));
                }
                if("2".equals(reportParameterPreset.getReportSchemaQueryParameter().getParameterControlId())){
                    reportParameterPreset.setValue(getDateByTimeType(endTimeType));
                }
            }
        }
        ReportTimingTaskFile reportTimingTaskFile = new ReportTimingTaskFile();
        reportTimingTaskFile.setCreateTime(new Date());
        reportTimingTaskFile.setReportTimingTaskManagementId(reportTimingTaskManagementId);
        ReportVO reportVO = BeanUtil.copyProperties(report, ReportVO.class);
        reportVO.setUserId(-1);
        JSONObject result = reportDataManager.findResult(reportVO);
        ReportTimingTaskResult reportTimingTaskResult = new ReportTimingTaskResult();
        reportTimingTaskResult.setResult(result);
        reportTimingTaskFile.setReportSchemaId(report.getReportSchemaId());
        reportTimingTaskFileMapper.insert(reportTimingTaskFile);
        reportTimingTaskResult.setReportTimingTaskFile(reportTimingTaskFile);
        return reportTimingTaskResult;
    }

    /**
     *   0 当前时间
     *  -1h 一小时前
     *  -1d 一天前
     *  -1w 一周前
     *  -1m 一月前
     */
    public String getDateByTimeType(String timeType){
        Date date = new Date();
        if(CharSequenceUtil.isNotEmpty(timeType) && !"0".equals(timeType)){
            int length = timeType.length();
            int time = Integer.parseInt(timeType.substring(0, length-1));
            String type = timeType.substring(length-1, length);
            switch (type) {
                case "h" -> date = DateUtil.dateAddHours(date, time);
                case "d" -> date = DateUtil.dateAddDays(date, time);
                case "w" -> date = DateUtil.dateAddDays(date, time * 7);
                case "m" -> date = DateUtil.dateAddMonth(date, time);
                default -> {}
            }
        }
        return DateUtil.dateToString(date);
    }

}
