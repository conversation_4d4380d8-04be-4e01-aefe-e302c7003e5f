package com.siteweb.report.job;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.PathUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.report.manager.ReportDataManager;
import com.siteweb.report.service.ReportExportService;
import com.siteweb.report.vo.ReportVO;
import com.siteweb.utility.configuration.FileServerConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 历史信号报表导出CSV
 * <AUTHOR>
 * @date 2023/05/13
 */
@Component
@Data
@Slf4j
public class ReportExportJob {
    public static final String USER_DATA = "userData" + GlobalConstants.PATH_SEPARATOR;
    private BlockingQueue<ReportVO> historySignalReportExportJobQueue  = new LinkedBlockingQueue<>(1000);
    @Autowired
    ReportExportService reportExportService;
    @Autowired
    FileServerConfig fileServerConfig;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    ReportDataManager reportDataManager;

    public void addReportTask(ReportVO reportVO) {
        try {
            historySignalReportExportJobQueue.offer(reportVO, 30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("添加报表导出队列错误", e);
            Thread.currentThread().interrupt();
        }
    }

    @PostConstruct
    public void init() {
        new Thread(() -> {
            Thread.currentThread().setName("report-export-thread");
            for (;;){
                ReportVO reportVO = null;
                try {
                    reportVO = historySignalReportExportJobQueue.take();
                    processReport(reportVO);
                } catch (InterruptedException e) {
                    log.error("报表导出线程被中断，但将继续运行", e);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                } catch (Exception e) {
                    // 捕获其他所有异常，保证线程不会意外终止
                    log.error("处理报表发生异常，reportVO: {}", reportVO, e);
                }
            }
        }).start();
    }

    private void processReport(ReportVO reportVO) {
        String filePath = getParentPath() + GlobalConstants.PATH_SEPARATOR + reportVO.getUserId() + GlobalConstants.PATH_SEPARATOR + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + ".csv";

        log.info("用户id:{} 开始导出报表文件，剩余报表导出队列长度:{}", reportVO.getUserId(), historySignalReportExportJobQueue.size());

        long startTime = System.currentTimeMillis();
        try {
            long count = reportDataManager.export(reportVO, filePath);
            log.info("用户Id:{} 导出报表文件完成，共导出数据:{}条,耗时：{}毫秒",
                    reportVO.getUserId(), count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("导出报表失败 userId：{}, reportId：{}, 耗时：{}毫秒",
                    reportVO.getUserId(), reportVO.getReportId(),
                    System.currentTimeMillis() - startTime, e);
        }
    }

    /**
     * 每次凌晨0点执行历报表的文件删除
     * 暂时只保留最近七天的报表文件
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void deleteReportCsv() {
        String parentPath = getParentPath();
        int retentionDays = 7; //七天过期 默认保留一个星期
        List<File> expiredFiles = findExpiredFiles(parentPath, retentionDays);
        deleteFiles(expiredFiles);
    }

    private String getParentPath() {
        return fileServerConfig.getRootPath() + GlobalConstants.PATH_SEPARATOR + USER_DATA;
    }

    /**
     * 获取所有的过期文件
     * @param parentPath 用户文件目录
     * @param retentionDays 保留时间 默认 7天
     * @return {@link List }<{@link File }>
     */
    private List<File> findExpiredFiles(String parentPath, int retentionDays) {
        return PathUtil.loopFiles(Paths.get(parentPath), null)
                       .stream()
                       .filter(file -> isFileExpired(file, retentionDays))
                       .toList();
    }

    private boolean isFileExpired(File file, int retentionDays) {
        return DateUtil.offsetDay(FileUtil.lastModifiedTime(file), retentionDays)
                       .isBefore(new Date());
    }

    private void deleteFiles(List<File> files) {
        if (files.isEmpty()) {
            return;
        }

        List<String> deletedFiles = files.stream()
                                         .map(file -> {
                                             FileUtil.del(file);
                                             return file.getName();
                                         })
                                         .toList();

        log.info("清理报表文件完成, 共清理{}个文件: {}", deletedFiles.size(), deletedFiles);
    }

}
