package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 报表文件夹实体类
 */
@Data
@TableName("reportfolder")
public class ReportFolder {
    /**
     * 文件夹ID
     */
    @TableId(value = "folderId", type = IdType.AUTO)
    private Integer folderId;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 父文件夹ID，根节点为0
     */
    private Integer parentId;

    /**
     * 排序值
     */
    private Integer sortIndex;
    /**
     * 创建时间
     */
    private Date createTime;
}