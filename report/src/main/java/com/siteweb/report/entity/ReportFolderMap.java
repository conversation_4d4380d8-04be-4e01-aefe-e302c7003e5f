package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 报表与文件夹映射关系实体类
 */
@Data
@TableName("reportfoldermap")
public class ReportFolderMap {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报表ID
     */
    private Integer reportId;

    /**
     * 文件夹ID
     */
    private Integer folderId;
    /**
     * 排序值
     */
    private Integer sortIndex;
    /**
     * 报表类型（1=普通，2=定时）
     */
    private Integer reportType;
}
