package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class CardSwipe {

    private Integer stationId;

    private Integer equipmentId;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 卡编号
     */
    private String cardCode;

    /**
     * 卡分组名称
     */
    private String cardGroupName;

    /**
     * 卡状态名称
     */
    private String cardStatusName;

    /**
     * 门号
     */
    private Integer doorNo;

    /**
     * 进门出门
     */
    private String enter;

    /**
     * 时间
     */
    private String recordTime;

    /**
     * 区域名称
     */
    @TableField(exist = false)
    private String areaName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 卡站点名称
     */
    private String cardStationName;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 持卡人
     */
    private String cardUserName;

    /**
     * 卡类别
     */
    private String cardCategoryName;

    /**
     * 门名称
     */
    private String doorName;

    /**
     * 门类别
     */
    private String doorCategoryName;

    /**
     * 刷卡或开门状态
     */
    private String validName;

}
