package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/9 13:09
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportexportparameterpreset")
public class ReportExportParameterPreset {

    @TableId(value = "ReportExportParameterPresetId", type = IdType.AUTO)
    private Integer reportExportParameterPresetId;

    /**
     * 报表ID
     */
    private Integer reportId;

    /**
     * 导出参数ID
     */
    private Integer reportSchemaExportParameterId;

    /**
     * 是否显示该参数
     */
    private Boolean display;

    /**
     * 导出参数实体
     */
    @TableField(exist = false)
    private ReportSchemaExportParameter reportSchemaExportParameter;

}

