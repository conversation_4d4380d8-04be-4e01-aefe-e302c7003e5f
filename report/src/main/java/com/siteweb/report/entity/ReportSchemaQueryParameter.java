package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/9 13:14
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportschemaqueryparameter")
public class ReportSchemaQueryParameter {

    @TableId(value = "ReportSchemaQueryParameterId", type = IdType.AUTO)
    private Integer reportSchemaQueryParameterId;

    /**
     * 查询参数名称
     */
    private String reportSchemaQueryParameterName;

    /**
     * 查询参数标题
     */
    private String reportSchemaQueryParameterTitle;

    /**
     * 报表SchemaID
     */
    private Integer reportSchemaId;

    /**
     * 参数控制ID，前端会根据该ID解析控件
     * 1:开始时间
     * 2:结束时间
     * 3:通用下拉控件
     * 4:设备
     * 5:设备信号
     * 6:指标
     * 7:支路空开测点
     * 8:即时报表id
     * 9:设备支路
     * 10：上传excel
     * 11：上传json
     * 12：输入框
     * 13：事件配置
     */
    private String parameterControlId;

    /**
     * 数据源表达式，配合parameterControlId使用
     */
    private String dataSourceExpression;

    /**
     * 解析dataSourceExpression的内容格式
     */
    private String dataSourceReturnTableName;

    /**
     * 是否为空
     */
    private Boolean isNull;

    /**
     * 排序顺序
     */
    private Integer sortIndex;
}
