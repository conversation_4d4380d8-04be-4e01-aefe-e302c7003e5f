package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.report.enums.ReportExportStyleEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("reporttimingtaskfile")
public class ReportTimingTaskFile {

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer reportTimingTaskFileId;
    /**
     * 报表定时任务管理ID
     */
    private Integer reportTimingTaskManagementId;

    /**
     * 报表SchemaId
     */
    private Integer reportSchemaId;
    /**
     * 文件内容
     */
    private String file;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 创建时间
     */
    private Date createTime;

    @JsonIgnore
    @TableField(exist = false)
    private String reportName;

    /**
     * 解析类型
     * 1：通用
     * 2：历史数据，历史指标
     * 3：电池
     * 4：定制报表
     *
     * @return
     */
    public Integer getType() {
        Integer type = 0;
        switch (this.reportSchemaId) {
            case 1: // 当前告警
            case 2: // 历史告警
            case 5:
            case 6:
            case 7: // 告警操作日志
            case 11:
            case 12:
            case 13: // 控制记录
            case 15: // 告警数量统计
            case 17:
            case 21:
                type = ReportExportStyleEnum.BASE_DATA.getValue();
                break;
            case 10: // 历史指标
                type = ReportExportStyleEnum.COMPLEXINDEX.getValue();
                break;
            case 3: // 历史数据
            case 16: // 用电统计
            case 28: // 历史数据（5分钟）
            case 20:
                type = ReportExportStyleEnum.INFLUXDB_DATA.getValue();
                break;
            case 9: // 历史放电
                type = ReportExportStyleEnum.BATTERY_DATA.getValue();
                break;
            case 14: // 即时报表
            case 18: // 通用定制
            case 19: // 日期定制报表（PUE报表）
            case 22: // 当前日期定制报表,支持历史数据，公式查询
                type = ReportExportStyleEnum.CUSTOM_DATA.getValue();
                break;
            default:
                type = ReportExportStyleEnum.BASE_DATA.getValue();
                break;
        }
        return type;
    }
}
