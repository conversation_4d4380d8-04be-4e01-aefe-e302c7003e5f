package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/9 13:09
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportparameterpreset")
public class ReportParameterPreset {

    /**
     * 主键
     */
    @TableId(value = "ReportParameterPresetId", type = IdType.AUTO)
    private Integer reportParameterPresetId;

    /**
     * 报表ID
     */
    private Integer reportId;

    /**
     * 查询参数ID
     */
    private Integer reportSchemaQueryParameterId;

    /**
     * 预设值
     */
    private String value;

    /**
     * 是否展示
     */
    private Boolean display;

    /**
     * 查询参数实体
     */
    @TableField(exist = false)
    private ReportSchemaQueryParameter reportSchemaQueryParameter;

}
