package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.report.enums.ReportDisplayStyleEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @author: Habits
 * @time: 2022/5/9 10:35
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("report")
public class Report {

    /**
     * 主键
     */
    @TableId(value = "reportId", type = IdType.AUTO)
    private Integer reportId;

    /**
     * 报表名称
     */
    private String reportName;

    /**
     * 报表SchemaID
     */
    private Integer reportSchemaId;

    /**
     * 报表Schema类别ID
     */
    private Integer reportSchemaCategoryId;

    /**
     * 修改人
     */
    private Integer updateUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 报表数据源ID
     */
    private Integer reportDataSourceId;

    /**
     * 报表备注
     */
    private String reportDescription;

    private Integer maxQueryInterval;

    @TableField(exist = false)
    private List<ReportParameterPreset> reportParameterPresetList;

    @TableField(exist = false)
    private List<ReportExportParameterPreset> reportExportParameterPresetList;
    /**
     * 报表拥有人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer createUserId;
    /**
     * 是否公开
     */
    private Boolean overt;
    /**
     * 报表列配置
     */
    private JsonNode columnConfig;

    public List<ReportParameterPreset> getReportParameterPresetList() {
        if (reportParameterPresetList == null){
            return Collections.emptyList();
        }
        return reportParameterPresetList;
    }

    public List<ReportExportParameterPreset> getReportExportParameterPresetList() {
        if (reportExportParameterPresetList == null){
            return Collections.emptyList();
        }
        return reportExportParameterPresetList;
    }

    /**
     * 解析类型
     * 1：通用
     * 2：历史数据，历史指标
     * 3：电池
     * 4：定制报表
     *
     * @return
     */
    /**
     * 解析类型
     * 1：通用
     * 2：历史数据，历史指标
     * 3：电池
     * 4：定制报表
     *
     * @return
     */
    public Integer getType() {
        return switch (this.reportSchemaId) {
            case 1, 2, 4, 25, 26, 27, 32, 33, 34, 35,37, 38 -> ReportDisplayStyleEnum.BASE_DATA_BACKEND_PAGING.getValue();
            case 3, 10, 16, 20, 28 -> ReportDisplayStyleEnum.INFLUXDB_DATA.getValue();
            case 9 -> ReportDisplayStyleEnum.BATTERY_DATA.getValue();
            case 14 -> ReportDisplayStyleEnum.CUSTOM_DATA_SIMPLE.getValue();
            case 17, 18, 19, 22 -> ReportDisplayStyleEnum.CUSTOM_DATA_COMPLEX.getValue();
            default -> ReportDisplayStyleEnum.BASE_DATA_FONT_PAGING.getValue();
        };
    }

}
