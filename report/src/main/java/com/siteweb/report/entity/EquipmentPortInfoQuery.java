package com.siteweb.report.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * Description: 设备端口信息查询参数
 * Author: system
 * Creation Date: 2024/12/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EquipmentPortInfoQuery {


    // 楼栋IDs (一级区域的层级ID集合）
    private List<Integer> resourceStructureIds;

    // 设备名称
    private String equipmentName;
    
    // 设备类型
    private Set<Integer> equipmentCategory;
    
    // 监控单元名称
    private String monitorUnitName;
    
    // 监控单元IP地址
    private String ipAddress;
    
    // 端口名称
    private String portName;
    
    // 采样器名称
    private String samplerUnitName;
    
    // 连接状态
    private Integer equipmentState;
    
    // 设备模板名称
    private String equipmentTemplateName;
} 