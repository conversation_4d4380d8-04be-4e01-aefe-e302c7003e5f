package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel("报表定时任务")
@TableName("reporttimingtaskmanagement")
public class ReportTimingTaskManagement {

	@TableId(type = IdType.AUTO)
	@ApiModelProperty("任务id")
	private Integer reportTimingTaskManagementId;

	@ApiModelProperty("任务名称")
	private String reportTimingTaskManagementName;

	@ApiModelProperty("报表id")
	private Integer reportId;

	@ApiModelProperty("报表名称")
	private String reportName;
	/**
	 * cron表达式
	 * 每小时 0 0 0/1 * * ? *
	 * 每12小时 0 0 0/12 * * ? *
	 * 每天 0 0 0 * * ? *
	 * 每周 0 0 0 ? * 2/7 (周一为第一天)
	 * 每月 0 0 0 1 1/1 ?
	 */
	@ApiModelProperty("存储周期(cron)")
	private String storageCycle;
	/**
	 *  0 当前时间
	 *  -1h 一小时前
	 *  -1d 一天前
	 *  -1w 一周前
	 *  -1m 一月前
	 */
	@ApiModelProperty("开始时间类型")
	private String startTimeType;
	/**
	 *  0 当前时间
	 *  -1h 一小时前
	 *  -1d 一天前
	 *  -1w 一周前
	 *  -1m 一月前
	 */
	@ApiModelProperty("开始时间类型")
	private String endTimeType;
	/**
	 * false 未启用
	 * true 启用
	 */
	@ApiModelProperty("状态")
	private Boolean status;

	@TableField("`to`")
	@ApiModelProperty("收件人")
	private String to;

	@ApiModelProperty("抄送人")
	private String cc;

	@TableField(exist = false)
	private Integer reportSchemaCategoryId;

	@TableField(exist = false)
	private Integer reportSchemaId;
	@ApiModelProperty("创建人id")
	private Integer createUserId;
	@ApiModelProperty("是否公开")
	private Boolean overt;
	/**
	 * 导出参数实体
	 */
	@TableField(exist = false)
	private List<ReportExportParameterPreset> reportExportParameterPresetList;
}
