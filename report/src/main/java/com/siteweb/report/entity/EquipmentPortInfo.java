package com.siteweb.report.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * Description: 设备端口信息实体类
 * Author: system
 * Creation Date: 2024/12/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class EquipmentPortInfo {
    // 设备ID
    private Integer equipmentId;
    
    // 一级区域名称
    private String resourceStructureName;

    // 设备名称
    private String equipmentName;
    
    // 设备类型
    private String equipmentCategory;
    
    // 监控单元名称
    private String monitorUnitName;
    
    // 监控单元IP地址
    private String ipAddress;
    
    // 端口名称
    private String portName;
    
    // 端口设置
    private String setting;
    
    // 采样器名称
    private String samplerUnitName;
    
    // 采样器地址
    private String address;
    
    // 连接状态
    private Integer equipmentState;
    
    // 设备模板名称
    private String equipmentTemplateName;

    private Integer resourceStructureId; //层级id
    private String stationId;           // 局站ID
    private String stationName;         // 局站名称
    private String monitorUnitId;       // 监控单元ID
}