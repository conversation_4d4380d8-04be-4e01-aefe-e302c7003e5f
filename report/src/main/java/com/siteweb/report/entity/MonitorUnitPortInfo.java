package com.siteweb.report.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/12/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorUnitPortInfo {
    // 站点名称
    private String stationName;

    // 监控单元名称
    private String monitorUnitName;

    // 监控单元ID
    private Long monitorUnitId;

    // 端口名称
    private String portName;

    // 端口类型代码
    private Integer portType;

    // 端口类型名称
    private String portTypeName;

    // 端口号
    private Integer portNo;

    // 端口设置
    private String setting;

    // DLL路径
    private String dllPath;

    // 更新时间
    private Date updateTime;

    // 采样器数量
    private Integer samplerUnitCount;
}
