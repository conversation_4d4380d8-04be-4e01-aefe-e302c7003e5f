package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email 
 * @date 2020-09-17 10:36:37
 */
@Data
@NoArgsConstructor
@ApiModel("报表定时任务时间类型")
@TableName("reporttimingtasktimetype")
public class ReportTimingTaskTimeType {

	@TableId(type = IdType.AUTO)
	@ApiModelProperty("类型id")
	private Integer reportTimingTaskTimeTypeId;

	@ApiModelProperty("类型名称")
	private String timeTypeName;

	@ApiModelProperty("类型值")
	private String timeTypeValue;
}
