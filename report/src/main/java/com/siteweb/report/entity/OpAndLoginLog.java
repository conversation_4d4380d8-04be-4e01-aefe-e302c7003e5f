package com.siteweb.report.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email
 * @date 2019-01-22 10:26:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpAndLoginLog {

    /**
     * 主键ID
     */
    private Integer oplId;
    /**
     * 用户人员ID
     */
    private String userId;
    private String personId;
    private String name;
    private String title;
    /**
     * 工号
     */
    private String jobNumber;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 操作时间
     */
    private String operationTime;

    private String objectId;

    private Integer objectType;

    private String objectTypeName;

    private String oldValue;

    private String newValue;

    private String propertyName;
}
