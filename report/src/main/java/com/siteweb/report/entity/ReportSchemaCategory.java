package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/4/29 13:07
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportschemacategory")
public class ReportSchemaCategory {

    /**
     * 主键ID
     */
    @TableId(value = "ReportSchemaCategoryId", type = IdType.AUTO)
    private Integer reportSchemaCategoryId;

    /**
     * 报表Schema类型名称
     */
    private String reportSchemaCategoryName;

    /**
     * 备注
     */
    private String reportSchemaCategoryDescription;

    /**
     * 路径
     */
    private String reportSchemaCategoryPath;

    /**
     * 排序
     */
    private String sortVal;
}
