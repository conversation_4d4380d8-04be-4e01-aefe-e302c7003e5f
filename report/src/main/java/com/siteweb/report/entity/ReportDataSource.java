package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/9 10:09
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportdatasource")
public class ReportDataSource {

    /**
     * 主键ID
     */
    @TableId(value = "ReportDataSourceId", type = IdType.AUTO)
    private Integer reportDataSourceId;

    /**
     * 报表数据源名称
     */
    private String reportDataSourceName;

    /**
     * 报表数据源备注
     */
    private String reportDataSourceDescription;

}
