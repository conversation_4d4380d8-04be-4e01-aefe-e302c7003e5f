package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/5/9 13:28
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportschemaexportparameter")
public class ReportSchemaExportParameter {

    /**
     * 主键
     */
    @TableId(value = "ReportSchemaExportParameterId", type = IdType.AUTO)
    private Integer reportSchemaExportParameterId;

    /**
     * 导出参数名称
     */
    private String reportSchemaExportParameterName;

    /**
     * 导出参数标题
     */
    private String reportSchemaExportParameterTitle;

    /**
     * 报表SchemaID
     */
    private Integer reportSchemaId;

    /**
     * 是否为空
     */
    private Boolean isNull;

}
