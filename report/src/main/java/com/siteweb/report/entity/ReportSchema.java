package com.siteweb.report.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.report.enums.ReportDisplayStyleEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: Habits
 * @time: 2022/4/29 13:07
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("reportschema")
public class ReportSchema {

    /**
     * 主键ID
     */
    @TableId(value = "ReportSchemaId", type = IdType.AUTO)
    private Integer reportSchemaId;

    /**
     * 报表Schema名称
     */
    private String reportSchemaName;

    /**
     * 备注
     */
    private String reportSchemaDescription;

    /**
     * 版本
     */
    private String version;

    /**
     * 报表Schema类型ID
     */
    private Integer reportSchemaCategoryId;

    /**
     * 作者
     */
    private String author;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 报表数据源ID
     */
    private Integer reportDataSourceId;

    /**
     * 是否显示
     */
    private Integer viewControlId;

    private Integer maxQueryInterval;
    /**
     * 报表数据源
     */
    @TableField(exist = false)
    private ReportDataSource reportDataSource;

    /**
     * 报表集合
     */
    @TableField(exist = false)
    private List<Report> reportList;

    /**
     * 报表Schema导出参数集合
     */
    @TableField(exist = false)
    private List<ReportSchemaExportParameter> reportSchemaExportParameterList;

    @TableField(exist = false)
    private List<ReportSchemaQueryParameter> reportSchemaQueryParameterList;

    /**
     * 分页解析类型
     * 1：前端分页
     * 2：后端分页
     * 3：历史数据，历史指标
     * 4：电池
     * 5：即时报表
     * 6：通用自定义报表
     *
     * @return
     */
    public Integer getType() {
        return switch (this.reportSchemaId) {
            case 1, 2, 4, 25, 26, 27, 32, 33, 34, 35,37, 38, 41 -> ReportDisplayStyleEnum.BASE_DATA_BACKEND_PAGING.getValue();
            case 3, 10, 16, 20, 28 -> ReportDisplayStyleEnum.INFLUXDB_DATA.getValue();
            case 9 -> ReportDisplayStyleEnum.BATTERY_DATA.getValue();
            case 14 -> ReportDisplayStyleEnum.CUSTOM_DATA_SIMPLE.getValue();
            case 17, 18, 19, 22 -> ReportDisplayStyleEnum.CUSTOM_DATA_COMPLEX.getValue();
            default -> ReportDisplayStyleEnum.BASE_DATA_FONT_PAGING.getValue();
        };
    }
}

