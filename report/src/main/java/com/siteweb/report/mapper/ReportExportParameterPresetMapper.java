package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.entity.ReportExportParameterPreset;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportExportParameterPresetMapper extends BaseMapper<ReportExportParameterPreset> {

    /**
     * 批量新增报表查询预设值
     * @param list
     * @return
     */
    int batchCreateReportExportParameterPreset(List<ReportExportParameterPreset> list);
    List<ReportExportParameterPreset> findByReportId(Integer reportId);

    int batchUpdateReportExportParameterPreset(@Param("reportExportParameterPresetList") List<ReportExportParameterPreset> reportExportParameterPresetList);
}
