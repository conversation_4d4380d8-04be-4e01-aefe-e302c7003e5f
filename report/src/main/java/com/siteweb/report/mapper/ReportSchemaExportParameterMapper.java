package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.entity.ReportSchemaExportParameter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportSchemaExportParameterMapper extends BaseMapper<ReportSchemaExportParameter> {
    List<ReportSchemaExportParameter> findByReportSchemaId(@Param("reportSchemaId") Integer reportSchemaId);

    ReportSchemaExportParameter findById(@Param("reportSchemaExportParameterId") Integer reportSchemaExportParameterId);
}
