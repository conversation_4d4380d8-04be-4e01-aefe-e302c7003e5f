package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.report.entity.CardSwipe;
import com.siteweb.report.parser.model.CardSwipeParam;
import org.apache.ibatis.annotations.Param;

public interface CardSwipeMapper extends BaseMapper<CardSwipe> {
    Page<CardSwipe> getPageCardSwipe(@Param("page") Page<CardSwipe> page, @Param("cardSwipeParam") CardSwipeParam cardSwipeParam);
}
