package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.entity.ReportTimingTaskManagement;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/13 10:06
 */
public interface ReportTimingTaskManagementMapper extends BaseMapper<ReportTimingTaskManagement> {

    int create(@Param("entity") ReportTimingTaskManagement entity);

    int update(@Param("entity") ReportTimingTaskManagement entity);

    ReportTimingTaskManagement findReportTimingTaskManagementById(Integer id);

    List<ReportTimingTaskManagement> findByUserId(@Param("userId") Integer userId);
}
