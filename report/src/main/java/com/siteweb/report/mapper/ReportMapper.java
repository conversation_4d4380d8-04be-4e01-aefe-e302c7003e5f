package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.entity.Report;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportMapper extends BaseMapper<Report> {
    List<Report> findReportByUserIdAndSchemaId(@Param("userId") Integer userId, @Param("reportSchemaId") Integer reportSchemaId);
}
