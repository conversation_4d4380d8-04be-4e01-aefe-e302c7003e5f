package com.siteweb.report.mapper;

import com.siteweb.report.entity.OpAndLoginLog;

import java.util.Date;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/8/25 16:34
 */
public interface OpAndLoginLogMapper {

    List<OpAndLoginLog> findOperationRecordByDataAndLogIds(Date startTime, Date endTime, List<Integer> userIds);


    List<OpAndLoginLog> findOperationDetailByDataAndLogIds(Date startTime, Date endTime, List<Integer> userIds);

}
