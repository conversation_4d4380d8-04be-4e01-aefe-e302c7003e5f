package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.entity.ReportSchemaQueryParameter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportSchemaQueryParameterMapper extends BaseMapper<ReportSchemaQueryParameter> {
    List<ReportSchemaQueryParameter> findByByReportSchemaId(@Param("reportSchemaId") Integer reportSchemaId);

    ReportSchemaQueryParameter findById(@Param("reportSchemaQueryParameterId") Integer reportSchemaQueryParameterId);
}
