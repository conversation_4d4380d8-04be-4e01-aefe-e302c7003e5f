package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.report.entity.ReportParameterPreset;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportParameterPresetMapper extends BaseMapper<ReportParameterPreset> {

    /**
     * 批量新增报表查询预设值
     * @param list
     * @return
     */
    int batchCreateReportParameterPreset(List<ReportParameterPreset> list);
    List<ReportParameterPreset> findByReportId(Integer reportId);

    int batchUpdateReportParameterPreset(@Param("reportParameterPresetList") List<ReportParameterPreset> reportParameterPresetList);
}
