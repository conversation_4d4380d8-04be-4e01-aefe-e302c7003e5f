package com.siteweb.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.hmi.dto.StatisticsResult;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.report.dto.AllEventDTO;
import com.siteweb.report.dto.EventClassificationStatisticalDTO;
import com.siteweb.report.dto.HistoryEventStatisticsDTO;
import com.siteweb.report.parser.model.AllEventParam;
import com.siteweb.report.parser.model.EventClassificationStatisticalParam;
import com.siteweb.report.vo.EventCount;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

public interface TotalEventMapper extends BaseMapper<HistoryEvent> {
    /**
     * 查询活动告警和历史告警中有告警记录的层级id(已去重)
     *
     * @param startDate            开始时间
     * @param endDate              结束时间
     * @param resourceStructureIds
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findResourceStructureIdBirthDuration(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("resourceStructureIds") List<Integer> resourceStructureIds);

    /**
     * 通过资源类型找到事件计数
     * 查找层级资源类型下的活动告警数量
     *
     * @param startTime                 开始时间
     * @param endTime                   结束时间
     * @param eventResourceStructureIds 层级ids
     * @return {@link List}<{@link EventCount}>
     */
    List<EventCount> findEventCountInResourceId(@Param("eventResourceStructureIds") List<Integer> eventResourceStructureIds, @Param("equipmentIds") Collection<Integer> equipmentIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 通过资源类型找到历史事件数量
     *
     * @param eventResourceStructureIds 层级ids
     * @param startTime                 开始时间
     * @param endTime                   结束时间
     * @return {@link List}<{@link EventCount}>
     */
    List<EventCount> findHistoryEventCountInResourceId(@Param("eventResourceStructureIds") List<Integer> eventResourceStructureIds, @Param("equipmentIds") Collection<Integer> equipmentIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    Page<AllEventDTO> findAllEvent(@Param("param") AllEventParam param,Page<AllEventDTO> page);

    long findAllEventCount(@Param("param") AllEventParam param);

    List<HistoryEventStatisticsDTO> findHistoryEventBetweenStartTimeAndEquipmentIds(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("equipmentIds") List<Integer> equipmentIds);

    List<StatisticsResult> findHistoryEventBetweenStartTimeAndCategories(@Param("startDate") LocalDateTime startDate,
                                                                         @Param("endDate") LocalDateTime endDate,
                                                                         @Param("equipmentCategories") List<Integer> equipmentCategories,
                                                                         @Param("eventLevelList") List<Integer> eventLevelList);

    List<EventClassificationStatisticalDTO> findEventClassificationStatistical(@Param("param") EventClassificationStatisticalParam param);
}
