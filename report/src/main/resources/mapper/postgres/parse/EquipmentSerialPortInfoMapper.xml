<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.parser.mapper.EquipmentSerialPortInfoMapper">
    <select id="findMonitorUnitPortInfo" resultType="com.siteweb.report.entity.MonitorUnitPortInfo">
        SELECT
            ts.stationname,
            tm.monitorunitname,
            tp.monitorunitid,
            tp.portname,
            tp.porttype,
            pt.portname AS porttypename,
            tp.portno,
            tp.setting,
            ts2.dllpath,
            ts2.updatetime,
            COUNT(CASE WHEN ts2.dllpath IS NOT NULL THEN 1 END) AS samplerunitcount
        FROM
            tsl_port tp
        LEFT JOIN
            tsl_monitorunit tm ON tp.monitorunitid = tm.monitorunitid
        LEFT JOIN
            tbl_station ts ON ts.stationid = tm.stationid
        LEFT JOIN (
            SELECT
                itemvalue AS portname,
                itemid AS porttype
            FROM
                tbl_dataitem td
            WHERE
                td.entryid = 39
        ) pt ON pt.porttype = tp.porttype
        LEFT JOIN
            tsl_samplerunit ts2 ON ts2.portid = tp.portid
        WHERE
            1 = 1
        <if test="stationIds != null and stationIds.size() > 0">
            AND ts.stationid IN
            <foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="monitorUnitName != null and monitorUnitName != ''">
            AND tm.monitorunitname LIKE CONCAT('%', #{monitorUnitName}, '%')
        </if>
        <if test="portName != null and portName != ''">
            AND tp.portname LIKE CONCAT('%', #{portName}, '%')
        </if>
        GROUP BY
            ts.stationname,
            tm.monitorunitname,
            tp.monitorunitid,
            tp.portname,
            tp.porttype,
            pt.portname,
            tp.portno,
            tp.setting,
            ts2.dllpath,
            ts2.updatetime
        ORDER BY
            tp.portno
    </select>
    
    <select id="findEquipmentPortInfo" resultType="com.siteweb.report.entity.EquipmentPortInfo">
        SELECT
            a.EquipmentId AS equipmentId,
            CONCAT(h.resourcestructurename, '/', g.resourcestructurename, '/', b.resourcestructurename) AS ResourceStructureName,
            a.equipmentname AS equipmentName,
            i.itemvalue AS equipmentCategory,
            f.monitorunitname AS monitorUnitName,
            f.ipaddress AS ipAddress,
            e.portname AS portName,
            e.setting AS setting,
            c.samplerunitname AS samplerUnitName,
            c.address AS address,
            a.equipmentstate AS equipmentState,
            d.equipmenttemplatename AS equipmentTemplateName,
            b.ResourceStructureId AS resourceStructureId,
            s.StationId AS stationId,
            s.StationName AS stationName,
            f.MonitorUnitId AS monitorUnitId
        FROM 
            tbl_equipment a
        INNER JOIN 
            resourcestructure b ON a.resourcestructureid = b.resourcestructureid
        INNER JOIN 
            tsl_samplerunit c ON a.samplerunitid = c.samplerunitid AND a.monitorunitid = c.monitorunitid
        INNER JOIN 
            tbl_equipmenttemplate d ON a.equipmenttemplateid = d.equipmenttemplateid
        INNER JOIN 
            tsl_port e ON c.portid = e.portid
        INNER JOIN 
            tsl_monitorunit f ON a.monitorunitid = f.monitorunitid
        INNER JOIN 
            resourcestructure g ON g.resourcestructureid = b.parentresourcestructureid
        INNER JOIN 
            resourcestructure h ON h.resourcestructureid = g.parentresourcestructureid
        INNER JOIN 
            tbl_dataitem i ON a.equipmentcategory = i.itemid AND i.entryid = 7
        INNER JOIN tbl_station s ON a.StationID = s.StationID
        WHERE 
            1 = 1
        <if test="query.resourceStructureIds != null and query.resourceStructureIds.size() > 0">
            AND a.resourcestructureid IN
            <foreach item="item" index="index" collection="query.resourceStructureIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.equipmentName != null and query.equipmentName != ''">
            AND a.equipmentname LIKE CONCAT('%', #{query.equipmentName}, '%')
        </if>
        <if test="query.equipmentCategory != null and query.equipmentCategory.size() > 0">
            AND a.equipmentcategory IN
            <foreach item="item" index="index" collection="query.equipmentCategory" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.monitorUnitName != null and query.monitorUnitName != ''">
            AND f.monitorunitname LIKE CONCAT('%', #{query.monitorUnitName}, '%')
        </if>
        <if test="query.ipAddress != null and query.ipAddress != ''">
            AND f.ipaddress LIKE CONCAT('%', #{query.ipAddress}, '%')
        </if>
        <if test="query.portName != null and query.portName != ''">
            AND e.portname LIKE CONCAT('%', #{query.portName}, '%')
        </if>
        <if test="query.samplerUnitName != null and query.samplerUnitName != ''">
            AND c.samplerunitname LIKE CONCAT('%', #{query.samplerUnitName}, '%')
        </if>
        <if test="query.equipmentState != null">
            AND a.equipmentstate = #{query.equipmentState}
        </if>
        <if test="query.equipmentTemplateName != null and query.equipmentTemplateName != ''">
            AND d.equipmenttemplatename LIKE CONCAT('%', #{query.equipmentTemplateName}, '%')
        </if>
        ORDER BY 
            h.resourcestructurename ASC,
            g.resourcestructurename ASC,
            b.resourcestructurename ASC,
            f.monitorunitname ASC,
            e.portno ASC
    </select>
</mapper>