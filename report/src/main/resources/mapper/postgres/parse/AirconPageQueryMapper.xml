<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.parser.mapper.AirconPageQueryMapper">


    <select id="findBatchControlGroupChangeLogDataByPage" resultType="com.siteweb.airconditioncontrol.entity.AirBatchControlGroupChangeLog"
            parameterType="com.siteweb.report.parser.model.BatchControlGroupChangeLogParam">
        SELECT a.OperateTypeLabel, a.Operator, a.GroupName, a.GroupNameNew, a.InsertTime, a.ChangeContent
        FROM Aircon_BatchControlGroupChangeLog a
        WHERE 1 = 1
        <if test="param.startDate != null">
            and a.InsertTime &gt;= #{param.startDate}
        </if>
        <if test="param.endDate != null">
            and a.InsertTime &lt;= #{param.endDate}
        </if>
        <if test="param.changeType != null">
            and a.OperateType = #{param.changeType}
        </if>
        ORDER By a.InsertTime DESC
    </select>

    <select id="findBatchControlCmdHistoryByPage" resultType="com.siteweb.airconditioncontrol.dto.AirHistoryControlDTO"
                parameterType="com.siteweb.report.parser.model.BatchControlCmdHistoryParam">
        SELECT tacTbl.*, tmu.MonitorUnitName, th.HouseName, e.EquipmentName
        FROM
        (
        SELECT  bcr.StationId, bcr.EquipmentId,
                bcr.StdControlId ControlType, bcr.StdWorkModeFlag WorkModeValue,
                bcr.AirStdTypeId, bcr.InsertTime RecordTime, bcr.airCommon2No,
                thc.StationName,thc.EquipmentName TacEquipmentName,thc.ControlId,thc.ControlName,thc.SerialNo,thc.ControlSeverity,
                thc.CmdToken,thc.ControlPhase,thc.StartTime,thc.EndTime,thc.ConfirmTime,thc.ConfirmerId,
                thc.ConfirmerName,thc.ControlResultType,thc.ControlResult,thc.ControlExecuterId,thc.ControlExecuterIdName,
                thc.ActionId,thc.Description,thc.Retry,thc.BaseTypeId,thc.BaseTypeName,thc.ParameterValues, thc.BaseCondId
        FROM Aircon_BatchControlRecord bcr
                INNER JOIN TBL_HistoryControl thc ON bcr.SerialNo = thc.SerialNo
        WHERE  1 = 1
            <if test="param.startDate != null">
                and bcr.InsertTime &gt;= #{param.startDate}
            </if>
            <if test="param.endDate != null">
                and bcr.InsertTime &lt;= #{param.endDate}
            </if>
            <if test="param.equipmentIds != null">
                and bcr.EquipmentId IN (${param.equipmentIds})
            </if>
            <if test="param.cmdType != null">
                and bcr.StdControlId = #{param.cmdType}
            </if>
        ) tacTbl
            LEFT JOIN TBL_Equipment e ON tacTbl.StationId = e.StationId AND tacTbl.EquipmentId = e.EquipmentId
            LEFT JOIN TSL_MonitorUnit tmu ON e.MonitorUnitId = tmu.MonitorUnitId
            LEFT JOIN TBL_House th ON e.StationId = th.StationId AND e.houseId = th.houseId
        ORDER BY tacTbl.RecordTime, tacTbl.StartTime
    </select>

    <select id="findAirAutoControlEquipmentChangeLogByPage" resultType="com.siteweb.airconditioncontrol.entity.AirAutoControlEquipmentChangeLog"
            parameterType="com.siteweb.report.parser.model.AirAutoControlEquipmentChangeLogParam">
        SELECT *
        FROM Aircon_AutoControlEquipmentChangeLog a
        WHERE 1 = 1
        <if test="param.startDate != null">
            and a.InsertTime &gt;= #{param.startDate}
        </if>
        <if test="param.endDate != null">
            and a.InsertTime &lt;= #{param.endDate}
        </if>
        <if test="param.changeType != null">
            and a.OperateType = #{param.changeType}
        </if>
        <if test="param.stationIds != null">
            and a.StationId IN (${param.stationIds})
        </if>
        ORDER By a.InsertTime DESC
    </select>
</mapper>