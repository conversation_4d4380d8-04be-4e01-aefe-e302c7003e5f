<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.parser.mapper.HistoryPreAlarmParserMapper">

    <select id="findHistoryDataByPage" resultType="com.siteweb.prealarm.entity.PreAlarmHistory"
            parameterType="com.siteweb.report.parser.querywrapper.HistoryPreAlarmQueryWrapper">
        select a.PreAlarmSeverityName, a.LevelOfPathName, a.ObjectName, a.PreAlarmCategoryName, a.TriggerValue, a.StartTime,
                a.ConfirmTime, a.EndTime, a.ConfirmName, a.Meanings
        from prealarmhistory a
        where 1 = 1
        <if test="wrapper.startDate != null">
            and a.SampleTime &gt;= #{wrapper.startDate}::timestamptz
        </if>
        <if test="wrapper.endDate != null">
            and a.SampleTime &lt;= #{wrapper.endDate}::timestamptz
        </if>
        <if test="wrapper.preAlarmCategory != null">
            and a.PreAlarmCategory = #{wrapper.preAlarmCategory}
        </if>
        <if test="wrapper.preAlarmSeverity != null">
            and a.PreAlarmSeverity = #{wrapper.preAlarmSeverity}
        </if>
        <if test="wrapper.levelOfPathName != null">
            and a.LevelOfPathName like concat('%%',#{wrapper.levelOfPathName},'%%')
        </if>
        <if test="wrapper.objectName != null">
            and a.ObjectName like concat('%%',#{wrapper.objectName},'%%')
        </if>
        order by a.SampleTime desc
    </select>

</mapper>