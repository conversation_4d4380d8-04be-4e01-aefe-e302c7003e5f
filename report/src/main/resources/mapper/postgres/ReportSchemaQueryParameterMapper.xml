<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportSchemaQueryParameterMapper">
    <select id="findByByReportSchemaId" resultType="com.siteweb.report.entity.ReportSchemaQueryParameter">
        SELECT ReportSchemaQueryParameterId,
               ReportSchemaQueryParameterName,
               ReportSchemaQueryParameterTitle,
               ReportSchemaId,
               ParameterControlId,
               DataSourceExpression,
               DataSourceReturnTableName,
               "isnull",
               SortIndex
        FROM ReportSchemaQueryParameter
        WHERE reportSchemaId = #{reportSchemaId}
        order by SortIndex
    </select>
    <select id="findById" resultType="com.siteweb.report.entity.ReportSchemaQueryParameter">
        SELECT ReportSchemaQueryParameterId,
               ReportSchemaQueryParameterName,
               ReportSchemaQueryParameterTitle,
               ReportSchemaId,
               ParameterControlId,
               DataSourceExpression,
               DataSourceReturnTableName,
               "isnull",
               SortIndex
        FROM ReportSchemaQueryParameter
        WHERE ReportSchemaQueryParameterId = #{reportSchemaQueryParameterId}
    </select>
</mapper>