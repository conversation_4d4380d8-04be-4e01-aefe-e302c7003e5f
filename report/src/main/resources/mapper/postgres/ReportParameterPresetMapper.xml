<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportParameterPresetMapper">
    <insert id="batchCreateReportParameterPreset">
        INSERT INTO reportparameterpreset(ReportId, ReportSchemaQueryParameterId, value, display) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.reportId}, #{item.reportSchemaQueryParameterId}, #{item.value}, #{item.display})
        </foreach>
    </insert>
    <update id="batchUpdateReportParameterPreset">
        <foreach collection="reportParameterPresetList" item="item" separator=";">
            UPDATE reportParameterPreset
            SET ReportId =#{item.reportId},
            ReportSchemaQueryParameterId = #{item.reportSchemaQueryParameterId},
            display = #{item.display},
            value = #{item.value}
            WHERE ReportParameterPresetId = #{item.reportParameterPresetId}
        </foreach>
    </update>
    <select id="findByReportId" resultType="com.siteweb.report.entity.ReportParameterPreset">
        SELECT reportparameterpresetid, reportid, reportschemaqueryparameterid, value, display
        FROM reportparameterpreset
        WHERE ReportId = #{reportId}
    </select>
</mapper>