<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportFolderMapMapper">

    <resultMap id="folderWithReportsResultMap" type="com.siteweb.report.dto.FolderWithReportsDTO">
        <id property="folderId" column="folderId"/>
        <collection property="reports" ofType="com.siteweb.report.dto.ReportFolderTreeDTO">
            <id property="id" column="reportId"/>
            <result property="name" column="reportName"/>
            <result property="type" column="type"/>
            <result property="sortIndex" column="sortIndex"/>
            <result property="reportSchemaId" column="reportSchemaId"/>
            <result property="reportSchemaCategoryId" column="reportSchemaCategoryId"/>
        </collection>
    </resultMap>

    <select id="selectReportMappings" resultMap="folderWithReportsResultMap">
        SELECT m.folderId AS folderId, r.ReportId AS reportId, r.ReportName AS reportName, 'report' AS type,
        r.reportSchemaId AS reportSchemaId, r.reportSchemaCategoryId AS reportSchemaCategoryId, m.sortIndex AS sortIndex
        FROM reportfoldermap m
        INNER JOIN report r ON m.reportId = r.ReportId
        WHERE m.reportType = 1
        AND m.folderId IN
        <foreach collection="folderIds" item="folderId" open="(" close=")" separator=",">
            #{folderId}
        </foreach>
        ORDER BY m.sortIndex
    </select>
    <select id="selectUnassignedReports" resultType="com.siteweb.report.dto.ReportFolderTreeDTO">
        SELECT r.ReportId AS id, r.ReportName AS name, 'report' AS type,
        r.reportSchemaId AS reportSchemaId, r.reportSchemaCategoryId AS reportSchemaCategoryId
        FROM report r
        WHERE NOT EXISTS (
        SELECT 1 FROM reportfoldermap m WHERE m.reportId = r.ReportId AND m.reportType = 1
        )
    </select>
    <select id="selectTimeReportMappings" resultMap="folderWithReportsResultMap">
        SELECT m.folderId AS folderId, r.reporttimingtaskmanagementid AS reportId, r.reporttimingtaskmanagementname AS reportName, 'report' AS type,
        -1 AS reportSchemaId, -1 AS reportSchemaCategoryId,m.sortIndex AS sortIndex
        FROM reporttimingtaskmanagement r
        INNER JOIN reportfoldermap m ON m.reportId = r.reporttimingtaskmanagementid AND m.reportType = 2
        WHERE (r.createUserId = #{userId} or r.createUserId is null or r.overt = 1)
    </select>
    <select id="selectUnassignedTimeReports" resultType="com.siteweb.report.dto.ReportFolderTreeDTO">
        SELECT r.reporttimingtaskmanagementid AS id, r.reporttimingtaskmanagementname AS name, 'report' AS type,
        -1 AS reportSchemaId, -1 AS reportSchemaCategoryId
        FROM reporttimingtaskmanagement r
        WHERE (createUserId = #{userId} or createUserId is null or overt = 1) AND NOT EXISTS (
        SELECT 1 FROM reportfoldermap m WHERE m.reportId = r.reporttimingtaskmanagementid AND m.reportType = 2
        )
    </select>
</mapper>