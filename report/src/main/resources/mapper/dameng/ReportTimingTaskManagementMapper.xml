<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.report.mapper.ReportTimingTaskManagementMapper">

    <resultMap type="com.siteweb.report.entity.ReportTimingTaskManagement" id="ReporttimingtaskmanagementMap">
        <result property="reportTimingTaskManagementId" column="ReportTimingTaskManagementId" jdbcType="INTEGER"/>
        <result property="reportTimingTaskManagementName" column="ReportTimingTaskManagementName" jdbcType="VARCHAR"/>
        <result property="reportId" column="ReportId" jdbcType="INTEGER"/>
        <result property="reportName" column="ReportName" jdbcType="VARCHAR"/>
        <result property="storageCycle" column="StorageCycle" jdbcType="VARCHAR"/>
        <result property="startTimeType" column="StartTimeType" jdbcType="VARCHAR"/>
        <result property="endTimeType" column="EndTimeType" jdbcType="VARCHAR"/>
        <result property="status" column="Status" jdbcType="INTEGER"/>
        <result property="to" column="to" jdbcType="VARCHAR"/>
        <result property="cc" column="cc" jdbcType="VARCHAR"/>
        <result property="overt" column="Overt" jdbcType="TINYINT"/>
    </resultMap>

    <insert id="create" useGeneratedKeys="true" keyProperty="reportTimingTaskManagementId" keyColumn="reportTimingTaskManagementId">
        INSERT INTO reporttimingtaskmanagement (ReportTimingTaskManagementName, ReportId, ReportName, StorageCycle, StartTimeType, EndTimeType, Status, "to", cc, CreateUserId, Overt)
        VALUES(#{entity.reportTimingTaskManagementName}, #{entity.reportId}, #{entity.reportName}, #{entity.storageCycle}, #{entity.startTimeType}, #{entity.endTimeType}, #{entity.status}, #{entity.to}, #{entity.cc}, #{entity.createUserId}, #{entity.overt})
    </insert>
    <update id="update">
        UPDATE reporttimingtaskmanagement SET ReportTimingTaskManagementName=#{entity.reportTimingTaskManagementName},
        ReportId=#{entity.reportId},
        ReportName=#{entity.reportName},
        StorageCycle=#{entity.storageCycle},
        StartTimeType=#{entity.startTimeType},
        EndTimeType=#{entity.endTimeType},
        Status=#{entity.status},
        "to"=#{entity.to},
        cc=#{entity.cc},
        CreateUserId=#{entity.createUserId},
        Overt=#{entity.overt}
        WHERE ReportTimingTaskManagementId=#{entity.reportTimingTaskManagementId}
    </update>

    <select id="selectList" resultMap="ReporttimingtaskmanagementMap">
        select t1.*, r.ReportSchemaId, r.ReportSchemaCategoryId from reporttimingtaskmanagement t1
                                                                         left join report r on t1.ReportId = r.ReportId
    </select>

    <select id="findReportTimingTaskManagementById" resultMap="ReporttimingtaskmanagementMap">
        select t1.*, r.ReportSchemaId, r.ReportSchemaCategoryId from reporttimingtaskmanagement t1
                                                                         left join report r on t1.ReportId = r.ReportId
        where ReportTimingTaskManagementId = #{id,jdbcType=INTEGER}
    </select>
    <select id="findByUserId" resultType="com.siteweb.report.entity.ReportTimingTaskManagement">
        SELECT reporttimingtaskmanagementid,
               reporttimingtaskmanagementname,
               reportid,
               reportname,
               storagecycle,
               starttimetype,
               endtimetype,
               status,
               "to",
               cc,
               overt
        FROM reporttimingtaskmanagement
        WHERE createUserId = #{userId} or createUserId is null or overt = 1
    </select>

</mapper>

