<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.CardSwipeMapper">
    <select id="getPageCardSwipe" resultType="com.siteweb.report.entity.CardSwipe">
        SELECT a.EquipmentName,
        a.CardCode,
        a.CardGroupName,
        a.StationName,
        a.CardStationName,
        a.CardName,
        a.cardUserName,
        a.CardStatusName,
        a.<PERSON>No,
        c.Meaning as enter,
        a.RecordTime,
        b.AreaName,
        a.CardCategoryName,
        a.DoorName,
        a.DoorCategoryName,
        a.ValidName
        FROM tbl_swapcardrecord a
        INNER JOIN (SELECT a.EquipmentId, IFNULL(c.AreaName, '未分区') AS AreaName, d.Category, b.areaid AreaId
        FROM TBL_Equipment a
        INNER JOIN TBL_Door d ON a.EquipmentId = d.EquipmentId
        LEFT JOIN tbl_doorareamap b ON a.EquipmentId = b.equipmentid
        LEFT JOIN tbl_doorarea c ON b.areaid = c.areaid
        WHERE a.EquipmentCategory = 82) b ON a.EquipmentId = b.EquipmentId
        LEFT JOIN tbl_doorpropertymeaning c
        ON b.Category = c.Category AND a.Enter = c.PropertyId AND c.propertytype = 4
        <where>
            <if test="cardSwipeParam.cardCode != null and cardSwipeParam.cardCode != ''">
                and a.CardCode like CONCAT('%',#{cardSwipeParam.cardCode},'%')
            </if>
            <if test="cardSwipeParam.cardName != null and cardSwipeParam.cardName != ''">
                and a.cardName like CONCAT('%',#{cardSwipeParam.cardName},'%')
            </if>
            <if test="cardSwipeParam.cardGroup != null">
                and a.cardGroup = #{cardSwipeParam.cardGroup}
            </if>
            <if test="cardSwipeParam.cardStatus != null">
                and a.cardStatus = #{cardSwipeParam.cardStatus}
            </if>
            <if test="cardSwipeParam.startDate != null">
                and a.RecordTime &gt;= #{cardSwipeParam.startDate}
            </if>
            <if test="cardSwipeParam.endDate != null">
                and a.RecordTime &lt;= #{cardSwipeParam.endDate}
            </if>
            <if test="cardSwipeParam.doorAreaId != null and cardSwipeParam.doorAreaId == -1">
                and b.AreaId IS NULL
            </if>
            <if test="cardSwipeParam.doorAreaId != null and cardSwipeParam.doorAreaId > 0">
                and b.AreaId = #{cardSwipeParam.doorAreaId}
            </if>
            <if test="cardSwipeParam.equipmentIds != null and cardSwipeParam.equipmentIds.size > 0">
                and a.EquipmentId in
                <foreach collection="cardSwipeParam.equipmentIds" item="equipmentId" open="(" close=")" separator=",">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="cardSwipeParam.operatorIds != null and cardSwipeParam.operatorIds.size > 0">
                and a.CardUserId in
                <foreach collection="cardSwipeParam.operatorIds" item="operatorId" open="(" close=")" separator=",">
                    #{operatorId}
                </foreach>
            </if>
            <if test="cardSwipeParam.inOutSigns != null and cardSwipeParam.inOutSigns != ''">
                and c.Meaning like CONCAT('%',#{cardSwipeParam.inOutSigns},'%')
            </if>
            <if test="cardSwipeParam.validName != null and cardSwipeParam.validName != ''">
                and a.ValidName like CONCAT('%',#{cardSwipeParam.validName},'%')
            </if>
        </where>
        ORDER BY a.RecordTime DESC
    </select>
</mapper>