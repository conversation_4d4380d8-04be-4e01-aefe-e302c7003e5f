<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.TotalEventMapper">
    <select id="findResourceStructureIdBirthDuration" resultType="java.lang.Integer">
        SELECT activeEvent.ResourceStructureId
        FROM tbl_activeevent activeEvent
        WHERE activeEvent.StartTime BETWEEN #{startDate} AND #{endDate} AND activeEvent.ResourceStructureId is not null
        <if test="resourceStructureIds != null and resourceStructureIds.size > 0">
            AND activeEvent.ResourceStructureId IN
            <foreach collection="resourceStructureIds" item="resourceStructureId" open="(" close=")" separator=",">
                #{resourceStructureId}
            </foreach>
        </if>
        UNION
        SELECT historyEvent.ResourceStructureId
        FROM tbl_historyevent historyEvent
        WHERE historyEvent.StartTime BETWEEN #{startDate} AND #{endDate} AND historyEvent.ResourceStructureId is not
        null
        <if test="resourceStructureIds != null and resourceStructureIds.size > 0">
            AND historyEvent.ResourceStructureId IN
            <foreach collection="resourceStructureIds" item="resourceStructureId" open="(" close=")" separator=",">
                #{resourceStructureId}
            </foreach>
        </if>
    </select>
    <select id="findEventCountInResourceId" resultType="com.siteweb.report.vo.EventCount">
        SELECT a.ResourceStructureId AS resourceStructureId,
        a.ResourceStructureName AS resourceStructureName,
        b.eventLevel AS eventLevel,
        COUNT(b.SequenceId) AS "count"
        FROM tbl_activeevent b
        INNER JOIN resourcestructure a ON a.ResourceStructureId = b.ResourceStructureId
        WHERE b.StartTime BETWEEN #{startTime} AND #{endTime} AND a.ResourceStructureId IN
        <foreach collection="eventResourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND b.equipmentId IN
        <foreach collection="equipmentIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY b.EventLevel, a.ResourceStructureId, a.ResourceStructureName
    </select>
    <select id="findHistoryEventCountInResourceId" resultType="com.siteweb.report.vo.EventCount">
        SELECT a.ResourceStructureId   AS resourceStructureId,
        a.ResourceStructureName AS resourceStructureName,
        b.eventLevel            AS eventLevel,
        COUNT(b.SequenceId)     AS "count"
        FROM tbl_historyevent b
        INNER JOIN resourcestructure a ON a.ResourceStructureId = b.ResourceStructureId
        WHERE b.StartTime BETWEEN #{startTime} AND #{endTime} AND a.ResourceStructureId IN
        <foreach collection="eventResourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND b.equipmentId IN
        <foreach collection="equipmentIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY b.EventLevel, a.ResourceStructureId, a.ResourceStructureName
    </select>
    <sql id="findAllEventSql">
        SELECT *
        FROM (SELECT activeEvnet.eventSeverity,
        activeEvnet.EquipmentName,
        activeEvnet.ResourceStructureId,
        activeEvnet.stationname,
        equipmentBaseType.BaseEquipmentName AS equipmentBaseName,
        activeEvnet.EquipmentCategoryName,
        activeEvnet.eventname,
        activeEvnet.EventValue,
        activeEvnet.meanings,
        activeEvnet.reversalNum,
        activeEvnet.StartTime,
        activeEvnet.ConfirmTime,
        activeEvnet.ConfirmerName,
        activeEvnet.EndTime,
        activeEvnet.Description,
        activeEvnet.eventReasonType,
        bdreason.name as eventReasonTypeName
        FROM tbl_activeevent activeEvnet
        LEFT JOIN tbl_equipmentbasetype equipmentBaseType on activeEvnet.BaseEquipmentId = equipmentBaseType.BaseEquipmentId
        LEFT JOIN bytedance_eventreasontype bdreason on activeEvnet.eventReasonType = bdreason.id
        WHERE activeEvnet.StartTime &gt;= #{param.startDate} and activeEvnet.StartTime &lt;= #{param.endDate}
        <if test="param.equipmentIds != null and param.equipmentIds.size > 0">
            and activeEvnet.equipmentId in
            <foreach collection="param.equipmentIds" open="(" close=")" separator="," item="equipmentId">
                #{equipmentId}
            </foreach>
        </if>
        <if test="param.baseEquipmentId != null">
            and activeEvnet.baseEquipmentId = #{param.baseEquipmentId}
        </if>
        <if test="param.equipmentCategories != null and param.equipmentCategories.size > 0">
            and activeEvnet.EquipmentCategory in
            <foreach collection="param.equipmentCategories" open="(" close=")" separator="," item="equipmentCategory">
                #{equipmentCategory}
            </foreach>
        </if>
        <if test="param.eventLevels != null and param.eventLevels.size > 0">
            and activeEvnet.EventLevel in
            <foreach collection="param.eventLevels" item="eventLevel" open="(" close=")" separator=",">
                #{eventLevel}
            </foreach>
        </if>
        <if test="param.eventName != null and param.eventName != ''">
            and activeEvnet.eventName like concat('%',#{param.eventName},'%')
        </if>
        <if test="param.confirmIds != null and param.confirmIds.size > 0">
            and activeEvnet.ConfirmerId in
            <foreach collection="param.confirmIds" item="confirmId" open="(" close=")" separator=",">
                #{confirmId}
            </foreach>
        </if>
        <if test="param.equipmentEventIdDTOS != null and param.equipmentEventIdDTOS.size > 0">
            and
            <foreach collection="param.equipmentEventIdDTOS" item="equipmentEventId" open="(" close=")"
                     separator=" or ">
                (activeEvnet.equipmentId = #{equipmentEventId.equipmentId} and activeEvnet.eventId =
                #{equipmentEventId.eventId})
            </foreach>
        </if>
        <if test="param.eventReasonTypes != null and param.eventReasonTypes.size > 0">
            and activeEvnet.eventReasonType in
            <foreach collection="param.eventReasonTypes" item="eventReasonType" open="(" close=")" separator=",">
                #{eventReasonType}
            </foreach>
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            and (activeEvnet.equipmentName like concat('%',#{param.keyword},'%') or
            activeEvnet.eventName like concat('%',#{param.keyword},'%') or
            activeEvnet.EquipmentCategoryName like concat('%',#{param.keyword},'%') or
            activeEvnet.StationName like concat('%',#{param.keyword},'%') or
            activeEvnet.eventSeverity like concat('%',#{param.keyword},'%') or
            activeEvnet.Meanings like concat('%',#{param.keyword},'%'))
        </if>
        UNION ALL
        SELECT historyEvent.eventSeverity,
        historyEvent.EquipmentName,
        historyEvent.ResourceStructureId,
        historyEvent.stationname,
        equipmentBaseType.BaseEquipmentName AS equipmentBaseName,
        historyEvent.EquipmentCategoryName,
        historyEvent.eventname,
        historyEvent.EventValue,
        historyEvent.meanings,
        historyEvent.reversalNum,
        historyEvent.StartTime,
        historyEvent.ConfirmTime,
        historyEvent.ConfirmerName,
        historyEvent.EndTime,
        historyEvent.Description,
        historyEvent.eventReasonType,
        bdreason.name as eventReasonTypeName
        FROM tbl_historyevent historyEvent
        LEFT JOIN tbl_equipmentbasetype equipmentBaseType on historyEvent.BaseEquipmentId = equipmentBaseType.BaseEquipmentId
        LEFT JOIN bytedance_eventreasontype bdreason on historyEvent.eventReasonType = bdreason.id
        WHERE historyEvent.StartTime &gt;= #{param.startDate} and historyEvent.StartTime &lt;= #{param.endDate}
        <if test="param.equipmentIds != null and param.equipmentIds.size > 0">
            and historyEvent.equipmentId in
            <foreach collection="param.equipmentIds" open="(" close=")" separator="," item="equipmentId">
                #{equipmentId}
            </foreach>
        </if>
        <if test="param.baseEquipmentId != null">
            and historyEvent.baseEquipmentId = #{param.baseEquipmentId}
        </if>
        <if test="param.equipmentCategories != null and param.equipmentCategories.size > 0">
            and historyEvent.EquipmentCategory in
            <foreach collection="param.equipmentCategories" open="(" close=")" separator="," item="equipmentCategory">
                #{equipmentCategory}
            </foreach>
        </if>
        <if test="param.eventLevels != null and param.eventLevels.size > 0">
            and historyEvent.EventLevel in
            <foreach collection="param.eventLevels" item="eventLevel" open="(" close=")" separator=",">
                #{eventLevel}
            </foreach>
        </if>
        <if test="param.eventName != null and param.eventName != ''">
            and historyEvent.eventName like concat('%',#{param.eventName},'%')
        </if>
        <if test="param.confirmIds != null and param.confirmIds.size > 0">
            and historyEvent.ConfirmerId in
            <foreach collection="param.confirmIds" item="confirmId" open="(" close=")" separator=",">
                #{confirmId}
            </foreach>
        </if>
        <if test="param.equipmentEventIdDTOS != null and param.equipmentEventIdDTOS.size > 0">
            and
            <foreach collection="param.equipmentEventIdDTOS" item="equipmentEventId" open="(" close=")"
                     separator=" or ">
                (historyEvent.equipmentId = #{equipmentEventId.equipmentId} and historyEvent.eventId =
                #{equipmentEventId.eventId})
            </foreach>
        </if>
        <if test="param.eventReasonTypes != null and param.eventReasonTypes.size > 0">
            and historyEvent.eventReasonType in
            <foreach collection="param.eventReasonTypes" item="eventReasonType" open="(" close=")" separator=",">
                #{eventReasonType}
            </foreach>
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            and (historyEvent.equipmentName like concat('%',#{param.keyword},'%') or
            historyEvent.eventName like concat('%',#{param.keyword},'%') or
            historyEvent.EquipmentCategoryName like concat('%',#{param.keyword},'%') or
            historyEvent.StationName like concat('%',#{param.keyword},'%') or
            historyEvent.eventSeverity like concat('%',#{param.keyword},'%') or
            historyEvent.Meanings like concat('%',#{param.keyword},'%'))
        </if>
        ) AS allEvent
    </sql>
    <select id="findAllEvent" resultType="com.siteweb.report.dto.AllEventDTO">
        <include refid="findAllEventSql"/> order by StartTime desc
    </select>
    <select id="findAllEventCount" resultType="java.lang.Long">
        select count(*) count FROM (<include refid="findAllEventSql"/>) as result
    </select>
    <select id="findHistoryEventBetweenStartTimeAndEquipmentIds" resultType="com.siteweb.report.dto.HistoryEventStatisticsDTO">
        SELECT
        eventLevel,
        StartTime
        FROM tbl_historyevent
        WHERE startTime &gt; #{startDate}
        AND startTime &lt; #{endDate}
        <if test="equipmentIds != null and equipmentIds.size > 0">
            AND EquipmentId IN
            <foreach collection="equipmentIds" item="equipmentId" separator="," open="(" close=")">
                #{equipmentId}
            </foreach>
        </if>
    </select>
    <select id="findHistoryEventBetweenStartTimeAndCategories" resultType="com.siteweb.hmi.dto.StatisticsResult">
        SELECT
        c.ItemId AS id,
        c.ItemValue AS name,
        count(*) value
        FROM
        tbl_historyevent a
        INNER JOIN tbl_dataitem c ON c.EntryId = 7 AND c.ItemId = a.EquipmentCategory
        WHERE
        a.startTime &gt; #{startDate}
        AND a.startTime &lt; #{endDate}
        <if test="equipmentCategories != null and equipmentCategories.size > 0">
            AND a.EquipmentCategory IN
            <foreach collection="equipmentCategories" item="equipmentCategory" separator="," open="(" close=")">
                #{equipmentCategory}
            </foreach>
        </if>
        <if test="eventLevelList != null and eventLevelList.size > 0">
            AND a.EventLevel IN
            <foreach collection="eventLevelList" item="level" separator="," open="(" close=")">
                #{level}
            </foreach>
        </if>
        group by c.ItemId,c.ItemValue
    </select>
    <select id="findEventClassificationStatistical" resultType="com.siteweb.report.dto.EventClassificationStatisticalDTO">
        SELECT
        event.BaseEquipmentName,
        count(DISTINCT event.EquipmentId) equipmentNum,
        count(*) allEventNum,
        count(CASE WHEN event.maintainState != 3 THEN 1 ELSE null END) abnormalEventNum,
        count(CASE WHEN event.maintainState != 3 AND event.state = 1 THEN 1 ELSE null END) abnormalHistoryEventNum,
        count(CASE WHEN event.maintainState != 3 AND event.state = 2 THEN 1 ELSE null END) abnormalActiveEventNum,
        count(CASE WHEN event.maintainState = 3 THEN 1 ELSE null END) constructEventNum,
        count(CASE WHEN event.maintainState = 3 AND event.state = 1 THEN 1 ELSE null END) constructHistoryEventNum,
        count(CASE WHEN event.maintainState = 3 AND event.state = 2 THEN 1 ELSE null END) constructActiveEventNum
        FROM
        (
        SELECT
        a.maintainState,
        a.EquipmentId,
        d.BaseEquipmentName,
        1 AS state
        FROM
        tbl_historyevent a
        LEFT JOIN tbl_equipmentbasetype d ON a.BaseEquipmentId = d.BaseEquipmentId
        <where>
            <if test="param.startDate != null">
                and a.StartTime &gt;= #{param.startDate}
            </if>
            <if test="param.endDate != null">
                and a.StartTime &lt;= #{param.endDate}
            </if>
            <if test="param.baseEquipmentIds != null and param.baseEquipmentIds.size > 0">
                AND a.baseEquipmentId IN
                <foreach collection="param.baseEquipmentIds" item="baseEquipmentId" separator="," open="(" close=")">
                    #{baseEquipmentId}
                </foreach>
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size > 0">
                AND a.EquipmentId IN
                <foreach collection="param.equipmentIds" item="equipmentId" separator="," open="(" close=")">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.permissionEquipmentIds != null and param.permissionEquipmentIds.size > 0">
                AND a.EquipmentId IN
                <foreach collection="param.permissionEquipmentIds" item="permissionEquipmentId" separator="," open="(" close=")">
                    #{permissionEquipmentId}
                </foreach>
            </if>
        </where>
        UNION ALL
        SELECT
        a.maintainState,
        a.EquipmentId,
        d.BaseEquipmentName,
        2 AS state
        FROM
        tbl_activeevent a
        LEFT JOIN tbl_equipmentbasetype d ON a.BaseEquipmentId = d.BaseEquipmentId
        <where>
            <if test="param.startDate != null">
                and a.StartTime &gt;= #{param.startDate}
            </if>
            <if test="param.endDate != null">
                and a.StartTime &lt;= #{param.endDate}
            </if>
            <if test="param.baseEquipmentIds != null and param.baseEquipmentIds.size > 0">
                AND a.baseEquipmentId IN
                <foreach collection="param.baseEquipmentIds" item="baseEquipmentId" separator="," open="(" close=")">
                    #{baseEquipmentId}
                </foreach>
            </if>
            <if test="param.equipmentIds != null and param.equipmentIds.size > 0">
                AND a.EquipmentId IN
                <foreach collection="param.equipmentIds" item="equipmentId" separator="," open="(" close=")">
                    #{equipmentId}
                </foreach>
            </if>
            <if test="param.permissionEquipmentIds != null and param.permissionEquipmentIds.size > 0">
                AND a.EquipmentId IN
                <foreach collection="param.permissionEquipmentIds" item="permissionEquipmentId" separator="," open="(" close=")">
                    #{permissionEquipmentId}
                </foreach>
            </if>
        </where>
        ) event
        GROUP BY event.BaseEquipmentName
        ORDER BY event.BaseEquipmentName
    </select>
</mapper>