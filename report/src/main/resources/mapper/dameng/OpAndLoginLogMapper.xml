<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.OpAndLoginLogMapper">

    <select id="findOperationRecordByDataAndLogIds" resultType="com.siteweb.report.entity.OpAndLoginLog">
        SELECT ROWNUM AS oplId, g.*
        FROM (
        SELECT f.EmployeeId, e.UserName AS UserId, f.EmployeeName AS Name, f.EmployeeTitle AS Title, f.JobNumber,
        d.StationName || ' ' || d.OperationContent AS Content, d.OperationTime
        FROM tbl_operationrecord d
        INNER JOIN tbl_account e ON d.UserId = e.UserId
        INNER JOIN tbl_employee f ON f.EmployeeId = e.UserId
        UNION ALL
        SELECT i.EmployeeId, h.UserName AS UserId, i.EmployeeName AS Name, i.EmployeeTitle AS Title, i.JobNumber,
        g.ClientType || ' ' || g.OperatingType || ' ' || g.ClientIp AS Content, g.OperatingTime AS OperationTime
        FROM loginlog g
        INNER JOIN tbl_account h ON g.UserId = h.UserId
        INNER JOIN tbl_employee i ON i.EmployeeId = h.UserId
        ) g
        where 1=1
            and g.OperationTime >= #{startTime}
            and g.OperationTime &lt;= #{endTime}
            <if test="userIds != null and userIds.size > 0">
                <foreach collection="userIds" item="item" separator="," open="and g.EmployeeId in (" close=")">
                    #{item}
                </foreach>
            </if>
        order by g.OperationTime desc;
    </select>

    <select id="findOperationDetailByDataAndLogIds" resultType="com.siteweb.report.entity.OpAndLoginLog">
        SELECT c.EmployeeId, b.UserName AS UserId, c.EmployeeName AS Name, c.EmployeeTitle AS Title, c.JobNumber,
        a.ObjectId, a.ObjectType, a.PropertyName, a.OldValue, a.NewValue, a.OperationType, a.OperationType || ' ' || a.PropertyName || ' ' || a.NewValue AS Content, a.OperationTime
        FROM tbl_operationdetail a
        INNER JOIN tbl_account b ON a.UserId = b.UserId
        INNER JOIN tbl_employee c ON c.EmployeeId = b.UserId
        WHERE a.OperationTime >= #{startTime}
            and a.OperationTime &lt;= #{endTime}
            <if test="userIds != null and userIds.size > 0">
                <foreach collection="userIds" item="item" separator="," open="and c.EmployeeId in (" close=")">
                    #{item}
                </foreach>
            </if>
    </select>

</mapper>