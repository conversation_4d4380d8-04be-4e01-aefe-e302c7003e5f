<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportSchemaExportParameterMapper">
    <select id="findByReportSchemaId" resultType="com.siteweb.report.entity.ReportSchemaExportParameter">
        SELECT ReportSchemaExportParameterId,
               ReportSchemaExportParameterName,
               ReportSchemaExportParameterTitle,
               ReportSchemaId,
               IsNull
        FROM reportschemaexportparameter
        WHERE reportSchemaId = #{reportSchemaId}
    </select>
    <select id="findById" resultType="com.siteweb.report.entity.ReportSchemaExportParameter">
        SELECT ReportSchemaExportParameterId,
               ReportSchemaExportParameterName,
               ReportSchemaExportParameterTitle,
               ReportSchemaId,
               IsNull
        FROM reportschemaexportparameter
        WHERE ReportSchemaExportParameterId = #{reportSchemaExportParameterId}
    </select>
</mapper>