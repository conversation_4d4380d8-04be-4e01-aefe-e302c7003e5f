<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportExportParameterPresetMapper">
    <insert id="batchCreateReportExportParameterPreset">
        INSERT INTO reportexportparameterpreset(ReportId, ReportSchemaExportParameterId, display) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.reportId},#{item.reportSchemaExportParameterId},#{item.display})
        </foreach>
    </insert>
    <update id="batchUpdateReportExportParameterPreset">
        <foreach collection="reportExportParameterPresetList" item="item" separator=";">
            UPDATE reportexportparameterpreset
            SET ReportId =#{item.reportId},
            ReportSchemaExportParameterId = #{item.reportSchemaExportParameterId},
            display = #{item.display}
            WHERE ReportExportParameterPresetId = #{item.reportExportParameterPresetId}
        </foreach>
    </update>
    <select id="findByReportId" resultType="com.siteweb.report.entity.ReportExportParameterPreset">
        SELECT reportexportparameterpresetid, reportid, reportschemaexportparameterid, display
        FROM reportexportparameterpreset
        WHERE ReportId = #{reportId}
    </select>
</mapper>