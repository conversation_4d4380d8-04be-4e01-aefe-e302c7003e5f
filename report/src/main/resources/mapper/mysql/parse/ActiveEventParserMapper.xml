<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.parser.mapper.ActiveEventParserMapper">
    <sql id="findActiveEventSql">
        SELECT
        a.EventSeverity,
        a.EquipmentName,
        a.ResourceStructureId,
        b.BaseEquipmentName,
        a.EquipmentCategoryName,
        a.EventName,
        a.EventValue,
        a.Meanings,
        a.StartTime,
        a.ConfirmTime,
        a.ConfirmerName,
        a.ReversalNum,
        a.EndTime,
        a.Description,
        a.eventReasonType
        FROM TBL_ActiveEvent a
        LEFT JOIN TBL_EquipmentBaseType b ON a.BaseEquipmentId = b.BaseEquipmentId
        <where>
            <if test="wrapper.startDate != null">
                and a.startTime >= #{wrapper.startDate}
            </if>
            <if test="wrapper.endDate != null">
                and a.startTime &lt;= #{wrapper.endDate}
            </if>
            <if test="wrapper.baseEquipmentIdList != null and wrapper.baseEquipmentIdList.size > 0">
                and a.BaseEquipmentId in
                <foreach collection="wrapper.baseEquipmentIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.equipmentCategories != null and wrapper.equipmentCategories.size > 0">
                and a.equipmentCategory in
                <foreach collection="wrapper.equipmentCategories" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.eventLevelList != null and wrapper.eventLevelList.size > 0">
                and a.EventLevel in
                <foreach collection="wrapper.eventLevelList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.equipmentIdList != null and wrapper.equipmentIdList.size > 0">
                and a.EquipmentId in
                <foreach collection="wrapper.equipmentIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.eventName != null">
                and a.EventName like concat('%%',#{wrapper.eventName},'%%')
            </if>
            <if test="wrapper.keyword != null">
                and (
                a.EventName like concat('%%',#{wrapper.keyword},'%%') or
                a.StationName like concat('%%',#{wrapper.keyword},'%%') or
                a.EquipmentName like concat('%%',#{wrapper.keyword},'%%') or
                a.BaseTypeName like concat('%%',#{wrapper.keyword},'%%') or
                a.EquipmentCategoryName like concat('%%',#{wrapper.keyword},'%%') or
                a.EventSeverity like concat('%%',#{wrapper.keyword},'%%') or
                a.Meanings like concat('%%',#{wrapper.keyword},'%%') or
                a.Description like concat('%%',#{wrapper.keyword},'%%')
                )
            </if>
            <if test="wrapper.description != null">
                and a.description like concat('%%',#{wrapper.description},'%%')
            </if>
            <if test="wrapper.operatorIds != null and wrapper.operatorIds.size > 0">
                and a.ConfirmerId in
                <foreach collection="wrapper.operatorIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.eventReasonTypes != null and wrapper.eventReasonTypes.size > 0">
                and a.eventReasonType in
                <foreach collection="wrapper.eventReasonTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.sql != null and wrapper.sql.size > 0">
                <foreach item="item" collection="wrapper.sql">
                    ${item}
                </foreach>
            </if>
        </where>
        order by a.startTime desc
    </sql>

    <select id="findActiveEventByPage" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        <include refid="findActiveEventSql"/>
    </select>

    <select id="findActiveEventCount" resultType="java.lang.Long">
        SELECT COUNT(*) from (<include refid="findActiveEventSql"/>) as a
    </select>
</mapper>