<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.parser.mapper.EquipmentSerialPortInfoMapper">
    <select id="findMonitorUnitPortInfo" resultType="com.siteweb.report.entity.MonitorUnitPortInfo">
        SELECT
            ts.StationName,
            tm.MonitorUnitName,
            tp.MonitorUnitId,
            tp.PortName,
            tp.PortType,
            pt.PortName AS PortTypeName,
            tp.PortNo,
            tp.Setting,
            ts2.DllPath,
            ts2.UpdateTime,
            COUNT(CASE WHEN ts2.DllPath IS NOT NULL THEN 1 END) AS SamplerUnitCount
        FROM
            tsl_port tp
                LEFT JOIN tsl_monitorunit tm ON tp.MonitorUnitId = tm.MonitorUnitId
                LEFT JOIN tbl_station ts ON ts.stationId = tm.stationId
                LEFT JOIN (
                    SELECT
                        ItemValue AS PortName,
                        ItemId AS PortType
                    FROM
                        tbl_dataitem td
                    WHERE
                        td.EntryId = 39
                ) pt ON pt.PortType = tp.PortType
                LEFT JOIN tsl_samplerunit ts2 ON ts2.PortId = tp.PortId
        WHERE
            1=1
            <if test="stationIds != null and stationIds.size() > 0">
                AND ts.stationId IN
                <foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="monitorUnitName != null and monitorUnitName != ''">
                AND tm.MonitorUnitName LIKE CONCAT('%', #{monitorUnitName}, '%')
            </if>
            <if test="portName != null and portName != ''">
                AND tp.PortName LIKE CONCAT('%', #{portName}, '%')
            </if>
        GROUP BY
            ts.StationName,
            tm.MonitorUnitName,
            tp.MonitorUnitId,
            tp.PortName,
            tp.PortType,
            pt.PortName,
            tp.PortNo,
            tp.Setting,
            ts2.DllPath,
            ts2.UpdateTime
        ORDER BY
            tp.PortNo
    </select>
    
    <select id="findEquipmentPortInfo" resultType="com.siteweb.report.entity.EquipmentPortInfo">
        SELECT
            a.EquipmentId AS equipmentId,
            CONCAT(h.ResourceStructureName, '/', g.ResourceStructureName, '/', b.ResourceStructureName) AS ResourceStructureName,
            a.EquipmentName AS equipmentName,
            i.ItemValue AS equipmentCategory,
            f.MonitorUnitName AS monitorUnitName,
            f.IpAddress AS ipAddress,
            e.PortName AS portName,
            e.Setting AS setting,
            c.SamplerUnitName AS samplerUnitName,
            c.Address AS address,
            a.EquipmentState AS equipmentState,
            d.EquipmentTemplateName AS equipmentTemplateName,
            b.ResourceStructureId AS resourceStructureId,
            s.StationId AS stationId,
            s.StationName AS stationName,
            f.MonitorUnitId AS monitorUnitId
        FROM 
            tbl_equipment a
        INNER JOIN 
            resourcestructure b ON a.ResourceStructureId = b.ResourceStructureId
        INNER JOIN 
            tsl_samplerunit c ON a.SamplerUnitId = c.SamplerUnitId AND a.MonitorUnitId = c.MonitorUnitId
        INNER JOIN 
            tbl_equipmenttemplate d ON a.EquipmentTemplateId = d.EquipmentTemplateId
        INNER JOIN 
            tsl_port e ON c.PortId = e.PortId
        INNER JOIN 
            tsl_monitorunit f ON a.MonitorUnitId = f.MonitorUnitId
        INNER JOIN 
            tbl_dataitem i ON a.EquipmentCategory = i.ItemId AND i.EntryId = 7
        inner join resourcestructure g on g.ResourceStructureId = b.ParentResourceStructureId
        inner join resourcestructure h on h.ResourceStructureId = g.ParentResourceStructureId
        INNER JOIN tbl_station s ON a.StationID = s.StationID
        WHERE 
            1 = 1
        <if test="query.resourceStructureIds != null and query.resourceStructureIds.size() > 0">
            AND a.ResourceStructureId IN
            <foreach item="item" index="index" collection="query.resourceStructureIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.equipmentName != null and query.equipmentName != ''">
            AND a.EquipmentName LIKE CONCAT('%', #{query.equipmentName}, '%')
        </if>
        <if test="query.equipmentCategory != null and query.equipmentCategory.size() > 0">
            AND a.EquipmentCategory IN
            <foreach item="item" index="index" collection="query.equipmentCategory" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.monitorUnitName != null and query.monitorUnitName != ''">
            AND f.MonitorUnitName LIKE CONCAT('%', #{query.monitorUnitName}, '%')
        </if>
        <if test="query.ipAddress != null and query.ipAddress != ''">
            AND f.IpAddress LIKE CONCAT('%', #{query.ipAddress}, '%')
        </if>
        <if test="query.portName != null and query.portName != ''">
            AND e.PortName LIKE CONCAT('%', #{query.portName}, '%')
        </if>
        <if test="query.samplerUnitName != null and query.samplerUnitName != ''">
            AND c.SamplerUnitName LIKE CONCAT('%', #{query.samplerUnitName}, '%')
        </if>
        <if test="query.equipmentState != null">
            AND a.equipmentState = #{query.equipmentState}
        </if>
        <if test="query.equipmentTemplateName != null and query.equipmentTemplateName != ''">
            AND d.EquipmentTemplateName LIKE CONCAT('%', #{query.equipmentTemplateName}, '%')
        </if>
        ORDER BY 
            b.ResourceStructureName ASC,
            f.MonitorUnitName ASC,
            e.PortNo ASC
    </select>
</mapper>