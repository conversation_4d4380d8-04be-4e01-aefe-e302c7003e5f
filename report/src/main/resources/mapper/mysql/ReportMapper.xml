<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.report.mapper.ReportMapper">
    <resultMap id="selectReportMap" type="com.siteweb.report.entity.Report">
        <id property="reportId" column="ReportId"/>
        <result property="reportSchemaId" column="ReportSchemaId"/>
        <result property="reportName" column="ReportName" />
        <result property="reportDescription" column="ReportDescription"/>
        <result property="reportSchemaCategoryId" column="ReportSchemaCategoryId"/>
        <result property="updateUserId" column="UpdateUserId"/>
        <result property="updateTime" column="UpdateTime"/>
        <result property="reportDataSourceId" column="ReportDataSourceId"/>
        <result property="maxQueryInterval" column="MaxQueryInterval"/>
        <result property="createUserId" column="CreateUserId"/>
        <result property="overt" column="Overt"/>
        <collection property="reportExportParameterPresetList" column="reportId=reportId" select="com.siteweb.report.mapper.ReportExportParameterPresetMapper.findByReportId"/>
        <collection property="reportParameterPresetList" column="reportId=reportId" select="com.siteweb.report.mapper.ReportParameterPresetMapper.findByReportId"/>
    </resultMap>
    <select id="findReportByUserIdAndSchemaId" resultMap="selectReportMap">
        SELECT report.ReportId,
               report.ReportName,
               report.ReportDescription,
               report.ReportSchemaId,
               report.ReportSchemaCategoryId,
               report.UpdateUserId,
               report.UpdateTime,
               report.ReportDataSourceId,
               report.MaxQueryInterval,
               report.CreateUserId,
               report.Overt
        FROM Report report
        WHERE report.reportSchemaId = #{reportSchemaId}
          AND (report.CreateUserId = #{userId} OR report.Overt = 1)
    </select>
</mapper>