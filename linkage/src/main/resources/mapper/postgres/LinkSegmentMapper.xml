<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.linkage.mapper.LinkSegmentMapper">
    <select id="findLinkSegmentsByLinkConfigId" resultType="com.siteweb.linkage.entity.LinkSegment">
        SELECT SegmentId, InputLinkElementConfigId, InputNodeId, OutputLinkElementConfigId, OutputNodeId
        FROM LinkSegment a INNER JOIN LinkElementConfig b on a.InputLinkElementConfigId = b.LinkElementConfigId
        WHERE b.LinkConfigId = #{linkConfigId}
    </select>
</mapper>