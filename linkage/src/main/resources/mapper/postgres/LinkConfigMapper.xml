<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.linkage.mapper.LinkConfigMapper">
    <delete id="deleteLinkConfigById">
        DELETE FROM linksegment where InputLinkElementConfigId in (SELECT LinkElementConfigId FROM LinkElementConfig WHERE LinkConfigId = #{linkConfigId});
        DELETE FROM linknode where LinkElementConfigId in (SELECT LinkElementConfigId FROM LinkElementConfig WHERE LinkConfigId = #{linkConfigId});
        DELETE FROM linkelementconfig where LinkConfigId = #{linkConfigId};
        DELETE FROM linkconfig where LinkConfigId = #{linkConfigId};
    </delete>
</mapper>