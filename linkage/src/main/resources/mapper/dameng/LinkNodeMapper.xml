<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.linkage.mapper.LinkNodeMapper">
    <select id="findLinkNodesByLinkConfigId" resultType="com.siteweb.linkage.entity.LinkNode">
        SELECT a.NodeId, a.LinkElementConfigId, a.NodeDirection, a.NodeType, a.NodeIndex, a.NodeTag, a.Expression
        FROM LinkNode a INNER JOIN LinkElementConfig b on a.LinkElementConfigId = b.LinkElementConfigId
        WHERE b.LinkConfigId = #{linkConfigId}
    </select>
</mapper>