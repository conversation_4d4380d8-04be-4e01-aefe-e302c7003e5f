<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.linkage.mapper.LinkElementConfigMapper">
    <delete id="deleteLinkElementConfigById">
        DELETE FROM linksegment where InputLinkElementConfigId = #{linkElementConfigId};
        DELETE FROM linknode where LinkElementConfigId = #{linkElementConfigId};
        DELETE FROM linkelementconfig where LinkElementConfigId = #{linkElementConfigId};
    </delete>
</mapper>