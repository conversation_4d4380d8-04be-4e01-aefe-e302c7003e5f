package com.siteweb.linkage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.linkage.job.LinkConfigCronJobManager;
import com.siteweb.linkage.mapper.LinkConfigMapper;
import com.siteweb.linkage.service.LinkConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class LinkConfigServiceImpl implements LinkConfigService {

    private final Logger log = LoggerFactory.getLogger(LinkConfigServiceImpl.class);

    @Autowired
    LinkConfigMapper linkConfigMapper;

    @Autowired
    LinkConfigCronJobManager linkConfigCronJobManager;

    @Override
    public int createLinkConfig(LinkConfig linkConfig) {
        linkConfig.setUpdateTime(new Date());
        int result = linkConfigMapper.insert(linkConfig);
        if (result > 0 && linkConfig.getLinkTriggerType().equals(1) && Boolean.TRUE.equals(linkConfig.getUsedStatus()) && checkTimeSpanValid(linkConfig)) {
            try {
                linkConfigCronJobManager.addCronJob(linkConfig);
            } catch (Exception e) {
                log.error("LinkConfigCronJobManager.addCronJob error: {}", e.getMessage(), e);
            }
        }
        return result;
    }

    @Override
    public LinkConfig findByLinkConfigId(Integer linkConfigId) {
        return linkConfigMapper.selectById(linkConfigId);
    }

    @Override
    public List<LinkConfig> findAllByLinkGroupId(Integer linkGroupId) {
        return linkConfigMapper.selectList(new QueryWrapper<LinkConfig>().eq("LinkGroupId", linkGroupId));
    }

    @Override
    public List<LinkConfig> findAll() {
        return linkConfigMapper.selectList(null);
    }

    @Override
    public List<LinkConfig> findAllByLinkTriggerType(Integer linkTriggerType) {
        return linkConfigMapper.selectList(new QueryWrapper<LinkConfig>().eq("LinkTriggerType", linkTriggerType));
    }

    @Override
    public int updateLinkConfig(LinkConfig linkConfig) {
        linkConfig.setUpdateTime(new Date());
        int result = linkConfigMapper.updateById(linkConfig);
        if (result > 0) {
            try {
                linkConfigCronJobManager.updateCronJob(linkConfig);
            } catch (Exception e) {
                log.error("LinkConfigCronJobManager.updateCronJob error: {}", e.getMessage(), e);
            }
        }
        return result;
    }

    @Override
    @Transactional
    public int deleteLinkConfigById(Integer linkConfigId) {
        LinkConfig linkConfig = linkConfigMapper.selectById(linkConfigId);
        if (linkConfig == null) {
            return -1;
        }
        //已启用的联动控制不允许删除
        if (Boolean.TRUE.equals(linkConfig.getUsedStatus())) {
            return -2;
        }
        linkConfigMapper.deleteLinkConfigById(linkConfigId);
        try {
            linkConfigCronJobManager.deleteCronJob(linkConfig);
        } catch (Exception e) {
            log.error("LinkConfigCronJobManager.deleteCronJob error: {}", e.getMessage(), e);
        }
        return 1;
    }

    private boolean checkTimeSpanValid(LinkConfig linkConfig) {
        if (linkConfig.getStartTime() == null && linkConfig.getEndTime() == null) {
            return true;
        } else {
            Date now = new Date();
            return linkConfig.getStartTime() != null && linkConfig.getEndTime() != null && linkConfig.getStartTime().before(now) && linkConfig.getEndTime().after(now);
        }
    }
}
