package com.siteweb.linkage.service.impl;

import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkSegment;
import com.siteweb.linkage.mapper.LinkSegmentMapper;
import com.siteweb.linkage.service.LinkSegmentService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class LinkSegmentServiceImpl implements LinkSegmentService {

    @Autowired
    LinkSegmentMapper linkSegmentMapper;

    @Override
    public List<LinkSegmentDTO> findLinkSegmentsByLinkConfigId(Integer linkConfigId) {
        List<LinkSegment> segments = linkSegmentMapper.findLinkSegmentsByLinkConfigId(linkConfigId);
        List<LinkSegmentDTO> segmentDTOList = new ArrayList<>();
        for (LinkSegment linkSegment : segments) {
            LinkSegmentDTO segmentDTO = new LinkSegmentDTO();
            BeanUtils.copyProperties(linkSegment, segmentDTO);
            segmentDTOList.add(segmentDTO);
        }
        return segmentDTOList;
    }
}
