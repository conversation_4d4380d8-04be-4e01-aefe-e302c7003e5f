package com.siteweb.linkage.service.impl;


import com.siteweb.linkage.dto.LinkNodeDTO;
import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkNode;
import com.siteweb.linkage.entity.LinkSegment;
import com.siteweb.linkage.mapper.LinkNodeMapper;
import com.siteweb.linkage.mapper.LinkSegmentMapper;
import com.siteweb.linkage.service.LinkNodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LinkNodeServiceImpl implements LinkNodeService {

    @Autowired
    LinkNodeMapper linkNodeMapper;

    @Autowired
    LinkSegmentMapper linkSegmentMapper;

    @Override
    public int createLinkNode(LinkNode linkNode) {
        return linkNodeMapper.insert(linkNode);
    }

    @Override
    public LinkNode findById(Integer linkNodeId) {
        return linkNodeMapper.selectById(linkNodeId);
    }

    @Override
    public List<LinkNodeDTO> findLinkNodesByLinkConfigId(Integer linkConfigId) {
        List<LinkNode> linkNodes = linkNodeMapper.findLinkNodesByLinkConfigId(linkConfigId);
        List<LinkSegment> linkSegments = linkSegmentMapper.findLinkSegmentsByLinkConfigId(linkConfigId);
        List<LinkNodeDTO> nodeDTOs = new ArrayList<>();
        for (LinkNode linkNode : linkNodes) {
            LinkNodeDTO nodeDTO = new LinkNodeDTO();
            BeanUtils.copyProperties(linkNode, nodeDTO);
            List<LinkSegmentDTO> segmentDTOList = new ArrayList<>();
            if ("right".equalsIgnoreCase(linkNode.getNodeDirection())) {
                List<LinkSegment> tmpSegmentList = linkSegments.stream().filter(o -> linkNode.getNodeId().equals(o.getInputNodeId())).collect(Collectors.toList());
                for (LinkSegment linkSegment : tmpSegmentList) {
                    LinkSegmentDTO segmentDTO = new LinkSegmentDTO();
                    BeanUtils.copyProperties(linkSegment, segmentDTO);
                    segmentDTOList.add(segmentDTO);
                }
            }
            nodeDTO.setSegmentDTOs(segmentDTOList);
            nodeDTOs.add(nodeDTO);
        }
        return nodeDTOs;
    }

    @Override
    public int updateLinkNode(LinkNode linkNode) {
        return linkNodeMapper.updateById(linkNode);
    }

    @Override
    public void deleteLinkNodeById(Integer linkNodeId) {
        linkNodeMapper.deleteById(linkNodeId);
    }
}
