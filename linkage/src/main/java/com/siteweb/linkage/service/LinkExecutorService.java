package com.siteweb.linkage.service;

import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkNodeDTO;
import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkInstance;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.vo.ControlCommandVO;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description LinkExecutorService
 * @createTime 2022-06-07 09:18:45
 */
public interface LinkExecutorService {

    List<LinkElementConfigDTO> findLinkElementConfigDTOsByLinkConfigId(Integer linkConfigId);

    List<LinkNodeDTO> findLinkNodesByLinkConfigId(Integer linkConfigId);

    List<LinkSegmentDTO> findLinkSegmentsByLinkConfigId(Integer linkConfigId);

    int createLinkInstance(LinkInstance linkInstance);

    List<ActiveEventDTO> findActiveEventsByEquipmentId(Integer equipmentId);

    List<ActiveEventDTO> findActiveEventsByResourceStructureId(Integer resourceStructureId);

    List<ActiveEventDTO> findActiveEventsByEquipmentIdAndEventId(Integer equipmentId, Integer eventId);

    RealTimeSignalItem getRealTimeSignalBySignalId(Integer equipmentId, Integer signalId);

    ControlResultType sendControlCommand(ControlCommandVO controlCommandVO, int userId);
}
