package com.siteweb.linkage.service;

import com.siteweb.linkage.entity.LinkConfig;

import java.util.List;

public interface LinkConfigService {

    int createLinkConfig(LinkConfig linkConfig);

    LinkConfig findByLinkConfigId(Integer linkConfigId);

    List<LinkConfig> findAllByLinkGroupId(Integer linkGroupId);

    List<LinkConfig> findAll();

    List<LinkConfig> findAllByLinkTriggerType(Integer linkTriggerType);

    int updateLinkConfig(LinkConfig linkConfig);

    int deleteLinkConfigById(Integer linkConfigId);
}
