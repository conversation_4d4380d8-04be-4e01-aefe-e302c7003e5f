package com.siteweb.linkage.service;

import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkLayoutDTO;

import java.util.List;

public interface LinkElementConfigService {

    int batchSaveLinkElementConfig(LinkLayoutDTO linkLayoutDTO);

    LinkElementConfigDTO findLinkElementConfigDTOByLinkElementConfigId(Integer linkElementConfigId);

    List<LinkElementConfigDTO> findLinkElementConfigDTOsByLinkConfigId(Integer linkConfigId);

    void deleteLinkElementConfigByLinkElementConfigId(Integer linkElementConfigId);
}
