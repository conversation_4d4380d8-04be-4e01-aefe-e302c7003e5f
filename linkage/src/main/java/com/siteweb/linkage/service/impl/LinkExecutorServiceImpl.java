package com.siteweb.linkage.service.impl;

import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkNodeDTO;
import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkInstance;
import com.siteweb.linkage.service.*;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.monitoring.vo.ControlCommandVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhou
 * @description LinkExecutorServiceImpl
 * @createTime 2022-06-07 09:21:02
 */
@Service
public class LinkExecutorServiceImpl implements LinkExecutorService {

    @Autowired
    LinkElementConfigService linkElementConfigService;

    @Autowired
    LinkNodeService linkNodeService;

    @Autowired
    LinkSegmentService linkSegmentService;

    @Autowired
    LinkInstanceService linkInstanceService;

    @Autowired
    ActiveEventService activeEventService;

    @Autowired
    RealTimeSignalManager realTimeSignalManager;

    @Autowired
    ActiveControlService activeControlService;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Override
    public List<LinkElementConfigDTO> findLinkElementConfigDTOsByLinkConfigId(Integer linkConfigId) {
        return linkElementConfigService.findLinkElementConfigDTOsByLinkConfigId(linkConfigId);
    }

    @Override
    public List<LinkNodeDTO> findLinkNodesByLinkConfigId(Integer linkConfigId) {
        return linkNodeService.findLinkNodesByLinkConfigId(linkConfigId);
    }

    @Override
    public List<LinkSegmentDTO> findLinkSegmentsByLinkConfigId(Integer linkConfigId) {
        return linkSegmentService.findLinkSegmentsByLinkConfigId(linkConfigId);
    }

    @Override
    public int createLinkInstance(LinkInstance linkInstance) {
        return linkInstanceService.createLinkInstance(linkInstance);
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByEquipmentId(Integer equipmentId) {
        return activeEventService.findActiveEventsByEquipmentId(equipmentId, null, null);
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByResourceStructureId(Integer resourceStructureId) {
        List<ResourceStructure> allRelatedResourceStructureList = new ArrayList<>();
        allRelatedResourceStructureList.add(resourceStructureManager.getResourceStructureById(resourceStructureId));
        resourceStructureManager.getAllFlatChildren(resourceStructureId, allRelatedResourceStructureList);
        return activeEventService.findActiveEventsByResourceStructureIdSet(allRelatedResourceStructureList.stream().map(ResourceStructure::getResourceStructureId).collect(Collectors.toSet()), null);
    }

    @Override
    public List<ActiveEventDTO> findActiveEventsByEquipmentIdAndEventId(Integer equipmentId, Integer eventId) {
        return activeEventService.findActiveEventsByEquipmentIdAndEventId(equipmentId, eventId);
    }

    @Override
    public RealTimeSignalItem getRealTimeSignalBySignalId(Integer equipmentId, Integer signalId) {
        return realTimeSignalManager.getRealTimeSignalBySignalId(equipmentId, signalId);
    }

    @Override
    public ControlResultType sendControlCommand(ControlCommandVO controlCommandVO, int userId) {
        return activeControlService.sendControlCommand(controlCommandVO, userId);
    }
}
