package com.siteweb.linkage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.linkage.entity.LinkElement;
import com.siteweb.linkage.mapper.LinkElementMapper;
import com.siteweb.linkage.service.LinkElementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LinkElementServiceImpl implements LinkElementService {

    @Autowired
    LinkElementMapper linkElementMapper;

    @Override
    public LinkElement findById(Integer linkElementId) {
        return linkElementMapper.selectById(linkElementId);
    }

    @Override
    public List<LinkElement> findAllByElementType(String elementType) {
        return linkElementMapper.selectList(new QueryWrapper<LinkElement>().eq("ElementType", elementType));
    }

    @Override
    public List<LinkElement> findVisibleElements() {
        return linkElementMapper.selectList(new QueryWrapper<LinkElement>().eq("visible", true));
    }

}
