package com.siteweb.linkage.service.impl;

import com.siteweb.linkage.entity.LinkConfigTemplate;
import com.siteweb.linkage.mapper.LinkConfigTemplateMapper;
import com.siteweb.linkage.service.LinkConfigTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LinkConfigTemplateServiceImpl implements LinkConfigTemplateService {

    @Autowired
    LinkConfigTemplateMapper linkConfigTemplateMapper;

    @Override
    public int createLinkConfigTemplate(LinkConfigTemplate linkConfigTemplate) {
        return linkConfigTemplateMapper.insert(linkConfigTemplate);
    }

    @Override
    public LinkConfigTemplate findByTemplateId(Integer linkConfigTemplateId) {
        return linkConfigTemplateMapper.selectById(linkConfigTemplateId);
    }

    @Override
    public List<LinkConfigTemplate> findAllLinkConfigTemplates() {
        return linkConfigTemplateMapper.selectList(null);
    }

    @Override
    public int updateLinkConfigTemplate(LinkConfigTemplate linkConfigTemplate) {
        return linkConfigTemplateMapper.updateById(linkConfigTemplate);
    }

    @Override
    public void deleteLinkConfigTemplate(Integer linkConfigTemplateId) {
        linkConfigTemplateMapper.deleteById(linkConfigTemplateId);
    }
}
