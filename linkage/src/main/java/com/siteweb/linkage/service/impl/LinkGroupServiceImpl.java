package com.siteweb.linkage.service.impl;

import com.siteweb.linkage.entity.LinkGroup;
import com.siteweb.linkage.mapper.LinkGroupMapper;
import com.siteweb.linkage.service.LinkGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LinkGroupServiceImpl implements LinkGroupService {

    @Autowired
    LinkGroupMapper linkGroupMapper;

    @Override
    public int createLinkGroup(LinkGroup linkGroup) {
        return linkGroupMapper.insert(linkGroup);
    }

    @Override
    public LinkGroup findByGroupId(Integer linkGroupId) {
        return linkGroupMapper.selectById(linkGroupId);
    }

    @Override
    public List<LinkGroup> findAllGroups() {
        return linkGroupMapper.selectList(null);
    }

    @Override
    public int updateLinkGroup(LinkGroup linkGroup) {
        return linkGroupMapper.updateById(linkGroup);
    }

    @Override
    public void deleteLinkGroup(Integer linkGroupId) {
        LinkGroup linkGroup = linkGroupMapper.selectById(linkGroupId);
        if (linkGroup != null) {
            linkGroupMapper.deleteById(linkGroupId);
        }
    }
}
