package com.siteweb.linkage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.linkage.entity.LinkInstance;
import com.siteweb.linkage.mapper.LinkInstanceMapper;
import com.siteweb.linkage.service.LinkInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LinkInstanceServiceImpl implements LinkInstanceService {

    @Autowired
    LinkInstanceMapper linkInstanceMapper;

    @Override
    public int createLinkInstance(LinkInstance linkInstance) {
        return linkInstanceMapper.insert(linkInstance);
    }

    @Override
    public List<LinkInstance> findAllByLinkConfigId(Integer linkConfigId) {
        return linkInstanceMapper.selectList(new QueryWrapper<LinkInstance>().eq("LinkConfigId", linkConfigId));
    }
}
