package com.siteweb.linkage.service;

import com.siteweb.linkage.entity.LinkNode;
import com.siteweb.linkage.dto.LinkNodeDTO;

import java.util.List;

public interface LinkNodeService {

    int createLinkNode(LinkNode linkNode);

    LinkNode findById(Integer linkNodeId);

    List<LinkNodeDTO> findLinkNodesByLinkConfigId(Integer linkConfigId);

    int updateLinkNode(LinkNode linkNode);

    void deleteLinkNodeById(Integer linkNodeId);
}
