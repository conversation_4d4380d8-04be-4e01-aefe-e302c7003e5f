package com.siteweb.linkage.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkLayoutDTO;
import com.siteweb.linkage.dto.LinkNodeDTO;
import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.linkage.entity.LinkElementConfig;
import com.siteweb.linkage.entity.LinkNode;
import com.siteweb.linkage.entity.LinkSegment;
import com.siteweb.linkage.mapper.LinkConfigMapper;
import com.siteweb.linkage.mapper.LinkElementConfigMapper;
import com.siteweb.linkage.mapper.LinkNodeMapper;
import com.siteweb.linkage.mapper.LinkSegmentMapper;
import com.siteweb.linkage.service.LinkElementConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class LinkElementConfigServiceImpl implements LinkElementConfigService {

    @Autowired
    LinkConfigMapper linkConfigMapper;

    @Autowired
    LinkElementConfigMapper linkElementConfigMapper;

    @Autowired
    LinkNodeMapper linkNodeMapper;

    @Autowired
    LinkSegmentMapper linkSegmentMapper;

    LinkElementConfig saveLinkElementConfig(LinkElementConfig linkElementConfig) {
        if (linkElementConfig.getLinkElementConfigId() != null) {
            linkElementConfigMapper.updateById(linkElementConfig);
            return linkElementConfig;
        }
        linkElementConfigMapper.insert(linkElementConfig);
        return linkElementConfig;
    }

    LinkNode saveLinkNode(LinkNode linkNode) {
        if (linkNode.getNodeId() != null) {
            linkNodeMapper.updateById(linkNode);
            return linkNode;
        }
        linkNodeMapper.insert(linkNode);
        return linkNode;
    }

    LinkSegment saveLinkSegment(LinkSegment linkSegment) {
        if (linkSegment.getSegmentId() != null) {
            linkSegmentMapper.updateById(linkSegment);
            return linkSegment;
        }
        linkSegmentMapper.insert(linkSegment);
        return linkSegment;
    }

    @Override
    @Transactional
    public int batchSaveLinkElementConfig(LinkLayoutDTO linkLayoutDTO) {
        LinkConfig linkConfig = linkConfigMapper.selectById(linkLayoutDTO.getLinkConfigId());
        if (linkConfig == null) {
            return -1;
        }
        HashMap<String, Integer> elementConfigIdHashMap = new HashMap<>();
        HashMap<String, Integer> nodeIdHashMap = new HashMap<>();
        HashMap<Integer, Integer> nodeElementConfigIdHashMap = new HashMap<>();
        HashSet<Integer> updatedElementConfigIdHashSet = new HashSet<>();
        for (LinkElementConfigDTO dto : linkLayoutDTO.getDtos()) {
            LinkElementConfig elementConfig = new LinkElementConfig();
            BeanUtils.copyProperties(dto, elementConfig);
            LinkElementConfig newElementConfig = this.saveLinkElementConfig(elementConfig);
            elementConfigIdHashMap.put(dto.getUniqueId(), newElementConfig.getLinkElementConfigId());
            if (dto.getNodeDTOs() == null || dto.getNodeDTOs().isEmpty())
                continue;
            updatedElementConfigIdHashSet.add(newElementConfig.getLinkElementConfigId());
            for (LinkNodeDTO nodeDTO : dto.getNodeDTOs()) {
                LinkNode linkNode = new LinkNode();
                BeanUtils.copyProperties(nodeDTO, linkNode);
                linkNode.setLinkElementConfigId(newElementConfig.getLinkElementConfigId());
                LinkNode newLinkNode = this.saveLinkNode(linkNode);
                nodeIdHashMap.put(nodeDTO.getUniqueId(), newLinkNode.getNodeId());
                nodeElementConfigIdHashMap.put(newLinkNode.getNodeId(), newElementConfig.getLinkElementConfigId());
            }
        }
        //处理已删除的控件
        List<LinkElementConfig> linkElementConfigs = linkElementConfigMapper.selectList(new QueryWrapper<LinkElementConfig>().eq("LinkConfigId", linkLayoutDTO.getLinkConfigId()));
        for (LinkElementConfig linkElementConfig : linkElementConfigs) {
            if (!updatedElementConfigIdHashSet.contains(linkElementConfig.getLinkElementConfigId())) {
                this.deleteLinkElementConfigByLinkElementConfigId(linkElementConfig.getLinkElementConfigId());
            }
        }
        //保存节点连线
        for (LinkElementConfigDTO dto : linkLayoutDTO.getDtos()) {
            dto.setLinkElementConfigId(elementConfigIdHashMap.get(dto.getUniqueId()));
            if (dto.getNodeDTOs() == null || dto.getNodeDTOs().isEmpty())
                continue;
            for (LinkNodeDTO nodeDTO : dto.getNodeDTOs()) {
                nodeDTO.setLinkElementConfigId(dto.getLinkElementConfigId());
                nodeDTO.setNodeId(nodeIdHashMap.get(nodeDTO.getUniqueId()));
                if (nodeDTO.getSegmentDTOs() != null) {
                    for (LinkSegmentDTO segmentDTO : nodeDTO.getSegmentDTOs()) {
                        LinkSegment linkSegment = new LinkSegment();
                        BeanUtils.copyProperties(segmentDTO, linkSegment);
                        linkSegment.setInputNodeId(nodeIdHashMap.get(segmentDTO.getInputNodeUniqueId()));
                        linkSegment.setInputLinkElementConfigId(nodeDTO.getLinkElementConfigId());
                        linkSegment.setOutputNodeId(nodeIdHashMap.get(segmentDTO.getOutputNodeUniqueId()));
                        linkSegment.setOutputLinkElementConfigId(nodeElementConfigIdHashMap.get(linkSegment.getOutputNodeId()));
                        if (linkSegment.getOutputLinkElementConfigId() == null) {
                            continue;
                        }
                        LinkSegment newSegment = this.saveLinkSegment(linkSegment);
                        segmentDTO.setSegmentId(newSegment.getSegmentId());
                        segmentDTO.setInputNodeId(newSegment.getInputNodeId());
                        segmentDTO.setOutputNodeId(newSegment.getOutputNodeId());
                        segmentDTO.setInputLinkElementConfigId(nodeDTO.getLinkElementConfigId());
                        segmentDTO.setOutputLinkElementConfigId(nodeElementConfigIdHashMap.get(linkSegment.getOutputNodeId()));
                    }
                }
            }
        }
        //保存前端布局展示需要的Layout信息
        linkConfig.setLayout(linkLayoutDTO.getLayout());
        linkConfig.setUpdateTime(new Date());
        linkConfigMapper.updateById(linkConfig);
        return 1;
    }

    @Override
    public LinkElementConfigDTO findLinkElementConfigDTOByLinkElementConfigId(Integer linkElementConfigId) {
        LinkElementConfig linkElementConfig = linkElementConfigMapper.selectById(linkElementConfigId);
        if (linkElementConfig == null)
            return null;
        LinkElementConfigDTO elementConfigDTO = new LinkElementConfigDTO();
        BeanUtils.copyProperties(linkElementConfig, elementConfigDTO);
        List<LinkNode> linkNodes = linkNodeMapper.selectList(new QueryWrapper<LinkNode>().eq("LinkElementConfigId", linkElementConfigId));
        List<LinkSegment> linkSegments = linkSegmentMapper.selectList(new QueryWrapper<LinkSegment>().eq("InputLinkElementConfigId", linkElementConfigId));
        List<LinkNodeDTO> nodeDTOs = new ArrayList<>();
        for (LinkNode linkNode : linkNodes) {
            LinkNodeDTO nodeDTO = new LinkNodeDTO();
            BeanUtils.copyProperties(linkNode, nodeDTO);
            List<LinkSegmentDTO> segmentDTOList = new ArrayList<>();
            if ("right".equalsIgnoreCase(linkNode.getNodeDirection())) {
                List<LinkSegment> tmpSegmentList = linkSegments.stream().filter(o -> linkNode.getNodeId().equals(o.getInputNodeId())).toList();
                for (LinkSegment linkSegment : tmpSegmentList) {
                    LinkSegmentDTO segmentDTO = new LinkSegmentDTO();
                    BeanUtils.copyProperties(linkSegment, segmentDTO);
                    segmentDTOList.add(segmentDTO);
                }
            }
            nodeDTO.setSegmentDTOs(segmentDTOList);
            nodeDTOs.add(nodeDTO);
        }
        elementConfigDTO.setNodeDTOs(nodeDTOs);
        return elementConfigDTO;
    }

    @Override
    public List<LinkElementConfigDTO> findLinkElementConfigDTOsByLinkConfigId(Integer linkConfigId) {
        List<LinkElementConfigDTO> result = new ArrayList<>();
        List<LinkElementConfig> linkElementConfigs = linkElementConfigMapper.selectList(new QueryWrapper<LinkElementConfig>().eq("LinkConfigId", linkConfigId));
        for (LinkElementConfig linkElementConfig : linkElementConfigs) {
            LinkElementConfigDTO elementConfigDTO = new LinkElementConfigDTO();
            BeanUtils.copyProperties(linkElementConfig, elementConfigDTO);
            List<LinkNode> linkNodes = linkNodeMapper.selectList(new QueryWrapper<LinkNode>().eq("LinkElementConfigId", linkElementConfig.getLinkElementConfigId()));
            List<LinkSegment> linkSegments = linkSegmentMapper.selectList(new QueryWrapper<LinkSegment>().eq("InputLinkElementConfigId", linkElementConfig.getLinkElementConfigId()));
            List<LinkNodeDTO> nodeDTOs = new ArrayList<>();
            for (LinkNode linkNode : linkNodes) {
                LinkNodeDTO nodeDTO = new LinkNodeDTO();
                BeanUtils.copyProperties(linkNode, nodeDTO);
                List<LinkSegmentDTO> segmentDTOList = new ArrayList<>();
                if ("right".equalsIgnoreCase(linkNode.getNodeDirection())) {
                    List<LinkSegment> tmpSegmentList = linkSegments.stream().filter(o -> linkNode.getNodeId().equals(o.getInputNodeId())).toList();
                    for (LinkSegment linkSegment : tmpSegmentList) {
                        LinkSegmentDTO segmentDTO = new LinkSegmentDTO();
                        BeanUtils.copyProperties(linkSegment, segmentDTO);
                        segmentDTOList.add(segmentDTO);
                    }
                }
                nodeDTO.setSegmentDTOs(segmentDTOList);
                nodeDTOs.add(nodeDTO);
            }
            elementConfigDTO.setNodeDTOs(nodeDTOs);
            result.add(elementConfigDTO);
        }
        return result;
    }

    @Override
    @Transactional
    public void deleteLinkElementConfigByLinkElementConfigId(Integer linkElementConfigId) {
        linkElementConfigMapper.deleteLinkElementConfigById(linkElementConfigId);
    }
}
