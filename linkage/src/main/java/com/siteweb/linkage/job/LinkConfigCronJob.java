package com.siteweb.linkage.job;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.linkage.service.LinkConfigService;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.service.HAStatusService;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description LinkConfigCronJob
 * @createTime 2022-09-21 15:30:58
 */
@Component
public class LinkConfigCronJob implements BaseJob {

    private final Logger log = LoggerFactory.getLogger(LinkConfigCronJob.class);

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    LinkConfigService linkConfigService;
    @Autowired
    HAStatusService haStatusService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (!haStatusService.isMasterHost()) {
            log.info("不是主机 退出定时任务");
            return;
        }
        log.info("LinkConfigCronJob execute");
        JobDataMap params = context.getJobDetail().getJobDataMap();
        if (params.size() == 0 || !params.containsKey("linkConfigId")) {
            return;
        }
        LinkConfig linkConfig = linkConfigService.findByLinkConfigId(params.getInt("linkConfigId"));
        if (ObjectUtil.isNull(linkConfig) || !Boolean.TRUE.equals(linkConfig.getUsedStatus()) || !checkTimeSpanValid(linkConfig)) {
            return;
        }
        //publish linkConfig event
        BaseSpringEvent<LinkConfig> linkConfigBaseSpringEvent = new BaseSpringEvent<>(linkConfig);
        linkConfigBaseSpringEvent.setData(linkConfig);
        applicationEventPublisher.publishEvent(linkConfigBaseSpringEvent);
    }

    private boolean checkTimeSpanValid(LinkConfig linkConfig) {
        if (linkConfig.getStartTime() == null && linkConfig.getEndTime() == null) {
            return true;
        } else {
            Date now = new Date();
            return linkConfig.getStartTime() != null && linkConfig.getEndTime() != null && linkConfig.getStartTime().before(now) && linkConfig.getEndTime().after(now);
        }
    }
}
