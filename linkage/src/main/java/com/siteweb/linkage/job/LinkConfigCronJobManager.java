package com.siteweb.linkage.job;

import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> zhou
 * @description LinkConfigCronJobManager
 * @createTime 2022-09-21 15:36:04
 */
@Component
public class LinkConfigCronJobManager {

    @Autowired
    SchedulerJobService schedulerService;

    private static final String JOB_GROUP = "LinkConfigCronJob";

    public void addCronJob(LinkConfig linkConfig) throws ClassNotFoundException, InstantiationException, SchedulerException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        if (linkConfig == null) {
            return;
        }
        SchedulerJob schedulerJob = getJobFromSafeMessageTaskManagement(linkConfig);
        schedulerService.addSchedulerJob(schedulerJob);
    }

    public void updateCronJob(LinkConfig linkConfig) throws ClassNotFoundException, InstantiationException, SchedulerException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        if (linkConfig == null) {
            return;
        }
        SchedulerJob schedulerJob = getJobFromSafeMessageTaskManagement(linkConfig);
        //修改时先删除再增加
        if (schedulerService.isJobWithNamePresent(schedulerJob.getJobName(), schedulerJob.getJobGroup())) {
            schedulerService.removeSchedulerJob(schedulerJob);
        }
        //
        if (linkConfig.getLinkTriggerType().equals(1) && Boolean.TRUE.equals(linkConfig.getUsedStatus())) {
            schedulerService.addCronSchedulerJob(schedulerJob);
        }
    }

    public void deleteCronJob(LinkConfig linkConfig) throws SchedulerException {
        if (linkConfig == null) {
            return;
        }
        SchedulerJob schedulerJob = getJobFromSafeMessageTaskManagement(linkConfig);
        schedulerService.removeSchedulerJob(schedulerJob);
    }

    private SchedulerJob getJobFromSafeMessageTaskManagement(LinkConfig linkConfig) {
        SchedulerJob schedulerJob = new SchedulerJob();
        schedulerJob.setClassName(LinkConfigCronJob.class.getName());
        schedulerJob.setCronExpression(linkConfig.getCron());
        schedulerJob.setJobGroup(JOB_GROUP);
        schedulerJob.setJobName(JOB_GROUP + linkConfig.getLinkConfigId());
        Map<String, Object> params = new HashMap<>();
        params.put("linkConfigId", linkConfig.getLinkConfigId());//配置主键
        schedulerJob.setParams(params);
        return schedulerJob;
    }
}
