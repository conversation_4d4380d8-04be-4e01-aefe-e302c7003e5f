package com.siteweb.linkage.executor;

import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkNodeDTO;
import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkInstance;
import com.siteweb.linkage.service.*;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.vo.ControlCommandVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.Stream;


public class LinkInstanceExecutor implements Runnable, Callable<HashMap<Integer, String>> {

    private final Logger log = LoggerFactory.getLogger(LinkInstanceExecutor.class);

    private static final String LEFT = "left";
    private static final String RIGHT = "right";
    private static final String TRUE = "true";
    private static final String FALSE = "false";
    private static final String EVENT = "event";
    private static final String EQUIPMENT = "equipment";
    private static final String RESOURCE_STRUCTURE = "resourcestructure";
    private static final Integer DEFAULT_SEND_COMMAND_USER = -1;

    private final HashMap<Integer, Boolean> visitedElementConfigIdHashMap = new HashMap<>();

    private final Deque<Integer> currentElementConfigIdDeque = new ArrayDeque<>();
    private final Deque<LinkElementConfigDTO> toCalcElementConfigDeque = new ArrayDeque<>();
    private final HashMap<Integer, String> nodeExpressionHashMap = new HashMap<>();

    private final ScheduledThreadPoolExecutor threadPoolExecutor;

    private final Integer linkConfigId;
    //事件节点是否需要重新计算，对于事件触发的后台联动策略，该值为false；对于定时触发的后台联动策略，该值为true
    private final boolean eventCalculate;
    private final LinkExecutorService linkExecutorService;
    private List<LinkElementConfigDTO> linkElementConfigDTOList;
    private List<LinkNodeDTO> linkNodeDTOList;
    private List<LinkSegmentDTO> linkSegmentDTOList;

    public LinkInstanceExecutor(Integer linkConfigId, boolean eventCalculate, LinkExecutorService linkExecutorService, ScheduledThreadPoolExecutor threadPoolExecutor) {
        this.linkConfigId = linkConfigId;
        this.eventCalculate = eventCalculate;
        this.linkExecutorService = linkExecutorService;
        this.threadPoolExecutor = threadPoolExecutor;
    }

    public LinkInstanceExecutor(Integer linkConfigId, boolean eventCalculate, List<LinkElementConfigDTO> linkElementConfigDTOList, List<LinkNodeDTO> linkNodeDTOList, List<LinkSegmentDTO> linkSegmentDTOList, List<LinkElementConfigDTO> level1ElementConfigDTOList, LinkExecutorService linkExecutorService, ScheduledThreadPoolExecutor threadPoolExecutor) {
        this.linkConfigId = linkConfigId;
        this.eventCalculate = eventCalculate;
        this.linkExecutorService = linkExecutorService;
        this.threadPoolExecutor = threadPoolExecutor;
        this.linkElementConfigDTOList = linkElementConfigDTOList;
        this.linkNodeDTOList = linkNodeDTOList;
        this.linkSegmentDTOList = linkSegmentDTOList;
        for (LinkElementConfigDTO elementConfigDTO : level1ElementConfigDTOList) {
            toCalcElementConfigDeque.addLast(elementConfigDTO);
        }
    }

    @Override
    public void run() {
        try {
            calcLinkElementConfigExpression();
        } catch (Exception ex) {
            log.error("后台联动线程执行失败 {} {}", ex, ex.getStackTrace());
        }
    }

    @Override
    public HashMap<Integer, String> call() throws Exception {
        if (toCalcElementConfigDeque.isEmpty()) {
            calcLevel1ElementConfig();
        }
        boolean execResult = true;
        while (!toCalcElementConfigDeque.isEmpty()) {
            LinkElementConfigDTO elementConfigDTO = toCalcElementConfigDeque.pollFirst();
            if (elementConfigDTO != null) {
                boolean execStatus = calcElementConfigExpression(elementConfigDTO);
                if (!execStatus) {
                    execResult = false;
                    break;
                }
            }
        }
        if (execResult) {
            return nodeExpressionHashMap;
        }
        return null;
    }

    private void calcLinkElementConfigExpression() {
        if (toCalcElementConfigDeque.isEmpty()) {
            calcLevel1ElementConfig();
        }
        boolean execResult = true;
        while (!toCalcElementConfigDeque.isEmpty()) {
            LinkElementConfigDTO elementConfigDTO = toCalcElementConfigDeque.pollFirst();
            if (elementConfigDTO != null) {
                boolean execStatus = calcElementConfigExpression(elementConfigDTO);
                if (!execStatus) {
                    execResult = false;
                    break;
                }
            }
        }
        LinkInstance linkInstance = new LinkInstance();
        linkInstance.setLinkConfigId(this.linkConfigId);
        if (execResult) {
            linkInstance.setStatus(1);
            linkInstance.setStatusResult("执行成功");
        } else {
            linkInstance.setStatus(2);
            linkInstance.setStatusResult("执行失败");
        }
        linkInstance.setUpdateTime(new Date());
        this.linkExecutorService.createLinkInstance(linkInstance);
    }

    private void calcLevel1ElementConfig() {
        this.visitedElementConfigIdHashMap.clear();
        this.currentElementConfigIdDeque.clear();
        this.nodeExpressionHashMap.clear();
        this.linkElementConfigDTOList = this.linkExecutorService.findLinkElementConfigDTOsByLinkConfigId(this.linkConfigId);
        this.linkNodeDTOList = this.linkExecutorService.findLinkNodesByLinkConfigId(this.linkConfigId);
        this.linkSegmentDTOList = this.linkExecutorService.findLinkSegmentsByLinkConfigId(this.linkConfigId);
        for (LinkElementConfigDTO elementConfigDTO : linkElementConfigDTOList) {
            visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), false);
            if (elementConfigDTO.getNodeDTOs() == null || elementConfigDTO.getNodeDTOs().isEmpty() || elementConfigDTO.getNodeDTOs().stream().noneMatch(o -> LEFT.equalsIgnoreCase(o.getNodeDirection()))) {
                toCalcElementConfigDeque.addLast(elementConfigDTO);
                continue;
            }
            for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
                if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                    List<LinkSegmentDTO> inputSegments = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                    if (inputSegments.isEmpty()) {
                        toCalcElementConfigDeque.addLast(elementConfigDTO);
                    }
                }
            }
        }
    }

    private boolean calcElementConfigExpression(LinkElementConfigDTO elementConfigDTO) {
        boolean result = true;
        switch (elementConfigDTO.getElementId()) {
            case 1://整型加法
                result = calcIntegerAddition(elementConfigDTO);
                break;
            case 2://整型减法
                result = calcIntegerSubtraction(elementConfigDTO);
                break;
            case 3://整型乘法
                result = calcIntegerMultiplication(elementConfigDTO);
                break;
            case 4://整型除法
                result = calcIntegerDivision(elementConfigDTO);
                break;
            case 5://整型取模
                result = calcIntegerDelivery(elementConfigDTO);
                break;
            case 6://浮点数加法
                result = calcFloatAddition(elementConfigDTO);
                break;
            case 7://浮点数减法
                result = calcFloatSubtraction(elementConfigDTO);
                break;
            case 8://浮点数乘法
                result = calcFloatMultiplication(elementConfigDTO);
                break;
            case 9://浮点数除法
                result = calcFloatDivision(elementConfigDTO);
                break;
            case 10, 11, 12, 13, 14, 15://关系运算
                result = calcRelationalOperation(elementConfigDTO);
                break;
            case 16, 17, 18://逻辑运算
                result = calcLogicalOperation(elementConfigDTO);
                break;
            case 19, 20, 21://最大值，最小值，平均值
                result = calcFunctionOperation(elementConfigDTO);
                break;
            case 31://事件开始
                result = calcEventStatus(elementConfigDTO, 1);
                break;
            case 32://事件结束
                result = calcEventStatus(elementConfigDTO, 2);
                break;
            case 35://事件确认
                result = calcEventStatus(elementConfigDTO, 3);
                break;
            case 33://当前秒级时间戳
                result = calcTimestamp(elementConfigDTO);
                break;
            case 34://时间延迟
                result = calcDelaySeconds(elementConfigDTO);
                break;
            case 36://事件存在
                result = calcActiveEventExists(elementConfigDTO);
                break;
            case 41://并行执行
                result = calcParallel(elementConfigDTO);
                break;
            case 42://执行脚本
                result = calcExecScript(elementConfigDTO);
                break;
            case 43://空指令
                result = calcEmptyCommand(elementConfigDTO);
                break;
            case 44://设备控制
                result = calcDeviceCommand(elementConfigDTO);
                break;
            case 51://条件运算
                result = calcIF(elementConfigDTO);
                break;
            case 52, 53://整型常量,浮点数常量
                result = calcConstant(elementConfigDTO);
                break;
            case 61://测点
                result = calcCorePoint(elementConfigDTO);
                break;
            case 62://设备
                result = calcDevice(elementConfigDTO);
                break;
            case 63://层级
                result = calcResourceStructure(elementConfigDTO);
                break;
            default:
                break;
        }
        log.info("当前联动控制的LinkConfigId为{}，已计算的控件LinkElementConfigId为{}", elementConfigDTO.getLinkConfigId(), elementConfigDTO.getLinkElementConfigId());
        if (result) {
            checkNextLinkElementConfig(elementConfigDTO);
        }
        return result;
    }

    //当前elementConfigDTO计算完毕后，找到其输出节点关联的下一个LinkElementConfigDTO并加入toCalcElementConfigDeque
    private void checkNextLinkElementConfig(LinkElementConfigDTO elementConfigDTO) {
        //对于并行执行和条件运算，不需要再找它下一可执行控件
        if (elementConfigDTO.getElementId().equals(41) || elementConfigDTO.getElementId().equals(51)) {
            return;
        }
        List<Integer> nextLinkElementConfigIds = new ArrayList<>();
        List<LinkNodeDTO> currentOutputNodes = elementConfigDTO.getNodeDTOs().stream().filter(o -> RIGHT.equalsIgnoreCase(o.getNodeDirection())).toList();
        for (LinkNodeDTO linkNodeDTO : currentOutputNodes) {
            List<LinkSegmentDTO> tmpList = linkSegmentDTOList.stream().filter(o -> linkNodeDTO.getNodeId().equals(o.getInputNodeId())).toList();
            if (!tmpList.isEmpty()) {
                nextLinkElementConfigIds.add(tmpList.get(0).getOutputLinkElementConfigId());
            }
        }
        for (Integer elementConfigId : nextLinkElementConfigIds) {
            List<Integer> tmpInputNodeIdList = linkNodeDTOList.stream().filter(o -> elementConfigId.equals(o.getLinkElementConfigId()) && LEFT.equalsIgnoreCase(o.getNodeDirection())).map(LinkNodeDTO::getNodeId).toList();
            List<Integer> tmpList = linkSegmentDTOList.stream().filter(o -> tmpInputNodeIdList.contains(o.getOutputNodeId())).map(LinkSegmentDTO::getInputNodeId).toList();
            if (nodeExpressionHashMap.keySet().containsAll(tmpList)) {
                Optional<LinkElementConfigDTO> nextElementConfigDTOOptional = linkElementConfigDTOList.stream().filter(o -> elementConfigId.equals(o.getLinkElementConfigId())).findFirst();
                nextElementConfigDTOOptional.ifPresent(toCalcElementConfigDeque::addLast);
            }
        }
    }

    //计算整型加法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcIntegerAddition(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Integer result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> tmpList = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                for (LinkSegmentDTO segmentDTO : tmpList) {
                    if (result == null) {
                        result = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId())));
                    } else {
                        result += (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId())));
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算整型减法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcIntegerSubtraction(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Integer result = null;
        Integer subtractedNumber = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent()) {
                    //NodeIndex为1代表左上角减数
                    if (nodeDTO.getNodeIndex().equals(1)) {
                        result = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())));
                    } else if (nodeDTO.getNodeIndex().equals(2)) {//NodeIndex为2代表左下角被减数
                        subtractedNumber = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())));
                    }
                }
            }
        }
        if (outputNode == null || result == null || subtractedNumber == null) {
            return false;
        }
        result = result - subtractedNumber;
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算整型乘法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcIntegerMultiplication(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Integer result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> tmpList = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                for (LinkSegmentDTO segmentDTO : tmpList) {
                    if (result == null) {
                        result = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId())));
                    } else {
                        result *= (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId())));
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算整型除法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcIntegerDivision(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Integer result = null;
        Integer divisor = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent()) {
                    //NodeIndex为1代表左上角除数
                    if (nodeDTO.getNodeIndex().equals(1)) {
                        result = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())));
                    } else if (nodeDTO.getNodeIndex().equals(2)) {//NodeIndex为2代表左下角被除数
                        divisor = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())));
                    }
                }
            }
        }
        if (outputNode == null || result == null || divisor == null) {
            return false;
        }
        result = result / divisor;
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //对整型取模，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcIntegerDelivery(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Integer result = null;
        Integer operand = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent()) {
                    //NodeIndex为1代表左上角操作数
                    if (nodeDTO.getNodeIndex().equals(1)) {
                        result = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())));
                    } else if (nodeDTO.getNodeIndex().equals(2)) {//NodeIndex为2代表左下角操作数
                        operand = (int) Math.floor(Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())));
                    }
                }
            }
        }
        if (outputNode == null || result == null || operand == null) {
            return false;
        }
        result = result % operand;
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算浮点数加法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcFloatAddition(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Float result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> tmpList = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                for (LinkSegmentDTO segmentDTO : tmpList) {
                    if (result == null) {
                        result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                    } else {
                        result += Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算浮点数减法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcFloatSubtraction(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Float result = null;
        Float subtractedNumber = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent()) {
                    //NodeIndex为1代表左上角减数
                    if (nodeDTO.getNodeIndex().equals(1)) {
                        result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()));
                    } else if (nodeDTO.getNodeIndex().equals(2)) {//NodeIndex为2代表左下角被减数
                        subtractedNumber = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()));
                    }
                }
            }
        }
        if (outputNode == null || result == null || subtractedNumber == null) {
            return false;
        }
        result = result - subtractedNumber;
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算浮点数乘法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcFloatMultiplication(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Float result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> tmpList = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                for (LinkSegmentDTO segmentDTO : tmpList) {
                    if (result == null) {
                        result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                    } else {
                        result *= Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算浮点数除法，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcFloatDivision(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Float result = null;
        Float divisor = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent()) {
                    //NodeIndex为1代表左上角除数
                    if (nodeDTO.getNodeIndex().equals(1)) {
                        result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()));
                    } else if (nodeDTO.getNodeIndex().equals(2)) {//NodeIndex为2代表左下角被除数
                        divisor = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()));
                    }
                }
            }
        }
        if (outputNode == null || result == null || divisor == null) {
            return false;
        }
        result = result / divisor;
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //关系运算，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcRelationalOperation(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Float operandA = null;
        Float operandB = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent()) {
                    //NodeIndex为1代表左上角操作数
                    if (nodeDTO.getNodeIndex().equals(1)) {
                        operandA = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()));
                    } else if (nodeDTO.getNodeIndex().equals(2)) {//NodeIndex为2代表左下角操作数
                        operandB = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()));
                    }
                }
            }
        }
        if (outputNode == null || operandA == null || operandB == null) {
            return false;
        }
        String result = "";
        switch (elementConfigDTO.getExpression()) {
            case ">":
                result = operandA > operandB ? TRUE : FALSE;
                break;
            case ">=":
                result = operandA >= operandB ? TRUE : FALSE;
                break;
            case "==":
                result = operandA.equals(operandB) ? TRUE : FALSE;
                break;
            case "<=":
                result = operandA <= operandB ? TRUE : FALSE;
                break;
            case "<":
                result = operandA < operandB ? TRUE : FALSE;
                break;
            case "!=":
                result = !operandA.equals(operandB) ? TRUE : FALSE;
                break;
            default:
                break;
        }
        if (result.isEmpty())
            return false;
        nodeExpressionHashMap.put(outputNode.getNodeId(), result);
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //逻辑运算，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcLogicalOperation(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Boolean result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> tmpList = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                for (LinkSegmentDTO segmentDTO : tmpList) {
                    if (result == null) {
                        result = Boolean.parseBoolean(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                    } else {
                        switch (elementConfigDTO.getExpression()) {
                            case "&&":
                                result = result && Boolean.parseBoolean(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                                break;
                            case "||":
                                result = result || Boolean.parseBoolean(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                                break;
                            case "!":
                                result = !Boolean.parseBoolean(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //聚合函数，计算结果及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcFunctionOperation(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Float result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> segmentDTOList = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                switch (elementConfigDTO.getExpression()) {
                    case "max()":
                        for (LinkSegmentDTO segmentDTO : segmentDTOList) {
                            if (result == null) {
                                result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                            } else {
                                result = Math.max(result, Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId())));
                            }
                        }
                        break;
                    case "min()":
                        for (LinkSegmentDTO segmentDTO : segmentDTOList) {
                            if (result == null) {
                                result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                            } else {
                                result = Math.min(result, Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId())));
                            }
                        }
                        break;
                    case "mean()":
                        for (LinkSegmentDTO segmentDTO : segmentDTOList) {
                            if (result == null) {
                                result = Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                            } else {
                                result += Float.parseFloat(nodeExpressionHashMap.get(segmentDTO.getInputNodeId()));
                            }
                        }
                        if (result != null) {
                            result = result / segmentDTOList.size();
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //从eventList列表中找到符合指定过滤条件的记录，若找到则返回true，否则返回false
    private boolean checkActiveEventExists(List<ActiveEventDTO> activeEventDTOList, String eventSeverityStr) {
        boolean exists = false;
        activeEventDTOList = activeEventDTOList.stream().filter(o -> o.getEndTime() == null).toList();
        if (activeEventDTOList.isEmpty()) {
            return false;
        }
        if (eventSeverityStr == null) {
            return true;
        }
        String[] tmpArray = eventSeverityStr.split(":");
        if (tmpArray.length != 2) {
            return false;
        }
        if ("severity".equalsIgnoreCase(tmpArray[0])) {
            String[] severityIdArray = tmpArray[1].split(",");
            if (severityIdArray.length == 0) {
                return false;
            }
            List<Integer> severityIdList = Arrays.asList(severityIdArray).stream().map(Integer::parseInt).toList();
            if (activeEventDTOList.stream().anyMatch(o -> severityIdList.contains(o.getEventLevel()))) {
                exists = true;
            }
        }
        return exists;
    }

    //从activeEventDTOList列表中找到未结束活动告警，若找到则返回false，否则返回true
    private boolean checkNoActiveEventExists(List<ActiveEventDTO> activeEventDTOList) {
        boolean result = false;
        activeEventDTOList = activeEventDTOList.stream().filter(o -> o.getEndTime() == null).toList();
        if (activeEventDTOList.isEmpty()) {
            result = true;
        }
        return result;
    }

    //从activeEventDTOList列表中找到未确认活动告警，若找到则返回false，否则返回true
    private boolean checkNoUnConfirmedActiveEventExists(List<ActiveEventDTO> activeEventDTOList) {
        boolean result = false;
        activeEventDTOList = activeEventDTOList.stream().filter(o -> o.getConfirmTime() == null).toList();
        if (activeEventDTOList.isEmpty()) {
            result = true;
        }
        return result;
    }

    //事件开始、事件结束或事件确认
    private boolean calcEventStatus(LinkElementConfigDTO elementConfigDTO, Integer alarmChangeOperationType) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Boolean result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent() && nodeExpressionHashMap.containsKey(segmentDTO.get().getInputNodeId())) {
                    String[] tmpArray = nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()).split(":");
                    Set<String> tmpSet = Stream.of(EVENT, EQUIPMENT, RESOURCE_STRUCTURE).collect(Collectors.toSet());
                    if (tmpArray.length == 2 && tmpSet.contains(tmpArray[0])) {
                        if (!this.eventCalculate || alarmChangeOperationType.equals(1)) {
                            //如果前置节点已经判断结束或确认，这里直接返回true
                            //如果是事件开始，这里也直接返回true
                            result = true;
                        } else {
                            if (alarmChangeOperationType.equals(2)) {//判断是否有未结束活动告警
                                result = calcEventEndByNodeExpression(tmpArray);
                            } else if (alarmChangeOperationType.equals(3)) {//判断是否有未确认活动告警
                                result = calcEventConfirmByNodeExpression(tmpArray);
                            }
                        }
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    private boolean calcEventEndByNodeExpression(String[] tmpArray) {
        boolean result = false;
        switch (tmpArray[0]) {
            case EVENT:
                //判断当前event是否未结束，有则返回true，没有则返回false
                String[] idArray = tmpArray[1].split(",");
                if (idArray.length == 3) {
                    result = checkNoActiveEventExists(linkExecutorService.findActiveEventsByEquipmentIdAndEventId(Integer.parseInt(idArray[1]), Integer.parseInt(idArray[2])));
                }
                break;
            case EQUIPMENT:
                //判断当前equipment是否有未结束活动告警，有则返回true，没有则返回false
                result = checkNoActiveEventExists(linkExecutorService.findActiveEventsByEquipmentId(Integer.parseInt(tmpArray[1])));
                break;
            case RESOURCE_STRUCTURE:
                //判断当前resourceStructure是否有未结束活动告警，有则返回true，没有则返回false
                result = checkNoActiveEventExists(linkExecutorService.findActiveEventsByResourceStructureId(Integer.parseInt(tmpArray[1])));
                break;
            default:
                break;
        }
        return result;
    }

    private boolean calcEventConfirmByNodeExpression(String[] tmpArray) {
        boolean result = false;
        switch (tmpArray[0]) {
            case EVENT:
                //判断当前event是否未确认，有则返回true，没有则返回false
                String[] idArray = tmpArray[1].split(",");
                if (idArray.length == 3) {
                    result = checkNoUnConfirmedActiveEventExists(linkExecutorService.findActiveEventsByEquipmentIdAndEventId(Integer.parseInt(idArray[1]), Integer.parseInt(idArray[2])));
                }
                break;
            case EQUIPMENT:
                //判断当前equipment是否有未确认活动告警，有则返回true，没有则返回false
                result = checkNoUnConfirmedActiveEventExists(linkExecutorService.findActiveEventsByEquipmentId(Integer.parseInt(tmpArray[1])));
                break;
            case RESOURCE_STRUCTURE:
                //判断当前resourceStructure是否有未确认活动告警，有则返回true，没有则返回false
                result = checkNoUnConfirmedActiveEventExists(linkExecutorService.findActiveEventsByResourceStructureId(Integer.parseInt(tmpArray[1])));
                break;
            default:
                break;
        }
        return result;
    }

    //计算当前秒级时间戳
    private boolean calcTimestamp(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                nodeExpressionHashMap.put(nodeDTO.getNodeId(), String.valueOf(new Date().getTime() / 1000));
                currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
                visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
                return true;
            }
        }
        return false;
    }

    //时间延迟
    private boolean calcDelaySeconds(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null || elementConfigDTO.getExpression() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                long delaySeconds = Integer.parseInt(elementConfigDTO.getExpression());
                try {
                    Thread.sleep(delaySeconds * 1000);
                } catch (InterruptedException e) {
                    log.error(e.getMessage());
                    Thread.currentThread().interrupt();
                }
                nodeExpressionHashMap.put(nodeDTO.getNodeId(), elementConfigDTO.getExpression());
                currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
                visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
                return true;
            }
        }
        return false;
    }

    //事件存在
    private boolean calcActiveEventExists(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkNodeDTO outputNode = null;
        Boolean result = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent() && nodeExpressionHashMap.containsKey(segmentDTO.get().getInputNodeId())) {
                    String[] tmpArray = nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()).split(":");
                    if (tmpArray.length == 2) {
                        switch (tmpArray[0]) {
                            case EVENT:
                                //判断当前event是否有活动告警，有则返回true，没有则返回false
                                String[] idArray = tmpArray[1].split(",");
                                if (idArray.length == 3) {
                                    result = checkActiveEventExists(linkExecutorService.findActiveEventsByEquipmentIdAndEventId(Integer.parseInt(idArray[1]), Integer.parseInt(idArray[2])), elementConfigDTO.getExpression());
                                }
                                break;
                            case EQUIPMENT:
                                //判断当前equipment是否有事件，有则返回true，没有则返回false
                                result = checkActiveEventExists(linkExecutorService.findActiveEventsByEquipmentId(Integer.parseInt(tmpArray[1])), elementConfigDTO.getExpression());
                                break;
                            case RESOURCE_STRUCTURE:
                                //判断当前resourceStructure是否有事件，有则返回true，没有则返回false
                                result = checkActiveEventExists(linkExecutorService.findActiveEventsByResourceStructureId(Integer.parseInt(tmpArray[1])), elementConfigDTO.getExpression());
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
        if (outputNode == null || result == null) {
            return false;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), result.toString());
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //并行执行
    private boolean calcParallel(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                List<LinkSegmentDTO> tmpList = this.linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getInputNodeId())).toList();
                for (LinkSegmentDTO segmentDTO : tmpList) {
                    List<LinkElementConfigDTO> level1ElementConfigDTOList = this.linkElementConfigDTOList.stream().filter(o -> segmentDTO.getOutputLinkElementConfigId().equals(o.getLinkElementConfigId())).toList();
                    if (!level1ElementConfigDTOList.isEmpty()) {
                        LinkInstanceExecutor subExecutor = new LinkInstanceExecutor(this.linkConfigId, this.eventCalculate, this.linkElementConfigDTOList, this.linkNodeDTOList, this.linkSegmentDTOList, level1ElementConfigDTOList, this.linkExecutorService, this.threadPoolExecutor);
                        Future<?> future = threadPoolExecutor.submit((Callable<?>) subExecutor);
                        try {
                            future.get();
                        } catch (InterruptedException e) {
                            log.error("calcParallel InterruptedException", e);
                            Thread.currentThread().interrupt();
                            return false;
                        } catch (ExecutionException e) {
                            log.error("calcParallel ExecutionException", e);
                            return false;
                        }
                    }
                }
            }
        }
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //执行脚本
    private boolean calcExecScript(LinkElementConfigDTO elementConfigDTO) {
        //TODO:
        return true;
    }

    //空指令
    private boolean calcEmptyCommand(LinkElementConfigDTO elementConfigDTO) {
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //设备控制
    private boolean calcDeviceCommand(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        Optional<LinkNodeDTO> optionalLinkNodeDTO = elementConfigDTO.getNodeDTOs().stream().filter(o -> LEFT.equalsIgnoreCase(o.getNodeDirection())).findFirst();
        if (!optionalLinkNodeDTO.isPresent())
            return false;
        Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> optionalLinkNodeDTO.get().getNodeId().equals(o.getOutputNodeId())).findFirst();
        if (!segmentDTO.isPresent() || !nodeExpressionHashMap.containsKey(segmentDTO.get().getInputNodeId()))
            return false;
        String[] tmpArray = nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId()).split(";");
        if (tmpArray.length == 2) {
            String[] idArray = tmpArray[0].split(",");
            if (idArray.length == 3) {
                ControlCommandVO controlCommandVO = new ControlCommandVO();
                controlCommandVO.setStationId(Integer.parseInt(idArray[0]));
                controlCommandVO.setEquipmentId(Integer.parseInt(idArray[1]));
                controlCommandVO.setControlId(Integer.parseInt(idArray[2]));
                controlCommandVO.setSetValue(tmpArray[1]);
                controlCommandVO.setStartTime(new Date());
                linkExecutorService.sendControlCommand(controlCommandVO, DEFAULT_SEND_COMMAND_USER);
            }
            currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
            visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
            return true;
        }
        return false;
    }

    //条件运算
    private boolean calcIF(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        LinkSegmentDTO inputSegmentDTO = null;
        LinkSegmentDTO outputSegmentDTO = null;
        Integer rightNodeIdForTrue = null;
        Integer rightNodeIdForFalse = null;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<LinkSegmentDTO> tmpOptional = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (tmpOptional.isPresent()) {
                    inputSegmentDTO = tmpOptional.get();
                }
            } else {
                if (nodeDTO.getNodeIndex().equals(1)) {
                    rightNodeIdForTrue = nodeDTO.getNodeId();
                } else {
                    rightNodeIdForFalse = nodeDTO.getNodeId();
                }
            }
        }
        if (inputSegmentDTO == null || rightNodeIdForTrue == null || rightNodeIdForFalse == null)
            return false;
        if (TRUE.equalsIgnoreCase(nodeExpressionHashMap.get(inputSegmentDTO.getInputNodeId()))) {
            Integer finalRightNodeIdForTrue = rightNodeIdForTrue;
            Optional<LinkSegmentDTO> tmpOptional = linkSegmentDTOList.stream().filter(o -> finalRightNodeIdForTrue.equals(o.getInputNodeId())).findFirst();
            if (tmpOptional.isPresent()) {
                outputSegmentDTO = tmpOptional.get();
            }
            nodeExpressionHashMap.put(rightNodeIdForTrue, elementConfigDTO.getExpression());
        } else {
            Integer finalRightNodeIdForFalse = rightNodeIdForFalse;
            Optional<LinkSegmentDTO> tmpOptional = linkSegmentDTOList.stream().filter(o -> finalRightNodeIdForFalse.equals(o.getInputNodeId())).findFirst();
            if (tmpOptional.isPresent()) {
                outputSegmentDTO = tmpOptional.get();
            }
            nodeExpressionHashMap.put(rightNodeIdForFalse, elementConfigDTO.getExpression());
        }
        if (outputSegmentDTO != null) {
            LinkSegmentDTO finalOutputSegmentDTO = outputSegmentDTO;
            Optional<LinkElementConfigDTO> tmpElementConfigDTOOptional = linkElementConfigDTOList.stream().filter(o -> finalOutputSegmentDTO.getOutputLinkElementConfigId().equals(o.getLinkElementConfigId())).findFirst();
            tmpElementConfigDTOOptional.ifPresent(toCalcElementConfigDeque::addLast);
        }
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //对于整型常量和浮点型常量，将其常量值（存储在LinkElementConfigDTO的Expression中）以及输出节点的NodeId存入nodeExpressionHashMap中
    private boolean calcConstant(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                nodeExpressionHashMap.put(nodeDTO.getNodeId(), elementConfigDTO.getExpression());
                currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
                visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
                return true;
            }
        }
        return false;
    }

    //计算测点
    private boolean calcCorePoint(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                if ("signal".equalsIgnoreCase(nodeDTO.getNodeType())) {
                    getSignalValue(nodeDTO);
                } else if (EVENT.equalsIgnoreCase(nodeDTO.getNodeType())) {
                    nodeExpressionHashMap.put(nodeDTO.getNodeId(), "event:" + nodeDTO.getExpression());
                } else if ("control".equalsIgnoreCase(nodeDTO.getNodeType())) {
                    nodeExpressionHashMap.put(nodeDTO.getNodeId(), nodeDTO.getExpression() + ";" + nodeDTO.getNodeTag());
                } else {
                    return false;
                }
            }
        }
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    private void getSignalValue(LinkNodeDTO nodeDTO) {
        if (nodeDTO.getExpression() != null && !nodeDTO.getExpression().isEmpty()) {
            String[] idArray = nodeDTO.getExpression().split(",");
            RealTimeSignalItem realTimeSignal = linkExecutorService.getRealTimeSignalBySignalId(Integer.parseInt(idArray[1]), Integer.parseInt(idArray[2]));
            if (realTimeSignal != null) {
                nodeExpressionHashMap.put(nodeDTO.getNodeId(), realTimeSignal.getCurrentValue());
            }
        }
    }

    //计算设备
    private boolean calcDevice(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                if ("signal".equalsIgnoreCase(nodeDTO.getNodeType())) {
                    getSignalValue(nodeDTO);
                } else if (EVENT.equalsIgnoreCase(nodeDTO.getNodeType())) {
                    nodeExpressionHashMap.put(nodeDTO.getNodeId(), "equipment:" + nodeDTO.getExpression());
                } else if ("control".equalsIgnoreCase(nodeDTO.getNodeType())) {
                    nodeExpressionHashMap.put(nodeDTO.getNodeId(), nodeDTO.getExpression() + ";" + nodeDTO.getNodeTag());
                } else {
                    return false;
                }
            }
        }
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }

    //计算层级
    private boolean calcResourceStructure(LinkElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null)
            return false;
        for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                nodeExpressionHashMap.put(nodeDTO.getNodeId(), "resourcestructure:" + nodeDTO.getExpression());
            }
        }
        currentElementConfigIdDeque.addLast(elementConfigDTO.getLinkElementConfigId());
        visitedElementConfigIdHashMap.put(elementConfigDTO.getLinkElementConfigId(), true);
        return true;
    }


}
