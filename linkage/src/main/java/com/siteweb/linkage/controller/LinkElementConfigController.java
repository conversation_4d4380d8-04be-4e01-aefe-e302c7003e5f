package com.siteweb.linkage.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkLayoutDTO;
import com.siteweb.linkage.service.LinkElementConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "LinkElementConfigController", tags = {"LinkElementConfig操作接口"})
public class LinkElementConfigController {

    @Autowired
    LinkElementConfigService linkElementConfigService;

    @ApiOperation(value = "根据LinkConfigId查找LinkElementConfig实体")
    @GetMapping(value = "/linkelementconfigs", produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"linkConfigId"})
    public ResponseEntity<ResponseResult> getLinkElementConfigDTOsByLinkConfigId(@RequestParam Integer linkConfigId) {
        List<LinkElementConfigDTO> result = linkElementConfigService.findLinkElementConfigDTOsByLinkConfigId(linkConfigId);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }

    @ApiOperation(value = "根据LinkElementConfigId查找LinkElementConfig实体")
    @GetMapping("/linkelementconfigs/{linkElementConfigId}")
    public ResponseEntity<ResponseResult> getLinkElementConfigById(@PathVariable Integer linkElementConfigId) {
        LinkElementConfigDTO linkElementConfigDTO = linkElementConfigService.findLinkElementConfigDTOByLinkElementConfigId(linkElementConfigId);
        return Optional.ofNullable(linkElementConfigDTO)
                .map(result -> ResponseHelper.successful(linkElementConfigDTO, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "保存LinkLayoutDTO实体")
    @PostMapping(value = "/linkelementconfigs")
    public ResponseEntity<ResponseResult> batchSaveLinkElementConfig(@Valid @RequestBody LinkLayoutDTO linkLayoutDTO) {
        return ResponseHelper.successful(linkElementConfigService.batchSaveLinkElementConfig(linkLayoutDTO), HttpStatus.OK);
    }

    @ApiOperation(value = "按LinkElementConfigId删除LinkElementConfig实体")
    @DeleteMapping(value = "/linkelementconfigs/{linkElementConfigId}")
    public ResponseEntity<ResponseResult> deleteLinkElementConfig(@PathVariable Integer linkElementConfigId) {
        linkElementConfigService.deleteLinkElementConfigByLinkElementConfigId(linkElementConfigId);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
