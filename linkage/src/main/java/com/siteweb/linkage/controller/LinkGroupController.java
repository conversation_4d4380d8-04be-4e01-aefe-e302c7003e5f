package com.siteweb.linkage.controller;


import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.linkage.entity.LinkGroup;
import com.siteweb.linkage.service.LinkConfigService;
import com.siteweb.linkage.service.LinkGroupService;
import com.siteweb.linkage.vo.LinkGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "LinkGroupController", tags = {"LinkGroup操作接口"})
public class LinkGroupController {

    @Autowired
    LinkGroupService linkGroupService;

    @Autowired
    LinkConfigService linkConfigService;

    @ApiOperation(value = "查询所有LinkGroup实体")
    @GetMapping("/linkgroups")
    public ResponseEntity<ResponseResult> getAllLinkGroups() {
        return ResponseHelper.successful(linkGroupService.findAllGroups(), HttpStatus.OK);
    }

    @ApiOperation(value = "根据LinkGroupId查找单个LinkGroup实体")
    @GetMapping("/linkgroups/{linkGroupId}")
    public ResponseEntity<ResponseResult> getLinkGroupById(@PathVariable Integer linkGroupId) {
        LinkGroup linkGroup = linkGroupService.findByGroupId(linkGroupId);
        return Optional.ofNullable(linkGroup)
                .map(result -> ResponseHelper.successful(linkGroup, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "新增LinkGroup实体")
    @PostMapping(value = "/linkgroups")
    public ResponseEntity<ResponseResult> createLinkGroup(@Valid @RequestBody LinkGroupVO linkGroupVO) throws URISyntaxException {
        if (linkGroupVO.getGroupId() != null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkGroupId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkGroupService.createLinkGroup(linkGroupVO.build());
        if (result > 0) {
            return ResponseHelper.successful(linkGroupVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create linkGroup error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "修改LinkGroup实体")
    @PutMapping(value = "/linkgroups")
    public ResponseEntity<ResponseResult> updateLinkGroup(@Valid @RequestBody LinkGroupVO linkGroupVO) {
        if (linkGroupVO.getGroupId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkGroupId can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkGroupService.updateLinkGroup(linkGroupVO.build());
        if (result > 0) {
            return ResponseHelper.successful(linkGroupVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update linkGroup error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "根据linkGroupId删除LinkGroup实体")
    @DeleteMapping(value = "/linkgroups/{linkGroupId}")
    public ResponseEntity<ResponseResult> deleteLinkGroup(@PathVariable Integer linkGroupId) {
        List<LinkConfig> linkConfigs = linkConfigService.findAllByLinkGroupId(linkGroupId);
        if (!linkConfigs.isEmpty()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkGroup exists linkConfigs",
                    HttpStatus.BAD_REQUEST);
        }
        linkGroupService.deleteLinkGroup(linkGroupId);
        return ResponseHelper.successful();
    }
}
