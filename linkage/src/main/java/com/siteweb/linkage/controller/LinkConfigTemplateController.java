package com.siteweb.linkage.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.linkage.entity.LinkConfigTemplate;
import com.siteweb.linkage.service.LinkConfigTemplateService;
import com.siteweb.linkage.vo.LinkConfigTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "LinkConfigTemplateController", tags = {"LinkConfigTemplate操作接口"})
public class LinkConfigTemplateController {

    @Autowired
    LinkConfigTemplateService linkConfigTemplateService;

    @ApiOperation(value = "查找所有LinkConfigTemplate实体")
    @GetMapping("/linkconfigtemplates")
    public ResponseEntity<ResponseResult> getAllLinkConfigTemplates() {
        List<LinkConfigTemplate> result = linkConfigTemplateService.findAllLinkConfigTemplates();
        return ResponseHelper.successful(result, HttpStatus.OK);
    }

    @ApiOperation(value = "根据LinkConfigTemplateId查找LinkConfigTemplate实体")
    @GetMapping("/linkconfigtemplates/{linkConfigTemplateId}")
    public ResponseEntity<ResponseResult> getLinkElementById(@PathVariable Integer linkConfigTemplateId) {
        LinkConfigTemplate linkConfigTemplate = linkConfigTemplateService.findByTemplateId(linkConfigTemplateId);
        return Optional.ofNullable(linkConfigTemplate)
                .map(result -> ResponseHelper.successful(linkConfigTemplate, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "新增LinkConfigTemplate实体")
    @PostMapping(value = "/linkconfigtemplates")
    public ResponseEntity<ResponseResult> createLinkConfigTemplate(@Valid @RequestBody LinkConfigTemplateVO linkConfigTemplateVO) {
        if (linkConfigTemplateVO.getLinkConfigTemplateId() != null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkConfigTemplateId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkConfigTemplateService.createLinkConfigTemplate(linkConfigTemplateVO.build());
        if (result > 0) {
            return ResponseHelper.successful(linkConfigTemplateVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create linkConfigTemplate error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "修改LinkConfigTemplate实体")
    @PutMapping(value = "/linkconfigtemplates")
    public ResponseEntity<ResponseResult> updateLinkConfigTemplate(@Valid @RequestBody LinkConfigTemplateVO linkConfigTemplateVO) {
        if (linkConfigTemplateVO.getLinkConfigTemplateId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkConfigTemplateId can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkConfigTemplateService.updateLinkConfigTemplate(linkConfigTemplateVO.build());
        if (result > 0) {
            return ResponseHelper.successful(linkConfigTemplateVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update linkConfigTemplate error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "根据LinkConfigTemplateId删除LinkConfigTemplate实体")
    @DeleteMapping(value = "/linkconfigtemplates/{linkConfigTemplateId}")
    public ResponseEntity<ResponseResult> deleteLinkConfigTemplate(@PathVariable Integer linkConfigTemplateId) {
        linkConfigTemplateService.deleteLinkConfigTemplate(linkConfigTemplateId);
        return ResponseHelper.successful();
    }
}
