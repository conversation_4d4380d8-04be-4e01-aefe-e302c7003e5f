package com.siteweb.linkage.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.linkage.entity.LinkElement;
import com.siteweb.linkage.service.LinkElementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "LinkElementController", tags = {"LinkElement操作接口"})
public class LinkElementController {

    @Autowired
    LinkElementService linkElementService;

    @ApiOperation(value = "根据Id查找LinkElement实体")
    @GetMapping("/linkelements/{id}")
    public ResponseEntity<ResponseResult> getLinkElementById(@PathVariable Integer id) {
        LinkElement linkElement = linkElementService.findById(id);
        return Optional.ofNullable(linkElement)
                .map(result -> ResponseHelper.successful(linkElement, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "查找所有LinkElement实体")
    @GetMapping("/linkelements")
    public ResponseEntity<ResponseResult> getAllLinkElements() {
        return ResponseHelper.successful(linkElementService.findVisibleElements(), HttpStatus.OK);
    }
}
