package com.siteweb.linkage.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.linkage.service.LinkConfigService;
import com.siteweb.linkage.vo.LinkConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "LinkConfigController", tags = {"LinkConfig操作接口"})
public class LinkConfigController {

    @Autowired
    LinkConfigService linkConfigService;

    @ApiOperation(value = "根据LinkGroupId查找LinkConfig实体")
    @GetMapping(value = "/linkconfigs", produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"linkGroupId"})
    public ResponseEntity<ResponseResult> getLinkConfigsByGroupId(@RequestParam Integer linkGroupId) {
        List<LinkConfig> result = linkConfigService.findAllByLinkGroupId(linkGroupId);
        return ResponseHelper.successful(result, HttpStatus.OK);
    }

    @ApiOperation(value = "根据LinkConfigId查找单个LinkConfig实体")
    @GetMapping("/linkconfigs/{linkConfigId}")
    public ResponseEntity<ResponseResult> getLinkConfigById(@PathVariable Integer linkConfigId) {
        LinkConfig linkConfig = linkConfigService.findByLinkConfigId(linkConfigId);
        return Optional.ofNullable(linkConfig)
                .map(result -> ResponseHelper.successful(linkConfig, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "新增LinkConfig实体")
    @PostMapping(value = "/linkconfigs")
    public ResponseEntity<ResponseResult> createLinkConfig(@Valid @RequestBody LinkConfigVO linkConfigVO) throws URISyntaxException {
        if (linkConfigVO.getLinkConfigId() != null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkConfigId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkConfigService.createLinkConfig(linkConfigVO.build());
        if (result > 0) {
            return ResponseHelper.successful(linkConfigVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create linkConfig error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "修改LinkConfig实体")
    @PutMapping(value = "/linkconfigs")
    public ResponseEntity<ResponseResult> updateLinkConfig(@Valid @RequestBody LinkConfigVO linkConfigVO) {
        if (linkConfigVO.getLinkConfigId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "linkConfigId can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkConfigService.updateLinkConfig(linkConfigVO.build());
        if (result > 0) {
            return ResponseHelper.successful(linkConfigVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update linkConfig error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "按LinkConfigId删除LinkConfig实体")
    @DeleteMapping(value = "/linkconfigs/{linkConfigId}")
    public ResponseEntity<ResponseResult> deleteLinkConfig(@PathVariable Integer linkConfigId) {
        LinkConfig linkConfig = linkConfigService.findByLinkConfigId(linkConfigId);
        if (linkConfig == null || Boolean.TRUE.equals(linkConfig.getUsedStatus())) {
            //已启用的联动控制不允许删除
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "linkConfig can not be delete",
                    HttpStatus.BAD_REQUEST);
        }
        int result = linkConfigService.deleteLinkConfigById(linkConfigId);
        if (result < 0) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
