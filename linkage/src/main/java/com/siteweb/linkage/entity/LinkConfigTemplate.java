package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("linkconfigtemplate")
public class LinkConfigTemplate {

    @TableId(value = "LinkConfigTemplateId", type = IdType.AUTO)
    private Integer linkConfigTemplateId;

    private String templateName;

    private String content;

    private String description;
}
