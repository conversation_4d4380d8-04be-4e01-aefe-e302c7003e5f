package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("linkelementconfig")
public class LinkElementConfig {

    @TableId(value = "LinkElementConfigId", type = IdType.AUTO)
    private Integer linkElementConfigId;

    private Integer linkConfigId;

    private Integer elementId;

    private String expression;

    private String extendField1;

    private String extendField2;

    private String extendField3;
}
