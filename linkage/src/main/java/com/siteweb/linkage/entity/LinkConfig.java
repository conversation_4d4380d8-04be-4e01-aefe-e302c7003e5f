package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("linkconfig")
public class LinkConfig {

    @TableId(value = "LinkConfigId", type = IdType.AUTO)
    private Integer linkConfigId;

    private String configName;

    private Boolean usedStatus;

    private Integer linkGroupId;

    private Integer linkTriggerType;

    private String cron;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date startTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    private String layout;

    private String description;

    private Date updateTime;
}
