package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("linknode")
public class LinkNode {

    @TableId(value = "NodeId", type = IdType.AUTO)
    private Integer nodeId;

    private Integer linkElementConfigId;

    private String nodeDirection;

    private String nodeType;

    private Integer nodeIndex;

    private String nodeTag;

    private String expression;
}
