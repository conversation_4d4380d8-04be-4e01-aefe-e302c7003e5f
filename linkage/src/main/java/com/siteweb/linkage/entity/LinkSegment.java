package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("linksegment")
public class LinkSegment {

    @TableId(value = "SegmentId", type = IdType.AUTO)
    private Integer segmentId;

    private Integer inputLinkElementConfigId;

    private Integer inputNodeId;

    private Integer outputLinkElementConfigId;

    private Integer outputNodeId;
}
