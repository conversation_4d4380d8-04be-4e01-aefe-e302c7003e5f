package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("linkelement")
public class LinkElement {

    @TableId(value = "ElementId", type = IdType.AUTO)
    private Integer elementId;

    private String elementName;

    private String elementType;

    private Integer inputNodesCount;

    private Integer outputNodesCount;

    private String icon;

    private String expression;

    private Boolean visible;

    private Integer sortIndex;

    private String description;
}
