package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("linkinstance")
public class LinkInstance {

    @TableId(value = "LinkInstanceId", type = IdType.AUTO)
    private Integer linkInstanceId;

    private Integer linkConfigId;

    private Date updateTime;

    private Integer status;

    private String statusResult;

    private String description;
}
