package com.siteweb.linkage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@TableName("linkgroup")
public class LinkGroup {

    @TableId(value = "GroupId", type = IdType.AUTO)
    private Integer groupId;

    private String groupName;

    private String description;
}
