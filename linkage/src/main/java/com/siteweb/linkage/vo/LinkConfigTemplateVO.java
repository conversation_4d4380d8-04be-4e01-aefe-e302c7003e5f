package com.siteweb.linkage.vo;

import com.siteweb.linkage.entity.LinkConfigTemplate;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description LinkConfigTemplateVO
 * @createTime 2022-05-26 15:48:29
 */
@Data
@NoArgsConstructor
public class LinkConfigTemplateVO {

    private Integer linkConfigTemplateId;

    private String templateName;

    private String content;

    private String description;

    public LinkConfigTemplate build() {
        LinkConfigTemplate linkConfigTemplate = new LinkConfigTemplate();
        BeanUtils.copyProperties(this, linkConfigTemplate);
        return linkConfigTemplate;
    }
}
