package com.siteweb.linkage.vo;

import com.siteweb.linkage.entity.LinkConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR> z<PERSON>
 * @description LinkConfigVO
 * @createTime 2022-05-26 15:00:42
 */
@Data
@NoArgsConstructor
public class LinkConfigVO {

    private Integer linkConfigId;

    private String configName;

    private Boolean usedStatus;

    private Integer linkGroupId;

    private Integer linkTriggerType;

    private String cron;

    private Date startTime;

    private Date endTime;

    private String layout;

    private String description;


    public LinkConfig build() {
        LinkConfig linkConfig = new LinkConfig();
        BeanUtils.copyProperties(this, linkConfig);
        return linkConfig;
    }
}
