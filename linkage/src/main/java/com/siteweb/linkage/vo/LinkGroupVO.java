package com.siteweb.linkage.vo;

import com.siteweb.linkage.entity.LinkGroup;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description LinkGroupVO
 * @createTime 2022-05-26 15:21:17
 */
@Data
@NoArgsConstructor
public class LinkGroupVO {

    private Integer groupId;

    private String groupName;

    private String description;

    public LinkGroup build() {
        LinkGroup linkGroup = new LinkGroup();
        BeanUtils.copyProperties(this, linkGroup);
        return linkGroup;
    }
}
