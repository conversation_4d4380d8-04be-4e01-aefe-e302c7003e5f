package com.siteweb.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "cqctcc-sso")
@Data
public class CqctccSSOProperties {

    private Boolean enable;
    /**
     * 重庆电信Ftp服务器Ip
     */
    private String ftpSeverIp;
    /**
     * 重庆电信Ftp服务器端口
     */
    private String ftpServerPort;
    /**
     * 重庆电信Ftp服务器用户名
     */
    private String ftpUserName;
    /**
     * 重庆电信Ftp服务器密码
     */
    private String ftpPassword;
    /**
     * 重庆电信Ftp重试次数
     */
    private Integer ftpRetryTimes;
    /**
     * 重庆电信Ftp文件路径
     */
    private String ftpFilePath;
    /**
     * 重庆电信Ftp本地文件保留天数
     */
    private Integer ftpFileKeepDays;
    /**
     * Ftp内容 DES密钥
     */
    private String ftpDesKey;
    /**
     * 重庆电信动环系统编码
     */
    private String sysCode;
    /**
     * 信息系统应用ID
     */
    private String appId;
    /**
     * 信息系统应用KEY
     */
    private String appKey;
    /**
     * 人主接口基础url
     */
    private String apiBaseUrl;
    /**
     * 5.1.5. 人员主账号信息查询接口
     */
    private String accountInfoUrl;
    /**
     * 人员主账号信息查询接口 DES密钥
     */
    private String accountInfoDesKey;
    /**
     * 统一凭证校验接口
     */
    private String tokenValidateUrl;
    /**
     * 统一凭证校验接口 DES密钥
     */
    private String tokenValidateDesKey;
    /**
     * 主账号目标系统登录权限控制接口
     */
    private String loginPrivUrl;
    /**
     * 主账号目标系统登录权限控制接口 DES密钥
     */
    private String loginPrivDesKey;
    /**
     * 统一登录认证接口
     */
    private String loginValidateUrl;
    /**
     * 统一登录认证接口 DES密钥
     */
    private String loginValidateDesKey;

}
