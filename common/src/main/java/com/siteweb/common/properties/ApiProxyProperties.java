package com.siteweb.common.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "api-proxy")
@Data
public class ApiProxyProperties {
    /**
     * 建立连接超时
     * 单位 秒
     */
    private int connectTimeout = 1;
    /**
     * 连接池获取连接请求超时
     * 单位 秒
     */
    private int connectionRequestTimeout = 1;
    /**
     * 读取超时时间
     * 单位 秒
     */
    private int readTimeout = 120;
    /**
     * apiProxy接口慢接口记录最大时长
     * 单位 秒
     */
    private int timeoutLog = 1;
}
