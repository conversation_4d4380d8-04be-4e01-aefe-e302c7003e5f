package com.siteweb.common.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "feature-enable")
@Data
public class FeatureEnableProperties {
    private Boolean redisPublishSubscription;
    /**
     * 告警去除存储过程
     */
    private Boolean eventRemoveProc;
    /**
     * 服务器部署ip
     */
    private String serverIp;
}
