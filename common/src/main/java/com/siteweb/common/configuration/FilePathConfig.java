package com.siteweb.common.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class FilePathConfig implements WebMvcConfigurer {
    @Autowired
    private Environment env;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/files/**").addResourceLocations("file:" + env.getProperty("fileserver.rootPath") + "/");
        registry.addResourceHandler("/microapps/**").addResourceLocations("file:microapps/");
        registry.addResourceHandler("/hotpush/**").addResourceLocations("file:hotpush/");
        registry.addResourceHandler("/mapfiles/**").addResourceLocations("file:mapfiles/");
    }
}
