package com.siteweb.common.configuration;

import org.apache.catalina.connector.Connector;
import org.apache.coyote.ProtocolHandler;
import org.apache.coyote.http11.AbstractHttp11Protocol;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.Compression;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

@Configuration
public class TomCatConfig {
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
        return (TomcatServletWebServerFactory factory) -> {
            // also listen on http
            final Connector connector = new Connector();
            connector.setPort(httpPort);
            ProtocolHandler handler = connector.getProtocolHandler();
            if (handler instanceof AbstractHttp11Protocol) {
                customize((AbstractHttp11Protocol<?>) handler,factory.getCompression());
            }
            factory.addAdditionalTomcatConnectors(connector);
        };
    }

    private void customize(AbstractHttp11Protocol<?> protocol,Compression compression) {
        protocol.setCompression("on");
        protocol.setCompressionMinSize((int)compression.getMinResponseSize().toBytes());
        protocol.setCompressibleMimeType(StringUtils.arrayToCommaDelimitedString(compression.getMimeTypes()));
        if (compression.getExcludedUserAgents() != null) {
            protocol.setNoCompressionUserAgents(StringUtils.arrayToCommaDelimitedString(compression.getExcludedUserAgents()));
        }
    }

    @Value("${server.http.port}")
    private int httpPort;
}