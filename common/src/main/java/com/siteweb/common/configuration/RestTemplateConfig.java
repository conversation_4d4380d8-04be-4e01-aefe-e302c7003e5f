package com.siteweb.common.configuration;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.properties.ApiProxyProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.time.Duration;

@Configuration
@Slf4j
public class RestTemplateConfig {
    @Value("${server.ssl.enable}")
    Boolean enableSSL;
    @Value("${server.ssl.key-store-type}")
    String clientKeyType;
    @Value("${server.ssl.key-store}")
    String clientPath;
    @Value("${server.ssl.key-store-password}")
    String clientPass;
    @Value("${server.ssl.trust-store-type}")
    String trustKeyType;
    @Value("${server.ssl.trust-store}")
    String trustPath;
    @Value("${server.ssl.trust-store-password}")
    String trustPass;

    /*
    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory factory){
        RestTemplate restTemplate = new RestTemplate(
                new HttpComponentsClientHttpRequestFactory()); // 使用HttpClient，支持GZIP
        restTemplate.getMessageConverters().set(1,
                new StringHttpMessageConverter(StandardCharsets.UTF_8)); // 支持中文编码
        return restTemplate;
    } */


    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = null;
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout((int) Duration.ofSeconds(5).toMillis());
        requestFactory.setConnectionRequestTimeout((int)(Duration.ofSeconds(10).toMillis()));
        requestFactory.setReadTimeout((int)(Duration.ofSeconds(30).toMillis()));
        if (enableSSL) {
            try {
                // 客户端证书类型
                KeyStore clientStore = KeyStore.getInstance(clientKeyType);
                // 加载客户端证书，即自己的私钥

                InputStream keyStream = this.getClass().getClassLoader().getResourceAsStream(clientPath);
                clientStore.load(keyStream, clientPass.toCharArray());
                // 创建密钥管理工厂实例
                KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
                // 初始化客户端密钥库
                keyManagerFactory.init(clientStore, clientPass.toCharArray());
                KeyManager[] keyManagers = keyManagerFactory.getKeyManagers();

                // 创建信任库管理工厂实例
                TrustManagerFactory trustManagerFactory = TrustManagerFactory
                        .getInstance(TrustManagerFactory.getDefaultAlgorithm());
                KeyStore trustStore = KeyStore.getInstance(trustKeyType);
                InputStream trustStream = this.getClass().getClassLoader().getResourceAsStream(trustPath);
                trustStore.load(trustStream, trustPass.toCharArray());

                // 初始化信任库
                trustManagerFactory.init(trustStore);
                TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
                // 建立TLS连接
                SSLContext sslContext = SSLContext.getInstance("TLS");
                // 初始化SSLContext
                sslContext.init(keyManagers, trustManagers, new SecureRandom());
                // INSTANCE 忽略域名检查
                SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
                CloseableHttpClient httpclient = HttpClients
                        .custom()
                        .setSSLSocketFactory(sslConnectionSocketFactory)
                        .setSSLHostnameVerifier(new NoopHostnameVerifier())
                        .build();
                requestFactory.setHttpClient(httpclient);
                restTemplate = new RestTemplate(requestFactory);
            } catch (KeyManagementException | FileNotFoundException | NoSuchAlgorithmException e) {
                log.error("restTemplateSSL", e);
            } catch (KeyStoreException | CertificateException | UnrecoverableKeyException | IOException e) {
                log.error("restTemplateSSL", e);
            }
        }
        if (ObjectUtil.isNull(restTemplate)) {
            restTemplate = new RestTemplate(requestFactory); // 使用HttpClient，支持GZIP
            restTemplate.getMessageConverters().set(1,
                    new StringHttpMessageConverter(StandardCharsets.UTF_8)); // 支持中文编码
            return restTemplate;
        }
        return restTemplate;
    }
    @Bean
    public RestTemplate restTemplateSSL() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        TrustStrategy acceptingTrustStrategy = ((x509Certificates, authType) -> true);
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
            SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
            HttpClientBuilder httpClientBuilder = HttpClients.custom();
            httpClientBuilder.setSSLSocketFactory(connectionSocketFactory);
            CloseableHttpClient httpClient = httpClientBuilder.build();
            factory.setHttpClient(httpClient);
            factory.setConnectTimeout((int) Duration.ofSeconds(5).toMillis());
            factory.setConnectionRequestTimeout((int)(Duration.ofSeconds(10).toMillis()));
            factory.setReadTimeout((int)(Duration.ofSeconds(30).toMillis()));
        } catch (NoSuchAlgorithmException e) {
            log.error("restTemplateSSL", e);
        } catch (KeyManagementException e) {
            log.error("restTemplateSSL", e);
        } catch (KeyStoreException e) {
            log.error("restTemplateSSL", e);
        }
        RestTemplate restTemplate = new RestTemplate(factory); // 使用HttpClient，支持GZIP
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8)); // 支持中文编码
        return restTemplate;
    }

    @Bean
    public RestTemplate proxyRestTemplateSSL(ApiProxyProperties apiProxyProperties) {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        TrustStrategy acceptingTrustStrategy = ((x509Certificates, authType) -> true);
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
            SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
            HttpClientBuilder httpClientBuilder = HttpClients.custom();
            httpClientBuilder.setSSLSocketFactory(connectionSocketFactory);
            CloseableHttpClient httpClient = httpClientBuilder.build();
            factory.setHttpClient(httpClient);
            factory.setConnectTimeout((int) Duration.ofSeconds(apiProxyProperties.getConnectTimeout()).toMillis());
            factory.setConnectionRequestTimeout((int)(Duration.ofSeconds(apiProxyProperties.getConnectionRequestTimeout()).toMillis()));
            factory.setReadTimeout((int)(Duration.ofSeconds(apiProxyProperties.getReadTimeout()).toMillis()));
        } catch (NoSuchAlgorithmException e) {
            log.error("proxyRestTemplateSSL", e);
        } catch (KeyManagementException e) {
            log.error("proxyRestTemplateSSL", e);
        } catch (KeyStoreException e) {
            log.error("proxyRestTemplateSSL", e);
        }
        RestTemplate restTemplate = new RestTemplate(factory); // 使用HttpClient，支持GZIP
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8)); // 支持中文编码
        restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
        return restTemplate;
    }

    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setReadTimeout(5000);//ms
        factory.setConnectTimeout(15000);//ms
        return factory;
    }
}
