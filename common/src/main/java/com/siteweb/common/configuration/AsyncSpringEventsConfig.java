package com.siteweb.common.configuration;

import com.siteweb.common.threadpool.ThreadPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.context.event.SimpleApplicationEventMulticaster;

/**
 * <AUTHOR> zhou
 * @description AsyncSpringEventsConfig
 * @createTime 2022-02-25 08:46:34
 */
@Configuration
public class AsyncSpringEventsConfig {

    @Bean(name = "applicationEventMulticaster")
    public ApplicationEventMulticaster simpleApplicationEventMulticaster(@Autowired ThreadPoolConfig threadPoolConfig) {
        SimpleApplicationEventMulticaster eventMulticaster = new SimpleApplicationEventMulticaster();
        eventMulticaster.setTaskExecutor(threadPoolConfig.getCustomThreadPool());
        return eventMulticaster;
    }
}
