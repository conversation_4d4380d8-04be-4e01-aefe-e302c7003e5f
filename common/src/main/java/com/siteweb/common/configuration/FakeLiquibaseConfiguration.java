package com.siteweb.common.configuration;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> zhou
 * @description FakeLiquibaseConfiguration
 * @createTime 2022-07-16 14:20:37
 */
@Configuration(value = "liquibase")
@ConditionalOnProperty(
        name = "spring.liquibase.enabled",
        havingValue = "false"
)
public class FakeLiquibaseConfiguration {
    //该类是为了解决在spring.liquibase.enabled=false的情况下，启动程序时@DependsOn("liquibase")报错的问题
}
