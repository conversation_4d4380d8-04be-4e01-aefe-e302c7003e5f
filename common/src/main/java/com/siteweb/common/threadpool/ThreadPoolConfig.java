package com.siteweb.common.threadpool;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.*;

@Configuration
public class ThreadPoolConfig {

    @Bean
    @Primary
    public ExecutorService getCustomThreadPool() {
        return new ThreadPoolExecutor(16, 16,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(),
                r -> new Thread(r, "cus_p_" + r.hashCode())
        );
    }

    @Bean(name = "longTimeThreadPool")
    public ExecutorService getLongTimeThreadPool() {
        return new ThreadPoolExecutor(16, 16,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(),
                r -> new Thread(r, "lt_p_" + r.hashCode())
        );
    }
}
