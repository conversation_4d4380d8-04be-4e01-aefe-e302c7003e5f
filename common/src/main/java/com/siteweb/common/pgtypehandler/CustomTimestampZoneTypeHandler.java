package com.siteweb.common.pgtypehandler;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

/**
 * pg数据库的timestamptz对应Java的OffsetDateTime，现在把timestamptz转成LocalDateTime
 */
@Slf4j
@MappedTypes(value = LocalDateTime.class)
@MappedJdbcTypes(value = {JdbcType.TIMESTAMP_WITH_TIMEZONE, JdbcType.TIMESTAMP})
public class CustomTimestampZoneTypeHandler extends BaseTypeHandler<LocalDateTime> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        ps.setObject(i, parameter);
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return convert(rs.getObject(columnName));
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return convert(rs.getObject(columnIndex));
    }

    @Override
    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return convert(cs.getObject(columnIndex));
    }

    private LocalDateTime convert(Object object) {
        if (object instanceof OffsetDateTime offsetDateTime) {
            return offsetDateTime.toLocalDateTime();
        } else if (object instanceof LocalDateTime localDateTime) {
            return localDateTime;
        } else {
            return Convert.toLocalDateTime(object);
        }
    }
}

