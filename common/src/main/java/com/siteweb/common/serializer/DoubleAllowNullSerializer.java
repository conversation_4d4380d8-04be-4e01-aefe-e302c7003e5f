package com.siteweb.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR> z<PERSON>
 * @description DoubleAllowNullSerializer
 * @createTime 2022-07-26 09:55:12
 */
public class DoubleAllowNullSerializer extends JsonSerializer<Double> {
    @Override
    public void serialize(Double doubleValue, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (doubleValue != null) {
            String strResult = String.format("%.2f", doubleValue);
            jsonGenerator.writeNumber(Double.parseDouble(strResult));
        }
    }
}
