package com.siteweb.common.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.Objects;

/**
 * 将接收的参数转换成大写
 * <AUTHOR>
 * @date 2023/12/19
 */
public class UppercaseDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String value = jsonParser.getValueAsString();
        if (Objects.isNull(value)) {
            return null;
        }
        return value.toUpperCase();
    }
}
