package com.siteweb.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR> z<PERSON>
 * @description DoubleNonNullSerializer
 * @createTime 2022-07-26 09:50:32
 */
public class DoubleNonNullSerializer extends JsonSerializer<Double> {
    @Override
    public void serialize(Double doubleValue, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (doubleValue != null) {
            String strResult = String.format("%.2f", doubleValue);
            jsonGenerator.writeNumber(Double.parseDouble(strResult));
        } else {
            jsonGenerator.writeNumber(0);
        }
    }
}
