package com.siteweb.common.websocket.dto;

import com.siteweb.common.websocket.enums.WebSocketBusinessTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 通用的webSocket业务发送实体
 * <AUTHOR>
 * @date 2024/03/01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketCommonMessageBody<T> {
    /**
     * 业务类型
     */
    private WebSocketBusinessTypeEnum webSocketBusinessType;
    /**
     * 需要发送的消息
     */
    private T msg;

    public static <T> WebSocketCommonMessageBody<T> of(WebSocketBusinessTypeEnum webSocketBusinessTypeEnum, T msg) {
        return new WebSocketCommonMessageBody<>(webSocketBusinessTypeEnum, msg);
    }

    /**
     * 序列化成json时返回给前端应为一个int数字
     * @return {@link Integer}
     */
    public Integer getWebSocketBusinessType() {
        return this.webSocketBusinessType.getType();
    }
}
