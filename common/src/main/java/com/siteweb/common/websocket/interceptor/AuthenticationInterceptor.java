/*
 * *
 *  * blog.coder4j.cn
 *  * Copyright (C) 2016-2019 All Rights Reserved.
 *
 */
package com.siteweb.common.websocket.interceptor;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.nio.charset.StandardCharsets;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/03/13
 */
@Component
@Slf4j
public class AuthenticationInterceptor implements HandshakeInterceptor {

    public static final String USER_ID = "userid";
    public static final String SESSION_ID = "sessionid";

    /**
     * 握手前
     *
     * @param request
     * @param response
     * @param wsHandler
     * @param attributes
     * @return
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) {
        // 获得请求参数
        Map<String, String> paramMap = HttpUtil.decodeParamMap(request.getURI().getQuery(), StandardCharsets.UTF_8);
        String userId = paramMap.get(USER_ID);
        String sessionId = paramMap.get(SESSION_ID);
        if (CharSequenceUtil.isBlank(userId) || CharSequenceUtil.isBlank(sessionId)) {
            log.error("未发现userId或sessionId,连接WebSocket失败{}", request.getURI());
            return false;
        }
        // 将sessionId与userId放入属性域
        attributes.put("sessionId", sessionId);
        attributes.put("userId", userId);
        return true;
    }

    /**
     * 握手后
     *
     * @param request
     * @param response
     * @param wsHandler
     * @param exception
     */
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {
    }
}
