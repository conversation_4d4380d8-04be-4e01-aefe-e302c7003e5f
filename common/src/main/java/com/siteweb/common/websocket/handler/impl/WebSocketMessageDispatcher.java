package com.siteweb.common.websocket.handler.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.websocket.handler.WebSocketBusinessHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebSocketMessageDispatcher {
    private final ConcurrentHashMap<String, WebSocketBusinessHandler> handlerMap = new ConcurrentHashMap<>();
    @Autowired
    GraphicPageEditUsersWebSocketHandler graphicPageEditUsersWebSocketHandler;

    @Autowired
    public WebSocketMessageDispatcher(List<WebSocketBusinessHandler> handlers) {
        // 自动把所有实现了 MessageHandler 接口的类注册进来
        for (WebSocketBusinessHandler handler : handlers) {
            handlerMap.put(handler.getType(), handler);
        }
    }

    public void dispatchMessage(WebSocketSession session, TextMessage message) {
        JSONObject payload = JSONUtil.parseObj(message.getPayload());
        String type = payload.getStr("type");
        if (graphicPageEditUsersWebSocketHandler.getType().contains(type)) {
            type = graphicPageEditUsersWebSocketHandler.getType();
        }
        WebSocketBusinessHandler handler = handlerMap.get(type);
        if (handler != null && handler.supports(session)) {
            handler.handleMessage(session, message);
        } else {
            // 没找到对应的type
            log.info("未找到处理器 type=" + type);
        }
    }

    public void dispatchConnectionClosed(WebSocketSession session) {
        for (WebSocketBusinessHandler handler : handlerMap.values()) {
            if (handler.supports(session)) {
                handler.handleConnectionClosed(session);
                return;
            }
        }
    }
}