package com.siteweb.common.websocket.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;


/**
 * <AUTHOR>
 * @date 2023/03/13
 */
@Slf4j
public abstract class WebSocketSessionManager {
    /**
     * 保存session状态池
     *
     * @return {@link ConcurrentHashMap}<{@link String}, {@link WebSocketSession}>
     */
    public abstract ConcurrentMap<String, WebSocketSession> getSessionStatePool();
    /**
     * 添加 session
     *
     * @param uniqueId 会话id
     */
    public void addSession(String uniqueId, WebSocketSession session) {
        // 添加 session
        getSessionStatePool().put(uniqueId, session);
    }


    /**
     * 删除并同步关闭连接
     *
     * @param uniqueId 会话id
     */
    public void removeAndClose(String uniqueId) throws IOException {
        WebSocketSession session = getSessionStatePool().remove(uniqueId);
        if (session != null && session.isOpen()) {
            // 关闭连接
            log.info("uniqueId：{}主动关闭WebSocket连接", uniqueId);
            session.close();
        }
    }
    /**
     * 获得 session
     *
     * @param uniqueId
     * @return
     */
    public WebSocketSession getSession(String uniqueId) {
        // 获得 session
        return getSessionStatePool().get(uniqueId);
    }

    /**
     * 发送文本消息
     * @param uniqueId 唯一id 由用户id + sessionId组成
     * @param msg 需要发送的文本消息
     */
    public void sendTextMsg(String uniqueId, String msg) throws IOException {
        WebSocketSession webSocketSession = getSessionStatePool().get(uniqueId);
        if (Objects.isNull(webSocketSession) || !webSocketSession.isOpen()) {
            log.error("uniqueId:{},消息内容:{},发送webSocket消息失败，会话不存在或者会话已经关闭", uniqueId, msg);
            return;
        }
        webSocketSession.sendMessage(new TextMessage(msg));
        log.info("uniqueId：{},发送消息成功,消息内容：{}", uniqueId, msg);
    }

    /**
     * 发送文本消息
     * @param userId 发送消息给指定的用户
     * @param msg 需要发送的文本消息
     */
    public void sendUserTextMsg(Integer userId, String msg) throws IOException {
        ConcurrentMap<String, WebSocketSession> sessionStatePool = getSessionStatePool();
        for (Map.Entry<String, WebSocketSession> stringWebSocketSessionEntry : sessionStatePool.entrySet()) {
            String[] uniqueIdArray = stringWebSocketSessionEntry.getKey().split(":");
            if (Objects.equals(Integer.valueOf(uniqueIdArray[0]), userId) && stringWebSocketSessionEntry.getValue().isOpen()) {
                stringWebSocketSessionEntry.getValue().sendMessage(new TextMessage(msg));
            }
        }
    }
    /**
     * 获取所有WebSocket会话
     * @return {@link Collection}<{@link WebSocketSession}>
     */
    public Collection<WebSocketSession> getAllSession() {
        return getSessionStatePool().values();
    }
}
