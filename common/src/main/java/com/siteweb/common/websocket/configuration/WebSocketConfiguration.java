package com.siteweb.common.websocket.configuration;

import com.siteweb.common.websocket.handler.impl.CommonMsgWebSocketHandler;
import com.siteweb.common.websocket.interceptor.AuthenticationInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * <AUTHOR> zhou
 * @description WebSocketConfiguration
 * @createTime 2022-05-26 09:14:34
 */
@Configuration
@EnableWebSocket
public class WebSocketConfiguration implements WebSocketConfigurer {
    @Autowired
    private AuthenticationInterceptor authInterceptor;
    @Autowired
    private CommonMsgWebSocketHandler commonMsgWebSocketHandler;
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(commonMsgWebSocketHandler, "/websocket/common")
                .addInterceptors(authInterceptor)
                .setAllowedOriginPatterns("*");
    }
}
