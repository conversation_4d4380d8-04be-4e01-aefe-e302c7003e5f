package com.siteweb.common.websocket.handler.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.websocket.dto.WebSocketCommonMessageBody;
import com.siteweb.common.websocket.enums.WebSocketBusinessTypeEnum;
import com.siteweb.common.websocket.handler.WebSocketBusinessHandler;
import com.siteweb.common.websocket.handler.WebSocketHandlerAdapter;
import com.siteweb.common.websocket.manager.impl.CommonMsgWebSocketManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class GraphicPageEditUsersWebSocketHandler implements WebSocketBusinessHandler {
    private static final String GRAPHIC_PAGE_EDIT_USERS_KEY = "graphicPageEditingUsers";

    @Autowired
    CommonMsgWebSocketManager commonMsgWebSocketManager;
    @Autowired
    @Lazy
    WebSocketHandlerAdapter webSocketHandlerAdapter;

    @Autowired
    RedisTemplate<String, String> redisTemplate;

    @Autowired
    NamedParameterJdbcTemplate jdbcTemplate;

    @Override
    public String getType() {
        return "enterEdit_leaveEdit";
    }

    @Override
    public boolean supports(WebSocketSession session) {
        return getGraphicPageEditUsersEnable();
    }

    //收到消息
    @Override
    public void handleMessage(WebSocketSession session, TextMessage message) {
        JSONObject json = JSONUtil.parseObj(message.getPayload());
        String type = json.getStr("type");

        switch (type) {
            case "enterEdit":
                String enterPageId = json.getStr("pageId");
                addEditFromRedis(GRAPHIC_PAGE_EDIT_USERS_KEY, enterPageId, webSocketHandlerAdapter.getUniqueId(session));
                broadcastEditors(session);
                break;
            case "leaveEdit":
                String leavePageId = json.getStr("pageId");
                removeEditFromRedis(GRAPHIC_PAGE_EDIT_USERS_KEY, leavePageId, webSocketHandlerAdapter.getUniqueId(session));
                broadcastEditors(session);
                break;
            default:
                break;
        }
    }

    @Override
    public void handleConnectionClosed(WebSocketSession session) {
        removeSessionFromRedis(session);
        broadcastEditors(session);
    }

    private void removeSessionFromRedis(WebSocketSession session) {
        this.removeUniqueIdFromRedis(webSocketHandlerAdapter.getUniqueId(session));
    }

    private void removeUniqueIdFromRedis(String uniqueId) {
        Map<Object, Object> allFields = redisTemplate.opsForHash().entries(GRAPHIC_PAGE_EDIT_USERS_KEY);
        for (Map.Entry<Object, Object> entry : allFields.entrySet()) {
            Object field = entry.getKey();
            Object value = entry.getValue();
            List<String> list = JSONUtil.toList(value.toString(), String.class);
            list.removeIf(item -> item.contains(uniqueId));
            redisTemplate.opsForHash().put(GRAPHIC_PAGE_EDIT_USERS_KEY, field, JSONUtil.toJsonStr(list));
        }
    }

    //发送消息
    private void broadcastEditors(WebSocketSession session) {
        if (!supports(session)) return;
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(GRAPHIC_PAGE_EDIT_USERS_KEY);
        //WS推送信息
        WebSocketCommonMessageBody<Map<Object, Object>> webSocketCommonMessageBody = WebSocketCommonMessageBody.of(WebSocketBusinessTypeEnum.GRAPHIC_PAGE_EDIT_USERS, entries);
        commonMsgWebSocketManager.sendAllUserMessage(webSocketCommonMessageBody);
    }

    /**
     * 往 Hash 的某个 field 里面的 List 加元素（去重添加）
     */
    public void addEditFromRedis(String redisKey, String field, String newValue) {
        // 先取出field对应的value
        Object obj = redisTemplate.opsForHash().get(redisKey, field);
        List<String> list;
        if (obj == null) {
            list = new ArrayList<>();
        } else {
            list = JSONUtil.toList(obj.toString(), String.class);
        }
        if (list.contains(newValue)) {
            return;
        }
        list.add(newValue);
        redisTemplate.opsForHash().put(redisKey, field, JSONUtil.toJsonStr(list));
    }

    /**
     * 从 Hash 的某个 field 里面的 List 移除元素
     */
    public void removeEditFromRedis(String redisKey, String field, String valueToRemove) {
        Object obj = redisTemplate.opsForHash().get(redisKey, field);
        if (obj == null) {
            return;
        }
        List<String> list = JSONUtil.toList(obj.toString(), String.class);

        if (list.remove(valueToRemove)) {
            if (list.isEmpty()) {
                redisTemplate.opsForHash().delete(redisKey, field);
            } else {
                redisTemplate.opsForHash().put(redisKey, field, JSONUtil.toJsonStr(list));
            }
        }
    }

    public boolean getGraphicPageEditUsersEnable() {
        String sql = "SELECT * FROM SystemConfig WHERE SystemConfigKey = 'graphic.page.edit.users.enable'";
        final String[] enable = new String[1];
        jdbcTemplate.query(sql, rs -> {
            enable[0] = rs.getString("SystemConfigValue");
        });
        return enable[0] != null && !enable[0].trim().isEmpty() && !"false".equals(enable[0]);
    }
}
