package com.siteweb.common.websocket.handler;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.websocket.handler.impl.CommonMsgWebSocketHandler;
import com.siteweb.common.websocket.handler.impl.WebSocketMessageDispatcher;
import com.siteweb.common.websocket.manager.WebSocketSessionManager;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Objects;

/**
 * WebSocket接收与发送消息的统一处理 方便多业务实现
 * {@link CommonMsgWebSocketHandler} 目前只有该一个实现类
 *
 * <AUTHOR>
 * @date 2023/03/13
 */
@Slf4j
public abstract class WebSocketHandlerAdapter extends TextWebSocketHandler {

    public static final String PING = "ping";
    public static final String PONG = "pong";

    /**
     * 单次消息发送最大时长 单位毫秒
     */
    private static final int SEND_TIME_LIMIT = 10 * 1000;
    /**
     * 消息发送的缓冲区大小 单位字节数
     */
    private static final int SEND_BUFFER_SIZE_LIMIT = 512 * 1024;

    public abstract WebSocketSessionManager getWebSocketManager();

    @Autowired
    private WebSocketMessageDispatcher dispatcher;

    /**
     * 处理接收到客户端发送过来的信息
     *
     * @param session 会话
     * @param message 消息
     * @throws IOException ioexception
     */
    @Override
    protected final void handleTextMessage(@NotNull WebSocketSession session, TextMessage message) throws Exception {
        if (PING.equals(message.getPayload())) {
            getWebSocketManager().sendTextMsg(getUniqueId(session), PONG);
            return;
        }
        log.info("接收uniqueId:{}传递过来的消息：{}", getUniqueId(session), message.getPayload());
        subHandleTextMessage(session, message);
    }

    /**
     * 子类接收到客户端发送过来的信息
     * 目前并不会接收客户端关于ping以外的其他消息,所以暂时没有子类实现
     *
     * @param session 会话
     * @param message 消息
     */
    protected void subHandleTextMessage(WebSocketSession session, TextMessage message) {
    }

    /**
     * socket 建立成功事件
     *
     * @param session websocket会话
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws IOException {
        String uniqueId = this.getUniqueId(session);
        if (CharSequenceUtil.isBlank(uniqueId)) {
            this.closeSession(session);
            log.warn("{},未获取到uniqueId，退出WebSocket连接", session.getUri());
            return;
        }
        WebSocketSessionManager webSocketManager = getWebSocketManager();
        //已经建立连接关闭旧连接(避免重试后多次请求建立多次连接)
        if (webSocketManager.getSession(uniqueId) != null) {
            log.warn("webSocketManager{},uniqueId:{} 重复建立连接", webSocketManager.getClass().getName(), uniqueId);
            webSocketManager.removeAndClose(uniqueId);
        }
        // 用户连接成功，放入会话用户缓存
        // 底层标准 WebSocket 会话 （JSR-356） 不允许并发发送。因此，发送必须同步。为确保这一点，一种选择是 WebSocketSession 用 ConcurrentWebSocketSessionDecorator.
        ConcurrentWebSocketSessionDecorator concurrentWebSocketSessionDecorator = new ConcurrentWebSocketSessionDecorator(session, SEND_TIME_LIMIT, SEND_BUFFER_SIZE_LIMIT, ConcurrentWebSocketSessionDecorator.OverflowStrategy.DROP);
        webSocketManager.addSession(uniqueId, concurrentWebSocketSessionDecorator);
        log.info("webSocketManager{},uniqueId:{} socket连接成功", webSocketManager.getClass().getName(), uniqueId);
    }

    /**
     * 获取用户id
     *
     * @param session 会话连接
     * @return {@link String}
     */
    protected Integer getUserId(WebSocketSession session) {
        Object userId = session.getAttributes().get("userId");
        if (Objects.isNull(userId)) {
            return null;
        }
        return Integer.valueOf(userId.toString());
    }

    /**
     * 获取sessionId
     *
     * @param session 会话连接
     * @return {@link String}
     */
    protected String getSessionId(WebSocketSession session) {
        Object id = session.getAttributes().get("sessionId");
        if (Objects.isNull(id)) {
            return CharSequenceUtil.EMPTY;
        }
        return id.toString();
    }

    /**
     * 获取用户id加sessionId的唯一id
     *
     * @param session websocket会话
     * @return {@link String}
     */
    public String getUniqueId(WebSocketSession session) {
        return this.getUserId(session) + ":" + this.getSessionId(session);
    }

    /**
     * 关闭连接
     *
     * @param session webSocket会话
     */
    private void closeSession(WebSocketSession session) throws IOException {
        if (session.isOpen()) {
            session.close();
        }
    }

    /**
     * socket 断开连接时
     *
     * @param session websocket会话
     * @param status  关闭状态
     * @throws Exception
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String uniqueId = this.getUniqueId(session);
        if (uniqueId != null) {
            WebSocketSessionManager webSocketManager = getWebSocketManager();
            // 用户退出，移除缓存
            webSocketManager.removeAndClose(uniqueId);
            dispatcher.dispatchConnectionClosed(session);
            log.info("webSocketManager:{},uniqueId:{} 退出socket连接", webSocketManager.getClass().getName(), uniqueId);
        }
    }
}
