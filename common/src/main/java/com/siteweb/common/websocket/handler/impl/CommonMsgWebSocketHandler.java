package com.siteweb.common.websocket.handler.impl;

import com.siteweb.common.websocket.handler.WebSocketHandlerAdapter;
import com.siteweb.common.websocket.manager.WebSocketSessionManager;
import com.siteweb.common.websocket.manager.impl.CommonMsgWebSocketManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

/**
 * <AUTHOR>
 * @date 2023/03/13
 */
@Component
public class CommonMsgWebSocketHandler extends WebSocketHandlerAdapter {
    @Autowired
    CommonMsgWebSocketManager commonMsgWebSocketManager;

    @Autowired
    private WebSocketMessageDispatcher dispatcher;

    @Override
    public WebSocketSessionManager getWebSocketManager() {
        return commonMsgWebSocketManager;
    }

    @Override
    protected void subHandleTextMessage(WebSocketSession session, TextMessage message) {
        dispatcher.dispatchMessage(session, message);
    }
}
