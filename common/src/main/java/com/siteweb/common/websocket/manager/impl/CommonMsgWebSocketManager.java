package com.siteweb.common.websocket.manager.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.websocket.dto.WebSocketCommonMessageBody;
import com.siteweb.common.websocket.manager.WebSocketSessionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通用的消息管理器
 * <AUTHOR>
 * @date 2023/03/14
 */
@Component
@Slf4j
public class CommonMsgWebSocketManager extends WebSocketSessionManager {
    private final ConcurrentHashMap<String, WebSocketSession> commonMsgWebsocketStatePool = new ConcurrentHashMap<>();

    @Override
    public ConcurrentHashMap<String, WebSocketSession> getSessionStatePool() {
        return commonMsgWebsocketStatePool;
    }

    /**
     * 发送给指定客户端消息
     * @param uniqueId 客户端唯一会话id
     * @param webSocketCommonMessageBody 需要发送的消息体
     */
    public <T> void sendMessage(String uniqueId, WebSocketCommonMessageBody<T> webSocketCommonMessageBody) {
        WebSocketSession session = commonMsgWebsocketStatePool.get(uniqueId);
        sendMessage(uniqueId, session, webSocketCommonMessageBody);
    }

    /**
     * 发送给所有在线客户端消息
     * @param webSocketCommonMessageBody webSocket需要接收的内容
     */
    public <T> void sendAllUserMessage(WebSocketCommonMessageBody<T> webSocketCommonMessageBody) {
        ArrayList<Map.Entry<String, WebSocketSession>> webSocketEntries = new ArrayList<>(commonMsgWebsocketStatePool.entrySet());
        for (Map.Entry<String, WebSocketSession> webSocketEntry : webSocketEntries) {
            sendMessage(webSocketEntry.getKey(), webSocketEntry.getValue(), webSocketCommonMessageBody);
        }
    }

    private <T> void sendMessage(String uniqueId, WebSocketSession webSocketSession, WebSocketCommonMessageBody<T> webSocketCommonMessageBody) {
        if (webSocketSession == null || !webSocketSession.isOpen()) {
            log.error("uniqueId:{},消息内容:{},发送webSocket消息失败，会话不存在或者会话已经关闭", uniqueId, webSocketCommonMessageBody);
            return;
        }
        try {
            TextMessage textMessage = new TextMessage(JSONUtil.toJsonStr(webSocketCommonMessageBody));
            webSocketSession.sendMessage(textMessage);
            log.info("uniqueId：{},发送消息成功,消息内容:{}", uniqueId, textMessage);
        } catch (IOException e) {
            log.error("uniqueId:{}，内容：{},发送webSocket消息失败 {}", uniqueId, webSocketCommonMessageBody, ExceptionUtil.stacktraceToString(e));
        }
    }
}
