package com.siteweb.common.websocket.handler;

import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;


public interface WebSocketBusinessHandler {
    String getType();

    boolean supports(WebSocketSession session);

    void handleMessage(WebSocketSession session, TextMessage message);

    void handleConnectionClosed(WebSocketSession session);
}