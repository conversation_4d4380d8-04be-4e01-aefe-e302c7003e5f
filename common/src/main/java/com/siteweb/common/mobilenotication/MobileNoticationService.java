package com.siteweb.common.mobilenotication;

import cn.hutool.json.JSONObject;
import com.siteweb.common.mobilenotication.exception.MobileNotificationTokenException;

/**
 * 手机推送service
 * @Author: lzy
 * @Date: 2023/3/18 10:50
 */
public interface MobileNoticationService<D> {

    /**
     * redis鉴权key
     * @return redis cache Key
     */
    String redisAuthKey();

    /**
     * 鉴权
     * @return token
     */
    String auth();

    /**
     *
     * 校验鉴权是否失效，抽象各个子类实现
     * @param jsonObject api响应结果
     * @return 是否成功
     * @throws MobileNotificationTokenException 抛出接口主动重试调用
     */
    boolean validAuthFail(JSONObject jsonObject) throws MobileNotificationTokenException;

    /**
     * 推送接口
     * @param d 数据模型
     * @return api结果
     */
    JSONObject push(D d) throws Exception;
}
