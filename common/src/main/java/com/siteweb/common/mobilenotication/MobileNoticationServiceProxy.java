package com.siteweb.common.mobilenotication;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.siteweb.common.mobilenotication.exception.MobileNotificationTokenException;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.Map;


/**
 * @Author: lzy
 * @Date: 2023/3/18 10:29
 */

@Slf4j
public class MobileNoticationServiceProxy {

    /**
     * 源对象（key：源对象class，value：源对象）
     */
    static Map<Class<?>, MobileNoticationService<?>> originServiceMap = new HashMap<>(8);
    /**
     * 代理对象（key：代理对象class，源对象class）
     */
    static Map<Class<?>, Class<?>> proxyServiceMap = new HashMap<>(8);
    public MobileNoticationServiceProxy() {
    }

    /**
     * 代理，token失败重试
     * 作用：在调用外部api接口时，出现token异常时，默认重试auth鉴权，并重新执行原api
     * 调用代理对象时，出现token异常时，重新调用auth并执行原方法（使用原service调用）
     */
    public static <D> MobileNoticationService<D> buildProxyObj(MobileNoticationService<D> mobileNoticationService) {
        Class<? extends MobileNoticationService> originClass = mobileNoticationService.getClass();
        originServiceMap.put(originClass, mobileNoticationService);
        MobileNoticationService<D> proxy = (MobileNoticationService<D>) Proxy.newProxyInstance(MobileNoticationServiceProxy.class.getClassLoader(), mobileNoticationService.getClass().getInterfaces(), (o, method, args) -> {
            Object result = null;
            // 使用代理对象的方法获取到当前方法执行的源对象（代理对象无法获取到当前方法具体的实现类，获取的是代理对象）
            MobileNoticationService<?> originService = originServiceMap.get(proxyServiceMap.get(o.getClass()));
            try {
                result = method.invoke(originService, args);
                if (ObjectUtil.isNotEmpty(result) && result instanceof JSONObject) {
                    originService.validAuthFail(((JSONObject) result));
                }
            } catch (MobileNotificationTokenException e) {
                String auth = originService.auth();
                if (CharSequenceUtil.isNotEmpty(auth)) {
                    result = method.invoke(originService, args);
                }
            } catch (Exception e) {
                log.error("MobileNotificationService executor exception", e);
            }
            return result;
        });
        proxyServiceMap.put(proxy.getClass(), originClass);
        return proxy;
    }

}
