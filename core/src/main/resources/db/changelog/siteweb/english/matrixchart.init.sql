/*组态控件自定义api接口
   1-100 除了1，2，3，4，5 其余给s2使用
   100-200 给phoenix使用
   其余后面自己定义区间
*/
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (1,'Monitoring Overview','stationhaltpoweroffgen',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (2,'Site Overview','stationcategorylist',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (3,'Alarm TOP5','stationeventtopfive',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (4,'Monitoring Statistics','devicemonitoringstatus',NULL);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (5,'Alarm Trends','alarmgrouptrends',2);
insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (6,'Region Alarm Distribution','eventdistribute',1);
insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (7,'Monthly Alarm Statistics','monthevent',1);
insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (9,'Regional Distribution of Break Stations','stninterruptdistribute',1);
insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (10,'Distribution Of Outage Areas','stnpoweroffdistribute',1);
insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (11,'Station Area Statistics','stnpoweroffdistribute',1);
insert INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (12,'Distribution Areas With Low Total Battery Voltage','batlowvoltage',1);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (100,'Capacity Load','capacity/percentcount',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (101,'Indicator Percentage Statistics','livecomplexindexs/percentcount',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (201,'Local Station Classification Statistics','stationstatistics',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (202,'Telecom Version Alerting Trends','alarmtrend',2);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (203,'Top10 Station Alarm Statistics','stationalarmstatistics',2);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (204,'Key Alarm Statistics','importanteventstatistics',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (205,'Monitor Availability','structureavailability',3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (206, 'Building Alarm Statistics', 'buildingalarmstatistics', 3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (207, 'Building Equipment Types Alarm Statistics', 'equipmentcategoryalarmstatistics', 3);
INSERT INTO matrixchart (MatrixChartId, Name, `Path`,DataType) VALUES (208, 'Safety Index In The Past 30 Days', 'rw/getdata?action=safe_index_30', 3);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (220, 'Room Rack Statistics', 'computerrack/capacitybyroom', 2);
INSERT INTO matrixchart (MatrixChartId,`Name`,`Path`,DataType) VALUES (221, 'Building Rack Statistics', 'computerrack/capacitybybuilding', 2);
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (401,'Robot Itself Business Statistics','Robot/RobotOwnStatistics','2');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (402,'Robot Alarm Level Statistics','RobotAlarm/AlarmStatisticsByLevel','3');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (403,'Time Statistics Of Robot Equipment Alarms','RobotAlarm/AlarmStatisticsByTime','3');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (404,'Robot Environment Statistics','Robot/EnvironmentDataStatistics','2');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (405,'Robot Inspection Point Temperature Statistics','RobotAlarm/TemperatureHLStatistics','2');
insert into matrixchart (MatrixChartId,Name,Path,DataType) values (406,'Robotic Equipment Statistics','RobotDevice/DeviceStatistics','2');
INSERT INTO matrixchart (MatrixChartId, Name, `Path`, DataType) VALUES(407, 'Monitoring Overview city', '/stationhaltpoweroffgen/102', 1);
INSERT INTO matrixchart (MatrixChartId, Name, `Path`, DataType) VALUES(408, 'Monitoring Overview District', '/stationhaltpoweroffgen/103', 1);
INSERT INTO matrixchart (MatrixChartId, Name, `Path`, DataType) VALUES(409, 'Safety Index Of The Day', 'rw/getdata?action=rador_index', 3);