INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (1,'SMS sending','Send method',1,1,null,null,false,1,null);
INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (2,'Mailing','Send method',1,1,null,null,true,2,null);
INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (3,'Alarm box','Send method',1,1,null,null,true,3,null);
INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (4,'Telephone voice (SMS)','Send method',1,1,null,null,true,4,null);
-- INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
-- VALUES (8,'短信发送(东北大学)','发送方式',1,1,null,null,false,8,null);
-- INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
-- VALUES (9,'邮件发送(东北大学)','发送方式',1,1,null,null,false,9,null);
-- INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
-- VALUES (10,'微信发送(东北大学)','发送方式',1,1,null,null,false,10,null);
INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (11,'Failure to resend','Alarm upgrade',1,1,null,null,true,100,null);
INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (12,'Timeout not confirmed','Alarm upgrade',1,1,null,null,true,101,null);
INSERT INTO alarmnotifyelement (ElementId, ElementName, ElementType, InputNodesCount, OutputNodesCount, Icon, Expression, Visible, SortIndex, Description)
VALUES(13, 'WeCom Application Notification', 'Send method', 1, 1, NULL, NULL, 1, 5, NULL);
INSERT INTO alarmnotifyelement (ElementId, ElementName, ElementType, InputNodesCount, OutputNodesCount, Icon, Expression, Visible, SortIndex, Description)
VALUES (14,'Alarm Light','Send method',1,1,null,null,true,6,null);
INSERT INTO `alarmnotifyelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
VALUES (15,'APP send','Send method',1,1,null,null,true,7,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (17,'Lark Notification','Send method',1,1,null,null,true,8,null);

INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(1, 'Park');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(2, 'Building');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(3, 'Floor');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(4, 'Room');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(5, 'MDC');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(6, 'Equipment');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(7, 'Alarm');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(8, 'Equipment base');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(9, 'Alarm base');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(10, 'Alarm Status');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(11, 'Alarm level');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(12, 'position');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(13, 'project status');
INSERT INTO `alarmnotifyfiltercondition` (`FilterConditionId`,`FilterConditionName`) VALUES(14, 'Keyword');