/*报表模板分类*/
INSERT INTO `reportschemacategory` VALUES (1, 'Alarm', 'alarm', 'alarm', 1);
INSERT INTO `reportschemacategory` VALUES (2, 'History', 'history', 'history', 2);
INSERT INTO `reportschemacategory` VALUES (3, 'Complex Index', 'complexindex', 'complexindex', 3);
INSERT INTO `reportschemacategory` VALUES (4, 'Log', 'log', 'log', 4);
INSERT INTO `reportschemacategory` VALUES (5, 'Others', 'others', 'others', 5);
INSERT INTO `reportschemacategory` VALUES (6, 'Battery', 'battery', 'battery', 6);
INSERT INTO `reportschemacategory` VALUES (7, 'Security', 'security', 'security', 7);
INSERT INTO `reportschemacategory` VALUES (8, 'Air conditioner', 'airconditioner', 'airconditioner', 8);

/*报表模板*/
INSERT INTO `reportschema` VALUES (1, 'Current Alarm Report', 'Display the results of the current alarm report data for the desired query', '1.0.0', 1, 'admin', NOW(), 1, 1,null);
INSERT INTO `reportschema` VALUES (2, 'History Alarm Reports', 'Display the results of the required query history alarm report data', '1.0.0', 1, 'admin', NOW(), 2, 1,null);
INSERT INTO `reportschema` VALUES (3, 'History Data Reports', 'Display the data results of the required query history data report', '1.0.0', 2, 'admin', NOW(), 3, 1,null);
INSERT INTO `reportschema` VALUES (7, 'Alarm Operation Log Report (Device)', 'Display the results of the required query alarm operation log report data', '1.0.0', 4, 'admin', NOW(), 7, 1,null);
INSERT INTO `reportschema` VALUES (8, 'Alarm Operation Log Report (Alarm Events)', 'Display the results of the required query alarm operation log report data', '1.0.0', 4, 'admin', NOW(), 7, 1,null);
INSERT INTO `reportschema` VALUES (9, 'History Discharge Reports', 'Display the historical discharge structure of the desired query device', '1.0.0', 6, 'admin', NOW(), 8, 1,null);
INSERT INTO `reportschema` VALUES (10, 'History Metrics Statement', 'Display the results of the required query history index report data', '1.0.0', 3, 'admin', NOW(), 9, 1,null);
INSERT INTO `reportschema` VALUES (13, 'Control Record Report', 'Display the control records for the desired query', '1.0.0', 5, 'admin', NOW(), 11, 1,null);
-- INSERT INTO `reportschema` VALUES (14, 'Instant Reports', 'Display real-time data for the desired query', '1.0.0', 5, 'admin', NOW(), 12, 1,null);
INSERT INTO `reportschema` VALUES (15, 'Statistical Report On The Number Of Alarms', 'Statistical report on the number of alarms', '1.0.0', 1, 'admin', NOW(), 12, 1,null);
INSERT INTO `reportschema` VALUES (16, 'Electricity Consumption Statistics Report', 'Display the electricity consumption statistics for the desired query', '1.0.0', 2, 'admin', NOW(), 21, 1,null);
INSERT INTO `reportschema` VALUES (18, 'Generic Customized Reports', 'Display the daily electricity consumption statistics for the desired query', '1.0.0', 2, 'admin', NOW(), 22, 1,null);
INSERT INTO `reportschema` VALUES (23, 'Alarm Masking Report', 'Display the results of the required query alarm masking report data', '1.0.0', 4, 'admin', NOW(), 15, 1,null);
INSERT INTO `reportschema` VALUES (24, 'User Operation Log Report', 'The user''s operation log contains operations such as login and configuration changes', '1.0.0', 4, 'admin', NOW(), 4, 1,null);
/*INSERT INTO `reportschema` VALUES (25, '门禁刷卡记录日志', '展示所需查询门禁刷卡记录报表数据结果', '1.0.0', 4, 'admin', NOW(), 16, 1,null);*/
INSERT INTO `reportschema` VALUES (26, 'Alarm Notification Delivery Record Report', 'Query alarm notification delivery records', '1.0.0', 1, 'admin', NOW(), 19, 1,null);
INSERT INTO `reportschema` VALUES (27, 'Historical Warning Data Query Report', 'Display the results of the desired query history alert data', '1.0.0', 2, 'admin', NOW(), 20, 1,null);
INSERT INTO `reportschema` VALUES (28, 'Historical Data Report(5 Minutes Storage)', 'Display the data results of the required query history data report', '1.0.0', 2, 'admin', NOW(), 3, 1,null);
INSERT INTO `reportschema` VALUES (29, 'Rack Change Record Report', 'Display the results of the desired rack change record', '1.0.0', 2, 'admin', NOW(), 23, 1,null);
INSERT INTO `reportschema` VALUES (30, 'Audit Record Report', 'Display the results of the desired audit record', '1.0.0', 7, 'admin', NOW(), 24, 1,null);
INSERT INTO `reportschema` VALUES (31, 'Security Log Report', 'Display the security log records to be queried', '1.0.0', 7, 'admin', NOW(), 25, 1,null);
INSERT INTO `reportschema` VALUES (32, 'Equip state report', 'Display the historical operation status of the subordinate equipment of the air-conditioning group control equipment that needs to be queried', '1.0.0', 8, 'admin', NOW(), 26, 1,null);
INSERT INTO `reportschema` VALUES (33, 'Batch Control Group Change Log Report', 'Display change log records of batch control groups', '1.0.0', 8, 'admin', NOW(), 27, 1,null);
INSERT INTO `reportschema` VALUES (34, 'Batch Issue Control Command History Report', 'Display the history of batch issued control commands', '1.0.0', 8, 'admin', NOW(), 28, 1,null);
INSERT INTO `reportschema` VALUES (35, 'Virtual Management Equipment History Change Report', 'Display the virtual management equipment and associated data change log records', '1.0.0', 8, 'admin', NOW(), 29, 1,null);
-- INSERT INTO `reportschema` VALUES (36, 'BA Control Command Report', 'Display BA issues instructions and synchronizes to the control record of s 6', '1.0.0', 4, 'admin', NOW(), 30, 1,null);
INSERT INTO `reportschema` VALUES (37, 'All Alarm Report', 'Display the results of all the alarm tables required to be queried', '1.0.0', 4, 'admin', NOW(), 31, 1,null);
INSERT INTO `reportschema` VALUES (38, 'Equipment Report', 'Display the required equipment list', '1.0.0', 5, 'admin', NOW(), 32, 1,null);
INSERT INTO `reportschema` VALUES (39, 'Historical Data Report(Raw data)', 'Display the data results of the required query history data report', '1.0.0', 2, 'admin', NOW(), 33, 1,null);
INSERT INTO `reportschema` VALUES (40, 'Alarm Classification Statistical Report', 'Displays the number of abnormal alarms and construction alarms to be queried', '1.0.0', 1, 'admin', NOW(),34, 1,null);
/*报表数据源*/
INSERT INTO `reportdatasource` VALUES (1, 'Current Alarms', 'live-events');
INSERT INTO `reportdatasource` VALUES (2, 'Historical Alerts', 'historyevents');
INSERT INTO `reportdatasource` VALUES (3, 'Historical Data', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (4, 'Operation Log', 'userlogs');
INSERT INTO `reportdatasource` VALUES (6, 'Maintenance Performance', 'maintenanceperformances');
INSERT INTO `reportdatasource` VALUES (7, 'Historical Data', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (8, 'Historical Discharge', 'batterydischargerecord');
INSERT INTO `reportdatasource` VALUES (9, 'Historical Indicators', 'historyComplexIndex');
INSERT INTO `reportdatasource` VALUES (10, 'Historical Data', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (11, 'Control Records', 'historycommandviews');
INSERT INTO `reportdatasource` VALUES (12, 'Number Of Alarms', 'live-events+historyevents');
INSERT INTO `reportdatasource` VALUES (13, 'Daily Inspection', 'currenthealthystate');
INSERT INTO `reportdatasource` VALUES (14, 'Integrated Alarms', 'totalevents');
INSERT INTO `reportdatasource` VALUES (15, 'Alarm Masking', 'alarmmask');
/*INSERT INTO `reportdatasource` VALUES (16, '门禁刷卡', 'cardswipe');*/
INSERT INTO `reportdatasource` VALUES (19, 'Alarm notification delivery log', 'alarmNotifyRecord');
INSERT INTO `reportdatasource` VALUES (20, 'Historical warning data query report', 'prealarmhistory');
INSERT INTO `reportdatasource` VALUES (23, 'Rack change record report', 'rackchangerecord');
INSERT INTO `reportdatasource` VALUES (24, 'Audit report', 'auditReport');
INSERT INTO `reportdatasource` VALUES (25, 'Security log report', 'securityReport');
INSERT INTO `reportdatasource` VALUES (26, 'Equip state report', 'equipStateReport');
INSERT INTO `reportdatasource` VALUES (27, 'Batch Control Group Change Log Report', 'batchControlGroupChangeLog');
INSERT INTO `reportdatasource` VALUES (28, 'Batch Issue Control Command History Report', 'batchControlCmdHistory');
INSERT INTO `reportdatasource` VALUES (29, 'Virtual Management Equipment History Change Report', 'autoControlEquipmentChangeLog');
-- INSERT INTO `reportdatasource` VALUES (30, 'BA Control Command Report', 'bacontrolcommand');
INSERT INTO `reportdatasource` VALUES (31, 'All Alarm Report', 'allAlarm');
INSERT INTO `reportdatasource` VALUES (32, 'Equipment List', 'equipmentReport');
INSERT INTO `reportdatasource` VALUES (33, 'Historical Data Report(Raw data)', 'historydatapoints');
INSERT INTO `reportdatasource` VALUES (34, 'Alarm Classification Statistical', 'alarmClassificationStatistical');
/*查询参数*/
/*
ParameterControlId:
1:开始时间
2:结束时间
3:通用下拉控件
4:设备
5:设备信号
6:指标
7:支路空开测点
8:即时报表id
9:设备支路
10：上传excel
11：上传json
12：输入框
13: 事件选择器
14: 信号基类选择器
15: 空调群控设备选择器
16: 局站选择器
17: 层级选择器
 */
/*当前告警报表*/
insert into `reportschemaqueryparameter` values (41, 'startDate', 'Start Time', 1, '1', NULL, NULL, TRUE,10);
insert into `reportschemaqueryparameter` values (42, 'endDate', 'End Time', '1', '2', NULL, NULL, TRUE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (43, 'baseEquipmentIds', 'Equipment Base Class', 1, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,30);
INSERT INTO `reportschemaqueryparameter` VALUES (44, 'equipmentIds', 'Equipment', 1, '4', 'type=multiple', NULL, TRUE,40);
INSERT INTO `reportschemaqueryparameter` VALUES (113, 'eventLevels', 'Alarm Level', 1, '3', 'api=coreeventseverities;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,50);
INSERT INTO `reportschemaqueryparameter` VALUES (114, 'eventName', 'Event Name', 1, '12', NULL, NULL, TRUE,60);
INSERT INTO `reportschemaqueryparameter` VALUES (115, 'keyword', 'Keyword', 1, '12', NULL, NULL, TRUE,70);
INSERT INTO `reportschemaqueryparameter` VALUES (116, 'confirmerIds', 'Confirmed By', 1, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,80);
INSERT INTO `reportschemaqueryparameter` VALUES (118, 'eventIds', 'Events', 1, '13', 'type=events', NULL, TRUE,90);
INSERT INTO `reportschemaqueryparameter` VALUES (292, 'description', 'description', 1, '12', NULL, NULL, TRUE,100);
INSERT INTO `reportschemaqueryparameter` VALUES (400, 'equipmentCategories', 'Equipment Category', 1, '3', NULL, NULL, TRUE,31);
INSERT INTO `reportschemaqueryparameter` VALUES (401, 'eventReasonTypes', 'Alarm Classification', 1, 3, 'api=eventreasontypes;type=multiple', '{"value": "id","display":"name"}', TRUE, 101);

/*历史告警报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (1, 'startDate', 'Start Time', 2, '1', NULL, NULL, FALSE,10);
INSERT INTO `reportschemaqueryparameter` VALUES (2, 'endDate', 'End Time', 2, '2', NULL, NULL, FALSE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (3, 'baseEquipmentId', 'Equipment Base Class', 2, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,30);
INSERT INTO `reportschemaqueryparameter` VALUES (4, 'equipmentIds', 'Equipment', 2, '4', 'type=multiple', NULL, TRUE,40);
INSERT INTO `reportschemaqueryparameter` VALUES (109, 'eventLevel', 'Alarm Level', 2, '3', 'api=coreeventseverities;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,50);
INSERT INTO `reportschemaqueryparameter` VALUES (110, 'eventName', 'Event Name', 2, '12', NULL, NULL, TRUE,60);
INSERT INTO `reportschemaqueryparameter` VALUES (111, 'operatorIds', 'Confirmed By', 2, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,70);
INSERT INTO `reportschemaqueryparameter` VALUES (112, 'keyword', 'Keyword', 2, '12', NULL, NULL, TRUE,80);
INSERT INTO `reportschemaqueryparameter` VALUES (117, 'eventIds', 'Events', 2, '13', 'type=events', NULL, TRUE,90);
INSERT INTO `reportschemaqueryparameter` VALUES (293, 'description', 'description', 2, '12', NULL, NULL, TRUE,100);
INSERT INTO `reportschemaqueryparameter` VALUES (500, 'equipmentCategories', 'Equipment Category', 2, '3', NULL, NULL, TRUE,31);
INSERT INTO `reportschemaqueryparameter` VALUES (501, 'eventReasonTypes', 'Alarm Classification', 2, 3, 'api=eventreasontypes;type=multiple', '{"value": "id","display":"name"}', TRUE, 101);

/*历史数据(信号)*/
INSERT INTO `reportschemaqueryparameter` VALUES (7, 'startTime', 'Start Time', 3, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (8, 'endTime', 'End Time', 3, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (10, 'timeGranularity', 'Time Granularity', 3, '3', 'json=[{"name": "1 Minutes","value": "1m-start"},{"name": "5 Minutes","value": "5m-start"},{"name": "10 Minutes","value": "10m-start"},{"name": "30 Minutes","value": "30m-start"},{"name": "1 Hour","value": "1h-start"},{"name": "3 Hour","value": "3h-start"},{"name": "6 Hour","value": "6h-start"},{"name": "12 Hour","value": "12h-start"},{"name": "1 Day","value": "1d-start"},{"name": "Week","value": "1w-start"},{"name": "Month","value": "month-start"},{"name": "Year","value": "year-start"}]', '{"value": "value", "display":"name"}', false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (85, 'signalIds', 'Equipment Signal', 3, '5', NULL, 'type=signals', true,5);
INSERT INTO `reportschemaqueryparameter` VALUES (150, 'baseTypeIds', 'Base Type', 3, '14', NULL, 'type=basetypeids', true,7);
INSERT INTO `reportschemaqueryparameter` VALUES (151, 'signalTypes', 'Signal Type', 3, '3', 'json=[{"name":"Statistical Data－Max","value":"0"},{"name":"Statistical Data－Min","value":"1"},{"name":"Statistical Data－Avg","value":"2"},{"name":"Event","value":"3"},{"name":"Variation Amplitude","value":"4"},{"name":"Storage Cycle","value":"5"},{"name":"Meter Reading Data","value":"6"},{"name":"Scheduled Storage","value":"7"}];type=multiple', '{"value": "value", "display":"name"}', true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (152, 'unit', 'Does it include units', 3, '3', 'json=[{"name":"No","value":"0"},{"name":"Yes","value":"1"}]', '{"value": "value", "display":"name"}', true,9);
INSERT INTO `reportschemaqueryparameter` VALUES (153, 'equipmentIds', 'Equipment', 3, '4', 'type=multiple', '{"value": "value", "display":"name"}', true,8);
INSERT INTO `reportschemaqueryparameter` VALUES (154, 'valueRetrievalMethod', 'valueRetrievalMethod', 3, '3', 'json=[{"name": "Max","value": "max"},{"name": "Min","value": "min"},{"name": "Avg","value": "mean"},{"name": "First","value": "first"},{"name": "Last","value": "last"}]', '{"value": "value", "display":"name"}', false,4);

/*历史数据(信号) 周期存储*/
INSERT INTO `reportschemaqueryparameter` VALUES (125, 'startTime', 'Start Time', 28, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (126, 'endTime', 'End Time', 28, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (127, 'timeGranularity', 'Time Granularity', 28, '3', 'json=[{"name": "Raw Data","value": "0"},{"name": "10 Minutes","value": "10m-start"},{"name": "30 Minutes","value": "30m-start"},{"name": "1 Hour","value": "1h-start"},{"name": "3 Hour","value": "3h-start"},{"name": "6 Hour","value": "6h-start"},{"name": "12 Hour","value": "12h-start"},{"name": "1 Day","value": "1d-start"},{"name": "Week","value": "1w-start"},{"name": "Month","value": "month-start"}]', '{"value": "value", "display":"name"}', false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (128, 'signalIds', 'Equipment Signal', 28, '5', NULL, 'type=signals', false,4);
INSERT INTO `reportschemaqueryparameter` VALUES (129, 'signalMeaningEscape', 'Signal Meaning Escape', 28, '3', 'json=[{"name":"No","value":"0"},{"name":"Yes","value":"1"}]', '{"value": "value", "display":"name"}', true,5);
-- INSERT INTO `reportschemaqueryparameter` VALUES (130, 'valueRetrievalMethod', 'valueRetrievalMethod', 28, '3', 'json=[{"name": "Max","value": "max"},{"name": "Min","value": "min"},{"name": "Avg","value": "mean"},{"name": "First","value": "first"},{"name": "Last","value": "last"}]', '{"value": "value", "display":"name"}', false,4);
/*告警操作日志报表(设备)*/
INSERT INTO `reportschemaqueryparameter` VALUES (12, 'startDate', 'Start Time', 7, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (13, 'endDate', 'End Time', 7, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (14, 'equipmentIds', 'Equipment', 7, '4', 'type=multiple', NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (16, 'operatorIds', 'Operators', 7, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (17, 'operation', 'Operation', 7, '3', 'json=[{"name":"All", "value":"0"},{"name":"Alarm Forced End", "value":"Alarm Forced End"}, {"name":"Alarm Confirm", "value":"Alarm Confirm"}, {"name":"Alarm Remark", "value":"Alarm Remark"}]', '{"value": "value", "display":"name"}', false,5);

/*告警操作日志报表(告警事件)*/
INSERT INTO `reportschemaqueryparameter` VALUES (18, 'startDate', 'Start Time', 8, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (19, 'endDate', 'End Time', 8, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (21, 'eventIds', 'Events', 8, '13', 'type=events', null, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (22, 'operatorIds', 'Operators', 8, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (23, 'operation', 'Operation', 8, '3', 'json=[{"name":"All", "value":"0"},{"name":"Alarm Forced End", "value":"Alarm Forced End"}, {"name":"Alarm Confirm", "value":"Alarm Confirm"}, {"name":"Alarm Remark", "value":"Alarm Remark"}]', '{"value": "value", "display":"name"}', false,5);

/*历史指标报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (26, 'startTime', 'Start Time', 10, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (27, 'endTime', 'End Time', 10, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (28, 'complexIndexIds', 'Indicators', 10, '6', NULL, NULL, false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (29, 'timeGranularity', 'Time Granularity', 10, '3', 'json=[{"name": "Raw Data","value": "0"},{"name": "10 Minutes","value": "10m"},{"name": "30 Minutes","value": "30m"},{"name": "1 Hour","value": "1h"},{"name": "3 Hour","value": "3h"},{"name": "6 Hour","value": "6h"},{"name": "12 Hour","value": "12h"},{"name": "1 Day","value": "1d"},{"name": "Week","value": "1w"},{"name": "Month","value": "month"}]', '{"value": "value", "display":"name"}', false,4);

/*告警屏蔽报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (78, 'maskStartDate', 'Shield Start Time', 23, '1', NULL, NULL, true,1);
INSERT INTO `reportschemaqueryparameter` VALUES (79, 'maskEndDate', 'Shield End Time', 23, '2', NULL, NULL, true,2);
INSERT INTO `reportschemaqueryparameter` VALUES (80, 'startDate', 'Start Time', 23, '1', NULL, NULL, true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (81, 'endDate', 'End Time', 23, '2', NULL, NULL, true,4);
INSERT INTO `reportschemaqueryparameter` VALUES (82, 'operationType', 'Operation Type', 23, '3', 'api=findalarmmaskoperationtype', '{"value": "operationType", "display":"desc"}', true,5);
INSERT INTO `reportschemaqueryparameter` VALUES (83, 'eventIds', 'Events', 23, '13', 'type=events&standardId=', NULL, true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (84, 'operatorIds', 'Operators', 23, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', true,7);

/*用户操作日志报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (86, 'startDate', 'Start Time', 24, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (87, 'endDate', 'End Time', 24, '2', NULL, NULL, false, 2);
INSERT INTO `reportschemaqueryparameter` VALUES (88, 'operatorIds', 'Operators', 24, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,3);

/*告警数量统计报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (45, 'startDate', 'Start Time', 15, '1', NULL, NULL, FALSE, 2);
INSERT INTO `reportschemaqueryparameter` VALUES (46, 'endDate', 'End Time', 15, '2', NULL, NULL, FALSE, 3);
INSERT INTO `reportschemaqueryparameter` VALUES (280, 'statisticType', 'Statistic Type', 15, '3', 'json=[{"name": "Room Level","value": "1"},{"name": "Equipment Level","value": "2"},{"name": "Equipment Category Level","value": "3"}]'', ''{"value": "value", "display":"name"}', '{"value": "value", "display":"name"}', FALSE, 1);
INSERT INTO `reportschemaqueryparameter` VALUES (281, 'resourceStructureIds', 'Resource Structure', 15, '17', 'type=multiple', NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (282, 'equipmentCategories', 'Equipment Category', 15, '18', 'type=multiple', NULL, true, 5);
INSERT INTO `reportschemaqueryparameter` VALUES (283, 'equipmentIds', 'Equipment', 15, '4', 'type=multiple', NULL, true, 6);

/*用电统计报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (47, 'startTime', 'Start Time', 16, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (48, 'endTime', 'End Time', 16, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (49, 'signalIds', 'Signal', 16, '5', 'type=signals&standardId=', NULL, false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (50, 'timeGranularity', 'Time Granularity', 16, '3', 'json=[{"name": "Day-Start","value": "start"},{"name": "Day-End","value": "end"},{"name": "Day-Difference","value": "difference"}]', '{"value": "value", "display":"name"}', false,4);
INSERT INTO reportschemaqueryparameter VALUES (303, 'displayEquPosition', 'Dispaly Equipment Position on Export', 16, '3', 'json=[{"name":"No", "value":"-1"},{"name":"Yes", "value":"1"}]', '{"value": "value", "display":"name"}', false, 5);

/*通用定制报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (55, 'startTime', 'Start Time', 18, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (56, 'endTime', 'End Time', 18, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (57, 'uploadExcel', 'Template File', 18, '10', NULL, 'upload-dir/customReport', false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (58, 'uploadJson', 'Configuration Files', 18, '11', NULL, 'upload-dir/customReport', false,4);
INSERT INTO `reportschemaqueryparameter` VALUES (59, 'timeGranularity', 'Time Granularity', 18, '3', 'json=[{"name": "Min Value","value": "min"},{"name": "Max Value","value": "max"},{"name": "Initial Value","value": "start"},{"name": "End Value","value": "end"},{"name": "Average Value","value": "avg"},{"name": "Difference Value","value": "difference"},{"name": "Max Difference Value","value": "maxdifference"},{"name": "Month Difference Value","value": "monthdifference"}]', '{"value": "value", "display":"name"}', false,5);

/*控制记录报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (38, 'startTime', 'Start Time', 13, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (39, 'endTime', 'End Time', 13, '2', NULL, NULL, false,2);

/*历史放电报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (25, 'equipmentIds', 'Equipment', 9, '4', 'type=multiple', NULL, TRUE,1);
-- 机架变更记录报表
INSERT INTO `reportschemaqueryparameter` VALUES (200, 'startTime', 'Start Time', 29, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (201, 'endTime', 'End Time', 29, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (203, 'position', 'Resource Location', 29, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (204, 'computerRackName', 'Rack Name', 29, '12', NULL, NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (205, 'iTDeviceName', 'IT Equipment Name', 29, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (206, 'operateState', 'Status', 29, '3', 'json=[{"name": "All","value": "0"},{"name": "Put In Shelf","value": "1"},{"name": "Take Form Shelf","value": "2"}]', '{"value": "value","display":"name"}', TRUE,6);

/*历史预警数据查询报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (119, 'startDate', 'Start Time', 27, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (120, 'endDate', 'End Time', 27, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (121, 'preAlarmCategory', 'Early Warning Category', 27, '3', 'api=prealarmcategorys', '{"value": "categoryId","display":"categoryName"}', TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (122, 'preAlarmSeverity', 'Early Warning Level', 27, '3', 'api=prealarmseveritys', '{"value": "preAlarmSeverityId","display":"preAlarmSeverityName"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (123, 'levelOfPath', 'Resource Level', 27, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (124, 'resourceName', 'Resource Name', 27, '12', NULL, NULL, TRUE,6);
-- 审计报表
INSERT INTO `reportschemaqueryparameter` VALUES (210, 'startTime', 'Start Time', 30, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (211, 'endTime', 'End Time', 30, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (212, 'operator', 'Operation Account', 30, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (213, 'details', 'Details', 30, '12', NULL, NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (214, 'clientIp', 'Client IP', 30, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (215, 'level', 'Audit Level', 30, '3', 'json=[{"name": "Min Level","value": "1"},{"name": "Basic Level","value": "2"},{"name": "Detail Level","value": "3"},{"name": "Undefined Level","value": "4"}];type=multiple', '{"value": "value","display":"name"}', TRUE,6);
-- 安全日志报表
INSERT INTO `reportschemaqueryparameter` VALUES (216, 'startTime', 'Start Time', 31, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (217, 'endTime', 'End Time', 31, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (218, 'operator', 'Operation Account', 31, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (219, 'type', 'Type', 31, '3', 'json=[{"name": "Identity Authentication","value": "1"},{"name": "Attack Detection","value": "2"},{"name": "Brute Force","value": "3"},{"name": "Integrity Test","value": "4"}];type=multiple', '{"value": "value","display":"name"}', TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (220, 'details', 'Details', 31, '12', NULL, NULL, TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (221, 'clientIp', 'Client Ip', 31, '12', NULL, NULL, TRUE,6);
-- 门禁报表
INSERT INTO `reportschemaqueryparameter` VALUES (89, 'doorAreaId', 'Area', 25, '3', 'api=doorarea', '{"value": "areaId", "display":"areaName"}', TRUE,1);
INSERT INTO `reportschemaqueryparameter` VALUES (90, 'equipmentIds', 'Equipment', 25, '4', 'type=multiple', NULL, TRUE,2);
INSERT INTO `reportschemaqueryparameter` VALUES (91, 'cardCode', 'Card Code', 25, '12', NULL, NULL, TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (92, 'cardName', 'Card Name', 25, '12', NULL, NULL, TRUE,4);
INSERT INTO `reportschemaqueryparameter` VALUES (93, 'operatorIds', 'Card Holder', 25, '3', 'api=accounts;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,5);
INSERT INTO `reportschemaqueryparameter` VALUES (94, 'cardGroup', 'Card Group', 25, '3', 'api=dataitems?entryId=75', '{"value": "itemId", "display":"itemValue"}', TRUE,6);
INSERT INTO `reportschemaqueryparameter` VALUES (95, 'cardStatus', 'Card Status', 25, '3', 'api=dataitems?entryId=46', '{"value": "itemId", "display":"itemValue"}', TRUE,7);
INSERT INTO `reportschemaqueryparameter` VALUES (96, 'startDate', 'Swipe Start Date', 25, '1', NULL, NULL, TRUE,8);
INSERT INTO `reportschemaqueryparameter` VALUES (97, 'endDate', 'Swipe End Date', 25, '2', NULL, NULL, TRUE,9);
INSERT INTO `reportschemaqueryparameter` VALUES (98, 'inOutSigns', 'Access Door Flag', 25, '3', 'json=[{"name": "Entrance","value": "Entrance"},{"name": "Go Out","value": "Go Out"}]', '{"value": "value", "display":"name"}', TRUE,10);
INSERT INTO `reportschemaqueryparameter` VALUES (99, 'validName', 'Door Status', 25, '12', NULL, NULL, TRUE,11);
-- 告警通知发送记录报表
INSERT INTO `reportschemaqueryparameter` VALUES (101, 'alarmStartTimeFrom', 'Alarm Time Start', 26, '1', NULL, NULL,  false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (102, 'alarmStartTimeTo', 'Alarm Time End', 26, '2', NULL, NULL,  false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (103, 'sendTimeFrom', 'Send Time From', 26, '1', NULL, NULL,  false,3);
INSERT INTO `reportschemaqueryparameter` VALUES (104, 'sendTimeTo', 'Send Time TO', 26, '2', NULL, NULL,  false,4);
INSERT INTO `reportschemaqueryparameter` VALUES (105, 'sendType', 'Send Type', 26, '3', 'json=[{"name":"All", "value":"0"},{"name":"Short Message", "value":"Short Message"}, {"name":"Mail", "value":"Mail"}, {"name":"Telephone Voice(SMS)", "value":"Telephone Voice(SMS)"},{"name":"WeCom Apply Notify", "value":"WeCom Apply Notify"}]', '{"value": "value", "display":"name"}',  true,5);
INSERT INTO `reportschemaqueryparameter` VALUES (106, 'sendResult', 'Send Result', 26, '3',  'json=[{"name":"All", "value":"0"},{"name":"Send Success", "value":"Send Success"}, {"name":"Send Fail", "value":"Send Fail"}]', '{"value": "value", "display":"name"}', true,6);
/*设备历史运行状态报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (222,  'startDate', 'Start Time', 32, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (223,  'endDate', 'End Time', 32, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (224,  'virtualInfo', 'Virtual Group Control Device', 32, '15', 'type=multiple', NULL, false,3);
/*批量控制分组变更报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (225, 'startDate', 'Start Time', 33, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (226, 'endDate', 'End Time', 33, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (227, 'changeType', 'Change Type', 33, '3', 'json=[{"name":"All", "value":"-1"},{"name":"New", "value":"1"},{"name":"Update", "value":"2"},{"name":"Delete", "value":"3"}]', '{"value": "value", "display":"name"}', true,3);
/*批量下发控制命令历史记录报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (228, 'startDate', 'Start Time', 34, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (229, 'endDate', 'End Time', 34, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (230, 'equipmentIds', 'Equipment', 34, '4', 'type=multiple', NULL, true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (231, 'cmdType', 'Command Type', 34, '3', 'json=[{"name":"All", "value":"-1"},{"name":"Remote boot", "value":"1"},{"name":"Remote shutdown", "value":"2"},{"name":"Temperature setting", "value":"3"},{"name":"Working mode switching", "value":"4"}]', '{"value": "value", "display":"name"}', true,4);
/*群控设备分组变更报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (232, 'startDate', 'Start Time', 35, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (233, 'endDate', 'End Time', 35, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (234, 'stationIds', 'Station', 35, '16', 'type=multiple', NULL, true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (235, 'changeType', 'Change Type', 35, '3', 'json=[{"name":"All", "value":"-1"},{"name":"New", "value":"1"},{"name":"Update", "value":"2"},{"name":"Delete", "value":"3"}]', '{"value": "value", "display":"name"}', true,4);
/*ba控制命令报表*/
-- INSERT INTO `reportschemaqueryparameter` VALUES (236, 'equipmentIds', 'Equipment', 36, '4', 'type=multiple', NULL, TRUE,1);
-- INSERT INTO `reportschemaqueryparameter` VALUES (237, 'controlName', 'Control Name', 36, '12', NULL, NULL, TRUE,2);
/*所有告警报表*/
insert into `reportschemaqueryparameter` values (238, 'startDate', 'Start Time', 37, '1', NULL, NULL, FALSE,10);
insert into `reportschemaqueryparameter` values (239, 'endDate', 'End Time', 37, '2', NULL, NULL, FALSE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (240, 'baseEquipmentIds', 'Equipment Base Class', 37, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,30);
INSERT INTO `reportschemaqueryparameter` VALUES (241, 'equipmentIds', 'Equipment', 37, '4', 'type=multiple', NULL, TRUE,40);
INSERT INTO `reportschemaqueryparameter` VALUES (242, 'eventLevels', 'Alarm Level', 37, '3', 'api=coreeventseverities;type=multiple', '{"value": "eventLevel", "display":"severityName"}', TRUE,50);
INSERT INTO `reportschemaqueryparameter` VALUES (243, 'eventName', 'Event Name', 37, '12', NULL, NULL, TRUE,60);
INSERT INTO `reportschemaqueryparameter` VALUES (244, 'keyword', 'Keyword', 37, '12', NULL, NULL, TRUE,70);
INSERT INTO `reportschemaqueryparameter` VALUES (245, 'confirmIds', 'Confirmed By', 37, '3', 'api=accounts/normal;type=multiple', '{"value": "userId", "display":"userName"}', TRUE,80);
INSERT INTO `reportschemaqueryparameter` VALUES (246, 'eventIds', 'Events', 37, '13', 'type=events', NULL, TRUE,90);
INSERT INTO `reportschemaqueryparameter` VALUES (600, 'equipmentCategories', 'Equipment Category', 37, '13', '', NULL, TRUE,31);
INSERT INTO `reportschemaqueryparameter` VALUES (601, 'eventReasonTypes', 'Alarm Classification', 37, 3, 'api=eventreasontypes;type=multiple', '{"value": "id","display":"name"}', TRUE, 101);
/*设备报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (247, 'resourceStructureIds', 'Resource Structure', 38, '17', 'type=multiple', NULL, TRUE,10);
INSERT INTO `reportschemaqueryparameter` VALUES (248, 'baseEquipmentIds', 'Equipment Base Class', 38, '3', 'api=equipmentbasetypedtos/used', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,20);
INSERT INTO `reportschemaqueryparameter` VALUES (288, 'equipmentState', 'Equipment State', 38, '3', 'json=[{"name": "OffLine","value": "0"},{"name": "OnLine","value": "1"},{"name": "Unregistered","value": "2"}]', '{"value": "value","display":"name"}', TRUE, 30);
INSERT INTO `reportschemaqueryparameter` VALUES (700, 'equipmentCategories', 'Equipment Category', 38, '3', '', '', TRUE, 21);

/*历史数据(原始数据)*/
INSERT INTO `reportschemaqueryparameter` VALUES (260, 'startTime', 'Start Time', 39, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (261, 'endTime', 'End Time', 39, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (262, 'signalIds', 'Equipment Signal', 39, '5', NULL, 'type=signals', true,3);
INSERT INTO `reportschemaqueryparameter` VALUES (263, 'baseTypeIds', 'Base Type', 39, '14', NULL, 'type=basetypeids', true,4);
INSERT INTO `reportschemaqueryparameter` VALUES (264, 'signalTypes', 'Signal Type', 39, '3', 'json=[{"name":"Statistical Data－Max","value":"0"},{"name":"Statistical Data－Min","value":"1"},{"name":"Statistical Data－Avg","value":"2"},{"name":"Event","value":"3"},{"name":"Variation Amplitude","value":"4"},{"name":"Storage Cycle","value":"5"},{"name":"Meter Reading Data","value":"6"},{"name":"Scheduled Storage","value":"7"}];type=multiple', '{"value": "value", "display":"name"}', true,6);
INSERT INTO `reportschemaqueryparameter` VALUES (265, 'equipmentIds', 'Equipment', 39, '4', 'type=multiple', '{"value": "value", "display":"name"}', true,5);
/*告警分类统计报表*/
INSERT INTO `reportschemaqueryparameter` VALUES (284, 'startDate', 'Start Time', 40, '1', NULL, NULL, false,1);
INSERT INTO `reportschemaqueryparameter` VALUES (285, 'endDate', 'End Time', 40, '2', NULL, NULL, false,2);
INSERT INTO `reportschemaqueryparameter` VALUES (286, 'baseEquipmentIds', 'Equipment Base Class', 40, '3', 'api=equipmentbasetypedtos/used;type=multiple', '{"value": "baseEquipmentId","display":"baseEquipmentName"}', TRUE,3);
INSERT INTO `reportschemaqueryparameter` VALUES (287, 'equipmentIds', 'Equipment', 40, '4', 'type=multiple', '{"value": "value", "display":"name"}', true,4);
/*输出参数*/
INSERT INTO reportschemaexportparameter VALUES (1,'max','Max',3,1);
INSERT INTO reportschemaexportparameter VALUES (2,'min','Min',3,1);
INSERT INTO reportschemaexportparameter VALUES (3,'sum','Sum',3,1);
INSERT INTO reportschemaexportparameter VALUES (4,'avg','Avg',3,1);
INSERT INTO reportschemaexportparameter VALUES (5,'max','Max',10,1);
INSERT INTO reportschemaexportparameter VALUES (6,'min','Min',10,1);
INSERT INTO reportschemaexportparameter VALUES (7,'sum','Sum',10,1);
INSERT INTO reportschemaexportparameter VALUES (8,'avg','Avg',10,1);
INSERT INTO reportschemaexportparameter VALUES (9,'first','First',15,1);
INSERT INTO reportschemaexportparameter VALUES (10,'second','Second',15,1);
INSERT INTO reportschemaexportparameter VALUES (11,'third','Third',15,1);
INSERT INTO reportschemaexportparameter VALUES (12,'fourth','Fourth',15,1);
INSERT INTO reportschemaexportparameter VALUES (13,'max','Max',16,1);
INSERT INTO reportschemaexportparameter VALUES (14,'min','Min',16,1);
INSERT INTO reportschemaexportparameter VALUES (15,'sum','Sum',16,1);
INSERT INTO reportschemaexportparameter VALUES (16,'avg','Avg',16,1);
INSERT INTO reportschemaexportparameter VALUES (17,'max','Max',20,1);
INSERT INTO reportschemaexportparameter VALUES (18,'min','Min',20,1);
INSERT INTO reportschemaexportparameter VALUES (19,'sum','Sum',20,1);
INSERT INTO reportschemaexportparameter VALUES (20,'avg','Avg',20,1);
INSERT INTO reportschemaexportparameter VALUES (21,'max','Max',28,1);
INSERT INTO reportschemaexportparameter VALUES (22,'min','Min',28,1);
INSERT INTO reportschemaexportparameter VALUES (23,'sum','Sum',28,1);
INSERT INTO reportschemaexportparameter VALUES (24,'avg','Avg',28,1);
INSERT INTO reportschemaexportparameter VALUES (25,'first','First',10,1);
INSERT INTO reportschemaexportparameter VALUES (26,'last','Last',10,1);
/*定时任务邮件发送配置*/
INSERT INTO `reporttimingtasktimetype` VALUES (1,'Storage Time','0'),(2,'An Hour Ago','-1h'),(3,'A Day Ago','-1d'),(4,'A Week Ago','-1w'),(5,'A Month Ago','-1m');

-- 默认初始化一个历史告警报表给现场定制页面查询
INSERT INTO report (ReportId, ReportName, ReportDescription, ReportSchemaId, ReportSchemaCategoryId, UpdateUserId, UpdateTime, ReportDataSourceId, MaxQueryInterval, CreateUserId, overt) VALUES(1, 'history event', NULL, 2, 2, -9999, '2023-06-08 10:50:11', 2, NULL, -9999, 0);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 41, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 42, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 43, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 44, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 113, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 114, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 115, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 116, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 118, '[]', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 292, '', 1);
INSERT INTO reportparameterpreset (ReportParameterPresetId, ReportId, ReportSchemaQueryParameterId, value, display) VALUES(null, 1, 401, '', 1);