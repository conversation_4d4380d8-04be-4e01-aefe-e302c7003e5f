# 字典项初始化
# 审计报表等级
INSERT INTO tbl_dataentry VALUES(3001,1,'Audit level',NULL,'Audit level',1,'');
CALL PIL_InitDictionaryEntryItem( 3001,1,0,0,1,'Min level','','min level');
CALL PIL_InitDictionaryEntryItem( 3001,2,0,0,1,'Basic level','','basic level');
CALL PIL_InitDictionaryEntryItem( 3001,3,0,0,1,'Detail level','','detail level');
CALL PIL_InitDictionaryEntryItem( 3001,4,0,0,1,'Undefined level','','undefined level');
# 安全日志报表类别
INSERT INTO tbl_dataentry VALUES(3002,1,'Type',NULL,'Type',1,'');
CALL PIL_InitDictionaryEntryItem( 3002,1,0,0,1,'Identity authentication','','identity authentication');
CALL PIL_InitDictionaryEntryItem( 3002,2,0,0,1,'Attack detection','','attack detection');
CALL PIL_InitDictionaryEntryItem( 3002,3,0,0,1,'Brute force','','brute force');
CALL PIL_InitDictionaryEntryItem( 3002,4,0,0,1,'Integrity test','','integrity test');
# IT设备模型类型
INSERT INTO tbl_dataentry VALUES(3003,1,'IT Device model type',NULL,'Category',1,'');
# 卡号类型
INSERT INTO tbl_dataentry VALUES(3004,1,'Card number type',NULL,'Card number type',1,'');
CALL PIL_InitDictionaryEntryItem(3004,10,0,0,1,'Decimal','','decimal');
CALL PIL_InitDictionaryEntryItem(3004,16,0,0,1,'Hexadecimal','','hexadecimal');
# 组态模板组件类型
INSERT INTO tbl_dataentry VALUES(3005,1,'Configure the template graphic type',NULL,'Configure the template graphic type',1,'');
CALL PIL_InitDictionaryEntryItem( 3005,1,0,0,1,'All','','all');
CALL PIL_InitDictionaryEntryItem( 3005,2,0,0,1,'Power & Environment Supervision','','power & environment supervision');
CALL PIL_InitDictionaryEntryItem( 3005,3,0,0,1,'General','','general');
CALL PIL_InitDictionaryEntryItem( 3005,4,0,0,1,'Complex index','','complex index');
CALL PIL_InitDictionaryEntryItem( 3005,5,0,0,1,'Energy','','energy');
CALL PIL_InitDictionaryEntryItem( 3005,6,0,0,1,'Customize','','customize');
CALL PIL_InitDictionaryEntryItem( 3005,7,0,0,1,'Chart','','chart');