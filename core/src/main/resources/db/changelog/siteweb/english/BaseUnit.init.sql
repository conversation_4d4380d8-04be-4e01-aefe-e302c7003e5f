-- 系统用到的单位计量
insert into baseUnit VALUES (1, 'Voltage', 'Voltage', 'V', 'U', 'Volt', 'Potential, voltage, electric potential');
insert into baseUnit VALUES (2, 'Voltage_kV', 'Voltage_kV', 'kV', 'U', 'Kilovolt', 'High Pressure');
insert into baseUnit VALUES (3, 'Current', 'Current', 'A', 'I', 'Ampere', '');
insert into baseUnit VALUES (4, 'Frequency', 'Frequency', 'Hz', 'F', 'Hertz', '');
insert into baseUnit VALUES (5, 'Power Factor', 'Power Factor', '', 'PF', 'η', '');
insert into baseUnit VALUES (6, 'Active Power', 'Active Power', 'W', 'P', 'tile', '');
insert into baseUnit VALUES (7, 'Active Power_kW', 'Active Power_kW', 'kW', 'P', 'Kilowatt', 'Active power, radiated flux');
insert into baseUnit VALUES (8, 'Active Power_MW', 'Active Power_MW', 'MW', 'P', 'Megawatt', 'Photovoltaic');
insert into baseUnit VALUES (9, 'Energy', 'Energy', 'kWh', '', 'Kilowatt hour', '');
insert into baseUnit VALUES (10, 'Reactive Power', 'Reactive Power', 'Var', 'Q', 'short of', '');
insert into baseUnit VALUES (11, 'Reactive Power_kVar', 'Reactive Power_kVar', 'kVar', 'Q', 'thousand shortages', '');
insert into baseUnit VALUES (12, 'Reactive Energy', 'Reactive Energy', 'Varh', '', 'when not in use', 'Reactive power');
insert into baseUnit VALUES (13, 'Reactive Energy_kVarh', 'Reactive Energy_kVarh', 'kVarh', '', 'time', '');
insert into baseUnit VALUES (14, 'Apparent Power', 'Apparent Power', 'VA', 'S', 'Voltaic', '');
insert into baseUnit VALUES (15, 'Apparent Power_kVA', 'Apparent Power_kVA', 'kVA', 'S', 'kVA', '');
insert into baseUnit VALUES (16, 'Temperature', 'Temperature', '℃', '', 'Degrees Celsius', 'If the information is in Fahrenheit, the protocol needs to be converted to Celsius');
insert into baseUnit VALUES (17, 'Humidity', 'Humidity', '%RH', '', 'Relative Humidity', '');
insert into baseUnit VALUES (18, 'Rotational Speed', 'Rotational Speed', 'r/min', '', 'Revolutions per minute', 'Fan speed');
insert into baseUnit VALUES (19, 'Pressure', 'Pressure', 'kPa', '', 'Kilopascal', 'Compressor X pressure, oil pressure, pressure, stress');
insert into baseUnit VALUES (20, 'Pressure_Mpa', 'Pressure_Mpa', 'MPa', '', 'MPa', 'Condenser water inlet pressure, unified by agreement, similar equipment, signals using a');
insert into baseUnit VALUES (21, 'Power', 'Power', 'N', '', 'Newton', 'Force, gravity');
insert into baseUnit VALUES (22, 'Capacity', 'Capacity', 'L', '', 'liter', 'Fuel remaining capacity');
insert into baseUnit VALUES (23, 'Battery Capacity', 'Battery Capacity', 'Ah', '', 'Anshi', '');
insert into baseUnit VALUES (24, 'percentage', 'percentage', '%', '', 'Load factor', '');
insert into baseUnit VALUES (25, 'Proportional Band', 'Proportional Band', '', '', '', '');
insert into baseUnit VALUES (26, 'Year', 'Year', 'Y', '', 'Date: Year', '');
insert into baseUnit VALUES (27, 'Month', 'Month', 'M', '', 'Date: Month', '');
insert into baseUnit VALUES (28, 'Day', 'Day', 'D', '', 'Date: Day', '');
insert into baseUnit VALUES (29, 'Hour', 'Hour', 'h', '', 'Time: Hours', 'Cumulative running time');
insert into baseUnit VALUES (30, 'Minute', 'Minute', 'min', '', 'Time: minute species', 'Battery back-up time');
insert into baseUnit VALUES (31, 'Second', 'Second', 'sec', '', 'Time: seconds', 'Compressor restart protection time');
insert into baseUnit VALUES (32, 'Resistance', 'Resistance', 'kΩ', 'R', 'Thousands of Euros', '');
insert into baseUnit VALUES (33, 'Battery Internal Resistance', 'Battery Internal Resistance', 'mΩ', '', 'Mio', 'Battery internal resistance');
insert into baseUnit VALUES (34, 'Capacity', 'Capacity', 'μF', 'C', 'Micromethod', '');
insert into baseUnit VALUES (35, 'Inductance', 'Inductance', 'H', '', 'Henry', '');
insert into baseUnit VALUES (36, 'Length', 'Length', 'm', '', 'm', 'Length, height');
insert into baseUnit VALUES (37, 'Weight', 'Weight', 'kg', '', 'kg/kg', '');
insert into baseUnit VALUES (38, 'Illuminance', 'Illuminance', 'lx', '', 'lux', 'Measure the luminous flux per unit area');
insert into baseUnit VALUES (39, 'Luminous Flux', 'Luminous Flux', 'lm', '', 'Lumen', '');
insert into baseUnit VALUES (40, 'Number of', 'Number of', 'pcs', '', '', 'individual');
insert into baseUnit VALUES (41, 'Solar Irradiance', 'Solar Irradiance', 'W/m2', '', '', '');
insert into baseUnit VALUES (42, 'Wind Speed', 'Wind Speed', 'm/s', '', 'm/s', '');
insert into baseUnit VALUES (43, 'Electromagnetic Torque', 'Electromagnetic Torque', 'Nm', '', 'Nm', '');
insert into baseUnit VALUES (44, 'Volume', 'Volume', 'm3', '', 'cubic meters', 'Volume, air volume');
insert into baseUnit VALUES (45, 'Battery Conductance', 'Battery Conductance', 'S', '', 'Siemens', 'Battery conductivity');
insert into baseUnit VALUES (46, 'Power Generation', 'Power Generation', 'MWh', '', 'Megawatt hour', 'Accumulated power');
insert into baseUnit VALUES (47, 'Smoke Density', 'Smoke Density', '%obs/m', '', '', 'Percentage of light obscured by smoke particles at a distance of one unit meter');
insert into baseUnit VALUES (48, 'Step', 'Step', 'Step', '', '', 'Throw cut steps');
insert into baseUnit VALUES (49, 'Coefficient', 'Coefficient', '', '', '', 'Scale factor');


-- 系统基本cron表达式
insert into goCronExpression VALUES (1, '0/1 * * * * ? *', 'Every(1).Second()', 'Every 1 second', 'Every second');
insert into goCronExpression VALUES (2, '0/5 * * * * ? ', 'Every(5).Seconds()', 'Every 5 seconds', 'Every 5 seconds');
insert into goCronExpression VALUES (3, '0/10 * * * * ? ', 'Every(10).Seconds()', 'Every 10 seconds', 'Every 10 seconds');
insert into goCronExpression VALUES (4, '0/15 * * * * ? ', 'Every(15).Seconds()', 'Every 15 seconds', 'Every 15 seconds');
insert into goCronExpression VALUES (5, '0/30 * * * * ? ', 'Every(30).Seconds()', 'Every 30 seconds', 'Every 30 seconds');
insert into goCronExpression VALUES (6, '0 0/1 * * * ? *', 'Every(1).Minute()', 'Every minute', 'Every minute');
insert into goCronExpression VALUES (7, '0 0/5 * * * ? *', 'Every(5).Minutes()', 'Every 5 minutes', 'Every 5 minutes');
insert into goCronExpression VALUES (8, '0 0/10 * * * ? *', 'Every(10).Minutes()', 'Every 10 minutes', 'Every 10 minutes');
insert into goCronExpression VALUES (9, '0 0/15 * * * ? *', 'Every(15).Minutes()', 'Every 15 minutes', 'Every 15 minutes');
insert into goCronExpression VALUES (10, '0 0/30 * * * ? *', 'Every(30).Minutes()', 'Every 30 minutes', 'Every 30 minutes');
insert into goCronExpression VALUES (11, '0 0 0/1 * * ? *', 'Every(1).Hour()', 'Every hour', 'Every hour');
insert into goCronExpression VALUES (12, '0 0 0/12 * * ? *', 'Every(12).Hours()', 'Every 12 hours', 'Every 12 hours');
insert into goCronExpression VALUES (13, '0 0 0 * * ? *', 'Every(1).Day().At(00:00)', 'Every day in the early morning', '@every 30s at 00:00');
insert into goCronExpression VALUES (14, '0 0 12 * * ? *', 'Every(1).Day().At(12:00)', '12:00 noon daily', '@every 30s at 12:00');
insert into gocronexpression values (15, '0 0 0/1 * * ? *', 'reporttimingtask','Hourly', null);
insert into gocronexpression values (16, '0 0 0/12 * * ? *', 'reporttimingtask','Every 12 hours', null);
insert into gocronexpression values (17, '0 0 0 * * ? *', 'reporttimingtask','Daily', null);
insert into gocronexpression values (18, '0 0 0 ? * 2/7', 'reporttimingtask','Weekly', null);
insert into gocronexpression values (19, '0 0 0 1 1/1 ?', 'reporttimingtask','Monthly', null);
insert into gocronexpression values (20, '0 0 1 * * ? *', 'Every(1).Day().At(01:00)','Every day at 1am', null);