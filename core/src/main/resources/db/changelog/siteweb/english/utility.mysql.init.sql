INSERT INTO systemconfig VALUES(1, 'menuPermission.enable', 'true', 1, 'Whether to enable menu display permission verification');
INSERT INTO systemconfig VALUES(2, 'max.concurrentLogin.user.count', '0', 1, 'Maximum number of concurrent users (requires reboot to take effect after modification)');
INSERT INTO systemconfig VALUES(3, 'password.effective.days', '90', 7, 'Password valid time (days)');
INSERT INTO systemconfig VALUES(4, 'password.expiring.warn.days', '7', 7, 'Password change reminder days');
INSERT INTO systemconfig VALUES(5, 'password.expiring.warn.enable', 'false', 7, 'Whether to enable front-end password change reminder and back-end password validation');
INSERT INTO systemconfig VALUES(6, 'password.history.save.counts', '5', 7, 'The number of times a single user''s history password is saved, 0 means no history password is saved');
INSERT INTO systemconfig VALUES(7, 'themeName.global.default', 'blue', 1, 'Global default theme name (optional values: blue, classic, sky), if the user theme has been set, the user theme prevail');
INSERT INTO systemconfig VALUES(8, 'batchAlarmWindow.alarm.severity', '1,2,3,4', 9, 'Alarm levels in the batch unacknowledged alarm pop-up box (multiple alarm levels are separated by half commas)');
INSERT INTO systemconfig VALUES(9, 'system.scene.type', '1', 1, '1-IDC,2- Nets');
INSERT INTO systemconfig VALUES(11, 'sms.message.batch.apiURL', 'http://127.0.0.1:12300/api/notify/smssend', 21, 'SMS gateway service to send bulk SMS URL');
INSERT INTO systemconfig VALUES(12, 'alarmbox.message.apiURL', 'http://127.0.0.1:12300/api/notifyalarmbox/alarmsend', 22, 'Notification gateway service sends alarm box notification URL');
INSERT INTO systemconfig VALUES(13, 'phoneSms.message.apiURL', 'http://127.0.0.1:12300/api/notify/phonesend', 21, 'Notification gateway service sends phone voice (SMS) notification URL');
INSERT INTO systemconfig VALUES(14, 'cas.user.defaultOrganizationIdAndRoleId', '', 26, 'CAS user default department ID and role ID (separated by a half comma)');
INSERT INTO systemconfig VALUES(15, 'ad.authenticate.enable', 'false', 26, 'Whether to enable Microsoft AD domain authentication');
INSERT INTO systemconfig VALUES(16, 'ldap.user.defaultDepartmentIdAndRoleId', '', 26, 'AD domain user default department ID and role ID (separated by a half comma)');
INSERT INTO systemconfig VALUES(21, 'global.search.device.alarm', 'true', 1, 'Global search for alarming devices');
INSERT INTO systemconfig VALUES(22, 'equimentControlShow', 'true', 12, 'Single device configuration control list to be displayed or not');
INSERT INTO systemconfig VALUES(23, 'batchAlarmWindow.show', 'false', 9, 'Whether to display the batch unconfirmed alarm pop-up box');
INSERT INTO systemconfig VALUES(24, 'alarmstatistics.show', 'true', 9, 'Whether to display the global alarm statistics bar');
INSERT INTO systemconfig VALUES(25, 'realtimecapacity.refresh.time', '30', 14, 'Real-time capacity page refresh interval');
-- INSERT INTO systemconfig VALUES(31, 'notificationhost.alarm.severity', '', 8, 'The alarm level that triggered the alarm notification (multiple alarm levels are separated by half commas)');
-- INSERT INTO systemconfig VALUES(32, 'notificationhost.alarm.status', '', 8, 'Alarm status to trigger alarm notification (1 for alarm start, 2 for alarm end)');
-- INSERT INTO systemconfig VALUES(33, 'tts.message.contentTemplate', '', 10, 'TTS voice notification alert message content template');
-- INSERT INTO systemconfig VALUES(34, 'tts.message.endAlarmContentTemplate', '', 10, 'TTS voice notification alert end message content template');
-- INSERT INTO systemconfig VALUES(35, 'tts.firstpushalarm.enable', 'true', 10, 'TTS does not broadcast alerts for activity at login');
-- INSERT INTO systemconfig VALUES(36, 'alarmtts.enable', 'false', 10, 'Whether to enable web-based TTS voice alert notification');
-- INSERT INTO systemconfig VALUES(37, 'tts.speakRepeatTimes', '1', 10, 'TTS voice single alarm message broadcast times');
INSERT INTO systemconfig VALUES(38, 'battery.string.environmenthumidity', '{"1101":"1101003001","1103":"","503":""}', 11, 'Ambient humidity of battery pack');
INSERT INTO systemconfig VALUES(39, 'battery.string.ambienttemperature', '{"1101":"1101001001","1103":"","503":""}', 11, 'Ambient temperature of battery pack');
INSERT INTO systemconfig VALUES(40, 'battery.string.totalvoltage', '{"1101":"1101170001","1103":"1103172001","503":"503172001"}', 11, 'Total battery pack voltage');
INSERT INTO systemconfig VALUES(41, 'battery.string.totalcurrent', '{"1101":"1101174001","1103":"1103174001","503":"503174001"}', 11, 'Total battery pack current');
INSERT INTO systemconfig VALUES(42, 'battery.cell.internalresistance', '{"1101":"1101310","1103":"1103310","503":"503310"}', 11, '%02d#Monomeric internal resistance');
INSERT INTO systemconfig VALUES(43, 'battery.cell.temperature', '{"1101":"1101193","1103":"","503":"503181"}', 11, '%02d#Monomer temperature');
INSERT INTO systemconfig VALUES(44, 'battery.cell.temperaturelow', '{"1101":"1101320","1103":"1103333","503":"503343"}', 11, '%02d#Monomer temperature is too low');
INSERT INTO systemconfig VALUES(45, 'battery.cell.temperaturehigh', '{"1101":"1101193","1103":"1103332","503":"503193"}', 11, '%02d#Monomer temperature is too high');
INSERT INTO systemconfig VALUES(46, 'battery.cell.voltage.12V', '{"1101":"1101191","1103":"1103191","503":"503191"}', 11, '%02d#Single 12V voltage');
INSERT INTO systemconfig VALUES(47, 'battery.cell.voltagelow.12V', '{"1101":"1101191","1103":"1103191","503":"503191"}', 11, '%02d#Monoblock 12V voltage is too low');
INSERT INTO systemconfig VALUES(48, 'battery.cell.voltagehigh.12V', '{"1101":"1101352","1103":"1103355","503":"503352"}', 11, '%02d#High voltage of single 12V');
INSERT INTO systemconfig VALUES(49, 'battery.cell.voltage.6V', '{"1101":"1101309","1103":"1103309","503":"503309"}', 11, '%02d#Monoblock 6V voltage');
INSERT INTO systemconfig VALUES(50, 'battery.cell.voltagelow.6V', '{"1101":"1101354","1103":"1103354","503":"503354"}', 11, '%02d#Single 6V voltage is too low');
INSERT INTO systemconfig VALUES(51, 'battery.cell.voltagehigh.6V', '{"1101":"1101353","1103":"1103353","503":"503353"}', 11, '%02d#Single 6V voltage is too high');
INSERT INTO systemconfig VALUES(52, 'battery.cell.voltage.2V', '{"1101":"1101192","1103":"1103192","503":"503179"}', 11, '%02d#Monoblock 2V voltage');
INSERT INTO systemconfig VALUES(53, 'battery.cell.voltagelow.2V', '{"1101":"1101192","1103":"1103192","503":"503382"}', 11, '%02d#Single 2V voltage is too low');
INSERT INTO systemconfig VALUES(54, 'battery.cell.voltagehigh.2V', '{"1101":"1101355","1103":"1103352","503":"503381"}', 11, '%02d#Single 2V voltage is too high');
INSERT INTO systemconfig VALUES(55, 'battery.string.dischargepower', '{"1101":"1101318001","1103":"1103318001","503":"503400001"}', 11, 'Battery pack discharge power');
INSERT INTO systemconfig VALUES(56, 'battery.string.totalsoc', '{"1101":"1101176001","1103":"","503":"503176001"}', 11, 'Total SOC of battery pack');
INSERT INTO systemconfig VALUES(57, 'battery.cell.soc', '{"1101":"1101315","1103":"","503":""}', 11, '%02d#Monomer SOC');
INSERT INTO systemconfig VALUES(58, 'battery.dischargeevent.basetypeid', '{"1101":"1101197001","1103":"","503":""}', 11, 'Battery discharge alarm');
INSERT INTO systemconfig VALUES(59, 'battery.dischargeevent.fixedDelay', '6', 11, 'Battery discharge cycle storage cycle (multiples of 5) default：5s × 6 = 30s');
-- INSERT INTO systemconfig VALUES(60, 'battery.dischargeevent.signalbasetypeid', '1101197001', 11, '电池放电信号基类');
INSERT INTO systemconfig VALUES(61, 'navigate.to.battery', 'true', 11, 'True indicates a jump to the battery discharge curve interface, while false indicates otherwise');
INSERT INTO systemconfig VALUES(62, 'battery.dischargeeven.max.duration', '60', 11, 'Maximum battery discharge duration (minutes)');
INSERT INTO systemconfig VALUES(63, 'battery.workstatus.signalbasetypeid', '', 11, 'Battery operating status signal base class ID');
INSERT INTO systemconfig VALUES(70, 'battery.relevance.config', '0', 11, 'Battery overview histogram color association settings： 0：Turn off the associated alarm display effect 1：Red when there is an alarm 2：Related alarm level display when there is an alarm');
INSERT INTO systemconfig VALUES(71, 'bigscreen.roomuindexrate.top', '10', 13, 'Server room u-position utilization rate topN');
INSERT INTO systemconfig VALUES(72, 'bigscreen.rackuindexrate.top', '10', 13, 'Rack u-position utilization rate topN');
INSERT INTO systemconfig VALUES(73, 'bigscreen.rackchangerecord.top', '100', 13, 'Recent rack change records topN');
INSERT INTO systemconfig VALUES(74, 'spring.mail.host', 'smtp.test.com', 3, 'Mail service ip');
INSERT INTO systemconfig VALUES(75, 'spring.mail.port', '465', 3, 'Mail service port');
INSERT INTO systemconfig VALUES(76, 'spring.mail.username', '<EMAIL>', 3, 'Default system email address');
INSERT INTO systemconfig VALUES(77, 'spring.mail.password', '123456789', 3, 'Default system mailbox password');
INSERT INTO systemconfig VALUES(78, 'spring.mail.properties.mail.smtp.auth', 'true', 3, 'Whether to use SMTP authentication');
INSERT INTO systemconfig VALUES(79, 'spring.mail.protocol', 'smtp', 3, 'Mailing Protocol');
INSERT INTO systemconfig VALUES(80, 'spring.mail.defaultEncoding', 'UTF-8', 3, 'Mail encoding method');
INSERT INTO systemconfig VALUES(81, 'report.timeJob.export.mode', '1', 15, 'Timed report export mode，1：Horizontal，0-Vertical');
INSERT INTO systemconfig VALUES(82, 'report.excel.export.cell.width.setting', 'false', 15, 'Report export excel cells whether to use the front-end customColumnWidths parameter');
INSERT INTO systemconfig VALUES(83, 'report.excel.export.cell.height.setting', 'false', 15, 'Whether the report export excel cells use the customColumnHeights parameter passed from the front end');
INSERT INTO systemconfig VALUES(84, 'report.chart.anchor', 'false', 15, 'Range tracing points of the legend');
INSERT INTO systemconfig VALUES(85, 'logo.img', 'TMlogo.png', 27, 'logo.png');
INSERT INTO systemconfig VALUES(86, 'logo.text', 'SiteWeb6', 27, 'IDC Data Center');
INSERT INTO systemconfig VALUES(87, 'website.tab.text', 'SiteWeb Infrastructure Management System V6.0', 27, NULL);
INSERT INTO systemconfig VALUES(88, 'website.tab.icon', '', 27, NULL);
INSERT INTO systemconfig VALUES(89, 'theme', 'default', 27, 'Default theme');
INSERT INTO systemconfig VALUES(90, 'complexindex.http.url', 'http://siteweb-indicators:8300/api/', 16, 'Indicator Services url');
INSERT INTO systemconfig VALUES(91, 'complexindex.syn.enable', 'true', 16, 'Indicator synchronization configuration switch');
INSERT INTO systemconfig VALUES(92, 'batchconfig.batteryandhvc.enable', 'false', 1, 'System switch, whether to release the battery and high-voltage DC type of batch dynamic configuration');
INSERT INTO systemconfig VALUES(93, 'connecterror.soundpop.enable', 'true', 1, 'System switch, after a connection error, whether to sound and pop-up windows');
INSERT INTO systemconfig VALUES(94, 'system.language.type', 'en_US', 1, 'System language type en_US:English，zh_CN:Chinese');
INSERT INTO systemconfig VALUES(95, 'max.try.login.count', '3', 7, 'Maximum number of login attempts');
INSERT INTO systemconfig VALUES(96, 'login.freeze.time', '600', 7, 'Login freeze time (sec)');
INSERT INTO systemconfig VALUES(97, 'batchtool.statesignal.enable', 'false', 17, 'Does the BA splitting tool display device communication status signals');
INSERT INTO systemconfig VALUES(98, 'map.online', 'true', 18, 'Online and offline modes for e-maps');
INSERT INTO systemconfig VALUES(99, 's2.alarm.showAdvice', 'true', 18, 'Expert advice switch for telecom scenario event view page');
INSERT INTO systemconfig VALUES(100, 'report.auditreport.enable', 'true', 15, 'Whether to open the audit statement');
INSERT INTO systemconfig VALUES(101, 'report.auditreport.level', '1,2,3,4', 15, 'Audit statement audit level 1 minimal level, 2 basic level, 3 detailed level, 4 not specified');
INSERT INTO systemconfig VALUES(102, 'report.auditreport.maxcount', '10000', 15, 'Maximum number of audit statement records');
INSERT INTO systemconfig VALUES(103, 'report.securityreport.enable', 'true', 15, 'Whether to open the security log report');
INSERT INTO systemconfig VALUES(104, 'report.securityreport.maxcount', '10000', 15, 'Maximum number of records in the security log report');
INSERT INTO systemconfig VALUES(105, 'account.login.timeSpan.enable', 'false', 23, 'Whether to open the login account access time period function');
INSERT INTO systemconfig VALUES(106, 'login.ip.filterpolicy.enable', 'false', 23, 'Whether to enable login IP filtering policy');
INSERT INTO systemconfig VALUES(108, 'zoom.enable', 'true', 12, 'Whether to turn on the system zoom function');
INSERT INTO systemconfig VALUES(109, 'loading.bar.show', 'false', 1, 'Request progress bar display switch');
INSERT INTO systemconfig VALUES(110, 'user.noaction.logout', 'false', 1, 'User does not operate whether to exit');
INSERT INTO systemconfig VALUES(111, 'user.noactionlogout.span', '5', 1, 'User exit time without operation (in minutes)');
INSERT INTO systemconfig VALUES(112, 'cas.login.enable', 'false', 26, 'Whether to enable CAS login authentication');
INSERT INTO systemconfig VALUES(113, 'cas.login.redirect.url', '', 26, 'URL to jump to after CAS server login');
INSERT INTO systemconfig VALUES(114, 'cas.logout.redirect.url', '', 26, 'URL of the jump after logging out of CAS server');
INSERT INTO systemconfig VALUES(115, 'menu.collapsible', 'true', 1, 'Left menu retractable');
INSERT INTO systemconfig VALUES(116, 'system.tag', 'siteweb6', 1, 'System Flag：s2/siteweb6');
INSERT INTO systemconfig VALUES(117, 'configure.breadcrumb.state', 'false', 12, 'Component Navigation Bar Switch Status');
INSERT INTO systemconfig VALUES(118, 'ubit.powerelectricity.show', 'false', 13, 'Whether the rack U bit utilization rate displays the rated power and power consumption columns');
INSERT INTO systemconfig VALUES(119, 'important.event.condition', '10:Poweroff,37:Leak,34:Hightemp,47:LowBatVoltage', 18, 'Telecom board power failure, water immersion, high temperature, low battery voltage event category id');
INSERT INTO systemconfig VALUES(120, 'user.online.expire.time', '30', 1, 'User online failure time (s)');
INSERT INTO systemconfig VALUES(121, 'security.file.integrity.enable', 'false', 23, 'Service file integrity self check switch');
INSERT INTO systemconfig VALUES(122, 'siteweb6.appsidecar.url', 'http://localhost:8000/api/v1/', 1, 'Appsidecar url');
INSERT INTO systemconfig VALUES(123, 'robot.remoteControl.compId', '2062', 19, 'Robot remote control control id');
INSERT INTO systemconfig VALUES(124, 'robot.chart.compId', '2060;2061;2059', 19, 'Robot body statistics; Environmental statistics; Equipment statistics compId');
INSERT INTO systemconfig VALUES(125, 'alarm.video.window.show', 'false', 20, 'Alarm video linkage switch');
INSERT INTO systemconfig VALUES(126, 'alarm.refresh.span', '2', 9, 'Refresh interval of alarm page, unit: second');
INSERT INTO systemconfig VALUES(127, 'realmonitor.refresh.span', '3', 1, 'Real time monitoring page, active alarm, configuration alarm and signal refresh interval, unit: second');
INSERT INTO systemconfig VALUES(128, 'graphic.pageTemplateCategory', '2', 12, 'Template classification of device configuration templates (1 is similar to S3 style, 2 is reconstructed style, and 3 is World Device Configuration Standard)');
INSERT INTO systemconfig VALUES(129, 'alarm.history.tab.show', 'false', 9, 'Whether to display historical alarm tab');
INSERT INTO systemconfig VALUES(130, 'global.search.device', 'false', 1, 'Global Search Device');
INSERT INTO systemconfig VALUES(131, 'backup.id', '1', 23, 'System backup task ID, data backup index of server management console, starting from 1');
INSERT INTO systemconfig VALUES(132, 'keycloak.user.defaultDepartmentIdAndRoleId', '', 26, 'Keycloak User default department ID and role ID (separated by half comma)');
INSERT INTO systemconfig VALUES(133, 'security.file.integrity.interval', '600', 23, 'Service file integrity self verification cycle time (s)');
INSERT INTO systemconfig VALUES(134, 'menu.routing.guard.enable', 'false', 1, 'Whether the route guard function of the front end menu is enabled');
INSERT INTO systemconfig VALUES(135, 'report.securityreport.sms.enable', 'false', 15, 'Enable SMS sending of security report');
INSERT INTO systemconfig VALUES(136, 'comp.devicename.length', '10', 12, 'Component the display length of the device name');
INSERT INTO systemconfig VALUES(137, 'report.column.name.format', 'equipmentName-signalName', 15, 'Report column name display：equipmentName:Equipment name，signalName:Signal Name');
INSERT INTO systemconfig VALUES(138, 'security.gateway.heartbeat.url', '', 23, 'Security authentication notification gateway heartbeat interface url');
INSERT INTO systemconfig VALUES(139, 'security.gateway.heartbeat.interval', '0', 23, 'Security authentication notification gateway heartbeat interface polling cycle (s)');
INSERT INTO systemconfig VALUES(140, 'security.violence.login.duration', '300', 23, 'Security authentication: brute force login duration (s)');
INSERT INTO systemconfig VALUES(141, 'alarm.video.window.keepAlive', 'false', 20, 'Whether the alarm linkage video pop-up window is alive');
INSERT INTO systemconfig VALUES(142, 'report.history.signal.limit', '20000', 15, 'Report history signal limit number');
INSERT INTO systemconfig VALUES(143, 'alarm.pagelock.span', '30', 9, 'The default duration when the page is locked by the alarm (seconds)');
INSERT INTO systemconfig VALUES(144, 'departmentPermission.enable', 'false', 1, 'enable department authority verification');
INSERT INTO systemconfig VALUES(145, 'end.alarm.pop.trigger.cron', '', 1, 'Unfinished alarm timing pop-up trigger time');
INSERT INTO systemconfig VALUES(146, 'notification.getui.allowEquipmentCategory', '11,12,13,17,18,20', 25, 'getui-Equipment classification conditions that allow push');
INSERT INTO systemconfig VALUES(147, 'notification.getui.allowEventCategory', '1,2,3,4,5,6,7,8,9,10', 25, 'getui-Event classification conditions that allow push');
INSERT INTO systemconfig VALUES(148, 'alarmNotification.mobile.getui.enable', 'false', 25, 'getui-Alarm Notification Push');
-- INSERT INTO systemconfig VALUES(149, 'tts.circularbroadcast.enable', 'false', 10, 'Whether TTS broadcasts active alarms cyclically');
-- INSERT INTO systemconfig VALUES(150, 'tts.confirmnobroadcast.enable', 'false', 10, 'Whether TTS confirms not to broadcast alarms');
-- INSERT INTO systemconfig VALUES(151, 'baControlCommandWebsocketPushEnable', 'false', 1, 'ba control command websocket push');
INSERT INTO systemconfig VALUES(152, 'report.generalCustomParser.activeSignal.ignoreUnit', 'false', 1, 'common use Custom Report active Signal ignore Unit');
INSERT INTO systemconfig VALUES(153, 'fiveMinutes.storage.database', 'false', 15, 'Whether to use the five-minute repository by default to query historical signal data');
INSERT INTO systemconfig VALUES(154, 'system.isSC', '0', 1, 'Whether it is the mode of the second-level simulation of the third-level');
INSERT INTO systemconfig VALUES(155, 'report.chart.type', '', 1, 'Historical data report and historical indicator report export legend, the default is the original echart type, 1 means use echart, 2 means use legend');
INSERT INTO systemconfig VALUES(156, 'weCom.apply.apiURL', 'http://127.0.0.1:12300/api/notify/wechatsend', 22, 'Notification gateway enterprise WeChat application notification URL');
INSERT INTO systemconfig VALUES(157, 'dcom.http.url', '', 1, 'dcom interface http URL，example：http://10.169.42.160:3001/api');
INSERT INTO systemconfig VALUES(158, 'alarmlight.message.apiURL', 'http://127.0.0.1:12300/api/notifyalarmbox/alarmsend', 22, 'Notification gateway service sends alarm light notification URL');
INSERT INTO systemconfig VALUES(159, 'libattery.safealarm.basetypeids', '', 1, 'lithium battery gassing alarm baseTypeIds');
INSERT INTO systemconfig VALUES(160, 'libattery.thermal.runaway.temperatureIncrementRateThreshold', '4', 1, 'lithium battery temperature increment rate threshold');
INSERT INTO systemconfig VALUES(161, 'libattery.thermal.runaway.temperatureDeviationThreshold', '', 1, 'lithium battery temperature deviation threshold');
INSERT INTO systemconfig VALUES(162, 'libattery.thermal.runaway.voltageDeviationThreshold', '', 1, 'lithium battery voltage deviation threshold');
INSERT INTO systemconfig VALUES(163, 'system.token.header', 'Authorization', 1, 'System-customized token request header (restart is required after modification to take effect)');
INSERT INTO systemconfig VALUES(164, 'report.history.signal.maxcount', '2000', 1, 'Query the maximum number of signals in the historical signal report');
INSERT INTO systemconfig VALUES(165, 'computerrack.airspace.height', '0', 13, 'Rack Management，Air exchange U height');
INSERT INTO systemconfig VALUES(166, 'alarmlist.static.showName', 'false', 9, 'Check whether the grade name is displayed in the statistics bar on the alarm management page');
INSERT INTO systemconfig VALUES(167, 'account.terminalDevice.bind.enable','false',23,'Whether to open the account terminal device bind function');
INSERT INTO systemconfig VALUES(168, 'app.video.type','1',1,'APP Video Type：1-S6V2 Video Platform');
INSERT INTO systemconfig VALUES(169, 's2.graphicPage.standardAlarmName','false',9,'In the telecom scenario configuration page, does the alarm-related interface only count standardized alarms');
INSERT INTO systemconfig VALUES(170, 'device.group.alarm.color', 'false', 1, 'Set individual red and different alarm colors');
INSERT INTO systemconfig VALUES(171, 'alarm.video.window.type', 1, 20, 'video platform type：1-PC Client,2-video platform,3-video2.0');
INSERT INTO systemconfig VALUES(172, 'login.token.expiration', '', 1, 'Login token expiration time (hours)');
INSERT INTO systemconfig VALUES(173, 'login.inactive.lock', '', 1, 'Inactive account lockout (hours)');
INSERT INTO systemconfig VALUES(174, 'ali.cycle.storage.interval.seconds', '10', 1, 'Customize Alibaba alert cycle storage interval time, define how many seconds between each storage.');
INSERT INTO systemconfig VALUES(175, 'ali.cycle.storage.max.period', '1800', 1, 'Max storage time for unfinished Alibaba alerts, in seconds');
INSERT INTO systemconfig VALUES(176, 'ali.cycle.storage.enable', 'false', 1, 'Alibaba alert history storage switch');
INSERT INTO systemconfig VALUES(177, 'tts.http.span', '10000', 1, 'TTS HTTP polling interval (ms)');
INSERT INTO systemconfig VALUES(178, 'spring.mail.properties.mail.smtp.ssl.enable', 'true', 3, 'Whether to enable email SSL encryption (configure to true if required by the server)');
INSERT INTO systemconfig VALUES(179, 'spring.mail.properties.mail.smtp.starttls.enable', 'true', 3, 'Whether to enable message STARTTLS encryption (configure to true if required by the server)');
INSERT INTO systemconfig VALUES(180, 'external.api.limit', '50000', 28, 'Limits on external API calls');
INSERT INTO systemconfig VALUES(181, 'battery.equipmentbasetype.config', '1101,1103,503', 11, 'Battery device base class type configuration, determined to support battery types such as 1101, 1103, 503');
INSERT INTO systemconfig VALUES(182, 'preAlarm.notify.enable', 'false', 1, 'PreAlarm Warning voice notification switch');
INSERT INTO systemconfig VALUES(183, 'prealarm.notify.confirmednotify', 'false', 1, 'Warning voice notification when logging in to confirm whether to notify the warning');
INSERT INTO systemconfig VALUES(184, 'report.exportTips.enable', 'true', 15, 'Whether to prompt that the output parameters are not configured when querying');
INSERT INTO systemconfig VALUES(185, 'app.menuPermission.enable', 'true', 1, 'Whether to enable APP menu (module entrance) permission verification');
INSERT INTO systemconfig VALUES(186, 'utag.bindpercent.hide', 'false', 13, 'Whether to hide Utag Bind Percent');
INSERT INTO systemconfig VALUES(187, 'tts.queue.show', 'true', 1, 'Whether to show tts queue');
INSERT INTO systemconfig VALUES(188, 'spring.mail.subject', 'Alarm notification', 3, 'Alarm notification email subject');
INSERT INTO systemconfig VALUES(189, 'ali.signal.basetypeid.enable', 'true', 1, 'Whether to enable Alibaba signal base type ID');
INSERT INTO systemconfig VALUES(190, 'alarms.end.ignore', 'false', 9, 'Global Statistics Bar: Ignore Ended Alarms');
INSERT INTO systemconfig VALUES(191, 'report.generalCustomParser.headerTimeFormat.month', 'MM', 1, 'Universal Customized Report - Header Month Time Format');
INSERT INTO systemconfig VALUES(192, 'report.generalCustomParser.headerTimeFormat.day', 'MMdd', 1, 'General custom report - header date and time format');
INSERT INTO systemconfig VALUES(193, 'lark.apply.apiURL', 'http://127.0.0.1:12300/api/notify/feishusend', 22, 'Notification Gateway Lark App URL');
INSERT INTO systemconfig VALUES(194, 'global.statistics.alarm.show', 'true', 9, 'Display global alarm statistics or not');
INSERT INTO systemconfig VALUES(195, 'global.statistics.alarm.severity', '1,2,3,4', 9, 'Alarm levels shown in alarm statistics');
INSERT INTO systemconfig VALUES(196, 'deploy.s2.enable', 'false', 9, 'Has the s2 backend been deployed');
INSERT INTO systemconfig VALUES(197, 'versionManager.fsuBackup.enable', 'false', 1, 'Whether to enable the backup function for the fsu home directory');
INSERT INTO systemconfig VALUES(198, 'patrol.timerTaskExpiredDaysEx', '31', 1, 'Clear automatic inspection-abnormal recording time range from 1-62');
INSERT INTO systemconfig VALUES(199, 'patrol.timerTaskExpiredDaysAll', '365', 1, 'Clear automatic inspection-full recording time range is 1-365');
INSERT INTO systemconfig VALUES(200, 'bytedance.capacityscreen.top', '5', 14, 'Byte Capacity Management Expo Board topN');
INSERT INTO systemconfig VALUES(201, 'udevice.restoreDelay.second', '150', 13, 'Search for the time (in seconds) to restore the U-position device indicator light on IT Device');
INSERT INTO systemconfig VALUES(202, 'forget.password.enable', 'false', 7, 'Forget Password Enable');
INSERT INTO systemconfig VALUES(203, 'alarmstatistics.s2Alarm.needsound', 'false',1, 'Whether Enable S2 Global Alarm Broadcast');
INSERT INTO systemconfig VALUES(204, 'global.alarm.template.broadcast', 'false', 1, 'Whether S2 Global Alarm Broadcast According To Selected Template(Default According To Default Template)');
INSERT INTO systemconfig VALUES(205, 'graphic.page.edit.users.enable', 'false', 12, 'Whether Enable Graphic Page Online Edit Users Count');
INSERT INTO systemconfig VALUES(206, 'zoom.scale.threshold', '0.005', 12, 'Prevent screen flickering caused by automatic redirection to configuration page size; do not adjust if below this precision');
INSERT INTO systemconfig VALUES(210, 'assetDevice.extField.equipment.LinkageModifyAssetInfo.enable', 'true', 1, 'Is the device type of asset extension field linked to modify asset information');