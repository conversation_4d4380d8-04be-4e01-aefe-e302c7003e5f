# 图表接口
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(25, 'No API', 'Currency', '/', 'GET', '[]', 'return [["区域","A楼","B楼","C楼"],["停电站点数",600,800,50],["正常电站点数",330,1000,80]];');

INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(27, 'Station/FSU/Device Outage Statistics', 'Carrier Multisite', '/api/interruptstatistics', 'GET', '[]', 'const count = data && data.station && data.station.count || 0;
const interrupt = data && data.station && data.station.interrupt || 0;
return count + ''/'' + interrupt;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(28, 'Power consumption analysis', 'Carrier Multisite', '/api/energy/total/useElectricityTrendAnalysis?startTime={startTime}&endTime={endTime}&businessTypeId=2&timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "genericList", "meaning": "时间频率", "required": true, "component": "", "customData": "[{\\"label\\":\\"month\\",\\"value\\":\\"m\\"},{\\"label\\":\\"day\\",\\"value\\":\\"d\\"}]", "defaultValue": ""}, {"name": "startTime,endTime", "array": false, "source": "component", "meaning": "length of time", "required": true, "component": "TimeRangeComponent", "customData": "", "defaultValue": ""}]', 'const result = [[''alarmCount''].concat(data.xaxisData)];
data.series.map(item=>{
  let arr = [];
  arr.push(item.name);
  arr = arr.concat(item.data)
  result.push(arr)
})
return result;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(29, 'Multiple Shift Group Duty Staff', 'Currency', '/api/shiftgroupmap/duty?shiftGroupIds={ids}', 'GET', '[{\"name\": \"ids\", \"array\": true, \"source\": \"input\", \"meaning\": \"Shift Group id(Multiple commas separated and filled in sequence)\", \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"1,2\"}]', 'data.map((item,i)=>{\n  if(!item.employeeTitle) data[i].employeeTitle = 0;\n})\ndata.sort(function(a,b){\n  if(a.employeeTitle<b.employeeTitle) return 1;\n  if(a.employeeTitle>b.employeeTitle) return -1;\n  if(a.employeeTitle=b.employeeTitle) return 0;\n})\nconst list = util.formatToTable(data, [\'number\',\'employeeName\',\'contact way 1\',\'contact way 2\'], [\'displayIndex\',\'employeeName\',\'phone\',\'mobile\'])\nreturn list;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(30, 'Service Status', 'Serve', 'https://**************:8000/api/v1/software?pageIndex=1&pageSize=999', 'GET', '[]', 'const type = {
  0: ''does not exist'',
  1: ''created'',
  2: ''running'',
  3: ''pause'',
  4: ''restarting'',
  5: ''migrating'',
  6: ''stop'',
  7: ''die''
}
const arr = data.data.list;
arr.map(item => item.currentState = type[item.Status]);
const list = util.formatToTable(arr, [''number'',''service name'',''current status''], [''softwareId'',''softwareAlias'',''currentState''])
return list;');
INSERT INTO chartapi (ApiId, ApiName, Category, Url, `Method`, ParamSchema, `Transform`) VALUES(31, 'Equipment Quantity Statistics', 'Currency', '/api/equipmentcategorystatisticsstate?deviceCategoryIds=1503,1504&objectId=517000001&pageCategory=2', 'GET', '[]', '
const list = util.formatToTable(data, [''number'',''count'',], [''name'',''value'',])
return list;
return util.arrArrFormatTime2(list, ''$M:$d'');
');

INSERT INTO `chartapi` VALUES(32,'Summary of authorized base stations PUE','energy','/api/energy/energyapi/telcom/PUEOfArea?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','return 3')
,(33,'The total amount of each type of energy consumption','energy','/api/energy/energyapi/telcom/electCategoryOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','const arr = [data];\nconst list = util.formatToTable(arr, [\'市电\',\'油机\',\'绿电\'], [\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\nreturn list;')
,(34,'Total electricity consumption and year-on-year comparison','energy','/api/energy/energyapi/telcom/totalEnergyUseOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.totalElectricityValue;\n\n//return data.yoyTotalElectricityValue;\n\n//return data.qoqTotalElectricityValue;')
,(35,'Carbon emissions and year-on-year comparison','energy','/api/energy/energyapi/telcom/totalCarbonUseOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','return data.totalCarbonValue;\n\n')
,(36,'Total carbon emissions of each type of energy use','energy','/api/energy/energyapi/telcom/carbonCategoryOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.cityElectValue;\n\n//return data.oilElectValue;\n\n//return data.greenElectValue;\n')
,(37,'Total carbon and energy consumption of a base station','energy','/api/energy/energyapi/telcom/oneRoomEnergyAndCarbon?resourceStructureId={resourceStructureId}','GET','[{\"name\": \"resourceStructureId\", \"array\": false, \"source\": \"input\", \"meaning\": \"节点ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.energyTotal;\n//\n\n//return data.carbonTotal;')
,(38,'The proportion of electricity consumption','energy','/api/energy/energyapi/telcom/electCategoryProportion?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\n\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式\'], [\'displayIndex\',\'employeeName\',\'phone\'])//二参是自定义表头\nreturn list;\n')
,(39,'Lower-level energy consumption carbon list interface','energy','/api/energy/energyapi/telcom/nextLevelCarbonRank?timeType={timeType}&userId={userId}','GET','[{\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}, {\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\nreturn list;\n')
,(40,'Annual trend of carbon emissions by energy consumption type','energy','/api/energy/energyapi/telcom/carbonCategoryTrend?userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'时间\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'time\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(41,'Summary PUE trend of authorized base stations','energy','/api/energy/energyapi/telcom/PUEOfAreaTrend?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"userId\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"timeType\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','\n// 第三种情况,接口返回数据data格式是对象的数组,如下\n// [{displayIndex:1, employeeName:\"a接口用户\", phone: 123456},\n// {displayIndex:2, employeeName:\"b接口用户\", phone: 654321},\n// {displayIndex:3, employeeName:\"c接口用户\", phone: 112233},]\nconst list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(42,'The annual trend of the total amount of each type of energy consumption','energy','/api/energy/energyapi/telcom/energyCategoryTrend?userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}]','\n//const list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式\'], [\'displayIndex\',\'employeeName\',\'phone\'])//二参是自定义表头\n//如果要对时间进行格式化\n//return util.arrArrFormatTime2(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n\n//堆叠图\n//const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\n//return list;\n\n//趋势图\n//const list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\n//return util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n\n\nconst list = util.formatToTable(data, [\'时间\',\'市电\',\'油机发电\',\'绿电\'], [\'time\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\n')
,(43,'Total carbon emissions of each type of energy use','energy','/api/energy/energyapi/telcom/carbonCategoryOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','const arr = [data];\nconst list = util.formatToTable(arr, [\'市电碳排放\',\'油机碳排放\',\'绿电碳抵消\'], [\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\nreturn list;\n')
,(44,'Lower level energy ranking','energy','/api/energy/energyapi/telcom/nextLevelEnergyRank?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','const list = util.formatToTable(data, [\'层级\',\'市电\',\'油机发电\',\'绿电\'], [\'resourceStructureName\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\nreturn list;\n')
,(45,'Carbon Offset YoY','energy','/api/energy/energyapi/telcom/greenCarbonUseOfRole?timeType={timeType}&userId={userId}','GET','[{\"name\": \"userId\", \"array\": false, \"source\": \"session\", \"meaning\": \"人员ID\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"personId\"}, {\"name\": \"timeType\", \"array\": false, \"source\": \"input\", \"meaning\": \"时间类型\", \"children\": [], \"required\": true, \"component\": \"\", \"customData\": \"\", \"defaultValue\": \"\"}]','// 第一种情况,接口返回数据data格式是二维数组,如下\n// [[\"区域\",\"测试\"],[\"停电站点数\",0]]\nreturn data.greenElectCarbonValue;\n\n');


# 图表样式
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(1, 'Foundation Histogram', 1, 'barchart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x-axis axis color
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x-axis text
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y-axis text
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//split line
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//legend
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
  series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(2, 'Horizontal Histogram', 1, 'horizontalbarchart.png', 'option = {
	grid: {
		bottom: 80
	},
	yAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x-axis axis color
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x-axis text
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	xAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y-axis text
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//split line
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//legend
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(3, 'Stack Bar Chart', 1, 'stackbarchart.png', 'option = {
    grid: {
        bottom: 80
    },
    xAxis: {
        type: ''category'',
        data: null,
        show: true,
        axisTick: { show: false },
        boundaryGap: true,
        silent: true,
        axisLine: {//axis
            show: true,
            lineStyle: {
                color: ''#BFDFFF'', //x-axis axis color
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//x-axis text
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
        }
    },
    yAxis: {
        type: ''value'',
        show: true,
        splitNumber: 4,
        max: null,
        min: 0,
        silent: true,
        textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
        axisLine: {//axis
            show: true,
            lineStyle: {
                color: ''#BFDFFF'',
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//y-axis text
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
        },
        splitLine: {//split line
            lineStyle: {
                width: 1,
                color: ''#BFDFFF'',
                opacity: 0.1
            }
        }
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''axis'',
        position: '''',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//legend
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40,
    },
    series: [],
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(4, 'Column Chart', 1, 'linebarchart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x-axis axis color
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x-axis text
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y-axis text
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//splint line
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//legend
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(5, 'Stacked Column Chart', 1, 'stackbarlinechart.png', 'option = {
    grid: {
        bottom: 80
    },
    xAxis: {
        type: ''category'',
        data: null,
        show: true,
        axisTick: { show: false },
        boundaryGap: true,
        silent: true,
        axisLine: {//axis
            show: true,
            lineStyle: {
                color: ''#BFDFFF'', //x-axis axis color
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//x-axis text
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
        }
    },
    yAxis: {
        type: ''value'',
        show: true,
        splitNumber: 4,
        max: null,
        min: 0,
        silent: true,
        textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
        axisLine: {//axis
            show: true,
            lineStyle: {
                color: ''#BFDFFF'',
                opacity: 0.2,
                width: 1
            }
        },
        axisLabel: {//y-axis text
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
        },
        splitLine: {//split line
            lineStyle: {
                width: 1,
                color: ''#BFDFFF'',
                opacity: 0.1
            }
        }
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''axis'',
        position: '''',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//legend
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40,
    },
    series: [],
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(6, 'Double y-axis Of Folded Column Chart', 1, 'linebarchartdoubleyaxis.png', 'option = {
  grid: {
    bottom: 80
  },
  xAxis: {
    type: ''category'',
    data: null,
    show: true,
    axisTick: { show: false },
    boundaryGap: true,
    silent: true,
    axisLine: {//axis
      show: true,
      lineStyle: {
        color: ''#BFDFFF'', //x-axis axis color
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//x-axis text
      show: true,
      overflow: ''break'',
      width: null,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13,
      ellipsis: ''...''
    }
  },
  yAxis: [{
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 1000,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//axis
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y-axis text
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//split line
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }, {
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 100,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//axis
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y-axis text
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//splint line
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }],
	dataZoom: [],
  dataset: {
    source: [
    ]
  },
  tooltip: {
    show: true,
    trigger: ''item'',
    position: ''top'',
    textStyle: {
      color: ''#bfdfff'',
      fontSize: 12
    },
    backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
    borderWidth: 1,
    borderColor: ''rgba(0,127,255,0.3)'',
    padding: null,
    axisPointer: {
      type: "none"
    },
  },
  legend: {//legend
    type: "scroll",
    pageIconColor: ''#aaa'',
    pageIconInactiveColor: ''#2f4554'',
    pageTextStyle: {
      color: "#aaa"
    },
    show: true,
    itemHeight: 5,
    bottom: 15,
    itemGap: 40,
  },
  series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(7, 'Stacked Column Chart With Double y-axis', 1, 'stackbarlinechartdoubleyaxis.png', 'option = {
  grid: {
    bottom: 80
  },
  xAxis: {
    type: ''category'',
    data: null,
    show: true,
    axisTick: { show: false },
    boundaryGap: true,
    silent: true,
    axisLine: {//axis
      show: true,
      lineStyle: {
        color: ''#BFDFFF'', //x-axis axis color
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//x-axis text
      show: true,
      overflow: ''break'',
      width: null,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13,
      ellipsis: ''...''
    }
  },
  yAxis: [{
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 1000,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//axis
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y-axis text
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//split line
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }, {
    type: ''value'',
    show: true,
    splitNumber: 4,
    max: 100,
    min: 0,
    silent: true,
    textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
    axisLine: {//axis
      show: true,
      lineStyle: {
        color: ''#BFDFFF'',
        opacity: 0.2,
        width: 1
      }
    },
    axisLabel: {//y-axis text
      show: true,
      color: ''#BFDFFF'',
      fontFamily: '''',
      fontSize: 13
    },
    splitLine: {//split line
      lineStyle: {
        width: 1,
        color: ''#BFDFFF'',
        opacity: 0.1
      }
    }
  }],
  dataZoom: [],
  dataset: {
    source: [
    ]
  },
  tooltip: {
    show: true,
    trigger: ''axis'',
    position: '''',
    textStyle: {
      color: ''#bfdfff'',
      fontSize: 12
    },
    backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
    borderWidth: 1,
    borderColor: ''rgba(0,127,255,0.3)'',
    padding: null,
    axisPointer: {
      type: "none"
    },
  },
  legend: {//legend
    type: "scroll",
    pageIconColor: ''#aaa'',
    pageIconInactiveColor: ''#2f4554'',
    pageTextStyle: {
      color: "#aaa"
    },
    show: true,
    itemHeight: 5,
    bottom: 15,
    itemGap: 40,
  },
  series: [],
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(8, 'Normal Pie Chart', 2, 'piechart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//legend
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(9, 'Nightingale Rose Pie Chart', 2, 'rosepiechart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//legend
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(10, 'Donut Chart', 2, 'ringchart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//legend
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(11, 'Nightingale''s Rose Circle', 2, 'roseringchart.png', 'option = {
    grid: {
        bottom: 80
    },
    dataZoom: [],
    dataset: {
        source: [
        ]
    },
    tooltip: {
        show: true,
        trigger: ''item'',
        position: ''top'',
        textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
        },
        backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
        borderWidth: 1,
        borderColor: ''rgba(0,127,255,0.3)'',
        padding: null,
        axisPointer: {
            type: "none"
        },
    },
    legend: {//legend
        type: "scroll",
        pageIconColor: ''#aaa'',
        pageIconInactiveColor: ''#2f4554'',
        pageTextStyle: {
            color: "#aaa"
        },
        show: true,
        itemHeight: 5,
        bottom: 15,
        itemGap: 40
    },
    series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(12, 'Normal Line Chart', 0, 'linechart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x-axis axis color
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x-axis text
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//轴线
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y-axis text
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//split line
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//legend
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(13, 'Area Chart', 0, 'arealinechart.png', 'option = {
	grid: {
		bottom: 80
	},
	xAxis: {
		type: ''category'',
		data: null,
		show: true,
		axisTick: { show: false },
		boundaryGap: true,
		silent: true,
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'', //x-axis axis color
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//x-axis text
			show: true,
			overflow: ''break'',
			width: null,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13,
			ellipsis: ''...''
		}
	},
	yAxis: {
		type: ''value'',
		show: true,
		splitNumber: 4,
		max: null,
		min: 0,
		silent: true,
		textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
		axisLine: {//axis
			show: true,
			lineStyle: {
				color: ''#BFDFFF'',
				opacity: 0.2,
				width: 1
			}
		},
		axisLabel: {//y-axis text
			show: true,
			color: ''#BFDFFF'',
			fontFamily: '''',
			fontSize: 13
		},
		splitLine: {//split line
			lineStyle: {
				width: 1,
				color: ''#BFDFFF'',
				opacity: 0.1
			}
		}
	},
	dataZoom: [],
	dataset: {
		source: [
		]
	},
	tooltip: {
		show: true,
		trigger: ''item'',
		position: ''top'',
		textStyle: {
			color: ''#bfdfff'',
			fontSize: 12
		},
		backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
		borderWidth: 1,
		borderColor: ''rgba(0,127,255,0.3)'',
		padding: null,
		axisPointer: {
			type: "none"
		},
	},
	legend: {//legend
		type: "scroll",
		pageIconColor: ''#aaa'',
		pageIconInactiveColor: ''#2f4554'',
		pageTextStyle: {
			color: "#aaa"
		},
		show: true,
		itemHeight: 5,
		bottom: 15,
		itemGap: 40,
	},
	series: [],
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(14, 'Normal Dashboard', 5, 'gaugechart.png', 'option = {
  series: [
    {
      name: ''Pressure'',
      type: ''gauge'',
      radius: ''100%'',
      center: [''50%'', ''60%''],
      startAngle: 180,
      endAngle: 0,
      splitNumber: 5,
      min: 1,
      max: 200,
      data:[],
      pointer: {
        length: ''70%'',
        width: 4,
        offsetCenter: [0, 0]
      },
      progress: {
        show: false,
        overlap: false,
        roundCap: false,
        clip: false,
        itemStyle: {
          borderWidth: 1,
          borderColor: ''#464646''
        }
      },
      axisTick: {
        show: true,
        length: 10,
        splitNumber: 5,
        distance: 0,
        lineStyle: {
          width: 1,
          color: ''#ffffff''
        }
      },
      axisLabel: {
        show: true,
        distance: 50,
        textStyle: {
          fontSize: 14,
          color: ''#3895ca''
        }
      },
      splitLine: {
        show: true,
        length: 25,
        distance: -27.5,
        lineStyle: {
          width: 1,
          color: ''#ffffff'',
          opacity: 1
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          width: 30,
          color: [
            [
              1,
              {
                colorStops: [//The color and percentage of the outermost circle of the dial
                  {
                    offset: 0,
                    color: ''#20c374''
                  },
                  {
                    offset: 0.5,
                    color: ''#FFFF00''
                  },
                  {
                    offset: 1,
                    color: ''#d15233''
                  }
                ],
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                type: ''linear'',
                global: false
              }
            ]
          ]
        }
      },
      anchor: {
        showAbove: true,
        size: 9.5,
        itemStyle: {
          borderColor: ''rgba(251, 251, 251, 1)'',
          color: ''rgba(0, 0, 0, 1)'',
          borderWidth: 2.5
        }
      },
      title: {
        show: true,
        textStyle: {
          fontSize: 30,
          color: ''#FFFFFF''
        }
      },
      detail: {
        show: true,
        formatter: ''{value}'',
        fontSize: 30,
        color: ''#FFD237''
      }
    }
  ]
};
');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(15, 'Basic Radar Chart', 4, 'radarchart.png', 'option = {
  legend: {
    right: ''0%'',
    textStyle: {
      color: ''#BFDFFF''
    },
    padding: [0, 0, 100, 0],
    data: []
  },
  tooltip: {
    show: true
  },
  radar: {
    indicator: [],
    axisName: {
      color: ''#BFDFFF'',
      fontSize: 12
    },
    axisLine: {
      lineStyle: {
        color: ''#BFDFFF'',
        fontSize: 1,
        opacity: 0.5
      }
    },
    splitLine: {
      lineStyle: {
        color: ''#BFDFFF'',
        fontSize: 2
      }
    }
  },
  series: [
    {
      type: ''radar'',
      data: []
    },
  ]
};');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(16, 'Common Form', 6, 'table.png', '');
INSERT INTO chartstyle (StyleId, StyleName, ChartId, Thumbnail, Expression) VALUES(17, 'Text Number', 3, 'textnumber.png', '');

# 图表主题
INSERT INTO charttheme (ThemeId, themeName, themeCode, themeData, themeDefault) VALUES(1, 'Default', 'default', '{"bar": {"itemStyle": {"barBorderColor": "#ccc", "barBorderWidth": 0}}, "geo": {"label": {"color": "#000"}, "emphasis": {"label": {"color": "rgb(100,0,0)"}, "itemStyle": {"areaColor": "rgba(255,215,0,0.8)", "borderColor": "#444", "borderWidth": 1}}, "itemStyle": {"areaColor": "#eee", "borderColor": "#444", "borderWidth": 0.5}}, "map": {"label": {"color": "#000"}, "emphasis": {"label": {"color": "rgb(100,0,0)"}, "itemStyle": {"areaColor": "rgba(255,215,0,0.8)", "borderColor": "#444", "borderWidth": 1}}, "itemStyle": {"areaColor": "#eee", "borderColor": "#444", "borderWidth": 0.5}}, "pie": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "line": {"smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderWidth": 1}, "lineStyle": {"width": 2}, "symbolSize": 4}, "color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"], "gauge": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "graph": {"color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"], "label": {"color": "#eee"}, "smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderColor": "#ccc", "borderWidth": 0}, "lineStyle": {"color": "#aaa", "width": 1}, "symbolSize": 4}, "radar": {"smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderWidth": 1}, "lineStyle": {"width": 2}, "symbolSize": 4}, "title": {"textStyle": {"color": "#464646"}, "subtextStyle": {"color": "#6E7079"}}, "funnel": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "legend": {"textStyle": {"color": "#333"}}, "sankey": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "boxplot": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "logAxis": {"axisLine": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": true, "lineStyle": {"color": ["#E0E6F1"]}}}, "scatter": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "toolbox": {"emphasis": {"iconStyle": {"borderColor": "#666"}}, "iconStyle": {"borderColor": "#999"}}, "tooltip": {"axisPointer": {"lineStyle": {"color": "#ccc", "width": 1}, "crossStyle": {"color": "#ccc", "width": 1}}}, "dataZoom": {"textStyle": {}, "handleSize": "undefined%"}, "parallel": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "timeAxis": {"axisLine": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": false, "lineStyle": {"color": ["#E0E6F1"]}}}, "timeline": {"label": {"color": "#A4B1D7"}, "emphasis": {"label": {"color": "#A4B1D7"}, "itemStyle": {"color": "#FFF"}, "controlStyle": {"color": "#A4B1D7", "borderColor": "#A4B1D7", "borderWidth": 1}}, "itemStyle": {"color": "#A4B1D7", "borderWidth": 1}, "lineStyle": {"color": "#DAE1F5", "width": 2}, "controlStyle": {"color": "#A4B1D7", "borderColor": "#A4B1D7", "borderWidth": 1}, "checkpointStyle": {"color": "#316bf3", "borderColor": "fff"}}, "markPoint": {"label": {"color": "#eee"}, "emphasis": {"label": {"color": "#eee"}}}, "textStyle": {}, "valueAxis": {"axisLine": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": true, "lineStyle": {"color": ["#E0E6F1"]}}}, "visualMap": {"color": ["#bf444c", "#d88273", "#f6efa6"]}, "candlestick": {"itemStyle": {"color": "#eb5454", "color0": "#47b262", "borderColor": "#eb5454", "borderWidth": 1, "borderColor0": "#47b262"}}, "categoryAxis": {"axisLine": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": false, "lineStyle": {"color": ["#E0E6F1"]}}}, "backgroundColor": "rgba(0, 0, 0, 0)"}', 1);
