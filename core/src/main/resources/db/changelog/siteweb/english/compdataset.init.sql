/* 数据源 */
insert into `compdataset` (`CompDataSetId`, `CompDataSetName`, `Url`) values('1','Station Equipment List','stationpage/equipmentlist');
-- insert into `compdataset` (`CompDataSetId`, `CompDataSetName`, `Url`) values('2','局站关注信号列表','stationpage/focussignal');
insert into `compdataset` (`CompDataSetId`, `CompDataSetName`, `Url`) values('2','Bureau station key signal list','realtime/importantsignal?stationId=');


/* 表 */
insert into `compdatatable` (`CompDataTableId`, `CompDataTableName`, `CompDataSetId`) values('1','Equipment List','1');
-- insert into `compdatatable` (`CompDataTableId`, `CompDataTableName`, `CompDataSetId`) values('2','关注信号列表','2');
insert into `compdatatable` (`CompDataTableId`, `CompDataTableName`, `CompDataSetId`) values('2','List of key signals','2');


/* 列字段 */
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('1','1','Equipment name','equipmentName','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('2','1','Equipment Type','equipmentType','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('3','1','Shield status','masked','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('4','1','Online Status','onlineStatus','1');

/*INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('5','2','','signalType','1');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('6','2','设备名称','equipmentName','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('7','2','信号名称','signalName','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('8','2','信号值','signalValue','2');*/

INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('5','2','','signalCategory','1');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('6','2','','eventSeverity','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('7','2','Equipment name','equipmentName','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('8','2','signalName','signalName','2');
INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('9','2','signalMeaning','signalMeaning','2');
-- INSERT INTO `compdatacolumn` (`CompDataColumnId`, `CompDataTableId`, `ColumnName`, `ColumnField`, `ValueType`) VALUES('10','2','','unit','2');

/* 皮肤 */
insert into `compskin` (`CompSkinId`, `CompDataSetId`, `SkinName`, `Content`) values('1','1','Station Equipment List',NULL);
-- insert into `compskin` (`CompSkinId`, `CompDataSetId`, `SkinName`, `Content`) values('2','2','局站关注信号列表',NULL);
insert into `compskin` (`CompSkinId`, `CompDataSetId`, `SkinName`, `Content`) values('2','2','Bureau station key signal list',NULL);