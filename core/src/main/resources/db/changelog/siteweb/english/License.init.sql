-- 试用版license
INSERT INTO license(LicenseId,Product, licensetype, ActiveTime, LimitTime)
VALUES (1,'SiteWeb Infrastructure Management System V6.0', 1, NOW(), DATE_ADD(NOW(), INTERVAL 3 MONTH ));

-- 模块信息
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (1,'SiteWeb6 Basic Functions',1,'[{"Key":"equipmentNum","Val":"50"}]');
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (2,'3D Digital Tools',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (3,'Temperature Cloud Map',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (4,'Robot Monitoring',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (5,'HVAC operational status management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (6,'Battery Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (7,'Video Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (8,'Telephone voice notification',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (9,'Voice box SMS notification',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (10,'Alarm box notification',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (11,'Capacity Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (12,'Power Distribution Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (13,'Energy consumption management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (14,'Early Warning Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (15,'Rack Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (16,'Asset Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (17,'Air conditioning group control',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (18,'Control linkage',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (19,'Access Control Management',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (20,'AI Energy Saving in Computer Room',0,'[{"Key":"AccessQuantity","Val":"0"},{"Key":"ExpiryDate","Val":"2000/01/01"}]');
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (21,'Site AI energy saving',0,'[{"Key":"AccessQuantity","Val":"0"},{"Key":"ExpiryDate","Val":"2000/01/01"}]');
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (22,'Central air-conditioning AI energy saving',0,'[{"Key":"AccessSysQuantity","Val":"0"},{"Key":"AccessRoomQuantity","Val":"0"},{"Key":"ExpiryDate","Val":"2000/01/01"}]');