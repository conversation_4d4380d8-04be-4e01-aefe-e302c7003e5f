
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(1, 'alarmtts.enable', 'false', 'Whether to enable web-based TTS voice alert notification');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(2, 'notificationhost.alarm.severity', '','The alarm level that triggered the alarm notification (multiple alarm levels are separated by half commas)');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(3, 'notificationhost.alarm.status', '', 'Alarm status to trigger alarm notification (1 for alarm start, 2 for alarm end)');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(4, 'tts.circularbroadcast.enable', 'false', 'Whether TTS broadcasts active alarms cyclically');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(5, 'tts.confirmnobroadcast.enable', 'false', 'Whether TTS confirms not to broadcast alarms');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(6, 'tts.firstpushalarm.enable', 'true', 'TTS does not broadcast alerts for activity at login');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(7, 'tts.message.contentTemplate', '', 'TTS voice notification alert message content template');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(8, 'tts.message.endAlarmContentTemplate', '', 'TTS voice notification alert end message content template');
INSERT INTO ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) VALUES(9, 'tts.speakRepeatTimes', '1', 'TTS voice single alarm message broadcast times');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(10,'tts.filter.position','','The level ID that triggers tts voice (separate multiple levels with half-width commas)');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(11,'tts.filter.baseType','','Alarm base class ID that triggers TTS voice (multiple alarm base classes are separated by half-width commas)');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(12,'tts.filter.baseEquipmentId','','The device base class ID that triggers TTS voice (multiple device base classes are separated by half-width commas)');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(13,'tts.filter.event','','Alarm ID that triggers TTS voice (separate multiple alarms with half-width commas)');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(14,'tts.filter.equipment','','The device ID that triggered TTS voice (separate multiple devices with half-width commas)');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(15,'tts.endnobroadcast.enable','','The alarm is not reported whether the TTS has ended');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(16,'tts.projectstatus.enable','','Project status');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(17,'tts.speakRate','1','TTS Speak Rate');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(18,'tts.speakVolume','1','TTS Speak Volume');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(19,'tts.filter.keyWord','','TTS Filter KeyWord');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(20,'tts.sortby.eventlevel','false','TTS Sortby Event Level');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(21,'prealarm.notify.confirmednotify','','TTS alert confirmed not to broadcast');
insert into ttsconfig(TtsConfigId,TtsConfigKey,TtsConfigValue,Description) values(22,'preAlarm.notify.enable','false','Does the TTS prioritize playback according to the alert level');