-- ============================================
-- ============== 容量基类定义 ================
-- ============================================
INSERT INTO CapacityBaseType(BaseTypeId, BaseTypeName, Description) VALUES (1000000,'Rack Capacity','Rack management, 3D, U position, capacity business');
INSERT INTO CapacityBaseType(BaseTypeId, BaseTypeName, Description) VALUES (1000001,'Power Capacity','Distribution capacity warning business');


-- ============================================
-- ============ 容量基类属性定义 ==============
-- ============================================


-- 机架管理 容量相关  2000001
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2000000,'U Position usage capacity' ,1000000,'rackSpace',  0,'U','Rack U position occupancy capacity');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2000001,'Structural load-bearing capacity',1000000,'rackWeight', 0,'kg','Rack weight load-bearing capacity');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2000002,'Refrigeration cooling capacity',1000000,'rackCooling',0,'kJ','Rack cooling cooling capacity');
-- 配电容量预警相关   2010000
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010000,'Power load capacity',1000001,'comPower',   1,'kVA','Common device apparent power load capacity');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010001,'Voltage load capacity',1000001,'comVoltage', 1,'V','Universal device voltage load capacity');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010002,'Current load capacity',1000001,'comCurrent', 1,'A','Generic device current load capacity');

-- INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010003,'视在功率容量',1000001,'comApparentPower', 1,'kVA','通用的设备视在功率负载容量');