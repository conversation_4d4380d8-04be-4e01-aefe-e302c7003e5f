-- 空调标准信号
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 1, 'Working Current', 'A', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 2, 'Temperature', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 3, 'Humidity', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 4, 'Power On Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 5, 'Power Off Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 6, 'Heating Mode Or Not', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 7, 'Cooling Mode Or Not', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 8, 'Abnormal Working Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 9, 'Compressor Current Abnormal Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 10, 'Aircon Anti-theft Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 11, 'Over Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 12, 'Temperature And Humidity Sensor Fault Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 13, 'Remote Power On', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 14, 'Remote Power Off', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 15, 'Set Heating Mode', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 16, 'Set Cooling Mode', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 17, 'Operating Temperature Setting Value', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 18, 'Operating Temperature Setting Command', '℃', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 19, 'Remote Control Fan Speed', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 20, 'Frequency Conversion Or Not', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 21, 'Upper Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 22, 'Lower Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (1, 23, 'Aircon Frequency', '', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 1, 'Dedicated Aircon Temperature', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 2, 'Dedicated Aircon Humidity', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 3, 'Power On Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 4, 'Power Off Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 5, 'Temperature Setting Value', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 6, 'Humidity Setting Value', '%', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 7, 'Low Air Flow Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 8, 'High Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 9, 'Compressor 1 High Pressure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 10, 'Compressor 2 High Pressure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 11, 'Compressor 1 Low Pressure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 12, 'Compressor 2 Low Pressure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 13, 'Compressor 1 Overheat Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 14, 'Compressor 2 Overheat Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 15, 'Compressor 1 Overhload Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 16, 'Compressor 2 Overload Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 17, 'System Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 18, 'Humidifier Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 19, 'Heater Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 20, 'Overflow Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 21, 'External Flooding Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 22, 'Electric Heating Over Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 23, 'Remote Power Off Failure', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 24, 'Lock Failure', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 25, 'Water Flow Loss Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 26, 'Fan Fault Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 27, 'Remote Power On', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 28, 'Remote Power Off', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 29, 'Reboot Mode', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 30, 'Temperature Setting Value', '℃', ' ', 4, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 31, 'Humidity Setting Value', '%Rh', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 32, 'Cooling Proportional Band', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 33, 'Heating Proportional Band', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 34, 'Humidification Proportional Band', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 35, 'Dehumidification Proportional Band', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 36, 'Core Center Aircon Performance Pre-alarm', ' ', 'Equipment Performance Warning', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 37, 'Frequency Conversion Or Not', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 38, 'Upper Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 39, 'Lower Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (2, 40, 'Aircon Frequency', '', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 1, 'Aircon 1 Temperature', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 2, 'Aircon 1 Humidity', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 3, 'Aircon 1 Power On Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 4, 'Aircon 1 Power Off Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 5, 'Aircon 1 Heating Mode Or Not', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 6, 'Aircon 1 Cooling Mode Or Not', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 7, 'Aircon 1 Working Abnormal Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 8, 'Aircon 1 Over Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 9, 'Aircon 1 Temperature And Humidity Sensor Failure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 10, 'Aircon 1 Remote Power On', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 11, 'Aircon 1 Remote Power Off', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 12, 'Aircon 1 Set Heating Mode', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 13, 'Aircon 1 Set Cooling Mode', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 14, 'Aircon 1 Working Temperature Setting', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 15, 'Aircon 1 Operating Temperature Setting', '℃', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 16, 'Aircon 1 Remote Control Fan Speed', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 17, 'Aircon 2 Temperature', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 18, 'Aircon 2 Humidity', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 19, 'Aircon 2 Power On Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 20, 'Aircon 2 Power Off Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 21, 'Aircon 2 Heating Mode Or Not', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 22, 'Aircon 2 Cooling Mode Or Not', ' ', ' ', 2, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 23, 'Aircon 2 Working Abnormal Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 24, 'Aircon 2 Over Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 25, 'Aircon 2 Temperature And Humidity Sensor Failure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 26, 'Aircon 2 Remote Power On', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 27, 'Aircon 2 Remote Power Off', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 28, 'Aircon 2 Set Heating Mode', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 29, 'Aircon 2 Set Cooling Mode', ' ', ' ', 3, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 30, 'Aircon 2 Working Temperature Setting', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 31, 'Aircon 2 Operating Temperature Setting', '℃', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 32, 'Aircon 2 Remote Control Fan Speed', ' ', ' ', 4, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 33, 'Aircon 1 Frequency Conversion Or Not', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 34, 'Aircon 1 Upper Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 35, 'Aircon 1 Lower Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 36, 'Aircon 1 Frequency', '', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 37, 'Aircon 2 Frequency Conversion Or Not', ' ', '1', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 38, 'Aircon 2 Upper Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 39, 'Aircon 2 Lower Limit Of Operating Frequency', '', '2', 6, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (3, 40, 'Aircon 2 Frequency', '', ' ', 1, 1, 0, 0, 0);

-- 温湿度采集设备标准信号
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 1, 'Ambient Temperature', '℃', ' ', 1, 1, 1, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 2, 'Ambient Humidity', '%Rh', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 3, 'Flooding Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 4, 'Smoke Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 5, 'Fire Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 6, 'Infrared Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 7, 'Over Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 8, 'Low Temperature Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 9, 'Over Humidity Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (4, 10, 'Low Humidity Alarm', ' ', ' ', 5, 1, 0, 0, 0);

-- 风机设备标准信号
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 1, 'Room Temperature', '℃', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 2, 'Outdoor Temperature', '℃', ' ', 1, 1, 1, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 3, 'Room Temperature And Humidity Sensor Failure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 4, 'Outdoor Temperature And Humidity Sensor Failure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 5, 'Inlet Fan Failure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 6, 'Outlet Fan Failure Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 7, 'Throttle Stuck Warning', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 8, 'Dirty Filter Alarm', ' ', ' ', 5, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 9, 'Force Start Fan Temperature', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 10, 'Fan Power On Temperature', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 11, 'Fan Power Off Temperature', '℃', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 12, 'Indoor And Outdoor Temperature Difference Control', ' ', ' ', 1, 1, 0, 0, 0);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 13, 'System Boot', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 14, 'System Shutdown', ' ', ' ', 3, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 15, 'Power On Status', ' ', ' ', 2, 1, 0, 0, 2);
INSERT INTO `aircon_stdsignal` (`TypeId`,`StdSignalId`,`StdSignalName`,`StdSignalUnit`,`StdSignalRemark`,`StdSignalType`,`NeedShow`,`CommandColor`,`BusinessTypeId`,`MapRequirement`)
VALUES (5, 16, 'Power Off Status', ' ', ' ', 2, 1, 0, 0, 2);

-- 标准信号的类型
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (1, 'Analog', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (2, 'Status', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (3, 'Remote Control', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (4, 'Remote Adjustment', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (5, 'Alarm', '');
INSERT INTO `aircon_stdsignaltype` (`SignalTypeId`,`SignalTypeName`,`SignalTypeRemark`) VALUES (6, 'Constant', '');

-- 标准空调类型
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (1, 'Ordinary Air Condition', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (2, 'Special Air Condition', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (3, 'Multi Split Air Conditioner System', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (4, 'Temperature And Humidity Collection Equipment', '');
INSERT INTO `aircon_stdtype` (`TypeId`,`TypeName`,`ExtendField`) VALUES (5, 'Fan Equipment', '');
