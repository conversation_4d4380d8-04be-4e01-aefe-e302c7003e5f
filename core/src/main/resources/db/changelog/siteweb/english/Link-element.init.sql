    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
        VALUES (1,'Integer addition','Integer operations',1,1,null,'+',true,1,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (2,'Integer Subtraction','Integer operations',2,1,null,'-',true,2,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (3,'Integer multiplication','Integer operations',1,1,null,'*',true,3,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (4,'Integer division','Integer operations',2,1,null,'/',true,4,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (5,'Integer modulo','Integer operations',2,1,null,'%',true,5,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (6,'Floating point addition','Floating point operations',1,1,null,'+',true,6,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (7,'Floating point subtraction','Floating point operations',2,1,null,'-',true,7,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (8,'Floating-point multiplication','Floating point operations',1,1,null,'*',true,8,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (9,'Floating point division','Floating point operations',2,1,null,'/',true,9,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (10,'>','Relational operations',2,1,null,'>',true,10,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (11,'>=','Relational operations',2,1,null,'>=',true,11,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (12,'=','Relational operations',2,1,null,'==',true,12,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (13,'<=','Relational operations',2,1,null,'<=',true,13,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (14,'<','Relational operations',2,1,null,'<',true,14,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (15,'!=','Relational operations',2,1,null,'!=',true,15,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (16,'and','Logical operations',1,1,null,'&&',true,16,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (17,'or','Logical operations',1,1,null,'||',true,17,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (18,'non','Logical operations',1,1,null,'!',true,18,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (19,'Maximum value','Aggregation functions',1,1,null,'max()',true,19,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (20,'Minimum value','Aggregation functions',1,1,null,'min()',true,20,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (21,'Average value','Aggregation functions',1,1,null,'mean()',true,21,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (31,'Event Start','Event',1,1,null,null,true,31,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (32,'Event End','Event',1,1,null,null,true,32,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (33,'Current Time','Time Components',1,1,null,null,true,33,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (34,'Time Delay','Time Components',1,1,null,null,true,34,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (41,'Parallel execution','Linkage control',1,1,null,null,true,41,null);
    -- INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    -- VALUES (42,'Execution Script','Linkage control',1,1,null,null,true,42,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (43,'Empty command','Linkage control',1,0,null,null,true,43,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (44,'Equipment Control','Linkage control',1,1,null,null,true,44,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (51,'Conditional Operations','Conditional Operations',1,2,null,'if()',true,51,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (52,'Integer constants','Constants',1,1,null,null,true,52,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (53,'Floating-point constants','Constants',1,1,null,null,true,53,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (61,'Measurement points','points',1,1,null,null,false,61,null);
    INSERT INTO `linkelement` (`ElementId`,`ElementName`,`ElementType`,`InputNodesCount`,`OutputNodesCount`,`Icon`,`Expression`,`Visible`,`SortIndex`,`Description`)
    VALUES (62,'Equipment','Equipment',0,1,null,null,false,62,null);

