-- ============================================================
-- ============== PreAlarm Category Definition ================
-- ============================================================
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (1, 'ComplexIndex', 0, 'ComplexIndex PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (2, 'Energy', 0, 'Energy PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (3, 'Capacity', 0, 'Capacity PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (4, 'CorePoint', 0, 'CorePoint PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (5, 'Battery', 0, 'Battery PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (6, 'Oil engine maintenance', 0, 'Oil engine maintenance PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (7, 'Abnormal oil engine signal', 0, 'Abnormal oil engine signal PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (8, 'Indicator expression exception', 0, 'Indicator expression exception PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (9, 'Abnormal indicator value', 0, 'Abnormal indicator value PreAlarm');
INSERT INTO PreAlarmCategory(CategoryId, CategoryName, ParentCategoryId, Description) VALUES (10, 'Equipment is out of service', 0, 'Equipment is out of service PreAlarm');

-- ============================================================
-- ============== PreAlarm Severity Definition ================
-- ============================================================
INSERT INTO PreAlarmSeverity (PreAlarmSeverityId, PreAlarmSeverityName, Color, Description) VALUES (1,'Level 1 PreAlarm','#FF2626','');
INSERT INTO PreAlarmSeverity (PreAlarmSeverityId, PreAlarmSeverityName, Color, Description) VALUES (2,'Level 2 PreAlarm','#F39924','');
INSERT INTO PreAlarmSeverity (PreAlarmSeverityId, PreAlarmSeverityName, Color, Description) VALUES (3,'Level 3 PreAlarm','#f711f4','');
INSERT INTO PreAlarmSeverity (PreAlarmSeverityId, PreAlarmSeverityName, Color, Description) VALUES (4,'Level 4 PreAlarm','#f8ff0f','');
