-- 门禁卡授权查询速度慢
CREATE INDEX idx_door_equipment ON TBL_Door (EquipmentId, DoorId);
CREATE INDEX idx_doorcard_door ON TBL_DoorCard (DoorId, CardId, TimeGroupId, TimeGroupType);
CREATE INDEX idx_doortimegroup_timegroup ON TBL_DoorTimeGroup (TimeGroupId, DoorId, TimeGroupType);

INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(1, 'Public Security', 'icon-gongan', '#ADD8E6', 'Public Security Door Access');
INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(2, 'Level One', 'icon-yiji', '#ADD8E6', 'Level One Door Access');
INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(3, 'IDC', 'icon-IDC', '#ADD8E6', 'IDC Door Access');
INSERT INTO doortag (TagId, TagName, TagIcon, TagColor, TagDescribe) VALUES(4, 'National Secret', 'icon-guomi', '#ADD8E6', 'National Secret Door Access');

INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (5,'DDS DoorAccess',NULL,10,8,4,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES(17, 'CHD200D7 DoorAccess(Child-Parent Device Integration)', NULL, 16, 10, 1, '', NULL);