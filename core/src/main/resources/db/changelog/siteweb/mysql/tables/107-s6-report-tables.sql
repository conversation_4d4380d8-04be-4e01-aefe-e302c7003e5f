CREATE TABLE `report` (
  `ReportId` int NOT NULL AUTO_INCREMENT,
  `ReportName` varchar(255) DEFAULT NULL COMMENT '报表名称',
  `ReportDescription` varchar(255) DEFAULT NULL COMMENT '报表备注信息',
  `ReportSchemaId` int DEFAULT NULL COMMENT '报表模式ID',
  `ReportSchemaCategoryId` int DEFAULT NULL COMMENT '报表Schema类型ID',
  `UpdateUserId` int DEFAULT NULL COMMENT '修改人ID',
  `UpdateTime` datetime DEFAULT NULL COMMENT '修改时间',
  `ReportDataSourceId` int DEFAULT NULL COMMENT '报表数据源ID',
  `MaxQueryInterval` int DEFAULT NULL COMMENT '报表数据源ID',
  `CreateUserId` int DEFAULT NULL COMMENT '创建报表的用户id',
  `Overt` tinyint DEFAULT NULL COMMENT '是否公开',
  `ColumnConfig` json DEFAULT NULL COMMENT '报表列配置',
  PRIMARY KEY (`ReportId`),
  UNIQUE KEY `ReportId` (`ReportId`)
);

CREATE TABLE `reportdatasource` (
  `ReportDataSourceId` int NOT NULL AUTO_INCREMENT,
  `ReportDataSourceName` varchar(128) DEFAULT NULL COMMENT '报表数据源名称',
  `ReportDataSourceDescription` varchar(255) DEFAULT NULL COMMENT '报表数据源备注',
  PRIMARY KEY (`ReportDataSourceId`),
  UNIQUE KEY `ReportDataSourceId` (`ReportDataSourceId`)
);

CREATE TABLE `reportexportparameterpreset` (
  `ReportExportParameterPresetId` int NOT NULL AUTO_INCREMENT,
  `ReportId` int DEFAULT NULL COMMENT '报表ID',
  `ReportSchemaExportParameterId` int DEFAULT NULL COMMENT '报表Schema导出参数ID',
  `display` tinyint DEFAULT NULL COMMENT '是否展示',
  PRIMARY KEY (`ReportExportParameterPresetId`),
  UNIQUE KEY `ReportExportParameterPresetId` (`ReportExportParameterPresetId`)
);

CREATE TABLE `reportparameterpreset` (
  `ReportParameterPresetId` int NOT NULL AUTO_INCREMENT,
  `ReportId` int DEFAULT NULL COMMENT '报表ID',
  `ReportSchemaQueryParameterId` int DEFAULT NULL COMMENT '报表Schema查询参数ID',
  `value` mediumtext COMMENT '值',
  `display` tinyint DEFAULT NULL COMMENT '是否展示',
  PRIMARY KEY (`ReportParameterPresetId`),
  UNIQUE KEY `ReportParameterPresetId` (`ReportParameterPresetId`)
);

CREATE TABLE `reportschema` (
  `ReportSchemaId` int NOT NULL AUTO_INCREMENT,
  `ReportSchemaName` varchar(255) DEFAULT NULL COMMENT '报表Schema名称',
  `ReportSchemaDescription` varchar(255) DEFAULT NULL COMMENT '报表Schema备注',
  `Version` varchar(32) DEFAULT NULL COMMENT '版本',
  `ReportSchemaCategoryId` int DEFAULT NULL COMMENT '报表Schema类型ID',
  `Author` varchar(128) DEFAULT NULL COMMENT '创建人',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  `ReportDataSourceId` int DEFAULT NULL COMMENT '报表数据源ID',
  `ViewControlId` int DEFAULT NULL COMMENT '是否隐藏',
  `MaxQueryInterval` int DEFAULT NULL COMMENT '最大查询间隔',
  PRIMARY KEY (`ReportSchemaId`),
  UNIQUE KEY `ReportSchemaId` (`ReportSchemaId`)
);

CREATE TABLE `reportschemacategory` (
  `ReportSchemaCategoryId` int NOT NULL AUTO_INCREMENT,
  `ReportSchemaCategoryName` varchar(128) DEFAULT NULL COMMENT '报表Schema分类名称',
  `ReportSchemaCategoryDescription` varchar(128) DEFAULT NULL COMMENT '报表Schema分类备注',
  `ReportSchemaCategoryPath` varchar(128) DEFAULT NULL,
  `SortVal` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`ReportSchemaCategoryId`),
  UNIQUE KEY `ReportSchemaCategoryId` (`ReportSchemaCategoryId`)
);

CREATE TABLE `reportschemaexportparameter` (
  `ReportSchemaExportParameterId` int NOT NULL AUTO_INCREMENT,
  `ReportSchemaExportParameterName` varchar(128) DEFAULT NULL COMMENT '报表Schema导出参数名称',
  `ReportSchemaExportParameterTitle` varchar(128) DEFAULT NULL COMMENT '报表Schema导出参数标题',
  `ReportSchemaId` int DEFAULT NULL COMMENT '报表SchemaId',
  `IsNull` tinyint DEFAULT NULL COMMENT '是否为空',
  PRIMARY KEY (`ReportSchemaExportParameterId`),
  UNIQUE KEY `ReportSchemaExportParameterId` (`ReportSchemaExportParameterId`)
);

CREATE TABLE `reportschemaqueryparameter` (
  `ReportSchemaQueryParameterId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `ReportSchemaQueryParameterName` varchar(128) DEFAULT NULL COMMENT '报表Schema查询参数名称',
  `ReportSchemaQueryParameterTitle` varchar(128) DEFAULT NULL COMMENT '报表Schema查询参数标题',
  `ReportSchemaId` int DEFAULT NULL COMMENT '报表SchemaId',
  `ParameterControlId` int DEFAULT NULL COMMENT '参数控制ID（前端）',
  `DataSourceExpression` text COMMENT '数据源表达式',
  `DataSourceReturnTableName` varchar(255) DEFAULT NULL COMMENT '数据源返回json',
  `IsNull` tinyint DEFAULT NULL COMMENT '是否为空',
  `SortIndex` int DEFAULT NULL COMMENT '展示顺序'
);

CREATE TABLE `reporttimingtaskfile` (
  `ReportTimingTaskFileId` int NOT NULL AUTO_INCREMENT,
  `ReportTimingTaskManagementId` int DEFAULT NULL COMMENT '报表定时任务管理ID',
  `reportSchemaId` int DEFAULT NULL COMMENT '报表SchemaId',
  `File` longtext COMMENT '文件内容',
  `FilePath` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ReportTimingTaskFileId`),
  UNIQUE KEY `ReportTimingTaskFileId` (`ReportTimingTaskFileId`),
  KEY `IDX_ReportTimingTaskManagementId_CreateTime` (`ReportTimingTaskManagementId`,`CreateTime`)
);

CREATE TABLE `reporttimingtaskmanagement` (
  `ReportTimingTaskManagementId` int NOT NULL AUTO_INCREMENT,
  `ReportTimingTaskManagementName` varchar(256) DEFAULT NULL COMMENT '报表定时任务管理名称',
  `ReportId` int DEFAULT NULL COMMENT '报表ID',
  `ReportName` varchar(256) DEFAULT NULL COMMENT '报表名称',
  `StorageCycle` varchar(64) DEFAULT NULL COMMENT '存储cron',
  `StartTimeType` varchar(64) DEFAULT NULL COMMENT '开始时间类型',
  `EndTimeType` varchar(64) DEFAULT NULL COMMENT '结束时间类型',
  `Status` tinyint(1) DEFAULT NULL COMMENT '状态',
  `to` varchar(1024) DEFAULT NULL COMMENT '收件人',
  `cc` varchar(1024) DEFAULT NULL COMMENT '抄送',
  `CreateUserId` int DEFAULT NULL COMMENT '创建报表的用户id',
  `Overt` tinyint DEFAULT NULL COMMENT '是否公开',
  PRIMARY KEY (`ReportTimingTaskManagementId`),
  UNIQUE KEY `ReportTimingTaskManagementId` (`ReportTimingTaskManagementId`)
);

CREATE TABLE `reporttimingtasktimetype` (
  `ReportTimingTaskTimeTypeId` int NOT NULL AUTO_INCREMENT,
  `TimeTypeName` varchar(45) DEFAULT NULL,
  `TimeTypeValue` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`ReportTimingTaskTimeTypeId`),
  UNIQUE KEY `ReportTimingTaskTimeTypeId` (`ReportTimingTaskTimeTypeId`)
);

CREATE TABLE `reportfolder` (
    `folderId` int AUTO_INCREMENT COMMENT '文件夹ID',
    `folderName` varchar(255) NOT NULL COMMENT '文件夹名称',
    `parentId` int DEFAULT 0 COMMENT '父文件夹ID，根节点为 0',
    `sortIndex` int DEFAULT NULL COMMENT '排序值',
    `createTime` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`folderId`)
);

CREATE TABLE `reportfoldermap` (
    `id` int NOT NULL AUTO_INCREMENT,
    `reportId` int NOT NULL COMMENT '报表ID',
    `folderId` int NOT NULL COMMENT '文件夹ID',
    `reportType` int DEFAULT 1 NOT NULL COMMENT '报表类型（1=普通，2=定时）',
    `sortIndex` int DEFAULT NULL COMMENT '排序值',
    PRIMARY KEY (`id`),
    UNIQUE KEY uniq_report (`reportId`, `reportType`)
);