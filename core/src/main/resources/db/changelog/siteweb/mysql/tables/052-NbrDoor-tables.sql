DROP TABLE IF EXISTS NBR_DoorDevice;

CREATE TABLE NBR_DoorDevice
(
    StationId     int null,
    HouseId     int null,
    EquipmentId   int,
    EquipmentTemplateId     int null,
    StationName         VARCHAR (255) null,
    HouseName         VARCHAR (255) null,
    EquipmentName         VARCHAR (255) null,
    Description         VARCHAR (255) null,
    Address         int null,
    SyncTime datetime null,
    WorkStationId int null,
    PRIMARY KEY (EquipmentId)
);


DROP TABLE IF EXISTS NBR_DoorSignal;

CREATE TABLE NBR_DoorSignal
(
    ChannelNo  int not null,

    IsAlarmSignal tinyint null,
    EquipmentTemplateId     int not null,
    StationId     int not null,
    EquipmentId   int not null,
    SignalId      int not null,
    SignalName     VARCHAR (255) null,
    StoreInterval       FLOAT DEFAULT ((0)) null,
    AbsValueThreshold   FLOAT DEFAULT ((0)) null,
    PercentThreshold    FLOAT DEFAULT ((0)) null,
    St<PERSON>sPeriod       INT DEFAULT ((0)) null,
    StartCompareValue FLOAT DEFAULT ((0)) NULL,

    DataType          INT NOT NULL,## 0 float, 1 string
    FloatValue        FLOAT,
    StringValue       VARCHAR (128),
    WorkStationId int null,
    CONSTRAINT PK_NBR_DoorSignal_ID PRIMARY KEY (EquipmentId,SignalId)
);


DROP TABLE IF EXISTS Nbr_CardFace;

CREATE TABLE Nbr_CardFace
(
    CardId     int not null,
    FaceId     int null,
    ControllerFaceId      VARCHAR (128) null,
    UpdateTime       VARCHAR (128) null,
    CONSTRAINT PK_Nbr_CardFace_ID PRIMARY KEY (CardId)
);

##纽贝儿我们是按照主动查询做的，下面的事件定义都是为了复用海康的告警处理逻辑添加的
DROP TABLE IF EXISTS NBR_CallBackEventDic;

CREATE TABLE NBR_CallBackEventDic
(
    Major  INT NOT NULL,  ##-告警大类，类型名与海康一致。
    MajorDesc     VARCHAR (255) null, ##告警大类名称
    Minor  INT NOT NULL,##-告警小类，
    MinorDesc     VARCHAR (255) null,##告警小类名称
    MinorHex     VARCHAR (255) null, ##小类的16进制值
    AlarmEndMinor  INT NULL,  ## 告警结束对应的小类值, -1表示不产生告警
    ChannelNo  int null, ##通道号
    PRIMARY KEY (Major,Minor)
);