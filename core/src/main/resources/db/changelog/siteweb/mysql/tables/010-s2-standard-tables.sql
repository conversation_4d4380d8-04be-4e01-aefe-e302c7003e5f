CREATE TABLE `tbl_standardback` (
  `EntryCategory` int NOT NULL,
  `EquipmentTemplateId` int NOT NULL,
  `EntryId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `SignalName` varchar(128) DEFAULT NULL,
  `StoreInterval` double DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  `PercentThreshold` double DEFAULT NULL,
  `EventName` varchar(128) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `StartCompareValue` double DEFAULT NULL,
  `StartDelay` int DEFAULT NULL,
  `StandardName` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `ControlName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`EntryCategory`,`EntryId`,`EquipmentTemplateId`,`EventConditionId`)
);

CREATE TABLE `tbl_standarddic` (
  `StandardDicId` int NOT NULL,
  `SignalStandardName` varchar(255) NOT NULL,
  `SignalStandardId` int NOT NULL,
  `EquipmentLogicClass` varchar(255) NOT NULL,
  `StoreInterval` int DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  `PercentThreshold` double DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `EventLogicClass` varchar(255) DEFAULT NULL,
  `EventClass` varchar(255) DEFAULT NULL,
  `NetManageId` varchar(255) DEFAULT NULL,
  `EquipmentAffect` varchar(255) DEFAULT NULL,
  `BusinessAffect` varchar(255) DEFAULT NULL,
  `CompareValue` varchar(128) DEFAULT NULL,
  `StartDelay` varchar(64) DEFAULT NULL,
  `ControlStandardName` varchar(255) DEFAULT NULL,
  `ControlStandardId` int DEFAULT NULL,
  `ControlType` int DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `NodeType` int DEFAULT NULL,
  PRIMARY KEY (`StandardDicId`)
);

CREATE TABLE `tbl_standarddiccontrol` (
  `StandardDicId` int NOT NULL,
  `StandardType` int NOT NULL,
  `EquipmentLogicClassId` int NOT NULL,
  `EquipmentLogicClass` varchar(128) NOT NULL,
  `ControlLogicClassId` int DEFAULT NULL,
  `ControlLogicClass` varchar(128) DEFAULT NULL,
  `ControlStandardName` varchar(255) DEFAULT NULL,
  `NetManageId` varchar(255) DEFAULT NULL,
  `StationCategory` int NOT NULL,
  `ModifyType` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendFiled1` text,
  `ExtendFiled2` text,
  PRIMARY KEY (`StandardDicId`,`StandardType`,`StationCategory`)
);

CREATE TABLE `tbl_standarddicevent` (
  `StandardDicId` int NOT NULL,
  `StandardType` int NOT NULL,
  `EquipmentLogicClassId` int NOT NULL,
  `EquipmentLogicClass` varchar(128) NOT NULL,
  `EventLogicClassId` int DEFAULT NULL,
  `EventLogicClass` varchar(128) DEFAULT NULL,
  `EventClass` varchar(255) DEFAULT NULL,
  `EventStandardName` varchar(255) DEFAULT NULL,
  `NetManageId` varchar(255) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `CompareValue` varchar(128) DEFAULT NULL,
  `StartDelay` varchar(64) DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EquipmentAffect` varchar(255) DEFAULT NULL,
  `BusinessAffect` varchar(255) DEFAULT NULL,
  `StationCategory` int NOT NULL,
  `ModifyType` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendFiled1` text,
  `ExtendFiled2` text,
  `ExtendFiled3` text,
  PRIMARY KEY (`StandardDicId`,`StandardType`,`StationCategory`)
);

CREATE TABLE `tbl_standarddicsig` (
  `StandardDicId` int NOT NULL,
  `StandardType` int NOT NULL,
  `EquipmentLogicClassId` int NOT NULL,
  `EquipmentLogicClass` varchar(128) NOT NULL,
  `SignalLogicClassId` int DEFAULT NULL,
  `SignalLogicClass` varchar(128) DEFAULT NULL,
  `SignalStandardName` varchar(255) NOT NULL,
  `NetManageId` varchar(255) DEFAULT NULL,
  `StoreInterval` int DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  `StatisticsPeriod` int DEFAULT NULL,
  `PercentThreshold` double DEFAULT NULL,
  `StationCategory` int NOT NULL,
  `ModifyType` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendFiled1` text,
  `ExtendFiled2` text,
  PRIMARY KEY (`StandardDicId`,`StandardType`,`StationCategory`)
);

CREATE TABLE `tbl_standardrule` (
  `StandardRuleId` int NOT NULL AUTO_INCREMENT,
  `StandardTemplateId` int NOT NULL,
  `SignalName` varchar(128) DEFAULT NULL,
  `EventName` varchar(128) DEFAULT NULL,
  `Expression` varchar(128) DEFAULT NULL,
  `Meanings` varchar(128) DEFAULT NULL,
  `ControlName` varchar(128) DEFAULT NULL,
  `StandardDicId` int DEFAULT NULL,
  PRIMARY KEY `StandardRuleId` (`StandardRuleId`)
);

CREATE TABLE `tbl_standardtemplate` (
  `StandardTemplateId` int NOT NULL,
  `StandardTemplateName` varchar(255) NOT NULL,
  `StationCategory` int NOT NULL,
  `EquipmentCategory` int NOT NULL,
  `Vendor` varchar(255) NOT NULL,
  `EquipmentModel` varchar(255) NOT NULL,
  `MonitorModule` varchar(255) DEFAULT NULL,
  PRIMARY KEY `StandardRuleId` (`StandardTemplateId`)
);

CREATE TABLE `tbl_standardtemplatemap` (
  `EquipmentTemplateId` int NOT NULL,
  `StationCategory` int NOT NULL,
  `StandardTemplateId` int NOT NULL,
  PRIMARY KEY (`EquipmentTemplateId`,`StationCategory`,`StandardTemplateId`)
);

CREATE TABLE `tbl_standardtype` (
  `StandardId` int NOT NULL,
  `StandardName` varchar(255) NOT NULL,
  `StandardAlias` varchar(255) NOT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`StandardId`)
);