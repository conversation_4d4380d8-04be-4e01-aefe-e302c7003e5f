CREATE TABLE `capacityattribute` (
  `AttributeId` int NOT NULL AUTO_INCREMENT,
  `BaseTypeId` int DEFAULT NULL COMMENT '容量基类id',
  `BaseAttributeId` int DEFAULT NULL COMMENT '容量父级id',
  `AttributeName` varchar(64) DEFAULT NULL COMMENT '容量名称',
  `LogicType` int DEFAULT NULL COMMENT '计算方式 0被动更新 1指标计算',
  `Description` varchar(128) DEFAULT NULL COMMENT '描述',
  `ObjectId` int DEFAULT NULL COMMENT '对象id',
  `ObjectTypeId` int DEFAULT NULL COMMENT '对象资源类型id',
  `ComplexIndex` int DEFAULT NULL COMMENT '指标ID（不可修改）',
  `MinValue` double DEFAULT NULL COMMENT '最小值',
  `MaxValue` double DEFAULT NULL COMMENT '最大值',
  `RatedCapacity` double DEFAULT NULL COMMENT '额定容量',
  `DefaultValue` double DEFAULT NULL COMMENT '默认值',
  `CompensateFactor` double DEFAULT NULL COMMENT '补偿因子',
  `OriginCapacity` double DEFAULT NULL COMMENT '原始输入数据',
  `UsedCapacity` double DEFAULT NULL COMMENT '已使用的容量 计算公式: 原生数据 * 补偿因子 (originCapacity * compensateFactor)',
  `FreeCapacity` double DEFAULT NULL COMMENT '空闲容量',
  `Percent` double DEFAULT NULL COMMENT '容量百分比',
  `SampleTime` varchar(20) DEFAULT NULL COMMENT '更新时间',
  `Unit` varchar(16) DEFAULT NULL COMMENT '单位',
  `Precision` int DEFAULT NULL COMMENT '精度',
  PRIMARY KEY (`AttributeId`),
  UNIQUE KEY `AttributeId` (`AttributeId`)
);

CREATE TABLE `capacityattributequartzrecord` (
  `recordId` int NOT NULL AUTO_INCREMENT,
  `BaseAttributeId` int DEFAULT NULL COMMENT '容量基类id',
  `AttributeName` varchar(64) DEFAULT NULL COMMENT '容量名称',
  `ObjectId` int DEFAULT NULL COMMENT '对象id',
  `ObjectTypeId` int DEFAULT NULL COMMENT '对象类型id',
  `RatedCapacity` double DEFAULT NULL COMMENT '额定容量',
  `UsedCapacity` double DEFAULT NULL COMMENT '已使用容量',
  `FreeCapacity` double DEFAULT NULL COMMENT '空闲剩余容量',
  `Percent` double DEFAULT NULL COMMENT '容量百分比(已使用/空闲剩余容量)',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`recordId`),
  UNIQUE KEY `RecordId` (`recordId`),
  KEY `idx_createTime` (`CreateTime`)
);

CREATE TABLE `capacitybaseattribute` (
  `BaseAttributeId` int NOT NULL AUTO_INCREMENT,
  `BaseAttributeName` varchar(64) DEFAULT NULL COMMENT '容量基类属性名称',
  `BaseTypeId` int DEFAULT NULL COMMENT '所属容量基类ID',
  `AttributeName` varchar(64) DEFAULT NULL COMMENT '容量分类属性名',
  `LogicType` int DEFAULT NULL COMMENT '逻辑处理方式 0被动更新 1指标计算',
  `Unit` varchar(16) DEFAULT NULL COMMENT '容量单位',
  `Description` varchar(128) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`BaseAttributeId`),
  UNIQUE KEY `BaseAttributeId` (`BaseAttributeId`)
);

CREATE TABLE `capacitybasetype` (
  `BaseTypeId` int NOT NULL AUTO_INCREMENT,
  `BaseTypeName` varchar(64) DEFAULT NULL COMMENT '容量基类名称',
  `Description` varchar(128) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`BaseTypeId`),
  UNIQUE KEY `BaseTypeId` (`BaseTypeId`)
);

CREATE TABLE `computerrack` (
  `ComputerRackId` int NOT NULL AUTO_INCREMENT,
  `ComputerRackName` varchar(128) DEFAULT NULL,
  `ComputerRackNumber` varchar(128) DEFAULT NULL,
  `ResourceStructureId` int DEFAULT NULL,
  `Position` varchar(128) DEFAULT NULL,
  `Customer` varchar(128) DEFAULT NULL,
  `Business` varchar(128) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ComputerRackId`),
  UNIQUE KEY `ComputerRackId` (`ComputerRackId`),
  UNIQUE KEY `ComputerRackNumber` (`ComputerRackNumber`)
);

CREATE TABLE `computerrackequipmentemap` (
  `ComputerRackEquipmentMapId` int NOT NULL AUTO_INCREMENT,
  `ComputerRackId` int DEFAULT NULL COMMENT '机架id',
  `EquipmentId` int DEFAULT NULL COMMENT '设备id',
  PRIMARY KEY (`ComputerRackEquipmentMapId`),
  UNIQUE KEY `ComputerRackEquipmentMapId` (`ComputerRackEquipmentMapId`)
);

CREATE TABLE `computerracksignalmap` (
    `computerRackSignalMapId` int NOT NULL AUTO_INCREMENT,
    `computerRackId` int NOT NULL COMMENT '机架id',
    `openExpression` varchar(256) DEFAULT NULL COMMENT '开通率表达式 绑定设备id.信号id',
    `powerExpression` varchar(256) DEFAULT NULL COMMENT '功率表达式 绑定设备id.信号id',
    PRIMARY KEY (`computerRackSignalMapId`),
    UNIQUE KEY `ComputerRackId` (`ComputerRackId`)
);

CREATE TABLE `itdevice` (
  `ITDeviceId` int NOT NULL AUTO_INCREMENT,
  `SerialNumber` varchar(64) DEFAULT NULL,
  `ITDeviceName` varchar(64) DEFAULT NULL,
  `ITDeviceModelId` int DEFAULT NULL,
  `ComputerRackId` int DEFAULT NULL,
  `UIndex` int DEFAULT NULL,
  `Customer` varchar(64) DEFAULT NULL,
  `Business` varchar(64) DEFAULT NULL,
  `PurchaseDate` datetime DEFAULT NULL,
  `LaunchDate` datetime DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `AssetDeviceId` int DEFAULT NULL COMMENT 'IT设备和资产绑定',
  `ipaddr` varchar(64) DEFAULT NULL COMMENT 'IP地址',
  PRIMARY KEY (`ITDeviceId`),
  UNIQUE KEY `ITDeviceId` (`ITDeviceId`)
);

CREATE TABLE `itdevicemodel` (
  `ITDeviceModelId` int NOT NULL AUTO_INCREMENT,
  `ITDeviceModelName` varchar(64) DEFAULT NULL,
  `UnitHeight` int DEFAULT NULL,
  `Manufactor` varchar(64) DEFAULT NULL,
  `Model` varchar(64) DEFAULT NULL,
  `Brand` varchar(64) DEFAULT NULL,
  `Length` double DEFAULT NULL,
  `Width` double DEFAULT NULL,
  `Height` double DEFAULT NULL,
  `RatePower` double DEFAULT NULL,
  `RateCooling` double DEFAULT NULL,
  `RateWeight` double DEFAULT NULL,
  `ModelFile` varchar(255) DEFAULT NULL,
  `CategoryId` int DEFAULT NULL COMMENT 'IT设备模型类型(用于机架管理统计)',
  `AssetDeviceId` int DEFAULT NULL COMMENT 'IT设备和资产绑定',
  PRIMARY KEY (`ITDeviceModelId`),
  UNIQUE KEY `ITDeviceModelId` (`ITDeviceModelId`)
);

CREATE TABLE `tbl_rackmountrecord` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `RackId` int NOT NULL,
  `RackDeviceId` int NOT NULL,
  `RackPosition` int NOT NULL,
  `OperateTime` datetime NOT NULL,
  `OperateState` int NOT NULL,
  `Expired` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `rackmountrecord_expired_index` (`Expired`),
  KEY `IDX_Expired` (`Expired`)
);

CREATE TABLE `tbl_udevice` (
  `UDeviceId` int NOT NULL AUTO_INCREMENT,
  `uDeviceNumber` varchar(128) DEFAULT NULL COMMENT 'U位管理设备唯一标识码',
  `IsOnline` tinyint(1) DEFAULT NULL COMMENT '是否在线',
  `RackId` int DEFAULT NULL COMMENT '对应机架Id',
  `ModuleCnt` int DEFAULT NULL COMMENT '额定U位',
  `UTagCnt` int DEFAULT NULL COMMENT '标签数',
  `IpAddr` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `SWEquipmentId` int DEFAULT NULL COMMENT 'SiteWeb设备Id(U位管理与SW采集设备关联关系)',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`UDeviceId`),
  UNIQUE KEY `UDeviceId` (`UDeviceId`),
  UNIQUE KEY `uDeviceNumber` (`uDeviceNumber`),
  UNIQUE KEY `RackId` (`RackId`)
);

CREATE TABLE `tbl_utag` (
  `UTagId` int NOT NULL AUTO_INCREMENT,
  `TagValue` varchar(45) DEFAULT NULL COMMENT '标签的唯一识别码',
  `AsserId` int DEFAULT NULL COMMENT '绑定资产Id',
  `IsOnline` tinyint(1) DEFAULT NULL COMMENT '是否在线',
  `UDeviceId` int DEFAULT NULL COMMENT 'U位管理Id',
  `UPosition` int DEFAULT NULL COMMENT '所处U位',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`UTagId`),
  UNIQUE KEY `UTagId` (`UTagId`),
  UNIQUE KEY `TagValue` (`TagValue`),
  UNIQUE KEY `AsserId` (`AsserId`)
);

CREATE TABLE `yd_tmputagmap` (
  `TagValue` varchar(255) NOT NULL,
  `AsserId` int DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `SignalId` int DEFAULT NULL,
  PRIMARY KEY `YD_TmpUtagMapIdx1` (`TagValue`)
);
