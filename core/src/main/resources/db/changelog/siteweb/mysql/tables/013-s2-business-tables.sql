CREATE TABLE `activeeventfiltertemplate` (
  `ActiveEventFilterTemplateId` int NOT NULL AUTO_INCREMENT,
  `UserId` int DEFAULT NULL,
  `FilterType` varchar(128) NOT NULL,
  `TemplateName` varchar(255) NOT NULL,
  `Content` varchar(8000) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ActiveEventFilterTemplateId`),
  UNIQUE KEY `ActiveEventFilterTemplateId` (`ActiveEventFilterTemplateId`)
);

CREATE TABLE `activeeventoperationlog` (
  `ActiveEventOperationLogId` bigint NOT NULL AUTO_INCREMENT,
  `SequenceId` varchar(128) DEFAULT NULL COMMENT '流水号',
  `StationId` int DEFAULT NULL COMMENT '局站ID',
  `EquipmentId` int DEFAULT NULL COMMENT '设备ID',
  `EventId` int DEFAULT NULL COMMENT '事件ID',
  `EventConditionId` int DEFAULT NULL COMMENT '事件条件ID',
  `StartTime` datetime DEFAULT NULL COMMENT '开始时间',
  `OperatorId` int DEFAULT NULL COMMENT '操作人ID',
  `Operation` varchar(128) DEFAULT NULL COMMENT '告警操作',
  `OperationTime` datetime DEFAULT NULL COMMENT '操作时间',
  `Description` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ActiveEventOperationLogId`),
  UNIQUE KEY `ActiveEventOperationLogId` (`ActiveEventOperationLogId`),
  KEY `IDX_ActiveEventOperationLog_SequenceId` (`SequenceId`),
  KEY `ActiveEventOperationLog_IDX1` (`StartTime`,`EquipmentId`,`EventId`,`StationId`)
);

CREATE TABLE `activenotification` (
  `AlarmSequenceId` int NOT NULL,
  `BirthTime` datetime DEFAULT NULL,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(128) DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `EventId` int DEFAULT NULL,
  `EventConditionId` int DEFAULT NULL,
  `EventUniqueId` varchar(255) DEFAULT NULL,
  `EventName` varchar(128) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `SeverityName` varchar(50) DEFAULT NULL,
  `EventStatus` int NOT NULL,
  `Overturn` int DEFAULT NULL,
  `Meaning` varchar(128) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `UpdateTimeFormat` varchar(255) DEFAULT NULL,
  `ConfirmUserId` int DEFAULT NULL,
  `ConfirmUserName` varchar(128) DEFAULT NULL,
  `CenterName` varchar(128) DEFAULT NULL,
  `StationState` varchar(128) DEFAULT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `EquipmentVendorName` varchar(128) DEFAULT NULL,
  `EquipmentLogicCategory` varchar(128) DEFAULT NULL,
  `LogicCategory` varchar(128) DEFAULT NULL,
  `SubLogicCategory` varchar(128) DEFAULT NULL,
  `InfectionToEquipment` varchar(128) DEFAULT NULL,
  `InfectionToBusiness` varchar(128) DEFAULT NULL,
  `StandardAlarmName` varchar(128) DEFAULT NULL,
  `StandardNameId` varchar(128) DEFAULT NULL,
  `AlarmComment` varchar(128) DEFAULT NULL,
  `NetAlarmId` varchar(128) DEFAULT NULL,
  `NotifyServerId` int DEFAULT NULL,
  `NotificationType` int DEFAULT NULL,
  `Setting` varchar(255) DEFAULT NULL,
  `NotificationRecieverId` int DEFAULT NULL,
  `RetryTimes` int DEFAULT NULL,
  `EventFilterDelay` int DEFAULT NULL,
  `NotifyResult` int DEFAULT NULL,
  `InstructionId` varchar(128) DEFAULT NULL,
  PRIMARY KEY `PK_ActiveNotification_ID` (`AlarmSequenceId`,`EventStatus`)
);

CREATE TABLE `alarmmasklog` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int DEFAULT NULL,
  `ResourceStructureId` int DEFAULT NULL,
  `UserId` int NOT NULL,
  `OperationType` int NOT NULL,
  `OperationTime` datetime DEFAULT NULL,
  `TimeGroupCategory` int DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `TimeGroupChars` varchar(255) DEFAULT NULL,
  `Comment` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `AlarmMaskLog_IDX1` (`OperationTime`,`EquipmentId`,`EventId`)
);

CREATE TABLE `allnotification` (
  `AlarmSequenceId` int NOT NULL,
  `BirthTime` datetime DEFAULT NULL,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(128) DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `EventId` int DEFAULT NULL,
  `EventConditionId` int DEFAULT NULL,
  `EventUniqueId` varchar(255) NOT NULL,
  `EventName` varchar(128) DEFAULT NULL,
  `EventSeverity` int DEFAULT NULL,
  `SeverityName` varchar(50) DEFAULT NULL,
  `EventStatus` int NOT NULL,
  `Overturn` int DEFAULT NULL,
  `Meaning` varchar(128) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `UpdateTimeFormat` varchar(255) DEFAULT NULL,
  `ConfirmUserId` int DEFAULT NULL,
  `ConfirmUserName` varchar(128) DEFAULT NULL,
  `CenterName` varchar(128) DEFAULT NULL,
  `StationState` varchar(128) DEFAULT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `EquipmentVendorName` varchar(128) DEFAULT NULL,
  `EquipmentLogicCategory` varchar(128) DEFAULT NULL,
  `LogicCategory` varchar(128) DEFAULT NULL,
  `SubLogicCategory` varchar(128) DEFAULT NULL,
  `InfectionToEquipment` varchar(128) DEFAULT NULL,
  `InfectionToBusiness` varchar(128) DEFAULT NULL,
  `StandardAlarmName` varchar(128) DEFAULT NULL,
  `StandardNameId` varchar(128) DEFAULT NULL,
  `AlarmComment` varchar(128) DEFAULT NULL,
  `NetAlarmId` varchar(128) DEFAULT NULL,
  `DefaultStationGroupName` varchar(128) DEFAULT NULL,
  `NotificationType` int NOT NULL,
  `StationCategoryId` int DEFAULT NULL,
  `StationCategoryName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`AlarmSequenceId`,`EventUniqueId`,`EventStatus`,`NotificationType`),
  KEY `AllNotification_IDX1` (`StationId`,`EquipmentId`,`EventId`,`EventConditionId`,`EventStatus`,`StartTime`,`NotificationType`),
  KEY `AllNotification_IDX2` (`EventUniqueId`,`AlarmSequenceId`,`EventStatus`,`StartTime`,`NotificationType`)
);

CREATE TABLE `eventfilter` (
  `EventFilterId` int NOT NULL,
  `EventFilterName` varchar(128) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdateDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`EventFilterId`)
);

CREATE TABLE `eventfiltercondition` (
  `EventFilterId` int NOT NULL,
  `EventFilterConditionId` int NOT NULL,
  `EventFilterCombination` text NOT NULL,
  `EventFilterSegment1` int DEFAULT NULL,
  `EventFilterSegment2` int DEFAULT NULL,
  `EventFilterSegment3` int DEFAULT NULL,
  `EventFilterSegment4` int DEFAULT NULL,
  `EventFilterSegment5` int DEFAULT NULL,
  `EventFilterSegment6` int DEFAULT NULL,
  `EventFilterSegment7` int DEFAULT NULL,
  `EventFilterSegment8` int DEFAULT NULL,
  `EventFilterSegment9` int DEFAULT NULL,
  `EventFilterSegment10` int DEFAULT NULL,
  `EventFilterSegment11` int DEFAULT NULL,
  `EventFilterDelay` int NOT NULL DEFAULT '0',
  `EventFilterCount` int NOT NULL DEFAULT '0',
  `Description` text,
  `LastUpdateDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`EventFilterId`,`EventFilterConditionId`)
);

CREATE TABLE `eventfiltermap` (
  `EventFilterMemberId` int NOT NULL,
  `EventFilterId` int NOT NULL,
  PRIMARY KEY (`EventFilterMemberId`,`EventFilterId`),
  KEY `EventFilterId` (`EventFilterId`)
);

CREATE TABLE `eventfiltermember` (
  `EventFilterMemberId` int NOT NULL,
  `EventFilterMemberName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdateDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`EventFilterMemberId`)
);

CREATE TABLE `eventnotifyreciever` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventSeverity` int NOT NULL,
  `EventState` int NOT NULL,
  `NotifyReceiverId` int NOT NULL,
  `NotifyReceiverCategory` int NOT NULL,
  `NotifyReceiverName` varchar(128) DEFAULT NULL,
  `NotifyAddress` varchar(255) DEFAULT NULL,
  `NotifyContent` varchar(255) DEFAULT NULL,
  `NotifyServerId` int DEFAULT NULL,
  `EventFilterDelay` int DEFAULT NULL,
  `EventFilterCount` int DEFAULT NULL,
  `EventFilterId` int DEFAULT NULL,
  `EventFilterConditionId` int DEFAULT NULL,
  `NotifyServerCategory` int DEFAULT NULL,
  PRIMARY KEY (`StationId`,`EquipmentId`,`EventId`,`EventSeverity`,`EventState`,`NotifyReceiverId`,`NotifyReceiverCategory`)
);

CREATE TABLE `tbl_bizbasetypeid` (
  `BusinessTypeId` int NOT NULL,
  `BaseTypeId` int NOT NULL,
  `StoreInterval` double DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  PRIMARY KEY (`BaseTypeId`,`BusinessTypeId`)
);

CREATE TABLE `tbl_bizexpequsignalsmap` (
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `AssociationId` int NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `SerialId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `StoreInterval` double DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  PRIMARY KEY (`BusinessTypeId`,`EquipmentId`,`ExpressionId`,`MonitorUnitId`,`SerialId`,`SignalId`,`StationId`)
);

CREATE TABLE `tbl_bizexpsignalscfg` (
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `AssociationId` int NOT NULL,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `StoreInterval` double DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  PRIMARY KEY (`BusinessTypeId`,`EquipmentId`,`ExpressionId`,`SignalId`,`StationId`)
);

CREATE TABLE `tbl_bizexpstationsmap` (
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `SerialId` int NOT NULL,
  `Expression` text,
  `SuppressExpression` text,
  `StateTriggerValue` int DEFAULT '1',
  `BeforeChgStoreInterval` double DEFAULT NULL,
  `AfterChgStoreInterval` double DEFAULT NULL,
  `ErrorFlag` int DEFAULT NULL,
  `Note` text,
  PRIMARY KEY (`BusinessTypeId`,`ExpressionId`,`MonitorUnitId`,`SerialId`,`StationId`)
);

CREATE TABLE `tbl_businessdataitem` (
  `BusinessId` int NOT NULL DEFAULT '0',
  `ParentEntryId` int NOT NULL DEFAULT '0',
  `ParentItemId` int NOT NULL DEFAULT '0',
  `EntryId` int NOT NULL,
  `ItemId` int NOT NULL,
  `ItemValue` varchar(128) NOT NULL,
  `ItemAlias` varchar(255) DEFAULT NULL,
  `IsSystem` bit(1) NOT NULL DEFAULT b'1',
  `IsDefault` bit(1) NOT NULL DEFAULT b'0',
  `Enable` bit(1) NOT NULL DEFAULT b'1',
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  `ExtendField5` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`BusinessId`,`EntryId`,`ItemId`)
);

CREATE TABLE `tbl_businessexpressioncfg` (
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `ExpressionName` varchar(255) DEFAULT NULL,
  `Expression` text,
  `SuppressExpression` text,
  `StateTriggerValue` int DEFAULT '1',
  `BeforeChgStoreInterval` double DEFAULT NULL,
  `AfterChgStoreInterval` double DEFAULT NULL,
  `Note` text,
  PRIMARY KEY (`BusinessTypeId`,`ExpressionId`)
);

CREATE TABLE `tbl_businesstype` (
  `BusinessTypeId` int NOT NULL,
  `BusinessTypeName` varchar(255) NOT NULL,
  `MiddleTableName` varchar(255) DEFAULT NULL,
  `Note` text,
  PRIMARY KEY (`BusinessTypeId`)
);

CREATE TABLE `tbl_businesstypestatus` (
  `StationId` int NOT NULL,
  `BusinessTypeId` int NOT NULL,
  `ExpressionId` int NOT NULL,
  `SerialId` int NOT NULL,
  `BusinessState` int NOT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `GroupId` int DEFAULT NULL,
  PRIMARY KEY (`BusinessState`,`BusinessTypeId`,`ExpressionId`,`SerialId`,`StartTime`,`StationId`)
);

CREATE TABLE `tbl_categoryidmap` (
  `BusinessId` int NOT NULL,
  `CategoryTypeId` int NOT NULL,
  `OriginalCategoryId` int NOT NULL,
  `BusinessCategoryId` int NOT NULL,
  PRIMARY KEY (`BusinessCategoryId`,`BusinessId`,`CategoryTypeId`,`OriginalCategoryId`)
);

CREATE TABLE `tbl_custominfo` (
  `CustomInfoId` int NOT NULL,
  `UserId` int NOT NULL,
  `CustomType` varchar(128) NOT NULL,
  `CustomContent` longtext NOT NULL,
  `CreateTime` datetime NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CustomInfoId`)
);

CREATE TABLE `tbl_experience` (
  `ExperienceId` int NOT NULL AUTO_INCREMENT,
  `ExperienceCaption` text,
  `Measure` text,
  `Description` text,
  `LastUpdateDate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Condition` text,
  PRIMARY KEY (`ExperienceId`)
);

CREATE TABLE `tbl_expert` (
  `ExpertId` int NOT NULL,
  `StationCategoryId` int DEFAULT NULL,
  `StationCategoryName` varchar(255) DEFAULT NULL,
  `BaseEquipmentTypeId` int DEFAULT NULL,
  `BaseEquipmentTypeName` varchar(255) DEFAULT NULL,
  `BaseAlarmId` int NOT NULL,
  `BaseAlarmName` varchar(255) DEFAULT NULL,
  `StandardEquipmentTypeId` int DEFAULT NULL,
  `StandardEquipmentTypeName` varchar(255) DEFAULT NULL,
  `StandardAlarmId` varchar(255) DEFAULT NULL,
  `StandardAlarmName` varchar(255) DEFAULT NULL,
  `Reason` text,
  `Solution` text,
  PRIMARY KEY (`ExpertId`)
);

CREATE TABLE `tbl_fault` (
  `FaultId` int NOT NULL,
  `SequenceId` varchar(255) NOT NULL,
  `UUID` varchar(128) NOT NULL,
  `StationGroupName` varchar(255) DEFAULT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `HouseName` varchar(255) DEFAULT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `EventName` varchar(255) DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `CreateUserId` int NOT NULL,
  `CreateTime` datetime NOT NULL,
  `CaseReason` text,
  `CaseSolution` text,
  PRIMARY KEY (`FaultId`)
);

CREATE TABLE `tbl_faultexpertmap` (
  `FaultExpertMapId` int NOT NULL AUTO_INCREMENT,
  `FaultId` int NOT NULL,
  `ExpertId` int NOT NULL,
  PRIMARY KEY (`FaultExpertMapId`)
);

CREATE TABLE `tbl_graphicpage` (
  `Id` int NOT NULL,
  `Type` varchar(255)  DEFAULT NULL,
  `GroupId` int DEFAULT NULL,
  `PageCategory` int DEFAULT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `ObjectId` bigint DEFAULT NULL,
  `GlobalResourceId` bigint DEFAULT NULL,
  `Name` varchar(255)  NOT NULL,
  `AppendName` varchar(255)  DEFAULT NULL,
  `TemplateId` int DEFAULT NULL,
  `Data` text,
  `Style` text,
  `Children` longtext,
  `Description` varchar(255)  DEFAULT NULL,
  `RouterType` varchar(255)  DEFAULT NULL,
  `CrumbsRouterType` varchar(255)  DEFAULT NULL,
  PRIMARY KEY (`Id`)
);

CREATE TABLE `tbl_graphicpagetemplate` (
  `Id` int NOT NULL,
  `Type` varchar(255)  DEFAULT NULL,
  `GroupId` int DEFAULT NULL,
  `PageCategory` int DEFAULT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `Internal` smallint DEFAULT NULL,
  `Name` varchar(255)  NOT NULL,
  `AppendName` varchar(255)  DEFAULT NULL,
  `Data` text,
  `Style` text,
  `Children` text,
  `Description` varchar(255)  DEFAULT NULL,
  `TemplateCategory` int DEFAULT NULL,
  PRIMARY KEY (`Id`)
);

CREATE TABLE `tbl_homepage` (
  `PageId` int NOT NULL,
  `PageName` varchar(255) NOT NULL,
  `FileName` varchar(255) NOT NULL,
  `PageType` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`PageId`)
);

CREATE TABLE `tbl_userhomepagemap` (
  `PageId` int NOT NULL,
  `UserId` int NOT NULL,
  PRIMARY KEY (`PageId`,`UserId`)
);

CREATE TABLE `tbl_kpipage` (
  `KPIPageId` int NOT NULL,
  `UserId` int NOT NULL,
  `Name` varchar(128) NOT NULL,
  `Type` int NOT NULL,
  `FileName` varchar(128) NOT NULL,
  `FileDir` varchar(150) DEFAULT NULL,
  `CreateTime` datetime NOT NULL,
  `ModifyTime` datetime DEFAULT NULL,
  `Description` varchar(200) DEFAULT NULL,
  `ThumbImage` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`KPIPageId`)
);

CREATE TABLE `tbl_kpipageuserrelate` (
  `UserId` int NOT NULL,
  `KPIPageId` int NOT NULL,
  PRIMARY KEY (`UserId`)
);

CREATE TABLE `tbl_equipmentbranch` (
  `id` int NOT NULL AUTO_INCREMENT,
  `BranchId` int NOT NULL,
  `BranchName` varchar(128) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `Id` (`id`),
  KEY `IDX_EquipmentBranch_EquipmentId_BranchId` (`EquipmentId`,`BranchId`)
);

CREATE TABLE `tbl_phoenixdiskfile` (
  `FileId` bigint NOT NULL AUTO_INCREMENT,
  `FilePath` varchar(1024)  NOT NULL,
  `FileName` varchar(256)  NOT NULL,
  `Status` int DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`FileId`)
);
