CREATE TABLE `st_org_cucc_equip` (
  `OrgCUCCEquipId` int PRIMARY KEY AUTO_INCREMENT,
  `RowId` int NOT NULL,
  `Class` varchar(255) DEFAULT NULL,
  `Type` varchar(255) DEFAULT NULL,
  `C1` varchar(255) DEFAULT NULL,
  `C2` varchar(255) DEFAULT NULL,
  `TypeId` varchar(255) DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_activemessagecmcc` (
  `UserId` int DEFAULT NULL,
  `SerialNo` decimal(14,0) NOT NULL,
  `SiteID` varchar(20) NOT NULL,
  `FSUID` varchar(20) NOT NULL,
  `DeviceID` varchar(20) NOT NULL,
  `SiteName` text,
  `FSUName` text,
  `DeviceName` text,
  `MessageType` int NOT NULL,
  `MessageName` varchar(64) DEFAULT NULL,
  `MessageParm` text,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `Try` int DEFAULT NULL,
  `FailureCause` text NOT NULL,
  `IsConfirm` bit(1) NOT NULL DEFAULT b'0',
  `ControlType` int DEFAULT NULL,
  `Description` text NOT NULL,
  PRIMARY KEY (`DeviceID`,`FSUID`,`MessageType`,`SerialNo`,`SiteID`)
);

CREATE TABLE `tbl_activemessagecucc` (
  `UserId` int DEFAULT NULL,
  `SerialNo` decimal(14,0) NOT NULL,
  `SUID` varchar(255) NOT NULL,
  `DeviceID` varchar(255) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `MessageType` int NOT NULL,
  `MessageName` varchar(64) DEFAULT NULL,
  `MessageParm` text,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `Try` int DEFAULT NULL,
  `FailureCause` text NOT NULL,
  `IsConfirm` bit(1) NOT NULL DEFAULT b'0',
  `ControlType` int DEFAULT NULL,
  `Description` text NOT NULL,
  PRIMARY KEY (`MessageType`,`SerialNo`)
);

CREATE TABLE `tbl_alarmdatacucc` (
  `SUID` varchar(64) NOT NULL,
  `DeviceID` varchar(64) NOT NULL,
  `StandardID` varchar(64) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `SerialNo` varchar(10) DEFAULT NULL,
  `AlarmTime` datetime DEFAULT NULL,
  `AlarmDesc` varchar(255) DEFAULT NULL,
  `TriggerVal` double DEFAULT NULL,
  `AlarmFlag` int DEFAULT NULL,
  `Description` text,
  `ExtendField1` text,
  `ExtendField2` text,
  `AlarmDataType` int NOT NULL,
  PRIMARY KEY (`AlarmDataType`,`DeviceID`,`StandardID`,`SUID`)
);

CREATE TABLE `tbl_alarmpropertycucc` (
  `SUID` varchar(64) NOT NULL,
  `DeviceID` varchar(64) NOT NULL,
  `StandardID` varchar(64) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `BDelay` double DEFAULT NULL,
  `EDelay` double DEFAULT NULL,
  `Description` text,
  `ExtendField1` text,
  `ExtendField2` text,
  `AlarmPropertyType` int NOT NULL,
  PRIMARY KEY (`AlarmPropertyType`,`DeviceID`,`StandardID`,`SUID`)
);

CREATE TABLE `tbl_aodatacucc` (
  `SUID` varchar(64) NOT NULL,
  `DeviceID` varchar(64) NOT NULL,
  `StandardID` varchar(64) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `SetValue` double DEFAULT NULL,
  `HLimit` double DEFAULT NULL,
  `SHLimit` double DEFAULT NULL,
  `LLimit` double DEFAULT NULL,
  `SLLimit` double DEFAULT NULL,
  `Threshold` double DEFAULT NULL,
  `RelativeVal` double DEFAULT NULL,
  `IntervalTime` double DEFAULT NULL,
  `Description` text,
  `ExtendField1` text,
  `ExtendField2` text,
  `AODataType` int NOT NULL,
  PRIMARY KEY (`AODataType`,`DeviceID`,`StandardID`,`SUID`)
);

CREATE TABLE `tbl_devicectcc` (
  `DeviceCTCCId` int PRIMARY KEY AUTO_INCREMENT,
  `SUDeviceTypeName` varchar(50)  DEFAULT NULL,
  `SUDeviceTypeId` varchar(10)  DEFAULT NULL,
  `EquipmentBaseTypeName` varchar(50)  DEFAULT NULL,
  `Description` varchar(255)  DEFAULT NULL,
  `EquipmentBaseTypeId` int DEFAULT NULL,
  `Meanings` varchar(255)  DEFAULT NULL,
  `UpdateTime` varchar(50)  DEFAULT NULL,
  `Recorder` varchar(50)  DEFAULT NULL,
  `DeviceTypeId` varchar(10)  DEFAULT NULL
);

CREATE TABLE `tbl_devicesubtypecmcc` (
  `DeviceTypeID` int NOT NULL,
  `DeviceSubTypeID` int NOT NULL,
  `DeviceSubTypeName` varchar(128) DEFAULT NULL,
  `Description` text,
  PRIMARY KEY (`DeviceSubTypeID`,`DeviceTypeID`)
);

CREATE TABLE `tbl_devicetypebasemapcmcc` (
  `DeviceTypeID` int NOT NULL,
  `DeviceSubTypeID` int NOT NULL,
  `BaseEquipmentID` int NOT NULL,
  PRIMARY KEY (`DeviceTypeID`,`DeviceSubTypeID`,`BaseEquipmentID`)
);

CREATE TABLE `tbl_devicetypecmcc` (
  `DeviceTypeID` int NOT NULL,
  `DeviceTypeName` varchar(128) DEFAULT NULL,
  `Description` text,
  PRIMARY KEY (`DeviceTypeID`)
);

CREATE TABLE `tbl_devicetypectcc` (
  `DeviceTypeCTCCId` int PRIMARY KEY AUTO_INCREMENT,
  `SUDeviceTypeId` varchar(20)  DEFAULT NULL,
  `SUDeviceTypeName` varchar(50)  DEFAULT NULL,
  `Description` varchar(50)  DEFAULT NULL,
  `UpdateTime` varchar(50)  DEFAULT NULL
);

CREATE TABLE `tbl_dodatacucc` (
  `SUID` varchar(64) NOT NULL,
  `DeviceID` varchar(64) NOT NULL,
  `StandardID` varchar(64) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `Description` text,
  `ExtendField1` text,
  `ExtendField2` text,
  `DODataType` int NOT NULL,
  PRIMARY KEY (`DeviceID`,`DODataType`,`StandardID`,`SUID`)
);

CREATE TABLE `tbl_enumdatacucc` (
  `EntryId` int NOT NULL,
  `AttributeType` int DEFAULT NULL,
  `AttributeName` varchar(128) DEFAULT NULL,
  `AttributeDescription` text,
  `EnumId` int DEFAULT NULL,
  `EnumType` varchar(128) DEFAULT NULL,
  `EnumValue` int DEFAULT NULL,
  `EnumDefine` text,
  `Description` text,
  `ExtendField1` text,
  PRIMARY KEY (`EntryId`)
);

CREATE TABLE `tbl_equipmentbasetypemapctcc` (
  `SUDeviceTypeId` int NOT NULL,
  `SUDeviceTypeName` varchar(50) DEFAULT NULL,
  `FirstEquipmentBaseTypeId` int DEFAULT NULL,
  `FirstEquipmentBaseTypeName` varchar(50) DEFAULT NULL,
  `SecondEquipmentBaseTypeId` int DEFAULT NULL,
  `SecondEquipmentBaseTypeName` varchar(50) DEFAULT NULL,
  `ThirdEquipmentBaseTypeId` int DEFAULT NULL,
  `ThirdEquipmentBaseTypeName` varchar(50) DEFAULT NULL,
  `ExtendField1` varchar(128) DEFAULT NULL,
  `ExtendField2` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`SUDeviceTypeId`),
  KEY `TBL_EquipmentBaseTypeMapCTCC_IDX1` (`SUDeviceTypeId`)
);

CREATE TABLE `tbl_equipmentcmcc` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `DeviceID` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `FSUID` varchar(255) DEFAULT '',
  `SiteID` varchar(255) DEFAULT '',
  `SiteName` varchar(255) DEFAULT NULL,
  `RoomID` varchar(255) DEFAULT NULL,
  `RoomName` varchar(255) DEFAULT NULL,
  `DeviceType` int DEFAULT NULL,
  `DeviceSubType` int DEFAULT NULL,
  `Model` varchar(255) DEFAULT NULL,
  `Brand` varchar(255) DEFAULT NULL,
  `RatedCapacity` double DEFAULT NULL,
  `Version` varchar(20) DEFAULT NULL,
  `BeginRunTime` datetime DEFAULT NULL,
  `DevDescribe` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`),
  KEY `TBL_EquipmentCMCC_IDX1` (`StationId`,`EquipmentId`),
  KEY `TBL_EquipmentCMCC_IDX2` (`FSUID`,`DeviceID`)
);

CREATE TABLE `tbl_equipmentcucc` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `DeviceID` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUID` varchar(255) DEFAULT NULL,
  `DeviceVender` varchar(255) DEFAULT NULL,
  `DeviceType` varchar(255) DEFAULT NULL,
  `MFD` varchar(255) DEFAULT NULL,
  `ControllerType` varchar(255) DEFAULT NULL,
  `SoftwareVersion` varchar(255) DEFAULT NULL,
  `BatchNo` varchar(255) DEFAULT NULL,
  `Password` varchar(512) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`),
  KEY `TBL_EquipmentCUCC_IDX1` (`StationId`,`EquipmentId`),
  KEY `IDX_EquipmentCUCC_SU_DeviceId` (`SUID`,`DeviceID`)
);

CREATE TABLE `tbl_equipmentfsucucc` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `DeviceID` varchar(255)  DEFAULT NULL,
  `NamePrefix` varchar(255)  DEFAULT NULL,
  `DeviceName` varchar(255)  DEFAULT NULL,
  `FSUID` varchar(255)  DEFAULT NULL,
  `SiteID` varchar(255)  DEFAULT NULL,
  `SiteName` varchar(255)  DEFAULT NULL,
  `RoomID` varchar(255)  DEFAULT NULL,
  `RoomName` varchar(255)  DEFAULT NULL,
  `Model` varchar(255)  DEFAULT NULL,
  `Brand` varchar(255)  DEFAULT NULL,
  `RatedCapacity` float DEFAULT NULL,
  `Version` varchar(20)  DEFAULT NULL,
  `BeginRunTime` datetime DEFAULT NULL,
  `DevDescribe` varchar(255)  DEFAULT NULL,
  `ExtendField1` varchar(255)  DEFAULT NULL,
  `ExtendField2` varchar(255)  DEFAULT NULL,
  `EquipmentCategory` int DEFAULT NULL,
  `ChildCategory` int DEFAULT NULL,
  `AssetsID` varchar(255)  DEFAULT NULL,
  `DeviceType` varchar(50)  DEFAULT NULL,
  `DeviceSubType` varchar(50)  DEFAULT NULL,
  `MFD` varchar(50)  DEFAULT NULL,
  `DeviceVender` varchar(50)  DEFAULT NULL,
  `SoftwareVersion` varchar(50)  DEFAULT NULL,
  `ControllerType` varchar(50)  DEFAULT NULL,
  `BatchNo` varchar(50)  DEFAULT NULL,
  `EquipmentMode` varchar(50)  DEFAULT NULL,
  PRIMARY KEY (`StationId`,`EquipmentId`),
  KEY `idxTBL_EquipmentFSUCUCCID` (`StationId`,`EquipmentId`)
);

CREATE TABLE `tbl_equipmentgdctcc` (
  `SUID` varchar(40) NOT NULL,
  `DeviceID` varchar(40) NOT NULL,
  `DeviceName` varchar(128) DEFAULT NULL,
  `EquipmentId` varchar(128) DEFAULT NULL,
  `DeviceHLType` int DEFAULT NULL,
  `ExtendField1` varchar(128) DEFAULT NULL,
  `ExtendField2` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`DeviceID`,`SUID`),
  KEY `TBL_EquipmentGDCTCC_IDX1` (`SUID`,`DeviceID`)
);

CREATE TABLE `tbl_equipmentktctcc` (
  `EquipmentktCTCCId` int PRIMARY KEY AUTO_INCREMENT,
  `StationKTName` varchar(128) NOT NULL,
  `StationKTId` varchar(40) NOT NULL,
  `RoomKTName` varchar(128) NOT NULL,
  `RoomKTId` varchar(40) NOT NULL,
  `SystemName` varchar(128) NOT NULL,
  `EquipmentName` varchar(128) NOT NULL,
  `EquipmentState` varchar(40) DEFAULT NULL,
  `EquipmentTypeName` varchar(40) DEFAULT NULL,
  `SUID` varchar(40) DEFAULT NULL,
  `IsAdded` int DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_eventconditionctcc` (
  `EquipmentTemplateId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `Description` varchar(50)  DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`,`EventId`,`EventConditionId`)
);

CREATE TABLE `tbl_fsuconfig` (
  `FSUID` varchar(255) NOT NULL,
  `NeedSyncFlag` bit(1) DEFAULT NULL,
  `FSUSendTime` datetime DEFAULT NULL,
  `FSUSendCfgCode` varchar(32) DEFAULT NULL,
  `FSUSendCfgContent` longtext,
  `LastFSUSendCfgContent` longtext,
  `FSUSendCount` int DEFAULT '0',
  `SCSyncCfgTime` datetime DEFAULT NULL,
  `SCSyncCfgCode` varchar(32) DEFAULT NULL,
  `SCSyncCfgContent` longtext,
  `LastSCSyncCfgContent` longtext,
  `SCSyncCount` int DEFAULT '0',
  PRIMARY KEY (`FSUID`)
);

CREATE TABLE `tbl_fsuconfigimportlog` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `FSUID` varchar(255) NOT NULL,
  `CfgType` varchar(255) NOT NULL,
  `CfgContent` longtext NOT NULL,
  `SaveTime` datetime NOT NULL,
  PRIMARY KEY (`LogId`)
);

CREATE TABLE `tbl_fsuconfigsendbyfsu` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `FSUID` varchar(255) NOT NULL,
  `FSUSendTime` datetime NOT NULL,
  `FSUSendCfgCode` varchar(32) NOT NULL,
  `FSUSendCfgContent` longtext NOT NULL,
  PRIMARY KEY (`LogId`),
  KEY `TBL_FSUConfigSendByFSU_IDX1` (`FSUID`,`FSUSendTime`)
);

CREATE TABLE `tbl_fsucuccpresetequip` (
  `RowId` int NOT NULL AUTO_INCREMENT,
  `CuccTypeId` varchar(3) NOT NULL,
  `CuccClassName` varchar(255) DEFAULT NULL,
  `CuccTypeName` varchar(255) DEFAULT NULL,
  `EquipmentTemplateId` int NOT NULL,
  `EquipmentTemplateName` varchar(255) DEFAULT NULL,
  `Memo` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Ex1` varchar(255) DEFAULT NULL,
  `Ex2` varchar(255) DEFAULT NULL,
  `Ex3` varchar(255) DEFAULT NULL,
  `Ex4` varchar(255) DEFAULT NULL,
  `ExX` longtext,
  PRIMARY KEY `idx_TBL_FsuCuccPresetEquip` (`CuccTypeId`),
  KEY `RowId` (`RowId`)
);

CREATE TABLE `tbl_fsuftphisdatalog` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `FSUID` varchar(255) NOT NULL,
  `FileName` varchar(255) NOT NULL,
  `FileSize` int NOT NULL,
  `SaveTime` datetime NOT NULL,
  PRIMARY KEY (`LogId`),
  KEY `TBL_FsuFtpHisDataLog_IDX1` (`FSUID`,`FileName`)
);

CREATE TABLE `tbl_fsutsignalcmcc` (
  `FSUID` varchar(20) NOT NULL,
  `DeviceID` varchar(26) NOT NULL,
  `ID` varchar(20) NOT NULL,
  `SignalNumber` varchar(5) NOT NULL,
  `Type` int DEFAULT NULL,
  `SignalName` varchar(80) DEFAULT NULL,
  `AlarmLevel` int DEFAULT NULL,
  `Threshold` double DEFAULT NULL,
  `AbsoluteVal` double DEFAULT NULL,
  `RelativeVal` double DEFAULT NULL,
  `Describe` varchar(120) DEFAULT NULL,
  `NMAlarmID` varchar(40) DEFAULT NULL,
  `TSignalId` int NOT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `EI1` int DEFAULT NULL,
  `ES1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`DeviceID`,`FSUID`,`TSignalId`),
  KEY `TBL_FsuTSignalCMCC_IDX1` (`FSUID`)
);

CREATE TABLE `tbl_historybinterfacelog` (
  `ID` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `FSUID` varchar(255) NOT NULL,
  `MonitorUnitId` varchar(255) NOT NULL,
  `DataServerIp` varchar(255) NOT NULL,
  `RequestMessageType` varchar(128) NOT NULL,
  `ResponseMessageType` varchar(128) DEFAULT NULL,
  `CommunicationResult` bit(1) DEFAULT NULL,
  `ParseResult` bit(1) DEFAULT NULL,
  `ResultCause` varchar(255) DEFAULT NULL,
  `InsertTime` datetime NOT NULL,
  `XmlInvokeContent` longtext,
  `XmlResultContent` longtext,
  `ExtendField` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_historymessagecmcc` (
  `UserId` int DEFAULT NULL,
  `SerialNo` decimal(14,0) NOT NULL,
  `SiteID` varchar(20) NOT NULL,
  `FSUID` varchar(20) NOT NULL,
  `DeviceID` varchar(20) NOT NULL,
  `SiteName` text,
  `FSUName` text,
  `DeviceName` text,
  `MessageType` int NOT NULL,
  `MessageName` varchar(64) DEFAULT NULL,
  `MessageParm` text,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `Try` int DEFAULT NULL,
  `FailureCause` text NOT NULL,
  `IsConfirm` bit(1) NOT NULL DEFAULT b'0',
  `ControlType` int DEFAULT NULL,
  `Description` text NOT NULL,
  PRIMARY KEY (`DeviceID`,`FSUID`,`MessageType`,`SerialNo`,`SiteID`)
);

CREATE TABLE `tbl_historymessagecucc` (
  `UserId` int DEFAULT NULL,
  `SerialNo` decimal(14,0) NOT NULL,
  `SUID` varchar(255) NOT NULL,
  `DeviceID` varchar(255) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `DeviceName` varchar(255) DEFAULT NULL,
  `MessageType` int NOT NULL,
  `MessageName` varchar(64) DEFAULT NULL,
  `MessageParm` text,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `Try` int DEFAULT NULL,
  `FailureCause` text NOT NULL,
  `IsConfirm` bit(1) NOT NULL DEFAULT b'0',
  `ControlType` int DEFAULT NULL,
  `Description` text NOT NULL,
  PRIMARY KEY (`MessageType`,`SerialNo`)
);

CREATE TABLE `tbl_roomcmcc` (
  `StationId` int NOT NULL,
  `HouseId` int NOT NULL,
  `RoomID` varchar(125) DEFAULT NULL,
  `RoomName` varchar(255) DEFAULT NULL,
  `SiteID` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`HouseId`,`StationId`),
  KEY `TBL_RoomCMCC_IDX1` (`StationId`,`HouseId`)
);

CREATE TABLE `tbl_roomfsucucc` (
  `StationId` int NOT NULL,
  `HouseId` int NOT NULL,
  `RoomID` varchar(125)  DEFAULT NULL,
  `RoomName` varchar(255)  DEFAULT NULL,
  `SiteID` varchar(255)  DEFAULT NULL,
  `Description` varchar(255)  DEFAULT NULL,
  PRIMARY KEY (`StationId`,`HouseId`)
);

CREATE TABLE `tbl_roomktctcc` (
  `RoomKTName` varchar(128) NOT NULL,
  `RoomKTId` varchar(40) NOT NULL,
  `StationKTName` varchar(128) NOT NULL,
  `StationKTId` varchar(40) NOT NULL,
  `FriendlyRoomName` varchar(128) DEFAULT NULL,
  `IsGenerated` int DEFAULT NULL,
  `IsAdded` int DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`RoomKTId`,`StationKTId`),
  KEY `TBL_RoomKTCTCC_IDX1` (`StationKTId`,`RoomKTId`)
);

CREATE TABLE `tbl_standarddicsigctcc` (
  `SID` float DEFAULT NULL,
  `StdSPDicName` varchar(255)  DEFAULT NULL,
  `Version` float DEFAULT NULL,
  `SUDeviceTypeId2` float DEFAULT NULL,
  `DeviceHLTyp` float DEFAULT NULL,
  `DeviceTypeName` varchar(255)  DEFAULT NULL,
  `StandardDicId4` float DEFAULT NULL,
  `StandardName` varchar(255)  DEFAULT NULL,
  `SPType` varchar(255)  DEFAULT NULL,
  `AlarmMeanings` varchar(255)  DEFAULT NULL,
  `NormalMeanings` varchar(255)  DEFAULT NULL,
  `Unit` varchar(255)  DEFAULT NULL,
  `Xmlcolumn` varchar(255)  DEFAULT NULL,
  `SerinoCom` varchar(255)  DEFAULT NULL,
  `SignalMeanings` varchar(255)  DEFAULT NULL,
  `update` datetime DEFAULT NULL,
  `SystemType` varchar(255)  DEFAULT NULL,
  `remark` varchar(255)  DEFAULT NULL,
  `OptionID` float DEFAULT NULL,
  `AlarmLevel` float DEFAULT NULL,
  `AlarmThresbhold` float DEFAULT NULL,
  `StartDelay` float DEFAULT NULL,
  `EndDelay` float DEFAULT NULL,
  `Period` float DEFAULT NULL,
  `AbsoluteVal` varchar(255)  DEFAULT NULL,
  `RelativeVal` varchar(255)  DEFAULT NULL,
  `Hysteresis` float DEFAULT NULL,
  `AlarmLevel1` float DEFAULT NULL,
  `AlarmThresbhold1` float DEFAULT NULL,
  `StartDelay1` float DEFAULT NULL,
  `EndDelay1` float DEFAULT NULL,
  `Period1` float DEFAULT NULL,
  `AbsoluteVal1` varchar(255)  DEFAULT NULL,
  `RelativeVal1` varchar(255)  DEFAULT NULL,
  `Hysteresis1` float DEFAULT NULL,
  `AlarmLevel2` float DEFAULT NULL,
  `AlarmThresbhold2` float DEFAULT NULL,
  `StartDelay2` float DEFAULT NULL,
  `EndDelay2` float DEFAULT NULL,
  `Period2` float DEFAULT NULL,
  `AbsoluteVal2` varchar(255)  DEFAULT NULL,
  `RelativeVal2` varchar(255)  DEFAULT NULL,
  `Hysteresis2` float DEFAULT NULL,
  `AlarmLevel3` float DEFAULT NULL,
  `AlarmThresbhold3` float DEFAULT NULL,
  `StartDelay3` float DEFAULT NULL,
  `EndDelay3` float DEFAULT NULL,
  `Period3` float DEFAULT NULL,
  `AbsoluteVal3` varchar(255)  DEFAULT NULL,
  `RelativeVal3` varchar(255)  DEFAULT NULL,
  `Hysteresis3` float DEFAULT NULL,
  `AlarmLevel4` float DEFAULT NULL,
  `AlarmThresbhold4` float DEFAULT NULL,
  `StartDelay4` float DEFAULT NULL,
  `EndDelay4` float DEFAULT NULL,
  `Period4` float DEFAULT NULL,
  `AbsoluteVal4` varchar(255)  DEFAULT NULL,
  `RelativeVal4` varchar(255)  DEFAULT NULL,
  `Hysteresis4` varchar(255)  DEFAULT NULL,
  `AlarmLevel5` float DEFAULT NULL,
  `AlarmThresbhold5` float DEFAULT NULL,
  `StartDelay5` float DEFAULT NULL,
  `EndDelay5` float DEFAULT NULL,
  `Period5` float DEFAULT NULL,
  `AbsoluteVal5` varchar(255)  DEFAULT NULL,
  `RelativeVal5` varchar(255)  DEFAULT NULL,
  `Hysteresis5` varchar(255)  DEFAULT NULL,
  `AlarmLevel6` float DEFAULT NULL,
  `AlarmThresbhold6` float DEFAULT NULL,
  `StartDelay6` float DEFAULT NULL,
  `EndDelay6` float DEFAULT NULL,
  `Period6` float DEFAULT NULL,
  `AbsoluteVal6` float DEFAULT NULL,
  `RelativeVal6` float DEFAULT NULL,
  `Hysteresis6` varchar(255)  DEFAULT NULL,
  `AlarmLevel7` float DEFAULT NULL,
  `AlarmThresbhold7` varchar(255)  DEFAULT NULL,
  `StartDelay7` float DEFAULT NULL,
  `EndDelay7` float DEFAULT NULL,
  `Period7` float DEFAULT NULL,
  `AbsoluteVal7` varchar(255)  DEFAULT NULL,
  `RelativeVal7` varchar(255)  DEFAULT NULL,
  `Hysteresis7` varchar(255)  DEFAULT NULL,
  `AlarmLevel8` float DEFAULT NULL,
  `AlarmThresbhold8` float DEFAULT NULL,
  `StartDelay8` float DEFAULT NULL,
  `EndDelay8` float DEFAULT NULL,
  `Period8` float DEFAULT NULL,
  `AbsoluteVal8` varchar(255)  DEFAULT NULL,
  `RelativeVal8` varchar(255)  DEFAULT NULL,
  `Hysteresis8` varchar(255)  DEFAULT NULL,
  `AlarmLevel9` float DEFAULT NULL,
  `AlarmThresbhold9` float DEFAULT NULL,
  `StartDelay9` float DEFAULT NULL,
  `EndDelay9` float DEFAULT NULL,
  `Period9` float DEFAULT NULL,
  `AbsoluteVal9` varchar(255)  DEFAULT NULL,
  `RelativeVal9` varchar(255)  DEFAULT NULL,
  `Hysteresis9` varchar(255)  DEFAULT NULL,
  `InputType` varchar(255)  DEFAULT NULL,
  `comfrom` varchar(255)  DEFAULT NULL,
  `StandardDicId` varchar(20)  NOT NULL,
  `SUDeviceTypeId` varchar(10)  DEFAULT NULL,
  `DeviceTypeIdCTCC` varchar(10)  DEFAULT NULL,
  PRIMARY KEY (`StandardDicId`)
);

CREATE TABLE `tbl_stationcmcc` (
  `StationId` int NOT NULL,
  `SiteID` varchar(255) DEFAULT NULL,
  `SiteName` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`StationId`),
  KEY `TBL_StationCMCC_IDX1` (`StationId`)
);

CREATE TABLE `tbl_stationktctcc` (
  `StationKTName` varchar(128) NOT NULL,
  `StationKTId` varchar(40) NOT NULL,
  `FriendlyStationName` varchar(128) DEFAULT NULL,
  `BigRegionName` varchar(40) DEFAULT NULL,
  `SmallRegionName` varchar(40) DEFAULT NULL,
  `StationType` varchar(40) DEFAULT NULL,
  `StationLevel` varchar(40) DEFAULT NULL,
  `AMSStationName` varchar(128) DEFAULT NULL,
  `AMSStationId` int DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`StationKTId`),
  KEY `TBL_TBL_StationKTCTCC_IDX1` (`StationKTId`)
);

CREATE TABLE `tbl_storagerulecmcc` (
  `SiteID` varchar(125) NOT NULL,
  `FSUID` varchar(125) NOT NULL,
  `DeviceID` varchar(125) NOT NULL,
  `ID` varchar(20) NOT NULL,
  `SignalNumber` varchar(10) NOT NULL,
  `Type` int DEFAULT NULL,
  `AbsoluteVal` double DEFAULT NULL,
  `RelativeVal` double DEFAULT NULL,
  `StorageInterval` decimal(12,0) DEFAULT NULL,
  `StorageRefTime` varchar(80) DEFAULT NULL,
  `StorageRuleType` int NOT NULL,
  PRIMARY KEY (`DeviceID`,`FSUID`,`ID`,`SignalNumber`,`SiteID`,`StorageRuleType`)
);

CREATE TABLE `tbl_suportcucc` (
  `SUID` varchar(255) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `PortNo` varchar(128) NOT NULL,
  `PortName` varchar(128) DEFAULT NULL,
  `PortType` varchar(128) DEFAULT NULL,
  `Settings` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`PortNo`,`SUID`)
);

CREATE TABLE `tbl_suportdevicecucc` (
  `SUID` varchar(255) NOT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `PortNo` varchar(128) NOT NULL,
  `DeviceID` varchar(255) NOT NULL,
  `DeviceRID` varchar(255) DEFAULT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `Protocol` varchar(255) DEFAULT NULL,
  `Version` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
    PRIMARY KEY (`SUID`,`DeviceID`)
);

CREATE TABLE `tbl_systemktctcc` (
  `SystemktCTCCId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `StationKTName` varchar(128) NOT NULL,
  `StationKTId` varchar(40) NOT NULL,
  `SystemName` varchar(128) NOT NULL,
  `SystemState` varchar(40) DEFAULT NULL,
  `SystemTypeName` varchar(40) DEFAULT NULL,
  `IsAdded` int DEFAULT NULL,
  `RoomKTName` varchar(128) DEFAULT NULL,
  `RoomKTId` varchar(40) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_thresholdcmcc` (
  `SiteID` varchar(125) NOT NULL,
  `FSUID` varchar(125) NOT NULL,
  `DeviceID` varchar(125) NOT NULL,
  `ID` varchar(20) NOT NULL,
  `SignalNumber` varchar(10) NOT NULL,
  `Type` int DEFAULT NULL,
  `Threshold` double DEFAULT NULL,
  `AlarmLevel` int NOT NULL,
  `NMAlarmID` varchar(40) DEFAULT NULL,
  `ThresholdType` int NOT NULL,
  PRIMARY KEY (`DeviceID`,`FSUID`,`ID`,`SignalNumber`,`SiteID`,`ThresholdType`)
);

CREATE TABLE `tbl_tsignalcmcc` (
  `FSUID` varchar(20) NOT NULL,
  `DeviceID` varchar(26) NOT NULL,
  `ID` varchar(20) NOT NULL,
  `SignalNumber` varchar(5) NOT NULL,
  `Type` int DEFAULT NULL,
  `SignalName` varchar(80) DEFAULT NULL,
  `AlarmLevel` int DEFAULT NULL,
  `Threshold` double DEFAULT NULL,
  `AbsoluteVal` double DEFAULT NULL,
  `RelativeVal` double DEFAULT NULL,
  `Describe` varchar(120) DEFAULT NULL,
  `NMAlarmID` varchar(40) DEFAULT NULL,
  `EI1` int DEFAULT NULL,
  `ES1` varchar(255) DEFAULT NULL,
  `TSignalType` int NOT NULL,
  PRIMARY KEY (`DeviceID`,`FSUID`,`ID`,`SignalNumber`,`TSignalType`)
);

CREATE TABLE `tbl_tsignalgdctcc` (
  `SUID` varchar(40) NOT NULL,
  `DeviceID` varchar(40) NOT NULL,
  `SPID` varchar(40) NOT NULL,
  `SPName` varchar(128) DEFAULT NULL,
  `SPType` int DEFAULT NULL,
  `OptionID` int DEFAULT NULL,
  `Unit` varchar(20) DEFAULT NULL,
  `NormalMeanings` varchar(128) DEFAULT NULL,
  `AlarmMeanings` varchar(128) DEFAULT NULL,
  `ExtendField1` varchar(128) DEFAULT NULL,
  `ExtendField2` varchar(128) DEFAULT NULL,
  `EquipmentId` varchar(20) DEFAULT NULL,
  `SignalId` varchar(20) DEFAULT NULL,
  `ConditionId` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`DeviceID`,`SPID`,`SUID`),
  KEY `TBL_TSignalGDCTCC_IDX1` (`SUID`,`DeviceID`,`SPID`)
);

CREATE TABLE `tsl_monitorunitcmcc` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `FSUID` varchar(255) DEFAULT '',
  `FSUName` varchar(255) DEFAULT NULL,
  `SiteID` varchar(255) DEFAULT '',
  `SiteName` varchar(255) DEFAULT NULL,
  `RoomID` varchar(255) DEFAULT NULL,
  `RoomName` varchar(255) DEFAULT NULL,
  `UserName` varchar(40) DEFAULT NULL,
  `PassWord` varchar(40) DEFAULT NULL,
  `FSUIP` varchar(255) DEFAULT NULL,
  `FSUMAC` varchar(20) DEFAULT NULL,
  `FSUVER` varchar(20) DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `FailureCause` varchar(255) DEFAULT NULL,
  `CPUUsage` double DEFAULT NULL,
  `MEMUsage` double DEFAULT NULL,
  `HardDiskUsage` double DEFAULT NULL,
  `GetFSUInfoResult` int DEFAULT NULL,
  `GetFSUFaliureCause` varchar(255) DEFAULT NULL,
  `GetFSUTime` datetime DEFAULT NULL,
  `FTPUserName` varchar(40) DEFAULT NULL,
  `FTPPassWord` varchar(40) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `GetConfigFlag` int DEFAULT '0',
  PRIMARY KEY (`MonitorUnitId`,`StationId`),
  KEY `TSL_MonitorUnitCMCC_IDX1` (`StationId`,`MonitorUnitId`),
  KEY `IDX_MonitorUnitCMCC_1` (`FSUID`)
);

CREATE TABLE `tsl_monitorunitctcc` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `SUID` varchar(255)  DEFAULT NULL,
  `SUName` varchar(255)  DEFAULT NULL,
  `UserName` varchar(40)  DEFAULT NULL,
  `PassWord` varchar(40)  DEFAULT NULL,
  `SUIP` varchar(255)  DEFAULT NULL,
  `SUPort` int DEFAULT NULL,
  `SUVendor` varchar(255)  DEFAULT NULL,
  `SUModel` varchar(255)  DEFAULT NULL,
  `SUHardVer` varchar(20)  DEFAULT NULL,
  `SUSoftVer` varchar(20)  DEFAULT NULL,
  `SCIP` varchar(255)  DEFAULT NULL,
  `SCPort` int DEFAULT NULL,
  `WhiteIps` varchar(255)  DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `FailureCause` varchar(255)  DEFAULT NULL,
  `SUFtpUserName` varchar(40)  DEFAULT NULL,
  `SUFtpPassWord` varchar(40)  DEFAULT NULL,
  `SUFtpPort` int DEFAULT NULL,
  `CPUUsage` float DEFAULT NULL,
  `MEMUsage` float DEFAULT NULL,
  `SUDateTime` datetime DEFAULT NULL,
  `HardDiskUsage` float DEFAULT NULL,
  `GetSUInfoResult` int DEFAULT NULL,
  `GetSUFaliureCause` varchar(255)  DEFAULT NULL,
  `ExtendField1` varchar(255)  DEFAULT NULL,
  `ExtendField2` varchar(255)  DEFAULT NULL,
  `GetConfigFlag` int DEFAULT (0),
  PRIMARY KEY (`StationId`,`MonitorUnitId`),
  KEY `TSL_MonitorUnitCTCC_IDX1` (`StationId`,`MonitorUnitId`)
);

CREATE TABLE `tsl_monitorunitcucc` (
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `SUID` varchar(255) DEFAULT NULL,
  `SUName` varchar(255) DEFAULT NULL,
  `SURID` varchar(255) DEFAULT NULL,
  `UserName` varchar(40) DEFAULT NULL,
  `PassWord` varchar(40) DEFAULT NULL,
  `SUIP` varchar(255) DEFAULT NULL,
  `SUVER` varchar(20) DEFAULT NULL,
  `SUPort` varchar(20) DEFAULT NULL,
  `SUVendor` varchar(20) DEFAULT NULL,
  `SUModel` varchar(20) DEFAULT NULL,
  `SUHardVER` varchar(20) DEFAULT NULL,
  `Longitude` double DEFAULT NULL,
  `Latitude` double DEFAULT NULL,
  `Result` int DEFAULT NULL,
  `FailureCause` varchar(255) DEFAULT NULL,
  `CPUUsage` double DEFAULT NULL,
  `MEMUsage` double DEFAULT NULL,
  `GetSUInfoResult` int DEFAULT NULL,
  `GetSUTime` datetime DEFAULT NULL,
  `FTPUserName` varchar(40) DEFAULT NULL,
  `FTPPassWord` varchar(40) DEFAULT NULL,
  `ConfigState` int DEFAULT NULL,
  `RegisterTime` datetime DEFAULT NULL,
  `SUConfigTime` varchar(255) DEFAULT NULL,
  `CenterConfigTime` varchar(255) DEFAULT NULL,
  `Devices` text,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`MonitorUnitId`,`StationId`),
  KEY `TSL_MonitorUnitCUCC_IDX1` (`StationId`,`MonitorUnitId`)
);

CREATE TABLE `tsl_monitorunitgdctcc` (
  `SUID` varchar(40) NOT NULL,
  `SUName` varchar(128) NOT NULL,
  `MonitorUnitId` int DEFAULT NULL,
  `SerialNo` int DEFAULT NULL,
  `ReqFactoryCfg` int NOT NULL DEFAULT '0',
  `ExtendField1` varchar(128) DEFAULT NULL,
  `ExtendField2` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`SUID`),
  KEY `TSL_MonitorUnitGDCTCC_IDX1` (`SUID`)
);