CREATE TABLE `tbl_patrolallrecord_1` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_2` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_3` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_4` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_5` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_6` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_7` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_8` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_9` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_10` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_11` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolallrecord_12` (
  `PatrolAllRecordID` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolcalop` (
  `CalOpId` int NOT NULL,
  `CalOperator` varchar(128) NOT NULL,
  `Meaning` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CalOpId`)
);

CREATE TABLE `tbl_patrolcronexpression` (
  `CronId` int NOT NULL AUTO_INCREMENT,
  `CronExpression` varchar(128) NOT NULL DEFAULT '',
  `Meaning` varchar(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`CronId`)
);

CREATE TABLE `tbl_patrolexrecord` (
  `PatrolExRecordId` int PRIMARY KEY AUTO_INCREMENT,
  `CenterId` int NOT NULL,
  `CenterName` varchar(255) DEFAULT NULL,
  `GroupId` int NOT NULL,
  `GroupName` varchar(255) DEFAULT NULL,
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentCategoryName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `SignalValue` varchar(64) DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `WarningLevelId` int DEFAULT NULL,
  `Reasonable` int NOT NULL,
  `IsPowerOffAlarm` varchar(8) NOT NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `Sn` varchar(36) NOT NULL
);

CREATE TABLE `tbl_patrolgroup` (
  `GroupId` int NOT NULL AUTO_INCREMENT,
  `GroupName` varchar(128) NOT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GroupId`)
);

CREATE TABLE `tbl_patrolgroupbasesignalmap` (
  `PatrolGroupBaseSignalMap` int PRIMARY KEY AUTO_INCREMENT,
  `GroupId` int NOT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `BaseTypeId` decimal(10,0) DEFAULT NULL
);

CREATE TABLE `tbl_patrolgroupparameters` (
  `PatrolGroupParameters` int PRIMARY KEY AUTO_INCREMENT,
  `GroupId` int NOT NULL,
  `StationTypeIds` varchar(24) DEFAULT NULL,
  `StationIds` longtext,
  `EquipmentIds` longtext,
  `SignalIds` longtext
);

CREATE TABLE `tbl_patrolgrouprulemap` (
  `PatrolGroupRuleMap` int PRIMARY KEY AUTO_INCREMENT,
  `GroupId` int NOT NULL,
  `RuleId` int NOT NULL
);

CREATE TABLE `tbl_patrolrule` (
  `RuleId` int NOT NULL AUTO_INCREMENT,
  `RuleName` varchar(128) NOT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitDownCalOpId` int NOT NULL,
  `LimitUp` double DEFAULT NULL,
  `LimitUpCalOpId` int NOT NULL,
  `UnitId` int DEFAULT NULL,
  `WarningLevelId` int NOT NULL,
  `ByPercentage` int NOT NULL,
  `RatedValue` double DEFAULT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `EquipmentLogicClassId` int DEFAULT NULL,
  `StandardDicId` int DEFAULT NULL,
  PRIMARY KEY (`RuleId`)
);

CREATE TABLE `tbl_patroltask` (
  `TaskId` int NOT NULL AUTO_INCREMENT,
  `TaskName` varchar(128) NOT NULL,
  `CronId` int NOT NULL,
  `GroupId` int NOT NULL,
  `IsPowerOffSave` int NOT NULL DEFAULT '0',
  `Note` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`TaskId`)
);

CREATE TABLE `tbl_patrolunit` (
  `UnitId` int NOT NULL,
  `UnitSymbol` varchar(128) NOT NULL,
  `Meaning` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`UnitId`)
);

CREATE TABLE `tbl_patrolwarninglevel` (
  `LevelId` int NOT NULL,
  `Meaning` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LevelId`)
);

CREATE TABLE `tbl_patrolgroupstandardsignalmap` (
    `PatrolGroupStandardSignalMap` int PRIMARY KEY AUTO_INCREMENT,
    `GroupId` int NOT NULL,
    `EquipmentLogicClassId` int DEFAULT NULL,
    `StandardDicId` int DEFAULT NULL
);

CREATE TABLE `tbl_patrolstandardgroup` (
    `GroupId` int NOT NULL AUTO_INCREMENT,
    `GroupName` varchar(128) NOT NULL,
    `EquipmentLogicClassId` int DEFAULT NULL,
    `StandardDicId` int DEFAULT NULL,
    `Note` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`GroupId`)
);