CREATE TABLE `safemessage` (
  `SafeMessageId` int NOT NULL AUTO_INCREMENT,
  `ConfigName` varchar(128) DEFAULT NULL COMMENT '平安短信配置名称',
  `Receiver` varchar(255) DEFAULT NULL COMMENT '接收人 多个用逗号隔开',
  `ReceiveMode` varchar(128) DEFAULT NULL COMMENT '接收方式 1短信  2语音 3.邮件 多个用逗号隔开',
  `SendTime` time DEFAULT NULL COMMENT '发送的具体时间',
  `SendType` int DEFAULT NULL COMMENT '发送时间类型 1每天 2每周 3每月',
  `SendTypeDescription` varchar(100) DEFAULT NULL COMMENT '每周的星期几与每月的多少号，多个用逗号隔开',
  `<PERSON>ron` varchar(100) DEFAULT NULL COMMENT 'corn表达式用于定时发送平安短信',
  `ContentTemplate` varchar(255) DEFAULT NULL COMMENT '模板内容',
  `Description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `UsedStatus` tinyint(1) DEFAULT NULL COMMENT '启用状态',
  PRIMARY KEY (`SafeMessageId`),
  UNIQUE KEY `SafeMessageId` (`SafeMessageId`)
);

CREATE TABLE `safemessageelementconfig` (
  `SafeMessageElementConfigId` int NOT NULL AUTO_INCREMENT,
  `SafeMessageId` int DEFAULT NULL COMMENT '平安短信配置id',
  `ElementType` int DEFAULT NULL COMMENT '元素类型 1信号 2指标',
  `ElementSetting` text COMMENT '元素配置',
  PRIMARY KEY (`SafeMessageElementConfigId`),
  UNIQUE KEY `SafeMessageElementConfigId` (`SafeMessageElementConfigId`)
);

CREATE TABLE `safemessagerecord` (
  `SafeMessageRecordId` int NOT NULL AUTO_INCREMENT,
  `SafeMessageId` int DEFAULT NULL COMMENT '平安短信配置id',
  `SendContent` varchar(255) DEFAULT NULL COMMENT '发送内容',
  `Receiver` varchar(255) DEFAULT NULL COMMENT '接收人',
  `SendTime` datetime DEFAULT NULL COMMENT '发送时间',
  `Remark` varchar(128) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`SafeMessageRecordId`),
  UNIQUE KEY `SafeMessageRecordId` (`SafeMessageRecordId`)
);