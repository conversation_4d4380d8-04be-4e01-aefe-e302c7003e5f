CREATE TABLE `tbl_activecontrolofdoor` (
  `StationId` int NOT NULL,
  `HostId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `ControlId` int NOT NULL,
  `UserId` int NOT NULL,
  `ParameterValues` text,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdate` datetime NOT NULL,
  `Id` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`Id`),
  KEY `IDX_ActiveDoorControl_HostId` (`HostId`),
  KEY `IDX_ActiveDoorControl_Complex` (`StationId`,`EquipmentId`,`ControlId`,`UserId`)
);

CREATE TABLE `tbl_activecontrolofdoor_fail` (
  `ActiveControlOfDoorFailId` int PRIMARY KEY AUTO_INCREMENT,
  `StationId` int NOT NULL,
  `HostId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `ControlId` int NOT NULL,
  `UserId` int NOT NULL,
  `ParameterValues` text,
  `Description` varchar(255) DEFAULT NULL,
  `LastUpdate` datetime NOT NULL,
  `SendResult` int DEFAULT NULL,
  KEY `IDX_tbl_activecontrolofdoor_fail_HostId` (`HostId`),
  KEY `IDX_tbl_activecontrolofdoor_fail_Complex` (`StationId`,`EquipmentId`,`ControlId`,`UserId`)
);

CREATE TABLE `tbl_card` (
  `CardId` int NOT NULL,
  `CardCode` varchar(20) NOT NULL,
  `CardName` varchar(128) DEFAULT NULL,
  `CardCategory` int DEFAULT NULL,
  `CardGroup` int DEFAULT NULL,
  `UserId` int DEFAULT NULL,
  `StationId` int DEFAULT NULL,
  `CardStatus` int DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `RegisterTime` datetime DEFAULT NULL,
  `UnRegisterTime` datetime DEFAULT NULL,
  `LostTime` datetime DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CardId`)
);

CREATE TABLE `tbl_cardext` (
  `CardId` int NOT NULL,
  `Password` varchar(30) DEFAULT NULL,
  `cardcodetype` int DEFAULT NULL COMMENT '卡号类型',
  `cardcodeconv` varchar(20) DEFAULT NULL COMMENT '卡号转换(tbl_card.cardcode的十进制或十六进制)',
  PRIMARY KEY (`CardId`)
);

CREATE TABLE `tbl_cardtypemap` (
  `CardId` int PRIMARY KEY,
  `CardType` int NOT NULL
);

CREATE TABLE `tbl_ddscardno` (
  `Id` int primary key AUTO_INCREMENT,
  `DoorId` int DEFAULT NULL,
  `CardId` int DEFAULT NULL,
  `CardNo` int DEFAULT NULL
);

CREATE TABLE `tbl_door` (
  `DoorId` int NOT NULL,
  `DoorNo` int NOT NULL,
  `DoorName` varchar(128) DEFAULT NULL,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `SamplerUnitId` int DEFAULT NULL,
  `Category` int NOT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `WorkMode` int DEFAULT NULL,
  `Infrared` int DEFAULT NULL,
  `Password` varchar(10) DEFAULT NULL,
  `DoorControlId` int DEFAULT NULL,
  `DoorInterval` int DEFAULT NULL,
  `OpenDelay` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `OpenMode` int DEFAULT NULL,
  PRIMARY KEY (`DoorId`),
  KEY `idx_door_equipment` (`EquipmentId`,`DoorId`)
);

CREATE TABLE `tbl_doorarea` (
  `areaid` int NOT NULL COMMENT '门区域ID',
  `areaname` varchar(255) DEFAULT NULL COMMENT '门区域名称',
  `parentid` int DEFAULT NULL COMMENT '父门区域ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`areaid`)
);

CREATE TABLE `tbl_doorareamap` (
  `areaid` int NOT NULL COMMENT '门区域ID',
  `equipmentid` int NOT NULL COMMENT '门设备ID',
  PRIMARY KEY `equipmentid` (`equipmentid`)
);

CREATE TABLE `tbl_doorcard` (
  `CardId` int NOT NULL,
  `TimeGroupId` int NOT NULL,
  `DoorId` int NOT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Password` varchar(30) DEFAULT NULL,
  `timegrouptype` int NOT NULL,
  PRIMARY KEY (`CardId`,`DoorId`,`TimeGroupId`,`timegrouptype`)
);

CREATE TABLE `tbl_doorcardlost` (
  `CardId` int NOT NULL,
  `TimeGroupId` int NOT NULL,
  `DoorId` int NOT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Password` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`CardId`,`DoorId`,`TimeGroupId`)
);

CREATE TABLE `tbl_doorcontroller` (
  `DoorControlId` int NOT NULL,
  `DoorControlName` varchar(128) NOT NULL,
  `LicenseKey` varchar(30) DEFAULT NULL,
  `Display` int DEFAULT NULL,
  `CardLength` int NOT NULL,
  `MaxDoorCount` int NOT NULL,
  `DLLPath` varchar(128) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`DoorControlId`)
);

CREATE TABLE `tbl_doorgroup` (
  `DoorGroupId` int NOT NULL,
  `DoorGroupName` varchar(255) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `LastTime` datetime DEFAULT NULL,
  PRIMARY KEY (`DoorGroupId`)
);

CREATE TABLE `tbl_doorgroupmap` (
  `DoorGroupMapId` int PRIMARY KEY AUTO_INCREMENT,
  `DoorGroupId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `LastTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_doorparam` (
  `doorid` int NOT NULL COMMENT '门ID',
  `paramtype` int NOT NULL COMMENT '参数类型',
  `paramvalue` int DEFAULT NULL COMMENT '门参数值',
  PRIMARY KEY (`doorid`,`paramtype`)
);

CREATE TABLE `tbl_doorproperty` (
  `category` int NOT NULL COMMENT '门类型',
  `propertytype` int NOT NULL COMMENT '属性类型',
  PRIMARY KEY `category` (`category`,`propertytype`)
);

CREATE TABLE `tbl_doorpropertymeaning` (
  `category` int NOT NULL COMMENT '门类型',
  `propertytype` int NOT NULL COMMENT '属性类型',
  `propertyid` int NOT NULL COMMENT '属性ID',
  `meaning` varchar(255) DEFAULT NULL COMMENT '属性含义',
  PRIMARY KEY `category` (`category`,`propertytype`,`propertyid`)
);

CREATE TABLE `tbl_doortimegroup` (
  `DoorId` int NOT NULL,
  `TimeGroupId` int NOT NULL,
  `TimeGroupType` int NOT NULL,
  PRIMARY KEY (`DoorId`,`TimeGroupId`,`TimeGroupType`)
);

CREATE TABLE `tbl_facedata` (
  `FaceId` int NOT NULL,
  `FaceData` longblob NOT NULL,
  PRIMARY KEY (`FaceId`)
);

CREATE TABLE `tbl_facedataauth` (
  `TimeGroupId` int NOT NULL,
  `DoorId` int NOT NULL,
  `CardId` int NOT NULL,
  `FaceId` int NOT NULL,
  `LastUpdateTime` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`CardId`,`DoorId`,`FaceId`,`TimeGroupId`)
);

CREATE TABLE `tbl_facedatamap` (
  `CardId` int NOT NULL,
  `FaceId` int NOT NULL,
  `LastUpdater` int NOT NULL,
  `LastUpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`CardId`,`FaceId`)
);

CREATE TABLE `tbl_fingerprint` (
  `FingerPrintId` int NOT NULL,
  `FingerPrintNO` int NOT NULL,
  `FingerPrintData` longblob NOT NULL,
  PRIMARY KEY (`FingerPrintId`,`FingerPrintNO`)
);

CREATE TABLE `tbl_fingerprintauth` (
  `TimeGroupId` int NOT NULL,
  `DoorId` int NOT NULL,
  `CardId` int NOT NULL,
  `FingerPrintId` int NOT NULL,
  `LastUpdateTime` datetime NOT NULL,
  PRIMARY KEY (`CardId`,`DoorId`,`FingerPrintId`,`TimeGroupId`)
);

CREATE TABLE `tbl_fingerprintcardmap` (
  `CardId` int NOT NULL,
  `FingerPrintId` int NOT NULL,
  `Vendor` int DEFAULT NULL,
  `LastUpdater` int NOT NULL,
  `LastUpdateTime` datetime NOT NULL,
  PRIMARY KEY (`CardId`,`FingerPrintId`)
);

CREATE TABLE `tbl_fingerprintdatadistributelogrecord` (
  `CardId` int NOT NULL,
  `ReaderEquipId` int NOT NULL,
  `Log` text,
  `IsDistributeSucceed` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`CardId`,`ReaderEquipId`)
);

CREATE TABLE `tbl_fingerprintdatamap` (
  `CardId` int NOT NULL,
  `FingerUserId` varchar(40) DEFAULT NULL,
  `FingerPrintNO` int NOT NULL,
  `Password` varchar(20) DEFAULT NULL,
  `FingerPrintData1` longblob NOT NULL,
  `FingerPrintData2` longblob,
  `FingerPrintData3` longblob,
  `FingerDataSize1` int NOT NULL,
  `FingerDataSize2` int NOT NULL,
  `FingerDataSize3` int NOT NULL,
  `IsDistributeSucceed` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`CardId`,`FingerPrintNO`)
);

CREATE TABLE `tbl_fingerprintmap` (
  `FingerPrintMapId` int PRIMARY KEY AUTO_INCREMENT,
  `CardId` int NOT NULL,
  `FingerUserId` int NOT NULL,
  `FingerPrintNO` int NOT NULL,
  `FingerPrintData` longblob NOT NULL,
  `FingerStatus` int NOT NULL
);

CREATE TABLE `tbl_fingerreadermap` (
  `ReaderId` int NOT NULL,
  `ReaderName` varchar(128) NOT NULL,
  `ReaderType` int NOT NULL,
  `ReaderCommType` int NOT NULL,
  `ReaderCommAddress` varchar(255) NOT NULL,
  `SamplerType` int NOT NULL,
  `PortId` int NOT NULL,
  `NetId` int DEFAULT NULL,
  `DoorId` int DEFAULT NULL,
  `MultiDoorOpenType` int DEFAULT NULL,
  `DoorInOutFlag` int DEFAULT NULL,
  `IsDistributeSucceed` int DEFAULT '0',
  `IsClearConfigSucceed` int DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ReaderId`)
);

CREATE TABLE `tbl_swapcardrecord` (
  `SwapCardRecordId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `CardStationId` int DEFAULT NULL,
  `CardStationName` varchar(255) DEFAULT NULL,
  `CardId` int DEFAULT NULL,
  `CardCode` varchar(255) DEFAULT NULL,
  `CardName` varchar(255) DEFAULT NULL,
  `CardUserId` int DEFAULT NULL,
  `CardUserName` varchar(255) DEFAULT NULL,
  `CardCategory` int DEFAULT NULL,
  `CardCategoryName` varchar(255) DEFAULT NULL,
  `CardGroup` int DEFAULT NULL,
  `CardGroupName` varchar(255) DEFAULT NULL,
  `CardStatus` int DEFAULT NULL,
  `CardStatusName` varchar(255) DEFAULT NULL,
  `DoorId` int DEFAULT NULL,
  `DoorNo` int DEFAULT NULL,
  `DoorName` varchar(255) DEFAULT NULL,
  `DoorCategory` int DEFAULT NULL,
  `DoorCategoryName` varchar(255) DEFAULT NULL,
  `Valid` int DEFAULT NULL,
  `ValidName` varchar(128) DEFAULT NULL,
  `Enter` smallint DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL,
  UNIQUE KEY `TBL_SwapCardRecord_IDX1` (`RecordTime`,`CardId`,`DoorNo`)
);

CREATE TABLE `tbl_swapcardrecordmid` (
  `SwapCardRecordIdMid` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `CardStationId` int DEFAULT NULL,
  `CardStationName` varchar(255) DEFAULT NULL,
  `CardId` int DEFAULT NULL,
  `CardCode` varchar(255) DEFAULT NULL,
  `CardName` varchar(255) DEFAULT NULL,
  `CardUserId` int DEFAULT NULL,
  `CardUserName` varchar(255) DEFAULT NULL,
  `CardCategory` int DEFAULT NULL,
  `CardCategoryName` varchar(255) DEFAULT NULL,
  `CardGroup` int DEFAULT NULL,
  `CardGroupName` varchar(255) DEFAULT NULL,
  `CardStatus` int DEFAULT NULL,
  `CardStatusName` varchar(255) DEFAULT NULL,
  `DoorId` int DEFAULT NULL,
  `DoorNo` int DEFAULT NULL,
  `DoorName` varchar(255) DEFAULT NULL,
  `DoorCategory` int DEFAULT NULL,
  `DoorCategoryName` varchar(255) DEFAULT NULL,
  `Valid` int DEFAULT NULL,
  `ValidName` varchar(128) DEFAULT NULL,
  `Enter` smallint DEFAULT NULL,
  `RecordTime` datetime DEFAULT NULL
);

CREATE TABLE `doortag` (
    `TagId`       int          NOT NULL AUTO_INCREMENT COMMENT '设置主键自增',
    `TagName`     varchar(255) NOT NULL COMMENT '标签名称',
    `TagIcon`     varchar(255) DEFAULT NULL COMMENT '标签图标',
    `TagColor`    varchar(255) DEFAULT NULL COMMENT '标签颜色',
    `TagDescribe` varchar(255) DEFAULT NULL COMMENT '标签描述',
    PRIMARY KEY (`TagId`)
);

CREATE TABLE `doortagmap` (
    `Id`          int NOT NULL AUTO_INCREMENT COMMENT '设置主键自增',
    `EquipmentId` int DEFAULT NULL,
    `TagId`       int NOT NULL COMMENT '标签主键id',
    PRIMARY KEY (`Id`)
);

CREATE TABLE `doorcardbackup` (
    `DoorCardBackupId` int NOT NULL AUTO_INCREMENT COMMENT '设置主键自增',
    `Type`             int NOT NULL COMMENT '类型 1设备 2卡',
    `Id`               int NOT NULL COMMENT '设备或者卡的id',
    `DeleteId`         varchar(200) DEFAULT NULL,
    `DeleteName`       varchar(200) DEFAULT NULL,
    `DeleteTime`       varchar(255) DEFAULT NULL COMMENT '被删除的时间',
    PRIMARY KEY (`DoorCardBackupId`)
);
