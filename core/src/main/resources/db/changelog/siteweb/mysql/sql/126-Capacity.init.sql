-- ============================================
-- ============== 容量基类定义 ================
-- ============================================
INSERT INTO CapacityBaseType(BaseTypeId, BaseTypeName, Description) VALUES (1000000,'机架容量','机架管理、3D、U位、容量业务');
INSERT INTO CapacityBaseType(BaseTypeId, BaseTypeName, Description) VALUES (1000001,'电力容量','配电容量预警业务');


-- ============================================
-- ============ 容量基类属性定义 ==============
-- ============================================


-- 机架管理 容量相关  2000001
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2000000,'U位使用容量' ,1000000,'rackSpace',  0,'U','机架U位占用容量');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2000001,'结构承重容量',1000000,'rackWeight', 0,'kg','机架重量承重容量');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2000002,'制冷冷却容量',1000000,'rackCooling',0,'kJ','机架制冷冷却容量');
-- 配电容量预警相关   2010000
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010000,'功率负载容量',1000001,'comPower',   1,'kVA','通用的设备视在功率负载容量');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010001,'电压负载容量',1000001,'comVoltage', 1,'V','通用的设备电压负载容量');
INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010002,'电流负载容量',1000001,'comCurrent', 1,'A','通用的设备电流负载容量');

-- INSERT INTO CapacityBaseAttribute(BaseAttributeId, BaseAttributeName, BaseTypeId, AttributeName, LogicType, Unit, Description) VALUES (2010003,'视在功率容量',1000001,'comApparentPower', 1,'kVA','通用的设备视在功率负载容量');