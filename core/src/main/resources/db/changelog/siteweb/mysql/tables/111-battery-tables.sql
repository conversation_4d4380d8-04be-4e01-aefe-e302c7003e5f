CREATE TABLE `batterycellmodel` (
  `BatteryCellModelId` int NOT NULL AUTO_INCREMENT,
  `Vendor` varchar(128) DEFAULT NULL COMMENT '电池厂商',
  `Model` varchar(255) DEFAULT NULL COMMENT '电池型号',
  `VoltageType` int DEFAULT NULL COMMENT '电压类型',
  `RatedVoltage` decimal(5,2) DEFAULT NULL COMMENT '额定电压',
  `RatedCapacity` decimal(6,2) DEFAULT NULL COMMENT '额定容量',
  `InitialIR` decimal(10,2) DEFAULT NULL COMMENT '初始内阻',
  `FloatChargeVoltage` decimal(5,2) DEFAULT NULL COMMENT '浮充电压',
  `EvenChargeVoltage` decimal(5,2) DEFAULT NULL COMMENT '均充电压',
  `TempCompensationFactor` decimal(3,2) DEFAULT NULL COMMENT '温度补偿系数',
  `TerminationVoltage` decimal(5,2) DEFAULT NULL COMMENT '终止电压',
  `ChargingCurrentLimitFactor` decimal(5,2) DEFAULT NULL COMMENT '充电限流系数',
  `DischargeCurrentLimitFactor` decimal(5,2) DEFAULT NULL COMMENT '放电限流系数',
  `Length` int DEFAULT NULL COMMENT '长度',
  `Width` int DEFAULT NULL COMMENT '宽度',
  `Height` int DEFAULT NULL COMMENT '高度',
  `Weight` decimal(10,2) DEFAULT NULL COMMENT '重量',
  PRIMARY KEY (`BatteryCellModelId`),
  UNIQUE KEY `BatteryCellModelId` (`BatteryCellModelId`)
);

CREATE TABLE `batterydischargerecord` (
  `BatteryDischargeRecordId` int NOT NULL AUTO_INCREMENT,
  `BatteryStringId` int DEFAULT NULL COMMENT '电池配置Id',
  `EquipmentId` int DEFAULT NULL COMMENT '设备ID',
  `StartDischargeTime` datetime DEFAULT NULL COMMENT '放电开始时间',
  `EndDischargeTime` datetime DEFAULT NULL COMMENT '放电结束时间',
  `SignalId` int DEFAULT NULL COMMENT '放电触发的信号ID',
  PRIMARY KEY (`BatteryDischargeRecordId`),
  UNIQUE KEY `BatteryDischargeRecordId` (`BatteryDischargeRecordId`)
);

CREATE TABLE `batterystring` (
  `BatteryStringId` int NOT NULL AUTO_INCREMENT,
  `BatteryStringName` varchar(128) DEFAULT NULL COMMENT '电池配置名称',
  `CellCount` int DEFAULT NULL COMMENT '电池节数量',
  `EquipmentId` int DEFAULT NULL COMMENT '设备ID',
  `BatteryCellModelId` int DEFAULT NULL COMMENT '电池模型ID',
  `StandbyPower` int DEFAULT NULL COMMENT '备电时间',
  `StartUsingTime` datetime DEFAULT NULL COMMENT '启用时间',
  `CurrentTransformerType` varchar(255) DEFAULT NULL COMMENT '电流互感器类型',
  `Vendor` varchar(255) DEFAULT NULL COMMENT '厂家',
  `Model` varchar(255) DEFAULT NULL COMMENT '型号',
  `RatedVoltage` decimal(5,2) DEFAULT NULL COMMENT '额定电压',
  `RatedCapacity` decimal(6,2) DEFAULT NULL COMMENT '额定容量',
  `InitialIR` decimal(10,2) DEFAULT NULL COMMENT '初始内阻',
  `FloatChargeVoltage` decimal(5,2) DEFAULT NULL COMMENT '浮充电压',
  `EvenChargeVoltage` decimal(5,2) DEFAULT NULL COMMENT '均充电压',
  `TempCompensationFactor` decimal(3,2) DEFAULT NULL COMMENT '温度补偿系数',
  `TerminationVoltage` decimal(10,2) DEFAULT NULL COMMENT '终止电压',
  `MaxChargingCurrent` decimal(5,2) DEFAULT NULL COMMENT '最大均充充电电流',
  `MaxFloatChargeVoltage` decimal(10,2) DEFAULT NULL COMMENT '最大浮充电压',
  `Weight` decimal(10,2) DEFAULT NULL COMMENT '重量',
  PRIMARY KEY (`BatteryStringId`),
  UNIQUE KEY `BatteryStringId` (`BatteryStringId`)
);