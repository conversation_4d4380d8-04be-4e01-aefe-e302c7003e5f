CREATE TABLE `tbl_report` (
  `ReportId` int NOT NULL,
  `ReportName` varchar(255) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ReportFileId` varchar(255) NOT NULL,
  `ReportFileName` varchar(255) DEFAULT NULL,
  `PreviewImageName` varchar(255) DEFAULT NULL,
  `CreateUserId` int DEFAULT NULL,
  `CreateUserName` varchar(255) DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `UpdateUserId` int DEFAULT NULL,
  `UpdateUserName` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Version` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`ReportId`)
);

CREATE TABLE `tbl_reportbrowehistory` (
  `ReportBroweHistoryId` int PRIMARY KEY AUTO_INCREMENT,
  `HistoryId` int NOT NULL,
  `UserId` int DEFAULT NULL,
  `ReportId` int DEFAULT NULL,
  `ViewCount` int DEFAULT NULL,
  `LastBrowseTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_reportchart` (
  `ReportId` int NOT NULL,
  `ChartId` int NOT NULL,
  `ChartType` varchar(20) DEFAULT NULL,
  `ChartName` varchar(128) DEFAULT NULL,
  `XAxis` varchar(128) DEFAULT NULL,
  `YAxis` varchar(128) DEFAULT NULL,
  `Serials` varchar(128) DEFAULT NULL,
  `SerialsName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ChartId`,`ReportId`)
);

CREATE TABLE `tbl_reportgroup` (
  `Id` int PRIMARY KEY AUTO_INCREMENT,
  `ReportGroupId` int NOT NULL,
  `GroupName` varchar(255) NOT NULL,
  `UserId` int DEFAULT NULL,
  `GroupType` int DEFAULT '0',
  `Description` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_reportgroupmap` (
  `ReportGroupMap` int PRIMARY KEY AUTO_INCREMENT,
  `ReportId` int NOT NULL,
  `ReportGroupId` int NOT NULL
);

CREATE TABLE `tbl_reportparameter` (
  `ParameterId` int NOT NULL,
  `ParameterName` varchar(128) DEFAULT NULL,
  `ParameterKey` varchar(128) DEFAULT NULL,
  `ProcedureName` varchar(128) NOT NULL,
  `ParameterString` text,
  `ParameterField` varchar(128) DEFAULT NULL,
  `ParameterType` varchar(20) DEFAULT NULL,
  `ParameterFrom` varchar(20) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ParameterId`)
);

CREATE TABLE `tbl_reportparametermap` (
  `ReportId` int NOT NULL,
  `ParameterId` int NOT NULL,
  PRIMARY KEY (`ParameterId`,`ReportId`)
);

CREATE TABLE `tbl_reportprocedure` (
  `ReportId` int NOT NULL,
  `ProcedureName` varchar(128) NOT NULL,
  `ParameterString` text,
  `ReportColumn` longtext,
  `ReportType` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ReportId`)
);

CREATE TABLE `tbl_reportquery` (
  `ReportQueryId` int PRIMARY KEY AUTO_INCREMENT,
  `QueryID` int DEFAULT NULL,
  `ReportId` int DEFAULT NULL,
  `Name` varchar(255) DEFAULT NULL,
  `TaskId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `QueryTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_reportqueryparameter` (
  `ReportQueryParameterId` int PRIMARY KEY AUTO_INCREMENT,
  `QueryParameterId` int NOT NULL,
  `QueryID` int DEFAULT NULL,
  `ReportId` int DEFAULT NULL,
  `ParameterName` varchar(255) DEFAULT NULL,
  `DataType` varchar(64) DEFAULT NULL,
  `Value` longtext
);

CREATE TABLE `tbl_reportrole` (
  `ReportRoleId` int NOT NULL,
  `RoleName` varchar(50) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ReportRoleId`)
);

CREATE TABLE `tbl_reportrolemap` (
  `ReportId` int NOT NULL,
  `ReportRoleId` int NOT NULL,
  PRIMARY KEY (`ReportId`,`ReportRoleId`)
);

CREATE TABLE `tbl_reportroleusermap` (
  `ReportRoleUserMapId` int PRIMARY KEY AUTO_INCREMENT,
  `ReportRoleId` int NOT NULL,
  `UserId` int NOT NULL
);

CREATE TABLE `tbl_reporttask` (
  `TaskId` int NOT NULL,
  `TaskName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ReportId` int NOT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `OutputTime` datetime DEFAULT NULL,
  `OutputInterval` int DEFAULT NULL,
  `OutputIntervalType` int DEFAULT NULL,
  `QueryPeriod` int DEFAULT NULL,
  `QueryPeriodType` int DEFAULT NULL,
  `QueryParameters` longtext NOT NULL,
  `CommandText` varchar(255) DEFAULT NULL,
  `CommandType` varchar(128) DEFAULT NULL,
  `CenterType` int NOT NULL DEFAULT '0',
  `QueryCount` int DEFAULT NULL,
  `EmailOut` int NOT NULL DEFAULT '0',
  `EmailUrl` longtext,
  `Creator` int DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `LastUpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`TaskId`)
);

CREATE TABLE `tbl_reporttaskfile` (
  `FileId` int NOT NULL AUTO_INCREMENT,
  `TaskId` int DEFAULT NULL,
  `ReportId` int DEFAULT NULL,
  `FileName` varchar(128) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `QueryTime` datetime DEFAULT NULL,
  PRIMARY KEY (`FileId`)
);

CREATE TABLE `tbl_reportusermap` (
  `ReportUserMapId` int PRIMARY KEY AUTO_INCREMENT,
  `ReportId` int DEFAULT NULL,
  `UserId` int DEFAULT NULL
);

CREATE TABLE `tbl_suitreport` (
  `SuitReportId` int NOT NULL PRIMARY KEY,
  `SuitReportName` varchar(255) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `CreateUserId` int DEFAULT NULL,
  `CreateUserName` varchar(255) DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `UpdateUserId` int DEFAULT NULL,
  `UpdateUserName` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_suitreportmap` (
  `SuitReportId` int NOT NULL,
  `ReportId` int NOT NULL,
    PRIMARY KEY (`SuitReportId`,ReportId)
);

CREATE TABLE `tbl_suitreportrolemap` (
  `SuitReportId` int NOT NULL,
  `ReportRoleId` int NOT NULL,
  PRIMARY KEY (`SuitReportId`,`ReportRoleId`)
);