CREATE TABLE `schedule` (
  `ScheduleId` int NOT NULL AUTO_INCREMENT,
  `ShiftGroupMapId` int DEFAULT NULL COMMENT '人员班次映射id',
  `ShiftId` int DEFAULT NULL COMMENT '班次id',
  `ScheduleTime` date DEFAULT NULL COMMENT '排版日期',
  PRIMARY KEY (`ScheduleId`),
  UNIQUE KEY `ScheduleId` (`ScheduleId`)
);

CREATE TABLE `shift` (
  `ShiftId` int NOT NULL AUTO_INCREMENT,
  `ShiftName` varchar(50) DEFAULT NULL COMMENT '班次名称',
  `ShiftStartTime` time DEFAULT NULL COMMENT '班次开始时间',
  `ShiftEndTime` time DEFAULT NULL COMMENT '班次结束时间',
  `ShiftColor` varchar(50) DEFAULT NULL COMMENT '班次颜色',
  `DepartmentId` int DEFAULT NULL COMMENT '部门Id',
  PRIMARY KEY (`ShiftId`),
  UNIQUE KEY `ShiftId` (`ShiftId`)
);

CREATE TABLE `shiftgroup` (
  `ShiftGroupId` int NOT NULL AUTO_INCREMENT,
  `ShiftGroupName` varchar(50) DEFAULT NULL COMMENT '班组名称',
  `Remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `DepartmentId` int DEFAULT NULL COMMENT '部门id',
  PRIMARY KEY (`ShiftGroupId`),
  UNIQUE KEY `ShiftGroupId` (`ShiftGroupId`)
);

CREATE TABLE `shiftgroupmap` (
  `ShiftGroupMapId` int NOT NULL AUTO_INCREMENT,
  `ShiftGroupId` int DEFAULT NULL COMMENT '班组id',
  `EmployeeId` int DEFAULT NULL COMMENT '员工id',
  `DisplayIndex` int DEFAULT NULL COMMENT '展示顺序',
  PRIMARY KEY (`ShiftGroupMapId`),
  UNIQUE KEY `ShiftGroupMapId` (`ShiftGroupMapId`)
);