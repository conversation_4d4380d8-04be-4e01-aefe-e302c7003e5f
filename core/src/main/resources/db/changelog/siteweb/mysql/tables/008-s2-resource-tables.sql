CREATE TABLE `tbl_resourceequipment` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(255) DEFAULT NULL,
  `SystemType` varchar(255) DEFAULT NULL,
  `SystemId` varchar(255) DEFAULT NULL,
  `SensorLocation` varchar(255) DEFAULT NULL,
  `MonitoredEquipmentName` varchar(255) DEFAULT NULL,
  `RateCapacity` varchar(255) DEFAULT NULL,
  `DeviceId` varchar(255) DEFAULT NULL,
  `AssetId` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `IsConfig` int DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_resourcehouse` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `HouseId` int NOT NULL,
  `HouseName` varchar(255) DEFAULT NULL,
  `HouseNo` decimal(18,0) DEFAULT NULL,
  `SystemNo` decimal(18,0) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `IsConfig` int DEFAULT NULL,
  PRIMARY KEY (`HouseId`,`StationId`)
);

CREATE TABLE `tbl_resourcesignal` (
  `EquipmentTemplateId` int NOT NULL,
  `EquipmentTemplateName` varchar(255) DEFAULT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) DEFAULT NULL,
  `ChannelNo` int DEFAULT NULL,
  `ChannelLevel` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `IsConfig` int DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`,`SignalId`)
);

CREATE TABLE `tbl_resourcestation` (
  `StationId` int NOT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `StationAddress` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `IsConfig` int DEFAULT NULL,
  PRIMARY KEY (`StationId`)
);