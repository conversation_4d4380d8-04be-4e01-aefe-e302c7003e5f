CREATE TABLE `wo_diccmdchecklist` (
  `CheckId` int NOT NULL AUTO_INCREMENT,
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `CheckType` varchar(10) DEFAULT NULL,
  `IsMust` varchar(10) DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CheckId`)
);

CREATE TABLE `wo_diceventcategorymap` (
  `DicEventCategoryMapId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128)  DEFAULT NULL,
  `BaseTypeId` decimal(10,0) NOT NULL,
  `BaseTypeName` varchar(128)  NOT NULL,
  `CheckType` varchar(10)  DEFAULT NULL,
  `IsMust` varchar(10)  DEFAULT NULL,
  `SiteWebEventCategory` varchar(255)  DEFAULT NULL,
  `SiteWebEventCategoryId` int DEFAULT NULL
);

CREATE TABLE `wo_diceventchecklist` (
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `CheckType` varchar(10) DEFAULT NULL,
  `IsMust` varchar(10) DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `CheckId` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`CheckId`)
);

CREATE TABLE `wo_dicsigchecklist` (
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `CheckType` varchar(10) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitUp` double DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `CheckId` int NOT NULL AUTO_INCREMENT,
  `IsMust` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`CheckId`)
);

CREATE TABLE `wo_dicstationtypemap` (
  `WR_ItemId` int NOT NULL,
  `SiteWeb_ItemId` int DEFAULT NULL,
  `SaveTime` datetime DEFAULT NULL,
  PRIMARY KEY (`WR_ItemId`)
);

CREATE TABLE `wo_dicuserartselfcheck` (
  `CheckDicId` int NOT NULL,
  `CheckDicNote` text NOT NULL,
  PRIMARY KEY (`CheckDicId`)
);

CREATE TABLE `wo_dicuserexpertcheck` (
  `CheckDicId` int NOT NULL,
  `CheckDicNote` text NOT NULL,
  PRIMARY KEY (`CheckDicId`)
);

CREATE TABLE `wo_fileuploadrec` (
  `FId` int NOT NULL AUTO_INCREMENT,
  `OrderId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `FType` varchar(255) DEFAULT NULL,
  `FSaveName` varchar(255) NOT NULL,
  `Uri` text NOT NULL,
  `FOriginalName` varchar(255) NOT NULL,
  `FSize` int NOT NULL,
  `UserId` int NOT NULL,
  `UploadTime` datetime NOT NULL,
  PRIMARY KEY (`FId`)
);

CREATE TABLE `wo_installclerk` (
  `ClerkId` int NOT NULL AUTO_INCREMENT,
  `CompanyId` int NOT NULL,
  `ClerkName` varchar(255) NOT NULL,
  PRIMARY KEY (`ClerkId`)
);

CREATE TABLE `wo_installcompany` (
  `CompanyId` int NOT NULL AUTO_INCREMENT,
  `CompanyName` varchar(255) NOT NULL,
  PRIMARY KEY (`CompanyId`)
);

CREATE TABLE `wo_sysconfig` (
  `SysConfigId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `ConfigId` int DEFAULT NULL,
  `ConfigName` varchar(255) DEFAULT NULL,
  `ValueInt` int DEFAULT NULL,
  `ValueString` varchar(255) DEFAULT NULL
);

CREATE TABLE `wo_testorder` (
  `OrderId` int NOT NULL AUTO_INCREMENT,
  `OrderState` int NOT NULL DEFAULT '1',
  `NeedUpload` int NOT NULL DEFAULT '0',
  `MyOrderId` varchar(255) NOT NULL,
  `OrderType` int NOT NULL,
  `StationId` int NOT NULL,
  `Latitude` decimal(22,17) NOT NULL,
  `Longitude` decimal(22,17) NOT NULL,
  `EquipItems` text,
  `InstallCompanyId` int DEFAULT NULL,
  `InstallClerkId` int DEFAULT NULL,
  `InstallCompany` varchar(255) DEFAULT NULL,
  `InstallClerk` varchar(255) DEFAULT NULL,
  `ApplyUserId` int NOT NULL,
  `ApplyUserName` varchar(255) NOT NULL,
  `ApplyUserFsuVendor` varchar(255) NOT NULL DEFAULT '',
  `ApplyTime` datetime NOT NULL,
  `StateChangeTime` datetime NOT NULL,
  `StateSetUserId` int NOT NULL,
  `StateSetUserName` varchar(255) NOT NULL,
  `SubmitTime` datetime DEFAULT NULL,
  `ExpertUserId` int DEFAULT '0',
  `ExpertDecision` varchar(255) DEFAULT '',
  `ExpertNote` varchar(255) DEFAULT '',
  `ExpertIsApprove` int DEFAULT '0',
  `FinalUserId` int DEFAULT '0',
  `FinalGeneralReuslt` varchar(255) DEFAULT '',
  `FinalDecision` varchar(255) DEFAULT '',
  `FinalNote` varchar(255) DEFAULT '',
  `FinalIsApprove` int DEFAULT '0',
  `ApproveTime` datetime DEFAULT NULL,
  `LockUserId` int DEFAULT NULL,
  `LockTime` datetime DEFAULT NULL,
  PRIMARY KEY (`OrderId`),
  KEY `idx_WO_TestOrder` (`OrderId`,`OrderState`)
);

CREATE TABLE `wo_testorderartselfchecklist` (
  `OrderCheckId` int NOT NULL AUTO_INCREMENT,
  `OrderId` int NOT NULL,
  `CheckDicId` int NOT NULL,
  `CheckDicNote` text NOT NULL,
  `IsPass` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`OrderCheckId`),
  KEY `idx_WO_TestOrderArtSelfCheckList` (`OrderId`)
);

CREATE TABLE `wo_testorderequipitem` (
  `OrderEquipId` int NOT NULL AUTO_INCREMENT,
  `OrderId` int DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `BaseEquipmentID` int DEFAULT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `UriProtocol` text,
  `UriImage` text,
  `SaveTime` datetime DEFAULT NULL,
  PRIMARY KEY (`OrderEquipId`),
  UNIQUE KEY `idx_WO_TestOrderEquipItemon` (`OrderId`,`EquipmentId`)
);

CREATE TABLE `wo_testorderequipitemchecklist` (
  `OrderCheckId` int NOT NULL AUTO_INCREMENT,
  `OrderId` int NOT NULL,
  `CheckTypeId` tinyint unsigned DEFAULT NULL,
  `CheckType` varchar(10) DEFAULT NULL,
  `HasConfig` bit(1) DEFAULT b'1',
  `IsPass` int NOT NULL DEFAULT '0',
  `PassNote` varchar(255) DEFAULT NULL,
  `PassFailReason` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `Unit` varchar(255) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitUp` double DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `CheckId` int DEFAULT NULL,
  `EquipmentLogicClass` varchar(128) DEFAULT NULL,
  `LogicClass` varchar(128) DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `SignalType` varchar(255) DEFAULT NULL,
  `SamplerValue` varchar(128) DEFAULT NULL,
  `SamplerTime` datetime DEFAULT NULL,
  PRIMARY KEY (`OrderCheckId`),
  KEY `idx_WO_TestOrderEquipItemCheckList` (`OrderId`,`CheckTypeId`,`IsPass`)
);

CREATE TABLE `wo_testorderequipitemchecklisthis` (
  `OrderCheckId` int NOT NULL,
  `OrderId` int NOT NULL,
  `CheckTypeId` tinyint unsigned NOT NULL,
  `CheckType` varchar(10) DEFAULT NULL,
  `HasConfig` bit(1) DEFAULT b'1',
  `IsPass` int NOT NULL DEFAULT '0',
  `PassNote` varchar(255) DEFAULT NULL,
  `PassFailReason` varchar(255) DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `EquipmentName` varchar(128) DEFAULT NULL,
  `EquipmentCategoryName` varchar(128) DEFAULT NULL,
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `Unit` varchar(255) DEFAULT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitUp` double DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `CheckId` int DEFAULT NULL,
  `EquipmentLogicClass` varchar(128) DEFAULT NULL,
  `LogicClass` varchar(128) DEFAULT NULL,
  `StandardName` varchar(255) DEFAULT NULL,
  `SignalType` varchar(255) DEFAULT NULL,
  `SamplerValue` varchar(128) DEFAULT NULL,
  `SamplerTime` datetime DEFAULT NULL,
  PRIMARY KEY (`OrderCheckId`),
  KEY `idx_WO_TestOrderEquipItemCheckListHis` (`OrderId`,`CheckTypeId`,`IsPass`)
);

CREATE TABLE `wo_testorderequipitemchecklistsignal` (
  `TestOrderEquipitemCheckListSignalId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `OrderCheckId` int DEFAULT NULL,
  `OrderId` int DEFAULT NULL,
  `EquipmentId` int NOT NULL,
  `SignalId` int NOT NULL,
  `LimitDown` double DEFAULT NULL,
  `LimitUp` double DEFAULT NULL,
  `FloatValue` double DEFAULT NULL,
  `SamplerTime` datetime DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `IsPass` bit(1) DEFAULT b'0',
  KEY `idx_WO_TestOrderEquipItemCheckListSignal` (`OrderId`)
);

CREATE TABLE `wo_testorderexpertchecklist` (
  `OrderCheckId` int NOT NULL AUTO_INCREMENT,
  `OrderId` int NOT NULL,
  `CheckDicId` int NOT NULL,
  `CheckDicNote` varchar(255) NOT NULL,
  `IsPass` int NOT NULL DEFAULT '1',
  `PassNote` varchar(255) DEFAULT '',
  PRIMARY KEY (`OrderCheckId`),
  KEY `idx_WO_TestOrderExpertCheckList` (`OrderId`)
);

CREATE TABLE `wo_testorderflow` (
  `OrderFlowId` int NOT NULL AUTO_INCREMENT,
  `OrderId` int NOT NULL,
  `StateSetUserName` varchar(255) NOT NULL,
  `OldOrderState` int NOT NULL,
  `IsApprove` int NOT NULL,
  `SaveTime` datetime NOT NULL,
  `NewOrderState` int NOT NULL,
  `StateSetUserId` int NOT NULL,
  `Decision` varchar(255) DEFAULT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `FlowText` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`OrderFlowId`),
  KEY `idx_WO_TestOrderFlow` (`OrderId`)
);