CREATE TABLE `alarmvideolink` (
  `AlarmVideoLinkId` int NOT NULL AUTO_INCREMENT,
  `ConfigName` varchar(255) DEFAULT NULL COMMENT '实例名称',
  `Description` varchar(255) DEFAULT NULL COMMENT '实例描述',
  `UsedStatus` tinyint(1) DEFAULT NULL COMMENT '启用状态',
  `DepartmentId` int DEFAULT NULL COMMENT '部门id',
  `LinkType` varchar(16) DEFAULT NULL COMMENT '联动类型',
  `OperationType` varchar(16) DEFAULT NULL COMMENT '事件状态',
  `SnapshotCount` int DEFAULT NULL COMMENT '抓图张数',
  `SnapshotInterval` int DEFAULT NULL COMMENT '抓图间隔（秒）',
  PRIMARY KEY (`AlarmVideoLinkId`),
  UNIQUE KEY `AlarmVideoLinkId` (`AlarmVideoLinkId`)
);

CREATE TABLE `alarmvideolinkevent` (
  `AlarmVideoLinkEventId` int NOT NULL AUTO_INCREMENT,
  `AlarmVideoLinkMapId` int DEFAULT NULL COMMENT '告警视频联动映射关系id',
  `EventId` int DEFAULT NULL COMMENT '事件id',
  `EventConditionId` int DEFAULT NULL COMMENT '事件条件id',
  PRIMARY KEY (`AlarmVideoLinkEventId`),
  UNIQUE KEY `AlarmVideoLinkEventId` (`AlarmVideoLinkEventId`)
);

CREATE TABLE `alarmvideolinkmap` (
  `AlarmVideoLinkMapId` int NOT NULL AUTO_INCREMENT,
  `AlarmVideoLinkId` int DEFAULT NULL COMMENT '告警视频联动id',
  `EquipmentId` int DEFAULT NULL COMMENT '设备id',
  `EventId` int DEFAULT NULL COMMENT '告警id',
  `EventConditionId` int DEFAULT NULL COMMENT '告警条件id',
  `CameraId` bigint DEFAULT NULL COMMENT '摄像头id',
  `CameraIds` varchar(255) DEFAULT NULL COMMENT '摄像头ids，多个逗号隔开',
  PRIMARY KEY (`AlarmVideoLinkMapId`),
  UNIQUE KEY `AlarmVideoLinkMapId` (`AlarmVideoLinkMapId`),
  KEY `IDX_EquipmentId_EventId_EventConditionId` (`EquipmentId`,`EventId`,`EventConditionId`)
);

CREATE TABLE `alarmchangesnapshot` (
  `SequenceId` varchar(128) NOT NULL COMMENT '主键id',
  `OperationType` int NOT NULL COMMENT '告警状态',
  `SnapshotUrl` varchar(128) NOT NULL COMMENT '抓图保存地址',
  `CameraId` int NOT NULL COMMENT '摄像头id',
  `CameraName` varchar(128) DEFAULT NULL COMMENT '摄像头名',
  `StartTime` datetime NOT NULL COMMENT '告警开始时间',
  PRIMARY KEY (`SequenceId`)
);