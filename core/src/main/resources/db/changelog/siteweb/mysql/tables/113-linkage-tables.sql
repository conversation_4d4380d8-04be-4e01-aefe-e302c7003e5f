CREATE TABLE `linkconfig` (
  `LinkConfigId` int NOT NULL AUTO_INCREMENT,
  `ConfigName` varchar(128) NOT NULL,
  `UsedStatus` tinyint(1) NOT NULL,
  `LinkGroupId` int NOT NULL,
  `LinkTriggerType` int NOT NULL,
  `<PERSON>ron` varchar(100) DEFAULT NULL COMMENT 'corn表达式用于定时执行后台联动',
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Layout` mediumtext,
  `Description` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`LinkConfigId`),
  UNIQUE KEY `LinkConfigId` (`LinkConfigId`)
);

CREATE TABLE `linkconfigtemplate` (
  `LinkConfigTemplateId` int NOT NULL AUTO_INCREMENT,
  `TemplateName` varchar(128) NOT NULL,
  `Content` mediumtext NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LinkConfigTemplateId`),
  UNIQUE KEY `LinkConfigTemplateId` (`LinkConfigTemplateId`)
);

CREATE TABLE `linkelement` (
  `ElementId` int NOT NULL AUTO_INCREMENT,
  `ElementName` varchar(128) NOT NULL,
  `ElementType` varchar(64) NOT NULL,
  `InputNodesCount` int NOT NULL,
  `OutputNodesCount` int NOT NULL,
  `Icon` varchar(64) DEFAULT NULL,
  `Expression` varchar(500) DEFAULT NULL,
  `Visible` tinyint(1) NOT NULL,
  `SortIndex` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ElementId`),
  UNIQUE KEY `ElementId` (`ElementId`)
);

CREATE TABLE `linkelementconfig` (
  `LinkElementConfigId` int NOT NULL AUTO_INCREMENT,
  `LinkConfigId` int NOT NULL,
  `ElementId` int NOT NULL,
  `Expression` varchar(1000) DEFAULT NULL,
  `ExtendField1` varchar(500) DEFAULT NULL,
  `ExtendField2` varchar(500) DEFAULT NULL,
  `ExtendField3` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`LinkElementConfigId`),
  UNIQUE KEY `LinkElementConfigId` (`LinkElementConfigId`)
);

CREATE TABLE `linkgroup` (
  `GroupId` int NOT NULL AUTO_INCREMENT,
  `GroupName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GroupId`),
  UNIQUE KEY `GroupId` (`GroupId`)
);

CREATE TABLE `linkinstance` (
  `LinkInstanceId` int NOT NULL AUTO_INCREMENT,
  `LinkConfigId` int NOT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Status` int NOT NULL,
  `StatusResult` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LinkInstanceId`),
  UNIQUE KEY `LinkInstanceId` (`LinkInstanceId`)
);

CREATE TABLE `linknode` (
  `NodeId` int NOT NULL AUTO_INCREMENT,
  `LinkElementConfigId` int NOT NULL,
  `NodeDirection` varchar(10) NOT NULL,
  `NodeType` varchar(64) NOT NULL,
  `NodeIndex` int NOT NULL,
  `NodeTag` varchar(255) DEFAULT NULL,
  `Expression` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`NodeId`),
  UNIQUE KEY `NodeId` (`NodeId`)
);

CREATE TABLE `linksegment` (
  `SegmentId` int NOT NULL AUTO_INCREMENT,
  `InputLinkElementConfigId` int NOT NULL,
  `InputNodeId` int NOT NULL,
  `OutputLinkElementConfigId` int NOT NULL,
  `OutputNodeId` int NOT NULL,
  PRIMARY KEY (`SegmentId`),
  UNIQUE KEY `SegmentId` (`SegmentId`)
);