CREATE TABLE internalmessagetype
(
    `InternalMessageTypeId` int         NOT NULL AUTO_INCREMENT,
    `TypeName`              varchar(32) NOT NULL COMMENT '消息类型',
    `EnableTts`             int         NOT NULL COMMENT '是否启用TTS播报',
    PRIMARY KEY (`InternalMessageTypeId`)
);
CREATE TABLE `internalmessage`
(
    `InternalMessageId` int          NOT NULL AUTO_INCREMENT,
    `body`              varchar(512) NOT NULL COMMENT '消息内容',
    `messageType`       int          NOT NULL COMMENT '消息类型',
    `createTime`        datetime     NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`InternalMessageId`)
);
CREATE TABLE `messagestatus`
(
    `MessageStatusId`   int NOT NULL AUTO_INCREMENT,
    `InternalMessageId` int DEFAULT NULL COMMENT '消息id',
    `userId`            int NOT NULL COMMENT '用户接收ID',
    `messageStatus`     int DEFAULT NULL COMMENT '消息状态 0未读 1已读',
    PRIMARY KEY (`MessageStatusId`),
    KEY `idx_user_id` (`userId`),
    KEY `idx_internal_message_id` (`InternalMessageId`)
);