CREATE TABLE `assetcategory` (
  `AssetCategoryId` int NOT NULL AUTO_INCREMENT,
  `AssetCategoryName` varchar(128) DEFAULT NULL COMMENT '资产类型名称',
  `Remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`AssetCategoryId`),
  UNIQUE KEY `AssetCategoryId` (`AssetCategoryId`)
);

CREATE TABLE `assetdevice` (
  `AssetDeviceId` int NOT NULL AUTO_INCREMENT,
  `SortIndex` int DEFAULT NULL COMMENT '排序',
  `AssetCode` varchar(128) DEFAULT NULL COMMENT '资产编号',
  `AssetName` varchar(128) DEFAULT NULL COMMENT '资产名称',
  `AssetCategoryId` int DEFAULT NULL COMMENT '资产类型',
  `Brand` varchar(128) DEFAULT NULL COMMENT '品牌',
  `Model` varchar(128) DEFAULT NULL COMMENT '型号',
  `CapacityParameter` varchar(128) DEFAULT NULL COMMENT '容量参数',
  `SettingPosition` varchar(128) DEFAULT NULL COMMENT '安装位置',
  `SerialNumber` varchar(128) DEFAULT NULL COMMENT '序列号',
  `Manufactor` varchar(128) DEFAULT NULL COMMENT '厂家',
  `TableName` varchar(128) DEFAULT NULL COMMENT '关联表',
  PRIMARY KEY (`AssetDeviceId`),
  UNIQUE KEY `AssetDeviceId` (`AssetDeviceId`)
);

CREATE TABLE `extfieldconfiguration` (
  `ExtId` int NOT NULL AUTO_INCREMENT,
  `ExtTable` varchar(50) DEFAULT NULL COMMENT '扩展关联表',
  `ExtCode` varchar(50) DEFAULT NULL COMMENT '扩展编码',
  `ExtName` varchar(100) DEFAULT NULL COMMENT '扩展名称',
  `ExtDesc` varchar(200) DEFAULT NULL COMMENT '扩展描述',
  `ExtOrder` int DEFAULT NULL COMMENT '扩展序号',
  `ExtNecessary` tinyint DEFAULT NULL COMMENT '扩展字段是否必须',
  `ExtDataType` varchar(50) DEFAULT NULL COMMENT '扩展字段数据类型',
  `ExtDataSource` varchar(300) DEFAULT NULL COMMENT '扩展字段数据来源',
  PRIMARY KEY (`ExtId`),
  UNIQUE KEY `ExtId` (`ExtId`)
);

CREATE TABLE `extvalueconfiguration` (
  `ExtValId` int NOT NULL AUTO_INCREMENT,
  `ExtId` int DEFAULT NULL COMMENT '扩展字段id',
  `ExtTable` varchar(50) DEFAULT NULL COMMENT '扩展字段值关联表',
  `ExtTablePkId` int DEFAULT NULL COMMENT '扩展字段关联表主键id',
  `ExtValue` varchar(1000) DEFAULT NULL COMMENT '扩展字段值',
  PRIMARY KEY (`ExtValId`),
  UNIQUE KEY `ExtValId` (`ExtValId`)
);