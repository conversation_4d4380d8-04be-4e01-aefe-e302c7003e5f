CREATE TABLE `prealarm` (
  `PreAlarmId` int NOT NULL AUTO_INCREMENT,
  `PreAlarmPointId` int NOT NULL,
  `Meanings` varchar(128) DEFAULT NULL,
  `PreAlarmSeverity` int DEFAULT NULL,
  `PreAlarmSeverityName` varchar(128) DEFAULT NULL,
  `PreAlarmCategory` int DEFAULT NULL,
  `PreAlarmCategoryName` varchar(128) DEFAULT NULL,
  `Color` varchar(128) DEFAULT NULL,
  `UniqueId` varchar(256) DEFAULT NULL,
  `UniqueName` varchar(256) DEFAULT NULL,
  `ObjectId` int DEFAULT NULL,
  `ObjectTypeId` int DEFAULT NULL,
  `ObjectName` varchar(128) DEFAULT NULL,
  `ResourceStructureId` int DEFAULT NULL,
  `LevelOfPath` varchar(256) DEFAULT NULL,
  `LevelOfPathName` varchar(256) DEFAULT NULL,
  `TriggerValue` varchar(128) DEFAULT NULL,
  `Unit` varchar(10) DEFAULT NULL,
  `SampleTime` datetime DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmId` int DEFAULT NULL,
  `ConfirmName` varchar(128) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `BusinessTypeId` int DEFAULT NULL,
  PRIMARY KEY (`PreAlarmId`),
  UNIQUE KEY `PreAlarmId` (`PreAlarmId`)
);

CREATE TABLE `prealarmcategory` (
  `CategoryId` int NOT NULL AUTO_INCREMENT,
  `CategoryName` varchar(64) NOT NULL,
  `ParentCategoryId` int DEFAULT NULL,
  `Description` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`CategoryId`),
  UNIQUE KEY `CategoryId` (`CategoryId`)
);

CREATE TABLE `prealarmhistory` (
  `PreAlarmHistoryId` int PRIMARY KEY AUTO_INCREMENT,
  `PreAlarmId` int NOT NULL,
  `PreAlarmPointId` int NOT NULL,
  `Meanings` varchar(128) DEFAULT NULL,
  `PreAlarmSeverity` int DEFAULT NULL,
  `PreAlarmSeverityName` varchar(128) DEFAULT NULL,
  `PreAlarmCategory` int DEFAULT NULL,
  `PreAlarmCategoryName` varchar(128) DEFAULT NULL,
  `Color` varchar(128) DEFAULT NULL,
  `UniqueId` varchar(256) DEFAULT NULL,
  `UniqueName` varchar(256) DEFAULT NULL,
  `ObjectId` int DEFAULT NULL,
  `ObjectTypeId` int DEFAULT NULL,
  `ObjectName` varchar(128) DEFAULT NULL,
  `ResourceStructureId` int DEFAULT NULL,
  `LevelOfPath` varchar(256) DEFAULT NULL,
  `LevelOfPathName` varchar(256) DEFAULT NULL,
  `TriggerValue` varchar(128) DEFAULT NULL,
  `Unit` varchar(10) DEFAULT NULL,
  `SampleTime` datetime DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `ConfirmId` int DEFAULT NULL,
  `ConfirmName` varchar(128) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `BusinessTypeId` int DEFAULT NULL
);

CREATE TABLE `prealarmpoint` (
  `PreAlarmPointId` int NOT NULL AUTO_INCREMENT,
  `PreAlarmPointName` varchar(128) DEFAULT NULL,
  `Meanings` varchar(128) DEFAULT NULL,
  `Expression` varchar(256) DEFAULT NULL,
  `AbnormalExpression` varchar(256) DEFAULT NULL,
  `ExecuteCron` varchar(256) DEFAULT NULL,
  `PreAlarmCategory` int DEFAULT NULL,
  `UniqueId` varchar(256) DEFAULT NULL,
  `ObjectId` int DEFAULT NULL,
  `ObjectTypeId` int DEFAULT NULL,
  `ObjectName` varchar(128) DEFAULT NULL,
  `ResourceStructureId` int DEFAULT NULL,
  `LevelOfPath` varchar(256) DEFAULT NULL,
  `LevelOfPathName` varchar(256) DEFAULT NULL,
  `PreAlarmSeverity` int DEFAULT NULL,
  `Enable` int DEFAULT NULL,
  `Unit` varchar(10) DEFAULT NULL,
  `MaskType` int DEFAULT NULL,
  `MaskDuration` varchar(128) DEFAULT NULL,
  `MaskStartTime` datetime DEFAULT NULL,
  `MaskEndTime` datetime DEFAULT NULL,
  `Stateful` int DEFAULT NULL,
  `Modifier` int DEFAULT NULL,
  `ModifierName` varchar(128) DEFAULT NULL,
  `ModifyTime` datetime DEFAULT NULL,
  PRIMARY KEY (`PreAlarmPointId`),
  UNIQUE KEY `PreAlarmPointId` (`PreAlarmPointId`)
);

CREATE TABLE `prealarmseverity` (
  `PreAlarmSeverityId` int NOT NULL AUTO_INCREMENT,
  `PreAlarmSeverityName` varchar(64) NOT NULL,
  `Color` varchar(128) DEFAULT NULL,
  `Description` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`PreAlarmSeverityId`),
  UNIQUE KEY `PreAlarmSeverityId` (`PreAlarmSeverityId`)
);

CREATE TABLE `prealarmchange` (
  `sequenceId` varchar(128) NOT NULL,
  `serialNo` bigint unsigned NOT NULL AUTO_INCREMENT,
  `operationType` int NOT NULL,
  `preAlarmId` bigint NOT NULL,
  `preAlarmName` varchar(128) NOT NULL,
  `meanings` varchar(255) NOT NULL,
  `triggerValue` varchar(255) NOT NULL,
  `preAlarmPointId` bigint NOT NULL,
  `preAlarmCategory` int NOT NULL,
  `preAlarmCategoryName` varchar(128) DEFAULT NULL,
  `preAlarmSeverity` int NOT NULL,
  `preAlarmSeverityName` varchar(128) DEFAULT NULL,
  `startTime` datetime NOT NULL,
  `endTime` datetime DEFAULT NULL,
  `confirmTime` datetime DEFAULT NULL,
  `confirmorId` int DEFAULT NULL,
  `confirmorName` varchar(128) DEFAULT NULL,
  `equipmentId` int NOT NULL,
  `equipmentName` varchar(128) NOT NULL,
  `equipmentCategory` int NOT NULL,
  `equipmentCategoryName` varchar(128) NOT NULL,
  `equipmentVendor` varchar(128) DEFAULT NULL,
  `centerId` int NOT NULL,
  `centerName` varchar(128) DEFAULT NULL,
  `structureId` int NOT NULL,
  `structureName` varchar(128) DEFAULT NULL,
  `stationId` int NOT NULL,
  `StationName` varchar(128) DEFAULT NULL,
  `stationCategoryId` int NOT NULL,
  `resourceStructureId` int NOT NULL,
  `levelOfPathName` varchar(255) DEFAULT NULL,
  `insertTime` datetime NOT NULL,
  PRIMARY KEY (`serialNo`),
  KEY `TBL_AlarmChange_ID1` (`sequenceId`)
) ;