CREATE TABLE `tbl_h5camera` (
  `CameraId` int NOT NULL AUTO_INCREMENT,
  `CameraName` varchar(255) NOT NULL,
  `CameraIp` varchar(128) NOT NULL,
  `CameraPort` int NOT NULL,
  `ChannelNumber` varchar(32) DEFAULT NULL,
  `CameraGroupId` int DEFAULT NULL,
  `UserName` varchar(128) DEFAULT NULL,
  `Password` varchar(32) DEFAULT NULL,
  `VendorId` int DEFAULT NULL,
  `VendorName` varchar(128) DEFAULT NULL,
  `CameraIndexCode` varchar(255) DEFAULT NULL,
  `CameraType` int DEFAULT NULL,
  `CameraTypeName` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CameraId`)
);

CREATE TABLE `tbl_h5cameragroup` (
  `CameraGroupId` int NOT NULL AUTO_INCREMENT,
  `<PERSON>GroupName` varchar(255) DEFAULT NULL,
  `ParentId` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CameraGroupId`)
);

CREATE TABLE `tbl_h5reportcentertypemap` (
  `H5ReportCenterTypeMapId` int PRIMARY KEY AUTO_INCREMENT,
  `ReportId` int NOT NULL,
  `CenterType` int NOT NULL DEFAULT '0'
);

CREATE TABLE `tbl_h5reportcronexpression` (
  `CronId` int NOT NULL AUTO_INCREMENT,
  `CronExpression` varchar(128) NOT NULL DEFAULT '',
  `Meaning` varchar(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`CronId`)
);

CREATE TABLE `tbl_h5reportprocedureparameters` (
  `H5ReportProcedureParameters` int PRIMARY KEY AUTO_INCREMENT,
  `PName` varchar(255) NOT NULL,
  `Parameters` varchar(4000) DEFAULT NULL
);

CREATE TABLE `tbl_h5reporttask` (
  `TaskId` int NOT NULL AUTO_INCREMENT,
  `TaskName` varchar(128) NOT NULL,
  `CronId` int NOT NULL,
  `IsEnable` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ReportId` int NOT NULL,
  `QueryParameters` longtext NOT NULL,
  `Creator` int DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `LastUpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`TaskId`)
);