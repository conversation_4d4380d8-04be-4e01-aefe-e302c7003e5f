CREATE TABLE `convergenceevent` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `EventName` varchar(255) NOT NULL,
  `ConvergenceType` int NOT NULL,
  `ConvergenceRuleId` int NOT NULL,
  `BirthTime` datetime NOT NULL,
  `ConvergenceCount` int NOT NULL,
  `Status` int DEFAULT NULL,
  `ConfirmTime` datetime DEFAULT NULL,
  `PossibleCauses` varchar(1024) DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EventId` int DEFAULT NULL,
  `EventConditionId` int DEFAULT NULL,
  `ClearTime` datetime DEFAULT NULL,
  `ConfirmerId` int DEFAULT NULL,
  `ConfirmerName` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `eventconvergencerule` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `RuleName` varchar(255) NOT NULL,
  `ConvergenceType` int NOT NULL,
  `EquipmentCategory` int NOT NULL,
  `StartExpression` varchar(1024) DEFAULT NULL,
  `FilterCondition` varchar(2048) DEFAULT NULL,
  `ConvergenceInterval` int DEFAULT NULL,
  `StartCount` int DEFAULT NULL,
  `EndCount` int DEFAULT NULL,
  `PossibleCauses` varchar(1024) DEFAULT NULL,
  `ParentRuleId` int DEFAULT NULL,
  `LevelOfPath` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `powerequipmentconnection` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `ParentEquipmentId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `LevelOFPath` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);