CREATE TABLE `chartapi` (
  `ApiId` int NOT NULL AUTO_INCREMENT,
  `ApiName` varchar(128) NOT NULL,
  `Category` varchar(45) NOT NULL,
  `Url` varchar(1024) NOT NULL,
  `Method` varchar(128) NOT NULL,
  `ParamSchema` json NOT NULL,
  `Transform` varchar(4096) NOT NULL,
  PRIMARY KEY (`ApiId`),
  UNIQUE KEY `ApiId` (`ApiId`)
);

CREATE TABLE `chartstyle` (
  `StyleId` int NOT NULL AUTO_INCREMENT,
  `StyleName` varchar(45) NOT NULL,
  `ChartId` int NOT NULL,
  `Thumbnail` text,
  `Expression` text NOT NULL,
  PRIMARY KEY (`StyleId`),
  UNIQUE KEY `StyleId` (`StyleId`),
  UNIQUE KEY `ChartStyle_StyleName` (`StyleName`)
);

CREATE TABLE `charttheme` (
  `ThemeId` int NOT NULL AUTO_INCREMENT,
  `themeName` varchar(45) NOT NULL,
  `themeCode` varchar(45) NOT NULL,
  `themeData` json NOT NULL,
  `themeDefault` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`ThemeId`),
  UNIQUE KEY `themeId` (`ThemeId`),
  UNIQUE KEY `ChartTheme_ThemeName` (`themeCode`)
);

CREATE TABLE `commonobject` (
  `CommonObjectId` int NOT NULL AUTO_INCREMENT,
  `CommonObjectName` varchar(255) DEFAULT NULL COMMENT '通用对象名称',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  `Photo` varchar(255) DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`CommonObjectId`),
  UNIQUE KEY `CommonObjectId` (`CommonObjectId`)
) AUTO_INCREMENT 10000;

CREATE TABLE `compdatacolumn` (
  `CompDataColumnId` int NOT NULL AUTO_INCREMENT,
  `CompDataTableId` int DEFAULT NULL,
  `ColumnName` varchar(50) DEFAULT NULL,
  `ColumnField` varchar(50) DEFAULT NULL,
  `ValueType` tinyint DEFAULT NULL,
  PRIMARY KEY (`CompDataColumnId`),
  UNIQUE KEY `CompDataColumnId` (`CompDataColumnId`)
);

CREATE TABLE `compdataset` (
  `CompDataSetId` int NOT NULL AUTO_INCREMENT,
  `CompDataSetName` varchar(50) DEFAULT NULL,
  `Url` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`CompDataSetId`),
  UNIQUE KEY `CompDataSetId` (`CompDataSetId`)
);

CREATE TABLE `compdatatable` (
  `CompDataTableId` int NOT NULL AUTO_INCREMENT,
  `CompDataTableName` varchar(50) DEFAULT NULL,
  `CompDataSetId` int DEFAULT NULL,
  PRIMARY KEY (`CompDataTableId`),
  UNIQUE KEY `CompDataTableId` (`CompDataTableId`)
);

CREATE TABLE `compskin` (
  `CompSkinId` int NOT NULL AUTO_INCREMENT,
  `CompDataSetId` int DEFAULT NULL,
  `SkinName` varchar(50) DEFAULT NULL,
  `Content` text,
  PRIMARY KEY (`CompSkinId`),
  UNIQUE KEY `CompSkinId` (`CompSkinId`)
);

CREATE TABLE `graphicpage` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Type` varchar(255) DEFAULT NULL,
  `GroupId` int DEFAULT NULL,
  `PageCategory` int DEFAULT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `ObjectId` int DEFAULT NULL,
  `Name` varchar(255) NOT NULL,
  `AppendName` varchar(255) DEFAULT NULL,
  `TemplateId` int DEFAULT NULL,
  `Data` text,
  `Style` text,
  `Children` longtext,
  `Description` varchar(255) DEFAULT NULL,
  `RouterType` varchar(255) DEFAULT NULL,
  `CrumbsRouterType` varchar(255) DEFAULT NULL,
  `PageCanFullScreen` tinyint(1) DEFAULT NULL,
  `SceneId` int DEFAULT NULL,
  `IsDefault` tinyint(1) DEFAULT NULL COMMENT '是否默认组态页',
  `UpdateTime` datetime DEFAULT NULL COMMENT '最新修改时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_GraphicPage_1` (`PageCategory`,`ObjectId`)
);

CREATE TABLE `graphicpagetemplate` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Type` varchar(255) DEFAULT NULL,
  `GroupId` int DEFAULT NULL,
  `PageCategory` int DEFAULT NULL,
  `BaseEquipmentId` int DEFAULT NULL,
  `Internal` tinyint DEFAULT NULL,
  `Name` varchar(255) NOT NULL,
  `AppendName` varchar(255) DEFAULT NULL,
  `Data` text,
  `Style` text,
  `Children` longtext,
  `TemplateCategory` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`),
  KEY `IDX_GraphicPageTemplate_1` (`PageCategory`,`BaseEquipmentId`)
);

CREATE TABLE `graphictemplate` (
  `GraphicTemplateId` int NOT NULL AUTO_INCREMENT,
  `GraphicTemplateName` varchar(128) DEFAULT NULL COMMENT '模板名称',
  `GraphicTemplateTag` varchar(128) DEFAULT NULL COMMENT '模板标签',
  `GraphicTemplateCompType` int DEFAULT NULL COMMENT '组件类型',
  `GraphicTemplateTypeId` int DEFAULT NULL COMMENT '组件id',
  `GraphicTemplateType` varchar(128) DEFAULT NULL COMMENT '组件英文名称',
  `GraphicTemplateTypeName` varchar(128) DEFAULT NULL COMMENT '组件中文名称',
  `GraphicTemplateCover` varchar(128) DEFAULT NULL COMMENT '模板缩略图',
  `GraphicTemplateConfig` text COMMENT '模板配置',
  `CreateTime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`GraphicTemplateId`),
  UNIQUE KEY `GraphicTemplateId` (`GraphicTemplateId`)
);

CREATE TABLE `matrixchart` (
  `MatrixChartId` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) DEFAULT NULL,
  `Path` varchar(1024) DEFAULT NULL,
  `DataType` int DEFAULT NULL,
  PRIMARY KEY (`MatrixChartId`),
  UNIQUE KEY `MatrixChartId` (`MatrixChartId`)
);

CREATE TABLE `systemnavigation` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) NOT NULL,
  `BusinessId` varchar(50) DEFAULT NULL,
  `PageId` int DEFAULT NULL,
  `SortValue` int DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `usergraphicpagemap` (
  `UserGraphicPageMapId` int NOT NULL AUTO_INCREMENT,
  `UserId` int DEFAULT NULL COMMENT '用户id',
  `GraphicPageId` int DEFAULT NULL COMMENT '组态id',
  `Config` text COMMENT '配置',
  PRIMARY KEY (`UserGraphicPageMapId`),
  UNIQUE KEY `UserGraphicPageMapId` (`UserGraphicPageMapId`)
);