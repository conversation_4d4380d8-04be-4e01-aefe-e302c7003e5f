CREATE TABLE `ttsconfig`
(
    `TtsConfigId`    int NOT NULL AUTO_INCREMENT,
    `TtsConfigKey`   varchar(255)   DEFAULT NULL COMMENT '键名',
    `TtsConfigValue` varchar(10000) DEFAULT NULL COMMENT '对应键值',
    `Description`    varchar(255)   DEFAULT NULL COMMENT '描述信息',
    PRIMARY KEY (`TtsConfigId`),
    UNIQUE KEY `TtsConfigId` (`TtsConfigId`),
    UNIQUE KEY `TtsConfigKey` (`TtsConfigKey`)
);
CREATE TABLE `ttsfilterconfig`
(
    `TtsFilterConfigId` int NOT NULL AUTO_INCREMENT COMMENT '主键自增id',
    `TtsStrategyId`     int            DEFAULT NULL COMMENT '所属策略的主键id',
    `TtsConfigKey`      varchar(255)   DEFAULT NULL COMMENT '键名',
    `TtsConfigValue`    varchar(10000) DEFAULT NULL COMMENT '键值',
    PRIMARY KEY (`TtsFilterConfigId`)
) COMMENT ='Tts过滤条件配置表';


CREATE TABLE `ttsstrategy`
(
    `TtsStrategyId`      int NOT NULL AUTO_INCREMENT COMMENT '主键自增id',
    `StrategyName`       varchar(50)  DEFAULT NULL COMMENT '策略名称',
    `Enable`             int          DEFAULT '0' COMMENT '是否启用 0 否 1是',
    `EffectiveStartTime` datetime     DEFAULT NULL COMMENT '生效开始时间',
    `EffectiveEndTime`   datetime     DEFAULT NULL COMMENT '生效结束时间',
    `Description`        varchar(255) DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`TtsStrategyId`)
) COMMENT ='TTS策略表';