CREATE TABLE `tbl_sparkindexbaseequipment` (
  `GroupId` int NOT NULL,
  `BaseEquipmentId` int NOT NULL,
  `GrapicPageId` int DEFAULT NULL,
  `DisplayIndex` int DEFAULT NULL,
  `DisplayFormat` text,
  `ExtendFiled1` text,
  `ExtendFiled2` text,
  `DisplayType` int DEFAULT NULL,
    PRIMARY KEY (`GroupId`,`BaseEquipmentId`)
);

CREATE TABLE `tbl_sparkindexbasesignal` (
  `GroupId` int NOT NULL,
  `BaseEquipmentId` int NOT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `DisplayIndex` int DEFAULT NULL,
  `ExtendFiled1` text,
  `ExtendFiled2` text,
  PRIMARY KEY (`GroupId`,`BaseEquipmentId`,`BaseTypeId`)
);

CREATE TABLE `tbl_sparkindexgroup` (
  `GroupId` int NOT NULL,
  `GroupTitle` varchar(255) NOT NULL,
  `GrapicPageId` int DEFAULT NULL,
  `ExtendFiled1` text,
  `ExtendFiled2` text,
  `ColumnCount` int DEFAULT NULL,
    PRIMARY KEY (`GroupId`)
);