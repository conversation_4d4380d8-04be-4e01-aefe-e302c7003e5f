CREATE TABLE `complexindex` (
  `ComplexIndexId` int NOT NULL AUTO_INCREMENT,
  `ComplexIndexName` varchar(128) DEFAULT NULL,
  `ComplexIndexDefinitionId` int DEFAULT NULL,
  `ObjectId` int DEFAULT NULL,
  `CalcC<PERSON>` varchar(128) DEFAULT NULL,
  `CalcType` int DEFAULT NULL,
  `AfterCalc` text,
  `SaveCron` varchar(128) DEFAULT NULL,
  `Expression` text,
  `Unit` varchar(128) DEFAULT NULL,
  `Accuracy` varchar(128) DEFAULT NULL,
  `ObjectTypeId` int DEFAULT NULL,
  `Remark` varchar(128) DEFAULT NULL,
  `Label` varchar(128) DEFAULT NULL,
  `BusinessTypeId` int DEFAULT NULL,
  `CheckExpression` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`ComplexIndexId`),
  UNIQUE KEY `ComplexIndexId` (`ComplexIndexId`)
);

CREATE TABLE `complexindexbusinessobjectmap` (
  `BusinessObjectMapId` int NOT NULL AUTO_INCREMENT,
  `SceneId` int NOT NULL,
  `BusinessTypeId` int NOT NULL,
  `ObjectTypeId` int NOT NULL,
  `ComplexIndexDefinitionId` int NOT NULL,
  PRIMARY KEY (`BusinessObjectMapId`),
  UNIQUE KEY `BusinessObjectMapId` (`BusinessObjectMapId`)
);

CREATE TABLE `complexindexbusinesstype` (
  `BusinessTypeId` int NOT NULL AUTO_INCREMENT,
  `BusinessTypeName` varchar(50) NOT NULL,
  `ParentId` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`BusinessTypeId`),
  UNIQUE KEY `BusinessTypeId` (`BusinessTypeId`)
);

CREATE TABLE `complexindexdefinition` (
  `ComplexIndexDefinitionId` int NOT NULL AUTO_INCREMENT,
  `ComplexIndexDefinitionName` varchar(50) NOT NULL,
  `CalcCron` varchar(128) DEFAULT NULL,
  `CalcType` int DEFAULT NULL,
  `AfterCalc` varchar(128) DEFAULT NULL,
  `SaveCron` varchar(128) DEFAULT NULL,
  `Expression` varchar(128) DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `Accuracy` varchar(128) DEFAULT NULL,
  `StartStatus` int DEFAULT NULL,
  `Icon` varchar(128) DEFAULT NULL,
  `CheckExpression` varchar(128) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ComplexIndexDefinitionId`),
  UNIQUE KEY `ComplexIndexDefinitionId` (`ComplexIndexDefinitionId`)
);

CREATE TABLE `complexindexfunction` (
  `FunctionId` int NOT NULL AUTO_INCREMENT,
  `FunctionExpression` varchar(128) DEFAULT NULL,
  `FunctionDescription` varchar(128) DEFAULT NULL,
  `FunctionName` varchar(128) DEFAULT NULL,
  `Remark` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`FunctionId`),
  UNIQUE KEY `FunctionId` (`FunctionId`)
);
