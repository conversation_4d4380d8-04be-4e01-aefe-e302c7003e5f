CREATE TABLE `libatterypack` (
  `BatteryPackId` int NOT NULL AUTO_INCREMENT,
  `BatteryPackNumber` int DEFAULT NULL COMMENT '电池单体编号',
  `BatteryNumber` int DEFAULT NULL COMMENT '所属电池编号',
  `BatteryGroupNumber` int DEFAULT NULL COMMENT '所属电池组编号',
  `BatteryModuleNumber` int DEFAULT NULL COMMENT '所属电池模块编号',
  `BatteryPackName` varchar(128) DEFAULT NULL COMMENT '电池单体名称',
  `EquipmentId` int DEFAULT NULL COMMENT '电池组设备Id',
  `UPSEquipmentId` int DEFAULT NULL COMMENT 'UPS设备Id',
  `MeanVoltageComplexIndexId` int DEFAULT NULL COMMENT '电池组平均电压指标Id',
  `MeanTemperatureComplexIndexId` int DEFAULT NULL COMMENT '电池组平均温度指标Id',
  `VoltageSignalId` int DEFAULT NULL COMMENT '单体电压信号Id',
  `TemperatureSignalId` int DEFAULT NULL COMMENT '单体温度信号Id',
  `TemperatureIncrementRateComplexIndexId` int DEFAULT NULL COMMENT '单体电池温升速率指标Id',
  `TemperatureDeviationComplexIndexId` int DEFAULT NULL COMMENT '单体电池与同组电池平均温度差值指标Id',
  `VoltageDeviationComplexIndexId` int DEFAULT NULL COMMENT '单体电池与同组电池平均电压差值指标Id',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`BatteryPackId`),
  UNIQUE KEY `BatteryPackId` (`BatteryPackId`)
);

CREATE TABLE `thermalrunawayadvice` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AdviceType` varchar(128) DEFAULT NULL COMMENT '建议类别',
  `Reason` varchar(500) DEFAULT NULL COMMENT '原因',
  `Advice` varchar(500) DEFAULT NULL COMMENT '处理建议',
  `Contacts` varchar(128) DEFAULT NULL COMMENT '联系人',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `thermalrunawayaffectdevice` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UPSEquipmentId` int DEFAULT NULL COMMENT 'UPS设备Id',
  `AffectDeviceName` varchar(128) DEFAULT NULL COMMENT '受影响设备名称',
  `AffectDevicePosition` varchar(128) DEFAULT NULL COMMENT '受影响设备位置',
  `AffectDevicePurpose` varchar(255) DEFAULT NULL COMMENT '受影响设备用途',
  `AffectDevicePower` varchar(255) DEFAULT NULL COMMENT '受影响设备功率',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `thermalrunawaycontrolpolicy` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `BatteryEquipmentId` int DEFAULT NULL COMMENT '电池设备Id',
  `PhaseId` int DEFAULT NULL COMMENT '热失控阶段Id',
  `ControlPolicy` varchar(500) DEFAULT NULL COMMENT '控制策略',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `thermalrunawaycontrolrecord` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ThermalRunawayEventId` int DEFAULT NULL COMMENT '热失控事件Id',
  `EquipmentId` int DEFAULT NULL COMMENT '控制设备Id',
  `ControlId` int DEFAULT NULL COMMENT '控制命令Id',
  `ControlValue` varchar(100) DEFAULT NULL COMMENT '控制值',
  `ControlTime` datetime DEFAULT NULL COMMENT '控制时间',
  `ControlResult` varchar(50) DEFAULT NULL COMMENT '控制结果',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `thermalrunawayevent` (
  `ThermalRunawayEventId` int NOT NULL AUTO_INCREMENT,
  `StartTime` datetime DEFAULT NULL COMMENT '开始时间',
  `EndTime` datetime DEFAULT NULL COMMENT '结束时间',
  `BatteryPackId` int DEFAULT NULL COMMENT '电池单体Id',
  `TriggerTemperature` double DEFAULT NULL COMMENT '触发热失控时的单体温度',
  `Prediction` varchar(128) DEFAULT NULL COMMENT '预测结果',
  `PhaseId` int DEFAULT NULL COMMENT '所处阶段ID',
  `Phase` varchar(128) DEFAULT NULL COMMENT '所处阶段',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ThermalRunawayEventId`),
  UNIQUE KEY `ThermalRunawayEventId` (`ThermalRunawayEventId`)
);

CREATE TABLE `thermalrunawayphase` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Phase` varchar(128) DEFAULT NULL COMMENT '阶段',
  `Prediction` varchar(128) DEFAULT NULL COMMENT '预测结果：事前预警或热失控',
  `TriggerTemperature` double DEFAULT NULL COMMENT '触发热失控时的单体温度',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);