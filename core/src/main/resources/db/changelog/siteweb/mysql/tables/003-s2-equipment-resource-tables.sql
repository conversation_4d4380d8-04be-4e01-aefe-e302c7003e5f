CREATE TABLE `tbl_equipmentresource` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `HouseId` int DEFAULT NULL,
  `EquipmentCategory` int NOT NULL,
  `Vendor` varchar(255) DEFAULT NULL,
  `UsedDate` datetime DEFAULT NULL,
  `EquipmentModule` varchar(128) DEFAULT NULL,
  `LocalNetwork` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourceaircon` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `AirconType` int DEFAULT NULL,
  `CoolingCapacity` double DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourcebattery` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `BatteryType` int DEFAULT NULL,
  `RatedOutputVoltage` double DEFAULT NULL,
  `RatedCapacity` double DEFAULT NULL,
  `BatteryVoltage` int DEFAULT NULL,
  `BatteryCapacity` double DEFAULT NULL,
  `BatteryCounts` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourcedcpower` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `ModuleCapacity` double DEFAULT NULL,
  `ModuleNums` int DEFAULT NULL,
  `BatteryNums` int DEFAULT NULL,
  `BatteryCounts` int DEFAULT NULL,
  `BatteryCapacity` double DEFAULT NULL,
  `BatteryVoltage` int DEFAULT NULL,
  `SupplyInfo` varchar(255) DEFAULT NULL,
  `SystemCapacities` double DEFAULT NULL,
  `BatteryCapacities` double DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourcegenset` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `RatedOutputPower` double DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourceitem` (
  `EntryItemId` int NOT NULL,
  `EntryId` int NOT NULL,
  `EntryValue` varchar(255) DEFAULT NULL,
  `ItemId` int NOT NULL,
  `ItemValue` varchar(255) NOT NULL,
  `ParentEntryId` int NOT NULL DEFAULT '0',
  `ParentItemId` int NOT NULL DEFAULT '0',
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryItemId`)
);

CREATE TABLE `tbl_equipmentresourcepower` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `PowerType` int DEFAULT NULL,
  `ModuleCapacity` int DEFAULT NULL,
  `ModuleNums` int DEFAULT NULL,
  `BatteryNums` int DEFAULT NULL,
  `BatteryCounts` int DEFAULT NULL,
  `BatteryCapacity` double DEFAULT NULL,
  `BatteryVoltage` int DEFAULT NULL,
  `SupplyInfo` varchar(255) DEFAULT NULL,
  `SystemCapacities` double DEFAULT NULL,
  `BatteryCapacities` double DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourcetransformer` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `TransformerType` int DEFAULT NULL,
  `RatedCapacity` double DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);

CREATE TABLE `tbl_equipmentresourceups` (
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `UPSType` int DEFAULT NULL,
  `RatedCapacity` double DEFAULT NULL,
  `ModuleNums` int DEFAULT NULL,
  `BatteryNums` int DEFAULT NULL,
  `BatteryCounts` int DEFAULT NULL,
  `BatteryCapacity` double DEFAULT NULL,
  `BatteryVoltage` int DEFAULT NULL,
  `SupplyInfo` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentId`,`StationId`)
);