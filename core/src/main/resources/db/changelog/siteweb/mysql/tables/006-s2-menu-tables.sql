CREATE TABLE `tbl_menuitem` (
  `MenuItemId` int NOT NULL,
  `ParentId` int DEFAULT NULL,
  `Path` varchar(255) DEFAULT NULL,
  `Title` varchar(255) DEFAULT NULL,
  `Icon` varchar(255) DEFAULT NULL,
  `Selected` tinyint unsigned DEFAULT NULL,
  `Expanded` tinyint unsigned DEFAULT NULL,
  `PathMatch` varchar(255) DEFAULT NULL,
  `LayoutPosition` int DEFAULT NULL,
  `IsSystemConfig` tinyint unsigned DEFAULT NULL,
  `IsExternalWeb` tinyint unsigned DEFAULT NULL,
  `MenuHasNavigation` tinyint unsigned DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY `PK_TBL_MenuItem_ID` (`MenuItemId`)
);

CREATE TABLE `tbl_menuitems` (
  `MenuItemsId` int NOT NULL,
  `ParentMenuItemsId` int DEFAULT NULL,
  `MenuItemsName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`MenuItemsId`)
);

CREATE TABLE `tbl_menuitemstructuremap` (
  `MenuItemStructureMapId` int NOT NULL,
  `MenuProfileId` int NOT NULL,
  `MenuStructureId` int NOT NULL,
  `MenuItemId` int NOT NULL,
  `SortIndex` int DEFAULT NULL,
  PRIMARY KEY `PK_TBL_MenuItemStructureMap_ID` (`MenuItemStructureMapId`)
);

CREATE TABLE `tbl_menuprofileinfo` (
  `MenuProfileId` int NOT NULL,
  `Name` varchar(255) DEFAULT NULL,
  `Checked` tinyint unsigned DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY `PK_TBL_MenuProfileInfo_ID` (`MenuProfileId`)
);

CREATE TABLE `tbl_menus` (
  `MenusId` int NOT NULL,
  `MenusName` varchar(128) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`MenusId`)
);

CREATE TABLE `tbl_menusmap` (
  `MenusId` int NOT NULL,
  `MenuItemsId` int NOT NULL,
  PRIMARY KEY (`MenuItemsId`,`MenusId`)
);

CREATE TABLE `tbl_menustructureinfo` (
  `MenuStructureId` int NOT NULL,
  `MenuProfileId` int DEFAULT NULL,
  `ParentId` int DEFAULT NULL,
  `Title` varchar(255) DEFAULT NULL,
  `Icon` varchar(255) DEFAULT NULL,
  `Selected` tinyint unsigned DEFAULT NULL,
  `Expanded` tinyint unsigned DEFAULT NULL,
  `Hidden` tinyint unsigned DEFAULT NULL,
  `SortIndex` int DEFAULT NULL,
  `IsSystem` tinyint unsigned DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY `PK_TBL_MenuStructureInfo_ID` (`MenuStructureId`)
);