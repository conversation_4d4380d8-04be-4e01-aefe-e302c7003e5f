/*ALTER TABLE TSL_SamplerUnit  ALTER COLUMN PhoneNumber VARCHAR (128)
##
##ALTER TABLE TSL_Port  ALTER COLUMN PhoneNumber VARCHAR (128)
*/

update tbl_doorcontroller set Display=16 where DoorControlId=16;

TRUNCATE TABLE NBR_CallBackEventDic;

INSERT INTO NBR_CallBackEventDic (Major, MajorDesc, Minor, MinorDesc, MinorHex, AlarmEndMinor, ChannelNo)
VALUES (10001, '刷卡状态', 0, '刷卡没有错误',  null, -1, 169);

INSERT INTO NBR_CallBackEventDic (Major, MajorDesc, Minor, MinorDesc, MinorHex, AlarmEndMinor, ChannelNo)
VALUES (10001, '刷卡状态', 1, '非法卡',  null, -1, 169);


INSERT INTO NBR_CallBackEventDic (<PERSON>, MajorDesc, Minor, MinorDesc, MinorHex, AlarmEndMinor, ChannelNo)
VALUES (10002, '门状态', 0, '关',  null, -1, 28);

INSERT INTO NBR_CallBackEventDic (Major, MajorDesc, Minor, MinorDesc, MinorHex, AlarmEndMinor, ChannelNo)
VALUES (10002, '门状态', 1, '开',  null, 0, 28);