CREATE TABLE `gene_generatorelecunitprice` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `EquipmentId` int NOT NULL,
  `EquipmentBaseType` int DEFAULT NULL,
  `Year` int NOT NULL,
  `Month` int NOT NULL,
  `YearMonth` varchar(16) NOT NULL,
  `ElecUnitPrice` double NOT NULL,
  `UpdaterId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  UNIQUE KEY `IDX_Gene_GeneratorElecUnitPrice_1` (`EquipmentId`,`Year`,`Month`)
);

CREATE TABLE `gene_generatorext` (
  `GeneId` int NOT NULL,
  `RatedPower` double DEFAULT NULL,
  `RatedPowerConsumption` double DEFAULT NULL,
  `UnitFuelConsumption` double DEFAULT NULL,
  `DefaultElecUnitPrice` double NOT NULL,
  `UpdaterId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GeneId`),
  UNIQUE KEY `GeneId` (`GeneId`)
);

CREATE TABLE `gene_generatorhistorysignal` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `GeneId` int NOT NULL,
  `SignalId` int NOT NULL,
  `GeneIdAndSignalId` varchar(255) NOT NULL,
  `SignalValue` double DEFAULT NULL,
  `InsertTime` datetime NOT NULL,
  `SignalValid` tinyint(1) NOT NULL,
  `PowerSerialId` int DEFAULT NULL,
  `MakeAlarm` tinyint DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `IDX_Gene_HistorySignal_1` (`GeneIdAndSignalId`),
  KEY `IDX_Gene_HistorySignal_2` (`GeneId`)
);

CREATE TABLE `gene_generatorsignalcheckinfo` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `GeneId` int NOT NULL,
  `EquipmentTemplateId` int NOT NULL,
  `SignalId` int NOT NULL,
  `SignalName` varchar(255) NOT NULL,
  `DynamicOrStatic` tinyint(1) NOT NULL,
  `TriggerValue` double DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  `Extend2` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `IDX_Gene_CheckSignal_1` (`GeneId`)
);

CREATE TABLE `gene_maintenancerecord` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `GeneId` int NOT NULL,
  `GeneName` varchar(256) DEFAULT NULL,
  `MaintenanceTime` datetime NOT NULL,
  `Position` varchar(1024) DEFAULT NULL,
  `MaintenanceItem` varchar(1024) DEFAULT NULL,
  `Maintainer` varchar(128) DEFAULT NULL,
  `Confirmor` varchar(128) DEFAULT NULL,
  `Remark` varchar(1024) DEFAULT NULL,
  `UpdaterId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `IDX_Gene_MaintenanceRecord_1` (`GeneId`)
);

CREATE TABLE `gene_oilboxext` (
  `OilId` int NOT NULL,
  `RatedVolume` double DEFAULT NULL,
  `FullOilLevel` double DEFAULT NULL,
  `UpdaterId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`OilId`),
  UNIQUE KEY `OilId` (`OilId`)
);

CREATE TABLE `gene_oilboxgeneratormap` (
  `OilId` int NOT NULL,
  `GeneId` int NOT NULL,
  `UpdaterId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`OilId`,`GeneId`)
);

CREATE TABLE `gene_oillevelrecord` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `OilId` int NOT NULL,
  `CurrentOilLevel` double NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `UpdateUserId` int NOT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `IDX_Gene_OilLevel_1` (`OilId`)
);

CREATE TABLE `gene_powergenerationrecord` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `EquipmentId` int NOT NULL,
  `EquipmentBaseType` int DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `PositiveElecEnergyStart` double NOT NULL,
  `PositiveElecEnergyEnd` double DEFAULT NULL,
  `PowerGeneration` double DEFAULT NULL,
  `RunDuration` bigint DEFAULT NULL,
  `PowerGenerationCost` double DEFAULT NULL,
  `PowerGenerationOil` double DEFAULT NULL,
  `PowerGenerationOilCost` double DEFAULT NULL,
  `StartInsertTime` datetime DEFAULT NULL,
  `EndInsertTime` datetime DEFAULT NULL,
  `StartSerialNo` bigint DEFAULT NULL,
  `EndSerialNo` bigint DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `IDX_PowerGenerationRecord_EquipmentId_1` (`EquipmentId`),
  KEY `IDX_PowerGenerationRecord_3Fields_1` (`EquipmentId`,`EquipmentBaseType`,`StartTime`)
);

CREATE TABLE `gene_refuelrecord` (
  `SerialId` int NOT NULL AUTO_INCREMENT,
  `OilId` int NOT NULL,
  `OilName` varchar(255) DEFAULT NULL,
  `RefuelTime` datetime NOT NULL,
  `RefuelQuantity` double NOT NULL,
  `UnitPrice` double DEFAULT NULL,
  `RefuelFee` double DEFAULT NULL,
  `Operator` varchar(255) DEFAULT NULL,
  `UpdaterId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Extend1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SerialId`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `IDX_Gene_RefuelRecord_1` (`OilId`)
);

