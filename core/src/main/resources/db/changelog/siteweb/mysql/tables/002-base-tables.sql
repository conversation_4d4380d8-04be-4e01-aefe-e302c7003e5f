CREATE TABLE `tbl_equipmentbasetype` (
  `BaseEquipmentId` int NOT NULL,
  `BaseEquipmentName` varchar(128) DEFAULT NULL,
  `EquipmentTypeId` int NOT NULL,
  `EquipmentSubTypeId` int NOT NULL,
  `Description` text,
  `ExtField` json DEFAULT NULL,
  PRIMARY KEY (`BaseEquipmentId`),
  UNIQUE KEY `BaseEquipmentId` (`BaseEquipmentId`)
);

CREATE TABLE `baseunit` (
  `BaseUnitId` int NOT NULL AUTO_INCREMENT,
  `BaseUnitName` varchar(128) DEFAULT NULL,
  `BaseUnitNameEn` varchar(128) DEFAULT NULL,
  `BaseUnitSymbol` varchar(128) NOT NULL,
  `BaseUnitNameCode` varchar(128) DEFAULT NULL,
  `BaseUnitSymbolName` varchar(128) DEFAULT NULL,
  `BaseUnitNameDescription` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`BaseUnitId`),
  UNIQUE KEY `baseUnitId` (`BaseUnitId`)
);

CREATE TABLE `tbl_baseclassdic` (
  `BaseClassId` int NOT NULL,
  `BaseClassName` varchar(255) NOT NULL,
  `BaseClassIcon` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`BaseClassId`)
);

CREATE TABLE `tbl_basecommandcode` (
  `CodeId` int NOT NULL,
  `Command` varchar(255) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CodeId`)
);

CREATE TABLE `tbl_basedicver` (
  `HiVer` int NOT NULL,
  `LoVer` int NOT NULL,
  `Remark` text,
  `ExtendField1` text,
  `ExtendField2` text,
  PRIMARY KEY (`HiVer`,`LoVer`)
);

CREATE TABLE `tbl_basedicverhistory` (
  `Version` varchar(255) NOT NULL,
  `UpdateDesciption` text NOT NULL,
  `UpdateDate` datetime NOT NULL,
  `Editor` int NOT NULL,
  `ExtendField1` text,
  `ExtendField2` text,
  PRIMARY KEY (`UpdateDate`,`Version`)
);

CREATE TABLE `tbl_baseequipmentcategorymap` (
  `BaseEquipmentID` int NOT NULL,
  `EquipmentCategory` int NOT NULL,
  PRIMARY KEY (`BaseEquipmentID`,`EquipmentCategory`)
);

CREATE TABLE `tbl_baseequipmentmap` (
  `StandardType` int NOT NULL,
  `StandardDicId` int NOT NULL,
  `StationBaseType` int NOT NULL,
  `EquipmentBaseType` int NOT NULL,
  PRIMARY KEY (`EquipmentBaseType`,`StandardDicId`,`StandardType`,`StationBaseType`)
);

CREATE TABLE `tbl_basesignaleventcode` (
  `CodeId` int NOT NULL,
  `Category` varchar(255) NOT NULL,
  `Signal` varchar(255) DEFAULT NULL,
  `EVENT` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`CodeId`)
);

CREATE TABLE `tbl_baseunitdic` (
  `BaseUnitID` int NOT NULL,
  `BaseUnitName` varchar(255) NOT NULL,
  `BaseUnitSymbol` varchar(255) NOT NULL,
  `BaseUnitDescription` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`BaseUnitID`)
);

CREATE TABLE `tbl_signalbasedic` (
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `BaseEquipmentId` int NOT NULL,
  `EnglishName` text,
  `BaseLogicCategoryId` int DEFAULT NULL,
  `StoreInterval` int DEFAULT NULL,
  `AbsValueThreshold` double DEFAULT NULL,
  `PercentThreshold` double DEFAULT NULL,
  `StoreInterval2` int DEFAULT NULL,
  `AbsValueThreshold2` double DEFAULT NULL,
  `PercentThreshold2` double DEFAULT NULL,
  `ExtendField1` text,
  `ExtendField2` text,
  `ExtendField3` text,
  `UnitId` int DEFAULT NULL,
  `BaseStatusId` int DEFAULT NULL,
  `BaseHysteresis` double DEFAULT NULL,
  `BaseFreqPeriod` int DEFAULT NULL,
  `BaseFreqCount` int DEFAULT NULL,
  `BaseShowPrecision` varchar(30) DEFAULT NULL,
  `BaseStatPeriod` int DEFAULT NULL,
  `CGElement` varchar(128) DEFAULT NULL,
  `Description` text,
  `BaseNameExt` varchar(128) DEFAULT NULL,
  `IsSystem` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`BaseTypeId`),
  UNIQUE KEY `BaseTypeId` (`BaseTypeId`)
);

CREATE TABLE `tbl_signalbasemap` (
  `StandardDicId` int NOT NULL,
  `StandardType` int NOT NULL,
  `StationBaseType` int NOT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseCondId` int DEFAULT NULL,
  PRIMARY KEY (`BaseTypeId`,`StandardDicId`,`StandardType`,`StationBaseType`)
);

CREATE TABLE `tbl_signalbaseconfirm` (
  `SignalBaseConfirmId` int PRIMARY KEY AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `SignalId` int NOT NULL,
  `StateValue` int DEFAULT NULL,
  `SubState` varchar(16) DEFAULT NULL,
  KEY `idxsignalbaseconfirmID` (`EquipmentTemplateId`,`SignalId`)
);

CREATE TABLE `tbl_statusbasedic` (
  `BaseStatusId` int NOT NULL,
  `BaseStatusName` varchar(128) NOT NULL,
  `BaseCondId` int NOT NULL,
  `Operator` varchar(30) NOT NULL,
  `Value` int DEFAULT NULL,
  `Meaning` varchar(128) DEFAULT NULL,
  `Description` text,
  PRIMARY KEY (`BaseCondId`,`BaseStatusId`)
);

CREATE TABLE `tbl_commandbasedic` (
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `BaseEquipmentId` int NOT NULL,
  `EnglishName` text,
  `BaseLogicCategoryId` int DEFAULT NULL,
  `CommandType` int NOT NULL,
  `BaseStatusId` int DEFAULT NULL,
  `ExtendField1` text,
  `ExtendField2` text,
  `ExtendField3` text,
  `Description` text,
  `BaseNameExt` varchar(128) DEFAULT NULL,
  `IsSystem` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`BaseTypeId`),
  UNIQUE KEY `BaseTypeId` (`BaseTypeId`)
);

CREATE TABLE `tbl_commandbasemap` (
  `StandardDicId` int NOT NULL,
  `StandardType` int NOT NULL,
  `StationBaseType` int NOT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseCondId` int DEFAULT NULL,
  PRIMARY KEY (`BaseTypeId`,`StandardDicId`,`StandardType`,`StationBaseType`)
);

CREATE TABLE `tbl_controlbaseconfirm` (
  `ControlBaseConfirmId` int PRIMARY KEY AUTO_INCREMENT,
  `EquipmentTemplateId` int NOT NULL,
  `ControlId` int NOT NULL,
  `ParameterValue` int DEFAULT NULL,
  `SubState` varchar(16) DEFAULT NULL
);

CREATE TABLE `tbl_eventbasedic` (
  `BaseTypeId` decimal(12,0) NOT NULL,
  `BaseTypeName` varchar(128) NOT NULL,
  `BaseEquipmentId` int NOT NULL,
  `EnglishName` text,
  `EventSeverityId` int NOT NULL,
  `ComparedValue` double DEFAULT NULL,
  `BaseLogicCategoryId` int DEFAULT NULL,
  `StartDelay` int DEFAULT NULL,
  `EndDelay` int DEFAULT NULL,
  `ExtendField1` text,
  `ExtendField2` text,
  `ExtendField3` text,
  `ExtendField4` text,
  `ExtendField5` text,
  `Description` text,
  `BaseNameExt` varchar(128) DEFAULT NULL,
  `IsSystem` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`BaseTypeId`),
  UNIQUE KEY `BaseTypeId` (`BaseTypeId`)
);

CREATE TABLE `tbl_eventbaseconfirm` (
  `EquipmentTemplateId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `SubState` varchar(16) DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`,`EventConditionId`,`EventId`)
);

CREATE TABLE `tbl_eventbasemap` (
  `StandardDicId` int NOT NULL,
  `StandardType` int NOT NULL,
  `StationBaseType` int NOT NULL,
  `BaseTypeId` decimal(12,0) NOT NULL,
  PRIMARY KEY (`BaseTypeId`,`StandardDicId`,`StandardType`,`StationBaseType`)
);

CREATE TABLE `tbl_logiccategorybasedic` (
  `BaseEquipmentId` int NOT NULL,
  `BaseLogicCategoryType` int NOT NULL,
  `BaseLogicCategoryId` int NOT NULL,
  `BaseLogicCategoryName` varchar(128) DEFAULT NULL,
  `Description` text,
  PRIMARY KEY (`BaseLogicCategoryId`),
  UNIQUE KEY `BaseLogicCategoryId` (`BaseLogicCategoryId`)
);