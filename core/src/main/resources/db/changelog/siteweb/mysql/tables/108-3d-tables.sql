CREATE TABLE `devicerefdimension` (
  `DeviceRefDimensionId` int NOT NULL AUTO_INCREMENT,
  `ObjectBindData` int NOT NULL,
  `type` varchar(128) NOT NULL,
  `DimensionDesignerId` int NOT NULL,
  `Relation` varchar(128) NOT NULL,
  PRIMARY KEY (`DeviceRefDimensionId`),
  UNIQUE KEY `DeviceRefDimensionId` (`DeviceRefDimensionId`)
);

CREATE TABLE `dimensionconfigure` (
  `DimensionConfigureId` int NOT NULL AUTO_INCREMENT,
  `DimensionConfigureType` int DEFAULT NULL COMMENT '资源类型',
  `DimensionConfigure` mediumtext COMMENT '可变的的配置项',
  `DimensionConfigureUuid` varchar(128) DEFAULT NULL COMMENT '唯一uuid',
  PRIMARY KEY (`DimensionConfigureId`),
  UNIQUE KEY `DimensionConfigureId` (`DimensionConfigureId`)
);

CREATE TABLE `dimensiondesigner` (
  `DimensionDesignerId` int NOT NULL AUTO_INCREMENT,
  `DimensionDesignerName` varchar(128) DEFAULT NULL COMMENT '名称',
  `FilePath` varchar(256) DEFAULT NULL COMMENT '文件路径',
  `UpdateTime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`DimensionDesignerId`),
  UNIQUE KEY `DimensionDesignerId` (`DimensionDesignerId`)
);

CREATE TABLE `dimensionmodel` (
  `DimensionModelId` int NOT NULL AUTO_INCREMENT,
  `DimensionModelCategory` int DEFAULT NULL COMMENT '模型类型',
  `DimensionModelName` varchar(128) DEFAULT NULL COMMENT '模型名称',
  `DimensionModelFile` varchar(128) DEFAULT NULL COMMENT '模型文件',
  PRIMARY KEY (`DimensionModelId`),
  UNIQUE KEY `DimensionModelId` (`DimensionModelId`)
);

CREATE TABLE `dimensionlinks` (
  `lineId` varchar(64) NOT NULL,
  `displayName` varchar(64) DEFAULT NULL,
  `lineType` int DEFAULT NULL,
  `localObjectId` int DEFAULT NULL,
  `localObjectType` int DEFAULT NULL,
  `localObjectPort` varchar(64) DEFAULT NULL,
  `localObjectModelPort` int DEFAULT NULL,
  `remoteObjectId` int DEFAULT NULL,
  `remoteObjectType` int DEFAULT NULL,
  `remoteObjectPort` varchar(64) DEFAULT NULL,
  `remoteObjectModelPort` int DEFAULT NULL,
  `staticPropertys` json DEFAULT NULL,
  `description` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`lineId`)
);