CREATE TABLE `aircon_autocontrolequipmentchangelog` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `StationId` int DEFAULT NULL,
  `StationName` varchar(255) DEFAULT NULL,
  `MonitorUnitId` int DEFAULT NULL,
  `MonitorUnitName` varchar(255) DEFAULT NULL,
  `VirtualEquipmentId` int NOT NULL,
  `GroupName` varchar(128) DEFAULT NULL,
  `GroupNameNew` varchar(128) DEFAULT NULL,
  `OperateType` int NOT NULL,
  `OperateTypeLabel` varchar(16) NOT NULL,
  `OperateModule` int NOT NULL,
  `OperateModuleLabel` varchar(64) NOT NULL,
  `SubOperateType` int DEFAULT NULL,
  `SubOperateTypeLabel` varchar(16) DEFAULT NULL,
  `SubObjectType` int DEFAULT NULL,
  `SubObjectTypeLabel` varchar(64) DEFAULT NULL,
  `Operator` varchar(128) DEFAULT NULL,
  `OperatorId` int DEFAULT NULL,
  `InsertTime` datetime NOT NULL,
  `ChangeContent` varchar(4000) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LogId`),
  UNIQUE KEY `LogId` (`LogId`)
);

CREATE TABLE `aircon_autocontrolpara` (
  `VirtualEquipmentId` int NOT NULL,
  `VirtualEquipmentName` varchar(128) NOT NULL,
  `GroupName` varchar(128) NOT NULL,
  `StationId` int NOT NULL,
  `SamplerAddress` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `ParaEnable` int NOT NULL,
  `RollingCount` int NOT NULL,
  `TempComputeMode` double NOT NULL,
  `TempCoolAll` double NOT NULL,
  `TempCoolStart` double NOT NULL,
  `WorkTemp` double NOT NULL,
  `TempInFanStop` double NOT NULL,
  `TempBottomLow` double NOT NULL,
  `TempSetting` double NOT NULL,
  `RunPeriod` int NOT NULL,
  `OperationInterval` int NOT NULL,
  `TempDiff` double NOT NULL,
  `FanInstall` int NOT NULL,
  `TempFanStart` double NOT NULL,
  `OutInTempDiff` double NOT NULL,
  `TempOutFanStop` double NOT NULL,
  `EnableWarm` int NOT NULL,
  `TempHot` double NOT NULL,
  `TempHotStart` double NOT NULL,
  `TempHotAll` double NOT NULL,
  `LastSuccussDeployTime` datetime DEFAULT NULL,
  `LastDeployState` int DEFAULT NULL,
  `LastDeployTime` datetime DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Operationor` int DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`VirtualEquipmentId`),
  UNIQUE KEY `VirtualEquipmentId` (`VirtualEquipmentId`)
);

CREATE TABLE `aircon_batchcontrolequipmentmap` (
  `GroupId` varchar(255) NOT NULL,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `UpdateId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GroupId`,`StationId`,`EquipmentId`)
);

CREATE TABLE `aircon_batchcontrolgroup` (
  `GroupId` varchar(255) NOT NULL COMMENT 'UUID PK',
  `GroupName` varchar(128) NOT NULL,
  `CreatorId` int DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `UpdateId` int DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GroupId`),
  UNIQUE KEY `GroupId` (`GroupId`)
);

CREATE TABLE `aircon_batchcontrolgroupchangelog` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `OperateType` int NOT NULL,
  `OperateTypeLabel` varchar(16) NOT NULL,
  `Operator` varchar(128) DEFAULT NULL,
  `OperatorId` int DEFAULT NULL,
  `GroupId` varchar(255) NOT NULL,
  `GroupName` varchar(128) DEFAULT NULL,
  `GroupNameNew` varchar(128) DEFAULT NULL,
  `InsertTime` datetime NOT NULL,
  `ChangeContent` varchar(4000) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LogId`),
  UNIQUE KEY `LogId` (`LogId`)
);

CREATE TABLE `aircon_batchcontrolrecord` (
  `RecordId` int NOT NULL AUTO_INCREMENT,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `ControlId` int NOT NULL,
  `StdControlId` int NOT NULL,
  `StdWorkModeFlag` int NOT NULL,
  `AirStdTypeId` int NOT NULL,
  `AirCommon2No` int NOT NULL,
  `SerialNo` int NOT NULL,
  `Uuid` varchar(255) NOT NULL,
  `InsertTime` datetime NOT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`RecordId`),
  UNIQUE KEY `RecordId` (`RecordId`)
);

CREATE TABLE `aircon_complexindexmap` (
  `MapId` varchar(255) NOT NULL COMMENT 'UUID PK',
  `StationId` int NOT NULL,
  `VirtualEquipmentId` int NOT NULL,
  `ComplexIndexId` int NOT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `UpdateId` int DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`MapId`),
  UNIQUE KEY `MapId` (`MapId`)
);

CREATE TABLE `aircon_equipmentcontrolpara` (
  `VirtualEquipmentId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `InsertTime` datetime DEFAULT NULL,
  `Operationor` int DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`VirtualEquipmentId`,`EquipmentId`)
);

CREATE TABLE `aircon_fancontrolpara` (
  `VirtualEquipmentId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `InsertTime` datetime DEFAULT NULL,
  `Operationor` int DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`VirtualEquipmentId`,`EquipmentId`)
);

CREATE TABLE `aircon_stdsignal` (
  `TypeId` int NOT NULL,
  `StdSignalId` int NOT NULL,
  `StdSignalName` varchar(255) NOT NULL,
  `StdSignalUnit` varchar(16) DEFAULT NULL,
  `StdSignalRemark` varchar(255) DEFAULT NULL,
  `StdSignalType` int NOT NULL,
  `NeedShow` int NOT NULL,
  `CommandColor` int NOT NULL,
  `BusinessTypeId` int NOT NULL,
  `MapRequirement` int NOT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`TypeId`,`StdSignalId`)
);

CREATE TABLE `aircon_stdsignaltype` (
  `SignalTypeId` int NOT NULL,
  `SignalTypeName` varchar(128) NOT NULL,
  `SignalTypeRemark` varchar(255) DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SignalTypeId`),
  UNIQUE KEY `SignalTypeId` (`SignalTypeId`)
);

CREATE TABLE `aircon_stdtype` (
  `TypeId` int NOT NULL,
  `TypeName` varchar(128) NOT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`TypeId`),
  UNIQUE KEY `TypeId` (`TypeId`)
);

CREATE TABLE `aircon_tempcontrolpara` (
  `VirtualEquipmentId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `StationId` int NOT NULL,
  `MonitorUnitId` int NOT NULL,
  `InsertTime` datetime DEFAULT NULL,
  `Operationor` int DEFAULT NULL,
  `BeOutTemp` int DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`VirtualEquipmentId`,`EquipmentId`)
);

CREATE TABLE `aircon_templatestdsignalmap` (
  `EquipmentTemplateId` int NOT NULL,
  `TypeId` int NOT NULL,
  `StdSignalId` int NOT NULL,
  `DefaultValue` decimal(18,0) DEFAULT NULL,
  `SwSignalId` int DEFAULT NULL,
  `SwSignalName` varchar(128) DEFAULT NULL,
  `SwSignalChanelNum` int DEFAULT NULL,
  `SwCmdToken` varchar(64) DEFAULT NULL,
  `SwParam` varchar(64) DEFAULT NULL,
  `SwOperator` varchar(64) DEFAULT NULL,
  `SwCmpValue` double DEFAULT NULL,
  `UpdateDate` datetime NOT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(128) DEFAULT NULL,
  `ExtendField1` varchar(128) DEFAULT NULL,
  `ExtendField2` varchar(128) DEFAULT NULL,
  `ExtendField3` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`,`TypeId`,`StdSignalId`),
  KEY `IDX_TemplateStdSignalMap_1` (`EquipmentTemplateId`,`TypeId`)
);

CREATE TABLE `aircon_templatestdtype` (
  `EquipmentTemplateId` int NOT NULL,
  `TypeId` int DEFAULT NULL,
  `TypeName` varchar(128) DEFAULT NULL,
  `UpdateDate` datetime NOT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(128) DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EquipmentTemplateId`),
  UNIQUE KEY `EquipmentTemplateId` (`EquipmentTemplateId`)
);

CREATE TABLE `aircon_zonecontroloperation` (
  `OperationId` int NOT NULL AUTO_INCREMENT,
  `SchemeId` varchar(255) NOT NULL,
  `OperationTime` varchar(16) NOT NULL,
  `OperationCmdId` int NOT NULL,
  `OperationCmdName` varchar(64) NOT NULL,
  `Params` varchar(64) DEFAULT NULL,
  `UpdateTime` datetime NOT NULL,
  `UpdateId` int NOT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`OperationId`),
  UNIQUE KEY `OperationId` (`OperationId`)
);

CREATE TABLE `aircon_zonecontrolscheme` (
  `SchemeId` varchar(255) NOT NULL COMMENT 'UUID PK',
  `SchemeName` varchar(255) NOT NULL,
  `StartDate` varchar(16) NOT NULL,
  `EndDate` varchar(16) NOT NULL,
  `VirtualEquipmentId` int NOT NULL,
  `StationId` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `UpdateId` int NOT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`SchemeId`),
  UNIQUE KEY `SchemeId` (`SchemeId`)
);