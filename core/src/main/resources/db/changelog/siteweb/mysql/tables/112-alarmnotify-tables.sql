CREATE TABLE `alarmnotifyconfig` (
  `AlarmNotifyConfigId` int NOT NULL AUTO_INCREMENT,
  `ConfigName` varchar(128) NOT NULL,
  `UsedStatus` tinyint(1) NOT NULL,
  `ContentTemplate` varchar(1000) NOT NULL,
  `NotifyDelay` int DEFAULT NULL,
  `Layout` mediumtext,
  `Description` varchar(255) DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `DepartmentId` int DEFAULT '0',
  PRIMARY KEY (`AlarmNotifyConfigId`),
  UNIQUE KEY `AlarmNotifyConfigId` (`AlarmNotifyConfigId`)
);

CREATE TABLE `alarmnotifyelement` (
  `ElementId` int NOT NULL AUTO_INCREMENT,
  `ElementName` varchar(128) NOT NULL,
  `ElementType` varchar(64) NOT NULL,
  `InputNodesCount` int NOT NULL,
  `OutputNodesCount` int NOT NULL,
  `Icon` varchar(64) DEFAULT NULL,
  `Expression` varchar(500) DEFAULT NULL,
  `Visible` tinyint(1) NOT NULL,
  `SortIndex` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ElementId`),
  UNIQUE KEY `ElementId` (`ElementId`)
);

CREATE TABLE `alarmnotifyelementconfig` (
  `AlarmNotifyElementConfigId` int NOT NULL AUTO_INCREMENT,
  `AlarmNotifyConfigId` int NOT NULL,
  `ElementId` int NOT NULL,
  `Expression` varchar(1000) DEFAULT NULL,
  `ExtendField1` varchar(500) DEFAULT NULL,
  `ExtendField2` varchar(500) DEFAULT NULL,
  `ExtendField3` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`AlarmNotifyElementConfigId`),
  UNIQUE KEY `AlarmNotifyElementConfigId` (`AlarmNotifyElementConfigId`)
);

CREATE TABLE `alarmnotifyfiltercondition` (
  `FilterConditionId` int NOT NULL,
  `FilterConditionName` varchar(128) NOT NULL,
  PRIMARY KEY (`FilterConditionId`),
  UNIQUE KEY `FilterConditionId` (`FilterConditionId`)
);

CREATE TABLE `alarmnotifyfilterrule` (
  `AlarmNotifyFilterRuleId` int NOT NULL AUTO_INCREMENT,
  `AlarmNotifyConfigId` int NOT NULL,
  `FilterConditionId` int NOT NULL,
  `FilterParameter` varchar(10000) NOT NULL,
  PRIMARY KEY (`AlarmNotifyFilterRuleId`),
  UNIQUE KEY `AlarmNotifyFilterRuleId` (`AlarmNotifyFilterRuleId`)
);

CREATE TABLE `alarmnotifygatewayservice` (
  `AlarmNotifyGatewayServiceId` int NOT NULL AUTO_INCREMENT,
  `ElementId` int DEFAULT NULL COMMENT '发送方式id',
  `GatewayServiceUrl` varchar(64) DEFAULT NULL COMMENT '网关服务url',
  `Description` varchar(256) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`AlarmNotifyGatewayServiceId`),
  UNIQUE KEY `AlarmNotifyGatewayServiceId` (`AlarmNotifyGatewayServiceId`)
);

CREATE TABLE `alarmnotifygatewayserviceconfig` (
  `AlarmNotifyGatewayServiceConfigId` int NOT NULL AUTO_INCREMENT,
  `AlarmNotifyConfigId` int DEFAULT NULL COMMENT '策略id',
  `AlarmNotifyGatewayServiceId` int DEFAULT NULL COMMENT 'AlarmNotifyGatewayServiceId',
  PRIMARY KEY (`AlarmNotifyGatewayServiceConfigId`),
  UNIQUE KEY `AlarmNotifyGatewayServiceConfigId` (`AlarmNotifyGatewayServiceConfigId`)
);

CREATE TABLE `alarmnotifynode` (
  `NodeId` int NOT NULL AUTO_INCREMENT,
  `AlarmNotifyElementConfigId` int NOT NULL,
  `NodeDirection` varchar(10) NOT NULL,
  `NodeType` varchar(64) NOT NULL,
  `NodeIndex` int NOT NULL,
  `NodeTag` varchar(255) DEFAULT NULL,
  `Expression` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`NodeId`),
  UNIQUE KEY `NodeId` (`NodeId`)
);

CREATE TABLE `alarmnotifyrecord` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AlarmNotifyConfigId` int NOT NULL,
  `StationId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `SequenceId` varchar(128) NOT NULL,
  `EventSeverityId` int NOT NULL,
  `Content` varchar(1000) NOT NULL,
  `AlarmStartTime` datetime NOT NULL,
  `SendTime` datetime NOT NULL,
  `Receiver` varchar(255) NOT NULL,
  `SendType` varchar(20) NOT NULL,
  `SendResult` varchar(100) NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `alarmnotifysegment` (
  `SegmentId` int NOT NULL AUTO_INCREMENT,
  `InputElementConfigId` int NOT NULL,
  `InputNodeId` int NOT NULL,
  `OutputElementConfigId` int NOT NULL,
  `OutputNodeId` int NOT NULL,
  PRIMARY KEY (`SegmentId`),
  UNIQUE KEY `SegmentId` (`SegmentId`)
);