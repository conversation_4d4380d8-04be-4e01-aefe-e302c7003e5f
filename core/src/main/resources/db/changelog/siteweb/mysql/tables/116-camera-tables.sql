CREATE TABLE `camera` (
  `CameraId` int NOT NULL AUTO_INCREMENT,
  `CameraName` varchar(255) DEFAULT NULL COMMENT '摄像头名称',
  `CameraIp` varchar(128) DEFAULT NULL COMMENT '摄像头ip',
  `CameraPort` int DEFAULT NULL COMMENT '摄像头端口',
  `ChannelNumber` varchar(32) DEFAULT NULL COMMENT '信道号',
  `CameraGroupId` int DEFAULT NULL COMMENT '摄像头分组id',
  `UserName` varchar(128) DEFAULT NULL COMMENT '用户名',
  `Password` varchar(32) DEFAULT NULL COMMENT '密码',
  `VendorId` int DEFAULT NULL COMMENT '厂商id',
  `VendorName` varchar(128) DEFAULT NULL COMMENT '厂商名称',
  `CameraIndexCode` varchar(255) DEFAULT NULL COMMENT '唯一id',
  `CameraType` int DEFAULT NULL COMMENT '摄像头类型',
  `CameraTypeName` varchar(255) DEFAULT NULL COMMENT '摄像头名称',
  `UpdateTime` datetime DEFAULT NULL COMMENT '最近更新时间',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`CameraId`),
  UNIQUE KEY `CameraId` (`CameraId`)
);

CREATE TABLE `cameragroup` (
  `CameraGroupId` int NOT NULL AUTO_INCREMENT,
  `CameraGroupName` varchar(255) DEFAULT NULL COMMENT '摄像头分组名称',
  `ParentId` int DEFAULT NULL COMMENT '父级id',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`CameraGroupId`),
  UNIQUE KEY `CameraGroupId` (`CameraGroupId`)
);

CREATE TABLE `camerapollgroup` (
  `CameraPollGroupId` int NOT NULL AUTO_INCREMENT,
  `CameraPollGroupName` varchar(255) DEFAULT NULL COMMENT '摄像头采集周期名称',
  `PollInterval` int DEFAULT NULL COMMENT '采集周期',
  `Description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`CameraPollGroupId`),
  UNIQUE KEY `CameraPollGroupId` (`CameraPollGroupId`)
);

CREATE TABLE `camerapollgroupmap` (
  `CameraPollGroupMapId` int NOT NULL AUTO_INCREMENT,
  `CameraPollGroupId` int DEFAULT NULL COMMENT '摄像头采集周期id',
  `CameraId` int DEFAULT NULL COMMENT '摄像头id',
  PRIMARY KEY (`CameraPollGroupMapId`),
  UNIQUE KEY `CameraPollGroupMapId` (`CameraPollGroupMapId`)
);