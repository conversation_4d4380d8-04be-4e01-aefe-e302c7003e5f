-- 系统用到的单位计量
insert into baseUnit VALUES (1, '电压', 'Voltage', 'V', 'U', '伏特', '电位、电压、电动势');
insert into baseUnit VALUES (2, '电压_kV', 'Voltage_kV', 'kV', 'U', '千伏', '高压');
insert into baseUnit VALUES (3, '电流', 'Current', 'A', 'I', '安培', '');
insert into baseUnit VALUES (4, '频率', 'Frequency', 'Hz', 'F', '赫兹', '');
insert into baseUnit VALUES (5, '功率因数PF', 'Power Factor', '', 'PF', 'η', '');
insert into baseUnit VALUES (6, '有功功率P_W', 'Active Power', 'W', 'P', '瓦', '');
insert into baseUnit VALUES (7, '有功功率P_kW', 'Active Power_kW', 'kW', 'P', '千瓦', '有功功率、辐射通量');
insert into baseUnit VALUES (8, '有功功率P_MW', 'Active Power_MW', 'MW', 'P', '兆瓦', '光伏');
insert into baseUnit VALUES (9, '电能', 'Energy', 'kWh', '', '千瓦时', '');
insert into baseUnit VALUES (10, '无功功率Q_Var', 'Reactive Power', 'Var', 'Q', '乏', '');
insert into baseUnit VALUES (11, '无功功率Q_kVar', 'Reactive Power_kVar', 'kVar', 'Q', '千乏', '');
insert into baseUnit VALUES (12, '无功电能_Varh', 'Reactive Energy', 'Varh', '', '乏时', '无功电量');
insert into baseUnit VALUES (13, '无功电能', 'Reactive Energy_kVarh', 'kVarh', '', '千乏时', '');
insert into baseUnit VALUES (14, '视在功率S_VA', 'Apparent Power', 'VA', 'S', '伏安', '');
insert into baseUnit VALUES (15, '视在功率S_kVA', 'Apparent Power_kVA', 'kVA', 'S', '千伏安', '');
insert into baseUnit VALUES (16, '温度', 'Temperature', '℃', '', '摄氏度', '如果资料为华氏度，需协议转换为摄氏度');
insert into baseUnit VALUES (17, '湿度', 'Humidity', '%RH', '', '相对湿度', '');
insert into baseUnit VALUES (18, '转速', 'Rotational Speed', 'r/min', '', '转/分钟', '风扇转速');
insert into baseUnit VALUES (19, '压力', 'Pressure', 'kPa', '', '千帕', '压缩机X压力、油压、压强、应力');
insert into baseUnit VALUES (20, '压力_MPa', 'Pressure_Mpa', 'MPa', '', '兆帕', '冷凝器进水压力，由协议统一，同类设备、信号使用一种');
insert into baseUnit VALUES (21, '力', 'Power', 'N', '', '牛顿', '力、重力');
insert into baseUnit VALUES (22, '容量', 'Capacity', 'L', '', '升', '燃油剩余容量');
insert into baseUnit VALUES (23, '电池容量', 'Battery Capacity', 'Ah', '', '安时', '');
insert into baseUnit VALUES (24, '百分比', 'percentage', '%', '', '负载率', '');
insert into baseUnit VALUES (25, '比例带', 'Proportional Band', '', '', '', '');
insert into baseUnit VALUES (26, '年', 'Year', 'Y', '', '日期：年', '');
insert into baseUnit VALUES (27, '月', 'Month', 'M', '', '日期：月', '');
insert into baseUnit VALUES (28, '日', 'Day', 'D', '', '日期：日', '');
insert into baseUnit VALUES (29, '小时', 'Hour', 'h', '', '时间：小时', '累计运行时间');
insert into baseUnit VALUES (30, '分钟', 'Minute', 'min', '', '时间：分种', '电池后备时间');
insert into baseUnit VALUES (31, '秒', 'Second', 'sec', '', '时间：秒', '压缩机重开保护时间');
insert into baseUnit VALUES (32, '电阻', 'Resistance', 'kΩ', 'R', '千欧', '');
insert into baseUnit VALUES (33, '电池内阻', 'Battery Internal Resistance', 'mΩ', '', '毫欧', '电池内阻');
insert into baseUnit VALUES (34, '电容', 'Capacity', 'μF', 'C', '微法', '');
insert into baseUnit VALUES (35, '电感', 'Inductance', 'H', '', '亨利', '');
insert into baseUnit VALUES (36, '长度', 'Length', 'm', '', '米', '长度、高度');
insert into baseUnit VALUES (37, '重量', 'Weight', 'kg', '', '公斤/千克', '');
insert into baseUnit VALUES (38, '照度', 'Illuminance', 'lx', '', '勒克斯', '衡量单位面积的光通量');
insert into baseUnit VALUES (39, '光通量', 'Luminous Flux', 'lm', '', '流明', '');
insert into baseUnit VALUES (40, '数量', 'Number of', 'pcs', '', '', '个');
insert into baseUnit VALUES (41, '日照幅度', 'Solar Irradiance', 'W/m2', '', '', '');
insert into baseUnit VALUES (42, '风速', 'Wind Speed', 'm/s', '', '米/秒', '');
insert into baseUnit VALUES (43, '电磁转矩', 'Electromagnetic Torque', 'Nm', '', '牛米', '');
insert into baseUnit VALUES (44, '体积', 'Volume', 'm3', '', '立方米', '容积、风量');
insert into baseUnit VALUES (45, '电池电导', 'Battery Conductance', 'S', '', '西门子', '电池电导');
insert into baseUnit VALUES (46, '发电量_MWh', 'Power Generation', 'MWh', '', '兆瓦时', '累计电能');
insert into baseUnit VALUES (47, '烟雾浓度', 'Smoke Density', '%obs/m', '', '', '单位米的距离下光被烟雾颗粒所遮蔽的程度的百分比');
insert into baseUnit VALUES (48, '步', 'Step', '步', '', '', '投切步数');
insert into baseUnit VALUES (49, '系数', 'Coefficient', '', '', '', '比例系数');


-- 系统基本cron表达式
insert into goCronExpression VALUES (1, '0/1 * * * * ? *', 'Every(1).Second()', '每1秒', 'Every second');
insert into goCronExpression VALUES (2, '0/5 * * * * ? ', 'Every(5).Seconds()', '每5秒', 'Every 5 seconds');
insert into goCronExpression VALUES (3, '0/10 * * * * ? ', 'Every(10).Seconds()', '每10秒', 'Every 10 seconds');
insert into goCronExpression VALUES (4, '0/15 * * * * ? ', 'Every(15).Seconds()', '每15秒', 'Every 15 seconds');
insert into goCronExpression VALUES (5, '0/30 * * * * ? ', 'Every(30).Seconds()', '每30秒', 'Every 30 seconds');
insert into goCronExpression VALUES (6, '0 0/1 * * * ? *', 'Every(1).Minute()', '每1分', 'Every minute');
insert into goCronExpression VALUES (7, '0 0/5 * * * ? *', 'Every(5).Minutes()', '每5分', 'Every 5 minutes');
insert into goCronExpression VALUES (8, '0 0/10 * * * ? *', 'Every(10).Minutes()', '每10分', 'Every 10 minutes');
insert into goCronExpression VALUES (9, '0 0/15 * * * ? *', 'Every(15).Minutes()', '每15分', 'Every 15 minutes');
insert into goCronExpression VALUES (10, '0 0/30 * * * ? *', 'Every(30).Minutes()', '每30分', 'Every 30 minutes');
insert into goCronExpression VALUES (11, '0 0 0/1 * * ? *', 'Every(1).Hour()', '每1小时', 'Every hour');
insert into goCronExpression VALUES (12, '0 0 0/12 * * ? *', 'Every(12).Hours()', '每12小时', 'Every 12 hours');
insert into goCronExpression VALUES (13, '0 0 0 * * ? *', 'Every(1).Day().At(00:00)', '每天凌晨', '@every 30s at 00:00');
insert into goCronExpression VALUES (14, '0 0 12 * * ? *', 'Every(1).Day().At(12:00)', '每天中午12点', '@every 30s at 12:00');
insert into gocronexpression values (15, '0 0 0/1 * * ? *', 'reporttimingtask','每小时', null);
insert into gocronexpression values (16, '0 0 0/12 * * ? *', 'reporttimingtask','每12小时', null);
insert into gocronexpression values (17, '0 0 0 * * ? *', 'reporttimingtask','每天', null);
insert into gocronexpression values (18, '0 0 0 ? * 2/7', 'reporttimingtask','每周', null);
insert into gocronexpression values (19, '0 0 0 1 1/1 ?', 'reporttimingtask','每月', null);
insert into gocronexpression values (20, '0 0 1 * * ? *', 'Every(1).Day().At(01:00)','每天凌晨1点', null);
insert into goCronExpression VALUES (21, '0/1 * * * * ? *', 'Every(1).Second()', '每1秒', 'last');
insert into goCronExpression VALUES (22, '0/5 * * * * ? ', 'Every(5).Seconds()', '每5秒', 'last');
insert into goCronExpression VALUES (23, '0/10 * * * * ? ', 'Every(10).Seconds()', '每10秒', 'last');
insert into goCronExpression VALUES (24, '0/15 * * * * ? ', 'Every(15).Seconds()', '每15秒', 'last');
insert into goCronExpression VALUES (25, '0/30 * * * * ? ', 'Every(30).Seconds()', '每30秒', 'last');
insert into goCronExpression VALUES (26, '0 0/1 * * * ? *', 'Every(1).Minute()', '每1分', 'last');
insert into goCronExpression VALUES (27, '0 0/5 * * * ? *', 'Every(5).Minutes()', '每5分', 'last');
insert into goCronExpression VALUES (28, '0 0/10 * * * ? *', 'Every(10).Minutes()', '每10分', 'last');
insert into goCronExpression VALUES (29, '0 0/15 * * * ? *', 'Every(15).Minutes()', '每15分', 'last');
insert into goCronExpression VALUES (30, '0 0/30 * * * ? *', 'Every(30).Minutes()', '每30分', 'last');
insert into goCronExpression VALUES (31, '0 2 0/1 * * ? *', 'Every(1).Hour()', '每1小时02分', 'last');
insert into goCronExpression VALUES (32, '0 2 0/12 * * ? *', 'Every(12).Hours()', '每12小时02分', 'last');
insert into goCronExpression VALUES (33, '0 2 0 * * ? *', 'Every(1).Day().At(00:00)', '每天凌晨02分', 'last');
insert into goCronExpression VALUES (34, '0 2 12 * * ? *', 'Every(1).Day().At(12:00)', '每天中午12点02分', 'last');
insert into gocronexpression values (35, '0 2 0 * * ? *', 'reporttimingtask','每天02分', 'last');
insert into gocronexpression values (36, '0 2 0 ? * 2/7', 'reporttimingtask','每周02分', 'last');
insert into gocronexpression values (37, '0 2 0 1 1/1 ?', 'reporttimingtask','每月02分', 'last');
insert into gocronexpression values (38, '0 2 1 * * ? *', 'Every(1).Day().At(01:00)','每天凌晨1点', 'last');