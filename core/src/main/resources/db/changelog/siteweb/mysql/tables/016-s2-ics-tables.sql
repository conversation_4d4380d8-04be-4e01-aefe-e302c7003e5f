CREATE TABLE `tbl_icsarchivetask` (
  `TaskId` int,
  `TaskName` varchar(128),
  `ProcedureName` varchar(128),
  `FileName` varchar(128),
   PRIMARY KEY (TaskId)
);

CREATE TABLE `tbl_icscontractinstall` (
  `ContractNo` varchar(128),
  `ProjectName` varchar(128),
  `PrimaryDate` datetime,
  `EndDate` datetime,
  `QualityStartPoint` varchar(128),
  `QualityPeriod` int,
  `QualityTerms` varchar(128),
  `StationCount` int,
  `FsuCount` int,
  `EquipmentCount` int,
   PRIMARY KEY (ContractNo)
);

CREATE TABLE `tbl_icscontractmaintenance` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ContractNo` varchar(128),
  `ProjectName` varchar(128),
  `StartDate` datetime,
  `EndDate` datetime,
  `MaintenanceTerms` varchar(128),
  PRIMARY <PERSON> (`Id`)
);

CREATE TABLE `tbl_icsequipmenttype` (
  `EquipmentTypeId` int,
  `EquipmentTypeName` varchar(128),
  `IsCanInputId` int,
  PRIMARY KEY (EquipmentTypeId)
);

CREATE TABLE `tbl_icsfsudatainfo` (
  `CollectTime` datetime NOT NULL,
  `SampleTime` datetime,
  `SN` varchar(128),
  `Mac` varchar(128),
  `Ip` varchar(128),
  `FsuType` varchar(128),
  `SiteName` varchar(128),
  `SiteVersion` varchar(128),
  `SiteCompileTime` datetime,
  `Hw` varchar(128),
  `FileSystem` varchar(128),
  `Linux` varchar(128),
  `CpuUsage` double,
  `MemTotal` double,
  `MemFree` double,
  `MemUsage` double,
  `UpTime` double,
  `FileNr` varchar(128),
  `MuFileName` varchar(128),
  `MuModifyTime` datetime,
  `MuFileSize` int,
  `BackUpMd5` varchar(128),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsudatanewinfo` (
  `CollectTime` datetime NOT NULL,
  `SiteName` varchar(128),
  `FsuType` varchar(128),
  `Hw` varchar(128),
  `SN` varchar(128),
  `Mac` varchar(128),
  `Ip` varchar(128),
  `MemTotal` double,
  `FlashSize` varchar(60),
  `Linux` varchar(128),
  `SiteVersion` varchar(128),
  `CpuUsage` double,
  `MemUsage` double,
  `FlashUsedRate` varchar(60),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsuflashdf` (
  `CollectTime` datetime NOT NULL,
  `SN` varchar(128),
  `Mac` varchar(128),
  `FlashName` varchar(128),
  `FileSystem` varchar(128),
  `FlashSize` varchar(60),
  `FlashUsed` varchar(60),
  `FlashAvailable` varchar(60),
  `FlashUsedRate` varchar(60),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsuftpdownloadinfo` (
  `DownLoadTime` datetime,
  `ServerIp` varchar(128),
  `FsuIp` varchar(128),
  `DownLoadPath` varchar(128),
  `FileName` varchar(128),
  `FileSize` varchar(128),
  PRIMARY KEY (ServerIp,FsuIp,DownLoadTime)
);

CREATE TABLE `tbl_icsfsumuequip` (
  `CollectTime` datetime NOT NULL,
  `SN` varchar(128),
  `Mac` varchar(128),
  `EquipName` varchar(128),
  `EquipId` int,
  `StdEquipId` int,
  `StdEquipName` varchar(128),
  `EquipAddr` varchar(128),
  `EquipPhoneno` varchar(128),
  `DllPath` varchar(128),
  `PortNo` int,
  `PortType` int,
  `Setting` varchar(128),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsumumodel` (
  `CollectTime` datetime NOT NULL,
  `SN` varchar(128),
  `Mac` varchar(128),
  `SoFileName` varchar(128),
  `SoVersion` varchar(128),
  `IsBInterface` varchar(20),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsupsinfo` (
  `CollectTime` datetime NOT NULL,
  `SN` varchar(128),
  `Mac` varchar(128),
  `PsName` varchar(128),
  `PsVersion` varchar(128),
  `PsCount` int,
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsusoinfo` (
  `CollectTime` datetime NOT NULL,
  `Ip` varchar(128) ,
  `SN` varchar(128),
  `Mac` varchar(128),
  `FileName` varchar(128),
  `SoVersion` varchar(128),
  `SoCode` varchar(128),
  `IsUsed` varchar(20),
  `IsLoaded` varchar(20),
  `GccInfo` varchar(128),
  `Md5` varchar(128),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsfsuudpinfo` (
  `CollectTime` datetime NOT NULL,
  `SN` varchar(128),
  `Mac` varchar(128),
  `FsuHost` varchar(128),
  `Ip` varchar(128),
  `NetMask` varchar(20),
  `GateWay` varchar(20),
  `SiteName` varchar(128),
  `Version` varchar(128),
  `ServerIp` varchar(128),
  PRIMARY KEY (SN, Mac, CollectTime)
);

CREATE TABLE `tbl_icsplatformqrcode` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `EquipmentTypeId` int,
  `SerialNumber` varchar(128),
  `EquipmentName` varchar(128),
  `CreateDate` datetime,
  `OperatorName` varchar(128),
  PRIMARY KEY (`Id`)
);

CREATE TABLE `tbl_icsscriptdatainfo` (
  `CollectTime` datetime NOT NULL,
  `ServerIp` varchar(128),
  `Module` varchar(255) NOT NULL,
  `ScriptVersion` varchar(30),
  `CompileDate` datetime,
  `CreateDate` datetime,
  `Feature` varchar(255) NOT NULL,
  PRIMARY KEY (ServerIp, CollectTime)
);

CREATE TABLE `tbl_icsserverinfo` (
  `CollectTime` datetime NOT NULL,
  `ServerIp` varchar(128),
  `CpuUsedRate` double,
  `MemoryUsedRate` double,
  `CdriveUsedRate` double,
  `DbServerIp` varchar(128),
  `DbInstallPath` varchar(128),
  `DbDriveUsedRate` double,
  PRIMARY KEY (ServerIp,CollectTime)
);

CREATE TABLE `tbl_icssitewebinfo` (
  `CollectTime` datetime NOT NULL,
  `SiteWebName` varchar(128),
  `SiteWebVersion` varchar(128),
  `CompileDate` datetime,
  `UsedDate` datetime,
  `ServerName` varchar(128),
  `ServerIp` varchar(128),
  `OperateSystem` varchar(128),
  `CpuConfig` varchar(255),
  `MemorySize` varchar(128),
  `DiskDrive` varchar(128),
  `InstallPath` varchar(128),
  `DiskUsedRate` double,
  PRIMARY KEY (SiteWebName,ServerIp,CollectTime)
);