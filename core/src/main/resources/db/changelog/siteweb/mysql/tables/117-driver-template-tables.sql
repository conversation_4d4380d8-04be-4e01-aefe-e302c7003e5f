CREATE TABLE `tbl_drivestructuretemplate` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `filePath` varchar(100) DEFAULT NULL COMMENT '结构路径',
  `pid` int DEFAULT NULL COMMENT '上级结构id',
  `fileId` bigint DEFAULT NULL COMMENT '模板文件id',
  `isDisk` tinyint(1) DEFAULT NULL COMMENT '是否是目录（默认是）',
  `isUpload` tinyint(1) DEFAULT NULL COMMENT '是否需要上传',
  `isFill` tinyint(1) DEFAULT NULL COMMENT '是否需要填充',
  `driveTemplateId` int DEFAULT NULL COMMENT '驱动模板id',
  `isLeaf` tinyint(1) DEFAULT NULL COMMENT '是否是叶子节点',
  `uploadTiming` tinyint(1) DEFAULT NULL COMMENT '上传时机 0-模板创建时 1-生成配置时',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `tbl_drivetemplate` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `driveTemplateName` varchar(100) DEFAULT NULL COMMENT '驱动模板名称',
  `driverTemplateDescribe` varchar(255) DEFAULT NULL COMMENT '驱动模板描述',
  `isDefaultTemplate` tinyint(1) DEFAULT NULL COMMENT '是否是默认模板(当前模板类型下)',
  `driveTemplateType` int DEFAULT NULL COMMENT '驱动模板类型',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `tbl_equipmentext` (
  `equipmentId` int NOT NULL COMMENT '设备主键id',
  `driveTemplateId` int DEFAULT NULL COMMENT '引用驱动模板id',
  `fileId` bigint DEFAULT NULL COMMENT '文件id',
  `isUpload` tinyint DEFAULT NULL COMMENT '是否已经上传',
  `isReset` tinyint DEFAULT NULL COMMENT '是否重新生成',
  `fieldHash` int DEFAULT NULL COMMENT '设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值(主要用于判断客户端配置工具是否对上述字段进行过修改)',
  PRIMARY KEY (`equipmentId`),
  UNIQUE KEY `equipmentId` (`equipmentId`)
);