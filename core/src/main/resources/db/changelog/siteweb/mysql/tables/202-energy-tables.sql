CREATE TABLE `energy_businessdefinitionmap` (
  `MapId` int NOT NULL AUTO_INCREMENT,
  `BusinessTypeId` int NOT NULL,
  `ComplexIndexDefinitionId` int NOT NULL,
  `ComplexIndexDefinitionTypeId` int NOT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`MapId`),
  UNIQUE KEY `MapId` (`MapId`)
);

CREATE TABLE `energy_carbonemissionmanage` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ObjectTypeId` varchar(45) NOT NULL,
  `ObjectId` varchar(45) NOT NULL,
  `Year` int DEFAULT NULL,
  `Month` int DEFAULT NULL,
  `PlanValue` float DEFAULT NULL,
  `YearPlanTotalValue` float DEFAULT NULL,
  `isArea` int NOT NULL,
  `area` float DEFAULT NULL,
  `units` varchar(45) NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_carbonmanage` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ObjectTypeId` varchar(45) NOT NULL,
  `ObjectId` varchar(45) NOT NULL,
  `Year` int NOT NULL,
  `Month` int NOT NULL,
  `PlanValue` float NOT NULL,
  `YearPlanTotalValue` float NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_complexindexclassificationmap` (
  `ResourceStructureId` int NOT NULL,
  `ComplexIndexId` int NOT NULL,
  `ClassificationId` int NOT NULL,
  `ExtendField1` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ComplexIndexId`),
  UNIQUE KEY `ComplexIndexId` (`ComplexIndexId`)
);

CREATE TABLE `energy_consumeconst` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ObjectId` varchar(45) NOT NULL,
  `ObjectTypeId` varchar(45) NOT NULL,
  `Peoples` int NOT NULL,
  `Area` float DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_consumedata` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ObjectId` varchar(45) NOT NULL,
  `ObjectTypeId` varchar(45) NOT NULL,
  `EnergyTypeId` int NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `EnergySavingValue` float DEFAULT NULL,
  `PlanValue` float DEFAULT NULL,
  `OverstepValue` float DEFAULT NULL,
  `PlanValuePreAlarm` float DEFAULT NULL,
  `OverstepValuePreAlarm` float DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_customer_config` (
  `Id` int NOT NULL,
  `CustomerName` varchar(256) DEFAULT NULL,
  `Enable` tinyint DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `Notes` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_customer_tb_report` (
  `ReportId` int NOT NULL,
  `ReportName` varchar(256) DEFAULT NULL,
  `Path` varchar(1000) DEFAULT NULL,
  `RecordPath` varchar(1000) DEFAULT NULL,
  `Notes` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`ReportId`),
  UNIQUE KEY `ReportId` (`ReportId`)
);

CREATE TABLE `energy_customer_tb_reportmap` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReportId` int DEFAULT NULL,
  `SheetId` int DEFAULT NULL,
  `RowId` int DEFAULT NULL,
  `CellId` int DEFAULT NULL,
  `Hour` int DEFAULT NULL,
  `EquipmentId` varchar(128) DEFAULT NULL,
  `SignalId` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_customer_tb_reportrecord` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReportId` int DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `ReportName` varchar(256) DEFAULT NULL,
  `FilePath` varchar(1000) DEFAULT NULL,
  `Notes` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_dataentry` (
  `EntryId` int NOT NULL AUTO_INCREMENT,
  `EntryName` varchar(128) DEFAULT NULL,
  `EntryAlias` varchar(255) DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `CanEdit` bit(1) DEFAULT NULL,
  `CanDelete` bit(1) DEFAULT NULL,
  PRIMARY KEY (`EntryId`),
  UNIQUE KEY `EntryId` (`EntryId`)
);

CREATE TABLE `energy_dataitem` (
  `EntryItemId` int NOT NULL AUTO_INCREMENT,
  `ParentEntryId` int NOT NULL,
  `EntryId` int NOT NULL,
  `ItemId` int NOT NULL,
  `ItemValue` varchar(128) DEFAULT NULL,
  `ItemAlias` varchar(255) DEFAULT NULL,
  `IsSystem` tinyint DEFAULT NULL,
  `IsDefault` tinyint DEFAULT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  `ExtendField5` varchar(255) DEFAULT NULL,
  `CanEdit` tinyint DEFAULT NULL,
  `CanDelete` tinyint DEFAULT NULL,
  `canTimeliness` tinyint DEFAULT NULL,
  PRIMARY KEY (`EntryItemId`),
  UNIQUE KEY `EntryItemId` (`EntryItemId`)
);

CREATE TABLE `energy_dataitemtimeliness` (
  `ItemTimelinessId` int NOT NULL AUTO_INCREMENT,
  `EntryItemId` int NOT NULL,
  `ItemValue` varchar(128) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL,
  `ExtendField4` varchar(255) DEFAULT NULL,
  `ExtendField5` varchar(255) DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  PRIMARY KEY (`ItemTimelinessId`),
  UNIQUE KEY `Energy_DataItemtimeliness_unique` (`EntryItemId`,`StartTime`)
);

CREATE TABLE `energy_dimensiontype` (
  `DimensionTypeId` int NOT NULL AUTO_INCREMENT,
  `DimensionTypeName` varchar(255) NOT NULL,
  `IsUsed` tinyint DEFAULT '1',
  `OperatorUserId` int NOT NULL,
  `UpdateTime` datetime NOT NULL,
  `Notes` varchar(1000) NOT NULL,
  PRIMARY KEY (`DimensionTypeId`),
  UNIQUE KEY `DimensionTypeId` (`DimensionTypeId`)
);

CREATE TABLE `energy_elecfeeconfigoperatelog` (
  `LogId` int NOT NULL AUTO_INCREMENT,
  `Operator` varchar(128) DEFAULT NULL,
  `OperatorId` int DEFAULT NULL,
  `UpdateDate` datetime NOT NULL,
  `OperationContent` varchar(4000) NOT NULL,
  `ChangeContent` mediumtext,
  `ExtendField1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`LogId`),
  UNIQUE KEY `LogId` (`LogId`)
);

CREATE TABLE `energy_elecfeefpg` (
  `FpgId` int NOT NULL AUTO_INCREMENT,
  `SchemeId` int NOT NULL,
  `EffectiveStart` datetime NOT NULL,
  `EffectiveEnd` datetime NOT NULL,
  `CreateDate` datetime NOT NULL,
  `UpdateDate` datetime NOT NULL,
  `CreaterId` int NOT NULL,
  `CreaterName` varchar(256) DEFAULT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(256) DEFAULT NULL,
  `FpgDescKey` int DEFAULT NULL,
  `FpgDesc` varchar(256) DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`FpgId`),
  UNIQUE KEY `FpgId` (`FpgId`)
);

CREATE TABLE `energy_elecfeefpgvalue` (
  `FpgValueId` int NOT NULL AUTO_INCREMENT,
  `FpgId` int NOT NULL,
  `StepPriceId` int NOT NULL,
  `SchemeId` int NOT NULL,
  `FpgValue` varchar(64) NOT NULL,
  `CreateDate` datetime NOT NULL,
  `UpdateDate` datetime NOT NULL,
  `CreaterId` int NOT NULL,
  `CreaterName` varchar(256) DEFAULT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(256) DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`FpgValueId`),
  UNIQUE KEY `FpgValueId` (`FpgValueId`)
);

CREATE TABLE `energy_elecfeescheme` (
  `SchemeId` int NOT NULL AUTO_INCREMENT,
  `SchemeName` varchar(512) DEFAULT NULL,
  `AppliedRange` varchar(1024) DEFAULT NULL,
  `EnableDate` datetime DEFAULT NULL,
  `DeactivateDate` datetime DEFAULT NULL,
  `StartMonth` int DEFAULT NULL,
  `EndMonth` int DEFAULT NULL,
  `EnableStatus` int NOT NULL,
  `CreateDate` datetime NOT NULL,
  `UpdateDate` datetime NOT NULL,
  `CreaterId` int NOT NULL,
  `CreaterName` varchar(256) DEFAULT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(256) DEFAULT NULL,
  `BusinessTypeId` int DEFAULT NULL,
  `BusinessTypeName` varchar(128) DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  `ExtendField2` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`SchemeId`),
  UNIQUE KEY `SchemeId` (`SchemeId`)
);

CREATE TABLE `energy_elecfeeschemestructuremap` (
  `MapId` int NOT NULL AUTO_INCREMENT,
  `SchemeId` int NOT NULL,
  `ResourceStructureId` int NOT NULL,
  `ResourceStructureName` varchar(256) DEFAULT NULL,
  `CreateDate` datetime NOT NULL,
  `UpdateDate` datetime NOT NULL,
  `CreaterId` int NOT NULL,
  `CreaterName` varchar(256) DEFAULT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(256) DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`MapId`),
  UNIQUE KEY `MapId` (`MapId`)
);

CREATE TABLE `energy_elecfeestepprice` (
  `StepPriceId` int NOT NULL AUTO_INCREMENT,
  `SchemeId` int NOT NULL,
  `StepName` varchar(256) NOT NULL,
  `UpperLimit` int NOT NULL,
  `CreateDate` datetime NOT NULL,
  `UpdateDate` datetime NOT NULL,
  `CreaterId` int NOT NULL,
  `CreaterName` varchar(256) DEFAULT NULL,
  `UpdaterId` int NOT NULL,
  `UpdaterName` varchar(256) DEFAULT NULL,
  `AsMaxStep` int DEFAULT NULL,
  `ExtendField1` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`StepPriceId`),
  UNIQUE KEY `StepPriceId` (`StepPriceId`)
);

CREATE TABLE `energy_errordatamodifyrecord` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ComplexIndexId` int NOT NULL,
  `ErrorTime` datetime NOT NULL,
  `ModifyTime` datetime NOT NULL,
  `UserId` int NOT NULL,
  `UserName` varchar(255) NOT NULL,
  `OriginalValue` double NOT NULL,
  `IndexValue` double NOT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_management` (
  `EnergyId` int NOT NULL AUTO_INCREMENT,
  `EnergyName` varchar(128) NOT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  `Operator` varchar(128) DEFAULT NULL,
  `Contact` varchar(128) DEFAULT NULL,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime NOT NULL,
  `Notes` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`EnergyId`),
  UNIQUE KEY `EnergyId` (`EnergyId`)
);

CREATE TABLE `energy_managementmap` (
  `MapId` int NOT NULL AUTO_INCREMENT,
  `EnergyId` int NOT NULL,
  `ResourceStructureId` int NOT NULL,
  `IsMarker` int NOT NULL,
  `Property` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`MapId`),
  UNIQUE KEY `MapId` (`MapId`),
  KEY `Energy_ManagementMap_IDX1` (`EnergyId`,`ResourceStructureId`)
);

CREATE TABLE `energy_manualrecord` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ComplexIndexId` int NOT NULL,
  `RecordTime` datetime NOT NULL,
  `RecordValue` float NOT NULL,
  `CalcType` int NOT NULL,
  `UserId` int NOT NULL,
  `UserName` varchar(255) NOT NULL,
  `InsertTime` datetime NOT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `Unit` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_multilevelobject` (
  `ObjectId` int NOT NULL,
  `ObjectName` varchar(512) DEFAULT NULL,
  `ParentObjectId` int DEFAULT NULL,
  `LevelNum` int DEFAULT NULL,
  PRIMARY KEY (`ObjectId`),
  UNIQUE KEY `ObjectId` (`ObjectId`)
);

CREATE TABLE `energy_objectmap` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `DimensionTypeId` int NOT NULL,
  `LevelId` int NOT NULL,
  `ObjectId` int NOT NULL,
  `ObjectIdType` int NOT NULL,
  `ParentObjectId` int NOT NULL,
  `ParentObjectIdType` int NOT NULL,
  `LevelOfPath` varchar(1024) NOT NULL,
  `Notes` varchar(1000) NOT NULL,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Id` (`Id`)
);

CREATE TABLE `energy_ratingconfig` (
  `RatingConfigId` int NOT NULL AUTO_INCREMENT,
  `OutDryTempParam` varchar(255) DEFAULT NULL,
  `OutWetTempParam` varchar(255) DEFAULT NULL,
  `InDryTempParam` varchar(255) DEFAULT NULL,
  `InWetTempParam` varchar(255) DEFAULT NULL,
  `RunningLoadParam` varchar(255) DEFAULT NULL,
  `ITPowerParam` varchar(255) DEFAULT NULL,
  `TotalPowerParam` varchar(255) DEFAULT NULL,
  `ObjectId` int NOT NULL,
  `Name` varchar(255) NOT NULL,
  `IntervalSecond` int NOT NULL,
  `Status` int NOT NULL DEFAULT '0',
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `WorkingCondition` varchar(255) DEFAULT NULL,
  `UserId` int NOT NULL,
  `CityId` int NOT NULL,
  PRIMARY KEY (`RatingConfigId`),
  UNIQUE KEY `RatingConfigId` (`RatingConfigId`)
);

CREATE TABLE `energy_ratingdata` (
  `RatingDataId` int NOT NULL AUTO_INCREMENT,
  `RatingConfigId` int NOT NULL,
  `SampleTime` datetime NOT NULL,
  `IntervalSecond` int NOT NULL,
  `WorkingCondition` varchar(255) DEFAULT NULL,
  `OutDryTemp` double DEFAULT NULL,
  `OutWetTemp` double DEFAULT NULL,
  `InDryTemp` double DEFAULT NULL,
  `InWetTemp` double DEFAULT NULL,
  `RunningLoad` double DEFAULT NULL,
  `ITPower` double DEFAULT NULL,
  `TotalPower` double DEFAULT NULL,
  PRIMARY KEY (`RatingDataId`),
  UNIQUE KEY `RatingDataId` (`RatingDataId`),
  KEY `energy_ratingdata_idx1` (`RatingConfigId`,`SampleTime`,`WorkingCondition`)
);

CREATE TABLE `energy_ratingdatahistory` (
  `RatingDataId` int NOT NULL AUTO_INCREMENT,
  `RatingConfigId` int NOT NULL,
  `SampleTime` datetime NOT NULL,
  `IntervalSecond` int NOT NULL,
  `WorkingCondition` varchar(255) NOT NULL,
  `OutDryTemp` double NOT NULL,
  `OutWetTemp` double NOT NULL,
  `InDryTemp` double NOT NULL,
  `InWetTemp` double NOT NULL,
  `RunningLoad` double NOT NULL,
  `ITPower` double NOT NULL,
  `TotalPower` double NOT NULL,
  `Times` int NOT NULL DEFAULT '1',
  `DeleteDate` datetime NOT NULL,
  PRIMARY KEY (`RatingDataId`),
  UNIQUE KEY `RatingDataId` (`RatingDataId`)
);

CREATE TABLE `energy_structure` (
  `StructureId` int NOT NULL AUTO_INCREMENT,
  `StructureName` varchar(255) NOT NULL,
  `SourceCategory` int NOT NULL,
  `Notes` varchar(1000) NOT NULL,
  `AsRootInflowNode` int DEFAULT NULL,
  PRIMARY KEY (`StructureId`),
  UNIQUE KEY `StructureId` (`StructureId`)
);