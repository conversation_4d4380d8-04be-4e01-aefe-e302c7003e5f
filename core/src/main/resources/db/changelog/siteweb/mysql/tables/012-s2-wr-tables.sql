CREATE TABLE `wr_dataentry` (
  `EntryId` int NOT NULL,
  `EntryCategory` int DEFAULT NULL,
  `EntryName` varchar(128) DEFAULT NULL,
  `EntryTitle` varchar(128) DEFAULT NULL,
  `<PERSON><PERSON><PERSON>s` varchar(255) DEFAULT NULL,
  `Enable` bit(1) NOT NULL DEFAULT b'1',
  `Description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`EntryId`)
);

CREATE TABLE `wr_dataitem` (
  `EntryItemId` int NOT NULL AUTO_INCREMENT,
  `EntryId` int NOT NULL,
  `ItemId` int NOT NULL,
  `ParentEntryId` int DEFAULT NULL,
  `ParentItemId` int DEFAULT NULL,
  `ItemValue` varchar(255) NOT NULL,
  `LastUpdateDate` datetime DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT '',
  `ExtendField2` varchar(255) DEFAULT '',
  `ExtendField3` varchar(255) DEFAULT '',
  PRIMARY KEY (`EntryItemId`)
);

CREATE TABLE `wr_devicemanagement` (
  `WRDeviceId` int NOT NULL AUTO_INCREMENT,
  `WRStationId` int DEFAULT NULL,
  `WRHouseId` int DEFAULT NULL,
  `WRFsuId` int DEFAULT NULL,
  `DeviceType` varchar(10) NOT NULL,
  `DeviceCode` varchar(20) NOT NULL,
  `DeviceRId` varchar(128) DEFAULT NULL,
  `DeviceName` varchar(128) DEFAULT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `SWStationId` int DEFAULT NULL,
  `SWHouseId` int DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`WRDeviceId`)
);

CREATE TABLE `wr_devicemanagementcucc` (
  `WRDeviceId` int NOT NULL AUTO_INCREMENT,
  `WRStationId` int DEFAULT NULL,
  `WRHouseId` int DEFAULT NULL,
  `WRFsuId` int DEFAULT NULL,
  `DeviceType` varchar(10) NOT NULL,
  `DeviceCode` varchar(20) NOT NULL,
  `DeviceRId` varchar(128) DEFAULT NULL,
  `DeviceName` varchar(128) DEFAULT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `SWStationId` int DEFAULT NULL,
  `SWHouseId` int DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`WRDeviceId`),
  KEY `IDX_EquipmentId_CUCCManage` (`WRFsuId`,`DeviceCode`,`SWStationId`)
);

CREATE TABLE `wr_fsumanagement` (
  `WRFsuId` int NOT NULL AUTO_INCREMENT,
  `WRHouseId` int NOT NULL,
  `FsuCode` varchar(20) NOT NULL,
  `FsuName` varchar(255) DEFAULT NULL,
  `IPAddress` varchar(255) DEFAULT NULL,
  `ManufacturerId` int DEFAULT NULL,
  `FsuStatus` int NOT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `UserName` varchar(40) DEFAULT NULL,
  `Password` varchar(40) DEFAULT NULL,
  `FtpUserName` varchar(40) DEFAULT NULL,
  `FtpPassword` varchar(40) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `ApproveTime` datetime DEFAULT NULL,
  `RejectCause` varchar(255) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `SWMonitorUnitId` int DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`WRFsuId`)
);

CREATE TABLE `wr_fsumanagementcucc` (
  `WRFsuId` int NOT NULL AUTO_INCREMENT,
  `WRHouseId` int NOT NULL,
  `FsuCode` varchar(20) NOT NULL,
  `FsuName` varchar(255) DEFAULT NULL,
  `IPAddress` varchar(255) DEFAULT NULL,
  `ManufacturerId` int DEFAULT NULL,
  `FsuStatus` int NOT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `UserName` varchar(40) DEFAULT NULL,
  `Password` varchar(40) DEFAULT NULL,
  `FtpUserName` varchar(40) DEFAULT NULL,
  `FtpPassword` varchar(40) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `ApproveTime` datetime DEFAULT NULL,
  `RejectCause` varchar(255) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `SWMonitorUnitId` int DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `FsuRId` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`WRFsuId`)
);

CREATE TABLE `wr_housemanagement` (
  `WRHouseId` int NOT NULL AUTO_INCREMENT,
  `WRStationId` int NOT NULL,
  `HouseCode` varchar(20) NOT NULL,
  `HouseName` varchar(128) DEFAULT NULL,
  `HouseStatus` int NOT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `ApproveTime` datetime DEFAULT NULL,
  `RejectCause` varchar(255) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `SWHouseId` int DEFAULT NULL,
  PRIMARY KEY (`WRHouseId`)
);

CREATE TABLE `wr_housemanagementcucc` (
  `WRHouseId` int NOT NULL AUTO_INCREMENT,
  `WRStationId` int NOT NULL,
  `HouseCode` varchar(20) NOT NULL,
  `HouseName` varchar(128) DEFAULT NULL,
  `HouseStatus` int NOT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `ApproveTime` datetime DEFAULT NULL,
  `RejectCause` varchar(255) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `SWHouseId` int DEFAULT NULL,
  `HouseRId` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`WRHouseId`)
);

CREATE TABLE `wr_operationrecord` (
  `OperationRecordId` int NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `WRStationId` int NOT NULL,
  `WRStationName` varchar(255) DEFAULT NULL,
  `OpCategory` int NOT NULL,
  `OpItemId` int NOT NULL,
  `OpItemValue` varchar(255) NOT NULL,
  `OpDateTime` datetime DEFAULT NULL,
  `UpdateString` text,
  `LastString` text,
  `OpUserId` int DEFAULT NULL,
  `ExtendField1` varchar(255) DEFAULT NULL,
  `ExtendField2` varchar(255) DEFAULT NULL,
  `ExtendField3` varchar(255) DEFAULT NULL
);

CREATE TABLE `wr_stationcode` (
  `CountyId` int NOT NULL,
  `MinValue` int NOT NULL,
  `CurrentValue` int NOT NULL,
  PRIMARY KEY (`CountyId`)
);

CREATE TABLE `wr_stationmanagement` (
  `WRStationId` int NOT NULL AUTO_INCREMENT,
  `StructureId` int NOT NULL,
  `StationCode` varchar(20) NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `StationCategory` int NOT NULL,
  `StationStatus` int NOT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `ApproveTime` datetime DEFAULT NULL,
  `Province` int DEFAULT NULL,
  `City` int DEFAULT NULL,
  `County` int DEFAULT NULL,
  `RejectCause` varchar(255) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `SWStationId` int DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`WRStationId`),
  KEY `Idx_WR_StationManagement_1` (`ApplyTime`,`StructureId`,`StationCategory`,`StationName`,`StationCode`,`StationStatus`)
);

CREATE TABLE `wr_stationmanagementcucc` (
  `WRStationId` int NOT NULL AUTO_INCREMENT,
  `StructureId` int NOT NULL,
  `StationCode` varchar(20) NOT NULL,
  `StationName` varchar(255) NOT NULL,
  `StationCategory` int NOT NULL,
  `StationStatus` int NOT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `UserId` int NOT NULL,
  `SWUserName` varchar(128) DEFAULT NULL,
  `ApplyTime` datetime DEFAULT NULL,
  `ApproveTime` datetime DEFAULT NULL,
  `Province` int DEFAULT NULL,
  `City` int DEFAULT NULL,
  `County` int DEFAULT NULL,
  `RejectCause` varchar(255) DEFAULT NULL,
  `Remark` varchar(255) DEFAULT NULL,
  `SWStationId` int DEFAULT NULL,
  `ContractNo` varchar(255) DEFAULT NULL,
  `ProjectName` varchar(255) DEFAULT NULL,
  `StationRId` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`WRStationId`),
  KEY `Idx_WR_StationManagementCUCC_1` (`ApplyTime`,`StructureId`,`StationCategory`,`StationName`,`StationCode`,`StationStatus`)
);
CREATE TABLE `wr_syncinfo` (
  `AutoId` int NOT NULL AUTO_INCREMENT,
  `StationId` int DEFAULT NULL,
  `HouseId` int DEFAULT NULL,
  `MonitorUnitId` int DEFAULT NULL,
  `SyncType` int NOT NULL,
  `SyncFlag` int NOT NULL DEFAULT 0,
  `Remark` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`AutoId`),
  KEY `Idx_WR_SyncInfo_SyncFlag` (`SyncFlag`)
);
