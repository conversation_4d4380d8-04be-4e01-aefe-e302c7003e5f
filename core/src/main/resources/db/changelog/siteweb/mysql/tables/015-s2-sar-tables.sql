CREATE TABLE `tbl_saralarmactiverecord` (
  `StationId` int NOT NULL,
  `StationCategoryId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `SequenceId` varchar(128) NOT NULL PRIMARY KEY,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Overturn` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `StandardId` int DEFAULT NULL,
  `InsertDateTime` datetime NOT NULL,
  `RelationType` int NOT NULL
);

CREATE TABLE `tbl_saralarmqueue` (
  `StationId` int NOT NULL,
  `StationCategoryId` int NOT NULL,
  `EquipmentId` int NOT NULL,
  `EventId` int NOT NULL,
  `EventConditionId` int NOT NULL,
  `SequenceId` varchar(128) NOT NULL PRIMARY KEY,
  `StartTime` datetime NOT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Overturn` int DEFAULT NULL,
  `Meanings` varchar(255) DEFAULT NULL,
  `EventValue` double DEFAULT NULL,
  `EndValue` double DEFAULT NULL,
  `BaseTypeId` decimal(12,0) DEFAULT NULL,
  `StandardId` int DEFAULT NULL,
  `InsertDateTime` datetime NOT NULL
);

CREATE TABLE `tbl_saralarmrecordstarttime` (
  `SarAlarmRecordStartTimeId` int PRIMARY KEY AUTO_INCREMENT,
  `StartTime` datetime DEFAULT NULL,
  `RelationType` int DEFAULT NULL
);

CREATE TABLE `tbl_saralarmrelation` (
  `SarAlarmRecordRelationId` int PRIMARY KEY AUTO_INCREMENT,
  `StationId` int DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EventId` int DEFAULT NULL,
  `EventConditionId` int DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `StandardId` int DEFAULT NULL,
  `CauseStationId` int DEFAULT NULL,
  `CauseEquipmentId` int DEFAULT NULL,
  `CauseEventId` int DEFAULT NULL,
  `CauseEventConditionId` int DEFAULT NULL,
  `CauseStartTime` datetime DEFAULT NULL,
  `CauseStandardId` int DEFAULT NULL,
  `RelationType` int DEFAULT NULL
);

CREATE TABLE `tbl_sarderivatealarmrule` (
  `SarDerivateAlarmRuleId` int PRIMARY KEY AUTO_INCREMENT,
  `RuleId` int NOT NULL,
  `RuleName` varchar(255) NOT NULL,
  `AlarmCount` int NOT NULL,
  `AlarmEndCount` int NOT NULL,
  `AlarmTimeScope` int NOT NULL,
  `Description` varchar(255) DEFAULT NULL
);

CREATE TABLE `tbl_sarisprocess` (
  `SarIsProcessId` int PRIMARY KEY AUTO_INCREMENT,
  `IsProcess` int DEFAULT NULL
);

CREATE TABLE `tbl_sarreversealarmrule` (
  `StartThreshold` int NOT NULL,
  `EndThreshold` int NOT NULL,
  `TimeThreshold` int NOT NULL,
  PRIMARY KEY (StartThreshold,EndThreshold,TimeThreshold)
);

CREATE TABLE `tbl_sarsecondaryalarmbyfilter` (
  `SarSecondaryAlarmByFilter` int PRIMARY KEY AUTO_INCREMENT,
  `SeconarySequenceId` text,
  `StationId` int DEFAULT NULL,
  `EquipmentId` int DEFAULT NULL,
  `EventId` int DEFAULT NULL,
  `EventConditionId` int DEFAULT NULL,
  `StartTime` datetime DEFAULT NULL,
  `PrimarySequenceId` text,
  `InsertDateTime` datetime DEFAULT NULL
);

CREATE TABLE `tbl_sarstationderivatemap` (
  `RuleId` int NOT NULL PRIMARY KEY,
  `DerivateBaseTypeId` int NOT NULL,
  `BaseTypeId` int NOT NULL,
  `StationCategoryId` int NOT NULL
);

CREATE TABLE `tbl_sarstationprimarymap` (
  `PrimaryId` int NOT NULL,
  `StationCategoryId` int NOT NULL,
  PRIMARY KEY (`PrimaryId`,`StationCategoryId`)
);