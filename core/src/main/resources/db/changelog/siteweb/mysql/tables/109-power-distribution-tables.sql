CREATE TABLE `pd_prealarmreportrecord` (
  `RecordId` int NOT NULL AUTO_INCREMENT,
  `PreAlarmId` int NOT NULL,
  `PreAlarmPointId` int DEFAULT NULL,
  `PreAlarmSeverity` int DEFAULT NULL,
  `PreAlarmCategory` int DEFAULT NULL,
  `ObjectId` int DEFAULT NULL,
  `ObjectTypeId` int DEFAULT NULL,
  `ResourceStructureId` int DEFAULT NULL,
  `TriggerValue` varchar(128) DEFAULT NULL,
  `ReportPath` varchar(512) DEFAULT NULL,
  `ReportFileName` varchar(255) DEFAULT NULL,
  `HistoryDataPath` varchar(512) DEFAULT NULL,
  `HistoryDataFileName` varchar(255) DEFAULT NULL,
  `InsertTime` datetime DEFAULT NULL,
  `GenerateTime` datetime DEFAULT NULL,
  `GenerateResult` int DEFAULT NULL COMMENT '生成结果：0-等待生成中；1-生成成功；2-生成失败；3-已删除；-1及其他-未知状态',
  `DeleteTime` datetime DEFAULT NULL,
  `ExtendField` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`RecordId`),
  UNIQUE KEY `RecordId` (`RecordId`),
  UNIQUE KEY `IDX_PD_PrealarmReportRecord_1` (`PreAlarmId`)
);

CREATE TABLE `powersimulationrecord` (
  `recordId` int NOT NULL AUTO_INCREMENT,
  `recordName` varchar(225) NOT NULL,
  `diagramId` int NOT NULL,
  `nodeId` varchar(225) NOT NULL,
  `onOffState` int NOT NULL,
  `downloadPath` varchar(225) NOT NULL,
  `attributeObjects` text NOT NULL,
  `reportName` varchar(225) NOT NULL,
  PRIMARY KEY (`recordId`),
  UNIQUE KEY `recordId` (`recordId`)
);