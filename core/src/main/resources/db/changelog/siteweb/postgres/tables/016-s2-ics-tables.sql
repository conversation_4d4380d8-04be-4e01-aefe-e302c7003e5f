CREATE TABLE tbl_icsarchivetask
(
    taskid        integer,
    taskname      character varying(128),
    procedurename character varying(128),
    filename      character varying(128)
);
ALTER TABLE ONLY tbl_icsarchivetask ADD CONSTRAINT tbl_icsarchivetask_taskid PRIMARY KEY (taskid);

CREATE TABLE tbl_icscontractinstall
(
    contractno        character varying(128),
    projectname       character varying(128),
    primarydate       timestamp,
    enddate           timestamp,
    qualitystartpoint character varying(128),
    qualityperiod     integer,
    qualityterms      character varying(128),
    stationcount      integer,
    fsucount          integer,
    equipmentcount    integer
);
ALTER TABLE ONLY tbl_icscontractinstall ADD CONSTRAINT tbl_icscontractinstall_contractno PRIMARY KEY (contractno);

CREATE TABLE tbl_icscontractmaintenance
(
    id               serial NOT NULL PRIMARY KEY,
    contractno       character varying(128),
    projectname      character varying(128),
    startdate        timestamp,
    enddate          timestamp,
    maintenanceterms character varying(128)
);

CREATE TABLE tbl_icsequipmenttype
(
    equipmenttypeid   integer,
    equipmenttypename character varying(128),
    iscaninputid      integer
);
ALTER TABLE ONLY tbl_icsequipmenttype ADD CONSTRAINT tbl_icsequipmenttype_equipmenttypeid PRIMARY KEY (equipmenttypeid);

CREATE TABLE tbl_icsfsudatainfo
(
    collecttime     timestamp NOT NULL,
    sampletime      timestamp,
    sn              character varying(128),
    mac             character varying(128),
    ip              character varying(128),
    fsutype         character varying(128),
    sitename        character varying(128),
    siteversion     character varying(128),
    sitecompiletime timestamp,
    hw              character varying(128),
    filesystem      character varying(128),
    linux           character varying(128),
    cpuusage        double precision,
    memtotal        double precision,
    memfree         double precision,
    memusage        double precision,
    uptime          double precision,
    filenr          character varying(128),
    mufilename      character varying(128),
    mumodifytime    timestamp,
    mufilesize      integer,
    backupmd5       character varying(128)
);
ALTER TABLE ONLY tbl_icsfsudatainfo ADD CONSTRAINT tbl_icsfsudatainfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsudatanewinfo
(
    collecttime   timestamp NOT NULL,
    sitename      character varying(128),
    fsutype       character varying(128),
    hw            character varying(128),
    sn            character varying(128),
    mac           character varying(128),
    ip            character varying(128),
    memtotal      double precision,
    flashsize     character varying(60),
    linux         character varying(128),
    siteversion   character varying(128),
    cpuusage      double precision,
    memusage      double precision,
    flashusedrate character varying(60)
);
ALTER TABLE ONLY tbl_icsfsudatanewinfo ADD CONSTRAINT tbl_icsfsudatanewinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsuflashdf
(
    collecttime    timestamp NOT NULL,
    sn             character varying(128),
    mac            character varying(128),
    flashname      character varying(128),
    filesystem     character varying(128),
    flashsize      character varying(60),
    flashused      character varying(60),
    flashavailable character varying(60),
    flashusedrate  character varying(60)
);
ALTER TABLE ONLY tbl_icsfsuflashdf ADD CONSTRAINT tbl_icsfsuflashdf_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsuftpdownloadinfo
(
    downloadtime timestamp,
    serverip     character varying(128),
    fsuip        character varying(128),
    downloadpath character varying(128),
    filename     character varying(128),
    filesize     character varying(128)
);
ALTER TABLE ONLY tbl_icsfsuftpdownloadinfo ADD CONSTRAINT tbl_icsfsuftpdownloadinfo_serverip_fsuip_downloadpath PRIMARY KEY (serverip,fsuip,downloadpath);

CREATE TABLE tbl_icsfsumuequip
(
    collecttime  timestamp NOT NULL,
    sn           character varying(128),
    mac          character varying(128),
    equipname    character varying(128),
    equipid      integer,
    stdequipid   integer,
    stdequipname character varying(128),
    equipaddr    character varying(128),
    equipphoneno character varying(128),
    dllpath      character varying(128),
    portno       integer,
    porttype     integer,
    setting      character varying(128)
);
ALTER TABLE ONLY tbl_icsfsumuequip ADD CONSTRAINT tbl_icsfsumuequip_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsumumodel
(
    collecttime  timestamp NOT NULL,
    sn           character varying(128),
    mac          character varying(128),
    sofilename   character varying(128),
    soversion    character varying(128),
    isbinterface character varying(20)
);
ALTER TABLE ONLY tbl_icsfsumumodel ADD CONSTRAINT tbl_icsfsumumodel_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsupsinfo
(
    collecttime timestamp NOT NULL,
    sn          character varying(128),
    mac         character varying(128),
    psname      character varying(128),
    psversion   character varying(128),
    pscount     integer
);
ALTER TABLE ONLY tbl_icsfsupsinfo ADD CONSTRAINT tbl_icsfsupsinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsusoinfo
(
    collecttime timestamp NOT NULL,
    ip          character varying(128),
    sn          character varying(128),
    mac         character varying(128),
    filename    character varying(128),
    soversion   character varying(128),
    socode      character varying(128),
    isused      character varying(20),
    isloaded    character varying(20),
    gccinfo     character varying(128),
    md5         character varying(128)
);
ALTER TABLE ONLY tbl_icsfsusoinfo ADD CONSTRAINT tbl_icsfsusoinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsuudpinfo
(
    collecttime timestamp NOT NULL,
    sn          character varying(128),
    mac         character varying(128),
    fsuhost     character varying(128),
    ip          character varying(128),
    netmask     character varying(20),
    gateway     character varying(20),
    sitename    character varying(128),
    version     character varying(128),
    serverip    character varying(128)
);
ALTER TABLE ONLY tbl_icsfsuudpinfo ADD CONSTRAINT tbl_icsfsuudpinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsplatformqrcode
(
    id              integer NOT NULL PRIMARY KEY,
    equipmenttypeid integer,
    serialnumber    character varying(128),
    equipmentname   character varying(128),
    createdate      timestamp,
    operatorname    character varying(128)
);

CREATE TABLE tbl_icsscriptdatainfo
(
    collecttime   timestamp NOT NULL,
    serverip      character varying(128),
    module        character varying(255) NOT NULL,
    scriptversion character varying(30),
    compiledate   timestamp,
    createdate    timestamp,
    feature       character varying(255) NOT NULL
);
ALTER TABLE ONLY tbl_icsscriptdatainfo ADD CONSTRAINT tbl_icsscriptdatainfo_serverip_collecttime PRIMARY KEY (serverip,collecttime);

CREATE TABLE tbl_icsserverinfo
(
    collecttime     timestamp NOT NULL,
    serverip        character varying(128),
    cpuusedrate     double precision,
    memoryusedrate  double precision,
    cdriveusedrate  double precision,
    dbserverip      character varying(128),
    dbinstallpath   character varying(128),
    dbdriveusedrate double precision
);
ALTER TABLE ONLY tbl_icsserverinfo ADD CONSTRAINT tbl_icsserverinfo_serverip_collecttime PRIMARY KEY (serverip,collecttime);

CREATE TABLE tbl_icssitewebinfo
(
    collecttime    timestamp NOT NULL,
    sitewebname    character varying(128),
    sitewebversion character varying(128),
    compiledate    timestamp,
    useddate       timestamp,
    servername     character varying(128),
    serverip       character varying(128),
    operatesystem  character varying(128),
    cpuconfig      character varying(255),
    memorysize     character varying(128),
    diskdrive      character varying(128),
    installpath    character varying(128),
    diskusedrate   double precision
);
ALTER TABLE ONLY tbl_icssitewebinfo ADD CONSTRAINT tbl_icssitewebinfo_sitewebname_serverip_collecttime PRIMARY KEY (sitewebname,serverip,collecttime);