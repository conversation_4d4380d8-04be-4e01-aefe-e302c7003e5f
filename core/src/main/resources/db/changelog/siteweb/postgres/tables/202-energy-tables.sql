CREATE TABLE energy_businessdefinitionmap
(
    mapid                        serial  NOT NULL PRIMARY KEY,
    businesstypeid               integer NOT NULL,
    complexindexdefinitionid     integer NOT NULL,
    complexindexdefinitiontypeid integer NOT NULL,
    extendfield1                 character varying(256)
);

CREATE TABLE energy_carbonemissionmanage
(
    id                 serial                NOT NULL PRIMARY KEY,
    objecttypeid       character varying(45) NOT NULL,
    objectid           character varying(45) NOT NULL,
    year               integer,
    month              integer,
    planvalue          double precision,
    yearplantotalvalue double precision,
    isarea             integer               NOT NULL,
    area               double precision,
    units              character varying(45) NOT NULL
);

CREATE TABLE energy_carbonmanage
(
    id                 serial                NOT NULL PRIMARY KEY,
    objecttypeid       character varying(45) NOT NULL,
    objectid           character varying(45) NOT NULL,
    year               integer               NOT NULL,
    month              integer               NOT NULL,
    planvalue          double precision      NOT NULL,
    yearplantotalvalue double precision      NOT NULL
);

CREATE TABLE energy_complexindexclassificationmap
(
    resourcestructureid integer NOT NULL,
    complexindexid      integer NOT NULL PRIMARY KEY,
    classificationid    integer NOT NULL,
    extendfield1        character varying(128)
);

CREATE TABLE energy_consumeconst
(
    id           serial                NOT NULL PRIMARY KEY,
    objectid     character varying(45) NOT NULL,
    objecttypeid character varying(45) NOT NULL,
    peoples      integer               NOT NULL,
    area         double precision
);

CREATE TABLE energy_consumedata
(
    id                    serial                NOT NULL PRIMARY KEY,
    objectid              character varying(45) NOT NULL,
    objecttypeid          character varying(45) NOT NULL,
    energytypeid          integer               NOT NULL,
    year                  integer               NOT NULL,
    month                 integer               NOT NULL,
    energysavingvalue     double precision,
    planvalue             double precision,
    overstepvalue         double precision,
    planvalueprealarm     double precision,
    overstepvalueprealarm double precision
);

CREATE TABLE energy_customer_config
(
    id           integer NOT NULL PRIMARY KEY,
    customername character varying(256),
    enable       smallint,
    createtime   timestamp,
    notes        character varying(1000)
);

CREATE TABLE energy_customer_tb_report
(
    reportid   integer NOT NULL PRIMARY KEY,
    reportname character varying(256),
    path       character varying(1000),
    recordpath character varying(1000),
    notes      character varying(1000)
);

CREATE TABLE energy_customer_tb_reportmap
(
    id          serial NOT NULL PRIMARY KEY,
    reportid    integer,
    sheetid     integer,
    rowid       integer,
    cellid      integer,
    hour        integer,
    equipmentid character varying(128),
    signalid    character varying(128)
);

CREATE TABLE energy_customer_tb_reportrecord
(
    id         serial NOT NULL PRIMARY KEY,
    reportid   integer,
    createtime timestamp,
    reportname character varying(256),
    filepath   character varying(1000),
    notes      character varying(1000)
);

CREATE TABLE energy_dataentry
(
    entryid      serial NOT NULL PRIMARY KEY,
    entryname    character varying(128),
    entryalias   character varying(255),
    description  character varying(255),
    extendfield1 character varying(255),
    canedit      integer,
    candelete    integer
);

CREATE TABLE energy_dataitem
(
    entryitemid   serial  NOT NULL PRIMARY KEY,
    parententryid integer NOT NULL,
    entryid       integer NOT NULL,
    itemid        integer NOT NULL,
    itemvalue     character varying(128),
    itemalias     character varying(255),
    issystem      smallint,
    isdefault     smallint,
    description   character varying(255),
    extendfield1  character varying(255),
    extendfield2  character varying(255),
    extendfield3  character varying(255),
    extendfield4  character varying(255),
    extendfield5  character varying(255),
    canedit       smallint,
    candelete     smallint,
    cantimeliness smallint
);

CREATE TABLE energy_dataitemtimeliness
(
    itemtimelinessid serial  NOT NULL PRIMARY KEY,
    entryitemid      integer NOT NULL,
    itemvalue        character varying(128),
    extendfield1     character varying(255),
    extendfield2     character varying(255),
    extendfield3     character varying(255),
    extendfield4     character varying(255),
    extendfield5     character varying(255),
    starttime        timestamp
);
CREATE UNIQUE INDEX idx_21834_energy_dataitemtimeliness_unique ON energy_dataitemtimeliness USING BTREE (entryitemid, starttime);

CREATE TABLE energy_dimensiontype
(
    dimensiontypeid   serial                  NOT NULL PRIMARY KEY,
    dimensiontypename character varying(255)  NOT NULL,
    isused            smallint DEFAULT '1'::SMALLINT,
    operatoruserid    integer                 NOT NULL,
    updatetime        timestamp NOT NULL,
    notes             character varying(1000) NOT NULL
);

CREATE TABLE energy_elecfeeconfigoperatelog
(
    logid            serial                  NOT NULL PRIMARY KEY,
    operator         character varying(128),
    operatorid       integer,
    updatedate       timestamp NOT NULL,
    operationcontent character varying(4000) NOT NULL,
    changecontent    text,
    extendfield1     character varying(255)
);

CREATE TABLE energy_elecfeefpg
(
    fpgid          serial  NOT NULL PRIMARY KEY,
    schemeid       integer NOT NULL,
    effectivestart timestamp NOT NULL,
    effectiveend   timestamp NOT NULL,
    createdate     timestamp NOT NULL,
    updatedate     timestamp NOT NULL,
    createrid      integer NOT NULL,
    creatername    character varying(256),
    updaterid      integer NOT NULL,
    updatername    character varying(256),
    fpgdesckey     integer,
    fpgdesc        character varying(256),
    extendfield1   character varying(256)
);

CREATE TABLE energy_elecfeefpgvalue
(
    fpgvalueid   serial                NOT NULL PRIMARY KEY,
    fpgid        integer               NOT NULL,
    steppriceid  integer               NOT NULL,
    schemeid     integer               NOT NULL,
    fpgvalue     character varying(64) NOT NULL,
    createdate   timestamp NOT NULL,
    updatedate   timestamp NOT NULL,
    createrid    integer               NOT NULL,
    creatername  character varying(256),
    updaterid    integer               NOT NULL,
    updatername  character varying(256),
    extendfield1 character varying(256)
);

CREATE TABLE energy_elecfeescheme
(
    schemeid         serial  NOT NULL PRIMARY KEY,
    schemename       character varying(512),
    appliedrange     character varying(1024),
    enabledate       timestamp,
    deactivatedate   timestamp,
    startmonth       integer,
    endmonth         integer,
    enablestatus     integer NOT NULL,
    createdate       timestamp NOT NULL,
    updatedate       timestamp NOT NULL,
    createrid        integer NOT NULL,
    creatername      character varying(256),
    updaterid        integer NOT NULL,
    updatername      character varying(256),
    businesstypeid   integer,
    businesstypename character varying(128),
    extendfield1     character varying(256),
    extendfield2     character varying(256)
);

CREATE TABLE energy_elecfeeschemestructuremap
(
    mapid                 serial  NOT NULL PRIMARY KEY,
    schemeid              integer NOT NULL,
    resourcestructureid   integer NOT NULL,
    resourcestructurename character varying(256),
    createdate            timestamp NOT NULL,
    updatedate            timestamp NOT NULL,
    createrid             integer NOT NULL,
    creatername           character varying(256),
    updaterid             integer NOT NULL,
    updatername           character varying(256),
    extendfield1          character varying(256)
);

CREATE TABLE energy_elecfeestepprice
(
    steppriceid  serial                 NOT NULL PRIMARY KEY,
    schemeid     integer                NOT NULL,
    stepname     character varying(256) NOT NULL,
    upperlimit   integer                NOT NULL,
    createdate   timestamp NOT NULL,
    updatedate   timestamp NOT NULL,
    createrid    integer                NOT NULL,
    creatername  character varying(256),
    updaterid    integer                NOT NULL,
    updatername  character varying(256),
    asmaxstep    integer,
    extendfield1 character varying(256)
);

CREATE TABLE energy_errordatamodifyrecord
(
    id             serial                 NOT NULL PRIMARY KEY,
    complexindexid integer                NOT NULL,
    errortime      timestamp NOT NULL,
    modifytime     timestamp NOT NULL,
    userid         integer                NOT NULL,
    username       character varying(255) NOT NULL,
    originalvalue  double precision       NOT NULL,
    indexvalue     double precision       NOT NULL,
    note           character varying(255),
    extendfield1   character varying(255)
);

CREATE TABLE energy_management
(
    energyid   serial                 NOT NULL PRIMARY KEY,
    energyname character varying(128) NOT NULL,
    unit       character varying(128),
    operator   character varying(128),
    contact    character varying(128),
    starttime  timestamp NOT NULL,
    endtime    timestamp NOT NULL,
    notes      character varying(1000)
);

CREATE TABLE energy_managementmap
(
    mapid               serial  NOT NULL PRIMARY KEY,
    energyid            integer NOT NULL,
    resourcestructureid integer NOT NULL,
    ismarker            integer NOT NULL,
    property            character varying(1000)
);
CREATE INDEX idx_21905_energy_managementmap_idx1 ON energy_managementmap USING BTREE (energyid, resourcestructureid);

CREATE TABLE energy_manualrecord
(
    id             serial                 NOT NULL PRIMARY KEY,
    complexindexid integer                NOT NULL,
    recordtime     timestamp NOT NULL,
    recordvalue    double precision       NOT NULL,
    calctype       integer                NOT NULL,
    userid         integer                NOT NULL,
    username       character varying(255) NOT NULL,
    inserttime     timestamp NOT NULL,
    extendfield1   character varying(255),
    unit           character varying(128)
);

CREATE TABLE energy_multilevelobject
(
    objectid       integer NOT NULL PRIMARY KEY,
    objectname     character varying(512),
    parentobjectid integer,
    levelnum       integer
);

CREATE TABLE energy_objectmap
(
    id                 serial                  NOT NULL PRIMARY KEY,
    dimensiontypeid    integer                 NOT NULL,
    levelid            integer                 NOT NULL,
    objectid           integer                 NOT NULL,
    objectidtype       integer                 NOT NULL,
    parentobjectid     integer                 NOT NULL,
    parentobjectidtype integer                 NOT NULL,
    levelofpath        character varying(1024) NOT NULL,
    notes              character varying(1000) NOT NULL
);

CREATE TABLE energy_ratingconfig
(
    ratingconfigid   serial                 NOT NULL PRIMARY KEY,
    outdrytempparam  character varying(255),
    outwettempparam  character varying(255),
    indrytempparam   character varying(255),
    inwettempparam   character varying(255),
    runningloadparam character varying(255),
    itpowerparam     character varying(255),
    totalpowerparam  character varying(255),
    objectid         integer                NOT NULL,
    name             character varying(255) NOT NULL,
    intervalsecond   integer                NOT NULL,
    status           integer DEFAULT 0      NOT NULL,
    starttime        timestamp,
    endtime          timestamp,
    workingcondition character varying(255),
    userid           integer                NOT NULL,
    cityid           integer                NOT NULL
);

CREATE TABLE energy_ratingdata
(
    ratingdataid     serial  NOT NULL PRIMARY KEY,
    ratingconfigid   integer NOT NULL,
    sampletime       timestamp NOT NULL,
    intervalsecond   integer NOT NULL,
    workingcondition character varying(255),
    outdrytemp       double precision,
    outwettemp       double precision,
    indrytemp        double precision,
    inwettemp        double precision,
    runningload      double precision,
    itpower          double precision,
    totalpower       double precision
);
CREATE INDEX idx_21939_energy_ratingdata_idx1 ON energy_ratingdata USING BTREE (ratingconfigid, sampletime, workingcondition);

CREATE TABLE energy_ratingdatahistory
(
    ratingdataid     serial                 NOT NULL PRIMARY KEY,
    ratingconfigid   integer                NOT NULL,
    sampletime       timestamp NOT NULL,
    intervalsecond   integer                NOT NULL,
    workingcondition character varying(255) NOT NULL,
    outdrytemp       double precision       NOT NULL,
    outwettemp       double precision       NOT NULL,
    indrytemp        double precision       NOT NULL,
    inwettemp        double precision       NOT NULL,
    runningload      double precision       NOT NULL,
    itpower          double precision       NOT NULL,
    totalpower       double precision       NOT NULL,
    times            integer DEFAULT 1      NOT NULL,
    deletedate       timestamp NOT NULL
);

CREATE TABLE energy_structure
(
    structureid      serial                  NOT NULL PRIMARY KEY,
    structurename    character varying(255)  NOT NULL,
    sourcecategory   integer                 NOT NULL,
    notes            character varying(1000) NOT NULL,
    asrootinflownode integer
);