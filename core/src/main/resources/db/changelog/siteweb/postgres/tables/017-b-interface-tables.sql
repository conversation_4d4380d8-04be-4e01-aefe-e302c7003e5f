CREATE TABLE st_org_cucc_equip
(
    OrgCUCCEquipId serial PRIMARY KEY,
    rowid  integer NOT NULL,
    class  character varying(255),
    type   character varying(255),
    c1     character varying(255),
    c2     character varying(255),
    typeid character varying(255),
    note   character varying(255)
);

CREATE TABLE tbl_activemessagecmcc
(
    userid       integer,
    serialno     numeric(14, 0)        NOT NULL,
    siteid       character varying(20) NOT NULL,
    fsuid        character varying(20) NOT NULL,
    deviceid     character varying(20) NOT NULL,
    sitename     text,
    fsuname      text,
    devicename   text,
    messagetype  integer               NOT NULL,
    messagename  character varying(64),
    messageparm  text,
    starttime    timestamp,
    endtime      timestamp,
    result       integer,
    try          integer,
    failurecause text                  NOT NULL,
    isconfirm    integer               NOT NULL,
    controltype  integer,
    description  text                  NOT NULL
);
ALTER TABLE ONLY tbl_activemessagecmcc ADD CONSTRAINT idx_22646_primary PRIMARY KEY (deviceid, fsuid, messagetype, serialno, siteid);

CREATE TABLE tbl_activemessagecucc
(
    userid       integer,
    serialno     numeric(14, 0)         NOT NULL,
    suid         character varying(255) NOT NULL,
    deviceid     character varying(255) NOT NULL,
    surid        character varying(255),
    devicerid    character varying(255),
    suname       character varying(255),
    devicename   character varying(255),
    messagetype  integer                NOT NULL,
    messagename  character varying(64),
    messageparm  text,
    starttime    timestamp,
    endtime      timestamp,
    result       integer,
    try          integer,
    failurecause text                   NOT NULL,
    isconfirm    integer                NOT NULL,
    controltype  integer,
    description  text                   NOT NULL
);
ALTER TABLE ONLY tbl_activemessagecucc ADD CONSTRAINT idx_22651_primary PRIMARY KEY (messagetype, serialno);

CREATE TABLE tbl_alarmdatacucc
(
    suid          character varying(64) NOT NULL,
    deviceid      character varying(64) NOT NULL,
    standardid    character varying(64) NOT NULL,
    surid         character varying(255),
    devicerid     character varying(255),
    suname        character varying(255),
    devicename    character varying(255),
    standardname  character varying(255),
    serialno      character varying(10),
    alarmtime     timestamp,
    alarmdesc     character varying(255),
    triggerval    double precision,
    alarmflag     integer,
    description   text,
    extendfield1  text,
    extendfield2  text,
    alarmdatatype integer               NOT NULL
);
ALTER TABLE ONLY tbl_alarmdatacucc ADD CONSTRAINT idx_22673_primary PRIMARY KEY (alarmdatatype, deviceid, standardid, suid);

CREATE TABLE tbl_alarmpropertycucc
(
    suid              character varying(64) NOT NULL,
    deviceid          character varying(64) NOT NULL,
    standardid        character varying(64) NOT NULL,
    surid             character varying(255),
    devicerid         character varying(255),
    suname            character varying(255),
    devicename        character varying(255),
    standardname      character varying(255),
    bdelay            double precision,
    edelay            double precision,
    description       text,
    extendfield1      text,
    extendfield2      text,
    alarmpropertytype integer               NOT NULL
);
ALTER TABLE ONLY tbl_alarmpropertycucc ADD CONSTRAINT idx_22678_primary PRIMARY KEY (alarmpropertytype, deviceid, standardid, suid);

CREATE TABLE tbl_aodatacucc
(
    suid         character varying(64) NOT NULL,
    deviceid     character varying(64) NOT NULL,
    standardid   character varying(64) NOT NULL,
    surid        character varying(255),
    devicerid    character varying(255),
    suname       character varying(255),
    devicename   character varying(255),
    standardname character varying(255),
    setvalue     double precision,
    hlimit       double precision,
    shlimit      double precision,
    llimit       double precision,
    sllimit      double precision,
    threshold    double precision,
    relativeval  double precision,
    intervaltime double precision,
    description  text,
    extendfield1 text,
    extendfield2 text,
    aodatatype   integer               NOT NULL
);
ALTER TABLE ONLY tbl_aodatacucc ADD CONSTRAINT idx_22683_primary PRIMARY KEY (aodatatype, deviceid, standardid, suid);

CREATE TABLE tbl_devicectcc
(
    DeviceCTCCId serial PRIMARY KEY,
    sudevicetypename      character varying(50),
    sudevicetypeid        character varying(10),
    equipmentbasetypename character varying(50),
    description           character varying(255),
    equipmentbasetypeid   integer,
    meanings              character varying(255),
    updatetime            character varying(50),
    recorder              character varying(50),
    devicetypeid          character varying(10)
);

CREATE TABLE tbl_devicesubtypecmcc
(
    devicetypeid      integer NOT NULL,
    devicesubtypeid   integer NOT NULL,
    devicesubtypename character varying(128),
    description       text
);
ALTER TABLE ONLY tbl_devicesubtypecmcc ADD CONSTRAINT idx_22872_primary PRIMARY KEY (devicesubtypeid, devicetypeid);

CREATE TABLE tbl_devicetypebasemapcmcc
(
    devicetypeid    integer NOT NULL,
    devicesubtypeid integer NOT NULL,
    baseequipmentid integer NOT NULL
);
ALTER TABLE ONLY tbl_devicetypebasemapcmcc ADD CONSTRAINT idx_22877_primary PRIMARY KEY (devicetypeid, devicesubtypeid, baseequipmentid);

CREATE TABLE tbl_devicetypecmcc
(
    devicetypeid   integer NOT NULL PRIMARY KEY,
    devicetypename character varying(128),
    description    text
);

CREATE TABLE tbl_devicetypectcc
(
    DeviceTypeCTCCId serial PRIMARY KEY,
    sudevicetypeid   character varying(20),
    sudevicetypename character varying(50),
    description      character varying(50),
    updatetime       character varying(50)
);

CREATE TABLE tbl_dodatacucc
(
    suid         character varying(64) NOT NULL,
    deviceid     character varying(64) NOT NULL,
    standardid   character varying(64) NOT NULL,
    surid        character varying(255),
    devicerid    character varying(255),
    suname       character varying(255),
    devicename   character varying(255),
    standardname character varying(255),
    description  text,
    extendfield1 text,
    extendfield2 text,
    dodatatype   integer               NOT NULL
);
ALTER TABLE ONLY tbl_dodatacucc ADD CONSTRAINT idx_22888_primary PRIMARY KEY (deviceid, dodatatype, standardid, suid);

CREATE TABLE tbl_enumdatacucc
(
    entryid              integer NOT NULL PRIMARY KEY,
    attributetype        integer,
    attributename        character varying(128),
    attributedescription text,
    enumid               integer,
    enumtype             character varying(128),
    enumvalue            integer,
    enumdefine           text,
    description          text,
    extendfield1         text
);

CREATE TABLE tbl_equipmentbasetypemapctcc
(
    sudevicetypeid              integer NOT NULL PRIMARY KEY,
    sudevicetypename            character varying(50),
    firstequipmentbasetypeid    integer,
    firstequipmentbasetypename  character varying(50),
    secondequipmentbasetypeid   integer,
    secondequipmentbasetypename character varying(50),
    thirdequipmentbasetypeid    integer,
    thirdequipmentbasetypename  character varying(50),
    extendfield1                character varying(128),
    extendfield2                character varying(128)
);

CREATE TABLE tbl_equipmentcmcc
(
    stationid     integer NOT NULL,
    monitorunitid integer NOT NULL,
    equipmentid   integer NOT NULL,
    deviceid      character varying(255),
    devicename    character varying(255),
    fsuid         character varying(255) DEFAULT ''::CHARACTER VARYING,
    siteid        character varying(255) DEFAULT ''::CHARACTER VARYING,
    sitename      character varying(255),
    roomid        character varying(255),
    roomname      character varying(255),
    devicetype    integer,
    devicesubtype integer,
    model         character varying(255),
    brand         character varying(255),
    ratedcapacity double precision,
    version       character varying(20),
    beginruntime  timestamp,
    devdescribe   character varying(255),
    extendfield1  character varying(255),
    extendfield2  character varying(255)
);
ALTER TABLE ONLY tbl_equipmentcmcc ADD CONSTRAINT idx_22990_primary PRIMARY KEY (equipmentid, stationid);
CREATE INDEX idx_22990_tbl_equipmentcmcc_idx1 ON tbl_equipmentcmcc USING BTREE (stationid, equipmentid);
CREATE INDEX idx_22990_tbl_equipmentcmcc_idx2 ON tbl_equipmentcmcc USING BTREE (fsuid, deviceid);

CREATE TABLE tbl_equipmentcucc
(
    stationid       integer NOT NULL,
    monitorunitid   integer NOT NULL,
    equipmentid     integer NOT NULL,
    deviceid        character varying(255),
    devicename      character varying(255),
    devicerid       character varying(255),
    suid            character varying(255),
    devicevender    character varying(255),
    devicetype      character varying(255),
    mfd             character varying(255),
    controllertype  character varying(255),
    softwareversion character varying(255),
    batchno         character varying(255),
    password        character varying(512),
    extendfield1    character varying(255),
    extendfield2    character varying(255)
);
ALTER TABLE ONLY tbl_equipmentcucc
    ADD CONSTRAINT idx_22997_primary PRIMARY KEY (equipmentid, stationid);
CREATE INDEX idx_22997_idx_equipmentcucc_su_deviceid ON tbl_equipmentcucc USING BTREE (suid, deviceid);
CREATE INDEX idx_22997_tbl_equipmentcucc_idx1 ON tbl_equipmentcucc USING BTREE (stationid, equipmentid);

CREATE TABLE tbl_equipmentfsucucc
(
    stationid         integer NOT NULL,
    monitorunitid     integer NOT NULL,
    equipmentid       integer NOT NULL,
    deviceid          character varying(255),
    nameprefix        character varying(255),
    devicename        character varying(255),
    fsuid             character varying(255),
    siteid            character varying(255),
    sitename          character varying(255),
    roomid            character varying(255),
    roomname          character varying(255),
    model             character varying(255),
    brand             character varying(255),
    ratedcapacity     double precision,
    version           character varying(20),
    beginruntime      timestamp,
    devdescribe       character varying(255),
    extendfield1      character varying(255),
    extendfield2      character varying(255),
    equipmentcategory integer,
    childcategory     integer,
    assetsid          character varying(255),
    devicetype        character varying(50),
    devicesubtype     character varying(50),
    mfd               character varying(50),
    devicevender      character varying(50),
    softwareversion   character varying(50),
    controllertype    character varying(50),
    batchno           character varying(50),
    equipmentmode     character varying(50)
);
ALTER TABLE ONLY tbl_equipmentfsucucc ADD CONSTRAINT idx_23005_primary PRIMARY KEY (stationid, equipmentid);

CREATE TABLE tbl_equipmentgdctcc
(
    suid         character varying(40) NOT NULL,
    deviceid     character varying(40) NOT NULL,
    devicename   character varying(128),
    equipmentid  character varying(128),
    devicehltype integer,
    extendfield1 character varying(128),
    extendfield2 character varying(128)
);
ALTER TABLE ONLY tbl_equipmentgdctcc ADD CONSTRAINT idx_23010_primary PRIMARY KEY (deviceid, suid);
CREATE INDEX idx_23010_tbl_equipmentgdctcc_idx1 ON tbl_equipmentgdctcc USING BTREE (suid, deviceid);

CREATE TABLE tbl_equipmentktctcc
(
    EquipmentktCTCCId serial PRIMARY KEY,
    stationktname     character varying(128) NOT NULL,
    stationktid       character varying(40)  NOT NULL,
    roomktname        character varying(128) NOT NULL,
    roomktid          character varying(40)  NOT NULL,
    systemname        character varying(128) NOT NULL,
    equipmentname     character varying(128) NOT NULL,
    equipmentstate    character varying(40),
    equipmenttypename character varying(40),
    suid              character varying(40),
    isadded           integer,
    extendfield1      character varying(255),
    extendfield2      character varying(255)
);

CREATE TABLE tbl_eventconditionctcc
(
    equipmenttemplateid integer NOT NULL,
    eventid             integer NOT NULL,
    eventconditionid    integer NOT NULL,
    description         character varying(50)
);
ALTER TABLE ONLY tbl_eventconditionctcc ADD CONSTRAINT idx_23124_primary PRIMARY KEY (equipmenttemplateid, eventid, eventconditionid);

CREATE TABLE tbl_fsuconfig
(
    fsuid                 character varying(255) NOT NULL PRIMARY KEY,
    needsyncflag          integer,
    fsusendtime           timestamp,
    fsusendcfgcode        character varying(32),
    fsusendcfgcontent     text,
    lastfsusendcfgcontent text,
    fsusendcount          integer DEFAULT 0,
    scsynccfgtime         timestamp,
    scsynccfgcode         character varying(32),
    scsynccfgcontent      text,
    lastscsynccfgcontent  text,
    scsynccount           integer DEFAULT 0
);

CREATE TABLE tbl_fsuconfigimportlog
(
    logid      integer                NOT NULL PRIMARY KEY,
    fsuid      character varying(255) NOT NULL,
    cfgtype    character varying(255) NOT NULL,
    cfgcontent text                   NOT NULL,
    savetime   timestamp NOT NULL
);

CREATE TABLE tbl_fsuconfigsendbyfsu
(
    logid             integer                NOT NULL PRIMARY KEY,
    fsuid             character varying(255) NOT NULL,
    fsusendtime       timestamp NOT NULL,
    fsusendcfgcode    character varying(32)  NOT NULL,
    fsusendcfgcontent text                   NOT NULL
);
CREATE INDEX idx_23228_tbl_fsuconfigsendbyfsu_idx1 ON tbl_fsuconfigsendbyfsu USING BTREE (fsuid, fsusendtime);

CREATE TABLE tbl_fsucuccpresetequip
(
    rowid                 serial               NOT NULL PRIMARY KEY,
    cucctypeid            character varying(3) NOT NULL,
    cuccclassname         character varying(255),
    cucctypename          character varying(255),
    equipmenttemplateid   integer              NOT NULL,
    equipmenttemplatename character varying(255),
    memo                  character varying(255),
    updatetime            timestamp,
    ex1                   character varying(255),
    ex2                   character varying(255),
    ex3                   character varying(255),
    ex4                   character varying(255),
    exx                   text
);
CREATE UNIQUE INDEX idx_23235_idx_tbl_fsucuccpresetequip ON tbl_fsucuccpresetequip USING BTREE (cucctypeid);

CREATE TABLE tbl_fsuftphisdatalog
(
    logid    serial                 NOT NULL PRIMARY KEY,
    fsuid    character varying(255) NOT NULL,
    filename character varying(255) NOT NULL,
    filesize integer                NOT NULL,
    savetime timestamp NOT NULL
);
CREATE INDEX idx_23242_tbl_fsuftphisdatalog_idx1 ON tbl_fsuftphisdatalog USING BTREE (fsuid, filename);

CREATE TABLE tbl_fsutsignalcmcc
(
    fsuid        character varying(20) NOT NULL,
    deviceid     character varying(26) NOT NULL,
    id           character varying(20) NOT NULL,
    signalnumber character varying(5)  NOT NULL,
    type         integer,
    signalname   character varying(80),
    alarmlevel   integer,
    threshold    double precision,
    absoluteval  double precision,
    relativeval  double precision,
    describe     character varying(120),
    nmalarmid    character varying(40),
    tsignalid    integer               NOT NULL,
    updatetime   timestamp,
    ei1          integer,
    es1          character varying(255)
);
ALTER TABLE ONLY tbl_fsutsignalcmcc ADD CONSTRAINT idx_23248_primary PRIMARY KEY (deviceid, fsuid, tsignalid);
CREATE INDEX idx_23248_tbl_fsutsignalcmcc_idx1 ON tbl_fsutsignalcmcc USING BTREE (fsuid);

CREATE TABLE tbl_historybinterfacelog
(
    id                  serial                 NOT NULL PRIMARY KEY,
    fsuid               character varying(255) NOT NULL,
    monitorunitid       character varying(255) NOT NULL,
    dataserverip        character varying(255) NOT NULL,
    requestmessagetype  character varying(128) NOT NULL,
    responsemessagetype character varying(128),
    communicationresult integer,
    parseresult         integer,
    resultcause         character varying(255),
    inserttime          timestamp NOT NULL,
    xmlinvokecontent    text,
    xmlresultcontent    text,
    extendfield         character varying(255)
);

CREATE TABLE tbl_historymessagecmcc
(
    userid       integer,
    serialno     numeric(14, 0)        NOT NULL,
    siteid       character varying(20) NOT NULL,
    fsuid        character varying(20) NOT NULL,
    deviceid     character varying(20) NOT NULL,
    sitename     text,
    fsuname      text,
    devicename   text,
    messagetype  integer               NOT NULL,
    messagename  character varying(64),
    messageparm  text,
    starttime    timestamp,
    endtime      timestamp,
    result       integer,
    try          integer,
    failurecause text                  NOT NULL,
    isconfirm    integer               NOT NULL,
    controltype  integer,
    description  text                  NOT NULL
);
ALTER TABLE ONLY tbl_historymessagecmcc ADD CONSTRAINT idx_23341_primary PRIMARY KEY (deviceid, fsuid, messagetype, serialno, siteid);

CREATE TABLE tbl_historymessagecucc
(
    userid       integer,
    serialno     numeric(14, 0)         NOT NULL,
    suid         character varying(255) NOT NULL,
    deviceid     character varying(255) NOT NULL,
    surid        character varying(255),
    devicerid    character varying(255),
    suname       character varying(255),
    devicename   character varying(255),
    messagetype  integer                NOT NULL,
    messagename  character varying(64),
    messageparm  text,
    starttime    timestamp,
    endtime      timestamp,
    result       integer,
    try          integer,
    failurecause text                   NOT NULL,
    isconfirm    integer                NOT NULL,
    controltype  integer,
    description  text                   NOT NULL
);
ALTER TABLE ONLY tbl_historymessagecucc ADD CONSTRAINT idx_23346_primary PRIMARY KEY (messagetype, serialno);

CREATE TABLE tbl_roomcmcc
(
    stationid   integer NOT NULL,
    houseid     integer NOT NULL,
    roomid      character varying(125),
    roomname    character varying(255),
    siteid      character varying(255),
    description character varying(255)
);
ALTER TABLE ONLY tbl_roomcmcc ADD CONSTRAINT idx_23854_primary PRIMARY KEY (houseid, stationid);
CREATE INDEX idx_23854_tbl_roomcmcc_idx1 ON tbl_roomcmcc USING BTREE (stationid, houseid);

CREATE TABLE tbl_roomfsucucc
(
    stationid   integer NOT NULL,
    houseid     integer NOT NULL,
    roomid      character varying(125),
    roomname    character varying(255),
    siteid      character varying(255),
    description character varying(255)
);
ALTER TABLE ONLY tbl_roomfsucucc ADD CONSTRAINT idx_23859_primary PRIMARY KEY (stationid, houseid);

CREATE TABLE tbl_roomktctcc
(
    roomktname       character varying(128) NOT NULL,
    roomktid         character varying(40)  NOT NULL,
    stationktname    character varying(128) NOT NULL,
    stationktid      character varying(40)  NOT NULL,
    friendlyroomname character varying(128),
    isgenerated      integer,
    isadded          integer,
    extendfield1     character varying(255),
    extendfield2     character varying(255)
);
ALTER TABLE ONLY tbl_roomktctcc ADD CONSTRAINT idx_23864_primary PRIMARY KEY (roomktid, stationktid);
CREATE INDEX idx_23864_tbl_roomktctcc_idx1 ON tbl_roomktctcc USING BTREE (stationktid, roomktid);

CREATE TABLE tbl_standarddicsigctcc
(
    sid              double precision,
    stdspdicname     character varying(255),
    version          double precision,
    sudevicetypeid2  double precision,
    devicehltyp      double precision,
    devicetypename   character varying(255),
    standarddicid4   double precision,
    standardname     character varying(255),
    sptype           character varying(255),
    alarmmeanings    character varying(255),
    normalmeanings   character varying(255),
    unit             character varying(255),
    xmlcolumn        character varying(255),
    serinocom        character varying(255),
    signalmeanings   character varying(255),
    update           timestamp,
    systemtype       character varying(255),
    remark           character varying(255),
    optionid         double precision,
    alarmlevel       double precision,
    alarmthresbhold  double precision,
    startdelay       double precision,
    enddelay         double precision,
    PERIOD DOUBLE PRECISION,
    absoluteval      character varying(255),
    relativeval      character varying(255),
    hysteresis       double precision,
    alarmlevel1      double precision,
    alarmthresbhold1 double precision,
    startdelay1      double precision,
    enddelay1        double precision,
    period1          double precision,
    absoluteval1     character varying(255),
    relativeval1     character varying(255),
    hysteresis1      double precision,
    alarmlevel2      double precision,
    alarmthresbhold2 double precision,
    startdelay2      double precision,
    enddelay2        double precision,
    period2          double precision,
    absoluteval2     character varying(255),
    relativeval2     character varying(255),
    hysteresis2      double precision,
    alarmlevel3      double precision,
    alarmthresbhold3 double precision,
    startdelay3      double precision,
    enddelay3        double precision,
    period3          double precision,
    absoluteval3     character varying(255),
    relativeval3     character varying(255),
    hysteresis3      double precision,
    alarmlevel4      double precision,
    alarmthresbhold4 double precision,
    startdelay4      double precision,
    enddelay4        double precision,
    period4          double precision,
    absoluteval4     character varying(255),
    relativeval4     character varying(255),
    hysteresis4      character varying(255),
    alarmlevel5      double precision,
    alarmthresbhold5 double precision,
    startdelay5      double precision,
    enddelay5        double precision,
    period5          double precision,
    absoluteval5     character varying(255),
    relativeval5     character varying(255),
    hysteresis5      character varying(255),
    alarmlevel6      double precision,
    alarmthresbhold6 double precision,
    startdelay6      double precision,
    enddelay6        double precision,
    period6          double precision,
    absoluteval6     double precision,
    relativeval6     double precision,
    hysteresis6      character varying(255),
    alarmlevel7      double precision,
    alarmthresbhold7 character varying(255),
    startdelay7      double precision,
    enddelay7        double precision,
    period7          double precision,
    absoluteval7     character varying(255),
    relativeval7     character varying(255),
    hysteresis7      character varying(255),
    alarmlevel8      double precision,
    alarmthresbhold8 double precision,
    startdelay8      double precision,
    enddelay8        double precision,
    period8          double precision,
    absoluteval8     character varying(255),
    relativeval8     character varying(255),
    hysteresis8      character varying(255),
    alarmlevel9      double precision,
    alarmthresbhold9 double precision,
    startdelay9      double precision,
    enddelay9        double precision,
    period9          double precision,
    absoluteval9     character varying(255),
    relativeval9     character varying(255),
    hysteresis9      character varying(255),
    inputtype        character varying(255),
    comfrom          character varying(255),
    standarddicid    character varying(20) NOT NULL PRIMARY KEY,
    sudevicetypeid   character varying(10),
    devicetypeidctcc character varying(10)
);

CREATE TABLE tbl_stationcmcc
(
    stationid   integer NOT NULL PRIMARY KEY,
    siteid      character varying(255),
    sitename    character varying(255),
    description character varying(255)
);

CREATE TABLE tbl_stationktctcc
(
    stationktname       character varying(128) NOT NULL,
    stationktid         character varying(40)  NOT NULL PRIMARY KEY,
    friendlystationname character varying(128),
    bigregionname       character varying(40),
    smallregionname     character varying(40),
    stationtype         character varying(40),
    stationlevel        character varying(40),
    amsstationname      character varying(128),
    amsstationid        integer,
    extendfield1        character varying(255),
    extendfield2        character varying(255)
);

CREATE TABLE tbl_storagerulecmcc
(
    siteid          character varying(125) NOT NULL,
    fsuid           character varying(125) NOT NULL,
    deviceid        character varying(125) NOT NULL,
    id              character varying(20)  NOT NULL,
    signalnumber    character varying(10)  NOT NULL,
    type            integer,
    absoluteval     double precision,
    relativeval     double precision,
    storageinterval numeric(12, 0),
    storagereftime  character varying(80),
    storageruletype integer                NOT NULL
);
ALTER TABLE ONLY tbl_storagerulecmcc ADD CONSTRAINT idx_24090_primary PRIMARY KEY (deviceid, fsuid, id, signalnumber, siteid, storageruletype);

CREATE TABLE tbl_suportcucc
(
    suid     character varying(255) NOT NULL,
    surid    character varying(255),
    portno   character varying(128) NOT NULL,
    portname character varying(128),
    porttype character varying(128),
    settings character varying(255)
);
ALTER TABLE ONLY tbl_suportcucc ADD CONSTRAINT idx_24106_primary PRIMARY KEY (portno, suid);

CREATE TABLE tbl_suportdevicecucc
(
    suid       character varying(255) NOT NULL,
    surid      character varying(255),
    portno     character varying(128) NOT NULL,
    deviceid   character varying(255) NOT NULL,
    devicerid  character varying(255),
    address    character varying(255),
    protocol   character varying(255),
    version    character varying(255),
    updatetime timestamp
);
alter table tbl_suportdevicecucc add CONSTRAINT tbl_suportdevicecucc_primary PRIMARY KEY (SUID, DeviceID);

CREATE TABLE tbl_systemktctcc
(
    SystemktCTCCId serial NOT NULL PRIMARY KEY,
    stationktname  character varying(128) NOT NULL,
    stationktid    character varying(40)  NOT NULL,
    systemname     character varying(128) NOT NULL,
    systemstate    character varying(40),
    systemtypename character varying(40),
    isadded        integer,
    roomktname     character varying(128),
    roomktid       character varying(40),
    extendfield1   character varying(255),
    extendfield2   character varying(255)
);

CREATE TABLE tbl_thresholdcmcc
(
    siteid        character varying(125) NOT NULL,
    fsuid         character varying(125) NOT NULL,
    deviceid      character varying(125) NOT NULL,
    id            character varying(20)  NOT NULL,
    signalnumber  character varying(10)  NOT NULL,
    type          integer,
    threshold     double precision,
    alarmlevel    integer                NOT NULL,
    nmalarmid     character varying(40),
    thresholdtype integer                NOT NULL
);
ALTER TABLE ONLY tbl_thresholdcmcc ADD CONSTRAINT idx_24141_primary PRIMARY KEY (deviceid, fsuid, id, signalnumber, siteid, thresholdtype);

CREATE TABLE tbl_tsignalcmcc
(
    fsuid        character varying(20) NOT NULL,
    deviceid     character varying(26) NOT NULL,
    id           character varying(20) NOT NULL,
    signalnumber character varying(5)  NOT NULL,
    type         integer,
    signalname   character varying(80),
    alarmlevel   integer,
    threshold    double precision,
    absoluteval  double precision,
    relativeval  double precision,
    describe     character varying(120),
    nmalarmid    character varying(40),
    ei1          integer,
    es1          character varying(255),
    tsignaltype  integer               NOT NULL
);
ALTER TABLE ONLY tbl_tsignalcmcc ADD CONSTRAINT idx_24153_primary PRIMARY KEY (deviceid, fsuid, id, signalnumber, tsignaltype);

CREATE TABLE tbl_tsignalgdctcc
(
    suid           character varying(40) NOT NULL,
    deviceid       character varying(40) NOT NULL,
    spid           character varying(40) NOT NULL,
    spname         character varying(128),
    sptype         integer,
    optionid       integer,
    unit           character varying(20),
    normalmeanings character varying(128),
    alarmmeanings  character varying(128),
    extendfield1   character varying(128),
    extendfield2   character varying(128),
    equipmentid    character varying(20),
    signalid       character varying(20),
    conditionid    character varying(20)
);
ALTER TABLE ONLY tbl_tsignalgdctcc ADD CONSTRAINT idx_24158_primary PRIMARY KEY (deviceid, spid, suid);
CREATE INDEX idx_24158_tbl_tsignalgdctcc_idx1 ON tbl_tsignalgdctcc USING BTREE (suid, deviceid, spid);

CREATE TABLE tsl_monitorunitcmcc
(
    stationid          integer NOT NULL,
    monitorunitid      integer NOT NULL,
    fsuid              character varying(255) DEFAULT ''::CHARACTER VARYING,
    fsuname            character varying(255),
    siteid             character varying(255) DEFAULT ''::CHARACTER VARYING,
    sitename           character varying(255),
    roomid             character varying(255),
    roomname           character varying(255),
    username           character varying(40),
    password           character varying(40),
    fsuip              character varying(255),
    fsumac             character varying(20),
    fsuver             character varying(20),
    result             integer,
    failurecause       character varying(255),
    cpuusage           double precision,
    memusage           double precision,
    harddiskusage      double precision,
    getfsuinforesult   integer,
    getfsufaliurecause character varying(255),
    getfsutime         timestamp,
    ftpusername        character varying(40),
    ftppassword        character varying(40),
    extendfield1       character varying(255),
    extendfield2       character varying(255),
    getconfigflag      integer                DEFAULT 0
);
ALTER TABLE ONLY tsl_monitorunitcmcc ADD CONSTRAINT idx_24270_primary PRIMARY KEY (monitorunitid, stationid);
CREATE INDEX idx_24270_idx_monitorunitcmcc_1 ON tsl_monitorunitcmcc USING BTREE (fsuid);
CREATE INDEX idx_24270_tsl_monitorunitcmcc_idx1 ON tsl_monitorunitcmcc USING BTREE (stationid, monitorunitid);

CREATE TABLE tsl_monitorunitctcc
(
    stationid         integer NOT NULL,
    monitorunitid     integer NOT NULL,
    suid              character varying(255),
    suname            character varying(255),
    username          character varying(40),
    password          character varying(40),
    suip              character varying(255),
    suport            integer,
    suvendor          character varying(255),
    sumodel           character varying(255),
    suhardver         character varying(20),
    susoftver         character varying(20),
    scip              character varying(255),
    scport            integer,
    whiteips          character varying(255),
    result            integer,
    failurecause      character varying(255),
    suftpusername     character varying(40),
    suftppassword     character varying(40),
    suftpport         integer,
    cpuusage          double precision,
    memusage          double precision,
    sudatetime        timestamp,
    harddiskusage     double precision,
    getsuinforesult   integer,
    getsufaliurecause character varying(255),
    extendfield1      character varying(255),
    extendfield2      character varying(255),
    getconfigflag     integer DEFAULT 0
);
ALTER TABLE ONLY tsl_monitorunitctcc ADD CONSTRAINT idx_24288_primary PRIMARY KEY (stationid, monitorunitid);

CREATE TABLE tsl_monitorunitcucc
(
    stationid        integer NOT NULL,
    monitorunitid    integer NOT NULL,
    suid             character varying(255),
    suname           character varying(255),
    surid            character varying(255),
    username         character varying(40),
    password         character varying(40),
    suip             character varying(255),
    suver            character varying(20),
    suport           character varying(20),
    suvendor         character varying(20),
    sumodel          character varying(20),
    suhardver        character varying(20),
    longitude        double precision,
    latitude         double precision,
    result           integer,
    failurecause     character varying(255),
    cpuusage         double precision,
    memusage         double precision,
    getsuinforesult  integer,
    getsutime        timestamp,
    ftpusername      character varying(40),
    ftppassword      character varying(40),
    configstate      integer,
    registertime     timestamp,
    suconfigtime     character varying(255),
    centerconfigtime character varying(255),
    devices          text,
    extendfield1     character varying(255),
    extendfield2     character varying(255)
);
ALTER TABLE ONLY tsl_monitorunitcucc ADD CONSTRAINT idx_24294_primary PRIMARY KEY (monitorunitid, stationid);
CREATE INDEX idx_24294_tsl_monitorunitcucc_idx1 ON tsl_monitorunitcucc USING BTREE (stationid, monitorunitid);

CREATE TABLE tsl_monitorunitgdctcc
(
    suid          character varying(40)  NOT NULL PRIMARY KEY,
    suname        character varying(128) NOT NULL,
    monitorunitid integer,
    serialno      integer,
    reqfactorycfg integer DEFAULT 0      NOT NULL,
    extendfield1  character varying(128),
    extendfield2  character varying(128)
);
