CREATE TABLE chartapi
(
    apiid       serial                  NOT NULL PRIMARY KEY,
    apiname     character varying(128)  NOT NULL,
    category    character varying(45)   NOT NULL,
    url         character varying(1024) NOT NULL,
    method      character varying(128)  NOT NULL,
    paramschema json                    NOT NULL,
    transform   character varying(4096) NOT NULL
);

CREATE TABLE chartstyle
(
    styleid    serial                NOT NULL PRIMARY KEY,
    stylename  character varying(45) NOT NULL,
    chartid    integer               NOT NULL,
    thumbnail  text,
    expression text                  NOT NULL
);
CREATE UNIQUE INDEX idx_21618_chartstyle_stylename ON chartstyle USING BTREE (stylename);

CREATE TABLE charttheme
(
    themeid      serial                NOT NULL PRIMARY KEY,
    themename    character varying(45) NOT NULL,
    themecode    character varying(45) NOT NULL,
    themedata    json                  NOT NULL,
    themedefault integer
);
CREATE UNIQUE INDEX idx_21625_charttheme_themename ON charttheme USING BTREE (themecode);

CREATE TABLE commonobject
(
    commonobjectid   serial NOT NULL PRIMARY KEY,
    commonobjectname character varying(255),
    description      character varying(255),
    photo            character varying(255)
);
COMMENT ON COLUMN commonobject.commonobjectname IS '通用对象名称';
COMMENT ON COLUMN commonobject.description IS '描述';
COMMENT ON COLUMN commonobject.photo IS '图片';
SELECT setval('commonobject_commonobjectid_seq', 9999, true);

CREATE TABLE compdatacolumn
(
    compdatacolumnid serial NOT NULL PRIMARY KEY,
    compdatatableid  integer,
    columnname       character varying(50),
    columnfield      character varying(50),
    valuetype        smallint
);

CREATE TABLE compdataset
(
    compdatasetid   serial NOT NULL PRIMARY KEY,
    compdatasetname character varying(50),
    url             character varying(200)
);

CREATE TABLE compdatatable
(
    compdatatableid   serial NOT NULL PRIMARY KEY,
    compdatatablename character varying(50),
    compdatasetid     integer
);

CREATE TABLE compskin
(
    compskinid    serial NOT NULL PRIMARY KEY,
    compdatasetid integer,
    skinname      character varying(50),
    content       text
);

CREATE TABLE graphicpage
(
    id                serial                 NOT NULL PRIMARY KEY,
    type              character varying(255),
    groupid           integer,
    pagecategory      integer,
    baseequipmentid   integer,
    objectid          integer,
    name              character varying(255) NOT NULL,
    appendname        character varying(255),
    templateid        integer,
    data              text,
    style             text,
    children          text,
    description       character varying(255),
    routertype        character varying(255),
    crumbsroutertype  character varying(255),
    pagecanfullscreen integer,
    sceneid           integer,
    isdefault         integer,
    updatetime        timestamp
);
COMMENT ON COLUMN graphicpage.isdefault IS '是否默认组态页';
COMMENT ON COLUMN graphicpage.updatetime IS '最新修改时间';
CREATE INDEX idx_22059_idx_graphicpage_1 ON graphicpage USING BTREE (pagecategory, objectid);

CREATE TABLE graphicpagetemplate
(
    id               serial                 NOT NULL PRIMARY KEY,
    type             character varying(255),
    groupid          integer,
    pagecategory     integer,
    baseequipmentid  integer,
    internal         smallint,
    name             character varying(255) NOT NULL,
    appendname       character varying(255),
    data             text,
    style            text,
    children         text,
    templatecategory integer,
    description      character varying(255)
);
CREATE INDEX idx_22066_idx_graphicpagetemplate_1 ON graphicpagetemplate USING BTREE (pagecategory, baseequipmentid);

CREATE TABLE graphictemplate
(
    graphictemplateid       serial NOT NULL PRIMARY KEY,
    graphictemplatename     character varying(128),
    graphictemplatetag      character varying(128),
    graphictemplatecomptype integer,
    graphictemplatetypeid   integer,
    graphictemplatetype     character varying(128),
    graphictemplatetypename character varying(128),
    graphictemplatecover    character varying(128),
    graphictemplateconfig   text,
    createtime              timestamp
);
COMMENT ON COLUMN graphictemplate.graphictemplatename IS '模板名称';
COMMENT ON COLUMN graphictemplate.graphictemplatetag IS '模板标签';
COMMENT ON COLUMN graphictemplate.graphictemplatecomptype IS '组件类型';
COMMENT ON COLUMN graphictemplate.graphictemplatetypeid IS '组件id';
COMMENT ON COLUMN graphictemplate.graphictemplatetype IS '组件英文名称';
COMMENT ON COLUMN graphictemplate.graphictemplatetypename IS '组件中文名称';
COMMENT ON COLUMN graphictemplate.graphictemplatecover IS '模板缩略图';
COMMENT ON COLUMN graphictemplate.graphictemplateconfig IS '模板配置';
COMMENT ON COLUMN graphictemplate.createtime IS '创建时间';

CREATE TABLE matrixchart
(
    matrixchartid serial NOT NULL PRIMARY KEY,
    name          character varying(50),
    path          character varying(1024),
    datatype      integer
);

CREATE TABLE systemnavigation
(
    id         serial                NOT NULL PRIMARY KEY,
    name       character varying(50) NOT NULL,
    businessid character varying(50),
    pageid     integer,
    sortvalue  integer
);

CREATE TABLE usergraphicpagemap
(
    usergraphicpagemapid serial NOT NULL PRIMARY KEY,
    userid               integer,
    graphicpageid        integer,
    config               text
);