CREATE TABLE tbl_saralarmactiverecord
(
    stationid         integer                NOT NULL,
    stationcategoryid integer                NOT NULL,
    equipmentid       integer                NOT NULL,
    eventid           integer                NOT NULL,
    eventconditionid  integer                NOT NULL,
    sequenceid        character varying(128) NOT NULL primary key,
    starttime         timestamp NOT NULL,
    endtime           timestamp,
    overturn          integer,
    meanings          character varying(255),
    eventvalue        double precision,
    endvalue          double precision,
    basetypeid        numeric(12, 0),
    standardid        integer,
    insertdatetime    timestamp NOT NULL,
    relationtype      integer                NOT NULL
);

CREATE TABLE tbl_saralarmqueue
(
    stationid         integer                NOT NULL,
    stationcategoryid integer                NOT NULL,
    equipmentid       integer                NOT NULL,
    eventid           integer                NOT NULL,
    eventconditionid  integer                NOT NULL,
    sequenceid        character varying(128) NOT NULL primary key,
    starttime         timestamp NOT NULL,
    endtime           timestamp,
    overturn          integer,
    meanings          character varying(255),
    eventvalue        double precision,
    endvalue          double precision,
    basetypeid        numeric(12, 0),
    standardid        integer,
    insertdatetime    timestamp NOT NULL
);

CREATE TABLE tbl_saralarmrecordstarttime
(
    SarAlarmRecordStartTimeId serial PRIMARY KEY,
    starttime    timestamp,
    relationtype integer
);

CREATE TABLE tbl_saralarmrelation
(
    SarAlarmRecordStartTimeId serial PRIMARY KEY,
    stationid             integer,
    equipmentid           integer,
    eventid               integer,
    eventconditionid      integer,
    starttime             timestamp,
    standardid            integer,
    causestationid        integer,
    causeequipmentid      integer,
    causeeventid          integer,
    causeeventconditionid integer,
    causestarttime        timestamp,
    causestandardid       integer,
    relationtype          integer
);

CREATE TABLE tbl_sarderivatealarmrule
(
    SarDerivateAlarmRuleId serial PRIMARY KEY,
    ruleid         integer                NOT NULL,
    rulename       character varying(255) NOT NULL,
    alarmcount     integer                NOT NULL,
    alarmendcount  integer                NOT NULL,
    alarmtimescope integer                NOT NULL,
    description    character varying(255)
);

CREATE TABLE tbl_sarisprocess
(
    SarIsProcessId serial PRIMARY KEY,
    isprocess integer
);

CREATE TABLE tbl_sarreversealarmrule
(
    startthreshold integer NOT NULL,
    endthreshold   integer NOT NULL,
    timethreshold  integer NOT NULL
);
alter table tbl_sarreversealarmrule add CONSTRAINT tbl_sarreversealarmrule_primary PRIMARY KEY (StartThreshold,EndThreshold,TimeThreshold);

CREATE TABLE tbl_sarsecondaryalarmbyfilter
(
    SarSecondaryAlarmByFilter serial PRIMARY KEY,
    seconarysequenceid text,
    stationid          integer,
    equipmentid        integer,
    eventid            integer,
    eventconditionid   integer,
    starttime          timestamp,
    primarysequenceid  text,
    insertdatetime     timestamp
);

CREATE TABLE tbl_sarstationderivatemap
(
    ruleid             integer NOT NULL PRIMARY KEY,
    derivatebasetypeid integer NOT NULL,
    basetypeid         integer NOT NULL,
    stationcategoryid  integer NOT NULL
);

CREATE TABLE tbl_sarstationprimarymap
(
    primaryid         integer NOT NULL,
    stationcategoryid integer NOT NULL
);
alter table tbl_sarstationprimarymap add CONSTRAINT tbl_sarstationprimarymap_primary PRIMARY KEY (primaryid, stationcategoryid);