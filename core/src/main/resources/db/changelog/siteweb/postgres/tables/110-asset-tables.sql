CREATE TABLE assetcategory
(
    assetcategoryid   serial NOT NULL PRIMARY KEY,
    assetcategoryname character varying(128),
    remarks           character varying(255)
);
COMMENT ON COLUMN assetcategory.assetcategoryname IS '资产类型名称';
COMMENT ON COLUMN assetcategory.remarks IS '备注';

CREATE TABLE assetdevice
(
    assetdeviceid     serial NOT NULL PRIMARY KEY,
    sortindex         integer,
    assetcode         character varying(128),
    assetname         character varying(128),
    assetcategoryid   integer,
    brand             character varying(128),
    model             character varying(128),
    capacityparameter character varying(128),
    settingposition   character varying(128),
    serialnumber      character varying(128),
    manufactor        character varying(128),
    tablename         character varying(128)
);
COMMENT ON COLUMN assetdevice.sortindex IS '排序';
COMMENT ON COLUMN assetdevice.assetcode IS '资产编号';
COMMENT ON COLUMN assetdevice.assetname IS '资产名称';
COMMENT ON COLUMN assetdevice.assetcategoryid IS '资产类型';
COMMENT ON COLUMN assetdevice.brand IS '品牌';
COMMENT ON COLUMN assetdevice.model IS '型号';
COMMENT ON COLUMN assetdevice.capacityparameter IS '容量参数';
COMMENT ON COLUMN assetdevice.settingposition IS '安装位置';
COMMENT ON COLUMN assetdevice.serialnumber IS '序列号';
COMMENT ON COLUMN assetdevice.manufactor IS '厂家';
COMMENT ON COLUMN assetdevice.tablename IS '关联表';

CREATE TABLE extfieldconfiguration
(
    extid         serial NOT NULL PRIMARY KEY,
    exttable      character varying(50),
    extcode       character varying(50),
    extname       character varying(100),
    extdesc       character varying(200),
    extorder      integer,
    extnecessary  smallint,
    extdatatype   character varying(50),
    extdatasource character varying(300)
);
COMMENT ON COLUMN extfieldconfiguration.exttable IS '扩展关联表';
COMMENT ON COLUMN extfieldconfiguration.extcode IS '扩展编码';
COMMENT ON COLUMN extfieldconfiguration.extname IS '扩展名称';
COMMENT ON COLUMN extfieldconfiguration.extdesc IS '扩展描述';
COMMENT ON COLUMN extfieldconfiguration.extorder IS '扩展序号';
COMMENT ON COLUMN extfieldconfiguration.extnecessary IS '扩展字段是否必须';
COMMENT ON COLUMN extfieldconfiguration.extdatatype IS '扩展字段数据类型';
COMMENT ON COLUMN extfieldconfiguration.extdatasource IS '扩展字段数据来源';

CREATE TABLE extvalueconfiguration (
                                              extvalid serial NOT NULL PRIMARY KEY ,
                                              extid integer,
                                              exttable character varying(50),
                                              exttablepkid integer,
                                              extvalue character varying(1000)
);
COMMENT ON COLUMN extvalueconfiguration.extid IS '扩展字段id';
COMMENT ON COLUMN extvalueconfiguration.exttable IS '扩展字段值关联表';
COMMENT ON COLUMN extvalueconfiguration.exttablepkid IS '扩展字段关联表主键id';
COMMENT ON COLUMN extvalueconfiguration.extvalue IS '扩展字段值';