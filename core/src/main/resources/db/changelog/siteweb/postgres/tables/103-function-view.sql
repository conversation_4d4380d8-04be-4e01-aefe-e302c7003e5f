DROP function IF EXISTS fuct_getdeviceposition;
-- 通过层级id获取设备层级信息，使用的地方过多，减少成本，直接改造
-- DROP FUNCTION siteweb_ali.fuct_getdeviceposition(int4);
CREATE OR REPLACE FUNCTION fuct_getdeviceposition(structureid integer)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
DECLARE
    tempParentId VARCHAR(256);
tempParent VARCHAR(510);
tempId INT;
tempArray int[];
begin
-- 使用异常处理来捕获无记录的情况
BEGIN
SELECT REPLACE(levelofpath, '.', ','), resourcestructureId
INTO tempParentId, tempId
FROM resourcestructure
WHERE resourcestructureId = StructureId;
EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN NULL; -- 如果数据库中层级不存在，则直接返回null
END;

    -- 检查记录是否有效
IF tempId IS NULL OR tempId = 0 THEN
        RETURN tempParent;
END IF;

    -- 去除根节点的名称
tempParentId := substring(tempParentId from position(',' in tempParentId) + 1);

    -- 如果处理后为空，直接返回
IF tempParentId IS NULL OR tempParentId = '' THEN
        RETURN NULL;
END IF;

    -- 将 tempParentId 转换为数组
tempArray := ARRAY(SELECT UNNEST(string_to_array(tempParentId, ','))::INT);

    -- 通过 unnest 和 string_agg 实现类似 GROUP_CONCAT 的功能
SELECT string_agg(c.resourcestructureName, '_')
INTO tempParent
FROM resourcestructure c
WHERE c.resourcestructureId = ANY(tempArray);

RETURN COALESCE(tempParent, ''); -- 如果没有找到任何名称，返回空字符串
END;
$function$;