CREATE TABLE linkconfig
(
    linkconfigid    serial                 NOT NULL PRIMARY KEY,
    configname      character varying(128) NOT NULL,
    usedstatus      integer                NOT NULL,
    linkgroupid     integer                NOT NULL,
    linktriggertype integer                NOT NULL,
    cron            character varying(100),
    starttime       timestamp,
    endtime         timestamp,
    layout          text,
    description     character varying(255),
    updatetime      timestamp
);
COMMENT ON COLUMN linkconfig.cron IS 'corn表达式用于定时执行后台联动';

CREATE TABLE linkconfigtemplate
(
    linkconfigtemplateid serial                 NOT NULL PRIMARY KEY,
    templatename         character varying(128) NOT NULL,
    content              text                   NOT NULL,
    description          character varying(255)
);

CREATE TABLE linkelement
(
    elementid        serial                 NOT NULL PRIMARY KEY,
    elementname      character varying(128) NOT NULL,
    elementtype      character varying(64)  NOT NULL,
    inputnodescount  integer                NOT NULL,
    outputnodescount integer                NOT NULL,
    icon             character varying(64),
    expression       character varying(500),
    visible          integer                NOT NULL,
    sortindex        integer                NOT NULL,
    description      character varying(255)
);

CREATE TABLE linkelementconfig
(
    linkelementconfigid serial  NOT NULL PRIMARY KEY,
    linkconfigid        integer NOT NULL,
    elementid           integer NOT NULL,
    expression          character varying(1000),
    extendfield1        character varying(500),
    extendfield2        character varying(500),
    extendfield3        character varying(500)
);

CREATE TABLE linkgroup
(
    groupid     serial                 NOT NULL PRIMARY KEY,
    groupname   character varying(128) NOT NULL,
    description character varying(255)
);

CREATE TABLE linkinstance
(
    linkinstanceid serial                 NOT NULL PRIMARY KEY,
    linkconfigid   integer                NOT NULL,
    updatetime     timestamp,
    status         integer                NOT NULL,
    statusresult   character varying(128) NOT NULL,
    description    character varying(255)
);

CREATE TABLE linknode
(
    nodeid              serial                NOT NULL PRIMARY KEY,
    linkelementconfigid integer               NOT NULL,
    nodedirection       character varying(10) NOT NULL,
    nodetype            character varying(64) NOT NULL,
    nodeindex           integer               NOT NULL,
    nodetag             character varying(255),
    expression          character varying(500)
);

CREATE TABLE linksegment
(
    segmentid                 serial  NOT NULL PRIMARY KEY,
    inputlinkelementconfigid  integer NOT NULL,
    inputnodeid               integer NOT NULL,
    outputlinkelementconfigid integer NOT NULL,
    outputnodeid              integer NOT NULL
);