CREATE TABLE pd_prealarmreportrecord
(
    recordid            serial  NOT NULL PRIMARY KEY,
    prealarmid          integer NOT NULL,
    prealarmpointid     integer,
    prealarmseverity    integer,
    prealarmcategory    integer,
    objectid            integer,
    objecttypeid        integer,
    resourcestructureid integer,
    triggervalue        character varying(128),
    reportpath          character varying(512),
    reportfilename      character varying(255),
    historydatapath     character varying(512),
    historydatafilename character varying(255),
    inserttime          timestamp,
    generatetime        timestamp,
    generateresult      integer,
    deletetime          timestamp,
    extendfield         character varying(255)
);
COMMENT ON COLUMN pd_prealarmreportrecord.generateresult IS '生成结果：0-等待生成中；1-生成成功；2-生成失败；3-已删除；-1及其他-未知状态';
CREATE UNIQUE INDEX idx_22292_idx_pd_prealarmreportrecord_1 ON pd_prealarmreportrecord USING BTREE (prealarmid);

CREATE TABLE powersimulationrecord
(
    recordid         serial                 NOT NULL PRIMARY KEY,
    recordname       character varying(225) NOT NULL,
    diagramid        integer                NOT NULL,
    nodeid           character varying(225) NOT NULL,
    onoffstate       integer                NOT NULL,
    downloadpath     character varying(225) NOT NULL,
    attributeobjects text                   NOT NULL,
    reportname       character varying(225) NOT NULL
);