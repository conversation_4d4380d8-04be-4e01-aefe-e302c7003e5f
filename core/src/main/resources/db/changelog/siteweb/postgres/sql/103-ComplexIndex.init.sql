-- 指标业务分类
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (1, '能耗', 0, null);
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (2, '电能耗', 1, '1');
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (3, '水能耗', 1, '3');
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (4, '气能耗', 1, '16');
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (5, '其他', 0, null);
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (6, '容量', 0, null);
insert into ComplexIndexBusinessType(BusinessTypeId,BusinessTypeName,ParentId,Description) values (7, '电力', 0, null);

-- 指标定义
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(1,'功率',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(2,'电压',null,null,null,null,null,'V',null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(3,'电流',null,null,null,null,null,'A',null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(4,'监控可用率',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(5,'PUE',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(6,'总用电量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(7,'IT设备用电量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(8,'UPS用电量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(9,'空调用电量',null,null,null,null,null,null,null,null,null,null);
-- insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(10,'总设备能耗',null,null,null,null,null,null,null,null,null,null);
-- insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(11,'IT设备能耗',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(12,'机柜数',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(13,'额定功率',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(14,'PLF',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(15,'CLF',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(16,'ALF',null,null,null,null,null,null,null,null,null,null);
-- insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(17,'额定功率',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(18,'列间温度',null,null,null,null,null,'℃',null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(19,'列间湿度',null,null,null,null,null,'RH',null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(20,'电负载率',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(21,'办公照明用电量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(22,'油机用电量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(23,'其他用电量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(24,'总用水量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(25,'WUE',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(26,'用水类型1',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(27,'用水类型2',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(28,'用水类型3',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(29,'用水类型4',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(30,'用水类型5',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(31,'水压',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(32,'总用气量',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(33,'用气效率指标',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(34,'用气类型1',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(35,'用气类型2',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(36,'用气类型3',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(37,'用气类型4',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(38,'用气类型5',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(39,'气压',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(40,'锂电池单体温升速率',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(41,'锂电池单体温度与同组电池平均温度差值',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(42,'锂电池单体电压与同组电池平均电压差值',null,null,null,null,null,null,null,null,null,null);
-- 现场库有增加的指标，跳id
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(100,'总电力',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(101,'IT电力',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(102,'制冷负荷',null,null,null,null,null,null,null,null,null,null);
insert into ComplexIndexDefinition(ComplexIndexDefinitionId,ComplexIndexDefinitionName,CalcCron,CalcType,AfterCalc,SaveCron,Expression,Unit,Accuracy,StartStatus,CheckExpression,Description) values(103,'市电进线负载',null,null,null,null,null,null,null,null,null,null);

insert into complexIndexFunction values (1, 'max(参数1,参数2,...)', '求多参数的最大值', 'max', '计算表达式使用');
insert into complexIndexFunction values (2, 'min(参数1,参数2,...)', '求多参数的最小值', 'min', '计算表达式使用');
insert into complexIndexFunction values (3, 'avg(参数1,参数2,...)', '求多参数的平均值', 'avg', '计算表达式使用');
insert into complexIndexFunction values (4, 'abs(信号ID)', '取实时信号值的绝对值', 'abs', '默认类型：浮点数');
insert into complexIndexFunction values (5, 'last(指标ID)', '某个指标的最新历史值', 'last', '默认类型：浮点数');
-- insert into complexIndexFunction values (6, 'powerfee(电量参数)', '将根据阶梯电价计算周期(计算周期)电费', 'powerfee', '只能用于后计算表达式');
-- insert into complexIndexFunction values (7, 'selfval()', '本指标的当前计算结果', 'selfval', '只能用于后计算表达式');
-- insert into complexIndexFunction values (8, 'x in (值1，值2，...)', 'x的计算结果是否是括号内值的集合中的一个', 'x in', '');
insert into complexIndexFunction values (9, 'cp(信号ID)', '某个信号的当前值', 'cp', '默认类型：浮点数');
insert into complexIndexFunction values (10, 'ci(指标ID)', '某个指标的当前值', 'ci', '默认类型：浮点数');
insert into complexIndexFunction values (11, 'ca(属性ID)', '某个属性的容量', 'ca', '默认类型：浮点数');
insert into complexIndexFunction values (12, 'lastDayAvg(指标ID)', '某个指标最后一天的平均值', 'lastDayAvg', '');
insert into complexIndexFunction values (13, 'lastDayAvgRange(指标ID,n天)', '某个指标最后一天的平均值与前n天平均值增加或减少的范围', 'lastDayAvgRange', '');
insert into complexIndexFunction values (14, 'lastDaySum(指标ID)', '某个指标最后一天的历史值', 'lastDaySum', '');
insert into complexIndexFunction values (15, 'lastDaySumRange(指标ID,n天)', '某个指标最后一天的历史值与前n天历史值增加或减少的范围', 'lastDaySumRange', '');
insert into complexIndexFunction values (16, 'recentdays(指标ID或信号,n天)', '求某个指标或信号最近n天的值总和，例如：recentdays(101,7)，指标101最近7天的值。recentdays(1011111.1111,7)：设备信号1011111.1111最近7天的值', 'recentdays', '');
insert into complexIndexFunction values (17, 'yoy(指标ID,月或天)', '同比增长率:一般是指和上年同期指标相比较的增长率.例子：yoy(101,0) 101是指标ID,0表示月,1表示天', 'yoy', '');
insert into complexIndexFunction values (18, 'cr(指标ID,月或天)', '环比增长率:连续2个统计周期内的量的变化比.例子：cr(101,0) 101是指标ID,0表示月,1表示天', 'cr', '');
insert into complexIndexFunction values (19, 'mddifference(指标ID或信号,月或天)', '求某个指标或信号当月或当天的差值，例如：mddifference(101,0)，指标101当天的差值。mddifference(1011111.1111,1)：设备信号1011111.1111当月的差值,0-当天,1-月', 'mddifference', '');


-- 场景、对象类型、业务类型、指标定义关联映射表
-- 字段（BusinessObjectMapId 自增长主键，SceneId 场景id，BusinessTypeId 业务类型id，ComplexIndexDefinitionId 指标定义id，ObjectTypeId 资源类型id）
-- IDC场景 园区 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）*/
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (1, 1, 2, 1, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (2, 1, 2, 2, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (3, 1, 2, 3, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (4, 1, 2, 5, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (5, 1, 2, 6, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (6, 1, 2, 7, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (7, 1, 2, 8, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (8, 1, 2, 9, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (9, 1, 2, 13, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (10, 1, 2, 14, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (11, 1, 2, 15, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (12, 1, 2, 16, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (13, 1, 2, 20, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (14, 1, 2, 21, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (15, 1, 2, 22, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (16, 1, 2, 23, 2);

-- IDC场景 园区 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (17, 1, 3, 24, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (18, 1, 3, 25, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (19, 1, 3, 26, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (20, 1, 3, 27, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (21, 1, 3, 28, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (22, 1, 3, 29, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (23, 1, 3, 30, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (24, 1, 3, 31, 2);

-- IDC场景 园区 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (25, 1, 4, 32, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (26, 1, 4, 33, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (27, 1, 4, 34, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (28, 1, 4, 35, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (29, 1, 4, 36, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (30, 1, 4, 37, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (31, 1, 4, 38, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (32, 1, 4, 39, 2);

-- IDC场景 园区 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (33, 1, 5, 4, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (34, 1, 5, 12, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (35, 1, 5, 18, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (36, 1, 5, 19, 2);


-- IDC场景 大楼 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (41, 1, 2, 1, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (42, 1, 2, 2, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (43, 1, 2, 3, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (44, 1, 2, 5, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (45, 1, 2, 6, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (46, 1, 2, 7, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (47, 1, 2, 8, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (48, 1, 2, 9, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (49, 1, 2, 13, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (50, 1, 2, 14, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (51, 1, 2, 15, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (52, 1, 2, 16, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (53, 1, 2, 20, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (54, 1, 2, 21, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (55, 1, 2, 22, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (56, 1, 2, 23, 3);

-- IDC场景 大楼 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (57, 1, 3, 24, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (58, 1, 3, 25, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (59, 1, 3, 26, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (60, 1, 3, 27, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (61, 1, 3, 28, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (62, 1, 3, 29, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (63, 1, 3, 30, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (64, 1, 3, 31, 3);

-- IDC场景 大楼 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (65, 1, 4, 32, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (66, 1, 4, 33, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (67, 1, 4, 34, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (68, 1, 4, 35, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (69, 1, 4, 36, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (70, 1, 4, 37, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (71, 1, 4, 38, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (72, 1, 4, 39, 3);

-- IDC场景 大楼 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (73, 1, 5, 4, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (74, 1, 5, 12, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (75, 1, 5, 18, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (76, 1, 5, 19, 3);



-- IDC场景 楼层 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (81, 1, 2, 1, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (82, 1, 2, 2, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (83, 1, 2, 3, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (84, 1, 2, 5, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (85, 1, 2, 6, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (86, 1, 2, 7, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (87, 1, 2, 8, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (88, 1, 2, 9, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (89, 1, 2, 13, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (90, 1, 2, 14, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (91, 1, 2, 15, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (92, 1, 2, 16, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (93, 1, 2, 20, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (94, 1, 2, 21, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (95, 1, 2, 22, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (96, 1, 2, 23, 4);

-- IDC场景 楼层 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (97, 1, 3, 24, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (98, 1, 3, 25, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (99, 1, 3, 26, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (100, 1, 3, 27, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (101, 1, 3, 28, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (102, 1, 3, 29, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (103, 1, 3, 30, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (104, 1, 3, 31, 4);

-- IDC场景 楼层 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (105, 1, 4, 32, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (106, 1, 4, 33, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (107, 1, 4, 34, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (108, 1, 4, 35, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (109, 1, 4, 36, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (110, 1, 4, 37, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (111, 1, 4, 38, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (112, 1, 4, 39, 4);

-- IDC场景 楼层 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (113, 1, 5, 4, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (114, 1, 5, 12, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (115, 1, 5, 18, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (116, 1, 5, 19, 4);


-- IDC场景 机房 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (121, 1, 2, 1, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (122, 1, 2, 2, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (123, 1, 2, 3, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (124, 1, 2, 5, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (125, 1, 2, 6, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (126, 1, 2, 7, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (127, 1, 2, 8, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (128, 1, 2, 9, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (129, 1, 2, 13, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (130, 1, 2, 14, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (131, 1, 2, 15, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (132, 1, 2, 16, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (133, 1, 2, 20, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (134, 1, 2, 21, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (135, 1, 2, 22, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (136, 1, 2, 23, 5);

-- IDC场景 机房 水能耗 可配置指标（）*
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (137, 1, 3, 24, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (138, 1, 3, 25, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (139, 1, 3, 26, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (140, 1, 3, 27, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (141, 1, 3, 28, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (142, 1, 3, 29, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (143, 1, 3, 30, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (144, 1, 3, 31, 5);

/*IDC场景 机房 气能耗 可配置指标（）*/
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (145, 1, 4, 32, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (146, 1, 4, 33, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (147, 1, 4, 34, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (148, 1, 4, 35, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (149, 1, 4, 36, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (150, 1, 4, 37, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (151, 1, 4, 38, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (152, 1, 4, 39, 5);

-- IDC场景 机房 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (153, 1, 5, 4, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (154, 1, 5, 12, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (155, 1, 5, 18, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (156, 1, 5, 19, 5);



-- IDC场景 MDC 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (157, 1, 2, 1, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (158, 1, 2, 2, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (159, 1, 2, 3, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (160, 1, 2, 5, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (161, 1, 2, 6, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (162, 1, 2, 7, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (163, 1, 2, 8, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (164, 1, 2, 9, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (165, 1, 2, 13, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (166, 1, 2, 14, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (167, 1, 2, 15, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (168, 1, 2, 16, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (169, 1, 2, 20, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (170, 1, 2, 21, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (171, 1, 2, 22, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (172, 1, 2, 23, 6);

-- IDC场景 MDC 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (173, 1, 3, 24, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (174, 1, 3, 25, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (175, 1, 3, 26, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (176, 1, 3, 27, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (177, 1, 3, 28, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (178, 1, 3, 29, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (179, 1, 3, 30, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (180, 1, 3, 31, 6);
-- IDC场景 MDC 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (181, 1, 4, 32, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (182, 1, 4, 33, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (183, 1, 4, 34, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (184, 1, 4, 35, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (185, 1, 4, 36, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (186, 1, 4, 37, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (187, 1, 4, 38, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (188, 1, 4, 39, 6);


-- IDC场景 MDC 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (189, 1, 5, 4, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (190, 1, 5, 12, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (191, 1, 5, 18, 6);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (192, 1, 5, 19, 6);




-- 电信场景 地市 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (201, 2, 2, 1, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (202, 2, 2, 2, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (203, 2, 2, 3, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (204, 2, 2, 5, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (205, 2, 2, 6, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (206, 2, 2, 7, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (207, 2, 2, 8, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (208, 2, 2, 9, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (209, 2, 2, 13, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (210, 2, 2, 14, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (211, 2, 2, 15, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (212, 2, 2, 16, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (213, 2, 2, 20, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (214, 2, 2, 21, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (215, 2, 2, 22, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (216, 2, 2, 23, 102);

-- 电信场景 地市 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (217, 2, 3, 24, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (218, 2, 3, 25, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (219, 2, 3, 26, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (220, 2, 3, 27, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (221, 2, 3, 28, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (222, 2, 3, 29, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (223, 2, 3, 30, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (224, 2, 3, 31, 102);

-- 电信场景 地市 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (225, 2, 4, 32, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (226, 2, 4, 33, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (227, 2, 4, 34, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (228, 2, 4, 35, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (229, 2, 4, 36, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (230, 2, 4, 37, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (231, 2, 4, 38, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (232, 2, 4, 39, 102);

-- 电信场景 地市 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (233, 2, 5, 4, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (234, 2, 5, 12, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (235, 2, 5, 18, 102);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (236, 2, 5, 19, 102);


-- 电信场景 片区 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (241, 2, 2, 1, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (242, 2, 2, 2, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (243, 2, 2, 3, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (244, 2, 2, 5, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (245, 2, 2, 6, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (246, 2, 2, 7, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (247, 2, 2, 8, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (248, 2, 2, 9, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (249, 2, 2, 13, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (250, 2, 2, 14, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (251, 2, 2, 15, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (252, 2, 2, 16, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (253, 2, 2, 20, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (254, 2, 2, 21, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (255, 2, 2, 22, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (256, 2, 2, 23, 103);

-- 电信场景 片区 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (257, 2, 3, 24, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (258, 2, 3, 25, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (259, 2, 3, 26, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (260, 2, 3, 27, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (261, 2, 3, 28, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (262, 2, 3, 29, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (263, 2, 3, 30, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (264, 2, 3, 31, 103);

-- 电信场景 片区 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (265, 2, 4, 32, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (266, 2, 4, 33, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (267, 2, 4, 34, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (268, 2, 4, 35, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (269, 2, 4, 36, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (270, 2, 4, 37, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (271, 2, 4, 38, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (272, 2, 4, 39, 103);

-- 电信场景 片区 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (273, 2, 5, 4, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (274, 2, 5, 12, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (275, 2, 5, 18, 103);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (276, 2, 5, 19, 103);



-- 电信场景 基站 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (281, 2, 2, 1, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (282, 2, 2, 2, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (283, 2, 2, 3, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (284, 2, 2, 5, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (285, 2, 2, 6, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (286, 2, 2, 7, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (287, 2, 2, 8, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (288, 2, 2, 9, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (289, 2, 2, 13, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (290, 2, 2, 14, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (291, 2, 2, 15, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (292, 2, 2, 16, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (293, 2, 2, 20, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (294, 2, 2, 21, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (295, 2, 2, 22, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (296, 2, 2, 23, 104);

-- 电信场景 基站 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (297, 2, 3, 24, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (298, 2, 3, 25, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (299, 2, 3, 26, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (300, 2, 3, 27, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (301, 2, 3, 28, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (302, 2, 3, 29, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (303, 2, 3, 30, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (304, 2, 3, 31, 104);

-- 电信场景 基站 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (305, 2, 4, 32, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (306, 2, 4, 33, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (307, 2, 4, 34, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (308, 2, 4, 35, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (309, 2, 4, 36, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (310, 2, 4, 37, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (311, 2, 4, 38, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (312, 2, 4, 39, 104);

-- 电信场景 基站 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (313, 2, 5, 4, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (314, 2, 5, 12, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (315, 2, 5, 18, 104);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (316, 2, 5, 19, 104);


-- 电信场景 局房 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (321, 2, 2, 1, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (322, 2, 2, 2, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (323, 2, 2, 3, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (324, 2, 2, 5, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (325, 2, 2, 6, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (326, 2, 2, 7, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (327, 2, 2, 8, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (328, 2, 2, 9, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (329, 2, 2, 13, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (330, 2, 2, 14, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (331, 2, 2, 15, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (332, 2, 2, 16, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (333, 2, 2, 20, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (334, 2, 2, 21, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (335, 2, 2, 22, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (336, 2, 2, 23, 105);

/*电信场景 局房 水能耗 可配置指标（）*/
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (337, 2, 3, 24, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (338, 2, 3, 25, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (339, 2, 3, 26, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (340, 2, 3, 27, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (341, 2, 3, 28, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (342, 2, 3, 29, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (343, 2, 3, 30, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (344, 2, 3, 31, 105);

-- 电信场景 局房 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (345, 2, 4, 32, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (346, 2, 4, 33, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (347, 2, 4, 34, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (348, 2, 4, 35, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (349, 2, 4, 36, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (350, 2, 4, 37, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (351, 2, 4, 38, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (352, 2, 4, 39, 105);

-- 电信场景 局房 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (353, 2, 5, 4, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (354, 2, 5, 12, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (355, 2, 5, 18, 105);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (356, 2, 5, 19, 105);

-- 电信场景 省份 电能耗 可配置指标（电压、电流、PUE、总用电量、IT设备用电量、功率、总用电量、空调用电量、UPS用电量）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (361, 2, 2, 1, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (362, 2, 2, 2, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (363, 2, 2, 3, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (364, 2, 2, 5, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (365, 2, 2, 6, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (366, 2, 2, 7, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (367, 2, 2, 8, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (368, 2, 2, 9, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (369, 2, 2, 13, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (370, 2, 2, 14, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (371, 2, 2, 15, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (372, 2, 2, 16, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (373, 2, 2, 20, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (374, 2, 2, 21, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (375, 2, 2, 22, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (376, 2, 2, 23, 101);

-- 电信场景 省份 水能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (377, 2, 3, 24, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (378, 2, 3, 25, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (379, 2, 3, 26, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (380, 2, 3, 27, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (381, 2, 3, 28, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (382, 2, 3, 29, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (383, 2, 3, 30, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (384, 2, 3, 31, 101);

-- 电信场景 省份 气能耗 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (385, 2, 4, 32, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (386, 2, 4, 33, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (387, 2, 4, 34, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (388, 2, 4, 35, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (389, 2, 4, 36, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (390, 2, 4, 37, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (391, 2, 4, 38, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (392, 2, 4, 39, 101);

-- 电信场景 省份 其他 可配置指标（）
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (393, 2, 5, 4, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (394, 2, 5, 12, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (395, 2, 5, 18, 101);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (396, 2, 5, 19, 101);

-- IDC场景 层级、楼层 总电力容量 现场脚本需要把id去掉让自增
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (397, 1, 6, 100, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (398, 1, 6, 100, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (399, 1, 6, 101, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (400, 1, 6, 101, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (401, 1, 6, 102, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (402, 1, 6, 102, 5);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (403, 1, 7, 103, 4);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (404, 1, 7, 103, 5);

insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (405, 1, 6, 100, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (406, 1, 6, 100, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (407, 1, 6, 101, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (408, 1, 6, 101, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (409, 1, 6, 102, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (410, 1, 6, 102, 3);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (411, 1, 7, 103, 2);
insert into ComplexIndexBusinessObjectMap(BusinessObjectMapId,SceneId,BusinessTypeId,ComplexIndexDefinitionId,ObjectTypeId) values (412, 1, 7, 103, 3);
