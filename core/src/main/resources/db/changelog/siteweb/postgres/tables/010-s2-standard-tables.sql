CREATE TABLE tbl_standardback
(
    entrycategory       integer NOT NULL,
    equipmenttemplateid integer NOT NULL,
    entryid             integer NOT NULL,
    eventconditionid    integer NOT NULL,
    signalname          character varying(128),
    storeinterval       double precision,
    absvaluethreshold   double precision,
    percentthreshold    double precision,
    eventname           character varying(128),
    eventseverity       integer,
    startcomparevalue   double precision,
    startdelay          integer,
    standardname        integer,
    meanings            character varying(255),
    controlname         character varying(128)
);
ALTER TABLE ONLY tbl_standardback ADD CONSTRAINT idx_23978_primary PRIMARY KEY (entrycategory, entryid, equipmenttemplateid, eventconditionid);

CREATE TABLE tbl_standarddic
(
    standarddicid       integer                NOT NULL PRIMARY KEY,
    signalstandardname  character varying(255) NOT NULL,
    signalstandardid    integer                NOT NULL,
    equipmentlogicclass character varying(255) NOT NULL,
    storeinterval       integer,
    absvaluethreshold   double precision,
    percentthreshold    double precision,
    standardname        character varying(255),
    eventseverity       integer,
    eventlogicclass     character varying(255),
    eventclass          character varying(255),
    netmanageid         character varying(255),
    equipmentaffect     character varying(255),
    businessaffect      character varying(255),
    comparevalue        character varying(128),
    startdelay          character varying(64),
    controlstandardname character varying(255),
    controlstandardid   integer,
    controltype         integer,
    unit                character varying(128),
    description         character varying(255),
    meanings            character varying(255),
    nodetype            integer
);

CREATE TABLE tbl_standarddiccontrol
(
    standarddicid         integer                NOT NULL,
    standardtype          integer                NOT NULL,
    equipmentlogicclassid integer                NOT NULL,
    equipmentlogicclass   character varying(128) NOT NULL,
    controllogicclassid   integer,
    controllogicclass     character varying(128),
    controlstandardname   character varying(255),
    netmanageid           character varying(255),
    stationcategory       integer                NOT NULL,
    modifytype            integer,
    description           character varying(255),
    extendfiled1          text,
    extendfiled2          text
);
ALTER TABLE ONLY tbl_standarddiccontrol ADD CONSTRAINT idx_23988_primary PRIMARY KEY (standarddicid, standardtype, stationcategory);

CREATE TABLE tbl_standarddicevent
(
    standarddicid         integer                NOT NULL,
    standardtype          integer                NOT NULL,
    equipmentlogicclassid integer                NOT NULL,
    equipmentlogicclass   character varying(128) NOT NULL,
    eventlogicclassid     integer,
    eventlogicclass       character varying(128),
    eventclass            character varying(255),
    eventstandardname     character varying(255),
    netmanageid           character varying(255),
    eventseverity         integer,
    comparevalue          character varying(128),
    startdelay            character varying(64),
    meanings              character varying(255),
    equipmentaffect       character varying(255),
    businessaffect        character varying(255),
    stationcategory       integer                NOT NULL,
    modifytype            integer,
    description           character varying(255),
    extendfiled1          text,
    extendfiled2          text,
    extendfiled3          text
);
ALTER TABLE ONLY tbl_standarddicevent ADD CONSTRAINT idx_23993_primary PRIMARY KEY (standarddicid, standardtype, stationcategory);

CREATE TABLE tbl_standarddicsig
(
    standarddicid         integer                NOT NULL,
    standardtype          integer                NOT NULL,
    equipmentlogicclassid integer                NOT NULL,
    equipmentlogicclass   character varying(128) NOT NULL,
    signallogicclassid    integer,
    signallogicclass      character varying(128),
    signalstandardname    character varying(255) NOT NULL,
    netmanageid           character varying(255),
    storeinterval         integer,
    absvaluethreshold     double precision,
    statisticsperiod      integer,
    percentthreshold      double precision,
    stationcategory       integer                NOT NULL,
    modifytype            integer,
    description           character varying(255),
    extendfiled1          text,
    extendfiled2          text
);
ALTER TABLE ONLY tbl_standarddicsig ADD CONSTRAINT idx_23998_primary PRIMARY KEY (standarddicid, standardtype, stationcategory);

CREATE TABLE tbl_standardrule
(
    standardruleid     serial  NOT NULL PRIMARY KEY,
    standardtemplateid integer NOT NULL,
    signalname         character varying(128),
    eventname          character varying(128),
    expression         character varying(128),
    meanings           character varying(128),
    controlname        character varying(128),
    standarddicid      integer
);

CREATE TABLE tbl_standardtemplate
(
    standardtemplateid   integer                NOT NULL primary key,
    standardtemplatename character varying(255) NOT NULL,
    stationcategory      integer                NOT NULL,
    equipmentcategory    integer                NOT NULL,
    vendor               character varying(255) NOT NULL,
    equipmentmodel       character varying(255) NOT NULL,
    monitormodule        character varying(255)
);

CREATE TABLE tbl_standardtemplatemap
(
    equipmenttemplateid integer NOT NULL,
    stationcategory     integer NOT NULL,
    standardtemplateid  integer NOT NULL
);
alter table tbl_standardtemplatemap add CONSTRAINT tbl_standardtemplatemap_primary PRIMARY KEY (EquipmentTemplateId,StationCategory,StandardTemplateId);


CREATE TABLE tbl_standardtype
(
    standardid    integer                NOT NULL PRIMARY KEY,
    standardname  character varying(255) NOT NULL,
    standardalias character varying(255) NOT NULL,
    remark        character varying(255)
);