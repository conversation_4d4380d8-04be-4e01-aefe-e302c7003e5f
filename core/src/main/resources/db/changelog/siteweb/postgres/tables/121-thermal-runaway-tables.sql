CREATE TABLE libatterypack
(
    batterypackid                          serial NOT NULL PRIMARY KEY,
    batterypacknumber                      integer,
    batterynumber                          integer,
    batterygroupnumber                     integer,
    batterymodulenumber                    integer,
    batterypackname                        character varying(128),
    equipmentid                            integer,
    upsequipmentid                         integer,
    meanvoltagecomplexindexid              integer,
    meantemperaturecomplexindexid          integer,
    voltagesignalid                        integer,
    temperaturesignalid                    integer,
    temperatureincrementratecomplexindexid integer,
    temperaturedeviationcomplexindexid     integer,
    voltagedeviationcomplexindexid         integer,
    description                            character varying(255)
);
COMMENT ON COLUMN libatterypack.batterypacknumber IS '电池单体编号';
COMMENT ON COLUMN libatterypack.batterynumber IS '所属电池编号';
COMMENT ON COLUMN libatterypack.batterygroupnumber IS '所属电池组编号';
COMMENT ON COLUMN libatterypack.batterymodulenumber IS '所属电池模块编号';
COMMENT ON COLUMN libatterypack.batterypackname IS '电池单体名称';
COMMENT ON COLUMN libatterypack.equipmentid IS '电池组设备Id';
COMMENT ON COLUMN libatterypack.upsequipmentid IS 'UPS设备Id';
COMMENT ON COLUMN libatterypack.meanvoltagecomplexindexid IS '电池组平均电压指标Id';
COMMENT ON COLUMN libatterypack.meantemperaturecomplexindexid IS '电池组平均温度指标Id';
COMMENT ON COLUMN libatterypack.voltagesignalid IS '单体电压信号Id';
COMMENT ON COLUMN libatterypack.temperaturesignalid IS '单体温度信号Id';
COMMENT ON COLUMN libatterypack.temperatureincrementratecomplexindexid IS '单体电池温升速率指标Id';
COMMENT ON COLUMN libatterypack.temperaturedeviationcomplexindexid IS '单体电池与同组电池平均温度差值指标Id';
COMMENT ON COLUMN libatterypack.voltagedeviationcomplexindexid IS '单体电池与同组电池平均电压差值指标Id';

CREATE TABLE thermalrunawayadvice
(
    id          serial NOT NULL PRIMARY KEY,
    advicetype  character varying(128),
    reason      character varying(500),
    advice      character varying(500),
    contacts    character varying(128),
    description character varying(255)
);
COMMENT ON COLUMN thermalrunawayadvice.advicetype IS '建议类别';
COMMENT ON COLUMN thermalrunawayadvice.reason IS '原因';
COMMENT ON COLUMN thermalrunawayadvice.advice IS '处理建议';
COMMENT ON COLUMN thermalrunawayadvice.contacts IS '联系人';

CREATE TABLE thermalrunawayaffectdevice
(
    id                   serial NOT NULL PRIMARY KEY,
    upsequipmentid       integer,
    affectdevicename     character varying(128),
    affectdeviceposition character varying(128),
    affectdevicepurpose  character varying(255),
    affectdevicepower    character varying(255),
    description          character varying(255)
);
COMMENT ON COLUMN thermalrunawayaffectdevice.upsequipmentid IS 'UPS设备Id';
COMMENT ON COLUMN thermalrunawayaffectdevice.affectdevicename IS '受影响设备名称';
COMMENT ON COLUMN thermalrunawayaffectdevice.affectdeviceposition IS '受影响设备位置';
COMMENT ON COLUMN thermalrunawayaffectdevice.affectdevicepurpose IS '受影响设备用途';
COMMENT ON COLUMN thermalrunawayaffectdevice.affectdevicepower IS '受影响设备功率';

CREATE TABLE thermalrunawaycontrolpolicy
(
    id                 serial NOT NULL PRIMARY KEY,
    batteryequipmentid integer,
    phaseid            integer,
    controlpolicy      character varying(500),
    description        character varying(255)
);
COMMENT ON COLUMN thermalrunawaycontrolpolicy.batteryequipmentid IS '电池设备Id';
COMMENT ON COLUMN thermalrunawaycontrolpolicy.phaseid IS '热失控阶段Id';
COMMENT ON COLUMN thermalrunawaycontrolpolicy.controlpolicy IS '控制策略';

CREATE TABLE thermalrunawaycontrolrecord
(
    id                    serial NOT NULL PRIMARY KEY,
    thermalrunawayeventid integer,
    equipmentid           integer,
    controlid             integer,
    controlvalue          character varying(100),
    controltime           timestamp,
    controlresult         character varying(50)
);
COMMENT ON COLUMN thermalrunawaycontrolrecord.thermalrunawayeventid IS '热失控事件Id';
COMMENT ON COLUMN thermalrunawaycontrolrecord.equipmentid IS '控制设备Id';
COMMENT ON COLUMN thermalrunawaycontrolrecord.controlid IS '控制命令Id';
COMMENT ON COLUMN thermalrunawaycontrolrecord.controlvalue IS '控制值';
COMMENT ON COLUMN thermalrunawaycontrolrecord.controltime IS '控制时间';
COMMENT ON COLUMN thermalrunawaycontrolrecord.controlresult IS '控制结果';

CREATE TABLE thermalrunawayevent
(
    thermalrunawayeventid serial NOT NULL PRIMARY KEY,
    starttime             timestamp,
    endtime               timestamp,
    batterypackid         integer,
    triggertemperature    double precision,
    prediction            character varying(128),
    phaseid               integer,
    phase                 character varying(128),
    description           character varying(255)
);
COMMENT ON COLUMN thermalrunawayevent.starttime IS '开始时间';
COMMENT ON COLUMN thermalrunawayevent.endtime IS '结束时间';
COMMENT ON COLUMN thermalrunawayevent.batterypackid IS '电池单体Id';
COMMENT ON COLUMN thermalrunawayevent.triggertemperature IS '触发热失控时的单体温度';
COMMENT ON COLUMN thermalrunawayevent.prediction IS '预测结果';
COMMENT ON COLUMN thermalrunawayevent.phaseid IS '所处阶段ID';
COMMENT ON COLUMN thermalrunawayevent.phase IS '所处阶段';

CREATE TABLE thermalrunawayphase
(
    id                 serial NOT NULL PRIMARY KEY,
    phase              character varying(128),
    prediction         character varying(128),
    triggertemperature double precision,
    description        character varying(255)
);
COMMENT ON COLUMN thermalrunawayphase.phase IS '阶段';
COMMENT ON COLUMN thermalrunawayphase.prediction IS '预测结果：事前预警或热失控';
COMMENT ON COLUMN thermalrunawayphase.triggertemperature IS '触发热失控时的单体温度';