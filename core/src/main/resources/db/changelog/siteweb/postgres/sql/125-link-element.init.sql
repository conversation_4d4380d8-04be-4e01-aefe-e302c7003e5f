INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (1,'整数加法','整数运算',1,1,null,'+',1,1,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (2,'整数减法','整数运算',2,1,null,'-',1,2,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (3,'整数乘法','整数运算',1,1,null,'*',1,3,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (4,'整数除法','整数运算',2,1,null,'/',1,4,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (5,'整数取模','整数运算',2,1,null,'%',1,5,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (6,'浮点数加法','浮点数运算',1,1,null,'+',1,6,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (7,'浮点数减法','浮点数运算',2,1,null,'-',1,7,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (8,'浮点数乘法','浮点数运算',1,1,null,'*',1,8,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (9,'浮点数除法','浮点数运算',2,1,null,'/',1,9,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (10,'>','关系运算',2,1,null,'>',1,10,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (11,'>=','关系运算',2,1,null,'>=',1,11,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (12,'=','关系运算',2,1,null,'==',1,12,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (13,'<=','关系运算',2,1,null,'<=',1,13,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (14,'<','关系运算',2,1,null,'<',1,14,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (15,'!=','关系运算',2,1,null,'!=',1,15,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (16,'逻辑与','逻辑运算',1,1,null,'&&',1,16,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (17,'逻辑或','逻辑运算',1,1,null,'||',1,17,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (18,'逻辑非','逻辑运算',1,1,null,'!',1,18,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (19,'最大值','聚合函数',1,1,null,'max()',1,19,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (20,'最小值','聚合函数',1,1,null,'min()',1,20,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (21,'平均值','聚合函数',1,1,null,'mean()',1,21,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (31,'事件开始','事件',1,1,null,null,1,31,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (32,'事件结束','事件',1,1,null,null,1,32,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (33,'当前时间','时间组件',1,1,null,null,1,33,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (34,'时间延迟','时间组件',1,1,null,null,1,34,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (35,'事件确认','事件',1,1,null,null,1,35,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (36,'事件存在','事件',1,1,null,null,1,36,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (41,'并行执行','联动控制',1,1,null,null,1,41,null);
-- INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
-- VALUES (42,'执行脚本','联动控制',1,1,null,null,1,42,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (43,'空指令','联动控制',1,0,null,null,1,43,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (44,'设备控制','联动控制',1,1,null,null,1,44,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (51,'条件运算','条件运算',1,2,null,'if()',1,51,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (52,'整数常量','常量',1,1,null,null,1,52,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (53,'浮点数常量','常量',1,1,null,null,1,53,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (61,'测点','测点',1,1,null,null,0,61,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (62,'设备','设备',0,1,null,null,0,62,null);
INSERT INTO linkelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (63,'层级','层级',0,1,null,null,0,63,null);

