CREATE TABLE tbl_drivestructuretemplate
(
    id              serial NOT NULL PRIMARY KEY,
    filepath        character varying(100),
    pid             integer,
    fileid          bigint,
    isdisk          integer,
    isupload        integer,
    isfill          integer,
    drivetemplateid integer,
    isleaf          integer,
    uploadtiming    integer
);
COMMENT ON COLUMN tbl_drivestructuretemplate.filepath IS '结构路径';
COMMENT ON COLUMN tbl_drivestructuretemplate.pid IS '上级结构id';
COMMENT ON COLUMN tbl_drivestructuretemplate.fileid IS '模板文件id';
COMMENT ON COLUMN tbl_drivestructuretemplate.isdisk IS '是否是目录（默认是）';
COMMENT ON COLUMN tbl_drivestructuretemplate.isupload IS '是否需要上传';
COMMENT ON COLUMN tbl_drivestructuretemplate.isfill IS '是否需要填充';
COMMENT ON COLUMN tbl_drivestructuretemplate.drivetemplateid IS '驱动模板id';
COMMENT ON COLUMN tbl_drivestructuretemplate.isleaf IS '是否是叶子节点';
COMMENT ON COLUMN tbl_drivestructuretemplate.uploadtiming IS '上传时机 0-模板创建时 1-生成配置时';

CREATE TABLE tbl_drivetemplate
(
    id                     serial NOT NULL PRIMARY KEY,
    drivetemplatename      character varying(100),
    drivertemplatedescribe character varying(255),
    isdefaulttemplate      integer,
    drivetemplatetype      integer
);
COMMENT ON COLUMN tbl_drivetemplate.drivetemplatename IS '驱动模板名称';
COMMENT ON COLUMN tbl_drivetemplate.drivertemplatedescribe IS '驱动模板描述';
COMMENT ON COLUMN tbl_drivetemplate.isdefaulttemplate IS '是否是默认模板(当前模板类型下)';
COMMENT ON COLUMN tbl_drivetemplate.drivetemplatetype IS '驱动模板类型';

CREATE TABLE tbl_equipmentext
(
    equipmentid     integer NOT NULL PRIMARY KEY,
    drivetemplateid integer,
    fileid          bigint,
    isupload        smallint,
    isreset         smallint,
    fieldhash       integer
);
COMMENT ON COLUMN tbl_equipmentext.equipmentid IS '设备主键id';
COMMENT ON COLUMN tbl_equipmentext.drivetemplateid IS '引用驱动模板id';
COMMENT ON COLUMN tbl_equipmentext.fileid IS '文件id';
COMMENT ON COLUMN tbl_equipmentext.isupload IS '是否已经上传';
COMMENT ON COLUMN tbl_equipmentext.isreset IS '是否重新生成';
COMMENT ON COLUMN tbl_equipmentext.fieldhash IS '设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值(主要用于判断客户端配置工具是否对上述字段进行过修改)';