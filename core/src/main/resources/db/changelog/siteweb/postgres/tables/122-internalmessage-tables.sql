CREATE TABLE internalmessagetype
(
    internalmessagetypeid serial      NOT NULL PRIMARY KEY,
    typename              varchar(32) NOT NULL,
    enabletts             integer     NOT NULL
);
COMMENT ON COLUMN internalmessagetype.typename IS '消息类型';
COMMENT ON COLUMN internalmessagetype.enabletts IS '是否启用tts播报';

CREATE TABLE internalmessage
(
    internalmessageid serial       NOT NULL PRIMARY KEY,
    body              varchar(512) NOT NULL,
    messagetype       integer      NOT NULL,
    createtime        timestamp    NOT NULL
);
COMMENT ON COLUMN internalmessage.body IS '消息内容';
COMMENT ON COLUMN internalmessage.messagetype IS '消息类型';
COMMENT ON COLUMN internalmessage.createtime IS '创建时间';

CREATE TABLE messagestatus
(
    messagestatusid   serial  NOT NULL PRIMARY KEY,
    internalmessageid integer DEFAULT NULL,
    userid            integer NOT NULL,
    messagestatus     integer DEFAULT NULL
);
COMMENT ON COLUMN messagestatus.internalmessageid IS '消息id';
COMMENT ON COLUMN messagestatus.userid IS '用户接收id';
COMMENT ON COLUMN messagestatus.messagestatus IS '消息状态 0未读 1已读';

CREATE INDEX idx_user_id ON messagestatus (userid);
CREATE INDEX idx_internal_message_id ON messagestatus (internalmessageid);
