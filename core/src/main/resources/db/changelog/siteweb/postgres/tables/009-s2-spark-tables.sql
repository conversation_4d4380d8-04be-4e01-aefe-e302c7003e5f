CREATE TABLE tbl_sparkindexbaseequipment
(
    groupid         integer NOT NULL,
    baseequipmentid integer NOT NULL,
    grapicpageid    integer,
    displayindex    integer,
    displayformat   text,
    extendfiled1    text,
    extendfiled2    text,
    displaytype     integer
);
alter table tbl_sparkindexbaseequipment add CONSTRAINT tbl_sparkindexbaseequipment_primary PRIMARY KEY (GroupId, BaseEquipmentId);

CREATE TABLE tbl_sparkindexbasesignal
(
    groupid         integer        NOT NULL,
    baseequipmentid integer        NOT NULL,
    basetypeid      numeric(12, 0) NOT NULL,
    displayindex    integer,
    extendfiled1    text,
    extendfiled2    text
);
alter table tbl_sparkindexbasesignal add CONSTRAINT tbl_sparkindexbasesignal_primary PRIMARY KEY (GroupId,BaseEquipmentId,BaseTypeId);

CREATE TABLE tbl_sparkindexgroup
(
    groupid      integer                NOT NULL primary key,
    grouptitle   character varying(255) NOT NULL,
    grapicpageid integer,
    extendfiled1 text,
    extendfiled2 text,
    columncount  integer
);