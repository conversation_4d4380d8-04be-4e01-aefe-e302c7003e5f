CREATE TABLE report
(
    reportid               SERIAL NOT NULL PRIMARY KEY,
    reportname             CHARACTER VARYING(255),
    reportdescription      CHARACTER VARYING(255),
    reportschemaid         INTEGER,
    reportschemacategoryid INTEGER,
    updateuserid           INTEGER,
    updatetime             TIMESTAMP,
    reportdatasourceid     INTEGER,
    maxqueryinterval       INTEGER,
    createuserid           INTEGER,
    overt                  SMALLINT,
    ColumnConfig           json
);
COMMENT ON COLUMN report.reportname IS '报表名称';
COMMENT ON COLUMN report.reportdescription IS '报表备注信息';
COMMENT ON COLUMN report.reportschemaid IS '报表模式ID';
COMMENT ON COLUMN report.reportschemacategoryid IS '报表Schema类型ID';
COMMENT ON COLUMN report.updateuserid IS '修改人ID';
COMMENT ON COLUMN report.updatetime IS '修改时间';
COMMENT ON COLUMN report.reportdatasourceid IS '报表数据源ID';
COMMENT ON COLUMN report.maxqueryinterval IS '报表数据源ID';
COMMENT ON COLUMN report.createuserid IS '创建报表的用户id';
COMMENT ON COLUMN report.overt IS '是否公开';

CREATE TABLE reportdatasource
(
    reportdatasourceid          SERIAL NOT NULL PRIMARY KEY,
    reportdatasourcename        CHARACTER VARYING(128),
    reportdatasourcedescription CHARACTER VARYING(255)
);
COMMENT ON COLUMN reportdatasource.reportdatasourcename IS '报表数据源名称';
COMMENT ON COLUMN reportdatasource.reportdatasourcedescription IS '报表数据源备注';

CREATE TABLE reportexportparameterpreset
(
    reportexportparameterpresetid SERIAL NOT NULL PRIMARY KEY,
    reportid                      INTEGER,
    reportschemaexportparameterid INTEGER,
    display                       SMALLINT
);
COMMENT ON COLUMN reportexportparameterpreset.reportid IS '报表ID';
COMMENT ON COLUMN reportexportparameterpreset.reportschemaexportparameterid IS '报表Schema导出参数ID';
COMMENT ON COLUMN reportexportparameterpreset.display IS '是否展示';

CREATE TABLE reportparameterpreset
(
    reportparameterpresetid      SERIAL NOT NULL PRIMARY KEY,
    reportid                     INTEGER,
    reportschemaqueryparameterid INTEGER,
    VALUE                        TEXT,
    display                      SMALLINT
);
COMMENT ON COLUMN reportparameterpreset.reportid IS '报表ID';
COMMENT ON COLUMN reportparameterpreset.reportschemaqueryparameterid IS '报表Schema查询参数ID';
COMMENT ON COLUMN reportparameterpreset.value IS '值';
COMMENT ON COLUMN reportparameterpreset.display IS '是否展示';

CREATE TABLE reportschema
(
    reportschemaid          SERIAL NOT NULL PRIMARY KEY,
    reportschemaname        CHARACTER VARYING(255),
    reportschemadescription CHARACTER VARYING(255),
    version                 CHARACTER VARYING(32),
    reportschemacategoryid  INTEGER,
    author                  CHARACTER VARYING(128),
    createtime              TIMESTAMP,
    reportdatasourceid      INTEGER,
    viewcontrolid           INTEGER,
    maxqueryinterval        INTEGER
);
COMMENT ON COLUMN reportschema.reportschemaname IS '报表Schema名称';
COMMENT ON COLUMN reportschema.reportschemadescription IS '报表Schema备注';
COMMENT ON COLUMN reportschema.version IS '版本';
COMMENT ON COLUMN reportschema.reportschemacategoryid IS '报表Schema类型ID';
COMMENT ON COLUMN reportschema.author IS '创建人';
COMMENT ON COLUMN reportschema.createtime IS '创建时间';
COMMENT ON COLUMN reportschema.reportdatasourceid IS '报表数据源ID';
COMMENT ON COLUMN reportschema.viewcontrolid IS '是否隐藏';
COMMENT ON COLUMN reportschema.maxqueryinterval IS '最大查询间隔';

CREATE TABLE reportschemacategory
(
    reportschemacategoryid          SERIAL NOT NULL PRIMARY KEY,
    reportschemacategoryname        CHARACTER VARYING(128),
    reportschemacategorydescription CHARACTER VARYING(128),
    reportschemacategorypath        CHARACTER VARYING(128),
    sortval                         INTEGER
);
COMMENT ON COLUMN reportschemacategory.reportschemacategoryname IS '报表Schema分类名称';
COMMENT ON COLUMN reportschemacategory.reportschemacategorydescription IS '报表Schema分类备注';
COMMENT ON COLUMN reportschemacategory.sortval IS '排序';

CREATE TABLE reportschemaexportparameter
(
    reportschemaexportparameterid    SERIAL NOT NULL PRIMARY KEY,
    reportschemaexportparametername  CHARACTER VARYING(128),
    reportschemaexportparametertitle CHARACTER VARYING(128),
    reportschemaid                   INTEGER,
    "isnull"                         SMALLINT
);
COMMENT ON COLUMN reportschemaexportparameter.reportschemaexportparametername IS '报表Schema导出参数名称';
COMMENT ON COLUMN reportschemaexportparameter.reportschemaexportparametertitle IS '报表Schema导出参数标题';
COMMENT ON COLUMN reportschemaexportparameter.reportschemaid IS '报表SchemaId';
COMMENT ON COLUMN reportschemaexportparameter."isnull" IS '是否为空';

CREATE TABLE reportschemaqueryparameter
(
    reportschemaqueryparameterid    serial NOT NULL primary key,
    reportschemaqueryparametername  character varying(128),
    reportschemaqueryparametertitle character varying(128),
    reportschemaid                  integer,
    parametercontrolid              integer,
    datasourceexpression            text,
    datasourcereturntablename       character varying(255),
    "isnull"                        smallint,
    sortindex                       integer
);
COMMENT ON COLUMN reportschemaqueryparameter.reportschemaqueryparametername IS '报表Schema查询参数名称';
COMMENT ON COLUMN reportschemaqueryparameter.reportschemaqueryparametertitle IS '报表Schema查询参数标题';
COMMENT ON COLUMN reportschemaqueryparameter.reportschemaid IS '报表SchemaId';
COMMENT ON COLUMN reportschemaqueryparameter.parametercontrolid IS '参数控制ID（前端）';
COMMENT ON COLUMN reportschemaqueryparameter.datasourceexpression IS '数据源表达式';
COMMENT ON COLUMN reportschemaqueryparameter.datasourcereturntablename IS '数据源返回json';
COMMENT ON COLUMN reportschemaqueryparameter."isnull" IS '是否为空';
COMMENT ON COLUMN reportschemaqueryparameter.sortindex IS '展示顺序';

CREATE TABLE reporttimingtaskfile
(
    reporttimingtaskfileid       serial NOT NULL PRIMARY KEY,
    reporttimingtaskmanagementid integer,
    reportschemaid               integer,
    file                         text,
    filepath                     character varying(255),
    createtime                   timestamp
);
COMMENT ON COLUMN reporttimingtaskfile.reporttimingtaskmanagementid IS '报表定时任务管理ID';
COMMENT ON COLUMN reporttimingtaskfile.reportschemaid IS '报表SchemaId';
COMMENT ON COLUMN reporttimingtaskfile.file IS '文件内容';
COMMENT ON COLUMN reporttimingtaskfile.filepath IS '文件路径';
COMMENT ON COLUMN reporttimingtaskfile.createtime IS '创建时间';
CREATE INDEX idx_22468_idx_reporttimingtaskmanagementid_createtime ON reporttimingtaskfile USING BTREE (reporttimingtaskmanagementid, createtime);

CREATE TABLE reporttimingtaskmanagement
(
    reporttimingtaskmanagementid   serial NOT NULL PRIMARY KEY,
    reporttimingtaskmanagementname character varying(256),
    reportid                       integer,
    reportname                     character varying(256),
    storagecycle                   character varying(64),
    starttimetype                  character varying(64),
    endtimetype                    character varying(64),
    status                         integer,
    "to"                           character varying(1024),
    cc                             character varying(1024),
    createuserid                   integer,
    overt                          smallint
);
COMMENT ON COLUMN reporttimingtaskmanagement.reporttimingtaskmanagementname IS '报表定时任务管理名称';
COMMENT ON COLUMN reporttimingtaskmanagement.reportid IS '报表ID';
COMMENT ON COLUMN reporttimingtaskmanagement.reportname IS '报表名称';
COMMENT ON COLUMN reporttimingtaskmanagement.storagecycle IS '存储cron';
COMMENT ON COLUMN reporttimingtaskmanagement.starttimetype IS '开始时间类型';
COMMENT ON COLUMN reporttimingtaskmanagement.endtimetype IS '结束时间类型';
COMMENT ON COLUMN reporttimingtaskmanagement.status IS '状态';
COMMENT ON COLUMN reporttimingtaskmanagement."to" IS '收件人';
COMMENT ON COLUMN reporttimingtaskmanagement.cc IS '抄送';
COMMENT ON COLUMN reporttimingtaskmanagement.createuserid IS '创建报表的用户id';
COMMENT ON COLUMN reporttimingtaskmanagement.overt IS '是否公开';

CREATE TABLE reporttimingtasktimetype
(
    reporttimingtasktimetypeid serial NOT NULL PRIMARY KEY,
    timetypename               character varying(45),
    timetypevalue              character varying(45)
);

CREATE TABLE reportfolder (
    folderId SERIAL PRIMARY KEY,
    folderName varchar(255) NOT NULL,
    parentId int DEFAULT 0,
    sortIndex int DEFAULT NULL,
    createTime timestamp DEFAULT NULL
);

CREATE TABLE reportfoldermap (
    id SERIAL PRIMARY KEY,
    reportId int NOT NULL,
    folderId int NOT NULL,
    reportType int NOT NULL DEFAULT 1,
    sortIndex int DEFAULT NULL,
    UNIQUE (reportId, reportType)
);