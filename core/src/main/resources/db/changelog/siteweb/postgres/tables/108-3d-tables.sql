CREATE TABLE devicerefdimension
(
    devicerefdimensionid serial                 NOT NULL PRIMARY KEY,
    objectbinddata       integer                NOT NULL,
    type                 character varying(128) NOT NULL,
    dimensiondesignerid  integer                NOT NULL,
    relation             character varying(128) NOT NULL
);

CREATE TABLE dimensionconfigure
(
    dimensionconfigureid   serial NOT NULL PRIMARY KEY,
    dimensionconfiguretype integer,
    dimensionconfigure     text,
    dimensionconfigureuuid character varying(128)
);
COMMENT ON COLUMN dimensionconfigure.dimensionconfiguretype IS '资源类型';
COMMENT ON COLUMN dimensionconfigure.dimensionconfigure IS '可变的的配置项';
COMMENT ON COLUMN dimensionconfigure.dimensionconfigureuuid IS '唯一uuid';

CREATE TABLE dimensiondesigner
(
    dimensiondesignerid   serial NOT NULL PRIMARY KEY,
    dimensiondesignername character varying(128),
    filepath              character varying(256),
    updatetime            timestamp
);
COMMENT ON COLUMN dimensiondesigner.dimensiondesignername IS '名称';
COMMENT ON COLUMN dimensiondesigner.filepath IS '文件路径';
COMMENT ON COLUMN dimensiondesigner.updatetime IS '更新时间';

CREATE TABLE dimensionmodel
(
    dimensionmodelid       serial NOT NULL PRIMARY KEY,
    dimensionmodelcategory integer,
    dimensionmodelname     character varying(128),
    dimensionmodelfile     character varying(128)
);
COMMENT ON COLUMN dimensionmodel.dimensionmodelcategory IS '模型类型';
COMMENT ON COLUMN dimensionmodel.dimensionmodelname IS '模型名称';
COMMENT ON COLUMN dimensionmodel.dimensionmodelfile IS '模型文件';

CREATE TABLE dimensionlinks (
  lineId VARCHAR(64) NOT NULL,
  displayName VARCHAR(64),
  lineType INTEGER,
  localObjectId INTEGER,
  localObjectType INTEGER,
  localObjectPort VARCHAR(64),
  localObjectModelPort INTEGER,
  remoteObjectId INTEGER,
  remoteObjectType INTEGER,
  remoteObjectPort VARCHAR(64),
  remoteObjectModelPort INTEGER,
  staticPropertys json,
  description VARCHAR(128),
  PRIMARY KEY (lineId)
);
