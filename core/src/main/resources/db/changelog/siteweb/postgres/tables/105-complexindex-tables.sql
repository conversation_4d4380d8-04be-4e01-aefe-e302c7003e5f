CREATE TABLE complexindex
(
    complexindexid           serial NOT NULL PRIMARY KEY,
    complexindexname         character varying(128),
    complexindexdefinitionid integer,
    objectid                 integer,
    calccron                 character varying(128),
    calctype                 integer,
    aftercalc                text,
    savecron                 character varying(128),
    expression               text,
    unit                     character varying(128),
    accuracy                 character varying(128),
    objecttypeid             integer,
    remark                   character varying(128),
    label                    character varying(128),
    businesstypeid           integer,
    checkexpression          character varying(1024)
);

CREATE TABLE complexindexbusinessobjectmap
(
    businessobjectmapid      serial  NOT NULL PRIMARY KEY,
    sceneid                  integer NOT NULL,
    businesstypeid           integer NOT NULL,
    objecttypeid             integer NOT NULL,
    complexindexdefinitionid integer NOT NULL
);

CREATE TABLE complexindexbusinesstype
(
    businesstypeid   serial                NOT NULL PRIMARY KEY,
    businesstypename character varying(50) NOT NULL,
    parentid         integer               NOT NULL,
    description      character varying(255)
);

CREATE TABLE complexindexdefinition
(
    complexindexdefinitionid   serial                NOT NULL PRIMARY KEY,
    complexindexdefinitionname character varying(50) NOT NULL,
    calccron                   character varying(128),
    calctype                   integer,
    aftercalc                  character varying(128),
    savecron                   character varying(128),
    expression                 character varying(128),
    unit                       character varying(128),
    accuracy                   character varying(128),
    startstatus                integer,
    icon                       character varying(128),
    checkexpression            character varying(128),
    description                character varying(255)
);

CREATE TABLE complexindexfunction
(
    functionid          serial NOT NULL PRIMARY KEY,
    functionexpression  character varying(128),
    functiondescription character varying(128),
    functionname        character varying(128),
    remark              character varying(128)
);