CREATE TABLE tbl_report
(
    reportid         integer                NOT NULL PRIMARY KEY,
    reportname       character varying(255) NOT NULL,
    description      character varying(255),
    reportfileid     character varying(255) NOT NULL,
    reportfilename   character varying(255),
    previewimagename character varying(255),
    createuserid     integer,
    createusername   character varying(255),
    createtime       timestamp,
    updateuserid     integer,
    updateusername   character varying(255),
    updatetime       timestamp,
    version          character varying(50)
);

CREATE TABLE tbl_reportbrowehistory
(
    ReportBroweHistoryId serial PRIMARY KEY,
    historyid      integer NOT NULL,
    userid         integer,
    reportid       integer,
    viewcount      integer,
    lastbrowsetime timestamp
);

CREATE TABLE tbl_reportchart
(
    reportid    integer NOT NULL,
    chartid     integer NOT NULL,
    charttype   character varying(20),
    chartname   character varying(128),
    xaxis       character varying(128),
    yaxis       character varying(128),
    serials     character varying(128),
    serialsname character varying(128)
);
ALTER TABLE ONLY tbl_reportchart ADD CONSTRAINT idx_23768_primary PRIMARY KEY (chartid, reportid);

CREATE TABLE tbl_reportgroup
(
    Id serial PRIMARY KEY,
    reportgroupid integer                NOT NULL,
    groupname     character varying(255) NOT NULL,
    userid        integer,
    grouptype     integer DEFAULT 0,
    description   character varying(255)
);

CREATE TABLE tbl_reportgroupmap
(
    ReportGroupMap serial PRIMARY KEY,
    reportid      integer NOT NULL,
    reportgroupid integer NOT NULL
);

CREATE TABLE tbl_reportparameter
(
    parameterid     integer                NOT NULL PRIMARY KEY,
    parametername   character varying(128),
    parameterkey    character varying(128),
    procedurename   character varying(128) NOT NULL,
    parameterstring text,
    parameterfield  character varying(128),
    parametertype   character varying(20),
    parameterfrom   character varying(20),
    description     character varying(255)
);

CREATE TABLE tbl_reportparametermap
(
    reportid    integer NOT NULL,
    parameterid integer NOT NULL
);
ALTER TABLE ONLY tbl_reportparametermap ADD CONSTRAINT idx_23787_primary PRIMARY KEY (parameterid, reportid);

CREATE TABLE tbl_reportprocedure
(
    reportid        integer                NOT NULL PRIMARY KEY,
    procedurename   character varying(128) NOT NULL,
    parameterstring text,
    reportcolumn    text,
    reporttype      integer,
    description     character varying(255)
);

CREATE TABLE tbl_reportquery
(
    ReportQueryId serial PRIMARY KEY,
    queryid     integer,
    reportid    integer,
    name        character varying(255),
    taskid      integer,
    description character varying(255),
    querytime   timestamp
);

CREATE TABLE tbl_reportqueryparameter
(
    ReportQueryParameterId serial PRIMARY KEY,
    queryparameterid integer NOT NULL,
    queryid          integer,
    reportid         integer,
    parametername    character varying(255),
    datatype         character varying(64),
    value            text
);

CREATE TABLE tbl_reportrole
(
    reportroleid integer               NOT NULL PRIMARY KEY,
    rolename     character varying(50) NOT NULL,
    description  character varying(255)
);

CREATE TABLE tbl_reportrolemap
(
    reportid     integer NOT NULL,
    reportroleid integer NOT NULL
);
ALTER TABLE ONLY tbl_reportrolemap ADD CONSTRAINT idx_23808_primary PRIMARY KEY (reportid, reportroleid);

CREATE TABLE tbl_reportroleusermap
(
    ReportRoleUserMapId serial PRIMARY KEY,
    reportroleid integer NOT NULL,
    userid       integer NOT NULL
);

CREATE TABLE tbl_reporttask
(
    taskid             integer                NOT NULL PRIMARY KEY,
    taskname           character varying(128) NOT NULL,
    description        character varying(255),
    reportid           integer                NOT NULL,
    starttime          timestamp,
    endtime            timestamp,
    outputtime         timestamp,
    outputinterval     integer,
    outputintervaltype integer,
    queryperiod        integer,
    queryperiodtype    integer,
    queryparameters    text                   NOT NULL,
    commandtext        character varying(255),
    commandtype        character varying(128),
    centertype         integer DEFAULT 0      NOT NULL,
    querycount         integer,
    emailout           integer DEFAULT 0      NOT NULL,
    emailurl           text,
    creator            integer,
    createtime         timestamp,
    lastupdatetime     timestamp
);

CREATE TABLE tbl_reporttaskfile
(
    fileid    serial NOT NULL PRIMARY KEY,
    taskid    integer,
    reportid  integer,
    filename  character varying(128),
    starttime timestamp,
    endtime   timestamp,
    querytime timestamp
);

CREATE TABLE tbl_reportusermap
(
    ReportUserMapId serial PRIMARY KEY,
    reportid integer,
    userid   integer
);

CREATE TABLE tbl_suitreport
(
    suitreportid   integer                NOT NULL primary key,
    suitreportname character varying(255) NOT NULL,
    description    character varying(255),
    createuserid   integer,
    createusername character varying(255),
    createtime     timestamp,
    updateuserid   integer,
    updateusername character varying(255),
    updatetime     timestamp
);

CREATE TABLE tbl_suitreportmap
(
    suitreportid integer NOT NULL,
    reportid     integer NOT NULL
);
alter table tbl_suitreportmap add CONSTRAINT tbl_suitreportmap_primary PRIMARY KEY (suitreportid, reportid);

CREATE TABLE tbl_suitreportrolemap
(
    suitreportid integer NOT NULL,
    reportroleid integer NOT NULL
);
alter table tbl_suitreportrolemap add CONSTRAINT tbl_suitreportrolemap_primary PRIMARY KEY (suitreportid, reportroleid);