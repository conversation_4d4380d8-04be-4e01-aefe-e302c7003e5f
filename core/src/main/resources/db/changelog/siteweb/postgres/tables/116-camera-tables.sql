CREATE TABLE camera
(
    cameraid        serial NOT NULL PRIMARY KEY,
    cameraname      character varying(255),
    cameraip        character varying(128),
    cameraport      integer,
    channelnumber   character varying(32),
    cameragroupid   integer,
    username        character varying(128),
    password        character varying(32),
    vendorid        integer,
    vendorname      character varying(128),
    cameraindexcode character varying(255),
    cameratype      integer,
    cameratypename  character varying(255),
    updatetime      timestamp,
    description     character varying(255)
);
COMMENT ON COLUMN camera.cameraname IS '摄像头名称';
COMMENT ON COLUMN camera.cameraip IS '摄像头ip';
COMMENT ON COLUMN camera.cameraport IS '摄像头端口';
COMMENT ON COLUMN camera.channelnumber IS '信道号';
COMMENT ON COLUMN camera.cameragroupid IS '摄像头分组id';
COMMENT ON COLUMN camera.username IS '用户名';
COMMENT ON COLUMN camera.password IS '密码';
COMMENT ON COLUMN camera.vendorid IS '厂商id';
COMMENT ON COLUMN camera.vendorname IS '厂商名称';
COMMENT ON COLUMN camera.cameraindexcode IS '唯一id';
COMMENT ON COLUMN camera.cameratype IS '摄像头类型';
COMMENT ON COLUMN camera.cameratypename IS '摄像头名称';
COMMENT ON COLUMN camera.updatetime IS '最近更新时间';
COMMENT ON COLUMN camera.description IS '描述';

CREATE TABLE cameragroup
(
    cameragroupid   serial NOT NULL PRIMARY KEY,
    cameragroupname character varying(255),
    parentid        integer,
    description     character varying(255)
);
COMMENT ON COLUMN cameragroup.cameragroupname IS '摄像头分组名称';
COMMENT ON COLUMN cameragroup.parentid IS '父级id';
COMMENT ON COLUMN cameragroup.description IS '描述';

CREATE TABLE camerapollgroup
(
    camerapollgroupid   serial NOT NULL PRIMARY KEY,
    camerapollgroupname character varying(255),
    pollinterval        integer,
    description         character varying(255)
);
COMMENT ON COLUMN camerapollgroup.camerapollgroupname IS '摄像头采集周期名称';
COMMENT ON COLUMN camerapollgroup.pollinterval IS '采集周期';
COMMENT ON COLUMN camerapollgroup.description IS '描述';

CREATE TABLE camerapollgroupmap (
                                           camerapollgroupmapid serial NOT NULL PRIMARY KEY ,
                                           camerapollgroupid integer,
                                           cameraid integer
);
COMMENT ON COLUMN camerapollgroupmap.camerapollgroupid IS '摄像头采集周期id';
COMMENT ON COLUMN camerapollgroupmap.cameraid IS '摄像头id';