CREATE TABLE tbl_patrolallrecord_1
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_2
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_3
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_4
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_5
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_6
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_7
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_8
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_9
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_10
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_11
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_12
(
    PatrolAllRecordID serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolcalop
(
    calopid     integer                NOT NULL PRIMARY KEY,
    caloperator character varying(128) NOT NULL,
    meaning     character varying(255)
);

CREATE TABLE tbl_patrolcronexpression
(
    cronid         integer NOT NULL PRIMARY KEY,
    cronexpression character varying(128) DEFAULT ''::CHARACTER VARYING NOT NULL,
    meaning        character varying(128) DEFAULT ''::CHARACTER VARYING NOT NULL
);

CREATE TABLE tbl_patrolexrecord
(
    PatrolExRecordId serial PRIMARY KEY,
    centerid              integer               NOT NULL,
    centername            character varying(255),
    groupid               integer               NOT NULL,
    groupname             character varying(255),
    stationid             integer               NOT NULL,
    stationname           character varying(255),
    equipmentcategoryname character varying(255),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(255),
    signalid              integer               NOT NULL,
    signalname            character varying(255),
    signalvalue           character varying(64),
    recordtime            timestamp,
    unit                  character varying(128),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(8) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(36) NOT NULL
);

CREATE TABLE tbl_patrolgroup
(
    groupid         serial                NOT NULL PRIMARY KEY,
    groupname       character varying(128) NOT NULL,
    baseequipmentid integer,
    basetypeid      numeric(12, 0),
    note            character varying(255)
);

CREATE TABLE tbl_patrolgroupbasesignalmap
(
    PatrolGroupBaseSignalMap serial PRIMARY KEY,
    groupid         integer NOT NULL,
    baseequipmentid integer,
    basetypeid      numeric(10, 0)
);

CREATE TABLE tbl_patrolgroupparameters
(
    PatrolGroupParameters serial PRIMARY KEY,
    groupid        integer NOT NULL,
    stationtypeids character varying(24),
    stationids     text,
    equipmentids   text,
    signalids      text
);

CREATE TABLE tbl_patrolgrouprulemap
(
    PatrolGroupRuleMap serial PRIMARY KEY,
    groupid integer NOT NULL,
    ruleid  integer NOT NULL
);

CREATE TABLE tbl_patrolrule
(
    ruleid           serial                 NOT NULL PRIMARY KEY,
    rulename         character varying(128) NOT NULL,
    limitdown        double precision,
    limitdowncalopid integer                NOT NULL,
    limitup          double precision,
    limitupcalopid   integer                NOT NULL,
    unitid           integer,
    warninglevelid   integer                NOT NULL,
    bypercentage     integer                NOT NULL,
    ratedvalue       double precision,
    baseequipmentid  integer,
    basetypeid       numeric(12, 0),
    note             character varying(255),
    description      character varying(255),
    equipmentLogicClassId integer,
    standardDicId integer
);

CREATE TABLE tbl_patroltask
(
    taskid         serial                 NOT NULL PRIMARY KEY,
    taskname       character varying(128) NOT NULL,
    cronid         integer                NOT NULL,
    groupid        integer                NOT NULL,
    ispoweroffsave integer DEFAULT 0      NOT NULL,
    note           character varying(255)
);

CREATE TABLE tbl_patrolunit
(
    unitid     integer                NOT NULL PRIMARY KEY,
    unitsymbol character varying(128) NOT NULL,
    meaning    character varying(255)
);

CREATE TABLE tbl_patrolwarninglevel
(
    levelid integer NOT NULL PRIMARY KEY,
    meaning character varying(255)
);

CREATE TABLE tbl_patrolgroupstandardsignalmap (
    PatrolGroupStandardSignalMap serial PRIMARY KEY,
    groupId integer NOT NULL,
    equipmentLogicClassId integer DEFAULT NULL,
    standardDicId integer DEFAULT NULL
);

CREATE TABLE tbl_patrolstandardgroup (
    groupId serial NOT NULL PRIMARY KEY,
    groupName character varying(256) NOT NULL,
    equipmentLogicClassId integer ,
    standardDicId integer ,
    note character varying(256)
);