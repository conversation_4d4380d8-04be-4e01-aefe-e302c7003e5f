CREATE TABLE accountalias
(
    accountaliasid serial NOT NULL PRIMARY KEY,
    userid         integer,
    alias          character varying(128),
    checked        integer
);
COMMENT ON COLUMN accountalias.userid IS '用户ID';
COMMENT ON COLUMN accountalias.alias IS '别名';
COMMENT ON COLUMN accountalias.checked IS '是否默认';
CREATE TABLE accountpassworderrrecord
(
    userid         integer NOT NULL PRIMARY KEY,
    passworderrcnt integer,
    freezetime     timestamp
);
COMMENT ON COLUMN accountpassworderrrecord.userid IS '用户ID';
COMMENT ON COLUMN accountpassworderrrecord.passworderrcnt IS '密码错误累计次数';
COMMENT ON COLUMN accountpassworderrrecord.freezetime IS '账号冻结目标时间';
CREATE TABLE accountterminaldevicemap
(
    userid           integer NOT NULL PRIMARY KEY,
    terminaldeviceid character varying(128),
    updatetime       timestamp,
    operatorid       integer
);
COMMENT ON COLUMN accountterminaldevicemap.userid IS '账号id';
COMMENT ON COLUMN accountterminaldevicemap.terminaldeviceid IS '终端设备id';
COMMENT ON COLUMN accountterminaldevicemap.updatetime IS '操作时间';
COMMENT ON COLUMN accountterminaldevicemap.operatorid IS '操作人ID';

CREATE TABLE accounttimespan
(
    accounttimespanid serial NOT NULL PRIMARY KEY,
    userid            integer,
    weekspanchar      character varying(128),
    description       character varying(255)
);
COMMENT ON COLUMN accounttimespan.userid IS '用户ID';
COMMENT ON COLUMN accounttimespan.weekspanchar IS '星期集合，逗号分隔';
COMMENT ON COLUMN accounttimespan.description IS '描述信息';

CREATE TABLE auditreport
(
    auditreportid    serial NOT NULL PRIMARY KEY,
    operationaccount character varying(128),
    level            integer,
    type             character varying(20),
    clientip         character varying(50),
    details          text,
    result           character varying(20),
    createtime       timestamp
);
COMMENT ON COLUMN auditreport.operationaccount IS '操作账户';
COMMENT ON COLUMN auditreport.level IS '级别';
COMMENT ON COLUMN auditreport.type IS '事件类型';
COMMENT ON COLUMN auditreport.clientip IS '客户端ip';
COMMENT ON COLUMN auditreport.details IS '详情';
COMMENT ON COLUMN auditreport.result IS '事件结果';
COMMENT ON COLUMN auditreport.createtime IS '创建时间';

CREATE TABLE departmentcodemap
(
    departmentid       integer NOT NULL PRIMARY KEY,
    parentdepartmentid integer,
    code               character varying(256),
    parentcode         character varying(256)
);
COMMENT ON COLUMN departmentcodemap.departmentid IS '主键id';
COMMENT ON COLUMN departmentcodemap.parentdepartmentid IS '父部门id';
COMMENT ON COLUMN departmentcodemap.code IS '第三方部门唯一标识';
COMMENT ON COLUMN departmentcodemap.parentcode IS '第三方父部门唯一标识';

CREATE TABLE diskfile
(
    fileid bigserial NOT NULL PRIMARY KEY,
    filepath   character varying(128) NOT NULL,
    filename   character varying(128) NOT NULL,
    status     integer,
    createtime timestamp
);

CREATE TABLE gocronexpression
(
    expressionid            serial                 NOT NULL PRIMARY KEY,
    expression              character varying(128) NOT NULL,
    simpleexpression        character varying(128) NOT NULL,
    expressiondescription   character varying(128),
    expressiondescriptionen character varying(128)
);

CREATE TABLE historypassword
(
    id bigserial NOT NULL PRIMARY KEY,
    logonid    character varying(128),
    password   character varying(128),
    updatetime timestamp
);
COMMENT ON COLUMN historypassword.logonid IS '登录名';
COMMENT ON COLUMN historypassword.password IS '密码';
COMMENT ON COLUMN historypassword.updatetime IS '更改时间';

CREATE TABLE ipfilterpolicy
(
    ipfilterpolicyid serial NOT NULL PRIMARY KEY,
    ipaddrset        character varying(255),
    weekspanchar     character varying(128),
    description      character varying(255)
);
COMMENT ON COLUMN ipfilterpolicy.ipaddrset IS 'Ip地址设置（具体IP:******* IP段：*******-*********）';
COMMENT ON COLUMN ipfilterpolicy.weekspanchar IS '星期集合，逗号分隔';
COMMENT ON COLUMN ipfilterpolicy.description IS '描述信息';

CREATE TABLE license
(
    licenseid   serial NOT NULL PRIMARY KEY,
    product     character varying(255),
    uniqueinfo  character varying(1000),
    licensetype integer,
    activetime  timestamp,
    limittime   timestamp
);
COMMENT ON COLUMN license.product IS '产品名称';
COMMENT ON COLUMN license.uniqueinfo IS '系统唯一uuid';
COMMENT ON COLUMN license.licensetype IS '授权码类型 1试用授权码 2正式授权码';
COMMENT ON COLUMN license.activetime IS '激活时间';
COMMENT ON COLUMN license.limittime IS '到期时间';

CREATE TABLE licensefeature
(
    featureid serial NOT NULL PRIMARY KEY,
    name      character varying(255),
    isactive  smallint,
    data      character varying(255)
);
COMMENT ON COLUMN licensefeature.name IS '模块名称';
COMMENT ON COLUMN licensefeature.isactive IS '是否激活 0否 1是';
COMMENT ON COLUMN licensefeature.data IS '附加扩展信息';

CREATE TABLE loginlog
(
    loginlogid    serial                NOT NULL PRIMARY KEY,
    userid        integer               NOT NULL,
    operatingtime timestamp NOT NULL,
    operatingtype character varying(64) NOT NULL,
    clienttype    character varying(32) NOT NULL,
    clientip      character varying(50) NOT NULL
);

CREATE TABLE menuitem
(
    menuitemid        serial NOT NULL PRIMARY KEY,
    parentid          integer,
    path              character varying(255),
    title             character varying(255),
    icon              character varying(255),
    featureid         integer,
    selected          integer,
    expanded          integer,
    pathmatch         character varying(255),
    layoutposition    integer,
    issystemconfig    integer,
    isexternalweb     integer,
    menuhasnavigation integer,
    description       character varying(255),
    alias             character varying(255),
    isembed           integer,
    sortindex         integer
);
COMMENT ON COLUMN menuitem.parentid IS '父菜单ID';
COMMENT ON COLUMN menuitem.path IS '菜单路径';
COMMENT ON COLUMN menuitem.title IS '菜单名';
COMMENT ON COLUMN menuitem.icon IS '菜单图标';
COMMENT ON COLUMN menuitem.featureid IS '所属模块id(对应LicenseFeature表)';
COMMENT ON COLUMN menuitem.selected IS '是否默认选中';
COMMENT ON COLUMN menuitem.expanded IS '是否可展开';
COMMENT ON COLUMN menuitem.pathmatch IS '路径匹配策略';
COMMENT ON COLUMN menuitem.layoutposition IS '菜单排序';
COMMENT ON COLUMN menuitem.issystemconfig IS '是否系统内置';
COMMENT ON COLUMN menuitem.isexternalweb IS '是否外部URL链接';
COMMENT ON COLUMN menuitem.menuhasnavigation IS '是否可跳转';
COMMENT ON COLUMN menuitem.description IS '描述';
COMMENT ON COLUMN menuitem.alias IS '别名';
COMMENT ON COLUMN menuitem.isembed IS '是否嵌入';

CREATE TABLE menuitemstructuremap
(
    menuitemstructuremapid serial NOT NULL PRIMARY KEY,
    menuprofileid          integer,
    menustructureid        integer,
    menuitemid             integer,
    sortindex              integer
);
COMMENT ON COLUMN menuitemstructuremap.menuprofileid IS '菜单方案ID';
COMMENT ON COLUMN menuitemstructuremap.menustructureid IS '菜单目录ID';
COMMENT ON COLUMN menuitemstructuremap.menuitemid IS '菜单ID';
COMMENT ON COLUMN menuitemstructuremap.sortindex IS '排序索引';

CREATE TABLE menupermissiongroup
(
    menupermissiongroupid   serial NOT NULL PRIMARY KEY,
    menupermissiongroupname character varying(128),
    description             character varying(255)
);
COMMENT ON COLUMN menupermissiongroup.menupermissiongroupname IS '菜单权限组名称';
COMMENT ON COLUMN menupermissiongroup.description IS '描述信息';
CREATE TABLE menupermissiongroupmap
(
    menupermissiongroupmapid serial NOT NULL PRIMARY KEY,
    menupermissiongroupid    integer,
    permissionid             integer
);
COMMENT ON COLUMN menupermissiongroupmap.menupermissiongroupid IS '菜单权限组Id';
COMMENT ON COLUMN menupermissiongroupmap.permissionid IS '权限Id';

CREATE TABLE menuprofile
(
    menuprofileid serial NOT NULL PRIMARY KEY,
    name          character varying(255),
    checked       integer,
    description   character varying(255)
);
COMMENT ON COLUMN menuprofile.name IS '方案名称';
COMMENT ON COLUMN menuprofile.checked IS '是否被选中';
COMMENT ON COLUMN menuprofile.description IS '描述';

CREATE TABLE menustructure
(
    menustructureid serial NOT NULL PRIMARY KEY,
    menuprofileid   integer,
    parentid        integer,
    title           character varying(255),
    icon            character varying(255),
    selected        integer,
    expanded        integer,
    hidden          integer,
    sortindex       integer,
    issystem        integer,
    description     character varying(255),
    alias           character varying(255)
);
COMMENT ON COLUMN menustructure.menuprofileid IS '菜单方案ID';
COMMENT ON COLUMN menustructure.parentid IS '父菜单目录ID';
COMMENT ON COLUMN menustructure.title IS '菜单目录名称';
COMMENT ON COLUMN menustructure.icon IS '菜单目录图标';
COMMENT ON COLUMN menustructure.selected IS '是否默认选中';
COMMENT ON COLUMN menustructure.expanded IS '是否可展开';
COMMENT ON COLUMN menustructure.hidden IS '是否隐藏显示';
COMMENT ON COLUMN menustructure.sortindex IS '排序索引';
COMMENT ON COLUMN menustructure.issystem IS '是否系统内置';
COMMENT ON COLUMN menustructure.description IS '描述';
COMMENT ON COLUMN menustructure.alias IS '别名';

CREATE TABLE mobileclientmap
(
    loginuserid integer NOT NULL PRIMARY KEY,
    cid         character varying(128),
    mobileinfo  character varying(256)
);
COMMENT ON COLUMN mobileclientmap.cid IS '客户端id';
COMMENT ON COLUMN mobileclientmap.mobileinfo IS '型号';

CREATE TABLE mobileconditionalpushconfig
(
    loginuserid integer NOT NULL PRIMARY KEY,
    enable      integer,
    conditional text
);
COMMENT ON COLUMN mobileconditionalpushconfig.enable IS '是否启用';
COMMENT ON COLUMN mobileconditionalpushconfig.conditional IS '推送条件';

CREATE TABLE notification
(
    notificationid serial NOT NULL PRIMARY KEY,
    pushed         integer,
    category       character varying(255),
    title          character varying(255),
    content        character varying(255),
    sender         character varying(255),
    color          character varying(255),
    icon           character varying(255),
    weblink        character varying(255),
    applink        character varying(255),
    createtime     timestamp,
    externalid     character varying(50),
    extparam       text
);
COMMENT ON COLUMN notification.pushed IS '推送是否成功';
COMMENT ON COLUMN notification.category IS '通知分类';
COMMENT ON COLUMN notification.title IS '通知标题';
COMMENT ON COLUMN notification.content IS '通知内容';
COMMENT ON COLUMN notification.sender IS '发送者';
COMMENT ON COLUMN notification.color IS '颜色';
COMMENT ON COLUMN notification.icon IS '图标';
COMMENT ON COLUMN notification.weblink IS 'web跳转链接';
COMMENT ON COLUMN notification.applink IS 'app跳转链接';
COMMENT ON COLUMN notification.createtime IS '创建时间';
COMMENT ON COLUMN notification.externalid IS '外部消息厂商请求标识';
COMMENT ON COLUMN notification.extparam IS '扩展参数';

CREATE TABLE notificationlog
(
    notificationlogid numeric(19, 0) NOT NULL PRIMARY KEY,
    stationid         integer        NOT NULL,
    equipmentid       integer        NOT NULL,
    eventid           integer        NOT NULL,
    eventconditionid  integer        NOT NULL,
    starttime         timestamp NOT NULL,
    eventstatus       integer        NOT NULL,
    notifyresult      integer        NOT NULL,
    notifyreciever    integer,
    notifyaddress     character varying(255),
    notifycategory    integer,
    description       character varying(255),
    smssenttime       timestamp,
    extendfield1      character varying(255),
    extendfield2      character varying(255),
    extendfield3      character varying(255)
);
CREATE INDEX idx_22258_notificationlog_idx ON notificationlog USING BTREE (starttime);

CREATE TABLE notificationlogmid
(
    notificationlogid numeric(19, 0) NOT NULL primary key,
    stationid         integer        NOT NULL,
    equipmentid       integer        NOT NULL,
    eventid           integer        NOT NULL,
    eventconditionid  integer        NOT NULL,
    starttime         timestamp NOT NULL,
    eventstatus       integer        NOT NULL,
    notifyresult      integer        NOT NULL,
    notifyreciever    integer,
    notifyaddress     character varying(255),
    notifycategory    integer,
    description       character varying(255),
    smssenttime       timestamp,
    extendfield1      character varying(255),
    extendfield2      character varying(255),
    extendfield3      character varying(255)
);

CREATE TABLE notificationreceiver
(
    notificationreceiverid serial  NOT NULL PRIMARY KEY,
    notificationid         integer,
    loginuserid            integer NOT NULL,
    readed                 integer,
    readedtime             timestamp,
    deleted                integer,
    deletedtime            timestamp
);
COMMENT ON COLUMN notificationreceiver.notificationid IS '消息通知id';
COMMENT ON COLUMN notificationreceiver.readed IS '是否已读';
COMMENT ON COLUMN notificationreceiver.readedtime IS '已读时间';
COMMENT ON COLUMN notificationreceiver.deleted IS '是否删除';
COMMENT ON COLUMN notificationreceiver.deletedtime IS '删除时间';

CREATE TABLE notifymode
(
    notifymodeid     integer                NOT NULL PRIMARY KEY,
    notifymodename   character varying(128) NOT NULL,
    notifymodeformat character varying(255),
    description      character varying(255),
    lastupdatedate   timestamp DEFAULT localtimestamp NOT NULL
);

CREATE TABLE notifyreceiver
(
    notifyreceiverid       integer                NOT NULL,
    notifyreceivercategory integer                NOT NULL,
    notifyreceivername     character varying(128) NOT NULL,
    notifyaddress          character varying(255) NOT NULL,
    notifycontent          character varying(255),
    description            character varying(255),
    lastupdatedate         timestamp DEFAULT localtimestamp NOT NULL
);
ALTER TABLE ONLY notifyreceiver ADD CONSTRAINT idx_22279_primary PRIMARY KEY (notifyreceiverid, notifyreceivercategory);
CREATE INDEX idx_22279_notifyreceivercategory ON notifyreceiver USING BTREE (notifyreceivercategory);

CREATE TABLE notifyreceivermap
(
    eventfilterid          integer NOT NULL,
    eventfilterconditionid integer NOT NULL,
    notifyreceiverid       integer NOT NULL,
    notifyreceivercategory integer NOT NULL,
    notifyserverid         integer NOT NULL,
    notifyservercategory   integer NOT NULL
);
ALTER TABLE ONLY notifyreceivermap ADD CONSTRAINT idx_22285_primary PRIMARY KEY (eventfilterid, eventfilterconditionid, notifyreceiverid, notifyreceivercategory, notifyserverid, notifyservercategory);

CREATE TABLE notifyserver
(
    notifyserverid       integer                NOT NULL,
    notifyservercategory integer                NOT NULL,
    notifyservername     character varying(128) NOT NULL,
    description          character varying(255)
);
ALTER TABLE ONLY notifyserver ADD CONSTRAINT idx_22288_primary PRIMARY KEY (notifyserverid, notifyservercategory);
CREATE INDEX idx_22288_notifyservercategory ON notifyserver USING BTREE (notifyservercategory);

CREATE TABLE permission
(
    permissionid serial NOT NULL PRIMARY KEY,
    name         character varying(128),
    category     integer,
    caption      character varying(255),
    description  character varying(255),
    updatetime   timestamp
);
COMMENT ON COLUMN permission.name IS '权限名称';
COMMENT ON COLUMN permission.category IS '权限分类';
COMMENT ON COLUMN permission.caption IS '标题';
COMMENT ON COLUMN permission.description IS '描述';
COMMENT ON COLUMN permission.updatetime IS '更新时间';

CREATE TABLE permissioncategory
(
    permissioncategoryid serial NOT NULL PRIMARY KEY,
    name                 character varying(128),
    caption              character varying(255),
    description          character varying(255)
);
COMMENT ON COLUMN permissioncategory.name IS '分类名称';
COMMENT ON COLUMN permissioncategory.caption IS '标题';
COMMENT ON COLUMN permissioncategory.description IS '描述';

CREATE TABLE region
(
    regionid    serial NOT NULL PRIMARY KEY,
    regionname  character varying(128),
    description character varying(255)
);
COMMENT ON COLUMN region.regionname IS '区域名称';
COMMENT ON COLUMN region.description IS '描述信息';

CREATE TABLE regionmap
(
    regionmapid         serial  NOT NULL,
    resourcestructureid integer NOT NULL,
    equipmentid         integer NOT NULL,
    regionid            integer NOT NULL
);
COMMENT ON COLUMN regionmap.resourcestructureid IS '资源组ID';
COMMENT ON COLUMN regionmap.equipmentid IS '设备ID';
COMMENT ON COLUMN regionmap.regionid IS '区域ID';
ALTER TABLE ONLY regionmap ADD CONSTRAINT idx_22415_primary PRIMARY KEY (regionmapid, resourcestructureid, equipmentid, regionid);
CREATE UNIQUE INDEX idx_22415_regionmapid ON regionmap USING BTREE (regionmapid);

CREATE TABLE resourcestructure
(
    resourcestructureid       serial NOT NULL PRIMARY KEY,
    sceneid                   integer,
    structuretypeid           integer,
    resourcestructurename     character varying(128),
    parentresourcestructureid integer,
    photo                     character varying(256),
    "position"                character varying(256),
    levelofpath               character varying(128),
    display                   integer,
    sortvalue                 integer,
    extendedfield             json,
    originid                  integer,
    originparentid            integer
);
COMMENT ON COLUMN resourcestructure.sceneid IS '场景ID';
COMMENT ON COLUMN resourcestructure.structuretypeid IS '资源组类型';
COMMENT ON COLUMN resourcestructure.resourcestructurename IS '分组名';
COMMENT ON COLUMN resourcestructure.parentresourcestructureid IS '父分组Id';
COMMENT ON COLUMN resourcestructure.photo IS '图片';
COMMENT ON COLUMN resourcestructure."position" IS '位置信息';
COMMENT ON COLUMN resourcestructure.levelofpath IS '连接路径';
COMMENT ON COLUMN resourcestructure.display IS '是否显示';
COMMENT ON COLUMN resourcestructure.sortvalue IS '排序Index';
COMMENT ON COLUMN resourcestructure.extendedfield IS '扩展信息';
COMMENT ON COLUMN resourcestructure.originid IS '源对象ID';
COMMENT ON COLUMN resourcestructure.originparentid IS '源父对象ID';
CREATE INDEX idx_22487_idx_resourcestructure_levelofpath ON resourcestructure USING BTREE (levelofpath);
CREATE INDEX idx_22487_idx_resourcestructure_1 ON resourcestructure USING BTREE (structuretypeid, originparentid, originid);

CREATE TABLE resourcestructuremask
(
    resourcestructureid integer NOT NULL PRIMARY KEY,
    timegroupid         integer,
    reason              character varying(255),
    starttime           timestamp,
    endtime             timestamp,
    userid              integer
);
COMMENT ON COLUMN resourcestructuremask.resourcestructureid IS '资源组ID';
COMMENT ON COLUMN resourcestructuremask.timegroupid IS '时间组ID';
COMMENT ON COLUMN resourcestructuremask.reason IS '屏蔽原因';
COMMENT ON COLUMN resourcestructuremask.starttime IS '屏蔽开始时间';
COMMENT ON COLUMN resourcestructuremask.endtime IS '屏蔽结束时间';
COMMENT ON COLUMN resourcestructuremask.userid IS '屏蔽人';

CREATE TABLE resourcestructuretype
(
    resourcestructuretypeid   integer NOT NULL PRIMARY KEY,
    sceneid                   integer,
    resourcestructuretypename character varying(128),
    description               character varying(255)
);
COMMENT ON COLUMN resourcestructuretype.resourcestructuretypeid IS '分类Id';
COMMENT ON COLUMN resourcestructuretype.sceneid IS '场景ID';
COMMENT ON COLUMN resourcestructuretype.resourcestructuretypename IS '分类名';
COMMENT ON COLUMN resourcestructuretype.description IS '描述信息';

CREATE TABLE rolegraphicpagemap
(
    roleid        integer NOT NULL PRIMARY KEY,
    graphicpageid integer,
    config        text
);
COMMENT ON COLUMN rolegraphicpagemap.roleid IS '角色id';
COMMENT ON COLUMN rolegraphicpagemap.graphicpageid IS '组态id';
COMMENT ON COLUMN rolegraphicpagemap.config IS '配置';

CREATE TABLE rolepermissionmap
(
    rolepermissionmapid  serial NOT NULL PRIMARY KEY,
    roleid               integer,
    permissioncategoryid integer,
    permissionid         integer
);
COMMENT ON COLUMN rolepermissionmap.roleid IS '角色ID';
COMMENT ON COLUMN rolepermissionmap.permissioncategoryid IS '权限分类ID';
COMMENT ON COLUMN rolepermissionmap.permissionid IS '权限ID';
CREATE INDEX idx_22505_idx_permissioncategoryid ON rolepermissionmap USING BTREE (permissioncategoryid);
CREATE INDEX idx_22505_idx_role_permissionid ON rolepermissionmap USING BTREE (roleid, permissionid);

CREATE TABLE roomcategory
(
    roomcategoryid   integer                NOT NULL,
    roomcategoryname character varying(128) NOT NULL,
    color            character varying(128)
);
ALTER TABLE ONLY roomcategory ADD CONSTRAINT idx_22509_primary PRIMARY KEY (roomcategoryid);

CREATE TABLE scene
(
    sceneid   serial NOT NULL PRIMARY KEY,
    scenename character varying(128),
    checked   integer,
    remark    character varying(128)
);
COMMENT ON COLUMN scene.scenename IS '场景名称';
COMMENT ON COLUMN scene.checked IS '是否已勾选';
COMMENT ON COLUMN scene.remark IS '备注';

CREATE TABLE scenecompmap
(
    scenecompmapid serial NOT NULL PRIMARY KEY,
    type           character varying(128),
    sceneid        integer,
    pagecategory   integer,
    comptype       integer
);

CREATE TABLE scenemenuprofilemap
(
    scenemenuprofilemapid serial NOT NULL PRIMARY KEY,
    sceneid               integer,
    menuprofileid         integer
);
COMMENT ON COLUMN scenemenuprofilemap.sceneid IS '场景Id';
COMMENT ON COLUMN scenemenuprofilemap.menuprofileid IS '菜单方案';

CREATE TABLE scenepermissioncategorymap
(
    scenepermissioncategorymapid integer NOT NULL PRIMARY KEY,
    permissioncategoryid         integer,
    sceneid                      integer
);
COMMENT ON COLUMN scenepermissioncategorymap.scenepermissioncategorymapid IS '场景权限分类唯一ID';
COMMENT ON COLUMN scenepermissioncategorymap.permissioncategoryid IS '权限分类ID';
COMMENT ON COLUMN scenepermissioncategorymap.sceneid IS '场景ID';

CREATE TABLE scenepermissionmap
(
    scenepermissionmapid serial NOT NULL PRIMARY KEY,
    permissionid         integer,
    sceneid              integer
);
COMMENT ON COLUMN scenepermissionmap.permissionid IS '权限点ID';
COMMENT ON COLUMN scenepermissionmap.sceneid IS '场景ID';

CREATE TABLE scenestructure
(
    scenestructureid serial  NOT NULL PRIMARY KEY,
    sceneid          integer NOT NULL,
    objecttypeid     integer NOT NULL,
    displayindex     integer
);

CREATE TABLE securityreport
(
    securityreportid serial NOT NULL PRIMARY KEY,
    operationaccount character varying(128),
    type             integer,
    clientip         character varying(50),
    details          text,
    createtime       timestamp
);
COMMENT ON COLUMN securityreport.operationaccount IS '操作账户';
COMMENT ON COLUMN securityreport.type IS '类别';
COMMENT ON COLUMN securityreport.clientip IS '客户端ip';
COMMENT ON COLUMN securityreport.details IS '详情';
COMMENT ON COLUMN securityreport.createtime IS '创建时间';

CREATE TABLE softwareversion
(
    id         serial                 NOT NULL PRIMARY KEY,
    modulename character varying(128) NOT NULL,
    version    character varying(256) NOT NULL,
    updatetime timestamp NOT NULL,
    feature    character varying(256) NOT NULL
);

CREATE TABLE systemconfig
(
    systemconfigid    serial NOT NULL PRIMARY KEY,
    systemconfigkey   character varying(255),
    systemconfigvalue character varying(255),
    systemconfigtype  integer,
    description       character varying(255)
);
COMMENT ON COLUMN systemconfig.systemconfigkey IS '键名';
COMMENT ON COLUMN systemconfig.systemconfigvalue IS '对应键值';
COMMENT ON COLUMN systemconfig.systemconfigtype IS '键值对业务类型';
COMMENT ON COLUMN systemconfig.description IS '描述信息';
CREATE UNIQUE INDEX idx_22601_systemconfigkey ON systemconfig USING BTREE (systemconfigkey);

CREATE TABLE userconfig
(
    userconfigid serial NOT NULL PRIMARY KEY,
    userid       integer,
    configtype   integer,
    configkey    character varying(255),
    configvalue  character varying(255)
);
COMMENT ON COLUMN userconfig.userid IS '用户ID';
COMMENT ON COLUMN userconfig.configtype IS '配置类型 1TTS';
COMMENT ON COLUMN userconfig.configkey IS '用户信息配置键';
COMMENT ON COLUMN userconfig.configvalue IS '用户信息配置值';
CREATE INDEX idx_24362_idx_userid_usertype ON userconfig USING BTREE (userid, configtype);

CREATE TABLE webclientlog
(
    webclientlogid serial NOT NULL PRIMARY KEY,
    username       character varying(255),
    clientip       character varying(255),
    businessmodule character varying(50),
    content        character varying(1000),
    remark         character varying(255),
    createtime     timestamp
);
COMMENT ON COLUMN webclientlog.username IS '用户名称';
COMMENT ON COLUMN webclientlog.clientip IS '客户端ip';
COMMENT ON COLUMN webclientlog.businessmodule IS '模块类型';
COMMENT ON COLUMN webclientlog.content IS '内容';
COMMENT ON COLUMN webclientlog.remark IS '备注';
COMMENT ON COLUMN webclientlog.createtime IS '创建时间';
CREATE INDEX idx_24376_idx_createtime ON webclientlog USING BTREE (createtime);
CREATE INDEX idx_24376_idx_username_createtime ON webclientlog USING BTREE (username, createtime);