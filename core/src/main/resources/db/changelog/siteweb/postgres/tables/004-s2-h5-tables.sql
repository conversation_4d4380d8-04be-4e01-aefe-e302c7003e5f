CREATE TABLE tbl_h5camera
(
    cameraid        integer                NOT NULL PRIMARY KEY,
    cameraname      character varying(255) NOT NULL,
    cameraip        character varying(128) NOT NULL,
    cameraport      integer                NOT NULL,
    channelnumber   character varying(32),
    cameragroupid   integer,
    username        character varying(128),
    password        character varying(32),
    vendorid        integer,
    vendorname      character varying(128),
    cameraindexcode character varying(255),
    cameratype      integer,
    cameratypename  character varying(255),
    updatetime      timestamp,
    description     character varying(255)
);

CREATE TABLE tbl_h5cameragroup
(
    cameragroupid   integer NOT NULL PRIMARY KEY,
    cameragroupname character varying(255),
    parentid        integer,
    description     character varying(255)
);

CREATE TABLE tbl_h5reportcentertypemap
(
    H5ReportCenterTypeMapId serial PRIMARY KEY,
    reportid   integer           NOT NULL,
    centertype integer DEFAULT 0 NOT NULL
);

CREATE TABLE tbl_h5reportcronexpression
(
    cronid         integer NOT NULL PRIMARY KEY,
    cronexpression character varying(128) DEFAULT ''::CHARACTER VARYING NOT NULL,
    meaning        character varying(128) DEFAULT ''::CHARACTER VARYING NOT NULL
);

CREATE TABLE tbl_h5reportprocedureparameters
(
    H5ReportProcedureParameters serial PRIMARY KEY,
    pname      character varying(255) NOT NULL,
    parameters character varying(4000)
);

CREATE TABLE tbl_h5reporttask
(
    taskid          serial                 NOT NULL PRIMARY KEY,
    taskname        character varying(128) NOT NULL,
    cronid          integer                NOT NULL,
    isenable        integer                NOT NULL,
    description     character varying(255),
    reportid        integer                NOT NULL,
    queryparameters text                   NOT NULL,
    creator         integer,
    createtime      timestamp,
    lastupdatetime  timestamp
);