CREATE TABLE schedule
(
    scheduleid      serial NOT NULL PRIMARY KEY,
    shiftgroupmapid integer,
    shiftid         integer,
    scheduletime    date
);
COMMENT ON COLUMN schedule.shiftgroupmapid IS '人员班次映射id';
COMMENT ON COLUMN schedule.shiftid IS '班次id';
COMMENT ON COLUMN schedule.scheduletime IS '排版日期';

CREATE TABLE shift
(
    shiftid        serial NOT NULL PRIMARY KEY,
    shiftname      character varying(50),
    shiftstarttime time WITHOUT TIME zone,
    shiftendtime   time WITHOUT TIME zone,
    shiftcolor     character varying(50),
    departmentid   integer
);
COMMENT ON COLUMN shift.shiftname IS '班次名称';
COMMENT ON COLUMN shift.shiftstarttime IS '班次开始时间';
COMMENT ON COLUMN shift.shiftendtime IS '班次结束时间';
COMMENT ON COLUMN shift.shiftcolor IS '班次颜色';
COMMENT ON COLUMN shift.departmentid IS '部门Id';

CREATE TABLE shiftgroup
(
    shiftgroupid   serial NOT NULL PRIMARY KEY,
    shiftgroupname character varying(50),
    remark         character varying(255),
    departmentid   integer
);
COMMENT ON COLUMN shiftgroup.shiftgroupname IS '班组名称';
COMMENT ON COLUMN shiftgroup.remark IS '备注';
COMMENT ON COLUMN shiftgroup.departmentid IS '部门id';

CREATE TABLE shiftgroupmap
(
    shiftgroupmapid serial NOT NULL PRIMARY KEY,
    shiftgroupid    integer,
    employeeid      integer,
    displayindex    integer
);
COMMENT ON COLUMN shiftgroupmap.shiftgroupid IS '班组id';
COMMENT ON COLUMN shiftgroupmap.employeeid IS '员工id';
COMMENT ON COLUMN shiftgroupmap.displayindex IS '展示顺序';