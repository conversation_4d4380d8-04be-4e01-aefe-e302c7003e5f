CREATE TABLE convergenceevent
(
    id bigserial NOT NULL PRIMARY KEY,
    eventname         character varying(255) NOT NULL,
    convergencetype   integer                NOT NULL,
    convergenceruleid integer                NOT NULL,
    birthtime         timestamp NOT NULL,
    convergencecount  integer                NOT NULL,
    status            integer,
    confirmtime       timestamp,
    possiblecauses    character varying(1024),
    equipmentid       integer,
    eventid           integer,
    eventconditionid  integer,
    cleartime         timestamp,
    confirmerid       integer,
    confirmername     character varying(128)
);

CREATE TABLE eventconvergencerule
(
    id                  serial                 NOT NULL PRIMARY KEY,
    rulename            character varying(255) NOT NULL,
    convergencetype     integer                NOT NULL,
    equipmentcategory   integer                NOT NULL,
    startexpression     character varying(1024),
    filtercondition     character varying(2048),
    convergenceinterval integer,
    startcount          integer,
    endcount            integer,
    possiblecauses      character varying(1024),
    parentruleid        integer,
    levelofpath         character varying(1024)
);

CREATE TABLE powerequipmentconnection
(
    id bigserial NOT NULL PRIMARY KEY,
    parentequipmentid integer NOT NULL,
    equipmentid       integer NOT NULL,
    levelofpath       character varying(255)
);