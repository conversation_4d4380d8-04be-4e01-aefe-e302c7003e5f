CREATE TABLE tbl_resourceequipment
(
    stationid              integer NOT NULL,
    stationname            character varying(255),
    equipmentid            integer NOT NULL,
    equipmentname          character varying(255),
    systemtype             character varying(255),
    systemid               character varying(255),
    sensorlocation         character varying(255),
    monitoredequipmentname character varying(255),
    ratecapacity           character varying(255),
    deviceid               character varying(255),
    assetid                character varying(255),
    description            character varying(255),
    extendfield1           character varying(255),
    extendfield2           character varying(255),
    isconfig               integer
);
ALTER TABLE ONLY tbl_resourceequipment ADD CONSTRAINT idx_23829_primary PRIMARY KEY (equipmentid, stationid);

CREATE TABLE tbl_resourcehouse
(
    stationid    integer NOT NULL,
    stationname  character varying(255),
    houseid      integer NOT NULL,
    housename    character varying(255),
    houseno      numeric(18, 0),
    systemno     numeric(18, 0),
    description  character varying(255),
    extendfield1 character varying(255),
    extendfield2 character varying(255),
    isconfig     integer
);
ALTER TABLE ONLY tbl_resourcehouse ADD CONSTRAINT idx_23834_primary PRIMARY KEY (houseid, stationid);

CREATE TABLE tbl_resourcesignal
(
    equipmenttemplateid   integer NOT NULL,
    equipmenttemplatename character varying(255),
    signalid              integer NOT NULL,
    signalname            character varying(255),
    channelno             integer,
    channellevel          integer,
    description           character varying(255),
    extendfield1          character varying(255),
    extendfield2          character varying(255),
    isconfig              integer
);
ALTER TABLE ONLY tbl_resourcesignal ADD CONSTRAINT idx_23839_primary PRIMARY KEY (equipmenttemplateid, signalid);

CREATE TABLE tbl_resourcestation
(
    stationid      integer NOT NULL PRIMARY KEY,
    stationname    character varying(255),
    stationaddress character varying(255),
    description    character varying(255),
    extendfield1   character varying(255),
    extendfield2   character varying(255),
    isconfig       integer
);