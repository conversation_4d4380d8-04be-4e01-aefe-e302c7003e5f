CREATE TABLE prealarm
(
    prealarmid           serial  NOT NULL PRIMARY KEY,
    prealarmpointid      integer NOT NULL,
    meanings             character varying(128),
    prealarmseverity     integer,
    prealarmseverityname character varying(128),
    prealarmcategory     integer,
    prealarmcategoryname character varying(128),
    color                character varying(128),
    uniqueid             character varying(256),
    uniquename           character varying(256),
    objectid             integer,
    objecttypeid         integer,
    objectname           character varying(128),
    resourcestructureid  integer,
    levelofpath          character varying(256),
    levelofpathname      character varying(256),
    triggervalue         character varying(128),
    unit                 character varying(10),
    sampletime           timestamp,
    starttime            timestamp,
    endtime              timestamp,
    confirmtime          timestamp,
    confirmid            integer,
    confirmname          character varying(128),
    remark               character varying(255),
    businesstypeid       integer
);

CREATE TABLE prealarmcategory
(
    categoryid       serial                NOT NULL PRIMARY KEY,
    categoryname     character varying(64) NOT NULL,
    parentcategoryid integer,
    description      character varying(128)
);

CREATE TABLE prealarmhistory
(
    PreAlarmHistoryId serial PRIMARY KEY,
    prealarmid           integer NOT NULL,
    prealarmpointid      integer NOT NULL,
    meanings             character varying(128),
    prealarmseverity     integer,
    prealarmseverityname character varying(128),
    prealarmcategory     integer,
    prealarmcategoryname character varying(128),
    color                character varying(128),
    uniqueid             character varying(256),
    uniquename           character varying(256),
    objectid             integer,
    objecttypeid         integer,
    objectname           character varying(128),
    resourcestructureid  integer,
    levelofpath          character varying(256),
    levelofpathname      character varying(256),
    triggervalue         character varying(128),
    unit                 character varying(10),
    sampletime           timestamp,
    starttime            timestamp,
    endtime              timestamp,
    confirmtime          timestamp,
    confirmid            integer,
    confirmname          character varying(128),
    remark               character varying(255),
    businesstypeid       integer
);

CREATE TABLE prealarmpoint
(
    prealarmpointid     serial NOT NULL PRIMARY KEY,
    prealarmpointname   character varying(128),
    meanings            character varying(128),
    expression          character varying(256),
    abnormalexpression  character varying(256),
    executecron         character varying(256),
    prealarmcategory    integer,
    uniqueid            character varying(256),
    objectid            integer,
    objecttypeid        integer,
    objectname          character varying(128),
    resourcestructureid integer,
    levelofpath         character varying(256),
    levelofpathname     character varying(256),
    prealarmseverity    integer,
    enable              integer,
    unit                character varying(10),
    masktype            integer,
    maskduration        character varying(128),
    maskstarttime       timestamp,
    maskendtime         timestamp,
    stateful            integer,
    modifier            integer,
    modifiername        character varying(128),
    modifytime          timestamp
);

CREATE TABLE prealarmseverity
(
    prealarmseverityid   serial                NOT NULL PRIMARY KEY,
    prealarmseverityname character varying(64) NOT NULL,
    color                character varying(128),
    description          character varying(128)
);

CREATE TABLE prealarmchange
(
    sequenceid            character varying(128) NOT NULL,
    serialno bigserial NOT NULL PRIMARY KEY,
    operationtype         integer                NOT NULL,
    prealarmid            bigint                 NOT NULL,
    prealarmname          character varying(128) NOT NULL,
    meanings              character varying(255) NOT NULL,
    triggervalue          character varying(255) NOT NULL,
    prealarmpointid       bigint                 NOT NULL,
    prealarmcategory      integer                NOT NULL,
    prealarmcategoryname  character varying(128),
    prealarmseverity      integer                NOT NULL,
    prealarmseverityname  character varying(128),
    starttime             timestamp NOT NULL,
    endtime               timestamp,
    confirmtime           timestamp,
    confirmorid           integer,
    confirmorname         character varying(128),
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(128) NOT NULL,
    equipmentcategory     integer                NOT NULL,
    equipmentcategoryname character varying(128) NOT NULL,
    equipmentvendor       character varying(128),
    centerid              integer                NOT NULL,
    centername            character varying(128),
    structureid           integer                NOT NULL,
    structurename         character varying(128),
    stationid             integer                NOT NULL,
    stationname           character varying(128),
    stationcategoryid     integer                NOT NULL,
    resourcestructureid   integer                NOT NULL,
    levelofpathname       character varying(255),
    inserttime            timestamp NOT NULL
);
CREATE INDEX idx_22337_tbl_alarmchange_id1 ON prealarmchange USING BTREE (sequenceid);