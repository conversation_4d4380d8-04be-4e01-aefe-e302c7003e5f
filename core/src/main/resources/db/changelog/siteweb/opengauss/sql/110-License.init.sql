-- 试用版license
INSERT INTO license(LicenseId, Product, licensetype, ActiveTime, LimitTime)
VALUES (1, 'SiteWeb基础设施管理系统 V6.0', 1, localtimestamp, localtimestamp + INTERVAL '3 months');

-- 模块信息
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (1,'SiteWeb6基础功能',1,'[{"Key":"equipmentNum","Val":"50"}]');
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (2,'3D数字化工具',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (3,'温度云图',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (4,'机器人监控',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (5,'暖通运行状态管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (6,'蓄电池管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (7,'视频管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (8,'电话语音通知',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (9,'语音盒短信通知',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (10,'告警箱通知',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (11,'容量管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (12,'配电管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (13,'能耗管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (14,'预警管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (15,'机架管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (16,'资产管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (17,'空调群控',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (18,'控制联动',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive) VALUES (19,'门禁管理',1);
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (20,'机房AI节能',0,'[{"Key":"AccessQuantity","Val":"0"},{"Key":"ExpiryDate","Val":"2000/01/01"}]');
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (21,'站点AI节能',0,'[{"Key":"AccessQuantity","Val":"0"},{"Key":"ExpiryDate","Val":"2000/01/01"}]');
INSERT INTO LicenseFeature(FeatureId,Name,IsActive,Data) VALUES (22,'中央空调AI节能',0,'[{"Key":"AccessSysQuantity","Val":"0"},{"Key":"AccessRoomQuantity","Val":"0"},{"Key":"ExpiryDate","Val":"2000/01/01"}]');