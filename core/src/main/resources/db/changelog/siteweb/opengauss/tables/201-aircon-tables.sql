CREATE TABLE aircon_autocontrolequipmentchangelog
(
    logid               serial                NOT NULL PRIMARY KEY,
    stationid           integer,
    stationname         character varying(510),
    monitorunitid       integer,
    monitorunitname     character varying(510),
    virtualequipmentid  integer               NOT NULL,
    groupname           character varying(256),
    groupnamenew        character varying(256),
    operatetype         integer               NOT NULL,
    operatetypelabel    character varying(32) NOT NULL,
    operatemodule       integer               NOT NULL,
    operatemodulelabel  character varying(128) NOT NULL,
    suboperatetype      integer,
    suboperatetypelabel character varying(32),
    subobjecttype       integer,
    subobjecttypelabel  character varying(128),
    operator            character varying(256),
    operatorid          integer,
    inserttime          timestamp NOT NULL,
    changecontent       character varying(8000),
    extendfield1        character varying(510)
);

CREATE TABLE aircon_autocontrolpara
(
    virtualequipmentid    integer                NOT NULL PRIMARY KEY,
    virtualequipmentname  character varying(256) NOT NULL,
    groupname             character varying(256) NOT NULL,
    stationid             integer                NOT NULL,
    sampleraddress        integer                NOT NULL,
    monitorunitid         integer                NOT NULL,
    paraenable            integer                NOT NULL,
    rollingcount          integer                NOT NULL,
    tempcomputemode       double precision       NOT NULL,
    tempcoolall           double precision       NOT NULL,
    tempcoolstart         double precision       NOT NULL,
    worktemp              double precision       NOT NULL,
    tempinfanstop         double precision       NOT NULL,
    tempbottomlow         double precision       NOT NULL,
    tempsetting           double precision       NOT NULL,
    runperiod             integer                NOT NULL,
    operationinterval     integer                NOT NULL,
    tempdiff              double precision       NOT NULL,
    faninstall            integer                NOT NULL,
    tempfanstart          double precision       NOT NULL,
    outintempdiff         double precision       NOT NULL,
    tempoutfanstop        double precision       NOT NULL,
    enablewarm            integer                NOT NULL,
    temphot               double precision       NOT NULL,
    temphotstart          double precision       NOT NULL,
    temphotall            double precision       NOT NULL,
    lastsuccussdeploytime timestamp,
    lastdeploystate       integer,
    lastdeploytime        timestamp,
    updatetime            timestamp,
    operationor           integer,
    description           character varying(510),
    extendfield           character varying(510)
);

CREATE TABLE aircon_batchcontrolequipmentmap
(
    groupid     character varying(510) NOT NULL,
    stationid   integer                NOT NULL,
    equipmentid integer                NOT NULL,
    updateid    integer,
    updatetime  timestamp,
    description character varying(510),
    extendfield character varying(510)
);
ALTER TABLE ONLY aircon_batchcontrolequipmentmap ADD CONSTRAINT idx_21352_primary PRIMARY KEY (groupid, stationid, equipmentid);

CREATE TABLE aircon_batchcontrolgroup
(
    groupid     character varying(510) NOT NULL PRIMARY KEY,
    groupname   character varying(256) NOT NULL,
    creatorid   integer,
    createtime  timestamp,
    updateid    integer,
    updatetime  timestamp,
    description character varying(510),
    extendfield character varying(510)
);

CREATE TABLE aircon_batchcontrolgroupchangelog
(
    logid            serial                 NOT NULL PRIMARY KEY,
    operatetype      integer                NOT NULL,
    operatetypelabel character varying(32)  NOT NULL,
    operator         character varying(256),
    operatorid       integer,
    groupid          character varying(510) NOT NULL,
    groupname        character varying(256),
    groupnamenew     character varying(256),
    inserttime       timestamp NOT NULL,
    changecontent    character varying(8000),
    extendfield1     character varying(510)
);

CREATE TABLE aircon_batchcontrolrecord
(
    recordid        serial                 NOT NULL PRIMARY KEY,
    stationid       integer                NOT NULL,
    equipmentid     integer                NOT NULL,
    controlid       integer                NOT NULL,
    stdcontrolid    integer                NOT NULL,
    stdworkmodeflag integer                NOT NULL,
    airstdtypeid    integer                NOT NULL,
    aircommon2no    integer                NOT NULL,
    serialno        integer                NOT NULL,
    uuid            character varying(510) NOT NULL,
    inserttime      timestamp NOT NULL,
    extendfield     character varying(510)
);

CREATE TABLE aircon_complexindexmap
(
    mapid              character varying(510) NOT NULL PRIMARY KEY,
    stationid          integer                NOT NULL,
    virtualequipmentid integer                NOT NULL,
    complexindexid     integer                NOT NULL,
    updatetime         timestamp,
    updateid           integer,
    extendfield        character varying(510)
);

CREATE TABLE aircon_equipmentcontrolpara
(
    virtualequipmentid integer NOT NULL,
    equipmentid        integer NOT NULL,
    stationid          integer NOT NULL,
    monitorunitid      integer NOT NULL,
    inserttime         timestamp,
    operationor        integer,
    extendfield        character varying(510)
);
ALTER TABLE ONLY aircon_equipmentcontrolpara ADD CONSTRAINT idx_21381_primary PRIMARY KEY (virtualequipmentid, equipmentid);

CREATE TABLE aircon_fancontrolpara
(
    virtualequipmentid integer NOT NULL,
    equipmentid        integer NOT NULL,
    stationid          integer NOT NULL,
    monitorunitid      integer NOT NULL,
    inserttime         timestamp,
    operationor        integer,
    extendfield        character varying(510)
);
ALTER TABLE ONLY aircon_fancontrolpara ADD CONSTRAINT idx_21384_primary PRIMARY KEY (virtualequipmentid, equipmentid);

CREATE TABLE aircon_stdsignal
(
    typeid          integer                NOT NULL,
    stdsignalid     integer                NOT NULL,
    stdsignalname   character varying(510) NOT NULL,
    stdsignalunit   character varying(32),
    stdsignalremark character varying(510),
    stdsignaltype   integer                NOT NULL,
    needshow        integer                NOT NULL,
    commandcolor    integer                NOT NULL,
    businesstypeid  integer                NOT NULL,
    maprequirement  integer                NOT NULL,
    extendfield     character varying(510)
);
ALTER TABLE ONLY aircon_stdsignal ADD CONSTRAINT idx_21387_primary PRIMARY KEY (typeid, stdsignalid);

CREATE TABLE aircon_stdsignaltype
(
    signaltypeid     integer                NOT NULL PRIMARY KEY,
    signaltypename   character varying(256) NOT NULL,
    signaltyperemark character varying(510),
    extendfield      character varying(510)
);

CREATE TABLE aircon_stdtype
(
    typeid      integer                NOT NULL PRIMARY KEY,
    typename    character varying(256) NOT NULL,
    extendfield character varying(510)
);

CREATE TABLE aircon_tempcontrolpara
(
    virtualequipmentid integer NOT NULL,
    equipmentid        integer NOT NULL,
    stationid          integer NOT NULL,
    monitorunitid      integer NOT NULL,
    inserttime         timestamp,
    operationor        integer,
    beouttemp          integer,
    extendfield        character varying(510)
);
ALTER TABLE ONLY aircon_tempcontrolpara ADD CONSTRAINT idx_21400_primary PRIMARY KEY (virtualequipmentid, equipmentid);

CREATE TABLE aircon_templatestdsignalmap
(
    equipmenttemplateid integer NOT NULL,
    typeid              integer NOT NULL,
    stdsignalid         integer NOT NULL,
    defaultvalue        numeric(18, 0),
    swsignalid          integer,
    swsignalname        character varying(256),
    swsignalchanelnum   integer,
    swcmdtoken          character varying(128),
    swparam             character varying(128),
    swoperator          character varying(128),
    swcmpvalue          double precision,
    updatedate          timestamp NOT NULL,
    updaterid           integer NOT NULL,
    updatername         character varying(256),
    extendfield1        character varying(256),
    extendfield2        character varying(256),
    extendfield3        character varying(256)
);
ALTER TABLE ONLY aircon_templatestdsignalmap ADD CONSTRAINT idx_21403_primary PRIMARY KEY (equipmenttemplateid, typeid, stdsignalid);

CREATE TABLE aircon_templatestdtype
(
    equipmenttemplateid integer NOT NULL PRIMARY KEY,
    typeid              integer,
    typename            character varying(256),
    updatedate          timestamp NOT NULL,
    updaterid           integer NOT NULL,
    updatername         character varying(256),
    extendfield         character varying(510)
);

CREATE TABLE aircon_zonecontroloperation
(
    operationid      serial                 NOT NULL PRIMARY KEY,
    schemeid         character varying(510) NOT NULL,
    operationtime    character varying(32)  NOT NULL,
    operationcmdid   integer                NOT NULL,
    operationcmdname character varying(128)  NOT NULL,
    params           character varying(128),
    updatetime       timestamp NOT NULL,
    updateid         integer                NOT NULL,
    extendfield      character varying(510)
);

CREATE TABLE aircon_zonecontrolscheme
(
    schemeid           character varying(510) NOT NULL PRIMARY KEY,
    schemename         character varying(510) NOT NULL,
    startdate          character varying(32)  NOT NULL,
    enddate            character varying(32)  NOT NULL,
    virtualequipmentid integer                NOT NULL,
    stationid          integer                NOT NULL,
    updatetime         timestamp NOT NULL,
    updateid           integer                NOT NULL,
    extendfield        character varying(510)
);