CREATE TABLE tbl_menuitem
(
    menuitemid        integer NOT NULL PRIMARY KEY,
    parentid          integer,
    path              character varying(510),
    title             character varying(510),
    icon              character varying(510),
    selected          smallint,
    expanded          smallint,
    pathmatch         character varying(510),
    layoutposition    integer,
    issystemconfig    smallint,
    isexternalweb     smallint,
    menuhasnavigation smallint,
    description       character varying(510)
);

CREATE TABLE tbl_menuitems
(
    menuitemsid       integer                NOT NULL PRIMARY KEY,
    parentmenuitemsid integer,
    menuitemsname     character varying(256) NOT NULL,
    description       character varying(510)
);

CREATE TABLE tbl_menuitemstructuremap
(
    menuitemstructuremapid integer NOT NULL PRIMARY KEY,
    menuprofileid          integer NOT NULL,
    menustructureid        integer NOT NULL,
    menuitemid             integer NOT NULL,
    sortindex              integer
);

CREATE TABLE tbl_menuprofileinfo
(
    menuprofileid integer NOT NULL PRIMARY KEY,
    name          character varying(510),
    checked       smallint,
    description   character varying(510)
);

CREATE TABLE tbl_menus
(
    menusid     integer                NOT NULL PRIMARY KEY,
    menusname   character varying(256) NOT NULL,
    description character varying(510)
);

CREATE TABLE tbl_menusmap
(
    menusid     integer NOT NULL,
    menuitemsid integer NOT NULL
);
ALTER TABLE ONLY tbl_menusmap ADD CONSTRAINT idx_23497_primary PRIMARY KEY (menuitemsid, menusid);

CREATE TABLE tbl_menustructureinfo
(
    menustructureid integer NOT NULL PRIMARY KEY,
    menuprofileid   integer,
    parentid        integer,
    title           character varying(510),
    icon            character varying(510),
    selected        smallint,
    expanded        smallint,
    hidden          smallint,
    sortindex       integer,
    issystem        smallint,
    description     character varying(510)
);