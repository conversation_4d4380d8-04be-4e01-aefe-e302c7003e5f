-- 设备
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('1','batteryIndicator','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('2','batteryIndicatorGroup','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('3','multiBranchList','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('4','thermomenter','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('5','historyCurve','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('6','equipmentAssetComponent','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('7','equipmentSignalsComponent','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('8','branchesStatus','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('9','signalCard','-1','7','2');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('13','waterLogLine','1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('10','equipmentOthersComponent','-1','7','2');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('11','switch','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('12','canvasGauges','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('13','setvalue','-1','7','2');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('14','switchCommand','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('15','airConditionStatus','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('16','signal','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('17','equipment','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('18','status','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('19','ngbTabTemplate','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('20','multiTab','-1','7','2');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('21','alarmTable','-1','7','2');







-- 通用
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('10','subSysEquipmentSignalStatus','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('30','compRouting','-1','-1', '1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('31','text','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('32','card','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('33','line','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('34','icon','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('35','img','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('36','safetyDays','1','8','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('37','cameraComponent','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('38','powerdistribution','-1','8','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('39','threed','-1','2','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('40','heatmap','-1','2','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('41','contactInformation','-1','-1','1');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('42','systemnavigation','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('43','video','-1','-1','1');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('44','rack','-1','-1','1');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('46','menuRouting','-1','-1', '1');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('47','multiPlans','-1','-1','1');


-- 动环
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('60','deviceConnectState','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('62','collectorState','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('63','buildingEquipmentsStatus','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('64','serverCommunicationStateComponent','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('66','signalText','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('67','roomSignalText','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('68','roomStatePic','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('69','indicatorConditionPic','-1','8','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('70','signalGroupAlarm','-1','8','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('71','roomDeviceGroupAlarm','-1','8','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('72','alarmLine','-1','-1','3');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('73','equipmentQuantityStatistics','-1','-1','3');
-- INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('74','historyAlarmTable','-1','-1','3');
-- INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('75','historyAlarmList','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('76','alarmPieChart','1','8','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('77','alarmOverview','1','3','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('78','roomAlarmList','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('80','alarmNumberListComponent','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('81','roomAlarmMatrix','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('82','alarmLevelCountChart','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('83','alarmDurationTimeCountChartSetForm','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('84','homepageAlarm','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('85','devicesBigData','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('86','controlSet','-1','-1','3');

insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('89','temperatureHotColdAisle','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('90','customSignalGroup','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('91','roomSignalGroupAlarm','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('93','equipmentGroupRadar','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('94','devices','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('95','alarm','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('96','alarmStackBarChart','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('97','holidayalarm','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('98','collectCommunicationStatusComponent','-1','-1','3');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('99','topologyComponent','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('100','topologyDeviceComponent','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('101','signal','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('102','equipment','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('103','batteryStringSignal','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('104','statePic','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('105','roomTempHumidity','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('106','alarmDurationOrTimes','-1','-1','3');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('107','waterLogLine','1','-1','3');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('108', 'historyCurve', '-1', '-1', '3');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('109','switch','-1','-1','3');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('110','controlCommandSet','-1','-1','3');


-- 指标
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('120','indicatorHistoryChart','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('121','realtimeIndicator','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('122','historyIndicatorAggregate','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('123','indicatorProgressChart','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('124','indicatorDoubleProgressChart','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('125','indicatorText','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('126','apiMatrixTable','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('127','indexComparison','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('128','progressBar','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('129','indicatorConditionStyle','-1','-1','4');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('130','indexDoughnutComparison','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('131','lineChartIndicator','-1','-1','4');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('132','indicatorDoughnutChart','-1','-1','4');

-- 能耗
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('140','energyPanoramaChart','-1','-1','5');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('141','prealarmLevelCount','-1','-1','5');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('142','realCapacityChart','-1','-1','5');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('143','capacityPieChart','-1','-1','5');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('144','preAlarmList','-1','-1','5');

-- 定制
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('200','digitalMap','-1','8','6');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('201','stationInfo','21','-1','6');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('202','deviceBaseClassGroup','-1','-1','6');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('203','tempHumidityBasicClass','-1','-1','6');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('204','deviceSignalGroup','-1','-1','6');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('205','flowLineGroup','1','8','6');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('206','universalTable','-1','-1','6');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('207','apiCustomBarChart','-1','-1','6');
-- insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('208','apiCustomGaugeChart','-1','-1','6');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('209','subSysEquipmentSignalStatus','-1','-1','6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('210','emsCommandRing','-1','-1','6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('211','thermalRunaway','-1','-1','6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('315', 'idcNavigation', '-1', '-1', '6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('316', 'availabilityAnalysis', '-1', '-1', '6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('318', 'signalExtremum', '-1', '-1', '6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('319', 'subSysEquipmentSignalAlarm', '-1', '-1', '6');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('320', 'rackView', -1, -1, 6);

-- INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('20001','roomWaterLog','1','-1','6');

-- 机器人
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('300','robotPatrolStatistics','-1','-1','7');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('301','robotPatrolEnvironment','-1','-1','7');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('302','robotListAndInfo','-1','-1','7');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('303','robotControl','-1','-1','7');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('304','robotLocation','-1','-1','7');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('305','robotVideo','-1','-1','7');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('313', 'energyStatistic', '-1', '-1', '7');

-- 图表类
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('306','barChart','-1','-1','8');
insert into scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) values('307','pieChart','-1','-1','8');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('308','lineChart','-1','-1','8');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('309','tableChart','-1','-1','8');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('310','gaugeChart','-1','-1','8');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('311','radarChart','-1','-1','8');
-- INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('314','textNumber','-1','-1','8');
INSERT INTO scenecompmap (SceneCompMapId, type, SceneId, PageCategory, CompType) VALUES('317','textNumberNoPlans','-1','-1','8');
