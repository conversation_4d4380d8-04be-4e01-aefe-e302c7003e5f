CREATE TABLE wo_diccmdchecklist
(
    checkid           serial                 NOT NULL PRIMARY KEY,
    baseequipmentid   integer                NOT NULL,
    baseequipmentname character varying(256),
    basetypeid        numeric(12, 0)         NOT NULL,
    basetypename      character varying(256) NOT NULL,
    checktype         character varying(20),
    ismust            character varying(20),
    note              character varying(510)
);

CREATE TABLE wo_diceventcategorymap
(
    baseequipmentid        integer                NOT NULL,
    baseequipmentname      character varying(256),
    basetypeid             numeric(10, 0)         NOT NULL,
    basetypename           character varying(256) NOT NULL,
    checktype              character varying(20),
    ismust                 character varying(20),
    sitewebeventcategory   character varying(510),
    sitewebeventcategoryid integer
);

CREATE TABLE wo_diceventchecklist
(
    baseequipmentid   integer                NOT NULL,
    baseequipmentname character varying(256),
    basetypeid        numeric(12, 0)         NOT NULL,
    basetypename      character varying(256) NOT NULL,
    checktype         character varying(20),
    ismust            character varying(20),
    note              character varying(510),
    checkid           serial                 NOT NULL PRIMARY KEY
);

CREATE TABLE wo_dicsigchecklist
(
    baseequipmentid   integer                NOT NULL,
    baseequipmentname character varying(256),
    basetypeid        numeric(12, 0)         NOT NULL,
    basetypename      character varying(256) NOT NULL,
    checktype         character varying(20),
    limitdown         double precision,
    limitup           double precision,
    note              character varying(510),
    checkid           serial                 NOT NULL PRIMARY KEY,
    ismust            character varying(20)
);

CREATE TABLE wo_dicstationtypemap
(
    wr_itemid      integer NOT NULL PRIMARY KEY,
    siteweb_itemid integer,
    savetime       timestamp
);

CREATE TABLE wo_dicuserartselfcheck
(
    checkdicid   integer NOT NULL PRIMARY KEY,
    checkdicnote text    NOT NULL
);

CREATE TABLE wo_dicuserexpertcheck
(
    checkdicid   integer NOT NULL PRIMARY KEY,
    checkdicnote text    NOT NULL
);

CREATE TABLE wo_fileuploadrec
(
    fid           serial                 NOT NULL PRIMARY KEY,
    orderid       integer                NOT NULL,
    equipmentid   integer                NOT NULL,
    ftype         character varying(510),
    fsavename     character varying(510) NOT NULL,
    uri           text                   NOT NULL,
    foriginalname character varying(510) NOT NULL,
    fsize         integer                NOT NULL,
    userid        integer                NOT NULL,
    uploadtime    timestamp NOT NULL
);

CREATE TABLE wo_installclerk
(
    clerkid   serial                 NOT NULL PRIMARY KEY,
    companyid integer                NOT NULL,
    clerkname character varying(510) NOT NULL
);

CREATE TABLE wo_installcompany
(
    companyid   serial                 NOT NULL PRIMARY KEY,
    companyname character varying(510) NOT NULL
);

CREATE TABLE wo_sysconfig
(
    configid    integer,
    configname  character varying(510),
    valueint    integer,
    valuestring character varying(510)
);

CREATE TABLE wo_testorder
(
    orderid            serial                           NOT NULL PRIMARY KEY,
    orderstate         integer                DEFAULT 1 NOT NULL,
    needupload         integer                DEFAULT 0 NOT NULL,
    myorderid          character varying(510)           NOT NULL,
    ordertype          integer                          NOT NULL,
    stationid          integer                          NOT NULL,
    latitude           numeric(22, 17)                  NOT NULL,
    longitude          numeric(22, 17)                  NOT NULL,
    equipitems         text,
    installcompanyid   integer,
    installclerkid     integer,
    installcompany     character varying(510),
    installclerk       character varying(510),
    applyuserid        integer                          NOT NULL,
    applyusername      character varying(510)           NOT NULL,
    applyuserfsuvendor character varying(510) DEFAULT ''::CHARACTER VARYING NOT NULL,
    applytime          timestamp NOT NULL,
    statechangetime    timestamp NOT NULL,
    statesetuserid     integer                          NOT NULL,
    statesetusername   character varying(510)           NOT NULL,
    submittime         timestamp,
    expertuserid       integer                DEFAULT 0,
    expertdecision     character varying(510) DEFAULT ''::CHARACTER VARYING,
    expertnote         character varying(510) DEFAULT ''::CHARACTER VARYING,
    expertisapprove    integer                DEFAULT 0,
    finaluserid        integer                DEFAULT 0,
    finalgeneralreuslt character varying(510) DEFAULT ''::CHARACTER VARYING,
    finaldecision      character varying(510) DEFAULT ''::CHARACTER VARYING,
    finalnote          character varying(510) DEFAULT ''::CHARACTER VARYING,
    finalisapprove     integer                DEFAULT 0,
    approvetime        timestamp,
    lockuserid         integer,
    locktime           timestamp
);
CREATE INDEX idx_24444_idx_wo_testorder ON wo_testorder USING BTREE (orderid, orderstate);

CREATE TABLE wo_testorderartselfchecklist
(
    ordercheckid serial            NOT NULL PRIMARY KEY,
    orderid      integer           NOT NULL,
    checkdicid   integer           NOT NULL,
    checkdicnote text              NOT NULL,
    ispass       integer DEFAULT 0 NOT NULL
);
CREATE INDEX idx_24463_idx_wo_testorderartselfchecklist ON wo_testorderartselfchecklist USING BTREE (orderid);

CREATE TABLE wo_testorderequipitem
(
    orderequipid      serial NOT NULL PRIMARY KEY,
    orderid           integer,
    equipmentid       integer,
    baseequipmentid   integer,
    baseequipmentname character varying(256),
    uriprotocol       text,
    uriimage          text,
    savetime          timestamp
);
CREATE UNIQUE INDEX idx_24471_idx_wo_testorderequipitemon ON wo_testorderequipitem USING BTREE (orderid, equipmentid);

CREATE TABLE wo_testorderequipitemchecklist
(
    ordercheckid          serial                 NOT NULL PRIMARY KEY,
    orderid               integer                NOT NULL,
    checktypeid           smallint,
    checktype             character varying(20),
    hasconfig             integer,
    ispass                integer DEFAULT 0      NOT NULL,
    passnote              character varying(510),
    passfailreason        character varying(510),
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256),
    equipmentcategoryname character varying(256),
    baseequipmentid       integer                NOT NULL,
    baseequipmentname     character varying(256),
    basetypeid            numeric(12, 0)         NOT NULL,
    basetypename          character varying(256) NOT NULL,
    unit                  character varying(510),
    limitdown             double precision,
    limitup               double precision,
    note                  character varying(510),
    checkid               integer,
    equipmentlogicclass   character varying(256),
    logicclass            character varying(256),
    standardname          character varying(510),
    signaltype            character varying(510),
    samplervalue          character varying(256),
    samplertime           timestamp
);
CREATE INDEX idx_24478_idx_wo_testorderequipitemchecklist ON wo_testorderequipitemchecklist USING BTREE (orderid, checktypeid, ispass);

CREATE TABLE wo_testorderequipitemchecklisthis
(
    ordercheckid          integer                NOT NULL PRIMARY KEY,
    orderid               integer                NOT NULL,
    checktypeid           smallint               NOT NULL,
    checktype             character varying(20),
    hasconfig             integer,
    ispass                integer DEFAULT 0      NOT NULL,
    passnote              character varying(510),
    passfailreason        character varying(510),
    equipmentid           integer                NOT NULL,
    equipmentname         character varying(256),
    equipmentcategoryname character varying(256),
    baseequipmentid       integer                NOT NULL,
    baseequipmentname     character varying(256),
    basetypeid            numeric(12, 0)         NOT NULL,
    basetypename          character varying(256) NOT NULL,
    unit                  character varying(510),
    limitdown             double precision,
    limitup               double precision,
    note                  character varying(510),
    checkid               integer,
    equipmentlogicclass   character varying(256),
    logicclass            character varying(256),
    standardname          character varying(510),
    signaltype            character varying(510),
    samplervalue          character varying(256),
    samplertime           timestamp
);
CREATE INDEX idx_24485_idx_wo_testorderequipitemchecklisthis ON wo_testorderequipitemchecklisthis USING BTREE (orderid, checktypeid, ispass);

CREATE TABLE wo_testorderequipitemchecklistsignal
(
    ordercheckid integer,
    orderid      integer,
    equipmentid  integer NOT NULL,
    signalid     integer NOT NULL,
    limitdown    double precision,
    limitup      double precision,
    floatvalue   double precision,
    samplertime  timestamp,
    basetypeid   numeric(12, 0),
    ispass       integer
);
CREATE INDEX idx_24491_idx_wo_testorderequipitemchecklistsignal ON wo_testorderequipitemchecklistsignal USING BTREE (orderid);

CREATE TABLE wo_testorderexpertchecklist
(
    ordercheckid serial                           NOT NULL PRIMARY KEY,
    orderid      integer                          NOT NULL,
    checkdicid   integer                          NOT NULL,
    checkdicnote character varying(510)           NOT NULL,
    ispass       integer                DEFAULT 1 NOT NULL,
    passnote     character varying(510) DEFAULT ''::CHARACTER VARYING
);
CREATE INDEX idx_24495_idx_wo_testorderexpertchecklist ON wo_testorderexpertchecklist USING BTREE (orderid);

CREATE TABLE wo_testorderflow
(
    orderflowid      serial                 NOT NULL PRIMARY KEY,
    orderid          integer                NOT NULL,
    statesetusername character varying(510) NOT NULL,
    oldorderstate    integer                NOT NULL,
    isapprove        integer                NOT NULL,
    savetime         timestamp NOT NULL,
    neworderstate    integer                NOT NULL,
    statesetuserid   integer                NOT NULL,
    decision         character varying(510),
    note             character varying(510),
    flowtext         character varying(510)
);
CREATE INDEX idx_24504_idx_wo_testorderflow ON wo_testorderflow USING BTREE (orderid);