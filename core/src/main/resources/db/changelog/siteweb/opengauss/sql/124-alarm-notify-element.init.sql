INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (1,'短信发送','发送方式',1,1,null,null,0,1,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (2,'邮件发送','发送方式',1,1,null,null,1,2,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (3,'告警箱','发送方式',1,1,null,null,1,3,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (4,'电话语音(短信)','发送方式',1,1,null,null,1,4,null);
-- INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
-- VALUES (8,'短信发送(东北大学)','发送方式',1,1,null,null,0,8,null);
-- INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
-- VALUES (9,'邮件发送(东北大学)','发送方式',1,1,null,null,0,9,null);
-- INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
-- VALUES (10,'微信发送(东北大学)','发送方式',1,1,null,null,0,10,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (11,'失败重发','告警升级',1,1,null,null,1,100,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (12,'超时未确认','告警升级',1,1,null,null,1,101,null);
INSERT INTO alarmnotifyelement (ElementId, ElementName, ElementType, InputNodesCount, OutputNodesCount, Icon, Expression, Visible, SortIndex, Description)
VALUES(13, '企业微信应用通知', '发送方式', 1, 1, NULL, NULL, 1, 5, NULL);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (14,'告警灯','发送方式',1,1,null,null,1,6,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (15,'APP推送','发送方式',1,1,null,null,1,7,null);
INSERT INTO alarmnotifyelement (ElementId,ElementName,ElementType,InputNodesCount,OutputNodesCount,Icon,Expression,Visible,SortIndex,Description)
VALUES (17,'飞书通知','发送方式',1,1,null,null,1,8,null);

INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(1, '园区');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(2, '大楼');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(3, '楼层');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(4, '房间');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(5, 'MDC');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(6, '设备');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(7, '告警');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(8, '设备基类');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(9, '告警基类');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(10, '告警状态');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(11, '告警等级');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(12, '位置');
INSERT INTO alarmnotifyfiltercondition (FilterConditionId,FilterConditionName) VALUES(13, '工程状态');