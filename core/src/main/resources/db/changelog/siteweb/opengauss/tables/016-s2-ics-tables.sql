CREATE TABLE tbl_icsarchivetask
(
    taskid        integer,
    taskname      character varying(256),
    procedurename character varying(256),
    filename      character varying(256)
);
ALTER TABLE ONLY tbl_icsarchivetask ADD CONSTRAINT tbl_icsarchivetask_taskid PRIMARY KEY (taskid);

CREATE TABLE tbl_icscontractinstall
(
    contractno        character varying(256),
    projectname       character varying(256),
    primarydate       timestamp,
    enddate           timestamp,
    qualitystartpoint character varying(256),
    qualityperiod     integer,
    qualityterms      character varying(256),
    stationcount      integer,
    fsucount          integer,
    equipmentcount    integer
);
ALTER TABLE ONLY tbl_icscontractinstall ADD CONSTRAINT tbl_icscontractinstall_contractno PRIMARY KEY (contractno);

CREATE TABLE tbl_icscontractmaintenance
(
    id               serial NOT NULL PRIMARY KEY,
    contractno       character varying(256),
    projectname      character varying(256),
    startdate        timestamp,
    enddate          timestamp,
    maintenanceterms character varying(256)
);

CREATE TABLE tbl_icsequipmenttype
(
    equipmenttypeid   integer,
    equipmenttypename character varying(256),
    iscaninputid      integer
);
ALTER TABLE ONLY tbl_icsequipmenttype ADD CONSTRAINT tbl_icsequipmenttype_equipmenttypeid PRIMARY KEY (equipmenttypeid);

CREATE TABLE tbl_icsfsudatainfo
(
    collecttime     timestamp NOT NULL,
    sampletime      timestamp,
    sn              character varying(256),
    mac             character varying(256),
    ip              character varying(256),
    fsutype         character varying(256),
    sitename        character varying(256),
    siteversion     character varying(256),
    sitecompiletime timestamp,
    hw              character varying(256),
    filesystem      character varying(256),
    linux           character varying(256),
    cpuusage        double precision,
    memtotal        double precision,
    memfree         double precision,
    memusage        double precision,
    uptime          double precision,
    filenr          character varying(256),
    mufilename      character varying(256),
    mumodifytime    timestamp,
    mufilesize      integer,
    backupmd5       character varying(256)
);
ALTER TABLE ONLY tbl_icsfsudatainfo ADD CONSTRAINT tbl_icsfsudatainfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsudatanewinfo
(
    collecttime   timestamp NOT NULL,
    sitename      character varying(256),
    fsutype       character varying(256),
    hw            character varying(256),
    sn            character varying(256),
    mac           character varying(256),
    ip            character varying(256),
    memtotal      double precision,
    flashsize     character varying(120),
    linux         character varying(256),
    siteversion   character varying(256),
    cpuusage      double precision,
    memusage      double precision,
    flashusedrate character varying(120)
);
ALTER TABLE ONLY tbl_icsfsudatanewinfo ADD CONSTRAINT tbl_icsfsudatanewinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsuflashdf
(
    collecttime    timestamp NOT NULL,
    sn             character varying(256),
    mac            character varying(256),
    flashname      character varying(256),
    filesystem     character varying(256),
    flashsize      character varying(120),
    flashused      character varying(120),
    flashavailable character varying(120),
    flashusedrate  character varying(120)
);
ALTER TABLE ONLY tbl_icsfsuflashdf ADD CONSTRAINT tbl_icsfsuflashdf_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsuftpdownloadinfo
(
    downloadtime timestamp,
    serverip     character varying(256),
    fsuip        character varying(256),
    downloadpath character varying(256),
    filename     character varying(256),
    filesize     character varying(256)
);
ALTER TABLE ONLY tbl_icsfsuftpdownloadinfo ADD CONSTRAINT tbl_icsfsuftpdownloadinfo_serverip_fsuip_downloadpath PRIMARY KEY (serverip,fsuip,downloadpath);

CREATE TABLE tbl_icsfsumuequip
(
    collecttime  timestamp NOT NULL,
    sn           character varying(256),
    mac          character varying(256),
    equipname    character varying(256),
    equipid      integer,
    stdequipid   integer,
    stdequipname character varying(256),
    equipaddr    character varying(256),
    equipphoneno character varying(256),
    dllpath      character varying(256),
    portno       integer,
    porttype     integer,
    setting      character varying(256)
);
ALTER TABLE ONLY tbl_icsfsumuequip ADD CONSTRAINT tbl_icsfsumuequip_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsumumodel
(
    collecttime  timestamp NOT NULL,
    sn           character varying(256),
    mac          character varying(256),
    sofilename   character varying(256),
    soversion    character varying(256),
    isbinterface character varying(40)
);
ALTER TABLE ONLY tbl_icsfsumumodel ADD CONSTRAINT tbl_icsfsumumodel_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsupsinfo
(
    collecttime timestamp NOT NULL,
    sn          character varying(256),
    mac         character varying(256),
    psname      character varying(256),
    psversion   character varying(256),
    pscount     integer
);
ALTER TABLE ONLY tbl_icsfsupsinfo ADD CONSTRAINT tbl_icsfsupsinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsusoinfo
(
    collecttime timestamp NOT NULL,
    ip          character varying(256),
    sn          character varying(256),
    mac         character varying(256),
    filename    character varying(256),
    soversion   character varying(256),
    socode      character varying(256),
    isused      character varying(40),
    isloaded    character varying(40),
    gccinfo     character varying(256),
    md5         character varying(256)
);
ALTER TABLE ONLY tbl_icsfsusoinfo ADD CONSTRAINT tbl_icsfsusoinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsfsuudpinfo
(
    collecttime timestamp NOT NULL,
    sn          character varying(256),
    mac         character varying(256),
    fsuhost     character varying(256),
    ip          character varying(256),
    netmask     character varying(40),
    gateway     character varying(40),
    sitename    character varying(256),
    version     character varying(256),
    serverip    character varying(256)
);
ALTER TABLE ONLY tbl_icsfsuudpinfo ADD CONSTRAINT tbl_icsfsuudpinfo_sn_mac_collecttime PRIMARY KEY (sn,mac,collecttime);

CREATE TABLE tbl_icsplatformqrcode
(
    id              integer NOT NULL PRIMARY KEY,
    equipmenttypeid integer,
    serialnumber    character varying(256),
    equipmentname   character varying(256),
    createdate      timestamp,
    operatorname    character varying(256)
);

CREATE TABLE tbl_icsscriptdatainfo
(
    collecttime   timestamp NOT NULL,
    serverip      character varying(256),
    module        character varying(510) NOT NULL,
    scriptversion character varying(60),
    compiledate   timestamp,
    createdate    timestamp,
    feature       character varying(510) NOT NULL
);
ALTER TABLE ONLY tbl_icsscriptdatainfo ADD CONSTRAINT tbl_icsscriptdatainfo_serverip_collecttime PRIMARY KEY (serverip,collecttime);

CREATE TABLE tbl_icsserverinfo
(
    collecttime     timestamp NOT NULL,
    serverip        character varying(256),
    cpuusedrate     double precision,
    memoryusedrate  double precision,
    cdriveusedrate  double precision,
    dbserverip      character varying(256),
    dbinstallpath   character varying(256),
    dbdriveusedrate double precision
);
ALTER TABLE ONLY tbl_icsserverinfo ADD CONSTRAINT tbl_icsserverinfo_serverip_collecttime PRIMARY KEY (serverip,collecttime);

CREATE TABLE tbl_icssitewebinfo
(
    collecttime    timestamp NOT NULL,
    sitewebname    character varying(256),
    sitewebversion character varying(256),
    compiledate    timestamp,
    useddate       timestamp,
    servername     character varying(256),
    serverip       character varying(256),
    operatesystem  character varying(256),
    cpuconfig      character varying(510),
    memorysize     character varying(256),
    diskdrive      character varying(256),
    installpath    character varying(256),
    diskusedrate   double precision
);
ALTER TABLE ONLY tbl_icssitewebinfo ADD CONSTRAINT tbl_icssitewebinfo_sitewebname_serverip_collecttime PRIMARY KEY (sitewebname,serverip,collecttime);