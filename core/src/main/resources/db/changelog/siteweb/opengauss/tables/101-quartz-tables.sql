DROP TABLE IF EXISTS qrtz_fired_triggers;
DROP TABLE IF EXISTS qrtz_paused_trigger_grps;
DROP TABLE IF EXISTS qrtz_scheduler_state;
DROP TABLE IF EXISTS qrtz_locks;
DROP TABLE IF EXISTS qrtz_simple_triggers;
DROP TABLE IF EXISTS qrtz_simprop_triggers;
DROP TABLE IF EXISTS qrtz_cron_triggers;
DROP TABLE IF EXISTS qrtz_blob_triggers;
DROP TABLE IF EXISTS qrtz_triggers;
DROP TABLE IF EXISTS qrtz_job_details;
DROP TABLE IF EXISTS qrtz_calendars;

CREATE TABLE qrtz_job_details
(
    sched_name        character varying(240) NOT NULL,
    job_name          character varying(400) NOT NULL,
    job_group         character varying(400) NOT NULL,
    description       character varying(500),
    job_class_name    character varying(500) NOT NULL,
    is_durable        bool   NOT NULL,
    is_nonconcurrent  bool   NOT NULL,
    is_update_data    bool   NOT NULL,
    requests_recovery bool   NOT NULL,
    job_data bytea
);
ALTER TABLE ONLY qrtz_job_details ADD CONSTRAINT idx_22380_primary PRIMARY KEY (sched_name, job_name, job_group);
CREATE INDEX idx_22380_idx_qrtz_j_grp ON qrtz_job_details USING BTREE (sched_name, job_group);
CREATE INDEX idx_22380_idx_qrtz_j_req_recovery ON qrtz_job_details USING BTREE (sched_name, requests_recovery);

CREATE TABLE qrtz_triggers
(
    sched_name     character varying(240) NOT NULL,
    trigger_name   character varying(400) NOT NULL,
    trigger_group  character varying(400) NOT NULL,
    job_name       character varying(400) NOT NULL,
    job_group      character varying(400) NOT NULL,
    description    character varying(500),
    next_fire_time bigint,
    prev_fire_time bigint,
    priority       integer,
    trigger_state  character varying(32)  NOT NULL,
    trigger_type   character varying(16)   NOT NULL,
    start_time     bigint                 NOT NULL,
    end_time       bigint,
    calendar_name  character varying(400),
    misfire_instr  smallint,
    job_data bytea
);
ALTER TABLE ONLY qrtz_triggers ADD CONSTRAINT idx_22404_primary PRIMARY KEY (sched_name, trigger_name, trigger_group);
CREATE INDEX idx_22404_idx_qrtz_t_c ON qrtz_triggers USING BTREE (sched_name, calendar_name);
CREATE INDEX idx_22404_idx_qrtz_t_g ON qrtz_triggers USING BTREE (sched_name, trigger_group);
CREATE INDEX idx_22404_idx_qrtz_t_j ON qrtz_triggers USING BTREE (sched_name, job_name, job_group);
CREATE INDEX idx_22404_idx_qrtz_t_jg ON qrtz_triggers USING BTREE (sched_name, job_group);
CREATE INDEX idx_22404_idx_qrtz_t_n_g_state ON qrtz_triggers USING BTREE (sched_name, trigger_group, trigger_state);
CREATE INDEX idx_22404_idx_qrtz_t_n_state ON qrtz_triggers USING BTREE (sched_name, trigger_name, trigger_group, trigger_state);
CREATE INDEX idx_22404_idx_qrtz_t_next_fire_time ON qrtz_triggers USING BTREE (sched_name, next_fire_time);
CREATE INDEX idx_22404_idx_qrtz_t_nft_misfire ON qrtz_triggers USING BTREE (sched_name, misfire_instr, next_fire_time);
CREATE INDEX idx_22404_idx_qrtz_t_nft_st ON qrtz_triggers USING BTREE (sched_name, trigger_state, next_fire_time);
CREATE INDEX idx_22404_idx_qrtz_t_nft_st_misfire ON qrtz_triggers USING BTREE (sched_name, misfire_instr, next_fire_time, trigger_state);
CREATE INDEX idx_22404_idx_qrtz_t_nft_st_misfire_grp ON qrtz_triggers USING BTREE (sched_name, misfire_instr, next_fire_time, trigger_group, trigger_state);
CREATE INDEX idx_22404_idx_qrtz_t_state ON qrtz_triggers USING BTREE (sched_name, trigger_state);
ALTER TABLE ONLY qrtz_triggers ADD CONSTRAINT qrtz_triggers_ibfk_1 FOREIGN KEY (sched_name, job_name, job_group) REFERENCES qrtz_job_details(sched_name, job_name, job_group);


CREATE TABLE qrtz_simple_triggers
(
    sched_name      character varying(240) NOT NULL,
    trigger_name    character varying(400) NOT NULL,
    trigger_group   character varying(400) NOT NULL,
    repeat_count    bigint                 NOT NULL,
    repeat_interval bigint                 NOT NULL,
    times_triggered bigint                 NOT NULL
);
ALTER TABLE ONLY qrtz_simple_triggers ADD CONSTRAINT idx_22394_primary PRIMARY KEY (sched_name, trigger_name, trigger_group);
ALTER TABLE ONLY qrtz_simple_triggers ADD CONSTRAINT qrtz_simple_triggers_ibfk_1 FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES qrtz_triggers(sched_name, trigger_name, trigger_group);

CREATE TABLE qrtz_cron_triggers
(
    sched_name      character varying(240) NOT NULL,
    trigger_name    character varying(400) NOT NULL,
    trigger_group   character varying(400) NOT NULL,
    cron_expression character varying(240) NOT NULL,
    time_zone_id    character varying(160)
);
ALTER TABLE ONLY qrtz_cron_triggers ADD CONSTRAINT idx_22370_primary PRIMARY KEY (sched_name, trigger_name, trigger_group);
ALTER TABLE ONLY qrtz_cron_triggers ADD CONSTRAINT qrtz_cron_triggers_ibfk_1 FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES qrtz_triggers(sched_name, trigger_name, trigger_group);

CREATE TABLE qrtz_simprop_triggers
(
    sched_name    character varying(240) NOT NULL,
    trigger_name  character varying(400) NOT NULL,
    trigger_group character varying(400) NOT NULL,
    str_prop_1    character varying(1024),
    str_prop_2    character varying(1024),
    str_prop_3    character varying(1024),
    int_prop_1    integer,
    int_prop_2    integer,
    long_prop_1   bigint,
    long_prop_2   bigint,
    dec_prop_1    numeric(13, 4),
    dec_prop_2    numeric(13, 4),
    bool_prop_1   bool,
    bool_prop_2   bool
);
ALTER TABLE ONLY qrtz_simprop_triggers ADD CONSTRAINT idx_22399_primary PRIMARY KEY (sched_name, trigger_name, trigger_group);
ALTER TABLE ONLY qrtz_simprop_triggers ADD CONSTRAINT qrtz_simprop_triggers_ibfk_1 FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES qrtz_triggers(sched_name, trigger_name, trigger_group);

CREATE TABLE qrtz_blob_triggers
(
    sched_name    character varying(240) NOT NULL,
    trigger_name  character varying(400) NOT NULL,
    trigger_group character varying(400) NOT NULL,
    blob_data bytea
);
ALTER TABLE ONLY qrtz_blob_triggers ADD CONSTRAINT idx_22360_primary PRIMARY KEY (sched_name, trigger_name, trigger_group);
CREATE INDEX idx_22360_sched_name ON qrtz_blob_triggers USING BTREE (sched_name, trigger_name, trigger_group);
ALTER TABLE ONLY qrtz_blob_triggers ADD CONSTRAINT qrtz_blob_triggers_ibfk_1 FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES qrtz_triggers(sched_name, trigger_name, trigger_group);

CREATE TABLE qrtz_calendars
(
    sched_name    character varying(240) NOT NULL,
    calendar_name character varying(400) NOT NULL,
    calendar bytea NOT NULL
);
ALTER TABLE ONLY qrtz_calendars ADD CONSTRAINT idx_22365_primary PRIMARY KEY (sched_name, calendar_name);

CREATE TABLE qrtz_paused_trigger_grps
(
    sched_name    character varying(240) NOT NULL,
    trigger_group character varying(400) NOT NULL
);
ALTER TABLE ONLY qrtz_paused_trigger_grps ADD CONSTRAINT idx_22388_primary PRIMARY KEY (sched_name, trigger_group);

CREATE TABLE qrtz_fired_triggers
(
    sched_name        character varying(240) NOT NULL,
    entry_id          character varying(190)  NOT NULL,
    trigger_name      character varying(400) NOT NULL,
    trigger_group     character varying(400) NOT NULL,
    instance_name     character varying(400) NOT NULL,
    fired_time        bigint                 NOT NULL,
    sched_time        bigint                 NOT NULL,
    priority          integer                NOT NULL,
    state             character varying(32)  NOT NULL,
    job_name          character varying(400),
    job_group         character varying(400),
    is_nonconcurrent  bool,
    requests_recovery bool
);
ALTER TABLE ONLY qrtz_fired_triggers ADD CONSTRAINT idx_22375_primary PRIMARY KEY (sched_name, entry_id);
CREATE INDEX idx_22375_idx_qrtz_ft_inst_job_req_rcvry ON qrtz_fired_triggers USING BTREE (sched_name, instance_name, requests_recovery);
CREATE INDEX idx_22375_idx_qrtz_ft_j_g ON qrtz_fired_triggers USING BTREE (sched_name, job_name, job_group);
CREATE INDEX idx_22375_idx_qrtz_ft_jg ON qrtz_fired_triggers USING BTREE (sched_name, job_group);
CREATE INDEX idx_22375_idx_qrtz_ft_t_g ON qrtz_fired_triggers USING BTREE (sched_name, trigger_name, trigger_group);
CREATE INDEX idx_22375_idx_qrtz_ft_tg ON qrtz_fired_triggers USING BTREE (sched_name, trigger_group);
CREATE INDEX idx_22375_idx_qrtz_ft_trig_inst_name ON qrtz_fired_triggers USING BTREE (sched_name, instance_name);

CREATE TABLE qrtz_scheduler_state
(
    sched_name        character varying(240) NOT NULL,
    instance_name     character varying(400) NOT NULL,
    last_checkin_time bigint                 NOT NULL,
    checkin_interval  bigint                 NOT NULL
);
ALTER TABLE ONLY qrtz_scheduler_state ADD CONSTRAINT idx_22391_primary PRIMARY KEY (sched_name, instance_name);

CREATE TABLE qrtz_locks
(
    sched_name character varying(240) NOT NULL,
    lock_name  character varying(80)  NOT NULL
);
ALTER TABLE ONLY qrtz_locks ADD CONSTRAINT idx_22385_primary PRIMARY KEY (sched_name, lock_name);