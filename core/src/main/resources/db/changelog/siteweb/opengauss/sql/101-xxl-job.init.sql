-- 初始化语句
INSERT INTO xxl_job_group(id, app_name, title, address_type, address_list, update_time) VALUES (1, 'siteweb-job-executor', 'JobServer执行器', 0, NULL, '2024-04-26 09:34:56' );

INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, 'WorkStation自诊断', '2024-04-26 09:34:56', '2024-04-26 09:34:56', 'siteweb', '', 'CRON', '0/20 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'workStationDiagnoses<PERSON>ob<PERSON><PERSON><PERSON>', '240', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-04-26 09:34:56', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, 'MU自诊断', '2024-05-09 10:41:31', '2024-05-09 10:41:31', 'siteweb', '', 'CRON', '0/30 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'muDiagnosesJobHandler', '300', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-09 10:41:31', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理日志数据', '2024-06-04 10:11:53', '2024-06-04 13:42:17', 'liaoximing', '', 'CRON', '0 30 1 * * ?', 'DO_NOTHING', 'FIRST', 'clearLogJobHandler', '180;200000;7;1', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-09-20 10:21:31', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '记录机架容量', '2024-06-04 15:57:21', '2024-06-04 16:28:00', 'liaoximing', '', 'CRON', '0 0 0 1 * ?', 'DO_NOTHING', 'FIRST', 'rackCapacityRecordJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-06-04 15:57:21', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '每小时锁定一次不活跃用户', '2024-06-06 10:14:03', '2024-06-06 10:14:24', 'liaoximing', '', 'CRON', '0 0 * * * ?', 'DO_NOTHING', 'FIRST', 'lockInactiveUsersJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-06-06 10:14:03', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '配置同步(检查)', '2024-05-24 11:30:31', '2024-05-24 11:30:31', 'siteweb', '', 'CRON', '0 0 20 * * ?', 'DO_NOTHING', 'FAILOVER', 'monitorUnitSyncCheckJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-24 11:30:31', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '配置同步(下发)', '2024-05-25 13:46:12', '2024-05-25 13:46:12', 'siteweb', '', 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FAILOVER', 'monitorUnitSyncExecJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-25 13:46:12', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理过期控制', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FAILOVER', 'clearTimeoutControlJobHandler', '180,86400', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理没有配置的活动事件', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'CRON', '0 0 2 * * ?', 'DO_NOTHING', 'FAILOVER', 'clearActiveEventJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '定时向workstation表报到并生成工作站告警', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'CRON', '0/30 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'bSDiagnosesJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
-- INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '分发门禁控制命令', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '3', 'DO_NOTHING', 'FAILOVER', 'distributeControlOfDoorJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '自动确认告警', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '300', 'DO_NOTHING', 'FAILOVER', 'autoConfirmActiveEventJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理告警变化', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '1800', 'DO_NOTHING', 'FAILOVER', 'clearAlarmChangeJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理屏蔽事件', '2024-05-27 15:32:34', '2024-05-27 15:32:34', 'siteweb', '', 'FIX_RATE', '300', 'DO_NOTHING', 'FAILOVER', 'clearEventMaskJobHandler', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-27 15:32:34', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史告警记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearHistoryEventJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史告警屏蔽记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearHistoryEventMaskJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史控制记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearHistoryControlJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史人员登录记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearLoginInfoJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史刷卡记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearSwapCardRecordJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史操作记录', '2024-05-28 10:38:22', '2024-05-28 10:38:22', 'siteweb', '', 'CRON', '0 0 2 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearOperationRecordJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-28 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '每分钟更新设备工程态状态', '2024-07-10 14:30:05', '2024-07-10 14:30:30', 'liaoximing', '', 'CRON', '0 * * * * ?', 'DO_NOTHING', 'FIRST', 'equipmentMaintainJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-07-10 14:30:05', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '同步FSU运行信息', '2024-11-26 14:30:05', '2024-11-26 14:30:30', 'liaoximing', '', 'CRON', '0 0 6 * * ?', 'DO_NOTHING', 'FIRST', 'fsuDataInfoSync', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-11-26 14:30:05', '', 0);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status)VALUES (1, '记录机架信号开通率和功率', '2025-02-28 15:57:21', '2025-02-28 16:28:00', 'shenhaijun', '', 'CRON', '0 0 * * * ?', 'DO_NOTHING', 'FIRST', 'computerRackSignalRecordJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-02-28 15:57:21', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '清理历史预警及预警变化表记录', '2025-03-19 16:38:22', '2025-03-19 16:38:22', 'siteweb', '', 'CRON', '0 0 4 1/7 * ?', 'DO_NOTHING', 'FAILOVER', 'clearPreAlarmHistoryJobHandler', '10;1', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-19 16:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '密码到期短信提醒', '2025-03-21 10:38:22', '2025-03-21 10:38:22', 'liaoximing', '', 'CRON', '0 0 9 * * ?', 'DO_NOTHING', 'FAILOVER', 'passwordExpirationSmsReminderJob', '动环监控平台提醒您：请尽快修改密码，密码到期帐户将被锁定', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-03-21 10:38:22', '', 1);
INSERT INTO xxl_job_info(job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status) VALUES (1, '每分钟更新局站工程态状态', '2025-06-30 10:38:22', '2025-06-30 10:38:22', 'liaoximing', '', 'CRON', '0 * * * * ?', 'DO_NOTHING', 'FAILOVER', 'stationMaintainJob', '', 'DISCARD_LATER', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-06-30 10:38:22', '', 1);


INSERT INTO xxl_job_user(id, username, password, role, permission) VALUES (1, 'admin', 'f6fdffe48c908deb0f4c3bd36c032e72', 1, NULL); -- 默认密码：adminadmin

INSERT INTO xxl_job_lock ( lock_name) VALUES ( 'schedule_lock');
