CREATE TABLE xxl_job_info
(
    id                        serial                          NOT NULL PRIMARY KEY,
    job_group                 integer                         NOT NULL,
    job_desc                  character varying(510)          NOT NULL,
    add_time                  timestamp,
    update_time               timestamp,
    author                    character varying(128),
    alarm_email               character varying(510),
    schedule_type             character varying(100) DEFAULT 'NONE'::CHARACTER VARYING NOT NULL,
    schedule_conf             character varying(256),
    misfire_strategy          character varying(100) DEFAULT 'DO_NOTHING'::CHARACTER VARYING NOT NULL,
    executor_route_strategy   character varying(100),
    executor_handler          character varying(510),
    executor_param            character varying(1024),
    executor_block_strategy   character varying(100),
    executor_timeout          integer               DEFAULT 0 NOT NULL,
    executor_fail_retry_count integer               DEFAULT 0 NOT NULL,
    glue_type                 character varying(100)           NOT NULL,
    glue_source               text,
    glue_remark               character varying(256),
    glue_updatetime           timestamp,
    child_jobid               character varying(510),
    trigger_status            smallint              DEFAULT '0'::SMALLINT NOT NULL,
    trigger_last_time         bigint                DEFAULT '0'::BIGINT NOT NULL,
    trigger_next_time         bigint                DEFAULT '0'::BIGINT NOT NULL
);
COMMENT ON COLUMN xxl_job_info.job_group IS '执行器主键ID';
COMMENT ON COLUMN xxl_job_info.author IS '作者';
COMMENT ON COLUMN xxl_job_info.alarm_email IS '报警邮件';
COMMENT ON COLUMN xxl_job_info.schedule_type IS '调度类型';
COMMENT ON COLUMN xxl_job_info.schedule_conf IS '调度配置，值含义取决于调度类型';
COMMENT ON COLUMN xxl_job_info.misfire_strategy IS '调度过期策略';
COMMENT ON COLUMN xxl_job_info.executor_route_strategy IS '执行器路由策略';
COMMENT ON COLUMN xxl_job_info.executor_handler IS '执行器任务handler';
COMMENT ON COLUMN xxl_job_info.executor_param IS '执行器任务参数';
COMMENT ON COLUMN xxl_job_info.executor_block_strategy IS '阻塞处理策略';
COMMENT ON COLUMN xxl_job_info.executor_timeout IS '任务执行超时时间，单位秒';
COMMENT ON COLUMN xxl_job_info.executor_fail_retry_count IS '失败重试次数';
COMMENT ON COLUMN xxl_job_info.glue_type IS 'GLUE类型';
COMMENT ON COLUMN xxl_job_info.glue_source IS 'GLUE源代码';
COMMENT ON COLUMN xxl_job_info.glue_remark IS 'GLUE备注';
COMMENT ON COLUMN xxl_job_info.glue_updatetime IS 'GLUE更新时间';
COMMENT ON COLUMN xxl_job_info.child_jobid IS '子任务ID，多个逗号分隔';
COMMENT ON COLUMN xxl_job_info.trigger_status IS '调度状态：0-停止，1-运行';
COMMENT ON COLUMN xxl_job_info.trigger_last_time IS '上次调度时间';
COMMENT ON COLUMN xxl_job_info.trigger_next_time IS '下次调度时间';

CREATE TABLE xxl_job_log
(
    id bigserial NOT NULL PRIMARY KEY,
    job_group                 integer            NOT NULL,
    job_id                    integer            NOT NULL,
    executor_address          character varying(510),
    executor_handler          character varying(510),
    executor_param            character varying(1024),
    executor_sharding_param   character varying(40),
    executor_fail_retry_count integer  DEFAULT 0 NOT NULL,
    trigger_time              timestamp,
    trigger_code              integer            NOT NULL,
    trigger_msg               text,
    handle_time               timestamp,
    handle_code               integer            NOT NULL,
    handle_msg                text,
    alarm_status              smallint DEFAULT '0'::SMALLINT NOT NULL
);
COMMENT ON COLUMN xxl_job_log.job_group IS '执行器主键ID';
COMMENT ON COLUMN xxl_job_log.job_id IS '任务，主键ID';
COMMENT ON COLUMN xxl_job_log.executor_address IS '执行器地址，本次执行的地址';
COMMENT ON COLUMN xxl_job_log.executor_handler IS '执行器任务handler';
COMMENT ON COLUMN xxl_job_log.executor_param IS '执行器任务参数';
COMMENT ON COLUMN xxl_job_log.executor_sharding_param IS '执行器任务分片参数，格式如 1/2';
COMMENT ON COLUMN xxl_job_log.executor_fail_retry_count IS '失败重试次数';
COMMENT ON COLUMN xxl_job_log.trigger_time IS '调度-时间';
COMMENT ON COLUMN xxl_job_log.trigger_code IS '调度-结果';
COMMENT ON COLUMN xxl_job_log.trigger_msg IS '调度-日志';
COMMENT ON COLUMN xxl_job_log.handle_time IS '执行-时间';
COMMENT ON COLUMN xxl_job_log.handle_code IS '执行-状态';
COMMENT ON COLUMN xxl_job_log.handle_msg IS '执行-日志';
COMMENT ON COLUMN xxl_job_log.alarm_status IS '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败';
CREATE INDEX idx_24615_i_handle_code ON xxl_job_log USING BTREE (handle_code);
CREATE INDEX idx_24615_i_trigger_time ON xxl_job_log USING BTREE (trigger_time);

CREATE TABLE xxl_job_log_report
(
    id            serial            NOT NULL PRIMARY KEY,
    trigger_day   timestamp,
    running_count integer DEFAULT 0 NOT NULL,
    suc_count     integer DEFAULT 0 NOT NULL,
    fail_count    integer DEFAULT 0 NOT NULL,
    update_time   timestamp
);
CREATE UNIQUE INDEX idx_24624_i_trigger_day ON xxl_job_log_report USING BTREE (trigger_day);

CREATE TABLE xxl_job_logglue
(
    id          serial                 NOT NULL PRIMARY KEY,
    job_id      integer                NOT NULL,
    glue_type   character varying(100),
    glue_source text,
    glue_remark character varying(256) NOT NULL,
    add_time    timestamp,
    update_time timestamp
);
COMMENT ON COLUMN xxl_job_logglue.job_id IS '任务，主键ID';
COMMENT ON COLUMN xxl_job_logglue.glue_type IS 'GLUE类型';
COMMENT ON COLUMN xxl_job_logglue.glue_source IS 'GLUE源代码';
COMMENT ON COLUMN xxl_job_logglue.glue_remark IS 'GLUE备注';

CREATE TABLE xxl_job_registry
(
    id             serial                 NOT NULL PRIMARY KEY,
    registry_group character varying(100)  NOT NULL,
    registry_key   character varying(510) NOT NULL,
    registry_value character varying(510) NOT NULL,
    update_time    timestamp
);
CREATE INDEX idx_24639_i_g_k_v ON xxl_job_registry USING BTREE (registry_group, registry_key, registry_value);

CREATE TABLE xxl_job_group
(
    id           serial                NOT NULL PRIMARY KEY,
    app_name     character varying(128) NOT NULL,
    title        character varying(128) NOT NULL,
    address_type smallint DEFAULT '0'::SMALLINT NOT NULL,
    address_list text,
    update_time  timestamp
);
COMMENT ON COLUMN xxl_job_group.app_name IS '执行器AppName';
COMMENT ON COLUMN xxl_job_group.title IS '执行器名称';
COMMENT ON COLUMN xxl_job_group.address_type IS '执行器地址类型：0=自动注册、1=手动录入';
COMMENT ON COLUMN xxl_job_group.address_list IS '执行器地址列表，多地址逗号分隔';

CREATE TABLE xxl_job_user
(
    id         serial                NOT NULL PRIMARY KEY,
    username   character varying(100) NOT NULL,
    password   character varying(100) NOT NULL,
    role       smallint              NOT NULL,
    permission character varying(510)
);
COMMENT ON COLUMN xxl_job_user.username IS '账号';
COMMENT ON COLUMN xxl_job_user.password IS '密码';
COMMENT ON COLUMN xxl_job_user.role IS '角色：0-普通用户、1-管理员';
COMMENT ON COLUMN xxl_job_user.permission IS '权限：执行器ID列表，多个逗号分割';
CREATE UNIQUE INDEX idx_24646_i_username ON xxl_job_user USING BTREE (username);

CREATE TABLE xxl_job_lock
(
    lock_name character varying(100) NOT NULL PRIMARY KEY
);
COMMENT ON COLUMN xxl_job_lock.lock_name IS '锁名称';