@echo off
setlocal enabledelayedexpansion

:: 设置代码页为UTF-8
chcp 65001 >nul

:: 定义文件路径
set "filePath=C:\company\code\siteweb6-server\core\src\main\resources\db\changelog\siteweb\postgres\sql\150-video-init.sql"
set "tempFilePath=%filePath%.tmp"

:: 逐行读取文件，删除反引号并保留空行
(for /f "tokens=1* delims=:" %%a in ('findstr /n "^" "%filePath%"') do (
    set "line=%%b"
    if defined line (
        set "line=!line:`=!"
    )
    echo(!line!
)) > "%tempFilePath%"

:: 替换原文件，确保保持UTF-8编码
move /y "%tempFilePath%" "%filePath%" >nul

echo 所有的反引号已成功移除，空行保留，且文件编码保持为UTF-8。
