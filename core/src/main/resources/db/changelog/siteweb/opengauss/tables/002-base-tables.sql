CREATE TABLE tbl_equipmentbasetype
(
    baseequipmentid    integer NOT NULL PRIMARY KEY,
    baseequipmentname  character varying(256),
    equipmenttypeid    integer NOT NULL,
    equipmentsubtypeid integer NOT NULL,
    description        text,
    extfield           json
);

CREATE TABLE baseunit
(
    baseunitid              serial                 NOT NULL PRIMARY KEY,
    baseunitname            character varying(256),
    baseunitnameen          character varying(256),
    baseunitsymbol          character varying(256),
    baseunitnamecode        character varying(256),
    baseunitsymbolname      character varying(256),
    baseunitnamedescription character varying(512)
);

CREATE TABLE tbl_baseclassdic
(
    baseclassid   integer                NOT NULL PRIMARY KEY,
    baseclassname character varying(510) NOT NULL,
    baseclassicon character varying(510)
);

CREATE TABLE tbl_basecommandcode
(
    codeid      integer                NOT NULL PRIMARY KEY,
    command     character varying(510) NOT NULL,
    description character varying(510)
);

CREATE TABLE tbl_basedicver
(
    hiver        integer NOT NULL PRIMARY KEY,
    lover        integer NOT NULL,
    remark       text,
    extendfield1 text,
    extendfield2 text
);

CREATE TABLE tbl_basedicverhistory
(
    version          character varying(510) NOT NULL,
    updatedesciption text                   NOT NULL,
    updatedate       timestamp NOT NULL,
    editor           integer                NOT NULL,
    extendfield1     text,
    extendfield2     text
);
ALTER TABLE ONLY tbl_basedicverhistory ADD CONSTRAINT idx_22712_primary PRIMARY KEY (updatedate, version);

CREATE TABLE tbl_baseequipmentcategorymap
(
    baseequipmentid   integer NOT NULL,
    equipmentcategory integer NOT NULL
);
ALTER TABLE ONLY tbl_baseequipmentcategorymap ADD CONSTRAINT idx_22717_primary PRIMARY KEY (baseequipmentid, equipmentcategory);

CREATE TABLE tbl_baseequipmentmap
(
    standardtype      integer NOT NULL,
    standarddicid     integer NOT NULL,
    stationbasetype   integer NOT NULL,
    equipmentbasetype integer NOT NULL
);
ALTER TABLE ONLY tbl_baseequipmentmap ADD CONSTRAINT idx_22720_primary PRIMARY KEY (equipmentbasetype, standarddicid, standardtype, stationbasetype);

CREATE TABLE tbl_basesignaleventcode
(
    codeid      integer                NOT NULL PRIMARY KEY,
    category    character varying(510) NOT NULL,
    signal      character varying(510),
    event       character varying(510),
    description character varying(510)
);

CREATE TABLE tbl_baseunitdic
(
    baseunitid          integer                NOT NULL PRIMARY KEY,
    baseunitname        character varying(510) NOT NULL,
    baseunitsymbol      character varying(510),
    baseunitdescription character varying(510)
);

CREATE TABLE tbl_signalbasedic
(
    basetypeid          numeric(12, 0)         NOT NULL PRIMARY KEY,
    basetypename        character varying(256) NOT NULL,
    baseequipmentid     integer                NOT NULL,
    englishname         text,
    baselogiccategoryid integer,
    storeinterval       integer,
    absvaluethreshold   double precision,
    percentthreshold    double precision,
    storeinterval2      integer,
    absvaluethreshold2  double precision,
    percentthreshold2   double precision,
    extendfield1        text,
    extendfield2        text,
    extendfield3        text,
    unitid              integer,
    basestatusid        integer,
    basehysteresis      double precision,
    basefreqperiod      integer,
    basefreqcount       integer,
    baseshowprecision   character varying(60),
    basestatperiod      integer,
    cgelement           character varying(256),
    description         text,
    basenameext         character varying(256),
    issystem            integer DEFAULT 1      NOT NULL
);

CREATE TABLE tbl_signalbasemap
(
    standarddicid   integer        NOT NULL,
    standardtype    integer        NOT NULL,
    stationbasetype integer        NOT NULL,
    basetypeid      numeric(12, 0) NOT NULL,
    basecondid      integer
);
ALTER TABLE ONLY tbl_signalbasemap ADD CONSTRAINT idx_23929_primary PRIMARY KEY (basetypeid, standarddicid, standardtype, stationbasetype);

CREATE TABLE tbl_signalbaseconfirm
(
    equipmenttemplateid integer NOT NULL,
    signalid            integer NOT NULL,
    statevalue          integer,
    substate            character varying(32)
);
CREATE INDEX idx_23920_idxsignalbaseconfirmid ON tbl_signalbaseconfirm USING BTREE (equipmenttemplateid, signalid);

CREATE TABLE tbl_statusbasedic
(
    basestatusid   integer                NOT NULL,
    basestatusname character varying(256) NOT NULL,
    basecondid     integer                NOT NULL,
    operator       character varying(60)  NOT NULL,
    value          integer,
    meaning        character varying(256),
    description    text
);
ALTER TABLE ONLY tbl_statusbasedic ADD CONSTRAINT idx_24085_primary PRIMARY KEY (basecondid, basestatusid);

CREATE TABLE tbl_commandbasedic
(
    basetypeid          numeric(12, 0)         NOT NULL PRIMARY KEY,
    basetypename        character varying(256) NOT NULL,
    baseequipmentid     integer                NOT NULL,
    englishname         text,
    baselogiccategoryid integer,
    commandtype         integer                NOT NULL,
    basestatusid        integer,
    extendfield1        text,
    extendfield2        text,
    extendfield3        text,
    description         text,
    basenameext         character varying(256),
    issystem            integer DEFAULT 1      NOT NULL
);

CREATE TABLE tbl_commandbasemap
(
    standarddicid   integer        NOT NULL,
    standardtype    integer        NOT NULL,
    stationbasetype integer        NOT NULL,
    basetypeid      numeric(12, 0) NOT NULL,
    basecondid      integer
);
ALTER TABLE ONLY tbl_commandbasemap ADD CONSTRAINT idx_22788_primary PRIMARY KEY (basetypeid, standarddicid, standardtype, stationbasetype);

CREATE TABLE tbl_controlbaseconfirm
(
    equipmenttemplateid integer NOT NULL,
    controlid           integer NOT NULL,
    parametervalue      integer,
    substate            character varying(32)
);

CREATE TABLE tbl_eventbasedic
(
    basetypeid          numeric(12, 0)         NOT NULL PRIMARY KEY,
    basetypename        character varying(256) NOT NULL,
    baseequipmentid     integer                NOT NULL,
    englishname         text,
    eventseverityid     integer                NOT NULL,
    comparedvalue       double precision,
    baselogiccategoryid integer,
    startdelay          integer,
    enddelay            integer,
    extendfield1        text,
    extendfield2        text,
    extendfield3        text,
    extendfield4        text,
    extendfield5        text,
    description         text,
    basenameext         character varying(256),
    issystem            integer DEFAULT 1      NOT NULL
);

CREATE TABLE tbl_eventbaseconfirm
(
    equipmenttemplateid integer NOT NULL,
    eventid             integer NOT NULL,
    eventconditionid    integer NOT NULL,
    substate            character varying(32)
);
ALTER TABLE ONLY tbl_eventbaseconfirm ADD CONSTRAINT idx_23107_primary PRIMARY KEY (equipmenttemplateid, eventconditionid, eventid);

CREATE TABLE tbl_eventbasemap
(
    standarddicid   integer        NOT NULL,
    standardtype    integer        NOT NULL,
    stationbasetype integer        NOT NULL,
    basetypeid      numeric(12, 0) NOT NULL
);
ALTER TABLE ONLY tbl_eventbasemap ADD CONSTRAINT idx_23116_primary PRIMARY KEY (basetypeid, standarddicid, standardtype, stationbasetype);

CREATE TABLE tbl_logiccategorybasedic
(
    baseequipmentid       integer NOT NULL,
    baselogiccategorytype integer NOT NULL,
    baselogiccategoryid   integer NOT NULL PRIMARY KEY,
    baselogiccategoryname character varying(256),
    description           text
);