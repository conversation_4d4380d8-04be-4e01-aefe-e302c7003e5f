CREATE TABLE wr_dataentry
(
    entryid       integer NOT NULL PRIMARY KEY,
    entrycategory integer,
    entryname     character varying(256),
    entrytitle    character varying(256),
    entryalias    character varying(510),
    enable        integer NOT NULL,
    description   character varying(510)
);

CREATE TABLE wr_dataitem
(
    entryitemid    serial                 NOT NULL PRIMARY KEY,
    entryid        integer                NOT NULL,
    itemid         integer                NOT NULL,
    parententryid  integer,
    parentitemid   integer,
    itemvalue      character varying(510) NOT NULL,
    lastupdatedate timestamp,
    extendfield1   character varying(510) DEFAULT ''::CHARACTER VARYING,
    extendfield2   character varying(510) DEFAULT ''::CHARACTER VARYING,
    extendfield3   character varying(510) DEFAULT ''::CHARACTER VARYING
);

CREATE TABLE wr_devicemanagement
(
    wrdeviceid  serial                NOT NULL PRIMARY KEY,
    wrstationid integer,
    wrhouseid   integer,
    wrfsuid     integer,
    devicetype  character varying(20) NOT NULL,
    devicecode  character varying(40) NOT NULL,
    devicerid   character varying(256),
    devicename  character varying(256),
    userid      integer               NOT NULL,
    swusername  character varying(256),
    applytime   timestamp,
    swstationid integer,
    swhouseid   integer,
    remark      character varying(510)
);

CREATE TABLE wr_devicemanagementcucc
(
    wrdeviceid  serial                NOT NULL PRIMARY KEY,
    wrstationid integer,
    wrhouseid   integer,
    wrfsuid     integer,
    devicetype  character varying(20) NOT NULL,
    devicecode  character varying(40) NOT NULL,
    devicerid   character varying(256),
    devicename  character varying(256),
    userid      integer               NOT NULL,
    swusername  character varying(256),
    applytime   timestamp,
    swstationid integer,
    swhouseid   integer,
    remark      character varying(510)
);
CREATE INDEX idx_24533_idx_equipmentid_cuccmanage ON wr_devicemanagementcucc USING BTREE (wrfsuid, devicecode, swstationid);

CREATE TABLE wr_fsumanagement
(
    wrfsuid         serial                NOT NULL PRIMARY KEY,
    wrhouseid       integer               NOT NULL,
    fsucode         character varying(40) NOT NULL,
    fsuname         character varying(510),
    ipaddress       character varying(510),
    manufacturerid  integer,
    fsustatus       integer               NOT NULL,
    userid          integer               NOT NULL,
    swusername      character varying(256),
    username        character varying(80),
    password        character varying(80),
    ftpusername     character varying(80),
    ftppassword     character varying(80),
    applytime       timestamp,
    approvetime     timestamp,
    rejectcause     character varying(510),
    remark          character varying(510),
    swmonitorunitid integer,
    contractno      character varying(510),
    projectname     character varying(510)
);

CREATE TABLE wr_fsumanagementcucc
(
    wrfsuid         serial                NOT NULL PRIMARY KEY,
    wrhouseid       integer               NOT NULL,
    fsucode         character varying(40) NOT NULL,
    fsuname         character varying(510),
    ipaddress       character varying(510),
    manufacturerid  integer,
    fsustatus       integer               NOT NULL,
    userid          integer               NOT NULL,
    swusername      character varying(256),
    username        character varying(80),
    password        character varying(80),
    ftpusername     character varying(80),
    ftppassword     character varying(80),
    applytime       timestamp,
    approvetime     timestamp,
    rejectcause     character varying(510),
    remark          character varying(510),
    swmonitorunitid integer,
    contractno      character varying(510),
    projectname     character varying(510),
    fsurid          character varying(256)
);

CREATE TABLE wr_housemanagement
(
    wrhouseid   serial                NOT NULL PRIMARY KEY,
    wrstationid integer               NOT NULL,
    housecode   character varying(40) NOT NULL,
    housename   character varying(256),
    housestatus integer               NOT NULL,
    userid      integer               NOT NULL,
    swusername  character varying(256),
    applytime   timestamp,
    approvetime timestamp,
    rejectcause character varying(510),
    remark      character varying(510),
    swhouseid   integer
);

CREATE TABLE wr_housemanagementcucc
(
    wrhouseid   serial                NOT NULL PRIMARY KEY,
    wrstationid integer               NOT NULL,
    housecode   character varying(40) NOT NULL,
    housename   character varying(256),
    housestatus integer               NOT NULL,
    userid      integer               NOT NULL,
    swusername  character varying(256),
    applytime   timestamp,
    approvetime timestamp,
    rejectcause character varying(510),
    remark      character varying(510),
    swhouseid   integer,
    houserid    character varying(256)
);

CREATE TABLE wr_operationrecord
(
    wrstationid   integer                NOT NULL,
    wrstationname character varying(510),
    opcategory    integer                NOT NULL,
    opitemid      integer                NOT NULL,
    opitemvalue   character varying(510) NOT NULL,
    opdatetime    timestamp,
    updatestring  text,
    laststring    text,
    opuserid      integer,
    extendfield1  character varying(510),
    extendfield2  character varying(510),
    extendfield3  character varying(510)
);

CREATE TABLE wr_stationcode
(
    countyid     integer NOT NULL PRIMARY KEY,
    "minvalue"     integer NOT NULL,
    currentvalue integer NOT NULL
);

CREATE TABLE wr_stationmanagement
(
    wrstationid     serial                 NOT NULL PRIMARY KEY,
    structureid     integer                NOT NULL,
    stationcode     character varying(40)  NOT NULL,
    stationname     character varying(510) NOT NULL,
    stationcategory integer                NOT NULL,
    stationstatus   integer                NOT NULL,
    address         character varying(510),
    userid          integer                NOT NULL,
    swusername      character varying(256),
    applytime       timestamp,
    approvetime     timestamp,
    province        integer,
    city            integer,
    county          integer,
    rejectcause     character varying(510),
    remark          character varying(510),
    swstationid     integer,
    contractno      character varying(510),
    projectname     character varying(510)
);
CREATE INDEX idx_24576_idx_wr_stationmanagement_1 ON wr_stationmanagement USING BTREE (applytime, structureid, stationcategory, stationname, stationcode, stationstatus);

CREATE TABLE wr_stationmanagementcucc
(
    wrstationid     serial                 NOT NULL PRIMARY KEY,
    structureid     integer                NOT NULL,
    stationcode     character varying(40)  NOT NULL,
    stationname     character varying(510) NOT NULL,
    stationcategory integer                NOT NULL,
    stationstatus   integer                NOT NULL,
    address         character varying(510),
    userid          integer                NOT NULL,
    swusername      character varying(256),
    applytime       timestamp,
    approvetime     timestamp,
    province        integer,
    city            integer,
    county          integer,
    rejectcause     character varying(510),
    remark          character varying(510),
    swstationid     integer,
    contractno      character varying(510),
    projectname     character varying(510),
    stationrid      character varying(256)
);
CREATE INDEX idx_24583_idx_wr_stationmanagementcucc_1 ON wr_stationmanagementcucc USING BTREE (applytime, structureid, stationcategory, stationname, stationcode, stationstatus);


CREATE TABLE wr_syncinfo
(
   autoid SERIAL                 NOT NULL PRIMARY KEY,
   stationid INT NULL,
   houseid INT NULL,
   monitorunitid INT NULL,
   synctype INT NOT NULL,
   syncflag INT NOT NULL DEFAULT 0,
   remark VARCHAR(256) NULL
);
CREATE INDEX idx_wr_syncinfo_syncflag ON wr_syncinfo USING BTREE (syncflag);