CREATE TABLE tbl_h5camera
(
    cameraid        integer                NOT NULL PRIMARY KEY,
    cameraname      character varying(510) NOT NULL,
    cameraip        character varying(256) NOT NULL,
    cameraport      integer                NOT NULL,
    channelnumber   character varying(64),
    cameragroupid   integer,
    username        character varying(256),
    password        character varying(64),
    vendorid        integer,
    vendorname      character varying(256),
    cameraindexcode character varying(510),
    cameratype      integer,
    cameratypename  character varying(510),
    updatetime      timestamp,
    description     character varying(510)
);

CREATE TABLE tbl_h5cameragroup
(
    cameragroupid   integer NOT NULL PRIMARY KEY,
    cameragroupname character varying(510),
    parentid        integer,
    description     character varying(510)
);

CREATE TABLE tbl_h5reportcentertypemap
(
    reportid   integer           NOT NULL,
    centertype integer DEFAULT 0 NOT NULL
);

CREATE TABLE tbl_h5reportcronexpression
(
    cronid         integer NOT NULL PRIMARY KEY,
    cronexpression character varying(256) DEFAULT ''::CHARACTER VARYING NOT NULL,
    meaning        character varying(256) DEFAULT ''::CHARACTER VARYING NOT NULL
);

CREATE TABLE tbl_h5reportprocedureparameters
(
    pname      character varying(510) NOT NULL,
    parameters character varying(8000)
);

CREATE TABLE tbl_h5reporttask
(
    taskid          serial                 NOT NULL PRIMARY KEY,
    taskname        character varying(256) NOT NULL,
    cronid          integer                NOT NULL,
    isenable        integer                NOT NULL,
    description     character varying(510),
    reportid        integer                NOT NULL,
    queryparameters text                   NOT NULL,
    creator         integer,
    createtime      timestamp,
    lastupdatetime  timestamp
);