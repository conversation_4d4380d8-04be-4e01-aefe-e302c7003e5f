CREATE TABLE ttsconfig
(
    TtsConfigId    SERIAL PRIMARY KEY,
    TtsConfigKey   NVARCHAR2(255),
    TtsConfigValue NVARCHAR2(10000),
    Description    NVARCHAR2(255)
);

-- 添加字段注释
COMMENT ON COLUMN ttsconfig.TtsConfigKey IS '键名';
COMMENT ON COLUMN ttsconfig.TtsConfigValue IS '对应键值';
COMMENT ON COLUMN ttsconfig.Description IS '描述信息';

-- 创建唯一索引
CREATE UNIQUE INDEX idx_ttsconfig_key ON ttsconfig (TtsConfigKey);

CREATE TABLE ttsfilterconfig
(
    TtsFilterConfigId SERIAL PRIMARY KEY,
    TtsStrategyId     INTEGER,
    TtsConfigKey      NVARCHAR2(255),
    TtsConfigValue    NVARCHAR2(10000)
);

-- 添加字段注释
COMMENT ON COLUMN ttsfilterconfig.TtsFilterConfigId IS '主键自增id';
COMMENT ON COLUMN ttsfilterconfig.TtsStrategyId IS '所属策略的主键id';
COMMENT ON COLUMN ttsfilterconfig.TtsConfigKey IS '键名';
COMMENT ON COLUMN ttsfilterconfig.TtsConfigValue IS '键值';

COMMENT ON TABLE ttsfilterconfig IS 'Tts过滤条件配置表';

CREATE TABLE ttsstrategy
(
    TtsStrategyId      SERIAL PRIMARY KEY,
    StrategyName       NVARCHAR2(50),
    Enable             INTEGER DEFAULT 0,
    EffectiveStartTime TIMESTAMP,
    EffectiveEndTime   TIMESTAMP,
    Description        NVARCHAR2(255)
);

-- 添加字段注释
COMMENT ON COLUMN ttsstrategy.TtsStrategyId IS '主键自增id';
COMMENT ON COLUMN ttsstrategy.StrategyName IS '策略名称';
COMMENT ON COLUMN ttsstrategy.Enable IS '是否启用 0 否 1是';
COMMENT ON COLUMN ttsstrategy.EffectiveStartTime IS '生效开始时间';
COMMENT ON COLUMN ttsstrategy.EffectiveEndTime IS '生效结束时间';
COMMENT ON COLUMN ttsstrategy.Description IS '描述';
COMMENT ON TABLE ttsstrategy IS 'TTS策略表';
