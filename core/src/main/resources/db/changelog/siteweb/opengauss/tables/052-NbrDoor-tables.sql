DROP TABLE IF EXISTS NBR_DoorDevice;

CREATE TABLE NBR_DoorDevice
(
    StationId     int null,
    HouseId     int null,
    EquipmentId   int,
    EquipmentTemplateId     int null,
    StationName         VARCHAR (255) null,
    HouseName         VARCHAR (255) null,
    EquipmentName         VARCHAR (255) null,
    Description         VARCHAR (255) null,
    Address         int null,
    SyncTime timestamp null,
    WorkStationId int null,
    CONSTRAINT PK_NBR_DoorDevice_EquipmentId PRIMARY KEY (EquipmentId)
);

DROP TABLE IF EXISTS NBR_DoorSignal;

CREATE TABLE NBR_DoorSignal
(
    ChannelNo  int not null,
    IsAlarmSignal boolean null,
    EquipmentTemplateId     int not null,
    StationId     int not null,
    EquipmentId   int not null,
    SignalId      int not null,
    SignalName     VARCHAR (255) null,
    StoreInterval       FLOAT DEFAULT ((0)) null,
    AbsValueThreshold   FLOAT DEFAULT ((0)) null,
    PercentThreshold    FLOAT DEFAULT ((0)) null,
    <PERSON>aticsPeriod       INT DEFAULT ((0)) null,
    StartCompareValue FLOAT DEFAULT ((0)) NULL,
    DataType          INT NOT NULL,
    FloatValue        FLOAT,
    StringValue       VARCHAR (128),
    WorkStationId int null,
    CONSTRAINT PK_NBR_DoorSignal_ID PRIMARY KEY (EquipmentId,SignalId)
);

DROP TABLE IF EXISTS Nbr_CardFace;

CREATE TABLE Nbr_CardFace
(
    CardId     int not null,
    FaceId     int null,
    ControllerFaceId      VARCHAR (128) null,
    UpdateTime       VARCHAR (128) null,
    CONSTRAINT PK_Nbr_CardFace_ID PRIMARY KEY (CardId)
);

DROP TABLE IF EXISTS NBR_CallBackEventDic;

CREATE TABLE NBR_CallBackEventDic
(
    Major         INT          NOT NULL,
    MajorDesc     VARCHAR(510) NULL,
    Minor         INT          NOT NULL,
    MinorDesc     VARCHAR(510) NULL,
    MinorHex      VARCHAR(510) NULL,
    AlarmEndMinor INT          NULL,
    ChannelNo     int          NULL,
    CONSTRAINT PK_NBR_CallBackEventDic_Major_Minor PRIMARY KEY (Major,Minor)
);
