<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_UTag_info" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_UTag" remarks="U位标签管理信息表">
            <column name="UTagId" type="int" remarks="U位标签管理信息表主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="TagValue" type="varchar(45)" remarks="标签的唯一识别码">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="AsserId" type="int" remarks="绑定资产Id">
                <constraints nullable="true" unique="true"/>
            </column>
            <column name="IsOnline" type="TINYINT(1)" remarks="是否在线">
                <constraints nullable="true" />
            </column>
            <column name="UDeviceId" type="int" remarks="U位管理Id">
                <constraints nullable="true" />
            </column>
            <column name="UPosition" type="int" remarks="所处U位">
                <constraints nullable="true" />
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_UTag" columnName="UTagId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>