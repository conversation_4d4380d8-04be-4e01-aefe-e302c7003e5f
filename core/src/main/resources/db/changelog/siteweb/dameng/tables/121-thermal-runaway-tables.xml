<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-121" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "libatterypack"
            (
            "BatteryPackId" INT AUTO_INCREMENT NOT NULL,
            "BatteryPackNumber" INT,
            "BatteryNumber" INT,
            "BatteryGroupNumber" INT,
            "BatteryModuleNumber" INT,
            "BatteryPackName" VARCHAR(128),
            "EquipmentId" INT,
            "UPSEquipmentId" INT,
            "MeanVoltageComplexIndexId" INT,
            "MeanTemperatureComplexIndexId" INT,
            "VoltageSignalId" INT,
            "TemperatureSignalId" INT,
            "TemperatureIncrementRateComplexIndexId" INT,
            "TemperatureDeviationComplexIndexId" INT,
            "VoltageDeviationComplexIndexId" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("BatteryPackId"));

            CREATE TABLE "thermalrunawayadvice"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "AdviceType" VARCHAR(128),
            "Reason" VARCHAR(500),
            "Advice" VARCHAR(500),
            "Contacts" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "thermalrunawayaffectdevice"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "UPSEquipmentId" INT,
            "AffectDeviceName" VARCHAR(128),
            "AffectDevicePosition" VARCHAR(128),
            "AffectDevicePurpose" VARCHAR(255),
            "AffectDevicePower" VARCHAR(255),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "thermalrunawaycontrolpolicy"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "BatteryEquipmentId" INT,
            "PhaseId" INT,
            "ControlPolicy" VARCHAR(500),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "thermalrunawaycontrolrecord"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ThermalRunawayEventId" INT,
            "EquipmentId" INT,
            "ControlId" INT,
            "ControlValue" VARCHAR(100),
            "ControlTime" TIMESTAMP(0),
            "ControlResult" VARCHAR(50),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "thermalrunawayevent"
            (
            "ThermalRunawayEventId" INT AUTO_INCREMENT NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "BatteryPackId" INT,
            "TriggerTemperature" DOUBLE,
            "Prediction" VARCHAR(128),
            "PhaseId" INT,
            "Phase" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ThermalRunawayEventId"));

            CREATE TABLE "thermalrunawayphase"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "Phase" VARCHAR(128),
            "Prediction" VARCHAR(128),
            "TriggerTemperature" DOUBLE,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));
        </sql>
    </changeSet>
</databaseChangeLog>