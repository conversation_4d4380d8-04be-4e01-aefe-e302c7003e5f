<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-012" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "wr_dataentry" (
            "EntryId" INT NOT NULL,
            "EntryCategory" INT ,
            "EntryName" VARCHAR(128) ,
            "EntryTitle" VARCHAR(128) ,
            "EntryAlias" VARCHAR(255) ,
            "Enable" BIT DEFAULT 1 NOT NULL,
            "Description" VARCHAR(255) ,
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "wr_dataitem" (
            "EntryItemId" INT AUTO_INCREMENT NOT NULL,
            "EntryId" INT NOT NULL,
            "ItemId" INT NOT NULL,
            "ParentEntryId" INT,
            "ParentItemId" INT,
            "ItemValue" VARCHAR(255) NOT NULL,
            "LastUpdateDate" TIMESTAMP(0),
            "ExtendField1" VARCHAR(255) DEFAULT '',
            "ExtendField2" VARCHAR(255) DEFAULT '',
            "ExtendField3" VARCHAR(255) DEFAULT '',
            NOT CLUSTER PRIMARY KEY("EntryItemId"));

            CREATE TABLE "wr_devicemanagement" (
            "WRDeviceId" INT AUTO_INCREMENT NOT NULL,
            "WRStationId" INT,
            "WRHouseId" INT,
            "WRFsuId" INT,
            "DeviceType" VARCHAR(10) NOT NULL,
            "DeviceCode" VARCHAR(20) NOT NULL,
            "DeviceRId" VARCHAR(128),
            "DeviceName" VARCHAR(128),
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "ApplyTime" TIMESTAMP(0),
            "SWStationId" INT,
            "SWHouseId" INT,
            "Remark" VARCHAR(255) ,
            NOT CLUSTER PRIMARY KEY("WRDeviceId"));

            CREATE TABLE "wr_devicemanagementcucc" (
            "WRDeviceId" INT AUTO_INCREMENT NOT NULL,
            "WRStationId" INT,
            "WRHouseId" INT,
            "WRFsuId" INT,
            "DeviceType" VARCHAR(10) NOT NULL,
            "DeviceCode" VARCHAR(20) NOT NULL,
            "DeviceRId" VARCHAR(128),
            "DeviceName" VARCHAR(128),
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "ApplyTime" TIMESTAMP(0),
            "SWStationId" INT,
            "SWHouseId" INT,
            "Remark" VARCHAR(255) ,
            NOT CLUSTER PRIMARY KEY("WRDeviceId"));
            -- KEY "IDX_EquipmentId_CUCCManage" ("WRFsuId","DeviceCode","SWStationId")

            CREATE TABLE "wr_fsumanagement" (
            "WRFsuId" INT AUTO_INCREMENT NOT NULL,
            "WRHouseId" INT NOT NULL,
            "FsuCode" VARCHAR(20) NOT NULL,
            "FsuName" VARCHAR(255),
            "IPAddress" VARCHAR(255),
            "ManufacturerId" INT,
            "FsuStatus" INT NOT NULL,
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "UserName" VARCHAR(40),
            "Password" VARCHAR(40),
            "FtpUserName" VARCHAR(40),
            "FtpPassword" VARCHAR(40),
            "ApplyTime" TIMESTAMP(0),
            "ApproveTime" TIMESTAMP(0),
            "RejectCause" VARCHAR(255),
            "Remark" VARCHAR(255),
            "SWMonitorUnitId" INT,
            "ContractNo" VARCHAR(255),
            "ProjectName" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("WRFsuId"));

            CREATE TABLE "wr_fsumanagementcucc" (
            "WRFsuId" INT AUTO_INCREMENT NOT NULL,
            "WRHouseId" INT NOT NULL,
            "FsuCode" VARCHAR(20) NOT NULL,
            "FsuName" VARCHAR(255),
            "IPAddress" VARCHAR(255),
            "ManufacturerId" INT,
            "FsuStatus" INT NOT NULL,
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "UserName" VARCHAR(40),
            "Password" VARCHAR(40),
            "FtpUserName" VARCHAR(40),
            "FtpPassword" VARCHAR(40),
            "ApplyTime" TIMESTAMP(0),
            "ApproveTime" TIMESTAMP(0),
            "RejectCause" VARCHAR(255),
            "Remark" VARCHAR(255),
            "SWMonitorUnitId" int,
            "ContractNo" VARCHAR(255),
            "ProjectName" VARCHAR(255),
            "FsuRId" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("WRFsuId"));

            CREATE TABLE "wr_housemanagement" (
            "WRHouseId" INT AUTO_INCREMENT NOT NULL,
            "WRStationId" INT NOT NULL,
            "HouseCode" VARCHAR(20) NOT NULL,
            "HouseName" VARCHAR(128) ,
            "HouseStatus" INT NOT NULL,
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128) ,
            "ApplyTime" datetime ,
            "ApproveTime" datetime ,
            "RejectCause" VARCHAR(255) ,
            "Remark" VARCHAR(255) ,
            "SWHouseId" INT ,
            PRIMARY KEY ("WRHouseId")
            );

            CREATE TABLE "wr_housemanagementcucc" (
            "WRHouseId" INT AUTO_INCREMENT NOT NULL,
            "WRStationId" INT NOT NULL,
            "HouseCode" VARCHAR(20) NOT NULL,
            "HouseName" VARCHAR(128),
            "HouseStatus" INT NOT NULL,
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "ApplyTime" TIMESTAMP(0),
            "ApproveTime" TIMESTAMP(0),
            "RejectCause" VARCHAR(255),
            "Remark" VARCHAR(255),
            "SWHouseId" INT,
            "HouseRId" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("WRHouseId"));

            CREATE TABLE "wr_operationrecord" (
            "OperationRecordId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "WRStationId" INT NOT NULL,
            "WRStationName" VARCHAR(255),
            "OpCategory" INT NOT NULL,
            "OpItemId" INT NOT NULL,
            "OpItemValue" VARCHAR(255) NOT NULL,
            "OpDateTime" TIMESTAMP(0),
            "UpdateString" TEXT,
            "LastString" TEXT,
            "OpUserId" INT,
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255));

            CREATE TABLE "wr_stationcode" (
            "CountyId" INT NOT NULL,
            "MinValue" INT NOT NULL,
            "CurrentValue" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("CountyId"));

            CREATE TABLE "wr_stationmanagement" (
            "WRStationId" INT AUTO_INCREMENT NOT NULL,
            "StructureId" INT NOT NULL,
            "StationCode" VARCHAR(20) NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "StationCategory" INT NOT NULL,
            "StationStatus" INT NOT NULL,
            "Address" VARCHAR(255),
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "ApplyTime" TIMESTAMP(0),
            "ApproveTime" TIMESTAMP(0),
            "Province" INT,
            "City" INT,
            "County" INT,
            "RejectCause" VARCHAR(255),
            "Remark" VARCHAR(255),
            "SWStationId" INT,
            "ContractNo" VARCHAR(255),
            "ProjectName" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("WRStationId"));

            CREATE OR REPLACE  INDEX "Idx_WR_StationManagement_1" ON "wr_stationmanagement"("ApplyTime" ASC,"StructureId" ASC,"StationCategory" ASC,"StationName" ASC,"StationCode" ASC,"StationStatus" ASC);

            CREATE TABLE "wr_stationmanagementcucc" (
            "WRStationId" INT AUTO_INCREMENT NOT NULL,
            "StructureId" INT NOT NULL,
            "StationCode" VARCHAR(20) NOT NULL,
            "StationName" VARCHAR(255) NOT NULL,
            "StationCategory" INT NOT NULL,
            "StationStatus" INT NOT NULL,
            "Address" VARCHAR(255),
            "UserId" INT NOT NULL,
            "SWUserName" VARCHAR(128),
            "ApplyTime" TIMESTAMP(0),
            "ApproveTime" TIMESTAMP(0),
            "Province" INT,
            "City" INT,
            "County" INT,
            "RejectCause" VARCHAR(255) ,
            "Remark" VARCHAR(255) ,
            "SWStationId" INT ,
            "ContractNo" VARCHAR(255) ,
            "ProjectName" VARCHAR(255) ,
            "StationRId" VARCHAR(128) ,
            NOT CLUSTER PRIMARY KEY("WRStationId"));

            CREATE OR REPLACE  INDEX "Idx_WR_StationManagementCUCC_1" ON "wr_stationmanagementcucc"("ApplyTime" ASC,"StructureId" ASC,"StationCategory" ASC,"StationName" ASC,"StationCode" ASC,"StationStatus" ASC);

            CREATE TABLE "wr_syncinfo" (
            "AutoId" INT AUTO_INCREMENT NOT NULL,
            "StationId" INT NULL,
            "HouseId" INT NULL,
            "MonitorUnitId" INT NULL,
            "SyncType" INT NOT NULL,
            "SyncFlag" INT NOT NULL DEFAULT 0,
            "Remark" VARCHAR(128) NULL,
            NOT CLUSTER PRIMARY KEY("AutoId")
            );

            CREATE OR REPLACE INDEX "Idx_WR_SyncInfo_1" ON "wr_syncinfo"("SyncType" ASC, "SyncFlag" ASC);
        </sql>
    </changeSet>
</databaseChangeLog>