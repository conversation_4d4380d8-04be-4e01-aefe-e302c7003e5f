<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-101" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "qrtz_job_details"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "JOB_NAME" VARCHAR(200) NOT NULL,
            "JOB_GROUP" VARCHAR(200) NOT NULL,
            "DESCRIPTION" VARCHAR(250),
            "JOB_CLASS_NAME" VARCHAR(250) NOT NULL,
            "IS_DURABLE" VARCHAR(1) NOT NULL,
            "IS_NONCONCURRENT" VARCHAR(1) NOT NULL,
            "IS_UPDATE_DATA" VARCHAR(1) NOT NULL,
            "REQUESTS_RECOVERY" VARCHAR(1) NOT NULL,
            "JOB_DATA" BLOB,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "JOB_NAME", "JOB_GROUP"));

            CREATE OR REPLACE  INDEX "IDX_QRTZ_J_REQ_RECOVERY" ON "qrtz_job_details"("SCHED_NAME" ASC,"REQUESTS_RECOVERY" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_J_GRP" ON "qrtz_job_details"("SCHED_NAME" ASC,"JOB_GROUP" ASC);

            CREATE TABLE "qrtz_triggers"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "TRIGGER_NAME" VARCHAR(200) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            "JOB_NAME" VARCHAR(200) NOT NULL,
            "JOB_GROUP" VARCHAR(200) NOT NULL,
            "DESCRIPTION" VARCHAR(250),
            "NEXT_FIRE_TIME" BIGINT,
            "PREV_FIRE_TIME" BIGINT,
            "PRIORITY" INT,
            "TRIGGER_STATE" VARCHAR(16) NOT NULL,
            "TRIGGER_TYPE" VARCHAR(8) NOT NULL,
            "START_TIME" BIGINT NOT NULL,
            "END_TIME" BIGINT,
            "CALENDAR_NAME" VARCHAR(200),
            "MISFIRE_INSTR" SMALLINT,
            "JOB_DATA" BLOB,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP"),
            CONSTRAINT "qrtz_triggers_ibfk_1" FOREIGN KEY("SCHED_NAME", "JOB_NAME", "JOB_GROUP") REFERENCES "qrtz_job_details"("SCHED_NAME", "JOB_NAME", "JOB_GROUP") WITH INDEX );

            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_G" ON "qrtz_triggers"("SCHED_NAME" ASC,"TRIGGER_GROUP" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_NFT_MISFIRE" ON "qrtz_triggers"("SCHED_NAME" ASC,"MISFIRE_INSTR" ASC,"NEXT_FIRE_TIME" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_N_STATE" ON "qrtz_triggers"("SCHED_NAME" ASC,"TRIGGER_NAME" ASC,"TRIGGER_GROUP" ASC,"TRIGGER_STATE" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_NFT_ST_MISFIRE_GRP" ON "qrtz_triggers"("SCHED_NAME" ASC,"MISFIRE_INSTR" ASC,"NEXT_FIRE_TIME" ASC,"TRIGGER_GROUP" ASC,"TRIGGER_STATE" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_N_G_STATE" ON "qrtz_triggers"("SCHED_NAME" ASC,"TRIGGER_GROUP" ASC,"TRIGGER_STATE" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_NFT_ST" ON "qrtz_triggers"("SCHED_NAME" ASC,"TRIGGER_STATE" ASC,"NEXT_FIRE_TIME" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_STATE" ON "qrtz_triggers"("SCHED_NAME" ASC,"TRIGGER_STATE" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_NFT_ST_MISFIRE" ON "qrtz_triggers"("SCHED_NAME" ASC,"MISFIRE_INSTR" ASC,"NEXT_FIRE_TIME" ASC,"TRIGGER_STATE" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_JG" ON "qrtz_triggers"("SCHED_NAME" ASC,"JOB_GROUP" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_C" ON "qrtz_triggers"("SCHED_NAME" ASC,"CALENDAR_NAME" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_NEXT_FIRE_TIME" ON "qrtz_triggers"("SCHED_NAME" ASC,"NEXT_FIRE_TIME" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_T_J" ON "qrtz_triggers"("SCHED_NAME" ASC,"JOB_NAME" ASC,"JOB_GROUP" ASC);

            CREATE TABLE "qrtz_simple_triggers"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "TRIGGER_NAME" VARCHAR(200) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            "REPEAT_COUNT" BIGINT NOT NULL,
            "REPEAT_INTERVAL" BIGINT NOT NULL,
            "TIMES_TRIGGERED" BIGINT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP"),
            CONSTRAINT "qrtz_simple_triggers_ibfk_1" FOREIGN KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") REFERENCES "qrtz_triggers"("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") WITH INDEX );

            CREATE TABLE "qrtz_cron_triggers"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "TRIGGER_NAME" VARCHAR(200) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            "CRON_EXPRESSION" VARCHAR(120) NOT NULL,
            "TIME_ZONE_ID" VARCHAR(80),
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP"),
            CONSTRAINT "qrtz_cron_triggers_ibfk_1" FOREIGN KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") REFERENCES "qrtz_triggers"("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") WITH INDEX );

            CREATE TABLE "qrtz_simprop_triggers"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "TRIGGER_NAME" VARCHAR(200) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            "STR_PROP_1" VARCHAR(512),
            "STR_PROP_2" VARCHAR(512),
            "STR_PROP_3" VARCHAR(512),
            "INT_PROP_1" INT,
            "INT_PROP_2" INT,
            "LONG_PROP_1" BIGINT,
            "LONG_PROP_2" BIGINT,
            "DEC_PROP_1" DECIMAL(13,4),
            "DEC_PROP_2" DECIMAL(13,4),
            "BOOL_PROP_1" VARCHAR(1),
            "BOOL_PROP_2" VARCHAR(1),
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP"),
            CONSTRAINT "qrtz_simprop_triggers_ibfk_1" FOREIGN KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") REFERENCES "qrtz_triggers"("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") WITH INDEX );

            CREATE TABLE "qrtz_blob_triggers"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "TRIGGER_NAME" VARCHAR(200) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            "BLOB_DATA" BLOB,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP"),
            CONSTRAINT "qrtz_blob_triggers_ibfk_1" FOREIGN KEY("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") REFERENCES "qrtz_triggers"("SCHED_NAME", "TRIGGER_NAME", "TRIGGER_GROUP") WITH INDEX );

            CREATE OR REPLACE  INDEX "SCHED_NAME" ON "qrtz_blob_triggers"("SCHED_NAME" ASC,"TRIGGER_NAME" ASC,"TRIGGER_GROUP" ASC);

            CREATE TABLE "qrtz_calendars"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "CALENDAR_NAME" VARCHAR(200) NOT NULL,
            "CALENDAR" BLOB NOT NULL,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "CALENDAR_NAME"));

            CREATE TABLE "qrtz_paused_trigger_grps"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "TRIGGER_GROUP"));

            CREATE TABLE "qrtz_fired_triggers"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "ENTRY_ID" VARCHAR(95) NOT NULL,
            "TRIGGER_NAME" VARCHAR(200) NOT NULL,
            "TRIGGER_GROUP" VARCHAR(200) NOT NULL,
            "INSTANCE_NAME" VARCHAR(200) NOT NULL,
            "FIRED_TIME" BIGINT NOT NULL,
            "SCHED_TIME" BIGINT NOT NULL,
            "PRIORITY" INT NOT NULL,
            "STATE" VARCHAR(16) NOT NULL,
            "JOB_NAME" VARCHAR(200),
            "JOB_GROUP" VARCHAR(200),
            "IS_NONCONCURRENT" VARCHAR(1),
            "REQUESTS_RECOVERY" VARCHAR(1),
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "ENTRY_ID"));

            CREATE OR REPLACE  INDEX "IDX_QRTZ_FT_JG" ON "qrtz_fired_triggers"("SCHED_NAME" ASC,"JOB_GROUP" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_FT_INST_JOB_REQ_RCVRY" ON "qrtz_fired_triggers"("SCHED_NAME" ASC,"INSTANCE_NAME" ASC,"REQUESTS_RECOVERY" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_FT_J_G" ON "qrtz_fired_triggers"("SCHED_NAME" ASC,"JOB_NAME" ASC,"JOB_GROUP" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_FT_TG" ON "qrtz_fired_triggers"("SCHED_NAME" ASC,"TRIGGER_GROUP" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_FT_T_G" ON "qrtz_fired_triggers"("SCHED_NAME" ASC,"TRIGGER_NAME" ASC,"TRIGGER_GROUP" ASC);
            CREATE OR REPLACE  INDEX "IDX_QRTZ_FT_TRIG_INST_NAME" ON "qrtz_fired_triggers"("SCHED_NAME" ASC,"INSTANCE_NAME" ASC);

            CREATE TABLE "qrtz_scheduler_state"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "INSTANCE_NAME" VARCHAR(200) NOT NULL,
            "LAST_CHECKIN_TIME" BIGINT NOT NULL,
            "CHECKIN_INTERVAL" BIGINT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "INSTANCE_NAME"));

            CREATE TABLE "qrtz_locks"
            (
            "SCHED_NAME" VARCHAR(120) NOT NULL,
            "LOCK_NAME" VARCHAR(40) NOT NULL,
            NOT CLUSTER PRIMARY KEY("SCHED_NAME", "LOCK_NAME"));
        </sql>
    </changeSet>
</databaseChangeLog>