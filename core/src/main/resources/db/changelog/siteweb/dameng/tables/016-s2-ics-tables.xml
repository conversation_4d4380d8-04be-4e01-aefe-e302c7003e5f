<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-016" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_icsarchivetask"
            (
            "TaskId" INT,
            "TaskName" VARCHAR(128),
            "ProcedureName" VARCHAR(128),
            "FileName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY ("TaskId"));

            CREATE TABLE "tbl_icscontractinstall"
            (
            "ContractNo" VARCHAR(128),
            "ProjectName" VARCHAR(128),
            "PrimaryDate" TIMESTAMP(0),
            "EndDate" TIMESTAMP(0),
            "QualityStartPoint" VARCHAR(128),
            "QualityPeriod" INT,
            "QualityTerms" VARCHAR(128),
            "StationCount" INT,
            "FsuCount" INT,
            "EquipmentCount" INT,
            NOT CLUSTER PRIMARY KEY (ContractNo));

            CREATE TABLE "tbl_icscontractmaintenance"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ContractNo" VARCHAR(128),
            "ProjectName" VARCHAR(128),
            "StartDate" TIMESTAMP(0),
            "EndDate" TIMESTAMP(0),
            "MaintenanceTerms" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_icsequipmenttype"
            (
            "EquipmentTypeId" INT,
            "EquipmentTypeName" VARCHAR(128),
            "IsCanInputId" INT,
            NOT CLUSTER PRIMARY KEY (EquipmentTypeId));

            CREATE TABLE "tbl_icsfsudatainfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SampleTime" TIMESTAMP(0),
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "Ip" VARCHAR(128),
            "FsuType" VARCHAR(128),
            "SiteName" VARCHAR(128),
            "SiteVersion" VARCHAR(128),
            "SiteCompileTime" TIMESTAMP(0),
            "Hw" VARCHAR(128),
            "FileSystem" VARCHAR(128),
            "Linux" VARCHAR(128),
            "CpuUsage" DOUBLE,
            "MemTotal" DOUBLE,
            "MemFree" DOUBLE,
            "MemUsage" DOUBLE,
            "UpTime" DOUBLE,
            "FileNr" VARCHAR(128),
            "MuFileName" VARCHAR(128),
            "MuModifyTime" TIMESTAMP(0),
            "MuFileSize" INT,
            "BackUpMd5" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsudatanewinfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SiteName" VARCHAR(128),
            "FsuType" VARCHAR(128),
            "Hw" VARCHAR(128),
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "Ip" VARCHAR(128),
            "MemTotal" DOUBLE,
            "FlashSize" VARCHAR(60),
            "Linux" VARCHAR(128),
            "SiteVersion" VARCHAR(128),
            "CpuUsage" DOUBLE,
            "MemUsage" DOUBLE,
            "FlashUsedRate" VARCHAR(60),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsuflashdf"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "FlashName" VARCHAR(128),
            "FileSystem" VARCHAR(128),
            "FlashSize" VARCHAR(60),
            "FlashUsed" VARCHAR(60),
            "FlashAvailable" VARCHAR(60),
            "FlashUsedRate" VARCHAR(60),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsuftpdownloadinfo"
            (
            "DownLoadTime" TIMESTAMP(0),
            "ServerIp" VARCHAR(128),
            "FsuIp" VARCHAR(128),
            "DownLoadPath" VARCHAR(128),
            "FileName" VARCHAR(128),
            "FileSize" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY (ServerIp,FsuIp,DownLoadTime));

            CREATE TABLE "tbl_icsfsumuequip"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "EquipName" VARCHAR(128),
            "EquipId" INT,
            "StdEquipId" INT,
            "StdEquipName" VARCHAR(128),
            "EquipAddr" VARCHAR(128),
            "EquipPhoneno" VARCHAR(128),
            "DllPath" VARCHAR(128),
            "PortNo" INT,
            "PortType" INT,
            "Setting" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsumumodel"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "SoFileName" VARCHAR(128),
            "SoVersion" VARCHAR(128),
            "IsBInterface" VARCHAR(20),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsupsinfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "PsName" VARCHAR(128),
            "PsVersion" VARCHAR(128),
            "PsCount" INT,
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsusoinfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "Ip" VARCHAR(128),
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "FileName" VARCHAR(128),
            "SoVersion" VARCHAR(128),
            "SoCode" VARCHAR(128),
            "IsUsed" VARCHAR(20),
            "IsLoaded" VARCHAR(20),
            "GccInfo" VARCHAR(128),
            "Md5" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsfsuudpinfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SN" VARCHAR(128),
            "Mac" VARCHAR(128),
            "FsuHost" VARCHAR(128),
            "Ip" VARCHAR(128),
            "NetMask" VARCHAR(20),
            "GateWay" VARCHAR(20),
            "SiteName" VARCHAR(128),
            "Version" VARCHAR(128),
            "ServerIp" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY (SN, Mac, CollectTime));

            CREATE TABLE "tbl_icsplatformqrcode"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "EquipmentTypeId" INT,
            "SerialNumber" VARCHAR(128),
            "EquipmentName" VARCHAR(128),
            "CreateDate" TIMESTAMP(0),
            "OperatorName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_icsscriptdatainfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "ServerIp" VARCHAR(128),
            "Module" VARCHAR(255) NOT NULL,
            "ScriptVersion" VARCHAR(30),
            "CompileDate" TIMESTAMP(0),
            "CreateDate" TIMESTAMP(0),
            "Feature" VARCHAR(255) NOT NULL,
            NOT CLUSTER PRIMARY KEY (ServerIp, CollectTime));

            CREATE TABLE "tbl_icsserverinfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "ServerIp" VARCHAR(128),
            "CpuUsedRate" DOUBLE,
            "MemoryUsedRate" DOUBLE,
            "CdriveUsedRate" DOUBLE,
            "DbServerIp" VARCHAR(128),
            "DbInstallPath" VARCHAR(128),
            "DbDriveUsedRate" DOUBLE,
            NOT CLUSTER PRIMARY KEY (ServerIp,CollectTime));

            CREATE TABLE "tbl_icssitewebinfo"
            (
            "CollectTime" TIMESTAMP(0) NOT NULL,
            "SiteWebName" VARCHAR(128),
            "SiteWebVersion" VARCHAR(128),
            "CompileDate" TIMESTAMP(0),
            "UsedDate" TIMESTAMP(0),
            "ServerName" VARCHAR(128),
            "ServerIp" VARCHAR(128),
            "OperateSystem" VARCHAR(128),
            "CpuConfig" VARCHAR(255),
            "MemorySize" VARCHAR(128),
            "DiskDrive" VARCHAR(128),
            "InstallPath" VARCHAR(128),
            "DiskUsedRate" DOUBLE,
            NOT CLUSTER PRIMARY KEY (SiteWebName,ServerIp,CollectTime));
        </sql>
    </changeSet>
</databaseChangeLog>