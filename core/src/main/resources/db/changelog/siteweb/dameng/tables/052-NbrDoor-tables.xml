<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-052" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "nbr_callbackeventdic"
            (
            "Major" INT NOT NULL,
            "MajorDesc" VARCHAR(255),
            "Minor" INT NOT NULL,
            "MinorDesc" VARCHAR(255),
            "MinorHex" VARCHAR(255),
            "AlarmEndMinor" INT,
            "ChannelNo" INT,
            NOT CLUSTER PRIMARY KEY("Major", "Minor"));

            CREATE TABLE "nbr_cardface"
            (
            "CardId" INT NOT NULL,
            "FaceId" INT,
            "ControllerFaceId" VARCHAR(128),
            "UpdateTime" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("CardId"));

            CREATE TABLE "nbr_doordevice"
            (
            "StationId" INT,
            "HouseId" INT,
            "EquipmentId" INT,
            "EquipmentTemplateId" INT,
            "StationName" VARCHAR(255),
            "HouseName" VARCHAR(255),
            "EquipmentName" VARCHAR(255),
            "Description" VARCHAR(255),
            "Address" INT,
            "SyncTime" TIMESTAMP(0),
            "WorkStationId" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentId"));

            CREATE TABLE "nbr_doorsignal"
            (
            "ChannelNo" INT NOT NULL,
            "IsAlarmSignal" TINYINT,
            "EquipmentTemplateId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "StoreInterval" REAL DEFAULT 0,
            "AbsValueThreshold" REAL DEFAULT 0,
            "PercentThreshold" REAL DEFAULT 0,
            "StaticsPeriod" INT DEFAULT 0,
            "StartCompareValue" REAL DEFAULT 0,
            "DataType" INT NOT NULL,
            "FloatValue" REAL,
            "StringValue" VARCHAR(128),
            "WorkStationId" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "SignalId"));
        </sql>
    </changeSet>
</databaseChangeLog>