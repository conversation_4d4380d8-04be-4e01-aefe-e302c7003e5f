<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-111" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "batterycellmodel"
            (
            "BatteryCellModelId" INT AUTO_INCREMENT NOT NULL,
            "Vendor" VARCHAR(128),
            "Model" VARCHAR(255),
            "VoltageType" INT,
            "RatedVoltage" DECIMAL(5,2),
            "RatedCapacity" DECIMAL(6,2),
            "InitialIR" DECIMAL(10,2),
            "FloatChargeVoltage" DECIMAL(5,2),
            "EvenChargeVoltage" DECIMAL(5,2),
            "TempCompensationFactor" DECIMAL(3,2),
            "TerminationVoltage" DECIMAL(5,2),
            "ChargingCurrentLimitFactor" DECIMAL(5,2),
            "DischargeCurrentLimitFactor" DECIMAL(5,2),
            "Length" INT,
            "Width" INT,
            "Height" INT,
            "Weight" DECIMAL(10,2),
            NOT CLUSTER PRIMARY KEY("BatteryCellModelId"));

            CREATE TABLE "batterydischargerecord"
            (
            "BatteryDischargeRecordId" INT AUTO_INCREMENT NOT NULL,
            "BatteryStringId" INT,
            "EquipmentId" INT,
            "StartDischargeTime" TIMESTAMP(0),
            "EndDischargeTime" TIMESTAMP(0),
            "SignalId" INT,
            NOT CLUSTER PRIMARY KEY("BatteryDischargeRecordId"));

            CREATE TABLE "batterystring"
            (
            "BatteryStringId" INT AUTO_INCREMENT NOT NULL,
            "BatteryStringName" VARCHAR(128),
            "CellCount" INT,
            "EquipmentId" INT,
            "BatteryCellModelId" INT,
            "StandbyPower" INT,
            "StartUsingTime" TIMESTAMP(0),
            "CurrentTransformerType" VARCHAR(255),
            "Vendor" VARCHAR(255),
            "Model" VARCHAR(255),
            "RatedVoltage" DECIMAL(5,2),
            "RatedCapacity" DECIMAL(6,2),
            "InitialIR" DECIMAL(10,2),
            "FloatChargeVoltage" DECIMAL(5,2),
            "EvenChargeVoltage" DECIMAL(5,2),
            "TempCompensationFactor" DECIMAL(3,2),
            "TerminationVoltage" DECIMAL(10,2),
            "MaxChargingCurrent" DECIMAL(5,2),
            "MaxFloatChargeVoltage" DECIMAL(10,2),
            "Weight" DECIMAL(10,2),
            NOT CLUSTER PRIMARY KEY("BatteryStringId"));
        </sql>
    </changeSet>
</databaseChangeLog>