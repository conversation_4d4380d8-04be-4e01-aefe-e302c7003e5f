<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-120" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "capacityattribute"
            (
            "AttributeId" INT AUTO_INCREMENT NOT NULL,
            "BaseTypeId" INT,
            "BaseAttributeId" INT,
            "AttributeName" VARCHAR(64),
            "LogicType" INT,
            "Description" VARCHAR(128),
            "ObjectId" INT,
            "ObjectTypeId" INT,
            "ComplexIndex" INT,
            "MinValue" DOUBLE,
            "MaxValue" DOUBLE,
            "RatedCapacity" DOUBLE,
            "DefaultValue" DOUBLE,
            "CompensateFactor" DOUBLE,
            "OriginCapacity" DOUBLE,
            "UsedCapacity" DOUBLE,
            "FreeCapacity" DOUBLE,
            "Percent" DOUBLE,
            "SampleTime" VARCHAR(20),
            "Unit" VARCHAR(16),
            "Precision" INT,
            NOT CLUSTER PRIMARY KEY("AttributeId"));

            CREATE TABLE "capacityattributequartzrecord"
            (
            "recordId" INT AUTO_INCREMENT NOT NULL,
            "BaseAttributeId" INT,
            "AttributeName" VARCHAR(64),
            "ObjectId" INT,
            "ObjectTypeId" INT,
            "RatedCapacity" DOUBLE,
            "UsedCapacity" DOUBLE,
            "FreeCapacity" DOUBLE,
            "Percent" DOUBLE,
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("recordId"));

            CREATE TABLE "capacitybaseattribute"
            (
            "BaseAttributeId" INT AUTO_INCREMENT NOT NULL,
            "BaseAttributeName" VARCHAR(64),
            "BaseTypeId" INT,
            "AttributeName" VARCHAR(64),
            "LogicType" INT,
            "Unit" VARCHAR(16),
            "Description" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("BaseAttributeId"));

            CREATE TABLE "capacitybasetype"
            (
            "BaseTypeId" INT AUTO_INCREMENT NOT NULL,
            "BaseTypeName" VARCHAR(64),
            "Description" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("BaseTypeId"));

            CREATE TABLE "computerrack"
            (
            "ComputerRackId" INT AUTO_INCREMENT NOT NULL,
            "ComputerRackName" VARCHAR(128),
            "ComputerRackNumber" VARCHAR(128),
            "ResourceStructureId" INT,
            "Position" VARCHAR(128),
            "Customer" VARCHAR(128),
            "Business" VARCHAR(128),
            "StartTime" TIMESTAMP(0),
            "Remark" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ComputerRackId"),
            CONSTRAINT "ComputerRackNumber" UNIQUE("ComputerRackNumber"));

            CREATE TABLE "computerrackequipmentemap"
            (
            "ComputerRackEquipmentMapId" INT AUTO_INCREMENT NOT NULL,
            "ComputerRackId" INT,
            "EquipmentId" INT,
            NOT CLUSTER PRIMARY KEY("ComputerRackEquipmentMapId"));

            CREATE TABLE "computerracksignalmap" (
            "computerRackSignalMapId" INT AUTO_INCREMENT NOT NULL,
            "computerRackId" INT NOT NULL,
            "openExpression" VARCHAR(256),
            "powerExpression" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("computerRackSignalMapId")
            );
            CREATE UNIQUE INDEX ind_computerracksignalmap_unique on "computerracksignalmap" ("ComputerRackId");

            CREATE TABLE "itdevice"
            (
            "ITDeviceId" INT AUTO_INCREMENT NOT NULL,
            "SerialNumber" VARCHAR(64),
            "ITDeviceName" VARCHAR(64),
            "ITDeviceModelId" INT,
            "ComputerRackId" INT,
            "UIndex" INT,
            "Customer" VARCHAR(64),
            "Business" VARCHAR(64),
            "PurchaseDate" TIMESTAMP(0),
            "LaunchDate" TIMESTAMP(0),
            "Remark" VARCHAR(255),
            "AssetDeviceId" INT,
            "ipaddr" VARCHAR(64),
            NOT CLUSTER PRIMARY KEY("ITDeviceId"));

            CREATE TABLE "itdevicemodel"
            (
            "ITDeviceModelId" INT AUTO_INCREMENT NOT NULL,
            "ITDeviceModelName" VARCHAR(64),
            "UnitHeight" INT,
            "Manufactor" VARCHAR(64),
            "Model" VARCHAR(64),
            "Brand" VARCHAR(64),
            "Length" DOUBLE,
            "Width" DOUBLE,
            "Height" DOUBLE,
            "RatePower" DOUBLE,
            "RateCooling" DOUBLE,
            "RateWeight" DOUBLE,
            "ModelFile" VARCHAR(255),
            "CategoryId" INT,
            "AssetDeviceId" INT,
            NOT CLUSTER PRIMARY KEY("ITDeviceModelId"));

            CREATE TABLE "tbl_rackmountrecord"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "RackId" INT NOT NULL,
            "RackDeviceId" INT NOT NULL,
            "RackPosition" INT NOT NULL,
            "OperateTime" TIMESTAMP(0) NOT NULL,
            "OperateState" INT NOT NULL,
            "Expired" TINYINT,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "rackmountrecord_expired_index" ON "tbl_rackmountrecord"("Expired" ASC);

            CREATE TABLE "tbl_udevice"
            (
            "UDeviceId" INT AUTO_INCREMENT NOT NULL,
            "uDeviceNumber" VARCHAR(128),
            "IsOnline" TINYINT,
            "RackId" INT,
            "ModuleCnt" INT,
            "UTagCnt" INT,
            "IpAddr" VARCHAR(45),
            "SWEquipmentId" INT,
            "CreateTime" TIMESTAMP(0),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("UDeviceId"),
            CONSTRAINT "RackId" UNIQUE("RackId"),
            CONSTRAINT "uDeviceNumber" UNIQUE("uDeviceNumber"));

            CREATE TABLE "tbl_utag"
            (
            "UTagId" INT AUTO_INCREMENT NOT NULL,
            "TagValue" VARCHAR(45),
            "AsserId" INT,
            "IsOnline" TINYINT,
            "UDeviceId" INT,
            "UPosition" INT,
            "CreateTime" TIMESTAMP(0),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("UTagId"),
            CONSTRAINT "AsserId" UNIQUE("AsserId"),
            CONSTRAINT "TagValue" UNIQUE("TagValue"));

            CREATE TABLE "yd_tmputagmap"
            (
            "TagValue" VARCHAR(255) NOT NULL primary key ,
            "AsserId" INT,
            "EquipmentId" INT,
            "SignalId" INT);
        </sql>
    </changeSet>
</databaseChangeLog>