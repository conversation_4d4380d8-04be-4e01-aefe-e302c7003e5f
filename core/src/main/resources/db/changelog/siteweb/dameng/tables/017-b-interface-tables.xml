<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-017" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            <!-- RowId是达梦数据库关键字，这里暂且注释掉该表 -->
<!--            CREATE TABLE "st_org_cucc_equip" (-->
<!--            "RowId" INT NOT NULL,-->
<!--            "Class" VARCHAR(255),-->
<!--            "Type" VARCHAR(255),-->
<!--            "C1" VARCHAR(255),-->
<!--            "C2" VARCHAR(255),-->
<!--            "TypeId" VARCHAR(255),-->
<!--            "Note" VARCHAR(255));-->

            CREATE TABLE "tbl_activemessagecmcc" (
            "UserId" INT,
            "SerialNo" DECIMAL(14,0) NOT NULL,
            "SiteID" VARCHAR(20) NOT NULL,
            "FSUID" VARCHAR(20) NOT NULL,
            "DeviceID" VARCHAR(20) NOT NULL,
            "SiteName" TEXT,
            "FSUName" TEXT,
            "DeviceName" TEXT,
            "MessageType" INT NOT NULL,
            "MessageName" VARCHAR(64),
            "MessageParm" TEXT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Result" INT,
            "Try" INT,
            "FailureCause" TEXT NOT NULL,
            "IsConfirm" BIT DEFAULT 0 NOT NULL,
            "ControlType" INT,
            "Description" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceID", "FSUID", "MessageType", "SerialNo", "SiteID"));

            CREATE TABLE "tbl_activemessagecucc" (
            "UserId" INT,
            "SerialNo" DECIMAL(14,0) NOT NULL,
            "SUID" VARCHAR(255) NOT NULL,
            "DeviceID" VARCHAR(255) NOT NULL,
            "SURID" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "MessageType" INT NOT NULL,
            "MessageName" VARCHAR(64),
            "MessageParm" TEXT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Result" INT,
            "Try" INT,
            "FailureCause" TEXT NOT NULL,
            "IsConfirm" BIT DEFAULT 0 NOT NULL,
            "ControlType" INT,
            "Description" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY ("MessageType","SerialNo"));

            CREATE TABLE "tbl_alarmdatacucc"
            (
            "SUID" VARCHAR(64) NOT NULL,
            "DeviceID" VARCHAR(64) NOT NULL,
            "StandardID" VARCHAR(64) NOT NULL,
            "SURID" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "StandardName" VARCHAR(255),
            "SerialNo" VARCHAR(10),
            "AlarmTime" TIMESTAMP(0),
            "AlarmDesc" VARCHAR(255),
            "TriggerVal" DOUBLE,
            "AlarmFlag" INT,
            "Description" TEXT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "AlarmDataType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("AlarmDataType", "DeviceID", "StandardID", "SUID"));

            CREATE TABLE "tbl_alarmpropertycucc"
            (
            "SUID" VARCHAR(64) NOT NULL,
            "DeviceID" VARCHAR(64) NOT NULL,
            "StandardID" VARCHAR(64) NOT NULL,
            "SURID" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "StandardName" VARCHAR(255),
            "BDelay" DOUBLE,
            "EDelay" DOUBLE,
            "Description" TEXT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "AlarmPropertyType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("AlarmPropertyType", "DeviceID", "StandardID", "SUID"));

            CREATE TABLE "tbl_aodatacucc"
            (
            "SUID" VARCHAR(64) NOT NULL,
            "DeviceID" VARCHAR(64) NOT NULL,
            "StandardID" VARCHAR(64) NOT NULL,
            "SURID" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "StandardName" VARCHAR(255),
            "SetValue" DOUBLE,
            "HLimit" DOUBLE,
            "SHLimit" DOUBLE,
            "LLimit" DOUBLE,
            "SLLimit" DOUBLE,
            "Threshold" DOUBLE,
            "RelativeVal" DOUBLE,
            "IntervalTime" DOUBLE,
            "Description" TEXT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "AODataType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("AODataType", "DeviceID", "StandardID", "SUID"));

            CREATE TABLE "tbl_devicectcc"
            (
            "DeviceCTCCId" int PRIMARY KEY AUTO_INCREMENT,
            "SUDeviceTypeName" VARCHAR(50),
            "SUDeviceTypeId" VARCHAR(10),
            "EquipmentBaseTypeName" VARCHAR(50),
            "Description" VARCHAR(255),
            "EquipmentBaseTypeId" INT,
            "Meanings" VARCHAR(255),
            "UpdateTime" VARCHAR(50),
            "Recorder" VARCHAR(50),
            "DeviceTypeId" VARCHAR(10));

            CREATE TABLE "tbl_devicesubtypecmcc"
            (
            "DeviceTypeID" INT NOT NULL,
            "DeviceSubTypeID" INT NOT NULL,
            "DeviceSubTypeName" VARCHAR(128),
            "Description" TEXT,
            NOT CLUSTER PRIMARY KEY("DeviceSubTypeID", "DeviceTypeID"));

            CREATE TABLE "tbl_devicetypebasemapcmcc"
            (
            "DeviceTypeID" INT NOT NULL,
            "DeviceSubTypeID" INT NOT NULL,
            "BaseEquipmentID" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceTypeID", "DeviceSubTypeID", "BaseEquipmentID"));

            CREATE TABLE "tbl_devicetypecmcc"
            (
            "DeviceTypeID" INT NOT NULL,
            "DeviceTypeName" VARCHAR(128),
            "Description" TEXT,
            NOT CLUSTER PRIMARY KEY("DeviceTypeID"));

            CREATE TABLE "tbl_devicetypectcc"
            (
            "DeviceTypeCTCCId" int PRIMARY KEY AUTO_INCREMENT,
            "SUDeviceTypeId" VARCHAR(20),
            "SUDeviceTypeName" VARCHAR(50),
            "Description" VARCHAR(50),
            "UpdateTime" VARCHAR(50));

            CREATE TABLE "tbl_dodatacucc"
            (
            "SUID" VARCHAR(64) NOT NULL,
            "DeviceID" VARCHAR(64) NOT NULL,
            "StandardID" VARCHAR(64) NOT NULL,
            "SURID" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "StandardName" VARCHAR(255),
            "Description" TEXT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "DODataType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceID", "DODataType", "StandardID", "SUID"));

            CREATE TABLE "tbl_enumdatacucc"
            (
            "EntryId" INT NOT NULL,
            "AttributeType" INT,
            "AttributeName" VARCHAR(128),
            "AttributeDescription" TEXT,
            "EnumId" INT,
            "EnumType" VARCHAR(128),
            "EnumValue" INT,
            "EnumDefine" TEXT,
            "Description" TEXT,
            "ExtendField1" TEXT,
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "tbl_equipmentbasetypemapctcc"
            (
            "SUDeviceTypeId" INT NOT NULL,
            "SUDeviceTypeName" VARCHAR(50),
            "FirstEquipmentBaseTypeId" INT,
            "FirstEquipmentBaseTypeName" VARCHAR(50),
            "SecondEquipmentBaseTypeId" INT,
            "SecondEquipmentBaseTypeName" VARCHAR(50),
            "ThirdEquipmentBaseTypeId" INT,
            "ThirdEquipmentBaseTypeName" VARCHAR(50),
            "ExtendField1" VARCHAR(128),
            "ExtendField2" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("SUDeviceTypeId"));

            CREATE OR REPLACE  INDEX "TBL_EquipmentBaseTypeMapCTCC_IDX1" ON "tbl_equipmentbasetypemapctcc"("SUDeviceTypeId" ASC);

            CREATE TABLE "tbl_equipmentcmcc"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "DeviceID" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "FSUID" VARCHAR(255) DEFAULT '',
            "SiteID" VARCHAR(255) DEFAULT '',
            "SiteName" VARCHAR(255),
            "RoomID" VARCHAR(255),
            "RoomName" VARCHAR(255),
            "DeviceType" INT,
            "DeviceSubType" INT,
            "Model" VARCHAR(255),
            "Brand" VARCHAR(255),
            "RatedCapacity" DOUBLE,
            "Version" VARCHAR(20),
            "BeginRunTime" TIMESTAMP(0),
            "DevDescribe" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE OR REPLACE  INDEX "TBL_EquipmentCMCC_IDX1" ON "tbl_equipmentcmcc"("StationId" ASC,"EquipmentId" ASC);
            CREATE OR REPLACE  INDEX "TBL_EquipmentCMCC_IDX2" ON "tbl_equipmentcmcc"("FSUID" ASC,"DeviceID" ASC);

            CREATE TABLE "tbl_equipmentcucc"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "DeviceID" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUID" VARCHAR(255),
            "DeviceVender" VARCHAR(255),
            "DeviceType" VARCHAR(255),
            "MFD" VARCHAR(255),
            "ControllerType" VARCHAR(255),
            "SoftwareVersion" VARCHAR(255),
            "BatchNo" VARCHAR(255),
            "Password" VARCHAR(512),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE OR REPLACE  INDEX "IDX_EquipmentCUCC_SU_DeviceId" ON "tbl_equipmentcucc"("SUID" ASC,"DeviceID" ASC);
            CREATE OR REPLACE  INDEX "TBL_EquipmentCUCC_IDX1" ON "tbl_equipmentcucc"("StationId" ASC,"EquipmentId" ASC);

            CREATE TABLE "tbl_equipmentfsucucc"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "DeviceID" VARCHAR(255),
            "NamePrefix" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "FSUID" VARCHAR(255),
            "SiteID" VARCHAR(255),
            "SiteName" VARCHAR(255),
            "RoomID" VARCHAR(255),
            "RoomName" VARCHAR(255),
            "Model" VARCHAR(255),
            "Brand" VARCHAR(255),
            "RatedCapacity" REAL,
            "Version" VARCHAR(20),
            "BeginRunTime" TIMESTAMP(0),
            "DevDescribe" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "EquipmentCategory" INT,
            "ChildCategory" INT,
            "AssetsID" VARCHAR(255),
            "DeviceType" VARCHAR(50),
            "DeviceSubType" VARCHAR(50),
            "MFD" VARCHAR(50),
            "DeviceVender" VARCHAR(50),
            "SoftwareVersion" VARCHAR(50),
            "ControllerType" VARCHAR(50),
            "BatchNo" VARCHAR(50),
            "EquipmentMode" VARCHAR(50),
            NOT CLUSTER PRIMARY KEY("StationId", "EquipmentId"));

            CREATE OR REPLACE  INDEX "idxTBL_EquipmentFSUCUCCID" ON "tbl_equipmentfsucucc"("StationId" ASC,"EquipmentId" ASC);

            CREATE TABLE "tbl_equipmentgdctcc"
            (
            "SUID" VARCHAR(40) NOT NULL,
            "DeviceID" VARCHAR(40) NOT NULL,
            "DeviceName" VARCHAR(128),
            "EquipmentId" VARCHAR(128),
            "DeviceHLType" INT,
            "ExtendField1" VARCHAR(128),
            "ExtendField2" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("DeviceID", "SUID"));

            CREATE OR REPLACE  INDEX "TBL_EquipmentGDCTCC_IDX1" ON "tbl_equipmentgdctcc"("SUID" ASC,"DeviceID" ASC);

            CREATE TABLE "tbl_equipmentktctcc"
            (
            "EquipmentktCTCCId" int PRIMARY KEY AUTO_INCREMENT,
            "StationKTName" VARCHAR(128) NOT NULL,
            "StationKTId" VARCHAR(40) NOT NULL,
            "RoomKTName" VARCHAR(128) NOT NULL,
            "RoomKTId" VARCHAR(40) NOT NULL,
            "SystemName" VARCHAR(128) NOT NULL,
            "EquipmentName" VARCHAR(128) NOT NULL,
            "EquipmentState" VARCHAR(40),
            "EquipmentTypeName" VARCHAR(40),
            "SUID" VARCHAR(40),
            "IsAdded" INT,
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255));

            CREATE TABLE "tbl_eventconditionctcc"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "Description" VARCHAR(50),
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId", "EventId", "EventConditionId"));

            CREATE TABLE "tbl_fsuconfig"
            (
            "FSUID" VARCHAR(255) NOT NULL,
            "NeedSyncFlag" BIT,
            "FSUSendTime" TIMESTAMP(0),
            "FSUSendCfgCode" VARCHAR(32),
            "FSUSendCfgContent" CLOB,
            "LastFSUSendCfgContent" CLOB,
            "FSUSendCount" INT DEFAULT 0,
            "SCSyncCfgTime" TIMESTAMP(0),
            "SCSyncCfgCode" VARCHAR(32),
            "SCSyncCfgContent" CLOB,
            "LastSCSyncCfgContent" CLOB,
            "SCSyncCount" INT DEFAULT 0,
            NOT CLUSTER PRIMARY KEY("FSUID"));

            CREATE TABLE "tbl_fsuconfigimportlog"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "FSUID" VARCHAR(255) NOT NULL,
            "CfgType" VARCHAR(255) NOT NULL,
            "CfgContent" CLOB NOT NULL,
            "SaveTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE TABLE "tbl_fsuconfigsendbyfsu"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "FSUID" VARCHAR(255) NOT NULL,
            "FSUSendTime" TIMESTAMP(0) NOT NULL,
            "FSUSendCfgCode" VARCHAR(32) NOT NULL,
            "FSUSendCfgContent" CLOB NOT NULL,
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE OR REPLACE  INDEX "TBL_FSUConfigSendByFSU_IDX1" ON "tbl_fsuconfigsendbyfsu"("FSUID" ASC,"FSUSendTime" ASC);

            <!-- RowId是达梦数据库关键字，这里暂且注释掉该表 -->
<!--            CREATE TABLE "tbl_fsucuccpresetequip" (-->
<!--            "RowId" INT AUTO_INCREMENT NOT NULL,-->
<!--            "CuccTypeId" VARCHAR(3) NOT NULL,-->
<!--            "CuccClassName" VARCHAR(255),-->
<!--            "CuccTypeName" VARCHAR(255),-->
<!--            "EquipmentTemplateId" INT NOT NULL,-->
<!--            "EquipmentTemplateName" VARCHAR(255),-->
<!--            "Memo" VARCHAR(255),-->
<!--            "UpdateTime" TIMESTAMP(0),-->
<!--            "Ex1" VARCHAR(255),-->
<!--            "Ex2" VARCHAR(255),-->
<!--            "Ex3" VARCHAR(255),-->
<!--            "Ex4" VARCHAR(255),-->
<!--            "ExX" CLOB);-->

<!--            CREATE OR REPLACE  INDEX "idx_TBL_FsuCuccPresetEquip" ON "tbl_fsucuccpresetequip"("CuccTypeId" ASC);-->

            CREATE TABLE "tbl_fsuftphisdatalog"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "FSUID" VARCHAR(255) NOT NULL,
            "FileName" VARCHAR(255) NOT NULL,
            "FileSize" INT NOT NULL,
            "SaveTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE OR REPLACE  INDEX "TBL_FsuFtpHisDataLog_IDX1" ON "tbl_fsuftphisdatalog"("FSUID" ASC,"FileName" ASC);

            CREATE TABLE "tbl_fsutsignalcmcc"
            (
            "FSUID" VARCHAR(20) NOT NULL,
            "DeviceID" VARCHAR(26) NOT NULL,
            "ID" VARCHAR(20) NOT NULL,
            "SignalNumber" VARCHAR(5) NOT NULL,
            "Type" INT,
            "SignalName" VARCHAR(80),
            "AlarmLevel" INT,
            "Threshold" DOUBLE,
            "AbsoluteVal" DOUBLE,
            "RelativeVal" DOUBLE,
            "Describe" VARCHAR(120),
            "NMAlarmID" VARCHAR(40),
            "TSignalId" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0),
            "EI1" INT,
            "ES1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("DeviceID", "FSUID", "TSignalId"));

            CREATE OR REPLACE  INDEX "TBL_FsuTSignalCMCC_IDX1" ON "tbl_fsutsignalcmcc"("FSUID" ASC);

            CREATE TABLE "tbl_historybinterfacelog"
            (
            "ID" INT IDENTITY(1, 1) NOT NULL PRIMARY KEY,
            "FSUID" VARCHAR(255) NOT NULL,
            "MonitorUnitId" VARCHAR(255) NOT NULL,
            "DataServerIp" VARCHAR(255) NOT NULL,
            "RequestMessageType" VARCHAR(128) NOT NULL,
            "ResponseMessageType" VARCHAR(128),
            "CommunicationResult" BIT,
            "ParseResult" BIT,
            "ResultCause" VARCHAR(255),
            "InsertTime" TIMESTAMP(0) NOT NULL,
            "XmlInvokeContent" CLOB,
            "XmlResultContent" CLOB,
            "ExtendField" VARCHAR(255));

            CREATE OR REPLACE  INDEX "ID" ON "tbl_historybinterfacelog"("ID" ASC);

            CREATE TABLE "tbl_historymessagecmcc" (
            "UserId" INT,
            "SerialNo" DECIMAL(14,0) NOT NULL,
            "SiteID" VARCHAR(20) NOT NULL,
            "FSUID" VARCHAR(20) NOT NULL,
            "DeviceID" VARCHAR(20) NOT NULL,
            "SiteName" TEXT,
            "FSUName" TEXT,
            "DeviceName" TEXT,
            "MessageType" INT NOT NULL,
            "MessageName" VARCHAR(64),
            "MessageParm" TEXT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Result" INT,
            "Try" INT,
            "FailureCause" TEXT NOT NULL,
            "IsConfirm" BIT DEFAULT 0 NOT NULL,
            "ControlType" INT,
            "Description" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY ("DeviceID","FSUID","MessageType","SerialNo","SiteID"));

            CREATE TABLE "tbl_historymessagecucc" (
            "UserId" INT,
            "SerialNo" DECIMAL(14,0) NOT NULL,
            "SUID" VARCHAR(255) NOT NULL,
            "DeviceID" VARCHAR(255) NOT NULL,
            "SURID" VARCHAR(255),
            "DeviceRID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "DeviceName" VARCHAR(255),
            "MessageType" INT NOT NULL,
            "MessageName" VARCHAR(64),
            "MessageParm" TEXT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Result" INT,
            "Try" INT,
            "FailureCause" TEXT NOT NULL,
            "IsConfirm" BIT DEFAULT 0 NOT NULL,
            "ControlType" INT,
            "Description" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY ("MessageType","SerialNo"));

            CREATE TABLE "tbl_roomcmcc"
            (
            "StationId" INT NOT NULL,
            "HouseId" INT NOT NULL,
            "RoomID" VARCHAR(125),
            "RoomName" VARCHAR(255),
            "SiteID" VARCHAR(255),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("HouseId", "StationId"));

            CREATE OR REPLACE  INDEX "TBL_RoomCMCC_IDX1" ON "tbl_roomcmcc"("StationId" ASC,"HouseId" ASC);

            CREATE TABLE "tbl_roomfsucucc"
            (
            "StationId" INT NOT NULL,
            "HouseId" INT NOT NULL,
            "RoomID" VARCHAR(125),
            "RoomName" VARCHAR(255),
            "SiteID" VARCHAR(255),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("StationId", "HouseId"));

            CREATE TABLE "tbl_roomktctcc"
            (
            "RoomKTName" VARCHAR(128) NOT NULL,
            "RoomKTId" VARCHAR(40) NOT NULL,
            "StationKTName" VARCHAR(128) NOT NULL,
            "StationKTId" VARCHAR(40) NOT NULL,
            "FriendlyRoomName" VARCHAR(128),
            "IsGenerated" INT,
            "IsAdded" INT,
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("RoomKTId", "StationKTId"));

            CREATE OR REPLACE  INDEX "TBL_RoomKTCTCC_IDX1" ON "tbl_roomktctcc"("StationKTId" ASC,"RoomKTId" ASC);

            CREATE TABLE "tbl_standarddicsigctcc"
            (
            "SID" REAL,
            "StdSPDicName" VARCHAR(255),
            "Version" REAL,
            "SUDeviceTypeId2" REAL,
            "DeviceHLTyp" REAL,
            "DeviceTypeName" VARCHAR(255),
            "StandardDicId4" REAL,
            "StandardName" VARCHAR(255),
            "SPType" VARCHAR(255),
            "AlarmMeanings" VARCHAR(255),
            "NormalMeanings" VARCHAR(255),
            "Unit" VARCHAR(255),
            "Xmlcolumn" VARCHAR(255),
            "SerinoCom" VARCHAR(255),
            "SignalMeanings" VARCHAR(255),
            "update" TIMESTAMP(0),
            "SystemType" VARCHAR(255),
            "remark" VARCHAR(255),
            "OptionID" REAL,
            "AlarmLevel" REAL,
            "AlarmThresbhold" REAL,
            "StartDelay" REAL,
            "EndDelay" REAL,
            "Period" REAL,
            "AbsoluteVal" VARCHAR(255),
            "RelativeVal" VARCHAR(255),
            "Hysteresis" REAL,
            "AlarmLevel1" REAL,
            "AlarmThresbhold1" REAL,
            "StartDelay1" REAL,
            "EndDelay1" REAL,
            "Period1" REAL,
            "AbsoluteVal1" VARCHAR(255),
            "RelativeVal1" VARCHAR(255),
            "Hysteresis1" REAL,
            "AlarmLevel2" REAL,
            "AlarmThresbhold2" REAL,
            "StartDelay2" REAL,
            "EndDelay2" REAL,
            "Period2" REAL,
            "AbsoluteVal2" VARCHAR(255),
            "RelativeVal2" VARCHAR(255),
            "Hysteresis2" REAL,
            "AlarmLevel3" REAL,
            "AlarmThresbhold3" REAL,
            "StartDelay3" REAL,
            "EndDelay3" REAL,
            "Period3" REAL,
            "AbsoluteVal3" VARCHAR(255),
            "RelativeVal3" VARCHAR(255),
            "Hysteresis3" REAL,
            "AlarmLevel4" REAL,
            "AlarmThresbhold4" REAL,
            "StartDelay4" REAL,
            "EndDelay4" REAL,
            "Period4" REAL,
            "AbsoluteVal4" VARCHAR(255),
            "RelativeVal4" VARCHAR(255),
            "Hysteresis4" VARCHAR(255),
            "AlarmLevel5" REAL,
            "AlarmThresbhold5" REAL,
            "StartDelay5" REAL,
            "EndDelay5" REAL,
            "Period5" REAL,
            "AbsoluteVal5" VARCHAR(255),
            "RelativeVal5" VARCHAR(255),
            "Hysteresis5" VARCHAR(255),
            "AlarmLevel6" REAL,
            "AlarmThresbhold6" REAL,
            "StartDelay6" REAL,
            "EndDelay6" REAL,
            "Period6" REAL,
            "AbsoluteVal6" REAL,
            "RelativeVal6" REAL,
            "Hysteresis6" VARCHAR(255),
            "AlarmLevel7" REAL,
            "AlarmThresbhold7" VARCHAR(255),
            "StartDelay7" REAL,
            "EndDelay7" REAL,
            "Period7" REAL,
            "AbsoluteVal7" VARCHAR(255),
            "RelativeVal7" VARCHAR(255),
            "Hysteresis7" VARCHAR(255),
            "AlarmLevel8" REAL,
            "AlarmThresbhold8" REAL,
            "StartDelay8" REAL,
            "EndDelay8" REAL,
            "Period8" REAL,
            "AbsoluteVal8" VARCHAR(255),
            "RelativeVal8" VARCHAR(255),
            "Hysteresis8" VARCHAR(255),
            "AlarmLevel9" REAL,
            "AlarmThresbhold9" REAL,
            "StartDelay9" REAL,
            "EndDelay9" REAL,
            "Period9" REAL,
            "AbsoluteVal9" VARCHAR(255),
            "RelativeVal9" VARCHAR(255),
            "Hysteresis9" VARCHAR(255),
            "InputType" VARCHAR(255),
            "comfrom" VARCHAR(255),
            "StandardDicId" VARCHAR(20) NOT NULL,
            "SUDeviceTypeId" VARCHAR(10),
            "DeviceTypeIdCTCC" VARCHAR(10),
            NOT CLUSTER PRIMARY KEY("StandardDicId"));

            CREATE TABLE "tbl_stationcmcc"
            (
            "StationId" INT NOT NULL,
            "SiteID" VARCHAR(255),
            "SiteName" VARCHAR(255),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("StationId"));

            CREATE OR REPLACE  INDEX "TBL_StationCMCC_IDX1" ON "tbl_stationcmcc"("StationId" ASC);

            CREATE TABLE "tbl_stationktctcc"
            (
            "StationKTName" VARCHAR(128) NOT NULL,
            "StationKTId" VARCHAR(40) NOT NULL,
            "FriendlyStationName" VARCHAR(128),
            "BigRegionName" VARCHAR(40),
            "SmallRegionName" VARCHAR(40),
            "StationType" VARCHAR(40),
            "StationLevel" VARCHAR(40),
            "AMSStationName" VARCHAR(128),
            "AMSStationId" INT,
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("StationKTId"));

            CREATE OR REPLACE  INDEX "TBL_TBL_StationKTCTCC_IDX1" ON "tbl_stationktctcc"("StationKTId" ASC);

            CREATE TABLE "tbl_storagerulecmcc"
            (
            "SiteID" VARCHAR(125) NOT NULL,
            "FSUID" VARCHAR(125) NOT NULL,
            "DeviceID" VARCHAR(125) NOT NULL,
            "ID" VARCHAR(20) NOT NULL,
            "SignalNumber" VARCHAR(10) NOT NULL,
            "Type" INT,
            "AbsoluteVal" DOUBLE,
            "RelativeVal" DOUBLE,
            "StorageInterval" DECIMAL(12,0),
            "StorageRefTime" VARCHAR(80),
            "StorageRuleType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceID", "FSUID", "ID", "SignalNumber", "SiteID", "StorageRuleType"));

            CREATE TABLE "tbl_suportcucc"
            (
            "SUID" VARCHAR(255) NOT NULL,
            "SURID" VARCHAR(255),
            "PortNo" VARCHAR(128) NOT NULL,
            "PortName" VARCHAR(128),
            "PortType" VARCHAR(128),
            "Settings" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("PortNo", "SUID"));

            CREATE TABLE "tbl_suportdevicecucc"
            (
            "SUID" VARCHAR(255) NOT NULL,
            "SURID" VARCHAR(255),
            "PortNo" VARCHAR(128) NOT NULL,
            "DeviceID" VARCHAR(255) NOT NULL,
            "DeviceRID" VARCHAR(255),
            "Address" VARCHAR(255),
            "Protocol" VARCHAR(255),
            "Version" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("SUID","DeviceID"));

            CREATE TABLE "tbl_systemktctcc"
            (
            "SystemktCTCCId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "StationKTName" VARCHAR(128) NOT NULL,
            "StationKTId" VARCHAR(40) NOT NULL,
            "SystemName" VARCHAR(128) NOT NULL,
            "SystemState" VARCHAR(40),
            "SystemTypeName" VARCHAR(40),
            "IsAdded" INT,
            "RoomKTName" VARCHAR(128),
            "RoomKTId" VARCHAR(40),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255));

            CREATE TABLE "tbl_thresholdcmcc"
            (
            "SiteID" VARCHAR(125) NOT NULL,
            "FSUID" VARCHAR(125) NOT NULL,
            "DeviceID" VARCHAR(125) NOT NULL,
            "ID" VARCHAR(20) NOT NULL,
            "SignalNumber" VARCHAR(10) NOT NULL,
            "Type" INT,
            "Threshold" DOUBLE,
            "AlarmLevel" INT NOT NULL,
            "NMAlarmID" VARCHAR(40),
            "ThresholdType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceID", "FSUID", "ID", "SignalNumber", "SiteID", "ThresholdType"));

            CREATE TABLE "tbl_tsignalcmcc"
            (
            "FSUID" VARCHAR(20) NOT NULL,
            "DeviceID" VARCHAR(26) NOT NULL,
            "ID" VARCHAR(20) NOT NULL,
            "SignalNumber" VARCHAR(5) NOT NULL,
            "Type" INT,
            "SignalName" VARCHAR(80),
            "AlarmLevel" INT,
            "Threshold" DOUBLE,
            "AbsoluteVal" DOUBLE,
            "RelativeVal" DOUBLE,
            "Describe" VARCHAR(120),
            "NMAlarmID" VARCHAR(40),
            "EI1" INT,
            "ES1" VARCHAR(255),
            "TSignalType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceID", "FSUID", "ID", "SignalNumber", "TSignalType"));

            CREATE TABLE "tbl_tsignalgdctcc"
            (
            "SUID" VARCHAR(40) NOT NULL,
            "DeviceID" VARCHAR(40) NOT NULL,
            "SPID" VARCHAR(40) NOT NULL,
            "SPName" VARCHAR(128),
            "SPType" INT,
            "OptionID" INT,
            "Unit" VARCHAR(20),
            "NormalMeanings" VARCHAR(128),
            "AlarmMeanings" VARCHAR(128),
            "ExtendField1" VARCHAR(128),
            "ExtendField2" VARCHAR(128),
            "EquipmentId" VARCHAR(20),
            "SignalId" VARCHAR(20),
            "ConditionId" VARCHAR(20),
            NOT CLUSTER PRIMARY KEY("DeviceID", "SPID", "SUID"));

            CREATE OR REPLACE  INDEX "TBL_TSignalGDCTCC_IDX1" ON "tbl_tsignalgdctcc"("SUID" ASC,"DeviceID" ASC,"SPID" ASC);

            CREATE TABLE "tsl_monitorunitcmcc"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "FSUID" VARCHAR(255) DEFAULT '',
            "FSUName" VARCHAR(255),
            "SiteID" VARCHAR(255) DEFAULT '',
            "SiteName" VARCHAR(255),
            "RoomID" VARCHAR(255),
            "RoomName" VARCHAR(255),
            "UserName" VARCHAR(40),
            "PassWord" VARCHAR(40),
            "FSUIP" VARCHAR(255),
            "FSUMAC" VARCHAR(20),
            "FSUVER" VARCHAR(20),
            "Result" INT,
            "FailureCause" VARCHAR(255),
            "CPUUsage" DOUBLE,
            "MEMUsage" DOUBLE,
            "HardDiskUsage" DOUBLE,
            "GetFSUInfoResult" INT,
            "GetFSUFaliureCause" VARCHAR(255),
            "GetFSUTime" TIMESTAMP(0),
            "FTPUserName" VARCHAR(40),
            "FTPPassWord" VARCHAR(40),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "GetConfigFlag" INT DEFAULT 0,
            NOT CLUSTER PRIMARY KEY("MonitorUnitId", "StationId"));

            CREATE OR REPLACE  INDEX "IDX_MonitorUnitCMCC_1" ON "tsl_monitorunitcmcc"("FSUID" ASC);
            CREATE OR REPLACE  INDEX "TSL_MonitorUnitCMCC_IDX1" ON "tsl_monitorunitcmcc"("StationId" ASC,"MonitorUnitId" ASC);

            CREATE TABLE "tsl_monitorunitctcc"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "SUID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "UserName" VARCHAR(40),
            "PassWord" VARCHAR(40),
            "SUIP" VARCHAR(255),
            "SUPort" INT,
            "SUVendor" VARCHAR(255),
            "SUModel" VARCHAR(255),
            "SUHardVer" VARCHAR(20),
            "SUSoftVer" VARCHAR(20),
            "SCIP" VARCHAR(255),
            "SCPort" INT,
            "WhiteIps" VARCHAR(255),
            "Result" INT,
            "FailureCause" VARCHAR(255),
            "SUFtpUserName" VARCHAR(40),
            "SUFtpPassWord" VARCHAR(40),
            "SUFtpPort" INT,
            "CPUUsage" REAL,
            "MEMUsage" REAL,
            "SUDateTime" TIMESTAMP(0),
            "HardDiskUsage" REAL,
            "GetSUInfoResult" INT,
            "GetSUFaliureCause" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "GetConfigFlag" INT DEFAULT 0,
            NOT CLUSTER PRIMARY KEY("StationId", "MonitorUnitId"));

            CREATE OR REPLACE  INDEX "TSL_MonitorUnitCTCC_IDX1" ON "tsl_monitorunitctcc"("StationId" ASC,"MonitorUnitId" ASC);

            CREATE TABLE "tsl_monitorunitcucc"
            (
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "SUID" VARCHAR(255),
            "SUName" VARCHAR(255),
            "SURID" VARCHAR(255),
            "UserName" VARCHAR(40),
            "PassWord" VARCHAR(40),
            "SUIP" VARCHAR(255),
            "SUVER" VARCHAR(20),
            "SUPort" VARCHAR(20),
            "SUVendor" VARCHAR(20),
            "SUModel" VARCHAR(20),
            "SUHardVER" VARCHAR(20),
            "Longitude" DOUBLE,
            "Latitude" DOUBLE,
            "Result" INT,
            "FailureCause" VARCHAR(255),
            "CPUUsage" DOUBLE,
            "MEMUsage" DOUBLE,
            "GetSUInfoResult" INT,
            "GetSUTime" TIMESTAMP(0),
            "FTPUserName" VARCHAR(40),
            "FTPPassWord" VARCHAR(40),
            "ConfigState" INT,
            "RegisterTime" TIMESTAMP(0),
            "SUConfigTime" VARCHAR(255),
            "CenterConfigTime" VARCHAR(255),
            "Devices" TEXT,
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MonitorUnitId", "StationId"));

            CREATE OR REPLACE  INDEX "TSL_MonitorUnitCUCC_IDX1" ON "tsl_monitorunitcucc"("StationId" ASC,"MonitorUnitId" ASC);

            CREATE TABLE "tsl_monitorunitgdctcc" (
            "SUID" VARCHAR(40) NOT NULL,
            "SUName" VARCHAR(128) NOT NULL,
            "MonitorUnitId" INT,
            "SerialNo" INT,
            "ReqFactoryCfg" INT DEFAULT 0 NOT NULL,
            "ExtendField1" VARCHAR(128),
            "ExtendField2" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY ("SUID"));
        </sql>
    </changeSet>
</databaseChangeLog>