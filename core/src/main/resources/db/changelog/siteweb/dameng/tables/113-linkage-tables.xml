<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-113" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "linkconfig"
            (
            "LinkConfigId" INT AUTO_INCREMENT NOT NULL,
            "ConfigName" VARCHAR(128) NOT NULL,
            "UsedStatus" TINYINT NOT NULL,
            "LinkGroupId" INT NOT NULL,
            "LinkTriggerType" INT NOT NULL,
            "Cron" VARCHAR(100),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Layout" CLOB,
            "Description" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("LinkConfigId"));

            CREATE TABLE "linkconfigtemplate"
            (
            "LinkConfigTemplateId" INT AUTO_INCREMENT NOT NULL,
            "TemplateName" VARCHAR(128) NOT NULL,
            "Content" CLOB NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LinkConfigTemplateId"));

            CREATE TABLE "linkelement"
            (
            "ElementId" INT AUTO_INCREMENT NOT NULL,
            "ElementName" VARCHAR(128) NOT NULL,
            "ElementType" VARCHAR(64) NOT NULL,
            "InputNodesCount" INT NOT NULL,
            "OutputNodesCount" INT NOT NULL,
            "Icon" VARCHAR(64),
            "Expression" VARCHAR(500),
            "Visible" TINYINT NOT NULL,
            "SortIndex" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ElementId"));

            CREATE TABLE "linkelementconfig"
            (
            "LinkElementConfigId" INT AUTO_INCREMENT NOT NULL,
            "LinkConfigId" INT NOT NULL,
            "ElementId" INT NOT NULL,
            "Expression" VARCHAR(1000),
            "ExtendField1" VARCHAR(500),
            "ExtendField2" VARCHAR(500),
            "ExtendField3" VARCHAR(500),
            NOT CLUSTER PRIMARY KEY("LinkElementConfigId"));

            CREATE TABLE "linkgroup"
            (
            "GroupId" INT AUTO_INCREMENT NOT NULL,
            "GroupName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("GroupId"));

            CREATE TABLE "linkinstance"
            (
            "LinkInstanceId" INT AUTO_INCREMENT NOT NULL,
            "LinkConfigId" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0),
            "Status" INT NOT NULL,
            "StatusResult" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LinkInstanceId"));

            CREATE TABLE "linknode"
            (
            "NodeId" INT AUTO_INCREMENT NOT NULL,
            "LinkElementConfigId" INT NOT NULL,
            "NodeDirection" VARCHAR(10) NOT NULL,
            "NodeType" VARCHAR(64) NOT NULL,
            "NodeIndex" INT NOT NULL,
            "NodeTag" VARCHAR(255),
            "Expression" VARCHAR(500),
            NOT CLUSTER PRIMARY KEY("NodeId"));

            CREATE TABLE "linksegment"
            (
            "SegmentId" INT AUTO_INCREMENT NOT NULL,
            "InputLinkElementConfigId" INT NOT NULL,
            "InputNodeId" INT NOT NULL,
            "OutputLinkElementConfigId" INT NOT NULL,
            "OutputNodeId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SegmentId"));
        </sql>
    </changeSet>
</databaseChangeLog>