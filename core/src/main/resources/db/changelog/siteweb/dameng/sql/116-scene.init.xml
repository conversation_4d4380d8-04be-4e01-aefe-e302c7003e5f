<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-116" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            INSERT INTO "scene"("SceneId", "SceneName", "Checked") VALUES ('1', 'IDC', 1);
            INSERT INTO "scene"("SceneId", "SceneName", "Checked") VALUES ('2', '网点', 0);



            -- 场景和菜单方案
            INSERT INTO "scenemenuprofilemap"("SceneMenuProfileMapId", "SceneId", "MenuProfileId") VALUES(1, 1, 1);
            INSERT INTO "scenemenuprofilemap"("SceneMenuProfileMapId", "SceneId", "MenuProfileId") VALUES(2, 2, 2);
            INSERT INTO "scenemenuprofilemap"("SceneMenuProfileMapId", "SceneId", "MenuProfileId") VALUES(3, 1, 3);
            -- INSERT INTO "scenemenuprofilemap"("SceneMenuProfileMapId", "SceneId", "MenuProfileId") VALUES(4, 1, 4);
            INSERT INTO "scenemenuprofilemap"("SceneMenuProfileMapId", "SceneId", "MenuProfileId") VALUES(5, 1, 5);

        </sql>
    </changeSet>
</databaseChangeLog>