<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-011" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "wo_diccmdchecklist"
            (
            "CheckId" INT AUTO_INCREMENT NOT NULL,
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "CheckType" VARCHAR(10),
            "IsMust" VARCHAR(10),
            "Note" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CheckId"));

            CREATE TABLE "wo_diceventcategorymap"
            (
            "DicEventCategoryMapId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "BaseTypeId" DECIMAL(10,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "CheckType" VARCHAR(10),
            "IsMust" VARCHAR(10),
            "SiteWebEventCategory" VARCHAR(255),
            "SiteWebEventCategoryId" INT);

            CREATE TABLE "wo_diceventchecklist"
            (
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "CheckType" VARCHAR(10),
            "IsMust" VARCHAR(10),
            "Note" VARCHAR(255),
            "CheckId" INT AUTO_INCREMENT NOT NULL,
            NOT CLUSTER PRIMARY KEY("CheckId"));

            CREATE TABLE "wo_dicsigchecklist"
            (
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "CheckType" VARCHAR(10),
            "LimitDown" DOUBLE,
            "LimitUp" DOUBLE,
            "Note" VARCHAR(255),
            "CheckId" INT AUTO_INCREMENT NOT NULL,
            "IsMust" VARCHAR(10),
            NOT CLUSTER PRIMARY KEY("CheckId"));

            CREATE TABLE "wo_dicstationtypemap"
            (
            "WR_ItemId" INT NOT NULL,
            "SiteWeb_ItemId" INT,
            "SaveTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("WR_ItemId"));

            CREATE TABLE "wo_dicuserartselfcheck"
            (
            "CheckDicId" INT NOT NULL,
            "CheckDicNote" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY("CheckDicId"));

            CREATE TABLE "wo_dicuserexpertcheck"
            (
            "CheckDicId" INT NOT NULL,
            "CheckDicNote" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY("CheckDicId"));

            CREATE TABLE "wo_fileuploadrec"
            (
            "FId" INT AUTO_INCREMENT NOT NULL,
            "OrderId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "FType" VARCHAR(255),
            "FSaveName" VARCHAR(255) NOT NULL,
            "Uri" TEXT NOT NULL,
            "FOriginalName" VARCHAR(255) NOT NULL,
            "FSize" INT NOT NULL,
            "UserId" INT NOT NULL,
            "UploadTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("FId"));

            CREATE TABLE "wo_installclerk"
            (
            "ClerkId" INT AUTO_INCREMENT NOT NULL,
            "CompanyId" INT NOT NULL,
            "ClerkName" VARCHAR(255) NOT NULL,
            NOT CLUSTER PRIMARY KEY("ClerkId"));

            CREATE TABLE "wo_installcompany"
            (
            "CompanyId" INT AUTO_INCREMENT NOT NULL,
            "CompanyName" VARCHAR(255) NOT NULL,
            NOT CLUSTER PRIMARY KEY("CompanyId"));

            CREATE TABLE "wo_sysconfig"
            (
            "SysConfigId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "ConfigId" INT,
            "ConfigName" VARCHAR(255),
            "ValueInt" INT,
            "ValueString" VARCHAR(255));

            CREATE TABLE "wo_testorder"
            (
            "OrderId" INT AUTO_INCREMENT NOT NULL,
            "OrderState" INT DEFAULT 1 NOT NULL,
            "NeedUpload" INT DEFAULT 0 NOT NULL,
            "MyOrderId" VARCHAR(255) NOT NULL,
            "OrderType" INT NOT NULL,
            "StationId" INT NOT NULL,
            "Latitude" DECIMAL(22,17) NOT NULL,
            "Longitude" DECIMAL(22,17) NOT NULL,
            "EquipItems" TEXT,
            "InstallCompanyId" INT,
            "InstallClerkId" INT,
            "InstallCompany" VARCHAR(255),
            "InstallClerk" VARCHAR(255),
            "ApplyUserId" INT NOT NULL,
            "ApplyUserName" VARCHAR(255) NOT NULL,
            "ApplyUserFsuVendor" VARCHAR(255) DEFAULT '' NOT NULL,
            "ApplyTime" TIMESTAMP(0) NOT NULL,
            "StateChangeTime" TIMESTAMP(0) NOT NULL,
            "StateSetUserId" INT NOT NULL,
            "StateSetUserName" VARCHAR(255) NOT NULL,
            "SubmitTime" TIMESTAMP(0),
            "ExpertUserId" INT DEFAULT 0,
            "ExpertDecision" VARCHAR(255) DEFAULT '',
            "ExpertNote" VARCHAR(255) DEFAULT '',
            "ExpertIsApprove" INT DEFAULT 0,
            "FinalUserId" INT DEFAULT 0,
            "FinalGeneralReuslt" VARCHAR(255) DEFAULT '',
            "FinalDecision" VARCHAR(255) DEFAULT '',
            "FinalNote" VARCHAR(255) DEFAULT '',
            "FinalIsApprove" INT DEFAULT 0,
            "ApproveTime" TIMESTAMP(0),
            "LockUserId" INT,
            "LockTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("OrderId"));

            CREATE OR REPLACE  INDEX "idx_WO_TestOrder" ON "wo_testorder"("OrderId" ASC,"OrderState" ASC);

            CREATE TABLE "wo_testorderartselfchecklist"
            (
            "OrderCheckId" INT AUTO_INCREMENT NOT NULL,
            "OrderId" INT NOT NULL,
            "CheckDicId" INT NOT NULL,
            "CheckDicNote" TEXT NOT NULL,
            "IsPass" INT DEFAULT 0 NOT NULL,
            NOT CLUSTER PRIMARY KEY("OrderCheckId"));

            CREATE OR REPLACE  INDEX "idx_WO_TestOrderArtSelfCheckList" ON "wo_testorderartselfchecklist"("OrderId" ASC);

            CREATE TABLE "wo_testorderequipitem"
            (
            "OrderEquipId" INT AUTO_INCREMENT NOT NULL,
            "OrderId" INT,
            "EquipmentId" INT,
            "BaseEquipmentID" INT,
            "BaseEquipmentName" VARCHAR(128),
            "UriProtocol" TEXT,
            "UriImage" TEXT,
            "SaveTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("OrderEquipId"),
            CONSTRAINT "idx_WO_TestOrderEquipItemon" UNIQUE("OrderId", "EquipmentId"));

            CREATE TABLE "wo_testorderequipitemchecklist" (
            "OrderCheckId" INT AUTO_INCREMENT NOT NULL,
            "OrderId" INT NOT NULL,
            "CheckTypeId" INT,
            "CheckType" VARCHAR(10),
            "HasConfig" BIT DEFAULT 1,
            "IsPass" INT DEFAULT 0 NOT NULL,
            "PassNote" VARCHAR(255),
            "PassFailReason" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128),
            "EquipmentCategoryName" VARCHAR(128),
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "Unit" VARCHAR(255),
            "LimitDown" DOUBLE,
            "LimitUp" DOUBLE,
            "Note" VARCHAR(255),
            "CheckId" INT,
            "EquipmentLogicClass" VARCHAR(128),
            "LogicClass" VARCHAR(128),
            "StandardName" VARCHAR(255),
            "SignalType" VARCHAR(255),
            "SamplerValue" VARCHAR(128),
            "SamplerTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("OrderCheckId"),
            CHECK("CheckTypeId" >= 0));

            CREATE OR REPLACE  INDEX "idx_WO_TestOrderEquipItemCheckList" ON "wo_testorderequipitemchecklist"("OrderId" ASC,"CheckTypeId" ASC,"IsPass" ASC);

            CREATE TABLE "wo_testorderequipitemchecklisthis" (
            "OrderCheckId" INT NOT NULL,
            "OrderId" INT NOT NULL,
            "CheckTypeId" INT NOT NULL,
            "CheckType" VARCHAR(10),
            "HasConfig" BIT DEFAULT 1,
            "IsPass" INT DEFAULT 0 NOT NULL,
            "PassNote" VARCHAR(255),
            "PassFailReason" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128),
            "EquipmentCategoryName" VARCHAR(128),
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "Unit" VARCHAR(255),
            "LimitDown" DOUBLE,
            "LimitUp" DOUBLE,
            "Note" VARCHAR(255),
            "CheckId" INT,
            "EquipmentLogicClass" VARCHAR(128),
            "LogicClass" VARCHAR(128),
            "StandardName" VARCHAR(255),
            "SignalType" VARCHAR(255),
            "SamplerValue" VARCHAR(128),
            "SamplerTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("OrderCheckId"),
            CHECK("CheckTypeId" >= 0));

            CREATE OR REPLACE  INDEX "idx_WO_TestOrderEquipItemCheckListHis" ON "wo_testorderequipitemchecklisthis"("OrderId" ASC,"CheckTypeId" ASC,"IsPass" ASC);

            CREATE TABLE "wo_testorderequipitemchecklistsignal" (
            "TestOrderEquipitemCheckListSignalId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "OrderCheckId" INT,
            "OrderId" INT,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "LimitDown" DOUBLE,
            "LimitUp" DOUBLE,
            "FloatValue" DOUBLE,
            "SamplerTime" TIMESTAMP(0),
            "BaseTypeId" DECIMAL(12,0),
            "IsPass" BIT DEFAULT 0);

            CREATE OR REPLACE  INDEX "idx_WO_TestOrderEquipItemCheckListSignal" ON "wo_testorderequipitemchecklistsignal"("OrderId" ASC);

            CREATE TABLE "wo_testorderexpertchecklist"
            (
            "OrderCheckId" INT AUTO_INCREMENT NOT NULL,
            "OrderId" INT NOT NULL,
            "CheckDicId" INT NOT NULL,
            "CheckDicNote" VARCHAR(255) NOT NULL,
            "IsPass" INT DEFAULT 1 NOT NULL,
            "PassNote" VARCHAR(255) DEFAULT '',
            NOT CLUSTER PRIMARY KEY("OrderCheckId"));

            CREATE OR REPLACE  INDEX "idx_WO_TestOrderExpertCheckList" ON "wo_testorderexpertchecklist"("OrderId" ASC);

            CREATE TABLE "wo_testorderflow"
            (
            "OrderFlowId" INT AUTO_INCREMENT NOT NULL,
            "OrderId" INT NOT NULL,
            "StateSetUserName" VARCHAR(255) NOT NULL,
            "OldOrderState" INT NOT NULL,
            "IsApprove" INT NOT NULL,
            "SaveTime" TIMESTAMP(0) NOT NULL,
            "NewOrderState" INT NOT NULL,
            "StateSetUserId" INT NOT NULL,
            "Decision" VARCHAR(255),
            "Note" VARCHAR(255),
            "FlowText" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("OrderFlowId"));

            CREATE OR REPLACE  INDEX "idx_WO_TestOrderFlow" ON "wo_testorderflow"("OrderId" ASC);
        </sql>
    </changeSet>
</databaseChangeLog>