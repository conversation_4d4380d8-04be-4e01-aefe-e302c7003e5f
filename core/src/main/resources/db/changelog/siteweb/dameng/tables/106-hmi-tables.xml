<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-106" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "chartapi"
            (
            "ApiId" INT AUTO_INCREMENT NOT NULL,
            "ApiName" VARCHAR(128) NOT NULL,
            "Category" VARCHAR(45) NOT NULL,
            "Url" VARCHAR(1024) NOT NULL,
            "Method" VARCHAR(128) NOT NULL,
            "ParamSchema" VARCHAR(8188) NOT NULL,
            "Transform" VARCHAR(4096) NOT NULL,
            NOT CLUSTER PRIMARY KEY("ApiId"));

            CREATE TABLE "chartstyle"
            (
            "StyleId" INT AUTO_INCREMENT NOT NULL,
            "StyleName" VARCHAR(45) NOT NULL,
            "ChartId" INT NOT NULL,
            "Thumbnail" TEXT,
            "Expression" TEXT NOT NULL,
            NOT CLUSTER PRIMARY KEY("StyleId"),
            CONSTRAINT "ChartStyle_StyleName" UNIQUE("StyleName"));

            CREATE TABLE "charttheme"
            (
            "ThemeId" INT AUTO_INCREMENT NOT NULL,
            "themeName" VARCHAR(45) NOT NULL,
            "themeCode" VARCHAR(45) NOT NULL,
            "themeData" VARCHAR(8188) NOT NULL,
            "themeDefault" TINYINT,
            NOT CLUSTER PRIMARY KEY("ThemeId"),
            CONSTRAINT "ChartTheme_ThemeName" UNIQUE("themeCode"),
            CHECK("themeData" IS JSON ));

            CREATE TABLE "commonobject"
            (
            "CommonObjectId" INT AUTO_INCREMENT NOT NULL,
            "CommonObjectName" VARCHAR(255),
            "Description" VARCHAR(255),
            "Photo" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CommonObjectId")) AUTO_INCREMENT=10000;

            CREATE TABLE "compdatacolumn"
            (
            "CompDataColumnId" INT AUTO_INCREMENT NOT NULL,
            "CompDataTableId" INT,
            "ColumnName" VARCHAR(50),
            "ColumnField" VARCHAR(50),
            "ValueType" TINYINT,
            NOT CLUSTER PRIMARY KEY("CompDataColumnId"));

            CREATE TABLE "compdataset"
            (
            "CompDataSetId" INT AUTO_INCREMENT NOT NULL,
            "CompDataSetName" VARCHAR(50),
            "Url" VARCHAR(200),
            NOT CLUSTER PRIMARY KEY("CompDataSetId"));

            CREATE TABLE "compdatatable"
            (
            "CompDataTableId" INT AUTO_INCREMENT NOT NULL,
            "CompDataTableName" VARCHAR(50),
            "CompDataSetId" INT,
            NOT CLUSTER PRIMARY KEY("CompDataTableId"));

            CREATE TABLE "compskin"
            (
            "CompSkinId" INT AUTO_INCREMENT NOT NULL,
            "CompDataSetId" INT,
            "SkinName" VARCHAR(50),
            "Content" TEXT,
            NOT CLUSTER PRIMARY KEY("CompSkinId"));

            CREATE TABLE "graphicpage"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "Type" VARCHAR(255),
            "GroupId" INT,
            "PageCategory" INT,
            "BaseEquipmentId" INT,
            "ObjectId" INT,
            "Name" VARCHAR(255) NOT NULL,
            "AppendName" VARCHAR(255),
            "TemplateId" INT,
            "Data" TEXT,
            "Style" TEXT,
            "Children" CLOB,
            "Description" VARCHAR(255),
            "RouterType" VARCHAR(255),
            "CrumbsRouterType" VARCHAR(255),
            "PageCanFullScreen" TINYINT,
            "SceneId" INT,
            "IsDefault" TINYINT,
            "UpdateTime" datetime,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_GraphicPage_1" ON "graphicpage"("PageCategory" ASC,"ObjectId" ASC);

            CREATE TABLE "graphicpagetemplate"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "Type" VARCHAR(255),
            "GroupId" INT,
            "PageCategory" INT,
            "BaseEquipmentId" INT,
            "Internal" TINYINT,
            "Name" VARCHAR(255) NOT NULL,
            "AppendName" VARCHAR(255),
            "Data" TEXT,
            "Style" TEXT,
            "Children" CLOB,
            "TemplateCategory" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_GraphicPageTemplate_1" ON "graphicpagetemplate"("PageCategory" ASC,"BaseEquipmentId" ASC);

            CREATE TABLE "graphictemplate"
            (
            "GraphicTemplateId" INT AUTO_INCREMENT NOT NULL,
            "GraphicTemplateName" VARCHAR(128),
            "GraphicTemplateTag" VARCHAR(128),
            "GraphicTemplateCompType" VARCHAR(128),
            "GraphicTemplateTypeId" INT,
            "GraphicTemplateType" VARCHAR(128),
            "GraphicTemplateTypeName" VARCHAR(128),
            "GraphicTemplateCover" VARCHAR(128),
            "GraphicTemplateConfig" TEXT,
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("GraphicTemplateId"));

            CREATE TABLE "matrixchart"
            (
            "MatrixChartId" INT AUTO_INCREMENT NOT NULL,
            "Name" VARCHAR(50),
            "Path" VARCHAR(1024),
            "DataType" INT,
            NOT CLUSTER PRIMARY KEY("MatrixChartId"));

            CREATE TABLE "systemnavigation"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "Name" VARCHAR(50) NOT NULL,
            "BusinessId" VARCHAR(50),
            "PageId" INT,
            "SortValue" INT,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "usergraphicpagemap"
            (
            "UserGraphicPageMapId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT,
            "GraphicPageId" INT,
            "Config" TEXT,
            NOT CLUSTER PRIMARY KEY("UserGraphicPageMapId"));
        </sql>
    </changeSet>
</databaseChangeLog>