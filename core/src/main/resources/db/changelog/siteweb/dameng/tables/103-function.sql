CREATE OR REPLACE FUNCTION "FUCT_GETDEVICEPOSITION" (StructureId IN NUMBER) RETURN VARCHAR2 AS
            tempParentId VARCHAR2(128);
            tempParent VARCHAR2(255);
            tempId NUMBER;
            BEGIN
            SELECT REPLACE(levelofpath, '.', ','),
            resourcestructureId
            INTO tempParentId, tempId
            FROM resourcestructure
            WHERE resourcestructureId = StructureId;

            IF tempId = 0 THEN
            RETURN tempParent;
            END IF;
            -- 去除根节点的名称
            SELECT SUBSTR(tempParentId, INSTR(tempParentId, ',') + 1) INTO tempParentId;
            SELECT LISTAGG(c.resourcestructureName, '_') WITHIN GROUP (ORDER BY resourcestructureId)
            INTO tempParent
            FROM resourcestructure c
            WHERE c.resourcestructureId IN (
            SELECT CAST(REGEXP_SUBSTR(tempParentId,'[^,]+', 1, LEVEL) AS NUMBER)
            FROM dual
            CONNECT BY REGEXP_SUBSTR(tempParentId, '[^,]+', 1, LEVEL) IS NOT NULL
            );


            RETURN tempParent;
            END FUCT_GETDEVICEPOSITION;