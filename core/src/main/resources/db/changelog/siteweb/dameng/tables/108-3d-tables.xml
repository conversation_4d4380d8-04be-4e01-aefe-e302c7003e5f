<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-108" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "devicerefdimension"
            (
            "DeviceRefDimensionId" INT AUTO_INCREMENT NOT NULL,
            "ObjectBindData" INT NOT NULL,
            "type" VARCHAR(128) NOT NULL,
            "DimensionDesignerId" INT NOT NULL,
            "Relation" VARCHAR(128) NOT NULL,
            NOT CLUSTER PRIMARY KEY("DeviceRefDimensionId"));

            CREATE TABLE "dimensionconfigure"
            (
            "DimensionConfigureId" INT AUTO_INCREMENT NOT NULL,
            "DimensionConfigureType" INT,
            "DimensionConfigure" CLOB,
            "DimensionConfigureUuid" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("DimensionConfigureId"));

            CREATE TABLE "dimensiondesigner"
            (
            "DimensionDesignerId" INT AUTO_INCREMENT NOT NULL,
            "DimensionDesignerName" VARCHAR(128),
            "FilePath" VARCHAR(256),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("DimensionDesignerId"));

            CREATE TABLE "dimensionmodel"
            (
            "DimensionModelId" INT AUTO_INCREMENT NOT NULL,
            "DimensionModelCategory" INT,
            "DimensionModelName" VARCHAR(128),
            "DimensionModelFile" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("DimensionModelId"));

            CREATE TABLE "dimensionlinks" (
            "lineId" VARCHAR(64) NOT NULL,
            "displayName" VARCHAR(64) DEFAULT NULL,
            "lineType" INT DEFAULT NULL,
            "localObjectId" INT DEFAULT NULL,
            "localObjectType" INT DEFAULT NULL,
            "localObjectPort" VARCHAR(64) DEFAULT NULL,
            "localObjectModelPort" INT DEFAULT NULL,
            "remoteObjectId" INT DEFAULT NULL,
            "remoteObjectType" INT DEFAULT NULL,
            "remoteObjectPort" VARCHAR(64) DEFAULT NULL,
            "remoteObjectModelPort" INT DEFAULT NULL,
            "staticPropertys" VARCHAR(8188) DEFAULT NULL,
            "description" VARCHAR(128) DEFAULT NULL,
            NOT CLUSTER PRIMARY KEY("lineId")
            );

        </sql>
    </changeSet>
</databaseChangeLog>