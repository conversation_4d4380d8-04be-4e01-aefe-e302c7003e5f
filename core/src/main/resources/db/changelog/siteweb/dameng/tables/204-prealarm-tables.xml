<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-204" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "prealarm"
            (
            "PreAlarmId" INT AUTO_INCREMENT NOT NULL,
            "PreAlarmPointId" INT NOT NULL,
            "Meanings" VARCHAR(128),
            "PreAlarmSeverity" INT,
            "PreAlarmSeverityName" VARCHAR(128),
            "PreAlarmCategory" INT,
            "PreAlarmCategoryName" VARCHAR(128),
            "Color" VARCHAR(128),
            "UniqueId" VARCHAR(256),
            "UniqueName" VARCHAR(256),
            "ObjectId" INT,
            "ObjectTypeId" INT,
            "ObjectName" VARCHAR(128),
            "ResourceStructureId" INT,
            "LevelOfPath" VARCHAR(256),
            "LevelOfPathName" VARCHAR(256),
            "TriggerValue" VARCHAR(128),
            "Unit" VARCHAR(10),
            "SampleTime" TIMESTAMP(0),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmId" INT,
            "ConfirmName" VARCHAR(128),
            "Remark" VARCHAR(255),
            "BusinessTypeId" INT,
            NOT CLUSTER PRIMARY KEY("PreAlarmId"));

            CREATE TABLE "prealarmcategory"
            (
            "CategoryId" INT AUTO_INCREMENT NOT NULL,
            "CategoryName" VARCHAR(64) NOT NULL,
            "ParentCategoryId" INT,
            "Description" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("CategoryId"));

            CREATE TABLE "prealarmhistory"
            (
            "PreAlarmHistoryId" int PRIMARY KEY AUTO_INCREMENT,
            "PreAlarmId" INT NOT NULL,
            "PreAlarmPointId" INT NOT NULL,
            "Meanings" VARCHAR(128),
            "PreAlarmSeverity" INT,
            "PreAlarmSeverityName" VARCHAR(128),
            "PreAlarmCategory" INT,
            "PreAlarmCategoryName" VARCHAR(128),
            "Color" VARCHAR(128),
            "UniqueId" VARCHAR(256),
            "UniqueName" VARCHAR(256),
            "ObjectId" INT,
            "ObjectTypeId" INT,
            "ObjectName" VARCHAR(128),
            "ResourceStructureId" INT,
            "LevelOfPath" VARCHAR(256),
            "LevelOfPathName" VARCHAR(256),
            "TriggerValue" VARCHAR(128),
            "Unit" VARCHAR(10),
            "SampleTime" TIMESTAMP(0),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "ConfirmId" INT,
            "ConfirmName" VARCHAR(128),
            "Remark" VARCHAR(255),
            "BusinessTypeId" INT);

            CREATE TABLE "prealarmpoint"
            (
            "PreAlarmPointId" INT AUTO_INCREMENT NOT NULL,
            "PreAlarmPointName" VARCHAR(128),
            "Meanings" VARCHAR(128),
            "Expression" VARCHAR(256),
            "AbnormalExpression" VARCHAR(256),
            "ExecuteCron" VARCHAR(256),
            "PreAlarmCategory" INT,
            "UniqueId" VARCHAR(256),
            "ObjectId" INT,
            "ObjectTypeId" INT,
            "ObjectName" VARCHAR(128),
            "ResourceStructureId" INT,
            "LevelOfPath" VARCHAR(256),
            "LevelOfPathName" VARCHAR(256),
            "PreAlarmSeverity" INT,
            "Enable" INT,
            "Unit" VARCHAR(10),
            "MaskType" INT,
            "MaskDuration" VARCHAR(128),
            "MaskStartTime" TIMESTAMP(0),
            "MaskEndTime" TIMESTAMP(0),
            "Stateful" INT,
            "Modifier" INT,
            "ModifierName" VARCHAR(128),
            "ModifyTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("PreAlarmPointId"));

            CREATE TABLE "prealarmseverity"
            (
            "PreAlarmSeverityId" INT AUTO_INCREMENT NOT NULL,
            "PreAlarmSeverityName" VARCHAR(64) NOT NULL,
            "Color" VARCHAR(128),
            "Description" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("PreAlarmSeverityId"));

            CREATE TABLE "prealarmchange" (
            "SequenceId" varchar(128) NOT NULL,
            "SerialNo" BIGINT AUTO_INCREMENT NOT NULL,
            "OperationType" int NOT NULL,
            "PreAlarmId" INT NOT NULL,
            "PreAlarmName" varchar(128) NOT NULL,
            "Meanings" varchar(255) NOT NULL,
            "TriggerValue" varchar(255) NOT NULL,
            "PreAlarmPointId" INT NOT NULL,
            "PreAlarmCategory" int NOT NULL,
            "PreAlarmCategoryName" varchar(128)  ,
            "PreAlarmSeverity" int NOT NULL,
            "PreAlarmSeverityName" varchar(128)  ,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) ,
            "ConfirmTime" TIMESTAMP(0) ,
            "ConfirmorId" int  ,
            "ConfirmorName" varchar(128) ,
            "EquipmentId" int NOT NULL,
            "EquipmentName" varchar(128) NOT NULL,
            "EquipmentCategory" int NOT NULL,
            "EquipmentCategoryName" varchar(128) NOT NULL,
            "EquipmentVendor" varchar(128) ,
            "CenterId" int NOT NULL,
            "CenterName" varchar(128) ,
            "StructureId" int NOT NULL,
            "StructureName" varchar(128) ,
            "StationId" int NOT NULL,
            "StationName" varchar(128) ,
            "StationCategoryId" int NOT NULL,
            "ResourceStructureId" int NOT NULL,
            "LevelOfPathName" varchar(255)  ,
            "InsertTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY ("serialNo"));
            CREATE INDEX PREALARMCHANGEINDEX1 ON"prealarmchange" ("SequenceId");
        </sql>
    </changeSet>
</databaseChangeLog>