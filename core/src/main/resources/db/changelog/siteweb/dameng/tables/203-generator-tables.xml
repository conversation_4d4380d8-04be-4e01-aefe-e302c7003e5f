<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-203" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "gene_generatorelecunitprice"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentBaseType" INT,
            "Year" INT NOT NULL,
            "Month" INT NOT NULL,
            "YearMonth" VARCHAR(16) NOT NULL,
            "ElecUnitPrice" DOUBLE NOT NULL,
            "UpdaterId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"),
            CONSTRAINT "IDX_Gene_GeneratorElecUnitPrice_1" UNIQUE("EquipmentId", "Year", "Month"));

            CREATE TABLE "gene_generatorext"
            (
            "GeneId" INT NOT NULL,
            "RatedPower" DOUBLE,
            "RatedPowerConsumption" DOUBLE,
            "UnitFuelConsumption" DOUBLE,
            "DefaultElecUnitPrice" DOUBLE NOT NULL,
            "UpdaterId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("GeneId"));

            CREATE TABLE "gene_generatorhistorysignal"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "GeneId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "GeneIdAndSignalId" VARCHAR(255) NOT NULL,
            "SignalValue" DOUBLE,
            "InsertTime" TIMESTAMP(0) NOT NULL,
            "SignalValid" TINYINT NOT NULL,
            "PowerSerialId" INT,
            "MakeAlarm" TINYINT,
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"));

            CREATE OR REPLACE  INDEX "IDX_Gene_HistorySignal_1" ON "gene_generatorhistorysignal"("GeneIdAndSignalId" ASC);
            CREATE OR REPLACE  INDEX "IDX_Gene_HistorySignal_2" ON "gene_generatorhistorysignal"("GeneId" ASC);

            CREATE TABLE "gene_generatorsignalcheckinfo"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "GeneId" INT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255) NOT NULL,
            "DynamicOrStatic" TINYINT NOT NULL,
            "TriggerValue" DOUBLE,
            "Extend1" VARCHAR(255),
            "Extend2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"));

            CREATE OR REPLACE  INDEX "IDX_Gene_CheckSignal_1" ON "gene_generatorsignalcheckinfo"("GeneId" ASC);

            CREATE TABLE "gene_maintenancerecord"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "GeneId" INT NOT NULL,
            "GeneName" VARCHAR(256),
            "MaintenanceTime" TIMESTAMP(0) NOT NULL,
            "Position" VARCHAR(1024),
            "MaintenanceItem" VARCHAR(1024),
            "Maintainer" VARCHAR(128),
            "Confirmor" VARCHAR(128),
            "Remark" VARCHAR(1024),
            "UpdaterId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"));

            CREATE OR REPLACE  INDEX "IDX_Gene_MaintenanceRecord_1" ON "gene_maintenancerecord"("GeneId" ASC);

            CREATE TABLE "gene_oilboxext"
            (
            "OilId" INT NOT NULL,
            "RatedVolume" DOUBLE,
            "FullOilLevel" DOUBLE,
            "UpdaterId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("OilId"));

            CREATE TABLE "gene_oilboxgeneratormap"
            (
            "OilId" INT NOT NULL,
            "GeneId" INT NOT NULL,
            "UpdaterId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("OilId", "GeneId"));

            CREATE TABLE "gene_oillevelrecord"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "OilId" INT NOT NULL,
            "CurrentOilLevel" DOUBLE NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "UpdateUserId" INT NOT NULL,
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"));

            CREATE OR REPLACE  INDEX "IDX_Gene_OilLevel_1" ON "gene_oillevelrecord"("OilId" ASC);

            CREATE TABLE "gene_powergenerationrecord"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EquipmentBaseType" INT,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "PositiveElecEnergyStart" DOUBLE NOT NULL,
            "PositiveElecEnergyEnd" DOUBLE,
            "PowerGeneration" DOUBLE,
            "RunDuration" BIGINT,
            "PowerGenerationCost" DOUBLE,
            "PowerGenerationOil" DOUBLE,
            "PowerGenerationOilCost" DOUBLE,
            "StartInsertTime" TIMESTAMP(0),
            "EndInsertTime" TIMESTAMP(0),
            "StartSerialNo" BIGINT,
            "EndSerialNo" BIGINT,
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"));

            CREATE OR REPLACE  INDEX "IDX_PowerGenerationRecord_EquipmentId_1" ON "gene_powergenerationrecord"("EquipmentId" ASC);
            CREATE OR REPLACE  INDEX "IDX_PowerGenerationRecord_3Fields_1" ON "gene_powergenerationrecord"("EquipmentId" ASC,"EquipmentBaseType" ASC,"StartTime" ASC);

            CREATE TABLE "gene_refuelrecord"
            (
            "SerialId" INT AUTO_INCREMENT NOT NULL,
            "OilId" INT NOT NULL,
            "OilName" VARCHAR(255),
            "RefuelTime" TIMESTAMP(0) NOT NULL,
            "RefuelQuantity" DOUBLE NOT NULL,
            "UnitPrice" DOUBLE,
            "RefuelFee" DOUBLE,
            "Operator" VARCHAR(255),
            "UpdaterId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Extend1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SerialId"));

            CREATE OR REPLACE  INDEX "IDX_Gene_RefuelRecord_1" ON "gene_refuelrecord"("OilId" ASC);
        </sql>
    </changeSet>
</databaseChangeLog>