<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-005" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_patrolallrecord_1"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_2"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_3"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_4"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_5"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_6"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_7"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_8"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_9"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_10"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_11"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolallrecord_12"
            (
            "PatrolAllRecordID" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolcalop"
            (
            "CalOpId" INT NOT NULL,
            "CalOperator" VARCHAR(128) NOT NULL,
            "Meaning" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CalOpId"));

            CREATE TABLE "tbl_patrolcronexpression"
            (
            "CronId" INT AUTO_INCREMENT NOT NULL,
            "CronExpression" VARCHAR(128) DEFAULT '' NOT NULL,
            "Meaning" VARCHAR(128) DEFAULT '' NOT NULL,
            NOT CLUSTER PRIMARY KEY("CronId"));

            CREATE TABLE "tbl_patrolexrecord"
            (
            "PatrolExRecordId" int PRIMARY KEY AUTO_INCREMENT,
            "CenterId" INT NOT NULL,
            "CenterName" VARCHAR(255),
            "GroupId" INT NOT NULL,
            "GroupName" VARCHAR(255),
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentCategoryName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "SignalValue" VARCHAR(64),
            "RecordTime" TIMESTAMP(0),
            "Unit" VARCHAR(128),
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "WarningLevelId" INT,
            "Reasonable" INT NOT NULL,
            "IsPowerOffAlarm" VARCHAR(8) DEFAULT '' NOT NULL,
            "CreateTime" TIMESTAMP(0),
            "Sn" VARCHAR(36) NOT NULL);

            CREATE TABLE "tbl_patrolgroup"
            (
            "GroupId" INT AUTO_INCREMENT NOT NULL,
            "GroupName" VARCHAR(128) NOT NULL,
            "BaseEquipmentId" INT,
            "BaseTypeId" DECIMAL(12,0),
            "Note" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("GroupId"));

            CREATE TABLE "tbl_patrolgroupbasesignalmap"
            (
            "PatrolGroupBaseSignalMap" int PRIMARY KEY AUTO_INCREMENT,
            "GroupId" INT NOT NULL,
            "BaseEquipmentId" INT,
            "BaseTypeId" DECIMAL(10,0));

            CREATE TABLE "tbl_patrolgroupparameters"
            (
            "PatrolGroupParameters" int PRIMARY KEY AUTO_INCREMENT,
            "GroupId" INT NOT NULL,
            "StationTypeIds" VARCHAR(24),
            "StationIds" CLOB,
            "EquipmentIds" CLOB,
            "SignalIds" CLOB);

            CREATE TABLE "tbl_patrolgrouprulemap"
            (
            "PatrolGroupRuleMap" int PRIMARY KEY AUTO_INCREMENT,
            "GroupId" INT NOT NULL,
            "RuleId" INT NOT NULL);

            CREATE TABLE "tbl_patrolrule"
            (
            "RuleId" INT AUTO_INCREMENT NOT NULL,
            "RuleName" VARCHAR(128) NOT NULL,
            "LimitDown" DOUBLE,
            "LimitDownCalOpId" INT NOT NULL,
            "LimitUp" DOUBLE,
            "LimitUpCalOpId" INT NOT NULL,
            "UnitId" INT,
            "WarningLevelId" INT NOT NULL,
            "ByPercentage" INT NOT NULL,
            "RatedValue" DOUBLE,
            "BaseEquipmentId" INT,
            "BaseTypeId" DECIMAL(12,0),
            "Note" VARCHAR(255),
            "Description" VARCHAR(255),
            "EquipmentLogicClassId" int ,
            "StandardDicId" int ,
            NOT CLUSTER PRIMARY KEY("RuleId"));

            CREATE TABLE "tbl_patroltask"
            (
            "TaskId" INT AUTO_INCREMENT NOT NULL,
            "TaskName" VARCHAR(128) NOT NULL,
            "CronId" INT NOT NULL,
            "GroupId" INT NOT NULL,
            "IsPowerOffSave" INT DEFAULT 0 NOT NULL,
            "Note" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("TaskId"));

            CREATE TABLE "tbl_patrolunit"
            (
            "UnitId" INT NOT NULL,
            "UnitSymbol" VARCHAR(128) NOT NULL,
            "Meaning" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("UnitId"));

            CREATE TABLE "tbl_patrolwarninglevel"
            (
            "LevelId" INT NOT NULL,
            "Meaning" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LevelId"));

            CREATE TABLE "tbl_patrolgroupstandardsignalmap" (
            "PatrolGroupStandardSignalMap" int PRIMARY KEY AUTO_INCREMENT,
            "GroupId" int NOT NULL,
            "EquipmentLogicClassId" int ,
            "StandardDicId" int
            );

            CREATE TABLE "tbl_patrolstandardgroup" (
            "GroupId" int NOT NULL AUTO_INCREMENT,
            "GroupName" varchar(128) NOT NULL,
            "EquipmentLogicClassId" int ,
            "StandardDicId" int ,
            "Note" varchar(255) ,
            NOT CLUSTER PRIMARY KEY ("GroupId")
            );

        </sql>
    </changeSet>
</databaseChangeLog>