<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-114" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "convergenceevent"
            (
            "Id" BIGINT AUTO_INCREMENT NOT NULL,
            "EventName" VARCHAR(255) NOT NULL,
            "ConvergenceType" INT NOT NULL,
            "ConvergenceRuleId" INT NOT NULL,
            "BirthTime" TIMESTAMP(0) NOT NULL,
            "ConvergenceCount" INT NOT NULL,
            "Status" INT,
            "ConfirmTime" TIMESTAMP(0),
            "PossibleCauses" VARCHAR(1024),
            "EquipmentId" INT,
            "EventId" INT,
            "EventConditionId" INT,
            "ClearTime" TIMESTAMP(0),
            "ConfirmerId" INT,
            "ConfirmerName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "eventconvergencerule"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "RuleName" VARCHAR(255) NOT NULL,
            "ConvergenceType" INT NOT NULL,
            "EquipmentCategory" INT NOT NULL,
            "StartExpression" VARCHAR(1024),
            "FilterCondition" VARCHAR(2048),
            "ConvergenceInterval" INT,
            "StartCount" INT,
            "EndCount" INT,
            "PossibleCauses" VARCHAR(1024),
            "ParentRuleId" INT,
            "LevelOfPath" VARCHAR(1024),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "powerequipmentconnection"
            (
            "Id" BIGINT AUTO_INCREMENT NOT NULL,
            "ParentEquipmentId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "LevelOFPath" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));
        </sql>
    </changeSet>
</databaseChangeLog>