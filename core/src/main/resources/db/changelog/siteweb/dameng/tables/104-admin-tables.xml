<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-104" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "accountalias"
            (
            "AccountAliasId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT,
            "Alias" VARCHAR(128),
            "Checked" TINYINT,
            NOT CLUSTER PRIMARY KEY("AccountAliasId"));

            CREATE TABLE "accountpassworderrrecord"
            (
            "UserId" INT NOT NULL,
            "PasswordErrCnt" INT,
            "FreezeTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("UserId"));

            COMMENT ON COLUMN "accountpassworderrrecord"."FreezeTime" IS '账号冻结目标时间';
            COMMENT ON COLUMN "accountpassworderrrecord"."PasswordErrCnt" IS '密码错误累计次数';
            COMMENT ON COLUMN "accountpassworderrrecord"."UserId" IS '用户ID';

            CREATE TABLE "accountterminaldevicemap" (
            "UserId" INT NOT NULL,
            "TerminalDeviceId" VARCHAR(128),
            "UpdateTime" datetime,
            "OperatorId" INT,
            NOT CLUSTER PRIMARY KEY("UserId"));

            CREATE TABLE "accounttimespan"
            (
            "AccountTimeSpanId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT,
            "WeekSpanChar" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("AccountTimeSpanId"));

            COMMENT ON COLUMN "accounttimespan"."Description" IS '描述信息';
            COMMENT ON COLUMN "accounttimespan"."UserId" IS '用户ID';
            COMMENT ON COLUMN "accounttimespan"."WeekSpanChar" IS '星期集合，逗号分隔';

            CREATE TABLE "auditreport"
            (
            "AuditReportId" INT AUTO_INCREMENT NOT NULL,
            "OperationAccount" VARCHAR(128),
            "Level" INT,
            "Type" VARCHAR(10),
            "ClientIP" VARCHAR(50),
            "Details" TEXT,
            "Result" VARCHAR(20),
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("AuditReportId"));

            CREATE TABLE "departmentcodemap"
            (
            "DepartmentId" INT NOT NULL,
            "ParentDepartmentId" INT,
            "Code" VARCHAR(256),
            "ParentCode" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("DepartmentId"));

            CREATE TABLE "diskfile"
            (
            "FileId" BIGINT AUTO_INCREMENT NOT NULL,
            "FilePath" VARCHAR(128) NOT NULL,
            "FileName" VARCHAR(128) NOT NULL,
            "Status" INT,
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("FileId"));

            CREATE TABLE "gocronexpression"
            (
            "ExpressionId" INT AUTO_INCREMENT NOT NULL,
            "Expression" VARCHAR(128) NOT NULL,
            "SimpleExpression" VARCHAR(128) NOT NULL,
            "ExpressionDescription" VARCHAR(128),
            "ExpressionDescriptionEn" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("ExpressionId"));

            CREATE TABLE "historypassword"
            (
            "Id" BIGINT AUTO_INCREMENT NOT NULL,
            "LogonId" VARCHAR(128),
            "Password" VARCHAR(128),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "ipfilterpolicy"
            (
            "IpFilterPolicyId" INT AUTO_INCREMENT NOT NULL,
            "IpAddrSet" VARCHAR(255),
            "WeekSpanChar" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("IpFilterPolicyId"));

            CREATE TABLE "license"
            (
            "LicenseId" INT AUTO_INCREMENT NOT NULL,
            "Product" VARCHAR(255),
            "UniqueInfo" VARCHAR(1000),
            "LicenseType" INT,
            "ActiveTime" TIMESTAMP(0),
            "LimitTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("LicenseId"));

            CREATE TABLE "licensefeature"
            (
            "FeatureId" INT AUTO_INCREMENT NOT NULL,
            "Name" VARCHAR(255),
            "IsActive" TINYINT,
            "Data" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("FeatureId"));

            CREATE TABLE "loginlog"
            (
            "LoginLogId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT NOT NULL,
            "OperatingTime" TIMESTAMP(0) NOT NULL,
            "OperatingType" VARCHAR(64) NOT NULL,
            "ClientType" VARCHAR(32) NOT NULL,
            "ClientIp" VARCHAR(50) NOT NULL,
            NOT CLUSTER PRIMARY KEY("LoginLogId"));

            CREATE TABLE "menuitem"
            (
            "MenuItemId" INT AUTO_INCREMENT NOT NULL,
            "ParentId" INT,
            "Path" VARCHAR(255),
            "Title" VARCHAR(255),
            "Icon" VARCHAR(255),
            "FeatureId" INT,
            "Selected" TINYINT,
            "Expanded" TINYINT,
            "PathMatch" VARCHAR(255),
            "LayoutPosition" INT,
            "IsSystemConfig" TINYINT,
            "IsExternalWeb" TINYINT,
            "MenuHasNavigation" TINYINT,
            "Description" VARCHAR(255),
            "Alias" VARCHAR(255),
            "IsEmbed" BIT DEFAULT 0,
            "SortIndex" INT,
            NOT CLUSTER PRIMARY KEY("MenuItemId"));

            CREATE TABLE "menuitemstructuremap"
            (
            "MenuItemStructureMapId" INT AUTO_INCREMENT NOT NULL,
            "MenuProfileId" INT,
            "MenuStructureId" INT,
            "MenuItemId" INT,
            "SortIndex" INT,
            NOT CLUSTER PRIMARY KEY("MenuItemStructureMapId"));

            CREATE TABLE "menupermissiongroup"
            (
            "MenuPermissionGroupId" INT AUTO_INCREMENT NOT NULL,
            "MenuPermissionGroupName" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MenuPermissionGroupId"));

            CREATE TABLE "menupermissiongroupmap"
            (
            "MenuPermissionGroupMapId" INT AUTO_INCREMENT NOT NULL,
            "MenuPermissionGroupId" INT,
            "PermissionId" INT,
            NOT CLUSTER PRIMARY KEY("MenuPermissionGroupMapId"));

            CREATE TABLE "menuprofile"
            (
            "MenuProfileId" INT AUTO_INCREMENT NOT NULL,
            "Name" VARCHAR(255),
            "Checked" TINYINT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MenuProfileId"));

            CREATE TABLE "menustructure"
            (
            "MenuStructureId" INT AUTO_INCREMENT NOT NULL,
            "MenuProfileId" INT,
            "ParentId" INT,
            "Title" VARCHAR(255),
            "Icon" VARCHAR(255),
            "Selected" TINYINT,
            "Expanded" TINYINT,
            "Hidden" TINYINT,
            "SortIndex" INT,
            "IsSystem" TINYINT,
            "Description" VARCHAR(255),
            "Alias" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MenuStructureId"));

            CREATE TABLE "mobileclientmap"
            (
            "LoginUserId" INT NOT NULL,
            "Cid" VARCHAR(128),
            "MobileInfo" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("LoginUserId"));

            CREATE TABLE "mobileconditionalpushconfig"
            (
            "LoginUserId" INT NOT NULL,
            "Enable" TINYINT,
            "Conditional" TEXT,
            NOT CLUSTER PRIMARY KEY("LoginUserId"));

            CREATE TABLE "notification"
            (
            "NotificationId" INT AUTO_INCREMENT NOT NULL,
            "Pushed" TINYINT,
            "Category" VARCHAR(255),
            "Title" VARCHAR(255),
            "Content" VARCHAR(255),
            "Sender" VARCHAR(255),
            "Color" VARCHAR(255),
            "Icon" VARCHAR(255),
            "WebLink" VARCHAR(255),
            "AppLink" VARCHAR(255),
            "CreateTime" TIMESTAMP(0),
            "ExternalId" VARCHAR(50),
            "ExtParam" TEXT,
            NOT CLUSTER PRIMARY KEY("NotificationId"));

            CREATE TABLE "notificationlog"
            (
            "NotificationLogId" DECIMAL(19,0) NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EventStatus" INT NOT NULL,
            "NotifyResult" INT NOT NULL,
            "NotifyReciever" INT,
            "NotifyAddress" VARCHAR(255),
            "NotifyCategory" INT,
            "Description" VARCHAR(255),
            "SMSSentTime" TIMESTAMP(0),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("NotificationLogId"));

            CREATE OR REPLACE  INDEX "NotificationLog_IDX" ON "notificationlog"("StartTime" ASC);

            CREATE TABLE "notificationlogmid"
            (
            "NotificationLogId" DECIMAL(19,0) NOT NULL PRIMARY KEY,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EventStatus" INT NOT NULL,
            "NotifyResult" INT NOT NULL,
            "NotifyReciever" INT,
            "NotifyAddress" VARCHAR(255),
            "NotifyCategory" INT,
            "Description" VARCHAR(255),
            "SMSSentTime" TIMESTAMP(0),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255));

            CREATE TABLE "notificationreceiver"
            (
            "NotificationReceiverId" INT AUTO_INCREMENT NOT NULL,
            "NotificationId" INT,
            "LoginUserId" INT NOT NULL,
            "Readed" TINYINT,
            "ReadedTime" TIMESTAMP(0),
            "Deleted" TINYINT,
            "DeletedTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("NotificationReceiverId"));

            CREATE TABLE "notifymode"
            (
            "NotifyModeId" INT NOT NULL,
            "NotifyModeName" VARCHAR(128) NOT NULL,
            "NotifyModeFormat" VARCHAR(255),
            "Description" VARCHAR(255),
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("NotifyModeId"));

            CREATE TABLE "notifyreceiver"
            (
            "NotifyReceiverId" INT NOT NULL,
            "NotifyReceiverCategory" INT NOT NULL,
            "NotifyReceiverName" VARCHAR(128) NOT NULL,
            "NotifyAddress" VARCHAR(255) NOT NULL,
            "NotifyContent" VARCHAR(255),
            "Description" VARCHAR(255),
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("NotifyReceiverId", "NotifyReceiverCategory"));

            CREATE OR REPLACE  INDEX "NotifyReceiverCategory" ON "notifyreceiver"("NotifyReceiverCategory" ASC);

            CREATE TABLE "notifyreceivermap"
            (
            "EventFilterId" INT NOT NULL,
            "EventFilterConditionId" INT NOT NULL,
            "NotifyReceiverId" INT NOT NULL,
            "NotifyReceiverCategory" INT NOT NULL,
            "NotifyServerId" INT NOT NULL,
            "NotifyServerCategory" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("EventFilterId", "EventFilterConditionId", "NotifyReceiverId", "NotifyReceiverCategory", "NotifyServerId", "NotifyServerCategory"));

            CREATE TABLE "notifyserver"
            (
            "NotifyServerId" INT NOT NULL,
            "NotifyServerCategory" INT NOT NULL,
            "NotifyServerName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("NotifyServerId", "NotifyServerCategory"));

            CREATE TABLE "permission"
            (
            "PermissionId" INT AUTO_INCREMENT NOT NULL,
            "Name" VARCHAR(128),
            "Category" INT,
            "Caption" VARCHAR(255),
            "Description" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("PermissionId"));

            CREATE TABLE "permissioncategory"
            (
            "PermissionCategoryId" INT AUTO_INCREMENT NOT NULL,
            "Name" VARCHAR(128),
            "Caption" VARCHAR(255),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("PermissionCategoryId"));

            CREATE TABLE "region"
            (
            "RegionId" INT AUTO_INCREMENT NOT NULL,
            "RegionName" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("RegionId"));

            CREATE TABLE "regionmap"
            (
            "RegionMapId" INT AUTO_INCREMENT NOT NULL,
            "ResourceStructureId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "RegionId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("RegionMapId", "ResourceStructureId", "EquipmentId", "RegionId"),
            CONSTRAINT "RegionMapId" UNIQUE("RegionMapId"));

            CREATE TABLE "resourcestructure"
            (
            "ResourceStructureId" INT AUTO_INCREMENT NOT NULL,
            "SceneId" INT,
            "StructureTypeId" INT,
            "ResourceStructureName" VARCHAR(128),
            "ParentResourceStructureId" INT,
            "Photo" VARCHAR(256),
            "Position" VARCHAR(256),
            "LevelOfPath" VARCHAR(128),
            "Display" TINYINT,
            "SortValue" INT,
            "ExtendedField" VARCHAR(8188),
            "OriginId" INT,
            "OriginParentId" INT,
            NOT CLUSTER PRIMARY KEY("ResourceStructureId"),
            CHECK("ExtendedField" IS JSON ));

            CREATE OR REPLACE  INDEX "IDX_ResourceStructure_LevelOfPath" ON "resourcestructure"("LevelOfPath" ASC);
            CREATE OR REPLACE  INDEX "IDX_ResourceStructure_1" ON "resourcestructure"("StructureTypeId" ASC,"OriginParentId" ASC,"OriginId" ASC);
            CREATE OR REPLACE  INDEX "IDX_ResourceStructure_Type" ON "resourcestructure"("StructureTypeId" ASC);

            CREATE TABLE "resourcestructuremask"
            (
            "ResourceStructureId" INT NOT NULL,
            "TimeGroupId" INT,
            "Reason" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "UserId" INT,
            NOT CLUSTER PRIMARY KEY("ResourceStructureId"));

            CREATE TABLE "resourcestructuretype"
            (
            "ResourceStructureTypeId" INT NOT NULL,
            "SceneId" INT,
            "ResourceStructureTypeName" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ResourceStructureTypeId"));

            CREATE TABLE "rolegraphicpagemap"
            (
            "RoleId" INT NOT NULL,
            "GraphicPageId" INT,
            "Config" TEXT,
            NOT CLUSTER PRIMARY KEY("RoleId"));

            CREATE TABLE "rolepermissionmap"
            (
            "RolePermissionMapId" INT AUTO_INCREMENT NOT NULL,
            "RoleId" INT,
            "PermissionCategoryId" INT,
            "PermissionId" INT,
            NOT CLUSTER PRIMARY KEY("RolePermissionMapId"));

            CREATE OR REPLACE  INDEX "IDX_PermissionCategoryId" ON "rolepermissionmap"("PermissionCategoryId" ASC);
            CREATE OR REPLACE  INDEX "IDX_ROLE_PermissionId" ON "rolepermissionmap"("RoleId" ASC,"PermissionId" ASC);

            CREATE TABLE "roomcategory"
            (
            "RoomCategoryId" INT NOT NULL,
            "RoomCategoryName" VARCHAR(128) NOT NULL,
            "Color" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("RoomCategoryId"));

            CREATE TABLE "scene"
            (
            "SceneId" INT AUTO_INCREMENT NOT NULL,
            "SceneName" VARCHAR(128),
            "Checked" TINYINT,
            "Remark" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("SceneId"));

            CREATE TABLE "scenecompmap"
            (
            "SceneCompMapId" INT AUTO_INCREMENT NOT NULL,
            "type" VARCHAR(128),
            "SceneId" INT,
            "PageCategory" INT,
            "CompType" INT,
            NOT CLUSTER PRIMARY KEY("SceneCompMapId"));

            CREATE TABLE "scenemenuprofilemap"
            (
            "SceneMenuProfileMapId" INT AUTO_INCREMENT NOT NULL,
            "SceneId" INT,
            "MenuProfileId" INT,
            NOT CLUSTER PRIMARY KEY("SceneMenuProfileMapId"));

            CREATE TABLE "scenepermissioncategorymap"
            (
            "ScenePermissionCategoryMapId" INT NOT NULL,
            "PermissionCategoryId" INT,
            "SceneId" INT,
            NOT CLUSTER PRIMARY KEY("ScenePermissionCategoryMapId"));

            CREATE TABLE "scenepermissionmap"
            (
            "ScenePermissionMapId" INT AUTO_INCREMENT NOT NULL,
            "PermissionId" INT,
            "SceneId" INT,
            NOT CLUSTER PRIMARY KEY("ScenePermissionMapId"));

            CREATE TABLE "scenestructure"
            (
            "SceneStructureId" INT AUTO_INCREMENT NOT NULL,
            "SceneId" INT NOT NULL,
            "ObjectTypeId" INT NOT NULL,
            "DisplayIndex" INT,
            NOT CLUSTER PRIMARY KEY("SceneStructureId"));

            CREATE TABLE "securityreport"
            (
            "SecurityReportId" INT AUTO_INCREMENT NOT NULL,
            "OperationAccount" VARCHAR(128),
            "Type" INT,
            "ClientIP" VARCHAR(50),
            "Details" TEXT,
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("SecurityReportId"));

            CREATE TABLE "softwareversion"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ModuleName" VARCHAR(128) NOT NULL,
            "Version" VARCHAR(256) NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "Feature" VARCHAR(256) NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "systemconfig"
            (
            "SystemConfigId" INT AUTO_INCREMENT NOT NULL,
            "SystemConfigKey" VARCHAR(255),
            "SystemConfigValue" VARCHAR(255),
            "SystemConfigType" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SystemConfigId"),
            CONSTRAINT "SystemConfigKey" UNIQUE("SystemConfigKey"));

            CREATE TABLE "userconfig"
            (
            "UserConfigId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT,
            "ConfigType" INT,
            "ConfigKey" VARCHAR(255),
            "ConfigValue" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("UserConfigId"));

            CREATE OR REPLACE  INDEX "IDX_UserId_UserType" ON "userconfig"("UserId" ASC,"ConfigType" ASC);

            CREATE TABLE "webclientlog"
            (
            "WebClientLogId" INT AUTO_INCREMENT NOT NULL,
            "UserName" VARCHAR(255),
            "ClientIp" VARCHAR(255),
            "BusinessModule" VARCHAR(50),
            "Content" VARCHAR(1000),
            "Remark" VARCHAR(255),
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("WebClientLogId"));

            CREATE OR REPLACE  INDEX "IDX_UserName_CreateTime" ON "webclientlog"("UserName" ASC,"CreateTime" ASC);
            CREATE OR REPLACE  INDEX "IDX_CreateTime" ON "webclientlog"("CreateTime" ASC);
        </sql>
    </changeSet>
</databaseChangeLog>