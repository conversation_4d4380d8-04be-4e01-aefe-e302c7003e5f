<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-015" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_saralarmactiverecord"
            (
            "StationId" INT NOT NULL,
            "StationCategoryId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "SequenceId" VARCHAR(128) NOT NULL PRIMARY KEY,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "Overturn" INT,
            "Meanings" VARCHAR(255),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "BaseTypeId" DECIMAL(12,0),
            "StandardId" INT,
            "InsertDateTime" TIMESTAMP(0) NOT NULL,
            "RelationType" INT NOT NULL);

            CREATE TABLE "tbl_saralarmqueue"
            (
            "StationId" INT NOT NULL,
            "StationCategoryId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "SequenceId" VARCHAR(128) NOT NULL PRIMARY KEY,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "Overturn" INT,
            "Meanings" VARCHAR(255),
            "EventValue" DOUBLE,
            "EndValue" DOUBLE,
            "BaseTypeId" DECIMAL(12,0),
            "StandardId" INT,
            "InsertDateTime" TIMESTAMP(0) NOT NULL);

            CREATE TABLE "tbl_saralarmrecordstarttime"
            (
            "SarAlarmRecordStartTimeId" int PRIMARY KEY AUTO_INCREMENT,
            "StartTime" TIMESTAMP(0),
            "RelationType" INT);

            CREATE TABLE "tbl_saralarmrelation"
            (
            "SarAlarmRecordRelationId" int PRIMARY KEY AUTO_INCREMENT,
            "StationId" INT,
            "EquipmentId" INT,
            "EventId" INT,
            "EventConditionId" INT,
            "StartTime" TIMESTAMP(0),
            "StandardId" INT,
            "CauseStationId" INT,
            "CauseEquipmentId" INT,
            "CauseEventId" INT,
            "CauseEventConditionId" INT,
            "CauseStartTime" TIMESTAMP(0),
            "CauseStandardId" INT,
            "RelationType" INT);

            CREATE TABLE "tbl_sarderivatealarmrule"
            (
            "SarDerivateAlarmRuleId" int PRIMARY KEY AUTO_INCREMENT,
            "RuleId" INT NOT NULL,
            "RuleName" VARCHAR(255) NOT NULL,
            "AlarmCount" INT NOT NULL,
            "AlarmEndCount" INT NOT NULL,
            "AlarmTimeScope" INT NOT NULL,
            "Description" VARCHAR(255));

            CREATE TABLE "tbl_sarisprocess"
            (
            "SarIsProcessId" int PRIMARY KEY AUTO_INCREMENT,
            "IsProcess" INT);

            CREATE TABLE "tbl_sarreversealarmrule"
            (
            "StartThreshold" INT NOT NULL,
            "EndThreshold" INT NOT NULL,
            "TimeThreshold" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("StartThreshold","EndThreshold","TimeThreshold"));

            CREATE TABLE "tbl_sarsecondaryalarmbyfilter"
            (
            "SarSecondaryAlarmByFilter" int PRIMARY KEY AUTO_INCREMENT,
            "SeconarySequenceId" TEXT,
            "StationId" INT,
            "EquipmentId" INT,
            "EventId" INT,
            "EventConditionId" INT,
            "StartTime" TIMESTAMP(0),
            "PrimarySequenceId" TEXT,
            "InsertDateTime" TIMESTAMP(0));

            CREATE TABLE "tbl_sarstationderivatemap"
            (
            "RuleId" INT NOT NULL PRIMARY KEY,
            "DerivateBaseTypeId" INT NOT NULL,
            "BaseTypeId" INT NOT NULL,
            "StationCategoryId" INT NOT NULL);

            CREATE TABLE "tbl_sarstationprimarymap"
            (
            "PrimaryId" INT NOT NULL,
            "StationCategoryId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("PrimaryId","StationCategoryId"));
        </sql>
    </changeSet>
</databaseChangeLog>