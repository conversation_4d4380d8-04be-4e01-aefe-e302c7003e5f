<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-122" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE internalmessagetype
            (
                InternalMessageTypeId INT AUTO_INCREMENT NOT NULL,
                TypeName              VARCHAR(32) NOT NULL COMMENT '消息类型',
                EnableTts             INT         NOT NULL COMMENT '是否启用TTS播报',
                PRIMARY KEY (InternalMessageTypeId)
            );
            CREATE TABLE internalmessage
            (
                InternalMessageId INT AUTO_INCREMENT NOT NULL,
                body              VARCHAR(512) NOT NULL COMMENT '消息内容',
                messageType       INT          NOT NULL COMMENT '消息类型',
                createTime        DATETIME     NOT NULL COMMENT '创建时间',
                PRIMARY KEY (InternalMessageId)
            );
            CREATE TABLE messagestatus
            (
                MessageStatusId   INT AUTO_INCREMENT NOT NULL,
                InternalMessageId INT DEFAULT NULL COMMENT '消息id',
                userId            INT NOT NULL COMMENT '用户接收ID',
                messageStatus     INT DEFAULT NULL COMMENT '消息状态 0未读 1已读',
                PRIMARY KEY (MessageStatusId)
            );
            CREATE INDEX idx_user_id ON messagestatus (userId);
            CREATE INDEX idx_internal_message_id ON messagestatus (InternalMessageId);
        </sql>
    </changeSet>
</databaseChangeLog>