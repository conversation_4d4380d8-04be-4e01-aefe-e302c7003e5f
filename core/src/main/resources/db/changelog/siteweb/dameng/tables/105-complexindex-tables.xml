<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-105" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "complexindex"
            (
            "ComplexIndexId" INT AUTO_INCREMENT NOT NULL,
            "ComplexIndexName" VARCHAR(128),
            "ComplexIndexDefinitionId" INT,
            "ObjectId" INT,
            "CalcCron" VARCHAR(128),
            "CalcType" INT,
            "AfterCalc" TEXT,
            "SaveCron" VARCHAR(128),
            "Expression" TEXT,
            "Unit" VARCHAR(128),
            "Accuracy" VARCHAR(128),
            "ObjectTypeId" INT,
            "Remark" VARCHAR(128),
            "Label" VARCHAR(128),
            "BusinessTypeId" INT,
            "CheckExpression" VARCHAR(1024),
            NOT CLUSTER PRIMARY KEY("ComplexIndexId"));

            CREATE TABLE "complexindexbusinessobjectmap"
            (
            "BusinessObjectMapId" INT AUTO_INCREMENT NOT NULL,
            "SceneId" INT NOT NULL,
            "BusinessTypeId" INT NOT NULL,
            "ObjectTypeId" INT NOT NULL,
            "ComplexIndexDefinitionId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("BusinessObjectMapId"));

            CREATE TABLE "complexindexbusinesstype"
            (
            "BusinessTypeId" INT AUTO_INCREMENT NOT NULL,
            "BusinessTypeName" VARCHAR(50) NOT NULL,
            "ParentId" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("BusinessTypeId"));

            CREATE TABLE "complexindexdefinition"
            (
            "ComplexIndexDefinitionId" INT AUTO_INCREMENT NOT NULL,
            "ComplexIndexDefinitionName" VARCHAR(50) NOT NULL,
            "CalcCron" VARCHAR(128),
            "CalcType" INT,
            "AfterCalc" VARCHAR(128),
            "SaveCron" VARCHAR(128),
            "Expression" VARCHAR(128),
            "Unit" VARCHAR(128),
            "Accuracy" VARCHAR(128),
            "StartStatus" INT,
            "Icon" VARCHAR(128),
            "CheckExpression" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ComplexIndexDefinitionId"));

            CREATE TABLE "complexindexfunction"
            (
            "FunctionId" INT AUTO_INCREMENT NOT NULL,
            "FunctionExpression" VARCHAR(128),
            "FunctionDescription" VARCHAR(128),
            "FunctionName" VARCHAR(128),
            "Remark" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("FunctionId"));
        </sql>
    </changeSet>
</databaseChangeLog>