<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-014" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_activecontrolofdoor"
            (
            "StationId" INT NOT NULL,
            "HostId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "UserId" INT NOT NULL,
            "ParameterValues" TEXT,
            "Description" VARCHAR(255),
            "LastUpdate" TIMESTAMP(0) NOT NULL,
            "Id" INT AUTO_INCREMENT NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "IDX_ActiveDoorControl_HostId" ON "tbl_activecontrolofdoor"("HostId" ASC);
            CREATE OR REPLACE  INDEX "IDX_ActiveDoorControl_Complex" ON "tbl_activecontrolofdoor"("StationId" ASC,"EquipmentId" ASC,"ControlId" ASC,"UserId" ASC);

            CREATE TABLE "tbl_activecontrolofdoor_fail"
            (
            "ActiveControlOfDoorFailId" int PRIMARY KEY AUTO_INCREMENT,
            "StationId" INT NOT NULL,
            "HostId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "UserId" INT NOT NULL,
            "ParameterValues" TEXT,
            "Description" VARCHAR(255),
            "LastUpdate" TIMESTAMP(0) NOT NULL,
            "SendResult" INT);

            CREATE OR REPLACE  INDEX "IDX_tbl_activecontrolofdoor_fail_HostId" ON "tbl_activecontrolofdoor_fail"("HostId" ASC);
            CREATE OR REPLACE  INDEX "IDX_tbl_activecontrolofdoor_fail_Complex" ON "tbl_activecontrolofdoor_fail"("StationId" ASC,"EquipmentId" ASC,"ControlId" ASC,"UserId" ASC);

            CREATE TABLE "tbl_card"
            (
            "CardId" INT NOT NULL,
            "CardCode" VARCHAR(20) NOT NULL,
            "CardName" VARCHAR(128),
            "CardCategory" INT,
            "CardGroup" INT,
            "UserId" INT,
            "StationId" INT,
            "CardStatus" INT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "RegisterTime" TIMESTAMP(0),
            "UnRegisterTime" TIMESTAMP(0),
            "LostTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CardId"));

            CREATE TABLE "tbl_cardext"
            (
            "CardId" INT NOT NULL,
            "Password" VARCHAR(30),
            "cardcodetype" INT,
            "cardcodeconv" VARCHAR(20),
            NOT CLUSTER PRIMARY KEY("CardId"));

            CREATE TABLE "tbl_cardtypemap"
            (
            "CardId" INT NOT NULL PRIMARY KEY,
            "CardType" INT NOT NULL);

            CREATE TABLE "tbl_ddscardno"
            (
            "Id" int primary key AUTO_INCREMENT,
            "DoorId" INT,
            "CardId" INT,
            "CardNo" INT);

            CREATE TABLE "tbl_door"
            (
            "DoorId" INT NOT NULL,
            "DoorNo" INT NOT NULL,
            "DoorName" VARCHAR(128),
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SamplerUnitId" INT,
            "Category" INT NOT NULL,
            "Address" VARCHAR(255),
            "WorkMode" INT,
            "Infrared" INT,
            "Password" VARCHAR(10),
            "DoorControlId" INT,
            "DoorInterval" INT,
            "OpenDelay" INT,
            "Description" VARCHAR(255),
            "OpenMode" INT,
            NOT CLUSTER PRIMARY KEY("DoorId"));
            CREATE INDEX "idx_door_equipment" ON "tbl_door" ("EquipmentId", "DoorId");

            CREATE TABLE "tbl_doorarea"
            (
            "areaid" INT NOT NULL,
            "areaname" VARCHAR(255),
            "parentid" INT,
            "description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("areaid"));

            CREATE TABLE "tbl_doorareamap"
            (
            "areaid" INT NOT NULL,
            "equipmentid" INT NOT NULL,
            CONSTRAINT "equipmentid" PRIMARY KEY("equipmentid"));

            CREATE TABLE "tbl_doorcard"
            (
            "CardId" INT NOT NULL,
            "TimeGroupId" INT NOT NULL,
            "DoorId" INT NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Password" VARCHAR(30),
            "timegrouptype" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("CardId", "DoorId", "TimeGroupId","timegrouptype"));

            CREATE TABLE "tbl_doorcardlost"
            (
            "CardId" INT NOT NULL,
            "TimeGroupId" INT NOT NULL,
            "DoorId" INT NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "Password" VARCHAR(30),
            NOT CLUSTER PRIMARY KEY("CardId", "DoorId", "TimeGroupId"));

            CREATE TABLE "tbl_doorcontroller"
            (
            "DoorControlId" INT NOT NULL,
            "DoorControlName" VARCHAR(128) NOT NULL,
            "LicenseKey" VARCHAR(30),
            "Display" INT,
            "CardLength" INT NOT NULL,
            "MaxDoorCount" INT NOT NULL,
            "DLLPath" VARCHAR(128),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("DoorControlId"));

            CREATE TABLE "tbl_doorgroup"
            (
            "DoorGroupId" INT NOT NULL,
            "DoorGroupName" VARCHAR(255) NOT NULL,
            "Description" VARCHAR(255),
            "LastTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("DoorGroupId"));

            CREATE TABLE "tbl_doorgroupmap"
            (
            "DoorGroupMapId" int PRIMARY KEY AUTO_INCREMENT,
            "DoorGroupId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "LastTime" TIMESTAMP(0));

            CREATE TABLE "tbl_doorparam"
            (
            "doorid" INT NOT NULL,
            "paramtype" INT NOT NULL,
            "paramvalue" INT,
            NOT CLUSTER PRIMARY KEY("doorid", "paramtype"));

            COMMENT ON TABLE "tbl_doorparam" IS '门参数表,用于记录tbl_door表外参数,参数类型EntryId=157';
            COMMENT ON COLUMN "tbl_doorparam"."doorid" IS '门ID';
            COMMENT ON COLUMN "tbl_doorparam"."paramtype" IS '参数类型';
            COMMENT ON COLUMN "tbl_doorparam"."paramvalue" IS '门参数值';

            CREATE TABLE "tbl_doorproperty"
            (
            "category" INT NOT NULL,
            "propertytype" INT NOT NULL,
            CONSTRAINT "category" PRIMARY KEY("category", "propertytype"));

            COMMENT ON TABLE "tbl_doorproperty" IS '门相关属性,用于记录不同类型门对应属性,属性类型EntryId=157';
            COMMENT ON COLUMN "tbl_doorproperty"."category" IS '门类型';
            COMMENT ON COLUMN "tbl_doorproperty"."propertytype" IS '属性类型';

            CREATE TABLE "tbl_doorpropertymeaning"
            (
            "category" INT NOT NULL,
            "propertytype" INT NOT NULL,
            "propertyid" INT NOT NULL,
            "meaning" VARCHAR(255),
            PRIMARY KEY("category", "propertytype", "propertyid"));

            COMMENT ON TABLE "tbl_doorpropertymeaning" IS '门相关属性含义表,用于记录不同类型门对应属性值含义,属性类型EntryId=157';
            COMMENT ON COLUMN "tbl_doorpropertymeaning"."category" IS '门类型';
            COMMENT ON COLUMN "tbl_doorpropertymeaning"."meaning" IS '属性含义';
            COMMENT ON COLUMN "tbl_doorpropertymeaning"."propertyid" IS '属性ID';
            COMMENT ON COLUMN "tbl_doorpropertymeaning"."propertytype" IS '属性类型';

            CREATE TABLE "tbl_doortimegroup"
            (
            "DoorId" INT NOT NULL,
            "TimeGroupId" INT NOT NULL,
            "TimeGroupType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("DoorId", "TimeGroupId","TimeGroupType"));

            CREATE TABLE "tbl_facedata"
            (
            "FaceId" INT NOT NULL,
            "FaceData" BLOB NOT NULL,
            NOT CLUSTER PRIMARY KEY("FaceId"));

            CREATE TABLE "tbl_facedataauth"
            (
            "TimeGroupId" INT NOT NULL,
            "DoorId" INT NOT NULL,
            "CardId" INT NOT NULL,
            "FaceId" INT NOT NULL,
            "LastUpdateTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
            NOT CLUSTER PRIMARY KEY("CardId", "DoorId", "FaceId", "TimeGroupId"));

            CREATE TABLE "tbl_facedatamap"
            (
            "CardId" INT NOT NULL,
            "FaceId" INT NOT NULL,
            "LastUpdater" INT NOT NULL,
            "LastUpdateTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("CardId", "FaceId"));

            CREATE TABLE "tbl_fingerprint"
            (
            "FingerPrintId" INT NOT NULL,
            "FingerPrintNO" INT NOT NULL,
            "FingerPrintData" BLOB NOT NULL,
            NOT CLUSTER PRIMARY KEY("FingerPrintId", "FingerPrintNO"));

            CREATE TABLE "tbl_fingerprintauth"
            (
            "TimeGroupId" INT NOT NULL,
            "DoorId" INT NOT NULL,
            "CardId" INT NOT NULL,
            "FingerPrintId" INT NOT NULL,
            "LastUpdateTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("CardId", "DoorId", "FingerPrintId", "TimeGroupId"));

            CREATE TABLE "tbl_fingerprintcardmap"
            (
            "CardId" INT NOT NULL,
            "FingerPrintId" INT NOT NULL,
            "Vendor" INT,
            "LastUpdater" INT NOT NULL,
            "LastUpdateTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("CardId", "FingerPrintId"));

            CREATE TABLE "tbl_fingerprintdatadistributelogrecord"
            (
            "CardId" INT NOT NULL,
            "ReaderEquipId" INT NOT NULL,
            "Log" TEXT,
            "IsDistributeSucceed" INT DEFAULT 0 NOT NULL,
            NOT CLUSTER PRIMARY KEY("CardId", "ReaderEquipId"));

            CREATE TABLE "tbl_fingerprintdatamap"
            (
            "CardId" INT NOT NULL,
            "FingerUserId" VARCHAR(40),
            "FingerPrintNO" INT NOT NULL,
            "Password" VARCHAR(20),
            "FingerPrintData1" BLOB NOT NULL,
            "FingerPrintData2" BLOB,
            "FingerPrintData3" BLOB,
            "FingerDataSize1" INT NOT NULL,
            "FingerDataSize2" INT NOT NULL,
            "FingerDataSize3" INT NOT NULL,
            "IsDistributeSucceed" INT DEFAULT 0 NOT NULL,
            NOT CLUSTER PRIMARY KEY("CardId", "FingerPrintNO"));

            CREATE TABLE "tbl_fingerprintmap"
            (
            "FingerPrintMapId" int PRIMARY KEY AUTO_INCREMENT,
            "CardId" INT NOT NULL,
            "FingerUserId" INT NOT NULL,
            "FingerPrintNO" INT NOT NULL,
            "FingerPrintData" BLOB NOT NULL,
            "FingerStatus" INT NOT NULL);

            CREATE TABLE "tbl_fingerreadermap"
            (
            "ReaderId" INT NOT NULL,
            "ReaderName" VARCHAR(128) NOT NULL,
            "ReaderType" INT NOT NULL,
            "ReaderCommType" INT NOT NULL,
            "ReaderCommAddress" VARCHAR(255) NOT NULL,
            "SamplerType" INT NOT NULL,
            "PortId" INT NOT NULL,
            "NetId" INT,
            "DoorId" INT,
            "MultiDoorOpenType" INT,
            "DoorInOutFlag" INT,
            "IsDistributeSucceed" INT DEFAULT 0,
            "IsClearConfigSucceed" INT,
            "Note" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ReaderId"));

            CREATE TABLE "tbl_swapcardrecord"
            (
            "SwapCardRecordId" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "StationId" INT,
            "StationName" VARCHAR(255),
            "EquipmentId" INT,
            "EquipmentName" VARCHAR(128),
            "CardStationId" INT,
            "CardStationName" VARCHAR(255),
            "CardId" INT,
            "CardCode" VARCHAR(255),
            "CardName" VARCHAR(255),
            "CardUserId" INT,
            "CardUserName" VARCHAR(255),
            "CardCategory" INT,
            "CardCategoryName" VARCHAR(255),
            "CardGroup" INT,
            "CardGroupName" VARCHAR(255),
            "CardStatus" INT,
            "CardStatusName" VARCHAR(255),
            "DoorId" INT,
            "DoorNo" INT,
            "DoorName" VARCHAR(255),
            "DoorCategory" INT,
            "DoorCategoryName" VARCHAR(255),
            "Valid" INT,
            "ValidName" VARCHAR(128),
            "Enter" SMALLINT,
            "RecordTime" TIMESTAMP(0),
            CONSTRAINT "TBL_SwapCardRecord_IDX1" UNIQUE("RecordTime", "CardId", "DoorNo"));

            CREATE TABLE "tbl_swapcardrecordmid"
            (
            "SwapCardRecordIdMid" int NOT NULL PRIMARY KEY AUTO_INCREMENT,
            "StationId" INT,
            "StationName" VARCHAR(255),
            "EquipmentId" INT,
            "EquipmentName" VARCHAR(128),
            "CardStationId" INT,
            "CardStationName" VARCHAR(255),
            "CardId" INT,
            "CardCode" VARCHAR(255),
            "CardName" VARCHAR(255),
            "CardUserId" INT,
            "CardUserName" VARCHAR(255),
            "CardCategory" INT,
            "CardCategoryName" VARCHAR(255),
            "CardGroup" INT,
            "CardGroupName" VARCHAR(255),
            "CardStatus" INT,
            "CardStatusName" VARCHAR(255),
            "DoorId" INT,
            "DoorNo" INT,
            "DoorName" VARCHAR(255),
            "DoorCategory" INT,
            "DoorCategoryName" VARCHAR(255),
            "Valid" INT,
            "ValidName" VARCHAR(128),
            "Enter" SMALLINT,
            "RecordTime" TIMESTAMP(0));
            CREATE TABLE "doortag"
            (
                "TagId"       INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
                "TagName"     VARCHAR(255) NOT NULL,
                "TagIcon"     VARCHAR(255),
                "TagColor"    VARCHAR(255),
                "TagDescribe" VARCHAR(255)
            );
            COMMENT ON TABLE "doortag" IS '门标签表';
            COMMENT ON COLUMN "doortag"."TagId" IS '设置主键自增';
            COMMENT ON COLUMN "doortag"."TagName" IS '标签名称';
            COMMENT ON COLUMN "doortag"."TagIcon" IS '标签图标';
            COMMENT ON COLUMN "doortag"."TagColor" IS '标签颜色';
            COMMENT ON COLUMN "doortag"."TagDescribe" IS '标签描述';
            CREATE TABLE "doortagmap"
            (
                "Id"          INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
                "EquipmentId" INT,
                "TagId"       INT NOT NULL
            );
            COMMENT ON TABLE "doortagmap" IS '门标签映射表';
            COMMENT ON COLUMN "doortagmap"."Id" IS '设置主键自增';
            COMMENT ON COLUMN "doortagmap"."EquipmentId" IS '设备ID';
            COMMENT ON COLUMN "doortagmap"."TagId" IS '标签主键id';
            CREATE TABLE "doorcardbackup"
            (
                "DoorCardBackupId" INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
                "Type"             INT NOT NULL,
                "Id"               INT NOT NULL,
                "DeleteId"         VARCHAR(200),
                "DeleteName"       VARCHAR(200),
                "DeleteTime"       VARCHAR(255)
            );
            COMMENT ON TABLE "doorcardbackup" IS '门卡备份表';
            COMMENT ON COLUMN "doorcardbackup"."DoorCardBackupId" IS '设置主键自增';
            COMMENT ON COLUMN "doorcardbackup"."Type" IS '类型 1设备 2卡';
            COMMENT ON COLUMN "doorcardbackup"."Id" IS '设备或者卡的id';
            COMMENT ON COLUMN "doorcardbackup"."DeleteId" IS '删除ID';
            COMMENT ON COLUMN "doorcardbackup"."DeleteName" IS '删除名称';
            COMMENT ON COLUMN "doorcardbackup"."DeleteTime" IS '被删除的时间';
        </sql>
    </changeSet>
</databaseChangeLog>