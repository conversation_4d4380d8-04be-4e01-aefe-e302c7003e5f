<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-010" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_standardback"
            (
            "EntryCategory" INT NOT NULL,
            "EquipmentTemplateId" INT NOT NULL,
            "EntryId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "SignalName" VARCHAR(128),
            "StoreInterval" DOUBLE,
            "AbsValueThreshold" DOUBLE,
            "PercentThreshold" DOUBLE,
            "EventName" VARCHAR(128),
            "EventSeverity" INT,
            "StartCompareValue" DOUBLE,
            "StartDelay" INT,
            "StandardName" INT,
            "Meanings" VARCHAR(255),
            "ControlName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("EntryCategory", "EntryId", "EquipmentTemplateId", "EventConditionId"));

            CREATE TABLE "tbl_standarddic"
            (
            "StandardDicId" INT NOT NULL,
            "SignalStandardName" VARCHAR(255) NOT NULL,
            "SignalStandardId" INT NOT NULL,
            "EquipmentLogicClass" VARCHAR(255) NOT NULL,
            "StoreInterval" INT,
            "AbsValueThreshold" DOUBLE,
            "PercentThreshold" DOUBLE,
            "StandardName" VARCHAR(255),
            "EventSeverity" INT,
            "EventLogicClass" VARCHAR(255),
            "EventClass" VARCHAR(255),
            "NetManageId" VARCHAR(255),
            "EquipmentAffect" VARCHAR(255),
            "BusinessAffect" VARCHAR(255),
            "CompareValue" VARCHAR(128),
            "StartDelay" VARCHAR(64),
            "ControlStandardName" VARCHAR(255),
            "ControlStandardId" INT,
            "ControlType" INT,
            "Unit" VARCHAR(128),
            "Description" VARCHAR(255),
            "Meanings" VARCHAR(255),
            "NodeType" INT,
            NOT CLUSTER PRIMARY KEY("StandardDicId"));

            CREATE TABLE "tbl_standarddiccontrol"
            (
            "StandardDicId" INT NOT NULL,
            "StandardType" INT NOT NULL,
            "EquipmentLogicClassId" INT NOT NULL,
            "EquipmentLogicClass" VARCHAR(128) NOT NULL,
            "ControlLogicClassId" INT,
            "ControlLogicClass" VARCHAR(128),
            "ControlStandardName" VARCHAR(255),
            "NetManageId" VARCHAR(255),
            "StationCategory" INT NOT NULL,
            "ModifyType" INT,
            "Description" VARCHAR(255),
            "ExtendFiled1" TEXT,
            "ExtendFiled2" TEXT,
            NOT CLUSTER PRIMARY KEY("StandardDicId", "StandardType", "StationCategory"));

            CREATE TABLE "tbl_standarddicevent"
            (
            "StandardDicId" INT NOT NULL,
            "StandardType" INT NOT NULL,
            "EquipmentLogicClassId" INT NOT NULL,
            "EquipmentLogicClass" VARCHAR(128) NOT NULL,
            "EventLogicClassId" INT,
            "EventLogicClass" VARCHAR(128),
            "EventClass" VARCHAR(255),
            "EventStandardName" VARCHAR(255),
            "NetManageId" VARCHAR(255),
            "EventSeverity" INT,
            "CompareValue" VARCHAR(128),
            "StartDelay" VARCHAR(64),
            "Meanings" VARCHAR(255),
            "EquipmentAffect" VARCHAR(255),
            "BusinessAffect" VARCHAR(255),
            "StationCategory" INT NOT NULL,
            "ModifyType" INT,
            "Description" VARCHAR(255),
            "ExtendFiled1" TEXT,
            "ExtendFiled2" TEXT,
            "ExtendFiled3" TEXT,
            NOT CLUSTER PRIMARY KEY("StandardDicId", "StandardType", "StationCategory"));

            CREATE TABLE "tbl_standarddicsig"
            (
            "StandardDicId" INT NOT NULL,
            "StandardType" INT NOT NULL,
            "EquipmentLogicClassId" INT NOT NULL,
            "EquipmentLogicClass" VARCHAR(128) NOT NULL,
            "SignalLogicClassId" INT,
            "SignalLogicClass" VARCHAR(128),
            "SignalStandardName" VARCHAR(255) NOT NULL,
            "NetManageId" VARCHAR(255),
            "StoreInterval" INT,
            "AbsValueThreshold" DOUBLE,
            "StatisticsPeriod" INT,
            "PercentThreshold" DOUBLE,
            "StationCategory" INT NOT NULL,
            "ModifyType" INT,
            "Description" VARCHAR(255),
            "ExtendFiled1" TEXT,
            "ExtendFiled2" TEXT,
            NOT CLUSTER PRIMARY KEY("StandardDicId", "StandardType", "StationCategory"));

            CREATE TABLE "tbl_standardrule"
            (
            "StandardRuleId" INT IDENTITY(1, 1) NOT NULL PRIMARY KEY,
            "StandardTemplateId" INT NOT NULL,
            "SignalName" VARCHAR(128),
            "EventName" VARCHAR(128),
            "Expression" VARCHAR(128),
            "Meanings" VARCHAR(128),
            "ControlName" VARCHAR(128),
            "StandardDicId" INT);

            CREATE TABLE "tbl_standardtemplate"
            (
            "StandardTemplateId" INT NOT NULL PRIMARY KEY,
            "StandardTemplateName" VARCHAR(255) NOT NULL,
            "StationCategory" INT NOT NULL,
            "EquipmentCategory" INT NOT NULL,
            "Vendor" VARCHAR(255) NOT NULL,
            "EquipmentModel" VARCHAR(255) NOT NULL,
            "MonitorModule" VARCHAR(255));

            CREATE TABLE "tbl_standardtemplatemap"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "StationCategory" INT NOT NULL,
            "StandardTemplateId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId","StationCategory","StandardTemplateId"));

            CREATE TABLE "tbl_standardtype"
            (
            "StandardId" INT NOT NULL,
            "StandardName" VARCHAR(255) NOT NULL,
            "StandardAlias" VARCHAR(255) NOT NULL,
            "Remark" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("StandardId"));
        </sql>
    </changeSet>
</databaseChangeLog>