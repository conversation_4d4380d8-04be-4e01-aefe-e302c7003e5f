<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-014" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            INSERT INTO "doortag" ("TagId", "TagName", "TagIcon", "TagColor", "TagDescribe") VALUES(1, '公安', 'icon-gongan', '#ADD8E6', '公安门禁');
            INSERT INTO "doortag" ("TagId", "TagName", "TagIcon", "TagColor", "TagDescribe") VALUES(2, '一级', 'icon-yiji', '#ADD8E6', '一级门禁');
            INSERT INTO "doortag" ("TagId", "TagName", "TagIcon", "TagColor", "TagDescribe") VALUES(3, 'IDC', 'icon-IDC', '#ADD8E6', 'IDC门禁');
            INSERT INTO "doortag" ("TagId", "TagName", "TagIcon", "TagColor", "TagDescribe") VALUES(4, '国密', 'icon-guomi', '#ADD8E6', '国密门禁');
        </sql>
    </changeSet>
</databaseChangeLog>