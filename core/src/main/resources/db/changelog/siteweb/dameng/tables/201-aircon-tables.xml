<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-201" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "aircon_autocontrolequipmentchangelog"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "StationId" INT,
            "StationName" VARCHAR(255),
            "MonitorUnitId" INT,
            "MonitorUnitName" VARCHAR(255),
            "VirtualEquipmentId" INT NOT NULL,
            "GroupName" VARCHAR(128),
            "GroupNameNew" VARCHAR(128),
            "OperateType" INT NOT NULL,
            "OperateTypeLabel" VARCHAR(16) NOT NULL,
            "OperateModule" INT NOT NULL,
            "OperateModuleLabel" VARCHAR(64) NOT NULL,
            "SubOperateType" INT,
            "SubOperateTypeLabel" VARCHAR(16),
            "SubObjectType" INT,
            "SubObjectTypeLabel" VARCHAR(64),
            "Operator" VARCHAR(128),
            "OperatorId" INT,
            "InsertTime" TIMESTAMP(0) NOT NULL,
            "ChangeContent" VARCHAR(4000),
            "ExtendField1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE TABLE "aircon_autocontrolpara"
            (
            "VirtualEquipmentId" INT NOT NULL,
            "VirtualEquipmentName" VARCHAR(128) NOT NULL,
            "GroupName" VARCHAR(128) NOT NULL,
            "StationId" INT NOT NULL,
            "SamplerAddress" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "ParaEnable" INT NOT NULL,
            "RollingCount" INT NOT NULL,
            "TempComputeMode" DOUBLE NOT NULL,
            "TempCoolAll" DOUBLE NOT NULL,
            "TempCoolStart" DOUBLE NOT NULL,
            "WorkTemp" DOUBLE NOT NULL,
            "TempInFanStop" DOUBLE NOT NULL,
            "TempBottomLow" DOUBLE NOT NULL,
            "TempSetting" DOUBLE NOT NULL,
            "RunPeriod" INT NOT NULL,
            "OperationInterval" INT NOT NULL,
            "TempDiff" DOUBLE NOT NULL,
            "FanInstall" INT NOT NULL,
            "TempFanStart" DOUBLE NOT NULL,
            "OutInTempDiff" DOUBLE NOT NULL,
            "TempOutFanStop" DOUBLE NOT NULL,
            "EnableWarm" INT NOT NULL,
            "TempHot" DOUBLE NOT NULL,
            "TempHotStart" DOUBLE NOT NULL,
            "TempHotAll" DOUBLE NOT NULL,
            "LastSuccussDeployTime" TIMESTAMP(0),
            "LastDeployState" INT,
            "LastDeployTime" TIMESTAMP(0),
            "UpdateTime" TIMESTAMP(0),
            "Operationor" INT,
            "Description" VARCHAR(255),
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("VirtualEquipmentId"));

            CREATE TABLE "aircon_batchcontrolequipmentmap"
            (
            "GroupId" VARCHAR(255) NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "UpdateId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("GroupId", "StationId", "EquipmentId"));

            CREATE TABLE "aircon_batchcontrolgroup"
            (
            "GroupId" VARCHAR(255) NOT NULL,
            "GroupName" VARCHAR(128) NOT NULL,
            "CreatorId" INT,
            "CreateTime" TIMESTAMP(0),
            "UpdateId" INT,
            "UpdateTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("GroupId"));

            CREATE TABLE "aircon_batchcontrolgroupchangelog"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "OperateType" INT NOT NULL,
            "OperateTypeLabel" VARCHAR(16) NOT NULL,
            "Operator" VARCHAR(128),
            "OperatorId" INT,
            "GroupId" VARCHAR(255) NOT NULL,
            "GroupName" VARCHAR(128),
            "GroupNameNew" VARCHAR(128),
            "InsertTime" TIMESTAMP(0) NOT NULL,
            "ChangeContent" VARCHAR(4000),
            "ExtendField1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE TABLE "aircon_batchcontrolrecord"
            (
            "RecordId" INT AUTO_INCREMENT NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "StdControlId" INT NOT NULL,
            "StdWorkModeFlag" INT NOT NULL,
            "AirStdTypeId" INT NOT NULL,
            "AirCommon2No" INT NOT NULL,
            "SerialNo" INT NOT NULL,
            "Uuid" VARCHAR(255) NOT NULL,
            "InsertTime" TIMESTAMP(0) NOT NULL,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("RecordId"));

            CREATE TABLE "aircon_complexindexmap"
            (
            "MapId" VARCHAR(255) NOT NULL,
            "StationId" INT NOT NULL,
            "VirtualEquipmentId" INT NOT NULL,
            "ComplexIndexId" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0),
            "UpdateId" INT,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MapId"));

            CREATE TABLE "aircon_equipmentcontrolpara"
            (
            "VirtualEquipmentId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "InsertTime" TIMESTAMP(0),
            "Operationor" INT,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("VirtualEquipmentId", "EquipmentId"));

            CREATE TABLE "aircon_fancontrolpara"
            (
            "VirtualEquipmentId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "InsertTime" TIMESTAMP(0),
            "Operationor" INT,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("VirtualEquipmentId", "EquipmentId"));

            CREATE TABLE "aircon_stdsignal"
            (
            "TypeId" INT NOT NULL,
            "StdSignalId" INT NOT NULL,
            "StdSignalName" VARCHAR(255) NOT NULL,
            "StdSignalUnit" VARCHAR(16),
            "StdSignalRemark" VARCHAR(255),
            "StdSignalType" INT NOT NULL,
            "NeedShow" INT NOT NULL,
            "CommandColor" INT NOT NULL,
            "BusinessTypeId" INT NOT NULL,
            "MapRequirement" INT NOT NULL,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("TypeId", "StdSignalId"));

            CREATE TABLE "aircon_stdsignaltype"
            (
            "SignalTypeId" INT NOT NULL,
            "SignalTypeName" VARCHAR(128) NOT NULL,
            "SignalTypeRemark" VARCHAR(255),
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SignalTypeId"));

            CREATE TABLE "aircon_stdtype"
            (
            "TypeId" INT NOT NULL,
            "TypeName" VARCHAR(128) NOT NULL,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("TypeId"));

            CREATE TABLE "aircon_tempcontrolpara"
            (
            "VirtualEquipmentId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "InsertTime" TIMESTAMP(0),
            "Operationor" INT,
            "BeOutTemp" INT,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("VirtualEquipmentId", "EquipmentId"));

            CREATE TABLE "aircon_templatestdsignalmap"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "TypeId" INT NOT NULL,
            "StdSignalId" INT NOT NULL,
            "DefaultValue" DECIMAL(18,0),
            "SwSignalId" INT,
            "SwSignalName" VARCHAR(128),
            "SwSignalChanelNum" INT,
            "SwCmdToken" VARCHAR(64),
            "SwParam" VARCHAR(64),
            "SwOperator" VARCHAR(64),
            "SwCmpValue" DOUBLE,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(128),
            "ExtendField1" VARCHAR(128),
            "ExtendField2" VARCHAR(128),
            "ExtendField3" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId", "TypeId", "StdSignalId"));

            CREATE TABLE "aircon_templatestdtype"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "TypeId" INT,
            "TypeName" VARCHAR(128),
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(128),
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId"));

            CREATE TABLE "aircon_zonecontroloperation"
            (
            "OperationId" INT AUTO_INCREMENT NOT NULL,
            "SchemeId" VARCHAR(255) NOT NULL,
            "OperationTime" VARCHAR(16) NOT NULL,
            "OperationCmdId" INT NOT NULL,
            "OperationCmdName" VARCHAR(64) NOT NULL,
            "Params" VARCHAR(64),
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "UpdateId" INT NOT NULL,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("OperationId"));

            CREATE TABLE "aircon_zonecontrolscheme"
            (
            "SchemeId" VARCHAR(255) NOT NULL,
            "SchemeName" VARCHAR(255) NOT NULL,
            "StartDate" VARCHAR(16) NOT NULL,
            "EndDate" VARCHAR(16) NOT NULL,
            "VirtualEquipmentId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "UpdateId" INT NOT NULL,
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("SchemeId"));
        </sql>
    </changeSet>
</databaseChangeLog>