<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-007" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_report"
            (
            "ReportId" INT NOT NULL,
            "ReportName" VARCHAR(255) NOT NULL,
            "Description" VARCHAR(255),
            "ReportFileId" VARCHAR(255) NOT NULL,
            "ReportFileName" VARCHAR(255),
            "PreviewImageName" VARCHAR(255),
            "CreateUserId" INT,
            "CreateUserName" VARCHAR(255),
            "CreateTime" TIMESTAMP(0),
            "UpdateUserId" INT,
            "UpdateUserName" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            "Version" VARCHAR(50),
            NOT CLUSTER PRIMARY KEY("ReportId"));

            CREATE TABLE "tbl_reportbrowehistory"
            (
            "ReportBroweHistoryId" int PRIMARY KEY AUTO_INCREMENT,
            "HistoryId" INT NOT NULL,
            "UserId" INT,
            "ReportId" INT,
            "ViewCount" INT,
            "LastBrowseTime" TIMESTAMP(0));

            CREATE TABLE "tbl_reportchart"
            (
            "ReportId" INT NOT NULL,
            "ChartId" INT NOT NULL,
            "ChartType" VARCHAR(20),
            "ChartName" VARCHAR(128),
            "XAxis" VARCHAR(128),
            "YAxis" VARCHAR(128),
            "Serials" VARCHAR(128),
            "SerialsName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("ChartId", "ReportId"));

            CREATE TABLE "tbl_reportgroup"
            (
            "Id" int PRIMARY KEY AUTO_INCREMENT,
            "ReportGroupId" INT NOT NULL,
            "GroupName" VARCHAR(255) NOT NULL,
            "UserId" INT,
            "GroupType" INT DEFAULT 0,
            "Description" VARCHAR(255));

            CREATE TABLE "tbl_reportgroupmap"
            (
            "ReportGroupMap" int PRIMARY KEY AUTO_INCREMENT,
            "ReportId" INT NOT NULL,
            "ReportGroupId" INT NOT NULL);

            CREATE TABLE "tbl_reportparameter"
            (
            "ParameterId" INT NOT NULL,
            "ParameterName" VARCHAR(128),
            "ParameterKey" VARCHAR(128),
            "ProcedureName" VARCHAR(128) NOT NULL,
            "ParameterString" TEXT,
            "ParameterField" VARCHAR(128),
            "ParameterType" VARCHAR(20),
            "ParameterFrom" VARCHAR(20),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ParameterId"));

            CREATE TABLE "tbl_reportparametermap"
            (
            "ReportId" INT NOT NULL,
            "ParameterId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("ParameterId", "ReportId"));

            CREATE TABLE "tbl_reportprocedure"
            (
            "ReportId" INT NOT NULL,
            "ProcedureName" VARCHAR(128) NOT NULL,
            "ParameterString" TEXT,
            "ReportColumn" CLOB,
            "ReportType" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ReportId"));

            CREATE TABLE "tbl_reportquery"
            (
            "ReportQueryId" int PRIMARY KEY AUTO_INCREMENT,
            "QueryID" INT,
            "ReportId" INT,
            "Name" VARCHAR(255),
            "TaskId" INT,
            "Description" VARCHAR(255),
            "QueryTime" TIMESTAMP(0));

            CREATE TABLE "tbl_reportqueryparameter"
            (
            "ReportQueryParameterId" int PRIMARY KEY AUTO_INCREMENT,
            "QueryParameterId" INT NOT NULL,
            "QueryID" INT,
            "ReportId" INT,
            "ParameterName" VARCHAR(255),
            "DataType" VARCHAR(64),
            "Value" CLOB);

            CREATE TABLE "tbl_reportrole"
            (
            "ReportRoleId" INT NOT NULL,
            "RoleName" VARCHAR(50) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ReportRoleId"));

            CREATE TABLE "tbl_reportrolemap"
            (
            "ReportId" INT NOT NULL,
            "ReportRoleId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("ReportId", "ReportRoleId"));

            CREATE TABLE "tbl_reportroleusermap"
            (
            "ReportRoleUserMapId" int PRIMARY KEY AUTO_INCREMENT,
            "ReportRoleId" INT NOT NULL,
            "UserId" INT NOT NULL);

            CREATE TABLE "tbl_reporttask"
            (
            "TaskId" INT NOT NULL,
            "TaskName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            "ReportId" INT NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "OutputTime" TIMESTAMP(0),
            "OutputInterval" INT,
            "OutputIntervalType" INT,
            "QueryPeriod" INT,
            "QueryPeriodType" INT,
            "QueryParameters" CLOB NOT NULL,
            "CommandText" VARCHAR(255),
            "CommandType" VARCHAR(128),
            "CenterType" INT DEFAULT 0 NOT NULL,
            "QueryCount" INT,
            "EmailOut" INT DEFAULT 0 NOT NULL,
            "EmailUrl" CLOB,
            "Creator" INT,
            "CreateTime" TIMESTAMP(0),
            "LastUpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("TaskId"));

            CREATE TABLE "tbl_reporttaskfile"
            (
            "FileId" INT AUTO_INCREMENT NOT NULL,
            "TaskId" INT,
            "ReportId" INT,
            "FileName" VARCHAR(128),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "QueryTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("FileId"));

            CREATE TABLE "tbl_reportusermap"
            (
            "ReportUserMapId" int PRIMARY KEY AUTO_INCREMENT,
            "ReportId" INT,
            "UserId" INT);

            CREATE TABLE "tbl_suitreport"
            (
            "SuitReportId" INT NOT NULL PRIMARY KEY,
            "SuitReportName" VARCHAR(255) NOT NULL,
            "Description" VARCHAR(255),
            "CreateUserId" INT,
            "CreateUserName" VARCHAR(255),
            "CreateTime" TIMESTAMP(0),
            "UpdateUserId" INT,
            "UpdateUserName" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0));

            CREATE TABLE "tbl_suitreportmap"
            (
            "SuitReportId" INT NOT NULL,
            "ReportId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SuitReportId","ReportId")
            );

            CREATE TABLE "tbl_suitreportrolemap"
            (
            "SuitReportId" INT NOT NULL,
            "ReportRoleId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SuitReportId","ReportRoleId"));
        </sql>
    </changeSet>
</databaseChangeLog>