<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-003" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_equipmentresource"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "HouseId" INT,
            "EquipmentCategory" INT NOT NULL,
            "Vendor" VARCHAR(255),
            "UsedDate" TIMESTAMP(0),
            "EquipmentModule" VARCHAR(128),
            "LocalNetwork" VARCHAR(255),
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourceaircon"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "AirconType" INT,
            "CoolingCapacity" DOUBLE,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourcebattery"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "BatteryType" INT,
            "RatedOutputVoltage" DOUBLE,
            "RatedCapacity" DOUBLE,
            "BatteryVoltage" INT,
            "BatteryCapacity" DOUBLE,
            "BatteryCounts" INT,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourcedcpower"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "ModuleCapacity" DOUBLE,
            "ModuleNums" INT,
            "BatteryNums" INT,
            "BatteryCounts" INT,
            "BatteryCapacity" DOUBLE,
            "BatteryVoltage" INT,
            "SupplyInfo" VARCHAR(255),
            "SystemCapacities" DOUBLE,
            "BatteryCapacities" DOUBLE,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourcegenset"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "RatedOutputPower" DOUBLE,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourceitem"
            (
            "EntryItemId" INT NOT NULL,
            "EntryId" INT NOT NULL,
            "EntryValue" VARCHAR(255),
            "ItemId" INT NOT NULL,
            "ItemValue" VARCHAR(255) NOT NULL,
            "ParentEntryId" INT DEFAULT 0 NOT NULL,
            "ParentItemId" INT DEFAULT 0 NOT NULL,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EntryItemId"));

            CREATE TABLE "tbl_equipmentresourcepower"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "PowerType" INT,
            "ModuleCapacity" INT,
            "ModuleNums" INT,
            "BatteryNums" INT,
            "BatteryCounts" INT,
            "BatteryCapacity" DOUBLE,
            "BatteryVoltage" INT,
            "SupplyInfo" VARCHAR(255),
            "SystemCapacities" DOUBLE,
            "BatteryCapacities" DOUBLE,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourcetransformer"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "TransformerType" INT,
            "RatedCapacity" DOUBLE,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_equipmentresourceups"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "UPSType" INT,
            "RatedCapacity" DOUBLE,
            "ModuleNums" INT,
            "BatteryNums" INT,
            "BatteryCounts" INT,
            "BatteryCapacity" DOUBLE,
            "BatteryVoltage" INT,
            "SupplyInfo" VARCHAR(255),
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));
        </sql>
    </changeSet>
</databaseChangeLog>