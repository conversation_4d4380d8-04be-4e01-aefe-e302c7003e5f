<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-006" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_menuitem"
            (
            "MenuItemId" INT NOT NULL,
            "ParentId" INT,
            "Path" VARCHAR(255),
            "Title" VARCHAR(255),
            "Icon" VARCHAR(255),
            "Selected" INT,
            "Expanded" INT,
            "PathMatch" VARCHAR(255),
            "LayoutPosition" INT,
            "IsSystemConfig" INT,
            "IsExternalWeb" INT,
            "MenuHasNavigation" INT,
            "Description" VARCHAR(255),
            CONSTRAINT "PK_TBL_MenuItem_ID" PRIMARY KEY("MenuItemId"),
            CHECK("Selected" >= 0)
            ,CHECK("Expanded" >= 0)
            ,CHECK("IsSystemConfig" >= 0)
            ,CHECK("IsExternalWeb" >= 0)
            ,CHECK("MenuHasNavigation" >= 0));

            CREATE TABLE "tbl_menuitems"
            (
            "MenuItemsId" INT NOT NULL,
            "ParentMenuItemsId" INT,
            "MenuItemsName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MenuItemsId"));

            CREATE TABLE "tbl_menuitemstructuremap"
            (
            "MenuItemStructureMapId" INT NOT NULL,
            "MenuProfileId" INT NOT NULL,
            "MenuStructureId" INT NOT NULL,
            "MenuItemId" INT NOT NULL,
            "SortIndex" INT,
            CONSTRAINT "PK_TBL_MenuItemStructureMap_ID" PRIMARY KEY("MenuItemStructureMapId"));

            CREATE TABLE "tbl_menuprofileinfo"
            (
            "MenuProfileId" INT NOT NULL,
            "Name" VARCHAR(255),
            "Checked" INT,
            "Description" VARCHAR(255),
            CONSTRAINT "PK_TBL_MenuProfileInfo_ID" PRIMARY KEY("MenuProfileId"),
            CHECK("Checked" >= 0));

            CREATE TABLE "tbl_menus"
            (
            "MenusId" INT NOT NULL,
            "MenusName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("MenusId"));

            CREATE TABLE "tbl_menusmap"
            (
            "MenusId" INT NOT NULL,
            "MenuItemsId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("MenuItemsId", "MenusId"));

            CREATE TABLE "tbl_menustructureinfo"
            (
            "MenuStructureId" INT NOT NULL,
            "MenuProfileId" INT,
            "ParentId" INT,
            "Title" VARCHAR(255),
            "Icon" VARCHAR(255),
            "Selected" INT,
            "Expanded" INT,
            "Hidden" INT,
            "SortIndex" INT,
            "IsSystem" INT,
            "Description" VARCHAR(255),
            CONSTRAINT "PK_TBL_MenuStructureInfo_ID" PRIMARY KEY("MenuStructureId"),
            CHECK("Selected" >= 0)
            ,CHECK("Expanded" >= 0)
            ,CHECK("Hidden" >= 0)
            ,CHECK("IsSystem" >= 0));
        </sql>
    </changeSet>
</databaseChangeLog>