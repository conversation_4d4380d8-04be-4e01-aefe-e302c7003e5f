<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-119" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "schedule"
            (
            "ScheduleId" INT AUTO_INCREMENT NOT NULL,
            "ShiftGroupMapId" INT,
            "ShiftId" INT,
            "ScheduleTime" DATE,
            NOT CLUSTER PRIMARY KEY("ScheduleId"));

            CREATE TABLE "shift"
            (
            "ShiftId" INT AUTO_INCREMENT NOT NULL,
            "ShiftName" VARCHAR(50),
            "ShiftStartTime" TIME(0),
            "ShiftEndTime" TIME(0),
            "ShiftColor" VARCHAR(50),
            "DepartmentId" INT,
            NOT CLUSTER PRIMARY KEY("ShiftId"));

            CREATE TABLE "shiftgroup"
            (
            "ShiftGroupId" INT AUTO_INCREMENT NOT NULL,
            "ShiftGroupName" VARCHAR(50),
            "Remark" VARCHAR(255),
            "DepartmentId" INT,
            NOT CLUSTER PRIMARY KEY("ShiftGroupId"));

            CREATE TABLE "shiftgroupmap"
            (
            "ShiftGroupMapId" INT AUTO_INCREMENT NOT NULL,
            "ShiftGroupId" INT,
            "EmployeeId" INT,
            "DisplayIndex" INT,
            NOT CLUSTER PRIMARY KEY("ShiftGroupMapId"));
        </sql>
    </changeSet>
</databaseChangeLog>