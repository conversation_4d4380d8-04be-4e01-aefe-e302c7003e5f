<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-1223" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE ttsconfig
            (
                TtsConfigId    int AUTO_INCREMENT NOT NULL,
                TtsConfigKey   varchar(255)   DEFAULT NULL COMMENT '键名',
                TtsConfigValue varchar(10000) DEFAULT NULL COMMENT '对应键值',
                Description    varchar(255)   DEFAULT NULL COMMENT '描述信息',
                PRIMARY KEY (TtsConfigId),
                UNIQUE (TtsConfigId),
                UNIQUE (TtsConfigKey)
            );

            CREATE TABLE ttsfilterconfig
            (
                TtsFilterConfigId int AUTO_INCREMENT NOT NULL COMMENT '主键自增id',
                TtsStrategyId     int            DEFAULT NULL COMMENT '所属策略的主键id',
                TtsConfigKey      varchar(255)   DEFAULT NULL COMMENT '键名',
                TtsConfigValue    varchar(10000) DEFAULT NULL COMMENT '键值',
                PRIMARY KEY (TtsFilterConfigId)
            );

            CREATE TABLE ttsstrategy
            (
                TtsStrategyId      int AUTO_INCREMENT NOT NULL COMMENT '主键自增id',
                StrategyName       varchar(50)  DEFAULT NULL COMMENT '策略名称',
                Enable             int          DEFAULT 0 COMMENT '是否启用 0 否 1是',
                EffectiveStartTime datetime     DEFAULT NULL COMMENT '生效开始时间',
                EffectiveEndTime   datetime     DEFAULT NULL COMMENT '生效结束时间',
                Description        varchar(255) DEFAULT NULL COMMENT '描述',
                PRIMARY KEY (TtsStrategyId)
            );
        </sql>
    </changeSet>
</databaseChangeLog>