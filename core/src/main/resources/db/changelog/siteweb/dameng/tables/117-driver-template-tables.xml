<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-117" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_drivestructuretemplate"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "filePath" VARCHAR(100),
            "pid" INT,
            "fileId" BIGINT,
            "isDisk" TINYINT,
            "isUpload" TINYINT,
            "isFill" TINYINT,
            "driveTemplateId" INT,
            "isLeaf" TINYINT,
            "uploadTiming" TINYINT,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_drivetemplate"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "driveTemplateName" VARCHAR(100),
            "driverTemplateDescribe" VARCHAR(255),
            "isDefaultTemplate" TINYINT,
            "driveTemplateType" INT,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_equipmentext"
            (
            "equipmentId" INT NOT NULL,
            "driveTemplateId" INT,
            "fileId" BIGINT,
            "isUpload" TINYINT,
            "isReset" TINYINT,
            "fieldHash" INT,
            NOT CLUSTER PRIMARY KEY("equipmentId"));
        </sql>
    </changeSet>
</databaseChangeLog>