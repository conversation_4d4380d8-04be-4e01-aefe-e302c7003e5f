<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-115" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "alarmvideolink"
            (
            "AlarmVideoLinkId" INT AUTO_INCREMENT NOT NULL,
            "ConfigName" VARCHAR(255),
            "Description" VARCHAR(255),
            "UsedStatus" TINYINT,
            "DepartmentId" INT,
            "LinkType" VARCHAR(16),
            "OperationType" VARCHAR(16),
            "SnapshotCount" INT,
            "SnapshotInterval" INT,
            NOT CLUSTER PRIMARY KEY("AlarmVideoLinkId"));

            CREATE TABLE "alarmvideolinkevent"
            (
            "AlarmVideoLinkEventId" INT AUTO_INCREMENT NOT NULL,
            "AlarmVideoLinkMapId" INT,
            "EventId" INT,
            "EventConditionId" INT,
            NOT CLUSTER PRIMARY KEY("AlarmVideoLinkEventId"));

            CREATE TABLE "alarmvideolinkmap"
            (
            "AlarmVideoLinkMapId" INT AUTO_INCREMENT NOT NULL,
            "AlarmVideoLinkId" INT,
            "EquipmentId" INT,
            "EventId" INT,
            "EventConditionId" INT,
            "CameraId" INT,
            "CameraIds" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("AlarmVideoLinkMapId"));

            CREATE OR REPLACE  INDEX "IDX_EquipmentId_EventId_EventConditionId" ON "alarmvideolinkmap"("EquipmentId" ASC,"EventId" ASC,"EventConditionId" ASC);

            CREATE TABLE "alarmchangesnapshot"
            (
            "SequenceId" VARCHAR(128) NOT NULL,
            "OperationType" INT NOT NULL,
            "SnapshotUrl" VARCHAR(128) NOT NULL,
            "CameraId" INT NOT NULL,
            "CameraName" VARCHAR(128),
            "StartTime" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("SequenceId"));
        </sql>
    </changeSet>
</databaseChangeLog>