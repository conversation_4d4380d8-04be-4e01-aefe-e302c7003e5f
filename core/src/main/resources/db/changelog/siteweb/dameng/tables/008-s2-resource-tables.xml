<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-008" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_resourceequipment"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(255),
            "SystemType" VARCHAR(255),
            "SystemId" VARCHAR(255),
            "SensorLocation" VARCHAR(255),
            "MonitoredEquipmentName" VARCHAR(255),
            "RateCapacity" VARCHAR(255),
            "DeviceId" VARCHAR(255),
            "AssetId" VARCHAR(255),
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "IsConfig" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentId", "StationId"));

            CREATE TABLE "tbl_resourcehouse"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "HouseId" INT NOT NULL,
            "HouseName" VARCHAR(255),
            "HouseNo" DECIMAL(18,0),
            "SystemNo" DECIMAL(18,0),
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "IsConfig" INT,
            NOT CLUSTER PRIMARY KEY("HouseId", "StationId"));

            CREATE TABLE "tbl_resourcesignal"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "EquipmentTemplateName" VARCHAR(255),
            "SignalId" INT NOT NULL,
            "SignalName" VARCHAR(255),
            "ChannelNo" INT,
            "ChannelLevel" INT,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "IsConfig" INT,
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId", "SignalId"));

            CREATE TABLE "tbl_resourcestation"
            (
            "StationId" INT NOT NULL,
            "StationName" VARCHAR(255),
            "StationAddress" VARCHAR(255),
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "IsConfig" INT,
            NOT CLUSTER PRIMARY KEY("StationId"));
        </sql>
    </changeSet>
</databaseChangeLog>