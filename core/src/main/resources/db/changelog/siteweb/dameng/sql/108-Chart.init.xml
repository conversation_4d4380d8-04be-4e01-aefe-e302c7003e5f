<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-108" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            -- 图表接口
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (25, '静态数据', '通用', '/', 'GET', '[]', 'return [["区域","A楼","B楼","C楼"],["停电站点数",600,800,50],["正常电站点数",330,1000,80]];');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (27, '局站/FSU/设备中断统计', '运营商多站点', '/api/interruptstatistics?resourceStructureIds={resourceStructureIds}', 'GET', '[{"name": "resourceStructureIds", "array": false, "source": "component", "meaning": "层级(多选)", "children": [], "required": false, "component": "HierarchySelectorComponent", "customData": "", "defaultValue": ""}]', 'const count = data &amp;&amp; data.station &amp;&amp; data.station.count || 0;
            const interrupt = data &amp;&amp; data.station &amp;&amp; data.station.interrupt || 0;
            return count + ''/'' + interrupt;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (28, '用电分析', '运营商多站点', '/api/energy/total/useElectricityTrendAnalysis?startTime={startTime}&amp;endTime={endTime}&amp;businessTypeId=2&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "genericList", "meaning": "时间频率", "required": true, "component": "", "customData": "[{\"label\":\"每月\",\"value\":\"m\"},{\"label\":\"每日\",\"value\":\"d\"}]", "defaultValue": ""}, {"name": "startTime,endTime", "array": false, "source": "component", "meaning": "时间长度", "required": true, "component": "TimeRangeComponent", "customData": "", "defaultValue": ""}]', 'const result = [[''alarmCount''].concat(data.xaxisData)];
            data.series.map(item=&gt;{
            let arr = [];
            arr.push(item.name);
            arr = arr.concat(item.data)
            result.push(arr)
            })
            return result;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (29, '多班组值班人员', '通用', '/api/shiftgroupmap/duty?shiftGroupIds={ids}', 'GET', '[{"name": "ids", "array": false, "source": "input", "meaning": "班组id(多个逗号分隔，按顺序填入)", "required": true, "component": "", "customData": "", "defaultValue": "1,2"}]', 'data.map((item,i)=&gt;{
            if(!item.employeeTitle) data[i].employeeTitle = 0;
            })
            data.sort(function(a,b){
            if(a.employeeTitle&lt;b.employeeTitle) return 1;
            if(a.employeeTitle&gt;b.employeeTitle) return -1;
            if(a.employeeTitle=b.employeeTitle) return 0;
            })
            const list = util.formatToTable(data, [''编号'',''班组人员'',''联系方式一'',''联系方式二''], [''displayIndex'',''employeeName'',''phone'',''mobile''])//二参是自定义表头
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (30, '服务状态', '服务', 'https://10.163.100.171:8000/api/v1/software?pageIndex=1&amp;pageSize=999', 'GET', '[]', 'const type = {
            0: ''不存在'',
            1: ''已创建'',
            2: ''运行中'',
            3: ''暂停'',
            4: ''重启中'',
            5: ''迁移中'',
            6: ''停止'',
            7: ''死亡''
            }
            const arr = data.data.list;
            arr.map(item =&gt; item.currentState = type[item.Status]);
            const list = util.formatToTable(arr, [''编号'',''服务名称'',''当前状态''], [''softwareId'',''softwareAlias'',''currentState''])
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (31, '设备数量统计', '通用', '/api/equipmentcategorystatisticsstate?deviceCategoryIds=1503,1504&amp;objectId=517000001&amp;pageCategory=2', 'GET', '[]', '// 第三种情况,接口返回数据data格式是对象的数组,如下
            const list = util.formatToTable(data, [''编号'',''数量'',], [''name'',''value'',])
            return list;
            //如果要对时间进行格式化
            return util.arrArrFormatTime2(list, ''$M:$d'');//二参格式可选$[y|Q|M|W|d|h|m|s] ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (32, '权限基站汇总PUE', 'energy', '/api/energy/energyapi/telcom/PUEOfArea?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "timeType", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "userId", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', 'return 3');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (33, '各用能类型总量', 'energy', '/api/energy/energyapi/telcom/electCategoryOfRole?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "timeType", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "userId", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', 'const arr = [data];
            const list = util.formatToTable(arr, [''市电'',''油机'',''绿电''], [''cityElectValue'',''oilElectValue'',''greenElectValue''])//二参是自定义表头
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (34, '总用电量及同环比', 'energy', '/api/energy/energyapi/telcom/totalEnergyUseOfRole?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data.totalElectricityValue;

            //return data.yoyTotalElectricityValue;

            //return data.qoqTotalElectricityValue;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (35, '碳排放及同环比', 'energy', '/api/energy/energyapi/telcom/totalCarbonUseOfRole?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', 'return data.totalCarbonValue;
            ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (36, '各用能类型碳排放总量', 'energy', '/api/energy/energyapi/telcom/carbonCategoryOfRole?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data.cityElectValue;
            //return data.oilElectValue;
            //return data.greenElectValue; ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (37, '某基站用碳及用能总量', 'energy', '/api/energy/energyapi/telcom/oneRoomEnergyAndCarbon?resourceStructureId={resourceStructureId}', 'GET', '[{"name": "resourceStructureId", "array": false, "source": "input", "meaning": "节点ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data.energyTotal;
            //

            //return data.carbonTotal;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (38, '用电分项占比', 'energy', '/api/energy/energyapi/telcom/electCategoryProportion?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '

            // 第三种情况,接口返回数据data格式是对象的数组,如下
            // [{displayIndex:1, employeeName:"a接口用户", phone: 123456},
            // {displayIndex:2, employeeName:"b接口用户", phone: 654321},
            // {displayIndex:3, employeeName:"c接口用户", phone: 112233},]
            const list = util.formatToTable(data, [''编号'',''班组人员'',''联系方式''], [''displayIndex'',''employeeName'',''phone''])//二参是自定义表头
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (39, '下级用能碳list接口', 'energy', '/api/energy/energyapi/telcom/nextLevelCarbonRank?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "timeType", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "timeType", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', 'const list = util.formatToTable(data, [''层级'',''市电碳排放'',''油机发电碳排放'',''绿电碳抵消''], [''resourceStructureName'',''cityElectCarbonValue'',''oilElectCarbonValue'',''greenElectCarbonValue''])//二参是自定义表头
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (40, '各用能类型碳排放年趋势', 'energy', '/api/energy/energyapi/telcom/carbonCategoryTrend?userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "userId", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '
            // 第三种情况,接口返回数据data格式是对象的数组,如下
            // [{displayIndex:1, employeeName:"a接口用户", phone: 123456},
            // {displayIndex:2, employeeName:"b接口用户", phone: 654321},
            // {displayIndex:3, employeeName:"c接口用户", phone: 112233},]
            const list = util.formatToTable(data, [''时间'',''市电碳排放'',''油机发电碳排放'',''绿电碳抵消''], [''time'',''cityElectCarbonValue'',''oilElectCarbonValue'',''greenElectCarbonValue''])//二参是自定义表头
            return util.arrArrFormatTime(list, ''$M'');//二参格式可选$[y|Q|M|W|d|h|m|s] ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (41, '权限基站汇总PUE趨勢', 'energy', '/api/energy/energyapi/telcom/PUEOfAreaTrend?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "userId", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "timeType", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', '
            // 第三种情况,接口返回数据data格式是对象的数组,如下
            // [{displayIndex:1, employeeName:"a接口用户", phone: 123456},
            // {displayIndex:2, employeeName:"b接口用户", phone: 654321},
            // {displayIndex:3, employeeName:"c接口用户", phone: 112233},]
            const list = util.formatToTable(data, [''时间'',''PUE值''], [''time'',''avgValue''])//二参是自定义表头
            return util.arrArrFormatTime(list, ''$M'');//二参格式可选$[y|Q|M|W|d|h|m|s] ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (42, '各用能类型总量年趋势', 'energy', '/api/energy/energyapi/telcom/energyCategoryTrend?userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '
            //const list = util.formatToTable(data, [''编号'',''班组人员'',''联系方式''], [''displayIndex'',''employeeName'',''phone''])//二参是自定义表头
            //如果要对时间进行格式化
            //return util.arrArrFormatTime2(list, ''$M'');//二参格式可选$[y|Q|M|W|d|h|m|s]

            //堆叠图
            //const list = util.formatToTable(data, [''层级'',''市电碳排放'',''油机发电碳排放'',''绿电碳抵消''], [''resourceStructureName'',''cityElectCarbonValue'',''oilElectCarbonValue'',''greenElectCarbonValue''])//二参是自定义表头
            //return list;

            //趋势图
            //const list = util.formatToTable(data, [''时间'',''PUE值''], [''time'',''avgValue''])//二参是自定义表头
            //return util.arrArrFormatTime(list, ''$M'');//二参格式可选$[y|Q|M|W|d|h|m|s]
            const list = util.formatToTable(data, [''时间'',''市电'',''油机发电'',''绿电''], [''time'',''cityElectValue'',''oilElectValue'',''greenElectValue''])//二参是自定义表头
            return util.arrArrFormatTime(list, ''$M'');//二参格式可选$[y|Q|M|W|d|h|m|s] ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (43, '各用能类型碳排放总量', 'energy', '/api/energy/energyapi/telcom/carbonCategoryOfRole?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', 'const arr = [data];
            const list = util.formatToTable(arr, [''市电碳排放'',''油机碳排放'',''绿电碳抵消''], [''cityElectCarbonValue'',''oilElectCarbonValue'',''greenElectCarbonValue''])//二参是自定义表头
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (44, '下级用能排名', 'energy', '/api/energy/energyapi/telcom/nextLevelEnergyRank?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', 'const list = util.formatToTable(data, [''层级'',''市电'',''油机发电'',''绿电''], [''resourceStructureName'',''cityElectValue'',''oilElectValue'',''greenElectValue''])//二参是自定义表头
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (45, '碳抵消同环比', 'energy', '/api/energy/energyapi/telcom/greenCarbonUseOfRole?timeType={timeType}&amp;userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "人员ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data.greenElectCarbonValue;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (46, '各用能类型碳排放年趋势(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/carbonCategoryTrend?userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "input", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "-1"}]', 'const list = util.formatToTable(data, [''时间'',''市电碳排放'',''油机发电碳排放'',''绿电碳抵消''],   [''time'',''totalValue1'',''totalValue2'',''totalValue3''])//二参是自定义表头
            return util.arrArrFormatTime(list, ''$M'');
            //return util.arrArrFormatTime(list, ''$M'');//二参格式可选$[y|Q|M|W|d|h|m|s] ');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (47, '下级各用能类型总量排名(结构堆叠)(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/nextLevelEnergyRank?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}, {"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', 'const list = util.formatToTable(data, [''名称'',''市电'',''油机发电'',''绿电''],   [''resourceStructureName'',''value1'',''value2'',''value3''])//二参是自定义表头
            return list;//二参格式可选$[y|Q|M|W|d|h|m|s]');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (48, '总用电量及同环比(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/totalEnergyUseOfRole?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '
            return data;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (49, '碳排放及同环比(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/totalCarbonUseOfRole?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (50, '各用能类型总量(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/electCategoryOfRole?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (51, '碳抵消同环比(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/greenCarbonUseOfRole?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (52, '各用能类型碳排放总量(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/carbonCategoryOfRole?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '
            return list;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (53, '某基站用碳及用能总量(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/carbonCategoryTrend?resourceStructureId={resourceStructureId}', 'GET', '[{"name": "resourceStructureId", "array": false, "source": "input", "meaning": "层级ID", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (54, '各用能类型总量年趋势(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/energyCategoryTrend?userId={userId}', 'GET', '[{"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data;');
            insert into "chartapi" ("ApiId","ApiName","Category","Url","Method","ParamSchema","Transform") values (55, '下级各用能类型碳排放排名(结构堆叠)(动态获取)', '能耗修改', '/api/energy/energyapi/telcom/dynamic/nextLevelCarbonRank?userId={userId}&amp;timeType={timeType}', 'GET', '[{"name": "timeType", "array": false, "source": "input", "meaning": "时间类型", "children": [], "required": true, "component": "", "customData": "", "defaultValue": ""}, {"name": "userId", "array": false, "source": "session", "meaning": "用户Id", "children": [], "required": true, "component": "", "customData": "", "defaultValue": "personId"}]', '// 第一种情况,接口返回数据data格式是二维数组,如下
            // [["区域","测试"],["停电站点数",0]]
            return data;');



            -- 图表样式
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (1, '基础柱状图', 1, 'barchart.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (2, '横向柱状图', 1, 'horizontalbarchart.png', 'option = {
            grid: {
            bottom: 80
            },
            yAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            xAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (3, '堆叠柱状图', 1, 'stackbarchart.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''axis'',
            position: '''',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };
            ');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (4, '折柱图', 1, 'linebarchart.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (5, '堆叠折柱图', 1, 'stackbarlinechart.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''axis'',
            position: '''',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };
            ');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (6, '折柱图双y轴', 1, 'linebarchartdoubleyaxis.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: [{
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: 1000,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            }, {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: 100,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            }],
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (7, '堆叠折柱图双y轴', 1, 'stackbarlinechartdoubleyaxis.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: [{
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: 1000,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            }, {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: 100,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            }],
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''axis'',
            position: '''',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };
            ');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (8, '普通饼图', 2, 'piechart.png', 'option = {
            grid: {
            bottom: 80
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (9, '南丁格尔玫瑰饼图', 2, 'rosepiechart.png', 'option = {
            grid: {
            bottom: 80
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (10, '圆环图', 2, 'ringchart.png', 'option = {
            grid: {
            bottom: 80
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (11, '南丁格尔玫瑰圆环图', 2, 'roseringchart.png', 'option = {
            grid: {
            bottom: 80
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (12, '普通折线图', 0, 'linechart.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (13, '面积图', 0, 'arealinechart.png', 'option = {
            grid: {
            bottom: 80
            },
            xAxis: {
            type: ''category'',
            data: null,
            show: true,
            axisTick: { show: false },
            boundaryGap: true,
            silent: true,
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'', //x轴轴线颜色
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//x轴文字
            show: true,
            overflow: ''break'',
            width: null,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13,
            ellipsis: ''...''
            }
            },
            yAxis: {
            type: ''value'',
            show: true,
            splitNumber: 4,
            max: null,
            min: 0,
            silent: true,
            textStyle: { color: ''#BFDFFF'', fontFamily: '''', fontSize: 14 },
            axisLine: {//轴线
            show: true,
            lineStyle: {
            color: ''#BFDFFF'',
            opacity: 0.2,
            width: 1
            }
            },
            axisLabel: {//y轴文字
            show: true,
            color: ''#BFDFFF'',
            fontFamily: '''',
            fontSize: 13
            },
            splitLine: {//分割线
            lineStyle: {
            width: 1,
            color: ''#BFDFFF'',
            opacity: 0.1
            }
            }
            },
            dataZoom: [],
            dataset: {
            source: [
            ]
            },
            tooltip: {
            show: true,
            trigger: ''item'',
            position: ''top'',
            textStyle: {
            color: ''#bfdfff'',
            fontSize: 12
            },
            backgroundColor: ''transparent'' || ''rgba(0,127,255,0.1)'',
            borderWidth: 1,
            borderColor: ''rgba(0,127,255,0.3)'',
            padding: null,
            axisPointer: {
            type: "none"
            },
            },
            legend: {//图例
            type: "scroll",
            pageIconColor: ''#aaa'',
            pageIconInactiveColor: ''#2f4554'',
            pageTextStyle: {
            color: "#aaa"
            },
            show: true,
            itemHeight: 5,
            bottom: 15,
            itemGap: 40,
            },
            series: [],
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (14, '普通仪表盘', 5, 'gaugechart.png', 'option = {
            series: [
            {
            name: ''Pressure'',
            type: ''gauge'',
            radius: ''100%'',
            center: [''50%'', ''60%''],
            startAngle: 180,
            endAngle: 0,
            splitNumber: 5,
            min: 1,
            max: 200,
            data:[],
            pointer: {
            length: ''70%'',
            width: 4,
            offsetCenter: [0, 0]
            },
            progress: {
            show: false,
            overlap: false,
            roundCap: false,
            clip: false,
            itemStyle: {
            borderWidth: 1,
            borderColor: ''#464646''
            }
            },
            axisTick: {
            show: true,
            length: 10,
            splitNumber: 5,
            distance: 0,
            lineStyle: {
            width: 1,
            color: ''#ffffff''
            }
            },
            axisLabel: {
            show: true,
            distance: 50,
            textStyle: {
            fontSize: 14,
            color: ''#3895ca''
            }
            },
            splitLine: {
            show: true,
            length: 25,
            distance: -27.5,
            lineStyle: {
            width: 1,
            color: ''#ffffff'',
            opacity: 1
            }
            },
            axisLine: {
            show: true,
            lineStyle: {
            width: 30,
            color: [
            [
            1,
            {
            colorStops: [//表盘最外圈颜色和百分比
            {
            offset: 0,
            color: ''#20c374''
            },
            {
            offset: 0.5,
            color: ''#FFFF00''
            },
            {
            offset: 1,
            color: ''#d15233''
            }
            ],
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            type: ''linear'',
            global: false
            }
            ]
            ]
            }
            },
            anchor: {
            showAbove: true,
            size: 9.5,
            itemStyle: {
            borderColor: ''rgba(251, 251, 251, 1)'',
            color: ''rgba(0, 0, 0, 1)'',
            borderWidth: 2.5
            }
            },
            title: {
            show: true,
            textStyle: {
            fontSize: 30,
            color: ''#FFFFFF''
            }
            },
            detail: {
            show: true,
            formatter: ''{value}'',
            fontSize: 30,
            color: ''#FFD237''
            }
            }
            ]
            };
            ');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (15, '基础雷达图', 4, 'radarchart.png', 'option = {
            legend: {
            right: ''0%'',
            textStyle: {
            color: ''#BFDFFF''
            },
            padding: [0, 0, 100, 0],
            data: []
            },
            tooltip: {
            show: true
            },
            radar: {
            indicator: [],
            axisName: {
            color: ''#BFDFFF'',
            fontSize: 12
            },
            axisLine: {
            lineStyle: {
            color: ''#BFDFFF'',
            fontSize: 1,
            opacity: 0.5
            }
            },
            splitLine: {
            lineStyle: {
            color: ''#BFDFFF'',
            fontSize: 2
            }
            }
            },
            series: [
            {
            type: ''radar'',
            data: []
            },
            ]
            };');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (16, '普通表格', 6, 'table.png', '');
            insert into "chartstyle" ("StyleId","StyleName","ChartId","Thumbnail","Expression") values (17, '文本数字', 3, 'textnumber.png', '');



            -- 图表主题
            insert into "charttheme" ("ThemeId","themeName","themeCode","themeData","themeDefault") values (1, '默认', 'default', '{"bar": {"itemStyle": {"barBorderColor": "#ccc", "barBorderWidth": 0}}, "geo": {"label": {"color": "#000"}, "emphasis": {"label": {"color": "rgb(100,0,0)"}, "itemStyle": {"areaColor": "rgba(255,215,0,0.8)", "borderColor": "#444", "borderWidth": 1}}, "itemStyle": {"areaColor": "#eee", "borderColor": "#444", "borderWidth": 0.5}}, "map": {"label": {"color": "#000"}, "emphasis": {"label": {"color": "rgb(100,0,0)"}, "itemStyle": {"areaColor": "rgba(255,215,0,0.8)", "borderColor": "#444", "borderWidth": 1}}, "itemStyle": {"areaColor": "#eee", "borderColor": "#444", "borderWidth": 0.5}}, "pie": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "line": {"smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderWidth": 1}, "lineStyle": {"width": 2}, "symbolSize": 4}, "color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"], "gauge": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "graph": {"color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"], "label": {"color": "#eee"}, "smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderColor": "#ccc", "borderWidth": 0}, "lineStyle": {"color": "#aaa", "width": 1}, "symbolSize": 4}, "radar": {"smooth": false, "symbol": "emptyCircle", "itemStyle": {"borderWidth": 1}, "lineStyle": {"width": 2}, "symbolSize": 4}, "title": {"textStyle": {"color": "#464646"}, "subtextStyle": {"color": "#6E7079"}}, "funnel": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "legend": {"textStyle": {"color": "#333"}}, "sankey": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "boxplot": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "logAxis": {"axisLine": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": true, "lineStyle": {"color": ["#E0E6F1"]}}}, "scatter": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "toolbox": {"emphasis": {"iconStyle": {"borderColor": "#666"}}, "iconStyle": {"borderColor": "#999"}}, "tooltip": {"axisPointer": {"lineStyle": {"color": "#ccc", "width": 1}, "crossStyle": {"color": "#ccc", "width": 1}}}, "dataZoom": {"textStyle": {}, "handleSize": "undefined%"}, "parallel": {"itemStyle": {"borderColor": "#ccc", "borderWidth": 0}}, "timeAxis": {"axisLine": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": false, "lineStyle": {"color": ["#E0E6F1"]}}}, "timeline": {"label": {"color": "#A4B1D7"}, "emphasis": {"label": {"color": "#A4B1D7"}, "itemStyle": {"color": "#FFF"}, "controlStyle": {"color": "#A4B1D7", "borderColor": "#A4B1D7", "borderWidth": 1}}, "itemStyle": {"color": "#A4B1D7", "borderWidth": 1}, "lineStyle": {"color": "#DAE1F5", "width": 2}, "controlStyle": {"color": "#A4B1D7", "borderColor": "#A4B1D7", "borderWidth": 1}, "checkpointStyle": {"color": "#316bf3", "borderColor": "fff"}}, "markPoint": {"label": {"color": "#eee"}, "emphasis": {"label": {"color": "#eee"}}}, "textStyle": {}, "valueAxis": {"axisLine": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": false, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": true, "lineStyle": {"color": ["#E0E6F1"]}}}, "visualMap": {"color": ["#bf444c", "#d88273", "#f6efa6"]}, "candlestick": {"itemStyle": {"color": "#eb5454", "color0": "#47b262", "borderColor": "#eb5454", "borderWidth": 1, "borderColor0": "#47b262"}}, "categoryAxis": {"axisLine": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisTick": {"show": true, "lineStyle": {"color": "#6E7079"}}, "axisLabel": {"show": true, "color": "#6E7079"}, "splitArea": {"show": false, "areaStyle": {"color": ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]}}, "splitLine": {"show": false, "lineStyle": {"color": ["#E0E6F1"]}}}, "backgroundColor": "rgba(0, 0, 0, 0)"}', 1);

        </sql>
    </changeSet>
</databaseChangeLog>