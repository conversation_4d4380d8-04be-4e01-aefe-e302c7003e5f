<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-116" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "camera"
            (
            "CameraId" INT AUTO_INCREMENT NOT NULL,
            "CameraName" VARCHAR(255),
            "CameraIp" VARCHAR(128),
            "CameraPort" INT,
            "ChannelNumber" VARCHAR(32),
            "CameraGroupId" INT,
            "UserName" VARCHAR(128),
            "Password" VARCHAR(32),
            "VendorId" INT,
            "VendorName" VARCHAR(128),
            "CameraIndexCode" VARCHAR(255),
            "CameraType" INT,
            "CameraTypeName" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CameraId"));

            CREATE TABLE "cameragroup"
            (
            "CameraGroupId" INT AUTO_INCREMENT NOT NULL,
            "CameraGroupName" VARCHAR(255),
            "ParentId" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CameraGroupId"));

            CREATE TABLE "camerapollgroup"
            (
            "CameraPollGroupId" INT AUTO_INCREMENT NOT NULL,
            "CameraPollGroupName" VARCHAR(255),
            "PollInterval" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CameraPollGroupId"));

            CREATE TABLE "camerapollgroupmap"
            (
            "CameraPollGroupMapId" INT AUTO_INCREMENT NOT NULL,
            "CameraPollGroupId" INT,
            "CameraId" INT,
            NOT CLUSTER PRIMARY KEY("CameraPollGroupMapId"));
        </sql>
    </changeSet>
</databaseChangeLog>