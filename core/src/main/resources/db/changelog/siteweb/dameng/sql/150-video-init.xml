<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-150" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            -- 视频IDC场景权限
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(500, 1, 0, '视频管理', '0', 0, 1, 0, 998, 1, NULL, 'Video');
            -- 视频电信场景
            INSERT INTO "MenuStructure" ("MenuStructureId", "MenuProfileId", "ParentId", "Title", "Icon", "Selected", "Expanded", "Hidden", "SortIndex", "IsSystem", "DESCRIPTION", "Alias") VALUES(501, 2, 0, '视频管理', '0', 0, 1, 0, 998, 1, NULL, 'Video');



            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias","isEmbed") VALUES(5000, 1, 'http://~{video_address}:9100/#/camerachannel', '通道管理', NULL, 7, NULL, NULL, NULL, 1, 0, 1, NULL, '1', 'Camera Channel',1);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias","isEmbed") VALUES(5001, 1, 'http://~{video_address}:9100/#/areaManagement', '区域管理', NULL, 7, NULL, NULL, NULL, 1, 0, 1, NULL, '1', 'Area Management',1);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias","isEmbed") VALUES(5002, 1, 'http://~{video_address}:9100/#/realtimerecord', '实时预览', NULL, 7, NULL, NULL, NULL, 1, 0, 1, NULL, '1', 'Realtime record',1);
            INSERT INTO "MenuItem" ("MenuItemId", "ParentId", "PATH", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "DESCRIPTION","Alias","isEmbed") VALUES(5003, 1, 'http://~{video_address}:9100/#/pollingManagement', '轮巡管理', NULL, 7, NULL, NULL, NULL, 1, 0, 1, NULL, '1', 'Polling Management',1);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description","Alias","isEmbed") VALUES(5004, 1, 'http://~{video_address}:9100/#/augmentedRManagement', 'AR管理', NULL, 7, NULL, NULL, NULL, 1, 0, 1, NULL, '1', 'AR Management', 1);
            INSERT INTO "menuitem" ("MenuItemId", "ParentId", "Path", "Title", "Icon", "FeatureId", "Selected", "Expanded", "PathMatch", "LayoutPosition", "IsSystemConfig", "IsExternalWeb", "MenuHasNavigation", "Description","Alias","isEmbed") VALUES(5005, 1, 'http://~{video_address}:9100/#/batchVideoPlayback', '批量回放', NULL, 7, NULL, NULL, NULL, 1, 0, 1, NULL, '1', 'Batch Play Back', 1);




            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1500, 1, 500, 5000, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1501, 1, 500, 5001, 2);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1502, 1, 500, 5002, 3);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1503, 1, 500, 5003, 4);
            INSERT INTO "menuitemstructuremap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1504, 1, 500, 5004, 5);
            INSERT INTO "menuitemstructuremap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (1505, 1, 500, 5005, 6);

            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1550, 2, 501, 5000, 1);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1551, 2, 501, 5001, 2);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1552, 2, 501, 5002, 3);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1553, 2, 501, 5003, 4);
            INSERT INTO "MenuItemStructureMap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES(1554, 2, 501, 5004, 5);
            INSERT INTO "menuitemstructuremap" ("MenuItemStructureMapId", "MenuProfileId", "MenuStructureId", "MenuItemId", "SortIndex") VALUES (1555, 2, 501, 5005, 6);



            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5000, '视频管理-通道管理', 6, 'MenuPermission', '500-5000', NULL);
            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5001, '视频管理-区域管理', 6, 'MenuPermission', '500-5001', NULL);
            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5002, '视频管理-实时预览', 6, 'MenuPermission', '500-5002', NULL);
            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5003, '视频管理-轮巡管理', 6, 'MenuPermission', '500-5003', NULL);
            INSERT INTO "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5004, '视频管理-AR管理', 6, 'MenuPermission', '500-5004', NULL);
            INSERT INTO "permission"("PermissionId", "Name", "Category", "Caption", "Description", "UpdateTime") VALUES (5005, '视频管理-批量回放', 6, 'MenuPermission', '500-5005', NULL);

            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5050, '视频管理-通道管理', 6, 'MenuPermission', '501-5000', NULL);
            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5051, '视频管理-区域管理', 6, 'MenuPermission', '501-5001', NULL);
            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5052, '视频管理-实时预览', 6, 'MenuPermission', '501-5002', NULL);
            insert into "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5053, '视频管理-轮巡管理', 6, 'MenuPermission', '501-5003', NULL);
            INSERT INTO "permission"("PermissionId", "Name", "Category", "Caption", "Description","UpdateTime") VALUES(5054, '视频管理-AR管理', 6, 'MenuPermission', '501-5004', NULL);
            INSERT INTO "permission"("PermissionId", "Name", "Category", "Caption", "Description", "UpdateTime") VALUES (5055, '视频管理-批量回放', 6, 'MenuPermission', '501-5005', NULL);



            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5000, -1, 6, 5000);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5001, -1, 6, 5001);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5002, -1, 6, 5002);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5003, -1, 6, 5003);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5004, -1, 6, 5004);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES (5005, -1, 6, 5005);

            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5050, -1, 6, 5050);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5051, -1, 6, 5051);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5052, -1, 6, 5052);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5053, -1, 6, 5053);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES(5054, -1, 6, 5054);
            INSERT INTO "rolepermissionmap"("RolePermissionMapId", "RoleId", "PermissionCategoryId", "PermissionId") VALUES (5055, -1, 6, 5055);



            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5000,5000,1);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5001,5001,1);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5002,5002,1);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5003,5003,1);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5004,5004,1);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES (5005, 5005, 1);


            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5050,5050,2);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5051,5051,2);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5052,5052,2);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5053,5053,2);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES(5054,5054,1);
            INSERT INTO "scenepermissionmap" ("ScenePermissionMapId", "PermissionId", "SceneId") VALUES (5055, 5055, 2);

        </sql>
    </changeSet>
</databaseChangeLog>