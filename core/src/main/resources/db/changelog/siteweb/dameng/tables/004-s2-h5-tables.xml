<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-004" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_h5camera"
            (
            "CameraId" INT AUTO_INCREMENT NOT NULL,
            "CameraName" VARCHAR(255) NOT NULL,
            "CameraIp" VARCHAR(128) NOT NULL,
            "CameraPort" INT NOT NULL,
            "ChannelNumber" VARCHAR(32),
            "CameraGroupId" INT,
            "UserName" VARCHAR(128),
            "Password" VARCHAR(32),
            "VendorId" INT,
            "VendorName" VARCHAR(128),
            "CameraIndexCode" VARCHAR(255),
            "CameraType" INT,
            "CameraTypeName" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CameraId"));

            CREATE TABLE "tbl_h5cameragroup"
            (
            "CameraGroupId" INT AUTO_INCREMENT NOT NULL,
            "CameraGroupName" VARCHAR(255),
            "ParentId" INT,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CameraGroupId"));

            CREATE TABLE "tbl_h5reportcentertypemap"
            (
            "H5ReportCenterTypeMapId" int PRIMARY KEY AUTO_INCREMENT,
            "ReportId" INT NOT NULL,
            "CenterType" INT DEFAULT 0 NOT NULL);

            CREATE TABLE "tbl_h5reportcronexpression"
            (
            "CronId" INT AUTO_INCREMENT NOT NULL,
            "CronExpression" VARCHAR(128) DEFAULT '' NOT NULL,
            "Meaning" VARCHAR(128) DEFAULT '' NOT NULL,
            NOT CLUSTER PRIMARY KEY("CronId"));

            CREATE TABLE "tbl_h5reportprocedureparameters"
            (
            "H5ReportProcedureParameters" int PRIMARY KEY AUTO_INCREMENT,
            "PName" VARCHAR(255) NOT NULL,
            "Parameters" VARCHAR(4000));

            CREATE TABLE "tbl_h5reporttask"
            (
            "TaskId" INT AUTO_INCREMENT NOT NULL,
            "TaskName" VARCHAR(128) NOT NULL,
            "CronId" INT NOT NULL,
            "IsEnable" INT NOT NULL,
            "Description" VARCHAR(255),
            "ReportId" INT NOT NULL,
            "QueryParameters" CLOB NOT NULL,
            "Creator" INT,
            "CreateTime" TIMESTAMP(0),
            "LastUpdateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("TaskId"));
        </sql>
    </changeSet>
</databaseChangeLog>