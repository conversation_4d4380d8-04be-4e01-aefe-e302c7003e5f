<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-002" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_equipmentbasetype"
            (
            "BaseEquipmentId" INT NOT NULL,
            "BaseEquipmentName" VARCHAR(128),
            "EquipmentTypeId" INT NOT NULL,
            "EquipmentSubTypeId" INT NOT NULL,
            "Description" TEXT,
            "ExtField" VARCHAR(8188),
            NOT CLUSTER PRIMARY KEY("BaseEquipmentId"),
            CHECK("ExtField" IS JSON ));

            CREATE TABLE "baseunit"
            (
            "BaseUnitId" INT AUTO_INCREMENT NOT NULL,
            "BaseUnitName" VARCHAR(128),
            "BaseUnitNameEn" VARCHAR(128),
            "BaseUnitSymbol" VARCHAR(128) NOT NULL,
            "BaseUnitNameCode" VARCHAR(128),
            "BaseUnitSymbolName" VARCHAR(128),
            "BaseUnitNameDescription" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("BaseUnitId"));

            CREATE TABLE "tbl_baseclassdic"
            (
            "BaseClassId" INT NOT NULL,
            "BaseClassName" VARCHAR(255) NOT NULL,
            "BaseClassIcon" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("BaseClassId"));

            CREATE TABLE "tbl_basecommandcode"
            (
            "CodeId" INT NOT NULL,
            "Command" VARCHAR(255) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CodeId"));

            CREATE TABLE "tbl_basedicver"
            (
            "HiVer" INT NOT NULL,
            "LoVer" INT NOT NULL,
            "Remark" TEXT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            NOT CLUSTER PRIMARY KEY("HiVer", "LoVer"));

            CREATE TABLE "tbl_basedicverhistory"
            (
            "Version" VARCHAR(255) NOT NULL,
            "UpdateDesciption" TEXT NOT NULL,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "Editor" INT NOT NULL,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            NOT CLUSTER PRIMARY KEY("UpdateDate", "Version"));

            CREATE TABLE "tbl_baseequipmentcategorymap"
            (
            "BaseEquipmentID" INT NOT NULL,
            "EquipmentCategory" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("BaseEquipmentID", "EquipmentCategory"));

            CREATE TABLE "tbl_baseequipmentmap"
            (
            "StandardType" INT NOT NULL,
            "StandardDicId" INT NOT NULL,
            "StationBaseType" INT NOT NULL,
            "EquipmentBaseType" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("EquipmentBaseType", "StandardDicId", "StandardType", "StationBaseType"));

            CREATE TABLE "tbl_basesignaleventcode"
            (
            "CodeId" INT NOT NULL,
            "Category" VARCHAR(255) NOT NULL,
            "Signal" VARCHAR(255),
            "EVENT" VARCHAR(255),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CodeId"));

            CREATE TABLE "tbl_baseunitdic"
            (
            "BaseUnitID" INT NOT NULL,
            "BaseUnitName" VARCHAR(255) NOT NULL,
            "BaseUnitSymbol" VARCHAR(255) NOT NULL,
            "BaseUnitDescription" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("BaseUnitID"));

            CREATE TABLE "tbl_signalbasedic"
            (
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "BaseEquipmentId" INT NOT NULL,
            "EnglishName" TEXT,
            "BaseLogicCategoryId" INT,
            "StoreInterval" INT,
            "AbsValueThreshold" DOUBLE,
            "PercentThreshold" DOUBLE,
            "StoreInterval2" INT,
            "AbsValueThreshold2" DOUBLE,
            "PercentThreshold2" DOUBLE,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "ExtendField3" TEXT,
            "UnitId" INT,
            "BaseStatusId" INT,
            "BaseHysteresis" DOUBLE,
            "BaseFreqPeriod" INT,
            "BaseFreqCount" INT,
            "BaseShowPrecision" VARCHAR(30),
            "BaseStatPeriod" INT,
            "CGElement" VARCHAR(128),
            "Description" TEXT,
            "BaseNameExt" VARCHAR(128),
            "IsSystem" TINYINT DEFAULT 1 NOT NULL,
            NOT CLUSTER PRIMARY KEY("BaseTypeId"));

            CREATE TABLE "tbl_signalbasemap"
            (
            "StandardDicId" INT NOT NULL,
            "StandardType" INT NOT NULL,
            "StationBaseType" INT NOT NULL,
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseCondId" INT,
            NOT CLUSTER PRIMARY KEY("BaseTypeId", "StandardDicId", "StandardType", "StationBaseType"));

            CREATE TABLE "tbl_signalbaseconfirm"
            (
            "SignalBaseConfirmId" int PRIMARY KEY AUTO_INCREMENT,
            "EquipmentTemplateId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "StateValue" INT,
            "SubState" VARCHAR(16));

            CREATE OR REPLACE  INDEX "idxsignalbaseconfirmID" ON "tbl_signalbaseconfirm"("EquipmentTemplateId" ASC,"SignalId" ASC);

            CREATE TABLE "tbl_statusbasedic"
            (
            "BaseStatusId" INT NOT NULL,
            "BaseStatusName" VARCHAR(128) NOT NULL,
            "BaseCondId" INT NOT NULL,
            "Operator" VARCHAR(30) NOT NULL,
            "Value" INT,
            "Meaning" VARCHAR(128),
            "Description" TEXT,
            NOT CLUSTER PRIMARY KEY("BaseCondId", "BaseStatusId"));

            CREATE TABLE "tbl_commandbasedic"
            (
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "BaseEquipmentId" INT NOT NULL,
            "EnglishName" TEXT,
            "BaseLogicCategoryId" INT,
            "CommandType" INT NOT NULL,
            "BaseStatusId" INT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "ExtendField3" TEXT,
            "Description" TEXT,
            "BaseNameExt" VARCHAR(128),
            "IsSystem" TINYINT DEFAULT 1 NOT NULL,
            NOT CLUSTER PRIMARY KEY("BaseTypeId"));

            CREATE TABLE "tbl_commandbasemap"
            (
            "StandardDicId" INT NOT NULL,
            "StandardType" INT NOT NULL,
            "StationBaseType" INT NOT NULL,
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseCondId" INT,
            NOT CLUSTER PRIMARY KEY("BaseTypeId", "StandardDicId", "StandardType", "StationBaseType"));

            CREATE TABLE "tbl_controlbaseconfirm"
            (
            "ControlBaseConfirmId" int PRIMARY KEY AUTO_INCREMENT,
            "EquipmentTemplateId" INT NOT NULL,
            "ControlId" INT NOT NULL,
            "ParameterValue" INT,
            "SubState" VARCHAR(16));

            CREATE TABLE "tbl_eventbasedic"
            (
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "BaseTypeName" VARCHAR(128) NOT NULL,
            "BaseEquipmentId" INT NOT NULL,
            "EnglishName" TEXT,
            "EventSeverityId" INT NOT NULL,
            "ComparedValue" DOUBLE,
            "BaseLogicCategoryId" INT,
            "StartDelay" INT,
            "EndDelay" INT,
            "ExtendField1" TEXT,
            "ExtendField2" TEXT,
            "ExtendField3" TEXT,
            "ExtendField4" TEXT,
            "ExtendField5" TEXT,
            "Description" TEXT,
            "BaseNameExt" VARCHAR(128),
            "IsSystem" TINYINT DEFAULT 1 NOT NULL,
            NOT CLUSTER PRIMARY KEY("BaseTypeId"));

            CREATE TABLE "tbl_eventbaseconfirm"
            (
            "EquipmentTemplateId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "SubState" VARCHAR(16),
            NOT CLUSTER PRIMARY KEY("EquipmentTemplateId", "EventConditionId", "EventId"));

            CREATE TABLE "tbl_eventbasemap"
            (
            "StandardDicId" INT NOT NULL,
            "StandardType" INT NOT NULL,
            "StationBaseType" INT NOT NULL,
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("BaseTypeId", "StandardDicId", "StandardType", "StationBaseType"));

            CREATE TABLE "tbl_logiccategorybasedic"
            (
            "BaseEquipmentId" INT NOT NULL,
            "BaseLogicCategoryType" INT NOT NULL,
            "BaseLogicCategoryId" INT NOT NULL,
            "BaseLogicCategoryName" VARCHAR(128),
            "Description" TEXT,
            NOT CLUSTER PRIMARY KEY("BaseLogicCategoryId"));
        </sql>
    </changeSet>
</databaseChangeLog>