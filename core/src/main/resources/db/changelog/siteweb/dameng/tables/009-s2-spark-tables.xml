<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-009" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "tbl_sparkindexbaseequipment"
            (
            "GroupId" INT NOT NULL,
            "BaseEquipmentId" INT NOT NULL,
            "GrapicPageId" INT,
            "DisplayIndex" INT,
            "DisplayFormat" TEXT,
            "ExtendFiled1" TEXT,
            "ExtendFiled2" TEXT,
            "DisplayType" INT,
            NOT CLUSTER PRIMARY KEY("GroupId","BaseEquipmentId"));

            CREATE TABLE "tbl_sparkindexbasesignal"
            (
            "GroupId" INT NOT NULL,
            "BaseEquipmentId" INT NOT NULL,
            "BaseTypeId" DECIMAL(12,0) NOT NULL,
            "DisplayIndex" INT,
            "ExtendFiled1" TEXT,
            "ExtendFiled2" TEXT,
            NOT CLUSTER PRIMARY KEY("GroupId","BaseEquipmentId","BaseTypeId"));

            CREATE TABLE "tbl_sparkindexgroup"
            (
            "GroupId" INT NOT NULL primary key,
            "GroupTitle" VARCHAR(255) NOT NULL,
            "GrapicPageId" INT,
            "ExtendFiled1" TEXT,
            "ExtendFiled2" TEXT,
            "ColumnCount" INT);
        </sql>
    </changeSet>
</databaseChangeLog>