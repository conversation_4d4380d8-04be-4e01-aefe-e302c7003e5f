<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-114" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('1',1, 'ECC', '监控中心');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('2', 1,'园区', '园区');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('3',1,'大楼','大楼');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('4',1,'楼层','楼层');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('5',1,'房间','房间');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('6',1,'MDC','MDC');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('7',1,'设备','设备');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('8',1, '通用对象', '通用对象');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('9', 1,'机架', '机架');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('10',1,'IT设备','IT设备');
            INSERT INTO"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('101',2, '省中心', '');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('102',2, '地市中心', '');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('103', 2,'片区', '');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('104',2, '基站', '');
            insert into"resourcestructuretype"("ResourceStructureTypeId", "SceneId", "ResourceStructureTypeName", "Description") values('105',2, '局房', '');
        </sql>
    </changeSet>
</databaseChangeLog>