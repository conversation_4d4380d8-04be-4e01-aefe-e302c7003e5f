<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-013" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "activeeventfiltertemplate"
            (
            "ActiveEventFilterTemplateId" INT AUTO_INCREMENT NOT NULL,
            "UserId" INT,
            "FilterType" VARCHAR(128) NOT NULL,
            "TemplateName" VARCHAR(255) NOT NULL,
            "Content" VARCHAR(8000) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ActiveEventFilterTemplateId"));

            CREATE TABLE "activeeventoperationlog"
            (
            "ActiveEventOperationLogId" BIGINT AUTO_INCREMENT NOT NULL,
            "SequenceId" VARCHAR(128),
            "StationId" INT,
            "EquipmentId" INT,
            "EventId" INT,
            "EventConditionId" INT,
            "StartTime" TIMESTAMP(0),
            "OperatorId" INT,
            "Operation" VARCHAR(128),
            "OperationTime" TIMESTAMP(0),
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ActiveEventOperationLogId"));

            CREATE OR REPLACE  INDEX "IDX_ActiveEventOperationLog_SequenceId" ON "activeeventoperationlog"("SequenceId" ASC);
            CREATE OR REPLACE  INDEX "ActiveEventOperationLog_IDX1" ON "activeeventoperationlog"("StartTime" ASC,"EquipmentId" ASC,"EventId" ASC,"StationId" ASC);

            CREATE TABLE "activenotification"
            (
            "AlarmSequenceId" INT NOT NULL,
            "BirthTime" TIMESTAMP(0),
            "StationId" INT,
            "StationName" VARCHAR(128),
            "EquipmentId" INT,
            "EquipmentName" VARCHAR(128),
            "EventId" INT,
            "EventConditionId" INT,
            "EventUniqueId" VARCHAR(255),
            "EventName" VARCHAR(128),
            "EventSeverity" INT,
            "SeverityName" VARCHAR(50),
            "EventStatus" INT NOT NULL,
            "Overturn" INT,
            "Meaning" VARCHAR(128),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "UpdateTimeFormat" VARCHAR(255),
            "ConfirmUserId" INT,
            "ConfirmUserName" VARCHAR(128),
            "CenterName" VARCHAR(128),
            "StationState" VARCHAR(128),
            "EquipmentCategoryName" VARCHAR(128),
            "EquipmentVendorName" VARCHAR(128),
            "EquipmentLogicCategory" VARCHAR(128),
            "LogicCategory" VARCHAR(128),
            "SubLogicCategory" VARCHAR(128),
            "InfectionToEquipment" VARCHAR(128),
            "InfectionToBusiness" VARCHAR(128),
            "StandardAlarmName" VARCHAR(128),
            "StandardNameId" VARCHAR(128),
            "AlarmComment" VARCHAR(128),
            "NetAlarmId" VARCHAR(128),
            "NotifyServerId" INT,
            "NotificationType" INT,
            "Setting" VARCHAR(255),
            "NotificationRecieverId" INT,
            "RetryTimes" INT,
            "EventFilterDelay" INT,
            "NotifyResult" INT,
            "InstructionId" VARCHAR(128),
            CONSTRAINT "PK_ActiveNotification_ID" PRIMARY KEY("AlarmSequenceId", "EventStatus"));

            CREATE TABLE "alarmmasklog"
            (
            "Id" BIGINT AUTO_INCREMENT NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT,
            "ResourceStructureId" INT,
            "UserId" INT NOT NULL,
            "OperationType" INT NOT NULL,
            "OperationTime" TIMESTAMP(0),
            "TimeGroupCategory" INT,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "TimeGroupChars" VARCHAR(255),
            "Comment" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE OR REPLACE  INDEX "AlarmMaskLog_IDX1" ON "alarmmasklog"("OperationTime" ASC,"EquipmentId" ASC,"EventId" ASC);

            CREATE TABLE "allnotification"
            (
            "AlarmSequenceId" INT NOT NULL,
            "BirthTime" TIMESTAMP(0),
            "StationId" INT,
            "StationName" VARCHAR(128),
            "EquipmentId" INT,
            "EquipmentName" VARCHAR(128),
            "EventId" INT,
            "EventConditionId" INT,
            "EventUniqueId" VARCHAR(255) NOT NULL,
            "EventName" VARCHAR(128),
            "EventSeverity" INT,
            "SeverityName" VARCHAR(50),
            "EventStatus" INT NOT NULL,
            "Overturn" INT,
            "Meaning" VARCHAR(128),
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "ConfirmTime" TIMESTAMP(0),
            "UpdateTimeFormat" VARCHAR(255),
            "ConfirmUserId" INT,
            "ConfirmUserName" VARCHAR(128),
            "CenterName" VARCHAR(128),
            "StationState" VARCHAR(128),
            "EquipmentCategoryName" VARCHAR(128),
            "EquipmentVendorName" VARCHAR(128),
            "EquipmentLogicCategory" VARCHAR(128),
            "LogicCategory" VARCHAR(128),
            "SubLogicCategory" VARCHAR(128),
            "InfectionToEquipment" VARCHAR(128),
            "InfectionToBusiness" VARCHAR(128),
            "StandardAlarmName" VARCHAR(128),
            "StandardNameId" VARCHAR(128),
            "AlarmComment" VARCHAR(128),
            "NetAlarmId" VARCHAR(128),
            "DefaultStationGroupName" VARCHAR(128),
            "NotificationType" INT NOT NULL,
            "StationCategoryId" INT,
            "StationCategoryName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("AlarmSequenceId", "EventUniqueId", "EventStatus", "NotificationType"));

            CREATE OR REPLACE  INDEX "AllNotification_IDX2" ON "allnotification"("EventUniqueId" ASC,"AlarmSequenceId" ASC,"EventStatus" ASC,"StartTime" ASC,"NotificationType" ASC);
            CREATE OR REPLACE  INDEX "AllNotification_IDX1" ON "allnotification"("StationId" ASC,"EquipmentId" ASC,"EventId" ASC,"EventConditionId" ASC,"EventStatus" ASC,"StartTime" ASC,"NotificationType" ASC);

            CREATE TABLE "eventfilter"
            (
            "EventFilterId" INT NOT NULL,
            "EventFilterName" VARCHAR(128),
            "Description" VARCHAR(255),
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("EventFilterId"));

            CREATE TABLE "eventfiltercondition"
            (
            "EventFilterId" INT NOT NULL,
            "EventFilterConditionId" INT NOT NULL,
            "EventFilterCombination" TEXT NOT NULL,
            "EventFilterSegment1" INT,
            "EventFilterSegment2" INT,
            "EventFilterSegment3" INT,
            "EventFilterSegment4" INT,
            "EventFilterSegment5" INT,
            "EventFilterSegment6" INT,
            "EventFilterSegment7" INT,
            "EventFilterSegment8" INT,
            "EventFilterSegment9" INT,
            "EventFilterSegment10" INT,
            "EventFilterSegment11" INT,
            "EventFilterDelay" INT DEFAULT 0 NOT NULL,
            "EventFilterCount" INT DEFAULT 0 NOT NULL,
            "Description" TEXT,
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("EventFilterId", "EventFilterConditionId"));

            CREATE TABLE "eventfiltermap"
            (
            "EventFilterMemberId" INT NOT NULL,
            "EventFilterId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("EventFilterMemberId", "EventFilterId"));

            CREATE OR REPLACE  INDEX "EventFilterId" ON "eventfiltermap"("EventFilterId" ASC);

            CREATE TABLE "eventfiltermember"
            (
            "EventFilterMemberId" INT NOT NULL,
            "EventFilterMemberName" VARCHAR(128) NOT NULL,
            "Description" VARCHAR(255),
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            NOT CLUSTER PRIMARY KEY("EventFilterMemberId"));

            CREATE TABLE "eventnotifyreciever"
            (
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventSeverity" INT NOT NULL,
            "EventState" INT NOT NULL,
            "NotifyReceiverId" INT NOT NULL,
            "NotifyReceiverCategory" INT NOT NULL,
            "NotifyReceiverName" VARCHAR(128),
            "NotifyAddress" VARCHAR(255),
            "NotifyContent" VARCHAR(255),
            "NotifyServerId" INT,
            "EventFilterDelay" INT,
            "EventFilterCount" INT,
            "EventFilterId" INT,
            "EventFilterConditionId" INT,
            "NotifyServerCategory" INT,
            NOT CLUSTER PRIMARY KEY("StationId", "EquipmentId", "EventId", "EventSeverity", "EventState", "NotifyReceiverId", "NotifyReceiverCategory"));

            CREATE TABLE "tbl_bizbasetypeid"
            (
            "BusinessTypeId" INT NOT NULL,
            "BaseTypeId" INT NOT NULL,
            "StoreInterval" DOUBLE,
            "AbsValueThreshold" DOUBLE,
            NOT CLUSTER PRIMARY KEY("BaseTypeId", "BusinessTypeId"));

            CREATE TABLE "tbl_bizexpequsignalsmap"
            (
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "AssociationId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "SerialId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "StoreInterval" DOUBLE,
            "AbsValueThreshold" DOUBLE,
            NOT CLUSTER PRIMARY KEY("BusinessTypeId", "EquipmentId", "ExpressionId", "MonitorUnitId", "SerialId", "SignalId", "StationId"));

            CREATE TABLE "tbl_bizexpsignalscfg"
            (
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "AssociationId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "SignalId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "StoreInterval" DOUBLE,
            "AbsValueThreshold" DOUBLE,
            NOT CLUSTER PRIMARY KEY("BusinessTypeId", "EquipmentId", "ExpressionId", "SignalId", "StationId"));

            CREATE TABLE "tbl_bizexpstationsmap"
            (
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "SerialId" INT NOT NULL,
            "Expression" TEXT,
            "SuppressExpression" TEXT,
            "StateTriggerValue" INT DEFAULT 1,
            "BeforeChgStoreInterval" DOUBLE,
            "AfterChgStoreInterval" DOUBLE,
            "ErrorFlag" INT,
            "Note" TEXT,
            NOT CLUSTER PRIMARY KEY("BusinessTypeId", "ExpressionId", "MonitorUnitId", "SerialId", "StationId"));

            CREATE TABLE "tbl_businessdataitem" (
            "BusinessId" INT DEFAULT 0 NOT NULL,
            "ParentEntryId" INT DEFAULT 0 NOT NULL,
            "ParentItemId" INT DEFAULT 0 NOT NULL,
            "EntryId" INT NOT NULL,
            "ItemId" INT NOT NULL,
            "ItemValue" VARCHAR(128) NOT NULL,
            "ItemAlias" VARCHAR(255),
            "IsSystem" BIT DEFAULT 1 NOT NULL,
            "IsDefault" BIT DEFAULT 0 NOT NULL,
            "Enable" BIT DEFAULT 1 NOT NULL,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            "ExtendField5" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("BusinessId", "EntryId", "ItemId"));

            CREATE TABLE "tbl_businessexpressioncfg"
            (
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "MonitorUnitId" INT NOT NULL,
            "ExpressionName" VARCHAR(255),
            "Expression" TEXT,
            "SuppressExpression" TEXT,
            "StateTriggerValue" INT DEFAULT 1,
            "BeforeChgStoreInterval" DOUBLE,
            "AfterChgStoreInterval" DOUBLE,
            "Note" TEXT,
            NOT CLUSTER PRIMARY KEY("BusinessTypeId", "ExpressionId"));

            CREATE TABLE "tbl_businesstype"
            (
            "BusinessTypeId" INT NOT NULL,
            "BusinessTypeName" VARCHAR(255) NOT NULL,
            "MiddleTableName" VARCHAR(255),
            "Note" TEXT,
            NOT CLUSTER PRIMARY KEY("BusinessTypeId"));

            CREATE TABLE "tbl_businesstypestatus"
            (
            "StationId" INT NOT NULL,
            "BusinessTypeId" INT NOT NULL,
            "ExpressionId" INT NOT NULL,
            "SerialId" INT NOT NULL,
            "BusinessState" INT NOT NULL,
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "GroupId" INT,
            NOT CLUSTER PRIMARY KEY("BusinessState", "BusinessTypeId", "ExpressionId", "SerialId", "StartTime", "StationId"));

            CREATE TABLE "tbl_categoryidmap"
            (
            "BusinessId" INT NOT NULL,
            "CategoryTypeId" INT NOT NULL,
            "OriginalCategoryId" INT NOT NULL,
            "BusinessCategoryId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("BusinessCategoryId", "BusinessId", "CategoryTypeId", "OriginalCategoryId"));

            CREATE TABLE "tbl_custominfo"
            (
            "CustomInfoId" INT NOT NULL,
            "UserId" INT NOT NULL,
            "CustomType" VARCHAR(128) NOT NULL,
            "CustomContent" CLOB NOT NULL,
            "CreateTime" TIMESTAMP(0) NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("CustomInfoId"));

            CREATE TABLE "tbl_experience"
            (
            "ExperienceId" INT AUTO_INCREMENT NOT NULL,
            "ExperienceCaption" TEXT,
            "Measure" TEXT,
            "Description" TEXT,
            "LastUpdateDate" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
            "Condition" TEXT,
            NOT CLUSTER PRIMARY KEY("ExperienceId"));

            CREATE TABLE "tbl_expert"
            (
            "ExpertId" INT NOT NULL,
            "StationCategoryId" INT,
            "StationCategoryName" VARCHAR(255),
            "BaseEquipmentTypeId" INT,
            "BaseEquipmentTypeName" VARCHAR(255),
            "BaseAlarmId" INT NOT NULL,
            "BaseAlarmName" VARCHAR(255),
            "StandardEquipmentTypeId" INT,
            "StandardEquipmentTypeName" VARCHAR(255),
            "StandardAlarmId" VARCHAR(255),
            "StandardAlarmName" VARCHAR(255),
            "Reason" TEXT,
            "Solution" TEXT,
            NOT CLUSTER PRIMARY KEY("ExpertId"));

            CREATE TABLE "tbl_fault"
            (
            "FaultId" INT NOT NULL,
            "SequenceId" VARCHAR(255) NOT NULL,
            "UUID" VARCHAR(128) NOT NULL,
            "StationGroupName" VARCHAR(255),
            "StationName" VARCHAR(255),
            "HouseName" VARCHAR(255),
            "EquipmentName" VARCHAR(255),
            "EventName" VARCHAR(255),
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0),
            "CreateUserId" INT NOT NULL,
            "CreateTime" TIMESTAMP(0) NOT NULL,
            "CaseReason" TEXT,
            "CaseSolution" TEXT,
            NOT CLUSTER PRIMARY KEY("FaultId"));

            CREATE TABLE "tbl_faultexpertmap"
            (
            "FaultExpertMapId" INT AUTO_INCREMENT NOT NULL,
            "FaultId" INT NOT NULL,
            "ExpertId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("FaultExpertMapId"));

            CREATE TABLE "tbl_graphicpage"
            (
            "Id" INT NOT NULL,
            "Type" VARCHAR(255),
            "GroupId" INT,
            "PageCategory" INT,
            "BaseEquipmentId" INT,
            "ObjectId" BIGINT,
            "GlobalResourceId" BIGINT,
            "Name" VARCHAR(255) NOT NULL,
            "AppendName" VARCHAR(255),
            "TemplateId" INT,
            "Data" TEXT,
            "Style" TEXT,
            "Children" CLOB,
            "Description" VARCHAR(255),
            "RouterType" VARCHAR(255),
            "CrumbsRouterType" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_graphicpagetemplate"
            (
            "Id" INT NOT NULL,
            "Type" VARCHAR(255),
            "GroupId" INT,
            "PageCategory" INT,
            "BaseEquipmentId" INT,
            "Internal" SMALLINT,
            "Name" VARCHAR(255) NOT NULL,
            "AppendName" VARCHAR(255),
            "Data" TEXT,
            "Style" TEXT,
            "Children" TEXT,
            "Description" VARCHAR(255),
            "TemplateCategory" INT,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "tbl_homepage"
            (
            "PageId" INT NOT NULL,
            "PageName" VARCHAR(255) NOT NULL,
            "FileName" VARCHAR(255) NOT NULL,
            "PageType" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("PageId"));

            CREATE TABLE "tbl_userhomepagemap"
            (
            "PageId" INT NOT NULL,
            "UserId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("PageId", "UserId"));

            CREATE TABLE "tbl_kpipage"
            (
            "KPIPageId" INT NOT NULL,
            "UserId" INT NOT NULL,
            "Name" VARCHAR(128) NOT NULL,
            "Type" INT NOT NULL,
            "FileName" VARCHAR(128) NOT NULL,
            "FileDir" VARCHAR(150),
            "CreateTime" TIMESTAMP(0) NOT NULL,
            "ModifyTime" TIMESTAMP(0),
            "Description" VARCHAR(200),
            "ThumbImage" VARCHAR(200),
            NOT CLUSTER PRIMARY KEY("KPIPageId"));

            CREATE TABLE "tbl_kpipageuserrelate"
            (
            "UserId" INT NOT NULL,
            "KPIPageId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("UserId"));

            CREATE TABLE "tbl_equipmentbranch"
            (
            "id" INT AUTO_INCREMENT NOT NULL,
            "BranchId" INT NOT NULL,
            "BranchName" VARCHAR(128),
            "EquipmentId" INT NOT NULL,
            "EquipmentName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("id"));

            CREATE OR REPLACE  INDEX "IDX_EquipmentBranch_EquipmentId_BranchId" ON "tbl_equipmentbranch"("EquipmentId" ASC,"BranchId" ASC);

            CREATE TABLE "tbl_phoenixdiskfile"
            (
            "FileId" BIGINT AUTO_INCREMENT NOT NULL,
            "FilePath" VARCHAR(1024) NOT NULL,
            "FileName" VARCHAR(256) NOT NULL,
            "Status" INT,
            "CreateTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("FileId"));
        </sql>
    </changeSet>
</databaseChangeLog>