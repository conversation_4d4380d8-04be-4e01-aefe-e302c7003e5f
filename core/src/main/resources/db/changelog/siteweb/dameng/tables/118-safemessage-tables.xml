<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-118" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "safemessage"
            (
            "SafeMessageId" INT AUTO_INCREMENT NOT NULL,
            "ConfigName" VARCHAR(128),
            "Receiver" VARCHAR(255),
            "ReceiveMode" VARCHAR(128),
            "SendTime" TIME(0),
            "SendType" INT,
            "SendTypeDescription" VARCHAR(100),
            "Cron" VARCHAR(100),
            "ContentTemplate" VARCHAR(255),
            "Description" VARCHAR(255),
            "UsedStatus" TINYINT,
            NOT CLUSTER PRIMARY KEY("SafeMessageId"));

            CREATE TABLE "safemessageelementconfig"
            (
            "SafeMessageElementConfigId" INT AUTO_INCREMENT NOT NULL,
            "SafeMessageId" INT,
            "ElementType" INT,
            "ElementSetting" TEXT,
            NOT CLUSTER PRIMARY KEY("SafeMessageElementConfigId"));

            CREATE TABLE "safemessagerecord"
            (
            "SafeMessageRecordId" INT AUTO_INCREMENT NOT NULL,
            "SafeMessageId" INT,
            "SendContent" VARCHAR(255),
            "Receiver" VARCHAR(255),
            "SendTime" TIMESTAMP(0),
            "Remark" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("SafeMessageRecordId"));
        </sql>
    </changeSet>
</databaseChangeLog>