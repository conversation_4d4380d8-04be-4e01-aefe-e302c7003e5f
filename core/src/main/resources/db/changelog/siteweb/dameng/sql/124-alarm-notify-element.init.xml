<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-124" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (1,'短信发送','发送方式',1,1,null,null,false,1,null);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (2,'邮件发送','发送方式',1,1,null,null,true,2,null);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (3,'告警箱','发送方式',1,1,null,null,true,3,null);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (4,'电话语音(短信)','发送方式',1,1,null,null,true,4,null);
            -- INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            -- VALUES (8,'短信发送(东北大学)','发送方式',1,1,null,null,false,8,null);
            -- INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            -- VALUES (9,'邮件发送(东北大学)','发送方式',1,1,null,null,false,9,null);
            -- INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            -- VALUES (10,'微信发送(东北大学)','发送方式',1,1,null,null,false,10,null);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (11,'失败重发','告警升级',1,1,null,null,true,100,null);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (12,'超时未确认','告警升级',1,1,null,null,true,101,null);
            INSERT INTO alarmnotifyelement (ElementId, ElementName, ElementType, InputNodesCount, OutputNodesCount, Icon, Expression, Visible, SortIndex, Description)
            VALUES(13, '企业微信应用通知', '发送方式', 1, 1, NULL, NULL, 1, 5, NULL);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (14,'告警灯','发送方式',1,1,null,null,true,6,null);
            INSERT INTO "alarmnotifyelement" ("ElementId","ElementName","ElementType","InputNodesCount","OutputNodesCount","Icon","Expression","Visible","SortIndex","Description")
            VALUES (15,'APP推送','发送方式',1,1,null,null,true,7,null);
            INSERT INTO "alarmnotifyelement" ("ElementId", "ElementName", "ElementType", "InputNodesCount", "OutputNodesCount", "Icon", "Expression", "Visible", "SortIndex", "Description")
            VALUES (17, '飞书通知', '发送方式', 1, 1, null, null, true, 8, null);


            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(1, '园区');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(2, '大楼');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(3, '楼层');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(4, '房间');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(5, 'MDC');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(6, '设备');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(7, '告警');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(8, '设备基类');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(9, '告警基类');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(10, '告警状态');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(11, '告警等级');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(12, '位置');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(13, '工程状态');
            INSERT INTO "alarmnotifyfiltercondition" ("FilterConditionId","FilterConditionName") VALUES(14, '关键字');
        </sql>
    </changeSet>
</databaseChangeLog>