<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-118" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            /*
            场景权限类别。
            IDC：用户操作权限、报表权限、菜单显示权限、区域权限。
            网点：用户操作权限、菜单显示权限、专业权限、片区权限
            视频：用户操作权限、报表权限、菜单显示权限、专业权限、片区权限、区域权限
            */
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (1, 2, 1);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (2, 5, 1);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (3, 6, 1);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (4, 10, 1);

            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (5, 2, 2);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (7, 6, 2);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (8, 8, 2);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (9, 9, 2);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (10, 11, 1);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (11, 11, 2);

            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (12, 2, 3);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (13, 5, 3);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (14, 6, 3);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (15, 8, 3);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (16, 9, 3);
            insert into "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") values (17, 10, 3);
            INSERT INTO "ScenePermissionCategoryMap" ("ScenePermissionCategoryMapId", "PermissionCategoryId", "SceneId") VALUES(18, 10, 2);
        </sql>
    </changeSet>
</databaseChangeLog>