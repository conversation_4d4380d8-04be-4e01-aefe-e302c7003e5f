<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-113" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">

            INSERT INTO "menupermissiongroup" ("MenuPermissionGroupId", "MenuPermissionGroupName", "Description") VALUES(-1, '所有菜单权限组', '拥有所有的菜单权限 ');


            -- 系统管理员默认拥有所有菜单权限组权限
            insert into "rolepermissionmap" ("RoleId", "PermissionCategoryId", "PermissionId") values(-1,6,-1);
            -- 系统管理员默认拥有所有操作权限组权限
            insert into "rolepermissionmap" ("RoleId", "PermissionCategoryId", "PermissionId") values(-1,2,-1);
        </sql>
    </changeSet>
</databaseChangeLog>