<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-109" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "pd_prealarmreportrecord"
            (
            "RecordId" INT AUTO_INCREMENT NOT NULL,
            "PreAlarmId" INT NOT NULL,
            "PreAlarmPointId" INT,
            "PreAlarmSeverity" INT,
            "PreAlarmCategory" INT,
            "ObjectId" INT,
            "ObjectTypeId" INT,
            "ResourceStructureId" INT,
            "TriggerValue" VARCHAR(128),
            "ReportPath" VARCHAR(512),
            "ReportFileName" VARCHAR(255),
            "HistoryDataPath" VARCHAR(512),
            "HistoryDataFileName" VARCHAR(255),
            "InsertTime" TIMESTAMP(0),
            "GenerateTime" TIMESTAMP(0),
            "GenerateResult" INT,
            "DeleteTime" TIMESTAMP(0),
            "ExtendField" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("RecordId"),
            CONSTRAINT "IDX_PD_PrealarmReportRecord_1" UNIQUE("PreAlarmId"));

            CREATE TABLE "powersimulationrecord"
            (
            "recordId" INT AUTO_INCREMENT NOT NULL,
            "recordName" VARCHAR(225) NOT NULL,
            "diagramId" INT NOT NULL,
            "nodeId" VARCHAR(225) NOT NULL,
            "onOffState" INT NOT NULL,
            "downloadPath" VARCHAR(225) NOT NULL,
            "attributeObjects" TEXT NOT NULL,
            "reportName" VARCHAR(225) NOT NULL,
            NOT CLUSTER PRIMARY KEY("recordId"));
        </sql>
    </changeSet>
</databaseChangeLog>