<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-112" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "alarmnotifyconfig"
            (
            "AlarmNotifyConfigId" INT AUTO_INCREMENT NOT NULL,
            "ConfigName" VARCHAR(128) NOT NULL,
            "UsedStatus" TINYINT NOT NULL,
            "ContentTemplate" VARCHAR(1000) NOT NULL,
            "NotifyDelay" INT,
            "Layout" CLOB,
            "Description" VARCHAR(255),
            "UpdateTime" TIMESTAMP(0),
            "DepartmentId" INT DEFAULT 0,
            NOT CLUSTER PRIMARY KEY("AlarmNotifyConfigId"));

            CREATE TABLE "alarmnotifyelement"
            (
            "ElementId" INT AUTO_INCREMENT NOT NULL,
            "ElementName" VARCHAR(128) NOT NULL,
            "ElementType" VARCHAR(64) NOT NULL,
            "InputNodesCount" INT NOT NULL,
            "OutputNodesCount" INT NOT NULL,
            "Icon" VARCHAR(64),
            "Expression" VARCHAR(500),
            "Visible" TINYINT NOT NULL,
            "SortIndex" INT NOT NULL,
            "Description" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("ElementId"));

            CREATE TABLE "alarmnotifyelementconfig"
            (
            "AlarmNotifyElementConfigId" INT AUTO_INCREMENT NOT NULL,
            "AlarmNotifyConfigId" INT NOT NULL,
            "ElementId" INT NOT NULL,
            "Expression" VARCHAR(1000),
            "ExtendField1" VARCHAR(500),
            "ExtendField2" VARCHAR(500),
            "ExtendField3" VARCHAR(500),
            NOT CLUSTER PRIMARY KEY("AlarmNotifyElementConfigId"));

            CREATE TABLE "alarmnotifyfiltercondition"
            (
            "FilterConditionId" INT NOT NULL,
            "FilterConditionName" VARCHAR(128) NOT NULL,
            NOT CLUSTER PRIMARY KEY("FilterConditionId"));

            CREATE TABLE "alarmnotifyfilterrule"
            (
            "AlarmNotifyFilterRuleId" INT AUTO_INCREMENT NOT NULL,
            "AlarmNotifyConfigId" INT NOT NULL,
            "FilterConditionId" INT NOT NULL,
            "FilterParameter" VARCHAR(10000) NOT NULL,
            NOT CLUSTER PRIMARY KEY("AlarmNotifyFilterRuleId"));

            CREATE TABLE "alarmnotifygatewayservice"
            (
            "AlarmNotifyGatewayServiceId" INT AUTO_INCREMENT NOT NULL,
            "ElementId" INT,
            "GatewayServiceUrl" VARCHAR(64),
            "Description" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("AlarmNotifyGatewayServiceId"));

            CREATE TABLE "alarmnotifygatewayserviceconfig"
            (
            "AlarmNotifyGatewayServiceConfigId" INT AUTO_INCREMENT NOT NULL,
            "AlarmNotifyConfigId" INT,
            "AlarmNotifyGatewayServiceId" INT,
            NOT CLUSTER PRIMARY KEY("AlarmNotifyGatewayServiceConfigId"));

            CREATE TABLE "alarmnotifynode"
            (
            "NodeId" INT AUTO_INCREMENT NOT NULL,
            "AlarmNotifyElementConfigId" INT NOT NULL,
            "NodeDirection" VARCHAR(10) NOT NULL,
            "NodeType" VARCHAR(64) NOT NULL,
            "NodeIndex" INT NOT NULL,
            "NodeTag" VARCHAR(255),
            "Expression" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("NodeId"));

            CREATE TABLE "alarmnotifyrecord"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "AlarmNotifyConfigId" INT NOT NULL,
            "StationId" INT NOT NULL,
            "EquipmentId" INT NOT NULL,
            "EventId" INT NOT NULL,
            "EventConditionId" INT NOT NULL,
            "SequenceId" VARCHAR(128) NOT NULL,
            "EventSeverityId" INT NOT NULL,
            "Content" VARCHAR(1000) NOT NULL,
            "AlarmStartTime" TIMESTAMP(0) NOT NULL,
            "SendTime" TIMESTAMP(0) NOT NULL,
            "Receiver" VARCHAR(255) NOT NULL,
            "SendType" VARCHAR(20) NOT NULL,
            "SendResult" VARCHAR(100) NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "alarmnotifysegment"
            (
            "SegmentId" INT AUTO_INCREMENT NOT NULL,
            "InputElementConfigId" INT NOT NULL,
            "InputNodeId" INT NOT NULL,
            "OutputElementConfigId" INT NOT NULL,
            "OutputNodeId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("SegmentId"));
        </sql>
    </changeSet>
</databaseChangeLog>