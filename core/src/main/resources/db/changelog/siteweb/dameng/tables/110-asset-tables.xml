<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-110" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "assetcategory"
            (
            "AssetCategoryId" INT AUTO_INCREMENT NOT NULL,
            "AssetCategoryName" VARCHAR(128),
            "Remarks" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("AssetCategoryId"));

            CREATE TABLE "assetdevice"
            (
            "AssetDeviceId" INT AUTO_INCREMENT NOT NULL,
            "SortIndex" INT,
            "AssetCode" VARCHAR(128),
            "AssetName" VARCHAR(128),
            "AssetCategoryId" INT,
            "Brand" VARCHAR(128),
            "Model" VARCHAR(128),
            "CapacityParameter" VARCHAR(128),
            "SettingPosition" VARCHAR(128),
            "SerialNumber" VARCHAR(128),
            "Manufactor" VARCHAR(128),
            "TableName" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("AssetDeviceId"));

            CREATE TABLE "extfieldconfiguration"
            (
            "ExtId" INT AUTO_INCREMENT NOT NULL,
            "ExtTable" VARCHAR(50),
            "ExtCode" VARCHAR(50),
            "ExtName" VARCHAR(100),
            "ExtDesc" VARCHAR(200),
            "ExtOrder" INT,
            "ExtNecessary" TINYINT,
            "ExtDataType" VARCHAR(50),
            "ExtDataSource" VARCHAR(300),
            NOT CLUSTER PRIMARY KEY("ExtId"));


            CREATE TABLE "extvalueconfiguration"
            (
            "ExtValId" INT AUTO_INCREMENT NOT NULL,
            "ExtId" INT,
            "ExtTable" VARCHAR(50),
            "ExtTablePkId" INT,
            "ExtValue" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("ExtValId"));
        </sql>
    </changeSet>
</databaseChangeLog>