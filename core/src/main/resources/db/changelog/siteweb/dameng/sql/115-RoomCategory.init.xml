<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-115" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (1, '包间', '#FF7F00');
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (2, '包间空调间', '#007FFF');
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (3, '变电所', '#00B285');
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (4, '变电所空调间', '#007FFF');
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (5, '电池室', '#00FF00');
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (6, '弱电间', '#9673FF');
            INSERT INTO "roomcategory"("RoomCategoryId", "RoomCategoryName", "Color") VALUES (10, '其他', '#D9D900');
        </sql>
    </changeSet>
</databaseChangeLog>