<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2-052" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            update "tbl_doorcontroller" set Display=16 where DoorControlId=16;

            TRUNCATE TABLE "NBR_CallBackEventDic";

            INSERT INTO "NBR_CallBackEventDic" ("Major", "MajorDesc", "Minor", "MinorDesc", "MinorHex", "AlarmEndMinor", "ChannelNo")
            VALUES (10001, '刷卡状态', 0, '刷卡没有错误',  null, -1, 169);

            INSERT INTO NBR_CallBackEventDic (Major, MajorDesc, Minor, MinorDesc, MinorHex, AlarmEndMinor, ChannelNo)
            VALUES (10001, '刷卡状态', 1, '非法卡',  null, -1, 169);

            INSERT INTO "NBR_CallBackEventDic" ("Major", "MajorDesc", "Minor", "MinorDesc", "MinorHex", "AlarmEndMinor", "ChannelNo")
            VALUES (10002, '门状态', 0, '关',  null, -1, 28);

            INSERT INTO "NBR_CallBackEventDic" ("Major", "MajorDesc", "Minor", "MinorDesc", "MinorHex", "AlarmEndMinor", "ChannelNo")
            VALUES (10002, '门状态', 1, '开',  null, 0, 28);
        </sql>
    </changeSet>
</databaseChangeLog>