package com.siteweb.datacharts.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@TableName(value = "chartapi", autoResultMap = true)
@NoArgsConstructor
public class ChartApi {


    @TableId(value = "apiId", type = IdType.AUTO)

    private Integer apiId;


    /**
     * API名称
     */
    private String apiName;

    /**
     * API类别
     */
    private String category;

    /**
     * 请求URL
     */
    private String url;


    /**
     * 请求方式 POST GET PUT DELETE
     */
    private String method;

    /**
     * 请求参数Schema
     */
    //@TableField(value = "paramSchema", typeHandler = JacksonTypeHandler.class)
    private JsonNode paramSchema;


    /**
     * js 变换脚本
     */
    private String transform;
}