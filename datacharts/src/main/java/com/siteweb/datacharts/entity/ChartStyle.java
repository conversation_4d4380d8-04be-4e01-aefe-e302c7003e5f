package com.siteweb.datacharts.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("chartstyle")
@NoArgsConstructor
public class ChartStyle {


    @TableId(value = "styleId", type = IdType.AUTO)
    private Integer styleId;


    private String styleName;


    private Integer chartId;


    private String thumbnail;


    private String expression;



}