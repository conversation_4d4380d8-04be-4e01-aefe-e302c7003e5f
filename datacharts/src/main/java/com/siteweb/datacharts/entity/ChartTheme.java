package com.siteweb.datacharts.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName(value = "charttheme",autoResultMap = true)
@NoArgsConstructor
public class ChartTheme {
    @TableId(value = "themeId", type = IdType.AUTO)
    private Integer themeId;

    private String themeName;

    private String themeCode;


    //@TableField(value = "themeData", typeHandler = JacksonTypeHandler.class)
    private JsonNode themeData;

    private Boolean themeDefault;
}