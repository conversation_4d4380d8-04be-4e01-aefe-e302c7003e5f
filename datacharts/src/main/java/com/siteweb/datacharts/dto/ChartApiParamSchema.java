package com.siteweb.datacharts.dto;


import lombok.Data;

import java.util.List;

@Data
public class ChartApiParamSchema {

    /**
     * 请求参数名 字段名
     * 如果 API Method为POST field为body时 则只能有一个参数 并把参数作为body传入
     */
    private String name;

    /**
     * 参数来源 input , router@xxxxx
     */
    private String source;

    /**
     * 请求参数含义，显示到前端界面
     */
    private String meaning;

    /**
     * 参数是否为数组类型
     */
    private boolean isArray;

    /**
     * 该参数是否为必须的
     */
    private boolean required;

    /**
     * 前端参数选择器组件名
     */
    private String component;


    /**
     * 自定义组件数据源（特定组件，通用列表选择器）
     */
    private String customData;


    /**
     * 默认值，前端初始化时的初始值
     */
    private String defaultValue;

    /**
     * 子参数
     */
    private List<ChartApiParamSchema> children;
}

