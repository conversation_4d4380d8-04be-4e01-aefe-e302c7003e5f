package com.siteweb.datacharts.dto;

import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel(value = "Api代理参数")
public class ApiProxyRequest {

    /**
     * 请求Url
     * <p>
     * 参数格式 http://*************:8000/api/v1/hostresource/{id}
     */
    @ApiModelProperty(value = "restful api url", required = true)
    public String url;

    /**
     * 请求方法
     * Post
     * Get
     */
    @ApiModelProperty(value = "http method(“get” or “post”)", required = true)
    public String method;

    /**
     * get 请求参数
     * 该参数必须包含在url内
     */
    @ApiModelProperty(value = "当method为get时传递url内的{}参数\n当method为post时传递body数据", required = true)
    public Object query;







}
