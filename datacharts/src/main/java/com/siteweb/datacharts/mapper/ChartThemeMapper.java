package com.siteweb.datacharts.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.datacharts.entity.ChartTheme;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChartThemeMapper  extends BaseMapper<ChartTheme> {
    List<ChartTheme> findAllThemes();


    ChartTheme findDefaultTheme();


    void updateDefaultTheme(Integer themeId);

    void createTheme(@Param("theme") ChartTheme theme);

    void updateTheme(@Param("theme") ChartTheme theme);
}