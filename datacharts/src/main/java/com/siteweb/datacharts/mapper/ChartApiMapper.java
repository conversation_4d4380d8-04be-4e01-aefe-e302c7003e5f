package com.siteweb.datacharts.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.datacharts.entity.ChartApi;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChartApiMapper  extends BaseMapper<ChartApi> {
    List<ChartApi> findAllApis();


    Integer createApi(@Param("api") ChartApi api);

    Integer updateApi(@Param("api") ChartApi api);
}
