package com.siteweb.datacharts.service.impl;


import com.siteweb.common.properties.ApiProxyProperties;
import com.siteweb.common.util.RegularUtils;
import com.siteweb.datacharts.dto.ApiProxyRequest;
import com.siteweb.datacharts.service.ApiProxyService;
import com.siteweb.datacharts.service.ChartApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


@Service()
@Configuration
@Slf4j
public class ApiProxyServiceImpl implements ApiProxyService {

    private static final String PLACEHOLDER = "\\{(.+?)\\}";


    @Autowired
    private ChartApiService chartApiService;

    @Value("${server.ssl.enable}")
    Boolean enableSSL;

    @Value("${server.http.port}")
    Integer httpProt;

    @Value("${server.port}")
    Integer httpsProt;

    @Autowired
    ApiProxyProperties apiProxyProperties;
    @Autowired
    @Qualifier("proxyRestTemplateSSL")
    public RestTemplate proxyRestTemplateSSL;


    @Override
    public ResponseEntity proxy(MultiValueMap<String, String> reqHeaders, ApiProxyRequest apiInfo) throws RuntimeException {
        HttpHeaders headers = new HttpHeaders();
        HttpMethod method = this.getMethod(apiInfo.method);
        if (method == null) {
            throw new RuntimeException("不支持的方法类型。");
        }
        ResponseEntity result = null;
        if (!apiInfo.url.toLowerCase().startsWith("http")) {
            apiInfo.url = String.format("http://localhost:%d/%s", httpProt, apiInfo.url);
        }
        // reqHeaders reqHeaders
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE + "," + MediaType.ALL_VALUE);
        headers.set(HttpHeaders.AUTHORIZATION, reqHeaders.get("authorization").get(0));
        Object body = method.equals(HttpMethod.POST) ? apiInfo.query : null;

        HttpEntity entity = new HttpEntity(body, headers);
        long startTime = System.currentTimeMillis();
        if (apiInfo.query instanceof LinkedHashMap map) {
            List<String> paramsList = RegularUtils.getParams(PLACEHOLDER, apiInfo.url);
            paramsList.forEach(e -> {
                if (!map.containsKey(e)) {
                    map.put(e, null);
                }
            });
            // 仅当query为map类型时传递uriVariables
            result = proxyRestTemplateSSL.exchange(apiInfo.url, method, entity, Object.class, map);
        } else if (apiInfo.query instanceof ArrayList) {
            // 当 query 内容为list时，不传递uriVariables
            result = proxyRestTemplateSSL.exchange(apiInfo.url, method, entity, Object.class);
        }
        long endTime = (System.currentTimeMillis()) - startTime;
        log.info("{}---{}ApiProxy接口耗时:{}", method, apiInfo.url, endTime);
        if (endTime >= apiProxyProperties.getTimeoutLog() * 1000L) {
            log.error("{}---{}ApiProxy接口耗时:{}", method, apiInfo.url, endTime);
        }
        return result;
    }

    private HttpMethod getMethod(String method) {
        String _method = method.toUpperCase();
        if (_method.equals("GET")) return HttpMethod.GET;
        if (_method.equals("POST")) return HttpMethod.POST;
        return null;
    }

}