package com.siteweb.datacharts.vo;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.datacharts.entity.ChartTheme;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ChartThemeVO {
    private Integer themeId;
    private String themeName;
    private String themeCode;
    private ObjectNode themeData;
    private Boolean themeDefault;

    public ChartTheme buildEntity(){
        ChartTheme theme = new ChartTheme();
        theme.setThemeId(this.themeId);
        theme.setThemeName(this.themeName);
        theme.setThemeCode(this.themeCode);
        theme.setThemeData(this.themeData);
        theme.setThemeDefault(themeDefault);
        return theme;
    }
}
