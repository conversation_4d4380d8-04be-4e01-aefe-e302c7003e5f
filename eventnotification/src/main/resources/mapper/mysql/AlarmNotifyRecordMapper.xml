<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifyRecordMapper">
    <select id="findByReportParam" resultType="com.siteweb.eventnotification.dto.AlarmNotifyRecordDto">
        SELECT equipment.equipmentName AS equipmentName,
               event.EventName         AS eventName,
               eventCondition.Meanings AS eventMeanings,
               dataItem.itemValue      AS eventSeverity,
               record.Content          AS content,
               record.AlarmStartTime   AS alarmStartTime,
               record.SendTime         AS sendTime,
               record.Receiver         AS receiver,
               record.SendType         AS sendType,
               record.SendResult       AS sendResult
        FROM alarmnotifyrecord record
                 INNER JOIN tbl_equipment equipment ON record.EquipmentId = equipment.EquipmentId
                 INNER JOIN tbl_equipmenttemplate template ON template.EquipmentTemplateId = equipment.EquipmentTemplateid
                 INNER JOIN tbl_event event ON event.EquipmentTemplateId = template.EquipmentTemplateId AND record.EventId = event.EventId
                 INNER JOIN tbl_eventcondition eventCondition ON eventCondition.EquipmentTemplateId = equipment.EquipmentTemplateid AND
                               eventCondition.EventId = event.EventId AND
                               record.EventConditionId = EventCondition.EventConditionId
                 INNER JOIN tbl_dataitem dataItem ON dataItem.EntryId = 23 AND dataItem.ExtendField4 = record.EventSeverityId
        WHERE (record.AlarmStartTime BETWEEN #{alarmNotifyRecordVO.alarmStartTimeFrom} AND #{alarmNotifyRecordVO.alarmStartTimeTo})
        AND (record.sendTime BETWEEN #{alarmNotifyRecordVO.sendTimeFrom} AND #{alarmNotifyRecordVO.sendTimeTo})
        <if test="alarmNotifyRecordVO.sendType != null and alarmNotifyRecordVO.sendType != '' and alarmNotifyRecordVO.sendType != '0'.toString()">
            AND record.sendType = #{alarmNotifyRecordVO.sendType}
        </if>
        <if test="alarmNotifyRecordVO.sendResult != null and alarmNotifyRecordVO.sendResult != ''  and alarmNotifyRecordVO.sendResult != '0'.toString()">
            AND record.sendResult = #{alarmNotifyRecordVO.sendResult}
        </if>
         AND equipment.equipmentId IN
         <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
         </foreach>
    </select>
    <select id="findByReportParamPage" resultType="com.siteweb.eventnotification.dto.AlarmNotifyRecordDto">
        SELECT equipment.equipmentName AS equipmentName,
        event.EventName         AS eventName,
        eventCondition.Meanings AS eventMeanings,
        dataItem.itemValue      AS eventSeverity,
        record.Content          AS content,
        record.AlarmStartTime   AS alarmStartTime,
        record.SendTime         AS sendTime,
        record.Receiver         AS receiver,
        record.SendType         AS sendType,
        record.SendResult       AS sendResult
        FROM alarmnotifyrecord record
        INNER JOIN tbl_equipment equipment ON record.EquipmentId = equipment.EquipmentId
        INNER JOIN tbl_equipmenttemplate template ON template.EquipmentTemplateId = equipment.EquipmentTemplateid
        INNER JOIN tbl_event event ON event.EquipmentTemplateId = template.EquipmentTemplateId AND record.EventId = event.EventId
        INNER JOIN tbl_eventcondition eventCondition ON eventCondition.EquipmentTemplateId = equipment.EquipmentTemplateid AND
        eventCondition.EventId = event.EventId AND
        record.EventConditionId = EventCondition.EventConditionId
        INNER JOIN tbl_dataitem dataItem ON dataItem.EntryId = 23 AND dataItem.ExtendField4 = record.EventSeverityId
        WHERE (record.AlarmStartTime BETWEEN #{alarmNotifyRecordVO.alarmStartTimeFrom} AND #{alarmNotifyRecordVO.alarmStartTimeTo})
        AND (record.sendTime BETWEEN #{alarmNotifyRecordVO.sendTimeFrom} AND #{alarmNotifyRecordVO.sendTimeTo})
        <if test="alarmNotifyRecordVO.sendType != null and alarmNotifyRecordVO.sendType != '' and alarmNotifyRecordVO.sendType != '0'.toString()">
            AND record.sendType = #{alarmNotifyRecordVO.sendType}
        </if>
        <if test="alarmNotifyRecordVO.sendResult != null and alarmNotifyRecordVO.sendResult != '' and alarmNotifyRecordVO.sendResult != '0'.toString()">
            AND record.sendResult = #{alarmNotifyRecordVO.sendResult}
        </if>
        AND equipment.equipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="findByReportParamCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM alarmnotifyrecord record
        INNER JOIN tbl_equipment equipment ON record.EquipmentId = equipment.EquipmentId
        INNER JOIN tbl_equipmenttemplate template ON template.EquipmentTemplateId = equipment.EquipmentTemplateid
        INNER JOIN tbl_event event ON event.EquipmentTemplateId = template.EquipmentTemplateId AND record.EventId =
        event.EventId
        INNER JOIN tbl_eventcondition eventCondition ON eventCondition.EquipmentTemplateId = equipment.EquipmentTemplateid
        AND eventCondition.EventId = event.EventId AND record.EventConditionId = EventCondition.EventConditionId
        INNER JOIN tbl_dataitem dataItem ON dataItem.EntryId = 23 AND dataItem.ExtendField4 = record.EventSeverityId
        WHERE (record.AlarmStartTime BETWEEN #{alarmNotifyRecordVO.alarmStartTimeFrom} AND
        #{alarmNotifyRecordVO.alarmStartTimeTo})
        AND (record.sendTime BETWEEN #{alarmNotifyRecordVO.sendTimeFrom} AND #{alarmNotifyRecordVO.sendTimeTo})
        <if test="alarmNotifyRecordVO.sendType != null and alarmNotifyRecordVO.sendType != '' and alarmNotifyRecordVO.sendType != '0'.toString()">
            AND record.sendType = #{alarmNotifyRecordVO.sendType}
        </if>
        <if test="alarmNotifyRecordVO.sendResult != null and alarmNotifyRecordVO.sendResult != '' and alarmNotifyRecordVO.sendResult != '0'.toString()">
            AND record.sendResult = #{alarmNotifyRecordVO.sendResult}
        </if>
        AND equipment.equipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="findAlarmNotifyRecordsByKeywords" resultType="com.siteweb.eventnotification.entity.AlarmNotifyRecord">
        SELECT a.Id, a.AlarmNotifyConfigId, a.StationId, a.EquipmentId, a.EventId, a.EventConditionId, a.SequenceId,
        a.EventSeverityId, a.Content, a.AlarmStartTime, a.SendTime, a.Receiver, a.SendType, a.SendResult
        FROM AlarmNotifyRecord a
        <where>
            <if test="keywords != null and keywords != ''">
                AND (a.Content like concat('%',#{keywords},'%') or a.Receiver like concat('%',#{keywords},'%') or
                a.SendType like concat('%',#{keywords},'%') )
            </if>
        </where>
        order by a.sendTime desc
    </select>
</mapper>