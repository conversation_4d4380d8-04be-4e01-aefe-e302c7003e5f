<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.TtsConfigMapper">
    <update id="batchUpdate">
        <foreach collection="ttsConfigList" item="ttsConfig">
            UPDATE ttsconfig SET TtsConfigValue = #{ttsConfig.ttsConfigValue} where TtsConfigKey = #{ttsConfig.ttsConfigKey};
        </foreach>
    </update>
</mapper>