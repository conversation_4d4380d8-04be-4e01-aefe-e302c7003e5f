<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmChangeSnapshotMapper">
    <insert id="batchInsertAlarmChangeSnapshot">
        INSERT INTO alarmchangesnapshot(SequenceId, OperationType,SnapshotUrl,CameraId,CameraName,StartTime) VALUES
        <foreach collection="alarmChangeSnapshotList" item="item" separator=",">
            (#{item.sequenceId},#{item.operationType},#{item.snapshotUrl},#{item.cameraId},#{item.cameraName},#{item.startTime})
        </foreach>
    </insert>
    <sql id="findAllEventSql">
        SELECT *
        FROM (SELECT activeEvent.eventSeverity,
        activeEvent.EquipmentName,
        activeEvent.EquipmentCategoryName,
        activeEvent.eventname,
        activeEvent.EventValue,
        activeEvent.meanings,
        activeEvent.StartTime,
        activeEvent.ConfirmTime,
        activeEvent.ConfirmerName,
        activeEvent.EndTime,
        activeEvent.ResourceStructureId,
        activeEvent.SequenceId
        FROM tbl_activeevent activeEvent
        <where>
            <if test="resourceStructureIds != null and resourceStructureIds.size > 0">
                and activeEvent.ResourceStructureId in
                <foreach collection="resourceStructureIds" open="(" close=")" separator="," item="resourceStructureId">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="alarmChangeSnapshotList != null and alarmChangeSnapshotList.size > 0">
                and
                <foreach collection="alarmChangeSnapshotList" open="(" close=")" separator=" or " item="item">
                    (activeEvent.SequenceId =#{item.sequenceId} and activeEvent.StartTime = #{item.startTime})
                </foreach>
            </if>
            <if test="alarmChangeSnapshotFilterDTO.keywords != null and alarmChangeSnapshotFilterDTO.keywords != ''">
                AND (activeEvent.EventSeverity LIKE CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords}, '%')
                OR activeEvent.EquipmentName like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR activeEvent.EventName like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR activeEvent.StartTime like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR activeEvent.Meanings like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%'))
            </if>
        </where>
        UNION ALL
        SELECT historyEvent.eventSeverity,
        historyEvent.EquipmentName,
        historyEvent.EquipmentCategoryName,
        historyEvent.eventname,
        historyEvent.EventValue,
        historyEvent.meanings,
        historyEvent.StartTime,
        historyEvent.ConfirmTime,
        historyEvent.ConfirmerName,
        historyEvent.EndTime,
        historyEvent.ResourceStructureId,
        historyEvent.SequenceId
        FROM tbl_historyevent historyEvent
        <where>
            <if test="resourceStructureIds != null and resourceStructureIds.size > 0">
                and historyEvent.ResourceStructureId in
                <foreach collection="resourceStructureIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="alarmChangeSnapshotList != null and alarmChangeSnapshotList.size > 0">
                and
                <foreach collection="alarmChangeSnapshotList" open="(" close=")" separator=" or " item="item">
                    (historyEvent.SequenceId = #{item.sequenceId}  and historyEvent.StartTime = #{item.startTime})
                </foreach>
            </if>
            <if test="alarmChangeSnapshotFilterDTO.keywords != null and alarmChangeSnapshotFilterDTO.keywords != ''">
                AND (historyEvent.EventSeverity LIKE CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords}, '%')
                OR historyEvent.EquipmentName like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR historyEvent.EventName like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR historyEvent.StartTime like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR historyEvent.EndTime like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%')
                OR historyEvent.Meanings like CONCAT('%', #{alarmChangeSnapshotFilterDTO.keywords},'%'))
            </if>
        </where>
        ) AS allEvent
        order by
        <choose>
            <when test="alarmChangeSnapshotFilterDTO.field != null and alarmChangeSnapshotFilterDTO.field != ''">
                ${alarmChangeSnapshotFilterDTO.field}
                <if test="alarmChangeSnapshotFilterDTO.order != null and alarmChangeSnapshotFilterDTO.order != ''">
                    ${alarmChangeSnapshotFilterDTO.order}
                </if>
            </when>
            <otherwise>
                allEvent.StartTime desc
            </otherwise>
        </choose>
    </sql>
    <select id="findAllEventDTOBySnapshot" resultType="com.siteweb.eventnotification.dto.AllEventDTO">
        <include refid="findAllEventSql"/>
    </select>
</mapper>