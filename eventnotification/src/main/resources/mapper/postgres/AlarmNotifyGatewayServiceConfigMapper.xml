<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifyGatewayServiceConfigMapper">
    <insert id="batchInsert">
        INSERT INTO alarmnotifygatewayserviceconfig(AlarmNotifyConfigId, AlarmNotifyGatewayServiceId) VALUES
        <foreach collection="alarmNotifyGatewayServiceConfigList" item="item" separator=",">
            (#{item.alarmNotifyConfigId},#{item.alarmNotifyGatewayServiceId})
        </foreach>
    </insert>
    <select id="findAlarmNotifyGatewayServicesByAlarmNotifyConfigId"
            resultType="com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO">
        select c.AlarmNotifyGatewayServiceConfigId, c.AlarmNotifyConfigId, c.AlarmNotifyGatewayServiceId, s.ElementId,
        s.GatewayServiceUrl, s.Description, e.ElementName, con.UsedStatus
        from alarmnotifygatewayserviceconfig c
        left join alarmnotifygatewayservice s on s.AlarmNotifyGatewayServiceId = c.AlarmNotifyGatewayServiceId
        left join alarmnotifyelement e on s.ElementId = e.ElementId
        left join alarmnotifyconfig con on con.AlarmNotifyConfigId = c.AlarmNotifyConfigId
        where c.AlarmNotifyConfigId = #{alarmNotifyConfigId}
    </select>

    <select id="findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyConfigIdAndElementId"
            resultType="com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO">
        select c.AlarmNotifyGatewayServiceConfigId, c.AlarmNotifyConfigId, c.AlarmNotifyGatewayServiceId, s.ElementId, s.GatewayServiceUrl, s.Description, e.ElementName
        from alarmnotifygatewayserviceconfig c
        left join alarmnotifygatewayservice s on s.AlarmNotifyGatewayServiceId = c.AlarmNotifyGatewayServiceId
        left join alarmnotifyelement e on s.ElementId = e.ElementId
        where c.AlarmNotifyConfigId = #{alarmNotifyConfigId} and e.ElementId = #{elementId}
    </select>
</mapper>