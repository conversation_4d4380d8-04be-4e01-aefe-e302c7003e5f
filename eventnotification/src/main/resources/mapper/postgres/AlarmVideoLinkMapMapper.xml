<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmVideoLinkMapMapper">
    <select id="findByCondition" resultType="com.siteweb.eventnotification.dto.AlarmVideoLinkSnapshotMap">
        SELECT map.alarmvideolinkmapid,
        map.alarmvideolinkid,
        map.equipmentid,
        map.cameraids,
        map.eventid,
        map.eventconditionid,
        map.cameraid,
        link.LinkType,
        link.OperationType,
        link.SnapshotCount,
        link.SnapshotInterval
        FROM alarmvideolinkmap map join alarmvideolink link
        on map.alarmvideolinkid = link.alarmvideolinkid
        <where>
            <if test="equipmentId != null">
                AND map.EquipmentId = #{equipmentId}
            </if>
            <if test="equipmentId == null">
                AND map.EquipmentId is null
            </if>
            <if test="eventId != null">
                AND map.EventId = #{eventId}
            </if>
            <if test="eventId == null">
                AND map.EventId is null
            </if>
            <if test="eventConditionId != null">
                AND map.EventConditionId = #{eventConditionId}
            </if>
            <if test="eventConditionId == null">
                AND map.EventConditionId is null
            </if>
            and link.usedstatus = 1
        </where>
    </select>
</mapper>