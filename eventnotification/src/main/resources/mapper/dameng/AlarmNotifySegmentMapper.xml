<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifySegmentMapper">
    <select id="findAlarmNotifySegmentsByAlarmNotifyConfigId"
            resultType="com.siteweb.eventnotification.entity.AlarmNotifySegment">
        SELECT SegmentId, InputElementConfigId, InputNodeId, OutputElementConfigId, OutputNodeId
        FROM AlarmNotifySegment a INNER JOIN AlarmNotifyElementConfig b on a.InputElementConfigId = b.AlarmNotifyElementConfigId
        WHERE b.AlarmNotifyConfigId = #{alarmNotifyConfigId}
    </select>
</mapper>