<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifyGatewayServiceMapper">
    <select id="findAlarmNotifyGatewayServicesUsedStatusByElementId"
            resultType="com.siteweb.eventnotification.entity.AlarmNotifyGatewayService">
        select s.AlarmNotifyGatewayServiceId, s.ElementId, s.GatewayServiceUrl, s.Description, e.ElementName, con.UsedStatus
        from AlarmNotifyGatewayService s
        left join alarmnotifygatewayserviceconfig c on s.AlarmNotifyGatewayServiceId = c.AlarmNotifyGatewayServiceId
        left join alarmnotifyconfig con on con.AlarmNotifyConfigId = c.AlarmNotifyConfigId
        left join alarmnotifyelement e on s.ElementId = e.ElementId
        where s.ElementId = #{elementId}
    </select>

    <select id="findAllAlarmNotifyGatewayService"
            resultType="com.siteweb.eventnotification.entity.AlarmNotifyGatewayService">
        select s.AlarmNotifyGatewayServiceId, s.ElementId, s.GatewayServiceUrl, s.Description, e.ElementName
        from AlarmNotifyGatewayService s left join alarmnotifyelement e
        on s.ElementId = e.ElementId
    </select>

    <select id="findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyGatewayServiceIds"
            resultType="com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO">
        select c.AlarmNotifyGatewayServiceConfigId, c.AlarmNotifyConfigId, c.AlarmNotifyGatewayServiceId, s.ElementId,
        s.GatewayServiceUrl, s.Description, e.ElementName, con.UsedStatus
        from alarmnotifygatewayserviceconfig c
        left join alarmnotifygatewayservice s on s.AlarmNotifyGatewayServiceId = c.AlarmNotifyGatewayServiceId
        left join alarmnotifyelement e on s.ElementId = e.ElementId
        left join alarmnotifyconfig con on con.AlarmNotifyConfigId = c.AlarmNotifyConfigId
        where s.AlarmNotifyGatewayServiceId in
        <foreach collection="alarmNotifyGatewayServiceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findAllAlarmNotifyGatewayServiceConfigDetailDTO"
            resultType="com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO">
        select c.AlarmNotifyGatewayServiceConfigId, c.AlarmNotifyConfigId, c.AlarmNotifyGatewayServiceId, s.ElementId,
        s.GatewayServiceUrl, s.Description, e.ElementName, con.UsedStatus
        from alarmnotifygatewayserviceconfig c
        left join alarmnotifygatewayservice s on s.AlarmNotifyGatewayServiceId = c.AlarmNotifyGatewayServiceId
        left join alarmnotifyelement e on s.ElementId = e.ElementId
        left join alarmnotifyconfig con on con.AlarmNotifyConfigId = c.AlarmNotifyConfigId
    </select>
</mapper>