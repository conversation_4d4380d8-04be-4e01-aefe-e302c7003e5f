<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifyConfigMapper">
    <delete id="deleteAlarmNotifyConfigById">
        DELETE FROM alarmnotifysegment where InputElementConfigId in (SELECT AlarmNotifyElementConfigId FROM AlarmNotifyElementConfig WHERE AlarmNotifyConfigId = #{alarmNotifyConfigId});
        DELETE FROM alarmnotifynode where AlarmNotifyElementConfigId in (SELECT AlarmNotifyElementConfigId FROM AlarmNotifyElementConfig WHERE AlarmNotifyConfigId = #{alarmNotifyConfigId});
        DELETE FROM alarmnotifyelementconfig where AlarmNotifyConfigId = #{alarmNotifyConfigId};
        DELETE FROM alarmnotifyfilterrule where AlarmNotifyConfigId = #{alarmNotifyConfigId};
        DELETE FROM alarmnotifyconfig where AlarmNotifyConfigId = #{alarmNotifyConfigId};
        DELETE FROM alarmnotifygatewayserviceconfig where AlarmNotifyConfigId = #{alarmNotifyConfigId};
    </delete>
</mapper>