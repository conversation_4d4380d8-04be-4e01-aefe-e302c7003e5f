<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifyNodeMapper">
    <select id="findAlarmNotifyNodesByAlarmNotifyConfigId"
            resultType="com.siteweb.eventnotification.entity.AlarmNotifyNode">
        SELECT a.NodeId, a.AlarmNotifyElementConfigId, a.NodeDirection, a.NodeType, a.NodeIndex, a.NodeTag, a.Expression
        FROM AlarmNotifyNode a INNER JOIN AlarmNotifyElementConfig b on a.AlarmNotifyElementConfigId = b.AlarmNotifyElementConfigId
        WHERE b.alarmNotifyConfigId = #{alarmNotifyConfigId}
    </select>
</mapper>