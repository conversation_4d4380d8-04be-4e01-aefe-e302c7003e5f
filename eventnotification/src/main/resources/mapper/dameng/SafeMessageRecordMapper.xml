<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.SafeMessageRecordMapper">
    <select id="findTodaySendRecord" resultType="java.lang.Integer">
        SELECT record.SafeMessageId
        FROM safemessagerecord record
        WHERE TO_DATE(record.SendTime, 'YYYY-MM-DD') = TO_DATE(SYSDATE, 'YYYY-MM-DD');
        AND record.SafeMessageId IN
        <foreach collection="safeMessageIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>