<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.AlarmNotifyElementConfigMapper">
    <delete id="deleteAlarmNotifyElementConfigByAlarmNotifyElementConfigId">
        DELETE FROM alarmnotifysegment where InputElementConfigId = #{alarmNotifyElementConfigId};
        DELETE FROM alarmnotifynode where AlarmNotifyElementConfigId = #{alarmNotifyElementConfigId};
        DELETE FROM alarmnotifyelementconfig where AlarmNotifyElementConfigId = #{alarmNotifyElementConfigId};
    </delete>
</mapper>