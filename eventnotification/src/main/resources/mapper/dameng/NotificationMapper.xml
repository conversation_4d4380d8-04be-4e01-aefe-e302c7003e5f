<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventnotification.mapper.NotificationMapper">

    <select id="findEquipmentCategoryIds" resultType="com.siteweb.eventnotification.vo.NotificationEquipmentCategoryVO">
        select ItemId, ItemValue from tbl_dataitem
        where entryId = 7
        <foreach collection="equipmentCategoryIds" item="item" separator="," open="and ItemId in (" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="findEventCategoryIds" resultType="com.siteweb.eventnotification.vo.NotificationEventCategoryVO">
        select ItemId, ItemValue from tbl_dataitem
        where entryId = 24
        <foreach collection="eventCategoryIds" item="item" separator="," open="and ItemId in (" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
</mapper>