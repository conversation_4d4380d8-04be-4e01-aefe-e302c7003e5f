package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementConfig
 * @createTime 2022-04-22 14:39:00
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifyelementconfig")
public class AlarmNotifyElementConfig {

    @TableId(value = "AlarmNotifyElementConfigId", type = IdType.AUTO)
    private Integer alarmNotifyElementConfigId;

    private Integer alarmNotifyConfigId;

    private Integer elementId;

    private String expression;

    private String extendField1;

    private String extendField2;

    private String extendField3;
}
