package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("alarmnotifygatewayserviceconfig")
public class AlarmNotifyGatewayServiceConfig {

    @TableId(value = "AlarmNotifyGatewayServiceConfigId", type = IdType.AUTO)
    private Integer alarmNotifyGatewayServiceConfigId;

    private Integer alarmNotifyConfigId;

    private Integer alarmNotifyGatewayServiceId;
}