package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("equipment")
public class VideoEquipment {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 摄像头编码
     */
    private String code;

    /**
     * 摄像头名称
     */
    private String name;

    @JsonProperty("endProxyId")
    private Integer end_proxy_id;

    /**
     * 摄像头访问用户名
     */
    @JsonProperty("userName")
    private String user_name;

    /**
     * 摄像头访问密码
     */
    @JsonProperty("userPassword")
    private String user_password;
    /**
     * 摄像头IP，这里的IP，可能是内网IP，无法直接访问。例如************这种的
     */
    private String ip;

    /**
     * 厂家摄像头序列号
     */
    @JsonProperty("serialNumber")
    private String serial_number;
    /**
     * 设备类型，例如：摄像头-1，NVR-2，CVR-3，SRS-4，存储-5
     */
    @JsonProperty("equipmentType")
    private Integer equipment_type;
    /**
     * 创建人ID
     */
    @JsonProperty("createUserId")
    private Integer create_user_id;

    private Date create_time;

    @JsonProperty("updateUserId")
    private Integer update_user_id;

    @JsonProperty("updateTime")
    private Date update_time;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Boolean deleted;
    /**
     * 加解密密钥
     */
    @JsonProperty("signKey")
    private String sign_key;
    /**
     * 协议名称，这里的是英文名称，例如 onvif halkon   dahua 等
     */
    @JsonProperty("protoName")
    private String proto_name;
    /**
     * 用于显示设备发现时的协议，供后期设备控制使用
     */
    @JsonProperty("protoShowName")
    private String proto_show_name;
    /**
     * 厂家摄像头序列号
     */
    private String uuid;

    @JsonProperty("equipmentStatusId")
    private Integer equipment_status_id;
    /**
     * 默认80
     */
    @JsonProperty("httpPort")
    private Integer http_port;
    /**
     * 默认554
     */
    @JsonProperty("rtspPort")
    private Integer rtsp_port;
    /**
     * 默认8000
     */
    @JsonProperty("serverPort")
    private Integer server_port;
}
