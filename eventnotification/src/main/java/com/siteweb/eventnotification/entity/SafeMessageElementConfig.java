package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("safemessageelementconfig")
public class SafeMessageElementConfig {
    /**
     * 主键自增
     */
    @TableId(type = IdType.AUTO)
    private Integer safeMessageElementConfigId;
    /**
     * 平安短信配置id
     */
    private Integer safeMessageId;
    /**
     * 元素类型 1信号  2指标
     */
    private Integer elementType;
    /**
     * 元素配置
     */
    private String elementSetting;
}
