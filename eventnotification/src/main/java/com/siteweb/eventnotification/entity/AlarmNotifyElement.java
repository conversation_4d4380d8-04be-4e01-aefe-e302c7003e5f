package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElement
 * @createTime 2022-04-22 14:36:10
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifyelement")
public class AlarmNotifyElement {

    @TableId(value = "ElementId", type = IdType.AUTO)
    private Integer elementId;

    private String elementName;

    private String elementType;

    private Integer inputNodesCount;

    private Integer outputNodesCount;

    private String icon;

    private String expression;

    private Boolean visible;

    private Integer sortIndex;

    private String description;
}
