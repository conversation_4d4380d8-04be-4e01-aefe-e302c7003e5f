package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElement
 * @createTime 2022-04-22 14:26:11
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifyconfig")
public class AlarmNotifyConfig {

    @TableId(value = "AlarmNotifyConfigId", type = IdType.AUTO)
    private Integer alarmNotifyConfigId;

    private String configName;

    private Boolean usedStatus;

    private String contentTemplate;

    private Integer notifyDelay;

    private String layout;

    private String description;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Integer departmentId;
}
