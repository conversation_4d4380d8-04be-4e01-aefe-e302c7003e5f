package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("alarmvideolinkmap")
public class AlarmVideoLinkMap {
    /**
     * 告警视频联动映射关系主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer alarmVideoLinkMapId;
    /**
     * 告警视频联动表id
     */
    private Integer alarmVideoLinkId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件条件id
     */
    private Integer eventConditionId;
    /**
     * 摄像头ids
     */
    private String cameraIds;
    /**
     * 摄像头id
     *
     */
    private Long cameraId;
}
