package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyRecord
 * @createTime 2022-04-22 14:47:51
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifyrecord")
public class AlarmNotifyRecord {

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    private Integer alarmNotifyConfigId;

    private Integer stationId;

    private Integer equipmentId;

    private Integer eventId;

    private Integer eventConditionId;

    private String sequenceId;

    private Integer eventSeverityId;

    private String content;

    private Date alarmStartTime;

    private Date sendTime;

    private String receiver;

    private String sendType;

    private String sendResult;
}
