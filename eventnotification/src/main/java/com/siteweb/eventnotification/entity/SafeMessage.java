package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("safemessage")
public class SafeMessage {
    /**
     * 主键自增
     */
    @TableId(type = IdType.AUTO)
    private Integer safeMessageId;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 接收方式  1短信  2语音  3.邮件
     */
    private String receiveMode;
    /**
     * 发送时间
     */
    private LocalTime sendTime;
    /**
     * 发送时间类型 1每天 2每周 3每月
     */
    private Integer sendType;
    /**
     * 每周的星期几与每月的多少号，多个用逗号隔开
     */
    private String sendTypeDescription;
    /**
     * corn表达式用于定时发送平安短信
     */
    private String cron;
    /**
     * 模板内容
     */
    private String contentTemplate;
    /**
     * 配置描述
     */
    private String description;
    /**
     * 启用状态
     */
    private Boolean usedStatus;
    /**
     * 元素配置项
     */
    @TableField(exist = false)
    private List<SafeMessageElementConfig> safeMessageElementConfigList;
}
