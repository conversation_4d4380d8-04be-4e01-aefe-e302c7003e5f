package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("safemessagerecord")
@Data
@NoArgsConstructor
public class SafeMessageRecord {
    public SafeMessageRecord(Integer safeMessageId, String sendContent, String receiver,String remark) {
        this.safeMessageId = safeMessageId;
        this.sendContent = sendContent;
        this.receiver = receiver;
        this.sendTime = new Date();
        this.remark = remark;
    }

    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private String safeMessageRecordId;
    /**
     * 安全短信配置id
     */
    private Integer safeMessageId;
    /**
     * 短信发送内容
     */
    private String sendContent;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 备注
     */
    private String remark;
}
