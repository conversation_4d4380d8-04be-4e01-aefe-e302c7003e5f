package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterRule
 * @createTime 2022-04-22 14:44:34
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifyfilterrule")
public class AlarmNotifyFilterRule {

    @TableId(value = "AlarmNotifyFilterRuleId", type = IdType.AUTO)
    private Integer alarmNotifyFilterRuleId;

    private Integer alarmNotifyConfigId;

    private Integer filterConditionId;

    private String filterParameter;
}
