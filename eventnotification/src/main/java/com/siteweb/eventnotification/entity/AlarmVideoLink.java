package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.List;

@Data
@TableName("alarmvideolink")
public class AlarmVideoLink {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer alarmVideoLinkId;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否启用
     */
    private Boolean usedStatus;
    /**
     * 部门id
     */
    private Integer departmentId;
    /**
     * 联动类型，1视频弹窗，2抓图，3未来可能是视频录制
     */
    private String linkType;
    /**
     * 事件状态，1告警开始，2告警结束
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String operationType;
    /**
     * 抓图张数
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer snapshotCount;
    /**
     * 抓图间隔
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer snapshotInterval;

    @TableField(exist = false)
    private List<AlarmVideoLinkMap> alarmVideoLinkMapList;
}
