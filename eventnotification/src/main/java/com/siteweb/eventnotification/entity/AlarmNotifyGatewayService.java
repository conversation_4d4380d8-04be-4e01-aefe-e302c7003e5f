package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("alarmnotifygatewayservice")
public class AlarmNotifyGatewayService {

    @TableId(value = "AlarmNotifyGatewayServiceId", type = IdType.AUTO)
    private Integer alarmNotifyGatewayServiceId;

    private Integer elementId;

    @TableField(exist = false)
    private String elementName;

    private String gatewayServiceUrl;

    private String description;

    @TableField(exist = false)
    private Boolean usedStatus;
}