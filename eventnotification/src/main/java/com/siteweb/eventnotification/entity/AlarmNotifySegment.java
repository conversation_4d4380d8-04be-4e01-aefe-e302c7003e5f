package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifySegment
 * @createTime 2022-04-22 14:49:47
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifysegment")
public class AlarmNotifySegment {

    @TableId(value = "SegmentId", type = IdType.AUTO)
    private Integer segmentId;

    private Integer inputElementConfigId;

    private Integer inputNodeId;

    private Integer outputElementConfigId;

    private Integer outputNodeId;
}
