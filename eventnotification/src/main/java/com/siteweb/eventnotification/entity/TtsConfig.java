package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("ttsconfig")
public class TtsConfig {
    /**
     * tts配置主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer ttsConfigId;

    /**
     * tts配置键
     */
    private String ttsConfigKey;
    /**
     * tts配置值
     */
    private String ttsConfigValue;
    /**
     * 描述
     */
    private String description;
}
