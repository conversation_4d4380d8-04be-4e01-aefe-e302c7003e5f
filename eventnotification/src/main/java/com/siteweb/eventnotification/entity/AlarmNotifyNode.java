package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyNode
 * @createTime 2022-04-22 14:46:09
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifynode")
public class AlarmNotifyNode {

    @TableId(value = "NodeId", type = IdType.AUTO)
    private Integer nodeId;

    private Integer alarmNotifyElementConfigId;

    private String nodeDirection;

    private String nodeType;

    private Integer nodeIndex;

    private String nodeTag;

    private String expression;
}
