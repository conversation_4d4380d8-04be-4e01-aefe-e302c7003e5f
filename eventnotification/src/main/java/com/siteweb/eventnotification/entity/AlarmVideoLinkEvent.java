package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/02/06
 */
@Data
@TableName("alarmvideolinkevent")
public class AlarmVideoLinkEvent {
    /**
     * 主键自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer alarmVideoLinkEventId;
    /**
     * 告警视频
     */
    private Integer alarmVideoLinkMapId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件条件id
     */
    private Integer eventConditionId;
}
