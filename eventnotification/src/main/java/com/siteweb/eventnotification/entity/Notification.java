package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 消息通知
 * @author: lzy
 * @creat: 2023/3/16 13:49
 */
@Data
@TableName("notification")
@NoArgsConstructor
@AllArgsConstructor

public class Notification implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer notificationId;

    /**
     * 是否已推送
     */
    private Boolean pushed;

    /**
     * 分类
     */
    private String category;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 颜色
     */
    private String color;

    /**
     * 图标
     */
    private String icon;

    /**
     * 用于导航到具体web页面的链接，Web
     */
    private String webLink;

    /**
     * 用于导航到具体app页面的链接，App
     */
    private String appLink;

    /**
     * 消息接收者对象
     */
    @TableField(exist = false)
    private List<NotificationReceiver> notificationReceivers;

    /**
     * 消息创建时间
     */
    private Date createTime;

    /**
     * 外部消息厂商请求标识
     */
    private String externalId;

    /**
     * 扩展参数
     */
    private String extParam;

}
