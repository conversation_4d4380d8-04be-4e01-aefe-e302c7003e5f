package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterCondition
 * @createTime 2022-04-22 14:42:04
 */
@Data
@NoArgsConstructor
@TableName("alarmnotifyfiltercondition")
public class AlarmNotifyFilterCondition {

    private Integer filterConditionId;

    private String filterConditionName;
}
