package com.siteweb.eventnotification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 消息通知接收者记录
 *
 * @Author: lzy
 * @Date: 2023/3/16 13:49
 */
@Data
@TableName("notificationreceiver")
@NoArgsConstructor
@AllArgsConstructor
public class NotificationReceiver {

    @TableId(type = IdType.AUTO)
    private Integer notificationReceiverId;
    /**
     * 消息Id
     */
    private Integer notificationId;
    /**
     * 消息推送接收人，系统登录用户的UserId
     */
    private Integer loginUserId;
    /**
     * 是否已读
     */
    private Boolean readed;
    /**
     * 已读时间
     */
    private Date readedTime;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 删除时间
     */
    private Date deletedTime;
}
