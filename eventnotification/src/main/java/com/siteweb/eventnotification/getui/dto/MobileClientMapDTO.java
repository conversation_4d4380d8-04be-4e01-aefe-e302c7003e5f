package com.siteweb.eventnotification.getui.dto;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import lombok.Data;

/**
 * @Author: lzy
 * @Date: 2023/3/17 17:04
 */
@Data
public class MobileClientMapDTO {
    /**
     * 型号
     */
    private String mobileInfo;
    /**
     * 客户端id
     */
    private String cid;

    public MobileClientMap build() {
        MobileClientMap result = new MobileClientMap();
        result.setCid(this.cid);
        result.setLoginUserId(TokenUserUtil.getLoginUserId());
        result.setMobileInfo(this.mobileInfo);
        return result;
    }
}
