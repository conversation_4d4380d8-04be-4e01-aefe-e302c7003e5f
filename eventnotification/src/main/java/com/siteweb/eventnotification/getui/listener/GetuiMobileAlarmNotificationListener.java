package com.siteweb.eventnotification.getui.listener;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.util.SnowflakeUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.eventnotification.entity.Notification;
import com.siteweb.eventnotification.getui.dto.GetuiNotificationAudience;
import com.siteweb.eventnotification.getui.dto.GetuiNotificationPushModel;
import com.siteweb.eventnotification.getui.dto.GetuiNotificationQueueModel;
import com.siteweb.eventnotification.getui.dto.MobileConditionalDTO;
import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import com.siteweb.eventnotification.getui.entity.MobileConditionalPushConfig;
import com.siteweb.eventnotification.getui.service.MobileClientMapService;
import com.siteweb.eventnotification.getui.service.MobileConditionalPushConfigService;
import com.siteweb.eventnotification.getui.utils.GetuiUtil;
import com.siteweb.eventnotification.service.NotificationService;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 手机告警推送监听器
 * @Author: lzy
 * @Date: 2023/3/16 14:21
 */
@Component
@Slf4j
public class GetuiMobileAlarmNotificationListener implements ApplicationListener<BaseSpringEvent<AlarmChange>> {

    private static final ConcurrentLinkedQueue<GetuiNotificationQueueModel> notificationQueue = new ConcurrentLinkedQueue<>();
    private static volatile boolean enable = false;
    /**
     * 告警颜色
     */
    private static Map<Integer, String> eventLevelColor;
    @Autowired
    GetuiUtil getuiUtil;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    MobileConditionalPushConfigService mobileConditionalPushConfigService;
    @Autowired
    MobileClientMapService mobileClientMapService;
    @Autowired
    ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    NotificationService notificationService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    HAStatusService haStatusService;
    @Autowired
    RegionService regionService;

    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP 手机个推逻辑结束");
            return;
        }
        if (!enable) {
            return;
        }
        AlarmChange alarmChange = event.getData();
        // 1. 查询启用条件推送的用户配置
        List<MobileConditionalPushConfig> mobileConditionalPushConfigList = mobileConditionalPushConfigService.findListByEnable(true);

        // 2. 获取符合当前告警条件的配置
        List<MobileConditionalPushConfig> prePushList = mobileConditionalPushConfigList.stream().filter(e -> this.conditionalFilter(e, alarmChange)).toList();

        // 3. 获取符合配置的用户id
        Set<Integer> prePushUserIds = prePushList.stream().map(MobileConditionalPushConfig::getLoginUserId).collect(Collectors.toSet());

        // 4. 根据用户id获取用户-客户端映射关系
        List<MobileClientMap> mobileClientMaps = mobileClientMapService.findByUserIds(prePushUserIds);

        // 5. 添加到通知队列
        mobileClientMaps.forEach(e -> notificationQueue.add(new GetuiNotificationQueueModel(e, alarmChange)));
    }

    @PostConstruct
    private void init() {
        SystemConfig systemConfigEnable = systemConfigService.findBySystemConfigKey("alarmNotification.mobile.getui.enable");
        if (systemConfigEnable == null || systemConfigEnable.getSystemConfigValue().trim().isEmpty() || "false".equals(systemConfigEnable.getSystemConfigValue())) {
            return;
        }
        enable = true;
        new Thread(() -> {
            try {
                eventLevelColor = dataItemService.findByEntryId(23).stream().collect(Collectors.toMap(e -> Integer.valueOf(e.getExtendField4()), DataItem::getExtendField3));
                log.info("告警个推推送启动成功......");
                while (true) {
                    // 没有告警睡眠2s
                    GetuiNotificationQueueModel queueModel = notificationQueue.poll();
                    if (ObjectUtil.isEmpty(queueModel)) {
                        ThreadUtil.sleep(2000);
                        continue;
                    }
                    threadPoolExecutor.execute(() -> {
                        MobileClientMap mobileClientMap = queueModel.getMobileClientMap();
                        AlarmChange alarmChange = queueModel.getAlarmChange();

                        String requestSign = SnowflakeUtil.nextStrId();
                        String pushTitle = "告警推送";
                        String pushContent = alarmChange.getStructureName() + alarmChange.getStationName() + alarmChange.getEquipmentName() + alarmChange.getEventName() + "发出告警";

                        // 消息入库
                        Notification notification = notificationService.createAlarmNotification(mobileClientMap, alarmChange, requestSign, pushTitle, pushContent);
                        if (notification == null) return;

                        // 推送消息
                        GetuiNotificationPushModel notificationPushModel = new GetuiNotificationPushModel();
                        notificationPushModel.setRequest_id(requestSign); // 请求标识
                        notificationPushModel.setAudience(new GetuiNotificationAudience(mobileClientMap.getCid())); // 目标用户
                        // 设置推送配置
                        getuiUtil.setGetuiSetting(notificationPushModel, pushTitle, pushContent);

                        try {
                            JSONObject result = getuiUtil.push(notificationPushModel);
                            if (result.getInt("code") == 0) {
                                notification.setPushed(true);
                                notificationService.updateById(notification);
                            } else {
                                getuiUtil.printGetuiPushError(result);
                                // 推送失败重新加入队列
                                // notificationQueue.add(queueModel);
                            }
                        } catch (Exception e) {
                            log.error("GetuiMobileAlarmNotification push exception: ", e);
                        }
                    });
                }
            }catch (Exception e) {
                log.error("GetuiMobileAlarmNotification push exception: ", e);
            }
        }).start();
    }

    /**
     * 不选择任何条件，返回true
     * 选择了条件，则根据条件判断是否符合
     */
    public boolean conditionalFilter(MobileConditionalPushConfig mobileConditionalPushConfig, AlarmChange alarmChange) {
        MobileConditionalDTO conditional = JSONUtil.toBean(mobileConditionalPushConfig.getConditional(), MobileConditionalDTO.class);

        // 区域权限
        if (!regionService.hasRegionMapPermissions(mobileConditionalPushConfig.getLoginUserId(),alarmChange.getResourceStructureId(),alarmChange.getEquipmentId())) {
            return false;
        }
        int loop = 0;
        int conditionNum = 0;
        // 告警等级
        if (CharSequenceUtil.isNotEmpty(conditional.getEventLevel())) {
            conditionNum++;
            Set<Integer> eventLevelSet = StringUtils.splitToIntegerCollection(conditional.getEventLevel(), HashSet::new);
            if (eventLevelSet.contains(alarmChange.getEventLevel())) {
                loop++;
            }
        }
        // 设备基类
        if (CharSequenceUtil.isNotEmpty(conditional.getEquipmentBaseType())) {
            conditionNum++;
            Set<Integer> equipmentCategorySet = StringUtils.splitToIntegerCollection(conditional.getEquipmentBaseType(), HashSet::new);
            if (equipmentCategorySet.contains(alarmChange.getBaseEquipmentId())) {
                loop++;
            }
        }
        // 告警类型
        if (CharSequenceUtil.isNotEmpty(conditional.getEventCategory())) {
            conditionNum++;
            Set<Integer> eventCategorySet = StringUtils.splitToIntegerCollection(conditional.getEventCategory(), HashSet::new);
            if (eventCategorySet.contains(alarmChange.getEventCategoryId())) {
                loop++;
            }
        }
        // 告警操作类型（只筛选非开始类型的alarmchange）
        if (CharSequenceUtil.isNotEmpty(conditional.getEventOperationType()) && !Objects.equals(alarmChange.getOperationType(), AlarmOperationTypeEnum.START.getValue())) {
            conditionNum++;
            Set<Integer> eventOperationTypeSet = StringUtils.splitToIntegerCollection(conditional.getEventOperationType(), HashSet::new);
            if (eventOperationTypeSet.contains(alarmChange.getOperationType())) {
                loop++;
            }
        }
        // 告警结束引起的确认，不发送告警通知（避免发送两次推送）
        if (Objects.equals(alarmChange.getOperationType(), AlarmOperationTypeEnum.CONFIRM.getValue())) {
            // 告警确认有结束时间则表示强制结束
            if (Objects.nonNull(alarmChange.getEndTime())) {
                return false;
            }
        }
        return loop >= conditionNum;
    }
}
