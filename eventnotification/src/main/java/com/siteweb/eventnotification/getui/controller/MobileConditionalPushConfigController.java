package com.siteweb.eventnotification.getui.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.getui.dto.MobileConditionalPushConfigDTO;
import com.siteweb.eventnotification.getui.dto.MobileConditionalDTO;
import com.siteweb.eventnotification.getui.entity.MobileConditionalPushConfig;
import com.siteweb.eventnotification.getui.service.MobileConditionalPushConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:44
 */
@Api("手机条件推送配置")
@RestController
@RequestMapping("/api")
public class MobileConditionalPushConfigController {

    @Autowired
    MobileConditionalPushConfigService mobileConditionalPushConfigService;

    @ApiOperation(value = "新增或更新配置")
    @RequestMapping(value = "/mobileconditionalpushconfig", method = {RequestMethod.POST,RequestMethod.PUT})
    public ResponseEntity<ResponseResult> createOrUpdate(@RequestBody MobileConditionalPushConfigDTO mobileConditionalPushConfigDTO) {
        return ResponseHelper.successful(mobileConditionalPushConfigService.createOrUpdate(mobileConditionalPushConfigDTO.build()));
    }

    @ApiOperation(value = "获取用户推送配置")
    @GetMapping(value = "/mobileconditionalpushconfig")
    public ResponseEntity<ResponseResult> getByUserId() {
        MobileConditionalPushConfig mobileConditionalPushConfig = mobileConditionalPushConfigService.findByUserId();
        MobileConditionalPushConfigDTO result = new MobileConditionalPushConfigDTO();
        if (ObjectUtil.isEmpty(mobileConditionalPushConfig)) {
            return ResponseHelper.successful(result);
        }

        result.setEnable(mobileConditionalPushConfig.getEnable());
        result.setConditional(JSONUtil.toBean(mobileConditionalPushConfig.getConditional(), MobileConditionalDTO.class));
        return ResponseHelper.successful(result);
    }

}
