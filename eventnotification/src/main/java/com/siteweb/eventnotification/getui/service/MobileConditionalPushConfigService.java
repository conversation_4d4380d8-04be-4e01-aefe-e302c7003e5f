package com.siteweb.eventnotification.getui.service;

import com.siteweb.eventnotification.getui.entity.MobileConditionalPushConfig;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:22
 */
public interface MobileConditionalPushConfigService {

    /**
     * 查询启用推送配置
     * @param enable 是否启用配置
     */
    List<MobileConditionalPushConfig> findListByEnable(boolean enable);

    /**
     * 新增或更新（id存在则更新）
     * @param conditionalPushConfig 配置
     */
    MobileConditionalPushConfig createOrUpdate(MobileConditionalPushConfig conditionalPushConfig);

    /**
     * 根据用户id获取条件推送配置
     */
    MobileConditionalPushConfig findByUserId();
}
