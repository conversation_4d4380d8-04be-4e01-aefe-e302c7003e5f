package com.siteweb.eventnotification.getui.dto;

import lombok.Data;

@Data
public class GetuiNotification {
    /**
     * 通知消息标题，长度 ≤ 50字
     */
    private String title;
    /**
     * 通知消息内容，长度 ≤ 256字
     */
    private String body;
    /**
     * 长文本消息内容，通知消息+长文本样式，与big_image二选一，两个都填写时报错，长度 ≤ 512字
     */
    private String big_text;
    /**
     * 大图的URL地址，通知消息+大图样式， 与big_text二选一，两个都填写时报错，URL长度 ≤ 1024字
     */
    private String big_image;
    /**
     * 点击通知时，附加自定义透传消息，长度 ≤ 3072字
     */
    private String payload;
    /**
     * 点击通知后续动作，
     * 目前支持以下后续动作，
     * intent：打开应用内特定页面，
     * url：打开网页地址，
     * payload：自定义消息内容启动应用，
     * payload_custom：自定义消息内容不启动应用，
     * startapp：打开应用首页，
     * none：纯通知，无后续动作
     */
    private String click_type = "none";
    /**
     * 点击通知打开应用特定页面，intent格式必须正确且不能为空，长度 ≤ 4096字;
     */
    private String intent;
    /**
     * 点击通知栏消息时，唤起系统默认浏览器打开此链接。必须填写可访问的链接
     */
    private String url;
    /**
     * 角标, 必须大于0, 个推通道下发有效
     * 此属性目前针对华为和荣耀
     */
    private Integer badge_add_num = 1;
}