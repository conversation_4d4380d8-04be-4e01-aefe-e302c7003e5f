package com.siteweb.eventnotification.getui.dto;
import cn.hutool.json.JSONObject;
import lombok.Data;

/**
 * @Author: lzy
 * @Date: 2023/3/18 10:23
 */
@Data
public class GetuiNotificationPushMessage {

    /**
     * 通知消息内容，仅支持安卓系统，iOS系统不展示个推通知消息，与transmission、revoke三选一，都填写时报错
     */
    private GetuiNotification notification;

    /**
     * 纯透传消息内容，安卓和iOS均支持，与notification、revoke 三选一，都填写时报错，长度 ≤ 3072字
     */
    private String transmission;

    // private JSON revoke;

    /**
     * options 第三方厂商扩展参数
     */
    private JSONObject options;

    public GetuiNotificationPushMessage() {
    }
    public GetuiNotificationPushMessage(String title, String body) {
        GetuiNotification getuiNotification = new GetuiNotification();
        getuiNotification.setTitle(title);
        getuiNotification.setBody(body);
        this.notification = getuiNotification;
        this.options = new JSONObject();
        // 华为
        {
            JSONObject entries = new JSONObject();
            entries.set("/message/android/notification/badge/class", "io.dcloud.PandoraEntry"); // 应用入口Activity路径名称
//            entries.set("/message/android/notification/badge/set_num", 1); // 角标设置数字
            entries.set("/message/android/notification/badge/add_num", 1); // 	角标设置数字
            this.options.set("HW", entries);
        }
    }
}
