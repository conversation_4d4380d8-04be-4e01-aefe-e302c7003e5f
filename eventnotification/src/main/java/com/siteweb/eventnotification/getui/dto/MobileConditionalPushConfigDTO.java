package com.siteweb.eventnotification.getui.dto;

import cn.hutool.json.JSONUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.eventnotification.getui.entity.MobileConditionalPushConfig;
import lombok.Data;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:46
 */
@Data
public class MobileConditionalPushConfigDTO {

    /**
     * 是否启用
     */
    private Integer enable;

    /**
     * 条件
     */
    private MobileConditionalDTO conditional;

    public MobileConditionalPushConfig build() {
        MobileConditionalPushConfig result = new MobileConditionalPushConfig();
        result.setConditional(JSONUtil.toJsonStr(this.conditional));
        result.setEnable(this.enable);
        result.setLoginUserId(TokenUserUtil.getLoginUserId());
        return result;
    }
}
