package com.siteweb.eventnotification.getui.dto;

import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import com.siteweb.monitoring.entity.AlarmChange;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: lzy
 * @Date: 2023/3/18 15:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetuiNotificationQueueModel {

    /**
     * 用户客户端映射关系
     */
    private MobileClientMap mobileClientMap;

    /**
     * 告警
     */
    private AlarmChange alarmChange;
}
