package com.siteweb.eventnotification.getui.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.getui.dto.MobileClientMapDTO;
import com.siteweb.eventnotification.getui.service.MobileClientMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: lzy
 * @Date: 2023/3/17 17:03
 */
@Api("手机客户端映射")
@RestController
@RequestMapping("/api")
public class MobileClientMapController {

    @Autowired
    MobileClientMapService mobileClientMapService;

    @ApiOperation(value = "新增或更新客户端映射关系")
    @RequestMapping(value = "/mobileclientmap", method = {RequestMethod.POST,RequestMethod.PUT})
    public ResponseEntity<ResponseResult> createOrUpdate(@RequestBody MobileClientMapDTO mobileClientMapDTO) {
        if (CharSequenceUtil.isEmpty(mobileClientMapDTO.getCid())) {
            return ResponseHelper.failed("cid cannot be empty");
        }
        return ResponseHelper.successful(mobileClientMapService.createOrUpdate(mobileClientMapDTO.build()));
    }

}
