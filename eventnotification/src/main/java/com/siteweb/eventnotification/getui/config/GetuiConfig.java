package com.siteweb.eventnotification.getui.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: lzy
 * @Date: 2023/3/18 10:07
 */
@Data
@Component
@ConfigurationProperties("getui")
public class GetuiConfig {
    /**
     * 应用id
     */
    private String appId;
    /**
     * appKey
     */
    private String appKey;
    /**
     * 应用密钥
     */
    private String appSecret;
    /**
     * 服务公钥
     */
    private String masterSecret;
    /**
     * api服务路径
     */
    private String baseUrl;

    public String getBaseUrl() {
        return this.baseUrl + this.appId;
    }
}
