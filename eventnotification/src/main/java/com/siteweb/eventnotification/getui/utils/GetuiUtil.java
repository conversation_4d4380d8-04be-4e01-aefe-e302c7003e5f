package com.siteweb.eventnotification.getui.utils;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.siteweb.common.mobilenotication.MobileNoticationService;
import com.siteweb.common.mobilenotication.MobileNoticationServiceProxy;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SpringBeanUtil;
import com.siteweb.eventnotification.getui.dto.*;
import com.siteweb.eventnotification.getui.service.impl.GetuiNoticationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: lzy
 * @Date: 2023/3/18 10:07
 */
@Slf4j
@Component
public class GetuiUtil {
    private static final String NOTIFICATION_TYPE = "alarmChange";
    static MobileNoticationService<GetuiNotificationPushModel> getuiService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    public String auth() {
        return buildService().auth();
    }
    public void setGetuiSetting(GetuiNotificationPushModel notificationPushModel, String pushTitle, String pushContent) {
        // 设置推送配置
        setGetuiStrategy(notificationPushModel);
        // 推送个推通道配置
        setGetuiChannalSetting(pushTitle, pushContent, notificationPushModel);
        // 推送厂商通道配置
        setFactoryChannelSetting(pushTitle, pushContent, notificationPushModel);
    }

    /**
     * 设置厂商通道配置
     * @param pushTitle 推送消息标题
     * @param pushContent 推送消息内容
     * @param notificationPushModel 推送消息模型
     */
    private void setFactoryChannelSetting(String pushTitle, String pushContent, GetuiNotificationPushModel notificationPushModel) {
        GetuiNotificationPushChannel pushChannel = new GetuiNotificationPushChannel();
        JSONObject body = new JSONObject();
        body.set("category", NOTIFICATION_TYPE);
        // 安卓厂商
        {
            // 2023-03-23
            GetuiNotificationPushChannelAndroid android = new GetuiNotificationPushChannelAndroid();
            GetuiNotificationPushMessage androidPushMessage = new GetuiNotificationPushMessage(pushTitle, pushContent);
            androidPushMessage.getNotification().setClick_type("intent"); // 跳转特定页面（startapp无法携带参数，所以使用该方式）
            androidPushMessage.getNotification().setIntent(buildIntent(body));
            android.setUps(androidPushMessage);
            pushChannel.setAndroid(android);
        }
        // ios厂商
        {
            GetuiNotificationPushChannelIos ios = new GetuiNotificationPushChannelIos();
            ios.setType("notify"); // 普通通知
            ios.setPayload(body.toString());
            GetuiNotificationPushChannelIosAps aps = new GetuiNotificationPushChannelIosAps();
            GetuiNotificationPushChannelIosApsAlert alert = new GetuiNotificationPushChannelIosApsAlert();
            alert.setTitle(pushTitle);
            alert.setBody(pushContent);
            aps.setAlert(alert);
            ios.setAps(aps);
            pushChannel.setIos(ios);
        }
        notificationPushModel.setPush_channel(pushChannel);
    }

    /**
     * 设个推通道设置
     * @param pushTitle 推送消息标题
     * @param pushContent 推送消息内容
     * @param notificationPushModel 推送配置model
     */
    private void setGetuiChannalSetting(String pushTitle, String pushContent, GetuiNotificationPushModel notificationPushModel) {
        GetuiNotificationPushMessage pushMessage = new GetuiNotificationPushMessage(pushTitle, pushContent);
        pushMessage.getNotification().setClick_type("payload"); // 透传
        JSONObject payload = new JSONObject();
        payload.set("category", NOTIFICATION_TYPE);
        pushMessage.getNotification().setPayload(payload.toString());
        notificationPushModel.setPush_message(pushMessage);
    }

    /**
     * 设置推送配置
     */
    private void setGetuiStrategy(GetuiNotificationPushModel notificationPushModel) {
        // 推送设置
        GetuiNotificationSetting setting = new GetuiNotificationSetting();
        JSONObject strategy = new JSONObject();
        strategy.set("default", 1); // 推送策略，优先个推，否则厂商
        setting.setStrategy(strategy);
        notificationPushModel.setSettings(setting); // 推送配置
    }
    private String buildIntent(JSONObject body) {
        return "intent:#Intent;launchFlags=0x04000000;package=com.siteweb.insight;component=io.dcloud.PandoraEntry;S.UP-OL-SU=true;S.payload=" + body.toString() + ";end";
    }
    public JSONObject push(GetuiNotificationPushModel getuiNotificationPushDTO) throws Exception {
        return buildService().push(getuiNotificationPushDTO);
    }
    /**
     * 输出个推错误状态码含义
     */
    public void printGetuiPushError(JSONObject result) {
        Integer code = result.getInt("code");
        String codeMessage = messageSourceUtil.getMessage("eventNotification.getui.pushResponseCode." + code);
        if (CharSequenceUtil.isNotEmpty(codeMessage)) {
            log.error("getui notification push exception: {}", codeMessage);
        }else {
            log.error("getui notification push exception: {}", result);
        }
    }

    private MobileNoticationService<GetuiNotificationPushModel> buildService() {
        if (ObjectUtil.isEmpty(getuiService)) {
            MobileNoticationService<GetuiNotificationPushModel> bean = SpringBeanUtil.getBean(GetuiNoticationServiceImpl.class);
            getuiService = MobileNoticationServiceProxy.buildProxyObj(bean);
        }
        return getuiService;
    }
}
