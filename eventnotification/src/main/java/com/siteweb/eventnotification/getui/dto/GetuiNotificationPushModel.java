package com.siteweb.eventnotification.getui.dto;

import lombok.Data;

import java.util.Collections;

/**
 * 具体配置参考: <a href="https://docs.getui.com/getui/server/rest_v2/push/">...</a>
 * @Author: lzy
 * @Date: 2023/3/18 10:19
 */
@Data
public class GetuiNotificationPushModel {
    /**
     * 请求唯一标识号，10-32位之间；如果request_id重复，会导致消息丢失
     */
    private String request_id;
    /**
     * 推送目标用户
     */
    private GetuiNotificationAudience audience;
    /**
     * 推送配置
     */
    private GetuiNotificationSetting settings;
    /**
     * 个推推送消息参数(在线个推通道消息内容)
     */
    private GetuiNotificationPushMessage push_message;
    /**
     * push_channel 离线厂商通道消息内容
     */
    private GetuiNotificationPushChannel push_channel;

    public GetuiNotificationPushModel() {
    }

    public GetuiNotificationPushModel(String requestId, String cid, String title, String body) {
        this.request_id = requestId;
        this.audience = new GetuiNotificationAudience(Collections.singletonList(cid));
        this.push_message = new GetuiNotificationPushMessage(title, body);
    }
}
