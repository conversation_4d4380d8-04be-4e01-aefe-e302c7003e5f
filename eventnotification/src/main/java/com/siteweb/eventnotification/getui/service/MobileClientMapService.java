package com.siteweb.eventnotification.getui.service;

import com.siteweb.eventnotification.getui.entity.MobileClientMap;

import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:38
 */
public interface MobileClientMapService {

    /**
     * 根据用户id获取用户-客户端映射
     * @param userIds 用户ids
     */
    List<MobileClientMap> findByUserIds(Collection<Integer> userIds);

    /**
     * 更新用户映射客户端id
     * @param mobileClientMap 映射关系
     */
    MobileClientMap createOrUpdate(MobileClientMap mobileClientMap);

    /**
     * 根据cid获取映射管理
     * @param cid 客户端id
     */
    MobileClientMap findByCid(String cid);
}
