package com.siteweb.eventnotification.getui.dto;

import lombok.Data;

/**
 * 苹果厂商通道配置
 * 参考：<a href="https://docs.getui.com/getui/server/rest_v2/common_args/?id=doc-title-7">...</a>
 * @Author: lzy
 * @Date: 2023/3/22 9:38
 */
@Data
public class GetuiNotificationPushChannelIos {
    /**
     * voip：voip语音推送，notify：apns通知消息，liveactivity：灵动岛推送(不支持p12证书)
     */
    private String type;
    /**
     * 增加自定义的数据
     */
    private String payload;
    /**
     * 推送通知消息内容
     */
    private GetuiNotificationPushChannelIosAps aps;
    /**
     * 用于计算icon上显示的数字，还可以实现显示数字的自动增减，如“+1”、 “-1”、 “1” 等，计算结果将覆盖badge
     */
    private String auto_badge = "+1";
}
