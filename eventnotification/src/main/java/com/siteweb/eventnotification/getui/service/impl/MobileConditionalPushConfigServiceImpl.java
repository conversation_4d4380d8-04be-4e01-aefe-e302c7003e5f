package com.siteweb.eventnotification.getui.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.eventnotification.getui.entity.MobileConditionalPushConfig;
import com.siteweb.eventnotification.getui.mapper.MobileConditionalPushConfigMapper;
import com.siteweb.eventnotification.getui.service.MobileConditionalPushConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:22
 */
@Service
public class MobileConditionalPushConfigServiceImpl implements MobileConditionalPushConfigService {

    @Autowired
    MobileConditionalPushConfigMapper mobileConditionalPushConfigMapper;

    @Override
    public MobileConditionalPushConfig createOrUpdate(MobileConditionalPushConfig entity) {
        MobileConditionalPushConfig mobileConditionalPushConfig = findByUserId();
        if (ObjectUtil.isEmpty(mobileConditionalPushConfig)) {
            mobileConditionalPushConfigMapper.insert(entity);
        }else {
            mobileConditionalPushConfigMapper.updateById(entity);
        }
        return entity;
    }

    @Override
    public MobileConditionalPushConfig findByUserId() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        LambdaQueryWrapper<MobileConditionalPushConfig> wrapper = Wrappers.lambdaQuery(MobileConditionalPushConfig.class);
        wrapper.eq(MobileConditionalPushConfig::getLoginUserId, loginUserId);
        return mobileConditionalPushConfigMapper.selectOne(wrapper);
    }

    @Override
    public List<MobileConditionalPushConfig> findListByEnable(boolean enable) {
        LambdaQueryWrapper<MobileConditionalPushConfig> wrapper = Wrappers.lambdaQuery(MobileConditionalPushConfig.class);
        wrapper.eq(MobileConditionalPushConfig::getEnable, enable ? 1 : 0);
        return mobileConditionalPushConfigMapper.selectList(wrapper);
    }
}
