package com.siteweb.eventnotification.getui.dto;

import lombok.Data;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:04
 */
@Data
public class MobileConditionalDTO {

    /**
     * 告警等级（多个半角逗号分隔）
     */
    private String eventLevel = "";

    /**
     * 设备基类
     */
    private String equipmentBaseType = "";

    /**
     * 告警类型（多个半角逗号分隔，对应字典entry 24的数据）
     */
    private String eventCategory = "";
    /**
     * 操作类型
     * {@link com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum}
     */
    private String eventOperationType = "";

}
