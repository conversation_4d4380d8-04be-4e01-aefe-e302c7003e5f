package com.siteweb.eventnotification.getui.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class GetuiNotificationAudience {
    /**
     * cid
     */
    private List<String> cid;

    public GetuiNotificationAudience(String cid) {
        this(Collections.singletonList(cid));
    }

    public GetuiNotificationAudience(List<String> cid) {
        this.cid = cid;
    }
}