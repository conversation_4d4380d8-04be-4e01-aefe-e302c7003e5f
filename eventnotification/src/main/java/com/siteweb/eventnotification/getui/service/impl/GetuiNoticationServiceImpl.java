package com.siteweb.eventnotification.getui.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.mobilenotication.MobileNoticationService;
import com.siteweb.common.mobilenotication.exception.MobileNotificationTokenException;
import com.siteweb.common.redis.RedisUtil;
import com.siteweb.eventnotification.getui.config.GetuiConfig;
import com.siteweb.eventnotification.getui.dto.GetuiNotificationPushModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lzy
 * @Date: 2023/3/18 11:05
 */
@Service
@Slf4j
public class GetuiNoticationServiceImpl implements MobileNoticationService<GetuiNotificationPushModel> {

    @Autowired
    RedisUtil redisUtil;
    @Autowired
    GetuiConfig getuiConfig;
    private static final Object lock = new Object();

    @Override
    public String redisAuthKey() {
        return "GetuiAuth";
    }

    @Override
    public String auth() {
        // 1. 是否已缓存
        Object tokenObj = redisUtil.get(this.redisAuthKey());
        if (ObjectUtil.isNotNull(tokenObj)) {
            return String.valueOf(tokenObj);
        }
        String token = "";
        synchronized (lock) {
            tokenObj = redisUtil.get(this.redisAuthKey());
            if (ObjectUtil.isNotNull(tokenObj)) {
                return String.valueOf(tokenObj);
            }

            // 1. 构建http
            String authUrl = "/auth";
            long timestamp = System.currentTimeMillis();
            HttpRequest post = HttpUtil.createPost(getuiConfig.getBaseUrl() + authUrl);
            post.header(Header.CONTENT_TYPE, "application/json;charset=utf-8");
            JSONObject params = new JSONObject();
            params.set("appkey", getuiConfig.getAppKey());
            params.set("timestamp", timestamp);
            params.set("sign", DigestUtil.sha256Hex(getuiConfig.getAppKey() + timestamp + getuiConfig.getMasterSecret()));
            post.body(JSONUtil.toJsonPrettyStr(params));

            // 2. 执行
            log.info("GetuiNoticationServiceImpl auth request: {}, params: {}", authUrl, params);
            HttpResponse execute = post.execute();
            String body = execute.body();
            if (execute.isOk()) {
                log.info("GetuiNoticationServiceImpl auth response: {}", body);
            }else {
                log.error("GetuiNoticationServiceImpl auth exception: {}", body);
                throw new BusinessException("Getui Auth Exception");
            }

            // 3. 设置token
            JSONObject jsonBody = JSONUtil.parseObj(body);
            if (jsonBody.getInt("code") == 0) {
                token = jsonBody.getJSONObject("data").getStr("token");
                redisUtil.set(this.redisAuthKey(), token);
            }
        }
        return token;
    }

    @Override
    public boolean validAuthFail(JSONObject result) {
        Integer code = result.getInt("code");
        if (code == 10001) {
            log.error("GetuiNoticationServiceImpl token invalid");
            redisUtil.del(this.redisAuthKey());
            throw new MobileNotificationTokenException();
        }
        return false;
    }

    @Override
    public JSONObject push(GetuiNotificationPushModel getuiNotificationPushDTO) {
        // 1. 构建http
        String pusthUrl = "/push/single/cid";
        HttpRequest post = HttpUtil.createPost(getuiConfig.getBaseUrl() + pusthUrl);
        post.header("token", String.valueOf(this.auth()));
        post.header(Header.CONTENT_TYPE, "application/json;charset=utf-8");
        String requestBody = JSONUtil.toJsonStr(getuiNotificationPushDTO);
        post.body(requestBody);

        // 2. 执行
        log.info("GetuiNoticationServiceImpl push request: {}, params: {}", pusthUrl, requestBody);
        String body = "";
        try (HttpResponse execute = post.execute()) {
             body = execute.body();
        } catch (Exception e) {
            log.error("getui 推送异常,{}", ExceptionUtil.stacktraceToString(e));
            throw e;
        }
        log.info("GetuiNoticationServiceImpl push result: {}", body);

        return JSONUtil.parseObj(body);
    }
}
