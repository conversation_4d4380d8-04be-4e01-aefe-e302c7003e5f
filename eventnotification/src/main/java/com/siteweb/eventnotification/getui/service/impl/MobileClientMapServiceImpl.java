package com.siteweb.eventnotification.getui.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import com.siteweb.eventnotification.getui.mapper.MobileClientMapMapper;
import com.siteweb.eventnotification.getui.service.MobileClientMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/16 15:38
 */
@Service
public class MobileClientMapServiceImpl implements MobileClientMapService {

    @Autowired
    MobileClientMapMapper mobileClientMapMapper;

    @Override
    public List<MobileClientMap> findByUserIds(Collection<Integer> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MobileClientMap> wrapper = Wrappers.lambdaQuery(MobileClientMap.class);
        wrapper.in(MobileClientMap::getLoginUserId, userIds);
        return mobileClientMapMapper.selectList(wrapper);
    }

    @Override
    public MobileClientMap createOrUpdate(MobileClientMap mobileClientMap) {
        mobileClientMapMapper.deleteById(mobileClientMap.getLoginUserId());
        mobileClientMapMapper.delete(Wrappers.lambdaQuery(MobileClientMap.class).eq(MobileClientMap::getCid, mobileClientMap.getCid()));
        mobileClientMapMapper.insert(mobileClientMap);
        return mobileClientMap;
    }

    @Override
    public MobileClientMap findByCid(String cid) {
        LambdaQueryWrapper<MobileClientMap> wrapper = Wrappers.lambdaQuery(MobileClientMap.class);
        wrapper.eq(MobileClientMap::getCid, cid);
        return mobileClientMapMapper.selectOne(wrapper);
    }
}
