package com.siteweb.eventnotification.sender;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmBoxMsgResponseDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.dto.PhoneSmsMsgRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * 通过工号发送短信通知
 * 仅需要将内容与工号发送给通知网关即可
 * <AUTHOR>
 * @date 2024/07/23
 */
@Slf4j
@Service
public class SmsAlarmNotifyByJobNumberSender extends AlarmNotifyBase implements AlarmNotifySendService {
    @Autowired
    RestTemplate restTemplate;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        if (emptyNotifyPerson(elementConfigDTO)) {
            log.error("短信(工号)接收人未配置 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("短信(工号)通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.MESSAGE_BATCH_API_URL, elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("短信(工号)网关服务发送批量短信URL未配置");
            return -3;
        }
        //获取通知人id
        List<Integer> notifyPersonIds = super.getNotifyPersonIds(CharSequenceUtil.subBefore(elementConfigDTO.getExpression(), ";", false), elementConfigDTO.getExtendField1());
        //获取接收人的工号
        String receiverJobNumber = getReceiverJobNumber(notifyPersonIds);
        if (CharSequenceUtil.isBlank(receiverJobNumber)) {
            log.error("短信(工号)接收人工号为空 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -4;
        }
        //默认是同时拨打电话和发送短信
        Optional<String> msgSendTypeOptional = Optional.ofNullable(CharSequenceUtil.subAfter(elementConfigDTO.getExpression(), ";", false));
        PhoneSmsMsgRequestDTO requestDTO = new PhoneSmsMsgRequestDTO();
        requestDTO.setContent(alarmNotifyExecutorDTO.getAlarmNotifyContent());
        requestDTO.setDelimiter(":,");
        requestDTO.setSubtype(msgSendTypeOptional.orElse("0"));
        requestDTO.setTo(receiverJobNumber);
        //直接发送content内容，网关不作处理
        requestDTO.setIsassemble("2");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PhoneSmsMsgRequestDTO> httpEntity = new HttpEntity<>(requestDTO, httpHeaders);
        AlarmBoxMsgResponseDTO alarmBoxMsgResponseDTO = null;
        String sendResult = "";
        try {
            log.info("发送电话语音(短信工号)通知：{}", requestDTO);
            alarmBoxMsgResponseDTO = restTemplate.postForObject(notifyGatewayUrl, httpEntity, AlarmBoxMsgResponseDTO.class);
            sendResult = messageSourceUtil.getMessage("send.result.success");
        } catch (RestClientException e) {
            sendResult = messageSourceUtil.getMessage("send.result.fail");
            log.error("电话语音(短信工号)通知数据发送失败：{} {}", requestDTO.getContent(), e.getMessage());
        }
        String sendType = messageSourceUtil.getMessage("send.type.telephonevoice");
        String receiverJoin = getReceiverNames(notifyPersonIds);
        alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, receiverJoin, sendResult);
        if (alarmBoxMsgResponseDTO != null && alarmBoxMsgResponseDTO.getError_code() == 0) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
