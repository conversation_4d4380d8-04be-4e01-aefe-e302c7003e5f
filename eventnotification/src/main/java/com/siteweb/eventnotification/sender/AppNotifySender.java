package com.siteweb.eventnotification.sender;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.SnowflakeUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.entity.Notification;
import com.siteweb.eventnotification.getui.dto.GetuiNotificationAudience;
import com.siteweb.eventnotification.getui.dto.GetuiNotificationPushModel;
import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import com.siteweb.eventnotification.getui.entity.MobileConditionalPushConfig;
import com.siteweb.eventnotification.getui.service.MobileClientMapService;
import com.siteweb.eventnotification.getui.service.MobileConditionalPushConfigService;
import com.siteweb.eventnotification.getui.utils.GetuiUtil;
import com.siteweb.eventnotification.service.NotificationService;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.service.AlarmChangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {
    @Autowired
    MobileClientMapService mobileClientMapService;
    @Autowired
    AlarmChangeService alarmChangeService;
    @Autowired
    NotificationService notificationService;
    @Autowired
    GetuiUtil getuiUtil;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    MobileConditionalPushConfigService mobileConditionalPushConfigService;
    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        if (emptyNotifyPerson(elementConfigDTO)) {
            log.error("APP通知接收人未配置 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("APP通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        //获取有效的通知人id
        List<MobileClientMap> validMobileClients = getValidMobileClients(elementConfigDTO);
        if (CollUtil.isEmpty(validMobileClients)) {
            log.error("APP通知接收人为空或接收人未登录APP AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -4;
        }
        String pushContent = alarmNotifyExecutorDTO.getAlarmNotifyContent();
        String pushTitle = messageSourceUtil.getMessage("common.alarm.push");
        AlarmChange alarmChange = alarmChangeService.findById(alarmNotifyExecutorDTO.getSerialNo());
        String sendResult = "";
        String sendType = messageSourceUtil.getMessage("send.type.App");
        int result = 1;
        // 推送消息
        for (MobileClientMap mobileClientMap : validMobileClients) {
            String requestSign = SnowflakeUtil.nextStrId();
            Notification notification = notificationService.createAlarmNotification(mobileClientMap, alarmChange, requestSign, pushTitle, pushContent);
            if (Objects.isNull(notification)) {
                continue;
            }
            GetuiNotificationPushModel notificationPushModel = new GetuiNotificationPushModel();
            notificationPushModel.setRequest_id(requestSign); // 请求标识
            notificationPushModel.setAudience(new GetuiNotificationAudience(mobileClientMap.getCid())); // 目标用户
            // 设置推送配置
            getuiUtil.setGetuiSetting(notificationPushModel, pushTitle, pushContent);
            try {
                JSONObject pushResult = getuiUtil.push(notificationPushModel);
                if (pushResult.getInt("code") == 0) {
                    notification.setPushed(true);
                    notificationService.updateById(notification);
                    sendResult = messageSourceUtil.getMessage("send.result.success");
                } else {
                    getuiUtil.printGetuiPushError(pushResult);
                    sendResult = messageSourceUtil.getMessage("send.result.fail");
                    result = 0;
                }
            } catch (Exception e) {
                log.error("GetuiMobileAlarmNotification push exception: ", e);
                sendResult = messageSourceUtil.getMessage("send.result.fail");
                result = 0;
            }
        }
        String receiverJoin = getReceiverNames(validMobileClients.stream().map(MobileClientMap::getLoginUserId).toList());
        super.alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, receiverJoin, sendResult);
        return result;
    }

    private List<MobileClientMap> getValidMobileClients(AlarmNotifyElementConfigDTO elementConfigDTO) {
        //获取启用的用户ID集合
        Set<Integer> enabledUserIds = mobileConditionalPushConfigService.findListByEnable(true)
                                                                        .stream()
                                                                        .map(MobileConditionalPushConfig::getLoginUserId)
                                                                        .collect(Collectors.toSet());

        if (enabledUserIds.isEmpty()) {
            log.warn("没有启用推送的用户");
            return Collections.emptyList();
        }

        // 获取需要通知的用户ID
        List<Integer> notifyPersonIds = super.getNotifyPersonIds(CharSequenceUtil.subBefore(elementConfigDTO.getExpression(), ";", false), elementConfigDTO.getExtendField1());

        if (CollUtil.isEmpty(notifyPersonIds)) {
            log.warn("没有需要通知的用户");
            return Collections.emptyList();
        }

        // 预先过滤掉未启用的用户ID
        List<Integer> validUserIds = notifyPersonIds.stream()
                                                    .filter(enabledUserIds::contains)
                                                    .toList();

        if (CollUtil.isEmpty(validUserIds)) {
            log.warn("没有有效的通知用户");
            return Collections.emptyList();
        }
        return mobileClientMapService.findByUserIds(validUserIds);
    }
}
