package com.siteweb.eventnotification.sender;

import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;

import java.util.HashMap;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifySendService
 * @createTime 2022-04-24 16:13:19
 */
public interface AlarmNotifySendService {

    int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);
}
