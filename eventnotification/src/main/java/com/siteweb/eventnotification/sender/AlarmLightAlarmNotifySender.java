package com.siteweb.eventnotification.sender;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.*;
import com.siteweb.utility.constans.SystemConfigEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;

@Service
public class AlarmLightAlarmNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(AlarmLightAlarmNotifySender.class);

    @Autowired
    @Qualifier("proxyRestTemplateSSL")
    RestTemplate proxyRestTemplateSSL;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("告警灯通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.ALARM_LIGHT_MESSAGE_API_URL,elementConfigDTO.getAlarmNotifyConfigId(),elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("通知网关服务发送告警灯通知URL未配置");
            return -2;
        }
        AlarmLightMsgRequestDTO alarmLightMsgRequestDTO = new AlarmLightMsgRequestDTO();
        alarmLightMsgRequestDTO.setContent(String.format("SequenceId:%s,SerialNo:%d,OperationType:%d,EventSeverityId:%d",
                alarmNotifyExecutorDTO.getSequenceId(), alarmNotifyExecutorDTO.getSerialNo(),
                alarmNotifyExecutorDTO.getOperationType(), alarmNotifyExecutorDTO.getEventSeverityId()));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<AlarmLightMsgRequestDTO> httpEntity = new HttpEntity<>(alarmLightMsgRequestDTO, httpHeaders);
        String sendType = messageSourceUtil.getMessage("send.type.alarmLight");
        String sendResult;
        AlarmLightMsgResponseDTO alarmLightMsgResponseDTO = null;
        try {
            alarmLightMsgResponseDTO = proxyRestTemplateSSL.postForObject(notifyGatewayUrl, httpEntity, AlarmLightMsgResponseDTO.class);
            log.info("发送告警灯通知：{}", alarmLightMsgRequestDTO.getContent());
            sendResult = messageSourceUtil.getMessage("send.result.success");
        } catch (RestClientException e) {
            log.error("告警灯通知数据发送失败：{} {} {}", alarmLightMsgResponseDTO, alarmNotifyExecutorDTO.getSequenceId(), ExceptionUtil.stacktraceToString(e));
            sendResult = messageSourceUtil.getMessage("send.result.fail");
        }
        log.info("告警灯通知响应:{}", alarmLightMsgResponseDTO);
//        为避免数据量过大,不记录日志
//        super.alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, "", sendResult);
        if (alarmLightMsgResponseDTO != null && alarmLightMsgResponseDTO.getError_code() == 0) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
