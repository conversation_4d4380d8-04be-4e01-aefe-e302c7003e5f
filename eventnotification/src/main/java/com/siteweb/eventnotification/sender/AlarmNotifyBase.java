package com.siteweb.eventnotification.sender;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.common.util.StringUtils;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyRecord;
import com.siteweb.eventnotification.service.AlarmNotifyGatewayServiceConfigService;
import com.siteweb.eventnotification.service.AlarmNotifyRecordService;
import com.siteweb.shift.service.ScheduleService;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AlarmNotifyBase {
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    AlarmNotifyRecordService alarmNotifyRecordService;
    @Autowired
    AlarmNotifyGatewayServiceConfigService alarmNotifyGatewayServiceConfigService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    EmployeeService employeeService;

    /**
     * 节点是否没有设置接收人
     * @param elementConfigDTO 通知节点
     * @return boolean true 没有设置接收人  false 设置了接收人
     */
    protected final boolean emptyNotifyPerson(AlarmNotifyElementConfigDTO elementConfigDTO) {
        if (Objects.isNull(elementConfigDTO.getNodeDTOs())) {
            return true;
        }
        //没有选择人员并且没有选择班组
        return CharSequenceUtil.isBlank(elementConfigDTO.getExpression()) && CharSequenceUtil.isBlank(elementConfigDTO.getExtendField1());
    }
    /**
     * 获取通知人id
     *
     * @param personIds 人员id 多个逗号隔开
     * @param shiftGroupIds 班组id 多个逗号分割
     * @return {@link List}<{@link Integer}>
     */
    protected final List<Integer> getNotifyPersonIds(String personIds, String shiftGroupIds) {
        Set<Integer> personIdList = new HashSet<>();
        //获取通知人
        if (CharSequenceUtil.isNotBlank(personIds)) {
            personIdList.addAll(StringUtils.splitToIntegerCollection(personIds, HashSet::new));
        }
        //获取当班人
        if (CharSequenceUtil.isNotBlank(shiftGroupIds)) {
            List<Integer> shiftGroupList = StringUtils.splitToIntegerList(shiftGroupIds);
            List<Integer> dutyPersonIds = scheduleService.findDutyPersonByShiftGroupIdList(shiftGroupList);
            personIdList.addAll(dutyPersonIds);
        }
        return new ArrayList<>(personIdList);
    }

    /**
     * 获取接收人的名称，多个用逗号隔开
     * @param notifyPersonIds 接收人ids
     * @return {@link String}
     */
    protected final String getReceiverNames(List<Integer> notifyPersonIds){
        if (CollUtil.isEmpty(notifyPersonIds)) {
            return CharSequenceUtil.EMPTY;
        }
        List<String> employeeNames = employeeService.findEmployeeNameByIds(notifyPersonIds);
        return CollUtil.join(employeeNames,",");
    }
    /**
     * 获取接收人的手机号，多个用逗号隔开
     * @param notifyPersonIds 接收人ids
     * @return {@link String} 接收人的手机号，多个逗号隔开
     */
    protected final String getReceiverPhones(List<Integer> notifyPersonIds){
        if (CollUtil.isEmpty(notifyPersonIds)) {
            return CharSequenceUtil.EMPTY;
        }
        List<String> employeePhones = employeeService.findEmployeePhoneByIds(notifyPersonIds);
        return CollUtil.join(employeePhones,",");
    }

    /**
     * 获取接收人的工号，多个用逗号隔开
     * @param notifyPersonIds 接收人ids
     * @return {@link String} 接收人的手机号，多个逗号隔开
     */
    protected final String getReceiverJobNumber(List<Integer> notifyPersonIds){
        if (CollUtil.isEmpty(notifyPersonIds)) {
            return CharSequenceUtil.EMPTY;
        }
        List<String> employeeJobNumber = employeeService.findEmployeeJobNumberByIds(notifyPersonIds);
        return employeeJobNumber.stream().filter(CharSequenceUtil::isNotBlank).collect(Collectors.joining(","));
    }
    /**
     * 获取接收人的邮件，多个用逗号隔开
     * @param notifyPersonIds 接收人ids
     * @return {@link String}
     */
    protected final String getReceiverEmails(List<Integer> notifyPersonIds){
        if (CollUtil.isEmpty(notifyPersonIds)) {
            return CharSequenceUtil.EMPTY;
        }
        List<String> employeeEmail = employeeService.findEmployeeEmailByIds(notifyPersonIds);
        return CollUtil.join(employeeEmail,";");
    }

    /**
     * 获取通知网关的url
     * @param systemConfigEnum 系统参数
     * @param alarmNotifyConfigId 告警通知配置id
     * @param elementId 元素id
     * @return {@link List}<{@link Integer}>
     */
    protected final String getNotifyGatewayUrl(SystemConfigEnum systemConfigEnum, Integer alarmNotifyConfigId, Integer elementId) {
        //优先获取单独配置了的通知网关url
        AlarmNotifyGatewayServiceConfigDetailDTO alarmNotifyGatewayServiceConfigDetailDTO = alarmNotifyGatewayServiceConfigService.findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyConfigIdAndElementId(alarmNotifyConfigId, elementId);
        if (Objects.nonNull(alarmNotifyGatewayServiceConfigDetailDTO) && CharSequenceUtil.isNotBlank(alarmNotifyGatewayServiceConfigDetailDTO.getGatewayServiceUrl())) {
            return alarmNotifyGatewayServiceConfigDetailDTO.getGatewayServiceUrl().trim();
        }
        //获取系统参数中配置了的全局url
        SystemConfig alarmLightMessageSystemConfig = systemConfigService.findBySystemConfigKey(systemConfigEnum.getSystemConfigKey());
        if (Objects.nonNull(alarmLightMessageSystemConfig) && CharSequenceUtil.isNotBlank(alarmLightMessageSystemConfig.getSystemConfigValue())) {
            return alarmLightMessageSystemConfig.getSystemConfigValue().trim();
        }
        return CharSequenceUtil.EMPTY;
    }


    /**
     * 记录告警通知记录
     */
    protected final void alarmNotifyRecord(AlarmNotifyExecutorDTO alarmNotifyExecutorDTO,Integer alarmNotifyConfigId,String sendType,String receiver,String sendResult){
        AlarmNotifyRecord alarmNotifyRecord = new AlarmNotifyRecord();
        alarmNotifyRecord.setAlarmNotifyConfigId(alarmNotifyConfigId);
        alarmNotifyRecord.setStationId(alarmNotifyExecutorDTO.getStationId());
        alarmNotifyRecord.setEquipmentId(alarmNotifyExecutorDTO.getEquipmentId());
        alarmNotifyRecord.setEventId(alarmNotifyExecutorDTO.getEventId());
        alarmNotifyRecord.setEventConditionId(alarmNotifyExecutorDTO.getEventConditionId());
        alarmNotifyRecord.setEventSeverityId(alarmNotifyExecutorDTO.getEventSeverityId());
        alarmNotifyRecord.setContent(alarmNotifyExecutorDTO.getAlarmNotifyContent());
        alarmNotifyRecord.setAlarmStartTime(alarmNotifyExecutorDTO.getStartTime());
        alarmNotifyRecord.setSequenceId(alarmNotifyExecutorDTO.getSequenceId());
        alarmNotifyRecord.setSendTime(new Date());
        alarmNotifyRecord.setReceiver(receiver);
        alarmNotifyRecord.setSendType(sendType);
        alarmNotifyRecord.setSendResult(sendResult);
        alarmNotifyRecordService.createAlarmNotifyRecord(alarmNotifyRecord);
    }
}
