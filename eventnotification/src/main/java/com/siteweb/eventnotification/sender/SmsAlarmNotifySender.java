package com.siteweb.eventnotification.sender;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.dto.SmsMessageRequestDTO;
import com.siteweb.utility.dto.SmsMessageResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description SmsAlarmNotifySender
 * @createTime 2022-04-24 16:25:28
 */
@Service
public class SmsAlarmNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(SmsAlarmNotifySender.class);

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        if (emptyNotifyPerson(elementConfigDTO)) {
            log.error("短信接收人未配置 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("短信通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.MESSAGE_BATCH_API_URL, elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("短信网关服务发送批量短信URL未配置");
            return -3;
        }
        //获取通知人id
        List<Integer> notifyPersonIds = super.getNotifyPersonIds(elementConfigDTO.getExpression(), elementConfigDTO.getExtendField1());
        String receiverPhones = getReceiverPhones(notifyPersonIds);
        if (CharSequenceUtil.isBlank(receiverPhones)) {
            log.error("短信接收人为空 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -4;
        }
        SmsMessageRequestDTO smsMessageRequestDTO = new SmsMessageRequestDTO();
        smsMessageRequestDTO.setText(alarmNotifyExecutorDTO.getAlarmNotifyContent());
        smsMessageRequestDTO.setSender("admin");
        smsMessageRequestDTO.setTo(receiverPhones);
        smsMessageRequestDTO.setSequenceId(alarmNotifyExecutorDTO.getSequenceId());
        smsMessageRequestDTO.setTag("alarm");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SmsMessageRequestDTO> httpEntity = new HttpEntity<>(smsMessageRequestDTO, httpHeaders);
        SmsMessageResponseDTO smsMessageResponseDTO = null;
        String sendResult = "";
        try {
            smsMessageResponseDTO = restTemplate.postForObject(notifyGatewayUrl, httpEntity, SmsMessageResponseDTO.class);
            sendResult = messageSourceUtil.getMessage("send.result.success");
            log.info("发送短信告警通知：{} {}", smsMessageRequestDTO.getText(), smsMessageRequestDTO.getSequenceId());
        } catch (RestClientException e) {
            sendResult = messageSourceUtil.getMessage("send.result.fail");
            log.error("短信告警通知数据发送失败：{} {} {}", smsMessageRequestDTO.getText(), smsMessageRequestDTO.getSequenceId(), e.getMessage());
        }
        String sendType = messageSourceUtil.getMessage("send.type.sms");
        String receiverJoin = getReceiverNames(notifyPersonIds);
        super.alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, receiverJoin, sendResult);
        if (smsMessageResponseDTO != null) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
