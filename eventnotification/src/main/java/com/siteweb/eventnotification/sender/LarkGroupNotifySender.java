package com.siteweb.eventnotification.sender;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.entity.Employee;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.dto.lark.LarkMessageRequestDTO;
import com.siteweb.eventnotification.dto.wecom.WeComMessageResponseDTO;
import com.siteweb.utility.constans.SystemConfigEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 飞书群通知
 * 接受人是@人
 * 没有接收人，没有@人则直接发群里
 * <AUTHOR>
 * @date 2025/03/16
 */
@Service
public class LarkGroupNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(LarkGroupNotifySender.class);

    @Autowired
    @Qualifier("proxyRestTemplateSSL")
    RestTemplate proxyRestTemplateSSL;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("飞书通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.LARK_APPLY_API_URL, elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("飞书通知URL未配置");
            return -2;
        }
        String to = getAtInfo(elementConfigDTO.getExpression(), elementConfigDTO.getExtendField1());
        LarkMessageRequestDTO larkMessageRequestDTO = new LarkMessageRequestDTO(alarmNotifyExecutorDTO.getAlarmNotifyContent(), to);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<LarkMessageRequestDTO> httpEntity = new HttpEntity<>(larkMessageRequestDTO, httpHeaders);
        String sendType = messageSourceUtil.getMessage("send.type.weComApply");
        String sendResult = "";
        WeComMessageResponseDTO weComweMessageResponseDTO = null;
        try {
            log.info("请求飞书通知消息,url:{},参数:{}", notifyGatewayUrl, larkMessageRequestDTO);
            weComweMessageResponseDTO = proxyRestTemplateSSL.postForObject(notifyGatewayUrl, httpEntity, WeComMessageResponseDTO.class);
            sendResult = messageSourceUtil.getMessage("send.result.success");
        } catch (RestClientException e) {
            sendResult = messageSourceUtil.getMessage("send.result.fail");
            log.error("飞书通知数据发送失败：{} {},{}", weComweMessageResponseDTO, alarmNotifyExecutorDTO.getSequenceId(), ExceptionUtil.stacktraceToString(e));
        }
        log.info("飞书通知响应:{}",weComweMessageResponseDTO);
        super.alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, "", sendResult);
        if (weComweMessageResponseDTO != null && Objects.equals(weComweMessageResponseDTO.getError_code(), 0)) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }

    /**
     * 获取发送的@的群成员信息
     * 发送格式 【工号|员工名称】 多个用逗号割开 例如 ou_sdfsdfsfsfsf66223|张三,ou_sdfsdfsfs343123|李四
     * 可以为空，为空则不@群成员
     * @param expression   人员信息
     * @param extendField1 班组信息
     * @return {@link String } 需要发送给通知网关的 to信息
     */
    private String getAtInfo(String expression, String extendField1) {
        List<Integer> notifyPersonIds = super.getNotifyPersonIds(expression, extendField1);
        if (CollUtil.isEmpty(notifyPersonIds)) {
            return "";
        }
        List<Employee> employeeList = employeeService.findByEmployeeIds(notifyPersonIds);
        return employeeList.stream()
                           .map(e -> e.getJobNumber() + "|" + e.getEmployeeName())
                           .collect(Collectors.joining(","));
    }
}
