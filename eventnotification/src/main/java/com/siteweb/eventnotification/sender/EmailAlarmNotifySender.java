package com.siteweb.eventnotification.sender;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.utility.entity.EmailMessage;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.enums.EmailOperationResponse;
import com.siteweb.utility.manager.EmailMessageManager;
import com.siteweb.utility.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description EmailAlarmNotifySender
 * @createTime 2022-04-25 15:00:41
 */
@Service
public class EmailAlarmNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(EmailAlarmNotifySender.class);

    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    EmailMessageManager emailMessageManager;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        if (emptyNotifyPerson(elementConfigDTO)) {
            log.error("邮件接收人未配置 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("邮件通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        SystemConfig emailSenderSystemConfig = systemConfigService.findBySystemConfigKey("spring.mail.username");
        if (emailSenderSystemConfig == null || emailSenderSystemConfig.getSystemConfigValue().trim().isEmpty()) {
            log.error("邮件发件人未配置");
            return -3;
        }
        //获取通知人id
        List<Integer> notifyPersonIds = super.getNotifyPersonIds(elementConfigDTO.getExpression(), elementConfigDTO.getExtendField1());
        String receiverEmails = getReceiverEmails(notifyPersonIds);
        if (CharSequenceUtil.isBlank(receiverEmails)) {
            log.error("邮件接收人为空 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -4;
        }
        EmailMessage emailMessage = new EmailMessage();
        emailMessage.setSender(emailSenderSystemConfig.getSystemConfigValue().trim());
        emailMessage.setTo(receiverEmails);
        emailMessage.setMessage(alarmNotifyExecutorDTO.getAlarmNotifyContent());
        SystemConfig emailSubjectSystemConfig = systemConfigService.findBySystemConfigKey("spring.mail.subject");
        if (ObjectUtil.isNull(emailSubjectSystemConfig)|| ObjectUtil.isNull(emailSubjectSystemConfig.getSystemConfigValue())
                || emailSubjectSystemConfig.getSystemConfigValue().trim().isEmpty()) {
            //没有配置邮件主题系统参数则使用原主题设置方式
            emailMessage.setSubject(messageSourceUtil.getMessage("send.mail.subject"));
        } else {
            emailMessage.setSubject(emailSubjectSystemConfig.getSystemConfigValue().trim());
        }

        emailMessage.setCreateTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        emailMessage.setSendTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        TimeInterval timeInterval = new TimeInterval();
        timeInterval.start();
        EmailOperationResponse operationResponse = emailMessageManager.sendMail(emailMessage);
        log.info("本次邮箱发送耗时:{}", timeInterval.interval());
        String sendResult = "";
        String sendType = messageSourceUtil.getMessage("send.type.mail");
        if (operationResponse.getOperationStatus().equals(EmailOperationResponse.ResponseStatusEnum.SUCCESS)) {
            sendResult = messageSourceUtil.getMessage("send.result.success");
            log.info("发送邮件告警通知：{}", alarmNotifyExecutorDTO.getAlarmNotifyContent());
            result = 1;
        } else {
            sendResult = messageSourceUtil.getMessage("send.result.fail");
            log.error("邮件告警通知数据发送失败：{}", alarmNotifyExecutorDTO.getAlarmNotifyContent());
        }
        String receiverJoin = getReceiverNames(notifyPersonIds);
        super.alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, receiverJoin, sendResult);
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
