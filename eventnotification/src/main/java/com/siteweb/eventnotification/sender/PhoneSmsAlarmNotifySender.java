package com.siteweb.eventnotification.sender;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.dto.AlarmBoxMsgResponseDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.dto.PhoneSmsMsgRequestDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description PhoneSmsAlarmNotifySender
 * @createTime 2022-04-25 15:01:40
 */
@Service
public class PhoneSmsAlarmNotifySender extends AlarmNotifyBase implements AlarmNotifySendService {

    private final Logger log = LoggerFactory.getLogger(PhoneSmsAlarmNotifySender.class);

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public int alarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        if (emptyNotifyPerson(elementConfigDTO)) {
            log.error("电话语音(短信)接收人未配置 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if ("right".equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("电话语音(短信)通知流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        String notifyGatewayUrl = getNotifyGatewayUrl(SystemConfigEnum.PHONE_SMS_API_URL, elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getElementId());
        if (CharSequenceUtil.isBlank(notifyGatewayUrl)) {
            log.error("电话语音(短信)网关服务发送批量电话语音(短信)URL未配置");
            return -3;
        }
        //获取通知人id
        List<Integer> notifyPersonIds = super.getNotifyPersonIds(CharSequenceUtil.subBefore(elementConfigDTO.getExpression(), ";", false), elementConfigDTO.getExtendField1());
        String phoneNOs = getReceiverPhones(notifyPersonIds);
        if (CharSequenceUtil.isBlank(phoneNOs)) {
            log.error("电话语音(短信)接收人为空 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -4;
        }
        //默认是同时拨打电话和发送短信
        Optional<String> msgSendTypeOptional = Optional.ofNullable(CharSequenceUtil.subAfter(elementConfigDTO.getExpression(), ";", false));
        PhoneSmsMsgRequestDTO requestDTO = new PhoneSmsMsgRequestDTO();
        requestDTO.setContent(alarmNotifyExecutorDTO.getAlarmNotifyContent());
        requestDTO.setDelimiter(":,");
        requestDTO.setSubtype(msgSendTypeOptional.orElse("0"));
        requestDTO.setTo(phoneNOs);
        //直接发送content内容，网关不作处理
        requestDTO.setIsassemble("2");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PhoneSmsMsgRequestDTO> httpEntity = new HttpEntity<>(requestDTO, httpHeaders);
        AlarmBoxMsgResponseDTO alarmBoxMsgResponseDTO = null;
        String sendResult = "";
        try {
            alarmBoxMsgResponseDTO = restTemplate.postForObject(notifyGatewayUrl, httpEntity, AlarmBoxMsgResponseDTO.class);
            sendResult = messageSourceUtil.getMessage("send.result.success");
            log.info("发送电话语音(短信)通知：{}", requestDTO.getContent());
        } catch (RestClientException e) {
            sendResult = messageSourceUtil.getMessage("send.result.fail");
            log.error("电话语音(短信)通知数据发送失败：{} {}", requestDTO.getContent(), e.getMessage());
        }
        String sendType = messageSourceUtil.getMessage("send.type.telephonevoice");
        String receiverJoin = getReceiverNames(notifyPersonIds);
        alarmNotifyRecord(alarmNotifyExecutorDTO, elementConfigDTO.getAlarmNotifyConfigId(), sendType, receiverJoin, sendResult);
        if (alarmBoxMsgResponseDTO != null && alarmBoxMsgResponseDTO.getError_code() == 0) {
            result = 1;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
