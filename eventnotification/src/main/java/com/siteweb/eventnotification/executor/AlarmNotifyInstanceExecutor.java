package com.siteweb.eventnotification.executor;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.dto.AlarmNotifySegmentDTO;
import com.siteweb.eventnotification.service.AlarmNotifyElementConfigService;
import com.siteweb.eventnotification.service.AlarmNotifyExecutorService;
import com.siteweb.eventnotification.service.AlarmNotifyNodeService;
import com.siteweb.eventnotification.service.AlarmNotifySegmentService;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.service.ActiveEventService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyInstanceExecutor
 * @createTime 2022-04-24 16:42:31
 */
public class AlarmNotifyInstanceExecutor implements Runnable {

    private final Logger log = LoggerFactory.getLogger(AlarmNotifyInstanceExecutor.class);

    private static final String LEFT = "left";
    private static final String RIGHT = "right";
    //节点执行中断时的返回值
    private static final int INTERRUPT_EXEC_RESULT = -100;

    private final Deque<AlarmNotifyElementConfigDTO> toCalcElementConfigDeque = new ArrayDeque<>();
    private final HashMap<Integer, String> nodeExpressionHashMap = new HashMap<>();

    private final Integer alarmNotifyConfigId;
    private AlarmNotifyExecutorDTO alarmNotifyExecutorDTO;
    private final AlarmNotifyElementConfigService alarmNotifyElementConfigService;
    private final AlarmNotifyNodeService alarmNotifyNodeService;
    private final AlarmNotifySegmentService alarmNotifySegmentService;
    private final ActiveEventService activeEventService;
    private final AlarmNotifyExecutorService alarmNotifyExecutorService;

    private List<AlarmNotifyElementConfigDTO> alarmNotifyElementConfigDTOList;
    private List<AlarmNotifyNodeDTO> alarmNotifyNodeDTOList;
    private List<AlarmNotifySegmentDTO> alarmNotifySegmentDTOList;

    public AlarmNotifyInstanceExecutor(Integer alarmNotifyConfigId, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, AlarmNotifyElementConfigService alarmNotifyElementConfigService,
                                       AlarmNotifyNodeService alarmNotifyNodeService, AlarmNotifySegmentService alarmNotifySegmentService, ActiveEventService activeEventService,
                                       AlarmNotifyExecutorService alarmNotifyExecutorService) {
        this.alarmNotifyConfigId = alarmNotifyConfigId;
        this.alarmNotifyExecutorDTO = alarmNotifyExecutorDTO;
        this.alarmNotifyElementConfigService = alarmNotifyElementConfigService;
        this.alarmNotifyNodeService = alarmNotifyNodeService;
        this.alarmNotifySegmentService = alarmNotifySegmentService;
        this.activeEventService = activeEventService;
        this.alarmNotifyExecutorService = alarmNotifyExecutorService;
    }

    @Override
    public void run() {
        try {
            if (Boolean.TRUE.equals(this.alarmNotifyExecutorDTO.getReCheckActiveEvent())) {
                if (this.alarmNotifyExecutorDTO.getOperationType() == 1) {//对于开始告警且启用了告警延时的策略，若告警结束后就停止告警通知任务
                    ActiveEvent activeEvent = activeEventService.getActiveEventBySequenceId(this.alarmNotifyExecutorDTO.getSequenceId());
                    if (activeEvent == null || (activeEvent != null && activeEvent.getEndTime() != null)) {
                        log.info("因告警 {} 在延时时间前已结束，告警通知任务停止, AlarmNotifyConfigId为{}", this.alarmNotifyExecutorDTO.getSequenceId(), this.alarmNotifyConfigId);
                        return;
                    }
                } else if (this.alarmNotifyExecutorDTO.getOperationType() == 2) {//对于结束告警且启用了告警延时的策略，若告警持续时间（告警结束时间减去告警开始时间）小于延时时间，则停止告警通知任务
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(this.alarmNotifyExecutorDTO.getStartTime());
                    calendar.add(Calendar.SECOND, Integer.parseInt(this.alarmNotifyExecutorDTO.getNotifyDelay()));
                    Date startTime = calendar.getTime();
                    if (startTime.after(this.alarmNotifyExecutorDTO.getEndTime())) {
                        log.info("因告警 {} 在延时时间前已结束，告警通知任务停止, AlarmNotifyConfigId为{}", this.alarmNotifyExecutorDTO.getSequenceId(), this.alarmNotifyConfigId);
                        return;
                    }
                } else if (this.alarmNotifyExecutorDTO.getOperationType() == 3) {//对于确认告警且启用了告警延时的策略，若告警确认时间减去告警开始时间之差小于延时时间，则停止告警通知任务
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(this.alarmNotifyExecutorDTO.getStartTime());
                    calendar.add(Calendar.SECOND, Integer.parseInt(this.alarmNotifyExecutorDTO.getNotifyDelay()));
                    Date startTime = calendar.getTime();
                    if (startTime.after(this.alarmNotifyExecutorDTO.getConfirmTime())) {
                        log.info("因告警 {} 在延时时间前已确认，告警通知任务停止, AlarmNotifyConfigId为{}", this.alarmNotifyExecutorDTO.getSequenceId(), this.alarmNotifyConfigId);
                        return;
                    }
                }
            }
            calcAlarmNotifyElementConfigExpression();
        } catch (Exception ex) {
            log.error("告警通知线程执行失败 {} {}", ex, ExceptionUtil.stacktraceToString(ex));
        }
    }

    private void calcAlarmNotifyElementConfigExpression() {
        if (toCalcElementConfigDeque.isEmpty()) {
            calcLevel1ElementConfig();
        }
        while (!toCalcElementConfigDeque.isEmpty()) {
            AlarmNotifyElementConfigDTO elementConfigDTO = toCalcElementConfigDeque.pollFirst();
            if (elementConfigDTO != null) {
                int execStatus = calcElementConfigExpression(elementConfigDTO);
                if (execStatus < 0 && execStatus != INTERRUPT_EXEC_RESULT) {
                    log.error("告警通知执行失败, AlarmNotifyConfigId为{}, AlarmNotifyElementConfigId为{}, ElementId为{}, 错误码{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId(), elementConfigDTO.getElementId(), execStatus);
                    break;
                }
            }
        }
    }

    private void calcLevel1ElementConfig() {
        this.nodeExpressionHashMap.clear();
        this.alarmNotifyElementConfigDTOList = this.alarmNotifyElementConfigService.findAlarmNotifyElementConfigDTOsByAlarmNotifyConfigId(this.alarmNotifyConfigId);
        this.alarmNotifyNodeDTOList = this.alarmNotifyNodeService.findAlarmNotifyNodesByAlarmNotifyConfigId(this.alarmNotifyConfigId);
        this.alarmNotifySegmentDTOList = this.alarmNotifySegmentService.findAlarmNotifySegmentsByAlarmNotifyConfigId(this.alarmNotifyConfigId);
        for (AlarmNotifyElementConfigDTO elementConfigDTO : alarmNotifyElementConfigDTOList) {
            if (elementConfigDTO.getNodeDTOs() == null || elementConfigDTO.getNodeDTOs().isEmpty() || elementConfigDTO.getNodeDTOs().stream().noneMatch(o -> LEFT.equalsIgnoreCase(o.getNodeDirection()))) {
                toCalcElementConfigDeque.addLast(elementConfigDTO);
                continue;
            }
            for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
                if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                    List<AlarmNotifySegmentDTO> inputSegments = alarmNotifySegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).toList();
                    if (inputSegments.isEmpty()) {
                        toCalcElementConfigDeque.addLast(elementConfigDTO);
                    }
                }
            }
        }
    }

    private int calcElementConfigExpression(AlarmNotifyElementConfigDTO elementConfigDTO) {
        int result = 0;
        switch (elementConfigDTO.getElementId()) {
            case 1://短信发送
                result = this.alarmNotifyExecutorService.smsAlarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 2://邮件发送
                result = this.alarmNotifyExecutorService.emailAlarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 3://告警箱通知
                result = this.alarmNotifyExecutorService.alarmBoxAlarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 4://电话语音（短信）告警通知
                result = this.alarmNotifyExecutorService.phoneSmsAlarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
//            case 8://短信发送（东北大学定制接口）
//                result = this.neuSmsAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
//                break;
//            case 9://邮件发送（东北大学定制接口）
//                result = this.neuEmailAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
//                break;
//            case 10://微信发送（东北大学定制接口）
//                result = this.neuWechatAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
//                break;
            case 11://失败重发
                result = calcRetry(elementConfigDTO);
                break;
            case 12://超时未确认
                result = calcTimeout(elementConfigDTO);
                break;
            case 13://企业微信应用通知
                result = this.alarmNotifyExecutorService.weComGroupNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 14://告警灯通知
                result = this.alarmNotifyExecutorService.alarmLightAlarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 15://APP推送
                result = this.alarmNotifyExecutorService.alarmAppNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 16://短信通知(工号)
                result = this.alarmNotifyExecutorService.alarmAppNotifyByJobNumberSend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            case 17://飞书通知
                result = this.alarmNotifyExecutorService.LarkNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
                break;
            default:
                break;
        }
        log.info("当前告警通知的AlarmNotifyConfigId为{}，已计算的控件AlarmNotifyElementConfigId为{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
        if (result >= 0) {
            checkNextLinkElementConfig(elementConfigDTO);
        }
        return result;
    }

    //当前elementConfigDTO计算完毕后，找到其输出节点关联的下一个LinkElementConfigDTO并加入toCalcElementConfigDeque
    private void checkNextLinkElementConfig(AlarmNotifyElementConfigDTO elementConfigDTO) {
        List<Integer> nextLinkElementConfigIds = new ArrayList<>();
        List<AlarmNotifyNodeDTO> currentOutputNodes = elementConfigDTO.getNodeDTOs().stream().filter(o -> RIGHT.equalsIgnoreCase(o.getNodeDirection())).toList();
        for (AlarmNotifyNodeDTO linkNodeDTO : currentOutputNodes) {
            List<AlarmNotifySegmentDTO> tmpList = alarmNotifySegmentDTOList.stream().filter(o -> linkNodeDTO.getNodeId().equals(o.getInputNodeId())).toList();
            if (!tmpList.isEmpty()) {
                nextLinkElementConfigIds.add(tmpList.get(0).getOutputElementConfigId());
            }
        }
        for (Integer elementConfigId : nextLinkElementConfigIds) {
            List<Integer> tmpInputNodeIdList = alarmNotifyNodeDTOList.stream().filter(o -> elementConfigId.equals(o.getAlarmNotifyElementConfigId()) && LEFT.equalsIgnoreCase(o.getNodeDirection())).map(AlarmNotifyNodeDTO::getNodeId).toList();
            List<Integer> tmpList = alarmNotifySegmentDTOList.stream().filter(o -> tmpInputNodeIdList.contains(o.getOutputNodeId())).map(AlarmNotifySegmentDTO::getInputNodeId).toList();
            if (nodeExpressionHashMap.keySet().containsAll(tmpList)) {
                Optional<AlarmNotifyElementConfigDTO> nextElementConfigDTOOptional = alarmNotifyElementConfigDTOList.stream().filter(o -> elementConfigId.equals(o.getAlarmNotifyElementConfigId())).findFirst();
                nextElementConfigDTOOptional.ifPresent(toCalcElementConfigDeque::addLast);
            }
        }
    }

    //失败重发
    private int calcRetry(AlarmNotifyElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null) {
            log.error("失败重发流程配置错误，找不到输入输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            } else if (LEFT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                Optional<AlarmNotifySegmentDTO> segmentDTO = alarmNotifySegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (segmentDTO.isPresent() && nodeExpressionHashMap.containsKey(segmentDTO.get().getInputNodeId()) && Integer.parseInt(nodeExpressionHashMap.get(segmentDTO.get().getInputNodeId())) == 0) {
                    result = 1;
                }
            }
        }
        if (outputNode == null) {
            log.error("失败重发流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        if (result <= 0) {
            log.info("无需失败重发，流程结束 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return INTERRUPT_EXEC_RESULT;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }

    //超时未确认
    private int calcTimeout(AlarmNotifyElementConfigDTO elementConfigDTO) {
        if (elementConfigDTO.getNodeDTOs() == null) {
            log.error("超时未确认流程配置错误，找不到输入输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -1;
        }
        AlarmNotifyNodeDTO outputNode = null;
        int result = 0;
        for (AlarmNotifyNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
            if (RIGHT.equalsIgnoreCase(nodeDTO.getNodeDirection())) {
                outputNode = nodeDTO;
            }
        }
        if (outputNode == null) {
            log.error("超时未确认流程配置错误，找不到输出节点 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return -2;
        }
        int timeoutSeconds = 0;
        if (elementConfigDTO.getExpression() != null && !elementConfigDTO.getExpression().trim().isEmpty()) {
            timeoutSeconds = Integer.parseInt(elementConfigDTO.getExpression());
        }
        if (timeoutSeconds > 0) {
            try {
                Thread.sleep(timeoutSeconds * 1000L);
            } catch (InterruptedException e) {
                log.error(e.getMessage());
                Thread.currentThread().interrupt();
            }
            ActiveEvent activeEvent = activeEventService.getActiveEventBySequenceId(this.alarmNotifyExecutorDTO.getSequenceId());
            if (activeEvent != null && activeEvent.getConfirmTime() == null) {
                result = 1;
            }
        }
        if (result <= 0) {
            log.info("没有符合超时未确认的告警，流程结束 AlarmNotifyConfigId:{} AlarmNotifyElementConfigId:{}", elementConfigDTO.getAlarmNotifyConfigId(), elementConfigDTO.getAlarmNotifyElementConfigId());
            return INTERRUPT_EXEC_RESULT;
        }
        nodeExpressionHashMap.put(outputNode.getNodeId(), String.valueOf(result));
        return result;
    }
}
