package com.siteweb.eventnotification.textnotify.alarmtextnotify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.eventnotification.dto.AlarmTextNotifyDTO;
import com.siteweb.eventnotification.entity.TtsConfig;
import com.siteweb.eventnotification.enums.TtsConfigEnum;
import com.siteweb.eventnotification.service.NotificationContentItemService;
import com.siteweb.eventnotification.service.TtsConfigService;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> zhou
 * @description AlarmTextNotifyServiceImpl
 * @createTime 2022-05-25 15:16:45
 */
@Service
@Slf4j
public class AlarmTextNotifyServiceImpl implements AlarmTextNotifyService {
    public static final String CONTENT_FORMAT = "%s %s %s";

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    NotificationContentItemService notificationContentItemService;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Autowired
    RegionService regionService;

    @Autowired
    RegionMapService regionMapService;
    @Autowired
    TtsConfigService ttsConfigService;

    @Override
    public AlarmTextNotifyDTO getAlarmTextNotifyByActiveEvent(AlarmChange alarmChange) {
        if (Objects.isNull(alarmChange)) {
            return null;
        }
        AlarmTextNotifyDTO alarmTextNotifyDTO = new AlarmTextNotifyDTO();
        if (alarmChange.getEndTime() == null) {
            setAlarmStartTextNotifyValue(alarmChange, alarmTextNotifyDTO);
        } else {
            setAlarmEndTextNotifyValue(alarmChange, alarmTextNotifyDTO);
        }
        return alarmTextNotifyDTO;
    }

    private void setAlarmStartTextNotifyValue(AlarmChange alarmChange, AlarmTextNotifyDTO alarmTextNotifyDTO) {
        String contentTemplate = getAlarmStartContentTemplate();
        if (contentTemplate != null) {
            alarmTextNotifyDTO.setText(notificationContentItemService.assembleNotificationContentByLiveEventDTO(new ActiveEventDTO(alarmChange.toActiveEvent()), contentTemplate));
        } else {
            alarmTextNotifyDTO.setText(String.format(CONTENT_FORMAT, alarmChange.getEventSeverity(), localeMessageSourceUtil.getMessage("eventNotification.start"), alarmChange.getEventName()));
        }
        alarmTextNotifyDTO.setSeverityId(alarmChange.getEventLevel());
        alarmTextNotifyDTO.setStartTime(alarmChange.getStartTime());
        alarmTextNotifyDTO.setSequenceId(alarmChange.getSequenceId());
        alarmTextNotifyDTO.setConfirmTime(alarmChange.getConfirmTime());
        alarmTextNotifyDTO.setOperationType(alarmChange.getOperationType());
    }

    private void setAlarmEndTextNotifyValue(AlarmChange alarmChange, AlarmTextNotifyDTO alarmTextNotifyDTO) {
        String contentTemplate = getAlarmEndContentTemplate();
        if (contentTemplate != null) {
            alarmTextNotifyDTO.setText(notificationContentItemService.assembleNotificationContentByLiveEventDTO(new ActiveEventDTO(alarmChange.toActiveEvent()), contentTemplate));
        } else {
            alarmTextNotifyDTO.setText(String.format(CONTENT_FORMAT, alarmChange.getEventSeverity(), localeMessageSourceUtil.getMessage("eventNotification.end"), alarmChange.getEventName()));
        }
        alarmTextNotifyDTO.setSeverityId(alarmChange.getEventLevel());
        alarmTextNotifyDTO.setStartTime(alarmChange.getStartTime());
        alarmTextNotifyDTO.setEndTime(alarmChange.getEndTime());
        alarmTextNotifyDTO.setConfirmTime(alarmChange.getConfirmTime());
        alarmTextNotifyDTO.setSequenceId(alarmChange.getSequenceId());
        alarmTextNotifyDTO.setOperationType(alarmChange.getOperationType());
    }

    @Override
    public Set<Integer> getAlarmSeverityIds() {
        TtsConfig ttsConfig = ttsConfigService.findByKey(TtsConfigEnum.NOTIFICATION_HOST_ALARM_SEVERITY_SYSTEM_CONFIG_KEY.getTtsConfigKey());
        if (Objects.isNull(ttsConfig) || CharSequenceUtil.isBlank(ttsConfig.getTtsConfigValue())) {
            return Collections.emptySet();
        }
        return StringUtils.splitToIntegerCollection(ttsConfig.getTtsConfigValue().trim(), HashSet::new);
    }

    private String getAlarmStartContentTemplate() {
        TtsConfig ttsConfig = ttsConfigService.findByKey(TtsConfigEnum.TTS_MESSAGE_START_ALARM_CONTENT_TEMPLATE.getTtsConfigKey());
        if (Objects.isNull(ttsConfig) || CharSequenceUtil.isBlank(ttsConfig.getTtsConfigValue())) {
            return null;
        }
        return ttsConfig.getTtsConfigValue().trim();
    }

    private String getAlarmEndContentTemplate() {
        TtsConfig ttsConfig = ttsConfigService.findByKey(TtsConfigEnum.TTS_MESSAGE_END_ALARM_CONTENT_TEMPLATE.getTtsConfigKey());
        if (Objects.isNull(ttsConfig) || CharSequenceUtil.isBlank(ttsConfig.getTtsConfigValue())) {
            return null;
        }
        return ttsConfig.getTtsConfigValue().trim();
    }

    @Override
    public List<ActiveEventDTO> getTtsActiveEvent(Integer userId, Comparator<? super ActiveEventDTO> comparator) {
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEventEnded(false);
        if (this.enableConfirmNoBroadcast()) {
            activeEventFilterVO.setEventConfirmed(false);
        }
        Set<Integer> alarmSeverityIds = getAlarmSeverityIds();
        activeEventFilterVO.setEventSeverityIds(CollUtil.join(alarmSeverityIds,","));
        return activeEventManager.queryActiveEvents(userId, activeEventFilterVO)
                                 .stream()
                                 .map(ActiveEventDTO::new)
                                 .sorted(comparator)
                                 .toList();
    }

    @Override
    public boolean enableCircularBroadcast() {
        return getTtsEnableParameter(TtsConfigEnum.TTS_CIRCULAR_BROADCAST_ENABLE);
    }

    @Override
    public boolean enableSortByEventLevel() {
        return getTtsEnableParameter(TtsConfigEnum.TTS_SORTBY_EVENTLEVEL);
    }

    @Override
    public boolean enableConfirmNoBroadcast() {
        return getTtsEnableParameter(TtsConfigEnum.TTS_CONFIRM_NO_BROADCAST_ENABLE);
    }

    @Override
    public boolean enableEndNoBroadcast() {
        return getTtsEnableParameter(TtsConfigEnum.TTS_END_NO_BROADCAST_ENABLE);
    }

    @Override
    public boolean enableFirstPushAlarm() {
        return getTtsEnableParameter(TtsConfigEnum.TTS_FIRST_PUSH_ALARM_ENABLE);
    }

    private boolean getTtsEnableParameter(TtsConfigEnum ttsConfigEnum){
        TtsConfig ttsConfig = ttsConfigService.findByKey(ttsConfigEnum.getTtsConfigKey());
        if (Objects.isNull(ttsConfig)) {
            return false;
        }
        return Boolean.parseBoolean(ttsConfig.getTtsConfigValue());
    }
}
