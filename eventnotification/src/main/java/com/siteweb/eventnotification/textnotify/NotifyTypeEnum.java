package com.siteweb.eventnotification.textnotify;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum NotifyTypeEnum {
    ALARM_TEXT_NOTIFY(1, "AlarmNotify:", 2, "告警通知","alarmTextNotifyHttpService"),
    PRE_ALARM_TEXT_NOTIFY(2, "PreAlarmNotify:", 3, "预警通知","preAlarmNotifyServiceImpl"),
    INTERNAL_MESSAGE(3, "InternalMessage:", 1, "站内消息","internalMessageTextNotifyService");

    /**
     * 消息类型
     */
    private final int notifyType;
    /**
     * redis消息前缀
     */
    private final String redisKeyPrefix;
    /**
     * 消息优先级  值越小 越优先播报
     */
    private final int priority;
    /**
     * 描述
     */
    private final String describe;

    /**
     * 注入bean的名称
     */
    private final String beanName;
    private static final Map<Integer, NotifyTypeEnum> NOTIFY_TYPE_MAP = Arrays.stream(values()).collect(Collectors.toMap(NotifyTypeEnum::getNotifyType, e -> e));

    public static NotifyTypeEnum getNotifyTypeEnum(int notifyType) {
        return NOTIFY_TYPE_MAP.get(notifyType);
    }

    @JsonValue
    public int getNotifyType() {
        return notifyType;
    }
}
