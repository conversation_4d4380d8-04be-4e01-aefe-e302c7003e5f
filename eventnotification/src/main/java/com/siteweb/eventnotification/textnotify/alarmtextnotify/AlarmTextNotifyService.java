package com.siteweb.eventnotification.textnotify.alarmtextnotify;

import com.siteweb.eventnotification.dto.AlarmTextNotifyDTO;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.entity.AlarmChange;

import java.util.Comparator;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description AlarmTextNotifyService
 * @createTime 2022-05-25 15:16:12
 */
public interface AlarmTextNotifyService {

    /**
     * 获取TTS语音需要播报的活动告警消息
     * @param userid 用户id
     * @param comparator 排序规则
     * @return {@link List}<{@link ActiveEventDTO}>
     */
    List<ActiveEventDTO> getTtsActiveEvent(Integer userid, Comparator<? super ActiveEventDTO> comparator);

    AlarmTextNotifyDTO getAlarmTextNotifyByActiveEvent(AlarmChange alarmChange);

    /**
     * 触发告警通知的告警等级(多个告警等级之间以半角逗号隔开)
     * @return {@link List}<{@link Integer}>
     */
    Set<Integer> getAlarmSeverityIds();

    /**
     * 是否开启活动告警循环播报
     */
    boolean enableCircularBroadcast();

    /**
     * 是否开启按照告警等级优先播报
     */
    boolean enableSortByEventLevel();

    /**
     * 确认不播报
     * @return boolean
     */
    boolean enableConfirmNoBroadcast();

    /**
     * 结束不播报
     * @return boolean
     */
    boolean enableEndNoBroadcast();

    /**
     * 是否启用TTS不播报登录时的活动告警
     * @return boolean
     */
    boolean enableFirstPushAlarm();
}
