package com.siteweb.eventnotification.textnotify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.util.StringUtils;
import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;
import com.siteweb.eventnotification.entity.TtsConfig;
import com.siteweb.eventnotification.enums.TtsConfigEnum;
import com.siteweb.eventnotification.service.TtsConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class TextNotifyManager {
    private static final int HEALTH_INTERVAL = 180;
    // 默认播放顺序的常量
    private static final List<Integer> DEFAULT_PLAYBACK_ORDER = List.of(1, 2, 3);
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    TtsConfigService ttsConfigService;
    @Autowired
    private ListableBeanFactory beanFactory;

    private List<TextNotifyService> getNotifyServices() {
        List<TextNotifyService> textNotifyServices = new ArrayList<>(NotifyTypeEnum.values().length);
        List<Integer> playbackOrderList = getPlaybackOrder();
        for (Integer playback : playbackOrderList) {
            NotifyTypeEnum notifyTypeEnum = NotifyTypeEnum.getNotifyTypeEnum(playback);
            textNotifyServices.add(beanFactory.getBean(notifyTypeEnum.getBeanName(), TextNotifyService.class));
        }
        return textNotifyServices;
    }

    /**
     * 获取TTS语音的播报顺序
     * @return {@link List }<{@link Integer }> 播放顺序 1 告警 2 预警 3 站内信
     */
    private List<Integer> getPlaybackOrder() {
        return Optional.ofNullable(ttsConfigService.findByKey(TtsConfigEnum.TTS_PLAYBACK_ORDER.getTtsConfigKey()))
                       // 如果配置项为空，返回默认播放顺序
                       .map(TtsConfig::getTtsConfigValue)
                       .map(String::trim)
                       .filter(CharSequenceUtil::isNotBlank)  // 确保配置值非空
                       .map(StringUtils::splitToIntegerList)  // 分割并转换为整数列表
                       .orElse(DEFAULT_PLAYBACK_ORDER);  // 若配置为空，则使用默认值
    }

    @Scheduled(fixedDelay = 60 * 1000)
    public void healthCheck() {
        //180秒没有再次请求则清理
        long expiryTime = System.currentTimeMillis() - (HEALTH_INTERVAL * 1000);
        Set<String> expiredUsers = redisTemplate.opsForZSet()
                                                .rangeByScore(GlobalConstants.REQUEST_MESSAGE_TIME, 0, expiryTime);

        if (CollUtil.isEmpty(expiredUsers)) {
            return;
        }
        List<String> expiredUsersList = new ArrayList<>(expiredUsers);
        getNotifyServices().parallelStream()
                           .forEach(service -> service.processAfterDisconnection(expiredUsersList));

        redisTemplate.opsForZSet()
                     .remove(GlobalConstants.REQUEST_MESSAGE_TIME, expiredUsersList.toArray());

        log.info("已清理断开连接的用户缓存信息, uniqueIds: {}", expiredUsersList);
    }

    public Object getCurrentTtsMsg(TtsMessageRequestDTO request) {
        putLastRequestTime(request.getUserId(), request.getSessionId());
        Object currentTtsMsg = null;
        for (TextNotifyService stringTextNotifyServiceEntry : getNotifyServices()) {
            currentTtsMsg = stringTextNotifyServiceEntry.getCurrentTtsMsg(request);
            if (currentTtsMsg != null) {
                break;
            }
        }
        return currentTtsMsg;
    }

    private void putLastRequestTime(Integer userId, String sessionId) {
        String uniqueId = getUniqueId(userId, sessionId);
        long currentTime = System.currentTimeMillis();

        Boolean isFirst = redisTemplate.opsForZSet()
                                       .addIfAbsent(GlobalConstants.REQUEST_MESSAGE_TIME, uniqueId, currentTime);

        if (Boolean.TRUE.equals(isFirst)) {
            log.info("新用户首次连接TTS语音, uniqueId: {}", uniqueId);
            getNotifyServices().parallelStream().forEach(service -> service.processAfterConnection(userId, sessionId));
        }

        redisTemplate.opsForZSet()
                     .add(GlobalConstants.REQUEST_MESSAGE_TIME, uniqueId, currentTime);
    }

    public boolean continueBroadcast(TtsContinueBroadcastRequestDTO request) {
        return findTextNotifyServiceByNotifyType(request.getNotifyType()).continueBroadcast(request);
    }

    public List<String> getUserTtsMsgByType(Integer notifyType, Integer userId, String sessionId) {
        return findTextNotifyServiceByNotifyType(notifyType).getUserTtsMsg(userId, sessionId);
    }

    public String getUniqueId(Integer userId, String sessionId) {
        return userId + ":" + sessionId;
    }

    public TextNotifyService findTextNotifyServiceByNotifyType(Integer notifyType) {
        NotifyTypeEnum notifyTypeEnum = NotifyTypeEnum.getNotifyTypeEnum(notifyType);
        return beanFactory.getBean(notifyTypeEnum.getBeanName(), TextNotifyService.class);
    }

    public boolean clearUserTtsMsgByType(Integer notifyType, Integer userId, String sessionId) {
        TextNotifyService textNotifyServiceByNotifyType = findTextNotifyServiceByNotifyType(notifyType);
        return textNotifyServiceByNotifyType.clearMsg(userId, sessionId);
    }
}

