package com.siteweb.eventnotification.textnotify.alarmtextnotify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.eventnotification.dto.AlarmTextNotifyDTO;
import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;
import com.siteweb.eventnotification.entity.TtsConfig;
import com.siteweb.eventnotification.enums.TtsConfigEnum;
import com.siteweb.eventnotification.service.TtsConfigService;
import com.siteweb.eventnotification.textnotify.AbstractTextNotifyService;
import com.siteweb.eventnotification.textnotify.NotifyTypeEnum;
import com.siteweb.eventnotification.textnotify.TextNotifyDTO;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhou
 * @description AlarmTextNotifyWebSocketService
 * @createTime 2022-05-25 16:01:22
 */
@Service
@Slf4j
public class AlarmTextNotifyHttpService extends AbstractTextNotifyService<AlarmChange> implements ApplicationListener<BaseSpringEvent<AlarmChange>> {
    @Value("${byteDance.eventLevelFilterEnable:false}")
    private boolean isEnable;
    @Autowired
    private AlarmTextNotifyService alarmTextNotifyService;
    @Autowired
    private ActiveEventManager activeEventManager;
    @Autowired
    private TtsConfigService ttsConfigService;
    @Autowired
    private HAStatusService haStatusService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    RegionService regionService;

    @Override
    public NotifyTypeEnum getNotifyType() {
        return NotifyTypeEnum.ALARM_TEXT_NOTIFY;
    }

    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP");
            return;
        }
        //字节走另外一套TTS逻辑 com.siteweb.tts.listencer.TtsListener
        if (isEnable) {
            return;
        }
        this.addEventChangeMsg(event.getData());
    }

    @Override
    public void processAfterConnection(Integer userId, String sessionId) {
        firstPushData(userId, sessionId);
    }

    /**
     * 首次连接tts需要发送活动告警
     */
    private void firstPushData(Integer userId, String sessionId) {
        if (alarmTextNotifyService.enableFirstPushAlarm()) {
            return;
        }

        List<ActiveEventDTO> alarmTextNotifyList = alarmTextNotifyService.getTtsActiveEvent(userId, Comparator.comparing(ActiveEventDTO::getEventLevel).reversed().thenComparing(ActiveEventDTO::getStartTime))
                                                                         .stream()
                                                                         .limit(TTS_MAX_CAPACITY)
                                                                         .toList();

        String uniqueId = getUniqueId(userId, sessionId);
        List<AlarmChange> alarmChangeList = alarmTextNotifyList.stream()
                                                               .map(ActiveEventDTO::alarmChange)
                                                               .toList();
        batchPushMsg(uniqueId, alarmChangeList);
    }

    @Override
    public TextNotifyDTO<AlarmTextNotifyDTO> getCurrentTtsMsg(TtsMessageRequestDTO ttsMessageRequestDTO) {
        //先移除已经播报完的消息，避免到时候重复播报
        removeBroadcastMessages(ttsMessageRequestDTO);
        AlarmChange currentUserEvent = getCurrentUserEvent(ttsMessageRequestDTO.getUserId(), ttsMessageRequestDTO.getSessionId(), ttsMessageRequestDTO.getSequenceId(), ttsMessageRequestDTO.getOperationType());

        if (Objects.isNull(currentUserEvent)) {
            return null;
        }

        AlarmTextNotifyDTO data = alarmTextNotifyService.getAlarmTextNotifyByActiveEvent(currentUserEvent);
        return new TextNotifyDTO<>(getNotifyType(), data);
    }

    /**
     * 先移除已经播报完的消息，避免到时候重复播报，主要针对如下情况
     * 当某条告警进入队列开始播放，但未播放完成时，又有新告警进来，新告警会排在队列最前，等旧告警播完，接着播放新告警，此时旧告警并没有被移除，要等新告警播放完后，新告警先从队列中移除，再播放一次旧告警，才会移除掉旧告警。
     * 简言之就是一条告警播放还未完成（未从队列中清除），又有新告警插入，这条告警会被重复播放。如果连续有插入的新告警，重复播放的次数会更多
     * @param ttsMessageRequestDTO TTS消息请求DTO
     */
    private void removeBroadcastMessages(TtsMessageRequestDTO ttsMessageRequestDTO) {
        String messageKey = getNotifyType().getRedisKeyPrefix() + getUniqueId(ttsMessageRequestDTO.getUserId(), ttsMessageRequestDTO.getSessionId());

        List<AlarmChange> allMessagesFromQueue = getAllMessagesFromQueue(messageKey, AlarmChange.class);

        List<AlarmChange> needRemoveMessage = allMessagesFromQueue.stream()
                                                                  .filter(alarmChange -> Objects.equals(alarmChange.getSequenceId(), ttsMessageRequestDTO.getSequenceId()) && Objects.equals(alarmChange.getOperationType(), ttsMessageRequestDTO.getOperationType()))
                                                                  .toList();
        removeMessage(messageKey, needRemoveMessage);
    }

    @Override
    protected List<String> extractMessageTexts(Set<String> messageList) {
        return messageList.stream()
                          .map(message -> {
                              AlarmChange alarmChange = JSONUtil.toBean(JSONUtil.toJsonStr(message), AlarmChange.class);
                              AlarmTextNotifyDTO data = alarmTextNotifyService.getAlarmTextNotifyByActiveEvent(alarmChange);
                              return data.getText();
                          })
                          .toList();
    }

    @Override
    protected double calculateScore(AlarmChange alarmChange) {
        if (alarmTextNotifyService.enableSortByEventLevel()) {
            // 告警等级越高越优先
            return alarmChange.getEventLevel();
        }
        // 越先来的消息越先播放
        return -System.currentTimeMillis();
    }

    @Override
    public boolean continueBroadcast(TtsContinueBroadcastRequestDTO ttsContinueBroadcastRequestDTO) {
        ActiveEventDTO activeEventDTO = activeEventManager.getActiveEventDTOBySequenceId(
                ttsContinueBroadcastRequestDTO.getSequenceId()
        );

        if (Objects.isNull(activeEventDTO)) {
            return false;
        }

        // 已确认不播报
        if (Objects.nonNull(activeEventDTO.getConfirmTime()) && alarmTextNotifyService.enableConfirmNoBroadcast()) {
            return false;
        }

        // 已结束不播报
        if (Objects.nonNull(activeEventDTO.getEndTime()) && alarmTextNotifyService.enableEndNoBroadcast()) {
            return false;
        }

        return true;
    }

    /**
     * 添加变更告警的消息
     */
    private void addEventChangeMsg(AlarmChange alarmChange) {
        // 符合条件才加入tts语音队列
        if (isFilter(alarmChange)) {
            return;
        }

        Set<String> lastRequestTimeSet = redisTemplate.opsForZSet().range(GlobalConstants.REQUEST_MESSAGE_TIME, 0, -1);

        if (CollUtil.isEmpty(lastRequestTimeSet)) {
            return;
        }

        for (String uniqueId : lastRequestTimeSet) {
            String[] uniqueIdArray = uniqueId.split(":");
            int userId = Integer.parseInt(uniqueIdArray[0]);

            // 判断当前登录用户ID及设备权限
            if (!regionService.hasRegionMapPermissions(userId,alarmChange.getResourceStructureId(),alarmChange.getEquipmentId())) {
                continue;
            }

            pushMsg(uniqueId, alarmChange);
        }
    }

    /**
     * 获取当前用户需要播报的消息
     */
    private AlarmChange getCurrentUserEvent(Integer userId, String sessionId, String sequenceId, Integer operationType)
    {
        AlarmChange latestEvent = getLatestEvent(getUniqueId(userId, sessionId), sequenceId, operationType);

        if (ObjectUtil.isNotNull(latestEvent)) {
            log.info("uniqueId:{},获取最新队列里的告警信息{}", getUniqueId(userId, sessionId), latestEvent);
            return latestEvent;
        }

        ActiveEventDTO activeEventDTO = getCircularBroadcastEvent(userId, sequenceId);
        if (ObjectUtil.isNotNull(activeEventDTO)) {
            log.info("uniqueId:{},获取循环播报里的告警信息{}", getUniqueId(userId, sessionId), activeEventDTO);
            return activeEventDTO.alarmChange();
        }

        return null;
    }

    /**
     * 获取最新事件，并处理消息队列
     */
    private AlarmChange getLatestEvent(String uniqueId, String sequenceId, Integer operationType) {
        String redisKey = getNotifyType().getRedisKeyPrefix() + uniqueId;

        while (true) {
            //获取队列中分值最小的元素
            AlarmChange alarmChange = getFirstMessageFromQueue(redisKey, AlarmChange.class);

            // 队列为空，直接返回
            if (alarmChange == null) {
                return null;
            }

            // 判断是否需要弹出并跳过当前消息
            if (isPop(alarmChange, sequenceId, operationType)) {
                redisTemplate.opsForZSet().remove(redisKey, JSONUtil.toJsonStr(alarmChange));
                continue;
            }

            return alarmChange;
        }
    }

    /**
     * 判断是否需要弹出消息
     */
    private boolean isPop(AlarmChange alarmChange, String sequenceId, Integer operationType) {
        // 消息被确认，pop当前消息，重新获取下一条消息
        if (Objects.equals(alarmChange.getSequenceId(), sequenceId) &&
                Objects.equals(alarmChange.getOperationType(), operationType)) {
            return true;
        }

        // 告警的翻转次数 > 0，就不播报
        if (Objects.nonNull(alarmChange.getReversalNum()) && alarmChange.getReversalNum() > 0) {
            return true;
        }

        // 告警已经结束了
        ActiveEventDTO activeEventDTOBySequenceId = activeEventManager.getActiveEventDTOBySequenceId(alarmChange.getSequenceId());
        if (activeEventDTOBySequenceId == null) {
            return true;
        }

        // 告警已经被确认并且开启了确认不播报
        if (Objects.nonNull(activeEventDTOBySequenceId.getConfirmTime()) && alarmTextNotifyService.enableConfirmNoBroadcast()) {
            return true;
        }

        // 告警已经结束并且开启了结束不播报
        return Objects.nonNull(activeEventDTOBySequenceId.getEndTime()) && alarmTextNotifyService.enableEndNoBroadcast();
    }

    /**
     * 获取循环播报的消息
     */
    private ActiveEventDTO getCircularBroadcastEvent(Integer userId, String sequenceId) {
        // 是否开启活动告警循环播报
        if (!alarmTextNotifyService.enableCircularBroadcast()) {
            return null;
        }

        List<ActiveEventDTO> alarmTextNotifyStream = alarmTextNotifyService.getTtsActiveEvent(
                userId,
                Comparator.comparing(ActiveEventDTO::getEventLevel)
                          .thenComparing(ActiveEventDTO::getStartTime, Comparator.reverseOrder())
        );

        if (CollUtil.isEmpty(alarmTextNotifyStream)) {
            return null;
        }

        int index = CollUtil.indexOf(alarmTextNotifyStream, m -> Objects.equals(m.getSequenceId(), sequenceId));

        // 没找到或者找到了并且是在队尾
        if (index == -1 || alarmTextNotifyStream.size() <= index + 1) {
            return alarmTextNotifyStream.get(0);
        }

        return alarmTextNotifyStream.get(index + 1);
    }

    /**
     * 告警过滤逻辑
     */
    private boolean isFilter(AlarmChange alarmChange) {
        List<TtsConfig> ttsConfigList = ttsConfigService.findByKeys(TtsConfigEnum.getFilterConditionKeys());

        Map<String, HashSet<String>> filterConditionRule = ttsConfigList.stream()
                                                                        .filter(rule -> CharSequenceUtil.isNotBlank(rule.getTtsConfigValue()))
                                                                        .collect(Collectors.toMap(
                                                                                TtsConfig::getTtsConfigKey,
                                                                                rule -> new HashSet<>(CharSequenceUtil.split(rule.getTtsConfigValue(), ","))
                                                                        ));

        // 判断各种条件规则
        for (Map.Entry<String, HashSet<String>> ruleEntry : filterConditionRule.entrySet()) {
            // 位置、告警等级、告警状态等过滤逻辑
            if (checkFilterCondition(ruleEntry, alarmChange)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查过滤条件
     */
    private boolean checkFilterCondition(Map.Entry<String, HashSet<String>> ruleEntry, AlarmChange alarmChange) {
        // 各种过滤条件的判断
        return isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_POSITION, alarmChange.getResourceStructureId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.NOTIFICATION_HOST_ALARM_SEVERITY_SYSTEM_CONFIG_KEY, alarmChange.getEventLevel()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.ALARM_NOTIFICATION_STATUS, alarmChange.getOperationType()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_BASE_TYPE, alarmChange.getBaseTypeId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_BASE_EQUIPMENT, alarmChange.getBaseEquipmentId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_EVENT, alarmChange.getEventId()) ||
                isFilterByKey(ruleEntry, TtsConfigEnum.TTS_FILTER_EQUIPMENT, alarmChange.getEquipmentId()) ||
                checkProjectStatus(ruleEntry, alarmChange) ||
                checkKeywordFilter(ruleEntry, alarmChange);
    }

    /**
     * 通用的过滤方法
     */
    private boolean isFilterByKey(Map.Entry<String, HashSet<String>> ruleEntry, TtsConfigEnum configEnum, Object value) {
        return Objects.equals(ruleEntry.getKey(), configEnum.getTtsConfigKey()) && Objects.nonNull(value) && !ruleEntry.getValue().contains(String.valueOf(value));
    }

    /**
     * 工程状态过滤
     */
    private boolean checkProjectStatus(Map.Entry<String, HashSet<String>> ruleEntry, AlarmChange alarmChange) {
        return Objects.equals(ruleEntry.getKey(), TtsConfigEnum.TTS_PROJECT_STATUS_ENABLE.getTtsConfigKey()) && !isMaintenanceStatusMatching(alarmChange.getMaintainState(), ruleEntry.getValue());
    }

    /**
     * 关键字过滤
     */
    private boolean checkKeywordFilter(Map.Entry<String, HashSet<String>> ruleEntry, AlarmChange alarmChange) {
        return Objects.equals(ruleEntry.getKey(), TtsConfigEnum.TTS_FILTER_KEYWORD.getTtsConfigKey()) && isKeywordFilter(ruleEntry.getValue(), alarmChange);
    }

    /**
     * 关键字过滤详细实现
     */
    private boolean isKeywordFilter(HashSet<String> keywords, AlarmChange alarmChange) {
        String keyWord = CollUtil.join(keywords, "");

        if (CharSequenceUtil.isBlank(keyWord)) {
            return false;
        }

        String fullResourceStructureName = resourceStructureManager.getFullPath(alarmChange.getResourceStructureId());
        String operationTypeName = AlarmOperationTypeEnum.getOperationTypeNameByValue(alarmChange.getOperationType());

        return !(
                (fullResourceStructureName != null && fullResourceStructureName.contains(keyWord)) || // 位置
                        (alarmChange.getBaseEquipmentName() != null && alarmChange.getBaseEquipmentName().contains(keyWord)) || // 设备基类
                        (alarmChange.getEquipmentName() != null && alarmChange.getEquipmentName().contains(keyWord)) || // 设备名称
                        (alarmChange.getBaseTypeName() != null && alarmChange.getBaseTypeName().contains(keyWord)) || // 告警基类
                        (alarmChange.getEventName() != null && alarmChange.getEventName().contains(keyWord)) || // 告警/事件
                        (alarmChange.getEventSeverity() != null && alarmChange.getEventSeverity().contains(keyWord)) || // 告警等级
                        (operationTypeName != null && operationTypeName.contains(keyWord)) // 事件状态
        );
    }

    /**
     * 维护状态匹配
     */
    private boolean isMaintenanceStatusMatching(Integer maintainState, HashSet<String> maintainStateSet) {
        boolean isProjectEnabled = maintainStateSet.contains("true");
        boolean isInMaintainState = !Objects.equals(1, maintainState);

        return (isProjectEnabled && isInMaintainState) || (!isProjectEnabled && !isInMaintainState);
    }
}

