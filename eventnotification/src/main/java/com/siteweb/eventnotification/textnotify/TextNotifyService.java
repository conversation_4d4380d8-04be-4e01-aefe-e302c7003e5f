package com.siteweb.eventnotification.textnotify;

import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;

import java.util.List;

public interface TextNotifyService {

    NotifyTypeEnum getNotifyType();

    /**
     * 首次连接后需要触发的事件
     * 目前是同步串行 有需要可以异步并行
     * @param userId 用户id
     * @param sessionId 事件id
     */
    void processAfterConnection(Integer userId, String sessionId);

    /**
     * 断开连接后需要触发的事件
     * 目前是同步串行 有需要可以异步并行
     *
     * @param onlineKeys 在线的用户会话唯一id
     */
    void processAfterDisconnection(List<String> onlineKeys);

    Object getCurrentTtsMsg(TtsMessageRequestDTO ttsMessageRequestDTO);

    /**
     * 是否继续播报
     * @param ttsContinueBroadcastRequestDTO 语音继续播报的请求体
     * @return boolean true 是 false 否
     */
    boolean continueBroadcast(TtsContinueBroadcastRequestDTO ttsContinueBroadcastRequestDTO);

    /**
     * 获取用户的TTS消息
     *
     * @param userId    用户id
     * @param sessionId
     * @return {@link List }<{@link String }>
     */
    List<String> getUserTtsMsg(Integer userId, String sessionId);

    boolean clearMsg(Integer userId, String sessionId);
}
