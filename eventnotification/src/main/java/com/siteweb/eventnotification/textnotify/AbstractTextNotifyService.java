package com.siteweb.eventnotification.textnotify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public abstract class AbstractTextNotifyService<T> implements TextNotifyService {
    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    /**
     * TTS语音的最大容量
     */
    protected static final int TTS_MAX_CAPACITY = 100;

    /**
     * 获取唯一标识
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 唯一标识
     */
    protected String getUniqueId(Integer userId, String sessionId) {
        return userId + ":" + sessionId;
    }

    /**
     * 批量推送消息
     * @param uniqueId 唯一标识
     * @param messageList 消息列表
     */
    protected void batchPushMsg(String uniqueId, List<T> messageList) {
        if (CollUtil.isEmpty(messageList)) {
            return;
        }
        String redisKey = getNotifyType().getRedisKeyPrefix() + uniqueId;
        Long count = redisTemplate.opsForZSet().size(redisKey);

        if (Objects.nonNull(count) && count + messageList.size() >= TTS_MAX_CAPACITY) {
            messageList = messageList.stream().limit(TTS_MAX_CAPACITY - count).toList();
        }

        Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>(messageList.size());
        for (T message : messageList) {
            tuples.add(new DefaultTypedTuple<>(JSONUtil.toJsonStr(message), calculateScore(message)));
        }

        redisTemplate.opsForZSet().add(redisKey, tuples);
    }

    /**
     * 推送单条消息
     * @param uniqueId 唯一标识
     * @param message 消息
     */
    public void pushMsg(String uniqueId, T message) {
        String redisKey = getNotifyType().getRedisKeyPrefix() + uniqueId;
        Long count = redisTemplate.opsForZSet().size(redisKey);

        if (Objects.nonNull(count) && count >= TTS_MAX_CAPACITY) {
            log.info("uniqueId:{},数量超过:{},消息被丢弃", uniqueId, count);
            return;
        }

        redisTemplate.opsForZSet().add(redisKey, JSONUtil.toJsonStr(message), calculateScore(message));
    }


    /**
     * 从队列获取所有消息
     *
     * @param redisKey Redis键
     * @param clazz    目标类型
     * @return 消息列表，无数据时返回空列表
     */
    protected List<T> getAllMessagesFromQueue(String redisKey, Class<T> clazz) {
        Set<ZSetOperations.TypedTuple<String>> tuples = fetchTuplesFromRedis(redisKey, 0, -1);
        return CollUtil.isEmpty(tuples)
                ? Collections.emptyList()
                : tuples.stream()
                        .map(tuple -> convertToBean(tuple, clazz))
                        .filter(Objects::nonNull)
                        .toList();
    }

    /**
     * 获取队列中的第一条消息
     *
     * @param redisKey Redis键
     * @param clazz    目标类型
     * @return 第一条消息，无数据时返回null
     */
    protected T getFirstMessageFromQueue(String redisKey, Class<T> clazz) {
        Set<ZSetOperations.TypedTuple<String>> tuples = fetchTuplesFromRedis(redisKey, 0, 0);
        return CollUtil.isEmpty(tuples)
                ? null
                : tuples.stream()
                        .findFirst()
                        .map(tuple -> convertToBean(tuple, clazz))
                        .orElse(null);
    }

    /**
     * 从Redis获取指定范围的有序集合元素
     *
     * @param redisKey Redis键
     * @param start    起始索引
     * @param end      结束索引
     * @return 有序集合元素
     */
    private Set<ZSetOperations.TypedTuple<String>> fetchTuplesFromRedis(String redisKey, long start, long end) {
        try {
            return redisTemplate.opsForZSet().rangeWithScores(redisKey, start, end);
        } catch (Exception e) {
            log.error("Failed to fetch tuples from Redis key: {}, range: [{} to {}]", redisKey, start, end, e);
            return Collections.emptySet();
        }
    }

    /**
     * 将Redis数据转换为目标Bean对象
     *
     * @param tuple Redis数据元组
     * @param clazz 目标类型
     * @return 转换后的Bean对象，转换失败时返回null
     */
    private T convertToBean(ZSetOperations.TypedTuple<String> tuple, Class<T> clazz) {
        try {
            String value = tuple.getValue();
            return value != null ? JSONUtil.toBean(value, clazz) : null;
        } catch (Exception e) {
            log.warn("Failed to convert value to bean of type {}: {}", clazz.getSimpleName(), e.getMessage());
            return null;
        }
    }

    protected void removeMessage(String redisKey, List<T> needRemoveMessage) {
        if (CollUtil.isEmpty(needRemoveMessage)) {
            return;
        }
        List<String> messageList = needRemoveMessage.stream().map(JSONUtil::toJsonStr).toList();
        redisTemplate.opsForZSet().remove(redisKey, messageList.toArray());
    }

    /**
     * 处理断开连接事件
     * @param onlineKeys 在线用户Key
     */
    @Override
    public void processAfterDisconnection(List<String> onlineKeys) {
        List<String> broadcastKey = new ArrayList<>();
        for (String requestOnlineKey : onlineKeys) {
            broadcastKey.add(getNotifyType().getRedisKeyPrefix() + requestOnlineKey);
        }

        redisTemplate.delete(broadcastKey);
        log.info("清理{}TTS消息:{}", getNotifyType().name(), broadcastKey);
    }

    /**
     * 获取用户TTS消息列表
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 消息列表
     */
    @Override
    public List<String> getUserTtsMsg(Integer userId, String sessionId) {
        String messageKey = getNotifyType().getRedisKeyPrefix() + getUniqueId(userId, sessionId);
        Set<String> messageList = redisTemplate.opsForZSet().range(messageKey, 0, -1);

        if (CollUtil.isEmpty(messageList)) {
            return Collections.emptyList();
        }

        return extractMessageTexts(messageList);
    }

    @Override
    public boolean clearMsg(Integer userId, String sessionId) {
        Boolean delete = redisTemplate.delete(getNotifyType().getRedisKeyPrefix() + getUniqueId(userId, sessionId));
        return Boolean.TRUE.equals(delete);
    }

    /**
     * 提取消息文本，由子类实现
     * @param messageList 消息列表
     * @return 文本列表
     */
    protected abstract List<String> extractMessageTexts(Set<String> messageList);

    /**
     * 计算分数，由子类实现
     * @param message 消息
     * @return 分数
     */
    protected abstract double calculateScore(T message);
}

