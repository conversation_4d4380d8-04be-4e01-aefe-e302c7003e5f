package com.siteweb.eventnotification.job;

import com.siteweb.eventnotification.entity.SafeMessage;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: liaoximing
 * @Date: 2022/6/13 15:26
 */
@Component
public class SafeMessageJobManager {
    @Autowired
    SchedulerJobService schedulerService;

    private static final String JOB_GROUP = "SafeMessageJob";

    public void addJob(SafeMessage safeMessage) throws ClassNotFoundException, InstantiationException, SchedulerException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        if(safeMessage == null){
            return;
        }
        SchedulerJob schedulerJob = getJobFromSafeMessageTaskManagement(safeMessage);
        schedulerService.addSchedulerJob(schedulerJob);
    }

    public void updateJob(SafeMessage safeMessage) throws ClassNotFoundException, InstantiationException, SchedulerException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        if(safeMessage == null){
            return;
        }
        SchedulerJob schedulerJob = getJobFromSafeMessageTaskManagement(safeMessage);
        schedulerService.updateSchedulerJob(schedulerJob);
    }

    public void deleteJob(SafeMessage safeMessage) throws SchedulerException {
        if(safeMessage == null){
            return;
        }
        SchedulerJob schedulerJob = getJobFromSafeMessageTaskManagement(safeMessage);
        schedulerService.removeSchedulerJob(schedulerJob);
    }

    private SchedulerJob getJobFromSafeMessageTaskManagement(SafeMessage safeMessage) {
        SchedulerJob schedulerJob = new SchedulerJob();
        schedulerJob.setClassName(SafeMessageJob.class.getName());
        schedulerJob.setCronExpression(safeMessage.getCron());
        schedulerJob.setJobGroup(JOB_GROUP);
        schedulerJob.setJobName(JOB_GROUP + safeMessage.getSafeMessageId());
        Map<String, Object> params = new HashMap<>();
        params.put("safeMessageId", safeMessage.getSafeMessageId());//配置主键
        schedulerJob.setParams(params);
        return schedulerJob;
    }
}
