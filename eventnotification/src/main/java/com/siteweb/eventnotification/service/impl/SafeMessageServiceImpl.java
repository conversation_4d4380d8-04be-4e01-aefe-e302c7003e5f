package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.entity.SafeMessage;
import com.siteweb.eventnotification.entity.SafeMessageElementConfig;
import com.siteweb.eventnotification.job.SafeMessageJobManager;
import com.siteweb.eventnotification.mapper.SafeMessageMapper;
import com.siteweb.eventnotification.service.SafeMessageElementConfigService;
import com.siteweb.eventnotification.service.SafeMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class SafeMessageServiceImpl implements SafeMessageService {
    @Autowired
    private SafeMessageMapper safeMessageMapper;
    @Autowired
    private SafeMessageElementConfigService safeMessageElementConfigService;
    @Autowired
    private SafeMessageJobManager safeMessageJobManager;
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Override
    public List<SafeMessage> findAll() {
        return safeMessageMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public SafeMessage findById(Integer id) {
        SafeMessage safeMessage = safeMessageMapper.selectById(id);
        if (Objects.isNull(safeMessage)) {
            return null;
        }
        List<SafeMessageElementConfig> safeMessageElementConfigList = safeMessageElementConfigService.findBySafeMessageId(safeMessage.getSafeMessageId());
        safeMessage.setSafeMessageElementConfigList(safeMessageElementConfigList);
        return safeMessage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SafeMessage deleteById(Integer id) {
        SafeMessage safeMessage = this.findById(id);
        if (ObjectUtil.isNull(safeMessage)) {
            return null;
        }
        safeMessageMapper.deleteById(id);
        safeMessageElementConfigService.deleteBySafeMessageId(id);
        try {
            safeMessageJobManager.deleteJob(safeMessage);
        } catch (Exception e) {
            log.error("定时任务异常：{} ：{}", localeMessageSourceUtil.getMessage("common.timeJobMsg.removeJobException"), ExceptionUtil.stacktraceToString(e));
        }
        return safeMessage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer createSafeMessage(SafeMessage safeMessage) {
        String cron = this.analysisCron(safeMessage);
        safeMessage.setCron(cron);
        Integer result = safeMessageMapper.insert(safeMessage);
        safeMessageElementConfigService.batchInsert(safeMessage.getSafeMessageId(), safeMessage.getSafeMessageElementConfigList());
        try {
            safeMessageJobManager.addJob(safeMessage);
        } catch (Exception e) {
            log.error("定时任务异常：{}：{}", localeMessageSourceUtil.getMessage("common.timeJobMsg.addJobException"), ExceptionUtil.stacktraceToString(e));
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer updateSafeMessage(SafeMessage safeMessage) {
        String cron = this.analysisCron(safeMessage);
        safeMessage.setCron(cron);
        Integer result = safeMessageMapper.updateById(safeMessage);
        safeMessageElementConfigService.batchInsert(safeMessage.getSafeMessageId(), safeMessage.getSafeMessageElementConfigList());
        try {
            safeMessageJobManager.updateJob(safeMessage);
        } catch (Exception e) {
            log.error("定时任务异常：{}：{}", localeMessageSourceUtil.getMessage("common.timeJobMsg.updateJobException"), ExceptionUtil.stacktraceToString(e));
        }
        return result;
    }

    /**
     * 解析corn表达式
     * @param safeMessage 平安短信
     * @return {@link String}
     */
    private String analysisCron(SafeMessage safeMessage){
        //拼接corn
        StringBuilder sb = new StringBuilder();
        int hour = safeMessage.getSendTime().getHour();
        int minute = safeMessage.getSendTime().getMinute();
        sb.append("0 ");//秒
        sb.append(minute + " ");//分
        sb.append(hour + " ");//时
        switch (safeMessage.getSendType()) {
            case 1 -> //每天都发送
               sb.append("* * ? *");
            case 2 -> { //每周n次
                sb.append("? * ");//天 月
                sb.append(safeMessage.getSendTypeDescription()); //周 星期几
            }
            case 3 -> { //每月n次
                sb.append(safeMessage.getSendTypeDescription() + " ");//日
                sb.append("* ? *");//月 周 年
            }
        }
        return sb.toString();
    }
}
