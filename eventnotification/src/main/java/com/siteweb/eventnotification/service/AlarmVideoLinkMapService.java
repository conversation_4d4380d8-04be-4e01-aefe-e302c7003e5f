package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmVideoLinkMapImportDTO;
import com.siteweb.eventnotification.dto.AlarmVideoLinkSnapshotMap;
import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;

import java.util.List;
import java.util.Map;

public interface AlarmVideoLinkMapService {
    List<AlarmVideoLinkMap> findByAlarmVideoLinkId(Integer alarmVideoLinkId);
    List<AlarmVideoLinkSnapshotMap> findMapByCondition(Integer equipmentId, Integer eventId, Integer eventConditionId);
    void deleteByAlarmVideoLinkId(Integer alarmVideoLinkId);
    void batchInsert(Integer alarmVideoLinkId, List<AlarmVideoLinkMap> alarmVideoLinkMapList);
    Map<String, Object> importAlarmVideoLinkMap(List<AlarmVideoLinkMapImportDTO> alarmVideoLinkMapImportDTOList);
    List<AlarmVideoLinkMapImportDTO> exportAlarmVideoLinkMap(List<AlarmVideoLinkMap> alarmVideoLinkMapList);
}
