package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmNotifyFilterRuleDTO;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterRuleService
 * @createTime 2022-04-24 08:58:55
 */
public interface AlarmNotifyFilterRuleService {

    List<AlarmNotifyFilterRuleDTO> batchSaveAlarmNotifyFilterRules(List<AlarmNotifyFilterRuleDTO> dtos);

    AlarmNotifyFilterRuleDTO findByFilterRuleId(Integer alarmNotifyFilterRuleId);

    List<AlarmNotifyFilterRuleDTO> findByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    void deleteByAlarmNotifyConfigId(Integer alarmNotifyConfigId);
}
