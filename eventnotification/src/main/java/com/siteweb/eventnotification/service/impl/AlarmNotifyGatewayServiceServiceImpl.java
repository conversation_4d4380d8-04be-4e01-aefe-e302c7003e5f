package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyElement;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayService;
import com.siteweb.eventnotification.mapper.*;
import com.siteweb.eventnotification.service.AlarmNotifyGatewayServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class AlarmNotifyGatewayServiceServiceImpl implements AlarmNotifyGatewayServiceService {

    @Autowired
    AlarmNotifyGatewayServiceMapper alarmNotifyGatewayServiceMapper;
    @Autowired
    AlarmNotifyElementMapper alarmNotifyElementMapper;

    @Override
    public List<AlarmNotifyGatewayService> findAlarmNotifyGatewayServicesByElementId(Integer elementId) {
        List<AlarmNotifyGatewayService> alarmNotifyGatewayServiceList = alarmNotifyGatewayServiceMapper.findAlarmNotifyGatewayServicesUsedStatusByElementId(elementId);
        Map<Integer, List<AlarmNotifyGatewayService>> groupby = alarmNotifyGatewayServiceList.stream().collect(Collectors.groupingBy(AlarmNotifyGatewayService::getAlarmNotifyGatewayServiceId));
        List<AlarmNotifyGatewayService> resultList = new ArrayList<>();
        for (List<AlarmNotifyGatewayService> list : groupby.values()) {
            AlarmNotifyGatewayService alarmNotifyGatewayService = list.get(0);
            Boolean usedStatus = list.stream().map(AlarmNotifyGatewayService::getUsedStatus).toList().contains(Boolean.TRUE);
            alarmNotifyGatewayService.setUsedStatus(usedStatus);
            resultList.add(alarmNotifyGatewayService);
        }
        return resultList;
    }

    @Override
    public int deleteAlarmNotifyGatewayServiceByAlarmNotifyGatewayServiceIds(String alarmNotifyGatewayServiceIds) {
        List<Integer> alarmNotifyGatewayServiceIdList = CharSequenceUtil.split(alarmNotifyGatewayServiceIds, ",")
                .stream()
                .map(Integer::valueOf)
                .toList();
        return alarmNotifyGatewayServiceMapper.delete(Wrappers.lambdaQuery(AlarmNotifyGatewayService.class)
                .in(AlarmNotifyGatewayService::getAlarmNotifyGatewayServiceId, alarmNotifyGatewayServiceIdList));
    }


    @Override
    public List<AlarmNotifyGatewayService> findAllAlarmNotifyGatewayService() {
        return alarmNotifyGatewayServiceMapper.findAllAlarmNotifyGatewayService();
    }

    @Override
    public AlarmNotifyGatewayService createAlarmNotifyGatewayService(AlarmNotifyGatewayService alarmNotifyGatewayService) {
        alarmNotifyGatewayServiceMapper.insert(alarmNotifyGatewayService);
        AlarmNotifyElement alarmNotifyElement = alarmNotifyElementMapper.selectById(alarmNotifyGatewayService.getElementId());
        alarmNotifyGatewayService.setElementName(alarmNotifyElement.getElementName());
        return alarmNotifyGatewayService;
    }

    @Override
    public boolean exists(AlarmNotifyGatewayService alarmNotifyGatewayService) {
        return alarmNotifyGatewayServiceMapper.exists(Wrappers.lambdaQuery(AlarmNotifyGatewayService.class)
                .ne(ObjectUtil.isNotNull(alarmNotifyGatewayService.getAlarmNotifyGatewayServiceId()), AlarmNotifyGatewayService::getAlarmNotifyGatewayServiceId, alarmNotifyGatewayService.getAlarmNotifyGatewayServiceId())
                .eq(AlarmNotifyGatewayService::getGatewayServiceUrl, alarmNotifyGatewayService.getGatewayServiceUrl())
                .eq(AlarmNotifyGatewayService::getElementId, alarmNotifyGatewayService.getElementId()));
    }

    @Override
    public AlarmNotifyGatewayService updateAlarmNotifyGatewayServiceByAlarmNotifyGatewayServiceId(AlarmNotifyGatewayService alarmNotifyGatewayService) {
        alarmNotifyGatewayServiceMapper.updateById(alarmNotifyGatewayService);
        return alarmNotifyGatewayService;
    }

    @Override
    public boolean getUsedStatus(String alarmNotifyGatewayServiceIds) {
        List<Integer> alarmNotifyGatewayServiceIdList = CharSequenceUtil.split(alarmNotifyGatewayServiceIds, ",")
                .stream()
                .map(Integer::valueOf)
                .toList();
        List<AlarmNotifyGatewayServiceConfigDetailDTO> alarmNotifyGatewayServiceConfigDetailDTOList = alarmNotifyGatewayServiceMapper.findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyGatewayServiceIds(alarmNotifyGatewayServiceIdList);
        List<Boolean> usedStatusList = alarmNotifyGatewayServiceConfigDetailDTOList.stream().map(AlarmNotifyGatewayServiceConfigDetailDTO::getUsedStatus).toList();
        return usedStatusList.contains(Boolean.TRUE);
    }

    @Override
    public Map<Integer, Boolean> getUsedStatusMap() {
        List<AlarmNotifyGatewayServiceConfigDetailDTO> alarmNotifyGatewayServiceConfigDetailDTOList = alarmNotifyGatewayServiceMapper.findAllAlarmNotifyGatewayServiceConfigDetailDTO();
        Map<Integer, List<AlarmNotifyGatewayServiceConfigDetailDTO>> groupby = alarmNotifyGatewayServiceConfigDetailDTOList.stream().collect(Collectors.groupingBy(AlarmNotifyGatewayServiceConfigDetailDTO::getElementId));
        Map<Integer, Boolean> resultMap = new HashMap<>();
        for (List<AlarmNotifyGatewayServiceConfigDetailDTO> list : groupby.values()) {
            Integer key = list.get(0).getAlarmNotifyGatewayServiceId();
            List<Boolean> usedStatusList = list.stream().map(AlarmNotifyGatewayServiceConfigDetailDTO::getUsedStatus).toList();
            Boolean value = usedStatusList.contains(Boolean.TRUE);
            resultMap.put(key, value);
        }
        return resultMap;
    }
}