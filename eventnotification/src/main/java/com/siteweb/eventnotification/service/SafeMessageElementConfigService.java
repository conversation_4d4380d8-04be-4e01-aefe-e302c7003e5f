package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.SafeMessageElementConfig;

import java.util.List;

public interface SafeMessageElementConfigService {
    /**
     * 根据安全短信配置id获取其元素配置
     * @param safeMessageId 安全短信配置id
     * @return {@link List}<{@link SafeMessageElementConfig}>
     */
    List<SafeMessageElementConfig> findBySafeMessageId(Integer safeMessageId);

    /**
     * 根据安全短信配置id删除其元素配置
     * @param safeMessageId 安全短信配置id
     * @return {@link List}<{@link SafeMessageElementConfig}>
     */
    void deleteBySafeMessageId(Integer safeMessageId);

    void batchInsert(Integer safeMessageId, List<SafeMessageElementConfig> safeMessageElementConfigList);
}
