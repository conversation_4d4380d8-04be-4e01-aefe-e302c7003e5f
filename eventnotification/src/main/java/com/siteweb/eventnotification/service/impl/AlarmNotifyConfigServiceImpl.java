package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.eventnotification.entity.AlarmNotifyConfig;
import com.siteweb.eventnotification.mapper.AlarmNotifyConfigMapper;
import com.siteweb.eventnotification.service.AlarmNotifyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyConfigServiceImpl
 * @createTime 2022-04-22 15:22:32
 */
@Service
public class AlarmNotifyConfigServiceImpl implements AlarmNotifyConfigService {

    @Autowired
    AlarmNotifyConfigMapper alarmNotifyConfigMapper;
    @Autowired
    DepartmentPermissionService departmentPermissionService;
    @Autowired
    EmployeeService employeeService;

    @Override
    public int createAlarmNotifyConfig(AlarmNotifyConfig alarmNotifyConfig) {
        Integer departmentId = employeeService.findDepartmentIdByEmployeeId(TokenUserUtil.getLoginUserId());
        alarmNotifyConfig.setDepartmentId(departmentId);
        return alarmNotifyConfigMapper.insert(alarmNotifyConfig);
    }

    @Override
    public AlarmNotifyConfig findByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        return alarmNotifyConfigMapper.selectById(alarmNotifyConfigId);
    }

    @Override
    public List<AlarmNotifyConfig> findAll() {
        return alarmNotifyConfigMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public int updateAlarmNotifyConfig(AlarmNotifyConfig alarmNotifyConfig) {
        return alarmNotifyConfigMapper.updateById(alarmNotifyConfig);
    }

    @Override
    @Transactional
    public int deleteAlarmNotifyConfigById(Integer alarmNotifyConfigId) {
        AlarmNotifyConfig alarmNotifyConfig = alarmNotifyConfigMapper.selectById(alarmNotifyConfigId);
        if (null == alarmNotifyConfig) {
            return -1;
        }
        //已启用的告警通知不允许删除
        if (Boolean.TRUE.equals(alarmNotifyConfig.getUsedStatus())) {
            return -2;
        }
        alarmNotifyConfigMapper.deleteAlarmNotifyConfigById(alarmNotifyConfigId);
        return 1;
    }

    @Override
    public List<AlarmNotifyConfig> findByUserId(Integer userId) {
        Set<Integer> departmentIds = departmentPermissionService.findDepartmentPermissionByUserId(userId);
        if (CollUtil.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
//      兼容DepartmentId为null
        if (departmentIds.contains(0)) {
            return alarmNotifyConfigMapper.selectList(Wrappers.lambdaQuery(AlarmNotifyConfig.class)
                    .in(AlarmNotifyConfig::getDepartmentId, departmentIds).or()
                    .isNull(AlarmNotifyConfig::getDepartmentId));
        }
        return alarmNotifyConfigMapper.selectList(Wrappers.lambdaQuery(AlarmNotifyConfig.class)
                .in(AlarmNotifyConfig::getDepartmentId, departmentIds));
    }
}
