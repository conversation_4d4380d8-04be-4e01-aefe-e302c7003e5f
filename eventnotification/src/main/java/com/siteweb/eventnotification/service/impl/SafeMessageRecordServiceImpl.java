package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.eventnotification.entity.SafeMessage;
import com.siteweb.eventnotification.entity.SafeMessageRecord;
import com.siteweb.eventnotification.mapper.SafeMessageRecordMapper;
import com.siteweb.eventnotification.service.SafeMessageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SafeMessageRecordServiceImpl implements SafeMessageRecordService {
    @Autowired
    SafeMessageRecordMapper safeMessageRecordMapper;

    @Override
    public List<SafeMessage> findTodayNoSendRecord(List<SafeMessage> safeMessageList) {
        if (CollUtil.isEmpty(safeMessageList)) {
            return new ArrayList<>();
        }
        List<Integer> safeMessageIds = safeMessageList.stream()
                                                      .map(SafeMessage::getSafeMessageId)
                                                      .toList();
        List<Integer> sendIds = safeMessageRecordMapper.findTodaySendRecord(safeMessageIds);
        return safeMessageList.stream()
                              .filter(safeMessage -> !sendIds.contains(safeMessage.getSafeMessageId()))
                              .toList();
    }

    @Override
    public void saveMessageRecord(Integer safeMessageId, String content, String receiver, String remark) {
        SafeMessageRecord safeMessageRecord = new SafeMessageRecord(safeMessageId, content, receiver, remark);
        safeMessageRecordMapper.insert(safeMessageRecord);
    }
}
