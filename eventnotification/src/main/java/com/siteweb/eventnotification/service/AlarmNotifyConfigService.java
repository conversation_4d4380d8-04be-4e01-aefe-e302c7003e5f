package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.AlarmNotifyConfig;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyConfigService
 * @createTime 2022-04-22 15:21:24
 */
public interface AlarmNotifyConfigService {

    int createAlarmNotifyConfig(AlarmNotifyConfig alarmNotifyConfig);

    AlarmNotifyConfig findByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    List<AlarmNotifyConfig> findAll();

    int updateAlarmNotifyConfig(AlarmNotifyConfig alarmNotifyConfig);

    int deleteAlarmNotifyConfigById(Integer alarmNotifyConfigId);

    List<AlarmNotifyConfig> findByUserId(Integer userId);
}
