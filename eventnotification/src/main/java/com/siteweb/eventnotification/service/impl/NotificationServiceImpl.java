package com.siteweb.eventnotification.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.eventnotification.entity.Notification;
import com.siteweb.eventnotification.entity.NotificationReceiver;
import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import com.siteweb.eventnotification.mapper.NotificationMapper;
import com.siteweb.eventnotification.service.NotificationReceiverService;
import com.siteweb.eventnotification.service.NotificationService;
import com.siteweb.eventnotification.vo.NotificationEquipmentCategoryVO;
import com.siteweb.eventnotification.vo.NotificationEventCategoryVO;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.manager.DataDictionaryManager;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2023/3/16 13:44
 */
@Service
public class NotificationServiceImpl implements NotificationService {
    @Autowired
    NotificationMapper notificationMapper;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    DataDictionaryManager dataDictionaryManager;
    @Autowired
    NotificationReceiverService notificationReceiverService;
    @Autowired
    SystemConfigService systemConfigService;
    @Override
    public Notification create(Notification notification) {
        return notificationMapper.insert(notification) > 0 ? notification : null;
    }

    @Override
    public Notification updateById(Notification notification) {
        return notificationMapper.updateById(notification) > 0 ? notification : null;
    }

    @Override
    public IPage<Notification> findListByPage(Pageable pageable, Integer readed) {
        Page<Notification> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        List<NotificationReceiver> receiverByReaded = notificationReceiverService.findReceiverByReaded(readed, loginUserId);
        Map<Integer, NotificationReceiver> noriticationReceiverMap = receiverByReaded.stream().collect(Collectors.toMap(NotificationReceiver::getNotificationId, e -> e));
        if (CollUtil.isEmpty(noriticationReceiverMap.keySet())) {
            return page;
        }
        LambdaQueryWrapper<Notification> wrapper = Wrappers.lambdaQuery(Notification.class);
        wrapper.in(Notification::getNotificationId, noriticationReceiverMap.keySet());
        wrapper.orderByDesc(Notification::getCreateTime);
        Page<Notification> notificationPage = notificationMapper.selectPage(page, wrapper);
        notificationPage.getRecords().forEach(e -> e.setNotificationReceivers(Collections.singletonList(noriticationReceiverMap.get(e.getNotificationId()))));
        return notificationPage;
    }

    @Override
    public List<NotificationEquipmentCategoryVO> findGetuiPushEquipmentCategory() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("notification.getui.allowEquipmentCategory");
        List<Integer> equipmentCategoryIds = Arrays.stream(systemConfig.getSystemConfigValue().split(",")).map(Integer::valueOf).toList();
        return notificationMapper.findEquipmentCategoryIds(equipmentCategoryIds);
    }

    @Override
    public List<NotificationEventCategoryVO> findGetuiPushEventCategory() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("notification.getui.allowEventCategory");
        List<Integer> eventCategoryIds = Arrays.stream(systemConfig.getSystemConfigValue().split(",")).map(Integer::valueOf).toList();
        return notificationMapper.findEventCategoryIds(eventCategoryIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Notification createAlarmNotification(MobileClientMap mobileClientMap, AlarmChange alarmChange, String requestSign, String pushTitle, String pushContent) {
        JSONObject notificationExtParam = new JSONObject();
        notificationExtParam.set("centerId", alarmChange.getCenterId());
        notificationExtParam.set("centerName", alarmChange.getCenterName());
        notificationExtParam.set("structureId", alarmChange.getStructureId());
        notificationExtParam.set("structureName", alarmChange.getStructureName());
        notificationExtParam.set("stationId", alarmChange.getStationId());
        notificationExtParam.set("stationName", alarmChange.getStationName());
        notificationExtParam.set("resourceStructureId", alarmChange.getResourceStructureId());
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(alarmChange.getResourceStructureId());
        String resourceStructureName = "";
        if (ObjectUtil.isNotEmpty(resourceStructure)) {
            resourceStructureName = resourceStructure.getResourceStructureName();
        }
        notificationExtParam.set("equipmentId", alarmChange.getEquipmentId());
        notificationExtParam.set("equipmentName", alarmChange.getEquipmentName());
        notificationExtParam.set("equipmentPosition", alarmChange.getCenterName() + "_" + alarmChange.getStructureName() + "_" + alarmChange.getStationName() + "_" + resourceStructureName);
        Notification notification = new Notification();
        notification.setExternalId(requestSign);
        notification.setPushed(false);
        notification.setCategory("alarmChange");
        notification.setTitle(pushTitle);
        String color = Optional.ofNullable(dataDictionaryManager.getEventSeverityByLevel(alarmChange.getEventLevel()))
                               .map(CoreEventSeverity::getDisplayColor).orElse("");
        notification.setColor(color);
        notification.setContent(pushContent);
        notification.setExtParam(notificationExtParam.toString());
        notification.setSender("system");
        notification.setCreateTime(new Date());
        notification = create(notification);

        NotificationReceiver notificationReceiver = new NotificationReceiver();
        notificationReceiver.setLoginUserId(mobileClientMap.getLoginUserId());
        notificationReceiver.setNotificationId(notification.getNotificationId());
        notificationReceiver.setReaded(false);
        notificationReceiver.setDeleted(false);
        notificationReceiverService.create(notificationReceiver);
        return notification;
    }
}
