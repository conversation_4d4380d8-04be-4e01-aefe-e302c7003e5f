package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.model.NotificationContentItem;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.HistoryEvent;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description NotificationContentItemService
 * @createTime 2022-04-24 09:00:16
 */
public interface NotificationContentItemService {

    List<NotificationContentItem> getAllNotificationContentItems();

    String assembleNotificationContentByLiveEventDTO(ActiveEventDTO activeEvent, String contentTemplate);

    String assembleNotificationContentByHistoryEvent(HistoryEvent historyEvent, String contentTemplate);

    String assembleNotificationContent(ActiveEvent activeEvent, String contentTemplate);
}
