package com.siteweb.eventnotification.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.eventnotification.entity.AlarmNotifyElement;
import com.siteweb.eventnotification.mapper.AlarmNotifyElementMapper;
import com.siteweb.eventnotification.service.AlarmNotifyElementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementServiceImpl
 * @createTime 2022-04-22 16:30:55
 */
@Service
public class AlarmNotifyElementServiceImpl implements AlarmNotifyElementService {

    @Autowired
    AlarmNotifyElementMapper alarmNotifyElementMapper;

    @Override
    public AlarmNotifyElement findById(Integer alarmNotifyElementId) {
        return alarmNotifyElementMapper.selectById(alarmNotifyElementId);
    }

    @Override
    public List<AlarmNotifyElement> findAllByElementType(String elementType) {
        return alarmNotifyElementMapper.selectList(new QueryWrapper<AlarmNotifyElement>().eq("ElementType", elementType));
    }

    @Override
    public List<AlarmNotifyElement> findVisibleElements() {
        return alarmNotifyElementMapper.selectList(Wrappers.lambdaQuery(AlarmNotifyElement.class)
                                                           .eq(AlarmNotifyElement::getVisible, GlobalConstants.YES)
                                                           .orderByAsc(AlarmNotifyElement::getSortIndex));
    }
}