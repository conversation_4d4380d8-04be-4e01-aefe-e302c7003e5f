package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.entity.SafeMessageElementConfig;
import com.siteweb.eventnotification.mapper.SafeMessageElementConfigMapper;
import com.siteweb.eventnotification.service.SafeMessageElementConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SafeMessageElementConfigServiceImpl implements SafeMessageElementConfigService {
    @Autowired
    private SafeMessageElementConfigMapper safeMessageElementConfigMapper;

    @Override
    public List<SafeMessageElementConfig> findBySafeMessageId(Integer safeMessageId) {
        return safeMessageElementConfigMapper.selectList(Wrappers.<SafeMessageElementConfig>lambdaQuery()
                                                                 .eq(SafeMessageElementConfig::getSafeMessageId, safeMessageId));
    }

    @Override
    public void deleteBySafeMessageId(Integer safeMessageId) {
        safeMessageElementConfigMapper.delete(Wrappers.<SafeMessageElementConfig>lambdaQuery()
                                                      .eq(SafeMessageElementConfig::getSafeMessageId, safeMessageId));
    }

    @Override
    public void batchInsert(Integer safeMessageId, List<SafeMessageElementConfig> safeMessageElementConfigList) {
        this.deleteBySafeMessageId(safeMessageId);
        if (CollUtil.isEmpty(safeMessageElementConfigList)) {
            return;
        }
        for (SafeMessageElementConfig safeMessageElementConfig : safeMessageElementConfigList) {
            safeMessageElementConfig.setSafeMessageId(safeMessageId);
            safeMessageElementConfigMapper.insert(safeMessageElementConfig);
        }
    }
}
