package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.SafeMessage;

import java.util.List;

public interface SafeMessageService {
    /**
     * 获取所有平安短信配置
     * @return {@link List}<{@link SafeMessage}>
     */
    List<SafeMessage> findAll();

    SafeMessage findById(Integer id);

    SafeMessage deleteById(Integer id);

    Integer createSafeMessage(SafeMessage safeMessage);

    Integer updateSafeMessage(SafeMessage safeMessage);
}
