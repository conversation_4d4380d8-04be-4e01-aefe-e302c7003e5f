package com.siteweb.eventnotification.service.impl;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.entity.VideoEquipment;
import com.siteweb.eventnotification.mapper.VideoEquipmentMapper;
import com.siteweb.eventnotification.service.VideoEquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VideoEquipmentServiceImpl implements VideoEquipmentService {
    @Autowired
    VideoEquipmentMapper videoEquipmentMapper;

    @Override
    public List<VideoEquipment> getAll() {
        return videoEquipmentMapper.selectList(Wrappers.emptyWrapper());
    }
}
