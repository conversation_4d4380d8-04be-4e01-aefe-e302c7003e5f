package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.entity.TtsConfig;
import com.siteweb.eventnotification.mapper.TtsConfigMapper;
import com.siteweb.eventnotification.service.TtsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class TtsConfigServiceImpl implements TtsConfigService {
    @Autowired
    private TtsConfigMapper ttsConfigMapper;

    @Override
    public List<TtsConfig> findAll() {
        return ttsConfigMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<TtsConfig> findByKeys(List<String> ttsConfigKeyList) {
        if (CollUtil.isEmpty(ttsConfigKeyList)) {
            return Collections.emptyList();
        }
        return ttsConfigMapper.selectList(Wrappers.lambdaQuery(TtsConfig.class)
                                                  .in(TtsConfig::getTtsConfigKey, ttsConfigKeyList));
    }

    @Override
    public List<TtsConfig> batchUpdate(List<TtsConfig> ttsConfigList) {
        ttsConfigMapper.batchUpdate(ttsConfigList);
        return ttsConfigList;
    }

    @Override
    public TtsConfig findByKey(String ttsConfigKey) {
        return ttsConfigMapper.selectOne(Wrappers.lambdaQuery(TtsConfig.class)
                                          .eq(TtsConfig::getTtsConfigKey, ttsConfigKey));
    }

    @Override
    public boolean findBooleanValue(String ttsConfigKey) {
        TtsConfig ttsConfig = findByKey(ttsConfigKey);
        if (Objects.isNull(ttsConfig)) {
            return false;
        }
        return Boolean.parseBoolean(ttsConfig.getTtsConfigValue());
    }
}
