package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.AlarmNotifyGatewayService;
import java.util.List;
import java.util.Map;

public interface AlarmNotifyGatewayServiceService {

    List<AlarmNotifyGatewayService> findAlarmNotifyGatewayServicesByElementId(Integer elementId);

    int deleteAlarmNotifyGatewayServiceByAlarmNotifyGatewayServiceIds(String alarmNotifyGatewayServiceIds);

    List<AlarmNotifyGatewayService> findAllAlarmNotifyGatewayService();

    AlarmNotifyGatewayService createAlarmNotifyGatewayService(AlarmNotifyGatewayService alarmNotifyGatewayService);

    boolean exists(AlarmNotifyGatewayService alarmNotifyGatewayService);

    AlarmNotifyGatewayService updateAlarmNotifyGatewayServiceByAlarmNotifyGatewayServiceId(AlarmNotifyGatewayService alarmNotifyGatewayService);

    boolean getUsedStatus(String alarmNotifyGatewayServiceIds);

    Map<Integer, Boolean> getUsedStatusMap();
}