package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.dto.AlarmVideoLinkMapImportDTO;
import com.siteweb.eventnotification.dto.AlarmVideoLinkSnapshotMap;
import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;
import com.siteweb.eventnotification.entity.VideoEquipment;
import com.siteweb.eventnotification.mapper.AlarmVideoLinkMapMapper;
import com.siteweb.eventnotification.service.AlarmVideoLinkEventService;
import com.siteweb.eventnotification.service.AlarmVideoLinkMapService;
import com.siteweb.eventnotification.service.VideoEquipmentService;
import com.siteweb.monitoring.dto.ConfigEventDTO;
import com.siteweb.monitoring.dto.EventConditionDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ConfigEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmVideoLinkMapServiceImpl implements AlarmVideoLinkMapService {
    private static final String NOT_FOUND = "找不到";

    @Autowired
    AlarmVideoLinkMapMapper alarmVideoLinkMapMapper;
    @Autowired
    AlarmVideoLinkEventService alarmVideoLinkEventService;

    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    ConfigEventManager configEventManager;
    @Autowired
    VideoEquipmentService videoEquipmentService;

    @Override
    public List<AlarmVideoLinkMap> findByAlarmVideoLinkId(Integer alarmVideoLinkId) {
        return alarmVideoLinkMapMapper.selectList(Wrappers.lambdaQuery(AlarmVideoLinkMap.class)
                                                          .eq(AlarmVideoLinkMap::getAlarmVideoLinkId, alarmVideoLinkId));
    }

    @Override
    public List<AlarmVideoLinkSnapshotMap> findMapByCondition(Integer equipmentId, Integer eventId, Integer eventConditionId) {
        return alarmVideoLinkMapMapper.findByCondition(equipmentId, eventId, eventConditionId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByAlarmVideoLinkId(Integer alarmVideoLinkId) {
        List<AlarmVideoLinkMap> alarmVideoLinkMapList = this.findByAlarmVideoLinkId(alarmVideoLinkId);
        if (CollUtil.isEmpty(alarmVideoLinkMapList)) {
            return;
        }
        alarmVideoLinkEventService.deleteByAlarmVideoLinkMapIds(alarmVideoLinkMapList.stream().map(AlarmVideoLinkMap::getAlarmVideoLinkMapId).toList());
        alarmVideoLinkMapMapper.delete(Wrappers.<AlarmVideoLinkMap>lambdaQuery()
                                               .eq(AlarmVideoLinkMap::getAlarmVideoLinkId, alarmVideoLinkId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(Integer alarmVideoLinkId, List<AlarmVideoLinkMap> alarmVideoLinkMapList) {
        this.deleteByAlarmVideoLinkId(alarmVideoLinkId);
        if (CollUtil.isEmpty(alarmVideoLinkMapList)) {
            return;
        }
        for (AlarmVideoLinkMap alarmVideoLinkMap : alarmVideoLinkMapList) {
            alarmVideoLinkMap.setAlarmVideoLinkId(alarmVideoLinkId);
            alarmVideoLinkMapMapper.insert(alarmVideoLinkMap);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> importAlarmVideoLinkMap(List<AlarmVideoLinkMapImportDTO> alarmVideoLinkMapImportDTOList) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<AlarmVideoLinkMap> alarmVideoLinkMapList = new ArrayList<>();
        Map<String, ResourceStructure> roomMap = resourceStructureManager.getAll().stream().collect(Collectors.toMap(ResourceStructure::getResourceStructureName, r -> r, (v1, v2) -> v1));
        Map<String, Equipment> equipmentMap = equipmentManager.getAllEquipments().stream().collect(Collectors.toMap(e -> e.getResourceStructureId() + "." + e.getEquipmentName(), e -> e, (v1, v2) -> v1));
        ConcurrentHashMap<Integer, List<ConfigEventDTO>> eventMap = configEventManager.getAllEventMap();
        List<VideoEquipment> videoEquipmentList= videoEquipmentService.getAll();
        Map<String, Long> videoEquipmentMap = videoEquipmentList.stream().collect(Collectors.toMap(VideoEquipment::getName,VideoEquipment::getId,(v1, v2) -> v1));


        for (int i = 0; i < alarmVideoLinkMapImportDTOList.size(); i++) {
            AlarmVideoLinkMapImportDTO alarmVideoLinkMapImportDTO = alarmVideoLinkMapImportDTOList.get(i);

            try {
                // 检测摄像头名称
                Long cameraId = videoEquipmentMap.get(alarmVideoLinkMapImportDTO.getCameraName());
                if (cameraId == null) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "cameraName", NOT_FOUND + alarmVideoLinkMapImportDTO.getCameraName()));
                    continue;
                }

                // 检测设备位置
                ResourceStructure resourceStructure = roomMap.get(alarmVideoLinkMapImportDTO.getRoomName());
                if (resourceStructure == null) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "roomName", NOT_FOUND + alarmVideoLinkMapImportDTO.getRoomName()));
                    continue;
                }

                // 检测设备名称
                Equipment equipment = equipmentMap.get(resourceStructure.getResourceStructureId() + "." + alarmVideoLinkMapImportDTO.getEquipmentName());
                if (equipment == null) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "equipmentName", NOT_FOUND + alarmVideoLinkMapImportDTO.getEquipmentName()));
                    continue;
                }
                List<ConfigEventDTO> configEventFilterDTO = new ArrayList<>();
                List<EventConditionDTO> eventConditionFilterDTOList = new ArrayList<>();

                if (!alarmVideoLinkMapImportDTO.getEventName().isEmpty() && !alarmVideoLinkMapImportDTO.getEventConditionName().isEmpty()) {
                    // 检测事件名称
                    List<ConfigEventDTO> configEventDTOList = eventMap.get(equipment.getEquipmentTemplateId());
                    configEventFilterDTO = configEventDTOList.stream().filter(c-> Objects.equals(c.getEventName(), alarmVideoLinkMapImportDTO.getEventName())).toList();
                    if (configEventFilterDTO.isEmpty()) {
                        importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "eventName", NOT_FOUND + alarmVideoLinkMapImportDTO.getEventName()));
                        continue;
                    }

                    // 检测条件名称
                    List<EventConditionDTO> eventConditionDTOList = configEventFilterDTO.get(0).getEventConditions();
                    eventConditionFilterDTOList = eventConditionDTOList.stream().filter(c-> Objects.equals(c.getBaseTypeName(), alarmVideoLinkMapImportDTO.getEventConditionName()) ||
                            Objects.equals(c.getMeanings(), alarmVideoLinkMapImportDTO.getEventConditionName())).toList();
                    if (eventConditionFilterDTOList.isEmpty()) {
                        importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "eventConditionName", NOT_FOUND + alarmVideoLinkMapImportDTO.getEventConditionName()));
                        continue;
                    }
                }

                AlarmVideoLinkMap alarmVideoLinkMap = new AlarmVideoLinkMap();
                alarmVideoLinkMap.setEquipmentId(equipment.getEquipmentId());
                if (!configEventFilterDTO.isEmpty())
                    alarmVideoLinkMap.setEventId(configEventFilterDTO.get(0).getEventId());
                if (!eventConditionFilterDTOList.isEmpty())
                    alarmVideoLinkMap.setEventConditionId(eventConditionFilterDTOList.get(0).getEventConditionId());
                alarmVideoLinkMap.setCameraId(cameraId);
                alarmVideoLinkMapList.add(alarmVideoLinkMap);

            } catch (Exception ex) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(null, "Exception", ex.getMessage()));
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("error", importErrorInfoList);
        result.put("data", alarmVideoLinkMapList);
        return result;
    }

    @Override
    public List<AlarmVideoLinkMapImportDTO> exportAlarmVideoLinkMap(List<AlarmVideoLinkMap> alarmVideoLinkMapList) {
        Map<Integer, Equipment> equipmentMap = equipmentManager.getAllEquipments().stream().collect(Collectors.toMap(Equipment::getEquipmentId, e -> e, (v1, v2) -> v1));
        ConcurrentHashMap<String, ConfigEventDTO> templateEventMap = configEventManager.getAllEquipmentEventMap();
        ConcurrentHashMap<String, EventConditionDTO> templateEventConditionMap = configEventManager.getAllEquipmentEventConditionMap();
        List<VideoEquipment> videoEquipmentList= videoEquipmentService.getAll();
        Map<Long, String> videoEquipmentMap = videoEquipmentList.stream().collect(Collectors.toMap(VideoEquipment::getId,VideoEquipment::getName,(v1, v2) -> v1));
        List<AlarmVideoLinkMapImportDTO> alarmVideoLinkMapImportDTOList  = new ArrayList<>();
        for (int i = 0; i < alarmVideoLinkMapList.size(); i++) {
            AlarmVideoLinkMap alarmVideoLinkMap = alarmVideoLinkMapList.get(i);
            try {
                AlarmVideoLinkMapImportDTO alarmVideoLinkMapImportDTO = new AlarmVideoLinkMapImportDTO();
                String cameraName = videoEquipmentMap.get(alarmVideoLinkMap.getCameraId());
                alarmVideoLinkMapImportDTO.setCameraName(cameraName);
                Equipment equipment = equipmentMap.get(alarmVideoLinkMap.getEquipmentId());
                alarmVideoLinkMapImportDTO.setEquipmentName(equipment.getEquipmentName());
                alarmVideoLinkMapImportDTO.setRoomName(equipment.getResourceStructureName());
                if (alarmVideoLinkMap.getEventId() != null && alarmVideoLinkMap.getEventConditionId() != null) {
                    ConfigEventDTO configEventDTO = templateEventMap.get(equipment.getEquipmentTemplateId() + "." + alarmVideoLinkMap.getEventId());
                    EventConditionDTO eventConditionDTO = templateEventConditionMap.get(equipment.getEquipmentTemplateId() + "." + alarmVideoLinkMap.getEventId() + "." + alarmVideoLinkMap.getEventConditionId());
                    alarmVideoLinkMapImportDTO.setEventName(configEventDTO.getEventName());
                    alarmVideoLinkMapImportDTO.setEventConditionName(Optional.ofNullable(eventConditionDTO.getBaseTypeName()).orElse(eventConditionDTO.getMeanings()));
                }
                alarmVideoLinkMapImportDTOList.add(alarmVideoLinkMapImportDTO);
            } catch (Exception ex) {
                log.error(ex.getMessage());
            }
        }
        return alarmVideoLinkMapImportDTOList;
    }
}
