package com.siteweb.eventnotification.service.impl;

import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.sender.*;
import com.siteweb.eventnotification.service.AlarmNotifyExecutorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyExecutorServiceImpl
 * @createTime 2022-07-11 13:49:49
 */
@Service
public class AlarmNotifyExecutorServiceImpl implements AlarmNotifyExecutorService {

    @Autowired
    private AlarmBoxAlarmNotifySender alarmBoxAlarmNotifySender;

    @Autowired
    private EmailAlarmNotifySender emailAlarmNotifySender;

    @Autowired
    private PhoneSmsAlarmNotifySender phoneSmsAlarmNotifySender;

    @Autowired
    private SmsAlarmNotifySender smsAlarmNotifySender;

    @Autowired
    private WeComGroupNotifySender weComGroupNotifySender;
    @Autowired
    private LarkGroupNotifySender larkGroupNotifySender;

    @Autowired
    private AlarmLightAlarmNotifySender alarmLightAlarmNotifySender;
    @Autowired
    private AppNotifySender appNotifySender;
    @Autowired
    private SmsAlarmNotifyByJobNumberSender smsAlarmNotifyByJobNumberSender;

    @Override
    public int alarmBoxAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return alarmBoxAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
    }

    @Override
    public int smsAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return smsAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
    }

    @Override
    public int emailAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return emailAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
    }

    @Override
    public int phoneSmsAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return phoneSmsAlarmNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
    }

    @Override
    public int weComGroupNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return weComGroupNotifySender.alarmNotifySend(elementConfigDTO,alarmNotifyExecutorDTO,nodeExpressionHashMap);
    }

    @Override
    public int LarkNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return larkGroupNotifySender.alarmNotifySend(elementConfigDTO,alarmNotifyExecutorDTO,nodeExpressionHashMap);
    }

    @Override
    public int alarmLightAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return alarmLightAlarmNotifySender.alarmNotifySend(elementConfigDTO,alarmNotifyExecutorDTO,nodeExpressionHashMap);
    }

    @Override
    public int alarmAppNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return appNotifySender.alarmNotifySend(elementConfigDTO, alarmNotifyExecutorDTO, nodeExpressionHashMap);
    }

    @Override
    public int alarmAppNotifyByJobNumberSend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap) {
        return smsAlarmNotifyByJobNumberSender.alarmNotifySend(elementConfigDTO,alarmNotifyExecutorDTO,nodeExpressionHashMap);
    }
}
