package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.NotificationReceiver;

import java.util.List;
import java.util.Set;

/**
 * @Author: lzy
 * @Date: 2023/3/16 13:54
 */
public interface NotificationReceiverService {
    NotificationReceiver create(NotificationReceiver notificationReceiver);

    List<NotificationReceiver> findReceiverByReaded(Integer readed, Integer loginUserId);

    Set<Integer> findNotificationIdByReaded(Integer readed, Integer loginUserId);

    void notificationReaded(List<Integer> notificationId);

    void notificationRemove(List<Integer> notificationIds);

    /**
     * 获取用户未读消息数
     */
    Long findUnreadNotificationCountsByUserId(Integer userId);
}
