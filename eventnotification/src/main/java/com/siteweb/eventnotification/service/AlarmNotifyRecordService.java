package com.siteweb.eventnotification.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.eventnotification.dto.AlarmNotifyRecordDto;
import com.siteweb.eventnotification.entity.AlarmNotifyRecord;
import com.siteweb.eventnotification.vo.AlarmNotifyRecordVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyRecordService
 * @createTime 2022-04-24 08:59:35
 */
public interface AlarmNotifyRecordService {

    int createAlarmNotifyRecord(AlarmNotifyRecord alarmNotifyRecord);

    List<AlarmNotifyRecord> findByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    Page<AlarmNotifyRecord> findAlarmNotifyRecordsByKeywords(String keywords, Pageable pageable);
    List<AlarmNotifyRecordDto> findByReportParam(AlarmNotifyRecordVO alarmNotifyRecordVO, Collection<Integer> equipmentIds);

    IPage<AlarmNotifyRecordDto> findByReportParamPage(IPage<AlarmNotifyRecordDto> page, AlarmNotifyRecordVO recordParam, Collection<Integer> equipmentIds);

    /**
     * 查询告警通知发送记录总数
     * 由于mybatis-plus的分页插件性能过慢固自定义一个查询总数的方法
     * @param recordParam  记录参数
     * @param equipmentIds 设备id
     * @return long
     */
    long findByReportParamCount(AlarmNotifyRecordVO recordParam, Collection<Integer> equipmentIds);
}
