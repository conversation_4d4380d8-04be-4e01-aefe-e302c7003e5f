package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.eventnotification.entity.NotificationReceiver;
import com.siteweb.eventnotification.mapper.NotificationReceiverMapper;
import com.siteweb.eventnotification.service.NotificationReceiverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2023/3/16 13:55
 */
@Service
public class NotificationReceiverServiceImpl implements NotificationReceiverService {
    @Autowired
    NotificationReceiverMapper notificationReceiverMapper;
    @Override
    public NotificationReceiver create(NotificationReceiver notificationReceiver) {
        return notificationReceiverMapper.insert(notificationReceiver) > 0 ? notificationReceiver : null;
    }

    @Override
    public List<NotificationReceiver> findReceiverByReaded(Integer readed, Integer loginUserId) {
        LambdaQueryWrapper<NotificationReceiver> wrapper = Wrappers.lambdaQuery(NotificationReceiver.class);
        wrapper.eq(ObjectUtil.isNotEmpty(readed), NotificationReceiver::getReaded, readed);
        wrapper.ne(NotificationReceiver::getDeleted, 1);
        wrapper.eq(NotificationReceiver::getLoginUserId, loginUserId);
        return notificationReceiverMapper.selectList(wrapper);
    }

    @Override
    public Set<Integer> findNotificationIdByReaded(Integer readed, Integer loginUserId) {
        return this.findReceiverByReaded(readed, loginUserId).stream().map(NotificationReceiver::getNotificationId).collect(Collectors.toSet());
    }

    @Override
    public void notificationReaded(List<Integer> notificationIds) {
        LambdaUpdateWrapper<NotificationReceiver> wrapper = Wrappers.lambdaUpdate(NotificationReceiver.class);
        wrapper.in(NotificationReceiver::getNotificationId, notificationIds);
        wrapper.eq(NotificationReceiver::getLoginUserId, TokenUserUtil.getLoginUserId());
        wrapper.set(NotificationReceiver::getReaded, 1);
        wrapper.set(NotificationReceiver::getReadedTime, new Date());
        notificationReceiverMapper.update(null, wrapper);
    }

    @Override
    public void notificationRemove(List<Integer> notificationIds) {
        LambdaUpdateWrapper<NotificationReceiver> wrapper = Wrappers.lambdaUpdate(NotificationReceiver.class);
        wrapper.in(NotificationReceiver::getNotificationId, notificationIds);
        wrapper.eq(NotificationReceiver::getLoginUserId, TokenUserUtil.getLoginUserId());
        wrapper.set(NotificationReceiver::getDeleted, 1);
        wrapper.set(NotificationReceiver::getDeletedTime, new Date());
        notificationReceiverMapper.update(null, wrapper);
    }

    @Override
    public Long findUnreadNotificationCountsByUserId(Integer userId) {
        LambdaUpdateWrapper<NotificationReceiver> wrapper = Wrappers.lambdaUpdate(NotificationReceiver.class);
        wrapper.in(NotificationReceiver::getLoginUserId, userId);
        wrapper.eq(NotificationReceiver::getReaded, 0);
        wrapper.eq(NotificationReceiver::getDeleted, 0);
        return notificationReceiverMapper.selectCount(wrapper);
    }
}
