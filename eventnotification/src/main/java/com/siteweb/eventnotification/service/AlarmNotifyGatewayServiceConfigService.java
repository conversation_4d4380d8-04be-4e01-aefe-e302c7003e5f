package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayServiceConfig;

import java.util.List;

public interface AlarmNotifyGatewayServiceConfigService {

    List<AlarmNotifyGatewayServiceConfig> batchSaveAlarmNotifyGatewayServiceConfig(AlarmNotifyGatewayServiceConfigDTO alarmNotifyGatewayServiceConfigDTO);

    List<AlarmNotifyGatewayServiceConfigDetailDTO> findAlarmNotifyGatewayServicesByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    AlarmNotifyGatewayServiceConfigDetailDTO findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyConfigIdAndElementId(Integer alarmNotifyConfigId, Integer elementId);

    void deleteAlarmNotifyGatewayServiceConfigByAlarmNotifyGatewayServiceIds(String alarmNotifyGatewayServiceIds);
}