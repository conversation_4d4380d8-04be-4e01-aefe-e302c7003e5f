package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.AlarmNotifyElement;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementService
 * @createTime 2022-04-22 16:28:56
 */
public interface AlarmNotifyElementService {

    AlarmNotifyElement findById(Integer alarmNotifyElementId);

    List<AlarmNotifyElement> findAllByElementType(String elementType);

    List<AlarmNotifyElement> findVisibleElements();
}
