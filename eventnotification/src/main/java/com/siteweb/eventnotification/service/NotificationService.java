package com.siteweb.eventnotification.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.eventnotification.entity.Notification;
import com.siteweb.eventnotification.getui.entity.MobileClientMap;
import com.siteweb.eventnotification.vo.NotificationEquipmentCategoryVO;
import com.siteweb.eventnotification.vo.NotificationEventCategoryVO;
import com.siteweb.monitoring.entity.AlarmChange;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/16 13:43
 */
public interface NotificationService {
    Notification create(Notification notification);

    Notification updateById(Notification notification);

    /**
     * 分页查询消息列表
     * @param pageable 分页
     * @param readed 是否已读
     */
    IPage<Notification> findListByPage(Pageable pageable, Integer readed);

    /**
     * 获取推送设备种类条件
     */
    List<NotificationEquipmentCategoryVO> findGetuiPushEquipmentCategory();

    /**
     * 获取推送事件类别条件
     */
    List<NotificationEventCategoryVO> findGetuiPushEventCategory();

    /**
     * 添加告警通知消息记录
     * @param mobileClientMap 手机与用户id的映射关系
     * @param alarmChange 告警变更记录
     * @param requestSign 唯一通知id
     * @param pushTitle 通知标题
     * @param pushContent 通知内容
     * @return {@link Notification}
     */
    Notification createAlarmNotification(MobileClientMap mobileClientMap, AlarmChange alarmChange, String requestSign, String pushTitle, String pushContent);
}
