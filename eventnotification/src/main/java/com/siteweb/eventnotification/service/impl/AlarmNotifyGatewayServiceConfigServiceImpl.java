package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayServiceConfig;
import com.siteweb.eventnotification.mapper.AlarmNotifyGatewayServiceConfigMapper;
import com.siteweb.eventnotification.service.AlarmNotifyGatewayServiceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class AlarmNotifyGatewayServiceConfigServiceImpl implements AlarmNotifyGatewayServiceConfigService {
    @Autowired
    AlarmNotifyGatewayServiceConfigMapper alarmNotifyGatewayServiceConfigMapper;

    @Autowired
    AlarmNotifyGatewayServiceServiceImpl alarmNotifyGatewayServiceServiceImpl;

    @Override
    public List<AlarmNotifyGatewayServiceConfig> batchSaveAlarmNotifyGatewayServiceConfig(AlarmNotifyGatewayServiceConfigDTO alarmNotifyGatewayServiceConfigDTO) {
        List<AlarmNotifyGatewayServiceConfig> existList = alarmNotifyGatewayServiceConfigMapper.selectList(Wrappers.lambdaQuery(AlarmNotifyGatewayServiceConfig.class)
                .eq(AlarmNotifyGatewayServiceConfig::getAlarmNotifyConfigId, alarmNotifyGatewayServiceConfigDTO.getAlarmNotifyConfigId()));
        List<Integer> existIdList = existList.stream().map(AlarmNotifyGatewayServiceConfig::getAlarmNotifyGatewayServiceConfigId).toList();
        if (!existIdList.isEmpty()) {
            alarmNotifyGatewayServiceConfigMapper.deleteBatchIds(existIdList);
        }
        List<AlarmNotifyGatewayServiceConfig> alarmNotifyGatewayServiceConfigList = alarmNotifyGatewayServiceConfigDTO.buildAlarmNotifyGatewayServiceList();
        if (CollectionUtil.isNotEmpty(alarmNotifyGatewayServiceConfigList))
            alarmNotifyGatewayServiceConfigMapper.batchInsert(alarmNotifyGatewayServiceConfigList);
        return alarmNotifyGatewayServiceConfigList;
    }

    @Override
    public List<AlarmNotifyGatewayServiceConfigDetailDTO> findAlarmNotifyGatewayServicesByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        Map<Integer, Boolean> usedStatusMap = alarmNotifyGatewayServiceServiceImpl.getUsedStatusMap();
        List<AlarmNotifyGatewayServiceConfigDetailDTO> alarmNotifyGatewayServiceConfigDetailDTOList = alarmNotifyGatewayServiceConfigMapper.findAlarmNotifyGatewayServicesByAlarmNotifyConfigId(alarmNotifyConfigId);
        alarmNotifyGatewayServiceConfigDetailDTOList.forEach(dto -> dto.setUsedStatus(usedStatusMap.get(dto.getAlarmNotifyGatewayServiceId())));
        return alarmNotifyGatewayServiceConfigDetailDTOList;
    }

    @Override
    public AlarmNotifyGatewayServiceConfigDetailDTO findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyConfigIdAndElementId(Integer alarmNotifyConfigId, Integer elementId) {
        return alarmNotifyGatewayServiceConfigMapper.findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyConfigIdAndElementId(alarmNotifyConfigId, elementId);
    }

    @Override
    public void deleteAlarmNotifyGatewayServiceConfigByAlarmNotifyGatewayServiceIds(String alarmNotifyGatewayServiceIds) {
        List<Integer> alarmNotifyGatewayServiceIdList = CharSequenceUtil.split(alarmNotifyGatewayServiceIds, ",")
                .stream()
                .map(Integer::valueOf)
                .toList();
        alarmNotifyGatewayServiceConfigMapper.delete(Wrappers.lambdaQuery(AlarmNotifyGatewayServiceConfig.class)
                .in(AlarmNotifyGatewayServiceConfig::getAlarmNotifyGatewayServiceId, alarmNotifyGatewayServiceIdList));
    }
}