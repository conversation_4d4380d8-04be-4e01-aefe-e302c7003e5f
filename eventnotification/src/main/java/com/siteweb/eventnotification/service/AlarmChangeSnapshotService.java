package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmChangeSnapshotFilterDTO;
import org.springframework.data.domain.Page;
import com.siteweb.eventnotification.dto.AllEventDTO;
import com.siteweb.eventnotification.entity.AlarmChangeSnapshot;
import org.springframework.data.domain.Pageable;
import java.util.List;

public interface AlarmChangeSnapshotService {
    int batchInsertAlarmChangeSnapshot(List<AlarmChangeSnapshot> alarmChangeSnapshotList);

    List<AlarmChangeSnapshot> findAlarmChangeSnapshotBySequenceId(String sequenceId);

    Page<AllEventDTO> findAllEventDTOBySnapshot(Integer userId, Pageable pageable, AlarmChangeSnapshotFilterDTO alarmChangeSnapshotFilterDTO);
}