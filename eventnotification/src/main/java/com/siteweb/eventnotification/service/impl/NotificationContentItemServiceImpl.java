package com.siteweb.eventnotification.service.impl;

import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.model.NotificationContentItem;
import com.siteweb.eventnotification.service.NotificationContentItemService;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.HistoryEvent;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> zhou
 * @description NotificationContentItemServiceImpl
 * @createTime 2022-04-24 15:11:14
 */
@Service
public class NotificationContentItemServiceImpl implements NotificationContentItemService {

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Override
    public List<NotificationContentItem> getAllNotificationContentItems() {
        List<NotificationContentItem> list = new ArrayList<>();
        list.add(new NotificationContentItem(1, "EquipmentName", messageSourceUtil.getMessage("eventNotification.activeEvent.equipmentName")));
        list.add(new NotificationContentItem(2, "EventName", messageSourceUtil.getMessage("eventNotification.activeEvent.eventName")));
        list.add(new NotificationContentItem(3, "EventSeverity", messageSourceUtil.getMessage("eventNotification.activeEvent.eventSeverity")));
        list.add(new NotificationContentItem(4, "EventValue", messageSourceUtil.getMessage("eventNotification.activeEvent.eventValue")));
        list.add(new NotificationContentItem(5, "StartTime", messageSourceUtil.getMessage("eventNotification.activeEvent.startTime")));
        list.add(new NotificationContentItem(6, "EndTime", messageSourceUtil.getMessage("eventNotification.activeEvent.endTime")));
        list.add(new NotificationContentItem(7, "Meanings", messageSourceUtil.getMessage("eventNotification.activeEvent.meanings")));
        list.add(new NotificationContentItem(8, "EquipmentPosition", messageSourceUtil.getMessage("eventNotification.activeEvent.fullPosition")));
        list.add(new NotificationContentItem(9, "ResourceStructureName", messageSourceUtil.getMessage("eventNotification.activeEvent.concisePosition")));
        return list;
    }

    @Override
    public String assembleNotificationContentByLiveEventDTO(ActiveEventDTO activeEvent, String contentTemplate) {
        Pattern pattern = Pattern.compile("\\{(\\w+[^{^}])\\}");
        Matcher matcher = pattern.matcher(contentTemplate);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String matcherStr = matcher.group();
            switch (matcherStr) {
                case "{ResourceStructureName}":
                    String resourceStructureName = Optional.ofNullable(resourceStructureManager.getResourceStructureById(activeEvent.getResourceStructureId()))
                                                           .map(ResourceStructure::getResourceStructureName)
                                                           .orElse("");
                    matcher.appendReplacement(sb, resourceStructureName);
                    break;
                case "{EquipmentPosition}":
                    matcher.appendReplacement(sb, resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
                    break;
                case "{EquipmentName}":
                    matcher.appendReplacement(sb, activeEvent.getEquipmentName());
                    break;
                case "{EventName}":
                    matcher.appendReplacement(sb, activeEvent.getEventName());
                    break;
                case "{EventSeverity}":
                    matcher.appendReplacement(sb, activeEvent.getEventSeverity());
                    break;
                case "{EventValue}":
                    try {
                        matcher.appendReplacement(sb, String.format("%.2f", activeEvent.getEventValue()));
                    } catch (NumberFormatException ex) {
                        matcher.appendReplacement(sb, activeEvent.getEventValue().toString());
                    }
                    break;
                case "{StartTime}":
                    matcher.appendReplacement(sb, DateUtil.dateToString(activeEvent.getStartTime()));
                    break;
                case "{EndTime}":
                    if (null != activeEvent.getEndTime()) {
                        matcher.appendReplacement(sb, DateUtil.dateToString(activeEvent.getEndTime()));
                    } else {
                        matcher.appendReplacement(sb, "");
                    }
                    break;
                case "{Meanings}":
                    matcher.appendReplacement(sb, activeEvent.getMeanings());
                    break;
                default:
                    break;
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    @Override
    public String assembleNotificationContentByHistoryEvent(HistoryEvent historyEvent, String contentTemplate) {
        Pattern pattern = Pattern.compile("\\{(\\w+[^{^}])\\}");
        Matcher matcher = pattern.matcher(contentTemplate);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String matcherStr = matcher.group();
            switch (matcherStr) {
                case "{ResourceStructureName}":
                    String resourceStructureName = Optional.ofNullable(resourceStructureManager.getResourceStructureById(historyEvent.getResourceStructureId()))
                                                           .map(ResourceStructure::getResourceStructureName)
                                                           .orElse("");
                    matcher.appendReplacement(sb, resourceStructureName);
                    break;
                case "{EquipmentPosition}":
                    matcher.appendReplacement(sb, resourceStructureManager.getFullPath(historyEvent.getResourceStructureId()));
                    break;
                case "{EquipmentName}":
                    matcher.appendReplacement(sb, historyEvent.getEquipmentName());
                    break;
                case "{EventName}":
                    matcher.appendReplacement(sb, historyEvent.getEventName());
                    break;
                case "{EventSeverity}":
                    matcher.appendReplacement(sb, historyEvent.getEventSeverity());
                    break;
                case "{EventValue}":
                    try {
                        matcher.appendReplacement(sb, String.format("%.2f", historyEvent.getEventValue()));
                    } catch (NumberFormatException ex) {
                        matcher.appendReplacement(sb, historyEvent.getEventValue().toString());
                    }
                    break;
                case "{StartTime}":
                    matcher.appendReplacement(sb, DateUtil.dateToString(historyEvent.getStartTime()));
                    break;
                case "{EndTime}":
                    if (null != historyEvent.getEndTime()) {
                        matcher.appendReplacement(sb, DateUtil.dateToString(historyEvent.getEndTime()));
                    } else {
                        matcher.appendReplacement(sb, "");
                    }
                    break;
                case "{Meanings}":
                    matcher.appendReplacement(sb, historyEvent.getMeanings());
                    break;
                default:
                    break;
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    @Override
    public String assembleNotificationContent(ActiveEvent activeEvent, String contentTemplate) {
        Pattern pattern = Pattern.compile("\\{(\\w+[^{^}])\\}");
        Matcher matcher = pattern.matcher(contentTemplate);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String matcherStr = matcher.group();
            switch (matcherStr) {
                case "{EquipmentPosition}":
                    matcher.appendReplacement(sb, resourceStructureManager.getFullPath(activeEvent.getResourceStructureId()));
                    break;
                case "{ResourceStructureName}":
                    String resourceStructureName = Optional.ofNullable(resourceStructureManager.getResourceStructureById(activeEvent.getResourceStructureId()))
                                                           .map(ResourceStructure::getResourceStructureName)
                                                           .orElse("");
                    matcher.appendReplacement(sb, resourceStructureName);
                    break;
                case "{EquipmentName}":
                    matcher.appendReplacement(sb, activeEvent.getEquipmentName());
                    break;
                case "{EventName}":
                    matcher.appendReplacement(sb, activeEvent.getEventName());
                    break;
                case "{EventSeverity}":
                    matcher.appendReplacement(sb, activeEvent.getEventSeverity());
                    break;
                case "{EventValue}":
                    try {
                        matcher.appendReplacement(sb, String.format("%.2f", activeEvent.getEventValue()));
                    } catch (NumberFormatException ex) {
                        matcher.appendReplacement(sb, activeEvent.getEventValue().toString());
                    }
                    break;
                case "{StartTime}":
                    matcher.appendReplacement(sb, DateUtil.dateToString(activeEvent.getStartTime()));
                    break;
                case "{EndTime}":
                    if (null != activeEvent.getEndTime()) {
                        matcher.appendReplacement(sb, DateUtil.dateToString(activeEvent.getEndTime()));
                    } else {
                        matcher.appendReplacement(sb, "");
                    }
                    break;
                case "{Meanings}":
                    matcher.appendReplacement(sb, activeEvent.getMeanings());
                    break;
                default:
                    break;
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
