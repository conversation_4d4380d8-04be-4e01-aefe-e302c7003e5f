package com.siteweb.eventnotification.service.impl;

import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.dto.AlarmNotifySegmentDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyNode;
import com.siteweb.eventnotification.entity.AlarmNotifySegment;
import com.siteweb.eventnotification.mapper.AlarmNotifyNodeMapper;
import com.siteweb.eventnotification.mapper.AlarmNotifySegmentMapper;
import com.siteweb.eventnotification.service.AlarmNotifyNodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyNodeServiceImpl
 * @createTime 2022-04-24 09:24:21
 */
@Service
public class AlarmNotifyNodeServiceImpl implements AlarmNotifyNodeService {

    @Autowired
    AlarmNotifyNodeMapper alarmNotifyNodeMapper;

    @Autowired
    AlarmNotifySegmentMapper alarmNotifySegmentMapper;

    @Override
    public int createAlarmNotifyNode(AlarmNotifyNode alarmNotifyNode) {
        return alarmNotifyNodeMapper.insert(alarmNotifyNode);
    }

    @Override
    public AlarmNotifyNode findById(Integer alarmNotifyNodeId) {
        return alarmNotifyNodeMapper.selectById(alarmNotifyNodeId);
    }

    @Override
    public List<AlarmNotifyNodeDTO> findAlarmNotifyNodesByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        List<AlarmNotifyNode> alarmNotifyNodes = alarmNotifyNodeMapper.findAlarmNotifyNodesByAlarmNotifyConfigId(alarmNotifyConfigId);
        List<AlarmNotifySegment> alarmNotifySegments = alarmNotifySegmentMapper.findAlarmNotifySegmentsByAlarmNotifyConfigId(alarmNotifyConfigId);
        List<AlarmNotifyNodeDTO> nodeDTOs = new ArrayList<>();
        for (AlarmNotifyNode alarmNotifyNode : alarmNotifyNodes) {
            AlarmNotifyNodeDTO nodeDTO = new AlarmNotifyNodeDTO();
            BeanUtils.copyProperties(alarmNotifyNode, nodeDTO);
            List<AlarmNotifySegmentDTO> segmentDTOList = new ArrayList<>();
            if ("right".equalsIgnoreCase(alarmNotifyNode.getNodeDirection())) {
                List<AlarmNotifySegment> tmpSegmentList = alarmNotifySegments.stream().filter(o -> alarmNotifyNode.getNodeId().equals(o.getInputNodeId())).toList();
                for (AlarmNotifySegment alarmNotifySegment : tmpSegmentList) {
                    AlarmNotifySegmentDTO segmentDTO = new AlarmNotifySegmentDTO();
                    BeanUtils.copyProperties(alarmNotifySegment, segmentDTO);
                    segmentDTOList.add(segmentDTO);
                }
            }
            nodeDTO.setSegmentDTOs(segmentDTOList);
            nodeDTOs.add(nodeDTO);
        }
        return nodeDTOs;
    }

    @Override
    public int updateAlarmNotifyNode(AlarmNotifyNode alarmNotifyNode) {
        return alarmNotifyNodeMapper.updateById(alarmNotifyNode);
    }

    @Override
    public void deleteAlarmNotifyNodeById(Integer alarmNotifyNodeId) {
        alarmNotifyNodeMapper.deleteById(alarmNotifyNodeId);
    }
}
