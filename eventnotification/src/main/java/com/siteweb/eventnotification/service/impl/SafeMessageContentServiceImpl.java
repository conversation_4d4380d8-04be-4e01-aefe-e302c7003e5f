package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.complexindex.entity.LiveComplexIndex;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.eventnotification.model.SafeMessageContentItem;
import com.siteweb.eventnotification.service.SafeMessageContentService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SafeMessageContentServiceImpl implements SafeMessageContentService {
    @Autowired
    private ActiveEventManager activeEventManager;
    @Autowired
    private RealTimeSignalManager realTimeSignalManager;
    @Autowired
    private ConfigSignalManager configSignalManager;
    @Autowired
    private ActiveSignalManager activeSignalManager;
    @Autowired
    private LiveComplexIndexManager liveComplexIndexManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Override
    public List<SafeMessageContentItem> findAllSafeMessageContentItems() {
        List<SafeMessageContentItem> list = new ArrayList<>();
        list.add(new SafeMessageContentItem(1, "Signal", localeMessageSourceUtil.getMessage("common.field.signal")));
        list.add(new SafeMessageContentItem(2, "ComplexIndex", localeMessageSourceUtil.getMessage("common.field.complexIndex")));
        list.add(new SafeMessageContentItem(3, "AlarmCount", localeMessageSourceUtil.getMessage("common.field.alarmCount")));
        return list;
    }

    public String analysisSafeMessageTemplate(String contentTemplate) {
        Pattern pattern = Pattern.compile("\\{[^}]+\\}");
        Matcher matcher = pattern.matcher(contentTemplate);
        StringBuilder stringBuilder = new StringBuilder();
        while (matcher.find()) {
            String matcherStr = matcher.group();
            //告警总数
            if ("{AlarmCount}".equals(matcherStr)) {
                int activeEventCount = activeEventManager.queryAllActiveEvents().size();
                matcher.appendReplacement(stringBuilder, String.valueOf(activeEventCount));
            }
            //信号
            if (CharSequenceUtil.startWith(matcherStr,"{S:") && CharSequenceUtil.endWith(matcherStr,"}")) {
                String signalId = matcherStr.replace( "{S:", "").replace("}", "");
                String[] split = signalId.split("\\.");//设备id.信号id
                RealTimeSignalItem realTimeSignal = realTimeSignalManager.getRealTimeSignalByKey(signalId);
                ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(Integer.valueOf(split[0]), Integer.valueOf(split[1]));
                if (ObjectUtil.isNotNull(realTimeSignal) && ObjectUtil.isNotNull(configSignalItem)) {
                    String signalValue = activeSignalManager.getCurrentValue(configSignalItem, realTimeSignal.getCurrentValue());
                    matcher.appendReplacement(stringBuilder, String.valueOf(signalValue));
                }else{
                    matcher.appendReplacement(stringBuilder, localeMessageSourceUtil.getMessage("common.field.signalNotExist"));
                }
            }
            //指标
            if (CharSequenceUtil.startWith(matcherStr,"{C:") && CharSequenceUtil.endWith(matcherStr,"}")) {
                String complexIndexId = matcherStr.replace( "{C:", "").replace("}", "");
                LiveComplexIndex liveComplexIndex = liveComplexIndexManager.findLiveComplexIndexById(Integer.valueOf(complexIndexId));
                if (ObjectUtil.isNotNull(liveComplexIndex)) {
                    matcher.appendReplacement(stringBuilder,liveComplexIndex.getCurrentValue() + liveComplexIndex.getUnit());
                }else {
                    matcher.appendReplacement(stringBuilder, localeMessageSourceUtil.getMessage("common.field.complexIndexNotExist"));
                }
            }
        }
        matcher.appendTail(stringBuilder);
        return stringBuilder.toString();
    }
}
