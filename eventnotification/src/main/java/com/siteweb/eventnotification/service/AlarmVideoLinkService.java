package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.AlarmVideoLink;

import java.util.List;

public interface AlarmVideoLinkService {
    List<AlarmVideoLink> findAll();
    /**
     * 找到所有告警视频联动配置
     *
     * @return {@link List}<{@link AlarmVideoLink}>
     */
    List<AlarmVideoLink> findByDepartmentPermission(Integer userId);

    AlarmVideoLink findById(Integer id);
    AlarmVideoLink createAlarmVideoLink(AlarmVideoLink alarmVideoLink);

    AlarmVideoLink updateAlarmVideoLink(AlarmVideoLink alarmVideoLink);

    Integer deleteById(Integer id);

    /**
     * 同步兼容旧版本告警视频联动配置
     * @return boolean
     */
    boolean syncSetting();
}
