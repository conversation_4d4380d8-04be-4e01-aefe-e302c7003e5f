package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyLayoutDTO;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementConfigService
 * @createTime 2022-04-22 16:35:22
 */
public interface AlarmNotifyElementConfigService {

    List<AlarmNotifyElementConfigDTO> batchSaveAlarmNotifyElementConfig(AlarmNotifyLayoutDTO alarmNotifyLayoutDTO);

    AlarmNotifyElementConfigDTO findAlarmNotifyElementConfigDTOByAlarmNotifyElementConfigId(Integer alarmNotifyElementConfigId);

    List<AlarmNotifyElementConfigDTO> findAlarmNotifyElementConfigDTOsByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    void deleteAlarmNotifyElementConfigByAlarmNotifyElementConfigId(Integer alarmNotifyElementConfigId);
}
