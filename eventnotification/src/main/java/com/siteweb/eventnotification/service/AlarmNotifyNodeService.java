package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyNode;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyNodeService
 * @createTime 2022-04-24 08:59:21
 */
public interface AlarmNotifyNodeService {

    int createAlarmNotifyNode(AlarmNotifyNode alarmNotifyNode);

    AlarmNotifyNode findById(Integer alarmNotifyNodeId);

    List<AlarmNotifyNodeDTO> findAlarmNotifyNodesByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    int updateAlarmNotifyNode(AlarmNotifyNode alarmNotifyNode);

    void deleteAlarmNotifyNodeById(Integer alarmNotifyNodeId);
}
