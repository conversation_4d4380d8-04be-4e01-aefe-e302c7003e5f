package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.SafeMessage;

import java.util.List;

public interface SafeMessageRecordService {
    /**
     * 查找今天为发送过的记录
     * @param safeMessageList
     * @return {@link List}<{@link SafeMessage}>
     */
    List<SafeMessage> findTodayNoSendRecord(List<SafeMessage> safeMessageList);
    void saveMessageRecord(Integer safeMessageId, String content, String receiver, String remark);
}
