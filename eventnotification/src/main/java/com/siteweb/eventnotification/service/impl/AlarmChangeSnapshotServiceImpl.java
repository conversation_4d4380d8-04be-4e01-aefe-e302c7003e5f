package com.siteweb.eventnotification.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.DateUtil;
import com.siteweb.eventnotification.dto.AlarmChangeSnapshotFilterDTO;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.springframework.data.domain.Page;
import com.siteweb.admin.entity.Region;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.eventnotification.dto.AllEventDTO;
import com.siteweb.eventnotification.entity.AlarmChangeSnapshot;
import com.siteweb.eventnotification.mapper.AlarmChangeSnapshotMapper;
import com.siteweb.eventnotification.service.AlarmChangeSnapshotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class AlarmChangeSnapshotServiceImpl implements AlarmChangeSnapshotService {
    @Autowired
    AlarmChangeSnapshotMapper alarmChangeSnapshotMapper;

    @Autowired
    RegionMapService regionMapService;

    @Autowired
    RegionService regionService;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertAlarmChangeSnapshot(List<AlarmChangeSnapshot> alarmChangeSnapshotList) {
        return alarmChangeSnapshotMapper.batchInsertAlarmChangeSnapshot(alarmChangeSnapshotList);
    }

    @Override
    public List<AlarmChangeSnapshot> findAlarmChangeSnapshotBySequenceId(String sequenceId) {
        return alarmChangeSnapshotMapper.selectList(Wrappers.lambdaQuery(AlarmChangeSnapshot.class)
                .eq(AlarmChangeSnapshot::getSequenceId, sequenceId));
    }

    @Override
    public Page<AllEventDTO> findAllEventDTOBySnapshot(Integer userId, Pageable pageable, AlarmChangeSnapshotFilterDTO alarmChangeSnapshotFilterDTO) {
        List<AllEventDTO> dtoList = new ArrayList<>();
        List<AlarmChangeSnapshot> snapshotList = alarmChangeSnapshotMapper.selectList(null);

        if (snapshotList.isEmpty()) {
            return new PageImpl<>(dtoList, pageable, 0);
        }

        List<Integer> resourceStructureIds = null;

        List<Region> regions = regionService.findAllRegionsByUserId(userId);
        //如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
        if (!regions.stream().anyMatch(o -> o.getRegionId().equals(-1))) {
            List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
            List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);
            //EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
            resourceStructureIds = regionMaps.stream().filter(o -> o.getEquipmentId().equals(-1)).map(RegionMap::getResourceStructureId).toList();
        }
        dtoList = alarmChangeSnapshotMapper.findAllEventDTOBySnapshot(resourceStructureIds, snapshotList, alarmChangeSnapshotFilterDTO);

        List<AllEventDTO> slice = dtoList.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        for (AllEventDTO eventDTO : slice) {
            eventDTO.setDuration(DateUtil.getTimeDifference(eventDTO.getStartTime(), eventDTO.getEndTime()));
            eventDTO.setEventValue(this.getTwoDecimal(eventDTO.getEventValue()));
            eventDTO.setEquipmentPosition(resourceStructureManager.getFullPath(eventDTO.getResourceStructureId()));
        }
        return new PageImpl<>(slice, pageable, dtoList.size());
    }

    public double getTwoDecimal(Double doubleValue) {
        if (doubleValue != null) {
            String strResult = String.format("%.2f", doubleValue);
            return Double.parseDouble(strResult);
        } else {
            return 0;
        }
    }
}