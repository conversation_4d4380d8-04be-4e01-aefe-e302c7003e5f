package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.eventnotification.dto.AlarmNotifyRecordDto;
import com.siteweb.eventnotification.entity.AlarmNotifyRecord;
import com.siteweb.eventnotification.mapper.AlarmNotifyRecordMapper;
import com.siteweb.eventnotification.service.AlarmNotifyRecordService;
import com.siteweb.eventnotification.vo.AlarmNotifyRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyRecordServiceImpl
 * @createTime 2022-04-24 11:12:44
 */
@Service
public class AlarmNotifyRecordServiceImpl implements AlarmNotifyRecordService {

    @Autowired
    AlarmNotifyRecordMapper alarmNotifyRecordMapper;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    public int createAlarmNotifyRecord(AlarmNotifyRecord alarmNotifyRecord) {
        alarmNotifyRecord.setSendTime(new Date());
        return alarmNotifyRecordMapper.insert(alarmNotifyRecord);
    }

    @Override
    public List<AlarmNotifyRecord> findByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        return alarmNotifyRecordMapper.selectList(new QueryWrapper<AlarmNotifyRecord>().eq("AlarmNotifyConfigId", alarmNotifyConfigId));
    }

    @Override
    public Page<AlarmNotifyRecord> findAlarmNotifyRecordsByKeywords(String keywords, Pageable pageable) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<AlarmNotifyRecord> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageable.getPageNumber() + 1, pageable.getPageSize());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<AlarmNotifyRecord> alarmNotifyRecordPage = alarmNotifyRecordMapper.findAlarmNotifyRecordsByKeywords(page,keywords);
        return new PageImpl<>(alarmNotifyRecordPage.getRecords(), pageable, alarmNotifyRecordPage.getTotal());
    }

    @Override
    public List<AlarmNotifyRecordDto> findByReportParam(AlarmNotifyRecordVO alarmNotifyRecordVO, Collection<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return alarmNotifyRecordMapper.findByReportParam(alarmNotifyRecordVO,equipmentIds);
    }

    @Override
    public IPage<AlarmNotifyRecordDto> findByReportParamPage(IPage<AlarmNotifyRecordDto> page, AlarmNotifyRecordVO recordParam, Collection<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return page;
        }
        return alarmNotifyRecordMapper.findByReportParamPage(page,recordParam,equipmentIds);
    }

    @Override
    public long findByReportParamCount(AlarmNotifyRecordVO recordParam, Collection<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return 0;
        }
        return alarmNotifyRecordMapper.findByReportParamCount(recordParam,equipmentIds);
    }
}
