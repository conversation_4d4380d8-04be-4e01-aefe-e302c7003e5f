package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.eventnotification.entity.AlarmVideoLink;
import com.siteweb.eventnotification.entity.AlarmVideoLinkEvent;
import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;
import com.siteweb.eventnotification.mapper.AlarmVideoLinkMapMapper;
import com.siteweb.eventnotification.mapper.AlarmVideoLinkMapper;
import com.siteweb.eventnotification.service.AlarmVideoLinkEventService;
import com.siteweb.eventnotification.service.AlarmVideoLinkMapService;
import com.siteweb.eventnotification.service.AlarmVideoLinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class AlarmVideoLinkServiceImpl implements AlarmVideoLinkService {
    @Autowired
    AlarmVideoLinkMapper alarmVideoLinkMapper;
    @Autowired
    AlarmVideoLinkMapMapper alarmVideoLinkMapMapper;
    @Autowired
    AlarmVideoLinkEventService alarmVideoLinkEventService;
    @Autowired
    AlarmVideoLinkMapService alarmVideoLinkMapService;
    @Autowired
    DepartmentPermissionService departmentPermissionService;

    @Override
    public List<AlarmVideoLink> findAll() {
        return alarmVideoLinkMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<AlarmVideoLink> findByDepartmentPermission(Integer userId) {
        Set<Integer> departmentIds = departmentPermissionService.findDepartmentPermissionByUserId(userId);
        if (CollUtil.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return alarmVideoLinkMapper.selectList(Wrappers.lambdaQuery(AlarmVideoLink.class)
                                                       .in(AlarmVideoLink::getDepartmentId, departmentIds));
    }

    @Override
    public AlarmVideoLink findById(Integer id) {
        AlarmVideoLink alarmVideoLink = alarmVideoLinkMapper.selectById(id);
        alarmVideoLink.setAlarmVideoLinkMapList(alarmVideoLinkMapService.findByAlarmVideoLinkId(id));
        return alarmVideoLink;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlarmVideoLink createAlarmVideoLink(AlarmVideoLink alarmVideoLink) {
        alarmVideoLinkMapper.insert(alarmVideoLink);
        alarmVideoLinkMapService.batchInsert(alarmVideoLink.getAlarmVideoLinkId(),alarmVideoLink.getAlarmVideoLinkMapList());
        return alarmVideoLink;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlarmVideoLink updateAlarmVideoLink(AlarmVideoLink alarmVideoLink) {
        alarmVideoLinkMapper.updateById(alarmVideoLink);
        alarmVideoLinkMapService.batchInsert(alarmVideoLink.getAlarmVideoLinkId(),alarmVideoLink.getAlarmVideoLinkMapList());
        return alarmVideoLink;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteById(Integer id) {
        Integer result = alarmVideoLinkMapper.deleteById(id);
        alarmVideoLinkMapService.deleteByAlarmVideoLinkId(id);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncSetting() {
        List<AlarmVideoLink>  alarmVideoLinkList= this.findAll();
        for (AlarmVideoLink alarmVideoLink : alarmVideoLinkList) {
            List<AlarmVideoLinkMap> byAlarmVideoLinkId = alarmVideoLinkMapService.findByAlarmVideoLinkId(alarmVideoLink.getAlarmVideoLinkId());
            for (AlarmVideoLinkMap alarmVideoLinkMap : byAlarmVideoLinkId) {
                if (CharSequenceUtil.isBlank(alarmVideoLinkMap.getCameraIds())) {
                    continue;
                }
                List<AlarmVideoLinkEvent> alarmVideoLinkEventList = alarmVideoLinkEventService.findByalarmVideoLinkMapId(alarmVideoLinkMap.getAlarmVideoLinkMapId());
                long[] cameraIdArray = CharSequenceUtil.splitToLong(alarmVideoLinkMap.getCameraIds(), ",");
                for (long cameraId : cameraIdArray) {
                    for (AlarmVideoLinkEvent alarmVideoLinkEvent : alarmVideoLinkEventList) {
                        AlarmVideoLinkMap alarmVideoLinkMapCopy = BeanUtil.copyProperties(alarmVideoLinkMap, AlarmVideoLinkMap.class);
                        alarmVideoLinkMapCopy.setCameraId(cameraId);
                        alarmVideoLinkMapCopy.setEventId(alarmVideoLinkEvent.getEventId());
                        alarmVideoLinkMapCopy.setEventConditionId(alarmVideoLinkEvent.getEventConditionId());
                        alarmVideoLinkMapCopy.setAlarmVideoLinkMapId(0);
                        alarmVideoLinkMapCopy.setCameraIds(null);
                        alarmVideoLinkMapMapper.insert(alarmVideoLinkMapCopy);
                    }
                }
                alarmVideoLinkMapMapper.deleteById(alarmVideoLinkMap);
            }
        }
        return true;
    }
}
