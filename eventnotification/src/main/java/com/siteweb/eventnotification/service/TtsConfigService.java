package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.TtsConfig;

import java.util.List;

public interface TtsConfigService {
    List<TtsConfig> findAll();
    List<TtsConfig> findByKeys(List<String> ttsConfigKeyList);

    List<TtsConfig> batchUpdate(List<TtsConfig> ttsConfigList);

    TtsConfig findByKey(String ttsConfigKey);

    boolean findBooleanValue(String ttsConfigKey);
}
