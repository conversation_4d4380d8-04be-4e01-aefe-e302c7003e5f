package com.siteweb.eventnotification.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyLayoutDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyNodeDTO;
import com.siteweb.eventnotification.dto.AlarmNotifySegmentDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyConfig;
import com.siteweb.eventnotification.entity.AlarmNotifyElementConfig;
import com.siteweb.eventnotification.entity.AlarmNotifyNode;
import com.siteweb.eventnotification.entity.AlarmNotifySegment;
import com.siteweb.eventnotification.mapper.AlarmNotifyConfigMapper;
import com.siteweb.eventnotification.mapper.AlarmNotifyElementConfigMapper;
import com.siteweb.eventnotification.mapper.AlarmNotifyNodeMapper;
import com.siteweb.eventnotification.mapper.AlarmNotifySegmentMapper;
import com.siteweb.eventnotification.service.AlarmNotifyElementConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementConfigServiceImpl
 * @createTime 2022-04-22 16:44:47
 */
@Service
public class AlarmNotifyElementConfigServiceImpl implements AlarmNotifyElementConfigService {

    @Autowired
    AlarmNotifyConfigMapper alarmNotifyConfigMapper;

    @Autowired
    AlarmNotifyElementConfigMapper alarmNotifyElementConfigMapper;

    @Autowired
    AlarmNotifyNodeMapper alarmNotifyNodeMapper;

    @Autowired
    AlarmNotifySegmentMapper alarmNotifySegmentMapper;

    int saveAlarmNotifyElementConfig(AlarmNotifyElementConfig alarmNotifyElementConfig) {
        if (alarmNotifyElementConfig.getAlarmNotifyElementConfigId() != null) {
            alarmNotifyElementConfigMapper.updateById(alarmNotifyElementConfig);
            return alarmNotifyElementConfig.getAlarmNotifyElementConfigId();
        }
        alarmNotifyElementConfigMapper.insert(alarmNotifyElementConfig);
        return alarmNotifyElementConfig.getAlarmNotifyElementConfigId();
    }

    int saveAlarmNotifyNode(AlarmNotifyNode alarmNotifyNode) {
        if (alarmNotifyNode.getNodeId() != null) {
            alarmNotifyNodeMapper.updateById(alarmNotifyNode);
            return alarmNotifyNode.getNodeId();
        }
        alarmNotifyNodeMapper.insert(alarmNotifyNode);
        return alarmNotifyNode.getNodeId();
    }

    int saveAlarmNotifySegment(AlarmNotifySegment alarmNotifySegment) {
        if (alarmNotifySegment.getSegmentId() != null) {
            alarmNotifySegmentMapper.updateById(alarmNotifySegment);
            return alarmNotifySegment.getSegmentId();
        }
        alarmNotifySegmentMapper.insert(alarmNotifySegment);
        return alarmNotifySegment.getSegmentId();
    }

    @Override
    @Transactional
    public List<AlarmNotifyElementConfigDTO> batchSaveAlarmNotifyElementConfig(AlarmNotifyLayoutDTO alarmNotifyLayoutDTO) {
        AlarmNotifyConfig alarmNotifyConfig = alarmNotifyConfigMapper.selectById(alarmNotifyLayoutDTO.getAlarmNotifyConfigId());
        if (alarmNotifyConfig == null) {
            return new ArrayList<>();
        }
        HashMap<String, Integer> elementConfigIdHashMap = new HashMap<>();
        HashMap<String, Integer> nodeIdHashMap = new HashMap<>();
        HashMap<Integer, Integer> nodeElementConfigIdHashMap = new HashMap<>();
        HashSet<Integer> updatedElementConfigIdHashSet = new HashSet<>();
        for (AlarmNotifyElementConfigDTO dto : alarmNotifyLayoutDTO.getDtos()) {
            AlarmNotifyElementConfig elementConfig = new AlarmNotifyElementConfig();
            BeanUtils.copyProperties(dto, elementConfig);
            int newElementConfigId = this.saveAlarmNotifyElementConfig(elementConfig);
            elementConfigIdHashMap.put(dto.getUniqueId(), newElementConfigId);
            if (dto.getNodeDTOs() == null || dto.getNodeDTOs().isEmpty())
                continue;
            updatedElementConfigIdHashSet.add(newElementConfigId);
            for (AlarmNotifyNodeDTO nodeDTO : dto.getNodeDTOs()) {
                AlarmNotifyNode alarmNotifyNode = new AlarmNotifyNode();
                BeanUtils.copyProperties(nodeDTO, alarmNotifyNode);
                alarmNotifyNode.setAlarmNotifyElementConfigId(newElementConfigId);
                int newAlarmNotifyNodeId = this.saveAlarmNotifyNode(alarmNotifyNode);
                nodeIdHashMap.put(nodeDTO.getUniqueId(), newAlarmNotifyNodeId);
                nodeElementConfigIdHashMap.put(newAlarmNotifyNodeId, newElementConfigId);
            }
        }
        //处理已删除的告警通知控件
        List<AlarmNotifyElementConfig> alarmNotifyElementConfigs = alarmNotifyElementConfigMapper.selectList(new QueryWrapper<AlarmNotifyElementConfig>().eq("AlarmNotifyConfigId", alarmNotifyLayoutDTO.getAlarmNotifyConfigId()));
        for (AlarmNotifyElementConfig alarmNotifyElementConfig : alarmNotifyElementConfigs) {
            if (!updatedElementConfigIdHashSet.contains(alarmNotifyElementConfig.getAlarmNotifyElementConfigId())) {
                this.deleteAlarmNotifyElementConfigByAlarmNotifyElementConfigId(alarmNotifyElementConfig.getAlarmNotifyElementConfigId());
            }
        }
        //保存节点连线
        for (AlarmNotifyElementConfigDTO dto : alarmNotifyLayoutDTO.getDtos()) {
            dto.setAlarmNotifyElementConfigId(elementConfigIdHashMap.get(dto.getUniqueId()));
            if (dto.getNodeDTOs() == null || dto.getNodeDTOs().isEmpty())
                continue;
            for (AlarmNotifyNodeDTO nodeDTO : dto.getNodeDTOs()) {
                nodeDTO.setAlarmNotifyElementConfigId(dto.getAlarmNotifyElementConfigId());
                nodeDTO.setNodeId(nodeIdHashMap.get(nodeDTO.getUniqueId()));
                if (nodeDTO.getSegmentDTOs() != null) {
                    for (AlarmNotifySegmentDTO segmentDTO : nodeDTO.getSegmentDTOs()) {
                        AlarmNotifySegment alarmNotifySegment = new AlarmNotifySegment();
                        BeanUtils.copyProperties(segmentDTO, alarmNotifySegment);
                        alarmNotifySegment.setInputNodeId(nodeIdHashMap.get(segmentDTO.getInputNodeUniqueId()));
                        alarmNotifySegment.setInputElementConfigId(nodeDTO.getAlarmNotifyElementConfigId());
                        alarmNotifySegment.setOutputNodeId(nodeIdHashMap.get(segmentDTO.getOutputNodeUniqueId()));
                        alarmNotifySegment.setOutputElementConfigId(nodeElementConfigIdHashMap.get(alarmNotifySegment.getOutputNodeId()));
                        int newSegmentId = this.saveAlarmNotifySegment(alarmNotifySegment);
                        segmentDTO.setSegmentId(newSegmentId);
                        segmentDTO.setInputNodeId(newSegmentId);
                        segmentDTO.setOutputNodeId(newSegmentId);
                        segmentDTO.setInputElementConfigId(nodeDTO.getAlarmNotifyElementConfigId());
                        segmentDTO.setOutputElementConfigId(nodeElementConfigIdHashMap.get(alarmNotifySegment.getOutputNodeId()));
                    }
                }
            }
        }
        //保存前端布局展示需要的Layout信息
        alarmNotifyConfig.setLayout(alarmNotifyLayoutDTO.getLayout());
        alarmNotifyConfig.setUpdateTime(new Date());
        alarmNotifyConfigMapper.updateById(alarmNotifyConfig);
        return alarmNotifyLayoutDTO.getDtos();
    }

    @Override
    public AlarmNotifyElementConfigDTO findAlarmNotifyElementConfigDTOByAlarmNotifyElementConfigId(Integer alarmNotifyElementConfigId) {
        AlarmNotifyElementConfig alarmNotifyElementConfig = alarmNotifyElementConfigMapper.selectById(alarmNotifyElementConfigId);
        if (alarmNotifyElementConfig == null)
            return null;
        AlarmNotifyElementConfigDTO elementConfigDTO = new AlarmNotifyElementConfigDTO();
        BeanUtils.copyProperties(alarmNotifyElementConfig, elementConfigDTO);
        List<AlarmNotifyNode> alarmNotifyNodes = alarmNotifyNodeMapper.selectList(new QueryWrapper<AlarmNotifyNode>().eq("AlarmNotifyElementConfigId", alarmNotifyElementConfigId));
        List<AlarmNotifySegment> alarmNotifySegments = alarmNotifySegmentMapper.selectList(new QueryWrapper<AlarmNotifySegment>().eq("InputElementConfigId", alarmNotifyElementConfig.getAlarmNotifyElementConfigId()));
        List<AlarmNotifyNodeDTO> nodeDTOs = new ArrayList<>();
        for (AlarmNotifyNode alarmNotifyNode : alarmNotifyNodes) {
            AlarmNotifyNodeDTO nodeDTO = new AlarmNotifyNodeDTO();
            BeanUtils.copyProperties(alarmNotifyNode, nodeDTO);
            List<AlarmNotifySegmentDTO> segmentDTOList = new ArrayList<>();
            if ("right".equalsIgnoreCase(alarmNotifyNode.getNodeDirection())) {
                List<AlarmNotifySegment> tmpSegmentList = alarmNotifySegments.stream().filter(o -> alarmNotifyNode.getNodeId().equals(o.getInputNodeId())).toList();
                for (AlarmNotifySegment alarmNotifySegment : tmpSegmentList) {
                    AlarmNotifySegmentDTO segmentDTO = new AlarmNotifySegmentDTO();
                    BeanUtils.copyProperties(alarmNotifySegment, segmentDTO);
                    segmentDTOList.add(segmentDTO);
                }
            }
            nodeDTO.setSegmentDTOs(segmentDTOList);
            nodeDTOs.add(nodeDTO);
        }
        elementConfigDTO.setNodeDTOs(nodeDTOs);
        return elementConfigDTO;
    }

    @Override
    public List<AlarmNotifyElementConfigDTO> findAlarmNotifyElementConfigDTOsByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        List<AlarmNotifyElementConfigDTO> result = new ArrayList<>();
        List<AlarmNotifyElementConfig> alarmNotifyElementConfigs = alarmNotifyElementConfigMapper.selectList(new QueryWrapper<AlarmNotifyElementConfig>().eq("AlarmNotifyConfigId", alarmNotifyConfigId));
        for (AlarmNotifyElementConfig alarmNotifyElementConfig : alarmNotifyElementConfigs) {
            AlarmNotifyElementConfigDTO elementConfigDTO = new AlarmNotifyElementConfigDTO();
            BeanUtils.copyProperties(alarmNotifyElementConfig, elementConfigDTO);
            List<AlarmNotifyNode> alarmNotifyNodes = alarmNotifyNodeMapper.selectList(new QueryWrapper<AlarmNotifyNode>().eq("AlarmNotifyElementConfigId", alarmNotifyElementConfig.getAlarmNotifyElementConfigId()));
            List<AlarmNotifySegment> alarmNotifySegments = alarmNotifySegmentMapper.selectList(new QueryWrapper<AlarmNotifySegment>().eq("InputElementConfigId", alarmNotifyElementConfig.getAlarmNotifyElementConfigId()));
            List<AlarmNotifyNodeDTO> nodeDTOs = new ArrayList<>();
            for (AlarmNotifyNode alarmNotifyNode : alarmNotifyNodes) {
                AlarmNotifyNodeDTO nodeDTO = new AlarmNotifyNodeDTO();
                BeanUtils.copyProperties(alarmNotifyNode, nodeDTO);
                List<AlarmNotifySegmentDTO> segmentDTOList = new ArrayList<>();
                if ("right".equalsIgnoreCase(alarmNotifyNode.getNodeDirection())) {
                    List<AlarmNotifySegment> tmpSegmentList = alarmNotifySegments.stream().filter(o -> alarmNotifyNode.getNodeId().equals(o.getInputNodeId())).toList();
                    for (AlarmNotifySegment alarmNotifySegment : tmpSegmentList) {
                        AlarmNotifySegmentDTO segmentDTO = new AlarmNotifySegmentDTO();
                        BeanUtils.copyProperties(alarmNotifySegment, segmentDTO);
                        segmentDTOList.add(segmentDTO);
                    }
                }
                nodeDTO.setSegmentDTOs(segmentDTOList);
                nodeDTOs.add(nodeDTO);
            }
            elementConfigDTO.setNodeDTOs(nodeDTOs);
            result.add(elementConfigDTO);
        }
        return result;
    }

    @Override
    @Transactional
    public void deleteAlarmNotifyElementConfigByAlarmNotifyElementConfigId(Integer alarmNotifyElementConfigId) {
        alarmNotifyElementConfigMapper.deleteAlarmNotifyElementConfigByAlarmNotifyElementConfigId(alarmNotifyElementConfigId);
    }
}
