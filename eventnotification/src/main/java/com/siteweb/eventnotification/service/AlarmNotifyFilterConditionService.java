package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.entity.AlarmNotifyFilterCondition;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterConditionService
 * @createTime 2022-04-24 08:58:29
 */
public interface AlarmNotifyFilterConditionService {

    AlarmNotifyFilterCondition findById(Integer filterConditionId);

    List<AlarmNotifyFilterCondition> findAllAlarmNotifyFilterConditions();
}
