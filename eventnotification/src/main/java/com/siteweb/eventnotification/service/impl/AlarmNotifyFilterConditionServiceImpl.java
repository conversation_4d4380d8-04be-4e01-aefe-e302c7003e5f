package com.siteweb.eventnotification.service.impl;

import com.siteweb.eventnotification.entity.AlarmNotifyFilterCondition;
import com.siteweb.eventnotification.mapper.AlarmNotifyFilterConditionMapper;
import com.siteweb.eventnotification.service.AlarmNotifyFilterConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterConditionServiceImpl
 * @createTime 2022-04-24 09:02:57
 */
@Service
public class AlarmNotifyFilterConditionServiceImpl implements AlarmNotifyFilterConditionService {

    @Autowired
    AlarmNotifyFilterConditionMapper alarmNotifyFilterConditionMapper;

    @Override
    public AlarmNotifyFilterCondition findById(Integer filterConditionId) {
        return alarmNotifyFilterConditionMapper.selectById(filterConditionId);
    }

    @Override
    public List<AlarmNotifyFilterCondition> findAllAlarmNotifyFilterConditions() {
        return alarmNotifyFilterConditionMapper.selectList(null);
    }
}
