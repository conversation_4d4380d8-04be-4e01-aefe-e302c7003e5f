package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.model.SafeMessageContentItem;

import java.util.List;

public interface SafeMessageContentService {
    /**
     * 获取所有平安短信配置模板占位符
     * @return {@link List}<{@link SafeMessageContentItem}>
     */
    List<SafeMessageContentItem> findAllSafeMessageContentItems();

    /**
     * 解析安全消息模板内容
     *
     * @param safeMessageId   安全消息id
     * @param contentTemplate 内容模板
     * @return {@link String} 解析完成的结果
     */
    String analysisSafeMessageTemplate(String contentTemplate);
}
