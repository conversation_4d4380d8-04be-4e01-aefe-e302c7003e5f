package com.siteweb.eventnotification.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.eventnotification.dto.AlarmNotifyFilterRuleDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyFilterRule;
import com.siteweb.eventnotification.mapper.AlarmNotifyFilterRuleMapper;
import com.siteweb.eventnotification.service.AlarmNotifyFilterRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterRuleServiceImpl
 * @createTime 2022-04-24 09:10:34
 */
@Service
public class AlarmNotifyFilterRuleServiceImpl implements AlarmNotifyFilterRuleService {

    @Autowired
    AlarmNotifyFilterRuleMapper alarmNotifyFilterRuleMapper;

    @Override
    @Transactional
    public List<AlarmNotifyFilterRuleDTO> batchSaveAlarmNotifyFilterRules(List<AlarmNotifyFilterRuleDTO> dtos) {
        if (dtos.isEmpty())
            return new ArrayList<>();
        deleteByAlarmNotifyConfigId(dtos.get(0).getAlarmNotifyConfigId());
        for (AlarmNotifyFilterRuleDTO dto : dtos) {
            AlarmNotifyFilterRule filterRule = new AlarmNotifyFilterRule();
            filterRule.setAlarmNotifyConfigId(dto.getAlarmNotifyConfigId());
            filterRule.setFilterConditionId(dto.getFilterConditionId());
            filterRule.setFilterParameter(dto.getFilterParameter());
            alarmNotifyFilterRuleMapper.insert(filterRule);
        }
        return findByAlarmNotifyConfigId(dtos.get(0).getAlarmNotifyConfigId());
    }

    @Override
    public AlarmNotifyFilterRuleDTO findByFilterRuleId(Integer alarmNotifyFilterRuleId) {
        AlarmNotifyFilterRule filterRule = alarmNotifyFilterRuleMapper.selectById(alarmNotifyFilterRuleId);
        if (filterRule == null)
            return null;
        AlarmNotifyFilterRuleDTO filterRuleDTO = new AlarmNotifyFilterRuleDTO();
        filterRuleDTO.setAlarmNotifyFilterRuleId(filterRule.getAlarmNotifyFilterRuleId());
        filterRuleDTO.setAlarmNotifyConfigId(filterRule.getAlarmNotifyConfigId());
        filterRuleDTO.setFilterConditionId(filterRule.getFilterConditionId());
        filterRuleDTO.setFilterParameter(filterRule.getFilterParameter());
        return filterRuleDTO;
    }

    @Override
    public List<AlarmNotifyFilterRuleDTO> findByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        List<AlarmNotifyFilterRule> alarmNotifyFilterRules = alarmNotifyFilterRuleMapper.selectList(new QueryWrapper<AlarmNotifyFilterRule>().eq("AlarmNotifyConfigId", alarmNotifyConfigId));
        List<AlarmNotifyFilterRuleDTO> result = new ArrayList<>();
        if (alarmNotifyFilterRules.isEmpty())
            return result;
        for (AlarmNotifyFilterRule filterRule : alarmNotifyFilterRules) {
            AlarmNotifyFilterRuleDTO dto = new AlarmNotifyFilterRuleDTO();
            dto.setAlarmNotifyFilterRuleId(filterRule.getAlarmNotifyFilterRuleId());
            dto.setAlarmNotifyConfigId(filterRule.getAlarmNotifyConfigId());
            dto.setFilterConditionId(filterRule.getFilterConditionId());
            dto.setFilterParameter(filterRule.getFilterParameter());
            result.add(dto);
        }
        return result;
    }

    @Override
    @Transactional
    public void deleteByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        alarmNotifyFilterRuleMapper.delete(new QueryWrapper<AlarmNotifyFilterRule>().eq("AlarmNotifyConfigId", alarmNotifyConfigId));
    }
}
