package com.siteweb.eventnotification.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.eventnotification.entity.AlarmVideoLinkEvent;
import com.siteweb.eventnotification.mapper.AlarmVideoLinkEventMapper;
import com.siteweb.eventnotification.service.AlarmVideoLinkEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/02/06
 */
@Service
public class AlarmVideoLinkEventServiceImpl implements AlarmVideoLinkEventService {
    @Autowired
    private AlarmVideoLinkEventMapper alarmVideoLinkEventMapper;

    @Override
    public List<AlarmVideoLinkEvent> findByalarmVideoLinkMapId(Integer alarmVideoLinkMapId) {
        return alarmVideoLinkEventMapper.selectList(Wrappers.lambdaQuery(AlarmVideoLinkEvent.class)
                                                            .eq(AlarmVideoLinkEvent::getAlarmVideoLinkMapId, alarmVideoLinkMapId));
    }

    @Override
    public int create(AlarmVideoLinkEvent alarmVideoLinkEvent) {
        return alarmVideoLinkEventMapper.insert(alarmVideoLinkEvent);
    }

    @Override
    public int deleteByAlarmVideoLinkMapIds(List<Integer> alarmVideoLinkMapIdList) {
        if (CollUtil.isEmpty(alarmVideoLinkMapIdList)) {
            return 0;
        }
        return alarmVideoLinkEventMapper.delete(Wrappers.lambdaQuery(AlarmVideoLinkEvent.class)
                                                        .in(AlarmVideoLinkEvent::getAlarmVideoLinkMapId, alarmVideoLinkMapIdList));
    }
}
