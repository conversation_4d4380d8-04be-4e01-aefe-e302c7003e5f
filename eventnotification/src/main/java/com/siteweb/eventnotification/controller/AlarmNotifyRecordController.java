package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.service.AlarmNotifyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyRecordController
 * @createTime 2022-04-26 10:49:44
 */
@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyRecordController", tags = {"AlarmNotifyRecord操作接口"})
public class AlarmNotifyRecordController {

    @Autowired
    AlarmNotifyRecordService alarmNotifyRecordService;

    /**
     * GET  /alarmnotifyrecords  query pageable alarmNotifyRecords
     *
     * @param keywords keywords
     * @param pageable pageable
     * @return the ResponseEntity with status 200 (OK) and with the body of the pageable alarmNotifyRecords
     */
    @ApiOperation(value = "根据keywords分页查询AlarmNotifyRecord实体")
    @GetMapping(value = "/alarmnotifyrecords",
            params = "keywords",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAlarmNotifyRecordsByKeywords(@RequestParam String keywords, Pageable pageable) {
        return ResponseHelper.successful(alarmNotifyRecordService.findAlarmNotifyRecordsByKeywords(keywords, pageable), HttpStatus.OK);
    }
}
