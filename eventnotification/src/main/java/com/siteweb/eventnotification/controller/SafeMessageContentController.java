package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.service.SafeMessageContentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "平安短信内容模板配置控制器", tags = {"平安短信内容模板配置控制器"})
public class SafeMessageContentController {
    @Autowired
    private SafeMessageContentService safeMessageContentService;

    @ApiOperation("获取所有平安短信模板")
    @GetMapping("/safemessagecontentitem")
    public ResponseEntity<ResponseResult> getAllSafeMessageContentItems(){
        return ResponseHelper.successful(safeMessageContentService.findAllSafeMessageContentItems());
    }
}
