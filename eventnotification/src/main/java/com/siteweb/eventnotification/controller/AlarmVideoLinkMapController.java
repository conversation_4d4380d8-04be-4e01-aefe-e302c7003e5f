package com.siteweb.eventnotification.controller;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.dto.AlarmVideoLinkMapImportDTO;
import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;
import com.siteweb.eventnotification.service.AlarmVideoLinkMapService;
import com.siteweb.eventnotification.vo.AlarmVideoLinkMapVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "告警视频联动映射关系接口", tags = {"告警视频联动映射关系接口"})
public class AlarmVideoLinkMapController {
    @Autowired
    AlarmVideoLinkMapService alarmVideoLinkMapService;

    @ApiOperation("获取告警视频联动实列配置")
    @GetMapping("/alarmvideolinkmap")
    public ResponseEntity<ResponseResult> getAlarmVideoLinkMap(Integer alarmVideoLinkId) {
        List<AlarmVideoLinkMap> alarmVideoLinkMapList = alarmVideoLinkMapService.findByAlarmVideoLinkId(alarmVideoLinkId);
        List<AlarmVideoLinkMapVO> alarmVideoLinkMapVOList = BeanUtil.copyToList(alarmVideoLinkMapList, AlarmVideoLinkMapVO.class);
        return ResponseHelper.successful(alarmVideoLinkMapVOList);
    }

    @ApiOperation("批量导入告警视频联动实列配置")
    @PostMapping(value = "/alarmvideolinkmap/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importAlarmVideoLinkMap(@Valid @RequestBody List<AlarmVideoLinkMapImportDTO> alarmVideoLinkMapImportDTOList) {
        return ResponseHelper.successful(alarmVideoLinkMapService.importAlarmVideoLinkMap(alarmVideoLinkMapImportDTOList));
    }

    @ApiOperation("批量导出告警视频联动实列配置")
    @PostMapping(value = "/alarmvideolinkmap/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> exportAlarmVideoLinkMap(@Valid @RequestBody List<AlarmVideoLinkMap> alarmVideoLinkMapList) {
        return ResponseHelper.successful(alarmVideoLinkMapService.exportAlarmVideoLinkMap(alarmVideoLinkMapList));
    }
}
