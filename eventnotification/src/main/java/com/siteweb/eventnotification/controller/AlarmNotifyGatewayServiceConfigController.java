package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDTO;
import com.siteweb.eventnotification.service.AlarmNotifyGatewayServiceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyGatewayServiceConfigController", tags = {"AlarmNotifyGatewayServiceConfigController操作接口"})
public class AlarmNotifyGatewayServiceConfigController {

    @Autowired
    AlarmNotifyGatewayServiceConfigService alarmNotifyGatewayServiceConfigService;


    @ApiOperation(value = "根据alarmNotifyConfigId查询AlarmNotifyGatewayServiceConfigDTO实体")
    @GetMapping("/alarmnotifygatewayserviceconfig/{alarmNotifyConfigId}")
    public ResponseEntity<ResponseResult> getAlarmNotifyGatewayServiceConfigByAlarmNotifyConfigId(@PathVariable Integer alarmNotifyConfigId) {
        return ResponseHelper.successful(alarmNotifyGatewayServiceConfigService.findAlarmNotifyGatewayServicesByAlarmNotifyConfigId(alarmNotifyConfigId), HttpStatus.OK);
    }


    @ApiOperation(value = "批量保存alarmnotifygatewayserviceconfig")
    @PostMapping(value = "/alarmnotifygatewayserviceconfig")
    public ResponseEntity<ResponseResult> batchSaveAlarmNotifyGatewayServiceConfig(@Valid @RequestBody AlarmNotifyGatewayServiceConfigDTO alarmNotifyGatewayServiceConfigDTO) {
        return ResponseHelper.successful(alarmNotifyGatewayServiceConfigService.batchSaveAlarmNotifyGatewayServiceConfig(alarmNotifyGatewayServiceConfigDTO), HttpStatus.OK);
    }


}