package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.service.AlarmNotifyFilterConditionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterConditionController
 * @createTime 2022-04-26 09:18:59
 */
@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyFilterConditionController", tags = {"AlarmNotifyFilterCondition操作接口"})
public class AlarmNotifyFilterConditionController {

    @Autowired
    AlarmNotifyFilterConditionService alarmNotifyFilterConditionService;

    /**
     * GET  /alarmnotifyfilterconditions  query alarmNotifyConditions
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the alarmNotifyConditions
     */
    @ApiOperation(value = "查询所有AlarmNotifyConfig实体")
    @GetMapping("/alarmnotifyfilterconditions")
    public ResponseEntity<ResponseResult> getAllAlarmNotifyConditions() {
        return ResponseHelper.successful(alarmNotifyFilterConditionService.findAllAlarmNotifyFilterConditions(), HttpStatus.OK);
    }
}
