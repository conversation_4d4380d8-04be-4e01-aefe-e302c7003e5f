package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.dto.AlarmNotifyFilterRuleDTO;
import com.siteweb.eventnotification.service.AlarmNotifyFilterRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyFilterRuleController
 * @createTime 2022-04-26 09:32:51
 */
@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyFilterRuleController", tags = {"AlarmNotifyFilterRule操作接口"})
public class AlarmNotifyFilterRuleController {

    @Autowired
    AlarmNotifyFilterRuleService alarmNotifyFilterRuleService;

    /**
     * GET  /alarmnotifyfilterrules/:alarmNotifyFilterRuleId :  query alarmNotifyFilterRuleDTO by alarmNotifyFilterRuleId
     *
     * @return the ResponseEntity with status 200 (OK) or status 404 when not found
     */
    @ApiOperation(value = "根据AlarmNotifyFilterRuleId查询AlarmNotifyFilterRuleDTO实体")
    @GetMapping("/alarmnotifyfilterrules/{alarmNotifyFilterRuleId}")
    public ResponseEntity<ResponseResult> getAlarmNotifyFilterConditionRulesById(@PathVariable Integer alarmNotifyFilterRuleId) {
        AlarmNotifyFilterRuleDTO alarmNotifyFilterRuleDTO = alarmNotifyFilterRuleService.findByFilterRuleId(alarmNotifyFilterRuleId);
        return Optional.ofNullable(alarmNotifyFilterRuleDTO)
                .map(result -> ResponseHelper.successful(alarmNotifyFilterRuleDTO, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * GET  /alarmnotifyfilterrules :  query alarmNotifyFilterRuleDTOs by alarmNotifyConfigId
     *
     * @return the ResponseEntity with status 200 (OK) or status 404 when not found
     */
    @ApiOperation(value = "根据AlarmNotifyConfigId查询AlarmNotifyFilterRuleDTO实体")
    @GetMapping(value = "/alarmnotifyfilterrules", params = "alarmNotifyConfigId")
    public ResponseEntity<ResponseResult> getAlarmNotifyFilterConditionRulesByAlarmNotifyConfigId(@RequestParam Integer alarmNotifyConfigId) {
        return ResponseHelper.successful(alarmNotifyFilterRuleService.findByAlarmNotifyConfigId(alarmNotifyConfigId), HttpStatus.OK);
    }

    /**
     * POST  /alarmnotifyfilterrules  post alarmNotifyFilterRuleDTOs
     *
     * @param dtos the alarmNotifyFilterRuleDTO to create
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "新增AlarmNotifyFilterRule实体")
    @PostMapping(value = "/alarmnotifyfilterrules")
    public ResponseEntity<ResponseResult> batchSaveAlarmNotifyFilterRule(@Valid @RequestBody List<AlarmNotifyFilterRuleDTO> dtos) {
        return ResponseHelper.successful(alarmNotifyFilterRuleService.batchSaveAlarmNotifyFilterRules(dtos), HttpStatus.OK);
    }

    /**
     * DELETE /alarmnotifyfilterrules/:alarmNotifyConfigId : Delete AlarmNotifyFilterRule by alarmNotifyConfigId.
     *
     * @param alarmNotifyConfigId the alarmNotifyConfigId of the AlarmNotifyFilterRule
     * @return the ResponseEntity with status 200 (OK) or status 404 (when alarmNotifyConfigId not found)
     */
    @ApiOperation(value = "按AlarmNotifyConfigId删除AlarmNotifyFilterRule实体")
    @DeleteMapping(value = "/alarmnotifyfilterrules", params = "alarmNotifyConfigId")
    public ResponseEntity<ResponseResult> deleteAlarmNotifyFilterRule(@RequestParam Integer alarmNotifyConfigId) {
        List<AlarmNotifyFilterRuleDTO> alarmNotifyFilterRuleDTOS = alarmNotifyFilterRuleService.findByAlarmNotifyConfigId(alarmNotifyConfigId);
        if (alarmNotifyFilterRuleDTOS.isEmpty()) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        alarmNotifyFilterRuleService.deleteByAlarmNotifyConfigId(alarmNotifyConfigId);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
