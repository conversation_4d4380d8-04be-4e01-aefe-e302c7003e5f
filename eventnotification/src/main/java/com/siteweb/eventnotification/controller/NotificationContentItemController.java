package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.service.NotificationContentItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> zhou
 * @description NotificationContentItemController
 * @createTime 2022-04-26 08:37:46
 */
@RestController
@RequestMapping("/api")
@Api(value = "NotificationContentItemController", tags = {"NotificationContentItem操作接口"})
public class NotificationContentItemController {

    @Autowired
    NotificationContentItemService notificationContentItemService;

    /**
     * GET  /notificationcontentitems  query notificationContentItems
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the notificationContentItems
     */
    @ApiOperation(value = "查询所有NotificationContentItem")
    @GetMapping(value = "/notificationcontentitems")
    public ResponseEntity<ResponseResult> getAllNotificationContentItems() {
        return ResponseHelper.successful(notificationContentItemService.getAllNotificationContentItems(), HttpStatus.OK);
    }
}
