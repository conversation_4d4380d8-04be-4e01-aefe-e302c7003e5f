package com.siteweb.eventnotification.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.entity.Notification;
import com.siteweb.eventnotification.service.NotificationReceiverService;
import com.siteweb.eventnotification.service.NotificationService;
import com.siteweb.eventnotification.vo.NotificationListVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2023/3/18 17:55
 */
@RestController
@RequestMapping("/api")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;
    @Autowired
    private NotificationReceiverService notificationReceiverService;

    @ApiOperation("获取当前用户所有消息分页")
    @GetMapping("/notification")
    public ResponseEntity<ResponseResult> getListByPage(Pageable pageable, Integer readed) {
        IPage<Notification> listByPage = notificationService.findListByPage(pageable, readed);
        Page<NotificationListVO> result = new Page<>(listByPage.getCurrent(), listByPage.getSize(), listByPage.getTotal());
        result.setRecords(BeanUtil.copyToList(listByPage.getRecords(), NotificationListVO.class));
        return ResponseHelper.successful(result);
    }

    @ApiOperation("已读")
    @RequestMapping(value = "/notification/readed/{notificationIds}", method = {RequestMethod.PUT, RequestMethod.POST})
    public ResponseEntity<ResponseResult> notificationReaded(@PathVariable("notificationIds") String notificationIdsStr) {
        List<Integer> notificationIds = Arrays.stream(notificationIdsStr.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        notificationReceiverService.notificationReaded(notificationIds);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除")
    @RequestMapping(value = "/notification/remove/{notificationIds}", method = {RequestMethod.DELETE, RequestMethod.POST})
    public ResponseEntity<ResponseResult> notificationRemove(@PathVariable("notificationIds") String notificationIdsStr) {
        List<Integer> notificationIds = Arrays.stream(notificationIdsStr.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        notificationReceiverService.notificationRemove(notificationIds);
        return ResponseHelper.successful();
    }

    @ApiOperation("获取推送设备种类条件")
    @GetMapping("/notification/equipmentcategory")
    public ResponseEntity<ResponseResult> getGetuiPushEquipmentCategory() {
        return ResponseHelper.successful(notificationService.findGetuiPushEquipmentCategory());
    }

    @ApiOperation("获取推送事件类别条件")
    @GetMapping("/notification/eventcategory")
    public ResponseEntity<ResponseResult> getGetuiPushEventCategory() {
        return ResponseHelper.successful(notificationService.findGetuiPushEventCategory());
    }

    @ApiOperation("获取当前用户未读消息个数")
    @GetMapping("/notification/unreadcount")
    public ResponseEntity<ResponseResult> unreadCount() {
        return ResponseHelper.successful(notificationReceiverService.findUnreadNotificationCountsByUserId(TokenUserUtil.getLoginUserId()));
    }

}
