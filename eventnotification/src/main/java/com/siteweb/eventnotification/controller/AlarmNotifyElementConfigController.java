package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.dto.AlarmNotifyLayoutDTO;
import com.siteweb.eventnotification.service.AlarmNotifyElementConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementConfigController
 * @createTime 2022-04-26 08:45:57
 */
@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyElementConfigController", tags = {"AlarmNotifyElementConfig操作接口"})
public class AlarmNotifyElementConfigController {

    @Autowired
    AlarmNotifyElementConfigService alarmNotifyElementConfigService;

    /**
     * GET  /alarmnotifyelementconfigs  query alarmNotifyElementConfigDTOs by alarmNotifyConfigId
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the alarmNotifyElementConfigDTOs
     */
    @ApiOperation(value = "根据alarmNotifyConfigId查询AlarmNotifyElementConfigDTO实体")
    @GetMapping(value = "/alarmnotifyelementconfigs", produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"alarmNotifyConfigId"})
    public ResponseEntity<ResponseResult> getAlarmNotifyElementConfigDTOsByAlarmNotifyConfigId(@RequestParam Integer alarmNotifyConfigId) {
        return ResponseHelper.successful(alarmNotifyElementConfigService.findAlarmNotifyElementConfigDTOsByAlarmNotifyConfigId(alarmNotifyConfigId), HttpStatus.OK);
    }

    /**
     * GET  /alarmnotifyelementconfigs/:alarmNotifyElementConfigId : query alarmNotifyElementConfigDTO by alarmNotifyElementConfigId
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the alarmNotifyElementConfigDTO
     */
    @ApiOperation(value = "根据alarmNotifyElementConfigId查询AlarmNotifyElementConfigDTO实体")
    @GetMapping("/alarmnotifyelementconfigs/{alarmNotifyElementConfigId}")
    public ResponseEntity<ResponseResult> getAlarmNotifyElementConfigById(@PathVariable Integer alarmNotifyElementConfigId) {
        return ResponseHelper.successful(alarmNotifyElementConfigService.findAlarmNotifyElementConfigDTOByAlarmNotifyElementConfigId(alarmNotifyElementConfigId), HttpStatus.OK);
    }

    /**
     * POST  /alarmnotifyconfigs  post AlarmNotifyLayoutDTO
     *
     * @param alarmNotifyLayoutDTO the alarmNotifyLayoutDTO to create
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "保存AlarmNotifyLayoutDTO实体")
    @PostMapping(value = "/alarmnotifyelementconfigs")
    public ResponseEntity<ResponseResult> batchSaveAlarmNotifyElementConfig(@Valid @RequestBody AlarmNotifyLayoutDTO alarmNotifyLayoutDTO) {
        return ResponseHelper.successful(alarmNotifyElementConfigService.batchSaveAlarmNotifyElementConfig(alarmNotifyLayoutDTO), HttpStatus.OK);
    }

    /**
     * DELETE /alarmnotifyelementconfigs/:alarmNotifyElementConfigId : Delete alarmNotifyElementConfig by alarmNotifyElementConfigId.
     *
     * @param alarmNotifyElementConfigId the alarmNotifyElementConfigId of the alarmNotifyElementConfig
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "按AlarmNotifyElementConfigId删除AlarmNotifyElementConfig实体")
    @DeleteMapping(value = "/alarmnotifyelementconfigs/{alarmNotifyElementConfigId}")
    public ResponseEntity<ResponseResult> deleteAlarmNotifyElementConfig(@PathVariable Integer alarmNotifyElementConfigId) {
        alarmNotifyElementConfigService.deleteAlarmNotifyElementConfigByAlarmNotifyElementConfigId(alarmNotifyElementConfigId);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
