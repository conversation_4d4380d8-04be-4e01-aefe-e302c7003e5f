package com.siteweb.eventnotification.controller;


import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.dto.TtsContinueBroadcastRequestDTO;
import com.siteweb.eventnotification.dto.TtsMessageRequestDTO;
import com.siteweb.eventnotification.textnotify.TextNotifyManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "AlarmTextNotifyWebSocketController", tags = {"AlarmTextNotifyWebSocketController操作接口"})
@Slf4j
public class AlarmTextNotifyHttpController {
    @Autowired
    TextNotifyManager textNotifyManager;

    @ApiOperation(value = "获取当前用户应当播报的TTS消息")
    @GetMapping(value = "/ttscurrentmsgs",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getUserTtsCurrentMsg(TtsMessageRequestDTO ttsMessageRequestDTO){
        return ResponseHelper.successful(textNotifyManager.getCurrentTtsMsg(ttsMessageRequestDTO));
    }

    @ApiOperation(value = "是否要继续播报当前的tts语音")
    @GetMapping(value = "/ttscontinuebroadcast",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getUserTtsCurrentMsg(TtsContinueBroadcastRequestDTO ttsContinueBroadcastRequestDTO){
        return ResponseHelper.successful(textNotifyManager.continueBroadcast(ttsContinueBroadcastRequestDTO));
    }

    @ApiOperation(value = "获取用户队列中的消息根据类型")
    @GetMapping(value = "/ttsmsgbytype")
    public ResponseEntity<ResponseResult> getUserTtsMsgByType(Integer notifyType, Integer userId, String sessionId) {
        return ResponseHelper.successful(textNotifyManager.getUserTtsMsgByType(notifyType, userId, sessionId));
    }

    @ApiOperation(value = "清除对应类型的TTS消息队列")
    @DeleteMapping(value = "/clearmsgbytype")
    public ResponseEntity<ResponseResult> clearUserTtsMsgByType(Integer notifyType, Integer userId, String sessionId) {
        return ResponseHelper.successful(textNotifyManager.clearUserTtsMsgByType(notifyType, userId, sessionId));
    }
}
