package com.siteweb.eventnotification.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.entity.TtsConfig;
import com.siteweb.eventnotification.service.TtsConfigService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "tts语音配置控制器", tags = {"tts语音配置控制器"})
public class TtsConfigController {
    @Autowired
    private TtsConfigService ttsConfigService;

    @GetMapping("/ttsconfig")
    public ResponseEntity<ResponseResult> getAll(){
        return ResponseHelper.successful(ttsConfigService.findAll());
    }

    @GetMapping(value = "/ttsconfig",params = {"keys"})
    public ResponseEntity<ResponseResult> getByKeys(String keys){
        if (CharSequenceUtil.isBlank(keys)) {
            return ResponseHelper.successful();
        }
        List<String> keyList = CharSequenceUtil.split(keys, ',');
        return ResponseHelper.successful(ttsConfigService.findByKeys(keyList));
    }

    @PutMapping("/ttsconfig/batch")
    public ResponseEntity<ResponseResult> batchUpdate(@RequestBody List<TtsConfig> ttsConfigList){
        return ResponseHelper.successful(ttsConfigService.batchUpdate(ttsConfigList));
    }
}
