package com.siteweb.eventnotification.controller;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.entity.AlarmVideoLink;
import com.siteweb.eventnotification.service.AlarmVideoLinkService;
import com.siteweb.eventnotification.vo.AlarmVideoLinkVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "告警视频联动接口", tags = {"告警视频联动接口"})
public class AlarmVideoLinkController {
    @Autowired
    AlarmVideoLinkService alarmVideoLinkService;
    @Autowired
    EmployeeService employeeService;


    @ApiOperation("根据权限获取告警视频联动实列配置")
    @GetMapping("/alarmvideolink")
    public ResponseEntity<ResponseResult> getAll() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(alarmVideoLinkService.findByDepartmentPermission(loginUserId));
    }

    @ApiOperation("获取所有告警视频联动实列配置")
    @GetMapping("/alarmvideolink/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable Integer id) {
        AlarmVideoLink alarmVideoLink = alarmVideoLinkService.findById(id);
        AlarmVideoLinkVO alarmVideoLinkVO = BeanUtil.copyProperties(alarmVideoLink, AlarmVideoLinkVO.class);
        return ResponseHelper.successful(alarmVideoLinkVO);
    }

    @ApiOperation("添加告警视频联动实列配置")
    @PostMapping("/alarmvideolink")
    public ResponseEntity<ResponseResult> createAlarmVideoLink(@RequestBody AlarmVideoLink alarmVideoLink) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Integer departmentId = employeeService.findDepartmentIdByEmployeeId(loginUserId);
        alarmVideoLink.setDepartmentId(departmentId);
        AlarmVideoLink alarmVideoLinkCreate = alarmVideoLinkService.createAlarmVideoLink(alarmVideoLink);
        AlarmVideoLinkVO alarmVideoLinkVO = BeanUtil.copyProperties(alarmVideoLinkCreate, AlarmVideoLinkVO.class);
        return ResponseHelper.successful(alarmVideoLinkVO);
    }

    @ApiOperation("修改告警视频联动实列配置")
    @PutMapping("/alarmvideolink")
    public ResponseEntity<ResponseResult> updateAlarmVideoLink(@RequestBody AlarmVideoLink alarmVideoLink) {
        AlarmVideoLink alarmVideoLinkUpdate = alarmVideoLinkService.updateAlarmVideoLink(alarmVideoLink);
        AlarmVideoLinkVO alarmVideoLinkVO = BeanUtil.copyProperties(alarmVideoLinkUpdate, AlarmVideoLinkVO.class);
        return ResponseHelper.successful(alarmVideoLinkVO);
    }

    @ApiOperation("删除告警视频联动信实列配置根据id")
    @DeleteMapping("/alarmvideolink/{id}")
    public ResponseEntity<ResponseResult> deleteAlarmVideoLink(@PathVariable Integer id) {
        return ResponseHelper.successful(alarmVideoLinkService.deleteById(id));
    }

    /**
     * 同步配置
     * @return {@link ResponseEntity}<{@link ResponseResult}>
     */
    @ApiOperation("告警视频联动同步配置")
    @GetMapping("/alarmvideolink/syncsetting")
    public ResponseEntity<ResponseResult> syncSetting(){
        return ResponseHelper.successful(alarmVideoLinkService.syncSetting());
    }
}
