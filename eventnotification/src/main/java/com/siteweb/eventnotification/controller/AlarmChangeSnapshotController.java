package com.siteweb.eventnotification.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.dto.AlarmChangeSnapshotFilterDTO;
import com.siteweb.eventnotification.dto.AllEventDTO;
import com.siteweb.eventnotification.service.AlarmChangeSnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;

@RestController
@RequestMapping("/api")
@Api(value = "告警视频联动抓图映射关系接口", tags = {"告警视频联抓图映射关系接口"})
public class AlarmChangeSnapshotController {
    @Autowired
    AlarmChangeSnapshotService alarmChangeSnapshotService;
    private static final String USER_ID_IS_NULL = "userid is null";

    @ApiOperation("获取告警视频联动抓图地址")
    @GetMapping("/alarmchangesnapshot/{sequenceId}")
    public ResponseEntity<ResponseResult> getAlarmChangeSnapshot(@PathVariable String sequenceId) {
        return ResponseHelper.successful(alarmChangeSnapshotService.findAlarmChangeSnapshotBySequenceId(sequenceId));
    }

    @ApiOperation("获取告警视频联动抓图的活动和历史告警")
    @GetMapping("/alleventbysnapshot")
    public ResponseEntity<ResponseResult> getAllEventBySnapshot(Pageable pageable, AlarmChangeSnapshotFilterDTO alarmChangeSnapshotFilterDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        Page<AllEventDTO> allEventDTOs = alarmChangeSnapshotService.findAllEventDTOBySnapshot(userId, pageable, alarmChangeSnapshotFilterDTO);
        return ResponseHelper.successful(allEventDTOs, HttpStatus.OK);
    }
}
