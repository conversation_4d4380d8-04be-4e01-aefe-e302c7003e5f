package com.siteweb.eventnotification.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.entity.AlarmNotifyConfig;
import com.siteweb.eventnotification.service.AlarmNotifyConfigService;
import com.siteweb.eventnotification.vo.AlarmNotifyConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyConfigController
 * @createTime 2022-04-25 15:33:32
 */
@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyConfigController", tags = {"AlarmNotifyConfig操作接口"})
public class AlarmNotifyConfigController {
    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    AlarmNotifyConfigService alarmNotifyConfigService;

    /**
     * GET  /alarmnotifyconfigs  query alarmNotifyConfigs
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the alarmNotifyConfigs
     */
    @ApiOperation(value = "查询AlarmNotifyConfig实体")
    @GetMapping("/alarmnotifyconfigs")
    public ResponseEntity<ResponseResult> getAllAlarmNotifyConfigs() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmNotifyConfigService.findByUserId(loginUserId), HttpStatus.OK);
    }

    /**
     * GET  /alarmnotifyconfigs/:alarmNotifyConfigId :  query alarmNotifyConfig by id
     *
     * @return the ResponseEntity with status 200 (OK) or status 404 when not found
     */
    @ApiOperation(value = "根据AlarmNotifyConfigId查询AlarmNotifyConfig实体")
    @GetMapping("/alarmnotifyconfigs/{alarmNotifyConfigId}")
    public ResponseEntity<ResponseResult> getAlarmNotifyConfigById(@PathVariable Integer alarmNotifyConfigId) {
        AlarmNotifyConfig alarmNotifyConfig = alarmNotifyConfigService.findByAlarmNotifyConfigId(alarmNotifyConfigId);
        return Optional.ofNullable(alarmNotifyConfig)
                .map(result -> ResponseHelper.successful(alarmNotifyConfig, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * POST  /alarmnotifyconfigs  post alarmNotifyConfigs
     *
     * @param alarmNotifyConfigVO the alarmNotifyConfigVO to create
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "新增AlarmNotifyConfig实体")
    @PostMapping(value = "/alarmnotifyconfigs")
    public ResponseEntity<ResponseResult> createAlarmNotifyConfig(@Valid @RequestBody AlarmNotifyConfigVO alarmNotifyConfigVO) {
        if (alarmNotifyConfigVO.getAlarmNotifyConfigId() != null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "alarmNotifyConfigId should be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = alarmNotifyConfigService.createAlarmNotifyConfig(alarmNotifyConfigVO.build());
        if (result > 0) {
            return ResponseHelper.successful(alarmNotifyConfigVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()),
                    "Create alarmNotifyConfig error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /alarmnotifyconfigs : Update alarmNotifyConfigVO.
     *
     * @param alarmNotifyConfigVO the alarmNotifyConfigVO to update
     * @return the ResponseEntity with status 200 (OK) and with body of the alarmNotifyConfigVO, or with
     * status 400 (Bad Request) if update error
     */
    @ApiOperation(value = "修改AlarmNotifyConfig实体")
    @PutMapping(value = "/alarmnotifyconfigs")
    public ResponseEntity<ResponseResult> updateAlarmNotifyConfig(@Valid @RequestBody AlarmNotifyConfigVO alarmNotifyConfigVO) {
        if (alarmNotifyConfigVO.getAlarmNotifyConfigId() == null) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "alarmNotifyConfigId can not be null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = alarmNotifyConfigService.updateAlarmNotifyConfig(alarmNotifyConfigVO.build());
        if (result > 0) {
            return ResponseHelper.successful(alarmNotifyConfigVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "Update alarmNotifyConfig error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * DELETE /alarmnotifyconfigs/:alarmnotifyconfigs : Delete alarmNotifyConfig by alarmNotifyConfigId.
     *
     * @param alarmNotifyConfigId the alarmNotifyConfigId of the alarmNotifyConfig
     * @return the ResponseEntity with status 200 (OK) or status 404 (when alarmNotifyConfigId not found)
     */
    @ApiOperation(value = "按AlarmNotifyConfigId删除AlarmNotifyConfig实体")
    @DeleteMapping(value = "/alarmnotifyconfigs/{alarmNotifyConfigId}")
    public ResponseEntity<ResponseResult> deleteAlarmNotifyConfig(@PathVariable Integer alarmNotifyConfigId) {
        AlarmNotifyConfig alarmNotifyConfig = alarmNotifyConfigService.findByAlarmNotifyConfigId(alarmNotifyConfigId);
        if (alarmNotifyConfig == null || Boolean.TRUE.equals(alarmNotifyConfig.getUsedStatus())) {
            //已启用的不允许删除
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()),
                    "alarmNotifyConfig can not be delete",
                    HttpStatus.BAD_REQUEST);
        }
        int result = alarmNotifyConfigService.deleteAlarmNotifyConfigById(alarmNotifyConfigId);
        if (result < 0) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
