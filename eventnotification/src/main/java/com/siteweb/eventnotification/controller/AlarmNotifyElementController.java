package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.entity.AlarmNotifyElement;
import com.siteweb.eventnotification.service.AlarmNotifyElementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyElementController
 * @createTime 2022-04-26 09:07:39
 */
@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyElementController", tags = {"AlarmNotifyElement操作接口"})
public class AlarmNotifyElementController {

    @Autowired
    AlarmNotifyElementService alarmNotifyElementService;

    /**
     * GET  /alarmnotifyelements/:id : query alarmNotifyElement by id
     *
     * @return the ResponseEntity with status 200 (OK) or status 404 when not found
     */
    @ApiOperation(value = "根据Id查询AlarmNotifyElement实体")
    @GetMapping("/alarmnotifyelements/{id}")
    public ResponseEntity<ResponseResult> getAlarmNotifyElementById(@PathVariable Integer id) {
        AlarmNotifyElement alarmNotifyElement = alarmNotifyElementService.findById(id);
        return Optional.ofNullable(alarmNotifyElement)
                .map(result -> ResponseHelper.successful(alarmNotifyElement, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * GET  /alarmnotifyelements  query alarmNotifyElements
     *
     * @return the ResponseEntity with status 200 (OK) and with the body of the alarmNotifyElements
     */
    @ApiOperation(value = "查询所有AlarmNotifyElement实体")
    @GetMapping("/alarmnotifyelements")
    public ResponseEntity<ResponseResult> getAllAlarmNotifyElements() {
        return ResponseHelper.successful(alarmNotifyElementService.findVisibleElements(), HttpStatus.OK);
    }
}
