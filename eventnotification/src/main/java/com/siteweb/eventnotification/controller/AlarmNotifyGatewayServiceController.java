package com.siteweb.eventnotification.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayService;
import com.siteweb.eventnotification.service.AlarmNotifyGatewayServiceConfigService;
import com.siteweb.eventnotification.service.AlarmNotifyGatewayServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api")
@Api(value = "AlarmNotifyGatewayServiceController", tags = {"AlarmNotifyGatewayServiceController操作接口"})
public class AlarmNotifyGatewayServiceController {
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    AlarmNotifyGatewayServiceService alarmNotifyGatewayServiceService;

    @Autowired
    AlarmNotifyGatewayServiceConfigService alarmNotifyGatewayServiceConfigService;

    @ApiOperation(value = "获取所有alarmnotifygatewayservice实体")
    @GetMapping("/alarmnotifygatewayservice")
    public ResponseEntity<ResponseResult> getAllAlarmNotifyGatewayService() {
        return ResponseHelper.successful(alarmNotifyGatewayServiceService.findAllAlarmNotifyGatewayService(), HttpStatus.OK);
    }

    @ApiOperation(value = "根据elementId查询alarmnotifygatewayservice实体")
    @GetMapping("/alarmnotifygatewayservice/{elementId}")
    public ResponseEntity<ResponseResult> getAlarmNotifyGatewayServiceByElementId(@PathVariable Integer elementId) {
        return ResponseHelper.successful(alarmNotifyGatewayServiceService.findAlarmNotifyGatewayServicesByElementId(elementId), HttpStatus.OK);
    }

    @ApiOperation(value = "新增alarmnotifygatewayservice")
    @PostMapping(value = "/alarmnotifygatewayservice")
    public ResponseEntity<ResponseResult> createAlarmNotifyGatewayService(@Valid @RequestBody AlarmNotifyGatewayService alarmNotifyGatewayService) {
        if (alarmNotifyGatewayServiceService.exists(alarmNotifyGatewayService)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID), localeMessageSourceUtil.getMessage("common.field.alarmNotifyGatewayServiceUnique"), HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmNotifyGatewayServiceService.createAlarmNotifyGatewayService(alarmNotifyGatewayService), HttpStatus.OK);
    }

    @ApiOperation(value = "修改alarmnotifygatewayservice")
    @PutMapping(value = "/alarmnotifygatewayservice")
    public ResponseEntity<ResponseResult> updateAlarmNotifyGatewayService(@Valid @RequestBody AlarmNotifyGatewayService alarmNotifyGatewayService) {
        if (alarmNotifyGatewayServiceService.exists(alarmNotifyGatewayService)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID), localeMessageSourceUtil.getMessage("common.field.alarmNotifyGatewayServiceUnique"), HttpStatus.BAD_REQUEST);
        }
        if (alarmNotifyGatewayServiceService.getUsedStatus(String.valueOf(alarmNotifyGatewayService.getAlarmNotifyGatewayServiceId()))) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID), localeMessageSourceUtil.getMessage("common.field.alarmNotifyGatewayServiceInUse"), HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(alarmNotifyGatewayServiceService.updateAlarmNotifyGatewayServiceByAlarmNotifyGatewayServiceId(alarmNotifyGatewayService), HttpStatus.OK);
    }

    @ApiOperation(value = "按AlarmNotifyGatewayServiceIds删除alarmnotifygatewayservice实体")
    @DeleteMapping(value = "/alarmnotifygatewayservice/{alarmNotifyGatewayServiceIds}")
    public ResponseEntity<ResponseResult> deleteAlarmNotifyGatewayService(@PathVariable String alarmNotifyGatewayServiceIds) {
        if (alarmNotifyGatewayServiceService.getUsedStatus(alarmNotifyGatewayServiceIds)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID), localeMessageSourceUtil.getMessage("common.field.alarmNotifyGatewayServiceInUse"), HttpStatus.BAD_REQUEST);
        }
        alarmNotifyGatewayServiceService.deleteAlarmNotifyGatewayServiceByAlarmNotifyGatewayServiceIds(alarmNotifyGatewayServiceIds);
        alarmNotifyGatewayServiceConfigService.deleteAlarmNotifyGatewayServiceConfigByAlarmNotifyGatewayServiceIds(alarmNotifyGatewayServiceIds);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}