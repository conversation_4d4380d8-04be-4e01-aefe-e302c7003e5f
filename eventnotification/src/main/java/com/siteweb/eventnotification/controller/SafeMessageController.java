package com.siteweb.eventnotification.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.eventnotification.entity.SafeMessage;
import com.siteweb.eventnotification.service.SafeMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "平安短信配置控制器", tags = {"平安短信配置控制器"})
public class SafeMessageController {
    @Autowired
    private SafeMessageService safeMessageService;

    @ApiOperation("获取所有平安短信配置")
    @GetMapping("/safemessage")
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(safeMessageService.findAll());
    }

    @ApiOperation("通过id获取平安短信配置")
    @GetMapping("/safemessage/{id}")
    public ResponseEntity<ResponseResult> getSafeMessageById(@PathVariable("id") Integer id) {
        SafeMessage safeMessage = safeMessageService.findById(id);
        if (ObjectUtil.isNotNull(safeMessage)) {
            return ResponseHelper.successful(safeMessage);
        }
        return ResponseHelper.successful(HttpStatus.NOT_FOUND);
    }

    @ApiOperation("添加平安短信配置")
    @PostMapping("/safemessage")
    public ResponseEntity<ResponseResult> createSafeMessage(@RequestBody SafeMessage safeMessage) {
        Integer result = safeMessageService.createSafeMessage(safeMessage);
        if (result > 0) {
            return ResponseHelper.successful(safeMessage);
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.CREATE_OBJECT_ERROR.value()), "Create safeMessage error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ApiOperation("编辑平安短信配置")
    @PutMapping("/safemessage")
    public ResponseEntity<ResponseResult> updateSafeMessage(@RequestBody SafeMessage safeMessage) {
        if (ObjectUtil.isNull(safeMessageService.findById(safeMessage.getSafeMessageId()))) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        Integer result = safeMessageService.updateSafeMessage(safeMessage);
        if (result > 0) {
            return ResponseHelper.successful(safeMessage);
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.UPDATE_OBJECT_ERROR.value()), "Update safeMessage error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ApiOperation("通过id删除平安短信配置")
    @DeleteMapping("/safemessage/{id}")
    public ResponseEntity<ResponseResult> deleteSafeMessageById(@PathVariable("id") Integer id) {
        SafeMessage safeMessage = safeMessageService.findById(id);
        if (ObjectUtil.isNull(safeMessage)) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(safeMessageService.deleteById(id));
    }
}
