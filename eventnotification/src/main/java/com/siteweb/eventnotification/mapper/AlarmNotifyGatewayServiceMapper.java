package com.siteweb.eventnotification.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayService;

import java.util.List;

public interface AlarmNotifyGatewayServiceMapper extends BaseMapper<AlarmNotifyGatewayService> {
    List<AlarmNotifyGatewayService> findAlarmNotifyGatewayServicesUsedStatusByElementId(Integer elementId);

    List<AlarmNotifyGatewayService> findAllAlarmNotifyGatewayService();
    List<AlarmNotifyGatewayServiceConfigDetailDTO> findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyGatewayServiceIds(List<Integer> alarmNotifyGatewayServiceIdList);
    List<AlarmNotifyGatewayServiceConfigDetailDTO> findAllAlarmNotifyGatewayServiceConfigDetailDTO();
}