package com.siteweb.eventnotification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventnotification.dto.AlarmChangeSnapshotFilterDTO;
import com.siteweb.eventnotification.entity.AlarmChangeSnapshot;
import com.siteweb.eventnotification.dto.AllEventDTO;
import java.util.List;

public interface AlarmChangeSnapshotMapper extends BaseMapper<AlarmChangeSnapshot> {
    int batchInsertAlarmChangeSnapshot(List<AlarmChangeSnapshot> alarmChangeSnapshotList);
    List<AllEventDTO> findAllEventDTOBySnapshot(List<Integer> resourceStructureIds, List<AlarmChangeSnapshot> alarmChangeSnapshotList, AlarmChangeSnapshotFilterDTO alarmChangeSnapshotFilterDTO);
}