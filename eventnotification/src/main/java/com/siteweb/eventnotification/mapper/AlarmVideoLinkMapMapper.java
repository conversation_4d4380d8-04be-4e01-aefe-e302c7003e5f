package com.siteweb.eventnotification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventnotification.dto.AlarmVideoLinkSnapshotMap;
import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;

import java.util.List;

public interface AlarmVideoLinkMapMapper extends BaseMapper<AlarmVideoLinkMap> {
    List<AlarmVideoLinkSnapshotMap> findByCondition(Integer equipmentId, Integer eventId, Integer eventConditionId);
}
