package com.siteweb.eventnotification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventnotification.entity.SafeMessageRecord;

import java.util.List;

public interface SafeMessageRecordMapper extends BaseMapper<SafeMessageRecord> {
    /**
     * 查找今日已存在的安全短信配置
     * @param safeMessageIds
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findTodaySendRecord(List<Integer> safeMessageIds);
}
