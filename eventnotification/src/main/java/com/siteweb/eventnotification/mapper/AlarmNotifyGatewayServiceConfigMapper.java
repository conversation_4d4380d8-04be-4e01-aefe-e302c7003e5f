package com.siteweb.eventnotification.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventnotification.dto.AlarmNotifyGatewayServiceConfigDetailDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayServiceConfig;

import java.util.List;

public interface AlarmNotifyGatewayServiceConfigMapper extends BaseMapper<AlarmNotifyGatewayServiceConfig> {
    int batchInsert(List<AlarmNotifyGatewayServiceConfig> alarmNotifyGatewayServiceConfigList);
    List<AlarmNotifyGatewayServiceConfigDetailDTO> findAlarmNotifyGatewayServicesByAlarmNotifyConfigId(Integer alarmNotifyConfigId);

    AlarmNotifyGatewayServiceConfigDetailDTO findAlarmNotifyGatewayServiceConfigDetailDTOByAlarmNotifyConfigIdAndElementId(Integer alarmNotifyConfigId, Integer elementId);

}