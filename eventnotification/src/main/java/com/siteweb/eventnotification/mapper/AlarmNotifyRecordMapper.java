package com.siteweb.eventnotification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.eventnotification.dto.AlarmNotifyRecordDto;
import com.siteweb.eventnotification.entity.AlarmNotifyRecord;
import com.siteweb.eventnotification.vo.AlarmNotifyRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyRecordMapper
 * @createTime 2022-04-22 16:24:55
 */
public interface AlarmNotifyRecordMapper extends BaseMapper<AlarmNotifyRecord> {

    /**
     * 查询告警通知记录
     *
     * @param alarmNotifyRecordVO 告警通知条件
     * @param equipmentIds 设备id
     * @return {@link List}<{@link AlarmNotifyRecordDto}>
     */
    List<AlarmNotifyRecordDto> findByReportParam(@Param("alarmNotifyRecordVO") AlarmNotifyRecordVO alarmNotifyRecordVO, @Param("equipmentIds") Collection<Integer> equipmentIds);

    IPage<AlarmNotifyRecordDto> findByReportParamPage(@Param("page") IPage<AlarmNotifyRecordDto> page, @Param("alarmNotifyRecordVO") AlarmNotifyRecordVO alarmNotifyRecordVO, @Param("equipmentIds") Collection<Integer> equipmentIds);

    long findByReportParamCount(@Param("alarmNotifyRecordVO") AlarmNotifyRecordVO alarmNotifyRecordVO, @Param("equipmentIds") Collection<Integer> equipmentIds);

    Page<AlarmNotifyRecord> findAlarmNotifyRecordsByKeywords(@Param("page") Page<AlarmNotifyRecord> page, @Param("keywords") String keywords);
}
