package com.siteweb.eventnotification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.eventnotification.entity.Notification;
import com.siteweb.eventnotification.vo.NotificationEquipmentCategoryVO;
import com.siteweb.eventnotification.vo.NotificationEventCategoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: lzy
 * @Date: 2023/3/16 13:43
 */
public interface NotificationMapper extends BaseMapper<Notification> {
    List<NotificationEquipmentCategoryVO> findEquipmentCategoryIds(@Param("equipmentCategoryIds") List<Integer> equipmentCategoryIds);

    List<NotificationEventCategoryVO> findEventCategoryIds(@Param("eventCategoryIds") List<Integer> eventCategoryIds);
}
