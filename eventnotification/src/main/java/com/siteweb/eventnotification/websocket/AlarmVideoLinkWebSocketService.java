package com.siteweb.eventnotification.websocket;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.util.StringUtils;
import com.siteweb.common.websocket.dto.WebSocketCommonMessageBody;
import com.siteweb.common.websocket.enums.WebSocketBusinessTypeEnum;
import com.siteweb.common.websocket.manager.impl.CommonMsgWebSocketManager;
import com.siteweb.eventnotification.textnotify.alarmtextnotify.AlarmTextNotifyService;
import com.siteweb.eventnotification.config.AlarmVideoLinkSnapshotConfig;
import com.siteweb.eventnotification.dto.AlarmVideoLinkNoticeDTO;
import com.siteweb.eventnotification.dto.AlarmVideoLinkSnapshotMap;
import com.siteweb.eventnotification.entity.AlarmChangeSnapshot;
import com.siteweb.eventnotification.service.AlarmChangeSnapshotService;
import com.siteweb.eventnotification.service.AlarmVideoLinkMapService;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.socket.WebSocketSession;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmVideoLinkWebSocketService implements ApplicationListener<BaseSpringEvent<AlarmChange>> {
    @Autowired
    RegionService regionService;
    @Autowired
    AlarmVideoLinkMapService alarmVideoLinkMapService;
    @Autowired
    CommonMsgWebSocketManager commonMsgWebSocketManager;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    HAStatusService haStatusService;
    @Autowired
    AlarmChangeSnapshotService alarmChangeSnapshotService;
    @Autowired
    AlarmVideoLinkSnapshotConfig alarmVideoLinkSnapshotConfig;
    @Autowired
    AccountService accountService;
    @Autowired
    TokenUtil tokenUtil;
    @Autowired
    RedisTemplate<String, String> redisTemplate;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    ThreadPoolExecutor threadPoolExecutor;
    private static final String LOGIN_USER_TOKEN = "LoginUserToken";

    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP:告警视频联动监听告警事件退出");
            return;
        }
        SystemConfig systemConfigKey = systemConfigService.findBySystemConfigKey(SystemConfigEnum.ALARM_VIDEO_WINDOW_SHOW.getSystemConfigKey());
        //没有开启告警视频联动
        if (ObjectUtil.isNull(systemConfigKey) || !Boolean.parseBoolean(systemConfigKey.getSystemConfigValue())) {
            return;
        }
        this.getMapList(event.getData());
    }

    private void getMapList(AlarmChange alarmChange) {
        if (ObjectUtil.equal(AlarmOperationTypeEnum.CONFIRM.getValue(), alarmChange.getOperationType())) {
            return;
        }
        List<AlarmVideoLinkSnapshotMap> mapList = alarmVideoLinkMapService.findMapByCondition(alarmChange.getEquipmentId(), null, null);
        mapList.addAll(alarmVideoLinkMapService.findMapByCondition(alarmChange.getEquipmentId(), alarmChange.getEventId(), null));
        mapList.addAll(alarmVideoLinkMapService.findMapByCondition(alarmChange.getEquipmentId(), alarmChange.getEventId(), alarmChange.getEventConditionId()));
        if (CollUtil.isEmpty(mapList)) {
            return;
        }
        List<AlarmVideoLinkSnapshotMap> websocketMapList = mapList.stream().filter(map -> map.getLinkType() == null || map.getLinkType().contains("1")).toList();
        List<AlarmVideoLinkSnapshotMap> snapshotMapList = mapList.stream().filter(map -> map.getLinkType() != null && map.getLinkType().contains("2")
                && map.getOperationType() != null && map.getOperationType().contains(String.valueOf(alarmChange.getOperationType()))
                && map.getCameraId() != null && map.getSnapshotCount() != null && map.getSnapshotInterval() != null
        ).toList();

        if (CollUtil.isNotEmpty(snapshotMapList)) {
            this.saveSnapShots(alarmChange, snapshotMapList);
        }
        threadPoolExecutor.execute(() -> {
            if (CollUtil.isNotEmpty(websocketMapList)) {
                this.sendVideoInfo(alarmChange, websocketMapList);
            }
        });
    }


    /**
     * 推送告警相关的视频信息
     *
     * @param alarmChange           告警改变信息
     * @param alarmVideoLinkMapList
     */
    private void sendVideoInfo(AlarmChange alarmChange, List<AlarmVideoLinkSnapshotMap> alarmVideoLinkMapList) {
        //不是开始状态
        if (ObjectUtil.notEqual(AlarmOperationTypeEnum.START.getValue(), alarmChange.getOperationType())) {
            return;
        }
        List<AlarmVideoLinkNoticeDTO> alarmVideoLinkNoticeDTOS = this.addExtendField(alarmChange, alarmVideoLinkMapList);
        Map<String, WebSocketSession> webSocketSessionHashMap = commonMsgWebSocketManager.getSessionStatePool();
        for (Map.Entry<String, WebSocketSession> stringWebSocketSessionEntry : webSocketSessionHashMap.entrySet()) {
            String[] uniqueIdArray = stringWebSocketSessionEntry.getKey().split(":");
            int userId = Integer.parseInt(uniqueIdArray[0]);
            //判断当前登录用户ID及设备权限
            if (!regionService.hasRegionMapPermissions(userId,alarmChange.getResourceStructureId(),alarmChange.getEquipmentId())) {
                continue;
            }
            //WS推送信息
            WebSocketCommonMessageBody<List<AlarmVideoLinkNoticeDTO>> webSocketCommonMessageBody = WebSocketCommonMessageBody.of(WebSocketBusinessTypeEnum.ALARM_VIDEO, alarmVideoLinkNoticeDTOS);
            commonMsgWebSocketManager.sendMessage(stringWebSocketSessionEntry.getKey(), webSocketCommonMessageBody);
        }
    }

    /**
     * 添加扩展字段
     *
     * @param alarmChange
     * @param alarmVideoLinkMapList
     * @return {@link List}<{@link AlarmVideoLinkNoticeDTO}>
     */
    private List<AlarmVideoLinkNoticeDTO> addExtendField(AlarmChange alarmChange, List<AlarmVideoLinkSnapshotMap> alarmVideoLinkMapList) {
        List<AlarmVideoLinkNoticeDTO> alarmVideoLinkNoticeDTOS = BeanUtil.copyToList(alarmVideoLinkMapList, AlarmVideoLinkNoticeDTO.class);
        for (AlarmVideoLinkNoticeDTO alarmVideoLinkNoticeDTO : alarmVideoLinkNoticeDTOS) {
            alarmVideoLinkNoticeDTO.setEventName(alarmChange.getEventName());
            alarmVideoLinkNoticeDTO.setEventSeverity(alarmChange.getEventSeverity());
            alarmVideoLinkNoticeDTO.setEquipmentName(alarmChange.getEquipmentName());
            alarmVideoLinkNoticeDTO.setMeanings(alarmChange.getMeanings());
            alarmVideoLinkNoticeDTO.setBaseTypeName(alarmChange.getBaseTypeName());
            alarmVideoLinkNoticeDTO.setEquipmentPosition(resourceStructureManager.getFullPath(alarmChange.getResourceStructureId()));
        }
        return alarmVideoLinkNoticeDTOS;
    }

    private String getTokenByUserName(String userName) {
        List<AccountDTO> accounts = accountService.findByLogonId(userName);
        AccountDTO accountDTO = accounts.get(0);
        String loginUserKey = accountDTO.getLogonId() + ":" + "externalApi";
        Object objOldToken = redisTemplate.opsForHash().get(LOGIN_USER_TOKEN, loginUserKey);
        String newToken = String.valueOf(objOldToken);
        if (ObjectUtil.isNull(objOldToken)) {
            TokenUser tokenUser = new TokenUser(accountDTO);
            newToken = tokenUtil.createTokenForUser(tokenUser, "externalApi");
        }
        return newToken;
    }

    /**
     * 告警视频抓图
     *
     * @param alarmChange 告警改变信息
     * @param resultList
     */
    private List<AlarmChangeSnapshot> takeSnapShots(AlarmChange alarmChange, List<AlarmVideoLinkSnapshotMap> resultList) {
        List<AlarmChangeSnapshot> alarmChangeSnapshotList = new ArrayList<>();
        String url = alarmVideoLinkSnapshotConfig.getBaseUrl() + "/v1/vpform/EqumentManage/takePic";
        String tokenHeader = "Authorization";
        SystemConfig systemConfigKey = systemConfigService.findBySystemConfigKey(SystemConfigEnum.SYSTEM_TOKEN_HEADER.getSystemConfigKey());
        if (ObjectUtil.isNotNull(systemConfigKey) && StringUtils.isNotBlank(systemConfigKey.getSystemConfigValue())) {
            tokenHeader = systemConfigKey.getSystemConfigValue();
        }
        String newToken = this.getTokenByUserName(alarmVideoLinkSnapshotConfig.getUser());
        for (AlarmVideoLinkSnapshotMap alarmVideoLinkSnapshotMap : resultList) {
            JSONObject params = new JSONObject();
            params.set("cameraId", alarmVideoLinkSnapshotMap.getCameraId());
            params.set("number", alarmVideoLinkSnapshotMap.getSnapshotCount());
            params.set("intervalTime", alarmVideoLinkSnapshotMap.getSnapshotInterval());
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set(tokenHeader, newToken);
            HttpEntity<JSONObject> httpEntity = new HttpEntity<>(params, httpHeaders);
            // 2. 执行
            log.info("request: {}, params: {}", url, JSONUtil.toJsonPrettyStr(params));
            JSONObject jsonBody = null;
            try {
                jsonBody = restTemplate.postForObject(url, httpEntity, JSONObject.class);
                log.info("远程调用:{}的响应为:{}", url, JSONUtil.toJsonPrettyStr(jsonBody));
                //3. 解析
                if (jsonBody != null) {
                    JSONObject data = jsonBody.getJSONObject("data");
                    if (data != null) {
                        String path = data.getStr("path");
                        String cameraName = data.getStr("cameraName");
                        AlarmChangeSnapshot alarmChangeSnapshot = new AlarmChangeSnapshot();
                        alarmChangeSnapshot.setSequenceId(alarmChange.getSequenceId());
                        alarmChangeSnapshot.setOperationType(alarmChange.getOperationType());
                        alarmChangeSnapshot.setSnapshotUrl(path);
                        alarmChangeSnapshot.setCameraId(alarmVideoLinkSnapshotMap.getCameraId());
                        alarmChangeSnapshot.setCameraName(cameraName);
                        alarmChangeSnapshot.setStartTime(alarmChange.getStartTime());
                        alarmChangeSnapshotList.add(alarmChangeSnapshot);
                    }
                }
            } catch (Exception e) {
                log.error("远程调用:{}的响应为:{},{}", url, e.getMessage(), ExceptionUtil.stacktraceToString(e));
            }
        }
        return alarmChangeSnapshotList;
    }

    /**
     * 告警视频抓图并存储地址
     *
     * @param alarmChange                   告警改变信息
     * @param alarmVideoLinkSnapshotMapList
     */
    private void saveSnapShots(AlarmChange alarmChange, List<AlarmVideoLinkSnapshotMap> alarmVideoLinkSnapshotMapList) {
        //不是开始状态、结束状态
        if (ObjectUtil.notEqual(1, alarmChange.getOperationType()) && ObjectUtil.notEqual(2, alarmChange.getOperationType())) {
            return;
        }
        Map<Long, List<AlarmVideoLinkSnapshotMap>> groupBy = alarmVideoLinkSnapshotMapList.stream().collect(Collectors.groupingBy(AlarmVideoLinkSnapshotMap::getCameraId));
        List<AlarmVideoLinkSnapshotMap> resultList = new ArrayList<>();
        for (List<AlarmVideoLinkSnapshotMap> list : groupBy.values()) {
            AlarmVideoLinkSnapshotMap alarmVideoLinkSnapshotMap = list.get(list.size() - 1);//相同的cameraId,策略不同会截图张数和间隔不一致，此时取截图张数和间隔取最后一个
            resultList.add(alarmVideoLinkSnapshotMap);
        }
        List<AlarmChangeSnapshot> alarmChangeSnapshotList = this.takeSnapShots(alarmChange, resultList);
        if (CollUtil.isNotEmpty(alarmChangeSnapshotList)) {
            alarmChangeSnapshotService.batchInsertAlarmChangeSnapshot(alarmChangeSnapshotList);
        }
    }
}
