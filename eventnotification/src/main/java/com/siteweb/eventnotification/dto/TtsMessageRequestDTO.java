package com.siteweb.eventnotification.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TtsMessageRequestDTO {
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 告警流水号
     */
    private String sequenceId;
    /**
     * 告警操作 1开始  2结束  3确认
     */
    private Integer operationType;
    /**
     * 预警id
     */
    private Integer preAlarmId;
    /**
     * 站内信id
     */
    private Integer internalMessageId;
}
