package com.siteweb.eventnotification.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmTextNotifyStatusRecordDTO {
    public AlarmTextNotifyStatusRecordDTO(Date sendTime, String sequenceId) {
        this.sendTime = sendTime;
        this.sequenceId = sequenceId;
    }

    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 发送告警标识
     */
    private String sequenceId;
    /**
     * 重试次数
     */
    private int retryCount;
}
