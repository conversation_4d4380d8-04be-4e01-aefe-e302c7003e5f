package com.siteweb.eventnotification.dto;

import lombok.Data;

import java.util.Date;

@Data
public class AlarmNotifyRecordDto {
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 事件含义
     */
    private String eventMeanings;
    /**
     * 告警等级
     */
    private String eventSeverity;
    /**
     * 内容
     */
    private String content;
    /**
     * 告警开始时间
     */
    private Date alarmStartTime;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 发送类型
     */
    private String sendType;
    /**
     * 发送结果
     */
    private String sendResult;
}
