package com.siteweb.eventnotification.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyExecutorDTO
 * @createTime 2022-04-24 16:15:14
 */
@Data
@NoArgsConstructor
public class AlarmNotifyExecutorDTO {

    private Long serialNo;
    private Integer stationId;
    private Integer equipmentId;
    private Integer eventId;
    private Integer eventConditionId;
    private String eventName;
    private int eventSeverityId;
    //1 告警开始；2 告警结束；3 告警确认
    private int operationType;
    private String equipmentName;
    private Integer resourceStructureId;
    private String sequenceId;
    private String alarmNotifyContent;
    private Date startTime;
    private Date endTime;
    private Date confirmTime;
    private Boolean reCheckActiveEvent;
    private String notifyDelay;
}
