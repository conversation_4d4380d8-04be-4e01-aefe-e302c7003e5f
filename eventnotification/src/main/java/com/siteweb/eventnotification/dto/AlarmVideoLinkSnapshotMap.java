package com.siteweb.eventnotification.dto;

import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlarmVideoLinkSnapshotMap extends AlarmVideoLinkMap {

    /**
     * 联动类型，1视频弹窗，2抓图，3未来可能是视频录制
     */
    private String linkType;
    /**
     * 事件状态，1告警开始，2告警结束
     */
    private String operationType;
    /**
     * 抓图张数
     */
    private Integer snapshotCount;
    /**
     * 抓图间隔
     */
    private Integer snapshotInterval;
}
