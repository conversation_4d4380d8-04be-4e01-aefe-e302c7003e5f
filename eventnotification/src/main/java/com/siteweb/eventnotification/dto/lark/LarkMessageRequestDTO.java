package com.siteweb.eventnotification.dto.lark;

import lombok.Data;

@Data
public class LarkMessageRequestDTO {
    public LarkMessageRequestDTO(String content,String to) {
        this.content = content;
        this.to = to;
    }

    /**
     * 告警内容
     */
    private String content;
    /**
     * 告警内容中的分隔符，由2个字符组成,固定为:,
     */
    private String delimiter = ":,";
    /**
     * 群成员接收人@信息
     *
     */
    private String to = "";
    /**
     * 发送内容字段标识。标记对外发送的内容是对content字段二次组装，还是直接发送content中的内容。
     * 1：需对content字段二次拼装发送（如告警内容）
     * 2：直接发送content字段内容，网关不做处理只转发
     */
    private String isassemble = "2";
}
