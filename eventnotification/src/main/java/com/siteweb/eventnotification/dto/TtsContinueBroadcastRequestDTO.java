package com.siteweb.eventnotification.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TtsContinueBroadcastRequestDTO {
    /**
     * 通知类型
     */
    private Integer notifyType;
    /**
     * 告警流水号
     */
    private String sequenceId;
    /**
     * 预警id
     */
    private Integer preAlarmId;
    /**
     * 站内信消息id
     */
    private String internalMessageId;
}
