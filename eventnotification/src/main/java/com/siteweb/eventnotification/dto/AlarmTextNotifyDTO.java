package com.siteweb.eventnotification.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description AlarmTextNotifyDTO
 * @createTime 2022-05-25 15:03:51
 */
@Data
@NoArgsConstructor
public class AlarmTextNotifyDTO {

    private String text;
    private int severityId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date confirmTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Date endTime;

    private String sequenceId;
    /**
     * 告警操作类型 1开始 2结束 3确认
     */
    private Integer operationType;
}
