package com.siteweb.eventnotification.dto;

import lombok.Data;
import java.util.Date;

@Data
public class AllEventDTO {
    private String sequenceId;

    private Integer resourceStructureId;
    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 设备名
     */
    private String equipmentName;
    /**
     * 设备位置
     */
    private String equipmentPosition;
    /**
     * 设备种类名
     */
    private String equipmentCategoryName;
    /**
     * 事件名
     */
    private String eventName;
    /**
     * 告警触发值
     */
    private Double eventValue;
    /**
     * 告警含义
     */
    private String meanings;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 确认人名
     */
    private String confirmerName;
    /**
     * 持续时间
     */
    private String duration;
}
