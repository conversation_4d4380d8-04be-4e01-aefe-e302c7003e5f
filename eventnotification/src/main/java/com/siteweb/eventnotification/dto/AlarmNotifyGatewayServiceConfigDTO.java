package com.siteweb.eventnotification.dto;

import com.siteweb.common.util.StringUtils;
import com.siteweb.eventnotification.entity.AlarmNotifyGatewayServiceConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
public class AlarmNotifyGatewayServiceConfigDTO {

    private Integer alarmNotifyConfigId;

    private String alarmNotifyGatewayServiceIds;

    public List<AlarmNotifyGatewayServiceConfig> buildAlarmNotifyGatewayServiceList() {
        List<AlarmNotifyGatewayServiceConfig> alarmNotifyGatewayServiceConfigList = new ArrayList<>();
        List<Integer> alarmNotifyGatewayServiceIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(this.getAlarmNotifyGatewayServiceIds())) {
            alarmNotifyGatewayServiceIdList = Arrays.stream(this.getAlarmNotifyGatewayServiceIds().split(",")).map(Integer::valueOf).toList();
        }
        alarmNotifyGatewayServiceIdList.forEach(id -> {
            AlarmNotifyGatewayServiceConfig alarmNotifyGatewayServiceConfig = new AlarmNotifyGatewayServiceConfig();
            alarmNotifyGatewayServiceConfig.setAlarmNotifyGatewayServiceId(id);
            alarmNotifyGatewayServiceConfig.setAlarmNotifyConfigId(this.getAlarmNotifyConfigId());
            alarmNotifyGatewayServiceConfigList.add(alarmNotifyGatewayServiceConfig);
        });
        return alarmNotifyGatewayServiceConfigList;
    }
}