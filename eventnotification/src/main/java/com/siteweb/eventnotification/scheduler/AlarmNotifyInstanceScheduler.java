package com.siteweb.eventnotification.scheduler;


import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyFilterRuleDTO;
import com.siteweb.eventnotification.entity.AlarmNotifyConfig;
import com.siteweb.eventnotification.executor.AlarmNotifyInstanceExecutor;
import com.siteweb.eventnotification.service.*;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.AlarmOperationTypeEnum;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.utility.service.HAStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyInstanceScheduler
 * @createTime 2022-04-24 17:06:48
 */
@Component
public class AlarmNotifyInstanceScheduler implements ApplicationListener<BaseSpringEvent<AlarmChange>> {

    private final Logger log = LoggerFactory.getLogger(AlarmNotifyInstanceScheduler.class);

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    AlarmNotifyConfigService alarmNotifyConfigService;

    @Autowired
    AlarmNotifyFilterRuleService alarmNotifyFilterRuleService;

    @Autowired
    AlarmNotifyElementConfigService alarmNotifyElementConfigService;

    @Autowired
    AlarmNotifyNodeService alarmNotifyNodeService;

    @Autowired
    AlarmNotifySegmentService alarmNotifySegmentService;

    @Autowired
    ActiveEventService liveEventService;

    @Autowired
    NotificationContentItemService notificationContentItemService;

    @Autowired
    AlarmNotifyExecutorService alarmNotifyExecutorService;

    @Autowired
    HAStatusService haStatusService;

    ScheduledThreadPoolExecutor threadPoolExecutor = new ScheduledThreadPoolExecutor(6, r -> {
        Thread thread = new Thread(r, "notify" + r.hashCode());
        thread.setUncaughtExceptionHandler((t, e) -> {
            log.error(e.getMessage());
        });
        return thread;
    });

    ConcurrentHashMap<Integer, String> alarmNotifyConfigIdContentTemplateHashMap = new ConcurrentHashMap<>();
    ConcurrentHashMap<Integer, String> alarmNotifyConfigIdNotifyDelayHashMap = new ConcurrentHashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdParkIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdBuildingIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdFloorIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdRoomIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdMdcIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdEquipmentIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdEventIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdBaseEquipmentIdHashMap = new HashMap<>();
    HashMap<Integer, List<Long>> alarmNotifyConfigIdBaseTypeIdHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdOperationTypeHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdEventSeverityHashMap = new HashMap<>();
    HashMap<Integer, List<Integer>> alarmNotifyConfigIdResourceStructureIdHashMap = new HashMap<>();
    /**
     * 工程态告警条件
     */
    HashMap<Integer, Boolean> alarmNotifyConfigIdProjectStatusHashMap = new HashMap<>();
    HashMap<Integer, String> alarmNotifyConfigIdKeywordHashMap = new HashMap<>();

    final byte[] lockObj = new byte[0];

    @Scheduled(fixedDelay = 10 * 1000)//every 10 seconds
    protected void schedule() {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP:定时刷新告警通知配置退出");
            return;
        }
        HashMap<Integer, String> tmpAlarmNotifyConfigIdContentTemplateHashMap = new HashMap<>();
        HashMap<Integer, String> tmpAlarmNotifyConfigIdNotifyDelayHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdParkIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdBuildingIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdFloorIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdRoomIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdMdcIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdEquipmentIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdEventIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdBaseEquipmentIdHashMap = new HashMap<>();
        HashMap<Integer, List<Long>> tmpAlarmNotifyConfigIdBaseTypeIdHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdOperationTypeHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdEventSeverityHashMap = new HashMap<>();
        HashMap<Integer, List<Integer>> tmpAlarmNotifyConfigIdResourceStructureIdHashMap = new HashMap<>();
        HashMap<Integer, Boolean> tmpAlarmNotifyConfigIdProjectStatusHashMap = new HashMap<>();
        HashMap<Integer, String> tmpAlarmNotifyConfigIdKeywordHashMap = new HashMap<>();

        List<AlarmNotifyConfig> alarmNotifyConfigList = alarmNotifyConfigService.findAll().stream().filter(o -> Boolean.TRUE.equals(o.getUsedStatus())).toList();
        for (AlarmNotifyConfig alarmNotifyConfig : alarmNotifyConfigList) {
            tmpAlarmNotifyConfigIdContentTemplateHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), alarmNotifyConfig.getContentTemplate());
            tmpAlarmNotifyConfigIdNotifyDelayHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), alarmNotifyConfig.getNotifyDelay() != null ? alarmNotifyConfig.getNotifyDelay().toString() : "");
            List<AlarmNotifyFilterRuleDTO> alarmNotifyFilterRuleDTOS = alarmNotifyFilterRuleService.findByAlarmNotifyConfigId(alarmNotifyConfig.getAlarmNotifyConfigId());
            for (AlarmNotifyFilterRuleDTO alarmNotifyFilterRuleDTO : alarmNotifyFilterRuleDTOS) {
                if (CharSequenceUtil.isBlank(alarmNotifyFilterRuleDTO.getFilterParameter()))
                    continue;
                switch (alarmNotifyFilterRuleDTO.getFilterConditionId()) {
                    case 1:
                        tmpAlarmNotifyConfigIdParkIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 2:
                        tmpAlarmNotifyConfigIdBuildingIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 3:
                        tmpAlarmNotifyConfigIdFloorIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 4:
                        tmpAlarmNotifyConfigIdRoomIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 5:
                        tmpAlarmNotifyConfigIdMdcIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 6:
                        tmpAlarmNotifyConfigIdEquipmentIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 7:
                        tmpAlarmNotifyConfigIdEventIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 8:
                        tmpAlarmNotifyConfigIdBaseEquipmentIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 9:
                        tmpAlarmNotifyConfigIdBaseTypeIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Long::valueOf).toList());
                        break;
                    case 10:
                        tmpAlarmNotifyConfigIdOperationTypeHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 11:
                        tmpAlarmNotifyConfigIdEventSeverityHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 12:
                        tmpAlarmNotifyConfigIdResourceStructureIdHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), Arrays.stream(alarmNotifyFilterRuleDTO.getFilterParameter().split(",")).map(Integer::valueOf).toList());
                        break;
                    case 13:
                        tmpAlarmNotifyConfigIdProjectStatusHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), BooleanUtil.toBoolean(alarmNotifyFilterRuleDTO.getFilterParameter()));
                        break;
                    case 14:
                        tmpAlarmNotifyConfigIdKeywordHashMap.put(alarmNotifyConfig.getAlarmNotifyConfigId(), alarmNotifyFilterRuleDTO.getFilterParameter());
                        break;
                    default:
                        break;
                }
            }
        }
        synchronized (lockObj) {
            alarmNotifyConfigIdContentTemplateHashMap.clear();
            for (Map.Entry<Integer, String> entry : tmpAlarmNotifyConfigIdContentTemplateHashMap.entrySet()) {
                alarmNotifyConfigIdContentTemplateHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdNotifyDelayHashMap.clear();
            for (Map.Entry<Integer, String> entry : tmpAlarmNotifyConfigIdNotifyDelayHashMap.entrySet()) {
                alarmNotifyConfigIdNotifyDelayHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdParkIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdParkIdHashMap.entrySet()) {
                alarmNotifyConfigIdParkIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdBuildingIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdBuildingIdHashMap.entrySet()) {
                alarmNotifyConfigIdBuildingIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdFloorIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdFloorIdHashMap.entrySet()) {
                alarmNotifyConfigIdFloorIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdRoomIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdRoomIdHashMap.entrySet()) {
                alarmNotifyConfigIdRoomIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdMdcIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdMdcIdHashMap.entrySet()) {
                alarmNotifyConfigIdMdcIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdEquipmentIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdEquipmentIdHashMap.entrySet()) {
                alarmNotifyConfigIdEquipmentIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdEventIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdEventIdHashMap.entrySet()) {
                alarmNotifyConfigIdEventIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdBaseEquipmentIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdBaseEquipmentIdHashMap.entrySet()) {
                alarmNotifyConfigIdBaseEquipmentIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdBaseTypeIdHashMap.clear();
            for (Map.Entry<Integer, List<Long>> entry : tmpAlarmNotifyConfigIdBaseTypeIdHashMap.entrySet()) {
                alarmNotifyConfigIdBaseTypeIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdOperationTypeHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdOperationTypeHashMap.entrySet()) {
                alarmNotifyConfigIdOperationTypeHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdEventSeverityHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdEventSeverityHashMap.entrySet()) {
                alarmNotifyConfigIdEventSeverityHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdResourceStructureIdHashMap.clear();
            for (Map.Entry<Integer, List<Integer>> entry : tmpAlarmNotifyConfigIdResourceStructureIdHashMap.entrySet()) {
                alarmNotifyConfigIdResourceStructureIdHashMap.put(entry.getKey(), entry.getValue());
            }
            alarmNotifyConfigIdProjectStatusHashMap.clear();
            alarmNotifyConfigIdProjectStatusHashMap.putAll(tmpAlarmNotifyConfigIdProjectStatusHashMap);
            alarmNotifyConfigIdKeywordHashMap.clear();
            alarmNotifyConfigIdKeywordHashMap.putAll(tmpAlarmNotifyConfigIdKeywordHashMap);
        }
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP：告警通知策略事件退出");
            return;
        }
        AlarmChange alarmChange = event.getData();
        if (null == alarmChange) {
            return;
        }
        if (Objects.equals(AlarmOperationTypeEnum.NOTE.getValue(), alarmChange.getOperationType())) {
            log.info("sequenceId:{},告警备注不发送告警通知", alarmChange.getSequenceId());
            return;
        }
        log.info("AlarmNotifyInstanceScheduler alarmChange arrived: {}", alarmChange);
        if (alarmNotifyConfigIdContentTemplateHashMap.isEmpty() || preCheckFailed())
            return;
        for (Map.Entry<Integer, String> alarmNotifyConfigEntry : alarmNotifyConfigIdContentTemplateHashMap.entrySet()) {
            if (!filterByCondition(alarmChange, alarmNotifyConfigEntry))
                continue;
            String notifyContent = notificationContentItemService.assembleNotificationContent(alarmChange.toActiveEvent(), alarmNotifyConfigEntry.getValue());
            AlarmNotifyExecutorDTO alarmNotifyExecutorDTO = new AlarmNotifyExecutorDTO();
            alarmNotifyExecutorDTO.setSerialNo(alarmChange.getSerialNo());
            alarmNotifyExecutorDTO.setStationId(alarmChange.getStationId());
            alarmNotifyExecutorDTO.setEventId(alarmChange.getEventId());
            alarmNotifyExecutorDTO.setEventName(alarmChange.getEventName());
            alarmNotifyExecutorDTO.setEventConditionId(alarmChange.getEventConditionId());
            alarmNotifyExecutorDTO.setEventSeverityId(alarmChange.getEventLevel());
            alarmNotifyExecutorDTO.setOperationType(alarmChange.getOperationType());
            alarmNotifyExecutorDTO.setEquipmentId(alarmChange.getEquipmentId());
            alarmNotifyExecutorDTO.setEquipmentName(alarmChange.getEquipmentName());
            alarmNotifyExecutorDTO.setResourceStructureId(alarmChange.getResourceStructureId());
            alarmNotifyExecutorDTO.setSequenceId(alarmChange.getSequenceId());
            alarmNotifyExecutorDTO.setAlarmNotifyContent(notifyContent);
            alarmNotifyExecutorDTO.setStartTime(alarmChange.getStartTime());
            alarmNotifyExecutorDTO.setEndTime(alarmChange.getEndTime());
            alarmNotifyExecutorDTO.setConfirmTime(alarmChange.getConfirmTime());
            String notifyDelay = alarmNotifyConfigIdNotifyDelayHashMap.get(alarmNotifyConfigEntry.getKey());
            boolean needNotifyDelay = checkNotifyDelay(notifyDelay);
            alarmNotifyExecutorDTO.setReCheckActiveEvent(needNotifyDelay);
            alarmNotifyExecutorDTO.setNotifyDelay(notifyDelay);
            AlarmNotifyInstanceExecutor alarmNotifyInstanceExecutor = new AlarmNotifyInstanceExecutor(alarmNotifyConfigEntry.getKey(), alarmNotifyExecutorDTO,
                    alarmNotifyElementConfigService, alarmNotifyNodeService, alarmNotifySegmentService, liveEventService, alarmNotifyExecutorService);

            if (alarmNotifyExecutorDTO.getOperationType() == 1 && needNotifyDelay) {//如果是告警开始，且告警过滤条件有设置延时，则将其加入线程池延时执行
                threadPoolExecutor.schedule(alarmNotifyInstanceExecutor, Integer.parseInt(notifyDelay), TimeUnit.SECONDS);
            } else {//将其加入线程池立即执行
                threadPoolExecutor.execute(alarmNotifyInstanceExecutor);
            }
        }
    }

    private boolean checkNotifyDelay(String notifyDelay) {
        return (null != notifyDelay && notifyDelay.trim().length() > 0 && !notifyDelay.equals("0"));
    }

    private boolean preCheckFailed() {
        return alarmNotifyConfigIdParkIdHashMap.isEmpty() && alarmNotifyConfigIdBuildingIdHashMap.isEmpty() &&
                alarmNotifyConfigIdFloorIdHashMap.isEmpty() && alarmNotifyConfigIdRoomIdHashMap.isEmpty() &&
                alarmNotifyConfigIdMdcIdHashMap.isEmpty() && alarmNotifyConfigIdEquipmentIdHashMap.isEmpty() &&
                alarmNotifyConfigIdEventIdHashMap.isEmpty() && alarmNotifyConfigIdBaseEquipmentIdHashMap.isEmpty() &&
                alarmNotifyConfigIdBaseTypeIdHashMap.isEmpty() && alarmNotifyConfigIdOperationTypeHashMap.isEmpty() &&
                alarmNotifyConfigIdEventSeverityHashMap.isEmpty() && alarmNotifyConfigIdResourceStructureIdHashMap.isEmpty() &&
                alarmNotifyConfigIdProjectStatusHashMap.isEmpty() && alarmNotifyConfigIdKeywordHashMap.isEmpty();
    }

    private boolean filterByCondition(AlarmChange alarmChange, Map.Entry<Integer, String> alarmNotifyConfigEntry) {
        if (alarmNotifyConfigEntry.getValue().trim().isEmpty()) {
            return false;
        }
        Map<Integer, ResourceStructure> resourceStructureHashMap = getFullResourceStructureHashMapById(alarmChange.getResourceStructureId());
        if (!alarmNotifyConfigIdParkIdHashMap.isEmpty() && alarmNotifyConfigIdParkIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdParkIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(resourceStructureHashMap.get(2).getResourceStructureId())) {
            return false;
        }
        if (!alarmNotifyConfigIdBuildingIdHashMap.isEmpty() && alarmNotifyConfigIdBuildingIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdBuildingIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(resourceStructureHashMap.get(3).getResourceStructureId())) {
            return false;
        }
        if (!alarmNotifyConfigIdFloorIdHashMap.isEmpty() && alarmNotifyConfigIdFloorIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdFloorIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(resourceStructureHashMap.get(4).getResourceStructureId())) {
            return false;
        }
        if (!alarmNotifyConfigIdRoomIdHashMap.isEmpty() && alarmNotifyConfigIdRoomIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdRoomIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(resourceStructureHashMap.get(5).getResourceStructureId())) {
            return false;
        }
        if (!alarmNotifyConfigIdEquipmentIdHashMap.isEmpty() && alarmNotifyConfigIdEquipmentIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdEquipmentIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getEquipmentId())) {
            return false;
        }
        if (!alarmNotifyConfigIdEventIdHashMap.isEmpty() && alarmNotifyConfigIdEventIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdEventIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getEventId())) {
            return false;
        }
        if (!alarmNotifyConfigIdBaseEquipmentIdHashMap.isEmpty() && alarmNotifyConfigIdBaseEquipmentIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && alarmChange.getBaseEquipmentId() != null && !alarmNotifyConfigIdBaseEquipmentIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getBaseEquipmentId())) {
            return false;
        }
        if (!alarmNotifyConfigIdBaseTypeIdHashMap.isEmpty() && alarmNotifyConfigIdBaseTypeIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && alarmChange.getBaseTypeId() != null && !alarmNotifyConfigIdBaseTypeIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getBaseTypeId())) {
            return false;
        }
        if (!alarmNotifyConfigIdOperationTypeHashMap.isEmpty() && alarmNotifyConfigIdOperationTypeHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdOperationTypeHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getOperationType())) {
            return false;
        }
        if (!alarmNotifyConfigIdResourceStructureIdHashMap.isEmpty() && alarmNotifyConfigIdResourceStructureIdHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !alarmNotifyConfigIdResourceStructureIdHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getResourceStructureId())) {
            return false;
        }
        if (!alarmNotifyConfigIdProjectStatusHashMap.isEmpty() && alarmNotifyConfigIdProjectStatusHashMap.get(alarmNotifyConfigEntry.getKey()) != null && !isMaintenanceStatusMatching(alarmChange, alarmNotifyConfigEntry)) {
            return false;
        }
        if (!alarmNotifyConfigIdKeywordHashMap.isEmpty() && alarmNotifyConfigIdKeywordHashMap.get(alarmNotifyConfigEntry.getKey()) != null && alarmNotifyConfigIdKeywordHashMap.get(alarmNotifyConfigEntry.getKey()) != "" && !isKeywordFilter(alarmChange, alarmNotifyConfigEntry)) {
            return false;
        }
        return alarmNotifyConfigIdEventSeverityHashMap.isEmpty() || alarmNotifyConfigIdEventSeverityHashMap.get(alarmNotifyConfigEntry.getKey()) == null || alarmNotifyConfigIdEventSeverityHashMap.get(alarmNotifyConfigEntry.getKey()).contains(alarmChange.getEventLevel());
    }

    private boolean isMaintenanceStatusMatching(AlarmChange alarmChange, Map.Entry<Integer, String> alarmNotifyConfigEntry) {
        boolean isProjectEnabled = alarmNotifyConfigIdProjectStatusHashMap.get(alarmNotifyConfigEntry.getKey());
        boolean isInMaintainState = !Objects.equals(1, alarmChange.getMaintainState());
        //处于工程状态并且告警是工程状态，或者不处于工程状态且告警不是工程状态
        return (isProjectEnabled && isInMaintainState) || (!isProjectEnabled && !isInMaintainState);
    }

    private boolean isKeywordFilter(AlarmChange alarmChange, Map.Entry<Integer, String> alarmNotifyConfigEntry) {
        String keyWord = alarmNotifyConfigIdKeywordHashMap.get(alarmNotifyConfigEntry.getKey());
        String fullResourceStructureName = resourceStructureManager.getFullPath(alarmChange.getResourceStructureId());
        String operationTypeName = AlarmOperationTypeEnum.getOperationTypeNameByValue(alarmChange.getOperationType());

        return fullResourceStructureName != null && fullResourceStructureName.contains(keyWord) ||//位置
                alarmChange.getBaseEquipmentName() != null && alarmChange.getBaseEquipmentName().contains(keyWord) ||//设备基类
                alarmChange.getEquipmentName() != null && alarmChange.getEquipmentName().contains(keyWord) ||//设备名称
                alarmChange.getBaseTypeName() != null && alarmChange.getBaseTypeName().contains(keyWord) ||//告警基类
                alarmChange.getEventName() != null && alarmChange.getEventName().contains(keyWord) ||//告警/事件
                alarmChange.getEventSeverity() != null && alarmChange.getEventSeverity().contains(keyWord) ||//告警等级
                operationTypeName != null && operationTypeName.contains(keyWord);//事件状态
    }

    private Map<Integer, ResourceStructure> getFullResourceStructureHashMapById(Integer resourceStructureId) {
        Map<Integer, ResourceStructure> resourceStructureHashMap = new HashMap<>();
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if (null != resourceStructure) {
            resourceStructureHashMap = resourceStructureManager.getAllParentStructureByPath(resourceStructure.getLevelOfPath());
        }
        return resourceStructureHashMap;
    }

}
