package com.siteweb.eventnotification.vo;

import com.siteweb.eventnotification.entity.AlarmNotifyConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyConfigVO
 * @createTime 2022-04-25 15:59:04
 */
@Data
@NoArgsConstructor
public class AlarmNotifyConfigVO {

    private Integer alarmNotifyConfigId;

    private String configName;

    private Boolean usedStatus;

    private String contentTemplate;

    private Integer notifyDelay;

    private String layout;

    private String description;

    public AlarmNotifyConfig build() {
        AlarmNotifyConfig alarmNotifyConfig = new AlarmNotifyConfig();
        BeanUtils.copyProperties(this, alarmNotifyConfig);
        return alarmNotifyConfig;
    }
}
