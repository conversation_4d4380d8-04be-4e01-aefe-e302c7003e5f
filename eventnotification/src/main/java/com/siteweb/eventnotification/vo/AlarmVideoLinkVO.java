package com.siteweb.eventnotification.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.siteweb.eventnotification.entity.AlarmVideoLinkMap;
import lombok.Data;

import java.util.List;

@Data
public class AlarmVideoLinkVO {
    /**
     * 主键id
     */
    private Integer alarmVideoLinkId;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否启用
     */
    private Boolean usedStatus;
    /**
     * 部门id
     */
    private Integer departmentId;
    /**
     * 联动类型，1视频弹窗，2抓图，3未来可能是视频录制
     */
    private String linkType;
    /**
     * 事件状态，1告警开始，2告警结束
     */
    private String operationType;
    /**
     * 抓图张数
     */
    private Integer snapshotCount;
    /**
     * 抓图间隔
     */
    private Integer snapshotInterval;

    private List<AlarmVideoLinkMapVO> alarmVideoLinkMapList;
}
