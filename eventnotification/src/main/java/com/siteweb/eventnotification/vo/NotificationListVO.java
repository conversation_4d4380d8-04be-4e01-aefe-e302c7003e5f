package com.siteweb.eventnotification.vo;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.eventnotification.entity.NotificationReceiver;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/3/20 13:34
 */
@Data
public class NotificationListVO {
    private Integer notificationId;

    /**
     * 是否已推送
     */
    private Boolean pushed;

    /**
     * 分类
     */
    private String category;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 颜色
     */
    private String color;

    /**
     * 图标
     */
    private String icon;

    /**
     * 用于导航到具体web页面的链接，Web
     */
    private String webLink;

    /**
     * 用于导航到具体app页面的链接，App
     */
    private String appLink;

    /**
     * 消息接收者对象
     */
    private List<NotificationReceiver> notificationReceivers;
    /**
     * 消息创建时间
     */
    private Date createTime;

    /**
     * 外部消息厂商请求标识
     */
    private String externalId;

    /**
     * 扩展参数
     */
    private String extParam;

    public JSONObject getExtParam() {
        return JSONUtil.parseObj(this.extParam);
    }
}
