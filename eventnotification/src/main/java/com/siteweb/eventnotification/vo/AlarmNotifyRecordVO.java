package com.siteweb.eventnotification.vo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

@Slf4j
@Data
public class AlarmNotifyRecordVO {
    /**
     * 告警时间起
     */
    private Date alarmStartTimeFrom;
    /**
     * 告警时间止
     */
    private Date alarmStartTimeTo;
    /**
     * 发送时间起
     */
    private Date sendTimeFrom;
    /**
     * 发送时间止
     */
    private Date sendTimeTo;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 发送方式
     */
    private String sendType;
    /**
     * 发送结果
     */
    private String sendResult;
}
