<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.batchtool.mapper.DriveStructureTemplateMapper">

    <resultMap type="com.siteweb.batchtool.entity.DriveStructureTemplate" id="TblDrivestructuretemplateMap">
        <result property="id" column="Id" jdbcType="INTEGER"/>
        <result property="filePath" column="filePath" jdbcType="VARCHAR"/>
        <result property="pid" column="pid" jdbcType="INTEGER"/>
        <result property="fileId" column="fileId" jdbcType="INTEGER"/>
        <result property="isDisk" column="isDisk" jdbcType="INTEGER"/>
        <result property="isUpload" column="isUpload" jdbcType="INTEGER"/>
        <result property="isFill" column="isFill" jdbcType="INTEGER"/>
        <result property="driveTemplateId" column="driveTemplateId" jdbcType="INTEGER"/>
        <result property="isLeaf" column="isLeaf" jdbcType="INTEGER"/>
        <result property="uploadTiming" column="uploadTiming" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findByDriveTemplateIds" resultMap="TblDrivestructuretemplateMap">
        select t1.id, t1.filePath, t1.pid, t1.isLeaf, t1.isDisk, t1.fileId, t1.isUpload, t1.isFill, t1.driveTemplateId, t1.uploadTiming, t2.fileName
        from tbl_drivestructuretemplate t1
        left join DiskFile t2 on t2.fileId = t1.fileId
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>

