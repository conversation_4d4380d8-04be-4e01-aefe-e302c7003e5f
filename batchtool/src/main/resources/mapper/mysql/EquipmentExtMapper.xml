<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.batchtool.mapper.EquipmentExtMapper">
    <sql id="SelectEquipmentSettingVo">
        SELECT monitor.workStationId, equipment.equipmentId, port.portId, sampler.samplerUnitId, sampler.monitorUnitId, -- 5
               ext.fileId, equipment.equipmentName, station.stationName, house.houseName, monitor.monitorUnitName, -- 10
               port.portName, port.setting, sampler.samplerUnitName, sampler.address, sampler.dllPath, -- 15
               sampler.spUnitInterval, ext.driveTemplateId, drive.driveTemplateName, ext.isReset, -- 20
               ext.isUpload, template.equipmentTemplateId, template.equipmentTemplateName, ext.fieldHash -- 24
        FROM tbl_equipment equipment
                 INNER JOIN tsl_monitorunit monitor ON equipment.monitorUnitId=monitor.monitorUnitId
                 INNER JOIN tsl_port port ON port.monitorUnitId=monitor.monitorUnitId
                 INNER JOIN tsl_samplerunit sampler ON sampler.monitorUnitId=port.monitorUnitId AND sampler.portId=port.portId AND equipment.samplerUnitId=sampler.samplerUnitId
                 INNER JOIN tbl_station station ON equipment.stationId=station.stationId
                 INNER JOIN tbl_house house ON house.stationId=station.stationId AND equipment.houseId=house.houseId
                 INNER JOIN tbl_equipmenttemplate template on equipment.equipmentTemplateId = template.equipmentTemplateId
                 LEFT JOIN tbl_equipmentExt ext ON ext.equipmentId = equipment.equipmentId
                 LEFT JOIN tbl_drivetemplate drive ON drive.id = ext.driveTemplateId
    </sql>

    <resultMap type="com.siteweb.batchtool.vo.EquipmentSettingVo" id="equipmentSettingVoMap">
        <result property="portId" column="portId" jdbcType="INTEGER"/>
        <result property="samplerUnitId" column="samplerUnitId" jdbcType="INTEGER"/>
        <result property="workStationId" column="workStationId" jdbcType="INTEGER"/>
        <result property="equipmentId" column="equipmentId" jdbcType="INTEGER"/>
        <result property="monitorUnitId" column="monitorUnitId" jdbcType="INTEGER"/>
        <result property="fileId" column="fileId" jdbcType="INTEGER"/>
        <result property="equipmentName" column="equipmentName" jdbcType="VARCHAR"/>
        <result property="stationName" column="stationName" jdbcType="VARCHAR"/>
        <result property="houseName" column="houseName" jdbcType="VARCHAR"/>
        <result property="monitorUnitName" column="monitorUnitName" jdbcType="VARCHAR"/>
        <result property="portName" column="portName" jdbcType="VARCHAR"/>
        <result property="setting" column="setting" jdbcType="VARCHAR"/>
        <result property="samplerUnitName" column="samplerUnitName" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="INTEGER"/>
        <result property="dllPath" column="dllPath" jdbcType="VARCHAR"/>
        <result property="spUnitInterval" column="spUnitInterval" jdbcType="DECIMAL"/>
        <result property="driveTemplateId" column="driveTemplateId" jdbcType="INTEGER"/>
        <result property="driveTemplateName" column="driveTemplateName" jdbcType="VARCHAR"/>
        <result property="isReset" column="isReset" jdbcType="INTEGER"/>
        <result property="equipmentTemplateId" column="equipmentTemplateId" jdbcType="INTEGER"/>
        <result property="equipmentTemplateName" column="equipmentTemplateName" jdbcType="VARCHAR"/>
        <result property="isUpload" column="isUpload" jdbcType="INTEGER"/>
        <result property="ipAddress" column="ipAddress" jdbcType="VARCHAR"/>
        <result property="portAttribute" column="portAttribute" jdbcType="VARCHAR"/>
        <result property="community" column="community" jdbcType="VARCHAR"/>
        <result property="writeCommunity" column="writeCommunity" jdbcType="VARCHAR"/>
        <result property="fieldHash" column="fieldHash" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO tbl_equipmentext(equipmentId, isReset, isUpload, driveTemplateId) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.equipmentId},#{item.isReset},#{item.isUpload},#{item.driveTemplateId})
        </foreach>
    </insert>

    <select id="findDestSettingListByEquipmentIds" resultMap="equipmentSettingVoMap">
        <include refid="SelectEquipmentSettingVo"/>
        WHERE port.portType = #{portType,jdbcType=INTEGER}
            AND equipment.equipmentId IN
            <foreach collection="equipmentIds" item="item" open="(" close=")" separator=",">
              #{item,jdbcType=INTEGER}
            </foreach>

    </select>
    <select id="getSettingManagerList" resultType="com.siteweb.batchtool.vo.EquipmentSettingVo">
        <include refid="SelectEquipmentSettingVo"/>
        WHERE port.portType=#{portType} ORDER BY
        monitor.monitorUnitId,port.portNo,sampler.samplerUnitId
    </select>
    <select id="existsEquipmentIds" resultType="java.lang.Integer">
        SELECT ext.equipmentId FROM tbl_equipmentExt ext WHERE ext.equipmentId IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findSettingManagerById" resultType="com.siteweb.batchtool.vo.EquipmentSettingVo">
        <include refid="SelectEquipmentSettingVo"/>
        WHERE equipment.equipmentId = #{id}
    </select>
    <select id="getNeedInitSampler" resultType="com.siteweb.batchtool.dto.NeedSamplerDto">
        SELECT sampler.id,port.PortNo
        FROM TBL_Equipment equipment
        INNER JOIN TSL_MonitorUnit monitor ON equipment.monitorUnitId = monitor.monitorUnitId
        INNER JOIN TSL_Port port ON port.monitorUnitId = monitor.monitorUnitId
        INNER JOIN TSL_SamplerUnit sampler ON sampler.monitorUnitId = port.monitorUnitId AND sampler.portId = port.portId AND equipment.samplerUnitId = sampler.samplerUnitId
        WHERE equipment.equipmentId IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateBatchByEquipmentId">
        <foreach collection="list" item="item" separator=";">
            UPDATE tbl_equipmentExt
            <set>
                driveTemplateId = #{item.driveTemplateId},
                fileId = #{item.fileId},
                isUpload = #{item.isUpload},
                isReset = #{item.isReset},
                fieldHash = #{item.fieldHash},
            </set>
            WHERE equipmentId = #{item.equipmentId}
        </foreach>
    </update>
    <update id="updatePort">
        UPDATE tsl_port `port`
        SET `port`.Setting = #{equipmentSettingUpdateDto.setting}
        WHERE `port`.PortId = #{equipmentSettingUpdateDto.portId}
          AND `port`.MonitorunitId = #{equipmentSettingUpdateDto.monitorUnitId}
    </update>
    <update id="updateSamplerUnit">
        UPDATE tsl_samplerunit sampler
        SET sampler.Address        = #{equipmentSettingUpdateDto.address},
            sampler.DllPath        = #{equipmentSettingUpdateDto.dllPath},
            sampler.SpUnitInterval = #{equipmentSettingUpdateDto.spUnitInterval}
        WHERE sampler.SamplerUnitId = #{equipmentSettingUpdateDto.samplerUnitId}
          AND sampler.PortId = #{equipmentSettingUpdateDto.portId}
          AND sampler.MonitorunitId = #{equipmentSettingUpdateDto.monitorUnitId}
    </update>
    <update id="updateTemplateByEquipmentId">
        UPDATE tbl_equipmentExt ext SET ext.driveTemplateId = #{driveTemplateId}  WHERE ext.equipmentId = #{equipmentId}
    </update>
    <update id="resetTemplateByTemplateId">
        UPDATE tbl_equipmentExt ext,tbl_equipment equipment
        SET ext.isReset = 1
        WHERE ext.equipmentId = equipment.EquipmentId
          AND equipment.EquipmentTemplateid = #{equipmentTemplateId}
    </update>
    <update id="updateTemplateFileById">
        UPDATE tbl_equipmentExt ext,tbl_equipment equipment
        SET ext.fileId   = #{fileId},
            ext.isUpload = #{isUpload}
        WHERE ext.equipmentId = equipment.EquipmentId
          AND equipment.EquipmentTemplateid = #{equipmentTemplateId}
    </update>
    <update id="updateFileIdByEquipmentId">
        UPDATE tbl_equipmentExt ext SET ext.fileId = #{fileId},ext.isUpload = #{isUpload} WHERE ext.equipmentId = #{equipmentId}
    </update>
    <update id="updateTemplateByTemplateId">
        UPDATE tbl_equipmentExt ext,tbl_equipment equipment
        SET ext.driveTemplateId = #{driveTemplateId}
        WHERE ext.equipmentId = equipment.EquipmentId
          AND equipment.EquipmentTemplateid = #{equipmentTemplateId}
    </update>
    <update id="batchInit">
        <foreach collection="list" item="item" separator=";">
            UPDATE tsl_samplerunit sampler SET sampler.dllpath = #{item.dllPath},sampler.address = #{item.address}
            WHERE sampler.id = #{item.id}
        </foreach>
    </update>
</mapper>

