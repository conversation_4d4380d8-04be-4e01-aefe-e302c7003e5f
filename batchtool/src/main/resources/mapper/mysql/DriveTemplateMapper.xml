<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.batchtool.mapper.DriveTemplateMapper">
    <update id="updateDefaultTemplate">
        UPDATE tbl_drivetemplate template
        SET template.isDefaultTemplate = 0
        WHERE template.driveTemplateType = #{driveTemplateType}
    </update>
    <select id="getDefaultTemplateByType" resultType="java.lang.Integer">
        SELECT drive.id FROM tbl_drivetemplate drive WHERE  drive.driveTemplateType = #{driveTemplateType} AND isDefaultTemplate = 1
    </select>
</mapper>