<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.batchtool.mapper.VirtualEquipmentMapper">
    <select id="findVirtualEquipmentBasicInfo"
            resultType="com.siteweb.batchtool.vo.VirtualEquipmentBasicInfoVo">
        SELECT
        te.EquipmentId AS virtualEquipmentId,
        te.EquipmentName AS virtualEquipmentName,
        tm.MonitorUnitName AS virtualMonitorUnitName,
        ts2.SamplerUnitName AS virtualSamplerUnitName,
        te2.EquipmentTemplateName AS virtualEquipmentTemplateName,
        house.HouseName AS virtualHouseName,
        port.portname as virtualPortName,
        ts.StationName AS virtualStationName,
        td.ItemValue AS virtualEquipmentCategoryName
        FROM tbl_equipment te
        INNER JOIN tbl_equipmenttemplate te2 ON te.EquipmentTemplateid = te2.EquipmentTemplateId
        INNER JOIN tbl_station ts ON ts.StationId = te.StationId
        INNER JOIN tsl_monitorunit tm ON tm.MonitorUnitId = te.MonitorUnitId AND ts.StationId = tm.StationId
        INNER JOIN tsl_samplerunit ts2 ON ts2.SamplerUnitId = te.SamplerUnitId AND ts2.MonitorUnitId = tm.MonitorUnitId
        INNER JOIN tsl_port port ON port.PortId = ts2.PortId
        INNER JOIN tbl_house house ON house.HouseId = te.HouseId and house.StationId = te.StationId
        INNER JOIN tbl_dataitem td ON td.EntryId = 7 AND td.ItemId = te.EquipmentCategory
        WHERE te.EquipmentId IN
        <foreach collection="equipmentIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findSplitInfo" resultType="com.siteweb.batchtool.dto.SplitEquipmentInfoDTO">
        SELECT monitor.MonitorUnitId,
               port.PortId,
               sampler.SamplerUnitId,
               equipment.EquipmentCategory
        FROM tbl_equipment equipment INNER JOIN tsl_monitorunit monitor ON equipment.monitorUnitId = monitor.monitorUnitId
                 INNER JOIN tsl_port port ON port.monitorUnitId = monitor.monitorUnitId
                 INNER JOIN tsl_samplerunit sampler ON sampler.monitorUnitId = port.monitorUnitId AND sampler.portId = port.portId AND equipment.samplerUnitId = sampler.samplerUnitId
        WHERE equipment.equipmentId = #{equipmentId}
    </select>
</mapper>