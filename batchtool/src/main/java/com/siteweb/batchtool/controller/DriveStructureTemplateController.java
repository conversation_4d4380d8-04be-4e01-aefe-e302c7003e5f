package com.siteweb.batchtool.controller;

import com.siteweb.batchtool.basic.manager.DriveStructurePlaceholderManager;
import com.siteweb.batchtool.dto.DriveStructureTemplateDTO;
import com.siteweb.batchtool.entity.DriveStructureTemplate;
import com.siteweb.batchtool.service.DriveStructureTemplateService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.utility.entity.DiskFile;
import com.siteweb.utility.service.DiskFileService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

/**
 * @Author: lzy
 * @Date: 2022/6/27 10:06
 */
@RestController
@RequestMapping("/api")
public class DriveStructureTemplateController {

    private static final String FILES = "addDriveStructureTemplate/";

    @Autowired
    private DriveStructureTemplateService driveStructureTemplateService;
    @Autowired
    private DiskFileService diskFileService;
    @Autowired
    private DriveStructurePlaceholderManager driveStructurePlaceholderManager;

    @ApiOperation("获取所有驱动结构(所有树结构)")
    @GetMapping("/drivestructuretemplates")
    public ResponseEntity<ResponseResult> findAllDriveStructureTemplate() {
        return ResponseHelper.successful(driveStructureTemplateService.findAllDriveStructureTemplate());
    }

    /**
     * 根据驱动模板id查询驱动树结构
     */
    @ApiOperation("根据驱动模板id查询驱动树结构")
    @GetMapping("/drivestructuretemplates/findbydrivetemplateid/{drivetemplateid}")
    public ResponseEntity<ResponseResult> findByDriveTemplateId(@PathVariable("drivetemplateid") Integer driveTemplateId) {
        return ResponseHelper.successful(driveStructureTemplateService.findByDriveTemplateIds(Collections.singletonList(driveTemplateId)));
    }

    @ApiOperation("根据驱动结构id获取某个驱动结构")
    @GetMapping("/drivestructuretemplates/{drivestructuretemplateid}")
    public ResponseEntity<ResponseResult> findById(@PathVariable("drivestructuretemplateid") Integer driveStructureTemplateId) {
        return ResponseHelper.successful(driveStructureTemplateService.findById(driveStructureTemplateId));
    }

    @ApiOperation("新增驱动结构")
    @PostMapping(value = "/drivestructuretemplates")
    public ResponseEntity<ResponseResult> create(@RequestBody DriveStructureTemplateDTO driveStructureTemplateDTO) {
        return ResponseHelper.successful(driveStructureTemplateService.create(driveStructureTemplateDTO.build()));
    }

    @ApiOperation("更新驱动结构")
    @PutMapping(value = "/drivestructuretemplates")
    public ResponseEntity<ResponseResult> update(@RequestBody DriveStructureTemplateDTO driveStructureTemplateDTO) {
        return ResponseHelper.successful(driveStructureTemplateService.update(driveStructureTemplateDTO.build()));
    }

    @ApiOperation("删除驱动结构")
    @DeleteMapping(value = "/drivestructuretemplates/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDriveStructureTemplate(@PathVariable Integer id) {
        return ResponseHelper.successful(driveStructureTemplateService.deleteById(id));
    }

    @ApiOperation("根据上传文件新增驱动结构")
    @PostMapping(value = "/adddrivestructuretemplate/**/{filename:.+}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addDriveStructureTemplate(@RequestParam("file") MultipartFile file,
                                                                    @RequestParam("pid") Integer pid,
                                                                    @RequestParam("driveTemplateId") Integer driveTemplateId,
                                                                    Integer operationType,
                                                                    HttpServletRequest request) {
        DiskFile diskFile = diskFileService.handleFileUpload(file, operationType, request.getServletPath().split(FILES)[1]);
        DriveStructureTemplate driveStructureTemplate = new DriveStructureTemplate();
        driveStructureTemplate.setPid(pid);
        driveStructureTemplate.setDriveTemplateId(driveTemplateId);
        return ResponseHelper.successful(driveStructureTemplateService.addDriveStructureTemplate(diskFile, driveStructureTemplate));
    }

    @ApiOperation("获取驱动结构占位符集合")
    @GetMapping(value = "drivestructureplaceholders")
    public ResponseEntity<ResponseResult> findDriveStructurePlaceholders() {
        return ResponseHelper.successful(driveStructurePlaceholderManager.getDriveStructurePlaceholder());
    }

}
