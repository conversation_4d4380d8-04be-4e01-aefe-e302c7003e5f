package com.siteweb.batchtool.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.batchtool.service.VirtualEquipmentService;
import com.siteweb.batchtool.vo.SplitEquipmentVo;
import com.siteweb.batchtool.vo.VirtualEquipmentSettingVo;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.StationManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "BA设备拆分控制类")
@RestController
@RequestMapping("/api")
@Slf4j
public class VirtualEquipmentController {
    /**
     * 生成的最大虚拟设备的数量
     */
    private static final int MAX_VIRTUAL_EQUIPMENT_COUNT = 200;
    @Autowired
    private VirtualEquipmentService virtualEquipmentService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private StationManager stationManager;
    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;

    @ApiOperation("获取层级资源(监控单元与设备)")
    @GetMapping("/resourcestructuremonitorunit")
    public ResponseEntity<ResponseResult> getResourceStructureEquipmentMonitorUnit(){
         return ResponseHelper.successful(virtualEquipmentService.getResourceStructureEquipmentMonitorUnit());
    }

    @ApiOperation("获取拆分设备信息")
    @GetMapping("/splitequipment/{equipmentId}")
    public ResponseEntity<ResponseResult> getSplitInfo(@PathVariable("equipmentId") Integer equipmentId){
           return ResponseHelper.successful(virtualEquipmentService.findSplitInfo(equipmentId));
    }

    @ApiOperation("拆分设备")
    @PostMapping("/splitequipment")
    public ResponseEntity<ResponseResult> buildSplitEquipment(@RequestBody SplitEquipmentVo splitEquipmentVo) {
        Equipment equipment = equipmentManager.getEquipmentById(splitEquipmentVo.getEquipmentId());
        Station station = stationManager.findStationById(equipment.getStationId());
        if (equipmentManager.findEquipmentByStationNameAndEquipmentName(station.getStationName(), splitEquipmentVo.getVirtualEquipmentName()) != null) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), localeMessageSourceUtil.getMessage("common.msg.equipmentexists"), HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(virtualEquipmentService.splitEquipment(splitEquipmentVo));
    }

    @ApiOperation("虚拟设备列表")
    @GetMapping("/virtualequipment")
    public ResponseEntity<ResponseResult> virtualEquipment(){
        return ResponseHelper.successful(virtualEquipmentService.virtualEquipmentList());
    }

    @ApiOperation("导出虚拟设备配置")
    @PostMapping("/export/equipmentsetting")
    public ResponseEntity<ResponseResult> exportVirtualEquipmentSetting(@RequestBody List<Integer> equipmentIds){
        return ResponseHelper.successful(virtualEquipmentService.exportVirtualEquipmentSetting(equipmentIds));
    }

    @ApiOperation("导入虚拟设备配置")
    @PostMapping("/import/equipmentsetting")
    public ResponseEntity<ResponseResult> importVirtualEquipmentSetting(@RequestBody VirtualEquipmentSettingVo virtualEquipmentSettingVo){
        if (CollUtil.size(virtualEquipmentSettingVo.getVirtualEquipmentExcelList()) > MAX_VIRTUAL_EQUIPMENT_COUNT) {
            log.error("导入的虚拟设备过多，请分批导入，最大支持200个虚拟设备导入");
            return ResponseHelper.failed("The import quantity is too large");
        }
        return ResponseHelper.successful(virtualEquipmentService.importVirtualEquipmentSetting(virtualEquipmentSettingVo));
    }

    @ApiOperation("删除虚拟设备")
    @DeleteMapping("/virtualequipment/{id}")
    public ResponseEntity<ResponseResult> deleteVirtualEquipment(@PathVariable Integer id){
        virtualEquipmentService.deleteVirtualEquipment(id);
        return ResponseHelper.successful();
    }

    @ApiOperation("获取虚拟设备详情")
    @GetMapping("/virtualequipment/{id}")
    public ResponseEntity<ResponseResult> getVirtualEquipmentDetail(@PathVariable Integer id){
        return ResponseHelper.successful(virtualEquipmentService.findVirtualEquipmentDetail(id));
    }
}
