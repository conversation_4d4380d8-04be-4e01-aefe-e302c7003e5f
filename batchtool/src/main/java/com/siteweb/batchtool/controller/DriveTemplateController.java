package com.siteweb.batchtool.controller;

import com.siteweb.batchtool.dto.DriveTemplateDTO;
import com.siteweb.batchtool.service.DriveTemplateService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Author: lzy
 * @Date: 2022/6/27 13:38
 */
@RestController
@RequestMapping("/api")
@Api(tags = "驱动模板控制器")
@SuppressWarnings("all")
public class DriveTemplateController {

    @Autowired
    DriveTemplateService driveTemplateService;

    @ApiOperation("获取驱动模板列表")
    @GetMapping("/drivetemplates")
    public ResponseEntity<ResponseResult> findAll() {
        return ResponseHelper.successful(driveTemplateService.findAllByType(null));
    }

    @ApiOperation("根据类型获取驱动模板列表")
    @GetMapping(value = "/drivetemplates", params = {"type"})
    public ResponseEntity<ResponseResult> findByType(@RequestParam("type") Integer type) {
        return ResponseHelper.successful(driveTemplateService.findAllByType(type));
    }

    @ApiOperation("根据驱动模板id获取单个驱动模板")
    @GetMapping("/drivetemplates/{drivetemplateid}")
    public ResponseEntity<ResponseResult> findById(@PathVariable("drivetemplateid") Integer driveTemplateId) {
        return ResponseHelper.successful(driveTemplateService.findById(driveTemplateId));
    }

    @ApiOperation("新增驱动模板")
    @PostMapping(value = "/drivetemplates")
    public ResponseEntity<ResponseResult> create(@Valid @RequestBody DriveTemplateDTO driveTemplateDTO) {
        return ResponseHelper.successful(driveTemplateService.create(driveTemplateDTO.build()));
    }

    @ApiOperation("更新驱动模板")
    @PutMapping(value = "/drivetemplates")
    public ResponseEntity<ResponseResult> update(@Valid @RequestBody DriveTemplateDTO driveTemplateDTO) {
        if (driveTemplateDTO.getId() == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(driveTemplateService.update(driveTemplateDTO.build()));
    }

    @ApiOperation("根据id删除驱动模板")
    @DeleteMapping(value = "/drivetemplates/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteById(@PathVariable Integer id) {
        return ResponseHelper.successful(driveTemplateService.deleteById(id));
    }

}
