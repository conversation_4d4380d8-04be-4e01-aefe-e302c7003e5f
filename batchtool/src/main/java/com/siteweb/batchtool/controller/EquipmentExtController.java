package com.siteweb.batchtool.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.batchtool.dto.BuildConfigDTO;
import com.siteweb.batchtool.dto.EquipmentExtUpdateDto;
import com.siteweb.batchtool.dto.EquipmentSettingDto;
import com.siteweb.batchtool.dto.EquipmentSettingUpdateDto;
import com.siteweb.batchtool.service.EquipmentExtService;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备控制器
 *
 * <AUTHOR>
 * @date 2022/03/28
 */
@Api(tags = "设备模板批量工具控制器")
@Validated
@RestController
@RequestMapping("/api")
@Slf4j
public class EquipmentExtController {

    @Autowired
    EquipmentExtService equipmentExtService;

    @ApiOperation("配置管理列表")
    @GetMapping("/settingmanager")
    public ResponseEntity<ResponseResult> getSettingManager(@Validated EquipmentSettingDto equipmentSettingDto) {
        List<EquipmentSettingVo> settingManager = equipmentExtService.getSettingManager(equipmentSettingDto);
        //初始化设备扩展配置
        equipmentExtService.initExt(settingManager.stream().map(EquipmentSettingVo::getEquipmentId).collect(Collectors.toList()),equipmentSettingDto.getPortType());
        return ResponseHelper.successful(settingManager);
    }

    @ApiOperation("配置管理信息根据设备id")
    @GetMapping("/settingmanager/{id}")
    public ResponseEntity<ResponseResult> getSettingManagerById(@PathVariable("id") Integer id){
        EquipmentSettingVo settingManager = equipmentExtService.getSettingManagerById(id);
        return ResponseHelper.successful(settingManager);
    }

    @ApiOperation("更新设备信息")
    @PutMapping("/updateequipment")
    public ResponseEntity<ResponseResult> updateEquipment(@Validated @RequestBody EquipmentSettingUpdateDto equipmentSettingUpdateDto) {
        return ResponseHelper.successful(equipmentExtService.updateEquipment(equipmentSettingUpdateDto));
    }

    @ApiOperation("上传配置")
    @PutMapping("/uploadsetting")
    public ResponseEntity<ResponseResult> uploadSetting(@Validated @RequestBody EquipmentExtUpdateDto equipmentExtUpdateDto) {
        if (ObjectUtil.isNull(equipmentExtUpdateDto.getFileId())) {
            return ResponseHelper.failed("1001", "fileId not exits", HttpStatus.OK);
        }
        return ResponseHelper.successful(equipmentExtService.uploadSetting(equipmentExtUpdateDto));
    }

    @ApiOperation("删除配置")
    @DeleteMapping("/deletesetting")
    public ResponseEntity<ResponseResult> deleteSetting(@Validated EquipmentExtUpdateDto equipmentExtUpdateDto) {
        return ResponseHelper.successful(equipmentExtService.deleteSetting(equipmentExtUpdateDto));
    }

    @ApiOperation("修改引用驱动模板")
    @PutMapping("/updatetemplate")
    public ResponseEntity<ResponseResult> updateTemplate(@Validated @RequestBody EquipmentExtUpdateDto equipmentExtUpdateDto){
        if (ObjectUtil.isNull(equipmentExtUpdateDto.getDriveTemplateId())) {
            return ResponseHelper.failed("1001", "driveTemplateId not exits", HttpStatus.OK);
        }
        return ResponseHelper.successful(equipmentExtService.updateTemplateByEquipmentId(equipmentExtUpdateDto));
    }

    /**
     * 生成配置
     *
     * @param buildConfigDTO
     * @return
     */
    @ApiOperation("生成配置")
    @PostMapping("/buildconfig")
    public ResponseEntity<ResponseResult> buildConfig(@Validated @RequestBody BuildConfigDTO buildConfigDTO, HttpServletResponse response) {
        if (buildConfigDTO == null || CollUtil.isEmpty(buildConfigDTO.getEquipmentIds()) || buildConfigDTO.getPortType() == null) {
            return ResponseHelper.failed("1001", "equipmentids not exits", HttpStatus.OK);
        }
        equipmentExtService.buildConfig(buildConfigDTO, response);
        return ResponseEntity.ok().build();
    }

    /**
     * 初始化采集配置
     * @param buildConfigDTO 初始化采集配置
     * @return 初始化成功多少条
     */
    @ApiOperation("初始化配置")
    @PutMapping("/initsampler")
    public ResponseEntity<ResponseResult> initSampler(@Validated @RequestBody BuildConfigDTO buildConfigDTO){
        if (buildConfigDTO == null || CollUtil.isEmpty(buildConfigDTO.getEquipmentIds())) {
            return ResponseHelper.failed("1001", "equipmentids not exits", HttpStatus.OK);
        }
        return ResponseHelper.successful(equipmentExtService.initSampler(buildConfigDTO));
    }
}
