package com.siteweb.batchtool.dto;

import com.siteweb.batchtool.entity.DriveStructureTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * 驱动结构模板表
 */
@Data
@NoArgsConstructor
@ApiModel("驱动结构模板DTO")
public class DriveStructureTemplateDTO {
    @ApiModelProperty("驱动结构模板id")
    private Integer id;
    @ApiModelProperty("驱动结构模板名称")
    private String templateName;
    @ApiModelProperty("结构路径")
    @NotEmpty(message = "结构路径不能为空")
    private String filePath;
    @ApiModelProperty("上级驱动模板id")
    private Integer pid;
    @ApiModelProperty("是否是叶子阶段")
    private Integer isLeaf;
    @ApiModelProperty("是否是目录")
    private Integer isDisk;
    @ApiModelProperty("模板文件id")
    private Long fileId;
    @ApiModelProperty("是否需要上传")
    private Integer isUpload;
    @ApiModelProperty("是否需要填充内容")
    private Integer isFill;
    @ApiModelProperty("驱动模板id")
    private Integer driveTemplateId;
    @ApiModelProperty("上传时机 0-模板创建时 1-生成配置时")
    private Integer uploadTiming;

    public DriveStructureTemplate build() {
        DriveStructureTemplate driveStructureTemplate = new DriveStructureTemplate();
        driveStructureTemplate.setId(this.id);
        driveStructureTemplate.setFilePath(this.filePath);
        driveStructureTemplate.setIsLeaf(this.isLeaf);
        driveStructureTemplate.setFileId(this.fileId);
        driveStructureTemplate.setPid(this.pid);
        driveStructureTemplate.setIsDisk(this.isDisk);
        driveStructureTemplate.setIsUpload(this.isUpload);
        driveStructureTemplate.setIsFill(this.isFill);
        driveStructureTemplate.setDriveTemplateId(this.driveTemplateId);
        driveStructureTemplate.setUploadTiming(this.uploadTiming);
        return driveStructureTemplate;
    }
}
