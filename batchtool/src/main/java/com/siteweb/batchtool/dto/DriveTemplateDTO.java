package com.siteweb.batchtool.dto;

import com.siteweb.batchtool.entity.DriveTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 驱动模板表
 */
@Data
@NoArgsConstructor
@ApiModel("驱动模板表DTO")
public class DriveTemplateDTO {
    @ApiModelProperty("驱动模板id")
    private Integer id;
    @ApiModelProperty("驱动模板名称")
    private String driveTemplateName;
    @ApiModelProperty("驱动模板描述")
    private String driverTemplateDescribe;
    @ApiModelProperty("是否是默认模板")
    private Integer isDefaultTemplate;
    @NotNull(message = "驱动模板类型不能为空")
    @ApiModelProperty("驱动模板类型 1-SNMP 2-BACNet")
    private Integer driveTemplateType;

    public DriveTemplate build() {
        DriveTemplate driveTemplate = new DriveTemplate();
        driveTemplate.setId(this.id);
        driveTemplate.setDriveTemplateName(this.driveTemplateName);
        driveTemplate.setDriverTemplateDescribe(this.driverTemplateDescribe);
        driveTemplate.setIsDefaultTemplate(this.isDefaultTemplate);
        driveTemplate.setDriveTemplateType(this.driveTemplateType);
        return driveTemplate;
    }
}
