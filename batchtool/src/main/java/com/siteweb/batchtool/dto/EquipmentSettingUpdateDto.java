package com.siteweb.batchtool.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 配置管理更新Dto
 *
 * <AUTHOR>
 * @date 2022/03/30
 */
@ApiModel("配置管理更新Dto")
@Data
public class EquipmentSettingUpdateDto {
    /**
     * 端口设置
     */
    @NotNull(message = "设备id不能为空")
    @ApiModelProperty("设备id")
    private Integer equipmentId;
    /**
     * 端口id
     */
    @ApiModelProperty("端口id")
    @NotNull(message = "端口id不能为空")
    private Integer portId;
    /**
     * 采集单元ID
     */
    @ApiModelProperty("采集单元ID")
    @NotNull(message = "采集单元ID不能为空")
    private Integer samplerUnitId;

    /**
     * 监控单元id
     */
    @ApiModelProperty("监控单元id")
    @NotNull(message = "监控单元id不能为空")
    private Integer monitorUnitId;
    /**
     * 端口设置
     */
    @JsonIgnore
    private String setting;
    /**
     * ip地址
     */
    @NotBlank(message = "ip地址不能为空")
    @ApiModelProperty("ip地址")
    private String ipAddress;
    /**
     * 端口号/读写属性
     */
    @NotBlank(message = "端口号/读写属性不能为空")
    @ApiModelProperty("端口号/读写属性")
    private String portAttribute;
    /**
     * 采集单元地址
     */
    @NotNull(message = "采集单元地址不能为空")
    @ApiModelProperty("采集单元地址")
    private Integer address;
    /**
     * 采集动态库地址
     */
    @NotBlank(message = "采集动态库地址不能为空")
    @ApiModelProperty("采集动态库地址")
    private String dllPath;
    /**
     * 采集周期
     */
    @NotNull(message = "采集周期不能为空")
    @ApiModelProperty("采集周期")
    private Double spUnitInterval;
    /**
     * 驱动模板id不能为空
     */
    @NotNull(message = "驱动模板id不能为空")
    @ApiModelProperty("驱动模板id")
    private Integer driveTemplateId;
}
