package com.siteweb.batchtool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 设备扩展更新dto
 *
 * <AUTHOR>
 * @date 2022/04/02
 */
@ApiModel("设备扩展更新dto")
@Data
public class EquipmentExtUpdateDto {
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    @NotNull(message = "设备id不能为空")
    private Integer equipmentId;
    /**
     * 文件id
     */
    @ApiModelProperty("文件id")
    private Integer fileId;
    /**
     * 引用驱动模板id
     */
    @ApiModelProperty("引用驱动模板id")
    private Integer driveTemplateId;

    /**
     * 是否全部删除/上传/切换
     */
    @ApiModelProperty("是否全部删除/上传/切换 0否 1是")
    private int isAll;
}
