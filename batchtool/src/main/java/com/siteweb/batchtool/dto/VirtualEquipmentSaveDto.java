package com.siteweb.batchtool.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 虚拟设备添加dto
 * <AUTHOR>
 * @date 2022/07/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VirtualEquipmentSaveDto {
    /**
     * 源设备id(由于虚拟设备中很多信息不是必填项,但数据库中是必填，可使用源设备中的信息代替)
     */
    private Integer equipmentId;

    /**
     * 虚拟设备名称
     */
    private String virtualEquipmentName;
    /**
     * 使用的虚拟模板id
     */
    private Integer virtualTemplateId;
    /**
     * 采集单元id
     */
    private Integer samplerUnitId;
     /**
     * 设备类型（非标准）
     */
    private Integer equipmentCategory;
    /**
     * 层级id
     */
    private Integer resourceStructureId;
    /**
     * 局房id
     */
    private Integer houseId;
}
