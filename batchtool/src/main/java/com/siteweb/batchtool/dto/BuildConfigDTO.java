package com.siteweb.batchtool.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 生成配置入参对象
 *
 * @Author: lzy
 * @Date: 2022/4/1 9:47
 */
@Data
public class BuildConfigDTO {
    @ApiModelProperty("设备id集合")
    List<Integer> equipmentIds;
    /**
     * 设备类型 5.虚拟设备
     */
    @ApiModelProperty("设备类型 5.虚拟设备 32 BACNet端口 33 SNMP端口")
    @NotNull(message = "设备类型必填")
    private Integer portType;
}
