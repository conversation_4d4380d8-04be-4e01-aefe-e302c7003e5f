package com.siteweb.batchtool.dto;

import com.siteweb.monitoring.entity.Equipment;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/07/29
 */
@NoArgsConstructor
@Data
public class MonitorSignalUnitSaveDto {
    public MonitorSignalUnitSaveDto(Equipment virtualEquipment, Integer signalId, Integer virtualSignalId, Integer equipmentId) {
        this.virtualEquipment = virtualEquipment;
        this.signalId = signalId;
        this.virtualSignalId = virtualSignalId;
        this.equipmentId = equipmentId;
    }

    /**
     * 虚拟设备实体
     */
    private Equipment virtualEquipment;
    /**
     * 信号id
     */
    private Integer signalId;
    /**
     * 虚拟信号id
     */
    private Integer virtualSignalId;
    /**
     * 设备id
     */
    private Integer equipmentId;
}
