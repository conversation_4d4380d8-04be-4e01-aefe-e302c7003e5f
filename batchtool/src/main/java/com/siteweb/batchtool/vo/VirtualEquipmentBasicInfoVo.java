package com.siteweb.batchtool.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 虚拟设备基础信息
 * <AUTHOR>
 * @date 2022/07/20
 */
@NoArgsConstructor
@Data
public class VirtualEquipmentBasicInfoVo {
    /**
     * 虚拟设备Id
     */
    private Integer virtualEquipmentId;
    /**
     * 虚拟设备名称
     */
    private String virtualEquipmentName;
    /**
     * 采集单元名称
     */
    private String virtualSamplerUnitName;
    /**
     * 监控单元名称
     */
    private String virtualMonitorUnitName;
    /**
     * 虚拟模板名称
     */
    private String virtualEquipmentTemplateName;
    /**
     * 机房名称
     */
    private String virtualHouseName;
    /**
     * 局站名称
     */
    private String virtualStationName;
    /**
     * 设备类型（非标准）
     */
    private String virtualEquipmentCategoryName;
    /**
     * 虚拟端口名称
     */
    private String virtualPortName;
}
