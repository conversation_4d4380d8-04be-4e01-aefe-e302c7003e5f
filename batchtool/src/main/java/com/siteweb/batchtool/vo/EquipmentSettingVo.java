package com.siteweb.batchtool.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.batchtool.basic.anno.DriveStructurePlaceholderAnno;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * 设备模板设置VO
 *
 * <AUTHOR>
 * @date 2022/03/28
 */
@ApiModel("设备模板设置VO")
@Data
public class EquipmentSettingVo {

    /**
     * 端口主键id
     */
    @ApiModelProperty("端口主键id")
    private Integer portId;
    /**
     * 采集单元id
     */
    @ApiModelProperty("采集单元id")
    private Integer samplerUnitId;
    /**
     * 工作站id
     */
    @ApiModelProperty("工作站id")
    private Integer workStationId;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Integer equipmentId;
    /**
     * 监控单元id
     */
    @ApiModelProperty("监控单元id")
    private Integer monitorUnitId;
    /**
     * 文件id
     */
    @ApiModelProperty("文件id")
    private Long fileId;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @DriveStructurePlaceholderAnno("设备名称")
    private String equipmentName;
    /**
     * 局站名
     */
    @ApiModelProperty("局站名")
    @DriveStructurePlaceholderAnno("局站名")
    private String stationName;
    /**
     * 局房名
     */
    @ApiModelProperty("局房名")
    @DriveStructurePlaceholderAnno("局房名")
    private String houseName;
    /**
     * 监控单元名
     */
    @ApiModelProperty("监控单元名")
    @DriveStructurePlaceholderAnno("监控单元名")
    private String monitorUnitName;
    /**
     * 端口名
     */
    @ApiModelProperty("端口名")
    @DriveStructurePlaceholderAnno("端口名")
    private String portName;
    /**
     * 端口设置
     */
    @ApiModelProperty("端口设置")
    @DriveStructurePlaceholderAnno("端口设置")
    private String setting;
    /**
     * 采集单元名称
     */
    @ApiModelProperty("采集单元名称")
    @DriveStructurePlaceholderAnno("采集单元名称")
    private String samplerUnitName;

    /**
     * 采集单元地址
     */
    @ApiModelProperty("采集单元地址")
    @DriveStructurePlaceholderAnno("采集单元地址")
    private Integer address;
    /**
     * 采集动态库地址
     */
    @ApiModelProperty("采集动态库地址")
    @DriveStructurePlaceholderAnno("动态库名称")
    private String dllPath;
    /**
     * 采集周期
     */
    @ApiModelProperty("采集周期")
    @DriveStructurePlaceholderAnno("采集周期")
    private Double spUnitInterval;
    /**
     * 引用驱动模板id
     */
    @ApiModelProperty("引用驱动模板id")
    private Integer driveTemplateId;
    /**
     * 引用驱动模板名称
     */
    @ApiModelProperty("引用驱动模板名称")
    @DriveStructurePlaceholderAnno("引用驱动模板名称")
    private String driveTemplateName;
    /**
     * 是否需要重新生成 0否 1是
     */
    @ApiModelProperty("是否需要重新生成  0否 1是")
    private Integer isReset;

    /**
     * 引用设备模板Id
     */
    @ApiModelProperty("引用设备模板Id")
    private Integer equipmentTemplateId;

    /**
     * 引用设备模板名称
     */
    @ApiModelProperty("引用设备模板名称")
    @DriveStructurePlaceholderAnno("引用设备模板名称")
    private String equipmentTemplateName;
    /**
     * 警告字段集合
     */
    @ApiModelProperty("警告字段集合")
    private Set<String> warningList = new HashSet<>(8);
    /**
     * 是否上传
     */
    @ApiModelProperty("是否上传  1是 0否")
    private Integer isUpload;

    /**
     * ip地址
     */
    @ApiModelProperty("ip地址")
    private String ipAddress;
    /**
     * 端口号/读写属性
     */
    @ApiModelProperty("端口号/读写属性")
    @DriveStructurePlaceholderAnno("端口号/读写属性（BACNet=端口号，SNMP=读写属性）")
    private String portAttribute;

    /**
     * 读权限(snmp中cfg文件中使用)
     */
    @ApiModelProperty("读权限")
    @DriveStructurePlaceholderAnno("读权限")
    private String community;

    /**
     * 写权限(snmp中cfg文件中使用)
     */
    @ApiModelProperty("写权限")
    @DriveStructurePlaceholderAnno("写权限")
    private String writeCommunity;

    /**
     * 设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值
     */
    @ApiModelProperty("设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值")
    private Integer fieldHash;


    /**
     * 设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值
     *
     * @return int 哈希值
     */
    public Integer fieldHashValue() {
        return Objects.hash(this.portName, this.setting, this.address, this.dllPath, this.spUnitInterval, this.equipmentId);
    }

    public Integer getIsReset() {
        return Optional.ofNullable(isReset).orElse(1);
    }

    public List<String> breakUpSetting() {
        return CharSequenceUtil.split(this.setting, '/').stream().toList();
    }

    public String getIpAddress() {
        return breakUpSetting().get(0);
    }

    public String getPortAttribute() {
        List<String> strArr = breakUpSetting();
        if (strArr.size() >= 2) {
            return strArr.get(1);
        }
        return portAttribute;
    }

    public String getCommunity() {
        String portAttributeTemp = getPortAttribute();
        if (CharSequenceUtil.isNotEmpty(portAttributeTemp) && portAttributeTemp.contains(":")) {
            List<String> permissionList = CharSequenceUtil.split(portAttributeTemp, ":");
            if (CollUtil.isNotEmpty(permissionList)) {
                return permissionList.get(0);
            }
        }
        return this.community;
    }

    public String getWriteCommunity() {
        String portAttributeTemp = getPortAttribute();
        if (CharSequenceUtil.isNotEmpty(portAttributeTemp) && portAttributeTemp.contains(":")) {
            List<String> permissionList = CharSequenceUtil.split(portAttributeTemp, ":");
            if (CollUtil.isNotEmpty(permissionList) && permissionList.size() >= 2) {
                return permissionList.get(1);
            }
        }
        return writeCommunity;
    }

}
