package com.siteweb.batchtool.vo;

import com.siteweb.monitoring.dto.MonitorUnitDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class MonitorUnitVo {
    public MonitorUnitVo(MonitorUnitDTO monitorUnitDTO) {
        this.monitorUnitId = monitorUnitDTO.getMonitorUnitId();
        this.monitorUnitName = monitorUnitDTO.getMonitorUnitName();
    }

    /**
     * 监控单元id
     */
    private Integer monitorUnitId;
    /**
     * 监控单元名称
     */
    private String monitorUnitName;
    /**
     * 设备集合
     */
    private List<EquipmentVo> equipmentChildren;

    public List<EquipmentVo> getEquipmentChildren() {
        if (equipmentChildren == null) {
            equipmentChildren = new ArrayList<>();
        }
        return equipmentChildren;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MonitorUnitVo that = (MonitorUnitVo) o;

        if (!Objects.equals(monitorUnitId, that.monitorUnitId))
            return false;
        return Objects.equals(monitorUnitName, that.monitorUnitName);
    }

    @Override
    public int hashCode() {
        int result = monitorUnitId != null ? monitorUnitId.hashCode() : 0;
        result = 31 * result + (monitorUnitName != null ? monitorUnitName.hashCode() : 0);
        return result;
    }
}
