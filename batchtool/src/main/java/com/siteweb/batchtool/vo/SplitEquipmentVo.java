package com.siteweb.batchtool.vo;

import lombok.Data;

import java.util.List;

@Data
public class SplitEquipmentVo {
    /**
     * 第一个选中的设备id
     */
    private Integer equipmentId;
    /**
     * 虚拟设备名称
     */
    private String virtualEquipmentName;
    /**
     * 采集单元id
     */
    private Integer samplerUnitId;
    /**
     * 设备类型（非标准）
     */
    private Integer equipmentCategory;

    /**
     * 设备拆分信息详情
     */
    private List<EquipmentSplitInfoVo> equipmentSplitInfoVos;
}
