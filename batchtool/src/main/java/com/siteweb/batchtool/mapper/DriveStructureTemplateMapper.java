package com.siteweb.batchtool.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.siteweb.batchtool.entity.DriveStructureTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/27 10:12
 */
public interface DriveStructureTemplateMapper extends BaseMapper<DriveStructureTemplate> {
    List<DriveStructureTemplate> findByDriveTemplateIds(@Param(Constants.WRAPPER) Wrapper<DriveStructureTemplate> wrapper);
}
