package com.siteweb.batchtool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.batchtool.dto.EquipmentSettingUpdateDto;
import com.siteweb.batchtool.dto.NeedSamplerDto;
import com.siteweb.batchtool.entity.EquipmentExt;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/28 10:40
 */
public interface EquipmentExtMapper extends BaseMapper<EquipmentExt> {

    List<EquipmentSettingVo> findDestSettingListByEquipmentIds(@Param("portType") Integer portType, @Param("equipmentIds") List<Integer> equipmentIds);

    int updateBatchByEquipmentId(@Param("list") List<EquipmentExt> collect);

    List<EquipmentSettingVo> getSettingManagerList(@Param("portType") Integer portType);


    /**
     * 根据设备ids查询已存在的扩展数据
     * @param equipmentIds 设备ids
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> existsEquipmentIds(@Param("list") List<Integer> equipmentIds);

    /**
     * 批量插入扩展设备
     * @param equipmentSaveList 设备拓展集合
     */
    void batchInsert(@Param("list") List<EquipmentExt> equipmentSaveList);

    /**
     * 查找配置列表id
     * @param id 设备id
     * @return {@link EquipmentSettingVo}
     */
    EquipmentSettingVo findSettingManagerById(@Param("id") Integer id);

    /**
     * 更新设备端口信息
     *
     * @param equipmentSettingUpdateDto 更新设备dto
     * @return {@link Integer}
     */
    void updatePort(@Param("equipmentSettingUpdateDto") EquipmentSettingUpdateDto equipmentSettingUpdateDto);

    /**
     * 更新采集单元信息
     *
     * @param equipmentSettingUpdateDto 更新设备dto
     * @return {@link Integer}
     */
    void updateSamplerUnit(@Param("equipmentSettingUpdateDto") EquipmentSettingUpdateDto equipmentSettingUpdateDto);

    /**
     * 更新设备的引用驱动模板
     *
     * @param equipmentId     设备id
     * @param driveTemplateId 引用驱动模板id
     * @return {@link Integer} 更新条数
     */
    Integer updateTemplateByEquipmentId(@Param("equipmentId") Integer equipmentId, @Param("driveTemplateId") Integer driveTemplateId);

    /**
     * 重新设置模板设备
     *
     * @param equipmentTemplateId 设备引用模板id
     */
    void resetTemplateByTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId);

    /**
     * 更新文件id根据模板id
     *
     * @param equipmentTemplateId 引用模板id
     * @param fileId              驱动模板文件id
     * @param isUpload            是否上传
     * @return {@link Integer}
     */
    Integer updateTemplateFileById(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("fileId") Integer fileId, @Param("isUpload") Integer isUpload);

    /**
     * 更新模板的驱动模板文件根域设备id
     *
     * @param equipmentId 设备id
     * @param fileId      驱动模板文件id
     * @param isUpload    是否上传
     * @return {@link Integer}
     */
    Integer updateFileIdByEquipmentId(@Param("equipmentId") Integer equipmentId, @Param("fileId") Integer fileId, @Param("isUpload") Integer isUpload);

    /**
     * 修改驱动模板根据引用模板
     *
     * @param equipmentTemplateId 设备引用模板id
     * @param driveTemplateId     驱动模板id
     * @return {@link Integer} 修改行数
     */
    Integer updateTemplateByTemplateId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("driveTemplateId") Integer driveTemplateId);

    /**
     * 通过设备ids查询采集单元信息
     *
     * @param equipmentIds 设备ids
     * @return {@link List}<{@link NeedSamplerDto}> 采集单元信息
     */
    List<NeedSamplerDto> getNeedInitSampler(@Param("list") List<Integer> equipmentIds);

    /**
     * 批量初始化采集单元地址与采集动态库地址
     * @param portList
     */
    void batchInit(@Param("list") List<NeedSamplerDto> portList);
}
