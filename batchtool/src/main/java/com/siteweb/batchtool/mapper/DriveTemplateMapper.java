package com.siteweb.batchtool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.batchtool.entity.DriveTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: lzy
 * @Date: 2022/6/27 13:43
 */
public interface DriveTemplateMapper extends BaseMapper<DriveTemplate> {
    Integer getDefaultTemplateByType(@Param("driveTemplateType") Integer driveTemplateType);

    void updateDefaultTemplate(Integer driveTemplateType);
}
