package com.siteweb.batchtool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.batchtool.dto.SplitEquipmentInfoDTO;
import com.siteweb.batchtool.vo.VirtualEquipmentBasicInfoVo;
import com.siteweb.monitoring.entity.Equipment;

import java.util.List;

public interface VirtualEquipmentMapper extends BaseMapper<Equipment> {
    List<VirtualEquipmentBasicInfoVo> findVirtualEquipmentBasicInfo(List<Integer> equipmentIds);

    SplitEquipmentInfoDTO findSplitInfo(Integer equipmentId);
}
