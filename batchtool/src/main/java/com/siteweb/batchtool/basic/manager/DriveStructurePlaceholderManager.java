package com.siteweb.batchtool.basic.manager;

import cn.hutool.core.util.ReflectUtil;
import com.siteweb.batchtool.basic.anno.DriveStructurePlaceholderAnno;
import com.siteweb.batchtool.entity.DriveStructurePlaceholder;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/5/11 13:45
 */
@Component
public class DriveStructurePlaceholderManager {
    private List<DriveStructurePlaceholder> driveStructurePlaceholder = new ArrayList<>();

    public List<DriveStructurePlaceholder> getDriveStructurePlaceholder() {
        return driveStructurePlaceholder;
    }

    @PostConstruct
    public void init() {
        Field[] fields = ReflectUtil.getFields(EquipmentSettingVo.class);
        ArrayList<DriveStructurePlaceholder> driveStructurePlaceholders = new ArrayList<>(fields.length);
        for (Field field : fields) {
            DriveStructurePlaceholderAnno anno = field.getAnnotation(DriveStructurePlaceholderAnno.class);
            if (anno == null) {
                continue;
            }
            driveStructurePlaceholders.add(new DriveStructurePlaceholder(field.getName(), anno.value(), String.format("%s (%s)", anno.value(), field.getName())));
        }
        driveStructurePlaceholder = driveStructurePlaceholders;
    }
}
