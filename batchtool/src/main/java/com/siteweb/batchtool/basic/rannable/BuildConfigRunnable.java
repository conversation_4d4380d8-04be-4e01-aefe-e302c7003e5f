package com.siteweb.batchtool.basic.rannable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.batchtool.basic.config.BuildFileConfig;
import com.siteweb.batchtool.entity.DriveStructureTemplate;
import com.siteweb.batchtool.entity.DriveTemplate;
import com.siteweb.batchtool.service.impl.EquipmentExtServiceImpl;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.common.util.RegularUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.ini4j.Wini;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 生成配置任务
 *
 * @Author: lzy
 * @Date: 2022/4/2 11:04
 */
@Slf4j
public class BuildConfigRunnable implements Runnable {
    /**
     * 同步计时器
     */
    private final CountDownLatch countDownLatch;

    private final EquipmentExtServiceImpl.BuildConfigTemp buildConfigTemp;

    /**
     * 解析 ${内容}
     */
    public static final String CONTENT_REG = "\\$\\{(.+?)\\}";
    /**
     * 解析 {内容}
     */
    public static final String URL_REG = "\\{(.+?)\\}";

    public BuildConfigRunnable(EquipmentExtServiceImpl.BuildConfigTemp buildConfigTemp, CountDownLatch countDownLatch) {
        this.buildConfigTemp = buildConfigTemp;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public void run() {
        try {
            // 1.创建文件夹和目录
            FolderTempObj folderTempObj = createFolderAndFile(buildConfigTemp);

            // 2.填充数据
            fillData(folderTempObj);
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * 创建文件夹和目录
     *
     * @param buildConfigTemp 临时对象
     * @return 填充文件临时对象
     */
    public FolderTempObj createFolderAndFile(EquipmentExtServiceImpl.BuildConfigTemp buildConfigTemp) {
        // 需要填充的文件对象(对象包含填充数据，结构信息，以及文件对象)
        FolderTempObj folderTempObj = new FolderTempObj(buildConfigTemp.getEquipmentSettingVo());

        DriveTemplate driveTemplate = buildConfigTemp.getDriveTemplate();
        // 1.递归创建文件夹和目录
        createFolderAndFile(driveTemplate.getDriveStructureTemplateList(), BuildFileConfig.getBuildFilePath() + driveTemplate.getTempPath(), folderTempObj);
        return folderTempObj;
    }

    /**
     * 递归创建文件和文件夹
     *
     * @param driveStructureTemplateList 当前驱动模板所的所有目录结构
     * @param parentPath                 生成后存放的路径
     * @param folderTempObj              当前生成配置时所需要的临时数据对象
     */
    public void createFolderAndFile(List<DriveStructureTemplate> driveStructureTemplateList, String parentPath, FolderTempObj folderTempObj) {
        for (DriveStructureTemplate driveStructureTemplate : driveStructureTemplateList) {
            // 填充占位符内容 例：a/{portNo} 替换成 a/com11
            String filePath = parseAndFill(URL_REG, parentPath + File.separator + driveStructureTemplate.getFilePath(), folderTempObj.getData());
            try {
                // 非文件夹走下面逻辑
                if (driveStructureTemplate.getIsDisk().compareTo(0) == 0) {
                    File file = FileUtil.touch(new File(filePath));
                    if (!file.isFile()) {
                        continue;
                    }

                    Long fileId = driveStructureTemplate.getFileId();
                    boolean isReadFile = false;

                    if (driveStructureTemplate.getIsUpload().compareTo(1) == 0) {
                        /**
                         * 需要上传并且该结构需要在创建模板时上传的文件
                         * 在该情况下的结构，一般都是固定文件（在生成配置时不需要手动上传，在创建模板时上传即可） 例如 ac.control
                         */
                        if (driveStructureTemplate.getUploadTiming().compareTo(0) == 0) {
                            fileId = driveStructureTemplate.getFileId();
                            isReadFile = true;
                        } else if (driveStructureTemplate.getUploadTiming().compareTo(1) == 0) {
                            /**
                             * 需要上传并且该结构需要在生成配置时上传的文件
                             * 生成配置时上传又分为是否需要填充内容两大类，是-ac.oid类文件，否-bacnet.ini类文件，因不需填充内容，所以直接将上传了的文件拷贝到该位置即可
                             */
                            fileId = folderTempObj.getEquipmentSettingVo().getFileId();
                            isReadFile = true;
                        }
                    }

                    if (isReadFile) {
                        /**
                         * 统一处理上传并且不需要填充内容的文件，直接拷贝到目录下即可
                         */
                        if (driveStructureTemplate.getIsFill().compareTo(0) == 0) {
                            byte[] bytes = buildConfigTemp.getByteMap().get(fileId);
                            if (bytes != null) {
                                FileUtil.writeBytes(bytes, file);
                            }
                        } else if (driveStructureTemplate.getIsFill().compareTo(1) == 0) {
                            /**
                             * 需要填充的文件则放入临时文件对象中，供后面填充对象时使用 => fillData() 方法
                             */
                            folderTempObj.getFillFileMap().add(new TempFillFileObj(driveStructureTemplate, file));
                        }
                    }
                }else {
                    FileUtil.mkdir(new File(filePath));
                }

                // 判断是否还有子结构
                List<DriveStructureTemplate> structureTemplateChildren = driveStructureTemplate.getChildren();
                if (CollUtil.isNotEmpty(structureTemplateChildren)) {
                    createFolderAndFile(structureTemplateChildren, filePath, folderTempObj);
                }
            } catch (Exception e) {
                log.error("创建[{}]异常", filePath, e);
            }
        }
    }

    /**
     * 填充数据
     *
     * @param folderTempObj
     */
    public void fillData(FolderTempObj folderTempObj) {
        for (TempFillFileObj tempFillFileObj : folderTempObj.getFillFileMap()) {
            DriveStructureTemplate driveStructureTemplate = tempFillFileObj.getDriveStructureTemplate();
            ByteArrayInputStream bais = null;
            // 创建模板时上传的文件
            if (driveStructureTemplate.getUploadTiming().compareTo(0) == 0) {
                if (driveStructureTemplate.getFileId() == null) {
                    continue;
                }
                try (FileWriter fw = new FileWriter(tempFillFileObj.getFile())) {
                    byte[] bytes = buildConfigTemp.getByteMap().get(driveStructureTemplate.getFileId());
                    if (bytes == null) {
                        log.info("{}=>文件id:{} 资源文件不存在", driveStructureTemplate.getFilePath(), driveStructureTemplate.getFileId());
                        return;
                    }

                    // 解析并替换内容
                    bais = new ByteArrayInputStream(bytes);
                    String content = com.siteweb.common.util.FileUtil.inputToString(bais, "GBk");
                    content = parseAndFill(CONTENT_REG, content, folderTempObj.getData());

                    fw.write(content);

                } catch (IOException e) {
                    log.error("{}文件填充失败", driveStructureTemplate.getFilePath(), e);
                } finally {
                    IoUtil.close(bais);
                }
            }
            // 生成配置时上传的文件
            else if (driveStructureTemplate.getUploadTiming().compareTo(1) == 0) {
                // 这里的 driveStructureTemplate 结构都是需要填充内容的结构
                byte[] bytes = buildConfigTemp.getByteMap().get(folderTempObj.getEquipmentSettingVo().getFileId());
                if (bytes == null) {
                    return;
                }
                OutputStreamWriter oStreamWriter = null;
                try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bytes), "GBK"))) {
                    // 读取格式和写入格式需要一致，否则可能会导致数据乱码
                    Wini wini = new Wini(bufferedReader);
                    // 数据填充先写死，后续优化
                    wini.put("BACNET_Cfg", "IPAddress", folderTempObj.getEquipmentSettingVo().getIpAddress());
                    wini.put("BACNET_Cfg", "Dnet", folderTempObj.getEquipmentSettingVo().getAddress());
                    oStreamWriter = new OutputStreamWriter(new BufferedOutputStream(new FileOutputStream(tempFillFileObj.getFile())), "GBK");
                    wini.store(oStreamWriter);
                } catch (Exception e) {
                    log.error("生成{}文件异常", driveStructureTemplate.getFilePath(), e);
                } finally {
                    IoUtil.close(oStreamWriter);
                }
            }
        }
    }


    /**
     * 检验是否需要填充
     *
     * @param reg     正则
     * @param content 待检查内容
     * @return string 需要填充则讲填充后的内容返回
     */
    private String parseAndFill(String reg, String content, JSONObject data) {
        if (CharSequenceUtil.isEmpty(content)) {
            return "";
        }
        List<String> placeholder = RegularUtils.getParams(reg, content);
        if (CollUtil.isNotEmpty(placeholder)) {
            Pattern p = Pattern.compile(reg);
            Matcher m = p.matcher(content);

            StringBuilder sb = new StringBuilder();
            while (m.find()) {
                String key = m.group(1);
                String value = data.getStr(key);
                m.appendReplacement(sb, CharSequenceUtil.isEmpty(value) ? "" : value);
            }
            m.appendTail(sb);
            return sb.toString();
        }
        return content;
    }


    /**
     * 当前结构对象(包含结构信息及file对象)
     */
    @Data
    @AllArgsConstructor
    static class TempFillFileObj {
        /**
         * 结构
         */
        private DriveStructureTemplate driveStructureTemplate;
        /**
         * 结构对应的文件对象
         */
        private File file;
    }

    /**
     * 当前这条记录的结构对象
     */
    @Data
    static class FolderTempObj {
        /**
         * 填充文件对象集合
         */
        List<TempFillFileObj> fillFileMap = new ArrayList<>();

        /**
         * 当前这条记录的原始数据
         */
        private EquipmentSettingVo equipmentSettingVo;
        /**
         * map格式的 equipmentSettingVo 数据
         */
        private JSONObject data;

        public FolderTempObj(EquipmentSettingVo equipmentSettingVo) {
            data = JSONUtil.parseObj(equipmentSettingVo);
            this.equipmentSettingVo = equipmentSettingVo;
        }
    }
}
