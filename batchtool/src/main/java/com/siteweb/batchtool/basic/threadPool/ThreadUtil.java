package com.siteweb.batchtool.basic.threadPool;

import cn.hutool.core.thread.NamedThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程帮助类
 *
 * <AUTHOR>
 * @date 2022/04/10
 */
@EnableAsync
@Component
public class ThreadUtil {
    public ThreadUtil() {
        this.cpuNum = Runtime.getRuntime().availableProcessors();
    }

    /**
     * cpu核数
     */
    private final int cpuNum;

    /**
     * 获取生成配置文件的线程池
     *
     * @return {@link ThreadPoolExecutor}
     */
    @Bean("settingThreadPool")
    public ThreadPoolExecutor getSettingThreadPool() {
        return new ThreadPoolExecutor(
                cpuNum * 2,
                cpuNum * 2,
                10,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(),
                new NamedThreadFactory("BatchTool-config-", false));
    }
}
