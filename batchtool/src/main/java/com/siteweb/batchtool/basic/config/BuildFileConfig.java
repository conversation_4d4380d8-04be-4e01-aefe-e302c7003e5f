package com.siteweb.batchtool.basic.config;

import java.io.File;

/**
 * 生成文件配置信息
 * @Author: lzy
 * @Date: 2022/4/12 11:10
 */
public class BuildFileConfig {
    /** 文件生成目录 例: upload-dir */
    public static String filePath;

    /** 文件生成目录 例: buildConfig */
    public static String buildFileRootPath = "buildConfig";

    public static String getBuildFilePath() {
        return filePath + File.separator + buildFileRootPath;
    }
}
