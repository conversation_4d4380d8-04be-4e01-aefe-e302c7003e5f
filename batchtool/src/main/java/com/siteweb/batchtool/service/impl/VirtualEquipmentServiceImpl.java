package com.siteweb.batchtool.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.batchtool.dto.MonitorSignalUnitSaveDto;
import com.siteweb.batchtool.dto.OriginSignalInfoDto;
import com.siteweb.batchtool.dto.SplitEquipmentInfoDTO;
import com.siteweb.batchtool.dto.VirtualEquipmentSaveDto;
import com.siteweb.batchtool.mapper.VirtualEquipmentMapper;
import com.siteweb.batchtool.service.VirtualEquipmentService;
import com.siteweb.batchtool.vo.*;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.MonitorUnitManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.DataItemService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VirtualEquipmentServiceImpl implements VirtualEquipmentService {
    /**
     * 包含8说明是虚拟设备
     */
    public static final String VIRTUAL_PROPERTY = "8";
    /**
     * 设备通讯状态信号ID与通道号
     */
    public static final int EQUIPMENT_STATE_ID = -3;
    /**
     * 虚拟信号ID与通道号
     */
    public static final int VIRTUAL_SIGNAL_ID = -2;
    /**
     * 设备通讯状态  特殊的信号与事件
     */
    public static final String EQUIPMENT_COMMUNICATION_STATUS = "设备通讯状态";
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private EquipmentTemplateService equipmentTemplateService;
    @Autowired
    private MonitorUnitManager monitorUnitManager;
    @Autowired
    private SamplerUnitService samplerUnitService;
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    private SignalService signalService;
    @Autowired
    private SignalMeaningsService signalMeaningsService;
    @Autowired
    private SignalPropertyService signalPropertyService;
    @Autowired
    private MonitorUnitSignalService monitorUnitSignalService;
    @Autowired
    private EventService eventService;
    @Autowired
    private EventConditionService eventConditionService;
    @Autowired
    private ConfigChangeMacroLogMapper configChangeMacroLogMapper;
    @Autowired
    private DataItemService dataItemService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private VirtualEquipmentMapper virtualEquipmentMapper;
    @Autowired
    private StationManager stationManager;
    @Autowired
    private ConfigChangeMacroLogService configChangeMacroLogService;
    @Autowired
    private HouseService houseService;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Override
    public ResourceStructureEquipmentMonitorUnitVo getResourceStructureEquipmentMonitorUnit() {
        List<ResourceStructure> resourceStructureList = resourceStructureService.findResourceStructures();
        List<Equipment> equipmentList = equipmentService.findEquipments();
        int rootId = 0;
        LinkedHashMap<Integer, ResourceStructureEquipmentMonitorUnitVo> resourceStructureDTOLinkedHashMap = new LinkedHashMap<>();
        for (ResourceStructure resourceStructure : resourceStructureList) {
            resourceStructureDTOLinkedHashMap.put(resourceStructure.getResourceStructureId(), new ResourceStructureEquipmentMonitorUnitVo(resourceStructure));
            if (resourceStructure.getParentResourceStructureId() == 0) {
                rootId = resourceStructure.getResourceStructureId();
            }
        }
        // 设备挂载
        if (CollUtil.isNotEmpty(equipmentList)) {
            for (Equipment equipment : equipmentList) {
                if (resourceStructureDTOLinkedHashMap.containsKey(equipment.getResourceStructureId()) && equipment.getStationId() > 0) {
                    ResourceStructureEquipmentMonitorUnitVo resourceStructureEquipmentMonitorUnitVo = resourceStructureDTOLinkedHashMap.get(equipment.getResourceStructureId());
                    MonitorUnitVo monitorUnitVo = new MonitorUnitVo(monitorUnitManager.getById(equipment.getMonitorUnitId()));
                    addMonitorUnitAndEquipment(equipment, resourceStructureEquipmentMonitorUnitVo, monitorUnitVo);
                }
            }
        }
        //层级挂载
        for (ResourceStructureEquipmentMonitorUnitVo resourceStructureEquipmentDTO : resourceStructureDTOLinkedHashMap.values()) {
            if (resourceStructureEquipmentDTO.getParentResourceStructureId() != 0 && resourceStructureDTOLinkedHashMap.containsKey(resourceStructureEquipmentDTO.getParentResourceStructureId())) {
                ResourceStructureEquipmentMonitorUnitVo pResourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(resourceStructureEquipmentDTO.getParentResourceStructureId());
                pResourceStructureEquipmentDTO.getChildren().add(resourceStructureEquipmentDTO);
            }
        }

        return resourceStructureDTOLinkedHashMap.get(rootId);
    }

    /**
     * 拆分或者组合设备的信号与事件
     * 生成虚拟设备模块 -> 添加虚拟设备 -> 添加虚拟信号 -> 添加跨站表达式 -> 添加虚拟事件
     * 代码中存在比较多的map主要为了映射新老字段之间的映射关系
     * @param splitEquipmentVo 拆分设备dto
     * @return {@link Boolean}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean splitEquipment(SplitEquipmentVo splitEquipmentVo) {
        //设置模板id
        this.setEquipmentTemplateId(splitEquipmentVo.getEquipmentSplitInfoVos());
        //生成虚拟模板
        Integer virtualTemplateId = this.buildEquipmentTemplate(splitEquipmentVo.getEquipmentId(),splitEquipmentVo.getVirtualEquipmentName());
        //生成虚拟设备
        VirtualEquipmentSaveDto virtualEquipmentSaveDto = BeanUtil.copyProperties(splitEquipmentVo, VirtualEquipmentSaveDto.class);
        virtualEquipmentSaveDto.setVirtualTemplateId(virtualTemplateId);
        Equipment virtualEquipment = this.buildEquipment(virtualEquipmentSaveDto);
        //生成虚拟信号 老设备id新信号id获取老设备id老信号id 为了获取跨站表达式
        Map<EquipmentSignalIdVo, EquipmentSignalIdVo> signalOriginEquipmentMap = new HashMap<>();
        //存储老信号与新信号的映射关系，主要为了生成事件时候的startExpression字段与Signal字段
        Map<EquipmentSignalIdVo,Integer> equipmentNewSignalMap = new HashMap<>();
        List<EquipmentSignalDto> signalList = this.buildSignal(splitEquipmentVo.getEquipmentSplitInfoVos(), virtualTemplateId, signalOriginEquipmentMap,equipmentNewSignalMap);
        //生成采集单元信号表达式
        this.buildMonitorSignalUnit(virtualEquipment, signalOriginEquipmentMap, signalList);
        //生成事件
        this.buildEvent(splitEquipmentVo.getEquipmentSplitInfoVos(),virtualTemplateId,equipmentNewSignalMap);
        //生成模板记录日志便于更新诸如设备\信号\事件等缓存
        this.buildConfigChangeLog(virtualTemplateId);
        //添加成功虚拟设备刷新本地缓存
        equipmentManager.refreshEquipmentByIds(List.of(virtualEquipment.getEquipmentId()));
        return Boolean.TRUE;
    }

    @Override
    public List<VirtualEquipmentBasicInfoVo> virtualEquipmentList() {
        List<Integer> equipmentIds = equipmentService.findEquipments()
                                                     .stream()
                                                     .filter(e -> CharSequenceUtil.contains(e.getProperty(), VIRTUAL_PROPERTY))
                                                     .map(Equipment::getEquipmentId)
                                                     .toList();
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return virtualEquipmentMapper.findVirtualEquipmentBasicInfo(equipmentIds);
    }

    @Override
    public VirtualEquipmentSettingVo exportVirtualEquipmentSetting(List<Integer> equipmentIds) {
        //查找虚拟设备信息
        List<VirtualEquipmentExcelVo> virtualEquipmentExcelList = this.getVirtualEquipmentInfo(equipmentIds);
        //查找虚拟信号信息
        List<VirtualSignalExcelVo> virtualSignalExcelList = this.getVirtualSignalInfo(equipmentIds);
        return new VirtualEquipmentSettingVo(virtualEquipmentExcelList,virtualSignalExcelList);
    }

    @Override
    public List<ImportErrorInfoDTO> importVirtualEquipmentSetting(VirtualEquipmentSettingVo virtualEquipmentSettingVo) {
        Map<String, String> equipmentErrorHashMap = this.buildEquipment(virtualEquipmentSettingVo);
        return this.buildMonitorSignalUnit(virtualEquipmentSettingVo, equipmentErrorHashMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVirtualEquipment(Integer id) {
        Equipment equipment = equipmentManager.getEquipmentById(id);
        //不是虚拟设备 无法删除
        if (!CharSequenceUtil.contains(equipment.getProperty(), VIRTUAL_PROPERTY)) {
            return;
        }
        equipmentService.deleteEquipment(id);
        //删除与模板挂钩的信息
        this.deleteVirtualTemplate(equipment.getEquipmentTemplateId());
        //删除表达式
        monitorUnitSignalService.deleteByEquipmentId(id);
    }

    @Override
    public List<VirtualEquipmentDetailVo> findVirtualEquipmentDetail(Integer equipmentId) {
        List<VirtualEquipmentDetailVo> list = new ArrayList<>();
        Equipment virtualEquipment = equipmentService.findById(equipmentId);
        VirtualEquipmentBasicInfoVo virtualEquipmentBasicInfo = this.findEquipmentBasicInfo(equipmentId);
        List<Integer> signalIds = monitorUnitSignalService.findSignalIdByEquipmentId(equipmentId);
        List<Signal> signalsByEquipmentId = signalService.findSignalsByTemplateIdAndSignalIds(virtualEquipment.getEquipmentTemplateId(), signalIds);
        for (Signal signal : signalsByEquipmentId) {
            VirtualEquipmentDetailVo virtualEquipmentDetailVo = BeanUtil.copyProperties(virtualEquipmentBasicInfo, VirtualEquipmentDetailVo.class);
            //获取源设备名源型号源通道号
            OriginSignalInfoDto originSignalInfoDto = this.findOriginSignalInfo(virtualEquipment.getStationId(), virtualEquipment.getEquipmentId(), signal.getSignalId());
            virtualEquipmentDetailVo.setVirtualSignalName(signal.getSignalName());
            virtualEquipmentDetailVo.setEquipmentName(originSignalInfoDto.getEquipmentName());
            virtualEquipmentDetailVo.setChannelNo(originSignalInfoDto.getChannelNo());
            virtualEquipmentDetailVo.setSignalName(originSignalInfoDto.getSignalName());
            list.add(virtualEquipmentDetailVo);
        }
        return list;
    }

    @Override
    public SplitEquipmentInfoDTO findSplitInfo(Integer equipmentId) {
        return virtualEquipmentMapper.findSplitInfo(equipmentId);
    }

    private void deleteVirtualTemplate(Integer equipmentTemplateId) {
        long equipmentTemplateCount = equipmentManager.getEquipmentTemplateCount(equipmentTemplateId);
        //该模板还有设备在使用无需删除相关配置
        if (equipmentTemplateCount > 0) {
            return;
        }
        //删除模板
        equipmentTemplateService.deleteById(equipmentTemplateId);
        //删除事件相关
        eventService.deleteByEquipmentTemplateId(equipmentTemplateId);
        eventConditionService.deleteByEquipmentTemplateId(equipmentTemplateId);
        //删除信号相关
        signalService.deleteByEquipmentTemplateId(equipmentTemplateId);
        signalMeaningsService.deleteByEquipmentTemplateId(equipmentTemplateId);
        signalPropertyService.deleteByEquipmentTemplateId(equipmentTemplateId);
    }

    public List<ImportErrorInfoDTO> buildMonitorSignalUnit(VirtualEquipmentSettingVo virtualEquipmentSettingVo, Map<String, String> equipmentErrorHashMap) {
        List<VirtualSignalExcelVo> virtualSignalExcelList = virtualEquipmentSettingVo.getVirtualSignalExcelList();
        List<ImportErrorInfoDTO> importErrorInfoDTOList = new ArrayList<>();
        Map<String,Equipment> equipmentMap = new HashMap<>();
        List<MonitorSignalUnitSaveDto> monitorSignalUnitSaveDtoList = new ArrayList<>(virtualSignalExcelList.size());
        for (int i = 0; i < virtualSignalExcelList.size(); i++) {
            if (equipmentErrorHashMap.containsKey(virtualSignalExcelList.get(i).getVirtualEquipmentName())) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "", equipmentErrorHashMap.get(virtualSignalExcelList.get(i).getVirtualEquipmentName())));
                continue;
            }
            Equipment equipment = this.findEquipmentByName(equipmentMap, virtualSignalExcelList.get(i).getEquipmentName());
            if (ObjectUtil.isNull(equipment)) {
                continue;
            }
            //判断虚拟设备名是否存在
            Equipment virtualEquipment = this.findEquipmentByName(equipmentMap, virtualSignalExcelList.get(i).getVirtualEquipmentName());
            if (ObjectUtil.isNull(virtualEquipment)) {
                continue;
            }
            //判断虚拟设备信号是否存在
            Signal virtualSignal = null;
            if (ObjectUtil.isNotNull(virtualEquipment)) {
                virtualSignal = signalService.findSignalIdByTemplateIdAndSignalName(virtualEquipment.getEquipmentTemplateId(), virtualSignalExcelList.get(i).getVirtualSignalName());
            }
            if (ObjectUtil.isNull(virtualSignal)) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "",messageSourceUtil.getMessage("virtualEquipment.notExist.virtualSignal")));
                continue;
            }
            //判断信号通道是否存在
            Signal signal = null;
            if (ObjectUtil.isNotNull(equipment)) {
                signal = signalService.findSignalIdByTemplateIdAndChannel(equipment.getEquipmentTemplateId(), virtualSignalExcelList.get(i).getChannelNo());
            }
            if (ObjectUtil.isNull(signal)) {
                importErrorInfoDTOList.add(ImportErrorInfoDTO.createImportErrorInfo(i, "","virtualEquipment.notExist.originChannel"));
                continue;
            }
            MonitorSignalUnitSaveDto monitorSignalUnitSaveDto = new MonitorSignalUnitSaveDto(virtualEquipment, signal.getSignalId(), virtualSignal.getSignalId(), equipment.getEquipmentId());
            monitorSignalUnitSaveDtoList.add(monitorSignalUnitSaveDto);
        }
        //完全没有错误才添加表达式
        if (CollUtil.isEmpty(importErrorInfoDTOList)) {
            for (MonitorSignalUnitSaveDto monitorSignalUnitSaveDto : monitorSignalUnitSaveDtoList) {
                this.buildMonitorSignalUnit(monitorSignalUnitSaveDto.getVirtualEquipment(), monitorSignalUnitSaveDto.getSignalId(), monitorSignalUnitSaveDto.getVirtualSignalId(), monitorSignalUnitSaveDto.getEquipmentId());
            }
        }
        return importErrorInfoDTOList;
    }

    /** 设备是否存在
     * @param equipmentMap 设备名与设备的映射map
     * @param equipmentName 设备名
     * @return {@link Boolean}
     */
    private Equipment findEquipmentByName(Map<String, Equipment> equipmentMap, String equipmentName) {
        if (equipmentMap.containsKey(equipmentName)) {
            return equipmentMap.get(equipmentName);
        }
        Equipment equipmentsByEquipmentName = equipmentManager.getEquipmentsByEquipmentName(equipmentName);
        if (ObjectUtil.isNotNull(equipmentsByEquipmentName)) {
            equipmentMap.put(equipmentName, equipmentsByEquipmentName);
            return equipmentsByEquipmentName;
        }
        return null;
    }

    public EquipmentTemplate findEquipmentTemplateByName(Map<String, EquipmentTemplate> equipmentTemplateMap, String equipmentTemplateName){
        if (equipmentTemplateMap.containsKey(equipmentTemplateName)) {
            return equipmentTemplateMap.get(equipmentTemplateName);
        }
        EquipmentTemplate equipmentTemplate = equipmentTemplateService.findEquipmentTemplateByName(equipmentTemplateName);
        if (ObjectUtil.isNotNull(equipmentTemplate)) {
            equipmentTemplateMap.put(equipmentTemplateName, equipmentTemplate);
            return equipmentTemplate;
        }
        return null;
    }

    /**
     * 导入的虚拟设备校验与添加
     * @param virtualEquipmentSettingVo 导入的虚拟设备excel列表
     */
    private Map<String, String> buildEquipment(VirtualEquipmentSettingVo virtualEquipmentSettingVo) {
        Map<String, String> errorMap = new HashMap<>(virtualEquipmentSettingVo.getVirtualEquipmentExcelList().size());
        Map<String, Integer> categoryMap = getEquipmentCategoryMap().entrySet()
                                                                .stream()
                                                                    .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (k1, k2) -> k1));
        Map<String, String> virtualRealEquipmentMap = virtualEquipmentSettingVo.getVirtualSignalExcelList()
                                                                               .stream()
                                                                               .filter(e -> ObjectUtil.isNotNull(e.getVirtualEquipmentName()) &&
                                                                                       ObjectUtil.isNotNull(e.getEquipmentName()))
                                                                               .collect(Collectors.toMap(
                                                                                       VirtualSignalExcelVo::getVirtualEquipmentName,
                                                                                       VirtualSignalExcelVo::getEquipmentName,
                                                                                       (k1, k2) -> k1));
        for (VirtualEquipmentExcelVo virtualEquipmentExcelVo : virtualEquipmentSettingVo.getVirtualEquipmentExcelList()) {
            //虚拟设备局站不存在
            Station station = stationManager.findStationByName(virtualEquipmentExcelVo.getVirtualStationName());
            if (ObjectUtil.isNull(station)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.virtualEquipmentStation"));
                continue;
            }
            //虚拟监控单元不存在
            MonitorUnit monitorUnit = monitorUnitManager.findMonitorUnit(station.getStationName(), virtualEquipmentExcelVo.getVirtualMonitorUnitName());
            if (ObjectUtil.isNull(monitorUnit)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.virtualMonitUnit"));
                continue;
            }
            //采集单元信息不存在
            SamplerUnit samplerUnit = samplerUnitService.findSamplerUnit(monitorUnit.getMonitorUnitId(), virtualEquipmentExcelVo.getVirtualSamplerUnitName(), virtualEquipmentExcelVo.getVirtualPortName());
            if (ObjectUtil.isNull(samplerUnit)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.sampleUnit"));
                continue;
            }
            //判断局房是否存在
            House house = houseService.findHouse(station.getStationId(), virtualEquipmentExcelVo.getVirtualHouseName());
            if (ObjectUtil.isNull(house)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.virtualEquipmentHouse"));
                continue;
            }
            //虚拟模板是否正确
            EquipmentTemplate virtualEquipmentTemplate = equipmentTemplateService.findEquipmentTemplateByName(virtualEquipmentExcelVo.getVirtualEquipmentTemplateName());
            if (ObjectUtil.isNull(virtualEquipmentTemplate)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.virtualTemplate"));
                continue;
            }
            //虚拟设备类型不存在
            if (!categoryMap.containsKey(virtualEquipmentExcelVo.getVirtualEquipmentCategoryName())) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.virtualEquipmentCategory"));
                continue;
            }
            //真实设备不存在
            String realEquipmentName = virtualRealEquipmentMap.get(virtualEquipmentExcelVo.getVirtualEquipmentName());
            Equipment realEquipment = equipmentManager.findEquipmentByStationNameAndEquipmentName(station.getStationName(), realEquipmentName);
            if (ObjectUtil.isNull(realEquipment)) {
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.notExist.originEquipment"));
                continue;
            }
            //虚拟设备已存在
            Equipment virtualEquipment = equipmentManager.findEquipmentByStationNameAndEquipmentName(station.getStationName(), virtualEquipmentExcelVo.getVirtualEquipmentName());
            if (ObjectUtil.isNotNull(realEquipment) && ObjectUtil.isNotNull(virtualEquipment) && ObjectUtil.notEqual(realEquipment.getMonitorUnitId(),virtualEquipment.getMonitorUnitId())){
                errorMap.put(virtualEquipmentExcelVo.getVirtualEquipmentName(), messageSourceUtil.getMessage("virtualEquipment.cannotAcross.monitUnit"));
            }
            if (ObjectUtil.isNotNull(virtualEquipment)) {
                continue;
            }
            Integer resourceStructureId = realEquipment.getResourceStructureId();
            VirtualEquipmentSaveDto virtualEquipmentSaveDto = new VirtualEquipmentSaveDto(
                    realEquipment.getEquipmentId(), virtualEquipmentExcelVo.getVirtualEquipmentName(),
                    virtualEquipmentTemplate.getEquipmentTemplateId(), samplerUnit.getSamplerUnitId(),
                    categoryMap.get(virtualEquipmentExcelVo.getVirtualEquipmentCategoryName()),
                    resourceStructureId,house.getHouseId());
            Equipment equipment = this.buildEquipment(virtualEquipmentSaveDto);
            equipmentManager.refreshEquipmentByIds(List.of(equipment.getEquipmentId()));
        }
        return errorMap;
    }

    /**
     * 获取虚拟信号excel中Sheet的信息
     * @param equipmentIds 设备ids
     * @return {@link VirtualSignalExcelVo}
     */
    private List<VirtualSignalExcelVo> getVirtualSignalInfo(List<Integer> equipmentIds) {
        List<VirtualSignalExcelVo> virtualSignalExcelList = new ArrayList<>();
        for (Integer equipmentId : equipmentIds) {
            Equipment virtualEquipment = equipmentService.findById(equipmentId);
            List<Signal> signalList = signalService.findSignalsByEquipmentId(equipmentId);
            for (Signal signal : signalList) {
                VirtualSignalExcelVo virtualSignalExcelVo = new VirtualSignalExcelVo();
                virtualSignalExcelVo.setVirtualEquipmentName(virtualEquipment.getEquipmentName());
                virtualSignalExcelVo.setVirtualSignalName(signal.getSignalName());
                //获取源设备名源型号源通道号
                OriginSignalInfoDto originSignalInfoDto = this.findOriginSignalInfo(virtualEquipment.getStationId(), virtualEquipment.getEquipmentId(), signal.getSignalId());
                virtualSignalExcelVo.setEquipmentName(originSignalInfoDto.getEquipmentName());
                virtualSignalExcelVo.setChannelNo(originSignalInfoDto.getChannelNo());
                virtualSignalExcelList.add(virtualSignalExcelVo);
            }
        }
        return virtualSignalExcelList;
    }

    private OriginSignalInfoDto findOriginSignalInfo(Integer stationId, Integer equipmentId, Integer signalId) {
        MonitorUnitSignal monitorUnitSignal = monitorUnitSignalService.findMonitorUnitSignal(stationId, equipmentId, signalId);
        if (ObjectUtil.isNull(monitorUnitSignal)){
            return new OriginSignalInfoDto();
        }
        String expression = monitorUnitSignal.getExpression();
        //0源设备id 1源信号id
        String[] originString = expression.replace("[", "")
                                   .replace("]", "")
                                   .split(",");
        OriginSignalInfoDto originSignalInfoDto = new OriginSignalInfoDto();
        Equipment equipment = equipmentService.findByIdFromDB(Integer.parseInt(originString[0]));
        originSignalInfoDto.setEquipmentName(equipment.getEquipmentName());
        ConfigSignalDTO configSignal = signalService.findConfigSignalDTOBySignalId(Integer.parseInt(originString[0]), Integer.parseInt(originString[1]));
        originSignalInfoDto.setChannelNo(configSignal.getChannelNo());
        originSignalInfoDto.setSignalName(configSignal.getSignalName());
        return originSignalInfoDto;
    }

    /**
     * 获取虚拟设备excel中Sheet的信息
     * @param equipmentIds 设备ids
     * @return {@link VirtualEquipmentExcelVo}
     */
    private List<VirtualEquipmentExcelVo> getVirtualEquipmentInfo(List<Integer> equipmentIds) {
        Map<Integer, String> dataItemMap = dataItemService.findByEntryId(7)
                                                      .stream()
                                                      .collect(Collectors.toMap(DataItem::getItemId,
                                                              DataItem::getItemValue, (k1, k2) -> k1));
        List<VirtualEquipmentExcelVo> list = new ArrayList<>(equipmentIds.size());
        for (Integer equipmentId : equipmentIds) {
            Equipment equipment = equipmentService.findById(equipmentId);
            VirtualEquipmentBasicInfoVo equipmentBasicInfo = this.findEquipmentBasicInfo(equipmentId);
            VirtualEquipmentExcelVo virtualEquipmentExcel = BeanUtil.copyProperties(equipmentBasicInfo, VirtualEquipmentExcelVo.class);
            virtualEquipmentExcel.setVirtualEquipmentCategoryName(dataItemMap.get(equipment.getEquipmentCategory()));
            list.add(virtualEquipmentExcel);
        }
        return list;
    }

    private void buildConfigChangeLog(Integer virtualTemplateId) {
        configChangeMacroLogService.saveConfigChangeLog(virtualTemplateId.toString(),6,2);
    }

    private void buildEvent(List<EquipmentSplitInfoVo> equipmentSplitInfoVos, Integer virtualTemplateId,Map<EquipmentSignalIdVo,Integer> oldNewSignalIdMap) {
        if (CollUtil.isEmpty(equipmentSplitInfoVos)) {
            return;
        }
        HashMap<Integer,List<Integer>> equipmentSignalMap = new HashMap<>(equipmentSplitInfoVos.size());
        for (EquipmentSplitInfoVo equipmentSplitInfoVo : equipmentSplitInfoVos) {
            equipmentSignalMap.computeIfAbsent(equipmentSplitInfoVo.getEquipmentId(), key -> new ArrayList<>())
                             .add(equipmentSplitInfoVo.getEventId());
        }
        List<EquipmentEventDto> equipmentEventDtos = new ArrayList<>(equipmentSplitInfoVos.size());
        equipmentSignalMap.forEach((equipmentId, eventIds) -> {
            equipmentEventDtos.addAll(eventService.findEventsByEquipmentIdAndEventIds(equipmentId, eventIds));
        });
        //无事件存在
        if (CollUtil.isEmpty(equipmentEventDtos)) {
            return;
        }
        //获取最大的事件id
        Integer eventMaxId = equipmentEventDtos.stream()
                                               .map(EquipmentEventDto::getEventId)
                                               .max(Integer::compareTo)
                                               .get();
        Map<EquipmentEventIdVo, Integer> equipmentNewEventMap = new HashMap<>();
        Integer displayIndex = 1;
        //设置虚拟模板id
        for (EquipmentEventDto event : equipmentEventDtos) {
            event.setDisplayIndex(displayIndex++);
            Integer newEventId = ++eventMaxId;
            Integer oldEventId = event.getEventId();
            Integer oldSignalId = event.getSignalId();
            event.setEquipmentTemplateId(virtualTemplateId);
            event.setEventId(newEventId);
            if (EQUIPMENT_COMMUNICATION_STATUS.equals(event.getEventName())){
                event.setSignalId(EQUIPMENT_STATE_ID);
                event.setEventId(EQUIPMENT_STATE_ID);
                newEventId = EQUIPMENT_STATE_ID;
            }
            equipmentNewEventMap.put(new EquipmentEventIdVo(event.getEquipmentId(), oldEventId), newEventId);
            //老信号获取新信号
            Integer newSignalId = oldNewSignalIdMap.get(new EquipmentSignalIdVo(event.getEquipmentId(),oldSignalId));
            event.setSignalId(newSignalId);
            event.setStartExpression(event.getStartExpression().replace(oldSignalId.toString(), newSignalId.toString()));
        }
        List<Event> eventList = BeanUtil.copyToList(equipmentEventDtos, Event.class);
        eventService.batchInsert(eventList);
        //事件条件批量获取和插入
        this.insertEventCondition(equipmentSignalMap, equipmentNewEventMap, virtualTemplateId);
    }

    private void insertEventCondition(HashMap<Integer, List<Integer>> equipmentSignalMap, Map<EquipmentEventIdVo, Integer> equipmentNewEventMap, Integer virtualTemplateId) {
        List<EquipmentEventConditionDTO> equipmentEventConditionDTOS = new ArrayList<>();
        equipmentSignalMap.forEach((equipmentId,eventIds)->{
            equipmentEventConditionDTOS.addAll(eventConditionService.findEventConditionByEquipmentIdAndEventIds(equipmentId, eventIds));
        });
        for (EquipmentEventConditionDTO eventCondition : equipmentEventConditionDTOS) {
            eventCondition.setEquipmentTemplateId(virtualTemplateId);
            eventCondition.setEventId(equipmentNewEventMap.get(new EquipmentEventIdVo(eventCondition.getEquipmentId(),eventCondition.getEventId())));
        }
        List<EventCondition> eventConditionList = BeanUtil.copyToList(equipmentEventConditionDTOS, EventCondition.class);
        eventConditionService.batchInsert(eventConditionList);
    }

    /**
     * @param virtualEquipment 虚拟设备
     * @param signalEquipmentMap 虚拟信号
     * @param signalList 真实设备id
     */
    private void buildMonitorSignalUnit(Equipment virtualEquipment, Map<EquipmentSignalIdVo, EquipmentSignalIdVo> signalEquipmentMap, List<EquipmentSignalDto> signalList) {
        if (CollUtil.isEmpty(signalList) || CollUtil.isEmpty(signalEquipmentMap)) {
            return;
        }
        for (EquipmentSignalDto signal : signalList) {
            EquipmentSignalIdVo equipmentSignalIdVo = signalEquipmentMap.get(new EquipmentSignalIdVo(signal.getEquipmentId(),signal.getSignalId()));
            MonitorUnitSignal monitorUnitSignal = new MonitorUnitSignal();
            Equipment originEquipment = equipmentService.findById(equipmentSignalIdVo.getEquipmentId());
            monitorUnitSignal.setStationId(virtualEquipment.getStationId());
            monitorUnitSignal.setMonitorUnitId(virtualEquipment.getMonitorUnitId());
            monitorUnitSignal.setEquipmentId(virtualEquipment.getEquipmentId());
            monitorUnitSignal.setSignalId(signal.getSignalId());
            monitorUnitSignal.setReferenceSamplerUnitId(originEquipment.getSamplerUnitId());
            monitorUnitSignal.setReferenceChannelNo(VIRTUAL_SIGNAL_ID);
            //[原来的设备id,原来的信号id]
            String expression = CharSequenceUtil.concat(true, "[", equipmentSignalIdVo.getEquipmentId().toString(), ",", equipmentSignalIdVo.getSignalId().toString(), "]");
            monitorUnitSignal.setExpression(expression);
            monitorUnitSignal.setInstanceType(2);
            //不存在则添加
            if (monitorUnitSignalService.findMonitorUnitSignal(monitorUnitSignal.getStationId(), monitorUnitSignal.getEquipmentId(), monitorUnitSignal.getSignalId()) == null) {
                monitorUnitSignalService.save(monitorUnitSignal);
            }
        }
    }

    private void buildMonitorSignalUnit(Equipment virtualEquipment, Integer realSignalId, Integer virtualSignalId, Integer equipmentId) {
        MonitorUnitSignal monitorUnitSignal = new MonitorUnitSignal();
        Equipment equipment = equipmentService.findById(equipmentId);
        monitorUnitSignal.setStationId(virtualEquipment.getStationId());
        monitorUnitSignal.setMonitorUnitId(virtualEquipment.getMonitorUnitId());
        monitorUnitSignal.setEquipmentId(virtualEquipment.getEquipmentId());
        monitorUnitSignal.setSignalId(virtualSignalId);
        monitorUnitSignal.setReferenceSamplerUnitId(equipment.getSamplerUnitId());
        monitorUnitSignal.setReferenceChannelNo(VIRTUAL_SIGNAL_ID);
        String expression = CharSequenceUtil.concat(true, "[", equipmentId.toString(), ",", realSignalId.toString(), "]");
        monitorUnitSignal.setExpression(expression);
        monitorUnitSignal.setInstanceType(2);
        //不存在则添加
        if (monitorUnitSignalService.findMonitorUnitSignal(monitorUnitSignal.getStationId(), monitorUnitSignal.getEquipmentId(), monitorUnitSignal.getSignalId()) == null) {
            monitorUnitSignalService.save(monitorUnitSignal);
        }
    }

    /**
     * 生成信号
     *
     * @param equipmentSplitInfoVos  拆分设备的基本信息
     * @param virtualTemplateId 虚拟模板
     * @return {@link List}<{@link EquipmentSignalDto}>
     */
    private List<EquipmentSignalDto> buildSignal(List<EquipmentSplitInfoVo> equipmentSplitInfoVos, Integer virtualTemplateId,Map<EquipmentSignalIdVo, EquipmentSignalIdVo> signalOriginEquipmentMap,Map<EquipmentSignalIdVo,Integer> equipmentNewSignalMap) {
        if (CollUtil.isEmpty(equipmentSplitInfoVos)) {
            return new ArrayList<>();
        }
        HashMap<Integer,List<Integer>> equipmentSignalMap = new HashMap<>(equipmentSplitInfoVos.size());
        for (EquipmentSplitInfoVo equipmentSplitInfoVo : equipmentSplitInfoVos) {
            equipmentSignalMap.computeIfAbsent(equipmentSplitInfoVo.getEquipmentId(), key -> new ArrayList<>())
                              .add(equipmentSplitInfoVo.getSignalId());
            EquipmentSignalIdVo equipmentSignalIdVo = new EquipmentSignalIdVo(equipmentSplitInfoVo.getEquipmentId(), equipmentSplitInfoVo.getSignalId());
            signalOriginEquipmentMap.put(equipmentSignalIdVo, equipmentSignalIdVo);
        }
        List<EquipmentSignalDto> equipmentSignalList = new ArrayList<>(equipmentSplitInfoVos.size());
//        this.enableStateSignalHandle(equipmentSplitInfoVos.get(0).getEquipmentId(), signalOriginEquipmentMap, equipmentSignalList);
        for (Map.Entry<Integer, List<Integer>> integerListEntry : equipmentSignalMap.entrySet()) {
            equipmentSignalList.addAll(signalService.findSignalsByEquipmentIdAndSignalIds(integerListEntry.getKey(), integerListEntry.getValue()));
        }
        //获取最大的信号id
        Integer signalMaxId = equipmentSignalList.stream()
                                    .map(EquipmentSignalDto::getSignalId)
                                    .max(Integer::compareTo)
                                    .get();
        Integer displayIndex = 1;
        //批量插入信号配置  设置通道号 与 信号id 设备模板id
        for (EquipmentSignalDto signal : equipmentSignalList) {
            signal.setDisplayIndex(displayIndex++);
            Integer oldSignalId = signal.getSignalId();
            Integer newSignalId = ++signalMaxId;
            signal.setEquipmentTemplateId(virtualTemplateId);
            signal.setSignalId(newSignalId);
            signal.setChannelNo(VIRTUAL_SIGNAL_ID);
            if (EQUIPMENT_COMMUNICATION_STATUS.equals(signal.getSignalName())){
                signal.setChannelNo(EQUIPMENT_STATE_ID);
                signal.setSignalId(EQUIPMENT_STATE_ID);
                newSignalId = EQUIPMENT_STATE_ID;
            }
            equipmentNewSignalMap.put(new EquipmentSignalIdVo(signal.getEquipmentId(),oldSignalId), newSignalId);
            //新的设备模板获取老的源设备id
            EquipmentSignalIdVo equipmentSignalIdVo = signalOriginEquipmentMap.get(new EquipmentSignalIdVo(signal.getEquipmentId(),oldSignalId));
            signalOriginEquipmentMap.put(new EquipmentSignalIdVo(signal.getEquipmentId(), newSignalId), equipmentSignalIdVo);
        }
        List<Signal> signalList = BeanUtil.copyToList(equipmentSignalList, Signal.class);
        signalService.batchInsert(signalList);
        //信号含义批量获取和插入
        insertSignalMeaning(equipmentSignalMap, virtualTemplateId,equipmentNewSignalMap);
        //信号属性批量获取和插入
        insertSignalProperty(equipmentSignalMap, virtualTemplateId,equipmentNewSignalMap);
        return equipmentSignalList;
    }

    /**
     * 开启或关闭全局signalId =-3 设备状态的额外处理
     * @param equipmentId
     * @param signalOriginEquipmentMap
     * @param equipmentSignalList
     */
    private void enableStateSignalHandle(Integer equipmentId, Map<EquipmentSignalIdVo, EquipmentSignalIdVo> signalOriginEquipmentMap, List<EquipmentSignalDto> equipmentSignalList) {
        //是否开启屏蔽配置设备通讯状态
        Boolean stateSignal = this.enableStateSignal();
        if (Boolean.FALSE.equals(stateSignal)) {
            //用于创建虚拟设备获取源设备与源信号id
            EquipmentSignalIdVo equipmentSignalIdVo1 = new EquipmentSignalIdVo(equipmentId, EQUIPMENT_STATE_ID);
            //由于设备通讯状态的信号id固定为-3 所以放置相同的vo
            signalOriginEquipmentMap.put(equipmentSignalIdVo1, equipmentSignalIdVo1);
            equipmentSignalList.addAll(signalService.findSignalsByEquipmentIdAndSignalIds(equipmentId, List.of(EQUIPMENT_STATE_ID)));
        }
    }

    /**
     * 是否展示BA拆分工具是否展示设备通讯状态信号
     * @return {@link Boolean} true是 false否
     */
    private Boolean enableStateSignal(){
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("batchtool.statesignal.enable");
        if (ObjectUtil.isNull(systemConfig)) {
            return Boolean.FALSE;
        }
        return Boolean.parseBoolean(systemConfig.getSystemConfigValue());
    }

    private void insertSignalMeaning(Map<Integer,List<Integer>> equipmentSignalMap, Integer virtualTemplateId,Map<EquipmentSignalIdVo, Integer> equipmentNewSignalMap) {
        List<EquipmentSignalMeaningsDTO> equipmentSignalMeanings = new ArrayList<>();
        equipmentSignalMap.forEach((equipmentId, signalIds) -> {
            equipmentSignalMeanings.addAll(signalMeaningsService.findSignalsByEquipmentAndSignalIds(equipmentId, signalIds));
        });
        for (EquipmentSignalMeaningsDTO signalMeaning : equipmentSignalMeanings) {
            signalMeaning.setSignalId(equipmentNewSignalMap.get(new EquipmentSignalIdVo(signalMeaning.getEquipmentId(), signalMeaning.getSignalId())));
            signalMeaning.setEquipmentTemplateId(virtualTemplateId);
        }
        List<SignalMeanings> signalMeanings = BeanUtil.copyToList(equipmentSignalMeanings, SignalMeanings.class);
        signalMeaningsService.batchInsert(signalMeanings);
    }

    private void insertSignalProperty(Map<Integer,List<Integer>> equipmentSignalMap, Integer virtualTemplateId,Map<EquipmentSignalIdVo, Integer> equipmentNewSignalMap) {
        List<EquipmentSignalPropertyDTO> equipmentSignalPropertyDTOS = new ArrayList<>();
        equipmentSignalMap.forEach((equipmentId, signalIds) -> {
            equipmentSignalPropertyDTOS.addAll(signalPropertyService.findSignalPropertiesByEquipmentIdAndSignalIds(equipmentId, signalIds));
        });
        for (EquipmentSignalPropertyDTO signalProperty : equipmentSignalPropertyDTOS) {
            signalProperty.setSignalId(equipmentNewSignalMap.get(new EquipmentSignalIdVo(signalProperty.getEquipmentId(), signalProperty.getSignalId())));
            signalProperty.setEquipmentTemplateId(virtualTemplateId);
        }
        List<SignalProperty> signalMeanings = BeanUtil.copyToList(equipmentSignalPropertyDTOS, SignalProperty.class);
        signalPropertyService.batchInsert(signalMeanings);
    }

    /**
     * 生成虚拟设备
     *
     * @param virtualEquipmentSaveDto 虚拟设备添加dto
     * @return {@link Equipment}
     */
    private Equipment buildEquipment(VirtualEquipmentSaveDto virtualEquipmentSaveDto) {
        Equipment equipment = equipmentService.findById(virtualEquipmentSaveDto.getEquipmentId());
        if (ObjectUtil.isNull(equipment)) {
            return null;
        }
        Equipment virtualEquipment = BeanUtil.copyProperties(equipment, Equipment.class);
        //设备主键
        int subEquipmentId = primaryKeyValueService.getGlobalIdentity("TBL_Equipment", 0);
        virtualEquipment.setEquipmentId(subEquipmentId);
        //模板id、采集单元id、设备类型、名称
        virtualEquipment.setEquipmentName(virtualEquipmentSaveDto.getVirtualEquipmentName());
        virtualEquipment.setSamplerUnitId(virtualEquipmentSaveDto.getSamplerUnitId());
        virtualEquipment.setEquipmentTemplateId(virtualEquipmentSaveDto.getVirtualTemplateId());
        virtualEquipment.setEquipmentCategory(virtualEquipmentSaveDto.getEquipmentCategory());
        String property = CharSequenceUtil.isBlank(equipment.getProperty()) ? "8" : equipment.getProperty() + "/8";
        virtualEquipment.setProperty(property);
        if (ObjectUtil.isNotNull(virtualEquipmentSaveDto.getResourceStructureId())) {
            virtualEquipment.setResourceStructureId(virtualEquipmentSaveDto.getResourceStructureId());
        }
        if (ObjectUtil.isNotNull(virtualEquipmentSaveDto.getHouseId())) {
            virtualEquipment.setHouseId(virtualEquipmentSaveDto.getHouseId());
        }
        virtualEquipment.setUpdateTime(new Date());
        equipmentService.saveEquipment(virtualEquipment);
        return virtualEquipment;
    }

    /**
     * 生成虚拟模板
     * @param equipmentId 模板id
     */
    private Integer buildEquipmentTemplate(Integer equipmentId,String virtualEquipmentName) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        EquipmentTemplate template = equipmentTemplateService.findById(equipment.getEquipmentTemplateId());
        EquipmentTemplate subTemplate = BeanUtil.copyProperties(template, EquipmentTemplate.class);
        subTemplate.setParentTemplateId(template.getEquipmentTemplateId());
        //模板主键
        int templateId = primaryKeyValueService.getGlobalIdentity("TBL_EquipmentTemplate", 0);
        subTemplate.setEquipmentTemplateId(templateId);
        //备注
        subTemplate.setMemo("虚拟模板");
        //虚拟模板名称 = 虚拟设备名称 + 时间戳
        subTemplate.setEquipmentTemplateName(virtualEquipmentName + System.currentTimeMillis());
        equipmentTemplateService.createEquipmentTemplate(subTemplate);
        return templateId;
    }

    private VirtualEquipmentBasicInfoVo findEquipmentBasicInfo(Integer equipmentId){
        return virtualEquipmentMapper.findVirtualEquipmentBasicInfo(List.of(equipmentId)).get(0);
    }

    /**
     * 向层级结构中的监控单元添加设备
     * @param equipment 设备
     * @param resourceStructureEquipmentMonitorUnitVo 层级
     * @param monitorUnitVo 监控单元
     */
    private void addMonitorUnitAndEquipment(Equipment equipment, ResourceStructureEquipmentMonitorUnitVo resourceStructureEquipmentMonitorUnitVo, MonitorUnitVo monitorUnitVo) {
        Optional<MonitorUnitVo> first = resourceStructureEquipmentMonitorUnitVo.getMonitorUnitChildren()
                                                                               .stream()
                                                                               .filter(e -> e.equals(monitorUnitVo))
                                                                               .findFirst();
        monitorUnitVo.getEquipmentChildren()
                     .add(new EquipmentVo(equipment));
        //该层级已存在该监控单元
        if (first.isPresent()) {
            first.get()
                 .getEquipmentChildren()
                 .addAll(monitorUnitVo.getEquipmentChildren());
            return;
        }
        //该层级不存在该监控单元
        resourceStructureEquipmentMonitorUnitVo.getMonitorUnitChildren()
                                               .add(monitorUnitVo);

    }

    /**
     * 设置模板id
     * @param equipmentSplitInfoVos 给每一个拆分设备设置模板id
     * @return {@link List}<{@link Integer}>
     */
    private void setEquipmentTemplateId(List<EquipmentSplitInfoVo> equipmentSplitInfoVos){
        for (EquipmentSplitInfoVo equipmentSplitInfoVo : equipmentSplitInfoVos) {
            //通过设备id设置其模板id
            Equipment equipment = equipmentManager.getEquipmentById(equipmentSplitInfoVo.getEquipmentId());
            equipmentSplitInfoVo.setEquipmentTemplateId(equipment.getEquipmentTemplateId());
        }
    }

    /**
     * 获取设备类型map
     * @return {@link Map}<{@link Integer}, {@link String}>
     */
    private Map<Integer, String> getEquipmentCategoryMap() {
        return dataItemService.findByEntryId(7)
                              .stream()
                              .collect(Collectors.toMap(DataItem::getItemId,
                                      DataItem::getItemValue, (k1, k2) -> k1));
    }
}
