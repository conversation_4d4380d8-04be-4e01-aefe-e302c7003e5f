package com.siteweb.batchtool.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.batchtool.entity.DriveStructureTemplate;
import com.siteweb.batchtool.mapper.DriveStructureTemplateMapper;
import com.siteweb.batchtool.service.DriveStructureTemplateService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.utility.entity.DiskFile;
import com.siteweb.utility.service.DiskFileService;
import com.siteweb.utility.service.StorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2022/6/27 10:10
 */
@Service
public class DriveStructureTemplateServiceImpl implements DriveStructureTemplateService {
    @Autowired
    private DriveStructureTemplateMapper driveStructureTemplateMapper;
    @Autowired
    private DiskFileService diskFileService;
    @Autowired
    private StorageService storageService;
    private static final Integer ROOT_ID = 0;
    private static final Logger log = LoggerFactory.getLogger(DriveStructureTemplateServiceImpl.class);

    @Override
    public List<DriveStructureTemplate> findAllDriveStructureTemplate() {
        return buildTree(driveStructureTemplateMapper.selectList(new QueryWrapper<>()));
    }

    @Override
    public List<DriveStructureTemplate> findByDriveTemplateIds(Collection<Integer> ids) {
        return findByDriveTemplateIds(ids, true);
    }

    @Override
    public List<DriveStructureTemplate> findByDriveTemplateIds(Collection<Integer> ids, boolean buildTree) {
        LambdaQueryWrapper<DriveStructureTemplate> wrapper = Wrappers.lambdaQuery(DriveStructureTemplate.class)
                .in(DriveStructureTemplate::getDriveTemplateId, ids);
        List<DriveStructureTemplate> data = driveStructureTemplateMapper.findByDriveTemplateIds(wrapper);
        return buildTree ? buildTree(data) : data;
    }

    @Override
    public DriveStructureTemplate findById(Integer driveStructureTemplateId) {
        return driveStructureTemplateMapper.selectById(driveStructureTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DriveStructureTemplate create(DriveStructureTemplate driveStructureTemplate) {
        // 当前驱动模板下所有结构
        List<DriveStructureTemplate> allDriveStructureTemplate = findByDriveTemplateIds(Collections.singleton(driveStructureTemplate.getDriveTemplateId()), false);

        // 获取上级结构信息
        DriveStructureTemplate parentNode = findNode(allDriveStructureTemplate, driveStructureTemplate.getPid());

        // 1.新增结构为文件类型时，上级结构不能为文件类型
        if (parentNode != null && parentNode.getIsDisk().compareTo(0) == 0) {
            // 上级结构为文件
            log.error("结构[/{}]的上级结构不能为文件类型", driveStructureTemplate.getFilePath());
            throw new BusinessException("上级结构不能为文件类型");
        }

        // 新节点默认叶子节点
        driveStructureTemplate.setIsLeaf(1);
        int insert = driveStructureTemplateMapper.insert(driveStructureTemplate);

        // 2.同一层级不能有相同文件类型的结构路径
        if (parentNode != null) {
            if (CollUtil.isNotEmpty(parentNode.getChildren())) {
                List<DriveStructureTemplate> childrenNodes = parentNode.getChildren();
                // 筛选出同一个路径并且相同文件类型
                long count = childrenNodes
                        .stream()
                        .filter(e -> driveStructureTemplate.getFilePath().equals(e.getFilePath()) && Objects.equals(driveStructureTemplate.getIsDisk(), e.getIsDisk()))
                        .count();
                if (count > 0) {
                    log.error("[/{}]已存在相同类型的结构", CollUtil.join(Arrays.asList(parentNode.getFilePath(), driveStructureTemplate.getFilePath()), "/"));
                    throw new BusinessException("已存在相同类型的结构");
                }
            }
            // 父节点修改为非叶子节点
            parentNode.setIsLeaf(0);
            driveStructureTemplateMapper.updateById(parentNode);
        }

        return insert > 0 ? driveStructureTemplate : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DriveStructureTemplate update(DriveStructureTemplate newNode) {
        // 当前驱动模板下所有结构
        List<DriveStructureTemplate> allDriveStructureTemplate = findByDriveTemplateIds(Collections.singleton(newNode.getDriveTemplateId()), false);
        // 获取结构信息
        DriveStructureTemplate currentNode = findNode(allDriveStructureTemplate, newNode.getId());

        // 1. 无法删除不存在的节点
        if (currentNode == null) {
            log.error("驱动结构id[{}]不存在", newNode.getId());
            throw new BusinessException("不存在的记录");
        }

        // 内容相同，则直接返回修改成功
        if (Objects.equals(currentNode.calcHashCode(), newNode.calcHashCode())) {
            return newNode;
        }

        // 2. 上级结构不能为自己
        if (Objects.equals(currentNode.getId(), newNode.getPid())) {
            log.error("id为[{}]的上级驱动结构不能为自己", newNode.getId());
            throw new BusinessException("上级结构不能为自己");
        }

        // 3. 禁止修改非叶子节点的文件类型
        if (currentNode.getIsLeaf().compareTo(0) == 0 && !Objects.equals(currentNode.getIsDisk(), newNode.getIsDisk())) {
            log.error("禁止修改非叶子节点的文件类型");
            throw new BusinessException("禁止修改非叶子节点的文件类型");
        }

        DriveStructureTemplate parentNode = findNode(allDriveStructureTemplate, newNode.getPid());
        if (parentNode != null) {
            // 4. 上级节点不能为文件类型
            if (parentNode.getIsDisk().compareTo(0) == 0) {
                log.error("id为[{}]的上级节点不能为文件类型", currentNode.getPid());
                throw new BusinessException("上级节点不能为文件类型");
            }
            // 5. 同层级下不能存在相同文件类型的结构排除自己
            List<DriveStructureTemplate> childrenNodes = parentNode.getChildren();
            if (CollUtil.isNotEmpty(childrenNodes)) {
                long count = childrenNodes
                        .stream()
                        .filter(e -> newNode.getFilePath().equals(e.getFilePath()) && Objects.equals(newNode.getIsDisk(), e.getIsDisk())
                                && !Objects.equals(newNode.getId(), e.getId()))
                        .count();
                if (count > 0) {
                    log.error("[/{}]已存在相同类型的结构", CollUtil.join(Arrays.asList(parentNode.getFilePath(), newNode.getFilePath()), "/"));
                    throw new BusinessException("当前模板已存在相同类型的结构");
                }
            }

            // 新上级结构为非叶子节点
            parentNode.setIsLeaf(0);
            int update = driveStructureTemplateMapper.updateById(parentNode);
            if (update < 1) {
                log.error("id[{}]修改新上级结构为非叶子节点失败", parentNode.getId());
                throw new BusinessException("修改上级结构失败");
            }
        }

        DriveStructureTemplate originNode = findNode(allDriveStructureTemplate, currentNode.getPid());
        // 修改后的上级节点不是原上级节点判断原上级节点是否还有子结构
        if (originNode != null && !Objects.equals(originNode.getId(), newNode.getPid())) {
            List<DriveStructureTemplate> originNodeChildren = originNode.getChildren();
            if (CollUtil.isNotEmpty(originNodeChildren)) {
                // 原上级节点子结构个数除开自己小于1个，则表示为叶子节点
                long count = originNodeChildren.stream().map(DriveStructureTemplate::getId).filter(e -> !Objects.equals(e, currentNode.getId())).count();
                if (count < 1) {
                    // 原上级结构为叶子节点
                    originNode.setIsLeaf(1);
                    int update = driveStructureTemplateMapper.updateById(originNode);
                    if (update < 1) {
                        log.error("id[{}]修改原上级结构为叶子失败", originNode.getId());
                        throw new BusinessException("修改上级结构失败");
                    }
                }
            }
        }

        return driveStructureTemplateMapper.updateById(newNode) > 0 ? newNode : null;
    }

    @Override
    public Map<Long, byte[]> getByteArrByFileIds(Collection<Long> diskFileIds) {
        List<DiskFile> diskFileList = diskFileService.findByFileIds(diskFileIds);
        Map<Long, byte[]> resourceHashMap = new HashMap<>(diskFileList.size());
        if (CollUtil.isNotEmpty(diskFileList)) {
            for (DiskFile diskFile : diskFileList) {
                Resource resource = storageService.loadAsResource(diskFile.getFileName(), diskFile.getFilePath());
                resourceHashMap.put(diskFile.getFileId(), storageService.resourceToByte(resource));
            }
        }
        return resourceHashMap;
    }

    @Override
    public Boolean deleteById(Integer id) {
        DriveStructureTemplate driveStructureTemplate = findById(id);
        if (driveStructureTemplate == null) {
            log.error("驱动结构id[{}]不存在", id);
            throw new BusinessException("id不存在");
        }

        // 1.禁止删除非叶子节点
        if (driveStructureTemplate.getIsLeaf().compareTo(0) == 0) {
            log.error("[/{}]非叶子节点，禁止删除", driveStructureTemplate.getFilePath());
            throw new BusinessException("禁止删除非叶子节点");
        }
        int delete = driveStructureTemplateMapper.deleteById(id);

        updateParentToLeafIfNoChildren(driveStructureTemplate);
        return delete > 0;
    }

    /**
     * 该节点的父节点如果没有多余的子节点，则更新其父节点为叶子节点
     *
     * @param driveStructureTemplate 驱动器结构模板
     */
    private void updateParentToLeafIfNoChildren(DriveStructureTemplate driveStructureTemplate) {
        // 当前驱动模板下所有结构
        List<DriveStructureTemplate> allDriveStructureTemplate = findByDriveTemplateIds(Collections.singletonList(driveStructureTemplate.getDriveTemplateId()), false);
        // 获取上级结构信息
        DriveStructureTemplate parentNode = findNode(allDriveStructureTemplate, driveStructureTemplate.getPid());
        // 父节点没有子节点了，则更新其为叶子节点
        if (parentNode != null && allDriveStructureTemplate.stream().noneMatch(e-> Objects.equals(e.getPid(), parentNode.getId()))) {
            // 父节点更新为非叶子节点
            parentNode.setIsLeaf(1);
            driveStructureTemplateMapper.updateById(parentNode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DriveStructureTemplate addDriveStructureTemplate(DiskFile diskFile, DriveStructureTemplate driveStructureTemplate) {
        driveStructureTemplate.setFilePath(diskFile.getFileName());
        driveStructureTemplate.setIsLeaf(1);
        driveStructureTemplate.setIsDisk(0);
        driveStructureTemplate.setIsUpload(1);
        driveStructureTemplate.setIsFill(0);
        driveStructureTemplate.setUploadTiming(1);
        driveStructureTemplate.setFileId(diskFile.getFileId());
        return create(driveStructureTemplate);
    }

    private List<DriveStructureTemplate> buildTree(List<DriveStructureTemplate> list) {
        HashSet<Integer> rootNodeIds = new HashSet<>(4);
        Map<Integer, DriveStructureTemplate> driveStructureTemplateMap = list.stream().collect(Collectors.toMap(DriveStructureTemplate::getId, e -> e));
        DriveStructureTemplate tempNode;
        for (Map.Entry<Integer, DriveStructureTemplate> entry : driveStructureTemplateMap.entrySet()) {
            tempNode = entry.getValue();
            DriveStructureTemplate parentNode = driveStructureTemplateMap.get(tempNode.getPid());
            if (parentNode != null) {
                parentNode.addChildren(tempNode);
                tempNode = parentNode;
            }
            if (ObjectUtil.equal(tempNode.getPid(), ROOT_ID)) {
                rootNodeIds.add(tempNode.getId());
            }
        }
        return rootNodeIds.stream().map(driveStructureTemplateMap::get).toList();
    }

    /**
     * @param list
     * @param pid
     * @return
     */
    private DriveStructureTemplate findNode(List<DriveStructureTemplate> list, Integer pid) {
        Map<Integer, DriveStructureTemplate> treeMap = list.stream().collect(Collectors.toMap(DriveStructureTemplate::getId, e -> e));
        return treeMap.get(pid);
    }

}
