package com.siteweb.batchtool.service;

import com.siteweb.batchtool.dto.SplitEquipmentInfoDTO;
import com.siteweb.batchtool.vo.*;
import com.siteweb.utility.dto.ImportErrorInfoDTO;

import java.util.List;

public interface VirtualEquipmentService {
    /**
     * 获取层级资源 包含监控单元与设备
     * @return {@link ResourceStructureEquipmentMonitorUnitVo}
     */
    ResourceStructureEquipmentMonitorUnitVo getResourceStructureEquipmentMonitorUnit();

    /**
     * 拆分虚拟设备
     * @param splitEquipmentDto
     * @return {@link Boolean}
     */
    Boolean splitEquipment(SplitEquipmentVo splitEquipmentDto);

    /**
     * 获取虚拟设备列表
     * @return {@link List}<{@link VirtualEquipmentBasicInfoVo}>
     */
    List<VirtualEquipmentBasicInfoVo> virtualEquipmentList();

    /**
     * 导出虚拟设备
     * @param splitEquipmentDto 虚拟设备与信号列表
     * @return {@link VirtualEquipmentSettingVo}
     */
    VirtualEquipmentSettingVo exportVirtualEquipmentSetting(List<Integer> equipmentIds);

    /**
     * 导入虚拟设备
     * @param virtualEquipmentSettingVo 虚拟设备与信号列表
     * @return {@link List}<{@link ImportErrorInfoDTO}>
     */
    List<ImportErrorInfoDTO> importVirtualEquipmentSetting(VirtualEquipmentSettingVo virtualEquipmentSettingVo);

    /**
     * 删除虚拟设备
     * @param id 虚拟设备id
     * @return {@link Boolean}
     */
    void deleteVirtualEquipment(Integer id);

    List<VirtualEquipmentDetailVo> findVirtualEquipmentDetail(Integer equipmentId);

    /**
     * 查看拆分的监控单元采集单元端口等信息
     * @param equipmentId 设备id
     * @return {@link SplitEquipmentInfoDTO}
     */
    SplitEquipmentInfoDTO findSplitInfo(Integer equipmentId);
}
