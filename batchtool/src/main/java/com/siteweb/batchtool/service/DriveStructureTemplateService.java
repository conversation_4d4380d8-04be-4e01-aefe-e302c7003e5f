package com.siteweb.batchtool.service;

import com.siteweb.batchtool.entity.DriveStructureTemplate;
import com.siteweb.utility.entity.DiskFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: lzy
 * @Date: 2022/6/27 10:07
 */
public interface DriveStructureTemplateService {
    List<DriveStructureTemplate> findAllDriveStructureTemplate();

    List<DriveStructureTemplate> findByDriveTemplateIds(Collection<Integer> ids);

    List<DriveStructureTemplate> findByDriveTemplateIds(Collection<Integer> ids, boolean buildTree);

    DriveStructureTemplate findById(Integer driveStructureTemplateId);

    DriveStructureTemplate create(DriveStructureTemplate driveStructureTemplate);

    DriveStructureTemplate update(DriveStructureTemplate driveStructureTemplate);

    Map<Long, byte[]> getByteArrByFileIds(Collection<Long> diskFileIds);

    Boolean deleteById(Integer id);

    DriveStructureTemplate addDriveStructureTemplate(DiskFile diskFile, DriveStructureTemplate driveStructureTemplate);
}
