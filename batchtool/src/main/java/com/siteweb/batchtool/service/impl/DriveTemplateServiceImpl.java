package com.siteweb.batchtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.batchtool.entity.DriveTemplate;
import com.siteweb.batchtool.mapper.DriveTemplateMapper;
import com.siteweb.batchtool.service.DriveTemplateService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * @Author: lzy
 * @Date: 2022/6/27 13:39
 */
@Service
@Slf4j
public class DriveTemplateServiceImpl implements DriveTemplateService {

    @Autowired
    DriveTemplateMapper driveTemplateMapper;

    @Autowired
    private  DataItemService dataItemService;

    @Override
    public List<DriveTemplate> findAllByType(Integer type) {
        LambdaQueryWrapper<DriveTemplate> wrapper = Wrappers.lambdaQuery();
        if (!(Objects.isNull(type) || type < 0)) {
            wrapper.eq(DriveTemplate::getDriveTemplateType, type);
        }
        return driveTemplateMapper.selectList(wrapper);
    }

    @Override
    public DriveTemplate findById(Integer driveTemplateId) {
        return driveTemplateMapper.selectById(driveTemplateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DriveTemplate create(DriveTemplate driveTemplate) {
        handleDefaultTemplate(driveTemplate);
        return driveTemplateMapper.insert(driveTemplate) > 0 ? driveTemplate : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DriveTemplate update(DriveTemplate driveTemplate) {
        List<DataItem> dataItemList = dataItemService.findByEntryId(DataEntryEnum.DRIVE_TEMPLATE_TYPE.getValue());
        long count = dataItemList.stream().map(DataItem::getItemId).filter(e -> Objects.equals(e, driveTemplate.getDriveTemplateType())).count();
        if (count < 1) {
            log.error("不存在的驱动模板类型[{}]", driveTemplate.getDriveTemplateType());
            throw new BusinessException("不存在的驱动模板类型");
        }
        handleDefaultTemplate(driveTemplate);
        return driveTemplateMapper.updateById(driveTemplate) > 0 ? driveTemplate : null;
    }

    /**
     * 处理默认模板逻辑，将同类型其他模板置为非默认状态
     * @param driveTemplate 驱动模板对象
     */
    private void handleDefaultTemplate(DriveTemplate driveTemplate) {
        //如果是默认的驱动模板需要将其他的同类型模板置为false
        if (Objects.equals(driveTemplate.getIsDefaultTemplate(),GlobalConstants.YES)) {
            driveTemplateMapper.updateDefaultTemplate(driveTemplate.getDriveTemplateType());
        }
    }


    @Override
    public Boolean deleteById(Integer id) {
        return driveTemplateMapper.deleteById(id) > 0;
    }

    @Override
    public Integer getDefaultTemplateByType(Integer driveTemplateType) {
        return driveTemplateMapper.getDefaultTemplateByType(driveTemplateType);
    }
}
