package com.siteweb.batchtool.service;

import com.siteweb.batchtool.entity.DriveTemplate;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/27 13:39
 */
public interface DriveTemplateService {
    List<DriveTemplate> findAllByType(Integer type);

    DriveTemplate findById(Integer driveTemplateId);

    DriveTemplate create(DriveTemplate build);

    DriveTemplate update(DriveTemplate build);

    Boolean deleteById(Integer id);

    Integer getDefaultTemplateByType(Integer driveTemplateType);
}
