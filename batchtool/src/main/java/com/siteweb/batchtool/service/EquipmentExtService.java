package com.siteweb.batchtool.service;

import com.siteweb.batchtool.dto.BuildConfigDTO;
import com.siteweb.batchtool.dto.EquipmentExtUpdateDto;
import com.siteweb.batchtool.dto.EquipmentSettingDto;
import com.siteweb.batchtool.dto.EquipmentSettingUpdateDto;
import com.siteweb.batchtool.vo.EquipmentSettingVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2022/6/28 10:55
 */
public interface EquipmentExtService {
    /**
     * 生成配置
     *
     * @param buildConfigDTO 生成配置dto
     * @param response
     */
    void buildConfig(BuildConfigDTO buildConfigDTO, HttpServletResponse response);


    /**
     * 修改配置下发状态
     *
     * @param equipmentSettingVoList 设备
     * @param isReset      下发状态
     */
    void updateTemplateByEquipment(List<EquipmentSettingVo> equipmentSettingVoList, Integer isReset);

    /**
     * 获取配置管理列表
     *
     * @param equipmentSettingDto 过滤条件
     * @return {@link EquipmentSettingVo}>
     */
    List<EquipmentSettingVo> getSettingManager(EquipmentSettingDto equipmentSettingDto);

    /**
     * 初始化设备的扩展信息
     *
     * @param equipmentIds 设备ids
     * @param portType     32 BACNet  33 SNMP
     */
    void initExt(List<Integer> equipmentIds, Integer portType);

    /**
     * 获取配置信息根据设备id
     *
     * @param id 设备id
     * @return {@link EquipmentSettingVo}
     */
    EquipmentSettingVo getSettingManagerById(Integer id);

    /**
     * 更新设备信息
     *
     * @param equipmentSettingUpdateDto 更新设备dto
     * @return {@link Boolean}
     */
    Boolean updateEquipment(EquipmentSettingUpdateDto equipmentSettingUpdateDto);

    /**
     * 上传配置
     *
     * @param equipmentExtUpdateDto dto
     * @return {@link Boolean}
     */
    Boolean uploadSetting(EquipmentExtUpdateDto equipmentExtUpdateDto);

    /**
     * 删除配置
     *
     * @param equipmentExtUpdateDto
     * @return {@link Boolean}
     */
    Boolean deleteSetting(EquipmentExtUpdateDto equipmentExtUpdateDto);

    /**
     * 修改引用驱动模板根据设备id
     *
     * @param equipmentExtUpdateDto 更新设备扩展dto
     * @return {@link Boolean}
     */
    Boolean updateTemplateByEquipmentId(EquipmentExtUpdateDto equipmentExtUpdateDto);

    /**
     * 初始化采集配置
     *
     * @param buildConfigDTO 设备信息
     * @return {@link Integer} 初始化数量
     */
    Integer initSampler(BuildConfigDTO buildConfigDTO);
}
