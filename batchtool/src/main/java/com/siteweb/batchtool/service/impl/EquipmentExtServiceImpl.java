package com.siteweb.batchtool.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.siteweb.batchtool.basic.config.BuildFileConfig;
import com.siteweb.batchtool.basic.rannable.BuildConfigRunnable;
import com.siteweb.batchtool.dto.*;
import com.siteweb.batchtool.entity.DriveStructureTemplate;
import com.siteweb.batchtool.entity.DriveTemplate;
import com.siteweb.batchtool.entity.EquipmentExt;
import com.siteweb.batchtool.mapper.EquipmentExtMapper;
import com.siteweb.batchtool.service.DriveStructureTemplateService;
import com.siteweb.batchtool.service.DriveTemplateService;
import com.siteweb.batchtool.service.EquipmentExtService;
import com.siteweb.batchtool.settingEnum.PortTypeEnum;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.batchtool.warning.WarningApplication;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.ZipUtil;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import com.siteweb.monitoring.mapper.SamplerUnitMapper;
import com.siteweb.utility.configuration.FileServerConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: lzy
 * @Date: 2022/6/28 10:55
 */
@Service
@Slf4j
public class EquipmentExtServiceImpl implements EquipmentExtService {

    @Autowired
    DriveStructureTemplateService driveStructureTemplateService;

    @Autowired
    EquipmentExtMapper equipmentExtMapper;

    @Autowired
    WarningApplication warningApplication;

    @Autowired
    DriveTemplateService driveTemplateService;

    @Autowired
    SamplerUnitMapper samplerUnitMapper;
    @Autowired
    EquipmentMapper equipmentMapper;
    @javax.annotation.Resource(name = "settingThreadPool")
    private ThreadPoolExecutor settingThreadPool;

    @Override
    public void buildConfig(BuildConfigDTO buildConfigDTO, HttpServletResponse response) {
        long dbStartTime = System.currentTimeMillis();
        // 1. 根据设备id查询设备相关信息
        List<EquipmentSettingVo> dealEquipments = equipmentExtMapper.findDestSettingListByEquipmentIds(buildConfigDTO.getPortType(), buildConfigDTO.getEquipmentIds());
        if (CollUtil.isEmpty(dealEquipments)) {
            return;
        }

        // 2. 获取设备使用的驱动模板
        Set<Integer> driveTemplateIds = dealEquipments.stream().map(EquipmentSettingVo::getDriveTemplateId).collect(Collectors.toSet());

        // 3. 根据驱动模板id查询驱动结构
        List<DriveStructureTemplate> driveStructureTemplateList = driveStructureTemplateService.findByDriveTemplateIds(driveTemplateIds);
        if (CollUtil.isEmpty(driveStructureTemplateList)) {
            log.error("请求生成配置参数：{}, 不存在的驱动模板ids，{}", buildConfigDTO, driveTemplateIds);
            throw new BusinessException("找不到的驱动模板");
        }

        // 4. 获取该些设备中上传了的文件id（后续需要加载到内存）
        Set<Long> fileIds = dealEquipments.stream().map(EquipmentSettingVo::getFileId).collect(Collectors.toSet());

        // 4.1 获取驱动模板中需要用到的文件id
        findTemplateFileId(driveStructureTemplateList, fileIds);

        // 5. 提前加载文件资源 key => fileId， value => fileByteData
        Map<Long, byte[]> byteMap = driveStructureTemplateService.getByteArrByFileIds(fileIds);

        long dbEndTime = System.currentTimeMillis();

        // 6. 将驱动结构整合成驱动模板
        Map<Integer, DriveTemplate> driveTemplateMap = driveStructureTemplateList
                .stream().collect(Collectors.groupingBy(DriveStructureTemplate::getDriveTemplateId)).entrySet()
                .stream().collect(Collectors.toMap(Map.Entry::getKey, e -> new DriveTemplate(e.getKey(), e.getValue())));
                // to [driveTemplateId(驱动模板id) => DriveTemplate(驱动模板， 包含当前模板下的所有驱动结构)]

        // 7. 生成随机目录明（后续用于压缩和创建文件）
        String tempPath = File.separator + UUID.randomUUID();
        CountDownLatch countDownLatch = new CountDownLatch(dealEquipments.size());
        long fileHandlerStarTime = System.currentTimeMillis();
        for (EquipmentSettingVo equipmentSettingVo : dealEquipments) {
            // 获取要处理的驱动模板
            DriveTemplate driveTemplate = driveTemplateMap.get(equipmentSettingVo.getDriveTemplateId());
            if (driveTemplate == null) {
                countDownLatch.countDown();
                continue;
            }
            // 指定临时目录
            driveTemplate.setTempPath(tempPath);

            // 多线程生成
            BuildConfigTemp buildConfigTemp = new BuildConfigTemp(driveTemplate, equipmentSettingVo, byteMap);
            settingThreadPool.execute(new BuildConfigRunnable(buildConfigTemp, countDownLatch));
        }

        try {
            boolean await = countDownLatch.await(3, TimeUnit.MINUTES);
            if (await) {
                long fileHandlerEndTime = System.currentTimeMillis();

                long zipStartTime = System.currentTimeMillis();
                String zipPath = BuildFileConfig.getBuildFilePath() + tempPath + ".zip";

                // 3.压缩
                ZipUtil.compress(BuildFileConfig.getBuildFilePath() + tempPath, zipPath, true);
                long zipEndTime = System.currentTimeMillis();

                long toWebStartTime = System.currentTimeMillis();
                // 4.输出web
                ZipUtil.toWebDownLoad(zipPath, DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".zip", response);
                // 5.删除压缩包
                PathUtil.del(Paths.get(zipPath));

                log.info("[{}]生成配置，数据操作相关耗时:{}ms, 文件创建耗时:{}ms, 压缩文件耗时:{}ms, 文件输出web耗时:{}ms, 总耗时:{}ms",
                        buildConfigDTO,
                        dbEndTime - dbStartTime,
                        fileHandlerEndTime - fileHandlerStarTime,
                        zipEndTime - zipStartTime,
                        System.currentTimeMillis() - toWebStartTime,
                        System.currentTimeMillis() - dbStartTime);

                if (CollUtil.isNotEmpty(dealEquipments)) {
                    updateTemplateByEquipment(dealEquipments, 0);
                }
            }
        } catch (Exception e) {
            log.error("生成配置异常, ", e);
        }
    }

    /**
     * 找到当前结构中需要读取本地文件的文件id
     *
     * @param driveStructureTemplateList
     * @param fileIds
     */
    public void findTemplateFileId(List<DriveStructureTemplate> driveStructureTemplateList, Set<Long> fileIds) {
        for (DriveStructureTemplate driveStructureTemplate : driveStructureTemplateList) {
            // 需要上传并且是创建模板时上传的文件
            if (driveStructureTemplate.getIsUpload().compareTo(1) == 0 && driveStructureTemplate.getUploadTiming().compareTo(0) == 0) {
                // 创建模板时就需要上传的文件
                fileIds.add(driveStructureTemplate.getFileId());
            }
            if (CollUtil.isNotEmpty(driveStructureTemplate.getChildren())) {
                findTemplateFileId(driveStructureTemplate.getChildren(), fileIds);
            }
        }
    }

    @Autowired
    public void setFileServerConfig(FileServerConfig fileServerConfig) {
        BuildFileConfig.filePath = fileServerConfig.getRootPath();
    }

    @Override
    public void updateTemplateByEquipment(List<EquipmentSettingVo> equipmentSettingVoList, Integer isReset) {
        List<EquipmentExt> collect = equipmentSettingVoList.stream().map(e -> {
            EquipmentExt equipmentExt = new EquipmentExt();
            equipmentExt.setFieldHash(e.fieldHashValue());
            equipmentExt.setIsReset(isReset);
            equipmentExt.setIsUpload(e.getIsUpload());
            equipmentExt.setFileId(e.getFileId());
            equipmentExt.setEquipmentId(e.getEquipmentId());
            equipmentExt.setDriveTemplateId(e.getDriveTemplateId());
            return equipmentExt;
        }).toList();
        equipmentExtMapper.updateBatchByEquipmentId(collect);
    }

    @Override
    public List<EquipmentSettingVo> getSettingManager(EquipmentSettingDto equipmentSettingDto) {
        List<EquipmentSettingVo> settingManager = equipmentExtMapper.getSettingManagerList(equipmentSettingDto.getPortType());
        //判断原集合中是否与目标集合中的有冲突,需警告或提醒
        warningApplication.isWarningOrRemind(settingManager, settingManager);
        //判断客户端工具是否有所修改
        this.existsClientUpdate(settingManager);
        return settingManager;
    }

    @Override
    @Async
    public void initExt(List<Integer> equipmentIds, Integer portType) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return;
        }
        Set<Integer> notInitEquipmentIds = this.getNotInitEquipmentIds(equipmentIds);
        if (CollUtil.isEmpty(notInitEquipmentIds)) {
            return;
        }
        //查询默认模板
        Integer defaultTemplateId = this.getDefaultTemplateId(portType);
        //初始化并保存ids
        List<EquipmentExt> equipmentSaveList = new ArrayList<>(notInitEquipmentIds.size());
        for (Integer notInitEquipmentId : notInitEquipmentIds) {
            equipmentSaveList.add(new EquipmentExt(notInitEquipmentId, GlobalConstants.YES, GlobalConstants.NO, defaultTemplateId));
        }
       equipmentExtMapper.batchInsert(equipmentSaveList);
    }

    @Override
    public EquipmentSettingVo getSettingManagerById(Integer id) {
        return equipmentExtMapper.findSettingManagerById(id);
    }

    @Override
    public Boolean updateEquipment(EquipmentSettingUpdateDto equipmentSettingUpdateDto) {
        //模板重新生成
        this.resetTemplateByEquipmentId(equipmentSettingUpdateDto.getEquipmentId());
        //更新设备端口信息
        equipmentSettingUpdateDto.setSetting(CharSequenceUtil.concat(true, equipmentSettingUpdateDto.getIpAddress(), "/", equipmentSettingUpdateDto.getPortAttribute()));
        this.equipmentExtMapper.updatePort(equipmentSettingUpdateDto);
        //更新采集单元信息
        this.equipmentExtMapper.updateSamplerUnit(equipmentSettingUpdateDto);
        //更新驱动模板
        this.equipmentExtMapper.updateTemplateByEquipmentId(equipmentSettingUpdateDto.getEquipmentId(), equipmentSettingUpdateDto.getDriveTemplateId());
        return true;
    }

    @Override
    public Boolean uploadSetting(EquipmentExtUpdateDto equipmentExtUpdateDto) {
        Equipment equipment = equipmentMapper.selectById(equipmentExtUpdateDto.getEquipmentId());
        //全部上传
        if (equipmentExtUpdateDto.getIsAll() == GlobalConstants.YES) {
            this.resetTemplateByTemplateId(equipment.getEquipmentTemplateId());
            return equipmentExtMapper.updateTemplateFileById(equipment.getEquipmentTemplateId(), equipmentExtUpdateDto.getFileId(), GlobalConstants.YES) > 0;
        }
        //上传一个
        this.resetTemplateByEquipmentId(equipment.getEquipmentId());
        return equipmentExtMapper.updateFileIdByEquipmentId(equipment.getEquipmentId(), equipmentExtUpdateDto.getFileId(), GlobalConstants.YES) > 0;
    }

    @Override
    public Boolean deleteSetting(EquipmentExtUpdateDto equipmentExtUpdateDto) {
        Equipment equipment = equipmentMapper.selectById(equipmentExtUpdateDto.getEquipmentId());
        //全部删除
        if (equipmentExtUpdateDto.getIsAll() == GlobalConstants.YES) {
            this.resetTemplateByTemplateId(equipment.getEquipmentTemplateId());
            return this.equipmentExtMapper.updateTemplateFileById(equipment.getEquipmentTemplateId(), null, GlobalConstants.NO) > 0;
        }
        //单单删除这一个
        this.resetTemplateByEquipmentId(equipment.getEquipmentId());
        return this.equipmentExtMapper.updateFileIdByEquipmentId(equipment.getEquipmentId(), null, GlobalConstants.NO) > 0;
    }

    @Override
    public Boolean updateTemplateByEquipmentId(EquipmentExtUpdateDto equipmentExtUpdateDto) {
        Equipment equipment = equipmentMapper.selectById(equipmentExtUpdateDto.getEquipmentId());
        //如果修改了驱动模板，对于引用统一模板的设备，弹框，手动选择是否要批量切换(是)
        if (equipmentExtUpdateDto.getIsAll() == GlobalConstants.YES) {
            this.resetTemplateByTemplateId(equipment.getEquipmentTemplateId());
            return this.equipmentExtMapper.updateTemplateByTemplateId(equipment.getEquipmentTemplateId(), equipmentExtUpdateDto.getDriveTemplateId()) > 0;
        }
        //否
        this.resetTemplateByEquipmentId(equipment.getEquipmentId());
        return this.equipmentExtMapper.updateTemplateByEquipmentId(equipmentExtUpdateDto.getEquipmentId(), equipmentExtUpdateDto.getDriveTemplateId()) > 0;
    }

    @Override
    public Integer initSampler(BuildConfigDTO buildConfigDTO) {
        if (CollUtil.isEmpty(buildConfigDTO.getEquipmentIds())) {
            return 0;
        }
        String portName = PortTypeEnum.getPortNameByType(buildConfigDTO.getPortType());
        if (CharSequenceUtil.isBlank(portName)) {
            log.error("端口类型错误:{}, ", buildConfigDTO);
            return 0;
        }
        List<NeedSamplerDto> portList = this.equipmentExtMapper.getNeedInitSampler(buildConfigDTO.getEquipmentIds());
        for (NeedSamplerDto dto : portList) {
            dto.setAddress(dto.getPortNo());
            dto.setDllPath(CharSequenceUtil.concat(true, portName, dto.getPortNo().toString(), ".so"));
        }
        //批量初始化采集单元地址与采集动态库地址
        equipmentExtMapper.batchInit(portList);
        return portList.size();
    }

    /**
     * 重新生成模板(设备id)
     *
     * @param equipmentId 设备id
     */
    private void resetTemplateByEquipmentId(Integer equipmentId) {
        if (ObjectUtil.isNull(equipmentId)) {
            return;
        }
        LambdaUpdateWrapper<EquipmentExt> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(EquipmentExt::getIsReset, GlobalConstants.YES)
                           .eq(EquipmentExt::getEquipmentId, equipmentId);
        equipmentExtMapper.update(null, lambdaUpdateWrapper);
    }

    private Integer getDefaultTemplateId(Integer portType) {
        Integer driveTemplateType = PortTypeEnum.getDriveTemplateTypeByType(portType);
        return driveTemplateService.getDefaultTemplateByType(driveTemplateType);
    }

    private Set<Integer> getNotInitEquipmentIds(List<Integer> equipmentIds) {
        //获取已初始化的id,并将其移除
        List<Integer> existsEquipmentIds = equipmentExtMapper.existsEquipmentIds(equipmentIds);
        equipmentIds.removeIf(existsEquipmentIds::contains);
        return new HashSet<>(equipmentIds);
    }

    /**
     * 判断客户端工具是否有所修改
     *
     * @param content 分页数据
     */
    private void existsClientUpdate(List<EquipmentSettingVo> content) {
        if (CollUtil.isEmpty(content)) {
            return;
        }

        for (EquipmentSettingVo setting : content) {
            //字段hash为空说明无重新生成过,必重新生成
            if (ObjectUtil.isNull(setting.getFieldHash())) {
                setting.setIsReset(GlobalConstants.YES);
                continue;
            }
            if (!ObjectUtil.equals(setting.getFieldHash(), setting.fieldHashValue())) {
                setting.setIsReset(GlobalConstants.YES);
            }
        }
    }

    /**
     * 重新生成模板根据 设备引用模板id
     * 相同模板的设备全部需要重新生成配置
     *
     * @param equipmentTemplateId 引用模板id
     */
    private void resetTemplateByTemplateId(Integer equipmentTemplateId) {
        if (ObjectUtil.isNull(equipmentTemplateId)) {
            return;
        }
        equipmentExtMapper.resetTemplateByTemplateId(equipmentTemplateId);
    }
    /**
     * 生成配置临时对象
     */
    @Data
    @AllArgsConstructor
    public static class BuildConfigTemp {
        /**
         * 结构模板
         */
        DriveTemplate driveTemplate;
        /**
         * 当前数据对象
         */
        EquipmentSettingVo equipmentSettingVo;
        /**
         * 文件对象
         */
        Map<Long, byte[]> byteMap;
    }
}
