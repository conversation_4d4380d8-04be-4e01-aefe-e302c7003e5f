package com.siteweb.batchtool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.influxdb.annotation.Column;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 驱动结构模板表
 *
 * <AUTHOR>
 */
@Data
@TableName("tbl_drivestructuretemplate")
@ApiModel("驱动结构模板")
public class DriveStructureTemplate {

    @TableId(type = IdType.AUTO)
    @Column(name = "id")
    private Integer id;

    @ApiModelProperty("模板路径")
    private String filePath;

    @ApiModelProperty("上级驱动结构id")
    private Integer pid;

    @ApiModelProperty("是否是叶子节点")
    private Integer isLeaf;

    @ApiModelProperty("是否是目录")
    private Integer isDisk;

    @ApiModelProperty("驱动结构文件id")
    private Long fileId;

    @ApiModelProperty("是否需要上传")
    private Integer isUpload;

    @ApiModelProperty("是否需要填充")
    private Integer isFill;

    @ApiModelProperty("所属驱动模板id")
    private Integer driveTemplateId;

    @ApiModelProperty("上传时机 0-模板创建时 1-生成配置时")
    private Integer uploadTiming;

    @ApiModelProperty("驱动模板id")
    @TableField(exist = false)
    private List<DriveStructureTemplate> children;

    @ApiModelProperty("文件名称")
    @TableField(exist = false)
    private String fileName;

    public DriveStructureTemplate() {
        children = new ArrayList<>();
    }

    public Integer calcHashCode() {
        return Objects.hash(id, filePath, pid, isLeaf, isDisk, fileId, isUpload, isFill, driveTemplateId, uploadTiming);
    }

    public void addChildren(DriveStructureTemplate driveStructureTemplate) {
        children.add(driveStructureTemplate);
    }
}
