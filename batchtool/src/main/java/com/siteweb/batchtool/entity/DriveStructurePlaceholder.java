package com.siteweb.batchtool.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 驱动结构占位符对象
 *
 * @Author: lzy
 * @Date: 2022/5/11 13:43
 */
@Data
@ApiModel("驱动结构占位符对象")
public class DriveStructurePlaceholder {
    @ApiModelProperty("字段属性值")
    private String field;

    @ApiModelProperty("字段名")
    private String fieldName;

    @ApiModelProperty("描述信息")
    private String desc;

    public DriveStructurePlaceholder() {
    }

    public DriveStructurePlaceholder(String field, String fieldName, String desc) {
        this.field = field;
        this.fieldName = fieldName;
        this.desc = desc;
    }
}
