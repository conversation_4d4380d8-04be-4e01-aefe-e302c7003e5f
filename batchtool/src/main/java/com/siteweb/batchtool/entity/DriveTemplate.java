package com.siteweb.batchtool.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 驱动模板表
 */
@Data
@TableName("tbl_drivetemplate")
public class DriveTemplate {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String driveTemplateName;

    private String driverTemplateDescribe;

    private Integer isDefaultTemplate;

    private Integer driveTemplateType;

    @JsonIgnore
    @TableField(exist = false)
    @ApiModelProperty("驱动结构")
    private List<DriveStructureTemplate> driveStructureTemplateList;

    @JsonIgnore
    @TableField(exist = false)
    @ApiModelProperty("临时目录")
    private String tempPath;

    public DriveTemplate() {
    }

    public DriveTemplate(Integer id, List<DriveStructureTemplate> driveStructureTemplateList) {
        this.id = id;
        this.driveStructureTemplateList = driveStructureTemplateList;
    }

}
