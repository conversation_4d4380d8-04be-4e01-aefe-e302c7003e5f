package com.siteweb.batchtool.warning;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.batchtool.warning.warningImpl.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 主要用于警告与提醒的判断
 *
 * <AUTHOR>
 * @date 2022/03/31
 */
@Service
public class WarningApplication implements InitializingBean {
    private final ArrayList<SettingWarning> settingWarningArrayList = new ArrayList<>(5);

    @Override
    public void afterPropertiesSet() {
        settingWarningArrayList.add(new PortWarning());
        settingWarningArrayList.add(new ReadWriteAttributeEmptyWarning());
        settingWarningArrayList.add(new SamplerUnitWarning());
        settingWarningArrayList.add(new AddressWarning());
        settingWarningArrayList.add(new DllPathWarning());
    }


    /**
     * 判断原集合中是否与目标集合中的有冲突,需警告或提醒
     *
     * @param originList      原集合
     * @param destinationList 目标集合
     */
    public void isWarningOrRemind(List<EquipmentSettingVo> originList, List<EquipmentSettingVo> destinationList) {
        if (CollUtil.isEmpty(originList) || CollUtil.isEmpty(destinationList)) {
            return;
        }
        //是否需要警告
        originList.stream().parallel().forEach(origin -> {
            for (EquipmentSettingVo dest : destinationList) {
                //同一设备无需对比
                if (ObjectUtil.equals(origin.getEquipmentId(), dest.getEquipmentId())) {
                    continue;
                }
                for (SettingWarning warning : this.settingWarningArrayList) {
                    warning.isWarning(origin, dest);
                }
            }
        });
    }
}
