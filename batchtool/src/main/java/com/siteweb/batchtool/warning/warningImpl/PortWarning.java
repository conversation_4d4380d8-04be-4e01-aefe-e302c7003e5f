package com.siteweb.batchtool.warning.warningImpl;


import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.batchtool.warning.SettingWarning;

import java.util.Objects;

/**
 * 端口列警告
 * 1、设备如果在同一监控单元下，端口相同、采集单元不同警告
 * 2、不同监控单元，同一个RMU服务器下，端口冲突警告 (同一RMU下端口相同)
 *(暂时弃用,与采集单元逻辑相冲突了)
 * <AUTHOR>
 * @date 2022/03/31
 */
public class PortWarning implements SettingWarning {
    @Override
    public String warningField() {
        return "portName";
    }

    @Override
    public void isWarning(EquipmentSettingVo origin, EquipmentSettingVo destination) {
        //设备如果在同一监控单元下，端口相同、采集单元不同警告
        if (Objects.equals(origin.getMonitorUnitId(), destination.getMonitorUnitId()) &&
                Objects.equals(origin.getPortName(), destination.getPortName()) &&
                !Objects.equals(origin.getMonitorUnitId(), destination.getMonitorUnitId())) {
            this.addWarningField(origin);
            return;
        }
        //不同监控单元，同一个RMU服务器下，端口相同警告
        if (!Objects.equals(origin.getMonitorUnitId(), destination.getMonitorUnitId()) &&
                (Objects.equals(origin.getWorkStationId(), destination.getWorkStationId()) &&
                        Objects.equals(origin.getPortName(), destination.getPortName()))) {
            this.addWarningField(origin);
        }
    }
}
