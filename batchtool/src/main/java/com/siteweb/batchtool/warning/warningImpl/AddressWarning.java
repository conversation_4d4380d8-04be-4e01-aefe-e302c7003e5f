package com.siteweb.batchtool.warning.warningImpl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.batchtool.warning.SettingWarning;

import java.util.Objects;

/**
 * 同一RMU服务器下，地址全局唯一校验，采集单元地址唯一
 * <AUTHOR>
 * @date 2022/04/11
 */
public class AddressWarning implements SettingWarning {
    @Override
    public String warningField() {
        return "address";
    }

    @Override
    public void isWarning(EquipmentSettingVo origin, EquipmentSettingVo destination) {
        if (ObjectUtil.isNull(origin.getWorkStationId()) || ObjectUtil.isNull(destination.getWorkStationId())) {
            return;
        }
        //监控单元相同不警告
        if (ObjectUtil.equals(origin.getSamplerUnitId(), destination.getSamplerUnitId())) {
            return;
        }
        //同一RMU服务器下，地址全局唯一校验，采集单元地址唯一
        if (Objects.equals(origin.getWorkStationId(), destination.getWorkStationId()) && Objects.equals(origin.getAddress(), destination.getAddress())) {
            this.addWarningField(origin);
        }
    }
}
