package com.siteweb.batchtool.warning.warningImpl;

import cn.hutool.core.util.StrUtil;
import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.batchtool.warning.SettingWarning;

/**
 * IP地址为空警告
 * <AUTHOR>
 * @date 2022/04/02
 */
public class ReadWriteAttributeEmptyWarning implements SettingWarning {
    @Override
    public String warningField() {
        return "portAttribute";
    }

    @Override
    public void isWarning(EquipmentSettingVo origin, EquipmentSettingVo destination) {
        //IP地址或读写属性为空警告
        if (StrUtil.hasBlank(origin.getIpAddress(), origin.getPortAttribute())) {
            this.addWarningField(origin);
        }
    }
}
