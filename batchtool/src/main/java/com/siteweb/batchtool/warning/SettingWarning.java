package com.siteweb.batchtool.warning;


import com.siteweb.batchtool.vo.EquipmentSettingVo;

/**
 * 配置警告冲突接口
 * 用于判断配置生成工具列表中数据的各种警告
 * <AUTHOR>
 * @date 2022/03/31
 */
public interface SettingWarning {
    /**
     * 警告字段
     * @return {@link String}
     */
    String warningField();
    /**
     * 是否需要警告
     *
     * @param origin 源数据
     * @param destination 目标数据
     * @return {@link Boolean} true是 false否
     */
    void isWarning(EquipmentSettingVo origin, EquipmentSettingVo destination);

    /**
     * 添加警告字段
     * @param origin 原数据
     */
    default void addWarningField(EquipmentSettingVo origin){
        origin.getWarningList().add(warningField());
    }
}
