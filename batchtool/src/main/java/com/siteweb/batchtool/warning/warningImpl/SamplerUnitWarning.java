package com.siteweb.batchtool.warning.warningImpl;


import com.siteweb.batchtool.vo.EquipmentSettingVo;
import com.siteweb.batchtool.warning.SettingWarning;

import java.util.Objects;

/**
 * 采集单元警告
 * 1、校验同一COM下，是否有不同采集单元名称的设备，若有，警告 (端口相同,采集单元不同警告)
 * (由于与PortWarning类逻辑重复，所以废弃)
 * <AUTHOR>
 * @date 2022/03/31
 */
public class SamplerUnitWarning implements SettingWarning {
    @Override
    public String warningField() {
        return "samplerUnitName";
    }

    @Override
    public void isWarning(EquipmentSettingVo origin, EquipmentSettingVo destination) {
        //同一监控单元下 端口相同(采用端口名称即可),采集单元不同警告
        if (Objects.equals(origin.getMonitorUnitId(),destination.getMonitorUnitId()) && Objects.equals(origin.getPortName(), destination.getPortName()) && !Objects.equals(origin.getSamplerUnitId(), destination.getSamplerUnitId())) {
            this.addWarningField(origin);
        }
    }
}
