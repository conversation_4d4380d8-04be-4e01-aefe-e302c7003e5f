package com.siteweb.powerdistribution.dto;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.capacity.dto.CapacityAttributeValue;
import com.siteweb.capacity.entity.CapacityAttribute;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name = "historycapacitydatas")
public class HistoryCapacityDatas {

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "AttributeId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String attributeId;

    @Column(name = "BaseAttributeId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String baseAttributeId;

    @Column(name = "DeviceId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String deviceId;

    @Column(name = "DiagramId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String diagramId;

    @Column(name = "AttributeName")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String attributeName;
    @Column(name = "CompensateFactor")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String compensateFactor;
    //@Column(name = "Description")
    //@JsonFormat(shape = JsonFormat.Shape.STRING)
    //public String description;
    @Column(name = "FreeCapacity")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String freeCapacity;
    @Column(name = "InsertTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String insertTime;
    @Column(name = "ObjectTypeId")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT)
    public String objectTypeId;
    @Column(name = "OriginCapacity")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String originCapacity;
    @Column(name = "Percent")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String percent;
    @Column(name = "RatedCapacity")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String ratedCapacity;
    @Column(name = "SampleTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String sampleTime;
    @Column(name = "Unit")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String unit;
    @Column(name = "UsedCapacity")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
    public String usedCapacity;
    @Column(name = "BaseAttributeName")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String baseAttributeName;


    public static CapacityAttributeValue fromAttribute(HistoryCapacityDatas attribute) {
        if (ObjectUtil.isNull(attribute)) {
            return null;
        }

        CapacityAttributeValue result = new CapacityAttributeValue();

        result.setObjectId(convertToInteger(attribute.getDeviceId()));
        result.setObjectTypeId(convertToInteger(attribute.getObjectTypeId()));
        result.setAttributeId(convertToInteger(attribute.getAttributeId()));
        result.setBaseAttributeId(convertToInteger(attribute.getBaseAttributeId()));

        result.setAttributeName(attribute.getAttributeName());
        result.setBaseAttributeName(attribute.getBaseAttributeName());

        result.setCompensateFactor(convertToDouble(attribute.getCompensateFactor()));
        result.setRatedCapacity(convertToDouble(attribute.getRatedCapacity()));
        result.setOriginCapacity(convertToDouble(attribute.getOriginCapacity()));
        result.setUsedCapacity(convertToDouble(attribute.getUsedCapacity()));
        result.setFreeCapacity(convertToDouble(attribute.getFreeCapacity()));
        result.setPercent(convertToDouble(attribute.getPercent()));

        result.setSampleTime(attribute.getSampleTime());
        result.setUnit(attribute.getUnit());

        return result;
    }

    private static Integer convertToInteger(String value) {
        if (value == null) {
            return null;
        }
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            // Handle or log the exception if necessary
            return null;
        }
    }

    private static Double convertToDouble(String value) {
        if (value == null) {
            return null;
        }
        try {
            return Double.valueOf(value);
        } catch (NumberFormatException e) {
            // Handle or log the exception if necessary
            return null;
        }
    }

    /** 去除日期格式串中的T和Z，如：2023-08-06T16:42:55Z */
    public String formatTime() {
        return time.replace("T", " ").replace("Z", "");
    }
}
