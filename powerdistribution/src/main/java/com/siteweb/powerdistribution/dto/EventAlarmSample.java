package com.siteweb.powerdistribution.dto;

import lombok.Data;

import java.util.Date;

@Data
public class EventAlarmSample {

    private Integer equipmentId;
    /**
     * 告警等级ID
     */
    private Integer eventLevel;
    /**
     * 告警等级名
     */
    private String eventSeverity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 取消时间
     */
    private Date cancelTime;
    private Date confirmTime;
    /**
     * 基类ID
     */
    private Long baseTypeId;
    /** 告警查询来源：1-历史报警表；2-活动告警缓存 */
    private Integer sourceFrom;

}
