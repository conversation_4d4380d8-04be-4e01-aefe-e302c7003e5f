package com.siteweb.powerdistribution.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name = "historysignaldatas")
public class HistorySignalDatas {

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "DeviceId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String deviceId;

    @Column(name = "DiagramId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String diagramId;

    @Column(name = "SignalId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String signalId;

    @Column(name = "CurrentValue")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String currentValue;

    @Column(name = "InsertTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String insertTime;

    @Column(name = "OriginalValue")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String originalValue;

    @Column(name = "SampleTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String sampleTime;

    @Column(name = "SignalName")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String signalName;

    @Column(name = "Unit")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String unit;

    @Column(name = "BaseTypeId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String baseTypeId;


    /** 去除日期格式串中的T和Z，如：2023-08-06T16:42:55Z */
    public String formatTime() {
        return time.replace("T", " ").replace("Z", "");
    }
}
