package com.siteweb.powerdistribution.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PDReportCapacityData {
    //设备id
    private Integer equipmentId;
    //设备名称
    private String equipmentName;
    //容量属性
    private String capacityName;
    //容量类型
    private String attributeName;
    //实时值
    private String realValue;
    //额定值
    private String ratedValue;
    //已用容量
    private String usedValue;
    //预警等级名称
    private String preAlarmLevelName;
    //预警等级
    private Integer preAlarmLevel;
    //预警颜色
    private String preAlarmColor;
}
