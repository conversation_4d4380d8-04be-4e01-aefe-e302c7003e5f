package com.siteweb.powerdistribution.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name = "recordtimedatas")
public class RecordTimeDatas {

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "DiagramId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String diagramId;

    @Column(name = "RecordTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String recordTime;

    @Column(name = "InsertSignalCount")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT)
    public Integer insertSignalCount;

    @Column(name = "InsertCapacityCount")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT)
    public Integer insertCapacityCount;
}
