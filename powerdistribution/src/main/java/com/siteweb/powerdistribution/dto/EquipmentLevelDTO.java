package com.siteweb.powerdistribution.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class EquipmentLevelDTO {
    private String equipmentName;
    private Integer equipmentId;
    private String equipmentPosition;
    private List<EquipmentInfo> children;
    private List<EquipmentInfo> parents;

    // 在添加子节点时进行元素去重
    public void addChild(EquipmentInfo child) {
        if (children == null) {
            children = new ArrayList<>();
        }

        // 利用HashSet进行元素去重
        Set<Integer> uniqueIds = new HashSet<>();
        List<EquipmentInfo> newChildren = new ArrayList<>();

        for (EquipmentInfo existingChild : children) {
            if (uniqueIds.add(existingChild.getEquipmentId())) {
                newChildren.add(existingChild);
            }
        }

        if (child != null && uniqueIds.add(child.getEquipmentId())) {
            newChildren.add(child);
        }

        children = newChildren;
    }

    // 在添加父节点时进行元素去重
    public void addParent(EquipmentInfo parent) {
        if (parents == null) {
            parents = new ArrayList<>();
        }

        // 利用HashSet进行元素去重
        Set<Integer> uniqueIds = new HashSet<>();
        List<EquipmentInfo> newParents = new ArrayList<>();

        for (EquipmentInfo existingParent : parents) {
            if (uniqueIds.add(existingParent.getEquipmentId())) {
                newParents.add(existingParent);
            }
        }

        if (parent != null && uniqueIds.add(parent.getEquipmentId())) {
            newParents.add(parent);
        }

        parents = newParents;
    }

}
