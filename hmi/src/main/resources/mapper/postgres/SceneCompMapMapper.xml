<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.SceneCompMapMapper">
    <select id="findByPageCategoryAndSceneId" resultType="com.siteweb.hmi.entity.SceneCompMap">
        select sceneCompMapId,type,sceneId,pageCategory from SceneCompMap
        where  sceneId in (-1, #{sceneId}) and pageCategory in(-1, #{pageCategory})
    </select>
</mapper>