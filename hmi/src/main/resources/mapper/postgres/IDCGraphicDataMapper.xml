<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.IDCGraphicDataMapper">
    <select id="getEventStatisticsByResourceStructure" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select  a.EventLevel AS Id, a.EventSeverity AS Name, count(*) AS Value from  tbl_activeevent   a
        inner join resourcestructure c on a.ResourceStructureId = c.ResourceStructureId
        inner join resourcestructure d on d.StructureTypeId  =#{structureType}  and c.LevelOfPath like concat(d.LevelOfPath,'%')  and d.ResourceStructureId  in
        <foreach collection="resourceStructureIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        where a.EndTime  is null
        group by a.EventLevel, a.EventSeverity ;
    </select>
    <select id="getEventStatisticsByEquipment" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select a.EventLevel AS Id, a.EventSeverity AS Name, count(*) AS Value from  tbl_activeevent a
        where a.EndTime  is null  and a.EquipmentId in
        <foreach collection="equipmentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by a.EventLevel, a.EventSeverity ;
    </select>

    <select id="getStructureEventStatisticsByDuration" resultType="com.siteweb.hmi.dto.EventStatisticsByDuration">
        SELECT baseEquipmentId,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &lt; 10 THEN 1 ELSE 0 END) AS count1,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &gt; 10 AND EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &lt; 30 THEN 1 ELSE 0 END) AS count2,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &gt; 30 AND EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &lt; 60 THEN 1 ELSE 0 END) AS count3,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &gt; 60 THEN 1 ELSE 0 END) AS count4
        FROM tbl_activeevent a
        INNER JOIN resourcestructure c ON a.ResourceStructureId = c.ResourceStructureId
        INNER JOIN resourcestructure d ON d.StructureTypeId = #{pageCategory} AND c.LevelOfPath LIKE d.LevelOfPath || '%' AND d.ResourceStructureId = #{objectId}
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.baseEquipmentId IS NOT NULL
        GROUP BY baseEquipmentId
        ORDER BY baseEquipmentId;
    </select>
    <select id="getEquipmentEventStatisticsByDuration"
            resultType="com.siteweb.hmi.dto.EventStatisticsByDuration">
        SELECT baseEquipmentId,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &lt; 10 THEN 1 ELSE 0 END) AS count1,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &gt; 10 AND EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &lt;30 THEN 1 ELSE 0 END) AS count2,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &gt; 30 AND EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &lt;60 THEN 1 ELSE 0 END) AS count3,
        SUM(CASE WHEN EXTRACT(EPOCH FROM (NOW() - StartTime)) / 60 &gt; 60 THEN 1 ELSE 0 END) AS count4
        FROM tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE EquipmentId = #{objectId} AND a.baseEquipmentId IS NOT NULL
        GROUP BY a.baseEquipmentId
        ORDER BY baseEquipmentId;
    </select>
    <select id="getAlarmEquipmentCategoryStatistics"
            resultType="com.siteweb.hmi.dto.AlarmEquipmentCategoryStatistics">
        select m.ResourceStructureId,m.ResourceStructureName, m.deviceCategoryId AS equipmentCategoryId, m.devicecategoryName AS equipmentCategoryName,m.totalCount, COALESCE(n.alarmCount,0) AS alarmCount
        from
        (select c.ResourceStructureId, c.ResourceStructureName,e.EquipmentBaseType deviceCategoryId, d.BaseEquipmentName devicecategoryName, count(a.EquipmentId) totalCount
        from tbl_equipment a inner join resourcestructure b on a.ResourceStructureId = b.ResourceStructureId
        inner join TBL_EquipmentTemplate e on a.EquipmentTemplateId = e.EquipmentTemplateId
        inner join tbl_equipmentbasetype d on e.EquipmentBaseType =d.BaseEquipmentId
        inner join resourcestructure c   on b.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        and c.StructureTypeId=3
        group by c.ResourceStructureId, c.ResourceStructureName,e.EquipmentBaseType,d.BaseEquipmentName) m
        LEFT JOIN (
        select c.ResourceStructureId, a.BaseEquipmentId AS deviceCategoryId, count(distinct a.equipmentId) AS alarmCount from tbl_activeevent a inner join resourcestructure b on a.ResourceStructureId = b.ResourceStructureId
        inner join resourcestructure c   on b.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        and c.StructureTypeId=3
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        where a.EndTime is null
        group by c.ResourceStructureId, a.BaseEquipmentId) n
        on m.ResourceStructureId = n.ResourceStructureId and m.DeviceCategoryId = n.DeviceCategoryId
    </select>
    <select id="getResourceStructureAlarmCountByGroup" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select c.ResourceStructureId as Id,c.ResourceStructureName as Name,count(b.SequenceId) as Value from tbl_activeevent b inner join resourcestructure a
        on a.ResourceStructureId = b.ResourceStructureId
        inner join resourcestructure c on  a.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        where b.EndTime is null and c.ResourceStructureId  in
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
        group by c.ResourceStructureId,c.ResourceStructureName
    </select>
    <select id="getResourceStructureAlarmLevelCountByGroup" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select a.EventLevel as Id,a.EventSeverity as Name,count(a.SequenceId) as Value from tbl_activeevent a
        where a.EndTime is null and a.ResourceStructureId  in
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
        group by a.EventLevel, a.EventSeverity ;
    </select>

</mapper>