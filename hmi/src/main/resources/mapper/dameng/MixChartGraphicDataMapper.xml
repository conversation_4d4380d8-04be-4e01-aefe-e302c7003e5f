<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.MixChartGraphicDataMapper">
    <select id="getAlarmDurationStatisticsDTOByHour" resultType="com.siteweb.hmi.dto.AlarmDurationStatisticsDTO">
        SELECT
        TO_CHAR(a.StartTime, 'YYYY-MM-DD HH24') || ':00:00' AS statisticsTime,
        a.EventLevel AS eventSeverityId,
        COUNT(a.sequenceId) AS alarmCount
        FROM
        tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE
        a.StartTime &gt; #{startTime} AND a.StartTime &lt;= #{endTime} AND a.EndTime IS NULL
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND a.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" item="item" open="(" separator="," close=")">
                #{item, jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(a.StartTime, 'YYYY-MM-DD HH24'),
        a.EventLevel
        UNION ALL
        SELECT
        TO_CHAR(h.StartTime, 'YYYY-MM-DD HH24') || ':00:00' AS statisticsTime,
        h.EventLevel AS eventSeverityId,
        COUNT(h.sequenceId) AS alarmCount
        FROM
        tbl_HistoryEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE
        h.StartTime &gt; #{startTime} AND h.StartTime &lt;= #{endTime}
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND h.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" item="item" open="(" separator="," close=")">
                #{item, jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(h.StartTime, 'YYYY-MM-DD HH24'),
        h.EventLevel
        order by statisticsTime
    </select>
    <select id="getAlarmDurationStatisticsDTOByDay" resultType="com.siteweb.hmi.dto.AlarmDurationStatisticsDTO">
        SELECT
        TO_CHAR(a.StartTime, 'YYYY-MM-DD') || ' 00:00:00' AS statisticsTime,
        a.EventLevel AS eventSeverityId,
        COUNT(a.sequenceId) AS alarmCount
        FROM
        tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE
        a.StartTime &gt; #{startTime} AND a.StartTime &lt;= #{endTime} AND a.EndTime IS NULL
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND a.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" item="item" open="(" separator="," close=")">
                #{item, jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(a.StartTime, 'YYYY-MM-DD'),
        a.EventLevel
        UNION ALL
        SELECT
        TO_CHAR(h.StartTime, 'YYYY-MM-DD') || ' 00:00:00' AS statisticsTime,
        h.EventLevel AS eventSeverityId,
        COUNT(h.sequenceId) AS alarmCount
        FROM
        tbl_HistoryEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE
        h.StartTime &gt; #{startTime} AND h.StartTime &lt;= #{endTime}
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND h.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" item="item" open="(" separator="," close=")">
                #{item, jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(h.StartTime, 'YYYY-MM-DD'),
        h.EventLevel
        order by statisticsTime
    </select>
    <select id="getAlarmDurationStatisticsDTOByTenMinute" resultType="com.siteweb.hmi.dto.AlarmDurationStatisticsDTO">
        SELECT
        TO_CHAR(TO_TIMESTAMP(TO_CHAR(a.StartTime, 'YYYY-MM-DD HH24') || ':' || FLOOR(TO_NUMBER(TO_CHAR(a.StartTime, 'MI')) / 10) * 10 || ':00', 'YYYY-MM-DD HH24:MI:SS') + INTERVAL '10' MINUTE, 'YYYY-MM-DD HH24:MI:SS') AS statisticsTime,
        a.EventLevel AS eventSeverityId,
        COUNT(a.sequenceId) AS alarmCount
        FROM
        tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE
        a.StartTime &gt; #{startTime} AND a.StartTime &lt;= #{endTime} AND a.EndTime IS NULL
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND a.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(TO_TIMESTAMP(TO_CHAR(a.StartTime, 'YYYY-MM-DD HH24') || ':' || FLOOR(TO_NUMBER(TO_CHAR(a.StartTime, 'MI')) / 10) * 10 || ':00', 'YYYY-MM-DD HH24:MI:SS') + INTERVAL '10' MINUTE, 'YYYY-MM-DD HH24:MI:SS'),
        a.EventLevel
        UNION ALL
        SELECT
        TO_CHAR(TO_TIMESTAMP(TO_CHAR(h.StartTime, 'YYYY-MM-DD HH24') || ':' || FLOOR(TO_NUMBER(TO_CHAR(h.StartTime, 'MI')) / 10) * 10 || ':00', 'YYYY-MM-DD HH24:MI:SS') + INTERVAL '10' MINUTE, 'YYYY-MM-DD HH24:MI:SS') AS statisticsTime,
        h.EventLevel AS eventSeverityId,
        COUNT(h.sequenceId) AS alarmCount
        FROM
        tbl_historyEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE
        h.StartTime &gt; #{startTime} AND h.StartTime &lt;= #{endTime}
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND h.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        TO_CHAR(TO_TIMESTAMP(TO_CHAR(h.StartTime, 'YYYY-MM-DD HH24') || ':' || FLOOR(TO_NUMBER(TO_CHAR(h.StartTime, 'MI')) / 10) * 10 || ':00', 'YYYY-MM-DD HH24:MI:SS') + INTERVAL '10' MINUTE, 'YYYY-MM-DD HH24:MI:SS'),
        h.EventLevel
        order by statisticsTime
    </select>
    <select id="getEquipmentStatistics" resultType="com.siteweb.hmi.dto.EquipmentStatisticsDTO">
        select b.EquipmentBaseType equipmentCategory, c.baseEquipmentName equipmentCategoryName, count(EquipmentId) equipmentCount, sum(case when a.connectstate = 1 then 1 else 0 end) OnlineCount
        from tbl_equipment  a inner join tbl_EquipmentTemplate b on a.EquipmentTemplateId = b.EquipmentTemplateId
        inner join tbl_equipmentBasetype c on b.EquipmentBaseType = c.baseEquipmentId
        where a.EquipmentCategory != 99
        <if test="eqIds.size > 0 and eqIds != null">
            <foreach collection="eqIds" item="item" open="and a.EquipmentId in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by b.EquipmentBaseType, c.baseEquipmentName
    </select>
    <select id="findStationCategoryCountByStationIds" resultType="com.siteweb.hmi.dto.StatisticsCommonResult">
        SELECT
        dic.ItemId AS id,
        dic.ItemValue AS name,
        COUNT(*) AS value
        FROM
        tbl_station station
        INNER JOIN TBL_DataItem dic ON
        dic.EntryId = 71
        AND station.StationCategory = dic.ItemId
        WHERE station.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        GROUP BY
        dic.ItemId,
        dic.ItemValue
        ORDER BY dic.ItemId
    </select>
    <select id="getStationEventTopByStationIds" resultType="com.siteweb.hmi.dto.StatisticsCommonResult">
        SELECT result.StationId AS id,
        result.StationName AS name,
        COUNT(*) AS value
        FROM (
        SELECT a.StationId,
        a.StationName
        FROM TBL_ActiveEvent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.StationId > 0
        AND a.StartTime &gt;= SYSDATE - INTERVAL '7' DAY
        AND a.StartTime &lt;= SYSDATE
        AND a.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        UNION ALL
        SELECT h.StationId,
        h.StationName
        FROM TBL_HistoryEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE h.StationId > 0
        AND h.StartTime &gt;= SYSDATE - INTERVAL '7' DAY
        AND h.StartTime &lt;= SYSDATE
        AND h.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ) result
        GROUP BY result.StationId,
        result.StationName
        ORDER BY value DESC
        LIMIT 5
    </select>
</mapper>