<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.GraphicPageMapper">
    <insert id="batchInsert">
        INSERT INTO graphicpage(type, groupid, pagecategory, baseequipmentid, objectid, name, appendname, templateid,
        data, style, children, description, routertype, crumbsroutertype, pagecanfullscreen, sceneid,
        isdefault,updatetime) VALUES
        <foreach collection="graphicPages" item="graphicPage" separator=",">
            (#{graphicPage.type},#{graphicPage.groupId},#{graphicPage.pageCategory},#{graphicPage.baseEquipmentId},#{graphicPage.objectId},#{graphicPage.name},#{graphicPage.appendName},#{graphicPage.templateId},
            #{graphicPage.data},#{graphicPage.style},#{graphicPage.children},#{graphicPage.description},#{graphicPage.routerType},#{graphicPage.crumbsRouterType},#{graphicPage.pageCanFullScreen},#{graphicPage.sceneId},
            #{graphicPage.isDefault},#{graphicPage.updateTime})
        </foreach>
    </insert>
    <update id="batchUpdateGraphicDefaultState">
        UPDATE graphicpage set isDefault = 0 WHERE Id IN
        <foreach collection="graphicPageIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="batchUpdateTime">
        UPDATE graphicpage set UpdateTime = #{updateTime} WHERE Id IN
        <foreach collection="graphicPageIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="findAllByObjectIdAndPageCategory" resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault,UpdateTime
        FROM graphicpage where ObjectId = #{objectId} and PageCategory = #{pageCategory};
    </select>
    <select id="findAllByObjectIdAndPageCategoryAndRouterTypeNotLike"
            resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault
        FROM graphicpage where ObjectId = #{objectId} and PageCategory = #{pageCategory} and routerType not like '%#{routerType}%';
    </select>
    <select id="findFirstByObjectIdAndPageCategory" resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault
        FROM graphicpage where ObjectId = #{objectId} and PageCategory = #{pageCategory} limit 1;
    </select>
    <select id="findAllByObjectId" resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault
        FROM graphicpage where ObjectId = #{objectId} ;
    </select>
    <select id="findFirstByObjectIdAndTemplateId" resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault
        FROM graphicpage where ObjectId = #{objectId}  and TemplateId = #{templateId}  limit 1;
    </select>
    <select id="existsByObjectIdAndTemplateId" resultType="java.lang.Boolean"></select>
    <select id="findAllByPageCategory" resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault
        FROM graphicpage where PageCategory = #{pageCategory} ;
    </select>
    <select id="getGraphicPageByScene" resultType="com.siteweb.hmi.entity.GraphicPage">
        SELECT Id, "Type", GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, "Data", "Style", Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId,IsDefault
        FROM graphicpage where SceneId = #{pageCategory} ;</select>
    <select id="findBatchApplyList" resultType="com.siteweb.hmi.dto.BatchApplyDTO">
        SELECT DISTINCT equipment.EquipmentId as eqId,equipment.EquipmentName as eqName,fuct_GetDevicePosition(equipment.ResourceStructureId) as fullPath
        FROM tbl_equipment equipment INNER JOIN graphicpage graphic ON equipment.EquipmentId = graphic.ObjectId AND graphic.PageCategory = 7
        WHERE graphic.BaseEquipmentId = #{baseEquipmentId}
        <if test="updateTime != null">
            AND graphic.UpdateTime = #{updateTime}
        </if>
    </select>
</mapper>