<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.GraphicTemplateMapper">
    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="GraphicTemplateId" keyProperty="graphicTemplateId">
        INSERT INTO GraphicTemplate
        (GraphicTemplateName, GraphicTemplateTag, GraphicTemplateCompType, GraphicTemplateTypeId, GraphicTemplateType,
        GraphicTemplateTypeName, GraphicTemplateCover, GraphicTemplateConfig,CreateTime)
        VALUES
        <foreach collection="graphicTemplateList" item="item" separator=",">
            (#{item.graphicTemplateName},#{item.graphicTemplateTag},#{item.graphicTemplateCompType},#{item.graphicTemplateTypeId},#{item.graphicTemplateType},
            #{item.graphicTemplateTypeName},#{item.graphicTemplateCover},#{item.graphicTemplateConfig},now())
        </foreach>
    </insert>
    <select id="findByConditional" resultType="com.siteweb.hmi.entity.GraphicTemplate">
        SELECT
        graphictemplateid,
        graphictemplatename,
        graphictemplatetag,
        graphictemplatecomptype,
        graphictemplatetypeid,
        graphictemplatetype,
        graphictemplatetypename,
        graphictemplatecover,
        graphictemplateconfig
        FROM graphictemplate
        <where>
            <if test="keywords != null and keywords != ''">
                AND graphictemplatename LIKE CONCAT('%',#{keywords},'%')
            </if>
            <if test="graphicTemplateCompType != null and graphicTemplateCompType != 1">
                AND graphictemplatecomptype = #{graphicTemplateCompType}
            </if>
            <if test="graphicTemplateTag != null and graphicTemplateTag != ''">
                AND graphicTemplateTag = #{graphicTemplateTag}
            </if>
        </where>
    </select>
</mapper>