<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.MixChartGraphicDataMapper">
    <select id="getAlarmDurationStatisticsDTOByHour"
            resultType="com.siteweb.hmi.dto.AlarmDurationStatisticsDTO">
        select DATE_FORMAT(a.StartTime,'%Y-%m-%d %H:00:00') statisticsTime,a.EventLevel eventSeverityId,count(a.sequenceId) alarmCount from tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.StartTime &gt;#{startTime} and a.StartTime &lt;=#{endTime} and a.EndTime is null
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND a.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" separator="," open="(" close=")" item="item">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        group by statisticsTime,a.EventLevel
        UNION ALL
        select DATE_FORMAT(h.StartTime,'%Y-%m-%d %H:00:00') statisticsTime,h.EventLevel eventSeverityId,count(h.sequenceId) alarmCount from tbl_HistoryEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE h.StartTime &gt;#{startTime} and h.StartTime &lt;=#{endTime}
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND h.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" separator="," open="(" close=")" item="item">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        group by statisticsTime,h.EventLevel;
    </select>
    <select id="getAlarmDurationStatisticsDTOByDay"
            resultType="com.siteweb.hmi.dto.AlarmDurationStatisticsDTO">
        select DATE_FORMAT(a.StartTime,'%Y-%m-%d 00:00:00') statisticsTime,a.EventLevel eventSeverityId,count(a.sequenceId) alarmCount from tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.StartTime &gt;#{startTime} and a.StartTime &lt;=#{endTime} and a.EndTime is null
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND a.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" separator="," open="(" close=")" item="item">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        group by statisticsTime,a.EventLevel
        UNION ALL
        select DATE_FORMAT(h.StartTime,'%Y-%m-%d 00:00:00') statisticsTime,h.EventLevel eventSeverityId,count(h.sequenceId) alarmCount from tbl_HistoryEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE h.StartTime &gt;#{startTime} and h.StartTime &lt;=#{endTime}
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND h.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by statisticsTime,h.EventLevel;
    </select>
    <select id="getAlarmDurationStatisticsDTOByTenMinute"
            resultType="com.siteweb.hmi.dto.AlarmDurationStatisticsDTO">
        SELECT DATE_ADD(CONCAT(DATE_FORMAT(a.StartTime,'%Y-%m-%d %H:'),FLOOR(MINUTE(a.StartTime)/10),"0:00"),INTERVAL 10 MINUTE) statisticsTime ,a.EventLevel eventSeverityId, COUNT(a.sequenceId) alarmCount
        FROM tbl_activeevent a
        WHERE a.StartTime &gt;#{startTime} and a.StartTime &lt;=#{endTime} and a.EndTime is null
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND a.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY DATE_ADD(CONCAT(DATE_FORMAT(a.StartTime,'%Y-%m-%d %H:'),FLOOR(MINUTE(a.StartTime)/10),"0:00"),INTERVAL 10 MINUTE), a.EventLevel
        UNION ALL
        SELECT DATE_ADD(CONCAT(DATE_FORMAT(h.StartTime,'%Y-%m-%d %H:'),FLOOR(MINUTE(h.StartTime)/10),"0:00"),INTERVAL 10 MINUTE) statisticsTime ,h.EventLevel eventSeverityId, COUNT(h.sequenceId) alarmCount
        FROM tbl_historyEvent h
        WHERE h.StartTime >#{startTime} and h.StartTime &lt;=#{endTime}
        <if test="resourceStructureIdList != null and resourceStructureIdList.size > 0">
            AND h.ResourceStructureId IN
            <foreach collection="resourceStructureIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY DATE_ADD(CONCAT(DATE_FORMAT(h.StartTime,'%Y-%m-%d %H:'),FLOOR(MINUTE(h.StartTime)/10),"0:00"),INTERVAL 10 MINUTE), h.EventLevel
    </select>
    <select id="getEquipmentStatistics" resultType="com.siteweb.hmi.dto.EquipmentStatisticsDTO">
        select b.EquipmentBaseType equipmentCategory, c.baseEquipmentName equipmentCategoryName, count(EquipmentId) equipmentCount, sum(case when a.connectstate = 1 then 1 else 0 end) OnlineCount
        from tbl_equipment  a inner join tbl_EquipmentTemplate b on a.EquipmentTemplateId = b.EquipmentTemplateId
        inner join tbl_equipmentBasetype c on b.EquipmentBaseType = c.baseEquipmentId
        where a.EquipmentCategory != 99
        <if test="eqIds.size > 0 and eqIds != null">
            <foreach collection="eqIds" item="item" open="and a.EquipmentId in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by b.EquipmentBaseType, c.baseEquipmentName
    </select>
    <select id="findStationCategoryCountByStationIds" resultType="com.siteweb.hmi.dto.StatisticsCommonResult">
        SELECT
        dic.ItemId AS id,
        dic.ItemValue AS name,
        COUNT(*) AS value
        FROM
        tbl_station station
        INNER JOIN TBL_DataItem dic ON
        dic.EntryId = 71
        AND station.StationCategory = dic.ItemId
        WHERE station.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        GROUP BY
        dic.ItemId,
        dic.ItemValue
        ORDER BY dic.ItemId
    </select>
    <select id="getStationEventTopByStationIds" resultType="com.siteweb.hmi.dto.StatisticsCommonResult">
        SELECT result.StationId as id,
        result.StationName as name,
        COUNT(*) as value
        FROM (
        SELECT a.StationId,
        a.StationName
        FROM TBL_ActiveEvent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.StationId > 0
        AND a.StartTime &gt;= DATE_ADD(CURDATE(), INTERVAL -7 DAY)
        AND a.StartTime &lt;= CURDATE()
        AND a.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        UNION ALL
        SELECT h.StationId,
        h.StationName
        FROM TBL_HistoryEvent h
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON h.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE h.StationId > 0
        AND h.StartTime &gt;= DATE_ADD(CURDATE(), INTERVAL -7 DAY)
        AND h.StartTime &lt;= CURDATE()
        AND h.StationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        ) result
        GROUP BY result.StationId,
        result.StationName
        ORDER BY value DESC
        LIMIT 5
    </select>
</mapper>