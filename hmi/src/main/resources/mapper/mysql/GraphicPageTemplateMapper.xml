<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.GraphicPageTemplateMapper">
    <select id="findAllByGroupIdLessThan" resultType="com.siteweb.hmi.entity.GraphicPageTemplate"></select>
    <select id="findAllByBaseEquipmentIdAndInternal" resultType="com.siteweb.hmi.entity.GraphicPageTemplate">
        SELECT Id, Type, GroupId, PageCategory, BaseEquipmentId, Internal, Name, AppendName, Data, Style, Children, TemplateCategory, Description
        FROM graphicpagetemplate where BaseEquipmentId = #{baseEquipmentId} and Internal =#{isInternal}
    </select>
    <select id="findFirstByPageCategoryAndInternal" resultType="com.siteweb.hmi.entity.GraphicPageTemplate">
        SELECT Id, Type, GroupId, PageCategory, BaseEquipmentId, Internal, Name, AppendName, Data, Style, Children, TemplateCategory, Description
        from GraphicPageTemplate where BaseEquipmentId = #{baseEquipmentId}
    </select>
    <select id="findAllByBaseEquipmentId" resultType="com.siteweb.hmi.entity.GraphicPageTemplate">
        SELECT Id, Type, GroupId, PageCategory, BaseEquipmentId, Internal, Name, AppendName, Data, Style, Children, TemplateCategory, Description
        from GraphicPageTemplate where BaseEquipmentId = #{baseEquipmentId}
    </select>
</mapper>