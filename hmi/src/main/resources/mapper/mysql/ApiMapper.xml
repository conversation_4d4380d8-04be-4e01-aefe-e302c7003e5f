<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.ApiMapper">

    <select id="countStationOnlineStatus" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select a.ConnectState as id, count(*) as value from tbl_station a
        group by a.ConnectState
    </select>

    <select id="statoinStatistics" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select a.ItemId as id, a.ItemValue as name, count(stationId) as value from tbl_dataitem a
            inner join tbl_Station b on a.ItemId = b.stationCategory
        where entryId = 71
        <if test="stations.size > 0 and stations != null">
            <foreach collection="stations" separator="," open="and b.StationId in (" close=")" item="item">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        group by a.ItemId, a.ItemValue
    </select>

    <select id="alarmtrends" resultType="com.siteweb.hmi.dto.AlarmtrendsDTO">
        select DATE_FORMAT(StartTime,'%Y-%m-%d 00:00:00') as statisticsTime,EventLevel as eventSeverityId,count(sequenceId) as alarmCount from tbl_activeevent b
        WHERE StartTime > #{startTime,jdbcType=TIMESTAMP} and StartTime &lt; #{endTime,jdbcType=TIMESTAMP} and EndTime is null
        <if test="stationId != null and stationId != ''">
            and  b.StationId = ifnull(#{stationId,jdbcType=INTEGER}, b.StationId)
        </if>
        group by statisticsTime,EventLevel
        UNION ALL
        select DATE_FORMAT(StartTime,'%Y-%m-%d 00:00:00') statisticsTime,EventLevel as eventSeverityId,count(sequenceId) alarmCount from tbl_HistoryEvent b
        WHERE StartTime > #{startTime,jdbcType=TIMESTAMP} and StartTime &lt; #{endTime,jdbcType=TIMESTAMP}
        <if test="stationId != null and stationId != ''">
            and  b.StationId = ifnull(#{stationId,jdbcType=INTEGER}, b.StationId)
        </if>
        group by statisticsTime,EventLevel ;
    </select>

    <select id="alarmStatisticsByResourceType" resultType="com.siteweb.hmi.dto.StatisticsResult">
        SELECT c.ResourceStructureId   AS id,
               c.ResourceStructureName AS name,
               COUNT(b.SequenceId)     AS value
        FROM tbl_activeevent b
                 INNER JOIN resourcestructure a ON a.ResourceStructureId = b.ResourceStructureId AND b.EndTime is null
                 INNER JOIN resourcestructure c ON c.StructureTypeId = #{resourceType} AND a.LevelOfPath LIKE CONCAT(c.LevelOfPath, '%')
                <if test="filterByEventLevel">
                    INNER JOIN tbl_dataitem tdi
                    ON b.EventLevel = tdi.ExtendField4
                    AND tdi.EntryId = 23
                    AND tdi.Enable = 1
                </if>
        GROUP BY c.ResourceStructureId, c.ResourceStructureName
    </select>
    <select id="alarmStatisticsByResourceStructureId" resultType="com.siteweb.hmi.dto.StatisticsResult">
        SELECT f.BaseEquipmentId AS id,
               f.BaseEquipmentName AS name,
               COUNT(a.SequenceId) AS value
        FROM tbl_activeevent a
                 INNER JOIN tbl_equipment b ON a.EquipmentId = b.EquipmentId AND a.EndTime is null
                 INNER JOIN resourcestructure c ON b.ResourceStructureId = c.ResourceStructureId
                 INNER JOIN resourcestructure d ON d.StructureTypeId = 3 AND c.LevelOfPath LIKE CONCAT(d.LevelOfPath, '%')
                 INNER JOIN tbl_equipmenttemplate e ON e.EquipmentTemplateId = b.EquipmentTemplateid
                 INNER JOIN tbl_equipmentbasetype f ON f.BaseEquipmentId = e.EquipmentBaseType
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE d.ResourceStructureId = #{resourceStructureId}
        GROUP BY f.BaseEquipmentId,
                 f.BaseEquipmentName
    </select>

    <select id="configcommonobject" resultType="com.siteweb.hmi.dto.CommonObjectDTO">
        select a.ItemId as id, a.ItemValue as name, a.ItemValue as initialName from tbl_dataitem a where EntryId = #{type,jdbcType=INTEGER} and exists(select 1 from tbl_equipment b where a.ItemId = b.equipmentCategory);
    </select>

    <select id="structureavailability" resultType="com.siteweb.hmi.dto.StructureAvailabilityDTO">
        select  a.ResourceStructureId as id, a.ResourceStructureName, c.StationId, c.StationName, count(c.StationId) totalCount, sum(case when c.ConnectState = 1 then 1 else 0 end) onlineCount
        from resourcestructure a
        inner join resourcestructure b on b.ParentResourceStructureId = a.ResourceStructureId
        AND  a.StructureTypeId =  103 and b.StructureTypeId = 104
        INNER JOIN TBL_Station c on b.OriginId = c.StationId
        and c.StationId in
        <foreach collection="stationIds" separator="," open=" (" close=")" item="item">
            #{item,jdbcType=INTEGER}
        </foreach>
        group by a.ResourceStructureId, a.ResourceStructureName, b.ResourceStructureId, b.ResourceStructureName
    </select>
    <select id="alarmStatistics" resultType="com.siteweb.hmi.dto.StatisticsResult">
        SELECT c.ResourceStructureId   AS id,
               b.EventSeverity     AS name,
               COUNT(b.SequenceId) AS value
        FROM tbl_activeevent b
                 INNER JOIN resourcestructure a ON a.ResourceStructureId = b.ResourceStructureId
                 INNER JOIN resourcestructure c ON c.StructureTypeId = #{resourceTypeId} AND a.LevelOfPath LIKE CONCAT(c.LevelOfPath, '%')
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON b.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE c.ResourceStructureId = #{resourceStructureId}
        GROUP BY c.ResourceStructureId, b.EventSeverity
        ORDER BY b.EventSeverity
    </select>
    <select id="categoryStatisticsByDuration" resultType="com.siteweb.hmi.dto.EventCategoryStatisticsByDuration">
        SELECT
        a.EquipmentCategory,
        td.ItemValue AS equipmentCategoryName,
        SUM(CASE WHEN TIMESTAMPDIFF(MINUTE, StartTime, NOW()) &lt; 10 THEN 1 ELSE 0 END) AS count1,
        SUM(CASE WHEN TIMESTAMPDIFF(MINUTE, StartTime, NOW()) BETWEEN 10 AND 29 THEN 1 ELSE 0 END) AS count2,
        SUM(CASE WHEN TIMESTAMPDIFF(MINUTE, StartTime, NOW()) BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS count3,
        SUM(CASE WHEN TIMESTAMPDIFF(MINUTE, StartTime, NOW()) &gt;= 60 THEN 1 ELSE 0 END) AS count4,
        COUNT(*) AS sum
        FROM tbl_activeevent a
        INNER JOIN resourcestructure c ON a.ResourceStructureId = c.ResourceStructureId
        INNER JOIN resourcestructure d ON c.LevelOfPath LIKE CONCAT(d.LevelOfPath, '%')
        AND d.ResourceStructureId = #{resourceStructureId}
        LEFT JOIN tbl_dataitem td ON td.EntryId = 7 AND td.ItemId = a.EquipmentCategory
        WHERE a.EquipmentCategory IS NOT NULL
        AND td.ItemValue IS NOT NULL
        GROUP BY a.EquipmentCategory, td.ItemValue
        ORDER BY sum DESC
    </select>

</mapper>