<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.hmi.mapper.IDCGraphicDataMapper">
    <select id="getEventStatisticsByResourceStructure" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select  a.EventLevel  Id, a.EventSeverity  Name, count(*) Value from  tbl_activeevent   a
        inner join resourcestructure c on a.ResourceStructureId = c.ResourceStructureId
        inner join resourcestructure d on d.StructureTypeId  =#{structureType}  and c.LevelOfPath like concat(d.LevelOfPath,'%')  and d.ResourceStructureId  in
        <foreach collection="resourceStructureIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        where a.EndTime  is null
        group by a.EventLevel, a.EventSeverity ;
    </select>
    <select id="getEventStatisticsByEquipment" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select a.EventLevel  Id, a.EventSeverity  Name, count(*) Value from  tbl_activeevent a
        where a.EndTime  is null  and a.EquipmentId in
        <foreach collection="equipmentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by a.EventLevel, a.EventSeverity ;
    </select>

    <select id="getStructureEventStatisticsByDuration" resultType="com.siteweb.hmi.dto.EventStatisticsByDuration">
        select baseEquipmentId,
        sum(Case when timestampdiff(MINUTE,StartTime,now()) &lt; 10 then 1 else 0 end ) count1,
        sum(Case when timestampdiff(MINUTE,StartTime,now()) &gt; 10 and timestampdiff(MINUTE,StartTime,now()) &lt; 30  then 1 else 0 end )  count2,
        sum(Case when timestampdiff(MINUTE,StartTime,now()) &gt; 30 and timestampdiff(MINUTE,StartTime,now()) &lt; 60  then 1 else 0 end )  count3,
        sum(Case  when timestampdiff(MINUTE,StartTime,now()) &gt; 60  then 1 else 0 end )  count4
        from tbl_activeevent a
        inner join resourcestructure c on a.ResourceStructureId = c.ResourceStructureId
        inner join resourcestructure d on d.StructureTypeId =#{pageCategory}  and c.LevelOfPath like concat(d.LevelOfPath,'%')  and d.ResourceStructureId = #{objectId}
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        where a.baseEquipmentId is not null
        group by baseEquipmentId
        order by baseEquipmentId
    </select>
    <select id="getEquipmentEventStatisticsByDuration"
            resultType="com.siteweb.hmi.dto.EventStatisticsByDuration">
        select baseEquipmentId ,
        sum(Case when timestampdiff(MINUTE,StartTime,now()) &lt; 10 then 1 else 0 end ) count1,
        sum(Case when timestampdiff(MINUTE,StartTime,now()) &gt; 10 and timestampdiff(MINUTE,StartTime,now()) &lt; 30  then 1 else 0 end )  count2,
        sum(Case when timestampdiff(MINUTE,StartTime,now()) &gt; 30 and timestampdiff(MINUTE,StartTime,now()) &lt; 60  then 1 else 0 end )  count3,
        sum(Case  when timestampdiff(MINUTE,StartTime,now()) &gt; 60  then 1 else 0 end )  count4
        from tbl_activeevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        where EquipmentId = #{objectId} and a.baseEquipmentId is not null
        group by a.baseEquipmentId
        order by baseEquipmentId
    </select>
    <select id="getAlarmEquipmentCategoryStatistics"
            resultType="com.siteweb.hmi.dto.AlarmEquipmentCategoryStatistics">
        select m.ResourceStructureId,m.ResourceStructureName, m.deviceCategoryId equipmentCategoryId, m.devicecategoryName equipmentCategoryName,m.totalCount, ifNULL(n.alarmCount,0) alarmCount
        from
        (select c.ResourceStructureId, c.ResourceStructureName,e.EquipmentBaseType deviceCategoryId, d.BaseEquipmentName devicecategoryName, count(a.EquipmentId) totalCount
        from tbl_equipment a inner join resourcestructure b on a.ResourceStructureId = b.ResourceStructureId
        inner join TBL_EquipmentTemplate e on a.EquipmentTemplateId = e.EquipmentTemplateId
        inner join tbl_equipmentbasetype d on e.EquipmentBaseType =d.BaseEquipmentId
        inner join resourcestructure c   on b.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        and c.StructureTypeId=3
        group by c.ResourceStructureId, c.ResourceStructureName,e.EquipmentBaseType) m
        LEFT JOIN (
        select c.ResourceStructureId, a.BaseEquipmentId deviceCategoryId, count(distinct a.equipmentId) alarmCount from tbl_activeevent a inner join resourcestructure b on a.ResourceStructureId = b.ResourceStructureId
        inner join resourcestructure c   on b.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        and c.StructureTypeId=3
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        where a.EndTime is null
        group by c.ResourceStructureId, a.BaseEquipmentId) n
        on m.ResourceStructureId = n.ResourceStructureId and m.DeviceCategoryId = n.DeviceCategoryId
    </select>
    <select id="getResourceStructureAlarmCountByGroup" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select c.ResourceStructureId as Id ,c.ResourceStructureName as Name,count(b.SequenceId) as Value from tbl_activeevent b inner join resourcestructure a
        on a.ResourceStructureId = b.ResourceStructureId
        inner join resourcestructure c on  a.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        where b.EndTime is null and c.ResourceStructureId  in
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
        group by c.ResourceStructureId,c.ResourceStructureName
    </select>
    <select id="getResourceStructureAlarmLevelCountByGroup" resultType="com.siteweb.hmi.dto.StatisticsResult">
        select a.EventLevel as Id ,a.EventSeverity as Name,count(a.SequenceId) as Value from tbl_activeevent a
        where a.EndTime is null and a.ResourceStructureId  in
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
        group by a.EventLevel, a.EventSeverity ;
    </select>

</mapper>