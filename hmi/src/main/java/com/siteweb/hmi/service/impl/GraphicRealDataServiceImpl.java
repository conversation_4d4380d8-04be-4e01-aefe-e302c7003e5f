package com.siteweb.hmi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.hmi.dto.EquipmentGraphicData;
import com.siteweb.hmi.dto.SignalReduceDTO;
import com.siteweb.hmi.service.GraphicRealDataService;
import com.siteweb.hmi.util.GraphicDataUtil;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.SignalMeanings;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.model.HistorySignalPointValue;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import com.siteweb.monitoring.vo.ActiveSignalRequestBySignalId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("GraphicRealDataService")
public class GraphicRealDataServiceImpl implements GraphicRealDataService {
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    RealTimeSignalManager realTimeSignalManager;
    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    GraphicDataUtil graphicDataUtil;
    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    EquipmentService equipmentService;
    @Autowired
    HistorySignalManager historySignalManager;
    @Autowired
    ConfigSignalManager configSignalManager;
    @Autowired
    SignalSubscribeManager signalSubscribeManager;


    @Override
    public EquipmentGraphicData getEquipmentGraphicDataByDeviceId(Integer equipmentId) {

        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if(equipment == null){
            return  null;
        }
        List<SimpleActiveSignal> simpleActiveSignalList =  activeSignalManager.getActiveSignalsByEquipmentId(equipmentId);
        simpleActiveSignalList.forEach(simpleActiveSignal -> {
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, simpleActiveSignal.getSignalId());
            List<SignalMeanings> signalMeanings = Optional.ofNullable(configSignalItem).map(ConfigSignalItem::getMeaningsList).orElse(null);
            if (CollUtil.isNotEmpty(signalMeanings)) {
                simpleActiveSignal.setSignalMeanings(signalMeanings.stream().filter(meaning -> Objects.nonNull(meaning.getId()))
                        .map(meaning -> String.format("%s-%s", meaning.getStateValue(), meaning.getMeanings())).collect(Collectors.joining("; ")));
            }
        });
        EquipmentGraphicData result = new EquipmentGraphicData(equipment, simpleActiveSignalList);
        EquipmentState equipmentState = equipmentStateManager.getEquipmentStateById(equipmentId);
        if(equipmentState != null){
            result.setOnlineState(equipmentState.getOnlineState());
        }
        // 设备告警状态
        result.setAlarmState(equipmentStateManager.getEquipmentAlarmStateById(equipment.getEquipmentId()));
        signalSubscribeManager.sendSignalSubscribe(equipmentId);
        return result;
    }

    @Override
    public List<EquipmentGraphicData> findEquipmentGraphicDataByEquipmentIds(String equipmentIds) {
        if (CharSequenceUtil.isBlank(equipmentIds)) {
            return Collections.emptyList();
        }
        //获取设备信号
        List<Integer> equipmentIdList = StringUtils.getIntegerListByString(equipmentIds);
        return findEquipmentGraphicData(equipmentIdList);
    }

    @Override
    public List<EquipmentGraphicData> getEquipmentGraphicDataByEquipmentIds(List<Integer> equipmentList) {
        if (CollUtil.isEmpty(equipmentList)) {
            return Collections.emptyList();
        }
        return findEquipmentGraphicData(equipmentList);
    }

    /**
     * 通过设备ids获取信号
     * @param equipmentList 设备id集合
     * @return {@link List}<{@link EquipmentGraphicData}>
     */
    private List<EquipmentGraphicData> findEquipmentGraphicData(List<Integer> equipmentList) {
        //获取设备信号
        List<ActiveSignalRequestBySignalId> activeSignalRequestBySignalIds = equipmentList.stream()
                                                                                          .map(ActiveSignalRequestBySignalId::new)
                                                                                          .toList();
        //获取设备组实时信号
        return this.findEquipmentGraphicDataByRequest(activeSignalRequestBySignalIds);
    }

    @Override
    public RealTimeSignalItem findRealTimeSignal(Integer equipmentId, Integer signalId) {
        return realTimeSignalManager.getRealTimeSignalBySignalId(equipmentId, signalId);
    }


    @Override
    public List<EquipmentGraphicData> findEquipmentSignalByEquipmentIdAndSignalIds(List<ActiveSignalRequestBySignalId> requestBySignalIds) {
        return this.findEquipmentGraphicDataByRequest(requestBySignalIds);
    }

    @Override
    public List<EquipmentGraphicData> findEquipmentSignalByEquipmentIdAndBaseTypeIds(List<ActiveSignalRequestByBaseTypeId> requestByBaseTypeIds) {
        if (CollUtil.isEmpty(requestByBaseTypeIds)) {
            return Collections.emptyList();
        }
        List<EquipmentActiveSignal> equipmentActiveSignalByBaseTypeId = activeSignalManager.getEquipmentActiveSignalByBaseTypeId(requestByBaseTypeIds);
        //简单设备信号信息转通用统一的设备信号响应体
        return graphicDataUtil.activeSignalConvertEquipmentGraphicData(equipmentActiveSignalByBaseTypeId);
    }

    @Override
    public List<EquipmentAlarmStateDTO> findEquipmentAlarmStateByGroup(List<Integer> dataIds,Integer userId) {
        List<EquipmentAlarmStateDTO> result =  activeEventManager.findEquipmentAlarmState(dataIds);

        Map<Integer, EquipmentDTO> dtos = equipmentService.findEquipmentDTOsByUserId(userId).stream().collect(
                Collectors.toMap(EquipmentDTO::getEqId, p -> p));

        for(EquipmentAlarmStateDTO equipmentAlarmStateDTO:result){
            if(!dtos.containsKey(equipmentAlarmStateDTO.getEquipmentId())){
                equipmentAlarmStateDTO.setFilter(false);
            }
            equipmentAlarmStateDTO.setOnlineState(equipmentStateManager.getEquipmentOnlineStateById(equipmentAlarmStateDTO.getEquipmentId()));
        }
        return  result;
    }

    @Override
    public Map<Integer,List<SignalReduceDTO>> findEquipmentSignalsReduce(List<ActiveSignalRequestBySignalId> activeSignalRequestList) {
        if (CollUtil.isEmpty(activeSignalRequestList)) {
            return Collections.emptyMap();
        }
        Map<Integer,List<SignalReduceDTO>> result = new LinkedHashMap<>(activeSignalRequestList.size());
        for (ActiveSignalRequestBySignalId activeSignalRequest : activeSignalRequestList) {
            List<SignalReduceDTO> equipmentSignalReduces = findEquipmentSignalReduce(activeSignalRequest);
            result.put(activeSignalRequest.getEquipmentId(), equipmentSignalReduces);
        }
        return result;
    }


    private List<SignalReduceDTO> findEquipmentSignalReduce(ActiveSignalRequestBySignalId activeSignalRequest) {
        List<Integer> signalIds = activeSignalRequest.getSignalIds();
        Integer equipmentId = activeSignalRequest.getEquipmentId();
        //获取实时信号
        List<SimpleActiveSignal> activeSignalsByEquipmentIdAndSignalId = activeSignalManager.getActiveSignalsByEquipmentIdAndSignalId(equipmentId, signalIds);
        //获取最近一天的历史信号
        Map<String, List<HistorySignalPointValue>> historySignalReduceMap = getLatestHistoricalSignal(equipmentId, signalIds);
        // 假设 historySignalReduce 已经被填充数据
        // 将其按 SignalId 分组，并计算最大值和最小值
        List<SignalReduceDTO> signalReduceDTOList = new ArrayList<>(signalIds.size());
        for (SimpleActiveSignal simpleActiveSignal : activeSignalsByEquipmentIdAndSignalId) {
            SignalReduceDTO signalReduceDTO = new SignalReduceDTO();
            String signalKey = equipmentId + "." + simpleActiveSignal.getSignalId();
            signalReduceDTO.setSignalId(signalKey);
            signalReduceDTO.setCurrentValue(simpleActiveSignal.getCurrentValue());
            List<HistorySignalPointValue> historySignalPointValues = historySignalReduceMap.getOrDefault(signalKey, Collections.emptyList());
            //设置该信号最近24小时的最大值 最小值
            historySignalPointValues.forEach(e -> {
                if (CharSequenceUtil.isBlank(signalReduceDTO.getMaxValue())) {
                    signalReduceDTO.setMaxValue(simpleActiveSignal.getOriginalValue());
                }
                if (CharSequenceUtil.isBlank(signalReduceDTO.getMinValue())) {
                    signalReduceDTO.setMinValue(simpleActiveSignal.getOriginalValue());
                }
                double pointValue = Double.parseDouble(e.getPointValue());
                if (Double.parseDouble(signalReduceDTO.getMaxValue()) < pointValue) {
                    signalReduceDTO.setMaxValue(String.valueOf(pointValue));
                }
                if ( Double.parseDouble(signalReduceDTO.getMinValue()) > pointValue) {
                    signalReduceDTO.setMinValue(String.valueOf(pointValue));
                }
            });
            signalReduceDTOList.add(signalReduceDTO);
        }
        //将最终值转换成信号含义
        signalMeaningsConvert(equipmentId, signalReduceDTOList);
        return signalReduceDTOList;
    }

    /**
     * 获取最近一天的历史信号
     * @param equipmentId 设备id
     * @param signalIds 信号ids
     * @return {@link Map }<{@link String }, {@link List }<{@link HistorySignalPointValue }>>
     */
    private Map<String, List<HistorySignalPointValue>> getLatestHistoricalSignal(Integer equipmentId,List<Integer> signalIds) {
        Date endTime = new Date();
        DateTime startTime = DateUtil.offsetDay(endTime, -1);
        return historySignalManager.getHistorySignaltMap(startTime, endTime, signalIds.stream().map(signalId -> equipmentId + "." + signalId).toList());
    }

    private void signalMeaningsConvert(Integer equipmentId, List<SignalReduceDTO> signalReduceDTOList) {
        for (SignalReduceDTO signalReduceDTO : signalReduceDTOList) {
            String signalId = signalReduceDTO.getSignalId().split("\\.")[1];
            ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(equipmentId, Integer.valueOf(signalId));
            signalReduceDTO.setSignalId(signalId);
            if (CharSequenceUtil.isBlank(signalReduceDTO.getMaxValue()) || CharSequenceUtil.isBlank(signalReduceDTO.getMinValue())) {
                signalReduceDTO.setMaxValue(signalReduceDTO.getCurrentValue());
                signalReduceDTO.setMinValue(signalReduceDTO.getCurrentValue());
                continue;
            }
            signalReduceDTO.setMaxValue(activeSignalManager.getCurrentValue(configSignalItem,signalReduceDTO.getMaxValue()));
            signalReduceDTO.setMinValue(activeSignalManager.getCurrentValue(configSignalItem,signalReduceDTO.getMinValue()));
        }
    }


    /**
     * 根据requestBySignalIds获取设备实时信号等基础值
     * @param requestBySignalIds
     * @return {@link List}<{@link EquipmentGraphicData}>
     */
    private List<EquipmentGraphicData> findEquipmentGraphicDataByRequest(List<ActiveSignalRequestBySignalId> requestBySignalIds) {
        if (CollUtil.isEmpty(requestBySignalIds)) {
            return Collections.emptyList();
        }
        List<EquipmentActiveSignal> equipmentActiveSignalList = activeSignalManager.getEquipmentActiveSignalBySignalId(requestBySignalIds);
        //简单设备信号信息转通用统一的设备信号响应体
        return graphicDataUtil.activeSignalConvertEquipmentGraphicData(equipmentActiveSignalList);
    }
}
