package com.siteweb.hmi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.hmi.entity.CompDataColumn;
import com.siteweb.hmi.mapper.CompDataColumnMapper;
import com.siteweb.hmi.service.CompDataColumnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CompDataColumnServiceImpl implements CompDataColumnService {

    @Autowired
    CompDataColumnMapper compDataColumnMapper;

    @Override
    public List<CompDataColumn> findCompDataColumns(Integer compDataTableId) {
        return compDataColumnMapper.selectList(new QueryWrapper<CompDataColumn>().eq("CompDataTableId", compDataTableId));
    }
}
