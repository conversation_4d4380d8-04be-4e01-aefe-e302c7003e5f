package com.siteweb.hmi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.hmi.entity.CompDataTable;
import com.siteweb.hmi.mapper.CompDataTableMapper;
import com.siteweb.hmi.service.CompDataTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CompDataTableServiceImpl implements CompDataTableService {

    @Autowired
    CompDataTableMapper compDataTableMapper;

    @Override
    public List<CompDataTable> findCompDataTables(Integer CompDataSetId) {
        return compDataTableMapper.selectList(new QueryWrapper<CompDataTable>().eq("CompDataSetId", CompDataSetId));
    }
}
