package com.siteweb.hmi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.hmi.dto.BranchGraphicSignal;
import com.siteweb.hmi.service.GraphicMultiBranchDataPointService;
import com.siteweb.monitoring.dto.EquipmentBranchSignalDTO;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.EquipmentBranch;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.service.EquipmentBranchService;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("GraphicMultiBranchDataPointService")
public class GraphicMultiBranchDataPointServiceImpl implements GraphicMultiBranchDataPointService {
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    EquipmentStateManager equipmentStateManager;

    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    EquipmentBranchService equipmentBranchService;
    @Override
    public BranchGraphicSignal getMultiBranchGraphicDataPoints(ActiveSignalRequestByBaseTypeId requestId) {
        BranchGraphicSignal result = new BranchGraphicSignal();
        Equipment equipment = equipmentManager.getEquipmentById(requestId.getEquipmentId());
        if(ObjectUtil.isNull(equipment)){
            return  result;
        }
        //获取所有信号列表
        List<SimpleActiveSignal> signalList = activeSignalManager.getActiveSignalsByEquipmentId(requestId.getEquipmentId());
        List<Long> ql = getQueryPrefix(requestId.getBaseTypeId());
        Map<Long, Integer> sortMap = new HashMap<>();
        for(int i = 0; i < ql.size(); i++){
            sortMap.put(ql.get(i), i);
        }
        //去掉多余的数据
        signalList = signalList.stream().filter(o -> o.getBaseTypeId() != null && ql.contains(getBaseTypePrefixId(o.getBaseTypeId()))).toList();
        //获取所有支路id，并去数据库查询支路名称
        Set<Integer> branchIds = new HashSet<>();
        for (SimpleActiveSignal simpleActiveSignal : signalList) {
            branchIds.add(this.getBaseTypeBranchId(simpleActiveSignal.getBaseTypeId()));
        }
        List<EquipmentBranch> equipmentBranchList = equipmentBranchService.findByEquipmentIdAndBranchIds(requestId.getEquipmentId(), new ArrayList<>(branchIds));
        Map<Integer, String> branchIdNameMap = equipmentBranchList.stream()
                                                                  .collect(Collectors.toMap(EquipmentBranch::getBranchId, EquipmentBranch::getBranchName, (k1, k2) -> k1));
        Map<String, List<EquipmentBranchSignalDTO>> branchSignalList = new LinkedHashMap<>();
        for (SimpleActiveSignal simpleActiveSignal : signalList) {
            simpleActiveSignal.setDisplayIndex(sortMap.get(getBaseTypePrefixId(simpleActiveSignal.getBaseTypeId())));
            Integer branchId = this.getBaseTypeBranchId(simpleActiveSignal.getBaseTypeId());
            String branchName = branchIdNameMap.getOrDefault(branchId, String.valueOf(branchId));
            EquipmentBranchSignalDTO equipmentBranchSignalDTO = BeanUtil.copyProperties(simpleActiveSignal, EquipmentBranchSignalDTO.class);
            equipmentBranchSignalDTO.setBranchId(branchId);
            branchSignalList.computeIfAbsent(branchName, key -> new ArrayList<>())
                            .add(equipmentBranchSignalDTO);
        }
        //需要前端传过来的数据来排序，否则会导致前端显示顺序错乱
        for (Map.Entry<String,  List<EquipmentBranchSignalDTO>> entry : branchSignalList.entrySet()) {
            List<EquipmentBranchSignalDTO> s = entry.getValue().stream().sorted(Comparator.comparing(EquipmentBranchSignalDTO::getDisplayIndex)).toList();
            branchSignalList.put(entry.getKey(), s);
        }

        result.setEquipmentId(equipment.getEquipmentId());
        result.setEquipmentName(equipment.getEquipmentName());
        result.setBranchSignalList(branchSignalList);
        result.setOnlineState(equipmentStateManager.getEquipmentOnlineStateById(equipment.getEquipmentId()));
        return  result;

    }

    /**
     * 获取请求的基类信号列表
     * @return
     */
    private List<Long> getQueryPrefix(List<Long> baseTypeIds){
        if (CollUtil.isEmpty(baseTypeIds)) {
            return Collections.emptyList();
        }
        List<Long> result = new ArrayList<>();
        for(Long baseTypeId:baseTypeIds){
            result.add(getBaseTypePrefixId(baseTypeId));
        }
        return  result;
    }

    /**
     * 获取信号分组
     * @param baseTypeId 基类Id
     * @return
     */
    private Long getBaseTypePrefixId(Long baseTypeId) {
        return baseTypeId / 1000;
    }

    /**
     * 获取信号排序队列
     * @param baseTypeId
     * @return
     */
    private Integer getBaseTypeBranchId(Long baseTypeId) {
        return  (int)(baseTypeId % 1000);
    }
}
