package com.siteweb.hmi.service.impl;

import com.siteweb.hmi.entity.ComtradeFilePair;
import com.siteweb.hmi.entity.ComtradeFileRetentionPolicy;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * COMTRADE文件清理服务实现
 * 
 * <AUTHOR>
 * @since 2025/5/22
 */
@Slf4j
@Service
public class ComtradeFileCleanupServiceImpl implements ComtradeFileCleanupService {
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    /**
     * 缓存的保留策略配置
     */
    private volatile ComtradeFileRetentionPolicy cachedPolicy;
    
    /**
     * 最后一次加载配置的时间
     */
    private volatile long lastConfigLoadTime = 0;
    
    /**
     * 配置缓存时间（5分钟）
     */
    private static final long CONFIG_CACHE_DURATION = 300000L;
    
    /**
     * 每个设备的最后清理时间缓存
     */
    private final Map<Integer, Long> lastCleanupTimeCache = new ConcurrentHashMap<>();
    
    @Override
    public int cleanupComtradeFiles(Integer equipmentId) {
        if (equipmentId == null) {
            log.warn("设备ID为null，跳过清理");
            return 0;
        }
        
        String localPath = "upload-dir/comtrade/" + equipmentId;
        File directory = new File(localPath);
        
        if (!directory.exists()) {
            log.info("设备ID: {} 的COMTRADE目录不存在: {}", equipmentId, localPath);
            return 0;
        }
        
        ComtradeFileRetentionPolicy policy = getRetentionPolicy();
        if (!policy.isCleanupEnabled()) {
            log.info("设备ID: {} 的文件清理功能已禁用", equipmentId);
            return 0;
        }
        
        // 检查清理间隔
        Long lastCleanupTime = lastCleanupTimeCache.get(equipmentId);
        long currentTime = System.currentTimeMillis();
        if (lastCleanupTime != null && 
            (currentTime - lastCleanupTime) < policy.getCleanupInterval()) {
            log.debug("设备ID: {} 距离上次清理时间过短，跳过此次清理", equipmentId);
            return 0;
        }
        
        try {
            int deletedCount = cleanupComtradeFiles(directory, policy);
            lastCleanupTimeCache.put(equipmentId, currentTime);
            
            if (deletedCount > 0) {
                log.info("设备ID: {} 清理完成，删除了 {} 个文件对", equipmentId, deletedCount);
            }
            
            return deletedCount;
        } catch (Exception e) {
            log.error("清理设备ID: {} 的COMTRADE文件时出错", equipmentId, e);
            return 0;
        }
    }
    
    @Override
    public int cleanupComtradeFiles(File directory, ComtradeFileRetentionPolicy policy) {
        if (!directory.exists() || !directory.isDirectory()) {
            return 0;
        }
        
        List<ComtradeFilePair> filePairs = scanComtradeFiles(directory);
        
        if (!policy.needsCleanup(filePairs.size())) {
            log.debug("目录 {} 的文件数量 {} 未超过限制 {}, 无需清理", 
                     directory.getPath(), filePairs.size(), policy.getMaxFilePairsPerDevice());
            return 0;
        }
        
        int filesToDelete = policy.getFilesToDelete(filePairs.size());
        if (filesToDelete <= 0) {
            return 0;
        }
        
        // 按时间排序，最旧的在前
        filePairs.sort(Comparator.comparing(ComtradeFilePair::getCreationTime));
        
        int deletedCount = 0;
        List<String> errors = new ArrayList<>();
        
        for (int i = 0; i < Math.min(filesToDelete, filePairs.size()); i++) {
            ComtradeFilePair filePair = filePairs.get(i);
            try {
                if (filePair.delete()) {
                    deletedCount++;
                    log.debug("删除文件对: {}", filePair.getFilePrefix());
                } else {
                    String error = "删除文件对失败: " + filePair.getFilePrefix();
                    errors.add(error);
                    log.warn(error);
                }
            } catch (Exception e) {
                String error = "删除文件对时出错: " + filePair.getFilePrefix() + ", 错误: " + e.getMessage();
                errors.add(error);
                log.error(error, e);
            }
        }
        
        if (!errors.isEmpty()) {
            log.warn("目录 {} 清理过程中发生 {} 个错误", directory.getPath(), errors.size());
        }
        
        return deletedCount;
    }
    
    @Override
    public List<ComtradeFilePair> scanComtradeFiles(File directory) {
        if (!directory.exists() || !directory.isDirectory()) {
            return new ArrayList<>();
        }
        
        File[] files = directory.listFiles();
        if (files == null) {
            return new ArrayList<>();
        }
        
        // 收集所有文件前缀
        Set<String> filePrefixes = new HashSet<>();
        
        for (File file : files) {
            if (file.isFile()) {
                String fileName = file.getName();
                String lowerFileName = fileName.toLowerCase();
                if (lowerFileName.endsWith(".cfg") || lowerFileName.endsWith(".dat")) {
                    String prefix = fileName.substring(0, fileName.lastIndexOf('.'));
                    filePrefixes.add(prefix);
                }
            }
        }
        
        // 创建文件对
        List<ComtradeFilePair> filePairs = new ArrayList<>();
        for (String prefix : filePrefixes) {
            ComtradeFilePair filePair = new ComtradeFilePair(prefix, directory);
            if (filePair.exists()) {
                filePairs.add(filePair);
            }
        }
        
        return filePairs;
    }
    
    @Override
    public ComtradeFileRetentionPolicy getRetentionPolicy() {
        long currentTime = System.currentTimeMillis();
        
        // 检查缓存是否过期
        if (cachedPolicy == null || 
            (currentTime - lastConfigLoadTime) > CONFIG_CACHE_DURATION) {
            loadRetentionPolicyFromConfig();
            lastConfigLoadTime = currentTime;
        }
        
        return cachedPolicy;
    }
    
    @Override
    public void updateRetentionPolicy(ComtradeFileRetentionPolicy policy) {
        this.cachedPolicy = policy;
        this.lastConfigLoadTime = System.currentTimeMillis();
        
        // 可选：将配置保存到系统配置表
        saveRetentionPolicyToConfig(policy);
    }
    
    @Override
    public ComtradeFileStatistics getFileStatistics(Integer equipmentId) {
        if (equipmentId == null) {
            return new ComtradeFileStatistics(0, 0, 0, 0);
        }
        
        String localPath = "upload-dir/comtrade/" + equipmentId;
        File directory = new File(localPath);
        
        if (!directory.exists()) {
            return new ComtradeFileStatistics(0, 0, 0, 0);
        }
        
        List<ComtradeFilePair> filePairs = scanComtradeFiles(directory);
        
        int totalFilePairs = filePairs.size();
        long totalSize = filePairs.stream().mapToLong(ComtradeFilePair::getTotalSize).sum();
        int completeFilePairs = (int) filePairs.stream().filter(ComtradeFilePair::isComplete).count();
        int orphanedFiles = totalFilePairs - completeFilePairs;
        
        return new ComtradeFileStatistics(totalFilePairs, totalSize, completeFilePairs, orphanedFiles);
    }
    
    @Override
    public CleanupResult manualCleanup(Integer equipmentId, boolean forceCleanup) {
        if (equipmentId == null) {
            return new CleanupResult(false, 0, "设备ID不能为null", 
                                   Collections.singletonList("设备ID不能为null"));
        }
        
        try {
            ComtradeFileRetentionPolicy policy = getRetentionPolicy();
            
            // 如果强制清理，临时启用清理功能
            if (forceCleanup && !policy.isCleanupEnabled()) {
                policy = ComtradeFileRetentionPolicy.builder()
                        .maxFilePairsPerDevice(policy.getMaxFilePairsPerDevice())
                        .cleanupEnabled(true)
                        .cleanupBatchSize(policy.getCleanupBatchSize())
                        .cleanupInterval(0L)  // 忽略间隔限制
                        .cleanupAfterDownload(policy.isCleanupAfterDownload())
                        .maxRetryCount(policy.getMaxRetryCount())
                        .build();
            }
            
            // 清空缓存的清理时间，确保可以立即执行
            if (forceCleanup) {
                lastCleanupTimeCache.remove(equipmentId);
            }
            
            int deletedCount = cleanupComtradeFiles(equipmentId);
            
            String message = String.format("设备ID: %d 手动清理完成，删除了 %d 个文件对", 
                                         equipmentId, deletedCount);
            
            return new CleanupResult(true, deletedCount, message, Collections.emptyList());
            
        } catch (Exception e) {
            String error = "手动清理失败: " + e.getMessage();
            log.error("设备ID: {} 手动清理失败", equipmentId, e);
            return new CleanupResult(false, 0, error, 
                                   Collections.singletonList(error));
        }
    }
    
    /**
     * 从系统配置加载保留策略
     */
    private void loadRetentionPolicyFromConfig() {
        try {
            ComtradeFileRetentionPolicy.ComtradeFileRetentionPolicyBuilder builder = 
                    ComtradeFileRetentionPolicy.builder();
            
            // 加载各项配置
            builder.maxFilePairsPerDevice(getIntConfig(
                    ComtradeFileRetentionPolicy.ConfigKeys.MAX_FILE_PAIRS_PER_DEVICE, 256));
            
            builder.cleanupEnabled(getBooleanConfig(
                    ComtradeFileRetentionPolicy.ConfigKeys.CLEANUP_ENABLED, true));
            
            builder.cleanupBatchSize(getIntConfig(
                    ComtradeFileRetentionPolicy.ConfigKeys.CLEANUP_BATCH_SIZE, 10));
            
            builder.cleanupInterval(getLongConfig(
                    ComtradeFileRetentionPolicy.ConfigKeys.CLEANUP_INTERVAL, 300000L));
            
            builder.cleanupAfterDownload(getBooleanConfig(
                    ComtradeFileRetentionPolicy.ConfigKeys.CLEANUP_AFTER_DOWNLOAD, true));
            
            builder.maxRetryCount(getIntConfig(
                    ComtradeFileRetentionPolicy.ConfigKeys.MAX_RETRY_COUNT, 3));
            
            this.cachedPolicy = builder.build();
            
            log.debug("成功加载COMTRADE文件保留策略配置");
            
        } catch (Exception e) {
            log.error("加载COMTRADE文件保留策略配置失败，使用默认配置", e);
            this.cachedPolicy = ComtradeFileRetentionPolicy.builder().build();
        }
    }
    
    /**
     * 保存保留策略到系统配置
     */
    private void saveRetentionPolicyToConfig(ComtradeFileRetentionPolicy policy) {
        // 实现配置保存逻辑（可选）
        log.info("保留策略配置已更新");
    }
    
    /**
     * 获取整数配置
     */
    private int getIntConfig(String key, int defaultValue) {
        try {
            var config = systemConfigService.findBySystemConfigKey(key);
            if (config == null || config.getSystemConfigValue() == null) {
                log.debug("配置 {} 不存在或值为空，使用默认值 {}", key, defaultValue);
                return defaultValue;
            }
            
            String value = config.getSystemConfigValue();
            if (StringUtils.hasText(value)) {
                return Integer.parseInt(value.trim());
            }
        } catch (Exception e) {
            log.debug("获取配置 {} 失败，使用默认值 {}", key, defaultValue);
        }
        return defaultValue;
    }
    
    /**
     * 获取布尔配置
     */
    private boolean getBooleanConfig(String key, boolean defaultValue) {
        try {
            var config = systemConfigService.findBySystemConfigKey(key);
            if (config == null || config.getSystemConfigValue() == null) {
                log.debug("配置 {} 不存在或值为空，使用默认值 {}", key, defaultValue);
                return defaultValue;
            }
            
            String value = config.getSystemConfigValue();
            if (StringUtils.hasText(value)) {
                return Boolean.parseBoolean(value.trim());
            }
        } catch (Exception e) {
            log.debug("获取配置 {} 失败，使用默认值 {}", key, defaultValue);
        }
        return defaultValue;
    }
    
    /**
     * 获取长整数配置
     */
    private long getLongConfig(String key, long defaultValue) {
        try {
            var config = systemConfigService.findBySystemConfigKey(key);
            if (config == null || config.getSystemConfigValue() == null) {
                log.debug("配置 {} 不存在或值为空，使用默认值 {}", key, defaultValue);
                return defaultValue;
            }
            
            String value = config.getSystemConfigValue();
            if (StringUtils.hasText(value)) {
                return Long.parseLong(value.trim());
            }
        } catch (Exception e) {
            log.debug("获取配置 {} 失败，使用默认值 {}", key, defaultValue);
        }
        return defaultValue;
    }
}