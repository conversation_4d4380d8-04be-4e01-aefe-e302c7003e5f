package com.siteweb.hmi.service;

import com.siteweb.hmi.entity.GraphicPageTemplate;

import java.util.List;

public interface GraphicPageTemplateService{

    List<GraphicPageTemplate> findGraphicPageTemplates();

    int createGraphicPageTemplate(GraphicPageTemplate graphicPageTemplate);

    int deleteById(Integer graphicPageTemplateId);

    int updateGraphicPageTemplate(GraphicPageTemplate graphicPageTemplate);

    GraphicPageTemplate findById(Integer graphicPageTemplateId);

    List<GraphicPageTemplate> findByBaseEquipmentId(Integer baseEquipmentId);
}

