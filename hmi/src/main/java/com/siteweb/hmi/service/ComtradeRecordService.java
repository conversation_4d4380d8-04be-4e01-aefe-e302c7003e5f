package com.siteweb.hmi.service;

import com.siteweb.hmi.entity.ComtradeRecord;
import com.siteweb.monitoring.dto.ResourceStructureEquipmentTreeDTO;
import java.util.List;

/**
 * Description: Comtrade记录服务接口
 * Author: <EMAIL>
 * Creation Date: 2025/5/20
 */
public interface ComtradeRecordService {
    /**
     * 通过设备ID查询Comtrade记录列表
     * @param equipmentId 设备ID
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return Comtrade记录列表
     */
    List<ComtradeRecord> getComtradeRecordsByEquipmentId(Integer equipmentId, String startTime, String endTime);

    /**
     * 根据系统配置中的COMTRADE设备基础类型ID获取资源结构设备树
     *
     * @return {@link ResourceStructureEquipmentTreeDTO} 层级树
     */
    ResourceStructureEquipmentTreeDTO getResourceStructureEquipmentTreeForComtrade();
}
