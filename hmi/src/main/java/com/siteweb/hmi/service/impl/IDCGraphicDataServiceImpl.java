package com.siteweb.hmi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.hmi.dto.*;
import com.siteweb.hmi.enums.NsbdjssyIndexEnum;
import com.siteweb.hmi.mapper.IDCGraphicDataMapper;
import com.siteweb.hmi.service.IDCGraphicDataService;
import com.siteweb.hmi.util.GraphicDataUtil;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.dto.ActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.model.RealTimeSignalKey;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.HistoryEventService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.CoreEventSeverityService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("IDCGraphicDataService")
public class IDCGraphicDataServiceImpl implements IDCGraphicDataService {
    @Autowired
    GraphicDataUtil graphicDataUtil;
    @Autowired
    IDCGraphicDataMapper idcGraphicDataMapper;
    @Autowired
    EquipmentBaseTypeManager equipmentBaseTypeManager;
    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    private EquipmentStateManager equipmentStateManager;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    ActiveEventService activeEventService;
    @Autowired
    HistoryEventService historyEventService;
    @Autowired
    CoreEventSeverityService coreEventSeverityService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    ActiveSignalManager activeSignalManager;
    @Autowired
    EquipmentManager equipmentManager;

    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    private Boolean eventLevelFilterEnable;

    @Override
    public List<StatisticsResult> getEventStatisticsByObjectIdAndType(Integer userId, String objectId, Integer pageCategory, String resourceStructureIds) {
        List<StatisticsResult> eventStatistics = null;
        List<Integer> objectIdList = new ArrayList<>();
        if (objectId != null) {
            objectIdList = Arrays.stream(objectId.split(",")).map(Integer::valueOf).toList();
        }
        if (CharSequenceUtil.isNotEmpty(resourceStructureIds)) {
            List<Integer> selectResourceIds = Arrays.stream(resourceStructureIds.split(",")).map(Integer::valueOf).toList();
            List<Integer> userResourceIds = resourceStructureService.findResourceIdsByUserIdAndResourceStructureIds(userId, selectResourceIds);
            eventStatistics = idcGraphicDataMapper.getResourceStructureAlarmLevelCountByGroup(userResourceIds);
        } else {
            if (pageCategory == null && objectId == null) {
                //都为null 查所有
                eventStatistics = this.getAllEventStatistics(userId);
                return eventStatistics;
            } else if (ObjectUtil.equals(SourceType.EQUIPMENT.value(), pageCategory)) {
                //是设备
                eventStatistics = idcGraphicDataMapper.getEventStatisticsByEquipment(objectIdList);
            } else {
                eventStatistics = idcGraphicDataMapper.getEventStatisticsByResourceStructure(objectIdList, pageCategory);
            }
        }
        List<StatisticsResult> eventStatisticsBySeverityList = graphicDataUtil.constructCurrentStatistics();
        Map<Integer, StatisticsResult> statisticsResultMap = eventStatistics.stream().collect(
                Collectors.toMap(StatisticsResult::getId, p -> p));

        for (StatisticsResult item : eventStatisticsBySeverityList) {
            StatisticsResult statisticsResult = statisticsResultMap.get(item.getId());
            if (ObjectUtil.isNotNull(statisticsResult))
                item.setValue(statisticsResult.getValue());
        }
        return eventStatisticsBySeverityList;
    }

    /**
     * 获取所有告警统计
     *
     * @return {@link List}<{@link StatisticsResult}>
     */
    private List<StatisticsResult> getAllEventStatistics(Integer userId) {
        //告警需未结束
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEventEnded(false);
        Map<Integer, Integer> eventLevelCountMap = activeEventManager.groupActiveEventsBySeverity(userId, activeEventFilterVO);
        List<StatisticsResult> eventStatisticsBySeverityList = graphicDataUtil.constructCurrentStatistics();
        for (StatisticsResult statisticsResult : eventStatisticsBySeverityList) {
            //存在该告警
            if (eventLevelCountMap.containsKey(statisticsResult.getId())) {
                statisticsResult.setValue(eventLevelCountMap.get(statisticsResult.getId()));
            }
        }
        return eventStatisticsBySeverityList;
    }

    @Override
    public List<StatisticsResult> getEquipmentStateStatistics() {
        return null;
    }

    @Override
    public List<StatisticsResult> getEquipmentStateStatistics(Integer objectId, Integer pageCategory) {
        List<Integer> equipmentIdList = findEquipmentIdsByObjectId(objectId);
        Map<Integer, Long> countMap = equipmentStateManager.getEquipmentStateByIds(equipmentIdList)
                .values()
                .stream()
                .collect(Collectors.groupingBy(e -> e.getOnlineState().value(), Collectors.counting()));
        return countMap.entrySet()
                .stream()
                .map(entry -> new StatisticsResult(entry.getKey(), messageSourceUtil.getMessage("api.stationStatus." + entry.getKey()), entry.getValue().intValue()))
                .sorted(Comparator.comparing(StatisticsResult::getId))
                .toList();
    }

    /**
     * @param objectId 层级id
     * @return {@link List }<{@link Integer }>
     */
    private List<Integer> findEquipmentIdsByObjectId(Integer objectId) {
        //没有传层级id则拥有权限的设备id
        if (Objects.isNull(objectId)) {
            return new ArrayList<>(equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId()));
        }
        return equipmentService.recursiveFindEquipmentsByResourceStructureIds(List.of(objectId))
                .stream()
                .map(Equipment::getEquipmentId)
                .toList();
    }


    @Override
    public List<EventStatisticsByDuration> getEventStatisticsByDuration(Integer objectId, Integer pageCategory) {
        List<EventStatisticsByDuration> result = null;
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        if (pageCategory == 7) {
            result = idcGraphicDataMapper.getEquipmentEventStatisticsByDuration(objectId, filterByEventLevel);
        } else {
            result = idcGraphicDataMapper.getStructureEventStatisticsByDuration(objectId, pageCategory, filterByEventLevel);
        }
        for (EventStatisticsByDuration eventStatisticsByDuration : result) {
            EquipmentBaseType equipmentBaseType = equipmentBaseTypeManager.getEquipmentBaseTypeFromCache(eventStatisticsByDuration.getBaseEquipmentId());
            if (ObjectUtil.isNotNull(equipmentBaseType))
                eventStatisticsByDuration.setBaseEquipmentName(equipmentBaseType.getBaseEquipmentName());
        }
        return result;
    }

    @Override
    public Map<String, List<AlarmEquipmentCategoryStatistics>> getAlarmEquipmentCategoryStatistics() {
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<AlarmEquipmentCategoryStatistics> statusStatistics = idcGraphicDataMapper.getAlarmEquipmentCategoryStatistics(filterByEventLevel);
        Map<String, List<AlarmEquipmentCategoryStatistics>> result = new HashMap<>();
        for (AlarmEquipmentCategoryStatistics alarmDeviceCategoryStatistics : statusStatistics) {
            if (result.containsKey(alarmDeviceCategoryStatistics.getResourceStructureName())) {
                result.get(alarmDeviceCategoryStatistics.getResourceStructureName()).add(alarmDeviceCategoryStatistics);
            } else {
                List<AlarmEquipmentCategoryStatistics> alarmDeviceCategoryStatisticsList = new ArrayList<>();
                alarmDeviceCategoryStatisticsList.add(alarmDeviceCategoryStatistics);
                result.put(alarmDeviceCategoryStatistics.getResourceStructureName(), alarmDeviceCategoryStatisticsList);
            }
        }
        return result;
    }

    @Override
    public EventStatisticsOverview getEventStatisticsOverview(Integer userId, boolean orderByStartTime) {
        EventStatisticsOverview eventStatisticsOverview = new EventStatisticsOverview();
        List<StatisticsResult> statisticsResults = graphicDataUtil.constructCurrentStatistics();
        ActiveEventFilterVO activeEventFilterVO = new ActiveEventFilterVO();
        activeEventFilterVO.setEventEnded(false);
        Map<Integer, Integer> map = activeEventManager.groupActiveEventsBySeverity(userId, activeEventFilterVO);
        for (StatisticsResult statisticsResult : statisticsResults) {
            if (map.containsKey(statisticsResult.getId()))
                statisticsResult.setValue(map.get(statisticsResult.getId()));
        }
        eventStatisticsOverview.setEventStatisticsBySeverities(statisticsResults);
        List<ActiveEventDTO> activeEventDTOS = activeEventManager.findTopNActiveEventDTOsByUserIdAndEndTimeIsNull(userId, 8, orderByStartTime);
        eventStatisticsOverview.setGraphicEvents(activeEventDTOS);
        return eventStatisticsOverview;

    }

    @Override
    public List<StatisticsResult> getResourceStructureAlarmCountByGroup(List<Integer> resourceStructureIds, int userId) {
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        Map<Integer, StatisticsResult> statisticsResultMap = idcGraphicDataMapper.getResourceStructureAlarmCountByGroup(resourceStructureIds, filterByEventLevel).stream().collect(
                Collectors.toMap(StatisticsResult::getId, p -> p));
        List<StatisticsResult> result = conStructStatisticsResultByResourceStructureId(resourceStructureIds, statisticsResultMap);
        return result;
    }

    private List<StatisticsResult> conStructStatisticsResultByResourceStructureId(List<Integer> resourceStructureIds, Map<Integer, StatisticsResult> statisticsResultMap) {
        List<StatisticsResult> results = new ArrayList<>();

        for (Integer resourceStructureId : resourceStructureIds) {
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
            StatisticsResult statisticsResult = new StatisticsResult();
            statisticsResult.setId(resourceStructureId);
            statisticsResult.setName(resourceStructure.getResourceStructureName());
            if (statisticsResultMap.containsKey(resourceStructureId)) {
                statisticsResult.setValue(statisticsResultMap.get(resourceStructureId).getValue());
            } else {
                statisticsResult.setValue(0);
            }
            results.add(statisticsResult);
        }
        return results;
    }

    private Map<Integer, Integer> filterEventMapByEventLevels(Map<Integer, Integer> eventMap, List<Integer> eventLevels) {
        return eventMap.entrySet().stream()
                .filter(entry -> eventLevels.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @Override
    public List<EventLevelRankDTO> getActiveHistoryEventSeverityRankStatistics(Date startTime, Date endTime, Integer userId) {
        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        vo.setStartTimeFrom(startTime);
        vo.setStartTimeTo(endTime);
        Map<Integer, Integer> activeMap = activeEventService.groupActiveEventsBySeverity(userId, vo);
        List<CoreEventSeverity> coreEventSeverityList = coreEventSeverityService.getCoreEventSeverities();
        List<Integer> eventLevelList = coreEventSeverityList.stream().map(CoreEventSeverity::getEventLevel).toList();
        activeMap = this.filterEventMapByEventLevels(activeMap, eventLevelList);
        Map<Integer, Integer> historyMap = historyEventService.groupHistoryEventsBySeverity(startTime, endTime, userId);
        historyMap = this.filterEventMapByEventLevels(historyMap, eventLevelList);
        Integer activeSum = activeMap.values().stream().mapToInt(Integer::intValue).sum();
        Integer historySum = historyMap.values().stream().mapToInt(Integer::intValue).sum();
        int sum = activeSum + historySum;
        Map<Integer, CoreEventSeverity> coreEventSeverityMap = coreEventSeverityList.stream().collect(Collectors.toMap(CoreEventSeverity::getEventLevel, r -> r));
        List<EventLevelRankDTO> eventLevelRankDTOs = new ArrayList<>();
        for (Integer eventLevel : eventLevelList) {
            EventLevelRankDTO rankDTO = new EventLevelRankDTO();
            int currentLevelAlarmCount = activeMap.getOrDefault(eventLevel, 0) + historyMap.getOrDefault(eventLevel, 0);
            rankDTO.setCount(currentLevelAlarmCount);
            rankDTO.setEventLevel(eventLevel);
            BigDecimal percent = getPercent(currentLevelAlarmCount, sum);
            rankDTO.setPercent(percent + "%");
            rankDTO.setEventSeverity(coreEventSeverityMap.get(eventLevel).getSeverityName());
            eventLevelRankDTOs.add(rankDTO);
        }
        return eventLevelRankDTOs;
    }

    private BigDecimal getPercent(int currentLevelAlarmCount, int sum) {
        if (sum == 0) {
            return BigDecimal.ZERO;
        }
        return NumberUtil.div(NumberUtil.toBigDecimal(currentLevelAlarmCount * 100), sum, 1);
    }

    @Override
    public NsbdjssyIndexDataDTO getNsbdjssyIndexData() {
        NsbdjssyIndexDataDTO result = new NsbdjssyIndexDataDTO();
        result.setCenterEnvironmentTemperature(getInnerIndexData(NsbdjssyIndexEnum.NSBDJSSY_INDEX_CENTERENVIRONMENTTEMPERATURE));
        result.setCenterEnvironmentHumidity(getInnerIndexData(NsbdjssyIndexEnum.NSBDJSSY_INDEX_CENTERENVIRONMENTHUMIDITY));
        result.setUpsBatteryTemperature(getInnerIndexData(NsbdjssyIndexEnum.NSBDJSSY_INDEX_UPSBATTERYTEMPERATURE));
        result.setComputerRoomTemperature(getInnerIndexData(NsbdjssyIndexEnum.NSBDJSSY_INDEX_COMPUTERROOMTEMPERATURE));
        result.setUpsIndoorTemperature(getInnerIndexData(NsbdjssyIndexEnum.NSBDJSSY_INDEX_UPSINDOORTEMPERATURE));
        return result;
    }

    private NsbdjssyIndexDataDTO.IndexData getInnerIndexData(NsbdjssyIndexEnum indexEnum) {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(indexEnum.getValue());
        if (Objects.isNull(systemConfig)) {
            return null;
        }
        String systemConfigValue = systemConfig.getSystemConfigValue();
        if (CharSequenceUtil.isBlank(systemConfigValue)) {
            return null;
        }
        return handleMultipleSignals(systemConfigValue);
    }


    private NsbdjssyIndexDataDTO.IndexData handleMultipleSignals(String systemConfigValue) {
        String[] deviceSignalPairs = systemConfigValue.split(",");
        if (deviceSignalPairs.length == 0) {
            return null;
        }
        List<RealTimeSignalKey> redisKey = new ArrayList<>();
        Integer equipmentId = null;
        List<Integer> signalIds = new ArrayList<>();
        for (String deviceSignalPair : deviceSignalPairs) {
            if (!deviceSignalPair.contains(".")) {
                continue;
            }
            String[] firstPair = deviceSignalPair.split("\\.");
            if (Objects.isNull(equipmentId)) {
                equipmentId = Integer.parseInt(firstPair[0]);
            }
            Integer signalId = Integer.parseInt(firstPair[1]);
            signalIds.add(signalId);
            redisKey.add(new RealTimeSignalKey(Integer.parseInt(firstPair[0]), Integer.parseInt(firstPair[1])));

        }
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return null;
        }
        NsbdjssyIndexDataDTO.IndexData innerIndexData = new NsbdjssyIndexDataDTO.IndexData();
        innerIndexData.setEquipmentId(equipment.getEquipmentId());
        innerIndexData.setEquipmentName(equipment.getEquipmentName());
        innerIndexData.setEquipmentPosition(equipmentManager.getEquipmentPosition(equipmentId));
        innerIndexData.setSignalId(signalIds.stream().map(Object::toString).collect(Collectors.joining(",")));
        // 获取信号值
        List<ActiveSignal> allSignals = activeSignalManager.getActiveSignalsByKeys(redisKey);
        if (CollUtil.isNotEmpty(allSignals)) {
            if (allSignals.size() == 1) {
                innerIndexData.setCurrentValue(allSignals.get(0).getCurrentValue());
            } else {
                // 多个信号计算平均值
                double sum = 0.0;
                int count = 0;
                for (ActiveSignal signal : allSignals) {
                    if (Objects.nonNull(signal.getOriginalValue()) && NumberUtil.isNumber(signal.getOriginalValue())) {
                        sum += Double.parseDouble(signal.getOriginalValue());
                        count++;
                    }
                }
                if (count > 0) {
                    innerIndexData.setCurrentValue(NumberUtil.div(sum, count, 2) + allSignals.get(0).getUnit());
                }
            }
        }
        return innerIndexData;
    }
}
