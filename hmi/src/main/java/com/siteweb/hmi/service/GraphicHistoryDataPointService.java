package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.EquipmentSignalDurationDTO;
import com.siteweb.hmi.dto.GraphicHistorySignal;
import com.siteweb.hmi.dto.GraphicMultiSampleSignal;
import com.siteweb.monitoring.dto.EquipmentSignalIndexDTO;
import com.siteweb.monitoring.entity.HistorySignal;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GraphicHistoryDataPointService {
    /**
     * 历史信号数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param equipmentId 设备id
     * @param signalId 信号id
     * @return {@link List}<{@link HistorySignal}>
     */
    List<HistorySignal> findEquipmentHistorySignal(Date startTime, Date endTime, Integer equipmentId, Integer signalId);
    List<GraphicMultiSampleSignal> findGraphicHistorySignalDataByBaseTypeId(Date startTime, Date endTime, Integer equipmentId, String baseTypeIds);
    List<GraphicMultiSampleSignal> findGraphicHistorySignalDataBySignal(EquipmentSignalDurationDTO equipmentSignalDurationDTO);
    List<GraphicHistorySignal>  getHistorySignalByBaseTypeId(Date startTime, Date endTime, Integer equipmentId, Long baseTypeId);

    List<HistorySignal>  getMultipleHistorySignalBySignalIds(Date startTime, Date endTime, List<EquipmentSignalIndexDTO> signals);

    Map<String, Map<String, String>> getMultipleHistorySignalMapBySignalIds(Date startTime, Date endTime, List<EquipmentSignalIndexDTO> query);

}
