package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IDCGraphicDataService {
    List<StatisticsResult> getEventStatisticsByObjectIdAndType(Integer userId, String objectId, Integer pageCategory, String resourceStructureIds);

    List<StatisticsResult> getEquipmentStateStatistics();

    public List<StatisticsResult> getEquipmentStateStatistics(Integer objectId, Integer pageCategory);

    public List<EventStatisticsByDuration> getEventStatisticsByDuration(Integer objectId, Integer pageCategory);

    Map<String, List<AlarmEquipmentCategoryStatistics>>getAlarmEquipmentCategoryStatistics();

    EventStatisticsOverview getEventStatisticsOverview(Integer userId, boolean orderByStartTime);
    List<StatisticsResult> getResourceStructureAlarmCountByGroup(List<Integer> resourceStructureIds, int userId);

    List<EventLevelRankDTO> getActiveHistoryEventSeverityRankStatistics(Date startTime, Date endTime, Integer userId);

    NsbdjssyIndexDataDTO getNsbdjssyIndexData();
}
