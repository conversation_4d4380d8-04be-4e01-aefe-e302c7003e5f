package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.AlarmDurationStatisticsDTO;
import com.siteweb.hmi.dto.EquipmentStatisticsDTO;
import com.siteweb.hmi.vo.InterruptStatisticsVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MixChartGraphicDataService {

    List<List<Object>> getStationCategoryResultList(Integer userId);

    List<Object[]> getStationHaltPowerOffGen(Integer userId, Integer structureTypeId, Integer resourceStructureId);

    List<List<Object>> getStationEventTop5(Integer userId);

    List<Object[]> deviceMonitoringStatus();

    Map<String,Object> getStationAvailability(Integer userId);

    Map<String, List<AlarmDurationStatisticsDTO>> getAlarmGroupData(Date startTime, Date endTime, String resourceStructureIds);

    List<EquipmentStatisticsDTO> getEquipmentStatistics(Integer userId, List<Integer> resourceStructureIdSet);

    /**
     * 中断统计（局站、采集单元、设备）
     * map:[key=>station/samplerUnit/equipment]
     * @param userId
     * @return
     */
    Map<String, InterruptStatisticsVO> getInterruptStatistics(Integer userId, String resourceStructureIds);

    /**
     * 根据事件类型统计可用率
     *
     * @param userId 用户id
     * @param eventCategoryId 事件类型  tbl_dataitem EntryId  = 24; 10市电可用率 7监控可用率
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param resourceStructureId 层级id
     * @return {@link String}
     */
    String getAvailableByEventCategoryId(Integer userId, Integer eventCategoryId, Date startTime, Date endTime, String resourceStructureId);
}
