package com.siteweb.hmi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.hmi.dto.EquipmentSignalDurationDTO;
import com.siteweb.hmi.dto.GraphicHistorySignal;
import com.siteweb.hmi.dto.GraphicMultiSampleSignal;
import com.siteweb.hmi.service.GraphicHistoryDataPointService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.EquipmentSignalIndexDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.HistorySignal;
import com.siteweb.monitoring.enumeration.SignalTypeEnum;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.mamager.HistorySignalManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.vo.ActiveSignalRequestBySignalId;
import com.siteweb.utility.constans.SystemConfigEnum;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SignalBaseDicService;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("GraphicHistoryDataPointService")
public class GraphicHistoryDataPointServiceImpl implements GraphicHistoryDataPointService {

    @Autowired
    HistorySignalManager historySignalManager;

    @Autowired
    ConfigSignalManager configSignalManager;

    @Autowired
    private EquipmentService equipment;
    @Autowired
    private SignalBaseDicService signalBaseDicService;
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 是否开启使用五分钟存储库查询历史数据
     * @return boolean
     */
    public boolean isOpenFiveMinuteStorage() {
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey(SystemConfigEnum.FIVE_MINUTES_STORAGE.getSystemConfigKey());
        if (Objects.isNull(systemConfig)) {
            return false;
        }
        return Boolean.parseBoolean(systemConfig.getSystemConfigValue());
    }

    @Override
    public List<HistorySignal> findEquipmentHistorySignal(Date startTime, Date endTime, Integer equipmentId, Integer signalId) {
        return historySignalManager.findHistorySignalByDuration(startTime, endTime, equipmentId, signalId, null, isOpenFiveMinuteStorage());
    }

    @Override
    public List<GraphicMultiSampleSignal> findGraphicHistorySignalDataByBaseTypeId(Date startTime, Date endTime, Integer equipmentId, String baseTypeIds) {
        if (CharSequenceUtil.isBlank(baseTypeIds)) {
            return Collections.emptyList();
        }
        List<Long> baseTypeIdList = Arrays.stream(baseTypeIds.split(","))
                                          .map(Long::parseLong)
                                          .toList();
        List<GraphicHistorySignal> historySignalList = new ArrayList<>();
        List<GraphicHistorySignal> allHistorySignalList = new ArrayList<>();
        //获取所有历史基类信号
        int sortIndex = 0;
        for (Long baseTypeId : baseTypeIdList) {
            List<GraphicHistorySignal> historySignal = this.getHistorySignalByBaseTypeId(startTime, endTime, equipmentId, baseTypeId);

            //添加第一条数据用于填充
            if (CollUtil.isNotEmpty(historySignal)) {
                allHistorySignalList.add(historySignal.get(0));
            }
            //添加排序字段避免避免顺序出错
            for (GraphicHistorySignal graphicHistorySignal : historySignal) {
                graphicHistorySignal.setSortIndex(sortIndex++);
            }
            historySignalList.addAll(historySignal);
        }
        return fillHistorySignal(historySignalList, allHistorySignalList);
    }

    @Override
    public List<GraphicMultiSampleSignal> findGraphicHistorySignalDataBySignal(EquipmentSignalDurationDTO equipmentSignalDurationDTO) {
        if (ObjectUtil.isNull(equipmentSignalDurationDTO)) {
            return Collections.emptyList();
        }
        List<GraphicHistorySignal> historySignalList = new ArrayList<>();
        List<GraphicHistorySignal> allHistorySignalList = new ArrayList<>();
        //获取所有历史基类信号
        int sortIndex = 0;
        for (ActiveSignalRequestBySignalId activeSignalRequestBySignalId : equipmentSignalDurationDTO.getEquipmentSignalList()) {
            for (Integer signalId : activeSignalRequestBySignalId.getSignalIds()) {
                ConfigSignalItem configSignalItem = configSignalManager.getConfigSignalItemByEquipmentIdAndSignalId(activeSignalRequestBySignalId.getEquipmentId(), signalId);
                List<GraphicHistorySignal> historySignal = this.getGraphicHistory(equipmentSignalDurationDTO.getStartTime(), equipmentSignalDurationDTO.getEndTime(), activeSignalRequestBySignalId.getEquipmentId(), configSignalItem);
                //添加第一条数据用于填充
                if (CollUtil.isNotEmpty(historySignal)) {
                    allHistorySignalList.add(historySignal.get(0));
                }
                //添加排序字段避免避免顺序出错
                for (GraphicHistorySignal graphicHistorySignal : historySignal) {
                    graphicHistorySignal.setSortIndex(sortIndex++);
                }
                historySignalList.addAll(historySignal);
            }
        }
        return fillHistorySignal(historySignalList, allHistorySignalList);
    }

    /**
     * 填补历史信号
     *
     * @param historySignalList    历史信号列表
     * @param allHistorySignalList 所有历史信号列表
     * @return {@link List}<{@link GraphicMultiSampleSignal}>
     */
    private List<GraphicMultiSampleSignal> fillHistorySignal(List<GraphicHistorySignal> historySignalList, List<GraphicHistorySignal> allHistorySignalList) {
        Map<String, List<GraphicHistorySignal>> graphicHistorySignalMap = historySignalList.stream()
                                                                                           .sorted(Comparator.comparing(GraphicHistorySignal::getTime))
                                                                                           .collect(Collectors.groupingBy(GraphicHistorySignal::getTime, LinkedHashMap::new, Collectors.toList()));
        List<GraphicMultiSampleSignal> graphicMultiSampleSignalList = new ArrayList<>();
        for (Map.Entry<String, List<GraphicHistorySignal>> entry : graphicHistorySignalMap.entrySet()) {
            GraphicMultiSampleSignal graphicMultiSampleSignal = new GraphicMultiSampleSignal();
            //不相等需要填充
            if (allHistorySignalList.size() != entry.getValue().size()) {
                this.fillHistorySignalData(allHistorySignalList, entry.getValue(),entry.getKey());
            }
            graphicMultiSampleSignal.setSampleTime(entry.getKey());
            graphicMultiSampleSignal.setHistorySignalList(entry.getValue());
            graphicMultiSampleSignalList.add(graphicMultiSampleSignal);
        }
        return graphicMultiSampleSignalList;
    }

    /**
     * 填充缺失的历史数据
     *
     * @param allHistorySignal  全的历史信号
     * @param fillHistorySignal 需要填充的信号
     * @param sampleTime        采集时间
     */
    private void fillHistorySignalData(List<GraphicHistorySignal> allHistorySignal, List<GraphicHistorySignal> fillHistorySignal, String sampleTime) {
        Set<String> signalIdSet = fillHistorySignal.stream()
                                                   .map(GraphicHistorySignal::getSignalId)
                                                   .collect(Collectors.toSet());
        for (GraphicHistorySignal graphicHistorySignal : allHistorySignal) {
            if (!signalIdSet.contains(graphicHistorySignal.getSignalId())) {
                GraphicHistorySignal historySignal = BeanUtil.toBean(graphicHistorySignal, GraphicHistorySignal.class);
                historySignal.setPointValue("-");
                DateTime dateTime = DateUtil.parse(sampleTime);
                historySignal.setTime(dateTime);
                fillHistorySignal.add(historySignal);
            }
        }
        fillHistorySignal.sort(Comparator.comparing(GraphicHistorySignal::getSortIndex));
    }

    @Override
    public List<GraphicHistorySignal> getHistorySignalByBaseTypeId(Date startTime, Date endTime, Integer equipmentId, Long baseTypeId) {
        List<Long> list = new ArrayList<>();
        list.add(baseTypeId);
        List<ConfigSignalItem> configSignalItems = configSignalManager.getConfigSignalByEquipmentIdAndBaseTypeId(equipmentId, list);
        if (configSignalItems.isEmpty()) {
            return new ArrayList<>();
        }
        return getGraphicHistory(startTime, endTime, equipmentId, configSignalItems.get(0));
    }


    public List<GraphicHistorySignal> getGraphicHistory(Date startTime, Date endTime, Integer equipmentId, ConfigSignalItem configSignalItem) {
        if (ObjectUtil.isNull(configSignalItem)) {
            return Collections.emptyList();
        }
        List<HistorySignal> historySignalByDuration = historySignalManager.findHistorySignalByDuration(startTime, endTime, equipmentId, configSignalItem.getSignalId(), null, isOpenFiveMinuteStorage());
        Collection<HistorySignal> deduplicatedList = historySignalByDuration.stream()
                                                                      .collect(Collectors.toMap(
                                                                              signal -> signal.getSignalId() + "_" + signal.getTime(), // 以 SignalId 和 time 组合作为键
                                                                              signal -> signal,
                                                                              (existing, replacement) -> existing, // 如果键重复，保留已有的（第一个）
                                                                              LinkedHashMap::new // 保持插入顺序
                                                                      ))
                                                                      .values();// 收集去重后的结果到列表中
        //扩展基本的历史信号字段 设备名称、单位、基类名称、支路id
        long baseTypeId = Optional.ofNullable(configSignalItem.getBaseTypeId()).orElse(0L);
        List<GraphicHistorySignal> graphicHistorySignals = BeanUtil.copyToList(deduplicatedList, GraphicHistorySignal.class);
        String equipmentName = Optional.ofNullable(equipment.findById(equipmentId)).orElse(new Equipment()).getEquipmentName();
        String baseTypeName = Optional.ofNullable(signalBaseDicService.findById(baseTypeId)).orElse(new SignalBaseDic()).getBaseTypeName();
        for (GraphicHistorySignal graphicHistorySignal : graphicHistorySignals) {
            graphicHistorySignal.setEquipmentName(equipmentName);
            graphicHistorySignal.setUnit(configSignalItem.getUnit());
            graphicHistorySignal.setBaseTypeName(baseTypeName);
            graphicHistorySignal.setSignalName(configSignalItem.getSignalName());
            graphicHistorySignal.setBranchId(baseTypeId % 1000);
        }
        return graphicHistorySignals;
    }

    @Override
    public List<HistorySignal> getMultipleHistorySignalBySignalIds(Date startTime, Date endTime, List<EquipmentSignalIndexDTO> query) {
        return historySignalManager.findMultipleHistorySignalByDuration(startTime, endTime, query, null, isOpenFiveMinuteStorage());
    }


    @Override
    public Map<String, Map<String, String>> getMultipleHistorySignalMapBySignalIds(Date startTime, Date endTime, List<EquipmentSignalIndexDTO> query) {
        List<HistorySignal> result = historySignalManager.findMultipleHistorySignalByDuration(startTime, endTime, query, null, isOpenFiveMinuteStorage());
        if (result == null || result.isEmpty()){
            return new HashMap<>();
        }
        Map<String, List<HistorySignal>> collect = result.stream().collect(Collectors.groupingBy(HistorySignal::getSignalId));
        Map<String, Map<String, String>> rMap = new HashMap<>();
        for (String signalId : collect.keySet()) {
            Map<String, String> signalMap = new HashMap<>();
            List<HistorySignal> signals = collect.get(signalId);
            for (HistorySignal s : signals) {

                signalMap.put(s.getTime(),s.getPointValue());
            }
            rMap.put(signalId, signalMap);
        }
        return rMap;
    }


}

