package com.siteweb.hmi.service;

import com.siteweb.hmi.entity.ChannelData;
import com.siteweb.hmi.entity.ComtradeAnalysis;
import com.siteweb.hmi.entity.ComtradeConfig;

import java.io.File;
import java.util.List;

/**
 * COMTRADE文件解析服务接口
 */
public interface ComtradeParser {
    
    /**
     * 解析配置文件
     * @param cfgFile 配置文件
     * @return 配置信息对象
     */
    ComtradeConfig parseConfigFile(File cfgFile);
    
    /**
     * 解析数据文件
     * @param datFile 数据文件
     * @param config 配置信息
     * @return 通道数据列表
     */
    List<ChannelData> parseDataFile(File datFile, ComtradeConfig config);
    

    /**
     * 计算对称分量、不平衡度以及相量数据。
     *
     * @param config COMTRADE配置信息
     * @param calculationWindowStartTime 计算窗口开始时间 (格式: yyyy-MM-dd HH:mm:ss.SSSSSS)
     * @param calculationWindowEndTime 计算窗口结束时间 (格式: yyyy-MM-dd HH:mm:ss.SSSSSS)
     * @return 包含对称分量、不平衡度及相量数据的分析结果对象
     */
    ComtradeAnalysis getComtradeAnalysis(ComtradeConfig config, String calculationWindowStartTime, String calculationWindowEndTime);

    /**
     * 根据设备ID和不含后缀的文件名解析COMTRADE数据文件。
     *
     * @param equipmentId 设备ID
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @return 解析后的通道数据列表
     */
    List<ChannelData> parseDataFileByName(Integer equipmentId, String fileNameWithoutExtension);
    
    /**
     * 根据设备ID、文件名和计算窗口时间过滤COMTRADE数据，并返回包含过滤后通道数据的配置对象
     *
     * @param equipmentId 设备ID
     * @param fileName 文件名（不含后缀）
     * @param calculationWindowStartTime 计算窗口开始时间
     * @param calculationWindowEndTime 计算窗口结束时间
     * @return 包含过滤后通道数据的COMTRADE配置对象。
     */
    ComtradeConfig getFilteredComtradeConfig(Integer equipmentId, String fileName, String calculationWindowStartTime, String calculationWindowEndTime);
    
    /**
     * 导出COMTRADE数据为CSV文件
     *
     * @param equipmentId 设备ID
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @param outputFilePath 输出CSV文件路径
     * @return 导出的数据行数
     */
    long exportComtradeDataToCsv(Integer equipmentId, String fileNameWithoutExtension, String outputFilePath);
    
    /**
     * 计算工频周期 20ms RMS值
     * 将原始数据按20ms窗口分割，计算每个窗口的RMS值
     * 
     * @param equipmentId 设备ID
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @return 包含20ms RMS值的通道数据列表
     */
    List<ChannelData> calculate20msRmsValues(Integer equipmentId, String fileNameWithoutExtension);
    
    /**
     * 计算工频周期 RMS值（支持自定义窗口大小）
     * 
     * @param equipmentId 设备ID
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @param windowSizeMs 窗口大小（毫秒）
     * @return 包含RMS值的通道数据列表
     */
    List<ChannelData> calculateRmsValues(Integer equipmentId, String fileNameWithoutExtension, double windowSizeMs);
} 