package com.siteweb.hmi.service;

import com.siteweb.hmi.entity.ComtradeFilePair;
import com.siteweb.hmi.entity.ComtradeFileRetentionPolicy;

import java.io.File;
import java.util.List;

/**
 * COMTRADE文件清理服务接口
 * 
 * <AUTHOR>
 * @since 2025/5/22
 */
public interface ComtradeFileCleanupService {
    
    /**
     * 清理指定设备的COMTRADE文件
     * 
     * @param equipmentId 设备ID
     * @return 清理的文件对数量
     */
    int cleanupComtradeFiles(Integer equipmentId);
    
    /**
     * 清理指定目录的COMTRADE文件
     * 
     * @param directory 目录路径
     * @param policy 保留策略
     * @return 清理的文件对数量
     */
    int cleanupComtradeFiles(File directory, ComtradeFileRetentionPolicy policy);
    
    /**
     * 扫描目录中的COMTRADE文件对
     * 
     * @param directory 目录路径
     * @return 文件对列表，按时间排序（最旧的在前）
     */
    List<ComtradeFilePair> scanComtradeFiles(File directory);
    
    /**
     * 获取当前保留策略配置
     * 
     * @return 保留策略配置
     */
    ComtradeFileRetentionPolicy getRetentionPolicy();
    
    /**
     * 更新保留策略配置
     * 
     * @param policy 新的保留策略配置
     */
    void updateRetentionPolicy(ComtradeFileRetentionPolicy policy);
    
    /**
     * 获取指定设备的文件统计信息
     * 
     * @param equipmentId 设备ID
     * @return 文件统计信息（文件对数量、总大小等）
     */
    ComtradeFileStatistics getFileStatistics(Integer equipmentId);
    
    /**
     * 手动触发清理指定设备的文件
     * 
     * @param equipmentId 设备ID
     * @param forceCleanup 是否强制清理（忽略启用状态）
     * @return 清理结果
     */
    CleanupResult manualCleanup(Integer equipmentId, boolean forceCleanup);
    
    /**
     * 文件统计信息
     */
    class ComtradeFileStatistics {
        private int totalFilePairs;
        private long totalSize;
        private int completeFilePairs;
        private int orphanedFiles;
        
        // 构造函数、getter和setter省略...
        public ComtradeFileStatistics(int totalFilePairs, long totalSize, int completeFilePairs, int orphanedFiles) {
            this.totalFilePairs = totalFilePairs;
            this.totalSize = totalSize;
            this.completeFilePairs = completeFilePairs;
            this.orphanedFiles = orphanedFiles;
        }
        
        public int getTotalFilePairs() { return totalFilePairs; }
        public long getTotalSize() { return totalSize; }
        public int getCompleteFilePairs() { return completeFilePairs; }
        public int getOrphanedFiles() { return orphanedFiles; }
    }
    
    /**
     * 清理结果
     */
    class CleanupResult {
        private boolean success;
        private int deletedFilePairs;
        private String message;
        private List<String> errors;
        
        public CleanupResult(boolean success, int deletedFilePairs, String message, List<String> errors) {
            this.success = success;
            this.deletedFilePairs = deletedFilePairs;
            this.message = message;
            this.errors = errors;
        }
        
        public boolean isSuccess() { return success; }
        public int getDeletedFilePairs() { return deletedFilePairs; }
        public String getMessage() { return message; }
        public List<String> getErrors() { return errors; }
    }
} 