package com.siteweb.hmi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.hmi.entity.GraphicPage;
import com.siteweb.hmi.mapper.GraphicPageMapper;
import com.siteweb.hmi.service.DefaultGraphicPageService;
import com.siteweb.hmi.service.GraphicPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/02/06
 */
@Service
public class DefaultGraphicPageServiceImpl implements DefaultGraphicPageService {
    @Autowired
    private GraphicPageMapper graphicPageMapper;

    @Autowired
    private GraphicPageService graphicPageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDefaultGraphic(Integer id) {
        GraphicPage graphicPage = graphicPageService.findById(id);
        if (ObjectUtil.isNull(graphicPage)) {
            return 0;
        }
        //设置相关组态页isDefault全为false
        List<Integer> graphicPageIds = graphicPageService.findByGraphicPageByPageCategoryAndObjectId(graphicPage.getPageCategory(), graphicPage.getObjectId())
                                                         .stream()
                                                         .map(GraphicPage::getId)
                                                         .toList();
        graphicPageMapper.batchUpdateGraphicDefaultState(graphicPageIds);
        //设置默认组态页
        graphicPage.setIsDefault(GlobalConstants.YES);
        return graphicPageMapper.updateById(graphicPage);
    }

    @Override
    public GraphicPage getDefaultGraphicPage(Integer pageCategory, Integer objectId) {
        return graphicPageMapper.selectOne(Wrappers.lambdaQuery(GraphicPage.class)
                                                   .eq(GraphicPage::getPageCategory, pageCategory)
                                                   .eq(GraphicPage::getObjectId, objectId)
                                                   .eq(GraphicPage::getIsDefault, true));
    }
}
