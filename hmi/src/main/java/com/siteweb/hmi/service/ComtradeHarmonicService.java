package com.siteweb.hmi.service;

import com.siteweb.hmi.entity.HarmonicAnalysis;
import com.siteweb.hmi.entity.ChannelHarmonic;
import com.siteweb.hmi.entity.ComtradeConfig;

import java.util.List;
import java.util.Map;

/**
 * COMTRADE谐波分析服务接口
 */
public interface ComtradeHarmonicService {
    
    /**
     * 对指定的COMTRADE文件进行完整的谐波分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @return 谐波分析结果
     */
    HarmonicAnalysis analyzeHarmonics(Integer equipmentId, String fileName);
    
    /**
     * 对指定通道进行详细谐波分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @param channelName 通道名称（如：Ia, Ib, Ic等）
     * @param maxHarmonicOrder 最大谐波次数（默认为427）
     * @return 通道谐波分析结果
     */
    ChannelHarmonic analyzeChannelHarmonics(Integer equipmentId, String fileName, 
                                           String channelName, Integer maxHarmonicOrder);
    
    /**
     * 获取三相电流谐波对比分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @return 三相电流谐波分析结果列表
     */
    List<ChannelHarmonic> analyzeThreePhaseCurrentHarmonics(Integer equipmentId, String fileName);
    
    /**
     * 获取三相电压谐波对比分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @return 三相电压谐波分析结果列表
     */
    List<ChannelHarmonic> analyzeThreePhaseVoltageHarmonics(Integer equipmentId, String fileName);
    
    /**
     * 基于现有的ComtradeConfig进行谐波分析
     * 
     * @param config 已经包含通道数据的COMTRADE配置
     * @return 谐波分析结果
     */
    HarmonicAnalysis analyzeHarmonicsFromConfig(ComtradeConfig config);
    
    /**
     * 导出谐波分析结果为CSV文件
     * 
     * @param harmonicAnalysis 谐波分析结果
     * @param outputFilePath 输出文件路径
     * @return 是否导出成功
     */
    boolean exportHarmonicAnalysisToCSV(HarmonicAnalysis harmonicAnalysis, String outputFilePath);
    
    /**
     * 获取指定通道的详细谐波数据（427次谐波）
     * 
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @param channelName 通道名称
     * @return 427次谐波的详细数据，每个内层List包含[总谐波失真%, 偶谐波%, 奇谐波%]
     */
    List<List<String>> getDetailedHarmonicData(Integer equipmentId, String fileName, String channelName);
    
    /**
     * 获取指定次数谐波的统计信息
     * 
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @param channelName 通道名称
     * @param harmonicOrder 谐波次数（1-427）
     * @return 指定次数谐波的统计信息 [总谐波失真%, 偶谐波%, 奇谐波%]
     */
    List<String> getHarmonicByOrder(Integer equipmentId, String fileName, String channelName, Integer harmonicOrder);
    
    /**
     * 导出指定通道的427次谐波详细数据为CSV
     * 
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @param channelName 通道名称
     * @param outputFilePath 输出文件路径
     * @return 是否导出成功
     */
    boolean exportChannelDetailedHarmonicsToCSV(Integer equipmentId, String fileName, 
                                               String channelName, String outputFilePath);
    
    /**
     * 获取所有通道的谐波对比数据
     * 
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @return 所有通道的谐波对比数据，格式：Map<通道名, List<List<String>>>
     */
    Map<String, List<List<String>>> getAllChannelsHarmonicComparison(Integer equipmentId, String fileName);
} 