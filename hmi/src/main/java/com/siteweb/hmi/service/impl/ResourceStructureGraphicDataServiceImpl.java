package com.siteweb.hmi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.hmi.dto.EquipmentGraphicData;
import com.siteweb.hmi.dto.FloorGraphicData;
import com.siteweb.monitoring.dto.ResourceStructureAlarmState;
import com.siteweb.hmi.dto.RoomGraphicData;
import com.siteweb.hmi.service.ResourceStructureGraphicDataService;
import com.siteweb.hmi.util.GraphicDataUtil;
import com.siteweb.hmi.vo.EquipmentCategoryDataRequest;
import com.siteweb.hmi.vo.GraphicRoomDataRequest;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.entity.RoomCategory;
import com.siteweb.utility.manager.ResourceStructureTypeManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("ResourceStructureGraphicDataService")
public class ResourceStructureGraphicDataServiceImpl implements ResourceStructureGraphicDataService {
    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    ActiveEventManager activeEventManager;

    @Autowired
    ActiveSignalManager activeSignalManager;

    @Autowired
    GraphicDataUtil graphicDataUtil;

    @Autowired
    ResourceStructureTypeManager resourceStructureTypeManager;

    @Autowired
    ResourceStructureService resourceStructureService;

    /**
     * 获取所有楼层-房间的对应信息
     *
     * @return 楼层信息
     */
    public List<FloorGraphicData> getAllFloorGraphicData(String buildingIds) {
        LinkedHashMap<Integer, FloorGraphicData> floorGraphicDataHashMap = new LinkedHashMap<>();
        List<ResourceStructure> floors = resourceStructureManager.getResourceStructureLstByTypeId(SourceType.FLOOR.value());
        for (ResourceStructure floor : floors) {
            if (buildingIds == null || buildingIds.equals("") || buildingIds.contains(floor.getParentResourceStructureId().toString())) {
                FloorGraphicData floorGraphicData = new FloorGraphicData();
                floorGraphicData.setId(floor.getResourceStructureId());
                floorGraphicData.setName(floor.getResourceStructureName());
                floorGraphicData.setSortValue(floor.getSortValue());
                floorGraphicDataHashMap.put(floor.getResourceStructureId(), floorGraphicData);
            }
        }
        List<RoomGraphicData> roomGraphicDatas = getAllRoomGraphicData();
        for (RoomGraphicData roomGraphicData : roomGraphicDatas) {
            if (floorGraphicDataHashMap.containsKey(roomGraphicData.getFloorId())) {
                floorGraphicDataHashMap.get(roomGraphicData.getFloorId()).getChildren().add(roomGraphicData);
            }
        }
        return new ArrayList<>(floorGraphicDataHashMap.values());
    }


    /**
     * 获取所有房间信息
     *
     * @return 房间列表
     */
    private List<RoomGraphicData> getAllRoomGraphicData() {
        List<RoomGraphicData> roomGraphicDatas = new ArrayList<>();
        List<ResourceStructure> rooms = resourceStructureManager.getResourceStructureLstByTypeId(SourceType.ROOM.value());
        //constructRoomList();
        for (ResourceStructure room : rooms) {
            RoomGraphicData roomGraphicData = new RoomGraphicData();
            roomGraphicData.setId(room.getResourceStructureId());
            roomGraphicData.setFloorId(room.getParentResourceStructureId());
            roomGraphicData.setName(room.getResourceStructureName());
            roomGraphicData.setSortValue(room.getSortValue());
            JsonNode extendedField = room.getExtendedField();
            if(extendedField != null && extendedField.get("roomCategory") != null){
                int roomCategoryId = extendedField.get("roomCategory").asInt();
                roomGraphicData.setRoomCategoryId(roomCategoryId);
                RoomCategory roomCategory = resourceStructureTypeManager.findRoomCategoryById(roomCategoryId);
                if(roomCategory != null)
                 roomGraphicData.setColor(roomCategory.getColor());
            }

            roomGraphicData.setAlarmState(activeEventManager.existsActiveByResourceStructureId(room.getResourceStructureId()));
            roomGraphicDatas.add(roomGraphicData);
        }
        roomGraphicDatas.sort(Comparator.comparing(RoomGraphicData::getSortValue));
        return roomGraphicDatas;
    }


    /**
     * 按设备类（基类ID或测点ID）获取设备实时信号
     *
     * @param equipmentCategoryDataRequest 请求实体
     * @return 设备信号
     */
    public List<FloorGraphicData> getDeviceCategoryFloorGraphicDataByCategoryId(EquipmentCategoryDataRequest equipmentCategoryDataRequest) {
        if (equipmentCategoryDataRequest == null) {
            return new ArrayList<>();
        }
        return getFloorGraphicDataByDeviceCategoryDataRequest(equipmentCategoryDataRequest);
    }

    @Override
    public List<RoomGraphicData> getRoomGraphicDataByDeviceCategory(List<GraphicRoomDataRequest> graphicRoomDataRequests) {
        List<RoomGraphicData> roomGraphicDataList = new ArrayList<>();
        if (graphicRoomDataRequests.isEmpty()) {
            return roomGraphicDataList;
        }
        List<GraphicRoomDataRequest> fetchDataFromGraphicRequests = new ArrayList<>();
        List<GraphicRoomDataRequest> fetchDataFromAPIRequests = new ArrayList<>();
        for (GraphicRoomDataRequest graphicRoomDataRequest : graphicRoomDataRequests) {
            if (graphicRoomDataRequest.getFetchDataFromAPI() != null && graphicRoomDataRequest.getFetchDataFromAPI()) {
                fetchDataFromAPIRequests.add(graphicRoomDataRequest);
            } else {
                fetchDataFromGraphicRequests.add(graphicRoomDataRequest);
            }
        }

        List<RoomGraphicData> graphicRoomDatas = getGraphicRoomDataRequestData(graphicRoomDataRequests);

        /* if (!fetchDataFromAPIRequests.isEmpty()) {
            List<RoomGraphicData> apiRoomDatas = estimateDischargeTimeService.getAPIRoomDataRequestData(fetchDataFromAPIRequests);
            if (!apiRoomDatas.isEmpty()) {
                roomGraphicDataList.addAll(apiRoomDatas);
            }
        }*/
        return graphicRoomDatas;
    }

    @Override
    public List<ResourceStructureAlarmState> getResourceStructureAlarmStateByGroup(List<Integer> dataIds, Integer userId) {
        List<ResourceStructureAlarmState> result = new ArrayList<>();
        for(Integer resourceStructureId:dataIds){
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
            if(!ObjectUtil.isNull(resourceStructure)){
                result.add(new ResourceStructureAlarmState(resourceStructure));
            }  else{
                result.add(new ResourceStructureAlarmState(resourceStructureId, "not exists", 0,0, false));
            }
        }
       //获取权限
       Map<Integer, ResourceStructure> resourceStructureMap = resourceStructureService.findResourceStructureByUserId(userId).stream().collect(
                Collectors.toMap(ResourceStructure::getResourceStructureId, p -> p));
        //获取层级告警状态
        Map<Integer, ResourceStructureAlarmState> resourceStructureAlarmMap =resourceStructureService.getResourceStructureAlarmState(dataIds).stream().collect(
                Collectors.toMap(ResourceStructureAlarmState::getResourceStructureId, p -> p));
        for(ResourceStructureAlarmState resourceStructureAlarmState:result){
            if(resourceStructureMap.containsKey(resourceStructureAlarmState.getResourceStructureId())){
                resourceStructureAlarmState.setFilter(true);
            }
            if(resourceStructureAlarmMap.containsKey(resourceStructureAlarmState.getResourceStructureId())){
                resourceStructureAlarmState.setEventLevel(resourceStructureAlarmMap.get(resourceStructureAlarmState.getResourceStructureId()).getEventLevel());
            }
        }
        return  result;
    }


    private List<RoomGraphicData> getGraphicRoomDataRequestData(List<GraphicRoomDataRequest> graphicRoomDataRequests) {
        List<RoomGraphicData> roomGraphicDataList = new ArrayList<>();
        for (GraphicRoomDataRequest graphicRoomDataRequest : graphicRoomDataRequests) {
            RoomGraphicData roomGraphicData = new RoomGraphicData();
            roomGraphicData.setUuid(graphicRoomDataRequest.getUuid());
            roomGraphicData.setId(graphicRoomDataRequest.getObjectId());
            List<EquipmentGraphicData> leq = new ArrayList<>();
            List<EquipmentGraphicData> deviceGraphicDataList = getDeviceGraphicDatasByCategoryId(graphicRoomDataRequest.getDeviceCategory(), graphicRoomDataRequest.getBaseTypeId());
            for(EquipmentGraphicData equipmentGraphicData:deviceGraphicDataList){
                if(equipmentGraphicData.getResourceStructureId().equals(graphicRoomDataRequest.getObjectId())){
                    leq.add(equipmentGraphicData);
                }
            }
            roomGraphicData.setChildren(leq);
            roomGraphicDataList.add(roomGraphicData);
        }
        return roomGraphicDataList;
    }

    private List<FloorGraphicData> getFloorGraphicDataByDeviceCategoryDataRequest(EquipmentCategoryDataRequest equipmentCategoryDataRequest) {
        List<RoomGraphicData> roomGraphicDatas = getDeviceCategoryRoomGraphicDatasByCategoryId(equipmentCategoryDataRequest);
        LinkedHashMap<Integer, FloorGraphicData> floorGraphicDataHashMap = new LinkedHashMap<>();
        for (RoomGraphicData roomGraphicData : roomGraphicDatas) {
            if (floorGraphicDataHashMap.containsKey(roomGraphicData.getFloorId())) {
                floorGraphicDataHashMap.get(roomGraphicData.getFloorId()).getChildren().add(roomGraphicData);
            } else {
                FloorGraphicData floorGraphicData = new FloorGraphicData();
                ResourceStructure floor = resourceStructureManager.getResourceStructureById(roomGraphicData.getFloorId());
                if (floor != null) {
                    floorGraphicData.setName(floor.getResourceStructureName());
                    floorGraphicData.setId(floor.getResourceStructureId());
                    floorGraphicData.getChildren().add(roomGraphicData);
                    floorGraphicDataHashMap.put(floor.getResourceStructureId(), floorGraphicData);
                }
            }
        }
        List<FloorGraphicData> floorGraphicDataList = new ArrayList<>(floorGraphicDataHashMap.values());
        floorGraphicDataList.sort(Comparator.comparing(FloorGraphicData::getName));

        //添加设备权限过滤
        /* if (!floorGraphicDataList.isEmpty() && deviceCategoryDataRequest.getPersonId() != null) {
            List<PersonDeviceMap> personDeviceMapList = personDeviceMapService.findByPersonId(deviceCategoryDataRequest.getPersonId());
            floorGraphicDataList = filterDeviceByPersonDeviceMap(floorGraphicDataList, personDeviceMapList);
        } */
        return floorGraphicDataList;
    }


    private List<RoomGraphicData> getDeviceCategoryRoomGraphicDatasByCategoryId(EquipmentCategoryDataRequest equipmentCategoryDataRequest) {
        List<EquipmentGraphicData> deviceGraphicDatas = getDeviceGraphicDatasByCategoryId(equipmentCategoryDataRequest.getEquipmentCategory(), equipmentCategoryDataRequest.getBaseTypeId());
        LinkedHashMap<Integer, RoomGraphicData> deviceCategoryRoomGraphicDatas = new LinkedHashMap<>();
        List<ResourceStructure> rooms =  resourceStructureManager.getResourceStructureLstByTypeId(5);
        for (EquipmentGraphicData graphicData : deviceGraphicDatas) {
            Integer roomId = graphicData.getResourceStructureId();
            if (deviceCategoryRoomGraphicDatas.containsKey(graphicData.getResourceStructureId())) {
                deviceCategoryRoomGraphicDatas.get(roomId).getChildren().add(graphicData);
            } else {
                RoomGraphicData roomGraphicData = new RoomGraphicData();
                ResourceStructure room =resourceStructureManager.getResourceStructureById(roomId);
                if (room != null) {
                    roomGraphicData.setName(room.getResourceStructureName());
                    roomGraphicData.setId(room.getResourceStructureId());
                    roomGraphicData.setFloorId(room.getParentResourceStructureId());
                    roomGraphicData.getChildren().add(graphicData);
                    deviceCategoryRoomGraphicDatas.put(room.getResourceStructureId(), roomGraphicData);
                }
            }
        }

        /*增加排序*/
        List<RoomGraphicData> roomGraphicDataList = new ArrayList<>(deviceCategoryRoomGraphicDatas.values());
        roomGraphicDataList.sort(Comparator.comparing(RoomGraphicData::getName));
        for (RoomGraphicData roomGraphicData : roomGraphicDataList) {
            roomGraphicData.getChildren().sort(Comparator.comparing(EquipmentGraphicData::getEquipmentName));
        }
        return roomGraphicDataList;
    }

    private List<EquipmentGraphicData> getDeviceGraphicDatasByCategoryId(Integer categoryId, List<Long> baseTypeId) {
        //获取实时信号
        List<EquipmentActiveSignal>  equipmentActiveSignals = activeSignalManager.getActiveSignalsByEquipmentCategoryAndBaseTypeId(categoryId, baseTypeId);
        //转换统一的设备信号响应体
        return graphicDataUtil.activeSignalConvertEquipmentGraphicData(equipmentActiveSignals);
    }
}
