package com.siteweb.hmi.service.impl;

import com.siteweb.hmi.entity.SceneCompMap;
import com.siteweb.hmi.mapper.SceneCompMapMapper;
import com.siteweb.hmi.service.SceneCompMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("SceneCompMapService")
public class SceneCompMapServiceImpl implements SceneCompMapService {
    @Autowired
    SceneCompMapMapper sceneCompMapMapper;

    @Override
    public List<SceneCompMap> findByPageCategoryAndSceneId(Integer pageCategory) {
        Integer sceneId = 1;
        return sceneCompMapMapper.findByPageCategoryAndSceneId(pageCategory, sceneId);
    }

    @Override
    public List<SceneCompMap> findByAll() {
        return sceneCompMapMapper.selectList(null);
    }
}
