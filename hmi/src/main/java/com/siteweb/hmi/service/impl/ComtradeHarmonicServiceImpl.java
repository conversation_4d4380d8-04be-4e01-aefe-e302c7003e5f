package com.siteweb.hmi.service.impl;

import com.siteweb.hmi.entity.*;
import com.siteweb.hmi.service.ComtradeHarmonicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;

import java.util.*;

/**
 * COMTRADE谐波分析服务实现类
 */
@Slf4j
@Service
public class ComtradeHarmonicServiceImpl implements ComtradeHarmonicService {

    @Autowired
    private ComtradeParserImpl comtradeParser;

    @Override
    public HarmonicAnalysis analyzeHarmonics(Integer equipmentId, String fileName) {
        log.info("开始对设备 {} 的文件 {} 进行谐波分析", equipmentId, fileName);

        try {
            // 1. 获取COMTRADE配置和数据（不进行时间过滤）
            ComtradeConfig config = comtradeParser.getComtradeConfig(equipmentId, fileName);

            if (config == null) {
                log.error("无法获取COMTRADE配置，设备ID: {}, 文件: {}", equipmentId, fileName);
                return createFailedAnalysis(equipmentId, fileName, "无法获取COMTRADE配置");
            }

            return analyzeHarmonicsFromConfig(config);

        } catch (Exception e) {
            log.error("谐波分析过程中发生错误: {}", e.getMessage(), e);
            return createFailedAnalysis(equipmentId, fileName, "分析过程中发生错误: " + e.getMessage());
        }
    }

    @Override
    public ChannelHarmonic analyzeChannelHarmonics(Integer equipmentId, String fileName,
                                                   String channelName, Integer maxHarmonicOrder) {
        log.info("开始对通道 {} 进行谐波分析", channelName);

        try {
            // 获取COMTRADE配置和数据
            ComtradeConfig config = comtradeParser.getComtradeConfig(equipmentId, fileName);

            if (config == null || config.getChannelDataList() == null) {
                log.error("无法获取通道数据进行谐波分析");
                return null;
            }

            // 查找指定通道
            ChannelData channelData = findChannelData(config.getChannelDataList(), channelName);
            if (channelData == null) {
                log.warn("未找到指定通道: {}", channelName);
                return null;
            }

            // 进行谐波分析
            return comtradeParser.analyzeChannelHarmonics(
                    channelData,
                    config.getLineFrequency().doubleValue(),
                    config.getSamplingRate().doubleValue(),
                    maxHarmonicOrder != null ? maxHarmonicOrder : 427
            );

        } catch (Exception e) {
            log.error("通道 {} 谐波分析失败: {}", channelName, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<ChannelHarmonic> analyzeThreePhaseCurrentHarmonics(Integer equipmentId, String fileName) {
        log.info("开始对三相电流进行谐波分析");

        try {
            ComtradeConfig config = comtradeParser.getComtradeConfig(equipmentId, fileName);

            if (config == null) {
                log.error("无法获取COMTRADE配置进行三相电流谐波分析");
                return new ArrayList<>();
            }

            // 现在直接调用原方法，因为ChannelHarmonic已经使用List<List<String>>格式
            List<ChannelHarmonic> results = new ArrayList<>();
            String[] currentChannels = {"Ia", "Ib", "Ic"};
            
            for (String channelName : currentChannels) {
                ChannelData channelData = findChannelData(config.getChannelDataList(), channelName);
                if (channelData != null) {
                    try {
                        ChannelHarmonic harmonicResult = comtradeParser.analyzeChannelHarmonics(
                                channelData,
                                config.getLineFrequency().doubleValue(),
                                config.getSamplingRate().doubleValue(),
                                427
                        );
                        if (harmonicResult != null) {
                            results.add(harmonicResult);
                        }
                    } catch (Exception e) {
                        log.error("通道 {} 谐波分析时发生异常: {}", channelName, e.getMessage(), e);
                    }
                }
            }
            
            return results;

        } catch (Exception e) {
            log.error("三相电流谐波分析失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ChannelHarmonic> analyzeThreePhaseVoltageHarmonics(Integer equipmentId, String fileName) {
        log.info("开始对三相电压进行谐波分析");

        try {
            ComtradeConfig config = comtradeParser.getComtradeConfig(equipmentId, fileName);

            if (config == null) {
                log.error("无法获取COMTRADE配置进行三相电压谐波分析");
                return new ArrayList<>();
            }

            // 现在直接调用原方法，因为ChannelHarmonic已经使用List<List<String>>格式
            List<ChannelHarmonic> results = new ArrayList<>();
            String[] voltageChannels = {"Ua", "Ub", "Uc"};
            
            for (String channelName : voltageChannels) {
                ChannelData channelData = findChannelData(config.getChannelDataList(), channelName);
                if (channelData != null) {
                    try {
                        ChannelHarmonic harmonicResult = comtradeParser.analyzeChannelHarmonics(
                                channelData,
                                config.getLineFrequency().doubleValue(),
                                config.getSamplingRate().doubleValue(),
                                427
                        );
                        if (harmonicResult != null) {
                            results.add(harmonicResult);
                        }
                    } catch (Exception e) {
                        log.error("通道 {} 谐波分析时发生异常: {}", channelName, e.getMessage(), e);
                    }
                }
            }
            
            return results;

        } catch (Exception e) {
            log.error("三相电压谐波分析失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public HarmonicAnalysis analyzeHarmonicsFromConfig(ComtradeConfig config) {
        if (config == null) {
            return createFailedAnalysis(null, null, "配置为空");
        }

        HarmonicAnalysis harmonicAnalysis = new HarmonicAnalysis();

        try {
            // 基本信息设置
            harmonicAnalysis.setEquipmentId(null); // 从config无法获得
            harmonicAnalysis.setFileName(null);    // 从config无法获得
            harmonicAnalysis.setAnalysisStartTime(config.getStartTime());
            harmonicAnalysis.setAnalysisEndTime(config.getTriggerTime());
            harmonicAnalysis.setNominalFrequency(config.getLineFrequency().doubleValue());
            harmonicAnalysis.setSampleRate(config.getSamplingRate().doubleValue());

            // 计算分析时间窗口长度（使用整个数据长度）
            calculateAnalysisWindowLength(harmonicAnalysis, config);

            // 进行三相电流谐波分析
            List<ChannelHarmonic> currentHarmonics = new ArrayList<>();
            String[] currentChannels = {"Ia", "Ib", "Ic"};
            for (String channelName : currentChannels) {
                ChannelData channelData = findChannelData(config.getChannelDataList(), channelName);
                if (channelData != null) {
                    try {
                        ChannelHarmonic harmonicResult = comtradeParser.analyzeChannelHarmonics(
                                channelData,
                                config.getLineFrequency().doubleValue(),
                                config.getSamplingRate().doubleValue(),
                                427
                        );
                        if (harmonicResult != null) {
                            currentHarmonics.add(harmonicResult);
                        }
                    } catch (Exception e) {
                        log.error("通道 {} 谐波分析时发生异常: {}", channelName, e.getMessage(), e);
                    }
                }
            }
            harmonicAnalysis.setCurrentHarmonics(currentHarmonics);

            // 进行三相电压谐波分析
            List<ChannelHarmonic> voltageHarmonics = new ArrayList<>();
            String[] voltageChannels = {"Ua", "Ub", "Uc"};
            for (String channelName : voltageChannels) {
                ChannelData channelData = findChannelData(config.getChannelDataList(), channelName);
                if (channelData != null) {
                    try {
                        ChannelHarmonic harmonicResult = comtradeParser.analyzeChannelHarmonics(
                                channelData,
                                config.getLineFrequency().doubleValue(),
                                config.getSamplingRate().doubleValue(),
                                427
                        );
                        if (harmonicResult != null) {
                            voltageHarmonics.add(harmonicResult);
                        }
                    } catch (Exception e) {
                        log.error("通道 {} 谐波分析时发生异常: {}", channelName, e.getMessage(), e);
                    }
                }
            }
            harmonicAnalysis.setVoltageHarmonics(voltageHarmonics);

            // 设置通道数量
            int channelCount = currentHarmonics.size() + voltageHarmonics.size();
            harmonicAnalysis.setChannelCount(channelCount);

            // 生成质量评估
            HarmonicQualityAssessment qualityAssessment = generateQualityAssessment(
                    currentHarmonics, voltageHarmonics);
            harmonicAnalysis.setQualityAssessment(qualityAssessment);

            // 设置分析状态
            if (currentHarmonics.isEmpty() && voltageHarmonics.isEmpty()) {
                harmonicAnalysis.setAnalysisStatus("FAILED");
                harmonicAnalysis.setMessage("未找到有效的通道数据进行谐波分析");
            } else if (currentHarmonics.size() < 3 && voltageHarmonics.size() < 3) {
                harmonicAnalysis.setAnalysisStatus("PARTIAL");
                harmonicAnalysis.setMessage("部分通道数据缺失，分析结果不完整");
            } else {
                harmonicAnalysis.setAnalysisStatus("SUCCESS");
                harmonicAnalysis.setMessage("谐波分析完成");
            }

            log.info("谐波分析完成，分析了 {} 个电流通道和 {} 个电压通道",
                    currentHarmonics.size(), voltageHarmonics.size());

            return harmonicAnalysis;

        } catch (Exception e) {
            log.error("谐波分析过程中发生错误: {}", e.getMessage(), e);
            return createFailedAnalysis(null, null, "分析过程中发生错误: " + e.getMessage());
        }
    }

    @Override
    public boolean exportHarmonicAnalysisToCSV(HarmonicAnalysis harmonicAnalysis, String outputFilePath) {
        log.info("开始导出谐波分析结果为CSV: {}", outputFilePath);

        try {
            List<String[]> csvData = new ArrayList<>();

            // 添加表头
            csvData.add(new String[]{
                    "通道名称", "通道类型", "单位", "有效值", "基波有效值", "THD(%)",
                    "奇谐波(%)", "偶谐波(%)", "K因数", "波峰因数", "波形因子",
                    "427次谐波详细数据(JSON格式)"
            });

            // 添加电流通道数据
            if (harmonicAnalysis.getCurrentHarmonics() != null) {
                for (ChannelHarmonic harmonic : harmonicAnalysis.getCurrentHarmonics()) {
                    // 将427次谐波数据转换为JSON字符串格式
                    String harmonicDetailsJson = convertHarmonicComponentsToJson(harmonic.getHarmonicComponents());
                    
                    csvData.add(new String[]{
                            harmonic.getChannelName(),
                            harmonic.getChannelType(),
                            harmonic.getUnit(),
                            formatDouble(harmonic.getEffectiveValue()),
                            formatDouble(harmonic.getFundamentalValue()),
                            formatDouble(harmonic.getTotalHarmonicDistortion()),
                            formatDouble(harmonic.getOddHarmonics()),
                            formatDouble(harmonic.getEvenHarmonics()),
                            formatDouble(harmonic.getKFactor()),
                            formatDouble(harmonic.getCrestFactor()),
                            formatDouble(harmonic.getFormFactor()),
                            harmonicDetailsJson
                    });
                }
            }

            // 添加电压通道数据
            if (harmonicAnalysis.getVoltageHarmonics() != null) {
                for (ChannelHarmonic harmonic : harmonicAnalysis.getVoltageHarmonics()) {
                    // 将427次谐波数据转换为JSON字符串格式
                    String harmonicDetailsJson = convertHarmonicComponentsToJson(harmonic.getHarmonicComponents());
                    
                    csvData.add(new String[]{
                            harmonic.getChannelName(),
                            harmonic.getChannelType(),
                            harmonic.getUnit(),
                            formatDouble(harmonic.getEffectiveValue()),
                            formatDouble(harmonic.getFundamentalValue()),
                            formatDouble(harmonic.getTotalHarmonicDistortion()),
                            formatDouble(harmonic.getOddHarmonics()),
                            formatDouble(harmonic.getEvenHarmonics()),
                            formatDouble(harmonic.getKFactor()),
                            formatDouble(harmonic.getCrestFactor()),
                            formatDouble(harmonic.getFormFactor()),
                            harmonicDetailsJson
                    });
                }
            }

            // 写入CSV文件
            try (CsvWriter writer = CsvUtil.getWriter(
                    java.nio.file.Paths.get(outputFilePath).toUri().getPath(),
                    java.nio.charset.StandardCharsets.UTF_8, false)) {

                for (String[] row : csvData) {
                    writer.writeLine(row);
                }
            }

            log.info("谐波分析结果成功导出为CSV: {}", outputFilePath);
            return true;

        } catch (Exception e) {
            log.error("导出谐波分析结果为CSV失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将427次谐波数据转换为JSON格式字符串
     * @param harmonicComponents 427次谐波数据
     * @return JSON格式字符串
     */
    private String convertHarmonicComponentsToJson(List<List<String>> harmonicComponents) {
        if (harmonicComponents == null || harmonicComponents.isEmpty()) {
            return "[]";
        }
        
        StringBuilder json = new StringBuilder("[");
        for (int i = 0; i < harmonicComponents.size(); i++) {
            List<String> harmonic = harmonicComponents.get(i);
            if (harmonic != null && harmonic.size() >= 3) {
                json.append("{")
                    .append("\"order\":").append(i + 1).append(",")
                    .append("\"thd\":\"").append(harmonic.get(0)).append("\",")
                    .append("\"even\":\"").append(harmonic.get(1)).append("\",")
                    .append("\"odd\":\"").append(harmonic.get(2)).append("\"")
                    .append("}");
                
                if (i < harmonicComponents.size() - 1) {
                    json.append(",");
                }
            }
        }
        json.append("]");
        
        return json.toString();
    }

    /**
     * 查找通道数据
     */
    private ChannelData findChannelData(List<ChannelData> channelDataList, String channelName) {
        if (channelDataList == null || channelName == null) {
            return null;
        }

        for (ChannelData channelData : channelDataList) {
            if (channelData.getName() != null && channelData.getName().equalsIgnoreCase(channelName)) {
                return channelData;
            }
        }
        return null;
    }

    /**
     * 创建失败的分析结果
     */
    private HarmonicAnalysis createFailedAnalysis(Integer equipmentId, String fileName, String errorMessage) {
        HarmonicAnalysis analysis = new HarmonicAnalysis();
        analysis.setEquipmentId(equipmentId);
        analysis.setFileName(fileName);
        analysis.setAnalysisStatus("FAILED");
        analysis.setMessage(errorMessage);
        analysis.setCurrentHarmonics(new ArrayList<>());
        analysis.setVoltageHarmonics(new ArrayList<>());
        analysis.setChannelCount(0);
        return analysis;
    }

    /**
     * 计算分析时间窗口长度（基于数据长度）
     */
    private void calculateAnalysisWindowLength(HarmonicAnalysis harmonicAnalysis, ComtradeConfig config) {
        try {
            if (config.getChannelDataList() != null && !config.getChannelDataList().isEmpty()) {
                // 获取第一个通道的数据点数量
                ChannelData firstChannel = config.getChannelDataList().get(0);
                if (firstChannel.getData() != null && !firstChannel.getData().isEmpty()) {
                    int dataPoints = firstChannel.getData().size();
                    // 根据采样率计算时间长度（毫秒）
                    long timeLength = (long) ((dataPoints / config.getSamplingRate()) * 1000);
                    harmonicAnalysis.setAnalysisWindowLength(timeLength);
                    return;
                }
            }
            harmonicAnalysis.setAnalysisWindowLength(0L);
        } catch (Exception e) {
            log.warn("计算时间窗口长度失败: {}", e.getMessage());
            harmonicAnalysis.setAnalysisWindowLength(0L);
        }
    }

    /**
     * 生成谐波质量评估
     */
    private HarmonicQualityAssessment generateQualityAssessment(List<ChannelHarmonic> currentHarmonics,
                                                                List<ChannelHarmonic> voltageHarmonics) {
        HarmonicQualityAssessment assessment = new HarmonicQualityAssessment();

        List<ChannelHarmonic> allHarmonics = new ArrayList<>();
        allHarmonics.addAll(currentHarmonics);
        allHarmonics.addAll(voltageHarmonics);

        if (allHarmonics.isEmpty()) {
            assessment.setOverallQuality("UNKNOWN");
            assessment.setMeetsStandard(false);
            assessment.setStandardName("GB/T 14549-1993");
            return assessment;
        }

        // 计算最大THD和平均THD
        double maxTHD = 0.0;
        String maxTHDChannel = "";
        double totalTHD = 0.0;
        int validChannels = 0;
        List<String> exceedingChannels = new ArrayList<>();

        for (ChannelHarmonic harmonic : allHarmonics) {
            if (harmonic.getTotalHarmonicDistortion() != null) {
                double thd = harmonic.getTotalHarmonicDistortion();
                totalTHD += thd;
                validChannels++;

                if (thd > maxTHD) {
                    maxTHD = thd;
                    maxTHDChannel = harmonic.getChannelName();
                }

                // 检查是否超标（假设标准为5%）
                if (thd > 5.0) {
                    exceedingChannels.add(harmonic.getChannelName());
                }
            }
        }

        assessment.setMaxTHD(maxTHD);
        assessment.setMaxTHDChannel(maxTHDChannel);
        assessment.setAverageTHD(validChannels > 0 ? totalTHD / validChannels : 0.0);
        assessment.setExceedingChannels(exceedingChannels);

        // 确定整体质量等级
        double avgTHD = assessment.getAverageTHD();
        if (avgTHD <= 2.0) {
            assessment.setOverallQuality("EXCELLENT");
        } else if (avgTHD <= 5.0) {
            assessment.setOverallQuality("GOOD");
        } else if (avgTHD <= 8.0) {
            assessment.setOverallQuality("FAIR");
        } else {
            assessment.setOverallQuality("POOR");
        }

        // 是否符合标准
        assessment.setMeetsStandard(exceedingChannels.isEmpty());
        assessment.setStandardName("GB/T 14549-1993");

        // 生成建议措施
        List<String> recommendations = new ArrayList<>();
        if (!exceedingChannels.isEmpty()) {
            recommendations.add("存在谐波超标通道，建议采取谐波治理措施");
            recommendations.add("考虑安装有源或无源滤波器");
            recommendations.add("检查负载设备是否正常工作");
        } else {
            recommendations.add("谐波水平良好，继续保持监测");
        }
        assessment.setRecommendations(recommendations);

        return assessment;
    }

    /**
     * 格式化Double值用于CSV输出
     */
    private String formatDouble(Double value) {
        if (value == null) {
            return "";
        }
        return String.format("%.3f", value);
    }

    /**
     * 获取指定通道的详细谐波数据（427次谐波）
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @param channelName 通道名称
     * @return 427次谐波的详细数据
     */
    public List<List<String>> getDetailedHarmonicData(Integer equipmentId, String fileName, String channelName) {
        log.info("获取通道 {} 的详细谐波数据", channelName);
        
        try {
            ChannelHarmonic channelHarmonic = analyzeChannelHarmonics(equipmentId, fileName, channelName, 427);
            if (channelHarmonic != null && channelHarmonic.getHarmonicComponents() != null) {
                return channelHarmonic.getHarmonicComponents();
            }
        } catch (Exception e) {
            log.error("获取通道 {} 详细谐波数据失败: {}", channelName, e.getMessage(), e);
        }
        
        return new ArrayList<>();
    }

    /**
     * 获取指定次数谐波的统计信息
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @param channelName 通道名称
     * @param harmonicOrder 谐波次数（1-427）
     * @return 指定次数谐波的统计信息 [总谐波失真%, 偶谐波%, 奇谐波%]
     */
    public List<String> getHarmonicByOrder(Integer equipmentId, String fileName, String channelName, Integer harmonicOrder) {
        log.info("获取通道 {} 的第{}次谐波数据", channelName, harmonicOrder);
        
        if (harmonicOrder == null || harmonicOrder < 1 || harmonicOrder > 427) {
            log.error("谐波次数无效: {}, 必须在1-427之间", harmonicOrder);
            return null;
        }
        
        try {
            ChannelHarmonic channelHarmonic = analyzeChannelHarmonics(equipmentId, fileName, channelName, 427);
            if (channelHarmonic != null) {
                return channelHarmonic.getHarmonicByOrder(harmonicOrder);
            }
        } catch (Exception e) {
            log.error("获取通道 {} 第{}次谐波数据失败: {}", channelName, harmonicOrder, e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 导出指定通道的427次谐波详细数据为CSV
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @param channelName 通道名称
     * @param outputFilePath 输出文件路径
     * @return 是否导出成功
     */
    public boolean exportChannelDetailedHarmonicsToCSV(Integer equipmentId, String fileName, 
                                                       String channelName, String outputFilePath) {
        log.info("开始导出通道 {} 的详细谐波数据为CSV: {}", channelName, outputFilePath);

        try {
            List<List<String>> harmonicData = getDetailedHarmonicData(equipmentId, fileName, channelName);
            if (harmonicData.isEmpty()) {
                log.warn("通道 {} 没有谐波数据可导出", channelName);
                return false;
            }

            List<String[]> csvData = new ArrayList<>();

            // 添加表头
            csvData.add(new String[]{
                    "谐波次数", "累积总谐波失真(%)", "累积偶谐波含量(%)", "累积奇谐波含量(%)"
            });

            // 添加数据行
            for (int i = 0; i < harmonicData.size(); i++) {
                List<String> harmonic = harmonicData.get(i);
                if (harmonic != null && harmonic.size() >= 3) {
                    csvData.add(new String[]{
                            String.valueOf(i + 1),  // 谐波次数
                            harmonic.get(0),        // 累积总谐波失真%
                            harmonic.get(1),        // 累积偶谐波含量%
                            harmonic.get(2)         // 累积奇谐波含量%
                    });
                }
            }

            // 写入CSV文件
            try (CsvWriter writer = CsvUtil.getWriter(
                    java.nio.file.Paths.get(outputFilePath).toUri().getPath(),
                    java.nio.charset.StandardCharsets.UTF_8, false)) {

                for (String[] row : csvData) {
                    writer.writeLine(row);
                }
            }

            log.info("通道 {} 的详细谐波数据成功导出为CSV: {}", channelName, outputFilePath);
            return true;

        } catch (Exception e) {
            log.error("导出通道 {} 详细谐波数据为CSV失败: {}", channelName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取所有通道的谐波对比数据
     * @param equipmentId 设备ID
     * @param fileName 文件名
     * @return 所有通道的谐波对比数据，格式：Map<通道名, List<List<String>>>
     */
    public Map<String, List<List<String>>> getAllChannelsHarmonicComparison(Integer equipmentId, String fileName) {
        log.info("获取所有通道的谐波对比数据");
        
        Map<String, List<List<String>>> result = new HashMap<>();
        
        try {
            HarmonicAnalysis harmonicAnalysis = analyzeHarmonics(equipmentId, fileName);
            
            // 处理电流通道
            if (harmonicAnalysis.getCurrentHarmonics() != null) {
                for (ChannelHarmonic harmonic : harmonicAnalysis.getCurrentHarmonics()) {
                    if (harmonic.getHarmonicComponents() != null) {
                        result.put(harmonic.getChannelName(), harmonic.getHarmonicComponents());
                    }
                }
            }
            
            // 处理电压通道
            if (harmonicAnalysis.getVoltageHarmonics() != null) {
                for (ChannelHarmonic harmonic : harmonicAnalysis.getVoltageHarmonics()) {
                    if (harmonic.getHarmonicComponents() != null) {
                        result.put(harmonic.getChannelName(), harmonic.getHarmonicComponents());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("获取所有通道谐波对比数据失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 演示新的427次谐波分析功能
     * 展示如何使用修改后的API获取详细的谐波数据
     * 
     * @param equipmentId 设备ID
     * @param fileName 文件名
     */
    public void demonstrateDetailedHarmonicAnalysis(Integer equipmentId, String fileName) {
        log.info("=== 演示427次谐波分析功能 ===");
        log.info("设备ID: {}, 文件名: {}", equipmentId, fileName);
        
        try {
            // 1. 获取完整的谐波分析结果
            HarmonicAnalysis harmonicAnalysis = analyzeHarmonics(equipmentId, fileName);
            log.info("谐波分析状态: {}", harmonicAnalysis.getAnalysisStatus());
            log.info("分析通道数: {}", harmonicAnalysis.getChannelCount());
            
            // 2. 演示获取指定通道的详细谐波数据
            String[] testChannels = {"Ia", "Ib", "Ic", "Ua", "Ub", "Uc"};
            for (String channelName : testChannels) {
                List<List<String>> detailedData = getDetailedHarmonicData(equipmentId, fileName, channelName);
                if (!detailedData.isEmpty()) {
                    log.info("通道 {} 的427次谐波数据已获取，数据长度: {}", channelName, detailedData.size());
                    
                    // 显示前5次谐波的数据作为示例
                    for (int i = 0; i < Math.min(5, detailedData.size()); i++) {
                        List<String> harmonicData = detailedData.get(i);
                        if (harmonicData.size() >= 3) {
                            log.info("  第{}次谐波 - 累积THD: {}%, 累积偶谐波: {}%, 累积奇谐波: {}%",
                                    i + 1, harmonicData.get(0), harmonicData.get(1), harmonicData.get(2));
                        }
                    }
                    
                    // 演示获取特定次数的谐波数据
                    List<String> fifthHarmonic = getHarmonicByOrder(equipmentId, fileName, channelName, 5);
                    if (fifthHarmonic != null && fifthHarmonic.size() >= 3) {
                        log.info("  通道 {} 第5次谐波详细数据: THD={}%, 偶谐波={}%, 奇谐波={}%",
                                channelName, fifthHarmonic.get(0), fifthHarmonic.get(1), fifthHarmonic.get(2));
                    }
                }
            }
            
            // 3. 演示获取所有通道的谐波对比数据
            Map<String, List<List<String>>> allChannelsData = getAllChannelsHarmonicComparison(equipmentId, fileName);
            log.info("所有通道谐波对比数据已获取，包含通道: {}", allChannelsData.keySet());
            
            // 4. 演示数据导出功能
            String outputPath = "upload-dir/harmonic_analysis_" + equipmentId + "_" + fileName + ".csv";
            boolean exportSuccess = exportHarmonicAnalysisToCSV(harmonicAnalysis, outputPath);
            log.info("谐波分析结果导出CSV: {}", exportSuccess ? "成功" : "失败");
            
            // 5. 演示单通道详细数据导出
            if (!testChannels[0].isEmpty()) {
                String channelOutputPath = "upload-dir/harmonic_detailed_" + testChannels[0] + "_" + equipmentId + "_" + fileName + ".csv";
                boolean channelExportSuccess = exportChannelDetailedHarmonicsToCSV(equipmentId, fileName, testChannels[0], channelOutputPath);
                log.info("通道 {} 详细谐波数据导出CSV: {}", testChannels[0], channelExportSuccess ? "成功" : "失败");
            }
            
            log.info("=== 427次谐波分析功能演示完成 ===");
            
        } catch (Exception e) {
            log.error("演示427次谐波分析功能时发生错误: {}", e.getMessage(), e);
        }
    }
} 