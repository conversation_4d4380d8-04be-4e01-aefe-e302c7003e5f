package com.siteweb.hmi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.battery.manager.BatteryConfigManager;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.hmi.service.BatteryGraphicDataService;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.service.EquipmentTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BatteryGraphicDataServiceImpl implements BatteryGraphicDataService {

    @Autowired
    ConfigSignalManager configSignalManager;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    BatteryConfigManager batteryConfigManager;

    @Autowired
    ActiveSignalManager activeSignalManager;

    @Autowired
    RealTimeSignalManager realTimeSignalManager;
    @Autowired
    EquipmentTemplateService equipmentTemplateService;


    private final String BATTERY_SYSTEMCONFIG_KEY = "battery.workstatus.signalbasetypeid";

    private final Integer BATTERY_CATEGORY_ID = 24;

    @Override
    public Map<String, Integer> GetBatteryWorkStatusCount() {
        HashMap<String, Integer> result = new HashMap<>();
        List<Equipment> batteryEqList = equipmentManager.getEquipmentsByEqCategoryId(BATTERY_CATEGORY_ID);
        String discontinue = localeMessageSourceUtil.getMessage("battery.workstatus.discontinue");
        String floatStr = localeMessageSourceUtil.getMessage("battery.workstatus.float");
        String discharge = localeMessageSourceUtil.getMessage("battery.workstatus.discharge");
        String equalize = localeMessageSourceUtil.getMessage("battery.workstatus.equalize");
        result.put(discontinue, 0);
        result.put(floatStr, 0);
        result.put(discharge, 0);
        result.put(equalize, 0);
        // 获取蓄电池所有工作状态信号
        String systemConfigValueByKey = batteryConfigManager.getSystemConfigValueByKey(BATTERY_SYSTEMCONFIG_KEY);
        long param = Long.parseLong(systemConfigValueByKey);
        List<Long> baseTypeIds = new ArrayList<>();
        baseTypeIds.add(param);
        for (Equipment batteryEq : batteryEqList) {
            // 判断是否有断连
            if (batteryEq.getEquipmentState().equals(OnlineState.OFFLINE.value())) {
                Integer count = result.get(discontinue);
                count++;
                result.put(discontinue, count);
                continue;
            }
            List<SimpleActiveSignal> signals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(batteryEq.getEquipmentId(), baseTypeIds);
            if (CollUtil.isNotEmpty(signals)) {
                String key = signals.get(0).getCurrentValue();
                if (!"null".equals(key) && result.containsKey(key)) {
                    Integer count = result.get(key);
                    count++;
                    result.put(key, count);
                }
            }

        }
        return result;
    }
}
