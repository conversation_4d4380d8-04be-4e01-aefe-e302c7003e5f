package com.siteweb.hmi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.hmi.dto.AlarmDurationStatisticsDTO;
import com.siteweb.hmi.dto.EquipmentStatisticsDTO;
import com.siteweb.hmi.dto.StatisticsCommonResult;
import com.siteweb.hmi.mapper.MixChartGraphicDataMapper;
import com.siteweb.hmi.model.StationHaltPowerOffGenResult;
import com.siteweb.hmi.service.MixChartGraphicDataService;
import com.siteweb.hmi.vo.InterruptStatisticsVO;
import com.siteweb.monitoring.dto.MonitorUnitDTO;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.*;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.manager.DataDictionaryManager;
import com.siteweb.utility.service.CoreEventSeverityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("MixChartGraphicDataService")
public class MixChartGraphicDataServiceImpl implements MixChartGraphicDataService {
    @Autowired
    MixChartGraphicDataMapper mixChartGraphicDataMapper;

    @Autowired
    EquipmentStateManager equipmentStateManager;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    DataDictionaryManager dataDictionaryManager;

    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    StationService stationService;

    @Autowired
    SamplerUnitService samplerUnitService;
    @Autowired
    HistoryEventService historyEventService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    MonitorUnitService monitorUnitService;

    @Autowired
    CoreEventSeverityService coreEventSeverityService;

    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    private Boolean eventLevelFilterEnable;
    @Override
    public List<List<Object>> getStationCategoryResultList(Integer userId) {
        List<Integer> stationIds = stationService.findStationIdsByUserId(userId);
        List<List<Object>> result = new ArrayList<>();
        result.add(List.of(messageSourceUtil.getMessage("common.field.site"), messageSourceUtil.getMessage("common.field.quantity"), messageSourceUtil.getMessage("common.field.percentage")));
        if (CollUtil.isEmpty(stationIds)) {
            result.add(List.of(messageSourceUtil.getMessage("common.field.total"), 0, ""));
            return result;
        }
        List<StatisticsCommonResult> statisticsCommonResults = mixChartGraphicDataMapper.findStationCategoryCountByStationIds(stationIds);
        for (StatisticsCommonResult statisticsCommonResult : statisticsCommonResults) {
            long stationCategoryCount = ((Long) statisticsCommonResult.getValue());
            BigDecimal percent = NumberUtil.div(NumberUtil.toBigDecimal(stationCategoryCount * 100), stationIds.size(), 1);
            result.add(List.of(statisticsCommonResult.getName(), statisticsCommonResult.getValue(), percent + "%"));
        }
        result.add(List.of(messageSourceUtil.getMessage("common.field.total"), stationIds.size(), ""));
        return result;
    }

    @Override
    public List<Object[]> getStationHaltPowerOffGen(Integer userId, Integer structureTypeId, Integer resourceStructureId) {
        // 1. 统计维度（102-地市，103-区县）
        List<ResourceStructure> resourceStructures;
        // 2. 查询局站
        Map<Integer, Station> stationMap = new HashMap<>();
        // 3. 获取当前维度下的所有子层级【key=>层级id, value=>子节点层级】
        Map<ResourceStructure, List<ResourceStructure>> childrenMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(resourceStructureId)) { // 传了层级则根据层级查找所属子层级、根据子层级查找局站
            List<Integer> userResourceIds = resourceStructureService.findResourceStructureIdsByUserId(userId);
            resourceStructures = resourceStructureManager.getResourceStructureLstByParentId(resourceStructureId);
            for (ResourceStructure e : resourceStructures) {
                if (!userResourceIds.contains(e.getResourceStructureId())) {
                    continue;
                }
                List<ResourceStructure> resourceStructureList = new ArrayList<>();
                resourceStructureManager.getAllFlatChildren(e.getResourceStructureId(), resourceStructureList);
                resourceStructureList.add(resourceStructureManager.getResourceStructureById(e.getResourceStructureId()));
                if (CollUtil.isNotEmpty(resourceStructureList)) {
                    childrenMap.put(e, resourceStructureList);
                }
            }
            Set<Integer> stationIds = childrenMap.values().stream().flatMap(Collection::stream).map(ResourceStructure::getOriginId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(stationIds)) {
                stationMap = stationService.findByIds(stationIds).stream().collect(Collectors.toMap(Station::getStationId, e -> e));
            }
        }else {
            resourceStructures = resourceStructureManager.getResourceStructureLstByTypeId(structureTypeId);
            stationMap = stationService.findByUserId(userId).stream().collect(Collectors.toMap(Station::getStationId, e -> e));
            resourceStructures.forEach(e -> {
                List<ResourceStructure> resourceStructureList = new ArrayList<>();
                resourceStructureManager.getAllFlatChildren(e.getResourceStructureId(), resourceStructureList);
                if (CollUtil.isNotEmpty(resourceStructureList)) {
                    childrenMap.put(e, resourceStructureList);
                }
            });
        }

        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<Integer> eventLevels = coreEventSeverityService.getCoreEventSeverities().stream().map(CoreEventSeverity::getEventLevel).toList();
        Map<Integer, List<ActiveEvent>> stationActiveEventMap = activeEventManager.findActiveEventByCondition(ae -> (!filterByEventLevel || eventLevels.contains(ae.getEventLevel())) && ObjectUtil.isEmpty(ae.getEndTime()) && (ObjectUtil.equal(ae.getEventCategoryId(), 10) || ObjectUtil.equal(ae.getEventCategoryId(), 42)))
                .stream().collect(Collectors.groupingBy(ActiveEvent::getStationId));
        // 4. 根据层级维度统计 局站数、停电数、发电数、中断数
        List<StationHaltPowerOffGenResult> stationHaltPowerOffGenResults = new ArrayList<>();
        final int[] index = {0}; // 序号
        Integer stationTotal = 0; // 总局站数
        Integer haltTotal = 0; // 总中断数
        int powerOffTotal = 0; // 总停电数
        int genStartTotal = 0; // 总发电数
        Iterator<Map.Entry<ResourceStructure, List<ResourceStructure>>> iterator = childrenMap.entrySet().iterator();
        ArrayList<Future<StationHaltPowerOffGenResult>> taskFutures = new ArrayList<>(childrenMap.size());
        while (iterator.hasNext()) {
            Map.Entry<ResourceStructure, List<ResourceStructure>> e = iterator.next();
            Map<Integer, Station> finalStationMap = stationMap;
            Future<StationHaltPowerOffGenResult> submit = threadPoolExecutor.submit(() -> {
                List<ResourceStructure> children = e.getValue();
                StationHaltPowerOffGenResult stationHaltPowerOffGenResult = new StationHaltPowerOffGenResult();
                stationHaltPowerOffGenResult.setIdx(index[0]++);
                stationHaltPowerOffGenResult.setStructureName(e.getKey().getResourceStructureName());
                stationHaltPowerOffGenResult.setStationId(e.getKey().getOriginId());
                int tempstationTotal = 0; // 当前层级局站数
                int temhaltTotal = 0; // 当前层级中断数
                int tempowerOffTotal = 0; // 当前层级停电数
                int tempgenStartTotal = 0; // 当前层级发电数
                boolean isPermission = false; // 是否具有当前层级权限（当前用户能看到的局站是否在当前层级下，不是则不展示）
                for (ResourceStructure child : children) {
                    if (!ObjectUtil.equal(child.getStructureTypeId(), 104)) {
                        continue;
                    }
                    if (!finalStationMap.containsKey(child.getOriginId())) {
                        continue;
                    }
                    if (!isPermission) {
                        isPermission = true;
                    }
                    Station station = finalStationMap.get(child.getOriginId());
                    // 局站数
                    stationHaltPowerOffGenResult.setStationCnt(stationHaltPowerOffGenResult.getStationCnt() + 1);
                    tempstationTotal++;

                    // 中断数
                    if (!ObjectUtil.equal(station.getConnectState(), 1)) {
                        temhaltTotal++;
                    }

                    // 查找局站的停电和发电的活动事件，并且未结束
                    List<ActiveEvent> activeEventByCondition = stationActiveEventMap.get(station.getStationId());
                    if (CollUtil.isNotEmpty(activeEventByCondition)) {
                        // 停电数计算
                        int powerOffNum = activeEventByCondition.stream().filter(t -> ObjectUtil.equal(t.getEventCategoryId(), 10)).toList().size();
                        tempowerOffTotal += powerOffNum;
                        // 发电数计算
                        int genStartNum = activeEventByCondition.stream().filter(t -> ObjectUtil.equal(t.getEventCategoryId(), 42)).toList().size();
                        tempgenStartTotal += genStartNum;
                    }
                }
                if (!isPermission) {
                    return null;
                }
                stationHaltPowerOffGenResult.setStationCnt(tempstationTotal);
                stationHaltPowerOffGenResult.setHaltCnt(temhaltTotal);
                stationHaltPowerOffGenResult.setPowerOffCnt(tempowerOffTotal);
                stationHaltPowerOffGenResult.setGenStartCnt(tempgenStartTotal);
                return stationHaltPowerOffGenResult;
            });
            taskFutures.add(submit);
        }


        try {
            for (Future<StationHaltPowerOffGenResult> future : taskFutures) {
                StationHaltPowerOffGenResult stationHaltPowerOffGenResult = future.get();
                if (ObjectUtil.isNull(stationHaltPowerOffGenResult)) {
                    continue;
                }
                stationTotal += stationHaltPowerOffGenResult.getStationCnt();
                haltTotal += stationHaltPowerOffGenResult.getHaltCnt();
                powerOffTotal += stationHaltPowerOffGenResult.getPowerOffCnt();
                genStartTotal += stationHaltPowerOffGenResult.getGenStartCnt();
                stationHaltPowerOffGenResults.add(stationHaltPowerOffGenResult);
            }
        } catch (Exception e) {
            log.error("监控总览计算异常：", e);
        }


        // 组态标题
        List<Object[]> result = new ArrayList<>(stationHaltPowerOffGenResults.size());
        Object[] title = {
                messageSourceUtil.getMessage("common.field.district"),
                messageSourceUtil.getMessage("common.field.site"),
                messageSourceUtil.getMessage("common.field.powerFailure"),
                messageSourceUtil.getMessage("common.field.electricityGeneration"),
                messageSourceUtil.getMessage("common.field.interrupt")
        };
        result.add(title);
        stationHaltPowerOffGenResults.sort(Comparator.comparing((StationHaltPowerOffGenResult e1) -> (double) e1.getHaltCnt() / e1.getStationCnt(), Double::compareTo).reversed()); // 百分比排序
        stationHaltPowerOffGenResults.forEach(a -> {
            BigDecimal num = NumberUtil.div(a.getHaltCnt(), a.getStationCnt()).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            Object[] param1 = {
                    a.getStructureName(),
                    a.getStationCnt(),
                    ObjectUtil.isEmpty(a.getPowerOffCnt()) ? 0 : a.getPowerOffCnt(),
                    ObjectUtil.isEmpty(a.getGenStartCnt()) ? 0 : a.getGenStartCnt(),
                    a.getHaltCnt() < 1 || a.getStationCnt() < 1 ? "0(0.0%)" + "[" + a.getStationId() + "]": a.getHaltCnt() + "(" + num + "%)" + "[" + a.getStationId() + "]"
            };
            result.add(param1);
        });
        result.add(new Object[]{
                messageSourceUtil.getMessage("common.field.total") + "：",
                stationTotal,
                powerOffTotal,
                genStartTotal,
                haltTotal < 1 || stationTotal < 1 ? "0(0.0%)" : haltTotal + "(" + NumberUtil.div(haltTotal, stationTotal).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) + "%)",
        });
        return result;
    }

    @Override
    public List<List<Object>> getStationEventTop5(Integer userId) {
        List<Integer> stationIds = stationService.findStationIdsByUserId(userId);
        List<List<Object>> result = new ArrayList<>();
        result.add(List.of(messageSourceUtil.getMessage("common.field.rank"), messageSourceUtil.getMessage("common.field.site"), messageSourceUtil.getMessage("eventNotification.activeEvent.eventName")));
        if (CollUtil.isEmpty(stationIds)) {
            return result;
        }
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<StatisticsCommonResult> statisticsCommonResults = mixChartGraphicDataMapper.getStationEventTopByStationIds(stationIds, filterByEventLevel);
        int rank = 1;
        for (StatisticsCommonResult statisticsCommonResult : statisticsCommonResults) {
            result.add(List.of(rank++,statisticsCommonResult.getName(),statisticsCommonResult.getValue()));
        }
        return result;
    }

    @Override
    public List<Object[]> deviceMonitoringStatus() {
        List<Object[]> result = new ArrayList<>();
        int totalCount = equipmentStateManager.getEquipmentCount();
        int maskState = equipmentStateManager.getMaskedEquipmentCount();
        int projectState = equipmentStateManager.getProjectEquipmentCount();
        int monitoring = totalCount - maskState - projectState;
        Object[] monitoringStatus = {messageSourceUtil.getMessage("common.field.monitorStatus"), messageSourceUtil.getMessage("common.field.underMonitoring"), messageSourceUtil.getMessage("common.field.blockedState"), messageSourceUtil.getMessage("common.field.engineeringState")};
        Object[] quantity = {messageSourceUtil.getMessage("common.field.quantity"), monitoring, maskState, projectState};
        result.add(monitoringStatus);
        result.add(quantity);

        return result;
    }

    @Override
    public Map<String, Object> getStationAvailability(Integer userId) {
        List<Integer> stationIds = stationService.findStationIdsByUserId(userId);
        if (CollUtil.isEmpty(stationIds)) {
            return Map.of("MonitoringAvailable", "0%", "PowerAvailable", "0%");
        }
        Date startTime = cn.hutool.core.date.DateUtil.offsetDay(new Date(), -7);
        Date endTime = new Date();
        String monitoringAvailable = getAvailableByEventCategoryId(userId, 7, startTime, endTime,null);
        String powerAvailable = getAvailableByEventCategoryId(userId, 10, startTime, endTime,null);
        Map<String, Object> result = new HashMap<>();
        result.put("MonitoringAvailable", monitoringAvailable);
        result.put("PowerAvailable", powerAvailable);
        return result;
    }


    public Map<String, List<AlarmDurationStatisticsDTO>> getAlarmGroupData(Date startTime, Date endTime, String resourceStructureIds) {
        Map<String, List<AlarmDurationStatisticsDTO>> result = new HashMap<>();
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            // 开始结束时间都为空,默认是按30天统计
            endTime = new Date();
            startTime = DateUtil.dateAddDays(endTime, -30);
        }
        Integer timeType = getTimeType(startTime, endTime);
        List<Integer> resourceStructureIdList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(resourceStructureIds)) {
            resourceStructureIdList = Stream.of(resourceStructureIds.split(","))
                    .map(Integer::valueOf)
                    .toList();
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        // 获取用户有权限并且指定层级以及层级下所有子层级id
        resourceStructureIdList = resourceStructureService.findResourceIdsByUserIdAndResourceStructureIds(userId, resourceStructureIdList);
        Collection<AlarmDurationStatisticsDTO> alarmDurationStatisticsDTOS = constructAlarmDurationStatisticsResult(startTime, endTime, timeType, resourceStructureIdList);
        //从数据库获取统计结果
        //将统计结果设置到结果中
        for (AlarmDurationStatisticsDTO alarmDurationStatisticsDTO : alarmDurationStatisticsDTOS) {
            if (result.containsKey(alarmDurationStatisticsDTO.getSeverityName())) {
                result.get(alarmDurationStatisticsDTO.getSeverityName()).add(alarmDurationStatisticsDTO);
            } else {
                List<AlarmDurationStatisticsDTO> als = new ArrayList<>();
                als.add(alarmDurationStatisticsDTO);
                result.put(alarmDurationStatisticsDTO.getSeverityName(), als);
            }
        }
        for (Map.Entry<String, List<AlarmDurationStatisticsDTO>> entry : result.entrySet()) {
            List<AlarmDurationStatisticsDTO> s = entry.getValue().stream().sorted(Comparator.comparing(AlarmDurationStatisticsDTO::getStatisticsTime)).toList();
            result.put(entry.getKey(), s);
        }

        return result;
    }

    @Override
    public List<EquipmentStatisticsDTO> getEquipmentStatistics(Integer userId, List<Integer> resourceStructureIdList) {
        Set<Integer> stationIdList;
        List<Equipment> equipments;
        if (CollUtil.isEmpty(resourceStructureIdList)) {//没选层级就片区权限
            stationIdList = new HashSet<>(stationService.findStationIdsByUserId(userId));
            equipments = equipmentManager.getEquipmentByStationId(stationIdList);
        } else {
            // 获取用户有权限并且指定层级以及层级下所有子层级id
            List<Integer> resourceStructureIds = resourceStructureService.findResourceIdsByUserIdAndResourceStructureIds(userId, resourceStructureIdList);
            equipments = equipmentService.findEquipmentsByResourceStructureIds(new HashSet<>(resourceStructureIds));
        }
        if (CollUtil.isEmpty(equipments)) {
            return new ArrayList<>();
        }
        Map<Integer, Equipment> equipmentMap = equipments.stream().collect(Collectors.toMap(Equipment::getEquipmentId, e -> e));
        List<Integer> equipmentIds = new ArrayList<>(equipmentMap.keySet());
        HashMap<Integer, EquipmentState> equipmentStateMap = equipmentStateManager.getEquipmentStateByIds(equipmentIds);
        Map<Integer, Integer> baseTypeOnlineStateCountMap = new HashMap<>();
        for (Map.Entry<Integer, EquipmentState> entry : equipmentStateMap.entrySet()) {
            Equipment equipment = equipmentMap.get(entry.getKey());
            Integer equipmentBaseType = equipment.getEquipmentBaseType();
            if (! OnlineState.ONLINE.equals(entry.getValue().getOnlineState()) || Objects.isNull(equipmentBaseType)) {
                continue;
            }
            Integer baseTypeOnlineStateCount = baseTypeOnlineStateCountMap.get(equipmentBaseType);
            if (Objects.isNull(baseTypeOnlineStateCount)) {
                baseTypeOnlineStateCount = 0;
            }
            baseTypeOnlineStateCountMap.put(equipmentBaseType, ++baseTypeOnlineStateCount);
        }
        List<EquipmentStatisticsDTO> result = mixChartGraphicDataMapper.getEquipmentStatistics(equipmentIds);
        result.forEach(e -> e.setOnlineCount(Optional.ofNullable(baseTypeOnlineStateCountMap.get(e.getEquipmentCategory())).orElse(0)));
        return result;
    }

    @Override
    public Map<String, InterruptStatisticsVO> getInterruptStatistics(Integer userId, String resourceStructureIds) {
        HashMap<String, InterruptStatisticsVO> result = new HashMap<>(4);
        Set<Integer> stationIds;

        TimeInterval timeInterval = new TimeInterval();
        timeInterval.start();
        if (CharSequenceUtil.isNotEmpty(resourceStructureIds)) {
            List<Integer> userResourceIds = resourceStructureService.findResourceStructureIdsByUserId(userId);
            Set<Integer> collect = Arrays.stream(resourceStructureIds.split(",")).map(Integer::valueOf).collect(Collectors.toSet());
            Set<Integer> allChildrenId = resourceStructureManager.getAllChildrenId(collect);
            userResourceIds = userResourceIds.stream().filter(allChildrenId::contains).toList();
            List<ResourceStructure> resourceStructureByIds = resourceStructureManager.getResourceStructureByIds(userResourceIds).stream().filter(e -> e.getStructureTypeId().equals(104)).toList();
            stationIds = resourceStructureByIds.stream().map(ResourceStructure::getOriginId).collect(Collectors.toSet());
        }else {
            stationIds = new HashSet<>(stationService.findStationIdsByUserId(userId));
        }

        // 局站
        {
            List<Station> stations = stationService.findByIds(stationIds);
            InterruptStatisticsVO stationInterruptStatistics = new InterruptStatisticsVO();
            stationInterruptStatistics.setCount(stations.size());
            stationInterruptStatistics.setInterrupt(stations.stream().filter(e -> ObjectUtil.notEqual(e.getConnectState(), 1)).toList().size());

            // 停电统计
            // 查找局站的停电和发电的活动事件，并且未结束
            long powerFailure = activeEventManager.queryAllActiveEvents()
                    .stream()
                    .filter(e -> (ObjectUtil.isEmpty(e.getEndTime()) && ObjectUtil.equal(e.getEventCategoryId(), 10) && stationIds.contains(e.getStationId()))).count();
            stationInterruptStatistics.setPowerFailure(powerFailure);
            result.put("station", stationInterruptStatistics);
        }

        // 设备
        List<Equipment> equipments = equipmentManager.getEquipmentByStationId(stationIds);
        InterruptStatisticsVO equipmentInterruptStatistics = new InterruptStatisticsVO();
        equipmentInterruptStatistics.setCount(equipments.size());
        equipmentInterruptStatistics.setInterrupt(equipments.stream().filter(e -> ObjectUtil.notEqual(equipmentStateManager.getEquipmentOnlineStateById(e.getEquipmentId()), OnlineState.ONLINE)).toList().size());
        result.put("equipment", equipmentInterruptStatistics);

        // 采集单元
        Collection<MonitorUnitDTO> monitorUnitList = Collections.synchronizedCollection(new ArrayList<>(equipments.size()));

        Set<Integer> monitorUnitIds = equipments.stream().map(Equipment::getMonitorUnitId).collect(Collectors.toSet());
        List<List<Integer>> sqlSize = CollUtil.split(monitorUnitIds, 1000);
        CountDownLatch countDownLatch = new CountDownLatch(sqlSize.size());
        for (List<Integer> e : sqlSize) {
            threadPoolExecutor.execute(() -> {
                try {
                    monitorUnitList.addAll(monitorUnitService.findMonitorUnitDTOsByIds(e));
                }finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("中断统计异常:", e);
        }

        // 采集单元
        InterruptStatisticsVO samplerUnitInterruptStatistics = new InterruptStatisticsVO();
        samplerUnitInterruptStatistics.setCount(monitorUnitList.size());
        samplerUnitInterruptStatistics.setInterrupt(monitorUnitList.stream().filter(e -> ObjectUtil.notEqual(e.getOnlineState(), OnlineState.ONLINE)).toList().size());
        result.put("samplerUnit", samplerUnitInterruptStatistics);

        return result;
    }

    @Override
    public String getAvailableByEventCategoryId(Integer userId, Integer eventCategoryId, Date startTime, Date endTime, String resourceStructureId) {
        List<HistoryEvent> historyEventList;
        if (CharSequenceUtil.isNotEmpty(resourceStructureId)) {
            Set<Integer> collect = Arrays.stream(resourceStructureId.split(",")).map(Integer::valueOf).collect(Collectors.toSet());
            Set<Integer> allChildrenId = resourceStructureManager.getAllChildrenId(collect);
            List<ResourceStructure> resourceStructureByIds = resourceStructureManager.getResourceStructureByIds(allChildrenId);
            if (CollUtil.isEmpty(resourceStructureByIds)) {
                return "0%";
            }
            List<Integer> resourceIds = resourceStructureByIds.stream()
                                                              .map(ResourceStructure::getResourceStructureId)
                                                              .toList();
            historyEventList = historyEventService.findDurationByResourceStructureIdsAndEventCategoryId(resourceIds, eventCategoryId, startTime, endTime);
        }else {
            List<Integer> stationIds = stationService.findStationIdsByUserId(userId);
            if (CollUtil.isEmpty(stationIds)) {
                return "0%";
            }
            historyEventList = historyEventService.findDurationByStationIdsAndEventCategoryId(stationIds, eventCategoryId, startTime, endTime);
        }
        //告警发生时长
        long eventDuration = historyEventList.stream()
                                             .mapToLong(event -> cn.hutool.core.date.DateUtil.between(event.getStartTime(), event.getEndTime(), DateUnit.SECOND))
                                             .sum();
        long between = cn.hutool.core.date.DateUtil.between(startTime, endTime, DateUnit.SECOND);
        //可用率 = (总时长 - 告警时长)/总时长
        BigDecimal availableRate = NumberUtil.round(1.0 * (between - eventDuration) / between * 100, 2);
        return availableRate.doubleValue() + "%";
    }

    private Collection<AlarmDurationStatisticsDTO> constructAlarmDurationStatisticsResult(Date startTime, Date endTime, Integer timeGroup, List<Integer> resourceStructureIdList) {
        List<AlarmDurationStatisticsDTO> result = new ArrayList<>();
        List<AlarmDurationStatisticsDTO> queryResult = null;
        List<String> timeG = new ArrayList<>();
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        switch (timeGroup) {
            case 1:
                // 小于一天，大于一个小时按小时统计
                timeG = getTimeGroup(startTime, endTime, Calendar.HOUR_OF_DAY, 1);
                queryResult = mixChartGraphicDataMapper.getAlarmDurationStatisticsDTOByHour(startTime, endTime, resourceStructureIdList, filterByEventLevel);
                break;
            case 2:
                // 一小时之内,按分钟统计(获取每十分钟的结果)
                timeG = getTimeGroup(startTime, endTime, Calendar.MINUTE, 10);
                queryResult = mixChartGraphicDataMapper.getAlarmDurationStatisticsDTOByTenMinute(startTime, endTime, resourceStructureIdList, filterByEventLevel);
                break;
            case 3:
                // 大于一天,按天统计
                timeG = getTimeGroup(startTime, endTime, Calendar.DAY_OF_YEAR, 1);
                queryResult = mixChartGraphicDataMapper.getAlarmDurationStatisticsDTOByDay(startTime, endTime, resourceStructureIdList, filterByEventLevel);
                break;
            default:
                break;
        }
        List<CoreEventSeverity> eventSeverities = coreEventSeverityService.getCoreEventSeverities();

        for (String statisticsTime : timeG) {
            for (CoreEventSeverity coreEventSeverity : eventSeverities) {
                AlarmDurationStatisticsDTO durationStatisticsDTO = new AlarmDurationStatisticsDTO(statisticsTime, 0, coreEventSeverity.getEventLevel(), coreEventSeverity.getSeverityName());
                result.add(durationStatisticsDTO);
            }
        }
        Map<String, AlarmDurationStatisticsDTO> stringAlarmDurationStatisticsDTOMap = result.stream().collect(
                Collectors.toMap(AlarmDurationStatisticsDTO::getKey, p -> p));

        for (AlarmDurationStatisticsDTO durationStatisticsDTO : queryResult) {
            if (stringAlarmDurationStatisticsDTOMap.containsKey(durationStatisticsDTO.getKey()))
                stringAlarmDurationStatisticsDTOMap.get(durationStatisticsDTO.getKey()).increaseAlarmCount(durationStatisticsDTO.getAlarmCount());
        }
        return stringAlarmDurationStatisticsDTOMap.values();
    }


    /**
     * 获取统计时段
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private Integer getTimeType(Date startTime, Date endTime) {
        // 时间差
        long diff = 0;
        // 一小时、一天的时间戳
        long hourTime = 1000 * 60 * 60;

        long dayTime = 1000 * 24 * 60 * 60;
        // 统计类型: 0.如果开始时间结束时间为空，按天统计 1.按小时统计 2.按分钟统计 3.根据开始时间结束时间按天统计
        int timeType = 0;
        // 计算两个时间戳差
        diff = endTime.getTime() - startTime.getTime();
        if (diff <= dayTime && diff >= hourTime) {
            // 小于一天大于一小时,按小时统计
            timeType = 1;
        } else if (diff < hourTime) {
            // 小于一小时,按分钟统计
            timeType = 2;
        } else {
            // 根据开始时间结束时间按天统计
            timeType = 3;
        }
        return timeType;
    }


    public static List<String> getTimeGroup(Date startTime, Date endTime, Integer timeType, Integer diff) {
        List<String> ymdList = new ArrayList<>();
        Calendar st = Calendar.getInstance();
        Calendar ed = Calendar.getInstance();
        st.setTime(startTime);
        ed.setTime(endTime);
        //按照天维度来拉取数据
        for (st.setTime(startTime); !st.after(ed); st.add(timeType, diff)) {
            ymdList.add(DateUtil.dateToString(st.getTime()));
        }
        return ymdList;
    }
}
