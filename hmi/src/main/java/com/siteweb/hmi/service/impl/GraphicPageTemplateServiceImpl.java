package com.siteweb.hmi.service.impl;

import com.siteweb.hmi.entity.GraphicPageTemplate;
import com.siteweb.hmi.mapper.GraphicPageTemplateMapper;
import com.siteweb.hmi.service.GraphicPageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("graphicPageTemplateService")
public class GraphicPageTemplateServiceImpl implements GraphicPageTemplateService {

    @Autowired
    private GraphicPageTemplateMapper graphicPageTemplateMapper;

    @Override
    public List<GraphicPageTemplate> findGraphicPageTemplates() {
        return graphicPageTemplateMapper.selectList(null);
    }

    @Override
    public int createGraphicPageTemplate(GraphicPageTemplate graphicPageTemplate) {
        return graphicPageTemplateMapper.insert(graphicPageTemplate);
    }

    @Override
    public int deleteById(Integer graphicPageTemplateId) {
        return    graphicPageTemplateMapper.deleteById(graphicPageTemplateId);
    }

    @Override
    public int updateGraphicPageTemplate(GraphicPageTemplate graphicPageTemplate) {
        return graphicPageTemplateMapper.updateById(graphicPageTemplate);
    }

    @Override
    public GraphicPageTemplate findById(Integer graphicPageTemplateId) {
        return graphicPageTemplateMapper.selectById(graphicPageTemplateId);
    }

    @Override
    public List<GraphicPageTemplate> findByBaseEquipmentId(Integer baseEquipmentId) {
        return graphicPageTemplateMapper.findAllByBaseEquipmentId(baseEquipmentId);
    }
}
