package com.siteweb.hmi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.siteweb.hmi.dto.CollectorNode;
import com.siteweb.hmi.service.MonitorUnitTopologyService;
import com.siteweb.monitoring.dto.EquipmentAlarmStateDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.MonitorUnit;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.MonitorUnitManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.MonitorUnitService;
import com.siteweb.monitoring.service.PortService;
import com.siteweb.monitoring.service.SamplerUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/12/28
 */

@Slf4j
@Service
public class MonitorUnitTopologyServiceImpl implements MonitorUnitTopologyService {


    @Autowired
    private MonitorUnitManager monitorUnitManager;

    @Autowired
    private EquipmentStateManager equipmentStateManager;

    @Autowired
    private MonitorUnitService monitorUnitService;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private PortService portService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    private ActiveEventManager activeEventManager;

    @Override
    public CollectorNode getMonitorUnitTopologyById(Integer monitorUnitId, Boolean showVirtualEquipment, List<Integer> excludePortIds) {
        // 获取监控单元信息
        MonitorUnit monitorUnit = monitorUnitService.findById(monitorUnitId);
        if (monitorUnit == null) {
            return null;
        }
        List<Port> ports = portService.findPortByMonitUnitId(monitorUnitId);
        List<Equipment> equipments = equipmentService.findByMonitorUnitId(monitorUnitId);
        List<SamplerUnit> samplerUnits = samplerUnitService.findSamplerUnitsByMonitorUnitId(monitorUnitId);

        // 创建监控单元根节点
        CollectorNode rootNode = new CollectorNode();
        rootNode.setId(monitorUnit.getMonitorUnitId().toString());
        rootNode.setNodeName(monitorUnit.getMonitorUnitName());
        rootNode.setNodeType(1); // 监控单元类型
        rootNode.setNodeDescription(monitorUnit.getDescription());
        rootNode.setChildren(new ArrayList<>());

        // 创建端口节点映射，用于快速查找
        Map<Integer, CollectorNode> portNodeMap = new HashMap<>();

        // 添加端口节点
        if (ports != null && !ports.isEmpty()) {
            // 对端口按照指定条件排序（这里以端口ID为例，你可以根据需要修改排序条件）
            ports.sort(Comparator.comparing(Port::getPortNo));
            for (Port port : ports) {
                // 通过excludeportids来过滤端口
                if (excludePortIds != null && !excludePortIds.isEmpty()) {
                    if (excludePortIds.contains(port.getPortNo())) {
                        continue;
                    }
                }
                CollectorNode portNode = new CollectorNode();
                portNode.setId(port.getPortId().toString());
                if (StrUtil.isNotBlank(port.getPhoneNumber())) {
                    portNode.setNodeName(port.getPhoneNumber());
                } else {
                    portNode.setNodeName(port.getPortName());
                }
                portNode.setSetting(port.getSetting());
                portNode.setPortTypeId(port.getPortType());
                portNode.setNodeType(2); // 端口类型
                portNode.setNodeDescription(port.getDescription());
                portNode.setChildren(new ArrayList<>());

                rootNode.getChildren().add(portNode);
                portNodeMap.put(port.getPortId(), portNode);
            }
        }

        // 创建设备节点映射，用于后续添加采样器
        Map<Integer, CollectorNode> equipmentNodeMap = new HashMap<>();

        // 通过samplerUnits找到设备所属的端口，并将设备添加到对应端口下
        if (samplerUnits != null && !samplerUnits.isEmpty()) {
            // 首先将equipment按portId分组
            Map<Integer, List<Equipment>> equipmentByPortMap = new HashMap<>();
            for (SamplerUnit samplerUnit : samplerUnits) {
                Integer portId = samplerUnit.getPortId();
                List<Equipment> portEquipments = equipments.stream()
                        .filter(e -> e.getSamplerUnitId() != null && e.getSamplerUnitId().equals(samplerUnit.getSamplerUnitId()))
                        .collect(Collectors.toList());

                if (!equipmentByPortMap.containsKey(portId)) {
                    equipmentByPortMap.put(portId, new ArrayList<>());
                }
                equipmentByPortMap.get(portId).addAll(portEquipments);
            }

            // 将设备添加到对应的端口节点下
            for (Map.Entry<Integer, List<Equipment>> entry : equipmentByPortMap.entrySet()) {
                Integer portId = entry.getKey();
                List<Equipment> portEquipments = entry.getValue();

                CollectorNode portNode = portNodeMap.get(portId);
                if (portNode != null && !portEquipments.isEmpty()) {
                    List<Integer> equipmentIds = equipments.stream().map(Equipment::getEquipmentId).collect(Collectors.toList());
                    Map<Integer, EquipmentAlarmStateDTO> alarmStateMap = activeEventManager.findEquipmentAlarmState(equipmentIds)
                            .stream().collect(Collectors.toMap(EquipmentAlarmStateDTO::getEquipmentId, state -> state));
                    for (Equipment equipment : portEquipments) {
                        // 虚拟设备和跨站RMU设备不显示
                        if (!showVirtualEquipment && equipment.getProperty() != null && (equipment.getProperty().contains("8") || equipment.getProperty().contains("9"))) {
                            continue;
                        }
                        CollectorNode equipmentNode = new CollectorNode();
                        equipmentNode.setId(equipment.getEquipmentId().toString());
                        equipmentNode.setNodeName(equipment.getEquipmentName());
                        equipmentNode.setNodeType(3); // 设备类型
                        equipmentNode.setNodeDescription(equipment.getDescription());
                        equipmentNode.setEquipmentId(equipment.getEquipmentId().toString());
                        equipmentNode.setChildren(new ArrayList<>());
                        OnlineState onlineState = equipmentStateManager.getEquipmentOnlineStateById(equipment.getEquipmentId());
                        equipmentNode.setOnlineState(onlineState == null? OnlineState.OFFLINE.name() : onlineState.name());
                        equipmentNode.setMaxEventSeverity(alarmStateMap.get(equipment.getEquipmentId()) == null ? 0 : alarmStateMap.get(equipment.getEquipmentId()).getEventLevel());

                        portNode.getChildren().add(equipmentNode);
                        equipmentNodeMap.put(equipment.getEquipmentId(), equipmentNode);
                    }
                }
            }

            // 将采样器添加到对应的设备节点下
            for (SamplerUnit samplerUnit : samplerUnits) {
                // 找到关联的设备节点
                List<Equipment> relatedEquipments = equipments.stream()
                        .filter(e -> e.getSamplerUnitId() != null && e.getSamplerUnitId().equals(samplerUnit.getSamplerUnitId()))
                        .collect(Collectors.toList());

                for (Equipment equipment : relatedEquipments) {
                    CollectorNode equipmentNode = equipmentNodeMap.get(equipment.getEquipmentId());
                    if (equipmentNode != null) {
                        CollectorNode samplerNode = new CollectorNode();
                        samplerNode.setId(samplerUnit.getSamplerUnitId().toString());
                        samplerNode.setNodeName(samplerUnit.getSamplerUnitName());
                        samplerNode.setAddress(samplerUnit.getAddress());
                        samplerNode.setNodeType(4); // 采样器类型
                        samplerNode.setNodeDescription(samplerUnit.getDescription());

                        equipmentNode.getChildren().add(samplerNode);
                    }
                }
            }
        }

        return rootNode;
    }
}
