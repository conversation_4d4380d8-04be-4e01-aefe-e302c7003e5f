package com.siteweb.hmi.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.hmi.entity.GraphicTemplate;
import com.siteweb.hmi.mapper.GraphicTemplateMapper;
import com.siteweb.hmi.service.GraphicTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GraphicTemplateServiceImpl implements GraphicTemplateService {
    @Autowired
    GraphicTemplateMapper graphicTemplateMapper;

    @Override
    public List<GraphicTemplate> findAll() {
        return graphicTemplateMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public GraphicTemplate findById(Integer graphicTemplateId) {
        return graphicTemplateMapper.selectOne(Wrappers.lambdaQuery(GraphicTemplate.class)
                                                       .eq(GraphicTemplate::getGraphicTemplateId, graphicTemplateId));
    }

    @Override
    public int deleteById(Integer graphicTemplateId) {
        return graphicTemplateMapper.deleteById(graphicTemplateId);
    }

    @Override
    public GraphicTemplate update(GraphicTemplate graphicTemplate) {
        graphicTemplateMapper.updateById(graphicTemplate);
        return graphicTemplate;
    }

    @Override
    public GraphicTemplate create(GraphicTemplate graphicTemplate) {
        graphicTemplateMapper.insert(graphicTemplate);
        return graphicTemplate;
    }

    @Override
    public List<GraphicTemplate> batchCreate(List<GraphicTemplate> graphicTemplateList) {
        graphicTemplateMapper.batchInsert(graphicTemplateList);
        return graphicTemplateList;
    }

    @Override
    public List<GraphicTemplate> findByConditional(String keywords, Integer graphicTemplateCompType, String graphicTemplateTag) {
        return graphicTemplateMapper.findByConditional(keywords, graphicTemplateCompType, graphicTemplateTag);
    }
}
