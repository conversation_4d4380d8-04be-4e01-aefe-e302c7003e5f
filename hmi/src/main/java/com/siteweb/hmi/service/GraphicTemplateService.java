package com.siteweb.hmi.service;

import com.siteweb.hmi.entity.GraphicTemplate;

import java.util.List;

public interface GraphicTemplateService {
    List<GraphicTemplate> findAll();

    GraphicTemplate findById(Integer graphicTemplateId);

    int deleteById(Integer graphicTemplateId);

    GraphicTemplate update(GraphicTemplate graphicTemplate);

    GraphicTemplate create(GraphicTemplate graphicTemplate);

    List<GraphicTemplate> batchCreate(List<GraphicTemplate> graphicTemplates);

    List<GraphicTemplate> findByConditional(String keywords, Integer graphicTemplateCompType, String graphicTemplateTag);
}
