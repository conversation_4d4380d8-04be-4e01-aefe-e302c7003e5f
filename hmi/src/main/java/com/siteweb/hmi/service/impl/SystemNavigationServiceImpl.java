package com.siteweb.hmi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.hmi.entity.SystemNavigation;
import com.siteweb.hmi.mapper.SystemNavigationMapper;
import com.siteweb.hmi.service.SystemNavigationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemNavigationServiceImpl implements SystemNavigationService {

    @Autowired
    SystemNavigationMapper systemNavigationMapper;

    @Override
    public List<SystemNavigation> finAllSystemNavigation() {
        return systemNavigationMapper
                .selectList(new QueryWrapper<SystemNavigation>()
                        .orderBy(true,true,"sortValue"));
    }
}
