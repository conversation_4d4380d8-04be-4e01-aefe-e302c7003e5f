package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.*;
import com.siteweb.monitoring.dto.StationEquipmentDTO;

import java.util.List;
import java.util.Map;

/**
 * @Author: lzy
 * @Date: 2022/7/25 17:18
 */
public interface ApiService {
    List<StatisticsResult> countStationOnlineStatus();

    List<StatisticsResult> eventSeverityStatistics(Integer userId, Integer stationId);

    List<StatisticsResult> stationStatistics(Integer userId, String resourceStructureIds);

    // key => 告警等级 valu=> 告警统计数据信息
    Map<String, List<AlarmtrendsDTO>> alarmtrends(Integer stationId);

    List<StatisticsResult> importantEventStatistics(String type, Integer userId, String resourceStructureIds);

    Map<String, List<StationAlarmStatisticsDTO>> stationalarmstatistics(Integer userId, String resourceStructureIds);

    List<StationEquipmentDTO> findEuipmentlist(Integer stationId);

    EquipmentStatistics devicecatogorystatistics(Integer type);

    List<StatisticsResult> alarmStatisticsByResourceType(Integer resourceType);

    List<StatisticsResult> alarmStatisticsByResourceStructureId(Integer resourceStructureId);

    List<CommonObjectDTO> configcommonobject(Integer type);

    List<StatisticsCommonResult> structureavailability(Integer userId, String resourceStructureIds);

    List<StatisticsResult> alarmStatistics(Integer resourceTypeId, Integer resourceStructureId);

    List<StatisticsCommonResult> deviceCategoryStatisticsState(EquipmentStateStatisticsVO equipmentStateStatisticsVO);

    /**
     * 设备在线、离线、屏蔽数量统计
     * @param userId 用户id，用于权限过滤
     * @return {@link List }<{@link StatisticsResult }>
     */
    List<StatisticsCommonResult> equipmentStateStatistics(Integer userId);


     // 告警总览页面
    //总设备
    StatisticsCommonResult equipmentSum(Integer userId);
    /**
     * 近N天告警设备数量
     * @param days 统计的数量
     */
    StatisticsCommonResult getAlarmDeviceCountByDays(Integer userId, Integer days);
    List<GeneralAlarmStatisticsDTO> getAlarmDeviceListByDays(Integer userId, Integer days);
    /**
     * 近N天设备告警率
     * @param days 统计的数量
     */
    StatisticsCommonResult getDeviceAlarmRateByDays(Integer userId, Integer days);
    /**
     * 近N天告警条数
     * @param unconfirmFlag 为true时表示查询未确认，否则查询所有
     * @return
     */
    StatisticsCommonResult getAlarmCountByDaysAndType(Integer userId, Boolean unconfirmFlag, Integer days);
    List<GeneralAlarmStatisticsDTO> getAlarmListByDaysAndType(Integer userId, Boolean unconfirmFlag, Integer days);
    /**
     * 设备类别告警数量统计
     * @return
     */
    List<StatisticsCommonResult> getEquipmentCategoryAlarmStatistics(Integer userId, Integer type);
    /**
     * 设备类别告警数量统计Top
     * @return
     */
    List<StatisticsCommonResult> getEquipmentAlarmRankStatistics(Integer userId, Integer type,Integer number);
    /**
     * 设备告警等级统计
     * @return
     */
    List<StatisticsCommonResult> getEquipmentAlarmLevelStatistics(Integer userId,Integer type,Integer equipmentId);
    /**
     * 区域（房间）类别告警数量统计Top
     * @return
     */
    List<StatisticsCommonResult> getAreaAlarmRankStatistics(Integer userId, Integer type,Integer number,Integer resourceStructureType);

    /**
     * 告警趋势统计
     * @return
     */
    List<AlarmTrendForByteDanceDTO> alarmTrendMonthOrWeek(Integer userId, Integer periodType);

    /**
     * 告警时长统计（按设备类别分组）
     */
    List<EventCategoryStatisticsByDuration> categoryStatisticsByDuration(Integer resourceSturctureId, Integer topN);
}
