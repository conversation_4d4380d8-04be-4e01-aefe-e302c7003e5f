package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.EquipmentGraphicData;
import com.siteweb.hmi.dto.SignalReduceDTO;
import com.siteweb.monitoring.dto.EquipmentAlarmStateDTO;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import com.siteweb.monitoring.vo.ActiveSignalRequestBySignalId;

import java.util.List;
import java.util.Map;

public interface GraphicRealDataService {
    EquipmentGraphicData getEquipmentGraphicDataByDeviceId(Integer equipmentId);

    /**
     * 根据设备ids获取每个设备的信号
     * @param equipmentIds 设备ids 根据逗号分割
     * @return {@link List}<{@link EquipmentGraphicData}> 设备信号
     */
    List<EquipmentGraphicData> findEquipmentGraphicDataByEquipmentIds(String equipmentIds);

    /**
     * 根据设备id列表获取每个设备的信号
     * @param equipmentList 设备id列表
     * @return {@link List}<{@link EquipmentGraphicData}> 设备信号
     */
    List<EquipmentGraphicData> getEquipmentGraphicDataByEquipmentIds(List<Integer> equipmentList);

    /**
     * 获取单条实时信号值
     * @param equipmentId 设备id
     * @param signalId 信号id
     * @return {@link RealTimeSignalItem}
     */
    RealTimeSignalItem findRealTimeSignal(Integer equipmentId, Integer signalId);


    /**
     * 根据设备id和信号ids查询设备信号
     * @param requestBySignalIds 获取信号条件：设备id和信号ids
     * @return {@link List}<{@link RealTimeSignalItem}>
     */
    List<EquipmentGraphicData> findEquipmentSignalByEquipmentIdAndSignalIds(List<ActiveSignalRequestBySignalId> requestBySignalIds);

    /**
     * 根据设备id和信号基类id查询设备信号
     * @param requestByBaseTypeIds
     * @return {@link List}<{@link EquipmentGraphicData}>
     */
    List<EquipmentGraphicData> findEquipmentSignalByEquipmentIdAndBaseTypeIds(List<ActiveSignalRequestByBaseTypeId> requestByBaseTypeIds);

    /**
     * 根据设备Id列表获取设备告警状态
     * @param dateIds 设备Id列表
     * @return 设备告警状态
     */
    List<EquipmentAlarmStateDTO> findEquipmentAlarmStateByGroup(List<Integer> dateIds, Integer userId);

    Map<Integer,List<SignalReduceDTO>> findEquipmentSignalsReduce(List<ActiveSignalRequestBySignalId> activeSignalRequestList);
}
