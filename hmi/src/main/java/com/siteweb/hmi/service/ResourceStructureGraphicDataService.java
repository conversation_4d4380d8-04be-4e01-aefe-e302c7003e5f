package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.FloorGraphicData;
import com.siteweb.monitoring.dto.ResourceStructureAlarmState;
import com.siteweb.hmi.dto.RoomGraphicData;
import com.siteweb.hmi.vo.EquipmentCategoryDataRequest;
import com.siteweb.hmi.vo.GraphicRoomDataRequest;

import java.util.List;

public interface ResourceStructureGraphicDataService {
    List<FloorGraphicData> getAllFloorGraphicData(String buildingIds);
    List<FloorGraphicData> getDeviceCategoryFloorGraphicDataByCategoryId( EquipmentCategoryDataRequest equipmentCategoryDataRequest);

    List<RoomGraphicData> getRoomGraphicDataByDeviceCategory(List<GraphicRoomDataRequest> graphicRoomDataRequest);

    List<ResourceStructureAlarmState> getResourceStructureAlarmStateByGroup(List<Integer> dataIds, Integer userId);

}
