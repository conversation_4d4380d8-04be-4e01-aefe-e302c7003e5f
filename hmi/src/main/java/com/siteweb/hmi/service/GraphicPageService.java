package com.siteweb.hmi.service;

import com.siteweb.hmi.dto.BatchApplyDTO;
import com.siteweb.hmi.dto.GraphicPageQueryParamDTO;
import com.siteweb.hmi.entity.GraphicPage;
import com.siteweb.hmi.vo.GraphicPageBatchRequest;

import java.util.Date;
import java.util.List;

public interface GraphicPageService{

    List<GraphicPage> findGraphicPages();

    int createGraphicPage(GraphicPage graphicPage);

    int deleteById(Integer graphicPageId);

    int deleteByPageCategoryAndObjectId(Integer pageCategory, Integer objectId);

    int updateGraphicPage(GraphicPage graphicPage);

    GraphicPage findById(Integer graphicPageId);

    List<GraphicPage> findByGraphicPageByPageCategoryAndObjectId(Integer pageCategory, Integer objectId);

    void batchApplyPage(GraphicPageBatchRequest graphicPageBatchRequest);

    List<GraphicPage> findByPageCategories(String pageCategories);

    List<GraphicPage> getGraphicPageCrumb(List<GraphicPageQueryParamDTO> graphicPageQueryParamDTOList);

    /**
     * 通过基类id与最近的更新时间获取批量应用列表
     * @param baseEquipmentId 设备基类id
     * @param updateTime 更新时间
     * @return {@link List}<{@link BatchApplyDTO}> 批量应用列表
     */
    List<BatchApplyDTO> findBatchApplyList(Integer baseEquipmentId, Date updateTime);

    /**
     * 设备基类是否更改
     *
     * @param equipmentId 设备主键id
     * @return boolean
     */
    boolean equipmentBaseTypeChange(Integer equipmentId);
}

