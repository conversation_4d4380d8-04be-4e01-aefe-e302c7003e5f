package com.siteweb.hmi.vo;

import com.siteweb.hmi.entity.GraphicPageTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * graphicpagetemplate info tableVO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-04-22 13:33:20
 */
@Data
@NoArgsConstructor
@ApiModel(value="GraphicPageTemplate实体",description="GraphicPageTemplate实体")
public class GraphicPageTemplateVO {

	/**
	 * 
	 */
    @ApiModelProperty(value="",name="id")
    private Integer id;

    /**
     * 
     */
    @ApiModelProperty(value="",name="type")
    private String type;

    /**
     * 
     */
    @ApiModelProperty(value="",name="groupId")
    private Integer groupId;

    /**
     * 
     */
    @ApiModelProperty(value="",name="pageCategory")
    private Integer pageCategory;

    /**
     * 
     */
    @ApiModelProperty(value="",name="baseEquipmentId")
    private Integer baseEquipmentId;

    /**
     * 
     */
    @ApiModelProperty(value="",name="internal")
    private Boolean internal;

    /**
     * 
     */
    @ApiModelProperty(value="",name="name")
    private String name;

    /**
     * 
     */
    @ApiModelProperty(value="",name="appendName")
    private String appendName;

    /**
     * 
     */
    @ApiModelProperty(value="",name="data")
    private String data;

    /**
     * 
     */
    @ApiModelProperty(value="",name="style")
    private String style;

    /**
     * 
     */
    @ApiModelProperty(value="",name="children")
    private String children;

    /**
     * 
     */
    @ApiModelProperty(value="",name="templateCategory")
    private Integer templateCategory;

    /**
     * 
     */
    @ApiModelProperty(value="",name="description")
    private String description;
	public GraphicPageTemplate build() {
        GraphicPageTemplate graphicPageTemplate = new GraphicPageTemplate();
        graphicPageTemplate.setId (this.id);
        graphicPageTemplate.setType (this.type);
        graphicPageTemplate.setGroupId (this.groupId);
        graphicPageTemplate.setPageCategory (this.pageCategory);
        graphicPageTemplate.setBaseEquipmentId (this.baseEquipmentId);
        graphicPageTemplate.setInternal (this.internal);
        graphicPageTemplate.setName (this.name);
        graphicPageTemplate.setAppendName (this.appendName);
        graphicPageTemplate.setData (this.data);
        graphicPageTemplate.setStyle (this.style);
        graphicPageTemplate.setChildren (this.children);
        graphicPageTemplate.setTemplateCategory (this.templateCategory);
        graphicPageTemplate.setDescription (this.description);
        return graphicPageTemplate;
	}
}
