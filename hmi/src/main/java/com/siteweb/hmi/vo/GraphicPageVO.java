package com.siteweb.hmi.vo;

import com.siteweb.utility.util.BooleanUtil;
import com.siteweb.hmi.entity.GraphicPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * graphicpage info tableVO
 *
 * <AUTHOR>
 * @email 
 * @date 2022-04-22 13:33:20
 */
@Data
@NoArgsConstructor
@ApiModel(value="GraphicPage实体",description="GraphicPage实体")
public class GraphicPageVO {

	/**
	 * 
	 */
    @ApiModelProperty(value="",name="id")
    private Integer id;

    /**
     * 
     */
    @ApiModelProperty(value="",name="type")
    private String type;

    /**
     * 
     */
    @ApiModelProperty(value="",name="groupId")
    private Integer groupId;

    /**
     * 
     */
    @ApiModelProperty(value="",name="pageCategory")
    private Integer pageCategory;

    /**
     * 
     */
    @ApiModelProperty(value="",name="baseEquipmentId")
    private Integer baseEquipmentId;

    /**
     * 
     */
    @ApiModelProperty(value="",name="objectId")
    private Integer objectId;

    /**
     * 
     */
    @ApiModelProperty(value="",name="name")
    private String name;

    /**
     * 
     */
    @ApiModelProperty(value="",name="appendName")
    private String appendName;

    /**
     * 
     */
    @ApiModelProperty(value="",name="templateId")
    private Integer templateId;

    /**
     * 
     */
    @ApiModelProperty(value="",name="data")
    private String data;

    /**
     * 
     */
    @ApiModelProperty(value="",name="style")
    private String style;

    /**
     * 
     */
    @ApiModelProperty(value="",name="children")
    private String children;

    /**
     * 
     */
    @ApiModelProperty(value="",name="description")
    private String description;

    /**
     * 
     */
    @ApiModelProperty(value="",name="routerType")
    private String routerType;

    /**
     * 
     */
    @ApiModelProperty(value="",name="crumbsRouterType")
    private String crumbsRouterType;

    /**
     * 
     */
    @ApiModelProperty(value="",name="pageCanFullScreen")
    private Boolean pageCanFullScreen;

    /**
     * 
     */
    @ApiModelProperty(value="",name="sceneId")
    private Integer sceneId;

	public GraphicPage build() {
        GraphicPage graphicPage = new GraphicPage();
        graphicPage.setId (this.id);
        graphicPage.setType (this.type);
        graphicPage.setGroupId (this.groupId);
        graphicPage.setPageCategory (this.pageCategory);
        graphicPage.setBaseEquipmentId (this.baseEquipmentId);
        graphicPage.setObjectId (this.objectId);
        graphicPage.setName (this.name);
        graphicPage.setAppendName (this.appendName);
        graphicPage.setTemplateId (this.templateId);
        graphicPage.setData (this.data);
        graphicPage.setStyle (this.style);
        graphicPage.setChildren (this.children);
        graphicPage.setDescription (this.description);
        graphicPage.setRouterType (this.routerType);
        graphicPage.setCrumbsRouterType (this.crumbsRouterType);
        graphicPage.setPageCanFullScreen (BooleanUtil.toInteger(this.pageCanFullScreen));
        graphicPage.setSceneId (this.sceneId);
        graphicPage.setUpdateTime(new Date());
        return graphicPage;
	}
}
