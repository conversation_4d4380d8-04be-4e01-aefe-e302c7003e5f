package com.siteweb.hmi.listener;

import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.hmi.service.GraphicPageService;
import com.siteweb.monitoring.dto.ResourceObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DeleteGraphicPageListener implements ApplicationListener<BaseSpringEvent<ResourceObject>> {
    @Autowired
    GraphicPageService graphicPageService;
    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<ResourceObject> event) {
        ResourceObject resourceObject = event.getData();
        graphicPageService.deleteByPageCategoryAndObjectId(resourceObject.getObjectTypeId(), resourceObject.getObjectId());
    }
}
