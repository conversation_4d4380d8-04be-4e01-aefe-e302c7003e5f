package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.dto.EquipmentSignalDurationDTO;
import com.siteweb.hmi.dto.EquipmentSignalQueryDTO;
import com.siteweb.hmi.service.GraphicHistoryDataPointService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * 历史数据组态接口
 */
@RequestMapping("/api")
@RestController
public class GraphicHistoryDataPointController {
    @Autowired
    GraphicHistoryDataPointService graphicHistoryDataPointService;

    @ApiOperation(value = "获取信号基类历史曲线")
    @GetMapping(value = "/graphichistorysignaldata", params = {"startTime", "endTime", "equipmentId", "baseTypeId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGraphicHistorySignalDataByBaseTypeId(Date startTime, Date endTime, Integer equipmentId, String baseTypeId) {
        return ResponseHelper.successful(graphicHistoryDataPointService.findGraphicHistorySignalDataByBaseTypeId(startTime, endTime, equipmentId, baseTypeId));
    }

    @ApiOperation(value = "获取信号历史曲线")
    @PostMapping(value = "/graphichistorysignaldata", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGraphicHistorySignalDataBySignal(@RequestBody EquipmentSignalDurationDTO equipmentSignalDurationDTO) {
        return ResponseHelper.successful(graphicHistoryDataPointService.findGraphicHistorySignalDataBySignal(equipmentSignalDurationDTO));
    }


    @ApiOperation(value = "按设备获取设备历史信号")
    @GetMapping(value = "/historysignals", params = {"startTime", "endTime", "equipmentId", "signalId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getHistorySignal(Date startTime, Date endTime, Integer equipmentId, Integer signalId) {
        return ResponseHelper.successful(graphicHistoryDataPointService.findEquipmentHistorySignal(startTime, endTime, equipmentId, signalId));
    }

    @ApiOperation(value = "按基类获取设备历史信号")
    @GetMapping(value = "/historysignals", params = {"startTime", "endTime", "equipmentId", "baseTypeId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getHistorySignalByBaseTypeId(Date startTime, Date endTime, Integer equipmentId, Long baseTypeId) {
        return ResponseHelper.successful(graphicHistoryDataPointService.getHistorySignalByBaseTypeId(startTime, endTime, equipmentId, baseTypeId));
    }


    @ApiOperation(value = "按照设备Id,信号ID 取多个信号的历史数据")
    @PostMapping(value = "/historysignals", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getHistorySignalByBaseTypeId2(@Valid @RequestBody EquipmentSignalQueryDTO query) {
        return ResponseHelper.successful(graphicHistoryDataPointService.getMultipleHistorySignalMapBySignalIds(query.getStartTime(), query.getEndTime(), query.getSignals()));
    }


}
