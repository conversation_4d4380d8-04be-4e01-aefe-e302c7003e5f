package com.siteweb.hmi.controller;

import com.siteweb.hmi.entity.ComtradeRecord;
import com.siteweb.hmi.service.ComtradeRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * COMTRADE记录控制器
 */
@RestController
@RequestMapping("/api/comtrade-records")
@RequiredArgsConstructor
public class ComtradeRecordController {
    
    private final ComtradeRecordService comtradeRecordService;
    
    /**
     * 获取指定设备的COMTRADE记录列表
     * @param equipmentId 设备ID
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return COMTRADE记录列表
     */
    @GetMapping("/equipment/{equipmentId}")
    public ResponseEntity<List<ComtradeRecord>> getComtradeRecordsByEquipment(
            @PathVariable Integer equipmentId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        List<ComtradeRecord> records = comtradeRecordService.getComtradeRecordsByEquipmentId(equipmentId, startTime, endTime);
        return ResponseEntity.ok(records);
    }
} 