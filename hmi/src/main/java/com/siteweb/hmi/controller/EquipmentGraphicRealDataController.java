package com.siteweb.hmi.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.GraphicRealDataService;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import com.siteweb.monitoring.vo.ActiveSignalRequestBySignalId;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备组态的实时数据接口
 */
@RestController
@RequestMapping("/api")
@Api(value="GraphicRealDataController",tags={"组态实时数据接口"})
public class EquipmentGraphicRealDataController {

    @Autowired
    private GraphicRealDataService graphicRealDataService;

    @ApiOperation(value="获取设备的实时信号组态接口")
    @GetMapping(value = "/equipmentactivesignals",
            params = {"equipmentId"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDeviceGraphicDataByDeviceId(@RequestParam(value = "equipmentId") Integer equipmentId) {
        return ResponseHelper.successful(graphicRealDataService.getEquipmentGraphicDataByDeviceId(equipmentId));
    }

    @ApiOperation(value="获取多个设备的实时信号组态接口(多个逗号隔开)")
    @GetMapping(value = "/equipmentactivesignals",
            params = {"equipmentIds"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentGraphicDataByEquipmentIds(@RequestParam(value = "equipmentIds") String equipmentIds) {
        return ResponseHelper.successful(graphicRealDataService.findEquipmentGraphicDataByEquipmentIds(equipmentIds));
    }

    @ApiOperation(value="获取多个设备的实时信号组态接口")
    @PostMapping(value = "/equipmentactivesignals",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentGraphicDataByEquipmentId(@RequestBody List<Integer> equipmentList) {
        return ResponseHelper.successful(graphicRealDataService.getEquipmentGraphicDataByEquipmentIds(equipmentList));
    }

    @ApiOperation(value = "根据设备id与信号id获取实时信号")
    @GetMapping(value = "/activesignals", params = {"equipmentId", "signalId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRealTimeSignal(Integer equipmentId, Integer signalId) {
        return ResponseHelper.successful(graphicRealDataService.findRealTimeSignal(equipmentId, signalId));
    }

    @ApiOperation(value = "根据设备ID，信号ID列表获取实时信号")
    @PostMapping(value = "/equipmentactivesignalsbysignalids", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSignalByEquipmentIdAndSignalIds(@RequestBody List<ActiveSignalRequestBySignalId> requestBySignalIds){
          return ResponseHelper.successful(graphicRealDataService.findEquipmentSignalByEquipmentIdAndSignalIds(requestBySignalIds));
    }

    @ApiOperation(value = "根据设备ID，信号基类ID列表获取实时信号")
    @PostMapping(value = "/equipmentactivesignalsbybasetypeids", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSignalByEquipmentIdAndBaseTypeIds(@RequestBody List<ActiveSignalRequestByBaseTypeId> requestByBaseTypeIds){
        return ResponseHelper.successful(graphicRealDataService.findEquipmentSignalByEquipmentIdAndBaseTypeIds(requestByBaseTypeIds));
    }

    @ApiOperation(value = "获取设备Id列表获取当前设备的告警状态")
    @PostMapping(value = "/equipmentalarmstates", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentAlarmStateByGroup(@RequestBody List<Integer> dataIds){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(graphicRealDataService.findEquipmentAlarmStateByGroup(dataIds,userId));
    }

    @ApiOperation(value = "根据设备ID，信号ID列表查询最近24小时的信号信息(包括当前在值、最大值、最小值)")
    @PostMapping(value = "/equipmentactivesignalsreduce", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentSignalReduce(@RequestBody List<ActiveSignalRequestBySignalId> activeSignalRequestList){
        return ResponseHelper.successful(graphicRealDataService.findEquipmentSignalsReduce(activeSignalRequestList));
    }
}
