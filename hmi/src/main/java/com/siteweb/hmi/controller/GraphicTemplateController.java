package com.siteweb.hmi.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.dto.GraphicTemplateDTO;
import com.siteweb.hmi.entity.GraphicTemplate;
import com.siteweb.hmi.service.GraphicTemplateService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "GraphicTemplateController", tags = {"组态模板管理接口"})
public class GraphicTemplateController {
    @Autowired
    GraphicTemplateService graphicTemplateService;

    @GetMapping(value = "/graphictemplate")
    public ResponseEntity<ResponseResult> findByConditional(String keywords,Integer graphicTemplateCompType,String graphicTemplateTag){
        return ResponseHelper.successful(graphicTemplateService.findByConditional(keywords, graphicTemplateCompType, graphicTemplateTag));
    }

    @GetMapping("/graphictemplate/{id}")
    public ResponseEntity<ResponseResult> findById(@PathVariable Integer id){
        return ResponseHelper.successful(graphicTemplateService.findById(id));
    }

    @DeleteMapping("/graphictemplate/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable Integer id){
        return ResponseHelper.successful(graphicTemplateService.deleteById(id));
    }

    @PutMapping("/graphictemplate")
    public ResponseEntity<ResponseResult> update(@RequestBody GraphicTemplate graphicTemplate){
        return ResponseHelper.successful(graphicTemplateService.update(graphicTemplate));
    }

    @PostMapping("/graphictemplate")
    public ResponseEntity<ResponseResult> create(@RequestBody GraphicTemplateDTO graphicTemplateDTO){
        GraphicTemplate graphicTemplate = BeanUtil.copyProperties(graphicTemplateDTO, GraphicTemplate.class);
        return ResponseHelper.successful(graphicTemplateService.create(graphicTemplate));
    }

    @PostMapping("/graphictemplate/batch")
    public ResponseEntity<ResponseResult> batchCreate(@RequestBody List<GraphicTemplateDTO> graphicTemplateDTOList){
        if (CollUtil.isEmpty(graphicTemplateDTOList)) {
            return ResponseHelper.successful();
        }
        List<GraphicTemplate> graphicTemplates = BeanUtil.copyToList(graphicTemplateDTOList, GraphicTemplate.class);
        return ResponseHelper.successful(graphicTemplateService.batchCreate(graphicTemplates));
    }
}
