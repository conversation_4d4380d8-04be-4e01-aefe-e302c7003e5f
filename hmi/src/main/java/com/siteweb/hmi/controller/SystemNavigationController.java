package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.SystemNavigationService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 数据地图访问接口
 */
@RequestMapping("/api")
@RestController
public class SystemNavigationController {
    @Autowired
    SystemNavigationService systemNavigationService;

    @ApiOperation(value="获取系统导航")
    @GetMapping(value = "/systemnavigations")
    public ResponseEntity<ResponseResult> getDataMapGraphicData() {
        return ResponseHelper.successful(systemNavigationService.finAllSystemNavigation());
    }
}
