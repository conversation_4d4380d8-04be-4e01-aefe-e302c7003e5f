package com.siteweb.hmi.controller;

import com.siteweb.hmi.dto.BranchGraphicSignal;
import com.siteweb.hmi.service.GraphicMultiBranchDataPointService;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RequestMapping("/api")
@RestController
public class GraphicMultiBranchDataPointController {
    private final Logger log = LoggerFactory.getLogger(GraphicMultiBranchDataPointController.class);

    @Autowired
    private GraphicMultiBranchDataPointService graphicMultiBranchDataPointService;

    @PostMapping(value = "graphicmultibranchsignals",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public BranchGraphicSignal getGraphicMultiBranchDataPoints(@Valid @RequestBody ActiveSignalRequestByBaseTypeId graphicDataRequest) {
        log.debug("REST request to get GraphicMultiBranchDataPoints");
        return  graphicMultiBranchDataPointService.getMultiBranchGraphicDataPoints(graphicDataRequest);
    }


}
