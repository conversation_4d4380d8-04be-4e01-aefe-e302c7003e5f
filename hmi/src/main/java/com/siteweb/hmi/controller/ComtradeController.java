package com.siteweb.hmi.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.entity.ChannelData;
import com.siteweb.hmi.entity.ComtradeAnalysis;
import com.siteweb.hmi.entity.ComtradeConfig;
import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.hmi.service.ComtradeParser;
import com.siteweb.hmi.service.ComtradeRecordService;
import com.siteweb.monitoring.dto.ResourceStructureEquipmentTreeDTO;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

/**
 * COMTRADE文件控制器
 */
@RestController
@RequestMapping("/api/comtrade")
@RequiredArgsConstructor
public class ComtradeController {

    private final ComtradeFtpService comtradeFtpService;

    private final ComtradeParser comtradeParser;

    private final ComtradeRecordService comtradeRecordService;

    /**
     * 请求体对象，用于POST请求获取COMTRADE数据
     */
    @Data
    public static class ComtradeDataRequest {
        private Integer equipmentId;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
        // Or your server's default timezone, or UTC
        private Date startTime;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
        // Or your server's default timezone, or UTC
        private Date endTime;
    }

    /**
     * 获取COMTRADE配置文件的内容
     *
     * @param filePath 配置文件的完整路径
     * @return 文件内容或错误信息
     */
    @GetMapping("/config-content")
    public ResponseEntity<?> getComtradeConfigContent(@RequestParam String filePath) {
        try {
            java.nio.file.Path path = Paths.get(filePath);
            if (!Files.exists(path) || !Files.isReadable(path)) {
                return ResponseEntity.badRequest().body("文件不存在或不可读: " + filePath);
            }
            File cfgFile = path.toFile();
            ComtradeConfig config = comtradeParser.parseConfigFile(cfgFile);
            if (config == null) {
                return ResponseEntity.badRequest().body("配置文件解析失败");
            }
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("处理请求时发生未知错误: " + e.getMessage());
        }
    }


  
    /**
     * 根据设备ID和不含后缀的文件名解析COMTRADE数据文件
     *
     * @param equipmentId 设备ID
     * @param fileName    不含后缀的文件名
     * @return 解析后的通道数据列表
     */
    @GetMapping("/data-by-name")
    public ResponseEntity<?> getComtradeDataByName(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName) {
        try {
            List<ChannelData> dataList = comtradeParser.parseDataFileByName(equipmentId, fileName);
            if (dataList == null || dataList.isEmpty()) {
                return ResponseEntity.ok().body("未找到数据或解析失败,文件名: " + fileName);
            }

            return ResponseEntity.ok(dataList);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("处理请求时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取过滤后的COMTRADE配置和数据
     *
     * @param equipmentId 设备ID
     * @param fileName    不含后缀的文件名
     * @param startTime   计算时间窗口开始时间
     * @param endTime     计算时间窗口结束时间
     * @return 过滤后的COMTRADE配置和通道数据
     */
    @GetMapping("/filtered-config")
    public ResponseEntity<?> getFilteredComtradeConfig(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            ComtradeConfig config = comtradeParser.getFilteredComtradeConfig(equipmentId, fileName, startTime, endTime);
            if (config == null) {
                return ResponseEntity.badRequest().body("获取过滤后的COMTRADE配置失败，设备ID: " + equipmentId + ", 文件名: " + fileName);
            }
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 数据对象，用于POST请求获取过滤后的COMTRADE配置和数据
     */
    @Data
    public static class FilteredComtradeConfigRequest {
        private Integer equipmentId;
        private String fileName;
        private String startTime;
        private String endTime;
    }

    /**
     * 通过POST请求获取COMTRADE数据分析结果
     *
     * @param request 包含设备ID、文件名、开始时间和结束时间的请求体
     * @return COMTRADE数据分析结果
     */
    @PostMapping("/analyze-data")
    public ResponseEntity<?> analyzeComtradeData(@RequestBody FilteredComtradeConfigRequest request) {
        try {
            ComtradeConfig config = comtradeParser.getFilteredComtradeConfig(
                    request.getEquipmentId(),
                    request.getFileName(),
                    request.getStartTime(),
                    request.getEndTime());

            if (config == null) {
                return ResponseEntity.badRequest().body("获取过滤后的COMTRADE配置失败，设备ID: " +
                        request.getEquipmentId() + ", 文件名: " + request.getFileName());
            }

            // 获取COMTRADE数据分析结果
            ComtradeAnalysis analysis = comtradeParser.getComtradeAnalysis(
                    config,
                    request.getStartTime(),
                    request.getEndTime());

            // 只返回分析结果
            return ResponseEntity.ok(analysis);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 导出COMTRADE数据为CSV文件
     *
     * @param equipmentId 设备ID
     * @param fileName    不含后缀的文件名
     * @return CSV文件下载响应
     */
    @GetMapping("/export-csv")
    public ResponseEntity<?> exportComtradeDataToCsv(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName) {
        try {
            // 生成临时文件路径
            String tempDir = System.getProperty("java.io.tmpdir");
            String csvFileName = fileName + "_" + System.currentTimeMillis() + ".csv";
            String outputFilePath = tempDir + File.separator + csvFileName;

            // 导出CSV文件
            long exportedRows = comtradeParser.exportComtradeDataToCsv(equipmentId, fileName, outputFilePath);

            if (exportedRows == 0) {
                return ResponseEntity.badRequest().body("导出失败：未找到数据或导出过程中发生错误");
            }

            // 读取生成的CSV文件
            File csvFile = new File(outputFilePath);
            if (!csvFile.exists()) {
                return ResponseEntity.internalServerError().body("CSV文件生成失败");
            }

            // 准备文件下载响应
            byte[] fileContent = Files.readAllBytes(csvFile.toPath());

            // 清理临时文件
            csvFile.delete();

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=\"" + csvFileName + "\"")
                    .header("Content-Type", "text/csv; charset=utf-8")
                    .body(fileContent);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("导出CSV文件时发生错误: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取COMTRADE设备资源结构设备树")
    @GetMapping("/resourcestructures/comtrade-equipment-tree")
    public ResponseEntity<ResponseResult> getResourceStructureEquipmentTreeForComtrade() {
        ResourceStructureEquipmentTreeDTO resourceStructure = comtradeRecordService.getResourceStructureEquipmentTreeForComtrade();
        return ResponseHelper.successful(resourceStructure, HttpStatus.OK);
    }


    /**
     * 计算工频周期20ms RMS值
     *
     * @param equipmentId 设备ID
     * @param fileName    不含后缀的文件名
     * @return 包含20ms RMS值的通道数据列表
     */
    @GetMapping("/20ms-rms-values")
    public ResponseEntity<?> calculate20msRmsValues(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName) {
        try {
            List<ChannelData> rmsValues = comtradeParser.calculate20msRmsValues(equipmentId, fileName);
            if (rmsValues == null || rmsValues.isEmpty()) {
                return ResponseEntity.ok().body("未找到数据或计算失败,文件名: " + fileName);
            }
            return ResponseEntity.ok(rmsValues);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("计算20ms RMS值时发生错误: " + e.getMessage());
        }
    }

}