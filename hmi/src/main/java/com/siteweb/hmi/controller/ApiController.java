package com.siteweb.hmi.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.hmi.dto.EquipmentStateStatisticsVO;
import com.siteweb.hmi.dto.EventLevelRankDTO;
import com.siteweb.hmi.dto.EventStatisticsByDuration;
import com.siteweb.hmi.dto.StatisticsResult;
import com.siteweb.hmi.service.ApiService;
import com.siteweb.hmi.service.IDCGraphicDataService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.service.ResourceStructureService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;

/**
 * @Author: lzy
 * @Date: 2022/7/25 17:14
 */
@RequestMapping("/api")
@RestController
public class ApiController {
    private static final String USER_ID_IS_NULL = "userid is null";

    @Autowired
    private ApiService apiService;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private IDCGraphicDataService idcGraphicDataService;

    @ApiOperation("局站在线状态统计")
    @GetMapping("/stationcategory")
    public ResponseEntity<ResponseResult> countStationOnlineStatus() {
        return ResponseHelper.successful(apiService.countStationOnlineStatus());
    }

    @ApiOperation("活动告警总览")
    @GetMapping(value = "/eventseveritystatistics")
    public ResponseEntity<ResponseResult> eventSeverityStatistics(@RequestParam(value = "stationid", required = false) Integer stationId){
        Map<String, List<StatisticsResult>> hashmap = new HashMap<>(4);
        hashmap.put("items", apiService.eventSeverityStatistics(TokenUserUtil.getLoginUserId(), stationId));
        return ResponseHelper.successful(hashmap);
    }

    @ApiOperation("局站分类统计")
    @GetMapping(value = "/stationstatistics")
    public ResponseEntity<ResponseResult> stationStatistics(String resourceStructureIds) {
        Map<String, List<StatisticsResult>> hashMap = new HashMap<>(4);
        Integer userId = TokenUserUtil.getLoginUserId();
        hashMap.put("items", apiService.stationStatistics(userId, resourceStructureIds));
        return ResponseHelper.successful(hashMap);
    }

    @ApiOperation("告警趋势统计")
    @GetMapping(value = "/alarmtrend")
    public ResponseEntity<ResponseResult> alarmtrends(@RequestParam(value = "stationId", required = false) Integer stationId) {
        return ResponseHelper.successful(apiService.alarmtrends(stationId));
    }

    @ApiOperation("关键告警统计接口")
    @GetMapping(value = "/importanteventstatistics", params = {"type"})
    public ResponseEntity<ResponseResult> importantEventStatistics(@RequestParam("type") String type, String resourceStructureIds) {
        if (CharSequenceUtil.isEmpty(type)) {
            throw new BusinessException("基类类型不能为空");
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.importantEventStatistics(type, userId, resourceStructureIds));
    }

    @ApiOperation("Top10局站告警统计")
    @GetMapping(value = "/stationalarmstatistics")
    public ResponseEntity<ResponseResult> stationalarmstatistics(String resourceStructureIds) {
        return ResponseHelper.successful(apiService.stationalarmstatistics(TokenUserUtil.getLoginUserId(), resourceStructureIds));
    }

    @ApiOperation("局站设备列表")
    @GetMapping(value = "/stationequipmentlists", params = {"stationId"})
    public ResponseEntity<ResponseResult> getEquipmentsByStationId(@RequestParam("stationId") Integer stationId) {
        return ResponseHelper.successful(apiService.findEuipmentlist(stationId));
    }

    @ApiOperation("设备数量统计")
    @GetMapping(value = "/devicecatogorystatistics", params = {"baseTypeId"})
    public ResponseEntity<ResponseResult> devicecatogorystatistics(@RequestParam("baseTypeId") Integer baseTypeId) {
        return ResponseHelper.successful(apiService.devicecatogorystatistics(baseTypeId));
    }

    @ApiOperation("设备类型统计设备状态(在线、离线、告警数量)")
    @GetMapping(value = "/equipmentcategorystatisticsstate")
    public ResponseEntity<ResponseResult> equipmentCategoryStatisticsState(EquipmentStateStatisticsVO equipmentStateStatisticsVO) {
        return ResponseHelper.successful(apiService.deviceCategoryStatisticsState(equipmentStateStatisticsVO));
    }

    @ApiOperation("大楼告警数量统计")
    @GetMapping(value = "/buildingalarmstatistics")
    public ResponseEntity<ResponseResult> buildingAlarmStatistics() {
        return ResponseHelper.successful(apiService.alarmStatisticsByResourceType(SourceType.BUILDING.value()));
    }

    @ApiOperation("大楼设备类型告警数量统计")
    @GetMapping(value = "/equipmentcategoryalarmstatistics", params = "resourceStructureId")
    public ResponseEntity<ResponseResult> alarmStatisticsByResourceStructureId(@RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(apiService.alarmStatisticsByResourceStructureId(resourceStructureId));
    }

    @ApiOperation("监控可用度")
    @GetMapping(value = "/structureavailability")
    public ResponseEntity<ResponseResult> structureavailability(String resourceStructureIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.structureavailability(userId, resourceStructureIds));
    }

    @ApiOperation("获取使用中的设别类型")
    @GetMapping(value = "/cfgcommonobject", params = {"type"})
    public ResponseEntity<ResponseResult> configcommonobject(@RequestParam("type") Integer type) {
        if (type == null) {
            throw new BusinessException("类型不能为空");
        }
        return ResponseHelper.successful(apiService.configcommonobject(type));
    }

    @ApiOperation("楼栋层级房间的告警数量统计")
    @GetMapping(value = "/alarmstatistics", params = {"resourceTypeId","resourceStructureId"})
    public ResponseEntity<ResponseResult> alarmStatistics(@RequestParam("resourceTypeId") Integer resourceTypeId, @RequestParam("resourceStructureId") Integer resourceStructureId) {
        return ResponseHelper.successful(apiService.alarmStatistics(resourceTypeId,resourceStructureId));
    }

    @ApiOperation("设备在线、离线、屏蔽数量统计")
    @GetMapping(value = "/equipmentstatestatistics")
    public ResponseEntity<ResponseResult> equipmentStateStatistics(){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.equipmentStateStatistics(userId));
    }
    @ApiOperation("设备总数")
    @GetMapping(value = "/equipmentsum")
    public ResponseEntity<ResponseResult> equipmentSum(){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.equipmentSum(userId));
    }
    @ApiOperation("近N天告警设备数量")
    @GetMapping(value = "/alarmdevicecountbydays", params = {"days"})
    public ResponseEntity<ResponseResult> alarmDeviceCountByDays(Integer days){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getAlarmDeviceCountByDays(userId, days));
    }
    @ApiOperation("近N天告警设备列表")
    @GetMapping(value = "/alarmdevicelistbydays", params = {"days"})
    public ResponseEntity<ResponseResult> alarmDeviceListByDays(Integer days){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getAlarmDeviceListByDays(userId, days));
    }
    @ApiOperation("近N天正常设备数量")
    @GetMapping(value = "/normaldevicecountbydays", params = {"days"})
    public ResponseEntity<ResponseResult> normalDeviceCountByDays(Integer days){
        Integer userId = TokenUserUtil.getLoginUserId();
        Long total = (long) apiService.equipmentSum(userId).getValue();
        Long alarmCount =(long)  apiService.getAlarmDeviceCountByDays(userId, days).getValue();
        return ResponseHelper.successful(total - alarmCount);
    }
    @ApiOperation("近N天告警率")
    @GetMapping(value = "/devicealarmratebydays", params = {"days"})
    public ResponseEntity<ResponseResult> deviceAlarmRateByDays(Integer days){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getDeviceAlarmRateByDays(userId, days));
    }

    @ApiOperation("近N天告警条数")
    @GetMapping(value = "/alarmcountbydaysandtype", params = {"unconfirmFlag","days"})
    public ResponseEntity<ResponseResult> alarmCountByDaysAndType(@RequestParam("unconfirmFlag")Boolean unconfirmFlag, @RequestParam("days")Integer days){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getAlarmCountByDaysAndType(userId,unconfirmFlag, days));
    }
    @ApiOperation("近N天告警列表")
    @GetMapping(value = "/alarmlistbydaysandtype", params = {"unconfirmFlag","days"})
    public ResponseEntity<ResponseResult> alarmlistbydaysandtype(@RequestParam("unconfirmFlag")Boolean unconfirmFlag, @RequestParam("days")Integer days){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getAlarmListByDaysAndType(userId,unconfirmFlag, days));
    }

    @ApiOperation("设备类别告警统计")
    @GetMapping(value = "/equipmentcategoryalarmstatistics", params = {"type"})
    public ResponseEntity<ResponseResult> equipmentCategoryAlarmStatistics(Integer type){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getEquipmentCategoryAlarmStatistics(userId,type));
    }

    @ApiOperation("设备等级告警统计")
    @GetMapping(value = "/equipmentalarmlevelstatistics")
    public ResponseEntity<ResponseResult> equipmentAlarmLevelStatistics(@RequestParam(value = "type",required = false,defaultValue = "2")Integer type,@RequestParam(value = "equipmentId",required = false) Integer equipmentId){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getEquipmentAlarmLevelStatistics(userId,type,equipmentId));
    }


    @ApiOperation("设备告警排名 TopN")
    @GetMapping(value = "/equipmentalarmrankstatistics", params = {"number","type"})
    public ResponseEntity<ResponseResult> equipmentAlarmRankStatistics(@RequestParam("number")Integer number, @RequestParam("type")Integer type){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getEquipmentAlarmRankStatistics(userId,type,number));
    }


    @ApiOperation("区域告警排名 TopN")
    @GetMapping(value = "/areaalarmrankstatistics")
    public ResponseEntity<ResponseResult> equipmentAlarmRankStatistics(@RequestParam(value = "number",required = false)Integer number, @RequestParam(name = "type",required = false)Integer type,@RequestParam(value = "resourceStructureType",required = false)Integer resourceStructureType){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.getAreaAlarmRankStatistics(userId,type,number,resourceStructureType));
    }


    @ApiOperation("告警趋势")
    @GetMapping(value = "/alarmtrendmonthorweek")
    public ResponseEntity<ResponseResult> alarmTrendMonthOrWeekStatistics(@RequestParam(value = "periodType",required = false,defaultValue = "1")Integer periodType){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(apiService.alarmTrendMonthOrWeek(userId,periodType));
    }


    @ApiOperation("告警时长统计分析")
    @GetMapping(value = "/alarmdurationstatistics")
    public ResponseEntity<ResponseResult> alarmDurationStatistics(@RequestParam( "objectId")String objectId, @RequestParam( "topN")Integer topN){
        Integer resourceStructureId;
        try {
            if (StringUtils.isNotEmpty(objectId)) {
                resourceStructureId = Integer.parseInt(objectId);
            } else {
                return ResponseHelper.successful(Collections.emptyList());
            }
        } catch (Exception e) {
            return ResponseHelper.failed("层级 id 传参无效");
        }

        return ResponseHelper.successful(apiService.categoryStatisticsByDuration(resourceStructureId, topN));
    }

    @ApiOperation("告警等级排名")
    @GetMapping(value = "/activehistoryeventseverityrankstatistics")
    public ResponseEntity<ResponseResult> getActiveHistoryEventSeverityRankStatistics(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  Date endTime){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        List<EventLevelRankDTO> eventLevelRankDTOs = idcGraphicDataService.getActiveHistoryEventSeverityRankStatistics(startTime, endTime, userId);
        return ResponseHelper.successful(eventLevelRankDTOs);
    }
}
