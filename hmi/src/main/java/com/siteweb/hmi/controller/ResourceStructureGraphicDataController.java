package com.siteweb.hmi.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.ResourceStructureGraphicDataService;
import com.siteweb.hmi.vo.EquipmentCategoryDataRequest;
import com.siteweb.hmi.vo.GraphicRoomDataRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value="ResourceStructureGraphicDataController",tags={"层级组态态信号接口"})
public class ResourceStructureGraphicDataController {
    @Autowired
    ResourceStructureGraphicDataService resourceStructureGraphicDataService;
    private static final String USER_ID_IS_NULL = "userid is null";

    @ApiOperation(value="获取所有房间楼层告警状态")
    @GetMapping(value = "floorgraphics",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getFloorList(String buildingIds) {
        return ResponseHelper.successful(resourceStructureGraphicDataService.getAllFloorGraphicData(buildingIds));
    }

    @ApiOperation(value="获取所有房间楼层设备实时信号")
    @PostMapping(value = "equipmentcategorygraphics",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDeviceCategoryFloorGraphicDataByCategoryId(@Valid @RequestBody EquipmentCategoryDataRequest equipmentCategoryDataRequest) {
        return  ResponseHelper.successful(resourceStructureGraphicDataService.getDeviceCategoryFloorGraphicDataByCategoryId(equipmentCategoryDataRequest));
    }

    @ApiOperation(value="获取指定房间设备实时信号")
    @PostMapping(value = "roomdevicecategorygraphics",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRoomDeviceCategoryGraphics(@Valid @RequestBody List<GraphicRoomDataRequest> graphicRoomDataRequest) {
        return  ResponseHelper.successful(resourceStructureGraphicDataService.getRoomGraphicDataByDeviceCategory(graphicRoomDataRequest));
    }

    @ApiOperation(value = "根据层级Id获取告警状态列表")
    @PostMapping(value = "/resourcestructurealarmstates", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceStructureAlarmStateByGroup(@RequestBody List<Integer> dataIds){
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(resourceStructureGraphicDataService.getResourceStructureAlarmStateByGroup(dataIds,userId));
    }
}
