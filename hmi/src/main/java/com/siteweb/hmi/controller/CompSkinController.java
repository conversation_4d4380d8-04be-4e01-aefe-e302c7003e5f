package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.entity.CompDataSet;
import com.siteweb.hmi.service.CompSkinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "CompSkinController", tags = {"CompSkin操作接口"})
public class CompSkinController {


    @Autowired
    CompSkinService compSkinService;


    @ApiOperation(value = "根据compDataSetId获取皮肤")
    @GetMapping(value = "/compskins", params = {"compDataSetId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCompskinByCompDataSetId(@RequestParam("compDataSetId") Integer compDataSetId) {
        return ResponseHelper.successful(compSkinService.findByCompDataSetId(compDataSetId));
    }

}
