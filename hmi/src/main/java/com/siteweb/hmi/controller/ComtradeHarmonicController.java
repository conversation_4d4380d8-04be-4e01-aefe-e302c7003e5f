package com.siteweb.hmi.controller;

import com.siteweb.hmi.entity.HarmonicAnalysis;
import com.siteweb.hmi.entity.ChannelHarmonic;
import com.siteweb.hmi.service.ComtradeHarmonicService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * COMTRADE谐波分析REST API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/comtrade/harmonic")
public class ComtradeHarmonicController {
    
    @Autowired
    private ComtradeHarmonicService comtradeHarmonicService;
    
    /**
     * 对指定的COMTRADE文件进行完整的谐波分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @return 谐波分析结果
     */
    @GetMapping("/analyze")
    public ResponseEntity<ResponseResult> analyzeHarmonics(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName) {
        
        log.info("收到谐波分析请求 - 设备ID: {}, 文件: {}", equipmentId, fileName);
        
        try {
            HarmonicAnalysis result = comtradeHarmonicService.analyzeHarmonics(
                equipmentId, fileName);
            
            if (result != null && "SUCCESS".equals(result.getAnalysisStatus())) {
                return ResponseHelper.successful(result);
            } else if (result != null && "PARTIAL".equals(result.getAnalysisStatus())) {
                return ResponseHelper.successful(result);
            } else {
                String message = result != null ? result.getMessage() : "谐波分析失败";
                return ResponseHelper.failed(message);
            }
            
        } catch (Exception e) {
            log.error("谐波分析接口异常: {}", e.getMessage(), e);
            return ResponseHelper.failed("服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 对指定通道进行详细谐波分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @param channelName 通道名称（如：Ia, Ib, Ic等）
     * @param maxHarmonicOrder 最大谐波次数（默认为427）
     * @return 通道谐波分析结果
     */
    @GetMapping("/analyze/channel")
    public ResponseEntity<ResponseResult> analyzeChannelHarmonics(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName,
            @RequestParam String channelName,
            @RequestParam(defaultValue = "427") Integer maxHarmonicOrder) {
        
        log.info("收到单通道谐波分析请求 - 设备ID: {}, 文件: {}, 通道: {}", 
                equipmentId, fileName, channelName);
        
        try {
            ChannelHarmonic result = comtradeHarmonicService.analyzeChannelHarmonics(
                equipmentId, fileName, channelName, maxHarmonicOrder);
            
            if (result != null) {
                return ResponseHelper.successful(result);
            } else {
                return ResponseHelper.failed("未找到指定通道或分析失败");
            }
            
        } catch (Exception e) {
            log.error("单通道谐波分析接口异常: {}", e.getMessage(), e);
            return ResponseHelper.failed("服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取三相电流和电压谐波对比分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @param analysisType 分析类型：current（电流）、voltage（电压）、all（全部，默认）
     * @return 三相谐波分析结果列表
     */
    @GetMapping("/analyze/three-phase")
    public ResponseEntity<ResponseResult> analyzeThreePhaseHarmonics(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName,
            @RequestParam(defaultValue = "all") String analysisType) {
        
        log.info("收到三相谐波分析请求 - 设备ID: {}, 文件: {}, 分析类型: {}", equipmentId, fileName, analysisType);
        
        try {
            long startTime = System.currentTimeMillis();
            
            List<ChannelHarmonic> result = null;
            
            switch (analysisType.toLowerCase()) {
                case "current":
                    result = comtradeHarmonicService.analyzeThreePhaseCurrentHarmonics(
                        equipmentId, fileName);
                    break;
                case "voltage":
                    result = comtradeHarmonicService.analyzeThreePhaseVoltageHarmonics(
                        equipmentId, fileName);
                    break;
                case "all":
                default:
                    // 获取电流和电压谐波分析结果
                    List<ChannelHarmonic> currentResult = comtradeHarmonicService.analyzeThreePhaseCurrentHarmonics(
                        equipmentId, fileName);
                    List<ChannelHarmonic> voltageResult = comtradeHarmonicService.analyzeThreePhaseVoltageHarmonics(
                        equipmentId, fileName);
                    
                    // 合并结果
                    result = new java.util.ArrayList<>();
                    if (currentResult != null) {
                        result.addAll(currentResult);
                    }
                    if (voltageResult != null) {
                        result.addAll(voltageResult);
                    }
                    break;
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            if (result != null && !result.isEmpty()) {
                log.info("三相谐波分析成功完成 - 设备ID: {}, 文件: {}, 分析类型: {}, 分析通道数: {}, 耗时: {}ms", 
                        equipmentId, fileName, analysisType, result.size(), duration);
                
                // 记录每个通道的THD值
                for (ChannelHarmonic harmonic : result) {
                    log.info("通道 {} - THD: {:.2f}%, 奇谐波: {:.2f}%, 偶谐波: {:.2f}%", 
                            harmonic.getChannelName(), 
                            harmonic.getTotalHarmonicDistortion(),
                            harmonic.getOddHarmonics(),
                            harmonic.getEvenHarmonics());
                }
                
                return ResponseHelper.successful(result);
            } else {
                String message = "all".equals(analysisType) ? "未找到三相电流或电压通道数据" : 
                               "current".equals(analysisType) ? "未找到三相电流通道数据" : "未找到三相电压通道数据";
                log.warn("三相谐波分析未找到有效数据 - 设备ID: {}, 文件: {}, 分析类型: {}", equipmentId, fileName, analysisType);
                return ResponseHelper.failed(message);
            }
            
        } catch (Exception e) {
            log.error("三相谐波分析接口异常 - 设备ID: {}, 文件: {}, 分析类型: {}, 错误: {}", 
                     equipmentId, fileName, analysisType, e.getMessage(), e);
            return ResponseHelper.failed("服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取三相电流谐波对比分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @return 三相电流谐波分析结果列表
     * @deprecated 请使用 analyzeThreePhaseHarmonics 方法，并设置 analysisType=current
     */
    @Deprecated
    @GetMapping("/analyze/current")
    public ResponseEntity<ResponseResult> analyzeThreePhaseCurrentHarmonics(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName) {
        
        log.info("收到三相电流谐波分析请求 - 设备ID: {}, 文件: {} (已废弃，建议使用 /analyze/three-phase?analysisType=current)", equipmentId, fileName);
        
        // 直接调用新的合并方法
        return analyzeThreePhaseHarmonics(equipmentId, fileName, "current");
    }
    
    /**
     * 获取三相电压谐波对比分析
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @return 三相电压谐波分析结果列表
     * @deprecated 请使用 analyzeThreePhaseHarmonics 方法，并设置 analysisType=voltage
     */
    @Deprecated
    @GetMapping("/analyze/voltage")
    public ResponseEntity<ResponseResult> analyzeThreePhaseVoltageHarmonics(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName) {
        
        log.info("收到三相电压谐波分析请求 - 设备ID: {}, 文件: {} (已废弃，建议使用 /analyze/three-phase?analysisType=voltage)", equipmentId, fileName);
        
        // 直接调用新的合并方法
        return analyzeThreePhaseHarmonics(equipmentId, fileName, "voltage");
    }
    
    /**
     * 导出谐波分析结果为CSV文件
     * 
     * @param equipmentId 设备ID
     * @param fileName COMTRADE文件名（不含扩展名）
     * @param outputFileName 输出文件名（不含路径和扩展名）
     * @return 导出结果
     */
    @PostMapping("/export/csv")
    public ResponseEntity<ResponseResult> exportHarmonicAnalysisToCSV(
            @RequestParam Integer equipmentId,
            @RequestParam String fileName,
            @RequestParam String outputFileName) {
        
        log.info("收到谐波分析结果CSV导出请求 - 设备ID: {}, 文件: {}, 输出文件: {}", 
                equipmentId, fileName, outputFileName);
        
        try {
            // 先进行谐波分析
            HarmonicAnalysis analysis = comtradeHarmonicService.analyzeHarmonics(
                equipmentId, fileName);
            
            if (analysis == null || !"SUCCESS".equals(analysis.getAnalysisStatus())) {
                return ResponseHelper.failed("谐波分析失败，无法导出");
            }
            
            // 构建输出文件路径
            String outputFilePath = "upload-dir/harmonic/" + equipmentId + "/" + outputFileName + ".csv";
            
            // 导出CSV
            boolean success = comtradeHarmonicService.exportHarmonicAnalysisToCSV(analysis, outputFilePath);
            
            if (success) {
                return ResponseHelper.successful(outputFilePath);
            } else {
                return ResponseHelper.failed("CSV导出失败");
            }
            
        } catch (Exception e) {
            log.error("谐波分析CSV导出接口异常: {}", e.getMessage(), e);
            return ResponseHelper.failed("服务器内部错误: " + e.getMessage());
        }
    }
} 