package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.entity.SceneCompMap;
import com.siteweb.hmi.service.SceneCompMapService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value="SceneCompMapController",tags={"组态空间场景映射操作接口"})
public class SceneCompMapController {
    @Autowired
    SceneCompMapService sceneCompMapService;
    /**
     * GET  /scenecompmaps : get SceneCompMaps by pageCategory and sceneId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of SceneCompMap List in body
     */
    @GetMapping(value = "/scenecompmaps")
    public ResponseEntity<ResponseResult> getSceneCompMapsByPageCategoryAndSceneId(@RequestParam(value = "pageCategory",required =false)  Integer pageCategory) {
        List<SceneCompMap> result = new ArrayList<>();
        if(pageCategory == null){
            result = sceneCompMapService.findByAll();
        }else {
            result = sceneCompMapService.findByPageCategoryAndSceneId(pageCategory);
        }
        return  ResponseHelper.successful(result, HttpStatus.OK);
    }
}
