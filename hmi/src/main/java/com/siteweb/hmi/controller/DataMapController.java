package com.siteweb.hmi.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.DataMapService;
import com.siteweb.hmi.vo.DataMapRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 数据地图访问接口
 */
@RequestMapping("/api")
@RestController
public class DataMapController {
    @Autowired
    DataMapService dataMapService;

    @ApiOperation(value="获取所有数据地图绑定数据")
    @PostMapping(value = "datamapactivesignals",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDataMapGraphicData(@Valid @RequestBody DataMapRequest dataMapRequest) {
        return ResponseHelper.successful(dataMapService.getDataMapDataPoints(TokenUserUtil.getLoginUserId(),dataMapRequest));
    }
}
