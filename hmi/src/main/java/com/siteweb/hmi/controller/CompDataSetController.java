package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.entity.CompDataSet;
import com.siteweb.hmi.entity.GraphicPage;
import com.siteweb.hmi.service.CompDataSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;


@RestController
@RequestMapping("/api")
@Api(value = "CompDataSetController", tags = {"CompDataSet操作接口"})
public class CompDataSetController {

    @Autowired
    CompDataSetService compDataSetService;


    @ApiOperation(value = "获取所有数据集")
    @GetMapping("/compdatasets")
    public ResponseEntity<ResponseResult> findCompDataSets() {
        return ResponseHelper.successful(compDataSetService.findCompDataSets(), HttpStatus.OK);
    }

    @ApiOperation(value = "根据ID获取单个数据集")
    @GetMapping("/compdatasets/{id}")
    public ResponseEntity<ResponseResult> getGraphicPageById(@PathVariable("id") @ApiParam(name = "id", value = "唯一ID", required = true) Integer id) {
        CompDataSet compDataSet = compDataSetService.finCompDataSetById(id);
        return Optional.ofNullable(compDataSet).map(result -> ResponseHelper.successful(compDataSet, HttpStatus.OK)).orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }


}
