package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.ComplexIndexGraphicService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api")
@RestController
public class ComplexIndexGraphicController {

    @Autowired
    ComplexIndexGraphicService complexIndexGraphicService;

    @ApiOperation(value = "获取所有数据集")
    @GetMapping("/complexindexgraphic/LiveComplexIndexPreAlarm")
    public ResponseEntity<ResponseResult> getLiveComplexIndexPreAlarm(@RequestParam("complexIndexIds") List<Integer> complexIndexIds) {
        return ResponseHelper.successful(complexIndexGraphicService.getLiveComplexIndexPreAlarm(complexIndexIds), HttpStatus.OK);
    }
}
