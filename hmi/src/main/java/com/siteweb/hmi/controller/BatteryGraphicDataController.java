package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.BatteryGraphicDataService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电池组态访问箭扣
 */
@RequestMapping("/api")
@RestController
public class BatteryGraphicDataController {


    @Autowired
    BatteryGraphicDataService batteryGraphicDataService;

    @ApiOperation(value = "获取电池工作组态统计")
    @GetMapping("/batteryworkstatus")
    public ResponseEntity<ResponseResult> batteryWorkStutus() {
        return ResponseHelper.successful(batteryGraphicDataService.GetBatteryWorkStatusCount());
    }
}
