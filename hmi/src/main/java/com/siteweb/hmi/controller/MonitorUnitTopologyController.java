package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.MonitorUnitTopologyService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2024/12/28
 */
@RestController
@RequestMapping("/api")
@Api(value="MonitorUnitTopologyController",tags={"采集器拓扑图控制器"})
public class MonitorUnitTopologyController {

    @Autowired
    private MonitorUnitTopologyService monitorUnitTopologyService;

    /**
     * 站点总览
     * @return
     */
    @GetMapping("/monitorunittopology/{monitorUnitId}")
    public ResponseEntity<ResponseResult> getMonitorUnitTopologyById(@PathVariable Integer monitorUnitId,
                                                                     @RequestParam(value = "showVirtualEquipment", required = false, defaultValue = "false") Boolean showVirtualEquipment,
                                                                     @RequestParam(value = "excludePortIds", required = false) List<Integer> excludePortIds) {
        return ResponseHelper.successful(monitorUnitTopologyService.getMonitorUnitTopologyById(monitorUnitId, showVirtualEquipment, excludePortIds));
    }
}
