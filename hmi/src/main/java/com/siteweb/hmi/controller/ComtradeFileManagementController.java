package com.siteweb.hmi.controller;

import com.siteweb.hmi.entity.ComtradeFileRetentionPolicy;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * COMTRADE文件管理控制器
 * 提供运维管理接口
 * 
 * <AUTHOR>
 * @since 2025/5/22
 */
@Slf4j
@RestController
@RequestMapping("/api/comtrade-management")
public class ComtradeFileManagementController {
    
    @Autowired
    private ComtradeFileCleanupService comtradeFileCleanupService;
    
    /**
     * 获取指定设备的文件统计信息
     */
    @GetMapping("/statistics/{equipmentId}")
    public ResponseEntity<Map<String, Object>> getFileStatistics(@PathVariable Integer equipmentId) {
        try {
            ComtradeFileCleanupService.ComtradeFileStatistics statistics = 
                    comtradeFileCleanupService.getFileStatistics(equipmentId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("equipmentId", equipmentId);
            result.put("totalFilePairs", statistics.getTotalFilePairs());
            result.put("totalSize", statistics.getTotalSize());
            result.put("completeFilePairs", statistics.getCompleteFilePairs());
            result.put("orphanedFiles", statistics.getOrphanedFiles());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备ID: {} 的文件统计信息失败", equipmentId, e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取文件统计信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * 手动触发清理指定设备的文件
     */
    @PostMapping("/cleanup/{equipmentId}")
    public ResponseEntity<Map<String, Object>> manualCleanup(
            @PathVariable Integer equipmentId,
            @RequestParam(defaultValue = "false") boolean forceCleanup) {
        try {
            ComtradeFileCleanupService.CleanupResult result = 
                    comtradeFileCleanupService.manualCleanup(equipmentId, forceCleanup);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("equipmentId", equipmentId);
            response.put("deletedFilePairs", result.getDeletedFilePairs());
            response.put("message", result.getMessage());
            response.put("errors", result.getErrors());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("手动清理设备ID: {} 的文件失败", equipmentId, e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "手动清理失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * 获取当前保留策略配置
     */
    @GetMapping("/retention-policy")
    public ResponseEntity<Map<String, Object>> getRetentionPolicy() {
        try {
            ComtradeFileRetentionPolicy policy = comtradeFileCleanupService.getRetentionPolicy();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("maxFilePairsPerDevice", policy.getMaxFilePairsPerDevice());
            result.put("cleanupEnabled", policy.isCleanupEnabled());
            result.put("cleanupBatchSize", policy.getCleanupBatchSize());
            result.put("cleanupInterval", policy.getCleanupInterval());
            result.put("cleanupAfterDownload", policy.isCleanupAfterDownload());
            result.put("maxRetryCount", policy.getMaxRetryCount());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取保留策略配置失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取保留策略配置失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * 更新保留策略配置
     */
    @PostMapping("/retention-policy")
    public ResponseEntity<Map<String, Object>> updateRetentionPolicy(@RequestBody Map<String, Object> configMap) {
        try {
            ComtradeFileRetentionPolicy.ComtradeFileRetentionPolicyBuilder builder = 
                    ComtradeFileRetentionPolicy.builder();
            
            if (configMap.containsKey("maxFilePairsPerDevice")) {
                builder.maxFilePairsPerDevice((Integer) configMap.get("maxFilePairsPerDevice"));
            }
            if (configMap.containsKey("cleanupEnabled")) {
                builder.cleanupEnabled((Boolean) configMap.get("cleanupEnabled"));
            }
            if (configMap.containsKey("cleanupBatchSize")) {
                builder.cleanupBatchSize((Integer) configMap.get("cleanupBatchSize"));
            }
            if (configMap.containsKey("cleanupInterval")) {
                builder.cleanupInterval(((Number) configMap.get("cleanupInterval")).longValue());
            }
            if (configMap.containsKey("cleanupAfterDownload")) {
                builder.cleanupAfterDownload((Boolean) configMap.get("cleanupAfterDownload"));
            }
            if (configMap.containsKey("maxRetryCount")) {
                builder.maxRetryCount((Integer) configMap.get("maxRetryCount"));
            }
            
            ComtradeFileRetentionPolicy policy = builder.build();
            comtradeFileCleanupService.updateRetentionPolicy(policy);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "保留策略配置更新成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新保留策略配置失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "更新保留策略配置失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * 清理所有设备的文件（批量操作）
     */
    @PostMapping("/cleanup-all")
    public ResponseEntity<Map<String, Object>> cleanupAllDevices(
            @RequestParam(defaultValue = "false") boolean forceCleanup) {
        try {
            // 此方法需要从设备服务获取所有设备列表，这里简化处理
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "批量清理功能需要完善实现");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量清理所有设备文件失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "批量清理失败: " + e.getMessage());
            return ResponseEntity.status(500).body(error);
        }
    }
} 