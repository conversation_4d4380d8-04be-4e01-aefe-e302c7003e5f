package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.hmi.entity.GraphicPage;
import com.siteweb.hmi.service.DefaultGraphicPageService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/02/06
 */
@RestController
@RequestMapping("/api")
@Api(value="GraphicPageController",tags={"GraphicPage操作接口"})
public class DefaultGraphicPageController {

    @Autowired
    private DefaultGraphicPageService defaultGraphicPageService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @PutMapping(value = "/graphicpages/default/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateDefaultGraphic(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(defaultGraphicPageService.updateDefaultGraphic(id));
    }

    @GetMapping(value = "/graphicpages/default", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateDefaultGraphic(@RequestParam(value = "pageCategory")  Integer pageCategory,
                                                               @RequestParam(value = "objectId")  Integer objectId) {
        GraphicPage defaultGraphicPage = defaultGraphicPageService.getDefaultGraphicPage(pageCategory, objectId);
        if (Objects.isNull(defaultGraphicPage)) {
            return ResponseHelper.failed("0", localeMessageSourceUtil.getMessage("common.graphicPage.noDefaultGraphicPage"), HttpStatus.OK);
        }
        return ResponseHelper.successful(defaultGraphicPage);
    }
}
