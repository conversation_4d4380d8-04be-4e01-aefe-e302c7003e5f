package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.MatrixChartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value="MatrixChartController",tags={"矩阵查询接口"})
public class MatrixChartController {

    @Autowired
    MatrixChartService matrixChartService;

    @ApiOperation(value="获取矩阵api列表")
    @GetMapping(value = "/matrixcharts")
    public ResponseEntity<ResponseResult> findMatrixCharts() {
        return ResponseHelper.successful(matrixChartService.findMatrixCharts());
    }
}
