package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.dto.GraphicPageQueryParamDTO;
import com.siteweb.hmi.entity.GraphicPage;
import com.siteweb.hmi.service.GraphicPageService;
import com.siteweb.hmi.vo.GraphicPageBatchRequest;
import com.siteweb.hmi.vo.GraphicPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * graphicpage info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-04-22 13:33:20
 */
@RestController
@RequestMapping("/api")
@Api(value="GraphicPageController",tags={"GraphicPage操作接口"})
public class GraphicPageController {

    private static final String ENTITY_NAME = "GraphicPage";
    @Autowired
    private GraphicPageService graphicPageService;


    @ApiOperation("批量应用组态页")
    @PostMapping(value = "/graphicpages/batchapply",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchApplyGraphicPage(@RequestBody GraphicPageBatchRequest graphicPageBatchRequest) {
        graphicPageService.batchApplyPage(graphicPageBatchRequest);
        return ResponseHelper.successful();
    }

    /**
     * GET  /graphicpages : get the GraphicPages.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of GraphicPages in body
     */
    @ApiOperation(value="获取所有GraphicPage实体")
    @GetMapping("/graphicpages" )
    public ResponseEntity<ResponseResult>  getGraphicPages() {
        return  ResponseHelper.successful(graphicPageService.findGraphicPages(), HttpStatus.OK);
    }

    @ApiOperation(value="根据组态类型获取GraphicPage实体")
    @GetMapping(value = "/graphicpages",params = {"pageCategories"})
    public ResponseEntity<ResponseResult>  getGraphicPagesByPageCategories(String pageCategories) {
        return  ResponseHelper.successful(graphicPageService.findByPageCategories(pageCategories));
    }

    /**
     * GET  /graphicpages/:id  get the GraphicPage by id.
     *
     * @param  id the GraphicPageId
     * @return the ResponseEntity with status 200 (OK) and with body the GraphicPage, or with status 404 (Not Found)
     */
    @ApiOperation(value="根据ID获取单个GraphicPage实体")
    @GetMapping("/graphicpages/{id}")
    public ResponseEntity<ResponseResult>  getGraphicPageById(@PathVariable("id" )  @ApiParam(name="id",value="唯一ID",required=true) Integer id) {
        GraphicPage graphicPage =graphicPageService.findById(id);
        return Optional.ofNullable(graphicPage)
                .map(result -> ResponseHelper.successful(graphicPage,  HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /graphicpages : create a new GraphicPage
     *
     * @param graphicPageVO the GraphicPage to create
     * @return the ResponseEntity with status 201 (Created) and with body the new GraphicPage,
     * or with status 400 (Bad Request) if the GraphicPage has already an ID
     */
    @ApiOperation(value="新增GraphicPage实体")
    @PostMapping(value = "/graphicpages")
    public ResponseEntity<ResponseResult>  createGraphicPage(@Valid @RequestBody GraphicPageVO graphicPageVO){
        if (graphicPageVO.getId() != null) {
            return ResponseHelper.failed("-1", "A new GraphicPage  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }

        graphicPageService.createGraphicPage(graphicPageVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * PUT  /graphicpages : Updates an existing GraphicPage.
     *
     * @param graphicPageVO the GraphicPage to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated GraphicPage,
     * or with status 404 (Not Found) if the graphicPageId is not exists,
     */
    @ApiOperation(value="更新GraphicPage实体")
    @PutMapping(value = "/graphicpages")
    public ResponseEntity<ResponseResult>  updateGraphicPage(@Valid @RequestBody GraphicPageVO graphicPageVO) {

        if (graphicPageVO.getId() == null){
            return ResponseHelper.failed("-1", "GraphicPage   Not Found.", HttpStatus.NOT_FOUND);
        }
        graphicPageService.updateGraphicPage(graphicPageVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * DELETE  /graphicpages/:id : delete the GraphicPage by id.
     *
     * @param id the id of the GraphicPage to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value="根据唯一ID删除GraphicPage实体")
    @DeleteMapping(value = "/graphicpages/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  deleteGraphicPage(@PathVariable @ApiParam(name="id",value="唯一ID",required=true) Integer id) {

        GraphicPage graphicPage = graphicPageService.findById(id);
        if (graphicPage == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        graphicPageService.deleteById(id);
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * GET  /graphicpages : get graphicpage by pageCategory and bizObjectId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of GraphicPage in body
     */
    @GetMapping(value = "/graphicpages",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"pageCategory","objectId"})
    @ApiOperation("通过类型与id获取组态页")
    public ResponseEntity<ResponseResult>  getGraphicPageByPageCategoryAndBizObjectId(@RequestParam(value = "pageCategory",required =true)  Integer pageCategory,
                                                                        @RequestParam(value = "objectId",required =true)  Integer objectId) {
        List<GraphicPage> pages =  graphicPageService.findByGraphicPageByPageCategoryAndObjectId(pageCategory,objectId);
        return  ResponseHelper.successful(pages,HttpStatus.OK);
    }

    @ApiOperation("通过类型与id集合获取组态页集合")
    @PostMapping("/graphicpagecrumb")
    public ResponseEntity<ResponseResult> getGraphicPageCrumb(@RequestBody List<GraphicPageQueryParamDTO> graphicPageQueryParamDTOList){
        return ResponseHelper.successful(graphicPageService.getGraphicPageCrumb(graphicPageQueryParamDTOList));
    }

    @ApiOperation("通过基类id与最近的更新时间获取批量应用列表")
    @GetMapping(value = "/graphicpages",params = {"baseEquipmentId","updateTime"})
    public ResponseEntity<ResponseResult> getBatchApplyList(Integer baseEquipmentId, Date updateTime){
        return ResponseHelper.successful(graphicPageService.findBatchApplyList(baseEquipmentId, updateTime));
    }

    @GetMapping(value = "/graphicpages/equipmentbasetypechange", params = "equipmentId")
    public ResponseEntity<ResponseResult> equipmentBaseTypeChange(Integer equipmentId){
        boolean result = graphicPageService.equipmentBaseTypeChange(equipmentId);
        return ResponseHelper.successful(result);
    }
}
