package com.siteweb.hmi.controller;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.hmi.service.MixChartGraphicDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value="MixChartGraphicDataController",tags={"矩阵查询组态接口"})
public class MixChartGraphicDataController {

    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    private MixChartGraphicDataService mixChartGraphicDataService;

    /**
     * 站点总览
     * @return
     */
    @GetMapping("/stationcategorylist")
    public ResponseEntity<ResponseResult> getStationById() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(mixChartGraphicDataService.getStationCategoryResultList(userId));
    }

    /**
     *  监控总览(默认区县统计)
     * @return
     */
    @GetMapping({"/stationhaltpoweroffgen/{structuretypeid}", "/stationhaltpoweroffgen"})
    public ResponseEntity<ResponseResult> stationHaltPoweroffGen(@PathVariable(value = "structuretypeid", required = false) Integer structureTypeId, Integer resourceStructureId) {
        if (ObjectUtil.isEmpty(structureTypeId)) {
            structureTypeId = 103;
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(mixChartGraphicDataService.getStationHaltPowerOffGen(userId, structureTypeId, resourceStructureId));
    }

    /**
     * 告警TOP5
     * @return
     */
    @GetMapping("/stationeventtopfive")
    public ResponseEntity<ResponseResult> stationEventTopfive() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(mixChartGraphicDataService.getStationEventTop5(userId));
    }

    /**
     * 可用率
     * @return
     */
    @GetMapping("/stationavailability")
    public ResponseEntity<ResponseResult> stationAvailability() {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(mixChartGraphicDataService.getStationAvailability(userId));
    }

    /**
     * 监控统计
     * @return
     */
    @GetMapping("/devicemonitoringstatus")
    public ResponseEntity<ResponseResult> deviceMonitoringStatistics() {
        return ResponseHelper.successful(mixChartGraphicDataService.deviceMonitoringStatus());
    }

    @GetMapping("/alarmgrouptrends")
    public ResponseEntity<ResponseResult> getAlarmGroupTrends(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                              @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  Date endTime,
                                                              String resourceStructureIds) {
        return ResponseHelper.successful(mixChartGraphicDataService.getAlarmGroupData(startTime,endTime, resourceStructureIds));
    }

    /**
     * 设备分类统计
     * @return
     */
    @GetMapping("/equipmentstatisticsbycategory")
    public ResponseEntity<ResponseResult> getAlarmGroupTrends(String resourceStructureIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        List<Integer> resourceStructureIdList = null;
        if (CharSequenceUtil.isNotBlank(resourceStructureIds)) {
            resourceStructureIdList = StringUtils.splitToIntegerList(resourceStructureIds);
        }
        return ResponseHelper.successful(mixChartGraphicDataService.getEquipmentStatistics(userId, resourceStructureIdList));
    }

    @ApiOperation("中断统计")
    @GetMapping("/interruptstatistics")
    public ResponseEntity<ResponseResult> getInterruptStatistics(String resourceStructureIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(mixChartGraphicDataService.getInterruptStatistics(userId,resourceStructureIds));
    }

    @ApiOperation("根据事件类型计算可用率")
    @GetMapping(value = "/availablebyeventcategory", params = {"eventCategoryId","startTime","endTime"})
    public ResponseEntity<ResponseResult> getAvailableByEventCategory(Integer eventCategoryId, Date startTime, Date endTime, String resourceStructureIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(mixChartGraphicDataService.getAvailableByEventCategoryId(userId, eventCategoryId, startTime, endTime,resourceStructureIds));
    }
}
