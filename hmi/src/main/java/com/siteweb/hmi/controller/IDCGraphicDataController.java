package com.siteweb.hmi.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.IDCGraphicDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value="IDCGraphicDataController",tags={"IDC统计类控件查询接口"})
public class IDCGraphicDataController {
    @Autowired
    IDCGraphicDataService idcGraphicDataService;
    private static final String USER_ID_IS_NULL = "userid is null";

    @ApiOperation(value="获取层级告警统计")
    @GetMapping(value = "idceventseveritystatistics",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEventStatisticsById(String objectId, Integer pageCategory, String resourceStructureIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(idcGraphicDataService.getEventStatisticsByObjectIdAndType(userId, objectId, pageCategory, resourceStructureIds));
    }
    @ApiOperation(value="获取层级设备状态统计")
    @GetMapping(value = "equipmentonlinestatus",
            params = {"objectId", "pageCategory"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public  ResponseEntity<ResponseResult> getEquipmentStateStatistics(@RequestParam(required = false) Integer objectId, @RequestParam(required = false) Integer pageCategory) {
        return ResponseHelper.successful(idcGraphicDataService.getEquipmentStateStatistics(objectId,pageCategory));
    }
    @ApiOperation(value="告警时长统计")
    @GetMapping(value = "/eventstatisticsbyduration",
            params = {"objectId", "pageCategory"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getAlarmStackBarChartData(@RequestParam(value = "objectId") Integer objectId, @RequestParam(value = "pageCategory") Integer pageCategory) {
        return  ResponseHelper.successful(idcGraphicDataService.getEventStatisticsByDuration(objectId,pageCategory));
    }
    @ApiOperation(value="获取楼宇设备类状态统计")
    @GetMapping(value = "/alarmequipmentcategorystatistics",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getAlarmEquipmentCategoryStatistics() {
        return  ResponseHelper.successful(idcGraphicDataService.getAlarmEquipmentCategoryStatistics());
    }
    @GetMapping(value = "/eventstatisticsoverviews",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getEventStatisticsOverview(@RequestParam(value = "orderByBirthTime",required = false) Integer orderByBirthTime) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(idcGraphicDataService.getEventStatisticsOverview(userId,orderByBirthTime != null && orderByBirthTime > 0));
    }
    @ApiOperation(value = "根据层级Id获取告警数量统计列表")
    @PostMapping(value = "/eventstatisticsbyresourcestructuregroup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  getEventStatisticsByResourceStructureGroup(@RequestBody List<Integer> dataIds) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    USER_ID_IS_NULL,
                    HttpStatus.BAD_REQUEST);
        }
        return  ResponseHelper.successful(idcGraphicDataService.getResourceStructureAlarmCountByGroup(dataIds,userId));
    }

    @ApiOperation(value = "获取南水北调江苏水源首页数据")
    @GetMapping(value = "/nsbdjssyindex", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getNsbdjssyIndexData() {
        return ResponseHelper.successful(idcGraphicDataService.getNsbdjssyIndexData());
    }
}
