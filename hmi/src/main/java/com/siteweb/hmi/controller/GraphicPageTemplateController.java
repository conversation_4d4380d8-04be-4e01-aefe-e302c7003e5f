package com.siteweb.hmi.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.entity.GraphicPageTemplate;
import com.siteweb.hmi.service.GraphicPageTemplateService;
import com.siteweb.hmi.vo.GraphicPageTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * graphicpagetemplate info table
 *
 * <AUTHOR>
 * @email 
 * @date 2022-04-22 13:33:20
 */
@RestController
@RequestMapping("/api")
@Api(value="GraphicPageTemplateController",tags={"GraphicPageTemplate操作接口"})
public class GraphicPageTemplateController {

    private static final String ENTITY_NAME = "GraphicPageTemplate";

    @Autowired
    private GraphicPageTemplateService graphicPageTemplateService;

    /**
     * GET  /graphicpagetemplates : get the GraphicPageTemplates.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of GraphicPageTemplates in body
     */
    @ApiOperation(value="获取所有GraphicPageTemplate实体")
    @GetMapping("/graphicpagetemplates" )
    public ResponseEntity<ResponseResult>  getGraphicPageTemplates() {
        return  ResponseHelper.successful(graphicPageTemplateService.findGraphicPageTemplates(), HttpStatus.OK);
    }

    /**
     * GET  /graphicpagetemplates/:id  get the GraphicPageTemplate by id.
     *
     * @param  id the GraphicPageTemplateId
     * @return the ResponseEntity with status 200 (OK) and with body the GraphicPageTemplate, or with status 404 (Not Found)
     */
    @ApiOperation(value="根据ID获取单个GraphicPageTemplate实体")
    @GetMapping("/graphicpagetemplates/{id}")
    public ResponseEntity<ResponseResult>  getGraphicPageTemplateById(@PathVariable("id" )  @ApiParam(name="id",value="唯一ID",required=true) Integer id) {
        GraphicPageTemplate graphicPageTemplate =graphicPageTemplateService.findById(id);
        return Optional.ofNullable(graphicPageTemplate)
                .map(result -> ResponseHelper.successful(graphicPageTemplate,  HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    /**
     * Post /graphicpagetemplates : create a new GraphicPageTemplate
     *
     * @param graphicPageTemplateVO the GraphicPageTemplate to create
     * @return the ResponseEntity with status 201 (Created) and with body the new GraphicPageTemplate,
     * or with status 400 (Bad Request) if the GraphicPageTemplate has already an ID
     */
    @ApiOperation(value="新增GraphicPageTemplate实体")
    @PostMapping(value = "/graphicpagetemplates")
    public ResponseEntity<ResponseResult>  createGraphicPageTemplate(@Valid @RequestBody GraphicPageTemplateVO graphicPageTemplateVO){
        if (graphicPageTemplateVO.getId() != null) {
            return ResponseHelper.failed("-1", "A new GraphicPageTemplate  cannot already have an ID", HttpStatus.BAD_REQUEST);
        }

        graphicPageTemplateService.createGraphicPageTemplate(graphicPageTemplateVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * PUT  /graphicpagetemplates : Updates an existing GraphicPageTemplate.
     *
     * @param graphicPageTemplateVO the GraphicPageTemplate to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated GraphicPageTemplate,
     * or with status 404 (Not Found) if the graphicPageTemplateId is not exists,
     */
    @ApiOperation(value="更新GraphicPageTemplate实体")
    @PutMapping(value = "/graphicpagetemplates")
    public ResponseEntity<ResponseResult>  updateGraphicPageTemplate(@Valid @RequestBody GraphicPageTemplateVO graphicPageTemplateVO) {

        if (graphicPageTemplateVO.getId() == null){
            return ResponseHelper.failed("-1", "GraphicPageTemplate   Not Found.", HttpStatus.NOT_FOUND);
        }
        graphicPageTemplateService.updateGraphicPageTemplate(graphicPageTemplateVO.build());
        return  ResponseHelper.successful(HttpStatus.OK);
    }

    /**
     * DELETE  /graphicpagetemplates/:id : delete the GraphicPageTemplate by id.
     *
     * @param id the id of the GraphicPageTemplate to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value="根据唯一ID删除GraphicPageTemplate实体")
    @DeleteMapping(value = "/graphicpagetemplates/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult>  deleteGraphicPageTemplate(@PathVariable @ApiParam(name="id",value="唯一ID",required=true) Integer id) {

        GraphicPageTemplate graphicPageTemplate = graphicPageTemplateService.findById(id);
        if (graphicPageTemplate == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        graphicPageTemplateService.deleteById(id);
        return  ResponseHelper.successful(HttpStatus.OK);
    }

}
