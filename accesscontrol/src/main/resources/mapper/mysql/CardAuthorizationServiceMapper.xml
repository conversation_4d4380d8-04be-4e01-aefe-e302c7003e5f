<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.CardAuthorizationServiceMapper">
    <select id="findCardAuthorizationPage" resultType="com.siteweb.accesscontrol.vo.AccessCardAuthorizationVO">
        SELECT a.EquipmentId
        ,a.EquipmentName
        ,f.DoorId
        ,e.DoorName
        ,g.CardName
        ,g.CardCode
        ,g.CardGroup AS CardGroupName
        ,f.CardId
        ,g.UserId
        ,f.TimeGroupId
        ,i.TimeGroupName
        ,f.StartTime
        ,f.EndTime
        ,c.AreaId
        ,c.AreaName
        ,h.TimeGroupType
        ,d.StationName
        FROM TBL_Equipment a
        LEFT JOIN tbl_doorareamap b ON a.EquipmentId = b.equipmentid
        LEFT JOIN tbl_doorarea c ON b.areaid = c.areaid
        LEFT JOIN tbl_Station d on a.StationId = d.StationId
        INNER JOIN TBL_Door e ON  e.EquipmentId = a.EquipmentId
        INNER JOIN TBL_DoorCard f ON f.DoorId = e.DoorId
        INNER JOIN TBL_Card g ON f.CardId = g.CardId
        INNER JOIN TBL_DoorTimeGroup h ON f.TimeGroupId = h.TimeGroupId AND f.DoorId = h.DoorId AND f.TimeGroupType = h.TimeGroupType
        INNER JOIN TBL_TimeGroup i ON h.TimeGroupId = i.TimeGroupId
        WHERE a.EquipmentCategory = 82 AND a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        <if test="cardCode != null and cardCode != ''">
            AND g.CardCode LIKE concat('%',#{cardCode},'%')
        </if>
        <if test="cardGroup != null">
            AND g.CardGroup = #{cardGroup}
        </if>
        <if test="userId != null">
            AND g.UserId = #{userId}
        </if>
        <if test="equipmentId != null">
            AND a.EquipmentId = #{equipmentId}
        </if>
        <if test="areaId != null and areaId != -1">
            AND b.areaid = #{areaId}
        </if>
        <if test="areaId != null and areaId == -1">
            AND b.areaid IS NULL
        </if>
        <if test="stationName != null and stationName != ''">
            AND d.StationName like CONCAT('%',#{stationName},'%')
        </if>
        <if test="employeeIds != null and employeeIds.size > 0">
            AND g.UserId in
            <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
                #{employeeId}
            </foreach>
        </if>
    </select>

    <select id="findCardAuthorizationCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM TBL_Equipment a
        LEFT JOIN tbl_doorareamap b ON a.EquipmentId = b.equipmentid
        LEFT JOIN tbl_doorarea c ON b.areaid = c.areaid
        LEFT JOIN tbl_Station d on a.StationId = d.StationId
        INNER JOIN TBL_Door e ON  e.EquipmentId = a.EquipmentId
        INNER JOIN TBL_DoorCard f ON f.DoorId = e.DoorId
        INNER JOIN TBL_Card g ON f.CardId = g.CardId
        INNER JOIN TBL_DoorTimeGroup h ON f.TimeGroupId = h.TimeGroupId AND f.DoorId = h.DoorId AND f.TimeGroupType = h.TimeGroupType
        INNER JOIN TBL_TimeGroup i ON h.TimeGroupId = i.TimeGroupId
        WHERE a.EquipmentCategory = 82 AND a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        <if test="cardCode != null and cardCode != ''">
            AND g.CardCode LIKE concat('%',#{cardCode},'%')
        </if>
        <if test="cardGroup != null">
            AND g.CardGroup = #{cardGroup}
        </if>
        <if test="userId != null">
            AND g.UserId = #{userId}
        </if>
        <if test="equipmentId != null">
            AND a.EquipmentId = #{equipmentId}
        </if>
        <if test="areaId != null and areaId != -1">
            AND b.areaid = #{areaId}
        </if>
        <if test="areaId != null and areaId == -1">
            AND b.areaid IS NULL
        </if>
        <if test="stationName != null and stationName != ''">
            AND d.StationName like CONCAT('%',#{stationName},'%')
        </if>
        <if test="employeeIds != null and employeeIds.size > 0">
            AND g.UserId in
            <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
                #{employeeId}
            </foreach>
        </if>
    </select>
    <select id="findFingerPrintAuthorizationInfo" resultType="com.siteweb.accesscontrol.vo.AccessCardAuthorizationVO">
        SELECT a.FingerPrintId,a.cardId,a.DoorId,a.TimeGroupId,
        CASE WHEN b.LastUpdateTime > a.LastUpdateTime OR b.LastUpdateTime IS NOT NULL AND a.LastUpdateTime IS NULL THEN 1 ELSE 0 END FingerprintOutdate
        FROM TBL_FingerprintAuth a
        INNER JOIN TBL_FingerPrintCardMap b ON a.CardId = b.CardId AND a.FingerprintId = b.FingerprintId
        WHERE a.CardId IN
        <foreach collection="records" item="item" open="(" close=")" separator=",">
            #{item.cardId}
        </foreach>
        and
        a.DoorId IN
        <foreach collection="records" item="item" open="(" close=")" separator=",">
            #{item.doorId}
        </foreach>
        and
        a.TimeGroupId IN
        <foreach collection="records" item="item" open="(" close=")" separator=",">
            #{item.timeGroupId}
        </foreach>
    </select>
    <select id="findFacePrintAuthorizationInfo" resultType="com.siteweb.accesscontrol.vo.AccessCardAuthorizationVO">
        SELECT a.FaceId,a.cardId,a.DoorId,a.TimeGroupId,
        CASE WHEN b.LastUpdateTime > a.LastUpdateTime OR b.LastUpdateTime IS NOT NULL AND a.LastUpdateTime IS NULL THEN 1 ELSE 0 END faceOutDate
        FROM tbl_facedataauth a
        INNER JOIN tbl_facedatamap b ON a.CardId = b.CardId AND a.FaceId = b.FaceId
        WHERE a.CardId IN
        <foreach collection="records" item="item" open="(" close=")" separator=",">
            #{item.cardId}
        </foreach>
        and
        a.DoorId IN
        <foreach collection="records" item="item" open="(" close=")" separator=",">
            #{item.doorId}
        </foreach>
        and
        a.TimeGroupId IN
        <foreach collection="records" item="item" open="(" close=")" separator=",">
            #{item.timeGroupId}
        </foreach>
    </select>
</mapper>