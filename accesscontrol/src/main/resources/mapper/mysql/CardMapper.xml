<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.CardMapper">
    <sql id="accessCardSql">
        SELECT a.CardId,
               a.CardCode,
               a.CardName,
               a.CardGroup,
               c.ItemValue CardGroupName,
               b.ItemValue CardStatusName,
               a.RegisterTime,
               a.StartTime,
               a.EndTime,
               a.CardStatus,
               a.UserId,
               d.Password,
               d.cardcodetype,
               e.ItemValue as CardCodeTypeName,
               f.CardType,
               g.Vendor,
               g.FingerprintId,
               h.FaceId
        FROM tbl_card a
                 INNER JOIN TBL_DataItem b ON a.CardStatus = b.ItemId AND b.EntryId = 46
                 LEFT JOIN TBL_DataItem c ON a.CardGroup = c.ItemId AND c.EntryId = 75
                 LEFT JOIN TBL_CardExt d ON a.CardId = d.CardId
                 LEFT JOIN TBL_DataItem e ON d.cardcodetype = e.ItemId AND e.EntryId = 3004
                 LEFT JOIN TBL_CardTypeMap f ON a.CardId = f.CardId
                 LEFT JOIN TBL_FingerPrintCardMap g ON a.CardId = g.CardId
                 LEFT JOIN TBL_FaceDataMap h ON a.CardId = h.CardId
    </sql>
    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*) count
        FROM TBL_Card a
                 LEFT JOIN tbl_cardext b ON a.cardId = b.cardId
        WHERE a.CardCode = #{cardCode}
          AND b.CardCodeType = #{cardCodeType}
          <if test="cardId != null">
              AND a.CardId != #{cardId}
          </if>
    </select>
    <select id="findById" resultType="com.siteweb.accesscontrol.dto.AccessCardDTO">
        <include refid="accessCardSql"/>
        WHERE a.CardId = #{cardId}
        group by a.CardId
    </select>
    <select id="findCardByCardGroup" resultType="com.siteweb.accesscontrol.dto.AccessCardDTO">
        <include refid="accessCardSql"/>
        <where>
            <if test="employeeIds != null and employeeIds.size > 0">
                AND a.UserId IN
                <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
                    #{employeeId}
                </foreach>
            </if>
            <if test="cardGroup != null">
                AND a.CardGroup = #{cardGroup}
            </if>
        </where>
        group by a.CardId
    </select>
    <select id="findInfoByCardId" resultType="com.siteweb.accesscontrol.dto.AccessCardInfoDTO">
        SELECT a.CardId,
               a.CardName,
               a.CardCode,
               b.Password,
               a.EndTime,
               IFNULL(b.CardCodeType, 16)                       cardCodeType,
               IFNULL(b.CardCodeConv, CONV(a.CardCode, 16, 10)) cardCodeConv
        FROM TBL_Card a
                 LEFT JOIN TBL_CardExt b ON a.CardId = b.CardId
        WHERE a.CardId = #{cardId}
    </select>
</mapper>