<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.FaceDataMapper">
    <delete id="deleteByCardId">
        DELETE
        a
        FROM tbl_facedata a INNER JOIN tbl_facedatamap b on a.FaceId = b.FaceId
        WHERE b.CardId = #{cardId}
    </delete>
    <select id="findFaceIdByCardId" resultType="java.lang.Integer">
        SELECT b.FaceId
        FROM TBL_Card a
                 LEFT JOIN TBL_FaceDataMap b ON a.CardId = b.CardId
        WHERE a.CardId = #{cardId}
    </select>
    <select id="findByCardId" resultType="com.siteweb.accesscontrol.entity.FaceData">
        SELECT a.FaceId, a.FaceData
        FROM TBL_FaceData a
                 INNER JOIN TBL_FaceDataMap b ON a.FaceId = b.FaceId
        WHERE b.CardId = #{cardId}
    </select>
</mapper>