<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorMapper">
    <select id="findCardAuthEquipment" resultType="java.lang.Integer">
        SELECT DISTINCT a.EquipmentId
        FROM TBL_Door a
                 INNER JOIN TBL_DoorCard b ON a.DoorId = b.DoorId
        WHERE b.CardId = #{cardId}
    </select>
    <select id="findDoorCategoryByEquipmentId" resultType="java.lang.Integer">
        SELECT CONVERT(c.Expression, SIGNED)
        FROM TBL_Equipment a
                 INNER JOIN TBL_EquipmentTemplate b ON a.EquipmentTemplateId = b.EquipmentTemplateId
                 INNER JOIN TBL_Signal c ON b.EquipmentTemplateId = c.EquipmentTemplateId AND c.ChannelNo = 39
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findInfoByEquipmentId" resultType="com.siteweb.accesscontrol.vo.EquipmentDoor">
        SELECT a.StationId, a.EquipmentId, b.Password, b.DoorNo, b.DoorId, b.Category,ifnull(c.Display,16) CardCodeType
        FROM TBL_Equipment a
                 INNER JOIN TBL_Door b ON a.EquipmentId = b.EquipmentId
                 LEFT JOIN TBL_DoorController c ON b.DoorControlId = c.DoorControlId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findDoorProperties" resultType="java.lang.Integer">
        SELECT DISTINCT b.PropertyType
        FROM TBL_Door a
                 INNER JOIN TBL_DoorProperty b ON a.Category = b.Category
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findDoorInfoByEquipmentId" resultType="com.siteweb.accesscontrol.dto.DoorDTO">
        SELECT a.DoorId,
               a.DoorName,
               a.DoorNo,
               a.Category,
               a.Password,
               a.EquipmentId,
               c.EquipmentName
        FROM tbl_door a
                 INNER JOIN TBL_Equipment c ON a.EquipmentId = c.EquipmentId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findDoorEquipmentByEquipmentIds" resultType="com.siteweb.accesscontrol.dto.DoorEquipmentVO">
        SELECT a.EquipmentId, a.EquipmentName
        FROM TBL_Equipment a
        INNER JOIN TBL_EquipmentTemplate b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE b.EquipmentBaseType = 1001 AND a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
</mapper>