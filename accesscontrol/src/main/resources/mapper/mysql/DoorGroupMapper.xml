<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorGroupMapper">
    <select id="findDoorGroupTree" resultType="com.siteweb.accesscontrol.dto.DoorGroupEquipmentDTO">
        SELECT c.DoorGroupId,
               c.DoorGroupName,
               c.description,
               a.equipmentid,
               a.equipmentname,
               d.category
        FROM tbl_equipment a
                 LEFT JOIN tbl_doorgroupmap b ON b.equipmentId = a.EquipmentId
                 LEFT JOIN tbl_doorgroup c ON c.DoorGroupId = b.DoorGroupId
                 INNER JOIN TBL_Door d ON a.EquipmentId = d.EquipmentId
        WHERE a.EquipmentCategory = 82
    </select>
</mapper>