<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorControlMapper">
    <delete id="batchDeleteWaitQueue">
        DELETE FROM tbl_activecontrolofdoor WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    <select id="findDoorControl" resultType="com.siteweb.accesscontrol.dto.DoorControlDTO">
        SELECT * FROM (
        SELECT activeControl.EquipmentId,
        activeControl.EquipmentName,
        activeControl.ControlName,
        activeControl.ControlResult,
        activeControl.ControlExecuterId,
        activeControl.ControlExecuterIdName,
        activeControl.SerialNo,
        activeControl.ParameterValues,
        activeControl.StartTime,
        activeControl.EndTime,
        activeControl.ConfirmTime
        FROM TBL_ActiveControl activeControl
        INNER JOIN TBL_Equipment equipment on activeControl.EquipmentId = equipment.EquipmentId
        INNER JOIN TBL_Control control ON equipment.EquipmentTemplateId = control.EquipmentTemplateId AND
        activeControl.ControlId = control.ControlId
        WHERE equipment.EquipmentCategory IN (82, 97, 98) AND activeControl.StartTime &gt;= #{startTime} AND
        activeControl.StartTime &lt;= #{endTime}
        AND control.ControlCategory NOT IN (12,13,14,31,32,33,34,35,36,40,41)
        <if test="equipmentId != null">
            AND activeControl.equipmentId = #{equipmentId}
        </if>
        <if test="controlResultType != null">
            AND activeControl.ControlResultType = #{controlResultType}
        </if>
        <if test="userId != null">
            AND activeControl.ControlExecuterId = #{userId}
        </if>
        AND equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND activeControl.controlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        UNION ALL
        SELECT historyControl.EquipmentId,
        historyControl.EquipmentName,
        historyControl.ControlName,
        historyControl.ControlResult,
        historyControl.ControlExecuterId,
        historyControl.ControlExecuterIdName,
        historyControl.SerialNo,
        historyControl.ParameterValues,
        historyControl.StartTime,
        historyControl.EndTime,
        historyControl.ConfirmTime
        FROM TBL_HistoryControl historyControl
        INNER JOIN TBL_Equipment equipment on historyControl.EquipmentId = equipment.EquipmentId
        INNER JOIN TBL_Control control ON equipment.EquipmentTemplateId = control.EquipmentTemplateId AND
        historyControl.ControlId = control.ControlId
        WHERE equipment.EquipmentCategory IN (82, 97, 98) AND historyControl.StartTime &gt;= #{startTime} AND
        historyControl.StartTime &lt;= #{endTime}
        AND control.ControlCategory NOT IN (12,13,14,31,32,33,34,35,36,40,41)
        <if test="equipmentId != null">
            AND historyControl.equipmentId = #{equipmentId}
        </if>
        <if test="controlResultType != null">
            AND historyControl.ControlResultType = #{controlResultType}
        </if>
        <if test="userId != null">
            AND historyControl.ControlExecuterId = #{userId}
        </if>
        AND equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND historyControl.controlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        ) result
        order by result.SerialNo desc
    </select>
    <select id="findCardControl" resultType="com.siteweb.accesscontrol.dto.DoorControlDTO">
        SELECT * FROM (
        SELECT activeControl.EquipmentId,
        activeControl.EquipmentName,
        activeControl.ControlName,
        activeControl.ControlResult,
        activeControl.ControlExecuterId,
        activeControl.SerialNo,
        activeControl.ParameterValues,
        activeControl.StartTime,
        activeControl.EndTime,
        activeControl.ConfirmTime
        FROM TBL_ActiveControl activeControl
        INNER JOIN TBL_Equipment equipment on activeControl.EquipmentId = equipment.EquipmentId
        INNER JOIN TBL_Control control ON equipment.EquipmentTemplateId = control.EquipmentTemplateId AND
        activeControl.ControlId = control.ControlId
        WHERE equipment.EquipmentCategory IN (82, 97, 98) AND activeControl.StartTime &gt;= #{startTime} AND
        activeControl.StartTime &lt;= #{endTime}
        AND control.ControlCategory IN (12,13,14,31,32,33,34,35,36,40,41)
        <if test="equipmentId != null">
            AND activeControl.equipmentId = #{equipmentId}
        </if>
        <if test="controlResultType != null">
            AND activeControl.ControlResultType = #{controlResultType}
        </if>
        <if test="userId != null">
            AND activeControl.ControlExecuterId = #{userId}
        </if>
        AND equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND activeControl.controlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        UNION ALL
        SELECT historyControl.EquipmentId,
        historyControl.EquipmentName,
        historyControl.ControlName,
        historyControl.ControlResult,
        historyControl.ControlExecuterId,
        historyControl.SerialNo,
        historyControl.ParameterValues,
        historyControl.StartTime,
        historyControl.EndTime,
        historyControl.ConfirmTime
        FROM TBL_HistoryControl historyControl
        INNER JOIN TBL_Equipment equipment on historyControl.EquipmentId = equipment.EquipmentId
        INNER JOIN TBL_Control control ON equipment.EquipmentTemplateId = control.EquipmentTemplateId AND
        historyControl.ControlId = control.ControlId
        WHERE equipment.EquipmentCategory IN (82, 97, 98) AND historyControl.StartTime &gt;= #{startTime} AND
        historyControl.StartTime &lt;= #{endTime}
        AND control.ControlCategory IN (12,13,14,31,33,35,36)
        <if test="equipmentId != null">
            AND historyControl.equipmentId = #{equipmentId}
        </if>
        <if test="controlResultType != null">
            AND historyControl.ControlResultType = #{controlResultType}
        </if>
        <if test="userId != null">
            AND historyControl.ControlExecuterId = #{userId}
        </if>
        AND equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND historyControl.controlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        ) result
        order by result.SerialNo desc
    </select>
    <select id="findControlQueueOfDoor" resultType="com.siteweb.accesscontrol.dto.DoorControlQueueDTO">
        SELECT
        a.id,
        a.EquipmentId,
        CASE WHEN b.EquipmentCategory IN (97, 98) THEN f.EquipmentName ELSE b.EquipmentName END equipmentName,
        a.ControlId,
        i.ControlName,
        y.EmployeeName as userId,
        a.ParameterValues,
        a.LastUpdate,
        CASE WHEN b.EquipmentCategory IN (97, 98) THEN h.AreaName ELSE d.AreaName END areaName
        FROM tbl_activecontrolofdoor a
        INNER JOIN TBL_Equipment b ON a.EquipmentId = b.EquipmentId
        LEFT JOIN tbl_doorareamap c ON b.EquipmentId = c.equipmentid
        LEFT JOIN tbl_doorarea d ON c.areaid = d.areaid
        INNER JOIN TBL_Equipment e ON b.EquipmentId = e.EquipmentId
        LEFT JOIN TBL_Equipment f ON b.ParentEquipmentId = f.EquipmentId
        LEFT JOIN tbl_doorareamap g ON b.ParentEquipmentId = g.equipmentId
        LEFT JOIN tbl_doorarea h ON h.areaId = g.areaId
        INNER JOIN tbl_control i ON b.equipmenttemplateid = i.equipmenttemplateid AND a.controlid = i.controlid
        LEFT JOIN tbl_employee y ON y.EmployeeId = a.UserId
        WHERE b.EquipmentCategory IN (82, 97, 98)
        AND b.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND a.UserId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        <if test="equipmentId != null">
            AND b.EquipmentId = #{equipmentId}
        </if>
        <if test="areaId != null and areaId != -1">
            AND d.areaId = #{areaId}
        </if>
        <if test="areaId != null and areaId == -1">
            AND c.areaid IS NULL
        </if>
        <if test="userId != null">
            AND a.userId = #{userId}
        </if>
        ORDER BY a.lastupdate DESC
    </select>
    <select id="findControlActiveOfDoor" resultType="com.siteweb.accesscontrol.dto.DoorControlDTO">
        SELECT a.EquipmentId,
        a.EquipmentName,
        CASE WHEN b.EquipmentCategory IN (97, 98) THEN g.AreaName ELSE d.AreaName END areaName,
        a.ControlName,
        a.ControlResult,
        h.EmployeeName as ControlExecuterId,
        a.SerialNo,
        a.ParameterValues,
        a.StartTime,
        a.EndTime,
        a.ConfirmTime
        FROM TBL_ActiveControl a INNER JOIN
        TBL_Equipment b ON a.EquipmentId = b.EquipmentId
        LEFT JOIN tbl_doorareamap c ON b.EquipmentId = c.equipmentid
        LEFT JOIN tbl_doorarea d ON c.areaid = d.areaid
        LEFT JOIN tbl_doorareamap f ON b.ParentEquipmentId = f.equipmentId
        LEFT JOIN tbl_doorarea g ON g.areaId = f.areaId
        LEFT JOIN tbl_employee h on h.EmployeeId = a.ControlExecuterId
        WHERE b.EquipmentCategory IN (82, 97, 98)
        AND b.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND a.ControlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        <if test="equipmentId != null">
            AND b.EquipmentId = #{equipmentId}
        </if>
        <if test="areaId != null and areaId != -1">
            AND d.areaId = #{areaId}
        </if>
        <if test="areaId != null and areaId == -1">
            AND c.areaid IS NULL
        </if>
        <if test="userId != null">
            AND a.ControlExecuterId = #{userId}
        </if>
        <if test="controlResultType != null">
            AND a.ControlResultType = #{controlResultType}
        </if>
        ORDER BY a.StartTime DESC
    </select>
    <select id="findControlHistoryOfDoor" resultType="com.siteweb.accesscontrol.dto.DoorControlDTO">
        SELECT a.EquipmentId,
        a.equipmentName,
        CASE WHEN b.EquipmentCategory IN (97, 98) THEN g.AreaName ELSE d.AreaName END areaName,
        a.ControlName,
        a.ControlResult,
        h.EmployeeName as ControlExecuterId,
        a.SerialNo,
        a.ParameterValues,
        a.StartTime,
        a.EndTime,
        a.ConfirmTime
        FROM TBL_HistoryControl a INNER JOIN
        TBL_Equipment b ON a.EquipmentId = b.EquipmentId
        LEFT JOIN tbl_doorareamap c ON b.EquipmentId = c.equipmentid
        LEFT JOIN tbl_doorarea d ON c.areaid = d.areaid
        LEFT JOIN tbl_doorareamap f ON b.ParentEquipmentId = f.equipmentId
        LEFT JOIN tbl_doorarea g ON g.areaId = f.areaId
        LEFT JOIN tbl_employee h on h.EmployeeId = a.ControlExecuterId
        WHERE b.EquipmentCategory IN (82, 97, 98) AND a.StartTime &gt;= #{startTime} AND a.StartTime &lt;= #{endTime}
        AND b.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND a.ControlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        <if test="equipmentId != null">
            AND b.EquipmentId = #{equipmentId}
        </if>
        <if test="areaId != null and areaId != -1">
            AND d.areaId = #{areaId}
        </if>
        <if test="areaId != null and areaId == -1">
            AND c.areaid IS NULL
        </if>
        <if test="userId != null">
            AND a.ControlExecuterId = #{userId}
        </if>
        <if test="controlResultType != null">
            AND a.ControlResultType = #{controlResultType}
        </if>
        ORDER BY a.StartTime DESC
    </select>
    <select id="getActiveQueueCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM TBL_ActiveControl a INNER JOIN TBL_Equipment b ON a.EquipmentId = b.EquipmentId WHERE
        b.EquipmentCategory IN (82,97,98)
        AND b.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        AND a.ControlExecuterId IN
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
    </select>
</mapper>