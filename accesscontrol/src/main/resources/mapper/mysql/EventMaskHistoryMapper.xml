<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EventMaskHistoryMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_eventmaskhistory (
        SequenceId,
        StationId,
        EquipmentId,
        EventId,
        EventConditionId,
        EventValue,
        Meanings,
        BaseTypeId,
        StartTime,
        EndTime
        ) VALUES
        <foreach collection="eventMaskHistoryInsertList" item="item" index="index" separator=",">
            (
            #{item.sequenceId},
            #{item.stationId},
            #{item.equipmentId},
            #{item.eventId},
            #{item.eventConditionId},
            #{item.eventValue},
            #{item.meanings},
            #{item.baseTypeId},
            #{item.startTime},
            #{item.endTime}
            )
        </foreach>
    </insert>
</mapper>