<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorAlarmMapper">
    <select id="findDoorAlarm" resultType="com.siteweb.accesscontrol.dto.DoorAlarmDTO">
        SELECT *
        FROM (SELECT a.EquipmentId,
                     a.EquipmentName,
                     a.EventName,
                     a.EventSeverity,
                     a.StartTime,
                     a.EndTime,
                     a.ConfirmTime
              FROM TBL_ActiveEvent a INNER JOIN tbl_equipment b ON a.EquipmentId = b.EquipmentId
              WHERE b.EquipmentCategory = 82
                AND b.EquipmentId = #{equipmentId}
                AND a.StartTime &gt;= #{startTime}
                AND a.StartTime &lt;= #{endTime}
              UNION ALL
              SELECT a.EquipmentId,
                     a.EquipmentName,
                     a.EventName,
                     a.EventSeverity,
                     a.StartTime,
                     a.EndTime,
                     a.ConfirmTime
              FROM TBL_HistoryEvent a INNER JOIN tbl_equipment b ON a.EquipmentId = b.EquipmentId
              WHERE b.EquipmentCategory = 82
                AND b.EquipmentId = #{equipmentId}
                AND a.StartTime &gt;= #{startTime}
                AND a.StartTime &lt;= #{endTime}) result
        ORDER BY result.startTime DESC
    </select>
</mapper>