<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorCardMapper">
    <select id="findDoorCardInfoByCardIds" resultType="com.siteweb.accesscontrol.dto.DoorCardInfoDTO">
        SELECT a.TimeGroupId, a.doorId, b.EquipmentId,a.CardId
        FROM tbl_doorcard a
        INNER JOIN tbl_door b ON a.DoorId = b.DoorId
        WHERE a.CardId IN
        <foreach collection="cardIds" item="cardId" open="(" close=")" separator=",">
            #{cardId}
        </foreach>
    </select>
    <select id="findEquipmentIdByCardId" resultType="java.lang.Integer">
        SELECT door.EquipmentId
        FROM tbl_doorcard doorCard
                 INNER JOIN tbl_door door ON door.DoorId = doorCard.DoorId
        WHERE doorCard.CardId = #{cardId}
    </select>
</mapper>