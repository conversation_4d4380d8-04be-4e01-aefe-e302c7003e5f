<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.FingerprintMapper">
    <delete id="deleteByCardId">
        DELETE
            a
        FROM TBL_Fingerprint a INNER JOIN TBL_FingerPrintCardMap b on a.FingerPrintId = b.FingerPrintId
        WHERE b.CardId = #{cardId}
    </delete>
    <select id="findCardFingerprintByVendor" resultType="com.siteweb.accesscontrol.entity.FingerPrint">
        SELECT b.FingerprintId, b.FingerprintNo, b.FingerprintData
        FROM TBL_FingerprintCardMap a
                 INNER JOIN TBL_Fingerprint b ON a.FingerprintId = b.FingerprintId
        WHERE a.CardId = #{cardId} AND a.Vendor = #{vendorId}
    </select>
    <select id="findChildEquipment" resultType="com.siteweb.accesscontrol.dto.ChildEquipment">
        SELECT a.StationId, a.EquipmentId, b.ItemId vendorId
        FROM TBL_Equipment a
                 INNER JOIN TBL_DataItem b ON a.Vendor = b.ItemValue AND b.EntryId = 14
                 INNER JOIN TBL_Equipment c ON a.ParentEquipmentId = c.EquipmentId
        WHERE a.ParentEquipmentId = #{equipmentId}
          AND a.EquipmentCategory = #{equipmentCategory}
          AND b.ItemId = #{vendorId}
    </select>
    <select id="findFingerPrintCategoryByEquipmentId" resultType="java.lang.Integer">
        SELECT Expression
        FROM tbl_signal
        WHERE EquipmentTemplateId = (SELECT EquipmentTemplateId FROM tbl_equipment WHERE EquipmentId = #{equipmentId})
          AND ChannelNo = 39
    </select>
    <select id="findFingerprintData" resultType="com.siteweb.accesscontrol.entity.FingerPrint">
        SELECT c.FingerPrintId, c.FingerPrintNO, c.FingerPrintData
        FROM TBL_Card a
                 INNER JOIN TBL_FingerPrintCardMap b ON a.CardId = b.CardId AND IFNULL(b.Vendor, 180) = #{vendor}
                 INNER JOIN TBL_Fingerprint c ON b.FingerprintId = c.FingerprintId
        WHERE a.CardId = #{cardId}
    </select>
    <select id="findFingerprintIdByCardId" resultType="java.lang.Integer">
        SELECT FingerprintId
        FROM TBL_FingerPrintCardMap t
        WHERE t.CardId = #{cardId}
          AND t.Vendor = #{vendor}
    </select>
    <select id="findCardFingerprintIdByVendor" resultType="java.lang.Integer">
        SELECT b.FingerprintId
        FROM TBL_Card a
                 LEFT JOIN TBL_FingerprintCardMap b ON a.CardId = b.CardId
        WHERE a.CardId = #{cardId}
          AND b.Vendor = #{vendorId}
    </select>
</mapper>