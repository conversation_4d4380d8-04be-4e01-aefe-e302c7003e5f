<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorAreaMapper">
    <select id="findDoorAreaEquipment" resultType="com.siteweb.accesscontrol.dto.DoorAreaEquipmentDTO">
        SELECT e.areaId, a.equipmentid, a.equipmentName, d.category,a.Vendor
        FROM TBL_Equipment a
        INNER JOIN TBL_EquipmentTemplate c ON a.EquipmentTemplateId = c.EquipmentTemplateId
        INNER JOIN TBL_Door d ON a.EquipmentId = d.EquipmentId
        LEFT JOIN TBL_DoorAreaMap e ON d.EquipmentId = e.EquipmentId
        WHERE c.EquipmentBaseType = 1001 AND a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
</mapper>