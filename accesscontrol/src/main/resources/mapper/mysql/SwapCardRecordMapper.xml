<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.SwapCardRecordMapper">
    <select id="findLastRecordByEquipmentId" resultType="com.siteweb.accesscontrol.entity.SwapCardRecordDTO">
        SELECT a.CardCode,
               a.CardUserId,
               d.Meaning enter,
               a.Valid,
               a.ValidName,
               a.RecordTime
        FROM TBL_SwapCardRecord a
                 INNER JOIN TBL_Equipment b ON a.EquipmentId = b.EquipmentId
                 INNER JOIN TBL_Door c ON b.EquipmentId = c.EquipmentId
                 LEFT JOIN tbl_doorpropertymeaning d
                           ON c.Category = d.category AND a.Enter = d.PropertyId AND d.propertyType = 4
        WHERE a.EquipmentId = #{equipmentId}
        ORDER BY a.RecordTime DESC
        LIMIT 1
    </select>
    <select id="findSwapCardRecordPage" resultType="com.siteweb.accesscontrol.entity.SwapCardRecordDTO">
        SELECT swapCard.EquipmentId,
        swapCard.EquipmentName,
        swapCard.CardCode,
        swapCard.CardName,
        swapCard.CardUserId,
        swapCard.CardGroupName,
        swapCard.CardStatusName,
        swapCard.DoorNo,
        swapCard.Valid,
        swapCard.ValidName,
        meaning.Meaning enter,
        swapCard.RecordTime,
        area.AreaName
        FROM TBL_Equipment equipment
        INNER JOIN TBL_Door door ON equipment.EquipmentId = door.EquipmentId
        LEFT JOIN tbl_doorareamap areaMap ON equipment.EquipmentId = areaMap.equipmentid
        LEFT JOIN tbl_doorarea area ON areaMap.areaid = area.areaid
        INNER JOIN TBL_SwapCardRecord swapCard on swapCard.EquipmentId = equipment.EquipmentId
        LEFT JOIN tbl_doorpropertymeaning meaning ON door.Category = meaning.Category AND swapCard.Enter = meaning.PropertyId AND meaning.propertytype = 4
        WHERE equipment.EquipmentCategory = 82 AND swapCard.RecordTime &gt;= #{startTime} AND swapCard.RecordTime &lt;=#{endTime}
        <if test="equipmentId != null">
            AND equipment.equipmentId = #{equipmentId}
        </if>
        <if test="areaId != null">
            AND areaMap.areaId = #{areaId}
        </if>
        <if test="userId != null">
            AND swapCard.cardUserId = #{userId}
        </if>
        order by swapCard.RecordTime DESC
    </select>
</mapper>