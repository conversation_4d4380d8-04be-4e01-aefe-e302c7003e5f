<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.FaceDataMapMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_facedatamap(cardid, faceid, lastupdater, lastupdatetime) VALUES
        <foreach collection="faceDataMapList" item="item" separator=",">
            (#{item.cardId},#{item.faceId},#{item.lastUpdater},#{item.lastUpdateTime})
        </foreach>
    </insert>
    <select id="findFaceIdByCardId" resultType="java.lang.Integer">
        SELECT b.FaceId
        FROM TBL_Card a
                 LEFT JOIN TBL_FaceDataMap b ON a.CardId = b.CardId
        WHERE a.CardId = #{cardId}
    </select>
</mapper>