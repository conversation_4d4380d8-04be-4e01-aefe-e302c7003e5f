<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.FingerprintAuthMapper">
    <insert id="saveAuthFingerPrint">
        INSERT INTO tbl_fingerprintauth(tbl_fingerprintauth.timegroupid, tbl_fingerprintauth.doorid,
        tbl_fingerprintauth.cardid, tbl_fingerprintauth.fingerprintid,
        tbl_fingerprintauth.lastupdatetime)
        VALUES
        <foreach collection="fingerprintAuthList" item="fingerprintAuth" separator=",">
            (#{fingerprintAuth.timeGroupId},#{fingerprintAuth.doorId},
            #{fingerprintAuth.cardId},#{fingerprintAuth.fingerPrintId},
            #{fingerprintAuth.lastUpdateTime})
        </foreach>
    </insert>
    <update id="updateLastUpdateAndFingerPrintId">
        UPDATE TBL_DoorCard a
            INNER JOIN TBL_Door b ON a.DoorId = b.DoorId
            INNER JOIN TBL_Equipment c ON b.StationId = c.StationId AND b.EquipmentId = c.EquipmentId
            INNER JOIN TBL_FingerPrintAuth d ON d.TimeGroupId = a.TimeGroupId AND d.DoorId = b.DoorId AND a.CardId = #{cardId}
        SET d.LastUpdateTime = sysdate, d.FingerprintId  = #{fingerPrintId}
        WHERE c.StationId = #{stationId} AND c.EquipmentId = #{equipmentId} AND a.CardId = #{cardId}
    </update>
    <delete id="deleteByCardId">
        DELETE
        FROM TBL_FingerprintAuth
        WHERE CardId = #{cardId}
    </delete>
    <delete id="deleteFingerprintAuth">
        DELETE
        FROM TBL_FingerprintAuth a
        WHERE EXISTS (SELECT 1
                      FROM TBL_Door b
                               JOIN TBL_Equipment c ON b.StationId = c.StationId AND b.EquipmentId = c.EquipmentId
                      WHERE a.DoorId = b.DoorId
                        AND b.StationId = #{stationId}
                        AND b.EquipmentId = #{equipmentId}
                        AND a.FingerprintId = #{fingerPrintId}
                        AND a.CardId = #{cardId})
    </delete>
    <select id="cardFingerprintAuthCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM TBL_FingerPrintAuth a
                 INNER JOIN TBL_Door b ON a.DoorId = b.DoorId
                 INNER JOIN TBL_Equipment c ON b.EquipmentId = c.EquipmentId
                 INNER JOIN TBL_FingerPrintCardMap d ON a.CardId = d.CardId AND a.FingerprintId = d.FingerprintId
        WHERE a.CardId = #{cardId}
    </select>
</mapper>