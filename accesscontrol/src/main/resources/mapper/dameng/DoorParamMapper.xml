<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorParamMapper">
    <select id="findDoorParamByEquipmentId" resultType="com.siteweb.accesscontrol.dto.DoorParamItem">
        SELECT a.ParamType,
               a.ParamValue,
               c.Meaning
        FROM TBL_DoorParam a
                 INNER JOIN TBL_Door b ON a.DoorId = b.DoorId
                 LEFT JOIN TBL_DoorPropertyMeaning c
                           ON b.Category = c.Category AND a.paramType = c.propertyType AND a.paramValue = c.propertyId
        WHERE b.EquipmentId = #{equipmentId}
    </select>
</mapper>