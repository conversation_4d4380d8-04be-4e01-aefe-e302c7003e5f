<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.TimeGroupMapper">
    <select id="findAll" resultType="com.siteweb.accesscontrol.dto.TimeGroupDTO">
        SELECT timeGroupId,
               timeGroupName
        FROM TBL_TimeGroup
        WHERE TimeGroupType &gt;= 10
        ORDER BY TimeGroupId;
    </select>
    <select id="findByEquipmentIds" resultType="com.siteweb.accesscontrol.entity.TimeGroup">
        SELECT c.TimeGroupId,
        c.TimeGroupCategory,
        c.TimeGroupName,
        b.TimeGroupType,
        c.TimeGroupException,
        c.StartTime,
        c.EndTime,
        c.LastUpdateDate
        FROM TBL_Door a
        INNER JOIN TBL_DoorTimeGroup b ON a.DoorId = b.DoorId
        INNER JOIN TBL_TimeGroup c ON b.TimeGroupId = c.TimeGroupId
        WHERE a.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        ORDER BY b.TimeGroupType
    </select>
</mapper>