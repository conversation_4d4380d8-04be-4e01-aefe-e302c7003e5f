<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorPropertyMeaningMapper">
    <select id="findDoorParamMeaning" resultType="com.siteweb.accesscontrol.dto.IdNameDTO">
        SELECT c.propertyid AS id,
               c.meaning    AS name
        FROM TBL_Door a
                 INNER JOIN TBL_DoorProperty b ON
            a.Category = b.Category
                 INNER JOIN tbl_doorpropertymeaning c ON
            c.propertytype = b.propertytype AND c.category = a.Category
        WHERE a.EquipmentId = #{equipmentId}
          AND b.propertytype = #{propertyType}
        ORDER BY propertyid
    </select>
</mapper>