<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorTimeGroupMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_doortimegroup(doorid, timegroupid, timegrouptype) VALUES
        <foreach collection="doorTimeGroupList" item="item" separator=",">
            (#{item.doorId},#{item.timeGroupId},#{item.timeGroupType})
        </foreach>
    </insert>
    <select id="findDoorTimeGroup" resultType="com.siteweb.accesscontrol.dto.EquipmentTimeGroupDTO">
        SELECT b.EquipmentId,
               a.TimeGroupId
        FROM tbl_doortimegroup a
                 INNER JOIN tbl_door b ON a.DoorId = b.DoorId
    </select>
    <select id="findDoorTimeGroupStr" resultType="com.siteweb.accesscontrol.dto.DoorTimeGroupStrDTO">
        SELECT a.TimeGroupType, b.TimeSpanChar, b.Week
        FROM TBL_DoorTimeGroup a
        INNER JOIN TBL_TimeGroupSpan b ON a.TimeGroupId = b.TimeGroupId
        WHERE a.TimeGroupId IN
        <foreach collection="delTimeGroupIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND a.DoorId = #{doorId}
        ORDER BY b.Week
    </select>
    <select id="findTimeGroupNo" resultType="java.lang.Integer">
        SELECT c.TimeGroupType
        FROM TBL_Equipment a
        INNER JOIN TBL_Door b ON a.EquipmentId = b.EquipmentId
        INNER JOIN TBL_DoorTimeGroup c ON b.DoorId = c.DoorId
        WHERE a.EquipmentId = #{equipmentId}
        <if test="timeGroupId != null">
            AND c.TimeGroupId = #{timeGroupId}
        </if>
    </select>
    <select id="findLackDoorTimeGroupType" resultType="java.lang.Integer">
        SELECT a.TimeGroupSetId AS timeGroupType
        FROM TBL_TimeGroupSet a
                 LEFT JOIN TBL_DoorTimeGroup b ON a.TimeGroupSetId = b.TimeGroupType AND b.DoorId = #{doorId}
        WHERE b.TimeGroupType IS NULL
          AND a.TimeGroupSetId &lt; 14
    </select>
    <select id="findDoorTimeGroupCountByEquipmentId" resultType="java.lang.Integer">
        SELECT count(*) count FROM tbl_door door inner JOIN tbl_doortimegroup doorTimeGroup on door.DoorId = doorTimeGroup.DoorId
        WHERE door.EquipmentId = #{equipmentId}
    </select>
</mapper>