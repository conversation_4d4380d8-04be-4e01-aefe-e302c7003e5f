<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorCardLostMapper">
    <insert id="createLostRecord">
        INSERT INTO TBL_DoorCardLost
        SELECT t.CardId, t.TimeGroupId, t.DoorId, t.StartTime, t.EndTime, t.Password
        FROM TBL_DoorCard t
        WHERE t.CardId = #{cardId}
    </insert>
    <select id="findDoorCardLost" resultType="com.siteweb.accesscontrol.dto.DoorCardLostDTO">
        SELECT d.StationId,
               d.EquipmentId,
               d.Password,
               tc.CardId,
               tc.CardCode,
               dt.TimeGroupType,
               dc.EndTime,
               dc.Password AS DoorCardPassword
        FROM TBL_DoorCardLost dc
                 INNER JOIN TBL_Card tc ON dc.CardId = tc.CardId
                 INNER JOIN TBL_DoorTimeGroup dt ON dt.DoorId = dc.DoorId AND dt.TimeGroupId = dc.TimeGroupId
                 INNER JOIN TBL_Door d ON dt.DoorId = d.DoorId
        WHERE tc.CardId = #{cardId};
    </select>
</mapper>