<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.DoorTagMapper">
    <select id="findDoorTagByEquipmentIds" resultType="com.siteweb.accesscontrol.vo.DoorTagVO">
        SELECT c.TagName,c.TagIcon,c.TagColor,a.equipmentId FROM tbl_door a
        INNER JOIN doortagmap b ON a.EquipmentId = b.equipmentId
        INNER JOIN doortag c ON c.TagId = b.TagId
        WHERE a.EquipmentId IN
        <foreach collection="doorEquipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
</mapper>