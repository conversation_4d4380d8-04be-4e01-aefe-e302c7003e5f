<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.PersonMapper">
    <select id="findPersons" resultType="com.siteweb.accesscontrol.dto.PersonDTO">
        SELECT a.EmployeeId AS PersonId, a.EmployeeName AS Name
        FROM tbl_employee a WHERE EmployeeId > 0 AND a.DepartmentId IN
        <foreach collection="departmentIds" item="departmentId" open="(" close=")" separator=",">
            #{departmentId}
        </foreach>
    </select>
</mapper>