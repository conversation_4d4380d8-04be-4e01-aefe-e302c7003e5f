<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.accesscontrol.mapper.CardGroupMapper">
    <select id="findAll" resultType="com.siteweb.accesscontrol.vo.CardGroupVO">
        SELECT item.ItemId as itemId, item.ItemValue as itemValue
        FROM tbl_dataitem item
        WHERE item.EntryId = 75
    </select>
</mapper>