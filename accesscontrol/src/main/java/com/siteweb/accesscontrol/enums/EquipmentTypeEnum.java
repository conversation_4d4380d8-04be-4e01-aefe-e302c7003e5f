package com.siteweb.accesscontrol.enums;

import lombok.Data;
import lombok.Getter;

@Getter
public enum EquipmentTypeEnum {
    FACE_READER(97,"人脸"),
    FINGER_READER(98,"指纹");


    EquipmentTypeEnum(Integer equipmentCategory, String describe) {
        this.equipmentCategory = equipmentCategory;
        this.describe = describe;
    }

    private final Integer equipmentCategory;
    /**
     * 端口名称
     */
    private final String describe;
}
