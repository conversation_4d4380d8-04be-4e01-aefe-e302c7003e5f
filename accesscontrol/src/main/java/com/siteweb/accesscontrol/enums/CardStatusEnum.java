package com.siteweb.accesscontrol.enums;

/**
 * 门禁卡状态 EntryId = 46
 * <AUTHOR>
 * @date 2022/09/16
 */
public enum CardStatusEnum {
    /**
     * 使用中
     */
    USING(1,"使用中"),
    /**
     * 挂失
     */
    LOST(2,"挂失"),
    /**
     * 注销
     */
    CANCEL(3,"注销");

    CardStatusEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }

    private final Integer status;
    private final String describe;

    public Integer getStatus() {
        return status;
    }
}
