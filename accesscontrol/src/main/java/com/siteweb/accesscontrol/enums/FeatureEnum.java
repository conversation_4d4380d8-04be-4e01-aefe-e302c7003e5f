package com.siteweb.accesscontrol.enums;

public enum FeatureEnum {
    CARD(1,"卡"),
    FINGERPRINT(2,"指纹"),
    FACE(4, "人脸");


    FeatureEnum(Integer type, String describe) {
        this.type = type;
        this.describe = describe;
    }

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String describe;

    public Integer getType() {
        return type;
    }
}
