package com.siteweb.accesscontrol.enums;

public enum CommandCategoryEnum {
    ADD_MANUAL_DOOR_CARD(12, "增加门禁卡"),
    DEL_MANUAL_DOOR_CARD(13, "删除门禁卡"),
    EDIT_MANUAL_DOOR_CARD(14, "修改门禁卡设置"),
    SET_TIME_GROUP(15, "设置星期准进时间段"),
    EDIT_DOOR_PASSWORD(16, "修改验证控制密码"),
    DEL_ALL_MANUAL_DOOR_CARD(17, "删除所有门禁卡"),
    BU_FANG_HONG_WAI(18, "布防红外"),
    CHE_FANG_HONG_WAI(19, "撤防红外"),
    OPEN_DELAY(20, "开门超时时间"),
    MANUAL_DOOR_WORK_MODE(21, "刷卡进门密码工作方式"),
    SET_TIME(22, "设置时间"),
    ADD_MULTIPLE_CARDS(29, "添加一人多卡"),
    DELETE_MULTIPLE_CARDS(30, "删除用户名下一人多卡"),
    ADD_OR_UPDATE_FINGERPRINT(31, "添加或修改指纹"),
    DELETE_FINGERPRINT(32, "删除指纹（海康）"),
    ADD_OR_UPDATE_FACE(33, "添加或修改人脸"),
    DELETE_FACE(34, "删除人脸"),
    ADD_OR_UPDATE_USER(35, "添加或修改用户"),
    DELETE_USER(36, "删除用户"),
    SAMPLE_FACE(37, "采集人脸"),
    SAMPLE_FINGERPRINT(38, "采集指纹"),
    SET_DOOR_OPEN_MODE(39, "设置读卡器默认验证方式（海康门开方式）"),
    SET_FINGERPRINT(40, "指纹读头设置指纹"),
    CLEAR_FINGERPRINT(41, "指纹读头删除指纹（纽贝尔）"),
    SET_DOOR_NORMALLY_OPEN(42, "设置门常开"),
    SET_DOOR_NORMALLY_CLOSE(43, "设置门常闭"),
    OPEN_DOOR(2, "开关门控制(远程开门)"),
    CLOSE_DOOR(44, "远程关门"),
    SET_FIRE_SIGNAL(45, "火警信号"),
    SET_LOCK_ERROR_COUNT(46, "卡封锁错误次数"),
    SET_LOCK_TIME(47, "卡封锁时间"),
    SET_SLOT_INTERVAL(48, "非法卡刷卡间隔"),
    SET_KEEP_TIME(49, "门开保持时间"),
    SET_OPEN_MODE(50, "设置开门模式(腾讯HTTP)");

    CommandCategoryEnum(Integer category, String describe) {
        this.category = category;
        this.describe = describe;
    }

    private Integer category;
    private String describe;

    public Integer getCategory() {
        return category;
    }

    public String getDescribe() {
        return describe;
    }
}
