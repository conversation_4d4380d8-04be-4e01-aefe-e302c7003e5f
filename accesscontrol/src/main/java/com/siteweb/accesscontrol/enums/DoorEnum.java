package com.siteweb.accesscontrol.enums;

public enum DoorEnum {
    CONTROL_RESULT_TYPE(30, "控制命令结果类型"),
    COMMAND_CATEGORY(31, "控制命令种类"),
    EMPLOYEE_TYPE(41, "人员分类"),
    EMPLOYEE_TITLE(42, "人员职称"),
    GENDER(84, "性别"),
    CARD_STATUS(46, "卡状态"),
    CARD_CATEGORY(47, "卡分类"),
    DOOR_INFRARED(74, "门禁红外设置类型"),
    CARD_GROUP(75, "卡分组"),
    CARD_TYPE(2003, "卡类型"),
    FINGER_READER_MULTI_OPEN_DOOR_TYPE(2004, "指纹读头多种开门方式"),
    FINGER_READER_DOOR_INOUT_FLAG(2005, "指纹读头进出门标志"),
    SERIAL_DEVICE_TYPE(2006, "指纹采集串口服务器类型"),
    EMERSON_ISU_SERIAL_PORT(2007, "艾默生ISU串口服务器串口号"),
    EMERSON_IDU_SERIAL_PORT(2008, "艾默生IDU串口服务器串口号"),
    EMERSONE_ESTONE_SERIAL_PORT(2009, "艾默生eStone串口服务器串口号"),
    NEWABEL_806D4M8_D2M8_SERIAL_PORT(2010, "纽贝尔806D4M3/D2M3串口服务器串口号"),
    HK_DOOR_OPEN_MODE(2013, "海康门禁门开方式(门禁类型11)"),
    TNB_OPEN_MODE(158, "腾讯纽贝尔门禁门开方式(门禁类型12)"),
    TNB_FIRE_SIGNAL(159, "腾讯纽贝尔门禁火警信号(门禁类型12)");

    DoorEnum(Integer entryId, String describe) {
        this.entryId = entryId;
        this.describe = describe;
    }

    private final Integer entryId;
    /**
     * 端口名称
     */
    private final String describe;

    public Integer getEntryId() {
        return entryId;
    }

    public String getDescribe() {
        return describe;
    }
}
