package com.siteweb.accesscontrol.enums;

public enum DoorPropertyEnum {
    OPEN_MODE(1,"开门方式"),
    FIRE_SIGNAL(2,"门禁火警信号"),
    SWIPE_OR_OPEN_STATE(3,"刷卡或开门状态"),
    ENTRY_OR_EXIT_SIGN(4,"进出门标志"),
    KEEP_TIME(5,"门开保持时间"),
    OPEN_DELAY(6,"门开超时时间"),
    LOCK_ERROR_COUNT(7,"卡封锁错误次数"),
    LOCK_TIME(8,"卡封锁时间"),
    SLOT_INTERVAL(9,"非法卡刷卡间隔");


    DoorPropertyEnum(Integer propertyId, String describe) {
        this.propertyId = propertyId;
        this.describe = describe;
    }

    private final Integer propertyId;
    /**
     * 端口名称
     */
    private final String describe;
}
