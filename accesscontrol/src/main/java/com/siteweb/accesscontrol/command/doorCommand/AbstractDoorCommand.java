package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.service.*;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import com.siteweb.monitoring.mamager.ActiveControlManager;
import com.siteweb.monitoring.service.ControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 抽象的门命令
 * <AUTHOR>
 * @date 2022/09/16
 */
@Slf4j
@Component
public abstract class AbstractDoorCommand {
    /**
     * 默认卡密码
     */
    protected static final String CARD_DEFAULT_PASSWORD = "1111";
    /**
     * 默认门密码
     */
    protected static final String DOOR_DEFAULT_PASSWORD = "000000";
    /**
     * 指纹机默认密码
     */
    protected static final String FINGERPRINT_DEFAULT_PASSWORD = "8888888888";
    @Autowired
    DoorService doorService;
    @Autowired
    ControlService controlService;
    @Autowired
    ActiveControlManager activeControlManager;
    @Autowired
    DoorTimeGroupService doorTimeGroupService;
    @Autowired
    CardService cardService;
    @Autowired
    FingerprintService fingerprintService;
    @Autowired
    FingerprintAuthService fingerprintAuthService;

    /**
     * 获取卡密码
     * @return {@link String}
     */
    protected final String getCardPassword(String cardPassword){
        return CharSequenceUtil.isBlank(cardPassword) ? CARD_DEFAULT_PASSWORD : cardPassword;
    }

    /**
     * 获取门密码
     * @return {@link String}
     */
    protected final String getDoorPassword(String doorPassword){
        return CharSequenceUtil.isBlank(doorPassword) ? DOOR_DEFAULT_PASSWORD : doorPassword;
    }

    /**
     * 部分门需要用户编号为8位，不足则向前补0
     * @param cardId 卡id
     * @return {@link String}
     */
    protected String getUserNo(Integer cardId){
        return CharSequenceUtil.padPre(Integer.toString(cardId), 8, '0');
    }

    /**
     * 获取时间组号
     *
     * @return {@link Integer}
     */
    protected final Integer getTimeGroupNo(Integer timeGroupNo,Integer doorNo){
        return  (timeGroupNo - 10) + 4 * (doorNo - 1);
    }

    /**
     * 2022-09-11 -> 20220911
     * 默认的日期格式化其
     * 大部分门卡都仅支持 yyyyMMdd 格式日期
     * @param date 时间
     * @return {@link String} 格式化好后的yyyyMMdd日期
     */
    protected final String defaultTimeFormat(Date date){
        return DateUtil.format(date, DatePattern.PURE_DATE_PATTERN);
    }


    /**
     * 发送简单门命令
     * 如开关门等无需额外参数信息的控制
     * @param userId      用户id
     * @param equipmentId 设备id
     * @param commandCategory  命令类型
     * @param command     命令
     * @return {@link Boolean}
     */
    protected final Boolean sendSimpleDoorCommand(Integer userId, Integer equipmentId, CommandCategoryEnum commandCategory, String command) {
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("{}: cannot find equipment by equipmentId:{}", commandCategory.getDescribe(), equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = this.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, commandCategory.getCategory());
        log.info("{}: StationId:{},EquipmentId:{}，CommandId:{}，CommandString:{}", commandCategory, stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, doorPassword + "," + command, userId);
        return Boolean.TRUE;
    }

    /**
     *
     *
     *
     *
     * @param doorCardCodeType
     * @param cardCodeType
     * @param cardCode
     * @param cardCodeConv
     * @return {@link String}
     */
    protected final String getCardCode(Integer doorCardCodeType, Integer cardCodeType, String cardCode, String cardCodeConv) {
        //门与卡的类型是否一致，一致则直接取卡号，否则取卡号转换,(卡号转换主要用于容错)
        return ObjectUtil.equals(doorCardCodeType, cardCodeType) ? cardCode : cardCodeConv;
    }

    protected final Integer getCardId(Integer cardId){
        return cardId % 10000;
    }

    /**
     * 删除门禁人脸
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId      卡id
     * @return {@link Boolean}
     */
    public Boolean deleteAuthFace(Integer userId, Integer equipmentId, Integer cardId){
        throw new UnsupportedOperationException("equipment Unsupported deleteAuthFace" + equipmentId);
    }

    /**
     * 删除门禁指纹
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId      卡id
     * @return {@link Boolean}
     */
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        throw new UnsupportedOperationException("equipment Unsupported deleteAuthFingerprint" + equipmentId);
    }

    /**
     * 按门删除门禁卡，（删除该门所有时间组下改卡授权）
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId      卡id
     * @return {@link Boolean}
     */
    public abstract Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId);


    /**
     * 编辑门禁卡信息
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @param timeGroupNo 时间组
     * @param cardId      卡id
     * @param validTime   有效时间
     * @return boolean
     */
    public Boolean editManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId, Date validTime) {
        throw new UnsupportedOperationException("equipment Unsupported editManualDoorCard" + equipmentId);
    }

    /**
     * 按门和准进时间组增加门禁卡-12,(删除门下某个时间组授权用这个）
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param timeGroupNo 校准时间组类型
     * @param cardId 卡id
     * @return {@link Boolean}
     */
    public abstract Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId);

    /**
     * 新增/修改门禁人脸
     * @param userId
     * @param equipmentId
     * @param cardId
     * @return boolean
     */
    public Boolean addOrUpdateAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        throw new UnsupportedOperationException("equipment Unsupported addOrUpdateAuthFace" + equipmentId);
    }

    /**
     * 新增/远程采集人脸
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡id
     * @return boolean
     */
    public Boolean remoteCollectFace(Integer userId, Integer equipmentId, Integer cardId) {
        throw new UnsupportedOperationException("equipment Unsupported remoteCollectFace" + equipmentId);
    }

    /**
     * 新增/修改门禁指纹
     * @param userId
     * @param equipmentId
     * @param cardId
     * @return {@link Boolean}
     */
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        throw new UnsupportedOperationException("equipment Unsupported addOrUpdateAuthFingerprint" + equipmentId);
    }

    /**
     * 新增/增加指纹仪用户
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡id
     * @return {@link Boolean}
     */
    public Boolean addFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
        throw new UnsupportedOperationException("equipment Unsupported addFingerReaderUser" + equipmentId);
    }

    /**
     * 设置门延迟
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param openDelay 延迟时间
     * @return {@link Boolean}
     */
    public Boolean setDoorOpenDelay(Integer userId, Integer equipmentId, Integer openDelay) {
        throw new UnsupportedOperationException("equipment Unsupported setDoorOpenDelay" + equipmentId);
    }

    /**
     * 设置门密码
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param newPassword 新门密码
     * @return {@link Boolean}
     */
    public Boolean setDoorPassword(Integer userId, Integer equipmentId, String newPassword) {
        //修改验证控制密码,即修改门密码
        //000000,1,666666    原门密码,门编号,新门密码； 本例：设置门密码为666666
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("EditDoorPassword: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.EDIT_DOOR_PASSWORD.getCategory());
        if (ObjectUtil.isNull(commandId)) {
            log.error("EditDoorPassword: CommandId is null,StationId:{},EquipmentId:{}", stationId, equipmentId);
            return Boolean.FALSE;
        }
        String oldPassword = this.getDoorPassword(equipmentDoor.getPassword());
        Integer doorNo = equipmentDoor.getDoorNo();
        String command = String.format("%s,%s,%s", oldPassword, doorNo, newPassword);
        log.info("EditDoorPassword: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    /**
     * 设置准进时间组
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param addTimeGroupIdList 添加的时间组
     * @param delTimeGroupIdList 删除的时间组
     * @return {@link Boolean}
     */
    public Boolean setDoorTimeGroup(Integer userId, Integer equipmentId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList) {
        //TimeSpanStrList 参数说明
        //1：字符串数组，一次可以传入一个门的多个准进时段数据
        //2：List中每笔字符串记录 准进时段组号，星期，准进字符串（原始数据库格式 00:00-23:5900:00-00:0000:00-00:00 ）
        //设置星期准进时间段
        //控制命令种类15,命令号42
        //000000,1,0,33E4B05786C20000000000000000000000001	门密码,门编号,时间组号,一天的时间组信息(最后一位表示星期几)；
        //本例：设置时间组0的星期一的第一时间段：08:30~12:00 第二时间段：14:00~17:30 第三时间段：00:00~00:00
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("SetTimeGroup: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer doorId = equipmentDoor.getDoorId();
        List<String> timeSpanStrList = doorTimeGroupService.findTimeSpanStrList(doorId, addTimeGroupIdList, delTimeGroupIdList);
        if (CollUtil.isEmpty(timeSpanStrList)) {
            return Boolean.TRUE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = this.getDoorPassword(equipmentDoor.getPassword());
        Integer doorNo = equipmentDoor.getDoorNo();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SET_TIME_GROUP.getCategory());
        if (ObjectUtil.isNull(commandId)) {
            log.error("SetTimeGroup: CommandId is null,StationId:{},EquipmentId:{}", stationId, equipmentId);
            return Boolean.FALSE;
        }
        for (String timeSpanStr : timeSpanStrList) {
            if (timeSpanStr.length() != 36) {
                log.error("Span length error: {},{},{}", stationId, equipmentId, timeSpanStr);
                continue;
            }
            Integer timeGroupNo = Integer.parseInt(CharSequenceUtil.sub(timeSpanStr, 0, 2));
            Integer week = Integer.parseInt(CharSequenceUtil.sub(timeSpanStr, 2, 3));
            String justSpanStr = CharSequenceUtil.sub(timeSpanStr, 3, timeSpanStr.length())
                                        .replace("-", "")
                                        .replace(":", "");
            StringBuilder retStr = new StringBuilder();
            for (int i = 0; i < justSpanStr.length(); i += 4) {
                //每次截取四个字符，将其转换成16进制，不足三位向前补0
                Integer halfSpan = Integer.parseInt(justSpanStr.substring(i, i + 4));
                retStr.append(StrUtil.fill(HexUtil.toHex(halfSpan), '0', 3, true));
            }
            String command = String.format("%s,%s,%s,%s000000000000000000%s", doorPassword, doorNo, getTimeGroupNo(timeGroupNo,doorNo), retStr.toString().toUpperCase(), week);
            activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        }
        return true;
    }

    /**
     * 门禁校准
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param timeStr 校准时间
     * @return {@link Boolean}
     */
    public Boolean setTime(Integer userId, Integer equipmentId, String timeStr) {
        throw new UnsupportedOperationException("equipment Unsupported setTime" + equipmentId);
    }
    /**
     * 设置非法卡刷卡间隔
     *
     * @param userId       用户id
     * @param equipmentId  设备id
     * @param slotInterval 槽间隔
     * @return {@link Boolean}
     */
    public Boolean setSlotInterval(Integer userId, Integer equipmentId, Integer slotInterval) {
        throw new UnsupportedOperationException("equipment Unsupported setSlotInterval" + equipmentId);
    }

    /**
     *  设置卡封锁错误次数
     * @param userId         用户id
     * @param equipmentId    设备id
     * @param lockErrorCount 卡封锁次数
     * @return {@link Boolean}
     */
    public Boolean setLockErrorCount(Integer userId, Integer equipmentId, Integer lockErrorCount) {
        throw new UnsupportedOperationException("equipment Unsupported setLockErrorCount" + equipmentId);
    }


    /**
     * 设置卡封锁时间
     * @param userId      用户id
     * @param equipmentId 设备id
     * @param lockTime
     * @return {@link Boolean}
     */
    public Boolean setLockTime(Integer userId, Integer equipmentId, Integer lockTime) {
        throw new UnsupportedOperationException("equipment Unsupported setLockTime" + equipmentId);
    }

    /**
     * 设置开门保持时间
     * @param userId 用户id
     * @param equipmentId  设备id
     * @param keepTime 开门保持时间
     * @return {@link Boolean}
     */
    public Boolean setKeepTime(Integer userId, Integer equipmentId, Integer keepTime) {
        throw new UnsupportedOperationException("equipment Unsupported setKeepTIme" + equipmentId);
    }



    /**
     * 设置门常开
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean setNormallyOpen(Integer userId, Integer equipmentId) {
        throw new UnsupportedOperationException("equipment Unsupported setNormallyOpen" + equipmentId);
    }


    /**
     * 设置门常关
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean setNormallyClose(Integer userId, Integer equipmentId) {
        throw new UnsupportedOperationException("equipment Unsupported setNormallyClose" + equipmentId);
    }

    /**
     * 设置火警信号
     *
     * @param userId       用户id
     * @param equipmentId  设备id
     * @param fireSignal  火警信号
     * @return {@link Boolean}
     */
    public Boolean setFireSignal(Integer userId, Integer equipmentId, Integer fireSignal) {
        throw new UnsupportedOperationException("equipment Unsupported setFireSignal" + equipmentId);
    }

    /**
     * 设置开门方式
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @param openMode    开门方式
     * @return {@link Boolean}
     */
    public Boolean setOpenMode(Integer userId, Integer equipmentId, Integer openMode){
        throw new UnsupportedOperationException("equipment Unsupported setOpenMode" + equipmentId);
    }

    /**
     * 开门
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean open(Integer userId, Integer equipmentId) {
        String command = "1";
        return this.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.OPEN_DOOR, command);
    }

    /**
     * 关门
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean close(Integer userId, Integer equipmentId) {
        String command = "1";
        return this.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.CLOSE_DOOR, command);
    }

    /**
     * 清除门的所有卡片
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean clearCard(Integer userId, Integer equipmentId) {
        throw new UnsupportedOperationException("equipment Unsupported clearCard" + equipmentId);
    }

    /**
     * 清空读头指纹
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    protected Boolean clearFingerReader(Integer userId, Integer equipmentId) {
        throw new UnsupportedOperationException("equipment Unsupported clearFingerReader" + equipmentId);
    }


    /**
     * 清空读头人脸
     * @param userId 用户id
     * @param equipmentId 设备id
     * @return {@link Boolean }
     */
    public Boolean ClearFaceReader(int userId, int equipmentId) {
        throw new UnsupportedOperationException("equipment Unsupported ClearFaceReader" + equipmentId);
    }
    protected Boolean clearUser(Integer userId, Integer equipmentId){
        throw new UnsupportedOperationException("equipment Unsupported clearUser" + equipmentId);
    }
    /**
     * 远程采集指纹
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡id
     * @param fingerNo 指纹编号
     * @return boolean
     */
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        throw new UnsupportedOperationException("equipment Unsupported remoteCollectFingerprint" + equipmentId);
    }

    /**
     * 删除指纹仪用户
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡号
     * @return {@link Boolean}
     */
    public Boolean delFingerReaderUser(Integer userId,Integer equipmentId,Integer cardId){
        throw new UnsupportedOperationException("equipment Unsupported remoteCollectFingerprint" + equipmentId);
    }
}
