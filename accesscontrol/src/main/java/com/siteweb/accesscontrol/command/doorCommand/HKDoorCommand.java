package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.enums.VendorEnum;
import com.siteweb.accesscontrol.service.FaceDataMapService;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 海康门禁控制实现，门禁类型11
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
@Service
@Slf4j
public class HKDoorCommand extends AbstractDoorCommand {

    @Autowired
    private FaceDataMapService faceDataMapService;
    /**
     * 删除人脸
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡id
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        //删除人脸
        //控制命令种类34,命令号96
        //000000,1, 000000BCDF	   门密码,用户ID,卡号
        Integer faceIdByCardId = faceDataMapService.findFaceIdByCardId(cardId);
        if (ObjectUtil.isNull(faceIdByCardId)) {
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFace: cannot find equipment by equipmentId: {}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("deleteAuthFace: cannot find card by cardId: {}" , cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_FACE.getCategory());

        Integer id = getCardId(accessCardInfo.getCardId());
        //门与卡的类型是否一致，一致则直接取卡号，否则取卡号转换,(卡号转换主要用于容错)
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s,%s", password, id, cardCode);
        log.info("deleteAuthFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //删除指纹（海康一体机）
        //控制命令种类32,命令号94
        //000000,1, 000000BCDF	   门密码,用户ID,卡号
        List<FingerPrint> cardFingerPrintByVendor = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.HIKVISION.getVendorId());
        if (CollUtil.isEmpty(cardFingerPrintByVendor)) {
            log.info("deleteAuthFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFingerprint: cannot find equipment by equipmentId:{}",equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("deleteAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }

        Integer stationId = equipmentDoor.getStationId();
        String password = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_FINGERPRINT.getCategory());

        Integer id = getCardId(accessCardInfo.getCardId());
        //门与卡的类型是否一致，一致则直接取卡号，否则取卡号转换,(卡号转换主要用于容错)
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s,%s", password, id, cardCode);
        log.info("deleteAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        //删除用户
        //控制命令种类36,命令号102
        //000000,1111+1111111111	   门密码,用户ID+卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("delManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_USER.getCategory());

        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(),accessCardInfo.getCardCodeType(),accessCardInfo.getCardCode(),accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s+%s", password, id, cardCode);
        log.info("delManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean editManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId, Date validTime) {
        //添加或修改用户(海康)
        //控制命令种类35,命令号101
        //000000,1111+1111111111,张三,0,20190101,20200101,1,1111  门密码,用户ID+卡号,用户姓名,用户权限,起始时间,结束时间,时间段ID,用户密码
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("editManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("editManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
        Integer doorNo = super.getTimeGroupNo(timeGroupNo, equipmentDoor.getDoorNo());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(),accessCardInfo.getCardCodeType(),accessCardInfo.getCardCode(),accessCardInfo.getCardCodeConv());
        String cardName = accessCardInfo.getCardName();
        String cardPassword = super.getCardPassword(accessCardInfo.getPassword());
        String command = String.format("%s,%s+%s,%s,%s,%s,%s,%s,%s", password, id, cardCode, cardName, 0, super.defaultTimeFormat(new Date()), super.defaultTimeFormat(validTime), doorNo, cardPassword);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId) {
        //添加或修改用户(海康)
        //控制命令种类35,命令号101
        //000000,1111+1111111111,张三,0,20190101,20200101,1,1111   门密码,用户ID+卡号,用户姓名,用户权限,起始时间,结束时间,时间段ID,用户密码
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = super.getDoorPassword(equipmentDoor.getPassword());
        String cardPassword = super.getCardPassword(accessCardInfo.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String cardName = accessCardInfo.getCardName();
        Integer doorGroupNo = getTimeGroupNo(timeGroupNo, equipmentDoor.getDoorNo());
        String command = String.format("%s,%s+%s,%s,%s,%s,%s,%s,%s", doorPassword, id, cardCode,cardName,0, defaultTimeFormat(new Date()),
                defaultTimeFormat(accessCardInfo.getEndTime()),doorGroupNo, cardPassword);
        log.info("addManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改人脸(海康)
        //控制命令种类33,命令号95
        //000000,1, 000000BCDF,1    门密码,用户ID,卡号,面部信息ID
        Integer faceId = faceDataMapService.findFaceIdByCardId(cardId);
        if (ObjectUtil.isNull(faceId)) {
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("AddOrUpdateFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("AddOrUpdateFace: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_FACE.getCategory());
        String command = String.format("%s,%s,%s,%s", password, id, cardCode, faceId);
        log.info("addOrUpdateAuthFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改指纹(海康）
        //控制命令种类31,命令号93
        //000000,1,000000BCDF,1	   门密码,用户ID,卡号,指纹信息ID
        List<FingerPrint> cardFingerPrintByVendor = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.HIKVISION.getVendorId());
        if (CollUtil.isEmpty(cardFingerPrintByVendor)) {
            log.info("addOrUpdateAuthFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("AddOrUpdateFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("AddOrUpdateFace: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_FINGERPRINT.getCategory());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s,%s,%s", password, id, cardCode, cardFingerPrintByVendor.get(0).getFingerPrintId());
        log.info("addOrUpdateAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setOpenMode(Integer userId, Integer equipmentId, Integer openMode) {
        //设置读卡器默认验证方式
        //控制命令种类39,命令号120
        //000000,123,1	  门密码,读卡器编号,读卡器默认验证方式
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("setOpenMode: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SET_DOOR_OPEN_MODE.getCategory());
        String command = String.format("%s,%s,%s", doorPassword, 2 * doorNo - 1, openMode);
        log.info("setOpenMode: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean remoteCollectFace(Integer userId, Integer equipmentId, Integer cardId) {
        //采集人脸(海康)
        //控制命令种类37,命令号110
        //000000,1111,1111111111	   门密码,用户ID,卡号（用户ID为卡ID后四位取模）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("remoteCollectFace: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SAMPLE_FACE.getCategory());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String doorPassword = getDoorPassword(accessCardInfo.getPassword());
        String command = String.format("%s,%s,%s",doorPassword , id, cardCode);
        log.info("SampleFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        //采集指纹(海康）
        //控制命令种类38,命令号111
        //000000,0,1111,1111111111	   门密码,手指编号,用户ID,卡号（用户ID为卡ID后四位取模）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("remoteCollectFingerprint: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SAMPLE_FINGERPRINT.getCategory());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String doorPassword = getDoorPassword(accessCardInfo.getPassword());
        String command = String.format("%s,%s,%s,%s", doorPassword, fingerNo, id, cardCode);
        log.info("SampleFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }
}
