package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CHD806门控制器命令处理类
 */
@Service
@Slf4j
public class CHD806DoorCommand extends CommonDoorCommand {
    /**
     * 清除命令字符串 - 全为0的224位长度字符串
     */
    private static final String CLEAR_COMMAND_STR = "0".repeat(224);
    /**
     * 142，密码，时间组序号(加卡命令中的权限)，门号，星期1星期2星期3星期4星期5星期6星期7
     * 命令格式
     */
    private static final String COMMAND_FORMAT = "142,%s,%s,%s,%s";
    /**
     * 空时间段
     */
    private static final String EMPTY_TIME_SPAN = "00000000";

    @Autowired
    private TimeGroupSpanService timeGroupSpanService;

    /**
     * 设置门禁时间组
     *
     * @param userId 用户ID
     * @param equipmentId 设备ID
     * @param addTimeGroupIdList 要添加的时间组ID列表
     * @param delTimeGroupIdList 要删除的时间组ID列表
     * @return 操作结果
     */
    @Override
    public Boolean setDoorTimeGroup(Integer userId, Integer equipmentId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList) {
        //新版准进时间组设置，虽然下面命令中包含了门号，但是实际下发时设置的是所有门，因为控制器里只有32张表，不够按门号再区分了。我们是将每一天的4个时段对应到控制器中的一张表，四类卡就对应了28张表，
        //142，密码，时间组序号(加卡命令中的权限)，门号，星期1星期2星期3星期4星期5星期6星期7
        //142,0000,1,3,00002359000023590000235900002359010023590100235901002359010023590200235902002359020023590200235903002359030023590300235903002359040023590400235904002359040023590500235905002359050023590500235906002359060023590600235906002359
        // 输入参数校验
        if (ObjectUtil.isNull(userId) || ObjectUtil.isNull(equipmentId)) {
            log.error("SetTimeGroup: invalid parameters, userId: {}, equipmentId: {}", userId, equipmentId);
            return Boolean.FALSE;
        }
        // 获取门禁设备信息
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("SetTimeGroup: cannot find equipment by equipmentId: {}", equipmentId);
            return Boolean.FALSE;
        }

        // 获取门ID
        Integer doorId = equipmentDoor.getDoorId();
        //获取命令id
        Integer commandId = controlService.findCommandIdByCommandCategory(equipmentDoor.getStationId(), equipmentDoor.getEquipmentId(), CommandCategoryEnum.SET_TIME_GROUP.getCategory());
        // 处理删除时间组
        if (CollectionUtil.isNotEmpty(delTimeGroupIdList)) {
            handleDeleteTimeGroups(userId, equipmentDoor, doorId, commandId, delTimeGroupIdList);
        }

        // 处理添加时间组
        if (CollectionUtil.isNotEmpty(addTimeGroupIdList)) {
            handleAddTimeGroups(userId, equipmentDoor, doorId, commandId, addTimeGroupIdList);
        }

        return Boolean.TRUE;
    }

    /**
     * 处理删除时间组
     */
    private void handleDeleteTimeGroups(Integer userId, EquipmentDoor equipmentDoor, Integer doorId, Integer commandId, List<Integer> delTimeGroupIdList) {
        for (Integer delTimeGroupId : delTimeGroupIdList) {
            Integer timeGroupType = doorTimeGroupService.findTimeGroupTimeByDoorIdAndTimeGroupId(doorId, delTimeGroupId);
            String commandStr = String.format(
                    COMMAND_FORMAT,
                    getDoorPassword(equipmentDoor.getPassword()),
                    getTimeGroupNo(timeGroupType, equipmentDoor.getDoorNo()),
                    equipmentDoor.getDoorNo(),
                    CLEAR_COMMAND_STR
            );
            activeControlManager.sendControl(equipmentDoor.getStationId(), equipmentDoor.getEquipmentId(), commandId, commandStr, userId);
            log.debug("Delete time group command sent: doorId={}, timeGroupId={}", doorId, delTimeGroupId);
        }
        // 从数据库中删除时间组
        doorTimeGroupService.deleteByDoorIdAndTimeGroupIds(doorId, delTimeGroupIdList);
    }

    /**
     * 处理添加时间组
     */
    private void handleAddTimeGroups(Integer userId, EquipmentDoor equipmentDoor, Integer doorId, Integer commandId, List<Integer> addTimeGroupIdList) {
        // 在数据库中创建时间组
        doorTimeGroupService.createDoorTimeGroup(doorId, addTimeGroupIdList);
        // 查询所有需要添加的时间组的时间段
        Map<Integer, List<TimeGroupSpan>> timeGroupSpanMap = fetchTimeGroupSpans(addTimeGroupIdList);
        for (Integer addTimeGroupId : addTimeGroupIdList) {
            Integer timeGroupType = doorTimeGroupService.findTimeGroupTimeByDoorIdAndTimeGroupId(doorId, addTimeGroupId);
            List<TimeGroupSpan> timeSpans = timeGroupSpanMap.getOrDefault(addTimeGroupId, Collections.emptyList());

            // 生成时间组命令
            String addCommandStr = processTimeSpans(timeSpans);
            String commandStr = String.format(
                    COMMAND_FORMAT,
                    getDoorPassword(equipmentDoor.getPassword()),
                    getTimeGroupNo(timeGroupType, equipmentDoor.getDoorNo()),
                    equipmentDoor.getDoorNo(),
                    addCommandStr
            );
            activeControlManager.sendControl(equipmentDoor.getStationId(), equipmentDoor.getEquipmentId(), commandId, commandStr, userId);
            log.debug("Add time group command sent: doorId={}, timeGroupId={}", doorId, addTimeGroupId);
        }
    }

    /**
     * 获取时间组时间段映射
     */
    private Map<Integer, List<TimeGroupSpan>> fetchTimeGroupSpans(List<Integer> timeGroupIds) {
        if (CollectionUtil.isEmpty(timeGroupIds)) {
            return Collections.emptyMap();
        }
        return timeGroupSpanService.findByTimeGroupIds(timeGroupIds)
                                   .stream()
                                   .collect(Collectors.groupingBy(TimeGroupSpan::getTimeGroupId));
    }

    /**
     * 处理时间段，将时间段格式化为控制器所需格式
     *
     * @param timeGroupSpans 时间组时间段列表
     * @return 格式化后的时间段字符串
     */
    public String processTimeSpans(List<TimeGroupSpan> timeGroupSpans) {
        if (CollectionUtil.isEmpty(timeGroupSpans)) {
            return CLEAR_COMMAND_STR;
        }

        StringBuilder result = new StringBuilder();
        for (TimeGroupSpan span : timeGroupSpans) {
            // 获取原始的时间段字符串
            String timeSpanChar = span.getTimeSpanChar();

            if (timeSpanChar == null) {
                log.warn("Null timeSpanChar found in TimeGroupSpan: {}", span);
                continue;
            }

            // 移除所有的冒号和连字符
            String processed = timeSpanChar.replace(":", "").replace("-", "");

            // 协议规定有4个时间段，但是平台侧只有3个时间段，故第四个时间段都置为0
            processed = processed + EMPTY_TIME_SPAN;

            // 将处理后的字符串添加到结果中
            result.append(processed);
        }

        return result.toString();
    }
}
