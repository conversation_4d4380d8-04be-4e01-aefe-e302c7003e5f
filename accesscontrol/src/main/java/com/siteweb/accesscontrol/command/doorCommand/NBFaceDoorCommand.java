package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.enums.VendorEnum;
import com.siteweb.accesscontrol.service.FaceDataService;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class NBFaceDoorCommand extends AbstractDoorCommand{
    @Autowired
    FaceDataService faceDataService;
    @Override
    public Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        //删除用户
        //控制命令种类36,命令号102
        //000000,1111+1111111111	   门密码,用户ID+卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("delManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_USER.getCategory());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        String command = String.format("%s,%s+%s", doorPassword, id, cardCode);
        log.info("delManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId) {
        //添加或修改用户(纽贝尔人脸一体机)
        //控制命令种类35,命令号101
        //000000,1111+1111111111,张三,0,20190101,20200101,1,1111	   门密码,用户ID+卡号,用户姓名,用户权限,起始时间,结束时间,时间段ID,用户密码
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardPassword = getCardPassword(accessCardInfo.getPassword());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        Integer timeGroup = getTimeGroupNo(timeGroupNo, equipmentDoor.getDoorNo());
        String startTime = defaultTimeFormat(new Date());
        String endTime = defaultTimeFormat(accessCardInfo.getEndTime());
        String command = String.format("%s,%s+%s,%s,%s,%s,%s,%s,%s", doorPassword, id, cardCode, accessCardInfo.getCardName(), 0, startTime, endTime, timeGroup, cardPassword);
        log.info("addManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }


    @Override
    public Boolean editManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId, Date validTime) {
        //添加或修改用户(纽贝尔人脸一体机)
        //控制命令种类35,命令号101
        //000000,1111+1111111111,张三,0,20190101,20200101,1,1111	   门密码,用户ID+卡号,用户姓名,用户权限,起始时间,结束时间,时间段ID,用户密码
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("editManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("editManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String endTime = defaultTimeFormat(validTime);
        Integer timeGroup = getTimeGroupNo(timeGroupNo, equipmentDoor.getDoorNo());
        String startTime = defaultTimeFormat(new Date());
        String cardPassword = getCardPassword(accessCardInfoDTO.getPassword());
        String command = String.format("%s,%s+%s,%s,%s,%s,%s,%s,%s", doorPassword, id, cardCode, accessCardInfoDTO.getCardName(), 0, startTime, endTime, timeGroup, cardPassword);
        log.info("editManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentDoor, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setOpenMode(Integer userId, Integer equipmentId, Integer openMode) {
        //设置读卡器默认验证方式
        //控制命令种类39,命令号120
        //123,1	  读卡器编号,读卡器默认验证方式
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("setOpenMode: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SET_DOOR_OPEN_MODE.getCategory());
        String command = String.format("%s,%s", 2 * doorNo - 1, openMode);
        log.info("setOpenMode: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentDoor, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改指纹(纽贝尔人脸一体机）
        //控制命令种类31,命令号93
        //000000,1,000000BCDF,1	   门密码,用户ID,卡号,指纹信息ID
        Integer fingerPrintId = fingerprintService.findCardFingerprintIdByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (Objects.isNull(fingerPrintId)) {
            log.error("AddOrUpdateFingerprint: not find fingerprint by equipmentId:{}, cardId: {}",equipmentId,cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("editManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("editManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_FINGERPRINT.getCategory());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String command = String.format("%s,%s,%s,%s", doorPassword, id, cardCode, fingerPrintId);
        log.info("addOrUpdateAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //删除指纹（纽贝尔人脸一体机）
        //控制命令种类32,命令号94
        //000000,1, 000000BCDF	   门密码,用户ID,卡号
        Integer fingerPrintId = fingerprintService.findCardFingerprintIdByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (Objects.isNull(fingerPrintId)) {
            log.error("deleteAuthFingerprint: not find fingerprint by equipmentId:{}, cardId: {}",equipmentId,cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("deleteAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("deleteAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }

        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_FINGERPRINT.getCategory());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        String command = String.format("%s,%s,%s", doorPassword, id, cardCode);
        log.info("deleteAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改人脸(纽贝尔人脸一体机)
        //控制命令种类33,命令号95
        //000000,1, 000000BCDF,1	   门密码,用户ID,卡号,面部信息ID
        Integer faceId = faceDataService.findFaceIdByCardId(cardId);
        if (Objects.isNull(faceId)) {
            log.error("addOrUpdateAuthFace: not find face data by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("deleteAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("deleteAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_OR_UPDATE_FACE.getCategory());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        String command = String.format("%s,%s,%s,%s", doorPassword, id, cardCode, faceId);
        log.info("addOrUpdateAuthFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        //删除人脸
        //控制命令种类34,命令号96
        //000000,1, 000000BCDF	   门密码,用户ID,卡号
        Integer faceId = faceDataService.findFaceIdByCardId(cardId);
        if (Objects.isNull(faceId)) {
            log.error("deleteAuthFace: not find face data by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("deleteAuthFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("deleteAuthFace: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_FACE.getCategory());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        String command = String.format("%s,%s,%s", doorPassword, id, cardCode);
        log.info("deleteAuthFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean remoteCollectFace(Integer userId, Integer equipmentId, Integer cardId) {
        //采集人脸(纽贝尔人脸一体机)
        //控制命令种类37,命令号110
        //000000,1111,1111111111	   门密码,用户ID,卡号（用户ID为卡ID后四位取模）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("remoteCollectFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("remoteCollectFace: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SAMPLE_FACE.getCategory());
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String command = String.format("%s,%s,%s",doorPassword,id,cardCode);
        log.info("remoteCollectFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        //采集指纹(纽贝尔人脸一体机）
        //控制命令种类38,命令号111
        //000000,0,1111,1111111111	   门密码,手指编号,用户ID,卡号（用户ID为卡ID后四位取模）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("remoteCollectFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.SAMPLE_FINGERPRINT.getCategory());
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String command = String.format("%s,%s,%s,%s", doorPassword, fingerNo, id, cardCode);
        log.info("remoteCollectFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }
    @Override
    public Boolean clearCard(Integer userId, Integer equipmentId) {
        //删除所有门禁卡
        //控制命令种类17,命令号45,
        //000000,1						                门密码,门编号；本例：删除门禁中所有门禁卡
        String command = "1";
        return sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.DEL_ALL_MANUAL_DOOR_CARD, command);
    }
    @Override
    public Boolean setTime(Integer userId, Integer equipmentId, String timeStr) {
        //门禁校时
        //控制命令种类22,命令号32
        //000000,200610131047035	 门密码,时间(yyyymmddhhmmssd)；本例：设置时间为：2006-10-13 10:47:03 Fri
        int week = DateUtil.thisDayOfWeek() - 1;
        week = week == 0 ? 7 : week;
        String command = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + week;
        return sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_TIME, command);
    }
}
