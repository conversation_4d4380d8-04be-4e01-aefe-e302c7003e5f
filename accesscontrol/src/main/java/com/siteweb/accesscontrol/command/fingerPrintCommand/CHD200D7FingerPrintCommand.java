package com.siteweb.accesscontrol.command.fingerPrintCommand;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.util.EncodingUtil;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import org.springframework.stereotype.Component;

@Component
public class CHD200D7FingerPrintCommand extends AbstractFingerPrintCommand{
    public static final String DEFAULT_PASSWORD = "8888888888";
    @Override
    public String addFingerReaderUser(ChildEquipment fingerPrintData, EquipmentDoor equipmentDoor, AccessCardInfoDTO accessCardInfo) {
        int cardId =  getCardId(accessCardInfo.getCardId());
        String userNo = CharSequenceUtil.padPre(Integer.toString(cardId), 8, '0');
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo);
        String cardPass = getCardPassword(accessCardInfo.getPassword());
        return String.format("%s,%s,%s,%s,%s,%s,%s,%s", DEFAULT_PASSWORD, userNo, cardId * 10, cardId * 10 + 1, cardId * 10 + 2, cardPass, cardCode.substring(2), EncodingUtil.utf8ToGBKHex(accessCardInfo.getCardName()));
    }

    @Override
    public String addOrUpdateAuthFingerprint(ChildEquipment fingerPrintData, FingerPrint fingerprint, EquipmentDoor equipmentDoor, Integer cardId) {
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        cardId = getCardId(cardId);
        String userNo = getUserNo(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo);
        int length = fingerprint.getFingerPrintData().length;
        return String.format("%s,%s%s,%s,%s,%s,%s,%s", DEFAULT_PASSWORD, cardId, fingerprint.getFingerPrintNo(), userNo,
                cardCode, EncodingUtil.utf8ToGBKHex(accessCardInfo.getCardName()), length,
                fingerprint.getFingerPrintId());
    }

    @Override
    public String deleteAuthFingerprint(ChildEquipment fingerPrintData, FingerPrint fingerprint, EquipmentDoor equipmentDoor, Integer cardId) {
        cardId =  getCardId(cardId);
        String userNo = getUserNo(cardId);
        return String.format("%s,%s%s,%s", DEFAULT_PASSWORD, cardId, fingerprint.getFingerPrintNo(), userNo);
    }
}
