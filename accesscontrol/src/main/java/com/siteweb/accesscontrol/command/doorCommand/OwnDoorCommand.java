package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.enums.EquipmentTypeEnum;
import com.siteweb.accesscontrol.enums.VendorEnum;
import com.siteweb.accesscontrol.service.FaceDataMapService;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class OwnDoorCommand extends AbstractDoorCommand {
    @Autowired
    private FaceDataMapService faceDataMapService;
    @Override
    public Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        //删除门禁卡
        //控制命令种类13,命令号37
        //000000,0+0002148D03					门密码,0+卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("delManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DEL_MANUAL_DOOR_CARD.getCategory());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s+%s", password, 0, cardCode);
        log.info("DelManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId) {
        //增加门禁卡
        //控制命令种类12,命令号36
        //000000,0+0002148D03,1,7777,20501231,张三			门密码,卡编号+卡号,时间组号,卡密码,卡有效期,卡名称
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("AddManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("AddManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = super.getDoorPassword(equipmentDoor.getPassword());
        String cardPassword = super.getCardPassword(accessCardInfo.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_MANUAL_DOOR_CARD.getCategory());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        Integer doorTimeGroupNo = getTimeGroupNo(timeGroupNo, equipmentDoor.getDoorNo());
        String command = String.format("%s,%s+%s,%s,%s,%s,%s", doorPassword, id, cardCode,doorTimeGroupNo,cardPassword,defaultTimeFormat(accessCardInfo.getEndTime()),accessCardInfo.getCardName());
        log.info("AddManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setTime(Integer userId, Integer equipmentId, String timeStr) {
        //门禁校时
        //控制命令种类22,命令号32
        //000000,200610131047035	 门密码,时间(yyyymmddhhmmss)；本例：设置时间为：2006-10-13 10:47:03 Fri
        int week = DateUtil.thisDayOfWeek() - 1;
        week = week == 0 ? 7 : week;
        String command = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + week;
        sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_TIME, command);
        return Boolean.TRUE;
    }

    @Override
    public Boolean clearCard(Integer userId, Integer equipmentId) {
        //删除所有门禁卡
        //控制命令种类13,命令号37
        //000000,0+0					门密码,0+0
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("clearCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DEL_MANUAL_DOOR_CARD.getCategory());
        String command = String.format("%s,%s+%s", doorPassword, 0, 0);
        log.info("clearCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        clearFingerReader(userId, equipmentId);
        ClearFaceReader(userId, equipmentId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改指纹
        //控制命令种类40,命令号18
        //000000,0,0001,0000001302,1	   门密码,指纹标志,用户ID，卡号,卡名称,指纹指纹信息ID （密码仅作占位，实际无检验）
        List<FingerPrint> fingerprintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerprintList)) {
            log.info("AddOrUpdateFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return true;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addOrUpdateAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        Integer fingerPrintNo = fingerprintList.get(0).getFingerPrintNo();
        Integer fingerPrintId = fingerprintList.get(0).getFingerPrintId();
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SET_FINGERPRINT.getCategory());
            String command = String.format("%s,%s,%s,%s,%s,%s", doorPassword, id, fingerPrintNo, cardCode, accessCardInfo.getCardName(), fingerPrintId);
            log.info("SetFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
            //添加指纹授权到数据库
            fingerprintAuthService.saveAuthFingerPrint(equipmentDoor.getStationId(), equipmentId, cardId, fingerPrintId, VendorEnum.VERTIV.getVendorId());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //删除指纹
        //控制命令种类41,命令号19
        //000000,0011	   门密码,用户ID （密码仅作占位，实际无检验）
        List<FingerPrint> fingerprintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerprintList)) {
            log.info("deleteAuthFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return true;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("deleteAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer id = getCardId(accessCardInfo.getCardId());
        Integer fingerPrintId = fingerprintList.get(0).getFingerPrintId();
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            String command = String.format("%s,%s", doorPassword, id);
            log.info("ClearFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
            //添加指纹授权到数据库
            fingerprintAuthService.deleteFingerprintAuth(equipmentDoor.getStationId(), equipmentId, cardId, fingerPrintId, VendorEnum.VERTIV.getVendorId());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean remoteCollectFace(Integer userId, Integer equipmentId, Integer cardId) {
        //采集人脸
        //控制命令种类37,命令号110
        //000000,1111,1111111111	   门密码,用户ID,卡号（用户ID为卡ID后四位取模）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("remoteCollectFace: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FACE_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SAMPLE_FACE.getCategory());
            String command = String.format("%s,%s,%s", doorPassword, id, cardCode);
            log.info("SampleFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, equipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        //采集指纹
        //控制命令种类38,命令号60
        //000000,0,00000001,009F20A8	   门密码,指纹编号,用户ID(8位),卡号 （密码仅作占位，实际无检验）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("remoteCollectFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SAMPLE_FINGERPRINT.getCategory());
            String command = String.format("%s,%s,%s,%s", doorPassword, fingerNo, id, cardCode);
            log.info("SampleFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, equipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    protected Boolean clearFingerReader(Integer userId, Integer equipmentId) {
        //删除所有指纹
        //控制命令种类41,命令号19
        //000000,0	   门密码,0 （密码仅作占位，实际无检验）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            String command = String.format("%s,%s", doorPassword, 0);
            log.info("clearFingerReader: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, equipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改人脸
        //控制命令种类33,命令号95
        //000000,1, 000000BCDF,1	   门密码,用户ID,卡号,卡名称,面部信息ID （密码仅作占位，实际无检验）
        Integer faceId = faceDataMapService.findFaceIdByCardId(cardId);
        if (ObjectUtil.isNull(faceId)) {
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addOrUpdateAuthFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFace: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FACE_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(accessCardInfo.getPassword());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String cardName = accessCardInfo.getCardName();
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            String command = String.format("%s,%s,%s,%s,%s", doorPassword, id, cardCode, cardName, faceId);
            log.info("AddOrUpdateFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFace(Integer userId, Integer equipmentId, Integer cardId) {
        //删除人脸
        //控制命令种类34,命令号96
        //000000,1, 000000BCDF	   门密码,用户ID,卡号 （密码仅作占位，实际无检验）
        Integer faceId = faceDataMapService.findFaceIdByCardId(cardId);
        if (ObjectUtil.isNull(faceId)) {
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("deleteAuthFace: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FACE_READER.getEquipmentCategory(),VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(accessCardInfo.getPassword());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.DELETE_FACE.getCategory());
            String command = String.format("%s,%s,%s", doorPassword, id, cardCode);
            log.info("DeleteFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean ClearFaceReader(int userId, int equipmentId) {
        //删除所有人脸
        //控制命令种类34,命令号96
        //000000,0,0	   门密码,0,0 （密码仅作占位，实际无检验,参照单个删除命令，中间0为删除所有用户）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFace: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FACE_READER.getEquipmentCategory(), VendorEnum.VERTIV.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.DELETE_FACE.getCategory());
            String command = String.format("%s,%s,%s", doorPassword, 0, 0);
            log.info("DeleteFace: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }
}
