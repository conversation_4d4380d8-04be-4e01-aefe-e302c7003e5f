package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 纽贝尔（HTTP协议）门禁实现，腾讯用，门禁类型12
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
@Service
@Slf4j
public class NBHttpDoorCommand extends AbstractDoorCommand {
    @Override
    public Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        //删除门禁卡
        //控制命令种类13,命令号37
        //000000,0+0002148D03	门密码,0+卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("DelManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("DelManualDoorCard: cannot find card by cardId:{}", cardId);
        }

        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DELETE_FINGERPRINT.getCategory());
        //门与卡的类型是否一致，一致则直接取卡号，否则取卡号转换,(卡号转换主要用于容错)
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s+%s", password, 0, cardCode);
        log.info("DelManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId) {
        //增加门禁卡
        //控制命令种类12,命令号36
        //000000,0+0002148D03,1,7777,20501231	门密码,卡编号+卡号,时间组号,卡密码,卡有效期
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("AddManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("AddManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = super.getDoorPassword(equipmentDoor.getPassword());
        String cardPassword = super.getCardPassword(accessCardInfo.getPassword());
        Integer doorNo = equipmentDoor.getDoorNo();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_MANUAL_DOOR_CARD.getCategory());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        String command = String.format("%s,%s+%s,%s,%s,%s", doorPassword, id, cardCode,(timeGroupNo - 10) + 4 * (doorNo - 1),cardPassword,defaultTimeFormat(accessCardInfo.getEndTime()));
        log.info("AddManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setDoorOpenDelay(Integer userId, Integer equipmentId, Integer openDelay) {
        //门开延迟
        //000000,1,160	门密码,门编号,延时时间(单位为0.1秒)； 本例：设置门开延时为160（0.1秒）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("OpenDelay: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.OPEN_DELAY.getCategory());
        String command = String.format("%s,%s,%s", doorPassword, doorNo, openDelay);
        log.info("addOrUpdateAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setTime(Integer userId, Integer equipmentId, String timeStr) {
        //门禁校时
        //控制命令种类22,命令号32
        //000000,200610131047035	 门密码,时间(yyyymmddhhmmss)；本例：设置时间为：2006-10-13 10:47:03 Fri
        //cn.hutool.core.date.Week对应星期值，需要处理
        int week = DateUtil.thisDayOfWeek() - 1;
        week = week == 0 ? 7 : week;
        String command = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + week;
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_TIME, command);
    }

    @Override
    public Boolean setSlotInterval(Integer userId, Integer equipmentId, Integer slotInterval) {
        String command = slotInterval.toString();
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_SLOT_INTERVAL, command);
    }

    @Override
    public Boolean setLockErrorCount(Integer userId, Integer equipmentId, Integer lockErrorCount) {
        String command = lockErrorCount.toString();
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_LOCK_ERROR_COUNT, command);
    }

    @Override
    public Boolean setLockTime(Integer userId, Integer equipmentId, Integer lockTime) {
        String command = lockTime.toString();
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_LOCK_TIME, command);
    }

    @Override
    public Boolean setKeepTime(Integer userId, Integer equipmentId, Integer keepTime) {
        String command = keepTime.toString();
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_KEEP_TIME, command);
    }

    @Override
    public Boolean setNormallyOpen(Integer userId, Integer equipmentId) {
        String command = "1";
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_DOOR_NORMALLY_OPEN, command);
    }

    @Override
    public Boolean setNormallyClose(Integer userId, Integer equipmentId) {
        String command = "1";
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_DOOR_NORMALLY_CLOSE, command);
    }

    @Override
    public Boolean setFireSignal(Integer userId, Integer equipmentId, Integer fireSignal) {
        String command = fireSignal.toString();
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_FIRE_SIGNAL, command);
    }

    @Override
    public Boolean setOpenMode(Integer userId, Integer equipmentId, Integer openMode) {
        String command = openMode.toString();
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_OPEN_MODE, command);
    }
}
