package com.siteweb.accesscontrol.command.fingerPrintCommand;

import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CommonFingerPrintCommand extends AbstractFingerPrintCommand {
    public static final String DEFAULT_PASSWORD = "8888888888";

    @Override
    public String addFingerReaderUser(ChildEquipment fingerPrintData, EquipmentDoor equipmentDoor, AccessCardInfoDTO accessCardInfo) {
        int cardId =  getCardId(accessCardInfo.getCardId());
        String userNo = getUserNo(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo);
        String cardPassword = getCardPassword(accessCardInfo.getPassword());
        return String.format("%s,%s,%s,%s,%s,%s,%s", DEFAULT_PASSWORD, userNo, cardId * 10, cardId * 10 + 1, cardId * 10 + 2, cardPassword, cardCode.substring(2));
    }

    @Override
    public String addOrUpdateAuthFingerprint(ChildEquipment fingerPrintData, FingerPrint fingerprint, EquipmentDoor equipmentDoor, Integer cardId) {
        cardId = getCardId(cardId);
        int length = fingerprint.getFingerPrintData().length;
        return String.format("%s,%s%s,%s,%s", DEFAULT_PASSWORD, cardId, fingerprint.getFingerPrintNo(), length, fingerprint.getFingerPrintId());
    }

    @Override
    public String deleteAuthFingerprint(ChildEquipment fingerPrintData, FingerPrint fingerprint, EquipmentDoor equipmentDoor, Integer cardId) {
        cardId = getCardId(cardId);
        return String.format("%s,%s%s", DEFAULT_PASSWORD, cardId, fingerprint.getFingerPrintNo());
    }
}
