package com.siteweb.accesscontrol.command;

import cn.hutool.extra.spring.SpringUtil;
import com.siteweb.accesscontrol.command.fingerPrintCommand.AbstractFingerPrintCommand;
import com.siteweb.accesscontrol.command.fingerPrintCommand.CHD200D7FingerPrintCommand;
import com.siteweb.accesscontrol.command.fingerPrintCommand.CHD200G41CFingerPrintCommand;
import com.siteweb.accesscontrol.command.fingerPrintCommand.CommonFingerPrintCommand;
import com.siteweb.accesscontrol.service.FingerprintService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.entity.ConfigChangeMacroLog;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * 指纹控制命令工厂
 * <AUTHOR>
 * @date 2023/10/30
 */
@Slf4j
@Component
public class FingerPrintCommandFactory {
    private static final ConcurrentHashMap<Integer, AbstractFingerPrintCommand> Finger_Print_COMMAND_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    private Date lastUpdateTime = new Date();
    @Autowired
    private FingerprintService fingerprintService;
    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;

    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncEquipment() {
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        List<ConfigChangeMacroLog> configChangeMacroLogList = configChangeMacroLogMapper.findEquipmentChangeMacroLogByUpdateTime(startTime, endTime);
        if (!configChangeMacroLogList.isEmpty()) {
            Finger_Print_COMMAND_CONCURRENT_HASH_MAP.clear();
            log.info("Clear All Door Command");
        }
    }

    public AbstractFingerPrintCommand getAbstractFingerPrintCommandInstance(Integer equipmentId) {
        AbstractFingerPrintCommand abstractFingerPrintCommand = Finger_Print_COMMAND_CONCURRENT_HASH_MAP.get(equipmentId);
        if (Objects.nonNull(abstractFingerPrintCommand)) {
            return abstractFingerPrintCommand;
        }
        abstractFingerPrintCommand = getAbstractFingerPrintCommand(equipmentId);
        Finger_Print_COMMAND_CONCURRENT_HASH_MAP.put(equipmentId, abstractFingerPrintCommand);
        return abstractFingerPrintCommand;
    }

    /**
     * 经过数据库查询指纹读头设备类型
     * @param equipmentId 设备id
     * @return {@link AbstractFingerPrintCommand}
     */
    private AbstractFingerPrintCommand getAbstractFingerPrintCommand(Integer equipmentId) {
        Integer fingerPrintCategory = fingerprintService.findFingerPrintCategoryByEquipmentId(equipmentId);
        return switch (fingerPrintCategory) {
            //如果CHD200D7的父子设备是拆开的，则equipmentId是子设备的id，类型为1
            //如果CHD200D7的父子设备是合并的，则equipmentId是父设备的id，类型为17
            //控制命令是一样的
            case 1, 17 -> SpringUtil.getBean(CHD200D7FingerPrintCommand.class);
            case 2 -> SpringUtil.getBean(CHD200G41CFingerPrintCommand.class);
            default -> SpringUtil.getBean(CommonFingerPrintCommand.class);
        };
    }
}
