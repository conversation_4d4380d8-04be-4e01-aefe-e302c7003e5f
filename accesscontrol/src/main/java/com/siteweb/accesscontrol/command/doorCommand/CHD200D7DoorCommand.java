package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.command.FingerPrintCommandFactory;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.enums.EquipmentTypeEnum;
import com.siteweb.accesscontrol.enums.VendorEnum;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service("CHD200D7DoorCommand")
@Slf4j
public class CHD200D7DoorCommand extends AbstractDoorCommand {
    @Autowired
    private FingerPrintCommandFactory fingerPrintCommandFactory;
    @Override
    public Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        //删除门禁卡
        //控制命令种类13,命令号37
        //000000,0+0002148D03	门密码,0+卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("delManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer stationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DEL_MANUAL_DOOR_CARD.getCategory());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s+%s", doorPassword, 0, cardCode);
        log.info("delManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return delFingerReaderUser(userId, equipmentId, cardId);
    }

    @Override
    public Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId) {
        //增加门禁卡
        //控制命令种类12,命令号36
        //000000,0+0002148D03,1,7777,20501231			门密码,卡编号+卡号,时间组号,卡密码,卡有效期
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_MANUAL_DOOR_CARD.getCategory());
        Integer id = getCardId(accessCardInfo.getCardId());
        String cardPassword = getCardPassword(accessCardInfo.getPassword());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        String endTime = defaultTimeFormat(accessCardInfo.getEndTime());
        doorNo = getTimeGroupNo(timeGroupNo, doorNo);
        String command = String.format("%s,%s+%s,%s,%s,%s", doorPassword, id, cardCode, doorNo, cardPassword, endTime);
        log.info("addManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return addFingerReaderUser(userId, equipmentId, cardId);
    }

    @Override
    public Boolean addFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改用户(纽贝尔指纹读头)
        //控制命令种类35,命令号17
        //8888888888,00000001,10,11,12,0000,009F20A8	   权限密码,用户ID,第一枚指纹编号,第二枚指纹编号,第三枚指纹编号,密码,卡号（仅支持8位卡号）
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类35,命令号117，模板修改，上层软件无需更改
        //20230112 xsx
        //8888888888,00000001,10,11,12,0000,009F20A8,张三 权限密码,用户ID,第一枚指纹编号,第二枚指纹编号,第三枚指纹编号,密码,卡号,用户名(GB2312编码的十六进制字符串)
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("addFingerReaderUser: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("AddOrUpdateUser: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(), VendorEnum.NEWBEL.getVendorId());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
            String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                      .addFingerReaderUser(childEquipment, equipmentDoor, accessCardInfoDTO);
            log.info("addFingerReaderUser: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean editManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId, Date validTime) {
        //修改门禁卡
        //控制命令种类14,命令号38
        //000000,0+0002148D03,1,7777,20501231			门密码,卡编号+卡号,时间组号,卡密码,卡有效期
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("editManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("editManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.EDIT_MANUAL_DOOR_CARD.getCategory());
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfoDTO.getCardCodeType(), accessCardInfoDTO.getCardCode(), accessCardInfoDTO.getCardCodeConv());
        String endTime = defaultTimeFormat(validTime);
        Integer doorTimeGroupNo = getTimeGroupNo(timeGroupNo, equipmentDoor.getDoorNo());
        String command = String.format("%s,%s+%s,%s,%s,%s", doorPassword, id, cardCode, doorTimeGroupNo, accessCardInfoDTO.getPassword(), endTime);
        log.info("command: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentDoor, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        addFingerReaderUser(userId, equipmentId, cardId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setOpenMode(Integer userId, Integer equipmentId, Integer openMode) {
        //刷卡进门密码工作方式
        //000000,1,1						门密码,门编号,门开方式(0:不用密码；1:要密码)； 本例：进门要密码
        //000000,1,0						门密码,门编号,门开方式(0:不用密码；1:要密码)； 本例：进门不用密码
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("setOpenMode: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.MANUAL_DOOR_WORK_MODE.getCategory());
        String command = String.format("%s,%s,%s", doorPassword, equipmentDoor.getDoorNo(), userId);
        log.info("SetDoorOpenMode: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setDoorOpenDelay(Integer userId, Integer equipmentId, Integer openDelay) {
        //门开延迟
        //000000,1,160					门密码,门编号,延时时间(单位为0.1秒)； 本例：设置门开延时为160（0.1秒）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("OpenDelay: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.OPEN_DELAY.getCategory());
        String command = String.format("%s,%s,%s", doorPassword, doorNo, openDelay);
        log.info("setDoorOpenDelay: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setTime(Integer userId, Integer equipmentId, String timeStr) {
        //门禁校时
        //控制命令种类22,命令号32
        //000000,200610131047035	 门密码,时间(yyyymmddhhmmssd)；本例：设置时间为：2006-10-13 10:47:03 Fri
        int week = DateUtil.thisDayOfWeek() - 1;
        week = week == 0 ? 7 : week;
        String command = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + week;
        return sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_TIME, command);
    }

    @Override
    public Boolean clearCard(Integer userId, Integer equipmentId) {
        //删除所有门禁卡
        //控制命令种类17,命令号45,
        //000000,1						                门密码,1；本例：删除门禁中所有门禁卡
        String command = "1";
        boolean clearUserResult = sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.DEL_ALL_MANUAL_DOOR_CARD, command);
        boolean clearFingerReaderResult = clearFingerReader(userId, equipmentId);
        return clearUserResult && clearFingerReaderResult;
    }

    @Override
    protected Boolean clearFingerReader(Integer userId, Integer equipmentId) {
        //删除所有指纹（纽贝尔指纹机）
        //控制命令种类41,命令号19
        //8888888888,0	   权限密码,0
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("ClearFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(), VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer clearFingerCommandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            String clearFingerCommand = String.format("%s,0", FINGERPRINT_DEFAULT_PASSWORD);
            log.info("ClearFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, clearFingerCommandId, clearFingerCommand);
            activeControlManager.sendControl(childStationId, childEquipmentId, clearFingerCommandId, clearFingerCommand, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改指纹(纽贝尔）
        //控制命令种类40,命令号18
        //8888888888,10,1302,1	   权限密码,指纹标志,指纹长度,指纹指纹信息ID
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类40,命令号118，模板修改，上层软件无需更改
        //新的命令格式修改为： 8888888888,100,123,009F20A8,张三,1302,22     权限密码,【卡Id】【指纹编号】,用户ID,卡号,用户名,指纹长度,指纹指纹信息ID
        List<FingerPrint> fingerPrintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            log.info("AddOrUpdateFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addOrUpdateAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer childStationId = childEquipment.getStationId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SET_FINGERPRINT.getCategory());
            for (FingerPrint fingerPrint : fingerPrintList) {
                String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                          .addOrUpdateAuthFingerprint(childEquipment, fingerPrint, equipmentDoor, cardId);
                log.info("SetFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
                activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
            }
            //添加指纹授权到数据库
            fingerprintAuthService.saveAuthFingerPrint(equipmentDoor.getStationId(), equipmentId, cardId, fingerPrintList.get(0).getFingerPrintId(), VendorEnum.NEWBEL.getVendorId());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //删除指纹（纽贝尔指纹机）
        //控制命令种类41,命令号19
        //8888888888,11,00000001	   权限密码,[卡id][指纹编号],用户id(8位) （指纹标志为卡ID和指纹编号拼接，例子中卡ID为1，指纹号为1,用户id为1）
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类41,命令号119，模板修改，上层软件无需更改
        List<FingerPrint> fingerPrintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            log.info("deleteAuthFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("deleteAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer childStationId = childEquipment.getStationId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            for (FingerPrint fingerPrint : fingerPrintList) {
                String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                          .deleteAuthFingerprint(childEquipment, fingerPrint, equipmentDoor, cardId);
                log.info("deleteAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
                activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
            }
            //删除指纹到数据库
            fingerprintAuthService.deleteFingerprintAuth(equipmentDoor.getStationId(), equipmentId, cardId, fingerPrintList.get(0).getFingerPrintId(), VendorEnum.NEWBEL.getVendorId());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean delFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
        //删除用户(纽贝尔指纹读头)
        //控制命令种类36,命令号20
        //8888888888,10	   权限密码,用户ID
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delFingerReaderUser: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.DELETE_USER.getCategory());
            Integer id = getCardId(cardId);
            String userNo = getUserNo(id);
            String command = String.format("%s,%s",FINGERPRINT_DEFAULT_PASSWORD,userNo);
            log.info("delFingerReaderUser: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        //采集指纹（纽贝尔）
        //控制命令种类38,命令号60
        //8888888888,0,1,009F20A8	   权限密码,指纹编号,卡Id,卡号
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类38,命令号160，模板修改，上层软件无需更改
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("remoteCollectFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SAMPLE_FINGERPRINT.getCategory());
            String command = String.format("%s,%s,%s,%s", FINGERPRINT_DEFAULT_PASSWORD, fingerNo, id, cardCode);
            log.info("remoteCollectFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }
}
