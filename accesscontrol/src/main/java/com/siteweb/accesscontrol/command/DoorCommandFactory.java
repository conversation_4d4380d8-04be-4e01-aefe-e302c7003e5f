package com.siteweb.accesscontrol.command;

import cn.hutool.extra.spring.SpringUtil;
import com.siteweb.accesscontrol.command.doorCommand.*;
import com.siteweb.accesscontrol.service.DoorService;
import com.siteweb.common.util.DateUtil;
import com.siteweb.monitoring.entity.ConfigChangeMacroLog;
import com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DoorCommandFactory {

    private static final ConcurrentHashMap<Integer,AbstractDoorCommand> DOOR_COMMAND_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    private Date lastUpdateTime = new Date();
    @Autowired
    private DoorService doorService;
    @Autowired
    ConfigChangeMacroLogMapper configChangeMacroLogMapper;

    @Scheduled(fixedDelay = 30 * 1000) // every 30 seconds
    protected void syncEquipment() {
        if (new Date().after(DateUtil.dateAddMinutes(lastUpdateTime, 60))) {
            lastUpdateTime = new Date();
        }
        Date startTime = DateUtil.dateAddMinutes(lastUpdateTime, -5);
        Date endTime = DateUtil.dateAddMinutes(lastUpdateTime, 60);
        List<ConfigChangeMacroLog> configChangeMacroLogList = configChangeMacroLogMapper.findEquipmentChangeMacroLogByUpdateTime(startTime, endTime);
        if (!configChangeMacroLogList.isEmpty()) {
            DOOR_COMMAND_CONCURRENT_HASH_MAP.clear();
            log.info("Clear All Door Command");
        }
    }
    /**
     * 获取门禁命令的具体实现
     * @param equipmentId 设备id
     * @return {@link AbstractDoorCommand}
     */
    public AbstractDoorCommand getCommandInstance(Integer equipmentId){
        AbstractDoorCommand doorCommand = DOOR_COMMAND_CONCURRENT_HASH_MAP.get(equipmentId);
        if (Objects.nonNull(doorCommand)) {
            return doorCommand;
        }
        doorCommand = this.getAbstractDoorCommandFormDb(equipmentId);
        DOOR_COMMAND_CONCURRENT_HASH_MAP.put(equipmentId, doorCommand);
        return doorCommand;
    }

    /**
     * 经过数据库查询门禁设备类型
     * @param equipmentId 设备id
     * @return {@link AbstractDoorCommand}
     */
    private AbstractDoorCommand getAbstractDoorCommandFormDb(Integer equipmentId) {
        Integer doorCategory = doorService.findDoorCategoryByEquipmentId(equipmentId);
        return switch (doorCategory) {
            //TODO   OwnDoorDoor 门禁类型20
            //海康门禁
            case 11 -> SpringUtil.getBean(HKDoorCommand.class);
            //纽贝尔门禁
            case 4 -> SpringUtil.getBean(CHD806DoorCommand.class);
            case 12 -> SpringUtil.getBean(NBHttpDoorCommand.class);
            case 15 -> SpringUtil.getBean("CHD200D7DoorCommand", CHD200D7DoorCommand.class);
            case 16 -> SpringUtil.getBean(NBFaceDoorCommand.class);
            case 17 -> SpringUtil.getBean(CHD200D7DoorNewCommand.class);
            default -> SpringUtil.getBean("CommonDoorCommand", CommonDoorCommand.class);
        };
    }
}
