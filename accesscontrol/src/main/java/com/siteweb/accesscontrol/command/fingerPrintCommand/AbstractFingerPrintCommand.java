package com.siteweb.accesscontrol.command.fingerPrintCommand;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.service.CardService;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 2023/2/9新增指纹命令类
 * 解决两门以及四门的门禁对接不同指纹读头控制命令有差异问题
 * 避免以后指纹读头以及门禁控制器排列组合代码出现冗余的问题
 * 只针对指纹读头模板与门禁控制器分开的情况
 * 如果是一体机不使用该类
 * 将指纹机的控制命令串交给指纹机类实现
 * <AUTHOR>
 * @date 2023/11/22
 */
@Slf4j
@Component
public abstract class AbstractFingerPrintCommand {

    /**
     * 默认卡密码
     */
    protected static final String CARD_DEFAULT_PASSWORD = "1111";
    @Autowired
    CardService cardService;

    protected Integer getCardId(Integer cardId){
        return cardId >= 10000 ? cardId % 10000 : cardId;
    }

    protected String getCardCode(Integer doorCardCodeType,AccessCardInfoDTO accessCardInfo){
        return Objects.equals(doorCardCodeType, accessCardInfo.getCardCodeType()) ? accessCardInfo.getCardCode() : accessCardInfo.getCardCodeConv();
    }

    protected String getCardPassword(String cardPassword) {
        return CharSequenceUtil.isBlank(cardPassword) ? CARD_DEFAULT_PASSWORD : cardPassword;
    }
    protected String getUserNo(Integer cardId){
        return CharSequenceUtil.padPre(Integer.toString(cardId), 8, '0');
    }

    /**
     * 添加指纹机用户
     * @param fingerPrintData 指纹机信息
     * @param equipmentDoor 门信息
     * @param accessCardInfo 卡信息
     * @return {@link String}
     */
    public String addFingerReaderUser(ChildEquipment fingerPrintData, EquipmentDoor equipmentDoor, AccessCardInfoDTO accessCardInfo) {
        throw new UnsupportedOperationException("only supported HK Door and Common Door");
    }

    public String addOrUpdateAuthFingerprint(ChildEquipment fingerPrintData, FingerPrint fingerprint, EquipmentDoor equipmentDoor, Integer cardId){
        throw new UnsupportedOperationException("only supported HK Door and Common Door");
    }

    /**
     * 删除指纹授权
     * @param fingerPrintData 指纹机数据
     * @param fingerprint 指纹数据
     * @param equipmentDoor 门禁设备
     * @param cardId 卡id
     * @return {@link String}
     */
    public String deleteAuthFingerprint(ChildEquipment fingerPrintData, FingerPrint fingerprint, EquipmentDoor equipmentDoor, Integer cardId) {
        throw new UnsupportedOperationException("only supported HK Door and Common Door");
    }
    public String RemoteCollectFingerprint(){
        throw new UnsupportedOperationException("RemoteCollectFingerprint ERROR Not Fount Instance");
    }
}
