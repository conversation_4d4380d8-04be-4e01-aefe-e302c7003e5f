package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.command.FingerPrintCommandFactory;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.enums.VendorEnum;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * CHD200D7父子设备合并的控制命令类 通道号39的值为17
 * 因为父子设备合并了，查找不到子设备，部分指纹机相关的控制命令无法使用，故添加一个新的门禁类型与控制命令类
 * <AUTHOR>
 * @date 2025/05/20
 */
@Service
@Slf4j
public class CHD200D7DoorNewCommand extends CHD200D7DoorCommand {
    @Autowired
    private FingerPrintCommandFactory fingerPrintCommandFactory;

    @Override
    public Boolean addFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改用户(纽贝尔指纹读头)
        //控制命令种类35,命令号17
        //8888888888,00000001,10,11,12,0000,009F20A8	   权限密码,用户ID,第一枚指纹编号,第二枚指纹编号,第三枚指纹编号,密码,卡号（仅支持8位卡号）
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类35,命令号117，模板修改，上层软件无需更改
        //20230112 xsx
        //8888888888,00000001,10,11,12,0000,009F20A8,张三 权限密码,用户ID,第一枚指纹编号,第二枚指纹编号,第三枚指纹编号,密码,卡号,用户名(GB2312编码的十六进制字符串)
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (Objects.isNull(equipmentDoor)) {
            log.error("addFingerReaderUser: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfoDTO = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfoDTO)) {
            log.error("AddOrUpdateUser: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer childStationId = equipmentDoor.getStationId();
        Integer childEquipmentId = equipmentDoor.getEquipmentId();
        Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
        String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                  .addFingerReaderUser(null, equipmentDoor, accessCardInfoDTO);
        log.info("addFingerReaderUser: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId,
                childEquipmentId, commandId, command);
        activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);

        return Boolean.TRUE;
    }

    @Override
    protected Boolean clearFingerReader(Integer userId, Integer equipmentId) {
        //删除所有指纹（纽贝尔指纹机）
        //控制命令种类41,命令号19
        //8888888888,0	   权限密码,0
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("ClearFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer childStationId = equipmentDoor.getStationId();
        Integer childEquipmentId = equipmentDoor.getEquipmentId();
        Integer clearFingerCommandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
        String clearFingerCommand = String.format("%s,0", FINGERPRINT_DEFAULT_PASSWORD);
        log.info("ClearFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, clearFingerCommandId, clearFingerCommand);
        activeControlManager.sendControl(childStationId, childEquipmentId, clearFingerCommandId, clearFingerCommand, userId);

        return Boolean.TRUE;
    }

    @Override
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改指纹(纽贝尔）
        //控制命令种类40,命令号18
        //8888888888,10,1302,1	   权限密码,指纹标志,指纹长度,指纹指纹信息ID
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类40,命令号118，模板修改，上层软件无需更改
        //新的命令格式修改为： 8888888888,100,123,009F20A8,张三,1302,22     权限密码,【卡Id】【指纹编号】,用户ID,卡号,用户名,指纹长度,指纹指纹信息ID
        List<FingerPrint> fingerPrintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            log.info("AddOrUpdateFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addOrUpdateAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer childEquipmentId = equipmentDoor.getEquipmentId();
        Integer childStationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SET_FINGERPRINT.getCategory());
        for (FingerPrint fingerPrint : fingerPrintList) {
            String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                      .addOrUpdateAuthFingerprint(null, fingerPrint, equipmentDoor, cardId);
            log.info("SetFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        //添加指纹授权到数据库
        fingerprintAuthService.saveAuthFingerPrint(equipmentDoor.getStationId(), equipmentId, cardId, fingerPrintList.get(0).getFingerPrintId(), VendorEnum.NEWBEL.getVendorId());
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //删除指纹（纽贝尔指纹机）
        //控制命令种类41,命令号19
        //8888888888,11,00000001	   权限密码,[卡id][指纹编号],用户id(8位) （指纹标志为卡ID和指纹编号拼接，例子中卡ID为1，指纹号为1,用户id为1）
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类41,命令号119，模板修改，上层软件无需更改
        List<FingerPrint> fingerPrintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            log.info("deleteAuthFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("deleteAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("deleteAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer childEquipmentId = equipmentDoor.getEquipmentId();
        Integer childStationId = equipmentDoor.getStationId();
        Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
        for (FingerPrint fingerPrint : fingerPrintList) {
            String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                      .deleteAuthFingerprint(null, fingerPrint, equipmentDoor, cardId);
            log.info("deleteAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        //删除指纹到数据库
        fingerprintAuthService.deleteFingerprintAuth(equipmentDoor.getStationId(), equipmentId, cardId, fingerPrintList.get(0).getFingerPrintId(), VendorEnum.NEWBEL.getVendorId());
        return Boolean.TRUE;
    }

    @Override
    public Boolean delFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
        //删除用户(纽贝尔指纹读头)
        //控制命令种类36,命令号20
        //8888888888,10	   权限密码,用户ID
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delFingerReaderUser: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer childStationId = equipmentDoor.getStationId();
        Integer childEquipmentId = equipmentDoor.getEquipmentId();
        Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.DELETE_USER.getCategory());
        Integer id = getCardId(cardId);
        String userNo = getUserNo(id);
        String command = String.format("%s,%s", FINGERPRINT_DEFAULT_PASSWORD, userNo);
        log.info("delFingerReaderUser: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
        activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        //采集指纹（纽贝尔）
        //控制命令种类38,命令号60
        //8888888888,0,1,009F20A8	   权限密码,指纹编号,卡Id,卡号
        //20230112 xsx
        //针对纽贝尔CHD805的指纹读头，模板调整控制命令为：控制命令种类38,命令号160，模板修改，上层软件无需更改
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("remoteCollectFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        Integer id = getCardId(cardId);
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(), accessCardInfo.getCardCodeConv());
        Integer childStationId = equipmentDoor.getStationId();
        Integer childEquipmentId = equipmentDoor.getEquipmentId();
        Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SAMPLE_FINGERPRINT.getCategory());
        String command = String.format("%s,%s,%s,%s", FINGERPRINT_DEFAULT_PASSWORD, fingerNo, id, cardCode);
        log.info("remoteCollectFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
        activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }
}
