package com.siteweb.accesscontrol.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Tuple;
import com.siteweb.accesscontrol.command.DoorCommandFactory;
import com.siteweb.accesscontrol.command.doorCommand.AbstractDoorCommand;
import com.siteweb.accesscontrol.dto.DoorCardLostDTO;
import com.siteweb.accesscontrol.enums.CardStatusEnum;
import com.siteweb.accesscontrol.enums.FeatureEnum;
import com.siteweb.accesscontrol.function.BooleanConsumer;
import com.siteweb.accesscontrol.service.*;
import com.siteweb.accesscontrol.vo.DoorParamVO;
import com.siteweb.monitoring.mamager.ActiveControlManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BooleanSupplier;

@Slf4j
@Component
public class DoorCommandManager {
    @Autowired
    private DoorCommandFactory doorCommandFactory;
    @Autowired
    private DoorCardService doorCardService;
    @Autowired
    private DoorCardLostService doorCardLostService;
    @Autowired
    private DoorService doorService;
    @Autowired
    @Lazy
    private CardService cardService;
    @Autowired
    private ActiveControlManager activeControlManager;
    @Autowired
    private DoorTimeGroupService doorTimeGroupService;
    /**
     * 挂失卡
     * @param userId 操作用户id
     * @param cardId 门禁卡id
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reportCardLoss(Integer userId, Integer cardId) {
        //修改卡的状态
        cardService.updateCardStatus(cardId, CardStatusEnum.LOST.getStatus());
        //添加挂失记录
        doorCardLostService.createLostRecord(cardId);
        //删除指定卡的所有授权
        this.deleteCardAllAuth(userId, cardId);
        return true;
    }


    /**
     * 注销卡
     * @param userId 用户id
     * @param cardId 卡id
     * @return {@link Boolean}
     */
    public boolean unRegisterCard(Integer userId, Integer cardId) {
        //修改卡的状态
        cardService.updateCardStatus(cardId, CardStatusEnum.CANCEL.getStatus());
        //删除指定卡的所有授权
        return this.deleteCardAllAuth(userId, cardId);
    }


    /**
     * 使用卡
     * @param userId 用户id
     * @param cardId 卡id
     * @return {@link Boolean}
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean useCard(Integer userId, Integer cardId) {
        boolean result = true;
        //修改卡的状态
        cardService.updateCardStatus(cardId, CardStatusEnum.USING.getStatus());
        //查询门卡挂失记录
        List<DoorCardLostDTO> doorCardLost = doorCardLostService.findDoorCardLost(cardId);
        List<Tuple> infoList = doorCardLost.stream().map(dto -> new Tuple(dto.getEquipmentId(), dto.getTimeGroupType())).toList();
        for (Tuple tuple : infoList) {
            AbstractDoorCommand doorCommand = doorCommandFactory.getCommandInstance(tuple.get(0));
            if (!ignoreUnsupportedOperationExceptionExecute(() -> doorCommand.addManualDoorCard(userId, tuple.get(0), tuple.get(1), cardId))) {
                result = false;
            }
        }
        List<Integer> equipmentIds = doorCardLost.stream().map(DoorCardLostDTO::getEquipmentId).toList();
        for (Integer equipmentId : equipmentIds) {
            AbstractDoorCommand doorCommand = doorCommandFactory.getCommandInstance(equipmentId);
            if (!ignoreUnsupportedOperationExceptionExecute(() -> doorCommand.addOrUpdateAuthFace(userId, equipmentId, cardId))) {
                result = false;
            }
            if (!ignoreUnsupportedOperationExceptionExecute(() -> doorCommand.addOrUpdateAuthFingerprint(userId, equipmentId, cardId))) {
                result = false;
            }
        }
        //删除挂失记录
        doorCardLostService.deleteLostRecord(cardId);
        return result;
    }


    /**
     * 删除指定卡的所有授权
     * @param userId 用户id
     * @param cardId 卡id
     * @return {@link Boolean}
     */
    public boolean deleteCardAllAuth(Integer userId, Integer cardId) {
        boolean result = true;
        List<Integer> equipmentIds = doorService.findCardAuthEquipment(cardId);
        if (CollUtil.isEmpty(equipmentIds)) {
            return true;
        }
        for (Integer equipmentId : equipmentIds) {
            if (!delManualDoorCard(userId, equipmentId, cardId)){
                result = false;
            }
        }
        return result;
    }

    /**
     * 删除门上指定卡授权
     * @param userId 操作用户id
     * @param equipmentId 设备id
     * @param cardId 门禁卡id
     * @return {@link boolean}
     */
    public boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        return this.delManualDoorCard(userId, equipmentId, cardId, 7);
    }

    /**
     * 删除门上指定卡授权
     * @param userId 操作用户id
     * @param equipmentId 设备id
     * @param cardId 门禁卡id
     * @param flag 按位与运算标志位 1卡 2指纹 4人脸
     * @return {@link Boolean}
     */
    public boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId, Integer flag) {
        boolean deleteCard = (flag & FeatureEnum.CARD.getType()) > 0;
        boolean deleteFace = (flag & FeatureEnum.FACE.getType()) > 0;
        boolean deleteFingerprint = (flag &FeatureEnum.FINGERPRINT.getType()) > 0;
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        //删人脸
        if (deleteFace && !ignoreUnsupportedOperationExceptionExecute(() -> abstractDoor.deleteAuthFace(userId, equipmentId, cardId))) {
            return false;
        }
        //删指纹
        if (deleteFingerprint && !ignoreUnsupportedOperationExceptionExecute(() -> abstractDoor.deleteAuthFingerprint(userId,equipmentId,cardId))) {
            return false;
        }
        //删卡
        if (deleteCard && !ignoreUnsupportedOperationExceptionExecute(() -> abstractDoor.delManualDoorCard(userId, equipmentId, cardId))) {
            return false;
        }
        return true;
    }


    /**
     * 修改门上指定的已授权卡
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @param timeGroupNo 时间组编号
     * @param cardId      卡id
     * @param validTime   有效时间
     * @return boolean
     */
    public boolean editManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId, Date validTime) {
        return doorCommandFactory.getCommandInstance(equipmentId).editManualDoorCard(userId, equipmentId, timeGroupNo, cardId, validTime);
    }



    /**
     * 授权指定卡到门
     * @param userId          用户id
     * @param equipmentIdList 设备id列表
     * @param timeGroupId  准进时间组id
     * @param cardIdList      卡id列表
     * @param flag            授权标识
     * @return {@link Boolean}
     */
    public boolean addManualDoorCard(Integer userId, List<Integer> equipmentIdList, Integer timeGroupId, List<Integer> cardIdList, Integer flag) {
        Map<Integer, List<Integer>> timeGroupMap = new HashMap<>();
        boolean result = true;
        boolean addCard = (flag & FeatureEnum.CARD.getType()) > 0;
        boolean addFace = (flag & FeatureEnum.FACE.getType()) > 0;
        boolean addFingerprint = (flag & FeatureEnum.FINGERPRINT.getType()) > 0;
        for (Integer cardId : cardIdList) {
            for (Integer equipmentId : equipmentIdList) {
                AbstractDoorCommand abstractDoorCommand = doorCommandFactory.getCommandInstance(equipmentId);
                List<Integer> timeGroupNoList = timeGroupMap.computeIfAbsent(equipmentId, key -> doorTimeGroupService.findTimeGroupNo(equipmentId, timeGroupId));
                //加卡
                if (addCard) {
                    for (Integer timeGroupNo : timeGroupNoList) {
                        if (!ignoreUnsupportedOperationExceptionExecute(() -> abstractDoorCommand.addManualDoorCard(userId, equipmentId, timeGroupNo, cardId))) {
                            result = false;
                        }
                    }
                }
                //加人脸
                if (addFace && !ignoreUnsupportedOperationExceptionExecute(() -> abstractDoorCommand.addOrUpdateAuthFace(userId, equipmentId, cardId))) {
                    result = false;
                }
                //加指纹
                if (addFingerprint && !ignoreUnsupportedOperationExceptionExecute(() -> abstractDoorCommand.addOrUpdateAuthFingerprint(userId, equipmentId, cardId))) {
                    result = false;
                }
            }
        }
        return result;
    }


    /**
     *  执行忽略UnsupportedOperationException(由于部分门禁设备没有部分命令)
     *
     * @param supplier 供应商
     * @return boolean
     */
    private boolean ignoreUnsupportedOperationExceptionExecute(BooleanSupplier supplier)
    {
        try
        {
            return supplier.getAsBoolean();
        }
        catch (UnsupportedOperationException ex) {
            log.error("Door Command UnsupportedOperation----{}", ex.getMessage());
        }
        catch (Exception ex)
        {
            log.error("call command error----",ex);
            return false;
        }
        return true;
    }

    /**
     * 设置门参数通用函数方法
     * @param doorParamVOList
     * @param applyAsBoolean
     * @return {@link Boolean}
     */
    private boolean setDoorParam(List<DoorParamVO> doorParamVOList, BooleanConsumer<DoorParamVO> applyAsBoolean) {
        boolean result = true;
        for (DoorParamVO vo : doorParamVOList) {
            if (!applyAsBoolean.applyAsBoolean(vo)) {
                result = false;
            }
        }
        return result;
    }



    /**
     * 设置门准进时间组
     *
     * @param userId              用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean setTimeGroup(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setDoorTimeGroup(userId, vo.getEquipmentId(), vo.getAddTimeGroupIdList(), vo.getDelTimeGroupIdList()));
    }

    /**
     * 设置非法卡刷卡间隔
     *
     * @param userId          用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    public Boolean setSlotInterval(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setSlotInterval(userId, vo.getEquipmentId(), vo.getSlotInterval()));
    }

    /**
     * 设置门延迟
     * @param userId 用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    public Boolean setDoorOpenDelay(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setDoorOpenDelay(userId, vo.getEquipmentId(), vo.getOpenDelay()));
    }

    /**
     * 设置门密码
     * @param userId 用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    public Boolean setDoorPassword(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setDoorPassword(userId, vo.getEquipmentId(), vo.getPassword()));
    }

    /**
     * 设置火警信号
     *
     * @param userId              用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    public Boolean setFireSignal(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setFireSignal(userId, vo.getEquipmentId(), vo.getFireSignal()));
    }



    /**
     * 设置卡封锁错误次数
     * @param userId          用户id
     * @param doorParamVOList 卡封锁次数
     * @return {@link Boolean}
     */
    public Boolean setLockErrorCount(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setLockErrorCount(userId, vo.getEquipmentId(), vo.getLockErrorCount()));
    }

    /**
     * 设置卡封锁时间
     * @param userId          用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean} 是否成功
     */
    public Boolean setLockTime(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setLockTime(userId, vo.getEquipmentId(), vo.getLockTime()));
    }

    /**
     * 设置开门保持时间
     *
     * @param userId          用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    public Boolean setKeepTime(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setKeepTime(userId, vo.getEquipmentId(), vo.getKeepTime()));
    }

    /**
     * 设置开门方式
     *
     * @param userId          用户id
     * @param doorParamVOList 门参数
     * @return {@link Boolean}
     */
    public Boolean setOpenMode(Integer userId, List<DoorParamVO> doorParamVOList) {
        return this.setDoorParam(doorParamVOList, vo -> doorCommandFactory.getCommandInstance(vo.getEquipmentId())
                                                                          .setOpenMode(userId, vo.getEquipmentId(), vo.getOpenMode()));
    }


    /**
     * 设置门常开
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean setNormallyOpen(Integer userId, Integer equipmentId) {
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        return abstractDoor.setNormallyOpen(userId,equipmentId);
    }


    /**
     * 设置门常关
     *
     * @param userId      用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean setNormallyClose(Integer userId, Integer equipmentId) {
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        return abstractDoor.setNormallyClose(userId,equipmentId);
    }

    /**
     * 门禁校时
     * @param userId 用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean setTime(Integer userId, Integer equipmentId) {
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        return abstractDoor.setTime(userId,equipmentId,"");
    }

    /**
     * 开门
     * @param userId 登录用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean open(Integer userId, Integer equipmentId) {
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        return abstractDoor.open(userId,equipmentId);
    }

    /**
     * 关门
     *
     * @param userId 登录用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean close(Integer userId, Integer equipmentId) {
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        return abstractDoor.close(userId,equipmentId);
    }

    /**
     * 清除卡片
     *
     * @param userId 登录用户id
     * @param equipmentId 设备id
     * @return {@link Boolean}
     */
    public Boolean clearCard(Integer userId, Integer equipmentId) {
        AbstractDoorCommand abstractDoor = doorCommandFactory.getCommandInstance(equipmentId);
        return abstractDoor.clearCard(userId, equipmentId);
    }

    /**
     * 确认门命令
     * @param userId       用户id
     * @param serialNoList 流水号集合
     * @return {@link Boolean}
     */
    public boolean confirmDoorCommand(Integer userId, List<Integer> serialNoList) {
        if (CollUtil.isEmpty(serialNoList)) {
            return true;
        }
        boolean result = true;
        for (Integer serialNo : serialNoList) {
            if (activeControlManager.confirmControlCommandBySeq(userId, serialNo) != 0) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 重新发送门命令
     * @param userId       用户id
     * @param serialNoList 流水号集合
     * @return {@link Boolean}
     */
    public Boolean resendDoorCommand(Integer userId, List<Integer> serialNoList) {
        if (CollUtil.isEmpty(serialNoList)) {
            return true;
        }
        boolean result = true;
        for (Integer serialNo : serialNoList) {
            if (activeControlManager.reSendControlCommand(userId, serialNo) != 0) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 远程采集指纹
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡id
     * @param fingerNo 指纹编号
     * @return boolean
     */
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        AbstractDoorCommand commandInstance = doorCommandFactory.getCommandInstance(equipmentId);
        return commandInstance.remoteCollectFingerprint(userId, equipmentId, cardId, fingerNo);
    }

    /**
     * 远程采集人脸
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param cardId 卡id
     * @return boolean 是否成功
     */
    public boolean remoteCollectFace(Integer userId, Integer equipmentId, Integer cardId) {
        AbstractDoorCommand commandInstance = doorCommandFactory.getCommandInstance(equipmentId);
        return commandInstance.remoteCollectFace(userId, equipmentId, cardId);
    }
}