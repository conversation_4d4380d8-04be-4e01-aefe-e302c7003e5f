package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("tbl_door")
public class Door {
    /**
     * 门id
     */
    @TableId(type = IdType.INPUT)
    private Integer doorId;
     /**
     * 门编号
     */
    private Integer doorNo;
    /**
     * 门名称
     */
    private String doorName;
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 采集单元id
     */
    private Integer samplerUnitId;
    /**
     * 类别
     */
    private Integer category;
    /**
     * 地址
     */
    private String address;
    /**
     * 工作模式
     */
    private Integer workMode;
    private Integer infrared;
    /**
     * 密码
     */
    private String password;
    /**
     * 门控制id
     */
    private Integer doorControlId;
    /**
     * 开门间隔
     */
    private Integer doorInterval;
    /**
     * 开门延迟
     */
    private Integer openDelay;
    /**
     * 描述
     */
    private String description;
    /**
     * 开门模式
     */
    private Integer openMode;
}
