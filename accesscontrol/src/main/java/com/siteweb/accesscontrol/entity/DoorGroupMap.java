package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("tbl_doorgroupmap")
public class DoorGroupMap {
    public DoorGroupMap(Integer doorGroupId, Integer equipmentId) {
        this.doorGroupId = doorGroupId;
        this.equipmentId = equipmentId;
    }

    /**
     * 门分组id
     */
    private Integer doorGroupId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 最新更新时间
     */
    private Date lastTime;
}
