package com.siteweb.accesscontrol.entity;

import lombok.Data;

import java.util.Date;

/**
 * 刷卡记录dto
 * <AUTHOR>
 * @date 2022/09/24
 */
@Data
public class SwapCardRecordDTO {
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 卡名称
     */
    private String cardName;
    /**
     * 持卡人id
     */
    private Integer cardUserId;
    /**
     * 卡分组名
     */
    private String cardGroupName;
    /**
     * 卡状态名
     */
    private String cardStatusName;
    /**
     * 门编号
     */
    private Integer doorNo;
    /**
     * 有效标志位
     */
    private Integer valid;
    /**
     * 有效名称
     */
    private String validName;
    /**
     * 进门\出门
     */
    private String enter;
    /**
     * 刷卡时间
     */
    private Date recordTime;
    /**
     * 区域名称
     */
    private String areaName;
}
