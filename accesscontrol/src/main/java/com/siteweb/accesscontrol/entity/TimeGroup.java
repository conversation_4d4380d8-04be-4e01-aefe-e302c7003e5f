package com.siteweb.accesscontrol.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("tbl_timegroup")
public class TimeGroup {
    public TimeGroup(Integer timeGroupId, String timeGroupName) {
        this.timeGroupId = timeGroupId;
        this.timeGroupName = timeGroupName;
        //下面几个常量是存储过程PAC_SaveTimeGroup 中写死的 固与存储过程保持一致
        this.timeGroupCategory = 1;
        this.timeGroupType = 10;
        this.timeGroupException = Boolean.FALSE;
        this.startTime = DateUtil.parse("2000-01-01 00:00:00");
        this.endTime = DateUtil.parse("9999-01-01 00:00:00");
        this.lastUpdateDate = new Date();
    }

    /**
     * 时间组id
     */
    @TableId(type = IdType.INPUT)
    private Integer timeGroupId;

    /**
     * 时间组类别
     */
    private Integer timeGroupCategory;

    /**
     * 时间组名称
     */
    private String timeGroupName;
    /**
     * 时间组类型
     */
    private Integer timeGroupType;
    /**
     * 是否异常
     */
    private Boolean timeGroupException;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 最新更新时间
     */
    private Date lastUpdateDate;
}
