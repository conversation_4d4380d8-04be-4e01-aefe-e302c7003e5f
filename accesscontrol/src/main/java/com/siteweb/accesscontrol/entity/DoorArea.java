package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("tbl_doorarea")
@Data
public class DoorArea {
    /**
     * 门区域ID
     */
    @TableId(type = IdType.INPUT)
    private Integer areaId;
    /**
     * 门区域名称
     */
    private String areaName;
    /**
     * 父门区域ID
     */
    private Integer parentId;
    /**
     * 描述
     */
    private String description;
}
