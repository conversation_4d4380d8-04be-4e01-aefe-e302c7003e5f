package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("tbl_cardtypemap")
public class CardTypeMap {
    /**
     * 卡id
     */
    @TableId(type = IdType.INPUT)
    private Integer cardId;
    /**
     * 卡类型 ID卡\IC卡
     */
    private Integer cardType;
}
