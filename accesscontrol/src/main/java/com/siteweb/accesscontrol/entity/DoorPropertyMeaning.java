package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门相关属性含义表,用于记录不同类型门对应属性值含义,属性类型EntryId=157
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_doorpropertymeaning")
public class DoorPropertyMeaning {

    /**
     * 门类型
     */
    private Integer category;

    /**
     * 属性类型
     */
    private Integer propertyType;

    /**
     * 属性ID
     */
    private Integer propertyId;

    /**
     * 属性含义
     */
    private String meaning;
}

