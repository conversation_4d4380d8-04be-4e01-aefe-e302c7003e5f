package com.siteweb.accesscontrol.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_facedatamap")
public class FaceDataMap {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 人脸id
     */
    private Integer faceId;
    /**
     * 最新的更新人
     */
    private Integer lastUpdater;
    /**
     * 最新更新时间
     */
    private Date lastUpdateTime;
}
