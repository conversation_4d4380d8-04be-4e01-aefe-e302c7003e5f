package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tbl_doorgroup")
public class DoorGroup {
    /**
     * 门分组id
     */
    @TableId(type = IdType.INPUT)
    private Integer doorGroupId;
    /**
     * 门分组名称
     */
    private String doorGroupName;
    /**
     * 描述
     */
    private String description;
    /**
     * 最新更新时间
     */
    private Date lastTime;
}
