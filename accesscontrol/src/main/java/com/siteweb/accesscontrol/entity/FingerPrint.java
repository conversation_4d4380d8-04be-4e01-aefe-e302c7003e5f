package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("tbl_fingerprint")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class FingerPrint {
    /**
     * 指纹id
     */
    private Integer fingerPrintId;
    /**
     * 指纹编号
     */
    private Integer fingerPrintNo;
    /**
     * 指纹数据
     */
    private byte[] fingerPrintData;
}
