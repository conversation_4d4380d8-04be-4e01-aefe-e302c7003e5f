package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.accesscontrol.vo.AccessCardVO;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("tbl_cardext")
@NoArgsConstructor
@Data
public class CardExt {
    /**
     * 卡id
     */
    @TableId(type = IdType.INPUT)
    private Integer cardId;
    /**
     * 卡密码
     */
    private String password;
    /**
     * 卡号类型 十进制\十六进制
     */
    private Integer cardCodeType;
    /**
     * 卡号转换(tbl_card.cardcode的十进制或十六进制)
     * 门与卡的类型是否一致，一致则直接取卡号，否则取卡号转换,(卡号转换主要用于容错)
     */
    private String cardCodeConv;

    public CardExt(AccessCardVO accessCardVO) {
        this.cardId = accessCardVO.getCardId();
        this.password = accessCardVO.getPassword();
        this.cardCodeType = accessCardVO.getCardCodeType();
        this.cardCodeConv = accessCardVO.getCardCodeConv();
    }
}
