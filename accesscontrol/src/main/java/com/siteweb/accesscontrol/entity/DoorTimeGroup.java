package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("tbl_doortimegroup")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DoorTimeGroup {
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 准进时间组id
     */
    private Integer timeGroupId;
    /**
     * 准进时间组类型
     */
    private Integer timeGroupType;
}
