package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tbl_facedataauth")
public class faceDataAuth {
    /**
     * 准近组id
     */
    private Integer timeGroupId;
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 人脸id
     */
    private Integer faceId;
    /**
     * 最新的更新时间
     */
    private Date lastUpdateTime;
}
