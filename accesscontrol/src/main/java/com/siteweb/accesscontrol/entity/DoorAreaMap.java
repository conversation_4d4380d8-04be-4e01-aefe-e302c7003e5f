package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("tbl_doorareamap")
@NoArgsConstructor
@AllArgsConstructor
public class DoorAreaMap {
    /**
     * 门区域ID
     */
    private Integer areaId;
    /**
     * 门设备ID
     */
    @TableId(type = IdType.INPUT)
    private Integer equipmentId;
}
