package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("DoorTagMap")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DoorTagMap {
    public DoorTagMap(Integer equipmentId, Integer tagId) {
        this.equipmentId = equipmentId;
        this.tagId = tagId;
    }

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 设备主键id
     */
    private Integer equipmentId;
    /**
     * 标签主键id
     */
    private Integer tagId;
}
