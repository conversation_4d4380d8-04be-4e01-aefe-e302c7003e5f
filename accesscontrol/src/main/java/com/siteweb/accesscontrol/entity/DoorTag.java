package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门标签实体类
 *
 * <AUTHOR>
 * @date 2024/06/15
 */
@TableName("DoorTag")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DoorTag {
    /**
     * 标签主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer tagId;
    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 标签图标
     */
    private String tagIcon;
    /**
     * 标签颜色
     */
    private String tagColor;
    /**
     * 标签描述
     */
    private String tagDescribe;
}
