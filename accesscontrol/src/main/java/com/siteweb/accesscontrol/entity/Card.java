package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("tbl_card")
@NoArgsConstructor
@Data
public class Card {
    /**
     * 卡主键id
     */
    @TableId(type = IdType.INPUT)
    private Integer cardId;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 卡名称
     */
    private String cardName;
    /**
     * 卡类型
     */
    private Integer cardCategory;
    /**
     * 卡分组
     */
    private Integer cardGroup;
    /**
     * 持卡人
     */
    private Integer userId;
    /**
     * 持卡人
     */
    private Integer stationId;
    /**
     * 卡状态
     */
    private Integer cardStatus;
    /**
     * 有效开始时间
     */
    private Date startTime;
    /**
     * 有效结束时间
     */
    private Date endTime;
    /**
     * 注册时间
     */
    private Date registerTime;
    /**
     * 注销时间
     */
    private Date unRegisterTime;
    /**
     * 挂失时间
     */
    private Date lostTime;
    /**
     * 描述
     */
    private String description;
}
