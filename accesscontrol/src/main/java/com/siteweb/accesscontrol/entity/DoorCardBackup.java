package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.accesscontrol.enums.DoorCardTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DoorCardBackup 实体类
 * 对应数据库表: DoorCardBackup
 * 门删除了备份卡  卡删除了备份门
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("DoorCardBackup")
public class DoorCardBackup {
    /**
     * DoorCardBackupId
     * 设置主键自增
     */
    @TableId(type = IdType.AUTO)
    private Integer doorCardBackupId;

    /**
     * Type
     * 类型 1设备 2卡
     */
    private Integer type;

    /**
     * Id
     * 设备或者卡的id
     */
    private Integer id;

    /**
     * 被删除的门或者卡名称
     * 注意:与类型是相反的，被删除的类型是门，则这里是卡名称，与之相反
     */
    private String deleteName;
    /**
     * 被删除的门或者卡Id
     */
    private Integer deleteId;
    /**
     * DeleteTime
     * 被删除的时间
     */
    private Date deleteTime;

    public static List<DoorCardBackup> backupDoorEntity(List<Integer> equipmentIds, Integer cardId, String cardName) {
        Date now = new Date();
        List<DoorCardBackup> doorCardBackupList = new ArrayList<>();
        for (Integer equipmentId : equipmentIds) {
            DoorCardBackup doorCardBackup = new DoorCardBackup();
            doorCardBackup.setType(DoorCardTypeEnum.DOOR.getValue());
            doorCardBackup.setId(equipmentId);
            doorCardBackup.setDeleteId(cardId);
            doorCardBackup.setDeleteName(cardName);
            doorCardBackup.setDeleteTime(now);
            doorCardBackupList.add(doorCardBackup);
        }
        return doorCardBackupList;
    }
}
