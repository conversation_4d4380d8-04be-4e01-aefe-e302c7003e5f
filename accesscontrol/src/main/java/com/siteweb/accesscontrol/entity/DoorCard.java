package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("tbl_doorcard")
@Data
public class DoorCard {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 校准时间组id
     */
    private Integer timeGroupId;
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 密码
     */
    private String password;
    /**
     * 校准时间组类型
     */
    private Integer timeGroupType;
}
