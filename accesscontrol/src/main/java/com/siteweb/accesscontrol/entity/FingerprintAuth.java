package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("tbl_fingerprintauth")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class FingerprintAuth {
    /**
     * 校准时间组id
     */
    private Integer timeGroupId;
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 指纹id
     */
    private Integer fingerPrintId;
    /**
     * 最新更新时间
     */
    private Date lastUpdateTime;
}
