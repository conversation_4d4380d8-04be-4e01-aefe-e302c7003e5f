package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_fingerprintcardmap")
public class FingerPrintCardMap {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 指纹id
     */
    private Integer fingerPrintId;
    /**
     * 供应商id
     */
    private Integer vendor;
    /**
     * 最新更新人
     */
    private Integer lastUpdater;
    /**
     * 最新更新时间
     */
    private Date lastUpdateTime;
}
