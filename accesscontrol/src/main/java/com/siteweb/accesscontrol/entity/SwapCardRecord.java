package com.siteweb.accesscontrol.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("tbl_swapcardrecord")
@Data
public class SwapCardRecord {
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 局站名字
     */
    private String stationName;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 卡局站id
     */
    private Integer cardStationId;
    /**
     * 卡局站名字
     */
    private String cardStationName;
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 卡名字
     */
    private String cardName;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 卡id
     */
    private Integer cardUserId;
    /**
     * 卡用户名
     */
    private String cardUserName;
    /**
     * 卡类型
     */
    private Integer cardCategory;
    /**
     * 卡类型名称
     */
    private String cardCategoryName;
    /**
     * 卡分组
     */
    private Integer cardGroup;
    /**
     * 卡分组名称
     */
    private String cardGroupName;
    /**
     * 卡状态
     */
    private Integer cardStatus;
    /**
     * 卡状态名称
     */
    private String cardStatusName;
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 门名字
     */
    private String doorName;
    /**
     * 门编号
     */
    private Integer doorNo;
    /**
     * 门类别
     */
    private Integer doorCategory;
    /**
     * 门类别名称
     */
    private String doorCategoryName;
    /**
     * 有效有效标志位
     */
    private Integer valid;
    /**
     * 有效名称
     */
    private String validName;
    /**
     * 含义
     */
    private Integer enter;
    /**
     * 记录时间
     */
    private Date recordTime;
}
