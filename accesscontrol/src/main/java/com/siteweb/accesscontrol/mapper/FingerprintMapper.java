package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;

import java.util.List;

public interface FingerprintMapper extends BaseMapper<FingerPrint> {
    /**
     * 通过卡id与供应商id找到指纹信息
     *
     * @param cardId   卡id
     * @param vendorId 供应商id
     * @return {@link FingerPrint}
     */
    List<FingerPrint> findCardFingerprintByVendor(Integer cardId, Integer vendorId);

    /**
     * 获取子设备指纹仪
     *
     * @param equipmentId 设备id
     * @param vendorId    供应商id
     * @return {@link ChildEquipment}
     */
    List<ChildEquipment> findChildEquipment(Integer equipmentId,Integer equipmentCategory, Integer vendorId);

    Integer findFingerPrintCategoryByEquipmentId(Integer equipmentId);

    List<FingerPrint> findFingerprintData(Integer cardId, Integer vendor);

    int deleteByCardId(Integer cardId);

    Integer findFingerprintIdByCardId(Integer cardId, Integer vendor);

    Integer findCardFingerprintIdByVendor(Integer cardId, Integer vendorId);
}
