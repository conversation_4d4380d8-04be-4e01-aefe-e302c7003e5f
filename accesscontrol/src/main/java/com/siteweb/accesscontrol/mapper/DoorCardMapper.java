package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.DoorCardInfoDTO;
import com.siteweb.accesscontrol.entity.DoorCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DoorCardMapper extends BaseMapper<DoorCard> {
    List<DoorCardInfoDTO> findDoorCardInfoByCardIds(@Param("cardIds") List<Integer> cardIds);

    List<Integer> findEquipmentIdByCardId(@Param("cardId") Integer cardId);
}
