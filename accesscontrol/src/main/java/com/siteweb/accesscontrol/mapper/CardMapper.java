package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.AccessCardDTO;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.entity.Card;

import java.util.Collection;
import java.util.List;

public interface CardMapper extends BaseMapper<Card> {
    Integer countByCondition(Integer cardId, String cardCode, Integer cardCodeType);

    AccessCardDTO findById(Integer id);

    List<AccessCardDTO> findCardByCardGroup(Integer cardGroup, Collection<Integer> employeeIds);

    AccessCardInfoDTO findInfoByCardId(Integer cardId);
}
