package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.entity.Card;
import com.siteweb.accesscontrol.vo.AccessCardAuthorizationVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface CardAuthorizationServiceMapper extends BaseMapper<Card> {
    Page<AccessCardAuthorizationVO> findCardAuthorizationPage(@Param("page") Page page, @Param("cardCode") String cardCode, @Param("equipmentId") Integer equipmentId, @Param("cardGroup") Integer cardGroup, @Param("areaId") Integer areaId, @Param("userId") Integer userId, @Param("stationName") String stationName, @Param("employeeIds") Collection<Integer> employeeIds, @Param("equipmentIds") Set<Integer> equipmentIds);

    List<AccessCardAuthorizationVO> findFingerPrintAuthorizationInfo(@Param("records") List<AccessCardAuthorizationVO> records);

    List<AccessCardAuthorizationVO> findFacePrintAuthorizationInfo(@Param("records") List<AccessCardAuthorizationVO> records);

    Long findCardAuthorizationCount(@Param("cardCode") String cardCode, @Param("equipmentId") Integer equipmentId, @Param("cardGroup") Integer cardGroup, @Param("areaId") Integer areaId, @Param("userId") Integer userId, @Param("stationName") String stationName, @Param("employeeIds") Set<Integer> employeeIds, @Param("equipmentIds") Set<Integer> equipmentIds);
}
