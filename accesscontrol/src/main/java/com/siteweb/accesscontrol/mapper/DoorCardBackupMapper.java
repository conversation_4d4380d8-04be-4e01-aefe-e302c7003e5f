package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.entity.DoorCardBackup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DoorCardBackupMapper extends BaseMapper<DoorCardBackup> {
    int batchInsert(@Param("backupList") List<DoorCardBackup> backupList);
}
