package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.entity.FaceDataMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FaceDataMapMapper extends BaseMapper<FaceDataMap> {
    Integer findFaceIdByCardId(Integer cardId);

    void batchInsert(@Param("faceDataMapList") List<FaceDataMap> faceDataMapList);
}
