package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.PersonDTO;
import com.siteweb.admin.entity.Employee;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper
public interface PersonMapper extends BaseMapper<Employee> {
    List<PersonDTO> findPersons(Collection<Integer> departmentIds);
}
