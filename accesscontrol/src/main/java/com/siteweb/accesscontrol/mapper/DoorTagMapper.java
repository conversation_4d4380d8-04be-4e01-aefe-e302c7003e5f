package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.entity.DoorTag;
import com.siteweb.accesscontrol.vo.DoorTagVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DoorTagMapper extends BaseMapper<DoorTag> {
    List<DoorTagVO> findDoorTagByEquipmentIds(List<Integer> doorEquipmentIds);
}
