package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.DoorAreaEquipmentDTO;
import com.siteweb.accesscontrol.entity.DoorArea;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface DoorAreaMapper extends BaseMapper<DoorArea> {
    /**
     * 获取门禁设备相关属性
     * @return {@link List}<{@link DoorAreaEquipmentDTO}>
     */
    List<DoorAreaEquipmentDTO> findDoorAreaEquipment(@Param("equipmentIds") Collection<Integer> equipmentIds);
}
