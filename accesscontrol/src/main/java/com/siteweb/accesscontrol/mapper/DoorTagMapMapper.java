package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.entity.DoorTagMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DoorTagMapMapper extends BaseMapper<DoorTagMap> {
    int batchInsert(@Param("doorTagMapList") List<DoorTagMap> doorTagMapList);
}
