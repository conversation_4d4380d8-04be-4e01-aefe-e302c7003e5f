package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.entity.SwapCardRecord;
import com.siteweb.accesscontrol.entity.SwapCardRecordDTO;

import java.util.Date;

public interface SwapCardRecordMapper extends BaseMapper<SwapCardRecord> {
    /**
     * 获取最新的刷卡记录
     * @param equipmentId 设备id
     * @return {@link SwapCardRecordDTO}
     */
    SwapCardRecordDTO findLastRecordByEquipmentId(Integer equipmentId);

    /**
     * 获取刷卡分页信息
     * @param page 分页信息
     * @param equipmentId 设备id
     * @param areaId 区域id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link Page}<{@link SwapCardRecordDTO}>
     */
    Page<SwapCardRecordDTO> findSwapCardRecordPage(Page page,Integer userId, Integer equipmentId, Integer areaId, Date startTime, Date endTime);
}
