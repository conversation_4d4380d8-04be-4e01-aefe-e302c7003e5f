package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.DoorAlarmDTO;
import com.siteweb.monitoring.entity.ActiveEvent;

import java.util.Date;

public interface DoorAlarmMapper extends BaseMapper<ActiveEvent> {
    /**
     * 获取门告警
     *
     * @param page        分页信息
     * @param equipmentId 设备id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return {@link Page}<{@link DoorAlarmDTO}>
     */
    Page<DoorAlarmDTO> findDoorAlarm(Page page, Integer equipmentId, Date startTime, Date endTime);
}
