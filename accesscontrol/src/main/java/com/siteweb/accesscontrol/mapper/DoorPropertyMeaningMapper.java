package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.IdNameDTO;
import com.siteweb.accesscontrol.entity.DoorPropertyMeaning;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DoorPropertyMeaningMapper extends BaseMapper<DoorPropertyMeaning> {
    List<IdNameDTO> findDoorParamMeaning(@Param("equipmentId") Integer equipmentId, @Param("propertyType") Integer propertyType);
}
