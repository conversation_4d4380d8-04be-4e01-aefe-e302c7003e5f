package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.DoorTimeGroupStrDTO;
import com.siteweb.accesscontrol.dto.EquipmentTimeGroupDTO;
import com.siteweb.accesscontrol.entity.DoorTimeGroup;
import com.siteweb.accesscontrol.entity.TimeGroup;

import java.util.List;

public interface DoorTimeGroupMapper extends BaseMapper<DoorTimeGroup> {
    List<EquipmentTimeGroupDTO> findDoorTimeGroup();

    List<DoorTimeGroupStrDTO> findDoorTimeGroupStr(Integer doorId, List<Integer> delTimeGroupIdList);

    /**
     * @param equipmentId 设备id
     * @param timeGroupId 时间组id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findTimeGroupNo(Integer equipmentId, Integer timeGroupId);

    /**
     * 获取缺失的门准进组
     * @param doorId 门id
     * @return {@link List}<{@link TimeGroup}>
     */
    List<Integer> findLackDoorTimeGroupType(Integer doorId);

    void batchInsert(List<DoorTimeGroup> doorTimeGroupList);

    /**
     * 通过设备id获取门绑定了多少个准进时间组
     * @param equipmentId 设备id
     * @return int 绑定的数量
     */
    int findDoorTimeGroupCountByEquipmentId(Integer equipmentId);
}
