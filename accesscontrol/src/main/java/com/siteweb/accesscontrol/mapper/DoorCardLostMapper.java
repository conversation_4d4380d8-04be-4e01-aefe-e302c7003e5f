package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.DoorCardLostDTO;
import com.siteweb.accesscontrol.entity.DoorCardLost;

import java.util.List;

public interface DoorCardLostMapper extends BaseMapper<DoorCardLost> {
    Integer createLostRecord(Integer cardId);

    List<DoorCardLostDTO> findDoorCardLost(Integer cardId);
}
