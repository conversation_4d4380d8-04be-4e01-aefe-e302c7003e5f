package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.entity.FingerprintAuth;

import java.util.List;

public interface FingerprintAuthMapper extends BaseMapper<FingerprintAuth> {
    Integer deleteByCardId(Integer cardId);

    void deleteFingerprintAuth(Integer stationId, Integer equipmentId, Integer cardId, Integer fingerPrintId);

    void updateLastUpdateAndFingerPrintId(Integer stationId, Integer equipmentId, Integer cardId, Integer fingerPrintId);

    void saveAuthFingerPrint(List<FingerprintAuth> fingerprintAuthList);

    int cardFingerprintAuthCount(Integer cardId);
}
