package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.DoorControlDTO;
import com.siteweb.accesscontrol.dto.DoorControlQueueDTO;
import com.siteweb.monitoring.entity.ActiveControl;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface DoorControlMapper extends BaseMapper<ActiveControl> {
    Page<DoorControlDTO> findDoorControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Collection<Integer> equipmentIds, Collection<Integer> employeeIds, Date startTime, Date endTime);

    Page<DoorControlDTO> findCardControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Collection<Integer> equipmentIds, Collection<Integer> employeeIds, Date startTime, Date endTime);

    Page<DoorControlQueueDTO> findControlQueueOfDoor(Page page, Integer userId, Integer areaId, Integer equipmentId, Collection<Integer> equipmentIds, Collection<Integer> employeeIds);

    void batchDeleteWaitQueue(List<Integer> ids);

    Page<DoorControlDTO> findControlActiveOfDoor(Page page, Integer userId, Integer areaId, Integer equipmentId, Integer controlResultType, Set<Integer> equipmentIds, Set<Integer> employeeIds);

    Page<DoorControlDTO> findControlHistoryOfDoor(Page page, Integer userId, Integer areaId, Integer equipmentId, Integer controlResultType, Set<Integer> equipmentIds, Set<Integer> employeeIds, Date startTime, Date endTime);

    Long getActiveQueueCount(Set<Integer> equipmentIds, Set<Integer> employeeIds);
}
