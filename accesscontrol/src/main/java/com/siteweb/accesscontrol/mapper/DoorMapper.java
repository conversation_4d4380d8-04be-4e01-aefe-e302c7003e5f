package com.siteweb.accesscontrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.accesscontrol.dto.DoorDTO;
import com.siteweb.accesscontrol.dto.DoorEquipmentVO;
import com.siteweb.accesscontrol.entity.Door;
import com.siteweb.accesscontrol.vo.EquipmentDoor;

import java.util.Collection;
import java.util.List;

public interface DoorMapper extends BaseMapper<Door> {
    List<Integer> findCardAuthEquipment(Integer cardId);

    Integer findDoorCategoryByEquipmentId(Integer equipmentId);

    EquipmentDoor findInfoByEquipmentId(Integer equipmentId);

    List<Integer> findDoorProperties(Integer equipmentId);

    DoorDTO findDoorInfoByEquipmentId(Integer equipmentId);

    List<DoorEquipmentVO> findDoorEquipmentByEquipmentIds(Collection<Integer> equipmentIds);
}
