package com.siteweb.accesscontrol.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class TimeGroupSpanDTO {
    private Integer timeSpanId;
    private Integer week;
    @JsonIgnore
    private String timeSpanChar;
    private String timeSpanChar1;
    private String timeSpanChar2;
    private String timeSpanChar3;

    public String getTimeSpanChar1() {
        return timeSpanChar.substring(0,11);
    }

    public String getTimeSpanChar2() {
        return timeSpanChar.substring(11,22);
    }

    public String getTimeSpanChar3() {
        return timeSpanChar.substring(22,33);
    }
}
