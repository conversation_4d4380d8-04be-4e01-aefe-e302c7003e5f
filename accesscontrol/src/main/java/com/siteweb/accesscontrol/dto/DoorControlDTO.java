package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Date;

/**
 * 门控制命令DTO
 * <AUTHOR>
 * @date 2022/09/24
 */
@Data
public class DoorControlDTO {
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 控制名称
     */
    private String controlName;
    /**
     * 控制结果
     */
    public String controlResult;
    /**
     * 控制实执行人id
     */
    private String controlExecuterId;
    /**
     * 控制实执行人
     */
    private String ControlExecuterIdName;
    /**
     * 序列号
     */
    private Integer serialNo;
    /**
     * 控制参数值
     */
    private String parameterValues;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
}
