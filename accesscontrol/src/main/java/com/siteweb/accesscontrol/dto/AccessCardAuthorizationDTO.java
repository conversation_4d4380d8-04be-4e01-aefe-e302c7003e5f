package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AccessCardAuthorizationDTO {

    /**
     * 卡授权标志位
     */
    private Integer flag;
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 卡id列表
     */
    private List<Integer> cardIdList;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备id列表
     */
    private List<Integer> equipmentIdList;
    /**
     * 时间组编号
     */
    private Integer timeGroupNo;
    /**
     * 有效时间
     */
    private Date validTime;
    /**
     * 准进组id
     */
    private Integer timeGroupId;
}
