package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Date;

@Data
public class DoorControlQueueDTO {
    /**
     * id
     */
    private Integer id;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 控制id
     */
    private Integer controlId;
    /**
     * 控制名字
     */
    private String controlName;
    /**
     * 用户用
     */
    private String userId;
    /**
     * 参数值
     */
    private String parameterValues;
    /**
     * 最新更新时间
     */
    private Date lastUpdate;
    /**
     * 区域名称
     */
    private String areaName;
}
