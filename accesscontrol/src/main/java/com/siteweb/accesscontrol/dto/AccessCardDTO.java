package com.siteweb.accesscontrol.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AccessCardDTO {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 卡分组id
     */
    private Integer cardGroup;
    /**
     * 卡分组名称
     */
    private String cardGroupName;
    /**
     * 卡分组状态
     */
    private String cardStatusName;
    /**
     * 注册时间
     */
    private Date registerTime;
    /**
     * 有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    /**
     * 有效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /**
     * 卡状态
     */
    private Integer cardStatus;
    /**
     * 持卡人id
     */
    private Integer userId;
    /**
     * 卡密码
     */
    private String password;
    /**
     * 卡号类型
     */
    private Integer cardCodeType;
    /**
     * 卡号类型名称
     */
    private String cardCodeTypeName;
    /**
     * 卡类型
     */
    private Integer cardType;
    /**
     * 指纹厂商
     */
    private String vendor;
    /**
     * 指纹id
     */
    private String fingerprintId;
    /**
     * 人脸id
     */
    private String faceId;

    public String getFingerprintId() {
        if (CharSequenceUtil.isBlank(vendor) || CharSequenceUtil.isBlank(fingerprintId)) {
            return null;
        }
        return vendor + "-" + fingerprintId;
    }
}
