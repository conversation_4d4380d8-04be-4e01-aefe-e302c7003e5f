package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Date;

/**
 * 门告警DTO
 * <AUTHOR>
 * @date 2022/09/24
 */
@Data
public class DoorAlarmDTO {
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 告警名称
     */
    private String eventName;
    /**
     * 告警等级
     */
    public String eventSeverity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 确认时间
     */
    private Date confirmTime;
}
