package com.siteweb.accesscontrol.dto;

import com.siteweb.accesscontrol.entity.DoorTag;
import com.siteweb.accesscontrol.vo.DoorTagVO;
import lombok.Data;

import java.util.List;

@Data
public class DoorAreaEquipmentDTO {
    /**
     * 区域id
     */
    private Integer areaId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 门禁类型
     */
    private Integer category;
    /**
     * 厂商
     */
    private String vendor;
    /**
     * 标签
     */
    private List<DoorTagVO> tags;
}
