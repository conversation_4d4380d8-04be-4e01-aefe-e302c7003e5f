package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Date;

@Data
public class DoorCardLostDTO {
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 门密码
     */
    private String password;
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 时间校验组类型
     */
    private Integer timeGroupType;
    /**
     * 挂失结束时间
     */
    private Date endTime;
    /**
     * 门卡密码
     */
    private String doorCardPassword;
}
