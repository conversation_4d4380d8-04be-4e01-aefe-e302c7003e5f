package com.siteweb.accesscontrol.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/08/30
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DoorCardBackupTreeDTO {
    /**
     * 被删除的id
     */
    private String id;
    /**
     * 被删除的名称
     */
    private String name;
    /**
     * id名称集合
     */
    private List<IdNameDTO> children;
}
