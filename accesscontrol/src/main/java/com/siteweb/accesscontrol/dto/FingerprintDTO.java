package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Objects;

@Data
public class FingerprintDTO {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 指纹编号
     */
    private Integer fingerPrintNo;
    /**
     * 指纹数据
     */
    private byte[] fingerPrintData;
    /**
     * 厂商
     */
    private Integer vendor;

    /**
     * 如果指纹数据为空 说明要删除指纹数据
     * @return boolean 指纹数据是否为空
     */
    public boolean fingerprintDataEmpty(){
        return Objects.isNull(fingerPrintData) || fingerPrintData.length == 0;
    }
}
