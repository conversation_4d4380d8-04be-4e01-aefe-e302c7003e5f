package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.Date;

@Data
public class AccessCardInfoDTO {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 卡名称
     */
    private String cardName;
    /**
     * 卡号
     */
    private String cardCode;
    /**
     * 卡密码
     */
    private String password;
    /**
     * 有效结束时间
     */
    private Date endTime;
    /**
     * 卡号类型 十进制\十六进制
     */
    private Integer cardCodeType;
    /**
     * 卡号转换(tbl_card.cardcode的十进制或十六进制)
     */
    private String cardCodeConv;

}
