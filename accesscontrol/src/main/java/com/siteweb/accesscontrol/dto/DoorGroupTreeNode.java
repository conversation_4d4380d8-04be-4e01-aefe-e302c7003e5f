package com.siteweb.accesscontrol.dto;

import lombok.Data;

import java.util.List;

@Data
public class DoorGroupTreeNode {

    public DoorGroupTreeNode(Integer id, String name, Boolean isEquipment, Integer parentId,String description,  Integer category) {
        this.id = id;
        this.name = name;
        this.isEquipment = isEquipment;
        this.description = description;
        this.parentId = parentId;
        this.category = category;
    }
    /**
     * 门id或者门分组id
     */
    private Integer id;
    /**
     * 门名称或者门分组名称
     */
    private String name;
    /**
     * 门禁类型
     */
    private Integer category;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否是设备
     */
    private Boolean isEquipment;
    /**
     * 父级id
     */
    private Integer parentId;
    /**
     * 准进时间组id
     */
    private List<Integer> timeGroupIdList;
}
