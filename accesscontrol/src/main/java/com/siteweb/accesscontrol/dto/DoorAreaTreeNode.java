package com.siteweb.accesscontrol.dto;

import com.siteweb.accesscontrol.entity.DoorArea;
import com.siteweb.accesscontrol.vo.DoorTagVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoorAreaTreeNode {
    public DoorAreaTreeNode(DoorArea doorArea) {
        this.id = doorArea.getAreaId();
        this.name = doorArea.getAreaName();
        this.isEquipment = false;
        this.parentId = doorArea.getParentId();
        this.description = doorArea.getDescription();
    }

    public DoorAreaTreeNode(DoorAreaEquipmentDTO doorAreaEquipmentDTO) {
        this.id = doorAreaEquipmentDTO.getEquipmentId();
        this.name = doorAreaEquipmentDTO.getEquipmentName();
        this.isEquipment = true;
        this.parentId = doorAreaEquipmentDTO.getAreaId();
        this.category = doorAreaEquipmentDTO.getCategory();
        this.vendor = doorAreaEquipmentDTO.getVendor();
        this.tagIcons = doorAreaEquipmentDTO.getTags();
    }

    /**
     * 区域id或者设备id
     */
    private Integer id;
    /**
     * 区域名称或者设备名称
     */
    private String name;
    /**
     * 是否是设备
     */
    private Boolean isEquipment;
    /**
     * 区域描述
     */
    private String description;

    /**
     * 父级id
     */
    private Integer parentId;
    /**
     * 门禁类型
     */
    private Integer category;
    /**
     * 厂商
     */
    private String vendor;
    /**
     * 准进时间组id
     */
    private List<Integer> timeGroupIdList;
    /**
     * 门的图标
     */
    private List<DoorTagVO> tagIcons;
}
