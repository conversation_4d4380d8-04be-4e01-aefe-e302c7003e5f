package com.siteweb.accesscontrol.dto;

import com.siteweb.accesscontrol.entity.TimeGroup;
import lombok.Data;

import java.util.List;

/**
 * 门基本信息
 * <AUTHOR>
 * @date 2022/09/23
 */
@Data
public class DoorDTO {
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 门编号
     */
    private Integer doorNo;
    /**
     * 门名称
     */
    private String doorName;
    /**
     * 密码
     */
    private String password;
    /**
     * 门类别
     */
    private Integer category;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 准进时间组列表
     */
    private List<TimeGroup> timeGroupList;
    /**
     * 门参数列表
     */
    private List<DoorParamItem> paramList;
}
