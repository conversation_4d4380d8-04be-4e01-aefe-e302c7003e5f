package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.CardValidDateDTO;
import com.siteweb.accesscontrol.entity.Door;
import com.siteweb.accesscontrol.entity.DoorCard;
import com.siteweb.accesscontrol.manager.DoorCommandManager;
import com.siteweb.accesscontrol.mapper.CardAuthorizationServiceMapper;
import com.siteweb.accesscontrol.service.*;
import com.siteweb.accesscontrol.vo.AccessCardAuthorizationVO;
import com.siteweb.accesscontrol.vo.CardGroupVO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.monitoring.service.EquipmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CardAuthorizationServiceImpl implements CardAuthorizationService {
    @Autowired
    private DoorCommandManager doorCommandManager;
    @Autowired
    DepartmentPermissionService departmentPermissionService;
    @Autowired
    CardAuthorizationServiceMapper cardAuthorizationServiceMapper;
    @Autowired
    CardGroupService cardGroupService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    ExecutorService scheduledService;
    @Autowired
    DoorCardService doorCardService;
    @Autowired
    DoorService doorService;
    @Autowired
    CardService cardService;
    public Boolean addAccessCardAuthorization(Integer userId, List<Integer> equipmentIds,List<Integer> cardIds,Integer timeGroupId,Integer flag) {
        return doorCommandManager.addManualDoorCard(userId, equipmentIds, timeGroupId, cardIds, flag);
    }

    @Override
    public Page<AccessCardAuthorizationVO> findCardAuthorizationPage(Page page, String cardCode, Integer equipmentId, Integer cardGroup, Integer areaId, Integer userId, String stationName) throws InterruptedException {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(employeeIds) || CollUtil.isEmpty(equipmentIds)) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        page.setSearchCount(false);
        Page<AccessCardAuthorizationVO> cardAuthorizationPage = cardAuthorizationServiceMapper.findCardAuthorizationPage(page, cardCode, equipmentId, cardGroup, areaId, userId, stationName, employeeIds,equipmentIds);
        Long count = cardAuthorizationServiceMapper.findCardAuthorizationCount(cardCode, equipmentId, cardGroup, areaId, userId, stationName, employeeIds,equipmentIds);
        cardAuthorizationPage.setTotal(count);
        addExtInfo(cardAuthorizationPage.getRecords());
        return cardAuthorizationPage;
    }

    @Override
    public void updateAccessCardAuthorizationDate(CardValidDateDTO cardValidDateDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        //获取卡被授权的门
        List<DoorCard> doorCardList = doorCardService.findByCardIds(cardValidDateDTO.getCardIdList());
        List<Door> door = doorService.findByDoorIds(doorCardList.stream().map(DoorCard::getDoorId).toList());
        Map<Integer, Integer> doorEquipmentIdMap = door.stream().collect(Collectors.toMap(Door::getDoorId, Door::getEquipmentId));
        Date date = DateUtil.parse(cardValidDateDTO.getValidTime());
        for (DoorCard doorCard : doorCardList) {
            Integer equipmentId = doorEquipmentIdMap.get(doorCard.getDoorId());
            if (Objects.isNull(equipmentId)) {
                log.warn("门id:{}已经被删除，无法修改卡授权时间 已忽略", doorCard.getDoorId());
                continue;
            }
            doorCommandManager.editManualDoorCard(userId, equipmentId, doorCard.getTimeGroupType(), doorCard.getCardId(), date);
        }
        //修改卡有效期
        cardService.updateEndTime(cardValidDateDTO.getCardIdList(),DateUtil.parse(cardValidDateDTO.getValidTime()));
    }

    /**
     * 为分页数据查询添加一些额外信息，
     * 避免连接的表过多，在外面做额外处理
     * @param records 查询出来的总数据
     */
    private void addExtInfo(List<AccessCardAuthorizationVO> records) throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(3);
        scheduledService.execute(() -> {
            findCardGroupName(records);
            countDownLatch.countDown();
        });
        scheduledService.execute(() -> {
            findFingerInfo(records);
            countDownLatch.countDown();
        });
        scheduledService.execute(() -> {
            findFaceInfo(records);
            countDownLatch.countDown();
        });
        countDownLatch.await(3, TimeUnit.SECONDS);
    }

    private void findFaceInfo(List<AccessCardAuthorizationVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Map<String, AccessCardAuthorizationVO> fingerInfoMap = cardAuthorizationServiceMapper
                .findFacePrintAuthorizationInfo(records).stream()
                .collect(Collectors.toMap(AccessCardAuthorizationVO::getAuthorizationKey, v -> v));
        records.forEach(record ->
        {
            if (fingerInfoMap.containsKey(record.getAuthorizationKey())) {
                AccessCardAuthorizationVO accessCardAuthorizationVO = fingerInfoMap.get(record.getAuthorizationKey());
                record.setFaceId(accessCardAuthorizationVO.getFaceId());
                record.setFaceOutDate(accessCardAuthorizationVO.getFaceOutDate());
            }
        });
    }

    private void findFingerInfo(List<AccessCardAuthorizationVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Map<String, AccessCardAuthorizationVO> fingerInfoMap = cardAuthorizationServiceMapper
                .findFingerPrintAuthorizationInfo(records).stream()
                .collect(Collectors.toMap(AccessCardAuthorizationVO::getAuthorizationKey, v -> v));
        records.forEach(record ->
        {
            if (fingerInfoMap.containsKey(record.getAuthorizationKey())) {
                AccessCardAuthorizationVO accessCardAuthorizationVO = fingerInfoMap.get(record.getAuthorizationKey());
                record.setFingerprintId(accessCardAuthorizationVO.getFingerprintId());
                record.setFingerprintOutDate(accessCardAuthorizationVO.getFingerprintOutDate());
            }
        });
    }

    /**
     * 获取卡分组名称
     * @param records 记录
     */
    private void findCardGroupName(List<AccessCardAuthorizationVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Map<Integer, String> cardGroupMap = cardGroupService.findAll().stream().collect(Collectors.toMap(CardGroupVO::getItemId, CardGroupVO::getItemValue));
        records.forEach(record -> record.setCardGroupName(cardGroupMap.get(Integer.valueOf(record.getCardGroupName()))));
    }
}
