package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.DoorAreaTreeNode;
import com.siteweb.accesscontrol.entity.DoorArea;

import java.util.List;

public interface DoorAreaService {
    /**
     * 获取所有门区域
     * @return {@link List}<{@link DoorArea}>
     */
    List<DoorArea> findAll();

    /**
     * 查看门禁区域报表
     * @return {@link List}<{@link DoorArea}>
     */
    List<DoorArea> findDoorAreas();

    DoorArea createDoorArea(DoorArea doorArea);

    DoorArea updateDoorArea(DoorArea doorArea);

    Boolean deleteById(Integer id);

    /**
     * 获取门区域树
     * @return {@link List}<{@link DoorAreaTreeNode}>
     */
    List<DoorAreaTreeNode> findDoorAreaTree();
}
