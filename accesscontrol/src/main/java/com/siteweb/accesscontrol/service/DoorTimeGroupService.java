package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.EquipmentTimeGroupDTO;
import com.siteweb.accesscontrol.entity.DoorTimeGroup;

import java.util.List;

public interface DoorTimeGroupService {
    List<DoorTimeGroup> findByTimeGroupIds(List<Integer> timeGroupIdList);

    Integer deleteByTimeGroupIds(List<Integer> timeGroupIdList);

    List<EquipmentTimeGroupDTO> findDoorTimeGroup();

    /**
     * 检查门设置的准进时间组是否达到上限
     * 注:一个门最多只能设置四个准进时间组
     * @param doorId 门id
     * @param addTimeGroupIdList 添加的准金时间组id集合
     * @param delTimeGroupIdList 删除的准金时间组id集合
     * @return boolean
     */
    boolean limitCheck(Integer equipmentId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList);

    /**
     * 将修改的时间组信息存库，返回控制下发所需的时间字符串列表
     * @return {@link List}<{@link String}>
     */
    List<String> findTimeSpanStrList(Integer doorId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList);

    void createDoorTimeGroup(Integer doorId, List<Integer> timeGroupIds);

    void deleteByDoorIdAndTimeGroupIds(Integer doorId, List<Integer> timeGroupIds);

    /**
     * 获取时间组编号
     * @param equipmentId 设备id
     * @param timeGroupId 时间组id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findTimeGroupNo(Integer equipmentId, Integer timeGroupId);

    /**
     * 找到时间组时间by门ID和时间组ID
     *
     * @param doorId         门ID
     * @param timeGroupId  时间组ID
     * @return {@link Integer }
     */
    Integer findTimeGroupTimeByDoorIdAndTimeGroupId(Integer doorId, Integer timeGroupId);
}
