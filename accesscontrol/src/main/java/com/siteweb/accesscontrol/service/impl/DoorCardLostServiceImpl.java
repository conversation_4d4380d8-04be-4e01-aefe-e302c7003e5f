package com.siteweb.accesscontrol.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorCardLostDTO;
import com.siteweb.accesscontrol.entity.DoorCardLost;
import com.siteweb.accesscontrol.mapper.DoorCardLostMapper;
import com.siteweb.accesscontrol.service.DoorCardLostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DoorCardLostServiceImpl implements DoorCardLostService {
    @Autowired
    private DoorCardLostMapper doorCardLostMapper;
    @Override
    public Integer createLostRecord(Integer cardId) {
        this.deleteLostRecord(cardId);
        return doorCardLostMapper.createLostRecord(cardId);
    }

    @Override
    public Integer deleteLostRecord(Integer cardId) {
        return doorCardLostMapper.delete(Wrappers.lambdaQuery(DoorCardLost.class)
                                                 .eq(DoorCardLost::getCardId, cardId));
    }

    @Override
    public List<DoorCardLostDTO> findDoorCardLost(Integer cardId) {
        return doorCardLostMapper.findDoorCardLost(cardId);
    }
}
