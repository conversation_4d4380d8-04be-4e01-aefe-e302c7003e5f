package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.DoorControlDTO;
import com.siteweb.accesscontrol.dto.DoorControlQueueDTO;
import com.siteweb.accesscontrol.mapper.DoorControlMapper;
import com.siteweb.accesscontrol.service.DoorControlService;
import com.siteweb.accesscontrol.vo.DoorControlCount;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.common.constants.GlobalConstants;
import com.siteweb.monitoring.entity.ActiveControlOfDoor;
import com.siteweb.monitoring.mapper.ActiveControlOfDoorMapper;
import com.siteweb.monitoring.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class DoorControlServiceImpl implements DoorControlService {
    @Autowired
    private DoorControlMapper doorControlMapper;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    ActiveControlOfDoorMapper activeControlOfDoorMapper;
    @Autowired
    private DepartmentPermissionService departmentPermissionService;
    @Override
    public Page<DoorControlDTO> findDoorControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Date startTime, Date endTime) {
        //control.ControlCategory  12,13,14,31,32,33,34,35,36,40,41 来自与 tbl_dataitem表中EntryId = 31与门禁卡相关的控制命令
        //equipment.EquipmentCategory  (82, 97, 98) 来自与 tbl_dataitem表中EntryId = 7与门禁设备相关的设备类型
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(equipmentIds) || CollUtil.isEmpty(employeeIds)) {
            return Page.of(page.getCurrent(),page.getSize());
        }
        page.setOptimizeCountSql(false);
        return doorControlMapper.findDoorControl(page, userId, equipmentId, controlResultType, equipmentIds, employeeIds, startTime, endTime);
    }

    @Override
    public Page<DoorControlDTO> findCardControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Date startTime, Date endTime) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(equipmentIds) || CollUtil.isEmpty(employeeIds)) {
            return Page.of(page.getCurrent(),page.getSize());
        }
        page.setOptimizeCountSql(false);
        return doorControlMapper.findCardControl(page, userId, equipmentId, controlResultType, equipmentIds, employeeIds, startTime, endTime);
    }

    @Override
    public DoorControlCount findControlCountOfDoor() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(equipmentIds) || CollUtil.isEmpty(employeeIds)) {
            return new DoorControlCount(0L, 0L);
        }
        //获取等待队列数
        Long waitQueueCount = activeControlOfDoorMapper.selectCount(Wrappers.lambdaQuery(ActiveControlOfDoor.class)
                                                                            .in(ActiveControlOfDoor::getEquipmentId, equipmentIds)
                                                                            .in(ActiveControlOfDoor::getUserId, employeeIds));
        //活动队列数
        Long activeQueueCount = doorControlMapper.getActiveQueueCount(equipmentIds, employeeIds);
        return new DoorControlCount(waitQueueCount, activeQueueCount);
    }

    @Override
    public Page<DoorControlQueueDTO> findControlQueueOfDoor(Page page, Integer userId, Integer areaId, Integer equipmentId) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(equipmentIds) || CollUtil.isEmpty(employeeIds)) {
            return Page.of(page.getCurrent(), page.getSize());
        }
        return doorControlMapper.findControlQueueOfDoor(page, userId, areaId, equipmentId, equipmentIds, employeeIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteControlQueueOfDoor(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return 0;
        }
        List<Integer> removeIds = new ArrayList<>();
        //500个一批删除
        for (Integer id : idList) {
            removeIds.add(id);
            if (removeIds.size() >= GlobalConstants.BATCH_INSERT_MAX_COUNT) {
                doorControlMapper.batchDeleteWaitQueue(removeIds);
                removeIds.clear();
            }
        }
        //存在未删除的完全的命令
        if (CollUtil.isNotEmpty(removeIds)) {
            doorControlMapper.batchDeleteWaitQueue(removeIds);
        }
        return idList.size();
    }

    @Override
    public Page<DoorControlDTO> findControlActiveOfDoor(Page page, Integer userId, Integer equipmentId, Integer areaId, Integer controlResultType) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(equipmentIds) || CollUtil.isEmpty(employeeIds)) {
            return Page.of(page.getCurrent(),page.getSize());
        }
        page.setOptimizeCountSql(false);
        return doorControlMapper.findControlActiveOfDoor(page, userId, areaId, equipmentId, controlResultType, equipmentIds, employeeIds);
    }

    @Override
    public Page<DoorControlDTO> findControlHistoryOfDoor(Page page, Integer userId, Integer equipmentId, Integer areaId, Integer controlResultType, Date startTime, Date endTime) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(loginUserId);
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(loginUserId);
        if (CollUtil.isEmpty(equipmentIds) || CollUtil.isEmpty(employeeIds)) {
            return Page.of(page.getCurrent(),page.getSize());
        }
        page.setOptimizeCountSql(false);
        return doorControlMapper.findControlHistoryOfDoor(page, userId, areaId, equipmentId, controlResultType, equipmentIds, employeeIds, startTime, endTime);
    }
}
