package com.siteweb.accesscontrol.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.DoorControlDTO;
import com.siteweb.accesscontrol.dto.DoorControlQueueDTO;
import com.siteweb.accesscontrol.vo.DoorControlCount;

import java.util.Date;
import java.util.List;

public interface DoorControlService {
    /**
     * 查找门门控制命令
     *
     * @param page              页面
     * @param userId            用户id
     * @param equipmentId       设备id
     * @param areaId            区域id
     * @param controlResultType 控制结果类型
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return {@link Page}<{@link DoorControlDTO}>
     */
    Page<DoorControlDTO> findDoorControl(Page page, Integer userId, Integer equipmentId,Integer controlResultType, Date startTime, Date endTime);

    Page<DoorControlDTO> findCardControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Date startTime, Date endTime);

    /**
     * 获取未执行完成和执行失败的门禁控制队列数量
     *
     * @return {@link DoorControlCount}
     */
    DoorControlCount findControlCountOfDoor();

    /**
     * 获取等待发送的门控制队列
     *
     * @param page        分页信息
     * @param userId      用户id
     * @param areaId      区域id
     * @param equipmentId 设备id
     * @return {@link DoorControlQueueDTO}
     */
    Page<DoorControlQueueDTO> findControlQueueOfDoor(Page page, Integer userId, Integer areaId, Integer equipmentId);

    /**
     * 删除等待发送的门控制队列
     * @param idList 需要删除的等待命令id
     * @return {@link Integer}
     */
    Integer deleteControlQueueOfDoor(List<Integer> idList);

    /**
     * 获取活动门控制队列
     *
     * @param page              分页信息
     * @param userId            用户id
     * @param equipmentId       设备id
     * @param areaId            区域id
     * @param controlResultType 控制结果类型
     * @return {@link Page}<{@link DoorControlDTO}>
     */
    Page<DoorControlDTO> findControlActiveOfDoor(Page page, Integer userId, Integer equipmentId, Integer areaId, Integer controlResultType);

    /**
     * 获取历史门控制队列
     * @param page              分页信息
     * @param userId            用户id
     * @param equipmentId       设备id
     * @param areaId            区域id
     * @param controlResultType 控制结果类型
     * @param startTime
     * @param endTime
     * @return {@link Page}<{@link DoorControlDTO}>
     */
    Page<DoorControlDTO> findControlHistoryOfDoor(Page page, Integer userId, Integer equipmentId, Integer areaId, Integer controlResultType, Date startTime, Date endTime);
}
