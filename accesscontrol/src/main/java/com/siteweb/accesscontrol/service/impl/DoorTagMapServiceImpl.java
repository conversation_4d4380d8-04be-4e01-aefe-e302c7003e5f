package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorTagMapDTO;
import com.siteweb.accesscontrol.entity.DoorTagMap;
import com.siteweb.accesscontrol.mapper.DoorTagMapMapper;
import com.siteweb.accesscontrol.service.DoorTagMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class DoorTagMapServiceImpl implements DoorTagMapService {
    @Autowired
    DoorTagMapMapper doorTagMapMapper;

    @Override
    public int insert(DoorTagMap doorTagMap) {
        return doorTagMapMapper.insert(doorTagMap);
    }

    @Override
    public int update(DoorTagMap doorTagMap) {
        return doorTagMapMapper.updateById(doorTagMap);
    }

    @Override
    public void deleteByTagIds(List<Integer> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return;
        }
        doorTagMapMapper.deleteByIds(tagIds);
    }

    @Override
    public int updateDoorTagMap(DoorTagMapDTO doorTagMapDTO) {
        deleteByDoorId(doorTagMapDTO.getEquipmentId());
        if (CollUtil.isEmpty(doorTagMapDTO.getDoorTagIds())) {
            return 0;
        }
        List<DoorTagMap> doorTagMapList = new ArrayList<>(doorTagMapDTO.getDoorTagIds().size());
        doorTagMapDTO.getDoorTagIds().forEach(doorTagId -> doorTagMapList.add(new DoorTagMap(doorTagMapDTO.getEquipmentId(), doorTagId)));
        return doorTagMapMapper.batchInsert(doorTagMapList);
    }

    private void deleteByDoorId(Integer doorId) {
        doorTagMapMapper.delete(Wrappers.lambdaQuery(DoorTagMap.class)
                                        .eq(DoorTagMap::getEquipmentId, doorId));
    }
}
