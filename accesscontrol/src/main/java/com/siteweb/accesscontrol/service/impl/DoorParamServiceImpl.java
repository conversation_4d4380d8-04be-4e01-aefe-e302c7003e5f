package com.siteweb.accesscontrol.service.impl;

import com.siteweb.accesscontrol.dto.DoorParamItem;
import com.siteweb.accesscontrol.mapper.DoorParamMapper;
import com.siteweb.accesscontrol.service.DoorParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DoorParamServiceImpl implements DoorParamService {
    @Autowired
    private DoorParamMapper doorParamMapper;
    @Override
    public List<DoorParamItem> findDoorParamByEquipmentId(Integer equipmentId) {
        return doorParamMapper.findDoorParamByEquipmentId(equipmentId);
    }
}
