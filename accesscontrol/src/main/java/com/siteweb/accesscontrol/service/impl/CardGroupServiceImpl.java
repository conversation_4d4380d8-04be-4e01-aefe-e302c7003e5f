package com.siteweb.accesscontrol.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.enums.DoorEnum;
import com.siteweb.accesscontrol.vo.CardGroupVO;
import com.siteweb.accesscontrol.mapper.CardGroupMapper;
import com.siteweb.accesscontrol.service.CardGroupService;
import com.siteweb.accesscontrol.service.CardService;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CardGroupServiceImpl implements CardGroupService {
    @Autowired
    private CardGroupMapper cardGroupMapper;
    @Autowired
    private CardService cardService;
    @Autowired
    private DataItemService dataItemService;

    @Override
    public List<CardGroupVO> findAll() {
        return cardGroupMapper.findAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteById(Integer id) {
        //该分组下的卡分到未分组中
        cardService.updateCardToUngrouped(id);
        return cardGroupMapper.delete(Wrappers.lambdaQuery(DataItem.class)
                                              .eq(DataItem::getEntryId, DoorEnum.CARD_GROUP.getEntryId())
                                              .eq(DataItem::getItemId, id));
    }

    @Override
    public Integer createCardGroup(CardGroupVO cardGroupVO) {
        return dataItemService.createDataItem(DoorEnum.CARD_GROUP.getEntryId(), cardGroupVO.getItemValue());
    }

    @Override
    public Integer updateCardGroup(CardGroupVO cardGroupVO) {
        return dataItemService.updateDataItem(DoorEnum.CARD_GROUP.getEntryId(), cardGroupVO.getItemId(), cardGroupVO.getItemValue());
    }
}
