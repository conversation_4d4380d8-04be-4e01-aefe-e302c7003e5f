package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.DoorCardLostDTO;

import java.util.List;

public interface DoorCardLostService {
    /**
     * 添加门禁卡挂失记录
     * @param cardId  门禁卡id
     * @return {@link Integer}
     */
    Integer createLostRecord(Integer cardId);

    Integer deleteLostRecord(Integer cardId);

    /**
     * 查询门卡挂失记录
     * @param cardId 卡id
     * @return {@link List}<{@link DoorCardLostDTO}>
     */
    List<DoorCardLostDTO> findDoorCardLost(Integer cardId);
}
