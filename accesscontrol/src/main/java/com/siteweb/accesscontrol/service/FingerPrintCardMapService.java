package com.siteweb.accesscontrol.service;


import com.siteweb.accesscontrol.entity.FingerPrintCardMap;

import java.util.Date;
import java.util.List;

public interface FingerPrintCardMapService {
    /**
     * 添加指纹授权信息 PAC_AddFingerprintAuth
     * @param fingerPrintId 指纹id
     * @param vendorId 生产商id
     */
    List<FingerPrintCardMap> findFingerPrintCardMap(Integer fingerPrintId, Integer vendorId);

    /**
     * 删除指纹信息，根据卡id
     * @param cardId 卡id
     */
    void deleteByCardId(Integer cardId);

    FingerPrintCardMap findByCardId(Integer cardId);

    /**
     * 卡是否已经存在指纹映射
     * @param cardId 卡id
     * @return boolean 是否存在指纹
     */
    boolean existsByCardId(Integer cardId);

    void batchInsert(List<FingerPrintCardMap> fingerPrintCardMapList);

    void updateUserIdAndTimeByCardId(Integer cardId, Integer userId, Date date);

    void deleteByFingerprintIdAndVendor(Integer fingerprintId, Integer vendor);
}
