package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorCardBackupTreeDTO;
import com.siteweb.accesscontrol.dto.IdNameDTO;
import com.siteweb.accesscontrol.entity.Card;
import com.siteweb.accesscontrol.entity.Door;
import com.siteweb.accesscontrol.entity.DoorCardBackup;
import com.siteweb.accesscontrol.enums.DoorCardTypeEnum;
import com.siteweb.accesscontrol.mapper.CardMapper;
import com.siteweb.accesscontrol.mapper.DoorCardBackupMapper;
import com.siteweb.accesscontrol.mapper.DoorMapper;
import com.siteweb.accesscontrol.service.DoorCardBackupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DoorCardBackupServiceImpl implements DoorCardBackupService {
    @Autowired
    DoorCardBackupMapper doorCardBackupMapper;
    @Autowired
    CardMapper cardMapper;
    @Autowired
    private DoorMapper doorMapper;

    @Override
    public List<DoorCardBackup> findAll() {
        return doorCardBackupMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public int backupDoor(List<Integer> equipmentIds, Integer cardId, String cardName) {
        List<DoorCardBackup> backupList = DoorCardBackup.backupDoorEntity(equipmentIds,cardId,cardName);
        if (CollUtil.isEmpty(backupList)) {
            return 0;
        }
        return doorCardBackupMapper.batchInsert(backupList);
    }

    @Override
    public List<DoorCardBackupTreeDTO> getBackupDoorTree() {
        return getBackupTree(DoorCardTypeEnum.DOOR, this::getDoorIdNameMap);
    }

    @Override
    public List<DoorCardBackupTreeDTO> getBackupCardTree() {
        return getBackupTree(DoorCardTypeEnum.CARD, this::getCardIdNameMap);
    }

    private List<DoorCardBackupTreeDTO> getBackupTree(DoorCardTypeEnum typeEnum, Function<List<DoorCardBackup>, Map<Integer, String>> idNameMapFunction) {
        List<DoorCardBackupTreeDTO> result = new ArrayList<>();
        List<DoorCardBackup> backupList = getBackupListByType(typeEnum);
        Map<Integer, String> idNameMap = idNameMapFunction.apply(backupList);
        Map<String, List<DoorCardBackup>> nameGroup = backupList.stream()
                                                                .filter(e -> idNameMap.containsKey(e.getId()))
                                                                .collect(Collectors.groupingBy(e -> e.getDeleteId() + "-" + e.getDeleteName()));
        if (CollUtil.isEmpty(idNameMap)) {
            return result;
        }
        nameGroup.forEach((key, value) -> {
            String[] split = key.split("-");
            DoorCardBackupTreeDTO dto = new DoorCardBackupTreeDTO();
            dto.setId(split[0]);
            dto.setName(split[1]);
            List<IdNameDTO> idNameDTOList = value.stream()
                                                 .map(v -> new IdNameDTO(v.getId(), idNameMap.get(v.getId())))
                                                 .toList();
            dto.setChildren(idNameDTOList);
            result.add(dto);
        });

        return result;
    }

    private Map<Integer, String> getCardIdNameMap(List<DoorCardBackup> backupList) {
        if (CollUtil.isEmpty(backupList)) {
            return Collections.emptyMap();
        }
        List<Card> cardList = cardMapper.selectList(Wrappers.lambdaQuery(Card.class)
                                                            .select(Card::getCardId, Card::getCardName)
                                                            .in(Card::getCardId, backupList.stream().map(DoorCardBackup::getId).toList()));
        return cardList.stream().collect(Collectors.toMap(Card::getCardId, Card::getCardName, (v1, v2) -> v1));

    }

    private Map<Integer, String> getDoorIdNameMap(List<DoorCardBackup> backupList) {
        if (CollUtil.isEmpty(backupList)) {
            return Collections.emptyMap();
        }
        List<Door> doorList = doorMapper.selectList(Wrappers.lambdaQuery(Door.class)
                                                            .select(Door::getEquipmentId, Door::getDoorName)
                                                            .in(Door::getEquipmentId, backupList.stream().map(DoorCardBackup::getId).toList()));
        return doorList.stream().collect(Collectors.toMap(Door::getEquipmentId, Door::getDoorName, (v1, v2) -> v1));
    }

    private List<DoorCardBackup> getBackupListByType(DoorCardTypeEnum typeEnum) {
        return doorCardBackupMapper.selectList(Wrappers.lambdaQuery(DoorCardBackup.class)
                                                       .eq(DoorCardBackup::getType, typeEnum.getValue()));
    }
}
