package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.AccessCardDTO;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.DoorCardInfoDTO;
import com.siteweb.accesscontrol.dto.FingerprintDTO;
import com.siteweb.accesscontrol.entity.*;
import com.siteweb.accesscontrol.enums.CardStatusEnum;
import com.siteweb.accesscontrol.enums.FeatureEnum;
import com.siteweb.accesscontrol.manager.DoorCommandManager;
import com.siteweb.accesscontrol.mapper.CardMapper;
import com.siteweb.accesscontrol.service.*;
import com.siteweb.accesscontrol.vo.AccessCardVO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.DepartmentPermissionService;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.utility.service.PrimaryKeyValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CardServiceImpl implements CardService {
    /**
     * 无卡分组id
     */
    public static final int UN_CARD_GROUP = 1;
    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private CardTypeMapService cardTypeMapService;

    @Autowired
    private CardExtService cardExtService;
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private FaceDataService faceDataService;

    @Autowired
    private DoorCommandManager doorCommandManager;

    @Autowired
    FingerPrintCardMapService fingerPrintCardMapService;
    @Autowired
    FaceDataMapService faceDataMapService;
    @Autowired
    FingerprintAuthService fingerprintAuthService;
    @Autowired
    FaceDataAuthService faceDataAuthService;
    @Autowired
    DoorCardService doorCardService;
    @Autowired
    FingerprintService fingerprintService;
    @Autowired
    DepartmentPermissionService departmentPermissionService;
    @Autowired
    private DoorCardBackupService doorCardBackupService;

    @Override
    public Integer updateCardToUngrouped(Integer cardGroupId) {
        return cardMapper.update(null, Wrappers.lambdaUpdate(Card.class)
                                               .set(Card::getCardGroup, UN_CARD_GROUP)
                                               .eq(Card::getCardGroup, cardGroupId));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createCard(AccessCardVO accessCardVO) {
        if (Boolean.TRUE.equals(isExistCard(null, accessCardVO.getCardCode(), accessCardVO.getCardCodeType()))) {
            return -1;
        }
        int cardId = primaryKeyValueService.getGlobalIdentity("TBL_Card", 0);
        Card card = BeanUtil.copyProperties(accessCardVO, Card.class);
        card.setCardId(cardId);
        card.setRegisterTime(new Date());
        cardMapper.insert(card);
        accessCardVO.setCardId(cardId);
        CardExt cardExt = new CardExt(accessCardVO);
        cardExtService.saveCardExt(cardExt);
        return cardTypeMapService.saveCardTypeMap(cardId, accessCardVO.getCardType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateCard(AccessCardVO accessCardVO) {
        if (Boolean.TRUE.equals(isExistCard(accessCardVO.getCardId(), accessCardVO.getCardCode(), accessCardVO.getCardCodeType()))) {
            return -1;
        }
        //卡状态处理
        this.cardStatusHandler(accessCardVO.getCardId(), accessCardVO.getCardStatus());
        Card card = BeanUtil.copyProperties(accessCardVO, Card.class);
        cardMapper.updateById(card);
        CardExt cardExt = new CardExt(accessCardVO);
        cardExtService.updateCardExt(cardExt);
        return cardTypeMapService.updateCardTypeMap(card.getCardId(), accessCardVO.getCardType());
    }

    /**
     * @param cardId 门禁卡id
     * @param updateCardStatus 需要修改的卡状态
     */
    private void cardStatusHandler(Integer cardId, Integer updateCardStatus) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (ObjectUtil.isNull(userId)) {
            throw new BusinessException("loginUserId is empty");
        }
        AccessCardDTO cardById = this.findCardById(cardId);
        Integer currentCardStatus = cardById.getCardStatus();
        //当前状态是使用中 转 挂失
        if (ObjectUtil.equals(currentCardStatus, CardStatusEnum.USING.getStatus()) && ObjectUtil.equals(updateCardStatus, CardStatusEnum.LOST.getStatus())) {
            doorCommandManager.reportCardLoss(userId, cardId);
        }
        //当前状态是使用中 转 注销
        if (ObjectUtil.equals(currentCardStatus, CardStatusEnum.USING.getStatus()) && ObjectUtil.equals(updateCardStatus, CardStatusEnum.CANCEL.getStatus())) {
            doorCommandManager.unRegisterCard(userId, cardId);
        }
        //当前状态是挂失 转 使用中
        if (ObjectUtil.equals(currentCardStatus, CardStatusEnum.LOST.getStatus()) && ObjectUtil.equals(updateCardStatus, CardStatusEnum.USING.getStatus())) {
            doorCommandManager.useCard(userId, cardId);
        }
    }

    @Override
    public AccessCardDTO findCardById(Integer id) {
        return cardMapper.findById(id);
    }

    @Override
    public List<AccessCardDTO> findCardByCardGroup(Integer cardGroup) {
        Set<Integer> employeeIds = departmentPermissionService.findEmployeeIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(employeeIds)) {
            return Collections.emptyList();
        }
        return cardMapper.findCardByCardGroup(cardGroup, employeeIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteById(Integer userId,Integer cardId) {
        Card card = findById(cardId);
        if (card == null) {
            log.error("cardId:{},没有查询到表数据", cardId);
            return 0;
        }
        backupDoor(cardId,card.getCardName());
        if (!doorCommandManager.deleteCardAllAuth(userId,cardId)) {
            return 0;
        }
        cardMapper.deleteById(cardId);
        cardExtService.deleteById(cardId);
        cardTypeMapService.deleteById(cardId);
        fingerprintService.deleteByCardId(cardId);
        return faceDataService.deleteFaceData(cardId);
    }
    private Card findById(Integer cardId) {
        return cardMapper.selectById(cardId);
    }

    private void backupDoor(Integer cardId, String cardName) {
        List<Integer> equipmentIdList = doorCardService.findEquipmentIdByCardId(cardId);
        doorCardBackupService.backupDoor(equipmentIdList, cardId, cardName);
    }

    @Override
    public Integer updateCardStatus(Integer cardId, Integer status) {
        return cardMapper.update(null, Wrappers.lambdaUpdate(Card.class)
                                               .set(Card::getCardStatus, status)
                                               .eq(Card::getCardId, cardId));
    }

    @Override
    public AccessCardInfoDTO findInfoByCardId(Integer cardId) {
        return cardMapper.findInfoByCardId(cardId);
    }

    @Override
    public boolean lostCard(Integer userId, String srcCardCode, String dstCardCode, Boolean lostFlag) {
        Card srcCard =  this.findCardByCardCode(srcCardCode);
        Card desCard =  this.findCardByCardCode(dstCardCode);
        if (Objects.isNull(srcCard) || Objects.isNull(desCard)) {
            log.error("卡丢失：原卡号{}或者目标卡号{}不存在", srcCardCode, dstCardCode);
            return false;
        }
        //添加原卡的指纹与人脸映射信息至目标卡
        copyBioinformatics(userId, srcCard.getCardId(), desCard.getCardId());
        //判断原先的卡下发的时候有没有下发人脸或者指纹
        int flag = 1;
        if (fingerprintAuthService.existByCardId(srcCard.getCardId())) {
            flag = 3;
        }
        if (faceDataAuthService.existByCardId(srcCard.getCardId())) {
            flag = 7;
        }
        //开始授权
        List<DoorCardInfoDTO> doorCardInfoByCardId = doorCardService.findDoorCardInfoByCardIds(List.of(srcCard.getCardId()));
        for (DoorCardInfoDTO cardInfoDTO : doorCardInfoByCardId) {
            doorCommandManager.addManualDoorCard(userId, List.of(cardInfoDTO.getEquipmentId()), cardInfoDTO.getTimeGroupId(), List.of(desCard.getCardId()), flag);
        }
        //是否需要挂失原卡
        if (Boolean.TRUE.equals(lostFlag)) {
            cardStatusHandler(srcCard.getCardId(), CardStatusEnum.LOST.getStatus());
        }
        return true;
    }
    /**
     * 复制生物信息
     *
     * @param userId    用户id
     * @param srcCardId 原卡id
     * @param desCardId 目标卡id
     */
    private void copyBioinformatics(Integer userId, Integer srcCardId, Integer desCardId) {
         /*
          复制生物信息过去
          1.删除目标卡所有的生物信息
          2.复制指纹
          3.复制人脸
         */
        copyFingerInfo(userId, srcCardId, desCardId);
        copyFaceInfo(userId, srcCardId, desCardId);
    }

    /**
     * 复制人脸信息
     * @param userId 用户id
     * @param srcCardId 原卡id
     * @param desCardId 目标卡id
     */
    private void copyFaceInfo(Integer userId, Integer srcCardId, Integer desCardId) {
        //删除目标卡的人脸
        faceDataService.deleteFaceData(desCardId);
        FaceDataMap faceDataMap = faceDataMapService.findByCardId(srcCardId);
        FaceData faceData = faceDataService.findByCardId(srcCardId);
        if (Objects.isNull(faceDataMap) || Objects.isNull(faceData)) {
            return;
        }
        faceDataService.createFaceData(userId, desCardId, faceData.getFaceData());
    }

    /**
     * 复制指纹信息
     *
     * @param userId    用户id
     * @param srcCardId 原卡id
     * @param desCardId 模板卡id
     */
    private void copyFingerInfo(Integer userId, Integer srcCardId, Integer desCardId) {
        //删除目标卡的指纹
        fingerprintService.deleteByCardId(desCardId);
        FingerPrintCardMap fingerPrintCardMap = fingerPrintCardMapService.findByCardId(srcCardId);
        if (Objects.isNull(fingerPrintCardMap)) {
            return;
        }
        List<FingerPrint> fingerPrintList = fingerprintService.findByFingerPrintId(fingerPrintCardMap.getFingerPrintId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            return;
        }
        //添加指纹
        List<FingerprintDTO> fingerprintDTOList = new ArrayList<>(fingerPrintList.size());
        for (FingerPrint fingerPrint : fingerPrintList) {
            FingerprintDTO fingerprintDTO = new FingerprintDTO();
            fingerprintDTO.setCardId(desCardId);
            fingerprintDTO.setFingerPrintNo(fingerPrint.getFingerPrintNo());
            fingerprintDTO.setFingerPrintData(fingerPrint.getFingerPrintData());
            fingerprintDTO.setVendor(fingerPrintCardMap.getVendor());
            fingerprintDTOList.add(fingerprintDTO);
        }
        //添加指纹与卡的映射关系
        fingerprintService.saveFingerprint(userId, fingerprintDTOList);
    }


    @Override
    public boolean copyCard(Integer userId, String srcCardCode, List<String> tarCardCodeList) {
        tarCardCodeList.add(srcCardCode);
        List<Card> tarCardList = this.findCardByCardCodes(tarCardCodeList);
        Map<String, Integer> codeIdMap = tarCardList.stream()
                                                    .collect(Collectors.toMap(Card::getCardCode, Card::getCardId));
        if (!codeIdMap.containsKey(srcCardCode)) {
            log.error("复制卡：源卡不存在,{}", srcCardCode);
            return false;
        }
        if (codeIdMap.size() != tarCardCodeList.size()) {
            log.error("复制卡：部分目标卡不存在,{}", tarCardCodeList);
            return false;
        }
        //获取源卡绑定的门设备
        Integer srcCardId = codeIdMap.get(srcCardCode);
        List<DoorCardInfoDTO> srcCopyList = doorCardService.findDoorCardInfoByCardIds(List.of(srcCardId));
        if (CollUtil.isEmpty(srcCopyList)) {
            log.info("源卡：{}没有绑定门",srcCardCode);
            return true;
        }

        //获取目标卡绑定的门设备信息
        List<Integer> tarCardIdList = codeIdMap.values()
                                      .stream()
                                      .filter(cardId -> Objects.equals(cardId, srcCardId))
                                      .toList();
        Map<Integer, List<DoorCardInfoDTO>> allTarCopyMap = doorCardService.findDoorCardInfoByCardIds(tarCardIdList)
                                                                           .stream()
                                                                           .collect(Collectors.groupingBy(DoorCardInfoDTO::getCardId));
        for (String tarCardCode : tarCardCodeList) {
            Integer tarCardId = codeIdMap.get(tarCardCode);
            List<DoorCardInfoDTO> tarCopyList = allTarCopyMap.get(tarCardId);
            //已经存在卡授权的去除
            List<DoorCardInfoDTO> addList = CollUtil.subtractToList(srcCopyList, tarCopyList);
            for (DoorCardInfoDTO cardInfoDTO : addList) {
                doorCommandManager.addManualDoorCard(userId, List.of(cardInfoDTO.getEquipmentId()), cardInfoDTO.getTimeGroupId(), List.of(tarCardId), FeatureEnum.CARD.getType());
            }
        }
        return true;
    }

    @Override
    public void updateEndTime(List<Integer> cardIdList, Date endTime) {
        if (CollUtil.isEmpty(cardIdList)) {
            return;
        }
        cardMapper.update(Wrappers.lambdaUpdate(Card.class).set(Card::getEndTime, endTime).in(Card::getCardId, cardIdList));
    }

    private Card findCardByCardCode(String cardCode) {
        return cardMapper.selectOne(Wrappers.lambdaQuery(Card.class)
                                            .eq(Card::getCardCode, cardCode));
    }

    private List<Card> findCardByCardCodes(List<String> cardCodes) {
        if (CollUtil.isEmpty(cardCodes)) {
            return Collections.emptyList();
        }
        return cardMapper.selectList(Wrappers.lambdaQuery(Card.class)
                                            .in(Card::getCardCode, cardCodes));
    }

    /**
     * 卡是否已存在
     *
     * @param cardCode     卡号
     * @param cardCodeType 卡类型
     * @return {@link Boolean}
     */
    public Boolean isExistCard(Integer cardId, String cardCode, Integer cardCodeType) {
        return cardMapper.countByCondition(cardId, cardCode, cardCodeType) > 0;
    }
}
