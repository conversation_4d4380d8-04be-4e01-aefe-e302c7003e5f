package com.siteweb.accesscontrol.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.CardValidDateDTO;
import com.siteweb.accesscontrol.vo.AccessCardAuthorizationVO;

import java.util.List;

public interface CardAuthorizationService {
    /**
     * @param userId       卡授权id
     * @param equipmentIds 设备id
     * @param cardIds 卡id
     * @param timeGroupId 准近事件组
     * @param flag 卡授权标识位
     * @return {@link Boolean}
     */
    Boolean addAccessCardAuthorization(Integer userId, List<Integer> equipmentIds, List<Integer> cardIds, Integer timeGroupId, Integer flag);

    Page<AccessCardAuthorizationVO> findCardAuthorizationPage(Page page, String cardCode, Integer equipmentId, Integer cardGroup, Integer areaId, Integer userId, String stationName) throws InterruptedException;

    /**
     * 修改卡的授权结束时间
     * @param cardValidDateDTO 卡的授权结束时间修改
     */
    void updateAccessCardAuthorizationDate(CardValidDateDTO cardValidDateDTO);
}
