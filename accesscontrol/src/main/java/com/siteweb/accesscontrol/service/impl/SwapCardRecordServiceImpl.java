package com.siteweb.accesscontrol.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.entity.SwapCardRecordDTO;
import com.siteweb.accesscontrol.mapper.SwapCardRecordMapper;
import com.siteweb.accesscontrol.service.SwapCardRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class SwapCardRecordServiceImpl implements SwapCardRecordService {
    @Autowired
    private SwapCardRecordMapper swapCardRecordMapper;

    @Override
    public SwapCardRecordDTO findLastRecordByEquipmentId(Integer equipmentId) {
        return swapCardRecordMapper.findLastRecordByEquipmentId(equipmentId);
    }

    @Override
    public Page<SwapCardRecordDTO> findSwapCardRecordPage(Page page, Integer userId,Integer equipmentId, Integer areaId, Date startTime, Date endTime) {
        return swapCardRecordMapper.findSwapCardRecordPage(page, userId, equipmentId, areaId, startTime, endTime);
    }
}
