package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.DoorTag;
import com.siteweb.accesscontrol.mapper.DoorTagMapper;
import com.siteweb.accesscontrol.service.DoorTagMapService;
import com.siteweb.accesscontrol.service.DoorTagService;
import com.siteweb.accesscontrol.vo.DoorTagVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class DoorTagServiceImpl implements DoorTagService {
    @Autowired
    DoorTagMapper doorTagMapper;
    @Autowired
    DoorTagMapService doorTagMapService;

    @Override
    public List<DoorTag> findAll(){
        return doorTagMapper.selectList(Wrappers.emptyWrapper());
    }
    @Override
    public DoorTag findById(Integer tagId){
        return doorTagMapper.selectById(tagId);
    }
    @Override
    public int insert(DoorTag doorTag){
        return doorTagMapper.insert(doorTag);
    }
    @Override
    public int updateById(DoorTag doorTag){
        return doorTagMapper.updateById(doorTag);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteById(Integer tagId){
        doorTagMapService.deleteByTagIds(List.of(tagId));
        return doorTagMapper.deleteById(tagId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByIds(List<Integer> doorTagIds) {
        if (CollUtil.isEmpty(doorTagIds)) {
            return 0;
        }
        doorTagMapService.deleteByTagIds(doorTagIds);
        return doorTagMapper.deleteByIds(doorTagIds);
    }

    @Override
    public List<DoorTagVO> findDoorTagByEquipmentIds(List<Integer> doorEquipmentIds) {
        if (CollUtil.isEmpty(doorEquipmentIds)) {
            return Collections.emptyList();
        }
        return doorTagMapper.findDoorTagByEquipmentIds(doorEquipmentIds);
    }
}
