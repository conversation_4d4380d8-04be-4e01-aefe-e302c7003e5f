package com.siteweb.accesscontrol.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.DoorAreaMap;
import com.siteweb.accesscontrol.mapper.DoorAreaMapMapper;
import com.siteweb.accesscontrol.service.DoorAreaMapService;
import com.siteweb.accesscontrol.vo.DoorAreaMapCreateRequest;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.monitoring.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class DoorAreaMapServiceImpl implements DoorAreaMapService {
    @Autowired
    private DoorAreaMapMapper doorAreaMapMapper;
    @Autowired
    private EquipmentService equipmentService;

    @Override
    public List<DoorAreaMap> findAll() {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return doorAreaMapMapper.selectList(Wrappers.lambdaQuery(DoorAreaMap.class).in(DoorAreaMap::getEquipmentId, equipmentIds));
    }

    @Override
    public List<DoorAreaMap> findByAreaId(Integer areaId) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return doorAreaMapMapper.selectList(Wrappers.lambdaQuery(DoorAreaMap.class)
                                                    .eq(DoorAreaMap::getAreaId, areaId)
                                                    .in(DoorAreaMap::getEquipmentId, equipmentIds));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createDoorAreaMap(DoorAreaMapCreateRequest doorAreaMapCreateRequest) {
        this.removeByAreaId(doorAreaMapCreateRequest.getAreaId());
        if (CollUtil.isEmpty(doorAreaMapCreateRequest.getEquipmentIdList())) {
            return;
        }
        List<DoorAreaMap> doorAreaMapList = doorAreaMapCreateRequest.getEquipmentIdList()
                                                                    .stream()
                                                                    .map(equipmentId -> new DoorAreaMap(doorAreaMapCreateRequest.getAreaId(), equipmentId))
                                                                    .toList();
        doorAreaMapMapper.batchInsert(doorAreaMapList);
    }

    @Override
    public void deleteByAreaId(Integer areaId) {
        doorAreaMapMapper.delete(Wrappers.lambdaQuery(DoorAreaMap.class)
                                         .eq(DoorAreaMap::getAreaId, areaId));
    }

    private void removeByAreaId(Integer areaId){
        doorAreaMapMapper.delete(Wrappers.lambdaQuery(DoorAreaMap.class)
                                         .eq(DoorAreaMap::getAreaId, areaId));
    }
}
