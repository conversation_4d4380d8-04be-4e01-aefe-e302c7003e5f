package com.siteweb.accesscontrol.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.faceDataAuth;
import com.siteweb.accesscontrol.mapper.FaceDataAuthMapper;
import com.siteweb.accesscontrol.service.FaceDataAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FaceDataAuthServiceImpl implements FaceDataAuthService {
    @Autowired
    FaceDataAuthMapper faceDataAuthMapper;

    @Override
    public boolean existByCardId(Integer cardId) {
        return faceDataAuthMapper.exists(Wrappers.lambdaQuery(faceDataAuth.class)
                                                 .eq(faceDataAuth::getCardId, cardId));
    }
}
