package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.vo.CardGroupVO;
import com.siteweb.utility.entity.DataItem;

import java.util.List;

public interface CardGroupService {
    /**
     * 获取所有卡分组
     * @return {@link List}<{@link DataItem}>
     */
    List<CardGroupVO> findAll();

    /**
     * 删除卡分组根据id
     * @param id itemId
     * @return {@link Boolean}
     */
    Integer deleteById(Integer id);

    /**
     * 创建卡分组
     * @param cardGroupVO 卡分组dto
     * @return {@link CardGroupVO}
     */
    Integer createCardGroup(CardGroupVO cardGroupVO);

    /**
     * 更新卡分组
     * @param cardGroupVO 卡分组dto
     * @return {@link Integer}
     */
    Integer updateCardGroup(CardGroupVO cardGroupVO);
}
