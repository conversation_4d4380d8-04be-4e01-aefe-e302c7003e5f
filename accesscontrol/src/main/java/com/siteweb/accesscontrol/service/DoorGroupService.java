package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.DoorGroupTreeNode;
import com.siteweb.accesscontrol.entity.DoorGroup;

import java.util.List;

public interface DoorGroupService {
    Integer createDoorGroup(DoorGroup doorGroup);

    List<DoorGroup> findAll();

    Integer updateDoorGroup(DoorGroup doorGroup);

    Integer deleteById(Integer id);

    /**
     * 获取门分组树
     * @return {@link List}<{@link DoorGroupTreeNode}>
     */
    List<DoorGroupTreeNode> findDoorGroupTree();
}
