package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.entity.FaceData;

public interface FaceDataService {
    /**
     * 创建人脸信息
     * @param userId 用户id
     * @param cardId 卡id
     * @param facedDataByte 人脸信息字节数组
     * @return {@link Integer}
     */
    Integer createFaceData(Integer userId, Integer cardId, byte[] facedDataByte);

    /**
     * 根据卡id查找人脸信息
     * @param cardId 卡id
     * @return {@link FaceData}
     */
    FaceData findByCardId(Integer cardId);

    /**
     * 删除人脸信息
     * @param cardId 卡id
     * @return {@link Integer}
     */
    Integer deleteFaceData(Integer cardId);

    Integer findFaceIdByCardId(Integer cardId);
}
