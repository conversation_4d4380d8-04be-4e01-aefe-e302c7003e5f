package com.siteweb.accesscontrol.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.DoorAlarmDTO;
import com.siteweb.accesscontrol.mapper.DoorAlarmMapper;
import com.siteweb.accesscontrol.service.DoorAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class DoorAlarmServiceImpl implements DoorAlarmService {
    @Autowired
    private DoorAlarmMapper doorAlarmMapper;
    @Override
    public Page<DoorAlarmDTO> findDoorAlarm(Page page, Integer equipmentId, Date startTime, Date endTime) {
        return doorAlarmMapper.findDoorAlarm(page,equipmentId,startTime,endTime);
    }
}
