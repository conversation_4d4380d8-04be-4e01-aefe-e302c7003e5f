package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.accesscontrol.dto.TimeGroupDTO;
import com.siteweb.accesscontrol.entity.TimeGroup;
import com.siteweb.accesscontrol.mapper.TimeGroupMapper;
import com.siteweb.accesscontrol.service.TimeGroupService;
import com.siteweb.accesscontrol.vo.TimeGroupVO;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class TimeGroupServiceImpl implements TimeGroupService {
    @Autowired
    private TimeGroupMapper timeGroupMapper;
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    private TimeGroupSpanService timeGroupSpanService;
    @Override
    public List<TimeGroupDTO> findAll() {
        return timeGroupMapper.findAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByTimeGroupIds(List<Integer> timeGroupIdList) {
        timeGroupSpanService.deleteByTimeGroupIds(timeGroupIdList);
        return timeGroupMapper.deleteBatchIds(timeGroupIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createTimeGroup(TimeGroupVO timeGroupVO) {
        int timeGroupId = primaryKeyValueService.getGlobalIdentity("TBL_TimeGroup", 0);
        Integer result = timeGroupMapper.insert(new TimeGroup(timeGroupId, timeGroupVO.getTimeGroupName()));
        timeGroupSpanService.createTimeSpans(timeGroupId, timeGroupVO.getTimeSpanStrs());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateTimeGroup(TimeGroupVO timeGroupVO) {
        Integer result =  timeGroupMapper.updateById(new TimeGroup(timeGroupVO.getTimeGroupId(), timeGroupVO.getTimeGroupName()));
        timeGroupSpanService.updateTimeSpans(timeGroupVO.getTimeGroupId(), timeGroupVO.getTimeSpanStrs());
        return result;
    }

    @Override
    public List<TimeGroup> findByEquipmentId(Integer equipmentId) {
        return timeGroupMapper.findByEquipmentIds(List.of(equipmentId));
    }
}
