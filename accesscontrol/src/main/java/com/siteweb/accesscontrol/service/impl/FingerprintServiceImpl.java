package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.dto.FingerprintDTO;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.entity.FingerPrintCardMap;
import com.siteweb.accesscontrol.mapper.FingerprintMapper;
import com.siteweb.accesscontrol.service.FingerPrintCardMapService;
import com.siteweb.accesscontrol.service.FingerprintService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
public class FingerprintServiceImpl implements FingerprintService {
    @Autowired
    private FingerprintMapper fingerprintMapper;
    @Autowired
    private FingerPrintCardMapService fingerPrintCardMapService;
    @Autowired
    PrimaryKeyValueService primaryKeyValueService;
    @Override
    public List<FingerPrint> findCardFingerprintByVendor(Integer cardId, Integer vendorId) {
        return fingerprintMapper.findCardFingerprintByVendor(cardId,vendorId);
    }

    @Override
    public Integer findCardFingerprintIdByVendor(Integer cardId, Integer vendorId) {
        return fingerprintMapper.findCardFingerprintIdByVendor(cardId,vendorId);
    }

    @Override
    public List<ChildEquipment> findChildEquipment(Integer equipmentId, Integer equipmentCategory, Integer vendorId) {
        return fingerprintMapper.findChildEquipment(equipmentId,equipmentCategory,vendorId);
    }

    @Override
    public Integer findFingerPrintCategoryByEquipmentId(Integer equipmentId) {
        return fingerprintMapper.findFingerPrintCategoryByEquipmentId(equipmentId);
    }

    @Override
    public List<FingerPrint> findFingerprintData(Integer cardId, Integer vendor) {
        return fingerprintMapper.findFingerprintData(cardId,vendor);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Integer cardId) {
        fingerprintMapper.deleteByCardId(cardId);
        fingerPrintCardMapService.deleteByCardId(cardId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFingerprint(Integer userId, List<FingerprintDTO> fingerPrintDTOList) {
        if (CollUtil.isEmpty(fingerPrintDTOList)) {
            return 0;
        }
        Integer cardId = fingerPrintDTOList.get(0).getCardId();
        Integer vendor = fingerPrintDTOList.get(0).getVendor();
        List<Integer> deleteFingerprintNos = new ArrayList<>(fingerPrintDTOList.size());
        Map<Integer, byte[]> addFingerprintNoMap = new HashMap<>(fingerPrintDTOList.size());
        //获取需要新增的指纹数据和需要删除的指纹数据
        for (FingerprintDTO fingerprintDTO : fingerPrintDTOList) {
            if (fingerprintDTO.fingerprintDataEmpty()) {
                deleteFingerprintNos.add(fingerprintDTO.getFingerPrintNo());
                continue;
            }
            addFingerprintNoMap.put(fingerprintDTO.getFingerPrintNo(),fingerprintDTO.getFingerPrintData());
        }
        Integer fingerprintId = getFingerprintId(cardId, vendor);
        createFingerprintMap(userId, cardId, vendor, fingerprintId, addFingerprintNoMap);
        deleteByFingerprintIdAndFingerprintNos(fingerprintId, deleteFingerprintNos);
        addByFingerprintIdAndFingerprintNos(fingerprintId, addFingerprintNoMap);
        if (existsByFingerprintId(fingerprintId)) {
            //更新指纹操作的用户与更新时间
            fingerPrintCardMapService.updateUserIdAndTimeByCardId(cardId, userId, new Date());
            return fingerPrintDTOList.size();
        }
        //不存在直接删除指纹卡映射
        fingerPrintCardMapService.deleteByFingerprintIdAndVendor(fingerprintId, vendor);
        return fingerPrintDTOList.size();
    }

    @Override
    public List<FingerPrint> findByFingerPrintId(Integer fingerPrintId) {
        return fingerprintMapper.selectList(Wrappers.lambdaQuery(FingerPrint.class).eq(FingerPrint::getFingerPrintId, fingerPrintId));
    }

    private void addByFingerprintIdAndFingerprintNos(Integer fingerprintId, Map<Integer, byte[]> addFingerprintNoMap) {
        if (CollUtil.isEmpty(addFingerprintNoMap)) {
            return;
        }
        for (Map.Entry<Integer, byte[]> fingerEntry : addFingerprintNoMap.entrySet()) {
            Integer fingerprintNo = fingerEntry.getKey();
            byte[] fingerprintData = fingerEntry.getValue();
            //指纹是否已存在
            if (existsByFingerprintIdAndFingerprintNo(fingerprintId, fingerprintNo)) {
                //更新指纹数据
                updateFingerprintData(fingerprintId, fingerprintNo, fingerprintData);
                continue;
            }
            //添加指纹数据
            fingerprintMapper.insert(new FingerPrint(fingerprintId, fingerprintNo, fingerprintData));
        }
    }

    /**
     * 添加卡与指纹id的映射关系
     * @param userId 用户id
     * @param cardId 卡id
     * @param vendor 厂商
     * @param fingerprintId 指纹id
     * @param addFingerprintNos 需要添加的指纹编号
     */
    private void createFingerprintMap(Integer userId, Integer cardId, Integer vendor, Integer fingerprintId, Map<Integer, byte[]> addFingerprintNos) {
        //没有需要添加的指纹编号 或 已存在卡指纹映射关系
        if (CollUtil.isEmpty(addFingerprintNos) || fingerPrintCardMapService.existsByCardId(cardId)) {
            return;
        }
        Date nowDate = new Date();
        FingerPrintCardMap fingerPrintCardMap = new FingerPrintCardMap(cardId, fingerprintId, vendor, userId, nowDate);
        fingerPrintCardMapService.batchInsert(List.of(fingerPrintCardMap));
    }

    /**
     * 获取指纹id
     * @param cardId 卡id
     * @param vendor 厂商id
     * @return {@link Integer}
     */
    private Integer getFingerprintId(Integer cardId, Integer vendor) {
        if (fingerPrintCardMapService.existsByCardId(cardId)) {
            return findFingerprintIdByCardIdAndVendor(cardId, vendor);
        }
        return primaryKeyValueService.getGlobalIdentity("TBL_Fingerprint", 0);
    }

    private boolean existsByFingerprintId(Integer fingerPrintId) {
        return fingerprintMapper.exists(Wrappers.lambdaQuery(FingerPrint.class)
                                                .eq(FingerPrint::getFingerPrintId, fingerPrintId));
    }

    /**
     * @param fingerPrintId 指纹id
     * @param fingerPrintNos 需要删除的指纹编号
     */
    private void deleteByFingerprintIdAndFingerprintNos(Integer fingerPrintId, List<Integer> fingerPrintNos) {
        if (CollUtil.isEmpty(fingerPrintNos)) {
            return;
        }
        fingerprintMapper.delete(Wrappers.lambdaQuery(FingerPrint.class)
                                         .eq(FingerPrint::getFingerPrintId, fingerPrintId)
                                         .in(FingerPrint::getFingerPrintNo, fingerPrintNos));
    }

    public Integer findFingerprintIdByCardIdAndVendor(Integer cardId, Integer vendor){
        return fingerprintMapper.findFingerprintIdByCardId(cardId, vendor);
    }

    public boolean existsByFingerprintIdAndFingerprintNo(Integer fingerPrintId, Integer fingerPrintNo) {
        return fingerprintMapper.exists(Wrappers.lambdaQuery(FingerPrint.class)
                                                .eq(FingerPrint::getFingerPrintId, fingerPrintId)
                                                .eq(FingerPrint::getFingerPrintNo, fingerPrintNo));
    }

    public boolean updateFingerprintData(Integer fingerPrintId, Integer fingerPrintNo, byte[] fingerPrintData) {
        return fingerprintMapper.update(null, Wrappers.lambdaUpdate(FingerPrint.class)
                                                      .set(FingerPrint::getFingerPrintData, fingerPrintData)
                                                      .eq(FingerPrint::getFingerPrintId, fingerPrintId)
                                                      .eq(FingerPrint::getFingerPrintNo, fingerPrintNo)) > 0;
    }
}
