package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.FaceDataMap;
import com.siteweb.accesscontrol.mapper.FaceDataMapMapper;
import com.siteweb.accesscontrol.service.FaceDataMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class FaceDataMapServiceImpl implements FaceDataMapService {
    @Autowired
    private FaceDataMapMapper faceDataMapMapper;
    @Override
    public Integer createFaceDataMap(Integer userId, Integer cardId, Integer faceId) {
        FaceDataMap faceDataMap = new FaceDataMap(cardId, faceId, userId, new Date());
        return faceDataMapMapper.insert(faceDataMap);
    }

    @Override
    public Integer deleteByCardId(Integer cardId) {
        return faceDataMapMapper.delete(Wrappers.lambdaQuery(FaceDataMap.class)
                                         .eq(FaceDataMap::getCardId, cardId));
    }

    @Override
    public Integer findFaceIdByCardId(Integer cardId) {
        return faceDataMapMapper.findFaceIdByCardId(cardId);
    }

    @Override
    public FaceDataMap findByCardId(Integer cardId) {
        return faceDataMapMapper.selectOne(Wrappers.lambdaQuery(FaceDataMap.class)
                                                   .eq(FaceDataMap::getCardId, cardId));
    }

    @Override
    public void batchInsert(List<FaceDataMap> faceDataMapList) {
        if (CollUtil.isEmpty(faceDataMapList)) {
            return;
        }
        faceDataMapMapper.batchInsert(faceDataMapList);
    }

    @Override
    public boolean existsByCardId(Integer cardId) {
        return faceDataMapMapper.exists(Wrappers.lambdaQuery(FaceDataMap.class)
                                                .eq(FaceDataMap::getCardId, cardId));
    }

    @Override
    public int update(FaceDataMap faceDataMap) {
        return faceDataMapMapper.update(null,
                Wrappers.lambdaUpdate(FaceDataMap.class).set(FaceDataMap::getFaceId, faceDataMap.getFaceId())
                        .set(FaceDataMap::getLastUpdater, faceDataMap.getLastUpdater())
                        .set(FaceDataMap::getLastUpdateTime, faceDataMap.getLastUpdateTime())
                        .eq(FaceDataMap::getCardId, faceDataMap.getCardId()));
    }
}
