package com.siteweb.accesscontrol.service.impl;

import com.siteweb.accesscontrol.entity.CardTypeMap;
import com.siteweb.accesscontrol.mapper.CardTypeMapMapper;
import com.siteweb.accesscontrol.service.CardTypeMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CardTypeMapServiceImpl implements CardTypeMapService {
    @Autowired
    private CardTypeMapMapper cardTypeMapMapper;

    @Override
    public Integer saveCardTypeMap(Integer cardId, Integer cardType) {
        CardTypeMap cardTypeMap = new CardTypeMap(cardId, cardType);
        return cardTypeMapMapper.insert(cardTypeMap);
    }

    @Override
    public Integer updateCardTypeMap(Integer cardId, Integer cardType) {
        CardTypeMap cardTypeMap = new CardTypeMap(cardId, cardType);
        return cardTypeMapMapper.updateById(cardTypeMap);
    }

    @Override
    public Integer deleteById(Integer cardId) {
        return cardTypeMapMapper.deleteById(cardId);
    }

}
