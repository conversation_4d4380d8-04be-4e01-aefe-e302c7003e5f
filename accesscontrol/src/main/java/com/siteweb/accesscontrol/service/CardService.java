package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.AccessCardDTO;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.vo.AccessCardVO;

import java.util.Date;
import java.util.List;

public interface CardService {
    /**
     * 将指定组中的卡归纳到未分组中
     * @param cardGroupId
     * @return {@link Integer}
     */
    Integer updateCardToUngrouped(Integer cardGroupId);

    /**
     * 添加卡
     * @param accessCardVO 卡dto
     * @return {@link Integer}
     */
    Integer createCard(AccessCardVO accessCardVO);

    /**
     * 更新门卡
     * @param accessCardVO 卡dto
     * @return {@link Integer}
     */
    Integer updateCard(AccessCardVO accessCardVO);

    /**
     * @param id 门禁卡id
     * @return {@link AccessCardDTO}
     */
    AccessCardDTO findCardById(Integer id);

    /**
     * @param cardGroup 卡分组id
     * @return {@link AccessCardDTO}
     */
    List<AccessCardDTO> findCardByCardGroup(Integer cardGroup);

    /**
     * 删除门禁卡
     * @param id 门禁卡id
     * @return {@link Integer}
     */
    Integer deleteById(Integer userId,Integer id);

    /**
     * 更新卡的状态
     * @param cardId 门禁卡id
     * @param status 门禁卡状态
     * @return {@link Integer}
     */
    Integer updateCardStatus(Integer cardId,Integer status);

    /**
     * 获取卡基本信息
     * @param cardId 卡id
     * @return {@link AccessCardInfoDTO}
     */
    AccessCardInfoDTO findInfoByCardId(Integer cardId);

    /**
     * @param userId 用户id
     * @param srcCardCode 原卡号
     * @param dstCardCode 目标卡号
     * @param delfLag     是否删除丢失的卡
     * @return boolean 是否成功
     */
    boolean lostCard(Integer userId, String srcCardCode, String dstCardCode, Boolean lostFlag);

    /**
     * 复制卡
     * @param userId 用户id
     * @param srcCardCode 源卡号
     * @param tarCardCodeList 目标卡号列表
     * @return boolean
     */
    boolean copyCard(Integer userId, String srcCardCode, List<String> tarCardCodeList);

    /**
     * 更新卡的结束事件
     * @param cardIdList 卡id列表
     * @param endTime 结束时间
     */
    void updateEndTime(List<Integer> cardIdList, Date endTime);
}
