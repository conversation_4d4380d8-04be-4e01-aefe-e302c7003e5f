package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.entity.FaceDataMap;

import java.util.List;

public interface FaceDataMapService {
    /**
     * 创建卡人脸映射
     * @param userId 用户id
     * @param cardId 门禁卡id
     * @param faceId 人脸id
     * @return {@link Integer}
     */
    Integer createFaceDataMap(Integer userId, Integer cardId, Integer faceId);

    /**
     * 删除卡人脸映射
     * @param cardId
     * @return {@link Integer}
     */
    Integer deleteByCardId(Integer cardId);

    /**
     * 根据卡id获取人脸id
     * @param cardId 卡id
     * @return {@link Integer}
     */
    Integer findFaceIdByCardId(Integer cardId);

    FaceDataMap findByCardId(Integer cardId);

    void batchInsert(List<FaceDataMap> faceDataMapList);

    boolean existsByCardId(Integer cardId);

    int update(FaceDataMap faceDataMap);
}
