package com.siteweb.accesscontrol.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.entity.SwapCardRecordDTO;

import java.util.Date;

public interface SwapCardRecordService {

    /**
     * 获取最新的刷卡记录
     * @param equipmentId 设备id
     * @return {@link SwapCardRecordDTO}
     */
    SwapCardRecordDTO findLastRecordByEquipmentId(Integer equipmentId);

    /**
     * 获取刷卡记录，分页
     *
     * @param page        分页信息
     * @param userId 用户id
     * @param equipmentId 设备id
     * @param areaId      区域id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return {@link IPage}<{@link SwapCardRecordDTO}>
     */
    IPage<SwapCardRecordDTO> findSwapCardRecordPage(Page page, Integer userId, Integer equipmentId, Integer areaId, Date startTime, Date endTime);
}
