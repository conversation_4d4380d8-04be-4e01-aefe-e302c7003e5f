package com.siteweb.accesscontrol.service.impl;

import com.siteweb.accesscontrol.entity.CardExt;
import com.siteweb.accesscontrol.mapper.CardExtMapper;
import com.siteweb.accesscontrol.service.CardExtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CardExtServiceImpl implements CardExtService {
    @Autowired
    private CardExtMapper cardExtMapper;
    @Override
    public Integer saveCardExt(CardExt cardExt) {
        return cardExtMapper.insert(cardExt);
    }

    @Override
    public Integer updateCardExt(CardExt cardExt) {
        return cardExtMapper.updateById(cardExt);
    }

    @Override
    public Integer deleteById(Integer id) {
        return cardExtMapper.deleteById(id);
    }
}
