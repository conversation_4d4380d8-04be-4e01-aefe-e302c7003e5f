package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.TimeGroupDTO;
import com.siteweb.accesscontrol.entity.TimeGroup;
import com.siteweb.accesscontrol.vo.TimeGroupVO;

import java.util.List;

public interface TimeGroupService {
    List<TimeGroupDTO> findAll();

    Integer deleteByTimeGroupIds(List<Integer> timeGroupIdList);

    Integer createTimeGroup(TimeGroupVO timeGroupVO);

    Integer updateTimeGroup(TimeGroupVO timeGroupVO);

    List<TimeGroup> findByEquipmentId(Integer equipmentId);
}
