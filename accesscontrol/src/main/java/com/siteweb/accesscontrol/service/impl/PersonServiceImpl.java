package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.accesscontrol.dto.PersonDTO;
import com.siteweb.accesscontrol.mapper.PersonMapper;
import com.siteweb.accesscontrol.service.PersonService;
import com.siteweb.admin.service.DepartmentPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class PersonServiceImpl implements PersonService {
    @Autowired
    PersonMapper personMapper;
    @Autowired
    DepartmentPermissionService departmentPermissionService;
    @Override
    public List<PersonDTO> findPersons(Integer userId) {
        Set<Integer> departmentIds = departmentPermissionService.findDepartmentPermissionByUserId(userId);
        if (CollUtil.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return personMapper.findPersons(departmentIds);
    }
}
