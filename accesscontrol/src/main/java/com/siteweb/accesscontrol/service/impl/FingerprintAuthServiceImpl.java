package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.DoorCard;
import com.siteweb.accesscontrol.entity.FingerPrintCardMap;
import com.siteweb.accesscontrol.entity.FingerprintAuth;
import com.siteweb.accesscontrol.mapper.FingerprintAuthMapper;
import com.siteweb.accesscontrol.service.DoorCardService;
import com.siteweb.accesscontrol.service.DoorService;
import com.siteweb.accesscontrol.service.FingerPrintCardMapService;
import com.siteweb.accesscontrol.service.FingerprintAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class FingerprintAuthServiceImpl implements FingerprintAuthService {
    @Autowired
    private FingerprintAuthMapper fingerprintAuthMapper;
    @Autowired
    private FingerPrintCardMapService fingerPrintCardMapService;
    @Autowired
    DoorService doorService;
    @Autowired
    DoorCardService doorCardService;
    @Override
    public Integer deleteByCardId(Integer cardId) {
        return fingerprintAuthMapper.deleteByCardId(cardId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAuthFingerPrint(Integer stationId, Integer equipmentId, Integer cardId, Integer fingerPrintId, Integer vendorId) {
        List<FingerPrintCardMap> fingerPrintCardMap = fingerPrintCardMapService.findFingerPrintCardMap(fingerPrintId, vendorId);
        if (CollUtil.isEmpty(fingerPrintCardMap)) {
            return;
        }
        Integer doorId = doorService.findDoorIdByEquipmentId(equipmentId);
        if (Objects.isNull(doorId)) {
            log.error("添加指纹授权失败，设备id:{},没有找到对应的门id", equipmentId);
            return;
        }
        List<DoorCard> doorCardList = doorCardService.findByDoorIdAndCardId(doorId, cardId);
        if (CollUtil.isEmpty(doorCardList)) {
            log.error("添加指纹授权失败，门id{},卡id:{},没有找到对应的门id卡映射关系", doorId, cardId);
            return;
        }
        //先删除
        fingerprintAuthMapper.deleteFingerprintAuth(stationId, equipmentId, cardId, fingerPrintId);
        //插入数据
        createFingerprintAuth(fingerPrintId, doorCardList);
    }

    private void createFingerprintAuth(Integer fingerPrintId, List<DoorCard> doorCardList) {
        if (CollUtil.isEmpty(doorCardList) || Objects.isNull(fingerPrintId)) {
            return;
        }
        List<FingerprintAuth> fingerprintAuthList = new ArrayList<>(doorCardList.size());
        for (DoorCard doorCard : doorCardList) {
            FingerprintAuth build = FingerprintAuth.builder().doorId(doorCard.getDoorId()).cardId(doorCard.getCardId())
                                                   .timeGroupId(doorCard.getTimeGroupId()).fingerPrintId(fingerPrintId)
                                                   .lastUpdateTime(new Date()).build();
            fingerprintAuthList.add(build);
        }
        fingerprintAuthMapper.saveAuthFingerPrint(fingerprintAuthList);
    }

    @Override
    public void deleteFingerprintAuth(Integer stationId, Integer equipmentId, Integer cardId, Integer fingerPrintId, Integer vendorId) {
        List<FingerPrintCardMap> fingerPrintCardMap = fingerPrintCardMapService.findFingerPrintCardMap(fingerPrintId, vendorId);
        if (CollUtil.isEmpty(fingerPrintCardMap)) {
            return;
        }
        fingerprintAuthMapper.deleteFingerprintAuth(stationId,equipmentId,cardId,fingerPrintId);
    }

    @Override
    public boolean existByCardId(Integer cardId) {
        return fingerprintAuthMapper.exists(Wrappers.lambdaQuery(FingerprintAuth.class)
                                                    .eq(FingerprintAuth::getCardId, cardId));
    }

    @Override
    public boolean existCardFingerprintAuth(Integer cardId) {
        return fingerprintAuthMapper.cardFingerprintAuthCount(cardId) > 0;
    }
}
