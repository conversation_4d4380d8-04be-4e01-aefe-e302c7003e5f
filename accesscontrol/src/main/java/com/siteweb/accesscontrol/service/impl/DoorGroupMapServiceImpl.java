package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.DoorGroupMap;
import com.siteweb.accesscontrol.mapper.DoorGroupMapMapper;
import com.siteweb.accesscontrol.service.DoorGroupMapService;
import com.siteweb.accesscontrol.vo.DoorGroupMapCreateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DoorGroupMapServiceImpl implements DoorGroupMapService {
    @Autowired
    private DoorGroupMapMapper doorGroupMapMapper;

    @Override
    public Integer deleteByDoorGroupId(Integer doorGroupId) {
        return doorGroupMapMapper.delete(Wrappers.lambdaQuery(DoorGroupMap.class)
                                                 .eq(DoorGroupMap::getDoorGroupId, doorGroupId));
    }

    @Override
    public List<DoorGroupMap> findAll() {
        return doorGroupMapMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateDoorGroupMap(DoorGroupMapCreateRequest doorGroupMapCreateRequest) {
        this.deleteByDoorGroupId(doorGroupMapCreateRequest.getDoorGroupId());
        if (CollUtil.isEmpty(doorGroupMapCreateRequest.getEquipmentIdList())) {
            return 0;
        }
        List<DoorGroupMap> doorGroupMapList = doorGroupMapCreateRequest.getEquipmentIdList()
                                                                       .stream()
                                                                       .map(equipmentId -> new DoorGroupMap(doorGroupMapCreateRequest.getDoorGroupId(),equipmentId))
                                                                       .toList();
        //批量添加
        return doorGroupMapMapper.batchInsert(doorGroupMapList);
    }
}
