package com.siteweb.accesscontrol.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.FaceData;
import com.siteweb.accesscontrol.entity.FaceDataMap;
import com.siteweb.accesscontrol.mapper.FaceDataMapper;
import com.siteweb.accesscontrol.service.FaceDataMapService;
import com.siteweb.accesscontrol.service.FaceDataService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class FaceDataServiceImpl implements FaceDataService {
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    private FaceDataMapper faceDataMapper;
    @Autowired
    private FaceDataMapService faceDataMapService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createFaceData(Integer userId, Integer cardId, byte[] faceDataByte) {
        //存在更新
        if (faceDataMapService.existsByCardId(cardId)) {
            updateFaceData(userId, cardId, faceDataByte);
            return 1;
        }
        //不存在创建
        int faceId = primaryKeyValueService.getGlobalIdentity("TBL_FaceData", 0);
        FaceData faceData = new FaceData(faceId, faceDataByte);
        faceDataMapper.insert(faceData);
        return faceDataMapService.createFaceDataMap(userId, cardId, faceId);
    }

    private void updateFaceData(Integer userId, Integer cardId, byte[] faceDataByte) {
        FaceDataMap faceDataMap = faceDataMapService.findByCardId(cardId);
        faceDataMap.setLastUpdateTime(new Date());
        faceDataMap.setLastUpdater(userId);
        faceDataMapService.update(faceDataMap);
        faceDataMapper.update(null, Wrappers.lambdaUpdate(FaceData.class)
                                            .set(FaceData::getFaceData, faceDataByte)
                                            .eq(FaceData::getFaceId, faceDataMap.getFaceId()));
    }

    @Override
    public FaceData findByCardId(Integer cardId) {
        return faceDataMapper.findByCardId(cardId);
    }

    /**
     * 删除卡的人脸信息
     * @param cardId 卡id
     */
    public Integer deleteFaceData(Integer cardId){
        //存在人脸授权不允许删除
        faceDataMapper.deleteByCardId(cardId);
        return faceDataMapService.deleteByCardId(cardId);
    }

    @Override
    public Integer findFaceIdByCardId(Integer cardId) {
        return faceDataMapper.findFaceIdByCardId(cardId);
    }
}
