package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.dto.FingerprintDTO;
import com.siteweb.accesscontrol.entity.FingerPrint;

import java.util.List;

public interface FingerprintService {
    /**
     * 通过卡id与供应商id找到指纹信息
     *
     * @param cardId   卡id
     * @param vendorId 供应商id
     * @return {@link FingerPrint}
     */
    List<FingerPrint> findCardFingerprintByVendor(Integer cardId, Integer vendorId);

    /**
     * 通过卡id与供应商id找到指纹id
     * @param cardId 卡id
     * @param vendorId 供应商id
     * @return {@link Integer}
     */
    Integer findCardFingerprintIdByVendor(Integer cardId, Integer vendorId);

    /**
     *
     * 获取子设备指纹仪
     *
     * @param equipmentId 设备id
     * @param vendorId    供应商id
     * @return {@link ChildEquipment}
     */
    List<ChildEquipment> findChildEquipment(Integer equipmentId,Integer equipmentType,Integer vendorId);

    Integer findFingerPrintCategoryByEquipmentId(Integer equipmentId);

    List<FingerPrint> findFingerprintData(Integer cardId, Integer vendor);

    void deleteByCardId(Integer cardId);

    /**
     * 保存指纹信息
     *
     * @param userId 用户id
     * @param fingerPrintDTOList 指纹信息列表
     * @return int
     */
    int saveFingerprint(Integer userId, List<FingerprintDTO> fingerPrintDTOList);

    List<FingerPrint> findByFingerPrintId(Integer fingerPrintId);
}
