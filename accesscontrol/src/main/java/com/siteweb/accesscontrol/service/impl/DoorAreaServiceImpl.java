package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorAreaEquipmentDTO;
import com.siteweb.accesscontrol.dto.DoorAreaTreeNode;
import com.siteweb.accesscontrol.entity.DoorArea;
import com.siteweb.accesscontrol.mapper.DoorAreaMapper;
import com.siteweb.accesscontrol.service.DoorAreaMapService;
import com.siteweb.accesscontrol.service.DoorAreaService;
import com.siteweb.accesscontrol.service.DoorTagService;
import com.siteweb.accesscontrol.vo.DoorTagVO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DoorAreaServiceImpl implements DoorAreaService {
    @Autowired
    private DoorAreaMapper doorAreaMapper;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private DoorAreaMapService doorAreaMapService;
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    private DoorTagService doorTagService;

    @Override
    public List<DoorArea> findAll() {
        return doorAreaMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<DoorArea> findDoorAreas() {
        List<DoorArea> doorAreas = doorAreaMapper.selectList(Wrappers.emptyWrapper());
        DoorArea doorArea = new DoorArea();
        doorArea.setAreaId(-1);
        doorArea.setAreaName("未分区");
        doorAreas.add(doorArea);
        return doorAreas;
    }

    @Override
    public DoorArea createDoorArea(DoorArea doorArea) {
        //获取主键id
        int primaryKey = primaryKeyValueService.getGlobalIdentity("tbl_doorarea", 0);
        doorArea.setAreaId(primaryKey);
        doorAreaMapper.insert(doorArea);
        return doorArea;
    }

    @Override
    public DoorArea updateDoorArea(DoorArea doorArea) {
        doorAreaMapper.updateById(doorArea);
        return doorArea;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Integer id) {
        doorAreaMapService.deleteByAreaId(id);
        return doorAreaMapper.deleteById(id) > 0;
    }

    @Override
    public List<DoorAreaTreeNode> findDoorAreaTree() {
        List<DoorArea> doorAreaList = this.findAll();
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        List<DoorAreaEquipmentDTO> doorAreaEquipmentDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(equipmentIds)) {
            doorAreaEquipmentDTOList.addAll(doorAreaMapper.findDoorAreaEquipment(equipmentIds));
            //设置门标签
            setDoorTag(doorAreaEquipmentDTOList);
        }
        List<DoorAreaTreeNode> doorAreaTreeNodeList = new ArrayList<>();
        for (DoorArea doorArea : doorAreaList) {
            doorAreaTreeNodeList.add(new DoorAreaTreeNode(doorArea));
        }
        for (DoorAreaEquipmentDTO doorAreaEquipmentDTO : doorAreaEquipmentDTOList) {
            doorAreaTreeNodeList.add(new DoorAreaTreeNode(doorAreaEquipmentDTO));
        }
        return doorAreaTreeNodeList;
    }

    private void setDoorTag(List<DoorAreaEquipmentDTO> doorAreaEquipmentDTOList) {
        List<Integer> doorEquipmentIds = doorAreaEquipmentDTOList.stream()
                                                                 .map(DoorAreaEquipmentDTO::getEquipmentId)
                                                                 .toList();
        List<DoorTagVO> doorTagList = doorTagService.findDoorTagByEquipmentIds(doorEquipmentIds);
        Map<Integer, List<DoorTagVO>> groupedByEquipmentId = doorTagList.stream()
                                                                        .collect(Collectors.groupingBy(DoorTagVO::getEquipmentId));
        for (DoorAreaEquipmentDTO doorAreaEquipmentDTO : doorAreaEquipmentDTOList) {
            List<DoorTagVO> tags = groupedByEquipmentId.getOrDefault(doorAreaEquipmentDTO.getEquipmentId(), Collections.emptyList());
            doorAreaEquipmentDTO.setTags(tags);
        }
    }
}
