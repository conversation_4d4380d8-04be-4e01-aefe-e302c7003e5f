package com.siteweb.accesscontrol.service;

public interface FingerprintAuthService {
    /**
     * 删除卡授权指纹
     * @param cardId 卡id
     * @return {@link Integer}
     */
    Integer deleteByCardId(Integer cardId);

    void saveAuthFingerPrint(Integer stationId, Integer equipmentId, Integer cardId, Integer fingerPrintId, Integer vendorId);

    void deleteFingerprintAuth(Integer stationId, Integer equipmentId, Integer cardId, Integer fingerPrintId, Integer vendorId);

    /**
     * 判断卡是否有授权过指纹信息
     * @param cardId 卡id
     * @return boolean true 是 false 否
     */
    boolean existByCardId(Integer cardId);

    /**
     * 是否已经授权
     * @param cardId 卡id
     * @param vendor 厂商 id
     * @return boolean 是否已经授权
     */
    boolean existCardFingerprintAuth(Integer cardId);
}
