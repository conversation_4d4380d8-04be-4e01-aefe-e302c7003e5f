package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.entity.FingerPrintCardMap;
import com.siteweb.accesscontrol.mapper.FingerPrintCardMapMapper;
import com.siteweb.accesscontrol.service.FingerPrintCardMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class FingerPrintCardMapServiceImpl implements FingerPrintCardMapService {
    @Autowired
    private FingerPrintCardMapMapper fingerPrintCardMapMapper;
    @Override
    public List<FingerPrintCardMap> findFingerPrintCardMap(Integer fingerPrintId, Integer vendorId) {
        if (ObjectUtil.isNull(vendorId)) {
            return new ArrayList<>();
        }
        return this.findByVendorAndFingerprintId(vendorId, fingerPrintId);
    }

    @Override
    public void deleteByCardId(Integer cardId) {
        fingerPrintCardMapMapper.delete(Wrappers.lambdaQuery(FingerPrintCardMap.class)
                                                .eq(FingerPrintCardMap::getCardId, cardId));
    }

    @Override
    public FingerPrintCardMap findByCardId(Integer cardId) {
        return fingerPrintCardMapMapper.selectOne(Wrappers.lambdaQuery(FingerPrintCardMap.class)
                                                          .eq(FingerPrintCardMap::getCardId, cardId));
    }

    @Override
    public boolean existsByCardId(Integer cardId) {
        return fingerPrintCardMapMapper.exists(Wrappers.lambdaQuery(FingerPrintCardMap.class)
                                                       .eq(FingerPrintCardMap::getCardId, cardId));
    }

    @Override
    public void batchInsert(List<FingerPrintCardMap> fingerPrintCardMapList) {
        if (CollUtil.isEmpty(fingerPrintCardMapList)) {
            return;
        }
        fingerPrintCardMapMapper.batchInsert(fingerPrintCardMapList);
    }

    @Override
    public void updateUserIdAndTimeByCardId(Integer cardId, Integer userId, Date date) {
        fingerPrintCardMapMapper.update(null,
                Wrappers.lambdaUpdate(FingerPrintCardMap.class)
                        .set(FingerPrintCardMap::getLastUpdater, userId)
                        .set(FingerPrintCardMap::getLastUpdateTime, date)
                        .eq(FingerPrintCardMap::getCardId, cardId));
    }

    @Override
    public void deleteByFingerprintIdAndVendor(Integer fingerprintId, Integer vendor) {
        fingerPrintCardMapMapper.delete(Wrappers.lambdaQuery(FingerPrintCardMap.class)
                                                .eq(FingerPrintCardMap::getFingerPrintId, fingerprintId)
                                                .eq(FingerPrintCardMap::getVendor, vendor));
    }

    private List<FingerPrintCardMap> findByVendorAndFingerprintId(Integer vendorId, Integer fingerPrintId) {
        return fingerPrintCardMapMapper.selectList(Wrappers.lambdaQuery(FingerPrintCardMap.class)
                                                           .eq(FingerPrintCardMap::getVendor, vendorId)
                                                           .eq(FingerPrintCardMap::getFingerPrintId, fingerPrintId));
    }
}
