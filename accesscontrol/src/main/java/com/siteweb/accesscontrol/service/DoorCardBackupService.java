package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.DoorCardBackupTreeDTO;
import com.siteweb.accesscontrol.entity.DoorCardBackup;

import java.util.List;

public interface DoorCardBackupService {
    List<DoorCardBackup> findAll();

    int backupDoor(List<Integer> equipmentIds, Integer cardId, String cardName);

    /**
     * 获取门备份树
     * @return {@link List }<{@link DoorCardBackupTreeDTO }>
     */
    List<DoorCardBackupTreeDTO> getBackupDoorTree();

    /**
     * 获取卡备份树
     * @return {@link List }<{@link DoorCardBackupTreeDTO }>
     */
    List<DoorCardBackupTreeDTO> getBackupCardTree();
}
