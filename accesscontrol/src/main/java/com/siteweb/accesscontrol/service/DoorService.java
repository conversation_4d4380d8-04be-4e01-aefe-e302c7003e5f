package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.DoorDTO;
import com.siteweb.accesscontrol.dto.DoorEquipmentVO;
import com.siteweb.accesscontrol.entity.Door;
import com.siteweb.accesscontrol.vo.EquipmentDoor;

import java.util.List;

public interface DoorService {
    /**
     * 获取存在指定卡的授权的门禁设备id
     * @param cardId 卡id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findCardAuthEquipment(Integer cardId);

    /**
     * 获取门禁类型
     * @param equipmentId 设备id
     * @return {@link Integer}
     */
    Integer findDoorCategoryByEquipmentId(Integer equipmentId);

    /**
     * 查找设备门信息
     * @param equipmentId 设备id
     * @return {@link EquipmentDoor}
     */
    EquipmentDoor findInfoByEquipmentId(Integer equipmentId);

    /**
     * 获取门属性
     * @param equipmentId 设备id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> findDoorProperties(Integer equipmentId);

    DoorDTO findDoorInfo(Integer equipmentId);

    List<DoorEquipmentVO> findDoorEquipment();

    Integer findDoorIdByEquipmentId(Integer equipmentId);

    List<Door> findByDoorIds(List<Integer> doorIds);

    List<Door> findByEquipmentIds(List<Integer> equipmentIds);
}
