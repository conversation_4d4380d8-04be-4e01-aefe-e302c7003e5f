package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.entity.DoorTag;
import com.siteweb.accesscontrol.vo.DoorTagVO;

import java.util.List;

public interface DoorTagService {
    /**
     * 获取所有门标签
     *
     * @return {@link List }<{@link DoorTag }>
     */
    List<DoorTag> findAll();

    DoorTag findById(Integer tagId);

    int insert(DoorTag doorTag);

    int updateById(DoorTag doorTag);

    int deleteById(Integer tagId);

    int deleteByIds(List<Integer> doorTagIds);

    List<DoorTagVO> findDoorTagByEquipmentIds(List<Integer> doorEquipmentIds);
}
