package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorGroupEquipmentDTO;
import com.siteweb.accesscontrol.dto.DoorGroupTreeNode;
import com.siteweb.accesscontrol.entity.DoorGroup;
import com.siteweb.accesscontrol.mapper.DoorGroupMapper;
import com.siteweb.accesscontrol.service.DoorGroupMapService;
import com.siteweb.accesscontrol.service.DoorGroupService;
import com.siteweb.utility.service.PrimaryKeyValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

@Service
public class DoorGroupServiceImpl implements DoorGroupService {
    @Autowired
    private PrimaryKeyValueService primaryKeyValueService;
    @Autowired
    private DoorGroupMapper doorGroupMapper;
    @Autowired
    private DoorGroupMapService doorGroupMapService;
    @Override
    public Integer createDoorGroup(DoorGroup doorGroup) {
        int doorGroupId = primaryKeyValueService.getGlobalIdentity("tbl_doorGroup", 0);
        doorGroup.setDoorGroupId(doorGroupId);
        doorGroup.setLastTime(new Date());
        return doorGroupMapper.insert(doorGroup);
    }

    @Override
    public List<DoorGroup> findAll() {
        return doorGroupMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public Integer updateDoorGroup(DoorGroup doorGroup) {
        doorGroup.setLastTime(new Date());
        return doorGroupMapper.updateById(doorGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteById(Integer id) {
        doorGroupMapService.deleteByDoorGroupId(id);
        return doorGroupMapper.deleteById(id);
    }

    @Override
    public List<DoorGroupTreeNode> findDoorGroupTree() {
        List<DoorGroupEquipmentDTO> doorGroupEquipmentDTOList = doorGroupMapper.findDoorGroupTree();
        List<DoorGroupTreeNode> doorGroupTreeNodeList = new ArrayList<>();
        HashSet<Integer> doorGroupIdSet = new HashSet<>();
        for (DoorGroupEquipmentDTO dto : doorGroupEquipmentDTOList) {
            Integer doorGroupId = dto.getDoorGroupId();
            String doorGroupName = dto.getDoorGroupName();
            Integer equipmentId = dto.getEquipmentId();
            String equipmentName = dto.getEquipmentName();
            String description = dto.getDescription();
            Integer category = dto.getCategory();
            //是分组
            if (ObjectUtil.isNotNull(doorGroupId) && !doorGroupIdSet.contains(doorGroupId)) {
                doorGroupTreeNodeList.add(new DoorGroupTreeNode(doorGroupId, doorGroupName, false, null, description,null));
                doorGroupIdSet.add(doorGroupId);
            }
            //是设备
            if (ObjectUtil.isNotNull(equipmentId)) {
                doorGroupTreeNodeList.add(new DoorGroupTreeNode(equipmentId, equipmentName, true, doorGroupId, null, category));
            }
        }
        return doorGroupTreeNodeList;
    }
}
