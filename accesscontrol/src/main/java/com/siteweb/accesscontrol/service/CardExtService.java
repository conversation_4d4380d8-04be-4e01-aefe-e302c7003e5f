package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.entity.CardExt;

public interface CardExtService {
    /**
     * 保存门禁卡扩展字段
     * @param cardExt
     * @return {@link Integer}
     */
    Integer saveCardExt(CardExt cardExt);

    /**
     * 更新门禁卡扩展字段
     * @param cardExt
     * @return {@link Integer}
     */
    Integer updateCardExt(CardExt cardExt);

    /**
     * 删除门禁卡扩展字段
     * @param id 门禁卡id
     * @return {@link Integer}
     */
    Integer deleteById(Integer id);
}
