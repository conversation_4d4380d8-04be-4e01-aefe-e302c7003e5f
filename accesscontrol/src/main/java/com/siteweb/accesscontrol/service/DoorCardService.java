package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.dto.DoorCardInfoDTO;
import com.siteweb.accesscontrol.entity.DoorCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DoorCardService {
    /**
     * 删除门卡映射关系
     * @param cardId 门禁卡id
     * @return {@link Integer}
     */
    Integer deleteByCardId(@Param("cardId") Integer cardId);

    /**
     * 查找门卡映射关系根据卡id
     * @param cardId 卡id
     * @return {@link List}<{@link DoorCard}>
     */
    List<DoorCard> findByCardId(Integer cardId);

    /**
     * 查询门卡信息
     * @param cardIds 卡ids
     * @return {@link List}<{@link DoorCardInfoDTO}>
     */
    List<DoorCardInfoDTO> findDoorCardInfoByCardIds(List<Integer> cardIds);

    List<DoorCard> findByDoorIdAndCardId(Integer doorId, Integer cardId);

    /**
     * 通过卡id获取被授权的门id
     * @param cardId 卡id
     * @return {@link List }<{@link Integer }>
     */
    List<Integer> findEquipmentIdByCardId(Integer cardId);

    List<DoorCard> findByCardIds(List<Integer> cardIdList);
}
