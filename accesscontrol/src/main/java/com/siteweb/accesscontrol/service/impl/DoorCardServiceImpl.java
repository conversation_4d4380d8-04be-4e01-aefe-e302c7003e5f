package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorCardInfoDTO;
import com.siteweb.accesscontrol.entity.DoorCard;
import com.siteweb.accesscontrol.mapper.DoorCardMapper;
import com.siteweb.accesscontrol.service.DoorCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DoorCardServiceImpl implements DoorCardService {
    @Autowired
    private DoorCardMapper doorCardMapper;

    @Override
    public Integer deleteByCardId(Integer cardId) {
        return doorCardMapper.delete(Wrappers.lambdaQuery(DoorCard.class)
                                             .eq(DoorCard::getCardId, cardId));
    }

    @Override
    public List<DoorCard> findByCardId(Integer cardId) {
        return doorCardMapper.selectList(Wrappers.lambdaQuery(DoorCard.class)
                                                 .eq(DoorCard::getCardId, cardId));
    }

    @Override
    public List<DoorCardInfoDTO> findDoorCardInfoByCardIds(List<Integer> cardIds) {
        if (CollUtil.isEmpty(cardIds)) {
            return Collections.emptyList();
        }
        return doorCardMapper.findDoorCardInfoByCardIds(cardIds);
    }

    @Override
    public List<DoorCard> findByDoorIdAndCardId(Integer doorId, Integer cardId) {
        return doorCardMapper.selectList(
                Wrappers.lambdaQuery(DoorCard.class).eq(DoorCard::getDoorId, doorId).eq(DoorCard::getCardId, cardId));
    }

    @Override
    public List<Integer> findEquipmentIdByCardId(Integer cardId) {
        return doorCardMapper.findEquipmentIdByCardId(cardId);
    }

    @Override
    public List<DoorCard> findByCardIds(List<Integer> cardIdList) {
        if (CollUtil.isEmpty(cardIdList)) {
            return Collections.emptyList();
        }
        return doorCardMapper.selectList(Wrappers.lambdaQuery(DoorCard.class)
                                                 .in(DoorCard::getCardId, cardIdList));
    }
}
