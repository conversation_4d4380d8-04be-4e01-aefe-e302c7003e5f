package com.siteweb.accesscontrol.service;

import com.siteweb.accesscontrol.entity.DoorAreaMap;
import com.siteweb.accesscontrol.vo.DoorAreaMapCreateRequest;

import java.util.List;

public interface DoorAreaMapService {
    List<DoorAreaMap> findAll();

    List<DoorAreaMap> findByAreaId(Integer areaId);

    void createDoorAreaMap(DoorAreaMapCreateRequest doorAreaMapCreateRequest);

    void deleteByAreaId(Integer areaId);
}
