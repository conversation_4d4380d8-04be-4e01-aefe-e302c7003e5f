package com.siteweb.accesscontrol.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.DoorAlarmDTO;

import java.util.Date;

public interface DoorAlarmService {
    /**
     * 获取门告警
     *
     * @param page        页面
     * @param equipmentId 设备id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return {@link Page}<{@link DoorAlarmDTO}>
     */
    Page<DoorAlarmDTO> findDoorAlarm(Page page, Integer equipmentId, Date startTime, Date endTime);
}
