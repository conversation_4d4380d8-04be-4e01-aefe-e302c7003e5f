package com.siteweb.accesscontrol.service.impl;

import com.siteweb.accesscontrol.dto.IdNameDTO;
import com.siteweb.accesscontrol.mapper.DoorPropertyMeaningMapper;
import com.siteweb.accesscontrol.service.DoorPropertyMeaningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DoorPropertyMeaningServiceImpl implements DoorPropertyMeaningService {
    @Autowired
    DoorPropertyMeaningMapper doorPropertyMeaningMapper;

    @Override
    public List<IdNameDTO> findDoorParamMeaning(Integer equipmentId, Integer propertyType) {
        return doorPropertyMeaningMapper.findDoorParamMeaning(equipmentId,propertyType);
    }
}
