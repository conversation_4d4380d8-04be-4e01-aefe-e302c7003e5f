package com.siteweb.accesscontrol.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorTimeGroupStrDTO;
import com.siteweb.accesscontrol.dto.EquipmentTimeGroupDTO;
import com.siteweb.accesscontrol.entity.DoorTimeGroup;
import com.siteweb.accesscontrol.mapper.DoorTimeGroupMapper;
import com.siteweb.accesscontrol.service.DoorTimeGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class DoorTimeGroupServiceImpl implements DoorTimeGroupService {
    @Autowired
    private DoorTimeGroupMapper doorTimeGroupMapper;

    @Override
    public List<DoorTimeGroup> findByTimeGroupIds(List<Integer> timeGroupIdList) {
        return doorTimeGroupMapper.selectList(Wrappers.lambdaQuery(DoorTimeGroup.class)
                                                      .in(DoorTimeGroup::getTimeGroupId, timeGroupIdList));
    }

    @Override
    public Integer deleteByTimeGroupIds(List<Integer> timeGroupIdList) {
        return doorTimeGroupMapper.delete(Wrappers.lambdaQuery(DoorTimeGroup.class)
                                                  .in(DoorTimeGroup::getTimeGroupId, timeGroupIdList));
    }

    @Override
    public List<EquipmentTimeGroupDTO> findDoorTimeGroup() {
        return doorTimeGroupMapper.findDoorTimeGroup();
    }
    @Override
    public boolean limitCheck(Integer equipmentId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList){
        int addTimeGroupCountMax = 4;
        int doorTimeGroupCount = doorTimeGroupMapper.findDoorTimeGroupCountByEquipmentId(equipmentId);
        //【需要添加的准进时间组数量 + 当前已经绑定的门禁时间组数量 - 需要删除的准进组数量】是否大于大于最多能绑定准进时间组的数量
        return CollUtil.size(addTimeGroupIdList) + doorTimeGroupCount - CollUtil.size(delTimeGroupIdList) > addTimeGroupCountMax;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> findTimeSpanStrList(Integer doorId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList) {
        List<String> timeSpanStrList = new ArrayList<>();
        if (CollUtil.isNotEmpty(delTimeGroupIdList)) {
            List<DoorTimeGroupStrDTO> doorTimeGroupStrDTOList = doorTimeGroupMapper.findDoorTimeGroupStr(doorId, delTimeGroupIdList);
            List<String> delTimeSpanStr = doorTimeGroupStrDTOList.stream()
                                                          .map(dto -> dto.getTimeGroupType().toString() + dto.getWeek().toString() + "00:00-00:0000:00-00:0000:00-00:00")
                                                          .toList();
            timeSpanStrList.addAll(delTimeSpanStr);
        }
        saveDoorTimeGroup(doorId, addTimeGroupIdList, delTimeGroupIdList);
        if (CollUtil.isNotEmpty(addTimeGroupIdList)) {
            List<DoorTimeGroupStrDTO> doorTimeGroupStrDTOList = doorTimeGroupMapper.findDoorTimeGroupStr(doorId, addTimeGroupIdList);
            List<String> addTimeSpanStr = doorTimeGroupStrDTOList.stream()
                                                                 .map(dto -> dto.getTimeGroupType().toString() + dto.getWeek().toString() + dto.getTimeSpanChar())
                                                                 .toList();
            timeSpanStrList.addAll(addTimeSpanStr);
        }
        return timeSpanStrList;
    }

    /**
     * 添加和删除门准进时间组
     * @param doorId 门id
     * @param addTimeGroupIdList 需要添加的门准进组
     * @param delTimeGroupIdList 需要删除的门准进组
     */
    public void saveDoorTimeGroup(Integer doorId, List<Integer> addTimeGroupIdList, List<Integer> delTimeGroupIdList) {
        deleteByDoorIdAndTimeGroupIds(doorId, delTimeGroupIdList);
        createDoorTimeGroup(doorId, addTimeGroupIdList);
    }

    /**
     * @param doorId 门id
     * @param timeGroupIds 需要添加的准进时间组id
     */
    @Override
    public void createDoorTimeGroup(Integer doorId, List<Integer> timeGroupIds) {
        if (Objects.isNull(doorId) || CollUtil.isEmpty(timeGroupIds)) {
            return;
        }
        List<DoorTimeGroup> doorTimeGroupList = new ArrayList<>(timeGroupIds.size());
        List<Integer> timeGroupTypes = doorTimeGroupMapper.findLackDoorTimeGroupType(doorId);
        for (int i = 0; i < timeGroupIds.size(); i++) {
            doorTimeGroupList.add(new DoorTimeGroup(doorId, timeGroupIds.get(i),timeGroupTypes.get(i)));
        }
        doorTimeGroupMapper.batchInsert(doorTimeGroupList);
    }

    /**
     * 删除准进时间组
     * @param doorId 门id
     * @param timeGroupIds 准进时间组ids
     */
    @Override
    public void deleteByDoorIdAndTimeGroupIds(Integer doorId, List<Integer> timeGroupIds){
        if (Objects.isNull(doorId) || CollUtil.isEmpty(timeGroupIds)) {
            return;
        }
        doorTimeGroupMapper.delete(Wrappers.lambdaQuery(DoorTimeGroup.class)
                                           .eq(DoorTimeGroup::getDoorId, doorId)
                                           .in(DoorTimeGroup::getTimeGroupId, timeGroupIds));
    }
    @Override
    public List<Integer> findTimeGroupNo(Integer equipmentId, Integer timeGroupId) {
        return doorTimeGroupMapper.findTimeGroupNo(equipmentId,timeGroupId);
    }

    @Override
    public Integer findTimeGroupTimeByDoorIdAndTimeGroupId(Integer doorId, Integer timeGroupId) {
        DoorTimeGroup doorTimeGroup = doorTimeGroupMapper.selectOne(Wrappers.lambdaQuery(DoorTimeGroup.class)
                                                                            .select(DoorTimeGroup::getTimeGroupType)
                                                                            .eq(DoorTimeGroup::getDoorId, doorId)
                                                                            .eq(DoorTimeGroup::getTimeGroupId, timeGroupId));
        return Optional.ofNullable(doorTimeGroup)
                       .map(DoorTimeGroup::getTimeGroupType)
                       .orElse(null);
    }
}
