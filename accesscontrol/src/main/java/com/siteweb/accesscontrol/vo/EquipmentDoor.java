package com.siteweb.accesscontrol.vo;

import lombok.Data;

@Data
public class EquipmentDoor {
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 门编号
     */
    private Integer doorNo;
    /**
     * 门密码
     */
    private String password;
    /**
     * 类别
     */
    private Integer category;
    /**
     * 门类型 10进制或16进制
     */
    private Integer cardCodeType;
}
