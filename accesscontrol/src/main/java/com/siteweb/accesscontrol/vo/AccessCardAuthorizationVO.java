package com.siteweb.accesscontrol.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AccessCardAuthorizationVO {
    private Integer equipmentId;
    private String equipmentName;
    private Integer doorId;
    private String doorName;
    private String cardName;
    private String cardCode;
    private String cardGroupName;
    private Integer cardId;
    private Integer userId;
    private Integer timeGroupId;
    private String timeGroupName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    private Integer areaId;
    private String areaName;
    private Integer timeGroupType;
    private Integer faceId;
    private Integer fingerprintId;
    private Integer faceOutDate;
    private Integer fingerprintOutDate;
    private String stationName;

    public String getAuthorizationKey(){
        return doorId + "." + timeGroupId + "." + cardId;
    }
}
