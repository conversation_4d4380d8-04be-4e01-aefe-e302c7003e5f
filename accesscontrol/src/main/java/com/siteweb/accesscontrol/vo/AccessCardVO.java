package com.siteweb.accesscontrol.vo;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.siteweb.common.serializer.UppercaseDeserializer;
import lombok.Data;

import java.util.Date;

@Data
public class AccessCardVO {
    /**
     * 卡id
     */
    private Integer cardId;
    /**
     * 卡号【需要为大写，小写底层识别不了】
     */
    @JsonDeserialize(using = UppercaseDeserializer.class)
    private String cardCode;
    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 卡分组id
     */
    private Integer cardGroup;

    /**
     * 持卡人id
     */
    private Integer userId;
    /**
     * 卡状态
     */
    private Integer cardStatus;

    /**
     * 卡号类型 十进制\十六进制
     */
    private Integer cardCodeType;
    /**
     * 卡类型 IC卡\ID卡
     */
    private Integer cardType;

    /**
     * 有效期开始时间
     */
    private Date startTime;
    /**
     * 有效期结束时间
     */
    private Date endTime;
    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注销时间
     */
    private Date unRegisterTime;
    /**
     * 挂失时间
     */
    private Date lostTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 卡密码
     */
    private String password;
    /**
     * 卡号转换(tbl_card.cardcode的十进制或十六进制)
     */
    private String cardCodeConv;

    public String getCardCodeConv(){
        //卡号类型是十进制则转成16进制，是16进制则转成10进制
        if (ObjectUtil.equals(this.cardCodeType, 10)) {
            return StrUtil.fill(HexUtil.toHex(Long.parseLong(this.cardCode)).toUpperCase(), '0', 10, true);
        }
        return StrUtil.fill(String.valueOf(HexUtil.hexToLong(this.cardCode)), '0', 10, true);
    }

}
