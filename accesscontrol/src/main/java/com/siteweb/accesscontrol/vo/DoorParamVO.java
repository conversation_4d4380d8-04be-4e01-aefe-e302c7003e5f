package com.siteweb.accesscontrol.vo;

import lombok.Data;

import java.util.List;

/**
 * 门参数
 * <AUTHOR>
 * @date 2022/09/22
 */
@Data
public class DoorParamVO {
    /**
     * 设备id
     */
    private Integer equipmentId;
    /**
     * 门名称
     */
    private String doorName;

    /**
     * 门延迟
     */
    private Integer openDelay;
    /**
     * 密码
     */
    private String password;
    /**
     * 非法卡刷卡间隔
     */
    private Integer slotInterval;
    /**
     * 火警信号
     */
    private Integer fireSignal;
    /**
     * 错误锁定次数
     */
    public Integer lockErrorCount;
    /**
     * 锁定时间
     */
    private Integer lockTime;
    /**
     * 持续时间
     */
    private Integer keepTime;
    /**
     * 开门方式
     */
    private Integer openMode;

    /**
     * 添加校准时间组
     */
    private List<Integer> addTimeGroupIdList;
    /**
     * 删除准进时间组
     */
    private List<Integer> delTimeGroupIdList;
}
