package com.siteweb.accesscontrol.util;

import cn.hutool.core.util.CharsetUtil;

import java.nio.charset.Charset;

public class EncodingUtil {
    /**
     * 将UTF-8编码的字符串转换为GBK编码的十六进制字符串
     * @param text 需要转换的文本
     * @return {@link String}
     */
    public static String utf8ToGBKHex(String text) {
        Charset gbk = Charset.forName(CharsetUtil.GBK);
        byte[] gbkBytes = text.getBytes(gbk);
        StringBuilder sb = new StringBuilder();
        for (byte b : gbkBytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
