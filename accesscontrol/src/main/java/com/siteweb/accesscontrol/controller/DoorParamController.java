package com.siteweb.accesscontrol.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.accesscontrol.manager.DoorCommandManager;
import com.siteweb.accesscontrol.service.DoorPropertyMeaningService;
import com.siteweb.accesscontrol.service.DoorTimeGroupService;
import com.siteweb.accesscontrol.vo.DoorParamVO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.service.EquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api")
@Api(value = "DoorParamController", tags = "门参数管理")
public class DoorParamController {

    @Autowired
    private DoorCommandManager doorCommandManager;
    @Autowired
    DoorTimeGroupService doorTimeGroupService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    DoorPropertyMeaningService doorPropertyMeaningService;

    @ApiOperation(("获取门参数含义"))
    @GetMapping(value = "/doorparam",params = {"equipmentId","propertyType"})
    public ResponseEntity<ResponseResult> getDoorParam(Integer equipmentId, Integer propertyType) {
        return ResponseHelper.successful(doorPropertyMeaningService.findDoorParamMeaning(equipmentId, propertyType));
    }

    @ApiOperation(("设置准进组时间"))
    @PutMapping("/doorparam/timegroup")
    public ResponseEntity<ResponseResult> setTimeGroup(@RequestBody List<DoorParamVO> doorParamVOList){
        List<String> doorNameList = new ArrayList<>(doorParamVOList.size());
        for (DoorParamVO vo : doorParamVOList) {
            if (doorTimeGroupService.limitCheck(vo.getEquipmentId(), vo.getAddTimeGroupIdList(), vo.getDelTimeGroupIdList())) {
                Optional.ofNullable(equipmentService.findById(vo.getEquipmentId()))
                        .map(Equipment::getEquipmentName)
                        .ifPresent(doorNameList::add);
            }
        }
        if (CollUtil.isNotEmpty(doorNameList)) {
            String doorNames = CollUtil.join(doorNameList, ",");
            return ResponseHelper.failed(String.valueOf(ErrorCode.DOOR_TIME_GROUP_COUNT_LIMIT.value()), doorNames, HttpStatus.OK);
        }
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        Boolean result = doorCommandManager.setTimeGroup(loginUserId, doorParamVOList);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("设置门延迟")
    @PutMapping("/doorparam/opendelay")
    public ResponseEntity<ResponseResult> setDoorOpenDelay(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setDoorOpenDelay(loginUserId,doorParamVOList));
    }

    @ApiOperation("设置门密码")
    @PutMapping("/doorparam/password")
    public ResponseEntity<ResponseResult> setDoorPassword(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setDoorPassword(loginUserId,doorParamVOList));
    }

    @ApiOperation(("设置非法卡刷卡间隔"))
    @PutMapping("/doorparam/slotinterval")
    public ResponseEntity<ResponseResult> setSlotInterval(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setSlotInterval(userId, doorParamVOList));
    }

    @ApiOperation(("设置火警信号"))
    @PutMapping("/doorparam/firesignal")
    public ResponseEntity<ResponseResult> setFireSignal(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setFireSignal(userId, doorParamVOList));
    }

    @ApiOperation(("设置卡封锁错误次数"))
    @PutMapping("/doorparam/lockerrorcount")
    public ResponseEntity<ResponseResult> setLockErrorCount(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setLockErrorCount(userId, doorParamVOList));
    }

    @ApiOperation(("设置卡封锁时间"))
    @PutMapping("/doorparam/locktime")
    public ResponseEntity<ResponseResult> setLockTime(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setLockTime(userId, doorParamVOList));
    }

    @ApiOperation(("设置开门保持时间时间"))
    @PutMapping("/doorparam/keeptime")
    public ResponseEntity<ResponseResult> setKeepTime(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setKeepTime(userId, doorParamVOList));
    }

    @ApiOperation(("设置开门方式"))
    @PutMapping("/doorparam/openmode")
    public ResponseEntity<ResponseResult> setOpenMode(@RequestBody List<DoorParamVO> doorParamVOList){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setOpenMode(userId, doorParamVOList));
    }

    @ApiOperation(("设置门常开"))
    @PutMapping("/doorparam/normallyopen")
    public ResponseEntity<ResponseResult> setNormallyOpen(@RequestBody DoorParamVO doorParamVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setNormallyOpen(userId, doorParamVO.getEquipmentId()));
    }

    @ApiOperation(("设置门常关"))
    @PutMapping("/doorparam/normallyclose")
    public ResponseEntity<ResponseResult> setNormallyClose(@RequestBody DoorParamVO doorParamVO){
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setNormallyClose(userId, doorParamVO.getEquipmentId()));
    }
}
