package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.service.DoorGroupMapService;
import com.siteweb.accesscontrol.vo.DoorGroupMapCreateRequest;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "DoorGroupController", tags = "门分组映射管理")
public class DoorGroupMapController {
    @Autowired
    private DoorGroupMapService doorGroupMapService;

    @ApiOperation("查询所有门分组映射")
    @GetMapping("/doorgroupmap")
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(doorGroupMapService.findAll());
    }

    @ApiOperation("更新门分组映射")
    @PutMapping("/doorgroupmap")
    public ResponseEntity<ResponseResult> updateDoorGroupMap(@RequestBody DoorGroupMapCreateRequest doorGroupMapCreateRequest){
        return ResponseHelper.successful(doorGroupMapService.updateDoorGroupMap(doorGroupMapCreateRequest));
    }
}
