package com.siteweb.accesscontrol.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.service.DoorAlarmService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api")
@Api(value = "DoorAlarmController",tags = "门告警管理")
public class DoorAlarmController {
    @Autowired
    private DoorAlarmService doorAlarmService;
    @ApiOperation("获取门告警")
    @GetMapping("/dooralarm")
    public ResponseEntity<ResponseResult> getDoorAlarm(Page page, Integer equipmentId, Date startTime, Date endTime) {
        return ResponseHelper.successful(doorAlarmService.findDoorAlarm(page, equipmentId, startTime, endTime));
    }
}
