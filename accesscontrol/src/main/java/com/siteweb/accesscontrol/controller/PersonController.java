package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.service.PersonService;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "PersonController", tags = "获取人员信息控制器")
public class PersonController {
    @Autowired
    PersonService personService;

    @GetMapping("/person")
    public ResponseEntity<ResponseResult> getPersons(){
        return ResponseHelper.successful(personService.findPersons(TokenUserUtil.getLoginUserId()));
    }
}
