package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.dto.DoorTagMapDTO;
import com.siteweb.accesscontrol.service.DoorTagMapService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api")
public class DoorTagMapController {
    @Autowired
    DoorTagMapService doorTagMapService;

    @PutMapping("/doortagmap")
    public ResponseEntity<ResponseResult> findByDoorId(@RequestBody DoorTagMapDTO doorTagMapDTO) {
        return ResponseHelper.successful(doorTagMapService.updateDoorTagMap(doorTagMapDTO));
    }
}
