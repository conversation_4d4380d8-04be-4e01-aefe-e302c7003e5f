package com.siteweb.accesscontrol.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.accesscontrol.entity.DoorTimeGroup;
import com.siteweb.accesscontrol.service.DoorTimeGroupService;
import com.siteweb.accesscontrol.service.TimeGroupService;
import com.siteweb.accesscontrol.vo.TimeGroupVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "TimeGroupController", tags = "时间组管理")
public class TimeGroupController {
    @Autowired
    private TimeGroupService timeGroupService;

    @Autowired
    private DoorTimeGroupService doorTimeGroupService;

    @ApiOperation("获取所有时间组信息")
    @GetMapping(value = "/timegroup")
    public ResponseEntity<ResponseResult> findAll() {
        return ResponseHelper.successful(timeGroupService.findAll());
    }

    @ApiOperation("添加时间组信息")
    @PostMapping(value = "/timegroup")
    public ResponseEntity<ResponseResult> createTimeGroup(@RequestBody TimeGroupVO timeGroupVO){

        return ResponseHelper.successful(timeGroupService.createTimeGroup(timeGroupVO));
    }

    @ApiOperation("更新时间组信息")
    @PutMapping(value = "/timegroup")
    public ResponseEntity<ResponseResult> updateTimeGroup(@RequestBody TimeGroupVO timeGroupVO){
        return ResponseHelper.successful(timeGroupService.updateTimeGroup(timeGroupVO));
    }

    @ApiOperation("删除时间组信息(多个逗号隔开)")
    @DeleteMapping(value = "/timegroup")
    public ResponseEntity<ResponseResult> deleteTimeGroup(String timeGroupIds) {
        List<Integer> timeGroupIdList = CharSequenceUtil.split(timeGroupIds, ",")
                                                        .stream()
                                                        .map(Integer::valueOf)
                                                        .toList();
        if (CollUtil.isEmpty(timeGroupIdList)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),"timeGroupIds is not empty", HttpStatus.BAD_REQUEST);
        }
        List<DoorTimeGroup> doorTimeGroup = doorTimeGroupService.findByTimeGroupIds(timeGroupIdList);
        if (CollUtil.isNotEmpty(doorTimeGroup)) {
            return ResponseHelper.failed((String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value())),"不允许删除已授权时间组！", HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(timeGroupService.deleteByTimeGroupIds(timeGroupIdList));
    }
}
