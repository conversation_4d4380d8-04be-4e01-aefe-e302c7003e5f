package com.siteweb.accesscontrol.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.dto.AccessCardDTO;
import com.siteweb.accesscontrol.dto.CopyCardRequestDTO;
import com.siteweb.accesscontrol.service.CardService;
import com.siteweb.accesscontrol.vo.AccessCardVO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api")
@Api(value = "CardController",tags = "卡管理")
public class CardController {
    @Autowired
    private CardService cardService;

    @ApiOperation("查询门禁卡根据id")
    @GetMapping("/card/{id}")
    public ResponseEntity<ResponseResult> getCardGroupById(@PathVariable("id") Integer id){
        return ResponseHelper.successful(cardService.findCardById(id));
    }

    @ApiOperation("查询门禁卡根据卡分组id")
    @GetMapping(value = "/card")
    public ResponseEntity<ResponseResult> getCardGroupByCardGroup(Integer cardGroup,Integer cardId){
        if (Objects.nonNull(cardId)) {
            return getCardGroupById(cardId);
        }
        return ResponseHelper.successful(cardService.findCardByCardGroup(cardGroup));
    }

    @ApiOperation("添加门禁卡")
    @PostMapping("/card")
    public ResponseEntity<ResponseResult> createCardGroup(@RequestBody AccessCardVO accessCardVO){
        return ResponseHelper.successful(cardService.createCard(accessCardVO));
    }

    @ApiOperation("修改门禁卡")
    @PutMapping("/card")
    public ResponseEntity<ResponseResult> updateCardGroup(@RequestBody AccessCardVO accessCardVO){
        AccessCardDTO card = cardService.findCardById(accessCardVO.getCardId());
        if (ObjectUtil.isNull(card)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID),"card id not exists", HttpStatus.NOT_FOUND);
        }
        return ResponseHelper.successful(cardService.updateCard(accessCardVO));
    }

    @ApiOperation("删除门禁卡")
    @DeleteMapping("/card")
    public ResponseEntity<ResponseResult> deleteCard(@RequestBody List<Integer> cardIdList){
        if (CollUtil.isEmpty(cardIdList)) {
            return ResponseHelper.successful();
        }
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        for (Integer cardId : cardIdList) {
            cardService.deleteById(loginUserId, cardId);
        }
        return ResponseHelper.successful(true);
    }

    @ApiOperation("丢失门禁卡")
    @DeleteMapping("/card/lostcard")
    public ResponseEntity<ResponseResult> lostCard(String srcCardCode,String dstCardCode,Boolean lostFlag){
        if (CharSequenceUtil.isBlank(srcCardCode) || CharSequenceUtil.isBlank(dstCardCode)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "srcCardCode Or dstCardCode Empty ", HttpStatus.BAD_REQUEST);
        }
        boolean result = cardService.lostCard(TokenUserUtil.getLoginUserId(),srcCardCode,dstCardCode,lostFlag);
        return ResponseHelper.successful(result);
    }

    @ApiOperation("复制门禁卡")
    @PostMapping("/card/copycard")
    public ResponseEntity<ResponseResult> copyCard(@RequestBody CopyCardRequestDTO copyCardRequestDTO){
        if (Objects.isNull(copyCardRequestDTO) || CollUtil.isEmpty(copyCardRequestDTO.getTarCardCode())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()), "srcCardCode Or dstCardCode Empty ", HttpStatus.BAD_REQUEST);
        }
        boolean result = cardService.copyCard(TokenUserUtil.getLoginUserId(), copyCardRequestDTO.getSrcCardCode(),
                copyCardRequestDTO.getTarCardCode());
        return ResponseHelper.successful(result);
    }
}
