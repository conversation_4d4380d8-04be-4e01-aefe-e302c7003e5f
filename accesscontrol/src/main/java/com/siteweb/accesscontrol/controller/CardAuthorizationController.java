package com.siteweb.accesscontrol.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.AccessCardAuthorizationDTO;
import com.siteweb.accesscontrol.dto.CardValidDateDTO;
import com.siteweb.accesscontrol.manager.DoorCommandManager;
import com.siteweb.accesscontrol.service.CardAuthorizationService;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "AccessCardAuthorizationController",tags = "卡授权管理")
public class CardAuthorizationController {
    /**
     * 最大授权数量
     */
    private static final int MAX_AUTHORIZATION_COUNT = 2000;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    DoorCommandManager doorCommandManager;
    @Autowired
    CardAuthorizationService cardAuthorizationService;
    @ApiOperation("获取卡授权列表")
    @GetMapping("/accesscardauthorization")
    public ResponseEntity<ResponseResult> getAccessCardAuthorizationPage(Page page, String cardCode, Integer equipmentId, Integer cardGroup, Integer areaId, Integer userId, String stationName) throws InterruptedException {
        return ResponseHelper.successful(cardAuthorizationService.findCardAuthorizationPage(page, cardCode, equipmentId, cardGroup, areaId, userId, stationName));
    }

    @ApiOperation("授权指定卡到门")
    @PostMapping("/accesscardauthorization")
    public ResponseEntity<ResponseResult> addAccessCardAuthorization(@RequestBody AccessCardAuthorizationDTO authorizationVO){
        if (CollUtil.isEmpty(authorizationVO.getEquipmentIdList()) || CollUtil.isEmpty(authorizationVO.getCardIdList())) {
            return ResponseHelper.successful();
        }
        if (authorizationVO.getEquipmentIdList().size() * authorizationVO.getCardIdList().size() >= MAX_AUTHORIZATION_COUNT) {
            return ResponseHelper.failed(messageSourceUtil.getMessage("accessControl.authorization.tooMuch"));
        }
        return ResponseHelper.successful(cardAuthorizationService.addAccessCardAuthorization(TokenUserUtil.getLoginUserId(),
                        authorizationVO.getEquipmentIdList(), authorizationVO.getCardIdList(),
                        authorizationVO.getTimeGroupId(), authorizationVO.getFlag()));
    }

    @ApiOperation("添加或修改门禁卡授权")
    @PutMapping("/accesscardauthorization")
    public ResponseEntity<ResponseResult> updateAccessCardAuthorization(@RequestBody List<AccessCardAuthorizationDTO> accessCardAuthorizationList){
        boolean result = Boolean.TRUE;
        for (AccessCardAuthorizationDTO accessCardAuthorizationDTO : accessCardAuthorizationList) {
            if (!doorCommandManager.editManualDoorCard(TokenUserUtil.getLoginUserId(), accessCardAuthorizationDTO.getEquipmentId(), accessCardAuthorizationDTO.getTimeGroupNo(), accessCardAuthorizationDTO.getCardId(), accessCardAuthorizationDTO.getValidTime())) {
                result = Boolean.FALSE;
            }
        }
        return ResponseHelper.successful(result);
    }

    @ApiOperation("修改卡授权的结束时间")
    @PutMapping("/accesscardauthorizationdate")
    public ResponseEntity<ResponseResult> updateAccessCardAuthorizationDate(@RequestBody CardValidDateDTO cardValidDateDTO){
        cardAuthorizationService.updateAccessCardAuthorizationDate(cardValidDateDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除门禁卡授权")
    @DeleteMapping("/accesscardauthorization")
    public ResponseEntity<ResponseResult> deleteAccessCardAuthorization(@RequestBody List<AccessCardAuthorizationDTO> accessCardAuthorizationList){
        boolean result = true;
        for (AccessCardAuthorizationDTO accessCardAuthorizationDTO : accessCardAuthorizationList) {
            if (!doorCommandManager.delManualDoorCard(TokenUserUtil.getLoginUserId(), accessCardAuthorizationDTO.getEquipmentId(), accessCardAuthorizationDTO.getCardId(), accessCardAuthorizationDTO.getFlag())) {
                result = false;
            }
        }
        return ResponseHelper.successful(result);
    }
}
