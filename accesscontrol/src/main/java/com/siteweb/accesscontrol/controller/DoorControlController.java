package com.siteweb.accesscontrol.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.dto.RemoteCollectDTO;
import com.siteweb.accesscontrol.manager.DoorCommandManager;
import com.siteweb.accesscontrol.service.DoorControlService;
import com.siteweb.accesscontrol.service.FaceDataAuthService;
import com.siteweb.accesscontrol.service.FingerprintAuthService;
import com.siteweb.accesscontrol.vo.DoorTimeGroupVO;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


@RestController
@RequestMapping("/api")
@Api(value = "DoorControlController", tags = "门控制管理")
@Slf4j
public class DoorControlController {
    @Autowired
    private DoorCommandManager doorCommandManager;
    @Autowired
    private DoorControlService doorControlService;
    @Autowired
    private FaceDataAuthService faceDataAuthService;
    @Autowired
    private FingerprintAuthService fingerprintAuthService;

    @ApiOperation("获取门设备控制队列")
    @GetMapping("/doorcontrol")
    public ResponseEntity<ResponseResult> getDoorControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Date startTime, Date endTime) {
        return ResponseHelper.successful(doorControlService.findDoorControl(page,userId,equipmentId,controlResultType,startTime,endTime));
    }
    @ApiOperation("获取卡授权相关控制记录")
    @GetMapping("/doorcontrol/card")
    public ResponseEntity<ResponseResult> getCardControl(Page page, Integer userId, Integer equipmentId, Integer controlResultType, Date startTime, Date endTime) {
        return ResponseHelper.successful(doorControlService.findCardControl(page, userId, equipmentId, controlResultType, startTime, endTime));
    }

    @ApiOperation("获取等待发送的门控制队列")
    @GetMapping("/doorcontrol/queue")
    public ResponseEntity<ResponseResult> getControlQueueOfDoor(Page page, Integer userId, Integer areaId, Integer equipmentId) {
        return ResponseHelper.successful(doorControlService.findControlQueueOfDoor(page, userId, areaId, equipmentId));
    }

    @ApiOperation("删除等待发送的门控制队列")
    @DeleteMapping("/doorcontrol/queue")
    public ResponseEntity<ResponseResult> deleteControlQueueOfDoor(@RequestBody List<Integer> idList) {
        return ResponseHelper.successful(doorControlService.deleteControlQueueOfDoor(idList));
    }

    @ApiOperation("获取未执行完成和执行失败的门禁控制队列数量")
    @GetMapping("/doorcontrol/count")
    public ResponseEntity<ResponseResult> getControlCountOfDoor(){
         return ResponseHelper.successful(doorControlService.findControlCountOfDoor());
    }

    @ApiOperation("获取活动门控制队列")
    @GetMapping("/doorcontrol/active")
    public ResponseEntity<ResponseResult> getControlActiveOfDoor(Page page,Integer userId,Integer equipmentId,Integer areaId,Integer controlResultType){
        return ResponseHelper.successful(doorControlService.findControlActiveOfDoor(page, userId, equipmentId, areaId, controlResultType));
    }

    @ApiOperation("获取历史门控制队列")
    @GetMapping("/doorcontrol/history")
    public ResponseEntity<ResponseResult> getControlHistoryOfDoor(Page page, Integer userId, Integer equipmentId, Integer areaId, Integer controlResultType, Date startTime, Date endTime) {
        return ResponseHelper.successful(doorControlService.findControlHistoryOfDoor(page, userId, equipmentId, areaId, controlResultType, startTime, endTime));
    }

    @ApiOperation("确认门控制命令")
    @PutMapping("/doorcontrol/confirm")
    public ResponseEntity<ResponseResult> confirmDoorCommand(@RequestBody List<Integer> serialNoList) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.confirmDoorCommand(loginUserId, serialNoList));
    }

    @ApiOperation("重发门控制命令")
    @PostMapping("/doorcontrol/resend")
    public ResponseEntity<ResponseResult> resendDoorCommand(@RequestBody List<Integer> serialNoList) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.resendDoorCommand(loginUserId, serialNoList));
    }

    @ApiOperation("门禁校时")
    @PostMapping("/doorcontrol/settime")
    public ResponseEntity<ResponseResult> setTime(@RequestBody DoorTimeGroupVO doorTimeGroupVO){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.setTime(loginUserId,doorTimeGroupVO.getEquipmentId()));
    }

    @ApiOperation("开门")
    @PostMapping("/doorcontrol/open")
    public ResponseEntity<ResponseResult> open(@RequestBody DoorTimeGroupVO doorTimeGroupVO){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.open(loginUserId,doorTimeGroupVO.getEquipmentId()));
    }

    @ApiOperation("关门")
    @PostMapping("/doorcontrol/close")
    public ResponseEntity<ResponseResult> close(@RequestBody DoorTimeGroupVO doorTimeGroupVO){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.close(loginUserId,doorTimeGroupVO.getEquipmentId()));
    }


    @ApiOperation(("删除所有卡"))
    @PostMapping("/doorcontrol/clearcard")
    public ResponseEntity<ResponseResult> clearCard(@RequestBody DoorTimeGroupVO doorTimeGroupVO){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(doorCommandManager.clearCard(loginUserId, doorTimeGroupVO.getEquipmentId()));
    }

    @ApiOperation("远程收集指纹")
    @PostMapping("/collectfingerprint")
    public ResponseEntity<ResponseResult> collectFingerprint(@RequestBody RemoteCollectDTO remoteCollectDTO){
        if (fingerprintAuthService.existCardFingerprintAuth(remoteCollectDTO.getCardId())) {
            log.error("cardId:{}不允许修改已经授权的指纹", remoteCollectDTO.getCardId());
            return ResponseHelper.failed(String.valueOf(ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FINGER_DATA.value()), ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FINGER_DATA.getReasonPhrase(), HttpStatus.OK);
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        boolean result = doorCommandManager.remoteCollectFingerprint(userId, remoteCollectDTO.getEquipmentId(), remoteCollectDTO.getCardId(), remoteCollectDTO.getFingerNo());
        return ResponseHelper.successful(result);
    }
    @ApiOperation("远程收集人脸")
    @PostMapping("/collectface")
    public ResponseEntity<ResponseResult> collectFace(@RequestBody RemoteCollectDTO remoteCollectDTO){
        if (faceDataAuthService.existByCardId(remoteCollectDTO.getCardId())) {
            log.error("卡id:{},远程采集的人脸存在授权信息", remoteCollectDTO.getCardId());
            return ResponseHelper.failed(String.valueOf(ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FACE_DATA.value()), ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FACE_DATA.getReasonPhrase(), HttpStatus.OK);
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        boolean result = doorCommandManager.remoteCollectFace(userId, remoteCollectDTO.getEquipmentId(), remoteCollectDTO.getCardId());
        return ResponseHelper.successful(result);
    }
}
