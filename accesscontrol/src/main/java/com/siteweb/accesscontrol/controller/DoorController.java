package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.service.DoorService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "DoorController", tags = "门管理")
public class DoorController {
    @Autowired
    private DoorService doorService;

    @ApiOperation("获取门基本信息")
    @GetMapping("/door/{equipmentId}")
    public ResponseEntity<ResponseResult> findDoorInfo(@PathVariable("equipmentId") Integer equipmentId){
        return ResponseHelper.successful(doorService.findDoorInfo(equipmentId));
    }

    @ApiOperation("获取门属性")
    @GetMapping("/door/property/{equipmentId}")
    public ResponseEntity<ResponseResult> findDoorProperties(@PathVariable("equipmentId") Integer equipmentId){
        return ResponseHelper.successful(doorService.findDoorProperties(equipmentId));
    }

    @ApiOperation("获取门设备列表")
    @GetMapping("/door/list")
    public ResponseEntity<ResponseResult> findDoorEquipment(){
        return ResponseHelper.successful(doorService.findDoorEquipment());
    }
}
