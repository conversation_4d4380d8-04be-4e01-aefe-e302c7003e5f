package com.siteweb.accesscontrol.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.accesscontrol.service.SwapCardRecordService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api")
@Api(value = "SwipeCardRecordController",tags = "刷卡记录管理")
public class SwapCardRecordController {
    @Autowired
    private SwapCardRecordService swapCardRecordService;

    @ApiOperation("获取刷卡记录")
    @GetMapping("/swapcard")
    public ResponseEntity<ResponseResult> getSwapCardRecordPage(Page page,Integer userId ,Integer equipmentId, Integer areaId, Date startTime, Date endTime){
        return ResponseHelper.successful(swapCardRecordService.findSwapCardRecordPage(page, userId, equipmentId, areaId, startTime, endTime));
    }

    @ApiOperation("获取最新的刷卡记录")
    @GetMapping("/swapcard/last/{equipmentId}")
    public ResponseEntity<ResponseResult> getLastRecordByEquipmentId(@PathVariable("equipmentId") Integer equipmentId){
        return ResponseHelper.successful(swapCardRecordService.findLastRecordByEquipmentId(equipmentId));
    }
}
