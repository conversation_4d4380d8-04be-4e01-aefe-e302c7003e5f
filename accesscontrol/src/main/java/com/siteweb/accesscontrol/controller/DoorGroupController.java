package com.siteweb.accesscontrol.controller;


import com.siteweb.accesscontrol.entity.DoorGroup;
import com.siteweb.accesscontrol.service.DoorGroupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(value = "DoorGroupController", tags = "门分组管理")
public class DoorGroupController {
    @Autowired
    private DoorGroupService doorGroupService;
    @ApiOperation("查询所有门分组")
    @GetMapping("/doorgroup")
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(doorGroupService.findAll());
    }

    @ApiOperation("添加门分组")
    @PostMapping("/doorgroup")
    public ResponseEntity<ResponseResult> createDoorGroup(@RequestBody DoorGroup doorGroup){
         return ResponseHelper.successful(doorGroupService.createDoorGroup(doorGroup));
    }

    @ApiOperation("添加门分组")
    @PutMapping("/doorgroup")
    public ResponseEntity<ResponseResult> updateDoorGroup(@RequestBody DoorGroup doorGroup){
        return ResponseHelper.successful(doorGroupService.updateDoorGroup(doorGroup));
    }

    @ApiOperation("删除门分组")
    @DeleteMapping("/doorgroup/{id}")
    public ResponseEntity<ResponseResult> deleteDoorGroup(@PathVariable("id") Integer id){
        return ResponseHelper.successful(doorGroupService.deleteById(id));
    }

    @ApiOperation("获取门分组树")
    @GetMapping("/doorgrouptree")
    public ResponseEntity<ResponseResult> findDoorGroupTree(){
        return ResponseHelper.successful(doorGroupService.findDoorGroupTree());
    }
}
