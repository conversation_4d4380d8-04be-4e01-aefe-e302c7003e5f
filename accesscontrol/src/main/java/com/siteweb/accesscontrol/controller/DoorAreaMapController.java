package com.siteweb.accesscontrol.controller;


import com.siteweb.accesscontrol.service.DoorAreaMapService;
import com.siteweb.accesscontrol.vo.DoorAreaMapCreateRequest;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "DoorAreaController", tags = "门区域设备映射管理")
public class DoorAreaMapController {
    @Autowired
    private DoorAreaMapService doorAreaMapService;

    @ApiOperation("获取所有门区域设备映射")
    @GetMapping("/doorareamap")
    public ResponseEntity<ResponseResult> getDoorAreaMap() {
        return ResponseHelper.successful(doorAreaMapService.findAll());
    }

    @ApiOperation("获取门区域设备映射通过门区域id")
    @GetMapping(value = "/doorareamap", params = {"areaId"})
    public ResponseEntity<ResponseResult> getDoorAreaMap(Integer areaId) {
        return ResponseHelper.successful(doorAreaMapService.findByAreaId(areaId));
    }

    @ApiOperation("更新区域设备映射关系")
    @PutMapping(value = "/doorareamap")
    public ResponseEntity<ResponseResult> createDoorAreaMap(@RequestBody List<DoorAreaMapCreateRequest> doorAreaMapCreateRequest) {
        for (DoorAreaMapCreateRequest areaMapCreateRequest : doorAreaMapCreateRequest) {
            doorAreaMapService.createDoorAreaMap(areaMapCreateRequest);
        }
        return ResponseHelper.successful(true);
    }
}
