package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.vo.CardGroupVO;
import com.siteweb.accesscontrol.service.CardGroupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "CardGroupController",tags = "卡分组管理")
public class CardGroupController {
    @Autowired
    private CardGroupService cardGroupService;

    @ApiOperation("获取所有卡分组")
    @GetMapping("/cardgroup")
    public ResponseEntity<ResponseResult> getCardGroups(){
        return ResponseHelper.successful(cardGroupService.findAll());
    }

    @ApiOperation("添加卡分组")
    @PostMapping("/cardgroup")
    public ResponseEntity<ResponseResult> createCardGroup(@RequestBody CardGroupVO cardGroupVO){
        return ResponseHelper.successful(cardGroupService.createCardGroup(cardGroupVO));
    }

    @ApiOperation("更新卡分组")
    @PutMapping("/cardgroup")
    public ResponseEntity<ResponseResult> updateCardGroup(@RequestBody CardGroupVO cardGroupVO){
        return ResponseHelper.successful(cardGroupService.updateCardGroup(cardGroupVO));
    }

    @ApiOperation("删除卡分组")
    @DeleteMapping("/cardgroup")
    public ResponseEntity<ResponseResult> deleteCardGroupById(@RequestBody List<Integer> ids){
        ids.forEach(id -> cardGroupService.deleteById(id));
        return ResponseHelper.successful(true);
    }
}
