package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.service.DoorTimeGroupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(value = "DoorGroupController", tags = "门分组映射管理")
public class DoorTimeGroupController {
    @Autowired
    private DoorTimeGroupService doorTimeGroupService;

    @GetMapping("/doortimegroup")
    public ResponseEntity<ResponseResult> findAll(){
          return ResponseHelper.successful(doorTimeGroupService.findDoorTimeGroup());
    }
}
