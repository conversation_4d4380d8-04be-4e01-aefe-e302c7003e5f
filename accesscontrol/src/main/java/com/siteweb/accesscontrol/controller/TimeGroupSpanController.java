package com.siteweb.accesscontrol.controller;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.accesscontrol.dto.TimeGroupSpanDTO;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.entity.TimeGroupSpan;
import com.siteweb.monitoring.service.TimeGroupSpanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "TimeGroupSpanController", tags = "时间段管理")
public class TimeGroupSpanController {
    @Autowired
    TimeGroupSpanService timeGroupSpanService;

    @ApiOperation("获取所有时间组信息")
    @GetMapping(value = "/timegroup/timespan")
    public ResponseEntity<ResponseResult> findAll(Integer timeGroupId) {
        List<TimeGroupSpan> timeGroupSpans = timeGroupSpanService.findByTimeGroupId(timeGroupId);
        return ResponseHelper.successful(BeanUtil.copyToList(timeGroupSpans, TimeGroupSpanDTO.class));
    }
}
