package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.dto.FingerprintDTO;
import com.siteweb.accesscontrol.service.FingerprintAuthService;
import com.siteweb.accesscontrol.service.FingerprintService;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Slf4j
@Api(value = "FingerprintController", tags = "指纹管理")
public class FingerprintController {
    @Autowired
    FingerprintService fingerprintService;
    @Autowired
    private FingerprintAuthService fingerprintAuthService;

    @ApiOperation("获取指纹信息通过卡id与厂商id")
    @GetMapping("/fingerprint")
    public ResponseEntity<ResponseResult> get(Integer cardId, Integer vendor) {
        return ResponseHelper.successful(fingerprintService.findFingerprintData(cardId, vendor));
    }

    @ApiOperation("批量修改卡指纹信息")
    @PutMapping("/fingerprint")
    public ResponseEntity<ResponseResult> update(@RequestBody List<FingerprintDTO> fingerprintDTOList) {
        //存在卡授权不允许修改指纹信息
        for (FingerprintDTO fingerPrintDTO : fingerprintDTOList) {
            if (fingerprintAuthService.existCardFingerprintAuth(fingerPrintDTO.getCardId())) {
                log.error("cardId:{}不允许修改已经授权的指纹", fingerPrintDTO.getCardId());
                return ResponseHelper.failed(String.valueOf(ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FINGER_DATA.value()), ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FINGER_DATA.getReasonPhrase(), HttpStatus.OK);
            }
        }
        return ResponseHelper.successful(fingerprintService.saveFingerprint(TokenUserUtil.getLoginUserId(),fingerprintDTOList));
    }
}
