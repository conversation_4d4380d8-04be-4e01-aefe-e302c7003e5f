package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.service.DoorCardBackupService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api")
public class DoorCardBackupController {
    @Autowired
    private DoorCardBackupService doorCardBackupService;

    @ApiOperation("获取所有的门卡备份记录")
    @GetMapping("/doorcardbackup")
    public ResponseEntity<ResponseResult> getDoorCardBackup() {
        return ResponseHelper.successful(doorCardBackupService.findAll());
    }

    @ApiOperation("获取门备份门树")
    @GetMapping("/doorcardbackup/doortree")
    public ResponseEntity<ResponseResult> getDoorTree(){
        return ResponseHelper.successful(doorCardBackupService.getBackupDoorTree());
    }


    @ApiOperation("获取卡备份门树")
    @GetMapping("/doorcardbackup/cardtree")
    public ResponseEntity<ResponseResult> getCardTree(){
        return ResponseHelper.successful(doorCardBackupService.getBackupCardTree());
    }
}
