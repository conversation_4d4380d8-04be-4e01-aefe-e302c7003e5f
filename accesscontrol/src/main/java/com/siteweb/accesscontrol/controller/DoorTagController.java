package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.entity.DoorTag;
import com.siteweb.accesscontrol.service.DoorTagService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
public class DoorTagController {
    @Autowired
    private DoorTagService doorTagService;

    @GetMapping("/doortag")
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(doorTagService.findAll());
    }

    @GetMapping("/doortag/{id}")
    public ResponseEntity<ResponseResult> getById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(doorTagService.findById(id));
    }

    @PostMapping("/doortag")
    public ResponseEntity<ResponseResult> createDoorTag(@RequestBody DoorTag doorTag) {
        return ResponseHelper.successful(doorTagService.insert(doorTag));
    }

    @DeleteMapping(value = "/doortag", params = "ids")
    public ResponseEntity<ResponseResult> deleteByIds(String ids) {
        List<Integer> doorTagIds = StringUtils.splitToIntegerList(ids);
        return ResponseHelper.successful(doorTagService.deleteByIds(doorTagIds));
    }

    @PutMapping(value = "/doortag")
    public ResponseEntity<ResponseResult> updateDoorTag(@RequestBody DoorTag doorTag) {
        return ResponseHelper.successful(doorTagService.updateById(doorTag));
    }
}
