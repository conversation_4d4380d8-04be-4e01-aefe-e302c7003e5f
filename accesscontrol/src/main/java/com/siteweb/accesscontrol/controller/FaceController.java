package com.siteweb.accesscontrol.controller;

import cn.hutool.crypto.digest.MD5;
import com.siteweb.accesscontrol.entity.FaceData;
import com.siteweb.accesscontrol.service.FaceDataAuthService;
import com.siteweb.accesscontrol.service.FaceDataService;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Objects;

@RestController
@RequestMapping("/api")
@Api(value = "FaceController", tags = "人脸管理")
@Slf4j
public class FaceController {
    @Autowired
    private FaceDataService faceDataService;
    @Autowired
    private FaceDataAuthService faceDataAuthService;

    @ApiOperation("根据卡id获取人脸照片")
    @GetMapping(value = "/faceimg",params = "cardId")
    public ResponseEntity<byte[]> getFaceData(Integer cardId) {
        FaceData faceData = faceDataService.findByCardId(cardId);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE);
        if (Objects.isNull(faceData) || Objects.isNull(faceData.getFaceData())) {
            return new ResponseEntity<>(new byte[0], httpHeaders, HttpStatus.OK);
        }
        byte[] imgData = faceData.getFaceData();

        return new ResponseEntity<>(imgData, httpHeaders, HttpStatus.OK);
    }

    @ApiOperation("根据卡id获取人脸信息照片的md5值")
    @GetMapping(value = "/facemd5",params = "cardId")
    public ResponseEntity<ResponseResult> getFaceDataMd5(Integer cardId){
        FaceData faceData = faceDataService.findByCardId(cardId);
        if (Objects.isNull(faceData) || Objects.isNull(faceData.getFaceData())) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.successful(MD5.create().digest(faceData.getFaceData()));
    }


    @ApiOperation("新增人脸信息")
    @PutMapping("/face")
    public ResponseEntity<ResponseResult> createFaceData(@RequestParam("file") MultipartFile file, Integer cardId) throws IOException {
        if (file == null || file.isEmpty()) {
            return ResponseHelper.failed("file not empty");
        }
        if (faceDataAuthService.existByCardId(cardId)) {
            log.error("卡id:{},新增的人脸存在授权信息", cardId);
            return ResponseHelper.failed(String.valueOf(ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FACE_DATA.value()), ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FACE_DATA.getReasonPhrase(), HttpStatus.OK);
        }
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        byte[] facedDataByte = file.getBytes();
        faceDataService.createFaceData(loginUserId, cardId, facedDataByte);
        return ResponseHelper.successful();
    }

    @ApiOperation("删除人脸信息")
    @DeleteMapping (value = "/face/{cardId}")
    public ResponseEntity<ResponseResult> deleteFaceData(@PathVariable("cardId") Integer cardId){
        if (faceDataAuthService.existByCardId(cardId)) {
            log.error("卡id:{},删除的人脸存在授权信息", cardId);
            return ResponseHelper.failed(String.valueOf(ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FACE_DATA.value()), ErrorCode.NOT_ALLOWED_DELETE_AUTHORIZED_FACE_DATA.getReasonPhrase(), HttpStatus.OK);
        }
        return ResponseHelper.successful(faceDataService.deleteFaceData(cardId));
    }
}
