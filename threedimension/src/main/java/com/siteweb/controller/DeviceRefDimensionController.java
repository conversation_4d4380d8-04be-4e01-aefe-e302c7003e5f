package com.siteweb.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.HeaderUtil;
import com.siteweb.domain.DeviceRefDimension;
import com.siteweb.dto.DeviceRefDimensionDTO;
import com.siteweb.service.DeviceRefDimensionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Optional;

/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2020-10-20 15:19:08
 */
@RestController
@RequestMapping("/api" )
public class DeviceRefDimensionController {

    private static final String ENTITY_NAME = "DeviceRefDimension";

    @Autowired
    private DeviceRefDimensionService deviceRefDimensionService;

    /**
     * GET  /devicerefdimensions : get the DeviceRefDimensions.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of DeviceRefDimensions in body
     */
    @GetMapping("/devicerefdimensions" )
    public ResponseEntity<ResponseResult> getDeviceRefDimensions() {
        return ResponseHelper.successful(deviceRefDimensionService.findDeviceRefDimensions());
    }

    @GetMapping(value = "/devicerefdimensions", params = "equipmentId")
    public ResponseEntity<ResponseResult> getDeviceRefDimensions(@RequestParam Integer equipmentId) {
        return ResponseHelper.successful(deviceRefDimensionService.findDeviceRefDimensions(equipmentId));
    }

}
