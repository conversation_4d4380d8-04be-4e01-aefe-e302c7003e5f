package com.siteweb.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.domain.DimensionModel;
import com.siteweb.dto.DimensionModelDTO;
import com.siteweb.service.DimensionModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email
 * @date 2021-01-11 17:12:25
 */
@RestController
@RequestMapping("/api")
public class DimensionModelController {

    @Autowired
    private DimensionModelService dimensionModelService;

    /**
     * GET  /dimensionmodels : get the DimensionModels.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of DimensionModels in body
     */
    @GetMapping("/dimensionmodels")
    public ResponseEntity<ResponseResult> getDimensionModels() {
        return ResponseHelper.successful(dimensionModelService.findDimensionModels());
    }

    @GetMapping(value = "/dimensionmodels", params = {"dimensionModelCategory"})
    public ResponseEntity<ResponseResult> getDimensionModelsByDimensionModelCategory(@RequestParam("dimensionModelCategory") Integer dimensionModelCategory) {
        return ResponseHelper.successful(
                dimensionModelService.findDimensionModelsByDimensionModelCategory(dimensionModelCategory));
    }

    /**
     * GET  /dimensionmodels/:id  get the DimensionModel by id.
     *
     * @param dimensionModelId the DimensionModelId
     * @return the ResponseEntity with status 200 (OK) and with body the DimensionModel, or with status 404 (Not Found)
     */
    @GetMapping("/dimensionmodels/{dimensionModelId}")
    public ResponseEntity<ResponseResult> getDimensionModelById(@PathVariable("dimensionModelId") Integer dimensionModelId) {
        DimensionModel dimensionModel = dimensionModelService.findById(dimensionModelId);
        return Optional.ofNullable(dimensionModel)
                       .map(ResponseHelper::successful)
                       .orElse(ResponseHelper.failed("dimensionModelId错误"));
    }

    /**
     * Post /dimensionmodels : create a new DimensionModel
     *
     * @return the ResponseEntity with status 201 (Created) and with body the new DimensionModel,
     * or with status 400 (Bad Request) if the DimensionModel has already an ID
     */
    @PostMapping(value = "/dimensionmodels")
    public ResponseEntity<ResponseResult> createDimensionModel(@RequestParam("dimensionModelCategory") Integer dimensionModelCategory, @RequestParam("file") MultipartFile file) {
        DimensionModel result = dimensionModelService.createDimensionModel(dimensionModelCategory, file);
        return ResponseHelper.successful(result);
    }

    /**
     * PUT  /dimensionmodels : Updates an existing DimensionModel.
     *
     * @param dimensionModelDTO the DimensionModel to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated DimensionModel,
     * or with status 404 (Not Found) if the dimensionModelId is not exists,
     */
    @PutMapping(value = "/dimensionmodels")
    public ResponseEntity<ResponseResult> updateDimensionModel(@Valid @RequestBody DimensionModelDTO dimensionModelDTO) {
        if (dimensionModelDTO.getDimensionModelId() == null) {
            return ResponseHelper.failed("dimensionModelId必填");
        }
        DimensionModel result = dimensionModelService.updateDimensionModel(dimensionModelDTO.build());
        return ResponseHelper.successful(result);
    }

    /**
     * DELETE  /dimensionmodels/:id : delete the DimensionModel by id.
     *
     * @param id the id of the DimensionModel to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping(value = "/dimensionmodels/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDimensionModel(@PathVariable Integer id) {
        DimensionModel dimensionModel = dimensionModelService.findById(id);
        if (dimensionModel == null) {
            return ResponseHelper.failed("id不正确");
        }
        dimensionModelService.deleteById(id);
        return ResponseHelper.successful();
    }


    /**
     * DELETE  /dimensionmodels/:id : delete the DimensionModel by id.
     *
     * @param file the id of the DimensionModel to delete  @PathVariable("file" )
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping(value = "/dimensionmodels", params = {"file"})
    public ResponseEntity<ResponseResult> deleteDimensionModelByFile(@RequestParam("file") String file) {
        DimensionModel result = dimensionModelService.deleteDimensionModel(file);
        if (ObjectUtil.isNull(result)) {
            return ResponseHelper.failed("file不正确");
        }
        return ResponseHelper.successful(result);
    }
}
