package com.siteweb.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.domain.DimensionLinks;
import com.siteweb.dto.DimensionLinksImportDTO;
import com.siteweb.dto.DimensionLinksQuery;
import com.siteweb.service.DimensionLinksService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 链路管理
 * Creation Date: 2024/11/14
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class DimensionLinksController {
    private final DimensionLinksService dimensionLinksService;

    @GetMapping("/dimensionlinks/list")
    public ResponseEntity<ResponseResult> findDimensionLinkList(DimensionLinksQuery query) {
        return ResponseHelper.successful(dimensionLinksService.findDimensionLinkList(query));
    }
    //查找某个设备的前后链路
    @GetMapping("/dimensionlinks/prepost")
    public ResponseEntity<ResponseResult> findPrePostDimensionLinkMapByDevice(DimensionLinksQuery query) {
        return ResponseHelper.successful(dimensionLinksService.findPrePostDimensionLinkMapByDevice(query));
    }

    @GetMapping("/dimensionlinks")
    public ResponseEntity<ResponseResult> getDeviceRefDimensions() {
        return ResponseHelper.successful(dimensionLinksService.findDimensionLinks());
    }

    @PostMapping(value = "/dimensionlinks")
    public ResponseEntity<ResponseResult> createDimensionLinks(@RequestBody DimensionLinks dimensionLinks) {
        dimensionLinksService.createDimensionLinks(dimensionLinks);
        return ResponseHelper.successful();
    }

    @PutMapping(value = "/dimensionlinks")
    public ResponseEntity<ResponseResult> updateDimensionLinks(@RequestBody DimensionLinks dimensionLinks) {
        if (dimensionLinks.getLineId() == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        dimensionLinksService.updateDimensionLinks(dimensionLinks);
        return ResponseHelper.successful();
    }

    @DeleteMapping(value = "/dimensionlinks", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDimensionLinks(@RequestParam List<String> lineIds) {
        if (CollUtil.isEmpty(lineIds)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        dimensionLinksService.deleteDimensionLinks(lineIds);
        return ResponseHelper.successful();
    }

    @ApiOperation("批量导入链路管理")
    @PostMapping(value = "/dimensionlinks/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importDimensionLinks(@RequestBody List<DimensionLinksImportDTO> dimensionLinksImports) {
        return ResponseHelper.successful(dimensionLinksService.importDimensionLinks(dimensionLinksImports));
    }

}
