package com.siteweb.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.domain.DimensionConfigure;
import com.siteweb.model.DimensionConfigureAll;
import com.siteweb.service.DimensionConfigureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <AUTHOR>
 * @email
 * @date 2021-05-21 16:02:28
 */
@RestController
@RequestMapping("/api")
public class DimensionConfigureController {

    private static final String ENTITY_NAME = "DimensionConfigure";

    @Autowired
    private DimensionConfigureService dimensionConfigureService;

    @GetMapping("/dimensionconfigures/all")
    public ResponseEntity<ResponseResult> getDimensionConfigureAll(String type) throws IOException {
        DimensionConfigureAll result = dimensionConfigureService.getDimensionConfigureAll(type);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/dimensionconfigures/material")
    public ResponseEntity<ResponseResult> createDimensionConfigureMaterial(@RequestBody JsonNode jsonNode) {
        DimensionConfigure result = dimensionConfigureService.createDimensionConfigureJsonNode(jsonNode, 1);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/dimensionconfigures/model")
    public ResponseEntity<ResponseResult> createDimensionConfigureModel(@RequestBody JsonNode jsonNode) {
        DimensionConfigure result = dimensionConfigureService.createDimensionConfigureJsonNode(jsonNode, 2);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/dimensionconfigures/texture")
    public ResponseEntity<ResponseResult> createDimensionConfigureTexture(@RequestBody JsonNode jsonNode) {
        DimensionConfigure result = dimensionConfigureService.createDimensionConfigureJsonNode(jsonNode, 3);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/dimensionconfigures/template")
    public ResponseEntity<ResponseResult> createDimensionConfigureTemplate(@RequestBody JsonNode jsonNode) {
        DimensionConfigure result = dimensionConfigureService.createDimensionConfigureJsonNode(jsonNode, 4);
        return ResponseHelper.successful(result);
    }

    @PostMapping(value = "/dimensionconfigures/capacitypalette")
    public ResponseEntity<ResponseResult> createDimensionConfigureCapacityPalette(@RequestBody JsonNode jsonNode) {
        DimensionConfigure result = dimensionConfigureService.createDimensionConfigureJsonNode(jsonNode, 5);
        return ResponseHelper.successful(result);
    }


    @DeleteMapping(value = "/dimensionconfigures", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDimensionConfigure(String uuid, Integer type) {
        DimensionConfigure dimensionConfigure = dimensionConfigureService.findByDimensionConfigureUuidAndDimensionConfigureType(
                uuid, type);
        if (dimensionConfigure == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        dimensionConfigureService.deleteByDimensionConfigureUuid(uuid, type);
        return ResponseHelper.successful();
    }
}
