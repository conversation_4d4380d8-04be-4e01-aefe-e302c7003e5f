package com.siteweb.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.domain.DimensionDesigner;
import com.siteweb.dto.DimensionDesignerDTO;
import com.siteweb.service.DimensionDesignerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email
 * @date 2020-10-20 15:19:08
 */
@RestController
@RequestMapping("/api")
public class DimensionDesignerController {

    @Autowired
    private DimensionDesignerService dimensionDesignerService;

    /**
     * GET  /dimensiondesigners : get the DimensionDesigners.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of DimensionDesigners in body
     */
    @GetMapping("/dimensiondesigners")
    public ResponseEntity<ResponseResult> getDimensionDesigners() {
        return ResponseHelper.successful(dimensionDesignerService.findDimensionDesigners());
    }

    /**
     * GET  /dimensiondesigners/:id  get the DimensionDesigner by id.
     *
     * @param dimensionDesignerId the DimensionDesignerId
     * @return the ResponseEntity with status 200 (OK) and with body the DimensionDesigner, or with status 404 (Not Found)
     */
    @GetMapping("/dimensiondesigners/{dimensionDesignerId}")
    public ResponseEntity<ResponseResult> getDimensionDesignerById(@PathVariable("dimensionDesignerId") Integer dimensionDesignerId) throws IOException {
        DimensionDesigner dimensionDesigner = dimensionDesignerService.findById(dimensionDesignerId);
        return Optional.ofNullable(dimensionDesigner)
                       .map(ResponseHelper::successful)
                       .orElse(ResponseHelper.failed("dimensionDesignerId不正确"));
    }

    /**
     * Post /dimensiondesigners : create a new DimensionDesigner
     *
     * @param dimensionDesignerDTO the DimensionDesigner to create
     * @return the ResponseEntity with status 201 (Created) and with body the new DimensionDesigner,
     * or with status 400 (Bad Request) if the DimensionDesigner has already an ID
     */
    @PostMapping(value = "/dimensiondesigners")
    public ResponseEntity<ResponseResult> createDimensionDesigner(@Valid @RequestBody DimensionDesignerDTO dimensionDesignerDTO) {
        DimensionDesigner result = dimensionDesignerService.createDimensionDesigner(dimensionDesignerDTO.build());
        return ResponseHelper.successful(result);
    }

    /**
     * PUT  /dimensiondesigners : Updates an existing DimensionDesigner.
     *
     * @param dimensionDesignerDTO the DimensionDesigner to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated DimensionDesigner,
     * or with status 404 (Not Found) if the dimensionDesignerId is not exists,
     */
    @PutMapping(value = "/dimensiondesigners")
    public ResponseEntity<ResponseResult> updateDimensionDesigner(@Valid @RequestBody DimensionDesignerDTO dimensionDesignerDTO) {
        if (dimensionDesignerDTO.getDimensionDesignerId() == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        DimensionDesigner result = dimensionDesignerService.updateDimensionDesigner(dimensionDesignerDTO.build());
        return ResponseHelper.successful(result);
    }

    /**
     * DELETE  /dimensiondesigners/:id : delete the DimensionDesigner by id.
     *
     * @param id the id of the DimensionDesigner to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping(value = "/dimensiondesigners/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteDimensionDesigner(@PathVariable Integer id) throws IOException {
        DimensionDesigner dimensionDesigner = dimensionDesignerService.findById(id);
        if (dimensionDesigner == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        dimensionDesignerService.deleteById(id);
        return ResponseHelper.successful();
    }

}
