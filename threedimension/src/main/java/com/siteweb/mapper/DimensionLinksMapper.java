package com.siteweb.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.domain.DimensionLinks;
import com.siteweb.dto.DimensionLinksNameSearchResultDTO;
import com.siteweb.dto.DimensionLinksQuery;

import java.util.List;

public interface DimensionLinksMapper extends BaseMapper<DimensionLinks> {

    List<DimensionLinks> findDimensionLinkList(DimensionLinksQuery query);

    List<DimensionLinksNameSearchResultDTO> findDimensionLinkListByDevice(DimensionLinksQuery query);

    int insert(DimensionLinks dimensionLinks);

    int updateById(DimensionLinks dimensionLinks);

    int batchInsert(List<DimensionLinks> list);

    int batchUpdate(List<DimensionLinks> list);
}
