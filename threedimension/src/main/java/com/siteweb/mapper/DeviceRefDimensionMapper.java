package com.siteweb.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.domain.DeviceRefDimension;

import java.util.List;

public interface DeviceRefDimensionMapper extends BaseMapper<DeviceRefDimension> {

    void deleteByDimensionDesignerId(Integer dimensionDesignerId);

    List<DeviceRefDimension> findByEquipmentId(Integer dimensionDesignerId);

    void insertBatch(List<DeviceRefDimension> deviceRefDimensionList);
}
