package com.siteweb.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.domain.DimensionConfigure;

import java.util.List;

public interface DimensionConfigureMapper extends BaseMapper<DimensionConfigure> {

    List<DimensionConfigure> findByDimensionConfigureType(Integer dimensionConfigureType);

    DimensionConfigure findByDimensionConfigureUuid(String uuid);

    void deleteByDimensionConfigureUuid(String dimensionConfigureUuid);

    DimensionConfigure findByDimensionConfigureUuidAndDimensionConfigureType(String dimensionConfigureUuid, Integer type);

    void deleteByDimensionConfigureUuidAndDimensionConfigureType(String dimensionConfigureUuid, Integer type);
}
