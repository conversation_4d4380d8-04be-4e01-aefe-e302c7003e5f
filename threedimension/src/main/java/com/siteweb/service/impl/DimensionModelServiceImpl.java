package com.siteweb.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.domain.DimensionModel;
import com.siteweb.dto.DimensionModelDTO;
import com.siteweb.mapper.DimensionModelMapper;
import com.siteweb.service.DimensionModelService;
import com.siteweb.utility.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Service("dimensionModelService")
public class DimensionModelServiceImpl implements DimensionModelService {
    private final static String PATH = "dimension";
    private final static SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");

    @Autowired
    private StorageService storageService;
    @Autowired
    private DimensionModelMapper dimensionModelMapper;

    @Autowired
    private Environment env;

    @Override
    public List<DimensionModel> findDimensionModels() {
        return this.dimensionModelMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public DimensionModel createDimensionModel(DimensionModelDTO dimensionModelDTO) {
        MultipartFile file = dimensionModelDTO.getFile();
        if (file != null) {
            storageService.store(file, PATH);
            dimensionModelDTO.setDimensionModelFile(
                    env.getProperty("fileserver.rootPath") + "/" + PATH + "/" + file.getOriginalFilename());
        }
        DimensionModel dimensionModel = dimensionModelDTO.build();
        this.dimensionModelMapper.insert(dimensionModel);
        return dimensionModel;
    }

    @Override
    public DimensionModel createDimensionModel(Integer dimensionModelCategory, MultipartFile file) {
        DimensionModel dimensionModel = new DimensionModel();
        String dimensionModelName = "";
        if (file != null) {
            String datetime = formatter.format(new Date());
            String filename = file.getOriginalFilename();
            if (filename.indexOf("~") > -1) {
                int splitIndex = filename.indexOf("~");
                filename = filename.substring(splitIndex + 1);
            }
            dimensionModelName = datetime + "~" + filename; // new Date().getTime()+file.getOriginalFilename();
            storageService.store(file, PATH, dimensionModelName);
            dimensionModel.setDimensionModelFile(PATH + "/" + dimensionModelName);
        }
        dimensionModel.setDimensionModelCategory(dimensionModelCategory);
        dimensionModel.setDimensionModelName(dimensionModelName.split("\\.")[0]);
        this.dimensionModelMapper.insert(dimensionModel);
        return dimensionModel;
    }

    @Override
    public DimensionModel deleteDimensionModel(String fileName) {
        DimensionModel dimensionModel = this.dimensionModelMapper.findByDimensionModelFile(fileName);
        try {
            if (dimensionModel != null) {
                this.dimensionModelMapper.deleteById(dimensionModel.getDimensionModelId());
            }
            storageService.deleteFile(fileName, "");
        } catch (IOException e) {
            log.error("Dimension文件删除异常{}", ExceptionUtil.stacktraceToString(e));
        }
        return dimensionModel;
    }


    @Override
    public void deleteById(Integer dimensionModelId) {
        this.dimensionModelMapper.deleteById(dimensionModelId);
    }

    @Override
    public DimensionModel updateDimensionModel(DimensionModel dimensionModel) {
        this.dimensionModelMapper.updateById(dimensionModel);
        return dimensionModel;
    }

    @Override
    public DimensionModel findById(Integer dimensionModelId) {
        return this.dimensionModelMapper.selectById(dimensionModelId);
    }

    @Override
    public List<DimensionModel> findDimensionModelsByDimensionModelCategory(Integer dimensionModelCategory) {
        return this.dimensionModelMapper.findDimensionModelsByDimensionModelCategory(dimensionModelCategory);
    }
}
