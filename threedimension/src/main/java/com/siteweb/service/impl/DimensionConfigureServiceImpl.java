package com.siteweb.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.domain.DimensionConfigure;
import com.siteweb.mapper.DimensionConfigureMapper;
import com.siteweb.model.DimensionConfigureAll;
import com.siteweb.service.DimensionConfigureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("dimensionConfigureService")
public class DimensionConfigureServiceImpl implements DimensionConfigureService {

    @Autowired
    private DimensionConfigureMapper dimensionConfigureMapper;

    @Override
    public void deleteById(Integer dimensionConfigureId) {
        this.dimensionConfigureMapper.deleteById(dimensionConfigureId);
    }

    @Override
    public DimensionConfigure findById(Integer dimensionConfigureId) {
        return this.dimensionConfigureMapper.selectById(dimensionConfigureId);
    }


    @Override
    public DimensionConfigureAll getDimensionConfigureAll(String type) {
        DimensionConfigureAll dimensionConfigureAll = new DimensionConfigureAll();
        if (type == null || type.equals("")) {
            dimensionConfigureAll.setMaterials(findArrayNodeByDimensionConfigureType(1));
            dimensionConfigureAll.setModels(findArrayNodeByDimensionConfigureType(2));
            dimensionConfigureAll.setTextures(findArrayNodeByDimensionConfigureType(3));
            dimensionConfigureAll.setTemplates(findArrayNodeByDimensionConfigureType(4));
            dimensionConfigureAll.setPalettes(findArrayNodeByDimensionConfigureType(5));
        } else {
            List<Integer> typeList = StringUtils.getIntegerListByString(type);
            for (Integer tempType : typeList) {
                switch (tempType) {
                    case 1:
                        dimensionConfigureAll.setMaterials(findArrayNodeByDimensionConfigureType(1));
                        break;
                    case 2:
                        dimensionConfigureAll.setModels(findArrayNodeByDimensionConfigureType(2));
                        break;
                    case 3:
                        dimensionConfigureAll.setTextures(findArrayNodeByDimensionConfigureType(3));
                        break;
                    case 4:
                        dimensionConfigureAll.setTemplates(findArrayNodeByDimensionConfigureType(4));
                        break;
                    case 5:
                        dimensionConfigureAll.setPalettes(findArrayNodeByDimensionConfigureType(5));
                        break;
                }
            }
        }


        return dimensionConfigureAll;
    }

    @Override
    public DimensionConfigure createDimensionConfigureJsonNode(JsonNode jsonNode, Integer type) {
        DimensionConfigure dimensionConfigureJsonNode = getDimensionConfigureJsonNode(jsonNode, type);
        //dimensionConfigureId为空插入否则更新
        if (ObjectUtil.isNull(dimensionConfigureJsonNode.getDimensionConfigureId())) {
            this.dimensionConfigureMapper.insert(dimensionConfigureJsonNode);
        }else{
            this.dimensionConfigureMapper.updateById(dimensionConfigureJsonNode);
        }
        return dimensionConfigureJsonNode;
    }

    @Override
    public DimensionConfigure findByDimensionConfigureUuidAndDimensionConfigureType(String dimensionConfigureUuid, Integer type) {
        return this.dimensionConfigureMapper.findByDimensionConfigureUuidAndDimensionConfigureType(dimensionConfigureUuid, type);
    }

    private DimensionConfigure getDimensionConfigureJsonNode(JsonNode jsonNode, Integer type) {
        DimensionConfigure dimensionConfigure = new DimensionConfigure();
        String uuid = jsonNode.get("uuid")
                              .asText();

        if (StrUtil.isNotBlank(uuid)) {
            dimensionConfigure = this.dimensionConfigureMapper.findByDimensionConfigureUuid(uuid);
        }
        if (dimensionConfigure == null) {
            dimensionConfigure = new DimensionConfigure();
            dimensionConfigure.setDimensionConfigureType(type);
            dimensionConfigure.setDimensionConfigureUuid(uuid);
        }
        try {
            dimensionConfigure.setDimensionConfigure(jsonNode.toString());
        } catch (Exception e) {
            log.error("getDimensionConfigureJsonNode error {}", e.getMessage());
        }

        return dimensionConfigure;
    }

    public List<JsonNode> findArrayNodeByDimensionConfigureType(Integer type) {
        List<DimensionConfigure> textureDimensionConfigureList = this.dimensionConfigureMapper.findByDimensionConfigureType(type);
        List<JsonNode> arrayNodeList = new ArrayList<>();
        try {
            for (DimensionConfigure dimensionConfigure : textureDimensionConfigureList) {
                arrayNodeList.add(JacksonUtil.getInstance()
                                             .readTree(dimensionConfigure.getDimensionConfigure()));
            }
        } catch (Exception e) {
            log.error("findArrayNodeByDimensionConfigureType error {}", e.getMessage());
        }
        return arrayNodeList;
    }

    @Override
    public DimensionConfigure findByDimensionConfigureUuid(String dimensionConfigureUuid) {
        return this.dimensionConfigureMapper.findByDimensionConfigureUuid(dimensionConfigureUuid);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByDimensionConfigureUuid(String dimensionConfigureUuid, Integer type) {
        this.dimensionConfigureMapper.deleteByDimensionConfigureUuidAndDimensionConfigureType(dimensionConfigureUuid, type);
    }

}
