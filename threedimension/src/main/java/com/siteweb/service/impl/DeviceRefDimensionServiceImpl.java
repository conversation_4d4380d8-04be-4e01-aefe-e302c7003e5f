package com.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.domain.DeviceRefDimension;
import com.siteweb.mapper.DeviceRefDimensionMapper;
import com.siteweb.service.DeviceRefDimensionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("deviceRefDimensionService")
public class DeviceRefDimensionServiceImpl implements DeviceRefDimensionService {

    @Autowired
    private DeviceRefDimensionMapper deviceRefDimensionMapper;

    @Override
    public List<DeviceRefDimension> findDeviceRefDimensions() {
        return this.deviceRefDimensionMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public DeviceRefDimension createDeviceRefDimension(DeviceRefDimension deviceRefDimension) {
        this.deviceRefDimensionMapper.insert(deviceRefDimension);
        return deviceRefDimension;
    }

    @Override
    public void deleteById(Integer deviceRefDimensionId) {
        this.deviceRefDimensionMapper.deleteById(deviceRefDimensionId);
    }

    @Override
    public DeviceRefDimension updateDeviceRefDimension(DeviceRefDimension deviceRefDimension) {
        this.deviceRefDimensionMapper.insert(deviceRefDimension);
        return deviceRefDimension;
    }

    @Override
    public DeviceRefDimension findById(Integer deviceRefDimensionId) {
        DeviceRefDimension deviceRefDimension = this.deviceRefDimensionMapper.selectById(deviceRefDimensionId);
        return deviceRefDimension;
    }

    @Transactional
    public void updateDeviceRefDimension(Integer dimensionDesignerId, JsonNode content) {
        if (content == null) {
            return;
        }
        this.deviceRefDimensionMapper.deleteByDimensionDesignerId(dimensionDesignerId);
        try {
            JsonNode body = content.get("body");
            if (body != null) {
                List<DeviceRefDimension> deviceRefDimensionList = getDeviceRefDimensions(body, dimensionDesignerId);
                //批量插入
                if (CollUtil.isNotEmpty(deviceRefDimensionList)) {
                    deviceRefDimensionMapper.insertBatch(deviceRefDimensionList);
                }
            }
        } catch (Exception e) {
            log.error("JsonNode 解析异常{}", e);
        }
    }

    @Override
    public void deleteByDimensionDesignerId(Integer dimensionDesignerId) {
        this.deviceRefDimensionMapper.deleteByDimensionDesignerId(dimensionDesignerId);
    }

    @Override
    public List<DeviceRefDimension> findDeviceRefDimensions(Integer equipmentId) {
        List<DeviceRefDimension> deviceRefDimensionList = this.deviceRefDimensionMapper.findByEquipmentId(equipmentId);
        return deviceRefDimensionList;
    }

    public List<DeviceRefDimension> getDeviceRefDimensions(JsonNode children, Integer dimensionDesignerId) {
        List<DeviceRefDimension> result = new ArrayList<>();
        String currentPath = children.get("uuid").asText();
        String type = children.get("type").asText();
        processBind(children.get("bindObject"), 7, true, currentPath, type, dimensionDesignerId, result);
        processBind(children.get("bindRack"), 9, false, currentPath, type, dimensionDesignerId, result);
        JsonNode childrenList = children.get("children");
        if (childrenList != null && childrenList.size() > 0) {
            for (int i = 0; i < childrenList.size(); i++) {
                JsonNode node = childrenList.get(i);
                result.addAll(getDeviceRefDimensions(node, dimensionDesignerId));
            }
        }
        return result;
    }

    private void processBind(JsonNode node, int expectedType, boolean isMultiple, String currentPath,
                             String type, Integer dimensionDesignerId, List<DeviceRefDimension> result) {
        if (node != null) {
            Integer objectType = node.get("type").asInt();
            if (objectType != null && objectType.equals(expectedType)) {
                JsonNode values = node.get("value");
                if (values != null && values.size() > 0) {
                    int length = isMultiple ? values.size() : 1;//bindObject数组元素都是设备id, bindRack数组第一个是机架id,第二个是机架id所在的resourcestructureid
                    for (int i = 0; i < length; i++) {
                        Integer value = values.get(i).asInt();
                        if (value != null) {
                            DeviceRefDimension deviceRefDimension = new DeviceRefDimension();
                            deviceRefDimension.setObjectBindData(value);
                            deviceRefDimension.setRelation(currentPath);
                            deviceRefDimension.setType(type);
                            deviceRefDimension.setDimensionDesignerId(dimensionDesignerId);
                            result.add(deviceRefDimension);
                        }
                    }
                }
            }
        }
    }
}
