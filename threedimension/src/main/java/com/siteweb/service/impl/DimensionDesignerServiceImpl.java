package com.siteweb.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.common.util.FileUtil;
import com.siteweb.common.util.JacksonUtil;
import com.siteweb.domain.DimensionDesigner;
import com.siteweb.mapper.DimensionDesignerMapper;
import com.siteweb.service.DeviceRefDimensionService;
import com.siteweb.service.DimensionDesignerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Service("dimensionDesignerService")
public class DimensionDesignerServiceImpl implements DimensionDesignerService {


    @Autowired
    private Environment env;
    @Autowired
    private DimensionDesignerMapper dimensionDesignerMapper;
    @Autowired
    private DeviceRefDimensionService deviceRefDimensionService;

    @Override
    public List<DimensionDesigner> findDimensionDesigners() {
        return this.dimensionDesignerMapper.selectList(Wrappers.emptyWrapper());
    }

    @Transactional
    public DimensionDesigner createDimensionDesigner(DimensionDesigner dimensionDesigner) {
        dimensionDesigner.setUpdateTime(new Date());
        this.dimensionDesignerMapper.insert(dimensionDesigner);
        if (null != dimensionDesigner.getContent()) {
            JsonNode jsonObject = dimensionDesigner.getContent();
            ((ObjectNode) jsonObject).put("id", dimensionDesigner.getDimensionDesignerId());
            FileUtil.writeToFile(jsonObject.toString(), env.getProperty("fileserver.rootPath") + "/3d/diagrams", dimensionDesigner.getDimensionDesignerId() + ".json");
        }
        if (StrUtil.isBlank(dimensionDesigner.getFilePath())) {
            dimensionDesigner.setFilePath("3d/diagrams/" + dimensionDesigner.getDimensionDesignerId() + ".json");
            this.dimensionDesignerMapper.updateById(dimensionDesigner);
        }
        deviceRefDimensionService.updateDeviceRefDimension(dimensionDesigner.getDimensionDesignerId(), dimensionDesigner.getContent());
        return dimensionDesigner;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer dimensionDesignerId) {
        deviceRefDimensionService.deleteByDimensionDesignerId(dimensionDesignerId);
        this.dimensionDesignerMapper.deleteById(dimensionDesignerId);
    }

    @Override
    public DimensionDesigner updateDimensionDesigner(DimensionDesigner dimensionDesigner) {
        dimensionDesigner.setUpdateTime(new Date());
        if (null != dimensionDesigner.getContent()) {
            JsonNode jsonObject = dimensionDesigner.getContent();
            ((ObjectNode) jsonObject).put("id", dimensionDesigner.getDimensionDesignerId());
            FileUtil.writeToFile(jsonObject.toString(), env.getProperty("fileserver.rootPath") + "/3d/diagrams", dimensionDesigner.getDimensionDesignerId() + ".json");
        }
        this.dimensionDesignerMapper.updateById(dimensionDesigner);

        deviceRefDimensionService.updateDeviceRefDimension(dimensionDesigner.getDimensionDesignerId(), dimensionDesigner.getContent());
        return dimensionDesigner;
    }

    @Override
    public DimensionDesigner findById(Integer dimensionDesignerId) throws IOException {
        DimensionDesigner dimensionDesigner = this.dimensionDesignerMapper.selectById(dimensionDesignerId);
        if (null != dimensionDesigner && null != dimensionDesigner.getFilePath()) {
            File file = new File(env.getProperty("fileserver.rootPath") + "/" + dimensionDesigner.getFilePath());
            if (file != null) {
                //dimensionDesigner.setContent(JSONObject.parseObject(FileUtils.readFileToString(file, StandardCharsets.UTF_8)));
                dimensionDesigner.setContent(JacksonUtil.getInstance().readTree(file));
                //dimensionDesigner.setContent(JacksonUtil.getInstance().readTree(FileUtils.readFileToString(file, StandardCharsets.UTF_8)));
            }
        }
        return dimensionDesigner;
    }
}
