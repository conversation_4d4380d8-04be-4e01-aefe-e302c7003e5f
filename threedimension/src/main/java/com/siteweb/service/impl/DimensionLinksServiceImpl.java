package com.siteweb.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.mapper.ComputerRackMapper;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.domain.DimensionLinks;
import com.siteweb.dto.*;
import com.siteweb.enums.DimensionLinksTypeEnum;
import com.siteweb.mapper.DimensionLinksMapper;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.service.DimensionLinksService;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("dimensionLinksService")
@RequiredArgsConstructor
public class DimensionLinksServiceImpl implements DimensionLinksService {
    private static final String COMMON_FIELD_CANNOT_EMPTY = "common.field.cannotEmpty";
    public static final String NOT_FOUND = " Not found";
    private final DimensionLinksMapper dimensionLinksMapper;
    private final ResourceStructureManager resourceStructureManager;
    private final EquipmentManager equipmentManager;
    private final ITDeviceService itDeviceService;
    private final ComputerRackMapper computerRackRepository;
    private final LocaleMessageSourceUtil messageSourceUtil;
    private final TransactionTemplate transactionTemplate;

    @Override
    public List<DimensionLinksDTO> findDimensionLinks() {
        List<DimensionLinks> dimensionLinks = dimensionLinksMapper.selectList(Wrappers.emptyWrapper());
        // 把同类型的id放在一起
        Map<Integer, List<Integer>> typeIdMaps = new HashMap<>();
        for (DimensionLinks dimensionLink : dimensionLinks) {
            typeIdMaps.computeIfAbsent(dimensionLink.getLocalObjectType(), t -> new ArrayList<>()).add(dimensionLink.getLocalObjectId());
            typeIdMaps.computeIfAbsent(dimensionLink.getRemoteObjectType(), t -> new ArrayList<>()).add(dimensionLink.getRemoteObjectId());
        }
        // 获取id的名称
        Map<Integer, Map<Integer, String>> idNameMap = getIdNameMap(typeIdMaps);
        return dimensionLinks.stream().map(dimensionLink -> {
            DimensionLinksDTO dimensionLinksDTO = BeanUtil.copyProperties(dimensionLink, DimensionLinksDTO.class);
            dimensionLinksDTO.setLineTypeName(DimensionLinksTypeEnum.getNameByValue(dimensionLink.getLineType()));
            String localObjectName = Optional.ofNullable(idNameMap.get(dimensionLinksDTO.getLocalObjectType()))
                    .map(m -> m.get(dimensionLinksDTO.getLocalObjectId())).orElse(null);
            dimensionLinksDTO.setLocalObjectName(localObjectName);
            String remoteObjectName = Optional.ofNullable(idNameMap.get(dimensionLinksDTO.getRemoteObjectType()))
                    .map(m -> m.get(dimensionLinksDTO.getRemoteObjectId())).orElse(null);
            dimensionLinksDTO.setRemoteObjectName(remoteObjectName);
            return dimensionLinksDTO;
        }).toList();
    }

    private Map<Integer, Map<Integer, String>> getIdNameMap(Map<Integer, List<Integer>> typeIdMaps) {
        Map<Integer, Map<Integer, String>> codeNameMap = new HashMap<>();
        typeIdMaps.forEach((key, value) -> {
            SourceType sourceType = SourceType.valueOf(key);
            if (Objects.isNull(sourceType)) {
                return;
            }
            List<Integer> idList = value.stream().distinct().toList();
            // 如果后续类型增加，在这里增加
            Map<Integer, String> collect = null;
            switch (sourceType) {
                case EQUIPMENT -> {
                    List<Equipment> equipments = equipmentManager.getEquipmentByIds(idList);
                    collect = equipments.stream().collect(
                            Collectors.toMap(Equipment::getEquipmentId, Equipment::getEquipmentName, (n1, n2) -> n1)
                    );
                    codeNameMap.put(sourceType.value(), collect);
                }
                case COMPUTERRACK -> {
                    List<ComputerRack> computerRacks = computerRackRepository.selectBatchIds(idList);
                    collect = computerRacks.stream().collect(
                            Collectors.toMap(ComputerRack::getComputerRackId, ComputerRack::getComputerRackName, (n1, n2) -> n1)
                    );
                }
                case ITDEVICE -> {
                    List<ITDevice> itDeviceList = itDeviceService.findITDeviceByIds(idList);
                    collect = itDeviceList.stream().collect(
                            Collectors.toMap(ITDevice::getITDeviceId, ITDevice::getITDeviceName, (n1, n2) -> n1)
                    );
                }
                default -> log.error("findDimensionLinks ==> 未处理的类型");
            }
            codeNameMap.put(sourceType.value(), collect);

        });
        return codeNameMap;
    }

    @Override
    public void createDimensionLinks(DimensionLinks dimensionLinks) {
        List<DimensionLinks> dimensionLinkAll = dimensionLinksMapper.selectList(Wrappers.emptyWrapper());
        if (hasCycle(dimensionLinks, dimensionLinkAll)) {
            throw new BusinessException("插入此链路将创建一个循环!");
        }
        dimensionLinks.setLineId(StringUtils.getUUId());
        dimensionLinksMapper.insert(dimensionLinks);
    }

    /**
     * 检查导入数据是否存在死循环，用名称寻找
     */
    private boolean hasCycleImport(DimensionLinksImportDTO current, List<DimensionLinksImportDTO> dimensionLinksImports) {
        // 构造 Map 提高查找效率
        Map<String, List<DimensionLinksImportDTO>> localObjectIdMap = dimensionLinksImports.stream()
                .collect(Collectors.groupingBy(link -> link.getLocalObjectName() + link.getLineTypeName()));
        // 检测环路
        return hasCycleImport(current, localObjectIdMap, new HashSet<>());
    }

    private boolean hasCycleImport(DimensionLinksImportDTO current,
                                   Map<String, List<DimensionLinksImportDTO>> localObjectIdMap,
                                   Set<String> visitedNodes) {
        String currentKey = current.getLocalObjectName() + current.getLineTypeName();
        // 如果节点已经访问过，检测到环路
        if (visitedNodes.contains(currentKey)) {
            return true;
        }
        // 标记节点为已访问
        visitedNodes.add(currentKey);
        String nextKey = current.getRemoteObjectName() + current.getLineTypeName();
        List<DimensionLinksImportDTO> nextLinks = localObjectIdMap.get(nextKey);
        if (nextLinks != null) {
            for (DimensionLinksImportDTO next : nextLinks) {
                if (hasCycleImport(next, localObjectIdMap, new HashSet<>(visitedNodes))) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 判断插入的链路是否会变成死循环
     *
     * @param newLink 新链路
     * @return true 死循环
     */
    private boolean hasCycle(DimensionLinks newLink, List<DimensionLinks> dimensionLinkAll) {
        // 构造 Map 提高查找效率
        Map<String, List<DimensionLinks>> localObjectIdMap = dimensionLinkAll.stream()
                .collect(Collectors.groupingBy(link -> buildLinkKey(link.getLocalObjectId(), link.getLocalObjectType(), link.getLineType())));
        // 加入新数据
        String newKey = buildLinkKey(newLink.getLocalObjectId(), newLink.getLocalObjectType(), newLink.getLineType());
        localObjectIdMap.computeIfAbsent(newKey, k -> new ArrayList<>()).add(newLink);
        // 检测环路
        return hasCycle(newLink, localObjectIdMap, new HashSet<>());
    }

    private boolean hasCycle(DimensionLinks current,
                             Map<String, List<DimensionLinks>> localObjectIdMap,
                             Set<String> visitedNodes) {
        String currentKey = buildLinkKey(current.getLocalObjectId(), current.getLocalObjectType(), current.getLineType());
        // 如果节点已经访问过，检测到环路
        if (visitedNodes.contains(currentKey)) {
            return true;
        }
        // 标记节点为已访问
        visitedNodes.add(currentKey);
        String nextKey = buildLinkKey(current.getRemoteObjectId(), current.getRemoteObjectType(), current.getLineType());
        List<DimensionLinks> nextLinks = localObjectIdMap.get(nextKey);
        if (nextLinks != null) {
            for (DimensionLinks next : nextLinks) {
                if (hasCycle(next, localObjectIdMap, new HashSet<>(visitedNodes))) {
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    public void updateDimensionLinks(DimensionLinks dimensionLinks) {
        List<DimensionLinks> dimensionLinkAll = dimensionLinksMapper.selectList(Wrappers.emptyWrapper());
        if (hasCycle(dimensionLinks, dimensionLinkAll)) {
            throw new BusinessException("修改此链路将创建一个循环!");
        }
        dimensionLinksMapper.updateById(dimensionLinks);
    }

    @Override
    public void deleteDimensionLinks(List<String> lineIds) {
        dimensionLinksMapper.deleteByIds(lineIds);
    }

    @Override
    public List<ImportErrorInfoDTO> importDimensionLinks(List<DimensionLinksImportDTO> dimensionLinksImports) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<DimensionLinks> dimensionLinksInsertList = new ArrayList<>();
        List<DimensionLinks> dimensionLinksUpdateList = new ArrayList<>();
        List<DimensionLinks> dimensionLinkAll = dimensionLinksMapper.selectList(Wrappers.emptyWrapper());
        Set<String> idSet = new HashSet<>();
        for (int i = 0; i < dimensionLinksImports.size(); i++) {
            DimensionLinksImportDTO dimensionLinksImportDTO = dimensionLinksImports.get(i);
            // 校验数据
            if (verifyDataExist(dimensionLinksImportDTO, importErrorInfoList, i)) {
                continue;
            }
            DimensionLinks dimensionLinks = dimensionLinksImportDTO.toDimensionLinks();
            Integer lineType = DimensionLinksTypeEnum.getValueByName(dimensionLinksImportDTO.getLineTypeName());
            if (Objects.isNull(lineType)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.lineTypeName, dimensionLinksImportDTO.getLineTypeName() + NOT_FOUND));
                continue;
            }
            dimensionLinks.setLineType(lineType);
            // 通过名字去找id和type
            String localObjectName = dimensionLinksImportDTO.getLocalObjectName();
            Pair<Integer, Integer> localObjectPair = findObjectPair(localObjectName);
            if (Objects.isNull(localObjectPair)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.localObjectName, localObjectName + NOT_FOUND));
                continue;
            }
            dimensionLinks.setLocalObjectType(localObjectPair.getKey());
            dimensionLinks.setLocalObjectId(localObjectPair.getValue());
            // 远端
            String remoteObjectName = dimensionLinksImportDTO.getRemoteObjectName();
            Pair<Integer, Integer> remoteObjectPair = findObjectPair(remoteObjectName);
            if (Objects.isNull(remoteObjectPair)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.remoteObjectName, remoteObjectName + NOT_FOUND));
                continue;
            }
            dimensionLinks.setRemoteObjectType(remoteObjectPair.getKey());
            dimensionLinks.setRemoteObjectId(remoteObjectPair.getValue());
            // 判断是否死循环
            if (hasCycleImport(dimensionLinksImportDTO, dimensionLinksImports) || hasCycle(dimensionLinks, dimensionLinkAll)) {
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.remoteObjectName, remoteObjectName + "会形成死循环"));
                importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.localObjectName, localObjectName + "会形成死循环"));
                continue;
            }
            if (CharSequenceUtil.isBlank(dimensionLinksImportDTO.getLineId())) {
                // 不存在id一定是新增
                dimensionLinks.setLineId(StringUtils.getUUId());
                dimensionLinksInsertList.add(dimensionLinks);
            } else {
                // 判断excel中的id是否重复
                if (idSet.contains(dimensionLinksImportDTO.getLineId())) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.lineId, dimensionLinksImportDTO.getLineId() + " 链路id重复"));
                    continue;
                }
                idSet.add(dimensionLinks.getLineId());
                // 数据库判断是否存在id
                boolean idExists = dimensionLinksMapper.exists(Wrappers.lambdaQuery(DimensionLinks.class)
                        .eq(DimensionLinks::getLineId, dimensionLinksImportDTO.getLineId()));
                if (idExists) {
                    // id存在则为修改
                    dimensionLinksUpdateList.add(dimensionLinks);
                } else {
                    dimensionLinksInsertList.add(dimensionLinks);
                }

            }
        }
        transactionTemplate.execute(status -> {
            if (CollUtil.isNotEmpty(dimensionLinksInsertList)) {
                dimensionLinksMapper.batchInsert(dimensionLinksInsertList);
            }
            if (CollUtil.isNotEmpty(dimensionLinksUpdateList)) {
                dimensionLinksMapper.batchUpdate(dimensionLinksUpdateList);
            }
            return null; // 返回成功才会提交事务
        });
        return importErrorInfoList;
    }

    @Override
    public Map<String, List<DimensionLinkListDTO>> findPrePostDimensionLinkMapByDevice(DimensionLinksQuery query) {
        if (!Objects.equals(query.getResultType(), 2)) {
            return Collections.emptyMap();
        }
        Map<String, List<DimensionLinkListDTO>> result = new HashMap<>();
        //lineId有重复
        List<DimensionLinksNameSearchResultDTO> dimensionLinksNameSearchResultDTOs = dimensionLinksMapper.findDimensionLinkListByDevice(query);
        if (dimensionLinksNameSearchResultDTOs.isEmpty()) {
            return Collections.emptyMap();
        }
        Set<Pair<Integer, Integer>> searchResultIdTypeSet = new HashSet<>();
        // 把同类型的id放在一起
        Map<Integer, List<Integer>> typeIdMaps = new HashMap<>();
        dimensionLinksNameSearchResultDTOs.forEach(dimensionLink -> {
            searchResultIdTypeSet.add(Pair.of(dimensionLink.getObjectId(), dimensionLink.getObjectType()));
            typeIdMaps.computeIfAbsent(dimensionLink.getLocalObjectType(), t -> new ArrayList<>()).add(dimensionLink.getLocalObjectId());
            typeIdMaps.computeIfAbsent(dimensionLink.getRemoteObjectType(), t -> new ArrayList<>()).add(dimensionLink.getRemoteObjectId());
        });
        // 获取id的名称(去重)
        Map<Integer, Map<Integer, String>> idNameMap = getIdNameMap(typeIdMaps);

        // 去重（按 lineId）
        List<DimensionLinksNameSearchResultDTO> distinctDimensionLinksNameSearchResultDTO = dimensionLinksNameSearchResultDTOs.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DimensionLinksNameSearchResultDTO::getLineId))),
                        ArrayList::new
                ));
        List<DimensionLinkListDTO> dimensionLinksDTOs = new ArrayList<>();
        distinctDimensionLinksNameSearchResultDTO.forEach(dto -> {
            DimensionLinkListDTO dimensionLinksDTO = BeanUtil.copyProperties(dto, DimensionLinkListDTO.class);
            dimensionLinksDTO.setLocalObjectName(getObjectName(idNameMap, dto.getLocalObjectType(), dto.getLocalObjectId()));
            dimensionLinksDTO.setRemoteObjectName(getObjectName(idNameMap, dto.getRemoteObjectType(), dto.getRemoteObjectId()));
            dimensionLinksDTOs.add(dimensionLinksDTO);
        });
        searchResultIdTypeSet.forEach(pair -> {
            Integer id = pair.getKey();
            Integer type = pair.getValue();
            String name = getObjectName(idNameMap, type, id);
            List<DimensionLinkListDTO> filtered = dimensionLinksDTOs.stream().filter(dimensionLinksDTO -> Objects.equals(dimensionLinksDTO.getLocalObjectId(), id) || Objects.equals(dimensionLinksDTO.getRemoteObjectId(), id)).toList();
            result.put(name, filtered);

        });
        return result;
    }


    private String getObjectName(Map<Integer, Map<Integer, String>> idNameMap, Integer objectType, Integer objectId) {
        return Optional.ofNullable(idNameMap.get(objectType))
                .map(m -> m.get(objectId))
                .orElse("unknown");
    }

    @Override
    public List<List<DimensionLinkListDTO>> findDimensionLinkList(DimensionLinksQuery query) {
        List<DimensionLinks> dimensionLinks = dimensionLinksMapper.findDimensionLinkList(query);
        if (CollUtil.isEmpty(dimensionLinks)) {
            return List.of();
        }
        List<List<DimensionLinks>> list;
        // 把同类型的id放在一起
        Map<Integer, List<Integer>> typeIdMaps = new HashMap<>();
        // 完整路径
        if (Objects.equals(query.getResultType(), 1)) {
            List<DimensionLinks> dimensionLinkAll = dimensionLinksMapper.selectList(Wrappers.emptyWrapper());
            // 先寻找最顶层的节点
            Set<DimensionLinks> parentDimensionLinks = new HashSet<>();
            Set<DimensionLinks> topDimensionLinks = getTopDimensionLinks(dimensionLinks, dimensionLinkAll, parentDimensionLinks);
            // 获取本身以及下层的节点
            Set<DimensionLinks> childDimensionLinks = getChildDimensionLinks(dimensionLinks, dimensionLinkAll);
            // 合并两个 List 并转成 Set,所有节点的数据
            Set<DimensionLinks> dimensionLinkAllSet = Stream.concat(parentDimensionLinks.stream(), childDimensionLinks.stream())
                    .collect(Collectors.toSet());
            // 返回完整路径
            list = constructPaths(topDimensionLinks, dimensionLinkAllSet);
            dimensionLinkAllSet.forEach(dimensionLink -> {
                typeIdMaps.computeIfAbsent(dimensionLink.getLocalObjectType(), t -> new ArrayList<>()).add(dimensionLink.getLocalObjectId());
                typeIdMaps.computeIfAbsent(dimensionLink.getRemoteObjectType(), t -> new ArrayList<>()).add(dimensionLink.getRemoteObjectId());
            });
        } else {
            list = new ArrayList<>(dimensionLinks.size());
            // 返回单独节点分组
            dimensionLinks.forEach(dimensionLink -> {
                typeIdMaps.computeIfAbsent(dimensionLink.getLocalObjectType(), t -> new ArrayList<>()).add(dimensionLink.getLocalObjectId());
                typeIdMaps.computeIfAbsent(dimensionLink.getRemoteObjectType(), t -> new ArrayList<>()).add(dimensionLink.getRemoteObjectId());
                List<DimensionLinks> dimensionLinkList = List.of(dimensionLink);
                list.add(dimensionLinkList);
            });
        }
        // 获取id的名称
        Map<Integer, Map<Integer, String>> idNameMap = getIdNameMap(typeIdMaps);
        return list.stream().map(dimensionLinksList -> dimensionLinksList.stream().map(dimensionLink -> {
            DimensionLinkListDTO dimensionLinksDTO = BeanUtil.copyProperties(dimensionLink, DimensionLinkListDTO.class);
            String localObjectName = Optional.ofNullable(idNameMap.get(dimensionLinksDTO.getLocalObjectType()))
                    .map(m -> m.get(dimensionLinksDTO.getLocalObjectId())).orElse(null);
            dimensionLinksDTO.setLocalObjectName(localObjectName);
            String remoteObjectName = Optional.ofNullable(idNameMap.get(dimensionLinksDTO.getRemoteObjectType()))
                    .map(m -> m.get(dimensionLinksDTO.getRemoteObjectId())).orElse(null);
            dimensionLinksDTO.setRemoteObjectName(remoteObjectName);
            return dimensionLinksDTO;
        }).toList()).toList();
    }

    /**
     * 获取本身以及下层的节点
     */
    private Set<DimensionLinks> getChildDimensionLinks(List<DimensionLinks> dimensionLinks, List<DimensionLinks> dimensionLinkAll) {
        Set<DimensionLinks> childDimensionLinks = new HashSet<>();
        Map<String, List<DimensionLinks>> forwardMap = dimensionLinkAll.stream()
                .collect(Collectors.groupingBy(link -> buildLinkKey(link.getLocalObjectId(), link.getLocalObjectType(), link.getLineType())));
        for (DimensionLinks dimensionLink : dimensionLinks) {
            buildChildDimensionLinks(dimensionLink, forwardMap, childDimensionLinks);
        }
        return childDimensionLinks;
    }

    /**
     * 构造链路寻找下面节点的条件key
     */
    private String buildLinkKey(Integer objectId, Integer objectType, Integer lineType) {
        return String.format("%s%s%s", objectId, objectType, lineType);
    }

    private void buildChildDimensionLinks(DimensionLinks current, Map<String, List<DimensionLinks>> backwardMap,
                                          Set<DimensionLinks> childDimensionLinks) {
        // 获取下面的节点
        childDimensionLinks.add(current);
        String backwardKey = buildLinkKey(current.getRemoteObjectId(), current.getRemoteObjectType(), current.getLineType());
        List<DimensionLinks> backwardLinks = backwardMap.get(backwardKey);
        // 向下递归
        if (CollUtil.isNotEmpty(backwardLinks)) {
            for (DimensionLinks next : backwardLinks) {
                buildChildDimensionLinks(next, backwardMap, childDimensionLinks);
            }
        }
    }

    /**
     * 获取上层节点
     *
     * @param parentDimensionLinks 本身以及父节点
     * @return 最顶层父节点
     */
    private Set<DimensionLinks> getTopDimensionLinks(List<DimensionLinks> dimensionLinks, List<DimensionLinks> dimensionLinkAll, Set<DimensionLinks> parentDimensionLinks) {
        Set<DimensionLinks> topDimensionLinks = new HashSet<>();
        Map<String, List<DimensionLinks>> backwardMap = dimensionLinkAll.stream()
                .collect(Collectors.groupingBy(link -> buildLinkKey(link.getRemoteObjectId(), link.getRemoteObjectType(), link.getLineType())));
        for (DimensionLinks dimensionLink : dimensionLinks) {
            buildTopDimensionLinks(dimensionLink, backwardMap, topDimensionLinks, parentDimensionLinks);
        }
        return topDimensionLinks;
    }

    private void buildTopDimensionLinks(DimensionLinks current, Map<String, List<DimensionLinks>> backwardMap,
                                        Set<DimensionLinks> topDimensionLinks, Set<DimensionLinks> parentDimensionLinks) {
        // 获取上一步的节点
        parentDimensionLinks.add(current);
        String backwardKey = buildLinkKey(current.getLocalObjectId(), current.getLocalObjectType(), current.getLineType());
        List<DimensionLinks> backwardLinks = backwardMap.get(backwardKey);
        // 如果没有更多路径可以探索，保存当前路径
        if (CollUtil.isEmpty(backwardLinks)) {
            topDimensionLinks.add(current);
        } else {
            // 向前递归
            for (DimensionLinks next : backwardLinks) {
                buildTopDimensionLinks(next, backwardMap, topDimensionLinks, parentDimensionLinks);
            }
        }
    }


    /**
     * 构造完整路径
     *
     * @param dimensionLinks 条件查询结果集合
     * @return 完整路径列表
     */
    private List<List<DimensionLinks>> constructPaths(Set<DimensionLinks> dimensionLinks, Set<DimensionLinks> dimensionLinkAllSet) {
        List<List<DimensionLinks>> result = new ArrayList<>();
        // 构造 Map 提高查找效率
        Map<String, List<DimensionLinks>> localObjectIdMap = dimensionLinkAllSet.stream()
                .collect(Collectors.groupingBy(link -> buildLinkKey(link.getLocalObjectId(), link.getLocalObjectType(), link.getLineType())));
        for (DimensionLinks link : dimensionLinks) {
            List<List<DimensionLinks>> paths = new ArrayList<>();
            buildPaths(link, localObjectIdMap, new ArrayList<>(), paths);
            result.addAll(paths);
        }
        return result;
    }

    /**
     * 构造单条路径（递归）
     */
    private void buildPaths(DimensionLinks current,
                            Map<String, List<DimensionLinks>> localObjectIdMap,
                            List<DimensionLinks> path,
                            List<List<DimensionLinks>> paths) {
        path.add(current);
        String nextKey = buildLinkKey(current.getRemoteObjectId(), current.getRemoteObjectType(), current.getLineType());
        List<DimensionLinks> nextLinks = localObjectIdMap.get(nextKey);
        if (nextLinks == null || nextLinks.isEmpty()) {
            paths.add(new ArrayList<>(path));
        } else {
            // 如果有后续连接，为每个分支递归生成路径
            for (DimensionLinks next : nextLinks) {
                buildPaths(next, localObjectIdMap, new ArrayList<>(path), paths);
            }
        }
    }

    /**
     * 寻找链路对象的id和类型
     */
    private Pair<Integer, Integer> findObjectPair(String name) {
        // 根据不同数据源寻找id
        // 设备 增加"\" 前面的是resourceStructure名称 后面的设备名称
        if (name.contains("\\")) {
            String[] split = name.split("\\\\");
            if (split.length == 2) {
                ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureByName(split[0]);
                if (Objects.isNull(resourceStructure)) {
                    return null;
                }
                Equipment equipment = equipmentManager.findByResourceIdAndName(resourceStructure.getResourceStructureId(), split[1]);
                if (Objects.nonNull(equipment)) {
                    return Pair.of(SourceType.EQUIPMENT.value(), equipment.getEquipmentId());
                }
            }
        }
        Equipment equipment = equipmentManager.getEquipmentsByEquipmentName(name);
        if (Objects.nonNull(equipment)) {
            return Pair.of(SourceType.EQUIPMENT.value(), equipment.getEquipmentId());
        }
        // 机架
        ComputerRack computerRackName = computerRackRepository.findByComputerRackName(name);
        if (Objects.nonNull(computerRackName)) {
            return Pair.of(SourceType.COMPUTERRACK.value(), computerRackName.getComputerRackId());
        }
        // IT设备
        ITDevice itDevice = itDeviceService.findITDevice(name);
        if (Objects.nonNull(itDevice)) {
            return Pair.of(SourceType.ITDEVICE.value(), itDevice.getITDeviceId());
        }
        return null;
    }

    /**
     * 校验数据是否合法
     */
    private boolean verifyDataExist(DimensionLinksImportDTO dimensionLinksImportDTO, List<ImportErrorInfoDTO> importErrorInfoList, int i) {
        boolean flag = false;
        if (CharSequenceUtil.isBlank(dimensionLinksImportDTO.getDisplayName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.displayName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(dimensionLinksImportDTO.getLineTypeName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.lineTypeName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(dimensionLinksImportDTO.getLocalObjectName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.localObjectName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(dimensionLinksImportDTO.getLocalObjectName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.localObjectName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(dimensionLinksImportDTO.getLocalObjectPort())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.localObjectPort, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (CharSequenceUtil.isBlank(dimensionLinksImportDTO.getRemoteObjectName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.remoteObjectName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(dimensionLinksImportDTO.getRemoteObjectPort())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.remoteObjectPort, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(dimensionLinksImportDTO.getStaticPropertys())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, DimensionLinksImportDTO.Fields.staticPropertys, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        return flag;
    }
}
