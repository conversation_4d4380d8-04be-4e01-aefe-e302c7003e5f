package com.siteweb.service;


import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.domain.DeviceRefDimension;

import java.util.List;

public interface DeviceRefDimensionService {

    List<DeviceRefDimension> findDeviceRefDimensions();

    DeviceRefDimension createDeviceRefDimension(DeviceRefDimension deviceRefDimension);

    void deleteById(Integer deviceRefDimensionId);

    DeviceRefDimension updateDeviceRefDimension(DeviceRefDimension deviceRefDimension);

    DeviceRefDimension findById(Integer deviceRefDimensionId);

    void updateDeviceRefDimension(Integer dimensionDesignerId, JsonNode content);

    void deleteByDimensionDesignerId(Integer dimensionDesignerId);

    List<DeviceRefDimension> findDeviceRefDimensions(Integer equipmentId);
}

