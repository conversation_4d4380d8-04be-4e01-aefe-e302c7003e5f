package com.siteweb.service;


import com.siteweb.domain.DimensionLinks;
import com.siteweb.dto.DimensionLinkListDTO;
import com.siteweb.dto.DimensionLinksDTO;
import com.siteweb.dto.DimensionLinksImportDTO;
import com.siteweb.dto.DimensionLinksQuery;
import com.siteweb.utility.dto.ImportErrorInfoDTO;

import java.util.List;
import java.util.Map;

public interface DimensionLinksService {

    List<DimensionLinksDTO> findDimensionLinks();

    void createDimensionLinks(DimensionLinks dimensionLinks);

    void updateDimensionLinks(DimensionLinks dimensionLinks);

    void deleteDimensionLinks(List<String> lineIds);

    List<ImportErrorInfoDTO> importDimensionLinks(List<DimensionLinksImportDTO> dimensionLinksImports);

    /**
     * 链路管理相关链路查询
     */
    List<List<DimensionLinkListDTO>> findDimensionLinkList(DimensionLinksQuery query);

    Map<String, List<DimensionLinkListDTO>> findPrePostDimensionLinkMapByDevice(DimensionLinksQuery query);
}
