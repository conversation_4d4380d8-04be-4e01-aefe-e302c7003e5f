package com.siteweb.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.domain.DimensionConfigure;
import com.siteweb.model.DimensionConfigureAll;

import java.io.IOException;

public interface DimensionConfigureService {


    void deleteById(Integer dimensionConfigureId);

    DimensionConfigure findById(Integer dimensionConfigureId);

    DimensionConfigureAll getDimensionConfigureAll(String type) throws IOException;

    DimensionConfigure findByDimensionConfigureUuid(String dimensionConfigureUuid);

    void deleteByDimensionConfigureUuid(String dimensionConfigureUuid, Integer type);

    DimensionConfigure createDimensionConfigureJsonNode(JsonNode jsonNode, Integer type);

    DimensionConfigure findByDimensionConfigureUuidAndDimensionConfigureType(String dimensionConfigureUuid, Integer type);
}

