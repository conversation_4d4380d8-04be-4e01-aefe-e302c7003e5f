package com.siteweb.service;


import com.siteweb.domain.DimensionDesigner;

import java.io.IOException;
import java.util.List;

public interface DimensionDesignerService {

    List<DimensionDesigner> findDimensionDesigners();

    DimensionDesigner createDimensionDesigner(DimensionDesigner dimensionDesigner);

    void deleteById(Integer dimensionDesignerId);

    DimensionDesigner updateDimensionDesigner(DimensionDesigner dimensionDesigner);

    DimensionDesigner findById(Integer dimensionDesignerId) throws IOException;
}

