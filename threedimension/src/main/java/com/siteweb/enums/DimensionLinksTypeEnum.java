package com.siteweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 链路管理线路类型枚举
 */
@Getter
@AllArgsConstructor
public enum DimensionLinksTypeEnum {
    /**
     * 线路类型 Communication=1, Power=2
     */
    COMMUNICATION(1, "Communication"),
    POWER(2, "Power"),
    ;


    private final int value;
    private final String name;

    // 根据value查找枚举
    public static DimensionLinksTypeEnum findByValue(int value) {
        return Arrays.stream(values())
                .filter(entry -> entry.getValue() == value)
                .findFirst()
                .orElse(null);
    }

    public static DimensionLinksTypeEnum findByName(String name) {
        return Arrays.stream(values())
                .filter(entry -> Objects.equals(entry.getName(), name))
                .findFirst()
                .orElse(null);
    }

    public static String getNameByValue(int value) {
        DimensionLinksTypeEnum dimensionLinksTypeEnum = findByValue(value);
        if (Objects.isNull(dimensionLinksTypeEnum)) {
            return null;
        }
        return dimensionLinksTypeEnum.getName();
    }

    public static Integer getValueByName(String name) {
        DimensionLinksTypeEnum dimensionLinksTypeEnum = findByName(name);
        if (Objects.isNull(dimensionLinksTypeEnum)) {
            return null;
        }
        return dimensionLinksTypeEnum.getValue();
    }


}
