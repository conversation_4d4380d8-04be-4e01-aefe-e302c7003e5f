package com.siteweb.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class Texture {
    private String uuid;
    private String name;
    private String image;
    private List<Integer> wrap;
    private List<Integer> repeat;
    private List<Integer> offset;
    private String md5;
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatetime;
    private String category;
    private Boolean readonly;
}
