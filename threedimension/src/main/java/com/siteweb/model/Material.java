package com.siteweb.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class Material {
    private String uuid;
    private String name;
    private String type;
    private String color;
    private String map;
    private String emissive;
    private Double emissiveIntensity;
    private String specular;
    private Double shininess;
    private Boolean transparent;
    private Double opacity;
    private String side;
    private Double alphaTest;
    private Boolean depthTest;
    private Boolean depthWrite;
    private Boolean wireframe;
    private String envMap;
    private String aoMap;
    private String alphaMap;
    private String specularMap;
    private Double aoMapIntensity;
    private String lightMap;
    private Double lightMapIntensity;
    private String bumpMap;
    private Double bumpScale;
    private String emissiveMap;
    private Double clearcoat;
    private String clearcoatMap;
    private String clearcoatNormalMap;
    private Double roughness;
    private String roughnessMap;
    private Double metalness;
    private String metalnessMap;
    private Boolean visible;
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatetime;
    private String category;
    private Boolean readonly;
}
