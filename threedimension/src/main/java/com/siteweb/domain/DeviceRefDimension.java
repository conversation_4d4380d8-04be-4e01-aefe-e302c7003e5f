package com.siteweb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2020-10-20 15:19:08
 */
@Data
@NoArgsConstructor
@TableName("devicerefdimension")
public class DeviceRefDimension {

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Integer deviceRefDimensionId;
	/**
	 * 
	 */
	private Integer objectBindData;
	private String type;
	/**
	 * 
	 */
	private Integer dimensionDesignerId;
	/**
	 * 
	 */
	private String relation;
}
