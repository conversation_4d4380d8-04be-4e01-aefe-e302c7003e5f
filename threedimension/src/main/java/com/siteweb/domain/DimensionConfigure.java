package com.siteweb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-05-21 16:02:28
 */
@Data
@NoArgsConstructor
@TableName("dimensionconfigure")
public class DimensionConfigure {

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Integer dimensionConfigureId;
	/**
	 * 
	 */
	private Integer dimensionConfigureType;
	/**
	 * 
	 */
	private String dimensionConfigure;
	/**
	 * 
	 */
	private String dimensionConfigureUuid;
}
