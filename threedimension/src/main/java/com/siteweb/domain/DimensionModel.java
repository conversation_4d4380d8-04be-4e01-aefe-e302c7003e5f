package com.siteweb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-01-11 17:12:25
 */
@Data
@NoArgsConstructor
@TableName("dimensionmodel")
public class DimensionModel {

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Integer dimensionModelId;
	/**
	 * 
	 */
	private Integer dimensionModelCategory;
	/**
	 * 
	 */
	private String dimensionModelName;
	/**
	 * 
	 */
	private String dimensionModelFile;
}
