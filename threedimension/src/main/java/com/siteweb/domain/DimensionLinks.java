package com.siteweb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

/**
 * 链路管理
 *
 * <AUTHOR>
 * Creation Date: 2024/11/14
 */

@Data
@TableName("dimensionlinks")
public class DimensionLinks {

    @TableId(type = IdType.INPUT)
    private String lineId;
    private String displayName;
    /**
     *  Communication=1, Power=2
     */
    private Integer lineType;
    /**
     * 设备ID、机柜ID、IT设备ID
     */
    private Integer localObjectId;
    /**
     * ITDEVICE=10，COMPUTERRACK=9，EQUIPMENT=7
     */
    private Integer localObjectType;
    /**
     * 端口
     */
    private String localObjectPort;
    /**
     * 本端对象3D模型端口
     */
    private Integer localObjectModelPort;
    /**
     * 参考localObjectId
     */
    private Integer remoteObjectId;
    private Integer remoteObjectType;
    private String remoteObjectPort;
    private Integer remoteObjectModelPort;

    /**
     * 静态属性，用户录入,支持添加删除   如： {"电压":"12.v"}
     */
    private JsonNode staticPropertys;
    /**
     * 连接线备注
     */
    private String description;

}

