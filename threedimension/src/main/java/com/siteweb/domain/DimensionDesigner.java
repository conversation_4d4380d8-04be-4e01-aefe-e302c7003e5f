package com.siteweb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2020-10-20 15:19:08
 */
@Data
@NoArgsConstructor
@TableName("dimensiondesigner")
public class DimensionDesigner {

	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	private Integer dimensionDesignerId;
	/**
	 * 
	 */
	private String dimensionDesignerName;
	/**
	 * 
	 */
	private String filePath;
	/**
	 * 
	 */
	private Date updateTime;

	@TableField(exist = false)
	private JsonNode content;

	/*public String getContent(){
		return this.content.toString();
	}*/
}
