package com.siteweb.dto;

import com.siteweb.domain.DimensionModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * DTO
 *
 * <AUTHOR>
 * @email 
 * @date 2021-01-11 17:12:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DimensionModelDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    private Integer dimensionModelId;

    /**
     * 
     */
    private Integer dimensionModelCategory;

    /**
     * 
     */
    private String dimensionModelName;

    /**
     * 
     */
    private String dimensionModelFile;

    private MultipartFile file;

	public DimensionModel build() {
        DimensionModel dimensionModel = new DimensionModel();
        BeanUtils.copyProperties(this, dimensionModel);
        return dimensionModel;
	}
}
