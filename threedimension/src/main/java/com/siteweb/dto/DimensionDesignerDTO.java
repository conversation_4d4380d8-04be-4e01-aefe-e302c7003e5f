package com.siteweb.dto;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.domain.DimensionDesigner;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * DTO
 *
 * <AUTHOR>
 * @email 
 * @date 2020-10-20 15:19:08
 */
@Data
@NoArgsConstructor
public class DimensionDesignerDTO {
    /**
     * 
     */
    private Integer dimensionDesignerId;

    /**
     * 
     */
    private String dimensionDesignerName;

    /**
     * 
     */
    private String filePath;

    /**
     * 
     */
    private Date updateTime;

    private ObjectNode content;

	public DimensionDesigner build() {
        DimensionDesigner dimensionDesigner = new DimensionDesigner();
        BeanUtils.copyProperties(this, dimensionDesigner);
        return dimensionDesigner;
	}
}
