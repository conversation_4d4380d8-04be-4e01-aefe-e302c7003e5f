package com.siteweb.dto;

import com.siteweb.domain.DimensionConfigure;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * DTO
 *
 * <AUTHOR>
 * @email 
 * @date 2021-05-21 16:02:28
 */
@Data
@NoArgsConstructor
public class DimensionConfigureDTO {
    /**
     * 
     */
    private Integer dimensionConfigureId;

    /**
     * 
     */
    private Integer dimensionConfigureType;

    /**
     * 
     */
    private String dimensionConfigure;

    /**
     * 
     */
    private String dimensionConfigureUuid;

	public DimensionConfigure build() {
        DimensionConfigure dimensionConfigure = new DimensionConfigure();
        BeanUtils.copyProperties(this, dimensionConfigure);
        return dimensionConfigure;
	}
}
