package com.siteweb.dto;

import com.siteweb.domain.DeviceRefDimension;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * DTO
 *
 * <AUTHOR>
 * @email 
 * @date 2020-10-20 15:19:08
 */
@Data
@NoArgsConstructor
public class DeviceRefDimensionDTO {
    /**
     * 
     */
    private Integer deviceRefDimensionId;

    /**
     * 
     */
    private Integer objectBindData;
    private String type;

    /**
     * 
     */
    private Integer dimensionDesignerId;

    /**
     * 
     */
    private String relation;

	public DeviceRefDimension build() {
        DeviceRefDimension deviceRefDimension = new DeviceRefDimension();
        BeanUtils.copyProperties(this, deviceRefDimension);
        return deviceRefDimension;
	}
}
