package com.siteweb.dto;

import lombok.Data;

/**
 * 链路管理查询
 *
 * Creation Date: 2024/11/22
 */

@Data
public class DimensionLinksQuery {

    /**
     * 搜索名称条件 ObjectId ObjectName lineId displayName it设备的SerialNumber 全模糊搜索
     */
    private String searchName;
    /**
     * 线路类型  Communication=1, Power=2
     */
    private Integer lineType;

    /**
     * localObjectPort remoteObjectPort
     */
    private String port;
    /**
     * Line = 0,  Link = 1 返回类型 LINE 只返回相关数据，不寻找相关节点(一个短线就是一个数组) Link 完整路径: 路径嵌套列表(一条大长线一个数组)
     * Link = 2 设备/机架/IT设备的输入输出链路
     */
    private Integer resultType;


}

