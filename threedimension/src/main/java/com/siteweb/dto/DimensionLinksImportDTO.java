package com.siteweb.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.domain.DimensionLinks;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

/**
 * 链路管理
 *
 * <AUTHOR>
 * Creation Date: 2024/11/14
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class DimensionLinksImportDTO {

    /**
     * 手动输入，空则生成uuid
     */
    private String lineId;
    private String displayName;
    /**
     *  Communication=1, Power=2
     */
    private String lineTypeName;

    /**
     * 通过名字去找id和type
     */
    private String localObjectName;

    /**
     * 端口
     */
    private String localObjectPort;
    /**
     * 本端对象3D模型端口
     */
    private Integer localObjectModelPort;
    /**
     * 通过名字去找id和type
     */
    private String remoteObjectName;
    /**
     * 端口
     */
    private String remoteObjectPort;

    private Integer remoteObjectModelPort;
    /**
     * 静态属性，用户录入,支持添加删除   如： {"电压":"12.v"}
     */
    private JsonNode staticPropertys;
    /**
     * 连接线备注
     */
    private String description;

    public DimensionLinks toDimensionLinks() {
        DimensionLinks dimensionLinks = new DimensionLinks();
        dimensionLinks.setLineId(this.lineId);
        dimensionLinks.setDisplayName(this.displayName);
        dimensionLinks.setLocalObjectPort(this.localObjectPort);
        dimensionLinks.setLocalObjectModelPort(this.localObjectModelPort);
        dimensionLinks.setRemoteObjectPort(this.remoteObjectPort);
        dimensionLinks.setRemoteObjectModelPort(this.remoteObjectModelPort);
        dimensionLinks.setStaticPropertys(this.staticPropertys.isNull() ? new ObjectMapper().createObjectNode() : this.staticPropertys);
        dimensionLinks.setDescription(this.description);
        return dimensionLinks;
    }

}

