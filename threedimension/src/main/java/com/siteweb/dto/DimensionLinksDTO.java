package com.siteweb.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

/**
 * 链路管理
 *
 * <AUTHOR>
 * Creation Date: 2024/11/14
 */

@Data
public class DimensionLinksDTO {

    /**
     * 线路ID
     */
    private String lineId;
    /**
     * 线路显示名称
     */
    private String displayName;
    /**
     * 线路类型  Communication=1, Power=2
     */
    private Integer lineType;
    /**
     * 线路类型名称
     */
    private String lineTypeName;
    /**
     * 本端对象名称ID 设备ID、机柜ID、IT设备ID
     */
    private Integer localObjectId;
    /**
     * 本端对象名称
     */
    private String localObjectName;
    /**
     * 本端对象类型 ITDEVICE=10，COMPUTERRACK=9，EQUIPMENT=7
     */
    private Integer localObjectType;
    /**
     * 本端对象端口
     */
    private String localObjectPort;
    /**
     * 本端对象3D模型端口
     */
    private Integer localObjectModelPort;
    /**
     * 远端对象id
     */
    private Integer remoteObjectId;
    /**
     * 远端对象名称
     */
    private String remoteObjectName;
    /**
     * 远端对象类型
     */
    private Integer remoteObjectType;
    /**
     * 远端对象端口
     */
    private String remoteObjectPort;

    private Integer remoteObjectModelPort;
    /**
     * 静态属性，用户录入,支持添加删除   如： {"电压":"12.v"}
     */
    private JsonNode staticPropertys;
    /**
     * 连接线备注
     */
    private String description;

}

