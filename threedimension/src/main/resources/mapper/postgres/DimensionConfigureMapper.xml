<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.mapper.DimensionConfigureMapper">
    <delete id="deleteByDimensionConfigureUuid">
        DELETE
        FROM DimensionConfigure
        WHERE DimensionConfigureUuid = #{dimensionConfigureUuid}
    </delete>
    <delete id="deleteByDimensionConfigureUuidAndDimensionConfigureType">
        DELETE
        FROM DimensionConfigure
        WHERE DimensionConfigureUuid = #{dimensionConfigureUuid}
          AND DimensionConfigureType = #{type}
    </delete>
    <select id="findByDimensionConfigureType" resultType="com.siteweb.domain.DimensionConfigure">
        SELECT dimensionconfigureid, dimensionconfiguretype, dimensionconfigure, dimensionconfigureuuid
        FROM DimensionConfigure
        WHERE DimensionConfigureType = #{dimensionConfigureType}
    </select>
    <select id="findByDimensionConfigureUuid" resultType="com.siteweb.domain.DimensionConfigure">
        SELECT dimensionconfigureid, dimensionconfiguretype, dimensionconfigure, dimensionconfigureuuid
        FROM DimensionConfigure
        WHERE dimensionConfigureUuid = #{uuid}
    </select>
    <select id="findByDimensionConfigureUuidAndDimensionConfigureType"
            resultType="com.siteweb.domain.DimensionConfigure">
        SELECT dimensionconfigureid, dimensionconfiguretype, dimensionconfigure, dimensionconfigureuuid
        FROM DimensionConfigure
        WHERE DimensionConfigureUuid = #{dimensionConfigureUuid}
          AND DimensionConfigureType = #{type}
    </select>
</mapper>