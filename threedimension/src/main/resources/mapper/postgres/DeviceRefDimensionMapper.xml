<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.mapper.DeviceRefDimensionMapper">
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="deviceRefDimensionId">
        INSERT INTO DeviceRefDimension(objectBindData,type,dimensionDesignerId,relation) VALUES
        <foreach collection="deviceRefDimensionList" item="item" separator=",">
            (#{item.objectBindData},#{item.type},#{item.dimensionDesignerId},#{item.relation})
        </foreach>
    </insert>
    <delete id="deleteByDimensionDesignerId">
        DELETE
        FROM DeviceRefDimension
        WHERE DimensionDesignerId = #{dimensionDesignerId}
    </delete>
    <select id="findByEquipmentId" resultType="com.siteweb.domain.DeviceRefDimension">
        SELECT devicerefdimensionid, objectbinddata, type, dimensiondesignerid, relation
        FROM DeviceRefDimension
        WHERE ObjectBindData = #{equipmentId}
    </select>
</mapper>