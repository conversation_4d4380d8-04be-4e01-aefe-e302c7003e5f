<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.mapper.DimensionLinksMapper">
    <select id="findDimensionLinkListByDevice" resultType="com.siteweb.dto.DimensionLinksNameSearchResultDTO">
        (SELECT
        ds.localObjectId as objectId,
        ds.localObjectType as objectType,
        ds.lineId,
        ds.displayName,
        ds.lineType,
        ds.localObjectId,
        ds.localObjectType,
        ds.localObjectPort,
        ds.localObjectModelPort,
        ds.remoteObjectId,
        ds.remoteObjectType,
        ds.remoteObjectPort,
        ds.remoteObjectModelPort,
        ds.staticPropertys,
        ds.description
        FROM
        dimensionlinks ds
        <where>
            <if test="searchName != null and searchName != '' ">
                AND (
                ds.localObjectId LIKE concat('%', #{searchName}, '%') OR
                EXISTS (
                SELECT 1
                FROM tbl_equipment te
                WHERE te.EquipmentName LIKE concat('%', #{searchName}, '%')
                AND te.EquipmentId = ds.localObjectId
                ) OR
                EXISTS (
                SELECT 1
                FROM itdevice it
                WHERE (
                it.ITDeviceName LIKE concat('%', #{searchName}, '%') OR  it.SerialNumber LIKE concat('%', #{searchName}, '%')
                )
                AND it.ITDeviceId = ds.localObjectId
                ) OR
                EXISTS (
                SELECT 1
                FROM computerrack cr
                WHERE cr.ComputerRackName LIKE concat('%', #{searchName}, '%')
                AND cr.ComputerRackId = ds.localObjectId
                )
                )
            </if>
            <if test="lineType != null ">
                and ds.lineType = #{lineType}
            </if>
        </where>)

        UNION

        (SELECT
        ds.remoteObjectId as objectId,
        ds.remoteObjectType as objectType,
        ds.lineId,
        ds.displayName,
        ds.lineType,
        ds.localObjectId,
        ds.localObjectType,
        ds.localObjectPort,
        ds.localObjectModelPort,
        ds.remoteObjectId,
        ds.remoteObjectType,
        ds.remoteObjectPort,
        ds.remoteObjectModelPort,
        ds.staticPropertys,
        ds.description
        FROM
        dimensionlinks ds
        <where>
            <if test="searchName != null and searchName != '' ">
                AND (
                ds.remoteObjectId LIKE concat('%', #{searchName}, '%') OR
                EXISTS (
                SELECT 1
                FROM tbl_equipment te
                WHERE te.EquipmentName LIKE concat('%', #{searchName}, '%')
                AND te.EquipmentId = ds.remoteObjectId
                ) OR
                EXISTS (
                SELECT 1
                FROM itdevice it
                WHERE (
                it.ITDeviceName LIKE concat('%', #{searchName}, '%') OR  it.SerialNumber LIKE concat('%', #{searchName}, '%')
                )
                AND it.ITDeviceId = ds.remoteObjectId
                ) OR
                EXISTS (
                SELECT 1
                FROM computerrack cr
                WHERE cr.ComputerRackName LIKE concat('%', #{searchName}, '%')
                AND cr.ComputerRackId = ds.remoteObjectId
                )
                )
            </if>
            <if test="lineType != null ">
                and ds.lineType = #{lineType}
            </if>
        </where>)
    </select>
    <select id="findDimensionLinkList" resultType="com.siteweb.domain.DimensionLinks">
        SELECT
        ds.lineId,
        ds.displayName,
        ds.lineType,
        ds.localObjectId,
        ds.localObjectType,
        ds.localObjectPort,
        ds.localObjectModelPort,
        ds.remoteObjectId,
        ds.remoteObjectType,
        ds.remoteObjectPort,
        ds.remoteObjectModelPort,
        ds.staticPropertys,
        ds.description
        FROM
        dimensionlinks ds
        <where>
            <if test="searchName != null and searchName != '' ">
                AND (
                ds.lineId LIKE concat('%', #{searchName}, '%') OR
                ds.displayName LIKE concat('%', #{searchName}, '%') OR
                ds.localObjectId LIKE concat('%', #{searchName}, '%') OR
                ds.remoteObjectId LIKE concat('%', #{searchName}, '%') OR
                EXISTS (
                SELECT 1
                FROM tbl_equipment te
                WHERE te.EquipmentName LIKE concat('%', #{searchName}, '%')
                AND (te.EquipmentId = ds.localObjectId OR te.EquipmentId = ds.remoteObjectId)
                ) OR
                EXISTS (
                SELECT 1
                FROM itdevice it
                WHERE (
                it.ITDeviceName LIKE concat('%', #{searchName}, '%') OR  it.SerialNumber LIKE concat('%', #{searchName}, '%')
                )
                AND (it.ITDeviceId = ds.localObjectId OR it.ITDeviceId = ds.remoteObjectId)
                ) OR
                EXISTS (
                SELECT 1
                FROM computerrack cr
                WHERE cr.ComputerRackName LIKE concat('%', #{searchName}, '%')
                AND (cr.ComputerRackId = ds.localObjectId OR cr.ComputerRackId = ds.remoteObjectId)
                )
                )
            </if>
            <if test="lineType != null ">
                and ds.lineType = #{lineType}
            </if>
            <if test="port != null and port != ''">
                and (ds.localObjectPort = #{port} OR ds.remoteObjectPort = #{port})
            </if>
        </where>

    </select>

    <insert id="insert">
        INSERT INTO dimensionlinks (
        lineId, displayName, lineType, localObjectId, localObjectType,
        localObjectPort, localObjectModelPort, remoteObjectId, remoteObjectType, remoteObjectPort, remoteObjectModelPort,
        staticPropertys, description
        ) VALUES (
        #{lineId}, #{displayName}, #{lineType}, #{localObjectId}, #{localObjectType},
        #{localObjectPort}, #{localObjectModelPort},#{remoteObjectId}, #{remoteObjectType}, #{remoteObjectPort}, #{remoteObjectModelPort},
        #{staticPropertys}, #{description}
        )
    </insert>

    <update id="updateById">
        UPDATE dimensionlinks
        <set>
            <if test="displayName != null">displayName = #{displayName},</if>
            <if test="lineType != null">lineType = #{lineType},</if>
            <if test="localObjectId != null">localObjectId = #{localObjectId},</if>
            <if test="localObjectType != null">localObjectType = #{localObjectType},</if>
            <if test="localObjectPort != null">localObjectPort = #{localObjectPort},</if>
            <if test="localObjectModelPort != null">localObjectModelPort = #{localObjectModelPort},</if>
            <if test="remoteObjectId != null">remoteObjectId = #{remoteObjectId},</if>
            <if test="remoteObjectType != null">remoteObjectType = #{remoteObjectType},</if>
            <if test="remoteObjectPort != null">remoteObjectPort = #{remoteObjectPort},</if>
            <if test="remoteObjectModelPort != null">remoteObjectModelPort = #{remoteObjectModelPort},</if>
            <if test="staticPropertys != null">staticPropertys = #{staticPropertys},</if>
            <if test="description != null">description = #{description},</if>
        </set>
        WHERE lineId = #{lineId}
    </update>

    <insert id="batchInsert">
        INSERT INTO dimensionlinks (
        lineId, displayName, lineType, localObjectId, localObjectType,
        localObjectPort, localObjectModelPort, remoteObjectId, remoteObjectType, remoteObjectPort, remoteObjectModelPort,
        staticPropertys, description
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.lineId}, #{item.displayName}, #{item.lineType},
            #{item.localObjectId}, #{item.localObjectType}, #{item.localObjectPort},#{item.localObjectModelPort},
            #{item.remoteObjectId}, #{item.remoteObjectType}, #{item.remoteObjectPort}, #{item.remoteObjectModelPort},
            #{item.staticPropertys}, #{item.description}
            )
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE dimensionlinks
            <set>
                <if test="item.displayName != null">displayName = #{item.displayName},</if>
                <if test="item.lineType != null">lineType = #{item.lineType},</if>
                <if test="item.localObjectId != null">localObjectId = #{item.localObjectId},</if>
                <if test="item.localObjectType != null">localObjectType = #{item.localObjectType},</if>
                <if test="item.localObjectPort != null">localObjectPort = #{item.localObjectPort},</if>
                <if test="item.localObjectModelPort != null">localObjectModelPort = #{item.localObjectModelPort},</if>
                <if test="item.remoteObjectId != null">remoteObjectId = #{item.remoteObjectId},</if>
                <if test="item.remoteObjectType != null">remoteObjectType = #{item.remoteObjectType},</if>
                <if test="item.remoteObjectPort != null">remoteObjectPort = #{item.remoteObjectPort},</if>
                <if test="item.remoteObjectModelPort != null">remoteObjectModelPort = #{item.remoteObjectModelPort},</if>
                <if test="item.staticPropertys != null">staticPropertys = #{item.staticPropertys},</if>
                <if test="item.description != null">description = #{item.description},</if>
            </set>
            WHERE lineId = #{item.lineId}
        </foreach>
    </update>


</mapper>