<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.mapper.DimensionModelMapper">
    <select id="findDimensionModelsByDimensionModelCategory" resultType="com.siteweb.domain.DimensionModel">
        SELECT DimensionModelId, DimensionModelCategory, DimensionModelName, DimensionModelFile
        FROM DimensionModel
        WHERE DimensionModelCategory = #{dimensionModelCategory}
    </select>
    <select id="findByDimensionModelFile" resultType="com.siteweb.domain.DimensionModel">
        SELECT DimensionModelId, DimensionModelCategory, DimensionModelName, DimensionModelFile
        FROM DimensionModel
        WHERE DimensionModelFile = #{file}
    </select>
</mapper>