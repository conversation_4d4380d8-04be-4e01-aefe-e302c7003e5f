package com.siteweb.uicore.mqtt.topicHandler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.admin.entity.UserRole;
import com.siteweb.admin.mapper.UserRoleMapper;
import com.siteweb.uicore.mqtt.constant.TopicConstSubscriptionConst;
import com.siteweb.uicore.mqtt.model.RoleSyncUICoreModel;
import com.siteweb.uicore.mqtt.topicHandler.BaseConsumerTopicHandler;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class RoleSyncTopicHandler extends BaseConsumerTopicHandler {

    @Autowired
    UserRoleMapper userRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consumer(String topic, MqttMessage message) throws Exception {
        Pattern r = Pattern.compile(TopicConstSubscriptionConst.ROLE_CHANGE.getWildcardTopicPath());
        Matcher m = r.matcher(topic);
        if (!m.find()) {
            return;
        }

        String requestType = m.group(1);
        Integer roleId = Integer.valueOf(m.group(2));
        // 超管绑定管理员
        if (Objects.equals(roleId, 1)) {
            roleId = -1;
        }
        RoleSyncUICoreModel messageModel = JSONUtil.toBean(new String(message.getPayload()), RoleSyncUICoreModel.class);

        Integer finalRoleId = roleId;
        Consumer<Boolean> addOrUpdateExecutor = add -> {
            boolean realAdd = Boolean.TRUE.equals(add);
            UserRole userRole = userRoleMapper.selectById(finalRoleId);
            if (Objects.isNull(userRole)) {
                List<UserRole> userRoleList = userRoleMapper.selectList(Wrappers.lambdaQuery(UserRole.class).eq(UserRole::getRoleName, messageModel.getRoleName()));
                if (CollUtil.isNotEmpty(userRoleList)) {
                    userRole = userRoleList.get(0);
                }
            }

            /*
                新增角色时：角色存在不处理
                修改角色时：角色不存在不处理
             */
            if ((realAdd && Objects.nonNull(userRole)) || (!realAdd && Objects.isNull(userRole))) {
                return; // 不需要处理，直接返回
            }

            if (realAdd) {
                userRole = new UserRole();
                userRole.setRoleId(finalRoleId);
            }
            userRole.setRoleName(messageModel.getRoleName());
            userRole.setDescription(messageModel.getRoleCode());
            if (realAdd) {
                userRoleMapper.insert(userRole);
            } else {
                userRoleMapper.updateById(userRole);
            }
        };

        switch (requestType) {
            case "add":
                addOrUpdateExecutor.accept(true);
                break;
            case "update":
                addOrUpdateExecutor.accept(false);
                break;
            case "delete":
                // 系统管理员角色不允许删除
                if (finalRoleId == -1) {
                    return;
                }
                userRoleMapper.deleteUserRoleByRoleId(finalRoleId);
                break;
            default:
                break;
        }
    }

    public RoleSyncTopicHandler() {
        super(TopicConstSubscriptionConst.ROLE_CHANGE);
    }
}
