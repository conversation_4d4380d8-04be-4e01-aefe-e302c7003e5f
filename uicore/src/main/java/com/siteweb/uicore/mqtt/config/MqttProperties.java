package com.siteweb.uicore.mqtt.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: lzy
 * @Date: 2023/7/14 14:38
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "siteweb.uicore.message.mqtt")
public class MqttProperties {
    private Long connectionRetryTime = 10000L; // 连接重试间隔时间，单位毫秒
    private MqttClientConfig providerConfig;
    private MqttClientConfig consumerConfig;
    public static final String API_TOKEN_MQTT_TOPIC = "s6v2";
}
