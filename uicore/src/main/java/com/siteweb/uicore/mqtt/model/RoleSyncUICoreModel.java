package com.siteweb.uicore.mqtt.model;

import lombok.Data;

@Data
public class RoleSyncUICoreModel {
    private Integer roleId; // 角色ID
    private Integer parentId; // ParentID 父角色ID
    private String roleName; // 角色名，多语言支持
    private String roleCode; // 角色代码
    private Integer jwtTimeout; // Token过期时间 秒
    private String homePage; // HomePage 角色首页
    private Boolean extendMenu; // Extend 继承子角色的菜单权限
}
