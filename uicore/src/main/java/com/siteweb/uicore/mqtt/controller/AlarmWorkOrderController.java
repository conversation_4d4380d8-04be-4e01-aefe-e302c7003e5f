package com.siteweb.uicore.mqtt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ActiveEventDTO;
import com.siteweb.monitoring.service.ActiveEventService;
import com.siteweb.uicore.mqtt.topicPublisher.impl.CreateAlarmWorkOrderTopicPublisher;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@Slf4j
@RequestMapping("/api")
@Api(value = "告警工单接口", tags = {"告警工单接口"})
public class AlarmWorkOrderController {
    @Autowired(required = false)
    CreateAlarmWorkOrderTopicPublisher createAlarmWorkOrderTopicPublisher;

    @Autowired
    ActiveEventService activeEventService;

    @ApiOperation(value = "新增告警工单接口")
    @PostMapping(value = "/createalarmworkorder")
    public ResponseEntity<ResponseResult> createAlarmWorkOrder(@RequestBody String sequenceId) {
        if (ObjectUtil.isNull(createAlarmWorkOrderTopicPublisher)) {
            log.error("mqtt未启动");
            return ResponseHelper.failed("mqtt未启动");
        }
        ActiveEventDTO activeEventDTO = activeEventService.getActiveEventDTOBySequenceId(sequenceId);
        if (null == activeEventDTO) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        int result = createAlarmWorkOrderTopicPublisher.publish(activeEventDTO);
        if (result == 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        }
        return ResponseHelper.failed("Create Alarm Work Order Publish Fail");
    }
}
