package com.siteweb.uicore.mqtt.model;

import lombok.Data;

import java.util.List;

@Data
public class UserSyncUICoreModel {
    private Integer userId;
    private String password; // 密码(使用aes对称加密)
    private String username; // 用户名
    private String nickName; // 昵称
    private String phone; // 电话
    private String language; // 语言
    private String theme; // 主题
    private String avatar; // 头像
    private String gender; // 性别
    private String email; // 邮箱
    private String remark; // 备注
    private Integer status; // // 0正常,1禁用,2锁定
    private List<RoleSyncUICoreModel> roles;
    private Integer organizationId; // 用户所属组织Id
    private String jobNumber; // 工作编号
}
