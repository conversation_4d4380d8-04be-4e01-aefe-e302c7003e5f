package com.siteweb.uicore.mqtt.constant;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 * topic订阅常量
 */
@Getter
@AllArgsConstructor
public enum TopicConstSubscriptionConst {
    USER_CHANGE("gateway/data/core/users/+/+"), // 用户变更
    ROLE_CHANGE("gateway/data/core/roles/+/+"), // 角色变更
    CREATE_ALARM_WORK_ORDER("gateway/workorder/dcom/alarmorder/create/+"), // 告警工单信息变更
    TOKEN_RESPONSE("gateway/data/core/token/update"), // token响应topic
    ORGANIZATION_CHANGE("gateway/data/core/organizations/+/+"), // 角色变更
    ;
     private final String topic;

    public String getWildcardTopicPath() {
        return topic.replace("+", "(.+)");
    }

    /**
     * 精准匹配
     */
    public static TopicConstSubscriptionConst getByTopic(String topic) {
        for (TopicConstSubscriptionConst value : values()) {
            if (CharSequenceUtil.equals(value.getTopic(), topic)) {
                return value;
            }
        }
        return wildcardMatch(topic);
    }

    /**
     * 通配符匹配
     */
    public static TopicConstSubscriptionConst wildcardMatch(String topic) {
        for (TopicConstSubscriptionConst value : values()) {
            Pattern r = Pattern.compile(value.getWildcardTopicPath());
            Matcher m = r.matcher(topic);
            if (m.matches()) {
                return value;
            }
        }
        return null;
    }
}