package com.siteweb.uicore.mqtt.config;

import lombok.Data;

import java.net.InetAddress;

/**
 * mqtt客户端配置参数
 */
@Data
public class MqttClientConfig {
    private String username;
    private String password;
    private String hostUrl;
    private String clientId;
    private Long publishTimeOut; // 发布超时时间, 单位毫秒
    private String[] defaultTopic; // 默认订阅通道
    public String getClientId() {
        String hostName = "";
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            hostName = localHost.getHostName();
        }catch (Exception e) {}
        return this.clientId + "-" + hostName;
    }
}
