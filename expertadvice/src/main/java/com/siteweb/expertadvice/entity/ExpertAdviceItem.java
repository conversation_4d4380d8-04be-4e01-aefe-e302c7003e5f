package com.siteweb.expertadvice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 专家设备建议知识库
 * @Author: lzy
 * @Date: 2023/5/22 17:23
 */
@Data
@TableName("tbl_expert")
public class ExpertAdviceItem {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer expertId;
    /**
     * 局站类型id
     */
    private Integer stationCategoryId;
    /**
     * 局站类型名称
     */
    private String stationCategoryName;
    /**
     * 设备基类类型id
     */
    private Integer baseEquipmentTypeId;
    /**
     * 设备基类类型名称
     */
    private String baseEquipmentTypeName;
    /**
     * 基类告警id
     */
    private Integer baseAlarmId;
    /**
     * 基类告警名称
     */
    private String baseAlarmName;
    /**
     * 标准设备基类类型id
     */
    private Integer standardEquipmentTypeId;
    /**
     * 标准设备基类类型名称
     */
    private String standardEquipmentTypeName;
    /**
     * 标准基类告警id
     */
    private String standardAlarmId;
    /**
     * 标准基类告警名称
     */
    private String standardAlarmName;
    /**
     * 可能故障原因
     */
    private String reason;
    /**
     * 建议解决方案
     */
    private String solution;
}
