package com.siteweb.expertadvice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2023/5/22 18:06
 */
@Data
@TableName("tbl_fault")
public class Fault {
    @TableId(type = IdType.AUTO)
    private Integer faultId;

    /**
     * 事件序列号
     */
    private String sequenceId;
    /**
     * uuid
     */
    private String uuId;
    /**
     * 关联知识库
     */
    @TableField(exist = false)
    private Integer expertId;
    /**
     * 知识库关联故障id
     */
    @TableField(exist = false)
    private Integer faultExpertMapId;
    /**
     * 分组名称
     */
    private String stationGroupName;
    /**
     * 基站名称
     */
    private String stationName;
    /**
     * 机房名称
     */
    private String houseName;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 告警开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 告警结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 操作人id
     */
    private Integer createUserId;
    /**
     * 操作人
     */
    @TableField(exist = false)
    private String createUser;
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 实际原因
     */
    private String caseReason;
    /**
     * 实际方案
     */
    private String caseSolution;
    /**
     * 知识库原因
     */
    @TableField(exist = false)
    private String reason;
    /**
     * 知识库方案
     */
    @TableField(exist = false)
    private String solution;
}
