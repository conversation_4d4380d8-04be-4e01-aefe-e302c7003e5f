package com.siteweb.expertadvice.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Author: lzy
 * @Date: 2023/5/23 11:03
 */
@Data
public class FaultExpertListVO {
    private String faultId;
    /**
     * 分组名称
     */
    private String stationGroupName;
    /**
     * 基站名称
     */
    private String stationName;
    /**
     * 机房名称
     */
    private String houseName;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 标准基类告警名称
     */
    private String standardAlarmName;
    /**
     * 告警开始时间
     */
    private Date startTime;
    /**
     * 告警结束时间
     */
    private Date endTime;
    /**
     * 实际故障原因
     */
    private String caseReason;
    /**
     * 实际方案
     */
    private String caseSolution;
    /**
     * 操作人员
     */
    private String userName;
    /**
     * 操作时间
     */
    private Date createTime;
    /**
     * 知识库原因
     */
    private String reason;
    /**
     * 知识库方案
     */
    private String solution;
}
