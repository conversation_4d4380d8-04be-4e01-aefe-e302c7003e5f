package com.siteweb.expertadvice.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.expertadvice.entity.FaultExpertMap;
import com.siteweb.expertadvice.mapper.FaultExpertMapMapper;
import com.siteweb.expertadvice.service.FaultExpertMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * @Author: lzy
 * @Date: 2023/6/13 15:49
 */
@Service
public class FaultExpertMapServiceImpl implements FaultExpertMapService {

    @Autowired
    FaultExpertMapMapper faultExpertMapMapper;


    @Override
    public void saveFaultExpertMap(FaultExpertMap faultExpertMap) {
        faultExpertMapMapper.insert(faultExpertMap);
    }

    @Override
    public boolean existsById(Integer expertId, Integer faultId) {
        return faultExpertMapMapper.exists(Wrappers.lambdaQuery(FaultExpertMap.class)
                .eq(FaultExpertMap::getExpertId, expertId)
                .eq(FaultExpertMap::getFaultId, faultId));
    }

    @Override
    public int delFaultExpertMap(Integer expertId, List<String> ids) {
        return faultExpertMapMapper.delete(Wrappers.lambdaQuery(FaultExpertMap.class)
                .eq(FaultExpertMap::getExpertId, expertId)
                .in(FaultExpertMap::getFaultId, ids));
    }

    @Override
    public List<FaultExpertMap> findFaultExpertMapByExpertIds(List<String> expertIds) {
        return faultExpertMapMapper.selectList(Wrappers.<FaultExpertMap>lambdaQuery()
                .in(FaultExpertMap::getExpertId, expertIds));
    }

    @Override
    public int delFaultExpertMapByExpertIds(List<String> expertIds) {
        return faultExpertMapMapper.delete(Wrappers.lambdaQuery(FaultExpertMap.class)
                .in(FaultExpertMap::getExpertId, expertIds));
    }
}
