package com.siteweb.expertadvice.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.common.util.StringUtils;
import com.siteweb.expertadvice.dto.FaultAlarmEventListVO;
import com.siteweb.expertadvice.dto.FaultExpertListVO;
import com.siteweb.expertadvice.entity.Fault;
import com.siteweb.expertadvice.entity.FaultExpertMap;
import com.siteweb.expertadvice.mapper.FaultMapper;
import com.siteweb.expertadvice.service.FaultExpertMapService;
import com.siteweb.expertadvice.service.FaultService;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Author: lzy
 * @Date: 2023/5/22 18:06
 */
@Service
public class FaultServiceImpl implements FaultService {

    @Autowired
    FaultMapper faultMapper;
    @Autowired
    FaultExpertMapService faultExpertMapService;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Override
    public List<FaultAlarmEventListVO> findAlarmByTypeId(Integer baseTypeId) {

        List<FaultAlarmEventListVO> alarmByTypeId = faultMapper.getAlarmByTypeId(baseTypeId);
        List<Integer> resourceStructureIdslist = alarmByTypeId.stream().map(FaultAlarmEventListVO::getResourceStructureId).distinct().toList();
        Map<Integer, String> resourceIdLevelOfPathMap = resourceStructureManager.getResourceIdLevelOfPathMap(resourceStructureIdslist);
        Map<Integer, String> resourceFullPathMap = resourceStructureManager.getResourceFullPath(resourceIdLevelOfPathMap);
        alarmByTypeId.forEach(faultAlarmEventListVO -> {
            String path = resourceFullPathMap.getOrDefault(faultAlarmEventListVO.getResourceStructureId(), "");
            String[] split = path.split("_");
            if (split.length >= 3 ){
                faultAlarmEventListVO.setStructureName(split[split.length - 3]);
                faultAlarmEventListVO.setStationName(split[split.length - 2]);
                faultAlarmEventListVO.setHouseName(split[split.length - 1]);
            }
        });
        return alarmByTypeId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createFault(Fault fault) {
        List<Fault> faults = faultMapper.selectList(Wrappers.<Fault>lambdaQuery().orderByDesc(Fault::getFaultId));
        Integer maxId = CollUtil.isEmpty(faults) ? 1 : faults.get(0).getFaultId() + 1;
        fault.setFaultId(maxId);
        fault.setUuId(UUID.randomUUID().toString());
        faultMapper.insert(fault);
        if (faultExpertMapService.existsById(fault.getExpertId(),fault.getFaultId())) {
            return;
        }
        FaultExpertMap faultExpertMap = new FaultExpertMap();
        faultExpertMap.setFaultId(fault.getFaultId());
        faultExpertMap.setExpertId(fault.getExpertId());
        faultExpertMapService.saveFaultExpertMap(faultExpertMap);
    }

    @Override
    public List<FaultExpertListVO> findFaultByExpertId(Integer expertId) {
        return faultMapper.findFaultByExpertId(expertId);
    }

    @Override
    public void removeFault(Integer expertId, List<String> ids) {
        faultMapper.deleteBatchIds(ids);
        faultExpertMapService.delFaultExpertMap(expertId, ids);
    }

    @Override
    public void updateFault(Fault fault) {
        LambdaUpdateWrapper<Fault> updateWrapper = Wrappers.lambdaUpdate(Fault.class);
        updateWrapper.set(Fault::getCaseReason, fault.getCaseReason());
        updateWrapper.set(Fault::getCaseSolution, fault.getCaseSolution());
        updateWrapper.eq(Fault::getFaultId, fault.getFaultId());
        faultMapper.update(null, updateWrapper);
    }

    @Override
    public void removeFaultByExpertIds(List<String> expertIds) {
        List<FaultExpertMap> faultExpertMapList = faultExpertMapService.findFaultExpertMapByExpertIds(expertIds);
        if (CollUtil.isEmpty(faultExpertMapList)) return;
        faultExpertMapService.delFaultExpertMapByExpertIds(expertIds);
        List<Integer> faultIdList = faultExpertMapList.stream().map(i -> i.getFaultId()).toList();
        if (CollUtil.isEmpty(faultIdList)) return;
        faultMapper.deleteBatchIds(faultIdList);
    }
}
