package com.siteweb.expertadvice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.expertadvice.dto.FaultAlarmEventListVO;
import com.siteweb.expertadvice.dto.FaultExpertListVO;
import com.siteweb.expertadvice.entity.Fault;

import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/5/22 18:06
 */
public interface FaultMapper extends BaseMapper<Fault> {
    List<FaultAlarmEventListVO> getAlarmByTypeId(Integer baseTypeId);

    List<FaultExpertListVO> findFaultByExpertId(Integer expertId);
}
