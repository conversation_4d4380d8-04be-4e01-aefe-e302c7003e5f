package com.siteweb.battery.config;

import cn.hutool.core.util.StrUtil;
import com.siteweb.battery.manager.BatteryDischargeManager;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableScheduling
public class DynamicBatteryTask implements SchedulingConfigurer {


    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    BatteryDischargeManager batteryDischargeManager;


    private int DynamicCronTask() {
        int cron = 5 * 6;
        //从数据库获得配置的corn表达式
        SystemConfig systemconfig = systemConfigService.findBySystemConfigKey("battery.dischargeevent.fixedDelay");
        if (systemconfig != null && StrUtil.isNotBlank(systemconfig.getSystemConfigValue())){
            cron = Integer.parseInt(systemconfig.getSystemConfigValue()) * 5;
        }
        return cron;
    }




    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.addTriggerTask(() -> {
            // 任务逻辑
            batteryDischargeManager.execSyncDischargeData();
        }, triggerContext -> {
            int seconds = DynamicCronTask();
            // 任务触发，可修改任务的执行周期
            PeriodicTrigger periodicTrigger = new PeriodicTrigger(seconds * 1000L);
            return periodicTrigger.nextExecutionTime(triggerContext);
        });

    }
}
