package com.siteweb.battery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.monitoring.entity.Equipment;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: Habits
 * @time: 2022/5/25 9:20
 * @description:
 **/
@Data
@NoArgsConstructor
@TableName("batterystring")
public class BatteryString {

    @TableId(value = "BatteryStringId", type = IdType.AUTO)
    private Integer batteryStringId;

    /**
     * 电池配置名称
     */
    private String batteryStringName;

    /**
     * 电池节数量
     */
    private Integer cellCount;

    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 电池模型ID
     */
    private Integer batteryCellModelId;

    /**
     * 备电时间
     */
    private Integer standbyPower;

    /**
     * 启用时间
     */
    private Date startUsingTime;

    /**
     * 电流互感器类型
     */
    private String currentTransformerType;

    /**
     * 厂家
     */
    private String vendor;

    /**
     * 型号
     */
    private String model;

    /**
     * 额定电压
     */
    private Double ratedVoltage;

    /**
     * 额定容量
     */
    private Double ratedCapacity;

    /**
     * 初始内阻
     */
    private Double initialIR;

    /**
     * 浮充电压
     */
    private Double floatChargeVoltage;

    /**
     * 均充电压
     */
    private Double evenChargeVoltage;

    /**
     * 温度补偿系数
     */
    private Double tempCompensationFactor;

    /**
     * 终止电压,节数*单体浮充电压
     */
    private Double terminationVoltage;

    /**
     * 最大均充充电电流
     */
    private Double maxChargingCurrent;

    /**
     * 最大均充充电电压
     */
    private Double maxFloatChargeVoltage;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 关联的设备
     */
    @TableField(exist = false)
    private Equipment equipment;

    /**
     * 电池模型
     */
    @TableField(exist = false)
    private BatteryCellModel batteryCellModel;


}
