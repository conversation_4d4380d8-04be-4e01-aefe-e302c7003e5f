package com.siteweb.battery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zhou
 * @description LiBatteryPack
 * @createTime 2023-07-10 09:08:58
 */
@Data
@NoArgsConstructor
@TableName("libatterypack")
public class LiBatteryPack {

    //锂电池单体ID
    @TableId(value = "BatteryPackId", type = IdType.AUTO)
    private Integer batteryPackId;

    //锂电池单体编号
    private Integer batteryPackNumber;

    //所属锂电池编号
    private Integer batteryNumber;

    //所属锂电池组编号
    private Integer batteryGroupNumber;

    //所属锂电池模块编号
    private Integer batteryModuleNumber;

    //锂电池单体名称
    private String batteryPackName;

    //锂电池组设备ID
    private Integer equipmentId;

    //UPS设备ID
    @TableField("UPSEquipmentId")
    private Integer upsEquipmentId;

    //电池组平均电压指标Id
    private Integer meanVoltageComplexIndexId;

    //电池组平均温度指标Id
    private Integer meanTemperatureComplexIndexId;

    //单体电压信号Id
    private Integer voltageSignalId;

    //单体温度信号Id
    private Integer temperatureSignalId;

    //单体电池温升速率指标Id
    private Integer temperatureIncrementRateComplexIndexId;

    //单体电池与同组电池平均温度差值指标Id
    private Integer temperatureDeviationComplexIndexId;

    //单体电池与同组电池平均电压差值指标Id
    private Integer voltageDeviationComplexIndexId;

    //备注
    private String description;
}
