package com.siteweb.battery.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name = "BatteryData")
public class BatteryData {

    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String time;

    @Column(name = "EquipmentId", tag = true)
    public String equipmentId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = "SampleTime")
    public String sampleTime;

    @Column(name = "TotalVoltage")
    public String totalVoltage;

    @Column(name = "TotalCurrent")
    public String totalCurrent;

    @Column(name = "DischargePower")
    public String dischargePower;

    @Column(name = "TotalSoc")
    public String totalSoc;

    @Column(name = "Voltage")
    public String voltage;

    @Column(name = "Temperature")
    public String temperature;

    @Column(name = "InternalResistance")
    public String internalResistance;

    @Column(name = "Soc")
    public String soc;

}
