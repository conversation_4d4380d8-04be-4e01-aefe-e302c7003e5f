package com.siteweb.battery.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description ThermalRunawayEventDTO
 * @createTime 2023-07-20 13:24:15
 */
@Data
@NoArgsConstructor
public class ThermalRunawayEventDTO {

    //热失控事件Id
    private Integer thermalRunawayEventId;

    //开始时间
    private Date startTime;

    //电池单体Id
    private Integer batteryPackId;

    //电池单体名称
    private String batteryPackName;

    //锂电池组设备ID
    private Integer equipmentId;

    //锂电池组设备名称
    private String equipmentName;

    //触发热失控时的单体温度
    private Double triggerTemperature;

    //预测结果
    private String prediction;

    //所处阶段ID
    private Integer phaseId;

    //所处阶段
    private String phase;

}
