package com.siteweb.battery.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description ThermalRunawayControlRecordDTO
 * @createTime 2023-07-26 17:01:54
 */
@Data
@NoArgsConstructor
public class ThermalRunawayControlRecordDTO {

    //热失控阶段
    private String phase;

    //热失控事件Id
    private Integer thermalRunawayEventId;

    //控制设备Id
    private Integer equipmentId;

    //控制设备名称
    private String equipmentName;

    //控制命令Id
    private Integer controlId;

    //控制命令名称
    private String controlName;

    //控制值
    private String controlValue;

    //控制时间
    private Date controlTime;

    //控制结果
    private String controlResult;
}
