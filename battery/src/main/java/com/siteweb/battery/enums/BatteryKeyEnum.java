package com.siteweb.battery.enums;

/**
 * 电池key的枚举
 *
 * <AUTHOR>
 */
public enum BatteryKeyEnum {
    /**
     * 总电流
     */
    BATTERYSTRING_TOTALCURRENT_KEY("battery.string.totalcurrent"),

    /**
     * 总电压
     */
    BATTERYSTRING_TOTALVOLTAGE_KEY("battery.string.totalvoltage"),

    /**
     * 环境温度
     */
    BATTERYSTRING_AT_KEY("battery.string.ambienttemperature"),

    /**
     * 环境湿度
     */
    BATTERYSTRING_EH_KEY("battery.string.environmenthumidity"),

    /**
     * 温度
     */
    BATTERYCELL_TEMPERATURE_KEY("battery.cell.temperature"),

    /**
     * 温度过低
     */
    BATTERYCELL_TEMPERATURELOW_KEY("battery.cell.temperaturelow"),

    /**
     * 温度过高
     */
    BATTERYCELL_TEMPERATUREHIGH_KEY("battery.cell.temperaturehigh"),

    /**
     * 内阻
     */
    BATTERYCELL_IR_KEY("battery.cell.internalresistance"),

    /**
     * 2V电压过低
     */
    BATTERYCELL_VOLTAGELOW2V_KEY("battery.cell.voltagelow.2V"),

    /**
     * 2V电压过高
     */
    BATTERYCELL_VOLTAGEHIGH2V_KEY("battery.cell.voltagehigh.2V"),

    /**
     * 6V电压过低
     */
    BATTERYCELL_VOLTAGELOW6V_KEY("battery.cell.voltagelow.6V"),

    /**
     * 6V电压过高
     */
    BATTERYCELL_VOLTAGEHIGH6V_KEY("battery.cell.voltagehigh.6V"),

    /**
     * 12V电压过低
     */
    BATTERYCELL_VOLTAGELOW12V_KEY("battery.cell.voltagelow.12V"),

    /**
     * 12V电压过高
     */
    BATTERYCELL_VOLTAGEHIGH12V_KEY("battery.cell.voltagehigh.12V"),

    /**
     * 2V电压
     */
    BATTERYCELL_VOLTAGE2V_KEY("battery.cell.voltage.2V"),

    /**
     * 2V电压
     */
    BATTERYCELL_VOLTAGE6V_KEY("battery.cell.voltage.6V"),

    /**
     * 2V电压
     */
    BATTERYCELL_VOLTAGE12V_KEY("battery.cell.voltage.12V"),

    /**
     * 蓄电池组放电总功率
     */
    BATTERYSTRING_DISCHARGEPOWER_KEY("battery.string.dischargepower"),

    /**
     * 蓄电池组总SOC
     */
    BATTERYSTRING_TOTALSOC_KEY("battery.string.dischargepower"),

    /**
     * 电池放电告警
     */
    BATTERY_DISCHARGEEVENT_BASETYPEID("battery.dischargeevent.basetypeid"),
    /**
     * 电池放电周期存储周期
     */
    BATTERY_DISCHARGEEVENT_FIXED_DELAY("battery.dischargeevent.fixedDelay"),
    /**
     * 电池放电信号基类
     */
    BATTERY_DISCHARGEEVENT_SIGNAL_BASETYPEID("battery.dischargeevent.signalbasetypeid"),
    /**
     * 电池设备基类配置
     */
    BATTERY_EQUIPMENTBASETYPE_CONFIG("battery.equipmentbasetype.config"),
    /**
     * 电池放电延长参数（秒）
     */
    BATTERY_DISCHARGE_EXTENSION_DURATION("battery.discharge.extension.duration"),
    /**
     * 电池放电前补点时长（分钟）
     */
    BATTERY_DISCHARGE_PREPOINT_ENABLED("battery.discharge.prepoint.enabled");



    private String name;

    BatteryKeyEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
